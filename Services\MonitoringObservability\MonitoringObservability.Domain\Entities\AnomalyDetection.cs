using MonitoringObservability.Domain.Enums;
using MonitoringObservability.Domain.Events;
using Shared.Domain.Common;

namespace MonitoringObservability.Domain.Entities;

/// <summary>
/// Represents an anomaly detection model for a specific metric
/// </summary>
public class AnomalyDetectionModel : AggregateRoot
{
    private readonly List<AnomalyDetectionResult> _detectionResults = new();
    private readonly List<ModelTrainingSession> _trainingSessions = new();

    public AnomalyDetectionModel(string name, string serviceName, string metricName, 
        AnomalyDetectionAlgorithm algorithm, Dictionary<string, object>? parameters = null)
    {
        Id = Guid.NewGuid();
        Name = name ?? throw new ArgumentNullException(nameof(name));
        ServiceName = serviceName ?? throw new ArgumentNullException(nameof(serviceName));
        MetricName = metricName ?? throw new ArgumentNullException(nameof(metricName));
        Algorithm = algorithm;
        Parameters = parameters ?? new Dictionary<string, object>();
        CreatedAt = DateTime.UtcNow;
        LastModified = DateTime.UtcNow;
        IsEnabled = true;
        SensitivityLevel = AnomalySensitivity.Medium;
        TrainingWindow = TimeSpan.FromDays(7);
        DetectionWindow = TimeSpan.FromMinutes(5);
        MinDataPoints = 100;
        Tags = new Dictionary<string, string>();
        Metadata = new Dictionary<string, object>();
    }

    // Required for EF Core
    private AnomalyDetectionModel() { }

    public string Name { get; private set; } = string.Empty;
    public string ServiceName { get; private set; } = string.Empty;
    public string MetricName { get; private set; } = string.Empty;
    public AnomalyDetectionAlgorithm Algorithm { get; private set; }
    public AnomalySensitivity SensitivityLevel { get; private set; }
    public bool IsEnabled { get; private set; }
    public bool IsTrained { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime LastModified { get; private set; }
    public DateTime? LastTrained { get; private set; }
    public DateTime? LastDetection { get; private set; }
    public TimeSpan TrainingWindow { get; private set; }
    public TimeSpan DetectionWindow { get; private set; }
    public int MinDataPoints { get; private set; }
    public double? Accuracy { get; private set; }
    public double? Precision { get; private set; }
    public double? Recall { get; private set; }
    public Dictionary<string, object> Parameters { get; private set; } = new();
    public Dictionary<string, string> Tags { get; private set; } = new();
    public Dictionary<string, object> Metadata { get; private set; } = new();

    // Navigation properties
    public IReadOnlyList<AnomalyDetectionResult> DetectionResults => _detectionResults.AsReadOnly();
    public IReadOnlyList<ModelTrainingSession> TrainingSessions => _trainingSessions.AsReadOnly();

    // Computed properties
    public int TotalDetections => _detectionResults.Count;
    public int AnomaliesDetected => _detectionResults.Count(r => r.IsAnomaly);
    public double AnomalyRate => TotalDetections > 0 ? (double)AnomaliesDetected / TotalDetections : 0.0;
    public bool NeedsRetraining => !IsTrained || 
                                  (LastTrained.HasValue && DateTime.UtcNow - LastTrained.Value > TrainingWindow);

    public void UpdateConfiguration(AnomalySensitivity? sensitivityLevel = null, 
        TimeSpan? trainingWindow = null, TimeSpan? detectionWindow = null, 
        int? minDataPoints = null, Dictionary<string, object>? parameters = null)
    {
        if (sensitivityLevel.HasValue)
            SensitivityLevel = sensitivityLevel.Value;
        
        if (trainingWindow.HasValue)
            TrainingWindow = trainingWindow.Value;
        
        if (detectionWindow.HasValue)
            DetectionWindow = detectionWindow.Value;
        
        if (minDataPoints.HasValue)
            MinDataPoints = minDataPoints.Value;
        
        if (parameters != null)
            Parameters = parameters;

        LastModified = DateTime.UtcNow;
        
        AddDomainEvent(new AnomalyDetectionModelUpdatedEvent(Id, Name));
    }

    public void StartTraining(Guid sessionId, int dataPointCount)
    {
        var session = new ModelTrainingSession(sessionId, Id, Algorithm, dataPointCount);
        _trainingSessions.Add(session);
        
        LastModified = DateTime.UtcNow;
        
        AddDomainEvent(new ModelTrainingStartedEvent(Id, sessionId, Algorithm.ToString()));
    }

    public void CompleteTraining(Guid sessionId, bool success, double? accuracy = null, 
        double? precision = null, double? recall = null, string? errorMessage = null)
    {
        var session = _trainingSessions.FirstOrDefault(s => s.Id == sessionId);
        if (session == null)
            throw new InvalidOperationException($"Training session {sessionId} not found");

        session.Complete(success, accuracy, precision, recall, errorMessage);
        
        if (success)
        {
            IsTrained = true;
            LastTrained = DateTime.UtcNow;
            Accuracy = accuracy;
            Precision = precision;
            Recall = recall;
        }

        LastModified = DateTime.UtcNow;
        
        AddDomainEvent(new ModelTrainingCompletedEvent(Id, sessionId, success, accuracy ?? 0));
    }

    public void RecordDetectionResult(AnomalyDetectionResult result)
    {
        if (result == null) throw new ArgumentNullException(nameof(result));
        
        _detectionResults.Add(result);
        LastDetection = DateTime.UtcNow;
        LastModified = DateTime.UtcNow;
        
        if (result.IsAnomaly)
        {
            AddDomainEvent(new AnomalyDetectedEvent(Id, result.Id, result.AnomalyScore, result.MetricValue));
        }
    }

    public void Enable()
    {
        if (!IsEnabled)
        {
            IsEnabled = true;
            LastModified = DateTime.UtcNow;
            
            AddDomainEvent(new AnomalyDetectionModelEnabledEvent(Id, Name));
        }
    }

    public void Disable()
    {
        if (IsEnabled)
        {
            IsEnabled = false;
            LastModified = DateTime.UtcNow;
            
            AddDomainEvent(new AnomalyDetectionModelDisabledEvent(Id, Name));
        }
    }

    public void AddTag(string key, string value)
    {
        if (string.IsNullOrWhiteSpace(key)) throw new ArgumentException("Tag key cannot be null or empty", nameof(key));
        Tags[key] = value ?? string.Empty;
        LastModified = DateTime.UtcNow;
    }

    public void AddMetadata(string key, object value)
    {
        if (string.IsNullOrWhiteSpace(key)) throw new ArgumentException("Metadata key cannot be null or empty", nameof(key));
        Metadata[key] = value;
        LastModified = DateTime.UtcNow;
    }

    public bool ShouldDetect()
    {
        return IsEnabled && IsTrained;
    }

    public double GetSensitivityThreshold()
    {
        return SensitivityLevel switch
        {
            AnomalySensitivity.Low => 3.0,      // 3 standard deviations
            AnomalySensitivity.Medium => 2.5,   // 2.5 standard deviations
            AnomalySensitivity.High => 2.0,     // 2 standard deviations
            AnomalySensitivity.VeryHigh => 1.5, // 1.5 standard deviations
            _ => 2.5
        };
    }
}

/// <summary>
/// Represents the result of an anomaly detection
/// </summary>
public class AnomalyDetectionResult
{
    public AnomalyDetectionResult(Guid modelId, double metricValue, DateTime timestamp, 
        bool isAnomaly, double anomalyScore, double? expectedValue = null, 
        double? confidence = null, string? explanation = null)
    {
        Id = Guid.NewGuid();
        ModelId = modelId;
        MetricValue = metricValue;
        Timestamp = timestamp;
        IsAnomaly = isAnomaly;
        AnomalyScore = anomalyScore;
        ExpectedValue = expectedValue;
        Confidence = confidence;
        Explanation = explanation;
        CreatedAt = DateTime.UtcNow;
        Metadata = new Dictionary<string, object>();
    }

    // Required for EF Core
    private AnomalyDetectionResult() { }

    public Guid Id { get; private set; }
    public Guid ModelId { get; private set; }
    public double MetricValue { get; private set; }
    public DateTime Timestamp { get; private set; }
    public bool IsAnomaly { get; private set; }
    public double AnomalyScore { get; private set; }
    public double? ExpectedValue { get; private set; }
    public double? Confidence { get; private set; }
    public string? Explanation { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public bool IsConfirmed { get; private set; }
    public bool IsFalsePositive { get; private set; }
    public string? UserFeedback { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; } = new();

    // Navigation properties
    public AnomalyDetectionModel Model { get; private set; } = null!;

    // Computed properties
    public double? Deviation => ExpectedValue.HasValue ? 
        Math.Abs(MetricValue - ExpectedValue.Value) : null;
    public double? DeviationPercentage => ExpectedValue.HasValue && ExpectedValue.Value != 0 ? 
        Math.Abs(MetricValue - ExpectedValue.Value) / ExpectedValue.Value * 100 : null;

    public void ConfirmAnomaly(string? userFeedback = null)
    {
        IsConfirmed = true;
        IsFalsePositive = false;
        UserFeedback = userFeedback;
    }

    public void MarkAsFalsePositive(string? userFeedback = null)
    {
        IsConfirmed = false;
        IsFalsePositive = true;
        UserFeedback = userFeedback;
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }
}

/// <summary>
/// Represents a model training session
/// </summary>
public class ModelTrainingSession
{
    public ModelTrainingSession(Guid id, Guid modelId, AnomalyDetectionAlgorithm algorithm, int dataPointCount)
    {
        Id = id;
        ModelId = modelId;
        Algorithm = algorithm;
        DataPointCount = dataPointCount;
        StartedAt = DateTime.UtcNow;
        Status = TrainingStatus.InProgress;
        Metadata = new Dictionary<string, object>();
    }

    // Required for EF Core
    private ModelTrainingSession() { }

    public Guid Id { get; private set; }
    public Guid ModelId { get; private set; }
    public AnomalyDetectionAlgorithm Algorithm { get; private set; }
    public int DataPointCount { get; private set; }
    public DateTime StartedAt { get; private set; }
    public DateTime? CompletedAt { get; private set; }
    public TrainingStatus Status { get; private set; }
    public double? Accuracy { get; private set; }
    public double? Precision { get; private set; }
    public double? Recall { get; private set; }
    public string? ErrorMessage { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; } = new();

    // Navigation properties
    public AnomalyDetectionModel Model { get; private set; } = null!;

    // Computed properties
    public TimeSpan? Duration => CompletedAt?.Subtract(StartedAt);

    public void Complete(bool success, double? accuracy = null, double? precision = null, 
        double? recall = null, string? errorMessage = null)
    {
        CompletedAt = DateTime.UtcNow;
        Status = success ? TrainingStatus.Completed : TrainingStatus.Failed;
        Accuracy = accuracy;
        Precision = precision;
        Recall = recall;
        ErrorMessage = errorMessage;
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }
}
