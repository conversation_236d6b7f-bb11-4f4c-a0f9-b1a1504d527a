using NetworkFleetManagement.Application.Queries.GetPartnerRecommendations;
using NetworkFleetManagement.Domain.Entities;

namespace NetworkFleetManagement.Application.Interfaces;

public interface IRecommendationEngine
{
    Task<List<Carrier>> GenerateRecommendationsAsync(
        Guid userId,
        List<Carrier> potentialPartners,
        RecommendationCriteria criteria,
        List<string> serviceAreas,
        List<string> loadTypes,
        decimal? budgetRange,
        int maxRecommendations,
        CancellationToken cancellationToken = default);

    Task<decimal> CalculatePartnerScoreAsync(
        Guid userId,
        Carrier partner,
        RecommendationCriteria criteria,
        CancellationToken cancellationToken = default);

    Task<List<Carrier>> GetSimilarPartnersAsync(
        Guid partnerId,
        int maxResults = 5,
        CancellationToken cancellationToken = default);

    Task<Dictionary<string, decimal>> GetRecommendationFactorsAsync(
        Guid userId,
        Guid partnerId,
        CancellationToken cancellationToken = default);
}

public interface IAnalyticsService
{
    Task<Dictionary<string, object>> CalculatePartnerMetricsAsync(
        Guid partnerId,
        DateTime fromDate,
        DateTime toDate,
        CancellationToken cancellationToken = default);

    Task<List<TrendDataPoint>> GetPerformanceTrendAsync(
        Guid partnerId,
        string metricName,
        DateTime fromDate,
        DateTime toDate,
        CancellationToken cancellationToken = default);

    Task<Dictionary<string, decimal>> GetBenchmarkDataAsync(
        string partnerType,
        List<string> serviceAreas,
        CancellationToken cancellationToken = default);

    Task<List<PredictiveInsight>> GeneratePredictiveInsightsAsync(
        List<Guid> partnerIds,
        int forecastDays = 30,
        CancellationToken cancellationToken = default);
}

public interface INetworkPerformanceRepository
{
    Task<PartnerPerformanceData?> GetPartnerPerformanceAsync(Guid partnerId, CancellationToken cancellationToken = default);
    Task<List<PartnerPerformanceData>> GetPartnerPerformanceBatchAsync(List<Guid> partnerIds, CancellationToken cancellationToken = default);
    Task<Dictionary<string, decimal>> GetIndustryBenchmarksAsync(string partnerType, CancellationToken cancellationToken = default);
    Task UpdatePartnerPerformanceAsync(Guid partnerId, PartnerPerformanceData performanceData, CancellationToken cancellationToken = default);
}

// Supporting classes
public class TrendDataPoint
{
    public DateTime Date { get; set; }
    public decimal Value { get; set; }
    public string? Label { get; set; }
}

public class PredictiveInsight
{
    public string InsightType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal Confidence { get; set; }
    public DateTime PredictionDate { get; set; }
    public Dictionary<string, object> Data { get; set; } = new();
}

public class PartnerPerformanceData
{
    public Guid PartnerId { get; set; }
    public int TotalTripsCompleted { get; set; }
    public int TotalTripsAssigned { get; set; }
    public decimal AverageResponseTimeHours { get; set; }
    public decimal ReliabilityScore { get; set; }
    public decimal QualityScore { get; set; }
    public decimal CostEfficiencyRating { get; set; }
    public string PerformanceTrend { get; set; } = string.Empty;
    public DateTime LastUpdated { get; set; }
    public Dictionary<string, decimal> AdditionalMetrics { get; set; } = new();
}
