using MediatR;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Entities;

namespace NetworkFleetManagement.Application.Queries.PreferredPartners;

public class GetPreferredPartnersQuery : IRequest<PagedResult<PreferredPartnerSummaryDto>>
{
    public Guid UserId { get; set; }
    public List<string>? PartnerTypes { get; set; }
    public List<string>? PreferenceLevels { get; set; }
    public List<string>? Statuses { get; set; }
    public bool? AutoAssignEnabled { get; set; }
    public bool? IsActive { get; set; }
    public decimal? MinRating { get; set; }
    public decimal? MaxRating { get; set; }
    public DateTime? CreatedAfter { get; set; }
    public DateTime? CreatedBefore { get; set; }
    public DateTime? LastCollaborationAfter { get; set; }
    public DateTime? LastCollaborationBefore { get; set; }
    public string? SearchTerm { get; set; }
    public string SortBy { get; set; } = "Priority";
    public string SortDirection { get; set; } = "asc";
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 20;
}
