using FluentAssertions;
using Microsoft.AspNetCore.Mvc.Testing;
using MobileWorkflow.Application.DTOs;
using MobileWorkflow.IntegrationTests.Infrastructure;
using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using Xunit;

namespace MobileWorkflow.IntegrationTests.API;

public class MobileAppsControllerTests : IClassFixture<TestWebApplicationFactory<Program>>
{
    private readonly TestWebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;
    private readonly JsonSerializerOptions _jsonOptions;

    public MobileAppsControllerTests(TestWebApplicationFactory<Program> factory)
    {
        _factory = factory;
        _client = _factory.CreateClient();
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
    }

    [Fact]
    public async Task GetMobileApps_ShouldReturnAllApps()
    {
        // Act
        var response = await _client.GetAsync("/api/mobileapps");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var apps = JsonSerializer.Deserialize<List<MobileAppDto>>(content, _jsonOptions);
        
        apps.Should().NotBeNull();
        apps.Should().HaveCountGreaterThan(0);
        apps.Should().Contain(app => app.Platform == "Android");
        apps.Should().Contain(app => app.Platform == "iOS");
    }

    [Fact]
    public async Task GetMobileApps_WithPlatformFilter_ShouldReturnFilteredApps()
    {
        // Act
        var response = await _client.GetAsync("/api/mobileapps?platform=Android");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var apps = JsonSerializer.Deserialize<List<MobileAppDto>>(content, _jsonOptions);
        
        apps.Should().NotBeNull();
        apps.Should().OnlyContain(app => app.Platform == "Android");
    }

    [Fact]
    public async Task GetMobileApps_WithActiveFilter_ShouldReturnActiveApps()
    {
        // Act
        var response = await _client.GetAsync("/api/mobileapps?isActive=true");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var apps = JsonSerializer.Deserialize<List<MobileAppDto>>(content, _jsonOptions);
        
        apps.Should().NotBeNull();
        apps.Should().OnlyContain(app => app.IsActive);
    }

    [Fact]
    public async Task GetMobileAppById_WithValidId_ShouldReturnApp()
    {
        // Arrange
        var existingApp = await GetFirstMobileAppAsync();

        // Act
        var response = await _client.GetAsync($"/api/mobileapps/{existingApp.Id}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var app = JsonSerializer.Deserialize<MobileAppDto>(content, _jsonOptions);
        
        app.Should().NotBeNull();
        app!.Id.Should().Be(existingApp.Id);
        app.Name.Should().Be(existingApp.Name);
    }

    [Fact]
    public async Task GetMobileAppById_WithInvalidId_ShouldReturnNotFound()
    {
        // Arrange
        var invalidId = Guid.NewGuid();

        // Act
        var response = await _client.GetAsync($"/api/mobileapps/{invalidId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task GetMobileAppByPackageId_WithValidPackageId_ShouldReturnApp()
    {
        // Arrange
        var existingApp = await GetFirstMobileAppAsync();

        // Act
        var response = await _client.GetAsync($"/api/mobileapps/package/{existingApp.PackageId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var app = JsonSerializer.Deserialize<MobileAppDto>(content, _jsonOptions);
        
        app.Should().NotBeNull();
        app!.PackageId.Should().Be(existingApp.PackageId);
    }

    [Fact]
    public async Task GetMobileAppByPackageId_WithInvalidPackageId_ShouldReturnNotFound()
    {
        // Arrange
        var invalidPackageId = "com.invalid.package";

        // Act
        var response = await _client.GetAsync($"/api/mobileapps/package/{invalidPackageId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task GetManifest_ShouldReturnPWAManifest()
    {
        // Arrange
        var existingApp = await GetFirstMobileAppAsync();

        // Act
        var response = await _client.GetAsync($"/api/mobileapps/{existingApp.Id}/manifest");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        response.Content.Headers.ContentType?.MediaType.Should().Be("application/json");
        
        var content = await response.Content.ReadAsStringAsync();
        var manifest = JsonSerializer.Deserialize<JsonElement>(content, _jsonOptions);
        
        manifest.TryGetProperty("name", out var name).Should().BeTrue();
        manifest.TryGetProperty("short_name", out var shortName).Should().BeTrue();
        manifest.TryGetProperty("icons", out var icons).Should().BeTrue();
        
        name.GetString().Should().NotBeNullOrEmpty();
        shortName.GetString().Should().NotBeNullOrEmpty();
        icons.GetArrayLength().Should().BeGreaterThan(0);
    }

    [Fact]
    public async Task GetServiceWorkerConfig_ShouldReturnConfiguration()
    {
        // Arrange
        var existingApp = await GetFirstMobileAppAsync();

        // Act
        var response = await _client.GetAsync($"/api/mobileapps/{existingApp.Id}/service-worker-config");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var config = JsonSerializer.Deserialize<JsonElement>(content, _jsonOptions);
        
        config.TryGetProperty("cacheName", out var cacheName).Should().BeTrue();
        config.TryGetProperty("cacheStrategy", out var cacheStrategy).Should().BeTrue();
        config.TryGetProperty("syncTags", out var syncTags).Should().BeTrue();
        
        cacheName.GetString().Should().NotBeNullOrEmpty();
        cacheStrategy.GetString().Should().NotBeNullOrEmpty();
        syncTags.GetArrayLength().Should().BeGreaterThan(0);
    }

    [Fact]
    public async Task CreateMobileApp_WithValidData_ShouldCreateApp()
    {
        // Arrange
        var createCommand = new
        {
            Name = "Test Mobile App",
            Version = "1.0.0",
            Platform = "Web",
            PackageId = "com.test.webapp.new",
            ReleaseDate = DateTime.UtcNow,
            MinimumOSVersion = "Chrome 90+",
            Features = new Dictionary<string, object> { { "pwa_support", true } },
            Configuration = new Dictionary<string, object> { { "cache_strategy", "CacheFirst" } },
            SupportsOffline = true,
            DownloadUrl = "https://test.com/webapp",
            FileSize = 5000000L,
            Checksum = "test-checksum-new"
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/mobileapps", createCommand);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Created);
        
        var content = await response.Content.ReadAsStringAsync();
        var createdApp = JsonSerializer.Deserialize<MobileAppDto>(content, _jsonOptions);
        
        createdApp.Should().NotBeNull();
        createdApp!.Name.Should().Be(createCommand.Name);
        createdApp.Platform.Should().Be(createCommand.Platform);
        createdApp.PackageId.Should().Be(createCommand.PackageId);
        createdApp.IsActive.Should().BeTrue();
        
        // Verify the app was actually created in the database
        var getResponse = await _client.GetAsync($"/api/mobileapps/{createdApp.Id}");
        getResponse.StatusCode.Should().Be(HttpStatusCode.OK);
    }

    [Fact]
    public async Task CreateMobileApp_WithDuplicatePackageId_ShouldReturnBadRequest()
    {
        // Arrange
        var existingApp = await GetFirstMobileAppAsync();
        var createCommand = new
        {
            Name = "Duplicate App",
            Version = "1.0.0",
            Platform = "Android",
            PackageId = existingApp.PackageId, // Use existing package ID
            ReleaseDate = DateTime.UtcNow,
            MinimumOSVersion = "8.0",
            Features = new Dictionary<string, object>(),
            Configuration = new Dictionary<string, object>(),
            SupportsOffline = false,
            DownloadUrl = "https://test.com/duplicate",
            FileSize = 1000000L,
            Checksum = "duplicate-checksum"
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/mobileapps", createCommand);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    private async Task<MobileAppDto> GetFirstMobileAppAsync()
    {
        var response = await _client.GetAsync("/api/mobileapps");
        response.EnsureSuccessStatusCode();
        
        var content = await response.Content.ReadAsStringAsync();
        var apps = JsonSerializer.Deserialize<List<MobileAppDto>>(content, _jsonOptions);
        
        return apps!.First();
    }
}
