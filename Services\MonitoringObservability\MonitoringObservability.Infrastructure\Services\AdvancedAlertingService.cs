using Microsoft.Extensions.Logging;
using MonitoringObservability.Domain.Entities;
using MonitoringObservability.Domain.Enums;
using MonitoringObservability.Domain.Interfaces;
using System.Collections.Concurrent;
using System.Text.Json;

namespace MonitoringObservability.Infrastructure.Services;

/// <summary>
/// Advanced alerting service with complex rules, correlation, and intelligent routing
/// </summary>
public class AdvancedAlertingService : IAdvancedAlertingService
{
    private readonly IAlertRuleRepository _alertRuleRepository;
    private readonly IAlertCorrelationRepository _correlationRepository;
    private readonly IAlertRepository _alertRepository;
    private readonly INotificationService _notificationService;
    private readonly ILogger<AdvancedAlertingService> _logger;
    
    // In-memory caches for performance
    private readonly ConcurrentDictionary<Guid, AlertRule> _activeRules = new();
    private readonly ConcurrentDictionary<Guid, AlertCorrelationRule> _correlationRules = new();
    private readonly ConcurrentDictionary<string, AlertCorrelationGroup> _activeGroups = new();

    public AdvancedAlertingService(
        IAlertRuleRepository alertRuleRepository,
        IAlertCorrelationRepository correlationRepository,
        IAlertRepository alertRepository,
        INotificationService notificationService,
        ILogger<AdvancedAlertingService> logger)
    {
        _alertRuleRepository = alertRuleRepository ?? throw new ArgumentNullException(nameof(alertRuleRepository));
        _correlationRepository = correlationRepository ?? throw new ArgumentNullException(nameof(correlationRepository));
        _alertRepository = alertRepository ?? throw new ArgumentNullException(nameof(alertRepository));
        _notificationService = notificationService ?? throw new ArgumentNullException(nameof(notificationService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task InitializeAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Initializing advanced alerting service");

            // Load active alert rules
            var activeRules = await _alertRuleRepository.GetActiveRulesAsync(cancellationToken);
            foreach (var rule in activeRules)
            {
                _activeRules.TryAdd(rule.Id, rule);
            }

            // Load correlation rules
            var correlationRules = await _correlationRepository.GetActiveRulesAsync(cancellationToken);
            foreach (var rule in correlationRules)
            {
                _correlationRules.TryAdd(rule.Id, rule);
            }

            // Load active correlation groups
            var activeGroups = await _correlationRepository.GetActiveGroupsAsync(cancellationToken);
            foreach (var group in activeGroups)
            {
                _activeGroups.TryAdd(group.GroupKey, group);
            }

            _logger.LogInformation("Advanced alerting service initialized with {RuleCount} rules and {CorrelationRuleCount} correlation rules",
                _activeRules.Count, _correlationRules.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize advanced alerting service");
            throw;
        }
    }

    public async Task EvaluateRulesAsync(Dictionary<string, object> context, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Evaluating {RuleCount} alert rules", _activeRules.Count);

            var evaluationTasks = _activeRules.Values
                .Where(rule => rule.ShouldEvaluate())
                .Select(rule => EvaluateRuleAsync(rule, context, cancellationToken));

            await Task.WhenAll(evaluationTasks);

            _logger.LogDebug("Completed rule evaluation");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during rule evaluation");
            throw;
        }
    }

    public async Task ProcessAlertAsync(Alert alert, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Processing alert {AlertId} for correlation", alert.Id);

            // Apply correlation rules
            await ApplyCorrelationRulesAsync(alert, cancellationToken);

            // Execute alert actions
            await ExecuteAlertActionsAsync(alert, cancellationToken);

            _logger.LogDebug("Completed processing alert {AlertId}", alert.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing alert {AlertId}", alert.Id);
            throw;
        }
    }

    public async Task<AlertCorrelationGroup?> CorrelateAlertsAsync(List<Alert> alerts, CancellationToken cancellationToken = default)
    {
        try
        {
            if (alerts.Count < 2)
                return null;

            _logger.LogDebug("Attempting to correlate {AlertCount} alerts", alerts.Count);

            // Find applicable correlation rules
            var applicableRules = _correlationRules.Values
                .Where(rule => rule.CanCorrelateAlerts(alerts))
                .OrderByDescending(rule => rule.Priority)
                .ToList();

            foreach (var rule in applicableRules)
            {
                var groupKey = rule.GenerateGroupKey(alerts);
                
                // Check if group already exists
                if (_activeGroups.TryGetValue(groupKey, out var existingGroup))
                {
                    // Add new alerts to existing group
                    foreach (var alert in alerts)
                    {
                        existingGroup.AddAlert(alert.Id);
                    }
                    
                    await _correlationRepository.UpdateGroupAsync(existingGroup, cancellationToken);
                    
                    _logger.LogInformation("Added {AlertCount} alerts to existing correlation group {GroupKey}",
                        alerts.Count, groupKey);
                    
                    return existingGroup;
                }
                else
                {
                    // Create new correlation group
                    var alertIds = alerts.Select(a => a.Id).ToList();
                    var newGroup = rule.CreateGroup(groupKey, alertIds);
                    
                    _activeGroups.TryAdd(groupKey, newGroup);
                    await _correlationRepository.AddGroupAsync(newGroup, cancellationToken);
                    
                    _logger.LogInformation("Created new correlation group {GroupKey} with {AlertCount} alerts",
                        groupKey, alerts.Count);
                    
                    return newGroup;
                }
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error correlating alerts");
            throw;
        }
    }

    public async Task EscalateAlertAsync(Alert alert, int escalationLevel, string reason, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Escalating alert {AlertId} to level {EscalationLevel}: {Reason}",
                alert.Id, escalationLevel, reason);

            // Find applicable alert rules
            var applicableRules = _activeRules.Values
                .Where(rule => IsRuleApplicableToAlert(rule, alert))
                .ToList();

            foreach (var rule in applicableRules)
            {
                var escalation = rule.GetNextEscalation(escalationLevel);
                if (escalation != null)
                {
                    // Execute escalation
                    await ExecuteEscalationAsync(alert, escalation, cancellationToken);
                    
                    // Update alert escalation level
                    alert.Escalate(escalation.Level, reason);
                    await _alertRepository.UpdateAsync(alert, cancellationToken);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error escalating alert {AlertId}", alert.Id);
            throw;
        }
    }

    public async Task SuppressAlertsAsync(List<Alert> alerts, TimeSpan duration, string reason, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Suppressing {AlertCount} alerts for {Duration}: {Reason}",
                alerts.Count, duration, reason);

            foreach (var alert in alerts)
            {
                alert.Suppress(duration, userId, reason);
                await _alertRepository.UpdateAsync(alert, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error suppressing alerts");
            throw;
        }
    }

    public async Task CleanupExpiredGroupsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var expiredGroups = _activeGroups.Values
                .Where(group => group.IsExpired())
                .ToList();

            foreach (var group in expiredGroups)
            {
                group.Close("Expired");
                await _correlationRepository.UpdateGroupAsync(group, cancellationToken);
                _activeGroups.TryRemove(group.GroupKey, out _);
            }

            if (expiredGroups.Any())
            {
                _logger.LogInformation("Cleaned up {ExpiredGroupCount} expired correlation groups", expiredGroups.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up expired groups");
            throw;
        }
    }

    private async Task EvaluateRuleAsync(AlertRule rule, Dictionary<string, object> context, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Evaluating rule {RuleName}", rule.Name);

            // Evaluate rule conditions
            var conditionResults = new List<bool>();
            
            foreach (var condition in rule.Conditions.Where(c => c.IsEnabled))
            {
                var result = await EvaluateConditionAsync(condition, context, cancellationToken);
                conditionResults.Add(result);
            }

            // Determine if rule should trigger
            bool shouldTrigger = rule.Conditions.Any() && conditionResults.All(r => r);

            if (shouldTrigger)
            {
                await TriggerRuleAsync(rule, context, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error evaluating rule {RuleName}", rule.Name);
        }
    }

    private async Task<bool> EvaluateConditionAsync(AlertRuleCondition condition, Dictionary<string, object> context, CancellationToken cancellationToken)
    {
        try
        {
            return condition.ConditionType switch
            {
                AlertConditionType.Threshold => EvaluateThresholdCondition(condition, context),
                AlertConditionType.Range => EvaluateRangeCondition(condition, context),
                AlertConditionType.Rate => await EvaluateRateConditionAsync(condition, context, cancellationToken),
                AlertConditionType.Count => await EvaluateCountConditionAsync(condition, context, cancellationToken),
                AlertConditionType.Composite => await EvaluateCompositeConditionAsync(condition, context, cancellationToken),
                AlertConditionType.Pattern => await EvaluatePatternConditionAsync(condition, context, cancellationToken),
                AlertConditionType.Anomaly => await EvaluateAnomalyConditionAsync(condition, context, cancellationToken),
                AlertConditionType.Custom => await EvaluateCustomConditionAsync(condition, context, cancellationToken),
                _ => false
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error evaluating condition {ConditionId}", condition.Id);
            return false;
        }
    }

    private bool EvaluateThresholdCondition(AlertRuleCondition condition, Dictionary<string, object> context)
    {
        // Parse expression like "cpu_usage > 80"
        var parts = condition.Expression.Split(' ', StringSplitOptions.RemoveEmptyEntries);
        if (parts.Length != 3)
            return false;

        var metricName = parts[0];
        var operatorStr = parts[1];
        var thresholdStr = parts[2];

        if (!context.TryGetValue(metricName, out var valueObj) || 
            !double.TryParse(thresholdStr, out var threshold))
            return false;

        var value = Convert.ToDouble(valueObj);

        return operatorStr switch
        {
            ">" => value > threshold,
            ">=" => value >= threshold,
            "<" => value < threshold,
            "<=" => value <= threshold,
            "==" => Math.Abs(value - threshold) < 0.001,
            "!=" => Math.Abs(value - threshold) >= 0.001,
            _ => false
        };
    }

    private bool EvaluateRangeCondition(AlertRuleCondition condition, Dictionary<string, object> context)
    {
        // Parse expression like "response_time between 100 and 500"
        var expression = condition.Expression.ToLowerInvariant();
        var parts = expression.Split(' ', StringSplitOptions.RemoveEmptyEntries);
        
        if (parts.Length != 5 || parts[1] != "between" || parts[3] != "and")
            return false;

        var metricName = parts[0];
        if (!context.TryGetValue(metricName, out var valueObj) ||
            !double.TryParse(parts[2], out var minValue) ||
            !double.TryParse(parts[4], out var maxValue))
            return false;

        var value = Convert.ToDouble(valueObj);
        return value >= minValue && value <= maxValue;
    }

    private async Task<bool> EvaluateRateConditionAsync(AlertRuleCondition condition, Dictionary<string, object> context, CancellationToken cancellationToken)
    {
        // Implementation for rate-based conditions (e.g., "error_rate > 5% over 5 minutes")
        // This would require historical data analysis
        await Task.CompletedTask;
        return false; // Placeholder
    }

    private async Task<bool> EvaluateCountConditionAsync(AlertRuleCondition condition, Dictionary<string, object> context, CancellationToken cancellationToken)
    {
        // Implementation for count-based conditions (e.g., "error_count > 10 in last 5 minutes")
        await Task.CompletedTask;
        return false; // Placeholder
    }

    private async Task<bool> EvaluateCompositeConditionAsync(AlertRuleCondition condition, Dictionary<string, object> context, CancellationToken cancellationToken)
    {
        // Implementation for composite conditions combining multiple metrics
        await Task.CompletedTask;
        return false; // Placeholder
    }

    private async Task<bool> EvaluatePatternConditionAsync(AlertRuleCondition condition, Dictionary<string, object> context, CancellationToken cancellationToken)
    {
        // Implementation for pattern-based conditions
        await Task.CompletedTask;
        return false; // Placeholder
    }

    private async Task<bool> EvaluateAnomalyConditionAsync(AlertRuleCondition condition, Dictionary<string, object> context, CancellationToken cancellationToken)
    {
        // Implementation for anomaly detection conditions
        await Task.CompletedTask;
        return false; // Placeholder
    }

    private async Task<bool> EvaluateCustomConditionAsync(AlertRuleCondition condition, Dictionary<string, object> context, CancellationToken cancellationToken)
    {
        // Implementation for custom script-based conditions
        await Task.CompletedTask;
        return false; // Placeholder
    }

    private async Task TriggerRuleAsync(AlertRule rule, Dictionary<string, object> context, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Triggering alert rule {RuleName}", rule.Name);

            // Create alert based on rule
            var alert = CreateAlertFromRule(rule, context);
            
            // Save alert
            await _alertRepository.AddAsync(alert, cancellationToken);
            
            // Record rule trigger
            rule.RecordTrigger();
            await _alertRuleRepository.UpdateAsync(rule, cancellationToken);
            
            // Execute rule actions
            await ExecuteRuleActionsAsync(rule, alert, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error triggering rule {RuleName}", rule.Name);
        }
    }

    private Alert CreateAlertFromRule(AlertRule rule, Dictionary<string, object> context)
    {
        var title = rule.Name;
        var description = rule.Description;
        var severity = rule.DefaultSeverity;
        var source = "AdvancedAlertingService";
        var serviceName = rule.ServiceName ?? "Unknown";
        var metricName = rule.MetricName ?? "Unknown";
        
        // Extract current value from context
        var currentValue = 0.0;
        if (!string.IsNullOrEmpty(metricName) && context.TryGetValue(metricName, out var valueObj))
        {
            currentValue = Convert.ToDouble(valueObj);
        }

        // Create threshold (simplified)
        var threshold = new AlertThreshold(metricName, 0, 0, ">=", TimeSpan.FromMinutes(5));

        return new Alert(title, description, severity, source, serviceName, metricName, currentValue, threshold, rule.Tags);
    }

    private async Task ExecuteRuleActionsAsync(AlertRule rule, Alert alert, CancellationToken cancellationToken)
    {
        var actions = rule.GetActionsForSeverity(alert.Severity);
        
        foreach (var action in actions.Where(a => a.IsEnabled))
        {
            await ExecuteActionAsync(action, alert, cancellationToken);
        }
    }

    private async Task ExecuteActionAsync(AlertRuleAction action, Alert alert, CancellationToken cancellationToken)
    {
        try
        {
            switch (action.ActionType)
            {
                case AlertActionType.Notification:
                    await ExecuteNotificationActionAsync(action, alert, cancellationToken);
                    break;
                case AlertActionType.Webhook:
                    await ExecuteWebhookActionAsync(action, alert, cancellationToken);
                    break;
                case AlertActionType.CreateIncident:
                    await ExecuteCreateIncidentActionAsync(action, alert, cancellationToken);
                    break;
                case AlertActionType.Escalate:
                    await ExecuteEscalateActionAsync(action, alert, cancellationToken);
                    break;
                case AlertActionType.Suppress:
                    await ExecuteSuppressActionAsync(action, alert, cancellationToken);
                    break;
                case AlertActionType.AutoRemediation:
                    await ExecuteAutoRemediationActionAsync(action, alert, cancellationToken);
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing action {ActionType} for alert {AlertId}", 
                action.ActionType, alert.Id);
        }
    }

    private async Task ExecuteNotificationActionAsync(AlertRuleAction action, Alert alert, CancellationToken cancellationToken)
    {
        if (action.Parameters.TryGetValue("channels", out var channelsObj) &&
            action.Parameters.TryGetValue("recipients", out var recipientsObj))
        {
            var channels = JsonSerializer.Deserialize<List<NotificationChannel>>(channelsObj.ToString() ?? "[]");
            var recipients = JsonSerializer.Deserialize<List<string>>(recipientsObj.ToString() ?? "[]");

            foreach (var channel in channels ?? new List<NotificationChannel>())
            {
                foreach (var recipient in recipients ?? new List<string>())
                {
                    await _notificationService.SendNotificationAsync(
                        channel, recipient, alert.Title, alert.Description, cancellationToken);
                }
            }
        }
    }

    private async Task ExecuteWebhookActionAsync(AlertRuleAction action, Alert alert, CancellationToken cancellationToken)
    {
        // Implementation for webhook actions
        await Task.CompletedTask;
    }

    private async Task ExecuteCreateIncidentActionAsync(AlertRuleAction action, Alert alert, CancellationToken cancellationToken)
    {
        // Implementation for creating incidents
        await Task.CompletedTask;
    }

    private async Task ExecuteEscalateActionAsync(AlertRuleAction action, Alert alert, CancellationToken cancellationToken)
    {
        // Implementation for escalation actions
        await Task.CompletedTask;
    }

    private async Task ExecuteSuppressActionAsync(AlertRuleAction action, Alert alert, CancellationToken cancellationToken)
    {
        // Implementation for suppression actions
        await Task.CompletedTask;
    }

    private async Task ExecuteAutoRemediationActionAsync(AlertRuleAction action, Alert alert, CancellationToken cancellationToken)
    {
        // Implementation for auto-remediation actions
        await Task.CompletedTask;
    }

    private async Task ApplyCorrelationRulesAsync(Alert alert, CancellationToken cancellationToken)
    {
        // Find recent alerts that might correlate with this one
        var recentAlerts = await _alertRepository.GetRecentAlertsAsync(TimeSpan.FromMinutes(30), cancellationToken);
        var candidateAlerts = recentAlerts.Where(a => a.Id != alert.Id).ToList();
        candidateAlerts.Add(alert);

        // Try to correlate with existing alerts
        await CorrelateAlertsAsync(candidateAlerts, cancellationToken);
    }

    private async Task ExecuteAlertActionsAsync(Alert alert, CancellationToken cancellationToken)
    {
        // Find rules that apply to this alert and execute their actions
        var applicableRules = _activeRules.Values
            .Where(rule => IsRuleApplicableToAlert(rule, alert))
            .ToList();

        foreach (var rule in applicableRules)
        {
            await ExecuteRuleActionsAsync(rule, alert, cancellationToken);
        }
    }

    private async Task ExecuteEscalationAsync(Alert alert, AlertRuleEscalation escalation, CancellationToken cancellationToken)
    {
        foreach (var channel in escalation.Channels)
        {
            foreach (var recipient in escalation.Recipients)
            {
                var escalatedTitle = $"[ESCALATED L{escalation.Level}] {alert.Title}";
                await _notificationService.SendNotificationAsync(
                    channel, recipient, escalatedTitle, alert.Description, cancellationToken);
            }
        }
    }

    private bool IsRuleApplicableToAlert(AlertRule rule, Alert alert)
    {
        // Check if rule applies to this alert based on service, metric, tags, etc.
        if (!string.IsNullOrEmpty(rule.ServiceName) && rule.ServiceName != alert.ServiceName)
            return false;

        if (!string.IsNullOrEmpty(rule.MetricName) && rule.MetricName != alert.MetricName)
            return false;

        // Check tag matching
        foreach (var ruleTag in rule.Tags)
        {
            if (!alert.Tags.TryGetValue(ruleTag.Key, out var alertTagValue) || 
                alertTagValue != ruleTag.Value)
                return false;
        }

        return true;
    }
}
