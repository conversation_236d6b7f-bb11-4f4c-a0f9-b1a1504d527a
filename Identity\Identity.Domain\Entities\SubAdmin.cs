using System;
using System.Collections.Generic;
using System.Linq;
using Identity.Domain.Common;
using Identity.Domain.Common.Events;
using Identity.Domain.Exceptions;
using Identity.Domain.ValueObjects;

namespace Identity.Domain.Entities
{
    public enum SubAdminStatus
    {
        Active,
        Inactive,
        Suspended,
        Locked
    }

    public class SubAdminCreatedEvent : DomainEvent
    {
        public Guid SubAdminId { get; }
        public string Email { get; }
        public Guid CreatedBy { get; }

        public SubAdminCreatedEvent(Guid subAdminId, string email, Guid createdBy)
        {
            SubAdminId = subAdminId;
            Email = email;
            CreatedBy = createdBy;
        }
    }

    public class SubAdminStatusChangedEvent : DomainEvent
    {
        public Guid SubAdminId { get; }
        public SubAdminStatus OldStatus { get; }
        public SubAdminStatus NewStatus { get; }
        public Guid ChangedBy { get; }

        public SubAdminStatusChangedEvent(Guid subAdminId, SubAdminStatus oldStatus, SubAdminStatus newStatus, Guid changedBy)
        {
            SubAdminId = subAdminId;
            OldStatus = oldStatus;
            NewStatus = newStatus;
            ChangedBy = changedBy;
        }
    }

    public class SubAdmin : AggregateRoot
    {
        public Guid UserId { get; private set; }
        public string Name { get; private set; }
        public Email Email { get; private set; }
        public SubAdminStatus Status { get; private set; }
        public DateTime? LastLoginAt { get; private set; }
        public string LastLoginIp { get; private set; }
        public bool TwoFactorEnabled { get; private set; }
        public string TwoFactorSecret { get; private set; }
        public List<string> AllowedIpAddresses { get; private set; }
        public DateTime? TemporaryAccessExpiry { get; private set; }
        public string Department { get; private set; }
        public Guid? SupervisorId { get; private set; }
        public Guid CreatedBy { get; private set; }
        public DateTime? LastPasswordChangeAt { get; private set; }
        public bool RequirePasswordChange { get; private set; }

        private readonly List<UserRole> _roles = new();
        public IReadOnlyCollection<UserRole> Roles => _roles.AsReadOnly();

        private readonly List<SubAdminSession> _sessions = new();
        public IReadOnlyCollection<SubAdminSession> Sessions => _sessions.AsReadOnly();

        private SubAdmin() 
        {
            AllowedIpAddresses = new List<string>();
        }

        public SubAdmin(Guid userId, string name, Email email, Guid createdBy, string department = null)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new DomainException("Sub-admin name cannot be empty");

            if (email == null)
                throw new DomainException("Email cannot be null");

            UserId = userId;
            Name = name;
            Email = email;
            Status = SubAdminStatus.Active;
            CreatedBy = createdBy;
            Department = department ?? string.Empty;
            TwoFactorEnabled = false;
            RequirePasswordChange = true;
            AllowedIpAddresses = new List<string>();
            CreatedAt = DateTime.UtcNow;

            AddDomainEvent(new SubAdminCreatedEvent(Id, email.Value, createdBy));
        }

        public void UpdateProfile(string name, string department)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new DomainException("Sub-admin name cannot be empty");

            Name = name;
            Department = department ?? string.Empty;
            UpdatedAt = DateTime.UtcNow;
        }

        public void Activate(Guid activatedBy)
        {
            if (Status == SubAdminStatus.Active)
                return;

            var oldStatus = Status;
            Status = SubAdminStatus.Active;
            UpdatedAt = DateTime.UtcNow;

            AddDomainEvent(new SubAdminStatusChangedEvent(Id, oldStatus, Status, activatedBy));
        }

        public void Deactivate(Guid deactivatedBy)
        {
            if (Status == SubAdminStatus.Inactive)
                return;

            var oldStatus = Status;
            Status = SubAdminStatus.Inactive;
            UpdatedAt = DateTime.UtcNow;

            AddDomainEvent(new SubAdminStatusChangedEvent(Id, oldStatus, Status, deactivatedBy));
        }

        public void Suspend(Guid suspendedBy)
        {
            var oldStatus = Status;
            Status = SubAdminStatus.Suspended;
            UpdatedAt = DateTime.UtcNow;

            AddDomainEvent(new SubAdminStatusChangedEvent(Id, oldStatus, Status, suspendedBy));
        }

        public void Lock(Guid lockedBy)
        {
            var oldStatus = Status;
            Status = SubAdminStatus.Locked;
            UpdatedAt = DateTime.UtcNow;

            AddDomainEvent(new SubAdminStatusChangedEvent(Id, oldStatus, Status, lockedBy));
        }

        public void EnableTwoFactor(string secret)
        {
            if (string.IsNullOrWhiteSpace(secret))
                throw new DomainException("Two-factor secret cannot be empty");

            TwoFactorEnabled = true;
            TwoFactorSecret = secret;
            UpdatedAt = DateTime.UtcNow;
        }

        public void DisableTwoFactor()
        {
            TwoFactorEnabled = false;
            TwoFactorSecret = null;
            UpdatedAt = DateTime.UtcNow;
        }

        public void SetAllowedIpAddresses(List<string> ipAddresses)
        {
            AllowedIpAddresses = ipAddresses ?? new List<string>();
            UpdatedAt = DateTime.UtcNow;
        }

        public void AddAllowedIpAddress(string ipAddress)
        {
            if (string.IsNullOrWhiteSpace(ipAddress))
                throw new DomainException("IP address cannot be empty");

            if (!AllowedIpAddresses.Contains(ipAddress))
            {
                AllowedIpAddresses.Add(ipAddress);
                UpdatedAt = DateTime.UtcNow;
            }
        }

        public void RemoveAllowedIpAddress(string ipAddress)
        {
            if (AllowedIpAddresses.Contains(ipAddress))
            {
                AllowedIpAddresses.Remove(ipAddress);
                UpdatedAt = DateTime.UtcNow;
            }
        }

        public bool IsIpAddressAllowed(string ipAddress)
        {
            if (AllowedIpAddresses == null || !AllowedIpAddresses.Any())
                return true; // No restrictions

            return AllowedIpAddresses.Contains(ipAddress);
        }

        public void SetTemporaryAccess(DateTime expiry)
        {
            if (expiry <= DateTime.UtcNow)
                throw new DomainException("Temporary access expiry must be in the future");

            TemporaryAccessExpiry = expiry;
            UpdatedAt = DateTime.UtcNow;
        }

        public void ClearTemporaryAccess()
        {
            TemporaryAccessExpiry = null;
            UpdatedAt = DateTime.UtcNow;
        }

        public bool HasTemporaryAccess()
        {
            return TemporaryAccessExpiry.HasValue && TemporaryAccessExpiry.Value > DateTime.UtcNow;
        }

        public void RecordLogin(string ipAddress)
        {
            LastLoginAt = DateTime.UtcNow;
            LastLoginIp = ipAddress;
            UpdatedAt = DateTime.UtcNow;
        }

        public void AddRole(Role role)
        {
            if (role == null)
                throw new DomainException("Role cannot be null");

            if (_roles.Any(r => r.RoleId == role.Id))
                return;

            _roles.Add(new UserRole(UserId, role.Id));
            UpdatedAt = DateTime.UtcNow;
        }

        public void RemoveRole(Role role)
        {
            if (role == null)
                throw new DomainException("Role cannot be null");

            var userRole = _roles.FirstOrDefault(r => r.RoleId == role.Id);
            if (userRole != null)
            {
                _roles.Remove(userRole);
                UpdatedAt = DateTime.UtcNow;
            }
        }

        public void SetSupervisor(Guid supervisorId)
        {
            SupervisorId = supervisorId;
            UpdatedAt = DateTime.UtcNow;
        }

        public void ClearSupervisor()
        {
            SupervisorId = null;
            UpdatedAt = DateTime.UtcNow;
        }

        public void RequirePasswordChangeOnNextLogin()
        {
            RequirePasswordChange = true;
            UpdatedAt = DateTime.UtcNow;
        }

        public void PasswordChanged()
        {
            LastPasswordChangeAt = DateTime.UtcNow;
            RequirePasswordChange = false;
            UpdatedAt = DateTime.UtcNow;
        }

        public bool IsActive()
        {
            return Status == SubAdminStatus.Active && 
                   (!TemporaryAccessExpiry.HasValue || TemporaryAccessExpiry.Value > DateTime.UtcNow);
        }

        public void AddSession(SubAdminSession session)
        {
            if (session == null)
                throw new DomainException("Session cannot be null");

            _sessions.Add(session);
            UpdatedAt = DateTime.UtcNow;
        }
    }
}
