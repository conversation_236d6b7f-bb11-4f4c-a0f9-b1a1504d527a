using NBomber.CSharp;
using NBomber.Http.CSharp;
using System.Net.Http.Json;
using SubscriptionManagement.Application.Commands.SendManualNotification;
using SubscriptionManagement.Application.Commands.TriggerSubscriptionAlerts;
using SubscriptionManagement.Domain.Enums;
using Xunit;

namespace SubscriptionManagement.Tests.Performance
{
    public class NotificationPerformanceTests
    {
        private const string BaseUrl = "https://localhost:7001"; // Adjust based on your setup
        private const string AdminToken = "Bearer test-admin-token"; // Use valid test token

        [Fact]
        public void ManualNotification_LoadTest_ShouldHandleConcurrentRequests()
        {
            var httpClient = new HttpClient();
            httpClient.DefaultRequestHeaders.Add("Authorization", AdminToken);

            var scenario = Scenario.Create("manual_notification_load_test", async context =>
            {
                var request = new SendManualNotificationRequest
                {
                    UserId = Guid.NewGuid(),
                    NotificationType = NotificationType.Custom,
                    Channels = new List<string> { "Email" },
                    CustomMessage = $"Load test notification {context.ScenarioInfo.CurrentOperation}",
                    Language = "en"
                };

                var response = await httpClient.PostAsJsonAsync($"{BaseUrl}/api/admin/notifications/subscription", request);
                
                return response.IsSuccessStatusCode ? Response.Ok() : Response.Fail();
            })
            .WithLoadSimulations(
                Simulation.InjectPerSec(rate: 10, during: TimeSpan.FromMinutes(2)),
                Simulation.KeepConstant(copies: 5, during: TimeSpan.FromMinutes(1))
            );

            var stats = NBomberRunner
                .RegisterScenarios(scenario)
                .Run();

            // Assert performance requirements
            var scnStats = stats.AllScenarios.First();
            Assert.True(scnStats.Ok.Request.Mean < 2000, "Average response time should be less than 2 seconds");
            Assert.True(scnStats.Ok.Request.Count > 0, "Should have successful requests");
            Assert.True(scnStats.Fail.Request.Count == 0, "Should have no failed requests");
        }

        [Fact]
        public void BulkNotificationAlerts_StressTest_ShouldHandleHighVolume()
        {
            var httpClient = new HttpClient();
            httpClient.DefaultRequestHeaders.Add("Authorization", AdminToken);

            var scenario = Scenario.Create("bulk_alerts_stress_test", async context =>
            {
                var request = new TriggerSubscriptionAlertsRequest
                {
                    SubscriptionStatuses = new List<SubscriptionStatus> { SubscriptionStatus.Active },
                    ExpiryDateFrom = DateTime.UtcNow,
                    ExpiryDateTo = DateTime.UtcNow.AddDays(7),
                    AlertType = NotificationType.ExpiryWarning,
                    Channels = new List<string> { "Email" },
                    Language = "en",
                    DryRun = true, // Use dry run for performance testing
                    BatchSize = 100
                };

                var response = await httpClient.PostAsJsonAsync($"{BaseUrl}/api/admin/notifications/subscription-alerts", request);
                
                return response.IsSuccessStatusCode ? Response.Ok() : Response.Fail();
            })
            .WithLoadSimulations(
                Simulation.InjectPerSec(rate: 5, during: TimeSpan.FromMinutes(3)),
                Simulation.RampingInject(rate: 10, interval: TimeSpan.FromSeconds(30), during: TimeSpan.FromMinutes(2))
            );

            var stats = NBomberRunner
                .RegisterScenarios(scenario)
                .Run();

            // Assert performance requirements for bulk operations
            var scnStats = stats.AllScenarios.First();
            Assert.True(scnStats.Ok.Request.Mean < 5000, "Bulk operations should complete within 5 seconds on average");
            Assert.True(scnStats.Ok.Request.Count > 0, "Should have successful requests");
        }

        [Fact]
        public void PaymentProofUpload_ConcurrencyTest_ShouldHandleSimultaneousUploads()
        {
            var httpClient = new HttpClient();
            httpClient.DefaultRequestHeaders.Add("Authorization", AdminToken);

            var scenario = Scenario.Create("payment_proof_upload_test", async context =>
            {
                var subscriptionId = Guid.NewGuid();
                
                // Create mock file content
                var fileContent = System.Text.Encoding.UTF8.GetBytes($"Mock payment proof {context.ScenarioInfo.CurrentOperation}");
                
                using var formContent = new MultipartFormDataContent();
                formContent.Add(new StringContent("1000"), "Amount");
                formContent.Add(new StringContent("INR"), "Currency");
                formContent.Add(new StringContent(DateTime.UtcNow.AddDays(-1).ToString("O")), "PaymentDate");
                formContent.Add(new StringContent("Performance test upload"), "Notes");
                formContent.Add(new ByteArrayContent(fileContent), "ProofImage", "test-proof.jpg");

                var response = await httpClient.PostAsync($"{BaseUrl}/api/subscriptions/{subscriptionId}/payment-proof", formContent);
                
                return response.IsSuccessStatusCode ? Response.Ok() : Response.Fail();
            })
            .WithLoadSimulations(
                Simulation.InjectPerSec(rate: 3, during: TimeSpan.FromMinutes(2))
            );

            var stats = NBomberRunner
                .RegisterScenarios(scenario)
                .Run();

            // Assert file upload performance
            var scnStats = stats.AllScenarios.First();
            Assert.True(scnStats.Ok.Request.Mean < 3000, "File uploads should complete within 3 seconds on average");
            Assert.True(scnStats.Ok.Request.Count > 0, "Should have successful uploads");
        }

        [Fact]
        public void AdminDashboard_ResponseTimeTest_ShouldLoadQuickly()
        {
            var httpClient = new HttpClient();
            httpClient.DefaultRequestHeaders.Add("Authorization", AdminToken);

            var scenario = Scenario.Create("dashboard_response_test", async context =>
            {
                var response = await httpClient.GetAsync($"{BaseUrl}/api/admin/dashboard/overview");
                
                return response.IsSuccessStatusCode ? Response.Ok() : Response.Fail();
            })
            .WithLoadSimulations(
                Simulation.InjectPerSec(rate: 20, during: TimeSpan.FromMinutes(1))
            );

            var stats = NBomberRunner
                .RegisterScenarios(scenario)
                .Run();

            // Assert dashboard performance
            var scnStats = stats.AllScenarios.First();
            Assert.True(scnStats.Ok.Request.Mean < 1000, "Dashboard should load within 1 second on average");
            Assert.True(scnStats.Ok.Request.Count > 0, "Should have successful requests");
        }

        [Fact]
        public void NotificationHistory_PaginationPerformance_ShouldHandleLargeDatasets()
        {
            var httpClient = new HttpClient();
            httpClient.DefaultRequestHeaders.Add("Authorization", AdminToken);

            var scenario = Scenario.Create("notification_history_pagination_test", async context =>
            {
                var pageNumber = Random.Shared.Next(1, 100);
                var pageSize = 50;
                var fromDate = DateTime.UtcNow.AddDays(-30);
                var toDate = DateTime.UtcNow;

                var response = await httpClient.GetAsync(
                    $"{BaseUrl}/api/admin/notifications/history?pageNumber={pageNumber}&pageSize={pageSize}&fromDate={fromDate:O}&toDate={toDate:O}");
                
                return response.IsSuccessStatusCode ? Response.Ok() : Response.Fail();
            })
            .WithLoadSimulations(
                Simulation.InjectPerSec(rate: 15, during: TimeSpan.FromMinutes(2))
            );

            var stats = NBomberRunner
                .RegisterScenarios(scenario)
                .Run();

            // Assert pagination performance
            var scnStats = stats.AllScenarios.First();
            Assert.True(scnStats.Ok.Request.Mean < 1500, "Paginated queries should complete within 1.5 seconds on average");
            Assert.True(scnStats.Ok.Request.Count > 0, "Should have successful requests");
        }

        [Fact]
        public void RateLimit_EnforcementTest_ShouldThrottleExcessiveRequests()
        {
            var httpClient = new HttpClient();
            httpClient.DefaultRequestHeaders.Add("Authorization", AdminToken);

            var scenario = Scenario.Create("rate_limit_test", async context =>
            {
                var request = new SendManualNotificationRequest
                {
                    UserId = Guid.NewGuid(),
                    NotificationType = NotificationType.Custom,
                    Channels = new List<string> { "Email" },
                    CustomMessage = "Rate limit test",
                    Language = "en"
                };

                var response = await httpClient.PostAsJsonAsync($"{BaseUrl}/api/admin/notifications/subscription", request);
                
                // Accept both success and rate limit responses as valid
                return response.IsSuccessStatusCode || response.StatusCode == System.Net.HttpStatusCode.TooManyRequests 
                    ? Response.Ok() 
                    : Response.Fail();
            })
            .WithLoadSimulations(
                Simulation.InjectPerSec(rate: 100, during: TimeSpan.FromMinutes(1)) // Exceed rate limit intentionally
            );

            var stats = NBomberRunner
                .RegisterScenarios(scenario)
                .Run();

            // Assert rate limiting is working
            var scnStats = stats.AllScenarios.First();
            Assert.True(scnStats.Ok.Request.Count > 0, "Should have some successful requests");
            // Note: In a real test, you'd check for 429 responses in the logs or custom metrics
        }

        [Fact]
        public void EndToEnd_NotificationWorkflow_PerformanceTest()
        {
            var httpClient = new HttpClient();
            httpClient.DefaultRequestHeaders.Add("Authorization", AdminToken);

            var scenario = Scenario.Create("e2e_notification_workflow", async context =>
            {
                try
                {
                    // Step 1: Send manual notification
                    var notificationRequest = new SendManualNotificationRequest
                    {
                        UserId = Guid.NewGuid(),
                        NotificationType = NotificationType.Custom,
                        Channels = new List<string> { "Email" },
                        CustomMessage = $"E2E test notification {context.ScenarioInfo.CurrentOperation}",
                        Language = "en"
                    };

                    var notificationResponse = await httpClient.PostAsJsonAsync(
                        $"{BaseUrl}/api/admin/notifications/subscription", notificationRequest);

                    if (!notificationResponse.IsSuccessStatusCode)
                        return Response.Fail();

                    // Step 2: Check notification history
                    var historyResponse = await httpClient.GetAsync(
                        $"{BaseUrl}/api/admin/notifications/history?pageSize=10");

                    if (!historyResponse.IsSuccessStatusCode)
                        return Response.Fail();

                    // Step 3: Get dashboard analytics
                    var analyticsResponse = await httpClient.GetAsync(
                        $"{BaseUrl}/api/admin/dashboard/notification-analytics");

                    return analyticsResponse.IsSuccessStatusCode ? Response.Ok() : Response.Fail();
                }
                catch
                {
                    return Response.Fail();
                }
            })
            .WithLoadSimulations(
                Simulation.InjectPerSec(rate: 5, during: TimeSpan.FromMinutes(2))
            );

            var stats = NBomberRunner
                .RegisterScenarios(scenario)
                .Run();

            // Assert end-to-end performance
            var scnStats = stats.AllScenarios.First();
            Assert.True(scnStats.Ok.Request.Mean < 5000, "End-to-end workflow should complete within 5 seconds on average");
            Assert.True(scnStats.Ok.Request.Count > 0, "Should have successful workflows");
        }
    }
}
