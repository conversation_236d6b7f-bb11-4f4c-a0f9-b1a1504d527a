# Content Management Service API Documentation

## Overview

The Content Management Service provides RESTful APIs for managing content, FAQs, and policy documents. All endpoints support JSON request/response format and use JWT authentication where required.

## Base URL
```
https://api.tli.com/content-management
```

## Authentication

Most endpoints require JWT Bearer token authentication:
```
Authorization: Bearer <your-jwt-token>
```

## Content Management API

### Get Content by ID
```http
GET /api/content/{id}
```

**Parameters:**
- `id` (path, required): Content UUID
- `includeVersions` (query, optional): Include version history
- `includeAttachments` (query, optional): Include attachments

**Response:**
```json
{
  "id": "uuid",
  "title": "Content Title",
  "slug": "content-slug",
  "type": "General",
  "status": "Published",
  "contentBody": "Content body text",
  "visibility": {
    "level": "Public",
    "allowedRoles": [],
    "visibleFrom": null,
    "visibleUntil": null
  },
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-01T00:00:00Z"
}
```

### Search Content
```http
GET /api/content/search
```

**Query Parameters:**
- `searchTerm` (optional): Text to search for
- `type` (optional): Content type filter
- `status` (optional): Content status filter
- `tags` (optional): Tag filters (comma-separated)
- `pageNumber` (optional, default: 1): Page number
- `pageSize` (optional, default: 20): Items per page

**Response:**
```json
{
  "items": [
    {
      "id": "uuid",
      "title": "Content Title",
      "type": "General",
      "status": "Published",
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ],
  "totalCount": 100,
  "pageNumber": 1,
  "pageSize": 20,
  "hasNextPage": true
}
```

### Create Content
```http
POST /api/content
Authorization: Bearer <token>
```

**Request Body:**
```json
{
  "title": "New Content Title",
  "slug": "new-content-slug",
  "type": "General",
  "contentBody": "Content body text",
  "description": "Content description",
  "visibility": {
    "level": "Public"
  },
  "tags": ["tag1", "tag2"],
  "publishSchedule": {
    "publishAt": "2024-01-01T00:00:00Z"
  }
}
```

**Response:** Returns created content object with 201 status.

### Update Content
```http
PUT /api/content/{id}
Authorization: Bearer <token>
```

**Request Body:**
```json
{
  "title": "Updated Title",
  "description": "Updated description",
  "contentBody": "Updated content body",
  "tags": ["updated", "tags"]
}
```

### Content Workflow Actions

#### Submit for Review
```http
POST /api/content/{id}/submit-for-review
Authorization: Bearer <token>
```

#### Approve Content
```http
POST /api/content/{id}/approve
Authorization: Bearer <token>
```

#### Publish Content
```http
POST /api/content/{id}/publish
Authorization: Bearer <token>
```

#### Archive Content
```http
POST /api/content/{id}/archive
Authorization: Bearer <token>
```

## FAQ Management API

### Get FAQ by ID
```http
GET /api/faq/{id}
```

**Response:**
```json
{
  "id": "uuid",
  "question": "What is the question?",
  "answer": "This is the answer",
  "category": "General",
  "isHighlighted": false,
  "viewCount": 42,
  "helpfulnessRating": 4.5,
  "totalRatings": 10,
  "roleVisibility": ["User", "Admin"]
}
```

### Search FAQs
```http
GET /api/faq/search
```

**Query Parameters:**
- `searchTerm` (optional): Search in questions and answers
- `category` (optional): FAQ category filter
- `isHighlighted` (optional): Filter highlighted FAQs
- `pageNumber` (optional): Page number
- `pageSize` (optional): Items per page

### Get FAQs by Category
```http
GET /api/faq/category/{category}
```

**Categories:**
- General
- Account
- Billing
- Technical
- Shipping
- Orders
- Payments
- Support
- Legal
- Privacy

### Create FAQ
```http
POST /api/faq
Authorization: Bearer <token>
```

**Request Body:**
```json
{
  "question": "What is the question?",
  "answer": "This is the answer",
  "category": "General",
  "roleVisibility": ["User"],
  "isHighlighted": false,
  "tags": ["faq", "help"]
}
```

### Rate FAQ Helpfulness
```http
POST /api/faq/{id}/rate
Authorization: Bearer <token>
```

**Request Body:**
```json
{
  "rating": 5
}
```

## Policy Management API

### Get Policy by Type
```http
GET /api/policy/type/{policyType}
```

**Policy Types:**
- TermsOfService
- PrivacyPolicy
- CookiePolicy
- DataProtection
- UserAgreement
- ServiceLevelAgreement

**Response:**
```json
{
  "id": "uuid",
  "title": "Privacy Policy",
  "policyType": "PrivacyPolicy",
  "version": "1.0",
  "effectiveDate": "2024-01-01T00:00:00Z",
  "requiresAcceptance": true,
  "applicableRoles": ["User", "Admin"],
  "contentBody": "Policy content..."
}
```

### Get Applicable Policies
```http
GET /api/policy/applicable
Authorization: Bearer <token>
```

Returns policies applicable to the current user's roles.

### Accept Policy
```http
POST /api/policy/{id}/accept
Authorization: Bearer <token>
```

**Response:**
```json
{
  "id": "uuid",
  "policyId": "uuid",
  "userId": "uuid",
  "version": "1.0",
  "acceptedAt": "2024-01-01T00:00:00Z"
}
```

### Get User Policy Status
```http
GET /api/policy/user-status
Authorization: Bearer <token>
```

**Response:**
```json
{
  "userId": "uuid",
  "userName": "John Doe",
  "userRole": "User",
  "policyStatuses": [
    {
      "policyId": "uuid",
      "title": "Privacy Policy",
      "version": "1.0",
      "requiresAcceptance": true,
      "isAccepted": true,
      "acceptedAt": "2024-01-01T00:00:00Z"
    }
  ]
}
```

## Admin & Analytics API

### Admin Dashboard
```http
GET /api/admin/dashboard
Authorization: Bearer <token>
Roles: Admin, ContentManager
```

**Response:**
```json
{
  "contentStatistics": {
    "totalContent": 1000,
    "publishedContent": 800,
    "draftContent": 150,
    "pendingContent": 50
  },
  "faqAnalytics": {
    "totalFaqs": 200,
    "totalViews": 10000,
    "averageRating": 4.2
  },
  "topPerformingContent": [
    {
      "contentId": "uuid",
      "title": "Popular FAQ",
      "viewCount": 500,
      "rating": 4.8
    }
  ]
}
```

### Content Analytics
```http
GET /api/admin/analytics
Authorization: Bearer <token>
```

**Query Parameters:**
- `fromDate` (optional): Start date for analytics
- `toDate` (optional): End date for analytics

### Content Performance
```http
GET /api/admin/performance
Authorization: Bearer <token>
```

**Query Parameters:**
- `limit` (optional, default: 20): Number of top items

## CMS Interface API

### Content Editor
```http
GET /api/cms/editor/{id}
Authorization: Bearer <token>
```

Returns content with editing permissions and available workflow actions.

### Generate Preview
```http
POST /api/cms/preview
Authorization: Bearer <token>
```

**Request Body:**
```json
{
  "title": "Content Title",
  "type": "General",
  "contentBody": "Content to preview"
}
```

**Response:**
```json
{
  "contentId": "uuid",
  "title": "Content Title",
  "excerpt": "Content excerpt...",
  "renderedContent": "<div>Rendered HTML</div>",
  "wordCount": 150,
  "readingTimeMinutes": 1,
  "seoAnalysis": {
    "score": 85,
    "issues": [],
    "recommendations": ["Add meta description"]
  }
}
```

### Validate Content
```http
POST /api/cms/validate
Authorization: Bearer <token>
```

**Request Body:**
```json
{
  "title": "Content Title",
  "slug": "content-slug",
  "contentBody": "Content body"
}
```

**Response:**
```json
{
  "isValid": true,
  "errors": [],
  "warnings": ["Title is quite short"],
  "suggestions": ["Add meta description"]
}
```

## Error Responses

All endpoints return consistent error responses:

```json
{
  "error": {
    "message": "Error description",
    "type": "ValidationException",
    "timestamp": "2024-01-01T00:00:00Z"
  }
}
```

**HTTP Status Codes:**
- `200` - Success
- `201` - Created
- `204` - No Content
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `409` - Conflict
- `500` - Internal Server Error

## Rate Limiting

API endpoints are rate limited:
- Authenticated users: 1000 requests/hour
- Anonymous users: 100 requests/hour
- Admin endpoints: 5000 requests/hour

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
```

## Pagination

List endpoints support pagination:
- `pageNumber`: Page number (1-based)
- `pageSize`: Items per page (max 100)

Response includes pagination metadata:
```json
{
  "items": [...],
  "totalCount": 1000,
  "pageNumber": 1,
  "pageSize": 20,
  "totalPages": 50,
  "hasNextPage": true,
  "hasPreviousPage": false
}
```

## Webhooks

The service can send webhooks for important events:
- Content published
- Policy accepted
- FAQ rated

Configure webhook URLs in the admin panel.
