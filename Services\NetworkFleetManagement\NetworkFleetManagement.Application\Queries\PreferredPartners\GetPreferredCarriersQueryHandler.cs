using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Entities;
using NetworkFleetManagement.Domain.Repositories;

namespace NetworkFleetManagement.Application.Queries.PreferredPartners;

public class GetPreferredCarriersQueryHandler : IRequestHandler<GetPreferredCarriersQuery, List<PreferredPartnerSummaryDto>>
{
    private readonly IPreferredPartnerRepository _preferredPartnerRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetPreferredCarriersQueryHandler> _logger;

    public GetPreferredCarriersQueryHandler(
        IPreferredPartnerRepository preferredPartnerRepository,
        IMapper mapper,
        ILogger<GetPreferredCarriersQueryHandler> logger)
    {
        _preferredPartnerRepository = preferredPartnerRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<List<PreferredPartnerSummaryDto>> Handle(GetPreferredCarriersQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var partners = await _preferredPartnerRepository.GetByUserIdAndPartnerTypeAsync(
                request.BrokerId, 
                PreferredPartnerType.Carrier, 
                request.ActiveOnly, 
                cancellationToken);

            // Filter by route and load type if specified
            if (!string.IsNullOrEmpty(request.Route) || !string.IsNullOrEmpty(request.LoadType))
            {
                partners = partners.Where(p => 
                    (string.IsNullOrEmpty(request.Route) || p.IsEligibleForRoute(request.Route)) &&
                    (string.IsNullOrEmpty(request.LoadType) || p.IsEligibleForLoadType(request.LoadType))
                ).ToList();
            }

            var result = _mapper.Map<List<PreferredPartnerSummaryDto>>(partners);

            if (request.Limit.HasValue)
            {
                result = result.Take(request.Limit.Value).ToList();
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving preferred carriers for broker {BrokerId}", request.BrokerId);
            throw;
        }
    }
}
