using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;
using CommunicationNotification.Infrastructure.Configuration;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Twilio;
using Twilio.Rest.Api.V2010.Account;
using Twilio.Types;

namespace CommunicationNotification.Infrastructure.Providers;

/// <summary>
/// Twilio SMS provider implementation
/// </summary>
public class TwilioSmsProvider : ISmsProvider
{
    private readonly ILogger<TwilioSmsProvider> _logger;
    private readonly TwilioConfiguration _configuration;

    public NotificationChannel Channel => NotificationChannel.Sms;

    public TwilioSmsProvider(
        IConfiguration configuration,
        ILogger<TwilioSmsProvider> logger)
    {
        _logger = logger;
        _configuration = configuration.GetSection("Twilio").Get<TwilioConfiguration>()
            ?? throw new InvalidOperationException("Twilio configuration is missing");

        InitializeTwilio();
    }

    public async Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(_configuration.AccountSid) || 
                string.IsNullOrEmpty(_configuration.AuthToken))
            {
                return false;
            }

            // Test API connectivity by fetching account info
            var account = await AccountResource.FetchAsync(cancellationToken: cancellationToken);
            return account != null && account.Status == AccountResource.StatusEnum.Active;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check Twilio SMS availability");
            return false;
        }
    }

    public async Task<NotificationResult> SendAsync(
        ContactInfo recipient,
        MessageContent content,
        Dictionary<string, string>? metadata = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (!ValidateRecipient(recipient))
            {
                return NotificationResult.Failure("Invalid phone number for SMS");
            }

            var result = await SendSmsAsync(
                recipient.PhoneNumber,
                content.Body,
                true,
                cancellationToken);

            return result.IsSuccess
                ? NotificationResult.Success(result.MessageId!, DeliveryStatus.Sent)
                : NotificationResult.Failure(result.ErrorMessage!);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send SMS to {PhoneNumber}", recipient.PhoneNumber);
            return NotificationResult.Failure($"Failed to send SMS: {ex.Message}");
        }
    }

    public async Task<DeliveryStatus> GetDeliveryStatusAsync(
        string externalId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var message = await MessageResource.FetchAsync(externalId, cancellationToken: cancellationToken);
            
            return message.Status switch
            {
                MessageResource.StatusEnum.Queued => DeliveryStatus.Pending,
                MessageResource.StatusEnum.Sending => DeliveryStatus.Pending,
                MessageResource.StatusEnum.Sent => DeliveryStatus.Sent,
                MessageResource.StatusEnum.Delivered => DeliveryStatus.Delivered,
                MessageResource.StatusEnum.Failed => DeliveryStatus.Failed,
                MessageResource.StatusEnum.Undelivered => DeliveryStatus.Failed,
                _ => DeliveryStatus.Pending
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get SMS delivery status for {MessageId}", externalId);
            return DeliveryStatus.Failed;
        }
    }

    public bool ValidateRecipient(ContactInfo recipient)
    {
        if (string.IsNullOrWhiteSpace(recipient.PhoneNumber))
            return false;

        // Basic phone number validation
        var phoneNumber = recipient.PhoneNumber.Trim();
        return phoneNumber.StartsWith("+") && phoneNumber.Length >= 10;
    }

    public async Task<SmsResult> SendSmsAsync(
        string phoneNumber,
        string message,
        bool enableDeliveryReceipt = true,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var messageOptions = new CreateMessageOptions(new PhoneNumber(phoneNumber))
            {
                From = new PhoneNumber(_configuration.FromPhoneNumber),
                Body = message
            };

            if (enableDeliveryReceipt && !string.IsNullOrEmpty(_configuration.StatusCallbackUrl))
            {
                messageOptions.StatusCallback = new Uri(_configuration.StatusCallbackUrl);
            }

            var twilioMessage = await MessageResource.CreateAsync(messageOptions, cancellationToken: cancellationToken);

            var cost = twilioMessage.Price != null ? decimal.Parse(twilioMessage.Price) : (decimal?)null;
            var messageParts = twilioMessage.NumSegments ?? 1;

            _logger.LogInformation("SMS sent successfully to {PhoneNumber}, MessageId: {MessageId}, Cost: {Cost}",
                phoneNumber, twilioMessage.Sid, cost);

            return SmsResult.Success(twilioMessage.Sid, cost, messageParts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send SMS to {PhoneNumber}", phoneNumber);
            return SmsResult.Failure($"Failed to send SMS: {ex.Message}");
        }
    }

    public async Task<BulkSmsResult> SendBulkSmsAsync(
        List<string> recipients,
        string message,
        CancellationToken cancellationToken = default)
    {
        var result = new BulkSmsResult
        {
            TotalMessages = recipients.Count
        };

        var tasks = recipients.Select(async phoneNumber =>
        {
            var smsResult = await SendSmsAsync(phoneNumber, message, true, cancellationToken);
            
            if (smsResult.IsSuccess)
            {
                Interlocked.Increment(ref result.SuccessfulMessages);
                if (smsResult.Cost.HasValue)
                {
                    lock (result)
                    {
                        result.TotalCost = (result.TotalCost ?? 0) + smsResult.Cost.Value;
                    }
                }
            }
            else
            {
                Interlocked.Increment(ref result.FailedMessages);
            }

            return smsResult;
        });

        result.Results = (await Task.WhenAll(tasks)).ToList();

        _logger.LogInformation("Bulk SMS completed. Total: {Total}, Success: {Success}, Failed: {Failed}, Cost: {Cost}",
            result.TotalMessages, result.SuccessfulMessages, result.FailedMessages, result.TotalCost);

        return result;
    }

    public async Task<SmsDeliveryReport> GetDeliveryReportAsync(
        string messageId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var message = await MessageResource.FetchAsync(messageId, cancellationToken: cancellationToken);

            return new SmsDeliveryReport
            {
                MessageId = messageId,
                PhoneNumber = message.To,
                Status = MapTwilioStatus(message.Status),
                DeliveredAt = message.DateSent,
                ErrorCode = message.ErrorCode?.ToString(),
                ErrorMessage = message.ErrorMessage
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get SMS delivery report for {MessageId}", messageId);
            
            return new SmsDeliveryReport
            {
                MessageId = messageId,
                Status = SmsDeliveryStatus.Failed,
                ErrorMessage = $"Failed to get delivery report: {ex.Message}"
            };
        }
    }

    private void InitializeTwilio()
    {
        TwilioClient.Init(_configuration.AccountSid, _configuration.AuthToken);
        _logger.LogInformation("Twilio SMS provider initialized with Account SID: {AccountSid}", 
            _configuration.AccountSid);
    }

    private static SmsDeliveryStatus MapTwilioStatus(MessageResource.StatusEnum? status)
    {
        return status switch
        {
            MessageResource.StatusEnum.Queued => SmsDeliveryStatus.Pending,
            MessageResource.StatusEnum.Sending => SmsDeliveryStatus.Pending,
            MessageResource.StatusEnum.Sent => SmsDeliveryStatus.Sent,
            MessageResource.StatusEnum.Delivered => SmsDeliveryStatus.Delivered,
            MessageResource.StatusEnum.Failed => SmsDeliveryStatus.Failed,
            MessageResource.StatusEnum.Undelivered => SmsDeliveryStatus.Failed,
            MessageResource.StatusEnum.Received => SmsDeliveryStatus.Delivered,
            _ => SmsDeliveryStatus.Unknown
        };
    }
}
