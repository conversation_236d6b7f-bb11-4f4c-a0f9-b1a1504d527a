using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AnalyticsBIService.Domain.Enums;

namespace AnalyticsBIService.Domain.Services
{
    /// <summary>
    /// Interface for report generation service operations
    /// </summary>
    public interface IReportGenerationService
    {
        /// <summary>
        /// Generate report from template
        /// </summary>
        /// <param name="templateId">Report template ID</param>
        /// <param name="parameters">Report parameters</param>
        /// <param name="format">Output format</param>
        /// <returns>Generated report data</returns>
        Task<GeneratedReportData> GenerateReportAsync(
            Guid templateId,
            Dictionary<string, object> parameters,
            ExportFormat format);

        /// <summary>
        /// Generate custom report
        /// </summary>
        /// <param name="reportRequest">Custom report request</param>
        /// <returns>Generated report data</returns>
        Task<GeneratedReportData> GenerateCustomReportAsync(
            CustomReportRequest reportRequest);

        /// <summary>
        /// Generate scheduled report
        /// </summary>
        /// <param name="scheduleId">Report schedule ID</param>
        /// <param name="executionDate">Execution date</param>
        /// <returns>Generated report data</returns>
        Task<GeneratedReportData> GenerateScheduledReportAsync(
            Guid scheduleId,
            DateTime executionDate);

        /// <summary>
        /// Generate dashboard report
        /// </summary>
        /// <param name="dashboardId">Dashboard ID</param>
        /// <param name="userId">User ID requesting the report</param>
        /// <param name="format">Output format</param>
        /// <returns>Generated dashboard report</returns>
        Task<GeneratedReportData> GenerateDashboardReportAsync(
            Guid dashboardId,
            Guid userId,
            ExportFormat format);

        /// <summary>
        /// Generate performance analytics report
        /// </summary>
        /// <param name="entityType">Type of entity</param>
        /// <param name="entityIds">List of entity IDs</param>
        /// <param name="startDate">Start date for report</param>
        /// <param name="endDate">End date for report</param>
        /// <param name="metrics">Metrics to include</param>
        /// <param name="format">Output format</param>
        /// <returns>Performance analytics report</returns>
        Task<GeneratedReportData> GeneratePerformanceAnalyticsReportAsync(
            EntityType entityType,
            IEnumerable<Guid> entityIds,
            DateTime startDate,
            DateTime endDate,
            IEnumerable<string> metrics,
            ExportFormat format);

        /// <summary>
        /// Generate financial report
        /// </summary>
        /// <param name="reportType">Type of financial report</param>
        /// <param name="entityType">Type of entity</param>
        /// <param name="entityIds">List of entity IDs</param>
        /// <param name="startDate">Start date for report</param>
        /// <param name="endDate">End date for report</param>
        /// <param name="currency">Currency for financial calculations</param>
        /// <param name="format">Output format</param>
        /// <returns>Financial report</returns>
        Task<GeneratedReportData> GenerateFinancialReportAsync(
            FinancialReportType reportType,
            EntityType entityType,
            IEnumerable<Guid> entityIds,
            DateTime startDate,
            DateTime endDate,
            string currency,
            ExportFormat format);

        /// <summary>
        /// Generate comparative analysis report
        /// </summary>
        /// <param name="comparisonRequest">Comparison request parameters</param>
        /// <returns>Comparative analysis report</returns>
        Task<GeneratedReportData> GenerateComparativeAnalysisReportAsync(
            ComparativeAnalysisRequest comparisonRequest);

        /// <summary>
        /// Generate trend analysis report
        /// </summary>
        /// <param name="trendRequest">Trend analysis request parameters</param>
        /// <returns>Trend analysis report</returns>
        Task<GeneratedReportData> GenerateTrendAnalysisReportAsync(
            TrendAnalysisRequest trendRequest);

        /// <summary>
        /// Generate real-time metrics report
        /// </summary>
        /// <param name="metricTypes">Types of metrics to include</param>
        /// <param name="timeWindow">Time window for real-time data</param>
        /// <param name="format">Output format</param>
        /// <returns>Real-time metrics report</returns>
        Task<GeneratedReportData> GenerateRealTimeMetricsReportAsync(
            IEnumerable<MetricType> metricTypes,
            TimeSpan timeWindow,
            ExportFormat format);

        /// <summary>
        /// Generate alert summary report
        /// </summary>
        /// <param name="alertFilters">Alert filtering criteria</param>
        /// <param name="format">Output format</param>
        /// <returns>Alert summary report</returns>
        Task<GeneratedReportData> GenerateAlertSummaryReportAsync(
            AlertReportFilters alertFilters,
            ExportFormat format);

        /// <summary>
        /// Generate usage analytics report
        /// </summary>
        /// <param name="startDate">Start date for usage analysis</param>
        /// <param name="endDate">End date for usage analysis</param>
        /// <param name="userTypes">Types of users to include</param>
        /// <param name="format">Output format</param>
        /// <returns>Usage analytics report</returns>
        Task<GeneratedReportData> GenerateUsageAnalyticsReportAsync(
            DateTime startDate,
            DateTime endDate,
            IEnumerable<UserRole> userTypes,
            ExportFormat format);

        /// <summary>
        /// Validate report template
        /// </summary>
        /// <param name="templateId">Template ID to validate</param>
        /// <returns>Validation result</returns>
        Task<ReportTemplateValidationResult> ValidateReportTemplateAsync(Guid templateId);

        /// <summary>
        /// Get report generation status
        /// </summary>
        /// <param name="executionId">Report execution ID</param>
        /// <returns>Report generation status</returns>
        Task<ReportGenerationStatus> GetReportGenerationStatusAsync(Guid executionId);

        /// <summary>
        /// Cancel report generation
        /// </summary>
        /// <param name="executionId">Report execution ID</param>
        /// <returns>Cancellation result</returns>
        Task<bool> CancelReportGenerationAsync(Guid executionId);

        /// <summary>
        /// Get available report formats for template
        /// </summary>
        /// <param name="templateId">Template ID</param>
        /// <returns>List of supported formats</returns>
        Task<IEnumerable<ExportFormat>> GetSupportedFormatsAsync(Guid templateId);

        /// <summary>
        /// Estimate report generation time
        /// </summary>
        /// <param name="templateId">Template ID</param>
        /// <param name="parameters">Report parameters</param>
        /// <returns>Estimated generation time</returns>
        Task<TimeSpan> EstimateGenerationTimeAsync(Guid templateId, Dictionary<string, object> parameters);
    }

    #region Supporting Data Models

    public class GeneratedReportData
    {
        public Guid ExecutionId { get; set; }
        public Guid TemplateId { get; set; }
        public string ReportName { get; set; } = string.Empty;
        public ExportFormat Format { get; set; }
        public byte[] Content { get; set; } = Array.Empty<byte>();
        public string ContentType { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public long FileSizeBytes { get; set; }
        public DateTime GeneratedAt { get; set; }
        public TimeSpan GenerationTime { get; set; }
        public Dictionary<string, object> Parameters { get; set; } = new();
        public ReportMetadata Metadata { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public ReportQualityMetrics QualityMetrics { get; set; } = new();
    }

    public class ReportMetadata
    {
        public int TotalRecords { get; set; }
        public int TotalPages { get; set; }
        public DateTime DataAsOfDate { get; set; }
        public List<string> DataSources { get; set; } = new();
        public Dictionary<string, object> Summary { get; set; } = new();
        public List<string> AppliedFilters { get; set; } = new();
        public string GeneratedBy { get; set; } = string.Empty;
        public string Version { get; set; } = "1.0";
    }

    public class ReportQualityMetrics
    {
        public decimal DataCompletenessPercentage { get; set; }
        public decimal DataAccuracyScore { get; set; }
        public int MissingDataPoints { get; set; }
        public int OutlierCount { get; set; }
        public List<string> QualityIssues { get; set; } = new();
        public string OverallQualityRating { get; set; } = string.Empty; // "Excellent", "Good", "Fair", "Poor"
    }

    public class CustomReportRequest
    {
        public string ReportName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public List<string> DataSources { get; set; } = new();
        public List<ReportColumn> Columns { get; set; } = new();
        public List<ReportFilter> Filters { get; set; } = new();
        public List<ReportGrouping> Groupings { get; set; } = new();
        public List<ReportSorting> Sorting { get; set; } = new();
        public ReportAggregation? Aggregation { get; set; }
        public ExportFormat Format { get; set; }
        public Dictionary<string, object> Parameters { get; set; } = new();
        public Guid RequestedBy { get; set; }
        public DateTime RequestedAt { get; set; } = DateTime.UtcNow;
    }

    public class ReportColumn
    {
        public string Name { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string DataType { get; set; } = string.Empty;
        public string Source { get; set; } = string.Empty;
        public string? Formula { get; set; }
        public string? Format { get; set; }
        public bool IsVisible { get; set; } = true;
        public int Order { get; set; }
        public int? Width { get; set; }
        public string? Alignment { get; set; }
    }

    public class ReportFilter
    {
        public string ColumnName { get; set; } = string.Empty;
        public string Operator { get; set; } = string.Empty; // "equals", "contains", "greater_than", etc.
        public object Value { get; set; } = new();
        public string LogicalOperator { get; set; } = "AND"; // "AND", "OR"
    }

    public class ReportGrouping
    {
        public string ColumnName { get; set; } = string.Empty;
        public string GroupType { get; set; } = string.Empty; // "standard", "date_range", "numeric_range"
        public Dictionary<string, object>? GroupOptions { get; set; }
    }

    public class ReportSorting
    {
        public string ColumnName { get; set; } = string.Empty;
        public string Direction { get; set; } = "ASC"; // "ASC", "DESC"
        public int Priority { get; set; }
    }

    public class ReportAggregation
    {
        public List<AggregationFunction> Functions { get; set; } = new();
        public List<string> GroupByColumns { get; set; } = new();
        public List<ReportFilter>? HavingFilters { get; set; }
    }

    public class AggregationFunction
    {
        public string Function { get; set; } = string.Empty; // "SUM", "AVG", "COUNT", "MIN", "MAX"
        public string ColumnName { get; set; } = string.Empty;
        public string? Alias { get; set; }
    }

    public class ComparativeAnalysisRequest
    {
        public EntityType EntityType { get; set; }
        public List<Guid> EntityIds { get; set; } = new();
        public List<string> Metrics { get; set; } = new();
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public ComparisonType ComparisonType { get; set; }
        public ExportFormat Format { get; set; }
        public bool IncludeRankings { get; set; } = true;
        public bool IncludeTrends { get; set; } = true;
        public bool IncludeBenchmarks { get; set; } = false;
        public Guid RequestedBy { get; set; }
    }

    public class TrendAnalysisRequest
    {
        public string MetricName { get; set; } = string.Empty;
        public EntityType EntityType { get; set; }
        public List<Guid> EntityIds { get; set; } = new();
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public TrendType TrendType { get; set; }
        public TimeGrouping TimeGrouping { get; set; }
        public ExportFormat Format { get; set; }
        public bool IncludeForecasting { get; set; } = false;
        public int? ForecastPeriods { get; set; }
        public Guid RequestedBy { get; set; }
    }

    public class AlertReportFilters
    {
        public List<string>? AlertTypes { get; set; }
        public List<string>? Severities { get; set; }
        public List<string>? Statuses { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public List<Guid>? EntityIds { get; set; }
        public EntityType? EntityType { get; set; }
        public bool IncludeResolved { get; set; } = true;
        public bool IncludeAcknowledged { get; set; } = true;
    }

    public class ReportTemplateValidationResult
    {
        public bool IsValid { get; set; }
        public List<ValidationError> Errors { get; set; } = new();
        public List<ValidationWarning> Warnings { get; set; } = new();
        public List<string> SupportedFormats { get; set; } = new();
        public Dictionary<string, object> RequiredParameters { get; set; } = new();
        public TimeSpan EstimatedGenerationTime { get; set; }
    }

    public class ValidationError
    {
        public string Code { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string Field { get; set; } = string.Empty;
        public string Severity { get; set; } = "Error";
    }

    public class ValidationWarning
    {
        public string Code { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string Field { get; set; } = string.Empty;
        public string Recommendation { get; set; } = string.Empty;
    }

    public class ReportGenerationStatus
    {
        public Guid ExecutionId { get; set; }
        public string Status { get; set; } = string.Empty; // "Queued", "Processing", "Completed", "Failed", "Cancelled"
        public decimal ProgressPercentage { get; set; }
        public string? CurrentStep { get; set; }
        public DateTime StartedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        public TimeSpan? EstimatedTimeRemaining { get; set; }
        public string? ErrorMessage { get; set; }
        public List<string> ProcessingLogs { get; set; } = new();
        public Dictionary<string, object> StatusDetails { get; set; } = new();
    }

    #endregion
}
