using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OrderManagement.Domain.Entities;
using System.Text.Json;

namespace OrderManagement.Infrastructure.Persistence.Configurations;

public class BrokerCommentConfiguration : IEntityTypeConfiguration<BrokerComment>
{
    public void Configure(EntityTypeBuilder<BrokerComment> builder)
    {
        builder.ToTable("BrokerComments");

        builder.<PERSON>ey(bc => bc.Id);

        builder.Property(bc => bc.Id)
            .ValueGeneratedOnAdd();

        builder.Property(bc => bc.RfqId)
            .IsRequired();

        builder.Property(bc => bc.BrokerId)
            .IsRequired();

        builder.Property(bc => bc.BrokerName)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(bc => bc.Comment)
            .IsRequired()
            .HasMaxLength(4000);

        builder.Property(bc => bc.CommentType)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(bc => bc.CreatedAt)
            .IsRequired();

        builder.Property(bc => bc.IsInternal)
            .IsRequired();

        builder.Property(bc => bc.IsVisible)
            .IsRequired();

        builder.Property(bc => bc.ParentCommentId)
            .IsRequired(false);

        builder.Property(bc => bc.Tags)
            .HasMaxLength(500);

        builder.Property(bc => bc.Priority)
            .IsRequired();

        builder.Property(bc => bc.ExpiresAt)
            .IsRequired(false);

        // Configure Metadata as JSON
        builder.Property(bc => bc.Metadata)
            .HasConversion(
                v => v == null ? null : JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => v == null ? null : JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null))
            .HasColumnType("jsonb"); // PostgreSQL specific

        // Relationships
        builder.HasOne(bc => bc.RFQ)
            .WithMany(r => r.BrokerComments)
            .HasForeignKey(bc => bc.RfqId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(bc => bc.ParentComment)
            .WithMany(bc => bc.Replies)
            .HasForeignKey(bc => bc.ParentCommentId)
            .OnDelete(DeleteBehavior.Restrict);

        // Indexes
        builder.HasIndex(bc => bc.RfqId)
            .HasDatabaseName("IX_BrokerComments_RfqId");

        builder.HasIndex(bc => bc.BrokerId)
            .HasDatabaseName("IX_BrokerComments_BrokerId");

        builder.HasIndex(bc => bc.CommentType)
            .HasDatabaseName("IX_BrokerComments_CommentType");

        builder.HasIndex(bc => bc.CreatedAt)
            .HasDatabaseName("IX_BrokerComments_CreatedAt");

        builder.HasIndex(bc => bc.IsVisible)
            .HasDatabaseName("IX_BrokerComments_IsVisible");

        builder.HasIndex(bc => bc.IsInternal)
            .HasDatabaseName("IX_BrokerComments_IsInternal");

        builder.HasIndex(bc => bc.ParentCommentId)
            .HasDatabaseName("IX_BrokerComments_ParentCommentId");

        builder.HasIndex(bc => bc.Priority)
            .HasDatabaseName("IX_BrokerComments_Priority");

        builder.HasIndex(bc => new { bc.RfqId, bc.CommentType, bc.IsVisible })
            .HasDatabaseName("IX_BrokerComments_RfqId_Type_Visible");
    }
}
