using MonitoringObservability.Domain.Enums;
using MonitoringObservability.Domain.Events;
using Shared.Domain.Common;

namespace MonitoringObservability.Domain.Entities;

/// <summary>
/// Represents a custom dashboard with user-defined widgets and layout
/// </summary>
public class CustomDashboard : AggregateRoot
{
    private readonly List<DashboardWidget> _widgets = new();
    private readonly List<DashboardFilter> _filters = new();
    private readonly List<DashboardPermission> _permissions = new();

    public CustomDashboard(string name, string description, Guid ownerId, 
        DashboardVisibility visibility = DashboardVisibility.Private)
    {
        Id = Guid.NewGuid();
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        OwnerId = ownerId;
        Visibility = visibility;
        CreatedAt = DateTime.UtcNow;
        LastModified = DateTime.UtcNow;
        IsActive = true;
        RefreshInterval = TimeSpan.FromMinutes(5);
        Layout = new DashboardLayout();
        Tags = new Dictionary<string, string>();
        Settings = new Dictionary<string, object>();
    }

    // Required for EF Core
    private CustomDashboard() { }

    public string Name { get; private set; } = string.Empty;
    public string Description { get; private set; } = string.Empty;
    public Guid OwnerId { get; private set; }
    public DashboardVisibility Visibility { get; private set; }
    public bool IsActive { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime LastModified { get; private set; }
    public DateTime? LastViewed { get; private set; }
    public TimeSpan RefreshInterval { get; private set; }
    public DashboardLayout Layout { get; private set; } = new();
    public Dictionary<string, string> Tags { get; private set; } = new();
    public Dictionary<string, object> Settings { get; private set; } = new();

    // Navigation properties
    public IReadOnlyList<DashboardWidget> Widgets => _widgets.AsReadOnly();
    public IReadOnlyList<DashboardFilter> Filters => _filters.AsReadOnly();
    public IReadOnlyList<DashboardPermission> Permissions => _permissions.AsReadOnly();

    // Computed properties
    public int WidgetCount => _widgets.Count;
    public int ViewCount { get; private set; }
    public bool IsShared => Visibility != DashboardVisibility.Private;
    public bool HasFilters => _filters.Any();

    public void UpdateBasicInfo(string? name = null, string? description = null, 
        DashboardVisibility? visibility = null, TimeSpan? refreshInterval = null)
    {
        if (!string.IsNullOrWhiteSpace(name))
            Name = name;
        
        if (!string.IsNullOrWhiteSpace(description))
            Description = description;
        
        if (visibility.HasValue)
            Visibility = visibility.Value;
        
        if (refreshInterval.HasValue)
            RefreshInterval = refreshInterval.Value;

        LastModified = DateTime.UtcNow;
        
        AddDomainEvent(new DashboardUpdatedEvent(Id, Name));
    }

    public void UpdateLayout(DashboardLayout layout)
    {
        Layout = layout ?? throw new ArgumentNullException(nameof(layout));
        LastModified = DateTime.UtcNow;
        
        AddDomainEvent(new DashboardLayoutUpdatedEvent(Id, Name));
    }

    public void AddWidget(DashboardWidget widget)
    {
        if (widget == null) throw new ArgumentNullException(nameof(widget));
        
        _widgets.Add(widget);
        LastModified = DateTime.UtcNow;
        
        AddDomainEvent(new DashboardWidgetAddedEvent(Id, widget.Id, widget.Type.ToString()));
    }

    public void RemoveWidget(Guid widgetId)
    {
        var widget = _widgets.FirstOrDefault(w => w.Id == widgetId);
        if (widget != null)
        {
            _widgets.Remove(widget);
            LastModified = DateTime.UtcNow;
            
            AddDomainEvent(new DashboardWidgetRemovedEvent(Id, widgetId));
        }
    }

    public void UpdateWidget(Guid widgetId, DashboardWidget updatedWidget)
    {
        var index = _widgets.FindIndex(w => w.Id == widgetId);
        if (index >= 0)
        {
            _widgets[index] = updatedWidget;
            LastModified = DateTime.UtcNow;
            
            AddDomainEvent(new DashboardWidgetUpdatedEvent(Id, widgetId));
        }
    }

    public void AddFilter(DashboardFilter filter)
    {
        if (filter == null) throw new ArgumentNullException(nameof(filter));
        
        _filters.Add(filter);
        LastModified = DateTime.UtcNow;
        
        AddDomainEvent(new DashboardFilterAddedEvent(Id, filter.Id, filter.Name));
    }

    public void RemoveFilter(Guid filterId)
    {
        var filter = _filters.FirstOrDefault(f => f.Id == filterId);
        if (filter != null)
        {
            _filters.Remove(filter);
            LastModified = DateTime.UtcNow;
            
            AddDomainEvent(new DashboardFilterRemovedEvent(Id, filterId));
        }
    }

    public void AddPermission(DashboardPermission permission)
    {
        if (permission == null) throw new ArgumentNullException(nameof(permission));
        
        // Remove existing permission for the same user/role
        var existing = _permissions.FirstOrDefault(p => 
            p.UserId == permission.UserId && p.RoleName == permission.RoleName);
        if (existing != null)
        {
            _permissions.Remove(existing);
        }
        
        _permissions.Add(permission);
        LastModified = DateTime.UtcNow;
        
        AddDomainEvent(new DashboardPermissionGrantedEvent(Id, permission.UserId, permission.RoleName, permission.AccessLevel.ToString()));
    }

    public void RemovePermission(Guid? userId, string? roleName)
    {
        var permission = _permissions.FirstOrDefault(p => 
            p.UserId == userId && p.RoleName == roleName);
        if (permission != null)
        {
            _permissions.Remove(permission);
            LastModified = DateTime.UtcNow;
            
            AddDomainEvent(new DashboardPermissionRevokedEvent(Id, userId, roleName));
        }
    }

    public void RecordView(Guid? userId = null)
    {
        ViewCount++;
        LastViewed = DateTime.UtcNow;
        
        if (userId.HasValue)
        {
            AddDomainEvent(new DashboardViewedEvent(Id, userId.Value));
        }
    }

    public void Activate()
    {
        if (!IsActive)
        {
            IsActive = true;
            LastModified = DateTime.UtcNow;
            
            AddDomainEvent(new DashboardActivatedEvent(Id, Name));
        }
    }

    public void Deactivate()
    {
        if (IsActive)
        {
            IsActive = false;
            LastModified = DateTime.UtcNow;
            
            AddDomainEvent(new DashboardDeactivatedEvent(Id, Name));
        }
    }

    public void AddTag(string key, string value)
    {
        if (string.IsNullOrWhiteSpace(key)) throw new ArgumentException("Tag key cannot be null or empty", nameof(key));
        Tags[key] = value ?? string.Empty;
        LastModified = DateTime.UtcNow;
    }

    public void RemoveTag(string key)
    {
        if (Tags.Remove(key))
        {
            LastModified = DateTime.UtcNow;
        }
    }

    public void UpdateSetting(string key, object value)
    {
        if (string.IsNullOrWhiteSpace(key)) throw new ArgumentException("Setting key cannot be null or empty", nameof(key));
        Settings[key] = value;
        LastModified = DateTime.UtcNow;
    }

    public bool CanUserAccess(Guid userId, string[] userRoles)
    {
        // Owner always has access
        if (OwnerId == userId)
            return true;

        // Public dashboards can be viewed by anyone
        if (Visibility == DashboardVisibility.Public)
            return true;

        // Check explicit permissions
        var userPermission = _permissions.FirstOrDefault(p => p.UserId == userId);
        if (userPermission != null)
            return userPermission.AccessLevel >= DashboardAccessLevel.View;

        // Check role-based permissions
        var rolePermissions = _permissions.Where(p => 
            !string.IsNullOrEmpty(p.RoleName) && userRoles.Contains(p.RoleName));
        
        return rolePermissions.Any(p => p.AccessLevel >= DashboardAccessLevel.View);
    }

    public bool CanUserEdit(Guid userId, string[] userRoles)
    {
        // Owner always can edit
        if (OwnerId == userId)
            return true;

        // Check explicit permissions
        var userPermission = _permissions.FirstOrDefault(p => p.UserId == userId);
        if (userPermission != null)
            return userPermission.AccessLevel >= DashboardAccessLevel.Edit;

        // Check role-based permissions
        var rolePermissions = _permissions.Where(p => 
            !string.IsNullOrEmpty(p.RoleName) && userRoles.Contains(p.RoleName));
        
        return rolePermissions.Any(p => p.AccessLevel >= DashboardAccessLevel.Edit);
    }

    public DashboardWidget? GetWidget(Guid widgetId)
    {
        return _widgets.FirstOrDefault(w => w.Id == widgetId);
    }

    public IEnumerable<DashboardWidget> GetWidgetsByType(WidgetType type)
    {
        return _widgets.Where(w => w.Type == type);
    }
}

/// <summary>
/// Represents a widget within a dashboard
/// </summary>
public class DashboardWidget
{
    public DashboardWidget(string title, WidgetType type, WidgetPosition position, 
        WidgetSize size, Dictionary<string, object> configuration)
    {
        Id = Guid.NewGuid();
        Title = title ?? throw new ArgumentNullException(nameof(title));
        Type = type;
        Position = position ?? throw new ArgumentNullException(nameof(position));
        Size = size ?? throw new ArgumentNullException(nameof(size));
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        CreatedAt = DateTime.UtcNow;
        LastModified = DateTime.UtcNow;
        IsVisible = true;
        RefreshInterval = TimeSpan.FromMinutes(1);
    }

    // Required for EF Core
    private DashboardWidget() { }

    public Guid Id { get; private set; }
    public string Title { get; private set; } = string.Empty;
    public WidgetType Type { get; private set; }
    public WidgetPosition Position { get; private set; } = new();
    public WidgetSize Size { get; private set; } = new();
    public bool IsVisible { get; private set; }
    public TimeSpan RefreshInterval { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime LastModified { get; private set; }
    public Dictionary<string, object> Configuration { get; private set; } = new();

    // Navigation properties
    public CustomDashboard Dashboard { get; private set; } = null!;

    public void UpdateConfiguration(Dictionary<string, object> configuration)
    {
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        LastModified = DateTime.UtcNow;
    }

    public void UpdatePosition(WidgetPosition position)
    {
        Position = position ?? throw new ArgumentNullException(nameof(position));
        LastModified = DateTime.UtcNow;
    }

    public void UpdateSize(WidgetSize size)
    {
        Size = size ?? throw new ArgumentNullException(nameof(size));
        LastModified = DateTime.UtcNow;
    }

    public void SetVisibility(bool isVisible)
    {
        IsVisible = isVisible;
        LastModified = DateTime.UtcNow;
    }

    public void SetRefreshInterval(TimeSpan interval)
    {
        RefreshInterval = interval;
        LastModified = DateTime.UtcNow;
    }
}

/// <summary>
/// Represents a filter that can be applied to dashboard data
/// </summary>
public class DashboardFilter
{
    public DashboardFilter(string name, FilterType type, string field, 
        FilterOperator filterOperator, object value)
    {
        Id = Guid.NewGuid();
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Type = type;
        Field = field ?? throw new ArgumentNullException(nameof(field));
        Operator = filterOperator;
        Value = value ?? throw new ArgumentNullException(nameof(value));
        IsEnabled = true;
        CreatedAt = DateTime.UtcNow;
    }

    // Required for EF Core
    private DashboardFilter() { }

    public Guid Id { get; private set; }
    public string Name { get; private set; } = string.Empty;
    public FilterType Type { get; private set; }
    public string Field { get; private set; } = string.Empty;
    public FilterOperator Operator { get; private set; }
    public object Value { get; private set; } = new();
    public bool IsEnabled { get; private set; }
    public DateTime CreatedAt { get; private set; }

    // Navigation properties
    public CustomDashboard Dashboard { get; private set; } = null!;

    public void UpdateFilter(FilterOperator filterOperator, object value)
    {
        Operator = filterOperator;
        Value = value ?? throw new ArgumentNullException(nameof(value));
    }

    public void Enable() => IsEnabled = true;
    public void Disable() => IsEnabled = false;
}

/// <summary>
/// Represents permissions for dashboard access
/// </summary>
public class DashboardPermission
{
    public DashboardPermission(Guid? userId, string? roleName, DashboardAccessLevel accessLevel)
    {
        if (!userId.HasValue && string.IsNullOrEmpty(roleName))
            throw new ArgumentException("Either userId or roleName must be provided");

        Id = Guid.NewGuid();
        UserId = userId;
        RoleName = roleName;
        AccessLevel = accessLevel;
        GrantedAt = DateTime.UtcNow;
    }

    // Required for EF Core
    private DashboardPermission() { }

    public Guid Id { get; private set; }
    public Guid? UserId { get; private set; }
    public string? RoleName { get; private set; }
    public DashboardAccessLevel AccessLevel { get; private set; }
    public DateTime GrantedAt { get; private set; }

    // Navigation properties
    public CustomDashboard Dashboard { get; private set; } = null!;
}

/// <summary>
/// Represents the layout configuration of a dashboard
/// </summary>
public class DashboardLayout
{
    public DashboardLayout()
    {
        Columns = 12; // Default grid system
        RowHeight = 50; // Default row height in pixels
        Margin = new LayoutMargin { X = 10, Y = 10 };
        Padding = new LayoutPadding { Top = 10, Right = 10, Bottom = 10, Left = 10 };
        IsResizable = true;
        IsDraggable = true;
    }

    public int Columns { get; set; }
    public int RowHeight { get; set; }
    public LayoutMargin Margin { get; set; } = new();
    public LayoutPadding Padding { get; set; } = new();
    public bool IsResizable { get; set; }
    public bool IsDraggable { get; set; }
}

/// <summary>
/// Represents the position of a widget in the dashboard grid
/// </summary>
public class WidgetPosition
{
    public WidgetPosition(int x = 0, int y = 0)
    {
        X = x;
        Y = y;
    }

    public int X { get; set; }
    public int Y { get; set; }
}

/// <summary>
/// Represents the size of a widget in the dashboard grid
/// </summary>
public class WidgetSize
{
    public WidgetSize(int width = 4, int height = 4)
    {
        Width = width;
        Height = height;
    }

    public int Width { get; set; }
    public int Height { get; set; }
}

/// <summary>
/// Represents layout margins
/// </summary>
public class LayoutMargin
{
    public int X { get; set; }
    public int Y { get; set; }
}

/// <summary>
/// Represents layout padding
/// </summary>
public class LayoutPadding
{
    public int Top { get; set; }
    public int Right { get; set; }
    public int Bottom { get; set; }
    public int Left { get; set; }
}
