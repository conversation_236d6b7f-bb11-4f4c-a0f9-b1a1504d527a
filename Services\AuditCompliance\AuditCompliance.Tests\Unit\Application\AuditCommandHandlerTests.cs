using Xunit;
using FluentAssertions;
using Moq;
using Microsoft.Extensions.Logging;
using AuditCompliance.Application.Commands;
using AuditCompliance.Application.Handlers;
using AuditCompliance.Application.Interfaces;
using AuditCompliance.Domain.Entities;
using AuditCompliance.Domain.Enums;

namespace AuditCompliance.Tests.Unit.Application;

public class AuditCommandHandlerTests
{
    private readonly Mock<IAuditLogRepository> _mockRepository;
    private readonly Mock<ILogger<CreateAuditLogCommandHandler>> _mockLogger;
    private readonly CreateAuditLogCommandHandler _handler;

    public AuditCommandHandlerTests()
    {
        _mockRepository = new Mock<IAuditLogRepository>();
        _mockLogger = new Mock<ILogger<CreateAuditLogCommandHandler>>();
        _handler = new CreateAuditLogCommandHandler(_mockRepository.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task Handle_CreateAuditLogCommand_ShouldCreateAuditLogSuccessfully()
    {
        // Arrange
        var command = new CreateAuditLogCommand
        {
            EventType = AuditEventType.UserLogin,
            Severity = AuditSeverity.Info,
            EntityType = "User",
            Action = "Login",
            Description = "User logged in successfully",
            UserId = Guid.NewGuid(),
            UserName = "testuser",
            UserRole = "Admin",
            IpAddress = "***********",
            UserAgent = "Mozilla/5.0",
            SessionId = "session123"
        };

        var expectedAuditLog = new AuditLog(
            command.EventType,
            command.Severity,
            command.EntityType,
            command.Action,
            command.Description,
            command.UserId,
            command.UserName,
            command.UserRole);

        _mockRepository.Setup(r => r.AddAsync(It.IsAny<AuditLog>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedAuditLog);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().Be(expectedAuditLog.Id);
        _mockRepository.Verify(r => r.AddAsync(It.IsAny<AuditLog>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateAuditLogCommand_WithException_ShouldThrowAndLog()
    {
        // Arrange
        var command = new CreateAuditLogCommand
        {
            EventType = AuditEventType.UserLogin,
            Severity = AuditSeverity.Info,
            EntityType = "User",
            Action = "Login",
            Description = "User logged in successfully"
        };

        var expectedException = new Exception("Database error");
        _mockRepository.Setup(r => r.AddAsync(It.IsAny<AuditLog>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(expectedException);

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => _handler.Handle(command, CancellationToken.None));
        
        _mockRepository.Verify(r => r.AddAsync(It.IsAny<AuditLog>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateAuditLogCommand_ShouldSetCorrectAuditContext()
    {
        // Arrange
        var command = new CreateAuditLogCommand
        {
            EventType = AuditEventType.UserLogin,
            Severity = AuditSeverity.Info,
            EntityType = "User",
            Action = "Login",
            Description = "User logged in successfully",
            IpAddress = "***********",
            UserAgent = "Mozilla/5.0",
            SessionId = "session123",
            CorrelationId = "correlation123"
        };

        AuditLog? capturedAuditLog = null;
        _mockRepository.Setup(r => r.AddAsync(It.IsAny<AuditLog>(), It.IsAny<CancellationToken>()))
            .Callback<AuditLog, CancellationToken>((auditLog, ct) => capturedAuditLog = auditLog)
            .ReturnsAsync((AuditLog auditLog, CancellationToken ct) => auditLog);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        capturedAuditLog.Should().NotBeNull();
        capturedAuditLog!.Context.IpAddress.Should().Be(command.IpAddress);
        capturedAuditLog.Context.UserAgent.Should().Be(command.UserAgent);
        capturedAuditLog.Context.SessionId.Should().Be(command.SessionId);
        capturedAuditLog.Context.CorrelationId.Should().Be(command.CorrelationId);
    }

    [Fact]
    public async Task Handle_CreateAuditLogCommand_WithComplianceFlags_ShouldSetFlags()
    {
        // Arrange
        var command = new CreateAuditLogCommand
        {
            EventType = AuditEventType.DataExport,
            Severity = AuditSeverity.Info,
            EntityType = "User",
            Action = "Export",
            Description = "Data exported",
            ComplianceFlags = new List<ComplianceStandard> { ComplianceStandard.GDPR, ComplianceStandard.DataRetention }
        };

        AuditLog? capturedAuditLog = null;
        _mockRepository.Setup(r => r.AddAsync(It.IsAny<AuditLog>(), It.IsAny<CancellationToken>()))
            .Callback<AuditLog, CancellationToken>((auditLog, ct) => capturedAuditLog = auditLog)
            .ReturnsAsync((AuditLog auditLog, CancellationToken ct) => auditLog);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        capturedAuditLog.Should().NotBeNull();
        capturedAuditLog!.ComplianceFlags.Should().Contain(ComplianceStandard.GDPR);
        capturedAuditLog.ComplianceFlags.Should().Contain(ComplianceStandard.DataRetention);
    }
}

public class CleanupExpiredAuditLogsCommandHandlerTests
{
    private readonly Mock<IAuditLogRepository> _mockRepository;
    private readonly Mock<ILogger<CleanupExpiredAuditLogsCommandHandler>> _mockLogger;
    private readonly CleanupExpiredAuditLogsCommandHandler _handler;

    public CleanupExpiredAuditLogsCommandHandlerTests()
    {
        _mockRepository = new Mock<IAuditLogRepository>();
        _mockLogger = new Mock<ILogger<CleanupExpiredAuditLogsCommandHandler>>();
        _handler = new CleanupExpiredAuditLogsCommandHandler(_mockRepository.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task Handle_CleanupExpiredAuditLogsCommand_ShouldDeleteExpiredLogs()
    {
        // Arrange
        var expiredLogs = new List<AuditLog>
        {
            new AuditLog(AuditEventType.UserLogin, AuditSeverity.Info, "User", "Login", "Old log 1"),
            new AuditLog(AuditEventType.UserLogout, AuditSeverity.Info, "User", "Logout", "Old log 2")
        };

        _mockRepository.Setup(r => r.GetExpiredAuditLogsAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(expiredLogs);

        _mockRepository.Setup(r => r.DeleteRangeAsync(It.IsAny<List<AuditLog>>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        var command = new CleanupExpiredAuditLogsCommand();

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().Be(2);
        _mockRepository.Verify(r => r.GetExpiredAuditLogsAsync(It.IsAny<CancellationToken>()), Times.Once);
        _mockRepository.Verify(r => r.DeleteRangeAsync(expiredLogs, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CleanupExpiredAuditLogsCommand_WithNoExpiredLogs_ShouldReturnZero()
    {
        // Arrange
        var emptyList = new List<AuditLog>();

        _mockRepository.Setup(r => r.GetExpiredAuditLogsAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(emptyList);

        var command = new CleanupExpiredAuditLogsCommand();

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().Be(0);
        _mockRepository.Verify(r => r.GetExpiredAuditLogsAsync(It.IsAny<CancellationToken>()), Times.Once);
        _mockRepository.Verify(r => r.DeleteRangeAsync(It.IsAny<List<AuditLog>>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task Handle_CleanupExpiredAuditLogsCommand_WithException_ShouldThrowAndLog()
    {
        // Arrange
        var expectedException = new Exception("Database error");
        _mockRepository.Setup(r => r.GetExpiredAuditLogsAsync(It.IsAny<CancellationToken>()))
            .ThrowsAsync(expectedException);

        var command = new CleanupExpiredAuditLogsCommand();

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => _handler.Handle(command, CancellationToken.None));
        
        _mockRepository.Verify(r => r.GetExpiredAuditLogsAsync(It.IsAny<CancellationToken>()), Times.Once);
    }
}
