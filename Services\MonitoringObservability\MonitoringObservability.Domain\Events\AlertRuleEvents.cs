using Shared.Domain.Common;

namespace MonitoringObservability.Domain.Events;

/// <summary>
/// Event raised when an alert rule is created
/// </summary>
public class AlertRuleCreatedEvent : DomainEvent
{
    public AlertRuleCreatedEvent(Guid ruleId, string ruleName, string ruleType)
    {
        RuleId = ruleId;
        RuleName = ruleName;
        RuleType = ruleType;
    }

    public Guid RuleId { get; }
    public string RuleName { get; }
    public string RuleType { get; }
}

/// <summary>
/// Event raised when an alert rule is updated
/// </summary>
public class AlertRuleUpdatedEvent : DomainEvent
{
    public AlertRuleUpdatedEvent(Guid ruleId, string ruleName)
    {
        RuleId = ruleId;
        RuleName = ruleName;
    }

    public Guid RuleId { get; }
    public string RuleName { get; }
}

/// <summary>
/// Event raised when an alert rule is triggered
/// </summary>
public class AlertRuleTriggeredEvent : DomainEvent
{
    public AlertRuleTriggeredEvent(Guid ruleId, string ruleName, int triggerCount)
    {
        RuleId = ruleId;
        RuleName = ruleName;
        TriggerCount = triggerCount;
    }

    public Guid RuleId { get; }
    public string RuleName { get; }
    public int TriggerCount { get; }
}

/// <summary>
/// Event raised when an alert rule is enabled
/// </summary>
public class AlertRuleEnabledEvent : DomainEvent
{
    public AlertRuleEnabledEvent(Guid ruleId, string ruleName)
    {
        RuleId = ruleId;
        RuleName = ruleName;
    }

    public Guid RuleId { get; }
    public string RuleName { get; }
}

/// <summary>
/// Event raised when an alert rule is disabled
/// </summary>
public class AlertRuleDisabledEvent : DomainEvent
{
    public AlertRuleDisabledEvent(Guid ruleId, string ruleName)
    {
        RuleId = ruleId;
        RuleName = ruleName;
    }

    public Guid RuleId { get; }
    public string RuleName { get; }
}

/// <summary>
/// Event raised when a condition is added to an alert rule
/// </summary>
public class AlertRuleConditionAddedEvent : DomainEvent
{
    public AlertRuleConditionAddedEvent(Guid ruleId, Guid conditionId)
    {
        RuleId = ruleId;
        ConditionId = conditionId;
    }

    public Guid RuleId { get; }
    public Guid ConditionId { get; }
}

/// <summary>
/// Event raised when a condition is removed from an alert rule
/// </summary>
public class AlertRuleConditionRemovedEvent : DomainEvent
{
    public AlertRuleConditionRemovedEvent(Guid ruleId, Guid conditionId)
    {
        RuleId = ruleId;
        ConditionId = conditionId;
    }

    public Guid RuleId { get; }
    public Guid ConditionId { get; }
}

/// <summary>
/// Event raised when an action is added to an alert rule
/// </summary>
public class AlertRuleActionAddedEvent : DomainEvent
{
    public AlertRuleActionAddedEvent(Guid ruleId, Guid actionId)
    {
        RuleId = ruleId;
        ActionId = actionId;
    }

    public Guid RuleId { get; }
    public Guid ActionId { get; }
}

/// <summary>
/// Event raised when an action is removed from an alert rule
/// </summary>
public class AlertRuleActionRemovedEvent : DomainEvent
{
    public AlertRuleActionRemovedEvent(Guid ruleId, Guid actionId)
    {
        RuleId = ruleId;
        ActionId = actionId;
    }

    public Guid RuleId { get; }
    public Guid ActionId { get; }
}

/// <summary>
/// Event raised when an escalation is added to an alert rule
/// </summary>
public class AlertRuleEscalationAddedEvent : DomainEvent
{
    public AlertRuleEscalationAddedEvent(Guid ruleId, Guid escalationId)
    {
        RuleId = ruleId;
        EscalationId = escalationId;
    }

    public Guid RuleId { get; }
    public Guid EscalationId { get; }
}

/// <summary>
/// Event raised when an escalation is removed from an alert rule
/// </summary>
public class AlertRuleEscalationRemovedEvent : DomainEvent
{
    public AlertRuleEscalationRemovedEvent(Guid ruleId, Guid escalationId)
    {
        RuleId = ruleId;
        EscalationId = escalationId;
    }

    public Guid RuleId { get; }
    public Guid EscalationId { get; }
}

/// <summary>
/// Event raised when an alert correlation rule is updated
/// </summary>
public class AlertCorrelationRuleUpdatedEvent : DomainEvent
{
    public AlertCorrelationRuleUpdatedEvent(Guid ruleId, string ruleName)
    {
        RuleId = ruleId;
        RuleName = ruleName;
    }

    public Guid RuleId { get; }
    public string RuleName { get; }
}

/// <summary>
/// Event raised when an alert correlation group is created
/// </summary>
public class AlertCorrelationGroupCreatedEvent : DomainEvent
{
    public AlertCorrelationGroupCreatedEvent(Guid ruleId, Guid groupId, int alertCount)
    {
        RuleId = ruleId;
        GroupId = groupId;
        AlertCount = alertCount;
    }

    public Guid RuleId { get; }
    public Guid GroupId { get; }
    public int AlertCount { get; }
}

/// <summary>
/// Event raised when an alert correlation group is closed
/// </summary>
public class AlertCorrelationGroupClosedEvent : DomainEvent
{
    public AlertCorrelationGroupClosedEvent(Guid ruleId, Guid groupId, string reason)
    {
        RuleId = ruleId;
        GroupId = groupId;
        Reason = reason;
    }

    public Guid RuleId { get; }
    public Guid GroupId { get; }
    public string Reason { get; }
}
