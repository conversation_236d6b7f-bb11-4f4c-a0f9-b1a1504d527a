using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.Interfaces;
using System.Security.Cryptography;
using System.Text;

namespace SubscriptionManagement.Infrastructure.Services
{
    public class PaymentProofFileService : IPaymentProofFileService
    {
        private readonly ILogger<PaymentProofFileService> _logger;
        private readonly IConfiguration _configuration;
        private readonly string _uploadPath;
        private readonly string _baseUrl;
        private readonly long _maxFileSize;
        private readonly HashSet<string> _allowedContentTypes;

        public PaymentProofFileService(
            ILogger<PaymentProofFileService> logger,
            IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
            
            _uploadPath = _configuration.GetValue<string>("PaymentProofStorage:UploadPath") ?? "uploads/payment-proofs";
            _baseUrl = _configuration.GetValue<string>("PaymentProofStorage:BaseUrl") ?? "https://localhost:7001/files/payment-proofs";
            _maxFileSize = _configuration.GetValue<long>("PaymentProofStorage:MaxFileSizeBytes") ?? 10 * 1024 * 1024; // 10MB default
            
            _allowedContentTypes = new HashSet<string>
            {
                "image/jpeg",
                "image/jpg", 
                "image/png",
                "image/gif",
                "image/webp",
                "application/pdf"
            };

            // Ensure upload directory exists
            Directory.CreateDirectory(_uploadPath);
        }

        public async Task<string> UploadPaymentProofAsync(
            IFormFile file, 
            Guid subscriptionId, 
            Guid userId, 
            CancellationToken cancellationToken = default)
        {
            ValidateFile(file);

            var fileName = GenerateUniqueFileName(file.FileName, subscriptionId, userId);
            var filePath = Path.Combine(_uploadPath, fileName);

            try
            {
                using var stream = new FileStream(filePath, FileMode.Create);
                await file.CopyToAsync(stream, cancellationToken);

                var fileUrl = $"{_baseUrl.TrimEnd('/')}/{fileName}";
                
                _logger.LogInformation("Payment proof uploaded successfully: {FileName} for subscription {SubscriptionId}", 
                    fileName, subscriptionId);

                return fileUrl;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to upload payment proof for subscription {SubscriptionId}", subscriptionId);
                
                // Clean up file if it was partially created
                if (File.Exists(filePath))
                {
                    try
                    {
                        File.Delete(filePath);
                    }
                    catch (Exception deleteEx)
                    {
                        _logger.LogWarning(deleteEx, "Failed to clean up partially uploaded file: {FilePath}", filePath);
                    }
                }
                
                throw new InvalidOperationException($"Failed to upload payment proof: {ex.Message}", ex);
            }
        }

        public async Task<Stream> GetPaymentProofAsync(string fileUrl, CancellationToken cancellationToken = default)
        {
            var fileName = ExtractFileNameFromUrl(fileUrl);
            var filePath = Path.Combine(_uploadPath, fileName);

            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException($"Payment proof file not found: {fileName}");
            }

            return new FileStream(filePath, FileMode.Open, FileAccess.Read);
        }

        public async Task<bool> DeletePaymentProofAsync(string fileUrl, CancellationToken cancellationToken = default)
        {
            try
            {
                var fileName = ExtractFileNameFromUrl(fileUrl);
                var filePath = Path.Combine(_uploadPath, fileName);

                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                    _logger.LogInformation("Payment proof file deleted: {FileName}", fileName);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to delete payment proof file: {FileUrl}", fileUrl);
                return false;
            }
        }

        public async Task<bool> FileExistsAsync(string fileUrl, CancellationToken cancellationToken = default)
        {
            try
            {
                var fileName = ExtractFileNameFromUrl(fileUrl);
                var filePath = Path.Combine(_uploadPath, fileName);
                return File.Exists(filePath);
            }
            catch
            {
                return false;
            }
        }

        public async Task<long> GetFileSizeAsync(string fileUrl, CancellationToken cancellationToken = default)
        {
            var fileName = ExtractFileNameFromUrl(fileUrl);
            var filePath = Path.Combine(_uploadPath, fileName);

            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException($"Payment proof file not found: {fileName}");
            }

            var fileInfo = new FileInfo(filePath);
            return fileInfo.Length;
        }

        public bool IsValidFileType(string contentType)
        {
            return _allowedContentTypes.Contains(contentType.ToLowerInvariant());
        }

        public bool IsValidFileSize(long fileSizeBytes)
        {
            return fileSizeBytes > 0 && fileSizeBytes <= _maxFileSize;
        }

        private void ValidateFile(IFormFile file)
        {
            if (file == null || file.Length == 0)
            {
                throw new ArgumentException("File is required and cannot be empty");
            }

            if (!IsValidFileSize(file.Length))
            {
                throw new ArgumentException($"File size must be between 1 byte and {_maxFileSize / (1024 * 1024)} MB");
            }

            if (!IsValidFileType(file.ContentType))
            {
                throw new ArgumentException($"File type '{file.ContentType}' is not allowed. Allowed types: {string.Join(", ", _allowedContentTypes)}");
            }

            var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
            var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".webp", ".pdf" };
            
            if (!allowedExtensions.Contains(extension))
            {
                throw new ArgumentException($"File extension '{extension}' is not allowed");
            }
        }

        private string GenerateUniqueFileName(string originalFileName, Guid subscriptionId, Guid userId)
        {
            var extension = Path.GetExtension(originalFileName);
            var timestamp = DateTime.UtcNow.ToString("yyyyMMdd_HHmmss");
            var uniqueId = Guid.NewGuid().ToString("N")[..8];
            
            // Create a hash of subscription and user IDs for additional uniqueness
            var hashInput = $"{subscriptionId}_{userId}_{timestamp}";
            var hash = ComputeHash(hashInput)[..8];
            
            return $"proof_{timestamp}_{hash}_{uniqueId}{extension}";
        }

        private string ExtractFileNameFromUrl(string fileUrl)
        {
            if (string.IsNullOrWhiteSpace(fileUrl))
            {
                throw new ArgumentException("File URL cannot be empty");
            }

            // Extract filename from URL
            var uri = new Uri(fileUrl);
            return Path.GetFileName(uri.LocalPath);
        }

        private string ComputeHash(string input)
        {
            using var sha256 = SHA256.Create();
            var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(input));
            return Convert.ToHexString(hashBytes).ToLowerInvariant();
        }
    }
}
