# Broker App Features - Testing Strategy

## Overview

This document outlines the comprehensive testing strategy for the new Broker App features, including unit tests, integration tests, end-to-end tests, and performance testing approaches.

## Testing Pyramid Structure

### 1. Unit Tests (70% of total tests)
- **Domain Logic Testing**: Business rules, value objects, and entity behavior
- **Application Service Testing**: Command/query handlers and application services
- **Infrastructure Testing**: Repository implementations and external service integrations

### 2. Integration Tests (20% of total tests)
- **API Endpoint Testing**: Controller behavior and request/response validation
- **Database Integration**: Repository operations and data persistence
- **External Service Integration**: Third-party API interactions

### 3. End-to-End Tests (10% of total tests)
- **User Journey Testing**: Complete broker workflows
- **Cross-Service Integration**: Multi-service feature interactions
- **Performance and Load Testing**: System behavior under load

## Feature-Specific Testing Strategies

### 1. KYC Auto-check Functionality

#### Unit Tests
```csharp
// Domain Layer Tests
[Test]
public void Document_StartAutoVerification_ShouldUpdateStatusAndMethod()
{
    // Arrange
    var document = new Document(DocumentType.PanCard);
    document.Upload("pan.jpg", "/path/to/file", "1MB", "image/jpeg");

    // Act
    document.StartAutoVerification();

    // Assert
    Assert.That(document.Status, Is.EqualTo(DocumentStatus.UnderReview));
    Assert.That(document.VerificationMethod, Is.EqualTo(VerificationMethod.AI_OCR));
    Assert.That(document.IsAutoVerificationEnabled, Is.True);
    Assert.That(document.VerificationAttempts, Is.EqualTo(1));
}

[Test]
public void KycVerificationResult_WithHighConfidence_ShouldApproveDocument()
{
    // Arrange
    var document = new Document(DocumentType.PanCard);
    document.Upload("pan.jpg", "/path/to/file", "1MB", "image/jpeg");
    document.StartAutoVerification();

    var verificationResult = new KycVerificationResult(
        "VER123", KycVerificationStatus.Verified, "PAN API", 0.95m,
        new Dictionary<string, object> { ["panNumber"] = "**********" },
        new List<string>(), DateTime.UtcNow, DateTime.UtcNow.AddYears(1));

    // Act
    document.CompleteAutoVerification(verificationResult);

    // Assert
    Assert.That(document.Status, Is.EqualTo(DocumentStatus.Approved));
    Assert.That(document.KycVerificationResult.ConfidenceScore, Is.EqualTo(0.95m));
}

// Application Layer Tests
[Test]
public async Task StartKycAutoVerificationCommandHandler_ValidRequest_ShouldInitiateVerification()
{
    // Arrange
    var command = new StartKycAutoVerificationCommand
    {
        DocumentId = Guid.NewGuid(),
        UserId = Guid.NewGuid(),
        DocumentType = DocumentType.PanCard
    };

    var mockRepository = new Mock<IDocumentSubmissionRepository>();
    var mockKycService = new Mock<IKycVerificationService>();
    var mockMessageBroker = new Mock<IMessageBroker>();

    var submission = CreateTestDocumentSubmission(command.UserId, command.DocumentId);
    mockRepository.Setup(r => r.GetByUserIdAsync(command.UserId, It.IsAny<CancellationToken>()))
               .ReturnsAsync(submission);

    var handler = new StartKycAutoVerificationCommandHandler(
        mockRepository.Object, mockKycService.Object, mockMessageBroker.Object);

    // Act
    var result = await handler.Handle(command, CancellationToken.None);

    // Assert
    Assert.That(result, Is.True);
    mockMessageBroker.Verify(m => m.PublishAsync("kyc.verification.requested", 
        It.IsAny<object>(), It.IsAny<CancellationToken>()), Times.Once);
}
```

#### Integration Tests
```csharp
[Test]
public async Task KycController_StartAutoVerification_ShouldReturn200WithValidRequest()
{
    // Arrange
    var client = _factory.CreateClient();
    var documentId = await CreateTestDocumentAsync();
    
    var request = new StartKycAutoVerificationRequest
    {
        DocumentType = DocumentType.PanCard,
        ForceVerification = false
    };

    // Act
    var response = await client.PostAsJsonAsync($"/api/kyc/verify/{documentId}", request);

    // Assert
    response.StatusCode.Should().Be(HttpStatusCode.OK);
    var result = await response.Content.ReadFromJsonAsync<bool>();
    result.Should().BeTrue();
}

[Test]
public async Task KycVerificationService_VerifyPanCard_ShouldReturnValidResult()
{
    // Arrange
    var service = new KycVerificationService(_httpClient, _configuration, _logger);
    var testFilePath = CreateTestPanCardImage();

    // Act
    var result = await service.VerifyPanCardAsync(testFilePath);

    // Assert
    result.Should().NotBeNull();
    result.Status.Should().Be(KycVerificationStatus.Verified);
    result.ConfidenceScore.Should().BeGreaterThan(0.8m);
    result.ExtractedData.Should().ContainKey("panNumber");
}
```

### 2. Auto-renew Toggle Implementation

#### Unit Tests
```csharp
[Test]
public void Subscription_UpdateAutoRenewalPreferences_ShouldUpdateSettings()
{
    // Arrange
    var subscription = CreateTestSubscription();
    var preferences = new AutoRenewalPreferences(
        isEnabled: true, daysBeforeExpiry: 5, maxRetryAttempts: 2);

    // Act
    subscription.UpdateAutoRenewalPreferences(preferences);

    // Assert
    Assert.That(subscription.AutoRenew, Is.True);
    Assert.That(subscription.AutoRenewalPreferences.DaysBeforeExpiry, Is.EqualTo(5));
    Assert.That(subscription.NextRenewalAttemptAt, Is.Not.Null);
}

[Test]
public void AutoRenewalPreferences_WithInvalidDays_ShouldClampToValidRange()
{
    // Arrange & Act
    var preferences = new AutoRenewalPreferences(true, daysBeforeExpiry: 50);

    // Assert
    Assert.That(preferences.DaysBeforeExpiry, Is.EqualTo(30)); // Clamped to max
}
```

#### Background Service Tests
```csharp
[Test]
public async Task SubscriptionRenewalService_ProcessPendingRenewals_ShouldRenewEligibleSubscriptions()
{
    // Arrange
    var mockRepository = new Mock<ISubscriptionRepository>();
    var mockPaymentService = new Mock<IPaymentService>();
    var mockMessageBroker = new Mock<IMessageBroker>();

    var pendingSubscriptions = CreateTestPendingRenewals();
    mockRepository.Setup(r => r.GetPendingRenewalsAsync(It.IsAny<DateTime>(), It.IsAny<CancellationToken>()))
               .ReturnsAsync(pendingSubscriptions);

    var service = new SubscriptionRenewalService(
        CreateServiceProvider(mockRepository, mockPaymentService, mockMessageBroker));

    // Act
    await service.ProcessPendingRenewalsAsync(CancellationToken.None);

    // Assert
    mockPaymentService.Verify(p => p.ProcessPaymentAsync(It.IsAny<PaymentRequest>(), 
        It.IsAny<CancellationToken>()), Times.Exactly(pendingSubscriptions.Count));
}
```

### 3. Broker Markup Editing System

#### Unit Tests
```csharp
[Test]
public void RfqBid_ApplyBrokerMarkup_WithValidMarkup_ShouldUpdateAmounts()
{
    // Arrange
    var bid = CreateTestRfqBid(originalAmount: 10000m);
    var markup = new BrokerMarkup(15.5m, 10000m);
    var subscriptionLimit = 20.0m;

    // Act
    bid.ApplyBrokerMarkup(markup, Guid.NewGuid(), subscriptionLimit);

    // Assert
    Assert.That(bid.BrokerMarkup.MarkupPercentage, Is.EqualTo(15.5m));
    Assert.That(bid.FinalQuoteAmount, Is.EqualTo(11550m));
    Assert.That(bid.QuotedAmount.Amount, Is.EqualTo(11550m));
}

[Test]
public void RfqBid_ApplyBrokerMarkup_ExceedsLimit_ShouldThrowException()
{
    // Arrange
    var bid = CreateTestRfqBid(originalAmount: 10000m);
    var markup = new BrokerMarkup(25.0m, 10000m);
    var subscriptionLimit = 20.0m;

    // Act & Assert
    Assert.Throws<OrderManagementDomainException>(() => 
        bid.ApplyBrokerMarkup(markup, Guid.NewGuid(), subscriptionLimit));
}
```

#### Integration Tests
```csharp
[Test]
public async Task UpdateBrokerMarkupCommand_ValidRequest_ShouldUpdateMarkup()
{
    // Arrange
    var command = new UpdateBrokerMarkupCommand
    {
        BidId = await CreateTestBidAsync(),
        BrokerId = _testBrokerId,
        MarkupPercentage = 15.0m,
        Justification = "Premium service"
    };

    // Act
    var result = await _mediator.Send(command);

    // Assert
    result.Success.Should().BeTrue();
    result.FinalAmount.Should().Be(11500m);
    result.IsWithinLimit.Should().BeTrue();
}
```

### 4. Auto-generate Invoice at Order Confirmation

#### Unit Tests
```csharp
[Test]
public async Task OrderConfirmedEventHandler_ValidOrder_ShouldGenerateInvoice()
{
    // Arrange
    var orderConfirmedEvent = new OrderConfirmedEvent { OrderId = Guid.NewGuid() };
    var mockInvoiceService = new Mock<ITaxAwareInvoiceService>();
    var mockOrderService = new Mock<IOrderService>();
    var mockMessageBroker = new Mock<IMessageBroker>();

    var orderDetails = CreateTestOrderDetails();
    mockOrderService.Setup(s => s.GetOrderDetailsAsync(It.IsAny<Guid>(), It.IsAny<CancellationToken>()))
                   .ReturnsAsync(orderDetails);

    var expectedInvoice = CreateTestInvoice();
    mockInvoiceService.Setup(s => s.GenerateInvoiceWithTaxAsync(
        It.IsAny<Guid>(), It.IsAny<Money>(), It.IsAny<CustomerDetails>(),
        It.IsAny<BillingDetails>(), It.IsAny<ServiceCategory>(), It.IsAny<TaxJurisdiction>(),
        It.IsAny<EntityType>(), It.IsAny<List<InvoiceLineItem>>(), It.IsAny<TdsSection?>(),
        It.IsAny<string?>(), It.IsAny<bool>(), It.IsAny<string?>()))
        .ReturnsAsync(expectedInvoice);

    var handler = new OrderConfirmedEventHandler(
        mockInvoiceService.Object, mockOrderService.Object, mockMessageBroker.Object, _logger);

    // Act
    await handler.Handle(orderConfirmedEvent, CancellationToken.None);

    // Assert
    mockInvoiceService.Verify(s => s.GenerateInvoiceWithTaxAsync(
        It.IsAny<Guid>(), It.IsAny<Money>(), It.IsAny<CustomerDetails>(),
        It.IsAny<BillingDetails>(), It.IsAny<ServiceCategory>(), It.IsAny<TaxJurisdiction>(),
        It.IsAny<EntityType>(), It.IsAny<List<InvoiceLineItem>>(), It.IsAny<TdsSection?>(),
        It.IsAny<string?>(), It.IsAny<bool>(), It.IsAny<string?>()), Times.Once);

    mockMessageBroker.Verify(m => m.PublishAsync("invoice.auto_generated", 
        It.IsAny<object>(), It.IsAny<CancellationToken>()), Times.Once);
}
```

## Performance Testing Strategy

### Load Testing Scenarios

#### 1. KYC Verification Load Test
```csharp
[Test]
public async Task KycVerification_ConcurrentRequests_ShouldHandleLoad()
{
    // Simulate 100 concurrent KYC verification requests
    var tasks = Enumerable.Range(0, 100)
        .Select(_ => StartKycVerificationAsync())
        .ToArray();

    var results = await Task.WhenAll(tasks);
    
    // Assert all requests completed successfully
    results.Should().AllSatisfy(r => r.Should().BeTrue());
}
```

#### 2. Auto-Renewal Processing Load Test
```csharp
[Test]
public async Task AutoRenewal_BulkProcessing_ShouldCompleteWithinTimeLimit()
{
    // Create 1000 subscriptions due for renewal
    var subscriptions = await CreateBulkTestSubscriptionsAsync(1000);
    
    var stopwatch = Stopwatch.StartNew();
    
    // Process renewals
    await _renewalService.ProcessPendingRenewalsAsync(CancellationToken.None);
    
    stopwatch.Stop();
    
    // Should complete within 5 minutes
    stopwatch.Elapsed.Should().BeLessThan(TimeSpan.FromMinutes(5));
}
```

### Memory and Resource Testing

```csharp
[Test]
public async Task InvoiceGeneration_BulkProcessing_ShouldNotExceedMemoryLimit()
{
    var initialMemory = GC.GetTotalMemory(true);
    
    // Generate 500 invoices
    var tasks = Enumerable.Range(0, 500)
        .Select(_ => GenerateTestInvoiceAsync())
        .ToArray();
    
    await Task.WhenAll(tasks);
    
    var finalMemory = GC.GetTotalMemory(true);
    var memoryIncrease = finalMemory - initialMemory;
    
    // Memory increase should be reasonable (less than 100MB)
    memoryIncrease.Should().BeLessThan(100 * 1024 * 1024);
}
```

## Test Data Management

### Test Data Builders
```csharp
public class TestDataBuilder
{
    public static DocumentSubmission CreateDocumentSubmission(Guid userId, UserType userType)
    {
        var submission = new DocumentSubmission(userId, userType);
        
        // Add required documents based on user type
        foreach (var docType in GetRequiredDocuments(userType))
        {
            var document = new Document(docType);
            document.Upload($"{docType}.jpg", $"/test/path/{docType}.jpg", "1MB", "image/jpeg");
            submission.AddDocument(document);
        }
        
        return submission;
    }
    
    public static Subscription CreateSubscription(Guid userId, bool autoRenew = true)
    {
        var plan = CreateTestPlan();
        return new Subscription(userId, plan, autoRenew: autoRenew);
    }
}
```

## Continuous Integration Testing

### GitHub Actions Workflow
```yaml
name: Broker Features Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup .NET
      uses: actions/setup-dotnet@v1
      with:
        dotnet-version: 8.0.x
    
    - name: Run Unit Tests
      run: dotnet test --configuration Release --logger trx --collect:"XPlat Code Coverage"
    
    - name: Run Integration Tests
      run: dotnet test --configuration Release --filter Category=Integration
    
    - name: Run Performance Tests
      run: dotnet test --configuration Release --filter Category=Performance
```

## Test Coverage Requirements

- **Unit Tests**: Minimum 90% code coverage
- **Integration Tests**: All API endpoints covered
- **End-to-End Tests**: All critical user journeys covered
- **Performance Tests**: All high-load scenarios tested

## Monitoring and Alerting in Tests

```csharp
[Test]
public async Task KycVerification_ResponseTime_ShouldMeetSLA()
{
    var stopwatch = Stopwatch.StartNew();
    
    await _kycService.VerifyPanCardAsync("test-file.jpg");
    
    stopwatch.Stop();
    
    // KYC verification should complete within 30 seconds
    stopwatch.Elapsed.Should().BeLessThan(TimeSpan.FromSeconds(30));
}
```

This comprehensive testing strategy ensures that all Broker App features are thoroughly tested across multiple dimensions, providing confidence in the system's reliability, performance, and correctness.
