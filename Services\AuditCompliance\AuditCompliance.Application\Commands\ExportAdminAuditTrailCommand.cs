using MediatR;
using AuditCompliance.Application.DTOs;

namespace AuditCompliance.Application.Commands
{
    public class ExportAdminAuditTrailCommand : IRequest<AdminAuditExportResponseDto>
    {
        public List<Guid> UserIds { get; set; } = new();
        public List<string> ActionTypes { get; set; } = new();
        public List<string> EntityTypes { get; set; } = new();
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public List<string> SeverityLevels { get; set; } = new();
        public List<string> IpAddresses { get; set; } = new();
        public bool IncludeSystemActions { get; set; } = true;
        public bool IncludeSensitiveData { get; set; } = false;
        public ExportFormat Format { get; set; } = ExportFormat.CSV;
        public List<string> SelectedColumns { get; set; } = new();
        public int? MaxRecords { get; set; }
        
        // Audit fields
        public Guid RequestedBy { get; set; }
        public string RequestedByRole { get; set; }
        public string IpAddress { get; set; }
        public string UserAgent { get; set; }
    }

    public class GetAuditTrailStatisticsQuery : IRequest<AuditTrailStatisticsDto>
    {
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
    }
}
