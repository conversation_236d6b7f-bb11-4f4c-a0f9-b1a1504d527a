using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Entities;
using OrderManagement.Domain.Enums;
using OrderManagement.Domain.ValueObjects;
using OrderManagement.Infrastructure.BackgroundServices;
using Xunit;

namespace OrderManagement.Tests.Infrastructure.BackgroundServices;

public class RfqExpirationMonitorServiceTests
{
    private readonly Mock<IServiceProvider> _serviceProviderMock;
    private readonly Mock<IServiceScope> _serviceScopeMock;
    private readonly Mock<IServiceScopeFactory> _serviceScopeFactoryMock;
    private readonly Mock<IRfqRepository> _rfqRepositoryMock;
    private readonly Mock<IUnitOfWork> _unitOfWorkMock;
    private readonly Mock<IMessageBroker> _messageBrokerMock;
    private readonly Mock<ILogger<RfqExpirationMonitorService>> _loggerMock;
    private readonly RfqExpirationOptions _options;

    public RfqExpirationMonitorServiceTests()
    {
        _serviceProviderMock = new Mock<IServiceProvider>();
        _serviceScopeMock = new Mock<IServiceScope>();
        _serviceScopeFactoryMock = new Mock<IServiceScopeFactory>();
        _rfqRepositoryMock = new Mock<IRfqRepository>();
        _unitOfWorkMock = new Mock<IUnitOfWork>();
        _messageBrokerMock = new Mock<IMessageBroker>();
        _loggerMock = new Mock<ILogger<RfqExpirationMonitorService>>();

        _options = new RfqExpirationOptions
        {
            CheckIntervalMinutes = 1, // Short interval for testing
            NotificationHoursBeforeExpiration = new[] { 24, 12, 6, 1 },
            DefaultTimeframeDurationHours = 72,
            MaxExtensionsAllowed = 3,
            AllowExtensionsByDefault = true
        };

        SetupServiceProvider();
    }

    private void SetupServiceProvider()
    {
        _serviceScopeMock.Setup(s => s.ServiceProvider.GetRequiredService<IRfqRepository>())
            .Returns(_rfqRepositoryMock.Object);
        _serviceScopeMock.Setup(s => s.ServiceProvider.GetRequiredService<IUnitOfWork>())
            .Returns(_unitOfWorkMock.Object);
        _serviceScopeMock.Setup(s => s.ServiceProvider.GetRequiredService<IMessageBroker>())
            .Returns(_messageBrokerMock.Object);

        _serviceProviderMock.Setup(sp => sp.CreateScope())
            .Returns(_serviceScopeMock.Object);
    }

    private RFQ CreateTestRfq(RfqStatus status = RfqStatus.Published, bool withTimeframe = true)
    {
        var loadDetails = new LoadDetails(
            "Test load",
            LoadType.General,
            new Weight(1000, WeightUnit.Kg),
            1,
            VehicleType.Truck);

        var routeDetails = new RouteDetails(
            new Address("123 Main St", "City", "State", "12345", "Country"),
            new Address("456 Oak Ave", "City2", "State2", "67890", "Country"),
            DateTime.UtcNow.AddDays(1),
            DateTime.UtcNow.AddDays(2));

        var requirements = new RfqRequirements(
            VehicleType.Truck,
            null, false, null, false, new List<string>(),
            false, null, false, false, false, null, new List<string>());

        var timeframe = withTimeframe ? new RfqTimeframe(1, TimeframeUnit.Minutes) : null;

        var rfq = new RFQ(
            Guid.NewGuid(),
            "Test RFQ",
            "Test Description",
            loadDetails,
            routeDetails,
            requirements,
            timeframe: timeframe);

        if (status == RfqStatus.Published)
        {
            rfq.Publish();
        }

        return rfq;
    }

    [Fact]
    public async Task ProcessExpiringRfqs_WithExpiredRfqs_ShouldExpireThemAndSaveChanges()
    {
        // Arrange
        var expiredRfq1 = CreateTestRfq();
        var expiredRfq2 = CreateTestRfq();
        var expiredRfqs = new List<RFQ> { expiredRfq1, expiredRfq2 };

        _rfqRepositoryMock.Setup(r => r.GetExpiredActiveRfqsAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(expiredRfqs);

        _rfqRepositoryMock.Setup(r => r.GetRfqsApproachingExpirationAsync(
            It.IsAny<int[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<RFQ>());

        var service = new RfqExpirationMonitorService(
            _serviceProviderMock.Object,
            _loggerMock.Object,
            Options.Create(_options));

        var cancellationTokenSource = new CancellationTokenSource();

        // Act
        var task = service.StartAsync(cancellationTokenSource.Token);
        await Task.Delay(2000); // Let it run for 2 seconds
        cancellationTokenSource.Cancel();
        await task;

        // Assert
        _rfqRepositoryMock.Verify(r => r.Update(It.IsAny<RFQ>()), Times.AtLeast(2));
        _unitOfWorkMock.Verify(u => u.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.AtLeastOnce);
    }

    [Fact]
    public async Task ProcessExpiringRfqs_WithApproachingExpirationRfqs_ShouldTriggerNotifications()
    {
        // Arrange
        var approachingRfq = CreateTestRfq();
        var approachingRfqs = new List<RFQ> { approachingRfq };

        _rfqRepositoryMock.Setup(r => r.GetExpiredActiveRfqsAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<RFQ>());

        _rfqRepositoryMock.Setup(r => r.GetRfqsApproachingExpirationAsync(
            It.IsAny<int[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(approachingRfqs);

        var service = new RfqExpirationMonitorService(
            _serviceProviderMock.Object,
            _loggerMock.Object,
            Options.Create(_options));

        var cancellationTokenSource = new CancellationTokenSource();

        // Act
        var task = service.StartAsync(cancellationTokenSource.Token);
        await Task.Delay(2000); // Let it run for 2 seconds
        cancellationTokenSource.Cancel();
        await task;

        // Assert
        _rfqRepositoryMock.Verify(r => r.Update(It.IsAny<RFQ>()), Times.AtLeastOnce);
        _unitOfWorkMock.Verify(u => u.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.AtLeastOnce);
    }

    [Fact]
    public async Task ProcessExpiringRfqs_WithNoExpiredOrApproachingRfqs_ShouldNotSaveChanges()
    {
        // Arrange
        _rfqRepositoryMock.Setup(r => r.GetExpiredActiveRfqsAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<RFQ>());

        _rfqRepositoryMock.Setup(r => r.GetRfqsApproachingExpirationAsync(
            It.IsAny<int[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<RFQ>());

        var service = new RfqExpirationMonitorService(
            _serviceProviderMock.Object,
            _loggerMock.Object,
            Options.Create(_options));

        var cancellationTokenSource = new CancellationTokenSource();

        // Act
        var task = service.StartAsync(cancellationTokenSource.Token);
        await Task.Delay(2000); // Let it run for 2 seconds
        cancellationTokenSource.Cancel();
        await task;

        // Assert
        _unitOfWorkMock.Verify(u => u.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task ProcessExpiringRfqs_WhenExceptionOccurs_ShouldLogErrorAndContinue()
    {
        // Arrange
        _rfqRepositoryMock.Setup(r => r.GetExpiredActiveRfqsAsync(It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Database error"));

        var service = new RfqExpirationMonitorService(
            _serviceProviderMock.Object,
            _loggerMock.Object,
            Options.Create(_options));

        var cancellationTokenSource = new CancellationTokenSource();

        // Act
        var task = service.StartAsync(cancellationTokenSource.Token);
        await Task.Delay(2000); // Let it run for 2 seconds
        cancellationTokenSource.Cancel();
        await task;

        // Assert
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Error occurred while processing expiring RFQs")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.AtLeastOnce);
    }

    [Fact]
    public void RfqExpirationOptions_DefaultValues_ShouldBeCorrect()
    {
        // Arrange & Act
        var options = new RfqExpirationOptions();

        // Assert
        options.CheckIntervalMinutes.Should().Be(15);
        options.NotificationHoursBeforeExpiration.Should().BeEquivalentTo(new[] { 24, 12, 6, 1 });
        options.DefaultTimeframeDurationHours.Should().Be(72);
        options.MaxExtensionsAllowed.Should().Be(3);
        options.AllowExtensionsByDefault.Should().BeTrue();
    }

    [Fact]
    public void RfqExpirationOptions_SectionName_ShouldBeCorrect()
    {
        // Act & Assert
        RfqExpirationOptions.SectionName.Should().Be("RfqExpiration");
    }
}

// Additional tests for command handlers
public class ExtendRfqTimeframeCommandHandlerTests
{
    private readonly Mock<IRfqRepository> _rfqRepositoryMock;
    private readonly Mock<IUnitOfWork> _unitOfWorkMock;
    private readonly Mock<IMessageBroker> _messageBrokerMock;
    private readonly Mock<ILogger<ExtendRfqTimeframeCommandHandler>> _loggerMock;
    private readonly ExtendRfqTimeframeCommandHandler _handler;

    public ExtendRfqTimeframeCommandHandlerTests()
    {
        _rfqRepositoryMock = new Mock<IRfqRepository>();
        _unitOfWorkMock = new Mock<IUnitOfWork>();
        _messageBrokerMock = new Mock<IMessageBroker>();
        _loggerMock = new Mock<ILogger<ExtendRfqTimeframeCommandHandler>>();

        _handler = new ExtendRfqTimeframeCommandHandler(
            _rfqRepositoryMock.Object,
            _unitOfWorkMock.Object,
            _messageBrokerMock.Object,
            _loggerMock.Object);
    }

    [Fact]
    public async Task Handle_WithValidRequest_ShouldExtendTimeframeAndReturnTrue()
    {
        // Arrange
        var rfqId = Guid.NewGuid();
        var transportCompanyId = Guid.NewGuid();

        var timeframe = new RfqTimeframe(24, TimeframeUnit.Hours, true, 3);
        var rfq = CreateTestRfqWithTimeframe(rfqId, transportCompanyId, timeframe);
        rfq.Publish();

        var command = new ExtendRfqTimeframeCommand
        {
            RfqId = rfqId,
            TransportCompanyId = transportCompanyId,
            Duration = 12,
            Unit = TimeframeUnit.Hours,
            Reason = "Need more time to review bids"
        };

        _rfqRepositoryMock.Setup(r => r.GetByIdAsync(rfqId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(rfq);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeTrue();
        _rfqRepositoryMock.Verify(r => r.Update(rfq), Times.Once);
        _unitOfWorkMock.Verify(u => u.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
        _messageBrokerMock.Verify(m => m.PublishAsync(
            "rfq.timeframe.extended",
            It.IsAny<object>(),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    private RFQ CreateTestRfqWithTimeframe(Guid rfqId, Guid transportCompanyId, RfqTimeframe timeframe)
    {
        var loadDetails = new LoadDetails(
            "Test load",
            LoadType.General,
            new Weight(1000, WeightUnit.Kg),
            1,
            VehicleType.Truck);

        var routeDetails = new RouteDetails(
            new Address("123 Main St", "City", "State", "12345", "Country"),
            new Address("456 Oak Ave", "City2", "State2", "67890", "Country"),
            DateTime.UtcNow.AddDays(1),
            DateTime.UtcNow.AddDays(2));

        var requirements = new RfqRequirements(
            VehicleType.Truck,
            null, false, null, false, new List<string>(),
            false, null, false, false, false, null, new List<string>());

        return new RFQ(
            transportCompanyId,
            "Test RFQ",
            "Test Description",
            loadDetails,
            routeDetails,
            requirements,
            timeframe: timeframe);
    }
}
