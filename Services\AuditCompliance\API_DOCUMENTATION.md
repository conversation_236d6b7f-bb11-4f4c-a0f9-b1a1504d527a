# Audit & Compliance Service API Documentation

## Base URL
```
https://api.tli.com/api/audit-compliance
```

## Authentication
All endpoints require JWT Bearer token authentication.

```http
Authorization: Bearer <your-jwt-token>
```

## API Endpoints

### 🔍 Audit Management

#### Create Audit Log Entry
```http
POST /api/audit/logs
```

**Request Body:**
```json
{
  "eventType": "UserLogin",
  "severity": "Info",
  "entityType": "User",
  "action": "Login",
  "description": "User logged in successfully",
  "userId": "123e4567-e89b-12d3-a456-426614174000",
  "userName": "john.doe",
  "userRole": "Admin",
  "entityId": "123e4567-e89b-12d3-a456-426614174000",
  "oldValues": "{}",
  "newValues": "{}",
  "ipAddress": "***********",
  "userAgent": "Mozilla/5.0...",
  "sessionId": "session123",
  "correlationId": "corr123",
  "complianceFlags": ["GDPR", "DataRetention"]
}
```

**Response:**
```json
{
  "auditLogId": "123e4567-e89b-12d3-a456-426614174000"
}
```

#### Get Audit Log by ID
```http
GET /api/audit/logs/{id}
```

**Response:**
```json
{
  "id": "123e4567-e89b-12d3-a456-426614174000",
  "eventType": "UserLogin",
  "severity": "Info",
  "userId": "123e4567-e89b-12d3-a456-426614174000",
  "userName": "john.doe",
  "userRole": "Admin",
  "entityType": "User",
  "entityId": "123e4567-e89b-12d3-a456-426614174000",
  "action": "Login",
  "description": "User logged in successfully",
  "eventTimestamp": "2024-01-15T10:30:00Z",
  "createdAt": "2024-01-15T10:30:00Z",
  "complianceFlags": ["GDPR"]
}
```

#### Search Audit Trail
```http
POST /api/audit/trail
```

**Request Body:**
```json
{
  "userId": "123e4567-e89b-12d3-a456-426614174000",
  "entityType": "User",
  "entityId": "123e4567-e89b-12d3-a456-426614174000",
  "eventType": "UserLogin",
  "minSeverity": "Info",
  "fromDate": "2024-01-01T00:00:00Z",
  "toDate": "2024-01-31T23:59:59Z",
  "searchTerm": "login",
  "pageNumber": 1,
  "pageSize": 50,
  "includeSensitiveData": false
}
```

### 📊 Compliance Management

#### Create Compliance Report
```http
POST /api/compliance/reports
```

**Request Body:**
```json
{
  "title": "GDPR Compliance Report Q1 2024",
  "description": "Quarterly GDPR compliance assessment",
  "standard": "GDPR",
  "reportPeriodStart": "2024-01-01T00:00:00Z",
  "reportPeriodEnd": "2024-03-31T23:59:59Z",
  "isAutomated": false
}
```

#### Get Compliance Summary
```http
GET /api/compliance/summary
```

**Response:**
```json
[
  {
    "standard": "GDPR",
    "overallStatus": "Compliant",
    "totalReports": 12,
    "compliantReports": 10,
    "nonCompliantReports": 2,
    "pendingReports": 0,
    "lastReportDate": "2024-01-15T10:30:00Z",
    "violationSummary": [
      {
        "severity": "High",
        "count": 2,
        "percentage": 16.67
      }
    ]
  }
]
```

### ⭐ Rating Management

#### Create Service Provider Rating
```http
POST /api/rating
```

**Request Body:**
```json
{
  "transportCompanyId": "123e4567-e89b-12d3-a456-426614174000",
  "transportCompanyName": "ABC Transport",
  "orderId": "123e4567-e89b-12d3-a456-426614174000",
  "orderNumber": "ORD-2024-001",
  "tripId": "123e4567-e89b-12d3-a456-426614174000",
  "tripNumber": "TRP-2024-001",
  "overallRating": 4.5,
  "reviewTitle": "Excellent Service",
  "reviewComment": "Very professional and timely delivery",
  "isAnonymous": false,
  "serviceCompletedAt": "2024-01-15T10:30:00Z",
  "categoryRatings": [
    {
      "category": "Timeliness",
      "rating": 5.0,
      "comment": "Always on time"
    },
    {
      "category": "Communication",
      "rating": 4.0,
      "comment": "Good updates"
    }
  ]
}
```

#### Get Transport Company Rating Summary
```http
GET /api/rating/transport-company/{transportCompanyId}/summary
```

**Response:**
```json
{
  "transportCompanyId": "123e4567-e89b-12d3-a456-426614174000",
  "transportCompanyName": "ABC Transport",
  "averageRating": 4.2,
  "totalRatings": 150,
  "fiveStarRatings": 45,
  "fourStarRatings": 60,
  "threeStarRatings": 30,
  "twoStarRatings": 10,
  "oneStarRatings": 5,
  "categoryAverages": [
    {
      "category": "Timeliness",
      "categoryName": "Timeliness",
      "averageRating": 4.5,
      "totalRatings": 150
    }
  ],
  "totalIssuesReported": 12,
  "resolvedIssues": 10,
  "lastRatingDate": "2024-01-15T10:30:00Z"
}
```

#### Report Service Issue
```http
POST /api/rating/issues
```

**Request Body:**
```json
{
  "serviceProviderRatingId": "123e4567-e89b-12d3-a456-426614174000",
  "issueType": "DelayedDelivery",
  "priority": "High",
  "description": "Package was delivered 2 days late",
  "evidence": "Tracking number: ABC123, Expected: 2024-01-10, Actual: 2024-01-12"
}
```

### 🏆 Preferred Provider Management

#### Get Preferred Providers
```http
GET /api/preferred-provider
```

**Response:**
```json
[
  {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "shipperId": "123e4567-e89b-12d3-a456-426614174000",
    "shipperName": "XYZ Logistics",
    "transportCompanyId": "123e4567-e89b-12d3-a456-426614174000",
    "transportCompanyName": "ABC Transport",
    "averageRating": 4.5,
    "totalOrders": 100,
    "completedOrders": 95,
    "completionRate": 95.0,
    "firstOrderDate": "2023-06-01T00:00:00Z",
    "lastOrderDate": "2024-01-15T10:30:00Z",
    "isActive": true,
    "preferenceRank": 1,
    "createdAt": "2023-06-01T00:00:00Z"
  }
]
```

#### Add Preferred Provider
```http
POST /api/preferred-provider
```

**Request Body:**
```json
{
  "transportCompanyId": "123e4567-e89b-12d3-a456-426614174000",
  "transportCompanyName": "ABC Transport",
  "averageRating": 4.5,
  "totalOrders": 50,
  "completedOrders": 48,
  "firstOrderDate": "2023-06-01T00:00:00Z",
  "lastOrderDate": "2024-01-15T10:30:00Z",
  "notes": "Excellent service provider with consistent performance"
}
```

## Error Responses

### Standard Error Format
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "One or more validation errors occurred",
    "details": [
      {
        "field": "overallRating",
        "message": "Rating must be between 1 and 5"
      }
    ]
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "path": "/api/rating"
}
```

### HTTP Status Codes
- `200 OK` - Successful request
- `201 Created` - Resource created successfully
- `400 Bad Request` - Invalid request data
- `401 Unauthorized` - Authentication required
- `403 Forbidden` - Insufficient permissions
- `404 Not Found` - Resource not found
- `409 Conflict` - Resource conflict
- `422 Unprocessable Entity` - Validation errors
- `500 Internal Server Error` - Server error

## Rate Limiting
- **Rate Limit**: 1000 requests per hour per user
- **Burst Limit**: 100 requests per minute
- **Headers**: `X-RateLimit-Remaining`, `X-RateLimit-Reset`

## Pagination
All list endpoints support pagination:
- `pageNumber` (default: 1)
- `pageSize` (default: 20, max: 100)

Response includes pagination metadata:
```json
{
  "data": [...],
  "totalCount": 500,
  "pageNumber": 1,
  "pageSize": 20,
  "totalPages": 25,
  "hasNextPage": true,
  "hasPreviousPage": false
}
```

## Webhooks
The service can send webhooks for important events:
- Compliance violations detected
- High-priority service issues reported
- Critical audit events

Configure webhook endpoints in your application settings.
