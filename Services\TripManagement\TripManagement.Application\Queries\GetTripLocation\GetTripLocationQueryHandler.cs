using MediatR;
using Microsoft.Extensions.Logging;
using TripManagement.Application.DTOs;
using TripManagement.Application.Interfaces;

namespace TripManagement.Application.Queries.GetTripLocation;

public class GetTripLocationQueryHandler : 
    IRequestHandler<GetTripLocationQuery, TripLocationDto?>,
    IRequestHandler<GetTripLocationHistoryQuery, List<TripLocationDto>>,
    IRequestHandler<GetRealTimeTripStatusQuery, RealTimeTripStatusDto?>
{
    private readonly ITripRepository _tripRepository;
    private readonly ILogger<GetTripLocationQueryHandler> _logger;

    public GetTripLocationQueryHandler(
        ITripRepository tripRepository,
        ILogger<GetTripLocationQueryHandler> logger)
    {
        _tripRepository = tripRepository;
        _logger = logger;
    }

    public async Task<TripLocationDto?> Handle(GetTripLocationQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Getting current location for trip {TripId}", request.TripId);

            var trip = await _tripRepository.GetByIdAsync(request.TripId, cancellationToken);
            if (trip == null)
            {
                _logger.LogWarning("Trip {TripId} not found", request.TripId);
                return null;
            }

            var currentLocation = trip.GetCurrentLocation();
            if (currentLocation == null)
            {
                _logger.LogWarning("No location data found for trip {TripId}", request.TripId);
                return null;
            }

            var latestLocationUpdate = trip.LocationUpdates
                .OrderByDescending(l => l.Timestamp)
                .FirstOrDefault();

            if (latestLocationUpdate == null)
                return null;

            return new TripLocationDto
            {
                TripId = request.TripId,
                Latitude = latestLocationUpdate.Location.Latitude,
                Longitude = latestLocationUpdate.Location.Longitude,
                Altitude = latestLocationUpdate.Altitude,
                Accuracy = latestLocationUpdate.Accuracy,
                Speed = latestLocationUpdate.Speed,
                Heading = latestLocationUpdate.Heading,
                Source = latestLocationUpdate.Source.ToString(),
                Timestamp = latestLocationUpdate.Timestamp,
                IsSignificantUpdate = latestLocationUpdate.IsSignificantUpdate,
                DistanceFromPreviousKm = latestLocationUpdate.DistanceFromPreviousKm,
                GeofenceStatus = latestLocationUpdate.GeofenceStatus.ToString(),
                GeofenceZoneId = latestLocationUpdate.GeofenceZoneId
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting location for trip {TripId}", request.TripId);
            throw;
        }
    }

    public async Task<List<TripLocationDto>> Handle(GetTripLocationHistoryQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Getting location history for trip {TripId}", request.TripId);

            var trip = await _tripRepository.GetByIdAsync(request.TripId, cancellationToken);
            if (trip == null)
            {
                _logger.LogWarning("Trip {TripId} not found", request.TripId);
                return new List<TripLocationDto>();
            }

            var locationUpdates = trip.LocationUpdates.AsQueryable();

            if (request.FromDate.HasValue)
                locationUpdates = locationUpdates.Where(l => l.Timestamp >= request.FromDate.Value);

            if (request.ToDate.HasValue)
                locationUpdates = locationUpdates.Where(l => l.Timestamp <= request.ToDate.Value);

            var locations = locationUpdates
                .OrderByDescending(l => l.Timestamp)
                .Take(request.Limit)
                .Select(l => new TripLocationDto
                {
                    TripId = request.TripId,
                    Latitude = l.Location.Latitude,
                    Longitude = l.Location.Longitude,
                    Altitude = l.Altitude,
                    Accuracy = l.Accuracy,
                    Speed = l.Speed,
                    Heading = l.Heading,
                    Source = l.Source.ToString(),
                    Timestamp = l.Timestamp,
                    IsSignificantUpdate = l.IsSignificantUpdate,
                    DistanceFromPreviousKm = l.DistanceFromPreviousKm,
                    GeofenceStatus = l.GeofenceStatus.ToString(),
                    GeofenceZoneId = l.GeofenceZoneId
                })
                .ToList();

            _logger.LogInformation("Retrieved {LocationCount} location updates for trip {TripId}", 
                locations.Count, request.TripId);

            return locations;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting location history for trip {TripId}", request.TripId);
            throw;
        }
    }

    public async Task<RealTimeTripStatusDto?> Handle(GetRealTimeTripStatusQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Getting real-time status for trip {TripId}", request.TripId);

            var trip = await _tripRepository.GetByIdWithStopsAsync(request.TripId, cancellationToken);
            if (trip == null)
            {
                _logger.LogWarning("Trip {TripId} not found", request.TripId);
                return null;
            }

            var currentLocation = trip.GetCurrentLocation();
            var latestLocationUpdate = trip.LocationUpdates
                .OrderByDescending(l => l.Timestamp)
                .FirstOrDefault();

            var tripLocationDto = latestLocationUpdate != null ? new TripLocationDto
            {
                TripId = request.TripId,
                Latitude = latestLocationUpdate.Location.Latitude,
                Longitude = latestLocationUpdate.Location.Longitude,
                Altitude = latestLocationUpdate.Altitude,
                Accuracy = latestLocationUpdate.Accuracy,
                Speed = latestLocationUpdate.Speed,
                Heading = latestLocationUpdate.Heading,
                Source = latestLocationUpdate.Source.ToString(),
                Timestamp = latestLocationUpdate.Timestamp,
                IsSignificantUpdate = latestLocationUpdate.IsSignificantUpdate,
                DistanceFromPreviousKm = latestLocationUpdate.DistanceFromPreviousKm,
                GeofenceStatus = latestLocationUpdate.GeofenceStatus.ToString(),
                GeofenceZoneId = latestLocationUpdate.GeofenceZoneId
            } : null;

            // Calculate progress percentage
            var totalDistance = trip.Route.StartLocation.CalculateDistanceKm(trip.Route.EndLocation);
            var remainingDistance = currentLocation?.CalculateDistanceKm(trip.Route.EndLocation) ?? totalDistance;
            var progressPercentage = totalDistance > 0 ? Math.Max(0, (totalDistance - remainingDistance) / totalDistance * 100) : 0;

            var stopStatuses = trip.Stops.Select(s => new TripStopStatusDto
            {
                StopId = s.Id,
                StopType = s.StopType.ToString(),
                Status = s.Status.ToString(),
                ScheduledArrival = s.ScheduledArrival,
                EstimatedArrival = s.EstimatedArrival,
                ActualArrival = s.ActualArrival,
                IsDelayed = s.ScheduledArrival.HasValue && s.EstimatedArrival.HasValue && 
                           s.EstimatedArrival.Value > s.ScheduledArrival.Value.AddMinutes(5)
            }).ToList();

            return new RealTimeTripStatusDto
            {
                TripId = request.TripId,
                TripNumber = trip.TripNumber,
                Status = trip.Status.ToString(),
                CurrentLocation = tripLocationDto,
                EstimatedArrival = trip.EstimatedEndTime,
                ProgressPercentage = progressPercentage,
                Stops = stopStatuses,
                LastUpdated = latestLocationUpdate?.Timestamp ?? DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting real-time status for trip {TripId}", request.TripId);
            throw;
        }
    }
}
