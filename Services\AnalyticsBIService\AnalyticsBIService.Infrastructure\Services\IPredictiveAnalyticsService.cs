using AnalyticsBIService.Domain.Enums;

namespace AnalyticsBIService.Infrastructure.Services;

/// <summary>
/// Interface for predictive analytics service
/// </summary>
public interface IPredictiveAnalyticsService
{
    /// <summary>
    /// Predict revenue for a given period
    /// </summary>
    Task<RevenueForecastDto> PredictRevenueAsync(Guid? userId, UserType? userType, int forecastDays, CancellationToken cancellationToken = default);

    /// <summary>
    /// Predict demand for transportation services
    /// </summary>
    Task<DemandForecastDto> PredictDemandAsync(Guid? userId, UserType? userType, int forecastDays, CancellationToken cancellationToken = default);

    /// <summary>
    /// Predict user churn probability
    /// </summary>
    Task<ChurnPredictionDto> PredictChurnAsync(Guid userId, UserType userType, CancellationToken cancellationToken = default);

    /// <summary>
    /// Predict optimal pricing for routes
    /// </summary>
    Task<PricingRecommendationDto> PredictOptimalPricingAsync(string fromLocation, string toLocation, DateTime proposedDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Predict seasonal trends
    /// </summary>
    Task<SeasonalTrendDto> PredictSeasonalTrendsAsync(string metricName, int forecastMonths, CancellationToken cancellationToken = default);

    /// <summary>
    /// Predict capacity requirements
    /// </summary>
    Task<CapacityForecastDto> PredictCapacityRequirementsAsync(Guid? userId, UserType? userType, int forecastDays, CancellationToken cancellationToken = default);

    /// <summary>
    /// Predict market trends
    /// </summary>
    Task<MarketTrendDto> PredictMarketTrendsAsync(string region, int forecastDays, CancellationToken cancellationToken = default);

    /// <summary>
    /// Train predictive models with historical data
    /// </summary>
    Task TrainModelsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Evaluate model accuracy
    /// </summary>
    Task<ModelAccuracyDto> EvaluateModelAccuracyAsync(string modelType, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get model performance metrics
    /// </summary>
    Task<List<ModelPerformanceDto>> GetModelPerformanceAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Revenue forecast data transfer object
/// </summary>
public class RevenueForecastDto
{
    public Guid? UserId { get; set; }
    public UserType? UserType { get; set; }
    public DateTime ForecastDate { get; set; }
    public int ForecastDays { get; set; }
    public List<ForecastDataPoint> Forecast { get; set; } = new();
    public decimal ConfidenceLevel { get; set; }
    public string ModelUsed { get; set; } = string.Empty;
    public DateTime GeneratedAt { get; set; }
}

/// <summary>
/// Demand forecast data transfer object
/// </summary>
public class DemandForecastDto
{
    public Guid? UserId { get; set; }
    public UserType? UserType { get; set; }
    public DateTime ForecastDate { get; set; }
    public int ForecastDays { get; set; }
    public List<ForecastDataPoint> Forecast { get; set; } = new();
    public decimal ConfidenceLevel { get; set; }
    public string ModelUsed { get; set; } = string.Empty;
    public DateTime GeneratedAt { get; set; }
}

/// <summary>
/// Churn prediction data transfer object
/// </summary>
public class ChurnPredictionDto
{
    public Guid UserId { get; set; }
    public UserType UserType { get; set; }
    public decimal ChurnProbability { get; set; }
    public string RiskLevel { get; set; } = string.Empty;
    public List<ChurnRiskFactor> RiskFactors { get; set; } = new();
    public List<string> RecommendedActions { get; set; } = new();
    public DateTime PredictionDate { get; set; }
    public string ModelUsed { get; set; } = string.Empty;
}

/// <summary>
/// Pricing recommendation data transfer object
/// </summary>
public class PricingRecommendationDto
{
    public string FromLocation { get; set; } = string.Empty;
    public string ToLocation { get; set; } = string.Empty;
    public DateTime ProposedDate { get; set; }
    public decimal RecommendedPrice { get; set; }
    public decimal MinPrice { get; set; }
    public decimal MaxPrice { get; set; }
    public decimal ConfidenceLevel { get; set; }
    public List<PricingFactor> PricingFactors { get; set; } = new();
    public string ModelUsed { get; set; } = string.Empty;
    public DateTime GeneratedAt { get; set; }
}

/// <summary>
/// Seasonal trend data transfer object
/// </summary>
public class SeasonalTrendDto
{
    public string MetricName { get; set; } = string.Empty;
    public int ForecastMonths { get; set; }
    public List<SeasonalDataPoint> SeasonalPattern { get; set; } = new();
    public List<ForecastDataPoint> Forecast { get; set; } = new();
    public decimal ConfidenceLevel { get; set; }
    public string ModelUsed { get; set; } = string.Empty;
    public DateTime GeneratedAt { get; set; }
}

/// <summary>
/// Capacity forecast data transfer object
/// </summary>
public class CapacityForecastDto
{
    public Guid? UserId { get; set; }
    public UserType? UserType { get; set; }
    public DateTime ForecastDate { get; set; }
    public int ForecastDays { get; set; }
    public List<CapacityDataPoint> CapacityForecast { get; set; } = new();
    public decimal ConfidenceLevel { get; set; }
    public string ModelUsed { get; set; } = string.Empty;
    public DateTime GeneratedAt { get; set; }
}

/// <summary>
/// Market trend data transfer object
/// </summary>
public class MarketTrendDto
{
    public string Region { get; set; } = string.Empty;
    public DateTime ForecastDate { get; set; }
    public int ForecastDays { get; set; }
    public List<MarketDataPoint> MarketTrends { get; set; } = new();
    public decimal ConfidenceLevel { get; set; }
    public string ModelUsed { get; set; } = string.Empty;
    public DateTime GeneratedAt { get; set; }
}

/// <summary>
/// Model accuracy data transfer object
/// </summary>
public class ModelAccuracyDto
{
    public string ModelType { get; set; } = string.Empty;
    public decimal Accuracy { get; set; }
    public decimal Precision { get; set; }
    public decimal Recall { get; set; }
    public decimal F1Score { get; set; }
    public decimal MeanAbsoluteError { get; set; }
    public decimal RootMeanSquareError { get; set; }
    public DateTime EvaluatedAt { get; set; }
    public int TestDataSize { get; set; }
}

/// <summary>
/// Model performance data transfer object
/// </summary>
public class ModelPerformanceDto
{
    public string ModelName { get; set; } = string.Empty;
    public string ModelType { get; set; } = string.Empty;
    public decimal Accuracy { get; set; }
    public DateTime LastTrained { get; set; }
    public DateTime LastEvaluated { get; set; }
    public int PredictionsMade { get; set; }
    public string Status { get; set; } = string.Empty;
}

/// <summary>
/// Forecast data point
/// </summary>
public class ForecastDataPoint
{
    public DateTime Date { get; set; }
    public decimal Value { get; set; }
    public decimal LowerBound { get; set; }
    public decimal UpperBound { get; set; }
    public decimal Confidence { get; set; }
}

/// <summary>
/// Churn risk factor
/// </summary>
public class ChurnRiskFactor
{
    public string Factor { get; set; } = string.Empty;
    public decimal Impact { get; set; }
    public string Description { get; set; } = string.Empty;
}

/// <summary>
/// Pricing factor
/// </summary>
public class PricingFactor
{
    public string Factor { get; set; } = string.Empty;
    public decimal Impact { get; set; }
    public string Description { get; set; } = string.Empty;
}

/// <summary>
/// Seasonal data point
/// </summary>
public class SeasonalDataPoint
{
    public int Month { get; set; }
    public decimal SeasonalIndex { get; set; }
    public string Description { get; set; } = string.Empty;
}

/// <summary>
/// Capacity data point
/// </summary>
public class CapacityDataPoint
{
    public DateTime Date { get; set; }
    public decimal RequiredCapacity { get; set; }
    public decimal AvailableCapacity { get; set; }
    public decimal UtilizationRate { get; set; }
    public string CapacityType { get; set; } = string.Empty;
}

/// <summary>
/// Market data point
/// </summary>
public class MarketDataPoint
{
    public DateTime Date { get; set; }
    public decimal MarketSize { get; set; }
    public decimal GrowthRate { get; set; }
    public decimal CompetitionIndex { get; set; }
    public string TrendDirection { get; set; } = string.Empty;
}
