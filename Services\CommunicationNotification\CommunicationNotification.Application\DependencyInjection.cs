using System.Reflection;
using Microsoft.Extensions.DependencyInjection;
using FluentValidation;

namespace CommunicationNotification.Application;

/// <summary>
/// Dependency injection configuration for Communication & Notification Application layer
/// </summary>
public static class DependencyInjection
{
    /// <summary>
    /// Add application services to the service collection
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <returns>Service collection</returns>
    public static IServiceCollection AddApplication(this IServiceCollection services)
    {
        // MediatR for CQRS
        services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly()));

        // AutoMapper for object mapping
        services.AddAutoMapper(Assembly.GetExecutingAssembly());

        // FluentValidation for request validation
        services.AddValidatorsFromAssembly(Assembly.GetExecutingAssembly());

        return services;
    }
}
