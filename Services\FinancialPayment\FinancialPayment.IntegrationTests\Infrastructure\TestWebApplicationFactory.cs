using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Xunit;
using FinancialPayment.Infrastructure.Data;
using Testcontainers.PostgreSql;

namespace FinancialPayment.IntegrationTests.Infrastructure;

public class TestWebApplicationFactory : WebApplicationFactory<Program>, IAsyncLifetime
{
    private readonly PostgreSqlContainer _dbContainer = new PostgreSqlBuilder()
        .WithImage("timescale/timescaledb:latest-pg15")
        .WithDatabase("test_financial_payment")
        .WithUsername("test_user")
        .WithPassword("test_password")
        .WithPortBinding(5433, 5432)
        .Build();

    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        builder.ConfigureServices(services =>
        {
            // Remove the existing DbContext registration
            var descriptor = services.SingleOrDefault(
                d => d.ServiceType == typeof(DbContextOptions<FinancialPaymentDbContext>));
            if (descriptor != null)
                services.Remove(descriptor);

            // Add test database context
            services.AddDbContext<FinancialPaymentDbContext>(options =>
            {
                options.UseNpgsql(_dbContainer.GetConnectionString());
            });

            // Override logging to reduce noise in tests
            services.AddLogging(builder =>
            {
                builder.ClearProviders();
                builder.AddConsole();
                builder.SetMinimumLevel(LogLevel.Warning);
            });
        });

        builder.UseEnvironment("Testing");
    }

    public async Task InitializeAsync()
    {
        await _dbContainer.StartAsync();

        // Create and migrate the database
        using var scope = Services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<FinancialPaymentDbContext>();
        await context.Database.EnsureCreatedAsync();
    }

    public new async Task DisposeAsync()
    {
        await _dbContainer.StopAsync();
        await _dbContainer.DisposeAsync();
        await base.DisposeAsync();
    }

    public async Task ResetDatabaseAsync()
    {
        using var scope = Services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<FinancialPaymentDbContext>();

        // Clear all data
        await context.Database.ExecuteSqlRawAsync("TRUNCATE TABLE financial_payment.escrow_accounts CASCADE");
        await context.Database.ExecuteSqlRawAsync("TRUNCATE TABLE financial_payment.settlements CASCADE");
        await context.Database.ExecuteSqlRawAsync("TRUNCATE TABLE financial_payment.commissions CASCADE");
        await context.Database.ExecuteSqlRawAsync("TRUNCATE TABLE financial_payment.payment_disputes CASCADE");
    }
}
