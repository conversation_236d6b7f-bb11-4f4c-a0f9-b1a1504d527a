# Tax System Deployment Script
# This script deploys the tax inclusion system with database migrations and validation

param(
    [Parameter(Mandatory=$true)]
    [string]$Environment,
    
    [Parameter(Mandatory=$false)]
    [string]$ConnectionString,
    
    [Parameter(Mandatory=$false)]
    [switch]$SkipMigration,
    
    [Parameter(Mandatory=$false)]
    [switch]$ValidateOnly,
    
    [Parameter(Mandatory=$false)]
    [switch]$Rollback
)

# Configuration
$ScriptPath = Split-Path -Parent $MyInvocation.MyCommand.Definition
$ProjectPath = Split-Path -Parent $ScriptPath
$MigrationProject = "$ProjectPath\SubscriptionManagement.Infrastructure"
$ApiProject = "$ProjectPath\SubscriptionManagement.API"

Write-Host "=== Tax System Deployment Script ===" -ForegroundColor Green
Write-Host "Environment: $Environment" -ForegroundColor Yellow
Write-Host "Project Path: $ProjectPath" -ForegroundColor Yellow

# Validate environment
if ($Environment -notin @("Development", "Staging", "Production")) {
    Write-Error "Invalid environment. Must be Development, Staging, or Production."
    exit 1
}

# Set connection string based on environment if not provided
if (-not $ConnectionString) {
    switch ($Environment) {
        "Development" { 
            $ConnectionString = "Host=localhost;Database=subscription_dev;Username=dev_user;Password=dev_password" 
        }
        "Staging" { 
            $ConnectionString = $env:STAGING_CONNECTION_STRING 
        }
        "Production" { 
            $ConnectionString = $env:PRODUCTION_CONNECTION_STRING 
        }
    }
}

if (-not $ConnectionString) {
    Write-Error "Connection string not provided and not found in environment variables."
    exit 1
}

function Test-DatabaseConnection {
    param([string]$ConnectionString)
    
    Write-Host "Testing database connection..." -ForegroundColor Blue
    
    try {
        # Test connection using psql
        $env:PGPASSWORD = ($ConnectionString -split "Password=")[1] -split ";")[0]
        $host = ($ConnectionString -split "Host=")[1] -split ";")[0]
        $database = ($ConnectionString -split "Database=")[1] -split ";")[0]
        $username = ($ConnectionString -split "Username=")[1] -split ";")[0]
        
        $result = psql -h $host -d $database -U $username -c "SELECT 1;" 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Database connection successful" -ForegroundColor Green
            return $true
        } else {
            Write-Error "✗ Database connection failed: $result"
            return $false
        }
    }
    catch {
        Write-Error "✗ Database connection test failed: $($_.Exception.Message)"
        return $false
    }
}

function Invoke-DatabaseMigration {
    param([string]$ConnectionString, [string]$ProjectPath)
    
    Write-Host "Running database migrations..." -ForegroundColor Blue
    
    try {
        # Set connection string environment variable
        $env:ConnectionStrings__DefaultConnection = $ConnectionString
        
        # Run EF Core migrations
        $migrationResult = dotnet ef database update --project $ProjectPath --verbose 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Database migrations completed successfully" -ForegroundColor Green
            Write-Host $migrationResult -ForegroundColor Gray
            return $true
        } else {
            Write-Error "✗ Database migration failed: $migrationResult"
            return $false
        }
    }
    catch {
        Write-Error "✗ Migration execution failed: $($_.Exception.Message)"
        return $false
    }
}

function Test-TaxSystemValidation {
    param([string]$ApiBaseUrl)
    
    Write-Host "Validating tax system deployment..." -ForegroundColor Blue
    
    try {
        # Test supported regions endpoint
        $regionsResponse = Invoke-RestMethod -Uri "$ApiBaseUrl/api/tax/regions" -Method Get -TimeoutSec 30
        
        if ($regionsResponse -and $regionsResponse.Count -gt 0) {
            Write-Host "✓ Tax regions endpoint working" -ForegroundColor Green
            Write-Host "  Supported regions: $($regionsResponse.Count)" -ForegroundColor Gray
        } else {
            Write-Warning "⚠ Tax regions endpoint returned empty response"
        }
        
        # Test health check if available
        try {
            $healthResponse = Invoke-RestMethod -Uri "$ApiBaseUrl/health" -Method Get -TimeoutSec 10
            Write-Host "✓ Health check passed" -ForegroundColor Green
        }
        catch {
            Write-Warning "⚠ Health check endpoint not available or failed"
        }
        
        return $true
    }
    catch {
        Write-Error "✗ Tax system validation failed: $($_.Exception.Message)"
        return $false
    }
}

function Install-DefaultTaxConfigurations {
    param([string]$ConnectionString)
    
    Write-Host "Installing default tax configurations..." -ForegroundColor Blue
    
    try {
        # Set environment variable for connection
        $env:PGPASSWORD = ($ConnectionString -split "Password=")[1] -split ";")[0]
        $host = ($ConnectionString -split "Host=")[1] -split ";")[0]
        $database = ($ConnectionString -split "Database=")[1] -split ";")[0]
        $username = ($ConnectionString -split "Username=")[1] -split ";")[0]
        
        # Check if default configurations already exist
        $existingConfigs = psql -h $host -d $database -U $username -t -c "SELECT COUNT(*) FROM subscription.\"GlobalTaxConfigurations\";" 2>&1
        
        if ($existingConfigs -and $existingConfigs.Trim() -eq "0") {
            Write-Host "Installing default tax configurations..." -ForegroundColor Yellow
            
            # Install default configurations
            $defaultConfigSql = @"
-- Default tax configurations
INSERT INTO subscription."GlobalTaxConfigurations" 
("Id", "TaxType", "Rate", "IsIncluded", "EffectiveDate", "ApplicableRegions", "IsActive", "Priority", "Description", "CreatedByUserId", "CreatedAt") 
VALUES
(gen_random_uuid(), 'GST', 18.0000, false, CURRENT_TIMESTAMP, 'IN', true, 1, 'Standard GST for digital services in India', '00000000-0000-0000-0000-000000000001', CURRENT_TIMESTAMP),
(gen_random_uuid(), 'TDS', 2.0000, false, CURRENT_TIMESTAMP, 'IN', true, 2, 'TDS for digital services in India', '00000000-0000-0000-0000-000000000001', CURRENT_TIMESTAMP),
(gen_random_uuid(), 'VAT', 20.0000, true, CURRENT_TIMESTAMP, 'GB', true, 1, 'Standard VAT for UK digital services', '00000000-0000-0000-0000-000000000001', CURRENT_TIMESTAMP);
"@
            
            $result = psql -h $host -d $database -U $username -c $defaultConfigSql 2>&1
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✓ Default tax configurations installed" -ForegroundColor Green
            } else {
                Write-Warning "⚠ Failed to install default configurations: $result"
            }
        } else {
            Write-Host "✓ Tax configurations already exist, skipping default installation" -ForegroundColor Green
        }
        
        return $true
    }
    catch {
        Write-Error "✗ Failed to install default configurations: $($_.Exception.Message)"
        return $false
    }
}

function Backup-Database {
    param([string]$ConnectionString, [string]$BackupPath)
    
    Write-Host "Creating database backup..." -ForegroundColor Blue
    
    try {
        $env:PGPASSWORD = ($ConnectionString -split "Password=")[1] -split ";")[0]
        $host = ($ConnectionString -split "Host=")[1] -split ";")[0]
        $database = ($ConnectionString -split "Database=")[1] -split ";")[0]
        $username = ($ConnectionString -split "Username=")[1] -split ";")[0]
        
        $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
        $backupFile = "$BackupPath\backup_${database}_${timestamp}.sql"
        
        # Ensure backup directory exists
        $backupDir = Split-Path -Parent $backupFile
        if (-not (Test-Path $backupDir)) {
            New-Item -ItemType Directory -Path $backupDir -Force | Out-Null
        }
        
        $result = pg_dump -h $host -U $username -d $database -f $backupFile 2>&1
        
        if ($LASTEXITCODE -eq 0 -and (Test-Path $backupFile)) {
            Write-Host "✓ Database backup created: $backupFile" -ForegroundColor Green
            return $backupFile
        } else {
            Write-Error "✗ Database backup failed: $result"
            return $null
        }
    }
    catch {
        Write-Error "✗ Backup creation failed: $($_.Exception.Message)"
        return $null
    }
}

# Main deployment logic
try {
    Write-Host "`n=== Starting Tax System Deployment ===" -ForegroundColor Green
    
    # Step 1: Test database connection
    if (-not (Test-DatabaseConnection -ConnectionString $ConnectionString)) {
        throw "Database connection test failed"
    }
    
    # Step 2: Create backup for non-development environments
    if ($Environment -ne "Development") {
        $backupPath = "$ProjectPath\backups"
        $backupFile = Backup-Database -ConnectionString $ConnectionString -BackupPath $backupPath
        if (-not $backupFile) {
            Write-Warning "⚠ Backup creation failed, but continuing with deployment"
        }
    }
    
    # Step 3: Run database migrations (unless skipped)
    if (-not $SkipMigration) {
        if (-not (Invoke-DatabaseMigration -ConnectionString $ConnectionString -ProjectPath $MigrationProject)) {
            throw "Database migration failed"
        }
        
        # Install default tax configurations
        if (-not (Install-DefaultTaxConfigurations -ConnectionString $ConnectionString)) {
            Write-Warning "⚠ Default tax configuration installation failed"
        }
    } else {
        Write-Host "⚠ Skipping database migration as requested" -ForegroundColor Yellow
    }
    
    # Step 4: Validation (if not validation-only mode)
    if (-not $ValidateOnly) {
        # Build and test the application
        Write-Host "Building application..." -ForegroundColor Blue
        $buildResult = dotnet build $ApiProject --configuration Release 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Application build successful" -ForegroundColor Green
        } else {
            Write-Error "✗ Application build failed: $buildResult"
            throw "Build failed"
        }
        
        # Run tests
        Write-Host "Running tests..." -ForegroundColor Blue
        $testResult = dotnet test "$ProjectPath\SubscriptionManagement.Tests" --configuration Release --logger "console;verbosity=minimal" 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ All tests passed" -ForegroundColor Green
        } else {
            Write-Warning "⚠ Some tests failed: $testResult"
            if ($Environment -eq "Production") {
                throw "Tests failed in production deployment"
            }
        }
    }
    
    # Step 5: Final validation
    Write-Host "`n=== Deployment Validation ===" -ForegroundColor Green
    Write-Host "✓ Database connection: OK" -ForegroundColor Green
    Write-Host "✓ Database migrations: OK" -ForegroundColor Green
    Write-Host "✓ Application build: OK" -ForegroundColor Green
    Write-Host "✓ Tax system: Deployed successfully" -ForegroundColor Green
    
    Write-Host "`n=== Deployment Summary ===" -ForegroundColor Green
    Write-Host "Environment: $Environment" -ForegroundColor White
    Write-Host "Deployment Time: $(Get-Date)" -ForegroundColor White
    Write-Host "Migration Status: $(if ($SkipMigration) { 'Skipped' } else { 'Completed' })" -ForegroundColor White
    
    if ($backupFile) {
        Write-Host "Backup File: $backupFile" -ForegroundColor White
    }
    
    Write-Host "`n🎉 Tax system deployment completed successfully!" -ForegroundColor Green
    
    # Post-deployment instructions
    Write-Host "`n=== Post-Deployment Steps ===" -ForegroundColor Yellow
    Write-Host "1. Verify tax configurations in admin panel" -ForegroundColor White
    Write-Host "2. Test tax calculations with sample data" -ForegroundColor White
    Write-Host "3. Review audit logs for any issues" -ForegroundColor White
    Write-Host "4. Update monitoring dashboards" -ForegroundColor White
    Write-Host "5. Notify stakeholders of deployment completion" -ForegroundColor White
    
}
catch {
    Write-Host "`n❌ Deployment failed: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($Environment -eq "Production" -and $backupFile) {
        Write-Host "`n⚠ Production deployment failed. Consider rollback using backup: $backupFile" -ForegroundColor Yellow
    }
    
    exit 1
}

Write-Host "`n=== Deployment Complete ===" -ForegroundColor Green
