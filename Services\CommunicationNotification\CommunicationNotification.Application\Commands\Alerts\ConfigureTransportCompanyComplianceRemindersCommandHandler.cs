using CommunicationNotification.Application.Common;
using CommunicationNotification.Application.Services;
using CommunicationNotification.Domain.Entities;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.Repositories;
using MediatR;
using Microsoft.Extensions.Logging;

namespace CommunicationNotification.Application.Commands.Alerts;

/// <summary>
/// Handler for configuring Transport Company compliance reminders
/// </summary>
public class ConfigureTransportCompanyComplianceRemindersCommandHandler : 
    IRequestHandler<ConfigureTransportCompanyComplianceRemindersCommand, ConfigureTransportCompanyComplianceRemindersResult>
{
    private readonly INotificationOrchestrationService _notificationService;
    private readonly ITemplateService _templateService;
    private readonly IUserPreferenceRepository _userPreferenceRepository;
    private readonly IScheduledJobService _scheduledJobService;
    private readonly ILogger<ConfigureTransportCompanyComplianceRemindersCommandHandler> _logger;

    public ConfigureTransportCompanyComplianceRemindersCommandHandler(
        INotificationOrchestrationService notificationService,
        ITemplateService templateService,
        IUserPreferenceRepository userPreferenceRepository,
        IScheduledJobService scheduledJobService,
        ILogger<ConfigureTransportCompanyComplianceRemindersCommandHandler> logger)
    {
        _notificationService = notificationService;
        _templateService = templateService;
        _userPreferenceRepository = userPreferenceRepository;
        _scheduledJobService = scheduledJobService;
        _logger = logger;
    }

    public async Task<ConfigureTransportCompanyComplianceRemindersResult> Handle(
        ConfigureTransportCompanyComplianceRemindersCommand request, 
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Configuring compliance reminders for Transport Company {TransportCompanyId}", 
            request.TransportCompanyId);

        try
        {
            var configurationId = Guid.NewGuid();
            var scheduledJobIds = new List<Guid>();
            var configurationSummary = new Dictionary<string, object>();

            // Validate and process each reminder configuration
            foreach (var config in request.ReminderConfigurations)
            {
                if (!config.IsEnabled) continue;

                var jobIds = await ConfigureReminderJobs(
                    request.TransportCompanyId,
                    config,
                    request,
                    cancellationToken);

                scheduledJobIds.AddRange(jobIds);
                
                configurationSummary[$"{config.DocumentType}_{config.EntityType}"] = new
                {
                    DocumentType = config.DocumentType,
                    EntityType = config.EntityType,
                    ReminderDays = config.ReminderDays,
                    JobIds = jobIds,
                    IsEnabled = config.IsEnabled
                };
            }

            // Calculate next reminder check time
            var nextReminderCheck = CalculateNextReminderCheck(request.PreferredDeliveryTime, request.TimeZone);

            // Store configuration in user preferences
            await StoreComplianceConfiguration(request, configurationId, cancellationToken);

            var result = new ConfigureTransportCompanyComplianceRemindersResult
            {
                ConfigurationId = configurationId,
                IsSuccess = true,
                ScheduledJobIds = scheduledJobIds,
                ConfiguredAt = DateTime.UtcNow,
                NextReminderCheck = nextReminderCheck,
                TotalRemindersConfigured = scheduledJobIds.Count,
                ConfigurationSummary = configurationSummary
            };

            _logger.LogInformation("Successfully configured {Count} compliance reminders for Transport Company {TransportCompanyId}",
                scheduledJobIds.Count, request.TransportCompanyId);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to configure compliance reminders for Transport Company {TransportCompanyId}",
                request.TransportCompanyId);

            return new ConfigureTransportCompanyComplianceRemindersResult
            {
                ConfigurationId = Guid.NewGuid(),
                IsSuccess = false,
                ErrorMessage = ex.Message,
                ConfiguredAt = DateTime.UtcNow
            };
        }
    }

    private async Task<List<Guid>> ConfigureReminderJobs(
        Guid transportCompanyId,
        ComplianceReminderConfiguration config,
        ConfigureTransportCompanyComplianceRemindersCommand request,
        CancellationToken cancellationToken)
    {
        var jobIds = new List<Guid>();

        foreach (var reminderDay in config.ReminderDays)
        {
            var jobId = await _scheduledJobService.ScheduleRecurringJobAsync(
                $"compliance_reminder_{transportCompanyId}_{config.DocumentType}_{config.EntityType}_{reminderDay}",
                "0 9 * * *", // Daily at 9 AM (can be customized based on request.PreferredDeliveryTime)
                new Dictionary<string, object>
                {
                    ["TransportCompanyId"] = transportCompanyId,
                    ["DocumentType"] = config.DocumentType,
                    ["EntityType"] = config.EntityType,
                    ["ReminderDays"] = reminderDay,
                    ["Priority"] = config.Priority,
                    ["RequireAcknowledgment"] = config.RequireAcknowledgment,
                    ["PreferredChannels"] = request.PreferredChannels,
                    ["CustomMessage"] = config.CustomMessage,
                    ["TimeZone"] = request.TimeZone,
                    ["GroupSimilarReminders"] = request.GroupSimilarReminders,
                    ["MaxRemindersPerDay"] = request.MaxRemindersPerDay
                },
                cancellationToken);

            if (jobId != Guid.Empty)
            {
                jobIds.Add(jobId);
            }
        }

        return jobIds;
    }

    private DateTime CalculateNextReminderCheck(TimeSpan preferredTime, string timeZone)
    {
        var now = DateTime.UtcNow;
        var today = now.Date;
        var nextCheck = today.Add(preferredTime);

        // If preferred time has passed today, schedule for tomorrow
        if (nextCheck <= now)
        {
            nextCheck = nextCheck.AddDays(1);
        }

        return nextCheck;
    }

    private async Task StoreComplianceConfiguration(
        ConfigureTransportCompanyComplianceRemindersCommand request,
        Guid configurationId,
        CancellationToken cancellationToken)
    {
        var userPreference = await _userPreferenceRepository.GetByUserIdAsync(request.TransportCompanyId, cancellationToken)
                           ?? new UserPreference(request.TransportCompanyId);

        // Store compliance reminder configuration in user preferences
        userPreference.UpdatePreference("ComplianceReminders", new
        {
            ConfigurationId = configurationId,
            IsActive = request.IsActive,
            PreferredChannels = request.PreferredChannels,
            TimeZone = request.TimeZone,
            PreferredDeliveryTime = request.PreferredDeliveryTime,
            GroupSimilarReminders = request.GroupSimilarReminders,
            MaxRemindersPerDay = request.MaxRemindersPerDay,
            ReminderConfigurations = request.ReminderConfigurations,
            ConfiguredAt = DateTime.UtcNow
        });

        await _userPreferenceRepository.UpdateAsync(userPreference, cancellationToken);
    }
}

/// <summary>
/// Interface for scheduled job service (to be implemented with Hangfire or similar)
/// </summary>
public interface IScheduledJobService
{
    Task<Guid> ScheduleRecurringJobAsync(
        string jobId,
        string cronExpression,
        Dictionary<string, object> jobData,
        CancellationToken cancellationToken = default);

    Task<bool> RemoveRecurringJobAsync(string jobId, CancellationToken cancellationToken = default);
    
    Task<bool> UpdateRecurringJobAsync(
        string jobId,
        string cronExpression,
        Dictionary<string, object> jobData,
        CancellationToken cancellationToken = default);
}
