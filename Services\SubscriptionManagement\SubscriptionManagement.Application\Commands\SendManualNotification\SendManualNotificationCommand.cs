using MediatR;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Application.Commands.SendManualNotification
{
    public class SendManualNotificationCommand : IRequest<SendManualNotificationResponse>
    {
        public Guid? SubscriptionId { get; set; }
        public Guid? UserId { get; set; }
        public NotificationType NotificationType { get; set; }
        public string? CustomMessage { get; set; }
        public List<string> Channels { get; set; } = new();
        public DateTime? ScheduledAt { get; set; }
        public Guid TriggeredByUserId { get; set; }
        public string Language { get; set; } = "en";
        public Dictionary<string, string> AdditionalVariables { get; set; } = new();
    }

    public class SendManualNotificationResponse
    {
        public Guid NotificationId { get; set; }
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public List<string> Channels { get; set; } = new();
        public DateTime? ScheduledAt { get; set; }
        public List<string> Errors { get; set; } = new();
    }
}
