# Subscription Services - Quick Start Guide

## 🚀 5-Minute Quick Start

### Prerequisites Check
```bash
dotnet --version  # Requires .NET 8+
psql --version    # Requires PostgreSQL
```

### 1. Database Setup (2 minutes)
```bash
# Navigate to subscription service
cd Services/SubscriptionManagement

# Run automated setup
./setup.ps1 -RunService
```

### 2. Manual Setup (if script fails)
```bash
# Create database
psql -h localhost -U timescale -d postgres -c "CREATE DATABASE TLI_SubscriptionManagement;"

# Run setup script
psql -h localhost -U timescale -d TLI_SubscriptionManagement -f database-setup.sql

# Start service
cd SubscriptionManagement.API
dotnet run
```

### 3. Verify Setup
```bash
# Check service health
curl http://localhost:5003/health

# View available plans
curl http://localhost:5003/api/plans

# Open Swagger UI
# Browser: http://localhost:5003
```

## 🔧 Essential Configuration

### appsettings.json
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Port=5432;Database=TLI_SubscriptionManagement;User Id=timescale;Password=timescale"
  },
  "JwtSettings": {
    "Secret": "your-super-secret-key-that-is-at-least-32-characters-long",
    "Issuer": "TLI-Identity-Service",
    "Audience": "TLI-Services"
  },
  "RazorPay": {
    "KeyId": "your_razorpay_key_id",
    "KeySecret": "your_razorpay_key_secret"
  }
}
```

## 📡 Key API Endpoints

### Public Endpoints
```bash
GET /api/plans                           # Get all public plans
GET /api/plans/{id}                      # Get specific plan
GET /api/plans/by-user-type/{userType}   # Get plans by user type
GET /health                              # Health check
```

### Authenticated Endpoints (Require JWT)
```bash
POST /api/subscriptions                  # Create subscription
GET /api/subscriptions/my-subscription   # Get user's subscription
POST /api/subscriptions/{id}/cancel      # Cancel subscription
POST /api/subscriptions/{id}/upgrade     # Upgrade subscription
GET /api/subscriptions/{id}/usage        # Get usage statistics
```

## 🔐 Authentication Flow

### 1. Get JWT Token
```bash
curl -X POST "http://localhost:5001/api/identity/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password"
  }'
```

### 2. Use Token in Requests
```bash
curl -X GET "http://localhost:5003/api/subscriptions/my-subscription" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 📊 Sample Plans Available

### Transport Company
- **Basic**: ₹999/month, 10 RFQs
- **Pro**: ₹2,999/month, Unlimited RFQs
- **Enterprise**: ₹9,999/month, Custom features

### Broker
- **Basic**: ₹1,499/month, 20 RFQs
- **Pro**: ₹4,999/month, Unlimited RFQs
- **Enterprise**: ₹14,999/month, Advanced features

### Carrier
- **Basic**: ₹799/month, 15 RFQs, 2 vehicles
- **Pro**: ₹2,499/month, Unlimited RFQs, 10 vehicles
- **Enterprise**: ₹7,999/month, Unlimited fleet

## 🔄 Common Workflows

### Create Subscription
```bash
# 1. Get available plans
curl http://localhost:5003/api/plans/by-user-type/TransportCompany

# 2. Create subscription with plan ID
curl -X POST "http://localhost:5003/api/subscriptions" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "planId": "PLAN_GUID_FROM_STEP_1",
    "autoRenew": true
  }'
```

### Check Subscription Status
```bash
curl -X GET "http://localhost:5003/api/subscriptions/my-subscription" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Upgrade Subscription
```bash
curl -X POST "http://localhost:5003/api/subscriptions/{id}/upgrade" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "newPlanId": "HIGHER_TIER_PLAN_ID"
  }'
```

## 🐛 Troubleshooting

### Service Won't Start
```bash
# Check .NET version
dotnet --version

# Check database connection
psql -h localhost -U timescale -d TLI_SubscriptionManagement -c "SELECT 1;"

# Check port availability
netstat -an | findstr :5003
```

### Database Issues
```bash
# Recreate database
psql -h localhost -U timescale -d postgres -c "DROP DATABASE IF EXISTS TLI_SubscriptionManagement;"
psql -h localhost -U timescale -d postgres -c "CREATE DATABASE TLI_SubscriptionManagement;"
psql -h localhost -U timescale -d TLI_SubscriptionManagement -f database-setup.sql
```

### JWT Authentication Issues
- Verify JWT secret matches Identity Service
- Check token expiration (default: 60 minutes)
- Ensure proper `Authorization: Bearer TOKEN` header format

## 🔗 Service Dependencies

### Required Services
1. **PostgreSQL** (Database)
2. **Identity Service** (Port 5001) - For authentication
3. **User Management** (Port 5002) - For user data

### Optional Services
1. **API Gateway** (Port 5000) - For routing
2. **RabbitMQ** - For messaging
3. **Redis** - For caching

## 📈 Monitoring

### Health Checks
- Service: `http://localhost:5003/health`
- Database: Check connection in health endpoint response

### Key Metrics
- Subscription creation rate
- Payment success/failure rates
- API response times
- Active subscriptions count

## 🚀 Production Deployment

### Environment Variables
```bash
ConnectionStrings__DefaultConnection=your_production_db_connection
JwtSettings__Secret=your_production_jwt_secret
RazorPay__KeyId=your_production_razorpay_key
RazorPay__KeySecret=your_production_razorpay_secret
```

### Docker Deployment
```bash
# Build image
docker build -t subscription-management .

# Run container
docker run -p 5003:80 \
  -e ConnectionStrings__DefaultConnection="your_connection_string" \
  -e JwtSettings__Secret="your_jwt_secret" \
  subscription-management
```

## 📚 Additional Resources

- **Full Workflow**: See `SUBSCRIPTION_SERVICES_WORKFLOW.md`
- **API Documentation**: `http://localhost:5003` (Swagger UI)
- **Setup Guide**: `Services/SubscriptionManagement/SETUP.md`
- **Architecture**: `Services/SubscriptionManagement/README.md`

---

**Need Help?** Check the health endpoints, review logs, and ensure all dependencies are running.
