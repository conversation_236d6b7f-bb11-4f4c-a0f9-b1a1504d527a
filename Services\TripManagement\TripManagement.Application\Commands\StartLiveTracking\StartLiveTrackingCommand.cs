using MediatR;

namespace TripManagement.Application.Commands.StartLiveTracking;

public record StartLiveTrackingCommand : IRequest<StartLiveTrackingResponse>
{
    public Guid TripId { get; init; }
    public Guid DriverId { get; init; }
    public int TrackingIntervalSeconds { get; init; } = 30;
    public bool EnableGeofencing { get; init; } = true;
    public bool EnableETAUpdates { get; init; } = true;
    public bool EnableSpeedMonitoring { get; init; } = true;
    public bool EnableRouteDeviation { get; init; } = true;
    public List<string> NotificationChannels { get; init; } = new();
}

public record StartLiveTrackingResponse
{
    public bool Success { get; init; }
    public string Message { get; init; } = string.Empty;
    public Guid? TrackingSessionId { get; init; }
    public string? SignalRConnectionUrl { get; init; }
    public List<GeofenceZoneDto> ActiveGeofences { get; init; } = new();
    public TrackingConfigurationDto Configuration { get; init; } = new();
    public DateTime StartedAt { get; init; }
}

public record GeofenceZoneDto
{
    public Guid Id { get; init; }
    public string Name { get; init; } = string.Empty;
    public string ZoneType { get; init; } = string.Empty;
    public LocationDto CenterLocation { get; init; } = new();
    public double RadiusMeters { get; init; }
    public bool IsActive { get; init; }
}

public record TrackingConfigurationDto
{
    public int TrackingIntervalSeconds { get; init; }
    public bool GeofencingEnabled { get; init; }
    public bool ETAUpdatesEnabled { get; init; }
    public bool SpeedMonitoringEnabled { get; init; }
    public bool RouteDeviationEnabled { get; init; }
    public double MaxSpeedKmh { get; init; }
    public double RouteDeviationThresholdMeters { get; init; }
}

public record LocationDto
{
    public double Latitude { get; init; }
    public double Longitude { get; init; }
    public double? Altitude { get; init; }
}
