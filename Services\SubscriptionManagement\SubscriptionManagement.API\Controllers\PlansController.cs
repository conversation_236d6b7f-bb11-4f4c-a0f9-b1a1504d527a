using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SubscriptionManagement.Application.Commands.CreatePlan;
using SubscriptionManagement.Application.Commands.AddPlanFeature;
using SubscriptionManagement.Application.DTOs;
using SubscriptionManagement.Application.Queries.GetPublicPlans;
using SubscriptionManagement.Application.Queries.GetPlan;
using SubscriptionManagement.Application.Queries.GetPlansByUserType;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.API.Controllers
{
    public class PlansController : BaseController
    {
        /// <summary>
        /// Get all public plans
        /// </summary>
        [HttpGet]
        [ProducesResponseType(typeof(List<PlanSummaryDto>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetPublicPlans([FromQuery] UserType? userType = null)
        {
            try
            {
                var query = new GetPublicPlansQuery(userType);
                var plans = await Mediator.Send(query);
                return Ok(plans);
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get plan by ID
        /// </summary>
        [HttpGet("{id}")]
        [ProducesResponseType(typeof(PlanDto), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetPlan(Guid id)
        {
            try
            {
                var query = new GetPlanQuery(id);
                var plan = await Mediator.Send(query);

                if (plan == null)
                {
                    return NotFound($"Plan with ID {id} not found");
                }

                return Ok(plan);
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get plans by user type
        /// </summary>
        [HttpGet("by-user-type/{userType}")]
        [ProducesResponseType(typeof(List<PlanSummaryDto>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetPlansByUserType(UserType userType)
        {
            try
            {
                var query = new GetPlansByUserTypeQuery(userType);
                var plans = await Mediator.Send(query);
                return Ok(plans);
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Create a new plan (Admin only)
        /// </summary>
        [HttpPost]
        [Authorize(Roles = "Admin")]
        [ProducesResponseType(typeof(Guid), StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> CreatePlan([FromBody] CreatePlanDto request)
        {
            try
            {
                var command = new CreatePlanCommand
                {
                    Name = request.Name,
                    Description = request.Description,
                    Type = request.Type,
                    UserType = request.UserType,
                    Price = request.Price,
                    Currency = request.Currency,
                    BillingInterval = request.BillingInterval,
                    BillingIntervalCount = request.BillingIntervalCount,
                    CustomBillingDays = request.CustomBillingDays,
                    IsPublic = request.IsPublic,
                    TrialPeriodDays = request.TrialPeriodDays,
                    SetupFee = request.SetupFee,
                    SetupFeeCurrency = request.SetupFeeCurrency,
                    Limits = request.Limits,
                    Features = request.Features
                };

                var planId = await Mediator.Send(command);
                return CreatedAtAction(nameof(GetPlan), new { id = planId }, planId);
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Update plan (Admin only)
        /// </summary>
        [HttpPut("{id}")]
        [Authorize(Roles = "Admin")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> UpdatePlan(Guid id, [FromBody] UpdatePlanDto request)
        {
            try
            {
                // TODO: Implement UpdatePlanCommand
                return Ok(new { message = "Update plan endpoint - to be implemented", id, request });
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Activate/Deactivate plan (Admin only)
        /// </summary>
        [HttpPatch("{id}/status")]
        [Authorize(Roles = "Admin")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> UpdatePlanStatus(Guid id, [FromBody] bool isActive)
        {
            try
            {
                // TODO: Implement UpdatePlanStatusCommand
                return Ok(new { message = "Update plan status endpoint - to be implemented", id, isActive });
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get all plans (Admin only)
        /// </summary>
        [HttpGet("admin/all")]
        [Authorize(Roles = "Admin")]
        [ProducesResponseType(typeof(List<PlanDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetAllPlans([FromQuery] int page = 1, [FromQuery] int pageSize = 10)
        {
            try
            {
                // TODO: Implement GetAllPlansQuery
                return Ok(new { message = "Get all plans endpoint - to be implemented", page, pageSize });
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Add feature to plan (Admin only)
        /// </summary>
        [HttpPost("{id}/features")]
        [Authorize(Roles = "Admin")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> AddPlanFeature(Guid id, [FromBody] CreatePlanFeatureDto request)
        {
            try
            {
                var command = new AddPlanFeatureCommand(
                    id,
                    request.FeatureType,
                    request.AccessType,
                    request.LimitValue,
                    request.Description);

                var result = await Mediator.Send(command);

                if (result)
                {
                    return Ok(new { message = "Feature added to plan successfully" });
                }

                return BadRequest(new { message = "Failed to add feature to plan" });
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Update plan feature (Admin only)
        /// </summary>
        [HttpPut("{id}/features/{featureType}")]
        [Authorize(Roles = "Admin")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> UpdatePlanFeature(Guid id, FeatureType featureType, [FromBody] UpdatePlanFeatureDto request)
        {
            try
            {
                // TODO: Implement UpdatePlanFeatureCommand
                return Ok(new { message = "Update plan feature endpoint - to be implemented", id, featureType, request });
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Remove feature from plan (Admin only)
        /// </summary>
        [HttpDelete("{id}/features/{featureType}")]
        [Authorize(Roles = "Admin")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> RemovePlanFeature(Guid id, FeatureType featureType)
        {
            try
            {
                // TODO: Implement RemovePlanFeatureCommand
                return Ok(new { message = "Remove plan feature endpoint - to be implemented", id, featureType });
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }
    }
}
