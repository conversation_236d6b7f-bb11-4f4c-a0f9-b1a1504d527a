using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Entities;
using OrderManagement.Domain.ValueObjects;
using OrderManagement.Domain.Exceptions;
using OrderManagement.Domain.Enums;
using Shared.Domain.Common;

namespace OrderManagement.Application.Commands.UpdateInvoice;

public class UpdateInvoiceCommandHandler : IRequestHandler<UpdateInvoiceCommand, bool>
{
    private readonly IInvoiceRepository _invoiceRepository;
    private readonly IOrderRepository _orderRepository;
    private readonly IMessageBroker _messageBroker;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<UpdateInvoiceCommandHandler> _logger;

    public UpdateInvoiceCommandHandler(
        IInvoiceRepository invoiceRepository,
        IOrderRepository orderRepository,
        IMessageBroker messageBroker,
        IUnitOfWork unitOfWork,
        ILogger<UpdateInvoiceCommandHandler> logger)
    {
        _invoiceRepository = invoiceRepository;
        _orderRepository = orderRepository;
        _messageBroker = messageBroker;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<bool> Handle(UpdateInvoiceCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Updating invoice {InvoiceId} by user {UpdatedBy}",
            request.InvoiceId, request.UpdatedBy);

        var invoice = await _invoiceRepository.GetByIdAsync(request.InvoiceId, cancellationToken);
        if (invoice == null)
        {
            _logger.LogWarning("Invoice {InvoiceId} not found", request.InvoiceId);
            throw new OrderManagementException("Invoice not found");
        }

        // Get the associated order for authorization
        var order = await _orderRepository.GetByIdAsync(invoice.OrderId, cancellationToken);
        if (order == null)
        {
            _logger.LogWarning("Order {OrderId} not found for invoice {InvoiceId}",
                invoice.OrderId, request.InvoiceId);
            throw new OrderManagementException("Associated order not found");
        }

        // Authorization check - only transport company or broker can update invoices
        if (order.TransportCompanyId != request.UpdatedBy && order.BrokerId != request.UpdatedBy)
        {
            _logger.LogWarning("User {UpdatedBy} is not authorized to update invoice {InvoiceId}",
                request.UpdatedBy, request.InvoiceId);
            throw new OrderManagementException("Not authorized to update this invoice");
        }

        // Business rule validation - only draft invoices can be updated (except for status changes)
        if (invoice.Status != InvoiceStatus.Draft && request.LineItems != null)
        {
            _logger.LogWarning("Cannot update line items for invoice {InvoiceId} with status {Status}",
                request.InvoiceId, invoice.Status);
            throw new OrderManagementException($"Cannot update line items for invoice with status {invoice.Status}");
        }

        try
        {
            var hasChanges = false;

            // Update basic properties if provided
            if (!string.IsNullOrWhiteSpace(request.Title))
            {
                // Note: The current Invoice entity doesn't have a Title property
                // This would need to be added to the domain model if required
                hasChanges = true;
            }

            if (!string.IsNullOrWhiteSpace(request.Description))
            {
                // Note: The current Invoice entity doesn't have a Description property
                // This would need to be added to the domain model if required
                hasChanges = true;
            }

            if (request.DueDate.HasValue && request.DueDate.Value != invoice.DueDate)
            {
                // Note: The current Invoice entity doesn't have a public setter for DueDate
                // This would need to be added to the domain model if required
                hasChanges = true;
            }

            if (!string.IsNullOrWhiteSpace(request.Notes) && request.Notes != invoice.Notes)
            {
                // Note: The current Invoice entity doesn't have a public setter for Notes
                // This would need to be added to the domain model if required
                hasChanges = true;
            }

            // Handle status changes
            if (request.Status.HasValue && request.Status.Value != invoice.Status)
            {
                switch (request.Status.Value)
                {
                    case InvoiceStatus.Sent:
                        if (invoice.Status == InvoiceStatus.Draft)
                        {
                            invoice.Send();
                            hasChanges = true;
                        }
                        else
                        {
                            throw new OrderManagementException($"Cannot send invoice with status {invoice.Status}");
                        }
                        break;

                    case InvoiceStatus.Cancelled:
                        invoice.Cancel();
                        hasChanges = true;
                        break;

                    default:
                        throw new OrderManagementException($"Status update to {request.Status.Value} is not supported");
                }
            }

            // Handle line items updates (only for draft invoices)
            if (request.LineItems != null && invoice.Status == InvoiceStatus.Draft)
            {
                // Note: The current Invoice entity doesn't support removing line items
                // This would need to be enhanced in the domain model

                // For now, we can only add new line items
                foreach (var lineItemDto in request.LineItems.Where(li => !li.Id.HasValue && !li.IsDeleted))
                {
                    var unitPrice = new Money(lineItemDto.UnitPrice, lineItemDto.Currency);
                    var lineItem = new InvoiceLineItem(
                        lineItemDto.Description,
                        lineItemDto.Quantity,
                        unitPrice);

                    invoice.AddLineItem(lineItem);
                    hasChanges = true;
                }
            }

            if (hasChanges)
            {
                // Update repository
                await _invoiceRepository.UpdateAsync(invoice, cancellationToken);
                await _unitOfWork.SaveChangesAsync(cancellationToken);

                // Publish integration event
                await _messageBroker.PublishAsync("invoice.updated", new
                {
                    InvoiceId = invoice.Id,
                    InvoiceNumber = invoice.InvoiceNumber,
                    OrderId = invoice.OrderId,
                    Status = invoice.Status.ToString(),
                    UpdatedBy = request.UpdatedBy,
                    UpdatedAt = DateTime.UtcNow
                }, cancellationToken);

                _logger.LogInformation("Successfully updated invoice {InvoiceId}", request.InvoiceId);
            }
            else
            {
                _logger.LogInformation("No changes detected for invoice {InvoiceId}", request.InvoiceId);
            }

            return true;
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Failed to update invoice {InvoiceId}: {Message}",
                request.InvoiceId, ex.Message);
            throw new OrderManagementException(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating invoice {InvoiceId}", request.InvoiceId);
            throw;
        }
    }
}

