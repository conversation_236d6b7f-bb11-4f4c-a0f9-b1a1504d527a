# PowerShell script to start all microservices
Write-Host "Starting TLI Microservices..." -ForegroundColor Green

# Function to start a service in a new terminal
function Start-ServiceInNewTerminal {
    param(
        [string]$ServiceName,
        [string]$ProjectPath,
        [int]$Port
    )
    
    Write-Host "Starting $ServiceName on port $Port..." -ForegroundColor Yellow
    
    # Start the service in a new PowerShell window
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$ProjectPath'; Write-Host 'Starting $ServiceName...' -ForegroundColor Green; dotnet run"
}

# Check if infrastructure is running
Write-Host "Checking infrastructure services..." -ForegroundColor Yellow

$infraRunning = $true
try {
    docker ps --filter "name=tli-postgres" --filter "status=running" --quiet | Out-Null
    if (-not $?) { $infraRunning = $false }
    
    docker ps --filter "name=tli-rabbitmq" --filter "status=running" --quiet | Out-Null
    if (-not $?) { $infraRunning = $false }
    
    docker ps --filter "name=tli-redis" --filter "status=running" --quiet | Out-Null
    if (-not $?) { $infraRunning = $false }
} catch {
    $infraRunning = $false
}

if (-not $infraRunning) {
    Write-Host "Infrastructure services are not running. Starting them first..." -ForegroundColor Yellow
    & "$PSScriptRoot\start-infrastructure.ps1"
    Start-Sleep -Seconds 15
}

# Get the solution root directory
$solutionRoot = Split-Path -Parent $PSScriptRoot

# Start Identity Service
Start-ServiceInNewTerminal -ServiceName "Identity API" -ProjectPath "$solutionRoot\Identity\Identity.API" -Port 5001

# Wait a bit for Identity service to start
Start-Sleep -Seconds 5

# Start API Gateway
Start-ServiceInNewTerminal -ServiceName "API Gateway" -ProjectPath "$solutionRoot\ApiGateway" -Port 5000

Write-Host "`nAll services are starting up!" -ForegroundColor Green
Write-Host "API Gateway: http://localhost:5000" -ForegroundColor Cyan
Write-Host "Identity API: http://localhost:5001" -ForegroundColor Cyan
Write-Host "`nSwagger Documentation:" -ForegroundColor Yellow
Write-Host "API Gateway: http://localhost:5000/swagger" -ForegroundColor Cyan
Write-Host "Identity API: http://localhost:5001/swagger" -ForegroundColor Cyan
