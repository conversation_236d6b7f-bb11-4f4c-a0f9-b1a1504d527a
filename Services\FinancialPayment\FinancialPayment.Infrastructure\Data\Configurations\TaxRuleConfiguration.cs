using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Infrastructure.Data.Configurations;

public class TaxRuleConfiguration : IEntityTypeConfiguration<TaxRule>
{
    public void Configure(EntityTypeBuilder<TaxRule> builder)
    {
        builder.ToTable("TaxRules");

        builder.HasKey(tr => tr.Id);

        builder.Property(tr => tr.Name)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(tr => tr.Description)
            .HasMaxLength(1000);

        builder.Property(tr => tr.Jurisdiction)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(tr => tr.TaxType)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(tr => tr.Rate)
            .HasPrecision(5, 4);

        // Configure Money value objects
        builder.OwnsOne(tr => tr.MinimumAmount, money =>
        {
            money.Property(m => m.Amount)
                .HasColumnName("MinimumAmount")
                .HasPrecision(18, 2);
            money.Property(m => m.Currency)
                .HasColumnName("MinimumAmountCurrency")
                .HasMaxLength(3);
        });

        builder.OwnsOne(tr => tr.MaximumAmount, money =>
        {
            money.Property(m => m.Amount)
                .HasColumnName("MaximumAmount")
                .HasPrecision(18, 2);
            money.Property(m => m.Currency)
                .HasColumnName("MaximumAmountCurrency")
                .HasMaxLength(3);
        });

        builder.Property(tr => tr.IsActive)
            .IsRequired();

        builder.Property(tr => tr.EffectiveFrom)
            .IsRequired();

        builder.Property(tr => tr.EffectiveTo);

        builder.Property(tr => tr.ProductCategory)
            .HasMaxLength(100);

        builder.Property(tr => tr.ServiceType)
            .HasMaxLength(100);

        builder.Property(tr => tr.CalculationType)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(tr => tr.Priority)
            .IsRequired();

        // Configure relationships
        builder.HasMany(tr => tr.Conditions)
            .WithOne()
            .HasForeignKey(trc => trc.TaxRuleId)
            .OnDelete(DeleteBehavior.Cascade);

        // Configure indexes
        builder.HasIndex(tr => tr.Jurisdiction);
        builder.HasIndex(tr => new { tr.Jurisdiction, tr.IsActive });
        builder.HasIndex(tr => new { tr.EffectiveFrom, tr.EffectiveTo });
    }
}

public class TaxRuleConditionConfiguration : IEntityTypeConfiguration<TaxRuleCondition>
{
    public void Configure(EntityTypeBuilder<TaxRuleCondition> builder)
    {
        builder.ToTable("TaxRuleConditions");

        builder.HasKey(trc => trc.Id);

        builder.Property(trc => trc.TaxRuleId)
            .IsRequired();

        builder.Property(trc => trc.Field)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(trc => trc.Operator)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(trc => trc.Value)
            .IsRequired()
            .HasMaxLength(500);

        builder.Property(trc => trc.Description)
            .HasMaxLength(500);

        // Configure indexes
        builder.HasIndex(trc => trc.TaxRuleId);
        builder.HasIndex(trc => new { trc.Field, trc.Operator });
    }
}

public class TaxCalculationConfiguration : IEntityTypeConfiguration<TaxCalculation>
{
    public void Configure(EntityTypeBuilder<TaxCalculation> builder)
    {
        builder.ToTable("TaxCalculations");

        builder.HasKey(tc => tc.Id);

        builder.Property(tc => tc.OrderId)
            .IsRequired();

        builder.Property(tc => tc.UserId);

        // Configure Money value objects
        builder.OwnsOne(tc => tc.SubtotalAmount, money =>
        {
            money.Property(m => m.Amount)
                .HasColumnName("SubtotalAmount")
                .HasPrecision(18, 2);
            money.Property(m => m.Currency)
                .HasColumnName("SubtotalCurrency")
                .HasMaxLength(3);
        });

        builder.OwnsOne(tc => tc.TotalTaxAmount, money =>
        {
            money.Property(m => m.Amount)
                .HasColumnName("TotalTaxAmount")
                .HasPrecision(18, 2);
            money.Property(m => m.Currency)
                .HasColumnName("TotalTaxCurrency")
                .HasMaxLength(3);
        });

        builder.OwnsOne(tc => tc.TotalAmount, money =>
        {
            money.Property(m => m.Amount)
                .HasColumnName("TotalAmount")
                .HasPrecision(18, 2);
            money.Property(m => m.Currency)
                .HasColumnName("TotalCurrency")
                .HasMaxLength(3);
        });

        builder.Property(tc => tc.Jurisdiction)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(tc => tc.CalculatedAt)
            .IsRequired();

        builder.Property(tc => tc.ProductCategory)
            .HasMaxLength(100);

        builder.Property(tc => tc.ServiceType)
            .HasMaxLength(100);

        builder.Property(tc => tc.Status)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(tc => tc.Notes)
            .HasMaxLength(1000);

        // Configure relationships
        builder.HasMany(tc => tc.TaxLines)
            .WithOne()
            .HasForeignKey(tcl => tcl.TaxCalculationId)
            .OnDelete(DeleteBehavior.Cascade);

        // Configure indexes
        builder.HasIndex(tc => tc.OrderId);
        builder.HasIndex(tc => tc.UserId);
        builder.HasIndex(tc => new { tc.Jurisdiction, tc.CalculatedAt });
        builder.HasIndex(tc => tc.Status);
    }
}

public class TaxCalculationLineConfiguration : IEntityTypeConfiguration<TaxCalculationLine>
{
    public void Configure(EntityTypeBuilder<TaxCalculationLine> builder)
    {
        builder.ToTable("TaxCalculationLines");

        builder.HasKey(tcl => tcl.Id);

        builder.Property(tcl => tcl.TaxCalculationId)
            .IsRequired();

        builder.Property(tcl => tcl.TaxRuleId)
            .IsRequired();

        builder.Property(tcl => tcl.TaxName)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(tcl => tcl.TaxType)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(tcl => tcl.TaxRate)
            .HasPrecision(5, 4);

        // Configure Money value object
        builder.OwnsOne(tcl => tcl.TaxAmount, money =>
        {
            money.Property(m => m.Amount)
                .HasColumnName("TaxAmount")
                .HasPrecision(18, 2);
            money.Property(m => m.Currency)
                .HasColumnName("TaxCurrency")
                .HasMaxLength(3);
        });

        builder.Property(tcl => tcl.Description)
            .HasMaxLength(500);

        // Configure indexes
        builder.HasIndex(tcl => tcl.TaxCalculationId);
        builder.HasIndex(tcl => tcl.TaxRuleId);
    }
}
