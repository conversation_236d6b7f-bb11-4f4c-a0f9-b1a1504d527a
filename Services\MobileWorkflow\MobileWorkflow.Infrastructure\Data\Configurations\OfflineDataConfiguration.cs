using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using MobileWorkflow.Domain.Entities;

namespace MobileWorkflow.Infrastructure.Data.Configurations;

public class OfflineDataConfiguration : IEntityTypeConfiguration<OfflineData>
{
    public void Configure(EntityTypeBuilder<OfflineData> builder)
    {
        builder.ToTable("offline_data");

        builder.Has<PERSON>ey(e => e.Id);

        builder.Property(e => e.Id)
            .HasColumnName("id")
            .ValueGeneratedOnAdd();

        builder.Property(e => e.UserId)
            .HasColumnName("user_id")
            .IsRequired();

        builder.Property(e => e.MobileSessionId)
            .HasColumnName("mobile_session_id")
            .IsRequired();

        builder.Property(e => e.MobileAppId)
            .HasColumnName("mobile_app_id");

        builder.Property(e => e.DataType)
            .HasColumnName("data_type")
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(e => e.Action)
            .HasColumnName("action")
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(e => e.CreatedOffline)
            .HasColumnName("created_offline")
            .IsRequired();

        builder.Property(e => e.SyncedAt)
            .HasColumnName("synced_at");

        builder.Property(e => e.IsSynced)
            .HasColumnName("is_synced")
            .IsRequired();

        builder.Property(e => e.SyncError)
            .HasColumnName("sync_error")
            .HasMaxLength(1000);

        builder.Property(e => e.SyncAttempts)
            .HasColumnName("sync_attempts")
            .IsRequired();

        builder.Property(e => e.Priority)
            .HasColumnName("priority")
            .IsRequired();

        builder.Property(e => e.ConflictResolution)
            .HasColumnName("conflict_resolution")
            .HasMaxLength(100);

        builder.Property(e => e.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(e => e.UpdatedAt)
            .HasColumnName("updated_at")
            .IsRequired();

        // Navigation properties
        builder.HasOne(e => e.MobileSession)
            .WithMany(s => s.OfflineData)
            .HasForeignKey(e => e.MobileSessionId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(e => e.MobileApp)
            .WithMany(a => a.OfflineData)
            .HasForeignKey(e => e.MobileAppId)
            .OnDelete(DeleteBehavior.SetNull);
    }
}
