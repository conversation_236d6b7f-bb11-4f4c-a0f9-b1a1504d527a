using MediatR;

namespace TripManagement.Application.Commands.ReassignDriver;

public record ReassignDriverCommand : IRequest<bool>
{
    public Guid TripId { get; init; }
    public Guid NewDriverId { get; init; }
    public Guid? NewVehicleId { get; init; }
    public string Reason { get; init; } = string.Empty;
    public Guid ReassignedBy { get; init; }
    public string? AdditionalNotes { get; init; }
    public bool NotifyStakeholders { get; init; } = true;
}
