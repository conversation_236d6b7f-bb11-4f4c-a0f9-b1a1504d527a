using MediatR;
using Microsoft.Extensions.Logging;
using TripManagement.Domain.Repositories;
using TripManagement.Domain.Enums;

namespace TripManagement.Application.Queries.GetEnhancedDriverDashboard;

public class GetEnhancedDriverDashboardQueryHandler : IRequestHandler<GetEnhancedDriverDashboardQuery, GetEnhancedDriverDashboardResponse>
{
    private readonly IDriverRepository _driverRepository;
    private readonly ITripRepository _tripRepository;
    private readonly ILogger<GetEnhancedDriverDashboardQueryHandler> _logger;

    public GetEnhancedDriverDashboardQueryHandler(
        IDriverRepository driverRepository,
        ITripRepository tripRepository,
        ILogger<GetEnhancedDriverDashboardQueryHandler> logger)
    {
        _driverRepository = driverRepository;
        _tripRepository = tripRepository;
        _logger = logger;
    }

    public async Task<GetEnhancedDriverDashboardResponse> Handle(GetEnhancedDriverDashboardQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Getting enhanced dashboard for driver {DriverId}", request.DriverId);

            // Get driver
            var driver = await _driverRepository.GetByIdAsync(request.DriverId, cancellationToken);
            if (driver == null)
            {
                return new GetEnhancedDriverDashboardResponse
                {
                    IsSuccess = false,
                    ErrorMessage = "Driver not found"
                };
            }

            // Build dashboard
            var dashboard = new EnhancedDriverDashboardDto
            {
                DriverId = driver.Id,
                DriverName = driver.FullName,
                Status = driver.Status.ToString(),
                LastActiveAt = driver.LastActiveAt ?? DateTime.UtcNow
            };

            // Get current trip
            dashboard.CurrentTrip = await GetCurrentTripInfo(request.DriverId, cancellationToken);

            // Get rating statistics
            if (request.IncludeRatingStats)
            {
                dashboard.RatingStats = await GetRatingStatistics(request.DriverId, cancellationToken);
            }

            // Get recent completed trips
            if (request.IncludeCompletedTrips)
            {
                dashboard.RecentCompletedTrips = await GetRecentCompletedTrips(request.DriverId, request.RecentTripsCount, cancellationToken);
            }

            // Get recent feedback
            if (request.IncludeFeedback)
            {
                dashboard.RecentFeedback = await GetRecentFeedback(request.DriverId, request.RecentFeedbackCount, cancellationToken);
            }

            // Get map shortcuts
            if (request.IncludeMapShortcuts)
            {
                dashboard.MapShortcuts = await GetMapShortcuts(request.DriverId, driver.CurrentLocation, cancellationToken);
            }

            // Get performance metrics
            if (request.IncludePerformanceMetrics)
            {
                dashboard.PerformanceMetrics = await GetPerformanceMetrics(request.DriverId, request.PerformanceDays, cancellationToken);
            }

            // Get pending actions
            dashboard.PendingActions = await GetPendingActions(request.DriverId, cancellationToken);

            // Get quick stats
            dashboard.QuickStats = await GetQuickStats(request.DriverId, cancellationToken);

            // Get earnings summary
            dashboard.Earnings = await GetEarningsSummary(request.DriverId, cancellationToken);

            return new GetEnhancedDriverDashboardResponse
            {
                IsSuccess = true,
                Dashboard = dashboard
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting enhanced dashboard for driver {DriverId}", request.DriverId);
            return new GetEnhancedDriverDashboardResponse
            {
                IsSuccess = false,
                ErrorMessage = ex.Message
            };
        }
    }

    private async Task<CurrentTripDashboardInfo?> GetCurrentTripInfo(Guid driverId, CancellationToken cancellationToken)
    {
        try
        {
            var activeTrips = await _tripRepository.GetActiveByDriverIdAsync(driverId, cancellationToken);
            var currentTrip = activeTrips.FirstOrDefault(t => t.Status == TripStatus.InProgress);

            if (currentTrip == null) return null;

            return new CurrentTripDashboardInfo
            {
                TripId = currentTrip.Id,
                TripNumber = currentTrip.TripNumber,
                Status = currentTrip.Status.ToString(),
                Origin = currentTrip.OriginAddress,
                Destination = currentTrip.DestinationAddress,
                StartTime = currentTrip.StartTime ?? DateTime.UtcNow,
                EstimatedEndTime = currentTrip.EstimatedEndTime,
                ProgressPercentage = CalculateTripProgress(currentTrip),
                CurrentMilestone = "In Transit", // Mock - would come from milestone tracking
                NextMilestone = "Delivery",
                Navigation = BuildNavigationInfo(currentTrip),
                AvailableActions = GetAvailableActions(currentTrip)
            };
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error getting current trip info for driver {DriverId}", driverId);
            return null;
        }
    }

    private async Task<DriverRatingStatistics> GetRatingStatistics(Guid driverId, CancellationToken cancellationToken)
    {
        // Mock implementation - would integrate with analytics service
        return new DriverRatingStatistics
        {
            AverageRating = 4.7,
            TotalRatings = 156,
            RatingDistribution = new Dictionary<int, int>
            {
                { 5, 89 }, { 4, 45 }, { 3, 15 }, { 2, 5 }, { 1, 2 }
            },
            RecentRating = 4.8,
            RecentRatingCount = 23,
            RatingTrend = 0.2, // Positive trend
            RatingGrade = "A",
            RatingHighlights = new List<string> { "Professional", "Punctual", "Helpful" }
        };
    }

    private async Task<List<CompletedTripSummary>> GetRecentCompletedTrips(Guid driverId, int count, CancellationToken cancellationToken)
    {
        // Mock implementation - would query completed trips
        var trips = new List<CompletedTripSummary>();
        for (int i = 0; i < count; i++)
        {
            trips.Add(new CompletedTripSummary
            {
                TripId = Guid.NewGuid(),
                TripNumber = $"TRP{1000 + i}",
                CompletedAt = DateTime.UtcNow.AddDays(-i - 1),
                Origin = $"Origin {i + 1}",
                Destination = $"Destination {i + 1}",
                Distance = 150 + (i * 25),
                Earnings = 250 + (i * 50),
                Rating = 4.5 + (i * 0.1),
                CustomerFeedback = i % 2 == 0 ? "Great service!" : null,
                Duration = TimeSpan.FromHours(3 + i),
                OnTime = i % 3 != 0
            });
        }
        return trips;
    }

    private async Task<List<FeedbackSummary>> GetRecentFeedback(Guid driverId, int count, CancellationToken cancellationToken)
    {
        // Mock implementation - would query feedback data
        var feedback = new List<FeedbackSummary>();
        var feedbackTypes = new[] { "Positive", "Neutral", "Negative" };
        var tags = new[] { "Professional", "Punctual", "Helpful", "Courteous", "Safe" };

        for (int i = 0; i < count; i++)
        {
            feedback.Add(new FeedbackSummary
            {
                FeedbackId = Guid.NewGuid(),
                TripId = Guid.NewGuid(),
                TripNumber = $"TRP{2000 + i}",
                Rating = 4.0 + (i * 0.3),
                Comments = i % 2 == 0 ? "Excellent driver, very professional!" : null,
                SubmittedAt = DateTime.UtcNow.AddDays(-i - 1),
                CustomerName = $"Customer {i + 1}",
                FeedbackType = feedbackTypes[i % feedbackTypes.Length],
                Tags = tags.Take(2 + (i % 3)).ToList()
            });
        }
        return feedback;
    }

    private async Task<List<MapShortcut>> GetMapShortcuts(Guid driverId, Domain.ValueObjects.Location? currentLocation, CancellationToken cancellationToken)
    {
        var shortcuts = new List<MapShortcut>
        {
            new MapShortcut
            {
                Name = "Home",
                Description = "Driver's home location",
                Latitude = 40.7128,
                Longitude = -74.0060,
                Address = "123 Home St, City, State",
                ShortcutType = "Home",
                NavigationUrl = "https://maps.google.com/?q=40.7128,-74.0060",
                IsFavorite = true
            },
            new MapShortcut
            {
                Name = "Main Depot",
                Description = "Primary loading depot",
                Latitude = 40.7589,
                Longitude = -73.9851,
                Address = "456 Depot Ave, City, State",
                ShortcutType = "Depot",
                NavigationUrl = "https://maps.google.com/?q=40.7589,-73.9851",
                IsFavorite = true
            },
            new MapShortcut
            {
                Name = "Fuel Station",
                Description = "Preferred fuel station",
                Latitude = 40.7505,
                Longitude = -73.9934,
                Address = "789 Fuel St, City, State",
                ShortcutType = "FuelStation",
                NavigationUrl = "https://maps.google.com/?q=40.7505,-73.9934",
                IsFavorite = false
            }
        };

        // Calculate distances if current location is available
        if (currentLocation != null)
        {
            foreach (var shortcut in shortcuts)
            {
                var distance = CalculateDistance(
                    currentLocation.Latitude, currentLocation.Longitude,
                    shortcut.Latitude, shortcut.Longitude);
                shortcut.DistanceFromCurrentKm = distance;
                shortcut.EstimatedTimeMinutes = (int)(distance / 0.8); // Rough estimate: 0.8 km/min average speed
            }
        }

        return shortcuts;
    }

    private async Task<PerformanceMetricsSummary> GetPerformanceMetrics(Guid driverId, int days, CancellationToken cancellationToken)
    {
        // Mock implementation - would integrate with analytics service
        return new PerformanceMetricsSummary
        {
            OnTimePerformance = 94.5,
            SafetyScore = 98.2,
            FuelEfficiency = 12.5,
            CustomerSatisfaction = 4.7,
            TotalTripsCompleted = 156,
            TotalMilesdriven = 15600,
            TotalDrivingTime = TimeSpan.FromHours(780),
            PerformanceGrade = "A",
            Alerts = new List<PerformanceAlert>(),
            MonthlyTrends = new Dictionary<string, double>
            {
                { "OnTime", 2.1 }, { "Safety", 1.5 }, { "Satisfaction", 0.8 }
            }
        };
    }

    private async Task<List<PendingAction>> GetPendingActions(Guid driverId, CancellationToken cancellationToken)
    {
        // Mock implementation - would query various services for pending actions
        return new List<PendingAction>
        {
            new PendingAction
            {
                ActionType = "DocumentRenewal",
                Title = "License Renewal Due",
                Description = "Your driving license expires in 45 days",
                Priority = "Medium",
                DueDate = DateTime.UtcNow.AddDays(45),
                IsOverdue = false,
                ActionUrl = "/documents/renew/license"
            }
        };
    }

    private async Task<DashboardQuickStats> GetQuickStats(Guid driverId, CancellationToken cancellationToken)
    {
        // Mock implementation - would aggregate from various sources
        return new DashboardQuickStats
        {
            TripsToday = 3,
            TripsThisWeek = 18,
            TripsThisMonth = 72,
            EarningsToday = 450.00m,
            EarningsThisWeek = 2800.00m,
            EarningsThisMonth = 12500.00m,
            DrivingTimeToday = TimeSpan.FromHours(6.5),
            MilesDrivenToday = 285,
            PendingTrips = 2,
            DocumentsExpiringSoon = 1
        };
    }

    private async Task<EarningsSummary> GetEarningsSummary(Guid driverId, CancellationToken cancellationToken)
    {
        // Mock implementation - would integrate with financial service
        return new EarningsSummary
        {
            TotalEarnings = 45600.00m,
            EarningsToday = 450.00m,
            EarningsThisWeek = 2800.00m,
            EarningsThisMonth = 12500.00m,
            AverageEarningsPerTrip = 173.50m,
            AverageEarningsPerMile = 2.92m,
            Breakdown = new List<EarningsBreakdown>
            {
                new EarningsBreakdown { Category = "Base", Amount = 10000.00m, Percentage = 80.0 },
                new EarningsBreakdown { Category = "Bonus", Amount = 1500.00m, Percentage = 12.0 },
                new EarningsBreakdown { Category = "Tips", Amount = 1000.00m, Percentage = 8.0 }
            },
            Trend = new EarningsTrend
            {
                WeeklyGrowth = 5.2,
                MonthlyGrowth = 12.8,
                TrendDirection = "Up",
                DailyEarnings = GenerateDailyEarnings(7)
            }
        };
    }

    // Helper methods
    private double CalculateTripProgress(Domain.Entities.Trip trip)
    {
        // Mock calculation - would be based on milestones or distance
        if (trip.StartTime == null) return 0;
        
        var elapsed = DateTime.UtcNow - trip.StartTime.Value;
        var estimated = trip.EstimatedEndTime - trip.StartTime.Value;
        
        if (estimated == null || estimated.Value.TotalMinutes <= 0) return 50; // Default
        
        return Math.Min(100, (elapsed.TotalMinutes / estimated.Value.TotalMinutes) * 100);
    }

    private NavigationInfo? BuildNavigationInfo(Domain.Entities.Trip trip)
    {
        // Mock navigation info - would integrate with mapping service
        return new NavigationInfo
        {
            CurrentLatitude = 40.7128,
            CurrentLongitude = -74.0060,
            DestinationLatitude = 40.7589,
            DestinationLongitude = -73.9851,
            RemainingDistanceKm = 25.5,
            EstimatedTimeMinutes = 35,
            NavigationUrl = "https://maps.google.com/directions?api=1&destination=40.7589,-73.9851",
            RouteAlerts = new List<string> { "Traffic ahead", "Construction zone" }
        };
    }

    private List<string> GetAvailableActions(Domain.Entities.Trip trip)
    {
        var actions = new List<string>();
        
        switch (trip.Status)
        {
            case TripStatus.Assigned:
                actions.AddRange(new[] { "Start Trip", "View Details", "Contact Customer" });
                break;
            case TripStatus.InProgress:
                actions.AddRange(new[] { "Update Status", "Complete Milestone", "Emergency Alert", "Navigation" });
                break;
            case TripStatus.Completed:
                actions.AddRange(new[] { "View Summary", "Upload POD" });
                break;
        }
        
        return actions;
    }

    private double CalculateDistance(double lat1, double lon1, double lat2, double lon2)
    {
        // Haversine formula for distance calculation
        const double R = 6371; // Earth's radius in kilometers
        var dLat = ToRadians(lat2 - lat1);
        var dLon = ToRadians(lon2 - lon1);
        var a = Math.Sin(dLat / 2) * Math.Sin(dLat / 2) +
                Math.Cos(ToRadians(lat1)) * Math.Cos(ToRadians(lat2)) *
                Math.Sin(dLon / 2) * Math.Sin(dLon / 2);
        var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));
        return R * c;
    }

    private double ToRadians(double degrees) => degrees * Math.PI / 180;

    private List<DailyEarnings> GenerateDailyEarnings(int days)
    {
        var earnings = new List<DailyEarnings>();
        var random = new Random();
        
        for (int i = days - 1; i >= 0; i--)
        {
            earnings.Add(new DailyEarnings
            {
                Date = DateTime.UtcNow.AddDays(-i).Date,
                Amount = 300 + (decimal)(random.NextDouble() * 200),
                TripCount = 2 + random.Next(4)
            });
        }
        
        return earnings;
    }
}
