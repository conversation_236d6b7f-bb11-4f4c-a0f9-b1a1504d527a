using Shared.Domain.Common;
using DataStorage.Domain.Enums;

namespace DataStorage.Domain.Events;

// Document lifecycle events
public record DocumentUploadedEvent(
    Guid DocumentId, 
    string Title, 
    DocumentType DocumentType, 
    Guid OwnerId, 
    string OwnerType) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record DocumentUpdatedEvent(
    Guid DocumentId, 
    string Title, 
    Guid OwnerId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record DocumentStatusChangedEvent(
    Guid DocumentId, 
    DocumentStatus PreviousStatus, 
    DocumentStatus NewStatus, 
    string? Reason) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record DocumentArchivedEvent(
    Guid DocumentId, 
    string Title, 
    Guid OwnerId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record DocumentDeletedEvent(
    Guid DocumentId, 
    string Title, 
    Guid OwnerId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// Access control events
public record DocumentAccessGrantedEvent(
    Guid DocumentId, 
    Guid UserId, 
    string UserType, 
    AccessLevel AccessLevel) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record DocumentAccessRevokedEvent(
    Guid DocumentId, 
    Guid UserId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// Version control events
public record DocumentVersionCreatedEvent(
    Guid DocumentId, 
    Guid VersionId, 
    int VersionNumber, 
    Guid CreatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// Storage events
public record DocumentStorageLocationChangedEvent(
    Guid DocumentId, 
    string OldLocation, 
    string NewLocation) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// Compliance events
public record DocumentRetentionPolicyAppliedEvent(
    Guid DocumentId, 
    string PolicyName, 
    DateTime RetentionUntil) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record DocumentComplianceViolationDetectedEvent(
    Guid DocumentId, 
    string ViolationType, 
    string Description) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}
