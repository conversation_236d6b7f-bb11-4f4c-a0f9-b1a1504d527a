using Microsoft.Extensions.Logging;
using MonitoringObservability.Application.DTOs;
using MonitoringObservability.Domain.Entities;
using MonitoringObservability.Domain.Enums;
using MonitoringObservability.Domain.Interfaces;
using System.Collections.Concurrent;

namespace MonitoringObservability.Infrastructure.Services;

/// <summary>
/// Service for capacity planning, resource forecasting, and scaling recommendations
/// </summary>
public class CapacityPlanningService : ICapacityPlanningService
{
    private readonly ICapacityPlanRepository _capacityPlanRepository;
    private readonly IMetricRepository _metricRepository;
    private readonly IAlertRepository _alertRepository;
    private readonly INotificationService _notificationService;
    private readonly ILogger<CapacityPlanningService> _logger;
    
    // Cache for active plans and forecasting models
    private readonly ConcurrentDictionary<Guid, CapacityPlan> _activePlans = new();
    private readonly ConcurrentDictionary<string, List<double>> _historicalData = new();

    public CapacityPlanningService(
        ICapacityPlanRepository capacityPlanRepository,
        IMetricRepository metricRepository,
        IAlertRepository alertRepository,
        INotificationService notificationService,
        ILogger<CapacityPlanningService> logger)
    {
        _capacityPlanRepository = capacityPlanRepository ?? throw new ArgumentNullException(nameof(capacityPlanRepository));
        _metricRepository = metricRepository ?? throw new ArgumentNullException(nameof(metricRepository));
        _alertRepository = alertRepository ?? throw new ArgumentNullException(nameof(alertRepository));
        _notificationService = notificationService ?? throw new ArgumentNullException(nameof(notificationService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task InitializeAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Initializing capacity planning service");

            // Load active capacity plans
            var activePlans = await _capacityPlanRepository.GetActivePlansAsync(cancellationToken);
            foreach (var plan in activePlans)
            {
                _activePlans.TryAdd(plan.Id, plan);
            }

            _logger.LogInformation("Loaded {PlanCount} active capacity plans", _activePlans.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize capacity planning service");
            throw;
        }
    }

    public async Task<CapacityPlan> CreateCapacityPlanAsync(string name, string description, string serviceName,
        CapacityPlanType planType, TimeSpan forecastHorizon, Guid ownerId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating capacity plan {PlanName} for service {ServiceName}", name, serviceName);

            var plan = new CapacityPlan(name, description, serviceName, planType, forecastHorizon, ownerId);
            
            await _capacityPlanRepository.AddAsync(plan, cancellationToken);
            
            // Add to active cache
            _activePlans.TryAdd(plan.Id, plan);
            
            _logger.LogInformation("Created capacity plan {PlanId}", plan.Id);
            return plan;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create capacity plan {PlanName}", name);
            throw;
        }
    }

    public async Task<List<ResourceForecast>> GenerateForecastsAsync(Guid planId, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var plan = await _capacityPlanRepository.GetByIdAsync(planId, cancellationToken);
            if (plan == null)
                throw new InvalidOperationException($"Capacity plan {planId} not found");

            _logger.LogInformation("Generating forecasts for capacity plan {PlanId}", planId);

            var forecasts = new List<ResourceForecast>();
            var resourceTypes = new[] { ResourceType.CPU, ResourceType.Memory, ResourceType.Storage, ResourceType.Network };

            foreach (var resourceType in resourceTypes)
            {
                var forecast = await GenerateResourceForecastAsync(plan, resourceType, cancellationToken);
                if (forecast != null)
                {
                    plan.AddForecast(forecast);
                    forecasts.Add(forecast);
                }
            }

            plan.Execute();
            await _capacityPlanRepository.UpdateAsync(plan, cancellationToken);

            _logger.LogInformation("Generated {ForecastCount} forecasts for plan {PlanId}", forecasts.Count, planId);
            return forecasts;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate forecasts for plan {PlanId}", planId);
            throw;
        }
    }

    public async Task<List<ScalingRecommendation>> GenerateScalingRecommendationsAsync(Guid planId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var plan = await _capacityPlanRepository.GetByIdAsync(planId, cancellationToken);
            if (plan == null)
                throw new InvalidOperationException($"Capacity plan {planId} not found");

            _logger.LogInformation("Generating scaling recommendations for plan {PlanId}", planId);

            var recommendations = new List<ScalingRecommendation>();

            // Analyze current resource utilization
            var utilizationData = await GetCurrentResourceUtilizationAsync(plan.ServiceName, cancellationToken);

            foreach (var (resourceType, utilization) in utilizationData)
            {
                var recommendation = await AnalyzeResourceAndGenerateRecommendationAsync(
                    plan, resourceType, utilization, cancellationToken);
                
                if (recommendation != null)
                {
                    plan.AddRecommendation(recommendation);
                    recommendations.Add(recommendation);
                }
            }

            await _capacityPlanRepository.UpdateAsync(plan, cancellationToken);

            _logger.LogInformation("Generated {RecommendationCount} scaling recommendations for plan {PlanId}",
                recommendations.Count, planId);
            return recommendations;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate scaling recommendations for plan {PlanId}", planId);
            throw;
        }
    }

    public async Task<CapacityAnalysisDto> AnalyzeCapacityAsync(string serviceName, TimeSpan analysisWindow,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Analyzing capacity for service {ServiceName} over {AnalysisWindow}",
                serviceName, analysisWindow);

            var fromDate = DateTime.UtcNow.Subtract(analysisWindow);
            var metrics = await _metricRepository.GetMetricsByServiceAsync(serviceName, fromDate, cancellationToken);

            var analysis = new CapacityAnalysisDto
            {
                ServiceName = serviceName,
                AnalysisWindow = analysisWindow,
                AnalyzedAt = DateTime.UtcNow,
                ResourceUtilization = new Dictionary<ResourceType, ResourceUtilizationDto>(),
                TrendAnalysis = new Dictionary<ResourceType, TrendAnalysisDto>(),
                Recommendations = new List<string>()
            };

            // Analyze each resource type
            var resourceTypes = new[] { ResourceType.CPU, ResourceType.Memory, ResourceType.Storage, ResourceType.Network };
            
            foreach (var resourceType in resourceTypes)
            {
                var resourceMetrics = GetMetricsForResourceType(metrics, resourceType);
                if (resourceMetrics.Any())
                {
                    var utilization = AnalyzeResourceUtilization(resourceMetrics);
                    var trend = AnalyzeTrend(resourceMetrics);
                    
                    analysis.ResourceUtilization[resourceType] = utilization;
                    analysis.TrendAnalysis[resourceType] = trend;
                    
                    // Generate recommendations based on analysis
                    var recommendations = GenerateRecommendationsFromAnalysis(resourceType, utilization, trend);
                    analysis.Recommendations.AddRange(recommendations);
                }
            }

            return analysis;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to analyze capacity for service {ServiceName}", serviceName);
            throw;
        }
    }

    public async Task<CapacityAlert> CreateCapacityAlertAsync(Guid planId, ResourceType resourceType, 
        string resourceName, CapacityAlertType alertType, double threshold, double currentValue,
        CapacityAlertSeverity severity, string message, CancellationToken cancellationToken = default)
    {
        try
        {
            var plan = await _capacityPlanRepository.GetByIdAsync(planId, cancellationToken);
            if (plan == null)
                throw new InvalidOperationException($"Capacity plan {planId} not found");

            var alert = new CapacityAlert(planId, resourceType, resourceName, alertType, 
                threshold, currentValue, severity, message);
            
            plan.AddAlert(alert);
            await _capacityPlanRepository.UpdateAsync(plan, cancellationToken);

            // Send notification for high/critical severity alerts
            if (severity >= CapacityAlertSeverity.High)
            {
                await SendCapacityAlertNotificationAsync(plan, alert, cancellationToken);
            }

            _logger.LogWarning("Capacity alert created: {Message}", message);
            return alert;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create capacity alert for plan {PlanId}", planId);
            throw;
        }
    }

    public async Task<CapacityDashboardDto> GetCapacityDashboardAsync(string? serviceName = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var plans = string.IsNullOrEmpty(serviceName)
                ? await _capacityPlanRepository.GetActivePlansAsync(cancellationToken)
                : await _capacityPlanRepository.GetPlansByServiceAsync(serviceName, cancellationToken);

            var dashboard = new CapacityDashboardDto
            {
                ServiceName = serviceName,
                TotalPlans = plans.Count(),
                ActiveAlerts = plans.Sum(p => p.ActiveAlertCount),
                PendingRecommendations = plans.Sum(p => p.PendingRecommendationCount),
                PlanStatuses = new List<CapacityPlanStatusDto>(),
                ResourceUtilization = new Dictionary<ResourceType, double>(),
                GeneratedAt = DateTime.UtcNow
            };

            // Calculate overall resource utilization
            if (!string.IsNullOrEmpty(serviceName))
            {
                var utilizationData = await GetCurrentResourceUtilizationAsync(serviceName, cancellationToken);
                dashboard.ResourceUtilization = utilizationData;
            }

            // Add plan statuses
            foreach (var plan in plans.Take(20)) // Limit for performance
            {
                dashboard.PlanStatuses.Add(new CapacityPlanStatusDto
                {
                    PlanId = plan.Id,
                    PlanName = plan.Name,
                    ServiceName = plan.ServiceName,
                    Status = plan.Status,
                    LastExecuted = plan.LastExecuted,
                    NextExecution = plan.NextExecution,
                    ActiveAlerts = plan.ActiveAlertCount,
                    PendingRecommendations = plan.PendingRecommendationCount,
                    Accuracy = plan.Accuracy
                });
            }

            return dashboard;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get capacity dashboard");
            throw;
        }
    }

    public async Task ExecuteAllPlansAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Executing all active capacity plans");

            var executionTasks = _activePlans.Values
                .Where(plan => ShouldExecutePlan(plan))
                .Select(plan => ExecutePlanAsync(plan, cancellationToken));

            await Task.WhenAll(executionTasks);

            _logger.LogInformation("Completed execution of capacity plans");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during capacity plan execution");
            throw;
        }
    }

    private async Task<ResourceForecast?> GenerateResourceForecastAsync(CapacityPlan plan, ResourceType resourceType,
        CancellationToken cancellationToken)
    {
        try
        {
            // Get historical data for the resource
            var historicalData = await GetHistoricalResourceDataAsync(plan.ServiceName, resourceType, 
                plan.ForecastHorizon, cancellationToken);

            if (historicalData.Count < 10) // Need minimum data points
            {
                _logger.LogWarning("Insufficient historical data for {ResourceType} forecast in service {ServiceName}",
                    resourceType, plan.ServiceName);
                return null;
            }

            // Generate forecast based on configured model
            var forecastModel = GetForecastModelFromConfiguration(plan.Configuration);
            var (predictedValue, confidence) = GenerateForecast(historicalData, forecastModel);

            var forecastDate = DateTime.UtcNow.Add(plan.ForecastHorizon);
            var resourceName = $"{plan.ServiceName}_{resourceType}";

            return new ResourceForecast(plan.Id, resourceType, resourceName, forecastDate, 
                predictedValue, confidence, forecastModel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating forecast for {ResourceType} in plan {PlanId}", 
                resourceType, plan.Id);
            return null;
        }
    }

    private async Task<ScalingRecommendation?> AnalyzeResourceAndGenerateRecommendationAsync(
        CapacityPlan plan, ResourceType resourceType, double currentUtilization, CancellationToken cancellationToken)
    {
        try
        {
            var resourceName = $"{plan.ServiceName}_{resourceType}";
            
            // Define thresholds for scaling decisions
            const double scaleUpThreshold = 80.0;   // 80% utilization
            const double scaleDownThreshold = 20.0; // 20% utilization
            const double criticalThreshold = 95.0;  // 95% utilization

            if (currentUtilization >= criticalThreshold)
            {
                return new ScalingRecommendation(plan.Id, resourceType, resourceName, ScalingAction.ScaleUp,
                    currentUtilization * 1.5, // Recommend 50% increase
                    $"Critical utilization detected ({currentUtilization:F1}%). Immediate scaling required.",
                    RecommendationPriority.Critical);
            }
            else if (currentUtilization >= scaleUpThreshold)
            {
                return new ScalingRecommendation(plan.Id, resourceType, resourceName, ScalingAction.ScaleUp,
                    currentUtilization * 1.2, // Recommend 20% increase
                    $"High utilization detected ({currentUtilization:F1}%). Consider scaling up.",
                    RecommendationPriority.High);
            }
            else if (currentUtilization <= scaleDownThreshold)
            {
                return new ScalingRecommendation(plan.Id, resourceType, resourceName, ScalingAction.ScaleDown,
                    currentUtilization * 0.8, // Recommend 20% decrease
                    $"Low utilization detected ({currentUtilization:F1}%). Consider scaling down to optimize costs.",
                    RecommendationPriority.Low);
            }

            return null; // No recommendation needed
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing resource {ResourceType} for plan {PlanId}", 
                resourceType, plan.Id);
            return null;
        }
    }

    private async Task<Dictionary<ResourceType, double>> GetCurrentResourceUtilizationAsync(string serviceName,
        CancellationToken cancellationToken)
    {
        var utilization = new Dictionary<ResourceType, double>();
        var fromDate = DateTime.UtcNow.AddHours(-1); // Last hour data

        var metrics = await _metricRepository.GetMetricsByServiceAsync(serviceName, fromDate, cancellationToken);

        // Calculate average utilization for each resource type
        utilization[ResourceType.CPU] = CalculateAverageUtilization(GetMetricsForResourceType(metrics, ResourceType.CPU));
        utilization[ResourceType.Memory] = CalculateAverageUtilization(GetMetricsForResourceType(metrics, ResourceType.Memory));
        utilization[ResourceType.Storage] = CalculateAverageUtilization(GetMetricsForResourceType(metrics, ResourceType.Storage));
        utilization[ResourceType.Network] = CalculateAverageUtilization(GetMetricsForResourceType(metrics, ResourceType.Network));

        return utilization;
    }

    private async Task<List<double>> GetHistoricalResourceDataAsync(string serviceName, ResourceType resourceType,
        TimeSpan period, CancellationToken cancellationToken)
    {
        var fromDate = DateTime.UtcNow.Subtract(period);
        var metrics = await _metricRepository.GetMetricsByServiceAsync(serviceName, fromDate, cancellationToken);
        
        return GetMetricsForResourceType(metrics, resourceType)
            .OrderBy(m => m.Timestamp)
            .Select(m => m.Value)
            .ToList();
    }

    private List<Metric> GetMetricsForResourceType(IEnumerable<Metric> metrics, ResourceType resourceType)
    {
        var metricNames = resourceType switch
        {
            ResourceType.CPU => new[] { "cpu_usage", "cpu_utilization", "processor_time" },
            ResourceType.Memory => new[] { "memory_usage", "memory_utilization", "available_memory" },
            ResourceType.Storage => new[] { "disk_usage", "storage_utilization", "disk_space" },
            ResourceType.Network => new[] { "network_usage", "bandwidth_utilization", "network_io" },
            _ => Array.Empty<string>()
        };

        return metrics.Where(m => metricNames.Contains(m.Name.ToLowerInvariant())).ToList();
    }

    private double CalculateAverageUtilization(List<Metric> metrics)
    {
        return metrics.Any() ? metrics.Average(m => m.Value) : 0.0;
    }

    private ForecastModel GetForecastModelFromConfiguration(Dictionary<string, object> configuration)
    {
        if (configuration.TryGetValue("forecast_model", out var modelValue) &&
            Enum.TryParse<ForecastModel>(modelValue.ToString(), out var model))
        {
            return model;
        }
        return ForecastModel.Linear; // Default
    }

    private (double predictedValue, double confidence) GenerateForecast(List<double> historicalData, ForecastModel model)
    {
        return model switch
        {
            ForecastModel.Linear => GenerateLinearForecast(historicalData),
            ForecastModel.Exponential => GenerateExponentialForecast(historicalData),
            ForecastModel.Seasonal => GenerateSeasonalForecast(historicalData),
            _ => GenerateLinearForecast(historicalData)
        };
    }

    private (double predictedValue, double confidence) GenerateLinearForecast(List<double> data)
    {
        if (data.Count < 2)
            return (data.LastOrDefault(), 0.5);

        // Simple linear regression
        var n = data.Count;
        var sumX = Enumerable.Range(0, n).Sum();
        var sumY = data.Sum();
        var sumXY = data.Select((y, x) => x * y).Sum();
        var sumX2 = Enumerable.Range(0, n).Sum(x => x * x);

        var slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
        var intercept = (sumY - slope * sumX) / n;

        var predictedValue = slope * n + intercept;
        var confidence = 0.8; // Simplified confidence calculation

        return (Math.Max(0, predictedValue), confidence);
    }

    private (double predictedValue, double confidence) GenerateExponentialForecast(List<double> data)
    {
        // Simplified exponential smoothing
        var alpha = 0.3; // Smoothing factor
        var forecast = data.First();
        
        foreach (var value in data.Skip(1))
        {
            forecast = alpha * value + (1 - alpha) * forecast;
        }

        return (Math.Max(0, forecast), 0.7);
    }

    private (double predictedValue, double confidence) GenerateSeasonalForecast(List<double> data)
    {
        // Simplified seasonal forecast (using weekly pattern)
        var seasonLength = Math.Min(7, data.Count / 4);
        if (seasonLength < 2)
            return GenerateLinearForecast(data);

        var recentSeason = data.TakeLast(seasonLength).Average();
        return (Math.Max(0, recentSeason), 0.6);
    }

    private ResourceUtilizationDto AnalyzeResourceUtilization(List<Metric> metrics)
    {
        if (!metrics.Any())
            return new ResourceUtilizationDto();

        var values = metrics.Select(m => m.Value).ToList();
        
        return new ResourceUtilizationDto
        {
            Current = values.LastOrDefault(),
            Average = values.Average(),
            Maximum = values.Max(),
            Minimum = values.Min(),
            Percentile95 = CalculatePercentile(values, 95),
            Percentile99 = CalculatePercentile(values, 99)
        };
    }

    private TrendAnalysisDto AnalyzeTrend(List<Metric> metrics)
    {
        if (metrics.Count < 2)
            return new TrendAnalysisDto { Direction = TrendDirection.Stable };

        var values = metrics.OrderBy(m => m.Timestamp).Select(m => m.Value).ToList();
        var firstHalf = values.Take(values.Count / 2).Average();
        var secondHalf = values.Skip(values.Count / 2).Average();
        
        var changePercentage = ((secondHalf - firstHalf) / firstHalf) * 100;
        
        var direction = changePercentage switch
        {
            > 10 => TrendDirection.Increasing,
            < -10 => TrendDirection.Decreasing,
            _ => TrendDirection.Stable
        };

        return new TrendAnalysisDto
        {
            Direction = direction,
            ChangePercentage = Math.Abs(changePercentage),
            Slope = (secondHalf - firstHalf) / (values.Count / 2)
        };
    }

    private double CalculatePercentile(List<double> values, double percentile)
    {
        var sorted = values.OrderBy(v => v).ToList();
        var index = (int)Math.Ceiling(percentile / 100.0 * sorted.Count) - 1;
        index = Math.Max(0, Math.Min(index, sorted.Count - 1));
        return sorted[index];
    }

    private List<string> GenerateRecommendationsFromAnalysis(ResourceType resourceType, 
        ResourceUtilizationDto utilization, TrendAnalysisDto trend)
    {
        var recommendations = new List<string>();

        if (utilization.Current > 80)
        {
            recommendations.Add($"{resourceType} utilization is high ({utilization.Current:F1}%). Consider scaling up.");
        }

        if (trend.Direction == TrendDirection.Increasing && trend.ChangePercentage > 20)
        {
            recommendations.Add($"{resourceType} usage is trending upward ({trend.ChangePercentage:F1}% increase). Plan for capacity expansion.");
        }

        if (utilization.Current < 20 && trend.Direction == TrendDirection.Decreasing)
        {
            recommendations.Add($"{resourceType} utilization is low and decreasing. Consider scaling down to optimize costs.");
        }

        return recommendations;
    }

    private bool ShouldExecutePlan(CapacityPlan plan)
    {
        if (!plan.IsActive || !plan.NextExecution.HasValue)
            return false;

        return DateTime.UtcNow >= plan.NextExecution.Value;
    }

    private async Task ExecutePlanAsync(CapacityPlan plan, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Executing capacity plan {PlanId}", plan.Id);

            // Generate forecasts
            await GenerateForecastsAsync(plan.Id, cancellationToken);

            // Generate recommendations
            await GenerateScalingRecommendationsAsync(plan.Id, cancellationToken);

            // Check for capacity alerts
            await CheckCapacityThresholdsAsync(plan, cancellationToken);

            _logger.LogDebug("Completed execution of capacity plan {PlanId}", plan.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing capacity plan {PlanId}", plan.Id);
        }
    }

    private async Task CheckCapacityThresholdsAsync(CapacityPlan plan, CancellationToken cancellationToken)
    {
        var utilizationData = await GetCurrentResourceUtilizationAsync(plan.ServiceName, cancellationToken);

        foreach (var (resourceType, utilization) in utilizationData)
        {
            if (utilization > 90) // Critical threshold
            {
                await CreateCapacityAlertAsync(plan.Id, resourceType, $"{plan.ServiceName}_{resourceType}",
                    CapacityAlertType.Threshold, 90, utilization, CapacityAlertSeverity.Critical,
                    $"Critical {resourceType} utilization: {utilization:F1}%", cancellationToken);
            }
            else if (utilization > 80) // Warning threshold
            {
                await CreateCapacityAlertAsync(plan.Id, resourceType, $"{plan.ServiceName}_{resourceType}",
                    CapacityAlertType.Threshold, 80, utilization, CapacityAlertSeverity.High,
                    $"High {resourceType} utilization: {utilization:F1}%", cancellationToken);
            }
        }
    }

    private async Task SendCapacityAlertNotificationAsync(CapacityPlan plan, CapacityAlert alert,
        CancellationToken cancellationToken)
    {
        try
        {
            var channel = alert.Severity switch
            {
                CapacityAlertSeverity.Critical => NotificationChannel.SMS,
                CapacityAlertSeverity.High => NotificationChannel.Email,
                _ => NotificationChannel.InApp
            };

            var message = $"Capacity Alert: {plan.Name}\n" +
                         $"Resource: {alert.ResourceName}\n" +
                         $"Current: {alert.CurrentValue:F1}%, Threshold: {alert.Threshold:F1}%\n" +
                         $"Severity: {alert.Severity}";

            // In a real implementation, you would get recipients from plan configuration
            var recipients = new[] { "<EMAIL>" };

            foreach (var recipient in recipients)
            {
                await _notificationService.SendNotificationAsync(channel, recipient,
                    "Capacity Alert", message, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send capacity alert notification for plan {PlanId}", plan.Id);
        }
    }
}
