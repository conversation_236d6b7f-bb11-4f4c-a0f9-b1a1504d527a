using FluentAssertions;
using Identity.Application.Auth.Commands.RegisterUser;
using Identity.Domain.Entities;
using FluentValidation.TestHelper;

namespace Identity.Tests;

public class RegisterUserCommandValidatorTests
{
    private readonly RegisterUserCommandValidator _validator;

    public RegisterUserCommandValidatorTests()
    {
        _validator = new RegisterUserCommandValidator();
    }

    [Fact]
    public void Should_Have_Error_When_Username_Is_Empty()
    {
        // Arrange
        var command = new RegisterUserCommand { Username = "" };

        // Act & Assert
        var result = _validator.TestValidate(command);
        result.ShouldHaveValidationErrorFor(x => x.Username);
    }

    [Fact]
    public void Should_Have_Error_When_Username_Is_Too_Short()
    {
        // Arrange
        var command = new RegisterUserCommand { Username = "ab" };

        // Act & Assert
        var result = _validator.TestValidate(command);
        result.ShouldHaveValidationErrorFor(x => x.Username);
    }

    [Fact]
    public void Should_Have_Error_When_Username_Is_Too_Long()
    {
        // Arrange
        var command = new RegisterUserCommand { Username = new string('a', 51) };

        // Act & Assert
        var result = _validator.TestValidate(command);
        result.ShouldHaveValidationErrorFor(x => x.Username);
    }

    [Fact]
    public void Should_Have_Error_When_Email_Is_Empty()
    {
        // Arrange
        var command = new RegisterUserCommand { Email = "" };

        // Act & Assert
        var result = _validator.TestValidate(command);
        result.ShouldHaveValidationErrorFor(x => x.Email);
    }

    [Fact]
    public void Should_Have_Error_When_Email_Is_Invalid()
    {
        // Arrange
        var command = new RegisterUserCommand { Email = "invalid-email" };

        // Act & Assert
        var result = _validator.TestValidate(command);
        result.ShouldHaveValidationErrorFor(x => x.Email);
    }

    [Fact]
    public void Should_Have_Error_When_Password_Is_Empty()
    {
        // Arrange
        var command = new RegisterUserCommand { Password = "" };

        // Act & Assert
        var result = _validator.TestValidate(command);
        result.ShouldHaveValidationErrorFor(x => x.Password);
    }

    [Fact]
    public void Should_Have_Error_When_Password_Is_Too_Short()
    {
        // Arrange
        var command = new RegisterUserCommand { Password = "12345" };

        // Act & Assert
        var result = _validator.TestValidate(command);
        result.ShouldHaveValidationErrorFor(x => x.Password);
    }

    [Theory]
    [InlineData(UserType.Admin)]
    [InlineData(UserType.SubAdmin)]
    public void Should_Have_Error_When_UserType_Is_Admin_Or_SubAdmin(UserType userType)
    {
        // Arrange
        var command = new RegisterUserCommand
        {
            Username = "testuser",
            Email = "<EMAIL>",
            Password = "password123",
            UserType = userType
        };

        // Act & Assert
        var result = _validator.TestValidate(command);
        result.ShouldHaveValidationErrorFor(x => x.UserType)
            .WithErrorMessage("UserType must be one of: TransportCompany, Broker, Carrier, Driver, or Shipper. Admin and SubAdmin accounts cannot be created through public registration.");
    }

    [Theory]
    [InlineData(UserType.TransportCompany)]
    [InlineData(UserType.Broker)]
    [InlineData(UserType.Carrier)]
    [InlineData(UserType.Driver)]
    [InlineData(UserType.Shipper)]
    public void Should_Not_Have_Error_When_UserType_Is_Valid_For_Registration(UserType userType)
    {
        // Arrange
        var command = new RegisterUserCommand
        {
            Username = "testuser",
            Email = "<EMAIL>",
            Password = "password123",
            UserType = userType
        };

        // Act & Assert
        var result = _validator.TestValidate(command);
        result.ShouldNotHaveValidationErrorFor(x => x.UserType);
    }

    [Fact]
    public void Should_Not_Have_Error_When_All_Required_Fields_Are_Valid()
    {
        // Arrange
        var command = new RegisterUserCommand
        {
            Username = "testuser",
            Email = "<EMAIL>",
            Password = "password123",
            UserType = UserType.Shipper,
            PhoneNumber = "+1234567890",
            FirstName = "John",
            LastName = "Doe"
        };

        // Act & Assert
        var result = _validator.TestValidate(command);
        result.ShouldNotHaveAnyValidationErrors();
    }
}