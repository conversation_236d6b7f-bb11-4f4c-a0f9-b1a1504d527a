using MediatR;

namespace TripManagement.Application.Commands.VerifyRecipient;

public record VerifyRecipientCommand : IRequest<bool>
{
    public Guid TripStopId { get; init; }
    public Guid DriverId { get; init; }
    public string RecipientName { get; init; } = string.Empty;
    public string? RecipientPhone { get; init; }
    public string? RecipientEmail { get; init; }
    public string? IdentificationNumber { get; init; }
    public string? IdentificationType { get; init; } // ID Card, Passport, etc.
    public string? Notes { get; init; }
}
