using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Repositories;
using AnalyticsBIService.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Shared.Infrastructure.Repositories;
using Shared.Infrastructure.Caching;
using Shared.Infrastructure.Monitoring;

namespace AnalyticsBIService.Infrastructure.Repositories;

/// <summary>
/// Repository implementation for order data operations
/// </summary>
public class OrderDataRepository : OptimizedRepositoryBase<OrderData, Guid>, IOrderDataRepository
{
    public OrderDataRepository(
        AnalyticsBIDbContext context,
        ICachingService cachingService,
        IPerformanceMetrics performanceMetrics) : base(context, cachingService, performanceMetrics)
    {
    }

    /// <summary>
    /// Get order data by date range
    /// </summary>
    public async Task<IEnumerable<OrderData>> GetByDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        return await Context.Set<OrderData>()
            .Where(od => od.OrderDate >= startDate && od.OrderDate <= endDate)
            .OrderByDescending(od => od.OrderDate)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get order data by shipper ID
    /// </summary>
    public async Task<IEnumerable<OrderData>> GetByShipperIdAsync(Guid shipperId, CancellationToken cancellationToken = default)
    {
        return await Context.Set<OrderData>()
            .Where(od => od.ShipperId == shipperId)
            .OrderByDescending(od => od.OrderDate)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get order data by carrier ID
    /// </summary>
    public async Task<IEnumerable<OrderData>> GetByCarrierIdAsync(Guid carrierId, CancellationToken cancellationToken = default)
    {
        return await Context.Set<OrderData>()
            .Where(od => od.CarrierId == carrierId)
            .OrderByDescending(od => od.OrderDate)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get order data by status
    /// </summary>
    public async Task<IEnumerable<OrderData>> GetByStatusAsync(string status, CancellationToken cancellationToken = default)
    {
        return await Context.Set<OrderData>()
            .Where(od => od.Status == status)
            .OrderByDescending(od => od.OrderDate)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get order analytics summary
    /// </summary>
    public async Task<object> GetOrderAnalyticsSummaryAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        var orders = await Context.Set<OrderData>()
            .Where(od => od.OrderDate >= startDate && od.OrderDate <= endDate)
            .ToListAsync(cancellationToken);

        return new
        {
            TotalOrders = orders.Count,
            CompletedOrders = orders.Count(o => o.Status == "Completed"),
            TotalValue = orders.Sum(o => o.OrderValue),
            TotalShippingCost = orders.Sum(o => o.ShippingCost),
            AverageDistance = orders.Any() ? orders.Average(o => o.Distance) : 0,
            OnTimeDeliveryRate = orders.Any() ? (decimal)orders.Count(o => o.IsOnTime) / orders.Count * 100 : 0,
            AverageCustomerRating = orders.Any() ? orders.Average(o => o.CustomerRating) : 0
        };
    }
}
