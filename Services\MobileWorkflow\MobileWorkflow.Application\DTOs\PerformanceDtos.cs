namespace MobileWorkflow.Application.DTOs;

public class PerformanceReportDto
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public DateTime GeneratedAt { get; set; }
    public int TotalOperations { get; set; }
    public double AverageResponseTime { get; set; }
    public double ErrorRate { get; set; }
    public double ThroughputPerSecond { get; set; }
    public List<SlowOperationDto> TopSlowOperations { get; set; } = new();
    public Dictionary<DateTime, double> PerformanceTrends { get; set; } = new();
    public Dictionary<string, double> MetricsSummary { get; set; } = new();
}

public class SlowOperationDto
{
    public string OperationName { get; set; } = string.Empty;
    public double AverageTime { get; set; }
    public double MaxTime { get; set; }
    public double MinTime { get; set; }
    public int Count { get; set; }
    public double ErrorRate { get; set; }
    public List<string> OptimizationSuggestions { get; set; } = new();
}



public class PerformanceAlertDto
{
    public Guid Id { get; set; }
    public string Severity { get; set; } = string.Empty; // Info, Warning, Critical
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime DetectedAt { get; set; }
    public DateTime? ResolvedAt { get; set; }
    public bool IsResolved { get; set; }
    public List<string> Alerts { get; set; } = new();
    public Dictionary<string, object> AlertData { get; set; } = new();
}

public class SystemHealthDto
{
    public DateTime CheckedAt { get; set; }
    public string OverallStatus { get; set; } = string.Empty; // Healthy, Warning, Critical, Error
    public double CpuUsagePercent { get; set; }
    public double MemoryUsageMB { get; set; }
    public double MemoryUsagePercent { get; set; }
    public double DiskUsagePercent { get; set; }
    public int ActiveConnections { get; set; }
    public double CacheHitRate { get; set; }
    public double AverageResponseTimeMs { get; set; }
    public double ErrorRatePercent { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object> AdditionalMetrics { get; set; } = new();
}

public class CacheStatisticsDto
{
    public DateTime GeneratedAt { get; set; }
    public long TotalKeys { get; set; }
    public long MemoryCacheKeys { get; set; }
    public long DistributedCacheKeys { get; set; }
    public double HitRate { get; set; }
    public double MissRate { get; set; }
    public long TotalHits { get; set; }
    public long TotalMisses { get; set; }
    public long TotalRequests { get; set; }
    public double AverageGetTime { get; set; }
    public double AverageSetTime { get; set; }
    public long MemoryUsageBytes { get; set; }
    public Dictionary<string, long> KeysByPrefix { get; set; } = new();
    public Dictionary<string, double> OperationTimes { get; set; } = new();
}

public class QueryOptimizationDto
{
    public Guid Id { get; set; }
    public string QueryName { get; set; } = string.Empty;
    public string QueryType { get; set; } = string.Empty;
    public double CurrentAverageTime { get; set; }
    public double TargetTime { get; set; }
    public int ExecutionCount { get; set; }
    public List<string> OptimizationSuggestions { get; set; } = new();
    public string Priority { get; set; } = string.Empty; // Low, Medium, High, Critical
    public DateTime AnalyzedAt { get; set; }
    public bool IsOptimized { get; set; }
    public Dictionary<string, object> QueryDetails { get; set; } = new();
}

public class PerformanceBenchmarkDto
{
    public string BenchmarkName { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public DateTime ExecutedAt { get; set; }
    public TimeSpan Duration { get; set; }
    public double OperationsPerSecond { get; set; }
    public double AverageLatency { get; set; }
    public double P95Latency { get; set; }
    public double P99Latency { get; set; }
    public long TotalOperations { get; set; }
    public long SuccessfulOperations { get; set; }
    public long FailedOperations { get; set; }
    public double SuccessRate { get; set; }
    public Dictionary<string, object> BenchmarkData { get; set; } = new();
}

public class ResourceUsageDto
{
    public DateTime Timestamp { get; set; }
    public double CpuPercent { get; set; }
    public double MemoryMB { get; set; }
    public double MemoryPercent { get; set; }
    public double DiskReadMBps { get; set; }
    public double DiskWriteMBps { get; set; }
    public double NetworkInMBps { get; set; }
    public double NetworkOutMBps { get; set; }
    public int ThreadCount { get; set; }
    public int HandleCount { get; set; }
    public long GCGen0Collections { get; set; }
    public long GCGen1Collections { get; set; }
    public long GCGen2Collections { get; set; }
}

public class PerformanceThresholdDto
{
    public string MetricName { get; set; } = string.Empty;
    public double WarningThreshold { get; set; }
    public double CriticalThreshold { get; set; }
    public string Unit { get; set; } = string.Empty;
    public string ComparisonOperator { get; set; } = "GreaterThan"; // GreaterThan, LessThan, Equals
    public bool IsEnabled { get; set; }
    public TimeSpan EvaluationWindow { get; set; }
    public int MinimumSamples { get; set; }
    public Dictionary<string, object> ThresholdConfig { get; set; } = new();
}

public class PerformanceTrendDto
{
    public string MetricName { get; set; } = string.Empty;
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string TrendDirection { get; set; } = string.Empty; // Improving, Degrading, Stable
    public double TrendSlope { get; set; }
    public double CorrelationCoefficient { get; set; }
    public List<DataPointDto> DataPoints { get; set; } = new();
    public string TrendAnalysis { get; set; } = string.Empty;
    public List<string> Recommendations { get; set; } = new();
}

public class DataPointDto
{
    public DateTime Timestamp { get; set; }
    public double Value { get; set; }
    public Dictionary<string, object> Properties { get; set; } = new();
}

public class PerformanceComparisonDto
{
    public string ComparisonName { get; set; } = string.Empty;
    public DateTime BaselinePeriodStart { get; set; }
    public DateTime BaselinePeriodEnd { get; set; }
    public DateTime ComparisonPeriodStart { get; set; }
    public DateTime ComparisonPeriodEnd { get; set; }
    public Dictionary<string, PerformanceComparisonMetricDto> Metrics { get; set; } = new();
    public string OverallTrend { get; set; } = string.Empty;
    public double OverallImprovementPercent { get; set; }
    public List<string> KeyFindings { get; set; } = new();
}

public class PerformanceComparisonMetricDto
{
    public string MetricName { get; set; } = string.Empty;
    public double BaselineValue { get; set; }
    public double ComparisonValue { get; set; }
    public double ChangePercent { get; set; }
    public string ChangeDirection { get; set; } = string.Empty; // Improved, Degraded, NoChange
    public bool IsSignificant { get; set; }
    public string Unit { get; set; } = string.Empty;
}

public class PerformanceProfileDto
{
    public Guid Id { get; set; }
    public string ProfileName { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public TimeSpan Duration { get; set; }
    public List<PerformanceProfileOperationDto> Operations { get; set; } = new();
    public Dictionary<string, double> Summary { get; set; } = new();
    public List<PerformanceBottleneckDto> Bottlenecks { get; set; } = new();
    public string ProfileType { get; set; } = string.Empty; // CPU, Memory, IO, Network
}

public class PerformanceProfileOperationDto
{
    public string OperationName { get; set; } = string.Empty;
    public TimeSpan Duration { get; set; }
    public double CpuTime { get; set; }
    public long MemoryAllocated { get; set; }
    public int CallCount { get; set; }
    public List<PerformanceProfileOperationDto> ChildOperations { get; set; } = new();
}

public class PerformanceBottleneckDto
{
    public string BottleneckType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public double ImpactScore { get; set; }
    public TimeSpan Duration { get; set; }
    public string Severity { get; set; } = string.Empty;
    public List<string> Recommendations { get; set; } = new();
    public Dictionary<string, object> Details { get; set; } = new();
}

public class ScalabilityTestDto
{
    public Guid Id { get; set; }
    public string TestName { get; set; } = string.Empty;
    public DateTime ExecutedAt { get; set; }
    public int MinConcurrentUsers { get; set; }
    public int MaxConcurrentUsers { get; set; }
    public int StepSize { get; set; }
    public TimeSpan StepDuration { get; set; }
    public List<ScalabilityTestResultDto> Results { get; set; } = new();
    public int RecommendedMaxUsers { get; set; }
    public string BottleneckIdentified { get; set; } = string.Empty;
    public List<string> ScalabilityRecommendations { get; set; } = new();
}

public class ScalabilityTestResultDto
{
    public int ConcurrentUsers { get; set; }
    public double AverageResponseTime { get; set; }
    public double ThroughputPerSecond { get; set; }
    public double ErrorRate { get; set; }
    public double CpuUsage { get; set; }
    public double MemoryUsage { get; set; }
    public bool PassedThresholds { get; set; }
}

// Request DTOs
public class CreatePerformanceBenchmarkRequest
{
    public string BenchmarkName { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public int OperationCount { get; set; }
    public TimeSpan Duration { get; set; }
    public Dictionary<string, object> BenchmarkConfig { get; set; } = new();
}

public class CreatePerformanceProfileRequest
{
    public string ProfileName { get; set; } = string.Empty;
    public string ProfileType { get; set; } = string.Empty;
    public TimeSpan Duration { get; set; }
    public List<string> OperationsToProfile { get; set; } = new();
    public Dictionary<string, object> ProfileConfig { get; set; } = new();
}

public class CreateScalabilityTestRequest
{
    public string TestName { get; set; } = string.Empty;
    public int MinConcurrentUsers { get; set; }
    public int MaxConcurrentUsers { get; set; }
    public int StepSize { get; set; }
    public TimeSpan StepDuration { get; set; }
    public Dictionary<string, object> TestConfig { get; set; } = new();
}
