using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using AuditCompliance.Application.Interfaces;
using AuditCompliance.Application.DTOs.Analytics;

namespace AuditCompliance.API.Controllers;

/// <summary>
/// Controller for advanced analytics and machine learning-based compliance features
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class AnalyticsController : ControllerBase
{
    private readonly IAdvancedAnalyticsService _analyticsService;
    private readonly ILogger<AnalyticsController> _logger;

    public AnalyticsController(
        IAdvancedAnalyticsService analyticsService,
        ILogger<AnalyticsController> logger)
    {
        _analyticsService = analyticsService;
        _logger = logger;
    }

    /// <summary>
    /// Predict compliance risk for an entity
    /// </summary>
    [HttpPost("risk-prediction")]
    [Authorize(Roles = "Admin,ComplianceOfficer,Auditor")]
    public async Task<ActionResult<ComplianceRiskPredictionDto>> PredictComplianceRisk(
        [FromBody] ComplianceRiskInputDto input)
    {
        try
        {
            var prediction = await _analyticsService.PredictComplianceRiskAsync(input);
            return Ok(prediction);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error predicting compliance risk for entity {EntityId}", input.EntityId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Predict potential compliance violations
    /// </summary>
    [HttpGet("violation-predictions/{entityId}")]
    [Authorize(Roles = "Admin,ComplianceOfficer,Auditor")]
    public async Task<ActionResult<List<ViolationPredictionDto>>> PredictViolations(
        Guid entityId,
        [FromQuery] string entityType,
        [FromQuery] DateTime? predictionPeriod = null)
    {
        try
        {
            var period = predictionPeriod ?? DateTime.UtcNow.AddDays(30);
            var predictions = await _analyticsService.PredictViolationsAsync(entityId, entityType, period);
            return Ok(predictions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error predicting violations for entity {EntityId}", entityId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Analyze compliance trends and patterns
    /// </summary>
    [HttpPost("trend-analysis")]
    [Authorize(Roles = "Admin,ComplianceOfficer,Auditor")]
    public async Task<ActionResult<ComplianceTrendAnalysisDto>> AnalyzeComplianceTrends(
        [FromBody] ComplianceTrendInputDto input)
    {
        try
        {
            var analysis = await _analyticsService.AnalyzeComplianceTrendsAsync(input);
            return Ok(analysis);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing compliance trends");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Detect compliance anomalies
    /// </summary>
    [HttpGet("anomalies")]
    [Authorize(Roles = "Admin,ComplianceOfficer,Auditor")]
    public async Task<ActionResult<List<ComplianceAnomalyDto>>> DetectComplianceAnomalies(
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] string? entityType = null)
    {
        try
        {
            var anomalies = await _analyticsService.DetectComplianceAnomaliesAsync(fromDate, toDate, entityType);
            return Ok(anomalies);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error detecting compliance anomalies");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Generate comprehensive compliance insights
    /// </summary>
    [HttpGet("insights")]
    [Authorize(Roles = "Admin,ComplianceOfficer")]
    public async Task<ActionResult<ComplianceInsightsDto>> GenerateComplianceInsights(
        [FromQuery] Guid? organizationId = null)
    {
        try
        {
            var insights = await _analyticsService.GenerateComplianceInsightsAsync(organizationId);
            return Ok(insights);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating compliance insights");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Train or retrain ML models
    /// </summary>
    [HttpPost("train-models")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<ModelTrainingResultDto>> TrainModels(
        [FromBody] List<string>? modelTypes = null)
    {
        try
        {
            var result = await _analyticsService.TrainModelsAsync(modelTypes);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error training models");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get model performance metrics
    /// </summary>
    [HttpGet("model-performance")]
    [Authorize(Roles = "Admin,ComplianceOfficer")]
    public async Task<ActionResult<List<ModelPerformanceDto>>> GetModelPerformance()
    {
        try
        {
            var performance = await _analyticsService.GetModelPerformanceAsync();
            return Ok(performance);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting model performance");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Forecast compliance scores
    /// </summary>
    [HttpGet("compliance-forecast/{entityId}")]
    [Authorize(Roles = "Admin,ComplianceOfficer,Auditor")]
    public async Task<ActionResult<List<ComplianceScoreForecastDto>>> ForecastComplianceScore(
        Guid entityId,
        [FromQuery] string entityType,
        [FromQuery] int forecastDays = 30)
    {
        try
        {
            var forecast = await _analyticsService.ForecastComplianceScoreAsync(entityId, entityType, forecastDays);
            return Ok(forecast);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error forecasting compliance score for entity {EntityId}", entityId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get analytics dashboard data
    /// </summary>
    [HttpGet("dashboard")]
    [Authorize(Roles = "Admin,ComplianceOfficer,Auditor")]
    public async Task<ActionResult<object>> GetAnalyticsDashboard(
        [FromQuery] Guid? organizationId = null,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        try
        {
            var from = fromDate ?? DateTime.UtcNow.AddDays(-30);
            var to = toDate ?? DateTime.UtcNow;

            // Get comprehensive analytics data for dashboard
            var insights = await _analyticsService.GenerateComplianceInsightsAsync(organizationId);
            var anomalies = await _analyticsService.DetectComplianceAnomaliesAsync(from, to);
            var modelPerformance = await _analyticsService.GetModelPerformanceAsync();

            var dashboardData = new
            {
                Overview = insights.Overview,
                RecentInsights = insights.Insights.Take(5),
                TopRecommendations = insights.Recommendations.Take(5),
                KeyMetrics = insights.KeyMetrics,
                RecentAnomalies = anomalies.Take(10),
                ModelStatus = modelPerformance.Select(m => new
                {
                    m.ModelType,
                    m.Accuracy,
                    m.LastTrained,
                    m.IsActive
                }),
                LastUpdated = DateTime.UtcNow
            };

            return Ok(dashboardData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting analytics dashboard data");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get compliance risk heatmap data
    /// </summary>
    [HttpGet("risk-heatmap")]
    [Authorize(Roles = "Admin,ComplianceOfficer")]
    public async Task<ActionResult<object>> GetRiskHeatmap(
        [FromQuery] Guid? organizationId = null,
        [FromQuery] string? entityType = null)
    {
        try
        {
            // This would generate risk heatmap data for visualization
            // For now, return a placeholder structure
            var heatmapData = new
            {
                OrganizationId = organizationId,
                EntityType = entityType,
                RiskLevels = new[]
                {
                    new { Level = "Critical", Count = 5, Percentage = 5.0 },
                    new { Level = "High", Count = 15, Percentage = 15.0 },
                    new { Level = "Medium", Count = 30, Percentage = 30.0 },
                    new { Level = "Low", Count = 50, Percentage = 50.0 }
                },
                RiskByStandard = new[]
                {
                    new { Standard = "GDPR", RiskScore = 0.6, EntityCount = 25 },
                    new { Standard = "SOX", RiskScore = 0.4, EntityCount = 20 },
                    new { Standard = "DataRetention", RiskScore = 0.3, EntityCount = 30 },
                    new { Standard = "AccessControl", RiskScore = 0.5, EntityCount = 35 }
                },
                GeneratedAt = DateTime.UtcNow
            };

            return Ok(heatmapData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting risk heatmap data");
            return StatusCode(500, "Internal server error");
        }
    }
}
