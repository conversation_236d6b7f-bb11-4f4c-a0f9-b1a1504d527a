using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Domain.Entities;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace CommunicationNotification.Infrastructure.Services;

/// <summary>
/// Service for communication analytics
/// </summary>
public class AnalyticsService : IAnalyticsService
{
    private readonly IMessageAnalyticsRepository _analyticsRepository;
    private readonly IDistributedCache _cache;
    private readonly ILogger<AnalyticsService> _logger;
    private readonly TimeSpan _cacheExpiration = TimeSpan.FromMinutes(15);

    public AnalyticsService(
        IMessageAnalyticsRepository analyticsRepository,
        IDistributedCache cache,
        ILogger<AnalyticsService> logger)
    {
        _analyticsRepository = analyticsRepository;
        _cache = cache;
        _logger = logger;
    }

    public async Task<MessagePerformanceMetrics> GetMessagePerformanceAsync(
        DateTime startDate,
        DateTime endDate,
        MessageType? messageType = null,
        NotificationChannel? channel = null,
        Guid? userId = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = GenerateCacheKey("message_performance", startDate, endDate, messageType, channel, userId);
            var cachedResult = await GetFromCacheAsync<MessagePerformanceMetrics>(cacheKey, cancellationToken);

            if (cachedResult != null)
            {
                _logger.LogDebug("Retrieved message performance metrics from cache");
                return cachedResult;
            }

            _logger.LogInformation("Calculating message performance metrics from {StartDate} to {EndDate}",
                startDate, endDate);

            var metrics = await _analyticsRepository.GetAggregatedMetricsAsync(
                startDate, endDate, messageType, channel, userId, cancellationToken);

            await SetCacheAsync(cacheKey, metrics, cancellationToken);

            return metrics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting message performance metrics");
            throw;
        }
    }

    public async Task<List<ChannelPerformanceMetrics>> GetChannelPerformanceAsync(
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = GenerateCacheKey("channel_performance", startDate, endDate);
            var cachedResult = await GetFromCacheAsync<List<ChannelPerformanceMetrics>>(cacheKey, cancellationToken);

            if (cachedResult != null)
            {
                _logger.LogDebug("Retrieved channel performance metrics from cache");
                return cachedResult;
            }

            _logger.LogInformation("Calculating channel performance metrics from {StartDate} to {EndDate}",
                startDate, endDate);

            var channelMetrics = new List<ChannelPerformanceMetrics>();
            var channels = Enum.GetValues<NotificationChannel>();

            foreach (var channel in channels)
            {
                var performance = await _analyticsRepository.GetAggregatedMetricsAsync(
                    startDate, endDate, null, channel, null, cancellationToken);

                if (performance.TotalMessages > 0)
                {
                    var hourlyDistribution = await GetHourlyDistributionAsync(
                        startDate, endDate, channel, cancellationToken);

                    var marketShare = await CalculateMarketShareAsync(
                        startDate, endDate, channel, cancellationToken);

                    var costEfficiency = CalculateCostEfficiency(performance);
                    var userPreference = await CalculateUserPreferenceAsync(
                        startDate, endDate, channel, cancellationToken);

                    var channelMetric = new ChannelPerformanceMetrics(
                        channel, performance, marketShare, costEfficiency, userPreference, hourlyDistribution);

                    channelMetrics.Add(channelMetric);
                }
            }

            await SetCacheAsync(cacheKey, channelMetrics, cancellationToken);

            return channelMetrics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting channel performance metrics");
            throw;
        }
    }

    public async Task<UserEngagementMetrics> GetUserEngagementAsync(
        Guid userId,
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = GenerateCacheKey("user_engagement", startDate, endDate, userId: userId);
            var cachedResult = await GetFromCacheAsync<UserEngagementMetrics>(cacheKey, cancellationToken);

            if (cachedResult != null)
            {
                _logger.LogDebug("Retrieved user engagement metrics from cache for user {UserId}", userId);
                return cachedResult;
            }

            _logger.LogInformation("Calculating user engagement metrics for user {UserId} from {StartDate} to {EndDate}",
                userId, startDate, endDate);

            var analytics = await _analyticsRepository.GetByDateRangeAsync(
                startDate, endDate, null, null, userId, cancellationToken);

            if (!analytics.Any())
            {
                return new UserEngagementMetrics(userId, 0, 0, 0, TimeSpan.Zero, NotificationChannel.Email);
            }

            var totalMessages = analytics.Count;
            var readMessages = analytics.Count(a => a.IsRead);
            var clickedMessages = analytics.Count(a => a.IsClicked);

            var responseTimes = analytics
                .Where(a => a.ResponseTime.HasValue)
                .Select(a => a.ResponseTime!.Value)
                .ToList();

            var averageResponseTime = responseTimes.Any()
                ? TimeSpan.FromTicks((long)responseTimes.Average(rt => rt.Ticks))
                : TimeSpan.Zero;

            var preferredChannel = analytics
                .GroupBy(a => a.Channel)
                .OrderByDescending(g => g.Count())
                .First().Key;

            var messageTypePreferences = analytics
                .GroupBy(a => a.MessageType)
                .ToDictionary(g => g.Key, g => g.Count());

            var lastEngagement = analytics
                .Where(a => a.IsRead || a.IsClicked)
                .Max(a => a.ReadAt ?? a.ClickedAt ?? a.SentAt);

            var engagement = new UserEngagementMetrics(
                userId, totalMessages, readMessages, clickedMessages,
                averageResponseTime, preferredChannel, messageTypePreferences, lastEngagement);

            await SetCacheAsync(cacheKey, engagement, cancellationToken);

            return engagement;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user engagement metrics for user {UserId}", userId);
            throw;
        }
    }

    public async Task<List<TimeBasedMetrics>> GetTimeBasedAnalyticsAsync(
        DateTime startDate,
        DateTime endDate,
        TimePeriodType periodType,
        MessageType? messageType = null,
        NotificationChannel? channel = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = GenerateCacheKey("time_based_analytics", startDate, endDate, messageType, channel, periodType: periodType);
            var cachedResult = await GetFromCacheAsync<List<TimeBasedMetrics>>(cacheKey, cancellationToken);

            if (cachedResult != null)
            {
                _logger.LogDebug("Retrieved time-based analytics from cache");
                return cachedResult;
            }

            _logger.LogInformation("Calculating time-based analytics from {StartDate} to {EndDate} with period {PeriodType}",
                startDate, endDate, periodType);

            var timeBasedMetrics = new List<TimeBasedMetrics>();
            var periods = GenerateTimePeriods(startDate, endDate, periodType);

            foreach (var period in periods)
            {
                var metrics = await _analyticsRepository.GetAggregatedMetricsAsync(
                    period.Start, period.End, messageType, channel, null, cancellationToken);

                var trendData = await CalculateTrendDataAsync(
                    period.Start, period.End, periodType, cancellationToken);

                var timeMetric = new TimeBasedMetrics(
                    period.Start, period.End, periodType, metrics, trendData);

                timeBasedMetrics.Add(timeMetric);
            }

            await SetCacheAsync(cacheKey, timeBasedMetrics, cancellationToken);

            return timeBasedMetrics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting time-based analytics");
            throw;
        }
    }

    public async Task<Dictionary<string, object>> GetRealTimeDashboardDataAsync(
        UserRole userRole,
        Guid? userId = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Getting real-time dashboard data for user role {UserRole}", userRole);

            var dashboardData = new Dictionary<string, object>();
            var now = DateTime.UtcNow;
            var last24Hours = now.AddDays(-1);
            var lastHour = now.AddHours(-1);

            // Get current metrics
            var last24HourMetrics = await GetMessagePerformanceAsync(
                last24Hours, now, null, null, userId, cancellationToken);

            var lastHourMetrics = await GetMessagePerformanceAsync(
                lastHour, now, null, null, userId, cancellationToken);

            // Build dashboard data based on user role
            dashboardData["totalMessages24h"] = last24HourMetrics.TotalMessages;
            dashboardData["deliveryRate24h"] = last24HourMetrics.DeliveryRate;
            dashboardData["readRate24h"] = last24HourMetrics.ReadRate;
            dashboardData["failureRate24h"] = last24HourMetrics.FailureRate;
            dashboardData["totalCost24h"] = last24HourMetrics.TotalCost;

            dashboardData["messagesLastHour"] = lastHourMetrics.TotalMessages;
            dashboardData["deliveryRateLastHour"] = lastHourMetrics.DeliveryRate;

            // Add role-specific metrics
            switch (userRole)
            {
                case UserRole.Admin:
                    dashboardData["systemHealth"] = await GetSystemHealthMetricsAsync(cancellationToken);
                    dashboardData["topChannels"] = await GetTopChannelsAsync(last24Hours, now, cancellationToken);
                    break;

                case UserRole.TransportCompany:
                case UserRole.Broker:
                    dashboardData["driverEngagement"] = await GetDriverEngagementMetricsAsync(last24Hours, now, cancellationToken);
                    break;
            }

            dashboardData["timestamp"] = now;

            return dashboardData;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting real-time dashboard data");
            throw;
        }
    }

    public async Task<Dictionary<string, decimal>> GetCostAnalysisAsync(
        DateTime startDate,
        DateTime endDate,
        NotificationChannel? channel = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = GenerateCacheKey("cost_analysis", startDate, endDate, channel: channel);
            var cachedResult = await GetFromCacheAsync<Dictionary<string, decimal>>(cacheKey, cancellationToken);

            if (cachedResult != null)
            {
                _logger.LogDebug("Retrieved cost analysis from cache");
                return cachedResult;
            }

            _logger.LogInformation("Calculating cost analysis from {StartDate} to {EndDate}", startDate, endDate);

            var analytics = await _analyticsRepository.GetByDateRangeAsync(
                startDate, endDate, null, channel, null, cancellationToken);

            var costAnalysis = new Dictionary<string, decimal>
            {
                ["totalCost"] = analytics.Sum(a => a.Cost),
                ["averageCostPerMessage"] = analytics.Any() ? analytics.Average(a => a.Cost) : 0,
                ["totalMessages"] = analytics.Count
            };

            // Cost by channel
            var channelCosts = analytics
                .GroupBy(a => a.Channel)
                .ToDictionary(g => $"cost_{g.Key}", g => g.Sum(a => a.Cost));

            foreach (var kvp in channelCosts)
            {
                costAnalysis[kvp.Key] = kvp.Value;
            }

            // Cost by message type
            var messageTypeCosts = analytics
                .GroupBy(a => a.MessageType)
                .ToDictionary(g => $"cost_{g.Key}", g => g.Sum(a => a.Cost));

            foreach (var kvp in messageTypeCosts)
            {
                costAnalysis[kvp.Key] = kvp.Value;
            }

            await SetCacheAsync(cacheKey, costAnalysis, cancellationToken);

            return costAnalysis;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cost analysis");
            throw;
        }
    }

    public async Task<Dictionary<string, MessagePerformanceMetrics>> GetABTestPerformanceAsync(
        string testId,
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = GenerateCacheKey("ab_test_performance", startDate, endDate, testId: testId);
            var cachedResult = await GetFromCacheAsync<Dictionary<string, MessagePerformanceMetrics>>(cacheKey, cancellationToken);

            if (cachedResult != null)
            {
                _logger.LogDebug("Retrieved A/B test performance from cache");
                return cachedResult;
            }

            _logger.LogInformation("Calculating A/B test performance for test {TestId} from {StartDate} to {EndDate}",
                testId, startDate, endDate);

            var analytics = await _analyticsRepository.GetByDateRangeAsync(
                startDate, endDate, null, null, null, cancellationToken);

            var testAnalytics = analytics.Where(a => a.ABTestVariant != null &&
                a.Tags.ContainsKey("testId") && a.Tags["testId"] == testId).ToList();

            var variantPerformance = new Dictionary<string, MessagePerformanceMetrics>();

            var variants = testAnalytics.Select(a => a.ABTestVariant!).Distinct();

            foreach (var variant in variants)
            {
                var variantAnalytics = testAnalytics.Where(a => a.ABTestVariant == variant).ToList();

                if (variantAnalytics.Any())
                {
                    var performance = CalculatePerformanceMetrics(variantAnalytics);
                    variantPerformance[variant] = performance;
                }
            }

            await SetCacheAsync(cacheKey, variantPerformance, cancellationToken);

            return variantPerformance;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting A/B test performance for test {TestId}", testId);
            throw;
        }
    }

    public async Task TrackMessageEventAsync(
        MessageAnalytics analytics,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Tracking message event for message {MessageId}", analytics.MessageId);

            await _analyticsRepository.AddAsync(analytics, cancellationToken);

            // Invalidate related cache entries
            await InvalidateRelatedCacheAsync(analytics, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error tracking message event for message {MessageId}", analytics.MessageId);
            throw;
        }
    }

    public async Task<List<(string TemplateId, MessagePerformanceMetrics Performance)>> GetTopPerformingTemplatesAsync(
        DateTime startDate,
        DateTime endDate,
        int limit = 10,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = GenerateCacheKey("top_templates", startDate, endDate, limit: limit);
            var cachedResult = await GetFromCacheAsync<List<(string, MessagePerformanceMetrics)>>(cacheKey, cancellationToken);

            if (cachedResult != null)
            {
                _logger.LogDebug("Retrieved top performing templates from cache");
                return cachedResult;
            }

            _logger.LogInformation("Getting top {Limit} performing templates from {StartDate} to {EndDate}",
                limit, startDate, endDate);

            var analytics = await _analyticsRepository.GetByDateRangeAsync(
                startDate, endDate, null, null, null, cancellationToken);

            var templateAnalytics = analytics
                .Where(a => !string.IsNullOrEmpty(a.CampaignId))
                .GroupBy(a => a.CampaignId!)
                .Select(g => new
                {
                    TemplateId = g.Key,
                    Performance = CalculatePerformanceMetrics(g.ToList())
                })
                .OrderByDescending(t => t.Performance.EngagementScore)
                .Take(limit)
                .Select(t => (t.TemplateId, t.Performance))
                .ToList();

            await SetCacheAsync(cacheKey, templateAnalytics, cancellationToken);

            return templateAnalytics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting top performing templates");
            throw;
        }
    }

    public async Task<Dictionary<string, object>> GetUserBehaviorInsightsAsync(
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = GenerateCacheKey("user_behavior_insights", startDate, endDate);
            var cachedResult = await GetFromCacheAsync<Dictionary<string, object>>(cacheKey, cancellationToken);

            if (cachedResult != null)
            {
                _logger.LogDebug("Retrieved user behavior insights from cache");
                return cachedResult;
            }

            _logger.LogInformation("Calculating user behavior insights from {StartDate} to {EndDate}",
                startDate, endDate);

            var analytics = await _analyticsRepository.GetByDateRangeAsync(
                startDate, endDate, null, null, null, cancellationToken);

            var insights = new Dictionary<string, object>();

            // Optimal send times
            var hourlyEngagement = analytics
                .GroupBy(a => a.SentAt.Hour)
                .ToDictionary(g => g.Key, g => g.Average(a => a.GetEngagementScore()));

            insights["optimalSendTimes"] = hourlyEngagement
                .OrderByDescending(kvp => kvp.Value)
                .Take(3)
                .ToDictionary(kvp => kvp.Key.ToString(), kvp => kvp.Value);

            // Channel preferences by engagement
            var channelEngagement = analytics
                .GroupBy(a => a.Channel)
                .ToDictionary(g => g.Key.ToString(), g => g.Average(a => a.GetEngagementScore()));

            insights["channelPreferences"] = channelEngagement;

            // Message type engagement
            var messageTypeEngagement = analytics
                .GroupBy(a => a.MessageType)
                .ToDictionary(g => g.Key.ToString(), g => g.Average(a => a.GetEngagementScore()));

            insights["messageTypeEngagement"] = messageTypeEngagement;

            // Response time patterns
            var responseTimes = analytics
                .Where(a => a.ResponseTime.HasValue)
                .Select(a => a.ResponseTime!.Value.TotalMinutes)
                .ToList();

            if (responseTimes.Any())
            {
                insights["averageResponseTimeMinutes"] = responseTimes.Average();
                insights["medianResponseTimeMinutes"] = responseTimes.OrderBy(x => x).Skip(responseTimes.Count / 2).First();
            }

            await SetCacheAsync(cacheKey, insights, cancellationToken);

            return insights;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user behavior insights");
            throw;
        }
    }

    // Helper methods
    private string GenerateCacheKey(string prefix, DateTime startDate, DateTime endDate,
        MessageType? messageType = null, NotificationChannel? channel = null,
        Guid? userId = null, TimePeriodType? periodType = null, string? testId = null, int? limit = null)
    {
        var keyParts = new List<string> { prefix, startDate.ToString("yyyyMMdd"), endDate.ToString("yyyyMMdd") };

        if (messageType.HasValue) keyParts.Add($"mt_{messageType}");
        if (channel.HasValue) keyParts.Add($"ch_{channel}");
        if (userId.HasValue) keyParts.Add($"u_{userId}");
        if (periodType.HasValue) keyParts.Add($"pt_{periodType}");
        if (!string.IsNullOrEmpty(testId)) keyParts.Add($"t_{testId}");
        if (limit.HasValue) keyParts.Add($"l_{limit}");

        return string.Join("_", keyParts);
    }

    private async Task<T?> GetFromCacheAsync<T>(string key, CancellationToken cancellationToken) where T : class
    {
        try
        {
            var cachedData = await _cache.GetStringAsync(key, cancellationToken);
            if (!string.IsNullOrEmpty(cachedData))
            {
                return JsonSerializer.Deserialize<T>(cachedData);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error retrieving data from cache for key {Key}", key);
        }

        return null;
    }

    private async Task SetCacheAsync<T>(string key, T data, CancellationToken cancellationToken)
    {
        try
        {
            var serializedData = JsonSerializer.Serialize(data);
            await _cache.SetStringAsync(key, serializedData, new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = _cacheExpiration
            }, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error setting cache for key {Key}", key);
        }
    }

    private async Task<Dictionary<string, decimal>> GetHourlyDistributionAsync(
        DateTime startDate, DateTime endDate, NotificationChannel channel, CancellationToken cancellationToken)
    {
        var analytics = await _analyticsRepository.GetByDateRangeAsync(
            startDate, endDate, null, channel, null, cancellationToken);

        return analytics
            .GroupBy(a => a.SentAt.Hour)
            .ToDictionary(g => g.Key.ToString(), g => (decimal)g.Count());
    }

    private async Task<decimal> CalculateMarketShareAsync(
        DateTime startDate, DateTime endDate, NotificationChannel channel, CancellationToken cancellationToken)
    {
        var totalMessages = await _analyticsRepository.GetByDateRangeAsync(
            startDate, endDate, null, null, null, cancellationToken);

        var channelMessages = totalMessages.Count(a => a.Channel == channel);

        return totalMessages.Count > 0 ? (decimal)channelMessages / totalMessages.Count * 100 : 0;
    }

    private decimal CalculateCostEfficiency(MessagePerformanceMetrics performance)
    {
        return performance.TotalCost > 0 && performance.EngagementScore > 0
            ? performance.EngagementScore / performance.TotalCost
            : 0;
    }

    private async Task<decimal> CalculateUserPreferenceAsync(
        DateTime startDate, DateTime endDate, NotificationChannel channel, CancellationToken cancellationToken)
    {
        // This would typically involve user preference data
        // For now, return a calculated preference based on usage
        var analytics = await _analyticsRepository.GetByDateRangeAsync(
            startDate, endDate, null, channel, null, cancellationToken);

        var uniqueUsers = analytics.Select(a => a.UserId).Distinct().Count();
        var totalUsers = await GetTotalActiveUsersAsync(startDate, endDate, cancellationToken);

        return totalUsers > 0 ? (decimal)uniqueUsers / totalUsers * 100 : 0;
    }

    private async Task<int> GetTotalActiveUsersAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken)
    {
        var analytics = await _analyticsRepository.GetByDateRangeAsync(
            startDate, endDate, null, null, null, cancellationToken);

        return analytics.Select(a => a.UserId).Distinct().Count();
    }

    private List<(DateTime Start, DateTime End)> GenerateTimePeriods(DateTime startDate, DateTime endDate, TimePeriodType periodType)
    {
        var periods = new List<(DateTime Start, DateTime End)>();
        var current = startDate;

        while (current < endDate)
        {
            DateTime periodEnd;
            switch (periodType)
            {
                case TimePeriodType.Hour:
                    periodEnd = current.AddHours(1);
                    break;
                case TimePeriodType.Day:
                    periodEnd = current.AddDays(1);
                    break;
                case TimePeriodType.Week:
                    periodEnd = current.AddDays(7);
                    break;
                case TimePeriodType.Month:
                    periodEnd = current.AddMonths(1);
                    break;
                case TimePeriodType.Quarter:
                    periodEnd = current.AddMonths(3);
                    break;
                case TimePeriodType.Year:
                    periodEnd = current.AddYears(1);
                    break;
                default:
                    periodEnd = current.AddDays(1);
                    break;
            }

            if (periodEnd > endDate)
                periodEnd = endDate;

            periods.Add((current, periodEnd));
            current = periodEnd;
        }

        return periods;
    }

    private async Task<Dictionary<string, decimal>> CalculateTrendDataAsync(
        DateTime startDate, DateTime endDate, TimePeriodType periodType, CancellationToken cancellationToken)
    {
        // Calculate trend data for the period
        var analytics = await _analyticsRepository.GetByDateRangeAsync(
            startDate, endDate, null, null, null, cancellationToken);

        var trendData = new Dictionary<string, decimal>
        {
            ["messageCount"] = analytics.Count,
            ["deliveryRate"] = analytics.Any() ? (decimal)analytics.Count(a => a.IsDelivered) / analytics.Count * 100 : 0,
            ["engagementScore"] = analytics.Any() ? analytics.Average(a => a.GetEngagementScore()) : 0
        };

        return trendData;
    }

    private MessagePerformanceMetrics CalculatePerformanceMetrics(List<MessageAnalytics> analytics)
    {
        if (!analytics.Any())
            return MessagePerformanceMetrics.Empty();

        var totalMessages = analytics.Count;
        var deliveredMessages = analytics.Count(a => a.IsDelivered);
        var readMessages = analytics.Count(a => a.IsRead);
        var clickedMessages = analytics.Count(a => a.IsClicked);
        var failedMessages = analytics.Count(a => a.IsFailed);

        var deliveryTimes = analytics
            .Where(a => a.DeliveryTime.HasValue)
            .Select(a => a.DeliveryTime!.Value)
            .ToList();

        var readTimes = analytics
            .Where(a => a.ReadTime.HasValue)
            .Select(a => a.ReadTime!.Value)
            .ToList();

        var averageDeliveryTime = deliveryTimes.Any()
            ? TimeSpan.FromTicks((long)deliveryTimes.Average(dt => dt.Ticks))
            : TimeSpan.Zero;

        var averageReadTime = readTimes.Any()
            ? TimeSpan.FromTicks((long)readTimes.Average(rt => rt.Ticks))
            : TimeSpan.Zero;

        var totalCost = analytics.Sum(a => a.Cost);
        var engagementScore = analytics.Average(a => a.GetEngagementScore());

        return new MessagePerformanceMetrics(
            totalMessages, deliveredMessages, readMessages, clickedMessages, failedMessages,
            averageDeliveryTime, averageReadTime, totalCost, engagementScore);
    }

    private async Task InvalidateRelatedCacheAsync(MessageAnalytics analytics, CancellationToken cancellationToken)
    {
        // Invalidate cache entries that might be affected by this new analytics data
        var keysToInvalidate = new[]
        {
            $"message_performance_{analytics.SentAt:yyyyMMdd}",
            $"channel_performance_{analytics.SentAt:yyyyMMdd}",
            $"user_engagement_{analytics.UserId}_{analytics.SentAt:yyyyMMdd}",
            "user_behavior_insights",
            "cost_analysis"
        };

        foreach (var key in keysToInvalidate)
        {
            try
            {
                await _cache.RemoveAsync(key, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error invalidating cache key {Key}", key);
            }
        }
    }

    private async Task<Dictionary<string, object>> GetSystemHealthMetricsAsync(CancellationToken cancellationToken)
    {
        // This would integrate with system health monitoring
        return new Dictionary<string, object>
        {
            ["systemStatus"] = "Healthy",
            ["activeConnections"] = 150,
            ["queueDepth"] = 25,
            ["errorRate"] = 0.5m
        };
    }

    private async Task<List<string>> GetTopChannelsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken)
    {
        var analytics = await _analyticsRepository.GetByDateRangeAsync(
            startDate, endDate, null, null, null, cancellationToken);

        return analytics
            .GroupBy(a => a.Channel)
            .OrderByDescending(g => g.Count())
            .Take(3)
            .Select(g => g.Key.ToString())
            .ToList();
    }

    private async Task<Dictionary<string, object>> GetDriverEngagementMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken)
    {
        var analytics = await _analyticsRepository.GetByDateRangeAsync(
            startDate, endDate, null, null, null, cancellationToken);

        var driverMessages = analytics.Where(a => a.Tags.ContainsKey("userRole") && a.Tags["userRole"] == "Driver").ToList();

        return new Dictionary<string, object>
        {
            ["totalDriverMessages"] = driverMessages.Count,
            ["driverEngagementRate"] = driverMessages.Any() ? driverMessages.Average(a => a.GetEngagementScore()) : 0,
            ["activeDrivers"] = driverMessages.Select(a => a.UserId).Distinct().Count()
        };
    }
}
