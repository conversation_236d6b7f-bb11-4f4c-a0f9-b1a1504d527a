using System;
using System.Collections.Generic;
using MediatR;

namespace MobileWorkflow.Application.Commands.OfflineSync
{
    public class ResolveAdvancedConflictCommand : IRequest<bool>
    {
        public Guid ConflictId { get; set; }
        public string ResolutionStrategy { get; set; } = string.Empty; // localwins, remotewins, merge, manual, auto
        public Dictionary<string, object>? CustomData { get; set; }
        public string ResolvedBy { get; set; } = string.Empty;
        public string? Notes { get; set; }
        public bool ApplyToSimilarConflicts { get; set; } = false;
        public ConflictResolutionMetadata? Metadata { get; set; }
    }

    public class ConflictResolutionMetadata
    {
        public string? DataType { get; set; }
        public string? EntityType { get; set; }
        public DateTime? ConflictDetectedAt { get; set; }
        public string? ConflictReason { get; set; }
        public Dictionary<string, object> LocalVersion { get; set; } = new();
        public Dictionary<string, object> RemoteVersion { get; set; } = new();
        public Dictionary<string, object> BaseVersion { get; set; } = new();
        public List<string> ConflictingFields { get; set; } = new();
        public string? AutoResolutionReason { get; set; }
        public double? ConfidenceScore { get; set; }
    }
}
