using MonitoringObservability.Domain.Enums;
using MonitoringObservability.Domain.Events;
using Shared.Domain.Common;

namespace MonitoringObservability.Domain.Entities;

/// <summary>
/// Represents a cost optimization analysis for cloud resources
/// </summary>
public class CostOptimizationAnalysis : AggregateRoot
{
    private readonly List<CostRecommendation> _recommendations = new();
    private readonly List<CostAlert> _alerts = new();
    private readonly List<ResourceCostAnalysis> _resourceAnalyses = new();

    public CostOptimizationAnalysis(string name, string description, string serviceName,
        CostAnalysisScope scope, TimeSpan analysisWindow, Guid ownerId)
    {
        Id = Guid.NewGuid();
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        ServiceName = serviceName ?? throw new ArgumentNullException(nameof(serviceName));
        Scope = scope;
        AnalysisWindow = analysisWindow;
        OwnerId = ownerId;
        CreatedAt = DateTime.UtcNow;
        LastModified = DateTime.UtcNow;
        IsActive = true;
        Status = CostAnalysisStatus.Active;
        Configuration = new Dictionary<string, object>();
        Tags = new Dictionary<string, string>();
    }

    // Required for EF Core
    private CostOptimizationAnalysis() { }

    public string Name { get; private set; } = string.Empty;
    public string Description { get; private set; } = string.Empty;
    public string ServiceName { get; private set; } = string.Empty;
    public CostAnalysisScope Scope { get; private set; }
    public TimeSpan AnalysisWindow { get; private set; }
    public Guid OwnerId { get; private set; }
    public bool IsActive { get; private set; }
    public CostAnalysisStatus Status { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime LastModified { get; private set; }
    public DateTime? LastExecuted { get; private set; }
    public DateTime? NextExecution { get; private set; }
    public decimal? TotalCost { get; private set; }
    public decimal? PotentialSavings { get; private set; }
    public Dictionary<string, object> Configuration { get; private set; } = new();
    public Dictionary<string, string> Tags { get; private set; } = new();

    // Navigation properties
    public IReadOnlyList<CostRecommendation> Recommendations => _recommendations.AsReadOnly();
    public IReadOnlyList<CostAlert> Alerts => _alerts.AsReadOnly();
    public IReadOnlyList<ResourceCostAnalysis> ResourceAnalyses => _resourceAnalyses.AsReadOnly();

    // Computed properties
    public int RecommendationCount => _recommendations.Count;
    public int HighPriorityRecommendationCount => _recommendations.Count(r => r.Priority >= CostOptimizationPriority.High);
    public int ActiveAlertCount => _alerts.Count(a => a.Status == CostAlertStatus.Open);
    public decimal? SavingsPercentage => TotalCost > 0 && PotentialSavings.HasValue ?
        (PotentialSavings.Value / TotalCost.Value) * 100 : null;

    public void UpdateConfiguration(TimeSpan? analysisWindow = null, Dictionary<string, object>? configuration = null)
    {
        if (analysisWindow.HasValue)
            AnalysisWindow = analysisWindow.Value;

        if (configuration != null)
            Configuration = configuration;

        LastModified = DateTime.UtcNow;

        AddDomainEvent(new CostOptimizationAnalysisUpdatedEvent(Id, Name));
    }

    public void AddRecommendation(CostRecommendation recommendation)
    {
        if (recommendation == null) throw new ArgumentNullException(nameof(recommendation));

        _recommendations.Add(recommendation);
        LastModified = DateTime.UtcNow;

        AddDomainEvent(new CostRecommendationCreatedEvent(Id, recommendation.Id, recommendation.Priority.ToString()));
    }

    public void AddAlert(CostAlert alert)
    {
        if (alert == null) throw new ArgumentNullException(nameof(alert));

        _alerts.Add(alert);
        LastModified = DateTime.UtcNow;

        AddDomainEvent(new CostAlertCreatedEvent(Id, alert.Id, alert.Severity.ToString()));
    }

    public void AddResourceAnalysis(ResourceCostAnalysis analysis)
    {
        if (analysis == null) throw new ArgumentNullException(nameof(analysis));

        _resourceAnalyses.Add(analysis);
        LastModified = DateTime.UtcNow;
    }

    public void UpdateCostData(decimal totalCost, decimal potentialSavings)
    {
        TotalCost = totalCost;
        PotentialSavings = potentialSavings;
        LastModified = DateTime.UtcNow;

        AddDomainEvent(new CostDataUpdatedEvent(Id, Name));
    }

    public void Execute()
    {
        LastExecuted = DateTime.UtcNow;
        NextExecution = CalculateNextExecution();
        LastModified = DateTime.UtcNow;

        AddDomainEvent(new CostOptimizationAnalysisExecutedEvent(Id, Name));
    }

    public void Activate()
    {
        if (!IsActive)
        {
            IsActive = true;
            Status = CostAnalysisStatus.Active;
            LastModified = DateTime.UtcNow;

            AddDomainEvent(new CostOptimizationAnalysisActivatedEvent(Id, Name));
        }
    }

    public void Deactivate()
    {
        if (IsActive)
        {
            IsActive = false;
            Status = CostAnalysisStatus.Inactive;
            LastModified = DateTime.UtcNow;

            AddDomainEvent(new CostOptimizationAnalysisDeactivatedEvent(Id, Name));
        }
    }

    public void AddTag(string key, string value)
    {
        if (string.IsNullOrWhiteSpace(key)) throw new ArgumentException("Tag key cannot be null or empty", nameof(key));
        Tags[key] = value ?? string.Empty;
        LastModified = DateTime.UtcNow;
    }

    public IEnumerable<CostRecommendation> GetHighPriorityRecommendations()
    {
        return _recommendations.Where(r => r.Priority >= CostOptimizationPriority.High);
    }

    public IEnumerable<CostAlert> GetActiveAlerts()
    {
        return _alerts.Where(a => a.Status == CostAlertStatus.Open);
    }

    public ResourceCostAnalysis? GetResourceAnalysis(string resourceId)
    {
        return _resourceAnalyses.FirstOrDefault(r => r.ResourceId == resourceId);
    }

    private DateTime CalculateNextExecution()
    {
        return Scope switch
        {
            CostAnalysisScope.Daily => DateTime.UtcNow.AddDays(1),
            CostAnalysisScope.Weekly => DateTime.UtcNow.AddDays(7),
            CostAnalysisScope.Monthly => DateTime.UtcNow.AddMonths(1),
            _ => DateTime.UtcNow.AddDays(1)
        };
    }
}

/// <summary>
/// Represents a cost optimization recommendation
/// </summary>
public class CostRecommendation
{
    public CostRecommendation(Guid analysisId, string resourceId, string resourceType,
        CostOptimizationType optimizationType, string title, string description,
        decimal estimatedSavings, CostOptimizationPriority priority)
    {
        Id = Guid.NewGuid();
        AnalysisId = analysisId;
        ResourceId = resourceId ?? throw new ArgumentNullException(nameof(resourceId));
        ResourceType = resourceType ?? throw new ArgumentNullException(nameof(resourceType));
        OptimizationType = optimizationType;
        Title = title ?? throw new ArgumentNullException(nameof(title));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        EstimatedSavings = estimatedSavings;
        Priority = priority;
        CreatedAt = DateTime.UtcNow;
        Status = CostRecommendationStatus.Pending;
        Metadata = new Dictionary<string, object>();
    }

    // Required for EF Core
    private CostRecommendation() { }

    public Guid Id { get; private set; }
    public Guid AnalysisId { get; private set; }
    public string ResourceId { get; private set; } = string.Empty;
    public string ResourceType { get; private set; } = string.Empty;
    public CostOptimizationType OptimizationType { get; private set; }
    public string Title { get; private set; } = string.Empty;
    public string Description { get; private set; } = string.Empty;
    public decimal EstimatedSavings { get; private set; }
    public CostOptimizationPriority Priority { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? ImplementedAt { get; private set; }
    public CostRecommendationStatus Status { get; private set; }
    public string? ImplementationNotes { get; private set; }
    public Guid? ImplementedBy { get; private set; }
    public decimal? ActualSavings { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; } = new();

    // Navigation properties
    public CostOptimizationAnalysis Analysis { get; private set; } = null!;

    // Computed properties
    public TimeSpan? ImplementationTime => ImplementedAt?.Subtract(CreatedAt);
    public bool IsImplemented => Status == CostRecommendationStatus.Implemented;
    public decimal? SavingsAccuracy => EstimatedSavings > 0 && ActualSavings.HasValue ?
        (ActualSavings.Value / EstimatedSavings) * 100 : null;

    public void Implement(Guid implementedBy, string? implementationNotes = null, decimal? actualSavings = null)
    {
        if (Status == CostRecommendationStatus.Pending || Status == CostRecommendationStatus.Approved)
        {
            Status = CostRecommendationStatus.Implemented;
            ImplementedAt = DateTime.UtcNow;
            ImplementedBy = implementedBy;
            ImplementationNotes = implementationNotes;
            ActualSavings = actualSavings;
        }
    }

    public void Approve(Guid approvedBy, string? notes = null)
    {
        if (Status == CostRecommendationStatus.Pending)
        {
            Status = CostRecommendationStatus.Approved;
            Metadata["approved_by"] = approvedBy;
            Metadata["approved_at"] = DateTime.UtcNow;
            if (!string.IsNullOrEmpty(notes))
                Metadata["approval_notes"] = notes;
        }
    }

    public void Reject(Guid rejectedBy, string reason)
    {
        if (Status == CostRecommendationStatus.Pending)
        {
            Status = CostRecommendationStatus.Rejected;
            Metadata["rejected_by"] = rejectedBy;
            Metadata["rejected_at"] = DateTime.UtcNow;
            Metadata["rejection_reason"] = reason;
        }
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }
}

/// <summary>
/// Represents a cost alert for budget thresholds or anomalies
/// </summary>
public class CostAlert
{
    public CostAlert(Guid analysisId, CostAlertType alertType, string title, string description,
        decimal threshold, decimal currentValue, CostAlertSeverity severity)
    {
        Id = Guid.NewGuid();
        AnalysisId = analysisId;
        AlertType = alertType;
        Title = title ?? throw new ArgumentNullException(nameof(title));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        Threshold = threshold;
        CurrentValue = currentValue;
        Severity = severity;
        CreatedAt = DateTime.UtcNow;
        Status = CostAlertStatus.Open;
        Metadata = new Dictionary<string, object>();
    }

    // Required for EF Core
    private CostAlert() { }

    public Guid Id { get; private set; }
    public Guid AnalysisId { get; private set; }
    public CostAlertType AlertType { get; private set; }
    public string Title { get; private set; } = string.Empty;
    public string Description { get; private set; } = string.Empty;
    public decimal Threshold { get; private set; }
    public decimal CurrentValue { get; private set; }
    public CostAlertSeverity Severity { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? ResolvedAt { get; private set; }
    public CostAlertStatus Status { get; private set; }
    public string? ResolutionNotes { get; private set; }
    public Guid? ResolvedBy { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; } = new();

    // Navigation properties
    public CostOptimizationAnalysis Analysis { get; private set; } = null!;

    // Computed properties
    public TimeSpan? Duration => ResolvedAt?.Subtract(CreatedAt);
    public decimal ThresholdExceededBy => Math.Abs(CurrentValue - Threshold);
    public decimal ThresholdExceededPercentage => Threshold > 0 ?
        Math.Abs((CurrentValue - Threshold) / Threshold) * 100 : 0;

    public void Resolve(Guid resolvedBy, string? resolutionNotes = null)
    {
        if (Status == CostAlertStatus.Open)
        {
            Status = CostAlertStatus.Resolved;
            ResolvedAt = DateTime.UtcNow;
            ResolvedBy = resolvedBy;
            ResolutionNotes = resolutionNotes;
        }
    }

    public void Acknowledge(Guid acknowledgedBy, string? notes = null)
    {
        if (Status == CostAlertStatus.Open)
        {
            Status = CostAlertStatus.Acknowledged;
            Metadata["acknowledged_by"] = acknowledgedBy;
            Metadata["acknowledged_at"] = DateTime.UtcNow;
            if (!string.IsNullOrEmpty(notes))
                Metadata["acknowledgment_notes"] = notes;
        }
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }
}

/// <summary>
/// Represents cost analysis for a specific resource
/// </summary>
public class ResourceCostAnalysis
{
    public ResourceCostAnalysis(Guid analysisId, string resourceId, string resourceType,
        string resourceName, decimal currentCost, decimal projectedCost,
        double utilizationPercentage, CostEfficiencyRating efficiency)
    {
        Id = Guid.NewGuid();
        AnalysisId = analysisId;
        ResourceId = resourceId ?? throw new ArgumentNullException(nameof(resourceId));
        ResourceType = resourceType ?? throw new ArgumentNullException(nameof(resourceType));
        ResourceName = resourceName ?? throw new ArgumentNullException(nameof(resourceName));
        CurrentCost = currentCost;
        ProjectedCost = projectedCost;
        UtilizationPercentage = utilizationPercentage;
        Efficiency = efficiency;
        AnalyzedAt = DateTime.UtcNow;
        Metadata = new Dictionary<string, object>();
    }

    // Required for EF Core
    private ResourceCostAnalysis() { }

    public Guid Id { get; private set; }
    public Guid AnalysisId { get; private set; }
    public string ResourceId { get; private set; } = string.Empty;
    public string ResourceType { get; private set; } = string.Empty;
    public string ResourceName { get; private set; } = string.Empty;
    public decimal CurrentCost { get; private set; }
    public decimal ProjectedCost { get; private set; }
    public double UtilizationPercentage { get; private set; }
    public CostEfficiencyRating Efficiency { get; private set; }
    public DateTime AnalyzedAt { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; } = new();

    // Navigation properties
    public CostOptimizationAnalysis Analysis { get; private set; } = null!;

    // Computed properties
    public decimal CostTrend => ProjectedCost - CurrentCost;
    public decimal CostTrendPercentage => CurrentCost > 0 ? (CostTrend / CurrentCost) * 100 : 0;
    public bool IsOverUtilized => UtilizationPercentage > 80;
    public bool IsUnderUtilized => UtilizationPercentage < 20;

    public void UpdateCostData(decimal currentCost, decimal projectedCost, double utilizationPercentage)
    {
        CurrentCost = currentCost;
        ProjectedCost = projectedCost;
        UtilizationPercentage = utilizationPercentage;
        AnalyzedAt = DateTime.UtcNow;
    }

    public void UpdateEfficiency(CostEfficiencyRating efficiency)
    {
        Efficiency = efficiency;
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }
}
