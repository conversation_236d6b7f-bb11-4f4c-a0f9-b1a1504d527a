using MediatR;
using Microsoft.Extensions.Logging;
using TripManagement.Application.Interfaces;
using TripManagement.Application.DTOs;

namespace TripManagement.Application.Commands.OptimizeRoute;

public class OptimizeRouteCommandHandler : IRequestHandler<OptimizeRouteCommand, RouteOptimizationResult>
{
    private readonly ITripRepository _tripRepository;
    private readonly IRouteOptimizationService _routeOptimizationService;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<OptimizeRouteCommandHandler> _logger;
    private readonly IMessageBroker _messageBroker;

    public OptimizeRouteCommandHandler(
        ITripRepository tripRepository,
        IRouteOptimizationService routeOptimizationService,
        IUnitOfWork unitOfWork,
        ILogger<OptimizeRouteCommandHandler> logger,
        IMessageBroker messageBroker)
    {
        _tripRepository = tripRepository;
        _routeOptimizationService = routeOptimizationService;
        _unitOfWork = unitOfWork;
        _logger = logger;
        _messageBroker = messageBroker;
    }

    public async Task<RouteOptimizationResult> Handle(OptimizeRouteCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Optimizing route for trip {TripId}", request.TripId);

            // Get the trip
            var trip = await _tripRepository.GetByIdAsync(request.TripId, cancellationToken);
            if (trip == null)
            {
                throw new ArgumentException($"Trip {request.TripId} not found");
            }

            // Prepare optimization parameters
            var optimizationParams = new RouteOptimizationParams
            {
                StartLocation = trip.Route.StartLocation,
                EndLocation = trip.Route.EndLocation,
                Waypoints = request.Waypoints,
                Criteria = request.Criteria,
                AvoidTolls = request.AvoidTolls,
                AvoidHighways = request.AvoidHighways,
                VehicleConstraints = request.VehicleConstraints
            };

            // Perform route optimization
            var optimizationResult = await _routeOptimizationService.OptimizeRouteAsync(optimizationParams, cancellationToken);

            // Update trip with optimized route if better
            if (optimizationResult.TotalDistanceKm < trip.Route.EstimatedDistanceKm)
            {
                trip.UpdateRoute(optimizationResult.OptimizedRoute);
                _tripRepository.Update(trip);
                await _unitOfWork.SaveChangesAsync(cancellationToken);

                // Publish route optimization event
                await _messageBroker.PublishAsync("trip.route_optimized", new
                {
                    TripId = request.TripId,
                    OriginalDistance = trip.Route.EstimatedDistanceKm,
                    OptimizedDistance = optimizationResult.TotalDistanceKm,
                    DistanceSaved = trip.Route.EstimatedDistanceKm - optimizationResult.TotalDistanceKm,
                    OptimizedAt = DateTime.UtcNow,
                    Criteria = request.Criteria
                }, cancellationToken);

                _logger.LogInformation("Route optimized for trip {TripId}. Distance reduced from {OriginalDistance}km to {OptimizedDistance}km", 
                    request.TripId, trip.Route.EstimatedDistanceKm, optimizationResult.TotalDistanceKm);
            }

            return optimizationResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error optimizing route for trip {TripId}", request.TripId);
            throw;
        }
    }
}

public record RouteOptimizationParams
{
    public LocationDto StartLocation { get; init; } = null!;
    public LocationDto EndLocation { get; init; } = null!;
    public List<LocationDto> Waypoints { get; init; } = new();
    public OptimizationCriteria Criteria { get; init; }
    public bool AvoidTolls { get; init; }
    public bool AvoidHighways { get; init; }
    public VehicleConstraints? VehicleConstraints { get; init; }
}
