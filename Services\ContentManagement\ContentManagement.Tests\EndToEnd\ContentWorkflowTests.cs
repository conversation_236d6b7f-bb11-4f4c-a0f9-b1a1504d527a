using ContentManagement.Application.Commands.Content;
using ContentManagement.Application.DTOs;
using ContentManagement.Domain.Enums;
using ContentManagement.Infrastructure.Data;
using FluentAssertions;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using Xunit;

namespace ContentManagement.Tests.EndToEnd;

public class ContentWorkflowTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;

    public ContentWorkflowTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory.WithWebHostBuilder(builder =>
        {
            builder.UseEnvironment("Testing");
            builder.ConfigureServices(services =>
            {
                // Remove the existing DbContext registration
                var descriptor = services.SingleOrDefault(
                    d => d.ServiceType == typeof(DbContextOptions<ContentManagementDbContext>));
                if (descriptor != null)
                    services.Remove(descriptor);

                // Add in-memory database for testing
                services.AddDbContext<ContentManagementDbContext>(options =>
                {
                    options.UseInMemoryDatabase("E2ETestDb");
                });

                // Build the service provider
                var sp = services.BuildServiceProvider();

                // Create a scope to obtain a reference to the database context
                using var scope = sp.CreateScope();
                var scopedServices = scope.ServiceProvider;
                var db = scopedServices.GetRequiredService<ContentManagementDbContext>();

                // Ensure the database is created
                db.Database.EnsureCreated();
            });
        });

        _client = _factory.CreateClient();
    }

    [Fact]
    public async Task Complete_Content_Workflow_Should_Work_End_To_End()
    {
        // Step 1: Create content
        var createCommand = new CreateContentCommand
        {
            Title = "E2E Test Content",
            Slug = "e2e-test-content",
            Type = ContentType.General,
            ContentBody = "This is end-to-end test content",
            Description = "E2E test description",
            Tags = new List<string> { "e2e", "test" }
        };

        var createResponse = await _client.PostAsJsonAsync("/api/content", createCommand);
        createResponse.StatusCode.Should().Be(HttpStatusCode.Created);
        
        var createdContent = await createResponse.Content.ReadFromJsonAsync<ContentDto>();
        createdContent.Should().NotBeNull();
        createdContent!.Status.Should().Be(ContentStatus.Draft);

        var contentId = createdContent.Id;

        // Step 2: Update content
        var updateCommand = new UpdateContentCommand
        {
            Title = "Updated E2E Test Content",
            Description = "Updated E2E test description",
            ContentBody = "This is updated end-to-end test content",
            Tags = new List<string> { "e2e", "test", "updated" }
        };

        var updateResponse = await _client.PutAsJsonAsync($"/api/content/{contentId}", updateCommand);
        updateResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var updatedContent = await updateResponse.Content.ReadFromJsonAsync<ContentDto>();
        updatedContent!.Title.Should().Be("Updated E2E Test Content");
        updatedContent.Status.Should().Be(ContentStatus.Draft);

        // Step 3: Submit for review
        var submitResponse = await _client.PostAsync($"/api/content/{contentId}/submit-for-review", null);
        submitResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var submittedContent = await submitResponse.Content.ReadFromJsonAsync<ContentDto>();
        submittedContent!.Status.Should().Be(ContentStatus.PendingReview);

        // Step 4: Approve content
        var approveResponse = await _client.PostAsync($"/api/content/{contentId}/approve", null);
        approveResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var approvedContent = await approveResponse.Content.ReadFromJsonAsync<ContentDto>();
        approvedContent!.Status.Should().Be(ContentStatus.Approved);

        // Step 5: Publish content
        var publishResponse = await _client.PostAsync($"/api/content/{contentId}/publish", null);
        publishResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var publishedContent = await publishResponse.Content.ReadFromJsonAsync<ContentDto>();
        publishedContent!.Status.Should().Be(ContentStatus.Published);

        // Step 6: Verify content is searchable when published
        var searchResponse = await _client.GetAsync("/api/content/search?searchTerm=E2E");
        searchResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var searchResult = await searchResponse.Content.ReadFromJsonAsync<ContentSearchResultDto>();
        searchResult!.Items.Should().Contain(c => c.Id == contentId);

        // Step 7: Get content by slug
        var slugResponse = await _client.GetAsync("/api/content/slug/e2e-test-content");
        slugResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var contentBySlug = await slugResponse.Content.ReadFromJsonAsync<ContentDto>();
        contentBySlug!.Id.Should().Be(contentId);
        contentBySlug.Status.Should().Be(ContentStatus.Published);

        // Step 8: Unpublish content
        var unpublishResponse = await _client.PostAsync($"/api/content/{contentId}/unpublish", null);
        unpublishResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var unpublishedContent = await unpublishResponse.Content.ReadFromJsonAsync<ContentDto>();
        unpublishedContent!.Status.Should().Be(ContentStatus.Approved);

        // Step 9: Archive content
        var archiveResponse = await _client.PostAsync($"/api/content/{contentId}/archive", null);
        archiveResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var archivedContent = await archiveResponse.Content.ReadFromJsonAsync<ContentDto>();
        archivedContent!.Status.Should().Be(ContentStatus.Archived);
    }

    [Fact]
    public async Task FAQ_Workflow_Should_Work_End_To_End()
    {
        // Step 1: Create FAQ
        var createFaqCommand = new
        {
            Question = "What is the meaning of life?",
            Answer = "42",
            Category = "General",
            RoleVisibility = new[] { "User", "Admin" },
            IsHighlighted = false,
            Tags = new[] { "philosophy", "life" }
        };

        var createResponse = await _client.PostAsJsonAsync("/api/faq", createFaqCommand);
        createResponse.StatusCode.Should().Be(HttpStatusCode.Created);
        
        var createdFaq = await createResponse.Content.ReadFromJsonAsync<FaqItemDto>();
        createdFaq.Should().NotBeNull();
        createdFaq!.Question.Should().Be("What is the meaning of life?");

        var faqId = createdFaq.Id;

        // Step 2: Update FAQ
        var updateFaqCommand = new
        {
            Question = "What is the ultimate meaning of life?",
            Answer = "42 - The answer to the ultimate question of life, the universe, and everything"
        };

        var updateResponse = await _client.PutAsJsonAsync($"/api/faq/{faqId}", updateFaqCommand);
        updateResponse.StatusCode.Should().Be(HttpStatusCode.OK);

        // Step 3: Set as highlighted
        var highlightCommand = new { IsHighlighted = true };
        var highlightResponse = await _client.PutAsJsonAsync($"/api/faq/{faqId}/highlight", highlightCommand);
        highlightResponse.StatusCode.Should().Be(HttpStatusCode.OK);

        // Step 4: Record view
        var viewResponse = await _client.PostAsync($"/api/faq/{faqId}/view", null);
        viewResponse.StatusCode.Should().Be(HttpStatusCode.OK);

        // Step 5: Rate FAQ
        var rateCommand = new { Rating = 5 };
        var rateResponse = await _client.PostAsJsonAsync($"/api/faq/{faqId}/rate", rateCommand);
        rateResponse.StatusCode.Should().Be(HttpStatusCode.OK);

        // Step 6: Search FAQs
        var searchResponse = await _client.GetAsync("/api/faq/search?searchTerm=meaning");
        searchResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var searchResult = await searchResponse.Content.ReadFromJsonAsync<FaqSearchResultDto>();
        searchResult!.Items.Should().Contain(f => f.Id == faqId);

        // Step 7: Get highlighted FAQs
        var highlightedResponse = await _client.GetAsync("/api/faq/highlighted");
        highlightedResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var highlightedFaqs = await highlightedResponse.Content.ReadFromJsonAsync<List<FaqSummaryDto>>();
        highlightedFaqs!.Should().Contain(f => f.Id == faqId);
    }

    [Fact]
    public async Task Policy_Workflow_Should_Work_End_To_End()
    {
        // Step 1: Create policy
        var createPolicyCommand = new
        {
            PolicyType = "PrivacyPolicy",
            Title = "Privacy Policy",
            Version = "1.0",
            ContentBody = "This is our privacy policy content",
            EffectiveDate = DateTime.UtcNow,
            RequiresAcceptance = true,
            ApplicableRoles = new[] { "User", "Admin" }
        };

        var createResponse = await _client.PostAsJsonAsync("/api/policy", createPolicyCommand);
        createResponse.StatusCode.Should().Be(HttpStatusCode.Created);
        
        var createdPolicy = await createResponse.Content.ReadFromJsonAsync<PolicyDocumentDto>();
        createdPolicy.Should().NotBeNull();
        createdPolicy!.Title.Should().Be("Privacy Policy");

        var policyId = createdPolicy.Id;

        // Step 2: Get policy by type
        var typeResponse = await _client.GetAsync("/api/policy/type/PrivacyPolicy");
        typeResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var policyByType = await typeResponse.Content.ReadFromJsonAsync<PolicyDocumentDto>();
        policyByType!.Id.Should().Be(policyId);

        // Step 3: Get applicable policies
        var applicableResponse = await _client.GetAsync("/api/policy/applicable");
        applicableResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var applicablePolicies = await applicableResponse.Content.ReadFromJsonAsync<List<PolicySummaryDto>>();
        applicablePolicies!.Should().Contain(p => p.Id == policyId);

        // Step 4: Accept policy
        var acceptResponse = await _client.PostAsync($"/api/policy/{policyId}/accept", null);
        acceptResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var acceptance = await acceptResponse.Content.ReadFromJsonAsync<PolicyAcceptanceDto>();
        acceptance!.PolicyId.Should().Be(policyId);

        // Step 5: Get user policy status
        var statusResponse = await _client.GetAsync("/api/policy/user-status");
        statusResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var userStatus = await statusResponse.Content.ReadFromJsonAsync<UserPolicyStatusDto>();
        userStatus!.PolicyStatuses.Should().Contain(p => p.PolicyId == policyId && p.IsAccepted);
    }

    [Fact]
    public async Task Admin_Dashboard_Should_Provide_Analytics()
    {
        // Create some test content first
        await CreateTestContentForAnalytics();

        // Get admin dashboard
        var dashboardResponse = await _client.GetAsync("/api/admin/dashboard");
        dashboardResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var dashboard = await dashboardResponse.Content.ReadFromJsonAsync<AdminDashboardDto>();
        dashboard.Should().NotBeNull();
        dashboard!.ContentStatistics.Should().NotBeNull();
        dashboard.ContentStatistics!.TotalContent.Should().BeGreaterThan(0);

        // Get content analytics
        var analyticsResponse = await _client.GetAsync("/api/admin/analytics");
        analyticsResponse.StatusCode.Should().Be(HttpStatusCode.OK);

        // Get performance metrics
        var performanceResponse = await _client.GetAsync("/api/admin/performance");
        performanceResponse.StatusCode.Should().Be(HttpStatusCode.OK);

        // Get content statistics
        var statsResponse = await _client.GetAsync("/api/content/statistics");
        statsResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var stats = await statsResponse.Content.ReadFromJsonAsync<ContentStatisticsDto>();
        stats!.TotalContent.Should().BeGreaterThan(0);
    }

    private async Task CreateTestContentForAnalytics()
    {
        // Create multiple content items for analytics
        for (int i = 0; i < 5; i++)
        {
            var command = new CreateContentCommand
            {
                Title = $"Analytics Test Content {i}",
                Slug = $"analytics-test-{i}",
                Type = ContentType.General,
                ContentBody = $"Analytics test content body {i}",
                Tags = new List<string> { "analytics", "test" }
            };

            await _client.PostAsJsonAsync("/api/content", command);
        }
    }
}
