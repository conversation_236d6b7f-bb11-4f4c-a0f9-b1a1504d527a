using CommunicationNotification.Application.Common;
using CommunicationNotification.Domain.Enums;

namespace CommunicationNotification.Application.Commands.Conversations;

/// <summary>
/// Command to create a new conversation
/// </summary>
public class CreateConversationCommand : CommandBase<CommandResult<CreateConversationResult>>
{
    public string Title { get; set; } = string.Empty;
    public ConversationType Type { get; set; }
    public List<Guid> ParticipantIds { get; set; } = new();
    public Guid CreatedBy { get; set; }
    public string? Description { get; set; }
    public bool IsPrivate { get; set; } = false;
    public Dictionary<string, object> Settings { get; set; } = new();
    public List<string> Tags { get; set; } = new();
}

/// <summary>
/// Command to join a conversation
/// </summary>
public class JoinConversationCommand : CommandBase<CommandResult<JoinConversationResult>>
{
    public Guid ConversationId { get; set; }
    public Guid UserId { get; set; }
    public string? Role { get; set; }
    public string? InvitationCode { get; set; }
}

/// <summary>
/// Command to leave a conversation
/// </summary>
public class LeaveConversationCommand : CommandBase<CommandResult<LeaveConversationResult>>
{
    public Guid ConversationId { get; set; }
    public Guid UserId { get; set; }
    public string? LeaveReason { get; set; }
}

/// <summary>
/// Command to send a message in a conversation
/// </summary>
public class SendConversationMessageCommand : CommandBase<CommandResult<SendConversationMessageResult>>
{
    public Guid ConversationId { get; set; }
    public Guid SenderId { get; set; }
    public string Content { get; set; } = string.Empty;
    public MessageType MessageType { get; set; } = MessageType.Text;
    public Guid? ReplyToMessageId { get; set; }
    public List<MessageAttachment> Attachments { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
    public bool IsSystemMessage { get; set; } = false;
}

/// <summary>
/// Command to update conversation settings
/// </summary>
public class UpdateConversationCommand : CommandBase<CommandResult<UpdateConversationResult>>
{
    public Guid ConversationId { get; set; }
    public Guid UpdatedBy { get; set; }
    public string? Title { get; set; }
    public string? Description { get; set; }
    public ConversationStatus? Status { get; set; }
    public Dictionary<string, object>? Settings { get; set; }
    public List<string>? Tags { get; set; }
}

/// <summary>
/// Command to add participants to a conversation
/// </summary>
public class AddParticipantsCommand : CommandBase<CommandResult<AddParticipantsResult>>
{
    public Guid ConversationId { get; set; }
    public List<Guid> ParticipantIds { get; set; } = new();
    public Guid AddedBy { get; set; }
    public string? DefaultRole { get; set; }
    public bool SendWelcomeMessage { get; set; } = true;
}

/// <summary>
/// Command to remove participants from a conversation
/// </summary>
public class RemoveParticipantsCommand : CommandBase<CommandResult<RemoveParticipantsResult>>
{
    public Guid ConversationId { get; set; }
    public List<Guid> ParticipantIds { get; set; } = new();
    public Guid RemovedBy { get; set; }
    public string? RemovalReason { get; set; }
    public bool SendNotification { get; set; } = true;
}

/// <summary>
/// Command to archive a conversation
/// </summary>
public class ArchiveConversationCommand : CommandBase<CommandResult<ArchiveConversationResult>>
{
    public Guid ConversationId { get; set; }
    public Guid ArchivedBy { get; set; }
    public string? ArchiveReason { get; set; }
    public bool NotifyParticipants { get; set; } = true;
}

/// <summary>
/// Command to mark messages as read
/// </summary>
public class MarkMessagesAsReadCommand : CommandBase<CommandResult<MarkMessagesAsReadResult>>
{
    public Guid ConversationId { get; set; }
    public Guid UserId { get; set; }
    public List<Guid> MessageIds { get; set; } = new();
    public DateTime? ReadUntil { get; set; }
}

/// <summary>
/// Command to delete a message
/// </summary>
public class DeleteMessageCommand : CommandBase<CommandResult<DeleteMessageResult>>
{
    public Guid MessageId { get; set; }
    public Guid DeletedBy { get; set; }
    public string? DeletionReason { get; set; }
    public bool SoftDelete { get; set; } = true;
}

/// <summary>
/// Command to edit a message
/// </summary>
public class EditMessageCommand : CommandBase<CommandResult<EditMessageResult>>
{
    public Guid MessageId { get; set; }
    public Guid EditedBy { get; set; }
    public string NewContent { get; set; } = string.Empty;
    public string? EditReason { get; set; }
    public bool PreserveHistory { get; set; } = true;
}

// Result classes
public class CreateConversationResult
{
    public Guid ConversationId { get; set; }
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime CreatedAt { get; set; }
    public int ParticipantCount { get; set; }
    public string? InvitationCode { get; set; }
}

public class JoinConversationResult
{
    public Guid ConversationId { get; set; }
    public Guid UserId { get; set; }
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime JoinedAt { get; set; }
    public string? AssignedRole { get; set; }
    public bool WasAlreadyMember { get; set; }
}

public class LeaveConversationResult
{
    public Guid ConversationId { get; set; }
    public Guid UserId { get; set; }
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime LeftAt { get; set; }
    public bool WasLastParticipant { get; set; }
}

public class SendConversationMessageResult
{
    public Guid MessageId { get; set; }
    public Guid ConversationId { get; set; }
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime SentAt { get; set; }
    public int DeliveredToCount { get; set; }
    public List<Guid> FailedDeliveries { get; set; } = new();
}

public class UpdateConversationResult
{
    public Guid ConversationId { get; set; }
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime UpdatedAt { get; set; }
    public List<string> UpdatedFields { get; set; } = new();
}

public class AddParticipantsResult
{
    public Guid ConversationId { get; set; }
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime AddedAt { get; set; }
    public List<Guid> SuccessfullyAdded { get; set; } = new();
    public List<Guid> AlreadyMembers { get; set; } = new();
    public List<Guid> FailedToAdd { get; set; } = new();
}

public class RemoveParticipantsResult
{
    public Guid ConversationId { get; set; }
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime RemovedAt { get; set; }
    public List<Guid> SuccessfullyRemoved { get; set; } = new();
    public List<Guid> NotMembers { get; set; } = new();
    public List<Guid> FailedToRemove { get; set; } = new();
}

public class ArchiveConversationResult
{
    public Guid ConversationId { get; set; }
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime ArchivedAt { get; set; }
    public int MessageCount { get; set; }
    public int ParticipantCount { get; set; }
}

public class MarkMessagesAsReadResult
{
    public Guid ConversationId { get; set; }
    public Guid UserId { get; set; }
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime ReadAt { get; set; }
    public int MessagesMarkedAsRead { get; set; }
    public List<Guid> FailedMessageIds { get; set; } = new();
}

public class DeleteMessageResult
{
    public Guid MessageId { get; set; }
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime DeletedAt { get; set; }
    public bool WasSoftDelete { get; set; }
    public bool NotifiedParticipants { get; set; }
}

public class EditMessageResult
{
    public Guid MessageId { get; set; }
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime EditedAt { get; set; }
    public bool HistoryPreserved { get; set; }
    public int EditCount { get; set; }
}

// Supporting classes
public class MessageAttachment
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public string FileName { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    public long Size { get; set; }
    public string Url { get; set; } = string.Empty;
    public Dictionary<string, object> Metadata { get; set; } = new();
}

// Enums
public enum ConversationType
{
    OneToOne,
    Group,
    Broadcast,
    Support,
    System
}

public enum ConversationStatus
{
    Active,
    Archived,
    Closed,
    Suspended
}
