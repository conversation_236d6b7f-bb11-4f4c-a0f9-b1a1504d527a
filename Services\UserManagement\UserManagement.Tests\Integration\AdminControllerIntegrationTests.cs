using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using UserManagement.Domain.Entities;
using UserManagement.Infrastructure.Data;
using Xunit;

namespace UserManagement.Tests.Integration
{
    public class AdminControllerIntegrationTests : IClassFixture<WebApplicationFactory<Program>>
    {
        private readonly WebApplicationFactory<Program> _factory;
        private readonly HttpClient _client;

        public AdminControllerIntegrationTests(WebApplicationFactory<Program> factory)
        {
            _factory = factory.WithWebHostBuilder(builder =>
            {
                builder.ConfigureServices(services =>
                {
                    // Remove the existing DbContext registration
                    var descriptor = services.SingleOrDefault(
                        d => d.ServiceType == typeof(DbContextOptions<UserManagementDbContext>));
                    if (descriptor != null)
                        services.Remove(descriptor);

                    // Add in-memory database for testing
                    services.AddDbContext<UserManagementDbContext>(options =>
                    {
                        options.UseInMemoryDatabase("TestDb");
                    });

                    // Build the service provider
                    var sp = services.BuildServiceProvider();

                    // Create a scope to obtain a reference to the database context
                    using var scope = sp.CreateScope();
                    var scopedServices = scope.ServiceProvider;
                    var db = scopedServices.GetRequiredService<UserManagementDbContext>();
                    var logger = scopedServices.GetRequiredService<ILogger<AdminControllerIntegrationTests>>();

                    // Ensure the database is created
                    db.Database.EnsureCreated();

                    try
                    {
                        // Seed the database with test data
                        SeedTestData(db);
                    }
                    catch (Exception ex)
                    {
                        logger.LogError(ex, "An error occurred seeding the database with test messages. Error: {Message}", ex.Message);
                    }
                });
            });

            _client = _factory.CreateClient();
        }

        [Fact]
        public async Task GetDashboardStats_WithoutAuth_ShouldReturnUnauthorized()
        {
            // Act
            var response = await _client.GetAsync("/api/admin/dashboard-stats");

            // Assert
            Assert.Equal(System.Net.HttpStatusCode.Unauthorized, response.StatusCode);
        }

        [Fact]
        public async Task GetDashboardStats_WithValidAuth_ShouldReturnStats()
        {
            // Arrange
            var token = GenerateJwtToken("<EMAIL>", "Admin");
            _client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            // Act
            var response = await _client.GetAsync("/api/admin/dashboard-stats");

            // Assert
            Assert.Equal(System.Net.HttpStatusCode.OK, response.StatusCode);

            var content = await response.Content.ReadAsStringAsync();
            var stats = JsonSerializer.Deserialize<DashboardStatsResponse>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            Assert.NotNull(stats);
            Assert.True(stats.TotalUsers >= 0);
            Assert.True(stats.PendingApprovals >= 0);
            Assert.True(stats.PendingDocuments >= 0);
        }

        [Fact]
        public async Task GetPendingApprovals_WithValidAuth_ShouldReturnPendingApprovals()
        {
            // Arrange
            var token = GenerateJwtToken("<EMAIL>", "Admin");
            _client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            // Act
            var response = await _client.GetAsync("/api/admin/pending-approvals?pageNumber=1&pageSize=10");

            // Assert
            Assert.Equal(System.Net.HttpStatusCode.OK, response.StatusCode);

            var content = await response.Content.ReadAsStringAsync();
            Assert.NotNull(content);
            // Additional assertions can be added based on the expected response structure
        }

        private static void SeedTestData(UserManagementDbContext context)
        {
            // Add test user profiles
            var userProfile1 = new UserProfile(Guid.NewGuid(), UserType.Driver, "<EMAIL>");
            userProfile1.UpdatePersonalDetails("John", "Doe", "**********");
            userProfile1.UpdateAddress("123 Main St", "City", "State", "12345");
            userProfile1.SetAadharNumber("**********12");
            userProfile1.SetLicenseNumber("DL123456789");
            userProfile1.SubmitForReview();

            var userProfile2 = new UserProfile(Guid.NewGuid(), UserType.Carrier, "<EMAIL>");
            userProfile2.UpdatePersonalDetails("Jane", "Smith", "**********");
            userProfile2.UpdateAddress("456 Oak Ave", "City", "State", "54321");
            userProfile2.SetAadharNumber("21**********");

            context.UserProfiles.AddRange(userProfile1, userProfile2);

            // Add test document submissions
            var docSubmission1 = new DocumentSubmission(userProfile1.UserId, UserType.Driver);
            var docSubmission2 = new DocumentSubmission(userProfile2.UserId, UserType.Carrier);

            context.DocumentSubmissions.AddRange(docSubmission1, docSubmission2);

            context.SaveChanges();
        }

        private static string GenerateJwtToken(string email, string role)
        {
            // This is a simplified JWT token generation for testing
            // In a real scenario, you would use the same JWT generation logic as your application
            var tokenHandler = new System.IdentityModel.Tokens.Jwt.JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes("your-test-secret-key-that-is-long-enough-for-hmac-sha256");
            var tokenDescriptor = new Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor
            {
                Subject = new System.Security.Claims.ClaimsIdentity(new[]
                {
                    new System.Security.Claims.Claim(System.Security.Claims.ClaimTypes.NameIdentifier, Guid.NewGuid().ToString()),
                    new System.Security.Claims.Claim(System.Security.Claims.ClaimTypes.Email, email),
                    new System.Security.Claims.Claim(System.Security.Claims.ClaimTypes.Role, role)
                }),
                Expires = DateTime.UtcNow.AddHours(1),
                SigningCredentials = new Microsoft.IdentityModel.Tokens.SigningCredentials(
                    new Microsoft.IdentityModel.Tokens.SymmetricSecurityKey(key),
                    Microsoft.IdentityModel.Tokens.SecurityAlgorithms.HmacSha256Signature)
            };
            var token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }

        // Helper class for deserialization
        public class DashboardStatsResponse
        {
            public int TotalUsers { get; set; }
            public int PendingApprovals { get; set; }
            public int PendingDocuments { get; set; }
            public int ApprovedToday { get; set; }
            public int RejectedToday { get; set; }
        }
    }
}
