using MediatR;
using SubscriptionManagement.Application.DTOs.Reports;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Application.Commands.Reports;

/// <summary>
/// Command to generate subscription usage report
/// </summary>
public class GenerateSubscriptionUsageReportCommand : IRequest<SubscriptionUsageReportDto>
{
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public Guid? UserId { get; set; }
    public Guid? PlanId { get; set; }
    public SubscriptionStatus? Status { get; set; }
    public FeatureType? FeatureType { get; set; }
    public ReportFormat Format { get; set; } = ReportFormat.Json;
    public bool IncludeUsageDetails { get; set; } = true;
    public bool IncludeFeatureBreakdown { get; set; } = true;
    public bool IncludeBillingAnalysis { get; set; } = true;
    public bool IncludeUsageTrends { get; set; } = false;
    public int? MaxRecords { get; set; }
    public string? GroupBy { get; set; } // User, Plan, Feature, BillingPeriod
    public Guid RequestedBy { get; set; }
    public string RequestedByRole { get; set; } = string.Empty;
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
}

/// <summary>
/// Enum for report formats
/// </summary>
public enum ReportFormat
{
    Json = 0,
    Csv = 1,
    Excel = 2,
    Pdf = 3
}
