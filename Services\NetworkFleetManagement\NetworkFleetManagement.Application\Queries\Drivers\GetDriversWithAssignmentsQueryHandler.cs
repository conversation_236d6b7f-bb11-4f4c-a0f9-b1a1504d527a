using AutoMapper;
using MediatR;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Repositories;

namespace NetworkFleetManagement.Application.Queries.Drivers;

public class GetDriversWithAssignmentsQueryHandler : IRequestHandler<GetDriversWithAssignmentsQuery, PagedResult<DriverDashboardDto>>
{
    private readonly NetworkFleetDbContext _context;
    private readonly IMapper _mapper;

    public GetDriversWithAssignmentsQueryHandler(NetworkFleetDbContext context, IMapper mapper)
    {
        _context = context;
        _mapper = mapper;
    }

    public async Task<PagedResult<DriverDashboardDto>> Handle(GetDriversWithAssignmentsQuery request, CancellationToken cancellationToken)
    {
        var query = _context.Drivers
            .Include(d => d.Carrier)
            .Include(d => d.Documents)
            .Include(d => d.VehicleAssignments.Where(va => va.IsActive))
                .ThenInclude(va => va.Vehicle)
            .Where(d => d.CarrierId == request.CarrierId);

        // Apply filters
        if (request.Status.HasValue)
        {
            query = query.Where(d => d.Status == request.Status.Value);
        }

        if (request.OnboardingStatus.HasValue)
        {
            query = query.Where(d => d.OnboardingStatus == request.OnboardingStatus.Value);
        }

        if (request.IsAssigned.HasValue)
        {
            if (request.IsAssigned.Value)
            {
                query = query.Where(d => d.VehicleAssignments.Any(va => va.IsActive));
            }
            else
            {
                query = query.Where(d => !d.VehicleAssignments.Any(va => va.IsActive));
            }
        }

        if (request.HasExpiredLicense.HasValue)
        {
            var now = DateTime.UtcNow;
            if (request.HasExpiredLicense.Value)
            {
                query = query.Where(d => d.LicenseExpiryDate <= now);
            }
            else
            {
                query = query.Where(d => d.LicenseExpiryDate > now);
            }
        }

        if (request.IsActive.HasValue)
        {
            var activeThreshold = DateTime.UtcNow.AddHours(-24);
            if (request.IsActive.Value)
            {
                query = query.Where(d => d.LastActiveAt.HasValue && d.LastActiveAt.Value >= activeThreshold);
            }
            else
            {
                query = query.Where(d => !d.LastActiveAt.HasValue || d.LastActiveAt.Value < activeThreshold);
            }
        }

        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            var searchTerm = request.SearchTerm.ToLower();
            query = query.Where(d =>
                d.FirstName.ToLower().Contains(searchTerm) ||
                d.LastName.ToLower().Contains(searchTerm) ||
                d.LicenseNumber.ToLower().Contains(searchTerm) ||
                d.PhoneNumber.Contains(searchTerm) ||
                d.Email.ToLower().Contains(searchTerm));
        }

        // Get total count
        var totalCount = await query.CountAsync(cancellationToken);

        // Apply pagination and get results
        var drivers = await query
            .OrderByDescending(d => d.CreatedAt)
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .ToListAsync(cancellationToken);

        // Map to DTOs with enhanced information
        var driverDashboardDtos = drivers.Select(driver =>
        {
            var currentAssignment = driver.VehicleAssignments.FirstOrDefault(va => va.IsActive);
            var now = DateTime.UtcNow;

            // Calculate license status
            var daysUntilLicenseExpiry = (int)(driver.LicenseExpiryDate - now).TotalDays;
            var licenseStatus = daysUntilLicenseExpiry < 0 ? "Expired" :
                               daysUntilLicenseExpiry <= 30 ? "Expiring" : "Valid";
            var licenseStatusColor = licenseStatus == "Expired" ? "Red" :
                                   licenseStatus == "Expiring" ? "Orange" : "Green";

            // Calculate activity status
            var isActive = driver.LastActiveAt.HasValue && driver.LastActiveAt.Value >= now.AddHours(-24);

            // Calculate document status (placeholder - would need actual document entities)
            var expiredDocumentsCount = 0;
            var expiringDocumentsCount = 0;
            // This would be calculated based on actual document entities when available

            // Calculate performance metrics
            var completionRate = driver.PerformanceMetrics.TotalTrips > 0
                ? (decimal)driver.PerformanceMetrics.CompletedTrips / driver.PerformanceMetrics.TotalTrips * 100
                : 0;
            var onTimeDeliveryRate = driver.PerformanceMetrics.OnTimeDeliveryRate;

            return new DriverDashboardDto
            {
                Id = driver.Id,
                CarrierId = driver.CarrierId,
                UserId = driver.UserId,
                FirstName = driver.FirstName,
                LastName = driver.LastName,
                FullName = driver.FullName,
                PhoneNumber = driver.PhoneNumber,
                Email = driver.Email,
                LicenseNumber = driver.LicenseNumber,
                LicenseExpiryDate = driver.LicenseExpiryDate,
                Status = driver.Status,
                OnboardingStatus = driver.OnboardingStatus,
                CarrierCompanyName = driver.Carrier.CompanyName,

                // License Status
                IsLicenseValid = driver.IsLicenseValid,
                DaysUntilLicenseExpiry = daysUntilLicenseExpiry,
                LicenseStatus = licenseStatus,
                LicenseStatusColor = licenseStatusColor,

                // Performance Metrics
                PerformanceMetrics = _mapper.Map<PerformanceMetricsDto>(driver.PerformanceMetrics),
                Rating = driver.PerformanceMetrics.Rating,
                TotalTrips = driver.PerformanceMetrics.TotalTrips,
                CompletedTrips = driver.PerformanceMetrics.CompletedTrips,
                CompletionRate = completionRate,
                OnTimeDeliveryRate = onTimeDeliveryRate,

                // Assignment Information
                CurrentVehicleId = currentAssignment?.VehicleId,
                CurrentVehicleRegistration = currentAssignment?.Vehicle?.RegistrationNumber,
                CurrentVehicleDisplayName = currentAssignment?.Vehicle?.DisplayName,
                IsAssignedToVehicle = currentAssignment != null,
                AssignmentStartDate = currentAssignment?.AssignedAt,

                // Trip Information (placeholder - would need integration with Trip Management)
                CurrentTripId = null,
                CurrentTripNumber = null,
                CurrentTripStatus = null,
                IsOnTrip = driver.Status == Domain.Enums.DriverStatus.OnTrip,

                // Location and Activity
                CurrentLocation = driver.CurrentLocation != null ? _mapper.Map<LocationDto>(driver.CurrentLocation) : null,
                LastActiveAt = driver.LastActiveAt,
                IsActive = isActive,

                // Verification and Compliance
                IsVerified = driver.IsVerified,
                VerifiedAt = driver.VerifiedAt,
                IsAvailable = driver.IsAvailable,
                DocumentCount = driver.Documents.Count,
                ExpiredDocumentsCount = expiredDocumentsCount,
                ExpiringDocumentsCount = expiringDocumentsCount,

                // Profile Information
                ProfilePhotoUrl = driver.ProfilePhotoUrl,
                PreferredRoutes = driver.PreferredRoutes.ToList(),
                OperationalAreas = driver.OperationalAreas.ToList(),

                CreatedAt = driver.CreatedAt,
                UpdatedAt = driver.UpdatedAt
            };
        }).ToList();

        return new PagedResult<DriverDashboardDto>
        {
            Items = driverDashboardDtos,
            TotalCount = totalCount,
            PageNumber = request.PageNumber,
            PageSize = request.PageSize
        };
    }
}
