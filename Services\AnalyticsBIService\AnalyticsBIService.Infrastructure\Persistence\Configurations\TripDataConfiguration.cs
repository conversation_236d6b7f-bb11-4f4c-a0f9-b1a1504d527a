using AnalyticsBIService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace AnalyticsBIService.Infrastructure.Persistence.Configurations;

/// <summary>
/// Entity configuration for TripData
/// </summary>
public class TripDataConfiguration : IEntityTypeConfiguration<TripData>
{
    public void Configure(EntityTypeBuilder<TripData> builder)
    {
        builder.ToTable("trip_data");

        builder.HasKey(td => td.Id);

        builder.Property(td => td.TripNumber)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(td => td.OrderId)
            .IsRequired();

        builder.Property(td => td.DriverId)
            .IsRequired();

        builder.Property(td => td.VehicleId)
            .IsRequired();

        builder.Property(td => td.Status)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(td => td.StartTime)
            .IsRequired();

        builder.Property(td => td.EndTime);

        builder.Property(td => td.OriginLocation)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(td => td.DestinationLocation)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(td => td.PlannedDistance)
            .HasPrecision(10, 2);

        builder.Property(td => td.ActualDistance)
            .HasPrecision(10, 2);

        builder.Property(td => td.PlannedDuration)
            .HasPrecision(8, 2);

        builder.Property(td => td.ActualDuration)
            .HasPrecision(8, 2);

        builder.Property(td => td.FuelConsumed)
            .HasPrecision(8, 2);

        builder.Property(td => td.FuelCost)
            .HasPrecision(10, 2);

        builder.Property(td => td.TollCharges)
            .HasPrecision(10, 2);

        builder.Property(td => td.OtherExpenses)
            .HasPrecision(10, 2);

        builder.Property(td => td.AverageSpeed)
            .HasPrecision(6, 2);

        builder.Property(td => td.MaxSpeed)
            .HasPrecision(6, 2);

        builder.Property(td => td.IdleTime)
            .IsRequired();

        builder.Property(td => td.RestStops)
            .IsRequired();

        builder.Property(td => td.HasIncidents)
            .IsRequired();

        builder.Property(td => td.IncidentDetails)
            .HasMaxLength(2000);

        builder.Property(td => td.DriverRating)
            .HasPrecision(3, 2);

        // Indexes for analytics queries
        builder.HasIndex(td => td.StartTime)
            .HasDatabaseName("IX_TripData_StartTime");

        builder.HasIndex(td => td.OrderId)
            .HasDatabaseName("IX_TripData_OrderId");

        builder.HasIndex(td => td.DriverId)
            .HasDatabaseName("IX_TripData_DriverId");

        builder.HasIndex(td => td.VehicleId)
            .HasDatabaseName("IX_TripData_VehicleId");

        builder.HasIndex(td => td.Status)
            .HasDatabaseName("IX_TripData_Status");

        builder.HasIndex(td => new { td.OriginLocation, td.DestinationLocation })
            .HasDatabaseName("IX_TripData_Route");

        builder.HasIndex(td => new { td.StartTime, td.Status })
            .HasDatabaseName("IX_TripData_StartTime_Status");

        builder.HasIndex(td => new { td.DriverId, td.StartTime })
            .HasDatabaseName("IX_TripData_DriverId_StartTime");
    }
}
