using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Domain.Enums;

namespace AnalyticsBIService.Infrastructure.Services;

/// <summary>
/// Interface for custom report builder service
/// </summary>
public interface ICustomReportBuilderService
{
    /// <summary>
    /// Create a new custom report template
    /// </summary>
    Task<CustomReportTemplateDto> CreateReportTemplateAsync(CreateReportTemplateRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update an existing report template
    /// </summary>
    Task<CustomReportTemplateDto> UpdateReportTemplateAsync(Guid templateId, UpdateReportTemplateRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete a report template
    /// </summary>
    Task<bool> DeleteReportTemplateAsync(Guid templateId, Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get report template by ID
    /// </summary>
    Task<CustomReportTemplateDto?> GetReportTemplateAsync(Guid templateId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all report templates for a user
    /// </summary>
    Task<List<CustomReportTemplateDto>> GetUserReportTemplatesAsync(Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Generate report from template
    /// </summary>
    Task<CustomReportResultDto> GenerateReportAsync(Guid templateId, GenerateReportRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get available data sources for report building
    /// </summary>
    Task<List<DataSourceDto>> GetAvailableDataSourcesAsync(Guid userId, UserType userType, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get available fields for a data source
    /// </summary>
    Task<List<DataFieldDto>> GetDataSourceFieldsAsync(string dataSourceName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Preview report data with filters
    /// </summary>
    Task<ReportPreviewDto> PreviewReportDataAsync(ReportPreviewRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get available visualization types
    /// </summary>
    Task<List<VisualizationTypeDto>> GetVisualizationTypesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate report configuration
    /// </summary>
    Task<ReportValidationResult> ValidateReportConfigurationAsync(ReportConfigurationDto configuration, CancellationToken cancellationToken = default);

    /// <summary>
    /// Clone an existing report template
    /// </summary>
    Task<CustomReportTemplateDto> CloneReportTemplateAsync(Guid templateId, Guid userId, string newName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get report execution history
    /// </summary>
    Task<List<ReportExecutionDto>> GetReportExecutionHistoryAsync(Guid templateId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Custom report template data transfer object
/// </summary>
public class CustomReportTemplateDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public Guid CreatedBy { get; set; }
    public UserType UserType { get; set; }
    public ReportConfigurationDto Configuration { get; set; } = new();
    public bool IsPublic { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public int ExecutionCount { get; set; }
    public DateTime? LastExecuted { get; set; }
}



/// <summary>
/// Data source configuration
/// </summary>
public class DataSourceConfigDto
{
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public Dictionary<string, object> Parameters { get; set; } = new();
    public List<string> JoinFields { get; set; } = new();
}

/// <summary>
/// Report field configuration
/// </summary>
public class ReportFieldDto
{
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string DataType { get; set; } = string.Empty;
    public string DataSource { get; set; } = string.Empty;
    public string AggregationType { get; set; } = string.Empty;
    public bool IsVisible { get; set; } = true;
    public int Order { get; set; }
    public ReportFieldFormattingDto Formatting { get; set; } = new();
}



/// <summary>
/// Report grouping configuration
/// </summary>
public class ReportGroupingDto
{
    public string FieldName { get; set; } = string.Empty;
    public string GroupType { get; set; } = string.Empty;
    public int Order { get; set; }
    public bool ShowSubtotals { get; set; }
}

/// <summary>
/// Report sorting configuration
/// </summary>
public class ReportSortingDto
{
    public string FieldName { get; set; } = string.Empty;
    public string Direction { get; set; } = "ASC";
    public int Order { get; set; }
}

/// <summary>
/// Visualization configuration
/// </summary>
public class VisualizationConfigDto
{
    public string Type { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public List<string> XAxisFields { get; set; } = new();
    public List<string> YAxisFields { get; set; } = new();
    public List<string> SeriesFields { get; set; } = new();
    public Dictionary<string, object> Options { get; set; } = new();
    public int Width { get; set; } = 12;
    public int Height { get; set; } = 400;
    public int Order { get; set; }
}

/// <summary>
/// Report layout configuration
/// </summary>
public class ReportLayoutDto
{
    public string Type { get; set; } = "Standard";
    public bool ShowHeader { get; set; } = true;
    public bool ShowFooter { get; set; } = true;
    public bool ShowPageNumbers { get; set; } = true;
    public string Orientation { get; set; } = "Portrait";
    public string PageSize { get; set; } = "A4";
    public Dictionary<string, object> CustomSettings { get; set; } = new();
}



/// <summary>
/// Report field formatting
/// </summary>
public class ReportFieldFormattingDto
{
    public string Format { get; set; } = string.Empty;
    public string Alignment { get; set; } = "Left";
    public string Color { get; set; } = string.Empty;
    public string BackgroundColor { get; set; } = string.Empty;
    public bool IsBold { get; set; }
    public bool IsItalic { get; set; }
    public int Width { get; set; }
}

/// <summary>
/// Create report template request
/// </summary>
public class CreateReportTemplateRequest
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public Guid UserId { get; set; }
    public UserType UserType { get; set; }
    public ReportConfigurationDto Configuration { get; set; } = new();
    public bool IsPublic { get; set; }
}

/// <summary>
/// Update report template request
/// </summary>
public class UpdateReportTemplateRequest
{
    public string? Name { get; set; }
    public string? Description { get; set; }
    public ReportConfigurationDto? Configuration { get; set; }
    public bool? IsPublic { get; set; }
    public bool? IsActive { get; set; }
}

/// <summary>
/// Generate report request
/// </summary>
public class GenerateReportRequest
{
    public Guid UserId { get; set; }
    public Dictionary<string, object> ParameterValues { get; set; } = new();
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public string OutputFormat { get; set; } = "JSON";
    public bool IncludeVisualizations { get; set; } = true;
}

/// <summary>
/// Custom report result
/// </summary>
public class CustomReportResultDto
{
    public Guid ReportId { get; set; }
    public string ReportName { get; set; } = string.Empty;
    public DateTime GeneratedAt { get; set; }
    public Guid GeneratedBy { get; set; }
    public List<ReportSectionResultDto> Sections { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
    public string OutputFormat { get; set; } = string.Empty;
    public int TotalRecords { get; set; }
    public TimeSpan ExecutionTime { get; set; }
}

/// <summary>
/// Report section result
/// </summary>
public class ReportSectionResultDto
{
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public List<Dictionary<string, object>> Data { get; set; } = new();
    public VisualizationResultDto? Visualization { get; set; }
    public Dictionary<string, object> Summary { get; set; } = new();
}

/// <summary>
/// Visualization result
/// </summary>
public class VisualizationResultDto
{
    public string Type { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public Dictionary<string, object> ChartData { get; set; } = new();
    public Dictionary<string, object> Options { get; set; } = new();
}

/// <summary>
/// Data source information
/// </summary>
public class DataSourceDto
{
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public List<string> SupportedOperations { get; set; } = new();
    public bool RequiresParameters { get; set; }
    public List<DataSourceParameterDto> Parameters { get; set; } = new();
}

/// <summary>
/// Data source parameter
/// </summary>
public class DataSourceParameterDto
{
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public bool IsRequired { get; set; }
    public object? DefaultValue { get; set; }
    public List<object> AllowedValues { get; set; } = new();
}

/// <summary>
/// Data field information
/// </summary>
public class DataFieldDto
{
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string DataType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public bool IsAggregatable { get; set; }
    public bool IsFilterable { get; set; }
    public bool IsSortable { get; set; }
    public List<string> SupportedAggregations { get; set; } = new();
}

/// <summary>
/// Report preview request
/// </summary>
public class ReportPreviewRequest
{
    public List<DataSourceConfigDto> DataSources { get; set; } = new();
    public List<ReportFieldDto> Fields { get; set; } = new();
    public List<ReportFilterDto> Filters { get; set; } = new();
    public int MaxRows { get; set; } = 100;
    public Guid UserId { get; set; }
}

/// <summary>
/// Report preview result
/// </summary>
public class ReportPreviewDto
{
    public List<Dictionary<string, object>> Data { get; set; } = new();
    public List<ReportFieldDto> Fields { get; set; } = new();
    public int TotalRows { get; set; }
    public bool HasMoreData { get; set; }
    public TimeSpan ExecutionTime { get; set; }
}

/// <summary>
/// Visualization type information
/// </summary>
public class VisualizationTypeDto
{
    public string Type { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public List<string> RequiredFields { get; set; } = new();
    public List<string> OptionalFields { get; set; } = new();
    public Dictionary<string, object> DefaultOptions { get; set; } = new();
}

/// <summary>
/// Report validation result
/// </summary>
public class ReportValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public List<string> Suggestions { get; set; } = new();
}

/// <summary>
/// Report execution history
/// </summary>
public class ReportExecutionDto
{
    public Guid Id { get; set; }
    public Guid ReportTemplateId { get; set; }
    public Guid ExecutedBy { get; set; }
    public DateTime ExecutedAt { get; set; }
    public TimeSpan ExecutionTime { get; set; }
    public int RecordsReturned { get; set; }
    public string Status { get; set; } = string.Empty;
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object> Parameters { get; set; } = new();
}
