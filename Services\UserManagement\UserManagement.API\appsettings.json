{"ConnectionStrings": {"DefaultConnection": "Host=**************;Port=5432;Database=TLI_UserManagement;Username=postgres;Password=************"}, "JwtSettings": {"Secret": "YourSuperSecretKeyThatIsAtLeast32CharactersLong!", "Issuer": "TLI.Identity", "Audience": "TLI.Services", "ExpiryInMinutes": 60}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/usermanagement-.txt", "rollingInterval": "Day", "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext"]}, "AllowedHosts": "*"}