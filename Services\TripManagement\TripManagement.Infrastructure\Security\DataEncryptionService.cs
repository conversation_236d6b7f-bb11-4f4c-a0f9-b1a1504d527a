using Microsoft.Extensions.Options;
using System.Security.Cryptography;
using System.Text;
using TripManagement.Application.Interfaces;

namespace TripManagement.Infrastructure.Security;

public class DataEncryptionService : IDataEncryptionService
{
    private readonly EncryptionConfiguration _config;

    public DataEncryptionService(IOptions<EncryptionConfiguration> config)
    {
        _config = config.Value;
    }

    public string EncryptSensitiveData(string plainText)
    {
        if (string.IsNullOrEmpty(plainText))
            return plainText;

        try
        {
            using var aes = Aes.Create();
            aes.Key = Convert.FromBase64String(_config.EncryptionKey);
            aes.IV = new byte[16]; // Use zero IV for deterministic encryption (consider using random IV for better security)

            using var encryptor = aes.CreateEncryptor();
            using var msEncrypt = new MemoryStream();
            using var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write);
            using var swEncrypt = new StreamWriter(csEncrypt);

            swEncrypt.Write(plainText);
            swEncrypt.Close();

            return Convert.ToBase64String(msEncrypt.ToArray());
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException("Failed to encrypt sensitive data", ex);
        }
    }

    public string DecryptSensitiveData(string cipherText)
    {
        if (string.IsNullOrEmpty(cipherText))
            return cipherText;

        try
        {
            using var aes = Aes.Create();
            aes.Key = Convert.FromBase64String(_config.EncryptionKey);
            aes.IV = new byte[16]; // Use same IV as encryption

            using var decryptor = aes.CreateDecryptor();
            using var msDecrypt = new MemoryStream(Convert.FromBase64String(cipherText));
            using var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read);
            using var srDecrypt = new StreamReader(csDecrypt);

            return srDecrypt.ReadToEnd();
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException("Failed to decrypt sensitive data", ex);
        }
    }

    public string HashSensitiveData(string data)
    {
        if (string.IsNullOrEmpty(data))
            return data;

        try
        {
            using var sha256 = SHA256.Create();
            var saltBytes = Encoding.UTF8.GetBytes(_config.HashSalt);
            var dataBytes = Encoding.UTF8.GetBytes(data);
            var combinedBytes = new byte[saltBytes.Length + dataBytes.Length];
            
            Buffer.BlockCopy(saltBytes, 0, combinedBytes, 0, saltBytes.Length);
            Buffer.BlockCopy(dataBytes, 0, combinedBytes, saltBytes.Length, dataBytes.Length);

            var hashBytes = sha256.ComputeHash(combinedBytes);
            return Convert.ToBase64String(hashBytes);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException("Failed to hash sensitive data", ex);
        }
    }

    public bool VerifyHashedData(string data, string hash)
    {
        if (string.IsNullOrEmpty(data) || string.IsNullOrEmpty(hash))
            return false;

        try
        {
            var computedHash = HashSensitiveData(data);
            return string.Equals(computedHash, hash, StringComparison.Ordinal);
        }
        catch
        {
            return false;
        }
    }

    public string MaskSensitiveData(string data, int visibleChars = 4)
    {
        if (string.IsNullOrEmpty(data))
            return data;

        if (data.Length <= visibleChars)
            return new string('*', data.Length);

        var visiblePart = data.Substring(0, visibleChars);
        var maskedPart = new string('*', data.Length - visibleChars);
        return visiblePart + maskedPart;
    }

    public string GenerateSecureToken(int length = 32)
    {
        try
        {
            using var rng = RandomNumberGenerator.Create();
            var bytes = new byte[length];
            rng.GetBytes(bytes);
            return Convert.ToBase64String(bytes);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException("Failed to generate secure token", ex);
        }
    }
}

public class EncryptionConfiguration
{
    public string EncryptionKey { get; set; } = string.Empty;
    public string HashSalt { get; set; } = string.Empty;
}
