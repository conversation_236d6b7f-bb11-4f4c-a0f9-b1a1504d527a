using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using FinancialPayment.Domain.Entities;

namespace FinancialPayment.Infrastructure.Data.Configurations;

public class DisputeDocumentConfiguration : IEntityTypeConfiguration<DisputeDocument>
{
    public void Configure(EntityTypeBuilder<DisputeDocument> builder)
    {
        builder.ToTable("dispute_documents");

        builder.HasKey(d => d.Id);

        builder.Property(d => d.Id)
            .HasColumnName("id")
            .ValueGeneratedNever();

        builder.Property(d => d.DisputeId)
            .HasColumnName("dispute_id")
            .IsRequired();

        builder.Property(d => d.FileName)
            .HasColumnName("file_name")
            .HasMaxLength(255)
            .IsRequired();

        builder.Property(d => d.FileUrl)
            .HasColumnName("file_url")
            .HasMaxLength(500)
            .IsRequired();

        builder.Property(d => d.Description)
            .HasColumnName("description")
            .HasMaxLength(500);

        builder.Property(d => d.UploadedBy)
            .HasColumnName("uploaded_by")
            .IsRequired();

        builder.Property(d => d.UploadedAt)
            .HasColumnName("uploaded_at")
            .IsRequired();

        // Indexes
        builder.HasIndex(d => d.DisputeId)
            .HasDatabaseName("ix_dispute_documents_dispute_id");

        builder.HasIndex(d => d.UploadedBy)
            .HasDatabaseName("ix_dispute_documents_uploaded_by");

        builder.HasIndex(d => d.UploadedAt)
            .HasDatabaseName("ix_dispute_documents_uploaded_at");
    }
}
