using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using NetworkFleetManagement.Domain.Entities;
using NetworkFleetManagement.Domain.Repositories;
using NetworkFleetManagement.Infrastructure.Performance;
using NetworkFleetManagement.PerformanceTests.Infrastructure;
using System.Diagnostics;
using Xunit;

namespace NetworkFleetManagement.PerformanceTests.StressTests;

public class DatabaseStressTests : PerformanceTestBase
{
    [Fact]
    public async Task DatabaseConnections_StressTest_HandlesHighConcurrency()
    {
        // Arrange
        const int concurrentTasks = 50;
        const int operationsPerTask = 20;
        var tasks = new List<Task>();
        var results = new List<TimeSpan>();
        var errors = new List<Exception>();

        // Act
        for (int i = 0; i < concurrentTasks; i++)
        {
            var taskId = i;
            tasks.Add(Task.Run(async () =>
            {
                try
                {
                    using var scope = ServiceProvider.CreateScope();
                    var repository = scope.ServiceProvider.GetRequiredService<IPreferredPartnerRepository>();
                    var stopwatch = Stopwatch.StartNew();

                    for (int j = 0; j < operationsPerTask; j++)
                    {
                        var userId = Guid.NewGuid();
                        var partners = await repository.GetByUserIdAsync(userId);
                        // Simulate some processing
                        await Task.Delay(1);
                    }

                    stopwatch.Stop();
                    lock (results)
                    {
                        results.Add(stopwatch.Elapsed);
                    }
                }
                catch (Exception ex)
                {
                    lock (errors)
                    {
                        errors.Add(ex);
                    }
                }
            }));
        }

        await Task.WhenAll(tasks);

        // Assert
        errors.Should().BeEmpty("No database connection errors should occur under stress");
        results.Should().HaveCount(concurrentTasks);
        
        var averageTime = results.Average(r => r.TotalMilliseconds);
        var maxTime = results.Max(r => r.TotalMilliseconds);
        
        averageTime.Should().BeLessThan(5000, "Average task completion time should be under 5 seconds");
        maxTime.Should().BeLessThan(10000, "Maximum task completion time should be under 10 seconds");

        Logger.LogInformation("Database stress test completed. Average: {AvgMs}ms, Max: {MaxMs}ms, Errors: {ErrorCount}",
            averageTime, maxTime, errors.Count);
    }

    [Fact]
    public async Task QueryOptimization_StressTest_MaintainsPerformance()
    {
        // Arrange
        await SeedLargeDatasetAsync();
        
        using var scope = ServiceProvider.CreateScope();
        var optimizationService = scope.ServiceProvider.GetRequiredService<IQueryOptimizationService>();
        
        const int iterations = 100;
        var queryTimes = new List<TimeSpan>();

        // Act
        for (int i = 0; i < iterations; i++)
        {
            var userId = Guid.NewGuid();
            var metrics = await optimizationService.AnalyzeQueryPerformanceAsync(
                "OptimizedPreferredPartners",
                async () =>
                {
                    await optimizationService.GetOptimizedPreferredPartnersAsync(
                        userId, 
                        PreferredPartnerType.Carrier, 
                        activeOnly: true, 
                        limit: 20);
                });

            queryTimes.Add(metrics.ExecutionTime);
        }

        // Assert
        var averageQueryTime = queryTimes.Average(t => t.TotalMilliseconds);
        var p95QueryTime = queryTimes.OrderBy(t => t.TotalMilliseconds).Skip((int)(iterations * 0.95)).First().TotalMilliseconds;

        averageQueryTime.Should().BeLessThan(300, "Average optimized query time should be under 300ms");
        p95QueryTime.Should().BeLessThan(500, "95th percentile query time should be under 500ms");

        Logger.LogInformation("Query optimization stress test completed. Average: {AvgMs}ms, P95: {P95Ms}ms",
            averageQueryTime, p95QueryTime);
    }

    [Fact]
    public async Task AutoAssignmentQueries_StressTest_ScalesWithLoad()
    {
        // Arrange
        await SeedLargeDatasetAsync();
        
        using var scope = ServiceProvider.CreateScope();
        var optimizationService = scope.ServiceProvider.GetRequiredService<IQueryOptimizationService>();
        
        const int concurrentQueries = 25;
        const int queriesPerThread = 10;
        var allQueryTimes = new List<TimeSpan>();
        var tasks = new List<Task>();

        // Act
        for (int i = 0; i < concurrentQueries; i++)
        {
            tasks.Add(Task.Run(async () =>
            {
                var threadQueryTimes = new List<TimeSpan>();
                
                for (int j = 0; j < queriesPerThread; j++)
                {
                    var userId = Guid.NewGuid();
                    var stopwatch = Stopwatch.StartNew();
                    
                    await optimizationService.GetOptimizedAutoAssignPartnersAsync(
                        userId,
                        PreferredPartnerType.Carrier,
                        route: "Route1",
                        loadType: "Dry Van",
                        minRating: 4.0m);
                    
                    stopwatch.Stop();
                    threadQueryTimes.Add(stopwatch.Elapsed);
                }

                lock (allQueryTimes)
                {
                    allQueryTimes.AddRange(threadQueryTimes);
                }
            }));
        }

        await Task.WhenAll(tasks);

        // Assert
        var averageTime = allQueryTimes.Average(t => t.TotalMilliseconds);
        var maxTime = allQueryTimes.Max(t => t.TotalMilliseconds);
        var p99Time = allQueryTimes.OrderBy(t => t.TotalMilliseconds).Skip((int)(allQueryTimes.Count * 0.99)).First().TotalMilliseconds;

        averageTime.Should().BeLessThan(200, "Average auto-assignment query should be under 200ms");
        p99Time.Should().BeLessThan(500, "99th percentile should be under 500ms");

        Logger.LogInformation("Auto-assignment stress test completed. Average: {AvgMs}ms, Max: {MaxMs}ms, P99: {P99Ms}ms",
            averageTime, maxTime, p99Time);
    }

    [Fact]
    public async Task MemoryUsage_StressTest_RemainsWithinLimits()
    {
        // Arrange
        const int iterations = 50;
        var initialMemory = GC.GetTotalMemory(true);
        var memorySnapshots = new List<long>();

        // Act
        for (int i = 0; i < iterations; i++)
        {
            using var scope = ServiceProvider.CreateScope();
            var repository = scope.ServiceProvider.GetRequiredService<IPreferredPartnerRepository>();

            // Perform memory-intensive operations
            var tasks = Enumerable.Range(0, 10).Select(async _ =>
            {
                var userId = Guid.NewGuid();
                var partners = await repository.GetByUserIdAsync(userId);
                var activePartners = await repository.GetActiveByUserIdAsync(userId);
                return partners.Concat(activePartners).ToList();
            });

            await Task.WhenAll(tasks);

            // Force garbage collection and measure memory
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();

            var currentMemory = GC.GetTotalMemory(false);
            memorySnapshots.Add(currentMemory);
        }

        // Assert
        var finalMemory = GC.GetTotalMemory(true);
        var memoryIncrease = finalMemory - initialMemory;
        var maxMemoryUsed = memorySnapshots.Max();
        var avgMemoryUsed = memorySnapshots.Average();

        var memoryIncreaseKB = memoryIncrease / 1024;
        var maxMemoryKB = maxMemoryUsed / 1024;
        var avgMemoryKB = avgMemoryUsed / 1024;

        memoryIncreaseKB.Should().BeLessThan(50 * 1024, "Memory increase should be less than 50MB");
        maxMemoryKB.Should().BeLessThan(500 * 1024, "Maximum memory usage should be less than 500MB");

        Logger.LogInformation("Memory stress test completed. Increase: {IncreaseKB}KB, Max: {MaxKB}KB, Avg: {AvgKB}KB",
            memoryIncreaseKB, maxMemoryKB, avgMemoryKB);
    }

    [Fact]
    public async Task CachePerformance_StressTest_MaintainsHitRates()
    {
        // Arrange
        await SeedLargeDatasetAsync();
        
        using var scope = ServiceProvider.CreateScope();
        var repository = scope.ServiceProvider.GetRequiredService<IPreferredPartnerRepository>();
        
        const int warmupQueries = 50;
        const int testQueries = 200;
        var userIds = Enumerable.Range(0, 20).Select(_ => Guid.NewGuid()).ToList();

        // Warmup cache
        foreach (var userId in userIds.Take(10))
        {
            await repository.GetByUserIdAsync(userId);
            await repository.GetActiveByUserIdAsync(userId);
        }

        var cacheHits = 0;
        var totalQueries = 0;

        // Act - Test cache performance
        var tasks = Enumerable.Range(0, testQueries).Select(async i =>
        {
            var userId = userIds[i % userIds.Count]; // Reuse user IDs to test cache hits
            var stopwatch = Stopwatch.StartNew();
            
            await repository.GetByUserIdAsync(userId);
            
            stopwatch.Stop();
            
            Interlocked.Increment(ref totalQueries);
            
            // Assume cache hit if query is very fast (< 10ms)
            if (stopwatch.ElapsedMilliseconds < 10)
            {
                Interlocked.Increment(ref cacheHits);
            }
        });

        await Task.WhenAll(tasks);

        // Assert
        var cacheHitRate = (double)cacheHits / totalQueries;
        
        cacheHitRate.Should().BeGreaterThan(0.7, "Cache hit rate should be above 70% under stress");
        totalQueries.Should().Be(testQueries);

        Logger.LogInformation("Cache stress test completed. Hit rate: {HitRate:P2}, Hits: {Hits}/{Total}",
            cacheHitRate, cacheHits, totalQueries);
    }

    private async Task SeedLargeDatasetAsync()
    {
        using var scope = ServiceProvider.CreateScope();
        var repository = scope.ServiceProvider.GetRequiredService<IPreferredPartnerRepository>();

        const int usersCount = 100;
        const int partnersPerUser = 10;

        var tasks = new List<Task>();

        for (int i = 0; i < usersCount; i++)
        {
            var userId = Guid.NewGuid();
            
            for (int j = 0; j < partnersPerUser; j++)
            {
                tasks.Add(Task.Run(async () =>
                {
                    var partner = PreferredPartner.Create(
                        userId,
                        Guid.NewGuid(),
                        (PreferredPartnerType)(j % 4),
                        (PreferenceLevel)(j % 3),
                        j + 1);

                    partner.Activate();
                    partner.UpdateAutoAssignSettings(
                        j % 2 == 0,
                        4.0m + (j * 0.1m),
                        new List<string> { $"Route{j % 5 + 1}" },
                        new List<string> { j % 2 == 0 ? "Dry Van" : "Refrigerated" },
                        new List<string>(),
                        new List<string>());

                    await repository.AddAsync(partner);
                }));

                // Batch the operations to avoid overwhelming the database
                if (tasks.Count >= 20)
                {
                    await Task.WhenAll(tasks);
                    tasks.Clear();
                }
            }
        }

        if (tasks.Any())
        {
            await Task.WhenAll(tasks);
        }

        Logger.LogInformation("Seeded {UsersCount} users with {PartnersPerUser} partners each for stress testing",
            usersCount, partnersPerUser);
    }
}
