using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using System.Security.Claims;

namespace Shared.Infrastructure.Security;

/// <summary>
/// Comprehensive authorization service with role-based and policy-based access control
/// Provides fine-grained permissions and resource-level authorization
/// </summary>
public interface IAuthorizationService
{
    Task<bool> AuthorizeAsync(ClaimsPrincipal user, string resource, string action, CancellationToken cancellationToken = default);
    Task<bool> AuthorizeAsync(ClaimsPrincipal user, object resource, string policy, CancellationToken cancellationToken = default);
    Task<AuthorizationResult> CheckPermissionAsync(Guid userId, string permission, CancellationToken cancellationToken = default);
    Task<AuthorizationResult> CheckResourceAccessAsync(Guid userId, string resourceType, Guid resourceId, string action, CancellationToken cancellationToken = default);
    Task<List<string>> GetUserPermissionsAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<List<string>> GetUserRolesAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<bool> HasRoleAsync(Guid userId, string role, CancellationToken cancellationToken = default);
    Task<bool> IsInTenantAsync(Guid userId, Guid tenantId, CancellationToken cancellationToken = default);
    Task<List<Guid>> GetUserAccessibleResourcesAsync(Guid userId, string resourceType, CancellationToken cancellationToken = default);
}

public class AuthorizationService : IAuthorizationService
{
    private readonly IUserPermissionRepository _userPermissionRepository;
    private readonly IRolePermissionRepository _rolePermissionRepository;
    private readonly IResourceAccessRepository _resourceAccessRepository;
    private readonly ITenantRepository _tenantRepository;
    private readonly ILogger<AuthorizationService> _logger;
    private readonly AuthorizationConfiguration _config;

    public AuthorizationService(
        IUserPermissionRepository userPermissionRepository,
        IRolePermissionRepository rolePermissionRepository,
        IResourceAccessRepository resourceAccessRepository,
        ITenantRepository tenantRepository,
        ILogger<AuthorizationService> logger,
        AuthorizationConfiguration config)
    {
        _userPermissionRepository = userPermissionRepository;
        _rolePermissionRepository = rolePermissionRepository;
        _resourceAccessRepository = resourceAccessRepository;
        _tenantRepository = tenantRepository;
        _logger = logger;
        _config = config;
    }

    public async Task<bool> AuthorizeAsync(ClaimsPrincipal user, string resource, string action, CancellationToken cancellationToken = default)
    {
        try
        {
            var userId = GetUserIdFromClaims(user);
            if (userId == Guid.Empty)
            {
                _logger.LogWarning("Authorization failed: Invalid user ID in claims");
                return false;
            }

            // Check if user is active
            var isActive = await IsUserActiveAsync(userId, cancellationToken);
            if (!isActive)
            {
                _logger.LogWarning("Authorization failed: User {UserId} is not active", userId);
                return false;
            }

            // Check super admin role
            if (await HasRoleAsync(userId, "SuperAdmin", cancellationToken))
            {
                _logger.LogDebug("Authorization granted: User {UserId} has SuperAdmin role", userId);
                return true;
            }

            // Check specific permission
            var permission = $"{resource}:{action}";
            var hasPermission = await CheckPermissionAsync(userId, permission, cancellationToken);
            
            if (hasPermission.Success)
            {
                _logger.LogDebug("Authorization granted: User {UserId} has permission {Permission}", userId, permission);
                return true;
            }

            _logger.LogWarning("Authorization denied: User {UserId} lacks permission {Permission}", userId, permission);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during authorization check for user and resource {Resource}:{Action}", resource, action);
            return false;
        }
    }

    public async Task<bool> AuthorizeAsync(ClaimsPrincipal user, object resource, string policy, CancellationToken cancellationToken = default)
    {
        try
        {
            var userId = GetUserIdFromClaims(user);
            if (userId == Guid.Empty)
            {
                return false;
            }

            return policy switch
            {
                "OwnerPolicy" => await CheckOwnershipAsync(userId, resource, cancellationToken),
                "TenantPolicy" => await CheckTenantAccessAsync(userId, resource, cancellationToken),
                "AdminPolicy" => await HasRoleAsync(userId, "Admin", cancellationToken) || await HasRoleAsync(userId, "SuperAdmin", cancellationToken),
                "ManagerPolicy" => await HasAnyRoleAsync(userId, new[] { "Manager", "Admin", "SuperAdmin" }, cancellationToken),
                _ => await EvaluateCustomPolicyAsync(userId, resource, policy, cancellationToken)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during policy-based authorization for policy {Policy}", policy);
            return false;
        }
    }

    public async Task<AuthorizationResult> CheckPermissionAsync(Guid userId, string permission, CancellationToken cancellationToken = default)
    {
        try
        {
            // Check direct user permissions
            var userPermissions = await _userPermissionRepository.GetUserPermissionsAsync(userId, cancellationToken);
            if (userPermissions.Contains(permission))
            {
                return AuthorizationResult.Success();
            }

            // Check role-based permissions
            var userRoles = await GetUserRolesAsync(userId, cancellationToken);
            foreach (var role in userRoles)
            {
                var rolePermissions = await _rolePermissionRepository.GetRolePermissionsAsync(role, cancellationToken);
                if (rolePermissions.Contains(permission))
                {
                    return AuthorizationResult.Success();
                }
            }

            // Check wildcard permissions
            var wildcardPermissions = GetWildcardPermissions(permission);
            foreach (var wildcardPermission in wildcardPermissions)
            {
                if (userPermissions.Contains(wildcardPermission))
                {
                    return AuthorizationResult.Success();
                }

                foreach (var role in userRoles)
                {
                    var rolePermissions = await _rolePermissionRepository.GetRolePermissionsAsync(role, cancellationToken);
                    if (rolePermissions.Contains(wildcardPermission))
                    {
                        return AuthorizationResult.Success();
                    }
                }
            }

            return AuthorizationResult.Failed($"User does not have permission: {permission}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking permission {Permission} for user {UserId}", permission, userId);
            return AuthorizationResult.Failed("Authorization check failed");
        }
    }

    public async Task<AuthorizationResult> CheckResourceAccessAsync(Guid userId, string resourceType, Guid resourceId, string action, CancellationToken cancellationToken = default)
    {
        try
        {
            // Check if user has general permission for this resource type and action
            var generalPermission = $"{resourceType}:{action}";
            var hasGeneralPermission = await CheckPermissionAsync(userId, generalPermission, cancellationToken);
            
            if (!hasGeneralPermission.Success)
            {
                return AuthorizationResult.Failed($"User does not have general permission: {generalPermission}");
            }

            // Check specific resource access
            var hasResourceAccess = await _resourceAccessRepository.HasAccessAsync(userId, resourceType, resourceId, action, cancellationToken);
            if (!hasResourceAccess)
            {
                // Check if user owns the resource
                var isOwner = await _resourceAccessRepository.IsResourceOwnerAsync(userId, resourceType, resourceId, cancellationToken);
                if (!isOwner)
                {
                    // Check tenant-level access
                    var userTenantId = await GetUserTenantIdAsync(userId, cancellationToken);
                    var resourceTenantId = await _resourceAccessRepository.GetResourceTenantIdAsync(resourceType, resourceId, cancellationToken);
                    
                    if (userTenantId == null || userTenantId != resourceTenantId)
                    {
                        return AuthorizationResult.Failed($"User does not have access to {resourceType} {resourceId}");
                    }
                }
            }

            return AuthorizationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking resource access for user {UserId}, resource {ResourceType}:{ResourceId}, action {Action}", 
                userId, resourceType, resourceId, action);
            return AuthorizationResult.Failed("Resource access check failed");
        }
    }

    public async Task<List<string>> GetUserPermissionsAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var permissions = new HashSet<string>();

            // Get direct user permissions
            var userPermissions = await _userPermissionRepository.GetUserPermissionsAsync(userId, cancellationToken);
            foreach (var permission in userPermissions)
            {
                permissions.Add(permission);
            }

            // Get role-based permissions
            var userRoles = await GetUserRolesAsync(userId, cancellationToken);
            foreach (var role in userRoles)
            {
                var rolePermissions = await _rolePermissionRepository.GetRolePermissionsAsync(role, cancellationToken);
                foreach (var permission in rolePermissions)
                {
                    permissions.Add(permission);
                }
            }

            return permissions.ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user permissions for user {UserId}", userId);
            return new List<string>();
        }
    }

    public async Task<List<string>> GetUserRolesAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _userPermissionRepository.GetUserRolesAsync(userId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user roles for user {UserId}", userId);
            return new List<string>();
        }
    }

    public async Task<bool> HasRoleAsync(Guid userId, string role, CancellationToken cancellationToken = default)
    {
        try
        {
            var userRoles = await GetUserRolesAsync(userId, cancellationToken);
            return userRoles.Contains(role, StringComparer.OrdinalIgnoreCase);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking role {Role} for user {UserId}", role, userId);
            return false;
        }
    }

    public async Task<bool> IsInTenantAsync(Guid userId, Guid tenantId, CancellationToken cancellationToken = default)
    {
        try
        {
            var userTenantId = await GetUserTenantIdAsync(userId, cancellationToken);
            return userTenantId == tenantId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking tenant membership for user {UserId} and tenant {TenantId}", userId, tenantId);
            return false;
        }
    }

    public async Task<List<Guid>> GetUserAccessibleResourcesAsync(Guid userId, string resourceType, CancellationToken cancellationToken = default)
    {
        try
        {
            // Get resources user owns
            var ownedResources = await _resourceAccessRepository.GetOwnedResourcesAsync(userId, resourceType, cancellationToken);

            // Get resources user has explicit access to
            var accessibleResources = await _resourceAccessRepository.GetAccessibleResourcesAsync(userId, resourceType, cancellationToken);

            // Get tenant-level accessible resources
            var userTenantId = await GetUserTenantIdAsync(userId, cancellationToken);
            var tenantResources = userTenantId.HasValue 
                ? await _resourceAccessRepository.GetTenantResourcesAsync(userTenantId.Value, resourceType, cancellationToken)
                : new List<Guid>();

            // Combine and deduplicate
            var allResources = new HashSet<Guid>();
            allResources.UnionWith(ownedResources);
            allResources.UnionWith(accessibleResources);
            allResources.UnionWith(tenantResources);

            return allResources.ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting accessible resources for user {UserId} and resource type {ResourceType}", userId, resourceType);
            return new List<Guid>();
        }
    }

    private Guid GetUserIdFromClaims(ClaimsPrincipal user)
    {
        var userIdClaim = user.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return Guid.TryParse(userIdClaim, out var userId) ? userId : Guid.Empty;
    }

    private async Task<bool> IsUserActiveAsync(Guid userId, CancellationToken cancellationToken)
    {
        // This would check user status in the database
        // For now, assume all users are active
        return true;
    }

    private async Task<bool> CheckOwnershipAsync(Guid userId, object resource, CancellationToken cancellationToken)
    {
        // Extract resource information and check ownership
        if (resource is IOwnable ownableResource)
        {
            return ownableResource.OwnerId == userId;
        }

        // Use reflection to check for common ownership properties
        var resourceType = resource.GetType();
        var ownerIdProperty = resourceType.GetProperty("OwnerId") ?? resourceType.GetProperty("CreatedBy") ?? resourceType.GetProperty("UserId");
        
        if (ownerIdProperty != null && ownerIdProperty.PropertyType == typeof(Guid))
        {
            var ownerId = (Guid)ownerIdProperty.GetValue(resource)!;
            return ownerId == userId;
        }

        return false;
    }

    private async Task<bool> CheckTenantAccessAsync(Guid userId, object resource, CancellationToken cancellationToken)
    {
        var userTenantId = await GetUserTenantIdAsync(userId, cancellationToken);
        if (!userTenantId.HasValue)
        {
            return false;
        }

        // Extract tenant information from resource
        if (resource is ITenantScoped tenantScopedResource)
        {
            return tenantScopedResource.TenantId == userTenantId.Value;
        }

        // Use reflection to check for common tenant properties
        var resourceType = resource.GetType();
        var tenantIdProperty = resourceType.GetProperty("TenantId");
        
        if (tenantIdProperty != null && tenantIdProperty.PropertyType == typeof(Guid?))
        {
            var resourceTenantId = (Guid?)tenantIdProperty.GetValue(resource);
            return resourceTenantId == userTenantId.Value;
        }

        return false;
    }

    private async Task<bool> HasAnyRoleAsync(Guid userId, string[] roles, CancellationToken cancellationToken)
    {
        var userRoles = await GetUserRolesAsync(userId, cancellationToken);
        return roles.Any(role => userRoles.Contains(role, StringComparer.OrdinalIgnoreCase));
    }

    private async Task<bool> EvaluateCustomPolicyAsync(Guid userId, object resource, string policy, CancellationToken cancellationToken)
    {
        // Implement custom policy evaluation logic
        // This could involve complex business rules, external service calls, etc.
        return false;
    }

    private async Task<Guid?> GetUserTenantIdAsync(Guid userId, CancellationToken cancellationToken)
    {
        // This would get the user's tenant ID from the database
        // For now, return null
        return null;
    }

    private List<string> GetWildcardPermissions(string permission)
    {
        var wildcards = new List<string>();
        var parts = permission.Split(':');
        
        if (parts.Length == 2)
        {
            // Add resource-level wildcard (e.g., "orders:*")
            wildcards.Add($"{parts[0]}:*");
            
            // Add action-level wildcard (e.g., "*:read")
            wildcards.Add($"*:{parts[1]}");
            
            // Add full wildcard
            wildcards.Add("*:*");
        }

        return wildcards;
    }
}

public class AuthorizationResult
{
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }

    public static AuthorizationResult Success()
    {
        return new AuthorizationResult { Success = true };
    }

    public static AuthorizationResult Failed(string errorMessage)
    {
        return new AuthorizationResult { Success = false, ErrorMessage = errorMessage };
    }
}

public class AuthorizationConfiguration
{
    public bool EnableResourceLevelSecurity { get; set; } = true;
    public bool EnableTenantIsolation { get; set; } = true;
    public bool CachePermissions { get; set; } = true;
    public TimeSpan PermissionCacheExpiry { get; set; } = TimeSpan.FromMinutes(15);
    public List<string> SuperAdminRoles { get; set; } = new() { "SuperAdmin" };
    public List<string> AdminRoles { get; set; } = new() { "Admin", "SuperAdmin" };
}

// Interfaces for resource ownership and tenant scoping
public interface IOwnable
{
    Guid OwnerId { get; }
}

public interface ITenantScoped
{
    Guid TenantId { get; }
}

// Repository interfaces
public interface IUserPermissionRepository
{
    Task<List<string>> GetUserPermissionsAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<List<string>> GetUserRolesAsync(Guid userId, CancellationToken cancellationToken = default);
}

public interface IRolePermissionRepository
{
    Task<List<string>> GetRolePermissionsAsync(string role, CancellationToken cancellationToken = default);
}

public interface IResourceAccessRepository
{
    Task<bool> HasAccessAsync(Guid userId, string resourceType, Guid resourceId, string action, CancellationToken cancellationToken = default);
    Task<bool> IsResourceOwnerAsync(Guid userId, string resourceType, Guid resourceId, CancellationToken cancellationToken = default);
    Task<Guid?> GetResourceTenantIdAsync(string resourceType, Guid resourceId, CancellationToken cancellationToken = default);
    Task<List<Guid>> GetOwnedResourcesAsync(Guid userId, string resourceType, CancellationToken cancellationToken = default);
    Task<List<Guid>> GetAccessibleResourcesAsync(Guid userId, string resourceType, CancellationToken cancellationToken = default);
    Task<List<Guid>> GetTenantResourcesAsync(Guid tenantId, string resourceType, CancellationToken cancellationToken = default);
}

public interface ITenantRepository
{
    Task<Guid?> GetUserTenantIdAsync(Guid userId, CancellationToken cancellationToken = default);
}

// Permission constants
public static class Permissions
{
    // Order Management
    public const string OrdersRead = "orders:read";
    public const string OrdersWrite = "orders:write";
    public const string OrdersDelete = "orders:delete";
    public const string OrdersManage = "orders:manage";

    // Trip Management
    public const string TripsRead = "trips:read";
    public const string TripsWrite = "trips:write";
    public const string TripsDelete = "trips:delete";
    public const string TripsManage = "trips:manage";

    // Financial & Payment
    public const string PaymentsRead = "payments:read";
    public const string PaymentsWrite = "payments:write";
    public const string PaymentsProcess = "payments:process";
    public const string PaymentsManage = "payments:manage";

    // Network & Fleet Management
    public const string CarriersRead = "carriers:read";
    public const string CarriersWrite = "carriers:write";
    public const string CarriersManage = "carriers:manage";
    public const string VehiclesRead = "vehicles:read";
    public const string VehiclesWrite = "vehicles:write";
    public const string VehiclesManage = "vehicles:manage";
    public const string DriversRead = "drivers:read";
    public const string DriversWrite = "drivers:write";
    public const string DriversManage = "drivers:manage";

    // Analytics & BI
    public const string AnalyticsRead = "analytics:read";
    public const string AnalyticsGenerate = "analytics:generate";
    public const string ReportsRead = "reports:read";
    public const string ReportsGenerate = "reports:generate";
    public const string ReportsManage = "reports:manage";

    // User Management
    public const string UsersRead = "users:read";
    public const string UsersWrite = "users:write";
    public const string UsersDelete = "users:delete";
    public const string UsersManage = "users:manage";

    // Admin
    public const string AdminAccess = "admin:access";
    public const string SystemManage = "system:manage";
    public const string ConfigurationManage = "configuration:manage";

    // Wildcards
    public const string AllRead = "*:read";
    public const string AllWrite = "*:write";
    public const string AllManage = "*:manage";
    public const string AllPermissions = "*:*";
}

// Role constants
public static class Roles
{
    public const string SuperAdmin = "SuperAdmin";
    public const string Admin = "Admin";
    public const string Manager = "Manager";
    public const string Shipper = "Shipper";
    public const string Carrier = "Carrier";
    public const string Driver = "Driver";
    public const string Broker = "Broker";
    public const string Dispatcher = "Dispatcher";
    public const string CustomerService = "CustomerService";
    public const string Finance = "Finance";
    public const string Auditor = "Auditor";
    public const string ReadOnly = "ReadOnly";
}
