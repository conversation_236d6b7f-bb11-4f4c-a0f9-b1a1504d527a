using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.DTOs;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Application.Queries.GetTaggedRfqs;

public class GetTaggedRfqsQueryHandler : IRequestHandler<GetTaggedRfqsQuery, PagedResult<RfqSummaryDto>>
{
    private readonly IRfqTagRepository _tagRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetTaggedRfqsQueryHandler> _logger;

    public GetTaggedRfqsQueryHandler(
        IRfqTagRepository tagRepository,
        IMapper mapper,
        ILogger<GetTaggedRfqsQueryHandler> logger)
    {
        _tagRepository = tagRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<PagedResult<RfqSummaryDto>> Handle(GetTaggedRfqsQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting tagged RFQs for user {UserId}, TagTypes: {TagTypes}, Page: {Page}, PageSize: {PageSize}",
            request.RequestingUserId, string.Join(",", request.TagTypes), request.Page, request.PageSize);

        // Validate pagination parameters
        if (request.Page < 1) request.Page = 1;
        if (request.PageSize < 1 || request.PageSize > 100) request.PageSize = 20;

        // Validate tag types
        if (!request.TagTypes.Any())
        {
            _logger.LogWarning("No tag types provided for user {UserId}", request.RequestingUserId);
            return new PagedResult<RfqSummaryDto>
            {
                Items = new List<RfqSummaryDto>(),
                TotalCount = 0,
                Page = request.Page,
                PageSize = request.PageSize
            };
        }

        // Parse tag types
        var tagTypes = new List<RfqTagType>();
        foreach (var tagTypeStr in request.TagTypes)
        {
            if (Enum.TryParse<RfqTagType>(tagTypeStr, true, out var tagType))
            {
                tagTypes.Add(tagType);
            }
        }

        if (!tagTypes.Any())
        {
            _logger.LogWarning("No valid tag types found for user {UserId}", request.RequestingUserId);
            return new PagedResult<RfqSummaryDto>
            {
                Items = new List<RfqSummaryDto>(),
                TotalCount = 0,
                Page = request.Page,
                PageSize = request.PageSize
            };
        }

        try
        {
            // Get RFQs by tags
            var (rfqs, totalCount) = await _tagRepository.GetRfqsByTagsAsync(
                tagTypes,
                request.Page,
                request.PageSize,
                request.ActiveTagsOnly,
                cancellationToken);

            // Map to DTOs
            var rfqDtos = _mapper.Map<List<RfqSummaryDto>>(rfqs);

            var result = new PagedResult<RfqSummaryDto>
            {
                Items = rfqDtos,
                TotalCount = totalCount,
                Page = request.Page,
                PageSize = request.PageSize
            };

            _logger.LogInformation("Successfully retrieved {Count} tagged RFQs out of {TotalCount} for user {UserId}",
                rfqDtos.Count, totalCount, request.RequestingUserId);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving tagged RFQs for user {UserId}", request.RequestingUserId);
            throw;
        }
    }
}
