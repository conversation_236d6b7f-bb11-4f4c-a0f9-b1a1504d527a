using Shared.Domain.Common;
using MobileWorkflow.Domain.Events;

namespace MobileWorkflow.Domain.Entities;

public class GeofenceEvent : AggregateRoot
{
    public Guid GeofenceId { get; private set; }
    public Guid UserId { get; private set; }
    public string EventType { get; private set; } // Enter, Exit, Dwell
    public DateTime EventTime { get; private set; }
    public double Latitude { get; private set; }
    public double Longitude { get; private set; }
    public double? Accuracy { get; private set; }
    public double? Speed { get; private set; }
    public double? Bearing { get; private set; }
    public string? DeviceId { get; private set; }
    public Dictionary<string, object> LocationContext { get; private set; }
    public Dictionary<string, object> TriggerData { get; private set; }
    public bool IsProcessed { get; private set; }
    public DateTime? ProcessedAt { get; private set; }
    public Dictionary<string, object> ProcessingResult { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    public virtual Geofence Geofence { get; private set; } = null!;

    private GeofenceEvent() { } // EF Core

    public GeofenceEvent(
        Guid geofenceId,
        Guid userId,
        string eventType,
        double latitude,
        double longitude,
        Dictionary<string, object> locationContext)
    {
        GeofenceId = geofenceId;
        UserId = userId;
        EventType = eventType ?? throw new ArgumentNullException(nameof(eventType));
        Latitude = latitude;
        Longitude = longitude;
        LocationContext = locationContext ?? throw new ArgumentNullException(nameof(locationContext));

        EventTime = DateTime.UtcNow;
        IsProcessed = false;
        TriggerData = new Dictionary<string, object>();
        ProcessingResult = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();

        AddDomainEvent(new GeofenceEventCreatedEvent(Id, GeofenceId, UserId, EventType, Latitude, Longitude));
    }

    public void SetLocationDetails(double? accuracy = null, double? speed = null, double? bearing = null, string? deviceId = null)
    {
        Accuracy = accuracy;
        Speed = speed;
        Bearing = bearing;
        DeviceId = deviceId;
    }

    public void SetTriggerData(Dictionary<string, object> triggerData)
    {
        TriggerData = triggerData ?? throw new ArgumentNullException(nameof(triggerData));
    }

    public void MarkAsProcessed(Dictionary<string, object> processingResult)
    {
        IsProcessed = true;
        ProcessedAt = DateTime.UtcNow;
        ProcessingResult = processingResult ?? throw new ArgumentNullException(nameof(processingResult));

        AddDomainEvent(new GeofenceEventProcessedEvent(Id, GeofenceId, UserId, EventType, processingResult));
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetLocationContext<T>(string key, T? defaultValue = default)
    {
        if (LocationContext.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetTriggerData<T>(string key, T? defaultValue = default)
    {
        if (TriggerData.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetProcessingResult<T>(string key, T? defaultValue = default)
    {
        if (ProcessingResult.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }
}

public class UserGeofenceStatus : AggregateRoot
{
    public Guid GeofenceId { get; private set; }
    public Guid UserId { get; private set; }
    public string Status { get; private set; } // Inside, Outside, Dwelling
    public DateTime StatusChangedAt { get; private set; }
    public DateTime? EnteredAt { get; private set; }
    public DateTime? ExitedAt { get; private set; }
    public DateTime? DwellStartedAt { get; private set; }
    public TimeSpan? TotalDwellTime { get; private set; }
    public int EntryCount { get; private set; }
    public double? LastLatitude { get; private set; }
    public double? LastLongitude { get; private set; }
    public DateTime? LastLocationUpdate { get; private set; }
    public Dictionary<string, object> StatusData { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    public virtual Geofence Geofence { get; private set; } = null!;

    private UserGeofenceStatus() { } // EF Core

    public UserGeofenceStatus(
        Guid geofenceId,
        Guid userId,
        string status)
    {
        GeofenceId = geofenceId;
        UserId = userId;
        Status = status ?? throw new ArgumentNullException(nameof(status));

        StatusChangedAt = DateTime.UtcNow;
        EntryCount = 0;
        StatusData = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();

        if (status.Equals("Inside", StringComparison.OrdinalIgnoreCase))
        {
            EnteredAt = DateTime.UtcNow;
            EntryCount = 1;
        }

        AddDomainEvent(new UserGeofenceStatusCreatedEvent(Id, GeofenceId, UserId, Status));
    }

    public void UpdateStatus(string newStatus, double? latitude = null, double? longitude = null)
    {
        var previousStatus = Status;
        Status = newStatus ?? throw new ArgumentNullException(nameof(newStatus));
        StatusChangedAt = DateTime.UtcNow;

        if (latitude.HasValue && longitude.HasValue)
        {
            LastLatitude = latitude;
            LastLongitude = longitude;
            LastLocationUpdate = DateTime.UtcNow;
        }

        // Handle status transitions
        switch (newStatus.ToLowerInvariant())
        {
            case "inside":
                if (previousStatus.Equals("Outside", StringComparison.OrdinalIgnoreCase))
                {
                    EnteredAt = DateTime.UtcNow;
                    EntryCount++;
                    ExitedAt = null;
                    DwellStartedAt = null;
                }
                break;

            case "outside":
                if (previousStatus.Equals("Inside", StringComparison.OrdinalIgnoreCase) ||
                    previousStatus.Equals("Dwelling", StringComparison.OrdinalIgnoreCase))
                {
                    ExitedAt = DateTime.UtcNow;

                    // Calculate dwell time if was dwelling
                    if (DwellStartedAt.HasValue)
                    {
                        var dwellDuration = DateTime.UtcNow - DwellStartedAt.Value;
                        TotalDwellTime = (TotalDwellTime ?? TimeSpan.Zero) + dwellDuration;
                        DwellStartedAt = null;
                    }
                }
                break;

            case "dwelling":
                if (previousStatus.Equals("Inside", StringComparison.OrdinalIgnoreCase))
                {
                    DwellStartedAt = DateTime.UtcNow;
                }
                break;
        }

        AddDomainEvent(new UserGeofenceStatusChangedEvent(Id, GeofenceId, UserId, previousStatus, newStatus));
    }

    public void UpdateLocation(double latitude, double longitude)
    {
        LastLatitude = latitude;
        LastLongitude = longitude;
        LastLocationUpdate = DateTime.UtcNow;
    }

    public TimeSpan? GetCurrentDwellTime()
    {
        if (DwellStartedAt.HasValue && Status.Equals("Dwelling", StringComparison.OrdinalIgnoreCase))
        {
            return DateTime.UtcNow - DwellStartedAt.Value;
        }
        return null;
    }

    public TimeSpan? GetCurrentSessionTime()
    {
        if (EnteredAt.HasValue && Status.Equals("Inside", StringComparison.OrdinalIgnoreCase))
        {
            return DateTime.UtcNow - EnteredAt.Value;
        }
        return null;
    }

    public bool IsInside()
    {
        return Status.Equals("Inside", StringComparison.OrdinalIgnoreCase) ||
               Status.Equals("Dwelling", StringComparison.OrdinalIgnoreCase);
    }

    public bool IsOutside()
    {
        return Status.Equals("Outside", StringComparison.OrdinalIgnoreCase);
    }

    public bool IsDwelling()
    {
        return Status.Equals("Dwelling", StringComparison.OrdinalIgnoreCase);
    }

    public void AddStatusData(string key, object value)
    {
        StatusData[key] = value;
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetStatusData<T>(string key, T? defaultValue = default)
    {
        if (StatusData.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }
}

public class LocationUpdate : AggregateRoot
{
    public Guid UserId { get; private set; }
    public double Latitude { get; private set; }
    public double Longitude { get; private set; }
    public double? Accuracy { get; private set; }
    public double? Altitude { get; private set; }
    public double? Speed { get; private set; }
    public double? Bearing { get; private set; }
    public DateTime Timestamp { get; private set; }
    public string? DeviceId { get; private set; }
    public string? Provider { get; private set; } // GPS, Network, Passive
    public Dictionary<string, object> LocationContext { get; private set; }
    public bool IsProcessed { get; private set; }
    public DateTime? ProcessedAt { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    private LocationUpdate() { } // EF Core

    public LocationUpdate(
        Guid userId,
        double latitude,
        double longitude,
        DateTime timestamp,
        Dictionary<string, object> locationContext)
    {
        UserId = userId;
        Latitude = latitude;
        Longitude = longitude;
        Timestamp = timestamp;
        LocationContext = locationContext ?? throw new ArgumentNullException(nameof(locationContext));

        IsProcessed = false;
        Metadata = new Dictionary<string, object>();

        AddDomainEvent(new LocationUpdateReceivedEvent(Id, UserId, Latitude, Longitude, Timestamp));
    }

    public void SetLocationDetails(double? accuracy = null, double? altitude = null, double? speed = null, double? bearing = null, string? deviceId = null, string? provider = null)
    {
        Accuracy = accuracy;
        Altitude = altitude;
        Speed = speed;
        Bearing = bearing;
        DeviceId = deviceId;
        Provider = provider;
    }

    public void MarkAsProcessed()
    {
        IsProcessed = true;
        ProcessedAt = DateTime.UtcNow;

        AddDomainEvent(new LocationUpdateProcessedEvent(Id, UserId, Latitude, Longitude));
    }

    public double CalculateDistanceTo(double targetLatitude, double targetLongitude)
    {
        const double R = 6371000; // Earth's radius in meters
        var dLat = ToRadians(targetLatitude - Latitude);
        var dLng = ToRadians(targetLongitude - Longitude);
        var a = Math.Sin(dLat / 2) * Math.Sin(dLat / 2) +
                Math.Cos(ToRadians(Latitude)) * Math.Cos(ToRadians(targetLatitude)) *
                Math.Sin(dLng / 2) * Math.Sin(dLng / 2);
        var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));
        return R * c;
    }

    public double CalculateBearingTo(double targetLatitude, double targetLongitude)
    {
        var dLng = ToRadians(targetLongitude - Longitude);
        var lat1 = ToRadians(Latitude);
        var lat2 = ToRadians(targetLatitude);

        var y = Math.Sin(dLng) * Math.Cos(lat2);
        var x = Math.Cos(lat1) * Math.Sin(lat2) - Math.Sin(lat1) * Math.Cos(lat2) * Math.Cos(dLng);

        var bearing = Math.Atan2(y, x);
        return (ToDegrees(bearing) + 360) % 360;
    }

    private double ToRadians(double degrees)
    {
        return degrees * Math.PI / 180;
    }

    private double ToDegrees(double radians)
    {
        return radians * 180 / Math.PI;
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetLocationContext<T>(string key, T? defaultValue = default)
    {
        if (LocationContext.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }
}


