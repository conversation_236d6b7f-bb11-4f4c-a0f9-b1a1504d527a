using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using NetworkFleetManagement.Domain.Repositories;
using NetworkFleetManagement.Infrastructure.Services;

namespace NetworkFleetManagement.Infrastructure.Monitoring.HealthChecks;

public class FleetOperationsHealthCheck : IHealthCheck
{
    private readonly ICarrierRepository _carrierRepository;
    private readonly IVehicleRepository _vehicleRepository;
    private readonly IDriverRepository _driverRepository;
    private readonly NetworkFleetCacheService _cacheService;
    private readonly ILogger<FleetOperationsHealthCheck> _logger;

    public FleetOperationsHealthCheck(
        ICarrierRepository carrierRepository,
        IVehicleRepository vehicleRepository,
        IDriverRepository driverRepository,
        NetworkFleetCacheService cacheService,
        ILogger<FleetOperationsHealthCheck> logger)
    {
        _carrierRepository = carrierRepository;
        _vehicleRepository = vehicleRepository;
        _driverRepository = driverRepository;
        _cacheService = cacheService;
        _logger = logger;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            var healthData = new Dictionary<string, object>();
            var warnings = new List<string>();
            var errors = new List<string>();

            // Check carrier operations
            try
            {
                var activeCarriers = await _carrierRepository.GetActiveCarriersAsync();
                healthData["active_carriers"] = activeCarriers.Count;
                
                if (activeCarriers.Count == 0)
                {
                    warnings.Add("No active carriers found");
                }
            }
            catch (Exception ex)
            {
                errors.Add($"Carrier repository error: {ex.Message}");
                _logger.LogError(ex, "Error checking carrier operations");
            }

            // Check vehicle operations
            try
            {
                var activeVehicles = await _vehicleRepository.GetActiveVehiclesAsync();
                healthData["active_vehicles"] = activeVehicles.Count;
                
                if (activeVehicles.Count == 0)
                {
                    warnings.Add("No active vehicles found");
                }
            }
            catch (Exception ex)
            {
                errors.Add($"Vehicle repository error: {ex.Message}");
                _logger.LogError(ex, "Error checking vehicle operations");
            }

            // Check driver operations
            try
            {
                var activeDrivers = await _driverRepository.GetActiveDriversAsync();
                healthData["active_drivers"] = activeDrivers.Count;
                
                if (activeDrivers.Count == 0)
                {
                    warnings.Add("No active drivers found");
                }
            }
            catch (Exception ex)
            {
                errors.Add($"Driver repository error: {ex.Message}");
                _logger.LogError(ex, "Error checking driver operations");
            }

            // Check cache operations
            try
            {
                var cacheStats = await _cacheService.GetCacheStatisticsAsync();
                healthData["cache_statistics"] = cacheStats;
            }
            catch (Exception ex)
            {
                errors.Add($"Cache service error: {ex.Message}");
                _logger.LogError(ex, "Error checking cache operations");
            }

            healthData["checked_at"] = DateTime.UtcNow;
            healthData["warnings"] = warnings;
            healthData["errors"] = errors;

            // Determine health status
            if (errors.Any())
            {
                return HealthCheckResult.Unhealthy("Fleet operations have errors", null, healthData);
            }
            
            if (warnings.Any())
            {
                return HealthCheckResult.Degraded("Fleet operations have warnings", null, healthData);
            }

            _logger.LogDebug("Fleet operations health check passed");
            return HealthCheckResult.Healthy("Fleet operations are healthy", healthData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Fleet operations health check failed");
            return HealthCheckResult.Unhealthy("Fleet operations health check failed", ex);
        }
    }
}
