using Xunit;
using FluentAssertions;
using Moq;
using MediatR;
using Microsoft.Extensions.Logging;
using AnalyticsBIService.Application.Services;
using AnalyticsBIService.Application.Commands.TrackEvent;
using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Enums;
using AnalyticsBIService.Tests.Infrastructure;

namespace AnalyticsBIService.Tests.Unit.Application;

/// <summary>
/// Unit tests for AnalyticsApplicationService
/// </summary>
public class AnalyticsApplicationServiceTests
{
    private readonly Mock<IMediator> _mockMediator;
    private readonly Mock<ILogger<AnalyticsApplicationService>> _mockLogger;
    private readonly AnalyticsApplicationService _service;

    public AnalyticsApplicationServiceTests()
    {
        _mockMediator = new Mock<IMediator>();
        _mockLogger = new Mock<ILogger<AnalyticsApplicationService>>();
        _service = new AnalyticsApplicationService(_mockMediator.Object, _mockLogger.Object);
    }

    [Fact]
    public void Constructor_Should_Create_Valid_Service()
    {
        // Act & Assert
        _service.Should().NotBeNull();
    }

    [Fact]
    public void Constructor_Should_Throw_When_Mediator_Is_Null()
    {
        // Act & Assert
        var action = () => new AnalyticsApplicationService(null!, _mockLogger.Object);
        action.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void Constructor_Should_Throw_When_Logger_Is_Null()
    {
        // Act & Assert
        var action = () => new AnalyticsApplicationService(_mockMediator.Object, null!);
        action.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public async Task TrackEventAsync_Should_Send_TrackEventCommand()
    {
        // Arrange
        var analyticsEvent = CreateTestAnalyticsEvent();
        var cancellationToken = CancellationToken.None;

        _mockMediator
            .Setup(m => m.Send(It.IsAny<TrackEventCommand>(), cancellationToken))
            .Returns(Task.FromResult(Guid.NewGuid()));

        // Act
        await _service.TrackEventAsync(analyticsEvent, cancellationToken);

        // Assert
        _mockMediator.Verify(
            m => m.Send(It.Is<TrackEventCommand>(cmd =>
                cmd.EventName == analyticsEvent.EventName &&
                cmd.EventType == analyticsEvent.EventType &&
                cmd.DataSource == analyticsEvent.DataSource &&
                cmd.UserId == analyticsEvent.UserId &&
                cmd.UserType == analyticsEvent.UserType &&
                cmd.EntityId == analyticsEvent.EntityId &&
                cmd.EntityType == analyticsEvent.EntityType &&
                cmd.Properties == analyticsEvent.Properties &&
                cmd.Metadata == analyticsEvent.Metadata &&
                cmd.SessionId == analyticsEvent.SessionId &&
                cmd.IpAddress == analyticsEvent.IpAddress &&
                cmd.UserAgent == analyticsEvent.UserAgent
            ), cancellationToken),
            Times.Once);
    }

    [Fact]
    public async Task TrackEventAsync_Should_Handle_Null_AnalyticsEvent()
    {
        // Act & Assert
        var action = async () => await _service.TrackEventAsync(null!, CancellationToken.None);
        await action.Should().ThrowAsync<ArgumentNullException>();
    }

    [Fact]
    public async Task TrackUserActivityAsync_Should_Send_TrackUserActivityCommand()
    {
        // Arrange
        var eventName = "user_login";
        var userId = Guid.NewGuid();
        var userType = UserType.Admin;
        var properties = new Dictionary<string, object> { { "source", "web" } };
        var cancellationToken = CancellationToken.None;

        _mockMediator
            .Setup(m => m.Send(It.IsAny<TrackUserActivityCommand>(), cancellationToken))
            .Returns(Task.FromResult(Guid.NewGuid()));

        // Act
        await _service.TrackUserActivityAsync(eventName, userId, userType, properties, cancellationToken);

        // Assert
        _mockMediator.Verify(
            m => m.Send(It.Is<TrackUserActivityCommand>(cmd =>
                cmd.EventName == eventName &&
                cmd.UserId == userId &&
                cmd.UserType == userType &&
                cmd.Properties == properties
            ), cancellationToken),
            Times.Once);
    }

    [Fact]
    public async Task TrackUserActivityAsync_Should_Handle_Null_Properties()
    {
        // Arrange
        var eventName = "user_logout";
        var userId = Guid.NewGuid();
        var userType = UserType.TransportCompany;
        var cancellationToken = CancellationToken.None;

        _mockMediator
            .Setup(m => m.Send(It.IsAny<TrackUserActivityCommand>(), cancellationToken))
            .Returns(Task.FromResult(Guid.NewGuid()));

        // Act
        await _service.TrackUserActivityAsync(eventName, userId, userType, null, cancellationToken);

        // Assert
        _mockMediator.Verify(
            m => m.Send(It.Is<TrackUserActivityCommand>(cmd =>
                cmd.EventName == eventName &&
                cmd.UserId == userId &&
                cmd.UserType == userType &&
                cmd.Properties == null
            ), cancellationToken),
            Times.Once);
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    public async Task TrackUserActivityAsync_Should_Throw_When_EventName_Is_Invalid(string invalidEventName)
    {
        // Arrange
        var userId = Guid.NewGuid();
        var userType = UserType.Broker;

        // Act & Assert
        var action = async () => await _service.TrackUserActivityAsync(invalidEventName, userId, userType);
        await action.Should().ThrowAsync<ArgumentException>();
    }

    [Fact]
    public async Task TrackUserActivityAsync_Should_Throw_When_EventName_Is_Null()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var userType = UserType.Driver;

        // Act & Assert
        var action = async () => await _service.TrackUserActivityAsync(null!, userId, userType);
        await action.Should().ThrowAsync<ArgumentNullException>();
    }

    [Fact]
    public async Task TrackUserActivityAsync_Should_Throw_When_UserId_Is_Empty()
    {
        // Arrange
        var eventName = "test_event";
        var userType = UserType.Admin;

        // Act & Assert
        var action = async () => await _service.TrackUserActivityAsync(eventName, Guid.Empty, userType);
        await action.Should().ThrowAsync<ArgumentException>();
    }

    [Theory]
    [InlineData(UserType.Admin)]
    [InlineData(UserType.TransportCompany)]
    [InlineData(UserType.Broker)]
    [InlineData(UserType.Carrier)]
    public async Task TrackUserActivityAsync_Should_Accept_All_Valid_UserTypes(UserType userType)
    {
        // Arrange
        var eventName = "test_event";
        var userId = Guid.NewGuid();
        var cancellationToken = CancellationToken.None;

        _mockMediator
            .Setup(m => m.Send(It.IsAny<TrackUserActivityCommand>(), cancellationToken))
            .Returns(Task.FromResult(Guid.NewGuid()));

        // Act
        await _service.TrackUserActivityAsync(eventName, userId, userType, null, cancellationToken);

        // Assert
        _mockMediator.Verify(
            m => m.Send(It.Is<TrackUserActivityCommand>(cmd =>
                cmd.UserType == userType
            ), cancellationToken),
            Times.Once);
    }

    [Fact]
    public async Task TrackEventAsync_Should_Handle_Mediator_Exception()
    {
        // Arrange
        var analyticsEvent = CreateTestAnalyticsEvent();
        var expectedException = new InvalidOperationException("Mediator error");

        _mockMediator
            .Setup(m => m.Send(It.IsAny<TrackEventCommand>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(expectedException);

        // Act & Assert
        var action = async () => await _service.TrackEventAsync(analyticsEvent);
        await action.Should().ThrowAsync<InvalidOperationException>()
            .WithMessage("Mediator error");
    }

    [Fact]
    public async Task TrackUserActivityAsync_Should_Handle_Mediator_Exception()
    {
        // Arrange
        var eventName = "test_event";
        var userId = Guid.NewGuid();
        var userType = UserType.Admin;
        var expectedException = new InvalidOperationException("Mediator error");

        _mockMediator
            .Setup(m => m.Send(It.IsAny<TrackUserActivityCommand>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(expectedException);

        // Act & Assert
        var action = async () => await _service.TrackUserActivityAsync(eventName, userId, userType);
        await action.Should().ThrowAsync<InvalidOperationException>()
            .WithMessage("Mediator error");
    }

    [Fact]
    public async Task TrackEventAsync_Should_Handle_Cancellation()
    {
        // Arrange
        var analyticsEvent = CreateTestAnalyticsEvent();
        var cancellationTokenSource = new CancellationTokenSource();
        cancellationTokenSource.Cancel();

        _mockMediator
            .Setup(m => m.Send(It.IsAny<TrackEventCommand>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new OperationCanceledException());

        // Act & Assert
        var action = async () => await _service.TrackEventAsync(analyticsEvent, cancellationTokenSource.Token);
        await action.Should().ThrowAsync<OperationCanceledException>();
    }

    [Fact]
    public async Task TrackUserActivityAsync_Should_Handle_Cancellation()
    {
        // Arrange
        var eventName = "test_event";
        var userId = Guid.NewGuid();
        var userType = UserType.Admin;
        var cancellationTokenSource = new CancellationTokenSource();
        cancellationTokenSource.Cancel();

        _mockMediator
            .Setup(m => m.Send(It.IsAny<TrackUserActivityCommand>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new OperationCanceledException());

        // Act & Assert
        var action = async () => await _service.TrackUserActivityAsync(eventName, userId, userType, null, cancellationTokenSource.Token);
        await action.Should().ThrowAsync<OperationCanceledException>();
    }

    [Fact]
    public async Task Service_Should_Support_Multiple_Concurrent_Operations()
    {
        // Arrange
        var analyticsEvent1 = CreateTestAnalyticsEvent();
        var analyticsEvent2 = CreateTestAnalyticsEvent();
        var analyticsEvent3 = CreateTestAnalyticsEvent();

        _mockMediator
            .Setup(m => m.Send(It.IsAny<TrackEventCommand>(), It.IsAny<CancellationToken>()))
            .Returns(Task.FromResult(Guid.NewGuid()));

        // Act
        var tasks = new[]
        {
            _service.TrackEventAsync(analyticsEvent1),
            _service.TrackEventAsync(analyticsEvent2),
            _service.TrackEventAsync(analyticsEvent3)
        };

        await Task.WhenAll(tasks);

        // Assert
        _mockMediator.Verify(
            m => m.Send(It.IsAny<TrackEventCommand>(), It.IsAny<CancellationToken>()),
            Times.Exactly(3));
    }

    /// <summary>
    /// Helper method to create a test AnalyticsEvent
    /// </summary>
    private static AnalyticsEvent CreateTestAnalyticsEvent()
    {
        return new AnalyticsEvent(
            eventName: "test_event",
            eventType: AnalyticsEventType.UserActivity,
            dataSource: DataSourceType.Application,
            userId: Guid.NewGuid(),
            userType: UserType.Admin,
            entityId: Guid.NewGuid(),
            entityType: "Order",
            properties: new Dictionary<string, object> { { "action", "create" } },
            metadata: new Dictionary<string, object> { { "version", "1.0" } },
            sessionId: Guid.NewGuid().ToString(),
            ipAddress: "***********",
            userAgent: "Mozilla/5.0 Test Browser");
    }
}
