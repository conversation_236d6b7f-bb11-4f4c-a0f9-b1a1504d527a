using AnalyticsBIService.Application.Events;
using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Enums;
using AnalyticsBIService.Domain.ValueObjects;
using AnalyticsBIService.Domain.Repositories;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Messaging;

namespace AnalyticsBIService.Application.Commands.TrackEvent;

/// <summary>
/// Handler for tracking analytics events
/// </summary>
public class TrackEventCommandHandler :
    IRequestHandler<TrackEventCommand, Guid>,
    IRequestHandler<TrackUserActivityCommand, Guid>,
    IRequestHandler<TrackBusinessTransactionCommand, Guid>,
    IRequestHandler<TrackSystemPerformanceCommand, Guid>,
    IRequestHandler<TrackEventsBatchCommand, List<Guid>>
{
    private readonly IAnalyticsEventRepository _analyticsEventRepository;
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<TrackEventCommandHandler> _logger;

    public TrackEventCommandHandler(
        IAnalyticsEventRepository analyticsEventRepository,
        IMessageBroker messageBroker,
        ILogger<TrackEventCommandHandler> logger)
    {
        _analyticsEventRepository = analyticsEventRepository;
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task<Guid> Handle(TrackEventCommand request, CancellationToken cancellationToken)
    {
        _logger.LogDebug("Tracking analytics event: {EventName}", request.EventName);

        var analyticsEvent = new AnalyticsEvent(
            request.EventName,
            request.EventType,
            request.DataSource,
            request.UserId,
            request.UserType,
            request.EntityId,
            request.EntityType,
            request.Properties,
            request.Metadata,
            request.SessionId,
            request.IpAddress,
            request.UserAgent);

        await _analyticsEventRepository.AddAsync(analyticsEvent, cancellationToken);

        // Publish integration event for real-time processing
        await _messageBroker.PublishAsync("analytics.event.tracked", new AnalyticsEventTrackedEvent
        {
            EventId = analyticsEvent.Id,
            EventName = analyticsEvent.EventName,
            EventType = analyticsEvent.EventType.ToString(),
            DataSource = analyticsEvent.DataSource.ToString(),
            UserId = analyticsEvent.UserId,
            UserType = analyticsEvent.UserType?.ToString(),
            EntityId = analyticsEvent.EntityId,
            EntityType = analyticsEvent.EntityType,
            Timestamp = analyticsEvent.Timestamp,
            Properties = analyticsEvent.Properties,
            SessionId = analyticsEvent.SessionId
        });

        _logger.LogInformation("Successfully tracked analytics event {EventId}: {EventName}",
            analyticsEvent.Id, request.EventName);

        return analyticsEvent.Id;
    }

    public async Task<Guid> Handle(TrackUserActivityCommand request, CancellationToken cancellationToken)
    {
        var trackEventCommand = new TrackEventCommand(
            request.EventName,
            AnalyticsEventType.UserActivity,
            DataSourceType.External,
            request.UserId,
            request.UserType,
            Properties: request.Properties,
            SessionId: request.SessionId,
            IpAddress: request.IpAddress,
            UserAgent: request.UserAgent);

        return await Handle(trackEventCommand, cancellationToken);
    }

    public async Task<Guid> Handle(TrackBusinessTransactionCommand request, CancellationToken cancellationToken)
    {
        var trackEventCommand = new TrackEventCommand(
            request.EventName,
            AnalyticsEventType.BusinessTransaction,
            request.DataSource,
            request.UserId,
            request.UserType,
            request.EntityId,
            request.EntityType,
            request.Properties);

        return await Handle(trackEventCommand, cancellationToken);
    }

    public async Task<Guid> Handle(TrackSystemPerformanceCommand request, CancellationToken cancellationToken)
    {
        var trackEventCommand = new TrackEventCommand(
            request.EventName,
            AnalyticsEventType.SystemPerformance,
            request.DataSource,
            Properties: request.Properties,
            Metadata: request.Metadata);

        return await Handle(trackEventCommand, cancellationToken);
    }

    public async Task<List<Guid>> Handle(TrackEventsBatchCommand request, CancellationToken cancellationToken)
    {
        _logger.LogDebug("Processing batch of {Count} analytics events", request.Events.Count);

        var eventIds = new List<Guid>();
        var analyticsEvents = new List<AnalyticsEvent>();

        foreach (var eventCommand in request.Events)
        {
            var analyticsEvent = new AnalyticsEvent(
                eventCommand.EventName,
                eventCommand.EventType,
                eventCommand.DataSource,
                eventCommand.UserId,
                eventCommand.UserType,
                eventCommand.EntityId,
                eventCommand.EntityType,
                eventCommand.Properties,
                eventCommand.Metadata,
                eventCommand.SessionId,
                eventCommand.IpAddress,
                eventCommand.UserAgent);

            analyticsEvents.Add(analyticsEvent);
            eventIds.Add(analyticsEvent.Id);
        }

        await _analyticsEventRepository.BulkInsertAsync(analyticsEvents, cancellationToken);

        // Publish batch integration event
        await _messageBroker.PublishAsync("analytics.events.batch.tracked", new
        {
            EventIds = eventIds,
            EventCount = analyticsEvents.Count,
            TrackedAt = DateTime.UtcNow
        });

        _logger.LogInformation("Successfully tracked batch of {Count} analytics events", analyticsEvents.Count);

        return eventIds;
    }
}

/// <summary>
/// Handler for metric calculation commands
/// </summary>
public class CalculateMetricCommandHandler :
    IRequestHandler<CalculateMetricCommand, Guid>,
    IRequestHandler<UpdateMetricCommand, bool>,
    IRequestHandler<SetMetricTargetCommand, bool>
{
    private readonly IMetricRepository _metricRepository;
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<CalculateMetricCommandHandler> _logger;

    public CalculateMetricCommandHandler(
        IMetricRepository metricRepository,
        IMessageBroker messageBroker,
        ILogger<CalculateMetricCommandHandler> logger)
    {
        _metricRepository = metricRepository;
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task<Guid> Handle(CalculateMetricCommand request, CancellationToken cancellationToken)
    {
        _logger.LogDebug("Calculating metric: {MetricName}", request.MetricName);

        var timestamp = request.Timestamp ?? DateTime.UtcNow;
        var periodStart = request.PeriodStart ?? GetPeriodStart(timestamp, request.Period);
        var periodEnd = request.PeriodEnd ?? GetPeriodEnd(timestamp, request.Period);

        var metricValue = MetricValue.Create(
            request.Value,
            request.Type,
            request.Unit,
            timestamp,
            request.Metadata);

        var timePeriod = TimePeriodValue.Create(periodStart, periodEnd, request.Period);

        KPITarget? target = null;
        if (request.TargetValue.HasValue)
        {
            target = KPITarget.Create(
                request.TargetValue.Value,
                request.TargetUnit ?? request.Unit,
                request.IsHigherBetter ?? true);

            if (request.WarningThreshold.HasValue || request.CriticalThreshold.HasValue)
            {
                target = KPITarget.CreateWithThresholds(
                    request.TargetValue.Value,
                    request.TargetUnit ?? request.Unit,
                    request.WarningThreshold ?? 0,
                    request.CriticalThreshold ?? 0,
                    request.IsHigherBetter ?? true);
            }
        }

        var metric = new Metric(
            request.MetricName,
            request.Description,
            request.Type,
            request.Category,
            metricValue,
            timePeriod,
            request.DataSource,
            request.UserId,
            request.UserType,
            target,
            request.Tags);

        await _metricRepository.AddAsync(metric, cancellationToken);

        // Publish integration event
        await _messageBroker.PublishAsync("analytics.metric.calculated", new
        {
            MetricId = metric.Id,
            MetricName = metric.Name,
            Category = metric.Category.ToString(),
            Type = metric.Type.ToString(),
            Value = metric.Value.Value,
            Unit = metric.Value.Unit,
            UserId = metric.UserId,
            UserType = metric.UserType?.ToString(),
            PeriodStart = metric.Period.StartDate,
            PeriodEnd = metric.Period.EndDate,
            PerformanceStatus = metric.GetPerformanceStatus().ToString(),
            CalculatedAt = DateTime.UtcNow
        });

        _logger.LogInformation("Successfully calculated metric {MetricId}: {MetricName} = {Value} {Unit}",
            metric.Id, request.MetricName, request.Value, request.Unit);

        return metric.Id;
    }

    public async Task<bool> Handle(UpdateMetricCommand request, CancellationToken cancellationToken)
    {
        var metric = await _metricRepository.GetByIdAsync(request.MetricId, cancellationToken);
        if (metric == null)
        {
            _logger.LogWarning("Metric {MetricId} not found for update", request.MetricId);
            return false;
        }

        var newMetricValue = MetricValue.Create(
            request.NewValue,
            metric.Value.Type,
            metric.Value.Unit,
            request.Timestamp ?? DateTime.UtcNow,
            request.Metadata ?? metric.Value.Metadata);

        metric.UpdateValue(newMetricValue);
        await _metricRepository.UpdateAsync(metric, cancellationToken);

        _logger.LogInformation("Successfully updated metric {MetricId} with new value {Value}",
            request.MetricId, request.NewValue);

        return true;
    }

    public async Task<bool> Handle(SetMetricTargetCommand request, CancellationToken cancellationToken)
    {
        var metric = await _metricRepository.GetByIdAsync(request.MetricId, cancellationToken);
        if (metric == null)
        {
            _logger.LogWarning("Metric {MetricId} not found for target setting", request.MetricId);
            return false;
        }

        var target = KPITarget.CreateWithThresholds(
            request.TargetValue,
            request.Unit,
            request.WarningThreshold ?? 0,
            request.CriticalThreshold ?? 0,
            request.IsHigherBetter);

        metric.SetTarget(target);
        await _metricRepository.UpdateAsync(metric, cancellationToken);

        _logger.LogInformation("Successfully set target for metric {MetricId}: {TargetValue} {Unit}",
            request.MetricId, request.TargetValue, request.Unit);

        return true;
    }

    private static DateTime GetPeriodStart(DateTime timestamp, TimePeriod period)
    {
        return period switch
        {
            TimePeriod.Hourly => new DateTime(timestamp.Year, timestamp.Month, timestamp.Day, timestamp.Hour, 0, 0),
            TimePeriod.Daily => timestamp.Date,
            TimePeriod.Weekly => timestamp.Date.AddDays(-(int)timestamp.DayOfWeek),
            TimePeriod.Monthly => new DateTime(timestamp.Year, timestamp.Month, 1),
            TimePeriod.Quarterly => new DateTime(timestamp.Year, ((timestamp.Month - 1) / 3) * 3 + 1, 1),
            TimePeriod.Yearly => new DateTime(timestamp.Year, 1, 1),
            _ => timestamp.Date
        };
    }

    private static DateTime GetPeriodEnd(DateTime timestamp, TimePeriod period)
    {
        var start = GetPeriodStart(timestamp, period);
        return period switch
        {
            TimePeriod.Hourly => start.AddHours(1).AddTicks(-1),
            TimePeriod.Daily => start.AddDays(1).AddTicks(-1),
            TimePeriod.Weekly => start.AddDays(7).AddTicks(-1),
            TimePeriod.Monthly => start.AddMonths(1).AddTicks(-1),
            TimePeriod.Quarterly => start.AddMonths(3).AddTicks(-1),
            TimePeriod.Yearly => start.AddYears(1).AddTicks(-1),
            _ => start.AddDays(1).AddTicks(-1)
        };
    }
}
