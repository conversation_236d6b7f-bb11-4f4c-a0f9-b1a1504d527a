using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Application.Commands.GenerateCrossServiceAnalytics;

namespace AnalyticsBIService.Application.Interfaces;

// ===== CORE REPORTING SERVICES =====

// IDataAggregationService is already defined in IDataAggregationService.cs - using that instead

public interface IVisualizationService
{
    Task<List<ReportVisualizationDto>> GenerateVisualizationsAsync(
        string dataSource,
        object data,
        List<string> metrics,
        CancellationToken cancellationToken = default);

    Task<List<ReportVisualizationDto>> GenerateVisualizationsAsync(
        VisualizationRequest request,
        CancellationToken cancellationToken = default);

    Task<List<ReportVisualizationDto>> GenerateCorrelationVisualizationsAsync(
        Dictionary<string, object> aggregatedData,
        CancellationToken cancellationToken = default);

    Task<ReportVisualizationDto> CreateCustomVisualizationAsync(
        ReportVisualizationConfigDto config,
        Dictionary<string, object> data,
        CancellationToken cancellationToken = default);

    Task<byte[]> ExportVisualizationAsync(
        ReportVisualizationDto visualization,
        string format,
        CancellationToken cancellationToken = default);
}

public interface IInsightGenerationService
{
    Task<List<ReportInsightDto>> GenerateDataInsightsAsync(
        Dictionary<string, object> aggregatedData,
        CancellationToken cancellationToken = default);

    Task<List<ReportInsightDto>> GenerateTrendInsightsAsync(
        Dictionary<string, object> aggregatedData,
        DateRangeDto dateRange,
        CancellationToken cancellationToken = default);

    Task<List<ReportInsightDto>> GeneratePerformanceInsightsAsync(
        Dictionary<string, object> aggregatedData,
        List<string> metrics,
        CancellationToken cancellationToken = default);

    Task<List<ReportInsightDto>> GeneratePredictiveInsightsAsync(
        Dictionary<string, object> aggregatedData,
        CancellationToken cancellationToken = default);

    Task<List<ReportInsightDto>> GenerateBenchmarkingInsightsAsync(
        Dictionary<string, object> aggregatedData,
        CancellationToken cancellationToken = default);

    Task<List<ReportInsightDto>> GenerateAnomalyInsightsAsync(
        Dictionary<string, object> aggregatedData,
        decimal threshold = 2.0m,
        CancellationToken cancellationToken = default);
}

// ===== CROSS-SERVICE ANALYTICS SERVICES =====

public interface ICrossServiceDataCollector
{
    Task<Dictionary<string, object>> CollectDataAsync(
        string dataSource,
        DateRangeDto dateRange,
        List<string> metrics,
        Dictionary<string, object> parameters,
        CancellationToken cancellationToken = default);

    Task<ServiceAnalyticsDataDto> CollectServiceAnalyticsAsync(
        string serviceName,
        DateRangeDto dateRange,
        List<string> metrics,
        Dictionary<string, object> parameters,
        AnalyticsDepth depth,
        CancellationToken cancellationToken = default);

    Task<Dictionary<string, object>> CollectRealTimeDataAsync(
        List<string> services,
        List<string> metrics,
        CancellationToken cancellationToken = default);
}

public interface ICorrelationAnalysisService
{
    Task<List<ServiceCorrelationDto>> AnalyzeServiceCorrelationsAsync(
        ServiceAnalyticsDataDto service1Data,
        ServiceAnalyticsDataDto service2Data,
        decimal threshold,
        CancellationToken cancellationToken = default);

    Task<List<ServiceCorrelationDto>> AnalyzeMetricCorrelationsAsync(
        Dictionary<string, ServiceAnalyticsDataDto> serviceData,
        List<string> metrics,
        decimal threshold,
        CancellationToken cancellationToken = default);

    Task<decimal> CalculateCorrelationCoefficientAsync(
        List<decimal> values1,
        List<decimal> values2,
        CancellationToken cancellationToken = default);

    Task<Dictionary<string, decimal>> AnalyzeCrossServiceImpactAsync(
        string sourceService,
        List<string> targetServices,
        DateRangeDto dateRange,
        CancellationToken cancellationToken = default);
}

// ===== TEMPLATE AND CONFIGURATION SERVICES =====

public interface IReportTemplateService
{
    Task<CustomReportTemplateDto?> GetTemplateAsync(
        string templateId,
        CancellationToken cancellationToken = default);

    Task<List<CustomReportTemplateDto>> GetUserTemplatesAsync(
        Guid userId,
        CancellationToken cancellationToken = default);

    Task<List<CustomReportTemplateDto>> GetPublicTemplatesAsync(
        CancellationToken cancellationToken = default);

    Task SaveReportAsTemplateAsync(
        ComprehensiveReportDto report,
        string templateName,
        Guid createdBy,
        CancellationToken cancellationToken = default);

    Task<CustomReportTemplateDto> CloneTemplateAsync(
        string templateId,
        string newName,
        Guid clonedBy,
        CancellationToken cancellationToken = default);
}

public interface IReportConfigurationValidator
{
    Task<ValidationResult> ValidateConfigurationAsync(
        ReportConfigurationDto configuration,
        CancellationToken cancellationToken = default);

    Task<ValidationResult> ValidateFieldConfigurationAsync(
        List<ReportFieldDto> fields,
        List<string> dataSources,
        CancellationToken cancellationToken = default);

    Task<ValidationResult> ValidateVisualizationConfigurationAsync(
        List<ReportVisualizationConfigDto> visualizations,
        List<ReportFieldDto> fields,
        CancellationToken cancellationToken = default);
}

public interface IDataSourceValidator
{
    Task<bool> ValidateDataSourceAsync(
        string dataSource,
        CancellationToken cancellationToken = default);

    Task<bool> ValidateFieldExistsAsync(
        string dataSource,
        string fieldName,
        CancellationToken cancellationToken = default);

    Task<List<string>> GetAvailableFieldsAsync(
        string dataSource,
        CancellationToken cancellationToken = default);

    Task<Dictionary<string, string>> GetFieldTypesAsync(
        string dataSource,
        CancellationToken cancellationToken = default);
}

// ===== EXPORT AND SCHEDULING SERVICES =====

public interface IReportExportService
{
    Task<ExportResultDto> ExportReportAsync(
        ComprehensiveReportDto report,
        string format,
        Dictionary<string, object> options,
        CancellationToken cancellationToken = default);

    Task<ExportResultDto> ExportDataAsync(
        Dictionary<string, object> data,
        string format,
        string fileName,
        CancellationToken cancellationToken = default);

    Task<List<string>> GetSupportedFormatsAsync(CancellationToken cancellationToken = default);

    Task<byte[]> GeneratePdfReportAsync(
        ComprehensiveReportDto report,
        Dictionary<string, object> options,
        CancellationToken cancellationToken = default);

    Task<byte[]> GenerateExcelReportAsync(
        ComprehensiveReportDto report,
        Dictionary<string, object> options,
        CancellationToken cancellationToken = default);
}

public interface IReportSchedulingService
{
    Task<Guid> ScheduleReportAsync(
        Guid templateId,
        string scheduleExpression,
        Dictionary<string, object> parameters,
        List<string> recipients,
        Guid scheduledBy,
        CancellationToken cancellationToken = default);

    Task<List<ScheduledReportDto>> GetScheduledReportsAsync(
        Guid userId,
        CancellationToken cancellationToken = default);

    Task UpdateScheduleAsync(
        Guid scheduleId,
        string newScheduleExpression,
        CancellationToken cancellationToken = default);

    Task CancelScheduleAsync(
        Guid scheduleId,
        CancellationToken cancellationToken = default);

    Task<List<ReportExecutionHistoryDto>> GetExecutionHistoryAsync(
        Guid scheduleId,
        CancellationToken cancellationToken = default);
}

// IReportGenerationService is already defined in IReportGenerationService.cs - using that instead

// ===== REPOSITORY INTERFACES =====

public interface IReportTemplateRepository
{
    Task<CustomReportTemplateDto?> GetTemplateAsync(
        Guid templateId,
        CancellationToken cancellationToken = default);

    Task<CustomReportTemplateDto?> GetTemplateByNameAsync(
        string name,
        Guid userId,
        CancellationToken cancellationToken = default);

    Task<List<CustomReportTemplateDto>> GetUserTemplatesAsync(
        Guid userId,
        CancellationToken cancellationToken = default);

    Task<List<CustomReportTemplateDto>> GetPublicTemplatesAsync(
        CancellationToken cancellationToken = default);

    Task CreateTemplateAsync(
        CustomReportTemplateDto template,
        CancellationToken cancellationToken = default);

    Task UpdateTemplateAsync(
        CustomReportTemplateDto template,
        CancellationToken cancellationToken = default);

    Task DeleteTemplateAsync(
        Guid templateId,
        CancellationToken cancellationToken = default);

    Task UpdateTemplateUsageAsync(
        Guid templateId,
        CancellationToken cancellationToken = default);
}



public interface ICrossServiceAnalyticsRepository
{
    Task SaveAnalyticsResultsAsync(
        CrossServiceAnalyticsDto analytics,
        string name,
        Guid savedBy,
        CancellationToken cancellationToken = default);

    Task<List<CrossServiceAnalyticsDto>> GetSavedAnalyticsAsync(
        Guid userId,
        CancellationToken cancellationToken = default);

    Task<CrossServiceAnalyticsDto?> GetAnalyticsResultAsync(
        Guid analyticsId,
        CancellationToken cancellationToken = default);
}

public interface IUnitOfWork
{
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
}

// ===== SUPPORTING CLASSES =====

public class ValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
}

public class ScheduledReportDto
{
    public Guid ScheduleId { get; set; }
    public Guid TemplateId { get; set; }
    public string TemplateName { get; set; } = string.Empty;
    public string ScheduleExpression { get; set; } = string.Empty;
    public DateTime NextRun { get; set; }
    public DateTime? LastRun { get; set; }
    public bool IsActive { get; set; }
    public List<string> Recipients { get; set; } = new();
    public Dictionary<string, object> Parameters { get; set; } = new();
}

public class ReportExecutionHistoryDto
{
    public Guid ExecutionId { get; set; }
    public Guid ScheduleId { get; set; }
    public DateTime ExecutedAt { get; set; }
    public string Status { get; set; } = string.Empty; // Success, Failed, Cancelled
    public TimeSpan ExecutionTime { get; set; }
    public string? ErrorMessage { get; set; }
    public string? ReportUrl { get; set; }
    public int RecipientCount { get; set; }
}

public class VisualizationRequest
{
    public AggregatedDataResult Data { get; set; } = new();
    public List<string> ChartTypes { get; set; } = new();
    public Dictionary<string, object> Customizations { get; set; } = new();
    public Dictionary<string, object> VisualizationSettings { get; set; } = new();
}
