using DataStorage.Domain.Enums;

namespace DataStorage.Application.DTOs;

public class DocumentDto
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; }
    public DocumentType DocumentType { get; set; }
    public DocumentCategory Category { get; set; }
    public DocumentStatus Status { get; set; }
    public AccessLevel AccessLevel { get; set; }
    
    // File Information
    public FileMetadataDto FileMetadata { get; set; } = null!;
    public StorageLocationDto StorageLocation { get; set; } = null!;
    
    // Ownership
    public Guid OwnerId { get; set; }
    public string OwnerType { get; set; } = string.Empty;
    
    // Versioning
    public int Version { get; set; }
    public Guid? ParentDocumentId { get; set; }
    public bool IsLatestVersion { get; set; }
    
    // Timestamps
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public DateTime? ExpiresAt { get; set; }
    
    // Metadata
    public Dictionary<string, string> Tags { get; set; } = new();
    public string? ExternalReference { get; set; }
    
    // Access URL
    public string? AccessUrl { get; set; }
    public string? ThumbnailUrl { get; set; }
}

public class FileMetadataDto
{
    public string FileName { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    public long FileSizeBytes { get; set; }
    public string FileExtension { get; set; } = string.Empty;
    public string? Checksum { get; set; }
    public Dictionary<string, string> CustomProperties { get; set; } = new();
    
    // Computed properties
    public string FileSizeFormatted => FormatFileSize(FileSizeBytes);
    public bool IsImage => ContentType.StartsWith("image/", StringComparison.OrdinalIgnoreCase);
    public bool IsPdf => ContentType.Equals("application/pdf", StringComparison.OrdinalIgnoreCase);
    
    private static string FormatFileSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB", "TB" };
        double len = bytes;
        int order = 0;
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }
        return $"{len:0.##} {sizes[order]}";
    }
}

public class StorageLocationDto
{
    public StorageProvider Provider { get; set; }
    public string ContainerName { get; set; } = string.Empty;
    public string FilePath { get; set; } = string.Empty;
    public string? CDNUrl { get; set; }
    public string? Region { get; set; }
    public Dictionary<string, string> ProviderSpecificSettings { get; set; } = new();
    public string FullPath { get; set; } = string.Empty;
    public string AccessUrl { get; set; } = string.Empty;
}

public class DocumentVersionDto
{
    public Guid Id { get; set; }
    public Guid DocumentId { get; set; }
    public int VersionNumber { get; set; }
    public FileMetadataDto FileMetadata { get; set; } = null!;
    public StorageLocationDto StorageLocation { get; set; } = null!;
    public string? ChangeDescription { get; set; }
    public DateTime CreatedAt { get; set; }
    public Guid CreatedBy { get; set; }
    public bool IsActive { get; set; }
}

public class DocumentAccessDto
{
    public Guid Id { get; set; }
    public Guid DocumentId { get; set; }
    public Guid UserId { get; set; }
    public string UserType { get; set; } = string.Empty;
    public AccessLevel AccessLevel { get; set; }
    public DateTime GrantedAt { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public bool IsActive { get; set; }
}

public class DocumentSearchResultDto
{
    public IEnumerable<DocumentDto> Documents { get; set; } = new List<DocumentDto>();
    public int TotalCount { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
    public bool HasNextPage => PageNumber < TotalPages;
    public bool HasPreviousPage => PageNumber > 1;
}

public class DocumentAnalyticsDto
{
    public Dictionary<DocumentType, int> DocumentCountByType { get; set; } = new();
    public Dictionary<DocumentStatus, int> DocumentCountByStatus { get; set; } = new();
    public Dictionary<DocumentCategory, int> DocumentCountByCategory { get; set; } = new();
    public long TotalStorageSize { get; set; }
    public int TotalDocuments { get; set; }
    public DateTime GeneratedAt { get; set; }
    
    public string TotalStorageSizeFormatted => FormatFileSize(TotalStorageSize);
    
    private static string FormatFileSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB", "TB" };
        double len = bytes;
        int order = 0;
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }
        return $"{len:0.##} {sizes[order]}";
    }
}
