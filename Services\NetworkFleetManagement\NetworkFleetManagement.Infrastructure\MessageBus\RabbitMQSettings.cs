namespace NetworkFleetManagement.Infrastructure.MessageBus;

public class RabbitMQSettings
{
    public const string SectionName = "RabbitMQ";
    
    public string HostName { get; set; } = "localhost";
    public int Port { get; set; } = 5672;
    public string UserName { get; set; } = "guest";
    public string Password { get; set; } = "guest";
    public string VirtualHost { get; set; } = "/";
    public string ExchangeName { get; set; } = "tli.events";
    public string QueueName { get; set; } = "network-fleet.events";
    public bool Durable { get; set; } = true;
    public bool AutoDelete { get; set; } = false;
    public int RetryCount { get; set; } = 3;
    public int RetryDelaySeconds { get; set; } = 5;
}
