using Azure.Messaging.ServiceBus;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NetworkFleetManagement.Infrastructure.MessageBus;

namespace NetworkFleetManagement.Infrastructure.HealthChecks;

public class AzureServiceBusHealthCheck : IHealthCheck
{
    private readonly AzureServiceBusSettings _settings;
    private readonly ILogger<AzureServiceBusHealthCheck> _logger;

    public AzureServiceBusHealthCheck(IOptions<AzureServiceBusSettings> settings, ILogger<AzureServiceBusHealthCheck> logger)
    {
        _settings = settings.Value;
        _logger = logger;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            await using var client = new ServiceBusClient(_settings.ConnectionString);
            
            // Test connection by getting topic properties
            var adminClient = new ServiceBusAdministrationClient(_settings.ConnectionString);
            var topicExists = await adminClient.TopicExistsAsync(_settings.TopicName, cancellationToken);
            
            if (!topicExists)
            {
                var data = new Dictionary<string, object>
                {
                    ["connectionString"] = MaskConnectionString(_settings.ConnectionString),
                    ["topicName"] = _settings.TopicName,
                    ["error"] = "Topic does not exist"
                };

                return HealthCheckResult.Degraded("Azure Service Bus topic does not exist", null, data);
            }

            var data2 = new Dictionary<string, object>
            {
                ["connectionString"] = MaskConnectionString(_settings.ConnectionString),
                ["topicName"] = _settings.TopicName,
                ["subscriptionName"] = _settings.SubscriptionName
            };

            return HealthCheckResult.Healthy("Azure Service Bus is healthy", data2);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Azure Service Bus health check failed");
            
            var data = new Dictionary<string, object>
            {
                ["connectionString"] = MaskConnectionString(_settings.ConnectionString),
                ["topicName"] = _settings.TopicName,
                ["error"] = ex.Message
            };

            return HealthCheckResult.Unhealthy("Azure Service Bus is unhealthy", ex, data);
        }
    }

    private static string MaskConnectionString(string connectionString)
    {
        if (string.IsNullOrEmpty(connectionString))
            return "Not configured";

        // Mask sensitive parts of the connection string
        var parts = connectionString.Split(';');
        var maskedParts = parts.Select(part =>
        {
            if (part.StartsWith("SharedAccessKey=", StringComparison.OrdinalIgnoreCase))
                return "SharedAccessKey=***";
            return part;
        });

        return string.Join(";", maskedParts);
    }
}
