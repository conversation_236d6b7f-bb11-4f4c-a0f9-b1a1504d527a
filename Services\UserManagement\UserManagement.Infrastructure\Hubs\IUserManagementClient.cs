namespace UserManagement.Infrastructure.Hubs
{
    public interface IUserManagementClient
    {
        Task UserApproved(UserApprovedNotification notification);
        Task UserRejected(UserRejectedNotification notification);
        Task DocumentsApproved(DocumentsApprovedNotification notification);
        Task DocumentsRejected(DocumentsRejectedNotification notification);
        Task DocumentOcrProcessed(DocumentOcrProcessedNotification notification);
        Task ProfileStatusChanged(ProfileStatusChangedNotification notification);
        Task NewUserRegistered(NewUserRegisteredNotification notification);
        Task DocumentSubmitted(DocumentSubmittedNotification notification);
        Task SystemNotification(SystemNotification notification);
    }

    public class UserApprovedNotification
    {
        public Guid UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string UserType { get; set; } = string.Empty;
        public DateTime ApprovedAt { get; set; }
        public string ApprovedBy { get; set; } = string.Empty;
        public string? Notes { get; set; }
    }

    public class UserRejectedNotification
    {
        public Guid UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string UserType { get; set; } = string.Empty;
        public string Reason { get; set; } = string.Empty;
        public DateTime RejectedAt { get; set; }
        public string RejectedBy { get; set; } = string.Empty;
        public string? Notes { get; set; }
    }

    public class DocumentsApprovedNotification
    {
        public Guid SubmissionId { get; set; }
        public Guid UserId { get; set; }
        public string UserType { get; set; } = string.Empty;
        public List<string> DocumentTypes { get; set; } = new();
        public List<string> ApprovedDocuments { get; set; } = new();
        public DateTime ApprovedAt { get; set; }
        public DateTime ProcessedAt { get; set; }
        public string ApprovedBy { get; set; } = string.Empty;
        public string? Notes { get; set; }
    }

    public class DocumentsRejectedNotification
    {
        public Guid SubmissionId { get; set; }
        public Guid UserId { get; set; }
        public string UserType { get; set; } = string.Empty;
        public List<DocumentRejectionInfo> RejectedDocuments { get; set; } = new();
        public DateTime RejectedAt { get; set; }
        public DateTime ProcessedAt { get; set; }
        public string RejectedBy { get; set; } = string.Empty;
        public string? Notes { get; set; }
    }

    public class DocumentRejectionInfo
    {
        public string DocumentType { get; set; } = string.Empty;
        public string Action { get; set; } = string.Empty;
        public string Reason { get; set; } = string.Empty;
    }

    public class DocumentOcrProcessedNotification
    {
        public Guid SubmissionId { get; set; }
        public Guid UserId { get; set; }
        public string DocumentType { get; set; } = string.Empty;
        public decimal ConfidenceScore { get; set; }
        public bool RequiresManualReview { get; set; }
        public bool AutoApproved { get; set; }
        public List<string> ValidationErrors { get; set; } = new();
        public DateTime ProcessedAt { get; set; }
    }

    public class ProfileStatusChangedNotification
    {
        public Guid UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string UserType { get; set; } = string.Empty;
        public string PreviousStatus { get; set; } = string.Empty;
        public string NewStatus { get; set; } = string.Empty;
        public DateTime ChangedAt { get; set; }
        public string ChangedBy { get; set; } = string.Empty;
        public string? Reason { get; set; }
    }

    public class NewUserRegisteredNotification
    {
        public Guid UserId { get; set; }
        public string UserType { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public DateTime RegisteredAt { get; set; }
    }

    public class DocumentSubmittedNotification
    {
        public Guid SubmissionId { get; set; }
        public Guid UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string UserType { get; set; } = string.Empty;
        public string DocumentType { get; set; } = string.Empty;
        public int DocumentCount { get; set; }
        public DateTime SubmittedAt { get; set; }
        public bool RequiresReview { get; set; }
    }

    public class SystemNotification
    {
        public Guid Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public Dictionary<string, object> Data { get; set; } = new();
        public DateTime CreatedAt { get; set; }
        public DateTime Timestamp { get; set; }
        public string? TargetUserId { get; set; }
        public string? TargetRole { get; set; }
    }
}
