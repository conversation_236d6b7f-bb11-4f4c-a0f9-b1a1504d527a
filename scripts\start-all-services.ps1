# TLI Microservices - Enhanced Service Startup Script
# This script starts all microservices with comprehensive monitoring and health checks

param(
    [string]$Environment = "Development",
    [switch]$StartInfrastructure = $true,
    [switch]$StartServices = $true,
    [switch]$StartGateway = $true,
    [int]$DelayBetweenServices = 5,
    [string[]]$ServicesToStart = @(),
    [switch]$SkipHealthChecks = $false,
    [switch]$Verbose = $false
)

$ErrorActionPreference = "Continue"

Write-Host "🚀 TLI Microservices Platform - Enhanced Startup" -ForegroundColor Cyan
Write-Host "=================================================" -ForegroundColor Cyan
Write-Host "Environment: $Environment" -ForegroundColor White
Write-Host "Start Infrastructure: $StartInfrastructure" -ForegroundColor White
Write-Host "Start Services: $StartServices" -ForegroundColor White
Write-Host "Start Gateway: $StartGateway" -ForegroundColor White
Write-Host ""

# Global variables for tracking
$Global:StartedProcesses = @()
$Global:ServiceStatus = @{}

# Function to log with timestamp
function Write-LogMessage {
    param(
        [string]$Message,
        [string]$Level = "INFO",
        [string]$Color = "White"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    if ($Verbose -or $Level -eq "ERROR" -or $Level -eq "WARN") {
        Write-Host $logMessage -ForegroundColor $Color
    }
}

# Function to start a service with enhanced monitoring
function Start-ServiceEnhanced {
    param(
        [string]$ServiceName,
        [string]$ServicePath,
        [int]$Port,
        [string]$ProjectFile = "",
        [hashtable]$EnvironmentVariables = @{}
    )
    
    Write-Host "🔄 Starting $ServiceName on port $Port..." -ForegroundColor Yellow
    Write-LogMessage "Starting service: $ServiceName at $ServicePath" "INFO"
    
    if (-not (Test-Path $ServicePath)) {
        Write-Warning "⚠️  Service path not found: $ServicePath"
        Write-LogMessage "Service path not found: $ServicePath" "ERROR" "Red"
        return $false
    }
    
    try {
        $originalLocation = Get-Location
        Set-Location $ServicePath
        
        # Set environment variables
        $env:ASPNETCORE_ENVIRONMENT = $Environment
        $env:ASPNETCORE_URLS = "https://localhost:$Port;http://localhost:$($Port + 1000)"
        $env:ASPNETCORE_HTTPS_PORT = $Port
        
        # Set custom environment variables
        foreach ($key in $EnvironmentVariables.Keys) {
            Set-Item -Path "Env:$key" -Value $EnvironmentVariables[$key]
        }
        
        # Prepare startup arguments
        $arguments = @("run")
        if ($ProjectFile) {
            $arguments += @("--project", $ProjectFile)
        }
        
        # Add configuration for the environment
        $arguments += @("--environment", $Environment)
        
        # Start the service process
        $processInfo = New-Object System.Diagnostics.ProcessStartInfo
        $processInfo.FileName = "dotnet"
        $processInfo.Arguments = $arguments -join " "
        $processInfo.WorkingDirectory = $ServicePath
        $processInfo.UseShellExecute = $false
        $processInfo.RedirectStandardOutput = $true
        $processInfo.RedirectStandardError = $true
        $processInfo.CreateNoWindow = $true
        
        # Set environment variables for the process
        $processInfo.EnvironmentVariables["ASPNETCORE_ENVIRONMENT"] = $Environment
        $processInfo.EnvironmentVariables["ASPNETCORE_URLS"] = "https://localhost:$Port;http://localhost:$($Port + 1000)"
        
        $process = [System.Diagnostics.Process]::Start($processInfo)
        
        # Track the process
        $Global:StartedProcesses += @{
            ServiceName = $ServiceName
            Process = $process
            Port = $Port
            StartTime = Get-Date
        }
        
        $Global:ServiceStatus[$ServiceName] = @{
            Status = "Starting"
            Port = $Port
            ProcessId = $process.Id
            StartTime = Get-Date
        }
        
        Write-Host "✅ $ServiceName started successfully (PID: $($process.Id))" -ForegroundColor Green
        Write-LogMessage "Service started: $ServiceName (PID: $($process.Id))" "INFO"
        return $true
    }
    catch {
        Write-Error "❌ Failed to start $ServiceName`: $_"
        Write-LogMessage "Failed to start service: $ServiceName - $_" "ERROR" "Red"
        return $false
    }
    finally {
        Set-Location $originalLocation
    }
}

# Function to check service health with retry logic
function Test-ServiceHealthEnhanced {
    param(
        [string]$ServiceName,
        [int]$Port,
        [int]$TimeoutSeconds = 60,
        [int]$RetryInterval = 2
    )
    
    $healthUrl = "https://localhost:$Port/health"
    $httpHealthUrl = "http://localhost:$($Port + 1000)/health"
    $maxAttempts = [math]::Floor($TimeoutSeconds / $RetryInterval)
    
    Write-LogMessage "Starting health check for $ServiceName at $healthUrl" "INFO"
    
    for ($i = 1; $i -le $maxAttempts; $i++) {
        try {
            # Try HTTPS first, then HTTP
            $response = $null
            try {
                $response = Invoke-WebRequest -Uri $healthUrl -TimeoutSec 5 -UseBasicParsing -SkipCertificateCheck -ErrorAction Stop
            }
            catch {
                $response = Invoke-WebRequest -Uri $httpHealthUrl -TimeoutSec 5 -UseBasicParsing -ErrorAction Stop
            }
            
            if ($response.StatusCode -eq 200) {
                Write-Host "✅ $ServiceName is healthy" -ForegroundColor Green
                Write-LogMessage "Service health check passed: $ServiceName" "INFO"
                
                $Global:ServiceStatus[$ServiceName].Status = "Healthy"
                $Global:ServiceStatus[$ServiceName].HealthCheckTime = Get-Date
                return $true
            }
        }
        catch {
            Write-LogMessage "Health check attempt $i failed for $ServiceName`: $($_.Exception.Message)" "WARN" "Yellow"
        }
        
        if ($i -lt $maxAttempts) {
            $remainingTime = ($maxAttempts - $i) * $RetryInterval
            Write-Host "⏳ Waiting for $ServiceName to be ready... ($i/$maxAttempts, ${remainingTime}s remaining)" -ForegroundColor Yellow
            Start-Sleep -Seconds $RetryInterval
        }
    }
    
    Write-Warning "⚠️  $ServiceName health check failed after $TimeoutSeconds seconds"
    Write-LogMessage "Service health check failed: $ServiceName after $TimeoutSeconds seconds" "ERROR" "Red"
    
    $Global:ServiceStatus[$ServiceName].Status = "Unhealthy"
    return $false
}

# Function to check infrastructure readiness
function Test-InfrastructureReadiness {
    Write-Host "🔍 Checking infrastructure readiness..." -ForegroundColor Yellow
    
    $infrastructureServices = @(
        @{ Name = "PostgreSQL"; Host = "localhost"; Port = 5432 },
        @{ Name = "Redis"; Host = "localhost"; Port = 6379 },
        @{ Name = "RabbitMQ"; Host = "localhost"; Port = 5672 }
    )
    
    $readyServices = 0
    foreach ($service in $infrastructureServices) {
        try {
            $connection = New-Object System.Net.Sockets.TcpClient
            $connection.Connect($service.Host, $service.Port)
            $connection.Close()
            Write-Host "✅ $($service.Name) is ready" -ForegroundColor Green
            $readyServices++
        }
        catch {
            Write-Warning "⚠️  $($service.Name) is not ready"
        }
    }
    
    return $readyServices -eq $infrastructureServices.Count
}

# Function to display service status dashboard
function Show-ServiceDashboard {
    Write-Host ""
    Write-Host "📊 Service Status Dashboard" -ForegroundColor Cyan
    Write-Host "============================" -ForegroundColor Cyan
    
    foreach ($serviceName in $Global:ServiceStatus.Keys) {
        $status = $Global:ServiceStatus[$serviceName]
        $statusColor = switch ($status.Status) {
            "Healthy" { "Green" }
            "Starting" { "Yellow" }
            "Unhealthy" { "Red" }
            default { "Gray" }
        }
        
        $uptime = if ($status.StartTime) {
            $elapsed = (Get-Date) - $status.StartTime
            "$($elapsed.Minutes)m $($elapsed.Seconds)s"
        } else {
            "N/A"
        }
        
        Write-Host "  $serviceName" -NoNewline
        Write-Host " [$($status.Status)]" -ForegroundColor $statusColor -NoNewline
        Write-Host " - Port: $($status.Port), PID: $($status.ProcessId), Uptime: $uptime"
    }
    Write-Host ""
}

# Step 1: Start Infrastructure (if requested)
if ($StartInfrastructure) {
    Write-Host "🏗️  Step 1: Starting Infrastructure Services..." -ForegroundColor Cyan
    
    # Check if Docker is running
    try {
        docker version | Out-Null
        Write-Host "✅ Docker is running" -ForegroundColor Green
        
        # Start infrastructure services
        Write-Host "🔄 Starting infrastructure with Docker Compose..." -ForegroundColor Yellow
        docker-compose -f docker-compose.infrastructure.yml up -d
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Infrastructure services started" -ForegroundColor Green
            
            # Wait for infrastructure to be ready
            Write-Host "⏳ Waiting for infrastructure services to be ready..." -ForegroundColor Yellow
            Start-Sleep -Seconds 30
            
            # Check infrastructure readiness
            $infraReady = Test-InfrastructureReadiness
            if (-not $infraReady) {
                Write-Warning "⚠️  Some infrastructure services are not ready. Continuing anyway..."
            }
        } else {
            Write-Warning "⚠️  Failed to start infrastructure services"
        }
    }
    catch {
        Write-Warning "⚠️  Docker is not running or not available. Skipping infrastructure startup."
        Write-Host "   Please ensure PostgreSQL, Redis, and RabbitMQ are running manually." -ForegroundColor Yellow
    }
    
    Write-Host ""
}

# Step 2: Start Core Services (if requested)
if ($StartServices) {
    Write-Host "🎯 Step 2: Starting Core Services..." -ForegroundColor Cyan
    
    # Define services in startup order
    $services = @(
        @{ Name = "Identity"; Path = "Identity/Identity.API"; Port = 7000 },
        @{ Name = "UserManagement"; Path = "Services/UserManagement/UserManagement.API"; Port = 7001 },
        @{ Name = "OrderManagement"; Path = "Services/OrderManagement/OrderManagement.API"; Port = 7002 },
        @{ Name = "SubscriptionManagement"; Path = "Services/SubscriptionManagement/SubscriptionManagement.API"; Port = 7003 },
        @{ Name = "TripManagement"; Path = "Services/TripManagement/TripManagement.API"; Port = 7004 },
        @{ Name = "NetworkFleetManagement"; Path = "Services/NetworkFleetManagement/NetworkFleetManagement.API"; Port = 7005 },
        @{ Name = "FinancialPayment"; Path = "Services/FinancialPayment/FinancialPayment.API"; Port = 7006 },
        @{ Name = "CommunicationNotification"; Path = "Services/CommunicationNotification/CommunicationNotification.API"; Port = 7007 },
        @{ Name = "AnalyticsBIService"; Path = "Services/AnalyticsBIService/AnalyticsBIService.API"; Port = 7008 },
        @{ Name = "DataStorage"; Path = "Services/DataStorage/DataStorage.API"; Port = 7009 },
        @{ Name = "MonitoringObservability"; Path = "Services/MonitoringObservability/MonitoringObservability.API"; Port = 7010 },
        @{ Name = "AuditCompliance"; Path = "Services/AuditCompliance/AuditCompliance.API"; Port = 7011 },
        @{ Name = "MobileWorkflow"; Path = "Services/MobileWorkflow/MobileWorkflow.API"; Port = 7012 }
    )
    
    # Filter services if specific ones are requested
    if ($ServicesToStart.Count -gt 0) {
        $services = $services | Where-Object { $_.Name -in $ServicesToStart }
    }
    
    $startedServices = @()
    
    foreach ($service in $services) {
        $success = Start-ServiceEnhanced -ServiceName $service.Name -ServicePath $service.Path -Port $service.Port
        
        if ($success) {
            $startedServices += $service
            
            # Wait between service starts
            if ($DelayBetweenServices -gt 0) {
                Start-Sleep -Seconds $DelayBetweenServices
            }
        }
    }
    
    Write-Host ""
    Write-Host "⏳ Waiting for services to initialize..." -ForegroundColor Yellow
    Start-Sleep -Seconds 15
    
    # Health check all started services (if not skipped)
    if (-not $SkipHealthChecks) {
        Write-Host "🔍 Checking service health..." -ForegroundColor Cyan
        $healthyServices = 0
        
        foreach ($service in $startedServices) {
            $isHealthy = Test-ServiceHealthEnhanced -ServiceName $service.Name -Port $service.Port -TimeoutSeconds 60
            if ($isHealthy) {
                $healthyServices++
            }
        }
        
        Write-Host ""
        Write-Host "📊 Service Health Summary: $healthyServices/$($startedServices.Count) services healthy" -ForegroundColor Cyan
    }
    
    Write-Host ""
}

# Step 3: Start API Gateway (if requested)
if ($StartGateway) {
    Write-Host "🌐 Step 3: Starting API Gateway..." -ForegroundColor Cyan
    
    $gatewaySuccess = Start-ServiceEnhanced -ServiceName "API Gateway" -ServicePath "ApiGateway" -Port 5000
    
    if ($gatewaySuccess) {
        Start-Sleep -Seconds 10
        
        if (-not $SkipHealthChecks) {
            $gatewayHealthy = Test-ServiceHealthEnhanced -ServiceName "API Gateway" -Port 5000 -TimeoutSeconds 30
            
            if ($gatewayHealthy) {
                Write-Host ""
                Write-Host "🎉 TLI Microservices Platform Started Successfully!" -ForegroundColor Green
            }
        }
    }
}

# Display final status
Show-ServiceDashboard

Write-Host ""
Write-Host "🌐 Access Points:" -ForegroundColor Cyan
Write-Host "   API Gateway: https://localhost:5000" -ForegroundColor White
Write-Host "   Swagger UI: https://localhost:5000/swagger" -ForegroundColor White
Write-Host "   Health Checks: https://localhost:5000/health" -ForegroundColor White
Write-Host ""
Write-Host "📊 Monitoring:" -ForegroundColor Cyan
Write-Host "   Grafana: http://localhost:3000 (admin/admin123)" -ForegroundColor White
Write-Host "   Prometheus: http://localhost:9090" -ForegroundColor White
Write-Host "   Jaeger: http://localhost:16686" -ForegroundColor White
Write-Host "   Kibana: http://localhost:5601" -ForegroundColor White
Write-Host ""
Write-Host "✅ Startup process completed!" -ForegroundColor Green
Write-Host ""
Write-Host "💡 Tips:" -ForegroundColor Cyan
Write-Host "   - Use 'scripts/stop-services.ps1' to stop all services" -ForegroundColor White
Write-Host "   - Check logs in each service directory" -ForegroundColor White
Write-Host "   - Monitor service health at /health endpoints" -ForegroundColor White
Write-Host "   - Use -Verbose flag for detailed logging" -ForegroundColor White
