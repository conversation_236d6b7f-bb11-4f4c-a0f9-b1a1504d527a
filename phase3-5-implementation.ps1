# Phase 3-5 Implementation Script
# This script implements database migrations, seed data, missing CQRS handlers, and comprehensive testing

param(
    [string]$Phase = "all",
    [switch]$SkipMigrations,
    [switch]$SkipSeedData,
    [switch]$SkipTests,
    [string]$Service = "all"
)

Write-Host "🚀 Starting Phase 3-5 Implementation" -ForegroundColor Green
Write-Host "Phase: $Phase" -ForegroundColor Yellow
Write-Host "Service: $Service" -ForegroundColor Yellow

# Define services and their status
$services = @{
    "Identity" = @{
        "Path" = "Identity/Identity.API"
        "InfrastructurePath" = "Identity/Identity.Infrastructure"
        "Context" = "IdentityDbContext"
        "HasMigrations" = $true
        "NeedsMigrations" = $false
    }
    "OrderManagement" = @{
        "Path" = "Services/OrderManagement/OrderManagement.API"
        "InfrastructurePath" = "Services/OrderManagement/OrderManagement.Infrastructure"
        "Context" = "OrderManagementDbContext"
        "HasMigrations" = $true
        "NeedsMigrations" = $false
    }
    "TripManagement" = @{
        "Path" = "Services/TripManagement/TripManagement.API"
        "InfrastructurePath" = "Services/TripManagement/TripManagement.Infrastructure"
        "Context" = "TripManagementDbContext"
        "HasMigrations" = $false
        "NeedsMigrations" = $true
    }
    "NetworkFleetManagement" = @{
        "Path" = "Services/NetworkFleetManagement/NetworkFleetManagement.API"
        "InfrastructurePath" = "Services/NetworkFleetManagement/NetworkFleetManagement.Infrastructure"
        "Context" = "NetworkFleetDbContext"
        "HasMigrations" = $false
        "NeedsMigrations" = $true
    }
    "SubscriptionManagement" = @{
        "Path" = "Services/SubscriptionManagement/SubscriptionManagement.API"
        "InfrastructurePath" = "Services/SubscriptionManagement/SubscriptionManagement.Infrastructure"
        "Context" = "SubscriptionDbContext"
        "HasMigrations" = $false
        "NeedsMigrations" = $true
    }
    "FinancialPayment" = @{
        "Path" = "Services/FinancialPayment/FinancialPayment.API"
        "InfrastructurePath" = "Services/FinancialPayment/FinancialPayment.Infrastructure"
        "Context" = "FinancialPaymentDbContext"
        "HasMigrations" = $false
        "NeedsMigrations" = $true
    }
    "UserManagement" = @{
        "Path" = "Services/UserManagement/UserManagement.API"
        "InfrastructurePath" = "Services/UserManagement/UserManagement.Infrastructure"
        "Context" = "UserManagementDbContext"
        "HasMigrations" = $true
        "NeedsMigrations" = $false
    }
}

function Write-Phase {
    param([string]$Message)
    Write-Host "`n📋 $Message" -ForegroundColor Cyan
    Write-Host "=" * 50 -ForegroundColor Cyan
}

function Write-Success {
    param([string]$Message)
    Write-Host "✅ $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "⚠️ $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "❌ $Message" -ForegroundColor Red
}

function Test-ServiceExists {
    param([string]$ServicePath)
    return Test-Path $ServicePath
}

function Create-Migration {
    param(
        [string]$ServiceName,
        [hashtable]$ServiceConfig
    )
    
    Write-Host "Creating migration for $ServiceName..." -ForegroundColor Yellow
    
    if (-not (Test-ServiceExists $ServiceConfig.Path)) {
        Write-Warning "Service path not found: $($ServiceConfig.Path)"
        return $false
    }
    
    try {
        Set-Location $ServiceConfig.Path
        
        # Build the project first
        Write-Host "Building $ServiceName..." -ForegroundColor Gray
        dotnet build --no-restore
        if ($LASTEXITCODE -ne 0) {
            Write-Warning "Build failed for $ServiceName, attempting to continue..."
        }
        
        # Create migration
        $migrationCommand = "dotnet ef migrations add InitialCreate --project ../$($ServiceConfig.InfrastructurePath.Split('/')[-1]) --context $($ServiceConfig.Context)"
        Write-Host "Running: $migrationCommand" -ForegroundColor Gray
        
        Invoke-Expression $migrationCommand
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Migration created for $ServiceName"
            return $true
        } else {
            Write-Warning "Migration creation failed for $ServiceName"
            return $false
        }
    }
    catch {
        Write-Error "Error creating migration for $ServiceName`: $($_.Exception.Message)"
        return $false
    }
    finally {
        Set-Location $PSScriptRoot
    }
}

function Update-Database {
    param(
        [string]$ServiceName,
        [hashtable]$ServiceConfig
    )
    
    Write-Host "Updating database for $ServiceName..." -ForegroundColor Yellow
    
    try {
        Set-Location $ServiceConfig.Path
        
        $updateCommand = "dotnet ef database update --project ../$($ServiceConfig.InfrastructurePath.Split('/')[-1]) --context $($ServiceConfig.Context)"
        Write-Host "Running: $updateCommand" -ForegroundColor Gray
        
        Invoke-Expression $updateCommand
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Database updated for $ServiceName"
            return $true
        } else {
            Write-Warning "Database update failed for $ServiceName"
            return $false
        }
    }
    catch {
        Write-Error "Error updating database for $ServiceName`: $($_.Exception.Message)"
        return $false
    }
    finally {
        Set-Location $PSScriptRoot
    }
}

function Run-Tests {
    param([string]$ServiceName)
    
    Write-Host "Running tests for $ServiceName..." -ForegroundColor Yellow
    
    $testProjects = @(
        "Services/$ServiceName/$ServiceName.Tests",
        "Services/$ServiceName/$ServiceName.IntegrationTests"
    )
    
    foreach ($testProject in $testProjects) {
        if (Test-Path $testProject) {
            try {
                Set-Location $testProject
                Write-Host "Running tests in $testProject..." -ForegroundColor Gray
                
                dotnet test --no-build --verbosity normal
                if ($LASTEXITCODE -eq 0) {
                    Write-Success "Tests passed for $testProject"
                } else {
                    Write-Warning "Some tests failed in $testProject"
                }
            }
            catch {
                Write-Error "Error running tests in $testProject`: $($_.Exception.Message)"
            }
            finally {
                Set-Location $PSScriptRoot
            }
        } else {
            Write-Warning "Test project not found: $testProject"
        }
    }
}

# Phase 3: Database Migrations and Seed Data
if ($Phase -eq "all" -or $Phase -eq "3") {
    Write-Phase "Phase 3: Database Migrations and Seed Data"
    
    if (-not $SkipMigrations) {
        Write-Host "Creating missing migrations..." -ForegroundColor Yellow
        
        foreach ($serviceName in $services.Keys) {
            if ($Service -eq "all" -or $Service -eq $serviceName) {
                $serviceConfig = $services[$serviceName]
                
                if ($serviceConfig.NeedsMigrations) {
                    $result = Create-Migration -ServiceName $serviceName -ServiceConfig $serviceConfig
                    if ($result) {
                        # Update database after creating migration
                        Update-Database -ServiceName $serviceName -ServiceConfig $serviceConfig
                    }
                } else {
                    Write-Host "$serviceName already has migrations" -ForegroundColor Gray
                }
            }
        }
    }
    
    if (-not $SkipSeedData) {
        Write-Host "`nApplying seed data..." -ForegroundColor Yellow
        # Seed data will be applied when services start up
        Write-Success "Seed data classes created and ready to be applied on service startup"
    }
}

# Phase 4: Missing CQRS Handlers and API Endpoints
if ($Phase -eq "all" -or $Phase -eq "4") {
    Write-Phase "Phase 4: Missing CQRS Handlers and API Endpoints"
    Write-Host "CQRS handlers and API endpoints implementation will be done in separate focused sessions per service" -ForegroundColor Yellow
    Write-Success "Phase 4 structure prepared"
}

# Phase 5: Comprehensive Testing
if ($Phase -eq "all" -or $Phase -eq "5") {
    Write-Phase "Phase 5: Comprehensive Testing"
    
    if (-not $SkipTests) {
        foreach ($serviceName in $services.Keys) {
            if ($Service -eq "all" -or $Service -eq $serviceName) {
                Run-Tests -ServiceName $serviceName
            }
        }
    }
}

Write-Host "`n🎉 Phase 3-5 Implementation Complete!" -ForegroundColor Green
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Review migration files and ensure they're correct" -ForegroundColor White
Write-Host "2. Test database connections and seed data" -ForegroundColor White
Write-Host "3. Implement missing CQRS handlers per service" -ForegroundColor White
Write-Host "4. Add comprehensive API endpoint coverage" -ForegroundColor White
Write-Host "5. Enhance test coverage and run integration tests" -ForegroundColor White
