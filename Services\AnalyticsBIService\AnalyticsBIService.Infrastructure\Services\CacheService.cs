using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using System.Text.Json;
using AnalyticsBIService.Application.Interfaces;

namespace AnalyticsBIService.Infrastructure.Services;

/// <summary>
/// Redis-based cache service implementation
/// </summary>
public class CacheService : ICacheService
{
    private readonly IDistributedCache _distributedCache;
    private readonly IConnectionMultiplexer _redis;
    private readonly ILogger<CacheService> _logger;
    private readonly JsonSerializerOptions _jsonOptions;

    public CacheService(
        IDistributedCache distributedCache,
        IConnectionMultiplexer redis,
        ILogger<CacheService> logger)
    {
        _distributedCache = distributedCache;
        _redis = redis;
        _logger = logger;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false
        };
    }

    /// <summary>
    /// Get cached value by key
    /// </summary>
    public async Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            var cachedValue = await _distributedCache.GetStringAsync(key, cancellationToken);

            if (string.IsNullOrEmpty(cachedValue))
                return default;

            return JsonSerializer.Deserialize<T>(cachedValue, _jsonOptions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving cached value for key: {Key}", key);
            return default;
        }
    }

    /// <summary>
    /// Set cached value with expiration
    /// </summary>
    public async Task SetAsync<T>(string key, T value, TimeSpan? expiration = null, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            var serializedValue = JsonSerializer.Serialize(value, _jsonOptions);

            var options = new DistributedCacheEntryOptions();
            if (expiration.HasValue)
            {
                options.SetAbsoluteExpiration(expiration.Value);
            }
            else
            {
                // Default expiration of 1 hour
                options.SetAbsoluteExpiration(TimeSpan.FromHours(1));
            }

            await _distributedCache.SetStringAsync(key, serializedValue, options, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting cached value for key: {Key}", key);
        }
    }

    /// <summary>
    /// Remove cached value by key
    /// </summary>
    public async Task RemoveAsync(string key, CancellationToken cancellationToken = default)
    {
        try
        {
            await _distributedCache.RemoveAsync(key, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing cached value for key: {Key}", key);
        }
    }

    /// <summary>
    /// Remove cached values by pattern
    /// </summary>
    public async Task RemoveByPatternAsync(string pattern, CancellationToken cancellationToken = default)
    {
        try
        {
            var database = _redis.GetDatabase();
            var server = _redis.GetServer(_redis.GetEndPoints().First());

            var keys = server.Keys(pattern: pattern);

            foreach (var key in keys)
            {
                await database.KeyDeleteAsync(key);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing cached values by pattern: {Pattern}", pattern);
        }
    }

    /// <summary>
    /// Check if key exists in cache
    /// </summary>
    public async Task<bool> ExistsAsync(string key, CancellationToken cancellationToken = default)
    {
        try
        {
            var cachedValue = await _distributedCache.GetStringAsync(key, cancellationToken);
            return !string.IsNullOrEmpty(cachedValue);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if key exists: {Key}", key);
            return false;
        }
    }

    /// <summary>
    /// Get or set cached value with factory function
    /// </summary>
    public async Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiration = null, CancellationToken cancellationToken = default) where T : class
    {
        var cachedValue = await GetAsync<T>(key, cancellationToken);

        if (cachedValue != null)
            return cachedValue;

        var value = await factory();
        await SetAsync(key, value, expiration, cancellationToken);

        return value;
    }

    /// <summary>
    /// Set multiple cached values
    /// </summary>
    public async Task SetManyAsync<T>(Dictionary<string, T> keyValuePairs, TimeSpan? expiration = null, CancellationToken cancellationToken = default) where T : class
    {
        var tasks = keyValuePairs.Select(kvp => SetAsync(kvp.Key, kvp.Value, expiration, cancellationToken));
        await Task.WhenAll(tasks);
    }

    /// <summary>
    /// Get multiple cached values
    /// </summary>
    public async Task<Dictionary<string, T?>> GetManyAsync<T>(IEnumerable<string> keys, CancellationToken cancellationToken = default) where T : class
    {
        var tasks = keys.Select(async key => new { Key = key, Value = await GetAsync<T>(key, cancellationToken) });
        var results = await Task.WhenAll(tasks);

        return results.ToDictionary(r => r.Key, r => r.Value);
    }

    /// <summary>
    /// Increment numeric value in cache
    /// </summary>
    public async Task<long> IncrementAsync(string key, long value = 1, CancellationToken cancellationToken = default)
    {
        try
        {
            var database = _redis.GetDatabase();
            return await database.StringIncrementAsync(key, value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error incrementing value for key: {Key}", key);
            return 0;
        }
    }

    /// <summary>
    /// Set expiration for existing key
    /// </summary>
    public async Task<bool> ExpireAsync(string key, TimeSpan expiration, CancellationToken cancellationToken = default)
    {
        try
        {
            var database = _redis.GetDatabase();
            return await database.KeyExpireAsync(key, expiration);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting expiration for key: {Key}", key);
            return false;
        }
    }

    /// <summary>
    /// Add item to a list in cache
    /// </summary>
    public async Task<long> ListPushAsync<T>(string key, T value, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            var database = _redis.GetDatabase();
            var serializedValue = JsonSerializer.Serialize(value, _jsonOptions);
            return await database.ListLeftPushAsync(key, serializedValue);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error pushing to list for key: {Key}", key);
            return 0;
        }
    }

    /// <summary>
    /// Get list items from cache
    /// </summary>
    public async Task<List<T>> ListGetAsync<T>(string key, long start = 0, long stop = -1, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            var database = _redis.GetDatabase();
            var values = await database.ListRangeAsync(key, start, stop);
            var result = new List<T>();

            foreach (var value in values)
            {
                if (value.HasValue)
                {
                    var deserializedValue = JsonSerializer.Deserialize<T>(value, _jsonOptions);
                    if (deserializedValue != null)
                        result.Add(deserializedValue);
                }
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting list for key: {Key}", key);
            return new List<T>();
        }
    }

    /// <summary>
    /// Add item to a set in cache
    /// </summary>
    public async Task<bool> SetAddAsync<T>(string key, T value, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            var database = _redis.GetDatabase();
            var serializedValue = JsonSerializer.Serialize(value, _jsonOptions);
            return await database.SetAddAsync(key, serializedValue);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding to set for key: {Key}", key);
            return false;
        }
    }

    /// <summary>
    /// Get set members from cache
    /// </summary>
    public async Task<HashSet<T>> SetMembersAsync<T>(string key, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            var database = _redis.GetDatabase();
            var values = await database.SetMembersAsync(key);
            var result = new HashSet<T>();

            foreach (var value in values)
            {
                if (value.HasValue)
                {
                    var deserializedValue = JsonSerializer.Deserialize<T>(value, _jsonOptions);
                    if (deserializedValue != null)
                        result.Add(deserializedValue);
                }
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting set members for key: {Key}", key);
            return new HashSet<T>();
        }
    }

    /// <summary>
    /// Add or update hash field in cache
    /// </summary>
    public async Task HashSetAsync<T>(string key, string field, T value, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            var database = _redis.GetDatabase();
            var serializedValue = JsonSerializer.Serialize(value, _jsonOptions);
            await database.HashSetAsync(key, field, serializedValue);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting hash field for key: {Key}, field: {Field}", key, field);
        }
    }

    /// <summary>
    /// Get hash field from cache
    /// </summary>
    public async Task<T?> HashGetAsync<T>(string key, string field, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            var database = _redis.GetDatabase();
            var value = await database.HashGetAsync(key, field);

            if (!value.HasValue)
                return default;

            return JsonSerializer.Deserialize<T>(value, _jsonOptions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting hash field for key: {Key}, field: {Field}", key, field);
            return default;
        }
    }

    /// <summary>
    /// Get all hash fields from cache
    /// </summary>
    public async Task<Dictionary<string, T>> HashGetAllAsync<T>(string key, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            var database = _redis.GetDatabase();
            var hashValues = await database.HashGetAllAsync(key);
            var result = new Dictionary<string, T>();

            foreach (var hashValue in hashValues)
            {
                if (hashValue.Value.HasValue)
                {
                    var deserializedValue = JsonSerializer.Deserialize<T>(hashValue.Value, _jsonOptions);
                    if (deserializedValue != null)
                        result[hashValue.Name] = deserializedValue;
                }
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all hash fields for key: {Key}", key);
            return new Dictionary<string, T>();
        }
    }
}
