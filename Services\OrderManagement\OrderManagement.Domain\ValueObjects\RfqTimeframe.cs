using OrderManagement.Domain.Enums;

namespace OrderManagement.Domain.ValueObjects;

/// <summary>
/// Value object representing RFQ timeframe configuration
/// </summary>
public record RfqTimeframe
{
    public int Duration { get; init; }
    public TimeframeUnit Unit { get; init; }
    public DateTime CreatedAt { get; init; }
    public DateTime ExpiresAt { get; init; }
    public bool AllowExtensions { get; init; }
    public int MaxExtensions { get; init; }
    public int ExtensionCount { get; init; }

    // Parameterless constructor for EF Core
    public RfqTimeframe() { }

    public RfqTimeframe(
        int duration,
        TimeframeUnit unit,
        bool allowExtensions = true,
        int maxExtensions = 3)
    {
        if (duration <= 0)
            throw new ArgumentException("Duration must be greater than zero", nameof(duration));

        if (maxExtensions < 0)
            throw new ArgumentException("Max extensions cannot be negative", nameof(maxExtensions));

        Duration = duration;
        Unit = unit;
        CreatedAt = DateTime.UtcNow;
        ExpiresAt = CalculateExpirationDate(duration, unit);
        AllowExtensions = allowExtensions;
        MaxExtensions = maxExtensions;
        ExtensionCount = 0;
    }

    /// <summary>
    /// Check if the RFQ has expired
    /// </summary>
    public bool IsExpired => DateTime.UtcNow > ExpiresAt;

    /// <summary>
    /// Check if the RFQ is approaching expiration (within 24 hours)
    /// </summary>
    public bool IsApproachingExpiration => !IsExpired && DateTime.UtcNow > ExpiresAt.AddHours(-24);

    /// <summary>
    /// Check if the RFQ is expiring soon (within specified hours)
    /// </summary>
    public bool IsExpiringSoon(int hoursBeforeExpiration) =>
        !IsExpired && DateTime.UtcNow > ExpiresAt.AddHours(-hoursBeforeExpiration);

    /// <summary>
    /// Check if extensions are allowed
    /// </summary>
    public bool CanExtend => AllowExtensions && ExtensionCount < MaxExtensions;

    /// <summary>
    /// Get remaining time until expiration
    /// </summary>
    public TimeSpan RemainingTime => IsExpired ? TimeSpan.Zero : ExpiresAt - DateTime.UtcNow;

    /// <summary>
    /// Get total duration in minutes
    /// </summary>
    public int TotalMinutes => Unit switch
    {
        TimeframeUnit.Minutes => Duration,
        TimeframeUnit.Hours => Duration * 60,
        TimeframeUnit.Days => Duration * 24 * 60,
        _ => throw new ArgumentOutOfRangeException(nameof(Unit))
    };

    /// <summary>
    /// Create an extended timeframe
    /// </summary>
    public RfqTimeframe Extend(int extensionDuration, TimeframeUnit extensionUnit)
    {
        if (!CanExtend)
            throw new InvalidOperationException("Cannot extend RFQ timeframe - maximum extensions reached or extensions not allowed");

        var additionalMinutes = extensionUnit switch
        {
            TimeframeUnit.Minutes => extensionDuration,
            TimeframeUnit.Hours => extensionDuration * 60,
            TimeframeUnit.Days => extensionDuration * 24 * 60,
            _ => throw new ArgumentOutOfRangeException(nameof(extensionUnit))
        };

        var newExpiresAt = ExpiresAt.AddMinutes(additionalMinutes);

        return this with
        {
            ExpiresAt = newExpiresAt,
            ExtensionCount = ExtensionCount + 1
        };
    }

    private static DateTime CalculateExpirationDate(int duration, TimeframeUnit unit)
    {
        var now = DateTime.UtcNow;
        return unit switch
        {
            TimeframeUnit.Minutes => now.AddMinutes(duration),
            TimeframeUnit.Hours => now.AddHours(duration),
            TimeframeUnit.Days => now.AddDays(duration),
            _ => throw new ArgumentOutOfRangeException(nameof(unit))
        };
    }
}

/// <summary>
/// Value object representing an RFQ timeframe extension
/// </summary>
public record TimeframeExtension
{
    public int Duration { get; init; }
    public TimeframeUnit Unit { get; init; }
    public string Reason { get; init; } = string.Empty;
    public DateTime ExtendedAt { get; init; }
    public Guid ExtendedBy { get; init; }
    public DateTime NewExpiresAt { get; init; }

    // Parameterless constructor for EF Core
    public TimeframeExtension() { }

    public TimeframeExtension(
        int duration,
        TimeframeUnit unit,
        string reason,
        Guid extendedBy,
        DateTime newExpiresAt)
    {
        if (duration <= 0)
            throw new ArgumentException("Extension duration must be greater than zero", nameof(duration));

        if (string.IsNullOrWhiteSpace(reason))
            throw new ArgumentException("Extension reason is required", nameof(reason));

        Duration = duration;
        Unit = unit;
        Reason = reason;
        ExtendedAt = DateTime.UtcNow;
        ExtendedBy = extendedBy;
        NewExpiresAt = newExpiresAt;
    }

    /// <summary>
    /// Get extension duration in minutes
    /// </summary>
    public int ExtensionMinutes => Unit switch
    {
        TimeframeUnit.Minutes => Duration,
        TimeframeUnit.Hours => Duration * 60,
        TimeframeUnit.Days => Duration * 24 * 60,
        _ => throw new ArgumentOutOfRangeException(nameof(Unit))
    };
}
