using MediatR;
using Microsoft.Extensions.Logging;
using FinancialPayment.Application.Interfaces.Repositories;
using FinancialPayment.Application.Interfaces;
using Shared.Infrastructure.Interfaces;

namespace FinancialPayment.Application.Commands.FundEscrowAccount;

public class FundEscrowAccountCommandHandler : IRequestHandler<FundEscrowAccountCommand, bool>
{
    private readonly IEscrowAccountRepository _escrowAccountRepository;
    private readonly IEnhancedPaymentService _paymentService;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<FundEscrowAccountCommandHandler> _logger;

    public FundEscrowAccountCommandHandler(
        IEscrowAccountRepository escrowAccountRepository,
        IEnhancedPaymentService paymentService,
        IUnitOfWork unitOfWork,
        ILogger<FundEscrowAccountCommandHandler> logger)
    {
        _escrowAccountRepository = escrowAccountRepository;
        _paymentService = paymentService;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<bool> Handle(FundEscrowAccountCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Funding escrow account {EscrowAccountId} with amount {Amount}",
            request.EscrowAccountId, request.Amount.Amount);

        try
        {
            // Get escrow account
            var escrowAccount = await _escrowAccountRepository.GetByIdAsync(request.EscrowAccountId);
            if (escrowAccount == null)
            {
                throw new InvalidOperationException($"Escrow account {request.EscrowAccountId} not found");
            }

            // Process payment through payment service
            var paymentResult = await _paymentService.ProcessEscrowFundingAsync(
                request.EscrowAccountId,
                request.Amount.ToMoney(),
                request.PaymentMethodId);

            if (!paymentResult.IsSuccess)
            {
                _logger.LogWarning("Payment failed for escrow funding {EscrowAccountId}: {Error}",
                    request.EscrowAccountId, paymentResult.ErrorMessage);
                return false;
            }

            // Fund the escrow account
            escrowAccount.Fund(
                request.Amount.ToMoney(),
                paymentResult.TransactionId ?? string.Empty,
                request.Notes);

            // Save changes
            await _escrowAccountRepository.UpdateAsync(escrowAccount);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Successfully funded escrow account {EscrowAccountId} with transaction {TransactionId}",
                request.EscrowAccountId, paymentResult.TransactionId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error funding escrow account {EscrowAccountId}", request.EscrowAccountId);
            throw;
        }
    }
}
