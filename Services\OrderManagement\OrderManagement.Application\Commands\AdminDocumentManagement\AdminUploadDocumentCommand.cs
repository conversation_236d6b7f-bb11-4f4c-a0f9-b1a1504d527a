using MediatR;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Application.Commands.AdminDocumentManagement;

public class AdminUploadDocumentCommand : IRequest<Guid>
{
    public Guid OrderId { get; set; }
    public string FileName { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    public byte[] FileContent { get; set; } = Array.Empty<byte>();
    public DocumentType DocumentType { get; set; }
    public string? Description { get; set; }
    public bool OverrideValidation { get; set; } = false;
    public Guid AdminUserId { get; set; }
    public string? AdminUserName { get; set; }
    public string? AdminNotes { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
}

public class AdminVerifyDocumentCommand : IRequest
{
    public Guid DocumentId { get; set; }
    public DocumentStatus NewStatus { get; set; }
    public string? VerificationNotes { get; set; }
    public bool OverrideRequirements { get; set; } = false;
    public Guid AdminUserId { get; set; }
    public string? AdminUserName { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
}

public class AdminDeleteDocumentCommand : IRequest
{
    public Guid DocumentId { get; set; }
    public string Reason { get; set; } = string.Empty;
    public bool PermanentDelete { get; set; } = false;
    public Guid AdminUserId { get; set; }
    public string? AdminUserName { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
}

public class AdminBulkDocumentActionCommand : IRequest<BulkDocumentActionResult>
{
    public List<Guid> DocumentIds { get; set; } = new();
    public BulkDocumentAction Action { get; set; }
    public DocumentStatus? NewStatus { get; set; }
    public string? Notes { get; set; }
    public string? Reason { get; set; }
    public Guid AdminUserId { get; set; }
    public string? AdminUserName { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
}

public enum BulkDocumentAction
{
    Approve = 0,
    Reject = 1,
    Delete = 2,
    Archive = 3
}

public class BulkDocumentActionResult
{
    public int TotalRequested { get; set; }
    public int SuccessCount { get; set; }
    public int FailureCount { get; set; }
    public List<BulkDocumentError> Errors { get; set; } = new();
    public DateTime ProcessedAt { get; set; }
}

public class BulkDocumentError
{
    public Guid DocumentId { get; set; }
    public string Error { get; set; } = string.Empty;
}
