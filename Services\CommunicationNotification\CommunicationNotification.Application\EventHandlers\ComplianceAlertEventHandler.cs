using Shared.Domain.Common;
using Microsoft.Extensions.Logging;
using Shared.Domain.Common;
using Shared.Messaging;
using Shared.Domain.Common;
using CommunicationNotification.Application.Commands.Alerts;
using Shared.Domain.Common;
using CommunicationNotification.Application.Commands.SendNotification;
using Shared.Domain.Common;

namespace CommunicationNotification.Application.EventHandlers;

public class ComplianceAlertEventHandler :
    INotificationHandler<DocumentExpiryDetectedEvent>,
    INotificationHandler<SubscriptionExpiryDetectedEvent>,
    INotificationHandler<KycComplianceViolationEvent>,
    INotificationHandler<FleetComplianceViolationEvent>
{
    private readonly IMediator _mediator;
    private readonly ILogger<ComplianceAlertEventHandler> _logger;

    public ComplianceAlertEventHandler(
        IMediator mediator,
        ILogger<ComplianceAlertEventHandler> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    public async Task Handle(DocumentExpiryDetectedEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing document expiry alert for document {DocumentId}", notification.DocumentId);

        try
        {
            var alertCommand = new SendDocumentExpiryAlertCommand
            {
                DocumentId = notification.DocumentId,
                DocumentType = notification.DocumentType,
                EntityId = notification.EntityId,
                EntityType = notification.EntityType,
                ExpiryDate = notification.ExpiryDate,
                DaysUntilExpiry = notification.DaysUntilExpiry,
                AlertLevel = GetAlertLevel(notification.DaysUntilExpiry),
                Recipients = await GetRecipientsForDocumentAlert(notification),
                Channels = GetChannelsForAlertLevel(GetAlertLevel(notification.DaysUntilExpiry)),
                CustomMessage = GenerateDocumentExpiryMessage(notification),
                Tags = new List<string> { "compliance", "document-expiry", notification.DocumentType.ToLower() }
            };

            await _mediator.Send(alertCommand, cancellationToken);

            // Schedule follow-up alerts if needed
            await ScheduleFollowUpAlerts(notification, cancellationToken);

            _logger.LogInformation("Document expiry alert processed successfully for document {DocumentId}", notification.DocumentId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing document expiry alert for document {DocumentId}", notification.DocumentId);
        }
    }

    public async Task Handle(SubscriptionExpiryDetectedEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing subscription expiry alert for subscription {SubscriptionId}", notification.SubscriptionId);

        try
        {
            var notificationCommand = new SendNotificationCommand
            {
                MessageType = "SubscriptionExpiry",
                Subject = GenerateSubscriptionExpirySubject(notification),
                Content = GenerateSubscriptionExpiryContent(notification),
                Priority = GetPriorityForDays(notification.DaysUntilExpiry),
                Recipients = new[]
                {
                    new NotificationRecipient
                    {
                        UserId = notification.UserId,
                        UserType = "User",
                        PreferredChannels = GetChannelsForAlertLevel(GetAlertLevel(notification.DaysUntilExpiry))
                    }
                },
                Channels = GetChannelsForAlertLevel(GetAlertLevel(notification.DaysUntilExpiry)),
                Tags = new[] { "subscription", "expiry", "billing" },
                Metadata = new Dictionary<string, object>
                {
                    ["SubscriptionId"] = notification.SubscriptionId,
                    ["UserId"] = notification.UserId,
                    ["ExpiryDate"] = notification.ExpiryDate,
                    ["DaysUntilExpiry"] = notification.DaysUntilExpiry,
                    ["SubscriptionPlan"] = notification.SubscriptionPlan
                }
            };

            await _mediator.Send(notificationCommand, cancellationToken);

            _logger.LogInformation("Subscription expiry alert processed successfully for subscription {SubscriptionId}", notification.SubscriptionId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing subscription expiry alert for subscription {SubscriptionId}", notification.SubscriptionId);
        }
    }

    public async Task Handle(KycComplianceViolationEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing KYC compliance violation for user {UserId}", notification.UserId);

        try
        {
            var notificationCommand = new SendNotificationCommand
            {
                MessageType = "KycComplianceViolation",
                Subject = "KYC Compliance Action Required",
                Content = GenerateKycViolationContent(notification),
                Priority = "High",
                Recipients = new[]
                {
                    new NotificationRecipient
                    {
                        UserId = notification.UserId,
                        UserType = "User",
                        PreferredChannels = new[] { "Email", "SMS", "Push" }
                    }
                },
                Channels = new[] { "Email", "SMS", "Push" },
                Tags = new[] { "kyc", "compliance", "violation" },
                Metadata = new Dictionary<string, object>
                {
                    ["UserId"] = notification.UserId,
                    ["ViolationType"] = notification.ViolationType,
                    ["RequiredActions"] = notification.RequiredActions,
                    ["Deadline"] = notification.Deadline
                }
            };

            await _mediator.Send(notificationCommand, cancellationToken);

            // Send to compliance team
            await SendComplianceTeamAlert("KYC Violation", notification, cancellationToken);

            _logger.LogInformation("KYC compliance violation alert processed successfully for user {UserId}", notification.UserId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing KYC compliance violation for user {UserId}", notification.UserId);
        }
    }

    public async Task Handle(FleetComplianceViolationEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing fleet compliance violation for entity {EntityId}", notification.EntityId);

        try
        {
            var notificationCommand = new SendNotificationCommand
            {
                MessageType = "FleetComplianceViolation",
                Subject = GenerateFleetViolationSubject(notification),
                Content = GenerateFleetViolationContent(notification),
                Priority = GetPriorityForViolationType(notification.ViolationType),
                Recipients = await GetFleetComplianceRecipients(notification),
                Channels = GetChannelsForViolationType(notification.ViolationType),
                Tags = new[] { "fleet", "compliance", "violation", notification.EntityType.ToLower() },
                Metadata = new Dictionary<string, object>
                {
                    ["EntityId"] = notification.EntityId,
                    ["EntityType"] = notification.EntityType,
                    ["ViolationType"] = notification.ViolationType,
                    ["Severity"] = notification.Severity,
                    ["RequiredActions"] = notification.RequiredActions
                }
            };

            await _mediator.Send(notificationCommand, cancellationToken);

            // Send to fleet management team
            await SendFleetManagementAlert(notification, cancellationToken);

            _logger.LogInformation("Fleet compliance violation alert processed successfully for entity {EntityId}", notification.EntityId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing fleet compliance violation for entity {EntityId}", notification.EntityId);
        }
    }

    private string GetAlertLevel(int daysUntilExpiry)
    {
        return daysUntilExpiry switch
        {
            <= 0 => "Critical",
            <= 1 => "High",
            <= 7 => "Medium",
            <= 15 => "Low",
            _ => "Info"
        };
    }

    private string[] GetChannelsForAlertLevel(string alertLevel)
    {
        return alertLevel switch
        {
            "Critical" => new[] { "Email", "SMS", "Push", "WhatsApp" },
            "High" => new[] { "Email", "SMS", "Push" },
            "Medium" => new[] { "Email", "Push" },
            "Low" => new[] { "Email" },
            _ => new[] { "Email" }
        };
    }

    private string GetPriorityForDays(int days)
    {
        return days switch
        {
            <= 0 => "Critical",
            <= 3 => "High",
            <= 7 => "Medium",
            _ => "Low"
        };
    }

    private async Task<NotificationRecipient[]> GetRecipientsForDocumentAlert(DocumentExpiryDetectedEvent notification)
    {
        // This would typically query the database for relevant recipients
        return new[]
        {
            new NotificationRecipient
            {
                UserId = notification.EntityId,
                UserType = notification.EntityType,
                PreferredChannels = GetChannelsForAlertLevel(GetAlertLevel(notification.DaysUntilExpiry))
            }
        };
    }

    private string GenerateDocumentExpiryMessage(DocumentExpiryDetectedEvent notification)
    {
        var urgency = notification.DaysUntilExpiry <= 0 ? "has expired" : $"expires in {notification.DaysUntilExpiry} day(s)";
        return $"Your {notification.DocumentType} {urgency}. Please renew it immediately to avoid service disruption.";
    }

    private string GenerateSubscriptionExpirySubject(SubscriptionExpiryDetectedEvent notification)
    {
        return notification.DaysUntilExpiry <= 0
            ? "Subscription Expired - Immediate Action Required"
            : $"Subscription Expires in {notification.DaysUntilExpiry} Days";
    }

    private string GenerateSubscriptionExpiryContent(SubscriptionExpiryDetectedEvent notification)
    {
        var status = notification.DaysUntilExpiry <= 0 ? "has expired" : $"expires in {notification.DaysUntilExpiry} day(s)";
        return $"Your {notification.SubscriptionPlan} subscription {status}. Please renew to continue using our services.";
    }

    private string GenerateKycViolationContent(KycComplianceViolationEvent notification)
    {
        return $"KYC compliance issue detected: {notification.ViolationType}. " +
               $"Required actions: {string.Join(", ", notification.RequiredActions)}. " +
               $"Please complete by {notification.Deadline:yyyy-MM-dd}.";
    }

    private string GenerateFleetViolationSubject(FleetComplianceViolationEvent notification)
    {
        return $"Fleet Compliance Alert: {notification.ViolationType} - {notification.EntityType}";
    }

    private string GenerateFleetViolationContent(FleetComplianceViolationEvent notification)
    {
        return $"Compliance violation detected for {notification.EntityType}: {notification.ViolationType}. " +
               $"Severity: {notification.Severity}. " +
               $"Required actions: {string.Join(", ", notification.RequiredActions)}.";
    }

    private async Task ScheduleFollowUpAlerts(DocumentExpiryDetectedEvent notification, CancellationToken cancellationToken)
    {
        // Schedule follow-up alerts based on document type and expiry timeline
        if (notification.DaysUntilExpiry > 0)
        {
            var followUpDays = notification.DaysUntilExpiry switch
            {
                > 15 => new[] { 7, 3, 1 },
                > 7 => new[] { 3, 1 },
                > 3 => new[] { 1 },
                _ => Array.Empty<int>()
            };

            foreach (var days in followUpDays)
            {
                // This would integrate with a job scheduler
                _logger.LogDebug("Scheduling follow-up alert for document {DocumentId} in {Days} days",
                    notification.DocumentId, days);
            }
        }
    }

    private async Task SendComplianceTeamAlert(string alertType, KycComplianceViolationEvent notification, CancellationToken cancellationToken)
    {
        // Send alert to compliance team
        _logger.LogDebug("Sending {AlertType} alert to compliance team for user {UserId}", alertType, notification.UserId);
    }

    private async Task SendFleetManagementAlert(FleetComplianceViolationEvent notification, CancellationToken cancellationToken)
    {
        // Send alert to fleet management team
        _logger.LogDebug("Sending fleet compliance alert to management team for {EntityType} {EntityId}",
            notification.EntityType, notification.EntityId);
    }

    private async Task<NotificationRecipient[]> GetFleetComplianceRecipients(FleetComplianceViolationEvent notification)
    {
        // This would query for relevant fleet managers and owners
        return new[]
        {
            new NotificationRecipient
            {
                UserId = notification.EntityId,
                UserType = "FleetOwner",
                PreferredChannels = GetChannelsForViolationType(notification.ViolationType)
            }
        };
    }

    private string GetPriorityForViolationType(string violationType)
    {
        return violationType.ToLower() switch
        {
            "expired_insurance" => "Critical",
            "expired_license" => "Critical",
            "expired_fitness" => "High",
            "missing_documents" => "Medium",
            _ => "Low"
        };
    }

    private string[] GetChannelsForViolationType(string violationType)
    {
        return violationType.ToLower() switch
        {
            "expired_insurance" => new[] { "Email", "SMS", "Push", "WhatsApp" },
            "expired_license" => new[] { "Email", "SMS", "Push" },
            _ => new[] { "Email", "Push" }
        };
    }
}

// Event classes
public record DocumentExpiryDetectedEvent(
    Guid DocumentId,
    string DocumentType,
    Guid EntityId,
    string EntityType,
    DateTime ExpiryDate,
    int DaysUntilExpiry) : INotification;

public record SubscriptionExpiryDetectedEvent(
    Guid SubscriptionId,
    Guid UserId,
    string SubscriptionPlan,
    DateTime ExpiryDate,
    int DaysUntilExpiry) : INotification;

public record KycComplianceViolationEvent(
    Guid UserId,
    string ViolationType,
    List<string> RequiredActions,
    DateTime Deadline) : INotification;

public record FleetComplianceViolationEvent(
    Guid EntityId,
    string EntityType,
    string ViolationType,
    string Severity,
    List<string> RequiredActions) : INotification;

