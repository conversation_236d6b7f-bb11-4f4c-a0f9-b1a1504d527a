using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Domain.Enums;
using MediatR;

namespace AnalyticsBIService.Application.Queries.Carrier;

/// <summary>
/// Query to get carrier performance dashboard
/// </summary>
public record GetCarrierDashboardQuery(
    Guid CarrierId,
    DateTime? FromDate = null,
    DateTime? ToDate = null,
    TimePeriod Period = TimePeriod.Daily
) : IRequest<CarrierDashboardDto>;

/// <summary>
/// Query to get carrier performance analytics
/// </summary>
public record GetCarrierPerformanceAnalyticsQuery(
    Guid CarrierId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    bool IncludeDeliveryMetrics = true,
    bool IncludeBrokerFeedback = true,
    bool IncludeImprovementAreas = true
) : IRequest<CarrierPerformanceAnalyticsDto>;

/// <summary>
/// Query to get carrier delivery performance
/// </summary>
public record GetCarrierDeliveryPerformanceQuery(
    Guid CarrierId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    string? RouteType = null,
    bool IncludeCustomerFeedback = true
) : IRequest<CarrierDeliveryPerformanceDto>;

/// <summary>
/// Query to get carrier performance metrics
/// </summary>
public record GetCarrierPerformanceMetricsQuery(
    Guid CarrierId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    bool IncludeDeliveryMetrics = true,
    bool IncludeEfficiencyMetrics = true,
    bool IncludeCustomerSatisfaction = true
) : IRequest<CarrierPerformanceMetricsDto>;

/// <summary>
/// Query to get carrier delivery completion analytics
/// </summary>
public record GetCarrierDeliveryCompletionAnalyticsQuery(
    Guid CarrierId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    string? RouteType = null,
    string? ServiceLevel = null
) : IRequest<CarrierDeliveryCompletionAnalyticsDto>;

/// <summary>
/// Query to get carrier efficiency tracking
/// </summary>
public record GetCarrierEfficiencyTrackingQuery(
    Guid CarrierId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    bool IncludeOnTimeDelivery = true,
    bool IncludeServiceQuality = true
) : IRequest<CarrierEfficiencyTrackingDto>;

/// <summary>
/// Query to get carrier earnings analytics
/// </summary>
public record GetCarrierEarningsAnalyticsQuery(
    Guid CarrierId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    bool IncludeIncomeTrends = true,
    bool IncludePerformanceBenchmarking = true
) : IRequest<CarrierEarningsAnalyticsDto>;

/// <summary>
/// Query to get carrier growth opportunities
/// </summary>
public record GetCarrierGrowthOpportunitiesQuery(
    Guid CarrierId,
    DateTime FromDate,
    DateTime ToDate,
    bool IncludeServiceImprovement = true,
    bool IncludeIncomeOptimization = true,
    bool IncludeMarketExpansion = true
) : IRequest<CarrierGrowthOpportunitiesDto>;

/// <summary>
/// Query to get broker feedback analytics for carrier
/// </summary>
public record GetCarrierBrokerFeedbackQuery(
    Guid CarrierId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    Guid? BrokerId = null,
    bool IncludeServiceQuality = true
) : IRequest<CarrierBrokerFeedbackDto>;

/// <summary>
/// Query to get carrier service quality monitoring
/// </summary>
public record GetCarrierServiceQualityMonitoringQuery(
    Guid CarrierId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    bool IncludeQualityScores = true,
    bool IncludeImprovementAreas = true
) : IRequest<CarrierServiceQualityMonitoringDto>;

/// <summary>
/// Query to get carrier route performance analytics
/// </summary>
public record GetCarrierRoutePerformanceQuery(
    Guid CarrierId,
    DateTime FromDate,
    DateTime ToDate,
    string? OriginCity = null,
    string? DestinationCity = null,
    bool IncludeEfficiencyMetrics = true
) : IRequest<CarrierRoutePerformanceDto>;

/// <summary>
/// Query to get carrier vehicle utilization analytics
/// </summary>
public record GetCarrierVehicleUtilizationQuery(
    Guid CarrierId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    string? VehicleType = null,
    bool IncludeMaintenanceMetrics = true
) : IRequest<CarrierVehicleUtilizationDto>;

/// <summary>
/// Query to get carrier financial performance
/// </summary>
public record GetCarrierFinancialPerformanceQuery(
    Guid CarrierId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Monthly,
    bool IncludeEarningsBreakdown = true,
    bool IncludeCostAnalysis = true,
    bool IncludeProfitabilityAnalysis = true
) : IRequest<CarrierFinancialPerformanceDto>;

/// <summary>
/// Query to get carrier customer satisfaction analytics
/// </summary>
public record GetCarrierCustomerSatisfactionQuery(
    Guid CarrierId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    bool IncludeFeedbackAnalysis = true,
    bool IncludeRatingTrends = true
) : IRequest<CarrierCustomerSatisfactionDto>;

/// <summary>
/// Query to get carrier competitive benchmarking
/// </summary>
public record GetCarrierCompetitiveBenchmarkingQuery(
    Guid CarrierId,
    DateTime FromDate,
    DateTime ToDate,
    string? CarrierType = null,
    string? GeographicRegion = null,
    bool IncludePerformanceComparison = true
) : IRequest<CarrierCompetitiveBenchmarkingDto>;

/// <summary>
/// Query to get carrier capacity management analytics
/// </summary>
public record GetCarrierCapacityManagementQuery(
    Guid CarrierId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    bool IncludeUtilizationMetrics = true,
    bool IncludeCapacityOptimization = true
) : IRequest<CarrierCapacityManagementDto>;

/// <summary>
/// Query to get carrier compliance analytics
/// </summary>
public record GetCarrierComplianceAnalyticsQuery(
    Guid CarrierId,
    DateTime FromDate,
    DateTime ToDate,
    string? ComplianceType = null,
    string? Region = null,
    bool IncludeViolations = true
) : IRequest<CarrierComplianceAnalyticsDto>;

/// <summary>
/// Query to get carrier technology adoption analytics
/// </summary>
public record GetCarrierTechnologyAdoptionQuery(
    Guid CarrierId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    bool IncludeAppUsage = true,
    bool IncludeFeatureAdoption = true
) : IRequest<CarrierTechnologyAdoptionDto>;

/// <summary>
/// Query to get carrier safety analytics
/// </summary>
public record GetCarrierSafetyAnalyticsQuery(
    Guid CarrierId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    bool IncludeIncidentAnalysis = true,
    bool IncludeSafetyScores = true
) : IRequest<CarrierSafetyAnalyticsDto>;

/// <summary>
/// Query to get carrier market opportunities
/// </summary>
public record GetCarrierMarketOpportunitiesQuery(
    Guid CarrierId,
    DateTime FromDate,
    DateTime ToDate,
    string? MarketSegment = null,
    string? GeographicRegion = null,
    bool IncludeGrowthPotential = true
) : IRequest<CarrierMarketOpportunitiesDto>;

/// <summary>
/// Query to get carrier operational insights
/// </summary>
public record GetCarrierOperationalInsightsQuery(
    Guid CarrierId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    bool IncludeEfficiencyInsights = true,
    bool IncludeOptimizationRecommendations = true
) : IRequest<CarrierOperationalInsightsDto>;

/// <summary>
/// Query to get carrier partnership analytics
/// </summary>
public record GetCarrierPartnershipAnalyticsQuery(
    Guid CarrierId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    bool IncludeBrokerRelationships = true,
    bool IncludePartnershipValue = true
) : IRequest<CarrierPartnershipAnalyticsDto>;
