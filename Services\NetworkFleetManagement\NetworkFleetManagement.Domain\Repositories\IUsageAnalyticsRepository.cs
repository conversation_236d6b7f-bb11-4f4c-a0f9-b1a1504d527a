using NetworkFleetManagement.Domain.Entities;

namespace NetworkFleetManagement.Domain.Repositories;

public interface IUsageAnalyticsRepository
{
    // Basic CRUD operations
    Task<UsageAnalyticsRecord> CreateAsync(UsageAnalyticsRecord record);
    Task<List<UsageAnalyticsRecord>> CreateBatchAsync(List<UsageAnalyticsRecord> records);
    Task<UsageAnalyticsRecord?> GetByIdAsync(Guid id);
    Task<List<UsageAnalyticsRecord>> GetByEntityAsync(Guid entityId, string entityType, DateTime? from = null, DateTime? to = null);
    Task<List<UsageAnalyticsRecord>> GetByEventTypeAsync(string eventType, DateTime? from = null, DateTime? to = null);
    Task<List<UsageAnalyticsRecord>> GetByCategoryAsync(string category, DateTime? from = null, DateTime? to = null);

    // Analytics queries
    Task<Dictionary<string, object>> GetFleetUtilizationMetricsAsync(Guid carrierId, DateTime from, DateTime to);
    Task<Dictionary<string, object>> GetVehiclePerformanceMetricsAsync(Guid vehicleId, DateTime from, DateTime to);
    Task<Dictionary<string, object>> GetDriverPerformanceMetricsAsync(Guid driverId, DateTime from, DateTime to);
    Task<Dictionary<string, object>> GetNetworkPerformanceMetricsAsync(Guid networkId, DateTime from, DateTime to);

    // Aggregation queries
    Task<List<Dictionary<string, object>>> GetTopPerformingEntitiesAsync(string entityType, string metricType, int count, DateTime? from = null, DateTime? to = null);
    Task<List<Dictionary<string, object>>> GetUnderperformingEntitiesAsync(string entityType, string metricType, double threshold, DateTime? from = null, DateTime? to = null);
    Task<Dictionary<string, object>> GetAggregatedMetricsAsync(string metricType, DateTime from, DateTime to, string aggregationType = "daily");

    // Time-series data
    Task<List<Dictionary<string, object>>> GetTimeSeriesDataAsync(string metricType, Guid? entityId, string? entityType, DateTime from, DateTime to, string interval = "hour");
    Task<Dictionary<string, object>> GetTrendAnalysisAsync(string metricType, Guid? entityId, string? entityType, DateTime from, DateTime to);

    // Real-time queries
    Task<List<Dictionary<string, object>>> GetRecentEventsAsync(string? category = null, int count = 100);
    Task<Dictionary<string, object>> GetRealTimeMetricsAsync(string metricType, Guid? entityId = null, string? entityType = null);
    Task<List<Dictionary<string, object>>> GetActiveAlertsAsync(Guid? entityId = null, string? entityType = null);

    // Maintenance and cleanup
    Task<int> ArchiveOldRecordsAsync(DateTime olderThan);
    Task<int> DeleteProcessedRecordsAsync(DateTime olderThan);
    Task<bool> OptimizeIndexesAsync();

    // Aggregation management
    Task<AnalyticsAggregation> CreateAggregationAsync(AnalyticsAggregation aggregation);
    Task<AnalyticsAggregation?> GetAggregationAsync(string metricType, string aggregationType, DateTime periodStart, DateTime periodEnd, Guid? entityId = null, string? entityType = null);
    Task<List<AnalyticsAggregation>> GetAggregationsAsync(string metricType, string aggregationType, DateTime from, DateTime to, Guid? entityId = null, string? entityType = null);
    Task UpdateAggregationAsync(AnalyticsAggregation aggregation);
}
