using AutoMapper;
using MediatR;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Repositories;
using NetworkFleetManagement.Domain.ValueObjects;
using Shared.Messaging;

namespace NetworkFleetManagement.Application.Commands.Vehicles;

public class UpdateVehicleLocationCommandHandler : IRequestHandler<UpdateVehicleLocationCommand, VehicleDto>
{
    private readonly IVehicleRepository _vehicleRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly IMessageBroker _messageBroker;

    public UpdateVehicleLocationCommandHandler(
        IVehicleRepository vehicleRepository,
        IUnitOfWork unitOfWork,
        IMapper mapper,
        IMessageBroker messageBroker)
    {
        _vehicleRepository = vehicleRepository;
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _messageBroker = messageBroker;
    }

    public async Task<VehicleDto> Handle(UpdateVehicleLocationCommand request, CancellationToken cancellationToken)
    {
        var vehicle = await _vehicleRepository.GetByIdAsync(request.VehicleId, cancellationToken);
        if (vehicle == null)
        {
            throw new InvalidOperationException($"Vehicle with ID {request.VehicleId} not found");
        }

        var locationDto = request.LocationDto.Location;
        var location = new Location(
            locationDto.Latitude,
            locationDto.Longitude,
            locationDto.Address,
            locationDto.City,
            locationDto.State,
            locationDto.Country,
            locationDto.PostalCode,
            locationDto.Timestamp);

        vehicle.UpdateLocation(location);

        _vehicleRepository.Update(vehicle);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        // Publish integration event
        await _messageBroker.PublishAsync("vehicle.location_updated", new
        {
            VehicleId = vehicle.Id,
            CarrierId = vehicle.CarrierId,
            RegistrationNumber = vehicle.RegistrationNumber,
            Location = new
            {
                locationDto.Latitude,
                locationDto.Longitude,
                locationDto.Address,
                locationDto.City,
                locationDto.State,
                locationDto.Country,
                locationDto.PostalCode,
                Timestamp = location.Timestamp
            },
            UpdatedAt = vehicle.UpdatedAt
        }, cancellationToken);

        var result = _mapper.Map<VehicleDto>(vehicle);
        return result;
    }
}
