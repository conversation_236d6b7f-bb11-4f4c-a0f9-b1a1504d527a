using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SubscriptionManagement.Domain.Entities;

namespace SubscriptionManagement.Infrastructure.Configurations
{
    public class PlanFeatureConfiguration : IEntityTypeConfiguration<PlanFeature>
    {
        public void Configure(EntityTypeBuilder<PlanFeature> builder)
        {
            builder.ToTable("PlanFeatures");

            builder.HasKey(pf => pf.Id);

            builder.Property(pf => pf.FeatureType)
                .IsRequired()
                .HasConversion<string>();

            builder.Property(pf => pf.AccessType)
                .IsRequired()
                .HasConversion<string>();

            builder.Property(pf => pf.LimitValue);

            builder.Property(pf => pf.Description)
                .HasMaxLength(500);

            builder.Property(pf => pf.IsEnabled)
                .IsRequired();

            builder.Property(pf => pf.CreatedAt)
                .IsRequired();

            builder.Property(pf => pf.UpdatedAt);

            // Indexes
            builder.HasIndex("PlanId", nameof(PlanFeature.FeatureType))
                .IsUnique();
        }
    }
}
