-- Financial & Payment Service Database Setup
-- This script sets up the database for the Financial & Payment Service

-- Create database if it doesn't exist
SELECT 'CREATE DATABASE tli_financial_payment'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'tli_financial_payment')\gexec

-- Connect to the database
\c tli_financial_payment;

-- Create TimescaleDB extension
CREATE EXTENSION IF NOT EXISTS timescaledb;

-- Create schema
CREATE SCHEMA IF NOT EXISTS financial_payment;

-- Set search path
SET search_path TO financial_payment, public;

-- Create enum types
CREATE TYPE escrow_status AS ENUM (
    'Created',
    'PartiallyFunded', 
    'Funded',
    'PartiallyReleased',
    'Released',
    'Refunded',
    'Cancelled'
);

CREATE TYPE escrow_transaction_type AS ENUM (
    'Fund',
    'Reserve',
    'Release',
    'Refund',
    'Adjustment'
);

CREATE TYPE escrow_transaction_status AS ENUM (
    'Pending',
    'Processing',
    'Completed',
    'Failed',
    'Cancelled'
);

CREATE TYPE escrow_milestone_status AS ENUM (
    'Pending',
    'Completed',
    'Cancelled',
    'Overdue'
);

CREATE TYPE settlement_status AS ENUM (
    'Created',
    'Processing',
    'Completed',
    'Failed',
    'Cancelled'
);

CREATE TYPE distribution_type AS ENUM (
    'CarrierPayment',
    'BrokerCommission',
    'PlatformFee',
    'TaxWithholding',
    'Refund',
    'Adjustment'
);

CREATE TYPE distribution_status AS ENUM (
    'Pending',
    'Processing',
    'Completed',
    'Failed',
    'Cancelled'
);

CREATE TYPE participant_role AS ENUM (
    'TransportCompany',
    'Broker',
    'Carrier',
    'Shipper',
    'Admin',
    'Platform'
);

CREATE TYPE commission_status AS ENUM (
    'Calculated',
    'Approved',
    'Disputed',
    'Paid',
    'Cancelled'
);

CREATE TYPE commission_type AS ENUM (
    'Percentage',
    'FixedAmount',
    'Tiered'
);

CREATE TYPE commission_adjustment_type AS ENUM (
    'Increase',
    'Decrease',
    'Bonus',
    'Penalty'
);

CREATE TYPE dispute_status AS ENUM (
    'Open',
    'InProgress',
    'Escalated',
    'Resolved',
    'Closed'
);

CREATE TYPE dispute_category AS ENUM (
    'PaymentDelay',
    'IncorrectAmount',
    'ServiceQuality',
    'DamageOrLoss',
    'ContractBreach',
    'CommissionDispute',
    'RefundRequest',
    'Other'
);

CREATE TYPE dispute_priority AS ENUM (
    'Low',
    'Medium',
    'High',
    'Critical'
);

-- Grant permissions to timescale user
GRANT USAGE ON SCHEMA financial_payment TO timescale;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA financial_payment TO timescale;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA financial_payment TO timescale;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA financial_payment TO timescale;

-- Grant permissions on types
GRANT USAGE ON TYPE escrow_status TO timescale;
GRANT USAGE ON TYPE escrow_transaction_type TO timescale;
GRANT USAGE ON TYPE escrow_transaction_status TO timescale;
GRANT USAGE ON TYPE escrow_milestone_status TO timescale;
GRANT USAGE ON TYPE settlement_status TO timescale;
GRANT USAGE ON TYPE distribution_type TO timescale;
GRANT USAGE ON TYPE distribution_status TO timescale;
GRANT USAGE ON TYPE participant_role TO timescale;
GRANT USAGE ON TYPE commission_status TO timescale;
GRANT USAGE ON TYPE commission_type TO timescale;
GRANT USAGE ON TYPE commission_adjustment_type TO timescale;
GRANT USAGE ON TYPE dispute_status TO timescale;
GRANT USAGE ON TYPE dispute_category TO timescale;
GRANT USAGE ON TYPE dispute_priority TO timescale;

-- Create indexes for better performance
-- Note: Actual table creation will be handled by Entity Framework migrations

COMMENT ON SCHEMA financial_payment IS 'Schema for Financial & Payment Service - handles escrow accounts, settlements, commissions, and payment disputes';

-- Display success message
SELECT 'Financial & Payment Service database setup completed successfully!' as status;
