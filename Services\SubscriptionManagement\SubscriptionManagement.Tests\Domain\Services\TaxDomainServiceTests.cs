using FluentAssertions;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Exceptions;
using SubscriptionManagement.Domain.Services;
using SubscriptionManagement.Domain.ValueObjects;
using Xunit;

namespace SubscriptionManagement.Tests.Domain.Services
{
    public class TaxDomainServiceTests
    {
        [Fact]
        public void ValidateTaxConfiguration_WithValidConfiguration_ShouldNotThrow()
        {
            // Arrange
            var taxConfig = TaxConfiguration.Create(
                TaxType.GST, 18.0m, false, new List<string> { "IN" }, DateTime.UtcNow);

            // Act & Assert
            var exception = Record.Exception(() => 
                TaxDomainService.ValidateTaxConfiguration(taxConfig, "IN"));
            
            exception.Should().BeNull();
        }

        [Fact]
        public void ValidateTaxConfiguration_WithNullConfiguration_ShouldThrowException()
        {
            // Act & Assert
            Assert.Throws<SubscriptionDomainException>(() =>
                TaxDomainService.ValidateTaxConfiguration(null!, "IN"));
        }

        [Fact]
        public void ValidateTaxConfiguration_WithEmptyRegion_ShouldThrowException()
        {
            // Arrange
            var taxConfig = TaxConfiguration.Create(
                TaxType.GST, 18.0m, false, new List<string> { "IN" }, DateTime.UtcNow);

            // Act & Assert
            Assert.Throws<SubscriptionDomainException>(() =>
                TaxDomainService.ValidateTaxConfiguration(taxConfig, ""));
        }

        [Fact]
        public void ValidateTaxConfiguration_WithNonApplicableRegion_ShouldThrowException()
        {
            // Arrange
            var taxConfig = TaxConfiguration.Create(
                TaxType.GST, 18.0m, false, new List<string> { "IN" }, DateTime.UtcNow);

            // Act & Assert
            Assert.Throws<SubscriptionDomainException>(() =>
                TaxDomainService.ValidateTaxConfiguration(taxConfig, "US"));
        }

        [Fact]
        public void ValidateTaxConfiguration_WithInactiveConfiguration_ShouldThrowException()
        {
            // Arrange
            var futureDate = DateTime.UtcNow.AddDays(1);
            var taxConfig = TaxConfiguration.Create(
                TaxType.GST, 18.0m, false, new List<string> { "IN" }, futureDate);

            // Act & Assert
            Assert.Throws<SubscriptionDomainException>(() =>
                TaxDomainService.ValidateTaxConfiguration(taxConfig, "IN"));
        }

        [Fact]
        public void ValidateTaxConfiguration_WithGSTRateAbove28InIndia_ShouldThrowException()
        {
            // Arrange
            var taxConfig = TaxConfiguration.Create(
                TaxType.GST, 30.0m, false, new List<string> { "IN" }, DateTime.UtcNow);

            // Act & Assert
            Assert.Throws<SubscriptionDomainException>(() =>
                TaxDomainService.ValidateTaxConfiguration(taxConfig, "IN"));
        }

        [Fact]
        public void ValidateTaxConfiguration_WithTDSRateAbove30_ShouldThrowException()
        {
            // Arrange
            var taxConfig = TaxConfiguration.Create(
                TaxType.TDS, 35.0m, false, new List<string> { "IN" }, DateTime.UtcNow);

            // Act & Assert
            Assert.Throws<SubscriptionDomainException>(() =>
                TaxDomainService.ValidateTaxConfiguration(taxConfig, "IN"));
        }

        [Fact]
        public void CalculateCompoundTax_WithMultipleTaxes_ShouldCalculateCorrectly()
        {
            // Arrange
            var baseAmount = Money.Create(100m, "INR");
            var taxConfigurations = new List<TaxConfiguration>
            {
                TaxConfiguration.Create(TaxType.CGST, 9.0m, false, new List<string> { "IN" }, DateTime.UtcNow),
                TaxConfiguration.Create(TaxType.SGST, 9.0m, false, new List<string> { "IN" }, DateTime.UtcNow),
                TaxConfiguration.Create(TaxType.Cess, 1.0m, false, new List<string> { "IN" }, DateTime.UtcNow)
            };

            // Act
            var totalTax = TaxDomainService.CalculateCompoundTax(baseAmount, taxConfigurations, "IN");

            // Assert
            // CGST: 100 * 9% = 9
            // SGST: 100 * 9% = 9
            // Cess: (100 + 9 + 9) * 1% = 1.18 (compound tax)
            totalTax.Amount.Should().BeApproximately(19.18m, 0.01m);
        }

        [Fact]
        public void CalculateCompoundTax_WithNullBaseAmount_ShouldThrowException()
        {
            // Arrange
            var taxConfigurations = new List<TaxConfiguration>();

            // Act & Assert
            Assert.Throws<SubscriptionDomainException>(() =>
                TaxDomainService.CalculateCompoundTax(null!, taxConfigurations, "IN"));
        }

        [Fact]
        public void CalculateCompoundTax_WithEmptyTaxConfigurations_ShouldReturnZero()
        {
            // Arrange
            var baseAmount = Money.Create(100m, "INR");
            var taxConfigurations = new List<TaxConfiguration>();

            // Act
            var totalTax = TaxDomainService.CalculateCompoundTax(baseAmount, taxConfigurations, "IN");

            // Assert
            totalTax.Amount.Should().Be(0m);
            totalTax.Currency.Should().Be("INR");
        }

        [Theory]
        [InlineData(TaxType.CGST, 1)]
        [InlineData(TaxType.SGST, 2)]
        [InlineData(TaxType.IGST, 3)]
        [InlineData(TaxType.GST, 5)]
        [InlineData(TaxType.Cess, 6)]
        [InlineData(TaxType.TDS, 10)]
        public void GetTaxPriority_ShouldReturnCorrectPriority(TaxType taxType, int expectedPriority)
        {
            // Act
            var priority = TaxDomainService.GetTaxPriority(taxType);

            // Assert
            priority.Should().Be(expectedPriority);
        }

        [Theory]
        [InlineData(TaxType.Cess, true)]
        [InlineData(TaxType.GST, false)]
        [InlineData(TaxType.CGST, false)]
        [InlineData(TaxType.TDS, false)]
        public void ShouldCompoundTax_ShouldReturnCorrectResult(TaxType taxType, bool expected)
        {
            // Act
            var shouldCompound = TaxDomainService.ShouldCompoundTax(taxType);

            // Assert
            shouldCompound.Should().Be(expected);
        }

        [Fact]
        public void CalculateBaseAmountFromTaxInclusive_WithValidInputs_ShouldCalculateCorrectly()
        {
            // Arrange
            var taxInclusiveAmount = Money.Create(118m, "INR");
            var taxRate = 18.0m;

            // Act
            var baseAmount = TaxDomainService.CalculateBaseAmountFromTaxInclusive(taxInclusiveAmount, taxRate);

            // Assert
            baseAmount.Amount.Should().BeApproximately(100m, 0.01m);
            baseAmount.Currency.Should().Be("INR");
        }

        [Fact]
        public void CalculateBaseAmountFromTaxInclusive_WithZeroTaxRate_ShouldReturnSameAmount()
        {
            // Arrange
            var amount = Money.Create(100m, "INR");
            var taxRate = 0m;

            // Act
            var baseAmount = TaxDomainService.CalculateBaseAmountFromTaxInclusive(amount, taxRate);

            // Assert
            baseAmount.Amount.Should().Be(100m);
            baseAmount.Currency.Should().Be("INR");
        }

        [Fact]
        public void ValidateRegionTaxRules_WithValidIndianRules_ShouldNotThrow()
        {
            // Arrange
            var taxConfigurations = new List<TaxConfiguration>
            {
                TaxConfiguration.Create(TaxType.CGST, 9.0m, false, new List<string> { "IN" }, DateTime.UtcNow),
                TaxConfiguration.Create(TaxType.SGST, 9.0m, false, new List<string> { "IN" }, DateTime.UtcNow)
            };

            // Act & Assert
            var exception = Record.Exception(() =>
                TaxDomainService.ValidateRegionTaxRules("IN", taxConfigurations));
            
            exception.Should().BeNull();
        }

        [Fact]
        public void ValidateRegionTaxRules_WithBothIGSTAndCGSTSGST_ShouldThrowException()
        {
            // Arrange
            var taxConfigurations = new List<TaxConfiguration>
            {
                TaxConfiguration.Create(TaxType.IGST, 18.0m, false, new List<string> { "IN" }, DateTime.UtcNow),
                TaxConfiguration.Create(TaxType.CGST, 9.0m, false, new List<string> { "IN" }, DateTime.UtcNow),
                TaxConfiguration.Create(TaxType.SGST, 9.0m, false, new List<string> { "IN" }, DateTime.UtcNow)
            };

            // Act & Assert
            Assert.Throws<SubscriptionDomainException>(() =>
                TaxDomainService.ValidateRegionTaxRules("IN", taxConfigurations));
        }

        [Fact]
        public void ValidateRegionTaxRules_WithUnequalCGSTSGSTRates_ShouldThrowException()
        {
            // Arrange
            var taxConfigurations = new List<TaxConfiguration>
            {
                TaxConfiguration.Create(TaxType.CGST, 9.0m, false, new List<string> { "IN" }, DateTime.UtcNow),
                TaxConfiguration.Create(TaxType.SGST, 10.0m, false, new List<string> { "IN" }, DateTime.UtcNow)
            };

            // Act & Assert
            Assert.Throws<SubscriptionDomainException>(() =>
                TaxDomainService.ValidateRegionTaxRules("IN", taxConfigurations));
        }

        [Fact]
        public void ValidateRegionTaxRules_WithTotalGSTRateAbove28_ShouldThrowException()
        {
            // Arrange
            var taxConfigurations = new List<TaxConfiguration>
            {
                TaxConfiguration.Create(TaxType.CGST, 15.0m, false, new List<string> { "IN" }, DateTime.UtcNow),
                TaxConfiguration.Create(TaxType.SGST, 15.0m, false, new List<string> { "IN" }, DateTime.UtcNow)
            };

            // Act & Assert
            Assert.Throws<SubscriptionDomainException>(() =>
                TaxDomainService.ValidateRegionTaxRules("IN", taxConfigurations));
        }

        [Theory]
        [InlineData("IN", true)]
        [InlineData("US", false)]
        [InlineData("GB", false)]
        public void ShouldDisplayTaxSeparately_ShouldReturnCorrectResult(string region, bool expected)
        {
            // Arrange
            var taxConfigurations = new List<TaxConfiguration>
            {
                TaxConfiguration.Create(TaxType.GST, 18.0m, false, new List<string> { region }, DateTime.UtcNow)
            };

            // Act
            var result = TaxDomainService.ShouldDisplayTaxSeparately(region, taxConfigurations);

            // Assert
            result.Should().Be(expected);
        }

        [Fact]
        public void CalculateEffectiveTaxRate_WithMultipleTaxes_ShouldReturnSum()
        {
            // Arrange
            var taxConfigurations = new List<TaxConfiguration>
            {
                TaxConfiguration.Create(TaxType.CGST, 9.0m, false, new List<string> { "IN" }, DateTime.UtcNow),
                TaxConfiguration.Create(TaxType.SGST, 9.0m, false, new List<string> { "IN" }, DateTime.UtcNow)
            };

            // Act
            var effectiveRate = TaxDomainService.CalculateEffectiveTaxRate(taxConfigurations, "IN");

            // Assert
            effectiveRate.Should().Be(18.0m);
        }

        [Fact]
        public void CalculateEffectiveTaxRate_WithNonApplicableRegion_ShouldReturnZero()
        {
            // Arrange
            var taxConfigurations = new List<TaxConfiguration>
            {
                TaxConfiguration.Create(TaxType.GST, 18.0m, false, new List<string> { "IN" }, DateTime.UtcNow)
            };

            // Act
            var effectiveRate = TaxDomainService.CalculateEffectiveTaxRate(taxConfigurations, "US");

            // Assert
            effectiveRate.Should().Be(0m);
        }
    }
}
