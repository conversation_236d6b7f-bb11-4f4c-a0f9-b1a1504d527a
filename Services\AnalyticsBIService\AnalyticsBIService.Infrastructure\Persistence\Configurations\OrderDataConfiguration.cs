using AnalyticsBIService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace AnalyticsBIService.Infrastructure.Persistence.Configurations;

/// <summary>
/// Entity configuration for OrderData
/// </summary>
public class OrderDataConfiguration : IEntityTypeConfiguration<OrderData>
{
    public void Configure(EntityTypeBuilder<OrderData> builder)
    {
        builder.ToTable("order_data");

        builder.HasKey(od => od.Id);

        builder.Property(od => od.OrderNumber)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(od => od.ShipperId)
            .IsRequired();

        builder.Property(od => od.CarrierId);

        builder.Property(od => od.BrokerId);

        builder.Property(od => od.Status)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(od => od.OrderDate)
            .IsRequired();

        builder.Property(od => od.PickupDate);

        builder.Property(od => od.DeliveryDate);

        builder.Property(od => od.OriginCity)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(od => od.DestinationCity)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(od => od.Distance)
            .HasPrecision(10, 2);

        builder.Property(od => od.Weight)
            .HasPrecision(10, 2);

        builder.Property(od => od.Volume)
            .HasPrecision(10, 2);

        builder.Property(od => od.OrderValue)
            .HasPrecision(18, 2);

        builder.Property(od => od.ShippingCost)
            .HasPrecision(18, 2);

        builder.Property(od => od.VehicleType)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(od => od.IsOnTime)
            .IsRequired();

        builder.Property(od => od.DelayHours)
            .IsRequired();

        builder.Property(od => od.CustomerRating)
            .HasPrecision(3, 2);

        // Indexes for analytics queries
        builder.HasIndex(od => od.OrderDate)
            .HasDatabaseName("IX_OrderData_OrderDate");

        builder.HasIndex(od => od.ShipperId)
            .HasDatabaseName("IX_OrderData_ShipperId");

        builder.HasIndex(od => od.CarrierId)
            .HasDatabaseName("IX_OrderData_CarrierId");

        builder.HasIndex(od => od.BrokerId)
            .HasDatabaseName("IX_OrderData_BrokerId");

        builder.HasIndex(od => od.Status)
            .HasDatabaseName("IX_OrderData_Status");

        builder.HasIndex(od => new { od.OriginCity, od.DestinationCity })
            .HasDatabaseName("IX_OrderData_Route");

        builder.HasIndex(od => new { od.OrderDate, od.Status })
            .HasDatabaseName("IX_OrderData_OrderDate_Status");

        builder.HasIndex(od => new { od.ShipperId, od.OrderDate })
            .HasDatabaseName("IX_OrderData_ShipperId_OrderDate");
    }
}
