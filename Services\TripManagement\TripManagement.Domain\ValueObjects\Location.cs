using System.ComponentModel.DataAnnotations;

namespace TripManagement.Domain.ValueObjects;

public record Location
{
    public double Latitude { get; init; }
    public double Longitude { get; init; }
    public double? Altitude { get; init; }
    public double? Accuracy { get; init; }
    public DateTime Timestamp { get; init; }
    public string? Address { get; init; }
    public string? City { get; init; }
    public string? State { get; init; }
    public string? Country { get; init; }
    public string? PostalCode { get; init; }

    // Parameterless constructor for EF Core
    public Location() { }

    public Location(
        double latitude,
        double longitude,
        DateTime? timestamp = null,
        double? altitude = null,
        double? accuracy = null,
        string? address = null,
        string? city = null,
        string? state = null,
        string? country = null,
        string? postalCode = null)
    {
        if (latitude < -90 || latitude > 90)
            throw new ArgumentException("Latitude must be between -90 and 90 degrees", nameof(latitude));

        if (longitude < -180 || longitude > 180)
            throw new ArgumentException("Longitude must be between -180 and 180 degrees", nameof(longitude));

        Latitude = latitude;
        Longitude = longitude;
        Altitude = altitude;
        Accuracy = accuracy;
        Timestamp = timestamp ?? DateTime.UtcNow;
        Address = address;
        City = city;
        State = state;
        Country = country;
        PostalCode = postalCode;
    }

    public double DistanceTo(Location other)
    {
        // Haversine formula to calculate distance between two points
        const double earthRadius = 6371; // Earth's radius in kilometers

        var lat1Rad = ToRadians(Latitude);
        var lat2Rad = ToRadians(other.Latitude);
        var deltaLatRad = ToRadians(other.Latitude - Latitude);
        var deltaLonRad = ToRadians(other.Longitude - Longitude);

        var a = Math.Sin(deltaLatRad / 2) * Math.Sin(deltaLatRad / 2) +
                Math.Cos(lat1Rad) * Math.Cos(lat2Rad) *
                Math.Sin(deltaLonRad / 2) * Math.Sin(deltaLonRad / 2);

        var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));

        return earthRadius * c;
    }

    private static double ToRadians(double degrees)
    {
        return degrees * Math.PI / 180;
    }

    public bool IsWithinRadius(Location center, double radiusKm)
    {
        return DistanceTo(center) <= radiusKm;
    }

    public double CalculateDistanceKm(Location other)
    {
        return DistanceTo(other);
    }

    public override string ToString()
    {
        return $"{Latitude:F6}, {Longitude:F6}";
    }
}

public record TimeWindow
{
    public TimeSpan StartTime { get; init; }
    public TimeSpan EndTime { get; init; }
    public string? TimeZone { get; init; }

    // Parameterless constructor for EF Core
    public TimeWindow() { }

    public TimeWindow(TimeSpan startTime, TimeSpan endTime, string? timeZone = null)
    {
        if (startTime >= endTime)
            throw new ArgumentException("Start time must be before end time");

        StartTime = startTime;
        EndTime = endTime;
        TimeZone = timeZone;
    }

    public bool IsWithinWindow(DateTime dateTime)
    {
        var timeOfDay = dateTime.TimeOfDay;
        return timeOfDay >= StartTime && timeOfDay <= EndTime;
    }

    public TimeSpan Duration => EndTime - StartTime;

    public override string ToString()
    {
        return $"{StartTime:hh\\:mm} - {EndTime:hh\\:mm}";
    }
}

public record Route
{
    public Location StartLocation { get; init; } = null!;
    public Location EndLocation { get; init; } = null!;
    public List<Location> Waypoints { get; init; } = new();
    public double? EstimatedDistanceKm { get; init; }
    public TimeSpan? EstimatedDuration { get; init; }
    public string? RouteInstructions { get; init; }

    // Parameterless constructor for EF Core
    public Route() { }

    public Route(
        Location startLocation,
        Location endLocation,
        List<Location>? waypoints = null,
        double? estimatedDistanceKm = null,
        TimeSpan? estimatedDuration = null,
        string? routeInstructions = null)
    {
        StartLocation = startLocation ?? throw new ArgumentNullException(nameof(startLocation));
        EndLocation = endLocation ?? throw new ArgumentNullException(nameof(endLocation));
        Waypoints = waypoints ?? new List<Location>();
        EstimatedDistanceKm = estimatedDistanceKm;
        EstimatedDuration = estimatedDuration;
        RouteInstructions = routeInstructions;
    }

    public double TotalEstimatedDistance
    {
        get
        {
            if (EstimatedDistanceKm.HasValue)
                return EstimatedDistanceKm.Value;

            // Calculate distance through waypoints
            var totalDistance = 0.0;
            var currentLocation = StartLocation;

            foreach (var waypoint in Waypoints)
            {
                totalDistance += currentLocation.DistanceTo(waypoint);
                currentLocation = waypoint;
            }

            totalDistance += currentLocation.DistanceTo(EndLocation);
            return totalDistance;
        }
    }
}
