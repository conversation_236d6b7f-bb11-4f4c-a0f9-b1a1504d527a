using MediatR;
using Microsoft.Extensions.Logging;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Application.Interfaces;

namespace NetworkFleetManagement.Application.Queries.GetPartnerRecommendations;

public class GetPartnerRecommendationsQueryHandler : IRe<PERSON>Handler<GetPartnerRecommendationsQuery, PartnerRecommendationsDto>
{
    private readonly IPreferredPartnerRepository _preferredPartnerRepository;
    private readonly ICarrierRepository _carrierRepository;
    private readonly INetworkPerformanceRepository _performanceRepository;
    private readonly IRecommendationEngine _recommendationEngine;
    private readonly ILogger<GetPartnerRecommendationsQueryHandler> _logger;

    public GetPartnerRecommendationsQueryHandler(
        IPreferredPartnerRepository preferredPartnerRepository,
        ICarrierRepository carrierRepository,
        INetworkPerformanceRepository performanceRepository,
        IRecommendationEngine recommendationEngine,
        ILogger<GetPartnerRecommendationsQueryHandler> logger)
    {
        _preferredPartnerRepository = preferredPartnerRepository;
        _carrierRepository = carrierRepository;
        _performanceRepository = performanceRepository;
        _recommendationEngine = recommendationEngine;
        _logger = logger;
    }

    public async Task<PartnerRecommendationsDto> Handle(GetPartnerRecommendationsQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting partner recommendations for user {UserId} with criteria {Criteria}", 
            request.UserId, request.Criteria);

        try
        {
            // Validate user permissions
            await ValidateUserPermissions(request.UserId, request.RequestingUserId);

            // Get user's current preferred partners
            var currentPreferredPartners = await _preferredPartnerRepository.GetActiveByUserIdAsync(request.UserId, cancellationToken);

            // Get potential partners based on criteria
            var potentialPartners = await GetPotentialPartners(request, currentPreferredPartners, cancellationToken);

            // Generate recommendations using the recommendation engine
            var recommendations = await _recommendationEngine.GenerateRecommendationsAsync(
                request.UserId,
                potentialPartners,
                request.Criteria,
                request.ServiceAreas,
                request.LoadTypes,
                request.BudgetRange,
                request.MaxRecommendations,
                cancellationToken);

            // Build the response
            var result = new PartnerRecommendationsDto
            {
                UserId = request.UserId,
                GeneratedAt = DateTime.UtcNow,
                Criteria = request.Criteria.ToString(),
                TotalRecommendations = recommendations.Count,
                Recommendations = recommendations.Select(MapToRecommendationDto).ToList()
            };

            // Add performance analysis if requested
            if (request.IncludePerformanceAnalysis)
            {
                await EnrichWithPerformanceAnalysis(result.Recommendations, cancellationToken);
            }

            // Add availability check if requested
            if (request.IncludeAvailabilityCheck && request.RequiredDate.HasValue)
            {
                await EnrichWithAvailabilityCheck(result.Recommendations, request.RequiredDate.Value, cancellationToken);
            }

            // Generate insights and suggestions
            result.Insights = GenerateRecommendationInsights(result.Recommendations, currentPreferredPartners);
            result.Suggestions = GenerateActionSuggestions(result.Recommendations, request.Criteria);

            _logger.LogInformation("Successfully generated {Count} partner recommendations for user {UserId}", 
                result.TotalRecommendations, request.UserId);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting partner recommendations for user {UserId}", request.UserId);
            throw;
        }
    }

    private async Task ValidateUserPermissions(Guid userId, Guid requestingUserId)
    {
        if (userId != requestingUserId)
        {
            throw new UnauthorizedAccessException("You do not have permission to get recommendations for this user");
        }
    }

    private async Task<List<Domain.Entities.Carrier>> GetPotentialPartners(
        GetPartnerRecommendationsQuery request,
        List<Domain.Entities.PreferredPartner> currentPreferred,
        CancellationToken cancellationToken)
    {
        // Get all active carriers
        var allCarriers = await _carrierRepository.GetActiveCarriersAsync(cancellationToken);

        // Filter by partner type if specified
        if (request.PartnerType.HasValue)
        {
            // Apply partner type filtering logic
        }

        // Filter by service areas
        if (request.ServiceAreas.Any())
        {
            allCarriers = allCarriers.Where(c => 
                c.ServiceAreas.Any(sa => request.ServiceAreas.Contains(sa, StringComparer.OrdinalIgnoreCase)))
                .ToList();
        }

        // Exclude current preferred partners if not including new partners only
        if (!request.IncludeNewPartners)
        {
            var currentPartnerIds = currentPreferred.Select(p => p.PartnerId).ToHashSet();
            allCarriers = allCarriers.Where(c => !currentPartnerIds.Contains(c.Id)).ToList();
        }

        return allCarriers;
    }

    private PartnerRecommendationItemDto MapToRecommendationDto(Domain.Entities.Carrier carrier)
    {
        return new PartnerRecommendationItemDto
        {
            PartnerId = carrier.Id,
            PartnerName = carrier.CompanyName,
            PartnerType = "Carrier",
            RecommendationScore = CalculateRecommendationScore(carrier),
            MatchPercentage = CalculateMatchPercentage(carrier),
            RecommendationReason = GenerateRecommendationReason(carrier),
            EstimatedCost = EstimateCost(carrier),
            EstimatedDeliveryTime = EstimateDeliveryTime(carrier),
            PerformanceRating = carrier.OverallRating,
            SafetyRating = carrier.SafetyRating,
            OnTimeDeliveryRate = carrier.OnTimeDeliveryRate,
            CustomerSatisfactionScore = carrier.CustomerSatisfactionScore,
            ServiceAreas = carrier.ServiceAreas.ToList(),
            VehicleTypes = carrier.VehicleTypes.Select(vt => vt.ToString()).ToList(),
            IsNewPartner = true, // Would need to check against current preferred partners
            IsAvailable = true, // Would need availability check
            Strengths = GenerateStrengths(carrier),
            Considerations = GenerateConsiderations(carrier)
        };
    }

    private async Task EnrichWithPerformanceAnalysis(List<PartnerRecommendationItemDto> recommendations, CancellationToken cancellationToken)
    {
        foreach (var recommendation in recommendations)
        {
            var performanceData = await _performanceRepository.GetPartnerPerformanceAsync(recommendation.PartnerId, cancellationToken);
            if (performanceData != null)
            {
                recommendation.PerformanceAnalysis = new PartnerPerformanceAnalysisDto
                {
                    TotalTripsCompleted = performanceData.TotalTripsCompleted,
                    AverageResponseTimeHours = performanceData.AverageResponseTimeHours,
                    ReliabilityScore = performanceData.ReliabilityScore,
                    QualityScore = performanceData.QualityScore,
                    CostEfficiencyRating = performanceData.CostEfficiencyRating,
                    PerformanceTrend = performanceData.PerformanceTrend,
                    LastPerformanceUpdate = performanceData.LastUpdated
                };
            }
        }
    }

    private async Task EnrichWithAvailabilityCheck(List<PartnerRecommendationItemDto> recommendations, DateTime requiredDate, CancellationToken cancellationToken)
    {
        foreach (var recommendation in recommendations)
        {
            // Check availability for the required date
            var isAvailable = await CheckPartnerAvailability(recommendation.PartnerId, requiredDate, cancellationToken);
            recommendation.IsAvailable = isAvailable;
            
            if (!isAvailable)
            {
                recommendation.NextAvailableDate = await GetNextAvailableDate(recommendation.PartnerId, requiredDate, cancellationToken);
            }
        }
    }

    private List<RecommendationInsightDto> GenerateRecommendationInsights(
        List<PartnerRecommendationItemDto> recommendations,
        List<Domain.Entities.PreferredPartner> currentPreferred)
    {
        var insights = new List<RecommendationInsightDto>();

        // Analyze recommendation quality
        var highQualityCount = recommendations.Count(r => r.RecommendationScore >= 8.0m);
        if (highQualityCount > 0)
        {
            insights.Add(new RecommendationInsightDto
            {
                Type = "Quality",
                Title = "High-Quality Partners Available",
                Description = $"Found {highQualityCount} partners with excellent ratings and performance",
                Impact = "Positive"
            });
        }

        // Analyze cost opportunities
        var costEffectiveCount = recommendations.Count(r => r.EstimatedCost?.Amount < 1000); // Example threshold
        if (costEffectiveCount > 0)
        {
            insights.Add(new RecommendationInsightDto
            {
                Type = "Cost",
                Title = "Cost-Effective Options",
                Description = $"Found {costEffectiveCount} partners offering competitive pricing",
                Impact = "Positive"
            });
        }

        return insights;
    }

    private List<ActionSuggestionDto> GenerateActionSuggestions(
        List<PartnerRecommendationItemDto> recommendations,
        RecommendationCriteria criteria)
    {
        var suggestions = new List<ActionSuggestionDto>();

        var topRecommendation = recommendations.OrderByDescending(r => r.RecommendationScore).FirstOrDefault();
        if (topRecommendation != null)
        {
            suggestions.Add(new ActionSuggestionDto
            {
                Action = "Add to Preferred Partners",
                Description = $"Consider adding {topRecommendation.PartnerName} to your preferred partners list",
                Priority = "High",
                PartnerIds = new List<Guid> { topRecommendation.PartnerId },
                ExpectedBenefit = "Improved service quality and reliability"
            });
        }

        return suggestions;
    }

    // Helper methods for calculations
    private decimal CalculateRecommendationScore(Domain.Entities.Carrier carrier)
    {
        // Complex scoring algorithm based on multiple factors
        var score = (carrier.OverallRating * 0.3m) +
                   (carrier.SafetyRating * 0.2m) +
                   (carrier.OnTimeDeliveryRate / 10 * 0.2m) +
                   (carrier.CustomerSatisfactionScore * 0.3m);
        
        return Math.Min(10, Math.Max(0, score));
    }

    private decimal CalculateMatchPercentage(Domain.Entities.Carrier carrier)
    {
        // Calculate how well the carrier matches the user's requirements
        return 85.0m; // Placeholder
    }

    private string GenerateRecommendationReason(Domain.Entities.Carrier carrier)
    {
        var reasons = new List<string>();
        
        if (carrier.OverallRating >= 4.5m)
            reasons.Add("Excellent overall rating");
        
        if (carrier.OnTimeDeliveryRate >= 95)
            reasons.Add("Outstanding on-time delivery");
        
        if (carrier.SafetyRating >= 4.5m)
            reasons.Add("Excellent safety record");

        return reasons.Any() ? string.Join(", ", reasons) : "Good overall performance";
    }

    private MoneyDto? EstimateCost(Domain.Entities.Carrier carrier)
    {
        // Cost estimation logic would go here
        return new MoneyDto { Amount = 1000, Currency = "USD" }; // Placeholder
    }

    private TimeSpan? EstimateDeliveryTime(Domain.Entities.Carrier carrier)
    {
        // Delivery time estimation logic would go here
        return TimeSpan.FromDays(2); // Placeholder
    }

    private List<string> GenerateStrengths(Domain.Entities.Carrier carrier)
    {
        var strengths = new List<string>();
        
        if (carrier.OverallRating >= 4.5m)
            strengths.Add("High customer satisfaction");
        
        if (carrier.SafetyRating >= 4.5m)
            strengths.Add("Excellent safety record");
        
        if (carrier.OnTimeDeliveryRate >= 95)
            strengths.Add("Reliable delivery times");

        return strengths;
    }

    private List<string> GenerateConsiderations(Domain.Entities.Carrier carrier)
    {
        var considerations = new List<string>();
        
        if (carrier.OverallRating < 4.0m)
            considerations.Add("Below average rating");
        
        if (carrier.OnTimeDeliveryRate < 90)
            considerations.Add("Delivery reliability concerns");

        return considerations;
    }

    private async Task<bool> CheckPartnerAvailability(Guid partnerId, DateTime requiredDate, CancellationToken cancellationToken)
    {
        // Availability check logic would go here
        return true; // Placeholder
    }

    private async Task<DateTime?> GetNextAvailableDate(Guid partnerId, DateTime fromDate, CancellationToken cancellationToken)
    {
        // Next available date logic would go here
        return fromDate.AddDays(3); // Placeholder
    }
}
