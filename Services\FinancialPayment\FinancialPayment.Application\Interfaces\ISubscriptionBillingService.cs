using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Application.Interfaces;

public interface ISubscriptionBillingService
{
    Task<Subscription> CreateSubscriptionAsync(CreateSubscriptionRequest request);
    Task<Subscription> UpdateSubscriptionAsync(Guid subscriptionId, UpdateSubscriptionRequest request);
    Task<Subscription> CancelSubscriptionAsync(Guid subscriptionId, string reason);
    Task<Subscription> PauseSubscriptionAsync(Guid subscriptionId, string reason);
    Task<Subscription> ResumeSubscriptionAsync(Guid subscriptionId);
    Task<List<Subscription>> GetUserSubscriptionsAsync(Guid userId);
    Task<Subscription?> GetSubscriptionAsync(Guid subscriptionId);
    Task<List<Subscription>> GetSubscriptionsDueForBillingAsync();
    Task<BillingResult> ProcessBillingAsync(Guid subscriptionId);
    Task<List<BillingResult>> ProcessBatchBillingAsync(List<Guid> subscriptionIds);
    Task<SubscriptionPlan> CreatePlanAsync(CreatePlanRequest request);
    Task<List<SubscriptionPlan>> GetActivePlansAsync();
    Task<SubscriptionPlan?> GetPlanAsync(Guid planId);
    Task<SubscriptionAnalytics> GetSubscriptionAnalyticsAsync(DateTime? fromDate = null, DateTime? toDate = null);
}

public interface ISubscriptionPlanRepository
{
    Task<SubscriptionPlan?> GetByIdAsync(Guid id);
    Task<SubscriptionPlan?> GetByPlanCodeAsync(string planCode);
    Task<List<SubscriptionPlan>> GetActivePlansAsync();
    Task<List<SubscriptionPlan>> GetAllPlansAsync();
    Task AddAsync(SubscriptionPlan plan);
    Task UpdateAsync(SubscriptionPlan plan);
    Task DeleteAsync(Guid id);
}

public interface ISubscriptionRepository
{
    Task<Subscription?> GetByIdAsync(Guid id);
    Task<List<Subscription>> GetByUserIdAsync(Guid userId);
    Task<List<Subscription>> GetByStatusAsync(SubscriptionStatus status);
    Task<List<Subscription>> GetDueForBillingAsync(DateTime beforeDate);
    Task<List<Subscription>> GetExpiringTrialsAsync(DateTime beforeDate);
    Task AddAsync(Subscription subscription);
    Task UpdateAsync(Subscription subscription);
    Task DeleteAsync(Guid id);
}

public interface IBillingProcessor
{
    Task<BillingResult> ProcessSubscriptionBillingAsync(Subscription subscription, SubscriptionPlan plan);
    Task<PaymentResult> ChargeSubscriptionAsync(Subscription subscription, Money amount, string description);
    Task<bool> ValidatePaymentMethodAsync(string paymentMethodId);
    Task<string> CreateCustomerAsync(Guid userId, string email, string? paymentMethodId = null);
    Task<string> UpdateCustomerPaymentMethodAsync(string customerId, string paymentMethodId);
}

public interface ISubscriptionNotificationService
{
    Task SendBillingSuccessNotificationAsync(Subscription subscription, BillingCycle billingCycle);
    Task SendBillingFailureNotificationAsync(Subscription subscription, BillingCycle billingCycle, string reason);
    Task SendTrialEndingNotificationAsync(Subscription subscription, int daysRemaining);
    Task SendSubscriptionCancelledNotificationAsync(Subscription subscription, string reason);
    Task SendPaymentMethodUpdateRequiredNotificationAsync(Subscription subscription);
}

// Request/Response DTOs
public class CreateSubscriptionRequest
{
    public Guid UserId { get; set; }
    public Guid PlanId { get; set; }
    public string? PaymentMethodId { get; set; }
    public string? CustomerId { get; set; }
    public DateTime? StartDate { get; set; }
    public bool StartTrial { get; set; } = false;
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class UpdateSubscriptionRequest
{
    public Guid? NewPlanId { get; set; }
    public string? PaymentMethodId { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}

public class CreatePlanRequest
{
    public string PlanName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal Price { get; set; }
    public string Currency { get; set; } = "INR";
    public BillingInterval BillingInterval { get; set; }
    public int BillingIntervalCount { get; set; } = 1;
    public int? TrialPeriodDays { get; set; }
    public string? PlanCode { get; set; }
    public Dictionary<string, object> Features { get; set; } = new();
    public List<CreatePlanTierRequest> Tiers { get; set; } = new();
}

public class CreatePlanTierRequest
{
    public string TierName { get; set; } = string.Empty;
    public decimal TierPrice { get; set; }
    public string Currency { get; set; } = "INR";
    public int? UserLimit { get; set; }
    public Dictionary<string, object> Features { get; set; } = new();
}

public class BillingResult
{
    public Guid SubscriptionId { get; set; }
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public string? TransactionId { get; set; }
    public Money? AmountCharged { get; set; }
    public DateTime ProcessedAt { get; set; }
    public BillingCycle? BillingCycle { get; set; }
    public int RetryCount { get; set; }
    public DateTime? NextRetryAt { get; set; }
}

public class SubscriptionAnalytics
{
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
    public int TotalSubscriptions { get; set; }
    public int ActiveSubscriptions { get; set; }
    public int CancelledSubscriptions { get; set; }
    public int PausedSubscriptions { get; set; }
    public int TrialSubscriptions { get; set; }
    public Money MonthlyRecurringRevenue { get; set; } = Money.Zero("INR");
    public Money AnnualRecurringRevenue { get; set; } = Money.Zero("INR");
    public decimal ChurnRate { get; set; }
    public decimal GrowthRate { get; set; }
    public Money AverageRevenuePerUser { get; set; } = Money.Zero("INR");
    public Dictionary<string, int> PlanDistribution { get; set; } = new();
    public List<SubscriptionTrend> GrowthTrends { get; set; } = new();
    public List<SubscriptionTrend> RevenueTrends { get; set; } = new();
}

public class SubscriptionTrend
{
    public DateTime Date { get; set; }
    public decimal Value { get; set; }
    public string Label { get; set; } = string.Empty;
    public string MetricType { get; set; } = string.Empty; // subscriptions, revenue, churn
}

public class SubscriptionConfiguration
{
    public bool EnableAutomaticBilling { get; set; } = true;
    public int BillingRetryAttempts { get; set; } = 3;
    public int BillingRetryIntervalHours { get; set; } = 24;
    public int TrialEndingNotificationDays { get; set; } = 3;
    public int PaymentFailureGracePeriodDays { get; set; } = 7;
    public bool EnableProration { get; set; } = true;
    public bool EnableDowngrades { get; set; } = true;
    public bool EnableUpgrades { get; set; } = true;
    public string DefaultCurrency { get; set; } = "INR";
    public List<string> SupportedCurrencies { get; set; } = new() { "INR", "USD", "EUR" };
    public Dictionary<string, string> NotificationTemplates { get; set; } = new();
}

public class SubscriptionSummary
{
    public Guid SubscriptionId { get; set; }
    public Guid UserId { get; set; }
    public string PlanName { get; set; } = string.Empty;
    public SubscriptionStatus Status { get; set; }
    public Money CurrentPrice { get; set; } = Money.Zero("INR");
    public DateTime NextBillingDate { get; set; }
    public DateTime? TrialEndDate { get; set; }
    public bool IsInTrial { get; set; }
    public int BillingCycleCount { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime? EndDate { get; set; }
}

public class BillingSchedule
{
    public Guid SubscriptionId { get; set; }
    public List<ScheduledBilling> UpcomingBillings { get; set; } = new();
    public Money TotalUpcomingAmount { get; set; } = Money.Zero("INR");
    public DateTime NextBillingDate { get; set; }
}

public class ScheduledBilling
{
    public DateTime BillingDate { get; set; }
    public Money Amount { get; set; } = Money.Zero("INR");
    public string Description { get; set; } = string.Empty;
    public bool IsProrated { get; set; }
}
