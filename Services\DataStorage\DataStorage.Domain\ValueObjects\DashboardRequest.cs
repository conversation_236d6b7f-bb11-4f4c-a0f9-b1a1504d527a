using Shared.Domain.ValueObjects;

namespace DataStorage.Domain.ValueObjects;

/// <summary>
/// Request for dashboard operations
/// </summary>
public class DashboardRequest : ValueObject
{
    public string DashboardName { get; init; }
    public string? DashboardType { get; init; }
    public List<string>? WidgetTypes { get; init; }
    public DateTime? FromDate { get; init; }
    public DateTime? ToDate { get; init; }
    public Dictionary<string, object> Parameters { get; init; }
    public List<string>? DataSources { get; init; }
    public string? RefreshInterval { get; init; }
    public bool IncludeMetadata { get; init; }

    public DashboardRequest(
        string dashboardName,
        string? dashboardType = null,
        List<string>? widgetTypes = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        Dictionary<string, object>? parameters = null,
        List<string>? dataSources = null,
        string? refreshInterval = null,
        bool includeMetadata = false)
    {
        DashboardName = dashboardName;
        DashboardType = dashboardType;
        WidgetTypes = widgetTypes;
        FromDate = fromDate;
        ToDate = toDate;
        Parameters = parameters ?? new Dictionary<string, object>();
        DataSources = dataSources;
        RefreshInterval = refreshInterval;
        IncludeMetadata = includeMetadata;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return DashboardName;
        yield return DashboardType ?? string.Empty;
        yield return FromDate?.ToString() ?? string.Empty;
        yield return ToDate?.ToString() ?? string.Empty;
        yield return RefreshInterval ?? string.Empty;
        yield return IncludeMetadata;
        
        if (WidgetTypes != null)
        {
            foreach (var widgetType in WidgetTypes.OrderBy(x => x))
            {
                yield return widgetType;
            }
        }
        
        foreach (var param in Parameters.OrderBy(x => x.Key))
        {
            yield return param.Key;
            yield return param.Value?.ToString() ?? string.Empty;
        }
        
        if (DataSources != null)
        {
            foreach (var dataSource in DataSources.OrderBy(x => x))
            {
                yield return dataSource;
            }
        }
    }
}
