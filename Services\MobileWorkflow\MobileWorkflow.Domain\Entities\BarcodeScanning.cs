using Shared.Domain.Common;
using MobileWorkflow.Domain.Events;

namespace MobileWorkflow.Domain.Entities;

public class ScanSession : AggregateRoot
{
    public Guid UserId { get; private set; }
    public string SessionName { get; private set; }
    public string SessionType { get; private set; } // Single, Batch, Inventory, Verification
    public string Status { get; private set; } // Active, Completed, Cancelled, Paused
    public DateTime StartedAt { get; private set; }
    public DateTime? CompletedAt { get; private set; }
    public string? DeviceId { get; private set; }
    public Dictionary<string, object> SessionConfiguration { get; private set; }
    public Dictionary<string, object> ValidationRules { get; private set; }
    public int TotalScans { get; private set; }
    public int SuccessfulScans { get; private set; }
    public int FailedScans { get; private set; }
    public int DuplicateScans { get; private set; }
    public Dictionary<string, object> SessionData { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    public virtual ICollection<ScanResult> ScanResults { get; private set; } = new List<ScanResult>();

    private ScanSession() { } // EF Core

    public ScanSession(
        Guid userId,
        string sessionName,
        string sessionType,
        Dictionary<string, object> sessionConfiguration)
    {
        UserId = userId;
        SessionName = sessionName ?? throw new ArgumentNullException(nameof(sessionName));
        SessionType = sessionType ?? throw new ArgumentNullException(nameof(sessionType));
        SessionConfiguration = sessionConfiguration ?? throw new ArgumentNullException(nameof(sessionConfiguration));

        Status = "Active";
        StartedAt = DateTime.UtcNow;
        TotalScans = 0;
        SuccessfulScans = 0;
        FailedScans = 0;
        DuplicateScans = 0;
        ValidationRules = new Dictionary<string, object>();
        SessionData = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();

        AddDomainEvent(new ScanSessionStartedEvent(Id, UserId, SessionName, SessionType));
    }

    public void SetValidationRules(Dictionary<string, object> validationRules)
    {
        ValidationRules = validationRules ?? throw new ArgumentNullException(nameof(validationRules));

        AddDomainEvent(new ScanSessionValidationRulesSetEvent(Id, SessionName, validationRules.Count));
    }

    public void SetDeviceId(string deviceId)
    {
        DeviceId = deviceId;
    }

    public void AddScanResult(ScanResult scanResult)
    {
        TotalScans++;

        switch (scanResult.Status.ToLowerInvariant())
        {
            case "success":
                SuccessfulScans++;
                break;
            case "failed":
                FailedScans++;
                break;
            case "duplicate":
                DuplicateScans++;
                break;
        }

        AddDomainEvent(new ScanSessionScanAddedEvent(Id, SessionName, scanResult.Id, scanResult.Status));
    }

    public void Complete()
    {
        if (Status != "Active" && Status != "Paused")
            throw new InvalidOperationException($"Cannot complete session with status: {Status}");

        Status = "Completed";
        CompletedAt = DateTime.UtcNow;

        AddDomainEvent(new ScanSessionCompletedEvent(Id, SessionName, TotalScans, SuccessfulScans, FailedScans));
    }

    public void Cancel()
    {
        if (Status == "Completed")
            throw new InvalidOperationException("Cannot cancel completed session");

        Status = "Cancelled";
        CompletedAt = DateTime.UtcNow;

        AddDomainEvent(new ScanSessionCancelledEvent(Id, SessionName, TotalScans));
    }

    public void Pause()
    {
        if (Status != "Active")
            throw new InvalidOperationException($"Cannot pause session with status: {Status}");

        Status = "Paused";

        AddDomainEvent(new ScanSessionPausedEvent(Id, SessionName));
    }

    public void Resume()
    {
        if (Status != "Paused")
            throw new InvalidOperationException($"Cannot resume session with status: {Status}");

        Status = "Active";

        AddDomainEvent(new ScanSessionResumedEvent(Id, SessionName));
    }

    public bool IsActive()
    {
        return Status == "Active";
    }

    public bool IsCompleted()
    {
        return Status == "Completed" || Status == "Cancelled";
    }

    public TimeSpan? GetDuration()
    {
        var endTime = CompletedAt ?? DateTime.UtcNow;
        return endTime - StartedAt;
    }

    public double GetSuccessRate()
    {
        return TotalScans > 0 ? (double)SuccessfulScans / TotalScans * 100 : 0;
    }

    public double GetFailureRate()
    {
        return TotalScans > 0 ? (double)FailedScans / TotalScans * 100 : 0;
    }

    public double GetDuplicateRate()
    {
        return TotalScans > 0 ? (double)DuplicateScans / TotalScans * 100 : 0;
    }

    public void AddSessionData(string key, object value)
    {
        SessionData[key] = value;
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetSessionData<T>(string key, T? defaultValue = default)
    {
        if (SessionData.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetSessionConfiguration<T>(string key, T? defaultValue = default)
    {
        if (SessionConfiguration.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetValidationRule<T>(string key, T? defaultValue = default)
    {
        if (ValidationRules.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }
}

// Continuing in MobileWorkflow.Domain.Entities namespace
public class ScanResult : AggregateRoot
{
    public Guid SessionId { get; private set; }
    public Guid UserId { get; private set; }
    public string BarcodeData { get; private set; }
    public string BarcodeFormat { get; private set; } // QR, Code128, Code39, EAN13, etc.
    public string Status { get; private set; } // Success, Failed, Duplicate, Invalid
    public DateTime ScannedAt { get; private set; }
    public double? Latitude { get; private set; }
    public double? Longitude { get; private set; }
    public string? DeviceId { get; private set; }
    public Dictionary<string, object> ScanContext { get; private set; }
    public Dictionary<string, object> ValidationResult { get; private set; }
    public Dictionary<string, object> ProcessingResult { get; private set; }
    public string? ErrorMessage { get; private set; }
    public string? ErrorCode { get; private set; }
    public bool IsProcessed { get; private set; }
    public DateTime? ProcessedAt { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    public virtual ScanSession Session { get; private set; } = null!;

    private ScanResult() { } // EF Core

    public ScanResult(
        Guid sessionId,
        Guid userId,
        string barcodeData,
        string barcodeFormat,
        Dictionary<string, object> scanContext)
    {
        SessionId = sessionId;
        UserId = userId;
        BarcodeData = barcodeData ?? throw new ArgumentNullException(nameof(barcodeData));
        BarcodeFormat = barcodeFormat ?? throw new ArgumentNullException(nameof(barcodeFormat));
        ScanContext = scanContext ?? throw new ArgumentNullException(nameof(scanContext));

        Status = "Success"; // Default, can be changed by validation
        ScannedAt = DateTime.UtcNow;
        IsProcessed = false;
        ValidationResult = new Dictionary<string, object>();
        ProcessingResult = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();

        AddDomainEvent(new ScanResultCreatedEvent(Id, SessionId, UserId, BarcodeFormat, barcodeData.Substring(0, Math.Min(50, barcodeData.Length))));
    }

    public void SetLocation(double latitude, double longitude)
    {
        Latitude = latitude;
        Longitude = longitude;
    }

    public void SetDeviceId(string deviceId)
    {
        DeviceId = deviceId;
    }

    public void SetValidationResult(Dictionary<string, object> validationResult, bool isValid)
    {
        ValidationResult = validationResult ?? throw new ArgumentNullException(nameof(validationResult));

        if (!isValid)
        {
            Status = "Invalid";
        }

        AddDomainEvent(new ScanResultValidatedEvent(Id, SessionId, isValid));
    }

    public void MarkAsDuplicate()
    {
        Status = "Duplicate";

        AddDomainEvent(new ScanResultMarkedAsDuplicateEvent(Id, SessionId, BarcodeData));
    }

    public void MarkAsFailed(string errorMessage, string? errorCode = null)
    {
        Status = "Failed";
        ErrorMessage = errorMessage;
        ErrorCode = errorCode;

        AddDomainEvent(new ScanResultFailedEvent(Id, SessionId, errorMessage, errorCode));
    }

    public void MarkAsProcessed(Dictionary<string, object> processingResult)
    {
        IsProcessed = true;
        ProcessedAt = DateTime.UtcNow;
        ProcessingResult = processingResult ?? throw new ArgumentNullException(nameof(processingResult));

        AddDomainEvent(new ScanResultProcessedEvent(Id, SessionId, processingResult));
    }

    public bool IsSuccessful()
    {
        return Status.Equals("Success", StringComparison.OrdinalIgnoreCase);
    }

    public bool IsFailed()
    {
        return Status.Equals("Failed", StringComparison.OrdinalIgnoreCase);
    }

    public bool IsDuplicate()
    {
        return Status.Equals("Duplicate", StringComparison.OrdinalIgnoreCase);
    }

    public bool IsInvalid()
    {
        return Status.Equals("Invalid", StringComparison.OrdinalIgnoreCase);
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetScanContext<T>(string key, T? defaultValue = default)
    {
        if (ScanContext.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetValidationResult<T>(string key, T? defaultValue = default)
    {
        if (ValidationResult.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetProcessingResult<T>(string key, T? defaultValue = default)
    {
        if (ProcessingResult.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }
}

public class BarcodeTemplate : AggregateRoot
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public string BarcodeFormat { get; private set; }
    public string Pattern { get; private set; } // Regex pattern for validation
    public Dictionary<string, object> ValidationRules { get; private set; }
    public Dictionary<string, object> ProcessingRules { get; private set; }
    public bool IsActive { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? UpdatedAt { get; private set; }
    public string CreatedBy { get; private set; }
    public string? UpdatedBy { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    private BarcodeTemplate() { } // EF Core

    public BarcodeTemplate(
        string name,
        string description,
        string barcodeFormat,
        string pattern,
        string createdBy)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        BarcodeFormat = barcodeFormat ?? throw new ArgumentNullException(nameof(barcodeFormat));
        Pattern = pattern ?? throw new ArgumentNullException(nameof(pattern));
        CreatedBy = createdBy ?? throw new ArgumentNullException(nameof(createdBy));

        IsActive = true;
        CreatedAt = DateTime.UtcNow;
        ValidationRules = new Dictionary<string, object>();
        ProcessingRules = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();

        AddDomainEvent(new BarcodeTemplateCreatedEvent(Id, Name, BarcodeFormat, CreatedBy));
    }

    public void UpdateValidationRules(Dictionary<string, object> validationRules, string updatedBy)
    {
        ValidationRules = validationRules ?? throw new ArgumentNullException(nameof(validationRules));
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new BarcodeTemplateValidationRulesUpdatedEvent(Id, Name, UpdatedBy));
    }

    public void UpdateProcessingRules(Dictionary<string, object> processingRules, string updatedBy)
    {
        ProcessingRules = processingRules ?? throw new ArgumentNullException(nameof(processingRules));
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new BarcodeTemplateProcessingRulesUpdatedEvent(Id, Name, UpdatedBy));
    }

    public void Activate(string updatedBy)
    {
        IsActive = true;
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new BarcodeTemplateActivatedEvent(Id, Name, UpdatedBy));
    }

    public void Deactivate(string updatedBy)
    {
        IsActive = false;
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new BarcodeTemplateDeactivatedEvent(Id, Name, UpdatedBy));
    }

    public bool ValidateBarcode(string barcodeData)
    {
        if (string.IsNullOrEmpty(Pattern))
        {
            return true; // No pattern means all data is valid
        }

        try
        {
            return System.Text.RegularExpressions.Regex.IsMatch(barcodeData, Pattern);
        }
        catch
        {
            return false;
        }
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
        UpdatedAt = DateTime.UtcNow;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetValidationRule<T>(string key, T? defaultValue = default)
    {
        if (ValidationRules.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetProcessingRule<T>(string key, T? defaultValue = default)
    {
        if (ProcessingRules.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }
}


