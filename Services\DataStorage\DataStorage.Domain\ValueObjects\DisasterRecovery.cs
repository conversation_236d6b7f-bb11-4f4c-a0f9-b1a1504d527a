using DataStorage.Domain.Enums;

namespace DataStorage.Domain.ValueObjects;

public class BackupSchedule
{
    public Guid Id { get; private set; }
    public string Name { get; private set; }
    public string Description { get; private set; }
    public BackupType Type { get; private set; }
    public string CronExpression { get; private set; }
    public DateTime NextRunTime { get; private set; }
    public DateTime? LastRunTime { get; private set; }
    public bool IsEnabled { get; private set; }
    public Guid CreatedBy { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public BackupRetentionPolicy RetentionPolicy { get; private set; }
    public BackupScheduleStatistics Statistics { get; private set; }

    public BackupSchedule(
        Guid id,
        string name,
        string description,
        BackupType type,
        string cronExpression,
        DateTime nextRunTime,
        bool isEnabled,
        Guid createdBy,
        DateTime createdAt,
        BackupRetentionPolicy retentionPolicy,
        DateTime? lastRunTime = null,
        BackupScheduleStatistics? statistics = null)
    {
        Id = id;
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        Type = type;
        CronExpression = cronExpression ?? throw new ArgumentNullException(nameof(cronExpression));
        NextRunTime = nextRunTime;
        LastRunTime = lastRunTime;
        IsEnabled = isEnabled;
        CreatedBy = createdBy;
        CreatedAt = createdAt;
        RetentionPolicy = retentionPolicy ?? throw new ArgumentNullException(nameof(retentionPolicy));
        Statistics = statistics ?? new BackupScheduleStatistics();
    }
}

public class BackupScheduleRequest
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public BackupType Type { get; private set; }
    public string CronExpression { get; private set; }
    public DateTime StartTime { get; private set; }
    public bool IsEnabled { get; private set; }
    public Guid CreatedBy { get; private set; }
    public BackupRetentionPolicy RetentionPolicy { get; private set; }
    public List<Guid> DocumentIds { get; private set; }
    public List<string> Categories { get; private set; }

    public BackupScheduleRequest(
        string name,
        string description,
        BackupType type,
        string cronExpression,
        DateTime startTime,
        Guid createdBy,
        BackupRetentionPolicy retentionPolicy,
        bool isEnabled = true,
        List<Guid>? documentIds = null,
        List<string>? categories = null)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        Type = type;
        CronExpression = cronExpression ?? throw new ArgumentNullException(nameof(cronExpression));
        StartTime = startTime;
        CreatedBy = createdBy;
        RetentionPolicy = retentionPolicy ?? throw new ArgumentNullException(nameof(retentionPolicy));
        IsEnabled = isEnabled;
        DocumentIds = documentIds ?? new List<Guid>();
        Categories = categories ?? new List<string>();
    }
}

public class BackupScheduleStatistics
{
    public int TotalRuns { get; private set; }
    public int SuccessfulRuns { get; private set; }
    public int FailedRuns { get; private set; }
    public TimeSpan AverageRunTime { get; private set; }
    public long TotalDataBackedUp { get; private set; }
    public DateTime? LastSuccessfulRun { get; private set; }

    public BackupScheduleStatistics(
        int totalRuns = 0,
        int successfulRuns = 0,
        int failedRuns = 0,
        TimeSpan averageRunTime = default,
        long totalDataBackedUp = 0,
        DateTime? lastSuccessfulRun = null)
    {
        TotalRuns = totalRuns;
        SuccessfulRuns = successfulRuns;
        FailedRuns = failedRuns;
        AverageRunTime = averageRunTime;
        TotalDataBackedUp = totalDataBackedUp;
        LastSuccessfulRun = lastSuccessfulRun;
    }
}

public class BackupExecutionResult
{
    public bool IsSuccessful { get; private set; }
    public string? ErrorMessage { get; private set; }
    public Guid ScheduleId { get; private set; }
    public Guid? BackupId { get; private set; }
    public DateTime ExecutedAt { get; private set; }
    public TimeSpan Duration { get; private set; }
    public BackupExecutionStatistics Statistics { get; private set; }

    private BackupExecutionResult()
    {
        Statistics = new BackupExecutionStatistics();
    }

    public static BackupExecutionResult Success(
        Guid scheduleId,
        Guid backupId,
        DateTime executedAt,
        TimeSpan duration,
        BackupExecutionStatistics statistics)
    {
        return new BackupExecutionResult
        {
            IsSuccessful = true,
            ScheduleId = scheduleId,
            BackupId = backupId,
            ExecutedAt = executedAt,
            Duration = duration,
            Statistics = statistics ?? new BackupExecutionStatistics()
        };
    }

    public static BackupExecutionResult Failure(Guid scheduleId, string errorMessage, DateTime executedAt)
    {
        return new BackupExecutionResult
        {
            IsSuccessful = false,
            ScheduleId = scheduleId,
            ErrorMessage = errorMessage,
            ExecutedAt = executedAt,
            Statistics = new BackupExecutionStatistics()
        };
    }
}

public class BackupExecutionStatistics
{
    public int FilesProcessed { get; private set; }
    public long BytesProcessed { get; private set; }
    public int FilesSkipped { get; private set; }
    public int Errors { get; private set; }

    public BackupExecutionStatistics(
        int filesProcessed = 0,
        long bytesProcessed = 0,
        int filesSkipped = 0,
        int errors = 0)
    {
        FilesProcessed = filesProcessed;
        BytesProcessed = bytesProcessed;
        FilesSkipped = filesSkipped;
        Errors = errors;
    }
}

public class ReplicationResult
{
    public bool IsSuccessful { get; private set; }
    public string? ErrorMessage { get; private set; }
    public Guid ReplicationId { get; private set; }
    public string SourceLocation { get; private set; }
    public string TargetLocation { get; private set; }
    public DateTime StartedAt { get; private set; }
    public DateTime? CompletedAt { get; private set; }
    public ReplicationStatistics Statistics { get; private set; }

    private ReplicationResult()
    {
        SourceLocation = string.Empty;
        TargetLocation = string.Empty;
        Statistics = new ReplicationStatistics();
    }

    public static ReplicationResult Success(
        Guid replicationId,
        string sourceLocation,
        string targetLocation,
        DateTime startedAt,
        DateTime completedAt,
        ReplicationStatistics statistics)
    {
        return new ReplicationResult
        {
            IsSuccessful = true,
            ReplicationId = replicationId,
            SourceLocation = sourceLocation,
            TargetLocation = targetLocation,
            StartedAt = startedAt,
            CompletedAt = completedAt,
            Statistics = statistics ?? new ReplicationStatistics()
        };
    }

    public static ReplicationResult Failure(
        Guid replicationId,
        string sourceLocation,
        string targetLocation,
        string errorMessage,
        DateTime startedAt)
    {
        return new ReplicationResult
        {
            IsSuccessful = false,
            ReplicationId = replicationId,
            SourceLocation = sourceLocation,
            TargetLocation = targetLocation,
            ErrorMessage = errorMessage,
            StartedAt = startedAt,
            Statistics = new ReplicationStatistics()
        };
    }
}

public class ReplicationStatistics
{
    public long BytesReplicated { get; private set; }
    public int FilesReplicated { get; private set; }
    public TimeSpan Duration { get; private set; }
    public double ThroughputMBps { get; private set; }

    public ReplicationStatistics(
        long bytesReplicated = 0,
        int filesReplicated = 0,
        TimeSpan duration = default,
        double throughputMBps = 0)
    {
        BytesReplicated = bytesReplicated;
        FilesReplicated = filesReplicated;
        Duration = duration;
        ThroughputMBps = throughputMBps;
    }
}

public class ReplicationSetupRequest
{
    public string Name { get; private set; }
    public string SourceLocation { get; private set; }
    public string TargetLocation { get; private set; }
    public ReplicationType Type { get; private set; }
    public ReplicationFrequency Frequency { get; private set; }
    public bool IsEnabled { get; private set; }
    public Guid CreatedBy { get; private set; }
    public ReplicationOptions Options { get; private set; }

    public ReplicationSetupRequest(
        string name,
        string sourceLocation,
        string targetLocation,
        ReplicationType type,
        ReplicationFrequency frequency,
        Guid createdBy,
        bool isEnabled = true,
        ReplicationOptions? options = null)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        SourceLocation = sourceLocation ?? throw new ArgumentNullException(nameof(sourceLocation));
        TargetLocation = targetLocation ?? throw new ArgumentNullException(nameof(targetLocation));
        Type = type;
        Frequency = frequency;
        CreatedBy = createdBy;
        IsEnabled = isEnabled;
        Options = options ?? new ReplicationOptions();
    }
}

public class ReplicationOptions
{
    public bool CompressData { get; private set; }
    public bool EncryptData { get; private set; }
    public bool VerifyIntegrity { get; private set; }
    public int RetryAttempts { get; private set; }
    public TimeSpan RetryDelay { get; private set; }

    public ReplicationOptions(
        bool compressData = true,
        bool encryptData = true,
        bool verifyIntegrity = true,
        int retryAttempts = 3,
        TimeSpan retryDelay = default)
    {
        CompressData = compressData;
        EncryptData = encryptData;
        VerifyIntegrity = verifyIntegrity;
        RetryAttempts = retryAttempts;
        RetryDelay = retryDelay == default ? TimeSpan.FromMinutes(5) : retryDelay;
    }
}

public class ReplicationTarget
{
    public Guid Id { get; private set; }
    public string Name { get; private set; }
    public string Location { get; private set; }
    public ReplicationTargetType Type { get; private set; }
    public ReplicationTargetStatus Status { get; private set; }
    public DateTime LastSyncTime { get; private set; }
    public long TotalSizeBytes { get; private set; }
    public ReplicationHealth Health { get; private set; }

    public ReplicationTarget(
        Guid id,
        string name,
        string location,
        ReplicationTargetType type,
        ReplicationTargetStatus status,
        DateTime lastSyncTime,
        long totalSizeBytes,
        ReplicationHealth health)
    {
        Id = id;
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Location = location ?? throw new ArgumentNullException(nameof(location));
        Type = type;
        Status = status;
        LastSyncTime = lastSyncTime;
        TotalSizeBytes = totalSizeBytes;
        Health = health;
    }
}

public class ReplicationStatus
{
    public Guid ReplicationId { get; private set; }
    public ReplicationState State { get; private set; }
    public double ProgressPercentage { get; private set; }
    public DateTime? LastSyncTime { get; private set; }
    public DateTime? NextSyncTime { get; private set; }
    public long BytesSynced { get; private set; }
    public int FilesSynced { get; private set; }
    public string? ErrorMessage { get; private set; }
    public ReplicationHealth Health { get; private set; }

    public ReplicationStatus(
        Guid replicationId,
        ReplicationState state,
        double progressPercentage,
        ReplicationHealth health,
        DateTime? lastSyncTime = null,
        DateTime? nextSyncTime = null,
        long bytesSynced = 0,
        int filesSynced = 0,
        string? errorMessage = null)
    {
        ReplicationId = replicationId;
        State = state;
        ProgressPercentage = Math.Max(0, Math.Min(100, progressPercentage));
        LastSyncTime = lastSyncTime;
        NextSyncTime = nextSyncTime;
        BytesSynced = bytesSynced;
        FilesSynced = filesSynced;
        ErrorMessage = errorMessage;
        Health = health;
    }
}
