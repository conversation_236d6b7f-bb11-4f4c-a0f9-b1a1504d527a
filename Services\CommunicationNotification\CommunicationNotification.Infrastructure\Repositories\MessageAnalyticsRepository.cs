using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Domain.Entities;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;
using CommunicationNotification.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace CommunicationNotification.Infrastructure.Repositories;

/// <summary>
/// Repository for message analytics data
/// </summary>
public class MessageAnalyticsRepository : IMessageAnalyticsRepository
{
    private readonly CommunicationDbContext _context;
    private readonly ILogger<MessageAnalyticsRepository> _logger;

    public MessageAnalyticsRepository(
        CommunicationDbContext context,
        ILogger<MessageAnalyticsRepository> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<MessageAnalytics> AddAsync(
        MessageAnalytics analytics,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Adding message analytics for message {MessageId}", analytics.MessageId);

            _context.MessageAnalytics.Add(analytics);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogDebug("Successfully added message analytics with ID {Id}", analytics.Id);
            return analytics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding message analytics for message {MessageId}", analytics.MessageId);
            throw;
        }
    }

    public async Task<MessageAnalytics> UpdateAsync(
        MessageAnalytics analytics,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Updating message analytics {Id}", analytics.Id);

            _context.MessageAnalytics.Update(analytics);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogDebug("Successfully updated message analytics {Id}", analytics.Id);
            return analytics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating message analytics {Id}", analytics.Id);
            throw;
        }
    }

    public async Task<MessageAnalytics?> GetByMessageIdAsync(
        Guid messageId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting message analytics for message {MessageId}", messageId);

            return await _context.MessageAnalytics
                .FirstOrDefaultAsync(ma => ma.MessageId == messageId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting message analytics for message {MessageId}", messageId);
            throw;
        }
    }

    public async Task<List<MessageAnalytics>> GetByDateRangeAsync(
        DateTime startDate,
        DateTime endDate,
        MessageType? messageType = null,
        NotificationChannel? channel = null,
        Guid? userId = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting message analytics from {StartDate} to {EndDate}", startDate, endDate);

            var query = _context.MessageAnalytics
                .Where(ma => ma.SentAt >= startDate && ma.SentAt <= endDate);

            if (messageType.HasValue)
                query = query.Where(ma => ma.MessageType == messageType.Value);

            if (channel.HasValue)
                query = query.Where(ma => ma.Channel == channel.Value);

            if (userId.HasValue)
                query = query.Where(ma => ma.UserId == userId.Value);

            var results = await query
                .OrderBy(ma => ma.SentAt)
                .ToListAsync(cancellationToken);

            _logger.LogDebug("Retrieved {Count} message analytics records", results.Count);
            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting message analytics by date range");
            throw;
        }
    }

    public async Task<MessagePerformanceMetrics> GetAggregatedMetricsAsync(
        DateTime startDate,
        DateTime endDate,
        MessageType? messageType = null,
        NotificationChannel? channel = null,
        Guid? userId = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting aggregated metrics from {StartDate} to {EndDate}", startDate, endDate);

            var query = _context.MessageAnalytics
                .Where(ma => ma.SentAt >= startDate && ma.SentAt <= endDate);

            if (messageType.HasValue)
                query = query.Where(ma => ma.MessageType == messageType.Value);

            if (channel.HasValue)
                query = query.Where(ma => ma.Channel == channel.Value);

            if (userId.HasValue)
                query = query.Where(ma => ma.UserId == userId.Value);

            var analytics = await query.ToListAsync(cancellationToken);

            if (!analytics.Any())
            {
                _logger.LogDebug("No analytics data found for the specified criteria");
                return MessagePerformanceMetrics.Empty();
            }

            // Calculate metrics
            var totalMessages = analytics.Count;
            var deliveredMessages = analytics.Count(a => a.IsDelivered);
            var readMessages = analytics.Count(a => a.IsRead);
            var clickedMessages = analytics.Count(a => a.IsClicked);
            var failedMessages = analytics.Count(a => a.IsFailed);

            var deliveryTimes = analytics
                .Where(a => a.DeliveryTime.HasValue)
                .Select(a => a.DeliveryTime!.Value)
                .ToList();

            var readTimes = analytics
                .Where(a => a.ReadTime.HasValue)
                .Select(a => a.ReadTime!.Value)
                .ToList();

            var averageDeliveryTime = deliveryTimes.Any() 
                ? TimeSpan.FromTicks((long)deliveryTimes.Average(dt => dt.Ticks))
                : TimeSpan.Zero;

            var averageReadTime = readTimes.Any() 
                ? TimeSpan.FromTicks((long)readTimes.Average(rt => rt.Ticks))
                : TimeSpan.Zero;

            var totalCost = analytics.Sum(a => a.Cost);
            var engagementScore = analytics.Average(a => a.GetEngagementScore());

            var metrics = new MessagePerformanceMetrics(
                totalMessages, deliveredMessages, readMessages, clickedMessages, failedMessages,
                averageDeliveryTime, averageReadTime, totalCost, engagementScore);

            _logger.LogDebug("Calculated aggregated metrics: {TotalMessages} total, {DeliveryRate}% delivery rate",
                totalMessages, metrics.DeliveryRate);

            return metrics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting aggregated metrics");
            throw;
        }
    }

    public async Task DeleteOldRecordsAsync(
        DateTime cutoffDate,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Deleting message analytics records older than {CutoffDate}", cutoffDate);

            var oldRecords = await _context.MessageAnalytics
                .Where(ma => ma.SentAt < cutoffDate)
                .ToListAsync(cancellationToken);

            if (oldRecords.Any())
            {
                _context.MessageAnalytics.RemoveRange(oldRecords);
                await _context.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("Deleted {Count} old message analytics records", oldRecords.Count);
            }
            else
            {
                _logger.LogDebug("No old message analytics records found to delete");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting old message analytics records");
            throw;
        }
    }

    public async Task<Dictionary<NotificationChannel, int>> GetChannelDistributionAsync(
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting channel distribution from {StartDate} to {EndDate}", startDate, endDate);

            var distribution = await _context.MessageAnalytics
                .Where(ma => ma.SentAt >= startDate && ma.SentAt <= endDate)
                .GroupBy(ma => ma.Channel)
                .Select(g => new { Channel = g.Key, Count = g.Count() })
                .ToDictionaryAsync(x => x.Channel, x => x.Count, cancellationToken);

            _logger.LogDebug("Retrieved channel distribution for {ChannelCount} channels", distribution.Count);
            return distribution;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting channel distribution");
            throw;
        }
    }

    public async Task<Dictionary<MessageType, int>> GetMessageTypeDistributionAsync(
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting message type distribution from {StartDate} to {EndDate}", startDate, endDate);

            var distribution = await _context.MessageAnalytics
                .Where(ma => ma.SentAt >= startDate && ma.SentAt <= endDate)
                .GroupBy(ma => ma.MessageType)
                .Select(g => new { MessageType = g.Key, Count = g.Count() })
                .ToDictionaryAsync(x => x.MessageType, x => x.Count, cancellationToken);

            _logger.LogDebug("Retrieved message type distribution for {TypeCount} types", distribution.Count);
            return distribution;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting message type distribution");
            throw;
        }
    }

    public async Task<List<MessageAnalytics>> GetTopEngagingMessagesAsync(
        DateTime startDate,
        DateTime endDate,
        int limit = 10,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting top {Limit} engaging messages from {StartDate} to {EndDate}", 
                limit, startDate, endDate);

            var topMessages = await _context.MessageAnalytics
                .Where(ma => ma.SentAt >= startDate && ma.SentAt <= endDate)
                .OrderByDescending(ma => ma.GetEngagementScore())
                .Take(limit)
                .ToListAsync(cancellationToken);

            _logger.LogDebug("Retrieved {Count} top engaging messages", topMessages.Count);
            return topMessages;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting top engaging messages");
            throw;
        }
    }
}
