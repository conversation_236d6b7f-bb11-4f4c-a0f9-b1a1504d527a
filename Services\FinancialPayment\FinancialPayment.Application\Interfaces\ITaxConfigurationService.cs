using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Application.Interfaces;

public interface ITaxConfigurationService
{
    // GST Configuration Management
    Task<GstConfiguration> CreateGstConfigurationAsync(
        string name,
        string description,
        ServiceCategory serviceCategory,
        TaxJurisdiction jurisdiction,
        GstRate gstRate,
        TaxRate taxRate,
        Money minimumAmount,
        Money maximumAmount,
        string createdBy,
        string? hsnCode = null,
        bool isReverseChargeApplicable = false,
        string? reverseChargeConditions = null);

    Task<GstConfiguration> UpdateGstConfigurationAsync(
        Guid id,
        string name,
        string description,
        ServiceCategory serviceCategory,
        TaxJurisdiction jurisdiction,
        GstRate gstRate,
        TaxRate taxRate,
        Money minimumAmount,
        Money maximumAmount,
        string modifiedBy,
        string? hsnCode = null,
        bool isReverseChargeApplicable = false,
        string? reverseChargeConditions = null);

    Task<GstConfiguration?> GetGstConfigurationAsync(Guid id);
    Task<List<GstConfiguration>> GetActiveGstConfigurationsAsync();
    Task<GstConfiguration?> GetApplicableGstConfigurationAsync(ServiceCategory serviceCategory, TaxJurisdiction jurisdiction, DateTime date);
    Task<Money> CalculateGstAsync(Money baseAmount, ServiceCategory serviceCategory, TaxJurisdiction jurisdiction);
    Task ActivateGstConfigurationAsync(Guid id, string activatedBy);
    Task DeactivateGstConfigurationAsync(Guid id, string deactivatedBy, string reason);
    Task ArchiveGstConfigurationAsync(Guid id, string archivedBy, string reason);

    // TDS Configuration Management
    Task<TdsConfiguration> CreateTdsConfigurationAsync(
        string name,
        string description,
        TdsSection section,
        EntityType entityType,
        TaxRate taxRate,
        TdsThreshold threshold,
        string createdBy,
        bool requiresPan = true,
        decimal higherRateWithoutPan = 0,
        string? specialConditions = null);

    Task<TdsConfiguration> UpdateTdsConfigurationAsync(
        Guid id,
        string name,
        string description,
        TdsSection section,
        EntityType entityType,
        TaxRate taxRate,
        TdsThreshold threshold,
        string modifiedBy,
        bool requiresPan = true,
        decimal higherRateWithoutPan = 0,
        string? specialConditions = null);

    Task<TdsConfiguration?> GetTdsConfigurationAsync(Guid id);
    Task<List<TdsConfiguration>> GetActiveTdsConfigurationsAsync();
    Task<TdsConfiguration?> GetApplicableTdsConfigurationAsync(TdsSection section, EntityType entityType, DateTime date, bool hasPan = true);
    Task<Money> CalculateTdsAsync(Money baseAmount, TdsSection section, EntityType entityType, bool hasPan = true);
    Task ActivateTdsConfigurationAsync(Guid id, string activatedBy);
    Task DeactivateTdsConfigurationAsync(Guid id, string deactivatedBy, string reason);
    Task ArchiveTdsConfigurationAsync(Guid id, string archivedBy, string reason);

    // HSN Code Management
    Task<HsnCode> CreateHsnCodeAsync(
        HsnCodeDetails codeDetails,
        string chapter,
        string section,
        DateTime effectiveFrom,
        string createdBy,
        DateTime? effectiveTo = null,
        string? additionalNotes = null);

    Task<HsnCode> UpdateHsnCodeAsync(
        Guid id,
        HsnCodeDetails codeDetails,
        string chapter,
        string section,
        string modifiedBy,
        string? additionalNotes = null);

    Task<HsnCode?> GetHsnCodeAsync(Guid id);
    Task<HsnCode?> GetHsnCodeByCodeAsync(string code);
    Task<List<HsnCode>> GetActiveHsnCodesAsync();
    Task<List<HsnCode>> SearchHsnCodesAsync(string searchTerm, int skip = 0, int take = 50);
    Task<GstRate?> GetApplicableGstRateForHsnAsync(string hsnCode, DateTime date);
    Task AddGstMappingToHsnAsync(Guid hsnCodeId, GstRate gstRate, DateTime effectiveFrom, DateTime? effectiveTo, string addedBy);
    Task<List<HsnCode>> BulkImportHsnCodesAsync(List<HsnCode> hsnCodes);
    Task ActivateHsnCodeAsync(Guid id, string activatedBy);
    Task DeactivateHsnCodeAsync(Guid id, string deactivatedBy, string reason);
    Task DeprecateHsnCodeAsync(Guid id, string deprecatedBy, string reason);

    // Tax Configuration Management
    Task<TaxConfiguration> CreateTaxConfigurationAsync(
        string name,
        string description,
        TaxJurisdiction jurisdiction,
        DateTime effectiveFrom,
        string createdBy,
        DateTime? effectiveTo = null,
        bool isDefault = false,
        int priority = 0);

    Task<TaxConfiguration> UpdateTaxConfigurationAsync(
        Guid id,
        string name,
        string description,
        TaxJurisdiction jurisdiction,
        DateTime effectiveFrom,
        DateTime? effectiveTo,
        string modifiedBy,
        int priority = 0);

    Task<TaxConfiguration> UpdateTaxSettingsAsync(
        Guid id,
        bool enableGstCalculation,
        bool enableTdsCalculation,
        bool enableReverseCharge,
        bool requireHsnCode,
        decimal defaultGstRate,
        decimal defaultTdsRate,
        string defaultCurrency,
        string modifiedBy);

    Task<TaxConfiguration?> GetTaxConfigurationAsync(Guid id);
    Task<TaxConfiguration?> GetDefaultTaxConfigurationAsync();
    Task<TaxConfiguration?> GetApplicableTaxConfigurationAsync(TaxJurisdiction jurisdiction, DateTime date);
    Task<List<TaxConfiguration>> GetActiveTaxConfigurationsAsync();
    Task SetAsDefaultTaxConfigurationAsync(Guid id, string modifiedBy);
    Task RemoveAsDefaultTaxConfigurationAsync(Guid id, string modifiedBy);
    Task ActivateTaxConfigurationAsync(Guid id, string activatedBy);
    Task DeactivateTaxConfigurationAsync(Guid id, string deactivatedBy, string reason);
    Task ArchiveTaxConfigurationAsync(Guid id, string archivedBy, string reason);

    // Comprehensive Tax Calculation
    Task<ComprehensiveTaxCalculationResult> CalculateComprehensiveTaxAsync(
        Money baseAmount,
        ServiceCategory serviceCategory,
        TaxJurisdiction jurisdiction,
        EntityType entityType,
        TdsSection? tdsSection = null,
        string? hsnCode = null,
        bool hasPan = true,
        DateTime? calculationDate = null);

    // Validation and Compliance
    Task<bool> ValidateTaxConfigurationAsync(Guid taxConfigurationId);
    Task<List<string>> GetTaxComplianceIssuesAsync(TaxJurisdiction jurisdiction);
    Task<bool> IsHsnCodeRequiredAsync(ServiceCategory serviceCategory, TaxJurisdiction jurisdiction);
}

public class ComprehensiveTaxCalculationResult
{
    public Money BaseAmount { get; set; } = Money.Zero("INR");
    public Money GstAmount { get; set; } = Money.Zero("INR");
    public Money TdsAmount { get; set; } = Money.Zero("INR");
    public Money TotalTaxAmount { get; set; } = Money.Zero("INR");
    public Money NetAmount { get; set; } = Money.Zero("INR");
    public decimal EffectiveGstRate { get; set; }
    public decimal EffectiveTdsRate { get; set; }
    public string? AppliedHsnCode { get; set; }
    public bool IsReverseChargeApplicable { get; set; }
    public List<string> AppliedRules { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public DateTime CalculatedAt { get; set; } = DateTime.UtcNow;
}
