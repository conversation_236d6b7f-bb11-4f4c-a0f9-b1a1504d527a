using MediatR;

namespace SubscriptionManagement.Application.Commands.CancelSubscription;

public class CancelSubscriptionCommand : IRequest<bool>
{
    public Guid SubscriptionId { get; set; }
    public Guid UserId { get; set; }
    public string? CancellationReason { get; set; }
    public bool ImmediateCancellation { get; set; } = false;
    public bool RefundProration { get; set; } = false;
    
    public CancelSubscriptionCommand(Guid subscriptionId, Guid userId, string? reason = null)
    {
        SubscriptionId = subscriptionId;
        UserId = userId;
        CancellationReason = reason;
    }
}
