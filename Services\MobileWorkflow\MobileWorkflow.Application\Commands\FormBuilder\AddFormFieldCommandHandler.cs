using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.Extensions.Logging;
using MobileWorkflow.Application.DTOs;
using MobileWorkflow.Application.Services;

namespace MobileWorkflow.Application.Commands.FormBuilder
{
    public class AddFormFieldCommandHandler : IRequestHandler<AddFormFieldCommand, FormFieldDto>
    {
        private readonly IFormBuilderService _formBuilderService;
        private readonly ILogger<AddFormFieldCommandHandler> _logger;

        public AddFormFieldCommandHandler(
            IFormBuilderService formBuilderService,
            ILogger<AddFormFieldCommandHandler> logger)
        {
            _formBuilderService = formBuilderService;
            _logger = logger;
        }

        public async Task<FormFieldDto> Handle(AddFormFieldCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Adding field {FieldName} to form {FormId}",
                    request.Name, request.FormDefinitionId);

                var createRequest = new MobileWorkflow.Application.Services.CreateFormFieldRequest
                {
                    FormDefinitionId = request.FormDefinitionId,
                    FormStepId = request.FormStepId ?? Guid.Empty,
                    Name = request.Name,
                    DisplayName = request.DisplayName,
                    Description = request.Description,
                    FieldType = request.FieldType,
                    Order = request.Order,
                    IsRequired = request.IsRequired,
                    IsReadOnly = request.IsReadOnly,
                    Configuration = request.Configuration,
                    ValidationRules = request.ValidationRules,
                    ConditionalLogic = request.ConditionalLogic,
                    Styling = request.Styling,
                    DataBinding = request.DataBinding,
                    DefaultValue = request.DefaultValue?.ToString() ?? string.Empty,
                    PlaceholderText = request.PlaceholderText ?? string.Empty,
                    HelpText = request.HelpText ?? string.Empty,
                    Options = request.Options?.Select(o => new MobileWorkflow.Application.Services.CreateFormFieldOptionRequest
                    {
                        Label = o.DisplayText,
                        Value = o.Value,
                        IsDefault = o.IsDefault,
                        Properties = o.Configuration ?? new Dictionary<string, object>()
                    }).ToList() ?? new List<MobileWorkflow.Application.Services.CreateFormFieldOptionRequest>()
                };

                var result = await _formBuilderService.AddFormFieldAsync(createRequest, cancellationToken);

                _logger.LogInformation("Successfully added field {FieldId} to form {FormId}",
                    result.Id, request.FormDefinitionId);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding field {FieldName} to form {FormId}",
                    request.Name, request.FormDefinitionId);
                throw;
            }
        }
    }
}
