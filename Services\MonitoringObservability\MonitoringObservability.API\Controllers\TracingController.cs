using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MonitoringObservability.Application.Commands;
using MonitoringObservability.Application.DTOs;
using MonitoringObservability.Application.Queries;
using MonitoringObservability.Domain.Enums;

namespace MonitoringObservability.API.Controllers;

/// <summary>
/// Distributed tracing API controller
/// </summary>
[ApiController]
[Route("api/monitoring/tracing")]
[Authorize(Roles = "Admin,Monitor")]
public class TracingController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<TracingController> _logger;

    public TracingController(IMediator mediator, ILogger<TracingController> logger)
    {
        _mediator = mediator ?? throw new ArgumentNullException(nameof(mediator));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Start a new distributed trace
    /// </summary>
    [HttpPost("traces/start")]
    public async Task<IActionResult> StartTrace([FromBody] StartTraceCommand command)
    {
        try
        {
            await _mediator.Send(command);
            return Ok(new { success = true, traceId = command.TraceId });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start trace {TraceId}", command.TraceId);
            return StatusCode(500, "Failed to start trace");
        }
    }

    /// <summary>
    /// End a distributed trace
    /// </summary>
    [HttpPost("traces/end")]
    public async Task<IActionResult> EndTrace([FromBody] EndTraceCommand command)
    {
        try
        {
            await _mediator.Send(command);
            return Ok(new { success = true, traceId = command.TraceId });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to end trace {TraceId}", command.TraceId);
            return StatusCode(500, "Failed to end trace");
        }
    }

    /// <summary>
    /// Record a span in a trace
    /// </summary>
    [HttpPost("spans/record")]
    public async Task<IActionResult> RecordSpan([FromBody] RecordSpanCommand command)
    {
        try
        {
            await _mediator.Send(command);
            return Ok(new { success = true, spanId = command.SpanId });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to record span {SpanId}", command.SpanId);
            return StatusCode(500, "Failed to record span");
        }
    }

    /// <summary>
    /// Get trace by ID
    /// </summary>
    [HttpGet("traces/{traceId}")]
    public async Task<ActionResult<TraceDto>> GetTrace(string traceId)
    {
        try
        {
            var query = new GetTraceByTraceIdQuery { TraceId = traceId };
            var trace = await _mediator.Send(query);
            
            if (trace == null)
            {
                return NotFound($"Trace {traceId} not found");
            }
            
            return Ok(trace);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get trace {TraceId}", traceId);
            return StatusCode(500, "Failed to get trace");
        }
    }

    /// <summary>
    /// Search traces
    /// </summary>
    [HttpGet("traces")]
    public async Task<ActionResult<IEnumerable<TraceDto>>> SearchTraces(
        [FromQuery] string? serviceName = null,
        [FromQuery] string? operationName = null,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] int? minDurationMs = null,
        [FromQuery] int? maxDurationMs = null,
        [FromQuery] TraceStatus? status = null,
        [FromQuery] bool? hasErrors = null,
        [FromQuery] int skip = 0,
        [FromQuery] int take = 100)
    {
        try
        {
            var query = new SearchTracesQuery
            {
                ServiceName = serviceName,
                OperationName = operationName,
                FromDate = fromDate,
                ToDate = toDate,
                MinDuration = minDurationMs.HasValue ? TimeSpan.FromMilliseconds(minDurationMs.Value) : null,
                MaxDuration = maxDurationMs.HasValue ? TimeSpan.FromMilliseconds(maxDurationMs.Value) : null,
                Status = status,
                HasErrors = hasErrors,
                Skip = skip,
                Take = take
            };
            
            var traces = await _mediator.Send(query);
            return Ok(traces);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to search traces");
            return StatusCode(500, "Failed to search traces");
        }
    }

    /// <summary>
    /// Get slow traces
    /// </summary>
    [HttpGet("traces/slow")]
    public async Task<ActionResult<IEnumerable<TraceDto>>> GetSlowTraces(
        [FromQuery] string? serviceName = null,
        [FromQuery] int thresholdMs = 5000,
        [FromQuery] int periodHours = 24,
        [FromQuery] int limit = 100)
    {
        try
        {
            var query = new GetSlowTracesQuery
            {
                ServiceName = serviceName,
                Threshold = TimeSpan.FromMilliseconds(thresholdMs),
                Period = TimeSpan.FromHours(periodHours),
                Limit = limit
            };
            
            var traces = await _mediator.Send(query);
            return Ok(traces);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get slow traces");
            return StatusCode(500, "Failed to get slow traces");
        }
    }

    /// <summary>
    /// Get error traces
    /// </summary>
    [HttpGet("traces/errors")]
    public async Task<ActionResult<IEnumerable<TraceDto>>> GetErrorTraces(
        [FromQuery] string? serviceName = null,
        [FromQuery] int periodHours = 24,
        [FromQuery] int limit = 100)
    {
        try
        {
            var query = new GetErrorTracesQuery
            {
                ServiceName = serviceName,
                Period = TimeSpan.FromHours(periodHours),
                Limit = limit
            };
            
            var traces = await _mediator.Send(query);
            return Ok(traces);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get error traces");
            return StatusCode(500, "Failed to get error traces");
        }
    }

    /// <summary>
    /// Get trace statistics
    /// </summary>
    [HttpGet("traces/statistics")]
    public async Task<ActionResult<TraceStatisticsDto>> GetTraceStatistics(
        [FromQuery] string? serviceName = null,
        [FromQuery] int periodHours = 24)
    {
        try
        {
            var query = new GetTraceStatisticsQuery
            {
                ServiceName = serviceName,
                Period = TimeSpan.FromHours(periodHours)
            };
            
            var statistics = await _mediator.Send(query);
            return Ok(statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get trace statistics");
            return StatusCode(500, "Failed to get trace statistics");
        }
    }

    /// <summary>
    /// Get service dependency graph
    /// </summary>
    [HttpGet("dependencies/graph")]
    public async Task<ActionResult<ServiceDependencyGraphDto>> GetServiceDependencyGraph(
        [FromQuery] bool includeHealthStatus = true,
        [FromQuery] int? periodHours = null)
    {
        try
        {
            var query = new GetServiceDependencyGraphQuery
            {
                IncludeHealthStatus = includeHealthStatus,
                Period = periodHours.HasValue ? TimeSpan.FromHours(periodHours.Value) : null
            };
            
            var graph = await _mediator.Send(query);
            return Ok(graph);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get service dependency graph");
            return StatusCode(500, "Failed to get service dependency graph");
        }
    }

    /// <summary>
    /// Get service dependencies
    /// </summary>
    [HttpGet("dependencies/{serviceName}")]
    public async Task<ActionResult<IEnumerable<ServiceDependencyDto>>> GetServiceDependencies(string serviceName)
    {
        try
        {
            var query = new GetServiceDependenciesQuery { ServiceName = serviceName };
            var dependencies = await _mediator.Send(query);
            return Ok(dependencies);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get dependencies for service {ServiceName}", serviceName);
            return StatusCode(500, "Failed to get service dependencies");
        }
    }

    /// <summary>
    /// Get high error rate dependencies
    /// </summary>
    [HttpGet("dependencies/high-error-rate")]
    public async Task<ActionResult<IEnumerable<ServiceDependencyDto>>> GetHighErrorRateDependencies(
        [FromQuery] double errorRateThreshold = 0.05)
    {
        try
        {
            var query = new GetHighErrorRateDependenciesQuery { ErrorRateThreshold = errorRateThreshold };
            var dependencies = await _mediator.Send(query);
            return Ok(dependencies);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get high error rate dependencies");
            return StatusCode(500, "Failed to get high error rate dependencies");
        }
    }

    /// <summary>
    /// Analyze trace performance
    /// </summary>
    [HttpPost("traces/{traceId}/analyze")]
    public async Task<ActionResult<TracePerformanceAnalysisDto>> AnalyzeTracePerformance(
        string traceId,
        [FromQuery] bool includeRecommendations = true)
    {
        try
        {
            var command = new AnalyzeTracePerformanceCommand
            {
                TraceId = traceId,
                IncludeRecommendations = includeRecommendations
            };
            
            var analysis = await _mediator.Send(command);
            return Ok(analysis);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to analyze trace performance for {TraceId}", traceId);
            return StatusCode(500, "Failed to analyze trace performance");
        }
    }

    /// <summary>
    /// Detect trace anomalies
    /// </summary>
    [HttpPost("traces/detect-anomalies")]
    public async Task<ActionResult<List<TraceAnomalyDto>>> DetectTraceAnomalies(
        [FromQuery] string? serviceName = null,
        [FromQuery] int periodHours = 24,
        [FromQuery] double anomalyThreshold = 2.0)
    {
        try
        {
            var command = new DetectTraceAnomaliesCommand
            {
                ServiceName = serviceName,
                Period = TimeSpan.FromHours(periodHours),
                AnomalyThreshold = anomalyThreshold
            };
            
            var anomalies = await _mediator.Send(command);
            return Ok(anomalies);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to detect trace anomalies");
            return StatusCode(500, "Failed to detect trace anomalies");
        }
    }

    /// <summary>
    /// Generate trace report
    /// </summary>
    [HttpPost("reports/generate")]
    public async Task<ActionResult<TraceReportDto>> GenerateTraceReport([FromBody] GenerateTraceReportCommand command)
    {
        try
        {
            var report = await _mediator.Send(command);
            return Ok(report);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate trace report");
            return StatusCode(500, "Failed to generate trace report");
        }
    }

    /// <summary>
    /// Cleanup old traces
    /// </summary>
    [HttpPost("cleanup")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<TraceCleanupResultDto>> CleanupOldTraces([FromBody] CleanupOldTracesCommand command)
    {
        try
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to cleanup old traces");
            return StatusCode(500, "Failed to cleanup old traces");
        }
    }
}
