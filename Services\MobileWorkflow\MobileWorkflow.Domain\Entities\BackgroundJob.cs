using Shared.Domain.Common;
using MobileWorkflow.Domain.Events;

namespace MobileWorkflow.Domain.Entities;

public class BackgroundJob : AggregateRoot
{
    public string JobType { get; private set; }
    public string JobName { get; private set; }
    public string Queue { get; private set; }
    public string Status { get; private set; } // Pending, Running, Completed, Failed, Cancelled, Scheduled
    public int Priority { get; private set; } // Higher number = higher priority
    public Dictionary<string, object> Parameters { get; private set; }
    public Dictionary<string, object> Result { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? ScheduledAt { get; private set; }
    public DateTime? StartedAt { get; private set; }
    public DateTime? CompletedAt { get; private set; }
    public TimeSpan? Duration { get; private set; }
    public string? ErrorMessage { get; private set; }
    public string? ErrorStackTrace { get; private set; }
    public int RetryCount { get; private set; }
    public int MaxRetries { get; private set; }
    public DateTime? NextRetryAt { get; private set; }
    public TimeSpan? RetryDelay { get; private set; }
    public string? ParentJobId { get; private set; }
    public List<string> ChildJobIds { get; private set; }
    public string? CreatedBy { get; private set; }
    public string? WorkerId { get; private set; }
    public Dictionary<string, object> Progress { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    public virtual ICollection<JobExecution> Executions { get; private set; } = new List<JobExecution>();

    private BackgroundJob() { } // EF Core

    public BackgroundJob(
        string jobType,
        string jobName,
        string queue,
        Dictionary<string, object> parameters,
        int priority = 0,
        DateTime? scheduledAt = null,
        string? createdBy = null)
    {
        JobType = jobType ?? throw new ArgumentNullException(nameof(jobType));
        JobName = jobName ?? throw new ArgumentNullException(nameof(jobName));
        Queue = queue ?? throw new ArgumentNullException(nameof(queue));
        Parameters = parameters ?? throw new ArgumentNullException(nameof(parameters));
        Priority = priority;
        ScheduledAt = scheduledAt;
        CreatedBy = createdBy;

        Status = scheduledAt.HasValue && scheduledAt.Value > DateTime.UtcNow ? "Scheduled" : "Pending";
        CreatedAt = DateTime.UtcNow;
        RetryCount = 0;
        MaxRetries = GetDefaultMaxRetries(jobType);
        Result = new Dictionary<string, object>();
        ChildJobIds = new List<string>();
        Progress = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();

        AddDomainEvent(new BackgroundJobCreatedEvent(Id, JobType, JobName, Queue, Priority, ScheduledAt));
    }

    public void Schedule(DateTime scheduledAt)
    {
        if (Status != "Pending")
            throw new InvalidOperationException($"Cannot schedule job with status: {Status}");

        ScheduledAt = scheduledAt;
        Status = "Scheduled";

        AddDomainEvent(new BackgroundJobScheduledEvent(Id, JobType, JobName, scheduledAt));
    }

    public void Start(string workerId)
    {
        if (Status != "Pending" && Status != "Scheduled")
            throw new InvalidOperationException($"Cannot start job with status: {Status}");

        Status = "Running";
        StartedAt = DateTime.UtcNow;
        WorkerId = workerId;

        AddDomainEvent(new BackgroundJobStartedEvent(Id, JobType, JobName, workerId, StartedAt.Value));
    }

    public void Complete(Dictionary<string, object> result)
    {
        if (Status != "Running")
            throw new InvalidOperationException($"Cannot complete job with status: {Status}");

        Status = "Completed";
        CompletedAt = DateTime.UtcNow;
        Result = result ?? throw new ArgumentNullException(nameof(result));

        if (StartedAt.HasValue)
        {
            Duration = CompletedAt.Value - StartedAt.Value;
        }

        AddDomainEvent(new BackgroundJobCompletedEvent(Id, JobType, JobName, Duration?.TotalMilliseconds, result));
    }

    public void Fail(string errorMessage, string? errorStackTrace = null)
    {
        Status = "Failed";
        CompletedAt = DateTime.UtcNow;
        ErrorMessage = errorMessage;
        ErrorStackTrace = errorStackTrace;

        if (StartedAt.HasValue)
        {
            Duration = CompletedAt.Value - StartedAt.Value;
        }

        AddDomainEvent(new BackgroundJobFailedEvent(Id, JobType, JobName, errorMessage, RetryCount));
    }

    public void Cancel(string reason = "Cancelled by user")
    {
        if (Status == "Completed" || Status == "Failed")
            throw new InvalidOperationException($"Cannot cancel job with status: {Status}");

        Status = "Cancelled";
        CompletedAt = DateTime.UtcNow;
        ErrorMessage = reason;

        if (StartedAt.HasValue)
        {
            Duration = CompletedAt.Value - StartedAt.Value;
        }

        AddDomainEvent(new BackgroundJobCancelledEvent(Id, JobType, JobName, reason));
    }

    public void Retry(TimeSpan? delay = null)
    {
        if (Status != "Failed")
            throw new InvalidOperationException($"Cannot retry job with status: {Status}");

        if (RetryCount >= MaxRetries)
            throw new InvalidOperationException($"Maximum retry count ({MaxRetries}) exceeded");

        RetryCount++;
        Status = "Pending";
        ErrorMessage = null;
        ErrorStackTrace = null;
        WorkerId = null;

        if (delay.HasValue)
        {
            RetryDelay = delay;
            NextRetryAt = DateTime.UtcNow.Add(delay.Value);
            Status = "Scheduled";
            ScheduledAt = NextRetryAt;
        }

        AddDomainEvent(new BackgroundJobRetriedEvent(Id, JobType, JobName, RetryCount, NextRetryAt));
    }

    public void UpdateProgress(Dictionary<string, object> progress)
    {
        Progress = progress ?? throw new ArgumentNullException(nameof(progress));

        AddDomainEvent(new BackgroundJobProgressUpdatedEvent(Id, JobType, JobName, progress));
    }

    public void SetParentJob(string parentJobId)
    {
        ParentJobId = parentJobId;
    }

    public void AddChildJob(string childJobId)
    {
        if (!ChildJobIds.Contains(childJobId))
        {
            ChildJobIds.Add(childJobId);
        }
    }

    public void RemoveChildJob(string childJobId)
    {
        ChildJobIds.Remove(childJobId);
    }

    public bool IsReadyToRun()
    {
        return Status == "Pending" || (Status == "Scheduled" && ScheduledAt <= DateTime.UtcNow);
    }

    public bool CanRetry()
    {
        return Status == "Failed" && RetryCount < MaxRetries;
    }

    public bool IsCompleted()
    {
        return Status == "Completed" || Status == "Failed" || Status == "Cancelled";
    }

    public bool IsRunning()
    {
        return Status == "Running";
    }

    public TimeSpan GetAge()
    {
        return DateTime.UtcNow - CreatedAt;
    }

    public TimeSpan? GetExecutionTime()
    {
        if (StartedAt.HasValue)
        {
            var endTime = CompletedAt ?? DateTime.UtcNow;
            return endTime - StartedAt.Value;
        }
        return null;
    }

    public double GetProgressPercentage()
    {
        if (Progress.TryGetValue("percentage", out var percentage) && percentage is double pct)
        {
            return Math.Max(0, Math.Min(100, pct));
        }

        if (Status == "Completed")
            return 100.0;

        if (Status == "Running" && StartedAt.HasValue)
        {
            // Estimate progress based on execution time if no explicit progress
            var executionTime = GetExecutionTime();
            if (executionTime.HasValue && executionTime.Value.TotalMinutes > 0)
            {
                // Simple heuristic: assume 50% progress after 1 minute
                return Math.Min(50, executionTime.Value.TotalMinutes * 50);
            }
        }

        return 0.0;
    }

    public string GetEstimatedTimeRemaining()
    {
        var progressPct = GetProgressPercentage();
        if (progressPct > 0 && progressPct < 100 && StartedAt.HasValue)
        {
            var elapsed = GetExecutionTime()?.TotalSeconds ?? 0;
            var estimated = (elapsed / progressPct) * (100 - progressPct);
            return TimeSpan.FromSeconds(estimated).ToString(@"hh\:mm\:ss");
        }
        return "Unknown";
    }

    private int GetDefaultMaxRetries(string jobType)
    {
        return jobType.ToLowerInvariant() switch
        {
            "email" or "notification" => 3,
            "sync" or "backup" => 5,
            "import" or "export" => 2,
            "cleanup" or "maintenance" => 1,
            _ => 3
        };
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetParameter<T>(string key, T? defaultValue = default)
    {
        if (Parameters.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetResult<T>(string key, T? defaultValue = default)
    {
        if (Result.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetProgress<T>(string key, T? defaultValue = default)
    {
        if (Progress.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public Dictionary<string, object> GetJobSummary()
    {
        return new Dictionary<string, object>
        {
            ["id"] = Id,
            ["jobType"] = JobType,
            ["jobName"] = JobName,
            ["queue"] = Queue,
            ["status"] = Status,
            ["priority"] = Priority,
            ["createdAt"] = CreatedAt,
            ["scheduledAt"] = ScheduledAt,
            ["startedAt"] = StartedAt,
            ["completedAt"] = CompletedAt,
            ["duration"] = Duration?.TotalMilliseconds,
            ["retryCount"] = RetryCount,
            ["maxRetries"] = MaxRetries,
            ["progressPercentage"] = GetProgressPercentage(),
            ["estimatedTimeRemaining"] = GetEstimatedTimeRemaining(),
            ["workerId"] = WorkerId,
            ["hasChildren"] = ChildJobIds.Count > 0,
            ["childCount"] = ChildJobIds.Count
        };
    }
}


