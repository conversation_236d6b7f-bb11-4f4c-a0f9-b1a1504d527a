using System.Net;
using System.Net.Http.Json;
using System.Text;
using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Xunit;
using FinancialPayment.Application.Commands.CreateEscrowAccount;
using FinancialPayment.Application.DTOs;
using FinancialPayment.Application.Queries.GetEscrowAccount;
using FinancialPayment.Infrastructure.Data;
using FinancialPayment.IntegrationTests.Infrastructure;

namespace FinancialPayment.IntegrationTests.Controllers;

public class EscrowControllerTests : IClassFixture<TestWebApplicationFactory>
{
    private readonly TestWebApplicationFactory _factory;
    private readonly HttpClient _client;

    public EscrowControllerTests(TestWebApplicationFactory factory)
    {
        _factory = factory;
        _client = _factory.CreateClient();
    }

    [Fact]
    public async Task CreateEscrowAccount_ShouldReturnCreated_WhenValidRequest()
    {
        // Arrange
        await _factory.ResetDatabaseAsync();

        var command = new CreateEscrowAccountCommand
        {
            OrderId = Guid.NewGuid(),
            TransportCompanyId = Guid.NewGuid(),
            BrokerId = Guid.NewGuid(),
            CarrierId = Guid.NewGuid(),
            TotalAmount = new CreateMoneyDto { Amount = 10000m, Currency = "INR" },
            Notes = "Integration test escrow account"
        };

        var json = JsonConvert.SerializeObject(command);
        var content = new StringContent(json, Encoding.UTF8, "application/json");

        // Act
        var response = await _client.PostAsync("/api/escrow", content);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Created);

        var responseContent = await response.Content.ReadAsStringAsync();
        var escrowAccountId = Guid.Parse(responseContent.Trim('"'));
        escrowAccountId.Should().NotBeEmpty();

        // Verify the escrow account was created in the database
        using var scope = _factory.Services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<FinancialPaymentDbContext>();
        var escrowAccount = await context.EscrowAccounts.FindAsync(escrowAccountId);

        escrowAccount.Should().NotBeNull();
        escrowAccount!.OrderId.Should().Be(command.OrderId);
        escrowAccount.TransportCompanyId.Should().Be(command.TransportCompanyId);
        escrowAccount.BrokerId.Should().Be(command.BrokerId);
        escrowAccount.CarrierId.Should().Be(command.CarrierId);
        escrowAccount.TotalAmount.Amount.Should().Be(10000m);
        escrowAccount.TotalAmount.Currency.Should().Be("INR");
        escrowAccount.Notes.Should().Be("Integration test escrow account");
    }

    [Fact]
    public async Task CreateEscrowAccount_ShouldReturnBadRequest_WhenDuplicateOrder()
    {
        // Arrange
        await _factory.ResetDatabaseAsync();

        var orderId = Guid.NewGuid();
        var command1 = new CreateEscrowAccountCommand
        {
            OrderId = orderId,
            TransportCompanyId = Guid.NewGuid(),
            BrokerId = Guid.NewGuid(),
            CarrierId = Guid.NewGuid(),
            TotalAmount = new CreateMoneyDto { Amount = 10000m, Currency = "INR" }
        };

        var command2 = new CreateEscrowAccountCommand
        {
            OrderId = orderId, // Same order ID
            TransportCompanyId = Guid.NewGuid(),
            BrokerId = Guid.NewGuid(),
            CarrierId = Guid.NewGuid(),
            TotalAmount = new CreateMoneyDto { Amount = 15000m, Currency = "INR" }
        };

        // Create first escrow account
        var json1 = JsonConvert.SerializeObject(command1);
        var content1 = new StringContent(json1, Encoding.UTF8, "application/json");
        var response1 = await _client.PostAsync("/api/escrow", content1);
        response1.StatusCode.Should().Be(HttpStatusCode.Created);

        // Act - Try to create second escrow account with same order ID
        var json2 = JsonConvert.SerializeObject(command2);
        var content2 = new StringContent(json2, Encoding.UTF8, "application/json");
        var response2 = await _client.PostAsync("/api/escrow", content2);

        // Assert
        response2.StatusCode.Should().Be(HttpStatusCode.BadRequest);

        var errorMessage = await response2.Content.ReadAsStringAsync();
        errorMessage.Should().Contain($"Escrow account already exists for order {orderId}");
    }

    [Fact]
    public async Task GetEscrowAccount_ShouldReturnEscrowAccount_WhenExists()
    {
        // Arrange
        await _factory.ResetDatabaseAsync();

        // First create an escrow account
        var command = new CreateEscrowAccountCommand
        {
            OrderId = Guid.NewGuid(),
            TransportCompanyId = Guid.NewGuid(),
            BrokerId = Guid.NewGuid(),
            CarrierId = Guid.NewGuid(),
            TotalAmount = new CreateMoneyDto { Amount = 25000m, Currency = "USD" },
            Notes = "Test escrow for retrieval"
        };

        var json = JsonConvert.SerializeObject(command);
        var content = new StringContent(json, Encoding.UTF8, "application/json");
        var createResponse = await _client.PostAsync("/api/escrow", content);
        createResponse.StatusCode.Should().Be(HttpStatusCode.Created);

        var escrowAccountIdString = await createResponse.Content.ReadAsStringAsync();
        var escrowAccountId = Guid.Parse(escrowAccountIdString.Trim('"'));

        // Act
        var getResponse = await _client.GetAsync($"/api/escrow/{escrowAccountId}");

        // Assert
        getResponse.StatusCode.Should().Be(HttpStatusCode.OK);

        var responseContent = await getResponse.Content.ReadAsStringAsync();
        var escrowAccountDto = JsonConvert.DeserializeObject<EscrowAccountDto>(responseContent);

        escrowAccountDto.Should().NotBeNull();
        escrowAccountDto!.Id.Should().Be(escrowAccountId);
        escrowAccountDto.OrderId.Should().Be(command.OrderId);
        escrowAccountDto.TransportCompanyId.Should().Be(command.TransportCompanyId);
        escrowAccountDto.BrokerId.Should().Be(command.BrokerId);
        escrowAccountDto.CarrierId.Should().Be(command.CarrierId);
        escrowAccountDto.TotalAmount.Should().Be(25000m);
        escrowAccountDto.TotalCurrency.Should().Be("USD");
        escrowAccountDto.AvailableAmount.Should().Be(0m);
        escrowAccountDto.ReservedAmount.Should().Be(0m);
        escrowAccountDto.Status.Should().Be("Created");
        escrowAccountDto.Notes.Should().Be("Test escrow for retrieval");
        escrowAccountDto.AccountNumber.Should().NotBeNullOrEmpty();
    }

    [Fact]
    public async Task GetEscrowAccount_ShouldReturnNotFound_WhenDoesNotExist()
    {
        // Arrange
        await _factory.ResetDatabaseAsync();
        var nonExistentId = Guid.NewGuid();

        // Act
        var response = await _client.GetAsync($"/api/escrow/{nonExistentId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);

        var errorMessage = await response.Content.ReadAsStringAsync();
        errorMessage.Should().Contain($"Escrow account {nonExistentId} not found");
    }

    [Fact]
    public async Task GetEscrowAccountByOrder_ShouldReturnEscrowAccount_WhenExists()
    {
        // Arrange
        await _factory.ResetDatabaseAsync();

        var orderId = Guid.NewGuid();
        var command = new CreateEscrowAccountCommand
        {
            OrderId = orderId,
            TransportCompanyId = Guid.NewGuid(),
            BrokerId = Guid.NewGuid(),
            CarrierId = Guid.NewGuid(),
            TotalAmount = new CreateMoneyDto { Amount = 12000m, Currency = "INR" }
        };

        var json = JsonConvert.SerializeObject(command);
        var content = new StringContent(json, Encoding.UTF8, "application/json");
        var createResponse = await _client.PostAsync("/api/escrow", content);
        createResponse.StatusCode.Should().Be(HttpStatusCode.Created);

        // Act
        var getResponse = await _client.GetAsync($"/api/escrow/order/{orderId}");

        // Assert
        getResponse.StatusCode.Should().Be(HttpStatusCode.OK);

        var responseContent = await getResponse.Content.ReadAsStringAsync();
        var escrowAccountDto = JsonConvert.DeserializeObject<EscrowAccountDto>(responseContent);

        escrowAccountDto.Should().NotBeNull();
        escrowAccountDto!.OrderId.Should().Be(orderId);
        escrowAccountDto.TotalAmount.Should().Be(12000m);
        escrowAccountDto.TotalCurrency.Should().Be("INR");
    }

    [Fact]
    public async Task GetEscrowAccountByOrder_ShouldReturnNotFound_WhenDoesNotExist()
    {
        // Arrange
        await _factory.ResetDatabaseAsync();
        var nonExistentOrderId = Guid.NewGuid();

        // Act
        var response = await _client.GetAsync($"/api/escrow/order/{nonExistentOrderId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);

        var errorMessage = await response.Content.ReadAsStringAsync();
        errorMessage.Should().Contain($"Escrow account for order {nonExistentOrderId} not found");
    }

    [Fact]
    public async Task CreateEscrowAccount_ShouldReturnBadRequest_WhenInvalidAmount()
    {
        // Arrange
        await _factory.ResetDatabaseAsync();

        var command = new CreateEscrowAccountCommand
        {
            OrderId = Guid.NewGuid(),
            TransportCompanyId = Guid.NewGuid(),
            BrokerId = Guid.NewGuid(),
            CarrierId = Guid.NewGuid(),
            TotalAmount = new CreateMoneyDto { Amount = 0m, Currency = "INR" } // Invalid amount
        };

        var json = JsonConvert.SerializeObject(command);
        var content = new StringContent(json, Encoding.UTF8, "application/json");

        // Act
        var response = await _client.PostAsync("/api/escrow", content);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task CreateEscrowAccount_ShouldReturnBadRequest_WhenMissingRequiredFields()
    {
        // Arrange
        await _factory.ResetDatabaseAsync();

        var invalidCommand = new
        {
            OrderId = Guid.NewGuid(),
            // Missing required fields
            TotalAmount = new { Amount = 10000m, Currency = "INR" }
        };

        var json = JsonConvert.SerializeObject(invalidCommand);
        var content = new StringContent(json, Encoding.UTF8, "application/json");

        // Act
        var response = await _client.PostAsync("/api/escrow", content);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }
}
