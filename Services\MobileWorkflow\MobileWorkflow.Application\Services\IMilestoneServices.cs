using MobileWorkflow.Application.DTOs;
using MobileWorkflow.Domain.Entities;

namespace MobileWorkflow.Application.Services;

public interface IMilestoneTemplateService
{
    Task<MilestoneTemplateDto> CreateTemplateAsync(CreateMilestoneTemplateRequest request, string createdBy, CancellationToken cancellationToken = default);
    Task<MilestoneTemplateDto> UpdateTemplateAsync(Guid id, UpdateMilestoneTemplateRequest request, string updatedBy, CancellationToken cancellationToken = default);
    Task<bool> DeleteTemplateAsync(Guid id, string deletedBy, CancellationToken cancellationToken = default);
    Task<MilestoneTemplateDto?> GetTemplateByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<MilestoneTemplateDto?> GetTemplateByNameAsync(string name, CancellationToken cancellationToken = default);
    Task<IEnumerable<MilestoneTemplateDto>> GetTemplatesByTypeAsync(string type, CancellationToken cancellationToken = default);
    Task<IEnumerable<MilestoneTemplateDto>> GetActiveTemplatesAsync(CancellationToken cancellationToken = default);
    Task<MilestoneTemplateDto?> GetDefaultTemplateByTypeAsync(string type, CancellationToken cancellationToken = default);
    Task<MilestoneTemplateDto> SetTemplateAsDefaultAsync(Guid id, string updatedBy, CancellationToken cancellationToken = default);
    Task<MilestoneTemplateDto> ActivateTemplateAsync(Guid id, string updatedBy, CancellationToken cancellationToken = default);
    Task<MilestoneTemplateDto> DeactivateTemplateAsync(Guid id, string updatedBy, CancellationToken cancellationToken = default);
    Task<MilestoneTemplateDto> CloneTemplateAsync(Guid sourceId, string newName, string createdBy, CancellationToken cancellationToken = default);
    Task<IEnumerable<MilestoneTemplatePreviewDto>> SearchTemplatesAsync(MilestoneTemplateSearchRequest request, CancellationToken cancellationToken = default);
    Task<BulkOperationResult> BulkOperationAsync(BulkMilestoneTemplateOperationRequest request, CancellationToken cancellationToken = default);
}

public interface IMilestonePayoutService
{
    Task<decimal> CalculateTotalPayoutPercentageAsync(Guid templateId, CancellationToken cancellationToken = default);
    Task<decimal> CalculateStepPayoutPercentageAsync(Guid stepId, CancellationToken cancellationToken = default);
    Task<bool> ValidatePayoutPercentagesAsync(Guid templateId, CancellationToken cancellationToken = default);
    Task<MilestonePayoutRuleDto> CreatePayoutRuleAsync(CreateMilestonePayoutRuleRequest request, CancellationToken cancellationToken = default);
    Task<MilestonePayoutRuleDto> UpdatePayoutRuleAsync(Guid id, UpdateMilestonePayoutRuleRequest request, CancellationToken cancellationToken = default);
    Task<bool> DeletePayoutRuleAsync(Guid id, CancellationToken cancellationToken = default);
    Task<IEnumerable<MilestonePayoutRuleDto>> GetPayoutRulesByStepAsync(Guid stepId, CancellationToken cancellationToken = default);
    Task<IEnumerable<MilestonePayoutRuleDto>> GetPayoutRulesByTemplateAsync(Guid templateId, CancellationToken cancellationToken = default);
    Task<bool> EvaluatePayoutTriggerAsync(Guid ruleId, Dictionary<string, object> context, CancellationToken cancellationToken = default);
    Task<IEnumerable<MilestonePayoutRuleDto>> GetTriggeredPayoutRulesAsync(Guid templateId, Dictionary<string, object> context, CancellationToken cancellationToken = default);
}

public interface ITemplateAssignmentService
{
    Task<RoleTemplateMappingDto> CreateRoleMappingAsync(CreateRoleTemplateMappingRequest request, CancellationToken cancellationToken = default);
    Task<RoleTemplateMappingDto> UpdateRoleMappingAsync(Guid id, UpdateRoleTemplateMappingRequest request, CancellationToken cancellationToken = default);
    Task<bool> DeleteRoleMappingAsync(Guid id, CancellationToken cancellationToken = default);
    Task<IEnumerable<RoleTemplateMappingDto>> GetMappingsByRoleAsync(string roleName, CancellationToken cancellationToken = default);
    Task<RoleTemplateMappingDto?> GetDefaultMappingByRoleAsync(string roleName, CancellationToken cancellationToken = default);
    Task<RoleTemplateMappingDto?> GetBestMappingAsync(string roleName, Dictionary<string, object> context, CancellationToken cancellationToken = default);
    Task<MilestoneTemplateDto?> GetAssignedTemplateAsync(string roleName, Dictionary<string, object> context, CancellationToken cancellationToken = default);
    Task<RoleTemplateMappingDto> SetMappingAsDefaultAsync(Guid id, string updatedBy, CancellationToken cancellationToken = default);
    Task<IEnumerable<string>> GetAvailableRolesAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<RoleTemplateMappingDto>> GetAllMappingsAsync(CancellationToken cancellationToken = default);
}

public interface IMilestoneValidationService
{
    Task<MilestoneTemplateValidationResult> ValidateTemplateAsync(Guid templateId, CancellationToken cancellationToken = default);
    Task<MilestoneTemplateValidationResult> ValidateTemplateAsync(MilestoneTemplate template, CancellationToken cancellationToken = default);
    Task<List<string>> ValidateStepSequenceAsync(Guid templateId, CancellationToken cancellationToken = default);
    Task<List<string>> ValidatePayoutRulesAsync(Guid templateId, CancellationToken cancellationToken = default);
    Task<List<string>> ValidateRoleMappingsAsync(Guid templateId, CancellationToken cancellationToken = default);
    Task<bool> CanTemplateBeDeletedAsync(Guid templateId, CancellationToken cancellationToken = default);
    Task<bool> CanStepBeDeletedAsync(Guid stepId, CancellationToken cancellationToken = default);
    Task<List<string>> GetTemplateUsageWarningsAsync(Guid templateId, CancellationToken cancellationToken = default);
    Task<Dictionary<string, object>> GetTemplateHealthCheckAsync(Guid templateId, CancellationToken cancellationToken = default);
}

public interface IMilestoneStepService
{
    Task<MilestoneStepDto> CreateStepAsync(CreateMilestoneStepRequest request, CancellationToken cancellationToken = default);
    Task<MilestoneStepDto> UpdateStepAsync(Guid id, UpdateMilestoneStepRequest request, CancellationToken cancellationToken = default);
    Task<bool> DeleteStepAsync(Guid id, CancellationToken cancellationToken = default);
    Task<MilestoneStepDto?> GetStepByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<IEnumerable<MilestoneStepDto>> GetStepsByTemplateAsync(Guid templateId, CancellationToken cancellationToken = default);
    Task<IEnumerable<MilestoneStepDto>> GetActiveStepsByTemplateAsync(Guid templateId, CancellationToken cancellationToken = default);
    Task<MilestoneStepDto> ActivateStepAsync(Guid id, CancellationToken cancellationToken = default);
    Task<MilestoneStepDto> DeactivateStepAsync(Guid id, CancellationToken cancellationToken = default);
    Task<bool> ReorderStepsAsync(Guid templateId, Dictionary<Guid, int> stepSequenceMap, CancellationToken cancellationToken = default);
    Task<int> GetNextSequenceNumberAsync(Guid templateId, CancellationToken cancellationToken = default);
}
