namespace MobileWorkflow.Application.DTOs;

public class GeofenceDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public Dictionary<string, object> Coordinates { get; set; } = new();
    public double? Radius { get; set; }
    public string TriggerType { get; set; } = string.Empty;
    public TimeSpan? DwellTime { get; set; }
    public Dictionary<string, object> TriggerActions { get; set; } = new();
    public Dictionary<string, object> Conditions { get; set; } = new();
    public List<string> TargetUserGroups { get; set; } = new();
    public List<Guid> TargetUserIds { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public string? UpdatedBy { get; set; }
}

public class GeofenceEventDto
{
    public Guid Id { get; set; }
    public Guid GeofenceId { get; set; }
    public Guid UserId { get; set; }
    public string EventType { get; set; } = string.Empty;
    public DateTime EventTime { get; set; }
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public double? Accuracy { get; set; }
    public double? Speed { get; set; }
    public double? Bearing { get; set; }
    public string? DeviceId { get; set; }
    public Dictionary<string, object> LocationContext { get; set; } = new();
    public Dictionary<string, object> TriggerData { get; set; } = new();
    public bool IsProcessed { get; set; }
    public DateTime? ProcessedAt { get; set; }
    public Dictionary<string, object> ProcessingResult { get; set; } = new();
}

public class UserGeofenceStatusDto
{
    public Guid Id { get; set; }
    public Guid GeofenceId { get; set; }
    public Guid UserId { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime StatusChangedAt { get; set; }
    public DateTime? EnteredAt { get; set; }
    public DateTime? ExitedAt { get; set; }
    public DateTime? DwellStartedAt { get; set; }
    public TimeSpan? TotalDwellTime { get; set; }
    public int EntryCount { get; set; }
    public double? LastLatitude { get; set; }
    public double? LastLongitude { get; set; }
    public DateTime? LastLocationUpdate { get; set; }
    public TimeSpan? CurrentDwellTime { get; set; }
    public TimeSpan? CurrentSessionTime { get; set; }
    public bool IsInside { get; set; }
    public bool IsOutside { get; set; }
    public bool IsDwelling { get; set; }
}

public class LocationUpdateDto
{
    public Guid Id { get; set; }
    public Guid UserId { get; set; }
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public double? Accuracy { get; set; }
    public double? Altitude { get; set; }
    public double? Speed { get; set; }
    public double? Bearing { get; set; }
    public DateTime Timestamp { get; set; }
    public string? DeviceId { get; set; }
    public string? Provider { get; set; }
    public Dictionary<string, object> LocationContext { get; set; } = new();
    public bool IsProcessed { get; set; }
    public DateTime? ProcessedAt { get; set; }
}

public class GeofenceAnalyticsDto
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public int TotalEvents { get; set; }
    public int EnterEvents { get; set; }
    public int ExitEvents { get; set; }
    public int DwellEvents { get; set; }
    public int UniqueUsers { get; set; }
    public int ActiveUsers { get; set; }
    public Dictionary<int, int> EventsByHour { get; set; } = new();
    public Dictionary<DateTime, int> EventsByDay { get; set; } = new();
    public double AverageDwellTime { get; set; }
    public Dictionary<Guid, int> MostActiveGeofences { get; set; } = new();
}

// Request DTOs
public class CreateGeofenceRequest
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty; // Circular, Polygon, Route
    public Dictionary<string, object> Coordinates { get; set; } = new();
    public double? Radius { get; set; }
    public string TriggerType { get; set; } = string.Empty; // Enter, Exit, Dwell, Both
    public TimeSpan? DwellTime { get; set; }
    public Dictionary<string, object>? TriggerActions { get; set; }
    public Dictionary<string, object>? Conditions { get; set; }
    public List<Guid>? TargetUserIds { get; set; }
    public List<string>? TargetUserGroups { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

public class UpdateGeofenceRequest
{
    public Dictionary<string, object>? Coordinates { get; set; }
    public double? Radius { get; set; }
    public string? TriggerType { get; set; }
    public TimeSpan? DwellTime { get; set; }
    public Dictionary<string, object>? TriggerActions { get; set; }
    public Dictionary<string, object>? Conditions { get; set; }
    public bool? IsActive { get; set; }
}

public class ProcessLocationUpdateRequest
{
    public Guid UserId { get; set; }
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public double? Accuracy { get; set; }
    public double? Altitude { get; set; }
    public double? Speed { get; set; }
    public double? Bearing { get; set; }
    public DateTime? Timestamp { get; set; }
    public string? DeviceId { get; set; }
    public string? Provider { get; set; }
    public Dictionary<string, object> LocationContext { get; set; } = new();
}

public class GeofenceTestDto
{
    public Guid GeofenceId { get; set; }
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public bool IsInside { get; set; }
    public double? DistanceToCenter { get; set; }
    public Dictionary<string, object> TestResults { get; set; } = new();
}

public class GeofencePerformanceDto
{
    public Guid GeofenceId { get; set; }
    public string GeofenceName { get; set; } = string.Empty;
    public int TotalEvents { get; set; }
    public int EnterEvents { get; set; }
    public int ExitEvents { get; set; }
    public int DwellEvents { get; set; }
    public int UniqueUsers { get; set; }
    public double AverageDwellTime { get; set; }
    public double MaxDwellTime { get; set; }
    public double MinDwellTime { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
}

public class GeofenceHeatmapDto
{
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public int EventCount { get; set; }
    public double Intensity { get; set; }
    public List<string> EventTypes { get; set; } = new();
}

public class GeofenceRouteDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public List<GeofenceWaypointDto> Waypoints { get; set; } = new();
    public double TotalDistance { get; set; }
    public TimeSpan EstimatedDuration { get; set; }
    public double ToleranceMeters { get; set; }
    public bool IsActive { get; set; }
}

public class GeofenceWaypointDto
{
    public int Order { get; set; }
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public string? Name { get; set; }
    public string? Description { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class GeofencePolygonDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public List<GeofencePointDto> Points { get; set; } = new();
    public double Area { get; set; }
    public double Perimeter { get; set; }
    public bool IsActive { get; set; }
}

public class GeofencePointDto
{
    public int Order { get; set; }
    public double Latitude { get; set; }
    public double Longitude { get; set; }
}

public class GeofenceCircularDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public double CenterLatitude { get; set; }
    public double CenterLongitude { get; set; }
    public double RadiusMeters { get; set; }
    public double Area { get; set; }
    public bool IsActive { get; set; }
}

public class GeofenceNotificationDto
{
    public Guid Id { get; set; }
    public Guid GeofenceId { get; set; }
    public string NotificationType { get; set; } = string.Empty; // Push, Email, SMS, Webhook
    public string Title { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public Dictionary<string, object> Configuration { get; set; } = new();
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
}

public class GeofenceWebhookDto
{
    public Guid Id { get; set; }
    public Guid GeofenceId { get; set; }
    public string Url { get; set; } = string.Empty;
    public string Method { get; set; } = "POST";
    public Dictionary<string, string> Headers { get; set; } = new();
    public string PayloadTemplate { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public int RetryCount { get; set; }
    public TimeSpan RetryDelay { get; set; }
    public DateTime CreatedAt { get; set; }
}

public class GeofenceScheduleDto
{
    public Guid Id { get; set; }
    public Guid GeofenceId { get; set; }
    public string ScheduleType { get; set; } = string.Empty; // Always, TimeRange, DaysOfWeek, DateRange
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public TimeSpan? StartTime { get; set; }
    public TimeSpan? EndTime { get; set; }
    public List<DayOfWeek> DaysOfWeek { get; set; } = new();
    public string TimeZone { get; set; } = string.Empty;
    public bool IsActive { get; set; }
}

public class GeofenceGroupDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<Guid> GeofenceIds { get; set; } = new();
    public Dictionary<string, object> GroupSettings { get; set; } = new();
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

public class GeofenceImportDto
{
    public string Name { get; set; } = string.Empty;
    public string Format { get; set; } = string.Empty; // GeoJSON, KML, CSV
    public string Data { get; set; } = string.Empty;
    public Dictionary<string, object> ImportOptions { get; set; } = new();
    public string CreatedBy { get; set; } = string.Empty;
}

public class GeofenceExportDto
{
    public List<Guid> GeofenceIds { get; set; } = new();
    public string Format { get; set; } = string.Empty; // GeoJSON, KML, CSV
    public Dictionary<string, object> ExportOptions { get; set; } = new();
    public bool IncludeEvents { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
}
