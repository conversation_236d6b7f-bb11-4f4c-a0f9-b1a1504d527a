using DataStorage.Domain.Enums;

namespace DataStorage.Domain.ValueObjects;

public class DocumentVersion
{
    public string VersionId { get; private set; }
    public Guid DocumentId { get; private set; }
    public string BranchName { get; private set; }
    public int VersionNumber { get; private set; }
    public string Title { get; private set; }
    public string? Description { get; private set; }
    public Guid CreatedBy { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public long SizeBytes { get; private set; }
    public string ContentHash { get; private set; }
    public VersionStatus Status { get; private set; }
    public List<string> Tags { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }
    public string? ParentVersionId { get; private set; }
    public List<string> ChildVersionIds { get; private set; }

    public DocumentVersion(
        string versionId,
        Guid documentId,
        string branchName,
        int versionNumber,
        string title,
        Guid createdBy,
        DateTime createdAt,
        long sizeBytes,
        string contentHash,
        VersionStatus status,
        string? description = null,
        List<string>? tags = null,
        Dictionary<string, object>? metadata = null,
        string? parentVersionId = null,
        List<string>? childVersionIds = null)
    {
        VersionId = versionId ?? throw new ArgumentNullException(nameof(versionId));
        DocumentId = documentId;
        BranchName = branchName ?? throw new ArgumentNullException(nameof(branchName));
        VersionNumber = versionNumber;
        Title = title ?? throw new ArgumentNullException(nameof(title));
        Description = description;
        CreatedBy = createdBy;
        CreatedAt = createdAt;
        SizeBytes = sizeBytes;
        ContentHash = contentHash ?? throw new ArgumentNullException(nameof(contentHash));
        Status = status;
        Tags = tags ?? new List<string>();
        Metadata = metadata ?? new Dictionary<string, object>();
        ParentVersionId = parentVersionId;
        ChildVersionIds = childVersionIds ?? new List<string>();
    }
}

public class VersionCreateRequest
{
    public string BranchName { get; private set; }
    public string Title { get; private set; }
    public string? Description { get; private set; }
    public Guid CreatedBy { get; private set; }
    public StorageLocation ContentLocation { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }
    public List<string> Tags { get; private set; }
    public bool CreateFromLatest { get; private set; }

    public VersionCreateRequest(
        string branchName,
        string title,
        Guid createdBy,
        StorageLocation contentLocation,
        string? description = null,
        Dictionary<string, object>? metadata = null,
        List<string>? tags = null,
        bool createFromLatest = true)
    {
        BranchName = branchName ?? throw new ArgumentNullException(nameof(branchName));
        Title = title ?? throw new ArgumentNullException(nameof(title));
        Description = description;
        CreatedBy = createdBy;
        ContentLocation = contentLocation ?? throw new ArgumentNullException(nameof(contentLocation));
        Metadata = metadata ?? new Dictionary<string, object>();
        Tags = tags ?? new List<string>();
        CreateFromLatest = createFromLatest;
    }
}

public class DocumentBranch
{
    public string Name { get; private set; }
    public Guid DocumentId { get; private set; }
    public string? Description { get; private set; }
    public Guid CreatedBy { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime LastModified { get; private set; }
    public string LatestVersionId { get; private set; }
    public int VersionCount { get; private set; }
    public BranchStatus Status { get; private set; }
    public string? ParentBranch { get; private set; }
    public List<string> ChildBranches { get; private set; }
    public BranchProtectionRules? ProtectionRules { get; private set; }

    public DocumentBranch(
        string name,
        Guid documentId,
        Guid createdBy,
        DateTime createdAt,
        string latestVersionId,
        int versionCount,
        BranchStatus status,
        string? description = null,
        DateTime lastModified = default,
        string? parentBranch = null,
        List<string>? childBranches = null,
        BranchProtectionRules? protectionRules = null)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        DocumentId = documentId;
        Description = description;
        CreatedBy = createdBy;
        CreatedAt = createdAt;
        LastModified = lastModified == default ? createdAt : lastModified;
        LatestVersionId = latestVersionId ?? throw new ArgumentNullException(nameof(latestVersionId));
        VersionCount = versionCount;
        Status = status;
        ParentBranch = parentBranch;
        ChildBranches = childBranches ?? new List<string>();
        ProtectionRules = protectionRules;
    }
}

public class BranchCreateRequest
{
    public string Name { get; private set; }
    public string? Description { get; private set; }
    public Guid CreatedBy { get; private set; }
    public string? SourceBranch { get; private set; }
    public string? SourceVersionId { get; private set; }
    public BranchProtectionRules? ProtectionRules { get; private set; }

    public BranchCreateRequest(
        string name,
        Guid createdBy,
        string? description = null,
        string? sourceBranch = null,
        string? sourceVersionId = null,
        BranchProtectionRules? protectionRules = null)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description;
        CreatedBy = createdBy;
        SourceBranch = sourceBranch;
        SourceVersionId = sourceVersionId;
        ProtectionRules = protectionRules;
    }
}

public class BranchProtectionRules
{
    public bool RequireReview { get; private set; }
    public int MinimumReviewers { get; private set; }
    public bool RequireStatusChecks { get; private set; }
    public List<Guid> AllowedMergers { get; private set; }
    public bool PreventForcePush { get; private set; }

    public BranchProtectionRules(
        bool requireReview = false,
        int minimumReviewers = 1,
        bool requireStatusChecks = false,
        List<Guid>? allowedMergers = null,
        bool preventForcePush = true)
    {
        RequireReview = requireReview;
        MinimumReviewers = Math.Max(1, minimumReviewers);
        RequireStatusChecks = requireStatusChecks;
        AllowedMergers = allowedMergers ?? new List<Guid>();
        PreventForcePush = preventForcePush;
    }
}

public class MergeRequest
{
    public string SourceBranch { get; private set; }
    public string TargetBranch { get; private set; }
    public string? Title { get; private set; }
    public string? Description { get; private set; }
    public Guid RequestedBy { get; private set; }
    public MergeStrategy Strategy { get; private set; }
    public bool DeleteSourceBranch { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    public MergeRequest(
        string sourceBranch,
        string targetBranch,
        Guid requestedBy,
        MergeStrategy strategy = MergeStrategy.Merge,
        string? title = null,
        string? description = null,
        bool deleteSourceBranch = false,
        Dictionary<string, object>? metadata = null)
    {
        SourceBranch = sourceBranch ?? throw new ArgumentNullException(nameof(sourceBranch));
        TargetBranch = targetBranch ?? throw new ArgumentNullException(nameof(targetBranch));
        Title = title;
        Description = description;
        RequestedBy = requestedBy;
        Strategy = strategy;
        DeleteSourceBranch = deleteSourceBranch;
        Metadata = metadata ?? new Dictionary<string, object>();
    }
}

public class MergeResult
{
    public bool IsSuccessful { get; private set; }
    public string? ErrorMessage { get; private set; }
    public string? MergeCommitId { get; private set; }
    public List<MergeConflict> Conflicts { get; private set; }
    public MergeStatistics Statistics { get; private set; }
    public DateTime MergedAt { get; private set; }
    public Guid MergedBy { get; private set; }

    private MergeResult()
    {
        Conflicts = new List<MergeConflict>();
        Statistics = new MergeStatistics();
    }

    public static MergeResult Success(
        string mergeCommitId,
        MergeStatistics statistics,
        Guid mergedBy)
    {
        return new MergeResult
        {
            IsSuccessful = true,
            MergeCommitId = mergeCommitId,
            Statistics = statistics,
            MergedAt = DateTime.UtcNow,
            MergedBy = mergedBy,
            Conflicts = new List<MergeConflict>()
        };
    }

    public static MergeResult Failure(string errorMessage, List<MergeConflict>? conflicts = null)
    {
        return new MergeResult
        {
            IsSuccessful = false,
            ErrorMessage = errorMessage,
            Conflicts = conflicts ?? new List<MergeConflict>(),
            Statistics = new MergeStatistics(),
            MergedAt = DateTime.UtcNow
        };
    }
}

public class MergeStatistics
{
    public int FilesChanged { get; private set; }
    public int LinesAdded { get; private set; }
    public int LinesDeleted { get; private set; }
    public int LinesModified { get; private set; }
    public int ConflictsResolved { get; private set; }

    public MergeStatistics(
        int filesChanged = 0,
        int linesAdded = 0,
        int linesDeleted = 0,
        int linesModified = 0,
        int conflictsResolved = 0)
    {
        FilesChanged = filesChanged;
        LinesAdded = linesAdded;
        LinesDeleted = linesDeleted;
        LinesModified = linesModified;
        ConflictsResolved = conflictsResolved;
    }
}
