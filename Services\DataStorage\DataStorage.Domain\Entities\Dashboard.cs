using System;
using System.Collections.Generic;
using Shared.Domain.Common;

namespace DataStorage.Domain.Entities
{
    public class Dashboard : BaseEntity
    {
        public string Name { get; private set; }
        public string Description { get; private set; }
        public string Layout { get; private set; } // JSON configuration
        public bool IsPublic { get; private set; }
        public Guid OwnerId { get; private set; }
        public string Category { get; private set; }
        public List<string> Tags { get; private set; }
        public int RefreshInterval { get; private set; } // in seconds
        public bool IsActive { get; private set; }
        public DateTime? LastViewedAt { get; private set; }
        public int ViewCount { get; private set; }

        private Dashboard()
        {
            Tags = new List<string>();
        }

        public Dashboard(
            string name,
            string description,
            string layout,
            Guid ownerId,
            string category = null,
            bool isPublic = false,
            int refreshInterval = 300)
        {
            Name = name ?? throw new ArgumentNullException(nameof(name));
            Description = description;
            Layout = layout ?? throw new ArgumentNullException(nameof(layout));
            OwnerId = ownerId;
            Category = category ?? "General";
            IsPublic = isPublic;
            RefreshInterval = refreshInterval;
            IsActive = true;
            ViewCount = 0;
            Tags = new List<string>();
            CreatedAt = DateTime.UtcNow;
        }

        public void UpdateLayout(string newLayout)
        {
            Layout = newLayout ?? throw new ArgumentNullException(nameof(newLayout));
            UpdatedAt = DateTime.UtcNow;
        }

        public void RecordView()
        {
            ViewCount++;
            LastViewedAt = DateTime.UtcNow;
            UpdatedAt = DateTime.UtcNow;
        }

        public void SetVisibility(bool isPublic)
        {
            IsPublic = isPublic;
            UpdatedAt = DateTime.UtcNow;
        }

        public void AddTag(string tag)
        {
            if (!string.IsNullOrWhiteSpace(tag) && !Tags.Contains(tag))
            {
                Tags.Add(tag);
                UpdatedAt = DateTime.UtcNow;
            }
        }

        public void RemoveTag(string tag)
        {
            if (Tags.Remove(tag))
            {
                UpdatedAt = DateTime.UtcNow;
            }
        }

        public void Activate()
        {
            IsActive = true;
            UpdatedAt = DateTime.UtcNow;
        }

        public void Deactivate()
        {
            IsActive = false;
            UpdatedAt = DateTime.UtcNow;
        }
    }
}
