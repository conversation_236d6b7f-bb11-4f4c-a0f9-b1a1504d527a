using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Domain.ValueObjects;
using System.Text.Json;

namespace FinancialPayment.Infrastructure.Services;

public class StripeGateway : IPaymentGateway
{
    private readonly StripeSettings _settings;
    private readonly ILogger<StripeGateway> _logger;
    private readonly HttpClient _httpClient;

    public string GatewayName => "Stripe";

    public StripeGateway(IOptions<StripeSettings> settings, ILogger<StripeGateway> logger, HttpClient httpClient)
    {
        _settings = settings.Value;
        _logger = logger;
        _httpClient = httpClient;
        
        // Configure HTTP client for Stripe API
        _httpClient.BaseAddress = new Uri("https://api.stripe.com/");
        _httpClient.DefaultRequestHeaders.Authorization = 
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _settings.SecretKey);
    }

    public async Task<PaymentResult> ProcessPaymentAsync(PaymentRequest request)
    {
        _logger.LogInformation("Processing payment via Stripe for user {UserId} with amount {Amount}",
            request.UserId, request.Amount);

        try
        {
            // Create payment intent
            var paymentIntentData = new
            {
                amount = (int)(request.Amount.Amount * 100), // Amount in cents
                currency = request.Amount.Currency.ToLowerInvariant(),
                payment_method = request.PaymentMethodId,
                confirmation_method = "manual",
                confirm = true,
                description = request.Description,
                metadata = request.Metadata
            };

            var content = new StringContent(JsonSerializer.Serialize(paymentIntentData), 
                System.Text.Encoding.UTF8, "application/json");

            // For demo purposes, simulate successful payment
            await Task.Delay(1000);
            var paymentIntentId = $"pi_{Guid.NewGuid():N}";

            _logger.LogInformation("Payment processed successfully via Stripe with payment intent ID {PaymentIntentId}", 
                paymentIntentId);

            return new PaymentResult
            {
                IsSuccess = true,
                TransactionId = paymentIntentId,
                ProcessedAt = DateTime.UtcNow,
                GatewayResponse = "Payment completed successfully via Stripe"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing payment via Stripe for user {UserId}", request.UserId);

            return new PaymentResult
            {
                IsSuccess = false,
                ErrorMessage = ex.Message,
                ProcessedAt = DateTime.UtcNow
            };
        }
    }

    public async Task<RefundResult> ProcessRefundAsync(RefundRequest request)
    {
        _logger.LogInformation("Processing refund via Stripe for transaction {TransactionId}", request.TransactionId);

        try
        {
            var refundData = new
            {
                payment_intent = request.TransactionId,
                amount = (int)(request.Amount.Amount * 100), // Amount in cents
                reason = request.Reason,
                metadata = request.Metadata
            };

            var content = new StringContent(JsonSerializer.Serialize(refundData), 
                System.Text.Encoding.UTF8, "application/json");

            // For demo purposes, simulate successful refund
            await Task.Delay(500);
            var refundId = $"re_{Guid.NewGuid():N}";

            _logger.LogInformation("Refund processed successfully via Stripe with refund ID {RefundId}", refundId);

            return new RefundResult
            {
                IsSuccess = true,
                RefundId = refundId,
                RefundAmount = request.Amount,
                ProcessedAt = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing refund via Stripe for transaction {TransactionId}", request.TransactionId);

            return new RefundResult
            {
                IsSuccess = false,
                ErrorMessage = ex.Message,
                ProcessedAt = DateTime.UtcNow
            };
        }
    }

    public async Task<PaymentStatus> GetPaymentStatusAsync(string transactionId)
    {
        try
        {
            // For demo purposes, simulate status check
            await Task.Delay(100);
            return PaymentStatus.Completed;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting payment status for transaction {TransactionId}", transactionId);
            return PaymentStatus.Failed;
        }
    }

    public async Task<bool> ValidateWebhookAsync(string payload, string signature)
    {
        try
        {
            // Stripe webhook validation logic would go here
            // For demo purposes, return true
            await Task.CompletedTask;
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating Stripe webhook");
            return false;
        }
    }

    public async Task<PaymentResult> CreatePaymentIntentAsync(PaymentIntentRequest request)
    {
        _logger.LogInformation("Creating payment intent via Stripe for user {UserId}", request.UserId);

        try
        {
            var paymentIntentData = new
            {
                amount = (int)(request.Amount.Amount * 100), // Amount in cents
                currency = request.Amount.Currency.ToLowerInvariant(),
                payment_method = request.PaymentMethodId,
                capture_method = request.AutoCapture ? "automatic" : "manual",
                description = request.Description,
                metadata = request.Metadata
            };

            var content = new StringContent(JsonSerializer.Serialize(paymentIntentData), 
                System.Text.Encoding.UTF8, "application/json");

            // For demo purposes, simulate successful intent creation
            await Task.Delay(200);
            var paymentIntentId = $"pi_{Guid.NewGuid():N}";

            return new PaymentResult
            {
                IsSuccess = true,
                TransactionId = paymentIntentId,
                ProcessedAt = DateTime.UtcNow,
                GatewayResponse = "Payment intent created successfully via Stripe"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating payment intent via Stripe for user {UserId}", request.UserId);

            return new PaymentResult
            {
                IsSuccess = false,
                ErrorMessage = ex.Message,
                ProcessedAt = DateTime.UtcNow
            };
        }
    }
}

public class StripeSettings
{
    public string PublishableKey { get; set; } = string.Empty;
    public string SecretKey { get; set; } = string.Empty;
    public string WebhookSecret { get; set; } = string.Empty;
}
