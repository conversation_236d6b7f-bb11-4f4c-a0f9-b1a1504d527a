using Shared.Domain.Common;
using MobileWorkflow.Domain.Events;

namespace MobileWorkflow.Domain.Entities;

public class DeviceCommand : AggregateRoot
{
    public Guid DeviceId { get; private set; }
    public string CommandType { get; private set; } // Lock, Wipe, Locate, UpdatePolicy, InstallApp, etc.
    public string CommandName { get; private set; }
    public string Status { get; private set; } // Pending, Sent, Acknowledged, Completed, Failed, Expired
    public Dictionary<string, object> Parameters { get; private set; }
    public Dictionary<string, object> Result { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? SentAt { get; private set; }
    public DateTime? AcknowledgedAt { get; private set; }
    public DateTime? CompletedAt { get; private set; }
    public DateTime? ExpiresAt { get; private set; }
    public string? ErrorMessage { get; private set; }
    public string? ErrorCode { get; private set; }
    public int RetryCount { get; private set; }
    public int MaxRetries { get; private set; }
    public string CreatedBy { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    public virtual ManagedDevice Device { get; private set; } = null!;

    private DeviceCommand() { } // EF Core

    public DeviceCommand(
        Guid deviceId,
        string commandType,
        string commandName,
        Dictionary<string, object> parameters,
        string createdBy,
        TimeSpan? expiresIn = null)
    {
        DeviceId = deviceId;
        CommandType = commandType ?? throw new ArgumentNullException(nameof(commandType));
        CommandName = commandName ?? throw new ArgumentNullException(nameof(commandName));
        Parameters = parameters ?? throw new ArgumentNullException(nameof(parameters));
        CreatedBy = createdBy ?? throw new ArgumentNullException(nameof(createdBy));

        Status = "Pending";
        CreatedAt = DateTime.UtcNow;
        ExpiresAt = expiresIn.HasValue ? DateTime.UtcNow.Add(expiresIn.Value) : DateTime.UtcNow.AddHours(24);
        RetryCount = 0;
        MaxRetries = GetDefaultMaxRetries(commandType);
        Result = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();

        AddDomainEvent(new DeviceCommandCreatedEvent(Id, DeviceId, CommandType, CommandName, CreatedBy));
    }

    public void MarkAsSent()
    {
        if (Status != "Pending")
            throw new InvalidOperationException($"Cannot mark as sent with status: {Status}");

        Status = "Sent";
        SentAt = DateTime.UtcNow;

        AddDomainEvent(new DeviceCommandSentEvent(Id, DeviceId, CommandType, CommandName));
    }

    public void MarkAsAcknowledged()
    {
        if (Status != "Sent")
            throw new InvalidOperationException($"Cannot mark as acknowledged with status: {Status}");

        Status = "Acknowledged";
        AcknowledgedAt = DateTime.UtcNow;

        AddDomainEvent(new DeviceCommandAcknowledgedEvent(Id, DeviceId, CommandType, CommandName));
    }

    public void MarkAsCompleted(Dictionary<string, object> result)
    {
        if (Status != "Acknowledged" && Status != "Sent")
            throw new InvalidOperationException($"Cannot mark as completed with status: {Status}");

        Status = "Completed";
        CompletedAt = DateTime.UtcNow;
        Result = result ?? throw new ArgumentNullException(nameof(result));

        AddDomainEvent(new DeviceCommandCompletedEvent(Id, DeviceId, CommandType, CommandName, result));
    }

    public void MarkAsFailed(string errorMessage, string? errorCode = null)
    {
        Status = "Failed";
        ErrorMessage = errorMessage;
        ErrorCode = errorCode;
        CompletedAt = DateTime.UtcNow;

        AddDomainEvent(new DeviceCommandFailedEvent(Id, DeviceId, CommandType, CommandName, errorMessage, errorCode));
    }

    public void MarkAsExpired()
    {
        if (Status == "Pending" || Status == "Sent")
        {
            Status = "Expired";
            CompletedAt = DateTime.UtcNow;

            AddDomainEvent(new DeviceCommandExpiredEvent(Id, DeviceId, CommandType, CommandName));
        }
    }

    public void Retry()
    {
        if (Status != "Failed" || RetryCount >= MaxRetries)
            throw new InvalidOperationException($"Cannot retry command with status: {Status} and retry count: {RetryCount}");

        RetryCount++;
        Status = "Pending";
        ErrorMessage = null;
        ErrorCode = null;

        AddDomainEvent(new DeviceCommandRetriedEvent(Id, DeviceId, CommandType, CommandName, RetryCount));
    }

    public bool IsExpired()
    {
        return ExpiresAt.HasValue && DateTime.UtcNow > ExpiresAt.Value;
    }

    public bool CanRetry()
    {
        return Status == "Failed" && RetryCount < MaxRetries && !IsExpired();
    }

    public bool IsCompleted()
    {
        return Status == "Completed" || Status == "Failed" || Status == "Expired";
    }

    public bool IsPending()
    {
        return Status == "Pending" || Status == "Sent" || Status == "Acknowledged";
    }

    public TimeSpan? GetExecutionTime()
    {
        if (SentAt.HasValue && CompletedAt.HasValue)
        {
            return CompletedAt.Value - SentAt.Value;
        }
        return null;
    }

    public TimeSpan GetAge()
    {
        return DateTime.UtcNow - CreatedAt;
    }

    public string GetPriority()
    {
        return CommandType.ToLowerInvariant() switch
        {
            "wipe" or "lock" or "locate" => "Critical",
            "updatepolicy" or "installapplication" => "High",
            "sync" or "inventory" => "Normal",
            _ => "Low"
        };
    }

    public bool RequiresUserInteraction()
    {
        return CommandType.ToLowerInvariant() switch
        {
            "wipe" or "lock" or "installapplication" => true,
            _ => false
        };
    }

    public Dictionary<string, object> GetCommandPayload()
    {
        var payload = new Dictionary<string, object>
        {
            ["commandId"] = Id,
            ["commandType"] = CommandType,
            ["commandName"] = CommandName,
            ["parameters"] = Parameters,
            ["createdAt"] = CreatedAt,
            ["expiresAt"] = ExpiresAt,
            ["priority"] = GetPriority(),
            ["requiresUserInteraction"] = RequiresUserInteraction()
        };

        return payload;
    }

    private int GetDefaultMaxRetries(string commandType)
    {
        return commandType.ToLowerInvariant() switch
        {
            "wipe" or "lock" => 5, // Critical commands get more retries
            "locate" or "updatepolicy" => 3,
            "sync" or "inventory" => 2,
            _ => 1
        };
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetParameter<T>(string key, T? defaultValue = default)
    {
        if (Parameters.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetResult<T>(string key, T? defaultValue = default)
    {
        if (Result.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public void UpdateParameters(Dictionary<string, object> newParameters)
    {
        if (Status != "Pending")
            throw new InvalidOperationException($"Cannot update parameters with status: {Status}");

        Parameters = newParameters ?? throw new ArgumentNullException(nameof(newParameters));
    }

    public void ExtendExpiration(TimeSpan extension)
    {
        if (IsCompleted())
            throw new InvalidOperationException("Cannot extend expiration of completed command");

        ExpiresAt = (ExpiresAt ?? DateTime.UtcNow).Add(extension);
    }
}

public class DevicePolicyAssignment : AggregateRoot
{
    public Guid DeviceId { get; private set; }
    public Guid PolicyId { get; private set; }
    public DateTime AssignedAt { get; private set; }
    public DateTime? LastAppliedAt { get; private set; }
    public string Status { get; private set; } // Pending, Applied, Failed
    public string? ErrorMessage { get; private set; }
    public string AssignedBy { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    public virtual ManagedDevice Device { get; private set; } = null!;
    public virtual DevicePolicy Policy { get; private set; } = null!;

    private DevicePolicyAssignment() { } // EF Core

    public DevicePolicyAssignment(
        Guid deviceId,
        Guid policyId,
        string assignedBy)
    {
        DeviceId = deviceId;
        PolicyId = policyId;
        AssignedBy = assignedBy ?? throw new ArgumentNullException(nameof(assignedBy));

        AssignedAt = DateTime.UtcNow;
        Status = "Pending";
        Metadata = new Dictionary<string, object>();

        AddDomainEvent(new DevicePolicyAssignmentCreatedEvent(Id, DeviceId, PolicyId, AssignedBy));
    }

    public void MarkAsApplied()
    {
        Status = "Applied";
        LastAppliedAt = DateTime.UtcNow;
        ErrorMessage = null;

        AddDomainEvent(new DevicePolicyAssignmentAppliedEvent(Id, DeviceId, PolicyId));
    }

    public void MarkAsFailed(string errorMessage)
    {
        Status = "Failed";
        ErrorMessage = errorMessage;

        AddDomainEvent(new DevicePolicyAssignmentFailedEvent(Id, DeviceId, PolicyId, errorMessage));
    }

    public void ResetToPending()
    {
        Status = "Pending";
        ErrorMessage = null;
    }

    public bool IsApplied()
    {
        return Status == "Applied";
    }

    public bool IsFailed()
    {
        return Status == "Failed";
    }

    public bool IsPending()
    {
        return Status == "Pending";
    }

    public TimeSpan? GetTimeSinceLastApplied()
    {
        return LastAppliedAt.HasValue ? DateTime.UtcNow - LastAppliedAt.Value : null;
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }
}

public class DeviceComplianceCheck : AggregateRoot
{
    public Guid DeviceId { get; private set; }
    public DateTime CheckedAt { get; private set; }
    public bool IsCompliant { get; private set; }
    public Dictionary<string, object> CheckResults { get; private set; }
    public List<string> Violations { get; private set; }
    public Dictionary<string, object> DeviceState { get; private set; }
    public string CheckType { get; private set; } // Manual, Scheduled, PolicyUpdate, Login
    public string? TriggeredBy { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    public virtual ManagedDevice Device { get; private set; } = null!;

    private DeviceComplianceCheck() { } // EF Core

    public DeviceComplianceCheck(
        Guid deviceId,
        bool isCompliant,
        Dictionary<string, object> checkResults,
        List<string> violations,
        Dictionary<string, object> deviceState,
        string checkType,
        string? triggeredBy = null)
    {
        DeviceId = deviceId;
        IsCompliant = isCompliant;
        CheckResults = checkResults ?? throw new ArgumentNullException(nameof(checkResults));
        Violations = violations ?? throw new ArgumentNullException(nameof(violations));
        DeviceState = deviceState ?? throw new ArgumentNullException(nameof(deviceState));
        CheckType = checkType ?? throw new ArgumentNullException(nameof(checkType));
        TriggeredBy = triggeredBy;

        CheckedAt = DateTime.UtcNow;
        Metadata = new Dictionary<string, object>();

        AddDomainEvent(new DeviceComplianceCheckCompletedEvent(Id, DeviceId, IsCompliant, violations.Count, CheckType));
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetCheckResult<T>(string key, T? defaultValue = default)
    {
        if (CheckResults.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetDeviceState<T>(string key, T? defaultValue = default)
    {
        if (DeviceState.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public bool HasViolation(string violationType)
    {
        return Violations.Any(v => v.Contains(violationType, StringComparison.OrdinalIgnoreCase));
    }

    public int GetViolationCount()
    {
        return Violations.Count;
    }

    public string GetComplianceScore()
    {
        if (IsCompliant)
            return "100%";

        var totalChecks = CheckResults.Count;
        var passedChecks = CheckResults.Count(r => r.Value is bool passed && passed);

        if (totalChecks == 0)
            return "0%";

        var score = (double)passedChecks / totalChecks * 100;
        return $"{score:F1}%";
    }
}


