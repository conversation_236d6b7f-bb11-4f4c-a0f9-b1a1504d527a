using MediatR;

namespace TripManagement.Application.Queries.GetMilestoneConfirmationRequirements;

public class GetMilestoneConfirmationRequirementsQuery : IRequest<GetMilestoneConfirmationRequirementsResponse>
{
    public Guid TripId { get; set; }
    public Guid? MilestoneId { get; set; }
    public Guid DriverId { get; set; }
    public string? LanguageCode { get; set; } = "en";
}

public class GetMilestoneConfirmationRequirementsResponse
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public MilestoneConfirmationRequirementsDto? Requirements { get; set; }
}

public class MilestoneConfirmationRequirementsDto
{
    public Guid TripId { get; set; }
    public Guid? MilestoneId { get; set; }
    public string TripNumber { get; set; } = string.Empty;
    public string? MilestoneName { get; set; }
    
    // Confirmation settings
    public bool RequireConfirmationPopup { get; set; }
    public bool RequireDoubleConfirmation { get; set; }
    public bool RequireReasonForCompletion { get; set; }
    public bool RequirePhotoEvidence { get; set; }
    public bool RequireLocationVerification { get; set; }
    public bool RequireSignature { get; set; }
    public bool RequireManagerApproval { get; set; }
    public bool AllowSkipWithReason { get; set; }
    
    // Sequence validation
    public bool EnableSequenceValidation { get; set; }
    public bool AllowOutOfSequenceCompletion { get; set; }
    public SequenceValidationInfo SequenceInfo { get; set; } = new();
    
    // UI configuration
    public int ConfirmationTimeoutSeconds { get; set; }
    public string ConfirmationMessage { get; set; } = string.Empty;
    public string WarningMessage { get; set; } = string.Empty;
    public List<string> RequiredFields { get; set; } = new();
    
    // Localized messages
    public Dictionary<string, string> LocalizedMessages { get; set; } = new();
    
    // Validation rules
    public Dictionary<string, object> CustomValidationRules { get; set; } = new();
    
    // Current state
    public bool CanProceed { get; set; }
    public List<string> BlockingIssues { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    
    public DateTime RetrievedAt { get; set; } = DateTime.UtcNow;
}

public class SequenceValidationInfo
{
    public int CurrentSequenceNumber { get; set; }
    public int TotalMilestones { get; set; }
    public bool IsFirstMilestone { get; set; }
    public bool IsLastMilestone { get; set; }
    public List<PendingMilestoneDto> PendingPreviousMilestones { get; set; } = new();
    public List<CompletedMilestoneDto> CompletedMilestones { get; set; } = new();
    public double CompletionPercentage { get; set; }
    public bool HasSequenceViolations { get; set; }
}

public class PendingMilestoneDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public int SequenceNumber { get; set; }
    public bool IsRequired { get; set; }
    public DateTime? DueDate { get; set; }
    public bool IsOverdue { get; set; }
}

public class CompletedMilestoneDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public int SequenceNumber { get; set; }
    public DateTime CompletedAt { get; set; }
    public string CompletedBy { get; set; } = string.Empty;
}
