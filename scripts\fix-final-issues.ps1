# Fix final compilation issues
Write-Host "Fixing final compilation issues..." -ForegroundColor Green

# Fix remaining test project package versions
Write-Host "Fixing remaining test project package versions..." -ForegroundColor Yellow
$testProjects = @(
    "Services\UserManagement\UserManagement.Tests\UserManagement.Tests.csproj",
    "Services\SubscriptionManagement\SubscriptionManagement.Tests\SubscriptionManagement.Tests.csproj",
    "Services\OrderManagement\OrderManagement.Tests\OrderManagement.Tests.csproj",
    "Services\TripManagement\TripManagement.Tests\TripManagement.Tests.csproj",
    "Services\NetworkFleetManagement\NetworkFleetManagement.Tests\NetworkFleetManagement.Tests.csproj",
    "Services\FinancialPayment\FinancialPayment.Tests\FinancialPayment.Tests.csproj",
    "Services\CommunicationNotification\CommunicationNotification.Tests\CommunicationNotification.Tests.csproj",
    "Services\AnalyticsBIService\AnalyticsBIService.Tests\AnalyticsBIService.Tests.csproj",
    "Services\DataStorage\DataStorage.Tests\DataStorage.Tests.csproj",
    "Services\MonitoringObservability\MonitoringObservability.Tests\MonitoringObservability.Tests.csproj",
    "Services\AuditCompliance\AuditCompliance.Tests\AuditCompliance.Tests.csproj",
    "Services\MobileWorkflow\MobileWorkflow.Tests\MobileWorkflow.Tests.csproj",
    "Services\ContentManagement\ContentManagement.Tests\ContentManagement.Tests.csproj"
)

foreach ($projectPath in $testProjects) {
    if (Test-Path $projectPath) {
        Write-Host "  Updating $projectPath..." -ForegroundColor Cyan
        $content = Get-Content $projectPath -Raw
        
        # Update EntityFrameworkCore versions to 8.0.11
        $content = $content -replace 'Microsoft\.EntityFrameworkCore" Version="[^"]*"', 'Microsoft.EntityFrameworkCore" Version="8.0.11"'
        $content = $content -replace 'Microsoft\.EntityFrameworkCore\.InMemory" Version="[^"]*"', 'Microsoft.EntityFrameworkCore.InMemory" Version="8.0.11"'
        $content = $content -replace 'Microsoft\.EntityFrameworkCore\.SqlServer" Version="[^"]*"', 'Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.11"'
        $content = $content -replace 'Microsoft\.EntityFrameworkCore\.Tools" Version="[^"]*"', 'Microsoft.EntityFrameworkCore.Tools" Version="8.0.11"'
        
        Set-Content -Path $projectPath -Value $content -Encoding UTF8
        Write-Host "    Updated $projectPath" -ForegroundColor Green
    }
}

# Create missing IBaseEntity interface in Shared.Domain
Write-Host "Creating missing IBaseEntity interface..." -ForegroundColor Yellow
$ibaseEntityFile = "Shared\Shared.Domain\Common\IBaseEntity.cs"
if (-not (Test-Path $ibaseEntityFile)) {
    $ibaseEntityContent = @"
using System;

namespace Shared.Domain.Common
{
    public interface IBaseEntity<TKey>
    {
        TKey Id { get; set; }
        DateTime CreatedAt { get; set; }
        DateTime? UpdatedAt { get; set; }
    }
}
"@
    Set-Content -Path $ibaseEntityFile -Value $ibaseEntityContent -Encoding UTF8
    Write-Host "  Created IBaseEntity interface" -ForegroundColor Green
}

# Fix Shared.Infrastructure OptimizedRepositoryBase
Write-Host "Fixing OptimizedRepositoryBase..." -ForegroundColor Yellow
$optimizedRepoFile = "Shared\Shared.Infrastructure\Repositories\OptimizedRepositoryBase.cs"
if (Test-Path $optimizedRepoFile) {
    $content = Get-Content $optimizedRepoFile -Raw
    
    # Fix the interface constraint
    $content = $content -replace "where TEntity : class, IBaseEntity<TKey>", "where TEntity : class, IBaseEntity<TKey>"
    $content = $content -replace "IBaseEntity<", "IBaseEntity<"
    
    Set-Content -Path $optimizedRepoFile -Value $content -Encoding UTF8
    Write-Host "  Fixed OptimizedRepositoryBase" -ForegroundColor Green
}

# Fix UserManagement command handler syntax errors
Write-Host "Fixing UserManagement command handler syntax errors..." -ForegroundColor Yellow
$logoHandlerFile = "Services\UserManagement\UserManagement.Application\Commands\TransportCompanyLogo\UploadTransportCompanyLogoCommandHandler.cs"
if (Test-Path $logoHandlerFile) {
    $content = Get-Content $logoHandlerFile -Raw
    
    # Fix catch blocks with missing exception variable
    $content = $content -replace "catch\s*\(\s*Exception\s+ex\s*\)\s*\{([^}]*)\}", "catch (Exception ex) { `$1 }"
    $content = $content -replace "catch\s*\(\s*\)\s*\{", "catch (Exception ex) {"
    
    Set-Content -Path $logoHandlerFile -Value $content -Encoding UTF8
    Write-Host "  Fixed UploadTransportCompanyLogoCommandHandler syntax" -ForegroundColor Green
}

$ocrHandlerFile = "Services\UserManagement\UserManagement.Application\Commands\TransportCompanyOCR\ProcessTransportCompanyDocumentWithOCRCommandHandler.cs"
if (Test-Path $ocrHandlerFile) {
    $content = Get-Content $ocrHandlerFile -Raw
    
    # Fix catch blocks with missing exception variable
    $content = $content -replace "catch\s*\(\s*Exception\s+ex\s*\)\s*\{([^}]*)\}", "catch (Exception ex) { `$1 }"
    $content = $content -replace "catch\s*\(\s*\)\s*\{", "catch (Exception ex) {"
    
    Set-Content -Path $ocrHandlerFile -Value $content -Encoding UTF8
    Write-Host "  Fixed ProcessTransportCompanyDocumentWithOCRCommandHandler syntax" -ForegroundColor Green
}

# Remove duplicate using statements
Write-Host "Removing duplicate using statements..." -ForegroundColor Yellow
$domainFiles = Get-ChildItem -Path "Services" -Recurse -Filter "*.cs" | Where-Object { $_.FullName -match "\.Domain\\" }

foreach ($file in $domainFiles) {
    $content = Get-Content $file.FullName -Raw
    $lines = $content -split "`n"
    $uniqueLines = @()
    $seenUsings = @{}
    
    foreach ($line in $lines) {
        if ($line -match "^using\s+([^;]+);") {
            $usingStatement = $matches[1]
            if (-not $seenUsings.ContainsKey($usingStatement)) {
                $seenUsings[$usingStatement] = $true
                $uniqueLines += $line
            }
        } else {
            $uniqueLines += $line
        }
    }
    
    $newContent = $uniqueLines -join "`n"
    if ($newContent -ne $content) {
        Set-Content -Path $file.FullName -Value $newContent -Encoding UTF8
    }
}

Write-Host "Final issues fixes completed!" -ForegroundColor Green
