using AutoMapper;
using MediatR;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Entities;
using NetworkFleetManagement.Domain.Repositories;
using NetworkFleetManagement.Domain.ValueObjects;
using Shared.Domain.Common;
using Shared.Messaging;

namespace NetworkFleetManagement.Application.Commands.Vehicles;

public class CreateVehicleCommandHandler : IRequestHandler<CreateVehicleCommand, VehicleDto>
{
    private readonly IVehicleRepository _vehicleRepository;
    private readonly ICarrierRepository _carrierRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly IMessageBroker _messageBroker;

    public CreateVehicleCommandHandler(
        IVehicleRepository vehicleRepository,
        ICarrierRepository carrierRepository,
        IUnitOfWork unitOfWork,
        IMapper mapper,
        IMessageBroker messageBroker)
    {
        _vehicleRepository = vehicleRepository;
        _carrierRepository = carrierRepository;
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _messageBroker = messageBroker;
    }

    public async Task<VehicleDto> Handle(CreateVehicleCommand request, CancellationToken cancellationToken)
    {
        var dto = request.VehicleDto;

        // Check if carrier exists
        var carrier = await _carrierRepository.GetByIdAsync(dto.CarrierId, cancellationToken);
        if (carrier == null)
        {
            throw new InvalidOperationException($"Carrier with ID {dto.CarrierId} not found");
        }

        // Check if vehicle with same registration number already exists
        var existingVehicle = await _vehicleRepository.GetByRegistrationNumberAsync(dto.RegistrationNumber, cancellationToken);
        if (existingVehicle != null)
        {
            throw new InvalidOperationException($"Vehicle with registration number {dto.RegistrationNumber} already exists");
        }

        // Create vehicle specifications
        var specifications = new VehicleSpecifications(
            dto.Specifications.LoadCapacityKg,
            dto.Specifications.VolumeCapacityM3,
            dto.Specifications.FuelCapacityLiters,
            dto.Specifications.MaxSpeed,
            dto.Specifications.Length,
            dto.Specifications.Width,
            dto.Specifications.Height,
            dto.Specifications.EngineType,
            dto.Specifications.FuelType);

        // Create vehicle entity
        var vehicle = new Vehicle(
            dto.CarrierId,
            dto.RegistrationNumber,
            dto.VehicleType,
            dto.Make,
            dto.Model,
            dto.Year,
            dto.Color,
            specifications,
            dto.InsuranceNumber,
            dto.InsuranceExpiryDate,
            dto.FitnessNumber,
            dto.FitnessExpiryDate,
            dto.PermitNumber,
            dto.PermitExpiryDate,
            dto.Notes);

        // Add to repository
        _vehicleRepository.Add(vehicle);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        // Publish integration event
        await _messageBroker.PublishAsync("vehicle.created", new
        {
            VehicleId = vehicle.Id,
            CarrierId = vehicle.CarrierId,
            RegistrationNumber = vehicle.RegistrationNumber,
            VehicleType = vehicle.VehicleType.ToString(),
            Make = vehicle.Make,
            Model = vehicle.Model,
            Year = vehicle.Year,
            Status = vehicle.Status.ToString(),
            CreatedAt = vehicle.CreatedAt
        }, cancellationToken);

        // Map to DTO and return
        var result = _mapper.Map<VehicleDto>(vehicle);
        return result;
    }
}

