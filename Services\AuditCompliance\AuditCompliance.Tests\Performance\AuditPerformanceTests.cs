using Xunit;
using FluentAssertions;
using System.Diagnostics;
using AuditCompliance.Domain.Entities;
using AuditCompliance.Domain.Enums;
using AuditCompliance.Application.Interfaces;

namespace AuditCompliance.Tests.Performance;

[Trait("Category", TestCategories.Performance)]
public class AuditPerformanceTests : TestBase
{
    private readonly IAuditLogRepository _auditLogRepository;

    public AuditPerformanceTests()
    {
        _auditLogRepository = GetService<IAuditLogRepository>();
    }

    [Fact]
    public async Task CreateAuditLogs_BulkInsert_ShouldCompleteWithinTimeLimit()
    {
        // Arrange
        const int numberOfLogs = 1000;
        const int maxExecutionTimeMs = 5000; // 5 seconds

        var auditLogs = new List<AuditLog>();
        for (int i = 0; i < numberOfLogs; i++)
        {
            auditLogs.Add(TestDataBuilder.CreateAuditLog(
                eventType: AuditEventType.UserLogin,
                description: $"Test audit log {i}",
                userId: Guid.NewGuid(),
                userName: $"user{i}"));
        }

        // Act
        var stopwatch = Stopwatch.StartNew();
        await _auditLogRepository.AddRangeAsync(auditLogs);
        stopwatch.Stop();

        // Assert
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(maxExecutionTimeMs, 
            $"Bulk insert of {numberOfLogs} audit logs should complete within {maxExecutionTimeMs}ms");
        
        // Verify all logs were inserted
        var insertedLogs = await _auditLogRepository.GetAuditTrailAsync(pageSize: numberOfLogs);
        insertedLogs.Should().HaveCount(numberOfLogs);
    }

    [Fact]
    public async Task GetAuditTrail_LargeDataset_ShouldCompleteWithinTimeLimit()
    {
        // Arrange
        const int numberOfLogs = 5000;
        const int maxExecutionTimeMs = 2000; // 2 seconds
        const int pageSize = 100;

        // Seed large dataset
        await SeedLargeAuditDataset(numberOfLogs);

        // Act
        var stopwatch = Stopwatch.StartNew();
        var result = await _auditLogRepository.GetAuditTrailAsync(
            pageNumber: 1,
            pageSize: pageSize);
        stopwatch.Stop();

        // Assert
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(maxExecutionTimeMs,
            $"Querying audit trail with {numberOfLogs} records should complete within {maxExecutionTimeMs}ms");
        
        result.Should().HaveCount(pageSize);
    }

    [Fact]
    public async Task GetAuditTrail_WithFilters_ShouldCompleteWithinTimeLimit()
    {
        // Arrange
        const int numberOfLogs = 2000;
        const int maxExecutionTimeMs = 1500; // 1.5 seconds
        
        await SeedLargeAuditDataset(numberOfLogs);

        // Act
        var stopwatch = Stopwatch.StartNew();
        var result = await _auditLogRepository.GetAuditTrailAsync(
            eventType: AuditEventType.UserLogin,
            minSeverity: AuditSeverity.Info,
            fromDate: DateTime.UtcNow.AddDays(-1),
            toDate: DateTime.UtcNow,
            pageSize: 50);
        stopwatch.Stop();

        // Assert
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(maxExecutionTimeMs,
            $"Filtered audit trail query should complete within {maxExecutionTimeMs}ms");
        
        result.Should().NotBeEmpty();
    }

    [Fact]
    public async Task GetSecurityEvents_LargeDataset_ShouldCompleteWithinTimeLimit()
    {
        // Arrange
        const int numberOfLogs = 3000;
        const int maxExecutionTimeMs = 1000; // 1 second
        
        await SeedSecurityEventDataset(numberOfLogs);

        // Act
        var stopwatch = Stopwatch.StartNew();
        var result = await _auditLogRepository.GetSecurityEventsAsync(
            DateTime.UtcNow.AddDays(-1),
            DateTime.UtcNow);
        stopwatch.Stop();

        // Assert
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(maxExecutionTimeMs,
            $"Security events query should complete within {maxExecutionTimeMs}ms");
        
        result.Should().NotBeEmpty();
    }

    [Fact]
    public async Task ConcurrentAuditLogCreation_ShouldHandleMultipleThreads()
    {
        // Arrange
        const int numberOfThreads = 10;
        const int logsPerThread = 100;
        const int maxExecutionTimeMs = 10000; // 10 seconds

        var tasks = new List<Task>();

        // Act
        var stopwatch = Stopwatch.StartNew();
        
        for (int i = 0; i < numberOfThreads; i++)
        {
            int threadId = i;
            tasks.Add(Task.Run(async () =>
            {
                var logs = new List<AuditLog>();
                for (int j = 0; j < logsPerThread; j++)
                {
                    logs.Add(TestDataBuilder.CreateAuditLog(
                        description: $"Thread {threadId} - Log {j}",
                        userId: Guid.NewGuid()));
                }
                await _auditLogRepository.AddRangeAsync(logs);
            }));
        }

        await Task.WhenAll(tasks);
        stopwatch.Stop();

        // Assert
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(maxExecutionTimeMs,
            $"Concurrent audit log creation should complete within {maxExecutionTimeMs}ms");

        // Verify all logs were created
        var totalLogs = await _auditLogRepository.GetAuditTrailAsync(pageSize: numberOfThreads * logsPerThread);
        totalLogs.Should().HaveCount(numberOfThreads * logsPerThread);
    }

    [Theory]
    [InlineData(100, 500)]   // 100 logs should complete in 500ms
    [InlineData(500, 1000)]  // 500 logs should complete in 1s
    [InlineData(1000, 2000)] // 1000 logs should complete in 2s
    public async Task AuditLogCreation_ScalabilityTest_ShouldMeetPerformanceTargets(int numberOfLogs, int maxTimeMs)
    {
        // Arrange
        var auditLogs = new List<AuditLog>();
        for (int i = 0; i < numberOfLogs; i++)
        {
            auditLogs.Add(TestDataBuilder.CreateAuditLog(
                description: $"Scalability test log {i}"));
        }

        // Act
        var stopwatch = Stopwatch.StartNew();
        await _auditLogRepository.AddRangeAsync(auditLogs);
        stopwatch.Stop();

        // Assert
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(maxTimeMs,
            $"Creating {numberOfLogs} audit logs should complete within {maxTimeMs}ms");
    }

    private async Task SeedLargeAuditDataset(int count)
    {
        var auditLogs = new List<AuditLog>();
        var eventTypes = Enum.GetValues<AuditEventType>();
        var severities = Enum.GetValues<AuditSeverity>();

        for (int i = 0; i < count; i++)
        {
            var eventType = eventTypes[i % eventTypes.Length];
            var severity = severities[i % severities.Length];
            
            auditLogs.Add(TestDataBuilder.CreateAuditLog(
                eventType: eventType,
                severity: severity,
                description: $"Performance test log {i}",
                userId: Guid.NewGuid(),
                userName: $"perfuser{i % 100}")); // Simulate 100 different users
        }

        await _auditLogRepository.AddRangeAsync(auditLogs);
    }

    private async Task SeedSecurityEventDataset(int count)
    {
        var securityEventTypes = new[]
        {
            AuditEventType.UnauthorizedAccess,
            AuditEventType.SecurityBreach,
            AuditEventType.SuspiciousActivity,
            AuditEventType.UserLogin,
            AuditEventType.UserLogout
        };

        var auditLogs = new List<AuditLog>();

        for (int i = 0; i < count; i++)
        {
            var eventType = securityEventTypes[i % securityEventTypes.Length];
            var severity = eventType == AuditEventType.SecurityBreach ? AuditSeverity.Critical : AuditSeverity.High;
            
            auditLogs.Add(TestDataBuilder.CreateAuditLog(
                eventType: eventType,
                severity: severity,
                description: $"Security event {i}",
                userId: Guid.NewGuid()));
        }

        await _auditLogRepository.AddRangeAsync(auditLogs);
    }
}
