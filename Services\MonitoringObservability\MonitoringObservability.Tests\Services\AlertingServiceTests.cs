using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using MonitoringObservability.Domain.Entities;
using MonitoringObservability.Domain.Enums;
using MonitoringObservability.Domain.Interfaces;
using MonitoringObservability.Infrastructure.Services;

namespace MonitoringObservability.Tests.Services;

/// <summary>
/// Unit tests for AlertingService following Clean Architecture patterns
/// </summary>
public class AlertingServiceTests
{
    private readonly Mock<IAlertRepository> _mockAlertRepository;
    private readonly Mock<IMetricRepository> _mockMetricRepository;
    private readonly Mock<INotificationService> _mockNotificationService;
    private readonly Mock<ILogger<AlertingService>> _mockLogger;
    private readonly AlertingService _alertingService;

    public AlertingServiceTests()
    {
        _mockAlertRepository = new Mock<IAlertRepository>();
        _mockMetricRepository = new Mock<IMetricRepository>();
        _mockNotificationService = new Mock<INotificationService>();
        _mockLogger = new Mock<ILogger<AlertingService>>();
        
        _alertingService = new AlertingService(
            _mockAlertRepository.Object,
            _mockMetricRepository.Object,
            _mockNotificationService.Object,
            _mockLogger.Object);
    }

    [Fact]
    public async Task CreateAlertAsync_ValidInput_CreatesAlert()
    {
        // Arrange
        var title = "Test Alert";
        var description = "Test Description";
        var severity = AlertSeverity.High;
        var source = "TestService";
        var serviceName = "TestService";
        var metricName = "cpu_usage";
        var value = 85.0;
        var threshold = new AlertThreshold(metricName, value, 80.0, "GreaterThan", TimeSpan.FromMinutes(5));

        _mockAlertRepository.Setup(x => x.AddAsync(It.IsAny<Alert>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _alertingService.CreateAlertAsync(title, description, severity, source, 
            serviceName, metricName, value, threshold);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(title, result.Title);
        Assert.Equal(description, result.Description);
        Assert.Equal(severity, result.Severity);
        Assert.Equal(AlertStatus.Open, result.Status);
        
        _mockAlertRepository.Verify(x => x.AddAsync(It.IsAny<Alert>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task CreateAlertAsync_NullTitle_ThrowsArgumentNullException()
    {
        // Arrange
        var threshold = new AlertThreshold("cpu_usage", 85.0, 80.0, "GreaterThan", TimeSpan.FromMinutes(5));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() =>
            _alertingService.CreateAlertAsync(null!, "description", AlertSeverity.High, 
                "source", "service", "metric", 85.0, threshold));
    }

    [Fact]
    public async Task ResolveAlertAsync_ExistingAlert_ResolvesAlert()
    {
        // Arrange
        var alertId = Guid.NewGuid();
        var resolvedBy = Guid.NewGuid();
        var resolutionNotes = "Fixed the issue";
        
        var alert = new Alert("Test Alert", "Description", AlertSeverity.High, "Source", 
            "Service", "metric", 85.0, 
            new AlertThreshold("metric", 85.0, 80.0, "GreaterThan", TimeSpan.FromMinutes(5)));

        _mockAlertRepository.Setup(x => x.GetByIdAsync(alertId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(alert);
        _mockAlertRepository.Setup(x => x.UpdateAsync(It.IsAny<Alert>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        await _alertingService.ResolveAlertAsync(alertId, resolvedBy, resolutionNotes);

        // Assert
        Assert.Equal(AlertStatus.Resolved, alert.Status);
        Assert.Equal(resolvedBy, alert.ResolvedBy);
        Assert.Equal(resolutionNotes, alert.ResolutionNotes);
        Assert.NotNull(alert.ResolvedAt);
        
        _mockAlertRepository.Verify(x => x.UpdateAsync(It.IsAny<Alert>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task ResolveAlertAsync_NonExistentAlert_ThrowsInvalidOperationException()
    {
        // Arrange
        var alertId = Guid.NewGuid();
        var resolvedBy = Guid.NewGuid();
        
        _mockAlertRepository.Setup(x => x.GetByIdAsync(alertId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((Alert?)null);

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _alertingService.ResolveAlertAsync(alertId, resolvedBy, "notes"));
    }

    [Fact]
    public async Task GetActiveAlertsAsync_ReturnsActiveAlerts()
    {
        // Arrange
        var activeAlerts = new List<Alert>
        {
            new Alert("Alert 1", "Description 1", AlertSeverity.High, "Source", "Service", "metric", 85.0,
                new AlertThreshold("metric", 85.0, 80.0, "GreaterThan", TimeSpan.FromMinutes(5))),
            new Alert("Alert 2", "Description 2", AlertSeverity.Medium, "Source", "Service", "metric", 75.0,
                new AlertThreshold("metric", 75.0, 70.0, "GreaterThan", TimeSpan.FromMinutes(5)))
        };

        _mockAlertRepository.Setup(x => x.GetActiveAlertsAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(activeAlerts);

        // Act
        var result = await _alertingService.GetActiveAlertsAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count());
        Assert.All(result, alert => Assert.Equal(AlertStatus.Open, alert.Status));
    }

    [Theory]
    [InlineData(AlertSeverity.Critical, NotificationChannel.SMS)]
    [InlineData(AlertSeverity.High, NotificationChannel.Email)]
    [InlineData(AlertSeverity.Medium, NotificationChannel.Email)]
    [InlineData(AlertSeverity.Low, NotificationChannel.InApp)]
    public async Task CreateAlertAsync_DifferentSeverities_UsesCorrectNotificationChannel(
        AlertSeverity severity, NotificationChannel expectedChannel)
    {
        // Arrange
        var threshold = new AlertThreshold("cpu_usage", 85.0, 80.0, "GreaterThan", TimeSpan.FromMinutes(5));
        
        _mockAlertRepository.Setup(x => x.AddAsync(It.IsAny<Alert>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);
        _mockNotificationService.Setup(x => x.SendNotificationAsync(
            It.IsAny<NotificationChannel>(), It.IsAny<string>(), It.IsAny<string>(), 
            It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        await _alertingService.CreateAlertAsync("Test Alert", "Description", severity, 
            "Source", "Service", "metric", 85.0, threshold);

        // Assert
        _mockNotificationService.Verify(x => x.SendNotificationAsync(
            expectedChannel, It.IsAny<string>(), It.IsAny<string>(), 
            It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task EvaluateAlertRulesAsync_WithMatchingConditions_CreatesAlert()
    {
        // Arrange
        var serviceName = "TestService";
        var metrics = new List<Metric>
        {
            new Metric("cpu_usage", 85.0, serviceName, DateTime.UtcNow, new Dictionary<string, string>())
        };

        var alertRule = new AlertRule("High CPU", "CPU usage is high", AlertSeverity.High, serviceName);
        var condition = new AlertCondition("cpu_usage", AlertOperator.GreaterThan, 80.0, TimeSpan.FromMinutes(5));
        alertRule.AddCondition(condition);

        var alertRules = new List<AlertRule> { alertRule };

        _mockMetricRepository.Setup(x => x.GetMetricsByServiceAsync(serviceName, 
            It.IsAny<DateTime>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(metrics);
        _mockAlertRepository.Setup(x => x.AddAsync(It.IsAny<Alert>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        await _alertingService.EvaluateAlertRulesAsync(alertRules, serviceName);

        // Assert
        _mockAlertRepository.Verify(x => x.AddAsync(It.IsAny<Alert>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public void AlertThreshold_ValidParameters_CreatesThreshold()
    {
        // Arrange
        var metricName = "cpu_usage";
        var actualValue = 85.0;
        var thresholdValue = 80.0;
        var comparisonOperator = "GreaterThan";
        var timeWindow = TimeSpan.FromMinutes(5);

        // Act
        var threshold = new AlertThreshold(metricName, actualValue, thresholdValue, comparisonOperator, timeWindow);

        // Assert
        Assert.Equal(metricName, threshold.MetricName);
        Assert.Equal(actualValue, threshold.ActualValue);
        Assert.Equal(thresholdValue, threshold.ThresholdValue);
        Assert.Equal(comparisonOperator, threshold.ComparisonOperator);
        Assert.Equal(timeWindow, threshold.TimeWindow);
        Assert.True(threshold.IsBreached);
    }

    [Fact]
    public void Alert_AddTag_AddsTagSuccessfully()
    {
        // Arrange
        var alert = new Alert("Test Alert", "Description", AlertSeverity.High, "Source", 
            "Service", "metric", 85.0,
            new AlertThreshold("metric", 85.0, 80.0, "GreaterThan", TimeSpan.FromMinutes(5)));

        // Act
        alert.AddTag("environment", "production");
        alert.AddTag("team", "platform");

        // Assert
        Assert.Equal("production", alert.Tags["environment"]);
        Assert.Equal("platform", alert.Tags["team"]);
        Assert.Equal(2, alert.Tags.Count);
    }

    [Fact]
    public void Alert_Acknowledge_UpdatesStatusAndMetadata()
    {
        // Arrange
        var alert = new Alert("Test Alert", "Description", AlertSeverity.High, "Source", 
            "Service", "metric", 85.0,
            new AlertThreshold("metric", 85.0, 80.0, "GreaterThan", TimeSpan.FromMinutes(5)));
        var acknowledgedBy = Guid.NewGuid();
        var notes = "Investigating the issue";

        // Act
        alert.Acknowledge(acknowledgedBy, notes);

        // Assert
        Assert.Equal(AlertStatus.Acknowledged, alert.Status);
        Assert.Equal(acknowledgedBy, alert.Metadata["acknowledged_by"]);
        Assert.Equal(notes, alert.Metadata["acknowledgment_notes"]);
        Assert.True(alert.Metadata.ContainsKey("acknowledged_at"));
    }

    [Fact]
    public void AlertRule_AddCondition_AddsConditionSuccessfully()
    {
        // Arrange
        var alertRule = new AlertRule("Test Rule", "Description", AlertSeverity.High, "TestService");
        var condition = new AlertCondition("cpu_usage", AlertOperator.GreaterThan, 80.0, TimeSpan.FromMinutes(5));

        // Act
        alertRule.AddCondition(condition);

        // Assert
        Assert.Single(alertRule.Conditions);
        Assert.Equal(condition, alertRule.Conditions.First());
    }

    [Fact]
    public void AlertRule_EvaluateConditions_WithMatchingMetrics_ReturnsTrue()
    {
        // Arrange
        var alertRule = new AlertRule("Test Rule", "Description", AlertSeverity.High, "TestService");
        var condition = new AlertCondition("cpu_usage", AlertOperator.GreaterThan, 80.0, TimeSpan.FromMinutes(5));
        alertRule.AddCondition(condition);

        var metrics = new List<Metric>
        {
            new Metric("cpu_usage", 85.0, "TestService", DateTime.UtcNow, new Dictionary<string, string>())
        };

        // Act
        var result = alertRule.EvaluateConditions(metrics);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public void AlertRule_EvaluateConditions_WithNonMatchingMetrics_ReturnsFalse()
    {
        // Arrange
        var alertRule = new AlertRule("Test Rule", "Description", AlertSeverity.High, "TestService");
        var condition = new AlertCondition("cpu_usage", AlertOperator.GreaterThan, 80.0, TimeSpan.FromMinutes(5));
        alertRule.AddCondition(condition);

        var metrics = new List<Metric>
        {
            new Metric("cpu_usage", 75.0, "TestService", DateTime.UtcNow, new Dictionary<string, string>())
        };

        // Act
        var result = alertRule.EvaluateConditions(metrics);

        // Assert
        Assert.False(result);
    }
}
