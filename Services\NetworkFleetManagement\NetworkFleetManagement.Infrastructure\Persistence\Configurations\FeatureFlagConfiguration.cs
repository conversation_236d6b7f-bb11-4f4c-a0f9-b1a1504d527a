using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetworkFleetManagement.Domain.Entities;
using System.Text.Json;

namespace NetworkFleetManagement.Infrastructure.Persistence.Configurations;

public class FeatureFlagConfiguration : IEntityTypeConfiguration<FeatureFlag>
{
    public void Configure(EntityTypeBuilder<FeatureFlag> builder)
    {
        builder.ToTable("feature_flags");

        builder.HasKey(f => f.Id);

        builder.Property(f => f.Key)
            .IsRequired()
            .HasMaxLength(100);

        builder.HasIndex(f => f.Key)
            .IsUnique();

        builder.Property(f => f.Name)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(f => f.Description)
            .HasMaxLength(1000);

        builder.Property(f => f.IsEnabled)
            .IsRequired()
            .HasDefaultValue(false);

        builder.Property(f => f.IsArchived)
            .IsRequired()
            .HasDefaultValue(false);

        builder.Property(f => f.RolloutPercentage)
            .HasDefaultValue(0.0);

        builder.Property(f => f.Environment)
            .IsRequired()
            .HasMaxLength(50)
            .HasDefaultValue("production");

        builder.Property(f => f.Tags)
            .HasMaxLength(500);

        builder.Property(f => f.ExpiresAt);

        builder.Property(f => f.LastUsedAt);

        builder.Property(f => f.TotalUsageCount)
            .HasDefaultValue(0);

        // JSON columns for complex data
        builder.Property(f => f.WhitelistedUsers)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<List<Guid>>(v, (JsonSerializerOptions?)null) ?? new List<Guid>())
            .HasColumnType("jsonb");

        builder.Property(f => f.BlacklistedUsers)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<List<Guid>>(v, (JsonSerializerOptions?)null) ?? new List<Guid>())
            .HasColumnType("jsonb");

        builder.Property(f => f.VariantWeights)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<Dictionary<string, double>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, double>())
            .HasColumnType("jsonb");

        builder.Property(f => f.VariantConfigurations)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        builder.Property(f => f.TargetingRules)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        builder.Property(f => f.UsageHistory)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<List<FeatureFlagUsage>>(v, (JsonSerializerOptions?)null) ?? new List<FeatureFlagUsage>())
            .HasColumnType("jsonb");

        // Indexes for performance
        builder.HasIndex(f => f.IsEnabled);
        builder.HasIndex(f => f.IsArchived);
        builder.HasIndex(f => f.Environment);
        builder.HasIndex(f => f.ExpiresAt);
        builder.HasIndex(f => f.LastUsedAt);

        // Audit fields
        builder.Property(f => f.CreatedAt)
            .IsRequired()
            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        builder.Property(f => f.UpdatedAt)
            .IsRequired()
            .HasDefaultValueSql("CURRENT_TIMESTAMP");
    }
}
