using CommunicationNotification.Domain.Enums;
using System.ComponentModel.DataAnnotations;

namespace CommunicationNotification.Infrastructure.Models;

/// <summary>
/// Request model for sending a notification
/// </summary>
public class SendNotificationRequest
{
    /// <summary>
    /// Target user ID
    /// </summary>
    [Required]
    public Guid UserId { get; set; }

    /// <summary>
    /// Notification content
    /// </summary>
    [Required]
    [StringLength(4000, MinimumLength = 1)]
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Message type
    /// </summary>
    [Required]
    public MessageType MessageType { get; set; }

    /// <summary>
    /// Notification priority
    /// </summary>
    public NotificationPriority Priority { get; set; } = NotificationPriority.Normal;

    /// <summary>
    /// Preferred communication channel
    /// </summary>
    public NotificationChannel? PreferredChannel { get; set; }

    /// <summary>
    /// Preferred language
    /// </summary>
    public Language PreferredLanguage { get; set; } = Language.English;

    /// <summary>
    /// Message subject (for email/formal notifications)
    /// </summary>
    [StringLength(200)]
    public string? Subject { get; set; }

    /// <summary>
    /// Template parameters for dynamic content
    /// </summary>
    public Dictionary<string, object>? Parameters { get; set; }

    /// <summary>
    /// Template ID for template-based notifications
    /// </summary>
    public string? TemplateId { get; set; }

    /// <summary>
    /// Whether delivery confirmation is required
    /// </summary>
    public bool RequireDeliveryConfirmation { get; set; } = false;

    /// <summary>
    /// Scheduled delivery time (optional)
    /// </summary>
    public DateTime? ScheduledAt { get; set; }

    /// <summary>
    /// Tags for categorization and filtering
    /// </summary>
    public List<string>? Tags { get; set; }

    /// <summary>
    /// Custom headers for advanced scenarios
    /// </summary>
    public Dictionary<string, string>? CustomHeaders { get; set; }

    /// <summary>
    /// Correlation ID for tracking
    /// </summary>
    public string? CorrelationId { get; set; }
}

/// <summary>
/// Request model for sending bulk notifications
/// </summary>
public class SendBulkNotificationRequest
{
    /// <summary>
    /// Target user IDs
    /// </summary>
    [Required]
    [MinLength(1)]
    [MaxLength(10000)]
    public List<Guid> UserIds { get; set; } = new();

    /// <summary>
    /// Notification content
    /// </summary>
    [Required]
    [StringLength(4000, MinimumLength = 1)]
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Message type
    /// </summary>
    [Required]
    public MessageType MessageType { get; set; }

    /// <summary>
    /// Notification priority
    /// </summary>
    public NotificationPriority Priority { get; set; } = NotificationPriority.Normal;

    /// <summary>
    /// Preferred communication channel
    /// </summary>
    public NotificationChannel? PreferredChannel { get; set; }

    /// <summary>
    /// Preferred language
    /// </summary>
    public Language PreferredLanguage { get; set; } = Language.English;

    /// <summary>
    /// Message subject
    /// </summary>
    [StringLength(200)]
    public string? Subject { get; set; }

    /// <summary>
    /// Template parameters
    /// </summary>
    public Dictionary<string, object>? Parameters { get; set; }

    /// <summary>
    /// Template ID
    /// </summary>
    public string? TemplateId { get; set; }

    /// <summary>
    /// Whether delivery confirmation is required
    /// </summary>
    public bool RequireDeliveryConfirmation { get; set; } = false;

    /// <summary>
    /// Scheduled delivery time
    /// </summary>
    public DateTime? ScheduledAt { get; set; }

    /// <summary>
    /// Tags for categorization
    /// </summary>
    public List<string>? Tags { get; set; }

    /// <summary>
    /// Batch size for processing
    /// </summary>
    [Range(1, 1000)]
    public int BatchSize { get; set; } = 100;

    /// <summary>
    /// Delay between batches in seconds
    /// </summary>
    [Range(0, 300)]
    public int BatchDelaySeconds { get; set; } = 5;

    /// <summary>
    /// Correlation ID for tracking
    /// </summary>
    public string? CorrelationId { get; set; }
}

/// <summary>
/// Request model for sending template-based notifications
/// </summary>
public class SendTemplateNotificationRequest
{
    /// <summary>
    /// Target user ID
    /// </summary>
    [Required]
    public Guid UserId { get; set; }

    /// <summary>
    /// Template ID
    /// </summary>
    [Required]
    [StringLength(100, MinimumLength = 1)]
    public string TemplateId { get; set; } = string.Empty;

    /// <summary>
    /// Template parameters
    /// </summary>
    [Required]
    public Dictionary<string, object> TemplateParameters { get; set; } = new();

    /// <summary>
    /// Message type
    /// </summary>
    [Required]
    public MessageType MessageType { get; set; }

    /// <summary>
    /// Notification priority
    /// </summary>
    public NotificationPriority Priority { get; set; } = NotificationPriority.Normal;

    /// <summary>
    /// Preferred communication channel
    /// </summary>
    public NotificationChannel? PreferredChannel { get; set; }

    /// <summary>
    /// Preferred language
    /// </summary>
    public Language PreferredLanguage { get; set; } = Language.English;

    /// <summary>
    /// Whether delivery confirmation is required
    /// </summary>
    public bool RequireDeliveryConfirmation { get; set; } = false;

    /// <summary>
    /// Scheduled delivery time
    /// </summary>
    public DateTime? ScheduledAt { get; set; }

    /// <summary>
    /// Tags for categorization
    /// </summary>
    public List<string>? Tags { get; set; }

    /// <summary>
    /// Correlation ID for tracking
    /// </summary>
    public string? CorrelationId { get; set; }
}

/// <summary>
/// Request model for sending emergency notifications
/// </summary>
public class SendEmergencyNotificationRequest
{
    /// <summary>
    /// Target user ID
    /// </summary>
    [Required]
    public Guid UserId { get; set; }

    /// <summary>
    /// Emergency notification content
    /// </summary>
    [Required]
    [StringLength(1000, MinimumLength = 1)]
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Type of emergency
    /// </summary>
    [Required]
    [StringLength(50)]
    public string EmergencyType { get; set; } = string.Empty;

    /// <summary>
    /// Location information
    /// </summary>
    [StringLength(500)]
    public string? Location { get; set; }

    /// <summary>
    /// Additional emergency data
    /// </summary>
    public Dictionary<string, object>? EmergencyData { get; set; }

    /// <summary>
    /// Whether acknowledgment is required
    /// </summary>
    public bool RequireAcknowledgment { get; set; } = true;

    /// <summary>
    /// Acknowledgment timeout in minutes
    /// </summary>
    [Range(1, 60)]
    public int AcknowledgmentTimeoutMinutes { get; set; } = 5;

    /// <summary>
    /// Escalation channels if no acknowledgment
    /// </summary>
    public List<NotificationChannel>? EscalationChannels { get; set; }

    /// <summary>
    /// Escalation user IDs if no acknowledgment
    /// </summary>
    public List<Guid>? EscalationUserIds { get; set; }

    /// <summary>
    /// Correlation ID for tracking
    /// </summary>
    public string? CorrelationId { get; set; }
}

/// <summary>
/// Response model for notification send operations
/// </summary>
public class NotificationSendResponse
{
    /// <summary>
    /// Notification ID
    /// </summary>
    public Guid NotificationId { get; set; }

    /// <summary>
    /// Current status
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// Channel used for delivery
    /// </summary>
    public string ChannelUsed { get; set; } = string.Empty;

    /// <summary>
    /// Sent timestamp
    /// </summary>
    public DateTime SentAt { get; set; }

    /// <summary>
    /// External provider message ID
    /// </summary>
    public string? ExternalId { get; set; }

    /// <summary>
    /// Estimated delivery time
    /// </summary>
    public TimeSpan EstimatedDeliveryTime { get; set; }

    /// <summary>
    /// Command ID for tracking
    /// </summary>
    public Guid CommandId { get; set; }

    /// <summary>
    /// Template ID (if template-based)
    /// </summary>
    public string? TemplateId { get; set; }

    /// <summary>
    /// Whether this is an emergency notification
    /// </summary>
    public bool IsEmergency { get; set; } = false;
}

/// <summary>
/// Response model for bulk notification operations
/// </summary>
public class BulkNotificationSendResponse
{
    /// <summary>
    /// Bulk operation ID
    /// </summary>
    public Guid BulkOperationId { get; set; }

    /// <summary>
    /// Total notifications to send
    /// </summary>
    public int TotalNotifications { get; set; }

    /// <summary>
    /// Successfully sent notifications
    /// </summary>
    public int SuccessfulNotifications { get; set; }

    /// <summary>
    /// Failed notifications
    /// </summary>
    public int FailedNotifications { get; set; }

    /// <summary>
    /// Operation start time
    /// </summary>
    public DateTime StartedAt { get; set; }

    /// <summary>
    /// Operation completion time
    /// </summary>
    public DateTime? CompletedAt { get; set; }

    /// <summary>
    /// Success rate percentage
    /// </summary>
    public double SuccessRate { get; set; }

    /// <summary>
    /// Command ID for tracking
    /// </summary>
    public Guid CommandId { get; set; }
}

/// <summary>
/// Response model for notification status queries
/// </summary>
public class NotificationStatusResponse
{
    /// <summary>
    /// Notification ID
    /// </summary>
    public Guid NotificationId { get; set; }

    /// <summary>
    /// Current status
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// Delivery channel
    /// </summary>
    public string Channel { get; set; } = string.Empty;

    /// <summary>
    /// Sent timestamp
    /// </summary>
    public DateTime SentAt { get; set; }

    /// <summary>
    /// Delivered timestamp
    /// </summary>
    public DateTime? DeliveredAt { get; set; }

    /// <summary>
    /// Read timestamp
    /// </summary>
    public DateTime? ReadAt { get; set; }

    /// <summary>
    /// External provider message ID
    /// </summary>
    public string? ExternalId { get; set; }

    /// <summary>
    /// Error message if failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Delivery attempts
    /// </summary>
    public List<DeliveryAttemptResponse> DeliveryAttempts { get; set; } = new();
}

/// <summary>
/// Response model for delivery attempts
/// </summary>
public class DeliveryAttemptResponse
{
    /// <summary>
    /// Attempt number
    /// </summary>
    public int AttemptNumber { get; set; }

    /// <summary>
    /// Channel used for attempt
    /// </summary>
    public string Channel { get; set; } = string.Empty;

    /// <summary>
    /// Attempt timestamp
    /// </summary>
    public DateTime AttemptedAt { get; set; }

    /// <summary>
    /// Whether attempt was successful
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// Error message if failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Response time
    /// </summary>
    public TimeSpan? ResponseTime { get; set; }
}
