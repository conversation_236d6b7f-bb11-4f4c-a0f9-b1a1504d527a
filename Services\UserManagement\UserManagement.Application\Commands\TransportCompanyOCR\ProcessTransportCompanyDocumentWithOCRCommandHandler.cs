using UserManagement.Application.Interfaces;
using Shared.Domain.Common;
using UserManagement.Domain.Entities;
using UserManagement.Domain.Repositories;
using Microsoft.Extensions.Logging;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Primitives;

namespace UserManagement.Application.Commands.TransportCompanyOCR;

/// <summary>
/// Handler for processing Transport Company documents with OCR
/// </summary>
public class ProcessTransportCompanyDocumentWithOCRCommandHandler :
    IRequestHandler<ProcessTransportCompanyDocumentWithOCRCommand, ProcessTransportCompanyDocumentWithOCRResult>
{
    private readonly IOCRService _ocrService;
    private readonly IDocumentValidationService _documentValidationService;
    private readonly IFileStorageService _fileStorageService;
    private readonly IDocumentSubmissionRepository _documentSubmissionRepository;
    private readonly ITransportCompanyRepository _transportCompanyRepository;
    private readonly ILogger<ProcessTransportCompanyDocumentWithOCRCommandHandler> _logger;

    public ProcessTransportCompanyDocumentWithOCRCommandHandler(
        IOCRService ocrService,
        IDocumentValidationService documentValidationService,
        IFileStorageService fileStorageService,
        IDocumentSubmissionRepository documentSubmissionRepository,
        ITransportCompanyRepository transportCompanyRepository,
        ILogger<ProcessTransportCompanyDocumentWithOCRCommandHandler> logger)
    {
        _ocrService = ocrService;
        _documentValidationService = documentValidationService;
        _fileStorageService = fileStorageService;
        _documentSubmissionRepository = documentSubmissionRepository;
        _transportCompanyRepository = transportCompanyRepository;
        _logger = logger;
    }

    public async Task<ProcessTransportCompanyDocumentWithOCRResult> Handle(
        ProcessTransportCompanyDocumentWithOCRCommand request,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing document with OCR for Transport Company {TransportCompanyId}, DocumentType: {DocumentType}",
            request.TransportCompanyId, request.DocumentType);

        try
        {
            // Validate request
            var validationResult = await ValidateRequest(request, cancellationToken);
            if (!validationResult.IsValid)
            {
                return new ProcessTransportCompanyDocumentWithOCRResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Request validation failed",
                    ValidationErrors = validationResult.Errors,
                    ProcessedAt = DateTime.UtcNow
                };
            }

            // Upload file to temporary storage
            var tempFilePath = await UploadToTempStorage(request, cancellationToken);

            // Process document with OCR
            var ocrResult = await ProcessDocumentWithOCR(request, tempFilePath, cancellationToken);

            // Validate extracted data
            var documentValidationResult = await ValidateExtractedData(request, ocrResult, cancellationToken);

            // Generate preview data
            var previewData = await GeneratePreviewData(request, ocrResult, documentValidationResult, cancellationToken);

            // Generate suggestions
            var suggestions = GenerateOCRSuggestions(ocrResult, documentValidationResult);

            var result = new ProcessTransportCompanyDocumentWithOCRResult
            {
                IsSuccess = true,
                ExtractionResult = ocrResult,
                ValidationResult = documentValidationResult,
                PreviewData = previewData,
                Suggestions = suggestions,
                ProcessedAt = DateTime.UtcNow,
                ProcessingMetadata = new Dictionary<string, object>
                {
                    ["OCRProvider"] = ocrResult.OCRProvider,
                    ["ProcessingTime"] = ocrResult.ProcessingTime.TotalMilliseconds,
                    ["OverallConfidence"] = ocrResult.OverallConfidence,
                    ["ValidationScore"] = documentValidationResult.ValidationScore,
                    ["QualityScore"] = ocrResult.QualityMetrics.ImageQuality
                }
            };

            if (request.IsPreviewMode)
            {
                // Generate preview token for confirmation
                result.PreviewToken = GeneratePreviewToken(request.TransportCompanyId, request.DocumentType);

                // Store preview data temporarily
                await StorePreviewData(result.PreviewToken, result, cancellationToken);
            }
            else
            {
                // Directly save the document if not in preview mode
                var documentId = await SaveProcessedDocument(request, ocrResult, documentValidationResult, cancellationToken);
                result.DocumentId = documentId;
            }

            _logger.LogInformation("Successfully processed document with OCR for Transport Company {TransportCompanyId}. Confidence: {Confidence}%",
                request.TransportCompanyId, ocrResult.OverallConfidence * 100);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing document with OCR for Transport Company {TransportCompanyId }",
                request.TransportCompanyId);

            return new ProcessTransportCompanyDocumentWithOCRResult
            {
                IsSuccess = false,
                ErrorMessage = ex.Message,
                ProcessedAt = DateTime.UtcNow
            };
        }
    }

    private async Task<ValidationResult> ValidateRequest(
        ProcessTransportCompanyDocumentWithOCRCommand request,
        CancellationToken cancellationToken)
    {
        var errors = new List<string>();

        // Validate transport company exists
        var transportCompany = await _transportCompanyRepository.GetByIdAsync(request.TransportCompanyId);
        if (transportCompany == null)
            errors.Add("Transport company not found");

        // Validate file content
        if (request.FileContent.Length == 0)
            errors.Add("File content is required");

        if (request.FileSize > 10 * 1024 * 1024) // 10MB limit
            errors.Add("File size cannot exceed 10MB");

        // Validate content type
        var allowedContentTypes = new[] { "image/jpeg", "image/png", "image/tiff", "application/pdf" };
        if (!allowedContentTypes.Contains(request.ContentType.ToLower()))
            errors.Add("Unsupported file type. Supported types: JPEG, PNG, TIFF, PDF");

        // Validate document type for transport company
        var allowedDocumentTypes = GetAllowedDocumentTypes();
        if (!allowedDocumentTypes.Contains(request.DocumentType))
            errors.Add($"Document type {request.DocumentType} is not allowed for transport companies");

        return new ValidationResult
        {
            IsValid = !errors.Any(),
            Errors = errors
        };
    }

    private async Task<string> UploadToTempStorage(
        ProcessTransportCompanyDocumentWithOCRCommand request,
        CancellationToken cancellationToken)
    {
        var tempFileName = $"temp_ocr_{Guid.NewGuid()}_{request.FileName}";
        using var stream = new MemoryStream(request.FileContent);

        // Create IFormFile from stream for the interface
        var formFile = new FormFileWrapper(stream, tempFileName, request.ContentType);
        var uploadedPath = await _fileStorageService.UploadFileAsync(formFile, "temp");
        return uploadedPath;
    }

    private async Task<OCRExtractionResult> ProcessDocumentWithOCR(
        ProcessTransportCompanyDocumentWithOCRCommand request,
        string filePath,
        CancellationToken cancellationToken)
    {
        var startTime = DateTime.UtcNow;

        // Configure OCR based on document type
        var ocrConfig = GetOCRConfiguration(request.DocumentType);

        // Process with OCR service
        var ocrData = await _ocrService.ExtractDataAsync(filePath, request.DocumentType.ToString(), cancellationToken);

        // Extract specific fields based on document type
        var extractedFields = ExtractDocumentSpecificFields(request.DocumentType, ocrData);

        // Analyze document quality
        var qualityMetrics = await AnalyzeDocumentQualityAsync(filePath, ocrData.ExtractedData, cancellationToken);

        return new OCRExtractionResult
        {
            OverallConfidence = (decimal)ocrData.Confidence,
            ExtractedFields = extractedFields,
            TextRegions = new List<OCRTextRegion>(), // Will be populated by actual OCR service
            QualityMetrics = qualityMetrics,
            OCRProvider = "DefaultOCRProvider", // Will be set by actual OCR service
            ProcessingTime = DateTime.UtcNow - startTime,
            RawOCRData = ocrData.ExtractedData.ToDictionary(kvp => kvp.Key, kvp => (object)kvp.Value)
        };
    }

    private async Task<DocumentValidationResult> ValidateExtractedData(
        ProcessTransportCompanyDocumentWithOCRCommand request,
        OCRExtractionResult ocrResult,
        CancellationToken cancellationToken)
    {
        var validationConfig = GetValidationConfiguration(request.DocumentType);

        // Convert OCRFieldResult dictionary to string dictionary
        var extractedFieldsAsStrings = ocrResult.ExtractedFields.ToDictionary(
            kvp => kvp.Key,
            kvp => kvp.Value.ExtractedValue ?? string.Empty
        );

        var interfaceResult = await _documentValidationService.ValidateDocumentDataAsync(
            request.DocumentType.ToString(),
            extractedFieldsAsStrings,
            validationConfig,
            cancellationToken);

        // Convert interface result to command result type
        return new DocumentValidationResult
        {
            IsValid = interfaceResult.IsValid,
            Issues = interfaceResult.Errors.Select(e => new ValidationIssue
            {
                IssueType = "Error",
                Description = e,
                Severity = "High",
                FieldName = ""
            }).Concat(interfaceResult.Warnings.Select(w => new ValidationIssue
            {
                IssueType = "Warning",
                Description = w,
                Severity = "Medium",
                FieldName = ""
            })).ToList(),
            ConfidenceScore = interfaceResult.ConfidenceScore,
            ValidatedFields = interfaceResult.ValidatedFields.Select(kvp => new ValidationIssue
            {
                IssueType = "Validation",
                Description = $"Field: {kvp.Key}, Value: {kvp.Value}",
                Severity = "Low",
                FieldName = kvp.Key
            }).ToList()
        };
    }

    private async Task<DocumentPreviewData> GeneratePreviewData(
        ProcessTransportCompanyDocumentWithOCRCommand request,
        OCRExtractionResult ocrResult,
        DocumentValidationResult validationResult,
        CancellationToken cancellationToken)
    {
        var extractedData = ocrResult.ExtractedFields.ToDictionary(
            kvp => kvp.Key,
            kvp => (object)kvp.Value.ExtractedValue);

        var mergedData = MergeWithExistingData(extractedData, request.ExistingData, request.OverwriteExistingData);
        var dataConflicts = IdentifyDataConflicts(extractedData, request.ExistingData, ocrResult.ExtractedFields);
        var previewFields = GeneratePreviewFields(request.DocumentType, ocrResult.ExtractedFields, validationResult);
        var annotatedRegions = GenerateAnnotatedRegions(ocrResult.ExtractedFields);

        // Generate preview image with annotations
        var previewImageUrl = await GeneratePreviewImage(ocrResult, annotatedRegions, cancellationToken);

        return new DocumentPreviewData
        {
            DocumentType = request.DocumentType.ToString(),
            ExtractedData = extractedData,
            MergedData = mergedData,
            DataConflicts = dataConflicts,
            PreviewFields = previewFields,
            PreviewImageUrl = previewImageUrl,
            AnnotatedRegions = annotatedRegions
        };
    }

    private List<OCRSuggestion> GenerateOCRSuggestions(
        OCRExtractionResult ocrResult,
        DocumentValidationResult validationResult)
    {
        var suggestions = new List<OCRSuggestion>();

        // Quality-based suggestions
        if (ocrResult.QualityMetrics.ImageQuality < 0.7m)
        {
            suggestions.Add(new OCRSuggestion
            {
                SuggestionType = "ImageQuality",
                Title = "Improve Image Quality",
                Description = "The document image quality is below optimal. Consider retaking the photo.",
                ActionRequired = "Retake photo with better lighting and focus",
                Priority = "High"
            });
        }

        // Confidence-based suggestions
        var lowConfidenceFields = ocrResult.ExtractedFields
            .Where(f => f.Value.Confidence < 0.8m)
            .ToList();

        if (lowConfidenceFields.Any())
        {
            suggestions.Add(new OCRSuggestion
            {
                SuggestionType = "LowConfidence",
                Title = "Review Low Confidence Fields",
                Description = $"Some fields have low confidence scores: {string.Join(", ", lowConfidenceFields.Select(f => f.Key))}",
                ActionRequired = "Manually verify and correct these fields",
                Priority = "Medium",
                SuggestionData = new Dictionary<string, object>
                {
                    ["LowConfidenceFields"] = lowConfidenceFields.Select(f => new { f.Key, f.Value.Confidence }).ToList()
                }
            });
        }

        // Validation-based suggestions
        if (validationResult.Issues.Any(i => i.IsBlocker))
        {
            suggestions.Add(new OCRSuggestion
            {
                SuggestionType = "ValidationIssues",
                Title = "Critical Validation Issues",
                Description = "There are critical validation issues that must be resolved",
                ActionRequired = "Address all critical validation issues before proceeding",
                Priority = "Critical"
            });
        }

        return suggestions;
    }

    private List<DocumentType> GetAllowedDocumentTypes()
    {
        return new List<DocumentType>
        {
            DocumentType.GstCertificate,
            DocumentType.TradeLicense,
            DocumentType.PanCard,
            DocumentType.VehicleRegistration,
            DocumentType.VehicleInsurance,
            DocumentType.DrivingLicense
        };
    }

    private Dictionary<string, object> GetOCRConfiguration(DocumentType documentType)
    {
        return documentType switch
        {
            DocumentType.GstCertificate => new Dictionary<string, object>
            {
                ["ExpectedFields"] = new[] { "GSTNumber", "LegalName", "TradeName", "Address", "StateCode", "RegistrationDate" },
                ["DocumentFormat"] = "GST_CERTIFICATE",
                ["Language"] = "en",
                ["EnableTableExtraction"] = false
            },
            DocumentType.PanCard => new Dictionary<string, object>
            {
                ["ExpectedFields"] = new[] { "PANNumber", "Name", "FatherName", "DateOfBirth", "Signature" },
                ["DocumentFormat"] = "PAN_CARD",
                ["Language"] = "en",
                ["EnableSignatureDetection"] = true
            },
            DocumentType.DrivingLicense => new Dictionary<string, object>
            {
                ["ExpectedFields"] = new[] { "LicenseNumber", "Name", "Address", "DateOfBirth", "IssueDate", "ExpiryDate", "VehicleClass" },
                ["DocumentFormat"] = "DRIVING_LICENSE",
                ["Language"] = "en",
                ["EnablePhotoDetection"] = true
            },
            _ => new Dictionary<string, object>
            {
                ["Language"] = "en",
                ["EnableTableExtraction"] = true
            }
        };
    }

    private Dictionary<string, OCRFieldResult> ExtractDocumentSpecificFields(
        DocumentType documentType,
        dynamic ocrData)
    {
        var fields = new Dictionary<string, OCRFieldResult>();

        switch (documentType)
        {
            case DocumentType.GstCertificate:
                ExtractGSTFields(fields, ocrData);
                break;
            case DocumentType.PanCard:
                ExtractPANFields(fields, ocrData);
                break;
            case DocumentType.DrivingLicense:
                ExtractDrivingLicenseFields(fields, ocrData);
                break;
            case DocumentType.VehicleRegistration:
                ExtractVehicleRegistrationFields(fields, ocrData);
                break;
            default:
                ExtractGenericFields(fields, ocrData);
                break;
        }

        return fields;
    }

    private void ExtractGSTFields(Dictionary<string, OCRFieldResult> fields, dynamic ocrData)
    {
        // Extract GST-specific fields with validation
        if (ocrData.Fields.ContainsKey("GSTNumber"))
        {
            fields["GSTNumber"] = new OCRFieldResult
            {
                FieldName = "GSTNumber",
                ExtractedValue = ocrData.Fields["GSTNumber"].Value,
                Confidence = ocrData.Fields["GSTNumber"].Confidence,
                IsRequired = true,
                IsValid = ValidateGSTNumber(ocrData.Fields["GSTNumber"].Value)
            };
        }

        if (ocrData.Fields.ContainsKey("LegalName"))
        {
            fields["LegalName"] = new OCRFieldResult
            {
                FieldName = "LegalName",
                ExtractedValue = ocrData.Fields["LegalName"].Value,
                Confidence = ocrData.Fields["LegalName"].Confidence,
                IsRequired = true,
                IsValid = !string.IsNullOrWhiteSpace(ocrData.Fields["LegalName"].Value)
            };
        }

        // Add more GST-specific fields...
    }

    private void ExtractPANFields(Dictionary<string, OCRFieldResult> fields, dynamic ocrData)
    {
        if (ocrData.Fields.ContainsKey("PANNumber"))
        {
            fields["PANNumber"] = new OCRFieldResult
            {
                FieldName = "PANNumber",
                ExtractedValue = ocrData.Fields["PANNumber"].Value,
                Confidence = ocrData.Fields["PANNumber"].Confidence,
                IsRequired = true,
                IsValid = ValidatePANNumber(ocrData.Fields["PANNumber"].Value)
            };
        }

        // Add more PAN-specific fields...
    }

    private void ExtractDrivingLicenseFields(Dictionary<string, OCRFieldResult> fields, dynamic ocrData)
    {
        if (ocrData.Fields.ContainsKey("LicenseNumber"))
        {
            fields["LicenseNumber"] = new OCRFieldResult
            {
                FieldName = "LicenseNumber",
                ExtractedValue = ocrData.Fields["LicenseNumber"].Value,
                Confidence = ocrData.Fields["LicenseNumber"].Confidence,
                IsRequired = true,
                IsValid = ValidateDrivingLicenseNumber(ocrData.Fields["LicenseNumber"].Value)
            };
        }

        // Add more driving license-specific fields...
    }

    private void ExtractVehicleRegistrationFields(Dictionary<string, OCRFieldResult> fields, dynamic ocrData)
    {
        // Extract vehicle registration specific fields
    }

    private void ExtractGenericFields(Dictionary<string, OCRFieldResult> fields, dynamic ocrData)
    {
        // Extract generic fields for unknown document types
    }

    private bool ValidateGSTNumber(string gstNumber)
    {
        // GST number format: 15 characters (2 state code + 10 PAN + 1 entity number + 1 Z + 1 check digit)
        return !string.IsNullOrWhiteSpace(gstNumber) &&
               gstNumber.Length == 15 &&
               System.Text.RegularExpressions.Regex.IsMatch(gstNumber, @"^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$");
    }

    private bool ValidatePANNumber(string panNumber)
    {
        // PAN format: 5 letters + 4 digits + 1 letter
        return !string.IsNullOrWhiteSpace(panNumber) &&
               panNumber.Length == 10 &&
               System.Text.RegularExpressions.Regex.IsMatch(panNumber, @"^[A-Z]{5}[0-9]{4}[A-Z]{1}$");
    }

    private bool ValidateDrivingLicenseNumber(string licenseNumber)
    {
        // Basic validation - varies by state
        return !string.IsNullOrWhiteSpace(licenseNumber) && licenseNumber.Length >= 10;
    }

    private string GeneratePreviewToken(Guid transportCompanyId, DocumentType documentType)
    {
        var tokenData = $"{transportCompanyId}:{documentType}:{DateTime.UtcNow:yyyyMMddHHmmss}:{Guid.NewGuid():N}";
        return Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(tokenData));
    }

    private async Task StorePreviewData(string previewToken, ProcessTransportCompanyDocumentWithOCRResult result, CancellationToken cancellationToken)
    {
        // Store preview data in cache or temporary storage for later confirmation
        // Implementation would depend on caching infrastructure
        await Task.CompletedTask;
    }

    private async Task<Guid> SaveProcessedDocument(
        ProcessTransportCompanyDocumentWithOCRCommand request,
        OCRExtractionResult ocrResult,
        DocumentValidationResult validationResult,
        CancellationToken cancellationToken)
    {
        // Save the processed document to the database
        // Implementation would create and save document entity
        return Guid.NewGuid();
    }

    private class ValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new();
    }

    private class DocumentQualityAnalysis
    {
        public double OverallScore { get; set; }
        public double Clarity { get; set; }
        public double Brightness { get; set; }
        public double Contrast { get; set; }
        public List<string> Issues { get; set; } = new();
    }

    private class TextRegion
    {
        public string Text { get; set; } = string.Empty;
        public BoundingBox? BoundingBox { get; set; }
        public double Confidence { get; set; }
    }



    private Dictionary<string, object> MergeWithExistingData(
        Dictionary<string, object> extractedData,
        Dictionary<string, object>? existingData,
        bool overwriteExisting)
    {
        if (existingData == null || !existingData.Any())
            return extractedData;

        var mergedData = new Dictionary<string, object>(existingData);

        foreach (var kvp in extractedData)
        {
            if (overwriteExisting || !mergedData.ContainsKey(kvp.Key))
            {
                mergedData[kvp.Key] = kvp.Value;
            }
        }

        return mergedData;
    }

    private List<DataConflict> IdentifyDataConflicts(
        Dictionary<string, object> extractedData,
        Dictionary<string, object>? existingData,
        Dictionary<string, OCRFieldResult> extractedFields)
    {
        var conflicts = new List<DataConflict>();

        if (existingData == null)
            return conflicts;

        foreach (var kvp in extractedData)
        {
            if (existingData.ContainsKey(kvp.Key))
            {
                var existingValue = existingData[kvp.Key]?.ToString() ?? "";
                var extractedValue = kvp.Value?.ToString() ?? "";

                if (!string.Equals(existingValue, extractedValue, StringComparison.OrdinalIgnoreCase))
                {
                    var confidence = extractedFields.ContainsKey(kvp.Key) ? extractedFields[kvp.Key].Confidence : 0.0m;
                    conflicts.Add(new DataConflict
                    {
                        FieldName = kvp.Key,
                        ExistingValue = existingValue,
                        ExtractedValue = extractedValue,
                        ExtractedConfidence = confidence,
                        RecommendedAction = confidence > 0.8m ? "Accept Extracted" : "Manual Review",
                        ConflictReason = "Value mismatch between extracted and existing data"
                    });
                }
            }
        }

        return conflicts;
    }

    private List<PreviewField> GeneratePreviewFields(
        DocumentType documentType,
        Dictionary<string, OCRFieldResult> extractedFields,
        DocumentValidationResult validationResult)
    {
        var previewFields = new List<PreviewField>();

        foreach (var kvp in extractedFields)
        {
            var hasError = validationResult.Issues.Any(e => e.FieldName == kvp.Key);
            previewFields.Add(new PreviewField
            {
                FieldName = kvp.Key,
                DisplayName = GetDisplayName(kvp.Key),
                Value = kvp.Value.ExtractedValue,
                OriginalValue = kvp.Value.ExtractedValue,
                Confidence = kvp.Value.Confidence,
                IsEditable = true,
                IsRequired = IsRequiredField(documentType, kvp.Key),
                IsValid = !hasError,
                HasConflict = false,
                FieldType = GetFieldType(kvp.Key),
                ValidationRules = GetValidationRules(kvp.Key),
                SourceRegion = kvp.Value.BoundingBox != null ? new BoundingBox
                {
                    X = kvp.Value.BoundingBox.X,
                    Y = kvp.Value.BoundingBox.Y,
                    Width = kvp.Value.BoundingBox.Width,
                    Height = kvp.Value.BoundingBox.Height,
                    Confidence = kvp.Value.Confidence
                } : null
            });
        }

        return previewFields;
    }

    private List<AnnotatedRegion> GenerateAnnotatedRegions(Dictionary<string, OCRFieldResult> extractedFields)
    {
        var regions = new List<AnnotatedRegion>();

        foreach (var kvp in extractedFields)
        {
            if (kvp.Value.BoundingBox != null)
            {
                regions.Add(new AnnotatedRegion
                {
                    FieldName = kvp.Key,
                    Label = GetDisplayName(kvp.Key),
                    BoundingBox = new BoundingBox
                    {
                        X = kvp.Value.BoundingBox.X,
                        Y = kvp.Value.BoundingBox.Y,
                        Width = kvp.Value.BoundingBox.Width,
                        Height = kvp.Value.BoundingBox.Height,
                        Confidence = kvp.Value.Confidence
                    },
                    Color = GetFieldColor(kvp.Key),
                    Confidence = kvp.Value.Confidence,
                    IsHighlighted = kvp.Value.Confidence > 0.8m
                });
            }
        }

        return regions;
    }

    private async Task<string> GeneratePreviewImage(
        OCRExtractionResult ocrResult,
        List<AnnotatedRegion> annotatedRegions,
        CancellationToken cancellationToken)
    {
        // Mock implementation - in production this would generate an annotated preview image
        await Task.Delay(10, cancellationToken);
        return $"/api/preview/document-{Guid.NewGuid()}.jpg";
    }

    private DocumentQualityAnalysis AnalyzeDocumentQuality(byte[] documentData)
    {
        // Mock implementation - in production this would analyze image quality
        return new DocumentQualityAnalysis
        {
            OverallScore = 0.85,
            Clarity = 0.9,
            Brightness = 0.8,
            Contrast = 0.85,
            Issues = new List<string>()
        };
    }

    private async Task<DocumentQualityMetrics> AnalyzeDocumentQualityAsync(string filePath, Dictionary<string, object> extractedData, CancellationToken cancellationToken)
    {
        // Mock implementation - in production this would analyze document quality
        await Task.Delay(10, cancellationToken);
        return new DocumentQualityMetrics
        {
            OverallScore = 0.85m,
            Clarity = 0.9m,
            Brightness = 0.8m,
            Contrast = 0.85m,
            Resolution = 300,
            ColorDepth = 24,
            FileSize = new FileInfo(filePath).Length,
            Issues = new List<string>(),
            Recommendations = new List<string>()
        };
    }

    private List<TextRegion> ConvertToTextRegions(Dictionary<string, OCRFieldResult> extractedFields)
    {
        return extractedFields.Select(kvp => new TextRegion
        {
            Text = kvp.Value.ExtractedValue,
            BoundingBox = kvp.Value.BoundingBox,
            Confidence = (double)kvp.Value.Confidence
        }).ToList();
    }

    private UserManagement.Application.Interfaces.ValidationConfiguration GetValidationConfiguration(DocumentType documentType)
    {
        // Mock implementation - in production this would return validation rules for the document type
        return new UserManagement.Application.Interfaces.ValidationConfiguration
        {
            RequiredFields = new List<string> { "Name", "Number", "Date" },
            FieldRules = new Dictionary<string, UserManagement.Application.Interfaces.FieldValidationRule>
            {
                ["Name"] = new UserManagement.Application.Interfaces.FieldValidationRule
                {
                    FieldName = "Name",
                    IsRequired = true,
                    MinLength = 2
                },
                ["Number"] = new UserManagement.Application.Interfaces.FieldValidationRule
                {
                    FieldName = "Number",
                    IsRequired = true,
                    Pattern = @"^\d+$"
                },
                ["Date"] = new UserManagement.Application.Interfaces.FieldValidationRule
                {
                    FieldName = "Date",
                    IsRequired = true
                }
            },
            MinimumConfidenceThreshold = 0.7m
        };
    }

    private string GetDisplayName(string fieldName)
    {
        return fieldName switch
        {
            "pan_number" => "PAN Number",
            "company_name" => "Company Name",
            "gst_number" => "GST Number",
            "registration_number" => "Registration Number",
            "license_number" => "License Number",
            "name" => "Full Name",
            "date_of_birth" => "Date of Birth",
            "address" => "Address",
            _ => fieldName.Replace("_", " ").ToTitleCase()
        };
    }

    private bool IsRequiredField(DocumentType documentType, string fieldName)
    {
        return documentType switch
        {
            DocumentType.PanCard => fieldName is "pan_number" or "name",
            DocumentType.GstCertificate => fieldName is "gst_number" or "company_name",
            DocumentType.DrivingLicense => fieldName is "license_number" or "name",
            _ => false
        };
    }

    private string GetFieldType(string fieldName)
    {
        return fieldName switch
        {
            var f when f.Contains("date") => "Date",
            var f when f.Contains("number") => "Number",
            var f when f.Contains("email") => "Email",
            _ => "Text"
        };
    }

    private List<string> GetValidationRules(string fieldName)
    {
        return fieldName switch
        {
            "pan_number" => new List<string> { "Required", "Length:10", "AlphaNumeric" },
            "gst_number" => new List<string> { "Required", "Length:15", "AlphaNumeric" },
            "email" => new List<string> { "Email" },
            _ => new List<string>()
        };
    }

    private string GetFieldColor(string fieldName)
    {
        return fieldName switch
        {
            var f when f.Contains("number") => "#007bff", // Blue for numbers
            var f when f.Contains("name") => "#28a745", // Green for names
            var f when f.Contains("date") => "#ffc107", // Yellow for dates
            _ => "#6c757d" // Gray for others
        };
    }
}

public static class StringExtensions
{
    public static string ToTitleCase(this string input)
    {
        if (string.IsNullOrEmpty(input))
            return input;

        var textInfo = System.Globalization.CultureInfo.CurrentCulture.TextInfo;
        return textInfo.ToTitleCase(input.ToLower());
    }
}

/// <summary>
/// Wrapper class to convert Stream to IFormFile
/// </summary>
public class FormFileWrapper : IFormFile
{
    private readonly Stream _stream;
    private readonly string _fileName;
    private readonly string _contentType;

    public FormFileWrapper(Stream stream, string fileName, string contentType)
    {
        _stream = stream;
        _fileName = fileName;
        _contentType = contentType;
    }

    public string ContentType => _contentType;
    public string ContentDisposition => $"form-data; name=\"file\"; filename=\"{_fileName}\"";
    public IHeaderDictionary Headers { get; } = new HeaderDictionary();
    public long Length => _stream.Length;
    public string Name => "file";
    public string FileName => _fileName;

    public Stream OpenReadStream() => _stream;

    public void CopyTo(Stream target) => _stream.CopyTo(target);

    public Task CopyToAsync(Stream target, CancellationToken cancellationToken = default)
        => _stream.CopyToAsync(target, cancellationToken);
}



