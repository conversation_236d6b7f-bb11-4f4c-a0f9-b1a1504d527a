using System;
using Identity.Domain.Common;
using Identity.Domain.Exceptions;

namespace Identity.Domain.Entities
{
    public class RefreshToken : Entity
    {
        public string Token { get; private set; }
        public Guid UserId { get; private set; }
        public DateTime ExpiryDate { get; private set; }
        public bool IsRevoked { get; private set; }
        public string? ReplacedByToken { get; private set; }
        public string? ReasonRevoked { get; private set; }
        public string CreatedByIp { get; private set; }
        public string? RevokedByIp { get; private set; }
        public DateTime? RevokedAt { get; private set; }

        private RefreshToken() { }

        public RefreshToken(string token, Guid userId, DateTime expiryDate, string createdByIp)
        {
            if (string.IsNullOrWhiteSpace(token))
                throw new DomainException("Token cannot be empty");

            Token = token;
            UserId = userId;
            ExpiryDate = expiryDate;
            CreatedByIp = createdByIp;
            IsRevoked = false;
            CreatedAt = DateTime.UtcNow;
        }

        public bool IsExpired => DateTime.UtcNow >= ExpiryDate;
        public bool IsActive => !IsRevoked && !IsExpired;

        public void Revoke(string reasonRevoked, string revokedByIp, string? replacedByToken = null)
        {
            IsRevoked = true;
            ReasonRevoked = reasonRevoked;
            RevokedByIp = revokedByIp;
            RevokedAt = DateTime.UtcNow;
            ReplacedByToken = replacedByToken;
            UpdatedAt = DateTime.UtcNow;
        }
    }
}
