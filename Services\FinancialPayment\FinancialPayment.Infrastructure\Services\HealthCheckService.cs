using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Domain.Entities;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace FinancialPayment.Infrastructure.Services;

public class HealthCheckService : IHealthCheckService
{
    private readonly ILogger<HealthCheckService> _logger;
    private readonly MonitoringConfiguration _configuration;
    
    // In-memory storage for demo purposes
    private readonly ConcurrentBag<HealthCheck> _healthChecks;
    private readonly ConcurrentDictionary<string, Func<Task<HealthCheckResult>>> _registeredChecks;

    public HealthCheckService(
        ILogger<HealthCheckService> logger,
        IOptions<MonitoringConfiguration> configuration)
    {
        _logger = logger;
        _configuration = configuration.Value;
        _healthChecks = new ConcurrentBag<HealthCheck>();
        _registeredChecks = new ConcurrentDictionary<string, Func<Task<HealthCheckResult>>>();

        // Register default health checks
        RegisterDefaultHealthChecks();
    }

    public async Task<HealthCheck> PerformHealthCheckAsync(string serviceName, string checkName)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            var checkKey = $"{serviceName}:{checkName}";
            
            if (_registeredChecks.TryGetValue(checkKey, out var healthCheckFunc))
            {
                var result = await healthCheckFunc();
                stopwatch.Stop();

                var healthCheck = new HealthCheck(
                    serviceName,
                    checkName,
                    result.Status,
                    stopwatch.Elapsed,
                    result.Description,
                    result.Data,
                    result.ErrorMessage);

                _healthChecks.Add(healthCheck);

                _logger.LogDebug("Health check performed: {ServiceName}/{CheckName} - {Status} in {Duration}ms",
                    serviceName, checkName, result.Status, stopwatch.ElapsedMilliseconds);

                return healthCheck;
            }
            else
            {
                stopwatch.Stop();
                var healthCheck = new HealthCheck(
                    serviceName,
                    checkName,
                    HealthStatus.Unknown,
                    stopwatch.Elapsed,
                    "Health check not registered",
                    errorMessage: "No health check function registered for this service");

                _healthChecks.Add(healthCheck);
                return healthCheck;
            }
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error performing health check for {ServiceName}/{CheckName}", serviceName, checkName);

            var healthCheck = new HealthCheck(
                serviceName,
                checkName,
                HealthStatus.Unhealthy,
                stopwatch.Elapsed,
                "Health check failed",
                errorMessage: ex.Message);

            _healthChecks.Add(healthCheck);
            return healthCheck;
        }
    }

    public async Task<List<HealthCheck>> GetHealthChecksAsync(string? serviceName = null)
    {
        try
        {
            var recentChecks = _healthChecks
                .Where(hc => hc.CheckedAt >= DateTime.UtcNow.AddMinutes(-5)) // Last 5 minutes
                .Where(hc => string.IsNullOrEmpty(serviceName) || hc.ServiceName == serviceName)
                .GroupBy(hc => new { hc.ServiceName, hc.CheckName })
                .Select(g => g.OrderByDescending(hc => hc.CheckedAt).First()) // Latest check for each service/check combination
                .OrderBy(hc => hc.ServiceName)
                .ThenBy(hc => hc.CheckName)
                .ToList();

            return await Task.FromResult(recentChecks);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting health checks");
            return new List<HealthCheck>();
        }
    }

    public async Task<SystemHealthSummary> GetSystemHealthSummaryAsync()
    {
        try
        {
            var recentChecks = await GetHealthChecksAsync();
            
            var serviceStatuses = recentChecks
                .GroupBy(hc => hc.ServiceName)
                .Select(g => new ServiceHealthStatus
                {
                    ServiceName = g.Key,
                    Status = g.Any(hc => hc.Status == HealthStatus.Unhealthy) ? HealthStatus.Unhealthy :
                            g.Any(hc => hc.Status == HealthStatus.Degraded) ? HealthStatus.Degraded :
                            g.All(hc => hc.Status == HealthStatus.Healthy) ? HealthStatus.Healthy :
                            HealthStatus.Unknown,
                    ResponseTime = TimeSpan.FromMilliseconds(g.Average(hc => hc.ResponseTime.TotalMilliseconds)),
                    LastChecked = g.Max(hc => hc.CheckedAt),
                    ErrorMessage = g.Where(hc => hc.Status == HealthStatus.Unhealthy).FirstOrDefault()?.ErrorMessage
                })
                .ToList();

            var healthyCount = serviceStatuses.Count(s => s.Status == HealthStatus.Healthy);
            var degradedCount = serviceStatuses.Count(s => s.Status == HealthStatus.Degraded);
            var unhealthyCount = serviceStatuses.Count(s => s.Status == HealthStatus.Unhealthy);

            var overallStatus = unhealthyCount > 0 ? HealthStatus.Unhealthy :
                               degradedCount > 0 ? HealthStatus.Degraded :
                               healthyCount > 0 ? HealthStatus.Healthy :
                               HealthStatus.Unknown;

            return new SystemHealthSummary
            {
                OverallStatus = overallStatus,
                HealthyServices = healthyCount,
                DegradedServices = degradedCount,
                UnhealthyServices = unhealthyCount,
                ServiceStatuses = serviceStatuses,
                LastUpdated = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting system health summary");
            return new SystemHealthSummary
            {
                OverallStatus = HealthStatus.Unknown,
                LastUpdated = DateTime.UtcNow
            };
        }
    }

    public async Task RegisterHealthCheckAsync(string serviceName, string checkName, Func<Task<HealthCheckResult>> healthCheckFunc)
    {
        try
        {
            var checkKey = $"{serviceName}:{checkName}";
            _registeredChecks.AddOrUpdate(checkKey, healthCheckFunc, (key, existing) => healthCheckFunc);

            _logger.LogInformation("Health check registered: {ServiceName}/{CheckName}", serviceName, checkName);
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error registering health check for {ServiceName}/{CheckName}", serviceName, checkName);
        }
    }

    public async Task<bool> IsServiceHealthyAsync(string serviceName)
    {
        try
        {
            var recentChecks = await GetHealthChecksAsync(serviceName);
            
            if (!recentChecks.Any())
            {
                // No recent health checks, assume unhealthy
                return false;
            }

            // Service is healthy if all checks are healthy or degraded (not unhealthy)
            return recentChecks.All(hc => hc.Status != HealthStatus.Unhealthy);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if service {ServiceName} is healthy", serviceName);
            return false;
        }
    }

    private void RegisterDefaultHealthChecks()
    {
        // Database health check
        RegisterHealthCheckAsync("Database", "Connection", async () =>
        {
            try
            {
                // Simulate database connection check
                await Task.Delay(50);
                
                return new HealthCheckResult
                {
                    Status = HealthStatus.Healthy,
                    Description = "Database connection is healthy",
                    Data = new Dictionary<string, object>
                    {
                        { "connection_pool_size", 10 },
                        { "active_connections", 3 }
                    }
                };
            }
            catch (Exception ex)
            {
                return new HealthCheckResult
                {
                    Status = HealthStatus.Unhealthy,
                    Description = "Database connection failed",
                    ErrorMessage = ex.Message
                };
            }
        });

        // Memory health check
        RegisterHealthCheckAsync("System", "Memory", async () =>
        {
            try
            {
                var process = Process.GetCurrentProcess();
                var memoryUsageMB = process.WorkingSet64 / 1024 / 1024;
                
                await Task.CompletedTask;

                var status = memoryUsageMB > 1000 ? HealthStatus.Unhealthy :
                            memoryUsageMB > 500 ? HealthStatus.Degraded :
                            HealthStatus.Healthy;

                return new HealthCheckResult
                {
                    Status = status,
                    Description = $"Memory usage: {memoryUsageMB} MB",
                    Data = new Dictionary<string, object>
                    {
                        { "memory_usage_mb", memoryUsageMB },
                        { "memory_limit_mb", 1000 }
                    }
                };
            }
            catch (Exception ex)
            {
                return new HealthCheckResult
                {
                    Status = HealthStatus.Unhealthy,
                    Description = "Memory check failed",
                    ErrorMessage = ex.Message
                };
            }
        });

        // Payment Gateway health check
        RegisterHealthCheckAsync("PaymentGateway", "Connectivity", async () =>
        {
            try
            {
                // Simulate payment gateway connectivity check
                await Task.Delay(100);
                
                var random = new Random();
                var isHealthy = random.NextDouble() > 0.1; // 90% chance of being healthy

                return new HealthCheckResult
                {
                    Status = isHealthy ? HealthStatus.Healthy : HealthStatus.Degraded,
                    Description = isHealthy ? "Payment gateway is responsive" : "Payment gateway is slow",
                    Data = new Dictionary<string, object>
                    {
                        { "response_time_ms", random.Next(50, 200) },
                        { "last_successful_transaction", DateTime.UtcNow.AddMinutes(-random.Next(1, 10)) }
                    }
                };
            }
            catch (Exception ex)
            {
                return new HealthCheckResult
                {
                    Status = HealthStatus.Unhealthy,
                    Description = "Payment gateway check failed",
                    ErrorMessage = ex.Message
                };
            }
        });

        _logger.LogInformation("Default health checks registered");
    }
}
