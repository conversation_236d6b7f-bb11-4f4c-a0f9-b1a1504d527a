using UserManagement.Application.Interfaces;

namespace UserManagement.Infrastructure.Services
{
    public class StubTranslationService : ITranslationService
    {
        public Task<string> TranslateAsync(string text, string fromLanguage, string toLanguage)
        {
            // Stub implementation - return original text
            return Task.FromResult(text);
        }

        public Task<Dictionary<string, string>> TranslateBatchAsync(Dictionary<string, string> texts, string fromLanguage, string toLanguage)
        {
            // Stub implementation - return original texts
            return Task.FromResult(texts);
        }

        public Task<bool> IsServiceAvailableAsync(CancellationToken cancellationToken = default)
        {
            // Stub implementation - always return true
            return Task.FromResult(true);
        }

        public Task<decimal> GetTranslationQualityAsync(string fromLanguage, string toLanguage, CancellationToken cancellationToken = default)
        {
            // Stub implementation - return high quality score
            return Task.FromResult(0.95m);
        }
    }
}
