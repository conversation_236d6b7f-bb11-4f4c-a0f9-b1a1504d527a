using DataStorage.Domain.Enums;

namespace DataStorage.Domain.ValueObjects;

public class BackupResult
{
    public bool IsSuccessful { get; private set; }
    public string? ErrorMessage { get; private set; }
    public Guid BackupId { get; private set; }
    public BackupType Type { get; private set; }
    public DateTime StartedAt { get; private set; }
    public DateTime? CompletedAt { get; private set; }
    public TimeSpan Duration { get; private set; }
    public long TotalSizeBytes { get; private set; }
    public long CompressedSizeBytes { get; private set; }
    public int FilesBackedUp { get; private set; }
    public double CompressionRatio { get; private set; }
    public string BackupLocation { get; private set; }

    private BackupResult()
    {
        BackupLocation = string.Empty;
    }

    public static BackupResult Success(
        Guid backupId,
        BackupType type,
        DateTime startedAt,
        TimeSpan duration,
        long totalSizeBytes,
        long compressedSizeBytes,
        int filesBackedUp,
        string backupLocation)
    {
        return new BackupResult
        {
            IsSuccessful = true,
            BackupId = backupId,
            Type = type,
            StartedAt = startedAt,
            CompletedAt = startedAt.Add(duration),
            Duration = duration,
            TotalSizeBytes = totalSizeBytes,
            CompressedSizeBytes = compressedSizeBytes,
            FilesBackedUp = filesBackedUp,
            CompressionRatio = totalSizeBytes > 0 ? (double)compressedSizeBytes / totalSizeBytes : 0,
            BackupLocation = backupLocation
        };
    }

    public static BackupResult Failure(Guid backupId, BackupType type, string errorMessage, DateTime startedAt)
    {
        return new BackupResult
        {
            IsSuccessful = false,
            BackupId = backupId,
            Type = type,
            ErrorMessage = errorMessage,
            StartedAt = startedAt,
            BackupLocation = string.Empty
        };
    }
}

public class BackupRequest
{
    public BackupType Type { get; private set; }
    public List<Guid> DocumentIds { get; private set; }
    public List<string> Categories { get; private set; }
    public Guid RequestedBy { get; private set; }
    public string? Description { get; private set; }
    public bool CompressBackup { get; private set; }
    public bool EncryptBackup { get; private set; }
    public BackupRetentionPolicy RetentionPolicy { get; private set; }
    public List<string> ReplicationTargets { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    public BackupRequest(
        BackupType type,
        Guid requestedBy,
        List<Guid>? documentIds = null,
        List<string>? categories = null,
        string? description = null,
        bool compressBackup = true,
        bool encryptBackup = true,
        BackupRetentionPolicy? retentionPolicy = null,
        List<string>? replicationTargets = null,
        Dictionary<string, object>? metadata = null)
    {
        Type = type;
        RequestedBy = requestedBy;
        DocumentIds = documentIds ?? new List<Guid>();
        Categories = categories ?? new List<string>();
        Description = description;
        CompressBackup = compressBackup;
        EncryptBackup = encryptBackup;
        RetentionPolicy = retentionPolicy ?? new BackupRetentionPolicy();
        ReplicationTargets = replicationTargets ?? new List<string>();
        Metadata = metadata ?? new Dictionary<string, object>();
    }
}

public class BackupRetentionPolicy
{
    public TimeSpan RetentionPeriod { get; private set; }
    public int MaxBackupCount { get; private set; }
    public BackupRetentionStrategy Strategy { get; private set; }
    public bool AutoDeleteExpired { get; private set; }

    public BackupRetentionPolicy(
        TimeSpan retentionPeriod = default,
        int maxBackupCount = 30,
        BackupRetentionStrategy strategy = BackupRetentionStrategy.TimeBasedRetention,
        bool autoDeleteExpired = true)
    {
        RetentionPeriod = retentionPeriod == default ? TimeSpan.FromDays(90) : retentionPeriod;
        MaxBackupCount = maxBackupCount;
        Strategy = strategy;
        AutoDeleteExpired = autoDeleteExpired;
    }
}

public class BackupInfo
{
    public Guid BackupId { get; private set; }
    public BackupType Type { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public Guid CreatedBy { get; private set; }
    public BackupStatus Status { get; private set; }
    public long SizeBytes { get; private set; }
    public long CompressedSizeBytes { get; private set; }
    public int FileCount { get; private set; }
    public string Location { get; private set; }
    public string? Description { get; private set; }
    public List<string> ReplicationTargets { get; private set; }
    public DateTime? ExpiresAt { get; private set; }
    public BackupVerificationStatus VerificationStatus { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    public BackupInfo(
        Guid backupId,
        BackupType type,
        DateTime createdAt,
        Guid createdBy,
        BackupStatus status,
        long sizeBytes,
        long compressedSizeBytes,
        int fileCount,
        string location,
        BackupVerificationStatus verificationStatus,
        string? description = null,
        List<string>? replicationTargets = null,
        DateTime? expiresAt = null,
        Dictionary<string, object>? metadata = null)
    {
        BackupId = backupId;
        Type = type;
        CreatedAt = createdAt;
        CreatedBy = createdBy;
        Status = status;
        SizeBytes = sizeBytes;
        CompressedSizeBytes = compressedSizeBytes;
        FileCount = fileCount;
        Location = location ?? throw new ArgumentNullException(nameof(location));
        Description = description;
        ReplicationTargets = replicationTargets ?? new List<string>();
        ExpiresAt = expiresAt;
        VerificationStatus = verificationStatus;
        Metadata = metadata ?? new Dictionary<string, object>();
    }
}

public class BackupFilter
{
    public List<BackupType> Types { get; private set; }
    public List<BackupStatus> Statuses { get; private set; }
    public DateTime? StartDate { get; private set; }
    public DateTime? EndDate { get; private set; }
    public Guid? CreatedBy { get; private set; }
    public List<string> Categories { get; private set; }

    public BackupFilter(
        List<BackupType>? types = null,
        List<BackupStatus>? statuses = null,
        DateTime? startDate = null,
        DateTime? endDate = null,
        Guid? createdBy = null,
        List<string>? categories = null)
    {
        Types = types ?? new List<BackupType>();
        Statuses = statuses ?? new List<BackupStatus>();
        StartDate = startDate;
        EndDate = endDate;
        CreatedBy = createdBy;
        Categories = categories ?? new List<string>();
    }
}

public class RecoveryResult
{
    public bool IsSuccessful { get; private set; }
    public string? ErrorMessage { get; private set; }
    public Guid RecoveryId { get; private set; }
    public Guid BackupId { get; private set; }
    public DateTime StartedAt { get; private set; }
    public DateTime? CompletedAt { get; private set; }
    public TimeSpan Duration { get; private set; }
    public int FilesRestored { get; private set; }
    public long BytesRestored { get; private set; }
    public string RestoreLocation { get; private set; }
    public List<string> RestoredFiles { get; private set; }

    private RecoveryResult()
    {
        RestoreLocation = string.Empty;
        RestoredFiles = new List<string>();
    }

    public static RecoveryResult Success(
        Guid recoveryId,
        Guid backupId,
        DateTime startedAt,
        TimeSpan duration,
        int filesRestored,
        long bytesRestored,
        string restoreLocation,
        List<string> restoredFiles)
    {
        return new RecoveryResult
        {
            IsSuccessful = true,
            RecoveryId = recoveryId,
            BackupId = backupId,
            StartedAt = startedAt,
            CompletedAt = startedAt.Add(duration),
            Duration = duration,
            FilesRestored = filesRestored,
            BytesRestored = bytesRestored,
            RestoreLocation = restoreLocation,
            RestoredFiles = restoredFiles ?? new List<string>()
        };
    }

    public static RecoveryResult Failure(Guid recoveryId, Guid backupId, string errorMessage, DateTime startedAt)
    {
        return new RecoveryResult
        {
            IsSuccessful = false,
            RecoveryId = recoveryId,
            BackupId = backupId,
            ErrorMessage = errorMessage,
            StartedAt = startedAt,
            RestoreLocation = string.Empty,
            RestoredFiles = new List<string>()
        };
    }
}

public class RestoreRequest
{
    public Guid BackupId { get; private set; }
    public List<Guid> DocumentIds { get; private set; }
    public string? TargetLocation { get; private set; }
    public bool RestoreToOriginalLocation { get; private set; }
    public bool OverwriteExisting { get; private set; }
    public Guid RequestedBy { get; private set; }
    public RestorePriority Priority { get; private set; }
    public string? Reason { get; private set; }

    public RestoreRequest(
        Guid backupId,
        Guid requestedBy,
        List<Guid>? documentIds = null,
        string? targetLocation = null,
        bool restoreToOriginalLocation = true,
        bool overwriteExisting = false,
        RestorePriority priority = RestorePriority.Normal,
        string? reason = null)
    {
        BackupId = backupId;
        RequestedBy = requestedBy;
        DocumentIds = documentIds ?? new List<Guid>();
        TargetLocation = targetLocation;
        RestoreToOriginalLocation = restoreToOriginalLocation;
        OverwriteExisting = overwriteExisting;
        Priority = priority;
        Reason = reason;
    }
}

public class PointInTimeRecoveryRequest
{
    public DateTime TargetDateTime { get; private set; }
    public List<Guid> DocumentIds { get; private set; }
    public string? TargetLocation { get; private set; }
    public Guid RequestedBy { get; private set; }
    public string? Reason { get; private set; }

    public PointInTimeRecoveryRequest(
        DateTime targetDateTime,
        Guid requestedBy,
        List<Guid>? documentIds = null,
        string? targetLocation = null,
        string? reason = null)
    {
        TargetDateTime = targetDateTime;
        RequestedBy = requestedBy;
        DocumentIds = documentIds ?? new List<Guid>();
        TargetLocation = targetLocation;
        Reason = reason;
    }
}

public class RecoveryPoint
{
    public Guid RecoveryPointId { get; private set; }
    public DateTime Timestamp { get; private set; }
    public Guid? DocumentId { get; private set; }
    public RecoveryPointType Type { get; private set; }
    public string Description { get; private set; }
    public long SizeBytes { get; private set; }
    public bool IsAvailable { get; private set; }
    public List<Guid> AvailableBackups { get; private set; }

    public RecoveryPoint(
        Guid recoveryPointId,
        DateTime timestamp,
        RecoveryPointType type,
        string description,
        long sizeBytes,
        bool isAvailable,
        Guid? documentId = null,
        List<Guid>? availableBackups = null)
    {
        RecoveryPointId = recoveryPointId;
        Timestamp = timestamp;
        DocumentId = documentId;
        Type = type;
        Description = description ?? throw new ArgumentNullException(nameof(description));
        SizeBytes = sizeBytes;
        IsAvailable = isAvailable;
        AvailableBackups = availableBackups ?? new List<Guid>();
    }
}
