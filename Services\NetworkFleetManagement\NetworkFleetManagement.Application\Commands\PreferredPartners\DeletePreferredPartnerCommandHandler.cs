using MediatR;
using Microsoft.Extensions.Logging;
using NetworkFleetManagement.Domain.Repositories;

namespace NetworkFleetManagement.Application.Commands.PreferredPartners;

public class DeletePreferredPartnerCommandHandler : IRequestHandler<DeletePreferredPartnerCommand, bool>
{
    private readonly IPreferredPartnerRepository _preferredPartnerRepository;
    private readonly ILogger<DeletePreferredPartnerCommandHandler> _logger;

    public DeletePreferredPartnerCommandHandler(
        IPreferredPartnerRepository preferredPartnerRepository,
        ILogger<DeletePreferredPartnerCommandHandler> logger)
    {
        _preferredPartnerRepository = preferredPartnerRepository;
        _logger = logger;
    }

    public async Task<bool> Handle(DeletePreferredPartnerCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var partner = await _preferredPartnerRepository.GetByIdAsync(request.Id, cancellationToken);
            
            if (partner == null || partner.UserId != request.UserId)
            {
                _logger.LogWarning("Preferred partner {Id} not found or user {UserId} doesn't have permission to delete it", 
                    request.Id, request.UserId);
                return false;
            }

            // Soft delete by deactivating first
            partner.Deactivate(request.Reason);
            await _preferredPartnerRepository.UpdateAsync(partner, cancellationToken);

            // Then hard delete
            await _preferredPartnerRepository.DeleteAsync(request.Id, cancellationToken);

            _logger.LogInformation("Preferred partner {Id} deleted by user {UserId} with reason: {Reason}", 
                request.Id, request.UserId, request.Reason);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting preferred partner {Id}", request.Id);
            throw;
        }
    }
}
