# TLI Microservices Troubleshooting Guide

## Overview

This guide provides comprehensive troubleshooting procedures for common issues that may occur in the TLI microservices deployment across two servers.

## Quick Diagnostic Commands

### Essential Commands for Troubleshooting
```bash
# Server status
systemctl status docker
systemctl status tli-infrastructure  # Server 1
systemctl status tli-applications    # Server 2

# Container status
docker ps -a
docker stats

# Service health
/opt/tli/health-check.sh

# Network connectivity
nc -z SERVER1_IP 5432  # PostgreSQL
nc -z SERVER1_IP 6379  # Redis
nc -z SERVER1_IP 5672  # RabbitMQ

# Logs
docker logs tli-servicename
journalctl -u tli-infrastructure
journalctl -u tli-applications
```

## Infrastructure Issues (Server 1)

### PostgreSQL Issues

#### Issue: PostgreSQL Container Won't Start
```bash
# Symptoms
docker ps | grep postgres  # No postgres container

# Diagnosis
docker logs tli-postgres
journalctl -u docker

# Common Causes & Solutions
# 1. Port already in use
sudo netstat -tlnp | grep 5432
sudo lsof -i :5432
# Kill conflicting process or change port

# 2. Data directory permissions
sudo chown -R 999:999 /opt/tli/data/postgres
sudo chmod 700 /opt/tli/data/postgres

# 3. Configuration file errors
sudo nano /opt/tli/config/postgres/postgresql.conf
# Check for syntax errors

# 4. Insufficient disk space
df -h /opt/tli/data/postgres
# Clean up or expand storage

# Restart PostgreSQL
cd /opt/tli/config/postgres
docker compose down
docker compose up -d
```

#### Issue: Cannot Connect to PostgreSQL from Server 2
```bash
# Symptoms
psql: could not connect to server: Connection refused

# Diagnosis
# From Server 2
nc -z SERVER1_IP 5432
telnet SERVER1_IP 5432

# From Server 1
docker exec tli-postgres pg_isready -U postgres

# Solutions
# 1. Check firewall on Server 1
sudo ufw status
sudo ufw allow from SERVER2_IP to any port 5432

# 2. Check PostgreSQL configuration
docker exec tli-postgres cat /etc/postgresql/postgresql.conf | grep listen_addresses
# Should be: listen_addresses = '*'

# 3. Check pg_hba.conf
docker exec tli-postgres cat /etc/postgresql/pg_hba.conf
# Should include: host all all SERVER2_IP/32 md5

# 4. Restart PostgreSQL
/opt/tli/manage-infrastructure.sh restart
```

#### Issue: Database Connection Pool Exhausted
```bash
# Symptoms
FATAL: remaining connection slots are reserved for non-replication superuser connections

# Diagnosis
docker exec tli-postgres psql -U postgres -c "SELECT count(*) FROM pg_stat_activity;"
docker exec tli-postgres psql -U postgres -c "SELECT * FROM pg_stat_activity WHERE state = 'active';"

# Solutions
# 1. Increase max_connections
sudo nano /opt/tli/config/postgres/postgresql.conf
# Set: max_connections = 200

# 2. Kill idle connections
docker exec tli-postgres psql -U postgres -c "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE state = 'idle' AND state_change < now() - interval '5 minutes';"

# 3. Restart PostgreSQL
/opt/tli/manage-infrastructure.sh restart
```

### Redis Issues

#### Issue: Redis Memory Issues
```bash
# Symptoms
OOM command not allowed when used memory > 'maxmemory'

# Diagnosis
docker exec tli-redis redis-cli -a "TLI_Redis_2024!@#" info memory
docker exec tli-redis redis-cli -a "TLI_Redis_2024!@#" config get maxmemory

# Solutions
# 1. Increase maxmemory
sudo nano /opt/tli/config/redis/redis.conf
# Set: maxmemory 4gb

# 2. Clear cache
docker exec tli-redis redis-cli -a "TLI_Redis_2024!@#" flushdb

# 3. Check memory policy
docker exec tli-redis redis-cli -a "TLI_Redis_2024!@#" config get maxmemory-policy
# Should be: allkeys-lru

# Restart Redis
/opt/tli/manage-infrastructure.sh restart
```

#### Issue: Redis Connection Refused
```bash
# Symptoms
Could not connect to Redis at SERVER1_IP:6379: Connection refused

# Diagnosis
nc -z SERVER1_IP 6379
docker exec tli-redis redis-cli ping

# Solutions
# 1. Check if Redis is running
docker ps | grep redis

# 2. Check Redis configuration
docker exec tli-redis cat /usr/local/etc/redis/redis.conf | grep bind
# Should include: bind 0.0.0.0

# 3. Check firewall
sudo ufw allow from SERVER2_IP to any port 6379

# 4. Restart Redis
/opt/tli/manage-infrastructure.sh restart
```

### RabbitMQ Issues

#### Issue: RabbitMQ Management Interface Not Accessible
```bash
# Symptoms
Cannot access http://SERVER1_IP:15672

# Diagnosis
curl -I http://localhost:15672
docker logs tli-rabbitmq

# Solutions
# 1. Check if management plugin is enabled
docker exec tli-rabbitmq rabbitmq-plugins list
docker exec tli-rabbitmq rabbitmq-plugins enable rabbitmq_management

# 2. Check firewall
sudo ufw allow from ADMIN_NETWORK to any port 15672

# 3. Check RabbitMQ status
docker exec tli-rabbitmq rabbitmqctl status

# 4. Restart RabbitMQ
/opt/tli/manage-infrastructure.sh restart
```

#### Issue: RabbitMQ Disk Space Alarm
```bash
# Symptoms
{disk_free_limit_alarm,rabbit@hostname}

# Diagnosis
docker exec tli-rabbitmq rabbitmqctl status
df -h /opt/tli/data/rabbitmq

# Solutions
# 1. Clear disk space
sudo find /opt/tli/logs -name "*.log" -mtime +7 -delete
sudo find /opt/tli/backups -name "*.gz" -mtime +30 -delete

# 2. Increase disk space limit
docker exec tli-rabbitmq rabbitmqctl set_disk_free_limit 1GB

# 3. Clear RabbitMQ logs
docker exec tli-rabbitmq find /var/log/rabbitmq -name "*.log" -mtime +7 -delete

# 4. Restart RabbitMQ
/opt/tli/manage-infrastructure.sh restart
```

## Application Issues (Server 2)

### Container Issues

#### Issue: Service Container Keeps Restarting
```bash
# Symptoms
docker ps shows container restarting

# Diagnosis
docker logs tli-servicename
docker inspect tli-servicename

# Common Causes & Solutions
# 1. Database connection issues
# Check if Server 1 is accessible
nc -z SERVER1_IP 5432

# 2. Environment variable issues
docker exec tli-servicename env | grep -E "POSTGRES|REDIS|RABBITMQ"

# 3. Port conflicts
sudo netstat -tlnp | grep PORT_NUMBER

# 4. Memory issues
docker stats tli-servicename
free -h

# 5. Application errors
docker logs tli-servicename 2>&1 | grep -i "error\|exception\|fatal"

# Solutions
# Fix configuration and restart
/opt/tli/manage-applications.sh restart
```

#### Issue: Service Not Responding to Health Checks
```bash
# Symptoms
curl http://localhost:PORT/health returns timeout or error

# Diagnosis
docker logs tli-servicename
netstat -tlnp | grep PORT

# Solutions
# 1. Check if service is listening
docker exec tli-servicename netstat -tlnp

# 2. Check application logs
docker logs tli-servicename --tail 100

# 3. Check health endpoint implementation
curl -v http://localhost:PORT/health

# 4. Restart service
docker restart tli-servicename
```

### Database Migration Issues

#### Issue: Migration Fails
```bash
# Symptoms
Entity Framework migration errors

# Diagnosis
docker logs tli-servicename | grep -i migration
/opt/tli/run-migrations.sh

# Solutions
# 1. Check database connectivity
docker run --rm postgres:15 psql -h SERVER1_IP -U tli_admin -d DATABASE_NAME -c "SELECT 1;"

# 2. Check migration files
ls -la /opt/tli/apps/TLIMicroservices/Services/ServiceName/Migrations/

# 3. Reset database (CAUTION: Data loss)
docker exec tli-postgres psql -U postgres -c "DROP DATABASE IF EXISTS DATABASE_NAME;"
docker exec tli-postgres psql -U postgres -c "CREATE DATABASE DATABASE_NAME;"
/opt/tli/run-migrations.sh

# 4. Manual migration
docker run --rm -v $(pwd):/app -w /app/Services/ServiceName/ServiceName.API \
  -e ConnectionStrings__DefaultConnection="Host=SERVER1_IP;Port=5432;Database=DATABASE_NAME;User Id=tli_admin;Password=*****************" \
  mcr.microsoft.com/dotnet/sdk:8.0 \
  dotnet ef database update
```

### Performance Issues

#### Issue: High CPU Usage
```bash
# Symptoms
docker stats shows high CPU usage

# Diagnosis
docker stats
htop
top -p $(docker inspect -f '{{.State.Pid}}' tli-servicename)

# Solutions
# 1. Check for infinite loops in logs
docker logs tli-servicename | grep -i "loop\|recursive"

# 2. Scale down and restart
docker restart tli-servicename

# 3. Check database queries
docker exec tli-postgres psql -U postgres -c "SELECT query, state, query_start FROM pg_stat_activity WHERE state = 'active';"

# 4. Increase resource limits
# Edit docker-compose file to increase CPU limits
```

#### Issue: High Memory Usage
```bash
# Symptoms
docker stats shows high memory usage

# Diagnosis
docker stats tli-servicename
free -h

# Solutions
# 1. Check for memory leaks
docker logs tli-servicename | grep -i "memory\|leak\|gc"

# 2. Restart service
docker restart tli-servicename

# 3. Increase memory limits
# Edit docker-compose file to increase memory limits

# 4. Check Redis cache size
docker exec tli-redis redis-cli -a "TLI_Redis_2024!@#" info memory
```

## Network Issues

### Connectivity Problems

#### Issue: Server 2 Cannot Reach Server 1
```bash
# Symptoms
Connection timeouts between servers

# Diagnosis
ping SERVER1_IP
traceroute SERVER1_IP
nc -z SERVER1_IP 5432

# Solutions
# 1. Check network configuration
ip route show
ip addr show

# 2. Check firewall rules
sudo ufw status verbose

# 3. Check DNS resolution
nslookup SERVER1_IP
cat /etc/hosts

# 4. Test with different ports
nc -z SERVER1_IP 22  # SSH should work
```

#### Issue: High Network Latency
```bash
# Symptoms
Slow response times between servers

# Diagnosis
ping -c 10 SERVER1_IP
mtr SERVER1_IP

# Solutions
# 1. Check network interface
ethtool eth0
ifconfig eth0

# 2. Check for network congestion
iftop -i eth0
nethogs

# 3. Optimize network settings
echo 'net.core.rmem_max = 16777216' >> /etc/sysctl.conf
echo 'net.core.wmem_max = 16777216' >> /etc/sysctl.conf
sysctl -p
```

## SSL/TLS Issues

#### Issue: SSL Certificate Problems
```bash
# Symptoms
SSL handshake failures

# Diagnosis
openssl s_client -connect localhost:443
curl -k https://localhost:443/health

# Solutions
# 1. Check certificate validity
openssl x509 -in /opt/tli/config/ssl/tli.crt -text -noout

# 2. Regenerate self-signed certificate
openssl req -x509 -newkey rsa:4096 -keyout /opt/tli/config/ssl/tli.key -out /opt/tli/config/ssl/tli.crt -days 365 -nodes

# 3. Check certificate permissions
chmod 600 /opt/tli/config/ssl/tli.key
chmod 644 /opt/tli/config/ssl/tli.crt
```

## Monitoring and Logging Issues

#### Issue: Logs Not Being Generated
```bash
# Symptoms
Empty log files or missing logs

# Diagnosis
ls -la /opt/tli/logs/
docker logs tli-servicename

# Solutions
# 1. Check log directory permissions
sudo chown -R tli:tli /opt/tli/logs
sudo chmod -R 755 /opt/tli/logs

# 2. Check logging configuration
docker exec tli-servicename cat appsettings.json | grep -i logging

# 3. Restart services
/opt/tli/manage-applications.sh restart
```

## Emergency Procedures

### Complete System Recovery

#### Full Infrastructure Restart
```bash
# Server 1 - Infrastructure restart
/opt/tli/manage-infrastructure.sh stop
sleep 30
/opt/tli/manage-infrastructure.sh start
sleep 60
/opt/tli/health-check.sh

# Server 2 - Applications restart
/opt/tli/manage-applications.sh stop
sleep 30
/opt/tli/manage-applications.sh start
sleep 60
/opt/tli/health-check.sh
```

#### Database Recovery
```bash
# Restore from backup (if available)
cd /opt/tli/backups/postgres
gunzip -c TLI_Identity_YYYYMMDD_HHMMSS.sql.gz | docker exec -i tli-postgres psql -U postgres -d TLI_Identity

# Or recreate databases
/opt/tli/config/postgres/setup-databases.sh
/opt/tli/run-migrations.sh
```

## Getting Help

### Log Collection for Support
```bash
# Create support bundle
mkdir -p /tmp/tli-support
cp -r /opt/tli/logs /tmp/tli-support/
docker logs tli-postgres > /tmp/tli-support/postgres.log 2>&1
docker logs tli-redis > /tmp/tli-support/redis.log 2>&1
docker logs tli-rabbitmq > /tmp/tli-support/rabbitmq.log 2>&1
docker ps -a > /tmp/tli-support/containers.txt
systemctl status docker > /tmp/tli-support/docker-status.txt
/opt/tli/health-check.sh > /tmp/tli-support/health-check.txt 2>&1
tar -czf tli-support-$(date +%Y%m%d-%H%M%S).tar.gz -C /tmp tli-support
```

### Contact Information
- **Technical Support**: <EMAIL>
- **Emergency Contact**: <EMAIL>
- **Documentation**: https://docs.tli.com

## Prevention

### Regular Maintenance
```bash
# Weekly maintenance script
cat > /opt/tli/weekly-maintenance.sh << 'EOF'
#!/bin/bash
# Clean old logs
find /opt/tli/logs -name "*.log" -mtime +7 -delete
# Clean old backups
find /opt/tli/backups -name "*.gz" -mtime +30 -delete
# Update Docker images
docker image prune -f
# Run health checks
/opt/tli/health-check.sh
EOF

chmod +x /opt/tli/weekly-maintenance.sh

# Add to cron
(crontab -l 2>/dev/null; echo "0 2 * * 0 /opt/tli/weekly-maintenance.sh") | crontab -
```
