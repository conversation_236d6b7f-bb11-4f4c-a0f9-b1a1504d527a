using Microsoft.Extensions.Logging;
using Moq;
using SubscriptionManagement.Application.Commands.UpgradeSubscription;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Exceptions;
using SubscriptionManagement.Domain.ValueObjects;
using SubscriptionManagement.Infrastructure.Services;
using Shared.Messaging;
using Xunit;

namespace SubscriptionManagement.Tests.Commands
{
    public class UpgradeSubscriptionCommandHandlerTests
    {
        private readonly Mock<ISubscriptionRepository> _subscriptionRepositoryMock;
        private readonly Mock<IPlanRepository> _planRepositoryMock;
        private readonly Mock<IPaymentService> _paymentServiceMock;
        private readonly Mock<IMessageBroker> _messageBrokerMock;
        private readonly Mock<ISubscriptionMetricsService> _metricsServiceMock;
        private readonly Mock<ILogger<UpgradeSubscriptionCommandHandler>> _loggerMock;
        private readonly UpgradeSubscriptionCommandHandler _handler;

        public UpgradeSubscriptionCommandHandlerTests()
        {
            _subscriptionRepositoryMock = new Mock<ISubscriptionRepository>();
            _planRepositoryMock = new Mock<IPlanRepository>();
            _paymentServiceMock = new Mock<IPaymentService>();
            _messageBrokerMock = new Mock<IMessageBroker>();
            _metricsServiceMock = new Mock<ISubscriptionMetricsService>();
            _loggerMock = new Mock<ILogger<UpgradeSubscriptionCommandHandler>>();

            _handler = new UpgradeSubscriptionCommandHandler(
                _subscriptionRepositoryMock.Object,
                _planRepositoryMock.Object,
                _paymentServiceMock.Object,
                _messageBrokerMock.Object,
                _loggerMock.Object);
        }

        [Fact]
        public async Task Handle_ValidUpgrade_ShouldSucceed()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var subscriptionId = Guid.NewGuid();
            var oldPlanId = Guid.NewGuid();
            var newPlanId = Guid.NewGuid();

            var oldPlan = new Plan("Basic Plan", "Basic plan", Money.Create(10, "USD"), BillingCycle.Monthly, PlanType.Basic, UserType.Individual);
            var newPlan = new Plan("Premium Plan", "Premium plan", Money.Create(20, "USD"), BillingCycle.Monthly, PlanType.Premium, UserType.Individual);
            
            var subscription = new Subscription(userId, oldPlan, DateTime.UtcNow, null, true, ProrationMode.CreateProrations);

            var command = new UpgradeSubscriptionCommand
            {
                SubscriptionId = subscriptionId,
                UserId = userId,
                NewPlanId = newPlanId,
                PaymentMethodId = "test_payment_method",
                ProrationMode = ProrationMode.CreateProrations,
                ImmediateUpgrade = true
            };

            _subscriptionRepositoryMock.Setup(x => x.GetByIdAsync(subscriptionId))
                .ReturnsAsync(subscription);

            _planRepositoryMock.Setup(x => x.GetByIdAsync(newPlanId))
                .ReturnsAsync(newPlan);

            _planRepositoryMock.Setup(x => x.GetByIdAsync(oldPlanId))
                .ReturnsAsync(oldPlan);

            _paymentServiceMock.Setup(x => x.ProcessProrationPaymentAsync(It.IsAny<Subscription>(), It.IsAny<decimal>(), It.IsAny<string>()))
                .ReturnsAsync(new PaymentResult { IsSuccess = true, PaymentId = "payment_123" });

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.True(result);
            _subscriptionRepositoryMock.Verify(x => x.UpdateAsync(It.IsAny<Subscription>()), Times.Once);
            _messageBrokerMock.Verify(x => x.PublishAsync("subscription.upgraded", It.IsAny<object>()), Times.Once);
        }

        [Fact]
        public async Task Handle_SubscriptionNotFound_ShouldThrowException()
        {
            // Arrange
            var command = new UpgradeSubscriptionCommand
            {
                SubscriptionId = Guid.NewGuid(),
                UserId = Guid.NewGuid(),
                NewPlanId = Guid.NewGuid()
            };

            _subscriptionRepositoryMock.Setup(x => x.GetByIdAsync(It.IsAny<Guid>()))
                .ReturnsAsync((Subscription?)null);

            // Act & Assert
            await Assert.ThrowsAsync<SubscriptionDomainException>(() => 
                _handler.Handle(command, CancellationToken.None));
        }

        [Fact]
        public async Task Handle_UnauthorizedUser_ShouldThrowException()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var differentUserId = Guid.NewGuid();
            var subscriptionId = Guid.NewGuid();

            var plan = new Plan("Basic Plan", "Basic plan", Money.Create(10, "USD"), BillingCycle.Monthly, PlanType.Basic, UserType.Individual);
            var subscription = new Subscription(userId, plan, DateTime.UtcNow, null, true, ProrationMode.CreateProrations);

            var command = new UpgradeSubscriptionCommand
            {
                SubscriptionId = subscriptionId,
                UserId = differentUserId, // Different user
                NewPlanId = Guid.NewGuid()
            };

            _subscriptionRepositoryMock.Setup(x => x.GetByIdAsync(subscriptionId))
                .ReturnsAsync(subscription);

            // Act & Assert
            await Assert.ThrowsAsync<SubscriptionDomainException>(() => 
                _handler.Handle(command, CancellationToken.None));
        }

        [Fact]
        public async Task Handle_InactiveSubscription_ShouldThrowException()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var subscriptionId = Guid.NewGuid();

            var plan = new Plan("Basic Plan", "Basic plan", Money.Create(10, "USD"), BillingCycle.Monthly, PlanType.Basic, UserType.Individual);
            var subscription = new Subscription(userId, plan, DateTime.UtcNow, null, true, ProrationMode.CreateProrations);
            subscription.Cancel("Test cancellation");

            var command = new UpgradeSubscriptionCommand
            {
                SubscriptionId = subscriptionId,
                UserId = userId,
                NewPlanId = Guid.NewGuid()
            };

            _subscriptionRepositoryMock.Setup(x => x.GetByIdAsync(subscriptionId))
                .ReturnsAsync(subscription);

            // Act & Assert
            await Assert.ThrowsAsync<SubscriptionDomainException>(() => 
                _handler.Handle(command, CancellationToken.None));
        }

        [Fact]
        public async Task Handle_NewPlanNotFound_ShouldThrowException()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var subscriptionId = Guid.NewGuid();
            var newPlanId = Guid.NewGuid();

            var plan = new Plan("Basic Plan", "Basic plan", Money.Create(10, "USD"), BillingCycle.Monthly, PlanType.Basic, UserType.Individual);
            var subscription = new Subscription(userId, plan, DateTime.UtcNow, null, true, ProrationMode.CreateProrations);

            var command = new UpgradeSubscriptionCommand
            {
                SubscriptionId = subscriptionId,
                UserId = userId,
                NewPlanId = newPlanId
            };

            _subscriptionRepositoryMock.Setup(x => x.GetByIdAsync(subscriptionId))
                .ReturnsAsync(subscription);

            _planRepositoryMock.Setup(x => x.GetByIdAsync(newPlanId))
                .ReturnsAsync((Plan?)null);

            // Act & Assert
            await Assert.ThrowsAsync<SubscriptionDomainException>(() => 
                _handler.Handle(command, CancellationToken.None));
        }

        [Fact]
        public async Task Handle_NotAnUpgrade_ShouldThrowException()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var subscriptionId = Guid.NewGuid();
            var newPlanId = Guid.NewGuid();

            var currentPlan = new Plan("Premium Plan", "Premium plan", Money.Create(20, "USD"), BillingCycle.Monthly, PlanType.Premium, UserType.Individual);
            var newPlan = new Plan("Basic Plan", "Basic plan", Money.Create(10, "USD"), BillingCycle.Monthly, PlanType.Basic, UserType.Individual);
            
            var subscription = new Subscription(userId, currentPlan, DateTime.UtcNow, null, true, ProrationMode.CreateProrations);

            var command = new UpgradeSubscriptionCommand
            {
                SubscriptionId = subscriptionId,
                UserId = userId,
                NewPlanId = newPlanId
            };

            _subscriptionRepositoryMock.Setup(x => x.GetByIdAsync(subscriptionId))
                .ReturnsAsync(subscription);

            _planRepositoryMock.Setup(x => x.GetByIdAsync(newPlanId))
                .ReturnsAsync(newPlan);

            // Act & Assert
            await Assert.ThrowsAsync<SubscriptionDomainException>(() => 
                _handler.Handle(command, CancellationToken.None));
        }

        [Fact]
        public async Task Handle_PaymentFailure_ShouldThrowException()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var subscriptionId = Guid.NewGuid();
            var newPlanId = Guid.NewGuid();

            var oldPlan = new Plan("Basic Plan", "Basic plan", Money.Create(10, "USD"), BillingCycle.Monthly, PlanType.Basic, UserType.Individual);
            var newPlan = new Plan("Premium Plan", "Premium plan", Money.Create(20, "USD"), BillingCycle.Monthly, PlanType.Premium, UserType.Individual);
            
            var subscription = new Subscription(userId, oldPlan, DateTime.UtcNow, null, true, ProrationMode.CreateProrations);

            var command = new UpgradeSubscriptionCommand
            {
                SubscriptionId = subscriptionId,
                UserId = userId,
                NewPlanId = newPlanId,
                PaymentMethodId = "test_payment_method",
                ProrationMode = ProrationMode.CreateProrations
            };

            _subscriptionRepositoryMock.Setup(x => x.GetByIdAsync(subscriptionId))
                .ReturnsAsync(subscription);

            _planRepositoryMock.Setup(x => x.GetByIdAsync(newPlanId))
                .ReturnsAsync(newPlan);

            _paymentServiceMock.Setup(x => x.ProcessProrationPaymentAsync(It.IsAny<Subscription>(), It.IsAny<decimal>(), It.IsAny<string>()))
                .ReturnsAsync(new PaymentResult { IsSuccess = false, ErrorMessage = "Payment failed" });

            // Act & Assert
            await Assert.ThrowsAsync<SubscriptionDomainException>(() => 
                _handler.Handle(command, CancellationToken.None));
        }
    }
}
