using Shared.Domain.Common;

namespace MobileWorkflow.Domain.Events;

// UIComponent Events
public record UIComponentCreatedEvent(Guid ComponentId, string Name, string Category, string ComponentType, string CreatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record UIComponentPropertiesUpdatedEvent(Guid ComponentId, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record UIComponentStylingUpdatedEvent(Guid ComponentId, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record UIComponentBehaviorUpdatedEvent(Guid ComponentId, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record UIComponentPlatformConfigUpdatedEvent(Guid ComponentId, string Name, string Platform) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record UIComponentDependencyAddedEvent(Guid ComponentId, string Name, string Dependency, string Version) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record UIComponentDependencyRemovedEvent(Guid ComponentId, string Name, string Dependency) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record UIComponentSetAsCompositeEvent(Guid ComponentId, string Name, string ParentComponentId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record UIComponentActivatedEvent(Guid ComponentId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record UIComponentDeactivatedEvent(Guid ComponentId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record UIComponentVersionUpdatedEvent(Guid ComponentId, string Name, string OldVersion, string NewVersion, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// SyncItem Events
public record SyncItemCreatedEvent(Guid SyncItemId, string EntityType, string EntityId, string Operation) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record SyncItemProcessedEvent(Guid SyncItemId, string EntityType, string EntityId, bool Success) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record SyncItemFailedEvent(Guid SyncItemId, string EntityType, string EntityId, string ErrorMessage) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record SyncItemRetryEvent(Guid SyncItemId, string EntityType, string EntityId, int RetryCount) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record SyncItemConflictDetectedEvent(Guid SyncItemId, string EntityType, string EntityId, string ConflictType) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record SyncItemConflictResolvedEvent(Guid SyncItemId, string EntityType, string EntityId, string Resolution) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record SyncItemDataUpdatedEvent(Guid SyncItemId, string EntityType, string EntityId, string Version) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record SyncItemCancelledEvent(Guid SyncItemId, string EntityType, string EntityId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// WorkflowExecution Events
public record WorkflowExecutionStartedEvent(Guid ExecutionId, Guid WorkflowId, string WorkflowName, string StartedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowExecutionCompletedEvent(Guid ExecutionId, Guid WorkflowId, string WorkflowName, bool Success) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowExecutionFailedEvent(Guid ExecutionId, Guid WorkflowId, string WorkflowName, string ErrorMessage) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// WorkflowStep Events
public record WorkflowStepStartedEvent(Guid StepId, Guid ExecutionId, string StepName, string StepType) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowStepCompletedEvent(Guid StepId, Guid ExecutionId, string StepName, bool Success) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowStepFailedEvent(Guid StepId, Guid ExecutionId, string StepName, string ErrorMessage) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowStepSkippedEvent(Guid StepId, Guid ExecutionId, string StepName, string Reason) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowStepRetryEvent(Guid StepId, Guid ExecutionId, string StepName, int RetryCount) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowStepTimeoutEvent(Guid StepId, Guid ExecutionId, string StepName, TimeSpan Timeout) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowStepCancelledEvent(Guid StepId, Guid ExecutionId, string StepName, string Reason) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// SyncOperation Events
public record SyncOperationStartedEvent(Guid OperationId, string OperationType, string DeviceId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record SyncOperationCompletedEvent(Guid OperationId, string OperationType, string DeviceId, bool Success) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record SyncOperationFailedEvent(Guid OperationId, string OperationType, string DeviceId, string ErrorMessage) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record SyncOperationProgressEvent(Guid OperationId, string OperationType, string DeviceId, int ProgressPercentage) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}



public record WorkflowStepExecutionUpdatedEvent(Guid ExecutionId, Guid StepId, string Status) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// WorkflowTask Events
public record WorkflowTaskCreatedEvent(Guid TaskId, string TaskName, string TaskType, Guid? AssignedTo, int Priority) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowTaskCompletedEvent(Guid TaskId, string TaskName, Guid? AssignedTo, double? Duration) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowTaskFailedEvent(Guid TaskId, string TaskName, Guid? AssignedTo, string ErrorMessage) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowTaskAssignedEvent(Guid TaskId, string TaskName, string AssignedTo) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowTaskCreatedWithRoleEvent(Guid TaskId, string TaskName, string TaskType, Guid? AssignedTo, string? AssignedToRole, int Priority) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowTaskStartedEvent(Guid TaskId, string TaskName, Guid? AssignedTo) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowTaskCancelledEvent(Guid TaskId, string TaskName, Guid? AssignedTo) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowTaskReassignedEvent(Guid TaskId, string TaskName, Guid? OldAssignedTo, Guid? NewAssignedTo) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowTaskDueDateUpdatedEvent(Guid TaskId, string TaskName, DateTime? DueDate) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowTaskOverdueEvent(Guid TaskId, string TaskName, Guid? AssignedTo, DateTime? DueDate) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// WorkflowStep Events
public record WorkflowStepConfigurationUpdatedEvent(Guid StepId, Guid WorkflowId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowStepInputSchemaSetEvent(Guid StepId, Guid WorkflowId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowStepOutputSchemaSetEvent(Guid StepId, Guid WorkflowId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowStepPreviousStepRemovedEvent(Guid StepId, Guid WorkflowId, string Name, string PreviousStep) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowStepSetAsParallelEvent(Guid StepId, Guid WorkflowId, string Name, string? ParentStepId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowStepSetAsSequentialEvent(Guid StepId, Guid WorkflowId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowStepTimeoutSetEvent(Guid StepId, Guid WorkflowId, string Name, double TimeoutMinutes) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowStepRetryPolicySetEvent(Guid StepId, Guid WorkflowId, string Name, int MaxRetries) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowStepErrorHandlingSetEvent(Guid StepId, Guid WorkflowId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowStepSetAsRequiredEvent(Guid StepId, Guid WorkflowId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowStepSetAsOptionalEvent(Guid StepId, Guid WorkflowId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowStepOrderUpdatedEvent(Guid StepId, Guid WorkflowId, string Name, int OldOrder, int NewOrder) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// Additional WorkflowStep Events
public record WorkflowStepConditionAddedEvent(Guid StepId, Guid WorkflowId, string StepName, string Condition) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowStepConditionRemovedEvent(Guid StepId, Guid WorkflowId, string StepName, string Condition) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowStepNextStepAddedEvent(Guid StepId, Guid WorkflowId, string StepName, string NextStep) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowStepNextStepRemovedEvent(Guid StepId, Guid WorkflowId, string StepName, string NextStep) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowStepPreviousStepAddedEvent(Guid StepId, Guid WorkflowId, string StepName, string PreviousStep) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowStepCreatedEvent(Guid StepId, Guid WorkflowId, string StepName, string StepType, int Order) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// MilestoneStep Events
public record MilestoneStepCreatedEvent(Guid StepId, Guid MilestoneTemplateId, string Name, int SequenceNumber) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// WorkflowStepExecution Events
public record WorkflowStepExecutionCreatedEvent(Guid ExecutionId, Guid WorkflowExecutionId, string StepName) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowStepExecutionCompletedEvent(Guid ExecutionId, Guid WorkflowExecutionId, string StepName, bool IsSuccessful) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowStepExecutionAssignedEvent(Guid ExecutionId, Guid WorkflowExecutionId, string StepName, string AssignedTo) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowStepExecutionStartedEvent(Guid ExecutionId, Guid WorkflowExecutionId, string StepName, int AttemptNumber) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowStepExecutionFailedEvent(Guid ExecutionId, Guid WorkflowExecutionId, string StepName, string ErrorMessage, int AttemptNumber) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowStepExecutionSkippedEvent(Guid ExecutionId, Guid WorkflowExecutionId, string StepName, string Reason) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowStepExecutionCancelledEvent(Guid ExecutionId, Guid WorkflowExecutionId, string StepName) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowStepExecutionRetryScheduledEvent(Guid ExecutionId, Guid WorkflowExecutionId, string StepName, int AttemptNumber, DateTime? NextRetryTime) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// Workflow Events
public record WorkflowCreatedEvent(Guid WorkflowId, string Name, string Category, string CreatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowDefinitionUpdatedEvent(Guid WorkflowId, string Name, string Version) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowConfigurationUpdatedEvent(Guid WorkflowId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowActivatedEvent(Guid WorkflowId, string Name, string Category) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowDeactivatedEvent(Guid WorkflowId, string Name, string Category) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// SyncOperation Events
public record SyncOperationProgressUpdatedEvent(Guid OperationId, Guid UserId, int ProcessedItems, int TotalItems, double ProgressPercentage) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record SyncOperationConflictDetectedEvent(Guid OperationId, Guid UserId, string ItemId, string ConflictType) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record SyncOperationItemFailedEvent(Guid OperationId, Guid UserId, string ItemId, string Error) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record SyncOperationRetryScheduledEvent(Guid OperationId, Guid UserId, Guid DeviceId, int RetryCount, DateTime? NextRetryTime) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record SyncOperationConflictResolvedEvent(Guid OperationId, Guid UserId, string ItemId, string Resolution) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// FormStep Events
public record FormStepCreatedEvent(Guid StepId, Guid FormDefinitionId, string Name, int Order) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormStepConfigurationUpdatedEvent(Guid StepId, Guid FormDefinitionId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormStepValidationRulesUpdatedEvent(Guid StepId, Guid FormDefinitionId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormStepConditionalLogicUpdatedEvent(Guid StepId, Guid FormDefinitionId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormStepStylingUpdatedEvent(Guid StepId, Guid FormDefinitionId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormStepNextStepConditionSetEvent(Guid StepId, Guid FormDefinitionId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormStepPreviousStepConditionSetEvent(Guid StepId, Guid FormDefinitionId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormStepSetAsRequiredEvent(Guid StepId, Guid FormDefinitionId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormStepSetAsOptionalEvent(Guid StepId, Guid FormDefinitionId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormStepActivatedEvent(Guid StepId, Guid FormDefinitionId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormStepDeactivatedEvent(Guid StepId, Guid FormDefinitionId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormStepOrderUpdatedEvent(Guid StepId, Guid FormDefinitionId, string Name, int OldOrder, int NewOrder) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// ComponentUsage Events
public record ComponentUsageRecordedEvent(Guid UsageId, Guid ComponentId, Guid UserId, string Platform, string Context) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ComponentUsageSuccessEvent(Guid UsageId, Guid ComponentId, Guid UserId, double DurationMs) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ComponentUsageFailedEvent(Guid UsageId, Guid ComponentId, Guid UserId, string ErrorMessage) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ComponentLibraryCreatedEvent(Guid LibraryId, string Name, string Version, string CreatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ComponentLibraryVersionUpdatedEvent(Guid LibraryId, string Name, string OldVersion, string NewVersion, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ComponentLibraryConfigurationUpdatedEvent(Guid LibraryId, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ComponentLibraryDependencyAddedEvent(Guid LibraryId, string Name, string Dependency, string Version) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ComponentLibraryDependencyRemovedEvent(Guid LibraryId, string Name, string Dependency) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ComponentLibraryActivatedEvent(Guid LibraryId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ComponentLibraryDeactivatedEvent(Guid LibraryId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// ConflictResolution Events
public record ConflictResolutionDetectedEvent(Guid ResolutionId, string EntityType, string EntityId, string ConflictType, string Severity) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ConflictResolutionResolvedEvent(Guid ResolutionId, string EntityType, string EntityId, string ResolutionStrategy, string ResolvedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ConflictResolutionFailedEvent(Guid ResolutionId, string EntityType, string EntityId, string ErrorMessage) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// FormField Events
public record FormFieldCreatedEvent(Guid FieldId, Guid FormDefinitionId, string Name, string FieldType, int Order) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormFieldConfigurationUpdatedEvent(Guid FieldId, Guid FormDefinitionId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormFieldValidationRulesUpdatedEvent(Guid FieldId, Guid FormDefinitionId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormFieldConditionalLogicUpdatedEvent(Guid FieldId, Guid FormDefinitionId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormFieldStylingUpdatedEvent(Guid FieldId, Guid FormDefinitionId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormFieldDataBindingUpdatedEvent(Guid FieldId, Guid FormDefinitionId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormFieldSetAsRequiredEvent(Guid FieldId, Guid FormDefinitionId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormFieldSetAsOptionalEvent(Guid FieldId, Guid FormDefinitionId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormFieldSetAsReadOnlyEvent(Guid FieldId, Guid FormDefinitionId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormFieldSetAsEditableEvent(Guid FieldId, Guid FormDefinitionId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormFieldActivatedEvent(Guid FieldId, Guid FormDefinitionId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormFieldDeactivatedEvent(Guid FieldId, Guid FormDefinitionId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormFieldOrderUpdatedEvent(Guid FieldId, Guid FormDefinitionId, string Name, int OldOrder, int NewOrder) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormFieldDefaultValueSetEvent(Guid FieldId, Guid FormDefinitionId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormFieldMovedToStepEvent(Guid FieldId, Guid FormDefinitionId, string Name, Guid? StepId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// NotificationTemplate Domain Events
public record NotificationTemplateCreatedEvent(Guid NotificationTemplateId, string Name, string Category, string Type, string CreatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record NotificationTemplateContentUpdatedEvent(Guid NotificationTemplateId, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record NotificationTemplateStylingUpdatedEvent(Guid NotificationTemplateId, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record NotificationTemplateConfigurationUpdatedEvent(Guid NotificationTemplateId, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record NotificationTemplatePlatformConfigUpdatedEvent(Guid NotificationTemplateId, string Name, string Platform) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record NotificationTemplateActivatedEvent(Guid NotificationTemplateId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record NotificationTemplateDeactivatedEvent(Guid NotificationTemplateId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// NotificationCampaign Domain Events
public record NotificationCampaignCreatedEvent(Guid CampaignId, string Name, string Type, string CreatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record NotificationCampaignTargetingUpdatedEvent(Guid CampaignId, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record NotificationCampaignVariablesUpdatedEvent(Guid CampaignId, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record NotificationCampaignConfigurationUpdatedEvent(Guid CampaignId, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record NotificationCampaignScheduledEvent(Guid CampaignId, string Name, DateTime ScheduledAt, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record NotificationCampaignStartedEvent(Guid CampaignId, string Name, int ActualRecipients) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record NotificationCampaignPausedEvent(Guid CampaignId, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record NotificationCampaignResumedEvent(Guid CampaignId, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record NotificationCampaignCompletedEvent(Guid CampaignId, string Name, int DeliveredCount, int FailedCount) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record NotificationCampaignCancelledEvent(Guid CampaignId, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// NotificationDelivery Domain Events
public record NotificationDeliveryCreatedEvent(Guid DeliveryId, Guid CampaignId, Guid UserId, string Platform) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record NotificationDeliverySentEvent(Guid DeliveryId, Guid CampaignId, Guid UserId, string Platform, string? ExternalId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record NotificationDeliveryDeliveredEvent(Guid DeliveryId, Guid CampaignId, Guid UserId, string Platform) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record NotificationDeliveryFailedEvent(Guid DeliveryId, Guid CampaignId, Guid UserId, string Platform, string ErrorMessage, string? ErrorCode) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record NotificationDeliveryOpenedEvent(Guid DeliveryId, Guid CampaignId, Guid UserId, string Platform) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record NotificationDeliveryClickedEvent(Guid DeliveryId, Guid CampaignId, Guid UserId, string Platform) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record NotificationDeliveryRetryScheduledEvent(Guid DeliveryId, Guid CampaignId, Guid UserId, string Platform, int RetryCount, DateTime? NextRetryAt) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// DeviceToken Domain Events
public record DeviceTokenRegisteredEvent(Guid TokenId, Guid UserId, string Platform, string TokenPreview) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record DeviceTokenUpdatedEvent(Guid TokenId, Guid UserId, string Platform) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record DeviceTokenDeactivatedEvent(Guid TokenId, Guid UserId, string Platform) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record DeviceTokenReactivatedEvent(Guid TokenId, Guid UserId, string Platform) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// UserBehaviorPattern Events
public record UserBehaviorPatternDetectedEvent(Guid PatternId, Guid UserId, string PatternType, string PatternName, double Confidence) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record UserBehaviorPatternUpdatedEvent(Guid PatternId, Guid UserId, string PatternName, int Frequency, double Confidence) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// PerformanceMetric Events
public record PerformanceMetricRecordedEvent(Guid MetricId, Guid UserId, string MetricType, string MetricName, double Value, string Unit) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// CrashReport Events
public record CrashReportCreatedEvent(Guid ReportId, Guid UserId, string CrashType, bool IsFatal, string Platform, string AppVersion) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// AnalyticsEvent Events
public record AnalyticsEventRecordedEvent(Guid EventId, Guid UserId, string EventType, string EventName, string Category, string Platform) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// UserSession Events
public record UserSessionStartedEvent(Guid SessionId, Guid UserId, string Platform, string AppVersion) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record UserSessionEndedEvent(Guid SessionId, Guid UserId, double Duration, int EventCount) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// BiometricProfile Events
public record BiometricProfileCreatedEvent(Guid ProfileId, Guid UserId, string DeviceId, string Platform, List<string> SupportedBiometrics) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record BiometricProfileBiometricEnabledEvent(Guid ProfileId, Guid UserId, string DeviceId, string BiometricType) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record BiometricProfileBiometricDisabledEvent(Guid ProfileId, Guid UserId, string DeviceId, string BiometricType) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record BiometricProfileDeactivatedEvent(Guid ProfileId, Guid UserId, string DeviceId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record BiometricProfileReactivatedEvent(Guid ProfileId, Guid UserId, string DeviceId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record BiometricProfileSecuritySettingsUpdatedEvent(Guid ProfileId, Guid UserId, string DeviceId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// BiometricAuthenticationAttempt Events
public record BiometricAuthenticationAttemptCreatedEvent(Guid AttemptId, Guid UserId, string DeviceId, string BiometricType, string Result) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// BiometricPolicy Events
public record BiometricPolicyCreatedEvent(Guid PolicyId, string Name, string Platform, string CreatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record BiometricPolicyPolicyRulesUpdatedEvent(Guid PolicyId, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record BiometricPolicySecurityRequirementsUpdatedEvent(Guid PolicyId, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record BiometricPolicyFallbackOptionsUpdatedEvent(Guid PolicyId, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record BiometricPolicySetAsDefaultEvent(Guid PolicyId, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record BiometricPolicyRemovedAsDefaultEvent(Guid PolicyId, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record BiometricPolicyActivatedEvent(Guid PolicyId, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record BiometricPolicyDeactivatedEvent(Guid PolicyId, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// BarcodeScanning Events - ScanSession Events
public record ScanSessionStartedEvent(Guid SessionId, Guid UserId, string SessionName, string SessionType) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ScanSessionValidationRulesSetEvent(Guid SessionId, string SessionName, int RuleCount) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ScanSessionScanAddedEvent(Guid SessionId, string SessionName, Guid ScanId, string Status) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ScanSessionCompletedEvent(Guid SessionId, string SessionName, int TotalScans, int SuccessfulScans, int FailedScans) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ScanSessionCancelledEvent(Guid SessionId, string SessionName, int TotalScans) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ScanSessionPausedEvent(Guid SessionId, string SessionName) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ScanSessionResumedEvent(Guid SessionId, string SessionName) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// BarcodeScanning Events - ScanResult Events
public record ScanResultCreatedEvent(Guid ScanId, Guid SessionId, Guid UserId, string BarcodeFormat, string BarcodeData) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ScanResultValidatedEvent(Guid ScanId, Guid SessionId, bool IsValid) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ScanResultMarkedAsDuplicateEvent(Guid ScanId, Guid SessionId, string BarcodeData) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ScanResultFailedEvent(Guid ScanId, Guid SessionId, string ErrorMessage, string ErrorCode) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ScanResultProcessedEvent(Guid ScanId, Guid SessionId, object ProcessingResult) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// BarcodeScanning Events - BarcodeTemplate Events
public record BarcodeTemplateCreatedEvent(Guid TemplateId, string Name, string BarcodeFormat, string CreatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record BarcodeTemplateValidationRulesUpdatedEvent(Guid TemplateId, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record BarcodeTemplateProcessingRulesUpdatedEvent(Guid TemplateId, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record BarcodeTemplateActivatedEvent(Guid TemplateId, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record BarcodeTemplateDeactivatedEvent(Guid TemplateId, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// BackgroundJob Events
public record BackgroundJobCreatedEvent(Guid JobId, string JobType, string JobName, string Queue, int Priority, DateTime? ScheduledAt) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record BackgroundJobScheduledEvent(Guid JobId, string JobType, string JobName, DateTime ScheduledAt) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record BackgroundJobStartedEvent(Guid JobId, string JobType, string JobName, string WorkerId, DateTime StartedAt) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record BackgroundJobCompletedEvent(Guid JobId, string JobType, string JobName, double? Duration, Dictionary<string, object> Result) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record BackgroundJobFailedEvent(Guid JobId, string JobType, string JobName, string ErrorMessage, int RetryCount) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record BackgroundJobCancelledEvent(Guid JobId, string JobType, string JobName, string Reason) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record BackgroundJobRetriedEvent(Guid JobId, string JobType, string JobName, int RetryCount, DateTime? NextRetryAt) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record BackgroundJobProgressUpdatedEvent(Guid JobId, string JobType, string JobName, Dictionary<string, object> Progress) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// MobileApp Events
public record MobileAppCreatedEvent(Guid AppId, string Name, string Version, string Platform) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record MobileAppVersionUpdatedEvent(Guid AppId, string Version, string Platform) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record MobileAppConfigurationUpdatedEvent(Guid AppId, string Platform) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record MobileAppActivatedEvent(Guid AppId, string Name, string Platform) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record MobileAppDeactivatedEvent(Guid AppId, string Name, string Platform) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// MilestoneTemplate Events
public record MilestoneTemplateCreatedEvent(Guid MilestoneTemplateId, string Name, string Type, string Category, string CreatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record MilestoneTemplateUpdatedEvent(Guid MilestoneTemplateId, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record MilestoneTemplateActivatedEvent(Guid MilestoneTemplateId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record MilestoneTemplateDeactivatedEvent(Guid MilestoneTemplateId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record MilestoneTemplateSetAsDefaultEvent(Guid MilestoneTemplateId, string Name, string Type, string Category) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record MilestoneTemplateRemovedAsDefaultEvent(Guid MilestoneTemplateId, string Name, string Type, string Category) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record MilestoneTemplateStepAddedEvent(Guid MilestoneTemplateId, string Name, string StepName, int SequenceNumber) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record MilestoneTemplateStepRemovedEvent(Guid MilestoneTemplateId, string Name, string StepName) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record MilestoneTemplateStepsReorderedEvent(Guid MilestoneTemplateId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record MilestoneTemplateUsageIncrementedEvent(Guid MilestoneTemplateId, string Name, int UsageCount) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record MilestoneTemplateConfigurationUpdatedEvent(Guid MilestoneTemplateId, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// MobileSession Events
public record MobileSessionStartedEvent(Guid MobileSessionId, Guid UserId, string Platform, string DeviceId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record MobileSessionDataUpdatedEvent(Guid MobileSessionId, Guid UserId, string Key) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record MobileSessionSyncCompletedEvent(Guid MobileSessionId, Guid UserId, DateTime LastSyncTime) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record MobileSessionEndedEvent(Guid MobileSessionId, Guid UserId, double Duration) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// MobileTesting Events
// TestSuite Events
public record TestSuiteCreatedEvent(Guid TestSuiteId, string Name, string TestType, string Platform, string CreatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record TestSuiteConfigurationUpdatedEvent(Guid TestSuiteId, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record TestSuiteActivatedEvent(Guid TestSuiteId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record TestSuiteDeactivatedEvent(Guid TestSuiteId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// TestCase Events
public record TestCaseCreatedEvent(Guid TestCaseId, Guid TestSuiteId, string Name, string Category, bool IsAutomated, string CreatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record TestCaseUpdatedEvent(Guid TestCaseId, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record TestCaseActivatedEvent(Guid TestCaseId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record TestCaseDeactivatedEvent(Guid TestCaseId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// TestExecution Events
public record TestExecutionCreatedEvent(Guid TestExecutionId, Guid TestSuiteId, string ExecutionName, string ExecutedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record TestExecutionStartedEvent(Guid TestExecutionId, Guid TestSuiteId, string ExecutionName) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record TestExecutionCompletedEvent(Guid TestExecutionId, Guid TestSuiteId, string ExecutionName, double? Duration) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record TestExecutionFailedEvent(Guid TestExecutionId, Guid TestSuiteId, string ExecutionName, string ErrorMessage) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record TestExecutionCancelledEvent(Guid TestExecutionId, Guid TestSuiteId, string ExecutionName) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// TestCaseExecution Events
public record TestCaseExecutionCreatedEvent(Guid TestCaseExecutionId, Guid TestExecutionId, Guid TestCaseId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record TestCaseExecutionStartedEvent(Guid TestCaseExecutionId, Guid TestExecutionId, Guid TestCaseId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record TestCaseExecutionPassedEvent(Guid TestCaseExecutionId, Guid TestExecutionId, Guid TestCaseId, double? Duration) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record TestCaseExecutionFailedEvent(Guid TestCaseExecutionId, Guid TestExecutionId, Guid TestCaseId, string ErrorMessage) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record TestCaseExecutionSkippedEvent(Guid TestCaseExecutionId, Guid TestExecutionId, Guid TestCaseId, string Reason) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// WorkflowTemplate Events
public record WorkflowTemplateCreatedEvent(Guid WorkflowTemplateId, string Name, string Category, string Industry, string CreatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowTemplateDefinitionUpdatedEvent(Guid WorkflowTemplateId, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowTemplateConfigurationUpdatedEvent(Guid WorkflowTemplateId, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowTemplateParameterSchemaUpdatedEvent(Guid WorkflowTemplateId, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowTemplateValidationRulesUpdatedEvent(Guid WorkflowTemplateId, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowTemplateTagAddedEvent(Guid WorkflowTemplateId, string Name, string Tag) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowTemplateTagRemovedEvent(Guid WorkflowTemplateId, string Name, string Tag) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowTemplateRequiredRoleAddedEvent(Guid WorkflowTemplateId, string Name, string Role) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowTemplateRequiredRoleRemovedEvent(Guid WorkflowTemplateId, string Name, string Role) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowTemplatePrerequisiteSetEvent(Guid WorkflowTemplateId, string Name, string Key) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowTemplatePrerequisiteRemovedEvent(Guid WorkflowTemplateId, string Name, string Key) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowTemplateMadePublicEvent(Guid WorkflowTemplateId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowTemplateMadePrivateEvent(Guid WorkflowTemplateId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowTemplateActivatedEvent(Guid WorkflowTemplateId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowTemplateDeactivatedEvent(Guid WorkflowTemplateId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowTemplateUsageIncrementedEvent(Guid WorkflowTemplateId, string Name, int UsageCount) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowTemplateRatingUpdatedEvent(Guid WorkflowTemplateId, string Name, double AverageRating) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record WorkflowTemplateVersionUpdatedEvent(Guid WorkflowTemplateId, string Name, string OldVersion, string NewVersion, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// ManagedDevice Events
public record ManagedDeviceEnrolledEvent(Guid ManagedDeviceId, Guid UserId, string DeviceId, string Platform) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ManagedDeviceInfoUpdatedEvent(Guid ManagedDeviceId, Guid UserId, string DeviceId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ManagedDeviceSecurityInfoUpdatedEvent(Guid ManagedDeviceId, Guid UserId, string DeviceId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ManagedDeviceComplianceChangedEvent(Guid ManagedDeviceId, Guid UserId, string DeviceId, bool IsCompliant, bool WasCompliant) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ManagedDevicePolicyAssignedEvent(Guid ManagedDeviceId, Guid UserId, string DeviceId, Guid PolicyId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ManagedDevicePolicyUnassignedEvent(Guid ManagedDeviceId, Guid UserId, string DeviceId, Guid PolicyId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ManagedDeviceConfigurationUpdatedEvent(Guid ManagedDeviceId, Guid UserId, string DeviceId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ManagedDeviceSuspendedEvent(Guid ManagedDeviceId, Guid UserId, string DeviceId, string Reason) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ManagedDeviceReactivatedEvent(Guid ManagedDeviceId, Guid UserId, string DeviceId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ManagedDeviceCompromisedEvent(Guid ManagedDeviceId, Guid UserId, string DeviceId, string Reason) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ManagedDeviceLostEvent(Guid ManagedDeviceId, Guid UserId, string DeviceId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ManagedDeviceUnenrolledEvent(Guid ManagedDeviceId, Guid UserId, string DeviceId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// Geofence Events
public record GeofenceCreatedEvent(Guid GeofenceId, string Name, string Type, string TriggerType, string CreatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record GeofenceCoordinatesUpdatedEvent(Guid GeofenceId, string Name, string Type) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record GeofenceTriggerSettingsUpdatedEvent(Guid GeofenceId, string Name, string TriggerType, TimeSpan? DwellTime) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record GeofenceConditionsUpdatedEvent(Guid GeofenceId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record GeofenceUserTargetedEvent(Guid GeofenceId, string Name, Guid UserId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record GeofenceUserUntargetedEvent(Guid GeofenceId, string Name, Guid UserId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record GeofenceGroupTargetedEvent(Guid GeofenceId, string Name, string GroupName) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record GeofenceGroupUntargetedEvent(Guid GeofenceId, string Name, string GroupName) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record GeofenceActivatedEvent(Guid GeofenceId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record GeofenceDeactivatedEvent(Guid GeofenceId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// GeofenceEvent Events
public record GeofenceEventCreatedEvent(Guid GeofenceEventId, Guid GeofenceId, Guid UserId, string EventType, double Latitude, double Longitude) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record GeofenceEventProcessedEvent(Guid GeofenceEventId, Guid GeofenceId, Guid UserId, string EventType, Dictionary<string, object> ProcessingResult) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record UserGeofenceStatusCreatedEvent(Guid StatusId, Guid GeofenceId, Guid UserId, string Status) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record UserGeofenceStatusChangedEvent(Guid StatusId, Guid GeofenceId, Guid UserId, string PreviousStatus, string NewStatus) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record LocationUpdateReceivedEvent(Guid LocationUpdateId, Guid UserId, double Latitude, double Longitude, DateTime Timestamp) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record LocationUpdateProcessedEvent(Guid LocationUpdateId, Guid UserId, double Latitude, double Longitude) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// ComponentTheme Events
public record ComponentThemeCreatedEvent(Guid ComponentThemeId, string Name, string CreatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ComponentThemeColorsUpdatedEvent(Guid ComponentThemeId, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ComponentThemeTypographyUpdatedEvent(Guid ComponentThemeId, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ComponentThemeSpacingUpdatedEvent(Guid ComponentThemeId, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ComponentThemeSetAsDefaultEvent(Guid ComponentThemeId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ComponentThemeRemovedAsDefaultEvent(Guid ComponentThemeId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ComponentThemeActivatedEvent(Guid ComponentThemeId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ComponentThemeDeactivatedEvent(Guid ComponentThemeId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ComponentThemePlatformOverrideSetEvent(Guid ComponentThemeId, string Name, string Platform) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ComponentThemeParentThemeSetEvent(Guid ComponentThemeId, string Name, string ParentThemeId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ComponentThemeCustomPropertyAddedEvent(Guid ComponentThemeId, string Name, string Key) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ComponentThemeCustomPropertyRemovedEvent(Guid ComponentThemeId, string Name, string Key) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// DeviceCommand Events
public record DeviceCommandCreatedEvent(Guid DeviceCommandId, Guid DeviceId, string CommandType, string CommandName, string CreatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record DeviceCommandSentEvent(Guid DeviceCommandId, Guid DeviceId, string CommandType, string CommandName) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record DeviceCommandAcknowledgedEvent(Guid DeviceCommandId, Guid DeviceId, string CommandType, string CommandName) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record DeviceCommandCompletedEvent(Guid DeviceCommandId, Guid DeviceId, string CommandType, string CommandName, object Result) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record DeviceCommandFailedEvent(Guid DeviceCommandId, Guid DeviceId, string CommandType, string CommandName, string ErrorMessage, string ErrorCode) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record DeviceCommandExpiredEvent(Guid DeviceCommandId, Guid DeviceId, string CommandType, string CommandName) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record DeviceCommandRetriedEvent(Guid DeviceCommandId, Guid DeviceId, string CommandType, string CommandName, int RetryCount) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// DevicePolicyAssignment Events
public record DevicePolicyAssignmentCreatedEvent(Guid DevicePolicyAssignmentId, Guid DeviceId, Guid PolicyId, string AssignedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record DevicePolicyAssignmentAppliedEvent(Guid DevicePolicyAssignmentId, Guid DeviceId, Guid PolicyId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record DevicePolicyAssignmentFailedEvent(Guid DevicePolicyAssignmentId, Guid DeviceId, Guid PolicyId, string ErrorMessage) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// DeviceComplianceCheck Events
public record DeviceComplianceCheckCompletedEvent(Guid DeviceComplianceCheckId, Guid DeviceId, bool IsCompliant, int ViolationCount, string CheckType) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// DevicePolicy Events
public record DevicePolicyCreatedEvent(Guid Id, string Name, string Category, string Platform, string CreatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record DevicePolicyRulesUpdatedEvent(Guid Id, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record DevicePolicyConfigurationUpdatedEvent(Guid Id, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record DevicePolicyEnforcementUpdatedEvent(Guid Id, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record DevicePolicyConditionsUpdatedEvent(Guid Id, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record DevicePolicyPriorityChangedEvent(Guid Id, string Name, int OldPriority, int NewPriority, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record DevicePolicySetAsDefaultEvent(Guid Id, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record DevicePolicyRemovedAsDefaultEvent(Guid Id, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record DevicePolicyActivatedEvent(Guid Id, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record DevicePolicyDeactivatedEvent(Guid Id, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// SyncFile Events
public record SyncFileCreatedEvent(Guid Id, string FileName, string FilePath, Guid UserId, string DeviceId, long FileSize) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record SyncFileUpdatedEvent(Guid Id, string FileName, int Version, long FileSize, string DeviceId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record SyncFileSyncStartedEvent(Guid Id, string FileName, int Version) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record SyncFileSyncedEvent(Guid Id, string FileName, int Version, DateTime? LastSyncedAt) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record SyncFileSyncFailedEvent(Guid Id, string FileName, int Version, string Reason) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record SyncFileConflictEvent(Guid Id, string FileName, int Version, string ConflictReason) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record SyncFileConflictResolvedEvent(Guid Id, string FileName, string Resolution, string ResolvedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record SyncFileDeletedEvent(Guid Id, string FileName, DateTime? DeletedAt) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record SyncFileRestoredEvent(Guid Id, string FileName) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// FileVersion Events
public record FileVersionCreatedEvent(Guid Id, Guid SyncFileId, int VersionNumber, string FileHash, long FileSize) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FileVersionDeactivatedEvent(Guid Id, Guid SyncFileId, int VersionNumber) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FileVersionActivatedEvent(Guid Id, Guid SyncFileId, int VersionNumber) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// FileSyncOperation Events
public record FileSyncOperationCreatedEvent(Guid Id, Guid SyncFileId, string OperationType, string SourceDevice, long TotalBytes) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FileSyncOperationStartedEvent(Guid Id, Guid SyncFileId, string OperationType) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FileSyncOperationProgressUpdatedEvent(Guid Id, Guid SyncFileId, long BytesTransferred, long TotalBytes, double ProgressPercentage) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FileSyncOperationCompletedEvent(Guid Id, Guid SyncFileId, string OperationType, TimeSpan? Duration) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FileSyncOperationFailedEvent(Guid Id, Guid SyncFileId, string OperationType, string ErrorMessage, string ErrorCode) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FileSyncOperationCancelledEvent(Guid Id, Guid SyncFileId, string OperationType) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// OfflineData Events
public record OfflineDataCreatedEvent(Guid OfflineDataId, Guid UserId, string DataType, string Action, int Priority) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record OfflineDataSyncedEvent(Guid OfflineDataId, Guid UserId, string DataType, DateTime SyncedAt) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record OfflineDataSyncFailedEvent(Guid OfflineDataId, Guid UserId, string DataType, int SyncAttempts, string Error) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record OfflineDataUpdatedEvent(Guid OfflineDataId, Guid UserId, string DataType) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// SyncPolicy Events
public record SyncPolicyCreatedEvent(Guid PolicyId, string Name, string? EntityType, int Priority) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record SyncPolicyActivatedEvent(Guid PolicyId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record SyncPolicyDeactivatedEvent(Guid PolicyId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record SyncPolicySetAsDefaultEvent(Guid PolicyId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record SyncPolicyRulesUpdatedEvent(Guid PolicyId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record SyncPolicyConflictRulesUpdatedEvent(Guid PolicyId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record SyncPolicyRetryPoliciesUpdatedEvent(Guid PolicyId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// FormSubmission Events
public record FormSubmissionCreatedEvent(Guid SubmissionId, Guid FormDefinitionId, Guid SubmittedBy, string Status) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormSubmissionDataUpdatedEvent(Guid SubmissionId, Guid FormDefinitionId, Guid SubmittedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormSubmissionDataMergedEvent(Guid SubmissionId, Guid FormDefinitionId, Guid SubmittedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormSubmissionSubmittedEvent(Guid SubmissionId, Guid FormDefinitionId, Guid SubmittedBy, DateTime? SubmittedAt) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormSubmissionApprovedEvent(Guid SubmissionId, Guid FormDefinitionId, Guid SubmittedBy, Guid? ReviewedBy, DateTime? ReviewedAt) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormSubmissionRejectedEvent(Guid SubmissionId, Guid FormDefinitionId, Guid SubmittedBy, Guid? ReviewedBy, DateTime? ReviewedAt) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormSubmissionProcessingStartedEvent(Guid SubmissionId, Guid FormDefinitionId, Guid SubmittedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormSubmissionProcessingCompletedEvent(Guid SubmissionId, Guid FormDefinitionId, Guid SubmittedBy, DateTime? CompletedAt) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormSubmissionStepAdvancedEvent(Guid SubmissionId, Guid FormDefinitionId, Guid SubmittedBy, int CurrentStep, int TotalSteps) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormSubmissionStepReversedEvent(Guid SubmissionId, Guid FormDefinitionId, Guid SubmittedBy, int CurrentStep, int TotalSteps) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormSubmissionStepChangedEvent(Guid SubmissionId, Guid FormDefinitionId, Guid SubmittedBy, int OldStep, int NewStep) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormSubmissionValidationResultsSetEvent(Guid SubmissionId, Guid FormDefinitionId, Guid SubmittedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// FormFieldOption Events
public record FormFieldOptionCreatedEvent(Guid OptionId, Guid FormFieldId, string Value, string DisplayText, int Order) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormFieldOptionDisplayTextUpdatedEvent(Guid OptionId, Guid FormFieldId, string Value, string DisplayText) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormFieldOptionDescriptionUpdatedEvent(Guid OptionId, Guid FormFieldId, string Value) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormFieldOptionOrderUpdatedEvent(Guid OptionId, Guid FormFieldId, string Value, int OldOrder, int NewOrder) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormFieldOptionSetAsDefaultEvent(Guid OptionId, Guid FormFieldId, string Value) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormFieldOptionRemovedAsDefaultEvent(Guid OptionId, Guid FormFieldId, string Value) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormFieldOptionActivatedEvent(Guid OptionId, Guid FormFieldId, string Value) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormFieldOptionDeactivatedEvent(Guid OptionId, Guid FormFieldId, string Value) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormFieldOptionConfigurationUpdatedEvent(Guid OptionId, Guid FormFieldId, string Value) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormFieldOptionConditionalLogicUpdatedEvent(Guid OptionId, Guid FormFieldId, string Value) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormFieldOptionStylingUpdatedEvent(Guid OptionId, Guid FormFieldId, string Value) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormFieldOptionIconSetEvent(Guid OptionId, Guid FormFieldId, string Value, string? IconUrl) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormFieldOptionColorSetEvent(Guid OptionId, Guid FormFieldId, string Value, string? Color) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// FormDefinition Events
public record FormDefinitionCreatedEvent(Guid FormDefinitionId, string Name, string Category, bool IsTemplate, string CreatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormDefinitionConfigurationUpdatedEvent(Guid FormDefinitionId, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormDefinitionStylingUpdatedEvent(Guid FormDefinitionId, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormDefinitionValidationRulesUpdatedEvent(Guid FormDefinitionId, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormDefinitionConditionalLogicUpdatedEvent(Guid FormDefinitionId, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormDefinitionDataBindingUpdatedEvent(Guid FormDefinitionId, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormDefinitionSetAsMultiStepEvent(Guid FormDefinitionId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormDefinitionSetAsSingleStepEvent(Guid FormDefinitionId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormDefinitionActivatedEvent(Guid FormDefinitionId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormDefinitionDeactivatedEvent(Guid FormDefinitionId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormDefinitionTagAddedEvent(Guid FormDefinitionId, string Name, string Tag) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormDefinitionTagRemovedEvent(Guid FormDefinitionId, string Name, string Tag) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormDefinitionRequiredRoleAddedEvent(Guid FormDefinitionId, string Name, string Role) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormDefinitionRequiredRoleRemovedEvent(Guid FormDefinitionId, string Name, string Role) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record FormDefinitionVersionUpdatedEvent(Guid FormDefinitionId, string Name, string OldVersion, string NewVersion, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record OfflineDataConflictResolvedEvent(Guid OfflineDataId, Guid UserId, string DataType, string Resolution) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// PWA Events
public record PWAInstallationCreatedEvent(Guid PWAInstallationId, Guid UserId, string DeviceId, string Platform, string AppVersion) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record PWAInstallationCapabilitiesUpdatedEvent(Guid PWAInstallationId, Guid UserId, string DeviceId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record PWAInstallationDeactivatedEvent(Guid PWAInstallationId, Guid UserId, string DeviceId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record PWAInstallationReactivatedEvent(Guid PWAInstallationId, Guid UserId, string DeviceId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record PWASessionStartedEvent(Guid PWASessionId, Guid PWAInstallationId, Guid UserId, string SessionType) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record PWASessionEndedEvent(Guid PWASessionId, Guid PWAInstallationId, Guid UserId, double? Duration) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ServiceWorkerEventCreatedEvent(Guid ServiceWorkerEventId, Guid PWAInstallationId, string EventType, bool IsSuccessful) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ServiceWorkerEventFailedEvent(Guid ServiceWorkerEventId, Guid PWAInstallationId, string EventType, string ErrorMessage) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record PWAManifestCreatedEvent(Guid PWAManifestId, string Name, string Version, string CreatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record PWAManifestUpdatedEvent(Guid PWAManifestId, string Name, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record PWAManifestActivatedEvent(Guid PWAManifestId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record PWAManifestDeactivatedEvent(Guid PWAManifestId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record OfflineCacheCreatedEvent(Guid OfflineCacheId, string CacheName, string CacheVersion, string CacheStrategy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record OfflineCacheDeactivatedEvent(Guid OfflineCacheId, string CacheName, string CacheVersion) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// JobExecution Events
public record JobExecutionStartedEvent(Guid JobExecutionId, Guid JobId, int ExecutionNumber, string WorkerId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record JobExecutionCompletedEvent(Guid JobExecutionId, Guid JobId, int ExecutionNumber, double Duration) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record JobExecutionFailedEvent(Guid JobExecutionId, Guid JobId, int ExecutionNumber, string ErrorMessage) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record JobExecutionCancelledEvent(Guid JobExecutionId, Guid JobId, int ExecutionNumber) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// JobQueue Events
public record JobQueueCreatedEvent(Guid JobQueueId, string Name, int MaxConcurrency, int Priority, string CreatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record JobQueueConfigurationUpdatedEvent(Guid JobQueueId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record JobQueueMaxConcurrencyUpdatedEvent(Guid JobQueueId, string Name, int MaxConcurrency) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record JobQueuePriorityUpdatedEvent(Guid JobQueueId, string Name, int Priority) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record JobQueuePausedEvent(Guid JobQueueId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record JobQueueResumedEvent(Guid JobQueueId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record JobQueueActivatedEvent(Guid JobQueueId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record JobQueueDeactivatedEvent(Guid JobQueueId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// JobWorker Events
public record JobWorkerStartedEvent(Guid JobWorkerId, string WorkerId, string WorkerName, string MachineName) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record JobWorkerMaintenanceModeEvent(Guid JobWorkerId, string WorkerId, string WorkerName) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record JobWorkerOfflineEvent(Guid JobWorkerId, string WorkerId, string WorkerName) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// MilestonePayoutRule Events
public record MilestonePayoutRuleCreatedEvent(Guid MilestonePayoutRuleId, Guid MilestoneStepId, decimal PayoutPercentage) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record MilestonePayoutRulePayoutPercentageUpdatedEvent(Guid MilestonePayoutRuleId, Guid MilestoneStepId, decimal OldPercentage, decimal NewPercentage) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record MilestonePayoutRuleTriggerConditionUpdatedEvent(Guid MilestonePayoutRuleId, Guid MilestoneStepId, string TriggerCondition) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record MilestonePayoutRuleDescriptionUpdatedEvent(Guid MilestonePayoutRuleId, Guid MilestoneStepId, string Description) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record MilestonePayoutRuleActivatedEvent(Guid MilestonePayoutRuleId, Guid MilestoneStepId, decimal PayoutPercentage) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record MilestonePayoutRuleDeactivatedEvent(Guid MilestonePayoutRuleId, Guid MilestoneStepId, decimal PayoutPercentage) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record MilestonePayoutRuleConfigurationUpdatedEvent(Guid MilestonePayoutRuleId, Guid MilestoneStepId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// MilestoneStep Events
public record MilestoneStepUpdatedEvent(Guid MilestoneStepId, Guid MilestoneTemplateId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record MilestoneStepSequenceUpdatedEvent(Guid MilestoneStepId, Guid MilestoneTemplateId, string Name, int OldSequence, int NewSequence) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record MilestoneStepRequiredStatusChangedEvent(Guid MilestoneStepId, Guid MilestoneTemplateId, string Name, bool IsRequired) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record MilestoneStepActivatedEvent(Guid MilestoneStepId, Guid MilestoneTemplateId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record MilestoneStepDeactivatedEvent(Guid MilestoneStepId, Guid MilestoneTemplateId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record MilestoneStepTriggerConditionSetEvent(Guid MilestoneStepId, Guid MilestoneTemplateId, string Name, string TriggerCondition) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record MilestoneStepPayoutRuleAddedEvent(Guid MilestoneStepId, Guid MilestoneTemplateId, string Name, decimal PayoutPercentage) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record MilestoneStepPayoutRuleRemovedEvent(Guid MilestoneStepId, Guid MilestoneTemplateId, string Name, decimal PayoutPercentage) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record MilestoneStepConfigurationUpdatedEvent(Guid MilestoneStepId, Guid MilestoneTemplateId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// RoleTemplateMappings Events
public record RoleTemplateMappingsCreatedEvent(Guid RoleTemplateMappingsId, string RoleName, Guid MilestoneTemplateId, bool IsDefault, string CreatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record RoleTemplateMappingsSetAsDefaultEvent(Guid RoleTemplateMappingsId, string RoleName, Guid MilestoneTemplateId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record RoleTemplateMappingsRemovedAsDefaultEvent(Guid RoleTemplateMappingsId, string RoleName, Guid MilestoneTemplateId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record RoleTemplateMappingsActivatedEvent(Guid RoleTemplateMappingsId, string RoleName, Guid MilestoneTemplateId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record RoleTemplateMappingsDeactivatedEvent(Guid RoleTemplateMappingsId, string RoleName, Guid MilestoneTemplateId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record RoleTemplateMappingsPriorityUpdatedEvent(Guid RoleTemplateMappingsId, string RoleName, Guid MilestoneTemplateId, int OldPriority, int NewPriority, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record RoleTemplateMappingsConditionsUpdatedEvent(Guid RoleTemplateMappingsId, string RoleName, Guid MilestoneTemplateId, string? Conditions, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record RoleTemplateMappingsConfigurationUpdatedEvent(Guid RoleTemplateMappingsId, string RoleName, Guid MilestoneTemplateId, string UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}


