using Shared.Domain.ValueObjects;

namespace DataStorage.Domain.ValueObjects;

/// <summary>
/// Request for performance report generation
/// </summary>
public class PerformanceReportRequest : ValueObject
{
    public string ReportName { get; init; }
    public string ReportType { get; init; }
    public DateTime FromDate { get; init; }
    public DateTime ToDate { get; init; }
    public List<string> Services { get; init; }
    public List<string> Metrics { get; init; }
    public string? GroupBy { get; init; }
    public string? AggregationType { get; init; }
    public Dictionary<string, object> Filters { get; init; }
    public string? OutputFormat { get; init; }

    public PerformanceReportRequest(
        string reportName,
        string reportType,
        DateTime fromDate,
        DateTime toDate,
        List<string> services,
        List<string> metrics,
        string? groupBy = null,
        string? aggregationType = null,
        Dictionary<string, object>? filters = null,
        string? outputFormat = null)
    {
        ReportName = reportName;
        ReportType = reportType;
        FromDate = fromDate;
        ToDate = toDate;
        Services = services;
        Metrics = metrics;
        GroupBy = groupBy;
        AggregationType = aggregationType;
        Filters = filters ?? new Dictionary<string, object>();
        OutputFormat = outputFormat;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return ReportName;
        yield return ReportType;
        yield return FromDate;
        yield return ToDate;
        yield return GroupBy ?? string.Empty;
        yield return AggregationType ?? string.Empty;
        yield return OutputFormat ?? string.Empty;
        
        foreach (var service in Services.OrderBy(x => x))
        {
            yield return service;
        }
        
        foreach (var metric in Metrics.OrderBy(x => x))
        {
            yield return metric;
        }
        
        foreach (var filter in Filters.OrderBy(x => x.Key))
        {
            yield return filter.Key;
            yield return filter.Value?.ToString() ?? string.Empty;
        }
    }
}
