using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using OrderManagement.Application.Commands.CreateRfq;
using OrderManagement.Application.Commands.SubmitBid;
using OrderManagement.Domain.Enums;
using OrderManagement.IntegrationTests.Infrastructure;
using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using Xunit;

namespace OrderManagement.IntegrationTests.Controllers;

public class BidControllerTests : IntegrationTestBase
{
    public BidControllerTests(TestWebApplicationFactory<Program> factory) : base(factory)
    {
    }

    [Fact]
    public async Task SubmitBid_WithValidData_ShouldReturnCreatedResult()
    {
        // Arrange
        await SeedDatabaseAsync();

        // First create and publish an RFQ
        var rfqId = await CreateAndPublishRfqAsync();

        var bidCommand = new SubmitBidCommand
        {
            RfqId = rfqId,
            QuotedPrice = new OrderManagement.Application.Commands.SubmitBid.CreateMoneyDto { Amount = 1500.00m, Currency = "USD" },
            EstimatedPickupDate = DateTime.UtcNow.AddDays(1),
            EstimatedDeliveryDate = DateTime.UtcNow.AddDays(3),
            ProposedTerms = "Standard terms and conditions",
            VehicleDetails = "2023 Freightliner Cascadia",
            DriverDetails = "Experienced driver with 10+ years",
            AdditionalServices = "Loading and unloading assistance",
            Notes = "Can provide expedited service if needed"
        };

        // Act
        var response = await Client.PostAsJsonAsync("/api/v1/bid", bidCommand);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Created);

        var content = await response.Content.ReadAsStringAsync();
        var bidId = JsonSerializer.Deserialize<Guid>(content);
        bidId.Should().NotBeEmpty();

        // Verify in database
        using var context = await GetDbContextAsync();
        var bid = await context.RfqBids.FirstOrDefaultAsync(b => b.Id == bidId);
        bid.Should().NotBeNull();
        bid!.RfqId.Should().Be(rfqId);
        bid.Status.Should().Be(BidStatus.Submitted);
        bid.QuotedPrice.Amount.Should().Be(1500.00m);
        bid.QuotedPrice.Currency.Should().Be("USD");
    }

    [Fact]
    public async Task SubmitBid_WithInvalidRfqId_ShouldReturnBadRequest()
    {
        // Arrange
        var invalidRfqId = Guid.NewGuid();

        var bidCommand = new SubmitBidCommand
        {
            RfqId = invalidRfqId,
            QuotedPrice = new SubmitBid.CreateMoneyDto { Amount = 1000.00m, Currency = "USD" },
            EstimatedPickupDate = DateTime.UtcNow.AddDays(1),
            EstimatedDeliveryDate = DateTime.UtcNow.AddDays(2)
        };

        // Act
        var response = await Client.PostAsJsonAsync("/api/v1/bid", bidCommand);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task SubmitBid_WithInvalidDates_ShouldReturnBadRequest()
    {
        // Arrange
        await SeedDatabaseAsync();
        var rfqId = await CreateAndPublishRfqAsync();

        var bidCommand = new SubmitBidCommand
        {
            RfqId = rfqId,
            QuotedPrice = new SubmitBid.CreateMoneyDto { Amount = 1000.00m, Currency = "USD" },
            EstimatedPickupDate = DateTime.UtcNow.AddDays(3), // Pickup after delivery
            EstimatedDeliveryDate = DateTime.UtcNow.AddDays(1)
        };

        // Act
        var response = await Client.PostAsJsonAsync("/api/v1/bid", bidCommand);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task SubmitBid_ToUnpublishedRfq_ShouldReturnBadRequest()
    {
        // Arrange
        await SeedDatabaseAsync();

        // Create RFQ but don't publish it
        var rfqId = await CreateRfqAsync();

        var bidCommand = new SubmitBidCommand
        {
            RfqId = rfqId,
            QuotedPrice = new SubmitBid.CreateMoneyDto { Amount = 1000.00m, Currency = "USD" },
            EstimatedPickupDate = DateTime.UtcNow.AddDays(1),
            EstimatedDeliveryDate = DateTime.UtcNow.AddDays(2)
        };

        // Act
        var response = await Client.PostAsJsonAsync("/api/v1/bid", bidCommand);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task SubmitBid_MultipleBidsFromSameBroker_ShouldReturnBadRequest()
    {
        // Arrange
        await SeedDatabaseAsync();
        var rfqId = await CreateAndPublishRfqAsync();

        var firstBidCommand = new SubmitBidCommand
        {
            RfqId = rfqId,
            QuotedPrice = new SubmitBid.CreateMoneyDto { Amount = 1000.00m, Currency = "USD" },
            EstimatedPickupDate = DateTime.UtcNow.AddDays(1),
            EstimatedDeliveryDate = DateTime.UtcNow.AddDays(2)
        };

        // Submit first bid
        var firstResponse = await Client.PostAsJsonAsync("/api/v1/bid", firstBidCommand);
        firstResponse.StatusCode.Should().Be(HttpStatusCode.Created);

        // Try to submit second bid from same broker
        var secondBidCommand = new SubmitBidCommand
        {
            RfqId = rfqId,
            QuotedPrice = new SubmitBid.CreateMoneyDto { Amount = 900.00m, Currency = "USD" },
            EstimatedPickupDate = DateTime.UtcNow.AddDays(1),
            EstimatedDeliveryDate = DateTime.UtcNow.AddDays(2)
        };

        // Act
        var secondResponse = await Client.PostAsJsonAsync("/api/v1/bid", secondBidCommand);

        // Assert
        secondResponse.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    private async Task<Guid> CreateRfqAsync()
    {
        var createCommand = new CreateRfqCommand
        {
            Title = "Test RFQ for Bidding",
            Description = "Test RFQ Description",
            LoadDetails = new CreateLoadDetailsDto
            {
                Description = "Test Load",
                LoadType = LoadType.General,
                Weight = new CreateWeightDto { Value = 1000, Unit = WeightUnit.Kg },
                Quantity = 1,
                RequiredVehicleType = VehicleType.Truck
            },
            RouteDetails = new CreateRouteDetailsDto
            {
                PickupAddress = new CreateAddressDto
                {
                    Street = "123 Pickup St",
                    City = "Pickup City",
                    State = "PC",
                    PostalCode = "12345",
                    Country = "USA"
                },
                DeliveryAddress = new CreateAddressDto
                {
                    Street = "456 Delivery Ave",
                    City = "Delivery City",
                    State = "DC",
                    PostalCode = "67890",
                    Country = "USA"
                },
                PreferredPickupDate = DateTime.UtcNow.AddDays(1),
                PreferredDeliveryDate = DateTime.UtcNow.AddDays(3)
            },
            Requirements = new CreateRfqRequirementsDto
            {
                RequiredVehicleType = VehicleType.Truck,
                RequiresInsurance = true,
                RequiresTracking = true
            }
        };

        var response = await Client.PostAsJsonAsync("/api/v1/rfq", createCommand);
        var rfqId = JsonSerializer.Deserialize<Guid>(await response.Content.ReadAsStringAsync());
        return rfqId;
    }

    private async Task<Guid> CreateAndPublishRfqAsync()
    {
        var rfqId = await CreateRfqAsync();

        // Publish the RFQ
        var publishResponse = await Client.PostAsync($"/api/v1/rfq/{rfqId}/publish", null);
        publishResponse.StatusCode.Should().Be(HttpStatusCode.OK);

        return rfqId;
    }
}
