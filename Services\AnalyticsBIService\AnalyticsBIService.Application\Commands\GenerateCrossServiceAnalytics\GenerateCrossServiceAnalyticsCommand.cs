using MediatR;
using AnalyticsBIService.Application.DTOs;

namespace AnalyticsBIService.Application.Commands.GenerateCrossServiceAnalytics;

public class GenerateCrossServiceAnalyticsCommand : IRequest<CrossServiceAnalyticsDto>
{
    public List<string> Services { get; set; } = new();
    public DateRangeDto DateRange { get; set; } = new();
    public List<string> Metrics { get; set; } = new();
    public bool IncludeCorrelations { get; set; } = true;
    public bool IncludeInsights { get; set; } = true;
    public bool IncludePredictiveAnalytics { get; set; } = false;
    public decimal CorrelationThreshold { get; set; } = 0.5m;
    public AnalyticsDepth Depth { get; set; } = AnalyticsDepth.Standard;
    public List<string> FocusAreas { get; set; } = new();
    public Dictionary<string, object> ServiceSpecificParameters { get; set; } = new();
    public Guid RequestedBy { get; set; }
    public bool SaveResults { get; set; } = true;
    public string? ResultsName { get; set; }
}

public enum AnalyticsDepth
{
    Basic = 1,
    Standard = 2,
    Detailed = 3,
    Comprehensive = 4
}
