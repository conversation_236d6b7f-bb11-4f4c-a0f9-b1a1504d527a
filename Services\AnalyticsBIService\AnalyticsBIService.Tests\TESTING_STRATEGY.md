# AnalyticsBIService Testing Strategy

## Overview
This document outlines the comprehensive testing strategy for the AnalyticsBIService to prevent the bug introduction cycle and ensure reliable, maintainable code.

## Testing Pyramid

### 1. Unit Tests (70% of tests)
**Location**: `AnalyticsBIService.Tests/Unit/`

**Purpose**: Test individual components in isolation
- Domain entities and their business logic
- Application services and command/query handlers
- Value objects and domain services
- Validators and mappers

**Key Principles**:
- Fast execution (< 100ms per test)
- No external dependencies
- Use mocks for dependencies
- Test one thing at a time
- Follow AAA pattern (Arrange, Act, Assert)

**Example Structure**:
```
Unit/
├── Domain/
│   ├── AlertTests.cs
│   ├── DashboardTests.cs
│   └── ReportTests.cs
├── Application/
│   ├── Commands/
│   ├── Queries/
│   └── Services/
└── Infrastructure/
    ├── Repositories/
    └── Services/
```

### 2. Integration Tests (20% of tests)
**Location**: `AnalyticsBIService.Tests/Integration/`

**Purpose**: Test component interactions and data flow
- API endpoints with real database
- Repository implementations
- External service integrations
- Message handling

**Key Principles**:
- Use TestWebApplicationFactory
- Real TimescaleDB container via Testcontainers
- Test realistic scenarios
- Clean database state between tests

### 3. End-to-End Tests (10% of tests)
**Location**: `AnalyticsBIService.Tests/E2E/`

**Purpose**: Test complete user workflows
- Full API workflows
- Cross-service communication
- Performance under load

## Testing Infrastructure

### TestDataFactory
**Location**: `Infrastructure/TestDataFactory.cs`

**Features**:
- Consistent test data creation
- Uses Bogus library for realistic fake data
- Domain-specific factory methods
- Supports various test scenarios

**Usage**:
```csharp
var report = TestDataFactory.CreateTestReport(
    reportType: ReportType.PerformanceAnalysis,
    userType: UserType.Admin);

var dashboard = TestDataFactory.CreateAdminDashboard();
var alerts = TestDataFactory.CreateTestAlerts(count: 5);
```

### TestWebApplicationFactory
**Location**: `Infrastructure/TestWebApplicationFactory.cs`

**Features**:
- TimescaleDB container for realistic testing
- Automatic database seeding
- Test configuration override
- Isolated test environment

### IntegrationTestBase
**Location**: `Infrastructure/IntegrationTestBase.cs`

**Features**:
- Common setup for integration tests
- Authentication handling
- HTTP client utilities
- Database context management

## Testing Patterns

### 1. Domain Entity Testing
```csharp
[Fact]
public void Alert_Should_Update_Status_When_Acknowledged()
{
    // Arrange
    var alert = TestDataFactory.CreateTestAlert();
    var acknowledgedBy = Guid.NewGuid();
    
    // Act
    alert.Acknowledge(acknowledgedBy);
    
    // Assert
    alert.Status.Should().Be(AlertStatus.Acknowledged);
    alert.AcknowledgedBy.Should().Be(acknowledgedBy);
    alert.AcknowledgedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
}
```

### 2. Application Service Testing
```csharp
[Fact]
public async Task CreateReportCommand_Should_Create_Report_Successfully()
{
    // Arrange
    var command = new CreateReportCommand(
        "Test Report",
        ReportType.PerformanceAnalysis,
        Guid.NewGuid());
    
    var handler = new CreateReportCommandHandler(_mockRepository.Object);
    
    // Act
    var result = await handler.Handle(command, CancellationToken.None);
    
    // Assert
    result.Should().NotBeEmpty();
    _mockRepository.Verify(r => r.AddAsync(It.IsAny<Report>()), Times.Once);
}
```

### 3. Integration Testing
```csharp
public class ReportsControllerTests : IntegrationTestBase
{
    public ReportsControllerTests(TestWebApplicationFactory factory) : base(factory) { }

    [Fact]
    public async Task GetReports_Should_Return_User_Reports()
    {
        // Arrange
        var userId = Guid.NewGuid();
        SetUserContext(userId, "Driver");
        
        SeedTestData(context =>
        {
            var report = TestDataFactory.CreateTestReport(generatedBy: userId);
            context.Reports.Add(report);
        });

        // Act
        var response = await Client.GetAsync("/api/v1/reports");

        // Assert
        AssertSuccessResponse(response);
        var reports = await DeserializeResponseAsync<List<ReportDto>>(response);
        reports.Should().NotBeEmpty();
    }
}
```

## Test Data Management

### 1. Test Data Isolation
- Each test gets a fresh database state
- Use transactions for unit tests
- Container restart for integration tests

### 2. Test Data Seeding
- Minimal seed data for each test
- Use TestDataFactory for consistency
- Avoid shared test data between tests

### 3. Test Data Cleanup
- Automatic cleanup via TestWebApplicationFactory
- Manual cleanup for specific scenarios
- Database reset between test classes

## Continuous Integration

### 1. Test Execution Order
1. Unit tests (fast feedback)
2. Integration tests (medium feedback)
3. E2E tests (comprehensive validation)

### 2. Test Categorization
```csharp
[Trait("Category", "Unit")]
[Trait("Category", "Integration")]
[Trait("Category", "E2E")]
```

### 3. Parallel Execution
- Unit tests: Parallel execution enabled
- Integration tests: Sequential per class
- E2E tests: Sequential execution

## Quality Gates

### 1. Code Coverage
- Minimum 80% line coverage
- 90% branch coverage for critical paths
- Exclude infrastructure code from coverage

### 2. Test Quality Metrics
- Test execution time < 5 minutes total
- No flaky tests (> 99% pass rate)
- Clear test naming and documentation

### 3. Performance Testing
- Load tests for critical endpoints
- Database performance validation
- Memory leak detection

## Best Practices

### 1. Test Naming
```csharp
[Fact]
public void MethodName_Should_ExpectedBehavior_When_Condition()
```

### 2. Test Organization
- One test class per production class
- Group related tests in nested classes
- Use descriptive test method names

### 3. Assertion Patterns
- Use FluentAssertions for readable assertions
- Test one concept per test method
- Avoid magic numbers and strings

### 4. Mock Usage
- Mock external dependencies only
- Verify important interactions
- Use strict mocks for critical paths

## Debugging and Troubleshooting

### 1. Test Debugging
- Enable detailed logging in test configuration
- Use test-specific connection strings
- Capture test execution context

### 2. Failure Analysis
- Detailed error messages
- Test data state capture
- Correlation ID tracking

### 3. Performance Monitoring
- Test execution time tracking
- Database query analysis
- Memory usage monitoring

## Migration Strategy

### 1. Existing Test Fixes
- ✅ Fixed 53 compilation errors
- ✅ Aligned test expectations with domain model
- ✅ Standardized DateTime handling patterns

### 2. New Test Development
- Use TestDataFactory for all new tests
- Follow integration test patterns
- Implement proper test isolation

### 3. Legacy Test Improvement
- Gradually migrate to new patterns
- Improve test reliability
- Add missing test coverage

This strategy ensures robust testing practices that prevent the bug introduction cycle and maintain code quality.
