using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Domain.Entities;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;
using Microsoft.Extensions.Logging;

namespace CommunicationNotification.Infrastructure.Services;

/// <summary>
/// Notification orchestration service with failover mechanisms
/// </summary>
public class NotificationOrchestrationService : INotificationService
{
    private readonly INotificationProviderFactory _providerFactory;
    private readonly INotificationRepository _notificationRepository;
    private readonly IUserPreferenceRepository _userPreferenceRepository;
    private readonly ILogger<NotificationOrchestrationService> _logger;

    public NotificationOrchestrationService(
        INotificationProviderFactory providerFactory,
        INotificationRepository notificationRepository,
        IUserPreferenceRepository userPreferenceRepository,
        ILogger<NotificationOrchestrationService> logger)
    {
        _providerFactory = providerFactory;
        _notificationRepository = notificationRepository;
        _userPreferenceRepository = userPreferenceRepository;
        _logger = logger;
    }

    public async Task<NotificationSendResult> SendNotificationAsync(
        NotificationRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Get user preferences
            var userPreference = await _userPreferenceRepository.GetByUserIdAsync(request.UserId, cancellationToken);
            
            // Determine the best channel to use
            var channel = DetermineOptimalChannel(request, userPreference);
            
            // Create notification entity
            var notification = Notification.Create(
                request.UserId,
                request.MessageType,
                request.Content,
                channel,
                request.Priority,
                request.ScheduledAt,
                request.RelatedEntityId,
                request.RelatedEntityType,
                request.Metadata);

            // Save notification to database
            await _notificationRepository.AddAsync(notification, cancellationToken);

            // Try to send through primary channel
            var result = await TrySendNotificationAsync(notification, channel, cancellationToken);

            if (result.IsSuccess)
            {
                notification.MarkAsSent(result.ExternalId);
                await _notificationRepository.UpdateAsync(notification, cancellationToken);

                _logger.LogInformation("Notification {NotificationId} sent successfully via {Channel}",
                    notification.Id, channel);

                return NotificationSendResult.Success(notification.Id, channel, result.ExternalId);
            }

            // Primary channel failed, try failover
            var failoverResult = await TryFailoverChannels(notification, channel, userPreference, cancellationToken);
            
            if (failoverResult.IsSuccess)
            {
                return failoverResult;
            }

            // All channels failed
            notification.MarkAsFailed($"All channels failed. Last error: {result.ErrorMessage}");
            await _notificationRepository.UpdateAsync(notification, cancellationToken);

            return NotificationSendResult.Failure($"Failed to send notification: {result.ErrorMessage}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception occurred while sending notification for user {UserId}", request.UserId);
            return NotificationSendResult.Failure($"Exception: {ex.Message}");
        }
    }

    public async Task<BulkNotificationResult> SendBulkNotificationAsync(
        BulkNotificationRequest request,
        CancellationToken cancellationToken = default)
    {
        var startTime = DateTime.UtcNow;
        var result = new BulkNotificationResult
        {
            TotalNotifications = request.UserIds.Count
        };

        var semaphore = new SemaphoreSlim(request.BatchSize, request.BatchSize);
        var tasks = request.UserIds.Select(async userId =>
        {
            await semaphore.WaitAsync(cancellationToken);
            try
            {
                var notificationRequest = new NotificationRequest
                {
                    UserId = userId,
                    MessageType = request.MessageType,
                    Content = request.Content,
                    Priority = request.Priority,
                    PreferredChannel = request.PreferredChannel,
                    RelatedEntityId = request.RelatedEntityId,
                    RelatedEntityType = request.RelatedEntityType,
                    Metadata = request.Metadata,
                    ScheduledAt = request.ScheduledAt
                };

                var sendResult = await SendNotificationAsync(notificationRequest, cancellationToken);
                
                if (sendResult.IsSuccess)
                {
                    Interlocked.Increment(ref result.SuccessfulNotifications);
                }
                else
                {
                    Interlocked.Increment(ref result.FailedNotifications);
                }

                return sendResult;
            }
            finally
            {
                semaphore.Release();
            }
        });

        result.Results = (await Task.WhenAll(tasks)).ToList();
        result.ProcessingTime = DateTime.UtcNow - startTime;

        _logger.LogInformation("Bulk notification completed. Total: {Total}, Success: {Success}, Failed: {Failed}, Time: {Time}ms",
            result.TotalNotifications, result.SuccessfulNotifications, result.FailedNotifications, 
            result.ProcessingTime.TotalMilliseconds);

        return result;
    }

    public async Task<ScheduledNotificationResult> ScheduleNotificationAsync(
        ScheduledNotificationRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Get user preferences
            var userPreference = await _userPreferenceRepository.GetByUserIdAsync(request.UserId, cancellationToken);
            
            // Determine the best channel to use
            var channel = DetermineOptimalChannel(request, userPreference);
            
            // Create scheduled notification entity
            var notification = Notification.Create(
                request.UserId,
                request.MessageType,
                request.Content,
                channel,
                request.Priority,
                request.ScheduledAt,
                request.RelatedEntityId,
                request.RelatedEntityType,
                request.Metadata);

            // Save notification to database
            await _notificationRepository.AddAsync(notification, cancellationToken);

            _logger.LogInformation("Notification {NotificationId} scheduled for {ScheduledAt}",
                notification.Id, request.ScheduledAt);

            return ScheduledNotificationResult.Success(notification.Id, request.ScheduledAt);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception occurred while scheduling notification for user {UserId}", request.UserId);
            return ScheduledNotificationResult.Failure($"Exception: {ex.Message}");
        }
    }

    public async Task<NotificationStatus> GetNotificationStatusAsync(
        Guid notificationId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var notification = await _notificationRepository.GetByIdAsync(notificationId, cancellationToken);
            
            if (notification == null)
            {
                throw new InvalidOperationException($"Notification {notificationId} not found");
            }

            return new NotificationStatus
            {
                NotificationId = notification.Id,
                UserId = notification.UserId,
                MessageType = notification.MessageType,
                Channel = notification.Channel,
                Status = notification.Status,
                ScheduledAt = notification.ScheduledAt,
                SentAt = notification.SentAt,
                DeliveredAt = notification.DeliveredAt,
                ReadAt = notification.ReadAt,
                ExternalId = notification.ExternalId,
                RetryCount = notification.RetryCount,
                FailureReason = notification.FailureReason,
                Metadata = notification.Metadata
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception occurred while getting notification status for {NotificationId}", notificationId);
            throw;
        }
    }

    public async Task<bool> CancelNotificationAsync(
        Guid notificationId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var notification = await _notificationRepository.GetByIdAsync(notificationId, cancellationToken);
            
            if (notification == null)
            {
                return false;
            }

            if (notification.Status != MessageStatus.Pending)
            {
                _logger.LogWarning("Cannot cancel notification {NotificationId} with status {Status}",
                    notificationId, notification.Status);
                return false;
            }

            notification.MarkAsFailed("Cancelled by user");
            await _notificationRepository.UpdateAsync(notification, cancellationToken);

            _logger.LogInformation("Notification {NotificationId} cancelled successfully", notificationId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception occurred while cancelling notification {NotificationId}", notificationId);
            return false;
        }
    }

    public async Task<NotificationSendResult> RetryNotificationAsync(
        Guid notificationId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var notification = await _notificationRepository.GetByIdAsync(notificationId, cancellationToken);
            
            if (notification == null)
            {
                return NotificationSendResult.Failure("Notification not found");
            }

            if (!notification.CanRetry(3))
            {
                return NotificationSendResult.Failure("Maximum retry attempts exceeded");
            }

            // Reset notification status for retry
            notification.MarkAsRetrying();
            await _notificationRepository.UpdateAsync(notification, cancellationToken);

            // Try to send again
            var result = await TrySendNotificationAsync(notification, notification.Channel, cancellationToken);

            if (result.IsSuccess)
            {
                notification.MarkAsSent(result.ExternalId);
                await _notificationRepository.UpdateAsync(notification, cancellationToken);

                _logger.LogInformation("Notification {NotificationId} retry successful via {Channel}",
                    notification.Id, notification.Channel);

                return NotificationSendResult.Success(notification.Id, notification.Channel, result.ExternalId);
            }
            else
            {
                notification.MarkAsFailed($"Retry failed: {result.ErrorMessage}");
                await _notificationRepository.UpdateAsync(notification, cancellationToken);

                return NotificationSendResult.Failure($"Retry failed: {result.ErrorMessage}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception occurred while retrying notification {NotificationId}", notificationId);
            return NotificationSendResult.Failure($"Exception: {ex.Message}");
        }
    }

    public async Task<UserPreference?> GetUserPreferencesAsync(
        Guid userId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            return await _userPreferenceRepository.GetByUserIdAsync(userId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception occurred while getting user preferences for {UserId}", userId);
            return null;
        }
    }

    public async Task<bool> UpdateUserPreferencesAsync(
        Guid userId,
        NotificationSettings preferences,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var userPreference = await _userPreferenceRepository.GetByUserIdAsync(userId, cancellationToken);
            
            if (userPreference == null)
            {
                // Create new user preference
                userPreference = UserPreference.Create(userId, ContactInfo.Create("", ""), preferences, UserRole.User);
                await _userPreferenceRepository.AddAsync(userPreference, cancellationToken);
            }
            else
            {
                // Update existing preference
                userPreference.UpdateNotificationSettings(preferences);
                await _userPreferenceRepository.UpdateAsync(userPreference, cancellationToken);
            }

            _logger.LogInformation("User preferences updated for {UserId}", userId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception occurred while updating user preferences for {UserId}", userId);
            return false;
        }
    }

    private NotificationChannel DetermineOptimalChannel(
        NotificationRequest request,
        UserPreference? userPreference)
    {
        // Use preferred channel if specified and enabled
        if (request.PreferredChannel.HasValue && 
            userPreference?.ShouldReceiveNotification(request.MessageType, request.PreferredChannel.Value, DateTime.UtcNow) == true)
        {
            return request.PreferredChannel.Value;
        }

        // Use user's preferred channel based on message type and priority
        if (userPreference != null)
        {
            var preferredChannel = userPreference.GetPreferredChannel(request.MessageType, request.Priority);
            if (userPreference.ShouldReceiveNotification(request.MessageType, preferredChannel, DateTime.UtcNow))
            {
                return preferredChannel;
            }
        }

        // Fallback to default channel based on message type and priority
        return GetDefaultChannel(request.MessageType, request.Priority);
    }

    private static NotificationChannel GetDefaultChannel(MessageType messageType, Priority priority)
    {
        return (messageType, priority) switch
        {
            (MessageType.Emergency, _) => NotificationChannel.Sms,
            (_, Priority.Emergency) => NotificationChannel.Sms,
            (MessageType.DriverInstruction, _) => NotificationChannel.Push,
            (MessageType.TripUpdate, _) => NotificationChannel.Push,
            (MessageType.PaymentNotification, _) => NotificationChannel.Email,
            (MessageType.SystemNotification, _) => NotificationChannel.InApp,
            _ => NotificationChannel.Push
        };
    }

    private async Task<NotificationResult> TrySendNotificationAsync(
        Notification notification,
        NotificationChannel channel,
        CancellationToken cancellationToken)
    {
        try
        {
            if (!_providerFactory.IsProviderAvailable(channel))
            {
                return NotificationResult.Failure($"Provider for {channel} is not available");
            }

            var provider = _providerFactory.GetProvider(channel);
            
            // Get user contact info (this would typically come from user service)
            var contactInfo = await GetUserContactInfoAsync(notification.UserId, cancellationToken);
            
            return await provider.SendAsync(contactInfo, notification.Content, notification.Metadata, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send notification {NotificationId} via {Channel}",
                notification.Id, channel);
            return NotificationResult.Failure($"Provider error: {ex.Message}");
        }
    }

    private async Task<NotificationSendResult> TryFailoverChannels(
        Notification notification,
        NotificationChannel primaryChannel,
        UserPreference? userPreference,
        CancellationToken cancellationToken)
    {
        var failoverChannels = GetFailoverChannels(primaryChannel, userPreference);

        foreach (var channel in failoverChannels)
        {
            _logger.LogInformation("Trying failover channel {Channel} for notification {NotificationId}",
                channel, notification.Id);

            var result = await TrySendNotificationAsync(notification, channel, cancellationToken);
            
            if (result.IsSuccess)
            {
                notification.MarkAsSent(result.ExternalId);
                notification.AddMetadata("failover_channel", channel.ToString());
                await _notificationRepository.UpdateAsync(notification, cancellationToken);

                _logger.LogInformation("Notification {NotificationId} sent successfully via failover channel {Channel}",
                    notification.Id, channel);

                return NotificationSendResult.Success(notification.Id, channel, result.ExternalId);
            }
        }

        return NotificationSendResult.Failure("All failover channels failed");
    }

    private static List<NotificationChannel> GetFailoverChannels(
        NotificationChannel primaryChannel,
        UserPreference? userPreference)
    {
        var failoverChannels = new List<NotificationChannel>();

        // Define failover hierarchy based on primary channel
        switch (primaryChannel)
        {
            case NotificationChannel.Push:
                failoverChannels.AddRange(new[] { NotificationChannel.Sms, NotificationChannel.Email, NotificationChannel.WhatsApp });
                break;
            case NotificationChannel.Sms:
                failoverChannels.AddRange(new[] { NotificationChannel.WhatsApp, NotificationChannel.Push, NotificationChannel.Email });
                break;
            case NotificationChannel.Email:
                failoverChannels.AddRange(new[] { NotificationChannel.Push, NotificationChannel.Sms, NotificationChannel.WhatsApp });
                break;
            case NotificationChannel.WhatsApp:
                failoverChannels.AddRange(new[] { NotificationChannel.Sms, NotificationChannel.Push, NotificationChannel.Email });
                break;
            case NotificationChannel.Voice:
                failoverChannels.AddRange(new[] { NotificationChannel.Sms, NotificationChannel.WhatsApp, NotificationChannel.Push });
                break;
            default:
                failoverChannels.AddRange(new[] { NotificationChannel.Push, NotificationChannel.Sms, NotificationChannel.Email });
                break;
        }

        // Filter based on user preferences
        if (userPreference != null)
        {
            failoverChannels = failoverChannels
                .Where(channel => userPreference.NotificationSettings.IsChannelEnabled(channel))
                .ToList();
        }

        return failoverChannels;
    }

    private async Task<ContactInfo> GetUserContactInfoAsync(Guid userId, CancellationToken cancellationToken)
    {
        // This would typically fetch from user service or database
        // For now, return a placeholder
        var userPreference = await _userPreferenceRepository.GetByUserIdAsync(userId, cancellationToken);
        
        return userPreference?.ContactInfo ?? ContactInfo.Create("<EMAIL>", "+1234567890");
    }
}
