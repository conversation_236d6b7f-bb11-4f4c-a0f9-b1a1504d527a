using CommunicationNotification.Domain.ValueObjects;

namespace CommunicationNotification.Application.Interfaces;

/// <summary>
/// Interface for push notification providers (Firebase, APNS, etc.)
/// </summary>
public interface IPushNotificationProvider : INotificationProvider
{
    /// <summary>
    /// Send push notification to a single device
    /// </summary>
    /// <param name="pushRequest">Push notification request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Push notification result</returns>
    Task<PushNotificationResult> SendPushNotificationAsync(
        PushNotificationRequest pushRequest,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Send push notification to multiple devices
    /// </summary>
    /// <param name="multicastRequest">Multicast push request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Multicast push result</returns>
    Task<MulticastPushResult> SendMulticastPushAsync(
        MulticastPushRequest multicastRequest,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Send push notification to a topic
    /// </summary>
    /// <param name="topicRequest">Topic push request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Topic push result</returns>
    Task<TopicPushResult> SendTopicPushAsync(
        TopicPushRequest topicRequest,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Subscribe device token to a topic
    /// </summary>
    /// <param name="deviceToken">Device token</param>
    /// <param name="topic">Topic name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if successful</returns>
    Task<bool> SubscribeToTopicAsync(
        string deviceToken,
        string topic,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Unsubscribe device token from a topic
    /// </summary>
    /// <param name="deviceToken">Device token</param>
    /// <param name="topic">Topic name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if successful</returns>
    Task<bool> UnsubscribeFromTopicAsync(
        string deviceToken,
        string topic,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate device token
    /// </summary>
    /// <param name="deviceToken">Device token to validate</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if token is valid</returns>
    Task<bool> ValidateDeviceTokenAsync(
        string deviceToken,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Push notification request
/// </summary>
public class PushNotificationRequest
{
    public string DeviceToken { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Body { get; set; } = string.Empty;
    public string? ImageUrl { get; set; }
    public Dictionary<string, string>? Data { get; set; }
    public PushNotificationPriority Priority { get; set; } = PushNotificationPriority.Normal;
    public string? Sound { get; set; }
    public int? Badge { get; set; }
    public string? ClickAction { get; set; }
    public string? CollapseKey { get; set; }
    public TimeSpan? TimeToLive { get; set; }
    public bool ContentAvailable { get; set; }
    public bool MutableContent { get; set; }
}

/// <summary>
/// Multicast push request
/// </summary>
public class MulticastPushRequest
{
    public List<string> DeviceTokens { get; set; } = new();
    public string Title { get; set; } = string.Empty;
    public string Body { get; set; } = string.Empty;
    public string? ImageUrl { get; set; }
    public Dictionary<string, string>? Data { get; set; }
    public PushNotificationPriority Priority { get; set; } = PushNotificationPriority.Normal;
    public string? Sound { get; set; }
    public int? Badge { get; set; }
    public string? ClickAction { get; set; }
    public string? CollapseKey { get; set; }
    public TimeSpan? TimeToLive { get; set; }
}

/// <summary>
/// Topic push request
/// </summary>
public class TopicPushRequest
{
    public string Topic { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Body { get; set; } = string.Empty;
    public string? ImageUrl { get; set; }
    public Dictionary<string, string>? Data { get; set; }
    public PushNotificationPriority Priority { get; set; } = PushNotificationPriority.Normal;
    public string? Sound { get; set; }
    public string? ClickAction { get; set; }
    public string? CollapseKey { get; set; }
    public TimeSpan? TimeToLive { get; set; }
    public string? Condition { get; set; }
}

/// <summary>
/// Push notification result
/// </summary>
public class PushNotificationResult
{
    public bool IsSuccess { get; set; }
    public string? MessageId { get; set; }
    public string? ErrorMessage { get; set; }
    public string? ErrorCode { get; set; }
    public DateTime SentAt { get; set; } = DateTime.UtcNow;

    public static PushNotificationResult Success(string messageId)
    {
        return new PushNotificationResult
        {
            IsSuccess = true,
            MessageId = messageId
        };
    }

    public static PushNotificationResult Failure(string errorMessage, string? errorCode = null)
    {
        return new PushNotificationResult
        {
            IsSuccess = false,
            ErrorMessage = errorMessage,
            ErrorCode = errorCode
        };
    }
}

/// <summary>
/// Multicast push result
/// </summary>
public class MulticastPushResult
{
    public int TotalDevices { get; set; }
    public int SuccessfulDevices { get; set; }
    public int FailedDevices { get; set; }
    public List<PushNotificationResult> Results { get; set; } = new();
    public List<string>? InvalidTokens { get; set; }

    public bool IsPartialSuccess => SuccessfulDevices > 0 && FailedDevices > 0;
    public bool IsCompleteSuccess => SuccessfulDevices == TotalDevices;
    public bool IsCompleteFailure => FailedDevices == TotalDevices;
}

/// <summary>
/// Topic push result
/// </summary>
public class TopicPushResult
{
    public bool IsSuccess { get; set; }
    public string? MessageId { get; set; }
    public string? ErrorMessage { get; set; }
    public string? ErrorCode { get; set; }
    public DateTime SentAt { get; set; } = DateTime.UtcNow;

    public static TopicPushResult Success(string messageId)
    {
        return new TopicPushResult
        {
            IsSuccess = true,
            MessageId = messageId
        };
    }

    public static TopicPushResult Failure(string errorMessage, string? errorCode = null)
    {
        return new TopicPushResult
        {
            IsSuccess = false,
            ErrorMessage = errorMessage,
            ErrorCode = errorCode
        };
    }
}

/// <summary>
/// Push notification priority
/// </summary>
public enum PushNotificationPriority
{
    Normal = 1,
    High = 2
}
