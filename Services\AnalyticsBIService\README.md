# Analytics & Business Intelligence Service

## Overview

The Analytics & Business Intelligence Service provides comprehensive analytics, reporting, and business intelligence capabilities for the TLI platform. It aggregates data from all microservices to deliver role-based dashboards, real-time metrics, and advanced analytics for different user types.

## Features

### Admin Panel Analytics
- **Platform Performance Metrics**: Real-time KPI monitoring, RFQ conversion rates, quote accuracy, trip completion percentages
- **Revenue Analytics**: Transaction volume tracking, subscription revenue analysis, churn analysis
- **User Growth**: Retention rates, engagement metrics across all user types
- **Geographic Analysis**: Market penetration data, performance by region
- **Business Intelligence Reports**: Comprehensive platform usage reports, regulatory compliance reporting
- **Custom Report Builder**: Flexible reporting for specific business requirements

### Transport Company Analytics
- **Performance Dashboard**: RFQ conversion rates, broker response times, delivery performance
- **Cost Analysis**: Pricing optimization, cost trends, profitability analysis
- **Market Intelligence**: Trend analysis, competitive positioning, customer acquisition metrics
- **Broker Performance**: Network efficiency comparisons, performance benchmarking
- **Advanced Analytics**: Predictive insights (Pro/Enterprise plans)

### Broker Analytics
- **Operational Dashboard**: RFQ conversion rates, quote success rates, carrier network utilization
- **Margin Analysis**: Profitability metrics, commission tracking
- **Business Growth**: Market opportunities, expansion potential, customer retention
- **Performance Tracking**: Trip completion rates, delivery performance monitoring

### Carrier Analytics
- **Performance Metrics**: Delivery completion rates, customer satisfaction ratings
- **Efficiency Tracking**: On-time delivery performance, service quality scores
- **Earnings Analytics**: Income trends, performance benchmarking
- **Growth Opportunities**: Service improvement identification, income optimization

### Shipper Analytics
- **SLA Performance**: On-time delivery analysis, performance against KPIs
- **Cost Analysis**: Cost per trip, freight optimization, route efficiency
- **Provider Comparison**: Transport company benchmarking, performance comparison
- **Business Reporting**: Comprehensive shipment reports, trend analysis

## Architecture

### Domain Layer
- **Entities**: Analytics data models, reports, dashboards, KPIs
- **Value Objects**: Metrics, time periods, performance indicators
- **Services**: Business logic for analytics calculations and aggregations

### Application Layer
- **Commands**: Data collection, report generation, dashboard updates
- **Queries**: Analytics retrieval, dashboard data, report queries
- **DTOs**: Data transfer objects for API responses
- **Services**: Application services for analytics processing

### Infrastructure Layer
- **Persistence**: TimescaleDB for time-series analytics data
- **Repositories**: Data access for analytics and reporting
- **Services**: External integrations, caching, real-time processing

### API Layer
- **Controllers**: REST endpoints for analytics and reporting
- **Authentication**: JWT-based role-based access control
- **Documentation**: Swagger/OpenAPI documentation

## Technology Stack

- **.NET 8**: Core framework
- **TimescaleDB**: Time-series database for analytics data
- **Entity Framework Core**: ORM for data access
- **MediatR**: CQRS pattern implementation
- **AutoMapper**: Object mapping
- **Redis**: Caching for performance optimization
- **JWT**: Authentication and authorization
- **Swagger**: API documentation

## Database Design

### Time-Series Tables (Hypertables)
- **analytics_events**: Core analytics events with time partitioning
- **performance_metrics**: KPI and performance data
- **user_activities**: User engagement and activity tracking
- **financial_metrics**: Revenue and financial analytics
- **operational_metrics**: Operational performance data

### Continuous Aggregates
- **daily_metrics**: Daily aggregated analytics
- **weekly_reports**: Weekly performance summaries
- **monthly_analytics**: Monthly business intelligence data
- **real_time_kpis**: Real-time dashboard metrics

## Getting Started

### Prerequisites
- .NET 8 SDK
- TimescaleDB (PostgreSQL with TimescaleDB extension)
- Redis (for caching)

### Setup
1. Clone the repository
2. Navigate to the Analytics & BI Service directory
3. Run database setup script
4. Configure connection strings
5. Run the service

### Configuration
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Port=5432;Database=analytics_bi;User Id=timescale;Password=timescale",
    "Redis": "localhost:6379"
  },
  "Analytics": {
    "RealTimeProcessing": true,
    "CacheExpirationMinutes": 15,
    "BatchProcessingInterval": "00:05:00"
  }
}
```

## API Endpoints

### Admin Analytics
- `GET /api/admin/dashboard` - Platform performance dashboard
- `GET /api/admin/reports` - Business intelligence reports
- `GET /api/admin/kpis` - Real-time KPI monitoring

### Role-Based Analytics
- `GET /api/transport-company/dashboard` - Transport company analytics
- `GET /api/broker/dashboard` - Broker operational dashboard
- `GET /api/carrier/dashboard` - Carrier performance metrics
- `GET /api/shipper/dashboard` - Shipper analytics dashboard

### Reports and Exports
- `GET /api/reports/custom` - Custom report builder
- `POST /api/reports/export` - Data export functionality
- `GET /api/reports/scheduled` - Scheduled report management

## Development

### Running Tests
```bash
dotnet test
```

### Database Migrations
```bash
dotnet ef migrations add InitialCreate
dotnet ef database update
```

### Docker Support
```bash
docker-compose up -d
```

## Monitoring and Observability

- Health checks for service dependencies
- Metrics for API performance
- Logging for audit trails
- Real-time alerting for critical metrics

## Security

- JWT-based authentication
- Role-based authorization
- Data encryption at rest and in transit
- Audit logging for compliance

## Contributing

1. Follow the established coding standards
2. Write comprehensive tests
3. Update documentation
4. Submit pull requests for review

## License

Copyright © 2024 TLI Platform. All rights reserved.
