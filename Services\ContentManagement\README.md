# Content Management Service

The Content Management Service is a comprehensive microservice for managing content, FAQs, and policy documents in the TLI (Transport & Logistics Intelligence) platform. It provides a robust content management system with workflow approval, versioning, search capabilities, and analytics.

## Features

### Core Content Management
- **Content Types**: Support for general content, FAQs, policies, static pages, news articles, and announcements
- **Content Workflow**: Draft → Pending Review → Approved → Published workflow with role-based permissions
- **Version Control**: Automatic versioning of content changes with change tracking
- **Rich Metadata**: Support for meta titles, descriptions, tags, and SEO optimization
- **Content Scheduling**: Schedule content for automatic publishing and unpublishing

### FAQ Management
- **Question & Answer Format**: Structured FAQ items with categories
- **Role-based Visibility**: Control which user roles can see specific FAQs
- **Highlighting**: Mark important FAQs as highlighted
- **Analytics**: Track view counts and helpfulness ratings
- **Categories**: Organize FAQs by categories (General, Account, Billing, Technical, etc.)

### Policy Management
- **Policy Documents**: Manage legal documents, terms & conditions, privacy policies
- **Version Control**: Track policy versions with effective dates
- **Acceptance Tracking**: Record user acceptance of policies for compliance
- **Role Applicability**: Define which roles policies apply to
- **Compliance Reporting**: Generate compliance reports and track acceptance rates

### Advanced Features
- **Full-text Search**: Advanced search capabilities with relevance scoring
- **Content Preview**: Generate previews with SEO analysis
- **Analytics & Reporting**: Comprehensive analytics on content performance
- **Admin Dashboard**: Rich administrative interface with metrics and insights
- **Audit Logging**: Complete audit trail of all content operations
- **Notification System**: Automated notifications for workflow events

## Architecture

The service follows Clean Architecture principles with clear separation of concerns:

```
├── ContentManagement.API/          # Web API layer
├── ContentManagement.Application/  # Application logic and use cases
├── ContentManagement.Domain/       # Domain entities and business rules
├── ContentManagement.Infrastructure/ # Data access and external services
└── ContentManagement.Tests/        # Unit and integration tests
```

### Key Patterns
- **CQRS**: Command Query Responsibility Segregation using MediatR
- **Domain Events**: Event-driven architecture for loose coupling
- **Repository Pattern**: Data access abstraction
- **Value Objects**: Rich domain modeling
- **Clean Architecture**: Dependency inversion and separation of concerns

## Technology Stack

- **.NET 8**: Latest .NET framework
- **ASP.NET Core**: Web API framework
- **Entity Framework Core**: ORM with PostgreSQL
- **MediatR**: CQRS and mediator pattern
- **AutoMapper**: Object-to-object mapping
- **FluentValidation**: Input validation
- **Serilog**: Structured logging
- **Swagger/OpenAPI**: API documentation
- **xUnit**: Unit testing framework

## Getting Started

### Prerequisites
- .NET 8 SDK
- PostgreSQL 13+
- RabbitMQ (for messaging)

### Configuration

Update `appsettings.json` with your database connection string:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=TLI_ContentManagement;Username=postgres;Password=your_password"
  }
}
```

### Running the Service

1. **Restore packages**:
   ```bash
   dotnet restore
   ```

2. **Update database**:
   ```bash
   dotnet ef database update --project ContentManagement.Infrastructure --startup-project ContentManagement.API
   ```

3. **Run the service**:
   ```bash
   dotnet run --project ContentManagement.API
   ```

4. **Access Swagger UI**: Navigate to `https://localhost:5007` (or configured port)

### Running Tests

```bash
# Run all tests
dotnet test

# Run with coverage
dotnet test --collect:"XPlat Code Coverage"
```

## API Endpoints

### Content Management
- `GET /api/content/{id}` - Get content by ID
- `GET /api/content/slug/{slug}` - Get content by slug
- `GET /api/content/search` - Search content
- `POST /api/content` - Create content
- `PUT /api/content/{id}` - Update content
- `POST /api/content/{id}/submit-for-review` - Submit for review
- `POST /api/content/{id}/approve` - Approve content
- `POST /api/content/{id}/publish` - Publish content
- `DELETE /api/content/{id}` - Delete content

### FAQ Management
- `GET /api/faq/{id}` - Get FAQ by ID
- `GET /api/faq/search` - Search FAQs
- `GET /api/faq/category/{category}` - Get FAQs by category
- `GET /api/faq/highlighted` - Get highlighted FAQs
- `POST /api/faq` - Create FAQ
- `PUT /api/faq/{id}` - Update FAQ
- `POST /api/faq/{id}/rate` - Rate FAQ helpfulness

### Policy Management
- `GET /api/policy/{id}` - Get policy by ID
- `GET /api/policy/type/{type}` - Get policy by type
- `GET /api/policy/applicable` - Get applicable policies
- `POST /api/policy` - Create policy
- `POST /api/policy/{id}/accept` - Accept policy
- `GET /api/policy/{id}/compliance-report` - Get compliance report

### Admin & Analytics
- `GET /api/admin/dashboard` - Admin dashboard data
- `GET /api/admin/analytics` - Content analytics
- `GET /api/admin/performance` - Performance metrics
- `GET /api/cms/editor/{id}` - Content editor interface
- `POST /api/cms/preview` - Generate content preview

## Domain Models

### Content Entity
```csharp
public class Content : BaseEntity
{
    public string Title { get; private set; }
    public string Slug { get; private set; }
    public ContentType Type { get; private set; }
    public ContentStatus Status { get; private set; }
    public string ContentBody { get; private set; }
    public ContentVisibility Visibility { get; private set; }
    // ... other properties
}
```

### FAQ Entity
```csharp
public class FaqItem : Content
{
    public string Question { get; private set; }
    public string Answer { get; private set; }
    public FaqCategory Category { get; private set; }
    public List<string> RoleVisibility { get; private set; }
    public bool IsHighlighted { get; private set; }
    public int ViewCount { get; private set; }
    public decimal HelpfulnessRating { get; private set; }
    // ... other properties
}
```

### Policy Entity
```csharp
public class PolicyDocument : Content
{
    public PolicyType PolicyType { get; private set; }
    public string Version { get; private set; }
    public DateTime EffectiveDate { get; private set; }
    public bool RequiresAcceptance { get; private set; }
    public List<string> ApplicableRoles { get; private set; }
    // ... other properties
}
```

## Integration

### Service Dependencies
- **Identity Service**: User authentication and authorization
- **Audit & Compliance Service**: Audit logging and compliance tracking
- **Communication & Notification Service**: Email and notification delivery
- **Data & Storage Service**: File and document management

### Event Publishing
The service publishes domain events for integration:
- `ContentCreatedEvent`
- `ContentPublishedEvent`
- `FaqViewedEvent`
- `PolicyAcceptedEvent`

### Message Queues
Uses RabbitMQ for:
- Audit event publishing
- Notification triggers
- Background job processing

## Security

### Authentication & Authorization
- JWT Bearer token authentication
- Role-based authorization (Admin, ContentManager, ContentApprover)
- Content visibility controls
- API rate limiting

### Data Protection
- Input validation and sanitization
- SQL injection prevention
- XSS protection
- Audit logging of all operations

## Monitoring & Observability

### Logging
- Structured logging with Serilog
- Request/response logging
- Error tracking and alerting
- Performance metrics

### Health Checks
- Database connectivity
- External service dependencies
- Application health status

### Metrics
- Content creation/update rates
- User engagement metrics
- Performance benchmarks
- Error rates and response times

## Development Guidelines

### Code Standards
- Follow Clean Architecture principles
- Use SOLID design principles
- Implement comprehensive unit tests
- Document public APIs
- Use meaningful commit messages

### Testing Strategy
- Unit tests for domain logic
- Integration tests for API endpoints
- Repository tests with in-memory database
- Performance tests for critical paths

### Deployment
- Docker containerization
- Environment-specific configurations
- Database migration scripts
- Health check endpoints

## Contributing

1. Fork the repository
2. Create a feature branch
3. Implement changes with tests
4. Submit a pull request
5. Ensure all checks pass

## License

This project is part of the TLI platform and is proprietary software.
