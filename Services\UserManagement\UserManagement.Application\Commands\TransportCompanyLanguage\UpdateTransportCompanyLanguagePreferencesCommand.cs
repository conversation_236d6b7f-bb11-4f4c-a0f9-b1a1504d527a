using UserManagement.Application.DTOs;
using MediatR;

namespace UserManagement.Application.Commands.TransportCompanyLanguage;

/// <summary>
/// Command to update Transport Company language preferences
/// </summary>
public class UpdateTransportCompanyLanguagePreferencesCommand : IRequest<UpdateTransportCompanyLanguagePreferencesResult>
{
    public Guid TransportCompanyId { get; set; }
    public Guid UserId { get; set; }
    public TransportCompanyLanguagePreferences Preferences { get; set; } = new();
    public bool ApplyToAllUsers { get; set; } = false;
    public string? ChangeReason { get; set; }
    public Guid UpdatedBy { get; set; }
}

/// <summary>
/// Transport Company language preferences
/// </summary>
public class TransportCompanyLanguagePreferences
{
    // Primary language settings
    public string PrimaryLanguageCode { get; set; } = "en"; // ISO 639-1
    public string PrimaryCountryCode { get; set; } = "US"; // ISO 3166-1 alpha-2
    public string PrimaryCultureCode { get; set; } = "en-US"; // Full culture code
    public string PrimaryLanguageName { get; set; } = "English";
    public string PrimaryNativeName { get; set; } = "English";

    // Secondary languages
    public List<LanguageOption> SecondaryLanguages { get; set; } = new();
    public bool EnableMultiLanguageSupport { get; set; } = true;
    public bool AllowUserLanguageOverride { get; set; } = true;

    // Auto-detection settings
    public bool EnableLocationBasedDetection { get; set; } = true;
    public bool EnableBrowserLanguageDetection { get; set; } = true;
    public bool EnableIPBasedDetection { get; set; } = false;
    public string FallbackLanguageCode { get; set; } = "en";

    // Translation settings
    public bool EnableAutoTranslation { get; set; } = false;
    public string TranslationProvider { get; set; } = "Google"; // Google, Azure, AWS
    public bool CacheTranslations { get; set; } = true;
    public int TranslationCacheHours { get; set; } = 24;

    // Content localization
    public bool LocalizeDocuments { get; set; } = true;
    public bool LocalizeNotifications { get; set; } = true;
    public bool LocalizeReports { get; set; } = true;
    public bool LocalizeUserInterface { get; set; } = true;
    public bool LocalizeEmails { get; set; } = true;
    public bool LocalizeSMS { get; set; } = true;

    // Regional formatting
    public RegionalFormattingPreferences RegionalFormatting { get; set; } = new();

    // Advanced settings
    public bool EnableRightToLeftLayout { get; set; } = false;
    public string TextDirection { get; set; } = "ltr"; // ltr, rtl
    public bool EnableLanguageSwitcher { get; set; } = true;
    public bool ShowLanguageFlags { get; set; } = true;
    public bool RememberLanguageChoice { get; set; } = true;
    public int LanguageChoiceExpiryDays { get; set; } = 365;

    // Custom settings
    public Dictionary<string, object> CustomSettings { get; set; } = new();
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Language option
/// </summary>
public class LanguageOption
{
    public string LanguageCode { get; set; } = string.Empty;
    public string CountryCode { get; set; } = string.Empty;
    public string CultureCode { get; set; } = string.Empty;
    public string LanguageName { get; set; } = string.Empty;
    public string NativeName { get; set; } = string.Empty;
    public bool IsEnabled { get; set; } = true;
    public int Priority { get; set; } = 0;
    public string? FlagIcon { get; set; }
    public bool IsRightToLeft { get; set; } = false;
}

/// <summary>
/// Regional formatting preferences
/// </summary>
public class RegionalFormattingPreferences
{
    // Date and time
    public string DateFormat { get; set; } = "MM/dd/yyyy";
    public string TimeFormat { get; set; } = "HH:mm";
    public string DateTimeFormat { get; set; } = "MM/dd/yyyy HH:mm";
    public bool Use24HourFormat { get; set; } = false;
    public string FirstDayOfWeek { get; set; } = "Sunday";
    public string TimeZone { get; set; } = "UTC";

    // Numbers and currency
    public string NumberFormat { get; set; } = "en-US";
    public string CurrencyCode { get; set; } = "USD";
    public string CurrencySymbol { get; set; } = "$";
    public string CurrencyFormat { get; set; } = "C";
    public string DecimalSeparator { get; set; } = ".";
    public string ThousandsSeparator { get; set; } = ",";
    public int DecimalPlaces { get; set; } = 2;

    // Address and phone
    public string AddressFormat { get; set; } = "US";
    public string PhoneNumberFormat { get; set; } = "US";
    public bool ShowCountryCode { get; set; } = false;

    // Units of measurement
    public string DistanceUnit { get; set; } = "miles"; // miles, kilometers
    public string WeightUnit { get; set; } = "pounds"; // pounds, kilograms
    public string TemperatureUnit { get; set; } = "fahrenheit"; // fahrenheit, celsius
    public string FuelUnit { get; set; } = "gallons"; // gallons, liters
}

/// <summary>
/// Result of updating language preferences
/// </summary>
public class UpdateTransportCompanyLanguagePreferencesResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime UpdatedAt { get; set; }
    public List<string> ValidationErrors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public LanguagePreferencesStatus Status { get; set; } = new();
    public List<AffectedUser> AffectedUsers { get; set; } = new();
}

/// <summary>
/// Language preferences status
/// </summary>
public class LanguagePreferencesStatus
{
    public string CurrentPrimaryLanguage { get; set; } = string.Empty;
    public int SupportedLanguagesCount { get; set; }
    public bool TranslationEnabled { get; set; }
    public bool LocationDetectionEnabled { get; set; }
    public string FallbackLanguage { get; set; } = string.Empty;
    public DateTime LastUpdated { get; set; }
    public List<string> EnabledFeatures { get; set; } = new();
}

/// <summary>
/// Affected user information
/// </summary>
public class AffectedUser
{
    public Guid UserId { get; set; }
    public string UserName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string PreviousLanguage { get; set; } = string.Empty;
    public string NewLanguage { get; set; } = string.Empty;
    public bool RequiresNotification { get; set; }
}

/// <summary>
/// Command to get language preferences
/// </summary>
public class GetTransportCompanyLanguagePreferencesQuery : IRequest<TransportCompanyLanguagePreferencesDto>
{
    public Guid TransportCompanyId { get; set; }
    public bool IncludeAvailableLanguages { get; set; } = true;
    public bool IncludeUsageStatistics { get; set; } = false;
    public bool IncludeTranslationStatus { get; set; } = false;
}

/// <summary>
/// Command to detect user language
/// </summary>
public class DetectUserLanguageCommand : IRequest<DetectUserLanguageResult>
{
    public Guid UserId { get; set; }
    public Guid TransportCompanyId { get; set; }
    public string? IPAddress { get; set; }
    public string? UserAgent { get; set; }
    public string? AcceptLanguageHeader { get; set; }
    public string? TimeZone { get; set; }
    public Dictionary<string, object> BrowserInfo { get; set; } = new();
}

/// <summary>
/// Result of language detection
/// </summary>
public class DetectUserLanguageResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public LanguageDetectionInfo DetectedLanguage { get; set; } = new();
    public List<LanguageDetectionInfo> AlternativeLanguages { get; set; } = new();
    public LanguageDetectionMetadata Metadata { get; set; } = new();
}

/// <summary>
/// Language detection information
/// </summary>
public class LanguageDetectionInfo
{
    public string LanguageCode { get; set; } = string.Empty;
    public string CountryCode { get; set; } = string.Empty;
    public string CultureCode { get; set; } = string.Empty;
    public string LanguageName { get; set; } = string.Empty;
    public string NativeName { get; set; } = string.Empty;
    public decimal Confidence { get; set; }
    public string DetectionMethod { get; set; } = string.Empty;
    public bool IsSupported { get; set; }
    public bool IsRecommended { get; set; }
}

/// <summary>
/// Language detection metadata
/// </summary>
public class LanguageDetectionMetadata
{
    public DateTime DetectedAt { get; set; }
    public List<string> DetectionMethods { get; set; } = new();
    public string? DetectedCountry { get; set; }
    public string? DetectedRegion { get; set; }
    public string? DetectedTimeZone { get; set; }
    public Dictionary<string, object> BrowserLanguages { get; set; } = new();
    public Dictionary<string, object> LocationData { get; set; } = new();
}

/// <summary>
/// Command to apply language to user
/// </summary>
public class ApplyLanguageToUserCommand : IRequest<ApplyLanguageToUserResult>
{
    public Guid UserId { get; set; }
    public Guid TransportCompanyId { get; set; }
    public string LanguageCode { get; set; } = string.Empty;
    public string CountryCode { get; set; } = string.Empty;
    public bool PersistChoice { get; set; } = true;
    public bool NotifyUser { get; set; } = false;
    public string? ChangeReason { get; set; }
    public Guid AppliedBy { get; set; }
}

/// <summary>
/// Result of applying language to user
/// </summary>
public class ApplyLanguageToUserResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime AppliedAt { get; set; }
    public string PreviousLanguage { get; set; } = string.Empty;
    public string NewLanguage { get; set; } = string.Empty;
    public bool RequiresReload { get; set; }
    public List<string> AffectedComponents { get; set; } = new();
    public Dictionary<string, object> LocalizationData { get; set; } = new();
}

/// <summary>
/// Command to get available languages
/// </summary>
public class GetAvailableLanguagesQuery : IRequest<List<AvailableLanguageDto>>
{
    public Guid? TransportCompanyId { get; set; }
    public bool IncludeTranslationStatus { get; set; } = false;
    public bool IncludeUsageStatistics { get; set; } = false;
    public bool OnlyEnabled { get; set; } = true;
}

/// <summary>
/// Available language DTO
/// </summary>
public class AvailableLanguageDto
{
    public string LanguageCode { get; set; } = string.Empty;
    public string CountryCode { get; set; } = string.Empty;
    public string CultureCode { get; set; } = string.Empty;
    public string LanguageName { get; set; } = string.Empty;
    public string NativeName { get; set; } = string.Empty;
    public string? FlagIcon { get; set; }
    public bool IsEnabled { get; set; }
    public bool IsRightToLeft { get; set; }
    public int Priority { get; set; }
    public TranslationStatusDto? TranslationStatus { get; set; }
    public LanguageUsageStatisticsDto? UsageStatistics { get; set; }
}

/// <summary>
/// Translation status DTO
/// </summary>
public class TranslationStatusDto
{
    public decimal CompletionPercentage { get; set; }
    public int TotalStrings { get; set; }
    public int TranslatedStrings { get; set; }
    public int PendingStrings { get; set; }
    public DateTime LastUpdated { get; set; }
    public string Quality { get; set; } = string.Empty; // High, Medium, Low
    public bool IsAutoTranslated { get; set; }
}

/// <summary>
/// Language usage statistics DTO
/// </summary>
public class LanguageUsageStatisticsDto
{
    public int ActiveUsers { get; set; }
    public int TotalUsers { get; set; }
    public decimal UsagePercentage { get; set; }
    public DateTime LastUsed { get; set; }
    public Dictionary<string, int> UsageByFeature { get; set; } = new();
}

/// <summary>
/// DTO for Transport Company language preferences
/// </summary>
public class TransportCompanyLanguagePreferencesDto
{
    public Guid TransportCompanyId { get; set; }
    public TransportCompanyLanguagePreferences Preferences { get; set; } = new();
    public List<AvailableLanguageDto> AvailableLanguages { get; set; } = new();
    public LanguageUsageStatisticsDto? UsageStatistics { get; set; }
    public TranslationStatusDto? TranslationStatus { get; set; }
    public DateTime LastUpdated { get; set; }
    public Guid LastUpdatedBy { get; set; }
    public string LastUpdatedByName { get; set; } = string.Empty;
}

/// <summary>
/// Command to bulk update user languages
/// </summary>
public class BulkUpdateUserLanguagesCommand : IRequest<BulkUpdateUserLanguagesResult>
{
    public Guid TransportCompanyId { get; set; }
    public List<Guid> UserIds { get; set; } = new();
    public string LanguageCode { get; set; } = string.Empty;
    public string CountryCode { get; set; } = string.Empty;
    public bool NotifyUsers { get; set; } = false;
    public string? ChangeReason { get; set; }
    public Guid UpdatedBy { get; set; }
}

/// <summary>
/// Result of bulk language update
/// </summary>
public class BulkUpdateUserLanguagesResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public int TotalUsers { get; set; }
    public int SuccessfulUpdates { get; set; }
    public int FailedUpdates { get; set; }
    public List<UserLanguageUpdateResult> UserResults { get; set; } = new();
    public DateTime ProcessedAt { get; set; }
}

/// <summary>
/// Individual user language update result
/// </summary>
public class UserLanguageUpdateResult
{
    public Guid UserId { get; set; }
    public string UserName { get; set; } = string.Empty;
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public string PreviousLanguage { get; set; } = string.Empty;
    public string NewLanguage { get; set; } = string.Empty;
}
