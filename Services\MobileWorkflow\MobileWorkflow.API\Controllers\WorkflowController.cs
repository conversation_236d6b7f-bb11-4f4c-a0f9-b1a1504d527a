using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MobileWorkflow.Application.Commands.Workflow;
using MobileWorkflow.Application.DTOs;
using MobileWorkflow.Infrastructure.Services;

namespace MobileWorkflow.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class WorkflowController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly IWorkflowExecutionEngine _executionEngine;
    private readonly IBusinessProcessAutomationService _automationService;
    private readonly ILogger<WorkflowController> _logger;

    public WorkflowController(
        IMediator mediator,
        IWorkflowExecutionEngine executionEngine,
        IBusinessProcessAutomationService automationService,
        ILogger<WorkflowController> logger)
    {
        _mediator = mediator;
        _executionEngine = executionEngine;
        _automationService = automationService;
        _logger = logger;
    }

    /// <summary>
    /// Get all workflows with optional filtering
    /// </summary>
    [HttpGet]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<IEnumerable<WorkflowDto>>> GetWorkflows([FromQuery] string? category = null, [FromQuery] bool? isActive = null)
    {
        // Implementation would use GetWorkflowsQuery
        return Ok(new List<WorkflowDto>());
    }

    /// <summary>
    /// Get workflow by ID
    /// </summary>
    [HttpGet("{id}")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<WorkflowDto>> GetWorkflow(Guid id)
    {
        // Implementation would use GetWorkflowByIdQuery
        return Ok();
    }

    /// <summary>
    /// Create a new workflow
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<WorkflowDto>> CreateWorkflow([FromBody] CreateWorkflowCommand command)
    {
        try
        {
            var result = await _mediator.Send(command);
            return CreatedAtAction(nameof(GetWorkflow), new { id = result.Id }, result);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Start workflow execution
    /// </summary>
    [HttpPost("{workflowId}/execute")]
    public async Task<ActionResult<WorkflowExecutionDto>> StartExecution(Guid workflowId, [FromBody] StartWorkflowExecutionCommand command)
    {
        command.WorkflowId = workflowId;
        command.TriggeredBy = GetCurrentUserId();

        try
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Get workflow execution status
    /// </summary>
    [HttpGet("executions/{executionId}")]
    public async Task<ActionResult<WorkflowExecutionDto>> GetExecution(Guid executionId)
    {
        // Implementation would use GetWorkflowExecutionByIdQuery
        return Ok();
    }

    /// <summary>
    /// Get running workflow executions
    /// </summary>
    [HttpGet("executions/running")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<List<WorkflowExecutionDto>>> GetRunningExecutions()
    {
        var executions = await _executionEngine.GetRunningExecutionsAsync();
        // Map to DTOs
        return Ok(new List<WorkflowExecutionDto>());
    }

    /// <summary>
    /// Cancel workflow execution
    /// </summary>
    [HttpPost("executions/{executionId}/cancel")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult> CancelExecution(Guid executionId, [FromBody] CancelExecutionRequest request)
    {
        var success = await _executionEngine.CancelExecutionAsync(executionId, request.Reason);
        
        if (!success)
            return BadRequest("Failed to cancel execution");

        return NoContent();
    }

    /// <summary>
    /// Pause workflow execution
    /// </summary>
    [HttpPost("executions/{executionId}/pause")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult> PauseExecution(Guid executionId)
    {
        var success = await _executionEngine.PauseExecutionAsync(executionId);
        
        if (!success)
            return BadRequest("Failed to pause execution");

        return NoContent();
    }

    /// <summary>
    /// Continue paused workflow execution
    /// </summary>
    [HttpPost("executions/{executionId}/continue")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult> ContinueExecution(Guid executionId)
    {
        var result = await _executionEngine.ContinueExecutionAsync(executionId);
        
        if (!result.IsSuccess)
            return BadRequest(result.ErrorMessage);

        return NoContent();
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst("sub")?.Value ?? User.FindFirst("userId")?.Value;
        return Guid.TryParse(userIdClaim, out var userId) ? userId : Guid.Empty;
    }
}

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class WorkflowTasksController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<WorkflowTasksController> _logger;

    public WorkflowTasksController(IMediator mediator, ILogger<WorkflowTasksController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get tasks assigned to current user
    /// </summary>
    [HttpGet("assigned")]
    public async Task<ActionResult<List<WorkflowTaskDto>>> GetAssignedTasks([FromQuery] string? status = null)
    {
        var userId = GetCurrentUserId();
        // Implementation would use GetTasksAssignedToUserQuery
        return Ok(new List<WorkflowTaskDto>());
    }

    /// <summary>
    /// Get tasks by role
    /// </summary>
    [HttpGet("by-role/{role}")]
    public async Task<ActionResult<List<WorkflowTaskDto>>> GetTasksByRole(string role, [FromQuery] string? status = null)
    {
        // Implementation would use GetTasksByRoleQuery
        return Ok(new List<WorkflowTaskDto>());
    }

    /// <summary>
    /// Get overdue tasks
    /// </summary>
    [HttpGet("overdue")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<List<WorkflowTaskDto>>> GetOverdueTasks()
    {
        // Implementation would use GetOverdueTasksQuery
        return Ok(new List<WorkflowTaskDto>());
    }

    /// <summary>
    /// Start working on a task
    /// </summary>
    [HttpPost("{taskId}/start")]
    public async Task<ActionResult> StartTask(Guid taskId)
    {
        // Implementation would use StartTaskCommand
        return NoContent();
    }

    /// <summary>
    /// Complete a task
    /// </summary>
    [HttpPost("{taskId}/complete")]
    public async Task<ActionResult> CompleteTask(Guid taskId, [FromBody] CompleteTaskRequest request)
    {
        // Implementation would use CompleteTaskCommand
        return NoContent();
    }

    /// <summary>
    /// Reassign a task
    /// </summary>
    [HttpPost("{taskId}/reassign")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult> ReassignTask(Guid taskId, [FromBody] ReassignTaskRequest request)
    {
        // Implementation would use ReassignTaskCommand
        return NoContent();
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst("sub")?.Value ?? User.FindFirst("userId")?.Value;
        return Guid.TryParse(userIdClaim, out var userId) ? userId : Guid.Empty;
    }
}

[ApiController]
[Route("api/[controller]")]
[Authorize(Roles = "Admin,Manager")]
public class BusinessProcessController : ControllerBase
{
    private readonly IBusinessProcessAutomationService _automationService;
    private readonly ILogger<BusinessProcessController> _logger;

    public BusinessProcessController(
        IBusinessProcessAutomationService automationService,
        ILogger<BusinessProcessController> logger)
    {
        _automationService = automationService;
        _logger = logger;
    }

    /// <summary>
    /// Get all active business processes
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<List<BusinessProcess>>> GetActiveProcesses()
    {
        var processes = await _automationService.GetActiveProcessesAsync();
        return Ok(processes);
    }

    /// <summary>
    /// Get process templates by category
    /// </summary>
    [HttpGet("templates")]
    public async Task<ActionResult<List<ProcessTemplate>>> GetProcessTemplates([FromQuery] string category = "General")
    {
        var templates = await _automationService.GetProcessTemplatesAsync(category);
        return Ok(templates);
    }

    /// <summary>
    /// Create a new business process
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<AutomationResult>> CreateBusinessProcess([FromBody] BusinessProcessDefinition definition)
    {
        var result = await _automationService.CreateBusinessProcessAsync(definition);
        
        if (!result.IsSuccess)
            return BadRequest(result.ErrorMessage);

        return Ok(result);
    }

    /// <summary>
    /// Trigger a business process
    /// </summary>
    [HttpPost("{processName}/trigger")]
    public async Task<ActionResult<AutomationResult>> TriggerProcess(string processName, [FromBody] Dictionary<string, object> triggerData)
    {
        var triggeredBy = GetCurrentUserId();
        var result = await _automationService.TriggerProcessAsync(processName, triggerData, triggeredBy);
        
        if (!result.IsSuccess)
            return BadRequest(result.ErrorMessage);

        return Ok(result);
    }

    /// <summary>
    /// Get process status and metrics
    /// </summary>
    [HttpGet("{processId}/status")]
    public async Task<ActionResult<BusinessProcessStatus>> GetProcessStatus(Guid processId)
    {
        var status = await _automationService.GetProcessStatusAsync(processId);
        return Ok(status);
    }

    /// <summary>
    /// Get automation metrics
    /// </summary>
    [HttpGet("metrics")]
    public async Task<ActionResult<AutomationMetrics>> GetAutomationMetrics([FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
    {
        var start = startDate ?? DateTime.UtcNow.AddDays(-30);
        var end = endDate ?? DateTime.UtcNow;
        
        var metrics = await _automationService.GetAutomationMetricsAsync(start, end);
        return Ok(metrics);
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst("sub")?.Value ?? User.FindFirst("userId")?.Value;
        return Guid.TryParse(userIdClaim, out var userId) ? userId : Guid.Empty;
    }
}

// Request models
public class CancelExecutionRequest
{
    public string Reason { get; set; } = string.Empty;
}

public class CompleteTaskRequest
{
    public Dictionary<string, object> Result { get; set; } = new();
    public string? Notes { get; set; }
}

public class ReassignTaskRequest
{
    public Guid? NewAssignedTo { get; set; }
    public string? NewAssignedToRole { get; set; }
    public string Reason { get; set; } = string.Empty;
}
