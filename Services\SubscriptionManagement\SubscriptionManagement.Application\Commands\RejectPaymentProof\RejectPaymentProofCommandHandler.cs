using MediatR;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Domain.Interfaces;

namespace SubscriptionManagement.Application.Commands.RejectPaymentProof
{
    public class RejectPaymentProofCommandHandler : IRequestHandler<RejectPaymentProofCommand, bool>
    {
        private readonly ISubscriptionPaymentProofRepository _paymentProofRepository;
        private readonly ILogger<RejectPaymentProofCommandHandler> _logger;

        public RejectPaymentProofCommandHandler(
            ISubscriptionPaymentProofRepository paymentProofRepository,
            ILogger<RejectPaymentProofCommandHandler> logger)
        {
            _paymentProofRepository = paymentProofRepository;
            _logger = logger;
        }

        public async Task<bool> Handle(RejectPaymentProofCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Rejecting payment proof {PaymentProofId} by admin {AdminUserId}", 
                request.PaymentProofId, request.RejectedByUserId);

            // Get payment proof
            var paymentProof = await _paymentProofRepository.GetByIdAsync(request.PaymentProofId, cancellationToken);
            if (paymentProof == null)
            {
                throw new ArgumentException($"Payment proof with ID {request.PaymentProofId} not found");
            }

            if (!paymentProof.CanBeRejected())
            {
                throw new InvalidOperationException($"Payment proof cannot be rejected in its current status: {paymentProof.Status}");
            }

            try
            {
                // Reject the payment proof
                paymentProof.Reject(request.RejectedByUserId, request.RejectionReason, request.VerificationNotes);

                // Save changes
                await _paymentProofRepository.UpdateAsync(paymentProof, cancellationToken);

                _logger.LogInformation("Payment proof {PaymentProofId} rejected successfully by admin {AdminUserId} with reason: {RejectionReason}", 
                    request.PaymentProofId, request.RejectedByUserId, request.RejectionReason);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to reject payment proof {PaymentProofId}", request.PaymentProofId);
                throw;
            }
        }
    }
}
