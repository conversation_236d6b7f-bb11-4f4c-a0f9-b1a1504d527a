{"Logging": {"LogLevel": {"Default": "Debug", "Microsoft.AspNetCore": "Information", "Microsoft.EntityFrameworkCore": "Information", "AnalyticsBIService": "Debug"}}, "ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=TLI_AnalyticsBI_Dev;User Id=timescale;Password=timescale", "TimescaleConnection": "Host=localhost;Port=5432;Database=TLI_AnalyticsBI_Timescale_Dev;User Id=timescale;Password=timescale", "Redis": "localhost:6379", "RabbitMQ": "amqp://guest:guest@localhost:5672/"}, "JwtSettings": {"Secret": "development-super-secret-jwt-key-that-is-at-least-32-characters-long", "Issuer": "TLI.Identity.Dev", "Audience": "TLI.Services.Dev", "ExpiryMinutes": 120}, "AnalyticsSettings": {"DataRetentionDays": 30, "MetricAggregationInterval": "00:01:00", "ReportGenerationTimeout": "00:05:00", "MaxConcurrentReports": 2, "DefaultPageSize": 20, "MaxPageSize": 100, "EnableRealTimeAnalytics": true, "EnablePredictiveAnalytics": false, "CacheExpirationMinutes": 5}, "ReportSettings": {"DefaultExportFormat": "PDF", "MaxReportSize": 10485760, "ReportStoragePath": "dev-reports", "ReportRetentionDays": 7, "EnableScheduledReports": true, "MaxScheduledReportsPerUser": 5, "ReportGenerationConcurrency": 1}, "DashboardSettings": {"MaxWidgetsPerDashboard": 10, "DefaultRefreshInterval": "00:00:30", "MaxDashboardsPerUser": 10, "EnableRealTimeDashboards": true, "CacheWidgetData": false, "WidgetCacheExpirationMinutes": 1}, "AlertSettings": {"EnableAlerting": true, "AlertEvaluationInterval": "00:00:30", "MaxAlertsPerHour": 50, "AlertRetentionDays": 7, "DefaultCooldownMinutes": 5, "MaxEscalationLevels": 3}, "ExportSettings": {"SupportedFormats": ["PDF", "Excel", "CSV", "JSON"], "MaxExportSize": 5242880, "ExportTimeout": "00:02:00", "ExportStoragePath": "dev-exports", "ExportRetentionDays": 3, "EnableAsyncExports": false}, "MLSettings": {"EnableMachineLearning": false, "ModelTrainingInterval": "24:00:00", "PredictionCacheExpirationHours": 1, "AnomalyDetectionThreshold": 0.7, "ModelStoragePath": "dev-models", "MaxTrainingDataPoints": 10000}, "PerformanceSettings": {"QueryTimeout": "00:01:00", "MaxConcurrentQueries": 5, "EnableQueryOptimization": false, "EnableDataCompression": false, "BatchSize": 100, "ParallelProcessing": false}, "SecuritySettings": {"EnableDataMasking": false, "EnableAuditLogging": true, "RequireDataAccess": false, "EnableRowLevelSecurity": false, "DataClassificationEnabled": false}, "Services": {"Identity": {"BaseUrl": "http://localhost:5001"}, "DataStorage": {"BaseUrl": "http://localhost:5010"}, "AuditCompliance": {"BaseUrl": "http://localhost:5012"}, "CommunicationNotification": {"BaseUrl": "http://localhost:5009"}, "MonitoringObservability": {"BaseUrl": "http://localhost:5013"}}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Information", "System": "Information"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/analytics-bi-dev-.log", "rollingInterval": "Day", "retainedFileCountLimit": 3, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}]}}