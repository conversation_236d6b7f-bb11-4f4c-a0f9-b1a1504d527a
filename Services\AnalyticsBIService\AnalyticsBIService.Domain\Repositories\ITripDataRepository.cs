using AnalyticsBIService.Domain.Entities;
using Shared.Infrastructure.Repositories;

namespace AnalyticsBIService.Domain.Repositories;

/// <summary>
/// Repository interface for trip data operations
/// </summary>
public interface ITripDataRepository : IRepositoryBase<TripData, Guid>
{
    /// <summary>
    /// Get trip data by date range
    /// </summary>
    Task<IEnumerable<TripData>> GetByDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get trip data by driver ID
    /// </summary>
    Task<IEnumerable<TripData>> GetByDriverIdAsync(Guid driverId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get trip data by vehicle ID
    /// </summary>
    Task<IEnumerable<TripData>> GetByVehicleIdAsync(Guid vehicleId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get trip data by route
    /// </summary>
    Task<IEnumerable<TripData>> GetByRouteAsync(string origin, string destination, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get trip analytics summary
    /// </summary>
    Task<object> GetTripAnalyticsSummaryAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);
}
