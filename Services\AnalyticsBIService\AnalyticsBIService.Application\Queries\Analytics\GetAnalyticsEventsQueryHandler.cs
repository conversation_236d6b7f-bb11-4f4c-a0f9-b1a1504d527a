using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Domain.Enums;
using AnalyticsBIService.Domain.Repositories;
using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Infrastructure.Repositories;

namespace AnalyticsBIService.Application.Queries.Analytics;

/// <summary>
/// Handler for analytics events queries
/// </summary>
public class GetAnalyticsEventsQueryHandler :
    IRequestHandler<GetAnalyticsEventsQuery, PagedResult<AnalyticsEventDto>>,
    IRequestHandler<GetAnalyticsEventByIdQuery, AnalyticsEventDto?>,
    IRequestHandler<GetUserActivityEventsQuery, PagedResult<AnalyticsEventDto>>,
    IRequestHandler<GetBusinessTransactionEventsQuery, PagedResult<AnalyticsEventDto>>,
    IRequestHandler<GetEventStatisticsQuery, List<EventStatisticsDto>>,
    IRequestHandler<GetTopEventsQuery, List<TopEventDto>>
{
    private readonly IAnalyticsEventRepository _analyticsEventRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetAnalyticsEventsQueryHandler> _logger;

    public GetAnalyticsEventsQueryHandler(
        IAnalyticsEventRepository analyticsEventRepository,
        IMapper mapper,
        ILogger<GetAnalyticsEventsQueryHandler> logger)
    {
        _analyticsEventRepository = analyticsEventRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<PagedResult<AnalyticsEventDto>> Handle(GetAnalyticsEventsQuery request, CancellationToken cancellationToken)
    {
        _logger.LogDebug("Getting analytics events with filters");

        // Get date range for repository call
        var fromDate = request.FromDate ?? DateTime.MinValue;
        var toDate = request.ToDate ?? DateTime.MaxValue;

        var allEvents = await _analyticsEventRepository.GetByDateRangeAsync(fromDate, toDate, 1, int.MaxValue, cancellationToken);

        var filteredEvents = allEvents.Items.AsEnumerable();

        // Apply filters
        if (request.EventType.HasValue)
            filteredEvents = filteredEvents.Where(e => e.EventType == request.EventType.Value);

        if (request.DataSource.HasValue)
            filteredEvents = filteredEvents.Where(e => e.DataSource == request.DataSource.Value);

        if (request.UserId.HasValue)
            filteredEvents = filteredEvents.Where(e => e.UserId == request.UserId.Value);

        if (request.UserType.HasValue)
            filteredEvents = filteredEvents.Where(e => e.UserType == request.UserType.Value);

        if (!string.IsNullOrWhiteSpace(request.EventName))
            filteredEvents = filteredEvents.Where(e => e.EventName.Contains(request.EventName));

        // Apply ordering and pagination
        var orderedEvents = filteredEvents.OrderByDescending(e => e.Timestamp);
        var totalCount = orderedEvents.Count();

        var events = orderedEvents
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .ToList();

        var eventDtos = _mapper.Map<List<AnalyticsEventDto>>(events);

        return new PagedResult<AnalyticsEventDto>
        {
            Items = eventDtos,
            TotalCount = totalCount,
            PageNumber = request.PageNumber,
            PageSize = request.PageSize
        };
    }

    public async Task<AnalyticsEventDto?> Handle(GetAnalyticsEventByIdQuery request, CancellationToken cancellationToken)
    {
        var analyticsEvent = await _analyticsEventRepository.GetByIdAsync(request.EventId, cancellationToken);

        return analyticsEvent != null ? _mapper.Map<AnalyticsEventDto>(analyticsEvent) : null;
    }

    public async Task<PagedResult<AnalyticsEventDto>> Handle(GetUserActivityEventsQuery request, CancellationToken cancellationToken)
    {
        // Get date range for repository call
        var fromDate = request.FromDate ?? DateTime.MinValue;
        var toDate = request.ToDate ?? DateTime.MaxValue;

        var allEvents = await _analyticsEventRepository.GetByDateRangeAsync(fromDate, toDate, 1, int.MaxValue, cancellationToken);

        var filteredEvents = allEvents.Items
            .Where(e => e.UserId == request.UserId && e.EventType == AnalyticsEventType.UserActivity);

        if (!string.IsNullOrWhiteSpace(request.EventName))
            filteredEvents = filteredEvents.Where(e => e.EventName.Contains(request.EventName));

        var orderedEvents = filteredEvents.OrderByDescending(e => e.Timestamp);
        var totalCount = orderedEvents.Count();

        var events = orderedEvents
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .ToList();

        var eventDtos = _mapper.Map<List<AnalyticsEventDto>>(events);

        return new PagedResult<AnalyticsEventDto>
        {
            Items = eventDtos,
            TotalCount = totalCount,
            PageNumber = request.PageNumber,
            PageSize = request.PageSize
        };
    }

    public async Task<PagedResult<AnalyticsEventDto>> Handle(GetBusinessTransactionEventsQuery request, CancellationToken cancellationToken)
    {
        // Get date range for repository call
        var fromDate = request.FromDate ?? DateTime.MinValue;
        var toDate = request.ToDate ?? DateTime.MaxValue;

        var allEvents = await _analyticsEventRepository.GetByDateRangeAsync(fromDate, toDate, 1, int.MaxValue, cancellationToken);

        var filteredEvents = allEvents.Items
            .Where(e => e.EventType == AnalyticsEventType.BusinessTransaction);

        if (request.DataSource.HasValue)
            filteredEvents = filteredEvents.Where(e => e.DataSource == request.DataSource.Value);

        if (!string.IsNullOrWhiteSpace(request.EntityType))
            filteredEvents = filteredEvents.Where(e => e.EntityType == request.EntityType);

        if (request.EntityId.HasValue)
            filteredEvents = filteredEvents.Where(e => e.EntityId == request.EntityId.Value);

        var orderedEvents = filteredEvents.OrderByDescending(e => e.Timestamp);
        var totalCount = orderedEvents.Count();

        var events = orderedEvents
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .ToList();

        var eventDtos = _mapper.Map<List<AnalyticsEventDto>>(events);

        return new PagedResult<AnalyticsEventDto>
        {
            Items = eventDtos,
            TotalCount = totalCount,
            PageNumber = request.PageNumber,
            PageSize = request.PageSize
        };
    }

    public async Task<List<EventStatisticsDto>> Handle(GetEventStatisticsQuery request, CancellationToken cancellationToken)
    {
        _logger.LogDebug("Getting event statistics from {FromDate} to {ToDate}", request.FromDate, request.ToDate);

        var allEvents = await _analyticsEventRepository.GetByDateRangeAsync(request.FromDate, request.ToDate, 1, int.MaxValue, cancellationToken);

        var filteredEvents = allEvents.Items.AsEnumerable();

        if (request.EventType.HasValue)
            filteredEvents = filteredEvents.Where(e => e.EventType == request.EventType.Value);

        if (request.DataSource.HasValue)
            filteredEvents = filteredEvents.Where(e => e.DataSource == request.DataSource.Value);

        if (request.UserType.HasValue)
            filteredEvents = filteredEvents.Where(e => e.UserType == request.UserType.Value);

        // Group by time period
        var groupedEvents = request.GroupBy switch
        {
            TimePeriod.Hourly => filteredEvents.GroupBy(e => new
            {
                PeriodStart = new DateTime(e.Timestamp.Year, e.Timestamp.Month, e.Timestamp.Day, e.Timestamp.Hour, 0, 0),
                e.EventType,
                e.DataSource,
                e.UserType
            }),
            TimePeriod.Daily => filteredEvents.GroupBy(e => new
            {
                PeriodStart = e.Timestamp.Date,
                e.EventType,
                e.DataSource,
                e.UserType
            }),
            TimePeriod.Weekly => filteredEvents.GroupBy(e => new
            {
                PeriodStart = e.Timestamp.Date.AddDays(-(int)e.Timestamp.DayOfWeek),
                e.EventType,
                e.DataSource,
                e.UserType
            }),
            TimePeriod.Monthly => filteredEvents.GroupBy(e => new
            {
                PeriodStart = new DateTime(e.Timestamp.Year, e.Timestamp.Month, 1),
                e.EventType,
                e.DataSource,
                e.UserType
            }),
            _ => filteredEvents.GroupBy(e => new
            {
                PeriodStart = e.Timestamp.Date,
                e.EventType,
                e.DataSource,
                e.UserType
            })
        };

        var statistics = groupedEvents
            .Select(g => new EventStatisticsDto
            {
                PeriodStart = g.Key.PeriodStart,
                PeriodEnd = GetPeriodEnd(g.Key.PeriodStart, request.GroupBy),
                Period = request.GroupBy.ToString(),
                EventType = g.Key.EventType.ToString(),
                DataSource = g.Key.DataSource.ToString(),
                UserType = g.Key.UserType.ToString(),
                EventCount = g.Count(),
                UniqueUsers = g.Where(e => e.UserId.HasValue).Select(e => e.UserId).Distinct().Count(),
                UniqueSessions = g.Where(e => !string.IsNullOrEmpty(e.SessionId)).Select(e => e.SessionId).Distinct().Count()
            })
            .OrderBy(s => s.PeriodStart)
            .ToList();

        return statistics;
    }

    public async Task<List<TopEventDto>> Handle(GetTopEventsQuery request, CancellationToken cancellationToken)
    {
        var allEvents = await _analyticsEventRepository.GetByDateRangeAsync(request.FromDate, request.ToDate, 1, int.MaxValue, cancellationToken);

        var filteredEvents = allEvents.Items.AsEnumerable();

        if (request.EventType.HasValue)
            filteredEvents = filteredEvents.Where(e => e.EventType == request.EventType.Value);

        if (request.DataSource.HasValue)
            filteredEvents = filteredEvents.Where(e => e.DataSource == request.DataSource.Value);

        var topEvents = filteredEvents
            .GroupBy(e => new { e.EventName, e.EventType, e.DataSource })
            .Select(g => new TopEventDto
            {
                EventName = g.Key.EventName,
                EventType = g.Key.EventType.ToString(),
                DataSource = g.Key.DataSource.ToString(),
                EventCount = g.Count(),
                UniqueUsers = g.Where(e => e.UserId.HasValue).Select(e => e.UserId).Distinct().Count(),
                FirstOccurrence = g.Min(e => e.Timestamp),
                LastOccurrence = g.Max(e => e.Timestamp)
            })
            .OrderByDescending(e => e.EventCount)
            .Take(request.TopCount)
            .ToList();

        return topEvents;
    }

    private static DateTime GetPeriodEnd(DateTime periodStart, TimePeriod period)
    {
        return period switch
        {
            TimePeriod.Hourly => periodStart.AddHours(1).AddTicks(-1),
            TimePeriod.Daily => periodStart.AddDays(1).AddTicks(-1),
            TimePeriod.Weekly => periodStart.AddDays(7).AddTicks(-1),
            TimePeriod.Monthly => periodStart.AddMonths(1).AddTicks(-1),
            _ => periodStart.AddDays(1).AddTicks(-1)
        };
    }
}
