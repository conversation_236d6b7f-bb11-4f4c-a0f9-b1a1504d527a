using MediatR;
using TripManagement.Application.DTOs;

namespace TripManagement.Application.Commands.ProcessGeofenceEvent;

public record ProcessGeofenceEventCommand : IRequest<bool>
{
    public Guid TripId { get; init; }
    public Guid DriverId { get; init; }
    public LocationDto CurrentLocation { get; init; } = null!;
    public GeofenceEventType EventType { get; init; }
    public string GeofenceName { get; init; } = string.Empty;
    public DateTime EventTime { get; init; }
}

public enum GeofenceEventType
{
    Enter = 0,
    Exit = 1,
    Dwell = 2,
    Approach = 3
}
