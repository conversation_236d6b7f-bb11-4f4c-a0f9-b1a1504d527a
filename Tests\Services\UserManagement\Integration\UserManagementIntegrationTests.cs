using System.Net;
using System.Net.Http.Json;
using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using Tests.Shared.Builders;
using Tests.Shared.Infrastructure;
using Tests.Shared.Utilities;
using UserManagement.API;
using Xunit;
using Xunit.Abstractions;

namespace Tests.Services.UserManagement.Integration;

[Trait("Category", "Integration")]
[Trait("Service", "UserManagement")]
public class UserManagementIntegrationTests : BaseIntegrationTest<Program>
{
    private readonly ITestOutputHelper _output;
    private readonly UserTestDataBuilder _userBuilder;

    public UserManagementIntegrationTests(
        IntegrationTestWebApplicationFactory<Program> factory,
        ITestOutputHelper output) : base(factory)
    {
        _output = output;
        _userBuilder = new UserTestDataBuilder();
    }

    [Fact]
    public async Task CreateUser_WithValidData_ShouldReturnCreatedUser()
    {
        // Arrange
        var userData = _userBuilder.Build();
        var createRequest = new CreateUserRequest
        {
            Email = userData.Email,
            FirstName = userData.FirstName,
            LastName = userData.LastName,
            PhoneNumber = userData.PhoneNumber,
            CompanyName = userData.CompanyName
        };

        // Act
        var response = await Client.PostAsJsonAsync("/api/users", createRequest);

        // Assert
        response.Should().HaveStatusCode(HttpStatusCode.Created);
        
        var createdUser = await response.ShouldBeSuccessfulWith<UserResponse>();
        createdUser.Email.Should().Be(userData.Email);
        createdUser.FirstName.Should().Be(userData.FirstName);
        createdUser.LastName.Should().Be(userData.LastName);
        
        _output.WriteLine($"Created user: {TestUtilities.ToJson(createdUser)}");
    }

    [Fact]
    public async Task CreateUser_WithInvalidEmail_ShouldReturnBadRequest()
    {
        // Arrange
        var createRequest = new CreateUserRequest
        {
            Email = "invalid-email",
            FirstName = "John",
            LastName = "Doe",
            PhoneNumber = "+1234567890",
            CompanyName = "Test Company"
        };

        // Act
        var response = await Client.PostAsJsonAsync("/api/users", createRequest);

        // Assert
        response.Should().HaveStatusCode(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task GetUser_WithValidId_ShouldReturnUser()
    {
        // Arrange
        var user = await CreateTestUserAsync();

        // Act
        var response = await Client.GetAsync($"/api/users/{user.Id}");

        // Assert
        response.Should().HaveStatusCode(HttpStatusCode.OK);
        
        var retrievedUser = await response.ShouldBeSuccessfulWith<UserResponse>();
        retrievedUser.Id.Should().Be(user.Id);
        retrievedUser.Email.Should().Be(user.Email);
    }

    [Fact]
    public async Task GetUser_WithInvalidId_ShouldReturnNotFound()
    {
        // Arrange
        var invalidId = Guid.NewGuid();

        // Act
        var response = await Client.GetAsync($"/api/users/{invalidId}");

        // Assert
        response.Should().HaveStatusCode(HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task UpdateUser_WithValidData_ShouldReturnUpdatedUser()
    {
        // Arrange
        var user = await CreateTestUserAsync();
        var updateRequest = new UpdateUserRequest
        {
            FirstName = "Updated",
            LastName = "Name",
            PhoneNumber = "+9876543210"
        };

        // Act
        var response = await Client.PutAsJsonAsync($"/api/users/{user.Id}", updateRequest);

        // Assert
        response.Should().HaveStatusCode(HttpStatusCode.OK);
        
        var updatedUser = await response.ShouldBeSuccessfulWith<UserResponse>();
        updatedUser.FirstName.Should().Be("Updated");
        updatedUser.LastName.Should().Be("Name");
        updatedUser.PhoneNumber.Should().Be("+9876543210");
    }

    [Fact]
    public async Task DeleteUser_WithValidId_ShouldReturnNoContent()
    {
        // Arrange
        var user = await CreateTestUserAsync();

        // Act
        var response = await Client.DeleteAsync($"/api/users/{user.Id}");

        // Assert
        response.Should().HaveStatusCode(HttpStatusCode.NoContent);

        // Verify user is deleted
        var getResponse = await Client.GetAsync($"/api/users/{user.Id}");
        getResponse.Should().HaveStatusCode(HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task GetUsersByCompany_WithValidCompany_ShouldReturnUsers()
    {
        // Arrange
        var companyName = "Test Company";
        var users = new List<UserResponse>();
        
        for (int i = 0; i < 3; i++)
        {
            var userData = _userBuilder.WithCompany(companyName).Build();
            var user = await CreateTestUserAsync(userData);
            users.Add(user);
        }

        // Act
        var response = await Client.GetAsync($"/api/users/company/{Uri.EscapeDataString(companyName)}");

        // Assert
        response.Should().HaveStatusCode(HttpStatusCode.OK);
        
        var companyUsers = await response.ShouldBeSuccessfulWith<List<UserResponse>>();
        companyUsers.Should().HaveCountGreaterOrEqualTo(3);
        companyUsers.Should().OnlyContain(u => u.CompanyName == companyName);
    }

    [Fact]
    public async Task HealthCheck_ShouldReturnHealthy()
    {
        // Act
        var response = await Client.GetAsync("/health");

        // Assert
        response.Should().HaveStatusCode(HttpStatusCode.OK);
        
        var healthStatus = await response.Content.ReadAsStringAsync();
        healthStatus.Should().Contain("Healthy");
    }

    [Fact]
    public async Task ConcurrentUserCreation_ShouldHandleMultipleRequests()
    {
        // Arrange
        var tasks = new List<Task<HttpResponseMessage>>();
        
        for (int i = 0; i < 10; i++)
        {
            var userData = _userBuilder.Build();
            var createRequest = new CreateUserRequest
            {
                Email = userData.Email,
                FirstName = userData.FirstName,
                LastName = userData.LastName,
                PhoneNumber = userData.PhoneNumber,
                CompanyName = userData.CompanyName
            };
            
            tasks.Add(Client.PostAsJsonAsync("/api/users", createRequest));
        }

        // Act
        var responses = await Task.WhenAll(tasks);

        // Assert
        responses.Should().OnlyContain(r => r.StatusCode == HttpStatusCode.Created);
        
        _output.WriteLine($"Successfully created {responses.Length} users concurrently");
    }

    private async Task<UserResponse> CreateTestUserAsync(UserTestData? userData = null)
    {
        userData ??= _userBuilder.Build();
        
        var createRequest = new CreateUserRequest
        {
            Email = userData.Email,
            FirstName = userData.FirstName,
            LastName = userData.LastName,
            PhoneNumber = userData.PhoneNumber,
            CompanyName = userData.CompanyName
        };

        var response = await Client.PostAsJsonAsync("/api/users", createRequest);
        response.EnsureSuccessStatusCode();
        
        return await response.ShouldBeSuccessfulWith<UserResponse>();
    }
}

// DTOs for testing
public class CreateUserRequest
{
    public string Email { get; set; } = string.Empty;
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string PhoneNumber { get; set; } = string.Empty;
    public string CompanyName { get; set; } = string.Empty;
}

public class UpdateUserRequest
{
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string PhoneNumber { get; set; } = string.Empty;
}

public class UserResponse
{
    public Guid Id { get; set; }
    public string Email { get; set; } = string.Empty;
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string PhoneNumber { get; set; } = string.Empty;
    public string CompanyName { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public bool IsActive { get; set; }
}
