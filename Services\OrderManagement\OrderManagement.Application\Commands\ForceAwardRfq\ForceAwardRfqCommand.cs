using MediatR;

namespace OrderManagement.Application.Commands.ForceAwardRfq;

public class ForceAwardRfqCommand : IRequest<bool>
{
    public Guid RfqId { get; set; }
    public Guid BidId { get; set; }
    public Guid AdminUserId { get; set; }
    public string AdminUserName { get; set; } = string.Empty;
    public string AdminRole { get; set; } = string.Empty;
    public string ForceAwardReason { get; set; } = string.Empty;
    public string? AdditionalNotes { get; set; }
    public bool OverrideValidation { get; set; } = false;
    public bool NotifyStakeholders { get; set; } = true;
    public Dictionary<string, object>? AuditMetadata { get; set; }
}
