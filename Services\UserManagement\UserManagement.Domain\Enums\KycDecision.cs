namespace UserManagement.Domain.Enums;

/// <summary>
/// Represents the decision made during KYC approval process
/// </summary>
public enum KycDecision
{
    /// <summary>
    /// KYC documents are approved
    /// </summary>
    Approved = 0,
    
    /// <summary>
    /// KYC documents are rejected
    /// </summary>
    Rejected = 1,
    
    /// <summary>
    /// KYC requires additional information
    /// </summary>
    RequiresMoreInfo = 2,
    
    /// <summary>
    /// KYC is under review
    /// </summary>
    UnderReview = 3,
    
    /// <summary>
    /// KYC is escalated to higher authority
    /// </summary>
    Escalated = 4
}
