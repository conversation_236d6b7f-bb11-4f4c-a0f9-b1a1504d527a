using MediatR;
using TripManagement.Application.DTOs;

namespace TripManagement.Application.Commands.ForecastDriverAvailability;

public record ForecastDriverAvailabilityCommand : IRequest<DriverAvailabilityForecast>
{
    public Guid? DriverId { get; init; } // If null, forecast for all drivers
    public Guid? CarrierId { get; init; } // If specified, filter by carrier
    public DateTime StartDate { get; init; }
    public DateTime EndDate { get; init; }
    public bool IncludeTrips { get; init; } = true;
    public bool IncludeLicenseExpiry { get; init; } = true;
    public bool IncludePerformanceFactors { get; init; } = true;
}

public record DriverAvailabilityForecast
{
    public DateTime ForecastDate { get; init; }
    public List<DriverAvailabilityDto> DriverAvailabilities { get; init; } = new();
    public DriverAvailabilitySummary Summary { get; init; } = null!;
}

public record DriverAvailabilityDto
{
    public Guid DriverId { get; init; }
    public string FullName { get; init; } = string.Empty;
    public string LicenseNumber { get; init; } = string.Empty;
    public List<DriverAvailabilityPeriod> AvailabilityPeriods { get; init; } = new();
    public List<DriverUnavailabilityPeriod> UnavailabilityPeriods { get; init; } = new();
    public double UtilizationPercentage { get; init; }
    public bool IsCurrentlyAvailable { get; init; }
    public DateTime? NextAvailableDate { get; init; }
    public decimal PerformanceRating { get; init; }
    public DriverReliabilityScore ReliabilityScore { get; init; } = null!;
}

public record DriverAvailabilityPeriod
{
    public DateTime StartDate { get; init; }
    public DateTime EndDate { get; init; }
    public DriverAvailabilityType Type { get; init; }
    public decimal ConfidenceLevel { get; init; } // 0-100%
}

public record DriverUnavailabilityPeriod
{
    public DateTime StartDate { get; init; }
    public DateTime EndDate { get; init; }
    public DriverUnavailabilityReason Reason { get; init; }
    public string Description { get; init; } = string.Empty;
    public Guid? RelatedEntityId { get; init; } // Trip ID, etc.
}

public record DriverAvailabilitySummary
{
    public int TotalDrivers { get; init; }
    public int AvailableDrivers { get; init; }
    public int UnavailableDrivers { get; init; }
    public int DriversOnTrips { get; init; }
    public int DriversWithExpiredLicenses { get; init; }
    public int HighPerformanceDrivers { get; init; }
    public double AverageUtilization { get; init; }
    public decimal AveragePerformanceRating { get; init; }
}

public record DriverReliabilityScore
{
    public decimal OnTimePercentage { get; init; }
    public decimal CompletionRate { get; init; }
    public decimal SafetyScore { get; init; }
    public decimal OverallReliability { get; init; }
    public List<string> ReliabilityFactors { get; init; } = new();
}

public enum DriverAvailabilityType
{
    Available = 0,
    PartiallyAvailable = 1,
    Reserved = 2,
    PreferredDriver = 3
}

public enum DriverUnavailabilityReason
{
    OnTrip = 0,
    OffDuty = 1,
    ExpiredLicense = 2,
    Suspended = 3,
    PersonalLeave = 4,
    Training = 5,
    Other = 6
}
