{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=TLI_AnalyticsBI;User Id=timescale;Password=timescale", "TimescaleConnection": "Host=localhost;Port=5432;Database=TLI_AnalyticsBI_Timescale;User Id=timescale;Password=timescale", "Redis": "localhost:6379", "RabbitMQ": "amqp://guest:guest@localhost:5672/"}, "JwtSettings": {"Secret": "your-super-secret-jwt-key-that-is-at-least-32-characters-long", "Issuer": "TLI.Identity", "Audience": "TLI.Services", "ExpiryMinutes": 60}, "RabbitMQ": {"Host": "localhost", "Port": 5672, "Username": "guest", "Password": "guest"}, "AnalyticsSettings": {"DataRetentionDays": 365, "MetricAggregationInterval": "00:05:00", "ReportGenerationTimeout": "00:10:00", "MaxConcurrentReports": 5, "DefaultPageSize": 50, "MaxPageSize": 1000, "EnableRealTimeAnalytics": true, "EnablePredictiveAnalytics": true, "CacheExpirationMinutes": 15}, "ReportSettings": {"DefaultExportFormat": "PDF", "MaxReportSize": 104857600, "ReportStoragePath": "reports", "ReportRetentionDays": 90, "EnableScheduledReports": true, "MaxScheduledReportsPerUser": 10, "ReportGenerationConcurrency": 3}, "DashboardSettings": {"MaxWidgetsPerDashboard": 20, "DefaultRefreshInterval": "00:01:00", "MaxDashboardsPerUser": 50, "EnableRealTimeDashboards": true, "CacheWidgetData": true, "WidgetCacheExpirationMinutes": 5}, "AlertSettings": {"EnableAlerting": true, "AlertEvaluationInterval": "00:01:00", "MaxAlertsPerHour": 100, "AlertRetentionDays": 30, "DefaultCooldownMinutes": 15, "MaxEscalationLevels": 5}, "ExportSettings": {"SupportedFormats": ["PDF", "Excel", "CSV", "JSON"], "MaxExportSize": 52428800, "ExportTimeout": "00:05:00", "ExportStoragePath": "exports", "ExportRetentionDays": 7, "EnableAsyncExports": true}, "MLSettings": {"EnableMachineLearning": true, "ModelTrainingInterval": "24:00:00", "PredictionCacheExpirationHours": 6, "AnomalyDetectionThreshold": 0.8, "ModelStoragePath": "models", "MaxTrainingDataPoints": 100000}, "PerformanceSettings": {"QueryTimeout": "00:02:00", "MaxConcurrentQueries": 10, "EnableQueryOptimization": true, "EnableDataCompression": true, "BatchSize": 1000, "ParallelProcessing": true}, "SecuritySettings": {"EnableDataMasking": true, "EnableAuditLogging": true, "RequireDataAccess": true, "EnableRowLevelSecurity": true, "DataClassificationEnabled": true}, "Services": {"Identity": {"BaseUrl": "http://localhost:5001"}, "DataStorage": {"BaseUrl": "http://localhost:5010"}, "AuditCompliance": {"BaseUrl": "http://localhost:5012"}, "CommunicationNotification": {"BaseUrl": "http://localhost:5009"}, "MonitoringObservability": {"BaseUrl": "http://localhost:5013"}}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/analytics-bi-.log", "rollingInterval": "Day", "retainedFileCountLimit": 7, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}]}}