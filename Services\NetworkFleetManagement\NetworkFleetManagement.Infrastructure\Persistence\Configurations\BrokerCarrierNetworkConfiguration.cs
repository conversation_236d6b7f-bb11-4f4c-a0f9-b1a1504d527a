using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetworkFleetManagement.Domain.Entities;

namespace NetworkFleetManagement.Infrastructure.Persistence.Configurations;

public class BrokerCarrierNetworkConfiguration : IEntityTypeConfiguration<BrokerCarrierNetwork>
{
    public void Configure(EntityTypeBuilder<BrokerCarrierNetwork> builder)
    {
        builder.ToTable("broker_carrier_networks");

        builder.HasKey(n => n.Id);

        builder.Property(n => n.Id)
            .ValueGeneratedNever();

        builder.Property(n => n.BrokerId)
            .IsRequired();

        builder.Property(n => n.CarrierId)
            .IsRequired();

        builder.Property(n => n.Status)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(n => n.EstablishedAt)
            .IsRequired();

        builder.Property(n => n.ActivatedAt);

        builder.Property(n => n.SuspendedAt);

        builder.Property(n => n.TerminatedAt);

        builder.Property(n => n.SuspensionReason)
            .HasMaxLength(500);

        builder.Property(n => n.TerminationReason)
            .HasMaxLength(500);

        builder.Property(n => n.PreferredRate)
            .HasPrecision(10, 2);

        builder.Property(n => n.Priority)
            .IsRequired();

        builder.Property(n => n.Notes)
            .HasMaxLength(1000);

        builder.Property(n => n.IsExclusive)
            .IsRequired();

        builder.Property(n => n.ExclusivityExpiresAt);

        builder.Property(n => n.MinimumRate)
            .HasPrecision(10, 2);

        builder.Property(n => n.MaximumRate)
            .HasPrecision(10, 2);

        builder.Property(n => n.PaymentTermsDays);

        builder.Property(n => n.ContractNumber)
            .HasMaxLength(50);

        builder.Property(n => n.ContractStartDate);

        builder.Property(n => n.ContractEndDate);

        builder.Property(n => n.CreatedAt)
            .IsRequired();

        builder.Property(n => n.UpdatedAt);

        // Configure PerformanceMetrics as owned entity
        builder.OwnsOne(n => n.PerformanceMetrics, pm =>
        {
            pm.Property(p => p.Rating)
                .HasColumnName("performance_rating")
                .HasPrecision(3, 2);

            pm.Property(p => p.TotalTrips)
                .HasColumnName("performance_total_trips");

            pm.Property(p => p.CompletedTrips)
                .HasColumnName("performance_completed_trips");

            pm.Property(p => p.CancelledTrips)
                .HasColumnName("performance_cancelled_trips");

            pm.Property(p => p.OnTimePercentage)
                .HasColumnName("performance_on_time_percentage")
                .HasPrecision(5, 2);

            pm.Property(p => p.CustomerSatisfactionScore)
                .HasColumnName("performance_customer_satisfaction")
                .HasPrecision(3, 2);

            pm.Property(p => p.LastUpdated)
                .HasColumnName("performance_last_updated");
        });

        // Configure relationships
        builder.HasMany(n => n.PerformanceRecords)
            .WithOne(pr => pr.Network)
            .HasForeignKey(pr => pr.NetworkId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(n => new { n.BrokerId, n.CarrierId })
            .IsUnique();

        builder.HasIndex(n => n.BrokerId);

        builder.HasIndex(n => n.CarrierId);

        builder.HasIndex(n => n.Status);

        builder.HasIndex(n => n.Priority);

        builder.HasIndex(n => n.IsExclusive);

        builder.HasIndex(n => n.ContractEndDate);

        builder.HasIndex(n => n.ExclusivityExpiresAt);
    }
}
