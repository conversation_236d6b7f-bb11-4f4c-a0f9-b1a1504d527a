using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.DTOs;
using SubscriptionManagement.Application.Interfaces;

namespace SubscriptionManagement.Application.Queries.GetMySubscription;

public class GetMySubscriptionQueryHandler : IRequestHandler<GetMySubscriptionQuery, SubscriptionDto?>
{
    private readonly ISubscriptionRepository _subscriptionRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetMySubscriptionQueryHandler> _logger;

    public GetMySubscriptionQueryHandler(
        ISubscriptionRepository subscriptionRepository,
        IMapper mapper,
        ILogger<GetMySubscriptionQueryHandler> logger)
    {
        _subscriptionRepository = subscriptionRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<SubscriptionDto?> Handle(GetMySubscriptionQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting active subscription for user: {UserId}", request.UserId);

        var subscription = await _subscriptionRepository.GetByUserIdAsync(request.UserId);
        
        if (subscription == null)
        {
            _logger.LogInformation("No subscription found for user {UserId}", request.UserId);
            return null;
        }

        var result = _mapper.Map<SubscriptionDto>(subscription);

        _logger.LogInformation("Successfully retrieved subscription {SubscriptionId} for user {UserId}", 
            subscription.Id, request.UserId);

        return result;
    }
}
