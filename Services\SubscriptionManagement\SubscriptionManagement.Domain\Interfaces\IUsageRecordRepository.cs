using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Domain.Interfaces
{
    public interface IUsageRecordRepository
    {
        Task<UsageRecord?> GetByIdAsync(Guid id);
        Task<List<UsageRecord>> GetByUserIdAsync(Guid userId);
        Task<List<UsageRecord>> GetBySubscriptionIdAsync(Guid subscriptionId);
        Task<List<UsageRecord>> GetByFeatureTypeAsync(FeatureType featureType);
        Task<List<UsageRecord>> GetUsageHistoryAsync(Guid userId, FeatureType? featureType = null, DateTime? from = null, DateTime? to = null);
        Task<Dictionary<FeatureType, int>> GetUsageSummaryAsync(Guid userId, DateTime from, DateTime to);
        Task<List<UsageRecord>> GetTopUsersAsync(FeatureType featureType, int count, DateTime from, DateTime to);
        Task<Dictionary<FeatureType, double>> GetAverageUsageAsync(DateTime from, DateTime to);
        Task<UsageRecord> AddAsync(UsageRecord usageRecord);
        Task UpdateAsync(UsageRecord usageRecord);
        Task DeleteAsync(Guid id);
        Task<int> GetTotalUsageCountAsync(Guid userId, FeatureType featureType, DateTime from, DateTime to);
        Task<List<UsageRecord>> GetUsageByPeriodAsync(Guid userId, DateTime periodStart, DateTime periodEnd);
    }
}
