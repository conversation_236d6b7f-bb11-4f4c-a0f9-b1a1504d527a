using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using TripManagement.Domain.Entities;
using TripManagement.Domain.Enums;
using TripManagement.Application.Interfaces;
using TripManagement.Application.DTOs;

namespace TripManagement.Application.Services;

public interface IMilestoneMonitoringService
{
    Task<List<MilestoneAlert>> CheckMilestonesAsync(Guid tripId, CancellationToken cancellationToken = default);
    Task<List<MilestoneAlert>> CheckAllActiveTripMilestonesAsync(CancellationToken cancellationToken = default);
    Task<MilestoneStatus> GetMilestoneStatusAsync(Guid tripId, Guid stopId, CancellationToken cancellationToken = default);
    Task UpdateMilestoneThresholdsAsync(Guid tripId, MilestoneThresholds thresholds, CancellationToken cancellationToken = default);
    Task<List<MilestonePerformanceMetrics>> GetMilestonePerformanceAsync(Guid tripId, CancellationToken cancellationToken = default);
}

public class MilestoneMonitoringService : IMilestoneMonitoringService
{
    private readonly ILogger<MilestoneMonitoringService> _logger;
    private readonly ITripRepository _tripRepository;
    private readonly IAdvancedETACalculationService _etaService;
    private readonly INotificationService _notificationService;
    private readonly IMessageBroker _messageBroker;
    private readonly MilestoneMonitoringOptions _options;

    public MilestoneMonitoringService(
        ILogger<MilestoneMonitoringService> logger,
        ITripRepository tripRepository,
        IAdvancedETACalculationService etaService,
        INotificationService notificationService,
        IMessageBroker messageBroker,
        IOptions<MilestoneMonitoringOptions> options)
    {
        _logger = logger;
        _tripRepository = tripRepository;
        _etaService = etaService;
        _notificationService = notificationService;
        _messageBroker = messageBroker;
        _options = options.Value;
    }

    public async Task<List<MilestoneAlert>> CheckMilestonesAsync(Guid tripId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Checking milestones for trip {TripId}", tripId);

            var trip = await _tripRepository.GetByIdWithStopsAsync(tripId, cancellationToken);
            if (trip == null)
            {
                _logger.LogWarning("Trip {TripId} not found", tripId);
                return new List<MilestoneAlert>();
            }

            var alerts = new List<MilestoneAlert>();
            var currentLocation = trip.GetCurrentLocation();

            if (currentLocation == null)
            {
                _logger.LogWarning("No current location available for trip {TripId}", tripId);
                return alerts;
            }

            foreach (var stop in trip.Stops.Where(s => s.Status == TripStopStatus.Pending))
            {
                var alert = await CheckStopMilestoneAsync(trip, stop, currentLocation, cancellationToken);
                if (alert != null)
                {
                    alerts.Add(alert);
                }
            }

            // Check overall trip milestone
            var tripAlert = await CheckTripMilestoneAsync(trip, currentLocation, cancellationToken);
            if (tripAlert != null)
            {
                alerts.Add(tripAlert);
            }

            // Process alerts
            foreach (var alert in alerts)
            {
                await ProcessMilestoneAlertAsync(alert, cancellationToken);
            }

            _logger.LogInformation("Found {AlertCount} milestone alerts for trip {TripId}", alerts.Count, tripId);
            return alerts;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking milestones for trip {TripId}", tripId);
            throw;
        }
    }

    public async Task<List<MilestoneAlert>> CheckAllActiveTripMilestonesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Checking milestones for all active trips");

            var activeTrips = await _tripRepository.GetActiveTripIdsAsync(cancellationToken);
            var allAlerts = new List<MilestoneAlert>();

            foreach (var tripId in activeTrips)
            {
                try
                {
                    var tripAlerts = await CheckMilestonesAsync(tripId, cancellationToken);
                    allAlerts.AddRange(tripAlerts);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error checking milestones for trip {TripId}", tripId);
                    // Continue with other trips
                }
            }

            _logger.LogInformation("Found {TotalAlerts} milestone alerts across {TripCount} active trips", 
                allAlerts.Count, activeTrips.Count);

            return allAlerts;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking milestones for all active trips");
            throw;
        }
    }

    public async Task<MilestoneStatus> GetMilestoneStatusAsync(Guid tripId, Guid stopId, CancellationToken cancellationToken = default)
    {
        try
        {
            var trip = await _tripRepository.GetByIdWithStopsAsync(tripId, cancellationToken);
            if (trip == null)
                throw new ArgumentException($"Trip {tripId} not found");

            var stop = trip.Stops.FirstOrDefault(s => s.Id == stopId);
            if (stop == null)
                throw new ArgumentException($"Stop {stopId} not found in trip {tripId}");

            var currentLocation = trip.GetCurrentLocation();
            if (currentLocation == null)
            {
                return new MilestoneStatus
                {
                    TripId = tripId,
                    StopId = stopId,
                    Status = "Unknown",
                    Message = "No current location available"
                };
            }

            var distanceToStop = currentLocation.CalculateDistanceKm(stop.Location);
            var isDelayed = stop.ScheduledArrival.HasValue && DateTime.UtcNow > stop.ScheduledArrival.Value;
            var delayDuration = isDelayed && stop.ScheduledArrival.HasValue 
                ? DateTime.UtcNow - stop.ScheduledArrival.Value 
                : TimeSpan.Zero;

            return new MilestoneStatus
            {
                TripId = tripId,
                StopId = stopId,
                Status = stop.Status.ToString(),
                IsDelayed = isDelayed,
                DelayDuration = delayDuration,
                DistanceToStopKm = distanceToStop,
                ScheduledArrival = stop.ScheduledArrival,
                EstimatedArrival = await CalculateEstimatedArrivalAsync(trip, stop, currentLocation, cancellationToken),
                Message = GenerateStatusMessage(stop, isDelayed, delayDuration, distanceToStop)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting milestone status for trip {TripId}, stop {StopId}", tripId, stopId);
            throw;
        }
    }

    public async Task UpdateMilestoneThresholdsAsync(Guid tripId, MilestoneThresholds thresholds, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Updating milestone thresholds for trip {TripId}", tripId);

            // This would typically store custom thresholds in the database
            // For now, we'll just log the update
            _logger.LogInformation("Updated thresholds for trip {TripId}: Warning={WarningMinutes}, Critical={CriticalMinutes}", 
                tripId, thresholds.WarningThresholdMinutes, thresholds.CriticalThresholdMinutes);

            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating milestone thresholds for trip {TripId}", tripId);
            throw;
        }
    }

    public async Task<List<MilestonePerformanceMetrics>> GetMilestonePerformanceAsync(Guid tripId, CancellationToken cancellationToken = default)
    {
        try
        {
            var trip = await _tripRepository.GetByIdWithStopsAsync(tripId, cancellationToken);
            if (trip == null)
                throw new ArgumentException($"Trip {tripId} not found");

            var metrics = new List<MilestonePerformanceMetrics>();

            foreach (var stop in trip.Stops)
            {
                var metric = new MilestonePerformanceMetrics
                {
                    TripId = tripId,
                    StopId = stop.Id,
                    StopType = stop.StopType,
                    ScheduledArrival = stop.ScheduledArrival,
                    ActualArrival = stop.ActualArrival,
                    IsOnTime = stop.ActualArrival.HasValue && stop.ScheduledArrival.HasValue 
                        ? stop.ActualArrival.Value <= stop.ScheduledArrival.Value.AddMinutes(_options.OnTimeThresholdMinutes)
                        : null,
                    DelayDuration = stop.ActualArrival.HasValue && stop.ScheduledArrival.HasValue
                        ? stop.ActualArrival.Value - stop.ScheduledArrival.Value
                        : null
                };

                metrics.Add(metric);
            }

            return metrics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting milestone performance for trip {TripId}", tripId);
            throw;
        }
    }

    private async Task<MilestoneAlert?> CheckStopMilestoneAsync(Trip trip, TripStop stop, Location currentLocation, CancellationToken cancellationToken)
    {
        if (!stop.ScheduledArrival.HasValue)
            return null;

        var now = DateTime.UtcNow;
        var scheduledArrival = stop.ScheduledArrival.Value;
        var delayMinutes = (now - scheduledArrival).TotalMinutes;

        // Check if we're approaching the milestone
        var estimatedArrival = await CalculateEstimatedArrivalAsync(trip, stop, currentLocation, cancellationToken);
        var estimatedDelayMinutes = estimatedArrival.HasValue 
            ? (estimatedArrival.Value - scheduledArrival).TotalMinutes 
            : 0;

        MilestoneAlertSeverity? severity = null;
        string? message = null;

        if (delayMinutes > _options.CriticalThresholdMinutes)
        {
            severity = MilestoneAlertSeverity.Critical;
            message = $"Stop is {delayMinutes:F0} minutes overdue";
        }
        else if (delayMinutes > _options.WarningThresholdMinutes)
        {
            severity = MilestoneAlertSeverity.Warning;
            message = $"Stop is {delayMinutes:F0} minutes late";
        }
        else if (estimatedDelayMinutes > _options.WarningThresholdMinutes)
        {
            severity = MilestoneAlertSeverity.Warning;
            message = $"Stop estimated to be {estimatedDelayMinutes:F0} minutes late";
        }

        if (severity.HasValue)
        {
            return new MilestoneAlert
            {
                TripId = trip.Id,
                TripNumber = trip.TripNumber,
                StopId = stop.Id,
                StopType = stop.StopType,
                Severity = severity.Value,
                Message = message!,
                ScheduledTime = scheduledArrival,
                EstimatedTime = estimatedArrival,
                DelayMinutes = Math.Max(delayMinutes, estimatedDelayMinutes),
                DistanceToStopKm = currentLocation.CalculateDistanceKm(stop.Location),
                DetectedAt = now
            };
        }

        return null;
    }

    private async Task<MilestoneAlert?> CheckTripMilestoneAsync(Trip trip, Location currentLocation, CancellationToken cancellationToken)
    {
        var scheduledEndTime = trip.EstimatedEndTime;
        var now = DateTime.UtcNow;
        var delayMinutes = (now - scheduledEndTime).TotalMinutes;

        // Calculate estimated completion time
        var etaRequest = new ETACalculationRequest
        {
            TripId = trip.Id,
            CurrentLocation = currentLocation,
            DestinationLocation = trip.Route.EndLocation,
            RemainingStops = trip.Stops.Where(s => s.Status == TripStopStatus.Pending).ToList(),
            CalculationMethod = ETACalculationMethod.Hybrid
        };

        var etaResult = await _etaService.CalculateETAAsync(etaRequest, cancellationToken);
        var estimatedDelayMinutes = (etaResult.FinalETA - scheduledEndTime).TotalMinutes;

        MilestoneAlertSeverity? severity = null;
        string? message = null;

        if (delayMinutes > _options.CriticalThresholdMinutes)
        {
            severity = MilestoneAlertSeverity.Critical;
            message = $"Trip is {delayMinutes:F0} minutes overdue for completion";
        }
        else if (delayMinutes > _options.WarningThresholdMinutes)
        {
            severity = MilestoneAlertSeverity.Warning;
            message = $"Trip is {delayMinutes:F0} minutes late";
        }
        else if (estimatedDelayMinutes > _options.WarningThresholdMinutes)
        {
            severity = MilestoneAlertSeverity.Warning;
            message = $"Trip estimated to be {estimatedDelayMinutes:F0} minutes late";
        }

        if (severity.HasValue)
        {
            return new MilestoneAlert
            {
                TripId = trip.Id,
                TripNumber = trip.TripNumber,
                StopId = null,
                StopType = null,
                Severity = severity.Value,
                Message = message!,
                ScheduledTime = scheduledEndTime,
                EstimatedTime = etaResult.FinalETA,
                DelayMinutes = Math.Max(delayMinutes, estimatedDelayMinutes),
                DistanceToStopKm = currentLocation.CalculateDistanceKm(trip.Route.EndLocation),
                DetectedAt = now
            };
        }

        return null;
    }

    private async Task<DateTime?> CalculateEstimatedArrivalAsync(Trip trip, TripStop stop, Location currentLocation, CancellationToken cancellationToken)
    {
        try
        {
            var etaRequest = new ETACalculationRequest
            {
                TripId = trip.Id,
                CurrentLocation = currentLocation,
                DestinationLocation = stop.Location,
                CalculationMethod = ETACalculationMethod.Simple
            };

            var result = await _etaService.CalculateETAAsync(etaRequest, cancellationToken);
            return result.FinalETA;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to calculate ETA for stop {StopId}", stop.Id);
            return null;
        }
    }

    private async Task ProcessMilestoneAlertAsync(MilestoneAlert alert, CancellationToken cancellationToken)
    {
        try
        {
            // Send notification
            await _notificationService.SendMilestoneAlertAsync(alert, cancellationToken);

            // Publish integration event
            await _messageBroker.PublishAsync("milestone.alert", new
            {
                alert.TripId,
                alert.TripNumber,
                alert.StopId,
                alert.Severity,
                alert.Message,
                alert.DelayMinutes,
                alert.DetectedAt
            }, cancellationToken);

            _logger.LogInformation("Processed milestone alert for trip {TripId}: {Message}", 
                alert.TripId, alert.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing milestone alert for trip {TripId}", alert.TripId);
        }
    }

    private string GenerateStatusMessage(TripStop stop, bool isDelayed, TimeSpan delayDuration, double distanceKm)
    {
        if (isDelayed)
        {
            return $"Stop is {delayDuration.TotalMinutes:F0} minutes late, {distanceKm:F1}km away";
        }

        return $"Stop is on schedule, {distanceKm:F1}km away";
    }
}
