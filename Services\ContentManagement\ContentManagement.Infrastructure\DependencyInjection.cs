using ContentManagement.Application.Interfaces;
using ContentManagement.Domain.Entities;
using ContentManagement.Domain.Enums;
using ContentManagement.Infrastructure.Data;
using ContentManagement.Infrastructure.Repositories;
using ContentManagement.Infrastructure.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace ContentManagement.Infrastructure;

/// <summary>
/// Dependency injection configuration for the Infrastructure layer
/// </summary>
public static class DependencyInjection
{
    public static IServiceCollection AddInfrastructure(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        // Add DbContext
        services.AddDbContext<ContentManagementDbContext>(options =>
            options.UseNpgsql(
                configuration.GetConnectionString("DefaultConnection"),
                b => b.MigrationsAssembly(typeof(ContentManagementDbContext).Assembly.FullName)));

        // Add repositories
        services.AddScoped<IContentRepository, ContentRepository>();
        services.AddScoped<IFaqRepository, FaqRepository>();
        services.AddScoped<IPolicyRepository, PolicyRepository>();
        services.AddScoped<IPolicyAcceptanceRepository, PolicyAcceptanceRepository>();

        // Add services
        services.AddScoped<IContentAuditService, ContentAuditService>();
        services.AddScoped<IContentSearchService, ContentSearchService>();
        services.AddScoped<IContentPublishingService, ContentPublishingService>();
        services.AddScoped<IContentNotificationService, ContentNotificationService>();

        // Add background services
        services.AddHostedService<ScheduledPublishingService>();

        // Add HTTP clients for external service integration
        services.AddHttpClient("DataStorageService", client =>
        {
            client.BaseAddress = new Uri(configuration["Services:DataStorage:BaseUrl"] ?? "http://localhost:5008");
            client.Timeout = TimeSpan.FromSeconds(30);
        });

        services.AddHttpClient("AuditComplianceService", client =>
        {
            client.BaseAddress = new Uri(configuration["Services:AuditCompliance:BaseUrl"] ?? "http://localhost:5012");
            client.Timeout = TimeSpan.FromSeconds(30);
        });

        services.AddHttpClient("CommunicationNotificationService", client =>
        {
            client.BaseAddress = new Uri(configuration["Services:CommunicationNotification:BaseUrl"] ?? "http://localhost:5009");
            client.Timeout = TimeSpan.FromSeconds(30);
        });

        return services;
    }
}

/// <summary>
/// Placeholder for PolicyRepository - to be implemented
/// </summary>
public class PolicyRepository : IPolicyRepository
{
    private readonly ContentManagementDbContext _context;

    public PolicyRepository(ContentManagementDbContext context)
    {
        _context = context;
    }

    // Implementation would be similar to ContentRepository and FaqRepository
    // For brevity, showing method signatures only
    public Task<PolicyDocument?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
        => throw new NotImplementedException();

    public Task<List<PolicyDocument>> GetAllAsync(CancellationToken cancellationToken = default)
        => throw new NotImplementedException();

    public Task AddAsync(PolicyDocument policyDocument, CancellationToken cancellationToken = default)
        => throw new NotImplementedException();

    public Task UpdateAsync(PolicyDocument policyDocument, CancellationToken cancellationToken = default)
        => throw new NotImplementedException();

    public Task DeleteAsync(PolicyDocument policyDocument, CancellationToken cancellationToken = default)
        => throw new NotImplementedException();

    public Task<PolicyDocument?> GetByTypeAsync(PolicyType policyType, bool currentVersionOnly = true, CancellationToken cancellationToken = default)
        => throw new NotImplementedException();

    public Task<List<PolicyDocument>> GetVersionsAsync(PolicyType policyType, CancellationToken cancellationToken = default)
        => throw new NotImplementedException();

    public Task<List<PolicyDocument>> GetApplicablePoliciesAsync(List<string> userRoles, bool requiresAcceptanceOnly = false, bool currentVersionOnly = true, bool effectiveOnly = true, CancellationToken cancellationToken = default)
        => throw new NotImplementedException();

    public Task<List<PolicyDocument>> GetPoliciesRequiringAcceptanceAsync(List<string> userRoles, bool currentVersionOnly = true, bool effectiveOnly = true, CancellationToken cancellationToken = default)
        => throw new NotImplementedException();

    public Task<(List<PolicyDocument> Items, int TotalCount)> SearchAsync(string? searchTerm = null, PolicyType? policyType = null, ContentStatus? status = null, bool? requiresAcceptance = null, bool? isCurrentVersion = null, DateTime? effectiveFrom = null, DateTime? effectiveTo = null, List<string>? userRoles = null, bool isAuthenticated = false, string sortBy = "EffectiveDate", bool sortDescending = true, int pageNumber = 1, int pageSize = 20, CancellationToken cancellationToken = default)
        => throw new NotImplementedException();

    public Task<Dictionary<PolicyType, int>> GetPolicyCountByTypeAsync(CancellationToken cancellationToken = default)
        => throw new NotImplementedException();

    public Task<Dictionary<PolicyType, decimal>> GetComplianceRateByTypeAsync(CancellationToken cancellationToken = default)
        => throw new NotImplementedException();
}

/// <summary>
/// Placeholder for PolicyAcceptanceRepository - to be implemented
/// </summary>
public class PolicyAcceptanceRepository : IPolicyAcceptanceRepository
{
    private readonly ContentManagementDbContext _context;

    public PolicyAcceptanceRepository(ContentManagementDbContext context)
    {
        _context = context;
    }

    // Implementation would be similar to other repositories
    // For brevity, showing method signatures only
    public Task<PolicyAcceptance?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
        => throw new NotImplementedException();

    public Task<List<PolicyAcceptance>> GetByPolicyIdAsync(Guid policyId, CancellationToken cancellationToken = default)
        => throw new NotImplementedException();

    public Task<List<PolicyAcceptance>> GetByUserIdAsync(Guid userId, CancellationToken cancellationToken = default)
        => throw new NotImplementedException();

    public Task<PolicyAcceptance?> GetByPolicyAndUserAsync(Guid policyId, Guid userId, CancellationToken cancellationToken = default)
        => throw new NotImplementedException();

    public Task AddAsync(PolicyAcceptance acceptance, CancellationToken cancellationToken = default)
        => throw new NotImplementedException();

    public Task<bool> HasUserAcceptedPolicyAsync(Guid policyId, Guid userId, CancellationToken cancellationToken = default)
        => throw new NotImplementedException();

    public Task<int> GetAcceptanceCountAsync(Guid policyId, CancellationToken cancellationToken = default)
        => throw new NotImplementedException();

    public Task<List<PolicyAcceptance>> GetAcceptanceHistoryAsync(Guid? policyId = null, Guid? userId = null, DateTime? fromDate = null, DateTime? toDate = null, string sortBy = "AcceptedAt", bool sortDescending = true, int pageNumber = 1, int pageSize = 50, CancellationToken cancellationToken = default)
        => throw new NotImplementedException();
}

/// <summary>
/// Content search service implementation
/// </summary>
public class ContentSearchService : IContentSearchService
{
    private readonly ContentManagementDbContext _context;
    private readonly ILogger<ContentSearchService> _logger;

    public ContentSearchService(ContentManagementDbContext context, ILogger<ContentSearchService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task IndexContentAsync(Content content, CancellationToken cancellationToken = default)
    {
        // In a real implementation, this would index content in a search engine like Elasticsearch
        _logger.LogInformation("Indexing content {ContentId} for search", content.Id);
        await Task.CompletedTask;
    }

    public async Task RemoveFromIndexAsync(Guid contentId, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Removing content {ContentId} from search index", contentId);
        await Task.CompletedTask;
    }

    public async Task UpdateIndexAsync(Content content, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Updating search index for content {ContentId}", content.Id);
        await Task.CompletedTask;
    }

    public async Task<(List<Guid> ContentIds, int TotalCount)> SearchAsync(
        string searchTerm,
        ContentType? type = null,
        List<string>? tags = null,
        List<string>? userRoles = null,
        bool isAuthenticated = false,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default)
    {
        // Simple database search implementation
        var query = _context.Contents.AsQueryable();

        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            query = query.Where(c =>
                c.Title.Contains(searchTerm) ||
                c.Description!.Contains(searchTerm) ||
                c.ContentBody.Contains(searchTerm));
        }

        if (type.HasValue)
        {
            query = query.Where(c => c.Type == type.Value);
        }

        if (tags?.Any() == true)
        {
            foreach (var tag in tags)
            {
                query = query.Where(c => c.Tags.Contains(tag));
            }
        }

        var totalCount = await query.CountAsync(cancellationToken);
        var contentIds = await query
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .Select(c => c.Id)
            .ToListAsync(cancellationToken);

        return (contentIds, totalCount);
    }

    public async Task<List<string>> GetSearchSuggestionsAsync(
        string partialTerm,
        ContentType? type = null,
        int maxSuggestions = 10,
        CancellationToken cancellationToken = default)
    {
        var query = _context.Contents.AsQueryable();

        if (type.HasValue)
        {
            query = query.Where(c => c.Type == type.Value);
        }

        var suggestions = await query
            .Where(c => c.Title.StartsWith(partialTerm))
            .Select(c => c.Title)
            .Distinct()
            .Take(maxSuggestions)
            .ToListAsync(cancellationToken);

        return suggestions;
    }

    public async Task RebuildIndexAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Rebuilding search index");

        var contents = await _context.Contents
            .Where(c => c.Status == ContentStatus.Published)
            .ToListAsync(cancellationToken);

        foreach (var content in contents)
        {
            await IndexContentAsync(content, cancellationToken);
        }

        _logger.LogInformation("Search index rebuild completed for {Count} content items", contents.Count);
    }
}

/// <summary>
/// Content publishing service implementation
/// </summary>
public class ContentPublishingService : IContentPublishingService
{
    private readonly ContentManagementDbContext _context;
    private readonly IContentAuditService _auditService;
    private readonly ILogger<ContentPublishingService> _logger;

    public ContentPublishingService(
        ContentManagementDbContext context,
        IContentAuditService auditService,
        ILogger<ContentPublishingService> logger)
    {
        _context = context;
        _auditService = auditService;
        _logger = logger;
    }

    public async Task ProcessScheduledPublishingAsync(CancellationToken cancellationToken = default)
    {
        var now = DateTime.UtcNow;
        var contentToPublish = await _context.Contents
            .Where(c => c.Status == ContentStatus.Approved &&
                       c.PublishSchedule != null &&
                       c.PublishSchedule.PublishAt <= now)
            .ToListAsync(cancellationToken);

        foreach (var content in contentToPublish)
        {
            try
            {
                content.Publish(Guid.Empty, "System"); // System user for scheduled publishing
                await _context.SaveChangesAsync(cancellationToken);

                await _auditService.LogContentStatusChangedAsync(
                    content,
                    ContentStatus.Approved,
                    ContentStatus.Published,
                    Guid.Empty,
                    "System",
                    "System",
                    "Scheduled publishing",
                    cancellationToken: cancellationToken);

                _logger.LogInformation("Published content {ContentId} via scheduled publishing", content.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to publish content {ContentId} via scheduled publishing", content.Id);
            }
        }
    }

    public async Task ProcessScheduledUnpublishingAsync(CancellationToken cancellationToken = default)
    {
        var now = DateTime.UtcNow;
        var contentToUnpublish = await _context.Contents
            .Where(c => c.Status == ContentStatus.Published &&
                       c.PublishSchedule != null &&
                       c.PublishSchedule.UnpublishAt <= now)
            .ToListAsync(cancellationToken);

        foreach (var content in contentToUnpublish)
        {
            try
            {
                content.Unpublish(Guid.Empty, "System"); // System user for scheduled unpublishing
                await _context.SaveChangesAsync(cancellationToken);

                await _auditService.LogContentStatusChangedAsync(
                    content,
                    ContentStatus.Published,
                    ContentStatus.Approved,
                    Guid.Empty,
                    "System",
                    "System",
                    "Scheduled unpublishing",
                    cancellationToken: cancellationToken);

                _logger.LogInformation("Unpublished content {ContentId} via scheduled unpublishing", content.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to unpublish content {ContentId} via scheduled unpublishing", content.Id);
            }
        }
    }

    public async Task<List<Content>> GetContentScheduledForPublishingAsync(DateTime? beforeDate = null, CancellationToken cancellationToken = default)
    {
        var cutoffDate = beforeDate ?? DateTime.UtcNow;

        return await _context.Contents
            .Where(c => c.Status == ContentStatus.Approved &&
                       c.PublishSchedule != null &&
                       c.PublishSchedule.PublishAt <= cutoffDate)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<Content>> GetContentScheduledForUnpublishingAsync(DateTime? beforeDate = null, CancellationToken cancellationToken = default)
    {
        var cutoffDate = beforeDate ?? DateTime.UtcNow;

        return await _context.Contents
            .Where(c => c.Status == ContentStatus.Published &&
                       c.PublishSchedule != null &&
                       c.PublishSchedule.UnpublishAt <= cutoffDate)
            .ToListAsync(cancellationToken);
    }

    public async Task<bool> ValidatePublishScheduleAsync(DateTime? publishAt, DateTime? unpublishAt, CancellationToken cancellationToken = default)
    {
        await Task.CompletedTask; // Placeholder for async operation

        if (publishAt.HasValue && publishAt.Value <= DateTime.UtcNow)
        {
            return false; // Cannot schedule publishing in the past
        }

        if (unpublishAt.HasValue && publishAt.HasValue && unpublishAt.Value <= publishAt.Value)
        {
            return false; // Unpublish date must be after publish date
        }

        return true;
    }
}

/// <summary>
/// Content notification service implementation
/// </summary>
public class ContentNotificationService : IContentNotificationService
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ILogger<ContentNotificationService> _logger;

    public ContentNotificationService(
        IHttpClientFactory httpClientFactory,
        ILogger<ContentNotificationService> logger)
    {
        _httpClientFactory = httpClientFactory;
        _logger = logger;
    }

    public async Task SendContentApprovalNotificationAsync(
        Content content,
        Guid approvedBy,
        string approvedByName,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var notification = new
            {
                Type = "ContentApproved",
                Recipients = new[] { content.CreatedBy },
                Subject = $"Content '{content.Title}' has been approved",
                Message = $"Your content '{content.Title}' has been approved by {approvedByName} and is ready for publishing.",
                Data = new
                {
                    ContentId = content.Id,
                    ContentTitle = content.Title,
                    ApprovedBy = approvedByName,
                    ApprovedAt = DateTime.UtcNow
                }
            };

            await SendNotificationAsync(notification, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send content approval notification for content {ContentId}", content.Id);
        }
    }

    public async Task SendContentRejectionNotificationAsync(
        Content content,
        Guid rejectedBy,
        string rejectedByName,
        string reason,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var notification = new
            {
                Type = "ContentRejected",
                Recipients = new[] { content.CreatedBy },
                Subject = $"Content '{content.Title}' has been rejected",
                Message = $"Your content '{content.Title}' has been rejected by {rejectedByName}. Reason: {reason}",
                Data = new
                {
                    ContentId = content.Id,
                    ContentTitle = content.Title,
                    RejectedBy = rejectedByName,
                    Reason = reason,
                    RejectedAt = DateTime.UtcNow
                }
            };

            await SendNotificationAsync(notification, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send content rejection notification for content {ContentId}", content.Id);
        }
    }

    public async Task SendContentPublishedNotificationAsync(
        Content content,
        Guid publishedBy,
        string publishedByName,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var notification = new
            {
                Type = "ContentPublished",
                Recipients = new[] { content.CreatedBy },
                Subject = $"Content '{content.Title}' has been published",
                Message = $"Your content '{content.Title}' has been published by {publishedByName} and is now live.",
                Data = new
                {
                    ContentId = content.Id,
                    ContentTitle = content.Title,
                    ContentSlug = content.Slug,
                    PublishedBy = publishedByName,
                    PublishedAt = DateTime.UtcNow
                }
            };

            await SendNotificationAsync(notification, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send content published notification for content {ContentId}", content.Id);
        }
    }

    public async Task SendPolicyAcceptanceReminderAsync(
        PolicyDocument policy,
        Guid userId,
        string userName,
        string userEmail,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var notification = new
            {
                Type = "PolicyAcceptanceReminder",
                Recipients = new[] { userId },
                Subject = $"Action Required: Accept {policy.PolicyType} Policy",
                Message = $"Please review and accept the updated {policy.PolicyType} policy (v{policy.Version}). This policy is effective from {policy.EffectiveDate:yyyy-MM-dd}.",
                Data = new
                {
                    PolicyId = policy.Id,
                    PolicyType = policy.PolicyType.ToString(),
                    PolicyVersion = policy.Version,
                    EffectiveDate = policy.EffectiveDate,
                    RequiresAcceptance = policy.RequiresAcceptance
                }
            };

            await SendNotificationAsync(notification, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send policy acceptance reminder for policy {PolicyId} to user {UserId}", policy.Id, userId);
        }
    }

    public async Task SendPolicyUpdateNotificationAsync(
        PolicyDocument policy,
        List<Guid> affectedUserIds,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var notification = new
            {
                Type = "PolicyUpdated",
                Recipients = affectedUserIds.ToArray(),
                Subject = $"{policy.PolicyType} Policy Updated",
                Message = $"The {policy.PolicyType} policy has been updated to version {policy.Version}. Please review the changes.",
                Data = new
                {
                    PolicyId = policy.Id,
                    PolicyType = policy.PolicyType.ToString(),
                    PolicyVersion = policy.Version,
                    EffectiveDate = policy.EffectiveDate,
                    RequiresAcceptance = policy.RequiresAcceptance
                }
            };

            await SendNotificationAsync(notification, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send policy update notification for policy {PolicyId}", policy.Id);
        }
    }

    private async Task SendNotificationAsync(object notification, CancellationToken cancellationToken)
    {
        var client = _httpClientFactory.CreateClient("CommunicationNotificationService");

        var json = System.Text.Json.JsonSerializer.Serialize(notification);
        var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

        var response = await client.PostAsync("/api/notifications", content, cancellationToken);

        if (!response.IsSuccessStatusCode)
        {
            _logger.LogWarning("Failed to send notification. Status: {StatusCode}, Response: {Response}",
                response.StatusCode, await response.Content.ReadAsStringAsync(cancellationToken));
        }
    }
}
