using AutoMapper;
using MediatR;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Repositories;

namespace NetworkFleetManagement.Application.Queries.Networks;

public class GetNetworkByIdQueryHandler : IRequestHandler<GetNetworkByIdQuery, BrokerCarrierNetworkDto?>
{
    private readonly IBrokerCarrierNetworkRepository _networkRepository;
    private readonly IMapper _mapper;

    public GetNetworkByIdQueryHandler(IBrokerCarrierNetworkRepository networkRepository, IMapper mapper)
    {
        _networkRepository = networkRepository;
        _mapper = mapper;
    }

    public async Task<BrokerCarrierNetworkDto?> Handle(GetNetworkByIdQuery request, CancellationToken cancellationToken)
    {
        var network = await _networkRepository.GetByIdAsync(request.NetworkId, cancellationToken);
        return network != null ? _mapper.Map<BrokerCarrierNetworkDto>(network) : null;
    }
}
