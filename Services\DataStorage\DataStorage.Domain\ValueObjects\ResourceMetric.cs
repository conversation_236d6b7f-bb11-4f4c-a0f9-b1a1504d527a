using Shared.Domain.ValueObjects;

namespace DataStorage.Domain.ValueObjects;

/// <summary>
/// Resource metric information
/// </summary>
public class ResourceMetric : ValueObject
{
    public string MetricName { get; init; }
    public string ResourceType { get; init; }
    public double Value { get; init; }
    public string Unit { get; init; }
    public DateTime Timestamp { get; init; }
    public Dictionary<string, string> Tags { get; init; }
    public string? Description { get; init; }

    public ResourceMetric(
        string metricName,
        string resourceType,
        double value,
        string unit,
        DateTime timestamp,
        Dictionary<string, string>? tags = null,
        string? description = null)
    {
        MetricName = metricName;
        ResourceType = resourceType;
        Value = value;
        Unit = unit;
        Timestamp = timestamp;
        Tags = tags ?? new Dictionary<string, string>();
        Description = description;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return MetricName;
        yield return ResourceType;
        yield return Value;
        yield return Unit;
        yield return Timestamp;
        yield return Description ?? string.Empty;
        
        foreach (var tag in Tags.OrderBy(x => x.Key))
        {
            yield return tag.Key;
            yield return tag.Value;
        }
    }
}
