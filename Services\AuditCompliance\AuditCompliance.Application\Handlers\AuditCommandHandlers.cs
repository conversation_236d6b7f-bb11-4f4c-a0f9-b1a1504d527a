using MediatR;
using AuditCompliance.Application.Commands;
using AuditCompliance.Application.Interfaces;
using AuditCompliance.Domain.Entities;
using AuditCompliance.Domain.ValueObjects;
using AuditCompliance.Domain.Enums;
using Microsoft.Extensions.Logging;

namespace AuditCompliance.Application.Handlers;

public class CreateAuditLogCommandHandler : IRequestHandler<CreateAuditLogCommand, Guid>
{
    private readonly IAuditLogRepository _auditLogRepository;
    private readonly ILogger<CreateAuditLogCommandHandler> _logger;

    public CreateAuditLogCommandHandler(
        IAuditLogRepository auditLogRepository,
        ILogger<CreateAuditLogCommandHandler> logger)
    {
        _auditLogRepository = auditLogRepository;
        _logger = logger;
    }

    public async Task<Guid> Handle(CreateAuditLogCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var context = AuditContext.Create(
                request.IpAddress,
                request.UserAgent,
                request.SessionId,
                request.CorrelationId);

            var auditLog = new AuditLog(
                request.EventType,
                request.Severity,
                request.EntityType,
                request.Action,
                request.Description,
                request.UserId,
                request.UserName,
                request.UserRole,
                request.EntityId,
                request.OldValues,
                request.NewValues,
                context,
                complianceFlags: request.ComplianceFlags);

            var savedAuditLog = await _auditLogRepository.AddAsync(auditLog, cancellationToken);

            _logger.LogInformation("Audit log created with ID: {AuditLogId} for event: {EventType}", 
                savedAuditLog.Id, request.EventType);

            return savedAuditLog.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating audit log for event: {EventType}", request.EventType);
            throw;
        }
    }
}

public class CleanupExpiredAuditLogsCommandHandler : IRequestHandler<CleanupExpiredAuditLogsCommand, int>
{
    private readonly IAuditLogRepository _auditLogRepository;
    private readonly ILogger<CleanupExpiredAuditLogsCommandHandler> _logger;

    public CleanupExpiredAuditLogsCommandHandler(
        IAuditLogRepository auditLogRepository,
        ILogger<CleanupExpiredAuditLogsCommandHandler> logger)
    {
        _auditLogRepository = auditLogRepository;
        _logger = logger;
    }

    public async Task<int> Handle(CleanupExpiredAuditLogsCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var expiredLogs = await _auditLogRepository.GetExpiredAuditLogsAsync(cancellationToken);
            
            if (expiredLogs.Any())
            {
                await _auditLogRepository.DeleteRangeAsync(expiredLogs, cancellationToken);
                _logger.LogInformation("Cleaned up {Count} expired audit logs", expiredLogs.Count);
            }

            return expiredLogs.Count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up expired audit logs");
            throw;
        }
    }
}

public class CreateComplianceReportCommandHandler : IRequestHandler<CreateComplianceReportCommand, Guid>
{
    private readonly IComplianceReportRepository _complianceReportRepository;
    private readonly ILogger<CreateComplianceReportCommandHandler> _logger;

    public CreateComplianceReportCommandHandler(
        IComplianceReportRepository complianceReportRepository,
        ILogger<CreateComplianceReportCommandHandler> logger)
    {
        _complianceReportRepository = complianceReportRepository;
        _logger = logger;
    }

    public async Task<Guid> Handle(CreateComplianceReportCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var report = new ComplianceReport(
                request.Title,
                request.Description,
                request.Standard,
                request.ReportPeriodStart,
                request.ReportPeriodEnd,
                request.GeneratedBy,
                request.GeneratedByName,
                request.IsAutomated);

            var savedReport = await _complianceReportRepository.AddAsync(report, cancellationToken);

            _logger.LogInformation("Compliance report created with ID: {ReportId} for standard: {Standard}", 
                savedReport.Id, request.Standard);

            return savedReport.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating compliance report for standard: {Standard}", request.Standard);
            throw;
        }
    }
}

public class StartComplianceReportProcessingCommandHandler : IRequestHandler<StartComplianceReportProcessingCommand, bool>
{
    private readonly IComplianceReportRepository _complianceReportRepository;
    private readonly ILogger<StartComplianceReportProcessingCommandHandler> _logger;

    public StartComplianceReportProcessingCommandHandler(
        IComplianceReportRepository complianceReportRepository,
        ILogger<StartComplianceReportProcessingCommandHandler> logger)
    {
        _complianceReportRepository = complianceReportRepository;
        _logger = logger;
    }

    public async Task<bool> Handle(StartComplianceReportProcessingCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var report = await _complianceReportRepository.GetByIdAsync(request.ReportId, cancellationToken);
            if (report == null)
            {
                _logger.LogWarning("Compliance report not found: {ReportId}", request.ReportId);
                return false;
            }

            report.SetInProgress();
            await _complianceReportRepository.UpdateAsync(report, cancellationToken);

            _logger.LogInformation("Started processing compliance report: {ReportId}", request.ReportId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting compliance report processing: {ReportId}", request.ReportId);
            throw;
        }
    }
}

public class CompleteComplianceReportCommandHandler : IRequestHandler<CompleteComplianceReportCommand, bool>
{
    private readonly IComplianceReportRepository _complianceReportRepository;
    private readonly ILogger<CompleteComplianceReportCommandHandler> _logger;

    public CompleteComplianceReportCommandHandler(
        IComplianceReportRepository complianceReportRepository,
        ILogger<CompleteComplianceReportCommandHandler> logger)
    {
        _complianceReportRepository = complianceReportRepository;
        _logger = logger;
    }

    public async Task<bool> Handle(CompleteComplianceReportCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var report = await _complianceReportRepository.GetByIdAsync(request.ReportId, cancellationToken);
            if (report == null)
            {
                _logger.LogWarning("Compliance report not found: {ReportId}", request.ReportId);
                return false;
            }

            report.Complete(request.Findings, request.Recommendations, request.FilePath, request.FileHash);
            await _complianceReportRepository.UpdateAsync(report, cancellationToken);

            _logger.LogInformation("Completed compliance report: {ReportId}", request.ReportId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error completing compliance report: {ReportId}", request.ReportId);
            throw;
        }
    }
}
