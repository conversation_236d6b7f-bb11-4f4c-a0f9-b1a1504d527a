using System;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using MobileWorkflow.Application.DTOs;
using MobileWorkflow.Domain.Entities;
using MobileWorkflow.Domain.Repositories;
using Shared.Domain.Common;

namespace MobileWorkflow.Application.Commands.Workflow;

public class CreateWorkflowCommandHandler : IRequestHandler<CreateWorkflowCommand, WorkflowDto>
{
    private readonly IWorkflowRepository _workflowRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<CreateWorkflowCommandHandler> _logger;

    public CreateWorkflowCommandHandler(
        IWorkflowRepository workflowRepository,
        IMapper mapper,
        ILogger<CreateWorkflowCommandHandler> logger)
    {
        _workflowRepository = workflowRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<WorkflowDto> Handle(CreateWorkflowCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Creating workflow: {Name} in category: {Category}", request.Name, request.Category);

        // Check if workflow name already exists
        var existingWorkflow = await _workflowRepository.GetByNameAsync(request.Name, cancellationToken);
        if (existingWorkflow != null)
        {
            throw new InvalidOperationException($"Workflow with name {request.Name} already exists");
        }

        // Create workflow entity
        var workflow = new Domain.Entities.Workflow(
            request.Name,
            request.Description,
            request.Category,
            request.Version,
            request.Definition,
            request.Configuration,
            request.TriggerType,
            request.TriggerConfiguration,
            request.CreatedBy);

        // Save to repository
        var savedWorkflow = await _workflowRepository.AddAsync(workflow, cancellationToken);

        _logger.LogInformation("Workflow created successfully with ID: {Id}", savedWorkflow.Id);

        return _mapper.Map<WorkflowDto>(savedWorkflow);
    }
}

