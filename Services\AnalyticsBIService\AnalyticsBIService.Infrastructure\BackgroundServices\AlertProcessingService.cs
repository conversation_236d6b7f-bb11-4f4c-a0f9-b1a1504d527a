using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using AnalyticsBIService.Domain.Repositories;
using AnalyticsBIService.Application.Interfaces;
using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Enums;

namespace AnalyticsBIService.Infrastructure.BackgroundServices;

/// <summary>
/// Background service for processing alerts and notifications
/// </summary>
public class AlertProcessingService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<AlertProcessingService> _logger;
    private readonly TimeSpan _processingInterval = TimeSpan.FromMinutes(2);

    public AlertProcessingService(
        IServiceProvider serviceProvider,
        ILogger<AlertProcessingService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Alert Processing Service started");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await ProcessAlertsAsync(stoppingToken);
                await Task.Delay(_processingInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("Alert Processing Service is stopping");
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in Alert Processing Service");
                await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
            }
        }

        _logger.LogInformation("Alert Processing Service stopped");
    }

    private async Task ProcessAlertsAsync(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var alertRepository = scope.ServiceProvider.GetRequiredService<IAlertRepository>();
        var metricRepository = scope.ServiceProvider.GetRequiredService<IMetricRepository>();
        var notificationService = scope.ServiceProvider.GetRequiredService<INotificationService>();

        try
        {
            _logger.LogDebug("Starting alert processing cycle");

            // Process pending alerts
            await ProcessPendingAlertsAsync(alertRepository, notificationService, cancellationToken);

            // Check for threshold breaches
            await CheckThresholdBreachesAsync(metricRepository, alertRepository, notificationService, cancellationToken);

            // Update alert statuses
            await UpdateAlertStatusesAsync(alertRepository, cancellationToken);

            _logger.LogDebug("Alert processing cycle completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during alert processing");
            throw;
        }
    }

    private async Task ProcessPendingAlertsAsync(
        IAlertRepository alertRepository,
        INotificationService notificationService,
        CancellationToken cancellationToken)
    {
        try
        {
            var pendingAlerts = await alertRepository.GetPendingAlertsAsync(50, cancellationToken);

            if (!pendingAlerts.Any())
            {
                _logger.LogDebug("No pending alerts found");
                return;
            }

            _logger.LogInformation("Processing {AlertCount} pending alerts", pendingAlerts.Count());

            foreach (var alert in pendingAlerts)
            {
                try
                {
                    await ProcessSingleAlertAsync(alert, notificationService, cancellationToken);

                    // Update alert status - use domain methods instead of direct property assignment
                    // Note: Alert entity doesn't have a SentAt property or Sent status
                    // We'll acknowledge the alert to mark it as processed
                    alert.Acknowledge(Guid.Empty, "Alert notification sent");
                    await alertRepository.UpdateAsync(alert, cancellationToken);

                    _logger.LogDebug("Processed alert {AlertId}", alert.Id);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing alert {AlertId}", alert.Id);

                    // Mark alert as failed - Alert entity doesn't have direct status assignment
                    // We'll add context to indicate the failure
                    alert.AddContext("NotificationStatus", "Failed");
                    alert.AddContext("NotificationFailedAt", DateTime.UtcNow);
                    await alertRepository.UpdateAsync(alert, cancellationToken);
                }
            }

            _logger.LogInformation("Completed processing {AlertCount} alerts", pendingAlerts.Count());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing pending alerts");
            throw;
        }
    }

    private async Task ProcessSingleAlertAsync(
        Alert alert,
        INotificationService notificationService,
        CancellationToken cancellationToken)
    {
        // Determine recipients based on alert configuration
        var recipients = GetAlertRecipients(alert);

        if (!recipients.Any())
        {
            _logger.LogWarning("No recipients found for alert {AlertId}", alert.Id);
            return;
        }

        // Send notification
        await notificationService.SendAlertNotificationAsync(alert, recipients, cancellationToken);

        _logger.LogDebug("Sent alert notification for {AlertId} to {RecipientCount} recipients",
            alert.Id, recipients.Count);
    }

    private List<string> GetAlertRecipients(Alert alert)
    {
        var recipients = new List<string>();

        // This is a simplified implementation
        // In a real scenario, you would determine recipients based on:
        // - Alert configuration
        // - User roles and permissions
        // - Subscription preferences
        // - Escalation rules

        // For now, return a default recipient list
        recipients.Add("<EMAIL>");

        // Add entity-specific recipients if available
        if (alert.Properties.ContainsKey("recipients"))
        {
            var alertRecipients = alert.Properties["recipients"].ToString();
            if (!string.IsNullOrEmpty(alertRecipients))
            {
                recipients.AddRange(alertRecipients.Split(',').Select(r => r.Trim()));
            }
        }

        return recipients.Distinct().ToList();
    }

    private async Task CheckThresholdBreachesAsync(
        IMetricRepository metricRepository,
        IAlertRepository alertRepository,
        INotificationService notificationService,
        CancellationToken cancellationToken)
    {
        try
        {
            // Get recent metrics for threshold checking
            var endTime = DateTime.UtcNow;
            var startTime = endTime.AddMinutes(-10); // Check last 10 minutes

            var recentMetrics = await metricRepository.GetMetricsByDateRangeAsync(startTime, endTime, cancellationToken);

            // Group metrics by entity and type for threshold checking
            var metricGroups = recentMetrics
                .GroupBy(m => new { m.EntityId, m.EntityType, m.Type })
                .ToList();

            foreach (var group in metricGroups)
            {
                try
                {
                    await CheckMetricGroupThresholds(group, alertRepository, notificationService, cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error checking thresholds for metric group {EntityId}-{MetricType}",
                        group.Key.EntityId, group.Key.Type);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking threshold breaches");
            throw;
        }
    }

    private async Task CheckMetricGroupThresholds(
        IGrouping<dynamic, Metric> metricGroup,
        IAlertRepository alertRepository,
        INotificationService notificationService,
        CancellationToken cancellationToken)
    {
        var latestMetric = metricGroup.OrderByDescending(m => m.Timestamp).First();
        var currentValue = latestMetric.Value.NumericValue;

        // Define threshold rules (in a real implementation, these would come from configuration)
        var thresholdRules = GetThresholdRules(latestMetric.Type, (MetricCategory)latestMetric.Category);

        foreach (var rule in thresholdRules)
        {
            if (IsThresholdBreached(currentValue, rule.ThresholdValue, rule.ComparisonType))
            {
                // Check if we already have a recent alert for this threshold
                var recentAlert = await alertRepository.GetRecentAlertAsync(
                    latestMetric.EntityId ?? Guid.Empty,
                    latestMetric.EntityType ?? "Unknown",
                    rule.RuleName,
                    TimeSpan.FromHours(1),
                    cancellationToken);

                if (recentAlert == null)
                {
                    // Create new threshold breach alert
                    var alert = CreateThresholdAlert(latestMetric, rule, currentValue);
                    await alertRepository.AddAsync(alert, cancellationToken);

                    _logger.LogWarning("Threshold breach detected: {MetricType} = {CurrentValue}, Threshold = {ThresholdValue}",
                        latestMetric.Type, currentValue, rule.ThresholdValue);
                }
            }
        }
    }

    private List<ThresholdRule> GetThresholdRules(MetricType metricType, MetricCategory category)
    {
        // This is a simplified implementation
        // In a real scenario, threshold rules would be stored in database or configuration

        var rules = new List<ThresholdRule>();

        switch (metricType)
        {
            case MetricType.ErrorRate:
                rules.Add(new ThresholdRule
                {
                    RuleName = "HighErrorRate",
                    ThresholdValue = 5,
                    ComparisonType = ThresholdComparisonType.GreaterThan
                });
                break;

            case MetricType.ResponseTime:
                rules.Add(new ThresholdRule
                {
                    RuleName = "SlowResponseTime",
                    ThresholdValue = 5000,
                    ComparisonType = ThresholdComparisonType.GreaterThan
                });
                break;

            case MetricType.Revenue:
                rules.Add(new ThresholdRule
                {
                    RuleName = "LowRevenue",
                    ThresholdValue = 1000,
                    ComparisonType = ThresholdComparisonType.LessThan
                });
                break;
        }

        return rules;
    }

    private bool IsThresholdBreached(decimal currentValue, decimal thresholdValue, ThresholdComparisonType comparisonType)
    {
        return comparisonType switch
        {
            ThresholdComparisonType.GreaterThan => currentValue > thresholdValue,
            ThresholdComparisonType.LessThan => currentValue < thresholdValue,
            ThresholdComparisonType.Equals => currentValue == thresholdValue,
            _ => false
        };
    }

    private Alert CreateThresholdAlert(Metric metric, ThresholdRule rule, decimal currentValue)
    {
        var context = new Dictionary<string, object>
        {
            ["MetricType"] = metric.Type.ToString(),
            ["CurrentValue"] = currentValue,
            ["ThresholdValue"] = rule.ThresholdValue,
            ["RuleName"] = rule.RuleName
        };

        return new Alert(
            name: $"Threshold Breach: {rule.RuleName}",
            description: $"Metric '{metric.Type}' has breached threshold. Current: {currentValue}, Threshold: {rule.ThresholdValue}",
            severity: AlertSeverity.Warning,
            message: $"Threshold breach detected for metric {metric.Type}",
            metricId: metric.Id,
            metricName: metric.Name,
            triggerValue: currentValue,
            thresholdValue: rule.ThresholdValue,
            context: context,
            entityId: metric.EntityId,
            entityType: metric.EntityType);
    }

    private async Task UpdateAlertStatusesAsync(
        IAlertRepository alertRepository,
        CancellationToken cancellationToken)
    {
        try
        {
            // Auto-resolve old alerts that are no longer relevant
            var cutoffTime = DateTime.UtcNow.AddHours(-24);
            var oldPendingAlerts = await alertRepository.GetOldPendingAlertsAsync(cutoffTime, cancellationToken);

            foreach (var alert in oldPendingAlerts)
            {
                // Alert entity doesn't have direct Status assignment - resolve the alert instead
                alert.Resolve(Guid.Empty, "Auto-resolved due to age");
                await alertRepository.UpdateAsync(alert, cancellationToken);
            }

            if (oldPendingAlerts.Any())
            {
                _logger.LogInformation("Expired {AlertCount} old pending alerts", oldPendingAlerts.Count());
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating alert statuses");
            throw;
        }
    }
}

/// <summary>
/// Threshold rule configuration
/// </summary>
public class ThresholdRule
{
    public string RuleName { get; set; } = string.Empty;
    public decimal ThresholdValue { get; set; }
    public ThresholdComparisonType ComparisonType { get; set; }
}

/// <summary>
/// Threshold comparison types
/// </summary>
public enum ThresholdComparisonType
{
    GreaterThan,
    LessThan,
    Equals
}
