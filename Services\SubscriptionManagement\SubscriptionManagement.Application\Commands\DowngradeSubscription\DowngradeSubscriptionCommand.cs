using MediatR;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Application.Commands.DowngradeSubscription
{
    public class DowngradeSubscriptionCommand : IRequest<bool>
    {
        public Guid SubscriptionId { get; set; }
        public Guid UserId { get; set; }
        public Guid NewPlanId { get; set; }
        public ProrationMode ProrationMode { get; set; } = ProrationMode.CreateProrations;
        public bool ImmediateDowngrade { get; set; } = false; // Usually applied at next billing cycle
        public string? DowngradeReason { get; set; }
        public bool ProcessRefund { get; set; } = true;
    }
}
