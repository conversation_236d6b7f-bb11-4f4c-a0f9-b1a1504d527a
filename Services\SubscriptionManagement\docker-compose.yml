version: '3.8'

services:
  subscription-management:
    build:
      context: ../../
      dockerfile: Services/SubscriptionManagement/Dockerfile
    ports:
      - "5003:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Host=postgres;Port=5432;Database=TLI_SubscriptionManagement;User Id=timescale;Password=timescale
      - JwtSettings__Secret=your-super-secret-key-that-is-at-least-32-characters-long
      - JwtSettings__Issuer=TLI-Identity-Service
      - JwtSettings__Audience=TLI-Services
      - RabbitMQ__Host=rabbitmq
      - RazorPay__KeyId=your_razorpay_key_id
      - RazorPay__KeySecret=your_razorpay_key_secret
    depends_on:
      - postgres
      - rabbitmq
    networks:
      - tli-network

  postgres:
    image: timescale/timescaledb:latest-pg15
    environment:
      - POSTGRES_DB=TLI_SubscriptionManagement
      - POSTGRES_USER=timescale
      - POSTGRES_PASSWORD=timescale
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database-setup.sql:/docker-entrypoint-initdb.d/01-setup.sql
    networks:
      - tli-network

  rabbitmq:
    image: rabbitmq:3-management
    environment:
      - RABBITMQ_DEFAULT_USER=guest
      - RABBITMQ_DEFAULT_PASS=guest
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - tli-network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - tli-network

volumes:
  postgres_data:
  rabbitmq_data:
  redis_data:

networks:
  tli-network:
    driver: bridge
