using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace AnalyticsBIService.Infrastructure.Persistence.Configurations;

/// <summary>
/// Entity Framework configuration for Dashboard entity
/// </summary>
public class DashboardConfiguration : IEntityTypeConfiguration<Dashboard>
{
    public void Configure(EntityTypeBuilder<Dashboard> builder)
    {
        // Table configuration
        builder.ToTable("dashboards");

        // Primary key
        builder.HasKey(d => d.Id);
        builder.Property(d => d.Id)
            .HasColumnName("id")
            .ValueGeneratedOnAdd();

        // Basic properties
        builder.Property(d => d.Name)
            .HasColumnName("name")
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(d => d.Description)
            .HasColumnName("description")
            .HasMaxLength(1000);

        builder.Property(d => d.Type)
            .HasColumnName("type")
            .HasConversion<string>()
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(d => d.UserId)
            .HasColumnName("user_id")
            .IsRequired();

        builder.Property(d => d.IsPublic)
            .HasColumnName("is_public")
            .HasDefaultValue(false);

        builder.Property(d => d.IsShared)
            .HasColumnName("is_shared")
            .HasDefaultValue(false);

        builder.Property(d => d.Layout)
            .HasColumnName("layout")
            .HasColumnType("jsonb");

        builder.Property(d => d.Configuration)
            .HasColumnName("configuration")
            .HasColumnType("jsonb");

        builder.Property(d => d.RefreshInterval)
            .HasColumnName("refresh_interval");

        builder.Property(d => d.LastRefreshed)
            .HasColumnName("last_refreshed");

        builder.Property(d => d.IsActive)
            .HasColumnName("is_active")
            .HasDefaultValue(true);

        builder.Property(d => d.Tags)
            .HasColumnName("tags")
            .HasConversion(
                v => string.Join(',', v),
                v => v.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList())
            .HasMaxLength(500);

        // Audit fields from BaseEntity
        builder.Property(d => d.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(d => d.UpdatedAt)
            .HasColumnName("updated_at");

        // Relationships
        builder.HasMany(d => d.Widgets)
            .WithOne(w => w.Dashboard)
            .HasForeignKey(w => w.DashboardId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(d => new { d.UserId, d.Type })
            .HasDatabaseName("IX_Dashboards_UserId_Type");

        builder.HasIndex(d => new { d.UserId, d.Name })
            .HasDatabaseName("IX_Dashboards_UserId_Name")
            .IsUnique();

        builder.HasIndex(d => d.IsPublic)
            .HasDatabaseName("IX_Dashboards_IsPublic");

        builder.HasIndex(d => d.IsShared)
            .HasDatabaseName("IX_Dashboards_IsShared");

        builder.HasIndex(d => d.CreatedAt)
            .HasDatabaseName("IX_Dashboards_CreatedAt");

        builder.HasIndex(d => d.IsActive)
            .HasDatabaseName("IX_Dashboards_IsActive");

        // Constraints
        builder.HasCheckConstraint("CK_Dashboards_Name_NotEmpty", "LENGTH(name) > 0");
        builder.HasCheckConstraint("CK_Dashboards_RefreshInterval_Positive", "refresh_interval IS NULL OR refresh_interval > 0");
    }
}
