using MediatR;
using AutoMapper;
using Microsoft.Extensions.Logging;
using DataStorage.Application.DTOs;
using DataStorage.Application.Commands;
using DataStorage.Domain.Interfaces;
using DataStorage.Domain.Enums;
using DataStorage.Domain.Exceptions;

namespace DataStorage.Application.Queries.Handlers;

public class GetCarrierDocumentsQueryHandler : IRequestHandler<GetCarrierDocumentsQuery, IEnumerable<DocumentDto>>
{
    private readonly IDocumentRepository _documentRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetCarrierDocumentsQueryHandler> _logger;

    public GetCarrierDocumentsQueryHandler(
        IDocumentRepository documentRepository,
        IMapper mapper,
        ILogger<GetCarrierDocumentsQueryHandler> logger)
    {
        _documentRepository = documentRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<IEnumerable<DocumentDto>> Handle(GetCarrierDocumentsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Getting documents for carrier {CarrierId}", request.CarrierId);

            // Check if requesting user has access (owner or admin)
            if (request.RequestingUserId != request.CarrierId)
            {
                _logger.LogWarning("User {RequestingUserId} requesting documents for different carrier {CarrierId}", 
                    request.RequestingUserId, request.CarrierId);
            }

            var documents = await _documentRepository.SearchAsync(
                searchTerm: null,
                documentType: request.DocumentType,
                category: null,
                status: null,
                ownerId: request.CarrierId,
                ownerType: "Carrier",
                createdAfter: request.FromDate,
                createdBefore: request.ToDate,
                skip: 0,
                take: 1000,
                cancellationToken: cancellationToken);

            var documentDtos = _mapper.Map<IEnumerable<DocumentDto>>(documents);

            // Set access URLs for each document
            foreach (var dto in documentDtos)
            {
                dto.AccessUrl = dto.StorageLocation.AccessUrl;
            }

            _logger.LogDebug("Retrieved {Count} documents for carrier {CarrierId}", 
                documentDtos.Count(), request.CarrierId);

            return documentDtos;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get documents for carrier {CarrierId}", request.CarrierId);
            throw;
        }
    }
}

public class GetCarrierTripDocumentsQueryHandler : IRequestHandler<GetCarrierTripDocumentsQuery, IEnumerable<DocumentDto>>
{
    private readonly IDocumentRepository _documentRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetCarrierTripDocumentsQueryHandler> _logger;

    public GetCarrierTripDocumentsQueryHandler(
        IDocumentRepository documentRepository,
        IMapper mapper,
        ILogger<GetCarrierTripDocumentsQueryHandler> logger)
    {
        _documentRepository = documentRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<IEnumerable<DocumentDto>> Handle(GetCarrierTripDocumentsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Getting trip documents for carrier {CarrierId}, trip {TripId}", 
                request.CarrierId, request.TripId);

            var documents = await _documentRepository.SearchAsync(
                searchTerm: null,
                documentType: request.DocumentType,
                category: null,
                status: null,
                ownerId: request.CarrierId,
                ownerType: "Carrier",
                createdAfter: null,
                createdBefore: null,
                skip: 0,
                take: 1000,
                cancellationToken: cancellationToken);

            var documentDtos = _mapper.Map<IEnumerable<DocumentDto>>(documents);

            // Filter by trip ID
            documentDtos = documentDtos.Where(d => 
                d.Tags.ContainsKey("trip_id") && 
                d.Tags["trip_id"] == request.TripId.ToString());

            // Set access URLs
            foreach (var dto in documentDtos)
            {
                dto.AccessUrl = dto.StorageLocation.AccessUrl;
            }

            return documentDtos.OrderBy(d => d.CreatedAt);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get trip documents for carrier {CarrierId}, trip {TripId}", 
                request.CarrierId, request.TripId);
            throw;
        }
    }
}

public class GetCarrierDeliveryPhotosQueryHandler : IRequestHandler<GetCarrierDeliveryPhotosQuery, IEnumerable<DocumentDto>>
{
    private readonly IDocumentRepository _documentRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetCarrierDeliveryPhotosQueryHandler> _logger;

    public GetCarrierDeliveryPhotosQueryHandler(
        IDocumentRepository documentRepository,
        IMapper mapper,
        ILogger<GetCarrierDeliveryPhotosQueryHandler> logger)
    {
        _documentRepository = documentRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<IEnumerable<DocumentDto>> Handle(GetCarrierDeliveryPhotosQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Getting delivery photos for carrier {CarrierId}", request.CarrierId);

            var documents = await _documentRepository.SearchAsync(
                searchTerm: null,
                documentType: DocumentType.DeliveryPhoto,
                category: DocumentCategory.Operational,
                status: null,
                ownerId: request.CarrierId,
                ownerType: "Carrier",
                createdAfter: request.FromDate,
                createdBefore: request.ToDate,
                skip: 0,
                take: 1000,
                cancellationToken: cancellationToken);

            var documentDtos = _mapper.Map<IEnumerable<DocumentDto>>(documents);

            // Filter by trip ID if specified
            if (request.TripId.HasValue)
            {
                documentDtos = documentDtos.Where(d => 
                    d.Tags.ContainsKey("trip_id") && 
                    d.Tags["trip_id"] == request.TripId.Value.ToString());
            }

            // Set access URLs and thumbnail URLs
            foreach (var dto in documentDtos)
            {
                dto.AccessUrl = dto.StorageLocation.AccessUrl;
                // Thumbnail URL would be set if available
            }

            return documentDtos.OrderByDescending(d => d.CreatedAt);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get delivery photos for carrier {CarrierId}", request.CarrierId);
            throw;
        }
    }
}

public class GetCarrierPODHistoryQueryHandler : IRequestHandler<GetCarrierPODHistoryQuery, IEnumerable<DocumentDto>>
{
    private readonly IDocumentRepository _documentRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetCarrierPODHistoryQueryHandler> _logger;

    public GetCarrierPODHistoryQueryHandler(
        IDocumentRepository documentRepository,
        IMapper mapper,
        ILogger<GetCarrierPODHistoryQueryHandler> logger)
    {
        _documentRepository = documentRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<IEnumerable<DocumentDto>> Handle(GetCarrierPODHistoryQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Getting POD history for carrier {CarrierId}", request.CarrierId);

            var documents = await _documentRepository.SearchAsync(
                searchTerm: null,
                documentType: DocumentType.ProofOfDelivery,
                category: DocumentCategory.Operational,
                status: null,
                ownerId: request.CarrierId,
                ownerType: "Carrier",
                createdAfter: request.FromDate,
                createdBefore: request.ToDate,
                skip: 0,
                take: 1000,
                cancellationToken: cancellationToken);

            var documentDtos = _mapper.Map<IEnumerable<DocumentDto>>(documents);

            // Filter by delivery status if specified
            if (request.DeliveryStatus.HasValue)
            {
                documentDtos = documentDtos.Where(d => 
                    d.Tags.ContainsKey("delivery_condition") && 
                    d.Tags["delivery_condition"] == request.DeliveryStatus.Value.ToString());
            }

            // Set access URLs
            foreach (var dto in documentDtos)
            {
                dto.AccessUrl = dto.StorageLocation.AccessUrl;
            }

            return documentDtos.OrderByDescending(d => d.CreatedAt);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get POD history for carrier {CarrierId}", request.CarrierId);
            throw;
        }
    }
}

// Additional query classes that would be referenced above
public class GetCarrierTripDocumentsQuery : IRequest<IEnumerable<DocumentDto>>
{
    public Guid CarrierId { get; set; }
    public Guid TripId { get; set; }
    public DocumentType? DocumentType { get; set; }
    public Guid RequestingUserId { get; set; }
}

public class GetCarrierDeliveryPhotosQuery : IRequest<IEnumerable<DocumentDto>>
{
    public Guid CarrierId { get; set; }
    public Guid? TripId { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public Guid RequestingUserId { get; set; }
}

public class GetCarrierPODHistoryQuery : IRequest<IEnumerable<DocumentDto>>
{
    public Guid CarrierId { get; set; }
    public DeliveryCondition? DeliveryStatus { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public Guid RequestingUserId { get; set; }
}
