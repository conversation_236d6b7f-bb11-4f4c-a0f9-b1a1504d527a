using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using TripManagement.Application.Commands.OptimizeRoute;
using TripManagement.Application.Commands.ProcessGeofenceEvent;
using TripManagement.Application.Commands.UpdateETA;
using TripManagement.Application.DTOs;
using TripManagement.Application.Interfaces;
using TripManagement.Domain.Enums;
using TripManagement.Infrastructure.Persistence;
using TripManagement.Infrastructure.Repositories;
using Xunit;

namespace TripManagement.Tests.Integration;

public class TrackingIntegrationTests : IDisposable
{
    private readonly TripManagementDbContext _context;
    private readonly ITripRepository _tripRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly Mock<IMessageBroker> _messageBrokerMock;
    private readonly Mock<IRouteOptimizationService> _routeOptimizationServiceMock;
    private readonly Mock<IETACalculationService> _etaCalculationServiceMock;
    private readonly Mock<ITrafficService> _trafficServiceMock;
    private readonly Mock<IWeatherService> _weatherServiceMock;
    private readonly Mock<INotificationService> _notificationServiceMock;

    public TrackingIntegrationTests()
    {
        var options = new DbContextOptionsBuilder<TripManagementDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new TripManagementDbContext(options);
        _tripRepository = new TripRepository(_context);
        _unitOfWork = new UnitOfWork(_context);
        _messageBrokerMock = new Mock<IMessageBroker>();
        _routeOptimizationServiceMock = new Mock<IRouteOptimizationService>();
        _etaCalculationServiceMock = new Mock<IETACalculationService>();
        _trafficServiceMock = new Mock<ITrafficService>();
        _weatherServiceMock = new Mock<IWeatherService>();
        _notificationServiceMock = new Mock<INotificationService>();
    }

    [Fact]
    public async Task OptimizeRoute_EndToEnd_Should_Work_Successfully()
    {
        // Arrange
        var trip = await CreateTestTrip();
        
        var optimizedRoute = new RouteDto
        {
            StartLocation = new LocationDto { Latitude = 40.7128, Longitude = -74.0060, Address = "New York, NY" },
            EndLocation = new LocationDto { Latitude = 34.0522, Longitude = -118.2437, Address = "Los Angeles, CA" },
            EstimatedDistanceKm = 3800, // Optimized distance (shorter)
            EstimatedDuration = TimeSpan.FromHours(38)
        };

        var optimizationResult = new RouteOptimizationResult
        {
            OptimizedRoute = optimizedRoute,
            OptimizedWaypoints = new List<LocationDto>(),
            TotalDistanceKm = 3800,
            EstimatedDuration = TimeSpan.FromHours(38),
            EstimatedFuelCost = 450m,
            EstimatedTollCost = 120m,
            OptimizationNotes = "Route optimized to avoid traffic"
        };

        _routeOptimizationServiceMock
            .Setup(r => r.OptimizeRouteAsync(It.IsAny<RouteOptimizationParams>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(optimizationResult);

        var loggerMock = new Mock<ILogger<OptimizeRouteCommandHandler>>();
        var handler = new OptimizeRouteCommandHandler(
            _tripRepository,
            _routeOptimizationServiceMock.Object,
            _unitOfWork,
            loggerMock.Object,
            _messageBrokerMock.Object);

        var command = new OptimizeRouteCommand
        {
            TripId = trip.Id,
            Waypoints = new List<LocationDto>(),
            Criteria = OptimizationCriteria.Distance,
            AvoidTolls = false,
            AvoidHighways = false
        };

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.TotalDistanceKm.Should().Be(3800);
        result.EstimatedDuration.Should().Be(TimeSpan.FromHours(38));

        // Verify route optimization service was called
        _routeOptimizationServiceMock.Verify(r => r.OptimizeRouteAsync(
            It.IsAny<RouteOptimizationParams>(),
            It.IsAny<CancellationToken>()), Times.Once);

        // Verify message broker was called for route optimization
        _messageBrokerMock.Verify(m => m.PublishAsync(
            "trip.route_optimized",
            It.IsAny<object>(),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task ProcessGeofenceEvent_Enter_Should_Work_Successfully()
    {
        // Arrange
        var trip = await CreateTestTrip();
        var driverId = Guid.NewGuid();
        trip.AssignDriverAndVehicle(driverId, Guid.NewGuid());
        _tripRepository.Update(trip);
        await _unitOfWork.SaveChangesAsync();

        var loggerMock = new Mock<ILogger<ProcessGeofenceEventCommandHandler>>();
        var handler = new ProcessGeofenceEventCommandHandler(
            _tripRepository,
            _unitOfWork,
            loggerMock.Object,
            _messageBrokerMock.Object,
            _notificationServiceMock.Object);

        var command = new ProcessGeofenceEventCommand
        {
            TripId = trip.Id,
            DriverId = driverId,
            CurrentLocation = new LocationDto
            {
                Latitude = 40.7128,
                Longitude = -74.0060,
                Address = "New York, NY",
                Timestamp = DateTime.UtcNow
            },
            EventType = GeofenceEventType.Enter,
            GeofenceName = "Pickup Location",
            EventTime = DateTime.UtcNow
        };

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeTrue();

        // Verify message broker was called
        _messageBrokerMock.Verify(m => m.PublishAsync(
            "trip.geofence_event",
            It.IsAny<object>(),
            It.IsAny<CancellationToken>()), Times.Once);

        // Verify notification service was called for arrival
        _notificationServiceMock.Verify(n => n.SendNotificationAsync(
            It.IsAny<Guid>(),
            "Driver Arrived",
            It.IsAny<string>(),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task UpdateETA_EndToEnd_Should_Work_Successfully()
    {
        // Arrange
        var trip = await CreateTestTrip();
        var originalETA = trip.EstimatedEndTime;
        var newETA = originalETA.AddMinutes(30); // 30 minutes delay

        var etaResult = new ETAUpdateResult
        {
            UpdatedETA = newETA,
            PreviousETA = originalETA,
            ETAChange = TimeSpan.FromMinutes(30),
            RemainingDistanceKm = 2000,
            StopETAs = new List<StopETADto>(),
            UpdateReason = "Traffic congestion detected",
            ConfidenceLevel = ETAConfidenceLevel.High
        };

        _etaCalculationServiceMock
            .Setup(e => e.CalculateETAAsync(It.IsAny<ETACalculationParams>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(etaResult);

        _trafficServiceMock
            .Setup(t => t.GetTrafficConditionsAsync(It.IsAny<LocationDto>(), It.IsAny<LocationDto>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new TrafficConditions
            {
                TrafficLevel = TrafficLevel.Heavy,
                DelayFactor = 1.3,
                Incidents = new List<TrafficIncident>()
            });

        _weatherServiceMock
            .Setup(w => w.GetWeatherConditionsAsync(It.IsAny<LocationDto>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new WeatherConditions
            {
                WeatherType = WeatherType.Clear,
                VisibilityKm = 10,
                WindSpeedKmh = 15,
                DelayFactor = 1.0
            });

        var loggerMock = new Mock<ILogger<UpdateETACommandHandler>>();
        var handler = new UpdateETACommandHandler(
            _tripRepository,
            _etaCalculationServiceMock.Object,
            _trafficServiceMock.Object,
            _weatherServiceMock.Object,
            _unitOfWork,
            loggerMock.Object,
            _messageBrokerMock.Object);

        var command = new UpdateETACommand
        {
            TripId = trip.Id,
            CurrentLocation = new LocationDto
            {
                Latitude = 39.0,
                Longitude = -76.0,
                Address = "Somewhere in between",
                Timestamp = DateTime.UtcNow
            },
            CurrentSpeed = 65.0,
            ConsiderTraffic = true,
            ConsiderWeather = true,
            ConsiderDriverBehavior = true
        };

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.UpdatedETA.Should().Be(newETA);
        result.ETAChange.Should().Be(TimeSpan.FromMinutes(30));
        result.UpdateReason.Should().Be("Traffic congestion detected");

        // Verify ETA calculation service was called
        _etaCalculationServiceMock.Verify(e => e.CalculateETAAsync(
            It.IsAny<ETACalculationParams>(),
            It.IsAny<CancellationToken>()), Times.Once);

        // Verify traffic service was called
        _trafficServiceMock.Verify(t => t.GetTrafficConditionsAsync(
            It.IsAny<LocationDto>(),
            It.IsAny<LocationDto>(),
            It.IsAny<CancellationToken>()), Times.Once);

        // Verify weather service was called
        _weatherServiceMock.Verify(w => w.GetWeatherConditionsAsync(
            It.IsAny<LocationDto>(),
            It.IsAny<CancellationToken>()), Times.Once);

        // Verify message broker was called for significant ETA change
        _messageBrokerMock.Verify(m => m.PublishAsync(
            "trip.eta_updated",
            It.IsAny<object>(),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task ProcessGeofenceEvent_Exit_WithIncompleteStop_Should_CreateException()
    {
        // Arrange
        var trip = await CreateTestTripWithStops();
        var driverId = Guid.NewGuid();
        trip.AssignDriverAndVehicle(driverId, Guid.NewGuid());
        _tripRepository.Update(trip);
        await _unitOfWork.SaveChangesAsync();

        var loggerMock = new Mock<ILogger<ProcessGeofenceEventCommandHandler>>();
        var handler = new ProcessGeofenceEventCommandHandler(
            _tripRepository,
            _unitOfWork,
            loggerMock.Object,
            _messageBrokerMock.Object,
            _notificationServiceMock.Object);

        var command = new ProcessGeofenceEventCommand
        {
            TripId = trip.Id,
            DriverId = driverId,
            CurrentLocation = new LocationDto
            {
                Latitude = 40.7128,
                Longitude = -74.0060,
                Address = "New York, NY",
                Timestamp = DateTime.UtcNow
            },
            EventType = GeofenceEventType.Exit,
            GeofenceName = "New York", // Matches the pickup location
            EventTime = DateTime.UtcNow
        };

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeTrue();

        var updatedTrip = await _tripRepository.GetByIdAsync(trip.Id);
        updatedTrip!.Exceptions.Should().HaveCount(1);
        updatedTrip.Exceptions.First().ExceptionType.Should().Be(ExceptionType.RouteDeviation);

        // Verify alert notification was sent
        _notificationServiceMock.Verify(n => n.SendNotificationAsync(
            It.IsAny<Guid>(),
            "Incomplete Stop Alert",
            It.IsAny<string>(),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    private async Task<TripManagement.Domain.Entities.Trip> CreateTestTrip()
    {
        var startLocation = new TripManagement.Domain.ValueObjects.Location(40.7128, -74.0060, DateTime.UtcNow, address: "New York, NY");
        var endLocation = new TripManagement.Domain.ValueObjects.Location(34.0522, -118.2437, DateTime.UtcNow, address: "Los Angeles, CA");
        var route = new TripManagement.Domain.ValueObjects.Route(startLocation, endLocation, estimatedDistanceKm: 3944);

        var trip = new TripManagement.Domain.Entities.Trip(
            Guid.NewGuid(),
            Guid.NewGuid(),
            route,
            DateTime.UtcNow.AddHours(1),
            DateTime.UtcNow.AddHours(41),
            "Test trip for tracking",
            false,
            3944m);

        await _tripRepository.AddAsync(trip);
        await _unitOfWork.SaveChangesAsync();

        return trip;
    }

    private async Task<TripManagement.Domain.Entities.Trip> CreateTestTripWithStops()
    {
        var trip = await CreateTestTrip();

        var startLocation = new TripManagement.Domain.ValueObjects.Location(40.7128, -74.0060, DateTime.UtcNow, address: "New York, NY");
        var endLocation = new TripManagement.Domain.ValueObjects.Location(34.0522, -118.2437, DateTime.UtcNow, address: "Los Angeles, CA");

        var pickupStop = new TripManagement.Domain.Entities.TripStop(
            trip.Id,
            TripStopType.Pickup,
            1,
            startLocation,
            DateTime.UtcNow.AddHours(1));

        var deliveryStop = new TripManagement.Domain.Entities.TripStop(
            trip.Id,
            TripStopType.Delivery,
            2,
            endLocation,
            DateTime.UtcNow.AddHours(40));

        trip.AddStop(pickupStop);
        trip.AddStop(deliveryStop);

        _tripRepository.Update(trip);
        await _unitOfWork.SaveChangesAsync();

        return trip;
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
