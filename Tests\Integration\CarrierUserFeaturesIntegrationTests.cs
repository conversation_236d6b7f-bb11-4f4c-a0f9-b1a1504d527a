using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using System.Net.Http.Json;
using System.Text.Json;
using Xunit;
using FluentAssertions;

namespace Tests.Integration;

/// <summary>
/// Integration tests for carrier user features across multiple services
/// </summary>
public class CarrierUserFeaturesIntegrationTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;
    private readonly JsonSerializerOptions _jsonOptions;

    public CarrierUserFeaturesIntegrationTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory;
        _client = _factory.CreateClient();
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
    }

    [Fact]
    public async Task GetCarrierDashboard_ShouldReturnComprehensiveData()
    {
        // Arrange
        var carrierId = Guid.NewGuid();
        var fromDate = DateTime.UtcNow.AddDays(-30);
        var toDate = DateTime.UtcNow;

        // Act
        var response = await _client.GetAsync($"/api/analytics/carriers/{carrierId}/dashboard?fromDate={fromDate:yyyy-MM-dd}&toDate={toDate:yyyy-MM-dd}");

        // Assert
        response.Should().BeSuccessful();
        var content = await response.Content.ReadAsStringAsync();
        content.Should().NotBeNullOrEmpty();

        var dashboard = JsonSerializer.Deserialize<CarrierDashboardDto>(content, _jsonOptions);
        dashboard.Should().NotBeNull();
        dashboard.CarrierId.Should().Be(carrierId);
        dashboard.PerformanceSummary.Should().NotBeNull();
        dashboard.EarningsSummary.Should().NotBeNull();
        dashboard.TripsSummary.Should().NotBeNull();
    }

    [Fact]
    public async Task GetCarrierVehicles_ShouldReturnVehicleListWithDocumentStatus()
    {
        // Arrange
        var carrierId = Guid.NewGuid();

        // Act
        var response = await _client.GetAsync($"/api/fleet/carriers/{carrierId}/vehicles");

        // Assert
        response.Should().BeSuccessful();
        var content = await response.Content.ReadAsStringAsync();
        var vehicles = JsonSerializer.Deserialize<List<VehicleWithDetailsDto>>(content, _jsonOptions);
        
        vehicles.Should().NotBeNull();
        vehicles.Should().AllSatisfy(v =>
        {
            v.CarrierId.Should().Be(carrierId);
            v.DocumentStatus.Should().NotBeNull();
            v.MaintenanceStatus.Should().NotBeNull();
        });
    }

    [Fact]
    public async Task GetCarrierDrivers_ShouldReturnDriverListWithAssignments()
    {
        // Arrange
        var carrierId = Guid.NewGuid();

        // Act
        var response = await _client.GetAsync($"/api/fleet/carriers/{carrierId}/drivers");

        // Assert
        response.Should().BeSuccessful();
        var content = await response.Content.ReadAsStringAsync();
        var drivers = JsonSerializer.Deserialize<List<DriverWithAssignmentsDto>>(content, _jsonOptions);
        
        drivers.Should().NotBeNull();
        drivers.Should().AllSatisfy(d =>
        {
            d.CarrierId.Should().Be(carrierId);
            d.LicenseStatus.Should().NotBeNull();
            d.CurrentAssignments.Should().NotBeNull();
        });
    }

    [Fact]
    public async Task GetCarrierTrips_ShouldReturnTripHistoryWithMilestones()
    {
        // Arrange
        var carrierId = Guid.NewGuid();
        var fromDate = DateTime.UtcNow.AddDays(-30);
        var toDate = DateTime.UtcNow;

        // Act
        var response = await _client.GetAsync($"/api/trips/carriers/{carrierId}/trips?fromDate={fromDate:yyyy-MM-dd}&toDate={toDate:yyyy-MM-dd}");

        // Assert
        response.Should().BeSuccessful();
        var content = await response.Content.ReadAsStringAsync();
        var trips = JsonSerializer.Deserialize<List<CarrierTripWithMilestonesDto>>(content, _jsonOptions);
        
        trips.Should().NotBeNull();
        trips.Should().AllSatisfy(t =>
        {
            t.CarrierId.Should().Be(carrierId);
            t.Milestones.Should().NotBeNull();
            t.PODStatus.Should().NotBeNull();
        });
    }

    [Fact]
    public async Task GetCarrierPerformanceAnalytics_ShouldReturnComprehensiveMetrics()
    {
        // Arrange
        var carrierId = Guid.NewGuid();
        var fromDate = DateTime.UtcNow.AddDays(-30);
        var toDate = DateTime.UtcNow;

        // Act
        var response = await _client.GetAsync($"/api/analytics/carriers/{carrierId}/performance?fromDate={fromDate:yyyy-MM-dd}&toDate={toDate:yyyy-MM-dd}");

        // Assert
        response.Should().BeSuccessful();
        var content = await response.Content.ReadAsStringAsync();
        var performance = JsonSerializer.Deserialize<CarrierPerformanceAnalyticsDto>(content, _jsonOptions);
        
        performance.Should().NotBeNull();
        performance.CarrierId.Should().Be(carrierId);
        performance.OnTimeDeliveryMetrics.Should().NotBeNull();
        performance.CustomerSatisfactionMetrics.Should().NotBeNull();
        performance.EfficiencyMetrics.Should().NotBeNull();
    }

    [Fact]
    public async Task GetCarrierQuotingHistory_ShouldReturnQuotingAnalytics()
    {
        // Arrange
        var carrierId = Guid.NewGuid();
        var fromDate = DateTime.UtcNow.AddDays(-30);
        var toDate = DateTime.UtcNow;

        // Act
        var response = await _client.GetAsync($"/api/analytics/carriers/{carrierId}/quoting-history?fromDate={fromDate:yyyy-MM-dd}&toDate={toDate:yyyy-MM-dd}");

        // Assert
        response.Should().BeSuccessful();
        var content = await response.Content.ReadAsStringAsync();
        var quotingHistory = JsonSerializer.Deserialize<CarrierQuotingHistoryDto>(content, _jsonOptions);
        
        quotingHistory.Should().NotBeNull();
        quotingHistory.CarrierId.Should().Be(carrierId);
        quotingHistory.RFQResponseHistory.Should().NotBeNull();
        quotingHistory.QuoteSuccessRate.Should().NotBeNull();
        quotingHistory.PricingAnalytics.Should().NotBeNull();
    }

    [Fact]
    public async Task GetCarrierRatings_ShouldReturnRatingAnalytics()
    {
        // Arrange
        var carrierId = Guid.NewGuid();
        var fromDate = DateTime.UtcNow.AddDays(-30);
        var toDate = DateTime.UtcNow;

        // Act
        var response = await _client.GetAsync($"/api/analytics/carriers/{carrierId}/ratings?fromDate={fromDate:yyyy-MM-dd}&toDate={toDate:yyyy-MM-dd}");

        // Assert
        response.Should().BeSuccessful();
        var content = await response.Content.ReadAsStringAsync();
        var ratings = JsonSerializer.Deserialize<CarrierRatingsAnalyticsDto>(content, _jsonOptions);
        
        ratings.Should().NotBeNull();
        ratings.CarrierId.Should().Be(carrierId);
        ratings.RatingSummary.Should().NotBeNull();
        ratings.BrokerRatings.Should().NotBeNull();
        ratings.ShipperRatings.Should().NotBeNull();
        ratings.FeedbackAnalysis.Should().NotBeNull();
    }

    [Fact]
    public async Task SendDocumentExpiryAlert_ShouldCreateAndSendNotification()
    {
        // Arrange
        var request = new
        {
            CarrierId = Guid.NewGuid(),
            DocumentType = "Insurance",
            EntityType = "Vehicle",
            EntityId = Guid.NewGuid(),
            EntityName = "KA01AB1234",
            ExpiryDate = DateTime.UtcNow.AddDays(7),
            DaysUntilExpiry = 7,
            AlertType = "Warning",
            ThresholdDays = 7,
            DocumentDetails = new Dictionary<string, object>
            {
                ["PolicyNumber"] = "POL123456",
                ["InsuranceCompany"] = "ABC Insurance"
            },
            PreferredChannels = new[] { "WhatsApp", "SMS" },
            Priority = "High",
            RequireAcknowledgment = true,
            Tags = new[] { "document-expiry", "insurance", "vehicle" }
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/notifications/send-document-expiry-alert", request);

        // Assert
        response.Should().BeSuccessful();
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<SendDocumentExpiryAlertResult>(content, _jsonOptions);
        
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.AlertId.Should().NotBeEmpty();
        result.RequiresAcknowledgment.Should().BeTrue();
    }

    [Fact]
    public async Task SendPerformanceAlert_ShouldCreateAndSendNotification()
    {
        // Arrange
        var request = new
        {
            CarrierId = Guid.NewGuid(),
            PerformanceMetric = "OnTimeDelivery",
            AlertType = "Decline",
            CurrentValue = 75.5m,
            PreviousValue = 85.0m,
            TargetValue = 90.0m,
            ChangeDirection = "Decrease",
            ChangePercentage = -11.2m,
            TimePeriod = "Monthly",
            MeasurementDate = DateTime.UtcNow,
            PerformanceData = new Dictionary<string, object>
            {
                ["TotalTrips"] = 50,
                ["OnTimeTrips"] = 38,
                ["DelayedTrips"] = 12
            },
            PreferredChannels = new[] { "WhatsApp", "Email" },
            Priority = "High",
            RequireAcknowledgment = false,
            Tags = new[] { "performance-alert", "on-time-delivery", "decline" }
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/notifications/send-performance-alert", request);

        // Assert
        response.Should().BeSuccessful();
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<SendPerformanceAlertResult>(content, _jsonOptions);
        
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.AlertId.Should().NotBeEmpty();
    }

    [Fact]
    public async Task SendRatingChangeAlert_ShouldCreateAndSendNotification()
    {
        // Arrange
        var request = new
        {
            CarrierId = Guid.NewGuid(),
            CurrentRating = 4.2m,
            PreviousRating = 3.8m,
            RatingChange = 0.4m,
            RatingCategory = "Overall",
            ChangeType = "Improvement",
            ReviewCount = 15,
            RatingDate = DateTime.UtcNow,
            RecentFeedback = new[] 
            {
                "Excellent service quality",
                "Very professional driver",
                "On-time delivery as promised"
            },
            RatingDetails = new Dictionary<string, object>
            {
                ["ServiceQuality"] = 4.3m,
                ["Communication"] = 4.1m,
                ["Timeliness"] = 4.2m
            },
            PreferredChannels = new[] { "WhatsApp" },
            Priority = "Normal",
            RequireAcknowledgment = false,
            Tags = new[] { "rating-change", "improvement", "overall" }
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/notifications/send-rating-change-alert", request);

        // Assert
        response.Should().BeSuccessful();
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<SendPerformanceAlertResult>(content, _jsonOptions);
        
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.AlertId.Should().NotBeEmpty();
    }

    [Fact]
    public async Task GetDocumentStatusSummary_ShouldReturnExpiryTracking()
    {
        // Arrange
        var carrierId = Guid.NewGuid();

        // Act
        var response = await _client.GetAsync($"/api/fleet/carriers/{carrierId}/document-status");

        // Assert
        response.Should().BeSuccessful();
        var content = await response.Content.ReadAsStringAsync();
        var documentStatus = JsonSerializer.Deserialize<DocumentStatusSummaryDto>(content, _jsonOptions);
        
        documentStatus.Should().NotBeNull();
        documentStatus.CarrierId.Should().Be(carrierId);
        documentStatus.ExpiringDocuments.Should().NotBeNull();
        documentStatus.DocumentCategories.Should().NotBeNull();
        documentStatus.ExpiryAlerts.Should().NotBeNull();
    }

    [Fact]
    public async Task CarrierWorkflow_EndToEnd_ShouldWorkSeamlessly()
    {
        // Arrange
        var carrierId = Guid.NewGuid();
        var fromDate = DateTime.UtcNow.AddDays(-30);
        var toDate = DateTime.UtcNow;

        // Act & Assert - Test the complete carrier workflow

        // 1. Get carrier dashboard
        var dashboardResponse = await _client.GetAsync($"/api/analytics/carriers/{carrierId}/dashboard?fromDate={fromDate:yyyy-MM-dd}&toDate={toDate:yyyy-MM-dd}");
        dashboardResponse.Should().BeSuccessful();

        // 2. Get vehicle list with document status
        var vehiclesResponse = await _client.GetAsync($"/api/fleet/carriers/{carrierId}/vehicles");
        vehiclesResponse.Should().BeSuccessful();

        // 3. Get driver list with assignments
        var driversResponse = await _client.GetAsync($"/api/fleet/carriers/{carrierId}/drivers");
        driversResponse.Should().BeSuccessful();

        // 4. Get trip history with milestones
        var tripsResponse = await _client.GetAsync($"/api/trips/carriers/{carrierId}/trips?fromDate={fromDate:yyyy-MM-dd}&toDate={toDate:yyyy-MM-dd}");
        tripsResponse.Should().BeSuccessful();

        // 5. Get performance analytics
        var performanceResponse = await _client.GetAsync($"/api/analytics/carriers/{carrierId}/performance?fromDate={fromDate:yyyy-MM-dd}&toDate={toDate:yyyy-MM-dd}");
        performanceResponse.Should().BeSuccessful();

        // 6. Get quoting analytics
        var quotingResponse = await _client.GetAsync($"/api/analytics/carriers/{carrierId}/quoting-history?fromDate={fromDate:yyyy-MM-dd}&toDate={toDate:yyyy-MM-dd}");
        quotingResponse.Should().BeSuccessful();

        // 7. Get rating analytics
        var ratingsResponse = await _client.GetAsync($"/api/analytics/carriers/{carrierId}/ratings?fromDate={fromDate:yyyy-MM-dd}&toDate={toDate:yyyy-MM-dd}");
        ratingsResponse.Should().BeSuccessful();

        // 8. Get document status summary
        var documentStatusResponse = await _client.GetAsync($"/api/fleet/carriers/{carrierId}/document-status");
        documentStatusResponse.Should().BeSuccessful();

        // All responses should be successful, indicating proper integration
        var allResponses = new[] 
        { 
            dashboardResponse, vehiclesResponse, driversResponse, tripsResponse, 
            performanceResponse, quotingResponse, ratingsResponse, documentStatusResponse 
        };
        
        allResponses.Should().AllSatisfy(r => r.IsSuccessStatusCode.Should().BeTrue());
    }
}

// Mock DTOs for testing (in a real implementation, these would be shared)
public class CarrierDashboardDto
{
    public Guid CarrierId { get; set; }
    public object PerformanceSummary { get; set; }
    public object EarningsSummary { get; set; }
    public object TripsSummary { get; set; }
}

public class VehicleWithDetailsDto
{
    public Guid CarrierId { get; set; }
    public object DocumentStatus { get; set; }
    public object MaintenanceStatus { get; set; }
}

public class DriverWithAssignmentsDto
{
    public Guid CarrierId { get; set; }
    public object LicenseStatus { get; set; }
    public object CurrentAssignments { get; set; }
}

public class CarrierTripWithMilestonesDto
{
    public Guid CarrierId { get; set; }
    public object Milestones { get; set; }
    public object PODStatus { get; set; }
}

public class CarrierPerformanceAnalyticsDto
{
    public Guid CarrierId { get; set; }
    public object OnTimeDeliveryMetrics { get; set; }
    public object CustomerSatisfactionMetrics { get; set; }
    public object EfficiencyMetrics { get; set; }
}

public class CarrierQuotingHistoryDto
{
    public Guid CarrierId { get; set; }
    public object RFQResponseHistory { get; set; }
    public object QuoteSuccessRate { get; set; }
    public object PricingAnalytics { get; set; }
}

public class CarrierRatingsAnalyticsDto
{
    public Guid CarrierId { get; set; }
    public object RatingSummary { get; set; }
    public object BrokerRatings { get; set; }
    public object ShipperRatings { get; set; }
    public object FeedbackAnalysis { get; set; }
}

public class DocumentStatusSummaryDto
{
    public Guid CarrierId { get; set; }
    public object ExpiringDocuments { get; set; }
    public object DocumentCategories { get; set; }
    public object ExpiryAlerts { get; set; }
}

public class SendDocumentExpiryAlertResult
{
    public bool IsSuccess { get; set; }
    public Guid AlertId { get; set; }
    public bool RequiresAcknowledgment { get; set; }
}

public class SendPerformanceAlertResult
{
    public bool IsSuccess { get; set; }
    public Guid AlertId { get; set; }
}
