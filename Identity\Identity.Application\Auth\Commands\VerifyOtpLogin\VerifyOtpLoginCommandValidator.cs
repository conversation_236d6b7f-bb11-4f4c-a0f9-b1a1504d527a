using FluentValidation;

namespace Identity.Application.Auth.Commands.VerifyOtpLogin
{
    public class VerifyOtpLoginCommandValidator : AbstractValidator<VerifyOtpLoginCommand>
    {
        public VerifyOtpLoginCommandValidator()
        {
            RuleFor(x => x.Username)
                .NotEmpty()
                .WithMessage("Username is required")
                .MaximumLength(320)
                .WithMessage("Username cannot exceed 320 characters");

            RuleFor(x => x.OtpToken)
                .NotEmpty()
                .WithMessage("OTP token is required")
                .Length(4, 10)
                .WithMessage("OTP token must be between 4 and 10 characters")
                .Matches(@"^\d+$")
                .WithMessage("OTP token must contain only digits");

            RuleFor(x => x.IpAddress)
                .MaximumLength(50)
                .WithMessage("IP address cannot exceed 50 characters");

            RuleFor(x => x.DeviceInfo)
                .MaximumLength(1024)
                .WithMessage("Device info cannot exceed 1024 characters");
        }
    }
}
