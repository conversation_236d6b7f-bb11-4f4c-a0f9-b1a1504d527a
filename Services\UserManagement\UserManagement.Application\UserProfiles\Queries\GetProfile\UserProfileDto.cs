using System;
using UserManagement.Domain.Entities;

namespace UserManagement.Application.UserProfiles.Queries.GetProfile
{
    public class UserProfileDto
    {
        public Guid Id { get; set; }
        public Guid UserId { get; set; }
        public UserType UserType { get; set; }
        public ProfileStatus Status { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string DisplayName { get; set; }
        public string Email { get; set; }
        public string PhoneNumber { get; set; }
        public string CompanyName { get; set; }
        public string GstNumber { get; set; }
        public string PanNumber { get; set; }
        public string AadharNumber { get; set; }
        public string LicenseNumber { get; set; }
        public string Address { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string PostalCode { get; set; }
        public string Country { get; set; }
        public string Region { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public DateTime? LastLoginAt { get; set; }
        public DateTime? ApprovedAt { get; set; }
        public Guid? ApprovedBy { get; set; }
        public string KycStatus { get; set; }
        public DateTime? KycSubmittedAt { get; set; }
        public DateTime? KycApprovedAt { get; set; }
        public bool IsActive { get; set; }
        public bool IsComplete { get; set; }
        
        // Activity Information
        public int RfqCount { get; set; }
        public int OrderCount { get; set; }
        public int TripCount { get; set; }
        public decimal TotalBusinessValue { get; set; }
        
        // Document Summary
        public int TotalDocuments { get; set; }
        public int ApprovedDocuments { get; set; }
        public int PendingDocuments { get; set; }
        public int RejectedDocuments { get; set; }
        public DateTime? LastDocumentUpload { get; set; }
    }
}
