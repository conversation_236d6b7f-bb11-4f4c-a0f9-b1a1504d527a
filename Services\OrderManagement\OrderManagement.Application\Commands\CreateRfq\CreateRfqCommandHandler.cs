using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Entities;
using OrderManagement.Domain.ValueObjects;
using Shared.Domain.Common;

namespace OrderManagement.Application.Commands.CreateRfq;

public class CreateRfqCommandHandler : IRequestHandler<CreateRfqCommand, Guid>
{
    private readonly IRfqRepository _rfqRepository;
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<CreateRfqCommandHandler> _logger;

    public CreateRfqCommandHandler(
        IRfqRepository rfqRepository,
        IMessageBroker messageBroker,
        ILogger<CreateRfqCommandHandler> logger)
    {
        _rfqRepository = rfqRepository;
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task<Guid> Handle(CreateRfqCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Creating RFQ for transport company {TransportCompanyId}", request.TransportCompanyId);

        // Create value objects
        var loadDetails = CreateLoadDetails(request.LoadDetails);
        var routeDetails = CreateRouteDetails(request.RouteDetails);
        var requirements = CreateRfqRequirements(request.Requirements);
        var budgetRange = request.BudgetRange != null
            ? new Money(request.BudgetRange.Amount, request.BudgetRange.Currency)
            : null;
        var timeframe = request.Timeframe != null
            ? new RfqTimeframe(request.Timeframe.Duration, request.Timeframe.Unit,
                request.Timeframe.AllowExtensions, request.Timeframe.MaxExtensions)
            : null;

        // Create RFQ entity
        var rfq = new RFQ(
            request.TransportCompanyId,
            request.Title,
            request.Description,
            loadDetails,
            routeDetails,
            requirements,
            request.ExpiresAt,
            budgetRange,
            request.SpecialInstructions,
            request.IsUrgent,
            request.ContactPerson,
            request.ContactPhone,
            request.ContactEmail,
            timeframe);

        // Save to repository
        await _rfqRepository.AddAsync(rfq, cancellationToken);

        // Publish integration event
        await _messageBroker.PublishAsync("rfq.created", new
        {
            RfqId = rfq.Id,
            RfqNumber = rfq.RfqNumber,
            TransportCompanyId = rfq.TransportCompanyId,
            Title = rfq.Title,
            LoadType = rfq.LoadDetails.LoadType.ToString(),
            PickupCity = rfq.RouteDetails.PickupAddress.City,
            DeliveryCity = rfq.RouteDetails.DeliveryAddress.City,
            PreferredPickupDate = rfq.RouteDetails.PreferredPickupDate,
            PreferredDeliveryDate = rfq.RouteDetails.PreferredDeliveryDate,
            IsUrgent = rfq.IsUrgent,
            CreatedAt = rfq.CreatedAt
        });

        _logger.LogInformation("Successfully created RFQ {RfqId} with number {RfqNumber}",
            rfq.Id, rfq.RfqNumber);

        return rfq.Id;
    }

    private LoadDetails CreateLoadDetails(CreateLoadDetailsDto dto)
    {
        var weight = new Weight(dto.Weight.Value, dto.Weight.Unit);
        var volume = dto.Volume != null ? new Volume(dto.Volume.Value, dto.Volume.Unit) : null;
        var dimensions = dto.Dimensions != null
            ? new Dimensions(dto.Dimensions.Length, dto.Dimensions.Width, dto.Dimensions.Height, dto.Dimensions.Unit)
            : null;
        var temperatureRange = dto.TemperatureRange != null
            ? new TemperatureRange(dto.TemperatureRange.MinTemperature, dto.TemperatureRange.MaxTemperature, dto.TemperatureRange.Unit)
            : null;

        return new LoadDetails(
            dto.Description,
            dto.LoadType,
            weight,
            dto.Quantity,
            dto.RequiredVehicleType,
            volume,
            dimensions,
            dto.PackagingType,
            dto.RequiresSpecialHandling,
            dto.SpecialHandlingInstructions,
            dto.IsHazardous,
            dto.HazardousClassification,
            dto.RequiresTemperatureControl,
            temperatureRange,
            dto.Photos);
    }

    private RouteDetails CreateRouteDetails(CreateRouteDetailsDto dto)
    {
        var pickupAddress = CreateAddress(dto.PickupAddress);
        var deliveryAddress = CreateAddress(dto.DeliveryAddress);
        var pickupTimeWindow = dto.PickupTimeWindow != null
            ? new TimeWindow(dto.PickupTimeWindow.StartTime, dto.PickupTimeWindow.EndTime)
            : null;
        var deliveryTimeWindow = dto.DeliveryTimeWindow != null
            ? new TimeWindow(dto.DeliveryTimeWindow.StartTime, dto.DeliveryTimeWindow.EndTime)
            : null;
        var estimatedDistance = dto.EstimatedDistance != null
            ? new Distance(dto.EstimatedDistance.Value, dto.EstimatedDistance.Unit)
            : null;
        var intermediateStops = dto.IntermediateStops.Select(CreateAddress).ToList();

        return new RouteDetails(
            pickupAddress,
            deliveryAddress,
            dto.PreferredPickupDate,
            dto.PreferredDeliveryDate,
            pickupTimeWindow,
            deliveryTimeWindow,
            estimatedDistance,
            dto.EstimatedDuration,
            intermediateStops,
            dto.RouteNotes,
            dto.IsFlexiblePickup,
            dto.IsFlexibleDelivery);
    }

    private Address CreateAddress(CreateAddressDto dto)
    {
        return new Address(
            dto.Street,
            dto.City,
            dto.State,
            dto.PostalCode,
            dto.Country,
            dto.Latitude,
            dto.Longitude,
            dto.ContactPerson,
            dto.ContactPhone,
            dto.SpecialInstructions);
    }

    private RfqRequirements CreateRfqRequirements(CreateRfqRequirementsDto dto)
    {
        var temperatureRange = dto.TemperatureRange != null
            ? new TemperatureRange(dto.TemperatureRange.MinTemperature, dto.TemperatureRange.MaxTemperature, dto.TemperatureRange.Unit)
            : null;

        return new RfqRequirements(
            dto.RequiredVehicleType,
            dto.MinVehicleCapacity,
            dto.RequiresInsurance,
            dto.MinInsuranceAmount,
            dto.RequiresLicense,
            dto.RequiredLicenses,
            dto.RequiresExperience,
            dto.MinYearsExperience,
            dto.RequiresBackground,
            dto.RequiresTracking,
            dto.RequiresTemperatureControl,
            temperatureRange,
            dto.AdditionalRequirements);
    }
}

