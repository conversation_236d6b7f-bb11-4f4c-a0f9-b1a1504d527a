using System.ComponentModel.DataAnnotations;

namespace AnalyticsBIService.Application.DTOs;

/// <summary>
/// Shipper business reporting data transfer object
/// </summary>
public class ShipperBusinessReportingDto
{
    public string ShipperId { get; set; } = string.Empty;
    public string ShipperName { get; set; } = string.Empty;
    public decimal TotalShippingCost { get; set; }
    public decimal AverageShippingCostPerOrder { get; set; }
    public int TotalOrders { get; set; }
    public int CompletedOrders { get; set; }
    public int PendingOrders { get; set; }
    public int CancelledOrders { get; set; }
    public decimal OrderCompletionRate { get; set; }
    public decimal OnTimeDeliveryRate { get; set; }
    public decimal AverageDeliveryTime { get; set; }
    public decimal CustomerSatisfactionScore { get; set; }
    public DateTime ReportPeriodStart { get; set; }
    public DateTime ReportPeriodEnd { get; set; }
    public List<ShipperCostBreakdownDto> CostBreakdown { get; set; } = new();
    public List<ShipperVolumeAnalysisDto> VolumeAnalysis { get; set; } = new();

    // Additional properties required by GetShipperAnalyticsQueryHandler
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;
    public List<ShipmentReportDto> ShipmentReports { get; set; } = new();
    public ShipmentTrendAnalysisDto TrendAnalysis { get; set; } = new();
}

/// <summary>
/// Shipper cost breakdown data transfer object
/// </summary>
public class ShipperCostBreakdownDto
{
    public string CostCategory { get; set; } = string.Empty; // "Transportation", "Fuel", "Insurance", "Handling"
    public decimal Amount { get; set; }
    public decimal Percentage { get; set; }
    public decimal PerUnitCost { get; set; }
    public string TrendDirection { get; set; } = string.Empty; // "Increasing", "Decreasing", "Stable"
    public decimal TrendPercentage { get; set; }
}

/// <summary>
/// Shipper volume analysis data transfer object
/// </summary>
public class ShipperVolumeAnalysisDto
{
    public string TimePeriod { get; set; } = string.Empty; // "Daily", "Weekly", "Monthly"
    public DateTime Date { get; set; }
    public int OrderCount { get; set; }
    public decimal TotalWeight { get; set; }
    public decimal TotalVolume { get; set; }
    public decimal TotalCost { get; set; }
    public decimal AverageCostPerKg { get; set; }
    public decimal CapacityUtilization { get; set; }
}

/// <summary>
/// Shipper route efficiency data transfer object
/// </summary>
public class ShipperRouteEfficiencyDto
{
    public Guid ShipperId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;
    public List<RouteEfficiencyDto> RouteEfficiencies { get; set; } = new();
    public decimal OverallRouteEfficiency { get; set; }
    public decimal AverageDeliveryDistance { get; set; }
    public decimal AverageDeliveryTime { get; set; }
    public decimal RouteOptimizationSavings { get; set; }
    public int TotalRoutes { get; set; }
    public int OptimizedRoutes { get; set; }
    public List<RouteRecommendationDto> RouteRecommendations { get; set; } = new();
}

// RouteEfficiencyTrendDto is already defined in CarrierSpecializedDTOs.cs - using that instead

// TopRouteDto is already defined in BrokerAnalyticsDto.cs - using that instead

/// <summary>
/// Route efficiency data transfer object
/// </summary>
public class RouteEfficiencyDto
{
    public string RouteId { get; set; } = string.Empty;
    public string OriginCity { get; set; } = string.Empty;
    public string DestinationCity { get; set; } = string.Empty;
    public decimal Distance { get; set; }
    public decimal OptimalDistance { get; set; }
    public decimal ActualTime { get; set; }
    public decimal OptimalTime { get; set; }
    public decimal EfficiencyScore { get; set; }
    public decimal CostPerKm { get; set; }
    public int FrequencyCount { get; set; }
    public decimal PotentialSavings { get; set; }
    public string EfficiencyRating { get; set; } = string.Empty; // "Excellent", "Good", "Average", "Poor"

    // Properties required by GetTransportCompanyAdvancedAnalyticsQueryHandler and GetCarrierAnalyticsQueryHandler
    public decimal OptimalEfficiency { get; set; }
    public decimal EfficiencyGap { get; set; }
    public decimal CostPerKilometer { get; set; }
    public decimal CurrentEfficiency { get; set; }
    public decimal RouteOptimizationScore { get; set; }
    public decimal AverageRouteTime { get; set; }
    public List<RouteEfficiencyTrendDto> RouteEfficiencyTrends { get; set; } = new();
    public List<TopRouteDto> TopRoutes { get; set; } = new();
    public List<RouteOptimizationOpportunityDto> OptimizationOpportunities { get; set; } = new();

    // Additional properties for shipper analytics
    public decimal TimeEfficiency { get; set; }
    public decimal CostEfficiency { get; set; }
    public string OverallRating { get; set; } = string.Empty;
}

/// <summary>
/// Route recommendation data transfer object
/// </summary>
public class RouteRecommendationDto
{
    public string RecommendationType { get; set; } = string.Empty; // "Consolidation", "Alternative Route", "Mode Change"
    public string Description { get; set; } = string.Empty;
    public decimal EstimatedSavings { get; set; }
    public decimal ImplementationEffort { get; set; }
    public string Priority { get; set; } = string.Empty; // "High", "Medium", "Low"
    public List<string> AffectedRoutes { get; set; } = new();
    public Dictionary<string, object> RecommendationDetails { get; set; } = new();

    // Additional properties required by GetShipperAnalyticsQueryHandler
    public string RouteId { get; set; } = string.Empty;
    public decimal PotentialSavings { get; set; }
}

/// <summary>
/// Shipper service quality data transfer object
/// </summary>
public class ShipperServiceQualityDto
{
    public Guid ShipperId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;
    public decimal OverallServiceScore { get; set; }
    public decimal OnTimeDeliveryRate { get; set; }
    public decimal DamageRate { get; set; }
    public decimal LossRate { get; set; }
    public decimal CustomerSatisfactionScore { get; set; }
    public decimal ComplaintResolutionRate { get; set; }
    public decimal AverageResolutionTime { get; set; }
    public int TotalComplaints { get; set; }
    public int ResolvedComplaints { get; set; }
    public int PendingComplaints { get; set; }
    public List<ServiceQualityMetricDto> QualityMetrics { get; set; } = new();
    public List<ServiceIssueDto> CommonIssues { get; set; } = new();
}

/// <summary>
/// Service quality metric data transfer object
/// </summary>
public class ServiceQualityMetricDto
{
    public string MetricName { get; set; } = string.Empty;
    public decimal CurrentValue { get; set; }
    public decimal TargetValue { get; set; }
    public decimal BenchmarkValue { get; set; }
    public string Unit { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty; // "On Target", "Below Target", "Above Target"
    public decimal TrendDirection { get; set; }
    public string PerformanceLevel { get; set; } = string.Empty;
}

/// <summary>
/// Service issue data transfer object
/// </summary>
public class ServiceIssueDto
{
    public string IssueType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int Frequency { get; set; }
    public decimal ImpactScore { get; set; }
    public string Severity { get; set; } = string.Empty; // "Low", "Medium", "High", "Critical"
    public List<string> RootCauses { get; set; } = new();
    public List<string> RecommendedActions { get; set; } = new();
    public decimal ResolutionRate { get; set; }
}

/// <summary>
/// Shipper financial performance data transfer object
/// </summary>
public class ShipperFinancialPerformanceDto
{
    public string ShipperId { get; set; } = string.Empty;
    public decimal TotalShippingSpend { get; set; }
    public decimal BudgetedSpend { get; set; }
    public decimal SpendVariance { get; set; }
    public decimal CostPerShipment { get; set; }
    public decimal CostPerKg { get; set; }
    public decimal CostPerKm { get; set; }
    public decimal YearOverYearGrowth { get; set; }
    public decimal SeasonalVariation { get; set; }
    public List<FinancialTrendDto> SpendTrends { get; set; } = new();
    public List<CostOptimizationDto> CostOptimizations { get; set; } = new();
    public List<BudgetAnalysisDto> BudgetAnalysis { get; set; } = new();
}

// CostOptimizationDto is already defined in AdminAnalyticsDto.cs - using that instead

/// <summary>
/// Budget analysis data transfer object
/// </summary>
public class BudgetAnalysisDto
{
    public string Category { get; set; } = string.Empty;
    public decimal BudgetedAmount { get; set; }
    public decimal ActualAmount { get; set; }
    public decimal Variance { get; set; }
    public decimal VariancePercentage { get; set; }
    public string Status { get; set; } = string.Empty; // "Under Budget", "On Budget", "Over Budget"
    public string Forecast { get; set; } = string.Empty;
    public List<string> VarianceReasons { get; set; } = new();
}

/// <summary>
/// Shipper carrier performance data transfer object
/// </summary>
public class ShipperCarrierPerformanceDto
{
    public string ShipperId { get; set; } = string.Empty;
    public List<CarrierPerformanceSummaryDto> CarrierPerformances { get; set; } = new();
    public List<CarrierComparisonDto> CarrierComparisons { get; set; } = new();
    public List<CarrierRecommendationDto> CarrierRecommendations { get; set; } = new();
    public decimal AverageCarrierRating { get; set; }
    public int TotalActiveCarriers { get; set; }
    public int PreferredCarriers { get; set; }
}

// CarrierPerformanceSummaryDto is already defined in CarrierAnalyticsDto.cs - using that instead

/// <summary>
/// Carrier comparison data transfer object
/// </summary>
public class CarrierComparisonDto
{
    public string MetricName { get; set; } = string.Empty;
    public List<CarrierMetricDto> CarrierMetrics { get; set; } = new();
    public string BestPerformer { get; set; } = string.Empty;
    public string WorstPerformer { get; set; } = string.Empty;
    public decimal AverageValue { get; set; }
    public string Unit { get; set; } = string.Empty;
}

/// <summary>
/// Carrier metric data transfer object
/// </summary>
public class CarrierMetricDto
{
    public string CarrierId { get; set; } = string.Empty;
    public string CarrierName { get; set; } = string.Empty;
    public decimal Value { get; set; }
    public int Rank { get; set; }
    public string PerformanceLevel { get; set; } = string.Empty;
}

/// <summary>
/// Carrier recommendation data transfer object
/// </summary>
public class CarrierRecommendationDto
{
    public string RecommendationType { get; set; } = string.Empty; // "New Carrier", "Increase Usage", "Decrease Usage", "Remove"
    public string CarrierId { get; set; } = string.Empty;
    public string CarrierName { get; set; } = string.Empty;
    public string Reasoning { get; set; } = string.Empty;
    public decimal ExpectedImpact { get; set; }
    public string Priority { get; set; } = string.Empty;
    public List<string> SupportingData { get; set; } = new();

    // Additional properties for analytics
    public string Description { get; set; } = string.Empty;
    public decimal PotentialSavings { get; set; }

    // Additional properties required by GetCarrierPerformanceSummaryQueryHandler
    public string Title { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string EstimatedImpact { get; set; } = string.Empty;
    public List<string> ActionItems { get; set; } = new();
}

/// <summary>
/// Consolidation opportunity data transfer object
/// </summary>
public class ConsolidationOpportunityDto
{
    public string OpportunityType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal PotentialSavings { get; set; }
    public int ShipmentCount { get; set; }
    public decimal ConsolidationRate { get; set; }
}

/// <summary>
/// Shipper sustainability metrics data transfer object
/// </summary>
public class ShipperSustainabilityDto
{
    public string ShipperId { get; set; } = string.Empty;
    public decimal CarbonFootprint { get; set; }
    public decimal CarbonIntensity { get; set; }
    public decimal FuelConsumption { get; set; }
    public decimal EmissionReduction { get; set; }
    public decimal SustainabilityScore { get; set; }
    public List<SustainabilityMetricDto> SustainabilityMetrics { get; set; } = new();
    public List<SustainabilityInitiativeDto> GreenInitiatives { get; set; } = new();
    public List<SustainabilityTargetDto> SustainabilityTargets { get; set; } = new();
}

/// <summary>
/// Sustainability metric data transfer object
/// </summary>
public class SustainabilityMetricDto
{
    public string MetricName { get; set; } = string.Empty;
    public decimal CurrentValue { get; set; }
    public decimal TargetValue { get; set; }
    public decimal BaselineValue { get; set; }
    public string Unit { get; set; } = string.Empty;
    public decimal ImprovementPercentage { get; set; }
    public string Status { get; set; } = string.Empty;
}

/// <summary>
/// Sustainability initiative data transfer object
/// </summary>
public class SustainabilityInitiativeDto
{
    public string InitiativeName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal ExpectedImpact { get; set; }
    public decimal ActualImpact { get; set; }
    public string Status { get; set; } = string.Empty; // "Planned", "In Progress", "Completed", "On Hold"
    public DateTime StartDate { get; set; }
    public DateTime? CompletionDate { get; set; }
    public decimal InvestmentRequired { get; set; }
}

/// <summary>
/// Sustainability target data transfer object
/// </summary>
public class SustainabilityTargetDto
{
    public string TargetName { get; set; } = string.Empty;
    public decimal TargetValue { get; set; }
    public decimal CurrentValue { get; set; }
    public DateTime TargetDate { get; set; }
    public decimal Progress { get; set; }
    public string Status { get; set; } = string.Empty; // "On Track", "At Risk", "Behind", "Achieved"
    public List<string> RequiredActions { get; set; } = new();
}

/// <summary>
/// Shipper capacity planning analytics DTO
/// </summary>
public class ShipperCapacityPlanningDto
{
    public string PlanningPeriod { get; set; } = string.Empty;
    public decimal CurrentCapacityUtilization { get; set; }
    public decimal ProjectedDemand { get; set; }
    public decimal CapacityGap { get; set; }
    public List<string> RecommendedActions { get; set; } = new();
    public decimal EstimatedCostImpact { get; set; }
    public DateTime LastUpdated { get; set; }
}

/// <summary>
/// Shipper sustainability analytics DTO
/// </summary>
public class ShipperSustainabilityAnalyticsDto
{
    public decimal CarbonFootprint { get; set; }
    public decimal FuelEfficiencyScore { get; set; }
    public decimal SustainabilityRating { get; set; }
    public List<string> GreenInitiatives { get; set; } = new();
    public decimal EnvironmentalCostSavings { get; set; }
    public string ComplianceStatus { get; set; } = string.Empty;
}

/// <summary>
/// Shipper risk management DTO
/// </summary>
public class ShipperRiskManagementDto
{
    public decimal OverallRiskScore { get; set; }
    public List<string> IdentifiedRisks { get; set; } = new();
    public List<string> MitigationStrategies { get; set; } = new();
    public decimal RiskExposure { get; set; }
    public string RiskTolerance { get; set; } = string.Empty;
    public DateTime LastAssessment { get; set; }
}

/// <summary>
/// Shipper market intelligence DTO
/// </summary>
public class ShipperMarketIntelligenceDto
{
    public decimal MarketShare { get; set; }
    public List<string> CompetitorAnalysis { get; set; } = new();
    public decimal PricingTrends { get; set; }
    public List<string> MarketOpportunities { get; set; } = new();
    public decimal DemandForecast { get; set; }
    public string MarketPosition { get; set; } = string.Empty;
}

/// <summary>
/// Shipper operational insights DTO
/// </summary>
public class ShipperOperationalInsightsDto
{
    public decimal OperationalEfficiency { get; set; }
    public List<string> ProcessImprovements { get; set; } = new();
    public decimal ResourceUtilization { get; set; }
    public List<string> BottleneckAreas { get; set; } = new();
    public decimal ProductivityScore { get; set; }
    public DateTime LastAnalysis { get; set; }
}

/// <summary>
/// Shipper technology utilization DTO
/// </summary>
public class ShipperTechnologyUtilizationDto
{
    public decimal TechnologyAdoptionRate { get; set; }
    public List<string> ImplementedSolutions { get; set; } = new();
    public decimal DigitalMaturityScore { get; set; }
    public List<string> RecommendedTechnologies { get; set; } = new();
    public decimal ROIFromTechnology { get; set; }
    public string TechnologyRoadmap { get; set; } = string.Empty;
}

/// <summary>
/// Shipper customer satisfaction DTO
/// </summary>
public class ShipperCustomerSatisfactionDto
{
    public decimal OverallSatisfactionScore { get; set; }
    public decimal ServiceQualityRating { get; set; }
    public decimal ResponseTimeRating { get; set; }
    public List<string> CustomerFeedback { get; set; } = new();
    public decimal CustomerRetentionRate { get; set; }
    public List<string> ImprovementAreas { get; set; } = new();
}
