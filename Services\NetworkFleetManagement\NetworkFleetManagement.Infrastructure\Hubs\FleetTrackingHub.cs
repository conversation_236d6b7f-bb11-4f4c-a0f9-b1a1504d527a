using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using NetworkFleetManagement.Infrastructure.Services;
using System.Security.Claims;

namespace NetworkFleetManagement.Infrastructure.Hubs;

public class FleetTrackingHub : Hub
{
    private readonly ILogger<FleetTrackingHub> _logger;
    private readonly NetworkFleetCacheService _cacheService;

    public FleetTrackingHub(ILogger<FleetTrackingHub> logger, NetworkFleetCacheService cacheService)
    {
        _logger = logger;
        _cacheService = cacheService;
    }

    public override async Task OnConnectedAsync()
    {
        var userId = Context.UserIdentifier;
        var carrierId = GetCarrierIdFromContext();
        
        _logger.LogInformation("User {UserId} connected to FleetTrackingHub", userId);

        if (carrierId.HasValue)
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, $"carrier_{carrierId}");
            _logger.LogDebug("Added connection {ConnectionId} to carrier group {CarrierId}", Context.ConnectionId, carrierId);
        }

        await base.OnConnectedAsync();
    }

    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        var userId = Context.UserIdentifier;
        var carrierId = GetCarrierIdFromContext();

        _logger.LogInformation("User {UserId} disconnected from FleetTrackingHub", userId);

        if (carrierId.HasValue)
        {
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"carrier_{carrierId}");
            _logger.LogDebug("Removed connection {ConnectionId} from carrier group {CarrierId}", Context.ConnectionId, carrierId);
        }

        await base.OnDisconnectedAsync(exception);
    }

    // Vehicle Tracking Methods
    public async Task JoinVehicleTracking(Guid vehicleId)
    {
        try
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, $"vehicle_{vehicleId}");
            _logger.LogDebug("Connection {ConnectionId} joined vehicle tracking for {VehicleId}", Context.ConnectionId, vehicleId);
            
            // Send current vehicle location if available
            var currentLocation = await _cacheService.GetVehicleLocationAsync(vehicleId);
            if (currentLocation != null)
            {
                await Clients.Caller.SendAsync("VehicleLocationUpdate", vehicleId, currentLocation);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error joining vehicle tracking for {VehicleId}", vehicleId);
        }
    }

    public async Task LeaveVehicleTracking(Guid vehicleId)
    {
        try
        {
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"vehicle_{vehicleId}");
            _logger.LogDebug("Connection {ConnectionId} left vehicle tracking for {VehicleId}", Context.ConnectionId, vehicleId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error leaving vehicle tracking for {VehicleId}", vehicleId);
        }
    }

    public async Task UpdateVehicleLocation(Guid vehicleId, double latitude, double longitude, Dictionary<string, object>? metadata = null)
    {
        try
        {
            var timestamp = DateTime.UtcNow;
            
            // Cache the location update
            await _cacheService.SetVehicleLocationAsync(vehicleId, latitude, longitude, timestamp, metadata);
            
            // Broadcast to all clients tracking this vehicle
            var locationUpdate = new
            {
                vehicleId,
                latitude,
                longitude,
                timestamp,
                metadata = metadata ?? new Dictionary<string, object>()
            };

            await Clients.Group($"vehicle_{vehicleId}").SendAsync("VehicleLocationUpdate", vehicleId, locationUpdate);
            
            // Also send to carrier group
            var carrierId = GetCarrierIdFromContext();
            if (carrierId.HasValue)
            {
                await Clients.Group($"carrier_{carrierId}").SendAsync("VehicleLocationUpdate", vehicleId, locationUpdate);
            }

            _logger.LogDebug("Broadcasted location update for vehicle {VehicleId}", vehicleId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating vehicle location for {VehicleId}", vehicleId);
        }
    }

    // Driver Status Methods
    public async Task JoinDriverTracking(Guid driverId)
    {
        try
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, $"driver_{driverId}");
            _logger.LogDebug("Connection {ConnectionId} joined driver tracking for {DriverId}", Context.ConnectionId, driverId);
            
            // Send current driver status if available
            var currentStatus = await _cacheService.GetDriverStatusAsync(driverId);
            if (currentStatus != null)
            {
                await Clients.Caller.SendAsync("DriverStatusUpdate", driverId, currentStatus);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error joining driver tracking for {DriverId}", driverId);
        }
    }

    public async Task LeaveDriverTracking(Guid driverId)
    {
        try
        {
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"driver_{driverId}");
            _logger.LogDebug("Connection {ConnectionId} left driver tracking for {DriverId}", Context.ConnectionId, driverId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error leaving driver tracking for {DriverId}", driverId);
        }
    }

    public async Task UpdateDriverStatus(Guid driverId, string status, Dictionary<string, object>? metadata = null)
    {
        try
        {
            var timestamp = DateTime.UtcNow;
            
            // Cache the status update
            await _cacheService.SetDriverStatusAsync(driverId, status, timestamp, metadata);
            
            // Broadcast to all clients tracking this driver
            var statusUpdate = new
            {
                driverId,
                status,
                timestamp,
                metadata = metadata ?? new Dictionary<string, object>()
            };

            await Clients.Group($"driver_{driverId}").SendAsync("DriverStatusUpdate", driverId, statusUpdate);
            
            // Also send to carrier group
            var carrierId = GetCarrierIdFromContext();
            if (carrierId.HasValue)
            {
                await Clients.Group($"carrier_{carrierId}").SendAsync("DriverStatusUpdate", driverId, statusUpdate);
            }

            _logger.LogDebug("Broadcasted status update for driver {DriverId}", driverId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating driver status for {DriverId}", driverId);
        }
    }

    // Fleet Monitoring Methods
    public async Task JoinFleetMonitoring(Guid carrierId)
    {
        try
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, $"fleet_monitor_{carrierId}");
            _logger.LogDebug("Connection {ConnectionId} joined fleet monitoring for carrier {CarrierId}", Context.ConnectionId, carrierId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error joining fleet monitoring for carrier {CarrierId}", carrierId);
        }
    }

    public async Task LeaveFleetMonitoring(Guid carrierId)
    {
        try
        {
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"fleet_monitor_{carrierId}");
            _logger.LogDebug("Connection {ConnectionId} left fleet monitoring for carrier {CarrierId}", Context.ConnectionId, carrierId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error leaving fleet monitoring for carrier {CarrierId}", carrierId);
        }
    }

    // Alert Methods
    public async Task JoinAlerts(Guid? carrierId = null)
    {
        try
        {
            var groupName = carrierId.HasValue ? $"alerts_{carrierId}" : "alerts_global";
            await Groups.AddToGroupAsync(Context.ConnectionId, groupName);
            _logger.LogDebug("Connection {ConnectionId} joined alerts group {GroupName}", Context.ConnectionId, groupName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error joining alerts for carrier {CarrierId}", carrierId);
        }
    }

    public async Task LeaveAlerts(Guid? carrierId = null)
    {
        try
        {
            var groupName = carrierId.HasValue ? $"alerts_{carrierId}" : "alerts_global";
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);
            _logger.LogDebug("Connection {ConnectionId} left alerts group {GroupName}", Context.ConnectionId, groupName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error leaving alerts for carrier {CarrierId}", carrierId);
        }
    }

    // Network Performance Methods
    public async Task JoinNetworkMonitoring(Guid networkId)
    {
        try
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, $"network_{networkId}");
            _logger.LogDebug("Connection {ConnectionId} joined network monitoring for {NetworkId}", Context.ConnectionId, networkId);
            
            // Send current network performance if available
            var currentPerformance = await _cacheService.GetNetworkPerformanceAsync(networkId);
            if (currentPerformance != null)
            {
                await Clients.Caller.SendAsync("NetworkPerformanceUpdate", networkId, currentPerformance);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error joining network monitoring for {NetworkId}", networkId);
        }
    }

    public async Task LeaveNetworkMonitoring(Guid networkId)
    {
        try
        {
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"network_{networkId}");
            _logger.LogDebug("Connection {ConnectionId} left network monitoring for {NetworkId}", Context.ConnectionId, networkId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error leaving network monitoring for {NetworkId}", networkId);
        }
    }

    // Helper Methods
    private Guid? GetCarrierIdFromContext()
    {
        var carrierIdClaim = Context.User?.FindFirst("carrierId")?.Value;
        if (Guid.TryParse(carrierIdClaim, out var carrierId))
        {
            return carrierId;
        }
        return null;
    }

    private string? GetUserRole()
    {
        return Context.User?.FindFirst(ClaimTypes.Role)?.Value;
    }

    private bool IsAuthorizedForCarrier(Guid carrierId)
    {
        var userCarrierId = GetCarrierIdFromContext();
        var userRole = GetUserRole();
        
        // Admin users can access any carrier
        if (userRole == "Admin" || userRole == "SuperAdmin")
            return true;
            
        // Users can only access their own carrier
        return userCarrierId == carrierId;
    }
}
