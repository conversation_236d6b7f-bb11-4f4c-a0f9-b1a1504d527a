using Shared.Domain.Common;

namespace MonitoringObservability.Domain.Events;

/// <summary>
/// Event raised when an anomaly detection model is created
/// </summary>
public class AnomalyDetectionModelCreatedEvent : DomainEvent
{
    public AnomalyDetectionModelCreatedEvent(Guid modelId, string modelName, string serviceName, string metricName, string algorithm)
    {
        ModelId = modelId;
        ModelName = modelName;
        ServiceName = serviceName;
        MetricName = metricName;
        Algorithm = algorithm;
    }

    public Guid ModelId { get; }
    public string ModelName { get; }
    public string ServiceName { get; }
    public string MetricName { get; }
    public string Algorithm { get; }
}

/// <summary>
/// Event raised when an anomaly detection model is updated
/// </summary>
public class AnomalyDetectionModelUpdatedEvent : DomainEvent
{
    public AnomalyDetectionModelUpdatedEvent(Guid modelId, string modelName)
    {
        ModelId = modelId;
        ModelName = modelName;
    }

    public Guid ModelId { get; }
    public string ModelName { get; }
}

/// <summary>
/// Event raised when an anomaly detection model is enabled
/// </summary>
public class AnomalyDetectionModelEnabledEvent : DomainEvent
{
    public AnomalyDetectionModelEnabledEvent(Guid modelId, string modelName)
    {
        ModelId = modelId;
        ModelName = modelName;
    }

    public Guid ModelId { get; }
    public string ModelName { get; }
}

/// <summary>
/// Event raised when an anomaly detection model is disabled
/// </summary>
public class AnomalyDetectionModelDisabledEvent : DomainEvent
{
    public AnomalyDetectionModelDisabledEvent(Guid modelId, string modelName)
    {
        ModelId = modelId;
        ModelName = modelName;
    }

    public Guid ModelId { get; }
    public string ModelName { get; }
}

/// <summary>
/// Event raised when model training starts
/// </summary>
public class ModelTrainingStartedEvent : DomainEvent
{
    public ModelTrainingStartedEvent(Guid modelId, Guid sessionId, string algorithm)
    {
        ModelId = modelId;
        SessionId = sessionId;
        Algorithm = algorithm;
    }

    public Guid ModelId { get; }
    public Guid SessionId { get; }
    public string Algorithm { get; }
}

/// <summary>
/// Event raised when model training completes
/// </summary>
public class ModelTrainingCompletedEvent : DomainEvent
{
    public ModelTrainingCompletedEvent(Guid modelId, Guid sessionId, bool success, double accuracy)
    {
        ModelId = modelId;
        SessionId = sessionId;
        Success = success;
        Accuracy = accuracy;
    }

    public Guid ModelId { get; }
    public Guid SessionId { get; }
    public bool Success { get; }
    public double Accuracy { get; }
}

/// <summary>
/// Event raised when an anomaly is detected
/// </summary>
public class AnomalyDetectedEvent : DomainEvent
{
    public AnomalyDetectedEvent(Guid modelId, Guid resultId, double anomalyScore, double metricValue)
    {
        ModelId = modelId;
        ResultId = resultId;
        AnomalyScore = anomalyScore;
        MetricValue = metricValue;
    }

    public Guid ModelId { get; }
    public Guid ResultId { get; }
    public double AnomalyScore { get; }
    public double MetricValue { get; }
}

/// <summary>
/// Event raised when user provides feedback on an anomaly detection result
/// </summary>
public class AnomalyFeedbackProvidedEvent : DomainEvent
{
    public AnomalyFeedbackProvidedEvent(Guid resultId, bool isAnomaly, string? feedback)
    {
        ResultId = resultId;
        IsAnomaly = isAnomaly;
        Feedback = feedback;
    }

    public Guid ResultId { get; }
    public bool IsAnomaly { get; }
    public string? Feedback { get; }
}

/// <summary>
/// Event raised when a model needs retraining
/// </summary>
public class ModelRetrainingRequiredEvent : DomainEvent
{
    public ModelRetrainingRequiredEvent(Guid modelId, string modelName, string reason)
    {
        ModelId = modelId;
        ModelName = modelName;
        Reason = reason;
    }

    public Guid ModelId { get; }
    public string ModelName { get; }
    public string Reason { get; }
}

/// <summary>
/// Event raised when anomaly statistics are generated
/// </summary>
public class AnomalyStatisticsGeneratedEvent : DomainEvent
{
    public AnomalyStatisticsGeneratedEvent(string? serviceName, int totalAnomalies, double averageScore)
    {
        ServiceName = serviceName;
        TotalAnomalies = totalAnomalies;
        AverageScore = averageScore;
    }

    public string? ServiceName { get; }
    public int TotalAnomalies { get; }
    public double AverageScore { get; }
}
