using MediatR;
using MonitoringObservability.Application.DTOs;
using MonitoringObservability.Domain.Enums;

namespace MonitoringObservability.Application.Queries;

// Alert queries
public class GetAlertByIdQuery : IRequest<AlertDto?>
{
    public Guid AlertId { get; set; }
    public bool IncludeDetails { get; set; } = false;
}

public class GetAlertsQuery : IRequest<IEnumerable<AlertDto>>
{
    public string? ServiceName { get; set; }
    public AlertSeverity? Severity { get; set; }
    public AlertStatus? Status { get; set; }
    public DateTime? TriggeredAfter { get; set; }
    public DateTime? TriggeredBefore { get; set; }
    public Guid? AssignedToUserId { get; set; }
    public int Skip { get; set; } = 0;
    public int Take { get; set; } = 50;
    public string? SortBy { get; set; } = "TriggeredAt";
    public bool SortDescending { get; set; } = true;
}

public class GetOpenAlertsQuery : IRequest<IEnumerable<AlertDto>>
{
    public string? ServiceName { get; set; }
    public AlertSeverity? MinimumSeverity { get; set; }
}

public class GetCriticalAlertsQuery : IRequest<IEnumerable<AlertDto>>
{
    public string? ServiceName { get; set; }
}

public class GetUnassignedAlertsQuery : IRequest<IEnumerable<AlertDto>>
{
    public AlertSeverity? MinimumSeverity { get; set; }
    public TimeSpan? OlderThan { get; set; }
}

public class GetAlertsAssignedToUserQuery : IRequest<IEnumerable<AlertDto>>
{
    public Guid UserId { get; set; }
    public AlertStatus? Status { get; set; }
}

public class GetAlertCountBySeverityQuery : IRequest<Dictionary<AlertSeverity, int>>
{
    public DateTime? FromDate { get; set; }
    public string? ServiceName { get; set; }
}

public class GetAlertCountByServiceQuery : IRequest<Dictionary<string, int>>
{
    public DateTime? FromDate { get; set; }
    public AlertSeverity? MinimumSeverity { get; set; }
}

// Metric queries
public class GetMetricByIdQuery : IRequest<MetricDto?>
{
    public Guid MetricId { get; set; }
}

public class GetMetricByNameQuery : IRequest<MetricDto?>
{
    public string MetricName { get; set; } = string.Empty;
    public string ServiceName { get; set; } = string.Empty;
}

public class GetMetricsQuery : IRequest<IEnumerable<MetricDto>>
{
    public string? ServiceName { get; set; }
    public MetricType? Type { get; set; }
    public MonitoringCategory? Category { get; set; }
    public bool? IsActive { get; set; }
    public bool? AlertingEnabled { get; set; }
}

public class GetMetricsByServiceQuery : IRequest<IEnumerable<MetricDto>>
{
    public string ServiceName { get; set; } = string.Empty;
    public bool ActiveOnly { get; set; } = true;
}

public class GetMetricDataPointsQuery : IRequest<IEnumerable<MetricDataPointDto>>
{
    public Guid MetricId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public TimeSpan? AggregationInterval { get; set; }
}

public class GetMetricDataByNameQuery : IRequest<IEnumerable<MetricDataPointDto>>
{
    public string ServiceName { get; set; } = string.Empty;
    public string MetricName { get; set; } = string.Empty;
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public TimeSpan? AggregationInterval { get; set; }
}

public class GetLatestMetricValuesQuery : IRequest<IEnumerable<MetricDataPointDto>>
{
    public string ServiceName { get; set; } = string.Empty;
    public TimeSpan Period { get; set; } = TimeSpan.FromHours(1);
}

public class GetMetricAggregationQuery : IRequest<MetricAggregationDto>
{
    public Guid MetricId { get; set; }
    public TimeSpan Period { get; set; }
    public AggregationType AggregationType { get; set; } = AggregationType.Average;
}

// Health check queries
public class GetHealthCheckByIdQuery : IRequest<HealthCheckDto?>
{
    public Guid HealthCheckId { get; set; }
}

public class GetHealthCheckByNameQuery : IRequest<HealthCheckDto?>
{
    public string Name { get; set; } = string.Empty;
    public string ServiceName { get; set; } = string.Empty;
}

public class GetHealthChecksQuery : IRequest<IEnumerable<HealthCheckDto>>
{
    public string? ServiceName { get; set; }
    public HealthStatus? Status { get; set; }
    public bool? IsEnabled { get; set; }
}

public class GetHealthChecksByServiceQuery : IRequest<IEnumerable<HealthCheckDto>>
{
    public string ServiceName { get; set; } = string.Empty;
    public bool EnabledOnly { get; set; } = true;
}

public class GetHealthChecksDueForExecutionQuery : IRequest<IEnumerable<HealthCheckDto>>
{
    public DateTime? AsOfTime { get; set; }
}

public class GetHealthStatusSummaryQuery : IRequest<Dictionary<HealthStatus, int>>
{
    public string? ServiceName { get; set; }
}

public class GetServiceHealthSummaryQuery : IRequest<ServiceHealthSummaryDto?>
{
    public string ServiceName { get; set; } = string.Empty;
    public bool IncludeMetrics { get; set; } = true;
    public bool IncludeAlerts { get; set; } = true;
    public bool IncludeIncidents { get; set; } = true;
}

public class GetAllServicesHealthSummaryQuery : IRequest<IEnumerable<ServiceHealthSummaryDto>>
{
    public bool IncludeMetrics { get; set; } = false;
    public bool IncludeAlerts { get; set; } = true;
    public bool IncludeIncidents { get; set; } = true;
}

public class GetServiceUptimeQuery : IRequest<double>
{
    public string ServiceName { get; set; } = string.Empty;
    public TimeSpan Period { get; set; } = TimeSpan.FromDays(30);
}

// Incident queries
public class GetIncidentByIdQuery : IRequest<IncidentDto?>
{
    public Guid IncidentId { get; set; }
    public bool IncludeDetails { get; set; } = false;
}

public class GetIncidentsQuery : IRequest<IEnumerable<IncidentDto>>
{
    public string? ServiceName { get; set; }
    public IncidentSeverity? Severity { get; set; }
    public IncidentStatus? Status { get; set; }
    public DateTime? CreatedAfter { get; set; }
    public DateTime? CreatedBefore { get; set; }
    public Guid? AssignedToUserId { get; set; }
    public int Skip { get; set; } = 0;
    public int Take { get; set; } = 50;
    public string? SortBy { get; set; } = "CreatedAt";
    public bool SortDescending { get; set; } = true;
}

public class GetOpenIncidentsQuery : IRequest<IEnumerable<IncidentDto>>
{
    public string? ServiceName { get; set; }
    public IncidentSeverity? MinimumSeverity { get; set; }
}

public class GetCriticalIncidentsQuery : IRequest<IEnumerable<IncidentDto>>
{
    public string? ServiceName { get; set; }
}

public class GetIncidentsAssignedToUserQuery : IRequest<IEnumerable<IncidentDto>>
{
    public Guid UserId { get; set; }
    public IncidentStatus? Status { get; set; }
}

public class GetIncidentCountBySeverityQuery : IRequest<Dictionary<IncidentSeverity, int>>
{
    public DateTime? FromDate { get; set; }
    public string? ServiceName { get; set; }
}

public class GetIncidentCountByServiceQuery : IRequest<Dictionary<string, int>>
{
    public DateTime? FromDate { get; set; }
    public IncidentSeverity? MinimumSeverity { get; set; }
}

public class GetAverageResolutionTimeQuery : IRequest<double>
{
    public DateTime? FromDate { get; set; }
    public string? ServiceName { get; set; }
    public IncidentSeverity? Severity { get; set; }
}

// Dashboard and reporting queries
public class GetMonitoringDashboardQuery : IRequest<MonitoringDashboardDto>
{
    public DashboardType Type { get; set; } = DashboardType.System;
    public string? ServiceName { get; set; }
    public TimeSpan? TimeRange { get; set; } = TimeSpan.FromHours(24);
}

public class GetPerformanceMetricsQuery : IRequest<PerformanceMetricsDto?>
{
    public string ServiceName { get; set; } = string.Empty;
    public TimeSpan? Period { get; set; } = TimeSpan.FromMinutes(5);
}

public class GetSystemOverviewQuery : IRequest<SystemOverviewDto>
{
    public TimeSpan? TimeRange { get; set; } = TimeSpan.FromHours(1);
}

// Supporting DTOs
public class MetricAggregationDto
{
    public Guid MetricId { get; set; }
    public string MetricName { get; set; } = string.Empty;
    public string ServiceName { get; set; } = string.Empty;
    public AggregationType AggregationType { get; set; }
    public double Value { get; set; }
    public TimeSpan Period { get; set; }
    public DateTime CalculatedAt { get; set; }
}

public class SystemOverviewDto
{
    public int TotalServices { get; set; }
    public int HealthyServices { get; set; }
    public int DegradedServices { get; set; }
    public int UnhealthyServices { get; set; }
    public int TotalAlerts { get; set; }
    public int CriticalAlerts { get; set; }
    public int OpenIncidents { get; set; }
    public int CriticalIncidents { get; set; }
    public double OverallSystemHealth { get; set; }
    public DateTime GeneratedAt { get; set; }
}

public enum AggregationType
{
    Average = 0,
    Sum = 1,
    Min = 2,
    Max = 3,
    Count = 4,
    Percentile95 = 5,
    Percentile99 = 6
}

// Distributed Tracing Queries
public class GetTraceByIdQuery : IRequest<TraceDto?>
{
    public Guid Id { get; set; }
}

public class GetTraceByTraceIdQuery : IRequest<TraceDto?>
{
    public string TraceId { get; set; } = string.Empty;
}

public class SearchTracesQuery : IRequest<IEnumerable<TraceDto>>
{
    public string? ServiceName { get; set; }
    public string? OperationName { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public TimeSpan? MinDuration { get; set; }
    public TimeSpan? MaxDuration { get; set; }
    public TraceStatus? Status { get; set; }
    public bool? HasErrors { get; set; }
    public Dictionary<string, string>? Tags { get; set; }
    public int Skip { get; set; } = 0;
    public int Take { get; set; } = 100;
}

public class GetSlowTracesQuery : IRequest<IEnumerable<TraceDto>>
{
    public string? ServiceName { get; set; }
    public TimeSpan Threshold { get; set; }
    public TimeSpan Period { get; set; }
    public int Limit { get; set; } = 100;
}

public class GetErrorTracesQuery : IRequest<IEnumerable<TraceDto>>
{
    public string? ServiceName { get; set; }
    public TimeSpan Period { get; set; }
    public int Limit { get; set; } = 100;
}

public class GetActiveTracesQuery : IRequest<IEnumerable<TraceDto>>
{
}

public class GetTraceStatisticsQuery : IRequest<TraceStatisticsDto>
{
    public string? ServiceName { get; set; }
    public TimeSpan Period { get; set; } = TimeSpan.FromDays(1);
}

// Span Queries
public class GetSpanByIdQuery : IRequest<SpanDto?>
{
    public Guid Id { get; set; }
}

public class GetSpanBySpanIdQuery : IRequest<SpanDto?>
{
    public string SpanId { get; set; } = string.Empty;
}

public class GetSpansByTraceIdQuery : IRequest<IEnumerable<SpanDto>>
{
    public string TraceId { get; set; } = string.Empty;
}

public class GetSpansByServiceQuery : IRequest<IEnumerable<SpanDto>>
{
    public string ServiceName { get; set; } = string.Empty;
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
}

public class GetSpansByOperationQuery : IRequest<IEnumerable<SpanDto>>
{
    public string OperationName { get; set; } = string.Empty;
    public string? ServiceName { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
}

public class GetErrorSpansQuery : IRequest<IEnumerable<SpanDto>>
{
    public string? ServiceName { get; set; }
    public TimeSpan? Period { get; set; }
    public int Limit { get; set; } = 100;
}

public class GetSlowSpansQuery : IRequest<IEnumerable<SpanDto>>
{
    public TimeSpan Threshold { get; set; }
    public string? ServiceName { get; set; }
    public TimeSpan? Period { get; set; }
    public int Limit { get; set; } = 100;
}

public class GetActiveSpansQuery : IRequest<IEnumerable<SpanDto>>
{
}

// Service Dependency Queries
public class GetServiceDependencyGraphQuery : IRequest<ServiceDependencyGraphDto>
{
    public bool IncludeHealthStatus { get; set; } = true;
    public TimeSpan? Period { get; set; }
}

public class GetServiceDependenciesQuery : IRequest<IEnumerable<ServiceDependencyDto>>
{
    public string ServiceName { get; set; } = string.Empty;
}

public class GetServiceDependentsQuery : IRequest<IEnumerable<ServiceDependencyDto>>
{
    public string ServiceName { get; set; } = string.Empty;
}

public class GetHighErrorRateDependenciesQuery : IRequest<IEnumerable<ServiceDependencyDto>>
{
    public double ErrorRateThreshold { get; set; } = 0.05;
}

public class GetSlowDependenciesQuery : IRequest<IEnumerable<ServiceDependencyDto>>
{
    public TimeSpan LatencyThreshold { get; set; }
}

public class GetServiceCallCountsQuery : IRequest<Dictionary<string, int>>
{
    public TimeSpan Period { get; set; } = TimeSpan.FromDays(1);
}

public class GetTopErrorRateDependenciesQuery : IRequest<IEnumerable<(string fromService, string toService, double errorRate)>>
{
    public int TopCount { get; set; } = 10;
    public TimeSpan? Period { get; set; }
}

// Trace Analytics Queries
public class GetAverageTraceTimeQuery : IRequest<double>
{
    public string ServiceName { get; set; } = string.Empty;
    public string? OperationName { get; set; }
    public TimeSpan? Period { get; set; }
}

public class GetSlowestOperationsQuery : IRequest<IEnumerable<(string operationName, double averageTime, int count)>>
{
    public string ServiceName { get; set; } = string.Empty;
    public TimeSpan Period { get; set; }
    public int TopCount { get; set; } = 10;
}

public class GetTraceCountByServiceQuery : IRequest<Dictionary<string, int>>
{
    public TimeSpan Period { get; set; } = TimeSpan.FromDays(1);
}

public class GetTraceCountByStatusQuery : IRequest<Dictionary<TraceStatus, int>>
{
    public TimeSpan Period { get; set; } = TimeSpan.FromDays(1);
    public string? ServiceName { get; set; }
}

public class GetOperationLatencyPercentilesQuery : IRequest<Dictionary<string, double>>
{
    public string ServiceName { get; set; } = string.Empty;
    public string OperationName { get; set; } = string.Empty;
    public TimeSpan Period { get; set; }
    public double[] Percentiles { get; set; } = { 50, 95, 99 };
}

// Supporting DTOs
public class TraceStatisticsDto
{
    public int TotalTraces { get; set; }
    public int SuccessfulTraces { get; set; }
    public int ErrorTraces { get; set; }
    public double ErrorRate { get; set; }
    public TimeSpan AverageDuration { get; set; }
    public TimeSpan MedianDuration { get; set; }
    public TimeSpan P95Duration { get; set; }
    public TimeSpan P99Duration { get; set; }
    public int TotalSpans { get; set; }
    public int UniqueServices { get; set; }
    public int UniqueOperations { get; set; }
    public Dictionary<string, int> TracesByService { get; set; } = new();
    public Dictionary<string, double> AverageLatencyByService { get; set; } = new();
    public Dictionary<TraceStatus, int> TracesByStatus { get; set; } = new();
    public DateTime GeneratedAt { get; set; }
}
