using MediatR;
using OrderManagement.Application.DTOs;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Application.Queries.GetBrokerQuoteAnalytics;

public class GetBrokerQuoteAnalyticsQuery : IRequest<BrokerQuoteAnalyticsDto>
{
    public Guid BrokerId { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public List<Guid>? CarrierIds { get; set; } // Filter by specific carriers
    public List<LoadType>? LoadTypes { get; set; } // Filter by load types
    public List<string>? Routes { get; set; } // Filter by specific routes (pickup-delivery combinations)
    public bool IncludeDailyBreakdown { get; set; } = true;
    public bool IncludeCarrierBreakdown { get; set; } = true;
    public bool IncludeLoadTypeBreakdown { get; set; } = true;
    public bool IncludeRouteBreakdown { get; set; } = true;
    public bool IncludeTopPerformingRoutes { get; set; } = true;
    public int TopRoutesLimit { get; set; } = 10;
    
    public GetBrokerQuoteAnalyticsQuery()
    {
        // Default to last 30 days if no dates specified
        ToDate = DateTime.UtcNow.Date.AddDays(1).AddTicks(-1); // End of today
        FromDate = ToDate.Value.AddDays(-30); // 30 days ago
    }
    
    public GetBrokerQuoteAnalyticsQuery(Guid brokerId, DateTime? fromDate = null, DateTime? toDate = null)
    {
        BrokerId = brokerId;
        FromDate = fromDate ?? DateTime.UtcNow.Date.AddDays(-30);
        ToDate = toDate ?? DateTime.UtcNow.Date.AddDays(1).AddTicks(-1);
    }
}
