using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Domain.Interfaces
{
    public interface ISubscriptionPaymentProofRepository
    {
        Task<SubscriptionPaymentProof?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
        Task<SubscriptionPaymentProof?> GetByIdWithSubscriptionAsync(Guid id, CancellationToken cancellationToken = default);
        Task<List<SubscriptionPaymentProof>> GetBySubscriptionIdAsync(Guid subscriptionId, CancellationToken cancellationToken = default);
        Task<List<SubscriptionPaymentProof>> GetByUserIdAsync(Guid userId, CancellationToken cancellationToken = default);
        Task<List<SubscriptionPaymentProof>> GetByStatusAsync(PaymentProofStatus status, CancellationToken cancellationToken = default);
        Task<List<SubscriptionPaymentProof>> GetPendingProofsAsync(CancellationToken cancellationToken = default);
        Task<List<SubscriptionPaymentProof>> GetProofsRequiringReviewAsync(CancellationToken cancellationToken = default);
        
        Task<(List<SubscriptionPaymentProof> Items, int TotalCount)> GetPagedAsync(
            int pageNumber, 
            int pageSize, 
            PaymentProofStatus? status = null,
            Guid? userId = null,
            Guid? subscriptionId = null,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            CancellationToken cancellationToken = default);

        Task<SubscriptionPaymentProof> AddAsync(SubscriptionPaymentProof paymentProof, CancellationToken cancellationToken = default);
        Task UpdateAsync(SubscriptionPaymentProof paymentProof, CancellationToken cancellationToken = default);
        Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);
        
        Task<bool> ExistsAsync(Guid id, CancellationToken cancellationToken = default);
        Task<bool> HasPendingProofForSubscriptionAsync(Guid subscriptionId, CancellationToken cancellationToken = default);
        Task<int> GetCountByStatusAsync(PaymentProofStatus status, CancellationToken cancellationToken = default);
        
        Task<List<SubscriptionPaymentProof>> GetVerifiedProofsByDateRangeAsync(
            DateTime fromDate, 
            DateTime toDate, 
            CancellationToken cancellationToken = default);
    }
}
