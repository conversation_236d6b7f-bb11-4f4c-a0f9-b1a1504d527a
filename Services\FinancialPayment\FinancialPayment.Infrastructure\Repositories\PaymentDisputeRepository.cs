using Microsoft.EntityFrameworkCore;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Application.Interfaces.Repositories;
using FinancialPayment.Infrastructure.Data;

namespace FinancialPayment.Infrastructure.Repositories;

public class PaymentDisputeRepository : IPaymentDisputeRepository
{
    private readonly FinancialPaymentDbContext _context;

    public PaymentDisputeRepository(FinancialPaymentDbContext context)
    {
        _context = context;
    }

    public async Task<PaymentDispute?> GetByIdAsync(Guid id)
    {
        return await _context.PaymentDisputes
            .Include(d => d.Comments)
            .Include(d => d.Documents)
            .Include(d => d.EscrowAccount)
            .Include(d => d.Settlement)
            .FirstOrDefaultAsync(d => d.Id == id);
    }

    public async Task<PaymentDispute?> GetByDisputeNumberAsync(string disputeNumber)
    {
        return await _context.PaymentDisputes
            .Include(d => d.Comments)
            .Include(d => d.Documents)
            .Include(d => d.EscrowAccount)
            .Include(d => d.Settlement)
            .FirstOrDefaultAsync(d => d.DisputeNumber == disputeNumber);
    }

    public async Task<List<PaymentDispute>> GetByOrderIdAsync(Guid orderId)
    {
        return await _context.PaymentDisputes
            .Include(d => d.Comments)
            .Include(d => d.Documents)
            .Include(d => d.EscrowAccount)
            .Include(d => d.Settlement)
            .Where(d => d.OrderId == orderId)
            .OrderByDescending(d => d.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<PaymentDispute>> GetByInitiatorIdAsync(Guid initiatorId)
    {
        return await _context.PaymentDisputes
            .Include(d => d.Comments)
            .Include(d => d.Documents)
            .Include(d => d.EscrowAccount)
            .Include(d => d.Settlement)
            .Where(d => d.InitiatedBy == initiatorId)
            .OrderByDescending(d => d.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<PaymentDispute>> GetByStatusAsync(DisputeStatus status)
    {
        return await _context.PaymentDisputes
            .Include(d => d.Comments)
            .Include(d => d.Documents)
            .Include(d => d.EscrowAccount)
            .Include(d => d.Settlement)
            .Where(d => d.Status == status)
            .OrderByDescending(d => d.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<PaymentDispute>> GetByCategoryAsync(DisputeCategory category)
    {
        return await _context.PaymentDisputes
            .Include(d => d.Comments)
            .Include(d => d.Documents)
            .Include(d => d.EscrowAccount)
            .Include(d => d.Settlement)
            .Where(d => d.Category == category)
            .OrderByDescending(d => d.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<PaymentDispute>> GetByPriorityAsync(DisputePriority priority)
    {
        return await _context.PaymentDisputes
            .Include(d => d.Comments)
            .Include(d => d.Documents)
            .Include(d => d.EscrowAccount)
            .Include(d => d.Settlement)
            .Where(d => d.Priority == priority)
            .OrderByDescending(d => d.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<PaymentDispute>> GetByDateRangeAsync(DateTime from, DateTime to)
    {
        return await _context.PaymentDisputes
            .Include(d => d.Comments)
            .Include(d => d.Documents)
            .Include(d => d.EscrowAccount)
            .Include(d => d.Settlement)
            .Where(d => d.CreatedAt >= from && d.CreatedAt <= to)
            .OrderByDescending(d => d.CreatedAt)
            .ToListAsync();
    }

    public async Task<PaymentDispute> AddAsync(PaymentDispute dispute)
    {
        _context.PaymentDisputes.Add(dispute);
        return dispute;
    }

    public async Task UpdateAsync(PaymentDispute dispute)
    {
        _context.PaymentDisputes.Update(dispute);
        await Task.CompletedTask;
    }

    public async Task DeleteAsync(Guid id)
    {
        var dispute = await _context.PaymentDisputes.FindAsync(id);
        if (dispute != null)
        {
            _context.PaymentDisputes.Remove(dispute);
        }
    }

    public async Task<bool> ExistsAsync(Guid id)
    {
        return await _context.PaymentDisputes.AnyAsync(d => d.Id == id);
    }

    public async Task<List<PaymentDispute>> GetOpenDisputesAsync()
    {
        return await _context.PaymentDisputes
            .Include(d => d.Comments)
            .Include(d => d.Documents)
            .Include(d => d.EscrowAccount)
            .Include(d => d.Settlement)
            .Where(d => d.Status == DisputeStatus.Open || d.Status == DisputeStatus.InProgress || d.Status == DisputeStatus.Escalated)
            .OrderBy(d => d.Priority)
            .ThenBy(d => d.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<PaymentDispute>> GetOverdueDisputesAsync()
    {
        var overdueDate = DateTime.UtcNow.AddDays(-7); // Consider disputes overdue after 7 days

        return await _context.PaymentDisputes
            .Include(d => d.Comments)
            .Include(d => d.Documents)
            .Include(d => d.EscrowAccount)
            .Include(d => d.Settlement)
            .Where(d => (d.Status == DisputeStatus.Open || d.Status == DisputeStatus.InProgress) && d.CreatedAt < overdueDate)
            .OrderBy(d => d.CreatedAt)
            .ToListAsync();
    }

    public async Task<int> GetDisputeCountByStatusAsync(DisputeStatus status)
    {
        return await _context.PaymentDisputes
            .CountAsync(d => d.Status == status);
    }
}
