using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using OrderManagement.Infrastructure.Persistence;

namespace OrderManagement.IntegrationTests.Infrastructure;

public class TestWebApplicationFactory<TProgram> : WebApplicationFactory<TProgram> where TProgram : class
{
    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        builder.ConfigureServices(services =>
        {
            // Remove the existing DbContext registration
            services.RemoveAll(typeof(DbContextOptions<OrderManagementDbContext>));
            services.RemoveAll(typeof(OrderManagementDbContext));

            // Add in-memory database for testing
            services.AddDbContext<OrderManagementDbContext>(options =>
            {
                options.UseInMemoryDatabase("TestDatabase");
            });

            // Override JWT settings for testing
            services.Configure<Microsoft.AspNetCore.Authentication.JwtBearer.JwtBearerOptions>(
                Microsoft.AspNetCore.Authentication.JwtBearer.JwtBearerDefaults.AuthenticationScheme,
                options =>
                {
                    options.RequireHttpsMetadata = false;
                });
        });

        builder.UseEnvironment("Testing");
    }

    public async Task<OrderManagementDbContext> GetDbContextAsync()
    {
        var scope = Services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<OrderManagementDbContext>();
        await context.Database.EnsureCreatedAsync();
        return context;
    }

    public async Task SeedDatabaseAsync()
    {
        using var context = await GetDbContextAsync();
        await SeedTestDataAsync(context);
    }

    private static async Task SeedTestDataAsync(OrderManagementDbContext context)
    {
        // Clear existing data
        context.Rfqs.RemoveRange(context.Rfqs);
        context.RfqBids.RemoveRange(context.RfqBids);
        context.Orders.RemoveRange(context.Orders);
        context.Invoices.RemoveRange(context.Invoices);
        
        await context.SaveChangesAsync();

        // Add test data if needed
        // This can be expanded based on test requirements
        
        await context.SaveChangesAsync();
    }
}
