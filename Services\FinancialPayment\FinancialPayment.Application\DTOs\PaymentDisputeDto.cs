namespace FinancialPayment.Application.DTOs;

public class PaymentDisputeDto
{
    public Guid Id { get; set; }
    public Guid OrderId { get; set; }
    public Guid? EscrowAccountId { get; set; }
    public Guid? SettlementId { get; set; }
    public Guid InitiatedBy { get; set; }
    public string InitiatorRole { get; set; } = string.Empty;
    public string DisputeNumber { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal DisputedAmount { get; set; }
    public string DisputedCurrency { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string Priority { get; set; } = string.Empty;
    public DateTime? ResolvedAt { get; set; }
    public string? Resolution { get; set; }
    public decimal? ResolvedAmount { get; set; }
    public string? ResolvedCurrency { get; set; }
    public Guid? ResolvedBy { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public List<DisputeCommentDto> Comments { get; set; } = new();
    public List<DisputeDocumentDto> Documents { get; set; } = new();
}

public class DisputeCommentDto
{
    public Guid Id { get; set; }
    public Guid DisputeId { get; set; }
    public Guid AuthorId { get; set; }
    public string AuthorRole { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public bool IsInternal { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

public class DisputeDocumentDto
{
    public Guid Id { get; set; }
    public Guid DisputeId { get; set; }
    public string FileName { get; set; } = string.Empty;
    public string FileUrl { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public Guid UploadedBy { get; set; }
    public DateTime UploadedAt { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}
