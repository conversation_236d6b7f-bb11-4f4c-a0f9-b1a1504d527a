using NBomber.CSharp;
using NBomber.Http.CSharp;
using NetworkFleetManagement.PerformanceTests.Infrastructure;
using System.Text.Json;
using Xunit.Abstractions;

namespace NetworkFleetManagement.PerformanceTests.LoadTests;

public class NetworkManagementLoadTests : PerformanceTestBase
{
    private readonly ITestOutputHelper _output;

    public NetworkManagementLoadTests(ITestOutputHelper output)
    {
        _output = output;
    }

    [Fact]
    public async Task NetworkPerformanceUpdates_HighFrequency_ShouldHandleLoad()
    {
        // Arrange
        await SeedTestDataAsync(carrierCount: 10, vehiclesPerCarrier: 20, driversPerCarrier: 15);
        await SeedNetworkDataAsync();
        
        var networkIds = await GetNetworkIdsAsync();
        var baseUrl = Client.BaseAddress!.ToString();

        // Create scenario for network performance updates
        var scenario = Scenario.Create("network_performance_updates", async context =>
        {
            var networkId = Faker.PickRandom(networkIds);
            var performanceData = GenerateNetworkPerformanceData();
            
            var request = Http.CreateRequest("PUT", $"{baseUrl}api/networks/{networkId}/performance")
                .WithHeader("Content-Type", "application/json")
                .WithBody(new StringContent(JsonSerializer.Serialize(performanceData)));

            var response = await Http.Send(Client, request);
            return response;
        })
        .WithLoadSimulations(
            Simulation.InjectPerSec(rate: 50, during: TimeSpan.FromMinutes(2)),
            Simulation.KeepConstant(copies: 25, during: TimeSpan.FromMinutes(3))
        );

        // Act & Assert
        var stats = NBomberRunner
            .RegisterScenarios(scenario)
            .WithReportFolder("performance-reports")
            .WithReportFormats(ReportFormat.Html, ReportFormat.Csv)
            .Run();

        // Verify performance criteria
        var scnStats = stats.AllScenarioStats.First();
        Assert.True(scnStats.Ok.Request.Mean < TimeSpan.FromMilliseconds(300), 
            $"Mean response time {scnStats.Ok.Request.Mean.TotalMilliseconds}ms exceeds 300ms threshold");
        Assert.True(scnStats.Ok.Request.Count > scnStats.Fail.Request.Count * 10, 
            "Success rate should be at least 90%");

        _output.WriteLine($"Network Performance Updates - Mean: {scnStats.Ok.Request.Mean.TotalMilliseconds}ms, " +
                         $"Success Rate: {(double)scnStats.Ok.Request.Count / (scnStats.Ok.Request.Count + scnStats.Fail.Request.Count) * 100:F2}%");
    }

    [Fact]
    public async Task NetworkAnalytics_ConcurrentQueries_ShouldHandleLoad()
    {
        // Arrange
        await SeedTestDataAsync(carrierCount: 15, vehiclesPerCarrier: 30, driversPerCarrier: 20);
        await SeedNetworkDataAsync();
        
        var networkIds = await GetNetworkIdsAsync();
        var baseUrl = Client.BaseAddress!.ToString();

        // Create scenario for network analytics queries
        var scenario = Scenario.Create("network_analytics_queries", async context =>
        {
            var networkId = Faker.PickRandom(networkIds);
            var fromDate = DateTime.UtcNow.AddDays(-30).ToString("yyyy-MM-dd");
            var toDate = DateTime.UtcNow.ToString("yyyy-MM-dd");
            
            var request = Http.CreateRequest("GET", $"{baseUrl}api/networks/{networkId}/analytics?from={fromDate}&to={toDate}")
                .WithHeader("Accept", "application/json");

            var response = await Http.Send(Client, request);
            return response;
        })
        .WithLoadSimulations(
            Simulation.InjectPerSec(rate: 15, during: TimeSpan.FromMinutes(2)),
            Simulation.KeepConstant(copies: 50, during: TimeSpan.FromMinutes(3))
        );

        // Act & Assert
        var stats = NBomberRunner
            .RegisterScenarios(scenario)
            .WithReportFolder("performance-reports")
            .WithReportFormats(ReportFormat.Html, ReportFormat.Csv)
            .Run();

        // Verify performance criteria
        var scnStats = stats.AllScenarioStats.First();
        Assert.True(scnStats.Ok.Request.Mean < TimeSpan.FromSeconds(3), 
            $"Mean response time {scnStats.Ok.Request.Mean.TotalMilliseconds}ms exceeds 3000ms threshold");
        Assert.True(scnStats.Ok.Request.Count > scnStats.Fail.Request.Count * 20, 
            "Success rate should be at least 95%");

        _output.WriteLine($"Network Analytics Queries - Mean: {scnStats.Ok.Request.Mean.TotalMilliseconds}ms, " +
                         $"Success Rate: {(double)scnStats.Ok.Request.Count / (scnStats.Ok.Request.Count + scnStats.Fail.Request.Count) * 100:F2}%");
    }

    [Fact]
    public async Task NetworkOperations_MixedWorkload_ShouldHandleLoad()
    {
        // Arrange
        await SeedTestDataAsync(carrierCount: 10, vehiclesPerCarrier: 25, driversPerCarrier: 20);
        await SeedNetworkDataAsync();
        
        var networkIds = await GetNetworkIdsAsync();
        var carrierIds = await GetCarrierIdsAsync();
        var baseUrl = Client.BaseAddress!.ToString();

        // Create scenario for network creation
        var createNetworkScenario = Scenario.Create("create_network", async context =>
        {
            var carrierId = Faker.PickRandom(carrierIds);
            var networkData = GenerateNetworkCreationData(carrierId);
            
            var request = Http.CreateRequest("POST", $"{baseUrl}api/networks")
                .WithHeader("Content-Type", "application/json")
                .WithBody(new StringContent(JsonSerializer.Serialize(networkData)));

            var response = await Http.Send(Client, request);
            return response;
        })
        .WithWeight(10)
        .WithLoadSimulations(
            Simulation.InjectPerSec(rate: 2, during: TimeSpan.FromMinutes(3))
        );

        // Create scenario for network status updates
        var updateNetworkScenario = Scenario.Create("update_network_status", async context =>
        {
            var networkId = Faker.PickRandom(networkIds);
            var statusData = GenerateNetworkStatusUpdate();
            
            var request = Http.CreateRequest("PUT", $"{baseUrl}api/networks/{networkId}/status")
                .WithHeader("Content-Type", "application/json")
                .WithBody(new StringContent(JsonSerializer.Serialize(statusData)));

            var response = await Http.Send(Client, request);
            return response;
        })
        .WithWeight(30)
        .WithLoadSimulations(
            Simulation.InjectPerSec(rate: 10, during: TimeSpan.FromMinutes(3))
        );

        // Create scenario for network queries
        var queryNetworkScenario = Scenario.Create("query_networks", async context =>
        {
            var carrierId = Faker.PickRandom(carrierIds);
            
            var request = Http.CreateRequest("GET", $"{baseUrl}api/networks?carrierId={carrierId}")
                .WithHeader("Accept", "application/json");

            var response = await Http.Send(Client, request);
            return response;
        })
        .WithWeight(60)
        .WithLoadSimulations(
            Simulation.InjectPerSec(rate: 20, during: TimeSpan.FromMinutes(3))
        );

        // Act & Assert
        var stats = NBomberRunner
            .RegisterScenarios(createNetworkScenario, updateNetworkScenario, queryNetworkScenario)
            .WithReportFolder("performance-reports")
            .WithReportFormats(ReportFormat.Html, ReportFormat.Csv)
            .Run();

        // Verify performance criteria for all scenarios
        foreach (var scnStats in stats.AllScenarioStats)
        {
            var maxResponseTime = scnStats.ScenarioName switch
            {
                "create_network" => TimeSpan.FromSeconds(2),
                "update_network_status" => TimeSpan.FromMilliseconds(500),
                "query_networks" => TimeSpan.FromSeconds(1),
                _ => TimeSpan.FromSeconds(1)
            };

            Assert.True(scnStats.Ok.Request.Mean < maxResponseTime, 
                $"Scenario {scnStats.ScenarioName} mean response time {scnStats.Ok.Request.Mean.TotalMilliseconds}ms exceeds {maxResponseTime.TotalMilliseconds}ms threshold");
            Assert.True(scnStats.Ok.Request.Count > scnStats.Fail.Request.Count * 10, 
                $"Scenario {scnStats.ScenarioName} success rate should be at least 90%");

            _output.WriteLine($"{scnStats.ScenarioName} - Mean: {scnStats.Ok.Request.Mean.TotalMilliseconds}ms, " +
                             $"Success Rate: {(double)scnStats.Ok.Request.Count / (scnStats.Ok.Request.Count + scnStats.Fail.Request.Count) * 100:F2}%");
        }
    }

    // Helper methods
    private async Task SeedNetworkDataAsync()
    {
        using var scope = Factory.Services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<NetworkFleetDbContext>();

        var carriers = await context.Carriers.ToListAsync();
        var networks = new List<BrokerCarrierNetwork>();

        foreach (var carrier in carriers.Take(5)) // Create networks for first 5 carriers
        {
            for (int i = 0; i < 3; i++) // 3 networks per carrier
            {
                var network = new BrokerCarrierNetwork(
                    Guid.NewGuid(), // Broker ID
                    carrier.Id,
                    Faker.Random.Double(0.1, 0.3), // Commission rate
                    Faker.Random.Int(1, 10) // Priority
                );
                
                networks.Add(network);
            }
        }

        await context.BrokerCarrierNetworks.AddRangeAsync(networks);
        await context.SaveChangesAsync();
    }

    private async Task<List<Guid>> GetNetworkIdsAsync()
    {
        using var scope = Factory.Services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<NetworkFleetDbContext>();
        return await context.BrokerCarrierNetworks.Select(n => n.Id).ToListAsync();
    }

    private async Task<List<Guid>> GetCarrierIdsAsync()
    {
        using var scope = Factory.Services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<NetworkFleetDbContext>();
        return await context.Carriers.Select(c => c.Id).ToListAsync();
    }

    private Dictionary<string, object> GenerateNetworkPerformanceData()
    {
        return new Dictionary<string, object>
        {
            ["responseTime"] = Faker.Random.Double(50, 500),
            ["throughput"] = Faker.Random.Double(100, 1000),
            ["errorRate"] = Faker.Random.Double(0, 0.05),
            ["availability"] = Faker.Random.Double(0.95, 1.0),
            ["timestamp"] = DateTime.UtcNow
        };
    }

    private Dictionary<string, object> GenerateNetworkCreationData(Guid carrierId)
    {
        return new Dictionary<string, object>
        {
            ["brokerId"] = Guid.NewGuid(),
            ["carrierId"] = carrierId,
            ["commissionRate"] = Faker.Random.Double(0.1, 0.3),
            ["priority"] = Faker.Random.Int(1, 10),
            ["contractTerms"] = Faker.Lorem.Paragraph()
        };
    }

    private Dictionary<string, object> GenerateNetworkStatusUpdate()
    {
        var statuses = new[] { "active", "suspended", "pending", "terminated" };
        return new Dictionary<string, object>
        {
            ["status"] = Faker.PickRandom(statuses),
            ["reason"] = Faker.Lorem.Sentence(),
            ["timestamp"] = DateTime.UtcNow
        };
    }
}
