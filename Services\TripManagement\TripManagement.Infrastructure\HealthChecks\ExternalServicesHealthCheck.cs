using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using System.Net.Http;

namespace TripManagement.Infrastructure.HealthChecks;

public class ExternalServicesHealthCheck : IHealthCheck
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<ExternalServicesHealthCheck> _logger;
    private readonly IConfiguration _configuration;

    public ExternalServicesHealthCheck(
        HttpClient httpClient, 
        ILogger<ExternalServicesHealthCheck> logger,
        IConfiguration configuration)
    {
        _httpClient = httpClient;
        _logger = logger;
        _configuration = configuration;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        var healthData = new Dictionary<string, object>();
        var isHealthy = true;
        var errors = new List<string>();

        // Check Order Management Service
        try
        {
            var orderServiceUrl = _configuration["ExternalServices:OrderManagement:BaseUrl"];
            if (!string.IsNullOrEmpty(orderServiceUrl))
            {
                var response = await _httpClient.GetAsync($"{orderServiceUrl}/health", cancellationToken);
                healthData["orderManagementService"] = response.IsSuccessStatusCode ? "Healthy" : "Unhealthy";
                
                if (!response.IsSuccessStatusCode)
                {
                    isHealthy = false;
                    errors.Add($"Order Management Service returned {response.StatusCode}");
                }
            }
        }
        catch (Exception ex)
        {
            healthData["orderManagementService"] = "Unreachable";
            isHealthy = false;
            errors.Add($"Order Management Service: {ex.Message}");
            _logger.LogWarning(ex, "Failed to check Order Management Service health");
        }

        // Check User Management Service
        try
        {
            var userServiceUrl = _configuration["ExternalServices:UserManagement:BaseUrl"];
            if (!string.IsNullOrEmpty(userServiceUrl))
            {
                var response = await _httpClient.GetAsync($"{userServiceUrl}/health", cancellationToken);
                healthData["userManagementService"] = response.IsSuccessStatusCode ? "Healthy" : "Unhealthy";
                
                if (!response.IsSuccessStatusCode)
                {
                    isHealthy = false;
                    errors.Add($"User Management Service returned {response.StatusCode}");
                }
            }
        }
        catch (Exception ex)
        {
            healthData["userManagementService"] = "Unreachable";
            isHealthy = false;
            errors.Add($"User Management Service: {ex.Message}");
            _logger.LogWarning(ex, "Failed to check User Management Service health");
        }

        // Check Communication Service
        try
        {
            var commServiceUrl = _configuration["ExternalServices:Communication:BaseUrl"];
            if (!string.IsNullOrEmpty(commServiceUrl))
            {
                var response = await _httpClient.GetAsync($"{commServiceUrl}/health", cancellationToken);
                healthData["communicationService"] = response.IsSuccessStatusCode ? "Healthy" : "Unhealthy";
                
                if (!response.IsSuccessStatusCode)
                {
                    isHealthy = false;
                    errors.Add($"Communication Service returned {response.StatusCode}");
                }
            }
        }
        catch (Exception ex)
        {
            healthData["communicationService"] = "Unreachable";
            isHealthy = false;
            errors.Add($"Communication Service: {ex.Message}");
            _logger.LogWarning(ex, "Failed to check Communication Service health");
        }

        if (errors.Any())
        {
            healthData["errors"] = errors;
        }

        return isHealthy 
            ? HealthCheckResult.Healthy("All external services are healthy", healthData)
            : HealthCheckResult.Degraded("Some external services are unhealthy", null, healthData);
    }
}
