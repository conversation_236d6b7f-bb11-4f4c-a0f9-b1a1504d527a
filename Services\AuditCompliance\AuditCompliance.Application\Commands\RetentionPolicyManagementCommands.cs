using AuditCompliance.Application.DTOs;
using AuditCompliance.Domain.Entities;
using AuditCompliance.Domain.Enums;
using AuditCompliance.Domain.ValueObjects;
using MediatR;

namespace AuditCompliance.Application.Commands
{
    /// <summary>
    /// Create retention policy management command
    /// </summary>
    public class CreateRetentionPolicyCommand : IRequest<RetentionPolicyManagementDto>
    {
        public string PolicyName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public RetentionPolicyDto RetentionPolicy { get; set; } = new();
        public List<ComplianceStandard> ApplicableStandards { get; set; } = new();
        public List<string> ApplicableEntityTypes { get; set; } = new();
        public List<string> ApplicableModules { get; set; } = new();
        public PolicyPriority Priority { get; set; } = PolicyPriority.Medium;
        public bool RequiresApproval { get; set; } = true;
        public List<string> Tags { get; set; } = new();
        public Dictionary<string, object> Configuration { get; set; } = new();
        
        // Audit fields
        public Guid CreatedBy { get; set; }
        public string CreatedByName { get; set; } = string.Empty;
    }

    /// <summary>
    /// Update retention policy management command
    /// </summary>
    public class UpdateRetentionPolicyCommand : IRequest<RetentionPolicyManagementDto>
    {
        public Guid PolicyId { get; set; }
        public string PolicyName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public RetentionPolicyDto RetentionPolicy { get; set; } = new();
        public List<ComplianceStandard> ApplicableStandards { get; set; } = new();
        public List<string> ApplicableEntityTypes { get; set; } = new();
        public List<string> ApplicableModules { get; set; } = new();
        public PolicyPriority Priority { get; set; } = PolicyPriority.Medium;
        public List<string> Tags { get; set; } = new();
        public Dictionary<string, object> Configuration { get; set; } = new();
        
        // Audit fields
        public Guid UpdatedBy { get; set; }
    }

    /// <summary>
    /// Delete retention policy command
    /// </summary>
    public class DeleteRetentionPolicyCommand : IRequest<bool>
    {
        public Guid PolicyId { get; set; }
        public Guid DeletedBy { get; set; }
        public string? Reason { get; set; }
    }

    /// <summary>
    /// Approve/Reject retention policy command
    /// </summary>
    public class ApproveRetentionPolicyCommand : IRequest<bool>
    {
        public Guid PolicyId { get; set; }
        public bool Approve { get; set; }
        public string? Notes { get; set; }
        public string? RejectionReason { get; set; }
        public Guid ApprovedBy { get; set; }
        public string ApprovedByName { get; set; } = string.Empty;
    }

    /// <summary>
    /// Activate/Deactivate retention policy command
    /// </summary>
    public class ToggleRetentionPolicyStatusCommand : IRequest<bool>
    {
        public Guid PolicyId { get; set; }
        public bool IsActive { get; set; }
        public string? Reason { get; set; }
        public Guid UpdatedBy { get; set; }
    }

    /// <summary>
    /// Execute retention policy command
    /// </summary>
    public class ExecuteRetentionPolicyCommand : IRequest<PolicyExecutionDto>
    {
        public Guid PolicyId { get; set; }
        public bool DryRun { get; set; } = false;
        public Guid ExecutedBy { get; set; }
        public string? ExecutionNotes { get; set; }
    }

    /// <summary>
    /// Analyze policy impact command
    /// </summary>
    public class AnalyzePolicyImpactCommand : IRequest<PolicyImpactAnalysisDto>
    {
        public Guid PolicyId { get; set; }
        public bool IncludeDetailedBreakdown { get; set; } = true;
        public Guid AnalyzedBy { get; set; }
    }

    /// <summary>
    /// Validate retention policy command
    /// </summary>
    public class ValidateRetentionPolicyCommand : IRequest<PolicyValidationResultDto>
    {
        public Guid? PolicyId { get; set; } // Null for new policies
        public string PolicyName { get; set; } = string.Empty;
        public RetentionPolicyDto RetentionPolicy { get; set; } = new();
        public List<ComplianceStandard> ApplicableStandards { get; set; } = new();
        public List<string> ApplicableEntityTypes { get; set; } = new();
        public List<string> ApplicableModules { get; set; } = new();
        public bool PerformImpactAnalysis { get; set; } = true;
        public bool CheckForConflicts { get; set; } = true;
    }

    /// <summary>
    /// Get retention status dashboard command
    /// </summary>
    public class GetRetentionStatusDashboardCommand : IRequest<RetentionStatusDashboardDto>
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public bool IncludeTrends { get; set; } = true;
        public bool IncludeAlerts { get; set; } = true;
        public Guid RequestedBy { get; set; }
    }

    /// <summary>
    /// Schedule policy execution command
    /// </summary>
    public class SchedulePolicyExecutionCommand : IRequest<string>
    {
        public Guid PolicyId { get; set; }
        public string CronExpression { get; set; } = string.Empty;
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string ScheduleName { get; set; } = string.Empty;
        public string? Description { get; set; }
        public bool IsActive { get; set; } = true;
        public Dictionary<string, object> ScheduleConfiguration { get; set; } = new();
        
        // Audit fields
        public Guid ScheduledBy { get; set; }
        public string ScheduledByName { get; set; } = string.Empty;
    }

    /// <summary>
    /// Update policy schedule command
    /// </summary>
    public class UpdatePolicyScheduleCommand : IRequest<bool>
    {
        public string ScheduleId { get; set; } = string.Empty;
        public string? CronExpression { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? ScheduleName { get; set; }
        public string? Description { get; set; }
        public bool? IsActive { get; set; }
        public Dictionary<string, object>? ScheduleConfiguration { get; set; }
        
        // Audit fields
        public Guid UpdatedBy { get; set; }
    }

    /// <summary>
    /// Cancel policy schedule command
    /// </summary>
    public class CancelPolicyScheduleCommand : IRequest<bool>
    {
        public string ScheduleId { get; set; } = string.Empty;
        public Guid CancelledBy { get; set; }
        public string? Reason { get; set; }
    }

    /// <summary>
    /// Bulk execute retention policies command
    /// </summary>
    public class BulkExecuteRetentionPoliciesCommand : IRequest<List<PolicyExecutionDto>>
    {
        public List<Guid> PolicyIds { get; set; } = new();
        public bool DryRun { get; set; } = false;
        public bool ExecuteInParallel { get; set; } = false;
        public int MaxConcurrency { get; set; } = 3;
        public Guid ExecutedBy { get; set; }
        public string? ExecutionNotes { get; set; }
    }

    /// <summary>
    /// Export retention policies command
    /// </summary>
    public class ExportRetentionPoliciesCommand : IRequest<PolicyExportDto>
    {
        public List<Guid> PolicyIds { get; set; } = new();
        public ExportFormat Format { get; set; } = ExportFormat.JSON;
        public bool IncludeExecutionHistory { get; set; } = false;
        public bool IncludeConfiguration { get; set; } = true;
        public Guid ExportedBy { get; set; }
    }

    /// <summary>
    /// Import retention policies command
    /// </summary>
    public class ImportRetentionPoliciesCommand : IRequest<PolicyImportResultDto>
    {
        public string ImportData { get; set; } = string.Empty;
        public ExportFormat Format { get; set; } = ExportFormat.JSON;
        public bool OverwriteExisting { get; set; } = false;
        public bool ValidateBeforeImport { get; set; } = true;
        public Guid ImportedBy { get; set; }
        public string ImportedByName { get; set; } = string.Empty;
    }

    /// <summary>
    /// Acknowledge retention alert command
    /// </summary>
    public class AcknowledgeRetentionAlertCommand : IRequest<bool>
    {
        public string AlertId { get; set; } = string.Empty;
        public Guid AcknowledgedBy { get; set; }
        public string? AcknowledgmentNotes { get; set; }
    }

    /// <summary>
    /// Create retention alert command
    /// </summary>
    public class CreateRetentionAlertCommand : IRequest<RetentionAlertDto>
    {
        public RetentionAlertType Type { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public RetentionAlertSeverity Severity { get; set; }
        public Guid? PolicyId { get; set; }
        public string? PolicyName { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
        public Guid CreatedBy { get; set; }
    }

    /// <summary>
    /// Policy export DTO
    /// </summary>
    public class PolicyExportDto
    {
        public string ExportId { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public ExportFormat Format { get; set; }
        public DateTime ExportedAt { get; set; }
        public Guid ExportedBy { get; set; }
        public int PolicyCount { get; set; }
        public Dictionary<string, object> ExportMetadata { get; set; } = new();
    }

    /// <summary>
    /// Policy import result DTO
    /// </summary>
    public class PolicyImportResultDto
    {
        public string ImportId { get; set; } = string.Empty;
        public bool IsSuccessful { get; set; }
        public int TotalPolicies { get; set; }
        public int ImportedPolicies { get; set; }
        public int SkippedPolicies { get; set; }
        public int FailedPolicies { get; set; }
        public List<PolicyImportErrorDto> Errors { get; set; } = new();
        public List<PolicyImportWarningDto> Warnings { get; set; } = new();
        public DateTime ImportedAt { get; set; }
        public Guid ImportedBy { get; set; }
        public Dictionary<string, object> ImportMetadata { get; set; } = new();
    }

    /// <summary>
    /// Policy import error DTO
    /// </summary>
    public class PolicyImportErrorDto
    {
        public string PolicyName { get; set; } = string.Empty;
        public string ErrorCode { get; set; } = string.Empty;
        public string ErrorMessage { get; set; } = string.Empty;
        public int? LineNumber { get; set; }
    }

    /// <summary>
    /// Policy import warning DTO
    /// </summary>
    public class PolicyImportWarningDto
    {
        public string PolicyName { get; set; } = string.Empty;
        public string WarningCode { get; set; } = string.Empty;
        public string WarningMessage { get; set; } = string.Empty;
        public string? Recommendation { get; set; }
    }
}
