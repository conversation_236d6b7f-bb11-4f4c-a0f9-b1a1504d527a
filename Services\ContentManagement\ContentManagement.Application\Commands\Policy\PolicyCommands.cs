using ContentManagement.Application.DTOs;
using ContentManagement.Domain.Enums;
using MediatR;

namespace ContentManagement.Application.Commands.Policy;

/// <summary>
/// Command to create a new policy document
/// </summary>
public class CreatePolicyDocumentCommand : IRequest<PolicyDocumentDto>
{
    public PolicyType PolicyType { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public string ContentBody { get; set; } = string.Empty;
    public DateTime EffectiveDate { get; set; }
    public DateTime? ExpiryDate { get; set; }
    public bool RequiresAcceptance { get; set; } = false;
    public List<string>? ApplicableRoles { get; set; }
    public string? LegalReferences { get; set; }
    public string? Description { get; set; }
    public ContentVisibilityDto? Visibility { get; set; }
    public List<string>? Tags { get; set; }
    
    // User context
    public Guid CreatedBy { get; set; }
    public string CreatedByName { get; set; } = string.Empty;
}

/// <summary>
/// Command to update an existing policy document
/// </summary>
public class UpdatePolicyDocumentCommand : IRequest<PolicyDocumentDto>
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string ContentBody { get; set; } = string.Empty;
    public DateTime EffectiveDate { get; set; }
    public DateTime? ExpiryDate { get; set; }
    public bool RequiresAcceptance { get; set; } = false;
    public List<string>? ApplicableRoles { get; set; }
    public string? LegalReferences { get; set; }
    public string? Description { get; set; }
    public string? ChangeDescription { get; set; }
    
    // User context
    public Guid UpdatedBy { get; set; }
    public string UpdatedByName { get; set; } = string.Empty;
}

/// <summary>
/// Command to create a new version of a policy document
/// </summary>
public class CreatePolicyVersionCommand : IRequest<PolicyDocumentDto>
{
    public Guid PolicyId { get; set; }
    public string NewVersion { get; set; } = string.Empty;
    public string ContentBody { get; set; } = string.Empty;
    public DateTime EffectiveDate { get; set; }
    public DateTime? ExpiryDate { get; set; }
    public string? ChangeDescription { get; set; }
    
    // User context
    public Guid CreatedBy { get; set; }
    public string CreatedByName { get; set; } = string.Empty;
}

/// <summary>
/// Command to mark a policy version as current
/// </summary>
public class ActivatePolicyVersionCommand : IRequest<PolicyDocumentDto>
{
    public Guid Id { get; set; }
    
    // User context
    public Guid UpdatedBy { get; set; }
    public string UpdatedByName { get; set; } = string.Empty;
}

/// <summary>
/// Command to mark a policy version as obsolete
/// </summary>
public class DeactivatePolicyVersionCommand : IRequest<PolicyDocumentDto>
{
    public Guid Id { get; set; }
    
    // User context
    public Guid UpdatedBy { get; set; }
    public string UpdatedByName { get; set; } = string.Empty;
}

/// <summary>
/// Command to record policy acceptance by a user
/// </summary>
public class AcceptPolicyCommand : IRequest<PolicyAcceptanceDto>
{
    public Guid PolicyId { get; set; }
    public Guid UserId { get; set; }
    public string UserName { get; set; } = string.Empty;
    public string UserRole { get; set; } = string.Empty;
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
}

/// <summary>
/// Command to bulk accept multiple policies for a user
/// </summary>
public class BulkAcceptPoliciesCommand : IRequest<List<PolicyAcceptanceDto>>
{
    public List<Guid> PolicyIds { get; set; } = new();
    public Guid UserId { get; set; }
    public string UserName { get; set; } = string.Empty;
    public string UserRole { get; set; } = string.Empty;
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
}
