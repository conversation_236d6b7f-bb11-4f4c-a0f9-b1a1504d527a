using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Domain.Entities;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace CommunicationNotification.Infrastructure.Controllers;

/// <summary>
/// Advanced scheduling API controller for intelligent message scheduling
/// </summary>
[ApiController]
[Route("api/communication/scheduling")]
[Authorize]
public class AdvancedSchedulingController : ControllerBase
{
    private readonly IAdvancedSchedulingService _schedulingService;
    private readonly IUserBehaviorAnalyticsService _behaviorAnalyticsService;
    private readonly ISchedulingOptimizationService _optimizationService;

    public AdvancedSchedulingController(
        IAdvancedSchedulingService schedulingService,
        IUserBehaviorAnalyticsService behaviorAnalyticsService,
        ISchedulingOptimizationService optimizationService)
    {
        _schedulingService = schedulingService;
        _behaviorAnalyticsService = behaviorAnalyticsService;
        _optimizationService = optimizationService;
    }

    /// <summary>
    /// Create a new message schedule
    /// </summary>
    [HttpPost("schedules")]
    public async Task<IActionResult> CreateSchedule([FromBody] CreateScheduleRequest request)
    {
        var userId = GetCurrentUserId();
        if (userId == Guid.Empty)
        {
            return Unauthorized("User ID not found");
        }

        var schedule = await _schedulingService.CreateScheduleAsync(
            request.Name,
            request.Description,
            request.MessageType,
            request.ScheduleType,
            request.Content,
            userId,
            request.ScheduledAt,
            request.RecurrencePattern,
            request.Rules,
            request.DeliveryWindow,
            request.OptimizationSettings,
            request.TargetUserIds,
            request.TargetSegments,
            request.TemplateId,
            request.TemplateParameters,
            request.PreferredChannel,
            request.Priority,
            request.TimeZones,
            request.MaxExecutions,
            request.ExpiresAt);

        return Ok(schedule);
    }

    /// <summary>
    /// Get schedule by ID
    /// </summary>
    [HttpGet("schedules/{scheduleId}")]
    public async Task<IActionResult> GetSchedule(Guid scheduleId)
    {
        var schedule = await _schedulingService.GetScheduleAsync(scheduleId);
        if (schedule == null)
        {
            return NotFound($"Schedule {scheduleId} not found");
        }

        return Ok(schedule);
    }

    /// <summary>
    /// Get all schedules
    /// </summary>
    [HttpGet("schedules")]
    public async Task<IActionResult> GetAllSchedules()
    {
        var schedules = await _schedulingService.GetAllSchedulesAsync();
        return Ok(schedules);
    }

    /// <summary>
    /// Get schedules by status
    /// </summary>
    [HttpGet("schedules/status/{status}")]
    public async Task<IActionResult> GetSchedulesByStatus(ScheduleStatus status)
    {
        var schedules = await _schedulingService.GetSchedulesByStatusAsync(status);
        return Ok(schedules);
    }

    /// <summary>
    /// Get schedules ready for execution
    /// </summary>
    [HttpGet("schedules/ready")]
    public async Task<IActionResult> GetSchedulesReadyForExecution()
    {
        var schedules = await _schedulingService.GetSchedulesReadyForExecutionAsync();
        return Ok(schedules);
    }

    /// <summary>
    /// Update schedule
    /// </summary>
    [HttpPut("schedules/{scheduleId}")]
    public async Task<IActionResult> UpdateSchedule(Guid scheduleId, [FromBody] MessageSchedule updatedSchedule)
    {
        var schedule = await _schedulingService.UpdateScheduleAsync(scheduleId, updatedSchedule);
        return Ok(schedule);
    }

    /// <summary>
    /// Delete schedule
    /// </summary>
    [HttpDelete("schedules/{scheduleId}")]
    public async Task<IActionResult> DeleteSchedule(Guid scheduleId)
    {
        await _schedulingService.DeleteScheduleAsync(scheduleId);
        return NoContent();
    }

    /// <summary>
    /// Activate schedule
    /// </summary>
    [HttpPost("schedules/{scheduleId}/activate")]
    public async Task<IActionResult> ActivateSchedule(Guid scheduleId)
    {
        var schedule = await _schedulingService.ActivateScheduleAsync(scheduleId);
        return Ok(schedule);
    }

    /// <summary>
    /// Pause schedule
    /// </summary>
    [HttpPost("schedules/{scheduleId}/pause")]
    public async Task<IActionResult> PauseSchedule(Guid scheduleId)
    {
        var schedule = await _schedulingService.PauseScheduleAsync(scheduleId);
        return Ok(schedule);
    }

    /// <summary>
    /// Cancel schedule
    /// </summary>
    [HttpPost("schedules/{scheduleId}/cancel")]
    public async Task<IActionResult> CancelSchedule(Guid scheduleId)
    {
        var schedule = await _schedulingService.CancelScheduleAsync(scheduleId);
        return Ok(schedule);
    }

    /// <summary>
    /// Execute schedule immediately
    /// </summary>
    [HttpPost("schedules/{scheduleId}/execute")]
    public async Task<IActionResult> ExecuteSchedule(Guid scheduleId, [FromBody] ExecuteScheduleRequest? request = null)
    {
        var forceExecution = request?.ForceExecution ?? false;
        var result = await _schedulingService.ExecuteScheduleAsync(scheduleId, forceExecution);

        if (result.IsSuccess)
        {
            return Ok(result);
        }
        else
        {
            return BadRequest(result);
        }
    }

    /// <summary>
    /// Get optimal delivery time for user
    /// </summary>
    [HttpGet("schedules/{scheduleId}/optimal-time")]
    public async Task<IActionResult> GetOptimalDeliveryTime(
        Guid scheduleId,
        [FromQuery] string userId,
        [FromQuery] string timeZone = "UTC")
    {
        var optimalTime = await _schedulingService.GetOptimalDeliveryTimeAsync(scheduleId, userId, timeZone);
        return Ok(new { optimalTime });
    }

    /// <summary>
    /// Get schedule analytics
    /// </summary>
    [HttpGet("schedules/{scheduleId}/analytics")]
    public async Task<IActionResult> GetScheduleAnalytics(
        Guid scheduleId,
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null)
    {
        var analytics = await _schedulingService.GetScheduleAnalyticsAsync(scheduleId, startDate, endDate);
        return Ok(analytics);
    }

    /// <summary>
    /// Get optimization recommendations
    /// </summary>
    [HttpGet("schedules/{scheduleId}/recommendations")]
    public async Task<IActionResult> GetOptimizationRecommendations(Guid scheduleId)
    {
        var recommendations = await _schedulingService.GetOptimizationRecommendationsAsync(scheduleId);
        return Ok(recommendations);
    }

    /// <summary>
    /// Get user engagement pattern
    /// </summary>
    [HttpGet("analytics/users/{userId}/engagement")]
    public async Task<IActionResult> GetUserEngagementPattern(
        string userId,
        [FromQuery] int lookbackDays = 30)
    {
        var pattern = await _behaviorAnalyticsService.GetUserEngagementPatternAsync(
            userId, TimeSpan.FromDays(lookbackDays));
        return Ok(pattern);
    }

    /// <summary>
    /// Get user's optimal send time
    /// </summary>
    [HttpGet("analytics/users/{userId}/optimal-time")]
    public async Task<IActionResult> GetUserOptimalSendTime(
        string userId,
        [FromQuery] DateTime? baseTime = null,
        [FromQuery] string timeZone = "UTC")
    {
        var optimalTime = await _behaviorAnalyticsService.GetOptimalSendTimeAsync(
            userId, baseTime ?? DateTime.UtcNow, timeZone);
        return Ok(new { optimalTime });
    }

    /// <summary>
    /// Get user's preferred channel
    /// </summary>
    [HttpGet("analytics/users/{userId}/preferred-channel")]
    public async Task<IActionResult> GetUserPreferredChannel(
        string userId,
        [FromQuery] MessageType messageType = MessageType.TripUpdate)
    {
        var preferredChannel = await _behaviorAnalyticsService.GetPreferredChannelAsync(userId, messageType);
        return Ok(new { preferredChannel });
    }

    /// <summary>
    /// Predict user response probability
    /// </summary>
    [HttpPost("analytics/users/{userId}/predict-response")]
    public async Task<IActionResult> PredictResponseProbability(
        string userId,
        [FromBody] PredictResponseRequest request)
    {
        var probability = await _behaviorAnalyticsService.PredictResponseProbabilityAsync(
            userId, request.MessageType, request.Channel, request.ProposedSendTime);
        return Ok(new { probability });
    }

    /// <summary>
    /// Optimize delivery times for schedule
    /// </summary>
    [HttpPost("schedules/{scheduleId}/optimize/delivery-times")]
    public async Task<IActionResult> OptimizeDeliveryTimes(
        Guid scheduleId,
        [FromBody] OptimizeDeliveryRequest request)
    {
        var schedule = await _schedulingService.GetScheduleAsync(scheduleId);
        if (schedule == null)
        {
            return NotFound($"Schedule {scheduleId} not found");
        }

        var optimizedDeliveries = await _optimizationService.OptimizeDeliveryTimesAsync(
            schedule, request.TargetUserIds);
        return Ok(optimizedDeliveries);
    }

    /// <summary>
    /// Optimize channel selection for schedule
    /// </summary>
    [HttpPost("schedules/{scheduleId}/optimize/channels")]
    public async Task<IActionResult> OptimizeChannelSelection(
        Guid scheduleId,
        [FromBody] OptimizeChannelRequest request)
    {
        var schedule = await _schedulingService.GetScheduleAsync(scheduleId);
        if (schedule == null)
        {
            return NotFound($"Schedule {scheduleId} not found");
        }

        var optimizedChannels = await _optimizationService.OptimizeChannelSelectionAsync(
            schedule, request.TargetUserIds);
        return Ok(optimizedChannels);
    }

    /// <summary>
    /// Optimize frequency for schedule
    /// </summary>
    [HttpPost("schedules/{scheduleId}/optimize/frequency")]
    public async Task<IActionResult> OptimizeFrequency(
        Guid scheduleId,
        [FromBody] OptimizeFrequencyRequest request)
    {
        var schedule = await _schedulingService.GetScheduleAsync(scheduleId);
        if (schedule == null)
        {
            return NotFound($"Schedule {scheduleId} not found");
        }

        var frequencyOptimization = await _optimizationService.OptimizeFrequencyAsync(
            schedule, request.TargetUserIds);
        return Ok(frequencyOptimization);
    }

    /// <summary>
    /// Generate A/B test variants for schedule
    /// </summary>
    [HttpPost("schedules/{scheduleId}/ab-test/variants")]
    public async Task<IActionResult> GenerateABTestVariants(
        Guid scheduleId,
        [FromBody] GenerateVariantsRequest? request = null)
    {
        var schedule = await _schedulingService.GetScheduleAsync(scheduleId);
        if (schedule == null)
        {
            return NotFound($"Schedule {scheduleId} not found");
        }

        var variantCount = request?.VariantCount ?? 2;
        var variants = await _optimizationService.GenerateABTestVariantsAsync(schedule, variantCount);
        return Ok(variants);
    }

    /// <summary>
    /// Analyze schedule performance
    /// </summary>
    [HttpGet("schedules/{scheduleId}/performance")]
    public async Task<IActionResult> AnalyzeSchedulePerformance(
        Guid scheduleId,
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null)
    {
        var analysis = await _optimizationService.AnalyzeSchedulePerformanceAsync(scheduleId, startDate, endDate);
        return Ok(analysis);
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return Guid.TryParse(userIdClaim, out var userId) ? userId : Guid.Empty;
    }
}

// Request/Response DTOs
public class CreateScheduleRequest
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public MessageType MessageType { get; set; }
    public ScheduleType ScheduleType { get; set; }
    public MessageContent Content { get; set; } = MessageContent.Empty();
    public DateTime? ScheduledAt { get; set; }
    public RecurrencePattern? RecurrencePattern { get; set; }
    public List<SchedulingRule>? Rules { get; set; }
    public DeliveryWindow? DeliveryWindow { get; set; }
    public OptimizationSettings? OptimizationSettings { get; set; }
    public List<string>? TargetUserIds { get; set; }
    public List<string>? TargetSegments { get; set; }
    public string? TemplateId { get; set; }
    public Dictionary<string, object>? TemplateParameters { get; set; }
    public NotificationChannel? PreferredChannel { get; set; }
    public Priority Priority { get; set; } = Priority.Normal;
    public List<string>? TimeZones { get; set; }
    public int MaxExecutions { get; set; } = int.MaxValue;
    public DateTime? ExpiresAt { get; set; }
}

public class ExecuteScheduleRequest
{
    public bool ForceExecution { get; set; } = false;
}

public class PredictResponseRequest
{
    public MessageType MessageType { get; set; }
    public NotificationChannel Channel { get; set; }
    public DateTime ProposedSendTime { get; set; }
}

public class OptimizeDeliveryRequest
{
    public List<string> TargetUserIds { get; set; } = new();
}

public class OptimizeChannelRequest
{
    public List<string> TargetUserIds { get; set; } = new();
}

public class OptimizeFrequencyRequest
{
    public List<string> TargetUserIds { get; set; } = new();
}

public class GenerateVariantsRequest
{
    public int VariantCount { get; set; } = 2;
}
