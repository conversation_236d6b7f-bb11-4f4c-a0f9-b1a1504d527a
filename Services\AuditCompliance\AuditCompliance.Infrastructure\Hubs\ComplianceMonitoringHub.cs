using Microsoft.AspNetCore.SignalR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using AuditCompliance.Application.Interfaces;
using AuditCompliance.Application.DTOs.Analytics;

namespace AuditCompliance.Infrastructure.Hubs;

/// <summary>
/// SignalR hub for real-time compliance monitoring and dashboard updates
/// </summary>
[Authorize]
public class ComplianceMonitoringHub : Hub
{
    private readonly ILogger<ComplianceMonitoringHub> _logger;
    private readonly IAdvancedAnalyticsService _analyticsService;

    public ComplianceMonitoringHub(
        ILogger<ComplianceMonitoringHub> logger,
        IAdvancedAnalyticsService analyticsService)
    {
        _logger = logger;
        _analyticsService = analyticsService;
    }

    /// <summary>
    /// Join organization-specific monitoring group
    /// </summary>
    public async Task JoinOrganizationGroup(string organizationId)
    {
        try
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, $"org_{organizationId}");
            _logger.LogInformation("User {UserId} joined organization group {OrganizationId}", 
                Context.UserIdentifier, organizationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error joining organization group {OrganizationId}", organizationId);
        }
    }

    /// <summary>
    /// Leave organization-specific monitoring group
    /// </summary>
    public async Task LeaveOrganizationGroup(string organizationId)
    {
        try
        {
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"org_{organizationId}");
            _logger.LogInformation("User {UserId} left organization group {OrganizationId}", 
                Context.UserIdentifier, organizationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error leaving organization group {OrganizationId}", organizationId);
        }
    }

    /// <summary>
    /// Join compliance dashboard monitoring
    /// </summary>
    public async Task JoinDashboardMonitoring()
    {
        try
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, "dashboard_monitoring");
            _logger.LogInformation("User {UserId} joined dashboard monitoring", Context.UserIdentifier);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error joining dashboard monitoring");
        }
    }

    /// <summary>
    /// Leave compliance dashboard monitoring
    /// </summary>
    public async Task LeaveDashboardMonitoring()
    {
        try
        {
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, "dashboard_monitoring");
            _logger.LogInformation("User {UserId} left dashboard monitoring", Context.UserIdentifier);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error leaving dashboard monitoring");
        }
    }

    /// <summary>
    /// Subscribe to specific entity monitoring
    /// </summary>
    public async Task SubscribeToEntity(string entityId, string entityType)
    {
        try
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, $"entity_{entityType}_{entityId}");
            _logger.LogInformation("User {UserId} subscribed to entity {EntityId} of type {EntityType}", 
                Context.UserIdentifier, entityId, entityType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error subscribing to entity {EntityId}", entityId);
        }
    }

    /// <summary>
    /// Unsubscribe from specific entity monitoring
    /// </summary>
    public async Task UnsubscribeFromEntity(string entityId, string entityType)
    {
        try
        {
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"entity_{entityType}_{entityId}");
            _logger.LogInformation("User {UserId} unsubscribed from entity {EntityId} of type {EntityType}", 
                Context.UserIdentifier, entityId, entityType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error unsubscribing from entity {EntityId}", entityId);
        }
    }

    /// <summary>
    /// Request current compliance status for dashboard
    /// </summary>
    public async Task RequestComplianceStatus(string? organizationId = null)
    {
        try
        {
            var orgId = string.IsNullOrEmpty(organizationId) ? null : Guid.Parse(organizationId);
            var insights = await _analyticsService.GenerateComplianceInsightsAsync(orgId);
            
            await Clients.Caller.SendAsync("ComplianceStatusUpdate", new
            {
                OrganizationId = organizationId,
                Overview = insights.Overview,
                KeyMetrics = insights.KeyMetrics,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error requesting compliance status");
            await Clients.Caller.SendAsync("Error", "Failed to retrieve compliance status");
        }
    }

    /// <summary>
    /// Request recent anomalies
    /// </summary>
    public async Task RequestRecentAnomalies(int count = 10)
    {
        try
        {
            var fromDate = DateTime.UtcNow.AddDays(-7);
            var toDate = DateTime.UtcNow;
            var anomalies = await _analyticsService.DetectComplianceAnomaliesAsync(fromDate, toDate);
            
            await Clients.Caller.SendAsync("RecentAnomalies", anomalies.Take(count));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error requesting recent anomalies");
            await Clients.Caller.SendAsync("Error", "Failed to retrieve recent anomalies");
        }
    }

    /// <summary>
    /// Handle client connection
    /// </summary>
    public override async Task OnConnectedAsync()
    {
        try
        {
            _logger.LogInformation("Client connected: {ConnectionId}, User: {UserId}", 
                Context.ConnectionId, Context.UserIdentifier);
            
            // Send welcome message with connection info
            await Clients.Caller.SendAsync("Connected", new
            {
                ConnectionId = Context.ConnectionId,
                UserId = Context.UserIdentifier,
                ConnectedAt = DateTime.UtcNow
            });
            
            await base.OnConnectedAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling client connection");
        }
    }

    /// <summary>
    /// Handle client disconnection
    /// </summary>
    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        try
        {
            _logger.LogInformation("Client disconnected: {ConnectionId}, User: {UserId}", 
                Context.ConnectionId, Context.UserIdentifier);
            
            if (exception != null)
            {
                _logger.LogWarning(exception, "Client disconnected with exception");
            }
            
            await base.OnDisconnectedAsync(exception);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling client disconnection");
        }
    }
}

/// <summary>
/// Interface for compliance monitoring hub clients
/// </summary>
public interface IComplianceMonitoringClient
{
    /// <summary>
    /// Send compliance alert to clients
    /// </summary>
    Task ComplianceAlert(object alert);

    /// <summary>
    /// Send compliance status update
    /// </summary>
    Task ComplianceStatusUpdate(object status);

    /// <summary>
    /// Send anomaly detection alert
    /// </summary>
    Task AnomalyDetected(ComplianceAnomalyDto anomaly);

    /// <summary>
    /// Send risk score update
    /// </summary>
    Task RiskScoreUpdate(object riskUpdate);

    /// <summary>
    /// Send dashboard metrics update
    /// </summary>
    Task DashboardMetricsUpdate(object metrics);

    /// <summary>
    /// Send recent anomalies
    /// </summary>
    Task RecentAnomalies(IEnumerable<ComplianceAnomalyDto> anomalies);

    /// <summary>
    /// Send connection confirmation
    /// </summary>
    Task Connected(object connectionInfo);

    /// <summary>
    /// Send error message
    /// </summary>
    Task Error(string message);

    /// <summary>
    /// Send compliance report status update
    /// </summary>
    Task ComplianceReportUpdate(object reportUpdate);

    /// <summary>
    /// Send audit log notification
    /// </summary>
    Task AuditLogNotification(object auditNotification);

    /// <summary>
    /// Send model training status update
    /// </summary>
    Task ModelTrainingUpdate(object trainingUpdate);
}
