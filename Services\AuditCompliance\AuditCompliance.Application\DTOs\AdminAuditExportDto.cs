using AuditCompliance.Domain.Enums;

namespace AuditCompliance.Application.DTOs
{
    /// <summary>
    /// Request DTO for exporting admin audit trail
    /// </summary>
    public class AdminAuditExportRequestDto
    {
        public List<Guid> UserIds { get; set; } = new();
        public List<string> ActionTypes { get; set; } = new();
        public List<string> EntityTypes { get; set; } = new();
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public List<string> SeverityLevels { get; set; } = new();
        public List<string> IpAddresses { get; set; } = new();
        public bool IncludeSystemActions { get; set; } = true;
        public bool IncludeSensitiveData { get; set; } = false;
        public ExportFormat Format { get; set; } = ExportFormat.CSV;
        public List<string> SelectedColumns { get; set; } = new();
        public int? MaxRecords { get; set; }
        
        // Audit fields (set by controller)
        public Guid RequestedBy { get; set; }
        public string RequestedByRole { get; set; }
        public string IpAddress { get; set; }
        public string UserAgent { get; set; }
    }

    /// <summary>
    /// Response DTO for admin audit trail export
    /// </summary>
    public class AdminAuditExportResponseDto
    {
        public string FileName { get; set; }
        public string FileUrl { get; set; }
        public ExportFormat Format { get; set; }
        public int RecordCount { get; set; }
        public int UniqueUsers { get; set; }
        public int UniqueIpAddresses { get; set; }
        public long FileSizeBytes { get; set; }
        public DateTime GeneratedAt { get; set; }
        public DateTime ExpiresAt { get; set; }
        public string ExportId { get; set; }
        public AdminAuditExportSummaryDto Summary { get; set; } = new();
        public bool SensitiveDataIncluded { get; set; }
        public List<string> SecurityWarnings { get; set; } = new();
    }

    /// <summary>
    /// Summary of exported audit trail data
    /// </summary>
    public class AdminAuditExportSummaryDto
    {
        public int TotalActions { get; set; }
        public int SystemActions { get; set; }
        public int UserActions { get; set; }
        public Dictionary<string, int> ActionTypeBreakdown { get; set; } = new();
        public Dictionary<string, int> EntityTypeBreakdown { get; set; } = new();
        public Dictionary<string, int> SeverityBreakdown { get; set; } = new();
        public Dictionary<string, int> HourlyDistribution { get; set; } = new();
        public List<string> TopUsers { get; set; } = new();
        public List<string> TopIpAddresses { get; set; } = new();
        public DateTime? EarliestAction { get; set; }
        public DateTime? LatestAction { get; set; }
        public int FailedActions { get; set; }
        public int SecurityEvents { get; set; }
    }

    /// <summary>
    /// Individual audit trail entry for export
    /// </summary>
    public class AdminAuditExportEntryDto
    {
        public Guid Id { get; set; }
        public string EntityType { get; set; }
        public Guid? EntityId { get; set; }
        public string Action { get; set; }
        public Guid? PerformedBy { get; set; }
        public string PerformedByName { get; set; }
        public string PerformedByRole { get; set; }
        public string Description { get; set; }
        public string IpAddress { get; set; }
        public string UserAgent { get; set; }
        public string Severity { get; set; }
        public DateTime Timestamp { get; set; }
        public Dictionary<string, object> AdditionalData { get; set; } = new();
        public bool IsSystemAction { get; set; }
        public bool IsSecurityEvent { get; set; }
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// Audit trail statistics DTO
    /// </summary>
    public class AuditTrailStatisticsDto
    {
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public int TotalActions { get; set; }
        public int UniqueUsers { get; set; }
        public int UniqueIpAddresses { get; set; }
        public Dictionary<string, int> ActionsByType { get; set; } = new();
        public Dictionary<string, int> ActionsByEntity { get; set; } = new();
        public Dictionary<string, int> ActionsBySeverity { get; set; } = new();
        public Dictionary<string, int> ActionsByHour { get; set; } = new();
        public Dictionary<string, int> ActionsByDay { get; set; } = new();
        public List<TopUserActivityDto> TopUsers { get; set; } = new();
        public List<SuspiciousActivityDto> SuspiciousActivities { get; set; } = new();
        public int SecurityEvents { get; set; }
        public int FailedActions { get; set; }
        public decimal SuccessRate { get; set; }
    }

    /// <summary>
    /// Top user activity DTO
    /// </summary>
    public class TopUserActivityDto
    {
        public Guid UserId { get; set; }
        public string UserName { get; set; }
        public string Role { get; set; }
        public int ActionCount { get; set; }
        public DateTime LastActivity { get; set; }
        public List<string> TopActions { get; set; } = new();
    }

    /// <summary>
    /// Suspicious activity DTO
    /// </summary>
    public class SuspiciousActivityDto
    {
        public string Type { get; set; }
        public string Description { get; set; }
        public int Count { get; set; }
        public DateTime FirstOccurrence { get; set; }
        public DateTime LastOccurrence { get; set; }
        public List<string> AffectedUsers { get; set; } = new();
        public List<string> AffectedIpAddresses { get; set; } = new();
        public string RiskLevel { get; set; }
    }
}
