{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=TLI_OrderManagement;User Id=timescale;Password=timescale"}, "JwtSettings": {"Secret": "your-super-secret-key-that-is-at-least-32-characters-long", "Issuer": "TLI-Identity-Service", "Audience": "TLI-Services", "ExpiryMinutes": 60}, "RabbitMQ": {"Host": "localhost", "Port": 5672, "Username": "guest", "Password": "guest"}, "RfqExpiration": {"CheckIntervalMinutes": 15, "NotificationHoursBeforeExpiration": [24, 12, 6, 1], "DefaultTimeframeDurationHours": 72, "MaxExtensionsAllowed": 3, "AllowExtensionsByDefault": true}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/order-management-.log", "rollingInterval": "Day", "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}]}}