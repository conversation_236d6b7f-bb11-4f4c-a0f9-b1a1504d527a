using Microsoft.EntityFrameworkCore;
using NetworkFleetManagement.Domain.Entities;
using NetworkFleetManagement.Domain.Enums;
using NetworkFleetManagement.Domain.Repositories;
using NetworkFleetManagement.Infrastructure.Persistence;

namespace NetworkFleetManagement.Infrastructure.Repositories;

public class BrokerCarrierNetworkRepository : IBrokerCarrierNetworkRepository
{
    private readonly NetworkFleetDbContext _context;

    public BrokerCarrierNetworkRepository(NetworkFleetDbContext context)
    {
        _context = context;
    }

    public async Task<BrokerCarrierNetwork?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.BrokerCarrierNetworks
            .Include(n => n.Carrier)
            .Include(n => n.PerformanceRecords)
            .FirstOrDefaultAsync(n => n.Id == id, cancellationToken);
    }

    public async Task<BrokerCarrierNetwork?> GetByBrokerAndCarrierAsync(Guid brokerId, Guid carrierId, CancellationToken cancellationToken = default)
    {
        return await _context.BrokerCarrierNetworks
            .Include(n => n.Carrier)
            .Include(n => n.PerformanceRecords)
            .FirstOrDefaultAsync(n => n.BrokerId == brokerId && n.CarrierId == carrierId, cancellationToken);
    }

    public async Task<IEnumerable<BrokerCarrierNetwork>> GetByBrokerIdAsync(Guid brokerId, CancellationToken cancellationToken = default)
    {
        return await _context.BrokerCarrierNetworks
            .Include(n => n.Carrier)
            .Include(n => n.PerformanceRecords)
            .Where(n => n.BrokerId == brokerId)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<BrokerCarrierNetwork>> GetByCarrierIdAsync(Guid carrierId, CancellationToken cancellationToken = default)
    {
        return await _context.BrokerCarrierNetworks
            .Include(n => n.Carrier)
            .Include(n => n.PerformanceRecords)
            .Where(n => n.CarrierId == carrierId)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<BrokerCarrierNetwork>> GetByStatusAsync(NetworkRelationshipStatus status, CancellationToken cancellationToken = default)
    {
        return await _context.BrokerCarrierNetworks
            .Include(n => n.Carrier)
            .Where(n => n.Status == status)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<BrokerCarrierNetwork>> GetActiveNetworksAsync(CancellationToken cancellationToken = default)
    {
        return await _context.BrokerCarrierNetworks
            .Include(n => n.Carrier)
            .Where(n => n.Status == NetworkRelationshipStatus.Active)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<BrokerCarrierNetwork>> GetExclusiveNetworksAsync(CancellationToken cancellationToken = default)
    {
        var today = DateTime.UtcNow;
        
        return await _context.BrokerCarrierNetworks
            .Include(n => n.Carrier)
            .Where(n => n.IsExclusive && 
                       n.Status == NetworkRelationshipStatus.Active &&
                       (!n.ExclusivityExpiresAt.HasValue || n.ExclusivityExpiresAt.Value > today))
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<BrokerCarrierNetwork>> GetNetworksByPriorityAsync(Guid brokerId, int maxPriority = 5, CancellationToken cancellationToken = default)
    {
        return await _context.BrokerCarrierNetworks
            .Include(n => n.Carrier)
            .Where(n => n.BrokerId == brokerId && 
                       n.Status == NetworkRelationshipStatus.Active &&
                       n.Priority <= maxPriority)
            .OrderBy(n => n.Priority)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<BrokerCarrierNetwork>> GetExpiredContractsAsync(CancellationToken cancellationToken = default)
    {
        var today = DateTime.UtcNow.Date;
        
        return await _context.BrokerCarrierNetworks
            .Include(n => n.Carrier)
            .Where(n => n.ContractEndDate.HasValue && n.ContractEndDate.Value <= today)
            .ToListAsync(cancellationToken);
    }

    public async Task<(IEnumerable<BrokerCarrierNetwork> Networks, int TotalCount)> GetPagedAsync(
        int pageNumber, 
        int pageSize, 
        Guid? brokerId = null,
        Guid? carrierId = null,
        NetworkRelationshipStatus? status = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.BrokerCarrierNetworks
            .Include(n => n.Carrier)
            .AsQueryable();

        // Apply filters
        if (brokerId.HasValue)
        {
            query = query.Where(n => n.BrokerId == brokerId.Value);
        }

        if (carrierId.HasValue)
        {
            query = query.Where(n => n.CarrierId == carrierId.Value);
        }

        if (status.HasValue)
        {
            query = query.Where(n => n.Status == status.Value);
        }

        var totalCount = await query.CountAsync(cancellationToken);

        var networks = await query
            .OrderBy(n => n.Priority)
            .ThenBy(n => n.Carrier.CompanyName)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return (networks, totalCount);
    }

    public void Add(BrokerCarrierNetwork network)
    {
        _context.BrokerCarrierNetworks.Add(network);
    }

    public void Update(BrokerCarrierNetwork network)
    {
        _context.BrokerCarrierNetworks.Update(network);
    }

    public void Remove(BrokerCarrierNetwork network)
    {
        _context.BrokerCarrierNetworks.Remove(network);
    }
}
