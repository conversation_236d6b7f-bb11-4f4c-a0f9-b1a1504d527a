using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Entities;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Application.Commands.ResetRfqAward;

public class ResetRfqAwardCommandHandler : IRequestHandler<ResetRfqAwardCommand, bool>
{
    private readonly IRfqRepository _rfqRepository;
    private readonly IRfqBidRepository _bidRepository;
    private readonly IRfqTimelineRepository _timelineRepository;
    private readonly IAdministrativeAuditRepository _auditRepository;
    private readonly IAuthorizationService _authorizationService;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<ResetRfqAwardCommandHandler> _logger;

    public ResetRfqAwardCommandHandler(
        IRfqRepository rfqRepository,
        IRfqBidRepository bidRepository,
        IRfqTimelineRepository timelineRepository,
        IAdministrativeAuditRepository auditRepository,
        IAuthorizationService authorizationService,
        IUnitOfWork unitOfWork,
        ILogger<ResetRfqAwardCommandHandler> logger)
    {
        _rfqRepository = rfqRepository;
        _bidRepository = bidRepository;
        _timelineRepository = timelineRepository;
        _auditRepository = auditRepository;
        _authorizationService = authorizationService;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<bool> Handle(ResetRfqAwardCommand request, CancellationToken cancellationToken)
    {
        _logger.LogWarning("ADMINISTRATIVE OVERRIDE: Resetting award for RFQ {RfqId} by admin {AdminUserId} ({AdminUserName})",
            request.RfqId, request.AdminUserId, request.AdminUserName);

        try
        {
            // Verify administrative authorization
            var isAuthorized = await _authorizationService.HasAdministrativeOverridePermissionAsync(
                request.AdminUserId, "ResetRfqAward", cancellationToken);

            if (!isAuthorized)
            {
                _logger.LogError("SECURITY VIOLATION: User {AdminUserId} ({AdminUserName}) attempted unauthorized reset of RFQ {RfqId}",
                    request.AdminUserId, request.AdminUserName, request.RfqId);

                await LogSecurityViolation(request, "Unauthorized reset award attempt", cancellationToken);
                throw new UnauthorizedAccessException("Insufficient permissions for administrative override");
            }

            // Get the RFQ
            var rfq = await _rfqRepository.GetByIdAsync(request.RfqId, cancellationToken);
            if (rfq == null)
            {
                _logger.LogWarning("RFQ {RfqId} not found for award reset", request.RfqId);
                return false;
            }

            // Validate RFQ can be reset
            var validationResult = ValidateAwardReset(rfq);
            if (!validationResult.IsValid)
            {
                _logger.LogWarning("Award reset validation failed for RFQ {RfqId}: {Errors}",
                    request.RfqId, string.Join(", ", validationResult.Errors));
                return false;
            }

            // Store original state for audit
            var originalStatus = rfq.Status;
            var originalAwardedBidId = rfq.AwardedBidId;
            var originalAwardedBidderName = string.Empty;

            // Get awarded bid details for audit
            if (originalAwardedBidId.HasValue)
            {
                var awardedBid = await _bidRepository.GetByIdAsync(originalAwardedBidId.Value, cancellationToken);
                originalAwardedBidderName = awardedBid?.BidderName ?? "Unknown";
            }

            // Reset the award
            rfq.ResetAward(request.AdminUserId, request.ResetReason);

            // Reopen for bidding if requested
            if (request.ReopenForBidding)
            {
                if (request.NewExpirationDate.HasValue)
                {
                    // Calculate extension duration in hours
                    var currentExpiration = rfq.ExpiresAt ?? DateTime.UtcNow;
                    var newExpiration = request.NewExpirationDate.Value;
                    var timeDifference = newExpiration - currentExpiration;
                    var extensionHours = (int)Math.Ceiling(timeDifference.TotalHours);

                    if (extensionHours > 0)
                    {
                        rfq.ExtendTimeframe(
                            extensionHours,
                            Domain.Enums.TimeframeUnit.Hours,
                            "Extended due to award reset",
                            request.AdminUserId);
                    }
                }

                rfq.Republish(request.AdminUserId);
            }

            // Create comprehensive audit log
            var auditLog = new AdministrativeAuditLog(
                request.AdminUserId,
                request.AdminUserName,
                request.AdminRole,
                AdministrativeAction.ResetRfqAward,
                request.RfqId,
                "RFQ",
                request.ResetReason,
                request.AdditionalNotes,
                new Dictionary<string, object>
                {
                    ["rfqId"] = request.RfqId,
                    ["originalStatus"] = originalStatus.ToString(),
                    ["newStatus"] = rfq.Status.ToString(),
                    ["originalAwardedBidId"] = originalAwardedBidId?.ToString() ?? "None",
                    ["originalAwardedBidderName"] = originalAwardedBidderName,
                    ["reopenForBidding"] = request.ReopenForBidding,
                    ["newExpirationDate"] = request.NewExpirationDate?.ToString() ?? "None",
                    ["resetTimestamp"] = DateTime.UtcNow
                }.Concat(request.AuditMetadata ?? new Dictionary<string, object>())
                .ToDictionary(kvp => kvp.Key, kvp => kvp.Value));

            await _auditRepository.AddAsync(auditLog, cancellationToken);

            // Create timeline event
            var description = $"RFQ award reset by administrator '{request.AdminUserName}'. Reason: {request.ResetReason}";
            if (request.ReopenForBidding)
            {
                description += " RFQ reopened for bidding.";
                if (request.NewExpirationDate.HasValue)
                    description += $" New expiration: {request.NewExpirationDate.Value:yyyy-MM-dd HH:mm} UTC";
            }

            var timelineEvent = RfqTimeline.CreateEvent(
                request.RfqId,
                RfqTimelineEventType.AwardReset,
                description,
                request.AdminUserId,
                request.AdminUserName,
                "Administrator",
                System.Text.Json.JsonSerializer.Serialize(new
                {
                    originalAwardedBidId = originalAwardedBidId?.ToString() ?? "None",
                    originalAwardedBidderName = originalAwardedBidderName,
                    resetReason = request.ResetReason,
                    reopenForBidding = request.ReopenForBidding,
                    newExpirationDate = request.NewExpirationDate,
                    auditLogId = auditLog.Id
                }),
                originalStatus,
                rfq.Status);

            await _timelineRepository.AddAsync(timelineEvent, cancellationToken);

            // Save changes
            _rfqRepository.Update(rfq);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogWarning("ADMINISTRATIVE OVERRIDE COMPLETED: RFQ {RfqId} award reset by admin {AdminUserId}",
                request.RfqId, request.AdminUserId);

            // TODO: Send notifications if requested
            if (request.NotifyStakeholders)
            {
                // Implement notification logic here
                _logger.LogInformation("Stakeholder notifications would be sent for award reset of RFQ {RfqId}", request.RfqId);
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during award reset of RFQ {RfqId} by admin {AdminUserId}",
                request.RfqId, request.AdminUserId);

            // Log the failed attempt
            await LogFailedAttempt(request, ex.Message, cancellationToken);
            throw;
        }
    }

    private static ValidationResult ValidateAwardReset(RFQ rfq)
    {
        var errors = new List<string>();

        // Check if RFQ is in a state that can be reset
        if (rfq.Status != RfqStatus.Awarded)
            errors.Add($"RFQ is in {rfq.Status} status and cannot be reset (must be Awarded)");

        if (!rfq.AwardedBidId.HasValue)
            errors.Add("RFQ has no awarded bid to reset");

        // Check if RFQ is too old to reset
        if (rfq.AwardedAt.HasValue && rfq.AwardedAt.Value.AddDays(90) < DateTime.UtcNow)
            errors.Add("RFQ was awarded more than 90 days ago and cannot be reset");

        return new ValidationResult
        {
            IsValid = !errors.Any(),
            Errors = errors
        };
    }

    private async Task LogSecurityViolation(ResetRfqAwardCommand request, string violation, CancellationToken cancellationToken)
    {
        var securityLog = new AdministrativeAuditLog(
            request.AdminUserId,
            request.AdminUserName,
            request.AdminRole,
            AdministrativeAction.SecurityViolation,
            request.RfqId,
            "Security",
            violation,
            $"Attempted reset of RFQ {request.RfqId} award",
            new Dictionary<string, object>
            {
                ["attemptedAction"] = "ResetRfqAward",
                ["rfqId"] = request.RfqId,
                ["violationType"] = "UnauthorizedAccess",
                ["timestamp"] = DateTime.UtcNow
            });

        await _auditRepository.AddAsync(securityLog, cancellationToken);
    }

    private async Task LogFailedAttempt(ResetRfqAwardCommand request, string error, CancellationToken cancellationToken)
    {
        var failureLog = new AdministrativeAuditLog(
            request.AdminUserId,
            request.AdminUserName,
            request.AdminRole,
            AdministrativeAction.ResetRfqAward,
            request.RfqId,
            "RFQ",
            "Failed award reset attempt",
            error,
            new Dictionary<string, object>
            {
                ["rfqId"] = request.RfqId,
                ["errorMessage"] = error,
                ["success"] = false,
                ["timestamp"] = DateTime.UtcNow
            });

        await _auditRepository.AddAsync(failureLog, cancellationToken);
    }

    private class ValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new();
    }
}
