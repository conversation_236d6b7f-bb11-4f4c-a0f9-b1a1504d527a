using AuditCompliance.API;
using AuditCompliance.Application.DTOs;
using AuditCompliance.Domain.Enums;
using AuditCompliance.Infrastructure.Persistence;
using FluentAssertions;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using Xunit;

namespace AuditCompliance.Tests.Integration
{
    /// <summary>
    /// Integration tests for enhanced export functionality
    /// </summary>
    public class EnhancedExportIntegrationTests : IClassFixture<WebApplicationFactory<Program>>, IDisposable
    {
        private readonly WebApplicationFactory<Program> _factory;
        private readonly HttpClient _client;
        private readonly IServiceScope _scope;
        private readonly AuditComplianceDbContext _context;

        public EnhancedExportIntegrationTests(WebApplicationFactory<Program> factory)
        {
            _factory = factory.WithWebHostBuilder(builder =>
            {
                builder.UseEnvironment("Testing");
                builder.ConfigureServices(services =>
                {
                    // Remove the existing DbContext registration
                    var descriptor = services.SingleOrDefault(d => d.ServiceType == typeof(DbContextOptions<AuditComplianceDbContext>));
                    if (descriptor != null)
                    {
                        services.Remove(descriptor);
                    }

                    // Add in-memory database for testing
                    services.AddDbContext<AuditComplianceDbContext>(options =>
                    {
                        options.UseInMemoryDatabase($"TestDb_{Guid.NewGuid()}");
                    });

                    // Reduce logging noise in tests
                    services.AddLogging(builder => builder.SetMinimumLevel(LogLevel.Warning));
                });
            });

            _client = _factory.CreateClient();
            _scope = _factory.Services.CreateScope();
            _context = _scope.ServiceProvider.GetRequiredService<AuditComplianceDbContext>();
            
            // Ensure database is created
            _context.Database.EnsureCreated();
        }

        [Fact]
        public async Task CreateEnhancedExport_WithValidRequest_ShouldReturnSuccess()
        {
            // Arrange
            await SeedTestDataAsync();
            var request = CreateValidExportRequest();

            // Act
            var response = await _client.PostAsJsonAsync("/api/audit/export", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<EnhancedAuditExportResponseDto>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            result!.IsSuccessful.Should().BeTrue();
            result.JobId.Should().NotBeNullOrEmpty();
            result.EstimatedCompletionTime.Should().BeAfter(DateTime.UtcNow);
        }

        [Fact]
        public async Task CreateEnhancedExport_WithInvalidRequest_ShouldReturnBadRequest()
        {
            // Arrange
            var request = CreateValidExportRequest();
            request.RequestedByRole = string.Empty; // Invalid: missing role

            // Act
            var response = await _client.PostAsJsonAsync("/api/audit/export", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        }

        [Fact]
        public async Task CreateBulkExport_WithValidRequest_ShouldReturnSuccess()
        {
            // Arrange
            await SeedTestDataAsync();
            var bulkRequest = new BulkAuditExportRequestDto
            {
                ExportRequests = new List<EnhancedAuditExportRequestDto>
                {
                    CreateValidExportRequest(),
                    CreateValidExportRequest()
                },
                ProcessInParallel = false,
                NotificationEmail = "<EMAIL>"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/audit/export/bulk", bulkRequest);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<BulkAuditExportResponseDto>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            result!.IsSuccessful.Should().BeTrue();
            result.JobIds.Should().HaveCount(2);
            result.JobIds.Should().AllSatisfy(jobId => jobId.Should().NotBeNullOrEmpty());
        }

        [Fact]
        public async Task GetExportJobStatus_WithValidJobId_ShouldReturnStatus()
        {
            // Arrange
            await SeedTestDataAsync();
            var request = CreateValidExportRequest();
            
            // Create export job first
            var createResponse = await _client.PostAsJsonAsync("/api/audit/export", request);
            createResponse.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var createContent = await createResponse.Content.ReadAsStringAsync();
            var createResult = JsonSerializer.Deserialize<EnhancedAuditExportResponseDto>(createContent, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            // Act
            var response = await _client.GetAsync($"/api/audit/export/{createResult!.JobId}/status");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<ExportJobStatusDto>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            result!.JobId.Should().Be(createResult.JobId);
            result.Status.Should().BeOneOf(ExportStatus.Queued, ExportStatus.Processing, ExportStatus.Completed);
        }

        [Fact]
        public async Task GetExportJobStatus_WithInvalidJobId_ShouldReturnNotFound()
        {
            // Arrange
            var invalidJobId = "invalid-job-id";

            // Act
            var response = await _client.GetAsync($"/api/audit/export/{invalidJobId}/status");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.NotFound);
        }

        [Fact]
        public async Task CancelExportJob_WithValidJobId_ShouldReturnSuccess()
        {
            // Arrange
            await SeedTestDataAsync();
            var request = CreateValidExportRequest();
            
            // Create export job first
            var createResponse = await _client.PostAsJsonAsync("/api/audit/export", request);
            var createContent = await createResponse.Content.ReadAsStringAsync();
            var createResult = JsonSerializer.Deserialize<EnhancedAuditExportResponseDto>(createContent, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            var cancelRequest = new CancelExportJobRequestDto
            {
                Reason = "Test cancellation"
            };

            // Act
            var response = await _client.PostAsJsonAsync($"/api/audit/export/{createResult!.JobId}/cancel", cancelRequest);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<JsonElement>(content);
            result.GetProperty("success").GetBoolean().Should().BeTrue();
        }

        [Fact]
        public async Task GetSupportedExportFormats_ShouldReturnFormats()
        {
            // Act
            var response = await _client.GetAsync("/api/audit/export/formats");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<List<ExportFormatConfigDto>>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            result!.Should().NotBeEmpty();
            result.Should().Contain(f => f.Format == ExportFormat.CSV);
            result.Should().Contain(f => f.Format == ExportFormat.Excel);
            result.Should().Contain(f => f.Format == ExportFormat.PDF);
        }

        [Fact]
        public async Task ValidateExportRequest_WithValidRequest_ShouldReturnNoErrors()
        {
            // Arrange
            await SeedTestDataAsync();
            var request = CreateValidExportRequest();

            // Act
            var response = await _client.PostAsJsonAsync("/api/audit/export/validate", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<List<ExportValidationDto>>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            result!.Where(v => v.Type == "Error").Should().BeEmpty();
        }

        [Fact]
        public async Task ValidateExportRequest_WithInvalidRequest_ShouldReturnErrors()
        {
            // Arrange
            var request = CreateValidExportRequest();
            request.FromDate = DateTime.UtcNow;
            request.ToDate = DateTime.UtcNow.AddDays(-1); // Invalid date range

            // Act
            var response = await _client.PostAsJsonAsync("/api/audit/export/validate", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<List<ExportValidationDto>>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            result!.Where(v => v.Type == "Error").Should().NotBeEmpty();
        }

        [Fact]
        public async Task GetExportHistory_ShouldReturnHistory()
        {
            // Arrange
            await SeedTestDataAsync();
            
            // Create a few export jobs first
            for (int i = 0; i < 3; i++)
            {
                var request = CreateValidExportRequest();
                await _client.PostAsJsonAsync("/api/audit/export", request);
            }

            // Act
            var response = await _client.GetAsync("/api/audit/export/history?pageNumber=1&pageSize=10");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<List<EnhancedAuditExportResponseDto>>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            result!.Should().HaveCountGreaterOrEqualTo(3);
        }

        private async Task SeedTestDataAsync()
        {
            // Add test module registry data
            var testModule = new Domain.Entities.ModuleRegistry(
                "TestModule",
                "Test Module",
                "Test module for integration tests",
                "1.0.0",
                new List<string> { "AuditLog", "ComplianceReport" },
                new List<string> { "Create", "Read", "Update", "Delete" },
                new List<string> { "Admin", "Auditor" },
                new Dictionary<string, object>(),
                Guid.NewGuid());

            _context.ModuleRegistries.Add(testModule);

            // Add test audit logs
            for (int i = 0; i < 10; i++)
            {
                var auditLog = new Domain.Entities.AuditLog(
                    Guid.NewGuid(),
                    "TestUser",
                    "TestAction",
                    "TestEntity",
                    Guid.NewGuid(),
                    "127.0.0.1",
                    "Test audit log entry",
                    new Dictionary<string, object> { { "testProperty", $"value{i}" } });

                _context.AuditLogs.Add(auditLog);
            }

            await _context.SaveChangesAsync();
        }

        private static EnhancedAuditExportRequestDto CreateValidExportRequest()
        {
            return new EnhancedAuditExportRequestDto
            {
                RequestedBy = Guid.NewGuid(),
                RequestedByRole = "Admin",
                ModuleNames = new List<string> { "TestModule" },
                EntityTypes = new List<string> { "AuditLog" },
                FromDate = DateTime.UtcNow.AddDays(-30),
                ToDate = DateTime.UtcNow,
                Format = ExportFormat.CSV,
                SelectedColumns = new List<string> { "Id", "Action", "Timestamp", "UserId" },
                MaxRecords = 1000,
                PageSize = 100,
                IncludeSensitiveData = false,
                IncludeSystemActions = true,
                ComplianceFlags = new List<string>(),
                CustomFilters = new Dictionary<string, object>(),
                SortBy = "Timestamp",
                SortDescending = true,
                IncludeMetadata = true,
                IncludeRelatedData = false,
                CompressionEnabled = true,
                DigitalSignatureRequired = false
            };
        }

        public void Dispose()
        {
            _context?.Dispose();
            _scope?.Dispose();
            _client?.Dispose();
        }
    }

    // Supporting DTOs for integration tests
    public class CancelExportJobRequestDto
    {
        public string Reason { get; set; } = string.Empty;
    }
}
