using Microsoft.EntityFrameworkCore;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Entities;
using OrderManagement.Domain.Enums;
using OrderManagement.Infrastructure.Persistence;

namespace OrderManagement.Infrastructure.Repositories;

public class RfqTagRepository : IRfqTagRepository
{
    private readonly OrderManagementDbContext _context;

    public RfqTagRepository(OrderManagementDbContext context)
    {
        _context = context;
    }

    public async Task<List<RfqTag>> GetByRfqIdAsync(Guid rfqId, bool activeOnly = true, CancellationToken cancellationToken = default)
    {
        var query = _context.RfqTags
            .Where(rt => rt.RfqId == rfqId);

        if (activeOnly)
        {
            query = query.Where(rt => rt.IsActive);
        }

        return await query
            .OrderByDescending(rt => rt.AppliedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<RfqTag?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.RfqTags
            .FirstOrDefaultAsync(rt => rt.Id == id, cancellationToken);
    }

    public async Task<List<RfqTag>> GetByTagTypeAsync(RfqTagType tagType, bool activeOnly = true, CancellationToken cancellationToken = default)
    {
        var query = _context.RfqTags
            .Where(rt => rt.TagType == tagType);

        if (activeOnly)
        {
            query = query.Where(rt => rt.IsActive);
        }

        return await query
            .OrderByDescending(rt => rt.AppliedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<(List<RFQ> Rfqs, int TotalCount)> GetRfqsByTagsAsync(
        List<RfqTagType> tagTypes,
        int page,
        int pageSize,
        bool activeTagsOnly = true,
        CancellationToken cancellationToken = default)
    {
        var query = _context.Rfqs
            .Where(r => r.Tags.Any(t => tagTypes.Contains(t.TagType) && (!activeTagsOnly || t.IsActive)))
            .Include(r => r.Tags.Where(t => tagTypes.Contains(t.TagType) && (!activeTagsOnly || t.IsActive)))
            .Include(r => r.Bids)
            .Include(r => r.Documents);

        var totalCount = await query.CountAsync(cancellationToken);

        var rfqs = await query
            .OrderByDescending(r => r.CreatedAt)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return (rfqs, totalCount);
    }

    public async Task AddAsync(RfqTag tag, CancellationToken cancellationToken = default)
    {
        await _context.RfqTags.AddAsync(tag, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task AddRangeAsync(List<RfqTag> tags, CancellationToken cancellationToken = default)
    {
        await _context.RfqTags.AddRangeAsync(tags, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public void Update(RfqTag tag)
    {
        _context.RfqTags.Update(tag);
    }

    public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var tag = await GetByIdAsync(id, cancellationToken);
        if (tag != null)
        {
            _context.RfqTags.Remove(tag);
            await _context.SaveChangesAsync(cancellationToken);
        }
    }
}
