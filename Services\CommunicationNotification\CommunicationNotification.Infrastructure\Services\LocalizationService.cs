using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using System.Globalization;
using System.Text.Json;

namespace CommunicationNotification.Infrastructure.Services;

/// <summary>
/// Localization service implementation
/// </summary>
public class LocalizationService : ILocalizationService
{
    private readonly IMemoryCache _cache;
    private readonly ILogger<LocalizationService> _logger;
    private readonly ILocalizationRepository _localizationRepository;
    private readonly Dictionary<Language, CultureInfo> _supportedCultures;
    private readonly TimeSpan _cacheExpiration = TimeSpan.FromHours(1);

    public LocalizationService(
        IMemoryCache cache,
        ILogger<LocalizationService> logger,
        ILocalizationRepository localizationRepository)
    {
        _cache = cache;
        _logger = logger;
        _localizationRepository = localizationRepository;
        
        _supportedCultures = new Dictionary<Language, CultureInfo>
        {
            { Language.English, new CultureInfo("en-US") },
            { Language.Hindi, new CultureInfo("hi-IN") },
            { Language.Kannada, new CultureInfo("kn-IN") }
        };
    }

    public async Task<string> GetLocalizedTextAsync(
        string key,
        Language language,
        Dictionary<string, object>? parameters = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"localization_{key}_{language}";
            
            if (_cache.TryGetValue(cacheKey, out string? cachedText) && !string.IsNullOrEmpty(cachedText))
            {
                return FormatText(cachedText, parameters);
            }

            var localizedText = await _localizationRepository.GetTextAsync(key, language, cancellationToken);
            
            if (string.IsNullOrEmpty(localizedText))
            {
                // Fallback to English if translation not found
                if (language != Language.English)
                {
                    localizedText = await _localizationRepository.GetTextAsync(key, Language.English, cancellationToken);
                }
                
                // If still not found, return the key itself
                if (string.IsNullOrEmpty(localizedText))
                {
                    localizedText = key;
                    _logger.LogWarning("Localization key '{Key}' not found for language '{Language}'", key, language);
                }
            }

            // Cache the result
            _cache.Set(cacheKey, localizedText, _cacheExpiration);

            return FormatText(localizedText, parameters);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get localized text for key '{Key}' and language '{Language}'", key, language);
            return key; // Return key as fallback
        }
    }

    public async Task<MessageContent> LocalizeMessageContentAsync(
        string titleKey,
        string bodyKey,
        Language language,
        Dictionary<string, object>? parameters = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var localizedTitle = await GetLocalizedTextAsync(titleKey, language, parameters, cancellationToken);
            var localizedBody = await GetLocalizedTextAsync(bodyKey, language, parameters, cancellationToken);

            return MessageContent.Create(localizedTitle, localizedBody, language);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to localize message content for keys '{TitleKey}', '{BodyKey}' and language '{Language}'", 
                titleKey, bodyKey, language);
            
            // Return fallback content
            return MessageContent.Create(titleKey, bodyKey, language);
        }
    }

    public async Task<List<LocalizedTemplate>> GetTemplatesAsync(
        string templateCategory,
        Language language,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"templates_{templateCategory}_{language}";
            
            if (_cache.TryGetValue(cacheKey, out List<LocalizedTemplate>? cachedTemplates) && cachedTemplates != null)
            {
                return cachedTemplates;
            }

            var templates = await _localizationRepository.GetTemplatesAsync(templateCategory, language, cancellationToken);
            
            // Cache the result
            _cache.Set(cacheKey, templates, _cacheExpiration);

            return templates;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get templates for category '{Category}' and language '{Language}'", 
                templateCategory, language);
            return new List<LocalizedTemplate>();
        }
    }

    public async Task<LocalizedTemplate?> GetTemplateAsync(
        string templateKey,
        Language language,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"template_{templateKey}_{language}";
            
            if (_cache.TryGetValue(cacheKey, out LocalizedTemplate? cachedTemplate) && cachedTemplate != null)
            {
                return cachedTemplate;
            }

            var template = await _localizationRepository.GetTemplateAsync(templateKey, language, cancellationToken);
            
            if (template == null && language != Language.English)
            {
                // Fallback to English
                template = await _localizationRepository.GetTemplateAsync(templateKey, Language.English, cancellationToken);
            }

            if (template != null)
            {
                // Cache the result
                _cache.Set(cacheKey, template, _cacheExpiration);
            }

            return template;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get template '{TemplateKey}' for language '{Language}'", templateKey, language);
            return null;
        }
    }

    public async Task<string> RenderTemplateAsync(
        string templateKey,
        Language language,
        Dictionary<string, object> parameters,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var template = await GetTemplateAsync(templateKey, language, cancellationToken);
            
            if (template == null)
            {
                _logger.LogWarning("Template '{TemplateKey}' not found for language '{Language}'", templateKey, language);
                return templateKey;
            }

            return RenderTemplate(template.Content, parameters);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to render template '{TemplateKey}' for language '{Language}'", templateKey, language);
            return templateKey;
        }
    }

    public CultureInfo GetCultureInfo(Language language)
    {
        return _supportedCultures.TryGetValue(language, out var culture) ? culture : _supportedCultures[Language.English];
    }

    public Language DetectLanguageFromCulture(string cultureName)
    {
        return cultureName?.ToLowerInvariant() switch
        {
            "hi" or "hi-in" => Language.Hindi,
            "kn" or "kn-in" => Language.Kannada,
            _ => Language.English
        };
    }

    public async Task<bool> IsTranslationAvailableAsync(
        string key,
        Language language,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var text = await _localizationRepository.GetTextAsync(key, language, cancellationToken);
            return !string.IsNullOrEmpty(text);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check translation availability for key '{Key}' and language '{Language}'", key, language);
            return false;
        }
    }

    public async Task InvalidateCacheAsync(string? key = null, Language? language = null)
    {
        try
        {
            if (key != null && language.HasValue)
            {
                // Remove specific cache entry
                var cacheKey = $"localization_{key}_{language}";
                _cache.Remove(cacheKey);
            }
            else
            {
                // Clear all localization cache (this is a simplified approach)
                // In production, you might want to use a more sophisticated cache invalidation strategy
                _logger.LogInformation("Cache invalidation requested - consider implementing more granular cache management");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to invalidate cache");
        }
    }

    public List<Language> GetSupportedLanguages()
    {
        return _supportedCultures.Keys.ToList();
    }

    private static string FormatText(string text, Dictionary<string, object>? parameters)
    {
        if (parameters == null || parameters.Count == 0)
        {
            return text;
        }

        var result = text;
        foreach (var parameter in parameters)
        {
            var placeholder = $"{{{parameter.Key}}}";
            result = result.Replace(placeholder, parameter.Value?.ToString() ?? string.Empty);
        }

        return result;
    }

    private static string RenderTemplate(string template, Dictionary<string, object> parameters)
    {
        var result = template;
        
        foreach (var parameter in parameters)
        {
            // Support both {{key}} and {key} placeholder formats
            var placeholder1 = $"{{{{{parameter.Key}}}}}";
            var placeholder2 = $"{{{parameter.Key}}}";
            
            var value = parameter.Value?.ToString() ?? string.Empty;
            
            result = result.Replace(placeholder1, value);
            result = result.Replace(placeholder2, value);
        }

        return result;
    }
}

/// <summary>
/// Localized template entity
/// </summary>
public class LocalizedTemplate
{
    public string Key { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public Language Language { get; set; }
    public string Content { get; set; } = string.Empty;
    public string? Subject { get; set; }
    public Dictionary<string, string> Metadata { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// Repository interface for localization data
/// </summary>
public interface ILocalizationRepository
{
    /// <summary>
    /// Get localized text by key and language
    /// </summary>
    Task<string?> GetTextAsync(string key, Language language, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get templates by category and language
    /// </summary>
    Task<List<LocalizedTemplate>> GetTemplatesAsync(string category, Language language, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get template by key and language
    /// </summary>
    Task<LocalizedTemplate?> GetTemplateAsync(string key, Language language, CancellationToken cancellationToken = default);

    /// <summary>
    /// Add or update localized text
    /// </summary>
    Task SetTextAsync(string key, Language language, string text, CancellationToken cancellationToken = default);

    /// <summary>
    /// Add or update template
    /// </summary>
    Task SetTemplateAsync(LocalizedTemplate template, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all localization keys for a language
    /// </summary>
    Task<List<string>> GetKeysAsync(Language language, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get translation statistics
    /// </summary>
    Task<LocalizationStatistics> GetStatisticsAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Localization statistics
/// </summary>
public class LocalizationStatistics
{
    public int TotalKeys { get; set; }
    public Dictionary<Language, int> TranslationCounts { get; set; } = new();
    public Dictionary<Language, double> CompletionPercentages { get; set; } = new();
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
}
