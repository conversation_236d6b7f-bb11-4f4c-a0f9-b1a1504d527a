using ContentManagement.Domain.Entities;
using ContentManagement.Domain.Enums;
using ContentManagement.Infrastructure.Data;
using ContentManagement.Infrastructure.Repositories;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace ContentManagement.Tests.Infrastructure;

public class ContentRepositoryTests : IDisposable
{
    private readonly ContentManagementDbContext _context;
    private readonly ContentRepository _repository;
    private readonly Mock<ILogger<ContentRepository>> _mockLogger;

    public ContentRepositoryTests()
    {
        var options = new DbContextOptionsBuilder<ContentManagementDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new ContentManagementDbContext(options);
        _mockLogger = new Mock<ILogger<ContentRepository>>();
        _repository = new ContentRepository(_context, _mockLogger.Object);
    }

    [Fact]
    public async Task AddAsync_Should_Add_Content_To_Database()
    {
        // Arrange
        var content = new Content(
            "Test Content",
            "test-content",
            ContentType.General,
            "This is test content",
            Guid.NewGuid(),
            "Test User");

        // Act
        await _repository.AddAsync(content);

        // Assert
        var savedContent = await _context.Contents.FirstOrDefaultAsync(c => c.Id == content.Id);
        savedContent.Should().NotBeNull();
        savedContent!.Title.Should().Be("Test Content");
        savedContent.Slug.Should().Be("test-content");
    }

    [Fact]
    public async Task GetByIdAsync_Should_Return_Content_When_Exists()
    {
        // Arrange
        var content = new Content(
            "Test Content",
            "test-content",
            ContentType.General,
            "This is test content",
            Guid.NewGuid(),
            "Test User");

        await _context.Contents.AddAsync(content);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(content.Id);

        // Assert
        result.Should().NotBeNull();
        result!.Id.Should().Be(content.Id);
        result.Title.Should().Be("Test Content");
    }

    [Fact]
    public async Task GetByIdAsync_Should_Return_Null_When_Not_Exists()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid();

        // Act
        var result = await _repository.GetByIdAsync(nonExistentId);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetBySlugAsync_Should_Return_Content_When_Exists()
    {
        // Arrange
        var content = new Content(
            "Test Content",
            "test-content",
            ContentType.General,
            "This is test content",
            Guid.NewGuid(),
            "Test User");

        await _context.Contents.AddAsync(content);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetBySlugAsync("test-content");

        // Assert
        result.Should().NotBeNull();
        result!.Slug.Should().Be("test-content");
        result.Title.Should().Be("Test Content");
    }

    [Fact]
    public async Task SearchAsync_Should_Return_Filtered_Results()
    {
        // Arrange
        var content1 = new Content("First Content", "first-content", ContentType.General, "First content body", Guid.NewGuid(), "User1");
        var content2 = new Content("Second Content", "second-content", ContentType.Faq, "Second content body", Guid.NewGuid(), "User2");
        var content3 = new Content("Third Article", "third-article", ContentType.NewsArticle, "Third content body", Guid.NewGuid(), "User1");

        await _context.Contents.AddRangeAsync(content1, content2, content3);
        await _context.SaveChangesAsync();

        // Act - Search by term
        var (items, totalCount) = await _repository.SearchAsync(searchTerm: "Content");

        // Assert
        totalCount.Should().Be(2);
        items.Should().HaveCount(2);
        items.Should().Contain(c => c.Title == "First Content");
        items.Should().Contain(c => c.Title == "Second Content");
    }

    [Fact]
    public async Task SearchAsync_Should_Filter_By_Type()
    {
        // Arrange
        var content1 = new Content("General Content", "general-content", ContentType.General, "General content body", Guid.NewGuid(), "User1");
        var content2 = new Content("FAQ Content", "faq-content", ContentType.Faq, "FAQ content body", Guid.NewGuid(), "User2");

        await _context.Contents.AddRangeAsync(content1, content2);
        await _context.SaveChangesAsync();

        // Act
        var (items, totalCount) = await _repository.SearchAsync(type: ContentType.Faq);

        // Assert
        totalCount.Should().Be(1);
        items.Should().HaveCount(1);
        items.First().Type.Should().Be(ContentType.Faq);
    }

    [Fact]
    public async Task SearchAsync_Should_Filter_By_Status()
    {
        // Arrange
        var content1 = new Content("Draft Content", "draft-content", ContentType.General, "Draft content body", Guid.NewGuid(), "User1");
        var content2 = new Content("Published Content", "published-content", ContentType.General, "Published content body", Guid.NewGuid(), "User2");
        
        // Publish the second content
        content2.SubmitForReview(Guid.NewGuid(), "Reviewer");
        content2.Approve(Guid.NewGuid(), "Approver");
        content2.Publish(Guid.NewGuid(), "Publisher");

        await _context.Contents.AddRangeAsync(content1, content2);
        await _context.SaveChangesAsync();

        // Act
        var (items, totalCount) = await _repository.SearchAsync(status: ContentStatus.Published);

        // Assert
        totalCount.Should().Be(1);
        items.Should().HaveCount(1);
        items.First().Status.Should().Be(ContentStatus.Published);
    }

    [Fact]
    public async Task IsSlugUniqueAsync_Should_Return_True_When_Slug_Is_Unique()
    {
        // Arrange
        var content = new Content("Test Content", "test-content", ContentType.General, "Test body", Guid.NewGuid(), "User");
        await _context.Contents.AddAsync(content);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.IsSlugUniqueAsync("unique-slug");

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task IsSlugUniqueAsync_Should_Return_False_When_Slug_Exists()
    {
        // Arrange
        var content = new Content("Test Content", "test-content", ContentType.General, "Test body", Guid.NewGuid(), "User");
        await _context.Contents.AddAsync(content);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.IsSlugUniqueAsync("test-content");

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task IsSlugUniqueAsync_Should_Exclude_Specified_Id()
    {
        // Arrange
        var content = new Content("Test Content", "test-content", ContentType.General, "Test body", Guid.NewGuid(), "User");
        await _context.Contents.AddAsync(content);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.IsSlugUniqueAsync("test-content", content.Id);

        // Assert
        result.Should().BeTrue(); // Should be unique when excluding the same content
    }

    [Fact]
    public async Task GenerateUniqueSlugAsync_Should_Return_Unique_Slug()
    {
        // Arrange
        var content1 = new Content("Test Content", "test-content", ContentType.General, "Test body", Guid.NewGuid(), "User");
        var content2 = new Content("Test Content 1", "test-content-1", ContentType.General, "Test body", Guid.NewGuid(), "User");
        
        await _context.Contents.AddRangeAsync(content1, content2);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GenerateUniqueSlugAsync("test-content");

        // Assert
        result.Should().Be("test-content-2");
    }

    [Fact]
    public async Task GetContentCountByStatusAsync_Should_Return_Correct_Counts()
    {
        // Arrange
        var content1 = new Content("Draft Content", "draft-content", ContentType.General, "Draft body", Guid.NewGuid(), "User");
        var content2 = new Content("Published Content", "published-content", ContentType.General, "Published body", Guid.NewGuid(), "User");
        var content3 = new Content("Another Draft", "another-draft", ContentType.General, "Another draft body", Guid.NewGuid(), "User");

        content2.SubmitForReview(Guid.NewGuid(), "Reviewer");
        content2.Approve(Guid.NewGuid(), "Approver");
        content2.Publish(Guid.NewGuid(), "Publisher");

        await _context.Contents.AddRangeAsync(content1, content2, content3);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetContentCountByStatusAsync();

        // Assert
        result.Should().ContainKey(ContentStatus.Draft);
        result.Should().ContainKey(ContentStatus.Published);
        result[ContentStatus.Draft].Should().Be(2);
        result[ContentStatus.Published].Should().Be(1);
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
