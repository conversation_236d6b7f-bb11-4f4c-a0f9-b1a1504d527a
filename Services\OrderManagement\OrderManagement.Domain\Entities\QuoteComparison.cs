using Shared.Domain.Common;
using OrderManagement.Domain.ValueObjects;

namespace OrderManagement.Domain.Entities;

public class QuoteComparison : BaseEntity
{
    public Guid RfqId { get; private set; }
    public DateTime GeneratedAt { get; private set; }
    public Guid GeneratedBy { get; private set; }
    public string? RecommendedBidId { get; private set; }
    public string? RecommendationReason { get; private set; }
    public decimal? RecommendationScore { get; private set; }
    public Money LowestPrice { get; private set; } = null!;
    public Money HighestPrice { get; private set; } = null!;
    public Money AveragePrice { get; private set; } = null!;
    public DateTime EarliestPickup { get; private set; }
    public DateTime LatestPickup { get; private set; }
    public DateTime EarliestDelivery { get; private set; }
    public DateTime LatestDelivery { get; private set; }
    public decimal HighestRatedBroker { get; private set; }
    public decimal LowestRatedBroker { get; private set; }
    public decimal AverageRating { get; private set; }
    public int TotalQuotes { get; private set; }
    public int CounterOffers { get; private set; }
    public string? ComparisonNotes { get; private set; }

    // Navigation properties
    public RFQ Rfq { get; private set; } = null!;

    private readonly List<QuoteComparisonItem> _comparisonItems = new();
    public IReadOnlyCollection<QuoteComparisonItem> ComparisonItems => _comparisonItems.AsReadOnly();

    private readonly List<string> _comparisonCriteria = new();
    public IReadOnlyCollection<string> ComparisonCriteria => _comparisonCriteria.AsReadOnly();

    private QuoteComparison() { } // EF Constructor

    public QuoteComparison(
        Guid rfqId,
        Guid generatedBy,
        List<RfqBid> bids,
        List<string> comparisonCriteria,
        string? comparisonNotes = null)
    {
        if (!bids.Any())
            throw new ArgumentException("At least one bid is required for comparison", nameof(bids));

        RfqId = rfqId;
        GeneratedAt = DateTime.UtcNow;
        GeneratedBy = generatedBy;
        ComparisonNotes = comparisonNotes;
        TotalQuotes = bids.Count;
        CounterOffers = bids.Count(b => b.IsCounterOffer);

        // Calculate summary statistics
        CalculateSummaryStatistics(bids);

        // Create comparison items
        CreateComparisonItems(bids);

        // Set comparison criteria
        _comparisonCriteria.AddRange(comparisonCriteria);

        // Generate recommendation
        GenerateRecommendation();
    }

    private void CalculateSummaryStatistics(List<RfqBid> bids)
    {
        var prices = bids.Select(b => b.QuotedPrice).ToList();
        var pickupDates = bids.Select(b => b.EstimatedPickupDate).ToList();
        var deliveryDates = bids.Select(b => b.EstimatedDeliveryDate).ToList();

        LowestPrice = prices.OrderBy(p => p.Amount).First();
        HighestPrice = prices.OrderByDescending(p => p.Amount).First();

        var avgAmount = prices.Average(p => p.Amount);
        AveragePrice = new Money(avgAmount, LowestPrice.Currency);

        EarliestPickup = pickupDates.Min();
        LatestPickup = pickupDates.Max();
        EarliestDelivery = deliveryDates.Min();
        LatestDelivery = deliveryDates.Max();

        // Note: Broker ratings would need to be fetched from external service
        // For now, using placeholder values
        HighestRatedBroker = 5.0m;
        LowestRatedBroker = 3.0m;
        AverageRating = 4.0m;
    }

    private void CreateComparisonItems(List<RfqBid> bids)
    {
        foreach (var bid in bids)
        {
            var item = new QuoteComparisonItem(
                Id,
                bid.Id,
                bid.BrokerId,
                bid.QuotedPrice,
                bid.EstimatedPickupDate,
                bid.EstimatedDeliveryDate,
                bid.VehicleDetails,
                bid.DriverDetails,
                bid.AdditionalServices,
                bid.IsCounterOffer,
                bid.OriginalBidId);

            _comparisonItems.Add(item);
        }
    }

    private void GenerateRecommendation()
    {
        if (!_comparisonItems.Any()) return;

        // Simple recommendation logic - can be enhanced with ML/AI
        var bestItem = _comparisonItems
            .OrderBy(item => item.CalculateScore())
            .First();

        RecommendedBidId = bestItem.BidId.ToString();
        RecommendationScore = bestItem.CalculateScore();
        RecommendationReason = GenerateRecommendationReason(bestItem);

        bestItem.MarkAsRecommended();
    }

    private string GenerateRecommendationReason(QuoteComparisonItem bestItem)
    {
        var reasons = new List<string>();

        if (bestItem.QuotedPrice.Amount == LowestPrice.Amount)
            reasons.Add("Lowest price");

        if (bestItem.EstimatedPickupDate == EarliestPickup)
            reasons.Add("Earliest pickup");

        if (bestItem.EstimatedDeliveryDate == EarliestDelivery)
            reasons.Add("Earliest delivery");

        if (!string.IsNullOrEmpty(bestItem.AdditionalServices))
            reasons.Add("Additional services offered");

        return reasons.Any() ? string.Join(", ", reasons) : "Best overall value";
    }

    public void UpdateRecommendation(string bidId, string reason, decimal? score = null)
    {
        // Clear previous recommendation
        foreach (var item in _comparisonItems)
        {
            item.ClearRecommendation();
        }

        // Set new recommendation
        var recommendedItem = _comparisonItems.FirstOrDefault(i => i.BidId.ToString() == bidId);
        if (recommendedItem != null)
        {
            recommendedItem.MarkAsRecommended();
            RecommendedBidId = bidId;
            RecommendationReason = reason;
            RecommendationScore = score;
        }
    }

    public void AddComparisonCriterion(string criterion)
    {
        if (!string.IsNullOrWhiteSpace(criterion) && !_comparisonCriteria.Contains(criterion))
        {
            _comparisonCriteria.Add(criterion);
        }
    }

    public void RemoveComparisonCriterion(string criterion)
    {
        _comparisonCriteria.Remove(criterion);
    }

    public QuoteComparisonItem? GetRecommendedItem()
    {
        return _comparisonItems.FirstOrDefault(i => i.IsRecommended);
    }

    public Money GetPriceDifference()
    {
        return new Money(HighestPrice.Amount - LowestPrice.Amount, LowestPrice.Currency);
    }

    public TimeSpan GetDeliveryTimeRange()
    {
        return LatestDelivery - EarliestDelivery;
    }
}

public class QuoteComparisonItem : BaseEntity
{
    public Guid QuoteComparisonId { get; private set; }
    public Guid BidId { get; private set; }
    public Guid BrokerId { get; private set; }
    public Money QuotedPrice { get; private set; } = null!;
    public DateTime EstimatedPickupDate { get; private set; }
    public DateTime EstimatedDeliveryDate { get; private set; }
    public string? VehicleDetails { get; private set; }
    public string? DriverDetails { get; private set; }
    public string? AdditionalServices { get; private set; }
    public bool IsCounterOffer { get; private set; }
    public Guid? OriginalBidId { get; private set; }
    public bool IsRecommended { get; private set; }
    public decimal? RecommendationScore { get; private set; }

    // Navigation properties
    public QuoteComparison QuoteComparison { get; private set; } = null!;

    private readonly List<string> _advantages = new();
    public IReadOnlyCollection<string> Advantages => _advantages.AsReadOnly();

    private readonly List<string> _disadvantages = new();
    public IReadOnlyCollection<string> Disadvantages => _disadvantages.AsReadOnly();

    private QuoteComparisonItem() { } // EF Constructor

    public QuoteComparisonItem(
        Guid quoteComparisonId,
        Guid bidId,
        Guid brokerId,
        Money quotedPrice,
        DateTime estimatedPickupDate,
        DateTime estimatedDeliveryDate,
        string? vehicleDetails = null,
        string? driverDetails = null,
        string? additionalServices = null,
        bool isCounterOffer = false,
        Guid? originalBidId = null)
    {
        QuoteComparisonId = quoteComparisonId;
        BidId = bidId;
        BrokerId = brokerId;
        QuotedPrice = quotedPrice ?? throw new ArgumentNullException(nameof(quotedPrice));
        EstimatedPickupDate = estimatedPickupDate;
        EstimatedDeliveryDate = estimatedDeliveryDate;
        VehicleDetails = vehicleDetails;
        DriverDetails = driverDetails;
        AdditionalServices = additionalServices;
        IsCounterOffer = isCounterOffer;
        OriginalBidId = originalBidId;
        IsRecommended = false;
    }

    public void MarkAsRecommended(decimal? score = null)
    {
        IsRecommended = true;
        RecommendationScore = score;
    }

    public void ClearRecommendation()
    {
        IsRecommended = false;
        RecommendationScore = null;
    }

    public void AddAdvantage(string advantage)
    {
        if (!string.IsNullOrWhiteSpace(advantage) && !_advantages.Contains(advantage))
        {
            _advantages.Add(advantage);
        }
    }

    public void AddDisadvantage(string disadvantage)
    {
        if (!string.IsNullOrWhiteSpace(disadvantage) && !_disadvantages.Contains(disadvantage))
        {
            _disadvantages.Add(disadvantage);
        }
    }

    public void RemoveAdvantage(string advantage)
    {
        _advantages.Remove(advantage);
    }

    public void RemoveDisadvantage(string disadvantage)
    {
        _disadvantages.Remove(disadvantage);
    }

    public decimal CalculateScore()
    {
        // Simple scoring algorithm - can be enhanced
        decimal score = 0;

        // Price factor (lower is better)
        score += (1000 - (decimal)QuotedPrice.Amount) / 100;

        // Time factor (earlier is better)
        var daysFromNow = (EstimatedPickupDate - DateTime.UtcNow).Days;
        score += Math.Max(0, 30 - daysFromNow);

        // Additional services bonus
        if (!string.IsNullOrEmpty(AdditionalServices))
            score += 10;

        // Advantages/disadvantages
        score += _advantages.Count * 5;
        score -= _disadvantages.Count * 3;

        return Math.Max(0, score);
    }

    public TimeSpan GetEstimatedDuration()
    {
        return EstimatedDeliveryDate - EstimatedPickupDate;
    }
}
