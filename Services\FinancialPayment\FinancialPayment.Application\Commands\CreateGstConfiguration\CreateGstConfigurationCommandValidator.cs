using FluentValidation;
using FinancialPayment.Domain.Enums;

namespace FinancialPayment.Application.Commands.CreateGstConfiguration;

public class CreateGstConfigurationCommandValidator : AbstractValidator<CreateGstConfigurationCommand>
{
    public CreateGstConfigurationCommandValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage("GST configuration name is required")
            .MaximumLength(200)
            .WithMessage("GST configuration name cannot exceed 200 characters");

        RuleFor(x => x.Description)
            .NotEmpty()
            .WithMessage("GST configuration description is required")
            .MaximumLength(1000)
            .WithMessage("GST configuration description cannot exceed 1000 characters");

        RuleFor(x => x.ServiceCategory)
            .IsInEnum()
            .WithMessage("Valid service category is required");

        RuleFor(x => x.Jurisdiction)
            .NotNull()
            .WithMessage("Jurisdiction is required");

        RuleFor(x => x.Jurisdiction.Country)
            .NotEmpty()
            .WithMessage("Country is required")
            .MaximumLength(100)
            .WithMessage("Country name cannot exceed 100 characters")
            .When(x => x.Jurisdiction != null);

        RuleFor(x => x.Jurisdiction.State)
            .MaximumLength(100)
            .WithMessage("State name cannot exceed 100 characters")
            .When(x => x.Jurisdiction != null);

        RuleFor(x => x.Jurisdiction.City)
            .MaximumLength(100)
            .WithMessage("City name cannot exceed 100 characters")
            .When(x => x.Jurisdiction != null);

        RuleFor(x => x.GstRate)
            .IsInEnum()
            .WithMessage("Valid GST rate is required");

        RuleFor(x => x.TaxRate)
            .NotNull()
            .WithMessage("Tax rate is required");

        RuleFor(x => x.TaxRate.Rate)
            .GreaterThanOrEqualTo(0)
            .WithMessage("Tax rate cannot be negative")
            .LessThanOrEqualTo(100)
            .WithMessage("Tax rate cannot exceed 100%")
            .When(x => x.TaxRate != null);

        RuleFor(x => x.TaxRate.CalculationMethod)
            .IsInEnum()
            .WithMessage("Valid calculation method is required")
            .When(x => x.TaxRate != null);

        RuleFor(x => x.TaxRate.EffectiveFrom)
            .NotEmpty()
            .WithMessage("Tax rate effective from date is required")
            .When(x => x.TaxRate != null);

        RuleFor(x => x.TaxRate.EffectiveTo)
            .GreaterThan(x => x.TaxRate.EffectiveFrom)
            .WithMessage("Tax rate effective to date must be after effective from date")
            .When(x => x.TaxRate != null && x.TaxRate.EffectiveTo.HasValue);

        RuleFor(x => x.MinimumAmount)
            .NotNull()
            .WithMessage("Minimum amount is required");

        RuleFor(x => x.MinimumAmount.Amount)
            .GreaterThanOrEqualTo(0)
            .WithMessage("Minimum amount cannot be negative")
            .When(x => x.MinimumAmount != null);

        RuleFor(x => x.MinimumAmount.Currency)
            .NotEmpty()
            .WithMessage("Minimum amount currency is required")
            .Length(3)
            .WithMessage("Currency code must be 3 characters")
            .When(x => x.MinimumAmount != null);

        RuleFor(x => x.MaximumAmount)
            .NotNull()
            .WithMessage("Maximum amount is required");

        RuleFor(x => x.MaximumAmount.Amount)
            .GreaterThanOrEqualTo(0)
            .WithMessage("Maximum amount cannot be negative")
            .GreaterThanOrEqualTo(x => x.MinimumAmount.Amount)
            .WithMessage("Maximum amount must be greater than or equal to minimum amount")
            .When(x => x.MaximumAmount != null && x.MinimumAmount != null);

        RuleFor(x => x.MaximumAmount.Currency)
            .NotEmpty()
            .WithMessage("Maximum amount currency is required")
            .Length(3)
            .WithMessage("Currency code must be 3 characters")
            .Equal(x => x.MinimumAmount.Currency)
            .WithMessage("Maximum amount currency must match minimum amount currency")
            .When(x => x.MaximumAmount != null && x.MinimumAmount != null);

        RuleFor(x => x.CreatedBy)
            .NotEmpty()
            .WithMessage("Created by is required")
            .MaximumLength(100)
            .WithMessage("Created by cannot exceed 100 characters");

        RuleFor(x => x.HsnCode)
            .MaximumLength(20)
            .WithMessage("HSN code cannot exceed 20 characters")
            .Matches(@"^\d{4,8}$")
            .WithMessage("HSN code must be 4-8 digits")
            .When(x => !string.IsNullOrEmpty(x.HsnCode));

        RuleFor(x => x.ReverseChargeConditions)
            .MaximumLength(1000)
            .WithMessage("Reverse charge conditions cannot exceed 1000 characters")
            .When(x => !string.IsNullOrEmpty(x.ReverseChargeConditions));

        // Custom validation to ensure GST rate matches tax rate
        RuleFor(x => x)
            .Must(x => ValidateGstRateConsistency(x.GstRate, x.TaxRate?.Rate ?? 0))
            .WithMessage("Tax rate must match the selected GST rate")
            .When(x => x.TaxRate != null);
    }

    private static bool ValidateGstRateConsistency(GstRate gstRate, decimal taxRate)
    {
        var expectedRate = (decimal)gstRate;
        return Math.Abs(taxRate - expectedRate) < 0.01m;
    }
}
