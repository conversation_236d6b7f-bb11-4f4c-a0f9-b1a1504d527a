using MediatR;

namespace OrderManagement.Application.Commands.UpdateMilestonePayoutStructure;

public class UpdateMilestonePayoutStructureCommand : IRequest<bool>
{
    public Guid RfqMilestoneAssignmentId { get; set; }
    public string CustomPayoutStructure { get; set; } = string.Empty;
    public List<MilestoneStepPayoutUpdate> StepPayoutUpdates { get; set; } = new();
    public Guid UpdatedBy { get; set; }
    public string? UpdatedByName { get; set; }
    public string? UpdateReason { get; set; }
    public bool CreateTimelineEvent { get; set; } = true;
}

public class MilestoneStepPayoutUpdate
{
    public Guid StepId { get; set; }
    public decimal NewPayoutPercentage { get; set; }
    public string? Reason { get; set; }
}
