using System;
using System.Threading.Tasks;

namespace Identity.Application.Common.Interfaces
{
    public interface ITwoFactorAuthService
    {
        Task<string> GenerateTwoFactorTokenAsync(Guid userId);
        Task<bool> ValidateTwoFactorTokenAsync(Guid userId, string token);
        Task<string> GenerateQrCodeUriAsync(Guid userId, string email);
        Task<bool> IsTwoFactorEnabledAsync(Guid userId);
        Task EnableTwoFactorAsync(Guid userId);
        Task DisableTwoFactorAsync(Guid userId);
    }
}
