using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Application.Interfaces;

public interface IPaymentRepository
{
    Task<Payment?> GetByIdAsync(Guid id);
    Task<List<Payment>> GetBySubscriptionIdAsync(Guid subscriptionId);
    Task<List<Payment>> GetByUserIdAsync(Guid userId);
    Task<List<Payment>> GetByStatusAsync(PaymentStatus status);
    Task<Payment?> GetByTransactionIdAsync(string transactionId);
    Task<List<Payment>> GetFailedPaymentsAsync();
    Task<List<Payment>> GetPendingRetryPaymentsAsync();
    Task<Payment> AddAsync(Payment payment);
    Task UpdateAsync(Payment payment);
    Task DeleteAsync(Guid id);
    Task<List<Payment>> GetPaymentHistoryAsync(Guid userId, DateTime? from = null, DateTime? to = null);
    Task<decimal> GetTotalRevenueAsync(DateTime? from = null, DateTime? to = null);
    Task<Dictionary<string, decimal>> GetRevenueByPeriodAsync(DateTime from, DateTime to, string groupBy = "month");
}
