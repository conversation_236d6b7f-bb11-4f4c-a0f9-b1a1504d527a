using Shared.Domain.Common;
using AuditCompliance.Domain.Enums;

namespace AuditCompliance.Domain.Entities;

/// <summary>
/// Individual item within a compliance report
/// </summary>
public class ComplianceReportItem : BaseEntity
{
    public Guid ComplianceReportId { get; private set; }
    public string CheckName { get; private set; }
    public string Description { get; private set; }
    public bool IsViolation { get; private set; }
    public AuditSeverity Severity { get; private set; }
    public string? Details { get; private set; }
    public string? Recommendation { get; private set; }
    public string? Evidence { get; private set; }
    public DateTime CheckedAt { get; private set; }
    public string? ReferenceData { get; private set; }

    // Navigation property
    public ComplianceReport ComplianceReport { get; private set; } = null!;

    private ComplianceReportItem() 
    {
        CheckName = string.Empty;
        Description = string.Empty;
    }

    public ComplianceReportItem(
        Guid complianceReportId,
        string checkName,
        string description,
        bool isViolation,
        AuditSeverity severity,
        string? details = null,
        string? recommendation = null,
        string? evidence = null,
        string? referenceData = null)
    {
        if (string.IsNullOrWhiteSpace(checkName))
            throw new ArgumentException("Check name cannot be empty", nameof(checkName));
        if (string.IsNullOrWhiteSpace(description))
            throw new ArgumentException("Description cannot be empty", nameof(description));

        ComplianceReportId = complianceReportId;
        CheckName = checkName;
        Description = description;
        IsViolation = isViolation;
        Severity = severity;
        Details = details;
        Recommendation = recommendation;
        Evidence = evidence;
        CheckedAt = DateTime.UtcNow;
        ReferenceData = referenceData;
    }

    public static ComplianceReportItem CreateCompliant(
        Guid complianceReportId,
        string checkName,
        string description,
        string? evidence = null)
    {
        return new ComplianceReportItem(
            complianceReportId,
            checkName,
            description,
            isViolation: false,
            AuditSeverity.Info,
            details: "Check passed successfully",
            evidence: evidence);
    }

    public static ComplianceReportItem CreateViolation(
        Guid complianceReportId,
        string checkName,
        string description,
        AuditSeverity severity,
        string details,
        string? recommendation = null,
        string? evidence = null)
    {
        return new ComplianceReportItem(
            complianceReportId,
            checkName,
            description,
            isViolation: true,
            severity,
            details,
            recommendation,
            evidence);
    }

    public void UpdateRecommendation(string recommendation)
    {
        if (string.IsNullOrWhiteSpace(recommendation))
            throw new ArgumentException("Recommendation cannot be empty", nameof(recommendation));

        Recommendation = recommendation;
        SetUpdatedAt();
    }

    public void AddEvidence(string evidence)
    {
        if (string.IsNullOrWhiteSpace(evidence))
            throw new ArgumentException("Evidence cannot be empty", nameof(evidence));

        Evidence = string.IsNullOrEmpty(Evidence) 
            ? evidence 
            : $"{Evidence}\n\n{evidence}";
        
        SetUpdatedAt();
    }
}
