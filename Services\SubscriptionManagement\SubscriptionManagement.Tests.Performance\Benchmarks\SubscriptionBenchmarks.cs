using BenchmarkDotNet.Attributes;
using BenchmarkDotNet.Configs;
using BenchmarkDotNet.Jobs;
using BenchmarkDotNet.Toolchains.InProcess.Emit;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.Commands.CreateSubscription;
using SubscriptionManagement.Application.Queries.GetSubscription;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Infrastructure.Services;

namespace SubscriptionManagement.Tests.Performance.Benchmarks
{
    [Config(typeof(Config))]
    [MemoryDiagnoser]
    [SimpleJob(RuntimeMoniker.Net80)]
    public class SubscriptionBenchmarks
    {
        private IServiceProvider _serviceProvider = null!;
        private List<Guid> _subscriptionIds = null!;
        private List<Guid> _planIds = null!;

        private class Config : ManualConfig
        {
            public Config()
            {
                AddJob(Job.Default.WithToolchain(InProcessEmitToolchain.Instance));
            }
        }

        [GlobalSetup]
        public void Setup()
        {
            var services = new ServiceCollection();
            
            // Add logging
            services.AddLogging(builder => builder.AddConsole());
            
            // Add your subscription management services here
            // This would typically include your real DI setup
            services.AddScoped<ISubscriptionMetricsService, SubscriptionMetricsService>();
            
            _serviceProvider = services.BuildServiceProvider();
            
            // Generate test data
            _subscriptionIds = GenerateTestIds(1000);
            _planIds = GenerateTestIds(10);
        }

        [GlobalCleanup]
        public void Cleanup()
        {
            _serviceProvider?.Dispose();
        }

        [Benchmark]
        [Arguments(1)]
        [Arguments(10)]
        [Arguments(100)]
        public async Task CreateSubscriptions(int count)
        {
            using var scope = _serviceProvider.CreateScope();
            var mediator = scope.ServiceProvider.GetRequiredService<MediatR.IMediator>();

            var tasks = new List<Task>();
            for (int i = 0; i < count; i++)
            {
                var command = new CreateSubscriptionCommand
                {
                    UserId = Guid.NewGuid(),
                    PlanId = _planIds[i % _planIds.Count],
                    PaymentMethodId = "benchmark_payment_method",
                    StartDate = DateTime.UtcNow,
                    AutoRenew = true,
                    ProrationMode = ProrationMode.CreateProrations
                };

                tasks.Add(mediator.Send(command));
            }

            await Task.WhenAll(tasks);
        }

        [Benchmark]
        [Arguments(1)]
        [Arguments(10)]
        [Arguments(100)]
        public async Task GetSubscriptions(int count)
        {
            using var scope = _serviceProvider.CreateScope();
            var mediator = scope.ServiceProvider.GetRequiredService<MediatR.IMediator>();

            var tasks = new List<Task>();
            for (int i = 0; i < count; i++)
            {
                var query = new GetSubscriptionQuery 
                { 
                    SubscriptionId = _subscriptionIds[i % _subscriptionIds.Count] 
                };

                tasks.Add(mediator.Send(query));
            }

            await Task.WhenAll(tasks);
        }

        [Benchmark]
        public async Task CachePerformanceTest()
        {
            using var scope = _serviceProvider.CreateScope();
            var cacheService = scope.ServiceProvider.GetRequiredService<ISubscriptionCacheService>();

            // Test cache set/get performance
            var testPlan = new SubscriptionManagement.Domain.Entities.Plan(
                "Benchmark Plan",
                "Test plan for benchmarking",
                SubscriptionManagement.Domain.ValueObjects.Money.Create(99.99m, "USD"),
                BillingCycle.Monthly,
                PlanType.Premium,
                UserType.Individual
            );

            // Set operation
            await cacheService.SetPlanAsync(testPlan);

            // Get operation
            var retrievedPlan = await cacheService.GetPlanAsync(testPlan.Id);

            // Cleanup
            await cacheService.RemovePlanAsync(testPlan.Id);
        }

        [Benchmark]
        public void MetricsRecording()
        {
            using var scope = _serviceProvider.CreateScope();
            var metricsService = scope.ServiceProvider.GetRequiredService<ISubscriptionMetricsService>();

            // Test metrics recording performance
            metricsService.RecordSubscriptionCreated("Benchmark Plan", 99.99m, "USD");
            metricsService.RecordPaymentSuccess(99.99m, "USD", "credit_card");
            metricsService.RecordFeatureUsage("api_calls", 1, Guid.NewGuid());
            metricsService.RecordApiCall("/api/subscriptions", TimeSpan.FromMilliseconds(100), true);
        }

        [Benchmark]
        [Arguments(100)]
        [Arguments(1000)]
        [Arguments(10000)]
        public async Task ConcurrentSubscriptionCreation(int concurrency)
        {
            using var scope = _serviceProvider.CreateScope();
            var mediator = scope.ServiceProvider.GetRequiredService<MediatR.IMediator>();

            var semaphore = new SemaphoreSlim(concurrency);
            var tasks = new List<Task>();

            for (int i = 0; i < concurrency; i++)
            {
                tasks.Add(Task.Run(async () =>
                {
                    await semaphore.WaitAsync();
                    try
                    {
                        var command = new CreateSubscriptionCommand
                        {
                            UserId = Guid.NewGuid(),
                            PlanId = _planIds[i % _planIds.Count],
                            PaymentMethodId = "concurrent_test",
                            StartDate = DateTime.UtcNow,
                            AutoRenew = true
                        };

                        await mediator.Send(command);
                    }
                    finally
                    {
                        semaphore.Release();
                    }
                }));
            }

            await Task.WhenAll(tasks);
        }

        [Benchmark]
        public async Task DatabaseVsCacheComparison()
        {
            using var scope = _serviceProvider.CreateScope();
            var cacheService = scope.ServiceProvider.GetRequiredService<ISubscriptionCacheService>();

            var planId = _planIds.First();

            // Simulate cache miss - direct database access
            var dbPlan = await GetPlanFromDatabase(planId);

            // Simulate cache hit
            if (dbPlan != null)
            {
                await cacheService.SetPlanAsync(dbPlan);
                var cachedPlan = await cacheService.GetPlanAsync(planId);
            }
        }

        [Benchmark]
        public async Task FeatureFlagEvaluation()
        {
            using var scope = _serviceProvider.CreateScope();
            var featureFlagService = scope.ServiceProvider.GetRequiredService<IFeatureFlagService>();

            var userId = Guid.NewGuid();
            var context = new Dictionary<string, object>
            {
                ["user_type"] = "premium",
                ["region"] = "US"
            };

            // Test feature flag evaluation performance
            var isEnabled = await featureFlagService.IsEnabledAsync("test_feature", userId, context);
            var variant = await featureFlagService.GetVariantAsync("ab_test_feature", userId, context);
        }

        private List<Guid> GenerateTestIds(int count)
        {
            var ids = new List<Guid>();
            for (int i = 0; i < count; i++)
            {
                ids.Add(Guid.NewGuid());
            }
            return ids;
        }

        private async Task<SubscriptionManagement.Domain.Entities.Plan?> GetPlanFromDatabase(Guid planId)
        {
            // Simulate database access
            await Task.Delay(10); // Simulate DB latency
            
            return new SubscriptionManagement.Domain.Entities.Plan(
                "Database Plan",
                "Plan loaded from database",
                SubscriptionManagement.Domain.ValueObjects.Money.Create(49.99m, "USD"),
                BillingCycle.Monthly,
                PlanType.Basic,
                UserType.Individual
            );
        }
    }
}
