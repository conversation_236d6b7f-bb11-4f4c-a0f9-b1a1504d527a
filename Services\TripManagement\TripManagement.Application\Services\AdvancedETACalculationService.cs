using Microsoft.Extensions.Logging;
using TripManagement.Domain.Entities;
using TripManagement.Domain.ValueObjects;
using TripManagement.Domain.Enums;
using TripManagement.Application.DTOs;
using TripManagement.Application.Interfaces;

namespace TripManagement.Application.Services;

public interface IAdvancedETACalculationService
{
    Task<ETACalculationResult> CalculateETAAsync(ETACalculationRequest request, CancellationToken cancellationToken = default);
    Task<ETACalculationResult> RecalculateETAAsync(Guid tripId, Location currentLocation, CancellationToken cancellationToken = default);
    Task<List<MilestoneETA>> CalculateMilestoneETAsAsync(ETACalculationRequest request, CancellationToken cancellationToken = default);
    Task<ETAAccuracyMetrics> GetETAAccuracyMetricsAsync(Guid tripId, CancellationToken cancellationToken = default);
}

public class AdvancedETACalculationService : IAdvancedETACalculationService
{
    private readonly ILogger<AdvancedETACalculationService> _logger;
    private readonly ITrafficService _trafficService;
    private readonly IWeatherService _weatherService;
    private readonly IDriverBehaviorService _driverBehaviorService;
    private readonly IRouteOptimizationService _routeOptimizationService;
    private readonly ITripRepository _tripRepository;

    public AdvancedETACalculationService(
        ILogger<AdvancedETACalculationService> logger,
        ITrafficService trafficService,
        IWeatherService weatherService,
        IDriverBehaviorService driverBehaviorService,
        IRouteOptimizationService routeOptimizationService,
        ITripRepository tripRepository)
    {
        _logger = logger;
        _trafficService = trafficService;
        _weatherService = weatherService;
        _driverBehaviorService = driverBehaviorService;
        _routeOptimizationService = routeOptimizationService;
        _tripRepository = tripRepository;
    }

    public async Task<ETACalculationResult> CalculateETAAsync(ETACalculationRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Calculating ETA for trip {TripId} using method {Method}",
                request.TripId, request.CalculationMethod);

            var result = new ETACalculationResult
            {
                TripId = request.TripId,
                CalculationMethod = request.CalculationMethod,
                CalculatedAt = DateTime.UtcNow,
                RequestId = Guid.NewGuid()
            };

            // Get base calculation
            var baseETA = await CalculateBaseETAAsync(request, cancellationToken);
            result.BaseETA = baseETA;

            // Apply adjustments based on calculation method
            switch (request.CalculationMethod)
            {
                case ETACalculationMethod.Simple:
                    result.FinalETA = baseETA;
                    break;

                case ETACalculationMethod.TrafficAware:
                    result.FinalETA = await ApplyTrafficAdjustmentsAsync(request, baseETA, cancellationToken);
                    break;

                case ETACalculationMethod.HistoricalData:
                    result.FinalETA = await ApplyHistoricalDataAdjustmentsAsync(request, baseETA, cancellationToken);
                    break;

                case ETACalculationMethod.MachineLearning:
                    result.FinalETA = await ApplyMLAdjustmentsAsync(request, baseETA, cancellationToken);
                    break;

                case ETACalculationMethod.Hybrid:
                    result.FinalETA = await ApplyHybridAdjustmentsAsync(request, baseETA, cancellationToken);
                    break;

                default:
                    result.FinalETA = baseETA;
                    break;
            }

            // Calculate confidence score
            result.ConfidenceScore = CalculateConfidenceScore(request, result);

            // Add factors that influenced the calculation
            result.InfluencingFactors = await GetInfluencingFactorsAsync(request, cancellationToken);

            _logger.LogInformation("ETA calculated for trip {TripId}: {ETA} (confidence: {Confidence}%)",
                request.TripId, result.FinalETA, result.ConfidenceScore);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating ETA for trip {TripId}", request.TripId);
            throw;
        }
    }

    public async Task<ETACalculationResult> RecalculateETAAsync(Guid tripId, Location currentLocation, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Recalculating ETA for trip {TripId} from current location", tripId);

            var trip = await _tripRepository.GetByIdAsync(tripId, cancellationToken);
            if (trip == null)
                throw new ArgumentException($"Trip {tripId} not found");

            var request = new ETACalculationRequest
            {
                TripId = tripId,
                CurrentLocation = currentLocation,
                DestinationLocation = trip.Route.EndLocation,
                RemainingStops = trip.Stops.Where(s => s.Status == TripStopStatus.Pending).ToList(),
                VehicleType = trip.Vehicle?.VehicleType,
                DriverId = trip.DriverId,
                CalculationMethod = ETACalculationMethod.Hybrid,
                ConsiderTraffic = true,
                ConsiderWeather = true,
                ConsiderDriverBehavior = true
            };

            return await CalculateETAAsync(request, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recalculating ETA for trip {TripId}", tripId);
            throw;
        }
    }

    public async Task<List<MilestoneETA>> CalculateMilestoneETAsAsync(ETACalculationRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Calculating milestone ETAs for trip {TripId}", request.TripId);

            var milestoneETAs = new List<MilestoneETA>();
            var currentLocation = request.CurrentLocation;
            var cumulativeTime = TimeSpan.Zero;

            foreach (var stop in request.RemainingStops.OrderBy(s => s.SequenceNumber))
            {
                var stopRequest = new ETACalculationRequest
                {
                    TripId = request.TripId,
                    CurrentLocation = currentLocation,
                    DestinationLocation = stop.Location,
                    CalculationMethod = request.CalculationMethod,
                    ConsiderTraffic = request.ConsiderTraffic,
                    ConsiderWeather = request.ConsiderWeather,
                    ConsiderDriverBehavior = request.ConsiderDriverBehavior
                };

                var stopETA = await CalculateETAAsync(stopRequest, cancellationToken);
                cumulativeTime += stopETA.FinalETA - DateTime.UtcNow;

                milestoneETAs.Add(new MilestoneETA
                {
                    StopId = stop.Id,
                    StopType = stop.StopType,
                    Location = stop.Location,
                    EstimatedArrival = DateTime.UtcNow + cumulativeTime,
                    ConfidenceScore = stopETA.ConfidenceScore,
                    DistanceFromCurrentKm = currentLocation.CalculateDistanceKm(stop.Location)
                });

                // Add estimated stop duration
                cumulativeTime += stop.EstimatedDuration ?? TimeSpan.FromMinutes(15);
                currentLocation = stop.Location;
            }

            return milestoneETAs;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating milestone ETAs for trip {TripId}", request.TripId);
            throw;
        }
    }

    public async Task<ETAAccuracyMetrics> GetETAAccuracyMetricsAsync(Guid tripId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Getting ETA accuracy metrics for trip {TripId}", tripId);

            // This would typically query historical ETA predictions vs actual arrival times
            // For now, return a placeholder implementation
            return new ETAAccuracyMetrics
            {
                TripId = tripId,
                TotalPredictions = 0,
                AverageAccuracyPercentage = 0,
                MedianErrorMinutes = 0,
                StandardDeviationMinutes = 0,
                CalculatedAt = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting ETA accuracy metrics for trip {TripId}", tripId);
            throw;
        }
    }

    private async Task<DateTime> CalculateBaseETAAsync(ETACalculationRequest request, CancellationToken cancellationToken)
    {
        // Simple distance/speed calculation
        var distance = request.CurrentLocation.CalculateDistanceKm(request.DestinationLocation);
        var averageSpeed = 60.0; // km/h default
        var travelTimeHours = distance / averageSpeed;

        return DateTime.UtcNow.AddHours(travelTimeHours);
    }

    private async Task<DateTime> ApplyTrafficAdjustmentsAsync(ETACalculationRequest request, DateTime baseETA, CancellationToken cancellationToken)
    {
        if (!request.ConsiderTraffic)
            return baseETA;

        try
        {
            var trafficData = await _trafficService.GetTrafficConditionsAsync(
                request.CurrentLocation,
                request.DestinationLocation,
                cancellationToken);

            var trafficMultiplier = trafficData.CongestionLevel switch
            {
                "Low" => 1.0,
                "Medium" => 1.2,
                "High" => 1.5,
                "Severe" => 2.0,
                _ => 1.1
            };

            var additionalTime = (baseETA - DateTime.UtcNow).TotalMinutes * (trafficMultiplier - 1.0);
            return baseETA.AddMinutes(additionalTime);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to get traffic data, using base ETA");
            return baseETA;
        }
    }

    private async Task<DateTime> ApplyHistoricalDataAdjustmentsAsync(ETACalculationRequest request, DateTime baseETA, CancellationToken cancellationToken)
    {
        // Apply historical data adjustments based on similar trips
        // This would query historical trip data for similar routes/times/conditions
        var historicalMultiplier = 1.1; // Placeholder
        var additionalTime = (baseETA - DateTime.UtcNow).TotalMinutes * (historicalMultiplier - 1.0);
        return baseETA.AddMinutes(additionalTime);
    }

    private async Task<DateTime> ApplyMLAdjustmentsAsync(ETACalculationRequest request, DateTime baseETA, CancellationToken cancellationToken)
    {
        // Apply machine learning model predictions
        // This would call an ML service with trip features
        var mlMultiplier = 1.05; // Placeholder
        var additionalTime = (baseETA - DateTime.UtcNow).TotalMinutes * (mlMultiplier - 1.0);
        return baseETA.AddMinutes(additionalTime);
    }

    private async Task<DateTime> ApplyHybridAdjustmentsAsync(ETACalculationRequest request, DateTime baseETA, CancellationToken cancellationToken)
    {
        // Combine multiple adjustment methods
        var trafficETA = await ApplyTrafficAdjustmentsAsync(request, baseETA, cancellationToken);
        var historicalETA = await ApplyHistoricalDataAdjustmentsAsync(request, baseETA, cancellationToken);
        var mlETA = await ApplyMLAdjustmentsAsync(request, baseETA, cancellationToken);

        // Weighted average of different methods
        var weights = new[] { 0.4, 0.3, 0.3 }; // Traffic, Historical, ML
        var etas = new[] { trafficETA, historicalETA, mlETA };

        var weightedTicks = etas.Zip(weights, (eta, weight) => eta.Ticks * weight).Sum();
        return new DateTime((long)weightedTicks);
    }

    private double CalculateConfidenceScore(ETACalculationRequest request, ETACalculationResult result)
    {
        var baseConfidence = 70.0; // Base confidence percentage

        // Adjust based on factors
        if (request.ConsiderTraffic) baseConfidence += 10;
        if (request.ConsiderWeather) baseConfidence += 5;
        if (request.ConsiderDriverBehavior) baseConfidence += 5;
        if (request.CalculationMethod == ETACalculationMethod.Hybrid) baseConfidence += 10;

        return Math.Min(baseConfidence, 95.0); // Cap at 95%
    }

    private async Task<List<string>> GetInfluencingFactorsAsync(ETACalculationRequest request, CancellationToken cancellationToken)
    {
        var factors = new List<string>();

        if (request.ConsiderTraffic)
            factors.Add("Traffic conditions");

        if (request.ConsiderWeather)
            factors.Add("Weather conditions");

        if (request.ConsiderDriverBehavior)
            factors.Add("Driver behavior patterns");

        factors.Add($"Calculation method: {request.CalculationMethod}");

        return factors;
    }
}
