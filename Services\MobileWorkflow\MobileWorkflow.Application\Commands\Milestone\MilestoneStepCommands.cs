using MediatR;
using MobileWorkflow.Application.DTOs;

namespace MobileWorkflow.Application.Commands.Milestone;

public class CreateMilestoneStepCommand : IRequest<MilestoneStepDto>
{
    public Guid MilestoneTemplateId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int SequenceNumber { get; set; }
    public bool IsRequired { get; set; } = true;
    public string? TriggerCondition { get; set; }
    public Dictionary<string, object> Configuration { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
    public List<CreateMilestonePayoutRuleRequest> PayoutRules { get; set; } = new();
}

public class UpdateMilestoneStepCommand : IRequest<MilestoneStepDto>
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public bool IsRequired { get; set; }
    public string? TriggerCondition { get; set; }
    public Dictionary<string, object> Configuration { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class DeleteMilestoneStepCommand : IRequest<bool>
{
    public Guid Id { get; set; }
}

public class ActivateMilestoneStepCommand : IRequest<MilestoneStepDto>
{
    public Guid Id { get; set; }
}

public class DeactivateMilestoneStepCommand : IRequest<MilestoneStepDto>
{
    public Guid Id { get; set; }
}

public class CreateMilestonePayoutRuleCommand : IRequest<MilestonePayoutRuleDto>
{
    public Guid MilestoneStepId { get; set; }
    public decimal PayoutPercentage { get; set; }
    public string? TriggerCondition { get; set; }
    public string? Description { get; set; }
    public Dictionary<string, object> Configuration { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class UpdateMilestonePayoutRuleCommand : IRequest<MilestonePayoutRuleDto>
{
    public Guid Id { get; set; }
    public decimal PayoutPercentage { get; set; }
    public string? TriggerCondition { get; set; }
    public string? Description { get; set; }
    public Dictionary<string, object> Configuration { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class DeleteMilestonePayoutRuleCommand : IRequest<bool>
{
    public Guid Id { get; set; }
}

public class CreateRoleTemplateMappingCommand : IRequest<RoleTemplateMappingDto>
{
    public string RoleName { get; set; } = string.Empty;
    public Guid MilestoneTemplateId { get; set; }
    public bool IsDefault { get; set; } = false;
    public int Priority { get; set; } = 100;
    public string? Conditions { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public Dictionary<string, object> Configuration { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class UpdateRoleTemplateMappingCommand : IRequest<RoleTemplateMappingDto>
{
    public Guid Id { get; set; }
    public bool IsDefault { get; set; }
    public int Priority { get; set; }
    public string? Conditions { get; set; }
    public string UpdatedBy { get; set; } = string.Empty;
    public Dictionary<string, object> Configuration { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class DeleteRoleTemplateMappingCommand : IRequest<bool>
{
    public Guid Id { get; set; }
}

public class SetRoleTemplateMappingAsDefaultCommand : IRequest<RoleTemplateMappingDto>
{
    public Guid Id { get; set; }
    public string UpdatedBy { get; set; } = string.Empty;
}

public class BulkMilestoneTemplateOperationCommand : IRequest<BulkOperationResult>
{
    public List<Guid> TemplateIds { get; set; } = new();
    public string Operation { get; set; } = string.Empty; // "activate", "deactivate", "delete"
    public string PerformedBy { get; set; } = string.Empty;
    public Dictionary<string, object> Parameters { get; set; } = new();
}
