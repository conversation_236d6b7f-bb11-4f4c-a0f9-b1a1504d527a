# Fix compilation issues - Remove duplicate shared library references
Write-Host "Fixing compilation issues..." -ForegroundColor Green

# Get all .csproj files
$projectFiles = Get-ChildItem -Path "Services" -Recurse -Filter "*.csproj"

Write-Host "Found $($projectFiles.Count) project files to process" -ForegroundColor Cyan

foreach ($projectFile in $projectFiles) {
    Write-Host "Processing $($projectFile.Name)..." -ForegroundColor Yellow

    # Read the project file content
    $content = Get-Content $projectFile.FullName -Raw
    $modified = $false

    # Remove TLI.Shared.Domain reference if Shared.Domain is present
    if ($content -match 'ProjectReference.*Shared\.Domain' -and $content -match 'ProjectReference.*TLI\.Shared\.Domain') {
        Write-Host "  Removing TLI.Shared.Domain reference (keeping Shared.Domain)" -ForegroundColor Cyan
        $content = $content -replace '<ProjectReference Include="[^"]*TLI\.Shared\.Domain[^"]*" />', ''
        $content = $content -replace '<ProjectReference Include="[^"]*TLI\.Shared\.Domain[^"]*">[^<]*</ProjectReference>', ''
        $modified = $true
    }

    # Write back the modified content if changes were made
    if ($modified) {
        Set-Content -Path $projectFile.FullName -Value $content -Encoding UTF8
        Write-Host "  Updated $($projectFile.Name)" -ForegroundColor Green
    }
}

Write-Host "Compilation fixes completed!" -ForegroundColor Green
