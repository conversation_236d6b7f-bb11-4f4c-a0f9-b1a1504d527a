using MediatR;
using FinancialPayment.Domain.ValueObjects;
using FinancialPayment.Application.DTOs;

namespace FinancialPayment.Application.Commands.CreatePaymentDispute;

public class CreatePaymentDisputeCommand : IRequest<Guid>
{
    public Guid OrderId { get; set; }
    public Guid? EscrowAccountId { get; set; }
    public Guid? SettlementId { get; set; }
    public Guid InitiatedBy { get; set; }
    public string InitiatorRole { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public CreateMoneyDto DisputedAmount { get; set; } = new();
    public string Category { get; set; } = string.Empty;
    public string Priority { get; set; } = string.Empty;
}


