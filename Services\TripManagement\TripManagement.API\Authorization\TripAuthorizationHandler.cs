using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;
using TripManagement.Domain.Entities;

namespace TripManagement.API.Authorization;

public class TripAuthorizationHandler : AuthorizationHandler<TripOperationRequirement, Trip>
{
    protected override Task HandleRequirementAsync(
        AuthorizationHandlerContext context,
        TripOperationRequirement requirement,
        Trip trip)
    {
        var user = context.User;
        var userId = GetUserId(user);
        var userRole = GetUserRole(user);
        var carrierId = GetCarrierId(user);

        switch (requirement.Operation)
        {
            case TripOperation.Read:
                if (CanReadTrip(trip, userId, userRole, carrierId))
                    context.Succeed(requirement);
                break;

            case TripOperation.Write:
                if (CanWriteTrip(trip, userId, userRole, carrierId))
                    context.Succeed(requirement);
                break;

            case TripOperation.Delete:
                if (CanDeleteTrip(trip, userId, userRole, carrierId))
                    context.Succeed(requirement);
                break;

            case TripOperation.Assign:
                if (CanAssignTrip(trip, userId, userRole, carrierId))
                    context.Succeed(requirement);
                break;

            case TripOperation.Complete:
                if (CanCompleteTrip(trip, userId, userRole, carrierId))
                    context.Succeed(requirement);
                break;
        }

        return Task.CompletedTask;
    }

    private bool CanReadTrip(Trip trip, Guid? userId, string? userRole, Guid? carrierId)
    {
        // Admin can read all trips
        if (userRole == TripManagementRoles.Admin)
            return true;

        // Carrier can read their own trips
        if (userRole == TripManagementRoles.Carrier && carrierId.HasValue && trip.CarrierId == carrierId.Value)
            return true;

        // Driver can read trips assigned to them
        if (userRole == TripManagementRoles.Driver && userId.HasValue && trip.DriverId == userId.Value)
            return true;

        // Broker can read trips they have access to (this would need more complex logic)
        if (userRole == TripManagementRoles.Broker)
            return true; // Simplified - in reality, check broker permissions

        return false;
    }

    private bool CanWriteTrip(Trip trip, Guid? userId, string? userRole, Guid? carrierId)
    {
        // Admin can write all trips
        if (userRole == TripManagementRoles.Admin)
            return true;

        // Carrier can write their own trips
        if (userRole == TripManagementRoles.Carrier && carrierId.HasValue && trip.CarrierId == carrierId.Value)
            return true;

        // Driver can update certain fields of trips assigned to them
        if (userRole == TripManagementRoles.Driver && userId.HasValue && trip.DriverId == userId.Value)
            return true;

        return false;
    }

    private bool CanDeleteTrip(Trip trip, Guid? userId, string? userRole, Guid? carrierId)
    {
        // Only Admin and Carrier can delete trips
        if (userRole == TripManagementRoles.Admin)
            return true;

        if (userRole == TripManagementRoles.Carrier && carrierId.HasValue && trip.CarrierId == carrierId.Value)
        {
            // Can only delete trips that haven't started
            return trip.Status == Domain.Enums.TripStatus.Pending || trip.Status == Domain.Enums.TripStatus.Assigned;
        }

        return false;
    }

    private bool CanAssignTrip(Trip trip, Guid? userId, string? userRole, Guid? carrierId)
    {
        // Only Admin and Carrier can assign trips
        if (userRole == TripManagementRoles.Admin)
            return true;

        if (userRole == TripManagementRoles.Carrier && carrierId.HasValue && trip.CarrierId == carrierId.Value)
        {
            // Can only assign pending trips
            return trip.Status == Domain.Enums.TripStatus.Pending;
        }

        return false;
    }

    private bool CanCompleteTrip(Trip trip, Guid? userId, string? userRole, Guid? carrierId)
    {
        // Admin can complete any trip
        if (userRole == TripManagementRoles.Admin)
            return true;

        // Carrier can complete their own trips
        if (userRole == TripManagementRoles.Carrier && carrierId.HasValue && trip.CarrierId == carrierId.Value)
            return true;

        // Driver can complete trips assigned to them
        if (userRole == TripManagementRoles.Driver && userId.HasValue && trip.DriverId == userId.Value)
        {
            // Can only complete in-progress trips
            return trip.Status == Domain.Enums.TripStatus.InProgress;
        }

        return false;
    }

    private Guid? GetUserId(ClaimsPrincipal user)
    {
        var userIdClaim = user.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return Guid.TryParse(userIdClaim, out var userId) ? userId : null;
    }

    private string? GetUserRole(ClaimsPrincipal user)
    {
        return user.FindFirst(ClaimTypes.Role)?.Value;
    }

    private Guid? GetCarrierId(ClaimsPrincipal user)
    {
        var carrierIdClaim = user.FindFirst("carrier_id")?.Value;
        return Guid.TryParse(carrierIdClaim, out var carrierId) ? carrierId : null;
    }
}

public class TripOperationRequirement : IAuthorizationRequirement
{
    public TripOperation Operation { get; }

    public TripOperationRequirement(TripOperation operation)
    {
        Operation = operation;
    }
}

public enum TripOperation
{
    Read,
    Write,
    Delete,
    Assign,
    Complete
}

public static class TripOperations
{
    public static TripOperationRequirement Read = new(TripOperation.Read);
    public static TripOperationRequirement Write = new(TripOperation.Write);
    public static TripOperationRequirement Delete = new(TripOperation.Delete);
    public static TripOperationRequirement Assign = new(TripOperation.Assign);
    public static TripOperationRequirement Complete = new(TripOperation.Complete);
}
