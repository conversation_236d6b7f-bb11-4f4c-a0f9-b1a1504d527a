using MediatR;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Application.Commands.CreateFeatureFlag
{
    public class CreateFeatureFlagCommand : IRequest<Guid>
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Key { get; set; } = string.Empty;
        public FeatureFlagType Type { get; set; } = FeatureFlagType.Boolean;
        public string? DefaultValue { get; set; }
        public bool IsEnabled { get; set; } = false;
        public int RolloutPercentage { get; set; } = 0;
        public string? TargetUserTypes { get; set; } // JSON array of user types
        public string? ABTestConfiguration { get; set; } // JSON configuration for A/B tests
        public string? Variants { get; set; } // JSON array of variants
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
    }
}
