using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Enums;
using Shared.Domain.Common;
using Shared.Infrastructure.Repositories;

namespace AnalyticsBIService.Domain.Repositories;

/// <summary>
/// Repository interface for Report entity
/// </summary>
public interface IReportRepository : IRepositoryBase<Report, Guid>
{
    /// <summary>
    /// Get reports by user ID
    /// </summary>
    Task<PagedResult<Report>> GetByUserIdAsync(
        Guid userId,
        int page,
        int pageSize,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get reports by type
    /// </summary>
    Task<PagedResult<Report>> GetByTypeAsync(
        ReportType type,
        int page,
        int pageSize,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get reports by generated by user
    /// </summary>
    Task<PagedResult<Report>> GetByGeneratedByAsync(
        Guid generatedBy,
        int page,
        int pageSize,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get reports by date range
    /// </summary>
    Task<PagedResult<Report>> GetByDateRangeAsync(
        DateTime startDate,
        DateTime endDate,
        int page,
        int pageSize,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get report with sections
    /// </summary>
    Task<Report?> GetWithSectionsAsync(
        Guid reportId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get scheduled reports
    /// </summary>
    Task<List<Report>> GetScheduledReportsAsync(
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get reports by status
    /// </summary>
    Task<PagedResult<Report>> GetByStatusAsync(
        ReportStatus status,
        int page,
        int pageSize,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Update report status
    /// </summary>
    Task UpdateStatusAsync(
        Guid reportId,
        ReportStatus status,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get recent reports
    /// </summary>
    Task<List<Report>> GetRecentReportsAsync(
        Guid userId,
        int count = 10,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Search reports
    /// </summary>
    Task<PagedResult<Report>> SearchReportsAsync(
        string searchTerm,
        Guid? userId = null,
        int page = 1,
        int pageSize = 10,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Save report
    /// </summary>
    Task<Guid> SaveReportAsync(Report report, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get report by ID
    /// </summary>
    Task<Report?> GetReportAsync(Guid reportId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Save template
    /// </summary>
    Task<Guid> SaveTemplateAsync(
        ReportTemplate template,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete old exports
    /// </summary>
    Task DeleteOldExportsAsync(
        DateTime cutoffDate,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for ReportSection entity
/// </summary>
public interface IReportSectionRepository : IRepositoryBase<ReportSection, Guid>
{
    /// <summary>
    /// Get sections by report ID
    /// </summary>
    Task<IEnumerable<ReportSection>> GetByReportIdAsync(
        Guid reportId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get section by order
    /// </summary>
    Task<ReportSection?> GetByOrderAsync(
        Guid reportId,
        int order,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Update section order
    /// </summary>
    Task UpdateOrderAsync(
        Guid sectionId,
        int newOrder,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Bulk update section orders
    /// </summary>
    Task BulkUpdateOrdersAsync(
        List<(Guid SectionId, int Order)> orders,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete sections by report ID
    /// </summary>
    Task DeleteByReportIdAsync(
        Guid reportId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get sections by user ID
    /// </summary>
    Task<IEnumerable<ReportSection>> GetByUserIdAsync(
        Guid userId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get active sections
    /// </summary>
    Task<IEnumerable<ReportSection>> GetActiveSectionsAsync(
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for ReportExport entity
/// </summary>
public interface IReportExportRepository : IRepositoryBase<ReportExport, Guid>
{
    /// <summary>
    /// Get exports by report ID
    /// </summary>
    Task<IEnumerable<ReportExport>> GetByReportIdAsync(
        Guid reportId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get exports by user ID
    /// </summary>
    Task<IEnumerable<ReportExport>> GetByUserIdAsync(
        Guid userId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get exports by format
    /// </summary>
    Task<IEnumerable<ReportExport>> GetByFormatAsync(
        ExportFormat format,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get exports by status
    /// </summary>
    Task<PagedResult<ReportExport>> GetByStatusAsync(
        ReportStatus status,
        int page,
        int pageSize,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Update export status
    /// </summary>
    Task UpdateStatusAsync(
        Guid exportId,
        ReportStatus status,
        string? filePath = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete old exports
    /// </summary>
    Task DeleteOldExportsAsync(
        DateTime cutoffDate,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Save template
    /// </summary>
    Task<Guid> SaveTemplateAsync(
        ReportTemplate template,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get active exports
    /// </summary>
    Task<IEnumerable<ReportExport>> GetActiveExportsAsync(
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get expired exports
    /// </summary>
    Task<IEnumerable<ReportExport>> GetExpiredExportsAsync(
        CancellationToken cancellationToken = default);
}
