using Microsoft.Extensions.Logging;
using SubscriptionManagement.Domain.Services;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Interfaces;
using Shared.Messaging;

namespace SubscriptionManagement.Infrastructure.Services
{
    public class UsageTrackingService : IUsageTrackingService
    {
        private readonly IUsageRecordRepository _usageRecordRepository;
        private readonly ISubscriptionRepository _subscriptionRepository;
        private readonly IPlanRepository _planRepository;
        private readonly ISubscriptionCacheService _cacheService;
        private readonly IMessageBroker _messageBroker;
        private readonly ILogger<UsageTrackingService> _logger;

        public UsageTrackingService(
            IUsageRecordRepository usageRecordRepository,
            ISubscriptionRepository subscriptionRepository,
            IPlanRepository planRepository,
            ISubscriptionCacheService cacheService,
            IMessageBroker messageBroker,
            ILogger<UsageTrackingService> logger)
        {
            _usageRecordRepository = usageRecordRepository;
            _subscriptionRepository = subscriptionRepository;
            _planRepository = planRepository;
            _cacheService = cacheService;
            _messageBroker = messageBroker;
            _logger = logger;
        }

        public async Task RecordUsageAsync(Guid userId, FeatureType featureType, int count = 1, string? metadata = null)
        {
            try
            {
                _logger.LogInformation("Recording usage for user {UserId}, feature {FeatureType}, count {Count}",
                    userId, featureType, count);

                // Get user's active subscription
                var subscription = await _subscriptionRepository.GetByUserIdAsync(userId);
                if (subscription == null)
                {
                    _logger.LogWarning("No active subscription found for user {UserId}", userId);
                    return;
                }

                // Create usage record
                var usageRecord = new UsageRecord(subscription.Id, userId, featureType, count, metadata);
                await _usageRecordRepository.AddAsync(usageRecord);

                // Invalidate usage cache for this user
                await _cacheService.InvalidateUserUsageCacheAsync(userId);

                // Check if user is approaching limits
                await CheckUsageLimitsAsync(userId, featureType);

                _logger.LogInformation("Usage recorded successfully for user {UserId}", userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error recording usage for user {UserId}", userId);
                throw;
            }
        }

        public async Task<bool> CanUseFeatureAsync(Guid userId, FeatureType featureType, int requestedCount = 1)
        {
            try
            {
                var subscription = await _subscriptionRepository.GetByUserIdAsync(userId);
                if (subscription == null)
                {
                    return false;
                }

                // Check if feature is unlimited for this plan
                if (await IsFeatureUnlimitedAsync(userId, featureType))
                {
                    return true;
                }

                var currentUsage = await GetCurrentPeriodUsageAsync(userId);
                var limits = await GetFeatureLimitsAsync(userId);

                if (!currentUsage.ContainsKey(featureType) || !limits.ContainsKey(featureType))
                {
                    return true; // No limit defined
                }

                return currentUsage[featureType] + requestedCount <= limits[featureType];
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking feature usage for user {UserId}", userId);
                return false;
            }
        }

        public async Task<int> GetRemainingUsageAsync(Guid userId, FeatureType featureType)
        {
            try
            {
                if (await IsFeatureUnlimitedAsync(userId, featureType))
                {
                    return int.MaxValue;
                }

                var currentUsage = await GetCurrentPeriodUsageAsync(userId);
                var limits = await GetFeatureLimitsAsync(userId);

                if (!currentUsage.ContainsKey(featureType) || !limits.ContainsKey(featureType))
                {
                    return int.MaxValue; // No limit defined
                }

                return Math.Max(0, limits[featureType] - currentUsage[featureType]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting remaining usage for user {UserId}", userId);
                return 0;
            }
        }

        public async Task<List<UsageRecord>> GetUsageHistoryAsync(Guid userId, FeatureType? featureType = null, DateTime? from = null, DateTime? to = null)
        {
            try
            {
                return await _usageRecordRepository.GetUsageHistoryAsync(userId, featureType, from, to);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting usage history for user {UserId}", userId);
                return new List<UsageRecord>();
            }
        }

        public async Task<Dictionary<FeatureType, int>> GetCurrentPeriodUsageAsync(Guid userId)
        {
            try
            {
                var now = DateTime.UtcNow;
                var periodKey = $"{now.Year}-{now.Month:D2}";

                // Try cache first
                var cachedUsage = await _cacheService.GetUserUsageAsync(userId, periodKey);
                if (cachedUsage != null)
                {
                    _logger.LogDebug("Current period usage for user {UserId} retrieved from cache", userId);
                    return cachedUsage;
                }

                // Load from database
                var periodStart = new DateTime(now.Year, now.Month, 1);
                var periodEnd = periodStart.AddMonths(1).AddDays(-1);
                var usage = await _usageRecordRepository.GetUsageSummaryAsync(userId, periodStart, periodEnd);

                // Cache the result for 10 minutes
                await _cacheService.SetUserUsageAsync(userId, periodKey, usage, TimeSpan.FromMinutes(10));
                _logger.LogDebug("Current period usage for user {UserId} loaded from database and cached", userId);

                return usage;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting current period usage for user {UserId}", userId);
                return new Dictionary<FeatureType, int>();
            }
        }

        public async Task<Dictionary<FeatureType, int>> GetUsageSummaryAsync(Guid userId, DateTime from, DateTime to)
        {
            try
            {
                return await _usageRecordRepository.GetUsageSummaryAsync(userId, from, to);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting usage summary for user {UserId}", userId);
                return new Dictionary<FeatureType, int>();
            }
        }

        public async Task<Dictionary<FeatureType, int>> GetFeatureLimitsAsync(Guid userId)
        {
            try
            {
                var subscription = await _subscriptionRepository.GetByUserIdAsync(userId);
                if (subscription == null)
                {
                    return new Dictionary<FeatureType, int>();
                }

                var plan = await _planRepository.GetByIdAsync(subscription.PlanId);
                if (plan == null)
                {
                    return new Dictionary<FeatureType, int>();
                }

                var limits = new Dictionary<FeatureType, int>();
                foreach (var feature in plan.Features)
                {
                    if (feature.AccessType == FeatureAccessType.Limit)
                    {
                        limits[feature.FeatureType] = feature.Limit ?? 0;
                    }
                }

                return limits;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting feature limits for user {UserId}", userId);
                return new Dictionary<FeatureType, int>();
            }
        }

        public async Task<bool> IsFeatureUnlimitedAsync(Guid userId, FeatureType featureType)
        {
            try
            {
                var subscription = await _subscriptionRepository.GetByUserIdAsync(userId);
                if (subscription == null)
                {
                    return false;
                }

                var plan = await _planRepository.GetByIdAsync(subscription.PlanId);
                if (plan == null)
                {
                    return false;
                }

                var feature = plan.Features.FirstOrDefault(f => f.FeatureType == featureType);
                return feature?.AccessType == FeatureAccessType.Unlimited;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if feature is unlimited for user {UserId}", userId);
                return false;
            }
        }

        public async Task<DateTime> GetUsagePeriodResetDateAsync(Guid userId)
        {
            try
            {
                var subscription = await _subscriptionRepository.GetByUserIdAsync(userId);
                if (subscription == null)
                {
                    return DateTime.UtcNow.AddMonths(1);
                }

                return subscription.NextBillingDate;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting usage period reset date for user {UserId}", userId);
                return DateTime.UtcNow.AddMonths(1);
            }
        }

        // Analytics methods
        public async Task<Dictionary<string, object>> GetUsageAnalyticsAsync(Guid userId, DateTime? from = null, DateTime? to = null)
        {
            try
            {
                var fromDate = from ?? DateTime.UtcNow.AddMonths(-1);
                var toDate = to ?? DateTime.UtcNow;

                var usageHistory = await GetUsageHistoryAsync(userId, null, fromDate, toDate);
                var currentPeriodUsage = await GetCurrentPeriodUsageAsync(userId);
                var limits = await GetFeatureLimitsAsync(userId);

                var analytics = new Dictionary<string, object>
                {
                    ["period"] = new { from = fromDate, to = toDate },
                    ["totalUsageEvents"] = usageHistory.Count,
                    ["currentPeriodUsage"] = currentPeriodUsage,
                    ["limits"] = limits,
                    ["usageByFeature"] = usageHistory
                        .GroupBy(u => u.FeatureType)
                        .ToDictionary(g => g.Key.ToString(), g => g.Sum(u => u.UsageCount)),
                    ["usageByDay"] = usageHistory
                        .GroupBy(u => u.UsageDate.Date)
                        .ToDictionary(g => g.Key.ToString("yyyy-MM-dd"), g => g.Sum(u => u.UsageCount)),
                    ["averageDailyUsage"] = usageHistory.Any()
                        ? usageHistory.Sum(u => u.UsageCount) / Math.Max(1, (toDate - fromDate).Days)
                        : 0
                };

                return analytics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting usage analytics for user {UserId}", userId);
                return new Dictionary<string, object>();
            }
        }

        public async Task<List<UsageRecord>> GetTopUsersAsync(FeatureType featureType, int count = 10, DateTime? from = null, DateTime? to = null)
        {
            try
            {
                var fromDate = from ?? DateTime.UtcNow.AddMonths(-1);
                var toDate = to ?? DateTime.UtcNow;

                return await _usageRecordRepository.GetTopUsersAsync(featureType, count, fromDate, toDate);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting top users for feature {FeatureType}", featureType);
                return new List<UsageRecord>();
            }
        }

        public async Task<Dictionary<FeatureType, double>> GetAverageUsageAsync(DateTime from, DateTime to)
        {
            try
            {
                return await _usageRecordRepository.GetAverageUsageAsync(from, to);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting average usage from {From} to {To}", from, to);
                return new Dictionary<FeatureType, double>();
            }
        }

        // Alerts and notifications methods
        public async Task<List<Guid>> GetUsersNearLimitAsync(FeatureType featureType, double threshold = 0.8)
        {
            try
            {
                var usersNearLimit = new List<Guid>();
                var activeSubscriptions = await _subscriptionRepository.GetActiveSubscriptionsAsync();

                foreach (var subscription in activeSubscriptions)
                {
                    if (await IsFeatureUnlimitedAsync(subscription.UserId, featureType))
                    {
                        continue;
                    }

                    var currentUsage = await GetCurrentPeriodUsageAsync(subscription.UserId);
                    var limits = await GetFeatureLimitsAsync(subscription.UserId);

                    if (currentUsage.ContainsKey(featureType) && limits.ContainsKey(featureType))
                    {
                        var usage = currentUsage[featureType];
                        var limit = limits[featureType];
                        var usagePercentage = (double)usage / limit;

                        if (usagePercentage >= threshold && usagePercentage < 1.0)
                        {
                            usersNearLimit.Add(subscription.UserId);
                        }
                    }
                }

                return usersNearLimit;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting users near limit for feature {FeatureType}", featureType);
                return new List<Guid>();
            }
        }

        public async Task<List<Guid>> GetUsersOverLimitAsync(FeatureType featureType)
        {
            try
            {
                var usersOverLimit = new List<Guid>();
                var activeSubscriptions = await _subscriptionRepository.GetActiveSubscriptionsAsync();

                foreach (var subscription in activeSubscriptions)
                {
                    if (await IsFeatureUnlimitedAsync(subscription.UserId, featureType))
                    {
                        continue;
                    }

                    var currentUsage = await GetCurrentPeriodUsageAsync(subscription.UserId);
                    var limits = await GetFeatureLimitsAsync(subscription.UserId);

                    if (currentUsage.ContainsKey(featureType) && limits.ContainsKey(featureType))
                    {
                        var usage = currentUsage[featureType];
                        var limit = limits[featureType];

                        if (usage > limit)
                        {
                            usersOverLimit.Add(subscription.UserId);
                        }
                    }
                }

                return usersOverLimit;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting users over limit for feature {FeatureType}", featureType);
                return new List<Guid>();
            }
        }

        public async Task SendUsageAlertAsync(Guid userId, FeatureType featureType, int currentUsage, int limit)
        {
            try
            {
                _logger.LogInformation("Sending usage alert for user {UserId}, feature {FeatureType}, usage {CurrentUsage}/{Limit}",
                    userId, featureType, currentUsage, limit);

                var usagePercentage = (double)currentUsage / limit;
                var alertType = usagePercentage >= 1.0 ? "limit_exceeded" : "approaching_limit";

                await _messageBroker.PublishAsync("usage.alert", new
                {
                    UserId = userId,
                    FeatureType = featureType.ToString(),
                    CurrentUsage = currentUsage,
                    Limit = limit,
                    UsagePercentage = usagePercentage,
                    AlertType = alertType,
                    Timestamp = DateTime.UtcNow
                });

                _logger.LogInformation("Usage alert sent successfully for user {UserId}", userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending usage alert for user {UserId}", userId);
            }
        }

        private async Task CheckUsageLimitsAsync(Guid userId, FeatureType featureType)
        {
            try
            {
                if (await IsFeatureUnlimitedAsync(userId, featureType))
                {
                    return;
                }

                var currentUsage = await GetCurrentPeriodUsageAsync(userId);
                var limits = await GetFeatureLimitsAsync(userId);

                if (!currentUsage.ContainsKey(featureType) || !limits.ContainsKey(featureType))
                {
                    return;
                }

                var usage = currentUsage[featureType];
                var limit = limits[featureType];
                var usagePercentage = (double)usage / limit;

                // Send alert if user is at 80% or 100% of limit
                if (usagePercentage >= 0.8)
                {
                    await SendUsageAlertAsync(userId, featureType, usage, limit);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking usage limits for user {UserId}", userId);
            }
        }
    }
}
