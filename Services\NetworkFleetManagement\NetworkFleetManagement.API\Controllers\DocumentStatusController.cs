using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Application.Queries.Documents;

namespace NetworkFleetManagement.API.Controllers;

[ApiController]
[Route("api/v1/[controller]")]
[Authorize]
public class DocumentStatusController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<DocumentStatusController> _logger;

    public DocumentStatusController(IMediator mediator, ILogger<DocumentStatusController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get document status summary for a carrier
    /// </summary>
    [HttpGet("summary/carrier/{carrierId:guid}")]
    [Authorize(Roles = "Admin,Carrier")]
    public async Task<ActionResult<DocumentStatusSummaryDto>> GetDocumentStatusSummary(
        Guid carrierId,
        [FromQuery] int expiryThresholdDays = 30,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetDocumentStatusSummaryQuery(carrierId, expiryThresholdDays);
            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while getting document status summary for carrier {CarrierId}", carrierId);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting document status summary for carrier {CarrierId}", carrierId);
            return StatusCode(500, "An error occurred while retrieving document status summary");
        }
    }

    /// <summary>
    /// Get expiring documents for a carrier
    /// </summary>
    [HttpGet("expiring/carrier/{carrierId:guid}")]
    [Authorize(Roles = "Admin,Carrier")]
    public async Task<ActionResult<PagedResult<ExpiringDocumentDto>>> GetExpiringDocuments(
        Guid carrierId,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] int expiryThresholdDays = 30,
        [FromQuery] string? entityType = null,
        [FromQuery] string? documentType = null,
        [FromQuery] bool? isExpired = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetExpiringDocumentsQuery(
                carrierId, pageNumber, pageSize, expiryThresholdDays, 
                entityType, documentType, isExpired);
            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while getting expiring documents for carrier {CarrierId}", carrierId);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting expiring documents for carrier {CarrierId}", carrierId);
            return StatusCode(500, "An error occurred while retrieving expiring documents");
        }
    }

    /// <summary>
    /// Get vehicle document status for a carrier
    /// </summary>
    [HttpGet("vehicles/carrier/{carrierId:guid}")]
    [Authorize(Roles = "Admin,Carrier")]
    public async Task<ActionResult<IEnumerable<VehicleDocumentStatusDto>>> GetVehicleDocumentStatus(
        Guid carrierId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // This would be implemented with a separate query handler
            // For now, return a placeholder response
            return Ok(new List<VehicleDocumentStatusDto>());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicle document status for carrier {CarrierId}", carrierId);
            return StatusCode(500, "An error occurred while retrieving vehicle document status");
        }
    }

    /// <summary>
    /// Get driver document status for a carrier
    /// </summary>
    [HttpGet("drivers/carrier/{carrierId:guid}")]
    [Authorize(Roles = "Admin,Carrier")]
    public async Task<ActionResult<IEnumerable<DriverDocumentStatusDto>>> GetDriverDocumentStatus(
        Guid carrierId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // This would be implemented with a separate query handler
            // For now, return a placeholder response
            return Ok(new List<DriverDocumentStatusDto>());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting driver document status for carrier {CarrierId}", carrierId);
            return StatusCode(500, "An error occurred while retrieving driver document status");
        }
    }

    /// <summary>
    /// Get document compliance dashboard for admin
    /// </summary>
    [HttpGet("compliance/dashboard")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<object>> GetComplianceDashboard(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // This would be implemented with a separate query handler for admin dashboard
            // For now, return a placeholder response
            return Ok(new
            {
                TotalCarriers = 0,
                CompliantCarriers = 0,
                NonCompliantCarriers = 0,
                OverallComplianceRate = 0.0m,
                GeneratedAt = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting compliance dashboard");
            return StatusCode(500, "An error occurred while retrieving compliance dashboard");
        }
    }
}
