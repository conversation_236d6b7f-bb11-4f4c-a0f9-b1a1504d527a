using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.DTOs;
using OrderManagement.Application.Interfaces;

namespace OrderManagement.Application.Queries.GetRfqMilestoneDetails;

public class GetRfqMilestoneDetailsQueryHandler : IRequestHandler<GetRfqMilestoneDetailsQuery, RfqMilestoneDetailsDto?>
{
    private readonly IRfqRepository _rfqRepository;
    private readonly IRfqMilestoneAssignmentRepository _assignmentRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetRfqMilestoneDetailsQueryHandler> _logger;

    public GetRfqMilestoneDetailsQueryHandler(
        IRfqRepository rfqRepository,
        IRfqMilestoneAssignmentRepository assignmentRepository,
        IMapper mapper,
        ILogger<GetRfqMilestoneDetailsQueryHandler> logger)
    {
        _rfqRepository = rfqRepository;
        _assignmentRepository = assignmentRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<RfqMilestoneDetailsDto?> Handle(GetRfqMilestoneDetailsQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting milestone details for RFQ {RfqId} by user {UserId}",
            request.RfqId, request.RequestingUserId);

        try
        {
            // Verify RFQ exists and user has access
            var rfq = await _rfqRepository.GetByIdAsync(request.RfqId, cancellationToken);
            if (rfq == null)
            {
                _logger.LogWarning("RFQ {RfqId} not found", request.RfqId);
                return null;
            }

            // TODO: Add authorization logic here
            // Check if the requesting user has permission to view this RFQ milestone details

            // Get active milestone assignment
            var assignment = await _assignmentRepository.GetActiveByRfqIdAsync(request.RfqId, cancellationToken);
            if (assignment == null)
            {
                _logger.LogInformation("No active milestone assignment found for RFQ {RfqId}", request.RfqId);
                return null;
            }

            // Map to DTO
            var milestoneDetailsDto = _mapper.Map<RfqMilestoneDetailsDto>(assignment);

            // Calculate payout information if requested
            if (request.IncludePayoutCalculations && assignment.TotalContractValue != null)
            {
                var completedAmount = assignment.GetCompletedPayoutAmount();
                var remainingAmount = assignment.GetRemainingPayoutAmount();

                milestoneDetailsDto.CompletedPayoutAmount = new MoneyDto
                {
                    Amount = completedAmount.Amount,
                    Currency = completedAmount.Currency
                };
                milestoneDetailsDto.RemainingPayoutAmount = new MoneyDto
                {
                    Amount = remainingAmount.Amount,
                    Currency = remainingAmount.Currency
                };
                milestoneDetailsDto.CompletionPercentage = assignment.GetCompletionPercentage();
            }

            _logger.LogInformation("Retrieved milestone details for RFQ {RfqId} with {StepCount} steps",
                request.RfqId, assignment.MilestoneTemplate.Steps.Count);

            return milestoneDetailsDto;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving milestone details for RFQ {RfqId}", request.RfqId);
            throw;
        }
    }
}
