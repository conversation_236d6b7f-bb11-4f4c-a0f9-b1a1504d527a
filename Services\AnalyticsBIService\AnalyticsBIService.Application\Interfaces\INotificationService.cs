using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Enums;

namespace AnalyticsBIService.Application.Interfaces;

/// <summary>
/// Notification service interface
/// </summary>
public interface INotificationService
{
    /// <summary>
    /// Send alert notification
    /// </summary>
    Task SendAlertNotificationAsync(
        Alert alert,
        List<string> recipients,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Send report completion notification
    /// </summary>
    Task SendReportNotificationAsync(
        Guid reportId,
        string reportName,
        string filePath,
        List<string> recipients,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Send threshold breach notification
    /// </summary>
    Task SendThresholdNotificationAsync(
        string metricName,
        decimal currentValue,
        decimal thresholdValue,
        string entityId,
        List<string> recipients,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Send system notification
    /// </summary>
    Task SendSystemNotificationAsync(
        string title,
        string message,
        List<string> recipients,
        NotificationType type = NotificationType.System,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Send bulk notifications
    /// </summary>
    Task SendBulkNotificationsAsync(
        List<NotificationRequest> notifications,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Notification request model
/// </summary>
public class NotificationRequest
{
    public string Recipient { get; set; } = string.Empty;
    public string Subject { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public NotificationType Type { get; set; } = NotificationType.System;
    public AlertSeverity Severity { get; set; } = AlertSeverity.Info;
}

/// <summary>
/// Notification types
/// </summary>
public enum NotificationType
{
    System,
    Alert,
    ReportReady,
    ThresholdBreach,
    Reminder,
    Warning,
    Error
}
