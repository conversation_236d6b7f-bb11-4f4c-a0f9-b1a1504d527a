using AutoMapper;
using DataStorage.Application.DTOs;
using DataStorage.Domain.Entities;
using DataStorage.Domain.Enums;
using DataStorage.Domain.Exceptions;
using DataStorage.Domain.Interfaces;
using DataStorage.Domain.ValueObjects;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Domain.Common;

namespace DataStorage.Application.Commands.Handlers;

public class UploadDocumentCommandHandler : IRequestHandler<UploadDocumentCommand, DocumentDto>
{
    private readonly IDocumentRepository _documentRepository;
    private readonly IFileStorageService _fileStorageService;
    private readonly IDocumentProcessingService _documentProcessingService;
    private readonly IMapper _mapper;
    private readonly ILogger<UploadDocumentCommandHandler> _logger;

    public UploadDocumentCommandHandler(
        IDocumentRepository documentRepository,
        IFileStorageService fileStorageService,
        IDocumentProcessingService documentProcessingService,
        IMapper mapper,
        ILogger<UploadDocumentCommandHandler> logger)
    {
        _documentRepository = documentRepository;
        _fileStorageService = fileStorageService;
        _documentProcessingService = documentProcessingService;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<DocumentDto> Handle(UploadDocumentCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Starting document upload for user {UserId}, file: {FileName}",
                request.OwnerId, request.FileName);

            // Validate file stream
            if (request.FileStream == null || !request.FileStream.CanRead)
                throw new FileUploadException(request.FileName, "Invalid file stream");

            // Upload file to storage
            var storageLocation = await _fileStorageService.UploadFileAsync(
                request.FileStream,
                request.FileName,
                request.ContentType,
                request.PreferredStorageProvider,
                cancellationToken: cancellationToken);

            // Get file metadata
            var fileMetadata = await _fileStorageService.GetFileMetadataAsync(storageLocation, cancellationToken);

            // Calculate checksum for integrity
            var checksum = await _documentProcessingService.CalculateChecksumAsync(storageLocation, cancellationToken);
            fileMetadata = fileMetadata.WithChecksum(checksum);

            // Create document entity
            var document = new Document(
                request.Title,
                request.Description,
                request.DocumentType,
                request.Category,
                fileMetadata,
                storageLocation,
                request.OwnerId,
                request.OwnerType,
                request.AccessLevel,
                request.ExpiresAt,
                request.Tags,
                request.ExternalReference);

            // Save to repository
            await _documentRepository.AddAsync(document, cancellationToken);

            _logger.LogInformation("Document uploaded successfully. DocumentId: {DocumentId}, FileName: {FileName}",
                document.Id, request.FileName);

            // Map to DTO and return
            var documentDto = _mapper.Map<DocumentDto>(document);
            documentDto.AccessUrl = storageLocation.GetAccessUrl();

            // Generate thumbnail for images
            if (fileMetadata.IsImage())
            {
                try
                {
                    var thumbnailBytes = await _documentProcessingService.GenerateThumbnailAsync(
                        storageLocation, 200, 200, cancellationToken);

                    if (thumbnailBytes?.Length > 0)
                    {
                        var thumbnailLocation = await _fileStorageService.UploadFileAsync(
                            thumbnailBytes,
                            $"thumb_{request.FileName}",
                            "image/jpeg",
                            cancellationToken: cancellationToken);

                        documentDto.ThumbnailUrl = thumbnailLocation.GetAccessUrl();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to generate thumbnail for document {DocumentId}", document.Id);
                    // Continue without thumbnail - not a critical failure
                }
            }

            return documentDto;
        }
        catch (Exception ex) when (!(ex is DataStorageDomainException))
        {
            _logger.LogError(ex, "Unexpected error during document upload for user {UserId}, file: {FileName}",
                request.OwnerId, request.FileName);
            throw new FileUploadException(request.FileName, ex);
        }
    }
}

public class UpdateDocumentCommandHandler : IRequestHandler<UpdateDocumentCommand, DocumentDto>
{
    private readonly IDocumentRepository _documentRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<UpdateDocumentCommandHandler> _logger;

    public UpdateDocumentCommandHandler(
        IDocumentRepository documentRepository,
        IMapper mapper,
        ILogger<UpdateDocumentCommandHandler> logger)
    {
        _documentRepository = documentRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<DocumentDto> Handle(UpdateDocumentCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Updating document {DocumentId} by user {UserId}",
            request.DocumentId, request.UserId);

        var document = await _documentRepository.GetByIdAsync(request.DocumentId, cancellationToken);
        if (document == null)
            throw new DocumentNotFoundException(request.DocumentId);

        // Check access rights
        if (!document.HasAccess(request.UserId, AccessLevel.Internal) && document.OwnerId != request.UserId)
            throw new DocumentAccessDeniedException(request.DocumentId, request.UserId);

        // Update document
        document.UpdateMetadata(request.Title, request.Description, request.Tags);

        await _documentRepository.UpdateAsync(document, cancellationToken);

        _logger.LogInformation("Document {DocumentId} updated successfully", request.DocumentId);

        return _mapper.Map<DocumentDto>(document);
    }
}

public class ChangeDocumentStatusCommandHandler : IRequestHandler<ChangeDocumentStatusCommand, DocumentDto>
{
    private readonly IDocumentRepository _documentRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<ChangeDocumentStatusCommandHandler> _logger;

    public ChangeDocumentStatusCommandHandler(
        IDocumentRepository documentRepository,
        IMapper mapper,
        ILogger<ChangeDocumentStatusCommandHandler> logger)
    {
        _documentRepository = documentRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<DocumentDto> Handle(ChangeDocumentStatusCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Changing status of document {DocumentId} to {NewStatus} by user {UserId}",
            request.DocumentId, request.NewStatus, request.UserId);

        var document = await _documentRepository.GetByIdAsync(request.DocumentId, cancellationToken);
        if (document == null)
            throw new DocumentNotFoundException(request.DocumentId);

        // Check access rights
        if (!document.HasAccess(request.UserId, AccessLevel.Internal) && document.OwnerId != request.UserId)
            throw new DocumentAccessDeniedException(request.DocumentId, request.UserId);

        // Change status
        document.ChangeStatus(request.NewStatus, request.Reason);

        await _documentRepository.UpdateAsync(document, cancellationToken);

        _logger.LogInformation("Document {DocumentId} status changed to {NewStatus}",
            request.DocumentId, request.NewStatus);

        return _mapper.Map<DocumentDto>(document);
    }
}

