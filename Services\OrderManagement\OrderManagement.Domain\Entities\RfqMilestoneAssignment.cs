using Shared.Domain.Common;
using OrderManagement.Domain.Enums;
using OrderManagement.Domain.ValueObjects;

namespace OrderManagement.Domain.Entities;

/// <summary>
/// Entity representing the assignment of a milestone template to an RFQ
/// </summary>
public class RfqMilestoneAssignment : BaseEntity
{
    public Guid RfqId { get; private set; }
    public Guid MilestoneTemplateId { get; private set; }
    public DateTime AssignedAt { get; private set; }
    public Guid AssignedBy { get; private set; }
    public string? AssignedByName { get; private set; }
    public bool IsActive { get; private set; }
    public DateTime? DeactivatedAt { get; private set; }
    public Guid? DeactivatedBy { get; private set; }
    public string? DeactivationReason { get; private set; }
    public Money? TotalContractValue { get; private set; }
    public string? CustomPayoutStructure { get; private set; }
    public Dictionary<string, object>? AssignmentMetadata { get; private set; }

    // Navigation properties
    public RFQ RFQ { get; private set; } = null!;
    public MilestoneTemplate MilestoneTemplate { get; private set; } = null!;

    private readonly List<RfqMilestoneProgress> _milestoneProgress = new();
    public IReadOnlyCollection<RfqMilestoneProgress> MilestoneProgress => _milestoneProgress.AsReadOnly();

    private RfqMilestoneAssignment() { } // EF Constructor

    public RfqMilestoneAssignment(
        Guid rfqId,
        Guid milestoneTemplateId,
        Guid assignedBy,
        string? assignedByName = null,
        Money? totalContractValue = null,
        string? customPayoutStructure = null,
        Dictionary<string, object>? assignmentMetadata = null)
    {
        RfqId = rfqId;
        MilestoneTemplateId = milestoneTemplateId;
        AssignedAt = DateTime.UtcNow;
        AssignedBy = assignedBy;
        AssignedByName = assignedByName;
        IsActive = true;
        TotalContractValue = totalContractValue;
        CustomPayoutStructure = customPayoutStructure;
        AssignmentMetadata = assignmentMetadata;
    }

    public void Deactivate(Guid deactivatedBy, string reason)
    {
        IsActive = false;
        DeactivatedAt = DateTime.UtcNow;
        DeactivatedBy = deactivatedBy;
        DeactivationReason = reason;
    }

    public void UpdateContractValue(Money totalContractValue)
    {
        TotalContractValue = totalContractValue;
    }

    public void UpdatePayoutStructure(string customPayoutStructure)
    {
        CustomPayoutStructure = customPayoutStructure;
    }

    public void AddMilestoneProgress(RfqMilestoneProgress progress)
    {
        _milestoneProgress.Add(progress);
    }

    public RfqMilestoneProgress? GetProgressForStep(Guid stepId)
    {
        return _milestoneProgress.FirstOrDefault(p => p.MilestoneStepId == stepId);
    }

    public decimal GetCompletedPayoutPercentage()
    {
        return _milestoneProgress
            .Where(p => p.Status == MilestoneProgressStatus.Completed)
            .Sum(p => p.PayoutPercentage);
    }

    public Money? GetCompletedPayoutAmount()
    {
        if (TotalContractValue == null) return null;

        var completedPercentage = GetCompletedPayoutPercentage();
        var amount = TotalContractValue.Amount * (completedPercentage / 100m);
        return new Money(amount, TotalContractValue.Currency);
    }

    public Money? GetRemainingPayoutAmount()
    {
        if (TotalContractValue == null) return null;

        var completedAmount = GetCompletedPayoutAmount();
        if (completedAmount == null) return TotalContractValue;

        var remainingAmount = TotalContractValue.Amount - completedAmount.Amount;
        return new Money(remainingAmount, TotalContractValue.Currency);
    }

    public bool IsFullyCompleted()
    {
        if (!MilestoneTemplate.Steps.Any()) return false;

        return MilestoneTemplate.Steps.All(step =>
            _milestoneProgress.Any(p => p.MilestoneStepId == step.Id &&
                                      p.Status == MilestoneProgressStatus.Completed));
    }

    public MilestoneStep? GetNextPendingStep()
    {
        return MilestoneTemplate.Steps
            .Where(step => !_milestoneProgress.Any(p => p.MilestoneStepId == step.Id &&
                                                       p.Status == MilestoneProgressStatus.Completed))
            .OrderBy(step => step.StepOrder)
            .FirstOrDefault();
    }

    public int GetCompletedStepsCount()
    {
        return _milestoneProgress.Count(p => p.Status == MilestoneProgressStatus.Completed);
    }

    public int GetTotalStepsCount()
    {
        return MilestoneTemplate.Steps.Count;
    }

    public decimal GetCompletionPercentage()
    {
        var totalSteps = GetTotalStepsCount();
        if (totalSteps == 0) return 0;

        var completedSteps = GetCompletedStepsCount();
        return (decimal)completedSteps / totalSteps * 100m;
    }
}
