using System;
using System.Collections.Generic;
using MediatR;
using MobileWorkflow.Application.DTOs;

namespace MobileWorkflow.Application.Commands.FormBuilder
{
    public class CreateFormDefinitionCommand : IRequest<FormDefinitionDto>
    {
        public string Name { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string Version { get; set; } = "1.0";
        public bool IsTemplate { get; set; } = false;
        public bool IsMultiStep { get; set; } = false;
        public Dictionary<string, object>? Configuration { get; set; }
        public Dictionary<string, object>? Styling { get; set; }
        public Dictionary<string, object>? ValidationRules { get; set; }
        public List<string>? RequiredRoles { get; set; }
        public List<string>? Tags { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
    }
}
