{"Logging": {"LogLevel": {"Default": "Warning", "Microsoft": "Error", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.EntityFrameworkCore": "Error", "System.Net.Http.HttpClient": "Error"}}, "ConnectionStrings": {"DefaultConnection": "Host=${DB_HOST};Port=${DB_PORT};Database=${DB_NAME};Username=${DB_USER};Password=${DB_PASSWORD};", "Redis": "${REDIS_CONNECTION_STRING}", "RabbitMQ": "${RABBITMQ_CONNECTION_STRING}"}, "Authentication": {"Jwt": {"SecretKey": "${JWT_SECRET_KEY}", "ExpiryInMinutes": 30, "RefreshTokenExpiryInDays": 1}}, "Cors": {"AllowedOrigins": ["${FRONTEND_URL}", "${ADMIN_URL}", "${MOBILE_URL}"]}, "RateLimiting": {"EnableRateLimiting": true, "GeneralRules": {"PermitLimit": 50, "Window": "00:01:00", "ReplenishmentPeriod": "00:00:10", "TokensPerPeriod": 5, "QueueLimit": 5}, "AuthenticationRules": {"PermitLimit": 3, "Window": "00:01:00", "ReplenishmentPeriod": "00:00:20", "TokensPerPeriod": 1, "QueueLimit": 1}}, "Caching": {"Redis": {"Enabled": true, "DefaultExpirationMinutes": 60, "ConnectionString": "${REDIS_CONNECTION_STRING}", "Database": 0}, "Memory": {"Enabled": false}}, "MessageBroker": {"RabbitMQ": {"Enabled": true, "ConnectionString": "${RABBITMQ_CONNECTION_STRING}", "RetryCount": 5, "RetryDelay": "00:00:10"}}, "HealthChecks": {"DetailedErrors": false, "Timeout": "00:00:15"}, "Monitoring": {"ApplicationInsights": {"Enabled": true, "InstrumentationKey": "${APPLICATION_INSIGHTS_KEY}"}, "Prometheus": {"Enabled": true}, "Jaeger": {"Enabled": true, "AgentHost": "${JAEGER_AGENT_HOST}", "AgentPort": 6831, "ServiceName": "TLI-Production"}, "Seq": {"Enabled": true, "ServerUrl": "${SEQ_SERVER_URL}", "ApiKey": "${SEQ_API_KEY}"}}, "Security": {"EnableHttpsRedirection": true, "EnableHsts": true, "HstsMaxAge": "365.00:00:00", "ContentSecurityPolicy": "default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' https:; connect-src 'self' https:;", "ApiKey": {"Enabled": true, "HeaderName": "X-API-Key", "ValidKeys": ["${API_KEY_1}", "${API_KEY_2}"]}}, "Swagger": {"Enabled": false}, "FileStorage": {"Provider": "${FILE_STORAGE_PROVIDER}", "Local": {"BasePath": "/app/uploads", "MaxFileSize": 10485760}, "Azure": {"ConnectionString": "${AZURE_STORAGE_CONNECTION_STRING}", "ContainerName": "tli-documents"}, "AWS": {"AccessKey": "${AWS_ACCESS_KEY}", "SecretKey": "${AWS_SECRET_KEY}", "BucketName": "${AWS_S3_BUCKET}", "Region": "${AWS_REGION}"}, "MinIO": {"Endpoint": "${MINIO_ENDPOINT}", "AccessKey": "${MINIO_ACCESS_KEY}", "SecretKey": "${MINIO_SECRET_KEY}", "BucketName": "${MINIO_BUCKET}", "UseSSL": true}}, "Email": {"Provider": "${EMAIL_PROVIDER}", "SMTP": {"Host": "${SMTP_HOST}", "Port": "${SMTP_PORT}", "EnableSsl": true, "Username": "${SMTP_USERNAME}", "Password": "${SMTP_PASSWORD}", "FromEmail": "${FROM_EMAIL}", "FromName": "TLI Platform"}, "SendGrid": {"ApiKey": "${SENDGRID_API_KEY}", "FromEmail": "${FROM_EMAIL}", "FromName": "TLI Platform"}}, "SMS": {"Provider": "${SMS_PROVIDER}", "Twilio": {"AccountSid": "${TWILIO_ACCOUNT_SID}", "AuthToken": "${TWILIO_AUTH_TOKEN}", "FromNumber": "${TWILIO_FROM_NUMBER}"}}, "Payment": {"Razorpay": {"KeyId": "${RAZORPAY_KEY_ID}", "KeySecret": "${RAZORPAY_KEY_SECRET}", "WebhookSecret": "${RAZORPAY_WEBHOOK_SECRET}"}, "Stripe": {"PublishableKey": "${STRIPE_PUBLISHABLE_KEY}", "SecretKey": "${STRIPE_SECRET_KEY}", "WebhookSecret": "${STRIPE_WEBHOOK_SECRET}"}}, "Maps": {"GoogleMaps": {"ApiKey": "${GOOGLE_MAPS_API_KEY}"}, "MapBox": {"AccessToken": "${MAPBOX_ACCESS_TOKEN}"}}, "Features": {"EnableUserRegistration": true, "EnableEmailVerification": true, "EnablePhoneVerification": true, "EnableTwoFactorAuth": true, "EnableSocialLogin": true, "EnableFileUpload": true, "EnableRealTimeNotifications": true, "EnableAnalytics": true, "EnableAuditLogging": true, "MaintenanceMode": false}, "BusinessRules": {"MaxOrderValue": 1000000, "MinOrderValue": 100, "SessionTimeoutMinutes": 30, "PasswordPolicy": {"MinLength": 12, "RequireUppercase": true, "RequireLowercase": true, "RequireDigit": true, "RequireSpecialChar": true}}, "ProductionSettings": {"EnableDetailedErrors": false, "EnableSensitiveDataLogging": false, "EnableDeveloperExceptionPage": false, "SeedTestData": false, "BypassAuthentication": false, "MockExternalServices": false, "EnableSqlLogging": false, "EnablePerformanceCounters": true, "DataRetentionDays": 2555, "BackupRetentionDays": 90, "LogRetentionDays": 30}}