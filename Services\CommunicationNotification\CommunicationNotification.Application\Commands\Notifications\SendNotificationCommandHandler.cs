using CommunicationNotification.Application.Common;
using CommunicationNotification.Application.Interfaces;
using Microsoft.Extensions.Logging;

namespace CommunicationNotification.Application.Commands.Notifications;

/// <summary>
/// Handler for sending individual notifications
/// </summary>
public class SendNotificationCommandHandler : ICommandHandler<SendNotificationCommand, CommandResult<SendNotificationResult>>
{
    private readonly INotificationService _notificationService;
    private readonly ICommunicationAuditService _auditService;
    private readonly ILogger<SendNotificationCommandHandler> _logger;

    public SendNotificationCommandHandler(
        INotificationService notificationService,
        ICommunicationAuditService auditService,
        ILogger<SendNotificationCommandHandler> logger)
    {
        _notificationService = notificationService;
        _auditService = auditService;
        _logger = logger;
    }

    public async Task<CommandResult<SendNotificationResult>> Handle(
        SendNotificationCommand request,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Processing send notification command {CommandId} for user {UserId}",
                request.CommandId, request.UserId);

            // Validate command
            var validationResult = await ValidateCommandAsync(request, cancellationToken);
            if (!validationResult.IsValid)
            {
                return CommandResult<SendNotificationResult>.ValidationFailure(
                    validationResult.ValidationErrors, request.CommandId);
            }

            // Create notification request
            var notificationRequest = new NotificationRequest
            {
                UserId = request.UserId,
                Content = request.Content,
                MessageType = request.MessageType,
                Priority = request.Priority,
                PreferredChannel = request.PreferredChannel,
                PreferredLanguage = request.PreferredLanguage,
                Subject = request.Subject,
                Parameters = request.Parameters,
                TemplateId = request.TemplateId,
                RequireDeliveryConfirmation = request.RequireDeliveryConfirmation,
                ScheduledAt = request.ScheduledAt,
                Tags = request.Tags,
                CustomHeaders = request.CustomHeaders,
                CorrelationId = request.CorrelationId
            };

            // Send notification
            var sendResult = await _notificationService.SendNotificationAsync(notificationRequest, cancellationToken);

            // Log audit event
            await LogAuditEventAsync(request, sendResult, cancellationToken);

            // Create result
            var result = new SendNotificationResult
            {
                NotificationId = sendResult.MessageId ?? Guid.NewGuid(),
                IsSuccess = sendResult.IsSuccess,
                ErrorMessage = sendResult.ErrorMessage,
                ChannelUsed = sendResult.ChannelUsed,
                SentAt = sendResult.SentAt,
                ExternalId = sendResult.ExternalId,
                DeliveryMetadata = sendResult.Metadata
            };

            _logger.LogInformation("Send notification command {CommandId} completed successfully. NotificationId: {NotificationId}",
                request.CommandId, result.NotificationId);

            return CommandResult<SendNotificationResult>.Success(result, request.CommandId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process send notification command {CommandId}", request.CommandId);
            return CommandResult<SendNotificationResult>.Failure(ex.Message, request.CommandId);
        }
    }

    private async Task<QueryValidationResult> ValidateCommandAsync(
        SendNotificationCommand command,
        CancellationToken cancellationToken)
    {
        var errors = new List<string>();

        if (command.UserId == Guid.Empty)
            errors.Add("UserId is required");

        if (string.IsNullOrWhiteSpace(command.Content) && string.IsNullOrWhiteSpace(command.TemplateId))
            errors.Add("Either Content or TemplateId must be provided");

        if (command.ScheduledAt.HasValue && command.ScheduledAt.Value <= DateTime.UtcNow)
            errors.Add("ScheduledAt must be in the future");

        return errors.Any() ? QueryValidationResult.Invalid(errors) : QueryValidationResult.Valid();
    }

    private async Task LogAuditEventAsync(
        SendNotificationCommand command,
        NotificationSendResult sendResult,
        CancellationToken cancellationToken)
    {
        try
        {
            var auditEvent = new CommunicationAuditEvent
            {
                EventType = AuditEventType.MessageSent,
                UserId = command.UserId,
                MessageId = sendResult.MessageId,
                Channel = sendResult.ChannelUsed,
                EventData = new Dictionary<string, object>
                {
                    ["messageType"] = command.MessageType.ToString(),
                    ["priority"] = command.Priority.ToString(),
                    ["isSuccess"] = sendResult.IsSuccess,
                    ["channelUsed"] = sendResult.ChannelUsed.ToString(),
                    ["commandId"] = command.CommandId
                },
                Severity = sendResult.IsSuccess ? AuditSeverity.Low : AuditSeverity.Medium,
                CorrelationId = command.CorrelationId
            };

            await _auditService.LogCommunicationEventAsync(auditEvent, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to log audit event for command {CommandId}", command.CommandId);
        }
    }
}

/// <summary>
/// Handler for sending bulk notifications
/// </summary>
public class SendBulkNotificationCommandHandler : ICommandHandler<SendBulkNotificationCommand, CommandResult<SendBulkNotificationResult>>
{
    private readonly INotificationService _notificationService;
    private readonly ICommunicationAuditService _auditService;
    private readonly ILogger<SendBulkNotificationCommandHandler> _logger;

    public SendBulkNotificationCommandHandler(
        INotificationService notificationService,
        ICommunicationAuditService auditService,
        ILogger<SendBulkNotificationCommandHandler> logger)
    {
        _notificationService = notificationService;
        _auditService = auditService;
        _logger = logger;
    }

    public async Task<CommandResult<SendBulkNotificationResult>> Handle(
        SendBulkNotificationCommand request,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Processing bulk notification command {CommandId} for {UserCount} users",
                request.CommandId, request.UserIds.Count);

            // Validate command
            var validationResult = await ValidateCommandAsync(request, cancellationToken);
            if (!validationResult.IsValid)
            {
                return CommandResult<SendBulkNotificationResult>.ValidationFailure(
                    validationResult.ValidationErrors, request.CommandId);
            }

            var bulkResult = new SendBulkNotificationResult
            {
                BulkOperationId = Guid.NewGuid(),
                TotalNotifications = request.UserIds.Count,
                StartedAt = DateTime.UtcNow
            };

            var results = new List<SendNotificationResult>();
            var errors = new List<string>();

            // Process in batches
            var batches = request.UserIds.Chunk(request.BatchSize);
            
            foreach (var batch in batches)
            {
                var batchTasks = batch.Select(async userId =>
                {
                    try
                    {
                        var notificationRequest = new NotificationRequest
                        {
                            UserId = userId,
                            Content = request.Content,
                            MessageType = request.MessageType,
                            Priority = request.Priority,
                            PreferredChannel = request.PreferredChannel,
                            PreferredLanguage = request.PreferredLanguage,
                            Subject = request.Subject,
                            Parameters = request.Parameters,
                            TemplateId = request.TemplateId,
                            RequireDeliveryConfirmation = request.RequireDeliveryConfirmation,
                            ScheduledAt = request.ScheduledAt,
                            Tags = request.Tags,
                            CorrelationId = request.CorrelationId
                        };

                        var sendResult = await _notificationService.SendNotificationAsync(notificationRequest, cancellationToken);

                        return new SendNotificationResult
                        {
                            NotificationId = sendResult.MessageId ?? Guid.NewGuid(),
                            IsSuccess = sendResult.IsSuccess,
                            ErrorMessage = sendResult.ErrorMessage,
                            ChannelUsed = sendResult.ChannelUsed,
                            SentAt = sendResult.SentAt,
                            ExternalId = sendResult.ExternalId,
                            DeliveryMetadata = sendResult.Metadata
                        };
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to send notification to user {UserId} in bulk operation {BulkOperationId}",
                            userId, bulkResult.BulkOperationId);

                        return new SendNotificationResult
                        {
                            NotificationId = Guid.NewGuid(),
                            IsSuccess = false,
                            ErrorMessage = ex.Message,
                            SentAt = DateTime.UtcNow
                        };
                    }
                });

                var batchResults = await Task.WhenAll(batchTasks);
                results.AddRange(batchResults);

                // Add delay between batches if specified
                if (request.BatchDelay > TimeSpan.Zero)
                {
                    await Task.Delay(request.BatchDelay, cancellationToken);
                }
            }

            bulkResult.Results = results;
            bulkResult.SuccessfulNotifications = results.Count(r => r.IsSuccess);
            bulkResult.FailedNotifications = results.Count(r => !r.IsSuccess);
            bulkResult.Errors = results.Where(r => !r.IsSuccess).Select(r => r.ErrorMessage ?? "Unknown error").ToList();
            bulkResult.CompletedAt = DateTime.UtcNow;
            bulkResult.IsSuccess = bulkResult.FailedNotifications == 0;

            // Log bulk audit event
            await LogBulkAuditEventAsync(request, bulkResult, cancellationToken);

            _logger.LogInformation("Bulk notification command {CommandId} completed. Success: {Success}/{Total}",
                request.CommandId, bulkResult.SuccessfulNotifications, bulkResult.TotalNotifications);

            return CommandResult<SendBulkNotificationResult>.Success(bulkResult, request.CommandId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process bulk notification command {CommandId}", request.CommandId);
            return CommandResult<SendBulkNotificationResult>.Failure(ex.Message, request.CommandId);
        }
    }

    private async Task<QueryValidationResult> ValidateCommandAsync(
        SendBulkNotificationCommand command,
        CancellationToken cancellationToken)
    {
        var errors = new List<string>();

        if (!command.UserIds.Any())
            errors.Add("At least one UserId is required");

        if (command.UserIds.Count > 10000)
            errors.Add("Maximum 10,000 users allowed per bulk operation");

        if (string.IsNullOrWhiteSpace(command.Content) && string.IsNullOrWhiteSpace(command.TemplateId))
            errors.Add("Either Content or TemplateId must be provided");

        if (command.BatchSize < 1 || command.BatchSize > 1000)
            errors.Add("BatchSize must be between 1 and 1000");

        return errors.Any() ? QueryValidationResult.Invalid(errors) : QueryValidationResult.Valid();
    }

    private async Task LogBulkAuditEventAsync(
        SendBulkNotificationCommand command,
        SendBulkNotificationResult result,
        CancellationToken cancellationToken)
    {
        try
        {
            var auditEvent = new CommunicationAuditEvent
            {
                EventType = AuditEventType.MessageSent,
                UserId = command.InitiatedBy ?? Guid.Empty,
                EventData = new Dictionary<string, object>
                {
                    ["bulkOperationId"] = result.BulkOperationId,
                    ["totalNotifications"] = result.TotalNotifications,
                    ["successfulNotifications"] = result.SuccessfulNotifications,
                    ["failedNotifications"] = result.FailedNotifications,
                    ["messageType"] = command.MessageType.ToString(),
                    ["priority"] = command.Priority.ToString(),
                    ["commandId"] = command.CommandId
                },
                Severity = result.IsSuccess ? AuditSeverity.Medium : AuditSeverity.High,
                CorrelationId = command.CorrelationId
            };

            await _auditService.LogCommunicationEventAsync(auditEvent, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to log bulk audit event for command {CommandId}", command.CommandId);
        }
    }
}
