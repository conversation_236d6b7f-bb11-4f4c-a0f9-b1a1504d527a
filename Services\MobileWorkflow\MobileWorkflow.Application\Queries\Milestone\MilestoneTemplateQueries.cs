using MediatR;
using MobileWorkflow.Application.DTOs;

namespace MobileWorkflow.Application.Queries.Milestone;

public class GetMilestoneTemplateByIdQuery : IRequest<MilestoneTemplateDto?>
{
    public Guid Id { get; set; }
}

public class GetMilestoneTemplateByNameQuery : IRequest<MilestoneTemplateDto?>
{
    public string Name { get; set; } = string.Empty;
}

public class GetMilestoneTemplatesByTypeQuery : IRequest<IEnumerable<MilestoneTemplateDto>>
{
    public string Type { get; set; } = string.Empty;
}

public class GetMilestoneTemplatesByCategoryQuery : IRequest<IEnumerable<MilestoneTemplateDto>>
{
    public string Category { get; set; } = string.Empty;
}

public class GetActiveMilestoneTemplatesQuery : IRequest<IEnumerable<MilestoneTemplateDto>>
{
}

public class GetDefaultMilestoneTemplatesQuery : IRequest<IEnumerable<MilestoneTemplateDto>>
{
}

public class GetDefaultMilestoneTemplateByTypeQuery : IRequest<MilestoneTemplateDto?>
{
    public string Type { get; set; } = string.Empty;
}

public class GetMilestoneTemplatesByCreatorQuery : IRequest<IEnumerable<MilestoneTemplateDto>>
{
    public string CreatedBy { get; set; } = string.Empty;
}

public class GetAllMilestoneTemplatesQuery : IRequest<IEnumerable<MilestoneTemplateDto>>
{
}

public class SearchMilestoneTemplatesQuery : IRequest<IEnumerable<MilestoneTemplateDto>>
{
    public string SearchTerm { get; set; } = string.Empty;
}

public class GetMostUsedMilestoneTemplatesQuery : IRequest<IEnumerable<MilestoneTemplateDto>>
{
    public int Count { get; set; } = 10;
}

public class GetMilestoneTemplatePreviewsQuery : IRequest<IEnumerable<MilestoneTemplatePreviewDto>>
{
    public string? Type { get; set; }
    public string? Category { get; set; }
    public bool? IsActive { get; set; }
    public bool? IsDefault { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 10;
}

public class ValidateMilestoneTemplateQuery : IRequest<MilestoneTemplateValidationResult>
{
    public Guid Id { get; set; }
}

// Milestone Step Queries
public class GetMilestoneStepByIdQuery : IRequest<MilestoneStepDto?>
{
    public Guid Id { get; set; }
}

public class GetMilestoneStepsByTemplateIdQuery : IRequest<IEnumerable<MilestoneStepDto>>
{
    public Guid TemplateId { get; set; }
}

public class GetActiveMilestoneStepsByTemplateIdQuery : IRequest<IEnumerable<MilestoneStepDto>>
{
    public Guid TemplateId { get; set; }
}

public class GetRequiredMilestoneStepsByTemplateIdQuery : IRequest<IEnumerable<MilestoneStepDto>>
{
    public Guid TemplateId { get; set; }
}

// Milestone Payout Rule Queries
public class GetMilestonePayoutRuleByIdQuery : IRequest<MilestonePayoutRuleDto?>
{
    public Guid Id { get; set; }
}

public class GetMilestonePayoutRulesByStepIdQuery : IRequest<IEnumerable<MilestonePayoutRuleDto>>
{
    public Guid StepId { get; set; }
}

public class GetMilestonePayoutRulesByTemplateIdQuery : IRequest<IEnumerable<MilestonePayoutRuleDto>>
{
    public Guid TemplateId { get; set; }
}

public class GetTotalPayoutPercentageByTemplateQuery : IRequest<decimal>
{
    public Guid TemplateId { get; set; }
}

// Role Template Mapping Queries
public class GetRoleTemplateMappingByIdQuery : IRequest<RoleTemplateMappingDto?>
{
    public Guid Id { get; set; }
}

public class GetRoleTemplateMappingsByRoleNameQuery : IRequest<IEnumerable<RoleTemplateMappingDto>>
{
    public string RoleName { get; set; } = string.Empty;
}

public class GetRoleTemplateMappingsByTemplateIdQuery : IRequest<IEnumerable<RoleTemplateMappingDto>>
{
    public Guid TemplateId { get; set; }
}

public class GetActiveRoleTemplateMappingsByRoleNameQuery : IRequest<IEnumerable<RoleTemplateMappingDto>>
{
    public string RoleName { get; set; } = string.Empty;
}

public class GetDefaultRoleTemplateMappingByRoleNameQuery : IRequest<RoleTemplateMappingDto?>
{
    public string RoleName { get; set; } = string.Empty;
}

public class GetBestRoleTemplateMappingQuery : IRequest<RoleTemplateMappingDto?>
{
    public string RoleName { get; set; } = string.Empty;
    public Dictionary<string, object> Context { get; set; } = new();
}

public class GetAllRoleTemplateMappingsQuery : IRequest<IEnumerable<RoleTemplateMappingDto>>
{
}

public class GetDistinctRoleNamesQuery : IRequest<IEnumerable<string>>
{
}
