# Analytics & BI Service Dockerfile
# Multi-stage build for optimized production image

# Base stage
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 8080

# Build stage
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy solution file
COPY ["TLIMicroservices.sln", "."]

# Copy project files for dependency resolution
COPY ["Services/AnalyticsBIService/AnalyticsBIService.API/AnalyticsBIService.API.csproj", "Services/AnalyticsBIService/AnalyticsBIService.API/"]
COPY ["Services/AnalyticsBIService/AnalyticsBIService.Application/AnalyticsBIService.Application.csproj", "Services/AnalyticsBIService/AnalyticsBIService.Application/"]
COPY ["Services/AnalyticsBIService/AnalyticsBIService.Domain/AnalyticsBIService.Domain.csproj", "Services/AnalyticsBIService/AnalyticsBIService.Domain/"]
COPY ["Services/AnalyticsBIService/AnalyticsBIService.Infrastructure/AnalyticsBIService.Infrastructure.csproj", "Services/AnalyticsBIService/AnalyticsBIService.Infrastructure/"]
COPY ["Shared/Shared.Domain/Shared.Domain.csproj", "Shared/Shared.Domain/"]
COPY ["Shared/Shared.Infrastructure/Shared.Infrastructure.csproj", "Shared/Shared.Infrastructure/"]
COPY ["Shared/Shared.Messaging/Shared.Messaging.csproj", "Shared/Shared.Messaging/"]

# Restore dependencies
RUN dotnet restore "Services/AnalyticsBIService/AnalyticsBIService.API/AnalyticsBIService.API.csproj"

# Copy all source code
COPY . .

# Build the application
WORKDIR "/src/Services/AnalyticsBIService/AnalyticsBIService.API"
RUN dotnet build "AnalyticsBIService.API.csproj" -c Release -o /app/build

# Publish stage
FROM build AS publish
RUN dotnet publish "AnalyticsBIService.API.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Final stage
FROM base AS final
WORKDIR /app

# Install additional packages for TimescaleDB connectivity and monitoring
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user for security
RUN groupadd -r analytics && useradd -r -g analytics analytics

# Copy published application
COPY --from=publish /app/publish .

# Create directories for logs and data
RUN mkdir -p /app/logs /app/data && \
    chown -R analytics:analytics /app

# Set environment variables
ENV ASPNETCORE_ENVIRONMENT=Production
ENV ASPNETCORE_URLS=http://+:8080
ENV DOTNET_RUNNING_IN_CONTAINER=true
ENV DOTNET_USE_POLLING_FILE_WATCHER=true

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Switch to non-root user
USER analytics

# Entry point
ENTRYPOINT ["dotnet", "AnalyticsBIService.API.dll"]
