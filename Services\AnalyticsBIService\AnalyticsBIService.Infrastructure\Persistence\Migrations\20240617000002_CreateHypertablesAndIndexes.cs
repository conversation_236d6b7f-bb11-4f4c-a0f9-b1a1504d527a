using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AnalyticsBIService.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class CreateHypertablesAndIndexes : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Create indexes for better performance
            migrationBuilder.Sql(@"
                -- Analytics Events indexes
                CREATE INDEX IF NOT EXISTS IX_analytics_events_timestamp ON analytics.analytics_events(timestamp);
                CREATE INDEX IF NOT EXISTS IX_analytics_events_event_type_timestamp ON analytics.analytics_events(event_type, timestamp);
                CREATE INDEX IF NOT EXISTS IX_analytics_events_user_id_timestamp ON analytics.analytics_events(user_id, timestamp);
                CREATE INDEX IF NOT EXISTS IX_analytics_events_data_source_timestamp ON analytics.analytics_events(data_source, timestamp);
                CREATE INDEX IF NOT EXISTS IX_analytics_events_event_name ON analytics.analytics_events(event_name);
                
                -- GIN indexes for JSONB columns
                CREATE INDEX IF NOT EXISTS IX_analytics_events_properties_gin ON analytics.analytics_events USING gin(properties);
                CREATE INDEX IF NOT EXISTS IX_analytics_events_metadata_gin ON analytics.analytics_events USING gin(metadata);
            ");

            migrationBuilder.Sql(@"
                -- Metrics indexes
                CREATE INDEX IF NOT EXISTS IX_metrics_name_user_id ON analytics.metrics(name, user_id);
                CREATE INDEX IF NOT EXISTS IX_metrics_category_type ON analytics.metrics(category, type);
                CREATE INDEX IF NOT EXISTS IX_metrics_created_at ON analytics.metrics(created_at);
                CREATE INDEX IF NOT EXISTS IX_metrics_tags_gin ON analytics.metrics USING gin(tags);
            ");

            migrationBuilder.Sql(@"
                -- Dashboard indexes
                CREATE INDEX IF NOT EXISTS IX_dashboards_user_id_type ON analytics.dashboards(user_id, type);
                CREATE INDEX IF NOT EXISTS IX_dashboards_is_default ON analytics.dashboards(is_default);
                
                -- Dashboard Widget indexes
                CREATE INDEX IF NOT EXISTS IX_dashboard_widgets_dashboard_id ON analytics.dashboard_widgets(dashboard_id);
                CREATE INDEX IF NOT EXISTS IX_dashboard_widgets_position ON analytics.dashboard_widgets(position);
            ");

            migrationBuilder.Sql(@"
                -- Report indexes
                CREATE INDEX IF NOT EXISTS IX_reports_generated_by_type ON analytics.reports(generated_by, type);
                CREATE INDEX IF NOT EXISTS IX_reports_generated_at ON analytics.reports(generated_at);
                CREATE INDEX IF NOT EXISTS IX_reports_is_scheduled ON analytics.reports(is_scheduled);
                
                -- Report Section indexes
                CREATE INDEX IF NOT EXISTS IX_report_sections_report_id ON analytics.report_sections(report_id);
                CREATE INDEX IF NOT EXISTS IX_report_sections_order ON analytics.report_sections(""order"");
                
                -- Report Export indexes
                CREATE INDEX IF NOT EXISTS IX_report_exports_report_id ON analytics.report_exports(report_id);
                CREATE INDEX IF NOT EXISTS IX_report_exports_exported_at ON analytics.report_exports(exported_at);
            ");

            migrationBuilder.Sql(@"
                -- Alert indexes
                CREATE INDEX IF NOT EXISTS IX_alerts_is_active_severity ON analytics.alerts(is_active, severity);
                CREATE INDEX IF NOT EXISTS IX_alerts_triggered_at ON analytics.alerts(triggered_at);
                CREATE INDEX IF NOT EXISTS IX_alerts_metric_id ON analytics.alerts(metric_id);
                CREATE INDEX IF NOT EXISTS IX_alerts_is_acknowledged ON analytics.alerts(is_acknowledged);
                CREATE INDEX IF NOT EXISTS IX_alerts_context_gin ON analytics.alerts USING gin(context);
                
                -- Alert Rule indexes
                CREATE INDEX IF NOT EXISTS IX_alert_rules_metric_name_is_enabled ON analytics.alert_rules(metric_name, is_enabled);
                CREATE INDEX IF NOT EXISTS IX_alert_rules_created_by_user_type ON analytics.alert_rules(created_by, user_type);
                CREATE INDEX IF NOT EXISTS IX_alert_rules_category ON analytics.alert_rules(category);
                CREATE INDEX IF NOT EXISTS IX_alert_rules_last_evaluated_at ON analytics.alert_rules(last_evaluated_at);
            ");

            // Create TimescaleDB hypertables for time-series data
            migrationBuilder.Sql(@"
                -- Create hypertable for analytics_events partitioned by timestamp
                SELECT create_hypertable('analytics.analytics_events', 'timestamp', 
                    chunk_time_interval => INTERVAL '1 day',
                    create_default_indexes => FALSE);
                
                -- Create hypertable for metrics partitioned by created_at
                SELECT create_hypertable('analytics.metrics', 'created_at', 
                    chunk_time_interval => INTERVAL '1 day',
                    create_default_indexes => FALSE);
                
                -- Create hypertable for alerts partitioned by triggered_at
                SELECT create_hypertable('analytics.alerts', 'triggered_at', 
                    chunk_time_interval => INTERVAL '1 day',
                    create_default_indexes => FALSE);
            ");

            // Create data retention policies for TimescaleDB
            migrationBuilder.Sql(@"
                -- Add data retention policy for analytics_events (keep for 2 years)
                SELECT add_retention_policy('analytics.analytics_events', INTERVAL '2 years');
                
                -- Add data retention policy for metrics (keep for 5 years)
                SELECT add_retention_policy('analytics.metrics', INTERVAL '5 years');
                
                -- Add data retention policy for alerts (keep for 3 years)
                SELECT add_retention_policy('analytics.alerts', INTERVAL '3 years');
            ");

            // Create continuous aggregates for analytics
            migrationBuilder.Sql(@"
                -- Create continuous aggregate for daily analytics events
                CREATE MATERIALIZED VIEW analytics.daily_analytics_events
                WITH (timescaledb.continuous) AS
                SELECT 
                    time_bucket('1 day', timestamp) AS day,
                    event_type,
                    data_source,
                    user_type,
                    COUNT(*) as event_count,
                    COUNT(DISTINCT user_id) as unique_users,
                    COUNT(DISTINCT session_id) as unique_sessions
                FROM analytics.analytics_events
                WHERE timestamp >= NOW() - INTERVAL '1 year'
                GROUP BY day, event_type, data_source, user_type
                WITH NO DATA;
                
                -- Create continuous aggregate for daily metrics
                CREATE MATERIALIZED VIEW analytics.daily_metrics
                WITH (timescaledb.continuous) AS
                SELECT 
                    time_bucket('1 day', created_at) AS day,
                    name,
                    category,
                    type,
                    user_type,
                    data_source,
                    AVG(metric_value) as avg_value,
                    MIN(metric_value) as min_value,
                    MAX(metric_value) as max_value,
                    COUNT(*) as metric_count
                FROM analytics.metrics
                WHERE created_at >= NOW() - INTERVAL '1 year'
                GROUP BY day, name, category, type, user_type, data_source
                WITH NO DATA;
                
                -- Create continuous aggregate for hourly real-time metrics
                CREATE MATERIALIZED VIEW analytics.hourly_real_time_metrics
                WITH (timescaledb.continuous) AS
                SELECT 
                    time_bucket('1 hour', created_at) AS hour,
                    name,
                    category,
                    user_type,
                    AVG(metric_value) as avg_value,
                    MAX(metric_value) as max_value,
                    COUNT(*) as metric_count
                FROM analytics.metrics
                WHERE created_at >= NOW() - INTERVAL '7 days'
                GROUP BY hour, name, category, user_type
                WITH NO DATA;
            ");

            // Add refresh policies for continuous aggregates
            migrationBuilder.Sql(@"
                -- Add refresh policy for daily analytics events (refresh every hour)
                SELECT add_continuous_aggregate_policy('analytics.daily_analytics_events',
                    start_offset => INTERVAL '1 month',
                    end_offset => INTERVAL '1 hour',
                    schedule_interval => INTERVAL '1 hour');
                
                -- Add refresh policy for daily metrics (refresh every hour)
                SELECT add_continuous_aggregate_policy('analytics.daily_metrics',
                    start_offset => INTERVAL '1 month',
                    end_offset => INTERVAL '1 hour',
                    schedule_interval => INTERVAL '1 hour');
                
                -- Add refresh policy for hourly real-time metrics (refresh every 15 minutes)
                SELECT add_continuous_aggregate_policy('analytics.hourly_real_time_metrics',
                    start_offset => INTERVAL '7 days',
                    end_offset => INTERVAL '15 minutes',
                    schedule_interval => INTERVAL '15 minutes');
            ");

            // Create additional analytics views
            migrationBuilder.Sql(@"
                -- Create view for KPI performance summary
                CREATE OR REPLACE VIEW analytics.kpi_performance_summary AS
                SELECT 
                    m.name,
                    m.category,
                    m.user_type,
                    m.metric_value as current_value,
                    m.target_value,
                    m.target_unit,
                    m.is_higher_better,
                    analytics.evaluate_kpi_performance(m.metric_value, m.target_value, COALESCE(m.is_higher_better, true)) as performance_status,
                    CASE 
                        WHEN m.target_value IS NOT NULL AND m.target_value != 0 THEN
                            ROUND((m.metric_value / m.target_value * 100)::numeric, 2)
                        ELSE NULL
                    END as target_achievement_percentage,
                    m.created_at as last_updated
                FROM analytics.metrics m
                WHERE m.target_value IS NOT NULL
                    AND m.created_at = (
                        SELECT MAX(created_at) 
                        FROM analytics.metrics m2 
                        WHERE m2.name = m.name 
                            AND m2.user_id = m.user_id
                    );
            ");

            migrationBuilder.Sql(@"
                -- Create view for alert summary
                CREATE OR REPLACE VIEW analytics.alert_summary AS
                SELECT 
                    severity,
                    is_active,
                    is_acknowledged,
                    COUNT(*) as alert_count,
                    COUNT(CASE WHEN is_active AND NOT is_acknowledged THEN 1 END) as unacknowledged_active_count,
                    AVG(EXTRACT(EPOCH FROM (COALESCE(resolved_at, NOW()) - triggered_at))) as avg_resolution_time_seconds,
                    MIN(triggered_at) as oldest_alert,
                    MAX(triggered_at) as newest_alert
                FROM analytics.alerts
                GROUP BY severity, is_active, is_acknowledged;
            ");

            // Create audit triggers for critical tables
            migrationBuilder.Sql(@"
                -- Create audit triggers for metrics table
                CREATE TRIGGER metrics_audit_trigger
                    AFTER INSERT OR UPDATE OR DELETE ON analytics.metrics
                    FOR EACH ROW EXECUTE FUNCTION analytics.audit_trigger_function();
                
                -- Create audit triggers for alerts table
                CREATE TRIGGER alerts_audit_trigger
                    AFTER INSERT OR UPDATE OR DELETE ON analytics.alerts
                    FOR EACH ROW EXECUTE FUNCTION analytics.audit_trigger_function();
                
                -- Create audit triggers for alert_rules table
                CREATE TRIGGER alert_rules_audit_trigger
                    AFTER INSERT OR UPDATE OR DELETE ON analytics.alert_rules
                    FOR EACH ROW EXECUTE FUNCTION analytics.audit_trigger_function();
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Drop audit triggers
            migrationBuilder.Sql("DROP TRIGGER IF EXISTS metrics_audit_trigger ON analytics.metrics;");
            migrationBuilder.Sql("DROP TRIGGER IF EXISTS alerts_audit_trigger ON analytics.alerts;");
            migrationBuilder.Sql("DROP TRIGGER IF EXISTS alert_rules_audit_trigger ON analytics.alert_rules;");

            // Drop views
            migrationBuilder.Sql("DROP VIEW IF EXISTS analytics.kpi_performance_summary;");
            migrationBuilder.Sql("DROP VIEW IF EXISTS analytics.alert_summary;");

            // Drop continuous aggregates
            migrationBuilder.Sql("DROP MATERIALIZED VIEW IF EXISTS analytics.daily_analytics_events;");
            migrationBuilder.Sql("DROP MATERIALIZED VIEW IF EXISTS analytics.daily_metrics;");
            migrationBuilder.Sql("DROP MATERIALIZED VIEW IF EXISTS analytics.hourly_real_time_metrics;");

            // Note: Hypertables and retention policies are automatically dropped when tables are dropped
        }
    }
}
