﻿Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Services", "Services", "{8B83C0F5-1A2B-4C3D-9E4F-5A6B7C8D9E0F}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Identity", "Identity", "{1A2B3C4D-5E6F-7A8B-9C0D-1E2F3A4B5C6D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Identity.Domain", "Identity\Identity.Domain\Identity.Domain.csproj", "{F089E5D0-5A01-4F9B-9816-5D5A316AE76C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Identity.Application", "Identity\Identity.Application\Identity.Application.csproj", "{83A95B14-B12A-4A4C-AA36-DE4E085A7F6A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Identity.Infrastructure", "Identity\Identity.Infrastructure\Identity.Infrastructure.csproj", "{CAEA704F-818C-4894-A56B-03D44D397BA9}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Identity.API", "Identity\Identity.API\Identity.API.csproj", "{5BD049B8-6EB2-4B5D-8C47-0B9312585429}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Identity.Tests", "Identity\Identity.Tests\Identity.Tests.csproj", "{6333271E-3623-4F27-A6BD-ED6CFBA23873}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "UserManagement", "UserManagement", "{A1B2C3D4-E5F6-7A8B-9C0D-1E2F3A4B5C6D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "UserManagement.Domain", "Services\UserManagement\UserManagement.Domain\UserManagement.Domain.csproj", "{B2C3D4E5-F6A7-8B9C-0D1E-2F3A4B5C6D7E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "UserManagement.Application", "Services\UserManagement\UserManagement.Application\UserManagement.Application.csproj", "{C3D4E5F6-A7B8-9C0D-1E2F-3A4B5C6D7E8F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "UserManagement.Infrastructure", "Services\UserManagement\UserManagement.Infrastructure\UserManagement.Infrastructure.csproj", "{D4E5F6A7-B8C9-0D1E-2F3A-4B5C6D7E8F9A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "UserManagement.API", "Services\UserManagement\UserManagement.API\UserManagement.API.csproj", "{E5F6A7B8-C9D0-1E2F-3A4B-5C6D7E8F9A0B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "UserManagement.Tests", "Services\UserManagement\UserManagement.Tests\UserManagement.Tests.csproj", "{F6A7B8C9-D0E1-2F3A-4B5C-6D7E8F9A0B1C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "SubscriptionManagement", "SubscriptionManagement", "{B1C2D3E4-F5A6-7B8C-9D0E-1F2A3B4C5D6E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SubscriptionManagement.Domain", "Services\SubscriptionManagement\SubscriptionManagement.Domain\SubscriptionManagement.Domain.csproj", "{C2D3E4F5-A6B7-8C9D-0E1F-2A3B4C5D6E7F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SubscriptionManagement.Application", "Services\SubscriptionManagement\SubscriptionManagement.Application\SubscriptionManagement.Application.csproj", "{D3E4F5A6-B7C8-9D0E-1F2A-3B4C5D6E7F8A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SubscriptionManagement.Infrastructure", "Services\SubscriptionManagement\SubscriptionManagement.Infrastructure\SubscriptionManagement.Infrastructure.csproj", "{E4F5A6B7-C8D9-0E1F-2A3B-4C5D6E7F8A9B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SubscriptionManagement.API", "Services\SubscriptionManagement\SubscriptionManagement.API\SubscriptionManagement.API.csproj", "{F5A6B7C8-D9E0-1F2A-3B4C-5D6E7F8A9B0C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SubscriptionManagement.Tests", "Services\SubscriptionManagement\SubscriptionManagement.Tests\SubscriptionManagement.Tests.csproj", "{A6B7C8D9-E0F1-2A3B-4C5D-6E7F8A9B0C1D}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Shared", "Shared", "{2B3C4D5E-6F7A-8B9C-0D1E-2F3A4B5C6D7E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Shared.Messaging", "Shared\Shared.Messaging\Shared.Messaging.csproj", "{0C2BB421-0589-4416-A741-47ECEABBB911}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Shared.Domain", "Shared\Shared.Domain\Shared.Domain.csproj", "{3C4D5E6F-7A8B-9C0D-1E2F-3A4B5C6D7E8F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Shared.Infrastructure", "Shared\Shared.Infrastructure\Shared.Infrastructure.csproj", "{4D5E6F7A-8B9C-0D1E-2F3A-4B5C6D7E8F9A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Gateway", "Gateway", "{5E6F7A8B-9C0D-1E2F-3A4B-5C6D7E8F9A0B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ApiGateway", "ApiGateway\ApiGateway.csproj", "{6F7A8B9C-0D1E-2F3A-4B5C-6D7E8F9A0B1C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Infrastructure", "Infrastructure", "{7A8B9C0D-1E2F-3A4B-5C6D-7E8F9A0B1C2D}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Docker", "Docker", "{8B9C0D1E-2F3A-4B5C-6D7E-8F9A0B1C2D3E}"
	ProjectSection(SolutionItems) = preProject
		docker-compose.yml = docker-compose.yml
		docker-compose.override.yml = docker-compose.override.yml
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{9C0D1E2F-3A4B-5C6D-7E8F-9A0B1C2D3E4F}"
	ProjectSection(SolutionItems) = preProject
		README.md = README.md
		.gitignore = .gitignore
		Directory.Build.props = Directory.Build.props
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "OrderManagement", "OrderManagement", "{40DA0DF9-6B51-451D-9AAC-183E0A9A7C16}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "OrderManagement.Domain", "Services\OrderManagement\OrderManagement.Domain\OrderManagement.Domain.csproj", "{518C8402-5DC2-4E55-9738-478DBAA60552}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "OrderManagement.Application", "Services\OrderManagement\OrderManagement.Application\OrderManagement.Application.csproj", "{D70DCF97-B7FF-4C4A-8F89-2F0BCE5D102D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "OrderManagement.Infrastructure", "Services\OrderManagement\OrderManagement.Infrastructure\OrderManagement.Infrastructure.csproj", "{01A919F7-EA70-41EB-94BF-FBB8D7E9D1E1}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "OrderManagement.API", "Services\OrderManagement\OrderManagement.API\OrderManagement.API.csproj", "{A7902077-F3ED-4F46-8776-E4C76EC071F0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "OrderManagement.IntegrationTests", "Services\OrderManagement\OrderManagement.IntegrationTests\OrderManagement.IntegrationTests.csproj", "{B63E6E96-1740-48B9-89A5-85F102CF88F6}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "TripManagement", "TripManagement", "{C1D2E3F4-A5B6-7C8D-9E0F-1A2B3C4D5E6F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TripManagement.Domain", "Services\TripManagement\TripManagement.Domain\TripManagement.Domain.csproj", "{D2E3F4A5-B6C7-8D9E-0F1A-2B3C4D5E6F7A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TripManagement.Application", "Services\TripManagement\TripManagement.Application\TripManagement.Application.csproj", "{E3F4A5B6-C7D8-9E0F-1A2B-3C4D5E6F7A8B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TripManagement.Infrastructure", "Services\TripManagement\TripManagement.Infrastructure\TripManagement.Infrastructure.csproj", "{F4A5B6C7-D8E9-0F1A-2B3C-4D5E6F7A8B9C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TripManagement.API", "Services\TripManagement\TripManagement.API\TripManagement.API.csproj", "{A5B6C7D8-E9F0-1A2B-3C4D-5E6F7A8B9C0D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TripManagement.Tests", "Services\TripManagement\TripManagement.Tests\TripManagement.Tests.csproj", "{B6C7D8E9-F0A1-2B3C-4D5E-6F7A8B9C0D1E}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "NetworkFleetManagement", "NetworkFleetManagement", "{C7D8E9F0-A1B2-3C4D-5E6F-7A8B9C0D1E2F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "NetworkFleetManagement.Domain", "Services\NetworkFleetManagement\NetworkFleetManagement.Domain\NetworkFleetManagement.Domain.csproj", "{D8E9F0A1-B2C3-4D5E-6F7A-8B9C0D1E2F3A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "CommunicationNotification", "CommunicationNotification", "{D8E9F0A1-B2C3-4D5E-6F7A-8B9C0D1E2F3A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CommunicationNotification.Domain", "Services\CommunicationNotification\CommunicationNotification.Domain\CommunicationNotification.Domain.csproj", "{E9F0A1B2-C3D4-5E6F-7A8B-9C0D1E2F3A4B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CommunicationNotification.Application", "Services\CommunicationNotification\CommunicationNotification.Application\CommunicationNotification.Application.csproj", "{F0A1B2C3-D4E5-6F7A-8B9C-0D1E2F3A4B5C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CommunicationNotification.Infrastructure", "Services\CommunicationNotification\CommunicationNotification.Infrastructure\CommunicationNotification.Infrastructure.csproj", "{A1B2C3D4-E5F6-7A8B-9C0D-1E2F3A4B5C6D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CommunicationNotification.API", "Services\CommunicationNotification\CommunicationNotification.API\CommunicationNotification.API.csproj", "{B2C3D4E5-F6A7-8B9C-0D1E-2F3A4B5C6D7E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CommunicationNotification.Tests", "Services\CommunicationNotification\CommunicationNotification.Tests\CommunicationNotification.Tests.csproj", "{C3D4E5F6-A7B8-9C0D-1E2F-3A4B5C6D7E8F}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "DataStorage", "DataStorage", "{D4E5F6A7-B8C9-0D1E-2F3A-4B5C6D7E8F9A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DataStorage.Domain", "Services\DataStorage\DataStorage.Domain\DataStorage.Domain.csproj", "{E5F6A7B8-C9D0-1E2F-3A4B-5C6D7E8F9A0B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DataStorage.Application", "Services\DataStorage\DataStorage.Application\DataStorage.Application.csproj", "{F6A7B8C9-D0E1-2F3A-4B5C-6D7E8F9A0B1C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DataStorage.Infrastructure", "Services\DataStorage\DataStorage.Infrastructure\DataStorage.Infrastructure.csproj", "{A7B8C9D0-E1F2-3A4B-5C6D-7E8F9A0B1C2D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DataStorage.API", "Services\DataStorage\DataStorage.API\DataStorage.API.csproj", "{B8C9D0E1-F2A3-4B5C-6D7E-8F9A0B1C2D3E}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "MonitoringObservability", "MonitoringObservability", "{C9D0E1F2-A3B4-5C6D-7E8F-9A0B1C2D3E4F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MonitoringObservability.Domain", "Services\MonitoringObservability\MonitoringObservability.Domain\MonitoringObservability.Domain.csproj", "{D0E1F2A3-B4C5-6D7E-8F9A-0B1C2D3E4F5A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MonitoringObservability.Application", "Services\MonitoringObservability\MonitoringObservability.Application\MonitoringObservability.Application.csproj", "{E1F2A3B4-C5D6-7E8F-9A0B-1C2D3E4F5A6B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MonitoringObservability.Infrastructure", "Services\MonitoringObservability\MonitoringObservability.Infrastructure\MonitoringObservability.Infrastructure.csproj", "{F2A3B4C5-D6E7-8F9A-0B1C-2D3E4F5A6B7C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MonitoringObservability.API", "Services\MonitoringObservability\MonitoringObservability.API\MonitoringObservability.API.csproj", "{A3B4C5D6-E7F8-9A0B-1C2D-3E4F5A6B7C8D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "NetworkFleetManagement.Application", "Services\NetworkFleetManagement\NetworkFleetManagement.Application\NetworkFleetManagement.Application.csproj", "{E9F0A1B2-C3D4-5E6F-7A8B-9C0D1E2F3A4B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "NetworkFleetManagement.Infrastructure", "Services\NetworkFleetManagement\NetworkFleetManagement.Infrastructure\NetworkFleetManagement.Infrastructure.csproj", "{F0A1B2C3-D4E5-6F7A-8B9C-0D1E2F3A4B5C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "NetworkFleetManagement.API", "Services\NetworkFleetManagement\NetworkFleetManagement.API\NetworkFleetManagement.API.csproj", "{A1B2C3D4-E5F6-7A8B-9C0D-1E2F3A4B5C6D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "NetworkFleetManagement.Tests", "Services\NetworkFleetManagement\NetworkFleetManagement.Tests\NetworkFleetManagement.Tests.csproj", "{B2C3D4E5-F6A7-8B9C-0D1E-2F3A4B5C6D7E}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "AnalyticsBIService", "AnalyticsBIService", "{E1F2A3B4-C5D6-7E8F-9A0B-1C2D3E4F5A6B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AnalyticsBIService.Domain", "Services\AnalyticsBIService\AnalyticsBIService.Domain\AnalyticsBIService.Domain.csproj", "{F2A3B4C5-D6E7-8F9A-0B1C-2D3E4F5A6B7C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AnalyticsBIService.Application", "Services\AnalyticsBIService\AnalyticsBIService.Application\AnalyticsBIService.Application.csproj", "{A3B4C5D6-E7F8-9A0B-1C2D-3E4F5A6B7C8D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AnalyticsBIService.Infrastructure", "Services\AnalyticsBIService\AnalyticsBIService.Infrastructure\AnalyticsBIService.Infrastructure.csproj", "{B4C5D6E7-F8A9-0B1C-2D3E-4F5A6B7C8D9E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AnalyticsBIService.API", "Services\AnalyticsBIService\AnalyticsBIService.API\AnalyticsBIService.API.csproj", "{C5D6E7F8-A9B0-1C2D-3E4F-5A6B7C8D9E0F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AnalyticsBIService.Tests", "Services\AnalyticsBIService\AnalyticsBIService.Tests\AnalyticsBIService.Tests.csproj", "{D6E7F8A9-B0C1-2D3E-4F5A-6B7C8D9E0F1A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "AuditCompliance", "AuditCompliance", "{E7F8A9B0-C1D2-3E4F-5A6B-7C8D9E0F1A2B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AuditCompliance.Domain", "Services\AuditCompliance\AuditCompliance.Domain\AuditCompliance.Domain.csproj", "{F8A9B0C1-D2E3-4F5A-6B7C-8D9E0F1A2B3C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AuditCompliance.Application", "Services\AuditCompliance\AuditCompliance.Application\AuditCompliance.Application.csproj", "{A9B0C1D2-E3F4-5A6B-7C8D-9E0F1A2B3C4D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AuditCompliance.Infrastructure", "Services\AuditCompliance\AuditCompliance.Infrastructure\AuditCompliance.Infrastructure.csproj", "{B0C1D2E3-F4A5-6B7C-8D9E-0F1A2B3C4D5E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AuditCompliance.API", "Services\AuditCompliance\AuditCompliance.API\AuditCompliance.API.csproj", "{C1D2E3F4-A5B6-7C8D-9E0F-1A2B3C4D5E6F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AuditCompliance.Tests", "Services\AuditCompliance\AuditCompliance.Tests\AuditCompliance.Tests.csproj", "{D2E3F4A5-B6C7-8D9E-0F1A-2B3C4D5E6F7A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "ContentManagement", "ContentManagement", "{E3F4A5B6-C7D8-9E0F-1A2B-3C4D5E6F7A8B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ContentManagement.Domain", "Services\ContentManagement\ContentManagement.Domain\ContentManagement.Domain.csproj", "{F4A5B6C7-D8E9-0F1A-2B3C-4D5E6F7A8B9C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ContentManagement.Application", "Services\ContentManagement\ContentManagement.Application\ContentManagement.Application.csproj", "{A5B6C7D8-E9F0-1A2B-3C4D-5E6F7A8B9C0D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ContentManagement.Infrastructure", "Services\ContentManagement\ContentManagement.Infrastructure\ContentManagement.Infrastructure.csproj", "{B6C7D8E9-F0A1-2B3C-4D5E-6F7A8B9C0D1E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ContentManagement.API", "Services\ContentManagement\ContentManagement.API\ContentManagement.API.csproj", "{C7D8E9F0-A1B2-3C4D-5E6F-7A8B9C0D1E2F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ContentManagement.Tests", "Services\ContentManagement\ContentManagement.Tests\ContentManagement.Tests.csproj", "{D8E9F0A1-B2C3-4D5E-6F7A-8B9C0D1E2F3A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "FinancialPayment", "FinancialPayment", "{E9F0A1B2-C3D4-5E6F-7A8B-9C0D1E2F3A4B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FinancialPayment.Domain", "Services\FinancialPayment\FinancialPayment.Domain\FinancialPayment.Domain.csproj", "{F0A1B2C3-D4E5-6F7A-8B9C-0D1E2F3A4B5C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FinancialPayment.Application", "Services\FinancialPayment\FinancialPayment.Application\FinancialPayment.Application.csproj", "{A1B2C3D4-E5F6-7A8B-9C0D-1E2F3A4B5C6D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FinancialPayment.Infrastructure", "Services\FinancialPayment\FinancialPayment.Infrastructure\FinancialPayment.Infrastructure.csproj", "{B2C3D4E5-F6A7-8B9C-0D1E-2F3A4B5C6D7E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FinancialPayment.API", "Services\FinancialPayment\FinancialPayment.API\FinancialPayment.API.csproj", "{C3D4E5F6-A7B8-9C0D-1E2F-3A4B5C6D7E8F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FinancialPayment.Tests", "Services\FinancialPayment\FinancialPayment.Tests\FinancialPayment.Tests.csproj", "{D4E5F6A7-B8C9-0D1E-2F3A-4B5C6D7E8F9A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "MobileWorkflow", "MobileWorkflow", "{E5F6A7B8-C9D0-1E2F-3A4B-5C6D7E8F9A0B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MobileWorkflow.Domain", "Services\MobileWorkflow\MobileWorkflow.Domain\MobileWorkflow.Domain.csproj", "{F6A7B8C9-D0E1-2F3A-4B5C-6D7E8F9A0B1C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MobileWorkflow.Application", "Services\MobileWorkflow\MobileWorkflow.Application\MobileWorkflow.Application.csproj", "{A7B8C9D0-E1F2-3A4B-5C6D-7E8F9A0B1C2D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MobileWorkflow.Infrastructure", "Services\MobileWorkflow\MobileWorkflow.Infrastructure\MobileWorkflow.Infrastructure.csproj", "{B8C9D0E1-F2A3-4B5C-6D7E-8F9A0B1C2D3E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MobileWorkflow.API", "Services\MobileWorkflow\MobileWorkflow.API\MobileWorkflow.API.csproj", "{C9D0E1F2-A3B4-5C6D-7E8F-9A0B1C2D3E4F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MobileWorkflow.Tests", "Services\MobileWorkflow\MobileWorkflow.Tests\MobileWorkflow.Tests.csproj", "{D0E1F2A3-B4C5-6D7E-8F9A-0B1C2D3E4F5A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TLI.Shared.Application", "Shared\TLI.Shared.Application\TLI.Shared.Application.csproj", "{831DE378-5D5A-4664-AC93-4AF90199EFEC}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TLI.Shared.Infrastructure", "Shared\TLI.Shared.Infrastructure\TLI.Shared.Infrastructure.csproj", "{1EC0DCFD-4B48-4F32-8363-213A3E44C74A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TLI.Shared.API", "Shared\TLI.Shared.API\TLI.Shared.API.csproj", "{17C4C6D4-7994-44C7-AB83-36C1C324DF04}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TLI.Shared.Domain", "Shared\TLI.Shared.Domain\TLI.Shared.Domain.csproj", "{7A41F614-207E-4D1A-8A04-9F9CC74563DB}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{F089E5D0-5A01-4F9B-9816-5D5A316AE76C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F089E5D0-5A01-4F9B-9816-5D5A316AE76C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F089E5D0-5A01-4F9B-9816-5D5A316AE76C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F089E5D0-5A01-4F9B-9816-5D5A316AE76C}.Release|Any CPU.Build.0 = Release|Any CPU
		{83A95B14-B12A-4A4C-AA36-DE4E085A7F6A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{83A95B14-B12A-4A4C-AA36-DE4E085A7F6A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{83A95B14-B12A-4A4C-AA36-DE4E085A7F6A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{83A95B14-B12A-4A4C-AA36-DE4E085A7F6A}.Release|Any CPU.Build.0 = Release|Any CPU
		{CAEA704F-818C-4894-A56B-03D44D397BA9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CAEA704F-818C-4894-A56B-03D44D397BA9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CAEA704F-818C-4894-A56B-03D44D397BA9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CAEA704F-818C-4894-A56B-03D44D397BA9}.Release|Any CPU.Build.0 = Release|Any CPU
		{5BD049B8-6EB2-4B5D-8C47-0B9312585429}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5BD049B8-6EB2-4B5D-8C47-0B9312585429}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5BD049B8-6EB2-4B5D-8C47-0B9312585429}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5BD049B8-6EB2-4B5D-8C47-0B9312585429}.Release|Any CPU.Build.0 = Release|Any CPU
		{6333271E-3623-4F27-A6BD-ED6CFBA23873}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6333271E-3623-4F27-A6BD-ED6CFBA23873}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6333271E-3623-4F27-A6BD-ED6CFBA23873}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6333271E-3623-4F27-A6BD-ED6CFBA23873}.Release|Any CPU.Build.0 = Release|Any CPU
		{B2C3D4E5-F6A7-8B9C-0D1E-2F3A4B5C6D7E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B2C3D4E5-F6A7-8B9C-0D1E-2F3A4B5C6D7E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B2C3D4E5-F6A7-8B9C-0D1E-2F3A4B5C6D7E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B2C3D4E5-F6A7-8B9C-0D1E-2F3A4B5C6D7E}.Release|Any CPU.Build.0 = Release|Any CPU
		{C3D4E5F6-A7B8-9C0D-1E2F-3A4B5C6D7E8F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C3D4E5F6-A7B8-9C0D-1E2F-3A4B5C6D7E8F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C3D4E5F6-A7B8-9C0D-1E2F-3A4B5C6D7E8F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C3D4E5F6-A7B8-9C0D-1E2F-3A4B5C6D7E8F}.Release|Any CPU.Build.0 = Release|Any CPU
		{E5F6A7B8-C9D0-1E2F-3A4B-5C6D7E8F9A0B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E5F6A7B8-C9D0-1E2F-3A4B-5C6D7E8F9A0B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E5F6A7B8-C9D0-1E2F-3A4B-5C6D7E8F9A0B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E5F6A7B8-C9D0-1E2F-3A4B-5C6D7E8F9A0B}.Release|Any CPU.Build.0 = Release|Any CPU
		{F6A7B8C9-D0E1-2F3A-4B5C-6D7E8F9A0B1C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F6A7B8C9-D0E1-2F3A-4B5C-6D7E8F9A0B1C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F6A7B8C9-D0E1-2F3A-4B5C-6D7E8F9A0B1C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F6A7B8C9-D0E1-2F3A-4B5C-6D7E8F9A0B1C}.Release|Any CPU.Build.0 = Release|Any CPU
		{A7B8C9D0-E1F2-3A4B-5C6D-7E8F9A0B1C2D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A7B8C9D0-E1F2-3A4B-5C6D-7E8F9A0B1C2D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A7B8C9D0-E1F2-3A4B-5C6D-7E8F9A0B1C2D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A7B8C9D0-E1F2-3A4B-5C6D-7E8F9A0B1C2D}.Release|Any CPU.Build.0 = Release|Any CPU
		{B8C9D0E1-F2A3-4B5C-6D7E-8F9A0B1C2D3E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B8C9D0E1-F2A3-4B5C-6D7E-8F9A0B1C2D3E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B8C9D0E1-F2A3-4B5C-6D7E-8F9A0B1C2D3E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B8C9D0E1-F2A3-4B5C-6D7E-8F9A0B1C2D3E}.Release|Any CPU.Build.0 = Release|Any CPU
		{D0E1F2A3-B4C5-6D7E-8F9A-0B1C2D3E4F5A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D0E1F2A3-B4C5-6D7E-8F9A-0B1C2D3E4F5A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D0E1F2A3-B4C5-6D7E-8F9A-0B1C2D3E4F5A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D0E1F2A3-B4C5-6D7E-8F9A-0B1C2D3E4F5A}.Release|Any CPU.Build.0 = Release|Any CPU
		{E1F2A3B4-C5D6-7E8F-9A0B-1C2D3E4F5A6B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E1F2A3B4-C5D6-7E8F-9A0B-1C2D3E4F5A6B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E1F2A3B4-C5D6-7E8F-9A0B-1C2D3E4F5A6B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E1F2A3B4-C5D6-7E8F-9A0B-1C2D3E4F5A6B}.Release|Any CPU.Build.0 = Release|Any CPU
		{F2A3B4C5-D6E7-8F9A-0B1C-2D3E4F5A6B7C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F2A3B4C5-D6E7-8F9A-0B1C-2D3E4F5A6B7C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F2A3B4C5-D6E7-8F9A-0B1C-2D3E4F5A6B7C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F2A3B4C5-D6E7-8F9A-0B1C-2D3E4F5A6B7C}.Release|Any CPU.Build.0 = Release|Any CPU
		{A3B4C5D6-E7F8-9A0B-1C2D-3E4F5A6B7C8D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A3B4C5D6-E7F8-9A0B-1C2D-3E4F5A6B7C8D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A3B4C5D6-E7F8-9A0B-1C2D-3E4F5A6B7C8D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A3B4C5D6-E7F8-9A0B-1C2D-3E4F5A6B7C8D}.Release|Any CPU.Build.0 = Release|Any CPU
		{D4E5F6A7-B8C9-0D1E-2F3A-4B5C6D7E8F9A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D4E5F6A7-B8C9-0D1E-2F3A-4B5C6D7E8F9A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D4E5F6A7-B8C9-0D1E-2F3A-4B5C6D7E8F9A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D4E5F6A7-B8C9-0D1E-2F3A-4B5C6D7E8F9A}.Release|Any CPU.Build.0 = Release|Any CPU
		{E5F6A7B8-C9D0-1E2F-3A4B-5C6D7E8F9A0B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E5F6A7B8-C9D0-1E2F-3A4B-5C6D7E8F9A0B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E5F6A7B8-C9D0-1E2F-3A4B-5C6D7E8F9A0B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E5F6A7B8-C9D0-1E2F-3A4B-5C6D7E8F9A0B}.Release|Any CPU.Build.0 = Release|Any CPU
		{F6A7B8C9-D0E1-2F3A-4B5C-6D7E8F9A0B1C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F6A7B8C9-D0E1-2F3A-4B5C-6D7E8F9A0B1C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F6A7B8C9-D0E1-2F3A-4B5C-6D7E8F9A0B1C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F6A7B8C9-D0E1-2F3A-4B5C-6D7E8F9A0B1C}.Release|Any CPU.Build.0 = Release|Any CPU
		{C2D3E4F5-A6B7-8C9D-0E1F-2A3B4C5D6E7F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C2D3E4F5-A6B7-8C9D-0E1F-2A3B4C5D6E7F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C2D3E4F5-A6B7-8C9D-0E1F-2A3B4C5D6E7F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C2D3E4F5-A6B7-8C9D-0E1F-2A3B4C5D6E7F}.Release|Any CPU.Build.0 = Release|Any CPU
		{D3E4F5A6-B7C8-9D0E-1F2A-3B4C5D6E7F8A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D3E4F5A6-B7C8-9D0E-1F2A-3B4C5D6E7F8A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D3E4F5A6-B7C8-9D0E-1F2A-3B4C5D6E7F8A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D3E4F5A6-B7C8-9D0E-1F2A-3B4C5D6E7F8A}.Release|Any CPU.Build.0 = Release|Any CPU
		{E4F5A6B7-C8D9-0E1F-2A3B-4C5D6E7F8A9B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E4F5A6B7-C8D9-0E1F-2A3B-4C5D6E7F8A9B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E4F5A6B7-C8D9-0E1F-2A3B-4C5D6E7F8A9B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E4F5A6B7-C8D9-0E1F-2A3B-4C5D6E7F8A9B}.Release|Any CPU.Build.0 = Release|Any CPU
		{F5A6B7C8-D9E0-1F2A-3B4C-5D6E7F8A9B0C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F5A6B7C8-D9E0-1F2A-3B4C-5D6E7F8A9B0C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F5A6B7C8-D9E0-1F2A-3B4C-5D6E7F8A9B0C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F5A6B7C8-D9E0-1F2A-3B4C-5D6E7F8A9B0C}.Release|Any CPU.Build.0 = Release|Any CPU
		{A6B7C8D9-E0F1-2A3B-4C5D-6E7F8A9B0C1D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A6B7C8D9-E0F1-2A3B-4C5D-6E7F8A9B0C1D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A6B7C8D9-E0F1-2A3B-4C5D-6E7F8A9B0C1D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A6B7C8D9-E0F1-2A3B-4C5D-6E7F8A9B0C1D}.Release|Any CPU.Build.0 = Release|Any CPU
		{0C2BB421-0589-4416-A741-47ECEABBB911}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0C2BB421-0589-4416-A741-47ECEABBB911}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0C2BB421-0589-4416-A741-47ECEABBB911}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0C2BB421-0589-4416-A741-47ECEABBB911}.Release|Any CPU.Build.0 = Release|Any CPU
		{3C4D5E6F-7A8B-9C0D-1E2F-3A4B5C6D7E8F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3C4D5E6F-7A8B-9C0D-1E2F-3A4B5C6D7E8F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3C4D5E6F-7A8B-9C0D-1E2F-3A4B5C6D7E8F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3C4D5E6F-7A8B-9C0D-1E2F-3A4B5C6D7E8F}.Release|Any CPU.Build.0 = Release|Any CPU
		{4D5E6F7A-8B9C-0D1E-2F3A-4B5C6D7E8F9A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4D5E6F7A-8B9C-0D1E-2F3A-4B5C6D7E8F9A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4D5E6F7A-8B9C-0D1E-2F3A-4B5C6D7E8F9A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4D5E6F7A-8B9C-0D1E-2F3A-4B5C6D7E8F9A}.Release|Any CPU.Build.0 = Release|Any CPU
		{6F7A8B9C-0D1E-2F3A-4B5C-6D7E8F9A0B1C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6F7A8B9C-0D1E-2F3A-4B5C-6D7E8F9A0B1C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6F7A8B9C-0D1E-2F3A-4B5C-6D7E8F9A0B1C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6F7A8B9C-0D1E-2F3A-4B5C-6D7E8F9A0B1C}.Release|Any CPU.Build.0 = Release|Any CPU
		{518C8402-5DC2-4E55-9738-478DBAA60552}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{518C8402-5DC2-4E55-9738-478DBAA60552}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{518C8402-5DC2-4E55-9738-478DBAA60552}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{518C8402-5DC2-4E55-9738-478DBAA60552}.Release|Any CPU.Build.0 = Release|Any CPU
		{D70DCF97-B7FF-4C4A-8F89-2F0BCE5D102D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D70DCF97-B7FF-4C4A-8F89-2F0BCE5D102D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D70DCF97-B7FF-4C4A-8F89-2F0BCE5D102D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D70DCF97-B7FF-4C4A-8F89-2F0BCE5D102D}.Release|Any CPU.Build.0 = Release|Any CPU
		{01A919F7-EA70-41EB-94BF-FBB8D7E9D1E1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{01A919F7-EA70-41EB-94BF-FBB8D7E9D1E1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{01A919F7-EA70-41EB-94BF-FBB8D7E9D1E1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{01A919F7-EA70-41EB-94BF-FBB8D7E9D1E1}.Release|Any CPU.Build.0 = Release|Any CPU
		{A7902077-F3ED-4F46-8776-E4C76EC071F0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A7902077-F3ED-4F46-8776-E4C76EC071F0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A7902077-F3ED-4F46-8776-E4C76EC071F0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A7902077-F3ED-4F46-8776-E4C76EC071F0}.Release|Any CPU.Build.0 = Release|Any CPU
		{B63E6E96-1740-48B9-89A5-85F102CF88F6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B63E6E96-1740-48B9-89A5-85F102CF88F6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B63E6E96-1740-48B9-89A5-85F102CF88F6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B63E6E96-1740-48B9-89A5-85F102CF88F6}.Release|Any CPU.Build.0 = Release|Any CPU
		{D2E3F4A5-B6C7-8D9E-0F1A-2B3C4D5E6F7A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D2E3F4A5-B6C7-8D9E-0F1A-2B3C4D5E6F7A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D2E3F4A5-B6C7-8D9E-0F1A-2B3C4D5E6F7A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D2E3F4A5-B6C7-8D9E-0F1A-2B3C4D5E6F7A}.Release|Any CPU.Build.0 = Release|Any CPU
		{E3F4A5B6-C7D8-9E0F-1A2B-3C4D5E6F7A8B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E3F4A5B6-C7D8-9E0F-1A2B-3C4D5E6F7A8B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E3F4A5B6-C7D8-9E0F-1A2B-3C4D5E6F7A8B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E3F4A5B6-C7D8-9E0F-1A2B-3C4D5E6F7A8B}.Release|Any CPU.Build.0 = Release|Any CPU
		{F4A5B6C7-D8E9-0F1A-2B3C-4D5E6F7A8B9C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F4A5B6C7-D8E9-0F1A-2B3C-4D5E6F7A8B9C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F4A5B6C7-D8E9-0F1A-2B3C-4D5E6F7A8B9C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F4A5B6C7-D8E9-0F1A-2B3C-4D5E6F7A8B9C}.Release|Any CPU.Build.0 = Release|Any CPU
		{A5B6C7D8-E9F0-1A2B-3C4D-5E6F7A8B9C0D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A5B6C7D8-E9F0-1A2B-3C4D-5E6F7A8B9C0D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A5B6C7D8-E9F0-1A2B-3C4D-5E6F7A8B9C0D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A5B6C7D8-E9F0-1A2B-3C4D-5E6F7A8B9C0D}.Release|Any CPU.Build.0 = Release|Any CPU
		{B6C7D8E9-F0A1-2B3C-4D5E-6F7A8B9C0D1E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B6C7D8E9-F0A1-2B3C-4D5E-6F7A8B9C0D1E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B6C7D8E9-F0A1-2B3C-4D5E-6F7A8B9C0D1E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B6C7D8E9-F0A1-2B3C-4D5E-6F7A8B9C0D1E}.Release|Any CPU.Build.0 = Release|Any CPU
		{D8E9F0A1-B2C3-4D5E-6F7A-8B9C0D1E2F3A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D8E9F0A1-B2C3-4D5E-6F7A-8B9C0D1E2F3A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D8E9F0A1-B2C3-4D5E-6F7A-8B9C0D1E2F3A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D8E9F0A1-B2C3-4D5E-6F7A-8B9C0D1E2F3A}.Release|Any CPU.Build.0 = Release|Any CPU
		{E9F0A1B2-C3D4-5E6F-7A8B-9C0D1E2F3A4B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E9F0A1B2-C3D4-5E6F-7A8B-9C0D1E2F3A4B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E9F0A1B2-C3D4-5E6F-7A8B-9C0D1E2F3A4B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E9F0A1B2-C3D4-5E6F-7A8B-9C0D1E2F3A4B}.Release|Any CPU.Build.0 = Release|Any CPU
		{F0A1B2C3-D4E5-6F7A-8B9C-0D1E2F3A4B5C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F0A1B2C3-D4E5-6F7A-8B9C-0D1E2F3A4B5C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F0A1B2C3-D4E5-6F7A-8B9C-0D1E2F3A4B5C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F0A1B2C3-D4E5-6F7A-8B9C-0D1E2F3A4B5C}.Release|Any CPU.Build.0 = Release|Any CPU
		{A1B2C3D4-E5F6-7A8B-9C0D-1E2F3A4B5C6D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7A8B-9C0D-1E2F3A4B5C6D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7A8B-9C0D-1E2F3A4B5C6D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7A8B-9C0D-1E2F3A4B5C6D}.Release|Any CPU.Build.0 = Release|Any CPU
		{B2C3D4E5-F6A7-8B9C-0D1E-2F3A4B5C6D7E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B2C3D4E5-F6A7-8B9C-0D1E-2F3A4B5C6D7E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B2C3D4E5-F6A7-8B9C-0D1E-2F3A4B5C6D7E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B2C3D4E5-F6A7-8B9C-0D1E-2F3A4B5C6D7E}.Release|Any CPU.Build.0 = Release|Any CPU
		{F2A3B4C5-D6E7-8F9A-0B1C-2D3E4F5A6B7C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F2A3B4C5-D6E7-8F9A-0B1C-2D3E4F5A6B7C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F2A3B4C5-D6E7-8F9A-0B1C-2D3E4F5A6B7C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F2A3B4C5-D6E7-8F9A-0B1C-2D3E4F5A6B7C}.Release|Any CPU.Build.0 = Release|Any CPU
		{A3B4C5D6-E7F8-9A0B-1C2D-3E4F5A6B7C8D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A3B4C5D6-E7F8-9A0B-1C2D-3E4F5A6B7C8D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A3B4C5D6-E7F8-9A0B-1C2D-3E4F5A6B7C8D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A3B4C5D6-E7F8-9A0B-1C2D-3E4F5A6B7C8D}.Release|Any CPU.Build.0 = Release|Any CPU
		{B4C5D6E7-F8A9-0B1C-2D3E-4F5A6B7C8D9E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B4C5D6E7-F8A9-0B1C-2D3E-4F5A6B7C8D9E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B4C5D6E7-F8A9-0B1C-2D3E-4F5A6B7C8D9E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B4C5D6E7-F8A9-0B1C-2D3E-4F5A6B7C8D9E}.Release|Any CPU.Build.0 = Release|Any CPU
		{C5D6E7F8-A9B0-1C2D-3E4F-5A6B7C8D9E0F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C5D6E7F8-A9B0-1C2D-3E4F-5A6B7C8D9E0F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C5D6E7F8-A9B0-1C2D-3E4F-5A6B7C8D9E0F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C5D6E7F8-A9B0-1C2D-3E4F-5A6B7C8D9E0F}.Release|Any CPU.Build.0 = Release|Any CPU
		{D6E7F8A9-B0C1-2D3E-4F5A-6B7C8D9E0F1A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D6E7F8A9-B0C1-2D3E-4F5A-6B7C8D9E0F1A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D6E7F8A9-B0C1-2D3E-4F5A-6B7C8D9E0F1A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D6E7F8A9-B0C1-2D3E-4F5A-6B7C8D9E0F1A}.Release|Any CPU.Build.0 = Release|Any CPU
		{F8A9B0C1-D2E3-4F5A-6B7C-8D9E0F1A2B3C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F8A9B0C1-D2E3-4F5A-6B7C-8D9E0F1A2B3C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F8A9B0C1-D2E3-4F5A-6B7C-8D9E0F1A2B3C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F8A9B0C1-D2E3-4F5A-6B7C-8D9E0F1A2B3C}.Release|Any CPU.Build.0 = Release|Any CPU
		{A9B0C1D2-E3F4-5A6B-7C8D-9E0F1A2B3C4D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A9B0C1D2-E3F4-5A6B-7C8D-9E0F1A2B3C4D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A9B0C1D2-E3F4-5A6B-7C8D-9E0F1A2B3C4D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A9B0C1D2-E3F4-5A6B-7C8D-9E0F1A2B3C4D}.Release|Any CPU.Build.0 = Release|Any CPU
		{B0C1D2E3-F4A5-6B7C-8D9E-0F1A2B3C4D5E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B0C1D2E3-F4A5-6B7C-8D9E-0F1A2B3C4D5E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B0C1D2E3-F4A5-6B7C-8D9E-0F1A2B3C4D5E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B0C1D2E3-F4A5-6B7C-8D9E-0F1A2B3C4D5E}.Release|Any CPU.Build.0 = Release|Any CPU
		{C1D2E3F4-A5B6-7C8D-9E0F-1A2B3C4D5E6F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C1D2E3F4-A5B6-7C8D-9E0F-1A2B3C4D5E6F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C1D2E3F4-A5B6-7C8D-9E0F-1A2B3C4D5E6F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C1D2E3F4-A5B6-7C8D-9E0F-1A2B3C4D5E6F}.Release|Any CPU.Build.0 = Release|Any CPU
		{D2E3F4A5-B6C7-8D9E-0F1A-2B3C4D5E6F7A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D2E3F4A5-B6C7-8D9E-0F1A-2B3C4D5E6F7A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D2E3F4A5-B6C7-8D9E-0F1A-2B3C4D5E6F7A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D2E3F4A5-B6C7-8D9E-0F1A-2B3C4D5E6F7A}.Release|Any CPU.Build.0 = Release|Any CPU
		{F4A5B6C7-D8E9-0F1A-2B3C-4D5E6F7A8B9C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F4A5B6C7-D8E9-0F1A-2B3C-4D5E6F7A8B9C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F4A5B6C7-D8E9-0F1A-2B3C-4D5E6F7A8B9C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F4A5B6C7-D8E9-0F1A-2B3C-4D5E6F7A8B9C}.Release|Any CPU.Build.0 = Release|Any CPU
		{A5B6C7D8-E9F0-1A2B-3C4D-5E6F7A8B9C0D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A5B6C7D8-E9F0-1A2B-3C4D-5E6F7A8B9C0D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A5B6C7D8-E9F0-1A2B-3C4D-5E6F7A8B9C0D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A5B6C7D8-E9F0-1A2B-3C4D-5E6F7A8B9C0D}.Release|Any CPU.Build.0 = Release|Any CPU
		{B6C7D8E9-F0A1-2B3C-4D5E-6F7A8B9C0D1E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B6C7D8E9-F0A1-2B3C-4D5E-6F7A8B9C0D1E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B6C7D8E9-F0A1-2B3C-4D5E-6F7A8B9C0D1E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B6C7D8E9-F0A1-2B3C-4D5E-6F7A8B9C0D1E}.Release|Any CPU.Build.0 = Release|Any CPU
		{C7D8E9F0-A1B2-3C4D-5E6F-7A8B9C0D1E2F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C7D8E9F0-A1B2-3C4D-5E6F-7A8B9C0D1E2F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C7D8E9F0-A1B2-3C4D-5E6F-7A8B9C0D1E2F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C7D8E9F0-A1B2-3C4D-5E6F-7A8B9C0D1E2F}.Release|Any CPU.Build.0 = Release|Any CPU
		{D8E9F0A1-B2C3-4D5E-6F7A-8B9C0D1E2F3A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D8E9F0A1-B2C3-4D5E-6F7A-8B9C0D1E2F3A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D8E9F0A1-B2C3-4D5E-6F7A-8B9C0D1E2F3A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D8E9F0A1-B2C3-4D5E-6F7A-8B9C0D1E2F3A}.Release|Any CPU.Build.0 = Release|Any CPU
		{F0A1B2C3-D4E5-6F7A-8B9C-0D1E2F3A4B5C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F0A1B2C3-D4E5-6F7A-8B9C-0D1E2F3A4B5C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F0A1B2C3-D4E5-6F7A-8B9C-0D1E2F3A4B5C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F0A1B2C3-D4E5-6F7A-8B9C-0D1E2F3A4B5C}.Release|Any CPU.Build.0 = Release|Any CPU
		{A1B2C3D4-E5F6-7A8B-9C0D-1E2F3A4B5C6D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7A8B-9C0D-1E2F3A4B5C6D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7A8B-9C0D-1E2F3A4B5C6D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7A8B-9C0D-1E2F3A4B5C6D}.Release|Any CPU.Build.0 = Release|Any CPU
		{B2C3D4E5-F6A7-8B9C-0D1E-2F3A4B5C6D7E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B2C3D4E5-F6A7-8B9C-0D1E-2F3A4B5C6D7E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B2C3D4E5-F6A7-8B9C-0D1E-2F3A4B5C6D7E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B2C3D4E5-F6A7-8B9C-0D1E-2F3A4B5C6D7E}.Release|Any CPU.Build.0 = Release|Any CPU
		{C3D4E5F6-A7B8-9C0D-1E2F-3A4B5C6D7E8F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C3D4E5F6-A7B8-9C0D-1E2F-3A4B5C6D7E8F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C3D4E5F6-A7B8-9C0D-1E2F-3A4B5C6D7E8F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C3D4E5F6-A7B8-9C0D-1E2F-3A4B5C6D7E8F}.Release|Any CPU.Build.0 = Release|Any CPU
		{D4E5F6A7-B8C9-0D1E-2F3A-4B5C6D7E8F9A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D4E5F6A7-B8C9-0D1E-2F3A-4B5C6D7E8F9A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D4E5F6A7-B8C9-0D1E-2F3A-4B5C6D7E8F9A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D4E5F6A7-B8C9-0D1E-2F3A-4B5C6D7E8F9A}.Release|Any CPU.Build.0 = Release|Any CPU
		{F6A7B8C9-D0E1-2F3A-4B5C-6D7E8F9A0B1C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F6A7B8C9-D0E1-2F3A-4B5C-6D7E8F9A0B1C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F6A7B8C9-D0E1-2F3A-4B5C-6D7E8F9A0B1C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F6A7B8C9-D0E1-2F3A-4B5C-6D7E8F9A0B1C}.Release|Any CPU.Build.0 = Release|Any CPU
		{A7B8C9D0-E1F2-3A4B-5C6D-7E8F9A0B1C2D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A7B8C9D0-E1F2-3A4B-5C6D-7E8F9A0B1C2D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A7B8C9D0-E1F2-3A4B-5C6D-7E8F9A0B1C2D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A7B8C9D0-E1F2-3A4B-5C6D-7E8F9A0B1C2D}.Release|Any CPU.Build.0 = Release|Any CPU
		{B8C9D0E1-F2A3-4B5C-6D7E-8F9A0B1C2D3E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B8C9D0E1-F2A3-4B5C-6D7E-8F9A0B1C2D3E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B8C9D0E1-F2A3-4B5C-6D7E-8F9A0B1C2D3E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B8C9D0E1-F2A3-4B5C-6D7E-8F9A0B1C2D3E}.Release|Any CPU.Build.0 = Release|Any CPU
		{C9D0E1F2-A3B4-5C6D-7E8F-9A0B1C2D3E4F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C9D0E1F2-A3B4-5C6D-7E8F-9A0B1C2D3E4F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C9D0E1F2-A3B4-5C6D-7E8F-9A0B1C2D3E4F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C9D0E1F2-A3B4-5C6D-7E8F-9A0B1C2D3E4F}.Release|Any CPU.Build.0 = Release|Any CPU
		{D0E1F2A3-B4C5-6D7E-8F9A-0B1C2D3E4F5A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D0E1F2A3-B4C5-6D7E-8F9A-0B1C2D3E4F5A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D0E1F2A3-B4C5-6D7E-8F9A-0B1C2D3E4F5A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D0E1F2A3-B4C5-6D7E-8F9A-0B1C2D3E4F5A}.Release|Any CPU.Build.0 = Release|Any CPU
		{831DE378-5D5A-4664-AC93-4AF90199EFEC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{831DE378-5D5A-4664-AC93-4AF90199EFEC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{831DE378-5D5A-4664-AC93-4AF90199EFEC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{831DE378-5D5A-4664-AC93-4AF90199EFEC}.Release|Any CPU.Build.0 = Release|Any CPU
		{1EC0DCFD-4B48-4F32-8363-213A3E44C74A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1EC0DCFD-4B48-4F32-8363-213A3E44C74A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1EC0DCFD-4B48-4F32-8363-213A3E44C74A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1EC0DCFD-4B48-4F32-8363-213A3E44C74A}.Release|Any CPU.Build.0 = Release|Any CPU
		{17C4C6D4-7994-44C7-AB83-36C1C324DF04}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{17C4C6D4-7994-44C7-AB83-36C1C324DF04}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{17C4C6D4-7994-44C7-AB83-36C1C324DF04}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{17C4C6D4-7994-44C7-AB83-36C1C324DF04}.Release|Any CPU.Build.0 = Release|Any CPU
		{7A41F614-207E-4D1A-8A04-9F9CC74563DB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7A41F614-207E-4D1A-8A04-9F9CC74563DB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7A41F614-207E-4D1A-8A04-9F9CC74563DB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7A41F614-207E-4D1A-8A04-9F9CC74563DB}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{1A2B3C4D-5E6F-7A8B-9C0D-1E2F3A4B5C6D} = {8B83C0F5-1A2B-4C3D-9E4F-5A6B7C8D9E0F}
		{F089E5D0-5A01-4F9B-9816-5D5A316AE76C} = {1A2B3C4D-5E6F-7A8B-9C0D-1E2F3A4B5C6D}
		{83A95B14-B12A-4A4C-AA36-DE4E085A7F6A} = {1A2B3C4D-5E6F-7A8B-9C0D-1E2F3A4B5C6D}
		{CAEA704F-818C-4894-A56B-03D44D397BA9} = {1A2B3C4D-5E6F-7A8B-9C0D-1E2F3A4B5C6D}
		{5BD049B8-6EB2-4B5D-8C47-0B9312585429} = {1A2B3C4D-5E6F-7A8B-9C0D-1E2F3A4B5C6D}
		{6333271E-3623-4F27-A6BD-ED6CFBA23873} = {1A2B3C4D-5E6F-7A8B-9C0D-1E2F3A4B5C6D}
		{A1B2C3D4-E5F6-7A8B-9C0D-1E2F3A4B5C6D} = {E9F0A1B2-C3D4-5E6F-7A8B-9C0D1E2F3A4B}
		{B2C3D4E5-F6A7-8B9C-0D1E-2F3A4B5C6D7E} = {E9F0A1B2-C3D4-5E6F-7A8B-9C0D1E2F3A4B}
		{C3D4E5F6-A7B8-9C0D-1E2F-3A4B5C6D7E8F} = {E9F0A1B2-C3D4-5E6F-7A8B-9C0D1E2F3A4B}
		{D4E5F6A7-B8C9-0D1E-2F3A-4B5C6D7E8F9A} = {E9F0A1B2-C3D4-5E6F-7A8B-9C0D1E2F3A4B}
		{E5F6A7B8-C9D0-1E2F-3A4B-5C6D7E8F9A0B} = {8B83C0F5-1A2B-4C3D-9E4F-5A6B7C8D9E0F}
		{F6A7B8C9-D0E1-2F3A-4B5C-6D7E8F9A0B1C} = {E5F6A7B8-C9D0-1E2F-3A4B-5C6D7E8F9A0B}
		{B1C2D3E4-F5A6-7B8C-9D0E-1F2A3B4C5D6E} = {8B83C0F5-1A2B-4C3D-9E4F-5A6B7C8D9E0F}
		{C2D3E4F5-A6B7-8C9D-0E1F-2A3B4C5D6E7F} = {B1C2D3E4-F5A6-7B8C-9D0E-1F2A3B4C5D6E}
		{D3E4F5A6-B7C8-9D0E-1F2A-3B4C5D6E7F8A} = {B1C2D3E4-F5A6-7B8C-9D0E-1F2A3B4C5D6E}
		{E4F5A6B7-C8D9-0E1F-2A3B-4C5D6E7F8A9B} = {B1C2D3E4-F5A6-7B8C-9D0E-1F2A3B4C5D6E}
		{F5A6B7C8-D9E0-1F2A-3B4C-5D6E7F8A9B0C} = {B1C2D3E4-F5A6-7B8C-9D0E-1F2A3B4C5D6E}
		{A6B7C8D9-E0F1-2A3B-4C5D-6E7F8A9B0C1D} = {B1C2D3E4-F5A6-7B8C-9D0E-1F2A3B4C5D6E}
		{0C2BB421-0589-4416-A741-47ECEABBB911} = {2B3C4D5E-6F7A-8B9C-0D1E-2F3A4B5C6D7E}
		{3C4D5E6F-7A8B-9C0D-1E2F-3A4B5C6D7E8F} = {2B3C4D5E-6F7A-8B9C-0D1E-2F3A4B5C6D7E}
		{4D5E6F7A-8B9C-0D1E-2F3A-4B5C6D7E8F9A} = {2B3C4D5E-6F7A-8B9C-0D1E-2F3A4B5C6D7E}
		{6F7A8B9C-0D1E-2F3A-4B5C-6D7E8F9A0B1C} = {5E6F7A8B-9C0D-1E2F-3A4B-5C6D7E8F9A0B}
		{8B9C0D1E-2F3A-4B5C-6D7E-8F9A0B1C2D3E} = {7A8B9C0D-1E2F-3A4B-5C6D-7E8F9A0B1C2D}
		{40DA0DF9-6B51-451D-9AAC-183E0A9A7C16} = {8B83C0F5-1A2B-4C3D-9E4F-5A6B7C8D9E0F}
		{518C8402-5DC2-4E55-9738-478DBAA60552} = {40DA0DF9-6B51-451D-9AAC-183E0A9A7C16}
		{D70DCF97-B7FF-4C4A-8F89-2F0BCE5D102D} = {40DA0DF9-6B51-451D-9AAC-183E0A9A7C16}
		{01A919F7-EA70-41EB-94BF-FBB8D7E9D1E1} = {40DA0DF9-6B51-451D-9AAC-183E0A9A7C16}
		{A7902077-F3ED-4F46-8776-E4C76EC071F0} = {40DA0DF9-6B51-451D-9AAC-183E0A9A7C16}
		{B63E6E96-1740-48B9-89A5-85F102CF88F6} = {40DA0DF9-6B51-451D-9AAC-183E0A9A7C16}
		{C1D2E3F4-A5B6-7C8D-9E0F-1A2B3C4D5E6F} = {E7F8A9B0-C1D2-3E4F-5A6B-7C8D9E0F1A2B}
		{D2E3F4A5-B6C7-8D9E-0F1A-2B3C4D5E6F7A} = {E7F8A9B0-C1D2-3E4F-5A6B-7C8D9E0F1A2B}
		{E3F4A5B6-C7D8-9E0F-1A2B-3C4D5E6F7A8B} = {8B83C0F5-1A2B-4C3D-9E4F-5A6B7C8D9E0F}
		{F4A5B6C7-D8E9-0F1A-2B3C-4D5E6F7A8B9C} = {E3F4A5B6-C7D8-9E0F-1A2B-3C4D5E6F7A8B}
		{A5B6C7D8-E9F0-1A2B-3C4D-5E6F7A8B9C0D} = {E3F4A5B6-C7D8-9E0F-1A2B-3C4D5E6F7A8B}
		{B6C7D8E9-F0A1-2B3C-4D5E-6F7A8B9C0D1E} = {E3F4A5B6-C7D8-9E0F-1A2B-3C4D5E6F7A8B}
		{C7D8E9F0-A1B2-3C4D-5E6F-7A8B9C0D1E2F} = {E3F4A5B6-C7D8-9E0F-1A2B-3C4D5E6F7A8B}
		{D8E9F0A1-B2C3-4D5E-6F7A-8B9C0D1E2F3A} = {E3F4A5B6-C7D8-9E0F-1A2B-3C4D5E6F7A8B}
		{E9F0A1B2-C3D4-5E6F-7A8B-9C0D1E2F3A4B} = {8B83C0F5-1A2B-4C3D-9E4F-5A6B7C8D9E0F}
		{F0A1B2C3-D4E5-6F7A-8B9C-0D1E2F3A4B5C} = {E9F0A1B2-C3D4-5E6F-7A8B-9C0D1E2F3A4B}
		{A7B8C9D0-E1F2-3A4B-5C6D-7E8F9A0B1C2D} = {E5F6A7B8-C9D0-1E2F-3A4B-5C6D7E8F9A0B}
		{B8C9D0E1-F2A3-4B5C-6D7E-8F9A0B1C2D3E} = {E5F6A7B8-C9D0-1E2F-3A4B-5C6D7E8F9A0B}
		{C9D0E1F2-A3B4-5C6D-7E8F-9A0B1C2D3E4F} = {E5F6A7B8-C9D0-1E2F-3A4B-5C6D7E8F9A0B}
		{D0E1F2A3-B4C5-6D7E-8F9A-0B1C2D3E4F5A} = {E5F6A7B8-C9D0-1E2F-3A4B-5C6D7E8F9A0B}
		{E1F2A3B4-C5D6-7E8F-9A0B-1C2D3E4F5A6B} = {8B83C0F5-1A2B-4C3D-9E4F-5A6B7C8D9E0F}
		{F2A3B4C5-D6E7-8F9A-0B1C-2D3E4F5A6B7C} = {E1F2A3B4-C5D6-7E8F-9A0B-1C2D3E4F5A6B}
		{A3B4C5D6-E7F8-9A0B-1C2D-3E4F5A6B7C8D} = {E1F2A3B4-C5D6-7E8F-9A0B-1C2D3E4F5A6B}
		{B4C5D6E7-F8A9-0B1C-2D3E-4F5A6B7C8D9E} = {E1F2A3B4-C5D6-7E8F-9A0B-1C2D3E4F5A6B}
		{C5D6E7F8-A9B0-1C2D-3E4F-5A6B7C8D9E0F} = {E1F2A3B4-C5D6-7E8F-9A0B-1C2D3E4F5A6B}
		{D6E7F8A9-B0C1-2D3E-4F5A-6B7C8D9E0F1A} = {E1F2A3B4-C5D6-7E8F-9A0B-1C2D3E4F5A6B}
		{E7F8A9B0-C1D2-3E4F-5A6B-7C8D9E0F1A2B} = {8B83C0F5-1A2B-4C3D-9E4F-5A6B7C8D9E0F}
		{F8A9B0C1-D2E3-4F5A-6B7C-8D9E0F1A2B3C} = {E7F8A9B0-C1D2-3E4F-5A6B-7C8D9E0F1A2B}
		{A9B0C1D2-E3F4-5A6B-7C8D-9E0F1A2B3C4D} = {E7F8A9B0-C1D2-3E4F-5A6B-7C8D9E0F1A2B}
		{B0C1D2E3-F4A5-6B7C-8D9E-0F1A2B3C4D5E} = {E7F8A9B0-C1D2-3E4F-5A6B-7C8D9E0F1A2B}
		{831DE378-5D5A-4664-AC93-4AF90199EFEC} = {2B3C4D5E-6F7A-8B9C-0D1E-2F3A4B5C6D7E}
		{1EC0DCFD-4B48-4F32-8363-213A3E44C74A} = {2B3C4D5E-6F7A-8B9C-0D1E-2F3A4B5C6D7E}
		{17C4C6D4-7994-44C7-AB83-36C1C324DF04} = {2B3C4D5E-6F7A-8B9C-0D1E-2F3A4B5C6D7E}
		{7A41F614-207E-4D1A-8A04-9F9CC74563DB} = {2B3C4D5E-6F7A-8B9C-0D1E-2F3A4B5C6D7E}
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal
