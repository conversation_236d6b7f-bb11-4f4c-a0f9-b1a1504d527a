using Shared.Domain.Common;
using TripManagement.Domain.Enums;

namespace TripManagement.Domain.Entities;

public class TripFeedback : BaseEntity
{
    public Guid TripId { get; private set; }
    public Guid ReviewerId { get; private set; }
    public string ReviewerRole { get; private set; } = string.Empty; // "Shipper", "Broker", "Admin"
    public Guid RevieweeId { get; private set; }
    public string RevieweeRole { get; private set; } = string.Empty; // "Driver", "Carrier", "Vehicle"

    // Overall rating (1-5 stars)
    public decimal OverallRating { get; private set; }

    // Detailed ratings
    public decimal ServiceQualityRating { get; private set; }
    public decimal TimelinessRating { get; private set; }
    public decimal CommunicationRating { get; private set; }
    public decimal ProfessionalismRating { get; private set; }
    public decimal VehicleConditionRating { get; private set; }
    public decimal CargoHandlingRating { get; private set; }

    // Feedback content
    public string Comments { get; private set; } = string.Empty;
    public List<string> PositiveAspects { get; private set; } = new();
    public List<string> ImprovementAreas { get; private set; } = new();
    public List<string> Tags { get; private set; } = new();

    // Feedback metadata
    public FeedbackType FeedbackType { get; private set; }
    public bool IsAnonymous { get; private set; }
    public bool IsVerified { get; private set; }
    public DateTime SubmittedAt { get; private set; }
    public DateTime? VerifiedAt { get; private set; }
    public Guid? VerifiedBy { get; private set; }

    // Trip context
    public DateTime TripStartedAt { get; private set; }
    public DateTime TripCompletedAt { get; private set; }
    public decimal TripDistanceKm { get; private set; }
    public TimeSpan TripDuration { get; private set; }
    public bool WasDelayed { get; private set; }
    public TimeSpan? DelayDuration { get; private set; }

    // Additional data
    public Dictionary<string, object> Metadata { get; private set; } = new();

    // Navigation properties
    public Trip Trip { get; private set; } = null!;

    private TripFeedback() { }

    public TripFeedback(
        Guid tripId,
        Guid reviewerId,
        string reviewerRole,
        Guid revieweeId,
        string revieweeRole,
        decimal overallRating,
        string comments,
        FeedbackType feedbackType = FeedbackType.PostTrip,
        bool isAnonymous = false)
    {
        if (overallRating < 1 || overallRating > 5)
            throw new ArgumentException("Overall rating must be between 1 and 5", nameof(overallRating));

        TripId = tripId;
        ReviewerId = reviewerId;
        ReviewerRole = reviewerRole;
        RevieweeId = revieweeId;
        RevieweeRole = revieweeRole;
        OverallRating = overallRating;
        Comments = comments;
        FeedbackType = feedbackType;
        IsAnonymous = isAnonymous;
        SubmittedAt = DateTime.UtcNow;
        IsVerified = false;

        // Initialize with default ratings
        ServiceQualityRating = overallRating;
        TimelinessRating = overallRating;
        CommunicationRating = overallRating;
        ProfessionalismRating = overallRating;
        VehicleConditionRating = overallRating;
        CargoHandlingRating = overallRating;
    }

    public void UpdateDetailedRatings(
        decimal serviceQuality,
        decimal timeliness,
        decimal communication,
        decimal professionalism,
        decimal vehicleCondition,
        decimal cargoHandling)
    {
        ValidateRating(serviceQuality, nameof(serviceQuality));
        ValidateRating(timeliness, nameof(timeliness));
        ValidateRating(communication, nameof(communication));
        ValidateRating(professionalism, nameof(professionalism));
        ValidateRating(vehicleCondition, nameof(vehicleCondition));
        ValidateRating(cargoHandling, nameof(cargoHandling));

        ServiceQualityRating = serviceQuality;
        TimelinessRating = timeliness;
        CommunicationRating = communication;
        ProfessionalismRating = professionalism;
        VehicleConditionRating = vehicleCondition;
        CargoHandlingRating = cargoHandling;

        // Recalculate overall rating as average
        OverallRating = Math.Round((serviceQuality + timeliness + communication +
                                  professionalism + vehicleCondition + cargoHandling) / 6, 1);
    }

    public void AddPositiveAspects(List<string> aspects)
    {
        PositiveAspects.AddRange(aspects.Where(a => !string.IsNullOrWhiteSpace(a)));
    }

    public void AddImprovementAreas(List<string> areas)
    {
        ImprovementAreas.AddRange(areas.Where(a => !string.IsNullOrWhiteSpace(a)));
    }

    public void AddTags(List<string> tags)
    {
        Tags.AddRange(tags.Where(t => !string.IsNullOrWhiteSpace(t)));
    }

    public void SetTripContext(
        DateTime tripStartedAt,
        DateTime tripCompletedAt,
        decimal tripDistanceKm,
        bool wasDelayed,
        TimeSpan? delayDuration = null)
    {
        TripStartedAt = tripStartedAt;
        TripCompletedAt = tripCompletedAt;
        TripDistanceKm = tripDistanceKm;
        TripDuration = tripCompletedAt - tripStartedAt;
        WasDelayed = wasDelayed;
        DelayDuration = delayDuration;
    }

    public void Verify(Guid verifiedBy)
    {
        IsVerified = true;
        VerifiedAt = DateTime.UtcNow;
        VerifiedBy = verifiedBy;
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    public bool IsHighRating() => OverallRating >= 4.0m;
    public bool IsLowRating() => OverallRating <= 2.0m;
    public bool HasConcerns() => ImprovementAreas.Any() || IsLowRating();

    private static void ValidateRating(decimal rating, string paramName)
    {
        if (rating < 1 || rating > 5)
            throw new ArgumentException($"Rating must be between 1 and 5", paramName);
    }
}

public enum FeedbackType
{
    PostTrip,
    MidTrip,
    Emergency,
    FollowUp
}


