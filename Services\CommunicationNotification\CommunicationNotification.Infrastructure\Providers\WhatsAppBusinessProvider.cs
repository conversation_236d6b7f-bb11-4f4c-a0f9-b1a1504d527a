using System.Text;
using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;
using CommunicationNotification.Infrastructure.Configuration;
using CommunicationNotification.Infrastructure.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace CommunicationNotification.Infrastructure.Providers;

/// <summary>
/// WhatsApp Business API provider implementation
/// </summary>
public class WhatsAppBusinessProvider : IWhatsAppProvider
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<WhatsAppBusinessProvider> _logger;
    private readonly WhatsAppConfiguration _configuration;

    public NotificationChannel Channel => NotificationChannel.WhatsApp;

    public WhatsAppBusinessProvider(
        HttpClient httpClient,
        IConfiguration configuration,
        ILogger<WhatsAppBusinessProvider> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
        _configuration = configuration.GetSection("WhatsApp").Get<WhatsAppConfiguration>()
            ?? throw new InvalidOperationException("WhatsApp configuration is missing");

        ConfigureHttpClient();
    }

    public async Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(_configuration.AccessToken) ||
                string.IsNullOrEmpty(_configuration.PhoneNumberId))
            {
                return false;
            }

            // Test API connectivity by getting phone number info
            var response = await _httpClient.GetAsync(
                $"{_configuration.BaseUrl}/{_configuration.PhoneNumberId}",
                cancellationToken);

            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check WhatsApp API availability");
            return false;
        }
    }

    public async Task<NotificationResult> SendAsync(
        ContactInfo recipient,
        MessageContent content,
        Dictionary<string, string>? metadata = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (!ValidateRecipient(recipient))
            {
                return NotificationResult.Failure("Invalid WhatsApp number");
            }

            var result = await SendTextMessageAsync(
                recipient.WhatsAppNumber ?? recipient.PhoneNumber,
                content.Body,
                cancellationToken);

            return result.IsSuccess
                ? NotificationResult.Success(result.MessageId!, DeliveryStatus.Sent)
                : NotificationResult.Failure(result.ErrorMessage!);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send WhatsApp message to {PhoneNumber}",
                recipient.WhatsAppNumber ?? recipient.PhoneNumber);
            return NotificationResult.Failure($"Failed to send WhatsApp message: {ex.Message}");
        }
    }

    public async Task<DeliveryStatus> GetDeliveryStatusAsync(
        string externalId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var status = await GetMessageStatusAsync(externalId, cancellationToken);

            return status.Status switch
            {
                WhatsAppDeliveryStatus.Pending => DeliveryStatus.Pending,
                WhatsAppDeliveryStatus.Sent => DeliveryStatus.Sent,
                WhatsAppDeliveryStatus.Delivered => DeliveryStatus.Delivered,
                WhatsAppDeliveryStatus.Read => DeliveryStatus.Read,
                WhatsAppDeliveryStatus.Failed => DeliveryStatus.Failed,
                WhatsAppDeliveryStatus.Rejected => DeliveryStatus.Failed,
                _ => DeliveryStatus.Pending
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get WhatsApp message status for {MessageId}", externalId);
            return DeliveryStatus.Failed;
        }
    }

    public bool ValidateRecipient(ContactInfo recipient)
    {
        var phoneNumber = recipient.WhatsAppNumber ?? recipient.PhoneNumber;

        if (string.IsNullOrWhiteSpace(phoneNumber))
            return false;

        // WhatsApp phone number should start with country code
        return phoneNumber.StartsWith("+") && phoneNumber.Length >= 10;
    }

    public async Task<WhatsAppResult> SendTextMessageAsync(
        string phoneNumber,
        string message,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var payload = new
            {
                messaging_product = "whatsapp",
                to = phoneNumber.TrimStart('+'),
                type = "text",
                text = new { body = message }
            };

            var json = JsonConvert.SerializeObject(payload);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync(
                $"{_configuration.BaseUrl}/{_configuration.PhoneNumberId}/messages",
                content,
                cancellationToken);

            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var result = JsonConvert.DeserializeObject<WhatsAppApiResponse>(responseContent);
                var messageId = result?.Messages?.FirstOrDefault()?.Id;

                _logger.LogInformation("WhatsApp message sent successfully to {PhoneNumber}, MessageId: {MessageId}",
                    phoneNumber, messageId);

                return WhatsAppResult.Success(messageId ?? Guid.NewGuid().ToString());
            }
            else
            {
                var error = JsonConvert.DeserializeObject<WhatsAppApiError>(responseContent);
                var errorMessage = error?.Error?.Message ?? "Unknown error";
                var errorCode = error?.Error?.Code?.ToString();

                _logger.LogError("Failed to send WhatsApp message to {PhoneNumber}: {ErrorMessage}",
                    phoneNumber, errorMessage);

                return WhatsAppResult.Failure(errorMessage, errorCode);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception occurred while sending WhatsApp message to {PhoneNumber}", phoneNumber);
            return WhatsAppResult.Failure($"Exception: {ex.Message}");
        }
    }

    public async Task<WhatsAppResult> SendTemplateMessageAsync(
        WhatsAppTemplateRequest templateRequest,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var components = new List<object>();

            if (templateRequest.Parameters?.Any() == true)
            {
                var parameters = templateRequest.Parameters.Select(p => new
                {
                    type = p.Type,
                    text = p.Value
                }).ToArray();

                components.Add(new
                {
                    type = "body",
                    parameters = parameters
                });
            }

            var payload = new
            {
                messaging_product = "whatsapp",
                to = templateRequest.PhoneNumber.TrimStart('+'),
                type = "template",
                template = new
                {
                    name = templateRequest.TemplateName,
                    language = new { code = templateRequest.LanguageCode },
                    components = components.ToArray()
                }
            };

            var json = JsonConvert.SerializeObject(payload);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync(
                $"{_configuration.BaseUrl}/{_configuration.PhoneNumberId}/messages",
                content,
                cancellationToken);

            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var result = JsonConvert.DeserializeObject<WhatsAppApiResponse>(responseContent);
                var messageId = result?.Messages?.FirstOrDefault()?.Id;

                _logger.LogInformation("WhatsApp template message sent successfully to {PhoneNumber}, Template: {TemplateName}, MessageId: {MessageId}",
                    templateRequest.PhoneNumber, templateRequest.TemplateName, messageId);

                return WhatsAppResult.Success(messageId ?? Guid.NewGuid().ToString());
            }
            else
            {
                var error = JsonConvert.DeserializeObject<WhatsAppApiError>(responseContent);
                var errorMessage = error?.Error?.Message ?? "Unknown error";
                var errorCode = error?.Error?.Code?.ToString();

                _logger.LogError("Failed to send WhatsApp template message to {PhoneNumber}: {ErrorMessage}",
                    templateRequest.PhoneNumber, errorMessage);

                return WhatsAppResult.Failure(errorMessage, errorCode);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception occurred while sending WhatsApp template message to {PhoneNumber}",
                templateRequest.PhoneNumber);
            return WhatsAppResult.Failure($"Exception: {ex.Message}");
        }
    }

    public async Task<WhatsAppResult> SendMediaMessageAsync(
        WhatsAppMediaRequest mediaRequest,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var mediaObject = new Dictionary<string, object>
            {
                ["link"] = mediaRequest.MediaUrl
            };

            if (!string.IsNullOrEmpty(mediaRequest.Caption))
            {
                mediaObject["caption"] = mediaRequest.Caption;
            }

            if (!string.IsNullOrEmpty(mediaRequest.FileName))
            {
                mediaObject["filename"] = mediaRequest.FileName;
            }

            var payload = new
            {
                messaging_product = "whatsapp",
                to = mediaRequest.PhoneNumber.TrimStart('+'),
                type = mediaRequest.MediaType.ToString().ToLower(),
                [mediaRequest.MediaType.ToString().ToLower()] = mediaObject
            };

            var json = JsonConvert.SerializeObject(payload);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync(
                $"{_configuration.BaseUrl}/{_configuration.PhoneNumberId}/messages",
                content,
                cancellationToken);

            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var result = JsonConvert.DeserializeObject<WhatsAppApiResponse>(responseContent);
                var messageId = result?.Messages?.FirstOrDefault()?.Id;

                _logger.LogInformation("WhatsApp media message sent successfully to {PhoneNumber}, Type: {MediaType}, MessageId: {MessageId}",
                    mediaRequest.PhoneNumber, mediaRequest.MediaType, messageId);

                return WhatsAppResult.Success(messageId ?? Guid.NewGuid().ToString());
            }
            else
            {
                var error = JsonConvert.DeserializeObject<WhatsAppApiError>(responseContent);
                var errorMessage = error?.Error?.Message ?? "Unknown error";
                var errorCode = error?.Error?.Code?.ToString();

                _logger.LogError("Failed to send WhatsApp media message to {PhoneNumber}: {ErrorMessage}",
                    mediaRequest.PhoneNumber, errorMessage);

                return WhatsAppResult.Failure(errorMessage, errorCode);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception occurred while sending WhatsApp media message to {PhoneNumber}",
                mediaRequest.PhoneNumber);
            return WhatsAppResult.Failure($"Exception: {ex.Message}");
        }
    }

    public async Task<WhatsAppResult> SendLocationMessageAsync(
        WhatsAppLocationRequest locationRequest,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var locationObject = new
            {
                latitude = locationRequest.Latitude,
                longitude = locationRequest.Longitude,
                name = locationRequest.Name,
                address = locationRequest.Address
            };

            var payload = new
            {
                messaging_product = "whatsapp",
                to = locationRequest.PhoneNumber.TrimStart('+'),
                type = "location",
                location = locationObject
            };

            var json = JsonConvert.SerializeObject(payload);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync(
                $"{_configuration.BaseUrl}/{_configuration.PhoneNumberId}/messages",
                content,
                cancellationToken);

            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var result = JsonConvert.DeserializeObject<WhatsAppApiResponse>(responseContent);
                var messageId = result?.Messages?.FirstOrDefault()?.Id;

                _logger.LogInformation("WhatsApp location message sent successfully to {PhoneNumber}, MessageId: {MessageId}",
                    locationRequest.PhoneNumber, messageId);

                return WhatsAppResult.Success(messageId ?? Guid.NewGuid().ToString());
            }
            else
            {
                var error = JsonConvert.DeserializeObject<WhatsAppApiError>(responseContent);
                var errorMessage = error?.Error?.Message ?? "Unknown error";
                var errorCode = error?.Error?.Code?.ToString();

                _logger.LogError("Failed to send WhatsApp location message to {PhoneNumber}: {ErrorMessage}",
                    locationRequest.PhoneNumber, errorMessage);

                return WhatsAppResult.Failure(errorMessage, errorCode);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception occurred while sending WhatsApp location message to {PhoneNumber}",
                locationRequest.PhoneNumber);
            return WhatsAppResult.Failure($"Exception: {ex.Message}");
        }
    }

    public async Task<WhatsAppMessageStatus> GetMessageStatusAsync(
        string messageId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _httpClient.GetAsync(
                $"{_configuration.BaseUrl}/{messageId}",
                cancellationToken);

            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var result = JsonConvert.DeserializeObject<WhatsAppMessageStatusResponse>(responseContent);

                return new WhatsAppMessageStatus
                {
                    MessageId = messageId,
                    PhoneNumber = result?.To ?? string.Empty,
                    Status = ParseDeliveryStatus(result?.Status),
                    DeliveredAt = result?.DeliveredAt,
                    ReadAt = result?.ReadAt,
                    ErrorCode = result?.Error?.Code?.ToString(),
                    ErrorMessage = result?.Error?.Message
                };
            }
            else
            {
                _logger.LogError("Failed to get WhatsApp message status for {MessageId}: {StatusCode}",
                    messageId, response.StatusCode);

                return new WhatsAppMessageStatus
                {
                    MessageId = messageId,
                    Status = WhatsAppDeliveryStatus.Failed,
                    ErrorMessage = $"API call failed with status {response.StatusCode}"
                };
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception occurred while getting WhatsApp message status for {MessageId}", messageId);

            return new WhatsAppMessageStatus
            {
                MessageId = messageId,
                Status = WhatsAppDeliveryStatus.Failed,
                ErrorMessage = $"Exception: {ex.Message}"
            };
        }
    }

    public async Task<bool> VerifyPhoneNumberAsync(
        string phoneNumber,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // WhatsApp doesn't provide a direct verification API
            // This is a basic validation based on format
            if (string.IsNullOrWhiteSpace(phoneNumber))
                return false;

            var cleanNumber = phoneNumber.TrimStart('+');

            // Basic validation: should be numeric and have reasonable length
            return cleanNumber.All(char.IsDigit) && cleanNumber.Length >= 10 && cleanNumber.Length <= 15;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception occurred while verifying WhatsApp phone number {PhoneNumber}", phoneNumber);
            return false;
        }
    }

    public async Task<List<WhatsAppTemplate>> GetTemplatesAsync(
        CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _httpClient.GetAsync(
                $"{_configuration.BaseUrl}/{_configuration.BusinessAccountId}/message_templates",
                cancellationToken);

            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var result = JsonConvert.DeserializeObject<WhatsAppTemplatesResponse>(responseContent);

                return result?.Data?.Select(t => new WhatsAppTemplate
                {
                    Name = t.Name ?? string.Empty,
                    Language = t.Language ?? string.Empty,
                    Status = t.Status ?? string.Empty,
                    Category = t.Category ?? string.Empty,
                    Components = t.Components?.Select(c => new WhatsAppTemplateComponent
                    {
                        Type = c.Type ?? string.Empty,
                        Text = c.Text,
                        Parameters = c.Parameters?.Select(p => new WhatsAppTemplateParameter
                        {
                            Type = p.Type ?? "text",
                            Value = p.Value ?? string.Empty
                        }).ToList() ?? new List<WhatsAppTemplateParameter>()
                    }).ToList() ?? new List<WhatsAppTemplateComponent>()
                }).ToList() ?? new List<WhatsAppTemplate>();
            }
            else
            {
                _logger.LogError("Failed to get WhatsApp templates: {StatusCode}", response.StatusCode);
                return new List<WhatsAppTemplate>();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception occurred while getting WhatsApp templates");
            return new List<WhatsAppTemplate>();
        }
    }

    private void ConfigureHttpClient()
    {
        _httpClient.DefaultRequestHeaders.Clear();
        _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_configuration.AccessToken}");
        _httpClient.DefaultRequestHeaders.Add("User-Agent", "TLI-Communication-Service/1.0");
    }

    private static WhatsAppDeliveryStatus ParseDeliveryStatus(string? status)
    {
        return status?.ToLower() switch
        {
            "sent" => WhatsAppDeliveryStatus.Sent,
            "delivered" => WhatsAppDeliveryStatus.Delivered,
            "read" => WhatsAppDeliveryStatus.Read,
            "failed" => WhatsAppDeliveryStatus.Failed,
            "rejected" => WhatsAppDeliveryStatus.Rejected,
            _ => WhatsAppDeliveryStatus.Pending
        };
    }
}
