using NetworkFleetManagement.Domain.Enums;
using NetworkFleetManagement.Domain.ValueObjects;
using Shared.Domain.Common;

namespace NetworkFleetManagement.Domain.Entities;

/// <summary>
/// Represents a preferred partner relationship between different user types
/// (Broker-Carrier, Transporter-Broker, Shipper-Transporter, etc.)
/// </summary>
public class PreferredPartner : AggregateRoot
{
    public Guid UserId { get; private set; } // The user who has the preference
    public Guid PartnerId { get; private set; } // The preferred partner
    public PreferredPartnerType PartnerType { get; private set; } // Broker, Carrier, Transporter, Shipper
    public PreferenceLevel PreferenceLevel { get; private set; } // Primary, Secondary, Tertiary
    public int Priority { get; private set; } // 1 = highest priority within the preference level
    public PreferenceStatus Status { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? ActivatedAt { get; private set; }
    public DateTime? DeactivatedAt { get; private set; }
    public string? DeactivationReason { get; private set; }
    public string? Notes { get; private set; }

    // Performance and relationship metrics
    public PartnerPerformanceMetrics PerformanceMetrics { get; private set; }
    public decimal? PreferredCommissionRate { get; private set; }
    public decimal? PreferredServiceRate { get; private set; }
    public bool IsExclusive { get; private set; }
    public DateTime? ExclusivityExpiresAt { get; private set; }

    // Contract and agreement details
    public string? AgreementNumber { get; private set; }
    public DateTime? AgreementStartDate { get; private set; }
    public DateTime? AgreementEndDate { get; private set; }
    public int? PaymentTermsDays { get; private set; }
    public string? SpecialTerms { get; private set; }

    // Auto-assignment preferences
    public bool AutoAssignEnabled { get; private set; }
    public decimal? AutoAssignThreshold { get; private set; } // Minimum rating/score for auto-assignment
    public List<string> PreferredRoutes { get; private set; } = new();
    public List<string> PreferredLoadTypes { get; private set; } = new();
    public List<string> ExcludedRoutes { get; private set; } = new();
    public List<string> ExcludedLoadTypes { get; private set; } = new();

    // Notification preferences
    public bool NotifyOnNewOpportunities { get; private set; }
    public bool NotifyOnPerformanceChanges { get; private set; }
    public bool NotifyOnContractExpiry { get; private set; }

    // Performance tracking
    private readonly List<PartnerPerformanceRecord> _performanceHistory = new();
    public IReadOnlyCollection<PartnerPerformanceRecord> PerformanceHistory => _performanceHistory.AsReadOnly();

    // Parameterless constructor for EF Core
    private PreferredPartner()
    {
        PerformanceMetrics = new PartnerPerformanceMetrics();
    }

    public PreferredPartner(
        Guid userId,
        Guid partnerId,
        PreferredPartnerType partnerType,
        PreferenceLevel preferenceLevel = PreferenceLevel.Secondary,
        int priority = 10,
        decimal? preferredCommissionRate = null,
        decimal? preferredServiceRate = null,
        bool autoAssignEnabled = false,
        string? notes = null)
    {
        UserId = userId;
        PartnerId = partnerId;
        PartnerType = partnerType;
        PreferenceLevel = preferenceLevel;
        Priority = priority;
        Status = PreferenceStatus.Active;
        CreatedAt = DateTime.UtcNow;
        ActivatedAt = DateTime.UtcNow;
        PerformanceMetrics = new PartnerPerformanceMetrics();
        PreferredCommissionRate = preferredCommissionRate;
        PreferredServiceRate = preferredServiceRate;
        AutoAssignEnabled = autoAssignEnabled;
        Notes = notes;
        NotifyOnNewOpportunities = true;
        NotifyOnPerformanceChanges = true;
        NotifyOnContractExpiry = true;
    }

    // Properties for business logic
    public bool IsActive => Status == PreferenceStatus.Active;
    public bool IsAgreementValid => !AgreementEndDate.HasValue || AgreementEndDate.Value > DateTime.UtcNow;
    public bool IsExclusivityActive => IsExclusive && (!ExclusivityExpiresAt.HasValue || ExclusivityExpiresAt.Value > DateTime.UtcNow);
    public bool CanAutoAssign => AutoAssignEnabled && IsActive && IsAgreementValid;

    public void Activate(string? notes = null)
    {
        if (Status == PreferenceStatus.Active)
            return;

        Status = PreferenceStatus.Active;
        ActivatedAt = DateTime.UtcNow;
        DeactivatedAt = null;
        DeactivationReason = null;

        if (!string.IsNullOrEmpty(notes))
            Notes = notes;

        SetUpdatedAt();
        AddDomainEvent(new PreferredPartnerActivatedEvent(UserId, PartnerId, Id, PartnerType));
    }

    public void Deactivate(string reason, DateTime? deactivationDate = null)
    {
        if (Status == PreferenceStatus.Inactive)
            return;

        Status = PreferenceStatus.Inactive;
        DeactivatedAt = deactivationDate ?? DateTime.UtcNow;
        DeactivationReason = reason;
        SetUpdatedAt();

        AddDomainEvent(new PreferredPartnerDeactivatedEvent(UserId, PartnerId, Id, PartnerType, reason));
    }

    public void UpdatePreferenceLevel(PreferenceLevel newLevel, int newPriority)
    {
        if (newPriority < 1)
            throw new ArgumentException("Priority must be at least 1", nameof(newPriority));

        PreferenceLevel = newLevel;
        Priority = newPriority;
        SetUpdatedAt();

        AddDomainEvent(new PreferredPartnerPreferenceLevelUpdatedEvent(UserId, PartnerId, Id, newLevel, newPriority));
    }

    public void UpdateRates(decimal? commissionRate = null, decimal? serviceRate = null)
    {
        if (commissionRate.HasValue && commissionRate.Value < 0)
            throw new ArgumentException("Commission rate cannot be negative", nameof(commissionRate));

        if (serviceRate.HasValue && serviceRate.Value < 0)
            throw new ArgumentException("Service rate cannot be negative", nameof(serviceRate));

        PreferredCommissionRate = commissionRate;
        PreferredServiceRate = serviceRate;
        SetUpdatedAt();
    }

    public void SetExclusivity(bool isExclusive, DateTime? expiresAt = null)
    {
        IsExclusive = isExclusive;
        ExclusivityExpiresAt = isExclusive ? expiresAt : null;
        SetUpdatedAt();

        AddDomainEvent(new PreferredPartnerExclusivityUpdatedEvent(UserId, PartnerId, Id, isExclusive, expiresAt));
    }

    public void UpdateAutoAssignSettings(
        bool enabled,
        decimal? threshold = null,
        List<string>? preferredRoutes = null,
        List<string>? preferredLoadTypes = null,
        List<string>? excludedRoutes = null,
        List<string>? excludedLoadTypes = null)
    {
        AutoAssignEnabled = enabled;
        AutoAssignThreshold = threshold;

        if (preferredRoutes != null)
            PreferredRoutes = preferredRoutes;

        if (preferredLoadTypes != null)
            PreferredLoadTypes = preferredLoadTypes;

        if (excludedRoutes != null)
            ExcludedRoutes = excludedRoutes;

        if (excludedLoadTypes != null)
            ExcludedLoadTypes = excludedLoadTypes;

        SetUpdatedAt();
    }

    public void UpdateNotificationPreferences(
        bool notifyOnNewOpportunities,
        bool notifyOnPerformanceChanges,
        bool notifyOnContractExpiry)
    {
        NotifyOnNewOpportunities = notifyOnNewOpportunities;
        NotifyOnPerformanceChanges = notifyOnPerformanceChanges;
        NotifyOnContractExpiry = notifyOnContractExpiry;
        SetUpdatedAt();
    }

    public void UpdateAgreementDetails(
        string? agreementNumber = null,
        DateTime? startDate = null,
        DateTime? endDate = null,
        int? paymentTermsDays = null,
        string? specialTerms = null)
    {
        AgreementNumber = agreementNumber;
        AgreementStartDate = startDate;
        AgreementEndDate = endDate;
        PaymentTermsDays = paymentTermsDays;
        SpecialTerms = specialTerms;
        SetUpdatedAt();
    }

    public void UpdatePerformanceMetrics(PartnerPerformanceMetrics newMetrics)
    {
        var oldMetrics = PerformanceMetrics;
        PerformanceMetrics = newMetrics;
        SetUpdatedAt();

        // Add performance record for tracking history
        var record = new PartnerPerformanceRecord(Id, newMetrics, DateTime.UtcNow);
        _performanceHistory.Add(record);

        // Notify if there's a significant change
        if (HasSignificantPerformanceChange(oldMetrics, newMetrics) && NotifyOnPerformanceChanges)
        {
            AddDomainEvent(new PreferredPartnerPerformanceChangedEvent(UserId, PartnerId, Id, oldMetrics, newMetrics));
        }
    }

    public bool IsEligibleForRoute(string route)
    {
        if (ExcludedRoutes.Contains(route))
            return false;

        if (PreferredRoutes.Any() && !PreferredRoutes.Contains(route))
            return false;

        return true;
    }

    public bool IsEligibleForLoadType(string loadType)
    {
        if (ExcludedLoadTypes.Contains(loadType))
            return false;

        if (PreferredLoadTypes.Any() && !PreferredLoadTypes.Contains(loadType))
            return false;

        return true;
    }

    public bool MeetsAutoAssignCriteria(decimal currentRating)
    {
        if (!CanAutoAssign)
            return false;

        if (AutoAssignThreshold.HasValue && currentRating < AutoAssignThreshold.Value)
            return false;

        return true;
    }

    private bool HasSignificantPerformanceChange(PartnerPerformanceMetrics oldMetrics, PartnerPerformanceMetrics newMetrics)
    {
        // Define what constitutes a significant change (e.g., 10% change in rating)
        const decimal significantChangeThreshold = 0.1m;

        if (Math.Abs(oldMetrics.OverallRating - newMetrics.OverallRating) >= significantChangeThreshold)
            return true;

        if (Math.Abs(oldMetrics.OnTimePerformance - newMetrics.OnTimePerformance) >= significantChangeThreshold)
            return true;

        return false;
    }

    // Domain event support is inherited from AggregateRoot base class
}

// Supporting enums
public enum PreferredPartnerType
{
    Broker = 1,
    Carrier = 2,
    Transporter = 3,
    Shipper = 4
}

public enum PreferenceLevel
{
    Primary = 1,    // Highest preference, first choice
    Secondary = 2,  // Second choice
    Tertiary = 3    // Third choice, backup option
}

public enum PreferenceStatus
{
    Active = 1,
    Inactive = 2,
    Suspended = 3
}

// Domain Events
public record PreferredPartnerActivatedEvent(Guid UserId, Guid PartnerId, Guid PreferenceId, PreferredPartnerType PartnerType) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record PreferredPartnerDeactivatedEvent(Guid UserId, Guid PartnerId, Guid PreferenceId, PreferredPartnerType PartnerType, string Reason) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record PreferredPartnerPreferenceLevelUpdatedEvent(Guid UserId, Guid PartnerId, Guid PreferenceId, PreferenceLevel NewLevel, int NewPriority) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record PreferredPartnerExclusivityUpdatedEvent(Guid UserId, Guid PartnerId, Guid PreferenceId, bool IsExclusive, DateTime? ExpiresAt) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record PreferredPartnerPerformanceChangedEvent(Guid UserId, Guid PartnerId, Guid PreferenceId, PartnerPerformanceMetrics OldMetrics, PartnerPerformanceMetrics NewMetrics) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}
