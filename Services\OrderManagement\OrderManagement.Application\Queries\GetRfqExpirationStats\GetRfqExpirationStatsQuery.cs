using MediatR;

namespace OrderManagement.Application.Queries.GetRfqExpirationStats;

public class GetRfqExpirationStatsQuery : IRequest<RfqExpirationStatsDto>
{
    public Guid? TransportCompanyId { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
}

public class RfqExpirationStatsDto
{
    public int TotalActiveRfqs { get; set; }
    public int ExpiredRfqs { get; set; }
    public int ExpiringSoon24Hours { get; set; }
    public int ExpiringSoon12Hours { get; set; }
    public int ExpiringSoon6Hours { get; set; }
    public int ExpiringSoon1Hour { get; set; }
    public int ExtendedRfqs { get; set; }
    public double AverageTimeframeHours { get; set; }
    public double ExpirationRate { get; set; }
    public List<RfqExpirationTrendDto> ExpirationTrend { get; set; } = new();
}

public class RfqExpirationTrendDto
{
    public DateTime Date { get; set; }
    public int ExpiredCount { get; set; }
    public int ExtendedCount { get; set; }
    public int CreatedCount { get; set; }
}
