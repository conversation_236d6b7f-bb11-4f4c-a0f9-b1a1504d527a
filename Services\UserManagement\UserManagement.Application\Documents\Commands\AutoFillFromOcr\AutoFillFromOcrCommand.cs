using MediatR;
using UserManagement.Domain.Entities;

namespace UserManagement.Application.Documents.Commands.AutoFillFromOcr;

public class AutoFillFromOcrCommand : IRequest<AutoFillFromOcrResponse>
{
    public string FilePath { get; set; } = string.Empty;
    public DocumentType DocumentType { get; set; }
    public string FormType { get; set; } = string.Empty; // "CarrierRegistration", "DriverRegistration", "VehicleRegistration"
    public Dictionary<string, object> ExistingFormData { get; set; } = new();
    public bool OverwriteExistingData { get; set; } = false;
    public decimal MinimumConfidenceThreshold { get; set; } = 0.6m;
}

public class AutoFillFromOcrResponse
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public Dictionary<string, object> AutoFilledData { get; set; } = new();
    public Dictionary<string, FieldAutoFillResult> FieldResults { get; set; } = new();
    public decimal OverallConfidence { get; set; }
    public List<string> Warnings { get; set; } = new();
    public List<string> RequiresManualReview { get; set; } = new();
    public DateTime ProcessedAt { get; set; }
}

public class FieldAutoFillResult
{
    public string FieldName { get; set; } = string.Empty;
    public string? OriginalValue { get; set; }
    public string? ExtractedValue { get; set; }
    public string? FinalValue { get; set; }
    public bool WasAutoFilled { get; set; }
    public bool WasOverwritten { get; set; }
    public decimal Confidence { get; set; }
    public string Status { get; set; } = string.Empty; // "AutoFilled", "Skipped", "RequiresReview", "Error"
    public string? Reason { get; set; }
}
