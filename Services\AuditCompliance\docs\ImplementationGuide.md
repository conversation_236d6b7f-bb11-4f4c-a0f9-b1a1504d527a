# Enhanced Audit & Compliance Implementation Guide

## Prerequisites

### System Requirements
- .NET 8.0 or later
- PostgreSQL 14+ with JSONB support
- Redis 6.0+ (optional, for caching and SignalR backplane)
- Minimum 4GB RAM for development
- 16GB+ RAM for production environments

### Dependencies
- Entity Framework Core 8.0+
- MediatR for CQRS pattern
- FluentValidation for request validation
- SignalR for real-time updates
- Serilog for structured logging

## Installation Steps

### 1. Database Setup

#### Create Database Schema
```sql
-- Create database
CREATE DATABASE tli_audit_compliance;

-- Create user
CREATE USER audit_service WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE tli_audit_compliance TO audit_service;
```

#### Run Migrations
```bash
cd Services/AuditCompliance/AuditCompliance.Infrastructure
dotnet ef database update --startup-project ../AuditCompliance.API
```

### 2. Configuration

#### appsettings.json
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=tli_audit_compliance;Username=audit_service;Password=your_secure_password",
    "Redis": "localhost:6379"
  },
  "Jwt": {
    "Issuer": "TLI-Platform",
    "Audience": "TLI-Services",
    "Key": "your-256-bit-secret-key-here"
  },
  "FileStorage": {
    "BasePath": "/app/exports",
    "MaxFileSizeBytes": 104857600,
    "RetentionDays": 30
  },
  "Export": {
    "MaxRecordsPerExport": 1000000,
    "MaxConcurrentJobs": 5,
    "DefaultPageSize": 1000,
    "CompressionEnabled": true
  },
  "Serilog": {
    "MinimumLevel": "Information",
    "WriteTo": [
      {
        "Name": "Console"
      },
      {
        "Name": "File",
        "Args": {
          "path": "logs/audit-compliance-.txt",
          "rollingInterval": "Day"
        }
      }
    ]
  }
}
```

### 3. Service Registration

#### Program.cs Updates
```csharp
// Add to Program.cs
builder.Services.AddApplication();
builder.Services.AddInfrastructure(builder.Configuration);

// Add health checks
builder.Services.AddHealthChecks()
    .AddNpgSql(builder.Configuration.GetConnectionString("DefaultConnection"))
    .AddRedis(builder.Configuration.GetConnectionString("Redis"));
```

## Development Workflow

### 1. Adding New Export Formats

#### Create Format Handler
```csharp
public class CustomFormatExportHandler : IExportFormatHandler
{
    public ExportFormat SupportedFormat => ExportFormat.Custom;
    
    public async Task<Stream> GenerateExportAsync(
        IEnumerable<object> data, 
        ExportConfiguration config)
    {
        // Implementation here
    }
}
```

#### Register Handler
```csharp
// In DependencyInjection.cs
services.AddScoped<IExportFormatHandler, CustomFormatExportHandler>();
```

### 2. Creating Custom Report Templates

#### Template Structure
```json
{
  "templateName": "Custom Audit Report",
  "sections": [
    {
      "id": "summary",
      "name": "Executive Summary",
      "order": 1,
      "isRequired": true,
      "template": "{{#summary}}...{{/summary}}",
      "dataSources": ["AuditLogs", "ComplianceReports"]
    }
  ],
  "parameters": [
    {
      "id": "dateRange",
      "name": "reportPeriod",
      "type": "DateRange",
      "isRequired": true,
      "validation": {
        "maxDays": 365
      }
    }
  ]
}
```

### 3. Implementing Retention Policies

#### Policy Configuration
```csharp
var policy = new RetentionPolicyManagement(
    "GDPR User Data",
    "7-year retention for user audit data",
    new RetentionPolicy(
        TimeSpan.FromDays(2557), // 7 years
        autoDelete: false,
        requireApproval: true
    ),
    new List<ComplianceStandard> { ComplianceStandard.GDPR },
    new List<string> { "User", "AuditLog" },
    new List<string> { "UserManagement" },
    PolicyPriority.High,
    createdBy,
    "Policy Creator"
);
```

## Testing Strategy

### 1. Unit Tests

#### Domain Entity Tests
```csharp
[Fact]
public void ModuleRegistry_WithValidData_ShouldCreateSuccessfully()
{
    // Arrange
    var moduleName = "TestModule";
    var displayName = "Test Module";
    
    // Act
    var module = new ModuleRegistry(
        moduleName, displayName, "Description", "1.0.0",
        new List<string>(), new List<string>(), 
        new List<string>(), new Dictionary<string, object>(),
        Guid.NewGuid());
    
    // Assert
    module.ModuleName.Should().Be(moduleName);
    module.IsActive.Should().BeTrue();
}
```

#### Service Tests
```csharp
[Fact]
public async Task ExportValidationService_WithValidRequest_ShouldReturnNoErrors()
{
    // Arrange
    var request = CreateValidExportRequest();
    
    // Act
    var result = await _validationService.ValidateExportRequestAsync(request);
    
    // Assert
    result.Where(v => v.Type == "Error").Should().BeEmpty();
}
```

### 2. Integration Tests

#### API Tests
```csharp
[Fact]
public async Task CreateExport_WithValidRequest_ShouldReturnSuccess()
{
    // Arrange
    var request = CreateValidExportRequest();
    
    // Act
    var response = await _client.PostAsJsonAsync("/api/audit/export", request);
    
    // Assert
    response.StatusCode.Should().Be(HttpStatusCode.OK);
}
```

### 3. Performance Tests

#### Load Testing
```csharp
[Fact]
public async Task BulkExport_With100Requests_ShouldCompleteWithinTimeout()
{
    // Arrange
    var requests = CreateBulkExportRequests(100);
    
    // Act
    var stopwatch = Stopwatch.StartNew();
    var response = await _client.PostAsJsonAsync("/api/audit/export/bulk", requests);
    stopwatch.Stop();
    
    // Assert
    response.StatusCode.Should().Be(HttpStatusCode.OK);
    stopwatch.ElapsedMilliseconds.Should().BeLessThan(30000); // 30 seconds
}
```

## Deployment Guide

### 1. Docker Configuration

#### Dockerfile
```dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["Services/AuditCompliance/AuditCompliance.API/AuditCompliance.API.csproj", "Services/AuditCompliance/AuditCompliance.API/"]
RUN dotnet restore "Services/AuditCompliance/AuditCompliance.API/AuditCompliance.API.csproj"
COPY . .
WORKDIR "/src/Services/AuditCompliance/AuditCompliance.API"
RUN dotnet build "AuditCompliance.API.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "AuditCompliance.API.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "AuditCompliance.API.dll"]
```

#### docker-compose.yml
```yaml
version: '3.8'
services:
  audit-compliance-api:
    build:
      context: .
      dockerfile: Services/AuditCompliance/AuditCompliance.API/Dockerfile
    ports:
      - "5001:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=tli_audit_compliance;Username=audit_service;Password=secure_password
      - ConnectionStrings__Redis=redis:6379
    depends_on:
      - postgres
      - redis
    volumes:
      - export-files:/app/exports

  postgres:
    image: postgres:14
    environment:
      POSTGRES_DB: tli_audit_compliance
      POSTGRES_USER: audit_service
      POSTGRES_PASSWORD: secure_password
    volumes:
      - postgres-data:/var/lib/postgresql/data

  redis:
    image: redis:6-alpine
    volumes:
      - redis-data:/data

volumes:
  postgres-data:
  redis-data:
  export-files:
```

### 2. Kubernetes Deployment

#### deployment.yaml
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: audit-compliance-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: audit-compliance-api
  template:
    metadata:
      labels:
        app: audit-compliance-api
    spec:
      containers:
      - name: api
        image: tli/audit-compliance-api:latest
        ports:
        - containerPort: 80
        env:
        - name: ConnectionStrings__DefaultConnection
          valueFrom:
            secretKeyRef:
              name: database-secret
              key: connection-string
        - name: ConnectionStrings__Redis
          value: "redis-service:6379"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        volumeMounts:
        - name: export-storage
          mountPath: /app/exports
      volumes:
      - name: export-storage
        persistentVolumeClaim:
          claimName: export-pvc
```

## Monitoring & Observability

### 1. Health Checks

#### Custom Health Checks
```csharp
public class ModuleRegistryHealthCheck : IHealthCheck
{
    private readonly IModuleRegistryService _moduleService;
    
    public async Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var modules = await _moduleService.GetActiveModulesAsync();
            var unhealthyModules = modules.Where(m => !m.IsHealthy).ToList();
            
            if (unhealthyModules.Any())
            {
                return HealthCheckResult.Degraded(
                    $"Unhealthy modules: {string.Join(", ", unhealthyModules.Select(m => m.ModuleName))}");
            }
            
            return HealthCheckResult.Healthy($"All {modules.Count} modules are healthy");
        }
        catch (Exception ex)
        {
            return HealthCheckResult.Unhealthy("Module registry check failed", ex);
        }
    }
}
```

### 2. Metrics Collection

#### Custom Metrics
```csharp
public class ExportMetrics
{
    private readonly IMetricsLogger _metrics;
    
    public void RecordExportStarted(string format, int recordCount)
    {
        _metrics.Counter("exports_started_total")
            .WithTag("format", format)
            .WithTag("size_category", GetSizeCategory(recordCount))
            .Increment();
    }
    
    public void RecordExportCompleted(string format, TimeSpan duration, bool success)
    {
        _metrics.Histogram("export_duration_seconds")
            .WithTag("format", format)
            .WithTag("success", success.ToString())
            .Record(duration.TotalSeconds);
    }
}
```

## Troubleshooting

### Common Issues

#### 1. Export Timeouts
**Problem**: Large exports timing out
**Solution**: 
- Increase timeout values in configuration
- Implement data chunking
- Use background processing

#### 2. Memory Issues
**Problem**: Out of memory during large exports
**Solution**:
- Implement streaming exports
- Reduce page sizes
- Add memory monitoring

#### 3. Permission Errors
**Problem**: Users cannot access certain modules
**Solution**:
- Check module access control settings
- Verify JWT token claims
- Review role assignments

### Performance Optimization

#### Database Optimization
```sql
-- Add indexes for common queries
CREATE INDEX CONCURRENTLY idx_auditlogs_timestamp_module 
ON audit_logs (event_timestamp, module_name) 
WHERE is_active = true;

-- Partition large tables
CREATE TABLE audit_logs_2024 PARTITION OF audit_logs 
FOR VALUES FROM ('2024-01-01') TO ('2025-01-01');
```

#### Caching Strategy
```csharp
// Cache frequently accessed data
public async Task<List<ModuleInfoDto>> GetActiveModulesAsync()
{
    return await _cache.GetOrCreateAsync("active_modules", async entry =>
    {
        entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(5);
        return await _repository.GetActiveModulesAsync();
    });
}
```

## Security Best Practices

### 1. Input Validation
- Validate all user inputs
- Sanitize file names and paths
- Implement rate limiting
- Use parameterized queries

### 2. Access Control
- Implement principle of least privilege
- Regular access reviews
- Audit all permission changes
- Use strong authentication

### 3. Data Protection
- Encrypt sensitive data at rest
- Use HTTPS for all communications
- Implement data masking
- Regular security assessments

## Maintenance Procedures

### 1. Regular Tasks
- Database maintenance and optimization
- Log file rotation and cleanup
- Export file cleanup
- Security updates

### 2. Monitoring
- Performance metrics review
- Error rate analysis
- Resource usage monitoring
- User activity analysis

### 3. Backup & Recovery
- Regular database backups
- Export file backups
- Configuration backups
- Disaster recovery testing
