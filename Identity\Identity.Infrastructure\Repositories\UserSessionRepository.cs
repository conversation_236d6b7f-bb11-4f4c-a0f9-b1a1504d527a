using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Identity.Domain.Entities;
using Identity.Domain.Repositories;
using Identity.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;

namespace Identity.Infrastructure.Repositories
{
    public class UserSessionRepository : IUserSessionRepository
    {
        private readonly Identity.Infrastructure.Persistence.IdentityDbContext _context;

        public UserSessionRepository(Identity.Infrastructure.Persistence.IdentityDbContext context)
        {
            _context = context;
        }

        public async Task<UserSession> GetByIdAsync(Guid id)
        {
            return await _context.UserSessions.FindAsync(id);
        }

        public async Task<UserSession> GetByTokenAsync(string token)
        {
            return await _context.UserSessions
                .FirstOrDefaultAsync(us => us.Token == token);
        }

        public async Task<IEnumerable<UserSession>> GetByUserIdAsync(Guid userId)
        {
            return await _context.UserSessions
                .Where(us => us.UserId == userId)
                .ToListAsync();
        }

        public async Task<IEnumerable<UserSession>> GetActiveByUserIdAsync(Guid userId)
        {
            return await _context.UserSessions
                .Where(us => us.UserId == userId && us.IsActive && us.ExpiresAt > DateTime.UtcNow)
                .ToListAsync();
        }

        public async Task AddAsync(UserSession session)
        {
            await _context.UserSessions.AddAsync(session);
            await _context.SaveChangesAsync();
        }

        public async Task UpdateAsync(UserSession session)
        {
            _context.UserSessions.Update(session);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteAsync(UserSession session)
        {
            _context.UserSessions.Remove(session);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteExpiredSessionsAsync()
        {
            var expiredSessions = await _context.UserSessions
                .Where(us => us.ExpiresAt < DateTime.UtcNow)
                .ToListAsync();

            if (expiredSessions.Any())
            {
                _context.UserSessions.RemoveRange(expiredSessions);
                await _context.SaveChangesAsync();
            }
        }
    }
}
