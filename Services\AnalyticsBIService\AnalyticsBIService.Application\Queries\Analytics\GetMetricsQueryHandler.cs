using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Domain.Enums;
using AnalyticsBIService.Domain.Repositories;
using AutoMapper;
using MediatR;
using Shared.Infrastructure.Repositories;
using Microsoft.Extensions.Logging;

namespace AnalyticsBIService.Application.Queries.Analytics;

/// <summary>
/// Handler for metrics queries
/// </summary>
public class GetMetricsQueryHandler :
    IRequestHandler<GetMetricsQuery, PagedResult<MetricDto>>,
    IRequestHandler<GetMetricByIdQuery, MetricDto?>,
    IRequestHandler<GetMetricTimeSeriesQuery, MetricTimeSeriesDto>,
    IRequestHandler<GetKPIPerformanceSummaryQuery, List<KPIPerformanceDto>>,
    IRequestHandler<GetRealTimeMetricsQuery, Dictionary<string, RealTimeMetricDto>>,
    IRequestHandler<GetMetricTrendsQuery, Dictionary<string, MetricTrendDto>>,
    IRequestHandler<CompareMetricsQuery, MetricComparisonDto>,
    IRequestHandler<GetUnderperformingKPIsQuery, List<UnderperformingKPIDto>>
{
    private readonly IMetricRepository _metricRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetMetricsQueryHandler> _logger;

    public GetMetricsQueryHandler(
        IMetricRepository metricRepository,
        IMapper mapper,
        ILogger<GetMetricsQueryHandler> logger)
    {
        _metricRepository = metricRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<PagedResult<MetricDto>> Handle(GetMetricsQuery request, CancellationToken cancellationToken)
    {
        // Get all metrics and apply filters in memory
        var allMetrics = await _metricRepository.GetPagedAsync(request.PageNumber, request.PageSize, cancellationToken: cancellationToken);
        var metrics = allMetrics.Items.AsEnumerable();

        // Apply filters
        if (!string.IsNullOrWhiteSpace(request.MetricName))
            metrics = metrics.Where(m => m.Name.Contains(request.MetricName));

        if (request.Category.HasValue)
            metrics = metrics.Where(m => m.Category == request.Category.Value);

        if (request.Type.HasValue)
            metrics = metrics.Where(m => m.Type == request.Type.Value);

        if (request.UserId.HasValue)
            metrics = metrics.Where(m => m.UserId == request.UserId.Value);

        if (request.UserType.HasValue)
            metrics = metrics.Where(m => m.UserType == request.UserType.Value);

        if (request.DataSource.HasValue)
            metrics = metrics.Where(m => m.DataSource == request.DataSource.Value);

        if (request.FromDate.HasValue)
            metrics = metrics.Where(m => m.CreatedAt >= request.FromDate.Value);

        if (request.ToDate.HasValue)
            metrics = metrics.Where(m => m.CreatedAt <= request.ToDate.Value);

        var filteredMetrics = metrics
            .OrderByDescending(m => m.CreatedAt)
            .ToList();

        var metricDtos = _mapper.Map<List<MetricDto>>(filteredMetrics);

        return new PagedResult<MetricDto>
        {
            Items = metricDtos,
            TotalCount = filteredMetrics.Count,
            PageNumber = request.PageNumber,
            PageSize = request.PageSize
        };
    }

    public async Task<MetricDto?> Handle(GetMetricByIdQuery request, CancellationToken cancellationToken)
    {
        var metric = await _metricRepository.GetByIdAsync(request.MetricId, cancellationToken);

        return metric != null ? _mapper.Map<MetricDto>(metric) : null;
    }

    public async Task<MetricTimeSeriesDto> Handle(GetMetricTimeSeriesQuery request, CancellationToken cancellationToken)
    {
        // Get metrics by date range and filter in memory
        var allMetrics = await _metricRepository.GetByDateRangeAsync(request.FromDate, request.ToDate, 1, int.MaxValue, cancellationToken);
        var metrics = allMetrics.Items
            .Where(m => m.Name == request.MetricName)
            .AsEnumerable();

        if (request.UserId.HasValue)
            metrics = metrics.Where(m => m.UserId == request.UserId.Value);

        if (request.UserType.HasValue)
            metrics = metrics.Where(m => m.UserType == request.UserType.Value);

        var orderedMetrics = metrics
            .OrderBy(m => m.CreatedAt)
            .ToList();

        if (!orderedMetrics.Any())
        {
            return new MetricTimeSeriesDto
            {
                MetricName = request.MetricName,
                FromDate = request.FromDate,
                ToDate = request.ToDate,
                Period = request.Period.ToString()
            };
        }

        var firstMetric = orderedMetrics.First();
        var dataPoints = orderedMetrics.Select(m => new MetricDataPointDto
        {
            Timestamp = m.Value.Timestamp,
            Value = m.Value.Value,
            Metadata = m.Value.Metadata
        }).ToList();

        var values = orderedMetrics.Select(m => m.Value.Value).ToList();
        var summary = new MetricSummaryDto
        {
            Average = values.Average(),
            Minimum = values.Min(),
            Maximum = values.Max(),
            Sum = values.Sum(),
            Count = values.Count,
            StandardDeviation = CalculateStandardDeviation(values),
            TrendDirection = CalculateTrendDirection(values),
            GrowthRate = CalculateGrowthRate(values)
        };

        return new MetricTimeSeriesDto
        {
            MetricName = request.MetricName,
            Category = firstMetric.Category.ToString(),
            Type = firstMetric.Type.ToString(),
            Unit = firstMetric.Value.Unit,
            FromDate = request.FromDate,
            ToDate = request.ToDate,
            Period = request.Period.ToString(),
            DataPoints = dataPoints,
            Summary = summary
        };
    }

    public async Task<List<KPIPerformanceDto>> Handle(GetKPIPerformanceSummaryQuery request, CancellationToken cancellationToken)
    {
        // Get all metrics and filter in memory
        var allMetrics = await _metricRepository.GetAllAsync(1, int.MaxValue, cancellationToken);
        var metrics = allMetrics.Items
            .Where(m => m.Target != null)
            .AsEnumerable();

        if (request.Category.HasValue)
            metrics = metrics.Where(m => m.Category == request.Category.Value);

        if (request.UserId.HasValue)
            metrics = metrics.Where(m => m.UserId == request.UserId.Value);

        if (request.UserType.HasValue)
            metrics = metrics.Where(m => m.UserType == request.UserType.Value);

        if (request.PerformanceStatus.HasValue)
        {
            // This would need to be implemented with a computed column or view
            // For now, we'll filter in memory after loading
        }

        // Get the latest metric for each name/user combination
        var latestMetrics = metrics
            .GroupBy(m => new { m.Name, m.UserId })
            .Select(g => g.OrderByDescending(m => m.CreatedAt).First())
            .ToList();

        var kpiPerformance = latestMetrics.Select(m => new KPIPerformanceDto
        {
            MetricName = m.Name,
            Category = m.Category.ToString(),
            UserType = m.UserType?.ToString(),
            CurrentValue = m.Value.Value,
            TargetValue = m.Target?.TargetValue,
            Unit = m.Value.Unit,
            IsHigherBetter = m.Target?.IsHigherBetter,
            PerformanceStatus = m.GetPerformanceStatus().ToString(),
            TargetAchievementPercentage = m.GetTargetAchievementPercentage(),
            AlertSeverity = m.GetAlertSeverity().ToString(),
            LastUpdated = m.CreatedAt
        }).ToList();

        // Apply performance status filter if specified
        if (request.PerformanceStatus.HasValue)
        {
            kpiPerformance = kpiPerformance
                .Where(k => k.PerformanceStatus == request.PerformanceStatus.Value.ToString())
                .ToList();
        }

        return kpiPerformance.OrderBy(k => k.PerformanceStatus).ThenBy(k => k.MetricName).ToList();
    }

    public async Task<Dictionary<string, RealTimeMetricDto>> Handle(GetRealTimeMetricsQuery request, CancellationToken cancellationToken)
    {
        var result = new Dictionary<string, RealTimeMetricDto>();

        foreach (var metricName in request.MetricNames)
        {
            // Get all metrics and filter in memory
            var allMetrics = await _metricRepository.GetPagedAsync(1, int.MaxValue, cancellationToken: cancellationToken);
            var metrics = allMetrics.Items
                .Where(m => m.Name == metricName)
                .AsEnumerable();

            if (request.UserId.HasValue)
                metrics = metrics.Where(m => m.UserId == request.UserId.Value);

            if (request.UserType.HasValue)
                metrics = metrics.Where(m => m.UserType == request.UserType.Value);

            var latestMetrics = metrics
                .OrderByDescending(m => m.CreatedAt)
                .Take(2)
                .ToList();

            if (latestMetrics.Any())
            {
                var current = latestMetrics.First();
                var previous = latestMetrics.Count > 1 ? latestMetrics[1] : null;

                var changePercentage = previous != null && previous.Value.Value != 0
                    ? ((current.Value.Value - previous.Value.Value) / previous.Value.Value) * 100
                    : (decimal?)null;

                result[metricName] = new RealTimeMetricDto
                {
                    MetricName = metricName,
                    CurrentValue = current.Value.Value,
                    Unit = current.Value.Unit,
                    LastUpdated = current.CreatedAt,
                    PreviousValue = previous?.Value.Value,
                    ChangePercentage = changePercentage,
                    TrendDirection = changePercentage.HasValue
                        ? (changePercentage > 0 ? TrendDirection.Increasing.ToString() :
                           changePercentage < 0 ? TrendDirection.Decreasing.ToString() :
                           TrendDirection.Stable.ToString())
                        : TrendDirection.Stable.ToString(),
                    PerformanceStatus = current.GetPerformanceStatus().ToString()
                };
            }
        }

        return result;
    }

    public async Task<Dictionary<string, MetricTrendDto>> Handle(GetMetricTrendsQuery request, CancellationToken cancellationToken)
    {
        var result = new Dictionary<string, MetricTrendDto>();

        foreach (var metricName in request.MetricNames)
        {
            // Get metrics by date range and filter in memory
            var allMetrics = await _metricRepository.GetByDateRangeAsync(request.FromDate, request.ToDate, 1, int.MaxValue, cancellationToken);
            var metrics = allMetrics.Items
                .Where(m => m.Name == metricName)
                .AsEnumerable();

            if (request.UserId.HasValue)
                metrics = metrics.Where(m => m.UserId == request.UserId.Value);

            var orderedMetrics = metrics
                .OrderBy(m => m.CreatedAt)
                .ToList();

            if (orderedMetrics.Count >= 2)
            {
                var values = orderedMetrics.Select(m => m.Value.Value).ToList();
                var firstValue = values.First();
                var lastValue = values.Last();

                var growthRate = firstValue != 0 ? (lastValue - firstValue) / firstValue : 0;
                var changePercentage = firstValue != 0 ? ((lastValue - firstValue) / firstValue) * 100 : 0;

                result[metricName] = new MetricTrendDto
                {
                    MetricName = metricName,
                    TrendDirection = CalculateTrendDirection(values),
                    GrowthRate = growthRate,
                    ChangePercentage = changePercentage,
                    CurrentValue = lastValue,
                    PreviousValue = firstValue,
                    Unit = orderedMetrics.First().Value.Unit,
                    AnalysisPeriodStart = request.FromDate,
                    AnalysisPeriodEnd = request.ToDate
                };
            }
        }

        return result;
    }

    public async Task<MetricComparisonDto> Handle(CompareMetricsQuery request, CancellationToken cancellationToken)
    {
        var userComparisons = new List<UserMetricComparisonDto>();

        foreach (var userId in request.UserIds)
        {
            // Get metrics by date range and filter in memory
            var allMetrics = await _metricRepository.GetByDateRangeAsync(request.FromDate, request.ToDate, 1, 10000, cancellationToken);
            var metrics = allMetrics.Items
                .Where(m => m.Name == request.MetricName && m.UserId == userId)
                .OrderBy(m => m.CreatedAt)
                .ToList();

            if (metrics.Any())
            {
                var values = metrics.Select(m => m.Value.Value).ToList();
                var dataPoints = metrics.Select(m => new MetricDataPointDto
                {
                    Timestamp = m.Value.Timestamp,
                    Value = m.Value.Value
                }).ToList();

                userComparisons.Add(new UserMetricComparisonDto
                {
                    UserId = userId,
                    UserType = metrics.First().UserType?.ToString(),
                    AverageValue = values.Average(),
                    TotalValue = values.Sum(),
                    DataPointCount = values.Count,
                    DataPoints = dataPoints
                });
            }
        }

        // Calculate performance ranks
        var sortedComparisons = userComparisons.OrderByDescending(u => u.AverageValue).ToList();
        for (int i = 0; i < sortedComparisons.Count; i++)
        {
            sortedComparisons[i].PerformanceRank = $"#{i + 1}";
        }

        // Calculate overall summary
        var allValues = userComparisons.SelectMany(u => u.DataPoints.Select(d => d.Value)).ToList();
        var overallSummary = new MetricSummaryDto();
        if (allValues.Any())
        {
            overallSummary.Average = allValues.Average();
            overallSummary.Minimum = allValues.Min();
            overallSummary.Maximum = allValues.Max();
            overallSummary.Sum = allValues.Sum();
            overallSummary.Count = allValues.Count;
            overallSummary.StandardDeviation = CalculateStandardDeviation(allValues);
            overallSummary.TrendDirection = CalculateTrendDirection(allValues);
        }

        return new MetricComparisonDto
        {
            MetricName = request.MetricName,
            Unit = userComparisons.FirstOrDefault()?.DataPoints.FirstOrDefault()?.Value.ToString() ?? "",
            FromDate = request.FromDate,
            ToDate = request.ToDate,
            Period = request.Period.ToString(),
            UserComparisons = userComparisons,
            OverallSummary = overallSummary
        };
    }

    public async Task<List<UnderperformingKPIDto>> Handle(GetUnderperformingKPIsQuery request, CancellationToken cancellationToken)
    {
        // Get all metrics and filter in memory
        var allMetrics = await _metricRepository.GetByDateRangeAsync(DateTime.MinValue, DateTime.MaxValue, 1, int.MaxValue, cancellationToken);
        var metrics = allMetrics.Items
            .Where(m => m.Target != null)
            .AsEnumerable();

        if (request.UserId.HasValue)
            metrics = metrics.Where(m => m.UserId == request.UserId.Value);

        if (request.UserType.HasValue)
            metrics = metrics.Where(m => m.UserType == request.UserType.Value);

        if (request.Category.HasValue)
            metrics = metrics.Where(m => m.Category == request.Category.Value);

        // Get latest metrics
        var latestMetrics = metrics
            .GroupBy(m => new { m.Name, m.UserId })
            .Select(g => g.OrderByDescending(m => m.CreatedAt).First())
            .ToList();

        var underperformingKPIs = latestMetrics
            .Where(m => m.GetPerformanceStatus() == PerformanceStatus.BelowAverage ||
                       m.GetPerformanceStatus() == PerformanceStatus.Poor)
            .Select(m => new UnderperformingKPIDto
            {
                MetricName = m.Name,
                Category = m.Category.ToString(),
                CurrentValue = m.Value.Value,
                TargetValue = m.Target!.TargetValue,
                Unit = m.Value.Unit,
                PerformanceGap = m.Target.TargetValue - m.Value.Value,
                PerformanceGapPercentage = m.Target.TargetValue != 0
                    ? ((m.Target.TargetValue - m.Value.Value) / m.Target.TargetValue) * 100
                    : 0,
                PerformanceStatus = m.GetPerformanceStatus().ToString(),
                AlertSeverity = m.GetAlertSeverity().ToString(),
                LastUpdated = m.CreatedAt,
                RecommendedAction = GenerateRecommendedAction(m)
            })
            .OrderByDescending(k => k.PerformanceGapPercentage)
            .Take(request.MaxResults)
            .ToList();

        return underperformingKPIs;
    }

    private static decimal CalculateStandardDeviation(List<decimal> values)
    {
        if (values.Count <= 1) return 0;

        var average = values.Average();
        var sumOfSquares = values.Sum(v => (double)Math.Pow((double)(v - average), 2));
        return (decimal)Math.Sqrt(sumOfSquares / (values.Count - 1));
    }

    private static string CalculateTrendDirection(List<decimal> values)
    {
        if (values.Count < 2) return TrendDirection.Stable.ToString();

        var firstHalf = values.Take(values.Count / 2).Average();
        var secondHalf = values.Skip(values.Count / 2).Average();

        var difference = secondHalf - firstHalf;
        var threshold = firstHalf * 0.05m; // 5% threshold

        return Math.Abs(difference) < threshold ? TrendDirection.Stable.ToString() :
               difference > 0 ? TrendDirection.Increasing.ToString() :
               TrendDirection.Decreasing.ToString();
    }

    private static decimal? CalculateGrowthRate(List<decimal> values)
    {
        if (values.Count < 2 || values.First() == 0) return null;

        var firstValue = values.First();
        var lastValue = values.Last();
        return (lastValue - firstValue) / firstValue;
    }

    private static string GenerateRecommendedAction(Domain.Entities.Metric metric)
    {
        var performanceStatus = metric.GetPerformanceStatus();
        var category = metric.Category;

        return (category, performanceStatus) switch
        {
            (KPICategory.Financial, PerformanceStatus.Poor) => "Review financial processes and cost optimization strategies",
            (KPICategory.Operational, PerformanceStatus.Poor) => "Analyze operational bottlenecks and process improvements",
            (KPICategory.Customer, PerformanceStatus.Poor) => "Focus on customer satisfaction and service quality improvements",
            (KPICategory.Quality, PerformanceStatus.Poor) => "Implement quality control measures and training programs",
            (KPICategory.Efficiency, PerformanceStatus.Poor) => "Optimize workflows and resource allocation",
            _ => "Monitor closely and consider improvement initiatives"
        };
    }
}
