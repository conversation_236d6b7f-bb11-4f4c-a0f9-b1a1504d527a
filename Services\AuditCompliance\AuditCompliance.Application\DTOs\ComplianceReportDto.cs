using AuditCompliance.Domain.Enums;

namespace AuditCompliance.Application.DTOs;

public class ComplianceReportDto
{
    public Guid Id { get; set; }
    public string ReportNumber { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public ComplianceStandard Standard { get; set; }
    public ComplianceStatus Status { get; set; }
    public DateTime ReportPeriodStart { get; set; }
    public DateTime ReportPeriodEnd { get; set; }
    public Guid GeneratedBy { get; set; }
    public string GeneratedByName { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string? FilePath { get; set; }
    public bool IsAutomated { get; set; }
    public string? Findings { get; set; }
    public string? Recommendations { get; set; }
    public int TotalViolations { get; set; }
    public int CriticalViolations { get; set; }
    public int HighViolations { get; set; }
    public int MediumViolations { get; set; }
    public int LowViolations { get; set; }
    public List<ComplianceReportItemDto> Items { get; set; } = new();
}

public class ComplianceReportItemDto
{
    public Guid Id { get; set; }
    public string CheckName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public bool IsViolation { get; set; }
    public AuditSeverity Severity { get; set; }
    public string? Details { get; set; }
    public string? Recommendation { get; set; }
    public string? Evidence { get; set; }
    public DateTime CheckedAt { get; set; }
}

public class CreateComplianceReportDto
{
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public ComplianceStandard Standard { get; set; }
    public DateTime ReportPeriodStart { get; set; }
    public DateTime ReportPeriodEnd { get; set; }
    public bool IsAutomated { get; set; } = false;
}

public class ComplianceReportQueryDto
{
    public ComplianceStandard? Standard { get; set; }
    public ComplianceStatus? Status { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public Guid? GeneratedBy { get; set; }
    public bool? IsAutomated { get; set; }
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 20;
}

public class ComplianceReportResultDto
{
    public List<ComplianceReportDto> Reports { get; set; } = new();
    public int TotalCount { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public int TotalPages { get; set; }
    public bool HasNextPage { get; set; }
    public bool HasPreviousPage { get; set; }
}

public class ComplianceSummaryDto
{
    public ComplianceStandard Standard { get; set; }
    public ComplianceStatus OverallStatus { get; set; }
    public int TotalReports { get; set; }
    public int CompliantReports { get; set; }
    public int NonCompliantReports { get; set; }
    public int PendingReports { get; set; }
    public DateTime LastReportDate { get; set; }
    public List<ComplianceViolationSummaryDto> ViolationSummary { get; set; } = new();
}

public class ComplianceViolationSummaryDto
{
    public AuditSeverity Severity { get; set; }
    public int Count { get; set; }
    public decimal Percentage { get; set; }
}
