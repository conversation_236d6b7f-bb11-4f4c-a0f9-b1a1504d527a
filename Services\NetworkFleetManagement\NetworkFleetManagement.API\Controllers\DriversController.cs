using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NetworkFleetManagement.Application.Commands.Drivers;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Application.Queries.Drivers;
using NetworkFleetManagement.Domain.Enums;

namespace NetworkFleetManagement.API.Controllers;

[ApiController]
[Route("api/v1/[controller]")]
[Authorize]
public class DriversController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<DriversController> _logger;

    public DriversController(IMediator mediator, ILogger<DriversController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get all drivers with pagination and filtering
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<PagedResult<DriverSummaryDto>>> GetDrivers(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] Guid? carrierId = null,
        [FromQuery] DriverStatus? status = null,
        [FromQuery] string? searchTerm = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetDriversQuery(pageNumber, pageSize, carrierId, status, searchTerm);
            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving drivers");
            return StatusCode(500, "An error occurred while retrieving drivers");
        }
    }

    /// <summary>
    /// Get driver by ID
    /// </summary>
    [HttpGet("{id:guid}")]
    public async Task<ActionResult<DriverDto>> GetDriver(Guid id, CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetDriverByIdQuery(id);
            var result = await _mediator.Send(query, cancellationToken);

            if (result == null)
                return NotFound($"Driver with ID {id} not found");

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving driver {DriverId}", id);
            return StatusCode(500, "An error occurred while retrieving the driver");
        }
    }

    /// <summary>
    /// Create a new driver
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<DriverDto>> CreateDriver(
        [FromBody] CreateDriverDto createDriverDto,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var command = new CreateDriverCommand(createDriverDto);
            var result = await _mediator.Send(command, cancellationToken);

            return CreatedAtAction(
                nameof(GetDriver),
                new { id = result.Id },
                result);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while creating driver");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating driver");
            return StatusCode(500, "An error occurred while creating the driver");
        }
    }

    /// <summary>
    /// Update driver information
    /// </summary>
    [HttpPut("{id:guid}")]
    public async Task<ActionResult<DriverDto>> UpdateDriver(
        Guid id,
        [FromBody] UpdateDriverDto updateDriverDto,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var command = new UpdateDriverCommand(id, updateDriverDto);
            var result = await _mediator.Send(command, cancellationToken);

            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while updating driver");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating driver {DriverId}", id);
            return StatusCode(500, "An error occurred while updating the driver");
        }
    }

    /// <summary>
    /// Update driver status
    /// </summary>
    [HttpPut("{id:guid}/status")]
    public async Task<ActionResult<DriverDto>> UpdateDriverStatus(
        Guid id,
        [FromBody] UpdateDriverStatusDto statusDto,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var command = new UpdateDriverStatusCommand(id, statusDto);
            var result = await _mediator.Send(command, cancellationToken);

            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while updating driver status");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating driver status for {DriverId}", id);
            return StatusCode(500, "An error occurred while updating the driver status");
        }
    }

    /// <summary>
    /// Update driver onboarding status
    /// </summary>
    [HttpPut("{id:guid}/onboarding-status")]
    public async Task<ActionResult<DriverDto>> UpdateDriverOnboardingStatus(
        Guid id,
        [FromBody] UpdateDriverOnboardingStatusDto statusDto,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var command = new UpdateDriverOnboardingStatusCommand(id, statusDto);
            var result = await _mediator.Send(command, cancellationToken);

            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while updating driver onboarding status");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating driver onboarding status for {DriverId}", id);
            return StatusCode(500, "An error occurred while updating the driver onboarding status");
        }
    }

    /// <summary>
    /// Update driver location
    /// </summary>
    [HttpPut("{id:guid}/location")]
    public async Task<ActionResult<DriverDto>> UpdateDriverLocation(
        Guid id,
        [FromBody] UpdateDriverLocationDto locationDto,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var command = new UpdateDriverLocationCommand(id, locationDto);
            var result = await _mediator.Send(command, cancellationToken);

            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while updating driver location");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating driver location for {DriverId}", id);
            return StatusCode(500, "An error occurred while updating the driver location");
        }
    }

    /// <summary>
    /// Assign driver to vehicle
    /// </summary>
    [HttpPost("{driverId:guid}/assign-vehicle/{vehicleId:guid}")]
    public async Task<ActionResult<DriverVehicleAssignmentDto>> AssignDriverToVehicle(
        Guid driverId,
        Guid vehicleId,
        [FromBody] AssignDriverToVehicleDto assignmentDto,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var command = new AssignDriverToVehicleCommand(driverId, vehicleId, assignmentDto);
            var result = await _mediator.Send(command, cancellationToken);

            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while assigning driver to vehicle");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning driver {DriverId} to vehicle {VehicleId}", driverId, vehicleId);
            return StatusCode(500, "An error occurred while assigning driver to vehicle");
        }
    }

    /// <summary>
    /// Unassign driver from vehicle
    /// </summary>
    [HttpPost("{driverId:guid}/unassign-vehicle/{vehicleId:guid}")]
    public async Task<ActionResult> UnassignDriverFromVehicle(
        Guid driverId,
        Guid vehicleId,
        [FromBody] UnassignDriverFromVehicleDto unassignmentDto,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var command = new UnassignDriverFromVehicleCommand(driverId, vehicleId, unassignmentDto);
            await _mediator.Send(command, cancellationToken);

            return Ok();
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while unassigning driver from vehicle");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error unassigning driver {DriverId} from vehicle {VehicleId}", driverId, vehicleId);
            return StatusCode(500, "An error occurred while unassigning driver from vehicle");
        }
    }

    /// <summary>
    /// Get drivers by carrier
    /// </summary>
    [HttpGet("by-carrier/{carrierId:guid}")]
    public async Task<ActionResult<IEnumerable<DriverSummaryDto>>> GetDriversByCarrier(
        Guid carrierId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetDriversByCarrierQuery(carrierId);
            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving drivers for carrier {CarrierId}", carrierId);
            return StatusCode(500, "An error occurred while retrieving drivers");
        }
    }

    /// <summary>
    /// Get drivers dashboard for carrier with assignment and performance information
    /// </summary>
    [HttpGet("dashboard/carrier/{carrierId:guid}")]
    [Authorize(Roles = "Admin,Carrier")]
    public async Task<ActionResult<PagedResult<DriverDashboardDto>>> GetDriversDashboardByCarrier(
        Guid carrierId,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] DriverStatus? status = null,
        [FromQuery] OnboardingStatus? onboardingStatus = null,
        [FromQuery] bool? isAssigned = null,
        [FromQuery] bool? hasExpiredLicense = null,
        [FromQuery] bool? isActive = null,
        [FromQuery] string? searchTerm = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetDriversWithAssignmentsQuery(
                carrierId, pageNumber, pageSize, status, onboardingStatus,
                isAssigned, hasExpiredLicense, isActive, searchTerm);
            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving drivers dashboard for carrier {CarrierId}", carrierId);
            return StatusCode(500, "An error occurred while retrieving drivers dashboard");
        }
    }

    /// <summary>
    /// Get available drivers
    /// </summary>
    [HttpGet("available")]
    public async Task<ActionResult<IEnumerable<DriverSummaryDto>>> GetAvailableDrivers(
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetAvailableDriversQuery();
            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving available drivers");
            return StatusCode(500, "An error occurred while retrieving available drivers");
        }
    }

    /// <summary>
    /// Get drivers by operational area
    /// </summary>
    [HttpGet("by-area/{area}")]
    public async Task<ActionResult<IEnumerable<DriverSummaryDto>>> GetDriversByOperationalArea(
        string area,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetDriversByOperationalAreaQuery(area);
            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving drivers for area {Area}", area);
            return StatusCode(500, "An error occurred while retrieving drivers");
        }
    }

    /// <summary>
    /// Get drivers requiring license renewal
    /// </summary>
    [HttpGet("license-renewal-required")]
    public async Task<ActionResult<IEnumerable<DriverSummaryDto>>> GetDriversRequiringLicenseRenewal(
        [FromQuery] int daysThreshold = 30,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetDriversRequiringLicenseRenewalQuery(daysThreshold);
            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving drivers requiring license renewal");
            return StatusCode(500, "An error occurred while retrieving drivers requiring license renewal");
        }
    }
}
