using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SubscriptionManagement.Application.Commands.SendManualNotification;
using SubscriptionManagement.Application.Commands.TriggerSubscriptionAlerts;
using SubscriptionManagement.Application.DTOs;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.API.Controllers
{
    [Authorize(Roles = "Admin")]
    [Route("api/admin/notifications")]
    public class AdminNotificationController : BaseController
    {
        /// <summary>
        /// Send manual notification for subscription (Admin only)
        /// </summary>
        [HttpPost("subscription")]
        [ProducesResponseType(typeof(SendManualNotificationResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> SendSubscriptionNotification([FromBody] SendSubscriptionNotificationRequest request)
        {
            try
            {
                var adminUserId = GetCurrentUserId();
                var command = new SendManualNotificationCommand
                {
                    SubscriptionId = request.SubscriptionId,
                    UserId = request.UserId,
                    NotificationType = request.NotificationType,
                    CustomMessage = request.CustomMessage,
                    Channels = request.Channels,
                    ScheduledAt = request.ScheduledAt,
                    TriggeredByUserId = adminUserId,
                    Language = request.Language ?? "en",
                    AdditionalVariables = request.AdditionalVariables ?? new Dictionary<string, string>()
                };

                var result = await Mediator.Send(command);

                if (result.Success)
                {
                    return Ok(result);
                }
                else
                {
                    return BadRequest(result);
                }
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Preview notification before sending (Admin only)
        /// </summary>
        [HttpPost("preview")]
        [ProducesResponseType(typeof(NotificationPreviewResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> PreviewNotification([FromBody] PreviewNotificationRequest request)
        {
            try
            {
                // TODO: Implement notification preview functionality
                // This would render the template without actually sending it

                var preview = new NotificationPreviewResponse
                {
                    Subject = "Preview: Subscription Notification",
                    Body = "This is a preview of the notification that would be sent.",
                    Channel = request.Channel,
                    Variables = request.Variables ?? new Dictionary<string, string>()
                };

                return Ok(preview);
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get available notification templates (Admin only)
        /// </summary>
        [HttpGet("templates")]
        [ProducesResponseType(typeof(List<NotificationTemplateDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetNotificationTemplates(
            [FromQuery] NotificationType? type = null,
            [FromQuery] string? channel = null,
            [FromQuery] string? language = null)
        {
            try
            {
                // TODO: Implement get templates functionality
                var templates = new List<NotificationTemplateDto>
                {
                    new NotificationTemplateDto
                    {
                        Id = Guid.NewGuid(),
                        Name = "Payment Proof Verified",
                        Type = NotificationType.PaymentProofVerified,
                        Channel = "Email",
                        Language = "en",
                        Subject = "Payment Verified - Subscription Activated",
                        IsActive = true
                    }
                };

                return Ok(templates);
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get notification history (Admin only)
        /// </summary>
        [HttpGet("history")]
        [ProducesResponseType(typeof(NotificationHistoryResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetNotificationHistory(
            [FromQuery] Guid? subscriptionId = null,
            [FromQuery] Guid? userId = null,
            [FromQuery] NotificationType? type = null,
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 20)
        {
            try
            {
                // TODO: Implement notification history functionality
                var history = new NotificationHistoryResponse
                {
                    Items = new List<NotificationHistoryDto>(),
                    TotalCount = 0,
                    PageNumber = pageNumber,
                    PageSize = pageSize,
                    TotalPages = 0
                };

                return Ok(history);
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Trigger subscription alerts for multiple subscriptions (Admin only)
        /// </summary>
        [HttpPost("subscription-alerts")]
        [ProducesResponseType(typeof(TriggerSubscriptionAlertsResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> TriggerSubscriptionAlerts([FromBody] TriggerSubscriptionAlertsRequest request)
        {
            try
            {
                var adminUserId = GetCurrentUserId();
                var command = new TriggerSubscriptionAlertsCommand
                {
                    SubscriptionStatuses = request.SubscriptionStatuses,
                    ExpiryDateFrom = request.ExpiryDateFrom,
                    ExpiryDateTo = request.ExpiryDateTo,
                    UserTypes = request.UserTypes,
                    PlanTypes = request.PlanTypes,
                    SubscriptionIds = request.SubscriptionIds,
                    AlertType = request.AlertType,
                    Channels = request.Channels,
                    CustomMessage = request.CustomMessage,
                    TriggeredByUserId = adminUserId,
                    Language = request.Language ?? "en",
                    DryRun = request.DryRun,
                    BatchSize = request.BatchSize > 0 ? request.BatchSize : 100
                };

                var result = await Mediator.Send(command);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }
    }

    // Request/Response DTOs
    public class SendSubscriptionNotificationRequest
    {
        public Guid? SubscriptionId { get; set; }
        public Guid? UserId { get; set; }
        public NotificationType NotificationType { get; set; }
        public string? CustomMessage { get; set; }
        public List<string> Channels { get; set; } = new();
        public DateTime? ScheduledAt { get; set; }
        public string? Language { get; set; }
        public Dictionary<string, string>? AdditionalVariables { get; set; }
    }

    public class PreviewNotificationRequest
    {
        public NotificationType NotificationType { get; set; }
        public string Channel { get; set; } = string.Empty;
        public string Language { get; set; } = "en";
        public Guid? SubscriptionId { get; set; }
        public Guid? UserId { get; set; }
        public Dictionary<string, string>? Variables { get; set; }
    }

    public class NotificationPreviewResponse
    {
        public string Subject { get; set; } = string.Empty;
        public string Body { get; set; } = string.Empty;
        public string Channel { get; set; } = string.Empty;
        public Dictionary<string, string> Variables { get; set; } = new();
    }

    public class NotificationTemplateDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public NotificationType Type { get; set; }
        public string Channel { get; set; } = string.Empty;
        public string Language { get; set; } = string.Empty;
        public string Subject { get; set; } = string.Empty;
        public bool IsActive { get; set; }
    }

    public class NotificationHistoryDto
    {
        public Guid Id { get; set; }
        public Guid? SubscriptionId { get; set; }
        public Guid? UserId { get; set; }
        public NotificationType Type { get; set; }
        public string Channel { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public DateTime SentAt { get; set; }
        public Guid TriggeredByUserId { get; set; }
    }

    public class NotificationHistoryResponse
    {
        public List<NotificationHistoryDto> Items { get; set; } = new();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
    }

    public class TriggerSubscriptionAlertsRequest
    {
        public List<SubscriptionStatus>? SubscriptionStatuses { get; set; }
        public DateTime? ExpiryDateFrom { get; set; }
        public DateTime? ExpiryDateTo { get; set; }
        public List<UserType>? UserTypes { get; set; }
        public List<PlanType>? PlanTypes { get; set; }
        public List<Guid>? SubscriptionIds { get; set; }
        public NotificationType AlertType { get; set; } = NotificationType.SubscriptionReminder;
        public List<string> Channels { get; set; } = new();
        public string? CustomMessage { get; set; }
        public string? Language { get; set; }
        public bool DryRun { get; set; } = false;
        public int BatchSize { get; set; } = 100;
    }
}
