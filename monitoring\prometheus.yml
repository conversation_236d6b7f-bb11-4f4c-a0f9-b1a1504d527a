global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # API Gateway
  - job_name: 'api-gateway'
    static_configs:
      - targets: ['host.docker.internal:5000']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # Identity Service
  - job_name: 'identity-service'
    static_configs:
      - targets: ['host.docker.internal:7000']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # User Management Service
  - job_name: 'user-management-service'
    static_configs:
      - targets: ['host.docker.internal:7001']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # Order Management Service
  - job_name: 'order-management-service'
    static_configs:
      - targets: ['host.docker.internal:7002']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # Subscription Management Service
  - job_name: 'subscription-management-service'
    static_configs:
      - targets: ['host.docker.internal:7003']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # Trip Management Service
  - job_name: 'trip-management-service'
    static_configs:
      - targets: ['host.docker.internal:7004']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # Network Fleet Management Service
  - job_name: 'network-fleet-management-service'
    static_configs:
      - targets: ['host.docker.internal:7005']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # Financial Payment Service
  - job_name: 'financial-payment-service'
    static_configs:
      - targets: ['host.docker.internal:7006']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # Communication Notification Service
  - job_name: 'communication-notification-service'
    static_configs:
      - targets: ['host.docker.internal:7007']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # Analytics BI Service
  - job_name: 'analytics-bi-service'
    static_configs:
      - targets: ['host.docker.internal:7008']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # Data Storage Service
  - job_name: 'data-storage-service'
    static_configs:
      - targets: ['host.docker.internal:7009']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # Monitoring Observability Service
  - job_name: 'monitoring-observability-service'
    static_configs:
      - targets: ['host.docker.internal:7010']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # Audit Compliance Service
  - job_name: 'audit-compliance-service'
    static_configs:
      - targets: ['host.docker.internal:7011']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # Mobile Workflow Service
  - job_name: 'mobile-workflow-service'
    static_configs:
      - targets: ['host.docker.internal:7012']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # Infrastructure monitoring
  - job_name: 'postgres-exporter'
    static_configs:
      - targets: ['host.docker.internal:9187']
    scrape_interval: 30s

  - job_name: 'redis-exporter'
    static_configs:
      - targets: ['host.docker.internal:9121']
    scrape_interval: 30s

  - job_name: 'rabbitmq-exporter'
    static_configs:
      - targets: ['host.docker.internal:15692']
    scrape_interval: 30s

  # Node exporter for system metrics
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['host.docker.internal:9100']
    scrape_interval: 30s

  # Docker metrics
  - job_name: 'docker'
    static_configs:
      - targets: ['host.docker.internal:9323']
    scrape_interval: 30s

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

# Load rules once and periodically evaluate them according to the global 'evaluation_interval'.
rule_files:
  - "alert_rules.yml"
