using AuditCompliance.Application.Commands;
using AuditCompliance.Application.Handlers;
using AuditCompliance.Application.Interfaces;
using AuditCompliance.Domain.Entities;
using AuditCompliance.Domain.Enums;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace AuditCompliance.Tests.Application.Handlers
{
    /// <summary>
    /// Unit tests for CreateCustomReportTemplateCommandHandler
    /// </summary>
    public class CreateCustomReportTemplateCommandHandlerTests
    {
        private readonly Mock<ICustomComplianceReportTemplateRepository> _repositoryMock;
        private readonly Mock<ILogger<CreateCustomReportTemplateCommandHandler>> _loggerMock;
        private readonly CreateCustomReportTemplateCommandHandler _handler;

        public CreateCustomReportTemplateCommandHandlerTests()
        {
            _repositoryMock = new Mock<ICustomComplianceReportTemplateRepository>();
            _loggerMock = new Mock<ILogger<CreateCustomReportTemplateCommandHandler>>();
            _handler = new CreateCustomReportTemplateCommandHandler(_repositoryMock.Object, _loggerMock.Object);
        }

        [Fact]
        public async Task Handle_WithValidCommand_ShouldCreateTemplateAndReturnDto()
        {
            // Arrange
            var command = CreateValidCommand();
            
            _repositoryMock
                .Setup(x => x.TemplateNameExistsAsync(command.TemplateName, null))
                .ReturnsAsync(false);

            _repositoryMock
                .Setup(x => x.AddAsync(It.IsAny<CustomComplianceReportTemplate>()))
                .ReturnsAsync((CustomComplianceReportTemplate template) => template);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.TemplateName.Should().Be(command.TemplateName);
            result.Description.Should().Be(command.Description);
            result.ComplianceStandard.Should().Be(command.ComplianceStandard);
            result.TemplateType.Should().Be(command.TemplateType);
            result.TemplateContent.Should().Be(command.TemplateContent);
            result.CreatedBy.Should().Be(command.CreatedBy);
            result.CreatedByName.Should().Be(command.CreatedByName);
            result.IsActive.Should().BeTrue();
            result.Version.Should().Be("1.0");

            _repositoryMock.Verify(x => x.AddAsync(It.IsAny<CustomComplianceReportTemplate>()), Times.Once);
        }

        [Fact]
        public async Task Handle_WithDuplicateTemplateName_ShouldThrowInvalidOperationException()
        {
            // Arrange
            var command = CreateValidCommand();
            
            _repositoryMock
                .Setup(x => x.TemplateNameExistsAsync(command.TemplateName, null))
                .ReturnsAsync(true);

            // Act & Assert
            var action = async () => await _handler.Handle(command, CancellationToken.None);
            
            await action.Should().ThrowAsync<InvalidOperationException>()
                .WithMessage($"Template with name '{command.TemplateName}' already exists");

            _repositoryMock.Verify(x => x.AddAsync(It.IsAny<CustomComplianceReportTemplate>()), Times.Never);
        }

        [Theory]
        [InlineData("")]
        [InlineData(null)]
        [InlineData("   ")]
        public async Task Handle_WithInvalidTemplateName_ShouldThrowArgumentException(string invalidTemplateName)
        {
            // Arrange
            var command = CreateValidCommand();
            command.TemplateName = invalidTemplateName;

            // Act & Assert
            var action = async () => await _handler.Handle(command, CancellationToken.None);
            
            await action.Should().ThrowAsync<ArgumentException>()
                .WithMessage("Template name cannot be empty*");
        }

        [Theory]
        [InlineData("")]
        [InlineData(null)]
        [InlineData("   ")]
        public async Task Handle_WithInvalidTemplateContent_ShouldThrowArgumentException(string invalidContent)
        {
            // Arrange
            var command = CreateValidCommand();
            command.TemplateContent = invalidContent;

            // Act & Assert
            var action = async () => await _handler.Handle(command, CancellationToken.None);
            
            await action.Should().ThrowAsync<ArgumentException>()
                .WithMessage("Template content cannot be empty*");
        }

        [Fact]
        public async Task Handle_WithEmptyCreatedBy_ShouldThrowArgumentException()
        {
            // Arrange
            var command = CreateValidCommand();
            command.CreatedBy = Guid.Empty;

            // Act & Assert
            var action = async () => await _handler.Handle(command, CancellationToken.None);
            
            await action.Should().ThrowAsync<ArgumentException>()
                .WithMessage("Created by user ID cannot be empty*");
        }

        [Theory]
        [InlineData("")]
        [InlineData(null)]
        [InlineData("   ")]
        public async Task Handle_WithInvalidCreatedByName_ShouldThrowArgumentException(string invalidCreatedByName)
        {
            // Arrange
            var command = CreateValidCommand();
            command.CreatedByName = invalidCreatedByName;

            // Act & Assert
            var action = async () => await _handler.Handle(command, CancellationToken.None);
            
            await action.Should().ThrowAsync<ArgumentException>()
                .WithMessage("Created by name cannot be empty*");
        }

        [Fact]
        public async Task Handle_WithRepositoryException_ShouldPropagateException()
        {
            // Arrange
            var command = CreateValidCommand();
            var expectedException = new InvalidOperationException("Database error");
            
            _repositoryMock
                .Setup(x => x.TemplateNameExistsAsync(command.TemplateName, null))
                .ReturnsAsync(false);

            _repositoryMock
                .Setup(x => x.AddAsync(It.IsAny<CustomComplianceReportTemplate>()))
                .ThrowsAsync(expectedException);

            // Act & Assert
            var action = async () => await _handler.Handle(command, CancellationToken.None);
            
            await action.Should().ThrowAsync<InvalidOperationException>()
                .WithMessage("Database error");
        }

        [Fact]
        public async Task Handle_ShouldLogTemplateCreation()
        {
            // Arrange
            var command = CreateValidCommand();
            
            _repositoryMock
                .Setup(x => x.TemplateNameExistsAsync(command.TemplateName, null))
                .ReturnsAsync(false);

            _repositoryMock
                .Setup(x => x.AddAsync(It.IsAny<CustomComplianceReportTemplate>()))
                .ReturnsAsync((CustomComplianceReportTemplate template) => template);

            // Act
            await _handler.Handle(command, CancellationToken.None);

            // Assert
            _loggerMock.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Custom report template created")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task Handle_WithValidSections_ShouldCreateTemplateWithSections()
        {
            // Arrange
            var command = CreateValidCommand();
            command.Sections = new List<Application.DTOs.ReportSectionDto>
            {
                new()
                {
                    Id = "section1",
                    Name = "Summary Section",
                    Description = "Summary of audit data",
                    Order = 1,
                    IsRequired = true,
                    Template = "Summary template content",
                    DataSources = new List<string> { "AuditLogs" },
                    Type = Domain.ValueObjects.SectionType.Summary,
                    Configuration = new Dictionary<string, object> { { "showCharts", true } }
                }
            };

            _repositoryMock
                .Setup(x => x.TemplateNameExistsAsync(command.TemplateName, null))
                .ReturnsAsync(false);

            _repositoryMock
                .Setup(x => x.AddAsync(It.IsAny<CustomComplianceReportTemplate>()))
                .ReturnsAsync((CustomComplianceReportTemplate template) => template);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Sections.Should().HaveCount(1);
            var section = result.Sections.First();
            section.Id.Should().Be("section1");
            section.Name.Should().Be("Summary Section");
            section.Order.Should().Be(1);
            section.IsRequired.Should().BeTrue();
        }

        [Fact]
        public async Task Handle_WithValidParameters_ShouldCreateTemplateWithParameters()
        {
            // Arrange
            var command = CreateValidCommand();
            command.Parameters = new List<Application.DTOs.ReportParameterDto>
            {
                new()
                {
                    Id = "param1",
                    Name = "startDate",
                    DisplayName = "Start Date",
                    Description = "Report start date",
                    Type = Domain.ValueObjects.ParameterType.Date,
                    IsRequired = true,
                    DefaultValue = DateTime.UtcNow.AddDays(-30),
                    Options = new List<Application.DTOs.ParameterOptionDto>(),
                    Validation = new Dictionary<string, object> { { "minDate", DateTime.UtcNow.AddYears(-1) } }
                }
            };

            _repositoryMock
                .Setup(x => x.TemplateNameExistsAsync(command.TemplateName, null))
                .ReturnsAsync(false);

            _repositoryMock
                .Setup(x => x.AddAsync(It.IsAny<CustomComplianceReportTemplate>()))
                .ReturnsAsync((CustomComplianceReportTemplate template) => template);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Parameters.Should().HaveCount(1);
            var parameter = result.Parameters.First();
            parameter.Id.Should().Be("param1");
            parameter.Name.Should().Be("startDate");
            parameter.DisplayName.Should().Be("Start Date");
            parameter.IsRequired.Should().BeTrue();
        }

        private static CreateCustomReportTemplateCommand CreateValidCommand()
        {
            return new CreateCustomReportTemplateCommand
            {
                TemplateName = "Test Template",
                Description = "Test template description",
                ComplianceStandard = ComplianceStandard.SOX,
                TemplateType = ReportTemplateType.Custom,
                TemplateContent = "Template content here",
                Sections = new List<Application.DTOs.ReportSectionDto>(),
                Parameters = new List<Application.DTOs.ReportParameterDto>(),
                Tags = new List<string> { "test", "compliance" },
                IsPublic = false,
                Metadata = new Dictionary<string, object>(),
                CreatedBy = Guid.NewGuid(),
                CreatedByName = "Test User"
            };
        }
    }
}
