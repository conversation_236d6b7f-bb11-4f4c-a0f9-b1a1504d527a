using FluentAssertions;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;
using FinancialPayment.Domain.Events;
using Xunit;

namespace FinancialPayment.Tests.Domain.Entities;

public class SettlementTests
{
    private readonly Guid _orderId = Guid.NewGuid();
    private readonly Guid _tripId = Guid.NewGuid();
    private readonly Guid _escrowAccountId = Guid.NewGuid();
    private readonly Money _totalAmount = new(10000m, "INR");

    [Fact]
    public void Constructor_ShouldCreateSettlement_WithValidParameters()
    {
        // Act
        var settlement = new Settlement(
            _orderId,
            _tripId,
            _escrowAccountId,
            _totalAmount,
            "Test settlement");

        // Assert
        settlement.OrderId.Should().Be(_orderId);
        settlement.TripId.Should().Be(_tripId);
        settlement.EscrowAccountId.Should().Be(_escrowAccountId);
        settlement.TotalAmount.Should().Be(_totalAmount);
        settlement.Status.Should().Be(SettlementStatus.Created);
        settlement.SettlementNumber.Should().NotBeNullOrEmpty();
        settlement.Notes.Should().Be("Test settlement");
        settlement.Distributions.Should().BeEmpty();
    }

    [Fact]
    public void Constructor_ShouldThrowException_WhenTotalAmountIsZero()
    {
        // Arrange
        var zeroAmount = Money.Zero("INR");

        // Act & Assert
        var action = () => new Settlement(_orderId, _tripId, _escrowAccountId, zeroAmount);

        action.Should().Throw<ArgumentException>()
            .WithMessage("Total amount must be greater than zero");
    }

    [Fact]
    public void AddDistribution_ShouldAddDistribution_WhenValidParameters()
    {
        // Arrange
        var settlement = CreateSettlement();
        var recipientId = Guid.NewGuid();
        var distributionAmount = new Money(5000m, "INR");

        // Act
        settlement.AddDistribution(
            recipientId,
            ParticipantRole.Carrier,
            distributionAmount,
            DistributionType.CarrierPayment,
            "Payment to carrier");

        // Assert
        settlement.Distributions.Should().HaveCount(1);
        var distribution = settlement.Distributions.First();
        distribution.RecipientId.Should().Be(recipientId);
        distribution.RecipientRole.Should().Be(ParticipantRole.Carrier);
        distribution.Amount.Should().Be(distributionAmount);
        distribution.Type.Should().Be(DistributionType.CarrierPayment);
        distribution.Description.Should().Be("Payment to carrier");
        distribution.Status.Should().Be(DistributionStatus.Pending);
    }

    [Fact]
    public void AddDistribution_ShouldThrowException_WhenSettlementNotCreated()
    {
        // Arrange
        var settlement = CreateSettlement();
        AddTestDistributions(settlement); // Add distributions first
        settlement.Process(); // Change status to Processing
        var recipientId = Guid.NewGuid();
        var distributionAmount = new Money(1000m, "INR");

        // Act & Assert
        var action = () => settlement.AddDistribution(
            recipientId,
            ParticipantRole.Carrier,
            distributionAmount,
            DistributionType.CarrierPayment,
            "Payment to carrier");

        action.Should().Throw<InvalidOperationException>()
            .WithMessage("Can only add distributions to created settlements");
    }

    [Fact]
    public void AddDistribution_ShouldThrowException_WhenCurrencyMismatch()
    {
        // Arrange
        var settlement = CreateSettlement();
        var recipientId = Guid.NewGuid();
        var wrongCurrencyAmount = new Money(5000m, "USD");

        // Act & Assert
        var action = () => settlement.AddDistribution(
            recipientId,
            ParticipantRole.Carrier,
            wrongCurrencyAmount,
            DistributionType.CarrierPayment,
            "Payment to carrier");

        action.Should().Throw<ArgumentException>()
            .WithMessage("Distribution currency must match settlement currency");
    }

    [Fact]
    public void AddDistribution_ShouldThrowException_WhenTotalExceedsSettlementAmount()
    {
        // Arrange
        var settlement = CreateSettlement();
        var recipientId1 = Guid.NewGuid();
        var recipientId2 = Guid.NewGuid();
        var distributionAmount1 = new Money(6000m, "INR");
        var distributionAmount2 = new Money(5000m, "INR"); // Total would be 11000, exceeding 10000

        settlement.AddDistribution(recipientId1, ParticipantRole.Carrier, distributionAmount1, DistributionType.CarrierPayment, "Payment 1");

        // Act & Assert
        var action = () => settlement.AddDistribution(
            recipientId2,
            ParticipantRole.Broker,
            distributionAmount2,
            DistributionType.BrokerCommission,
            "Payment 2");

        action.Should().Throw<ArgumentException>()
            .WithMessage("Total distributions cannot exceed settlement amount");
    }

    [Fact]
    public void Process_ShouldChangeStatusToProcessing_WhenDistributionsExist()
    {
        // Arrange
        var settlement = CreateSettlement();
        AddTestDistributions(settlement);

        // Act
        settlement.Process();

        // Assert
        settlement.Status.Should().Be(SettlementStatus.Processing);
    }

    [Fact]
    public void Process_ShouldThrowException_WhenNoDistributions()
    {
        // Arrange
        var settlement = CreateSettlement();

        // Act & Assert
        var action = () => settlement.Process();

        action.Should().Throw<InvalidOperationException>()
            .WithMessage("Cannot process settlement without distributions");
    }

    [Fact]
    public void Process_ShouldThrowException_WhenDistributionsDoNotMatchTotal()
    {
        // Arrange
        var settlement = CreateSettlement();
        var recipientId = Guid.NewGuid();
        var partialAmount = new Money(5000m, "INR"); // Less than total 10000

        settlement.AddDistribution(
            recipientId,
            ParticipantRole.Carrier,
            partialAmount,
            DistributionType.CarrierPayment,
            "Partial payment");

        // Act & Assert
        var action = () => settlement.Process();

        action.Should().Throw<InvalidOperationException>()
            .WithMessage("Total distributions must equal settlement amount");
    }

    [Fact]
    public void CompleteDistribution_ShouldMarkDistributionAsCompleted()
    {
        // Arrange
        var settlement = CreateSettlement();
        AddTestDistributions(settlement);
        settlement.Process();
        var distributionId = settlement.Distributions.First().Id;
        var transactionId = "txn_123";

        // Act
        settlement.CompleteDistribution(distributionId, transactionId);

        // Assert
        var distribution = settlement.Distributions.First(d => d.Id == distributionId);
        distribution.Status.Should().Be(DistributionStatus.Completed);
        distribution.PaymentGatewayTransactionId.Should().Be(transactionId);
    }

    [Fact]
    public void CompleteDistribution_ShouldCompleteSettlement_WhenAllDistributionsCompleted()
    {
        // Arrange
        var settlement = CreateSettlement();
        AddTestDistributions(settlement);
        settlement.Process();

        // Act - Complete all distributions
        foreach (var distribution in settlement.Distributions)
        {
            settlement.CompleteDistribution(distribution.Id, $"txn_{distribution.Id}");
        }

        // Assert
        settlement.Status.Should().Be(SettlementStatus.Completed);
        settlement.ProcessedAt.Should().NotBeNull();
        settlement.ProcessedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void FailDistribution_ShouldFailSettlement()
    {
        // Arrange
        var settlement = CreateSettlement();
        AddTestDistributions(settlement);
        settlement.Process();
        var distributionId = settlement.Distributions.First().Id;
        var failureReason = "Payment gateway error";

        // Act
        settlement.FailDistribution(distributionId, failureReason);

        // Assert
        var distribution = settlement.Distributions.First(d => d.Id == distributionId);
        distribution.Status.Should().Be(DistributionStatus.Failed);
        distribution.FailureReason.Should().Be(failureReason);
        settlement.Status.Should().Be(SettlementStatus.Failed);
        settlement.Notes.Should().Contain(failureReason);
    }

    [Fact]
    public void Fail_ShouldMarkSettlementAsFailed()
    {
        // Arrange
        var settlement = CreateSettlement();
        AddTestDistributions(settlement);
        settlement.Process();
        var failureReason = "External service unavailable";

        // Act
        settlement.Fail(failureReason);

        // Assert
        settlement.Status.Should().Be(SettlementStatus.Failed);
        settlement.Notes.Should().Be(failureReason);
        settlement.ProcessedAt.Should().NotBeNull();
    }

    [Fact]
    public void DomainEvents_ShouldBeRaised_WhenSettlementIsCreated()
    {
        // Act
        var settlement = CreateSettlement();

        // Assert
        settlement.DomainEvents.Should().HaveCount(1);
        settlement.DomainEvents.First().Should().BeOfType<SettlementCreatedEvent>();

        var createdEvent = (SettlementCreatedEvent)settlement.DomainEvents.First();
        createdEvent.SettlementId.Should().Be(settlement.Id);
        createdEvent.OrderId.Should().Be(_orderId);
        createdEvent.TripId.Should().Be(_tripId);
        createdEvent.TotalAmount.Should().Be(_totalAmount);
    }

    [Fact]
    public void DomainEvents_ShouldBeRaised_WhenDistributionIsAdded()
    {
        // Arrange
        var settlement = CreateSettlement();
        settlement.ClearDomainEvents(); // Clear creation event
        var recipientId = Guid.NewGuid();
        var distributionAmount = new Money(5000m, "INR");

        // Act
        settlement.AddDistribution(
            recipientId,
            ParticipantRole.Carrier,
            distributionAmount,
            DistributionType.CarrierPayment,
            "Payment to carrier");

        // Assert
        settlement.DomainEvents.Should().HaveCount(1);
        settlement.DomainEvents.First().Should().BeOfType<SettlementDistributionAddedEvent>();

        var addedEvent = (SettlementDistributionAddedEvent)settlement.DomainEvents.First();
        addedEvent.SettlementId.Should().Be(settlement.Id);
        addedEvent.RecipientId.Should().Be(recipientId);
        addedEvent.Amount.Should().Be(distributionAmount);
    }

    [Fact]
    public void DomainEvents_ShouldBeRaised_WhenSettlementIsCompleted()
    {
        // Arrange
        var settlement = CreateSettlement();
        AddTestDistributions(settlement);
        settlement.Process();
        settlement.ClearDomainEvents(); // Clear previous events

        // Act - Complete all distributions
        foreach (var distribution in settlement.Distributions)
        {
            settlement.CompleteDistribution(distribution.Id, $"txn_{distribution.Id}");
        }

        // Assert
        settlement.DomainEvents.Should().Contain(e => e is SettlementCompletedEvent);

        var completedEvent = settlement.DomainEvents.OfType<SettlementCompletedEvent>().First();
        completedEvent.SettlementId.Should().Be(settlement.Id);
        completedEvent.OrderId.Should().Be(_orderId);
        completedEvent.TripId.Should().Be(_tripId);
        completedEvent.TotalAmount.Should().Be(_totalAmount);
    }

    private Settlement CreateSettlement()
    {
        return new Settlement(_orderId, _tripId, _escrowAccountId, _totalAmount, "Test settlement");
    }

    private void AddTestDistributions(Settlement settlement)
    {
        var carrierId = Guid.NewGuid();
        var brokerId = Guid.NewGuid();

        settlement.AddDistribution(
            carrierId,
            ParticipantRole.Carrier,
            new Money(7000m, "INR"),
            DistributionType.CarrierPayment,
            "Payment to carrier");

        settlement.AddDistribution(
            brokerId,
            ParticipantRole.Broker,
            new Money(3000m, "INR"),
            DistributionType.BrokerCommission,
            "Commission to broker");
    }
}
