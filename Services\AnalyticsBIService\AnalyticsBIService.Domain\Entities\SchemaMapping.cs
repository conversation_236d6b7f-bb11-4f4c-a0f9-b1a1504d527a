using Shared.Domain.Common;

namespace AnalyticsBIService.Domain.Entities;

/// <summary>
/// Schema mapping entity for data warehouse schema transformations
/// </summary>
public class SchemaMapping : BaseEntity
{
    public Guid ConnectionId { get; private set; }
    public string SourceSchema { get; private set; }
    public string SourceTable { get; private set; }
    public string TargetSchema { get; private set; }
    public string TargetTable { get; private set; }
    public string FieldMappings { get; private set; } // JSON serialized field mappings
    public string MappingType { get; private set; }
    public string TransformationRules { get; private set; } // JSON serialized transformation rules
    public bool IsActive { get; private set; }

    private SchemaMapping()
    {
        SourceSchema = string.Empty;
        SourceTable = string.Empty;
        TargetSchema = string.Empty;
        TargetTable = string.Empty;
        FieldMappings = string.Empty;
        MappingType = string.Empty;
        TransformationRules = string.Empty;
    }

    public SchemaMapping(
        Guid connectionId,
        string sourceSchema,
        string sourceTable,
        string targetSchema,
        string targetTable,
        string fieldMappings,
        string mappingType,
        string transformationRules)
    {
        ConnectionId = connectionId;
        SourceSchema = sourceSchema ?? throw new ArgumentNullException(nameof(sourceSchema));
        SourceTable = sourceTable ?? throw new ArgumentNullException(nameof(sourceTable));
        TargetSchema = targetSchema ?? throw new ArgumentNullException(nameof(targetSchema));
        TargetTable = targetTable ?? throw new ArgumentNullException(nameof(targetTable));
        FieldMappings = fieldMappings ?? "{}";
        MappingType = mappingType ?? throw new ArgumentNullException(nameof(mappingType));
        TransformationRules = transformationRules ?? "{}";
        IsActive = true;
    }

    public void UpdateFieldMappings(string fieldMappings)
    {
        FieldMappings = fieldMappings ?? "{}";
        SetUpdatedAt();
    }

    public void UpdateTransformationRules(string transformationRules)
    {
        TransformationRules = transformationRules ?? "{}";
        SetUpdatedAt();
    }

    public void Activate()
    {
        IsActive = true;
        SetUpdatedAt();
    }

    public void Deactivate()
    {
        IsActive = false;
        SetUpdatedAt();
    }
}
