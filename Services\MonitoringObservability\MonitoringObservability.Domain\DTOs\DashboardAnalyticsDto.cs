namespace MonitoringObservability.Domain.DTOs;

/// <summary>
/// DTO for dashboard analytics
/// </summary>
public class DashboardAnalyticsDto
{
    public Guid DashboardId { get; set; }
    public string DashboardName { get; set; } = string.Empty;
    public DashboardUsageMetrics Usage { get; set; } = new();
    public List<DashboardWidgetAnalyticsDto> WidgetAnalytics { get; set; } = new();
    public DashboardPerformanceMetrics Performance { get; set; } = new();
    public DateTime AnalyticsPeriodStart { get; set; }
    public DateTime AnalyticsPeriodEnd { get; set; }
    public DateTime GeneratedAt { get; set; }
}

public class DashboardUsageMetrics
{
    public int TotalViews { get; set; }
    public int UniqueUsers { get; set; }
    public double AverageSessionDurationMinutes { get; set; }
    public int TotalSessions { get; set; }
    public Dictionary<string, int> ViewsByHour { get; set; } = new();
    public Dictionary<string, int> ViewsByDay { get; set; } = new();
    public List<string> TopUsers { get; set; } = new();
}

public class DashboardWidgetAnalyticsDto
{
    public Guid WidgetId { get; set; }
    public string WidgetName { get; set; } = string.Empty;
    public string WidgetType { get; set; } = string.Empty;
    public int InteractionCount { get; set; }
    public double AverageLoadTimeMs { get; set; }
    public int ErrorCount { get; set; }
    public Dictionary<string, int> InteractionsByType { get; set; } = new();
}

public class DashboardPerformanceMetrics
{
    public double AverageLoadTimeMs { get; set; }
    public double AverageRenderTimeMs { get; set; }
    public int TotalErrors { get; set; }
    public double ErrorRate { get; set; }
    public Dictionary<string, double> LoadTimesByWidget { get; set; } = new();
    public List<string> SlowWidgets { get; set; } = new();
}
