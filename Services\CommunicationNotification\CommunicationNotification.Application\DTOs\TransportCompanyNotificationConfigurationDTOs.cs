using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;

namespace CommunicationNotification.Application.DTOs;

/// <summary>
/// DTO for Transport Company notification configuration
/// </summary>
public class TransportCompanyNotificationConfigurationDto
{
    public Guid Id { get; set; }
    public Guid TransportCompanyId { get; set; }
    public bool IsEnabled { get; set; }
    public NotificationChannelPreferencesDto ChannelPreferences { get; set; } = new();
    public QuietHoursConfigurationDto QuietHours { get; set; } = new();
    public NotificationFrequencySettingsDto FrequencySettings { get; set; } = new();
    public NotificationGroupingSettingsDto GroupingSettings { get; set; } = new();
    public List<NotificationTypeConfigurationDto> NotificationTypes { get; set; } = new();
    public List<NotificationRecipientConfigurationDto> Recipients { get; set; } = new();
    public EscalationConfigurationDto? EscalationSettings { get; set; }
    public DigestConfigurationDto? DigestSettings { get; set; }
    public Dictionary<string, object> CustomSettings { get; set; } = new();
    public DateTime? LastUpdatedAt { get; set; }
    public Guid? LastUpdatedByUserId { get; set; }
    public string? LastUpdatedByUserName { get; set; }
}

/// <summary>
/// DTO for notification channel preferences
/// </summary>
public class NotificationChannelPreferencesDto
{
    public bool EmailEnabled { get; set; } = true;
    public bool SmsEnabled { get; set; } = true;
    public bool PushEnabled { get; set; } = true;
    public bool InAppEnabled { get; set; } = true;
    public bool WhatsAppEnabled { get; set; } = false;
    public List<NotificationChannel> PriorityChannels { get; set; } = new();
    public List<NotificationChannel> EmergencyChannels { get; set; } = new();
    public Dictionary<string, List<NotificationChannel>> ChannelsByPriority { get; set; } = new();
}

/// <summary>
/// DTO for quiet hours configuration
/// </summary>
public class QuietHoursConfigurationDto
{
    public bool IsEnabled { get; set; } = false;
    public TimeSpan StartTime { get; set; } = new(22, 0, 0);
    public TimeSpan EndTime { get; set; } = new(6, 0, 0);
    public string TimeZone { get; set; } = "Asia/Kolkata";
    public List<DayOfWeek> ApplicableDays { get; set; } = new();
    public bool EmergencyOverride { get; set; } = true;
    public List<MessageType> ExemptMessageTypes { get; set; } = new();
}

/// <summary>
/// DTO for notification frequency settings
/// </summary>
public class NotificationFrequencySettingsDto
{
    public int MaxNotificationsPerHour { get; set; } = 50;
    public int MaxNotificationsPerDay { get; set; } = 200;
    public Dictionary<string, int> MessageTypeHourlyLimits { get; set; } = new();
    public Dictionary<string, int> MessageTypeDailyLimits { get; set; } = new();
    public TimeSpan CooldownPeriod { get; set; } = TimeSpan.FromMinutes(5);
    public Dictionary<string, TimeSpan> MessageTypeCooldowns { get; set; } = new();
    public bool BurstModeEnabled { get; set; } = true;
    public int BurstModeThreshold { get; set; } = 10;
}

/// <summary>
/// DTO for notification grouping settings
/// </summary>
public class NotificationGroupingSettingsDto
{
    public bool IsEnabled { get; set; } = true;
    public TimeSpan GroupingWindow { get; set; } = TimeSpan.FromMinutes(15);
    public int MaxGroupSize { get; set; } = 10;
    public List<MessageType> GroupableMessageTypes { get; set; } = new();
    public Dictionary<string, TimeSpan> MessageTypeGroupingWindows { get; set; } = new();
    public bool GroupBySender { get; set; } = true;
    public bool GroupByPriority { get; set; } = true;
    public bool GroupByRelatedEntity { get; set; } = true;
}

/// <summary>
/// DTO for escalation configuration
/// </summary>
public class EscalationConfigurationDto
{
    public bool IsEnabled { get; set; } = false;
    public List<EscalationLevelDto> EscalationLevels { get; set; } = new();
    public TimeSpan DefaultEscalationInterval { get; set; } = TimeSpan.FromHours(2);
    public int MaxEscalationLevel { get; set; } = 3;
}

/// <summary>
/// DTO for digest configuration
/// </summary>
public class DigestConfigurationDto
{
    public bool IsEnabled { get; set; } = false;
    public List<DigestScheduleDto> DigestSchedules { get; set; } = new();
    public List<MessageType> DigestableMessageTypes { get; set; } = new();
    public int MaxItemsPerDigest { get; set; } = 50;
    public string DigestTemplate { get; set; } = "default";
}

/// <summary>
/// Request for testing notification configuration
/// </summary>
public class TestNotificationConfigurationRequest
{
    public List<NotificationChannel> ChannelsToTest { get; set; } = new();
    public List<MessageType> MessageTypesToTest { get; set; } = new();
    public string? TestMessage { get; set; }
    public bool TestQuietHours { get; set; } = false;
    public bool TestFrequencyLimits { get; set; } = false;
    public bool TestGrouping { get; set; } = false;
}

/// <summary>
/// Result of testing notification configuration
/// </summary>
public class TestNotificationConfigurationResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime TestedAt { get; set; }
    public List<NotificationTestResult> TestResults { get; set; } = new();
    public TestConfigurationSummary Summary { get; set; } = new();
}

/// <summary>
/// Individual notification test result
/// </summary>
public class NotificationTestResult
{
    public string Channel { get; set; } = string.Empty;
    public bool IsSuccess { get; set; }
    public string Message { get; set; } = string.Empty;
    public TimeSpan ResponseTime { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Summary of test configuration results
/// </summary>
public class TestConfigurationSummary
{
    public int TotalTests { get; set; }
    public int SuccessfulTests { get; set; }
    public int FailedTests { get; set; }
    public double SuccessRate { get; set; }
    public TimeSpan AverageResponseTime { get; set; }
    public List<string> FailedChannels { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
}

/// <summary>
/// DTO for notification analytics
/// </summary>
public class NotificationAnalyticsDto
{
    public Guid TransportCompanyId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public DateTime GeneratedAt { get; set; }
    
    // Overall metrics
    public int TotalNotificationsSent { get; set; }
    public int TotalNotificationsDelivered { get; set; }
    public int TotalNotificationsFailed { get; set; }
    public double DeliveryRate { get; set; }
    public double FailureRate { get; set; }
    
    // Channel breakdown
    public Dictionary<string, NotificationChannelMetrics> ChannelMetrics { get; set; } = new();
    
    // Message type breakdown
    public Dictionary<string, NotificationTypeMetrics> MessageTypeMetrics { get; set; } = new();
    
    // Time-based trends
    public List<NotificationTrendData> DailyTrends { get; set; } = new();
    public List<NotificationTrendData> HourlyTrends { get; set; } = new();
    
    // Configuration effectiveness
    public ConfigurationEffectivenessMetrics ConfigurationMetrics { get; set; } = new();
}

/// <summary>
/// Metrics for notification channels
/// </summary>
public class NotificationChannelMetrics
{
    public string Channel { get; set; } = string.Empty;
    public int TotalSent { get; set; }
    public int TotalDelivered { get; set; }
    public int TotalFailed { get; set; }
    public double DeliveryRate { get; set; }
    public TimeSpan AverageDeliveryTime { get; set; }
    public List<string> TopFailureReasons { get; set; } = new();
}

/// <summary>
/// Metrics for notification message types
/// </summary>
public class NotificationTypeMetrics
{
    public string MessageType { get; set; } = string.Empty;
    public int TotalSent { get; set; }
    public int TotalDelivered { get; set; }
    public int TotalGrouped { get; set; }
    public int TotalSuppressed { get; set; }
    public double GroupingRate { get; set; }
    public double SuppressionRate { get; set; }
    public TimeSpan AverageGroupingWindow { get; set; }
}

/// <summary>
/// Trend data for notifications
/// </summary>
public class NotificationTrendData
{
    public DateTime Date { get; set; }
    public int NotificationsSent { get; set; }
    public int NotificationsDelivered { get; set; }
    public int NotificationsFailed { get; set; }
    public int NotificationsGrouped { get; set; }
    public int NotificationsSuppressed { get; set; }
    public double DeliveryRate { get; set; }
}

/// <summary>
/// Configuration effectiveness metrics
/// </summary>
public class ConfigurationEffectivenessMetrics
{
    public int QuietHoursSuppressions { get; set; }
    public int FrequencyLimitSuppressions { get; set; }
    public int NotificationsGrouped { get; set; }
    public int EscalationsTriggered { get; set; }
    public int DigestsGenerated { get; set; }
    public double ConfigurationCompliance { get; set; }
    public List<string> OptimizationSuggestions { get; set; } = new();
}

/// <summary>
/// Request for notification analytics
/// </summary>
public class GetNotificationAnalyticsRequest
{
    public Guid TransportCompanyId { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public List<NotificationChannel> Channels { get; set; } = new();
    public List<MessageType> MessageTypes { get; set; } = new();
    public bool IncludeDetailedBreakdown { get; set; } = true;
    public bool IncludeTrends { get; set; } = true;
    public bool IncludeRecommendations { get; set; } = true;
}

/// <summary>
/// DTO for notification configuration template
/// </summary>
public class NotificationConfigurationTemplateDto
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty; // "Basic", "Advanced", "Enterprise"
    public bool IsDefault { get; set; }
    public TransportCompanyNotificationConfigurationDto Configuration { get; set; } = new();
    public List<string> RecommendedFor { get; set; } = new(); // Company sizes, industries, etc.
    public Dictionary<string, object> Metadata { get; set; } = new();
}
