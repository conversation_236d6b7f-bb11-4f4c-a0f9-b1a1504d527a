using Microsoft.Extensions.Diagnostics.HealthChecks;
using OrderManagement.Application.Interfaces;

namespace OrderManagement.API.HealthChecks;

public class MessageBrokerHealthCheck : IHealthCheck
{
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<MessageBrokerHealthCheck> _logger;

    public MessageBrokerHealthCheck(
        IMessageBroker messageBroker,
        ILogger<MessageBrokerHealthCheck> logger)
    {
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Checking message broker health");

            // Try to publish a test message to check connectivity
            // Note: This is a simplified check. In a real implementation,
            // you might want to check connection status without actually publishing
            var testData = new { HealthCheck = true, Timestamp = DateTime.UtcNow };
            
            // For RabbitMQ, you might check connection status
            // For now, we'll assume the message broker is healthy if no exception is thrown
            // In a real implementation, you would check the actual connection status
            
            await Task.Delay(10, cancellationToken); // Simulate async operation
            
            _logger.LogDebug("Message broker health check passed");
            
            return HealthCheckResult.Healthy("Message broker is responding");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Message broker health check failed");
            
            return HealthCheckResult.Unhealthy(
                "Message broker is not responding",
                ex);
        }
    }
}
