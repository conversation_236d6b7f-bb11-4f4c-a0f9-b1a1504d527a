using FluentAssertions;
using Microsoft.AspNetCore.Mvc.Testing;
using MobileWorkflow.Application.DTOs;
using MobileWorkflow.IntegrationTests.Infrastructure;
using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using Xunit;

namespace MobileWorkflow.IntegrationTests.API;

public class MilestoneStepControllerTests : IClassFixture<TestWebApplicationFactory<Program>>, IAsyncLifetime
{
    private readonly TestWebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;
    private readonly JsonSerializerOptions _jsonOptions;
    private Guid _templateId;

    public MilestoneStepControllerTests(TestWebApplicationFactory<Program> factory)
    {
        _factory = factory;
        _client = _factory.CreateClient();
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
    }

    public async Task InitializeAsync()
    {
        // Create a test template for step operations
        _templateId = await _factory.ExecuteDbContextAsync(async context =>
        {
            // Clear existing data
            context.MilestoneSteps.RemoveRange(context.MilestoneSteps);
            context.MilestonePayoutRules.RemoveRange(context.MilestonePayoutRules);
            context.MilestoneTemplates.RemoveRange(context.MilestoneTemplates);
            await context.SaveChangesAsync();

            // Create test template
            var template = new Domain.Entities.MilestoneTemplate(
                "Test Template for Steps",
                "Template for testing step operations",
                "Trip",
                "Testing",
                "<EMAIL>");

            context.MilestoneTemplates.Add(template);
            await context.SaveChangesAsync();

            return template.Id;
        });
    }

    public Task DisposeAsync() => Task.CompletedTask;

    [Fact]
    public async Task GetMilestoneSteps_WithValidTemplateId_ShouldReturnSteps()
    {
        // Arrange - Add a step to the template
        await _factory.ExecuteDbContextAsync(async context =>
        {
            var template = await context.MilestoneTemplates.FindAsync(_templateId);
            template!.AddStep("Test Step", "A test step", 1, true);
            await context.SaveChangesAsync();
        });

        // Act
        var response = await _client.GetAsync($"/api/milestone-templates/{_templateId}/MilestoneStep");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var steps = JsonSerializer.Deserialize<List<MilestoneStepDto>>(content, _jsonOptions);
        
        steps.Should().NotBeNull();
        steps.Should().HaveCount(1);
        steps.First().Name.Should().Be("Test Step");
    }

    [Fact]
    public async Task GetRequiredMilestoneSteps_ShouldReturnOnlyRequiredSteps()
    {
        // Arrange - Add required and optional steps
        await _factory.ExecuteDbContextAsync(async context =>
        {
            var template = await context.MilestoneTemplates.FindAsync(_templateId);
            template!.AddStep("Required Step", "A required step", 1, true);
            template.AddStep("Optional Step", "An optional step", 2, false);
            await context.SaveChangesAsync();
        });

        // Act
        var response = await _client.GetAsync($"/api/milestone-templates/{_templateId}/MilestoneStep/required");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var steps = JsonSerializer.Deserialize<List<MilestoneStepDto>>(content, _jsonOptions);
        
        steps.Should().NotBeNull();
        steps.Should().HaveCount(1);
        steps.First().Name.Should().Be("Required Step");
        steps.First().IsRequired.Should().BeTrue();
    }

    [Fact]
    public async Task CreateMilestoneStep_WithValidData_ShouldCreateSuccessfully()
    {
        // Arrange
        var request = new CreateMilestoneStepRequest
        {
            Name = "New Test Step",
            Description = "A new test step",
            SequenceNumber = 1,
            IsRequired = true,
            TriggerCondition = "status=ready",
            Configuration = new Dictionary<string, object> { { "require_photo", true } },
            Metadata = new Dictionary<string, object> { { "importance", "high" } },
            PayoutRules = new List<CreateMilestonePayoutRuleRequest>
            {
                new CreateMilestonePayoutRuleRequest
                {
                    PayoutPercentage = 100.0m,
                    Description = "Full payment for step completion",
                    TriggerCondition = "status=completed"
                }
            }
        };

        // Act
        var response = await _client.PostAsJsonAsync($"/api/milestone-templates/{_templateId}/MilestoneStep", request, _jsonOptions);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Created);
        
        var content = await response.Content.ReadAsStringAsync();
        var step = JsonSerializer.Deserialize<MilestoneStepDto>(content, _jsonOptions);
        
        step.Should().NotBeNull();
        step.Name.Should().Be(request.Name);
        step.Description.Should().Be(request.Description);
        step.SequenceNumber.Should().Be(request.SequenceNumber);
        step.IsRequired.Should().Be(request.IsRequired);
        step.TriggerCondition.Should().Be(request.TriggerCondition);

        // Verify in database
        var dbStep = await _factory.ExecuteDbContextAsync(async context =>
        {
            return context.MilestoneSteps
                .FirstOrDefault(s => s.Name == request.Name);
        });

        dbStep.Should().NotBeNull();
        dbStep!.PayoutRules.Should().HaveCount(1);
        dbStep.PayoutRules.First().PayoutPercentage.Should().Be(100.0m);
    }

    [Fact]
    public async Task UpdateMilestoneStep_WithValidData_ShouldUpdateSuccessfully()
    {
        // Arrange - Create a step first
        var stepId = await _factory.ExecuteDbContextAsync(async context =>
        {
            var template = await context.MilestoneTemplates.FindAsync(_templateId);
            var step = template!.AddStep("Original Step", "Original description", 1, true);
            await context.SaveChangesAsync();
            return step.Id;
        });

        var request = new UpdateMilestoneStepRequest
        {
            Name = "Updated Step Name",
            Description = "Updated description",
            IsRequired = false,
            TriggerCondition = "status=updated",
            Configuration = new Dictionary<string, object> { { "updated", true } },
            Metadata = new Dictionary<string, object> { { "version", "2.0" } }
        };

        // Act
        var response = await _client.PutAsJsonAsync($"/api/milestone-templates/{_templateId}/MilestoneStep/{stepId}", request, _jsonOptions);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var step = JsonSerializer.Deserialize<MilestoneStepDto>(content, _jsonOptions);
        
        step.Should().NotBeNull();
        step.Name.Should().Be(request.Name);
        step.Description.Should().Be(request.Description);
        step.IsRequired.Should().Be(request.IsRequired);
        step.TriggerCondition.Should().Be(request.TriggerCondition);

        // Verify in database
        var dbStep = await _factory.ExecuteDbContextAsync(async context =>
        {
            return context.MilestoneSteps.FirstOrDefault(s => s.Id == stepId);
        });

        dbStep.Should().NotBeNull();
        dbStep!.Name.Should().Be(request.Name);
        dbStep.Description.Should().Be(request.Description);
        dbStep.IsRequired.Should().Be(request.IsRequired);
    }

    [Fact]
    public async Task DeleteMilestoneStep_WithValidId_ShouldDeleteSuccessfully()
    {
        // Arrange - Create a step first
        var stepId = await _factory.ExecuteDbContextAsync(async context =>
        {
            var template = await context.MilestoneTemplates.FindAsync(_templateId);
            var step = template!.AddStep("Step to Delete", "This step will be deleted", 1, false);
            await context.SaveChangesAsync();
            return step.Id;
        });

        // Act
        var response = await _client.DeleteAsync($"/api/milestone-templates/{_templateId}/MilestoneStep/{stepId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NoContent);

        // Verify deletion in database
        var dbStep = await _factory.ExecuteDbContextAsync(async context =>
        {
            return context.MilestoneSteps.FirstOrDefault(s => s.Id == stepId);
        });

        dbStep.Should().BeNull();
    }

    [Fact]
    public async Task ActivateMilestoneStep_WithValidId_ShouldActivateSuccessfully()
    {
        // Arrange - Create and deactivate a step
        var stepId = await _factory.ExecuteDbContextAsync(async context =>
        {
            var template = await context.MilestoneTemplates.FindAsync(_templateId);
            var step = template!.AddStep("Step to Activate", "This step will be activated", 1, true);
            step.Deactivate();
            await context.SaveChangesAsync();
            return step.Id;
        });

        // Act
        var response = await _client.PostAsync($"/api/milestone-templates/{_templateId}/MilestoneStep/{stepId}/activate", null);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        // Verify activation in database
        var dbStep = await _factory.ExecuteDbContextAsync(async context =>
        {
            return context.MilestoneSteps.FirstOrDefault(s => s.Id == stepId);
        });

        dbStep.Should().NotBeNull();
        dbStep!.IsActive.Should().BeTrue();
    }

    [Fact]
    public async Task DeactivateMilestoneStep_WithValidId_ShouldDeactivateSuccessfully()
    {
        // Arrange - Create a step
        var stepId = await _factory.ExecuteDbContextAsync(async context =>
        {
            var template = await context.MilestoneTemplates.FindAsync(_templateId);
            var step = template!.AddStep("Step to Deactivate", "This step will be deactivated", 1, true);
            await context.SaveChangesAsync();
            return step.Id;
        });

        // Act
        var response = await _client.PostAsync($"/api/milestone-templates/{_templateId}/MilestoneStep/{stepId}/deactivate", null);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        // Verify deactivation in database
        var dbStep = await _factory.ExecuteDbContextAsync(async context =>
        {
            return context.MilestoneSteps.FirstOrDefault(s => s.Id == stepId);
        });

        dbStep.Should().NotBeNull();
        dbStep!.IsActive.Should().BeFalse();
    }

    [Fact]
    public async Task GetMilestoneStep_WithInvalidId_ShouldReturnNotFound()
    {
        // Arrange
        var invalidStepId = Guid.NewGuid();

        // Act
        var response = await _client.GetAsync($"/api/milestone-templates/{_templateId}/MilestoneStep/{invalidStepId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task CreateMilestoneStep_WithInvalidTemplateId_ShouldReturnBadRequest()
    {
        // Arrange
        var invalidTemplateId = Guid.NewGuid();
        var request = new CreateMilestoneStepRequest
        {
            Name = "Test Step",
            Description = "Test description",
            SequenceNumber = 1,
            IsRequired = true
        };

        // Act
        var response = await _client.PostAsJsonAsync($"/api/milestone-templates/{invalidTemplateId}/MilestoneStep", request, _jsonOptions);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }
}
