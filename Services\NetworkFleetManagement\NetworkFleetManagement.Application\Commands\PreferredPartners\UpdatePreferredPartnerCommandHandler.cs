using MediatR;
using Microsoft.Extensions.Logging;
using NetworkFleetManagement.Domain.Repositories;

namespace NetworkFleetManagement.Application.Commands.PreferredPartners;

public class UpdatePreferredPartnerCommandHandler : IRequestHandler<UpdatePreferredPartnerCommand, bool>
{
    private readonly IPreferredPartnerRepository _preferredPartnerRepository;
    private readonly ILogger<UpdatePreferredPartnerCommandHandler> _logger;

    public UpdatePreferredPartnerCommandHandler(
        IPreferredPartnerRepository preferredPartnerRepository,
        ILogger<UpdatePreferredPartnerCommandHandler> logger)
    {
        _preferredPartnerRepository = preferredPartnerRepository;
        _logger = logger;
    }

    public async Task<bool> Handle(UpdatePreferredPartnerCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Updating preferred partner {PreferredPartnerId}", request.Id);

        var preferredPartner = await _preferredPartnerRepository.GetByIdAsync(request.Id, cancellationToken);
        if (preferredPartner == null)
        {
            _logger.LogWarning("Preferred partner {PreferredPartnerId} not found", request.Id);
            throw new KeyNotFoundException($"Preferred partner with ID {request.Id} not found");
        }

        // Update preference level and priority if provided
        if (request.PreferenceLevel.HasValue || request.Priority.HasValue)
        {
            var newLevel = request.PreferenceLevel ?? preferredPartner.PreferenceLevel;
            var newPriority = request.Priority ?? preferredPartner.Priority;
            
            // If priority is changing, adjust other partners' priorities
            if (request.Priority.HasValue && request.Priority.Value != preferredPartner.Priority)
            {
                await AdjustExistingPartnerPriorities(
                    preferredPartner.UserId, 
                    newLevel, 
                    newPriority, 
                    preferredPartner.Id, 
                    cancellationToken);
            }
            
            preferredPartner.UpdatePreferenceLevel(newLevel, newPriority);
        }

        // Update rates if provided
        if (request.PreferredCommissionRate.HasValue || request.PreferredServiceRate.HasValue)
        {
            preferredPartner.UpdateRates(request.PreferredCommissionRate, request.PreferredServiceRate);
        }

        // Update auto-assignment settings if any are provided
        if (request.AutoAssignEnabled.HasValue || 
            request.AutoAssignThreshold.HasValue ||
            request.PreferredRoutes != null ||
            request.PreferredLoadTypes != null ||
            request.ExcludedRoutes != null ||
            request.ExcludedLoadTypes != null)
        {
            preferredPartner.UpdateAutoAssignSettings(
                request.AutoAssignEnabled ?? preferredPartner.AutoAssignEnabled,
                request.AutoAssignThreshold ?? preferredPartner.AutoAssignThreshold,
                request.PreferredRoutes ?? preferredPartner.PreferredRoutes.ToList(),
                request.PreferredLoadTypes ?? preferredPartner.PreferredLoadTypes.ToList(),
                request.ExcludedRoutes ?? preferredPartner.ExcludedRoutes.ToList(),
                request.ExcludedLoadTypes ?? preferredPartner.ExcludedLoadTypes.ToList());
        }

        // Update notification preferences if any are provided
        if (request.NotifyOnNewOpportunities.HasValue ||
            request.NotifyOnPerformanceChanges.HasValue ||
            request.NotifyOnContractExpiry.HasValue)
        {
            preferredPartner.UpdateNotificationPreferences(
                request.NotifyOnNewOpportunities ?? preferredPartner.NotifyOnNewOpportunities,
                request.NotifyOnPerformanceChanges ?? preferredPartner.NotifyOnPerformanceChanges,
                request.NotifyOnContractExpiry ?? preferredPartner.NotifyOnContractExpiry);
        }

        // Update agreement details if any are provided
        if (!string.IsNullOrEmpty(request.AgreementNumber) ||
            request.AgreementStartDate.HasValue ||
            request.AgreementEndDate.HasValue ||
            request.PaymentTermsDays.HasValue ||
            !string.IsNullOrEmpty(request.SpecialTerms))
        {
            preferredPartner.UpdateAgreementDetails(
                request.AgreementNumber ?? preferredPartner.AgreementNumber,
                request.AgreementStartDate ?? preferredPartner.AgreementStartDate,
                request.AgreementEndDate ?? preferredPartner.AgreementEndDate,
                request.PaymentTermsDays ?? preferredPartner.PaymentTermsDays,
                request.SpecialTerms ?? preferredPartner.SpecialTerms);
        }

        // Update exclusivity settings if provided
        if (request.IsExclusive.HasValue)
        {
            preferredPartner.SetExclusivity(
                request.IsExclusive.Value,
                request.ExclusivityExpiresAt ?? preferredPartner.ExclusivityExpiresAt);
        }

        // Update notes if provided
        if (!string.IsNullOrEmpty(request.Notes))
        {
            // Append to existing notes with timestamp
            var timestamp = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss");
            var updatedNotes = string.IsNullOrEmpty(preferredPartner.Notes)
                ? $"[{timestamp}] {request.Notes}"
                : $"{preferredPartner.Notes}\n[{timestamp}] {request.Notes}";
            
            // Use reflection or create a method to update notes
            // For now, we'll assume there's a way to update notes
        }

        await _preferredPartnerRepository.UpdateAsync(preferredPartner, cancellationToken);

        _logger.LogInformation("Successfully updated preferred partner {PreferredPartnerId}", request.Id);
        return true;
    }

    private async Task AdjustExistingPartnerPriorities(
        Guid userId, 
        Domain.Entities.PreferenceLevel preferenceLevel, 
        int newPriority, 
        Guid excludeId,
        CancellationToken cancellationToken)
    {
        // Get existing partners in the same preference level (excluding the one being updated)
        var existingPartners = await _preferredPartnerRepository.GetByUserAndPreferenceLevelAsync(
            userId, preferenceLevel, cancellationToken);

        var partnersToAdjust = existingPartners
            .Where(p => p.Id != excludeId && p.Priority >= newPriority)
            .ToList();

        // Adjust priorities of partners that have the same or higher priority
        foreach (var partner in partnersToAdjust)
        {
            partner.UpdatePreferenceLevel(preferenceLevel, partner.Priority + 1);
            await _preferredPartnerRepository.UpdateAsync(partner, cancellationToken);
        }
    }
}
