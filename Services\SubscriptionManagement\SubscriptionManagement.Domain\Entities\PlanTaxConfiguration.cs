using Shared.Domain.Common;
using SubscriptionManagement.Domain.Events;
using SubscriptionManagement.Domain.Exceptions;
using SubscriptionManagement.Domain.ValueObjects;

namespace SubscriptionManagement.Domain.Entities
{
    public class PlanTaxConfiguration : AggregateRoot
    {
        public Guid PlanId { get; private set; }
        public Plan Plan { get; private set; }
        public TaxConfiguration TaxConfiguration { get; private set; }
        public bool IsActive { get; private set; }
        public DateTime EffectiveFrom { get; private set; }
        public DateTime? EffectiveTo { get; private set; }
        public string? Notes { get; private set; }

        private PlanTaxConfiguration()
        {
            Plan = null!;
            TaxConfiguration = null!;
        }

        public PlanTaxConfiguration(Guid planId, Plan plan, TaxConfiguration taxConfiguration, 
            DateTime? effectiveFrom = null, string? notes = null)
        {
            if (planId == Guid.Empty)
                throw new SubscriptionDomainException("Plan ID cannot be empty");

            if (plan == null)
                throw new SubscriptionDomainException("Plan cannot be null");

            if (taxConfiguration == null)
                throw new SubscriptionDomainException("Tax configuration cannot be null");

            PlanId = planId;
            Plan = plan;
            TaxConfiguration = taxConfiguration;
            IsActive = true;
            EffectiveFrom = effectiveFrom ?? DateTime.UtcNow;
            Notes = notes;

            AddDomainEvent(new PlanTaxConfigurationAddedEvent(planId, plan.Name, 
                taxConfiguration.TaxType, taxConfiguration.Rate, taxConfiguration.ApplicableRegions));
        }

        public void UpdateTaxConfiguration(TaxConfiguration newTaxConfiguration, string? notes = null)
        {
            if (newTaxConfiguration == null)
                throw new SubscriptionDomainException("Tax configuration cannot be null");

            var oldRate = TaxConfiguration.Rate;
            TaxConfiguration = newTaxConfiguration;
            Notes = notes;
            UpdatedAt = DateTime.UtcNow;

            AddDomainEvent(new PlanTaxConfigurationChangedEvent(PlanId, Plan.Name, 
                TaxConfiguration.TaxType, oldRate, newTaxConfiguration.Rate));
        }

        public void Activate()
        {
            if (IsActive)
                return;

            IsActive = true;
            UpdatedAt = DateTime.UtcNow;
        }

        public void Deactivate(DateTime? effectiveTo = null)
        {
            if (!IsActive)
                return;

            IsActive = false;
            EffectiveTo = effectiveTo ?? DateTime.UtcNow;
            UpdatedAt = DateTime.UtcNow;

            AddDomainEvent(new PlanTaxConfigurationRemovedEvent(PlanId, Plan.Name, TaxConfiguration.TaxType));
        }

        public void UpdateNotes(string? notes)
        {
            Notes = notes;
            UpdatedAt = DateTime.UtcNow;
        }

        public bool IsEffectiveOn(DateTime date)
        {
            return IsActive && 
                   date >= EffectiveFrom && 
                   (!EffectiveTo.HasValue || date <= EffectiveTo.Value) &&
                   TaxConfiguration.IsActiveOn(date);
        }

        public bool IsApplicableToRegion(string region)
        {
            return TaxConfiguration.IsApplicableToRegion(region);
        }

        public Money CalculateTax(Money baseAmount, string region, DateTime? date = null)
        {
            var checkDate = date ?? DateTime.UtcNow;
            
            if (!IsEffectiveOn(checkDate) || !IsApplicableToRegion(region))
                return Money.Zero(baseAmount.Currency);

            return TaxConfiguration.CalculateTax(baseAmount, region);
        }
    }
}
