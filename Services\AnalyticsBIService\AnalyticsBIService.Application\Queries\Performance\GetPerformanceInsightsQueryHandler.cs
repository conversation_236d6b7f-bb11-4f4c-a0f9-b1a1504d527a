using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Application.Services;
using MediatR;
using Microsoft.Extensions.Logging;

namespace AnalyticsBIService.Application.Queries.Performance;

/// <summary>
/// Handler for performance insights queries
/// </summary>
public class GetPerformanceInsightsQueryHandler :
    IRequestHandler<GetPerformanceInsightsQuery, PerformanceInsightsDto>,
    IRequestHandler<GetQuoteToOrderConversionRateQuery, decimal>,
    IRequestHandler<GetExpiredRFQRatioQuery, decimal>,
    IRequestHandler<GetDriverPerformanceQuery, DriverPerformanceDto>,
    IRequestHandler<GetMilestoneComplianceRateQuery, decimal>,
    IRequestHandler<GetPerformanceTrendsQuery, List<PerformanceTrendDto>>
{
    private readonly IPerformanceInsightsService _performanceInsightsService;
    private readonly ILogger<GetPerformanceInsightsQueryHandler> _logger;

    public GetPerformanceInsightsQueryHandler(
        IPerformanceInsightsService performanceInsightsService,
        ILogger<GetPerformanceInsightsQueryHandler> logger)
    {
        _performanceInsightsService = performanceInsightsService;
        _logger = logger;
    }

    public async Task<PerformanceInsightsDto> Handle(GetPerformanceInsightsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Handling GetPerformanceInsightsQuery for user {UserId} of type {UserType}", 
                request.UserId, request.UserType);

            var insights = await _performanceInsightsService.GetPerformanceInsightsAsync(
                request.UserId, 
                request.UserType, 
                cancellationToken);

            _logger.LogDebug("Successfully retrieved performance insights for user {UserId}", request.UserId);
            return insights;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling GetPerformanceInsightsQuery for user {UserId}", request.UserId);
            throw;
        }
    }

    public async Task<decimal> Handle(GetQuoteToOrderConversionRateQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Handling GetQuoteToOrderConversionRateQuery for user {UserId}", request.UserId);

            var conversionRate = await _performanceInsightsService.CalculateQuoteToOrderConversionRateAsync(
                request.UserId,
                request.FromDate,
                request.ToDate,
                cancellationToken);

            _logger.LogDebug("Successfully calculated quote to order conversion rate: {Rate}% for user {UserId}", 
                conversionRate, request.UserId);
            return conversionRate;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling GetQuoteToOrderConversionRateQuery for user {UserId}", request.UserId);
            throw;
        }
    }

    public async Task<decimal> Handle(GetExpiredRFQRatioQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Handling GetExpiredRFQRatioQuery for user {UserId}", request.UserId);

            var expiredRatio = await _performanceInsightsService.CalculateExpiredRFQRatioAsync(
                request.UserId,
                request.FromDate,
                request.ToDate,
                cancellationToken);

            _logger.LogDebug("Successfully calculated expired RFQ ratio: {Ratio}% for user {UserId}", 
                expiredRatio, request.UserId);
            return expiredRatio;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling GetExpiredRFQRatioQuery for user {UserId}", request.UserId);
            throw;
        }
    }

    public async Task<DriverPerformanceDto> Handle(GetDriverPerformanceQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Handling GetDriverPerformanceQuery for driver {DriverId}", request.DriverId);

            var performance = await _performanceInsightsService.GetDriverPerformanceAsync(
                request.DriverId,
                request.FromDate,
                request.ToDate,
                cancellationToken);

            _logger.LogDebug("Successfully retrieved driver performance for driver {DriverId}", request.DriverId);
            return performance;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling GetDriverPerformanceQuery for driver {DriverId}", request.DriverId);
            throw;
        }
    }

    public async Task<decimal> Handle(GetMilestoneComplianceRateQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Handling GetMilestoneComplianceRateQuery for user {UserId}", request.UserId);

            var complianceRate = await _performanceInsightsService.CalculateMilestoneComplianceRateAsync(
                request.UserId,
                request.FromDate,
                request.ToDate,
                cancellationToken);

            _logger.LogDebug("Successfully calculated milestone compliance rate: {Rate}% for user {UserId}", 
                complianceRate, request.UserId);
            return complianceRate;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling GetMilestoneComplianceRateQuery for user {UserId}", request.UserId);
            throw;
        }
    }

    public async Task<List<PerformanceTrendDto>> Handle(GetPerformanceTrendsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Handling GetPerformanceTrendsQuery for user {UserId} with period {Period}", 
                request.UserId, request.Period);

            var trends = await _performanceInsightsService.GetPerformanceTrendsAsync(
                request.UserId,
                request.UserType,
                request.Period,
                cancellationToken);

            _logger.LogDebug("Successfully retrieved {Count} performance trends for user {UserId}", 
                trends.Count, request.UserId);
            return trends;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling GetPerformanceTrendsQuery for user {UserId}", request.UserId);
            throw;
        }
    }
}
