using Shared.Domain.Common;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Domain.Entities;

/// <summary>
/// Entity for comprehensive audit logging of administrative override actions
/// </summary>
public class AdministrativeAuditLog : BaseEntity
{
    public Guid AdminUserId { get; private set; }
    public string AdminUserName { get; private set; } = string.Empty;
    public string AdminRole { get; private set; } = string.Empty;
    public AdministrativeAction Action { get; private set; }
    public Guid? TargetEntityId { get; private set; }
    public string? TargetEntityType { get; private set; }
    public string Reason { get; private set; } = string.Empty;
    public string? AdditionalNotes { get; private set; }
    public DateTime ActionTimestamp { get; private set; }
    public string? IpAddress { get; private set; }
    public string? UserAgent { get; private set; }
    public AuditSeverity Severity { get; private set; }
    public bool IsSuccessful { get; private set; }
    public string? ErrorMessage { get; private set; }
    public Dictionary<string, object>? AuditMetadata { get; private set; }
    public Guid? CorrelationId { get; private set; }
    public string? SessionId { get; private set; }

    private AdministrativeAuditLog() { } // EF Constructor

    public AdministrativeAuditLog(
        Guid adminUserId,
        string adminUserName,
        string adminRole,
        AdministrativeAction action,
        Guid? targetEntityId,
        string? targetEntityType,
        string reason,
        string? additionalNotes = null,
        Dictionary<string, object>? auditMetadata = null,
        string? ipAddress = null,
        string? userAgent = null,
        Guid? correlationId = null,
        string? sessionId = null)
    {
        AdminUserId = adminUserId;
        AdminUserName = adminUserName ?? throw new ArgumentNullException(nameof(adminUserName));
        AdminRole = adminRole ?? throw new ArgumentNullException(nameof(adminRole));
        Action = action;
        TargetEntityId = targetEntityId;
        TargetEntityType = targetEntityType;
        Reason = reason ?? throw new ArgumentNullException(nameof(reason));
        AdditionalNotes = additionalNotes;
        ActionTimestamp = DateTime.UtcNow;
        IpAddress = ipAddress;
        UserAgent = userAgent;
        Severity = DetermineSeverity(action);
        IsSuccessful = true;
        AuditMetadata = auditMetadata;
        CorrelationId = correlationId ?? Guid.NewGuid();
        SessionId = sessionId;
    }

    public void MarkAsFailed(string errorMessage)
    {
        IsSuccessful = false;
        ErrorMessage = errorMessage;
        Severity = AuditSeverity.High; // Failed admin actions are always high severity
    }

    public void UpdateMetadata(Dictionary<string, object> additionalMetadata)
    {
        if (AuditMetadata == null)
        {
            AuditMetadata = additionalMetadata;
        }
        else
        {
            foreach (var kvp in additionalMetadata)
            {
                AuditMetadata[kvp.Key] = kvp.Value;
            }
        }
    }

    public void AddMetadata(string key, object value)
    {
        AuditMetadata ??= new Dictionary<string, object>();
        AuditMetadata[key] = value;
    }

    private static AuditSeverity DetermineSeverity(AdministrativeAction action)
    {
        return action switch
        {
            AdministrativeAction.ForceAwardRfq => AuditSeverity.Critical,
            AdministrativeAction.ResetRfqAward => AuditSeverity.Critical,
            AdministrativeAction.SecurityViolation => AuditSeverity.Critical,
            AdministrativeAction.DataExport => AuditSeverity.High,
            AdministrativeAction.BulkUpdate => AuditSeverity.High,
            AdministrativeAction.SystemConfiguration => AuditSeverity.Medium,
            AdministrativeAction.UserManagement => AuditSeverity.Medium,
            AdministrativeAction.ReportGeneration => AuditSeverity.Low,
            AdministrativeAction.DataView => AuditSeverity.Low,
            _ => AuditSeverity.Medium
        };
    }

    // Factory methods for common audit scenarios
    public static AdministrativeAuditLog CreateForceAwardAudit(
        Guid adminUserId,
        string adminUserName,
        string adminRole,
        Guid rfqId,
        Guid bidId,
        string reason,
        Dictionary<string, object>? metadata = null)
    {
        var auditMetadata = new Dictionary<string, object>
        {
            ["rfqId"] = rfqId,
            ["bidId"] = bidId,
            ["actionType"] = "ForceAward"
        };

        if (metadata != null)
        {
            foreach (var kvp in metadata)
                auditMetadata[kvp.Key] = kvp.Value;
        }

        return new AdministrativeAuditLog(
            adminUserId,
            adminUserName,
            adminRole,
            AdministrativeAction.ForceAwardRfq,
            rfqId,
            "RFQ",
            reason,
            null,
            auditMetadata);
    }

    public static AdministrativeAuditLog CreateResetAwardAudit(
        Guid adminUserId,
        string adminUserName,
        string adminRole,
        Guid rfqId,
        string reason,
        Dictionary<string, object>? metadata = null)
    {
        var auditMetadata = new Dictionary<string, object>
        {
            ["rfqId"] = rfqId,
            ["actionType"] = "ResetAward"
        };

        if (metadata != null)
        {
            foreach (var kvp in metadata)
                auditMetadata[kvp.Key] = kvp.Value;
        }

        return new AdministrativeAuditLog(
            adminUserId,
            adminUserName,
            adminRole,
            AdministrativeAction.ResetRfqAward,
            rfqId,
            "RFQ",
            reason,
            null,
            auditMetadata);
    }

    public static AdministrativeAuditLog CreateSecurityViolationAudit(
        Guid userId,
        string userName,
        string userRole,
        string violationType,
        string description,
        Dictionary<string, object>? metadata = null)
    {
        var auditMetadata = new Dictionary<string, object>
        {
            ["violationType"] = violationType,
            ["actionType"] = "SecurityViolation"
        };

        if (metadata != null)
        {
            foreach (var kvp in metadata)
                auditMetadata[kvp.Key] = kvp.Value;
        }

        return new AdministrativeAuditLog(
            userId,
            userName,
            userRole,
            AdministrativeAction.SecurityViolation,
            null,
            "Security",
            violationType,
            description,
            auditMetadata);
    }
}
