using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Domain.Entities;

namespace FinancialPayment.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class FinancialReportingController : ControllerBase
{
    private readonly IFinancialReportingService _reportingService;
    private readonly ILogger<FinancialReportingController> _logger;

    public FinancialReportingController(
        IFinancialReportingService reportingService,
        ILogger<FinancialReportingController> logger)
    {
        _reportingService = reportingService;
        _logger = logger;
    }

    /// <summary>
    /// Generate a financial report
    /// </summary>
    [HttpPost("generate")]
    [Authorize(Roles = "Admin,Finance")]
    public async Task<ActionResult<FinancialReport>> GenerateReport([FromBody] GenerateFinancialReportRequest request)
    {
        try
        {
            var report = await _reportingService.GenerateReportAsync(request);
            
            _logger.LogInformation("Financial report generation initiated: {ReportId}", report.Id);
            return Ok(report);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating financial report");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get financial reports
    /// </summary>
    [HttpGet]
    [Authorize(Roles = "Admin,Finance,Manager")]
    public async Task<ActionResult<List<FinancialReport>>> GetReports(
        [FromQuery] Guid? userId = null,
        [FromQuery] string? reportType = null)
    {
        try
        {
            var reports = await _reportingService.GetReportsAsync(userId, reportType);
            return Ok(reports);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting financial reports");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get financial report by ID
    /// </summary>
    [HttpGet("{id}")]
    [Authorize(Roles = "Admin,Finance,Manager")]
    public async Task<ActionResult<FinancialReport>> GetReport(Guid id)
    {
        try
        {
            var report = await _reportingService.GetReportAsync(id);
            if (report == null)
            {
                return NotFound($"Financial report {id} not found");
            }

            return Ok(report);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting financial report {ReportId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Export financial report
    /// </summary>
    [HttpGet("{id}/export")]
    [Authorize(Roles = "Admin,Finance")]
    public async Task<ActionResult> ExportReport(Guid id, [FromQuery] string format = "excel")
    {
        try
        {
            var reportBytes = await _reportingService.ExportReportAsync(id, format);
            
            var contentType = format.ToLowerInvariant() switch
            {
                "excel" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "pdf" => "application/pdf",
                _ => "application/octet-stream"
            };

            var fileName = $"financial_report_{id}_{DateTime.UtcNow:yyyyMMdd}.{format}";
            
            return File(reportBytes, contentType, fileName);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid export request for report {ReportId}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting financial report {ReportId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get financial metrics
    /// </summary>
    [HttpPost("metrics")]
    [Authorize(Roles = "Admin,Finance,Manager")]
    public async Task<ActionResult<FinancialMetrics>> GetFinancialMetrics([FromBody] GetFinancialMetricsRequest request)
    {
        try
        {
            var metrics = await _reportingService.GetFinancialMetricsAsync(
                request.FromDate, 
                request.ToDate, 
                request.Filter);
            
            return Ok(metrics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting financial metrics");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get revenue breakdown
    /// </summary>
    [HttpPost("revenue-breakdown")]
    [Authorize(Roles = "Admin,Finance,Manager")]
    public async Task<ActionResult<RevenueBreakdown>> GetRevenueBreakdown([FromBody] GetFinancialMetricsRequest request)
    {
        try
        {
            var breakdown = await _reportingService.GetRevenueBreakdownAsync(
                request.FromDate, 
                request.ToDate, 
                request.Filter);
            
            return Ok(breakdown);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting revenue breakdown");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get expense breakdown
    /// </summary>
    [HttpPost("expense-breakdown")]
    [Authorize(Roles = "Admin,Finance,Manager")]
    public async Task<ActionResult<ExpenseBreakdown>> GetExpenseBreakdown([FromBody] GetFinancialMetricsRequest request)
    {
        try
        {
            var breakdown = await _reportingService.GetExpenseBreakdownAsync(
                request.FromDate, 
                request.ToDate, 
                request.Filter);
            
            return Ok(breakdown);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting expense breakdown");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Generate profit & loss statement
    /// </summary>
    [HttpPost("profit-loss")]
    [Authorize(Roles = "Admin,Finance")]
    public async Task<ActionResult<ProfitLossStatement>> GenerateProfitLossStatement([FromBody] GetFinancialMetricsRequest request)
    {
        try
        {
            var statement = await _reportingService.GenerateProfitLossStatementAsync(
                request.FromDate, 
                request.ToDate, 
                request.Filter);
            
            return Ok(statement);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating profit & loss statement");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Generate cash flow statement
    /// </summary>
    [HttpPost("cash-flow")]
    [Authorize(Roles = "Admin,Finance")]
    public async Task<ActionResult<CashFlowStatement>> GenerateCashFlowStatement([FromBody] GetFinancialMetricsRequest request)
    {
        try
        {
            var statement = await _reportingService.GenerateCashFlowStatementAsync(
                request.FromDate, 
                request.ToDate, 
                request.Filter);
            
            return Ok(statement);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating cash flow statement");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get financial trends
    /// </summary>
    [HttpPost("trends")]
    [Authorize(Roles = "Admin,Finance,Manager")]
    public async Task<ActionResult<List<FinancialTrend>>> GetFinancialTrends([FromBody] GetFinancialTrendsRequest request)
    {
        try
        {
            var trends = await _reportingService.GetFinancialTrendsAsync(
                request.FromDate, 
                request.ToDate, 
                request.MetricType, 
                request.Filter);
            
            return Ok(trends);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting financial trends");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Create report template
    /// </summary>
    [HttpPost("templates")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<ReportTemplate>> CreateTemplate([FromBody] CreateReportTemplateRequest request)
    {
        try
        {
            var template = await _reportingService.CreateTemplateAsync(request);
            
            _logger.LogInformation("Report template created: {TemplateId}", template.Id);
            return CreatedAtAction(nameof(GetTemplate), new { id = template.Id }, template);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating report template");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get report templates
    /// </summary>
    [HttpGet("templates")]
    [Authorize(Roles = "Admin,Finance")]
    public async Task<ActionResult<List<ReportTemplate>>> GetTemplates([FromQuery] string? reportType = null)
    {
        try
        {
            var templates = await _reportingService.GetTemplatesAsync(reportType);
            return Ok(templates);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting report templates");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get report template by ID
    /// </summary>
    [HttpGet("templates/{id}")]
    [Authorize(Roles = "Admin,Finance")]
    public async Task<ActionResult<ReportTemplate>> GetTemplate(Guid id)
    {
        try
        {
            var template = await _reportingService.GetTemplateAsync(id);
            if (template == null)
            {
                return NotFound($"Report template {id} not found");
            }

            return Ok(template);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting report template {TemplateId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get financial dashboard data
    /// </summary>
    [HttpPost("dashboard")]
    [Authorize(Roles = "Admin,Finance,Manager")]
    public async Task<ActionResult<FinancialDashboardData>> GetDashboardData([FromBody] GetFinancialMetricsRequest request)
    {
        try
        {
            var metrics = await _reportingService.GetFinancialMetricsAsync(request.FromDate, request.ToDate, request.Filter);
            var revenue = await _reportingService.GetRevenueBreakdownAsync(request.FromDate, request.ToDate, request.Filter);
            var expenses = await _reportingService.GetExpenseBreakdownAsync(request.FromDate, request.ToDate, request.Filter);
            var trends = await _reportingService.GetFinancialTrendsAsync(request.FromDate, request.ToDate, "revenue", request.Filter);

            var kpis = new List<FinancialKPI>
            {
                new FinancialKPI { Name = "Total Revenue", Value = metrics.TotalRevenue.Amount, Unit = metrics.TotalRevenue.Currency, Category = "Revenue" },
                new FinancialKPI { Name = "Net Income", Value = metrics.NetIncome.Amount, Unit = metrics.NetIncome.Currency, Category = "Profitability" },
                new FinancialKPI { Name = "Gross Profit Margin", Value = metrics.GrossProfitMargin, Unit = "%", Category = "Profitability" },
                new FinancialKPI { Name = "Transaction Count", Value = metrics.TotalTransactionCount, Unit = "count", Category = "Volume" },
                new FinancialKPI { Name = "Average Transaction Value", Value = metrics.AverageTransactionValue.Amount, Unit = metrics.AverageTransactionValue.Currency, Category = "Volume" }
            };

            var dashboardData = new FinancialDashboardData
            {
                KPIs = kpis,
                Metrics = metrics,
                Revenue = revenue,
                Expenses = expenses,
                Trends = trends,
                LastUpdated = DateTime.UtcNow
            };

            return Ok(dashboardData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting financial dashboard data");
            return StatusCode(500, "Internal server error");
        }
    }
}

// Request DTOs
public class GetFinancialMetricsRequest
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public FinancialReportFilter? Filter { get; set; }
}

public class GetFinancialTrendsRequest
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string MetricType { get; set; } = "revenue";
    public FinancialReportFilter? Filter { get; set; }
}
