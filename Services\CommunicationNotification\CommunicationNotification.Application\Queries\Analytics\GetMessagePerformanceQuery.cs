using CommunicationNotification.Application.Common;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;
using MediatR;

namespace CommunicationNotification.Application.Queries.Analytics;

/// <summary>
/// Query to get message performance metrics
/// </summary>
public class GetMessagePerformanceQuery : IRequest<QueryResult<MessagePerformanceMetrics>>
{
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public MessageType? MessageType { get; set; }
    public NotificationChannel? Channel { get; set; }
    public Guid? UserId { get; set; }
    public string? CampaignId { get; set; }
    public string? ABTestVariant { get; set; }
}

/// <summary>
/// Query to get channel performance comparison
/// </summary>
public class GetChannelPerformanceQuery : IRequest<QueryResult<List<ChannelPerformanceMetrics>>>
{
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public List<NotificationChannel>? Channels { get; set; }
    public bool IncludeHourlyDistribution { get; set; } = true;
}

/// <summary>
/// Query to get user engagement metrics
/// </summary>
public class GetUserEngagementQuery : IRequest<QueryResult<UserEngagementMetrics>>
{
    public Guid UserId { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public bool IncludeMessageTypePreferences { get; set; } = true;
}

/// <summary>
/// Query to get time-based analytics trends
/// </summary>
public class GetTimeBasedAnalyticsQuery : IRequest<QueryResult<List<TimeBasedMetrics>>>
{
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public TimePeriodType PeriodType { get; set; }
    public MessageType? MessageType { get; set; }
    public NotificationChannel? Channel { get; set; }
    public Guid? UserId { get; set; }
}

/// <summary>
/// Query to get real-time dashboard data
/// </summary>
public class GetRealTimeDashboardQuery : IRequest<QueryResult<Dictionary<string, object>>>
{
    public UserRole UserRole { get; set; }
    public Guid? UserId { get; set; }
    public List<string>? MetricTypes { get; set; }
    public int RefreshIntervalSeconds { get; set; } = 30;
}

/// <summary>
/// Query to get cost analysis metrics
/// </summary>
public class GetCostAnalysisQuery : IRequest<QueryResult<Dictionary<string, decimal>>>
{
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public NotificationChannel? Channel { get; set; }
    public string? CampaignId { get; set; }
    public bool GroupByChannel { get; set; } = true;
    public bool GroupByMessageType { get; set; } = false;
    public bool GroupByDay { get; set; } = false;
}

/// <summary>
/// Query to get A/B test performance comparison
/// </summary>
public class GetABTestPerformanceQuery : IRequest<QueryResult<Dictionary<string, MessagePerformanceMetrics>>>
{
    public string TestId { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public List<string>? Variants { get; set; }
    public bool IncludeStatisticalSignificance { get; set; } = true;
}

/// <summary>
/// Query to get top performing message templates
/// </summary>
public class GetTopPerformingTemplatesQuery : IRequest<QueryResult<List<TemplatePerformanceResult>>>
{
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public int Limit { get; set; } = 10;
    public MessageType? MessageType { get; set; }
    public NotificationChannel? Channel { get; set; }
    public string OrderBy { get; set; } = "EngagementScore"; // EngagementScore, DeliveryRate, ClickThroughRate
}

/// <summary>
/// Query to get user behavior insights
/// </summary>
public class GetUserBehaviorInsightsQuery : IRequest<QueryResult<Dictionary<string, object>>>
{
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public UserRole? UserRole { get; set; }
    public List<string>? InsightTypes { get; set; } // OptimalSendTimes, ChannelPreferences, MessageTypeEngagement
    public int MinimumSampleSize { get; set; } = 100;
}

/// <summary>
/// Query to get analytics dashboard data
/// </summary>
public class GetAnalyticsDashboardQuery : IRequest<QueryResult<Dictionary<string, object>>>
{
    public Guid DashboardId { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public Dictionary<string, string>? Filters { get; set; }
    public bool RefreshData { get; set; } = false;
}

/// <summary>
/// Query to get message delivery funnel analytics
/// </summary>
public class GetMessageDeliveryFunnelQuery : IRequest<QueryResult<MessageDeliveryFunnelResult>>
{
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public MessageType? MessageType { get; set; }
    public NotificationChannel? Channel { get; set; }
    public string? CampaignId { get; set; }
    public bool GroupByChannel { get; set; } = false;
}

/// <summary>
/// Result classes for analytics queries
/// </summary>
public class TemplatePerformanceResult
{
    public string TemplateId { get; set; } = string.Empty;
    public string TemplateName { get; set; } = string.Empty;
    public MessagePerformanceMetrics Performance { get; set; } = MessagePerformanceMetrics.Empty();
    public int UsageCount { get; set; }
    public DateTime LastUsed { get; set; }
    public List<string> TopChannels { get; set; } = new();
}

public class MessageDeliveryFunnelResult
{
    public int TotalSent { get; set; }
    public int Delivered { get; set; }
    public int Read { get; set; }
    public int Clicked { get; set; }
    public int Converted { get; set; }
    public Dictionary<string, int> DropOffPoints { get; set; } = new();
    public Dictionary<NotificationChannel, MessageDeliveryFunnelResult>? ChannelBreakdown { get; set; }
}

public class UserSegmentAnalytics
{
    public string SegmentName { get; set; } = string.Empty;
    public int UserCount { get; set; }
    public MessagePerformanceMetrics Performance { get; set; } = MessagePerformanceMetrics.Empty();
    public Dictionary<NotificationChannel, decimal> ChannelPreferences { get; set; } = new();
    public Dictionary<string, object> Characteristics { get; set; } = new();
}

/// <summary>
/// Query to get user segment analytics
/// </summary>
public class GetUserSegmentAnalyticsQuery : IRequest<QueryResult<List<UserSegmentAnalytics>>>
{
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public List<string>? SegmentCriteria { get; set; } // UserRole, EngagementLevel, PreferredChannel
    public int MinimumSegmentSize { get; set; } = 50;
}

/// <summary>
/// Query to get message heatmap data
/// </summary>
public class GetMessageHeatmapQuery : IRequest<QueryResult<Dictionary<string, object>>>
{
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public string HeatmapType { get; set; } = "HourlyEngagement"; // HourlyEngagement, DailyVolume, ChannelUsage
    public MessageType? MessageType { get; set; }
    public NotificationChannel? Channel { get; set; }
    public string? TimeZone { get; set; } = "UTC";
}
