using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.DTOs;
using SubscriptionManagement.Application.Interfaces;

namespace SubscriptionManagement.Application.Queries.GetPlan;

public class GetPlanQueryHandler : IRequestHandler<GetPlanQuery, PlanDto?>
{
    private readonly IPlanRepository _planRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetPlanQueryHandler> _logger;

    public GetPlanQueryHandler(
        IPlanRepository planRepository,
        IMapper mapper,
        ILogger<GetPlanQueryHandler> logger)
    {
        _planRepository = planRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<PlanDto?> Handle(GetPlanQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting plan with ID: {PlanId}", request.PlanId);

        var plan = await _planRepository.GetByIdAsync(request.PlanId);
        
        if (plan == null)
        {
            _logger.LogWarning("Plan with ID {PlanId} not found", request.PlanId);
            return null;
        }

        var result = _mapper.Map<PlanDto>(plan);

        _logger.LogInformation("Successfully retrieved plan {PlanName} (ID: {PlanId})", plan.Name, plan.Id);

        return result;
    }
}
