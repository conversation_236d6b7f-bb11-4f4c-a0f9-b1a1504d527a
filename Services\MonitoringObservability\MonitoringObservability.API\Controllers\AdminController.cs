using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using MediatR;
using MonitoringObservability.Application.Commands;
using MonitoringObservability.Application.Queries;
using MonitoringObservability.Application.DTOs;
using MonitoringObservability.Domain.Enums;

namespace MonitoringObservability.API.Controllers;

[ApiController]
[Route("api/v{version:apiVersion}/admin")]
[ApiVersion("1.0")]
[Authorize(Roles = "Admin")]
public class AdminController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<AdminController> _logger;

    public AdminController(IMediator mediator, ILogger<AdminController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get comprehensive system health dashboard for admins
    /// </summary>
    [HttpGet("dashboard")]
    public async Task<ActionResult<AdminDashboardDto>> GetAdminDashboard([FromQuery] TimeSpan? timeRange = null)
    {
        try
        {
            var range = timeRange ?? TimeSpan.FromHours(24);
            var fromDate = DateTime.UtcNow - range;

            // Get system overview
            var systemOverviewQuery = new GetSystemOverviewQuery { TimeRange = range };
            var systemOverview = await _mediator.Send(systemOverviewQuery);

            // Get alert statistics
            var alertSeverityQuery = new GetAlertCountBySeverityQuery { FromDate = fromDate };
            var alertServiceQuery = new GetAlertCountByServiceQuery { FromDate = fromDate };
            var alertSeverityCount = await _mediator.Send(alertSeverityQuery);
            var alertServiceCount = await _mediator.Send(alertServiceQuery);

            // Get incident statistics
            var incidentSeverityQuery = new GetIncidentCountBySeverityQuery { FromDate = fromDate };
            var incidentServiceQuery = new GetIncidentCountByServiceQuery { FromDate = fromDate };
            var avgResolutionQuery = new GetAverageResolutionTimeQuery { FromDate = fromDate };
            
            var incidentSeverityCount = await _mediator.Send(incidentSeverityQuery);
            var incidentServiceCount = await _mediator.Send(incidentServiceQuery);
            var avgResolutionTime = await _mediator.Send(avgResolutionQuery);

            // Get health status summary
            var healthStatusQuery = new GetHealthStatusSummaryQuery();
            var healthStatusCount = await _mediator.Send(healthStatusQuery);

            // Get all services health summary
            var servicesHealthQuery = new GetAllServicesHealthSummaryQuery 
            { 
                IncludeMetrics = true, 
                IncludeAlerts = true, 
                IncludeIncidents = true 
            };
            var servicesHealth = await _mediator.Send(servicesHealthQuery);

            var dashboard = new AdminDashboardDto
            {
                SystemOverview = systemOverview,
                AlertStatistics = new AlertStatisticsDto
                {
                    CountBySeverity = alertSeverityCount,
                    CountByService = alertServiceCount,
                    GeneratedAt = DateTime.UtcNow
                },
                IncidentStatistics = new IncidentStatisticsDto
                {
                    CountBySeverity = incidentSeverityCount,
                    CountByService = incidentServiceCount,
                    AverageResolutionTimeMinutes = avgResolutionTime,
                    GeneratedAt = DateTime.UtcNow
                },
                HealthStatusSummary = new HealthStatusSummaryDto
                {
                    StatusCounts = healthStatusCount,
                    TotalHealthChecks = healthStatusCount.Values.Sum(),
                    HealthyPercentage = healthStatusCount.TryGetValue(HealthStatus.Healthy, out var healthy) 
                        ? (double)healthy / healthStatusCount.Values.Sum() * 100 : 0,
                    GeneratedAt = DateTime.UtcNow
                },
                ServicesHealth = servicesHealth.ToList(),
                TimeRange = range,
                GeneratedAt = DateTime.UtcNow
            };

            return Ok(dashboard);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get admin dashboard");
            return StatusCode(500, "Failed to retrieve admin dashboard");
        }
    }

    /// <summary>
    /// Get system performance analysis and optimization recommendations
    /// </summary>
    [HttpGet("performance-analysis")]
    public async Task<ActionResult<PerformanceAnalysisDto>> GetPerformanceAnalysis([FromQuery] TimeSpan? period = null)
    {
        try
        {
            var analysisCommand = new AnalyzeSystemPerformanceCommand
            {
                Period = period ?? TimeSpan.FromHours(24),
                IncludeRecommendations = true,
                AnalyzeThresholds = true
            };

            var result = await _mediator.Send(analysisCommand);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get performance analysis");
            return StatusCode(500, "Failed to retrieve performance analysis");
        }
    }

    /// <summary>
    /// Get detailed system metrics with trends
    /// </summary>
    [HttpGet("metrics/detailed")]
    public async Task<ActionResult<DetailedMetricsDto>> GetDetailedMetrics(
        [FromQuery] string[]? serviceNames = null,
        [FromQuery] TimeSpan? period = null,
        [FromQuery] MetricType? metricType = null)
    {
        try
        {
            var query = new GetDetailedSystemMetricsQuery
            {
                ServiceNames = serviceNames?.ToList(),
                Period = period ?? TimeSpan.FromHours(6),
                MetricType = metricType,
                IncludeTrends = true,
                IncludeAnomalies = true
            };

            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get detailed metrics");
            return StatusCode(500, "Failed to retrieve detailed metrics");
        }
    }

    /// <summary>
    /// Bulk manage alerts (acknowledge, assign, resolve multiple alerts)
    /// </summary>
    [HttpPost("alerts/bulk-action")]
    public async Task<ActionResult<BulkActionResultDto>> BulkManageAlerts([FromBody] BulkAlertActionRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            
            var command = new BulkUpdateAlertsCommand
            {
                AlertIds = request.AlertIds,
                NewStatus = request.NewStatus,
                AssignToUserId = request.AssignToUserId,
                AssignToUserName = request.AssignToUserName,
                UpdatedByUserId = userId,
                UpdateReason = request.UpdateReason
            };

            var result = await _mediator.Send(command);
            
            var bulkResult = new BulkActionResultDto
            {
                TotalRequested = request.AlertIds.Count(),
                SuccessfulUpdates = result.Count(),
                FailedUpdates = request.AlertIds.Count() - result.Count(),
                UpdatedItems = result.ToList(),
                ActionPerformed = request.Action,
                PerformedAt = DateTime.UtcNow,
                PerformedBy = userId
            };

            return Ok(bulkResult);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to perform bulk alert action");
            return StatusCode(500, "Failed to perform bulk alert action");
        }
    }

    /// <summary>
    /// Configure system-wide alert thresholds
    /// </summary>
    [HttpPost("thresholds/configure")]
    public async Task<ActionResult<ThresholdConfigurationResultDto>> ConfigureSystemThresholds([FromBody] ConfigureThresholdsRequest request)
    {
        try
        {
            var command = new ConfigureSystemThresholdsCommand
            {
                ServiceName = request.ServiceName,
                ThresholdConfigurations = request.Thresholds,
                ApplyToExistingMetrics = request.ApplyToExistingMetrics,
                CreateMissingMetrics = request.CreateMissingMetrics
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to configure system thresholds");
            return StatusCode(500, "Failed to configure system thresholds");
        }
    }

    /// <summary>
    /// Get system capacity planning recommendations
    /// </summary>
    [HttpGet("capacity-planning")]
    public async Task<ActionResult<CapacityPlanningDto>> GetCapacityPlanning([FromQuery] TimeSpan? analysisWindow = null)
    {
        try
        {
            var query = new GetCapacityPlanningQuery
            {
                AnalysisWindow = analysisWindow ?? TimeSpan.FromDays(30),
                ProjectionPeriod = TimeSpan.FromDays(90),
                IncludeResourceRecommendations = true,
                IncludeScalingRecommendations = true
            };

            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get capacity planning");
            return StatusCode(500, "Failed to retrieve capacity planning");
        }
    }

    /// <summary>
    /// Generate comprehensive system report
    /// </summary>
    [HttpPost("reports/generate")]
    public async Task<ActionResult<SystemReportDto>> GenerateSystemReport([FromBody] GenerateReportRequest request)
    {
        try
        {
            var command = new GenerateSystemReportCommand
            {
                ReportType = request.ReportType,
                Period = request.Period,
                ServiceNames = request.ServiceNames,
                IncludeMetrics = request.IncludeMetrics,
                IncludeAlerts = request.IncludeAlerts,
                IncludeIncidents = request.IncludeIncidents,
                IncludePerformanceAnalysis = request.IncludePerformanceAnalysis,
                IncludeRecommendations = request.IncludeRecommendations,
                Format = request.Format
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate system report");
            return StatusCode(500, "Failed to generate system report");
        }
    }

    /// <summary>
    /// Get alert escalation rules and configuration
    /// </summary>
    [HttpGet("escalation-rules")]
    public async Task<ActionResult<IEnumerable<EscalationRuleDto>>> GetEscalationRules()
    {
        try
        {
            var query = new GetEscalationRulesQuery();
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get escalation rules");
            return StatusCode(500, "Failed to retrieve escalation rules");
        }
    }

    /// <summary>
    /// Configure alert escalation rules
    /// </summary>
    [HttpPost("escalation-rules")]
    public async Task<ActionResult<EscalationRuleDto>> CreateEscalationRule([FromBody] CreateEscalationRuleRequest request)
    {
        try
        {
            var command = new CreateEscalationRuleCommand
            {
                Name = request.Name,
                Description = request.Description,
                ServiceName = request.ServiceName,
                AlertSeverity = request.AlertSeverity,
                EscalationLevels = request.EscalationLevels,
                IsEnabled = request.IsEnabled
            };

            var result = await _mediator.Send(command);
            return CreatedAtAction(nameof(GetEscalationRules), result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create escalation rule");
            return StatusCode(500, "Failed to create escalation rule");
        }
    }

    /// <summary>
    /// Get notification configuration and statistics
    /// </summary>
    [HttpGet("notifications/config")]
    public async Task<ActionResult<NotificationConfigurationDto>> GetNotificationConfiguration()
    {
        try
        {
            var query = new GetNotificationConfigurationQuery();
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get notification configuration");
            return StatusCode(500, "Failed to retrieve notification configuration");
        }
    }

    /// <summary>
    /// Update notification configuration
    /// </summary>
    [HttpPut("notifications/config")]
    public async Task<ActionResult<NotificationConfigurationDto>> UpdateNotificationConfiguration([FromBody] UpdateNotificationConfigRequest request)
    {
        try
        {
            var command = new UpdateNotificationConfigurationCommand
            {
                EmailSettings = request.EmailSettings,
                SMSSettings = request.SMSSettings,
                SlackSettings = request.SlackSettings,
                TeamsSettings = request.TeamsSettings,
                WebhookSettings = request.WebhookSettings,
                DefaultChannels = request.DefaultChannels,
                EscalationChannels = request.EscalationChannels
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update notification configuration");
            return StatusCode(500, "Failed to update notification configuration");
        }
    }

    /// <summary>
    /// Test notification channels
    /// </summary>
    [HttpPost("notifications/test")]
    public async Task<ActionResult<NotificationTestResultDto>> TestNotificationChannels([FromBody] TestNotificationRequest request)
    {
        try
        {
            var command = new TestNotificationChannelsCommand
            {
                Channels = request.Channels,
                TestMessage = request.TestMessage,
                Recipients = request.Recipients
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to test notification channels");
            return StatusCode(500, "Failed to test notification channels");
        }
    }

    /// <summary>
    /// Get system maintenance recommendations
    /// </summary>
    [HttpGet("maintenance/recommendations")]
    public async Task<ActionResult<MaintenanceRecommendationsDto>> GetMaintenanceRecommendations()
    {
        try
        {
            var query = new GetMaintenanceRecommendationsQuery
            {
                AnalysisPeriod = TimeSpan.FromDays(30),
                IncludeDataCleanup = true,
                IncludePerformanceOptimization = true,
                IncludeSecurityRecommendations = true
            };

            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get maintenance recommendations");
            return StatusCode(500, "Failed to retrieve maintenance recommendations");
        }
    }

    /// <summary>
    /// Execute system maintenance tasks
    /// </summary>
    [HttpPost("maintenance/execute")]
    public async Task<ActionResult<MaintenanceExecutionResultDto>> ExecuteMaintenanceTasks([FromBody] ExecuteMaintenanceRequest request)
    {
        try
        {
            var command = new ExecuteMaintenanceTasksCommand
            {
                TaskTypes = request.TaskTypes,
                DataRetentionPeriod = request.DataRetentionPeriod,
                DryRun = request.DryRun,
                ScheduledFor = request.ScheduledFor
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to execute maintenance tasks");
            return StatusCode(500, "Failed to execute maintenance tasks");
        }
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst("sub") ?? User.FindFirst("user_id");
        if (userIdClaim != null && Guid.TryParse(userIdClaim.Value, out var userId))
        {
            return userId;
        }
        throw new UnauthorizedAccessException("User ID not found in token");
    }
}

// Request DTOs
public class BulkAlertActionRequest
{
    public IEnumerable<Guid> AlertIds { get; set; } = new List<Guid>();
    public string Action { get; set; } = string.Empty; // "acknowledge", "assign", "resolve", "close"
    public AlertStatus? NewStatus { get; set; }
    public Guid? AssignToUserId { get; set; }
    public string? AssignToUserName { get; set; }
    public string? UpdateReason { get; set; }
}

public class ConfigureThresholdsRequest
{
    public string? ServiceName { get; set; }
    public List<ThresholdConfigurationDto> Thresholds { get; set; } = new();
    public bool ApplyToExistingMetrics { get; set; } = true;
    public bool CreateMissingMetrics { get; set; } = false;
}

public class GenerateReportRequest
{
    public ReportType ReportType { get; set; }
    public TimeSpan Period { get; set; }
    public List<string>? ServiceNames { get; set; }
    public bool IncludeMetrics { get; set; } = true;
    public bool IncludeAlerts { get; set; } = true;
    public bool IncludeIncidents { get; set; } = true;
    public bool IncludePerformanceAnalysis { get; set; } = true;
    public bool IncludeRecommendations { get; set; } = true;
    public ReportFormat Format { get; set; } = ReportFormat.JSON;
}

public class CreateEscalationRuleRequest
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string? ServiceName { get; set; }
    public AlertSeverity? AlertSeverity { get; set; }
    public List<EscalationLevelDto> EscalationLevels { get; set; } = new();
    public bool IsEnabled { get; set; } = true;
}

public class UpdateNotificationConfigRequest
{
    public EmailSettingsDto? EmailSettings { get; set; }
    public SMSSettingsDto? SMSSettings { get; set; }
    public SlackSettingsDto? SlackSettings { get; set; }
    public TeamsSettingsDto? TeamsSettings { get; set; }
    public WebhookSettingsDto? WebhookSettings { get; set; }
    public List<NotificationChannel> DefaultChannels { get; set; } = new();
    public Dictionary<AlertSeverity, List<NotificationChannel>> EscalationChannels { get; set; } = new();
}

public class TestNotificationRequest
{
    public List<NotificationChannel> Channels { get; set; } = new();
    public string TestMessage { get; set; } = "Test notification from TLI Monitoring System";
    public List<string> Recipients { get; set; } = new();
}

public class ExecuteMaintenanceRequest
{
    public List<MaintenanceTaskType> TaskTypes { get; set; } = new();
    public TimeSpan? DataRetentionPeriod { get; set; }
    public bool DryRun { get; set; } = true;
    public DateTime? ScheduledFor { get; set; }
}

// Enums for admin features
public enum ReportFormat
{
    JSON = 0,
    PDF = 1,
    Excel = 2,
    CSV = 3
}

public enum MaintenanceTaskType
{
    DataCleanup = 0,
    IndexOptimization = 1,
    PerformanceTuning = 2,
    SecurityAudit = 3,
    ConfigurationValidation = 4
}
