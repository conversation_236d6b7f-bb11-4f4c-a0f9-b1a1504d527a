using FluentValidation;
using MobileWorkflow.Application.Commands.OfflineSync;

namespace MobileWorkflow.Application.Validators
{
    public class StartAdvancedSyncCommandValidator : AbstractValidator<StartAdvancedSyncCommand>
    {
        public StartAdvancedSyncCommandValidator()
        {
            RuleFor(x => x.UserId)
                .NotEmpty()
                .WithMessage("User ID is required");

            RuleFor(x => x.DeviceId)
                .NotEmpty()
                .WithMessage("Device ID is required");

            RuleFor(x => x.Options)
                .NotNull()
                .WithMessage("Sync options are required");

            RuleFor(x => x.MaxBandwidthKbps)
                .GreaterThan(0)
                .When(x => x.MaxBandwidthKbps.HasValue)
                .WithMessage("Max bandwidth must be greater than 0");

            RuleFor(x => x.Options.BatchSize)
                .GreaterThan(0)
                .LessThanOrEqualTo(1000)
                .WithMessage("Batch size must be between 1 and 1000");

            RuleFor(x => x.Options.MaxRetryAttempts)
                .GreaterThanOrEqualTo(0)
                .LessThanOrEqualTo(10)
                .WithMessage("Max retry attempts must be between 0 and 10");
        }
    }

    public class ResolveAdvancedConflictCommandValidator : AbstractValidator<ResolveAdvancedConflictCommand>
    {
        public ResolveAdvancedConflictCommandValidator()
        {
            RuleFor(x => x.ConflictId)
                .NotEmpty()
                .WithMessage("Conflict ID is required");

            RuleFor(x => x.ResolutionStrategy)
                .NotEmpty()
                .WithMessage("Resolution strategy is required")
                .Must(BeValidResolutionStrategy)
                .WithMessage("Invalid resolution strategy");

            RuleFor(x => x.ResolvedBy)
                .NotEmpty()
                .WithMessage("ResolvedBy is required");

            RuleFor(x => x.CustomData)
                .NotNull()
                .When(x => x.ResolutionStrategy.ToLowerInvariant() == "manual")
                .WithMessage("Custom data is required for manual resolution");
        }

        private bool BeValidResolutionStrategy(string strategy)
        {
            var validStrategies = new[] { "localwins", "remotewins", "merge", "manual", "auto" };
            return validStrategies.Contains(strategy.ToLowerInvariant());
        }
    }
}
