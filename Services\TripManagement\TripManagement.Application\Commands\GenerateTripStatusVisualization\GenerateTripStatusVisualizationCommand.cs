using MediatR;
using TripManagement.Application.DTOs;

namespace TripManagement.Application.Commands.GenerateTripStatusVisualization;

public class GenerateTripStatusVisualizationCommand : IRequest<TripStatusVisualizationDto>
{
    public Guid TripId { get; set; }
    public bool IncludeTimeline { get; set; } = true;
    public bool IncludeMilestones { get; set; } = true;
    public bool IncludeNotifications { get; set; } = true;
    public bool IncludeDelayAlerts { get; set; } = true;
    public bool IncludeMetrics { get; set; } = true;
    public bool IncludeLocationTracking { get; set; } = true;
    public bool IncludeExceptions { get; set; } = true;
    public bool IncludeRealTimeData { get; set; } = true;
    public VisualizationDetailLevel DetailLevel { get; set; } = VisualizationDetailLevel.Standard;
    public List<string> CustomFields { get; set; } = new();
    public Guid RequestingUserId { get; set; }
    public string? ViewContext { get; set; } // Mobile, Desktop, Dashboard, Report
}

public enum VisualizationDetailLevel
{
    Basic = 1,
    Standard = 2,
    Detailed = 3,
    Comprehensive = 4
}

public class GenerateDelayAlertCommand : IRequest<List<DelayAlertDto>>
{
    public Guid TripId { get; set; }
    public TimeSpan DelayThreshold { get; set; } = TimeSpan.FromMinutes(30);
    public bool AutoEscalate { get; set; } = true;
    public List<string> EscalationRecipients { get; set; } = new();
    public Guid TriggeredBy { get; set; }
    public string? CustomMessage { get; set; }
    public Dictionary<string, object> AlertParameters { get; set; } = new();
}

public class UpdateMilestoneStatusCommand : IRequest<bool>
{
    public Guid TripId { get; set; }
    public Guid MilestoneId { get; set; }
    public string NewStatus { get; set; } = string.Empty;
    public DateTime? ActualDateTime { get; set; }
    public string? Notes { get; set; }
    public List<DocumentUploadDto> Documents { get; set; } = new();
    public bool NotifyStakeholders { get; set; } = true;
    public Guid UpdatedBy { get; set; }
    public LocationDto? CurrentLocation { get; set; }
}

public class DocumentUploadDto
{
    public string FileName { get; set; } = string.Empty;
    public string FileType { get; set; } = string.Empty;
    public string FileUrl { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public string DocumentType { get; set; } = string.Empty;
    public bool IsRequired { get; set; }
}
