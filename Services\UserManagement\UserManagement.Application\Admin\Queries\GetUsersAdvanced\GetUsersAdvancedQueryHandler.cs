using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.Extensions.Logging;
using UserManagement.Domain.Repositories;
using UserManagement.Domain.Entities;

namespace UserManagement.Application.Admin.Queries.GetUsersAdvanced
{
    public class GetUsersAdvancedQueryHandler : IRequestHandler<GetUsersAdvancedQuery, GetUsersAdvancedResponse>
    {
        private readonly IUserProfileRepository _userProfileRepository;
        private readonly ILogger<GetUsersAdvancedQueryHandler> _logger;

        public GetUsersAdvancedQueryHandler(
            IUserProfileRepository userProfileRepository,
            ILogger<GetUsersAdvancedQueryHandler> logger)
        {
            _userProfileRepository = userProfileRepository;
            _logger = logger;
        }

        public async Task<GetUsersAdvancedResponse> Handle(GetUsersAdvancedQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Getting advanced user list with filters: SearchTerm={SearchTerm}, UserType={UserType}, Status={Status}", 
                    request.SearchTerm, request.UserType, request.Status);

                // Build the query with all filters
                var (users, totalCount) = await _userProfileRepository.GetUsersAdvancedAsync(
                    pageNumber: request.PageNumber,
                    pageSize: request.PageSize,
                    searchTerm: request.SearchTerm,
                    email: request.Email,
                    phoneNumber: request.PhoneNumber,
                    companyName: request.CompanyName,
                    userName: request.UserName,
                    subscriptionPlan: request.SubscriptionPlan,
                    planExpiryFrom: request.PlanExpiryFrom,
                    planExpiryTo: request.PlanExpiryTo,
                    registrationDateFrom: request.RegistrationDateFrom,
                    registrationDateTo: request.RegistrationDateTo,
                    status: request.Status,
                    userType: request.UserType,
                    isActive: request.IsActive,
                    kycStatus: request.KycStatus,
                    kycCompleted: request.KycCompleted,
                    lastLoginFrom: request.LastLoginFrom,
                    lastLoginTo: request.LastLoginTo,
                    minRfqVolume: request.MinRfqVolume,
                    maxRfqVolume: request.MaxRfqVolume,
                    sortBy: request.SortBy,
                    sortDirection: request.SortDirection,
                    hasSubscription: request.HasSubscription,
                    autoRenewalEnabled: request.AutoRenewalEnabled,
                    region: request.Region,
                    state: request.State,
                    city: request.City);

                // Get statistics
                var statistics = await GetUserStatistics();

                // Map to DTOs
                var userDtos = users.Select(MapToUserAdvancedDto).ToList();

                var totalPages = (int)Math.Ceiling((double)totalCount / request.PageSize);

                var response = new GetUsersAdvancedResponse
                {
                    Users = userDtos,
                    TotalCount = totalCount,
                    PageNumber = request.PageNumber,
                    PageSize = request.PageSize,
                    TotalPages = totalPages,
                    HasNextPage = request.PageNumber < totalPages,
                    HasPreviousPage = request.PageNumber > 1,
                    Statistics = statistics
                };

                _logger.LogInformation("Successfully retrieved {Count} users out of {Total} total users", 
                    userDtos.Count, totalCount);

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting advanced user list");
                throw;
            }
        }

        private UserAdvancedDto MapToUserAdvancedDto(UserProfile userProfile)
        {
            return new UserAdvancedDto
            {
                Id = userProfile.Id,
                UserId = userProfile.UserId,
                Email = userProfile.Email,
                FirstName = userProfile.FirstName,
                LastName = userProfile.LastName,
                DisplayName = userProfile.GetDisplayName(),
                PhoneNumber = userProfile.PhoneNumber,
                CompanyName = userProfile.CompanyName,
                UserType = userProfile.UserType,
                Status = userProfile.Status,
                CreatedAt = userProfile.CreatedAt,
                UpdatedAt = userProfile.UpdatedAt,
                LastLoginAt = userProfile.LastLoginAt,
                IsActive = userProfile.Status == ProfileStatus.Approved,
                KycStatus = userProfile.KycStatus?.ToString() ?? "NotStarted",
                KycSubmittedAt = userProfile.KycSubmittedAt,
                KycApprovedAt = userProfile.KycApprovedAt,
                KycCompleted = userProfile.Status == ProfileStatus.Approved,
                
                Subscription = new SubscriptionInfo
                {
                    PlanName = userProfile.SubscriptionPlan ?? "Free",
                    StartDate = userProfile.SubscriptionStartDate,
                    ExpiryDate = userProfile.SubscriptionExpiryDate,
                    IsActive = userProfile.SubscriptionExpiryDate > DateTime.UtcNow,
                    AutoRenewalEnabled = userProfile.AutoRenewalEnabled ?? false,
                    Status = GetSubscriptionStatus(userProfile),
                    MonthlyFee = userProfile.SubscriptionMonthlyFee,
                    FeatureLimits = userProfile.SubscriptionFeatureLimits
                },
                
                Activity = new ActivityInfo
                {
                    LastLoginAt = userProfile.LastLoginAt,
                    LastLoginIp = userProfile.LastLoginIp,
                    RfqCount = userProfile.RfqCount ?? 0,
                    OrderCount = userProfile.OrderCount ?? 0,
                    TripCount = userProfile.TripCount ?? 0,
                    TotalBusinessValue = userProfile.TotalBusinessValue
                },
                
                Address = new AddressInfo
                {
                    Street = userProfile.Address,
                    City = userProfile.City,
                    State = userProfile.State,
                    Country = userProfile.Country,
                    PostalCode = userProfile.PostalCode,
                    Region = userProfile.Region
                },
                
                Documents = new DocumentSummary
                {
                    TotalDocuments = userProfile.TotalDocuments ?? 0,
                    ApprovedDocuments = userProfile.ApprovedDocuments ?? 0,
                    PendingDocuments = userProfile.PendingDocuments ?? 0,
                    RejectedDocuments = userProfile.RejectedDocuments ?? 0,
                    LastDocumentUpload = userProfile.LastDocumentUpload
                }
            };
        }

        private string GetSubscriptionStatus(UserProfile userProfile)
        {
            if (userProfile.SubscriptionExpiryDate == null)
                return "None";
            
            if (userProfile.SubscriptionExpiryDate > DateTime.UtcNow)
                return "Active";
            
            return "Expired";
        }

        private async Task<UserStatistics> GetUserStatistics()
        {
            var stats = await _userProfileRepository.GetUserStatisticsAsync();
            
            return new UserStatistics
            {
                TotalUsers = stats.TotalUsers,
                ActiveUsers = stats.ActiveUsers,
                InactiveUsers = stats.InactiveUsers,
                PendingApproval = stats.PendingApproval,
                KycCompleted = stats.KycCompleted,
                KycPending = stats.KycPending,
                WithActiveSubscription = stats.WithActiveSubscription,
                WithExpiredSubscription = stats.WithExpiredSubscription,
                UsersByType = stats.UsersByType,
                UsersByStatus = stats.UsersByStatus,
                UsersByRegion = stats.UsersByRegion
            };
        }
    }
}
