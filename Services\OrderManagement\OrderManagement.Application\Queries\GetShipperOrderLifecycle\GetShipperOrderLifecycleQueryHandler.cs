using MediatR;
using OrderManagement.Application.Interfaces;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.DTOs;
using OrderManagement.Domain.Enums;


namespace OrderManagement.Application.Queries.GetShipperOrderLifecycle;

public class GetShipperOrderLifecycleQueryHandler : IRequestHandler<GetShipperOrderLifecycleQuery, ShipperOrderLifecycleAnalyticsDto>
{
    private readonly IOrderRepository _orderRepository;
    private readonly IRfqRepository _rfqRepository;
    private readonly ILogger<GetShipperOrderLifecycleQueryHandler> _logger;

    public GetShipperOrderLifecycleQueryHandler(
        IOrderRepository orderRepository,
        IRfqRepository rfqRepository,
        ILogger<GetShipperOrderLifecycleQueryHandler> logger)
    {
        _orderRepository = orderRepository;
        _rfqRepository = rfqRepository;
        _logger = logger;
    }

    public async Task<ShipperOrderLifecycleAnalyticsDto> Handle(GetShipperOrderLifecycleQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting order lifecycle analytics for shipper {ShipperId} from {FromDate} to {ToDate}",
            request.ShipperId, request.FromDate, request.ToDate);

        // Base query for shipper's orders within date range
        // Note: TransportCompanyId represents the shipper in this context
        var orders = await _orderRepository.GetOrdersByTransportCompanyAsync(request.ShipperId, cancellationToken);

        // Apply filters to the list
        var filteredOrders = orders.AsEnumerable();

        if (request.FromDate.HasValue)
            filteredOrders = filteredOrders.Where(o => o.CreatedAt >= request.FromDate.Value);

        if (request.ToDate.HasValue)
            filteredOrders = filteredOrders.Where(o => o.CreatedAt <= request.ToDate.Value);

        if (request.StatusFilter?.Any() == true)
        {
            filteredOrders = filteredOrders.Where(o => request.StatusFilter.Contains(o.Status));
        }

        if (request.ServiceProviderIds?.Any() == true)
        {
            filteredOrders = filteredOrders.Where(o =>
                request.ServiceProviderIds.Contains(o.BrokerId) ||
                (o.CarrierId.HasValue && request.ServiceProviderIds.Contains(o.CarrierId.Value)));
        }

        if (request.LoadTypes?.Any() == true)
        {
            filteredOrders = filteredOrders.Where(o => request.LoadTypes.Contains(o.LoadDetails.LoadType));
        }

        if (request.Routes?.Any() == true)
        {
            filteredOrders = filteredOrders.Where(o => request.Routes.Contains(
                o.RouteDetails.PickupAddress.City + "-" + o.RouteDetails.DeliveryAddress.City));
        }

        var finalOrders = filteredOrders.ToList();

        // Calculate main metrics
        var totalOrders = orders.Count;
        var completedOrders = orders.Count(o => o.Status == OrderStatus.Completed);
        var cancelledOrders = orders.Count(o => o.Status == OrderStatus.Cancelled);
        var inProgressOrders = orders.Count(o => o.Status == OrderStatus.InProgress || o.Status == OrderStatus.Confirmed);

        var totalOrderValue = orders.Sum(o => o.AgreedPrice.Amount);
        var averageOrderValue = totalOrders > 0 ? totalOrderValue / totalOrders : 0;

        var analytics = new ShipperOrderLifecycleAnalyticsDto
        {
            ShipperId = request.ShipperId,
            AnalysisPeriodStart = request.FromDate ?? DateTime.MinValue,
            AnalysisPeriodEnd = request.ToDate ?? DateTime.MaxValue,
            TotalOrders = totalOrders,
            CompletedOrders = completedOrders,
            CancelledOrders = cancelledOrders,
            InProgressOrders = inProgressOrders,
            CompletionRate = totalOrders > 0 ? (decimal)completedOrders / totalOrders * 100 : 0,
            CancellationRate = totalOrders > 0 ? (decimal)cancelledOrders / totalOrders * 100 : 0,
            TotalOrderValue = new MoneyDto { Amount = totalOrderValue, Currency = "INR" },
            AverageOrderValue = new MoneyDto { Amount = averageOrderValue, Currency = "INR" }
        };

        // Calculate average order duration and delivery time
        var completedOrdersWithTimes = orders.Where(o => o.Status == OrderStatus.Completed && o.CompletedAt.HasValue).ToList();
        if (completedOrdersWithTimes.Any())
        {
            var orderDurations = completedOrdersWithTimes.Select(o => o.CompletedAt!.Value - o.CreatedAt).ToList();
            analytics.AverageOrderDuration = TimeSpan.FromTicks((long)orderDurations.Average(d => d.Ticks));

            // For delivery time, we'll use the time from confirmation to completion
            var deliveryTimes = completedOrdersWithTimes
                .Where(o => o.ConfirmedAt.HasValue)
                .Select(o => o.CompletedAt!.Value - o.ConfirmedAt!.Value)
                .ToList();

            if (deliveryTimes.Any())
            {
                analytics.AverageDeliveryTime = TimeSpan.FromTicks((long)deliveryTimes.Average(d => d.Ticks));
            }
        }

        // Status breakdown (if requested)
        if (request.IncludeStatusBreakdown)
        {
            analytics.OrdersByStatus = GetOrdersByStatus(orders);
        }

        // Service provider breakdown (if requested)
        if (request.IncludeProviderBreakdown)
        {
            analytics.OrdersByServiceProvider = GetOrdersByServiceProvider(orders);
        }

        // Daily breakdown (if requested)
        if (request.IncludeDailyBreakdown)
        {
            analytics.DailyOrders = GetDailyOrders(orders);
        }

        // Route breakdown (if requested)
        if (request.IncludeRouteBreakdown)
        {
            analytics.OrdersByRoute = GetOrdersByRoute(orders);
        }

        // Service provider performance (if requested)
        if (request.IncludeServiceProviderComparison)
        {
            analytics.ServiceProviderPerformance = GetServiceProviderPerformance(orders);
        }

        _logger.LogInformation("Successfully calculated order lifecycle analytics for shipper {ShipperId}. Total orders: {TotalOrders}, Completion rate: {CompletionRate}%",
            request.ShipperId, totalOrders, analytics.CompletionRate);

        return analytics;
    }

    private List<OrderLifecycleByStatusDto> GetOrdersByStatus(List<Domain.Entities.Order> orders)
    {
        var totalOrders = orders.Count;

        return orders.GroupBy(o => o.Status)
            .Select(g => new OrderLifecycleByStatusDto
            {
                Status = g.Key,
                Count = g.Count(),
                Percentage = totalOrders > 0 ? (decimal)g.Count() / totalOrders * 100 : 0,
                TotalValue = new MoneyDto { Amount = g.Sum(o => o.AgreedPrice.Amount), Currency = "INR" },
                AverageTimeInStatus = CalculateAverageTimeInStatus(g.ToList(), g.Key)
            })
            .OrderByDescending(s => s.Count)
            .ToList();
    }

    private List<OrderLifecycleByProviderDto> GetOrdersByServiceProvider(List<Domain.Entities.Order> orders)
    {
        var providerAnalytics = new List<OrderLifecycleByProviderDto>();

        // Group by broker
        var brokerGroups = orders.GroupBy(o => o.BrokerId);
        foreach (var group in brokerGroups)
        {
            var brokerOrders = group.ToList();
            var completedCount = brokerOrders.Count(o => o.Status == OrderStatus.Completed);

            providerAnalytics.Add(new OrderLifecycleByProviderDto
            {
                ProviderId = group.Key,
                ProviderName = $"Broker-{group.Key}", // TODO: Get actual broker name
                ProviderType = "Broker",
                TotalOrders = brokerOrders.Count,
                CompletedOrders = completedCount,
                CompletionRate = brokerOrders.Count > 0 ? (decimal)completedCount / brokerOrders.Count * 100 : 0,
                TotalValue = new MoneyDto { Amount = brokerOrders.Sum(o => o.AgreedPrice.Amount), Currency = "INR" },
                AverageDeliveryTime = CalculateAverageDeliveryTime(brokerOrders),
                PerformanceRating = CalculateProviderPerformanceRating(brokerOrders)
            });
        }

        // Group by carrier (for orders that have been assigned to carriers)
        var carrierGroups = orders.Where(o => o.CarrierId.HasValue).GroupBy(o => o.CarrierId!.Value);
        foreach (var group in carrierGroups)
        {
            var carrierOrders = group.ToList();
            var completedCount = carrierOrders.Count(o => o.Status == OrderStatus.Completed);

            providerAnalytics.Add(new OrderLifecycleByProviderDto
            {
                ProviderId = group.Key,
                ProviderName = $"Carrier-{group.Key}", // TODO: Get actual carrier name
                ProviderType = "Carrier",
                TotalOrders = carrierOrders.Count,
                CompletedOrders = completedCount,
                CompletionRate = carrierOrders.Count > 0 ? (decimal)completedCount / carrierOrders.Count * 100 : 0,
                TotalValue = new MoneyDto { Amount = carrierOrders.Sum(o => o.AgreedPrice.Amount), Currency = "INR" },
                AverageDeliveryTime = CalculateAverageDeliveryTime(carrierOrders),
                PerformanceRating = CalculateProviderPerformanceRating(carrierOrders)
            });
        }

        return providerAnalytics.OrderByDescending(p => p.PerformanceRating).ToList();
    }

    private List<OrderLifecycleByDateDto> GetDailyOrders(List<Domain.Entities.Order> orders)
    {
        return orders.GroupBy(o => o.CreatedAt.Date)
            .Select(g => new OrderLifecycleByDateDto
            {
                Date = g.Key,
                OrdersCreated = g.Count(),
                OrdersCompleted = g.Count(o => o.Status == OrderStatus.Completed),
                OrdersCancelled = g.Count(o => o.Status == OrderStatus.Cancelled),
                TotalValue = new MoneyDto { Amount = g.Sum(o => o.AgreedPrice.Amount), Currency = "INR" },
                CompletionRate = g.Count() > 0 ? (decimal)g.Count(o => o.Status == OrderStatus.Completed) / g.Count() * 100 : 0
            })
            .OrderBy(d => d.Date)
            .ToList();
    }

    private List<OrderLifecycleByRouteDto> GetOrdersByRoute(List<Domain.Entities.Order> orders)
    {
        return orders.GroupBy(o => new
        {
            PickupCity = o.RouteDetails.PickupAddress.City,
            PickupState = o.RouteDetails.PickupAddress.State,
            DeliveryCity = o.RouteDetails.DeliveryAddress.City,
            DeliveryState = o.RouteDetails.DeliveryAddress.State
        })
            .Select(g => new OrderLifecycleByRouteDto
            {
                Route = $"{g.Key.PickupCity}, {g.Key.PickupState} → {g.Key.DeliveryCity}, {g.Key.DeliveryState}",
                PickupCity = g.Key.PickupCity,
                PickupState = g.Key.PickupState,
                DeliveryCity = g.Key.DeliveryCity,
                DeliveryState = g.Key.DeliveryState,
                TotalOrders = g.Count(),
                CompletedOrders = g.Count(o => o.Status == OrderStatus.Completed),
                CompletionRate = g.Count() > 0 ? (decimal)g.Count(o => o.Status == OrderStatus.Completed) / g.Count() * 100 : 0,
                AverageOrderValue = new MoneyDto { Amount = g.Count() > 0 ? g.Sum(o => o.AgreedPrice.Amount) / g.Count() : 0, Currency = "INR" },
                AverageDeliveryTime = CalculateAverageDeliveryTime(g.ToList())
            })
            .OrderByDescending(r => r.CompletionRate)
            .ToList();
    }

    private List<ServiceProviderPerformanceDto> GetServiceProviderPerformance(List<Domain.Entities.Order> orders)
    {
        var performance = new List<ServiceProviderPerformanceDto>();

        // Analyze broker performance
        var brokerGroups = orders.GroupBy(o => o.BrokerId);
        foreach (var group in brokerGroups)
        {
            var brokerOrders = group.ToList();
            var completedOrders = brokerOrders.Where(o => o.Status == OrderStatus.Completed).ToList();
            var onTimeDeliveries = CalculateOnTimeDeliveries(completedOrders);

            performance.Add(new ServiceProviderPerformanceDto
            {
                ProviderId = group.Key,
                ProviderName = $"Broker-{group.Key}",
                ProviderType = "Broker",
                TotalOrders = brokerOrders.Count,
                OnTimeDeliveries = onTimeDeliveries,
                OnTimePercentage = completedOrders.Count > 0 ? (decimal)onTimeDeliveries / completedOrders.Count * 100 : 0,
                AverageRating = 4.2m, // TODO: Implement actual rating system
                TotalSpent = new MoneyDto { Amount = brokerOrders.Sum(o => o.AgreedPrice.Amount), Currency = "INR" },
                AverageOrderValue = new MoneyDto { Amount = brokerOrders.Count > 0 ? brokerOrders.Sum(o => o.AgreedPrice.Amount) / brokerOrders.Count : 0, Currency = "INR" },
                AverageDeliveryTime = CalculateAverageDeliveryTime(brokerOrders),
                TotalIssues = brokerOrders.Count(o => o.Status == OrderStatus.Cancelled), // Simplified issue calculation
                IssueRate = brokerOrders.Count > 0 ? (decimal)brokerOrders.Count(o => o.Status == OrderStatus.Cancelled) / brokerOrders.Count * 100 : 0,
                LastOrderDate = brokerOrders.Max(o => o.CreatedAt),
                IsPreferred = false // TODO: Implement preferred provider logic
            });
        }

        return performance.OrderByDescending(p => p.OnTimePercentage).ToList();
    }

    private TimeSpan CalculateAverageTimeInStatus(List<Domain.Entities.Order> orders, OrderStatus status)
    {
        var times = new List<TimeSpan>();

        foreach (var order in orders)
        {
            var statusHistory = order.StatusHistory.Where(h => h.Status == status).ToList();
            if (statusHistory.Any())
            {
                var firstEntry = statusHistory.OrderBy(h => h.ChangedAt).First();
                var nextEntry = order.StatusHistory
                    .Where(h => h.ChangedAt > firstEntry.ChangedAt)
                    .OrderBy(h => h.ChangedAt)
                    .FirstOrDefault();

                var endTime = nextEntry?.ChangedAt ?? DateTime.UtcNow;
                times.Add(endTime - firstEntry.ChangedAt);
            }
        }

        return times.Any() ? TimeSpan.FromTicks((long)times.Average(t => t.Ticks)) : TimeSpan.Zero;
    }

    private TimeSpan CalculateAverageDeliveryTime(List<Domain.Entities.Order> orders)
    {
        var deliveryTimes = orders
            .Where(o => o.Status == OrderStatus.Completed && o.ConfirmedAt.HasValue && o.CompletedAt.HasValue)
            .Select(o => o.CompletedAt!.Value - o.ConfirmedAt!.Value)
            .ToList();

        return deliveryTimes.Any() ? TimeSpan.FromTicks((long)deliveryTimes.Average(t => t.Ticks)) : TimeSpan.Zero;
    }

    private decimal CalculateProviderPerformanceRating(List<Domain.Entities.Order> orders)
    {
        if (!orders.Any()) return 0;

        var completionRate = (decimal)orders.Count(o => o.Status == OrderStatus.Completed) / orders.Count * 100;
        var cancellationRate = (decimal)orders.Count(o => o.Status == OrderStatus.Cancelled) / orders.Count * 100;

        // Simple performance rating calculation
        var rating = completionRate - (cancellationRate * 2);
        return Math.Max(0, Math.Min(100, rating));
    }

    private int CalculateOnTimeDeliveries(List<Domain.Entities.Order> completedOrders)
    {
        // Simplified on-time calculation - assumes orders completed within expected timeframe are on-time
        // TODO: Implement proper on-time delivery calculation based on expected vs actual delivery dates
        return completedOrders.Count(o =>
            o.ConfirmedAt.HasValue &&
            o.CompletedAt.HasValue &&
            (o.CompletedAt.Value - o.ConfirmedAt.Value).TotalDays <= 7); // Assuming 7 days is standard delivery time
    }
}
