using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using UserManagement.Application.Services;
using UserManagement.Domain.Entities;
using UserManagement.Domain.Enums;
using static UserManagement.Application.Services.IOcrService;

namespace UserManagement.Infrastructure.Services
{
    public class AzureOcrService : IOcrService
    {
        private readonly HttpClient _httpClient;
        private readonly IConfiguration _configuration;
        private readonly ILogger<AzureOcrService> _logger;
        private readonly string _endpoint;
        private readonly string _subscriptionKey;

        public AzureOcrService(
            HttpClient httpClient,
            IConfiguration configuration,
            ILogger<AzureOcrService> logger)
        {
            _httpClient = httpClient;
            _configuration = configuration;
            _logger = logger;
            _endpoint = _configuration["AzureComputerVision:Endpoint"] ?? throw new ArgumentNullException("AzureComputerVision:Endpoint");
            _subscriptionKey = _configuration["AzureComputerVision:SubscriptionKey"] ?? throw new ArgumentNullException("AzureComputerVision:SubscriptionKey");
        }

        public async Task<OcrResult> ExtractDataAsync(string filePath, DocumentType documentType, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Starting OCR extraction for file: {FilePath}, DocumentType: {DocumentType}", filePath, documentType);

                if (!File.Exists(filePath))
                {
                    return new OcrResult
                    {
                        Success = false,
                        ErrorMessage = "File not found"
                    };
                }

                using var fileStream = File.OpenRead(filePath);
                return await ExtractDataFromStreamAsync(fileStream, Path.GetFileName(filePath), documentType, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error extracting data from file: {FilePath}", filePath);
                return new OcrResult
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        public async Task<OcrResult> ExtractDataFromStreamAsync(Stream fileStream, string fileName, DocumentType documentType, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Starting OCR extraction for stream: {FileName}, DocumentType: {DocumentType}", fileName, documentType);

                // First, extract text using Azure Computer Vision OCR
                var ocrText = await ExtractTextFromImageAsync(fileStream, cancellationToken);

                if (string.IsNullOrEmpty(ocrText))
                {
                    return new OcrResult
                    {
                        Success = false,
                        ErrorMessage = "No text could be extracted from the document"
                    };
                }

                // Parse the extracted text based on document type
                var extractedData = ParseDocumentData(ocrText, documentType);
                var validationResult = ValidateExtractedData(extractedData, documentType);
                var confidenceScore = CalculateConfidenceScore(extractedData, validationResult);

                var result = new OcrResult
                {
                    Success = true,
                    ExtractedText = ocrText,
                    ExtractedData = extractedData,
                    ConfidenceScore = confidenceScore,
                    ValidationResult = validationResult
                };

                _logger.LogInformation("OCR extraction completed for {FileName}. Confidence: {Confidence}%",
                    fileName, confidenceScore * 100);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error extracting data from stream: {FileName}", fileName);
                return new OcrResult
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        public async Task<bool> IsDocumentTypeSupported(DocumentType documentType)
        {
            // For now, support all document types
            return await Task.FromResult(true);
        }

        public async Task<DocumentValidationResult> ValidateDocumentAsync(string filePath, DocumentType documentType, CancellationToken cancellationToken = default)
        {
            var ocrResult = await ExtractDataAsync(filePath, documentType, cancellationToken);
            return ocrResult.ValidationResult;
        }

        private async Task<string> ExtractTextFromImageAsync(Stream imageStream, CancellationToken cancellationToken)
        {
            try
            {
                // Reset stream position
                imageStream.Position = 0;

                // Prepare the request
                var requestUri = $"{_endpoint}/vision/v3.2/ocr?language=en&detectOrientation=true";

                using var request = new HttpRequestMessage(HttpMethod.Post, requestUri);
                request.Headers.Add("Ocp-Apim-Subscription-Key", _subscriptionKey);

                var content = new StreamContent(imageStream);
                content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/octet-stream");
                request.Content = content;

                // Send the request
                var response = await _httpClient.SendAsync(request, cancellationToken);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
                    _logger.LogError("Azure OCR API error: {StatusCode} - {Content}", response.StatusCode, errorContent);
                    return string.Empty;
                }

                var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
                var ocrResponse = JsonSerializer.Deserialize<AzureOcrResponse>(responseContent);

                // Extract text from the response
                var extractedText = ExtractTextFromOcrResponse(ocrResponse);
                return extractedText;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling Azure OCR API");
                return string.Empty;
            }
        }

        private string ExtractTextFromOcrResponse(AzureOcrResponse? response)
        {
            if (response?.Regions == null || !response.Regions.Any())
                return string.Empty;

            var textLines = new List<string>();

            foreach (var region in response.Regions)
            {
                foreach (var line in region.Lines ?? new List<AzureOcrLine>())
                {
                    var lineText = string.Join(" ", line.Words?.Select(w => w.Text) ?? new List<string>());
                    if (!string.IsNullOrWhiteSpace(lineText))
                    {
                        textLines.Add(lineText);
                    }
                }
            }

            return string.Join("\n", textLines);
        }

        private Dictionary<string, object> ParseDocumentData(string ocrText, DocumentType documentType)
        {
            var extractedData = new Dictionary<string, object>();

            try
            {
                switch (documentType)
                {
                    case DocumentType.AadharCard:
                        ParseAadharCard(ocrText, extractedData);
                        break;
                    case DocumentType.PanCard:
                        ParsePanCard(ocrText, extractedData);
                        break;
                    case DocumentType.DrivingLicense:
                        ParseDrivingLicense(ocrText, extractedData);
                        break;
                    case DocumentType.GstCertificate:
                        ParseGstCertificate(ocrText, extractedData);
                        break;
                    case DocumentType.VehicleRegistration:
                        ParseVehicleRegistration(ocrText, extractedData);
                        break;
                    case DocumentType.VehicleInsurance:
                        ParseVehicleInsurance(ocrText, extractedData);
                        break;
                    default:
                        // For unsupported document types, just store the raw text
                        extractedData["raw_text"] = ocrText;
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error parsing document data for type: {DocumentType}", documentType);
                extractedData["parsing_error"] = ex.Message;
                extractedData["raw_text"] = ocrText;
            }

            return extractedData;
        }

        private void ParseAadharCard(string ocrText, Dictionary<string, object> extractedData)
        {
            // Simple regex patterns for Aadhar card parsing
            var patterns = new Dictionary<string, string>
            {
                ["aadhar_number"] = @"\b\d{4}\s?\d{4}\s?\d{4}\b",
                ["date_of_birth"] = @"\b\d{2}[/-]\d{2}[/-]\d{4}\b",
                ["gender"] = @"\b(MALE|FEMALE|M|F)\b"
            };

            foreach (var pattern in patterns)
            {
                var match = System.Text.RegularExpressions.Regex.Match(ocrText, pattern.Value,
                    System.Text.RegularExpressions.RegexOptions.IgnoreCase);
                if (match.Success)
                {
                    extractedData[pattern.Key] = match.Value.Trim();
                }
            }

            // Extract name (usually appears after "Name" or before DOB)
            var nameMatch = System.Text.RegularExpressions.Regex.Match(ocrText,
                @"(?:Name|नाम)[:\s]*([A-Za-z\s]+)",
                System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            if (nameMatch.Success && nameMatch.Groups.Count > 1)
            {
                extractedData["name"] = nameMatch.Groups[1].Value.Trim();
            }
        }

        private void ParsePanCard(string ocrText, Dictionary<string, object> extractedData)
        {
            // PAN number pattern
            var panMatch = System.Text.RegularExpressions.Regex.Match(ocrText, @"\b[A-Z]{5}\d{4}[A-Z]\b");
            if (panMatch.Success)
            {
                extractedData["pan_number"] = panMatch.Value;
            }

            // Date of birth
            var dobMatch = System.Text.RegularExpressions.Regex.Match(ocrText, @"\b\d{2}[/-]\d{2}[/-]\d{4}\b");
            if (dobMatch.Success)
            {
                extractedData["date_of_birth"] = dobMatch.Value;
            }
        }

        private void ParseDrivingLicense(string ocrText, Dictionary<string, object> extractedData)
        {
            // License number patterns (varies by state)
            var licensePatterns = new[]
            {
                @"\b[A-Z]{2}\d{13}\b", // Standard format
                @"\b[A-Z]{2}-\d{4}-\d{7}\b", // Hyphenated format
                @"\bDL\s*NO[:\s]*([A-Z0-9\-]+)\b"
            };

            foreach (var pattern in licensePatterns)
            {
                var match = System.Text.RegularExpressions.Regex.Match(ocrText, pattern,
                    System.Text.RegularExpressions.RegexOptions.IgnoreCase);
                if (match.Success)
                {
                    extractedData["license_number"] = match.Groups.Count > 1 ? match.Groups[1].Value : match.Value;
                    break;
                }
            }

            // Validity dates
            var validityMatch = System.Text.RegularExpressions.Regex.Match(ocrText,
                @"(?:Valid Till|Validity)[:\s]*(\d{2}[/-]\d{2}[/-]\d{4})",
                System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            if (validityMatch.Success && validityMatch.Groups.Count > 1)
            {
                extractedData["validity_date"] = validityMatch.Groups[1].Value;
            }
        }

        private void ParseGstCertificate(string ocrText, Dictionary<string, object> extractedData)
        {
            // GST number pattern
            var gstMatch = System.Text.RegularExpressions.Regex.Match(ocrText, @"\b\d{2}[A-Z]{5}\d{4}[A-Z]\d[Z][A-Z0-9]\b");
            if (gstMatch.Success)
            {
                extractedData["gst_number"] = gstMatch.Value;
            }

            // Business name (usually appears after "Legal Name" or "Trade Name")
            var nameMatch = System.Text.RegularExpressions.Regex.Match(ocrText,
                @"(?:Legal Name|Trade Name|Business Name)[:\s]*([A-Za-z0-9\s&.,'-]+)",
                System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            if (nameMatch.Success && nameMatch.Groups.Count > 1)
            {
                extractedData["business_name"] = nameMatch.Groups[1].Value.Trim();
            }
        }

        private void ParseVehicleRegistration(string ocrText, Dictionary<string, object> extractedData)
        {
            // Vehicle number pattern
            var vehicleNumberMatch = System.Text.RegularExpressions.Regex.Match(ocrText,
                @"\b[A-Z]{2}\s?\d{2}\s?[A-Z]{1,2}\s?\d{4}\b");
            if (vehicleNumberMatch.Success)
            {
                extractedData["vehicle_number"] = vehicleNumberMatch.Value.Replace(" ", "");
            }

            // Engine and chassis numbers
            var engineMatch = System.Text.RegularExpressions.Regex.Match(ocrText,
                @"(?:Engine No|Engine Number)[:\s]*([A-Z0-9]+)",
                System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            if (engineMatch.Success && engineMatch.Groups.Count > 1)
            {
                extractedData["engine_number"] = engineMatch.Groups[1].Value;
            }

            var chassisMatch = System.Text.RegularExpressions.Regex.Match(ocrText,
                @"(?:Chassis No|Chassis Number)[:\s]*([A-Z0-9]+)",
                System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            if (chassisMatch.Success && chassisMatch.Groups.Count > 1)
            {
                extractedData["chassis_number"] = chassisMatch.Groups[1].Value;
            }
        }

        private void ParseVehicleInsurance(string ocrText, Dictionary<string, object> extractedData)
        {
            // Policy number
            var policyMatch = System.Text.RegularExpressions.Regex.Match(ocrText,
                @"(?:Policy No|Policy Number)[:\s]*([A-Z0-9\-/]+)",
                System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            if (policyMatch.Success && policyMatch.Groups.Count > 1)
            {
                extractedData["policy_number"] = policyMatch.Groups[1].Value;
            }

            // Vehicle number
            var vehicleMatch = System.Text.RegularExpressions.Regex.Match(ocrText,
                @"\b[A-Z]{2}\s?\d{2}\s?[A-Z]{1,2}\s?\d{4}\b");
            if (vehicleMatch.Success)
            {
                extractedData["vehicle_number"] = vehicleMatch.Value.Replace(" ", "");
            }

            // Policy period
            var periodMatch = System.Text.RegularExpressions.Regex.Match(ocrText,
                @"(\d{2}[/-]\d{2}[/-]\d{4})\s*(?:to|TO|-)\s*(\d{2}[/-]\d{2}[/-]\d{4})");
            if (periodMatch.Success && periodMatch.Groups.Count > 2)
            {
                extractedData["policy_start_date"] = periodMatch.Groups[1].Value;
                extractedData["policy_end_date"] = periodMatch.Groups[2].Value;
            }
        }

        private DocumentValidationResult ValidateExtractedData(Dictionary<string, object> extractedData, DocumentType documentType)
        {
            var result = new DocumentValidationResult
            {
                ExtractedFields = extractedData
            };

            var errors = new List<ValidationError>();

            // Validate based on document type
            switch (documentType)
            {
                case DocumentType.AadharCard:
                    ValidateAadharData(extractedData, errors);
                    break;
                case DocumentType.PanCard:
                    ValidatePanData(extractedData, errors);
                    break;
                case DocumentType.DrivingLicense:
                    ValidateLicenseData(extractedData, errors);
                    break;
                    // Add more validations as needed
            }

            result.Errors = errors;
            result.IsValid = !errors.Any();

            return result;
        }

        private void ValidateAadharData(Dictionary<string, object> data, List<ValidationError> errors)
        {
            if (!data.ContainsKey("aadhar_number") || string.IsNullOrEmpty(data["aadhar_number"].ToString()))
            {
                errors.Add(new ValidationError
                {
                    Field = "aadhar_number",
                    Message = "Aadhar number not found or invalid",
                    Code = "AADHAR_NUMBER_MISSING"
                });
            }
            else
            {
                var aadharNumber = data["aadhar_number"].ToString()?.Replace(" ", "");
                if (aadharNumber?.Length != 12 || !aadharNumber.All(char.IsDigit))
                {
                    errors.Add(new ValidationError
                    {
                        Field = "aadhar_number",
                        Message = "Aadhar number must be 12 digits",
                        Code = "AADHAR_NUMBER_INVALID"
                    });
                }
            }
        }

        private void ValidatePanData(Dictionary<string, object> data, List<ValidationError> errors)
        {
            if (!data.ContainsKey("pan_number") || string.IsNullOrEmpty(data["pan_number"].ToString()))
            {
                errors.Add(new ValidationError
                {
                    Field = "pan_number",
                    Message = "PAN number not found",
                    Code = "PAN_NUMBER_MISSING"
                });
            }
        }

        private void ValidateLicenseData(Dictionary<string, object> data, List<ValidationError> errors)
        {
            if (!data.ContainsKey("license_number") || string.IsNullOrEmpty(data["license_number"].ToString()))
            {
                errors.Add(new ValidationError
                {
                    Field = "license_number",
                    Message = "License number not found",
                    Code = "LICENSE_NUMBER_MISSING"
                });
            }
        }

        private decimal CalculateConfidenceScore(Dictionary<string, object> extractedData, DocumentValidationResult validationResult)
        {
            if (!extractedData.Any())
                return 0m;

            var totalFields = GetExpectedFieldCount(extractedData);
            var extractedFields = extractedData.Count(kvp => !string.IsNullOrEmpty(kvp.Value?.ToString()));
            var errorCount = validationResult.Errors.Count;

            // Base score from field extraction
            var fieldScore = totalFields > 0 ? (decimal)extractedFields / totalFields : 0m;

            // Penalty for validation errors
            var errorPenalty = errorCount * 0.1m;

            var finalScore = Math.Max(0m, Math.Min(1m, fieldScore - errorPenalty));

            return Math.Round(finalScore, 2);
        }

        private int GetExpectedFieldCount(Dictionary<string, object> extractedData)
        {
            // This is a simplified approach - in reality, you'd have different expected counts per document type
            return 5; // Average expected fields per document
        }

        // Enhanced OCR methods for advanced processing
        public async Task<OcrExtractionResult> ExtractDataAsync(string filePath, string documentType, CancellationToken cancellationToken = default)
        {
            try
            {
                var result = await ExtractDataAsync(filePath, ParseDocumentType(documentType), cancellationToken);
                return new OcrExtractionResult
                {
                    Fields = result.ExtractedData.ToDictionary(kvp => kvp.Key, kvp => kvp.Value?.ToString() ?? string.Empty),
                    Confidence = result.ConfidenceScore,
                    ExtractedText = new List<string> { result.ExtractedText },
                    Success = result.Success,
                    ErrorMessage = result.ErrorMessage ?? string.Empty,
                    Engine = "Azure Computer Vision",
                    ProcessedAt = DateTime.UtcNow
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in enhanced OCR extraction for file: {FilePath}", filePath);
                return new OcrExtractionResult
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    Engine = "Azure Computer Vision",
                    ProcessedAt = DateTime.UtcNow
                };
            }
        }

        public async Task<OcrComparisonResult> CompareExtractedDataAsync(OcrExtractionResult extractedData, Dictionary<string, string> userProvidedData, string documentType)
        {
            try
            {
                var comparisonResult = new OcrComparisonResult
                {
                    DocumentType = documentType,
                    PassedValidation = true,
                    FieldComparisons = new Dictionary<string, OcrFieldComparison>(),
                    ProcessingEngine = "Azure Computer Vision",
                    ProcessedAt = DateTime.UtcNow,
                    ExtractedText = extractedData.ExtractedText,
                    ValidationIssues = new List<OcrValidationIssue>()
                };

                decimal totalConfidence = 0;
                int fieldCount = 0;

                foreach (var userField in userProvidedData)
                {
                    var extractedValue = extractedData.Fields.GetValueOrDefault(userField.Key, "");
                    var isMatch = string.Equals(extractedValue, userField.Value, StringComparison.OrdinalIgnoreCase);

                    if (!isMatch)
                        comparisonResult.PassedValidation = false;

                    var fieldComparison = new OcrFieldComparison
                    {
                        FieldName = userField.Key,
                        ExtractedValue = extractedValue,
                        UserProvidedValue = userField.Value,
                        IsMatch = isMatch,
                        Confidence = isMatch ? 1.0m : 0.0m,
                        MatchType = isMatch ? "Exact" : "NoMatch",
                        Suggestions = new List<string>()
                    };

                    comparisonResult.FieldComparisons[userField.Key] = fieldComparison;
                    totalConfidence += fieldComparison.Confidence;
                    fieldCount++;
                }

                comparisonResult.OverallConfidence = fieldCount > 0 ? totalConfidence / fieldCount : 0;
                return comparisonResult;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error comparing extracted data");
                return new OcrComparisonResult
                {
                    DocumentType = documentType,
                    PassedValidation = false,
                    FieldComparisons = new Dictionary<string, OcrFieldComparison>(),
                    ProcessingEngine = "Azure Computer Vision",
                    ProcessedAt = DateTime.UtcNow
                };
            }
        }

        public async Task<List<OcrValidationIssue>> ValidateDocumentQualityAsync(string filePath, string documentType)
        {
            var issues = new List<OcrValidationIssue>();

            try
            {
                if (!File.Exists(filePath))
                {
                    issues.Add(new OcrValidationIssue
                    {
                        IssueType = "FileNotFound",
                        Description = "The specified file does not exist",
                        Severity = "Critical",
                        FieldName = string.Empty,
                        Recommendation = "Please provide a valid file path"
                    });
                    return issues;
                }

                var fileInfo = new FileInfo(filePath);

                // Check file size
                if (fileInfo.Length > 20 * 1024 * 1024) // 20MB limit
                {
                    issues.Add(new OcrValidationIssue
                    {
                        IssueType = "FileTooLarge",
                        Description = "File size exceeds 20MB limit",
                        Severity = "Critical",
                        FieldName = string.Empty,
                        Recommendation = "Please reduce file size or compress the document"
                    });
                }

                // Check file extension
                var allowedExtensions = new[] { ".pdf", ".jpg", ".jpeg", ".png", ".bmp", ".tiff" };
                if (!allowedExtensions.Contains(fileInfo.Extension.ToLowerInvariant()))
                {
                    issues.Add(new OcrValidationIssue
                    {
                        IssueType = "UnsupportedFormat",
                        Description = $"File format {fileInfo.Extension} is not supported",
                        Severity = "Critical",
                        FieldName = string.Empty,
                        Recommendation = "Please convert to a supported format (PDF, JPG, PNG, etc.)"
                    });
                }

                return issues;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating document quality for file: {FilePath}", filePath);
                issues.Add(new OcrValidationIssue
                {
                    IssueType = "ValidationError",
                    Description = ex.Message,
                    Severity = "Critical",
                    FieldName = string.Empty,
                    Recommendation = "Please check the file and try again"
                });
                return issues;
            }
        }

        public async Task<decimal> CalculateConfidenceScoreAsync(OcrExtractionResult extractedData, Dictionary<string, string> userProvidedData)
        {
            try
            {
                if (!userProvidedData.Any())
                    return extractedData.Confidence;

                var matchingFields = 0;
                var totalFields = userProvidedData.Count;

                foreach (var userField in userProvidedData)
                {
                    var extractedValue = extractedData.Fields.GetValueOrDefault(userField.Key, "");
                    if (string.Equals(extractedValue, userField.Value, StringComparison.OrdinalIgnoreCase))
                    {
                        matchingFields++;
                    }
                }

                var matchPercentage = (decimal)matchingFields / totalFields;
                return (extractedData.Confidence + matchPercentage) / 2;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating confidence score");
                return 0;
            }
        }

        private DocumentType ParseDocumentType(string documentType)
        {
            return documentType.ToLowerInvariant() switch
            {
                "passport" => DocumentType.Passport,
                "drivinglicense" => DocumentType.DrivingLicense,
                "nationalid" => DocumentType.NationalId,
                "businesslicense" => DocumentType.BusinessLicense,
                "insurancecertificate" => DocumentType.InsuranceCertificate,
                "vehicleregistration" => DocumentType.VehicleRegistration,
                _ => DocumentType.Other
            };
        }
    }

    // Azure OCR Response Models
    public class AzureOcrResponse
    {
        public string Language { get; set; } = string.Empty;
        public double TextAngle { get; set; }
        public string Orientation { get; set; } = string.Empty;
        public List<AzureOcrRegion> Regions { get; set; } = new();
    }

    public class AzureOcrRegion
    {
        public string BoundingBox { get; set; } = string.Empty;
        public List<AzureOcrLine> Lines { get; set; } = new();
    }

    public class AzureOcrLine
    {
        public string BoundingBox { get; set; } = string.Empty;
        public List<AzureOcrWord> Words { get; set; } = new();
    }

    public class AzureOcrWord
    {
        public string BoundingBox { get; set; } = string.Empty;
        public string Text { get; set; } = string.Empty;
    }
}
