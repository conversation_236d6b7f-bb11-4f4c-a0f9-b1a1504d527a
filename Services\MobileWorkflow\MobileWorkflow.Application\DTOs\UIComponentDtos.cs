namespace MobileWorkflow.Application.DTOs;

public class UIComponentDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string ComponentType { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public bool IsPublic { get; set; } = true;
    public Dictionary<string, object> Properties { get; set; } = new();
    public Dictionary<string, object> DefaultValues { get; set; } = new();
    public Dictionary<string, object> Styling { get; set; } = new();
    public Dictionary<string, object> Behavior { get; set; } = new();
    public List<string> SupportedPlatforms { get; set; } = new();
    public Dictionary<string, object> PlatformSpecific { get; set; } = new();
    public Dictionary<string, object> Dependencies { get; set; } = new();
    public bool IsComposite { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public string? UpdatedBy { get; set; }
}

public class ComponentThemeDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public bool IsDefault { get; set; }
    public bool IsActive { get; set; }
    public Dictionary<string, object> Colors { get; set; } = new();
    public Dictionary<string, object> Typography { get; set; } = new();
    public Dictionary<string, object> Spacing { get; set; } = new();
    public Dictionary<string, object> Borders { get; set; } = new();
    public Dictionary<string, object> Shadows { get; set; } = new();
    public Dictionary<string, object> Animations { get; set; } = new();
    public Dictionary<string, object> CustomProperties { get; set; } = new();
    public List<string> SupportedPlatforms { get; set; } = new();
    public Dictionary<string, object> PlatformOverrides { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public string? UpdatedBy { get; set; }
}

// Alias for backward compatibility
public class ThemeDto : ComponentThemeDto
{
}

public class ComponentLibraryDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public List<string> SupportedPlatforms { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
    public Dictionary<string, object> Dependencies { get; set; } = new();
    public string? RepositoryUrl { get; set; }
    public string? DocumentationUrl { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public string? UpdatedBy { get; set; }
}

public class ComponentUsageStatsDto
{
    public Guid ComponentId { get; set; }
    public int TotalUsages { get; set; }
    public int SuccessfulUsages { get; set; }
    public int FailedUsages { get; set; }
    public double AverageDuration { get; set; }
    public double SuccessRate { get; set; }
    public Dictionary<string, int> PlatformBreakdown { get; set; } = new();
    public Dictionary<string, int> ContextBreakdown { get; set; } = new();
    public Dictionary<DateTime, int> DailyUsage { get; set; } = new();
}

public class ResponsiveLayoutDto
{
    public string Platform { get; set; } = string.Empty;
    public string ScreenSize { get; set; } = string.Empty;
    public string Orientation { get; set; } = string.Empty;
    public List<ResponsiveComponentDto> Components { get; set; } = new();
}

public class ResponsiveComponentDto
{
    public Guid ComponentId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public Dictionary<string, object> Layout { get; set; } = new();
    public Dictionary<string, object> Properties { get; set; } = new();
    public Dictionary<string, object> Styling { get; set; } = new();
}

// Request DTOs
public class CreateComponentLibraryRequest
{
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public List<string> SupportedPlatforms { get; set; } = new();
    public Dictionary<string, object>? Configuration { get; set; }
    public string? RepositoryUrl { get; set; }
    public string? DocumentationUrl { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

public class CreateUIComponentRequest
{
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string ComponentType { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public Dictionary<string, object> Properties { get; set; } = new();
    public Dictionary<string, object>? DefaultValues { get; set; }
    public Dictionary<string, object>? Styling { get; set; }
    public Dictionary<string, object>? Behavior { get; set; }
    public List<string> SupportedPlatforms { get; set; } = new();
    public List<ComponentDependency>? Dependencies { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

public class CreateComponentThemeRequest
{
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<string> SupportedPlatforms { get; set; } = new();
    public Dictionary<string, object>? Colors { get; set; }
    public Dictionary<string, object>? Typography { get; set; }
    public Dictionary<string, object>? Spacing { get; set; }
    public bool IsDefault { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

public class UpdateUIComponentRequest
{
    public Dictionary<string, object>? Properties { get; set; }
    public Dictionary<string, object>? Styling { get; set; }
    public Dictionary<string, object>? Behavior { get; set; }
    public string? Version { get; set; }
    public string UpdatedBy { get; set; } = string.Empty;
}

public class UpdateComponentThemeRequest
{
    public Dictionary<string, object>? Colors { get; set; }
    public Dictionary<string, object>? Typography { get; set; }
    public Dictionary<string, object>? Spacing { get; set; }
    public string UpdatedBy { get; set; } = string.Empty;
}

public class RecordComponentUsageRequest
{
    public Guid ComponentId { get; set; }
    public Guid UserId { get; set; }
    public string ApplicationId { get; set; } = string.Empty;
    public string Platform { get; set; } = string.Empty;
    public string Context { get; set; } = string.Empty;
    public Dictionary<string, object> Configuration { get; set; } = new();
    public bool IsSuccessful { get; set; } = true;
    public TimeSpan? Duration { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object>? PerformanceMetrics { get; set; }
    public Dictionary<string, object>? UsageData { get; set; }
}

public class GenerateLayoutRequest
{
    public string Platform { get; set; } = string.Empty;
    public string ScreenSize { get; set; } = string.Empty; // small, medium, large
    public string Orientation { get; set; } = string.Empty; // portrait, landscape
    public List<LayoutComponentRequest> Components { get; set; } = new();
}

public class LayoutComponentRequest
{
    public Guid ComponentId { get; set; }
    public Dictionary<string, object> Properties { get; set; } = new();
    public int Order { get; set; }
}

public class ComponentDependency
{
    public string Name { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public string Type { get; set; } = "component";
}

public class RenderComponentRequest
{
    public Guid ComponentId { get; set; }
    public string Platform { get; set; } = string.Empty;
    public Dictionary<string, object> Props { get; set; } = new();
    public Guid? ThemeId { get; set; }
}

public class ComponentSearchRequest
{
    public string? Platform { get; set; }
    public string? Category { get; set; }
    public string? ComponentType { get; set; }
    public string? SearchTerm { get; set; }
    public List<string>? Tags { get; set; }
    public bool ActiveOnly { get; set; } = true;
}

public class ThemeSearchRequest
{
    public string? Platform { get; set; }
    public string? SearchTerm { get; set; }
    public bool ActiveOnly { get; set; } = true;
    public bool DefaultOnly { get; set; } = false;
}

public class ComponentValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public Dictionary<string, object> ValidationDetails { get; set; } = new();
}

public class ThemeValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public Dictionary<string, object> ValidationDetails { get; set; } = new();
}

public class ComponentCompatibilityDto
{
    public Guid ComponentId { get; set; }
    public string ComponentName { get; set; } = string.Empty;
    public Dictionary<string, bool> PlatformCompatibility { get; set; } = new();
    public Dictionary<string, string> VersionCompatibility { get; set; } = new();
    public List<string> MissingDependencies { get; set; } = new();
    public List<string> ConflictingDependencies { get; set; } = new();
}

public class ComponentPreviewDto
{
    public Guid ComponentId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Platform { get; set; } = string.Empty;
    public Dictionary<string, object> RenderedComponent { get; set; } = new();
    public string PreviewUrl { get; set; } = string.Empty;
    public Dictionary<string, object> PreviewOptions { get; set; } = new();
}

// Workflow DTOs
public class WorkflowExecutionSummary
{
    public Guid ExecutionId { get; set; }
    public Guid WorkflowId { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public Guid TriggeredBy { get; set; }
    public Guid? CurrentStep { get; set; }
    public double ProgressPercentage { get; set; }
}

public class WorkflowPerformanceMetrics
{
    public Guid WorkflowId { get; set; }
    public int TotalExecutions { get; set; }
    public int SuccessfulExecutions { get; set; }
    public int FailedExecutions { get; set; }
    public double AverageExecutionTime { get; set; }
    public double SuccessRate { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
}

public class WorkflowValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public Dictionary<string, object> ValidationDetails { get; set; } = new();
}
