using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using OrderManagement.Application.Interfaces;
using OrderManagement.Infrastructure.BackgroundServices;
using OrderManagement.Infrastructure.Persistence;
using OrderManagement.Infrastructure.Repositories;
using OrderManagement.Infrastructure.Services;
using OrderManagement.Infrastructure.Services;

namespace OrderManagement.Infrastructure;

public static class DependencyInjection
{
    public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        // Database
        services.AddDbContext<OrderManagementDbContext>(options =>
            options.UseNpgsql(
                configuration.GetConnectionString("DefaultConnection"),
                b => b.MigrationsAssembly(typeof(OrderManagementDbContext).Assembly.FullName)));

        // Unit of Work
        services.AddScoped<IUnitOfWork, UnitOfWork>();

        // Repositories
        services.AddScoped<IRfqRepository, RfqRepository>();
        services.AddScoped<IRfqBidRepository, RfqBidRepository>();
        services.AddScoped<IRfqTimelineRepository, RfqTimelineRepository>();
        services.AddScoped<IRfqRoutingHistoryRepository, RfqRoutingHistoryRepository>();
        services.AddScoped<IRfqTagRepository, RfqTagRepository>();
        services.AddScoped<IMilestoneTemplateRepository, MilestoneTemplateRepository>();
        services.AddScoped<IRfqMilestoneAssignmentRepository, RfqMilestoneAssignmentRepository>();
        services.AddScoped<IBrokerCommentRepository, BrokerCommentRepository>();
        services.AddScoped<IAdministrativeAuditRepository, AdministrativeAuditRepository>();
        services.AddScoped<IAuthorizationService, AuthorizationService>();
        services.AddScoped<IOrderRepository, OrderRepository>();
        services.AddScoped<IInvoiceRepository, InvoiceRepository>();

        // Services
        services.AddScoped<IMessageBroker, MessageBroker>();

        // Configuration
        services.Configure<RfqExpirationOptions>(
            configuration.GetSection(RfqExpirationOptions.SectionName));
        services.Configure<RfqRoutingMonitorOptions>(
            configuration.GetSection(RfqRoutingMonitorOptions.SectionName));

        // Background Services
        services.AddHostedService<RfqExpirationMonitorService>();
        services.AddHostedService<RfqRoutingMonitorService>();

        return services;
    }
}
