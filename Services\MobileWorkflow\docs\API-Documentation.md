# Mobile & Workflow Service API Documentation

## Overview

The Mobile & Workflow Service provides comprehensive APIs for mobile application management, cross-platform synchronization, driver mobile features, and business process automation.

**Base URL**: `http://localhost:5014/api`  
**Authentication**: JWT Bearer Token  
**Content-Type**: `application/json`

## Authentication

All API endpoints require authentication using JWT Bearer tokens:

```http
Authorization: Bearer <your-jwt-token>
```

### Roles and Permissions

- **Admin**: Full access to all endpoints
- **Manager**: Access to workflow and business process management
- **Driver**: Access to driver mobile features
- **Carrier**: Access to carrier-specific mobile features
- **Developer**: Access to mobile app management (with Admin)

## Mobile Apps Management

### Get All Mobile Apps

```http
GET /api/mobileapps
```

**Query Parameters:**
- `platform` (string, optional): Filter by platform (Android, iOS, Web, PWA)
- `isActive` (boolean, optional): Filter by active status
- `supportsOffline` (boolean, optional): Filter by offline support

**Response:**
```json
[
  {
    "id": "uuid",
    "name": "TLI Driver App",
    "version": "1.0.0",
    "platform": "Android",
    "packageId": "com.tli.driver.android",
    "isActive": true,
    "releaseDate": "2024-01-15T10:00:00Z",
    "minimumOSVersion": "8.0",
    "supportsOffline": true,
    "downloadUrl": "https://play.google.com/store/apps/details?id=com.tli.driver",
    "fileSize": 25000000,
    "features": {
      "offline_mode": true,
      "gps_tracking": true,
      "camera_access": true
    }
  }
]
```

### Get Mobile App by ID

```http
GET /api/mobileapps/{id}
```

**Path Parameters:**
- `id` (uuid): Mobile app ID

### Get Mobile App by Package ID

```http
GET /api/mobileapps/package/{packageId}
```

**Path Parameters:**
- `packageId` (string): Package identifier

### Create Mobile App

```http
POST /api/mobileapps
```

**Required Roles:** Admin, Developer

**Request Body:**
```json
{
  "name": "TLI Driver App",
  "version": "1.0.0",
  "platform": "Android",
  "packageId": "com.tli.driver.android",
  "releaseDate": "2024-01-15T10:00:00Z",
  "minimumOSVersion": "8.0",
  "features": {
    "offline_mode": true,
    "gps_tracking": true
  },
  "configuration": {
    "sync_interval": 300
  },
  "supportsOffline": true,
  "downloadUrl": "https://example.com/app",
  "fileSize": 25000000,
  "checksum": "abc123def456"
}
```

### Get PWA Manifest

```http
GET /api/mobileapps/{id}/manifest
```

**Response:**
```json
{
  "name": "TLI Logistics Mobile",
  "short_name": "TLI Mobile",
  "description": "TLI Logistics mobile application",
  "start_url": "/",
  "display": "standalone",
  "background_color": "#ffffff",
  "theme_color": "#2196F3",
  "icons": [
    {
      "src": "/icons/icon-192x192.png",
      "sizes": "192x192",
      "type": "image/png"
    }
  ]
}
```

## Driver Mobile Features

### Get Driver Dashboard

```http
GET /api/drivermobile/dashboard
```

**Required Roles:** Driver, Carrier

**Response:**
```json
{
  "driverId": "uuid",
  "isOnline": true,
  "currentLocation": "12.9716,77.5946",
  "pendingTrips": [
    {
      "tripId": "uuid",
      "pickupLocation": "Bangalore Airport",
      "dropoffLocation": "MG Road",
      "pickupTime": "2024-01-15T14:00:00Z",
      "estimatedDistance": 25.5,
      "paymentAmount": 450.00,
      "customerName": "John Doe"
    }
  ],
  "todaysEarnings": 1250.00,
  "completedTrips": 8,
  "pendingSyncItems": 3,
  "lastSyncTime": "2024-01-15T13:45:00Z"
}
```

### Accept Trip Assignment

```http
POST /api/drivermobile/trip-assignments/{tripId}/accept
```

**Required Roles:** Driver, Carrier

**Response:**
```json
{
  "isSuccess": true,
  "tripId": "uuid",
  "status": "Accepted",
  "message": "Trip assignment accepted successfully"
}
```

### Reject Trip Assignment

```http
POST /api/drivermobile/trip-assignments/{tripId}/reject
```

**Request Body:**
```json
{
  "reason": "Vehicle maintenance required"
}
```

### Update Trip Status

```http
PUT /api/drivermobile/trips/{tripId}/status
```

**Request Body:**
```json
{
  "status": "InTransit",
  "statusData": {
    "location": "12.9716,77.5946",
    "timestamp": "2024-01-15T14:30:00Z",
    "notes": "On route to destination"
  }
}
```

### Update Driver Location

```http
POST /api/drivermobile/location
```

**Request Body:**
```json
{
  "latitude": 12.9716,
  "longitude": 77.5946,
  "accuracy": 5.0,
  "speed": 60.0,
  "heading": 180.0,
  "timestamp": "2024-01-15T14:30:00Z",
  "address": "MG Road, Bangalore"
}
```

### Upload Proof of Delivery (POD)

```http
POST /api/drivermobile/trips/{tripId}/pod
```

**Request Body:**
```json
{
  "customerName": "John Doe",
  "customerSignature": "base64-encoded-signature",
  "deliveryPhotos": [
    "base64-encoded-photo1",
    "base64-encoded-photo2"
  ],
  "deliveryNotes": "Package delivered successfully",
  "deliveryTime": "2024-01-15T15:00:00Z"
}
```

### Upload Document

```http
POST /api/drivermobile/documents
```

**Request Body:**
```json
{
  "documentType": "license",
  "fileName": "driver_license.pdf",
  "fileSize": 1024000,
  "fileData": "base64-encoded-file-data",
  "description": "Updated driver license"
}
```

## Workflow Management

### Get All Workflows

```http
GET /api/workflow
```

**Required Roles:** Admin, Manager

**Query Parameters:**
- `category` (string, optional): Filter by category
- `isActive` (boolean, optional): Filter by active status

### Create Workflow

```http
POST /api/workflow
```

**Required Roles:** Admin, Manager

**Request Body:**
```json
{
  "name": "Driver Onboarding",
  "description": "Complete workflow for onboarding new drivers",
  "category": "UserOnboarding",
  "version": "1.0",
  "definition": {
    "steps": [
      {
        "id": "collect_documents",
        "name": "Collect Documents",
        "type": "Manual",
        "parameters": {
          "required_docs": ["license", "aadhar", "pan"]
        }
      }
    ]
  },
  "triggerType": "Manual",
  "triggerConfiguration": {}
}
```

### Start Workflow Execution

```http
POST /api/workflow/{workflowId}/execute
```

**Request Body:**
```json
{
  "inputData": {
    "userId": "uuid",
    "priority": "high"
  },
  "triggerSource": "API"
}
```

### Get Running Executions

```http
GET /api/workflow/executions/running
```

**Required Roles:** Admin, Manager

### Cancel Workflow Execution

```http
POST /api/workflow/executions/{executionId}/cancel
```

**Request Body:**
```json
{
  "reason": "User requested cancellation"
}
```

## Business Process Automation

### Get Active Processes

```http
GET /api/businessprocess
```

**Required Roles:** Admin, Manager

### Get Process Templates

```http
GET /api/businessprocess/templates
```

**Query Parameters:**
- `category` (string): Template category (OrderProcessing, TripManagement, etc.)

**Response:**
```json
[
  {
    "id": "order-rfq-processing",
    "name": "RFQ Processing Workflow",
    "description": "Automated workflow for processing RFQ requests",
    "category": "OrderProcessing",
    "steps": [
      {
        "name": "Validate RFQ",
        "type": "Automated",
        "description": "Validate RFQ data and requirements"
      }
    ]
  }
]
```

### Create Business Process

```http
POST /api/businessprocess
```

**Request Body:**
```json
{
  "name": "Custom RFQ Process",
  "description": "Custom RFQ processing workflow",
  "category": "OrderProcessing",
  "version": "1.0",
  "steps": [
    {
      "name": "Validate Request",
      "type": "Automated",
      "parameters": {
        "validation_rules": ["required_fields", "data_format"]
      }
    }
  ],
  "triggerType": "Event",
  "triggerConfiguration": {
    "event": "rfq_created"
  },
  "createdBy": "admin"
}
```

### Trigger Business Process

```http
POST /api/businessprocess/{processName}/trigger
```

**Request Body:**
```json
{
  "rfqId": "uuid",
  "priority": "high",
  "requestedBy": "uuid"
}
```

### Get Automation Metrics

```http
GET /api/businessprocess/metrics
```

**Query Parameters:**
- `startDate` (datetime, optional): Start date for metrics
- `endDate` (datetime, optional): End date for metrics

**Response:**
```json
{
  "startDate": "2024-01-01T00:00:00Z",
  "endDate": "2024-01-31T23:59:59Z",
  "totalExecutions": 150,
  "successfulExecutions": 142,
  "failedExecutions": 8,
  "successRate": 94.67,
  "averageExecutionTime": "00:15:30",
  "processBreakdown": {
    "OrderProcessing": 75,
    "TripManagement": 45,
    "UserOnboarding": 30
  }
}
```

## Error Responses

All endpoints return consistent error responses:

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": [
      {
        "field": "name",
        "message": "Name is required"
      }
    ]
  },
  "timestamp": "2024-01-15T14:30:00Z",
  "path": "/api/mobileapps"
}
```

### Common HTTP Status Codes

- `200 OK`: Request successful
- `201 Created`: Resource created successfully
- `400 Bad Request`: Invalid request data
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `409 Conflict`: Resource already exists
- `422 Unprocessable Entity`: Validation errors
- `500 Internal Server Error`: Server error

## Rate Limiting

API endpoints are rate-limited:
- **Standard endpoints**: 100 requests per minute
- **Upload endpoints**: 10 requests per minute
- **Bulk operations**: 5 requests per minute

Rate limit headers are included in responses:
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1642248000
```

## Pagination

List endpoints support pagination:

**Query Parameters:**
- `page` (integer): Page number (default: 1)
- `pageSize` (integer): Items per page (default: 20, max: 100)

**Response Headers:**
```http
X-Total-Count: 150
X-Page-Count: 8
X-Current-Page: 1
X-Per-Page: 20
```

## Webhooks

The service supports webhooks for real-time notifications:

### Workflow Events
- `workflow.execution.started`
- `workflow.execution.completed`
- `workflow.execution.failed`
- `workflow.task.assigned`
- `workflow.task.completed`

### Mobile Events
- `mobile.session.started`
- `mobile.session.ended`
- `mobile.sync.completed`
- `mobile.offline.data.created`

### Driver Events
- `driver.trip.accepted`
- `driver.trip.rejected`
- `driver.location.updated`
- `driver.pod.uploaded`

**Webhook Payload Example:**
```json
{
  "event": "workflow.execution.completed",
  "timestamp": "2024-01-15T14:30:00Z",
  "data": {
    "executionId": "uuid",
    "workflowId": "uuid",
    "status": "Completed",
    "duration": "00:15:30"
  }
}
```
