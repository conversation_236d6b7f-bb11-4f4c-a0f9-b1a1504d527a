using Microsoft.EntityFrameworkCore;
using TripManagement.Application.Interfaces;
using TripManagement.Domain.Entities;
using TripManagement.Domain.Enums;
using TripManagement.Infrastructure.Persistence;

namespace TripManagement.Infrastructure.Repositories;

public class VehicleRepository : IVehicleRepository
{
    private readonly TripManagementDbContext _context;

    public VehicleRepository(TripManagementDbContext context)
    {
        _context = context;
    }

    public async Task<Vehicle?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.Vehicles
            .Include(v => v.Documents)
            .FirstOrDefaultAsync(v => v.Id == id, cancellationToken);
    }

    public async Task<Vehicle?> GetByRegistrationNumberAsync(string registrationNumber, CancellationToken cancellationToken = default)
    {
        return await _context.Vehicles
            .Include(v => v.Documents)
            .FirstOrDefaultAsync(v => v.RegistrationNumber == registrationNumber, cancellationToken);
    }

    public async Task<List<Vehicle>> GetAvailableVehiclesAsync(CancellationToken cancellationToken = default)
    {
        return await _context.Vehicles
            .Where(v => v.Status == VehicleStatus.Available && 
                       (!v.InsuranceExpiryDate.HasValue || v.InsuranceExpiryDate.Value > DateTime.UtcNow) &&
                       (!v.FitnessExpiryDate.HasValue || v.FitnessExpiryDate.Value > DateTime.UtcNow))
            .OrderBy(v => v.RegistrationNumber)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<Vehicle>> GetByCarrierIdAsync(Guid carrierId, CancellationToken cancellationToken = default)
    {
        return await _context.Vehicles
            .Where(v => v.CarrierId == carrierId)
            .OrderBy(v => v.RegistrationNumber)
            .ToListAsync(cancellationToken);
    }

    public async Task AddAsync(Vehicle vehicle, CancellationToken cancellationToken = default)
    {
        await _context.Vehicles.AddAsync(vehicle, cancellationToken);
    }

    public void Update(Vehicle vehicle)
    {
        _context.Vehicles.Update(vehicle);
    }

    public void Delete(Vehicle vehicle)
    {
        _context.Vehicles.Remove(vehicle);
    }
}
