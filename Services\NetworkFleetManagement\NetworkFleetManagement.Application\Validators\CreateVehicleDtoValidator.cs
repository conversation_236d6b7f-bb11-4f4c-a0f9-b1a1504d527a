using FluentValidation;
using NetworkFleetManagement.Application.DTOs;

namespace NetworkFleetManagement.Application.Validators;

public class CreateVehicleDtoValidator : AbstractValidator<CreateVehicleDto>
{
    public CreateVehicleDtoValidator()
    {
        RuleFor(x => x.CarrierId)
            .NotEmpty()
            .WithMessage("Carrier ID is required");

        RuleFor(x => x.RegistrationNumber)
            .NotEmpty()
            .WithMessage("Registration number is required")
            .MaximumLength(20)
            .WithMessage("Registration number cannot exceed 20 characters");

        RuleFor(x => x.VehicleType)
            .IsInEnum()
            .WithMessage("Invalid vehicle type");

        RuleFor(x => x.Make)
            .NotEmpty()
            .WithMessage("Make is required")
            .MaximumLength(50)
            .WithMessage("Make cannot exceed 50 characters");

        RuleFor(x => x.Model)
            .NotEmpty()
            .WithMessage("Model is required")
            .MaximumLength(50)
            .WithMessage("Model cannot exceed 50 characters");

        RuleFor(x => x.Year)
            .InclusiveBetween(1900, DateTime.UtcNow.Year + 1)
            .WithMessage($"Year must be between 1900 and {DateTime.UtcNow.Year + 1}");

        RuleFor(x => x.Color)
            .NotEmpty()
            .WithMessage("Color is required")
            .MaximumLength(30)
            .WithMessage("Color cannot exceed 30 characters");

        RuleFor(x => x.Specifications)
            .NotNull()
            .WithMessage("Vehicle specifications are required")
            .SetValidator(new VehicleSpecificationsDtoValidator());

        RuleFor(x => x.InsuranceNumber)
            .MaximumLength(50)
            .WithMessage("Insurance number cannot exceed 50 characters")
            .When(x => !string.IsNullOrEmpty(x.InsuranceNumber));

        RuleFor(x => x.InsuranceExpiryDate)
            .GreaterThan(DateTime.UtcNow)
            .WithMessage("Insurance expiry date must be in the future")
            .When(x => x.InsuranceExpiryDate.HasValue);

        RuleFor(x => x.FitnessNumber)
            .MaximumLength(50)
            .WithMessage("Fitness number cannot exceed 50 characters")
            .When(x => !string.IsNullOrEmpty(x.FitnessNumber));

        RuleFor(x => x.FitnessExpiryDate)
            .GreaterThan(DateTime.UtcNow)
            .WithMessage("Fitness expiry date must be in the future")
            .When(x => x.FitnessExpiryDate.HasValue);

        RuleFor(x => x.PermitNumber)
            .MaximumLength(50)
            .WithMessage("Permit number cannot exceed 50 characters")
            .When(x => !string.IsNullOrEmpty(x.PermitNumber));

        RuleFor(x => x.PermitExpiryDate)
            .GreaterThan(DateTime.UtcNow)
            .WithMessage("Permit expiry date must be in the future")
            .When(x => x.PermitExpiryDate.HasValue);

        RuleFor(x => x.Notes)
            .MaximumLength(1000)
            .WithMessage("Notes cannot exceed 1000 characters")
            .When(x => !string.IsNullOrEmpty(x.Notes));
    }
}

public class VehicleSpecificationsDtoValidator : AbstractValidator<VehicleSpecificationsDto>
{
    public VehicleSpecificationsDtoValidator()
    {
        RuleFor(x => x.LoadCapacityKg)
            .GreaterThan(0)
            .WithMessage("Load capacity must be greater than zero");

        RuleFor(x => x.VolumeCapacityM3)
            .GreaterThan(0)
            .WithMessage("Volume capacity must be greater than zero");

        RuleFor(x => x.FuelCapacityLiters)
            .GreaterThan(0)
            .WithMessage("Fuel capacity must be greater than zero")
            .When(x => x.FuelCapacityLiters.HasValue);

        RuleFor(x => x.MaxSpeed)
            .GreaterThan(0)
            .WithMessage("Max speed must be greater than zero")
            .When(x => x.MaxSpeed.HasValue);

        RuleFor(x => x.Length)
            .GreaterThan(0)
            .WithMessage("Length must be greater than zero")
            .When(x => x.Length.HasValue);

        RuleFor(x => x.Width)
            .GreaterThan(0)
            .WithMessage("Width must be greater than zero")
            .When(x => x.Width.HasValue);

        RuleFor(x => x.Height)
            .GreaterThan(0)
            .WithMessage("Height must be greater than zero")
            .When(x => x.Height.HasValue);

        RuleFor(x => x.EngineType)
            .MaximumLength(50)
            .WithMessage("Engine type cannot exceed 50 characters")
            .When(x => !string.IsNullOrEmpty(x.EngineType));

        RuleFor(x => x.FuelType)
            .MaximumLength(30)
            .WithMessage("Fuel type cannot exceed 30 characters")
            .When(x => !string.IsNullOrEmpty(x.FuelType));
    }
}
