{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"DefaultConnection": "Host=localhost;Database=tli_microservices;Username=********;Password=********"}, "RabbitMQ": {"Host": "localhost"}, "JwtSettings": {"Secret": "your-super-secret-jwt-key-that-is-at-least-32-characters-long", "Issuer": "TLI.Identity", "Audience": "TLI.Services", "ExpiryMinutes": 60}, "AllowedHosts": "*"}