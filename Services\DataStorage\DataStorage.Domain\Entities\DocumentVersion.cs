using Shared.Domain.Common;
using DataStorage.Domain.ValueObjects;
using DataStorage.Domain.Enums;

namespace DataStorage.Domain.Entities;

public class DocumentVersion : BaseEntity
{
    public Guid DocumentId { get; private set; }
    public int VersionNumber { get; private set; }
    public FileMetadata FileMetadata { get; private set; }
    public StorageLocation StorageLocation { get; private set; }
    public string? ChangeDescription { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public Guid CreatedBy { get; private set; }
    public bool IsActive { get; private set; }

    private DocumentVersion()
    {
        FileMetadata = null!;
        StorageLocation = null!;
    }

    public DocumentVersion(
        Guid documentId,
        int versionNumber,
        FileMetadata fileMetadata,
        StorageLocation storageLocation,
        string? changeDescription = null,
        Guid? createdBy = null)
    {
        Id = Guid.NewGuid();
        DocumentId = documentId;
        VersionNumber = versionNumber;
        FileMetadata = fileMetadata ?? throw new ArgumentNullException(nameof(fileMetadata));
        StorageLocation = storageLocation ?? throw new ArgumentNullException(nameof(storageLocation));
        ChangeDescription = changeDescription;
        CreatedAt = DateTime.UtcNow;
        CreatedBy = createdBy ?? Guid.Empty;
        IsActive = true;
    }

    public void Deactivate()
    {
        IsActive = false;
    }

    public void Activate()
    {
        IsActive = true;
    }
}

public class DocumentAccess : BaseEntity
{
    public Guid DocumentId { get; private set; }
    public Guid UserId { get; private set; }
    public string UserType { get; private set; }
    public AccessLevel AccessLevel { get; private set; }
    public DateTime GrantedAt { get; private set; }
    public DateTime? ExpiresAt { get; private set; }
    public bool IsActive => ExpiresAt == null || ExpiresAt > DateTime.UtcNow;

    private DocumentAccess()
    {
        UserType = string.Empty;
    }

    public DocumentAccess(
        Guid documentId,
        Guid userId,
        string userType,
        AccessLevel accessLevel,
        DateTime? expiresAt = null)
    {
        if (string.IsNullOrWhiteSpace(userType))
            throw new ArgumentException("User type cannot be empty", nameof(userType));

        Id = Guid.NewGuid();
        DocumentId = documentId;
        UserId = userId;
        UserType = userType;
        AccessLevel = accessLevel;
        GrantedAt = DateTime.UtcNow;
        ExpiresAt = expiresAt;
    }

    public void UpdateAccess(AccessLevel newAccessLevel, DateTime? newExpiresAt = null)
    {
        AccessLevel = newAccessLevel;
        ExpiresAt = newExpiresAt;
    }

    public void ExtendAccess(DateTime newExpiresAt)
    {
        if (newExpiresAt <= DateTime.UtcNow)
            throw new ArgumentException("New expiry date must be in the future", nameof(newExpiresAt));

        ExpiresAt = newExpiresAt;
    }

    public void RevokeAccess()
    {
        ExpiresAt = DateTime.UtcNow.AddMinutes(-1);
    }
}
