using Shared.Domain.Common;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Events;
using SubscriptionManagement.Domain.Exceptions;
using SubscriptionManagement.Domain.ValueObjects;

namespace SubscriptionManagement.Domain.Entities
{
    public class Subscription : AggregateRoot
    {
        public Guid UserId { get; private set; }
        public Guid PlanId { get; private set; }
        public SubscriptionStatus Status { get; private set; }
        public DateTime StartDate { get; private set; }
        public DateTime? EndDate { get; private set; }
        public DateTime NextBillingDate { get; private set; }
        public DateTime? TrialEndDate { get; private set; }
        public DateTime? CancelledAt { get; private set; }
        public string? CancellationReason { get; private set; }
        public bool AutoRenew { get; private set; }
        public Money CurrentPrice { get; private set; }
        public BillingCycle BillingCycle { get; private set; }
        public ProrationMode ProrationMode { get; private set; }

        // Grace Period Extension Properties
        public DateTime? GracePeriodEndDate { get; private set; }
        public DateTime? LastExtendedAt { get; private set; }
        public string? ExtensionReason { get; private set; }
        public Guid? ExtendedByUserId { get; private set; }

        // Navigation properties
        public Plan Plan { get; private set; }

        private readonly List<Payment> _payments = new();
        public IReadOnlyCollection<Payment> Payments => _payments.AsReadOnly();

        private readonly List<SubscriptionChange> _changes = new();
        public IReadOnlyCollection<SubscriptionChange> Changes => _changes.AsReadOnly();

        private Subscription() { }

        public Subscription(Guid userId, Plan plan, DateTime? startDate = null,
            DateTime? trialEndDate = null, bool autoRenew = true, ProrationMode prorationMode = ProrationMode.CreateProrations)
        {
            if (plan == null)
                throw new SubscriptionDomainException("Plan cannot be null");

            if (!plan.IsActive)
                throw new SubscriptionDomainException("Cannot create subscription for inactive plan");

            UserId = userId;
            PlanId = plan.Id;
            Plan = plan;
            Status = SubscriptionStatus.Pending;
            StartDate = startDate ?? DateTime.UtcNow;
            TrialEndDate = trialEndDate;
            AutoRenew = autoRenew;
            CurrentPrice = plan.Price;
            BillingCycle = plan.BillingCycle;
            ProrationMode = prorationMode;

            // Calculate next billing date
            if (TrialEndDate.HasValue && TrialEndDate.Value > StartDate)
            {
                NextBillingDate = TrialEndDate.Value;
            }
            else
            {
                NextBillingDate = BillingCycle.CalculateNextBillingDate(StartDate);
            }

            AddDomainEvent(new SubscriptionCreatedEvent(Id, userId, plan.Id, StartDate, NextBillingDate,
                CurrentPrice.Amount, CurrentPrice.Currency));
        }

        public void Activate()
        {
            if (Status != SubscriptionStatus.Pending)
                throw new SubscriptionDomainException("Only pending subscriptions can be activated");

            Status = SubscriptionStatus.Active;
            UpdatedAt = DateTime.UtcNow;

            AddDomainEvent(new SubscriptionActivatedEvent(Id, UserId, DateTime.UtcNow));
        }

        public void Cancel(string? reason = null, bool immediateCancellation = false)
        {
            if (Status == SubscriptionStatus.Cancelled)
                throw new SubscriptionDomainException("Subscription is already cancelled");

            if (Status == SubscriptionStatus.Expired)
                throw new SubscriptionDomainException("Cannot cancel expired subscription");

            var cancelDate = DateTime.UtcNow;

            Status = SubscriptionStatus.Cancelled;
            CancelledAt = cancelDate;
            CancellationReason = reason;
            AutoRenew = false;

            // If immediate cancellation, set end date to now, otherwise let it run until next billing
            if (immediateCancellation)
            {
                EndDate = cancelDate;
            }
            else if (!EndDate.HasValue)
            {
                EndDate = NextBillingDate;
            }

            UpdatedAt = DateTime.UtcNow;

            AddDomainEvent(new SubscriptionCancelledEvent(Id, UserId, cancelDate, reason));
        }

        public void Suspend(string? reason = null)
        {
            if (Status != SubscriptionStatus.Active)
                throw new SubscriptionDomainException("Only active subscriptions can be suspended");

            Status = SubscriptionStatus.Suspended;
            UpdatedAt = DateTime.UtcNow;

            // Record the suspension as a change
            RecordChange("Suspended", reason);
        }

        public void Resume()
        {
            if (Status != SubscriptionStatus.Suspended)
                throw new SubscriptionDomainException("Only suspended subscriptions can be resumed");

            Status = SubscriptionStatus.Active;
            UpdatedAt = DateTime.UtcNow;

            RecordChange("Resumed", "Subscription resumed");
        }

        public void UpgradePlan(Plan newPlan, DateTime? effectiveDate = null)
        {
            if (newPlan == null)
                throw new SubscriptionDomainException("New plan cannot be null");

            if (!newPlan.IsActive)
                throw new SubscriptionDomainException("Cannot upgrade to inactive plan");

            if (!newPlan.IsCompatibleWith(Plan.UserType))
                throw new SubscriptionDomainException("New plan is not compatible with current user type");

            if (!newPlan.IsUpgradeFrom(Plan))
                throw new SubscriptionDomainException("New plan is not an upgrade from current plan");

            var upgradeDate = effectiveDate ?? DateTime.UtcNow;
            var prorationAmount = Money.Zero(CurrentPrice.Currency);

            if (ProrationMode == ProrationMode.CreateProrations)
            {
                prorationAmount = newPlan.CalculateProrationAmount(upgradeDate, NextBillingDate, CurrentPrice);
            }

            var oldPlanId = PlanId;
            PlanId = newPlan.Id;
            Plan = newPlan;
            CurrentPrice = newPlan.Price;
            BillingCycle = newPlan.BillingCycle;
            UpdatedAt = DateTime.UtcNow;

            RecordChange("Upgraded", $"Upgraded from {Plan.Name} to {newPlan.Name}");

            AddDomainEvent(new SubscriptionUpgradedEvent(Id, UserId, oldPlanId, newPlan.Id,
                upgradeDate, prorationAmount.Amount, prorationAmount.Currency));
        }

        public void DowngradePlan(Plan newPlan, DateTime? effectiveDate = null)
        {
            if (newPlan == null)
                throw new SubscriptionDomainException("New plan cannot be null");

            if (!newPlan.IsActive)
                throw new SubscriptionDomainException("Cannot downgrade to inactive plan");

            if (!newPlan.IsCompatibleWith(Plan.UserType))
                throw new SubscriptionDomainException("New plan is not compatible with current user type");

            if (!newPlan.IsDowngradeFrom(Plan))
                throw new SubscriptionDomainException("New plan is not a downgrade from current plan");

            var downgradeDate = effectiveDate ?? DateTime.UtcNow;
            var refundAmount = Money.Zero(CurrentPrice.Currency);

            if (ProrationMode == ProrationMode.CreateProrations)
            {
                refundAmount = CurrentPrice.Subtract(newPlan.CalculateProrationAmount(downgradeDate, NextBillingDate, CurrentPrice));
            }

            var oldPlanId = PlanId;
            PlanId = newPlan.Id;
            Plan = newPlan;
            CurrentPrice = newPlan.Price;
            BillingCycle = newPlan.BillingCycle;
            UpdatedAt = DateTime.UtcNow;

            RecordChange("Downgraded", $"Downgraded from {Plan.Name} to {newPlan.Name}");

            AddDomainEvent(new SubscriptionDowngradedEvent(Id, UserId, oldPlanId, newPlan.Id,
                downgradeDate, refundAmount.Amount, refundAmount.Currency));
        }

        public void Renew()
        {
            if (Status != SubscriptionStatus.Active)
                throw new SubscriptionDomainException("Only active subscriptions can be renewed");

            if (!AutoRenew)
                throw new SubscriptionDomainException("Auto-renewal is disabled for this subscription");

            var renewalDate = DateTime.UtcNow;
            NextBillingDate = BillingCycle.CalculateNextBillingDate(NextBillingDate);
            UpdatedAt = DateTime.UtcNow;

            RecordChange("Renewed", "Subscription renewed");

            AddDomainEvent(new SubscriptionRenewedEvent(Id, UserId, renewalDate, NextBillingDate,
                CurrentPrice.Amount, CurrentPrice.Currency));
        }

        public void AddPayment(Payment payment)
        {
            if (payment == null)
                throw new SubscriptionDomainException("Payment cannot be null");

            _payments.Add(payment);
            UpdatedAt = DateTime.UtcNow;
        }

        public bool IsInTrial()
        {
            return TrialEndDate.HasValue && DateTime.UtcNow < TrialEndDate.Value;
        }

        public bool IsExpired()
        {
            return Status == SubscriptionStatus.Expired ||
                   (EndDate.HasValue && DateTime.UtcNow > EndDate.Value);
        }

        public bool IsDueForRenewal()
        {
            return Status == SubscriptionStatus.Active &&
                   DateTime.UtcNow >= NextBillingDate &&
                   AutoRenew;
        }

        public int GetDaysUntilRenewal()
        {
            if (Status != SubscriptionStatus.Active)
                return 0;

            return Math.Max(0, (NextBillingDate - DateTime.UtcNow).Days);
        }

        public bool IsInGracePeriod()
        {
            return GracePeriodEndDate.HasValue && DateTime.UtcNow <= GracePeriodEndDate.Value;
        }

        public bool IsExpiredWithoutGrace()
        {
            return IsExpired() && !IsInGracePeriod();
        }

        public int GetDaysRemainingInGracePeriod()
        {
            if (!IsInGracePeriod())
                return 0;

            return Math.Max(0, (GracePeriodEndDate!.Value - DateTime.UtcNow).Days);
        }

        public Money GetTotalPaid()
        {
            var successfulPayments = _payments.Where(p => p.Status == PaymentStatus.Completed);
            if (!successfulPayments.Any())
                return Money.Zero(CurrentPrice.Currency);

            return successfulPayments.Aggregate(Money.Zero(CurrentPrice.Currency),
                (total, payment) => total.Add(payment.Amount));
        }

        public Money CalculateProrationRefund()
        {
            if (Status != SubscriptionStatus.Active && Status != SubscriptionStatus.Cancelled)
            {
                return Money.Create(0, CurrentPrice.Currency);
            }

            var now = DateTime.UtcNow;
            var billingPeriodStart = GetCurrentBillingPeriodStart();
            var billingPeriodEnd = NextBillingDate;

            // Calculate total days in billing period
            var totalDays = (billingPeriodEnd - billingPeriodStart).TotalDays;

            // Calculate remaining days
            var remainingDays = Math.Max(0, (billingPeriodEnd - now).TotalDays);

            // Calculate prorated refund amount
            var refundPercentage = remainingDays / totalDays;
            var refundAmount = CurrentPrice.Amount * (decimal)refundPercentage;

            return Money.Create(Math.Max(0, refundAmount), CurrentPrice.Currency);
        }

        private DateTime GetCurrentBillingPeriodStart()
        {
            var now = DateTime.UtcNow;
            var intervalCount = BillingCycle.IntervalCount;

            return BillingCycle.Interval switch
            {
                BillingInterval.Weekly => NextBillingDate.AddDays(-7 * intervalCount),
                BillingInterval.BiWeekly => NextBillingDate.AddDays(-14 * intervalCount),
                BillingInterval.Monthly => NextBillingDate.AddMonths(-intervalCount),
                BillingInterval.Quarterly => NextBillingDate.AddMonths(-3 * intervalCount),
                BillingInterval.SemiAnnually => NextBillingDate.AddMonths(-6 * intervalCount),
                BillingInterval.Annually => NextBillingDate.AddYears(-intervalCount),
                BillingInterval.Custom => NextBillingDate.AddMonths(-intervalCount), // Default to monthly for custom
                _ => NextBillingDate.AddMonths(-1)
            };
        }

        private void RecordChange(string changeType, string? description = null)
        {
            var change = new SubscriptionChange(changeType, description);
            _changes.Add(change);
        }

        // Additional methods for new command handlers
        public void UpgradeToPlan(Plan newPlan, bool immediateUpgrade, ProrationMode prorationMode)
        {
            if (newPlan == null)
                throw new SubscriptionDomainException("New plan cannot be null");

            if (!newPlan.IsActive)
                throw new SubscriptionDomainException("Cannot upgrade to inactive plan");

            var oldPlanId = PlanId;
            PlanId = newPlan.Id;
            Plan = newPlan;
            CurrentPrice = newPlan.Price;
            BillingCycle = newPlan.BillingCycle;
            ProrationMode = prorationMode;

            if (immediateUpgrade)
            {
                // Recalculate next billing date based on new plan
                NextBillingDate = BillingCycle.CalculateNextBillingDate(DateTime.UtcNow);
            }

            UpdatedAt = DateTime.UtcNow;
            RecordChange("Upgraded", $"Upgraded to {newPlan.Name}");
        }

        public void DowngradeToPlan(Plan newPlan, bool immediateDowngrade, ProrationMode prorationMode)
        {
            if (newPlan == null)
                throw new SubscriptionDomainException("New plan cannot be null");

            if (!newPlan.IsActive)
                throw new SubscriptionDomainException("Cannot downgrade to inactive plan");

            var oldPlanId = PlanId;
            PlanId = newPlan.Id;
            Plan = newPlan;
            CurrentPrice = newPlan.Price;
            BillingCycle = newPlan.BillingCycle;
            ProrationMode = prorationMode;

            if (immediateDowngrade)
            {
                // Recalculate next billing date based on new plan
                NextBillingDate = BillingCycle.CalculateNextBillingDate(DateTime.UtcNow);
            }

            UpdatedAt = DateTime.UtcNow;
            RecordChange("Downgraded", $"Downgraded to {newPlan.Name}");
        }

        public void Pause(DateTime? pauseUntil, string? reason)
        {
            if (Status != SubscriptionStatus.Active)
                throw new SubscriptionDomainException("Only active subscriptions can be paused");

            Status = SubscriptionStatus.Suspended;
            UpdatedAt = DateTime.UtcNow;

            var pauseDescription = pauseUntil.HasValue
                ? $"Paused until {pauseUntil.Value:yyyy-MM-dd}"
                : "Paused indefinitely";

            if (!string.IsNullOrEmpty(reason))
                pauseDescription += $". Reason: {reason}";

            RecordChange("Paused", pauseDescription);
        }

        public void Resume(string? reason = null)
        {
            if (Status != SubscriptionStatus.Suspended)
                throw new SubscriptionDomainException("Only suspended subscriptions can be resumed");

            Status = SubscriptionStatus.Active;
            UpdatedAt = DateTime.UtcNow;

            var resumeDescription = "Subscription resumed";
            if (!string.IsNullOrEmpty(reason))
                resumeDescription += $". Reason: {reason}";

            RecordChange("Resumed", resumeDescription);
        }

        public string? PaymentMethodId { get; private set; }

        public void UpdatePaymentMethod(string newPaymentMethodId, string? reason = null)
        {
            if (string.IsNullOrEmpty(newPaymentMethodId))
                throw new SubscriptionDomainException("Payment method ID cannot be null or empty");

            var oldPaymentMethodId = PaymentMethodId;
            PaymentMethodId = newPaymentMethodId;
            UpdatedAt = DateTime.UtcNow;

            var updateDescription = "Payment method updated";
            if (!string.IsNullOrEmpty(reason))
                updateDescription += $". Reason: {reason}";

            RecordChange("PaymentMethodUpdated", updateDescription);
        }

        public void ExtendSubscription(int extensionDays, string? reason, Guid extendedByUserId, bool applyAsGracePeriod = false)
        {
            if (extensionDays <= 0)
                throw new SubscriptionDomainException("Extension days must be greater than 0");

            if (extensionDays > 90) // Maximum 90 days extension
                throw new SubscriptionDomainException("Extension cannot exceed 90 days");

            var extensionDate = DateTime.UtcNow;
            var originalEndDate = EndDate;
            var originalNextBillingDate = NextBillingDate;
            var originalGracePeriodEndDate = GracePeriodEndDate;

            if (applyAsGracePeriod)
            {
                // Apply as grace period - doesn't affect billing cycle
                if (IsExpired() || Status == SubscriptionStatus.Expired)
                {
                    // For expired subscriptions, set grace period from expiry date
                    var baseDate = EndDate ?? NextBillingDate;
                    GracePeriodEndDate = baseDate.AddDays(extensionDays);
                }
                else
                {
                    // For active subscriptions, set grace period from next billing date
                    GracePeriodEndDate = NextBillingDate.AddDays(extensionDays);
                }
            }
            else
            {
                // Regular extension - moves the billing cycle forward
                if (Status == SubscriptionStatus.Expired || IsExpired())
                {
                    // Reactivate expired subscription
                    Status = SubscriptionStatus.Active;
                    EndDate = null;
                }

                // Extend the next billing date
                NextBillingDate = NextBillingDate.AddDays(extensionDays);

                // Clear any existing grace period since we're extending the actual subscription
                GracePeriodEndDate = null;
            }

            // Update extension tracking
            LastExtendedAt = extensionDate;
            ExtensionReason = reason;
            ExtendedByUserId = extendedByUserId;
            UpdatedAt = DateTime.UtcNow;

            // Record the change
            var changeDescription = applyAsGracePeriod
                ? $"Grace period of {extensionDays} days applied"
                : $"Subscription extended by {extensionDays} days";

            if (!string.IsNullOrEmpty(reason))
                changeDescription += $". Reason: {reason}";

            RecordChange("Extended", changeDescription);

            // Publish domain event
            AddDomainEvent(new SubscriptionExtendedEvent(
                Id,
                UserId,
                extensionDays,
                reason,
                extendedByUserId,
                applyAsGracePeriod,
                originalEndDate,
                originalNextBillingDate,
                originalGracePeriodEndDate,
                EndDate,
                NextBillingDate,
                GracePeriodEndDate,
                extensionDate));
        }

        public void ExtendBillingPeriod(DateTime newEndDate)
        {
            if (newEndDate <= DateTime.UtcNow)
                throw new SubscriptionDomainException("New end date must be in the future");

            // If subscription is expired, reactivate it
            if (Status == SubscriptionStatus.Expired || IsExpired())
            {
                Status = SubscriptionStatus.Active;
                EndDate = null;
            }

            // Update next billing date
            NextBillingDate = newEndDate;
            UpdatedAt = DateTime.UtcNow;

            // Clear any grace period since we're extending the actual subscription
            GracePeriodEndDate = null;

            RecordChange("BillingExtended", $"Billing period extended to {newEndDate:yyyy-MM-dd}");
        }

        public void ProcessBilling(DateTime billingDate)
        {
            if (Status != SubscriptionStatus.Active)
                throw new SubscriptionDomainException("Only active subscriptions can be billed");

            // Update next billing date
            NextBillingDate = BillingCycle.CalculateNextBillingDate(billingDate);
            UpdatedAt = DateTime.UtcNow;

            RecordChange("Billed", $"Billing processed for {billingDate:yyyy-MM-dd}");
        }
    }
}
