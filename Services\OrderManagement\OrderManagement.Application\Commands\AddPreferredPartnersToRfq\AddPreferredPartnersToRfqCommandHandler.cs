using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Entities;

namespace OrderManagement.Application.Commands.AddPreferredPartnersToRfq;

public class AddPreferredPartnersToRfqCommandHandler : IRequestHandler<AddPreferredPartnersToRfqCommand, bool>
{
    private readonly IRfqRepository _rfqRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<AddPreferredPartnersToRfqCommandHandler> _logger;

    public AddPreferredPartnersToRfqCommandHandler(
        IRfqRepository rfqRepository,
        IUnitOfWork unitOfWork,
        ILogger<AddPreferredPartnersToRfqCommandHandler> logger)
    {
        _rfqRepository = rfqRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<bool> Handle(AddPreferredPartnersToRfqCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Adding preferred partners to RFQ {RfqId} by user {UserId}", 
            request.RfqId, request.RequestingUserId);

        try
        {
            // Get the RFQ
            var rfq = await _rfqRepository.GetByIdAsync(request.RfqId, cancellationToken);
            if (rfq == null)
            {
                _logger.LogWarning("RFQ {RfqId} not found", request.RfqId);
                throw new ArgumentException($"RFQ {request.RfqId} not found");
            }

            // Validate user has permission to modify this RFQ
            if (rfq.TransportCompanyId != request.RequestingUserId)
            {
                _logger.LogWarning("User {UserId} does not have permission to modify RFQ {RfqId}", 
                    request.RequestingUserId, request.RfqId);
                throw new UnauthorizedAccessException("You do not have permission to modify this RFQ");
            }

            // Validate RFQ is in a state that allows modification
            if (rfq.Status != Domain.Enums.RfqStatus.Draft && rfq.Status != Domain.Enums.RfqStatus.Published)
            {
                _logger.LogWarning("Cannot add preferred partners to RFQ {RfqId} with status {Status}", 
                    request.RfqId, rfq.Status);
                throw new InvalidOperationException($"Cannot add preferred partners to RFQ with status {rfq.Status}");
            }

            // Create preferred partner entities
            foreach (var partnerRequest in request.PreferredPartners)
            {
                var preferredPartner = new PreferredPartner(
                    request.RfqId,
                    partnerRequest.PartnerId,
                    partnerRequest.PartnerType,
                    partnerRequest.PartnerName,
                    partnerRequest.CompanyName,
                    partnerRequest.Email,
                    partnerRequest.PhoneNumber,
                    partnerRequest.PreferenceRating,
                    request.RequestingUserId,
                    partnerRequest.Notes,
                    partnerRequest.Priority);

                // Add service areas
                foreach (var serviceArea in partnerRequest.ServiceAreas)
                {
                    preferredPartner.AddServiceArea(serviceArea);
                }

                // Add vehicle types
                foreach (var vehicleType in partnerRequest.VehicleTypes)
                {
                    preferredPartner.AddVehicleType(vehicleType);
                }

                rfq.AddPreferredPartner(preferredPartner);
            }

            // Save changes
            _rfqRepository.Update(rfq);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Successfully added {Count} preferred partners to RFQ {RfqId}", 
                request.PreferredPartners.Count, request.RfqId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding preferred partners to RFQ {RfqId}", request.RfqId);
            throw;
        }
    }
}
