using Shared.Domain.Common;

namespace MobileWorkflow.Domain.Entities;

/// <summary>
/// Entity representing workflow template ratings
/// </summary>
public class WorkflowTemplateRating : BaseEntity
{
    public Guid TemplateId { get; private set; }
    public Guid UserId { get; private set; }
    public int Rating { get; private set; } // 1-5 stars
    public string? Comment { get; private set; }
    public DateTime RatedAt { get; private set; }
    public bool IsVerified { get; private set; }
    public string? UserRole { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    public virtual WorkflowTemplate Template { get; private set; } = null!;

    private WorkflowTemplateRating() 
    {
        Metadata = new Dictionary<string, object>();
    } // EF Core

    public WorkflowTemplateRating(
        Guid templateId,
        Guid userId,
        int rating,
        string? comment = null,
        string? userRole = null,
        Dictionary<string, object>? metadata = null)
    {
        TemplateId = templateId;
        UserId = userId;
        Rating = rating;
        Comment = comment;
        RatedAt = DateTime.UtcNow;
        IsVerified = false;
        UserRole = userRole;
        Metadata = metadata ?? new Dictionary<string, object>();

        ValidateRating();
    }

    public void UpdateRating(int newRating, string? newComment = null)
    {
        Rating = newRating;
        Comment = newComment;
        RatedAt = DateTime.UtcNow;
        
        ValidateRating();
    }

    public void VerifyRating()
    {
        IsVerified = true;
    }

    public void UnverifyRating()
    {
        IsVerified = false;
    }

    public void UpdateMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    private void ValidateRating()
    {
        if (Rating < 1 || Rating > 5)
        {
            throw new ArgumentException("Rating must be between 1 and 5", nameof(Rating));
        }
    }

    public bool IsPositiveRating() => Rating >= 4;
    public bool IsNegativeRating() => Rating <= 2;
    public bool IsNeutralRating() => Rating == 3;
}
