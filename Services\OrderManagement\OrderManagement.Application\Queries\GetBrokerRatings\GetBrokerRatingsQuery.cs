using MediatR;
using OrderManagement.Application.DTOs;

namespace OrderManagement.Application.Queries.GetBrokerRatings;

public class GetBrokerRatingsQuery : IRequest<List<BrokerRatingDto>>
{
    public Guid RfqId { get; set; }
    public Guid RequestingUserId { get; set; }
    public List<string> ServiceAreas { get; set; } = new();
    public List<string> Specializations { get; set; } = new();
    public decimal? MinRating { get; set; }
    public int? MaxResults { get; set; } = 50;
    public bool OnlyPreferredPartners { get; set; } = false;
    public string? SortBy { get; set; } = "OverallRating"; // OverallRating, MatchSuccessRate, TotalRfqs, LastRfqDate
    public bool SortDescending { get; set; } = true;
}
