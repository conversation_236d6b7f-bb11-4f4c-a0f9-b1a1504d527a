using SubscriptionManagement.Domain.Entities;

namespace SubscriptionManagement.Application.Interfaces
{
    public interface IInvoiceService
    {
        /// <summary>
        /// Generate an invoice for a verified payment
        /// </summary>
        /// <param name="subscription">The subscription</param>
        /// <param name="payment">The payment</param>
        /// <param name="paymentProofId">The payment proof ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Invoice ID or reference</returns>
        Task<string> GenerateInvoiceForVerifiedPaymentAsync(
            Subscription subscription, 
            Payment payment, 
            Guid paymentProofId, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Generate a receipt for a verified payment
        /// </summary>
        /// <param name="subscription">The subscription</param>
        /// <param name="payment">The payment</param>
        /// <param name="paymentProofId">The payment proof ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Receipt ID or reference</returns>
        Task<string> GenerateReceiptForVerifiedPaymentAsync(
            Subscription subscription, 
            Payment payment, 
            Guid paymentProofId, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Send invoice/receipt to user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="invoiceReference">Invoice reference</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if sent successfully</returns>
        Task<bool> SendInvoiceToUserAsync(
            Guid userId, 
            string invoiceReference, 
            CancellationToken cancellationToken = default);
    }
}
