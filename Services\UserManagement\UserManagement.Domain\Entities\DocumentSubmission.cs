using System;
using System.Collections.Generic;
using System.Linq;
using Shared.Domain.Common;
using UserManagement.Domain.Exceptions;

namespace UserManagement.Domain.Entities
{
    public enum DocumentType
    {
        // Company Documents
        GstCertificate,
        TradeLicense,
        PanCard,
        BusinessLicense,

        // Individual Documents
        AadharCard,
        DrivingLicense,
        Passport,
        NationalId,

        // Vehicle Documents
        VehicleRegistration,
        VehicleInsurance,
        InsuranceCertificate,

        // Profile Photo
        ProfilePhoto,

        // Other
        Other
    }

    public enum DocumentStatus
    {
        Pending,
        Uploaded,
        UnderReview,
        Approved,
        Rejected,
        RequiresResubmission
    }

    public enum VerificationMethod
    {
        Manual,
        AI_OCR,
        Hybrid
    }

    public class DocumentSubmission : AggregateRoot
    {
        public Guid UserId { get; private set; }
        public UserType UserType { get; private set; }
        public DocumentStatus OverallStatus { get; private set; }
        public DateTime? SubmittedAt { get; private set; }
        public DateTime? ReviewStartedAt { get; private set; }
        public DateTime? CompletedAt { get; private set; }
        public Guid? ReviewedBy { get; private set; }
        public string? ReviewNotes { get; private set; }

        private readonly List<Document> _documents = new();
        public IReadOnlyCollection<Document> Documents => _documents.AsReadOnly();

        private DocumentSubmission() { }

        public DocumentSubmission(Guid userId, UserType userType)
        {
            if (userId == Guid.Empty)
                throw new UserManagementDomainException("User ID cannot be empty");

            UserId = userId;
            UserType = userType;
            OverallStatus = DocumentStatus.Pending;
            CreatedAt = DateTime.UtcNow;

            InitializeRequiredDocuments();
        }

        private void InitializeRequiredDocuments()
        {
            var requiredDocuments = GetRequiredDocumentTypes();

            foreach (var docType in requiredDocuments)
            {
                _documents.Add(new Document(docType));
            }
        }

        private List<DocumentType> GetRequiredDocumentTypes()
        {
            var documents = new List<DocumentType> { DocumentType.ProfilePhoto };

            switch (UserType)
            {
                case UserType.TransportCompany:
                    documents.AddRange(new[]
                    {
                        DocumentType.GstCertificate,
                        DocumentType.TradeLicense,
                        DocumentType.PanCard
                    });
                    break;

                case UserType.Broker:
                    documents.AddRange(new[]
                    {
                        DocumentType.GstCertificate,
                        DocumentType.PanCard
                    });
                    break;

                case UserType.Driver:
                    documents.AddRange(new[]
                    {
                        DocumentType.DrivingLicense,
                        DocumentType.AadharCard
                    });
                    break;

                case UserType.Carrier:
                    documents.AddRange(new[]
                    {
                        DocumentType.AadharCard,
                        DocumentType.VehicleRegistration,
                        DocumentType.VehicleInsurance
                    });
                    break;

                case UserType.Shipper:
                    documents.Add(DocumentType.AadharCard);
                    break;
            }

            return documents;
        }

        public void UploadDocument(DocumentType documentType, string fileName, string filePath,
            string fileSize, string mimeType)
        {
            var document = _documents.FirstOrDefault(d => d.DocumentType == documentType);
            if (document == null)
                throw new UserManagementDomainException($"Document type {documentType} is not required for user type {UserType}");

            document.Upload(fileName, filePath, fileSize, mimeType);
            UpdateOverallStatus();
            UpdatedAt = DateTime.UtcNow;
        }

        public void SubmitForReview()
        {
            if (!AreAllRequiredDocumentsUploaded())
                throw new UserManagementDomainException("All required documents must be uploaded before submission");

            if (OverallStatus != DocumentStatus.Pending)
                throw new UserManagementDomainException("Documents can only be submitted when in pending status");

            OverallStatus = DocumentStatus.UnderReview;
            SubmittedAt = DateTime.UtcNow;
            UpdatedAt = DateTime.UtcNow;
        }

        public void StartReview(Guid reviewerId)
        {
            if (OverallStatus != DocumentStatus.UnderReview)
                throw new UserManagementDomainException("Documents must be submitted for review to start review");

            ReviewedBy = reviewerId;
            ReviewStartedAt = DateTime.UtcNow;
            UpdatedAt = DateTime.UtcNow;
        }

        public void ApproveDocument(DocumentType documentType, string reviewNotes = null)
        {
            var document = GetDocument(documentType);
            document.Approve(reviewNotes);

            CheckAndUpdateOverallStatus();
            UpdatedAt = DateTime.UtcNow;
        }

        public void RejectDocument(DocumentType documentType, string rejectionReason, string reviewNotes = null)
        {
            var document = GetDocument(documentType);
            document.Reject(rejectionReason, reviewNotes);

            OverallStatus = DocumentStatus.Rejected;
            UpdatedAt = DateTime.UtcNow;
        }

        public void RequestDocumentResubmission(DocumentType documentType, string rejectionReason, string reviewNotes = null)
        {
            var document = GetDocument(documentType);
            document.RequestResubmission(rejectionReason, reviewNotes);

            OverallStatus = DocumentStatus.RequiresResubmission;
            UpdatedAt = DateTime.UtcNow;
        }

        public void CompleteReview(string reviewNotes = null)
        {
            if (!AreAllDocumentsApproved())
                throw new UserManagementDomainException("All documents must be approved to complete review");

            OverallStatus = DocumentStatus.Approved;
            CompletedAt = DateTime.UtcNow;
            ReviewNotes = reviewNotes;
            UpdatedAt = DateTime.UtcNow;
        }

        private Document GetDocument(DocumentType documentType)
        {
            var document = _documents.FirstOrDefault(d => d.DocumentType == documentType);
            if (document == null)
                throw new UserManagementDomainException($"Document type {documentType} not found");
            return document;
        }

        private void UpdateOverallStatus()
        {
            if (AreAllRequiredDocumentsUploaded() && OverallStatus == DocumentStatus.Pending)
            {
                // Ready for submission, but not automatically submitted
                return;
            }
        }

        private void CheckAndUpdateOverallStatus()
        {
            if (AreAllDocumentsApproved())
            {
                OverallStatus = DocumentStatus.Approved;
                CompletedAt = DateTime.UtcNow;
            }
        }

        private bool AreAllRequiredDocumentsUploaded()
        {
            return _documents.All(d => d.Status != DocumentStatus.Pending);
        }

        private bool AreAllDocumentsApproved()
        {
            return _documents.All(d => d.Status == DocumentStatus.Approved);
        }

        public bool HasRejectedDocuments()
        {
            return _documents.Any(d => d.Status == DocumentStatus.Rejected);
        }

        public bool HasDocumentsRequiringResubmission()
        {
            return _documents.Any(d => d.Status == DocumentStatus.RequiresResubmission);
        }

        public int GetCompletionPercentage()
        {
            var totalDocuments = _documents.Count;
            var uploadedDocuments = _documents.Count(d => d.Status != DocumentStatus.Pending);

            return totalDocuments > 0 ? (uploadedDocuments * 100) / totalDocuments : 0;
        }
    }

    public class Document : Shared.Domain.Common.BaseEntity
    {
        public DocumentType DocumentType { get; private set; }
        public DocumentStatus Status { get; private set; }
        public string? FileName { get; private set; }
        public string? FilePath { get; private set; }
        public string? FileSize { get; private set; }
        public string? MimeType { get; private set; }
        public VerificationMethod VerificationMethod { get; private set; }
        public string? ExtractedData { get; private set; }
        public decimal ConfidenceScore { get; private set; }
        public string? ReviewNotes { get; private set; }
        public string? RejectionReason { get; private set; }
        public DateTime? UploadedAt { get; private set; }
        public DateTime? ReviewedAt { get; private set; }
        public DateTime? ExpiryDate { get; private set; }
        public int Version { get; private set; } = 1;
        public Guid? PreviousVersionId { get; private set; }
        public bool IsExpired => ExpiryDate.HasValue && ExpiryDate.Value < DateTime.UtcNow;

        private Document() { }

        public Document(DocumentType documentType)
        {
            DocumentType = documentType;
            Status = DocumentStatus.Pending;
            VerificationMethod = VerificationMethod.Manual;
            CreatedAt = DateTime.UtcNow;
        }

        public void Upload(string fileName, string filePath, string fileSize, string mimeType, DateTime? expiryDate = null)
        {
            if (Status != DocumentStatus.Pending && Status != DocumentStatus.RequiresResubmission)
                throw new UserManagementDomainException("Document can only be uploaded when pending or requires resubmission");

            FileName = fileName;
            FilePath = filePath;
            FileSize = fileSize;
            MimeType = mimeType;
            ExpiryDate = expiryDate;
            Status = DocumentStatus.Uploaded;
            UploadedAt = DateTime.UtcNow;

            // Clear previous rejection data
            RejectionReason = null;
            ReviewNotes = null;
            ReviewedAt = null;

            UpdatedAt = DateTime.UtcNow;
        }

        public void Approve(string reviewNotes = null)
        {
            if (Status != DocumentStatus.Uploaded && Status != DocumentStatus.UnderReview)
                throw new UserManagementDomainException("Document must be uploaded or under review to approve");

            Status = DocumentStatus.Approved;
            ReviewNotes = reviewNotes;
            ReviewedAt = DateTime.UtcNow;
            RejectionReason = null;
            UpdatedAt = DateTime.UtcNow;
        }

        public void Reject(string rejectionReason, string reviewNotes = null)
        {
            if (Status != DocumentStatus.Uploaded && Status != DocumentStatus.UnderReview)
                throw new UserManagementDomainException("Document must be uploaded or under review to reject");

            if (string.IsNullOrWhiteSpace(rejectionReason))
                throw new UserManagementDomainException("Rejection reason is required");

            Status = DocumentStatus.Rejected;
            RejectionReason = rejectionReason;
            ReviewNotes = reviewNotes;
            ReviewedAt = DateTime.UtcNow;
            UpdatedAt = DateTime.UtcNow;
        }

        public void RequestResubmission(string rejectionReason, string reviewNotes = null)
        {
            if (Status != DocumentStatus.Uploaded && Status != DocumentStatus.UnderReview)
                throw new UserManagementDomainException("Document must be uploaded or under review to request resubmission");

            if (string.IsNullOrWhiteSpace(rejectionReason))
                throw new UserManagementDomainException("Rejection reason is required");

            Status = DocumentStatus.RequiresResubmission;
            RejectionReason = rejectionReason;
            ReviewNotes = reviewNotes;
            ReviewedAt = DateTime.UtcNow;
            UpdatedAt = DateTime.UtcNow;
        }

        public void SetAIExtractionResults(string extractedData, decimal confidenceScore)
        {
            if (VerificationMethod != VerificationMethod.AI_OCR && VerificationMethod != VerificationMethod.Hybrid)
                throw new UserManagementDomainException("AI extraction results can only be set for AI/OCR or Hybrid verification");

            ExtractedData = extractedData;
            ConfidenceScore = confidenceScore;
            UpdatedAt = DateTime.UtcNow;
        }

        public void SetExpiryDate(DateTime expiryDate)
        {
            if (expiryDate <= DateTime.UtcNow)
                throw new UserManagementDomainException("Expiry date must be in the future");

            ExpiryDate = expiryDate;
            UpdatedAt = DateTime.UtcNow;
        }

        public void CreateNewVersion(Guid previousVersionId)
        {
            PreviousVersionId = previousVersionId;
            Version += 1;
            Status = DocumentStatus.Pending;

            // Clear previous data
            FileName = null;
            FilePath = null;
            FileSize = null;
            MimeType = null;
            ExtractedData = null;
            ConfidenceScore = 0;
            ReviewNotes = null;
            RejectionReason = null;
            UploadedAt = null;
            ReviewedAt = null;

            UpdatedAt = DateTime.UtcNow;
        }

        public void MarkAsExpired()
        {
            if (!ExpiryDate.HasValue)
                throw new UserManagementDomainException("Cannot mark document as expired without expiry date");

            if (ExpiryDate.Value > DateTime.UtcNow)
                throw new UserManagementDomainException("Cannot mark document as expired before expiry date");

            Status = DocumentStatus.RequiresResubmission;
            RejectionReason = "Document has expired";
            UpdatedAt = DateTime.UtcNow;
        }
    }
}
