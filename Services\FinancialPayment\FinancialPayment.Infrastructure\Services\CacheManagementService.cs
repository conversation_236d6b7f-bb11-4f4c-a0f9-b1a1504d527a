using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using StackExchange.Redis;
using FinancialPayment.Application.Interfaces;
using System.Diagnostics;

namespace FinancialPayment.Infrastructure.Services;

public interface ICacheManagementService
{
    Task<CacheStatistics> GetCacheStatisticsAsync();
    Task<CacheHealthCheck> PerformHealthCheckAsync();
    Task InvalidateCacheAsync(CacheInvalidationRequest request);
    Task WarmupCacheAsync(CacheWarmupRequest request);
    Task<Dictionary<string, object>> GetCacheInfoAsync();
    Task<List<string>> GetCacheKeysAsync(string pattern = "*", int limit = 100);
    Task<bool> FlushCacheAsync(string? pattern = null);
    Task<Dictionary<string, TimeSpan?>> GetKeyExpirationTimesAsync(List<string> keys);
    Task SetKeyExpirationAsync(string key, TimeSpan expiration);
    Task<long> GetCacheMemoryUsageAsync();
    Task<Dictionary<string, long>> GetKeyCountByPatternAsync();
}

public class CacheManagementService : ICacheManagementService
{
    private readonly IConnectionMultiplexer _connectionMultiplexer;
    private readonly IDistributedCacheService _cacheService;
    private readonly ILogger<CacheManagementService> _logger;
    private readonly CacheConfiguration _configuration;
    private readonly IDatabase _database;
    private readonly IServer _server;

    public CacheManagementService(
        IConnectionMultiplexer connectionMultiplexer,
        IDistributedCacheService cacheService,
        ILogger<CacheManagementService> logger,
        IOptions<CacheConfiguration> configuration)
    {
        _connectionMultiplexer = connectionMultiplexer;
        _cacheService = cacheService;
        _logger = logger;
        _configuration = configuration.Value;
        _database = connectionMultiplexer.GetDatabase();
        _server = connectionMultiplexer.GetServer(connectionMultiplexer.GetEndPoints().First());
    }

    public async Task<CacheStatistics> GetCacheStatisticsAsync()
    {
        try
        {
            var info = await _server.InfoAsync();
            var keyspaceInfo = info.FirstOrDefault(i => i.Key == "Keyspace");
            
            var statistics = new CacheStatistics
            {
                LastUpdated = DateTime.UtcNow
            };

            // Parse Redis info for statistics
            foreach (var section in info)
            {
                if (section.Key == "Stats")
                {
                    foreach (var item in section)
                    {
                        switch (item.Key)
                        {
                            case "total_commands_processed":
                                statistics.TotalRequests = long.Parse(item.Value);
                                break;
                            case "keyspace_hits":
                                statistics.CacheHits = long.Parse(item.Value);
                                break;
                            case "keyspace_misses":
                                statistics.CacheMisses = long.Parse(item.Value);
                                break;
                        }
                    }
                }
                else if (section.Key == "Memory")
                {
                    foreach (var item in section)
                    {
                        if (item.Key == "used_memory")
                        {
                            statistics.MemoryUsageBytes = long.Parse(item.Value);
                            break;
                        }
                    }
                }
            }

            // Get key counts by pattern
            statistics.KeysByPattern = await GetKeyCountByPatternAsync();
            statistics.TotalKeys = statistics.KeysByPattern.Values.Sum();

            return statistics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cache statistics");
            return new CacheStatistics { LastUpdated = DateTime.UtcNow };
        }
    }

    public async Task<CacheHealthCheck> PerformHealthCheckAsync()
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            // Test basic operations
            var testKey = $"health_check_{Guid.NewGuid()}";
            var testValue = "health_check_value";
            
            await _cacheService.SetStringAsync(testKey, testValue, TimeSpan.FromMinutes(1));
            var retrievedValue = await _cacheService.GetStringAsync(testKey);
            await _cacheService.RemoveAsync(testKey);
            
            stopwatch.Stop();
            
            var isHealthy = retrievedValue == testValue;
            
            return new CacheHealthCheck
            {
                IsHealthy = isHealthy,
                ResponseTime = stopwatch.Elapsed,
                Details = new Dictionary<string, object>
                {
                    { "test_key", testKey },
                    { "test_successful", isHealthy },
                    { "connection_state", _connectionMultiplexer.IsConnected },
                    { "database_number", _database.Database }
                }
            };
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            
            return new CacheHealthCheck
            {
                IsHealthy = false,
                ResponseTime = stopwatch.Elapsed,
                ErrorMessage = ex.Message,
                Details = new Dictionary<string, object>
                {
                    { "connection_state", _connectionMultiplexer.IsConnected },
                    { "error_type", ex.GetType().Name }
                }
            };
        }
    }

    public async Task InvalidateCacheAsync(CacheInvalidationRequest request)
    {
        try
        {
            _logger.LogInformation("Invalidating cache - Keys: {KeyCount}, Patterns: {PatternCount}, Reason: {Reason}",
                request.Keys.Count, request.Patterns.Count, request.Reason);

            // Remove specific keys
            foreach (var key in request.Keys)
            {
                await _cacheService.RemoveAsync(key);
            }

            // Remove keys by patterns
            foreach (var pattern in request.Patterns)
            {
                await _cacheService.RemoveByPatternAsync(pattern);
            }

            _logger.LogInformation("Cache invalidation completed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error invalidating cache");
            throw;
        }
    }

    public async Task WarmupCacheAsync(CacheWarmupRequest request)
    {
        try
        {
            _logger.LogInformation("Starting cache warmup for types: {CacheTypes}", string.Join(", ", request.CacheTypes));

            foreach (var cacheType in request.CacheTypes)
            {
                switch (cacheType.ToLowerInvariant())
                {
                    case "plans":
                        await WarmupPlansAsync(request.ForceRefresh);
                        break;
                    case "tax_rules":
                        await WarmupTaxRulesAsync(request.ForceRefresh);
                        break;
                    case "fraud_rules":
                        await WarmupFraudRulesAsync(request.ForceRefresh);
                        break;
                    case "feature_flags":
                        await WarmupFeatureFlagsAsync(request.ForceRefresh);
                        break;
                    default:
                        _logger.LogWarning("Unknown cache type for warmup: {CacheType}", cacheType);
                        break;
                }
            }

            _logger.LogInformation("Cache warmup completed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during cache warmup");
            throw;
        }
    }

    public async Task<Dictionary<string, object>> GetCacheInfoAsync()
    {
        try
        {
            var info = await _server.InfoAsync();
            var result = new Dictionary<string, object>();

            foreach (var section in info)
            {
                var sectionData = new Dictionary<string, string>();
                foreach (var item in section)
                {
                    sectionData[item.Key] = item.Value;
                }
                result[section.Key] = sectionData;
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cache info");
            return new Dictionary<string, object>();
        }
    }

    public async Task<List<string>> GetCacheKeysAsync(string pattern = "*", int limit = 100)
    {
        try
        {
            var keys = _server.Keys(pattern: $"{_configuration.InstanceName}:{pattern}")
                             .Take(limit)
                             .Select(k => k.ToString().Replace($"{_configuration.InstanceName}:", ""))
                             .ToList();
            
            return await Task.FromResult(keys);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cache keys with pattern: {Pattern}", pattern);
            return new List<string>();
        }
    }

    public async Task<bool> FlushCacheAsync(string? pattern = null)
    {
        try
        {
            if (string.IsNullOrEmpty(pattern))
            {
                await _server.FlushDatabaseAsync();
                _logger.LogWarning("Entire cache database flushed");
            }
            else
            {
                await _cacheService.RemoveByPatternAsync(pattern);
                _logger.LogInformation("Cache flushed for pattern: {Pattern}", pattern);
            }
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error flushing cache");
            return false;
        }
    }

    public async Task<Dictionary<string, TimeSpan?>> GetKeyExpirationTimesAsync(List<string> keys)
    {
        try
        {
            var result = new Dictionary<string, TimeSpan?>();
            
            foreach (var key in keys)
            {
                var ttl = await _cacheService.GetTtlAsync(key);
                result[key] = ttl;
            }
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting key expiration times");
            return new Dictionary<string, TimeSpan?>();
        }
    }

    public async Task SetKeyExpirationAsync(string key, TimeSpan expiration)
    {
        try
        {
            await _cacheService.ExpireAsync(key, expiration);
            _logger.LogDebug("Set expiration for key {Key} to {Expiration}", key, expiration);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting key expiration for {Key}", key);
        }
    }

    public async Task<long> GetCacheMemoryUsageAsync()
    {
        try
        {
            var info = await _server.InfoAsync("Memory");
            var memorySection = info.FirstOrDefault(i => i.Key == "Memory");
            
            if (memorySection != null)
            {
                var usedMemoryItem = memorySection.FirstOrDefault(i => i.Key == "used_memory");
                if (usedMemoryItem != null && long.TryParse(usedMemoryItem.Value, out var usedMemory))
                {
                    return usedMemory;
                }
            }
            
            return 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cache memory usage");
            return 0;
        }
    }

    public async Task<Dictionary<string, long>> GetKeyCountByPatternAsync()
    {
        try
        {
            var patterns = new[]
            {
                "payment:*",
                "subscription:*",
                "plan:*",
                "user_payment_methods:*",
                "tax_rules:*",
                "fraud_rules:*",
                "analytics:*",
                "feature_flag:*",
                "metric:*",
                "session:*",
                "rate_limit:*"
            };

            var result = new Dictionary<string, long>();
            
            foreach (var pattern in patterns)
            {
                var keys = _server.Keys(pattern: $"{_configuration.InstanceName}:{pattern}");
                result[pattern] = keys.Count();
            }
            
            return await Task.FromResult(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting key count by pattern");
            return new Dictionary<string, long>();
        }
    }

    private async Task WarmupPlansAsync(bool forceRefresh)
    {
        try
        {
            // In a real implementation, you would load plans from the database
            // and cache them here
            _logger.LogDebug("Warming up plans cache (force refresh: {ForceRefresh})", forceRefresh);
            await Task.Delay(100); // Simulate work
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error warming up plans cache");
        }
    }

    private async Task WarmupTaxRulesAsync(bool forceRefresh)
    {
        try
        {
            _logger.LogDebug("Warming up tax rules cache (force refresh: {ForceRefresh})", forceRefresh);
            await Task.Delay(100); // Simulate work
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error warming up tax rules cache");
        }
    }

    private async Task WarmupFraudRulesAsync(bool forceRefresh)
    {
        try
        {
            _logger.LogDebug("Warming up fraud rules cache (force refresh: {ForceRefresh})", forceRefresh);
            await Task.Delay(100); // Simulate work
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error warming up fraud rules cache");
        }
    }

    private async Task WarmupFeatureFlagsAsync(bool forceRefresh)
    {
        try
        {
            _logger.LogDebug("Warming up feature flags cache (force refresh: {ForceRefresh})", forceRefresh);
            await Task.Delay(100); // Simulate work
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error warming up feature flags cache");
        }
    }
}
