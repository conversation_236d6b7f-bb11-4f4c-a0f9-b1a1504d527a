using MediatR;
using TripManagement.Application.DTOs.Reports;
using TripManagement.Domain.Enums;

namespace TripManagement.Application.Commands.Reports;

/// <summary>
/// Command to generate routing failure report
/// </summary>
public class GenerateRoutingFailureReportCommand : IRequest<RoutingFailureReportDto>
{
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public List<FailureType>? FailureTypes { get; set; }
    public List<FailureSeverity>? Severities { get; set; }
    public List<FailureStatus>? Statuses { get; set; }
    public List<string>? Categories { get; set; }
    public bool? IsRecurring { get; set; }
    public decimal? MinImpactCost { get; set; }
    public decimal? MaxImpactCost { get; set; }
    public bool IncludeResolved { get; set; } = true;
    public bool IncludeFailureDetails { get; set; } = true;
    public bool IncludePatternAnalysis { get; set; } = true;
    public bool IncludeImpactAnalysis { get; set; } = true;
    public bool IncludeTrendAnalysis { get; set; } = false;
    public bool IncludeRootCauseAnalysis { get; set; } = true;
    public ReportFormat Format { get; set; } = ReportFormat.Json;
    public int? MaxRecords { get; set; }
    public string? SortBy { get; set; } // Severity, Date, ImpactCost, ResolutionTime
    public string? SortOrder { get; set; } = "DESC";
    public Guid RequestedBy { get; set; }
    public string RequestedByRole { get; set; } = string.Empty;
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
}

/// <summary>
/// Enum for report formats
/// </summary>
public enum ReportFormat
{
    Json = 0,
    Csv = 1,
    Excel = 2,
    Pdf = 3
}
