using CommunicationNotification.Application.Commands.Notifications;
using CommunicationNotification.Application.Commands.Alerts;
using CommunicationNotification.Application.Queries.Messages;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Infrastructure.Controllers.Models;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.RateLimiting;
using System.ComponentModel.DataAnnotations;
using System.Security.Claims;

namespace CommunicationNotification.Infrastructure.Controllers;

/// <summary>
/// Notification management API controller
/// </summary>
[ApiController]
[Route("api/notifications")]
[Authorize]
[EnableRateLimiting("NotificationPolicy")]
public class NotificationController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<NotificationController> _logger;

    public NotificationController(IMediator mediator, ILogger<NotificationController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Send a notification to a specific user
    /// </summary>
    /// <param name="request">Notification details</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Notification send result</returns>
    [HttpPost("send")]
    [ProducesResponseType(typeof(NotificationSendResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status429TooManyRequests)]
    public async Task<IActionResult> SendNotification(
        [FromBody] SendNotificationRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Sending notification to user {UserId} via {Channel}",
                request.UserId, request.PreferredChannel);

            var command = new SendNotificationCommand
            {
                UserId = request.UserId,
                Content = request.Content,
                MessageType = request.MessageType,
                Priority = request.Priority,
                PreferredChannel = request.PreferredChannel,
                PreferredLanguage = request.PreferredLanguage,
                Subject = request.Subject,
                Parameters = request.Parameters ?? new(),
                TemplateId = request.TemplateId,
                RequireDeliveryConfirmation = request.RequireDeliveryConfirmation,
                ScheduledAt = request.ScheduledAt,
                Tags = request.Tags ?? new(),
                CustomHeaders = request.CustomHeaders ?? new(),
                InitiatedBy = GetCurrentUserId(),
                CorrelationId = request.CorrelationId ?? HttpContext.TraceIdentifier
            };

            var result = await _mediator.Send(command, cancellationToken);

            if (result.IsSuccess)
            {
                var response = new NotificationSendResponse
                {
                    NotificationId = result.Data!.NotificationId,
                    Status = result.Data.Status.ToString(),
                    ChannelUsed = result.Data.ChannelUsed.ToString(),
                    SentAt = result.Data.SentAt,
                    ExternalId = result.Data.ExternalId,
                    EstimatedDeliveryTime = CalculateEstimatedDeliveryTime(result.Data.ChannelUsed),
                    CommandId = result.CommandId
                };

                return Ok(response);
            }

            return BadRequest(new ValidationProblemDetails
            {
                Title = "Notification Send Failed",
                Detail = result.ErrorMessage,
                Instance = HttpContext.Request.Path,
                Extensions = { ["commandId"] = result.CommandId.ToString() }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending notification to user {UserId}", request.UserId);
            return StatusCode(500, new { Error = "Internal server error", TraceId = HttpContext.TraceIdentifier });
        }
    }

    /// <summary>
    /// Send bulk notifications to multiple users
    /// </summary>
    /// <param name="request">Bulk notification details</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Bulk notification send result</returns>
    [HttpPost("send-bulk")]
    [Authorize(Roles = "Admin,Dispatcher,BulkSender")]
    [ProducesResponseType(typeof(BulkNotificationSendResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> SendBulkNotification(
        [FromBody] SendBulkNotificationRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Sending bulk notification to {UserCount} users",
                request.UserIds.Count);

            var command = new SendBulkNotificationCommand
            {
                UserIds = request.UserIds,
                Content = request.Content,
                MessageType = request.MessageType,
                Priority = request.Priority,
                PreferredChannel = request.PreferredChannel,
                PreferredLanguage = request.PreferredLanguage,
                Subject = request.Subject,
                Parameters = request.Parameters ?? new(),
                TemplateId = request.TemplateId,
                RequireDeliveryConfirmation = request.RequireDeliveryConfirmation,
                ScheduledAt = request.ScheduledAt,
                Tags = request.Tags ?? new(),
                BatchSize = request.BatchSize,
                BatchDelay = TimeSpan.FromSeconds(request.BatchDelaySeconds),
                InitiatedBy = GetCurrentUserId(),
                CorrelationId = request.CorrelationId ?? HttpContext.TraceIdentifier
            };

            var result = await _mediator.Send(command, cancellationToken);

            if (result.IsSuccess)
            {
                var response = new BulkNotificationSendResponse
                {
                    BulkOperationId = result.Data!.BulkOperationId,
                    TotalNotifications = result.Data.TotalNotifications,
                    SuccessfulNotifications = result.Data.SuccessfulNotifications,
                    FailedNotifications = result.Data.FailedNotifications,
                    StartedAt = result.Data.StartedAt,
                    CompletedAt = result.Data.CompletedAt,
                    CommandId = result.CommandId,
                    SuccessRate = result.Data.TotalNotifications > 0
                        ? (double)result.Data.SuccessfulNotifications / result.Data.TotalNotifications * 100
                        : 0
                };

                return Ok(response);
            }

            return BadRequest(new ValidationProblemDetails
            {
                Title = "Bulk Notification Send Failed",
                Detail = result.ErrorMessage,
                Instance = HttpContext.Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending bulk notification");
            return StatusCode(500, new { Error = "Internal server error", TraceId = HttpContext.TraceIdentifier });
        }
    }

    /// <summary>
    /// Send a template-based notification
    /// </summary>
    /// <param name="request">Template notification details</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Template notification send result</returns>
    [HttpPost("send-template")]
    [ProducesResponseType(typeof(NotificationSendResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> SendTemplateNotification(
        [FromBody] SendTemplateNotificationRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Sending template notification {TemplateId} to user {UserId}",
                request.TemplateId, request.UserId);

            var command = new SendTemplateNotificationCommand
            {
                UserId = request.UserId,
                TemplateId = request.TemplateId,
                TemplateParameters = request.TemplateParameters ?? new(),
                MessageType = request.MessageType,
                Priority = request.Priority,
                PreferredChannel = request.PreferredChannel,
                PreferredLanguage = request.PreferredLanguage,
                RequireDeliveryConfirmation = request.RequireDeliveryConfirmation,
                ScheduledAt = request.ScheduledAt,
                Tags = request.Tags ?? new(),
                InitiatedBy = GetCurrentUserId(),
                CorrelationId = request.CorrelationId ?? HttpContext.TraceIdentifier
            };

            var result = await _mediator.Send(command, cancellationToken);

            if (result.IsSuccess)
            {
                var response = new NotificationSendResponse
                {
                    NotificationId = result.Data!.NotificationId,
                    Status = result.Data.Status.ToString(),
                    ChannelUsed = result.Data.ChannelUsed.ToString(),
                    SentAt = result.Data.SentAt,
                    ExternalId = result.Data.ExternalId,
                    EstimatedDeliveryTime = CalculateEstimatedDeliveryTime(result.Data.ChannelUsed),
                    CommandId = result.CommandId,
                    TemplateId = request.TemplateId
                };

                return Ok(response);
            }

            return BadRequest(new ValidationProblemDetails
            {
                Title = "Template Notification Send Failed",
                Detail = result.ErrorMessage,
                Instance = HttpContext.Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending template notification {TemplateId} to user {UserId}",
                request.TemplateId, request.UserId);
            return StatusCode(500, new { Error = "Internal server error", TraceId = HttpContext.TraceIdentifier });
        }
    }

    /// <summary>
    /// Send an emergency notification with high priority
    /// </summary>
    /// <param name="request">Emergency notification details</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Emergency notification send result</returns>
    [HttpPost("send-emergency")]
    [Authorize(Roles = "Admin,EmergencyDispatcher,SystemOperator")]
    [ProducesResponseType(typeof(NotificationSendResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> SendEmergencyNotification(
        [FromBody] SendEmergencyNotificationRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogWarning("Sending emergency notification to user {UserId}: {EmergencyType}",
                request.UserId, request.EmergencyType);

            var command = new SendEmergencyNotificationCommand
            {
                UserId = request.UserId,
                Content = request.Content,
                EmergencyType = request.EmergencyType,
                Location = request.Location,
                EmergencyData = request.EmergencyData ?? new(),
                RequireAcknowledgment = request.RequireAcknowledgment,
                AcknowledgmentTimeout = TimeSpan.FromMinutes(request.AcknowledgmentTimeoutMinutes),
                EscalationChannels = request.EscalationChannels ?? new(),
                EscalationUserIds = request.EscalationUserIds ?? new(),
                InitiatedBy = GetCurrentUserId(),
                CorrelationId = request.CorrelationId ?? HttpContext.TraceIdentifier
            };

            var result = await _mediator.Send(command, cancellationToken);

            if (result.IsSuccess)
            {
                var response = new NotificationSendResponse
                {
                    NotificationId = result.Data!.NotificationId,
                    Status = result.Data.Status.ToString(),
                    ChannelUsed = result.Data.ChannelUsed.ToString(),
                    SentAt = result.Data.SentAt,
                    ExternalId = result.Data.ExternalId,
                    EstimatedDeliveryTime = CalculateEstimatedDeliveryTime(result.Data.ChannelUsed),
                    CommandId = result.CommandId,
                    IsEmergency = true
                };

                return Ok(response);
            }

            return BadRequest(new ValidationProblemDetails
            {
                Title = "Emergency Notification Send Failed",
                Detail = result.ErrorMessage,
                Instance = HttpContext.Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending emergency notification to user {UserId}", request.UserId);
            return StatusCode(500, new { Error = "Internal server error", TraceId = HttpContext.TraceIdentifier });
        }
    }

    /// <summary>
    /// Get notification delivery status
    /// </summary>
    /// <param name="notificationId">Notification ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Notification delivery status</returns>
    [HttpGet("{notificationId}/status")]
    [ProducesResponseType(typeof(NotificationStatusResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetNotificationStatus(
        [FromRoute] Guid notificationId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetMessageDeliveryStatusQuery
            {
                MessageId = notificationId,
                IncludeDeliveryAttempts = true,
                IncludeChannelDetails = true,
                RequestedBy = GetCurrentUserId()
            };

            var result = await _mediator.Send(query, cancellationToken);

            if (result.IsSuccess && result.Data != null)
            {
                var response = new NotificationStatusResponse
                {
                    NotificationId = result.Data.MessageId,
                    Status = result.Data.Status.ToString(),
                    Channel = result.Data.Channel.ToString(),
                    SentAt = result.Data.SentAt,
                    DeliveredAt = result.Data.DeliveredAt,
                    ReadAt = result.Data.ReadAt,
                    ExternalId = result.Data.ExternalId,
                    ErrorMessage = result.Data.ErrorMessage,
                    DeliveryAttempts = result.Data.DeliveryAttempts.Select(a => new DeliveryAttemptResponse
                    {
                        AttemptNumber = a.AttemptNumber,
                        Channel = a.Channel.ToString(),
                        AttemptedAt = a.AttemptedAt,
                        IsSuccess = a.IsSuccess,
                        ErrorMessage = a.ErrorMessage,
                        ResponseTime = a.ResponseTime
                    }).ToList()
                };

                return Ok(response);
            }

            return NotFound(new { Message = $"Notification {notificationId} not found" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting notification status for {NotificationId}", notificationId);
            return StatusCode(500, new { Error = "Internal server error", TraceId = HttpContext.TraceIdentifier });
        }
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (Guid.TryParse(userIdClaim, out var userId))
        {
            return userId;
        }
        throw new UnauthorizedAccessException("User ID not found in claims");
    }

    private TimeSpan CalculateEstimatedDeliveryTime(NotificationChannel channel)
    {
        return channel switch
        {
            NotificationChannel.Push => TimeSpan.FromSeconds(5),
            NotificationChannel.Sms => TimeSpan.FromSeconds(30),
            NotificationChannel.WhatsApp => TimeSpan.FromSeconds(15),
            NotificationChannel.Email => TimeSpan.FromMinutes(2),
            NotificationChannel.Voice => TimeSpan.FromMinutes(1),
            _ => TimeSpan.FromMinutes(5)
        };
    }

    #region Document Expiry Alerts

    /// <summary>
    /// Send document expiry alert notification
    /// </summary>
    /// <param name="request">Document expiry alert details</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Document expiry alert send result</returns>
    [HttpPost("send-document-expiry-alert")]
    [Authorize(Roles = "Admin,Carrier,System")]
    [ProducesResponseType(typeof(SendDocumentExpiryAlertResult), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> SendDocumentExpiryAlert(
        [FromBody] SendDocumentExpiryAlertRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Sending document expiry alert for carrier {CarrierId}, document {DocumentType}",
                request.CarrierId, request.DocumentType);

            var command = new SendDocumentExpiryAlertCommand
            {
                CarrierId = request.CarrierId,
                DocumentType = request.DocumentType,
                EntityType = request.EntityType,
                EntityId = request.EntityId,
                EntityName = request.EntityName,
                ExpiryDate = request.ExpiryDate,
                DaysUntilExpiry = request.DaysUntilExpiry,
                AlertType = request.AlertType,
                ThresholdDays = request.ThresholdDays,
                DocumentDetails = request.DocumentDetails ?? new(),
                PreferredChannels = request.PreferredChannels ?? new(),
                Priority = request.Priority,
                RequireAcknowledgment = request.RequireAcknowledgment,
                Tags = request.Tags ?? new(),
                CorrelationId = request.CorrelationId ?? HttpContext.TraceIdentifier
            };

            var result = await _mediator.Send(command, cancellationToken);

            if (result.IsSuccess)
            {
                return Ok(result);
            }

            return BadRequest(new ValidationProblemDetails
            {
                Title = "Document Expiry Alert Send Failed",
                Detail = result.ErrorMessage,
                Instance = HttpContext.Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending document expiry alert for carrier {CarrierId}", request.CarrierId);
            return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while sending the document expiry alert");
        }
    }

    /// <summary>
    /// Send bulk document expiry alerts
    /// </summary>
    /// <param name="request">Bulk document expiry alert details</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Bulk document expiry alert send result</returns>
    [HttpPost("send-bulk-document-expiry-alerts")]
    [Authorize(Roles = "Admin,System")]
    [ProducesResponseType(typeof(SendBulkDocumentExpiryAlertsResult), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> SendBulkDocumentExpiryAlerts(
        [FromBody] SendBulkDocumentExpiryAlertsRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Sending bulk document expiry alerts for carrier {CarrierId}, {DocumentCount} documents",
                request.CarrierId, request.ExpiringDocuments.Count);

            var command = new SendBulkDocumentExpiryAlertsCommand
            {
                CarrierId = request.CarrierId,
                ExpiringDocuments = request.ExpiringDocuments.Select(d => new DocumentExpiryAlertInfo
                {
                    DocumentType = d.DocumentType,
                    EntityType = d.EntityType,
                    EntityId = d.EntityId,
                    EntityName = d.EntityName,
                    ExpiryDate = d.ExpiryDate,
                    DaysUntilExpiry = d.DaysUntilExpiry,
                    DocumentDetails = d.DocumentDetails ?? new()
                }).ToList(),
                ThresholdDays = request.ThresholdDays,
                AlertType = request.AlertType,
                PreferredChannels = request.PreferredChannels ?? new(),
                Priority = request.Priority,
                RequireAcknowledgment = request.RequireAcknowledgment,
                GroupByEntityType = request.GroupByEntityType,
                Tags = request.Tags ?? new(),
                CorrelationId = request.CorrelationId ?? HttpContext.TraceIdentifier
            };

            var result = await _mediator.Send(command, cancellationToken);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending bulk document expiry alerts for carrier {CarrierId}", request.CarrierId);
            return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while sending bulk document expiry alerts");
        }
    }

    /// <summary>
    /// Schedule document expiry monitoring for a carrier
    /// </summary>
    /// <param name="request">Document expiry monitoring configuration</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Document expiry monitoring schedule result</returns>
    [HttpPost("schedule-document-expiry-monitoring")]
    [Authorize(Roles = "Admin,Carrier")]
    [ProducesResponseType(typeof(ScheduleDocumentExpiryMonitoringResult), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> ScheduleDocumentExpiryMonitoring(
        [FromBody] ScheduleDocumentExpiryMonitoringRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Scheduling document expiry monitoring for carrier {CarrierId}", request.CarrierId);

            var command = new ScheduleDocumentExpiryMonitoringCommand
            {
                CarrierId = request.CarrierId,
                AlertThresholds = request.AlertThresholds ?? new() { 30, 15, 7, 0 },
                DocumentTypes = request.DocumentTypes ?? new(),
                EntityTypes = request.EntityTypes ?? new(),
                CronExpression = request.CronExpression ?? "0 9 * * *",
                IsActive = request.IsActive,
                PreferredChannels = request.PreferredChannels ?? new(),
                MonitoringSettings = request.MonitoringSettings ?? new(),
                CorrelationId = request.CorrelationId ?? HttpContext.TraceIdentifier
            };

            var result = await _mediator.Send(command, cancellationToken);

            if (result.IsSuccess)
            {
                return Ok(result);
            }

            return BadRequest(new ValidationProblemDetails
            {
                Title = "Document Expiry Monitoring Schedule Failed",
                Detail = result.ErrorMessage,
                Instance = HttpContext.Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error scheduling document expiry monitoring for carrier {CarrierId}", request.CarrierId);
            return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while scheduling document expiry monitoring");
        }
    }

    #endregion

    #region Performance Alerts

    /// <summary>
    /// Send performance alert notification
    /// </summary>
    /// <param name="request">Performance alert details</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Performance alert send result</returns>
    [HttpPost("send-performance-alert")]
    [Authorize(Roles = "Admin,System,Analytics")]
    [ProducesResponseType(typeof(SendPerformanceAlertResult), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> SendPerformanceAlert(
        [FromBody] SendPerformanceAlertRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Sending performance alert for carrier {CarrierId}, metric {PerformanceMetric}",
                request.CarrierId, request.PerformanceMetric);

            var command = new SendPerformanceAlertCommand
            {
                CarrierId = request.CarrierId,
                PerformanceMetric = request.PerformanceMetric,
                AlertType = request.AlertType,
                CurrentValue = request.CurrentValue,
                PreviousValue = request.PreviousValue,
                TargetValue = request.TargetValue,
                ThresholdValue = request.ThresholdValue,
                ChangeDirection = request.ChangeDirection,
                ChangePercentage = request.ChangePercentage,
                TimePeriod = request.TimePeriod,
                MeasurementDate = request.MeasurementDate,
                PerformanceData = request.PerformanceData ?? new(),
                PreferredChannels = request.PreferredChannels ?? new(),
                Priority = request.Priority,
                RequireAcknowledgment = request.RequireAcknowledgment,
                Tags = request.Tags ?? new(),
                CorrelationId = request.CorrelationId ?? HttpContext.TraceIdentifier
            };

            var result = await _mediator.Send(command, cancellationToken);

            if (result.IsSuccess)
            {
                return Ok(result);
            }

            return BadRequest(new ValidationProblemDetails
            {
                Title = "Performance Alert Send Failed",
                Detail = result.ErrorMessage,
                Instance = HttpContext.Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending performance alert for carrier {CarrierId}", request.CarrierId);
            return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while sending the performance alert");
        }
    }

    /// <summary>
    /// Send rating change alert notification
    /// </summary>
    /// <param name="request">Rating change alert details</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Rating change alert send result</returns>
    [HttpPost("send-rating-change-alert")]
    [Authorize(Roles = "Admin,System,Analytics")]
    [ProducesResponseType(typeof(SendPerformanceAlertResult), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> SendRatingChangeAlert(
        [FromBody] SendRatingChangeAlertRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Sending rating change alert for carrier {CarrierId}, change {RatingChange}",
                request.CarrierId, request.RatingChange);

            var command = new SendRatingChangeAlertCommand
            {
                CarrierId = request.CarrierId,
                CurrentRating = request.CurrentRating,
                PreviousRating = request.PreviousRating,
                RatingChange = request.RatingChange,
                RatingCategory = request.RatingCategory,
                ChangeType = request.ChangeType,
                ReviewCount = request.ReviewCount,
                RatingDate = request.RatingDate,
                RecentFeedback = request.RecentFeedback ?? new(),
                RatingDetails = request.RatingDetails ?? new(),
                PreferredChannels = request.PreferredChannels ?? new(),
                Priority = request.Priority,
                RequireAcknowledgment = request.RequireAcknowledgment,
                Tags = request.Tags ?? new(),
                CorrelationId = request.CorrelationId ?? HttpContext.TraceIdentifier
            };

            var result = await _mediator.Send(command, cancellationToken);

            if (result.IsSuccess)
            {
                return Ok(result);
            }

            return BadRequest(new ValidationProblemDetails
            {
                Title = "Rating Change Alert Send Failed",
                Detail = result.ErrorMessage,
                Instance = HttpContext.Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending rating change alert for carrier {CarrierId}", request.CarrierId);
            return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while sending the rating change alert");
        }
    }

    /// <summary>
    /// Send performance milestone alert notification
    /// </summary>
    /// <param name="request">Performance milestone alert details</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Performance milestone alert send result</returns>
    [HttpPost("send-milestone-alert")]
    [Authorize(Roles = "Admin,System,Analytics")]
    [ProducesResponseType(typeof(SendPerformanceAlertResult), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> SendMilestoneAlert(
        [FromBody] SendPerformanceMilestoneAlertRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Sending milestone alert for carrier {CarrierId}, milestone {MilestoneName}",
                request.CarrierId, request.MilestoneName);

            var command = new SendPerformanceMilestoneAlertCommand
            {
                CarrierId = request.CarrierId,
                MilestoneType = request.MilestoneType,
                MilestoneName = request.MilestoneName,
                MilestoneValue = request.MilestoneValue,
                MilestoneUnit = request.MilestoneUnit,
                AchievedDate = request.AchievedDate,
                AchievementLevel = request.AchievementLevel,
                MilestoneData = request.MilestoneData ?? new(),
                Rewards = request.Rewards ?? new(),
                NextMilestone = request.NextMilestone,
                NextMilestoneTarget = request.NextMilestoneTarget,
                PreferredChannels = request.PreferredChannels ?? new(),
                Priority = request.Priority,
                RequireAcknowledgment = request.RequireAcknowledgment,
                Tags = request.Tags ?? new(),
                CorrelationId = request.CorrelationId ?? HttpContext.TraceIdentifier
            };

            var result = await _mediator.Send(command, cancellationToken);

            if (result.IsSuccess)
            {
                return Ok(result);
            }

            return BadRequest(new ValidationProblemDetails
            {
                Title = "Milestone Alert Send Failed",
                Detail = result.ErrorMessage,
                Instance = HttpContext.Request.Path
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending milestone alert for carrier {CarrierId}", request.CarrierId);
            return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while sending the milestone alert");
        }
    }

    #endregion
}
