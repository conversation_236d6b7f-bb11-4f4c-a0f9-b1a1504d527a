using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Domain.Entities;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;
using CommunicationNotification.Infrastructure.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using System.Security.Claims;

namespace CommunicationNotification.Infrastructure.Hubs;

/// <summary>
/// SignalR hub for real-time chat functionality
/// </summary>
[Authorize]
public class ChatHub : Hub<IChatClient>
{
    private readonly IChatService _chatService;
    private readonly ILogger<ChatHub> _logger;
    private readonly IConnectionTrackingService _connectionTracker;

    public ChatHub(
        IChatService chatService,
        ILogger<ChatHub> logger,
        IConnectionTrackingService connectionTracker)
    {
        _chatService = chatService;
        _logger = logger;
        _connectionTracker = connectionTracker;
    }

    /// <summary>
    /// Handle client connection
    /// </summary>
    public override async Task OnConnectedAsync()
    {
        var userId = GetUserId();
        var userRole = GetUserRole();

        _logger.LogInformation("User {UserId} with role {Role} connected to chat hub", userId, userRole);

        // Track connection
        await _connectionTracker.AddConnectionAsync(userId, Context.ConnectionId);

        // Add user to their personal group
        await Groups.AddToGroupAsync(Context.ConnectionId, $"user_{userId}");

        // Add user to role-based groups
        await Groups.AddToGroupAsync(Context.ConnectionId, $"role_{userRole}");

        // Notify other clients about user coming online
        await Clients.Others.UserOnline(userId, userRole.ToString());

        await base.OnConnectedAsync();
    }

    /// <summary>
    /// Handle client disconnection
    /// </summary>
    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        var userId = GetUserId();
        var userRole = GetUserRole();

        _logger.LogInformation("User {UserId} disconnected from chat hub", userId);

        // Remove connection tracking
        await _connectionTracker.RemoveConnectionAsync(Context.ConnectionId);

        // Remove user from groups
        await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"user_{userId}");
        await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"role_{userRole}");

        // Check if user is still online (has other connections)
        var isStillOnline = await _connectionTracker.GetUserConnectionsAsync(userId);
        if (!isStillOnline.Any())
        {
            // Notify other clients about user going offline only if no other connections
            await Clients.Others.UserOffline(userId);
        }

        if (exception != null)
        {
            _logger.LogError(exception, "User {UserId} disconnected with error", userId);
        }

        await base.OnDisconnectedAsync(exception);
    }

    /// <summary>
    /// Send a message to a conversation
    /// </summary>
    /// <param name="conversationId">Conversation ID</param>
    /// <param name="message">Message content</param>
    /// <param name="messageType">Type of message</param>
    public async Task SendMessage(Guid conversationId, string message, string messageType = "Text")
    {
        try
        {
            var userId = GetUserId();
            var messageTypeEnum = Enum.Parse<MessageType>(messageType, true);

            var chatRequest = new ChatMessageRequest
            {
                SenderId = userId,
                ConversationId = conversationId,
                Content = MessageContent.Create("", message, Language.English),
                MessageType = messageTypeEnum,
                Priority = Priority.Normal
            };

            var result = await _chatService.SendMessageAsync(chatRequest);

            if (result.IsSuccess)
            {
                // Send message to all participants in the conversation
                await Clients.Group($"conversation_{conversationId}")
                    .ReceiveMessage(result.MessageId!.Value, userId, message, messageType, DateTime.UtcNow);

                _logger.LogInformation("Message sent successfully in conversation {ConversationId} by user {UserId}",
                    conversationId, userId);
            }
            else
            {
                await Clients.Caller.MessageError(result.ErrorMessage ?? "Failed to send message");
                _logger.LogError("Failed to send message in conversation {ConversationId} by user {UserId}: {Error}",
                    conversationId, userId, result.ErrorMessage);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception occurred while sending message in conversation {ConversationId}", conversationId);
            await Clients.Caller.MessageError("An error occurred while sending the message");
        }
    }

    /// <summary>
    /// Join a conversation
    /// </summary>
    /// <param name="conversationId">Conversation ID to join</param>
    public async Task JoinConversation(Guid conversationId)
    {
        try
        {
            var userId = GetUserId();

            // Add user to conversation group
            await Groups.AddToGroupAsync(Context.ConnectionId, $"conversation_{conversationId}");

            // Notify other participants
            await Clients.Group($"conversation_{conversationId}")
                .UserJoinedConversation(conversationId, userId);

            _logger.LogInformation("User {UserId} joined conversation {ConversationId}", userId, conversationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception occurred while joining conversation {ConversationId}", conversationId);
            await Clients.Caller.MessageError("Failed to join conversation");
        }
    }

    /// <summary>
    /// Leave a conversation
    /// </summary>
    /// <param name="conversationId">Conversation ID to leave</param>
    public async Task LeaveConversation(Guid conversationId)
    {
        try
        {
            var userId = GetUserId();

            // Remove user from conversation group
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"conversation_{conversationId}");

            // Notify other participants
            await Clients.Group($"conversation_{conversationId}")
                .UserLeftConversation(conversationId, userId);

            _logger.LogInformation("User {UserId} left conversation {ConversationId}", userId, conversationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception occurred while leaving conversation {ConversationId}", conversationId);
        }
    }

    /// <summary>
    /// Start typing indicator
    /// </summary>
    /// <param name="conversationId">Conversation ID</param>
    public async Task StartTyping(Guid conversationId)
    {
        try
        {
            var userId = GetUserId();

            await Clients.GroupExcept($"conversation_{conversationId}", Context.ConnectionId)
                .UserStartedTyping(conversationId, userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception occurred while starting typing indicator for conversation {ConversationId}", conversationId);
        }
    }

    /// <summary>
    /// Stop typing indicator
    /// </summary>
    /// <param name="conversationId">Conversation ID</param>
    public async Task StopTyping(Guid conversationId)
    {
        try
        {
            var userId = GetUserId();

            await Clients.GroupExcept($"conversation_{conversationId}", Context.ConnectionId)
                .UserStoppedTyping(conversationId, userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception occurred while stopping typing indicator for conversation {ConversationId}", conversationId);
        }
    }

    /// <summary>
    /// Mark message as read
    /// </summary>
    /// <param name="messageId">Message ID</param>
    /// <param name="conversationId">Conversation ID</param>
    public async Task MarkMessageAsRead(Guid messageId, Guid conversationId)
    {
        try
        {
            var userId = GetUserId();

            var success = await _chatService.MarkMessageAsReadAsync(messageId, userId);

            if (success)
            {
                // Notify other participants that message was read
                await Clients.GroupExcept($"conversation_{conversationId}", Context.ConnectionId)
                    .MessageRead(messageId, userId, DateTime.UtcNow);

                _logger.LogDebug("Message {MessageId} marked as read by user {UserId}", messageId, userId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception occurred while marking message {MessageId} as read", messageId);
        }
    }

    /// <summary>
    /// Create a new conversation
    /// </summary>
    /// <param name="title">Conversation title</param>
    /// <param name="participantIds">List of participant user IDs</param>
    /// <param name="conversationType">Type of conversation</param>
    /// <param name="relatedEntityId">Related entity ID (trip, order, etc.)</param>
    /// <param name="relatedEntityType">Related entity type</param>
    public async Task CreateConversation(
        string? title,
        List<Guid> participantIds,
        string conversationType = "Group",
        Guid? relatedEntityId = null,
        string? relatedEntityType = null)
    {
        try
        {
            var userId = GetUserId();
            var typeEnum = Enum.Parse<ConversationType>(conversationType, true);

            var request = new CreateConversationRequest
            {
                Title = title,
                Type = typeEnum,
                ParticipantIds = participantIds,
                CreatedBy = userId,
                RelatedEntityId = relatedEntityId,
                RelatedEntityType = relatedEntityType
            };

            var conversation = await _chatService.CreateConversationAsync(request);

            // Add all participants to the conversation group
            foreach (var participantId in participantIds)
            {
                await Clients.Group($"user_{participantId}")
                    .ConversationCreated(conversation.Id, conversation.Title, conversation.Type.ToString(), userId);
            }

            _logger.LogInformation("Conversation {ConversationId} created by user {UserId} with {ParticipantCount} participants",
                conversation.Id, userId, participantIds.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception occurred while creating conversation");
            await Clients.Caller.MessageError("Failed to create conversation");
        }
    }

    /// <summary>
    /// Get user ID from claims
    /// </summary>
    private Guid GetUserId()
    {
        var userIdClaim = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (Guid.TryParse(userIdClaim, out var userId))
        {
            return userId;
        }
        throw new UnauthorizedAccessException("User ID not found in claims");
    }

    /// <summary>
    /// Get user role from claims
    /// </summary>
    private UserRole GetUserRole()
    {
        var roleClaim = Context.User?.FindFirst(ClaimTypes.Role)?.Value;
        if (Enum.TryParse<UserRole>(roleClaim, true, out var role))
        {
            return role;
        }
        return UserRole.User; // Default role
    }
}

/// <summary>
/// Interface for chat client methods
/// </summary>
public interface IChatClient
{
    /// <summary>
    /// Receive a new message
    /// </summary>
    Task ReceiveMessage(Guid messageId, Guid senderId, string message, string messageType, DateTime timestamp);

    /// <summary>
    /// User came online
    /// </summary>
    Task UserOnline(Guid userId, string role);

    /// <summary>
    /// User went offline
    /// </summary>
    Task UserOffline(Guid userId);

    /// <summary>
    /// User joined conversation
    /// </summary>
    Task UserJoinedConversation(Guid conversationId, Guid userId);

    /// <summary>
    /// User left conversation
    /// </summary>
    Task UserLeftConversation(Guid conversationId, Guid userId);

    /// <summary>
    /// User started typing
    /// </summary>
    Task UserStartedTyping(Guid conversationId, Guid userId);

    /// <summary>
    /// User stopped typing
    /// </summary>
    Task UserStoppedTyping(Guid conversationId, Guid userId);

    /// <summary>
    /// Message was read
    /// </summary>
    Task MessageRead(Guid messageId, Guid userId, DateTime readAt);

    /// <summary>
    /// New conversation created
    /// </summary>
    Task ConversationCreated(Guid conversationId, string? title, string type, Guid createdBy);

    /// <summary>
    /// Error occurred
    /// </summary>
    Task MessageError(string error);
}
