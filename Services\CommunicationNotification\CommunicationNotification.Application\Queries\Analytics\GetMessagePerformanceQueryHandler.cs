using CommunicationNotification.Application.Common;
using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Domain.ValueObjects;
using MediatR;
using Microsoft.Extensions.Logging;

namespace CommunicationNotification.Application.Queries.Analytics;

/// <summary>
/// Handler for getting message performance metrics
/// </summary>
public class GetMessagePerformanceQueryHandler : IRequestHandler<GetMessagePerformanceQuery, QueryResult<MessagePerformanceMetrics>>
{
    private readonly IAnalyticsService _analyticsService;
    private readonly ILogger<GetMessagePerformanceQueryHandler> _logger;

    public GetMessagePerformanceQueryHandler(
        IAnalyticsService analyticsService,
        ILogger<GetMessagePerformanceQueryHandler> logger)
    {
        _analyticsService = analyticsService;
        _logger = logger;
    }

    public async Task<QueryResult<MessagePerformanceMetrics>> Handle(
        GetMessagePerformanceQuery request,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Getting message performance metrics from {StartDate} to {EndDate}",
                request.StartDate, request.EndDate);

            // Validate date range
            if (request.StartDate >= request.EndDate)
            {
                return QueryResult<MessagePerformanceMetrics>.Failure("Start date must be before end date");
            }

            if (request.EndDate > DateTime.UtcNow)
            {
                return QueryResult<MessagePerformanceMetrics>.Failure("End date cannot be in the future");
            }

            var metrics = await _analyticsService.GetMessagePerformanceAsync(
                request.StartDate,
                request.EndDate,
                request.MessageType,
                request.Channel,
                request.UserId,
                cancellationToken);

            _logger.LogInformation("Retrieved message performance metrics: {TotalMessages} total messages, {DeliveryRate}% delivery rate",
                metrics.TotalMessages, metrics.DeliveryRate);

            return QueryResult<MessagePerformanceMetrics>.Success(metrics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting message performance metrics");
            return QueryResult<MessagePerformanceMetrics>.Failure("Failed to retrieve message performance metrics");
        }
    }
}

/// <summary>
/// Handler for getting channel performance comparison
/// </summary>
public class GetChannelPerformanceQueryHandler : IRequestHandler<GetChannelPerformanceQuery, QueryResult<List<ChannelPerformanceMetrics>>>
{
    private readonly IAnalyticsService _analyticsService;
    private readonly ILogger<GetChannelPerformanceQueryHandler> _logger;

    public GetChannelPerformanceQueryHandler(
        IAnalyticsService analyticsService,
        ILogger<GetChannelPerformanceQueryHandler> logger)
    {
        _analyticsService = analyticsService;
        _logger = logger;
    }

    public async Task<QueryResult<List<ChannelPerformanceMetrics>>> Handle(
        GetChannelPerformanceQuery request,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Getting channel performance comparison from {StartDate} to {EndDate}",
                request.StartDate, request.EndDate);

            var channelMetrics = await _analyticsService.GetChannelPerformanceAsync(
                request.StartDate,
                request.EndDate,
                cancellationToken);

            // Filter by requested channels if specified
            if (request.Channels?.Any() == true)
            {
                channelMetrics = channelMetrics
                    .Where(cm => request.Channels.Contains(cm.Channel))
                    .ToList();
            }

            _logger.LogInformation("Retrieved performance metrics for {ChannelCount} channels",
                channelMetrics.Count);

            return QueryResult<List<ChannelPerformanceMetrics>>.Success(channelMetrics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting channel performance metrics");
            return QueryResult<List<ChannelPerformanceMetrics>>.Failure("Failed to retrieve channel performance metrics");
        }
    }
}

/// <summary>
/// Handler for getting user engagement metrics
/// </summary>
public class GetUserEngagementQueryHandler : IRequestHandler<GetUserEngagementQuery, QueryResult<UserEngagementMetrics>>
{
    private readonly IAnalyticsService _analyticsService;
    private readonly ILogger<GetUserEngagementQueryHandler> _logger;

    public GetUserEngagementQueryHandler(
        IAnalyticsService analyticsService,
        ILogger<GetUserEngagementQueryHandler> logger)
    {
        _analyticsService = analyticsService;
        _logger = logger;
    }

    public async Task<QueryResult<UserEngagementMetrics>> Handle(
        GetUserEngagementQuery request,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Getting user engagement metrics for user {UserId} from {StartDate} to {EndDate}",
                request.UserId, request.StartDate, request.EndDate);

            if (request.UserId == Guid.Empty)
            {
                return QueryResult<UserEngagementMetrics>.Failure("User ID is required");
            }

            var engagement = await _analyticsService.GetUserEngagementAsync(
                request.UserId,
                request.StartDate,
                request.EndDate,
                cancellationToken);

            _logger.LogInformation("Retrieved user engagement metrics: {EngagementRate}% engagement rate, {IsActive} active user",
                engagement.EngagementRate, engagement.IsActiveUser);

            return QueryResult<UserEngagementMetrics>.Success(engagement);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user engagement metrics for user {UserId}", request.UserId);
            return QueryResult<UserEngagementMetrics>.Failure("Failed to retrieve user engagement metrics");
        }
    }
}

/// <summary>
/// Handler for getting time-based analytics trends
/// </summary>
public class GetTimeBasedAnalyticsQueryHandler : IRequestHandler<GetTimeBasedAnalyticsQuery, QueryResult<List<TimeBasedMetrics>>>
{
    private readonly IAnalyticsService _analyticsService;
    private readonly ILogger<GetTimeBasedAnalyticsQueryHandler> _logger;

    public GetTimeBasedAnalyticsQueryHandler(
        IAnalyticsService analyticsService,
        ILogger<GetTimeBasedAnalyticsQueryHandler> logger)
    {
        _analyticsService = analyticsService;
        _logger = logger;
    }

    public async Task<QueryResult<List<TimeBasedMetrics>>> Handle(
        GetTimeBasedAnalyticsQuery request,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Getting time-based analytics from {StartDate} to {EndDate} with period type {PeriodType}",
                request.StartDate, request.EndDate, request.PeriodType);

            var timeBasedMetrics = await _analyticsService.GetTimeBasedAnalyticsAsync(
                request.StartDate,
                request.EndDate,
                request.PeriodType,
                request.MessageType,
                request.Channel,
                cancellationToken);

            _logger.LogInformation("Retrieved {Count} time-based metric periods", timeBasedMetrics.Count);

            return QueryResult<List<TimeBasedMetrics>>.Success(timeBasedMetrics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting time-based analytics");
            return QueryResult<List<TimeBasedMetrics>>.Failure("Failed to retrieve time-based analytics");
        }
    }
}

/// <summary>
/// Handler for getting real-time dashboard data
/// </summary>
public class GetRealTimeDashboardQueryHandler : IRequestHandler<GetRealTimeDashboardQuery, QueryResult<Dictionary<string, object>>>
{
    private readonly IAnalyticsService _analyticsService;
    private readonly IRealTimeAnalyticsService _realTimeAnalyticsService;
    private readonly ILogger<GetRealTimeDashboardQueryHandler> _logger;

    public GetRealTimeDashboardQueryHandler(
        IAnalyticsService analyticsService,
        IRealTimeAnalyticsService realTimeAnalyticsService,
        ILogger<GetRealTimeDashboardQueryHandler> logger)
    {
        _analyticsService = analyticsService;
        _realTimeAnalyticsService = realTimeAnalyticsService;
        _logger = logger;
    }

    public async Task<QueryResult<Dictionary<string, object>>> Handle(
        GetRealTimeDashboardQuery request,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Getting real-time dashboard data for user role {UserRole}", request.UserRole);

            // Get real-time metrics
            var realTimeMetrics = await _realTimeAnalyticsService.GetRealTimeMetricsAsync(cancellationToken);

            // Get role-specific dashboard data
            var dashboardData = await _analyticsService.GetRealTimeDashboardDataAsync(
                request.UserRole,
                request.UserId,
                cancellationToken);

            // Combine real-time and dashboard data
            var combinedData = new Dictionary<string, object>(dashboardData);
            foreach (var kvp in realTimeMetrics)
            {
                combinedData[$"realtime_{kvp.Key}"] = kvp.Value;
            }

            // Add metadata
            combinedData["refreshInterval"] = request.RefreshIntervalSeconds;
            combinedData["lastUpdated"] = DateTime.UtcNow;
            combinedData["userRole"] = request.UserRole.ToString();

            _logger.LogInformation("Retrieved real-time dashboard data with {MetricCount} metrics", combinedData.Count);

            return QueryResult<Dictionary<string, object>>.Success(combinedData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting real-time dashboard data");
            return QueryResult<Dictionary<string, object>>.Failure("Failed to retrieve real-time dashboard data");
        }
    }
}
