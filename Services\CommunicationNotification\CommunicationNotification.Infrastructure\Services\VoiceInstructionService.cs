using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Domain.Enums;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace CommunicationNotification.Infrastructure.Services;

/// <summary>
/// Voice instruction service for drivers
/// </summary>
public class VoiceInstructionService : IVoiceInstructionService
{
    private readonly IVoiceProvider _voiceProvider;
    private readonly ILocalizationService _localizationService;
    private readonly ILogger<VoiceInstructionService> _logger;
    private readonly VoiceInstructionSettings _settings;

    public VoiceInstructionService(
        IVoiceProvider voiceProvider,
        ILocalizationService localizationService,
        IConfiguration configuration,
        ILogger<VoiceInstructionService> logger)
    {
        _voiceProvider = voiceProvider;
        _localizationService = localizationService;
        _logger = logger;
        _settings = configuration.GetSection("VoiceInstructions").Get<VoiceInstructionSettings>() ?? new();
    }

    public async Task<VoiceInstructionResult> SendVoiceInstructionAsync(
        VoiceInstruction instruction,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Get localized voice message
            var localizedMessage = await GetLocalizedVoiceMessage(instruction, cancellationToken);
            
            // Prepare voice call request
            var voiceRequest = new VoiceCallRequest
            {
                PhoneNumber = instruction.PhoneNumber ?? await GetDriverPhoneNumberAsync(instruction.DriverId),
                Message = localizedMessage,
                VoiceSettings = GetVoiceSettings(instruction.Language, instruction.Priority),
                Metadata = new Dictionary<string, string>
                {
                    ["driverId"] = instruction.DriverId.ToString(),
                    ["tripId"] = instruction.TripId?.ToString() ?? "",
                    ["instructionType"] = instruction.InstructionType.ToString(),
                    ["priority"] = instruction.Priority.ToString()
                }
            };

            var result = await _voiceProvider.MakeCallAsync(voiceRequest, cancellationToken);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Voice instruction sent to driver {DriverId}: {InstructionType}", 
                    instruction.DriverId, instruction.InstructionType);
                
                return VoiceInstructionResult.Success(result.CallId, result.EstimatedCost);
            }
            else
            {
                _logger.LogWarning("Failed to send voice instruction to driver {DriverId}: {Error}", 
                    instruction.DriverId, result.ErrorMessage);
                
                return VoiceInstructionResult.Failure(result.ErrorMessage ?? "Voice call failed");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception occurred while sending voice instruction to driver {DriverId}", instruction.DriverId);
            return VoiceInstructionResult.Failure($"Exception: {ex.Message}");
        }
    }

    public async Task<VoiceInstructionResult> SendVoiceNavigationAsync(
        DriverNavigationInstruction navigation,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var voiceMessage = await GenerateNavigationVoiceMessage(navigation, cancellationToken);
            
            var voiceInstruction = new VoiceInstruction
            {
                DriverId = navigation.DriverId,
                TripId = navigation.TripId,
                Message = voiceMessage,
                Language = navigation.PreferredLanguage,
                Priority = VoicePriority.Normal,
                InstructionType = VoiceInstructionType.Navigation,
                PhoneNumber = navigation.PhoneNumber
            };

            return await SendVoiceInstructionAsync(voiceInstruction, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send voice navigation to driver {DriverId}", navigation.DriverId);
            return VoiceInstructionResult.Failure($"Navigation voice failed: {ex.Message}");
        }
    }

    public async Task<VoiceInstructionResult> SendEmergencyVoiceAlertAsync(
        DriverEmergencyAlert alert,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var emergencyMessage = await GenerateEmergencyVoiceMessage(alert, cancellationToken);
            
            var voiceInstruction = new VoiceInstruction
            {
                DriverId = alert.DriverId,
                TripId = alert.TripId,
                Message = emergencyMessage,
                Language = alert.PreferredLanguage,
                Priority = VoicePriority.Critical,
                InstructionType = VoiceInstructionType.Emergency,
                PhoneNumber = alert.PhoneNumber
            };

            return await SendVoiceInstructionAsync(voiceInstruction, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send emergency voice alert to driver {DriverId}", alert.DriverId);
            return VoiceInstructionResult.Failure($"Emergency voice alert failed: {ex.Message}");
        }
    }

    public async Task<List<VoiceInstructionHistory>> GetVoiceHistoryAsync(
        Guid driverId,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // This would typically query a database or external service
            // For now, return empty list as placeholder
            return new List<VoiceInstructionHistory>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get voice history for driver {DriverId}", driverId);
            return new List<VoiceInstructionHistory>();
        }
    }

    public async Task<VoiceInstructionSettings> GetDriverVoiceSettingsAsync(
        Guid driverId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // This would typically query driver preferences from database
            // For now, return default settings
            return _settings;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get voice settings for driver {DriverId}", driverId);
            return _settings;
        }
    }

    public async Task<bool> UpdateDriverVoiceSettingsAsync(
        Guid driverId,
        VoiceInstructionSettings settings,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // This would typically update driver preferences in database
            _logger.LogInformation("Voice settings updated for driver {DriverId}", driverId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update voice settings for driver {DriverId}", driverId);
            return false;
        }
    }

    private async Task<string> GetLocalizedVoiceMessage(VoiceInstruction instruction, CancellationToken cancellationToken)
    {
        try
        {
            var templateKey = instruction.InstructionType switch
            {
                VoiceInstructionType.TripAssignment => "voice_trip_assignment",
                VoiceInstructionType.Navigation => "voice_navigation",
                VoiceInstructionType.TripUpdate => "voice_trip_update",
                VoiceInstructionType.Emergency => "voice_emergency",
                VoiceInstructionType.General => "voice_general",
                _ => "voice_general"
            };

            var localizedText = await _localizationService.GetLocalizedTextAsync(
                templateKey, 
                instruction.Language, 
                cancellationToken: cancellationToken);

            // If template not found, use the original message
            if (string.IsNullOrEmpty(localizedText) || localizedText == templateKey)
            {
                return instruction.Message;
            }

            // Replace placeholder with actual message
            return localizedText.Replace("{{message}}", instruction.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get localized voice message");
            return instruction.Message; // Fallback to original message
        }
    }

    private async Task<string> GenerateNavigationVoiceMessage(DriverNavigationInstruction navigation, CancellationToken cancellationToken)
    {
        try
        {
            var parameters = new Dictionary<string, object>
            {
                ["instruction"] = navigation.Instruction,
                ["distance"] = $"{navigation.Distance:F1} kilometers",
                ["direction"] = navigation.Direction
            };

            if (!string.IsNullOrEmpty(navigation.StreetName))
            {
                parameters["streetName"] = navigation.StreetName;
            }

            if (!string.IsNullOrEmpty(navigation.Landmark))
            {
                parameters["landmark"] = navigation.Landmark;
            }

            var localizedMessage = await _localizationService.GetLocalizedTextAsync(
                "voice_navigation_detailed",
                navigation.PreferredLanguage,
                parameters,
                cancellationToken);

            return !string.IsNullOrEmpty(localizedMessage) ? localizedMessage : navigation.Instruction;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate navigation voice message");
            return navigation.Instruction;
        }
    }

    private async Task<string> GenerateEmergencyVoiceMessage(DriverEmergencyAlert alert, CancellationToken cancellationToken)
    {
        try
        {
            var parameters = new Dictionary<string, object>
            {
                ["alertType"] = alert.AlertType.ToString(),
                ["message"] = alert.Message,
                ["severity"] = alert.Severity.ToString()
            };

            if (!string.IsNullOrEmpty(alert.Location))
            {
                parameters["location"] = alert.Location;
            }

            var localizedMessage = await _localizationService.GetLocalizedTextAsync(
                "voice_emergency_alert",
                alert.PreferredLanguage,
                parameters,
                cancellationToken);

            return !string.IsNullOrEmpty(localizedMessage) ? localizedMessage : 
                $"Emergency alert: {alert.AlertType}. {alert.Message}";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate emergency voice message");
            return $"Emergency alert: {alert.AlertType}. {alert.Message}";
        }
    }

    private VoiceSettings GetVoiceSettings(Language language, VoicePriority priority)
    {
        var voiceId = language switch
        {
            Language.Hindi => _settings.HindiVoiceId,
            Language.Kannada => _settings.KannadaVoiceId,
            _ => _settings.EnglishVoiceId
        };

        var speed = priority switch
        {
            VoicePriority.Critical => 0.8f, // Slower for critical messages
            VoicePriority.High => 0.9f,
            VoicePriority.Normal => 1.0f,
            VoicePriority.Low => 1.1f,
            _ => 1.0f
        };

        return new VoiceSettings
        {
            VoiceId = voiceId,
            Speed = speed,
            Language = GetLanguageCode(language),
            Volume = _settings.DefaultVolume
        };
    }

    private static string GetLanguageCode(Language language)
    {
        return language switch
        {
            Language.Hindi => "hi-IN",
            Language.Kannada => "kn-IN",
            _ => "en-US"
        };
    }

    private async Task<string> GetDriverPhoneNumberAsync(Guid driverId)
    {
        // This would typically query the driver's phone number from the database
        // For now, return a placeholder
        return "+91XXXXXXXXXX";
    }
}

/// <summary>
/// Voice instruction settings
/// </summary>
public class VoiceInstructionSettings
{
    public bool EnableVoiceInstructions { get; set; } = true;
    public bool EnableNavigationVoice { get; set; } = true;
    public bool EnableEmergencyVoice { get; set; } = true;
    public string EnglishVoiceId { get; set; } = "alice";
    public string HindiVoiceId { get; set; } = "aditi";
    public string KannadaVoiceId { get; set; } = "raveena";
    public float DefaultVolume { get; set; } = 0.8f;
    public float DefaultSpeed { get; set; } = 1.0f;
    public int MaxRetryAttempts { get; set; } = 3;
    public TimeSpan RetryDelay { get; set; } = TimeSpan.FromSeconds(5);
    public bool LogVoiceCalls { get; set; } = true;
}

/// <summary>
/// Voice instruction types
/// </summary>
public enum VoiceInstructionType
{
    TripAssignment,
    Navigation,
    TripUpdate,
    Emergency,
    General
}

/// <summary>
/// Voice priority levels
/// </summary>
public enum VoicePriority
{
    Low,
    Normal,
    High,
    Critical
}

/// <summary>
/// Voice instruction request
/// </summary>
public class VoiceInstruction
{
    public Guid DriverId { get; set; }
    public Guid? TripId { get; set; }
    public string Message { get; set; } = string.Empty;
    public Language Language { get; set; } = Language.English;
    public VoicePriority Priority { get; set; } = VoicePriority.Normal;
    public VoiceInstructionType InstructionType { get; set; } = VoiceInstructionType.General;
    public string? PhoneNumber { get; set; }
    public Dictionary<string, string> Metadata { get; set; } = new();
}

/// <summary>
/// Voice instruction result
/// </summary>
public class VoiceInstructionResult
{
    public bool IsSuccess { get; set; }
    public string? CallId { get; set; }
    public decimal? Cost { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    public static VoiceInstructionResult Success(string? callId, decimal? cost = null)
    {
        return new VoiceInstructionResult
        {
            IsSuccess = true,
            CallId = callId,
            Cost = cost
        };
    }

    public static VoiceInstructionResult Failure(string errorMessage)
    {
        return new VoiceInstructionResult
        {
            IsSuccess = false,
            ErrorMessage = errorMessage
        };
    }
}

/// <summary>
/// Voice instruction history
/// </summary>
public class VoiceInstructionHistory
{
    public Guid Id { get; set; }
    public Guid DriverId { get; set; }
    public Guid? TripId { get; set; }
    public VoiceInstructionType InstructionType { get; set; }
    public string Message { get; set; } = string.Empty;
    public Language Language { get; set; }
    public VoicePriority Priority { get; set; }
    public bool IsSuccess { get; set; }
    public string? CallId { get; set; }
    public decimal? Cost { get; set; }
    public DateTime CreatedAt { get; set; }
    public string? ErrorMessage { get; set; }
}
