using Shared.Domain.Common;
using MobileWorkflow.Domain.Enums;
using MobileWorkflow.Domain.Events;

namespace MobileWorkflow.Domain.Entities;

public class SyncItem : AggregateRoot
{
    public Guid SyncOperationId { get; private set; }
    public string EntityType { get; private set; } // Trip, Order, Document, etc.
    public string EntityId { get; private set; }
    public string Action { get; private set; } // Create, Update, Delete
    public SyncStatus Status { get; private set; }
    public int Priority { get; private set; } // 1 = High, 2 = Medium, 3 = Low
    public DateTime CreatedAt { get; private set; }
    public DateTime? ProcessedAt { get; private set; }
    public string? ErrorMessage { get; private set; }
    public Dictionary<string, object> Data { get; private set; }
    public Dictionary<string, object> OriginalData { get; private set; }
    public string? DataHash { get; private set; }
    public long DataSizeBytes { get; private set; }
    public int Version { get; private set; }
    public DateTime LastModified { get; private set; }
    public string? ConflictType { get; private set; }
    public Dictionary<string, object> ConflictData { get; private set; }
    public string? ConflictResolution { get; private set; }
    public int RetryCount { get; private set; }
    public DateTime? NextRetryTime { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    public virtual SyncOperation SyncOperation { get; private set; } = null!;

    private SyncItem() { } // EF Core

    public SyncItem(
        Guid syncOperationId,
        string entityType,
        string entityId,
        string action,
        Dictionary<string, object> data,
        int priority = 2,
        int version = 1)
    {
        SyncOperationId = syncOperationId;
        EntityType = entityType ?? throw new ArgumentNullException(nameof(entityType));
        EntityId = entityId ?? throw new ArgumentNullException(nameof(entityId));
        Action = action ?? throw new ArgumentNullException(nameof(action));
        Status = SyncStatus.Pending;
        Priority = priority;
        CreatedAt = DateTime.UtcNow;
        Data = data ?? throw new ArgumentNullException(nameof(data));
        OriginalData = new Dictionary<string, object>(data);
        Version = version;
        LastModified = DateTime.UtcNow;
        ConflictData = new Dictionary<string, object>();
        RetryCount = 0;
        Metadata = new Dictionary<string, object>();

        // Calculate data hash and size
        DataHash = CalculateDataHash(data);
        DataSizeBytes = CalculateDataSize(data);

        AddDomainEvent(new SyncItemCreatedEvent(Id, EntityType, EntityId, Action));
    }

    public void StartProcessing()
    {
        if (Status != SyncStatus.Pending)
            throw new InvalidOperationException($"Cannot start processing sync item with status: {Status}");

        Status = SyncStatus.InProgress;
        ProcessedAt = DateTime.UtcNow;

        AddDomainEvent(new SyncItemProcessedEvent(Id, EntityType, EntityId, false)); // Processing started
    }

    public void Complete()
    {
        if (Status != SyncStatus.InProgress)
            throw new InvalidOperationException($"Cannot complete sync item with status: {Status}");

        Status = SyncStatus.Completed;
        ProcessedAt = DateTime.UtcNow;

        AddDomainEvent(new SyncItemProcessedEvent(Id, EntityType, EntityId, true));
    }

    public void Fail(string errorMessage)
    {
        Status = SyncStatus.Failed;
        ErrorMessage = errorMessage;
        ProcessedAt = DateTime.UtcNow;

        AddDomainEvent(new SyncItemFailedEvent(Id, EntityType, EntityId, errorMessage));
    }

    public void DetectConflict(string conflictType, Dictionary<string, object> conflictData)
    {
        Status = SyncStatus.Conflict;
        ConflictType = conflictType;
        ConflictData = conflictData ?? new Dictionary<string, object>();

        AddDomainEvent(new SyncItemConflictDetectedEvent(Id, EntityType, EntityId, conflictType));
    }

    public void ResolveConflict(string resolution, Dictionary<string, object> resolvedData)
    {
        if (Status != SyncStatus.Conflict)
            throw new InvalidOperationException($"Cannot resolve conflict for sync item with status: {Status}");

        ConflictResolution = resolution;
        Data = resolvedData ?? throw new ArgumentNullException(nameof(resolvedData));
        Status = SyncStatus.Pending; // Ready for retry

        // Update hash and size with resolved data
        DataHash = CalculateDataHash(Data);
        DataSizeBytes = CalculateDataSize(Data);
        LastModified = DateTime.UtcNow;

        AddDomainEvent(new SyncItemConflictResolvedEvent(Id, EntityType, EntityId, resolution));
    }

    public void UpdateData(Dictionary<string, object> newData, int newVersion)
    {
        if (Status == SyncStatus.Completed)
            throw new InvalidOperationException("Cannot update completed sync item");

        Data = newData ?? throw new ArgumentNullException(nameof(newData));
        Version = newVersion;
        LastModified = DateTime.UtcNow;

        // Update hash and size
        DataHash = CalculateDataHash(Data);
        DataSizeBytes = CalculateDataSize(Data);

        AddDomainEvent(new SyncItemDataUpdatedEvent(Id, EntityType, EntityId, newVersion.ToString()));
    }

    public void ScheduleRetry(TimeSpan delay)
    {
        RetryCount++;
        NextRetryTime = DateTime.UtcNow.Add(delay);
        Status = SyncStatus.Pending;

        AddDomainEvent(new SyncItemRetryEvent(Id, EntityType, EntityId, RetryCount));
    }

    public void Cancel()
    {
        if (Status == SyncStatus.Completed)
            throw new InvalidOperationException("Cannot cancel completed sync item");

        Status = SyncStatus.Cancelled;
        ProcessedAt = DateTime.UtcNow;

        AddDomainEvent(new SyncItemCancelledEvent(Id, EntityType, EntityId));
    }

    public bool ShouldRetry()
    {
        return Status == SyncStatus.Failed && RetryCount < GetMaxRetries() &&
               (NextRetryTime == null || DateTime.UtcNow >= NextRetryTime);
    }

    public bool HasDataChanged()
    {
        var currentHash = CalculateDataHash(Data);
        return currentHash != DataHash;
    }

    public TimeSpan GetRetryDelay()
    {
        // Exponential backoff based on priority and retry count
        var baseDelay = Priority switch
        {
            1 => TimeSpan.FromSeconds(30), // High priority - shorter delay
            2 => TimeSpan.FromMinutes(1),  // Medium priority
            3 => TimeSpan.FromMinutes(5),  // Low priority - longer delay
            _ => TimeSpan.FromMinutes(1)
        };

        return TimeSpan.FromTicks(baseDelay.Ticks * (long)Math.Pow(2, Math.Min(RetryCount, 6)));
    }

    private int GetMaxRetries()
    {
        return Priority switch
        {
            1 => 5, // High priority - more retries
            2 => 3, // Medium priority
            3 => 2, // Low priority - fewer retries
            _ => 3
        };
    }

    private string CalculateDataHash(Dictionary<string, object> data)
    {
        // Simple hash calculation for data integrity
        var json = System.Text.Json.JsonSerializer.Serialize(data, new System.Text.Json.JsonSerializerOptions
        {
            PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase
        });

        using var sha256 = System.Security.Cryptography.SHA256.Create();
        var hashBytes = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(json));
        return Convert.ToBase64String(hashBytes);
    }

    private long CalculateDataSize(Dictionary<string, object> data)
    {
        var json = System.Text.Json.JsonSerializer.Serialize(data);
        return System.Text.Encoding.UTF8.GetByteCount(json);
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    public bool IsHighPriority()
    {
        return Priority == 1;
    }

    public bool IsReadyForProcessing()
    {
        return Status == SyncStatus.Pending && (NextRetryTime == null || DateTime.UtcNow >= NextRetryTime);
    }
}



