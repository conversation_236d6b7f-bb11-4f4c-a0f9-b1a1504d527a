using Microsoft.EntityFrameworkCore;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Interfaces;
using SubscriptionManagement.Infrastructure.Persistence;

namespace SubscriptionManagement.Infrastructure.Repositories
{
    public class SubscriptionPaymentProofRepository : ISubscriptionPaymentProofRepository
    {
        private readonly SubscriptionDbContext _context;

        public SubscriptionPaymentProofRepository(SubscriptionDbContext context)
        {
            _context = context;
        }

        public async Task<SubscriptionPaymentProof?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
        {
            return await _context.SubscriptionPaymentProofs
                .FirstOrDefaultAsync(p => p.Id == id, cancellationToken);
        }

        public async Task<SubscriptionPaymentProof?> GetByIdWithSubscriptionAsync(Guid id, CancellationToken cancellationToken = default)
        {
            return await _context.SubscriptionPaymentProofs
                .Include(p => p.Subscription)
                .FirstOrDefaultAsync(p => p.Id == id, cancellationToken);
        }

        public async Task<List<SubscriptionPaymentProof>> GetBySubscriptionIdAsync(Guid subscriptionId, CancellationToken cancellationToken = default)
        {
            return await _context.SubscriptionPaymentProofs
                .Where(p => p.SubscriptionId == subscriptionId)
                .OrderByDescending(p => p.CreatedAt)
                .ToListAsync(cancellationToken);
        }

        public async Task<List<SubscriptionPaymentProof>> GetByUserIdAsync(Guid userId, CancellationToken cancellationToken = default)
        {
            return await _context.SubscriptionPaymentProofs
                .Where(p => p.UserId == userId)
                .OrderByDescending(p => p.CreatedAt)
                .ToListAsync(cancellationToken);
        }

        public async Task<List<SubscriptionPaymentProof>> GetByStatusAsync(PaymentProofStatus status, CancellationToken cancellationToken = default)
        {
            return await _context.SubscriptionPaymentProofs
                .Where(p => p.Status == status)
                .OrderByDescending(p => p.CreatedAt)
                .ToListAsync(cancellationToken);
        }

        public async Task<List<SubscriptionPaymentProof>> GetPendingProofsAsync(CancellationToken cancellationToken = default)
        {
            return await _context.SubscriptionPaymentProofs
                .Where(p => p.Status == PaymentProofStatus.Pending)
                .OrderBy(p => p.CreatedAt)
                .ToListAsync(cancellationToken);
        }

        public async Task<List<SubscriptionPaymentProof>> GetProofsRequiringReviewAsync(CancellationToken cancellationToken = default)
        {
            return await _context.SubscriptionPaymentProofs
                .Where(p => p.Status == PaymentProofStatus.Pending || 
                           p.Status == PaymentProofStatus.UnderReview ||
                           p.Status == PaymentProofStatus.RequiresAdditionalInfo)
                .OrderBy(p => p.CreatedAt)
                .ToListAsync(cancellationToken);
        }

        public async Task<(List<SubscriptionPaymentProof> Items, int TotalCount)> GetPagedAsync(
            int pageNumber, 
            int pageSize, 
            PaymentProofStatus? status = null,
            Guid? userId = null,
            Guid? subscriptionId = null,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            CancellationToken cancellationToken = default)
        {
            var query = _context.SubscriptionPaymentProofs.AsQueryable();

            if (status.HasValue)
                query = query.Where(p => p.Status == status.Value);

            if (userId.HasValue)
                query = query.Where(p => p.UserId == userId.Value);

            if (subscriptionId.HasValue)
                query = query.Where(p => p.SubscriptionId == subscriptionId.Value);

            if (fromDate.HasValue)
                query = query.Where(p => p.CreatedAt >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(p => p.CreatedAt <= toDate.Value);

            var totalCount = await query.CountAsync(cancellationToken);

            var items = await query
                .OrderByDescending(p => p.CreatedAt)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync(cancellationToken);

            return (items, totalCount);
        }

        public async Task<SubscriptionPaymentProof> AddAsync(SubscriptionPaymentProof paymentProof, CancellationToken cancellationToken = default)
        {
            _context.SubscriptionPaymentProofs.Add(paymentProof);
            await _context.SaveChangesAsync(cancellationToken);
            return paymentProof;
        }

        public async Task UpdateAsync(SubscriptionPaymentProof paymentProof, CancellationToken cancellationToken = default)
        {
            _context.SubscriptionPaymentProofs.Update(paymentProof);
            await _context.SaveChangesAsync(cancellationToken);
        }

        public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
        {
            var paymentProof = await _context.SubscriptionPaymentProofs.FindAsync(new object[] { id }, cancellationToken);
            if (paymentProof != null)
            {
                _context.SubscriptionPaymentProofs.Remove(paymentProof);
                await _context.SaveChangesAsync(cancellationToken);
            }
        }

        public async Task<bool> ExistsAsync(Guid id, CancellationToken cancellationToken = default)
        {
            return await _context.SubscriptionPaymentProofs
                .AnyAsync(p => p.Id == id, cancellationToken);
        }

        public async Task<bool> HasPendingProofForSubscriptionAsync(Guid subscriptionId, CancellationToken cancellationToken = default)
        {
            return await _context.SubscriptionPaymentProofs
                .AnyAsync(p => p.SubscriptionId == subscriptionId && 
                              (p.Status == PaymentProofStatus.Pending || 
                               p.Status == PaymentProofStatus.UnderReview ||
                               p.Status == PaymentProofStatus.RequiresAdditionalInfo), 
                         cancellationToken);
        }

        public async Task<int> GetCountByStatusAsync(PaymentProofStatus status, CancellationToken cancellationToken = default)
        {
            return await _context.SubscriptionPaymentProofs
                .CountAsync(p => p.Status == status, cancellationToken);
        }

        public async Task<List<SubscriptionPaymentProof>> GetVerifiedProofsByDateRangeAsync(
            DateTime fromDate, 
            DateTime toDate, 
            CancellationToken cancellationToken = default)
        {
            return await _context.SubscriptionPaymentProofs
                .Where(p => p.Status == PaymentProofStatus.Verified &&
                           p.VerifiedAt >= fromDate &&
                           p.VerifiedAt <= toDate)
                .OrderByDescending(p => p.VerifiedAt)
                .ToListAsync(cancellationToken);
        }
    }
}
