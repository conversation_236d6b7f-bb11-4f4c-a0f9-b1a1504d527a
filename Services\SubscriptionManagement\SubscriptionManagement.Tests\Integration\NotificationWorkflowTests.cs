using FluentAssertions;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using SubscriptionManagement.API;
using SubscriptionManagement.Application.Commands.SendManualNotification;
using SubscriptionManagement.Application.Commands.TriggerSubscriptionAlerts;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.API.Controllers;
using System.Net;
using System.Net.Http.Json;
using Xunit;

namespace SubscriptionManagement.Tests.Integration
{
    public class NotificationWorkflowTests : IClassFixture<WebApplicationFactory<Program>>
    {
        private readonly WebApplicationFactory<Program> _factory;
        private readonly HttpClient _client;
        private readonly Mock<ICommunicationService> _communicationServiceMock;

        public NotificationWorkflowTests(WebApplicationFactory<Program> factory)
        {
            _communicationServiceMock = new Mock<ICommunicationService>();

            _factory = factory.WithWebHostBuilder(builder =>
            {
                builder.UseEnvironment("Testing");
                builder.ConfigureServices(services =>
                {
                    // Replace the real communication service with mock
                    services.AddSingleton(_communicationServiceMock.Object);
                });
            });

            _client = _factory.CreateClient();
            _client.DefaultRequestHeaders.Add("Authorization", "Bearer test-admin-token");
        }

        [Fact]
        public async Task SendManualNotification_WithValidRequest_ShouldSendSuccessfully()
        {
            // Arrange
            var subscriptionId = Guid.NewGuid();
            var request = new SendManualNotificationRequest
            {
                SubscriptionId = subscriptionId,
                NotificationType = NotificationType.SubscriptionReminder,
                Channels = new List<string> { "Email", "SMS" },
                CustomMessage = "Your subscription is expiring soon. Please renew to continue using our services.",
                Language = "en"
            };

            _communicationServiceMock.Setup(x => x.SendNotificationAsync(It.IsAny<NotificationRequest>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new NotificationResult { Success = true, NotificationId = Guid.NewGuid().ToString() });

            // Act
            var response = await _client.PostAsJsonAsync("/api/admin/notifications/subscription", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);

            var result = await response.Content.ReadFromJsonAsync<SendManualNotificationResponse>();
            result.Should().NotBeNull();
            result!.Success.Should().BeTrue();
            result.Channels.Should().Contain("Email");
            result.Channels.Should().Contain("SMS");

            // Verify communication service was called
            _communicationServiceMock.Verify(x => x.SendNotificationAsync(
                It.Is<NotificationRequest>(r => r.Type == NotificationType.SubscriptionReminder),
                It.IsAny<CancellationToken>()), Times.AtLeast(2)); // Once for each channel
        }

        [Fact]
        public async Task SendManualNotification_WithScheduling_ShouldScheduleCorrectly()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var scheduledAt = DateTime.UtcNow.AddHours(2);

            var request = new SendManualNotificationRequest
            {
                UserId = userId,
                NotificationType = NotificationType.Custom,
                Channels = new List<string> { "Email" },
                CustomMessage = "This is a scheduled notification",
                ScheduledAt = scheduledAt,
                Language = "en"
            };

            _communicationServiceMock.Setup(x => x.ScheduleNotificationAsync(
                It.IsAny<NotificationRequest>(), It.IsAny<DateTime>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new NotificationResult { Success = true, NotificationId = Guid.NewGuid().ToString() });

            // Act
            var response = await _client.PostAsJsonAsync("/api/admin/notifications/subscription", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);

            var result = await response.Content.ReadFromJsonAsync<SendManualNotificationResponse>();
            result.Should().NotBeNull();
            result!.Success.Should().BeTrue();
            result.ScheduledAt.Should().Be(scheduledAt);

            // Verify scheduling was called
            _communicationServiceMock.Verify(x => x.ScheduleNotificationAsync(
                It.IsAny<NotificationRequest>(), scheduledAt, It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task TriggerSubscriptionAlerts_WithBulkRequest_ShouldProcessSuccessfully()
        {
            // Arrange
            var request = new TriggerSubscriptionAlertsRequest
            {
                SubscriptionStatuses = new List<SubscriptionStatus> { SubscriptionStatus.Active },
                ExpiryDateFrom = DateTime.UtcNow,
                ExpiryDateTo = DateTime.UtcNow.AddDays(7),
                AlertType = NotificationType.ExpiryWarning,
                Channels = new List<string> { "Email" },
                Language = "en",
                DryRun = false,
                BatchSize = 10
            };

            _communicationServiceMock.Setup(x => x.SendBulkNotificationsAsync(
                It.IsAny<List<NotificationRequest>>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new BulkNotificationResult
                {
                    Success = true,
                    TotalRequests = 5,
                    SuccessfulSends = 5,
                    FailedSends = 0
                });

            // Act
            var response = await _client.PostAsJsonAsync("/api/admin/notifications/subscription-alerts", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);

            var result = await response.Content.ReadFromJsonAsync<TriggerSubscriptionAlertsResponse>();
            result.Should().NotBeNull();
            result!.Success.Should().BeTrue();
            result.TotalSubscriptions.Should().BeGreaterThanOrEqualTo(0);
        }

        [Fact]
        public async Task TriggerSubscriptionAlerts_WithDryRun_ShouldNotSendNotifications()
        {
            // Arrange
            var request = new TriggerSubscriptionAlertsRequest
            {
                SubscriptionStatuses = new List<SubscriptionStatus> { SubscriptionStatus.Active },
                AlertType = NotificationType.SubscriptionReminder,
                Channels = new List<string> { "Email" },
                DryRun = true
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/admin/notifications/subscription-alerts", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);

            var result = await response.Content.ReadFromJsonAsync<TriggerSubscriptionAlertsResponse>();
            result.Should().NotBeNull();
            result!.Success.Should().BeTrue();
            result.DryRun.Should().BeTrue();

            // Verify no actual notifications were sent
            _communicationServiceMock.Verify(x => x.SendNotificationAsync(
                It.IsAny<NotificationRequest>(), It.IsAny<CancellationToken>()), Times.Never);

            _communicationServiceMock.Verify(x => x.SendBulkNotificationsAsync(
                It.IsAny<List<NotificationRequest>>(), It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task GetNotificationHistory_WithFiltering_ShouldReturnFilteredResults()
        {
            // Arrange
            var subscriptionId = Guid.NewGuid();
            var fromDate = DateTime.UtcNow.AddDays(-7);
            var toDate = DateTime.UtcNow;

            // Act
            var response = await _client.GetAsync(
                $"/api/admin/notifications/history?subscriptionId={subscriptionId}&fromDate={fromDate:O}&toDate={toDate:O}&pageSize=20");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);

            var result = await response.Content.ReadFromJsonAsync<NotificationHistoryResponse>();
            result.Should().NotBeNull();
            result!.Items.Should().NotBeNull();
        }

        [Fact]
        public async Task SendNotification_WithInvalidChannel_ShouldReturnBadRequest()
        {
            // Arrange
            var request = new SendManualNotificationRequest
            {
                UserId = Guid.NewGuid(),
                NotificationType = NotificationType.Custom,
                Channels = new List<string> { "InvalidChannel" },
                CustomMessage = "Test message"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/admin/notifications/subscription", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        }

        [Fact]
        public async Task SendNotification_WithoutAdminRole_ShouldReturnForbidden()
        {
            // Arrange
            var clientWithoutAdminRole = _factory.CreateClient();
            clientWithoutAdminRole.DefaultRequestHeaders.Add("Authorization", "Bearer test-user-token");

            var request = new SendManualNotificationRequest
            {
                UserId = Guid.NewGuid(),
                NotificationType = NotificationType.Custom,
                Channels = new List<string> { "Email" },
                CustomMessage = "Test message"
            };

            // Act
            var response = await clientWithoutAdminRole.PostAsJsonAsync("/api/admin/notifications/subscription", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.Forbidden);
        }

        [Fact]
        public async Task SendNotification_WithRateLimitExceeded_ShouldReturnTooManyRequests()
        {
            // Arrange
            var request = new SendManualNotificationRequest
            {
                UserId = Guid.NewGuid(),
                NotificationType = NotificationType.Custom,
                Channels = new List<string> { "Email" },
                CustomMessage = "Rate limit test"
            };

            _communicationServiceMock.Setup(x => x.SendNotificationAsync(It.IsAny<NotificationRequest>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new NotificationResult { Success = true, NotificationId = Guid.NewGuid().ToString() });

            // Act - Send multiple requests rapidly to trigger rate limiting
            var tasks = new List<Task<HttpResponseMessage>>();
            for (int i = 0; i < 60; i++) // Exceed the rate limit
            {
                tasks.Add(_client.PostAsJsonAsync("/api/admin/notifications/subscription", request));
            }

            var responses = await Task.WhenAll(tasks);

            // Assert - At least some requests should be rate limited
            responses.Should().Contain(r => r.StatusCode == HttpStatusCode.TooManyRequests);
        }

        [Fact]
        public async Task NotificationAnalytics_ShouldReturnCorrectMetrics()
        {
            // Arrange
            var fromDate = DateTime.UtcNow.AddDays(-30);
            var toDate = DateTime.UtcNow;

            // Act
            var response = await _client.GetAsync(
                $"/api/admin/dashboard/notification-analytics?fromDate={fromDate:O}&toDate={toDate:O}");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);

            var analytics = await response.Content.ReadFromJsonAsync<NotificationAnalyticsDto>();
            analytics.Should().NotBeNull();
            analytics!.FromDate.Should().Be(fromDate.Date);
            analytics.ToDate.Should().Be(toDate.Date);
            analytics.NotificationsByStatus.Should().NotBeNull();
            analytics.NotificationsByChannel.Should().NotBeNull();
        }

        [Fact]
        public async Task NotificationPreview_ShouldReturnRenderedContent()
        {
            // Arrange
            var request = new PreviewNotificationRequest
            {
                NotificationType = NotificationType.SubscriptionReminder,
                Channel = "Email",
                Variables = new Dictionary<string, string>
                {
                    ["UserName"] = "John Doe",
                    ["PlanName"] = "Premium Plan"
                }
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/admin/dashboard/preview-notification", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);

            var preview = await response.Content.ReadFromJsonAsync<NotificationPreviewDto>();
            preview.Should().NotBeNull();
            preview!.Subject.Should().NotBeEmpty();
            preview.Body.Should().NotBeEmpty();
            preview.Channel.Should().Be("Email");
        }
    }
}
