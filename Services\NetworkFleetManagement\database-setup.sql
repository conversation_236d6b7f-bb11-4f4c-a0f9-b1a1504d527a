-- Network & Fleet Management Service Database Setup
-- This script sets up the PostgreSQL database for the Network & Fleet Management Service

-- Create database (run this as superuser)
-- CREATE DATABASE "TLI_NetworkFleetManagement" WITH OWNER = timescale;

-- Connect to the database
\c "TLI_NetworkFleetManagement";

-- Create schema
CREATE SCHEMA IF NOT EXISTS networkfleet;

-- Set default schema
SET search_path TO networkfleet;

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create enum types
CREATE TYPE carrier_status AS ENUM ('Pending', 'Active', 'Suspended', 'Inactive', 'Blacklisted', 'UnderReview');
CREATE TYPE onboarding_status AS ENUM ('NotStarted', 'DocumentsUploaded', 'DocumentsVerified', 'BackgroundCheckPending', 'BackgroundCheckCompleted', 'Approved', 'Rejected', 'RequiresAdditionalInfo');
CREATE TYPE vehicle_status AS ENUM ('Available', 'InUse', 'Maintenance', 'OutOfService', 'Retired');
CREATE TYPE vehicle_type AS ENUM ('Truck', 'Van', 'Trailer', 'Container', 'Pickup', 'Motorcycle', 'Bicycle', 'Other');
CREATE TYPE driver_status AS ENUM ('Available', 'OnTrip', 'OffDuty', 'OnBreak', 'Suspended', 'Inactive');
CREATE TYPE document_type AS ENUM ('DrivingLicense', 'AadharCard', 'PANCard', 'ProfilePhoto', 'MedicalCertificate', 'RegistrationCertificate', 'InsuranceCertificate', 'FitnessCertificate', 'PollutionCertificate', 'PermitCertificate', 'BusinessLicense', 'TaxCertificate', 'BankDetails', 'CompanyRegistration');
CREATE TYPE network_relationship_status AS ENUM ('Pending', 'Active', 'Suspended', 'Terminated', 'Blocked');

-- Grant permissions to timescale user
GRANT USAGE ON SCHEMA networkfleet TO timescale;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA networkfleet TO timescale;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA networkfleet TO timescale;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA networkfleet TO timescale;

-- Set default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA networkfleet GRANT ALL ON TABLES TO timescale;
ALTER DEFAULT PRIVILEGES IN SCHEMA networkfleet GRANT ALL ON SEQUENCES TO timescale;
ALTER DEFAULT PRIVILEGES IN SCHEMA networkfleet GRANT ALL ON FUNCTIONS TO timescale;

-- Create indexes for performance
-- These will be created automatically by Entity Framework migrations
-- but we can add additional indexes here if needed

COMMENT ON SCHEMA networkfleet IS 'Network & Fleet Management Service schema containing all tables for carrier network management, fleet operations, and driver management';

-- Sample data can be inserted here for testing
-- INSERT INTO networkfleet.carriers (...) VALUES (...);

COMMIT;
