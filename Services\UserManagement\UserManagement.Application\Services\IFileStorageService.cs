namespace UserManagement.Application.Services
{
    public interface IFileStorageService
    {
        /// <summary>
        /// Save export file to storage and return the file URL
        /// </summary>
        Task<string> SaveExportFileAsync(string fileName, string content, string contentType);
        
        /// <summary>
        /// Save export file to storage and return the file URL
        /// </summary>
        Task<string> SaveExportFileAsync(string fileName, byte[] content, string contentType);
        
        /// <summary>
        /// Get file URL for download
        /// </summary>
        Task<string> GetFileUrlAsync(string fileName);
        
        /// <summary>
        /// Delete export file
        /// </summary>
        Task<bool> DeleteFileAsync(string fileName);
        
        /// <summary>
        /// Check if file exists
        /// </summary>
        Task<bool> FileExistsAsync(string fileName);
        
        /// <summary>
        /// Get file size in bytes
        /// </summary>
        Task<long> GetFileSizeAsync(string fileName);
        
        /// <summary>
        /// Clean up expired export files
        /// </summary>
        Task CleanupExpiredFilesAsync(DateTime cutoffDate);
    }
}
