using Shared.Domain.Common;
using TripManagement.Domain.Enums;
using TripManagement.Domain.ValueObjects;

namespace TripManagement.Domain.Entities;

public class Driver : BaseEntity
{
    public Guid UserId { get; private set; }
    public string FirstName { get; private set; } = string.Empty;
    public string LastName { get; private set; } = string.Empty;
    public string PhoneNumber { get; private set; } = string.Empty;
    public string Email { get; private set; } = string.Empty;
    public string LicenseNumber { get; private set; } = string.Empty;
    public DateTime LicenseExpiryDate { get; private set; }
    public DriverStatus Status { get; private set; }
    public new DateTime CreatedAt { get; private set; }
    public DateTime? LastActiveAt { get; private set; }
    public Location? CurrentLocation { get; private set; }
    public string? ProfilePhotoUrl { get; private set; }
    public decimal Rating { get; private set; }
    public int TotalTrips { get; private set; }
    public int CompletedTrips { get; private set; }
    public string? Notes { get; private set; }

    // Navigation properties
    private readonly List<Trip> _trips = new();
    public IReadOnlyCollection<Trip> Trips => _trips.AsReadOnly();

    private readonly List<DriverDocument> _documents = new();
    public IReadOnlyCollection<DriverDocument> Documents => _documents.AsReadOnly();

    private readonly List<DriverPerformance> _performanceRecords = new();
    public IReadOnlyCollection<DriverPerformance> PerformanceRecords => _performanceRecords.AsReadOnly();

    // Parameterless constructor for EF Core
    private Driver() { }

    public Driver(
        Guid userId,
        string firstName,
        string lastName,
        string phoneNumber,
        string email,
        string licenseNumber,
        DateTime licenseExpiryDate,
        string? profilePhotoUrl = null,
        string? notes = null)
    {
        if (string.IsNullOrWhiteSpace(firstName))
            throw new ArgumentException("First name cannot be empty", nameof(firstName));

        if (string.IsNullOrWhiteSpace(lastName))
            throw new ArgumentException("Last name cannot be empty", nameof(lastName));

        if (string.IsNullOrWhiteSpace(phoneNumber))
            throw new ArgumentException("Phone number cannot be empty", nameof(phoneNumber));

        if (string.IsNullOrWhiteSpace(email))
            throw new ArgumentException("Email cannot be empty", nameof(email));

        if (string.IsNullOrWhiteSpace(licenseNumber))
            throw new ArgumentException("License number cannot be empty", nameof(licenseNumber));

        if (licenseExpiryDate <= DateTime.UtcNow)
            throw new ArgumentException("License expiry date must be in the future", nameof(licenseExpiryDate));

        UserId = userId;
        FirstName = firstName;
        LastName = lastName;
        PhoneNumber = phoneNumber;
        Email = email;
        LicenseNumber = licenseNumber;
        LicenseExpiryDate = licenseExpiryDate;
        Status = DriverStatus.Available;
        CreatedAt = DateTime.UtcNow;
        ProfilePhotoUrl = profilePhotoUrl;
        Rating = 0;
        TotalTrips = 0;
        CompletedTrips = 0;
        Notes = notes;
    }

    public string FullName => $"{FirstName} {LastName}";

    public bool IsLicenseValid => LicenseExpiryDate > DateTime.UtcNow;

    public bool IsAvailable => Status == DriverStatus.Available && IsLicenseValid;

    public void UpdateStatus(DriverStatus newStatus)
    {
        Status = newStatus;
        LastActiveAt = DateTime.UtcNow;
    }

    public void UpdateLocation(Location location)
    {
        CurrentLocation = location;
        LastActiveAt = DateTime.UtcNow;
    }

    public void UpdateContactInfo(string phoneNumber, string email)
    {
        if (string.IsNullOrWhiteSpace(phoneNumber))
            throw new ArgumentException("Phone number cannot be empty", nameof(phoneNumber));

        if (string.IsNullOrWhiteSpace(email))
            throw new ArgumentException("Email cannot be empty", nameof(email));

        PhoneNumber = phoneNumber;
        Email = email;
    }

    public void UpdateLicense(string licenseNumber, DateTime expiryDate)
    {
        if (string.IsNullOrWhiteSpace(licenseNumber))
            throw new ArgumentException("License number cannot be empty", nameof(licenseNumber));

        if (expiryDate <= DateTime.UtcNow)
            throw new ArgumentException("License expiry date must be in the future", nameof(expiryDate));

        LicenseNumber = licenseNumber;
        LicenseExpiryDate = expiryDate;
    }

    public void AddTrip(Trip trip)
    {
        _trips.Add(trip);
        TotalTrips++;
    }

    public void CompleteTrip()
    {
        CompletedTrips++;
        UpdateStatus(DriverStatus.Available);
    }

    public void UpdateRating(decimal newRating)
    {
        if (newRating < 0 || newRating > 5)
            throw new ArgumentException("Rating must be between 0 and 5", nameof(newRating));

        Rating = newRating;
    }

    public void AddDocument(DriverDocument document)
    {
        _documents.Add(document);
    }

    public void Suspend(string reason)
    {
        Status = DriverStatus.Suspended;
        Notes = $"Suspended: {reason}";
    }

    public void Reactivate()
    {
        if (!IsLicenseValid)
            throw new InvalidOperationException("Cannot reactivate driver with expired license");

        Status = DriverStatus.Available;
    }

    public void AddPerformanceRecord(DriverPerformance performanceRecord)
    {
        _performanceRecords.Add(performanceRecord);
    }
}

public class DriverDocument : BaseEntity
{
    public Guid DriverId { get; private set; }
    public DocumentType DocumentType { get; private set; }
    public string FileName { get; private set; } = string.Empty;
    public string FileUrl { get; private set; } = string.Empty;
    public string? Description { get; private set; }
    public DateTime UploadedAt { get; private set; }
    public DateTime? ExpiryDate { get; private set; }
    public bool IsVerified { get; private set; }
    public DateTime? VerifiedAt { get; private set; }
    public string? VerifiedBy { get; private set; }

    // Navigation properties
    public Driver Driver { get; private set; } = null!;

    // Parameterless constructor for EF Core
    private DriverDocument() { }

    public DriverDocument(
        Guid driverId,
        DocumentType documentType,
        string fileName,
        string fileUrl,
        string? description = null,
        DateTime? expiryDate = null)
    {
        if (string.IsNullOrWhiteSpace(fileName))
            throw new ArgumentException("File name cannot be empty", nameof(fileName));

        if (string.IsNullOrWhiteSpace(fileUrl))
            throw new ArgumentException("File URL cannot be empty", nameof(fileUrl));

        DriverId = driverId;
        DocumentType = documentType;
        FileName = fileName;
        FileUrl = fileUrl;
        Description = description;
        UploadedAt = DateTime.UtcNow;
        ExpiryDate = expiryDate;
        IsVerified = false;
    }

    public bool IsExpired => ExpiryDate.HasValue && ExpiryDate.Value <= DateTime.UtcNow;

    public void Verify(string verifiedBy)
    {
        if (string.IsNullOrWhiteSpace(verifiedBy))
            throw new ArgumentException("Verified by cannot be empty", nameof(verifiedBy));

        IsVerified = true;
        VerifiedAt = DateTime.UtcNow;
        VerifiedBy = verifiedBy;
    }

    public void Reject()
    {
        IsVerified = false;
        VerifiedAt = null;
        VerifiedBy = null;
    }
}

public class DriverPerformance : BaseEntity
{
    public Guid DriverId { get; private set; }
    public Guid TripId { get; private set; }
    public object Metrics { get; private set; } = null!;
    public DateTime EvaluationDate
    { get; private set; }
    public string? EvaluatedBy { get; private set; }
    public string? Notes { get; private set; }

    // Navigation properties
    public Driver Driver { get; private set; } = null!;
    public Trip Trip { get; private set; } = null!;

    // Parameterless constructor for EF Core
    private DriverPerformance() { }

    public DriverPerformance(
        Guid driverId,
        Guid tripId,
        object metrics,
        DateTime evaluationDate,
        string? evaluatedBy = null,
        string? notes = null)
    {
        DriverId = driverId;
        TripId = tripId;
        Metrics = metrics ?? throw new ArgumentNullException(nameof(metrics));
        EvaluationDate = evaluationDate;
        EvaluatedBy = evaluatedBy;
        Notes = notes;
    }

    public void UpdateMetrics(object newMetrics)
    {
        Metrics = newMetrics ?? throw new ArgumentNullException(nameof(newMetrics));
    }

    public void AddNotes(string additionalNotes)
    {
        if (!string.IsNullOrWhiteSpace(additionalNotes))
        {
            Notes = string.IsNullOrWhiteSpace(Notes)
                ? additionalNotes
                : $"{Notes}\n{additionalNotes}";
        }
    }
}


