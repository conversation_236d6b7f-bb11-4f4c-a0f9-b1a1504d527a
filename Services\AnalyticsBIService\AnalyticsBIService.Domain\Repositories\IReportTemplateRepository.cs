using AnalyticsBIService.Domain.Entities;
using Shared.Infrastructure.Repositories;

namespace AnalyticsBIService.Domain.Repositories;

/// <summary>
/// Repository interface for report template operations
/// </summary>
public interface IReportTemplateRepository : IRepositoryBase<ReportTemplate, Guid>
{
    /// <summary>
    /// Get report templates by category
    /// </summary>
    Task<IEnumerable<ReportTemplate>> GetByCategoryAsync(string category, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get active report templates
    /// </summary>
    Task<IEnumerable<ReportTemplate>> GetActiveTemplatesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get report templates by user role
    /// </summary>
    Task<IEnumerable<ReportTemplate>> GetByUserRoleAsync(string userRole, CancellationToken cancellationToken = default);

    /// <summary>
    /// Search report templates by name or description
    /// </summary>
    Task<IEnumerable<ReportTemplate>> SearchAsync(string searchTerm, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get templates by transport company
    /// </summary>
    Task<IEnumerable<ReportTemplate>> GetByTransportCompanyAsync(Guid transportCompanyId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Add custom template
    /// </summary>
    Task<ReportTemplate> AddCustomTemplateAsync(ReportTemplate template, CancellationToken cancellationToken = default);
}
