using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using AnalyticsBIService.Domain.Entities;

namespace AnalyticsBIService.Infrastructure.Persistence.Configurations;

public class ReportSectionConfiguration : IEntityTypeConfiguration<ReportSection>
{
    public void Configure(EntityTypeBuilder<ReportSection> builder)
    {
        builder.ToTable("report_sections");

        builder.HasKey(rs => rs.Id);

        builder.Property(rs => rs.Name)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(rs => rs.Type)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(rs => rs.Order)
            .IsRequired();

        builder.Property(rs => rs.Content)
            .HasColumnType("text");

        builder.Property(rs => rs.CreatedAt)
            .IsRequired();

        builder.Property(rs => rs.UpdatedAt)
            .IsRequired();

        // Configure Configuration as JSON
        builder.Property(rs => rs.Configuration)
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        // Configure Data as JSON
        builder.Property(rs => rs.Data)
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        // Configure indexes
        builder.HasIndex(rs => rs.Type)
            .HasDatabaseName("IX_report_sections_type");

        builder.HasIndex(rs => rs.Order)
            .HasDatabaseName("IX_report_sections_order");

        builder.HasIndex(rs => rs.CreatedAt)
            .HasDatabaseName("IX_report_sections_created_at");

        // Foreign key will be configured by Report entity
        builder.HasIndex("ReportId")
            .HasDatabaseName("IX_report_sections_report_id");
    }
}
