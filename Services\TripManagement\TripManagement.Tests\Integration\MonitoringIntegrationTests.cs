using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using TripManagement.Application.Interfaces;
using TripManagement.Domain.Enums;
using TripManagement.Infrastructure.HealthChecks;
using TripManagement.Infrastructure.Monitoring;
using TripManagement.Infrastructure.Persistence;
using TripManagement.Infrastructure.Repositories;
using Xunit;

namespace TripManagement.Tests.Integration;

public class MonitoringIntegrationTests : IDisposable
{
    private readonly TripManagementDbContext _context;
    private readonly ITripRepository _tripRepository;
    private readonly IDriverRepository _driverRepository;
    private readonly IVehicleRepository _vehicleRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly Mock<INotificationService> _notificationServiceMock;
    private readonly Mock<IMessageBroker> _messageBrokerMock;

    public MonitoringIntegrationTests()
    {
        var options = new DbContextOptionsBuilder<TripManagementDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new TripManagementDbContext(options);
        _tripRepository = new TripRepository(_context);
        _driverRepository = new DriverRepository(_context);
        _vehicleRepository = new VehicleRepository(_context);
        _unitOfWork = new UnitOfWork(_context);
        _notificationServiceMock = new Mock<INotificationService>();
        _messageBrokerMock = new Mock<IMessageBroker>();
    }

    [Fact]
    public async Task DatabaseHealthCheck_Should_Return_Healthy_When_Database_Available()
    {
        // Arrange
        var healthCheck = new DatabaseHealthCheck(_context);
        var context = new HealthCheckContext();

        // Act
        var result = await healthCheck.CheckHealthAsync(context);

        // Assert
        result.Status.Should().Be(HealthStatus.Healthy);
        result.Description.Should().Be("Database is healthy");
        result.Data.Should().ContainKey("database");
        result.Data["database"].Should().Be("Connected");
        result.Data.Should().ContainKey("tripCount");
    }

    [Fact]
    public async Task BusinessLogicHealthCheck_Should_Return_Healthy_With_Normal_Conditions()
    {
        // Arrange
        await CreateTestDataForHealthCheck();
        
        var loggerMock = new Mock<ILogger<BusinessLogicHealthCheck>>();
        var healthCheck = new BusinessLogicHealthCheck(
            _tripRepository,
            _driverRepository,
            _vehicleRepository,
            loggerMock.Object);
        
        var context = new HealthCheckContext();

        // Act
        var result = await healthCheck.CheckHealthAsync(context);

        // Assert
        result.Status.Should().Be(HealthStatus.Healthy);
        result.Description.Should().Be("Business logic is healthy");
        result.Data.Should().ContainKey("activeTrips");
        result.Data.Should().ContainKey("availableDrivers");
        result.Data.Should().ContainKey("availableVehicles");
    }

    [Fact]
    public async Task BusinessLogicHealthCheck_Should_Return_Degraded_With_High_Exception_Count()
    {
        // Arrange
        await CreateTestDataWithManyExceptions();
        
        var loggerMock = new Mock<ILogger<BusinessLogicHealthCheck>>();
        var healthCheck = new BusinessLogicHealthCheck(
            _tripRepository,
            _driverRepository,
            _vehicleRepository,
            loggerMock.Object);
        
        var context = new HealthCheckContext();

        // Act
        var result = await healthCheck.CheckHealthAsync(context);

        // Assert
        result.Status.Should().Be(HealthStatus.Degraded);
        result.Description.Should().Be("Business logic health check has warnings");
        result.Data.Should().ContainKey("warnings");
        
        var warnings = result.Data["warnings"] as List<string>;
        warnings.Should().Contain(w => w.Contains("High number of trips in exception status"));
    }

    [Fact]
    public async Task MetricsCollectionService_Should_Collect_Metrics_Successfully()
    {
        // Arrange
        await CreateTestDataForMetrics();
        
        var loggerMock = new Mock<ILogger<MetricsCollectionService>>();
        var metricsService = new MetricsCollectionService(
            _tripRepository,
            _driverRepository,
            _vehicleRepository,
            loggerMock.Object);

        // Act
        var metrics = await metricsService.CollectMetricsAsync();

        // Assert
        metrics.Should().NotBeNull();
        metrics.ActiveTrips.Should().BeGreaterThan(0);
        metrics.AvailableDrivers.Should().BeGreaterThan(0);
        metrics.AvailableVehicles.Should().BeGreaterThan(0);
        metrics.CollectedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromMinutes(1));
    }

    [Fact]
    public async Task AlertingService_Should_Send_Critical_Alert_For_High_Overdue_Trips()
    {
        // Arrange
        var alertingConfig = new AlertingConfiguration
        {
            CriticalOverdueTripsThreshold = 5,
            CriticalAlertRecipients = new List<Guid> { Guid.NewGuid() }
        };

        var configMock = new Mock<IOptions<AlertingConfiguration>>();
        configMock.Setup(c => c.Value).Returns(alertingConfig);

        var loggerMock = new Mock<ILogger<AlertingService>>();
        var alertingService = new AlertingService(
            _notificationServiceMock.Object,
            _messageBrokerMock.Object,
            loggerMock.Object,
            configMock.Object);

        var metrics = new TripManagementMetrics
        {
            ActiveTrips = 20,
            OverdueTrips = 10, // Exceeds threshold of 5
            TripsInException = 2,
            AvailableDrivers = 15,
            AvailableVehicles = 12,
            OnTimeDeliveryRate = 85.0,
            CollectedAt = DateTime.UtcNow
        };

        // Act
        await alertingService.MonitorMetricsAndAlertAsync(metrics);

        // Assert
        _notificationServiceMock.Verify(n => n.SendNotificationAsync(
            It.IsAny<Guid>(),
            It.Is<string>(title => title.Contains("CRITICAL")),
            It.Is<string>(message => message.Contains("overdue trips")),
            It.IsAny<CancellationToken>()), Times.Once);

        _messageBrokerMock.Verify(m => m.PublishAsync(
            "alerts.critical",
            It.IsAny<object>(),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task AlertingService_Should_Send_Warning_Alert_For_Low_Driver_Availability()
    {
        // Arrange
        var alertingConfig = new AlertingConfiguration
        {
            MinimumAvailableDrivers = 10,
            WarningAlertRecipients = new List<Guid> { Guid.NewGuid() }
        };

        var configMock = new Mock<IOptions<AlertingConfiguration>>();
        configMock.Setup(c => c.Value).Returns(alertingConfig);

        var loggerMock = new Mock<ILogger<AlertingService>>();
        var alertingService = new AlertingService(
            _notificationServiceMock.Object,
            _messageBrokerMock.Object,
            loggerMock.Object,
            configMock.Object);

        var metrics = new TripManagementMetrics
        {
            ActiveTrips = 15,
            OverdueTrips = 2,
            TripsInException = 1,
            AvailableDrivers = 5, // Below threshold of 10
            AvailableVehicles = 12,
            OnTimeDeliveryRate = 85.0,
            CollectedAt = DateTime.UtcNow
        };

        // Act
        await alertingService.MonitorMetricsAndAlertAsync(metrics);

        // Assert
        _notificationServiceMock.Verify(n => n.SendNotificationAsync(
            It.IsAny<Guid>(),
            It.Is<string>(title => title.Contains("WARNING")),
            It.Is<string>(message => message.Contains("driver availability")),
            It.IsAny<CancellationToken>()), Times.Once);

        _messageBrokerMock.Verify(m => m.PublishAsync(
            "alerts.warning",
            It.IsAny<object>(),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public void MetricsCollectionService_Should_Record_Trip_Events()
    {
        // Arrange
        var loggerMock = new Mock<ILogger<MetricsCollectionService>>();
        var metricsService = new MetricsCollectionService(
            _tripRepository,
            _driverRepository,
            _vehicleRepository,
            loggerMock.Object);

        var tripId = Guid.NewGuid();
        var carrierId = Guid.NewGuid();

        // Act & Assert - Should not throw exceptions
        metricsService.RecordTripCreated(tripId, carrierId, false);
        metricsService.RecordTripCompleted(tripId, TimeSpan.FromHours(8), 500.0, false);
        metricsService.RecordTripCancelled(tripId, "Customer request");
        metricsService.RecordExceptionReported(tripId, ExceptionType.Delay, "High");

        // Verify logger was called for each metric recording
        loggerMock.Verify(
            l => l.Log(
                LogLevel.Debug,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("trip created")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    private async Task CreateTestDataForHealthCheck()
    {
        // Create some test trips
        var trip1 = await CreateTestTrip(TripStatus.InProgress);
        var trip2 = await CreateTestTrip(TripStatus.Assigned);
        
        // Create test drivers
        var driver1 = CreateTestDriver(DriverStatus.Available);
        var driver2 = CreateTestDriver(DriverStatus.Available);
        await _driverRepository.AddAsync(driver1);
        await _driverRepository.AddAsync(driver2);

        // Create test vehicles
        var vehicle1 = CreateTestVehicle(VehicleStatus.Available);
        var vehicle2 = CreateTestVehicle(VehicleStatus.Available);
        await _vehicleRepository.AddAsync(vehicle1);
        await _vehicleRepository.AddAsync(vehicle2);

        await _unitOfWork.SaveChangesAsync();
    }

    private async Task CreateTestDataWithManyExceptions()
    {
        // Create trips with exception status
        for (int i = 0; i < 15; i++)
        {
            await CreateTestTrip(TripStatus.Exception);
        }

        await _unitOfWork.SaveChangesAsync();
    }

    private async Task CreateTestDataForMetrics()
    {
        // Create active trips
        await CreateTestTrip(TripStatus.InProgress);
        await CreateTestTrip(TripStatus.Assigned);
        
        // Create available drivers
        var driver = CreateTestDriver(DriverStatus.Available);
        await _driverRepository.AddAsync(driver);

        // Create available vehicles
        var vehicle = CreateTestVehicle(VehicleStatus.Available);
        await _vehicleRepository.AddAsync(vehicle);

        await _unitOfWork.SaveChangesAsync();
    }

    private async Task<TripManagement.Domain.Entities.Trip> CreateTestTrip(TripStatus status)
    {
        var startLocation = new TripManagement.Domain.ValueObjects.Location(40.7128, -74.0060, DateTime.UtcNow, address: "New York, NY");
        var endLocation = new TripManagement.Domain.ValueObjects.Location(34.0522, -118.2437, DateTime.UtcNow, address: "Los Angeles, CA");
        var route = new TripManagement.Domain.ValueObjects.Route(startLocation, endLocation, estimatedDistanceKm: 3944);

        var trip = new TripManagement.Domain.Entities.Trip(
            Guid.NewGuid(),
            Guid.NewGuid(),
            route,
            DateTime.UtcNow.AddHours(1),
            DateTime.UtcNow.AddHours(41),
            "Test trip for monitoring",
            false,
            3944m);

        // Set status based on parameter
        if (status == TripStatus.Exception)
        {
            trip.AddException(ExceptionType.Delay, "Test exception");
        }
        else if (status == TripStatus.InProgress)
        {
            trip.AssignDriverAndVehicle(Guid.NewGuid(), Guid.NewGuid());
            trip.Start();
        }
        else if (status == TripStatus.Assigned)
        {
            trip.AssignDriverAndVehicle(Guid.NewGuid(), Guid.NewGuid());
        }

        await _tripRepository.AddAsync(trip);
        return trip;
    }

    private TripManagement.Domain.Entities.Driver CreateTestDriver(DriverStatus status)
    {
        var driver = new TripManagement.Domain.Entities.Driver(
            Guid.NewGuid(),
            "Test",
            "Driver",
            "+91-9876543210",
            "<EMAIL>",
            "DL123456789",
            DateTime.UtcNow.AddYears(2));

        driver.UpdateStatus(status);
        return driver;
    }

    private TripManagement.Domain.Entities.Vehicle CreateTestVehicle(VehicleStatus status)
    {
        var vehicle = new TripManagement.Domain.Entities.Vehicle(
            Guid.NewGuid(),
            "MH01AB1234",
            VehicleType.Truck,
            "Tata",
            "LPT 1618",
            2020,
            "Red",
            10000.0m,
            50.0m);

        vehicle.UpdateStatus(status);
        return vehicle;
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
