using AuditCompliance.Application.Interfaces;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Hosting;
using System.Collections.Concurrent;
using System.Text.Json;

namespace AuditCompliance.Infrastructure.Services
{
    /// <summary>
    /// Background job service implementation for processing export jobs
    /// </summary>
    public class BackgroundJobService : BackgroundService, IBackgroundJobService
    {
        private readonly ILogger<BackgroundJobService> _logger;
        private readonly IServiceProvider _serviceProvider;
        private readonly ConcurrentQueue<ExportJobDto> _jobQueue;
        private readonly ConcurrentDictionary<string, ExportJobStatusDto> _jobStatuses;
        private readonly SemaphoreSlim _semaphore;
        private readonly Timer _cleanupTimer;

        public BackgroundJobService(
            ILogger<BackgroundJobService> logger,
            IServiceProvider serviceProvider)
        {
            _logger = logger;
            _serviceProvider = serviceProvider;
            _jobQueue = new ConcurrentQueue<ExportJobDto>();
            _jobStatuses = new ConcurrentDictionary<string, ExportJobStatusDto>();
            _semaphore = new SemaphoreSlim(3, 3); // Max 3 concurrent jobs
            
            // Cleanup timer runs every hour
            _cleanupTimer = new Timer(CleanupExpiredJobs, null, TimeSpan.FromHours(1), TimeSpan.FromHours(1));
        }

        public async Task<string> QueueExportJobAsync(ExportJobDto job)
        {
            try
            {
                var jobId = Guid.NewGuid().ToString("N")[..12];
                job.JobId = jobId;

                var status = new ExportJobStatusDto
                {
                    JobId = jobId,
                    Status = ExportStatus.Queued,
                    StatusMessage = "Job queued for processing",
                    QueuedAt = DateTime.UtcNow,
                    EstimatedCompletionTime = DateTime.UtcNow.AddMinutes(EstimateCompletionTime(job)),
                    TotalRecords = EstimateTotalRecords(job),
                    AdditionalInfo = new Dictionary<string, object>
                    {
                        ["JobType"] = job.JobType,
                        ["Priority"] = job.Priority,
                        ["RequestedBy"] = job.RequestedBy
                    }
                };

                _jobStatuses.TryAdd(jobId, status);
                _jobQueue.Enqueue(job);

                _logger.LogInformation("Export job {JobId} queued for processing", jobId);
                return jobId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error queueing export job");
                throw;
            }
        }

        public async Task<ExportJobStatusDto?> GetJobStatusAsync(string jobId)
        {
            return await Task.FromResult(_jobStatuses.TryGetValue(jobId, out var status) ? status : null);
        }

        public async Task<bool> CancelJobAsync(string jobId, string reason)
        {
            try
            {
                if (_jobStatuses.TryGetValue(jobId, out var status))
                {
                    if (status.Status == ExportStatus.Queued || status.Status == ExportStatus.Processing)
                    {
                        status.Status = ExportStatus.Cancelled;
                        status.StatusMessage = $"Job cancelled: {reason}";
                        status.CompletedAt = DateTime.UtcNow;
                        
                        _logger.LogInformation("Export job {JobId} cancelled: {Reason}", jobId, reason);
                        return true;
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cancelling job {JobId}", jobId);
                return false;
            }
        }

        public async Task<List<ExportJobStatusDto>> GetJobHistoryAsync(Guid? requestedBy = null, int pageNumber = 1, int pageSize = 20)
        {
            try
            {
                var jobs = _jobStatuses.Values.AsQueryable();

                if (requestedBy.HasValue)
                {
                    jobs = jobs.Where(j => j.AdditionalInfo.ContainsKey("RequestedBy") && 
                                          j.AdditionalInfo["RequestedBy"].Equals(requestedBy.Value));
                }

                var result = jobs
                    .OrderByDescending(j => j.QueuedAt)
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();

                return await Task.FromResult(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving job history");
                throw;
            }
        }

        public async Task UpdateJobProgressAsync(string jobId, int progressPercentage, string? statusMessage = null)
        {
            try
            {
                if (_jobStatuses.TryGetValue(jobId, out var status))
                {
                    status.ProgressPercentage = Math.Min(100, Math.Max(0, progressPercentage));
                    status.StatusMessage = statusMessage ?? status.StatusMessage;
                    
                    if (status.Status == ExportStatus.Queued)
                    {
                        status.Status = ExportStatus.Processing;
                        status.StartedAt = DateTime.UtcNow;
                    }

                    _logger.LogDebug("Job {JobId} progress updated: {Progress}%", jobId, progressPercentage);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating job progress for {JobId}", jobId);
            }
        }

        public async Task CompleteJobAsync(string jobId, ExportJobResultDto result)
        {
            try
            {
                if (_jobStatuses.TryGetValue(jobId, out var status))
                {
                    status.Status = result.IsSuccess ? ExportStatus.Completed : ExportStatus.Failed;
                    status.StatusMessage = result.IsSuccess ? "Job completed successfully" : result.ErrorMessage ?? "Job failed";
                    status.CompletedAt = DateTime.UtcNow;
                    status.ProgressPercentage = 100;
                    status.RecordsProcessed = result.RecordCount;
                    
                    if (result.IsSuccess)
                    {
                        status.AdditionalInfo["FileUrl"] = result.FileUrl ?? "";
                        status.AdditionalInfo["FileName"] = result.FileName ?? "";
                        status.AdditionalInfo["FileSizeBytes"] = result.FileSizeBytes;
                    }
                    else
                    {
                        status.ErrorMessage = result.ErrorMessage;
                    }

                    _logger.LogInformation("Job {JobId} completed with status: {Status}", jobId, status.Status);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error completing job {JobId}", jobId);
            }
        }

        public async Task FailJobAsync(string jobId, string errorMessage, Exception? exception = null)
        {
            try
            {
                if (_jobStatuses.TryGetValue(jobId, out var status))
                {
                    status.Status = ExportStatus.Failed;
                    status.StatusMessage = "Job failed";
                    status.ErrorMessage = errorMessage;
                    status.CompletedAt = DateTime.UtcNow;
                    
                    if (exception != null)
                    {
                        status.AdditionalInfo["ExceptionType"] = exception.GetType().Name;
                        status.AdditionalInfo["ExceptionMessage"] = exception.Message;
                    }

                    _logger.LogError(exception, "Job {JobId} failed: {ErrorMessage}", jobId, errorMessage);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error failing job {JobId}", jobId);
            }
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("Background job service started");

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    if (_jobQueue.TryDequeue(out var job))
                    {
                        await _semaphore.WaitAsync(stoppingToken);
                        
                        // Process job in background task
                        _ = Task.Run(async () =>
                        {
                            try
                            {
                                await ProcessJobAsync(job, stoppingToken);
                            }
                            finally
                            {
                                _semaphore.Release();
                            }
                        }, stoppingToken);
                    }
                    else
                    {
                        // Wait for new jobs
                        await Task.Delay(1000, stoppingToken);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in background job service");
                    await Task.Delay(5000, stoppingToken); // Wait before retrying
                }
            }

            _logger.LogInformation("Background job service stopped");
        }

        private async Task ProcessJobAsync(ExportJobDto job, CancellationToken cancellationToken)
        {
            var jobId = job.JobId;
            
            try
            {
                await UpdateJobProgressAsync(jobId, 0, "Starting job processing");

                using var scope = _serviceProvider.CreateScope();
                
                // Process based on job type
                var result = job.JobType switch
                {
                    "EnhancedAuditExport" => await ProcessEnhancedAuditExportAsync(job, scope, cancellationToken),
                    "BulkAuditExport" => await ProcessBulkAuditExportAsync(job, scope, cancellationToken),
                    "CustomReportGeneration" => await ProcessCustomReportGenerationAsync(job, scope, cancellationToken),
                    "RetentionPolicyExecution" => await ProcessRetentionPolicyExecutionAsync(job, scope, cancellationToken),
                    _ => throw new ArgumentException($"Unknown job type: {job.JobType}")
                };

                await CompleteJobAsync(jobId, result);

                // Send notification if email provided
                if (!string.IsNullOrEmpty(job.NotificationEmail))
                {
                    await SendCompletionNotificationAsync(job, result, scope);
                }
            }
            catch (OperationCanceledException)
            {
                await UpdateJobProgressAsync(jobId, 0, "Job cancelled");
                _logger.LogInformation("Job {JobId} was cancelled", jobId);
            }
            catch (Exception ex)
            {
                await FailJobAsync(jobId, ex.Message, ex);
            }
        }

        private async Task<ExportJobResultDto> ProcessEnhancedAuditExportAsync(ExportJobDto job, IServiceScope scope, CancellationToken cancellationToken)
        {
            // Simulate processing with progress updates
            for (int i = 0; i <= 100; i += 10)
            {
                await UpdateJobProgressAsync(job.JobId, i, $"Processing audit export: {i}%");
                await Task.Delay(500, cancellationToken); // Simulate work
                
                if (cancellationToken.IsCancellationRequested)
                    throw new OperationCanceledException();
            }

            return new ExportJobResultDto
            {
                JobId = job.JobId,
                IsSuccess = true,
                FileName = $"audit_export_{DateTime.UtcNow:yyyyMMdd_HHmmss}.csv",
                FileUrl = "https://storage.example.com/exports/audit_export.csv",
                RecordCount = 1000,
                FileSizeBytes = 50000,
                ProcessingTime = TimeSpan.FromMinutes(2)
            };
        }

        private async Task<ExportJobResultDto> ProcessBulkAuditExportAsync(ExportJobDto job, IServiceScope scope, CancellationToken cancellationToken)
        {
            // Simulate bulk processing
            await Task.Delay(3000, cancellationToken);
            
            return new ExportJobResultDto
            {
                JobId = job.JobId,
                IsSuccess = true,
                FileName = $"bulk_audit_export_{DateTime.UtcNow:yyyyMMdd_HHmmss}.zip",
                FileUrl = "https://storage.example.com/exports/bulk_audit_export.zip",
                RecordCount = 5000,
                FileSizeBytes = 250000,
                ProcessingTime = TimeSpan.FromMinutes(5)
            };
        }

        private async Task<ExportJobResultDto> ProcessCustomReportGenerationAsync(ExportJobDto job, IServiceScope scope, CancellationToken cancellationToken)
        {
            // Simulate report generation
            await Task.Delay(2000, cancellationToken);
            
            return new ExportJobResultDto
            {
                JobId = job.JobId,
                IsSuccess = true,
                FileName = $"custom_report_{DateTime.UtcNow:yyyyMMdd_HHmmss}.pdf",
                FileUrl = "https://storage.example.com/reports/custom_report.pdf",
                RecordCount = 500,
                FileSizeBytes = 100000,
                ProcessingTime = TimeSpan.FromMinutes(1)
            };
        }

        private async Task<ExportJobResultDto> ProcessRetentionPolicyExecutionAsync(ExportJobDto job, IServiceScope scope, CancellationToken cancellationToken)
        {
            // Simulate retention policy execution
            await Task.Delay(4000, cancellationToken);
            
            return new ExportJobResultDto
            {
                JobId = job.JobId,
                IsSuccess = true,
                FileName = $"retention_execution_{DateTime.UtcNow:yyyyMMdd_HHmmss}.log",
                FileUrl = "https://storage.example.com/logs/retention_execution.log",
                RecordCount = 2000,
                FileSizeBytes = 75000,
                ProcessingTime = TimeSpan.FromMinutes(3)
            };
        }

        private async Task SendCompletionNotificationAsync(ExportJobDto job, ExportJobResultDto result, IServiceScope scope)
        {
            try
            {
                // Implementation would send actual notification
                _logger.LogInformation("Sending completion notification for job {JobId} to {Email}", job.JobId, job.NotificationEmail);
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending completion notification for job {JobId}", job.JobId);
            }
        }

        private void CleanupExpiredJobs(object? state)
        {
            try
            {
                var expiredJobs = _jobStatuses.Values
                    .Where(j => j.CompletedAt.HasValue && j.CompletedAt.Value < DateTime.UtcNow.AddDays(-7))
                    .ToList();

                foreach (var job in expiredJobs)
                {
                    _jobStatuses.TryRemove(job.JobId, out _);
                }

                if (expiredJobs.Any())
                {
                    _logger.LogInformation("Cleaned up {Count} expired job records", expiredJobs.Count);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during job cleanup");
            }
        }

        private int EstimateCompletionTime(ExportJobDto job)
        {
            return job.JobType switch
            {
                "EnhancedAuditExport" => 5,
                "BulkAuditExport" => 15,
                "CustomReportGeneration" => 3,
                "RetentionPolicyExecution" => 10,
                _ => 5
            };
        }

        private int EstimateTotalRecords(ExportJobDto job)
        {
            // This would be calculated based on actual parameters
            return job.JobType switch
            {
                "EnhancedAuditExport" => 1000,
                "BulkAuditExport" => 5000,
                "CustomReportGeneration" => 500,
                "RetentionPolicyExecution" => 2000,
                _ => 1000
            };
        }

        public override void Dispose()
        {
            _cleanupTimer?.Dispose();
            _semaphore?.Dispose();
            base.Dispose();
        }
    }
}
