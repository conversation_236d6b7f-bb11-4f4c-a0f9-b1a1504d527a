{"ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=TLI_Communication;User Id=timescale;Password=timescale", "Redis": "localhost:6379"}, "SignalR": {"EnableDetailedErrors": true, "KeepAliveInterval": "00:00:15", "ClientTimeoutInterval": "00:00:30", "HandshakeTimeout": "00:00:15", "MaximumReceiveMessageSize": 32768, "StreamBufferCapacity": 10, "EnableRedisBackplane": true}, "Chat": {"MaxMessageLength": 4000, "MaxConversationParticipants": 50, "MessageRetentionDays": 365, "OfflineMessageRetentionDays": 30, "EnableOfflineNotifications": true, "EnableTypingIndicators": true, "EnableReadReceipts": true, "EnableMessageSearch": true, "MaxSearchResults": 100, "ConversationSettings": {"AllowDirectMessages": true, "AllowGroupMessages": true, "AllowTripConversations": true, "AllowOrderConversations": true, "AllowSupportConversations": true, "AutoCreateTripConversations": true, "AutoCreateOrderConversations": true}, "RateLimiting": {"MessagesPerMinute": 60, "ConversationsPerHour": 10, "ParticipantsPerConversation": 50}}, "OfflineMessages": {"EnablePushNotifications": true, "MaxOfflineMessages": 1000, "CleanupIntervalHours": 24, "RetentionDays": 30, "BatchSize": 100}, "Authentication": {"JwtSettings": {"SecretKey": "your-secret-key-here", "Issuer": "TLI-Communication-Service", "Audience": "TLI-Users", "ExpirationMinutes": 60}}, "Logging": {"LogLevel": {"Default": "Information", "CommunicationNotification.Infrastructure.Hubs": "Debug", "CommunicationNotification.Infrastructure.Services.ChatService": "Debug", "CommunicationNotification.Infrastructure.Services.OfflineMessageService": "Debug", "Microsoft.AspNetCore.SignalR": "Information"}}}