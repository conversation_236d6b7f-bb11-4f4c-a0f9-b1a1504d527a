using AnalyticsBIService.Domain.Enums;
using Shared.Domain.Common;

namespace AnalyticsBIService.Domain.Entities;

/// <summary>
/// Threshold rule entity for alert conditions
/// </summary>
public class ThresholdRule : BaseEntity
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public string MetricName { get; private set; }
    public string Operator { get; private set; }
    public decimal ThresholdValue { get; private set; }
    public AlertSeverity Severity { get; private set; }
    public bool IsEnabled { get; private set; }
    public Guid CreatedBy { get; private set; }
    public UserType UserType { get; private set; }
    public DateTime? LastTriggered { get; private set; }
    public int TriggerCount { get; private set; }

    // Additional properties required by services
    public Guid? MonitorId { get; private set; }

    private ThresholdRule()
    {
        Name = string.Empty;
        Description = string.Empty;
        MetricName = string.Empty;
        Operator = string.Empty;
    }

    public ThresholdRule(
        string name,
        string description,
        string metricName,
        string @operator,
        decimal thresholdValue,
        AlertSeverity severity,
        Guid createdBy,
        UserType userType,
        Guid? monitorId = null)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        MetricName = metricName ?? throw new ArgumentNullException(nameof(metricName));
        Operator = @operator ?? throw new ArgumentNullException(nameof(@operator));
        ThresholdValue = thresholdValue;
        Severity = severity;
        CreatedBy = createdBy;
        UserType = userType;
        MonitorId = monitorId;
        IsEnabled = true;
        TriggerCount = 0;
    }

    public void UpdateThreshold(decimal thresholdValue)
    {
        ThresholdValue = thresholdValue;
        SetUpdatedAt();
    }

    public void UpdateSeverity(AlertSeverity severity)
    {
        Severity = severity;
        SetUpdatedAt();
    }

    public void Enable()
    {
        IsEnabled = true;
        SetUpdatedAt();
    }

    public void Disable()
    {
        IsEnabled = false;
        SetUpdatedAt();
    }

    public void RecordTrigger()
    {
        LastTriggered = DateTime.UtcNow;
        TriggerCount++;
        SetUpdatedAt();
    }

    public bool EvaluateCondition(decimal currentValue)
    {
        return Operator.ToLowerInvariant() switch
        {
            ">" or "greater_than" => currentValue > ThresholdValue,
            "<" or "less_than" => currentValue < ThresholdValue,
            ">=" or "greater_than_or_equal" => currentValue >= ThresholdValue,
            "<=" or "less_than_or_equal" => currentValue <= ThresholdValue,
            "==" or "equals" => Math.Abs(currentValue - ThresholdValue) < 0.001m,
            "!=" or "not_equals" => Math.Abs(currentValue - ThresholdValue) >= 0.001m,
            _ => false
        };
    }
}
