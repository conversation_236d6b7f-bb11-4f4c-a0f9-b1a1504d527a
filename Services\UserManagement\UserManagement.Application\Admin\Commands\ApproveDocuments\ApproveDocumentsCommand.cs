using MediatR;
using FluentValidation;
using UserManagement.Domain.Entities;

namespace UserManagement.Application.Admin.Commands.ApproveDocuments
{
    public class ApproveDocumentsCommand : IRequest<ApproveDocumentsResponse>
    {
        public Guid SubmissionId { get; set; }
        public List<DocumentApprovalRequest> Documents { get; set; } = new();
        public Guid ApprovedBy { get; set; }
        public string Notes { get; set; } = string.Empty;
    }

    public class DocumentApprovalRequest
    {
        public DocumentType DocumentType { get; set; }
        public string ReviewNotes { get; set; } = string.Empty;
    }

    public class ApproveDocumentsResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public List<DocumentApprovalResult> Results { get; set; } = new();
        public DateTime ProcessedAt { get; set; }
    }

    public class DocumentApprovalResult
    {
        public DocumentType DocumentType { get; set; }
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    public class ApproveDocumentsCommandValidator : AbstractValidator<ApproveDocumentsCommand>
    {
        public ApproveDocumentsCommandValidator()
        {
            RuleFor(x => x.SubmissionId)
                .NotEmpty()
                .WithMessage("Submission ID is required");

            RuleFor(x => x.ApprovedBy)
                .NotEmpty()
                .WithMessage("Approver ID is required");

            RuleFor(x => x.Documents)
                .NotEmpty()
                .WithMessage("At least one document must be specified for approval");

            RuleForEach(x => x.Documents)
                .SetValidator(new DocumentApprovalRequestValidator());
        }
    }

    public class DocumentApprovalRequestValidator : AbstractValidator<DocumentApprovalRequest>
    {
        public DocumentApprovalRequestValidator()
        {
            RuleFor(x => x.DocumentType)
                .IsInEnum()
                .WithMessage("Valid document type is required");
        }
    }
}
