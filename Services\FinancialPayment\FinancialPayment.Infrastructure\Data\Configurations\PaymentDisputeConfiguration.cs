using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using FinancialPayment.Domain.Entities;

namespace FinancialPayment.Infrastructure.Data.Configurations;

public class PaymentDisputeConfiguration : IEntityTypeConfiguration<PaymentDispute>
{
    public void Configure(EntityTypeBuilder<PaymentDispute> builder)
    {
        builder.ToTable("payment_disputes");

        builder.<PERSON><PERSON>ey(d => d.Id);

        builder.Property(d => d.Id)
            .HasColumnName("id")
            .ValueGeneratedNever();

        builder.Property(d => d.OrderId)
            .HasColumnName("order_id")
            .IsRequired();

        builder.Property(d => d.EscrowAccountId)
            .HasColumnName("escrow_account_id");

        builder.Property(d => d.SettlementId)
            .HasColumnName("settlement_id");

        builder.Property(d => d.InitiatedBy)
            .HasColumnName("initiated_by")
            .IsRequired();

        builder.Property(d => d.InitiatorRole)
            .HasColumnName("initiator_role")
            .HasConversion<int>()
            .IsRequired();

        builder.Property(d => d.DisputeNumber)
            .HasColumnName("dispute_number")
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(d => d.Title)
            .HasColumnName("title")
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(d => d.Description)
            .HasColumnName("description")
            .HasMaxLength(2000)
            .IsRequired();

        // Money value object
        builder.OwnsOne(d => d.DisputedAmount, money =>
        {
            money.Property(m => m.Amount)
                .HasColumnName("disputed_amount")
                .HasColumnType("decimal(18,2)")
                .IsRequired();

            money.Property(m => m.Currency)
                .HasColumnName("disputed_currency")
                .HasMaxLength(3)
                .IsRequired();
        });

        builder.OwnsOne(d => d.ResolvedAmount, money =>
        {
            money.Property(m => m.Amount)
                .HasColumnName("resolved_amount")
                .HasColumnType("decimal(18,2)");

            money.Property(m => m.Currency)
                .HasColumnName("resolved_currency")
                .HasMaxLength(3);
        });

        builder.Property(d => d.Category)
            .HasColumnName("category")
            .HasConversion<int>()
            .IsRequired();

        builder.Property(d => d.Status)
            .HasColumnName("status")
            .HasConversion<int>()
            .IsRequired();

        builder.Property(d => d.Priority)
            .HasColumnName("priority")
            .HasConversion<int>()
            .IsRequired();

        builder.Property(d => d.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(d => d.ResolvedAt)
            .HasColumnName("resolved_at");

        builder.Property(d => d.Resolution)
            .HasColumnName("resolution")
            .HasMaxLength(2000);

        builder.Property(d => d.ResolvedBy)
            .HasColumnName("resolved_by");

        // Relationships
        builder.HasMany(d => d.Comments)
            .WithOne(c => c.Dispute)
            .HasForeignKey(c => c.DisputeId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(d => d.Documents)
            .WithOne(doc => doc.Dispute)
            .HasForeignKey(doc => doc.DisputeId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(d => d.EscrowAccount)
            .WithMany()
            .HasForeignKey(d => d.EscrowAccountId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(d => d.Settlement)
            .WithMany()
            .HasForeignKey(d => d.SettlementId)
            .OnDelete(DeleteBehavior.Restrict);

        // Indexes
        builder.HasIndex(d => d.OrderId)
            .HasDatabaseName("ix_payment_disputes_order_id");

        builder.HasIndex(d => d.DisputeNumber)
            .IsUnique()
            .HasDatabaseName("ix_payment_disputes_dispute_number");

        builder.HasIndex(d => d.InitiatedBy)
            .HasDatabaseName("ix_payment_disputes_initiated_by");

        builder.HasIndex(d => d.InitiatorRole)
            .HasDatabaseName("ix_payment_disputes_initiator_role");

        builder.HasIndex(d => d.Category)
            .HasDatabaseName("ix_payment_disputes_category");

        builder.HasIndex(d => d.Status)
            .HasDatabaseName("ix_payment_disputes_status");

        builder.HasIndex(d => d.Priority)
            .HasDatabaseName("ix_payment_disputes_priority");

        builder.HasIndex(d => d.CreatedAt)
            .HasDatabaseName("ix_payment_disputes_created_at");

        builder.HasIndex(d => d.EscrowAccountId)
            .HasDatabaseName("ix_payment_disputes_escrow_account_id");

        builder.HasIndex(d => d.SettlementId)
            .HasDatabaseName("ix_payment_disputes_settlement_id");
    }
}
