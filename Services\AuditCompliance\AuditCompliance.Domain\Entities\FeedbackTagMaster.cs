using Shared.Domain.Common;
using AuditCompliance.Domain.Events;

namespace AuditCompliance.Domain.Entities;

/// <summary>
/// Master data entity for feedback tags
/// </summary>
public class FeedbackTagMaster : AggregateRoot
{
    public string Code { get; private set; } = string.Empty;
    public string Name { get; private set; } = string.Empty;
    public string Description { get; private set; } = string.Empty;
    public string Category { get; private set; } = string.Empty; // Service, Quality, Communication, Timeliness, etc.
    public string TagType { get; private set; } = string.Empty; // Positive, Negative, Neutral
    public string Color { get; private set; } = string.Empty; // Hex color for UI display
    public bool IsSystemGenerated { get; private set; } // Auto-generated vs user-selected
    public bool RequiresComment { get; private set; }
    public decimal WeightFactor { get; private set; } = 1.0m; // Impact on overall rating
    public bool AffectsRating { get; private set; }
    public bool IsActive { get; private set; }
    public int SortOrder { get; private set; }
    public string? IconUrl { get; private set; }
    public Dictionary<string, object> AdditionalProperties { get; private set; } = new();

    // Private constructor for EF Core
    private FeedbackTagMaster() { }

    public FeedbackTagMaster(
        string code,
        string name,
        string description,
        string category,
        string tagType = "Neutral",
        string color = "#808080",
        bool isSystemGenerated = false,
        bool requiresComment = false,
        decimal weightFactor = 1.0m,
        bool affectsRating = true,
        int sortOrder = 0,
        string? iconUrl = null,
        Dictionary<string, object>? additionalProperties = null)
    {
        if (string.IsNullOrWhiteSpace(code))
            throw new ArgumentException("Feedback tag code cannot be empty", nameof(code));

        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Feedback tag name cannot be empty", nameof(name));

        if (string.IsNullOrWhiteSpace(category))
            throw new ArgumentException("Category cannot be empty", nameof(category));

        Code = code.Trim().ToUpperInvariant();
        Name = name.Trim();
        Description = description?.Trim() ?? string.Empty;
        Category = category.Trim();
        TagType = tagType.Trim();
        Color = color.Trim();
        IsSystemGenerated = isSystemGenerated;
        RequiresComment = requiresComment;
        WeightFactor = weightFactor;
        AffectsRating = affectsRating;
        IsActive = true;
        SortOrder = sortOrder;
        IconUrl = iconUrl?.Trim();
        AdditionalProperties = additionalProperties ?? new Dictionary<string, object>();

        // Validate business rules
        ValidateBusinessRules();

        // Add domain event
        AddDomainEvent(new FeedbackTagMasterCreatedEvent(Id, Code, Name, Category));
    }

    public void UpdateDetails(
        string name,
        string description,
        string category,
        string tagType = "Neutral",
        string color = "#808080",
        bool isSystemGenerated = false,
        bool requiresComment = false,
        decimal weightFactor = 1.0m,
        bool affectsRating = true,
        int sortOrder = 0,
        string? iconUrl = null,
        Dictionary<string, object>? additionalProperties = null)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Feedback tag name cannot be empty", nameof(name));

        if (string.IsNullOrWhiteSpace(category))
            throw new ArgumentException("Category cannot be empty", nameof(category));

        var oldName = Name;
        var oldCategory = Category;

        Name = name.Trim();
        Description = description?.Trim() ?? string.Empty;
        Category = category.Trim();
        TagType = tagType.Trim();
        Color = color.Trim();
        IsSystemGenerated = isSystemGenerated;
        RequiresComment = requiresComment;
        WeightFactor = weightFactor;
        AffectsRating = affectsRating;
        SortOrder = sortOrder;
        IconUrl = iconUrl?.Trim();

        if (additionalProperties != null)
        {
            AdditionalProperties = additionalProperties;
        }

        // Validate business rules
        ValidateBusinessRules();

        SetUpdatedAt();

        // Add domain event if significant changes
        if (oldName != Name || oldCategory != Category)
        {
            AddDomainEvent(new FeedbackTagMasterUpdatedEvent(Id, Code, Name, Category, oldName, oldCategory));
        }
    }

    public void Activate()
    {
        if (IsActive) return;

        IsActive = true;
        SetUpdatedAt();

        AddDomainEvent(new FeedbackTagMasterActivatedEvent(Id, Code, Name));
    }

    public void Deactivate()
    {
        if (!IsActive) return;

        IsActive = false;
        SetUpdatedAt();

        AddDomainEvent(new FeedbackTagMasterDeactivatedEvent(Id, Code, Name));
    }

    public void UpdateSortOrder(int newSortOrder)
    {
        if (SortOrder == newSortOrder) return;

        SortOrder = newSortOrder;
        SetUpdatedAt();
    }

    public void AddOrUpdateProperty(string key, object value)
    {
        if (string.IsNullOrWhiteSpace(key))
            throw new ArgumentException("Property key cannot be empty", nameof(key));

        AdditionalProperties[key] = value;
        SetUpdatedAt();
    }

    public void RemoveProperty(string key)
    {
        if (string.IsNullOrWhiteSpace(key)) return;

        if (AdditionalProperties.Remove(key))
        {
            SetUpdatedAt();
        }
    }

    public bool IsPositive()
    {
        return TagType.Equals("Positive", StringComparison.OrdinalIgnoreCase);
    }

    public bool IsNegative()
    {
        return TagType.Equals("Negative", StringComparison.OrdinalIgnoreCase);
    }

    public bool IsNeutral()
    {
        return TagType.Equals("Neutral", StringComparison.OrdinalIgnoreCase);
    }

    public decimal CalculateRatingImpact(decimal baseRating)
    {
        if (!AffectsRating) return 0m;

        return IsPositive() ? WeightFactor : IsNegative() ? -WeightFactor : 0m;
    }

    public bool RequiresUserInput()
    {
        return RequiresComment && !IsSystemGenerated;
    }

    private void ValidateBusinessRules()
    {
        // Validate tag type
        var validTagTypes = new[] { "Positive", "Negative", "Neutral" };
        if (!validTagTypes.Contains(TagType, StringComparer.OrdinalIgnoreCase))
            throw new ArgumentException("Tag type must be Positive, Negative, or Neutral");

        // Validate color format (basic hex color validation)
        if (!string.IsNullOrWhiteSpace(Color) && !Color.StartsWith("#"))
            throw new ArgumentException("Color must be in hex format (e.g., #FF0000)");

        // Validate weight factor
        if (WeightFactor < 0 || WeightFactor > 10)
            throw new ArgumentException("Weight factor must be between 0 and 10");
    }
}



