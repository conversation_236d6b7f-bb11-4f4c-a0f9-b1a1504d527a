using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Domain.Common;
using Shared.Messaging;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Entities;

namespace SubscriptionManagement.Application.Commands.TransportCompanyAutoRenewal;

/// <summary>
/// Handler for updating Transport Company auto-renewal settings
/// </summary>
public class UpdateAutoRenewalSettingsCommandHandler :
    IRequestHandler<UpdateAutoRenewalSettingsCommand, UpdateAutoRenewalSettingsResult>
{
    private readonly ISubscriptionRepository _subscriptionRepository;
    private readonly IPaymentService _paymentService;
    private readonly IAutoRenewalSchedulingService _schedulingService;
    private readonly INotificationService _notificationService;
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<UpdateAutoRenewalSettingsCommandHandler> _logger;

    public UpdateAutoRenewalSettingsCommandHandler(
        ISubscriptionRepository subscriptionRepository,
        IPaymentService paymentService,
        IAutoRenewalSchedulingService schedulingService,
        INotificationService notificationService,
        IMessageBroker messageBroker,
        ILogger<UpdateAutoRenewalSettingsCommandHandler> logger)
    {
        _subscriptionRepository = subscriptionRepository;
        _paymentService = paymentService;
        _schedulingService = schedulingService;
        _notificationService = notificationService;
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task<UpdateAutoRenewalSettingsResult> Handle(
        UpdateAutoRenewalSettingsCommand request,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Updating auto-renewal settings for Transport Company {TransportCompanyId}, Subscription {SubscriptionId}",
            request.TransportCompanyId, request.SubscriptionId);

        try
        {
            // Validate request
            var validationResult = await ValidateRequest(request, cancellationToken);
            if (!validationResult.IsValid)
            {
                return new UpdateAutoRenewalSettingsResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Validation failed",
                    ValidationErrors = validationResult.Errors,
                    UpdatedAt = DateTime.UtcNow
                };
            }

            // Get subscription
            var subscription = await _subscriptionRepository.GetByIdAsync(request.SubscriptionId, cancellationToken);
            if (subscription == null)
            {
                return new UpdateAutoRenewalSettingsResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Subscription not found",
                    UpdatedAt = DateTime.UtcNow
                };
            }

            // Verify subscription belongs to transport company
            if (!await VerifySubscriptionOwnership(subscription, request.TransportCompanyId, cancellationToken))
            {
                return new UpdateAutoRenewalSettingsResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Subscription does not belong to the specified transport company",
                    UpdatedAt = DateTime.UtcNow
                };
            }

            var previousAutoRenewStatus = subscription.AutoRenew;

            // Update auto-renewal settings
            await UpdateSubscriptionAutoRenewal(subscription, request, cancellationToken);

            // Update renewal scheduling if enabled
            if (request.AutoRenewEnabled)
            {
                await ScheduleRenewalReminders(subscription, request.Preferences, cancellationToken);
                await ValidatePaymentMethod(subscription, request.Preferences, cancellationToken);
            }
            else
            {
                await CancelScheduledReminders(subscription.Id, cancellationToken);
            }

            // Save changes
            await _subscriptionRepository.UpdateAsync(subscription, cancellationToken);

            // Record the change in history
            await RecordAutoRenewalChange(subscription, request, previousAutoRenewStatus, cancellationToken);

            // Publish event
            await PublishAutoRenewalUpdatedEvent(subscription, request, previousAutoRenewStatus, cancellationToken);

            // Send confirmation notification
            await SendConfirmationNotification(subscription, request, cancellationToken);

            var result = new UpdateAutoRenewalSettingsResult
            {
                IsSuccess = true,
                UpdatedAt = DateTime.UtcNow,
                CurrentStatus = await BuildCurrentStatus(subscription, cancellationToken),
                NextRenewal = await BuildNextRenewalInfo(subscription, cancellationToken)
            };

            _logger.LogInformation("Auto-renewal settings updated successfully for Transport Company {TransportCompanyId}, Subscription {SubscriptionId}. AutoRenew: {AutoRenew}",
                request.TransportCompanyId, request.SubscriptionId, request.AutoRenewEnabled);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating auto-renewal settings for Transport Company {TransportCompanyId}, Subscription {SubscriptionId}",
                request.TransportCompanyId, request.SubscriptionId);

            return new UpdateAutoRenewalSettingsResult
            {
                IsSuccess = false,
                ErrorMessage = ex.Message,
                UpdatedAt = DateTime.UtcNow
            };
        }
    }

    private async Task<ValidationResult> ValidateRequest(
        UpdateAutoRenewalSettingsCommand request,
        CancellationToken cancellationToken)
    {
        var result = new ValidationResult { IsValid = true };

        // Validate subscription ID
        if (request.SubscriptionId == Guid.Empty)
        {
            result.IsValid = false;
            result.Errors.Add("Subscription ID is required");
        }

        // Validate transport company ID
        if (request.TransportCompanyId == Guid.Empty)
        {
            result.IsValid = false;
            result.Errors.Add("Transport Company ID is required");
        }

        // Validate preferences if auto-renewal is enabled
        if (request.AutoRenewEnabled)
        {
            if (request.Preferences.ReminderDaysBefore < 0 || request.Preferences.ReminderDaysBefore > 90)
            {
                result.IsValid = false;
                result.Errors.Add("Reminder days before must be between 0 and 90");
            }

            if (request.Preferences.GracePeriodDays < 0 || request.Preferences.GracePeriodDays > 30)
            {
                result.IsValid = false;
                result.Errors.Add("Grace period days must be between 0 and 30");
            }

            if (request.Preferences.MaxAutoRenewalAmount < 0)
            {
                result.IsValid = false;
                result.Errors.Add("Maximum auto-renewal amount cannot be negative");
            }

            // Validate additional reminder days
            if (request.Preferences.AdditionalReminderDays.Any(d => d < 0 || d > 365))
            {
                result.IsValid = false;
                result.Errors.Add("Additional reminder days must be between 0 and 365");
            }
        }

        return result;
    }

    private async Task<bool> VerifySubscriptionOwnership(
        Subscription subscription,
        Guid transportCompanyId,
        CancellationToken cancellationToken)
    {
        // This would typically verify that the subscription belongs to the transport company
        // Implementation would depend on how user-company relationships are managed
        return await Task.FromResult(true);
    }

    private async Task UpdateSubscriptionAutoRenewal(
        Subscription subscription,
        UpdateAutoRenewalSettingsCommand request,
        CancellationToken cancellationToken)
    {
        // Update the auto-renewal flag
        if (request.AutoRenewEnabled)
        {
            subscription.EnableAutoRenewal();
        }
        else
        {
            subscription.DisableAutoRenewal(request.ChangeReason ?? "Auto-renewal disabled by user");
        }

        // Store preferences in subscription metadata or separate entity
        await StoreAutoRenewalPreferences(subscription.Id, request.Preferences, cancellationToken);
    }

    private async Task StoreAutoRenewalPreferences(
        Guid subscriptionId,
        AutoRenewalPreferences preferences,
        CancellationToken cancellationToken)
    {
        // This would store preferences in a separate table or as JSON in subscription metadata
        // For now, we'll assume it's stored as metadata
        await Task.CompletedTask;
    }

    private async Task ScheduleRenewalReminders(
        Subscription subscription,
        AutoRenewalPreferences preferences,
        CancellationToken cancellationToken)
    {
        var reminderDays = new List<int> { preferences.ReminderDaysBefore };
        reminderDays.AddRange(preferences.AdditionalReminderDays);

        await _schedulingService.ScheduleRenewalRemindersAsync(
            subscription.Id,
            subscription.NextBillingDate,
            reminderDays,
            GetNotificationChannels(preferences),
            cancellationToken);
    }

    private async Task CancelScheduledReminders(Guid subscriptionId, CancellationToken cancellationToken)
    {
        await _schedulingService.CancelScheduledRemindersAsync(subscriptionId, cancellationToken);
    }

    private async Task ValidatePaymentMethod(
        Subscription subscription,
        AutoRenewalPreferences preferences,
        CancellationToken cancellationToken)
    {
        if (!string.IsNullOrEmpty(preferences.PreferredPaymentMethodId))
        {
            var isValid = await _paymentService.ValidatePaymentMethodAsync(
                preferences.PreferredPaymentMethodId, cancellationToken);

            if (!isValid)
            {
                _logger.LogWarning("Invalid payment method {PaymentMethodId} for subscription {SubscriptionId}",
                    preferences.PreferredPaymentMethodId, subscription.Id);
            }
        }
    }

    private async Task RecordAutoRenewalChange(
        Subscription subscription,
        UpdateAutoRenewalSettingsCommand request,
        bool previousStatus,
        CancellationToken cancellationToken)
    {
        var action = request.AutoRenewEnabled ? "Enabled" : "Disabled";
        var changeRecord = new AutoRenewalHistoryRecord
        {
            SubscriptionId = subscription.Id,
            Action = action,
            Reason = request.ChangeReason,
            PreviousStatus = previousStatus,
            NewStatus = request.AutoRenewEnabled,
            ChangedBy = request.UpdatedBy,
            ChangedAt = DateTime.UtcNow,
            Metadata = new Dictionary<string, object>
            {
                ["ReminderDaysBefore"] = request.Preferences.ReminderDaysBefore,
                ["GracePeriodDays"] = request.Preferences.GracePeriodDays,
                ["EmailNotifications"] = request.Preferences.EnableEmailNotifications,
                ["SMSNotifications"] = request.Preferences.EnableSMSNotifications
            }
        };

        // Store in history table
        await StoreAutoRenewalHistory(changeRecord, cancellationToken);
    }

    private async Task StoreAutoRenewalHistory(
        AutoRenewalHistoryRecord record,
        CancellationToken cancellationToken)
    {
        // Implementation would store in auto-renewal history table
        await Task.CompletedTask;
    }

    private async Task PublishAutoRenewalUpdatedEvent(
        Subscription subscription,
        UpdateAutoRenewalSettingsCommand request,
        bool previousStatus,
        CancellationToken cancellationToken)
    {
        await _messageBroker.PublishAsync("subscription.auto_renewal_updated", new
        {
            SubscriptionId = subscription.Id,
            TransportCompanyId = request.TransportCompanyId,
            UserId = subscription.UserId,
            PreviousAutoRenewStatus = previousStatus,
            NewAutoRenewStatus = request.AutoRenewEnabled,
            ChangeReason = request.ChangeReason,
            UpdatedBy = request.UpdatedBy,
            UpdatedAt = DateTime.UtcNow,
            NextBillingDate = subscription.NextBillingDate,
            PlanName = subscription.Plan?.Name,
            Amount = subscription.CurrentPrice.Amount,
            Currency = subscription.CurrentPrice.Currency,
            Preferences = new
            {
                request.Preferences.EnableEmailNotifications,
                request.Preferences.EnableSMSNotifications,
                request.Preferences.ReminderDaysBefore,
                request.Preferences.GracePeriodDays
            }
        });
    }

    private async Task SendConfirmationNotification(
        Subscription subscription,
        UpdateAutoRenewalSettingsCommand request,
        CancellationToken cancellationToken)
    {
        if (request.Preferences.EnableEmailNotifications)
        {
            var message = request.AutoRenewEnabled
                ? "Auto-renewal has been enabled for your subscription"
                : "Auto-renewal has been disabled for your subscription";

            await _notificationService.SendAutoRenewalUpdateNotificationAsync(
                subscription.UserId,
                message,
                subscription,
                cancellationToken);
        }
    }

    private async Task<AutoRenewalStatus> BuildCurrentStatus(
        Subscription subscription,
        CancellationToken cancellationToken)
    {
        var paymentMethodValid = await ValidateCurrentPaymentMethod(subscription, cancellationToken);
        var renewalBlockers = await GetRenewalBlockers(subscription, cancellationToken);

        return new AutoRenewalStatus
        {
            IsEnabled = subscription.AutoRenew,
            NextRenewalDate = subscription.NextBillingDate,
            NextRenewalAmount = subscription.CurrentPrice.Amount,
            Currency = subscription.CurrentPrice.Currency,
            PaymentMethodValid = paymentMethodValid,
            PaymentMethodLastFour = await GetPaymentMethodLastFour(subscription, cancellationToken),
            IsInGracePeriod = subscription.IsInGracePeriod(),
            GracePeriodEndDate = subscription.GracePeriodEndDate,
            RenewalBlockers = renewalBlockers
        };
    }

    private async Task<NextRenewalInfo?> BuildNextRenewalInfo(
        Subscription subscription,
        CancellationToken cancellationToken)
    {
        if (!subscription.AutoRenew)
            return null;

        var reminders = await GetScheduledReminders(subscription.Id, cancellationToken);

        return new NextRenewalInfo
        {
            RenewalDate = subscription.NextBillingDate,
            Amount = subscription.CurrentPrice.Amount,
            Currency = subscription.CurrentPrice.Currency,
            PlanName = subscription.Plan?.Name ?? "Unknown Plan",
            DaysUntilRenewal = subscription.GetDaysUntilRenewal(),
            CanCancel = true,
            CancellationDeadline = subscription.NextBillingDate.AddDays(-1),
            ScheduledReminders = reminders
        };
    }

    private List<string> GetNotificationChannels(AutoRenewalPreferences preferences)
    {
        var channels = new List<string>();

        if (preferences.EnableEmailNotifications)
            channels.Add("Email");

        if (preferences.EnableSMSNotifications)
            channels.Add("SMS");

        return channels;
    }

    private async Task<bool> ValidateCurrentPaymentMethod(Subscription subscription, CancellationToken cancellationToken)
    {
        // Implementation would validate the current payment method
        return await Task.FromResult(true);
    }

    private async Task<List<string>> GetRenewalBlockers(Subscription subscription, CancellationToken cancellationToken)
    {
        var blockers = new List<string>();

        if (!subscription.AutoRenew)
            blockers.Add("Auto-renewal is disabled");

        if (subscription.Status != Domain.Enums.SubscriptionStatus.Active)
            blockers.Add("Subscription is not active");

        var paymentMethodValid = await ValidateCurrentPaymentMethod(subscription, cancellationToken);
        if (!paymentMethodValid)
            blockers.Add("Payment method is invalid or expired");

        return blockers;
    }

    private async Task<string?> GetPaymentMethodLastFour(Subscription subscription, CancellationToken cancellationToken)
    {
        // Implementation would get the last four digits of the payment method
        return await Task.FromResult("****");
    }

    private async Task<List<RenewalReminder>> GetScheduledReminders(Guid subscriptionId, CancellationToken cancellationToken)
    {
        // Implementation would get scheduled reminders from the scheduling service
        return await Task.FromResult(new List<RenewalReminder>());
    }

    private class ValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new();
    }

    private class AutoRenewalHistoryRecord
    {
        public Guid SubscriptionId { get; set; }
        public string Action { get; set; } = string.Empty;
        public string? Reason { get; set; }
        public bool PreviousStatus { get; set; }
        public bool NewStatus { get; set; }
        public Guid ChangedBy { get; set; }
        public DateTime ChangedAt { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
    }
}

