using CommunicationNotification.Application.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using Twilio;
using Twilio.Rest.Api.V2010.Account;
using Twilio.Types;

namespace CommunicationNotification.Infrastructure.ExternalServices;

/// <summary>
/// Twilio SMS provider implementation
/// </summary>
public class TwilioSmsProvider : ISmsProvider
{
    private readonly ILogger<TwilioSmsProvider> _logger;
    private readonly TwilioSettings _settings;
    private readonly HttpClient _httpClient;

    public TwilioSmsProvider(
        IConfiguration configuration,
        ILogger<TwilioSmsProvider> logger,
        HttpClient httpClient)
    {
        _logger = logger;
        _httpClient = httpClient;
        _settings = configuration.GetSection("Twilio").Get<TwilioSettings>() ?? new();

        if (!string.IsNullOrEmpty(_settings.AccountSid) && !string.IsNullOrEmpty(_settings.AuthToken))
        {
            TwilioClient.Init(_settings.AccountSid, _settings.AuthToken);
        }
    }

    public async Task<SmsResult> SendSmsAsync(SmsRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Sending SMS to {PhoneNumber} via Twilio", request.PhoneNumber);

            // Validate request
            var validationResult = ValidateRequest(request);
            if (!validationResult.IsValid)
            {
                return SmsResult.Failure(string.Join(", ", validationResult.Errors));
            }

            // Format phone number
            var toPhoneNumber = FormatPhoneNumber(request.PhoneNumber);
            var fromPhoneNumber = new PhoneNumber(_settings.FromPhoneNumber);

            // Create message options
            var messageOptions = new CreateMessageOptions(toPhoneNumber)
            {
                From = fromPhoneNumber,
                Body = request.Message
            };

            // Add media URLs if provided
            if (request.MediaUrls?.Any() == true)
            {
                messageOptions.MediaUrl = request.MediaUrls.Select(url => new Uri(url)).ToList();
            }

            // Set callback URL for delivery status
            if (!string.IsNullOrEmpty(_settings.StatusCallbackUrl))
            {
                messageOptions.StatusCallback = new Uri(_settings.StatusCallbackUrl);
            }

            // Send message
            var message = await MessageResource.CreateAsync(messageOptions);

            var result = new SmsResult
            {
                IsSuccess = true,
                MessageId = message.Sid,
                ExternalId = message.Sid,
                Status = MapTwilioStatus(message.Status),
                SentAt = DateTime.UtcNow,
                Cost = message.Price?.ToString(),
                Metadata = new Dictionary<string, object>
                {
                    ["twilioSid"] = message.Sid,
                    ["twilioStatus"] = message.Status.ToString(),
                    ["twilioDirection"] = message.Direction.ToString(),
                    ["twilioNumSegments"] = message.NumSegments?.ToString() ?? "1"
                }
            };

            _logger.LogInformation("SMS sent successfully to {PhoneNumber}. Twilio SID: {TwilioSid}",
                request.PhoneNumber, message.Sid);

            return result;
        }
        catch (Twilio.Exceptions.ApiException ex)
        {
            _logger.LogError(ex, "Twilio API error sending SMS to {PhoneNumber}: {ErrorCode} - {ErrorMessage}",
                request.PhoneNumber, ex.Code, ex.Message);

            return SmsResult.Failure($"Twilio API error: {ex.Message}", ex.Code?.ToString());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error sending SMS to {PhoneNumber}", request.PhoneNumber);
            return SmsResult.Failure($"Unexpected error: {ex.Message}");
        }
    }

    public async Task<SmsResult> GetDeliveryStatusAsync(string messageId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting delivery status for Twilio message {MessageId}", messageId);

            var message = await MessageResource.FetchAsync(messageId);

            var result = new SmsResult
            {
                IsSuccess = true,
                MessageId = messageId,
                ExternalId = message.Sid,
                Status = MapTwilioStatus(message.Status),
                SentAt = message.DateCreated ?? DateTime.UtcNow,
                DeliveredAt = message.Status == MessageResource.StatusEnum.Delivered ? message.DateUpdated : null,
                Cost = message.Price?.ToString(),
                ErrorMessage = message.ErrorMessage,
                Metadata = new Dictionary<string, object>
                {
                    ["twilioSid"] = message.Sid,
                    ["twilioStatus"] = message.Status.ToString(),
                    ["twilioErrorCode"] = message.ErrorCode?.ToString() ?? "",
                    ["twilioNumSegments"] = message.NumSegments?.ToString() ?? "1",
                    ["twilioPrice"] = message.Price?.ToString() ?? "0",
                    ["twilioDirection"] = message.Direction.ToString()
                }
            };

            return result;
        }
        catch (Twilio.Exceptions.ApiException ex)
        {
            _logger.LogError(ex, "Twilio API error getting delivery status for message {MessageId}: {ErrorCode} - {ErrorMessage}",
                messageId, ex.Code, ex.Message);

            return SmsResult.Failure($"Twilio API error: {ex.Message}", ex.Code?.ToString());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error getting delivery status for message {MessageId}", messageId);
            return SmsResult.Failure($"Unexpected error: {ex.Message}");
        }
    }

    public async Task<bool> ValidatePhoneNumberAsync(string phoneNumber, CancellationToken cancellationToken = default)
    {
        try
        {
            // Use Twilio Lookup API to validate phone number
            var lookupClient = new Twilio.Rest.Lookups.V1.PhoneNumberResource();
            var result = await Twilio.Rest.Lookups.V1.PhoneNumberResource.FetchAsync(
                pathPhoneNumber: new PhoneNumber(phoneNumber));

            return result != null && !string.IsNullOrEmpty(result.PhoneNumber?.ToString());
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Phone number validation failed for {PhoneNumber}", phoneNumber);
            return false;
        }
    }

    public async Task<List<SmsResult>> GetMessageHistoryAsync(
        DateTime fromDate,
        DateTime toDate,
        int pageSize = 50,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var messages = await MessageResource.ReadAsync(
                dateSentAfter: fromDate,
                dateSentBefore: toDate,
                limit: pageSize);

            return messages.Select(m => new SmsResult
            {
                IsSuccess = true,
                MessageId = m.Sid,
                ExternalId = m.Sid,
                Status = MapTwilioStatus(m.Status),
                SentAt = m.DateCreated ?? DateTime.UtcNow,
                DeliveredAt = m.Status == MessageResource.StatusEnum.Delivered ? m.DateUpdated : null,
                Cost = m.Price?.ToString(),
                ErrorMessage = m.ErrorMessage,
                Metadata = new Dictionary<string, object>
                {
                    ["twilioSid"] = m.Sid,
                    ["twilioStatus"] = m.Status.ToString(),
                    ["twilioTo"] = m.To,
                    ["twilioFrom"] = m.From,
                    ["twilioBody"] = m.Body ?? ""
                }
            }).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving message history from Twilio");
            return new List<SmsResult>();
        }
    }

    private SmsValidationResult ValidateRequest(SmsRequest request)
    {
        var errors = new List<string>();

        if (string.IsNullOrWhiteSpace(request.PhoneNumber))
            errors.Add("Phone number is required");

        if (string.IsNullOrWhiteSpace(request.Message))
            errors.Add("Message content is required");

        if (request.Message?.Length > _settings.MaxMessageLength)
            errors.Add($"Message exceeds maximum length of {_settings.MaxMessageLength} characters");

        if (!IsValidPhoneNumberFormat(request.PhoneNumber))
            errors.Add("Invalid phone number format");

        return new SmsValidationResult
        {
            IsValid = !errors.Any(),
            Errors = errors
        };
    }

    private bool IsValidPhoneNumberFormat(string phoneNumber)
    {
        if (string.IsNullOrWhiteSpace(phoneNumber))
            return false;

        // Basic phone number validation - should start with + and contain only digits
        var cleaned = phoneNumber.Replace(" ", "").Replace("-", "").Replace("(", "").Replace(")", "");
        return cleaned.StartsWith("+") && cleaned.Length >= 10 && cleaned.Skip(1).All(char.IsDigit);
    }

    private PhoneNumber FormatPhoneNumber(string phoneNumber)
    {
        // Ensure phone number is in E.164 format
        var cleaned = phoneNumber.Replace(" ", "").Replace("-", "").Replace("(", "").Replace(")", "");
        
        if (!cleaned.StartsWith("+"))
        {
            // Add default country code if not present (assuming India +91)
            cleaned = "+91" + cleaned;
        }

        return new PhoneNumber(cleaned);
    }

    private SmsStatus MapTwilioStatus(MessageResource.StatusEnum twilioStatus)
    {
        return twilioStatus switch
        {
            MessageResource.StatusEnum.Queued => SmsStatus.Queued,
            MessageResource.StatusEnum.Sending => SmsStatus.Sending,
            MessageResource.StatusEnum.Sent => SmsStatus.Sent,
            MessageResource.StatusEnum.Delivered => SmsStatus.Delivered,
            MessageResource.StatusEnum.Failed => SmsStatus.Failed,
            MessageResource.StatusEnum.Undelivered => SmsStatus.Failed,
            MessageResource.StatusEnum.Received => SmsStatus.Delivered,
            _ => SmsStatus.Unknown
        };
    }
}

/// <summary>
/// Twilio configuration settings
/// </summary>
public class TwilioSettings
{
    public string AccountSid { get; set; } = string.Empty;
    public string AuthToken { get; set; } = string.Empty;
    public string FromPhoneNumber { get; set; } = string.Empty;
    public string StatusCallbackUrl { get; set; } = string.Empty;
    public int MaxMessageLength { get; set; } = 1600;
    public bool EnableDeliveryReceipts { get; set; } = true;
    public int TimeoutSeconds { get; set; } = 30;
    public int RetryAttempts { get; set; } = 3;
    public string WebhookAuthToken { get; set; } = string.Empty;
}

/// <summary>
/// SMS validation result
/// </summary>
public class SmsValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
}
