namespace CommunicationNotification.Domain.Enums;

public enum MessageType
{
    /// <summary>
    /// General text message between users
    /// </summary>
    Text = 1,

    /// <summary>
    /// System-generated notification
    /// </summary>
    SystemNotification = 2,

    /// <summary>
    /// RFQ-related communication
    /// </summary>
    RfqCommunication = 3,

    /// <summary>
    /// Trip-related updates and instructions
    /// </summary>
    TripUpdate = 4,

    /// <summary>
    /// Payment and financial notifications
    /// </summary>
    PaymentNotification = 5,

    /// <summary>
    /// Emergency or urgent communication
    /// </summary>
    Emergency = 6,

    /// <summary>
    /// Driver instructions and navigation
    /// </summary>
    DriverInstruction = 7,

    /// <summary>
    /// Broker coordination messages
    /// </summary>
    BrokerCoordination = 8,

    /// <summary>
    /// Carrier network communications
    /// </summary>
    CarrierNetwork = 9,

    /// <summary>
    /// Subscription and billing notifications
    /// </summary>
    SubscriptionNotification = 10,

    /// <summary>
    /// Document expiry notifications
    /// </summary>
    DocumentExpiry = 11
}
