using MediatR;
using Microsoft.Extensions.Logging;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Domain.Enums;
using Shared.Infrastructure.Interfaces;

namespace FinancialPayment.Application.Commands.CreatePaymentDispute;

public class CreatePaymentDisputeCommandHandler : IRequestHandler<CreatePaymentDisputeCommand, Guid>
{
    private readonly IPaymentDisputeService _paymentDisputeService;
    private readonly ILogger<CreatePaymentDisputeCommandHandler> _logger;
    private readonly IUnitOfWork _unitOfWork;

    public CreatePaymentDisputeCommandHandler(
        IPaymentDisputeService paymentDisputeService,
        ILogger<CreatePaymentDisputeCommandHandler> logger,
        IUnitOfWork unitOfWork)
    {
        _paymentDisputeService = paymentDisputeService;
        _logger = logger;
        _unitOfWork = unitOfWork;
    }

    public async Task<Guid> Handle(CreatePaymentDisputeCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Creating payment dispute for Order {OrderId}", request.OrderId);

            var dispute = await _paymentDisputeService.CreateDisputeAsync(
                request.OrderId,
                request.InitiatedBy,
                Enum.Parse<ParticipantRole>(request.InitiatorRole, true),
                request.Title,
                request.Description,
                request.DisputedAmount.ToMoney(),
                Enum.Parse<DisputeCategory>(request.Category, true),
                Enum.Parse<DisputePriority>(request.Priority, true),
                request.EscrowAccountId,
                request.SettlementId);

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Payment dispute {DisputeId} created successfully for Order {OrderId}",
                dispute.Id, request.OrderId);

            return dispute.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating payment dispute for Order {OrderId}", request.OrderId);
            throw;
        }
    }
}
