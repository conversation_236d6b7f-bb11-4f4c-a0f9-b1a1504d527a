using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Application.Interfaces.Repositories;

public interface ITaxConfigurationRepository
{
    Task<TaxConfiguration?> GetByIdAsync(Guid id);
    Task<List<TaxConfiguration>> GetAllAsync();
    Task<List<TaxConfiguration>> GetActiveAsync();
    Task<List<TaxConfiguration>> GetByStatusAsync(TaxConfigurationStatus status);
    Task<List<TaxConfiguration>> GetByJurisdictionAsync(TaxJurisdiction jurisdiction);
    Task<List<TaxConfiguration>> GetEffectiveOnDateAsync(DateTime date);
    Task<TaxConfiguration?> GetDefaultAsync();
    Task<TaxConfiguration?> GetDefaultByJurisdictionAsync(TaxJurisdiction jurisdiction);
    Task<List<TaxConfiguration>> GetByPriorityAsync(int priority);
    Task<List<TaxConfiguration>> GetOrderedByPriorityAsync();
    Task<List<TaxConfiguration>> GetWithGstCalculationEnabledAsync();
    Task<List<TaxConfiguration>> GetWithTdsCalculationEnabledAsync();
    Task<List<TaxConfiguration>> GetWithReverseChargeEnabledAsync();
    Task<List<TaxConfiguration>> GetRequiringHsnCodeAsync();
    Task<List<TaxConfiguration>> GetByDefaultGstRateAsync(decimal gstRate);
    Task<List<TaxConfiguration>> GetByDefaultTdsRateAsync(decimal tdsRate);
    Task<List<TaxConfiguration>> GetByCurrencyAsync(string currency);
    Task<List<TaxConfiguration>> GetByCreatedByAsync(string createdBy);
    Task<List<TaxConfiguration>> GetByDateRangeAsync(DateTime from, DateTime to);
    Task<TaxConfiguration> AddAsync(TaxConfiguration taxConfiguration);
    Task UpdateAsync(TaxConfiguration taxConfiguration);
    Task DeleteAsync(Guid id);
    Task<bool> ExistsAsync(Guid id);
    Task<bool> ExistsByNameAsync(string name, Guid? excludeId = null);
    Task<bool> HasDefaultConfigurationAsync(TaxJurisdiction jurisdiction, Guid? excludeId = null);
    Task<int> GetCountByStatusAsync(TaxConfigurationStatus status);
    Task<List<TaxConfiguration>> SearchAsync(string searchTerm, int skip = 0, int take = 50);
    Task<List<TaxConfigurationHistory>> GetHistoryAsync(Guid taxConfigurationId);
    Task<List<TaxConfigurationRule>> GetRulesAsync(Guid taxConfigurationId);
    Task<TaxConfiguration?> GetApplicableConfigurationAsync(TaxJurisdiction jurisdiction, DateTime date);
}
