using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Domain.Repositories;
using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace AnalyticsBIService.Application.Queries.Carrier;

/// <summary>
/// Handler for carrier shipper ratings queries
/// </summary>
public class GetCarrierShipperRatingsQueryHandler : IRequestHandler<GetCarrierShipperRatingsQuery, CarrierShipperRatingsDto>
{
    private readonly IAnalyticsEventRepository _analyticsEventRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetCarrierShipperRatingsQueryHandler> _logger;

    public GetCarrierShipperRatingsQueryHandler(
        IAnalyticsEventRepository analyticsEventRepository,
        IMapper mapper,
        ILogger<GetCarrierShipperRatingsQueryHandler> logger)
    {
        _analyticsEventRepository = analyticsEventRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<CarrierShipperRatingsDto> Handle(GetCarrierShipperRatingsQuery request, CancellationToken cancellationToken)
    {
        _logger.LogDebug("Getting shipper ratings for carrier {CarrierId}", request.CarrierId);

        // Get shipper rating events for this carrier
        var allEvents = await _analyticsEventRepository.GetByDateRangeAsync(request.FromDate, request.ToDate, 1, 10000, cancellationToken);
        var shipperRatingEvents = allEvents.Items
            .Where(e => e.UserId == request.CarrierId &&
                       e.EventName.Contains("ShipperRating"))
            .ToList();

        // Calculate basic metrics (using mock data for demonstration)
        var averageShipperRating = 4.1m;
        var totalShipperRatings = 67;
        var uniqueShippers = 23;
        var shipperSatisfactionScore = 82.0m;

        // Generate top rated shippers
        var topRatedShippers = new List<ShipperRatingDto>
        {
            new()
            {
                ShipperId = Guid.NewGuid(),
                ShipperName = "Manufacturing Corp",
                Rating = 4.6m,
                RatingCount = 8,
                LastRatingDate = DateTime.UtcNow.AddDays(-2),
                BusinessVolume = "High",
                PositiveComments = new List<string> { "Professional handling", "Careful with goods", "Timely delivery" },
                ImprovementAreas = new List<string> { "Could improve loading efficiency" }
            },
            new()
            {
                ShipperId = Guid.NewGuid(),
                ShipperName = "Retail Solutions",
                Rating = 4.3m,
                RatingCount = 6,
                LastRatingDate = DateTime.UtcNow.AddDays(-4),
                BusinessVolume = "Medium",
                PositiveComments = new List<string> { "Good service", "Reliable carrier", "Fair pricing" },
                ImprovementAreas = new List<string> { "Better communication during transit" }
            },
            new()
            {
                ShipperId = Guid.NewGuid(),
                ShipperName = "Tech Distributors",
                Rating = 4.0m,
                RatingCount = 5,
                LastRatingDate = DateTime.UtcNow.AddDays(-6),
                BusinessVolume = "Medium",
                PositiveComments = new List<string> { "Handled fragile items well", "On-time delivery" },
                ImprovementAreas = new List<string> { "Documentation could be faster", "Better packaging protection" }
            }
        };

        // Generate shipper rating trends
        var shipperRatingTrends = GenerateShipperRatingTrends(request.FromDate, request.ToDate, averageShipperRating);

        // Generate shipper feedback themes
        var shipperFeedbackThemes = new List<FeedbackThemeDto>
        {
            new() { Theme = "Delivery Quality", MentionCount = 42, Percentage = 62.7m, Sentiment = "Positive", ImpactScore = 8.8m, SampleComments = new List<string> { "Goods arrived in perfect condition", "Careful handling" } },
            new() { Theme = "Timeliness", MentionCount = 38, Percentage = 56.7m, Sentiment = "Positive", ImpactScore = 9.1m, SampleComments = new List<string> { "Always on time", "Delivered as promised" } },
            new() { Theme = "Driver Behavior", MentionCount = 25, Percentage = 37.3m, Sentiment = "Positive", ImpactScore = 7.5m, SampleComments = new List<string> { "Professional driver", "Courteous service" } },
            new() { Theme = "Communication", MentionCount = 18, Percentage = 26.9m, Sentiment = "Neutral", ImpactScore = 6.9m, SampleComments = new List<string> { "Could update more frequently", "Good tracking info" } }
        };

        return new CarrierShipperRatingsDto
        {
            AverageShipperRating = averageShipperRating,
            TotalShipperRatings = totalShipperRatings,
            UniqueShippers = uniqueShippers,
            ShipperSatisfactionScore = shipperSatisfactionScore,
            TopRatedShippers = topRatedShippers,
            ShipperRatingTrends = shipperRatingTrends,
            ShipperFeedbackThemes = shipperFeedbackThemes
        };
    }

    private List<ShipperRatingTrendDto> GenerateShipperRatingTrends(DateTime fromDate, DateTime toDate, decimal baseRating)
    {
        var days = (toDate - fromDate).Days;
        var trends = new List<ShipperRatingTrendDto>();

        for (var date = fromDate; date <= toDate; date = date.AddDays(7)) // Weekly trends
        {
            var variance = (decimal)(new Random().NextDouble() * 0.4 - 0.2); // ±0.2 rating variance
            var rating = Math.Max(1, Math.Min(5, baseRating + variance));
            var ratingCount = new Random().Next(1, 6);
            var ratingChange = variance;

            trends.Add(new ShipperRatingTrendDto
            {
                Date = date,
                AverageRating = rating,
                RatingCount = ratingCount,
                RatingChange = ratingChange
            });
        }

        return trends;
    }
}
