using Shared.Domain.Common;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Domain.Events;

// Commission Events
public record CommissionCalculatedEvent(Guid CommissionId, Guid OrderId, Guid BrokerId, Money Amount) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record CommissionAdjustedEvent(Guid CommissionId, Guid OrderId, Guid AdjustmentId, Money AdjustmentAmount, CommissionAdjustmentType Type) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record CommissionApprovedEvent(Guid CommissionId, Guid OrderId, Guid BrokerId, Money Amount) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record CommissionPaidEvent(Guid CommissionId, Guid OrderId, Guid BrokerId, Money Amount, string PaymentGatewayTransactionId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record CommissionDisputedEvent(Guid CommissionId, Guid OrderId, Guid BrokerId, string Reason) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record CommissionDisputeResolvedEvent(Guid CommissionId, Guid OrderId, Guid BrokerId, Money ResolvedAmount, string Resolution) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}
