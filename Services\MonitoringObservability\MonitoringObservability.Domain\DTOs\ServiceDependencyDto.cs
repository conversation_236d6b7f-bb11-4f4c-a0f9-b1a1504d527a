namespace MonitoringObservability.Domain.DTOs;

/// <summary>
/// DTO for service dependency information
/// </summary>
public class ServiceDependencyDto
{
    public string ServiceName { get; set; } = string.Empty;
    public string DependentService { get; set; } = string.Empty;
    public string DependencyType { get; set; } = string.Empty;
    public bool IsCritical { get; set; }
    public string Status { get; set; } = string.Empty;
    public DependencyMetricsDto Metrics { get; set; } = new();
    public List<DependencyPathDto> Paths { get; set; } = new();
    public DateTime LastChecked { get; set; }
}

public class DependencyMetricsDto
{
    public double AvailabilityPercentage { get; set; }
    public double AverageResponseTimeMs { get; set; }
    public int TotalRequests { get; set; }
    public int FailedRequests { get; set; }
    public double ErrorRate { get; set; }
    public DateTime MetricsPeriodStart { get; set; }
    public DateTime MetricsPeriodEnd { get; set; }
}

public class DependencyPathDto
{
    public string Path { get; set; } = string.Empty;
    public string Method { get; set; } = string.Empty;
    public double AverageResponseTimeMs { get; set; }
    public int RequestCount { get; set; }
    public double ErrorRate { get; set; }
    public DateTime LastUsed { get; set; }
}
