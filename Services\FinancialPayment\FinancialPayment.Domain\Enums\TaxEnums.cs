using Shared.Domain.Common;

namespace FinancialPayment.Domain.Enums;

/// <summary>
/// GST tax slabs as per Indian tax system
/// </summary>
public enum GstRate
{
    Zero = 0,
    Five = 5,
    Twelve = 12,
    Eighteen = 18,
    TwentyEight = 28
}

/// <summary>
/// TDS sections as per Indian Income Tax Act
/// </summary>
public enum TdsSection
{
    Section194C = 194, // Payments to contractors
    Section194J = 1941, // Professional/technical services
    Section194I = 1942, // Rent payments
    Section194H = 1943, // Commission/brokerage
    Section194A = 1944, // Interest payments
    Section194O = 1945, // E-commerce transactions
    Section194Q = 1946, // Purchase of goods
    Section194S = 1947  // Cryptocurrency transactions
}

/// <summary>
/// Entity types for TDS calculation
/// </summary>
public enum EntityType
{
    Individual = 1,
    Company = 2,
    Partnership = 3,
    LLP = 4,
    Trust = 5,
    Society = 6,
    HUF = 7, // Hindu Undivided Family
    Government = 8,
    ForeignCompany = 9
}

/// <summary>
/// Tax configuration status
/// </summary>
public enum TaxConfigurationStatus
{
    Draft = 1,
    Active = 2,
    Inactive = 3,
    Archived = 4
}

/// <summary>
/// Service categories for tax calculation
/// </summary>
public enum ServiceCategory
{
    Transportation = 1,
    Logistics = 2,
    Warehousing = 3,
    Packaging = 4,
    Insurance = 5,
    Brokerage = 6,
    Consulting = 7,
    Technology = 8,
    Other = 99
}

/// <summary>
/// Location types for tax jurisdiction
/// </summary>
public enum LocationType
{
    Country = 1,
    State = 2,
    City = 3,
    Zone = 4
}

/// <summary>
/// Tax calculation method
/// </summary>
public enum TaxCalculationMethod
{
    Percentage = 1,
    FixedAmount = 2,
    Slab = 3,
    Progressive = 4
}

/// <summary>
/// HSN code status
/// </summary>
public enum HsnCodeStatus
{
    Active = 1,
    Inactive = 2,
    Deprecated = 3
}

