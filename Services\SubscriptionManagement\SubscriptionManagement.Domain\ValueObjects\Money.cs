using SubscriptionManagement.Domain.Exceptions;

namespace SubscriptionManagement.Domain.ValueObjects
{
    public class Money : IEquatable<Money>
    {
        public decimal Amount { get; private set; }
        public string Currency { get; private set; }

        private Money() { }

        public Money(decimal amount, string currency)
        {
            if (amount < 0)
                throw new SubscriptionDomainException("Amount cannot be negative");

            if (string.IsNullOrWhiteSpace(currency))
                throw new SubscriptionDomainException("Currency cannot be null or empty");

            if (currency.Length != 3)
                throw new SubscriptionDomainException("Currency must be a 3-letter ISO code");

            Amount = Math.Round(amount, 2);
            Currency = currency.ToUpperInvariant();
        }

        public static Money Create(decimal amount, string currency)
        {
            return new Money(amount, currency);
        }

        public static Money Zero(string currency)
        {
            return new Money(0, currency);
        }

        public Money Add(Money other)
        {
            if (Currency != other.Currency)
                throw new SubscriptionDomainException("Cannot add money with different currencies");

            return new Money(Amount + other.Amount, Currency);
        }

        public Money Subtract(Money other)
        {
            if (Currency != other.Currency)
                throw new SubscriptionDomainException("Cannot subtract money with different currencies");

            var result = Amount - other.Amount;
            if (result < 0)
                throw new SubscriptionDomainException("Result cannot be negative");

            return new Money(result, Currency);
        }

        public Money Multiply(decimal factor)
        {
            if (factor < 0)
                throw new SubscriptionDomainException("Factor cannot be negative");

            return new Money(Amount * factor, Currency);
        }

        public Money ApplyDiscount(decimal discountPercentage)
        {
            if (discountPercentage < 0 || discountPercentage > 100)
                throw new SubscriptionDomainException("Discount percentage must be between 0 and 100");

            var discountAmount = Amount * (discountPercentage / 100);
            return new Money(Amount - discountAmount, Currency);
        }

        public bool Equals(Money? other)
        {
            if (other is null) return false;
            return Amount == other.Amount && Currency == other.Currency;
        }

        public override bool Equals(object? obj)
        {
            return Equals(obj as Money);
        }

        public override int GetHashCode()
        {
            return HashCode.Combine(Amount, Currency);
        }

        public static bool operator ==(Money? left, Money? right)
        {
            return EqualityComparer<Money>.Default.Equals(left, right);
        }

        public static bool operator !=(Money? left, Money? right)
        {
            return !(left == right);
        }

        public static bool operator >(Money left, Money right)
        {
            if (left.Currency != right.Currency)
                throw new SubscriptionDomainException("Cannot compare money with different currencies");

            return left.Amount > right.Amount;
        }

        public static bool operator <(Money left, Money right)
        {
            if (left.Currency != right.Currency)
                throw new SubscriptionDomainException("Cannot compare money with different currencies");

            return left.Amount < right.Amount;
        }

        public static bool operator >=(Money left, Money right)
        {
            return left > right || left == right;
        }

        public static bool operator <=(Money left, Money right)
        {
            return left < right || left == right;
        }

        public override string ToString()
        {
            return $"{Amount:F2} {Currency}";
        }
    }
}
