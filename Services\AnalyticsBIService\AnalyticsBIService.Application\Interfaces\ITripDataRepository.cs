using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using AnalyticsBIService.Application.DTOs;
using Shared.Infrastructure.Repositories;

namespace AnalyticsBIService.Application.Interfaces;

/// <summary>
/// Repository interface for trip data operations
/// </summary>
public interface ITripDataRepository
{
    /// <summary>
    /// Gets trip data for export
    /// </summary>
    Task<PagedResult<TripDataDto>> GetTripDataAsync(
        DateTime startDate,
        DateTime endDate,
        int page = 1,
        int pageSize = 100,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets trip analytics data
    /// </summary>
    Task<List<TripAnalyticsDto>> GetTripAnalyticsAsync(
        DateTime startDate,
        DateTime endDate,
        Guid? driverId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets trip summary data
    /// </summary>
    Task<TripSummaryDto> GetTripSummaryAsync(
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default);

    // Additional methods required by OrderTripExportService
    Task<List<TripDataDto>> GetTripsForExportAsync(TripDataFilter filter, CancellationToken cancellationToken = default);
}

/// <summary>
/// Trip data transfer object
/// </summary>
public class TripDataDto
{
    public Guid TripId { get; set; }
    public string TripNumber { get; set; } = string.Empty;
    public Guid OrderId { get; set; }
    public Guid DriverId { get; set; }
    public string DriverName { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public string Status { get; set; } = string.Empty;
    public string Origin { get; set; } = string.Empty;
    public string Destination { get; set; } = string.Empty;
    public decimal Distance { get; set; }
    public decimal FuelConsumed { get; set; }
    public decimal Cost { get; set; }
}

/// <summary>
/// Trip analytics data transfer object
/// </summary>
public class TripAnalyticsDto
{
    public DateTime Date { get; set; }
    public int TripCount { get; set; }
    public decimal TotalDistance { get; set; }
    public decimal TotalCost { get; set; }
    public decimal AverageCost { get; set; }
    public string Status { get; set; } = string.Empty;
}

/// <summary>
/// Trip summary data transfer object
/// </summary>
public class TripSummaryDto
{
    public int TotalTrips { get; set; }
    public decimal TotalDistance { get; set; }
    public decimal TotalCost { get; set; }
    public decimal AverageTripCost { get; set; }
    public int CompletedTrips { get; set; }
    public int InProgressTrips { get; set; }
    public int CancelledTrips { get; set; }
}

/// <summary>
/// Trip data filter for export operations
/// </summary>
public class TripDataFilter
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public Guid? DriverId { get; set; }
    public Guid? VehicleId { get; set; }
    public Guid? CarrierId { get; set; }
    public string? Status { get; set; }
    public bool IncludePerformanceMetrics { get; set; }
    public bool IncludeFinancials { get; set; }
}
