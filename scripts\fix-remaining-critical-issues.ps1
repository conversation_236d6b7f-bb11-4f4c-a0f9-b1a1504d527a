#!/usr/bin/env pwsh

Write-Host "Fixing remaining critical issues..." -ForegroundColor Green

# Fix missing ValueObject base class references
Write-Host "Adding missing ValueObject using statements..." -ForegroundColor Yellow

$valueObjectFiles = @(
    "Services/MonitoringObservability/MonitoringObservability.Domain/ValueObjects/MetricValue.cs",
    "Services/CommunicationNotification/CommunicationNotification.Domain/ValueObjects/AdvancedAnalyticsMetrics.cs"
)

foreach ($file in $valueObjectFiles) {
    if (Test-Path $file) {
        $content = Get-Content $file -Raw
        if ($content -notmatch "using Shared\.Domain\.ValueObjects;") {
            $content = $content -replace "(using [^;]+;)", "`$1`nusing Shared.Domain.ValueObjects;"
            Set-Content $file $content -NoNewline
            Write-Host "Added ValueObject using statement to $file" -ForegroundColor Green
        }
    }
}

# Fix missing type definitions by creating placeholder classes
Write-Host "Creating missing type definitions..." -ForegroundColor Yellow

# Create TemplateVersion class
$templateVersionPath = "Services/CommunicationNotification/CommunicationNotification.Domain/ValueObjects/TemplateVersion.cs"
if (-not (Test-Path $templateVersionPath)) {
    $templateVersionContent = @"
using Shared.Domain.ValueObjects;

namespace CommunicationNotification.Domain.ValueObjects;

public class TemplateVersion : ValueObject
{
    public int Major { get; private set; }
    public int Minor { get; private set; }
    public int Patch { get; private set; }
    public string? Label { get; private set; }

    private TemplateVersion() { }

    public TemplateVersion(int major, int minor, int patch, string? label = null)
    {
        Major = major;
        Minor = minor;
        Patch = patch;
        Label = label;
    }

    public override string ToString() => Label != null ? $"{Major}.{Minor}.{Patch}-{Label}" : $"{Major}.{Minor}.{Patch}";

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Major;
        yield return Minor;
        yield return Patch;
        yield return Label ?? string.Empty;
    }
}
"@
    Set-Content $templateVersionPath $templateVersionContent -NoNewline
    Write-Host "Created TemplateVersion class" -ForegroundColor Green
}

# Create WorkflowTemplateRating class
$workflowRatingPath = "Services/MobileWorkflow/MobileWorkflow.Domain/ValueObjects/WorkflowTemplateRating.cs"
if (-not (Test-Path $workflowRatingPath)) {
    $workflowRatingContent = @"
using Shared.Domain.ValueObjects;

namespace MobileWorkflow.Domain.ValueObjects;

public class WorkflowTemplateRating : ValueObject
{
    public decimal AverageRating { get; private set; }
    public int TotalRatings { get; private set; }
    public DateTime LastUpdated { get; private set; }

    private WorkflowTemplateRating() { }

    public WorkflowTemplateRating(decimal averageRating, int totalRatings)
    {
        AverageRating = averageRating;
        TotalRatings = totalRatings;
        LastUpdated = DateTime.UtcNow;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return AverageRating;
        yield return TotalRatings;
    }
}
"@
    Set-Content $workflowRatingPath $workflowRatingContent -NoNewline
    Write-Host "Created WorkflowTemplateRating class" -ForegroundColor Green
}

# Create IdentityDbContext class
$identityDbContextPath = "Identity/Identity.Infrastructure/Data/IdentityDbContext.cs"
if (-not (Test-Path $identityDbContextPath)) {
    $identityDbContextContent = @"
using Microsoft.EntityFrameworkCore;
using Identity.Domain.Entities;

namespace Identity.Infrastructure.Data;

public class IdentityDbContext : DbContext
{
    public IdentityDbContext(DbContextOptions<IdentityDbContext> options) : base(options) { }

    public DbSet<OtpToken> OtpTokens { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);
        
        modelBuilder.Entity<OtpToken>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Token).IsRequired().HasMaxLength(10);
            entity.Property(e => e.PhoneNumber).IsRequired().HasMaxLength(15);
            entity.HasIndex(e => e.PhoneNumber);
        });
    }
}
"@
    Set-Content $identityDbContextPath $identityDbContextContent -NoNewline
    Write-Host "Created IdentityDbContext class" -ForegroundColor Green
}

Write-Host "Critical issues fixes completed!" -ForegroundColor Green
