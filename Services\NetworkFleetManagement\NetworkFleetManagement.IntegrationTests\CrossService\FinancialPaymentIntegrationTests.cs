using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Entities;
using NetworkFleetManagement.Domain.Repositories;
using NetworkFleetManagement.IntegrationTests.TestFixtures;
using System.Net.Http.Json;
using Xunit;

namespace NetworkFleetManagement.IntegrationTests.CrossService;

[Collection("NetworkFleet")]
public class FinancialPaymentIntegrationTests : IClassFixture<NetworkFleetTestFixture>
{
    private readonly NetworkFleetTestFixture _fixture;
    private readonly HttpClient _client;
    private readonly Guid _testUserId = Guid.NewGuid();

    public FinancialPaymentIntegrationTests(NetworkFleetTestFixture fixture)
    {
        _fixture = fixture;
        _client = _fixture.CreateClient();
        
        _client.DefaultRequestHeaders.Add("X-User-Id", _testUserId.ToString());
        _client.DefaultRequestHeaders.Add("X-User-Role", "Broker");
    }

    [Fact]
    public async Task PaymentCompletion_UpdatesPartnerFinancialMetrics()
    {
        // Arrange
        await _fixture.ResetDatabaseAsync();
        var partnerId = await CreateTestPartnerAsync();

        // Simulate payment completion event from Financial & Payment service
        var paymentData = new
        {
            PaymentId = Guid.NewGuid(),
            PartnerId = partnerId,
            Amount = 5000.00m,
            Currency = "USD",
            PaymentDate = DateTime.UtcNow,
            PaymentMethod = "BankTransfer",
            TransactionFee = 25.00m,
            CommissionRate = 0.15m
        };

        // Act - Process payment completion
        using var scope = _fixture.Services.CreateScope();
        var repository = scope.ServiceProvider.GetRequiredService<IPreferredPartnerRepository>();
        
        var partner = await repository.GetByIdAsync(partnerId);
        partner.Should().NotBeNull();

        // Update financial metrics (this would typically be done via event handler)
        partner!.UpdateFinancialTerms(
            paymentData.CommissionRate,
            paymentData.Amount / 10, // Service rate
            30, // Payment terms days
            "Net 30 payment terms");

        await repository.UpdateAsync(partner);

        // Assert
        var updatedPartner = await repository.GetByIdAsync(partnerId);
        updatedPartner.Should().NotBeNull();
        updatedPartner!.PreferredCommissionRate.Should().Be(paymentData.CommissionRate);
        updatedPartner.PaymentTermsDays.Should().Be(30);
    }

    [Fact]
    public async Task CommissionRateNegotiation_UpdatesPreferredRates()
    {
        // Arrange
        await _fixture.ResetDatabaseAsync();
        var partnerId = await CreateTestPartnerAsync();

        var negotiationData = new
        {
            PartnerId = partnerId,
            NewCommissionRate = 0.12m, // Reduced from 0.15m
            EffectiveDate = DateTime.UtcNow.AddDays(30),
            NegotiatedBy = _testUserId,
            Reason = "Volume discount for preferred partner"
        };

        // Act - Update commission rate
        var updateDto = new UpdatePreferredPartnerDto
        {
            PreferenceLevel = "High",
            Priority = 1,
            PreferredCommissionRate = negotiationData.NewCommissionRate,
            Notes = negotiationData.Reason
        };

        var response = await _client.PutAsJsonAsync($"/api/v1/preferredpartners/{partnerId}", updateDto);

        // Assert
        response.Should().BeSuccessful();

        // Verify in database
        using var scope = _fixture.Services.CreateScope();
        var repository = scope.ServiceProvider.GetRequiredService<IPreferredPartnerRepository>();
        var updatedPartner = await repository.GetByIdAsync(partnerId);
        
        updatedPartner.Should().NotBeNull();
        updatedPartner!.PreferredCommissionRate.Should().Be(negotiationData.NewCommissionRate);
        updatedPartner.Notes.Should().Contain(negotiationData.Reason);
    }

    [Fact]
    public async Task PaymentDelays_AffectPartnerRating()
    {
        // Arrange
        await _fixture.ResetDatabaseAsync();
        var partnerId = await CreateTestPartnerAsync();

        // Simulate payment delay event
        var paymentDelayData = new
        {
            PaymentId = Guid.NewGuid(),
            PartnerId = partnerId,
            OriginalDueDate = DateTime.UtcNow.AddDays(-5),
            ActualPaymentDate = DateTime.UtcNow,
            DelayDays = 5,
            DelayReason = "Bank processing delay"
        };

        // Act - Process payment delay impact
        using var scope = _fixture.Services.CreateScope();
        var repository = scope.ServiceProvider.GetRequiredService<IPreferredPartnerRepository>();
        
        var partner = await repository.GetByIdAsync(partnerId);
        partner.Should().NotBeNull();

        // Reduce rating slightly for payment delays
        var currentMetrics = partner!.PerformanceMetrics;
        partner.UpdatePerformanceMetrics(
            Math.Max(1.0m, currentMetrics.OverallRating - 0.1m), // Small penalty
            currentMetrics.OnTimePerformance,
            currentMetrics.QualityScore,
            currentMetrics.CommunicationRating,
            currentMetrics.AverageResponseTime);

        await repository.UpdateAsync(partner);

        // Assert
        var updatedPartner = await repository.GetByIdAsync(partnerId);
        updatedPartner.Should().NotBeNull();
        updatedPartner!.PerformanceMetrics.OverallRating.Should().BeLessThan(currentMetrics.OverallRating);
    }

    [Fact]
    public async Task InvoiceDispute_TemporarilyDisablesAutoAssignment()
    {
        // Arrange
        await _fixture.ResetDatabaseAsync();
        var partnerId = await CreateTestPartnerAsync();

        // Simulate invoice dispute event
        var disputeData = new
        {
            InvoiceId = Guid.NewGuid(),
            PartnerId = partnerId,
            DisputeAmount = 1500.00m,
            DisputeReason = "Service quality issues",
            DisputeDate = DateTime.UtcNow,
            Status = "Under Review"
        };

        // Act - Disable auto-assignment during dispute
        var updateDto = new UpdateAutoAssignmentSettingsDto
        {
            AutoAssignEnabled = false,
            AutoAssignThreshold = null,
            PreferredRoutes = new List<string> { "Route1", "Route2" },
            PreferredLoadTypes = new List<string> { "Dry Van" }
        };

        var response = await _client.PutAsJsonAsync($"/api/v1/preferredpartners/{partnerId}/auto-assignment", updateDto);

        // Assert
        response.Should().BeSuccessful();

        // Verify auto-assignment is disabled
        using var scope = _fixture.Services.CreateScope();
        var repository = scope.ServiceProvider.GetRequiredService<IPreferredPartnerRepository>();
        var updatedPartner = await repository.GetByIdAsync(partnerId);
        
        updatedPartner.Should().NotBeNull();
        updatedPartner!.AutoAssignEnabled.Should().BeFalse();

        // Verify partner doesn't appear in auto-assignment results
        var autoAssignResponse = await _client.PostAsJsonAsync("/api/v1/preferredpartners/test-auto-assignment", 
            new TestAutoAssignmentRequest
            {
                PartnerType = "Carrier",
                Route = "Route1",
                LoadType = "Dry Van",
                MaxResults = 10
            });

        autoAssignResponse.Should().BeSuccessful();
        var eligiblePartners = await autoAssignResponse.Content.ReadFromJsonAsync<List<PreferredPartnerSummaryDto>>();
        eligiblePartners.Should().NotBeNull();
        eligiblePartners!.Should().NotContain(p => p.PartnerId == partnerId);
    }

    [Fact]
    public async Task PaymentTermsUpdate_ReflectsInPartnerAgreement()
    {
        // Arrange
        await _fixture.ResetDatabaseAsync();
        var partnerId = await CreateTestPartnerAsync();

        // Simulate payment terms update from Financial service
        var termsUpdateData = new
        {
            PartnerId = partnerId,
            NewPaymentTerms = 45, // Changed from 30 to 45 days
            EffectiveDate = DateTime.UtcNow.AddDays(1),
            UpdatedBy = _testUserId,
            Reason = "Extended terms for preferred partner"
        };

        // Act - Update payment terms
        using var scope = _fixture.Services.CreateScope();
        var repository = scope.ServiceProvider.GetRequiredService<IPreferredPartnerRepository>();
        
        var partner = await repository.GetByIdAsync(partnerId);
        partner.Should().NotBeNull();

        partner!.UpdateFinancialTerms(
            partner.PreferredCommissionRate,
            partner.PreferredServiceRate,
            termsUpdateData.NewPaymentTerms,
            $"Updated payment terms: {termsUpdateData.Reason}");

        await repository.UpdateAsync(partner);

        // Assert
        var updatedPartner = await repository.GetByIdAsync(partnerId);
        updatedPartner.Should().NotBeNull();
        updatedPartner!.PaymentTermsDays.Should().Be(termsUpdateData.NewPaymentTerms);
        updatedPartner.SpecialTerms.Should().Contain(termsUpdateData.Reason);
    }

    [Fact]
    public async Task FinancialAnalytics_IncludesCommissionData()
    {
        // Arrange
        await _fixture.ResetDatabaseAsync();
        var partnerId = await CreateTestPartnerAsync();

        // Simulate financial transactions
        await SimulateFinancialTransactionsAsync(partnerId);

        // Act - Get partner analytics with financial data
        var response = await _client.GetAsync($"/api/v1/preferredpartners/{partnerId}/analytics?fromDate=2024-01-01&toDate=2024-12-31");

        // Assert
        response.Should().BeSuccessful();
        
        var analytics = await response.Content.ReadFromJsonAsync<PartnerAnalyticsDto>();
        analytics.Should().NotBeNull();
        analytics!.PreferredPartnerId.Should().Be(partnerId);
        analytics.TotalValue.Should().BeGreaterOrEqualTo(0);
        analytics.AverageOrderValue.Should().BeGreaterOrEqualTo(0);
    }

    private async Task<Guid> CreateTestPartnerAsync()
    {
        var partner = PreferredPartner.Create(
            _testUserId,
            Guid.NewGuid(),
            PreferredPartnerType.Carrier,
            PreferenceLevel.High,
            1);

        partner.Activate();
        partner.UpdateAutoAssignSettings(true, 4.0m,
            new List<string> { "Route1", "Route2" },
            new List<string> { "Dry Van", "Refrigerated" },
            new List<string>(),
            new List<string>());

        partner.UpdateFinancialTerms(0.15m, 500.00m, 30, "Standard terms");

        using var context = _fixture.GetDbContext();
        context.PreferredPartners.Add(partner);
        await context.SaveChangesAsync();

        return partner.Id;
    }

    private async Task SimulateFinancialTransactionsAsync(Guid partnerId)
    {
        using var scope = _fixture.Services.CreateScope();
        var repository = scope.ServiceProvider.GetRequiredService<IPreferredPartnerRepository>();

        var partner = await repository.GetByIdAsync(partnerId);
        if (partner == null) return;

        // Simulate multiple successful transactions
        for (int i = 0; i < 3; i++)
        {
            partner.UpdatePerformanceMetrics(
                4.5m, // Good rating
                0.95m, // 95% on-time
                4.6m,
                4.4m,
                6.0m); // 6 hours average
        }

        await repository.UpdateAsync(partner);
    }
}
