using Shared.Domain.ValueObjects;

namespace DataStorage.Domain.ValueObjects;

/// <summary>
/// Request for SLA report generation
/// </summary>
public class SlaReportRequest : ValueObject
{
    public string ReportName { get; init; }
    public DateTime FromDate { get; init; }
    public DateTime ToDate { get; init; }
    public List<string>? ServiceNames { get; init; }
    public List<string>? SlaTypes { get; init; }
    public string? ComplianceThreshold { get; init; }
    public bool IncludeViolations { get; init; }
    public bool IncludeTrends { get; init; }
    public string? GroupBy { get; init; }
    public string? OutputFormat { get; init; }

    public SlaReportRequest(
        string reportName,
        DateTime fromDate,
        DateTime toDate,
        List<string>? serviceNames = null,
        List<string>? slaTypes = null,
        string? complianceThreshold = null,
        bool includeViolations = true,
        bool includeTrends = true,
        string? groupBy = null,
        string? outputFormat = null)
    {
        ReportName = reportName;
        FromDate = fromDate;
        ToDate = toDate;
        ServiceNames = serviceNames;
        SlaTypes = slaTypes;
        ComplianceThreshold = complianceThreshold;
        IncludeViolations = includeViolations;
        IncludeTrends = includeTrends;
        GroupBy = groupBy;
        OutputFormat = outputFormat;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return ReportName;
        yield return FromDate;
        yield return ToDate;
        yield return ComplianceThreshold ?? string.Empty;
        yield return IncludeViolations;
        yield return IncludeTrends;
        yield return GroupBy ?? string.Empty;
        yield return OutputFormat ?? string.Empty;
        
        if (ServiceNames != null)
        {
            foreach (var serviceName in ServiceNames.OrderBy(x => x))
            {
                yield return serviceName;
            }
        }
        
        if (SlaTypes != null)
        {
            foreach (var slaType in SlaTypes.OrderBy(x => x))
            {
                yield return slaType;
            }
        }
    }
}
