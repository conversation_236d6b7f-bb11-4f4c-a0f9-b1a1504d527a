using MediatR;
using Microsoft.Extensions.Logging;
using TripManagement.Application.Interfaces;
using TripManagement.Domain.Entities;
using TripManagement.Domain.Enums;

namespace TripManagement.Application.Commands.VerifyRecipient;

public class VerifyRecipientCommandHandler : IRequestHandler<VerifyRecipientCommand, bool>
{
    private readonly ITripRepository _tripRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<VerifyRecipientCommandHandler> _logger;
    private readonly IMessageBroker _messageBroker;

    public VerifyRecipientCommandHandler(
        ITripRepository tripRepository,
        IUnitOfWork unitOfWork,
        ILogger<VerifyRecipientCommandHandler> logger,
        IMessageBroker messageBroker)
    {
        _tripRepository = tripRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
        _messageBroker = messageBroker;
    }

    public async Task<bool> Handle(VerifyRecipientCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Verifying recipient for trip stop {TripStopId} by driver {DriverId}", 
                request.TripStopId, request.DriverId);

            // Find the trip stop
            var tripStop = await _tripRepository.GetTripStopByIdAsync(request.TripStopId, cancellationToken);
            if (tripStop == null)
            {
                _logger.LogWarning("Trip stop {TripStopId} not found", request.TripStopId);
                return false;
            }

            // Verify the driver is assigned to this trip
            var trip = await _tripRepository.GetByIdAsync(tripStop.TripId, cancellationToken);
            if (trip == null || trip.DriverId != request.DriverId)
            {
                _logger.LogWarning("Driver {DriverId} is not authorized for trip {TripId}", 
                    request.DriverId, tripStop.TripId);
                return false;
            }

            // Create recipient verification document
            var verificationNotes = $"Recipient verified: {request.RecipientName}";
            if (!string.IsNullOrEmpty(request.IdentificationNumber))
            {
                verificationNotes += $"\nID: {request.IdentificationType} - {request.IdentificationNumber}";
            }
            if (!string.IsNullOrEmpty(request.RecipientPhone))
            {
                verificationNotes += $"\nPhone: {request.RecipientPhone}";
            }
            if (!string.IsNullOrEmpty(request.Notes))
            {
                verificationNotes += $"\nNotes: {request.Notes}";
            }

            // Create trip document for verification
            var verificationDocument = new TripDocument(
                tripId: trip.Id,
                documentType: DocumentType.Other,
                fileName: $"recipient_verification_{request.TripStopId}_{DateTime.UtcNow:yyyyMMddHHmmss}.txt",
                fileUrl: "", // This would be generated by a document service
                description: "Recipient Verification",
                uploadedBy: request.DriverId.ToString());

            trip.AddDocument(verificationDocument);

            // Update trip stop with recipient information
            tripStop.UpdateContactInfo(request.RecipientName, request.RecipientPhone);

            // Save changes
            _tripRepository.Update(trip);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Publish integration event
            await _messageBroker.PublishAsync("trip.recipient_verified", new
            {
                TripId = trip.Id,
                TripStopId = request.TripStopId,
                DriverId = request.DriverId,
                RecipientName = request.RecipientName,
                RecipientPhone = request.RecipientPhone,
                IdentificationNumber = request.IdentificationNumber,
                IdentificationType = request.IdentificationType,
                VerifiedAt = DateTime.UtcNow
            }, cancellationToken);

            _logger.LogInformation("Recipient verified successfully for trip stop {TripStopId}", 
                request.TripStopId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error verifying recipient for trip stop {TripStopId}", 
                request.TripStopId);
            throw;
        }
    }
}
