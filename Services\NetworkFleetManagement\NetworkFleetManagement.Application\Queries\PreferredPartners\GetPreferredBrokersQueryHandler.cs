using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Entities;
using NetworkFleetManagement.Domain.Repositories;

namespace NetworkFleetManagement.Application.Queries.PreferredPartners;

public class GetPreferredBrokersQueryHandler : IRequestHandler<GetPreferredBrokersQuery, List<PreferredPartnerSummaryDto>>
{
    private readonly IPreferredPartnerRepository _preferredPartnerRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetPreferredBrokersQueryHandler> _logger;

    public GetPreferredBrokersQueryHandler(
        IPreferredPartnerRepository preferredPartnerRepository,
        IMapper mapper,
        ILogger<GetPreferredBrokersQueryHandler> logger)
    {
        _preferredPartnerRepository = preferredPartnerRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<List<PreferredPartnerSummaryDto>> Handle(GetPreferredBrokersQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var partners = await _preferredPartnerRepository.GetByUserIdAndPartnerTypeAsync(
                request.TransporterId, 
                PreferredPartnerType.Broker, 
                request.ActiveOnly, 
                cancellationToken);

            // Filter by route and load type if specified
            if (!string.IsNullOrEmpty(request.Route) || !string.IsNullOrEmpty(request.LoadType))
            {
                partners = partners.Where(p => 
                    (string.IsNullOrEmpty(request.Route) || p.IsEligibleForRoute(request.Route)) &&
                    (string.IsNullOrEmpty(request.LoadType) || p.IsEligibleForLoadType(request.LoadType))
                ).ToList();
            }

            var result = _mapper.Map<List<PreferredPartnerSummaryDto>>(partners);

            if (request.Limit.HasValue)
            {
                result = result.Take(request.Limit.Value).ToList();
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving preferred brokers for transporter {TransporterId}", request.TransporterId);
            throw;
        }
    }
}
