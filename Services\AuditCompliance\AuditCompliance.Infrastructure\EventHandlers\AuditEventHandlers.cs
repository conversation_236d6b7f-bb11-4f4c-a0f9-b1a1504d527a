using MediatR;
using Shared.Messaging;
using Shared.Messaging.Events;
using AuditCompliance.Domain.Events;
using AuditCompliance.Application.Interfaces;
using Microsoft.Extensions.Logging;

namespace AuditCompliance.Infrastructure.EventHandlers;

/// <summary>
/// Handles audit log domain events and publishes integration events
/// </summary>
public class AuditLogCreatedEventHandler : INotificationHandler<AuditLogCreatedEvent>
{
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<AuditLogCreatedEventHandler> _logger;

    public AuditLogCreatedEventHandler(
        IMessageBroker messageBroker,
        ILogger<AuditLogCreatedEventHandler> logger)
    {
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task Handle(AuditLogCreatedEvent notification, CancellationToken cancellationToken)
    {
        try
        {
            var integrationEvent = new Shared.Messaging.Events.AuditLogCreatedEvent
            {
                AuditLogId = notification.AuditLog.Id,
                EventType = notification.AuditLog.EventType.ToString(),
                Severity = notification.AuditLog.Severity.ToString(),
                UserId = notification.AuditLog.UserId,
                UserName = notification.AuditLog.UserName,
                EntityType = notification.AuditLog.EntityType,
                EntityId = notification.AuditLog.EntityId,
                Action = notification.AuditLog.Action,
                Description = notification.AuditLog.Description,
                EventTimestamp = notification.AuditLog.EventTimestamp,
                ComplianceFlags = notification.AuditLog.ComplianceFlags.Select(cf => cf.ToString()).ToList(),
                IpAddress = notification.AuditLog.Context.IpAddress,
                SessionId = notification.AuditLog.Context.SessionId
            };

            await _messageBroker.PublishAsync(integrationEvent, cancellationToken);

            _logger.LogInformation("Published AuditLogCreatedEvent for audit log: {AuditLogId}", 
                notification.AuditLog.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing AuditLogCreatedEvent for audit log: {AuditLogId}", 
                notification.AuditLog.Id);
        }
    }
}

/// <summary>
/// Handles compliance report domain events and publishes integration events
/// </summary>
public class ComplianceReportCreatedEventHandler : INotificationHandler<ComplianceReportCreatedEvent>
{
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<ComplianceReportCreatedEventHandler> _logger;

    public ComplianceReportCreatedEventHandler(
        IMessageBroker messageBroker,
        ILogger<ComplianceReportCreatedEventHandler> logger)
    {
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task Handle(ComplianceReportCreatedEvent notification, CancellationToken cancellationToken)
    {
        try
        {
            var integrationEvent = new ComplianceReportGeneratedEvent
            {
                ReportId = notification.ComplianceReport.Id,
                ReportNumber = notification.ComplianceReport.ReportNumber,
                Title = notification.ComplianceReport.Title,
                Standard = notification.ComplianceReport.Standard.ToString(),
                Status = notification.ComplianceReport.Status.ToString(),
                ReportPeriodStart = notification.ComplianceReport.ReportPeriodStart,
                ReportPeriodEnd = notification.ComplianceReport.ReportPeriodEnd,
                GeneratedBy = notification.ComplianceReport.GeneratedBy,
                GeneratedByName = notification.ComplianceReport.GeneratedByName,
                IsAutomated = notification.ComplianceReport.IsAutomated,
                CreatedAt = notification.ComplianceReport.CreatedAt
            };

            await _messageBroker.PublishAsync(integrationEvent, cancellationToken);

            _logger.LogInformation("Published ComplianceReportGeneratedEvent for report: {ReportId}", 
                notification.ComplianceReport.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing ComplianceReportGeneratedEvent for report: {ReportId}", 
                notification.ComplianceReport.Id);
        }
    }
}

public class ComplianceReportCompletedEventHandler : INotificationHandler<ComplianceReportCompletedEvent>
{
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<ComplianceReportCompletedEventHandler> _logger;

    public ComplianceReportCompletedEventHandler(
        IMessageBroker messageBroker,
        ILogger<ComplianceReportCompletedEventHandler> logger)
    {
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task Handle(ComplianceReportCompletedEvent notification, CancellationToken cancellationToken)
    {
        try
        {
            var integrationEvent = new Shared.Messaging.Events.ComplianceReportCompletedEvent
            {
                ReportId = notification.ComplianceReport.Id,
                ReportNumber = notification.ComplianceReport.ReportNumber,
                Standard = notification.ComplianceReport.Standard.ToString(),
                Status = notification.ComplianceReport.Status.ToString(),
                TotalViolations = notification.ComplianceReport.TotalViolations,
                CriticalViolations = notification.ComplianceReport.CriticalViolations,
                HighViolations = notification.ComplianceReport.HighViolations,
                Findings = notification.ComplianceReport.Findings,
                Recommendations = notification.ComplianceReport.Recommendations,
                CompletedAt = notification.ComplianceReport.CompletedAt ?? DateTime.UtcNow
            };

            await _messageBroker.PublishAsync(integrationEvent, cancellationToken);

            // Check for critical violations and publish violation events
            if (notification.ComplianceReport.CriticalViolations > 0)
            {
                var criticalItems = notification.ComplianceReport.Items
                    .Where(item => item.IsViolation && item.Severity == Domain.Enums.AuditSeverity.Critical);

                foreach (var item in criticalItems)
                {
                    var violationEvent = new ComplianceViolationDetectedEvent
                    {
                        ViolationId = item.Id,
                        Standard = notification.ComplianceReport.Standard.ToString(),
                        Severity = item.Severity.ToString(),
                        ViolationType = item.CheckName,
                        Description = item.Description,
                        Evidence = item.Evidence,
                        Recommendation = item.Recommendation,
                        DetectedAt = item.CheckedAt,
                        RequiresImmediateAction = true
                    };

                    await _messageBroker.PublishAsync(violationEvent, cancellationToken);
                }
            }

            _logger.LogInformation("Published ComplianceReportCompletedEvent for report: {ReportId}", 
                notification.ComplianceReport.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing ComplianceReportCompletedEvent for report: {ReportId}", 
                notification.ComplianceReport.Id);
        }
    }
}
