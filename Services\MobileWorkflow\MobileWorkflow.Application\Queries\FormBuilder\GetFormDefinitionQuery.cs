using System;
using MediatR;
using MobileWorkflow.Application.DTOs;

namespace MobileWorkflow.Application.Queries.FormBuilder
{
    public class GetFormDefinitionQuery : IRequest<FormDefinitionDto?>
    {
        public Guid FormDefinitionId { get; set; }
        public bool IncludeFields { get; set; } = true;
        public bool IncludeSteps { get; set; } = true;
        public string? Platform { get; set; }
    }
}
