using MediatR;
using Microsoft.Extensions.Logging;
using FinancialPayment.Application.DTOs;
using FinancialPayment.Application.Interfaces.Repositories;

namespace FinancialPayment.Application.Queries.GetEscrowAccountByOrder;

public class GetEscrowAccountByOrderQueryHandler : IRequestHandler<GetEscrowAccountByOrderQuery, EscrowAccountDto?>
{
    private readonly IEscrowAccountRepository _escrowAccountRepository;
    private readonly ILogger<GetEscrowAccountByOrderQueryHandler> _logger;

    public GetEscrowAccountByOrderQueryHandler(
        IEscrowAccountRepository escrowAccountRepository,
        ILogger<GetEscrowAccountByOrderQueryHandler> logger)
    {
        _escrowAccountRepository = escrowAccountRepository;
        _logger = logger;
    }

    public async Task<EscrowAccountDto?> Handle(GetEscrowAccountByOrderQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Getting escrow account for Order {OrderId}", request.OrderId);

            var escrowAccount = await _escrowAccountRepository.GetByOrderIdAsync(request.OrderId);
            if (escrowAccount == null)
            {
                _logger.LogWarning("Escrow account not found for Order {OrderId}", request.OrderId);
                return null;
            }

            return new EscrowAccountDto
            {
                Id = escrowAccount.Id,
                OrderId = escrowAccount.OrderId,
                TransportCompanyId = escrowAccount.TransportCompanyId,
                BrokerId = escrowAccount.BrokerId,
                CarrierId = escrowAccount.CarrierId,
                AccountNumber = escrowAccount.EscrowAccountNumber,
                TotalAmount = escrowAccount.TotalAmount.Amount,
                TotalCurrency = escrowAccount.TotalAmount.Currency,
                AvailableAmount = escrowAccount.AvailableAmount.Amount,
                AvailableCurrency = escrowAccount.AvailableAmount.Currency,
                ReservedAmount = escrowAccount.ReservedAmount.Amount,
                ReservedCurrency = escrowAccount.ReservedAmount.Currency,
                Status = escrowAccount.Status.ToString(),
                FundedAt = escrowAccount.FundedAt,
                ReleasedAt = escrowAccount.ReleasedAt,
                ReleaseReason = escrowAccount.ReleaseReason,
                Notes = escrowAccount.Notes,
                CreatedAt = escrowAccount.CreatedAt,
                UpdatedAt = escrowAccount.UpdatedAt
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting escrow account for Order {OrderId}", request.OrderId);
            throw;
        }
    }
}
