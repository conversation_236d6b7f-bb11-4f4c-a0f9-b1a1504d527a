using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Domain.Enums;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;

namespace CommunicationNotification.Infrastructure.Services;

/// <summary>
/// Health check service for notification providers
/// </summary>
public class NotificationHealthCheckService : IHealthCheck
{
    private readonly INotificationProviderFactory _providerFactory;
    private readonly ILogger<NotificationHealthCheckService> _logger;

    public NotificationHealthCheckService(
        INotificationProviderFactory providerFactory,
        ILogger<NotificationHealthCheckService> logger)
    {
        _providerFactory = providerFactory;
        _logger = logger;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        var healthResults = new Dictionary<string, object>();
        var overallHealthy = true;
        var unhealthyChannels = new List<string>();

        try
        {
            // Check all notification channels
            var channels = Enum.GetValues<NotificationChannel>();
            
            foreach (var channel in channels)
            {
                try
                {
                    if (_providerFactory.IsProviderAvailable(channel))
                    {
                        var provider = _providerFactory.GetProvider(channel);
                        var isAvailable = await provider.IsAvailableAsync(cancellationToken);
                        
                        healthResults[channel.ToString()] = new
                        {
                            Status = isAvailable ? "Healthy" : "Unhealthy",
                            LastChecked = DateTime.UtcNow,
                            Provider = provider.GetType().Name
                        };

                        if (!isAvailable)
                        {
                            overallHealthy = false;
                            unhealthyChannels.Add(channel.ToString());
                        }

                        _logger.LogDebug("Health check for {Channel}: {Status}", channel, isAvailable ? "Healthy" : "Unhealthy");
                    }
                    else
                    {
                        healthResults[channel.ToString()] = new
                        {
                            Status = "NotConfigured",
                            LastChecked = DateTime.UtcNow,
                            Provider = "None"
                        };
                    }
                }
                catch (NotSupportedException)
                {
                    // Channel not supported, skip
                    healthResults[channel.ToString()] = new
                    {
                        Status = "NotSupported",
                        LastChecked = DateTime.UtcNow,
                        Provider = "None"
                    };
                }
                catch (Exception ex)
                {
                    overallHealthy = false;
                    unhealthyChannels.Add(channel.ToString());
                    
                    healthResults[channel.ToString()] = new
                    {
                        Status = "Error",
                        LastChecked = DateTime.UtcNow,
                        Error = ex.Message,
                        Provider = "Unknown"
                    };

                    _logger.LogError(ex, "Health check failed for {Channel}", channel);
                }
            }

            var result = overallHealthy ? HealthCheckResult.Healthy() : HealthCheckResult.Degraded();
            
            if (unhealthyChannels.Any())
            {
                result = HealthCheckResult.Degraded($"Unhealthy channels: {string.Join(", ", unhealthyChannels)}");
            }

            return result.WithData(healthResults);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Health check failed with exception");
            return HealthCheckResult.Unhealthy("Health check failed with exception", ex);
        }
    }
}

/// <summary>
/// Individual provider health check
/// </summary>
public class ProviderHealthCheck : IHealthCheck
{
    private readonly INotificationProvider _provider;
    private readonly string _providerName;
    private readonly ILogger<ProviderHealthCheck> _logger;

    public ProviderHealthCheck(
        INotificationProvider provider,
        string providerName,
        ILogger<ProviderHealthCheck> logger)
    {
        _provider = provider;
        _providerName = providerName;
        _logger = logger;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var isAvailable = await _provider.IsAvailableAsync(cancellationToken);
            
            var data = new Dictionary<string, object>
            {
                ["Provider"] = _providerName,
                ["Channel"] = _provider.Channel.ToString(),
                ["LastChecked"] = DateTime.UtcNow,
                ["IsAvailable"] = isAvailable
            };

            if (isAvailable)
            {
                _logger.LogDebug("{ProviderName} health check: Healthy", _providerName);
                return HealthCheckResult.Healthy($"{_providerName} is available").WithData(data);
            }
            else
            {
                _logger.LogWarning("{ProviderName} health check: Unhealthy", _providerName);
                return HealthCheckResult.Unhealthy($"{_providerName} is not available").WithData(data);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "{ProviderName} health check failed with exception", _providerName);
            
            var data = new Dictionary<string, object>
            {
                ["Provider"] = _providerName,
                ["Channel"] = _provider.Channel.ToString(),
                ["LastChecked"] = DateTime.UtcNow,
                ["Error"] = ex.Message
            };

            return HealthCheckResult.Unhealthy($"{_providerName} health check failed", ex).WithData(data);
        }
    }
}

/// <summary>
/// Extension methods for health check registration
/// </summary>
public static class NotificationHealthCheckExtensions
{
    /// <summary>
    /// Add notification health checks
    /// </summary>
    public static IServiceCollection AddNotificationHealthChecks(this IServiceCollection services)
    {
        services.AddHealthChecks()
            .AddCheck<NotificationHealthCheckService>(
                "notification-providers",
                HealthStatus.Degraded,
                new[] { "notification", "providers" })
            .AddTypeActivatedCheck<ProviderHealthCheck>(
                "whatsapp-provider",
                HealthStatus.Degraded,
                new[] { "notification", "whatsapp" },
                args: new object[] { "WhatsApp Provider" })
            .AddTypeActivatedCheck<ProviderHealthCheck>(
                "sms-provider",
                HealthStatus.Degraded,
                new[] { "notification", "sms" },
                args: new object[] { "SMS Provider" })
            .AddTypeActivatedCheck<ProviderHealthCheck>(
                "email-provider",
                HealthStatus.Degraded,
                new[] { "notification", "email" },
                args: new object[] { "Email Provider" })
            .AddTypeActivatedCheck<ProviderHealthCheck>(
                "push-provider",
                HealthStatus.Degraded,
                new[] { "notification", "push" },
                args: new object[] { "Push Provider" })
            .AddTypeActivatedCheck<ProviderHealthCheck>(
                "voice-provider",
                HealthStatus.Degraded,
                new[] { "notification", "voice" },
                args: new object[] { "Voice Provider" });

        return services;
    }
}

/// <summary>
/// Notification system metrics
/// </summary>
public class NotificationMetrics
{
    public int TotalProviders { get; set; }
    public int HealthyProviders { get; set; }
    public int UnhealthyProviders { get; set; }
    public int NotConfiguredProviders { get; set; }
    public Dictionary<string, ProviderMetrics> ProviderMetrics { get; set; } = new();
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Individual provider metrics
/// </summary>
public class ProviderMetrics
{
    public string ProviderName { get; set; } = string.Empty;
    public NotificationChannel Channel { get; set; }
    public bool IsHealthy { get; set; }
    public bool IsConfigured { get; set; }
    public DateTime LastHealthCheck { get; set; }
    public string? LastError { get; set; }
    public TimeSpan? ResponseTime { get; set; }
}

/// <summary>
/// Service for collecting notification metrics
/// </summary>
public interface INotificationMetricsService
{
    /// <summary>
    /// Get current notification system metrics
    /// </summary>
    Task<NotificationMetrics> GetMetricsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get metrics for a specific provider
    /// </summary>
    Task<ProviderMetrics?> GetProviderMetricsAsync(NotificationChannel channel, CancellationToken cancellationToken = default);
}

/// <summary>
/// Implementation of notification metrics service
/// </summary>
public class NotificationMetricsService : INotificationMetricsService
{
    private readonly INotificationProviderFactory _providerFactory;
    private readonly ILogger<NotificationMetricsService> _logger;

    public NotificationMetricsService(
        INotificationProviderFactory providerFactory,
        ILogger<NotificationMetricsService> logger)
    {
        _providerFactory = providerFactory;
        _logger = logger;
    }

    public async Task<NotificationMetrics> GetMetricsAsync(CancellationToken cancellationToken = default)
    {
        var metrics = new NotificationMetrics();
        var channels = Enum.GetValues<NotificationChannel>();

        foreach (var channel in channels)
        {
            var providerMetrics = await GetProviderMetricsAsync(channel, cancellationToken);
            if (providerMetrics != null)
            {
                metrics.ProviderMetrics[channel.ToString()] = providerMetrics;
                metrics.TotalProviders++;

                if (providerMetrics.IsConfigured)
                {
                    if (providerMetrics.IsHealthy)
                    {
                        metrics.HealthyProviders++;
                    }
                    else
                    {
                        metrics.UnhealthyProviders++;
                    }
                }
                else
                {
                    metrics.NotConfiguredProviders++;
                }
            }
        }

        return metrics;
    }

    public async Task<ProviderMetrics?> GetProviderMetricsAsync(NotificationChannel channel, CancellationToken cancellationToken = default)
    {
        try
        {
            var startTime = DateTime.UtcNow;
            
            if (!_providerFactory.IsProviderAvailable(channel))
            {
                return new ProviderMetrics
                {
                    ProviderName = "Not Configured",
                    Channel = channel,
                    IsHealthy = false,
                    IsConfigured = false,
                    LastHealthCheck = DateTime.UtcNow
                };
            }

            var provider = _providerFactory.GetProvider(channel);
            var isHealthy = await provider.IsAvailableAsync(cancellationToken);
            var responseTime = DateTime.UtcNow - startTime;

            return new ProviderMetrics
            {
                ProviderName = provider.GetType().Name,
                Channel = channel,
                IsHealthy = isHealthy,
                IsConfigured = true,
                LastHealthCheck = DateTime.UtcNow,
                ResponseTime = responseTime
            };
        }
        catch (NotSupportedException)
        {
            return null; // Channel not supported
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get metrics for {Channel}", channel);
            
            return new ProviderMetrics
            {
                ProviderName = "Error",
                Channel = channel,
                IsHealthy = false,
                IsConfigured = true,
                LastHealthCheck = DateTime.UtcNow,
                LastError = ex.Message
            };
        }
    }
}
