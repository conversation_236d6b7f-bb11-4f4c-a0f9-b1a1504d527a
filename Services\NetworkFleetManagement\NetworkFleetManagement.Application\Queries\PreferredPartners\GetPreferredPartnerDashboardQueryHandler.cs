using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Repositories;

namespace NetworkFleetManagement.Application.Queries.PreferredPartners;

public class GetPreferredPartnerDashboardQueryHandler : IRequestHandler<GetPreferredPartnerDashboardQuery, PreferredPartnerDashboardDto>
{
    private readonly IPreferredPartnerRepository _preferredPartnerRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetPreferredPartnerDashboardQueryHandler> _logger;

    public GetPreferredPartnerDashboardQueryHandler(
        IPreferredPartnerRepository preferredPartnerRepository,
        IMapper mapper,
        ILogger<GetPreferredPartnerDashboardQueryHandler> logger)
    {
        _preferredPartnerRepository = preferredPartnerRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<PreferredPartnerDashboardDto> Handle(GetPreferredPartnerDashboardQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var dashboard = new PreferredPartnerDashboardDto
            {
                UserId = request.UserId,
                UserRole = "User", // This would be populated from user context
                GeneratedAt = DateTime.UtcNow
            };

            // Get summary statistics
            dashboard.TotalPreferredPartners = await _preferredPartnerRepository.GetTotalPreferredPartnersCountAsync(request.UserId, cancellationToken);
            dashboard.ActivePreferredPartners = await _preferredPartnerRepository.GetActivePreferredPartnersCountAsync(request.UserId, cancellationToken);
            dashboard.InactivePreferredPartners = dashboard.TotalPreferredPartners - dashboard.ActivePreferredPartners;
            dashboard.AveragePartnerRating = await _preferredPartnerRepository.GetAveragePartnerRatingAsync(request.UserId, cancellationToken);

            // Get partner type breakdown
            var partnerTypes = Enum.GetValues<NetworkFleetManagement.Domain.Entities.PreferredPartnerType>();
            foreach (var partnerType in partnerTypes)
            {
                var count = await _preferredPartnerRepository.GetPreferredPartnersCountByTypeAsync(request.UserId, partnerType, cancellationToken);
                if (count > 0)
                {
                    dashboard.PartnerTypeStats.Add(new PartnerTypeStatsDto
                    {
                        PartnerType = partnerType.ToString(),
                        TotalCount = count,
                        ActiveCount = count, // Simplified for now
                        AverageRating = dashboard.AveragePartnerRating,
                        TotalCollaborations = 0, // Would be calculated from collaboration data
                        TotalValue = 0 // Would be calculated from collaboration data
                    });
                }
            }

            // Get recent activities (simplified)
            var recentPartners = await _preferredPartnerRepository.GetRecentlyActivePartnersAsync(
                request.UserId, 
                DateTime.UtcNow.AddDays(-30), 
                cancellationToken);

            dashboard.RecentActivities = recentPartners.Take(10).Select(p => new RecentPartnerActivityDto
            {
                PartnerId = p.PartnerId,
                PartnerName = "Partner", // Would be populated from partner service
                ActivityType = "Collaboration",
                ActivityDate = p.LastCollaborationDate ?? p.CreatedAt,
                Description = $"Recent activity with {p.PartnerType} partner"
            }).ToList();

            // Add performance insights (simplified)
            dashboard.PerformanceInsights.Add(new PerformanceInsightDto
            {
                InsightType = "Trend",
                Title = "Partner Performance",
                Description = $"You have {dashboard.ActivePreferredPartners} active preferred partners with an average rating of {dashboard.AveragePartnerRating:F1}",
                Severity = "Info",
                GeneratedAt = DateTime.UtcNow
            });

            return dashboard;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating preferred partner dashboard for user {UserId}", request.UserId);
            throw;
        }
    }
}
