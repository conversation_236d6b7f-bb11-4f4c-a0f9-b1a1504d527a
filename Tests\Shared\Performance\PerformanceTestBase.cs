using System.Diagnostics;
using FluentAssertions;
using Xunit;
using Xunit.Abstractions;

namespace Tests.Shared.Performance;

/// <summary>
/// Base class for performance tests
/// </summary>
public abstract class PerformanceTestBase
{
    protected readonly ITestOutputHelper Output;

    protected PerformanceTestBase(ITestOutputHelper output)
    {
        Output = output;
    }

    /// <summary>
    /// Measure execution time of an operation
    /// </summary>
    protected async Task<PerformanceResult> MeasureAsync(Func<Task> operation, string operationName = "Operation")
    {
        var stopwatch = Stopwatch.StartNew();
        var memoryBefore = GC.GetTotalMemory(false);

        await operation();

        stopwatch.Stop();
        var memoryAfter = GC.GetTotalMemory(false);

        var result = new PerformanceResult
        {
            OperationName = operationName,
            ElapsedTime = stopwatch.Elapsed,
            MemoryUsed = memoryAfter - memoryBefore
        };

        Output.WriteLine($"Performance: {operationName} - {result.ElapsedTime.TotalMilliseconds:F2}ms, Memory: {result.MemoryUsed} bytes");

        return result;
    }

    /// <summary>
    /// Measure execution time of a synchronous operation
    /// </summary>
    protected PerformanceResult Measure(Action operation, string operationName = "Operation")
    {
        var stopwatch = Stopwatch.StartNew();
        var memoryBefore = GC.GetTotalMemory(false);

        operation();

        stopwatch.Stop();
        var memoryAfter = GC.GetTotalMemory(false);

        var result = new PerformanceResult
        {
            OperationName = operationName,
            ElapsedTime = stopwatch.Elapsed,
            MemoryUsed = memoryAfter - memoryBefore
        };

        Output.WriteLine($"Performance: {operationName} - {result.ElapsedTime.TotalMilliseconds:F2}ms, Memory: {result.MemoryUsed} bytes");

        return result;
    }

    /// <summary>
    /// Run a load test with multiple concurrent operations
    /// </summary>
    protected async Task<LoadTestResult> RunLoadTestAsync(
        Func<Task> operation,
        int concurrentUsers,
        TimeSpan duration,
        string testName = "Load Test")
    {
        var results = new List<PerformanceResult>();
        var errors = new List<Exception>();
        var cancellationTokenSource = new CancellationTokenSource(duration);

        Output.WriteLine($"Starting load test: {testName} with {concurrentUsers} concurrent users for {duration}");

        var tasks = Enumerable.Range(0, concurrentUsers)
            .Select(async userId =>
            {
                var userResults = new List<PerformanceResult>();
                var operationCount = 0;

                while (!cancellationTokenSource.Token.IsCancellationRequested)
                {
                    try
                    {
                        var result = await MeasureAsync(operation, $"User{userId}-Op{operationCount}");
                        userResults.Add(result);
                        operationCount++;
                    }
                    catch (Exception ex)
                    {
                        errors.Add(ex);
                    }
                }

                lock (results)
                {
                    results.AddRange(userResults);
                }
            });

        await Task.WhenAll(tasks);

        var loadTestResult = new LoadTestResult
        {
            TestName = testName,
            ConcurrentUsers = concurrentUsers,
            Duration = duration,
            TotalOperations = results.Count,
            TotalErrors = errors.Count,
            AverageResponseTime = results.Any() ? TimeSpan.FromMilliseconds(results.Average(r => r.ElapsedTime.TotalMilliseconds)) : TimeSpan.Zero,
            MinResponseTime = results.Any() ? results.Min(r => r.ElapsedTime) : TimeSpan.Zero,
            MaxResponseTime = results.Any() ? results.Max(r => r.ElapsedTime) : TimeSpan.Zero,
            OperationsPerSecond = results.Count / duration.TotalSeconds,
            Errors = errors
        };

        Output.WriteLine($"Load test completed: {loadTestResult}");

        return loadTestResult;
    }

    /// <summary>
    /// Assert that operation completes within expected time
    /// </summary>
    protected void ShouldCompleteWithin(PerformanceResult result, TimeSpan expectedTime)
    {
        result.ElapsedTime.Should().BeLessOrEqualTo(expectedTime,
            $"Operation '{result.OperationName}' should complete within {expectedTime.TotalMilliseconds}ms but took {result.ElapsedTime.TotalMilliseconds}ms");
    }

    /// <summary>
    /// Assert that load test meets performance criteria
    /// </summary>
    protected void ShouldMeetPerformanceCriteria(LoadTestResult result, double minOperationsPerSecond, TimeSpan maxAverageResponseTime, double maxErrorRate = 0.01)
    {
        result.OperationsPerSecond.Should().BeGreaterOrEqualTo(minOperationsPerSecond,
            $"Should achieve at least {minOperationsPerSecond} operations per second");

        result.AverageResponseTime.Should().BeLessOrEqualTo(maxAverageResponseTime,
            $"Average response time should be less than {maxAverageResponseTime.TotalMilliseconds}ms");

        var errorRate = (double)result.TotalErrors / result.TotalOperations;
        errorRate.Should().BeLessOrEqualTo(maxErrorRate,
            $"Error rate should be less than {maxErrorRate:P}");
    }
}

/// <summary>
/// Result of a performance measurement
/// </summary>
public class PerformanceResult
{
    public string OperationName { get; set; } = string.Empty;
    public TimeSpan ElapsedTime { get; set; }
    public long MemoryUsed { get; set; }
}

/// <summary>
/// Result of a load test
/// </summary>
public class LoadTestResult
{
    public string TestName { get; set; } = string.Empty;
    public int ConcurrentUsers { get; set; }
    public TimeSpan Duration { get; set; }
    public int TotalOperations { get; set; }
    public int TotalErrors { get; set; }
    public TimeSpan AverageResponseTime { get; set; }
    public TimeSpan MinResponseTime { get; set; }
    public TimeSpan MaxResponseTime { get; set; }
    public double OperationsPerSecond { get; set; }
    public List<Exception> Errors { get; set; } = new();

    public override string ToString()
    {
        return $"Load Test: {TestName} - {TotalOperations} ops in {Duration}, " +
               $"{OperationsPerSecond:F2} ops/sec, Avg: {AverageResponseTime.TotalMilliseconds:F2}ms, " +
               $"Errors: {TotalErrors}";
    }
}
