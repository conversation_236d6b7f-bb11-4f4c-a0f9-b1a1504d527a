using OrderManagement.Domain.Enums;

namespace OrderManagement.Domain.ValueObjects;

public record RfqRequirements
{
    public VehicleType RequiredVehicleType { get; init; }
    public int? MinVehicleCapacity { get; init; }
    public bool RequiresInsurance { get; init; }
    public decimal? MinInsuranceAmount { get; init; }
    public bool RequiresLicense { get; init; }
    public List<string> RequiredLicenses { get; init; } = new();
    public bool RequiresExperience { get; init; }
    public int? MinYearsExperience { get; init; }
    public bool RequiresBackground { get; init; }
    public bool RequiresTracking { get; init; }
    public bool RequiresTemperatureControl { get; init; }
    public TemperatureRange? TemperatureRange { get; init; }
    public List<string> AdditionalRequirements { get; init; } = new();

    // Parameterless constructor for EF Core
    public RfqRequirements() { }

    public RfqRequirements(
        VehicleType requiredVehicleType,
        int? minVehicleCapacity = null,
        bool requiresInsurance = true,
        decimal? minInsuranceAmount = null,
        bool requiresLicense = false,
        List<string>? requiredLicenses = null,
        bool requiresExperience = false,
        int? minYearsExperience = null,
        bool requiresBackground = false,
        bool requiresTracking = true,
        bool requiresTemperatureControl = false,
        TemperatureRange? temperatureRange = null,
        List<string>? additionalRequirements = null)
    {
        if (requiresInsurance && minInsuranceAmount.HasValue && minInsuranceAmount <= 0)
            throw new ArgumentException("Insurance amount must be positive", nameof(minInsuranceAmount));

        if (requiresExperience && minYearsExperience.HasValue && minYearsExperience <= 0)
            throw new ArgumentException("Years of experience must be positive", nameof(minYearsExperience));

        if (requiresTemperatureControl && temperatureRange == null)
            throw new ArgumentException("Temperature range is required when temperature control is required", nameof(temperatureRange));

        RequiredVehicleType = requiredVehicleType;
        MinVehicleCapacity = minVehicleCapacity;
        RequiresInsurance = requiresInsurance;
        MinInsuranceAmount = minInsuranceAmount;
        RequiresLicense = requiresLicense;
        RequiredLicenses = requiredLicenses ?? new List<string>();
        RequiresExperience = requiresExperience;
        MinYearsExperience = minYearsExperience;
        RequiresBackground = requiresBackground;
        RequiresTracking = requiresTracking;
        RequiresTemperatureControl = requiresTemperatureControl;
        TemperatureRange = temperatureRange;
        AdditionalRequirements = additionalRequirements ?? new List<string>();
    }
}
