using CommunicationNotification.Application.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Text;
using System.Text.Json;

namespace CommunicationNotification.Infrastructure.ExternalServices;

/// <summary>
/// WhatsApp Business API provider implementation
/// </summary>
public class WhatsAppBusinessProvider : IWhatsAppProvider
{
    private readonly ILogger<WhatsAppBusinessProvider> _logger;
    private readonly WhatsAppBusinessSettings _settings;
    private readonly HttpClient _httpClient;

    public WhatsAppBusinessProvider(
        IConfiguration configuration,
        ILogger<WhatsAppBusinessProvider> logger,
        HttpClient httpClient)
    {
        _logger = logger;
        _httpClient = httpClient;
        _settings = configuration.GetSection("WhatsAppBusiness").Get<WhatsAppBusinessSettings>() ?? new();

        // Configure HTTP client
        _httpClient.BaseAddress = new Uri(_settings.ApiBaseUrl);
        _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_settings.AccessToken}");
        _httpClient.Timeout = TimeSpan.FromSeconds(_settings.TimeoutSeconds);
    }

    public async Task<WhatsAppResult> SendTextMessageAsync(
        string phoneNumber,
        string message,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Sending WhatsApp text message to {PhoneNumber}", phoneNumber);

            // Validate request
            var validationResult = ValidateTextMessage(phoneNumber, message);
            if (!validationResult.IsValid)
            {
                return WhatsAppResult.Failure(string.Join(", ", validationResult.Errors));
            }

            // Create message payload
            var payload = new
            {
                messaging_product = "whatsapp",
                to = FormatPhoneNumber(phoneNumber),
                type = "text",
                text = new { body = message }
            };

            // Send message
            var response = await SendMessageAsync(payload, cancellationToken);

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error sending WhatsApp text message to {PhoneNumber}", phoneNumber);
            return WhatsAppResult.Failure($"Unexpected error: {ex.Message}");
        }
    }

    public async Task<WhatsAppResult> SendTemplateMessageAsync(
        WhatsAppTemplateRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Sending WhatsApp template message {TemplateName} to {PhoneNumber}",
                request.TemplateName, request.PhoneNumber);

            // Validate request
            var validationResult = ValidateTemplateMessage(request);
            if (!validationResult.IsValid)
            {
                return WhatsAppResult.Failure(string.Join(", ", validationResult.Errors));
            }

            // Create template payload
            var payload = new
            {
                messaging_product = "whatsapp",
                to = FormatPhoneNumber(request.PhoneNumber),
                type = "template",
                template = new
                {
                    name = request.TemplateName,
                    language = new { code = GetLanguageCode(request.Language) },
                    components = CreateTemplateComponents(request)
                }
            };

            // Send message
            var response = await SendMessageAsync(payload, cancellationToken);

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error sending WhatsApp template message to {PhoneNumber}", request.PhoneNumber);
            return WhatsAppResult.Failure($"Unexpected error: {ex.Message}");
        }
    }

    public async Task<WhatsAppResult> SendMediaMessageAsync(
        WhatsAppMediaRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Sending WhatsApp media message ({MediaType}) to {PhoneNumber}",
                request.MediaType, request.PhoneNumber);

            // Validate request
            var validationResult = ValidateMediaMessage(request);
            if (!validationResult.IsValid)
            {
                return WhatsAppResult.Failure(string.Join(", ", validationResult.Errors));
            }

            // Create media payload
            var payload = CreateMediaPayload(request);

            // Send message
            var response = await SendMessageAsync(payload, cancellationToken);

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error sending WhatsApp media message to {PhoneNumber}", request.PhoneNumber);
            return WhatsAppResult.Failure($"Unexpected error: {ex.Message}");
        }
    }

    public async Task<WhatsAppResult> GetMessageStatusAsync(string messageId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting WhatsApp message status for {MessageId}", messageId);

            var response = await _httpClient.GetAsync($"/{_settings.PhoneNumberId}/messages/{messageId}", cancellationToken);
            var content = await response.Content.ReadAsStringAsync(cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var statusResponse = JsonSerializer.Deserialize<WhatsAppStatusResponse>(content);
                
                return new WhatsAppResult
                {
                    IsSuccess = true,
                    MessageId = messageId,
                    ExternalId = messageId,
                    Status = MapWhatsAppStatus(statusResponse?.Status),
                    Metadata = new Dictionary<string, object>
                    {
                        ["whatsappStatus"] = statusResponse?.Status ?? "unknown",
                        ["timestamp"] = statusResponse?.Timestamp ?? DateTime.UtcNow.ToString("O")
                    }
                };
            }
            else
            {
                var errorResponse = JsonSerializer.Deserialize<WhatsAppErrorResponse>(content);
                return WhatsAppResult.Failure($"WhatsApp API error: {errorResponse?.Error?.Message ?? "Unknown error"}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting WhatsApp message status for {MessageId}", messageId);
            return WhatsAppResult.Failure($"Error getting message status: {ex.Message}");
        }
    }

    public async Task<bool> ValidatePhoneNumberAsync(string phoneNumber, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
                return false;

            // Basic phone number validation for WhatsApp
            var cleaned = phoneNumber.Replace(" ", "").Replace("-", "").Replace("(", "").Replace(")", "");
            
            // WhatsApp requires numbers without + prefix for API calls
            if (cleaned.StartsWith("+"))
                cleaned = cleaned.Substring(1);

            return cleaned.Length >= 10 && cleaned.All(char.IsDigit);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Phone number validation failed for {PhoneNumber}", phoneNumber);
            return false;
        }
    }

    private async Task<WhatsAppResult> SendMessageAsync(object payload, CancellationToken cancellationToken)
    {
        try
        {
            var json = JsonSerializer.Serialize(payload, new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase });
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync($"/{_settings.PhoneNumberId}/messages", content, cancellationToken);
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var successResponse = JsonSerializer.Deserialize<WhatsAppSuccessResponse>(responseContent);
                
                return new WhatsAppResult
                {
                    IsSuccess = true,
                    MessageId = successResponse?.Messages?.FirstOrDefault()?.Id ?? Guid.NewGuid().ToString(),
                    ExternalId = successResponse?.Messages?.FirstOrDefault()?.Id,
                    Status = WhatsAppStatus.Sent,
                    SentAt = DateTime.UtcNow,
                    Metadata = new Dictionary<string, object>
                    {
                        ["whatsappMessageId"] = successResponse?.Messages?.FirstOrDefault()?.Id ?? "",
                        ["whatsappStatus"] = "sent",
                        ["apiResponse"] = responseContent
                    }
                };
            }
            else
            {
                var errorResponse = JsonSerializer.Deserialize<WhatsAppErrorResponse>(responseContent);
                var errorMessage = errorResponse?.Error?.Message ?? $"HTTP {response.StatusCode}";
                
                _logger.LogError("WhatsApp API error: {StatusCode} - {ErrorMessage}", response.StatusCode, errorMessage);
                
                return WhatsAppResult.Failure($"WhatsApp API error: {errorMessage}", 
                    errorResponse?.Error?.Code?.ToString());
            }
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "HTTP error sending WhatsApp message");
            return WhatsAppResult.Failure($"HTTP error: {ex.Message}");
        }
        catch (TaskCanceledException ex)
        {
            _logger.LogError(ex, "Timeout sending WhatsApp message");
            return WhatsAppResult.Failure("Request timeout");
        }
    }

    private object CreateMediaPayload(WhatsAppMediaRequest request)
    {
        var mediaObject = new Dictionary<string, object>();

        switch (request.MediaType.ToLower())
        {
            case "image":
                mediaObject = new Dictionary<string, object>
                {
                    ["link"] = request.MediaUrl,
                    ["caption"] = request.Caption ?? ""
                };
                break;
            case "document":
                mediaObject = new Dictionary<string, object>
                {
                    ["link"] = request.MediaUrl,
                    ["caption"] = request.Caption ?? "",
                    ["filename"] = request.FileName ?? "document"
                };
                break;
            case "audio":
                mediaObject = new Dictionary<string, object>
                {
                    ["link"] = request.MediaUrl
                };
                break;
            case "video":
                mediaObject = new Dictionary<string, object>
                {
                    ["link"] = request.MediaUrl,
                    ["caption"] = request.Caption ?? ""
                };
                break;
        }

        return new
        {
            messaging_product = "whatsapp",
            to = FormatPhoneNumber(request.PhoneNumber),
            type = request.MediaType.ToLower(),
            [request.MediaType.ToLower()] = mediaObject
        };
    }

    private List<object> CreateTemplateComponents(WhatsAppTemplateRequest request)
    {
        var components = new List<object>();

        // Add header component if provided
        if (request.HeaderParameters?.Any() == true)
        {
            components.Add(new
            {
                type = "header",
                parameters = request.HeaderParameters.Select(p => new { type = "text", text = p }).ToArray()
            });
        }

        // Add body component if provided
        if (request.BodyParameters?.Any() == true)
        {
            components.Add(new
            {
                type = "body",
                parameters = request.BodyParameters.Select(p => new { type = "text", text = p }).ToArray()
            });
        }

        // Add button component if provided
        if (request.ButtonParameters?.Any() == true)
        {
            components.Add(new
            {
                type = "button",
                sub_type = "quick_reply",
                index = "0",
                parameters = request.ButtonParameters.Select(p => new { type = "payload", payload = p }).ToArray()
            });
        }

        return components;
    }

    private string FormatPhoneNumber(string phoneNumber)
    {
        // WhatsApp API expects phone numbers without + prefix
        var cleaned = phoneNumber.Replace(" ", "").Replace("-", "").Replace("(", "").Replace(")", "");
        
        if (cleaned.StartsWith("+"))
            cleaned = cleaned.Substring(1);

        return cleaned;
    }

    private string GetLanguageCode(Language language)
    {
        return language switch
        {
            Language.Hindi => "hi",
            Language.Kannada => "kn",
            _ => "en"
        };
    }

    private WhatsAppStatus MapWhatsAppStatus(string? status)
    {
        return status?.ToLower() switch
        {
            "sent" => WhatsAppStatus.Sent,
            "delivered" => WhatsAppStatus.Delivered,
            "read" => WhatsAppStatus.Read,
            "failed" => WhatsAppStatus.Failed,
            _ => WhatsAppStatus.Unknown
        };
    }

    private WhatsAppValidationResult ValidateTextMessage(string phoneNumber, string message)
    {
        var errors = new List<string>();

        if (string.IsNullOrWhiteSpace(phoneNumber))
            errors.Add("Phone number is required");

        if (string.IsNullOrWhiteSpace(message))
            errors.Add("Message content is required");

        if (message?.Length > _settings.MaxMessageLength)
            errors.Add($"Message exceeds maximum length of {_settings.MaxMessageLength} characters");

        if (!IsValidPhoneNumberFormat(phoneNumber))
            errors.Add("Invalid phone number format");

        return new WhatsAppValidationResult
        {
            IsValid = !errors.Any(),
            Errors = errors
        };
    }

    private WhatsAppValidationResult ValidateTemplateMessage(WhatsAppTemplateRequest request)
    {
        var errors = new List<string>();

        if (string.IsNullOrWhiteSpace(request.PhoneNumber))
            errors.Add("Phone number is required");

        if (string.IsNullOrWhiteSpace(request.TemplateName))
            errors.Add("Template name is required");

        if (!IsValidPhoneNumberFormat(request.PhoneNumber))
            errors.Add("Invalid phone number format");

        return new WhatsAppValidationResult
        {
            IsValid = !errors.Any(),
            Errors = errors
        };
    }

    private WhatsAppValidationResult ValidateMediaMessage(WhatsAppMediaRequest request)
    {
        var errors = new List<string>();

        if (string.IsNullOrWhiteSpace(request.PhoneNumber))
            errors.Add("Phone number is required");

        if (string.IsNullOrWhiteSpace(request.MediaUrl))
            errors.Add("Media URL is required");

        if (string.IsNullOrWhiteSpace(request.MediaType))
            errors.Add("Media type is required");

        var validMediaTypes = new[] { "image", "document", "audio", "video" };
        if (!validMediaTypes.Contains(request.MediaType.ToLower()))
            errors.Add($"Invalid media type. Supported types: {string.Join(", ", validMediaTypes)}");

        if (!IsValidPhoneNumberFormat(request.PhoneNumber))
            errors.Add("Invalid phone number format");

        if (!Uri.TryCreate(request.MediaUrl, UriKind.Absolute, out _))
            errors.Add("Invalid media URL format");

        return new WhatsAppValidationResult
        {
            IsValid = !errors.Any(),
            Errors = errors
        };
    }

    private bool IsValidPhoneNumberFormat(string phoneNumber)
    {
        if (string.IsNullOrWhiteSpace(phoneNumber))
            return false;

        var cleaned = phoneNumber.Replace(" ", "").Replace("-", "").Replace("(", "").Replace(")", "");
        if (cleaned.StartsWith("+"))
            cleaned = cleaned.Substring(1);

        return cleaned.Length >= 10 && cleaned.All(char.IsDigit);
    }
}

/// <summary>
/// WhatsApp Business API configuration settings
/// </summary>
public class WhatsAppBusinessSettings
{
    public string AccessToken { get; set; } = string.Empty;
    public string PhoneNumberId { get; set; } = string.Empty;
    public string ApiBaseUrl { get; set; } = "https://graph.facebook.com/v18.0";
    public int MaxMessageLength { get; set; } = 4096;
    public int TimeoutSeconds { get; set; } = 30;
    public int RetryAttempts { get; set; } = 3;
    public string WebhookVerifyToken { get; set; } = string.Empty;
    public string WebhookUrl { get; set; } = string.Empty;
}

/// <summary>
/// WhatsApp validation result
/// </summary>
public class WhatsAppValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
}

/// <summary>
/// WhatsApp API response models
/// </summary>
public class WhatsAppSuccessResponse
{
    public List<WhatsAppMessage>? Messages { get; set; }
}

public class WhatsAppMessage
{
    public string? Id { get; set; }
}

public class WhatsAppErrorResponse
{
    public WhatsAppError? Error { get; set; }
}

public class WhatsAppError
{
    public string? Message { get; set; }
    public int? Code { get; set; }
    public string? Type { get; set; }
}

public class WhatsAppStatusResponse
{
    public string? Status { get; set; }
    public string? Timestamp { get; set; }
}
