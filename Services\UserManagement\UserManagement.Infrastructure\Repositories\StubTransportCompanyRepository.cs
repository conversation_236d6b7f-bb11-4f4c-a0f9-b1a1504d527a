using UserManagement.Application.Interfaces;
using UserManagement.Domain.Entities;

namespace UserManagement.Infrastructure.Repositories
{
    public class StubTransportCompanyRepository : ITransportCompanyRepository
    {
        public Task<UserProfile?> GetByIdAsync(Guid id)
        {
            // Stub implementation - return null for now
            return Task.FromResult<UserProfile?>(null);
        }

        public Task<UserProfile> UpdateAsync(UserProfile transportCompany)
        {
            // Stub implementation
            return Task.FromResult(transportCompany);
        }

        public Task SaveChangesAsync()
        {
            // Stub implementation
            return Task.CompletedTask;
        }
    }
}
