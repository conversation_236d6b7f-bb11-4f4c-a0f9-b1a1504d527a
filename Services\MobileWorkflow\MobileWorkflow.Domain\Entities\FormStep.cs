using Shared.Domain.Common;
using MobileWorkflow.Domain.Events;

namespace MobileWorkflow.Domain.Entities;

public class FormStep : AggregateRoot
{
    public Guid FormDefinitionId { get; private set; }
    public string Name { get; private set; }
    public string DisplayName { get; private set; }
    public string Description { get; private set; }
    public int Order { get; private set; }
    public bool IsActive { get; private set; }
    public bool IsRequired { get; private set; }
    public Dictionary<string, object> Configuration { get; private set; }
    public Dictionary<string, object> ValidationRules { get; private set; }
    public Dictionary<string, object> ConditionalLogic { get; private set; }
    public Dictionary<string, object> Styling { get; private set; }
    public string? NextStepCondition { get; private set; }
    public string? PreviousStepCondition { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    public virtual FormDefinition FormDefinition { get; private set; } = null!;
    public virtual ICollection<FormField> Fields { get; private set; } = new List<FormField>();

    private FormStep() { } // EF Core

    public FormStep(
        Guid formDefinitionId,
        string name,
        string displayName,
        string description,
        int order)
    {
        FormDefinitionId = formDefinitionId;
        Name = name ?? throw new ArgumentNullException(nameof(name));
        DisplayName = displayName ?? throw new ArgumentNullException(nameof(displayName));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        Order = order;

        IsActive = true;
        IsRequired = true;
        Configuration = new Dictionary<string, object>();
        ValidationRules = new Dictionary<string, object>();
        ConditionalLogic = new Dictionary<string, object>();
        Styling = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();

        SetDefaultConfiguration();

        AddDomainEvent(new FormStepCreatedEvent(Id, FormDefinitionId, Name, Order));
    }

    public void UpdateConfiguration(Dictionary<string, object> configuration)
    {
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));

        AddDomainEvent(new FormStepConfigurationUpdatedEvent(Id, FormDefinitionId, Name));
    }

    public void UpdateValidationRules(Dictionary<string, object> validationRules)
    {
        ValidationRules = validationRules ?? throw new ArgumentNullException(nameof(validationRules));

        AddDomainEvent(new FormStepValidationRulesUpdatedEvent(Id, FormDefinitionId, Name));
    }

    public void UpdateConditionalLogic(Dictionary<string, object> conditionalLogic)
    {
        ConditionalLogic = conditionalLogic ?? throw new ArgumentNullException(nameof(conditionalLogic));

        AddDomainEvent(new FormStepConditionalLogicUpdatedEvent(Id, FormDefinitionId, Name));
    }

    public void UpdateStyling(Dictionary<string, object> styling)
    {
        Styling = styling ?? throw new ArgumentNullException(nameof(styling));

        AddDomainEvent(new FormStepStylingUpdatedEvent(Id, FormDefinitionId, Name));
    }

    public void SetNextStepCondition(string? condition)
    {
        NextStepCondition = condition;

        AddDomainEvent(new FormStepNextStepConditionSetEvent(Id, FormDefinitionId, Name));
    }

    public void SetPreviousStepCondition(string? condition)
    {
        PreviousStepCondition = condition;

        AddDomainEvent(new FormStepPreviousStepConditionSetEvent(Id, FormDefinitionId, Name));
    }

    public void SetAsRequired()
    {
        IsRequired = true;

        AddDomainEvent(new FormStepSetAsRequiredEvent(Id, FormDefinitionId, Name));
    }

    public void SetAsOptional()
    {
        IsRequired = false;

        AddDomainEvent(new FormStepSetAsOptionalEvent(Id, FormDefinitionId, Name));
    }

    public void Activate()
    {
        IsActive = true;

        AddDomainEvent(new FormStepActivatedEvent(Id, FormDefinitionId, Name));
    }

    public void Deactivate()
    {
        IsActive = false;

        AddDomainEvent(new FormStepDeactivatedEvent(Id, FormDefinitionId, Name));
    }

    public void UpdateOrder(int newOrder)
    {
        var oldOrder = Order;
        Order = newOrder;

        AddDomainEvent(new FormStepOrderUpdatedEvent(Id, FormDefinitionId, Name, oldOrder, newOrder));
    }

    public bool CanNavigateToNext(Dictionary<string, object> context)
    {
        if (string.IsNullOrEmpty(NextStepCondition))
        {
            return true;
        }

        return EvaluateCondition(NextStepCondition, context);
    }

    public bool CanNavigateToPrevious(Dictionary<string, object> context)
    {
        if (string.IsNullOrEmpty(PreviousStepCondition))
        {
            return true;
        }

        return EvaluateCondition(PreviousStepCondition, context);
    }

    public bool IsVisible(Dictionary<string, object> context)
    {
        if (ConditionalLogic.TryGetValue("visibility", out var visibilityConditions) &&
            visibilityConditions is Dictionary<string, object> conditions)
        {
            return EvaluateConditions(conditions, context);
        }

        return true;
    }

    public List<FormField> GetVisibleFields(Dictionary<string, object> context)
    {
        var visibleFields = new List<FormField>();

        foreach (var field in Fields.Where(f => f.IsActive).OrderBy(f => f.Order))
        {
            if (field.EvaluateVisibilityConditions(context))
            {
                visibleFields.Add(field);
            }
        }

        return visibleFields;
    }

    public Dictionary<string, object> ValidateStep(Dictionary<string, object> submissionData)
    {
        var validationResults = new Dictionary<string, object>
        {
            ["isValid"] = true,
            ["errors"] = new List<string>(),
            ["fieldErrors"] = new Dictionary<string, List<string>>()
        };

        var errors = (List<string>)validationResults["errors"];
        var fieldErrors = (Dictionary<string, List<string>>)validationResults["fieldErrors"];

        // Validate each field in this step
        foreach (var field in Fields.Where(f => f.IsActive))
        {
            var fieldValidationErrors = field.ValidateValue(submissionData);
            if (fieldValidationErrors.Count > 0)
            {
                fieldErrors[field.Name] = fieldValidationErrors;
                errors.AddRange(fieldValidationErrors.Select(e => $"{field.DisplayName}: {e}"));
            }
        }

        // Apply step-level validation rules
        var stepValidationErrors = ApplyStepValidationRules(submissionData);
        errors.AddRange(stepValidationErrors);

        validationResults["isValid"] = errors.Count == 0;
        return validationResults;
    }

    public bool IsComplete(Dictionary<string, object> submissionData)
    {
        if (!IsRequired)
        {
            return true;
        }

        // Check if all required fields in this step are completed
        var requiredFields = Fields.Where(f => f.IsActive && f.EvaluateRequiredConditions(submissionData));

        foreach (var field in requiredFields)
        {
            if (!submissionData.ContainsKey(field.Name) || IsValueEmpty(submissionData[field.Name]))
            {
                return false;
            }
        }

        // Check step-level completion conditions
        if (ConditionalLogic.TryGetValue("completion", out var completionConditions) &&
            completionConditions is Dictionary<string, object> conditions)
        {
            return EvaluateConditions(conditions, submissionData);
        }

        return true;
    }

    public double GetCompletionPercentage(Dictionary<string, object> submissionData)
    {
        var totalFields = Fields.Count(f => f.IsActive);
        if (totalFields == 0)
        {
            return 100.0;
        }

        var completedFields = 0;
        foreach (var field in Fields.Where(f => f.IsActive))
        {
            if (submissionData.ContainsKey(field.Name) && !IsValueEmpty(submissionData[field.Name]))
            {
                completedFields++;
            }
        }

        return (double)completedFields / totalFields * 100;
    }

    private bool EvaluateCondition(string condition, Dictionary<string, object> context)
    {
        // Simple condition evaluation - can be enhanced with expression engine
        // For now, just check if the condition string evaluates to true
        return condition.ToLowerInvariant().Contains("true");
    }

    private bool EvaluateConditions(Dictionary<string, object> conditions, Dictionary<string, object> context)
    {
        if (conditions.TryGetValue("operator", out var op) && op is string operatorStr)
        {
            if (conditions.TryGetValue("conditions", out var conditionsList) && conditionsList is List<object> condList)
            {
                var results = new List<bool>();

                foreach (var condition in condList)
                {
                    if (condition is Dictionary<string, object> condDict)
                    {
                        results.Add(EvaluateSingleCondition(condDict, context));
                    }
                }

                return operatorStr.ToLowerInvariant() switch
                {
                    "and" => results.All(r => r),
                    "or" => results.Any(r => r),
                    _ => true
                };
            }
        }

        return true;
    }

    private bool EvaluateSingleCondition(Dictionary<string, object> condition, Dictionary<string, object> context)
    {
        if (condition.TryGetValue("field", out var field) && field is string fieldName &&
            condition.TryGetValue("operator", out var op) && op is string operatorStr &&
            condition.TryGetValue("value", out var expectedValue))
        {
            context.TryGetValue(fieldName, out var contextValue);
            return EvaluateOperator(operatorStr, contextValue, expectedValue);
        }

        return true;
    }

    private bool EvaluateOperator(string operatorStr, object? contextValue, object expectedValue)
    {
        return operatorStr.ToLowerInvariant() switch
        {
            "equals" or "eq" => Equals(contextValue, expectedValue),
            "not_equals" or "ne" => !Equals(contextValue, expectedValue),
            "greater_than" or "gt" => CompareValues(contextValue, expectedValue) > 0,
            "greater_than_or_equal" or "gte" => CompareValues(contextValue, expectedValue) >= 0,
            "less_than" or "lt" => CompareValues(contextValue, expectedValue) < 0,
            "less_than_or_equal" or "lte" => CompareValues(contextValue, expectedValue) <= 0,
            "contains" => contextValue?.ToString()?.Contains(expectedValue?.ToString() ?? "") == true,
            "is_empty" => IsValueEmpty(contextValue),
            "is_not_empty" => !IsValueEmpty(contextValue),
            _ => true
        };
    }

    private int CompareValues(object? value1, object value2)
    {
        if (value1 is IComparable comparable1 && value2 is IComparable comparable2)
        {
            try
            {
                return comparable1.CompareTo(comparable2);
            }
            catch
            {
                return 0;
            }
        }
        return 0;
    }

    private bool IsValueEmpty(object? value)
    {
        return value == null ||
               (value is string str && string.IsNullOrWhiteSpace(str)) ||
               (value is Array arr && arr.Length == 0) ||
               (value is System.Collections.ICollection collection && collection.Count == 0);
    }

    private List<string> ApplyStepValidationRules(Dictionary<string, object> submissionData)
    {
        var errors = new List<string>();

        // Apply custom validation rules defined in ValidationRules
        foreach (var rule in ValidationRules)
        {
            if (rule.Value is Dictionary<string, object> ruleDefinition)
            {
                var ruleErrors = EvaluateValidationRule(rule.Key, ruleDefinition, submissionData);
                errors.AddRange(ruleErrors);
            }
        }

        return errors;
    }

    private List<string> EvaluateValidationRule(string ruleName, Dictionary<string, object> ruleDefinition, Dictionary<string, object> submissionData)
    {
        var errors = new List<string>();

        // Implement step-level validation rules
        switch (ruleName.ToLowerInvariant())
        {
            case "required_fields":
                if (ruleDefinition.TryGetValue("fields", out var fieldsObj) && fieldsObj is List<string> requiredFieldNames)
                {
                    foreach (var fieldName in requiredFieldNames)
                    {
                        if (!submissionData.ContainsKey(fieldName) || IsValueEmpty(submissionData[fieldName]))
                        {
                            errors.Add($"Field '{fieldName}' is required for this step");
                        }
                    }
                }
                break;

            case "conditional_required":
                // Implement conditional required validation
                break;

            case "step_completion":
                // Implement step completion validation
                break;
        }

        return errors;
    }

    private void SetDefaultConfiguration()
    {
        Configuration["show_progress"] = true;
        Configuration["allow_back"] = true;
        Configuration["auto_advance"] = false;
        Configuration["validation_mode"] = "on_next"; // on_next, real_time, on_blur

        Styling["layout"] = "vertical";
        Styling["spacing"] = "medium";
        Styling["animation"] = "slide";
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetConfiguration<T>(string key, T? defaultValue = default)
    {
        if (Configuration.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }
}


