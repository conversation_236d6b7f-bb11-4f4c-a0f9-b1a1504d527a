using MediatR;
using SubscriptionManagement.Application.DTOs;

namespace SubscriptionManagement.Application.Queries.GetTaxCategories
{
    public class GetTaxCategoriesQuery : IRequest<List<TaxCategoryDto>>
    {
        public bool ActiveOnly { get; set; } = true;
        public string? Region { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public string? SearchTerm { get; set; }
    }
}
