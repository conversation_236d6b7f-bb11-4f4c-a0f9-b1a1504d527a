using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using MobileWorkflow.Domain.Entities;
using MobileWorkflow.Domain.Repositories;
using MobileWorkflow.Application.DTOs;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace MobileWorkflow.Infrastructure.Services;

public interface IBarcodeScanningService
{
    Task<ScanSessionDto> CreateScanSessionAsync(CreateScanSessionRequest request, CancellationToken cancellationToken = default);
    Task<ScanSessionDto?> GetScanSessionAsync(Guid sessionId, CancellationToken cancellationToken = default);
    Task<List<ScanSessionDto>> GetUserScanSessionsAsync(Guid userId, bool? isActive = null, CancellationToken cancellationToken = default);
    Task<bool> CompleteScanSessionAsync(Guid sessionId, CancellationToken cancellationToken = default);
    Task<bool> CancelScanSessionAsync(Guid sessionId, CancellationToken cancellationToken = default);
    Task<ScanResultDto> ProcessScanAsync(ProcessScanRequest request, CancellationToken cancellationToken = default);
    Task<List<ScanResultDto>> ProcessBatchScanAsync(ProcessBatchScanRequest request, CancellationToken cancellationToken = default);
    Task<List<ScanResultDto>> GetScanResultsAsync(Guid sessionId, string? status = null, CancellationToken cancellationToken = default);
    Task<BarcodeTemplateDto> CreateBarcodeTemplateAsync(CreateBarcodeTemplateRequest request, CancellationToken cancellationToken = default);
    Task<List<BarcodeTemplateDto>> GetBarcodeTemplatesAsync(string? barcodeFormat = null, CancellationToken cancellationToken = default);
    Task<bool> UpdateBarcodeTemplateAsync(Guid templateId, UpdateBarcodeTemplateRequest request, CancellationToken cancellationToken = default);
    Task<BarcodeValidationResult> ValidateBarcodeAsync(ValidateBarcodeRequest request, CancellationToken cancellationToken = default);
    Task<ScanAnalyticsDto> GetScanAnalyticsAsync(Guid? sessionId = null, Guid? userId = null, DateTime? fromDate = null, CancellationToken cancellationToken = default);
    Task<List<string>> GetSupportedFormatsAsync(CancellationToken cancellationToken = default);
    Task ProcessPendingScanResultsAsync(CancellationToken cancellationToken = default);
}

public class BarcodeScanningService : IBarcodeScanningService
{
    private readonly IScanSessionRepository _sessionRepository;
    private readonly IScanResultRepository _scanResultRepository;
    private readonly IBarcodeTemplateRepository _templateRepository;
    private readonly IBarcodeProcessingService _processingService;
    private readonly ILogger<BarcodeScanningService> _logger;
    private readonly IMemoryCache _cache;
    private readonly IConfiguration _configuration;

    public BarcodeScanningService(
        IScanSessionRepository sessionRepository,
        IScanResultRepository scanResultRepository,
        IBarcodeTemplateRepository templateRepository,
        IBarcodeProcessingService processingService,
        ILogger<BarcodeScanningService> logger,
        IMemoryCache cache,
        IConfiguration configuration)
    {
        _sessionRepository = sessionRepository;
        _scanResultRepository = scanResultRepository;
        _templateRepository = templateRepository;
        _processingService = processingService;
        _logger = logger;
        _cache = cache;
        _configuration = configuration;
    }

    public async Task<ScanSessionDto> CreateScanSessionAsync(CreateScanSessionRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating scan session {SessionName} for user {UserId}", request.SessionName, request.UserId);

            var session = new ScanSession(
                request.UserId,
                request.SessionName,
                request.SessionType,
                request.SessionConfiguration);

            if (request.ValidationRules != null)
            {
                session.SetValidationRules(request.ValidationRules);
            }

            if (!string.IsNullOrEmpty(request.DeviceId))
            {
                session.SetDeviceId(request.DeviceId);
            }

            _sessionRepository.Add(session);
            await _sessionRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Scan session {SessionId} created successfully", session.Id);

            return MapToScanSessionDto(session);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating scan session {SessionName}", request.SessionName);
            throw;
        }
    }

    public async Task<ScanSessionDto?> GetScanSessionAsync(Guid sessionId, CancellationToken cancellationToken = default)
    {
        try
        {
            var session = await _sessionRepository.GetByIdAsync(sessionId, cancellationToken);
            if (session == null)
            {
                return null;
            }

            return MapToScanSessionDto(session);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting scan session {SessionId}", sessionId);
            return null;
        }
    }

    public async Task<List<ScanSessionDto>> GetUserScanSessionsAsync(Guid userId, bool? isActive = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var sessions = await _sessionRepository.GetByUserIdAsync(userId, isActive, cancellationToken);
            return sessions.Select(MapToScanSessionDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting scan sessions for user {UserId}", userId);
            return new List<ScanSessionDto>();
        }
    }

    public async Task<bool> CompleteScanSessionAsync(Guid sessionId, CancellationToken cancellationToken = default)
    {
        try
        {
            var session = await _sessionRepository.GetByIdAsync(sessionId, cancellationToken);
            if (session == null)
            {
                return false;
            }

            session.Complete();
            await _sessionRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Scan session {SessionId} completed", sessionId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error completing scan session {SessionId}", sessionId);
            return false;
        }
    }

    public async Task<bool> CancelScanSessionAsync(Guid sessionId, CancellationToken cancellationToken = default)
    {
        try
        {
            var session = await _sessionRepository.GetByIdAsync(sessionId, cancellationToken);
            if (session == null)
            {
                return false;
            }

            session.Cancel();
            await _sessionRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Scan session {SessionId} cancelled", sessionId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling scan session {SessionId}", sessionId);
            return false;
        }
    }

    public async Task<ScanResultDto> ProcessScanAsync(ProcessScanRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Processing scan for session {SessionId}", request.SessionId);

            var session = await _sessionRepository.GetByIdAsync(request.SessionId, cancellationToken);
            if (session == null)
            {
                throw new InvalidOperationException("Scan session not found");
            }

            if (!session.IsActive())
            {
                throw new InvalidOperationException("Scan session is not active");
            }

            // Create scan result
            var scanResult = new BarcodeScanning.ScanResult(
                request.SessionId,
                request.UserId,
                request.BarcodeData,
                request.BarcodeFormat,
                request.ScanContext);

            if (request.Latitude.HasValue && request.Longitude.HasValue)
            {
                scanResult.SetLocation(request.Latitude.Value, request.Longitude.Value);
            }

            if (!string.IsNullOrEmpty(request.DeviceId))
            {
                scanResult.SetDeviceId(request.DeviceId);
            }

            // Check for duplicates
            var isDuplicate = await CheckForDuplicateAsync(request.SessionId, request.BarcodeData, cancellationToken);
            if (isDuplicate)
            {
                scanResult.MarkAsDuplicate();
            }
            else
            {
                // Validate barcode
                var validationResult = await ValidateBarcodeInternalAsync(request.BarcodeData, request.BarcodeFormat, session.ValidationRules, cancellationToken);
                if (!validationResult.IsValid)
                {
                    scanResult.SetValidationResult(validationResult.ValidationDetails, false);
                }
            }

            _scanResultRepository.Add(scanResult);
            session.AddScanResult(scanResult);

            await _scanResultRepository.SaveChangesAsync(cancellationToken);
            await _sessionRepository.SaveChangesAsync(cancellationToken);

            // Process the scan result asynchronously
            _ = Task.Run(async () => await ProcessScanResultAsync(scanResult.Id, CancellationToken.None));

            _logger.LogDebug("Scan processed for session {SessionId}, result: {Status}", request.SessionId, scanResult.Status);

            return MapToScanResultDto(scanResult);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing scan for session {SessionId}", request.SessionId);
            throw;
        }
    }

    public async Task<List<ScanResultDto>> ProcessBatchScanAsync(ProcessBatchScanRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Processing batch scan with {Count} items for session {SessionId}",
                request.ScanItems.Count, request.SessionId);

            var results = new List<ScanResultDto>();

            foreach (var scanItem in request.ScanItems)
            {
                try
                {
                    var scanRequest = new ProcessScanRequest
                    {
                        SessionId = request.SessionId,
                        UserId = request.UserId,
                        BarcodeData = scanItem.BarcodeData,
                        BarcodeFormat = scanItem.BarcodeFormat,
                        ScanContext = scanItem.ScanContext,
                        Latitude = scanItem.Latitude,
                        Longitude = scanItem.Longitude,
                        DeviceId = request.DeviceId
                    };

                    var result = await ProcessScanAsync(scanRequest, cancellationToken);
                    results.Add(result);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing batch scan item {BarcodeData}", scanItem.BarcodeData);

                    // Create a failed result
                    var failedResult = new ScanResultDto
                    {
                        SessionId = request.SessionId,
                        UserId = request.UserId,
                        BarcodeData = scanItem.BarcodeData,
                        BarcodeFormat = scanItem.BarcodeFormat,
                        Status = "Failed",
                        ErrorMessage = ex.Message,
                        ScannedAt = DateTime.UtcNow
                    };
                    results.Add(failedResult);
                }
            }

            _logger.LogInformation("Batch scan completed for session {SessionId}, processed {Count} items",
                request.SessionId, results.Count);

            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing batch scan for session {SessionId}", request.SessionId);
            throw;
        }
    }

    public async Task<List<ScanResultDto>> GetScanResultsAsync(Guid sessionId, string? status = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var scanResults = await _scanResultRepository.GetBySessionIdAsync(sessionId, status, cancellationToken);
            return scanResults.Select(MapToScanResultDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting scan results for session {SessionId}", sessionId);
            return new List<ScanResultDto>();
        }
    }

    public async Task<BarcodeTemplateDto> CreateBarcodeTemplateAsync(CreateBarcodeTemplateRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating barcode template {Name} for format {BarcodeFormat}", request.Name, request.BarcodeFormat);

            var template = new BarcodeScanning.BarcodeTemplate(
                request.Name,
                request.Description,
                request.BarcodeFormat,
                request.Pattern,
                request.CreatedBy);

            if (request.ValidationRules != null)
            {
                template.UpdateValidationRules(request.ValidationRules, request.CreatedBy);
            }

            if (request.ProcessingRules != null)
            {
                template.UpdateProcessingRules(request.ProcessingRules, request.CreatedBy);
            }

            _templateRepository.Add(template);
            await _templateRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Barcode template {TemplateId} created successfully", template.Id);

            return MapToBarcodeTemplateDto(template);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating barcode template {Name}", request.Name);
            throw;
        }
    }

    public async Task<List<BarcodeTemplateDto>> GetBarcodeTemplatesAsync(string? barcodeFormat = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var templates = await _templateRepository.GetTemplatesAsync(barcodeFormat, cancellationToken);
            return templates.Select(MapToBarcodeTemplateDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting barcode templates");
            return new List<BarcodeTemplateDto>();
        }
    }

    public async Task<bool> UpdateBarcodeTemplateAsync(Guid templateId, UpdateBarcodeTemplateRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var template = await _templateRepository.GetByIdAsync(templateId, cancellationToken);
            if (template == null)
            {
                return false;
            }

            if (request.ValidationRules != null)
            {
                template.UpdateValidationRules(request.ValidationRules, request.UpdatedBy);
            }

            if (request.ProcessingRules != null)
            {
                template.UpdateProcessingRules(request.ProcessingRules, request.UpdatedBy);
            }

            if (request.IsActive.HasValue)
            {
                if (request.IsActive.Value)
                {
                    template.Activate(request.UpdatedBy);
                }
                else
                {
                    template.Deactivate(request.UpdatedBy);
                }
            }

            await _templateRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Barcode template {TemplateId} updated successfully", templateId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating barcode template {TemplateId}", templateId);
            return false;
        }
    }

    public async Task<BarcodeValidationResult> ValidateBarcodeAsync(ValidateBarcodeRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var validationRules = request.ValidationRules ?? new Dictionary<string, object>();
            return await ValidateBarcodeInternalAsync(request.BarcodeData, request.BarcodeFormat, validationRules, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating barcode");
            return new BarcodeValidationResult
            {
                IsValid = false,
                ErrorMessage = ex.Message,
                ValidationDetails = new Dictionary<string, object> { ["error"] = ex.Message }
            };
        }
    }

    public async Task<ScanAnalyticsDto> GetScanAnalyticsAsync(Guid? sessionId = null, Guid? userId = null, DateTime? fromDate = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var from = fromDate ?? DateTime.UtcNow.AddDays(-30);
            var scanResults = await _scanResultRepository.GetScanResultsAsync(sessionId, userId, from, cancellationToken);
            var sessions = sessionId.HasValue
                ? new List<ScanSession> { await _sessionRepository.GetByIdAsync(sessionId.Value, cancellationToken) }.Where(s => s != null).Cast<ScanSession>().ToList()
                : userId.HasValue
                    ? await _sessionRepository.GetByUserIdAsync(userId.Value, null, cancellationToken)
                    : await _sessionRepository.GetSessionsAsync(from, cancellationToken);

            var analytics = new ScanAnalyticsDto
            {
                FromDate = from,
                ToDate = DateTime.UtcNow,
                TotalScans = scanResults.Count,
                SuccessfulScans = scanResults.Count(r => r.IsSuccessful()),
                FailedScans = scanResults.Count(r => r.IsFailed()),
                DuplicateScans = scanResults.Count(r => r.IsDuplicate()),
                InvalidScans = scanResults.Count(r => r.IsInvalid()),
                SuccessRate = scanResults.Count > 0 ? (double)scanResults.Count(r => r.IsSuccessful()) / scanResults.Count * 100 : 0,
                TotalSessions = sessions.Count,
                CompletedSessions = sessions.Count(s => s.IsCompleted()),
                ActiveSessions = sessions.Count(s => s.IsActive()),
                ScansByFormat = scanResults.GroupBy(r => r.BarcodeFormat).ToDictionary(g => g.Key, g => g.Count()),
                ScansByHour = scanResults.GroupBy(r => r.ScannedAt.Hour).ToDictionary(g => g.Key, g => g.Count()),
                ScansByDay = scanResults.GroupBy(r => r.ScannedAt.Date).ToDictionary(g => g.Key, g => g.Count()),
                AverageScansPerSession = sessions.Count > 0 ? (double)scanResults.Count / sessions.Count : 0,
                MostActiveUsers = scanResults.GroupBy(r => r.UserId)
                    .OrderByDescending(g => g.Count())
                    .Take(10)
                    .ToDictionary(g => g.Key, g => g.Count())
            };

            return analytics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting scan analytics");
            return new ScanAnalyticsDto { FromDate = fromDate ?? DateTime.UtcNow.AddDays(-30), ToDate = DateTime.UtcNow };
        }
    }

    public async Task<List<string>> GetSupportedFormatsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = "supported_barcode_formats";
            if (_cache.TryGetValue(cacheKey, out List<string>? cachedFormats))
            {
                return cachedFormats!;
            }

            var supportedFormats = await _processingService.GetSupportedFormatsAsync(cancellationToken);

            _cache.Set(cacheKey, supportedFormats, TimeSpan.FromHours(24));
            return supportedFormats;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting supported formats");
            return new List<string> { "QR", "Code128", "Code39", "EAN13", "UPC", "DataMatrix", "PDF417" };
        }
    }

    public async Task ProcessPendingScanResultsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var pendingScanResults = await _scanResultRepository.GetUnprocessedScanResultsAsync(cancellationToken);

            foreach (var scanResult in pendingScanResults)
            {
                try
                {
                    await ProcessScanResultAsync(scanResult.Id, cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing scan result {ScanResultId}", scanResult.Id);
                }
            }

            if (pendingScanResults.Any())
            {
                _logger.LogInformation("Processed {Count} pending scan results", pendingScanResults.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing pending scan results");
        }
    }

    // Helper methods
    private async Task<bool> CheckForDuplicateAsync(Guid sessionId, string barcodeData, CancellationToken cancellationToken)
    {
        try
        {
            var existingScan = await _scanResultRepository.GetBySessionAndBarcodeAsync(sessionId, barcodeData, cancellationToken);
            return existingScan != null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking for duplicate barcode");
            return false;
        }
    }

    private async Task<BarcodeValidationResult> ValidateBarcodeInternalAsync(
        string barcodeData,
        string barcodeFormat,
        Dictionary<string, object> validationRules,
        CancellationToken cancellationToken)
    {
        var result = new BarcodeValidationResult
        {
            IsValid = true,
            ValidationDetails = new Dictionary<string, object>()
        };

        try
        {
            // Get template for format
            var templates = await _templateRepository.GetByFormatAsync(barcodeFormat, cancellationToken);
            var activeTemplate = templates.FirstOrDefault(t => t.IsActive);

            if (activeTemplate != null)
            {
                // Validate against template pattern
                if (!activeTemplate.ValidateBarcode(barcodeData))
                {
                    result.IsValid = false;
                    result.ErrorMessage = "Barcode does not match expected pattern";
                    result.ValidationDetails["pattern_validation"] = false;
                    return result;
                }

                // Apply template validation rules
                foreach (var rule in activeTemplate.ValidationRules)
                {
                    var ruleResult = await ApplyValidationRuleAsync(barcodeData, rule.Key, rule.Value, cancellationToken);
                    result.ValidationDetails[rule.Key] = ruleResult;

                    if (!ruleResult)
                    {
                        result.IsValid = false;
                        result.ErrorMessage = $"Validation rule '{rule.Key}' failed";
                    }
                }
            }

            // Apply session-specific validation rules
            foreach (var rule in validationRules)
            {
                var ruleResult = await ApplyValidationRuleAsync(barcodeData, rule.Key, rule.Value, cancellationToken);
                result.ValidationDetails[rule.Key] = ruleResult;

                if (!ruleResult)
                {
                    result.IsValid = false;
                    result.ErrorMessage = $"Session validation rule '{rule.Key}' failed";
                }
            }

            // Basic format validation
            var formatValidation = await _processingService.ValidateFormatAsync(barcodeData, barcodeFormat, cancellationToken);
            result.ValidationDetails["format_validation"] = formatValidation;

            if (!formatValidation)
            {
                result.IsValid = false;
                result.ErrorMessage = "Invalid barcode format";
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during barcode validation");
            result.IsValid = false;
            result.ErrorMessage = ex.Message;
            result.ValidationDetails["validation_error"] = ex.Message;
            return result;
        }
    }

    private async Task<bool> ApplyValidationRuleAsync(string barcodeData, string ruleName, object ruleValue, CancellationToken cancellationToken)
    {
        try
        {
            return ruleName.ToLowerInvariant() switch
            {
                "min_length" => barcodeData.Length >= Convert.ToInt32(ruleValue),
                "max_length" => barcodeData.Length <= Convert.ToInt32(ruleValue),
                "exact_length" => barcodeData.Length == Convert.ToInt32(ruleValue),
                "starts_with" => barcodeData.StartsWith(ruleValue.ToString()!, StringComparison.OrdinalIgnoreCase),
                "ends_with" => barcodeData.EndsWith(ruleValue.ToString()!, StringComparison.OrdinalIgnoreCase),
                "contains" => barcodeData.Contains(ruleValue.ToString()!, StringComparison.OrdinalIgnoreCase),
                "regex" => Regex.IsMatch(barcodeData, ruleValue.ToString()!),
                "numeric_only" => barcodeData.All(char.IsDigit),
                "alphanumeric_only" => barcodeData.All(char.IsLetterOrDigit),
                "allowed_values" => ruleValue is List<object> allowedValues && allowedValues.Contains(barcodeData),
                "forbidden_values" => ruleValue is List<object> forbiddenValues && !forbiddenValues.Contains(barcodeData),
                "checksum" => await ValidateChecksumAsync(barcodeData, ruleValue.ToString()!, cancellationToken),
                _ => true // Unknown rules pass by default
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying validation rule {RuleName}", ruleName);
            return false;
        }
    }

    private async Task<bool> ValidateChecksumAsync(string barcodeData, string checksumType, CancellationToken cancellationToken)
    {
        try
        {
            return await _processingService.ValidateChecksumAsync(barcodeData, checksumType, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating checksum");
            return false;
        }
    }

    private async Task ProcessScanResultAsync(Guid scanResultId, CancellationToken cancellationToken)
    {
        try
        {
            var scanResult = await _scanResultRepository.GetByIdAsync(scanResultId, cancellationToken);
            if (scanResult == null || scanResult.IsProcessed)
            {
                return;
            }

            var processingResult = await _processingService.ProcessScanResultAsync(scanResult, cancellationToken);
            scanResult.MarkAsProcessed(processingResult);

            await _scanResultRepository.SaveChangesAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing scan result {ScanResultId}", scanResultId);
        }
    }

    // Mapping methods
    private ScanSessionDto MapToScanSessionDto(ScanSession session)
    {
        return new ScanSessionDto
        {
            Id = session.Id,
            UserId = session.UserId,
            SessionName = session.SessionName,
            SessionType = session.SessionType,
            Status = session.Status,
            StartedAt = session.StartedAt,
            CompletedAt = session.CompletedAt,
            DeviceId = session.DeviceId,
            SessionConfiguration = session.SessionConfiguration,
            ValidationRules = session.ValidationRules,
            TotalScans = session.TotalScans,
            SuccessfulScans = session.SuccessfulScans,
            FailedScans = session.FailedScans,
            DuplicateScans = session.DuplicateScans,
            SessionData = session.SessionData,
            IsActive = session.IsActive(),
            IsCompleted = session.IsCompleted(),
            Duration = session.GetDuration(),
            SuccessRate = session.GetSuccessRate(),
            FailureRate = session.GetFailureRate(),
            DuplicateRate = session.GetDuplicateRate()
        };
    }

    private ScanResultDto MapToScanResultDto(BarcodeScanning.ScanResult scanResult)
    {
        return new ScanResultDto
        {
            Id = scanResult.Id,
            SessionId = scanResult.SessionId,
            UserId = scanResult.UserId,
            BarcodeData = scanResult.BarcodeData,
            BarcodeFormat = scanResult.BarcodeFormat,
            Status = scanResult.Status,
            ScannedAt = scanResult.ScannedAt,
            Latitude = scanResult.Latitude,
            Longitude = scanResult.Longitude,
            DeviceId = scanResult.DeviceId,
            ScanContext = scanResult.ScanContext,
            ValidationResult = scanResult.ValidationResult,
            ProcessingResult = scanResult.ProcessingResult,
            ErrorMessage = scanResult.ErrorMessage,
            ErrorCode = scanResult.ErrorCode,
            IsProcessed = scanResult.IsProcessed,
            ProcessedAt = scanResult.ProcessedAt,
            IsSuccessful = scanResult.IsSuccessful(),
            IsFailed = scanResult.IsFailed(),
            IsDuplicate = scanResult.IsDuplicate(),
            IsInvalid = scanResult.IsInvalid()
        };
    }

    private BarcodeTemplateDto MapToBarcodeTemplateDto(BarcodeScanning.BarcodeTemplate template)
    {
        return new BarcodeTemplateDto
        {
            Id = template.Id,
            Name = template.Name,
            Description = template.Description,
            BarcodeFormat = template.BarcodeFormat,
            Pattern = template.Pattern,
            ValidationRules = template.ValidationRules,
            ProcessingRules = template.ProcessingRules,
            IsActive = template.IsActive,
            CreatedAt = template.CreatedAt,
            UpdatedAt = template.UpdatedAt,
            CreatedBy = template.CreatedBy,
            UpdatedBy = template.UpdatedBy
        };
    }
}

// Supporting interfaces and classes
public interface IBarcodeProcessingService
{
    Task<List<string>> GetSupportedFormatsAsync(CancellationToken cancellationToken = default);
    Task<bool> ValidateFormatAsync(string barcodeData, string format, CancellationToken cancellationToken = default);
    Task<bool> ValidateChecksumAsync(string barcodeData, string checksumType, CancellationToken cancellationToken = default);
    Task<Dictionary<string, object>> ProcessScanResultAsync(BarcodeScanning.ScanResult scanResult, CancellationToken cancellationToken = default);
}

public class BarcodeValidationResult
{
    public bool IsValid { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object> ValidationDetails { get; set; } = new();
}
