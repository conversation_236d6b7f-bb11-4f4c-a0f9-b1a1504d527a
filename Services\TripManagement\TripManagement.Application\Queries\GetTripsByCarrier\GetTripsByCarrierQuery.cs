using MediatR;
using TripManagement.Application.DTOs;
using TripManagement.Domain.Enums;

namespace TripManagement.Application.Queries.GetTripsByCarrier;

public record GetTripsByCarrierQuery : IRequest<PagedResult<TripSummaryDto>>
{
    public Guid CarrierId { get; init; }
    public int Page { get; init; } = 1;
    public int PageSize { get; init; } = 20;
    public TripStatus? Status { get; init; }
    public DateTime? FromDate { get; init; }
    public DateTime? ToDate { get; init; }
    public string? SearchTerm { get; init; }
}

public record PagedResult<T>
{
    public List<T> Items { get; init; } = new();
    public int TotalCount { get; init; }
    public int Page { get; init; }
    public int PageSize { get; init; }
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
    public bool HasNextPage => Page < TotalPages;
    public bool HasPreviousPage => Page > 1;
}

public record TripSummaryDto
{
    public Guid Id { get; init; }
    public string TripNumber { get; init; } = string.Empty;
    public Guid OrderId { get; init; }
    public Guid CarrierId { get; init; }
    public Guid? DriverId { get; init; }
    public string? DriverName { get; init; }
    public Guid? VehicleId { get; init; }
    public string? VehicleNumber { get; init; }
    public TripStatus Status { get; init; }
    public DateTime EstimatedStartTime { get; init; }
    public DateTime EstimatedEndTime { get; init; }
    public DateTime? StartedAt { get; init; }
    public DateTime? CompletedAt { get; init; }
    public decimal EstimatedDistanceKm { get; init; }
    public decimal? ActualDistanceKm { get; init; }
    public bool IsUrgent { get; init; }
    public DateTime CreatedAt { get; init; }
    public LocationDto? CurrentLocation { get; init; }
}
