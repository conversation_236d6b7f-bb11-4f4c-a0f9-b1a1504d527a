# TLI Microservices API Gateway Configuration

## Overview

The TLI API Gateway serves as the single entry point for all microservices in the TLI logistics platform. It provides routing, authentication, rate limiting, and third-party integration capabilities.

## Configured Services

### Core Microservices

| Service | Port | Routes | Description |
|---------|------|--------|-------------|
| **Identity Service** | 5001 | `/api/identity/*` | Authentication and user identity management |
| **User Management** | 5002 | `/api/usermanagement/*` | User profile and role management |
| **Subscription Management** | 5003 | `/api/subscriptions/*`, `/api/plans/*` | Subscription plans and feature management |
| **Order Management** | 5004 | `/api/orders/*`, `/api/rfq/*`, `/api/quotes/*` | RFQ, quotes, and order processing |
| **Network & Fleet Management** | 5005 | `/api/network/*`, `/api/fleet/*`, `/api/drivers/*`, `/api/vehicles/*` | Carrier networks, fleet, and driver management |
| **Trip Management** | 5006 | `/api/trips/*`, `/api/tracking/*` | Trip execution and real-time tracking |
| **Financial & Payment** | 5007 | `/api/payments/*`, `/api/escrow/*`, `/api/settlements/*` | Payment processing and financial management |
| **Communication & Notification** | 5008 | `/api/notifications/*`, `/api/chat/*`, `/api/sms/*`, `/api/whatsapp/*` | Multi-channel communication services |
| **Analytics & BI** | 5009 | `/api/analytics/*`, `/api/reports/*`, `/api/dashboards/*` | Business intelligence and analytics |

### Third-Party Integrations

| Service | Routes | Description |
|---------|--------|-------------|
| **Google Maps API** | `/api/maps/*` | Maps, geocoding, and navigation services |
| **Google Directions** | `/api/directions/*` | Route optimization and directions |
| **Google Geocoding** | `/api/geocode/*` | Address to coordinates conversion |
| **Razorpay Webhooks** | `/api/razorpay/webhook/*` | Payment gateway webhook handling |

### Health Check Endpoints

All services expose health check endpoints at `/health/{service-name}`:

- `/health/identity`
- `/health/usermanagement`
- `/health/subscriptions`
- `/health/orders`
- `/health/network`
- `/health/trips`
- `/health/payments`
- `/health/communications`
- `/health/analytics`

## Security Features

### JWT Authentication

- **Provider**: Bearer token authentication
- **Issuer**: TriTrackzIdentity
- **Audience**: TriTrackzServices
- **Token Expiration**: 60 minutes (120 minutes in development)

### Rate Limiting

- **General Limit**: 1000 requests per minute per IP
- **Hourly Limit**: 10,000 requests per hour per IP
- **Daily Limit**: 100,000 requests per day per IP
- **Localhost Exception**: 10,000 requests per minute for 127.0.0.1

### Security Headers

- `X-Content-Type-Options: nosniff`
- `X-Frame-Options: DENY`
- `X-XSS-Protection: 1; mode=block`
- `Referrer-Policy: strict-origin-when-cross-origin`

### CORS Configuration

- **Development**: Allows localhost origins on ports 3000, 3001, 4200
- **Production**: Configured for specific allowed origins

## Quality of Service (QoS)

- **Circuit Breaker**: 3 exceptions before breaking
- **Break Duration**: 5 seconds
- **Timeout**: 30 seconds per request

## Configuration Files

### ocelot.json
Main routing configuration with all service routes, authentication, and rate limiting rules.

### appsettings.json
Production configuration with JWT settings, rate limiting, and third-party service configurations.

### appsettings.Development.json
Development-specific overrides with relaxed rate limiting and additional CORS origins.

## Usage Examples

### Authentication
```bash
# Get JWT token from Identity Service
curl -X POST http://localhost:5000/api/identity/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}'

# Use token in subsequent requests
curl -X GET http://localhost:5000/api/orders/my-orders \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Google Maps Integration
```bash
# Get directions (requires Google Maps API key)
curl -X GET "http://localhost:5000/api/directions/json?origin=Bangalore&destination=Mumbai&key=YOUR_API_KEY"

# Geocode address
curl -X GET "http://localhost:5000/api/geocode/json?address=Bangalore&key=YOUR_API_KEY"
```

### Health Checks
```bash
# Check overall gateway health
curl -X GET http://localhost:5000/health

# Check specific service health
curl -X GET http://localhost:5000/health/identity
```

## Deployment Considerations

### Environment Variables

Set the following environment variables for production:

```bash
JwtSettings__Secret=your-production-jwt-secret
ThirdPartyServices__GoogleMaps__ApiKey=your-google-maps-api-key
ThirdPartyServices__Razorpay__KeyId=your-razorpay-key-id
ThirdPartyServices__Razorpay__KeySecret=your-razorpay-key-secret
```

### Docker Configuration

The API Gateway is configured to run on port 5000 and can be deployed using Docker with the provided Dockerfile.

### Load Balancing

For production deployments, consider:
- Multiple API Gateway instances behind a load balancer
- Service discovery integration with Consul
- Health check monitoring
- Distributed rate limiting with Redis

## Monitoring and Logging

- **Structured Logging**: Serilog with console and file sinks
- **Health Checks**: Built-in ASP.NET Core health checks
- **Metrics**: Ready for Prometheus integration
- **Tracing**: Request correlation IDs for distributed tracing

## Troubleshooting

### Common Issues

1. **Authentication Failures**: Verify JWT settings match Identity Service configuration
2. **Rate Limiting**: Check IP rate limiting rules and whitelist localhost for development
3. **CORS Errors**: Ensure frontend origins are added to allowed origins list
4. **Service Unavailable**: Check downstream service health and network connectivity

### Debug Mode

Enable debug logging in development:
```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "Ocelot": "Debug"
    }
  }
}
```
