using MonitoringObservability.Domain.Entities;
using MonitoringObservability.Domain.Enums;

namespace MonitoringObservability.Domain.Interfaces;

/// <summary>
/// Repository interface for distributed traces
/// </summary>
public interface ITraceRepository
{
    // Basic CRUD operations
    Task<Trace?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<Trace?> GetByTraceIdAsync(string traceId, CancellationToken cancellationToken = default);
    Task<IEnumerable<Trace>> GetAllAsync(CancellationToken cancellationToken = default);
    Task AddAsync(Trace trace, CancellationToken cancellationToken = default);
    Task UpdateAsync(Trace trace, CancellationToken cancellationToken = default);
    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);

    // Query operations
    Task<IEnumerable<Trace>> SearchTracesAsync(
        string? serviceName = null,
        string? operationName = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        TimeSpan? minDuration = null,
        TimeSpan? maxDuration = null,
        TraceStatus? status = null,
        bool? hasErrors = null,
        Dictionary<string, string>? tags = null,
        int skip = 0,
        int take = 100,
        CancellationToken cancellationToken = default);

    Task<IEnumerable<Trace>> GetSlowTracesAsync(
        string? serviceName,
        TimeSpan threshold,
        TimeSpan period,
        int limit = 100,
        CancellationToken cancellationToken = default);

    Task<IEnumerable<Trace>> GetErrorTracesAsync(
        string? serviceName,
        TimeSpan period,
        int limit = 100,
        CancellationToken cancellationToken = default);

    Task<IEnumerable<Trace>> GetActiveTracesAsync(CancellationToken cancellationToken = default);

    // Analytics operations
    Task<double> GetAverageTraceTimeAsync(
        string serviceName,
        string? operationName = null,
        TimeSpan? period = null,
        CancellationToken cancellationToken = default);

    Task<IEnumerable<(string operationName, double averageTime, int count)>> GetSlowestOperationsAsync(
        string serviceName,
        TimeSpan period,
        int topCount = 10,
        CancellationToken cancellationToken = default);

    Task<Dictionary<string, int>> GetTraceCountByServiceAsync(
        TimeSpan period,
        CancellationToken cancellationToken = default);

    Task<Dictionary<TraceStatus, int>> GetTraceCountByStatusAsync(
        TimeSpan period,
        string? serviceName = null,
        CancellationToken cancellationToken = default);

    // Cleanup operations
    Task<int> DeleteOldTracesAsync(TimeSpan retentionPeriod, CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for spans
/// </summary>
public interface ISpanRepository
{
    // Basic CRUD operations
    Task<Span?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<Span?> GetBySpanIdAsync(string spanId, CancellationToken cancellationToken = default);
    Task<IEnumerable<Span>> GetByTraceIdAsync(string traceId, CancellationToken cancellationToken = default);
    Task AddAsync(Span span, CancellationToken cancellationToken = default);
    Task UpdateAsync(Span span, CancellationToken cancellationToken = default);
    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);

    // Query operations
    Task<IEnumerable<Span>> GetSpansByServiceAsync(
        string serviceName,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default);

    Task<IEnumerable<Span>> GetSpansByOperationAsync(
        string operationName,
        string? serviceName = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default);

    Task<IEnumerable<Span>> GetErrorSpansAsync(
        string? serviceName = null,
        TimeSpan? period = null,
        int limit = 100,
        CancellationToken cancellationToken = default);

    Task<IEnumerable<Span>> GetSlowSpansAsync(
        TimeSpan threshold,
        string? serviceName = null,
        TimeSpan? period = null,
        int limit = 100,
        CancellationToken cancellationToken = default);

    Task<IEnumerable<Span>> GetActiveSpansAsync(CancellationToken cancellationToken = default);

    // Analytics operations
    Task<double> GetAverageSpanDurationAsync(
        string serviceName,
        string? operationName = null,
        TimeSpan? period = null,
        CancellationToken cancellationToken = default);

    Task<Dictionary<string, double>> GetOperationLatencyPercentilesAsync(
        string serviceName,
        string operationName,
        TimeSpan period,
        double[] percentiles,
        CancellationToken cancellationToken = default);

    // Cleanup operations
    Task<int> DeleteOldSpansAsync(TimeSpan retentionPeriod, CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for service dependencies
/// </summary>
public interface IServiceDependencyRepository
{
    // Basic CRUD operations
    Task<ServiceDependency?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<IEnumerable<ServiceDependency>> GetAllAsync(CancellationToken cancellationToken = default);
    Task AddAsync(ServiceDependency dependency, CancellationToken cancellationToken = default);
    Task UpdateAsync(ServiceDependency dependency, CancellationToken cancellationToken = default);
    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);

    // Query operations
    Task<IEnumerable<ServiceDependency>> GetDependenciesForServiceAsync(
        string serviceName,
        CancellationToken cancellationToken = default);

    Task<IEnumerable<ServiceDependency>> GetDependentsOfServiceAsync(
        string serviceName,
        CancellationToken cancellationToken = default);

    Task<ServiceDependency?> GetDependencyAsync(
        string fromService,
        string toService,
        string operationType,
        CancellationToken cancellationToken = default);

    Task<IEnumerable<ServiceDependency>> GetHighErrorRateDependenciesAsync(
        double errorRateThreshold = 0.05,
        CancellationToken cancellationToken = default);

    Task<IEnumerable<ServiceDependency>> GetSlowDependenciesAsync(
        TimeSpan latencyThreshold,
        CancellationToken cancellationToken = default);

    // Analytics operations
    Task<Dictionary<string, int>> GetServiceCallCountsAsync(
        TimeSpan period,
        CancellationToken cancellationToken = default);

    Task<IEnumerable<(string fromService, string toService, double errorRate)>> GetTopErrorRateDependenciesAsync(
        int topCount = 10,
        TimeSpan? period = null,
        CancellationToken cancellationToken = default);

    // Cleanup operations
    Task<int> DeleteOldDependenciesAsync(TimeSpan retentionPeriod, CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for alert rules
/// </summary>
public interface IAlertRuleRepository
{
    // Basic CRUD operations
    Task<AlertRule?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<IEnumerable<AlertRule>> GetAllAsync(CancellationToken cancellationToken = default);
    Task AddAsync(AlertRule alertRule, CancellationToken cancellationToken = default);
    Task UpdateAsync(AlertRule alertRule, CancellationToken cancellationToken = default);
    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);

    // Query operations
    Task<IEnumerable<AlertRule>> GetActiveRulesAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<AlertRule>> GetRulesByServiceAsync(string serviceName, CancellationToken cancellationToken = default);
    Task<IEnumerable<AlertRule>> GetRulesByTypeAsync(AlertRuleType ruleType, CancellationToken cancellationToken = default);
    Task<IEnumerable<AlertRule>> GetRulesByPriorityAsync(int minPriority, CancellationToken cancellationToken = default);
    Task<AlertRule?> GetRuleByNameAsync(string name, CancellationToken cancellationToken = default);

    // Analytics operations
    Task<Dictionary<AlertRuleType, int>> GetRuleCountByTypeAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<(Guid ruleId, string ruleName, int triggerCount)>> GetMostTriggeredRulesAsync(
        int topCount = 10, TimeSpan? period = null, CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for alert correlation
/// </summary>
public interface IAlertCorrelationRepository
{
    // Correlation Rules
    Task<AlertCorrelationRule?> GetRuleByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<IEnumerable<AlertCorrelationRule>> GetActiveRulesAsync(CancellationToken cancellationToken = default);
    Task AddRuleAsync(AlertCorrelationRule rule, CancellationToken cancellationToken = default);
    Task UpdateRuleAsync(AlertCorrelationRule rule, CancellationToken cancellationToken = default);
    Task DeleteRuleAsync(Guid id, CancellationToken cancellationToken = default);

    // Correlation Groups
    Task<AlertCorrelationGroup?> GetGroupByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<AlertCorrelationGroup?> GetGroupByKeyAsync(string groupKey, CancellationToken cancellationToken = default);
    Task<IEnumerable<AlertCorrelationGroup>> GetActiveGroupsAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<AlertCorrelationGroup>> GetGroupsByRuleAsync(Guid ruleId, CancellationToken cancellationToken = default);
    Task AddGroupAsync(AlertCorrelationGroup group, CancellationToken cancellationToken = default);
    Task UpdateGroupAsync(AlertCorrelationGroup group, CancellationToken cancellationToken = default);
    Task DeleteGroupAsync(Guid id, CancellationToken cancellationToken = default);

    // Analytics operations
    Task<Dictionary<CorrelationRuleType, int>> GetCorrelationCountByTypeAsync(
        TimeSpan period, CancellationToken cancellationToken = default);
    Task<IEnumerable<(string groupKey, int alertCount, TimeSpan duration)>> GetLargestCorrelationGroupsAsync(
        int topCount = 10, TimeSpan? period = null, CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for anomaly detection
/// </summary>
public interface IAnomalyDetectionRepository
{
    // Model operations
    Task<AnomalyDetectionModel?> GetModelByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<IEnumerable<AnomalyDetectionModel>> GetAllModelsAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<AnomalyDetectionModel>> GetTrainedModelsAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<AnomalyDetectionModel>> GetModelsByServiceAsync(string? serviceName, CancellationToken cancellationToken = default);
    Task<IEnumerable<AnomalyDetectionModel>> GetModelsByAlgorithmAsync(AnomalyDetectionAlgorithm algorithm, CancellationToken cancellationToken = default);
    Task AddModelAsync(AnomalyDetectionModel model, CancellationToken cancellationToken = default);
    Task UpdateModelAsync(AnomalyDetectionModel model, CancellationToken cancellationToken = default);
    Task DeleteModelAsync(Guid id, CancellationToken cancellationToken = default);

    // Detection result operations
    Task<AnomalyDetectionResult?> GetResultByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<IEnumerable<AnomalyDetectionResult>> GetResultsByModelAsync(Guid modelId, CancellationToken cancellationToken = default);
    Task<IEnumerable<AnomalyDetectionResult>> GetAnomaliesAsync(string? serviceName = null, string? metricName = null, DateTime? fromDate = null, CancellationToken cancellationToken = default);
    Task<IEnumerable<AnomalyDetectionResult>> GetUnreviewedAnomaliesAsync(CancellationToken cancellationToken = default);
    Task AddResultAsync(AnomalyDetectionResult result, CancellationToken cancellationToken = default);
    Task UpdateResultAsync(AnomalyDetectionResult result, CancellationToken cancellationToken = default);
    Task DeleteResultAsync(Guid id, CancellationToken cancellationToken = default);

    // Training session operations
    Task<ModelTrainingSession?> GetTrainingSessionByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<IEnumerable<ModelTrainingSession>> GetTrainingSessionsByModelAsync(Guid modelId, CancellationToken cancellationToken = default);
    Task AddTrainingSessionAsync(ModelTrainingSession session, CancellationToken cancellationToken = default);
    Task UpdateTrainingSessionAsync(ModelTrainingSession session, CancellationToken cancellationToken = default);

    // Analytics operations
    Task<Dictionary<AnomalyDetectionAlgorithm, int>> GetModelCountByAlgorithmAsync(CancellationToken cancellationToken = default);
    Task<Dictionary<string, int>> GetAnomalyCountByServiceAsync(TimeSpan period, CancellationToken cancellationToken = default);
    Task<IEnumerable<(string metricName, int anomalyCount, double averageScore)>> GetTopAnomalousMetricsAsync(
        int topCount = 10, TimeSpan? period = null, CancellationToken cancellationToken = default);

    // Cleanup operations
    Task<int> DeleteOldResultsAsync(TimeSpan retentionPeriod, CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for custom dashboards
/// </summary>
public interface ICustomDashboardRepository
{
    // Dashboard operations
    Task<CustomDashboard?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<IEnumerable<CustomDashboard>> GetAllAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<CustomDashboard>> GetByOwnerAsync(Guid ownerId, CancellationToken cancellationToken = default);
    Task<IEnumerable<CustomDashboard>> GetAccessibleDashboardsAsync(Guid userId, string[] userRoles, CancellationToken cancellationToken = default);
    Task<IEnumerable<CustomDashboard>> GetPublicDashboardsAsync(CancellationToken cancellationToken = default);
    Task AddAsync(CustomDashboard dashboard, CancellationToken cancellationToken = default);
    Task UpdateAsync(CustomDashboard dashboard, CancellationToken cancellationToken = default);
    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);

    // Widget operations
    Task<DashboardWidget?> GetWidgetByIdAsync(Guid widgetId, CancellationToken cancellationToken = default);
    Task<IEnumerable<DashboardWidget>> GetWidgetsByDashboardAsync(Guid dashboardId, CancellationToken cancellationToken = default);
    Task<IEnumerable<DashboardWidget>> GetWidgetsByTypeAsync(WidgetType type, CancellationToken cancellationToken = default);

    // Analytics operations
    Task<IEnumerable<(DateTime Date, int ViewCount)>> GetViewHistoryAsync(Guid dashboardId, TimeSpan period, CancellationToken cancellationToken = default);
    Task<IEnumerable<(Guid widgetId, int viewCount)>> GetPopularWidgetsAsync(Guid dashboardId, TimeSpan period, CancellationToken cancellationToken = default);
    Task<Dictionary<DashboardVisibility, int>> GetDashboardCountByVisibilityAsync(CancellationToken cancellationToken = default);
    Task<Dictionary<WidgetType, int>> GetWidgetCountByTypeAsync(CancellationToken cancellationToken = default);

    // Search operations
    Task<IEnumerable<CustomDashboard>> SearchDashboardsAsync(string searchTerm, Guid? userId = null, string[]? userRoles = null, CancellationToken cancellationToken = default);
    Task<IEnumerable<CustomDashboard>> GetDashboardsByTagAsync(string tagKey, string? tagValue = null, CancellationToken cancellationToken = default);
}
