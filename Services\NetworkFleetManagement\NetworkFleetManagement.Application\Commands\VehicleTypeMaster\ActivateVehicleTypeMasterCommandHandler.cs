using MediatR;
using Microsoft.Extensions.Logging;
using NetworkFleetManagement.Domain.Interfaces;
using Shared.Domain.Common;
using Shared.Messaging;
using NetworkFleetManagement.Domain.Repositories;

namespace NetworkFleetManagement.Application.Commands.VehicleTypeMaster;

/// <summary>
/// Handler for activating a vehicle type master
/// </summary>
public class ActivateVehicleTypeMasterCommandHandler : IRequestHandler<ActivateVehicleTypeMasterCommand, bool>
{
    private readonly IVehicleTypeMasterRepository _repository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<ActivateVehicleTypeMasterCommandHandler> _logger;

    public ActivateVehicleTypeMasterCommandHandler(
        IVehicleTypeMasterRepository repository,
        IUnitOfWork unitOfWork,
        IMessageBroker messageBroker,
        ILogger<ActivateVehicleTypeMasterCommandHandler> logger)
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task<bool> Handle(ActivateVehicleTypeMasterCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Activating vehicle type master with ID: {Id}", request.Id);

            var vehicleTypeMaster = await _repository.GetByIdAsync(request.Id, cancellationToken);
            if (vehicleTypeMaster == null)
            {
                _logger.LogWarning("Vehicle type master with ID: {Id} not found", request.Id);
                return false;
            }

            vehicleTypeMaster.Activate();
            _repository.Update(vehicleTypeMaster);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            await _messageBroker.PublishAsync("vehicletypemaster.activated", new
            {
                Id = vehicleTypeMaster.Id,
                Code = vehicleTypeMaster.Code,
                Name = vehicleTypeMaster.Name,
                ActivatedAt = DateTime.UtcNow
            }, cancellationToken);

            _logger.LogInformation("Successfully activated vehicle type master with ID: {Id}", request.Id);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error activating vehicle type master with ID: {Id}", request.Id);
            throw;
        }
    }
}

/// <summary>
/// Handler for deactivating a vehicle type master
/// </summary>
public class DeactivateVehicleTypeMasterCommandHandler : IRequestHandler<DeactivateVehicleTypeMasterCommand, bool>
{
    private readonly IVehicleTypeMasterRepository _repository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<DeactivateVehicleTypeMasterCommandHandler> _logger;

    public DeactivateVehicleTypeMasterCommandHandler(
        IVehicleTypeMasterRepository repository,
        IUnitOfWork unitOfWork,
        IMessageBroker messageBroker,
        ILogger<DeactivateVehicleTypeMasterCommandHandler> logger)
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task<bool> Handle(DeactivateVehicleTypeMasterCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Deactivating vehicle type master with ID: {Id}", request.Id);

            var vehicleTypeMaster = await _repository.GetByIdAsync(request.Id, cancellationToken);
            if (vehicleTypeMaster == null)
            {
                _logger.LogWarning("Vehicle type master with ID: {Id} not found", request.Id);
                return false;
            }

            vehicleTypeMaster.Deactivate();
            _repository.Update(vehicleTypeMaster);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            await _messageBroker.PublishAsync("vehicletypemaster.deactivated", new
            {
                Id = vehicleTypeMaster.Id,
                Code = vehicleTypeMaster.Code,
                Name = vehicleTypeMaster.Name,
                DeactivatedAt = DateTime.UtcNow
            }, cancellationToken);

            _logger.LogInformation("Successfully deactivated vehicle type master with ID: {Id}", request.Id);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deactivating vehicle type master with ID: {Id}", request.Id);
            throw;
        }
    }
}
