using CommunicationNotification.Application.Commands.Alerts;
using Shared.Domain.Common;
using CommunicationNotification.Application.Common;
using Shared.Domain.Common;
using CommunicationNotification.Domain.Enums;
using Shared.Domain.Common;
using Shared.Domain.Common;
using Microsoft.Extensions.Logging;
using Shared.Domain.Common;

namespace CommunicationNotification.Application.Services;

/// <summary>
/// Service for processing Transport Company compliance reminders
/// </summary>
public interface ITransportCompanyComplianceReminderService
{
    Task<ComplianceReminderProcessingResult> ProcessDailyComplianceRemindersAsync(
        Guid transportCompanyId,
        CancellationToken cancellationToken = default);

    Task<ComplianceReminderProcessingResult> ProcessComplianceReminderForDocumentAsync(
        Guid transportCompanyId,
        string documentType,
        string entityType,
        Guid entityId,
        DateTime expiryDate,
        CancellationToken cancellationToken = default);

    Task<List<ExpiringDocumentInfo>> GetExpiringDocumentsAsync(
        Guid transportCompanyId,
        List<int> reminderDays,
        CancellationToken cancellationToken = default);
}

public class TransportCompanyComplianceReminderService : ITransportCompanyComplianceReminderService
{
    private readonly IMediator _mediator;
    private readonly IUserManagementService _userManagementService;
    private readonly INetworkFleetService _networkFleetService;
    private readonly ILogger<TransportCompanyComplianceReminderService> _logger;

    public TransportCompanyComplianceReminderService(
        IMediator mediator,
        IUserManagementService userManagementService,
        INetworkFleetService networkFleetService,
        ILogger<TransportCompanyComplianceReminderService> logger)
    {
        _mediator = mediator;
        _userManagementService = userManagementService;
        _networkFleetService = networkFleetService;
        _logger = logger;
    }

    public async Task<ComplianceReminderProcessingResult> ProcessDailyComplianceRemindersAsync(
        Guid transportCompanyId,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Processing daily compliance reminders for Transport Company {TransportCompanyId}",
            transportCompanyId);

        try
        {
            var result = new ComplianceReminderProcessingResult
            {
                TransportCompanyId = transportCompanyId,
                ProcessedAt = DateTime.UtcNow
            };

            // Get compliance reminder configuration for the transport company
            var reminderConfig = await GetComplianceReminderConfiguration(transportCompanyId, cancellationToken);
            if (reminderConfig == null || !reminderConfig.IsActive)
            {
                _logger.LogInformation("No active compliance reminder configuration found for Transport Company {TransportCompanyId}",
                    transportCompanyId);
                return result;
            }

            // Get all expiring documents based on configured reminder days
            var allReminderDays = reminderConfig.ReminderConfigurations
                .Where(c => c.IsEnabled)
                .SelectMany(c => c.ReminderDays)
                .Distinct()
                .ToList();

            var expiringDocuments = await GetExpiringDocumentsAsync(transportCompanyId, allReminderDays, cancellationToken);

            if (!expiringDocuments.Any())
            {
                _logger.LogInformation("No expiring documents found for Transport Company {TransportCompanyId}",
                    transportCompanyId);
                return result;
            }

            // Group documents by reminder configuration and process
            var processedReminders = new List<ComplianceReminderInfo>();
            var remindersSentToday = 0;

            foreach (var config in reminderConfig.ReminderConfigurations.Where(c => c.IsEnabled))
            {
                if (remindersSentToday >= reminderConfig.MaxRemindersPerDay)
                {
                    _logger.LogWarning("Maximum reminders per day ({MaxReminders}) reached for Transport Company {TransportCompanyId}",
                        reminderConfig.MaxRemindersPerDay, transportCompanyId);
                    break;
                }

                var documentsForConfig = expiringDocuments
                    .Where(d => d.DocumentType.Equals(config.DocumentType, StringComparison.OrdinalIgnoreCase) &&
                               d.EntityType.Equals(config.EntityType, StringComparison.OrdinalIgnoreCase) &&
                               config.ReminderDays.Contains(d.DaysUntilExpiry))
                    .ToList();

                if (!documentsForConfig.Any()) continue;

                if (reminderConfig.GroupSimilarReminders && documentsForConfig.Count > 1)
                {
                    // Send grouped reminder
                    var groupedReminderResult = await SendGroupedComplianceReminder(
                        transportCompanyId, documentsForConfig, config, reminderConfig, cancellationToken);

                    if (groupedReminderResult.IsSuccess)
                    {
                        processedReminders.Add(new ComplianceReminderInfo
                        {
                            DocumentType = config.DocumentType,
                            EntityType = config.EntityType,
                            DocumentCount = documentsForConfig.Count,
                            IsGrouped = true,
                            SentAt = groupedReminderResult.SentAt,
                            AlertId = groupedReminderResult.AlertId
                        });
                        remindersSentToday++;
                    }
                }
                else
                {
                    // Send individual reminders
                    foreach (var document in documentsForConfig)
                    {
                        if (remindersSentToday >= reminderConfig.MaxRemindersPerDay) break;

                        var individualReminderResult = await SendIndividualComplianceReminder(
                            transportCompanyId, document, config, reminderConfig, cancellationToken);

                        if (individualReminderResult.IsSuccess)
                        {
                            processedReminders.Add(new ComplianceReminderInfo
                            {
                                DocumentType = document.DocumentType,
                                EntityType = document.EntityType,
                                EntityId = document.EntityId,
                                EntityName = document.EntityName,
                                ExpiryDate = document.ExpiryDate,
                                DaysUntilExpiry = document.DaysUntilExpiry,
                                DocumentCount = 1,
                                IsGrouped = false,
                                SentAt = individualReminderResult.SentAt,
                                AlertId = individualReminderResult.AlertId
                            });
                            remindersSentToday++;
                        }
                    }
                }
            }

            result.ProcessedReminders = processedReminders;
            result.TotalRemindersProcessed = processedReminders.Count;
            result.IsSuccess = true;

            _logger.LogInformation("Successfully processed {Count} compliance reminders for Transport Company {TransportCompanyId}",
                processedReminders.Count, transportCompanyId);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process daily compliance reminders for Transport Company {TransportCompanyId}",
                transportCompanyId);

            return new ComplianceReminderProcessingResult
            {
                TransportCompanyId = transportCompanyId,
                ProcessedAt = DateTime.UtcNow,
                IsSuccess = false,
                ErrorMessage = ex.Message
            };
        }
    }

    public async Task<ComplianceReminderProcessingResult> ProcessComplianceReminderForDocumentAsync(
        Guid transportCompanyId,
        string documentType,
        string entityType,
        Guid entityId,
        DateTime expiryDate,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Processing compliance reminder for specific document: {DocumentType} for {EntityType} {EntityId}",
            documentType, entityType, entityId);

        try
        {
            var daysUntilExpiry = (expiryDate.Date - DateTime.UtcNow.Date).Days;

            var reminderConfig = await GetComplianceReminderConfiguration(transportCompanyId, cancellationToken);
            if (reminderConfig == null || !reminderConfig.IsActive)
            {
                return new ComplianceReminderProcessingResult
                {
                    TransportCompanyId = transportCompanyId,
                    ProcessedAt = DateTime.UtcNow,
                    IsSuccess = false,
                    ErrorMessage = "No active compliance reminder configuration found"
                };
            }

            var matchingConfig = reminderConfig.ReminderConfigurations
                .FirstOrDefault(c => c.IsEnabled &&
                                   c.DocumentType.Equals(documentType, StringComparison.OrdinalIgnoreCase) &&
                                   c.EntityType.Equals(entityType, StringComparison.OrdinalIgnoreCase) &&
                                   c.ReminderDays.Contains(daysUntilExpiry));

            if (matchingConfig == null)
            {
                return new ComplianceReminderProcessingResult
                {
                    TransportCompanyId = transportCompanyId,
                    ProcessedAt = DateTime.UtcNow,
                    IsSuccess = false,
                    ErrorMessage = "No matching reminder configuration found for this document"
                };
            }

            // Get entity name for the document
            var entityName = await GetEntityName(entityType, entityId, cancellationToken);

            var document = new ExpiringDocumentInfo
            {
                DocumentType = documentType,
                EntityType = entityType,
                EntityId = entityId,
                EntityName = entityName,
                ExpiryDate = expiryDate,
                DaysUntilExpiry = daysUntilExpiry
            };

            var reminderResult = await SendIndividualComplianceReminder(
                transportCompanyId, document, matchingConfig, reminderConfig, cancellationToken);

            return new ComplianceReminderProcessingResult
            {
                TransportCompanyId = transportCompanyId,
                ProcessedAt = DateTime.UtcNow,
                IsSuccess = reminderResult.IsSuccess,
                ErrorMessage = reminderResult.ErrorMessage,
                TotalRemindersProcessed = reminderResult.IsSuccess ? 1 : 0,
                ProcessedReminders = reminderResult.IsSuccess ? new List<ComplianceReminderInfo>
                {
                    new ComplianceReminderInfo
                    {
                        DocumentType = documentType,
                        EntityType = entityType,
                        EntityId = entityId,
                        EntityName = entityName,
                        ExpiryDate = expiryDate,
                        DaysUntilExpiry = daysUntilExpiry,
                        DocumentCount = 1,
                        IsGrouped = false,
                        SentAt = reminderResult.SentAt,
                        AlertId = reminderResult.AlertId
                    }
                } : new List<ComplianceReminderInfo>()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process compliance reminder for document {DocumentType} {EntityType} {EntityId}",
                documentType, entityType, entityId);

            return new ComplianceReminderProcessingResult
            {
                TransportCompanyId = transportCompanyId,
                ProcessedAt = DateTime.UtcNow,
                IsSuccess = false,
                ErrorMessage = ex.Message
            };
        }
    }

    public async Task<List<ExpiringDocumentInfo>> GetExpiringDocumentsAsync(
        Guid transportCompanyId,
        List<int> reminderDays,
        CancellationToken cancellationToken = default)
    {
        var expiringDocuments = new List<ExpiringDocumentInfo>();

        try
        {
            // Get company documents from UserManagement service
            var companyDocuments = await _userManagementService.GetExpiringCompanyDocumentsAsync(
                transportCompanyId, reminderDays, cancellationToken);
            expiringDocuments.AddRange(companyDocuments);

            // Get vehicle and driver documents from NetworkFleet service
            var fleetDocuments = await _networkFleetService.GetExpiringFleetDocumentsAsync(
                transportCompanyId, reminderDays, cancellationToken);
            expiringDocuments.AddRange(fleetDocuments);

            return expiringDocuments;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get expiring documents for Transport Company {TransportCompanyId}",
                transportCompanyId);
            return expiringDocuments;
        }
    }

    private async Task<TransportCompanyComplianceConfiguration?> GetComplianceReminderConfiguration(
        Guid transportCompanyId,
        CancellationToken cancellationToken)
    {
        // This would typically retrieve from user preferences or a dedicated configuration store
        // For now, return a default configuration
        return new TransportCompanyComplianceConfiguration
        {
            TransportCompanyId = transportCompanyId,
            IsActive = true,
            MaxRemindersPerDay = 5,
            GroupSimilarReminders = true,
            PreferredChannels = new List<NotificationChannel> { NotificationChannel.Email, NotificationChannel.SMS },
            ReminderConfigurations = new List<ComplianceReminderConfiguration>
            {
                new ComplianceReminderConfiguration
                {
                    DocumentType = "GST",
                    EntityType = "Company",
                    ReminderDays = new List<int> { 30, 15, 7, 3, 1 },
                    IsEnabled = true,
                    Priority = Priority.High
                },
                new ComplianceReminderConfiguration
                {
                    DocumentType = "PAN",
                    EntityType = "Company",
                    ReminderDays = new List<int> { 30, 15, 7 },
                    IsEnabled = true,
                    Priority = Priority.Normal
                },
                new ComplianceReminderConfiguration
                {
                    DocumentType = "Insurance",
                    EntityType = "Vehicle",
                    ReminderDays = new List<int> { 30, 15, 7, 3, 1 },
                    IsEnabled = true,
                    Priority = Priority.High
                },
                new ComplianceReminderConfiguration
                {
                    DocumentType = "License",
                    EntityType = "Driver",
                    ReminderDays = new List<int> { 30, 15, 7 },
                    IsEnabled = true,
                    Priority = Priority.Normal
                }
            }
        };
    }

    private async Task<SendDocumentExpiryAlertResult> SendGroupedComplianceReminder(
        Guid transportCompanyId,
        List<ExpiringDocumentInfo> documents,
        ComplianceReminderConfiguration config,
        TransportCompanyComplianceConfiguration reminderConfig,
        CancellationToken cancellationToken)
    {
        var bulkCommand = new SendBulkDocumentExpiryAlertsCommand
        {
            CarrierId = transportCompanyId,
            ExpiringDocuments = documents.Select(d => new DocumentExpiryAlertInfo
            {
                DocumentType = d.DocumentType,
                EntityType = d.EntityType,
                EntityId = d.EntityId,
                EntityName = d.EntityName,
                ExpiryDate = d.ExpiryDate,
                DaysUntilExpiry = d.DaysUntilExpiry,
                DocumentDetails = d.DocumentDetails
            }).ToList(),
            ThresholdDays = documents.First().DaysUntilExpiry,
            AlertType = GetAlertType(documents.First().DaysUntilExpiry),
            PreferredChannels = reminderConfig.PreferredChannels,
            Priority = config.Priority,
            RequireAcknowledgment = config.RequireAcknowledgment,
            GroupByEntityType = true,
            Tags = new List<string> { "transport-company", "compliance", "grouped" }
        };

        var result = await _mediator.Send(bulkCommand, cancellationToken);
        return result.AlertResults.FirstOrDefault() ?? new SendDocumentExpiryAlertResult
        {
            IsSuccess = false,
            ErrorMessage = "No alert results returned"
        };
    }

    private async Task<SendDocumentExpiryAlertResult> SendIndividualComplianceReminder(
        Guid transportCompanyId,
        ExpiringDocumentInfo document,
        ComplianceReminderConfiguration config,
        TransportCompanyComplianceConfiguration reminderConfig,
        CancellationToken cancellationToken)
    {
        var command = new SendDocumentExpiryAlertCommand
        {
            CarrierId = transportCompanyId,
            DocumentType = document.DocumentType,
            EntityType = document.EntityType,
            EntityId = document.EntityId,
            EntityName = document.EntityName,
            ExpiryDate = document.ExpiryDate,
            DaysUntilExpiry = document.DaysUntilExpiry,
            AlertType = GetAlertType(document.DaysUntilExpiry),
            ThresholdDays = document.DaysUntilExpiry,
            DocumentDetails = document.DocumentDetails,
            PreferredChannels = reminderConfig.PreferredChannels,
            Priority = config.Priority,
            RequireAcknowledgment = config.RequireAcknowledgment,
            Tags = new List<string> { "transport-company", "compliance", "individual" }
        };

        return await _mediator.Send(command, cancellationToken);
    }

    private async Task<string> GetEntityName(string entityType, Guid entityId, CancellationToken cancellationToken)
    {
        try
        {
            return entityType.ToLower() switch
            {
                "company" => await _userManagementService.GetCompanyNameAsync(entityId, cancellationToken),
                "vehicle" => await _networkFleetService.GetVehicleNameAsync(entityId, cancellationToken),
                "driver" => await _networkFleetService.GetDriverNameAsync(entityId, cancellationToken),
                _ => $"{entityType} {entityId}"
            };
        }
        catch
        {
            return $"{entityType} {entityId}";
        }
    }

    private string GetAlertType(int daysUntilExpiry)
    {
        return daysUntilExpiry switch
        {
            <= 0 => "Expired",
            <= 3 => "Critical",
            <= 7 => "Warning",
            _ => "Notice"
        };
    }
}

