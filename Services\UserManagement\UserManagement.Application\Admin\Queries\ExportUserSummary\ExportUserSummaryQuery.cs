using MediatR;
using UserManagement.Application.Admin.Queries.GetUsersAdvanced;
using UserManagement.Domain.Enums;

namespace UserManagement.Application.Admin.Queries.ExportUserSummary
{
    public class ExportUserSummaryQuery : IRequest<UserSummaryExportResponse>
    {
        public ExportFormat Format { get; set; } = ExportFormat.CSV;
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public UserType? UserType { get; set; }
        public ProfileStatus? Status { get; set; }
        public string? Location { get; set; }
        public string? Region { get; set; }
        public bool IncludeActivityMetrics { get; set; } = true;
        public bool IncludeDocumentSummary { get; set; } = true;
        public bool IncludeSubscriptionInfo { get; set; } = true;
        public List<string> SelectedColumns { get; set; } = new();
        public int? MaxRecords { get; set; }
        public Guid RequestedBy { get; set; }
        public string RequestedByRole { get; set; }
        public string IpAddress { get; set; }
        public string UserAgent { get; set; }
    }
}
