using TripManagement.Application.Interfaces;

namespace TripManagement.Infrastructure.Persistence;

public class UnitOfWork : IUnitOfWork
{
    private readonly TripManagementDbContext _context;

    public UnitOfWork(TripManagementDbContext context)
    {
        _context = context;
    }

    public async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        return await _context.SaveChangesAsync(cancellationToken);
    }
}
