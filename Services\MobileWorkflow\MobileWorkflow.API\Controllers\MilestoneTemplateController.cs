using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MobileWorkflow.Application.Commands.Milestone;
using MobileWorkflow.Application.DTOs;
using MobileWorkflow.Application.Queries.Milestone;
using System.ComponentModel.DataAnnotations;

namespace MobileWorkflow.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
[Produces("application/json")]
[Tags("Milestone Templates")]
public class MilestoneTemplateController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<MilestoneTemplateController> _logger;

    public MilestoneTemplateController(IMediator mediator, ILogger<MilestoneTemplateController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get all milestone templates with optional filtering
    /// </summary>
    /// <param name="type">Filter by template type (Trip, Order, Delivery, etc.)</param>
    /// <param name="category">Filter by category (Logistics, Transportation, etc.)</param>
    /// <param name="isActive">Filter by active status</param>
    /// <param name="isDefault">Filter by default status</param>
    /// <returns>List of milestone templates matching the criteria</returns>
    /// <response code="200">Returns the list of milestone templates</response>
    /// <response code="401">Unauthorized - Invalid or missing authentication token</response>
    /// <response code="403">Forbidden - Insufficient permissions</response>
    /// <response code="500">Internal server error</response>
    [HttpGet]
    [Authorize(Roles = "Admin,Manager")]
    [ProducesResponseType(typeof(IEnumerable<MilestoneTemplateDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<IEnumerable<MilestoneTemplateDto>>> GetMilestoneTemplates(
        [FromQuery] string? type = null,
        [FromQuery] string? category = null,
        [FromQuery] bool? isActive = null,
        [FromQuery] bool? isDefault = null)
    {
        try
        {
            IEnumerable<MilestoneTemplateDto> templates;

            if (!string.IsNullOrEmpty(type))
            {
                templates = await _mediator.Send(new GetMilestoneTemplatesByTypeQuery { Type = type });
            }
            else if (!string.IsNullOrEmpty(category))
            {
                templates = await _mediator.Send(new GetMilestoneTemplatesByCategoryQuery { Category = category });
            }
            else if (isActive == true)
            {
                templates = await _mediator.Send(new GetActiveMilestoneTemplatesQuery());
            }
            else if (isDefault == true)
            {
                templates = await _mediator.Send(new GetDefaultMilestoneTemplatesQuery());
            }
            else
            {
                templates = await _mediator.Send(new GetAllMilestoneTemplatesQuery());
            }

            return Ok(templates);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting milestone templates");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get milestone template by ID
    /// </summary>
    [HttpGet("{id}")]
    [Authorize(Roles = "Admin,Manager,User")]
    public async Task<ActionResult<MilestoneTemplateDto>> GetMilestoneTemplate(Guid id)
    {
        try
        {
            var template = await _mediator.Send(new GetMilestoneTemplateByIdQuery { Id = id });
            if (template == null)
            {
                return NotFound($"Milestone template with ID {id} not found");
            }

            return Ok(template);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting milestone template: {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get milestone template by name
    /// </summary>
    [HttpGet("by-name/{name}")]
    [Authorize(Roles = "Admin,Manager,User")]
    public async Task<ActionResult<MilestoneTemplateDto>> GetMilestoneTemplateByName(string name)
    {
        try
        {
            var template = await _mediator.Send(new GetMilestoneTemplateByNameQuery { Name = name });
            if (template == null)
            {
                return NotFound($"Milestone template with name '{name}' not found");
            }

            return Ok(template);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting milestone template by name: {Name}", name);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get default milestone template by type
    /// </summary>
    [HttpGet("default/{type}")]
    [Authorize(Roles = "Admin,Manager,User")]
    public async Task<ActionResult<MilestoneTemplateDto>> GetDefaultMilestoneTemplateByType(string type)
    {
        try
        {
            var template = await _mediator.Send(new GetDefaultMilestoneTemplateByTypeQuery { Type = type });
            if (template == null)
            {
                return NotFound($"No default milestone template found for type '{type}'");
            }

            return Ok(template);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting default milestone template by type: {Type}", type);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Search milestone templates
    /// </summary>
    [HttpGet("search")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<IEnumerable<MilestoneTemplateDto>>> SearchMilestoneTemplates([FromQuery] string searchTerm)
    {
        try
        {
            var templates = await _mediator.Send(new SearchMilestoneTemplatesQuery { SearchTerm = searchTerm });
            return Ok(templates);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching milestone templates: {SearchTerm}", searchTerm);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get milestone template previews with pagination
    /// </summary>
    [HttpGet("previews")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<IEnumerable<MilestoneTemplatePreviewDto>>> GetMilestoneTemplatePreviews(
        [FromQuery] string? type = null,
        [FromQuery] string? category = null,
        [FromQuery] bool? isActive = null,
        [FromQuery] bool? isDefault = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 10)
    {
        try
        {
            var query = new GetMilestoneTemplatePreviewsQuery
            {
                Type = type,
                Category = category,
                IsActive = isActive,
                IsDefault = isDefault,
                Page = page,
                PageSize = pageSize
            };

            var previews = await _mediator.Send(query);
            return Ok(previews);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting milestone template previews");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get most used milestone templates
    /// </summary>
    [HttpGet("most-used")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<IEnumerable<MilestoneTemplateDto>>> GetMostUsedMilestoneTemplates([FromQuery] int count = 10)
    {
        try
        {
            var templates = await _mediator.Send(new GetMostUsedMilestoneTemplatesQuery { Count = count });
            return Ok(templates);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting most used milestone templates");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Create a new milestone template
    /// </summary>
    /// <param name="request">The milestone template creation request</param>
    /// <returns>The created milestone template</returns>
    /// <response code="201">Template created successfully</response>
    /// <response code="400">Bad request - Invalid data or validation errors</response>
    /// <response code="401">Unauthorized - Invalid or missing authentication token</response>
    /// <response code="403">Forbidden - Insufficient permissions</response>
    /// <response code="409">Conflict - Template with same name already exists</response>
    /// <response code="422">Unprocessable Entity - Business rule validation failed</response>
    /// <response code="500">Internal server error</response>
    [HttpPost]
    [Authorize(Roles = "Admin,Manager")]
    [ProducesResponseType(typeof(MilestoneTemplateDto), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status409Conflict)]
    [ProducesResponseType(StatusCodes.Status422UnprocessableEntity)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<MilestoneTemplateDto>> CreateMilestoneTemplate([FromBody] CreateMilestoneTemplateRequest request)
    {
        try
        {
            var command = new CreateMilestoneTemplateCommand
            {
                Name = request.Name,
                Description = request.Description,
                Type = request.Type,
                Category = request.Category,
                CreatedBy = GetCurrentUserId(),
                Configuration = request.Configuration,
                Metadata = request.Metadata,
                Steps = request.Steps
            };

            var result = await _mediator.Send(command);
            return CreatedAtAction(nameof(GetMilestoneTemplate), new { id = result.Id }, result);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating milestone template: {Name}", request.Name);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Update milestone template
    /// </summary>
    [HttpPut("{id}")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<MilestoneTemplateDto>> UpdateMilestoneTemplate(Guid id, [FromBody] UpdateMilestoneTemplateRequest request)
    {
        try
        {
            var command = new UpdateMilestoneTemplateCommand
            {
                Id = id,
                Name = request.Name,
                Description = request.Description,
                UpdatedBy = GetCurrentUserId(),
                Configuration = request.Configuration,
                Metadata = request.Metadata
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating milestone template: {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Delete milestone template
    /// </summary>
    [HttpDelete("{id}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> DeleteMilestoneTemplate(Guid id)
    {
        try
        {
            var command = new DeleteMilestoneTemplateCommand
            {
                Id = id,
                DeletedBy = GetCurrentUserId()
            };

            var result = await _mediator.Send(command);
            if (result)
            {
                return NoContent();
            }

            return BadRequest("Failed to delete milestone template");
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting milestone template: {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Activate milestone template
    /// </summary>
    [HttpPost("{id}/activate")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<MilestoneTemplateDto>> ActivateMilestoneTemplate(Guid id)
    {
        try
        {
            var command = new ActivateMilestoneTemplateCommand
            {
                Id = id,
                UpdatedBy = GetCurrentUserId()
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error activating milestone template: {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Deactivate milestone template
    /// </summary>
    [HttpPost("{id}/deactivate")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<MilestoneTemplateDto>> DeactivateMilestoneTemplate(Guid id)
    {
        try
        {
            var command = new DeactivateMilestoneTemplateCommand
            {
                Id = id,
                UpdatedBy = GetCurrentUserId()
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deactivating milestone template: {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Set milestone template as default
    /// </summary>
    [HttpPost("{id}/set-default")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<MilestoneTemplateDto>> SetMilestoneTemplateAsDefault(Guid id)
    {
        try
        {
            var command = new SetMilestoneTemplateAsDefaultCommand
            {
                Id = id,
                UpdatedBy = GetCurrentUserId()
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting milestone template as default: {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Remove milestone template as default
    /// </summary>
    [HttpPost("{id}/remove-default")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<MilestoneTemplateDto>> RemoveMilestoneTemplateAsDefault(Guid id)
    {
        try
        {
            var command = new RemoveMilestoneTemplateAsDefaultCommand
            {
                Id = id,
                UpdatedBy = GetCurrentUserId()
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing milestone template as default: {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Reorder milestone steps
    /// </summary>
    [HttpPost("{id}/reorder-steps")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<MilestoneTemplateDto>> ReorderMilestoneSteps(Guid id, [FromBody] ReorderMilestoneStepsRequest request)
    {
        try
        {
            var command = new ReorderMilestoneStepsCommand
            {
                TemplateId = id,
                StepSequenceMap = request.StepSequenceMap,
                UpdatedBy = GetCurrentUserId()
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reordering milestone steps: {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Validate milestone template
    /// </summary>
    [HttpGet("{id}/validate")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<MilestoneTemplateValidationResult>> ValidateMilestoneTemplate(Guid id)
    {
        try
        {
            var result = await _mediator.Send(new ValidateMilestoneTemplateQuery { Id = id });
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating milestone template: {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Bulk operations on milestone templates
    /// </summary>
    [HttpPost("bulk")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<BulkOperationResult>> BulkMilestoneTemplateOperation([FromBody] BulkMilestoneTemplateOperationRequest request)
    {
        try
        {
            var command = new BulkMilestoneTemplateOperationCommand
            {
                TemplateIds = request.TemplateIds,
                Operation = request.Operation,
                PerformedBy = GetCurrentUserId(),
                Parameters = request.Parameters
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing bulk milestone template operation");
            return StatusCode(500, "Internal server error");
        }
    }

    private string GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst("sub")?.Value ?? User.FindFirst("userId")?.Value;
        return userIdClaim ?? "system";
    }
}
