using System.ComponentModel.DataAnnotations;

namespace AnalyticsBIService.Application.DTOs;

/// <summary>
/// Driver benchmarking data transfer object
/// </summary>
public class DriverBenchmarkingDto
{
    public string DriverId { get; set; } = string.Empty;
    public string DriverName { get; set; } = string.Empty;
    public decimal OverallScore { get; set; }
    public string PerformanceRating { get; set; } = string.Empty; // "Excellent", "Good", "Average", "Needs Improvement"
    public int IndustryPercentile { get; set; }
    public int CompanyPercentile { get; set; }
    public List<DriverBenchmarkMetricDto> BenchmarkMetrics { get; set; } = new();
    public List<DriverComparisonDto> PeerComparisons { get; set; } = new();
    public DateTime BenchmarkDate { get; set; }

    // Properties required by DriverPerformanceAnalyticsService
    public string BenchmarkType { get; set; } = string.Empty;
    public DateTime BenchmarkedAt { get; set; }
    public List<DriverBenchmarkMetricDto> DriverMetrics { get; set; } = new();
    public List<PerformanceComparisonDto> Comparisons { get; set; } = new();
    public int OverallRanking { get; set; }
    public List<PerformanceGapDto> PerformanceGaps { get; set; } = new();
    public List<ImprovementOpportunityDto> ImprovementOpportunities { get; set; } = new();
}

/// <summary>
/// Driver benchmark metric data transfer object
/// </summary>
public class DriverBenchmarkMetricDto
{
    public string MetricName { get; set; } = string.Empty;
    public decimal DriverValue { get; set; }
    public decimal IndustryAverage { get; set; }
    public decimal CompanyAverage { get; set; }
    public decimal TopPerformerValue { get; set; }
    public string Unit { get; set; } = string.Empty;
    public string PerformanceLevel { get; set; } = string.Empty;
    public decimal ImprovementPotential { get; set; }
}

/// <summary>
/// Driver comparison data transfer object
/// </summary>
public class DriverComparisonDto
{
    public string ComparisonDriverId { get; set; } = string.Empty;
    public string ComparisonDriverName { get; set; } = string.Empty;
    public decimal ComparisonScore { get; set; }
    public string RelativePerformance { get; set; } = string.Empty; // "Better", "Similar", "Worse"
    public List<string> StrengthAreas { get; set; } = new();
    public List<string> ImprovementAreas { get; set; } = new();
}

/// <summary>
/// Driver trip data transfer object
/// </summary>
public class DriverTripDataDto
{
    public string DriverId { get; set; } = string.Empty;
    public string TripId { get; set; } = string.Empty;
    public DateTime TripStartTime { get; set; }
    public DateTime TripEndTime { get; set; }
    public decimal TripDuration { get; set; }
    public decimal TripDistance { get; set; }
    public string OriginCity { get; set; } = string.Empty;
    public string DestinationCity { get; set; } = string.Empty;
    public decimal FuelConsumption { get; set; }
    public decimal FuelEfficiency { get; set; }
    public decimal AverageSpeed { get; set; }
    public decimal MaxSpeed { get; set; }
    public int HardBrakingEvents { get; set; }
    public int RapidAccelerationEvents { get; set; }
    public int SpeedingEvents { get; set; }
    public decimal IdleTime { get; set; }
    public decimal DrivingScore { get; set; }
    public string TripStatus { get; set; } = string.Empty;
    public bool OnTimeDelivery { get; set; }
    public decimal DelayTime { get; set; }

    // Properties required by DriverPerformanceAnalyticsService
    public decimal BaseEarnings { get; set; }
    public decimal TotalRevenue { get; set; }
    public int TotalTrips { get; set; }
    public int CompletedTrips { get; set; }
    public int CancelledTrips { get; set; }
    public int OnTimeDeliveries { get; set; }
    public decimal AverageDeliveryTime { get; set; }

    // Additional property required by DriverPerformanceAnalyticsService
    public decimal TotalDrivingHours { get; set; }
}

/// <summary>
/// Driver KPI analytics data transfer object
/// </summary>
public class DriverKPIAnalyticsDto
{
    public string DriverId { get; set; } = string.Empty;
    public string DriverName { get; set; } = string.Empty;
    public decimal SafetyScore { get; set; }
    public decimal EfficiencyScore { get; set; }
    public decimal ReliabilityScore { get; set; }
    public decimal CustomerSatisfactionScore { get; set; }
    public decimal OnTimeDeliveryRate { get; set; }
    public decimal FuelEfficiencyRating { get; set; }
    public int TotalTrips { get; set; }
    public int CompletedTrips { get; set; }
    public decimal TotalMileage { get; set; }
    public decimal TotalDrivingHours { get; set; }
    public int SafetyViolations { get; set; }
    public int CustomerComplaints { get; set; }
    public int CustomerCompliments { get; set; }
    public DateTime AnalyticsPeriodStart { get; set; }
    public DateTime AnalyticsPeriodEnd { get; set; }

    // Properties required by DriverPerformanceAnalyticsService
    public decimal FuelEfficiencyScore { get; set; }
    public DateTime CalculatedAt { get; set; }
    public string DateRange { get; set; } = string.Empty;
    public decimal TripCompletionRate { get; set; }
    public decimal CancellationRate { get; set; }
    public decimal AverageDeliveryTime { get; set; }
    public decimal ServiceQualityScore { get; set; }
    public decimal CommunicationScore { get; set; }
    public decimal ProfessionalismScore { get; set; }
    public decimal TripsPerDay { get; set; }
    public decimal KilometersPerDay { get; set; }
    public decimal HoursUtilization { get; set; }
    public decimal RevenuePerTrip { get; set; }
    public decimal AccidentRate { get; set; }
    public int ViolationCount { get; set; }
    public decimal AttendanceRate { get; set; }
    public decimal AvailabilityRate { get; set; }
    public decimal ResponseTime { get; set; }
    public decimal EcoFriendlyDrivingScore { get; set; }
    public decimal OverallPerformanceScore { get; set; }
    public decimal QualityScore { get; set; }
}

/// <summary>
/// Driver performance insight data transfer object
/// </summary>
public class DriverPerformanceInsightDto
{
    public string DriverId { get; set; } = string.Empty;
    public List<PerformanceInsightDto> Insights { get; set; } = new();
    public List<RecommendationDto> Recommendations { get; set; } = new();
    public List<AlertDto> Alerts { get; set; } = new();
    public decimal OverallPerformanceTrend { get; set; }
    public string TrendDirection { get; set; } = string.Empty; // "Improving", "Declining", "Stable"
    public DateTime LastAnalysisDate { get; set; }
}

/// <summary>
/// Performance insight data transfer object
/// </summary>
public class PerformanceInsightDto
{
    public string InsightType { get; set; } = string.Empty; // "Strength", "Weakness", "Opportunity", "Risk"
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal ImpactScore { get; set; }
    public string Priority { get; set; } = string.Empty; // "High", "Medium", "Low"
    public List<string> SupportingData { get; set; } = new();
}

/// <summary>
/// Recommendation data transfer object
/// </summary>
public class RecommendationDto
{
    public string RecommendationType { get; set; } = string.Empty; // "Training", "Route Optimization", "Behavior Change"
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal ExpectedImpact { get; set; }
    public string Priority { get; set; } = string.Empty;
    public string TimeFrame { get; set; } = string.Empty; // "Immediate", "Short-term", "Long-term"
    public List<string> ActionSteps { get; set; } = new();
}

// AlertDto is already defined in AnalyticsDto.cs - using that instead

/// <summary>
/// Driver trend analysis data transfer object
/// </summary>
public class DriverTrendAnalysisDto
{
    public string DriverId { get; set; } = string.Empty;
    public List<TrendAnalysisDto> SafetyTrends { get; set; } = new();
    public List<TrendAnalysisDto> EfficiencyTrends { get; set; } = new();
    public List<TrendAnalysisDto> ReliabilityTrends { get; set; } = new();
    public List<TrendAnalysisDto> CustomerSatisfactionTrends { get; set; } = new();
    public string OverallTrendDirection { get; set; } = string.Empty;
    public decimal TrendStrength { get; set; }
    public DateTime TrendAnalysisDate { get; set; }

    // Properties required by DriverPerformanceAnalyticsService
    public DateTime AnalyzedAt { get; set; }
    public string DateRange { get; set; } = string.Empty;
    public List<TrendAnalysisDto> PerformanceTrends { get; set; } = new();
    public List<TrendAnalysisDto> EarningsTrends { get; set; } = new();
    public List<TrendAnalysisDto> QualityTrends { get; set; } = new();
    public List<TrendAnalysisDto> TrendPredictions { get; set; } = new();
    public List<TrendAnalysisDto> SeasonalPatterns { get; set; } = new();
    public List<TrendAnalysisDto> PerformanceCorrelations { get; set; } = new();
}

/// <summary>
/// Trend analysis data transfer object
/// </summary>
public class TrendAnalysisDto
{
    public string MetricName { get; set; } = string.Empty;
    public string TrendDirection { get; set; } = string.Empty; // "Improving", "Declining", "Stable"
    public decimal TrendSlope { get; set; }
    public decimal ConfidenceLevel { get; set; }
    public List<TrendDataPointDto> DataPoints { get; set; } = new();
    public string TimeFrame { get; set; } = string.Empty; // "Daily", "Weekly", "Monthly"
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
}

/// <summary>
/// Driver safety analytics data transfer object
/// </summary>
public class DriverSafetyAnalyticsDto
{
    public string DriverId { get; set; } = string.Empty;
    public decimal SafetyScore { get; set; }
    public int TotalSafetyEvents { get; set; }
    public int HardBrakingEvents { get; set; }
    public int RapidAccelerationEvents { get; set; }
    public int SpeedingEvents { get; set; }
    public int SharpTurnEvents { get; set; }
    public int DistractedDrivingEvents { get; set; }
    public decimal AccidentRate { get; set; }
    public int NearMissEvents { get; set; }
    public decimal SafeMileage { get; set; }
    public int DaysSinceLastIncident { get; set; }
    public List<SafetyEventDto> RecentSafetyEvents { get; set; } = new();
    public List<SafetyImprovementDto> SafetyImprovements { get; set; } = new();
}

/// <summary>
/// Safety event data transfer object
/// </summary>
public class SafetyEventDto
{
    public string EventId { get; set; } = string.Empty;
    public string EventType { get; set; } = string.Empty;
    public DateTime EventDateTime { get; set; }
    public string Location { get; set; } = string.Empty;
    public decimal Severity { get; set; }
    public string Description { get; set; } = string.Empty;
    public bool ActionTaken { get; set; }
    public string ActionDescription { get; set; } = string.Empty;
}

/// <summary>
/// Safety improvement data transfer object
/// </summary>
public class SafetyImprovementDto
{
    public string ImprovementArea { get; set; } = string.Empty;
    public decimal BaselineValue { get; set; }
    public decimal CurrentValue { get; set; }
    public decimal ImprovementPercentage { get; set; }
    public DateTime ImprovementStartDate { get; set; }
    public string ImprovementMethod { get; set; } = string.Empty;
}

/// <summary>
/// Driver efficiency analytics data transfer object
/// </summary>
public class DriverEfficiencyAnalyticsDto
{
    public string DriverId { get; set; } = string.Empty;
    public decimal EfficiencyScore { get; set; }
    public decimal FuelEfficiency { get; set; }
    public decimal RouteOptimizationScore { get; set; }
    public decimal TimeUtilizationRate { get; set; }
    public decimal IdleTimePercentage { get; set; }
    public decimal AverageSpeed { get; set; }
    public decimal OptimalSpeedAdherence { get; set; }
    public int TotalStops { get; set; }
    public decimal AverageStopTime { get; set; }
    public decimal DeliveryEfficiency { get; set; }
    public List<EfficiencyMetricDto> EfficiencyMetrics { get; set; } = new();
    public List<EfficiencyOpportunityDto> ImprovementOpportunities { get; set; } = new();
}

/// <summary>
/// Efficiency metric data transfer object
/// </summary>
public class EfficiencyMetricDto
{
    public string MetricName { get; set; } = string.Empty;
    public decimal CurrentValue { get; set; }
    public decimal BenchmarkValue { get; set; }
    public decimal EfficiencyRating { get; set; }
    public string Unit { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;

    // Properties required by GetTransportCompanyAdvancedAnalyticsQueryHandler
    public decimal TargetValue { get; set; }
    public decimal PerformanceGap { get; set; }
    public decimal ImprovementPotential { get; set; }
    public string Trend { get; set; } = string.Empty;
}

// EfficiencyOpportunityDto is already defined in AdminAnalyticsDto.cs - using that instead
