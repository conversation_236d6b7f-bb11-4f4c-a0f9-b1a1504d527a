using MediatR;
using TripManagement.Application.DTOs;

namespace TripManagement.Application.Commands.UpdateETA;

public record UpdateETACommand : IRequest<ETAUpdateResult>
{
    public Guid TripId { get; init; }
    public LocationDto CurrentLocation { get; init; } = null!;
    public double? CurrentSpeed { get; init; }
    public bool ConsiderTraffic { get; init; } = true;
    public bool ConsiderWeather { get; init; } = true;
    public bool ConsiderDriverBehavior { get; init; } = true;
}

public record ETAUpdateResult
{
    public Guid TripId { get; init; }
    public DateTime UpdatedETA { get; init; }
    public DateTime PreviousETA { get; init; }
    public TimeSpan ETAChange { get; init; }
    public double RemainingDistanceKm { get; init; }
    public List<StopETADto> StopETAs { get; init; } = new();
    public string UpdateReason { get; init; } = string.Empty;
    public ETAConfidenceLevel ConfidenceLevel { get; init; }
    public double ConfidenceScore { get; init; }
    public List<string> InfluencingFactors { get; init; } = new();
    public DateTime UpdatedAt { get; init; } = DateTime.UtcNow;
}

public record StopETADto
{
    public Guid StopId { get; init; }
    public string StopName { get; init; } = string.Empty;
    public DateTime EstimatedArrival { get; init; }
    public DateTime? PreviousEstimatedArrival { get; init; }
    public TimeSpan? ETAChange { get; init; }
    public double DistanceFromCurrentLocation { get; init; }
}

public enum ETAConfidenceLevel
{
    Low = 0,
    Medium = 1,
    High = 2,
    VeryHigh = 3
}
