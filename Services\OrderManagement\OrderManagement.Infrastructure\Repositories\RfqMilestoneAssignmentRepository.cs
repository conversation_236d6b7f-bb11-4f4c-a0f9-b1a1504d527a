using Microsoft.EntityFrameworkCore;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Entities;
using OrderManagement.Infrastructure.Persistence;

namespace OrderManagement.Infrastructure.Repositories;

public class RfqMilestoneAssignmentRepository : IRfqMilestoneAssignmentRepository
{
    private readonly OrderManagementDbContext _context;

    public RfqMilestoneAssignmentRepository(OrderManagementDbContext context)
    {
        _context = context;
    }

    public async Task<RfqMilestoneAssignment?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.RfqMilestoneAssignments
            .Include(rma => rma.MilestoneTemplate)
                .ThenInclude(mt => mt.Steps.OrderBy(s => s.StepOrder))
            .Include(rma => rma.MilestoneProgress)
                .ThenInclude(mp => mp.MilestoneStep)
            .Include(rma => rma.RFQ)
            .FirstOrDefaultAsync(rma => rma.Id == id, cancellationToken);
    }

    public async Task<RfqMilestoneAssignment?> GetActiveByRfqIdAsync(Guid rfqId, CancellationToken cancellationToken = default)
    {
        return await _context.RfqMilestoneAssignments
            .Include(rma => rma.MilestoneTemplate)
                .ThenInclude(mt => mt.Steps.OrderBy(s => s.StepOrder))
            .Include(rma => rma.MilestoneProgress)
                .ThenInclude(mp => mp.MilestoneStep)
            .Where(rma => rma.RfqId == rfqId && rma.IsActive)
            .FirstOrDefaultAsync(cancellationToken);
    }

    public async Task<List<RfqMilestoneAssignment>> GetByRfqIdAsync(Guid rfqId, bool activeOnly = true, CancellationToken cancellationToken = default)
    {
        var query = _context.RfqMilestoneAssignments
            .Include(rma => rma.MilestoneTemplate)
                .ThenInclude(mt => mt.Steps.OrderBy(s => s.StepOrder))
            .Include(rma => rma.MilestoneProgress)
                .ThenInclude(mp => mp.MilestoneStep)
            .Where(rma => rma.RfqId == rfqId);

        if (activeOnly)
        {
            query = query.Where(rma => rma.IsActive);
        }

        return await query
            .OrderByDescending(rma => rma.AssignedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<RfqMilestoneAssignment>> GetByTemplateIdAsync(Guid templateId, bool activeOnly = true, CancellationToken cancellationToken = default)
    {
        var query = _context.RfqMilestoneAssignments
            .Include(rma => rma.RFQ)
            .Include(rma => rma.MilestoneProgress)
            .Where(rma => rma.MilestoneTemplateId == templateId);

        if (activeOnly)
        {
            query = query.Where(rma => rma.IsActive);
        }

        return await query
            .OrderByDescending(rma => rma.AssignedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task AddAsync(RfqMilestoneAssignment assignment, CancellationToken cancellationToken = default)
    {
        await _context.RfqMilestoneAssignments.AddAsync(assignment, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public void Update(RfqMilestoneAssignment assignment)
    {
        _context.RfqMilestoneAssignments.Update(assignment);
    }

    public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var assignment = await GetByIdAsync(id, cancellationToken);
        if (assignment != null)
        {
            _context.RfqMilestoneAssignments.Remove(assignment);
            await _context.SaveChangesAsync(cancellationToken);
        }
    }
}
