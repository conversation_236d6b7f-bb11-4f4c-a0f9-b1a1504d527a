using Shared.Domain.Common;

namespace UserManagement.Domain.Entities
{
    public enum AuditAction
    {
        Create,
        Update,
        Delete,
        Approve,
        Reject,
        Submit,
        Review,
        Upload,
        Download,
        View,
        Login,
        Logout,
        PasswordChange,
        StatusChange,
        BulkOperation
    }

    public enum AuditSeverity
    {
        Low,
        Medium,
        High,
        Critical
    }

    public class AuditLog : BaseEntity
    {
        public AuditAction Action { get; private set; }
        public string EntityType { get; private set; }
        public Guid? EntityId { get; private set; }
        public Guid? UserId { get; private set; }
        public string UserName { get; private set; }
        public string UserRole { get; private set; }
        public string Description { get; private set; }
        public string? OldValues { get; private set; }
        public string? NewValues { get; private set; }
        public AuditSeverity Severity { get; private set; }
        public string IpAddress { get; private set; }
        public string UserAgent { get; private set; }
        public string? SessionId { get; private set; }
        public string? CorrelationId { get; private set; }
        public Dictionary<string, object> AdditionalData { get; private set; }
        public DateTime Timestamp { get; private set; }
        public bool IsImmutable { get; private set; } = true;

        private AuditLog()
        {
            AdditionalData = new Dictionary<string, object>();
        }

        public AuditLog(
            AuditAction action,
            string entityType,
            Guid? entityId,
            Guid? userId,
            string userName,
            string userRole,
            string description,
            AuditSeverity severity = AuditSeverity.Medium,
            string? oldValues = null,
            string? newValues = null,
            string ipAddress = "",
            string userAgent = "",
            string? sessionId = null,
            string? correlationId = null,
            Dictionary<string, object>? additionalData = null)
        {
            Action = action;
            EntityType = entityType;
            EntityId = entityId;
            UserId = userId;
            UserName = userName;
            UserRole = userRole;
            Description = description;
            Severity = severity;
            OldValues = oldValues;
            NewValues = newValues;
            IpAddress = ipAddress;
            UserAgent = userAgent;
            SessionId = sessionId;
            CorrelationId = correlationId;
            AdditionalData = additionalData ?? new Dictionary<string, object>();
            Timestamp = DateTime.UtcNow;
            CreatedAt = DateTime.UtcNow;
            IsImmutable = true;
        }

        // Prevent modifications to audit logs
        public void PreventUpdate()
        {
            if (IsImmutable)
                throw new InvalidOperationException("Audit logs are immutable and cannot be modified");
        }

        public void AddAdditionalData(string key, object value)
        {
            if (IsImmutable && CreatedAt != default)
                throw new InvalidOperationException("Cannot modify audit log after creation");

            AdditionalData[key] = value;
        }

        public static AuditLog CreateUserApprovalLog(Guid userId, string userName, Guid approvedBy, string approverName,
            string approverRole, string ipAddress, string userAgent, string? notes = null)
        {
            var additionalData = new Dictionary<string, object>();
            if (!string.IsNullOrEmpty(notes))
                additionalData["notes"] = notes;

            return new AuditLog(
                AuditAction.Approve,
                "UserProfile",
                userId,
                approvedBy,
                approverName,
                approverRole,
                $"User profile approved for {userName}",
                AuditSeverity.High,
                oldValues: "Status: UnderReview",
                newValues: "Status: Approved",
                ipAddress: ipAddress,
                userAgent: userAgent,
                additionalData: additionalData
            );
        }

        public static AuditLog CreateUserRejectionLog(Guid userId, string userName, Guid rejectedBy, string rejectorName,
            string rejectorRole, string reason, string ipAddress, string userAgent, string? notes = null)
        {
            var additionalData = new Dictionary<string, object>
            {
                ["rejection_reason"] = reason
            };
            if (!string.IsNullOrEmpty(notes))
                additionalData["notes"] = notes;

            return new AuditLog(
                AuditAction.Reject,
                "UserProfile",
                userId,
                rejectedBy,
                rejectorName,
                rejectorRole,
                $"User profile rejected for {userName}: {reason}",
                AuditSeverity.High,
                oldValues: "Status: UnderReview",
                newValues: "Status: Rejected",
                ipAddress: ipAddress,
                userAgent: userAgent,
                additionalData: additionalData
            );
        }

        public static AuditLog CreateDocumentApprovalLog(Guid submissionId, string documentType, Guid approvedBy,
            string approverName, string approverRole, string ipAddress, string userAgent, string? notes = null)
        {
            var additionalData = new Dictionary<string, object>
            {
                ["document_type"] = documentType
            };
            if (!string.IsNullOrEmpty(notes))
                additionalData["notes"] = notes;

            return new AuditLog(
                AuditAction.Approve,
                "Document",
                submissionId,
                approvedBy,
                approverName,
                approverRole,
                $"Document {documentType} approved",
                AuditSeverity.Medium,
                oldValues: "Status: UnderReview",
                newValues: "Status: Approved",
                ipAddress: ipAddress,
                userAgent: userAgent,
                additionalData: additionalData
            );
        }

        public static AuditLog CreateDocumentRejectionLog(Guid submissionId, string documentType, Guid rejectedBy,
            string rejectorName, string rejectorRole, string reason, string ipAddress, string userAgent,
            bool requiresResubmission = true, string? notes = null)
        {
            var additionalData = new Dictionary<string, object>
            {
                ["document_type"] = documentType,
                ["rejection_reason"] = reason,
                ["requires_resubmission"] = requiresResubmission
            };
            if (!string.IsNullOrEmpty(notes))
                additionalData["notes"] = notes;

            var newStatus = requiresResubmission ? "RequiresResubmission" : "Rejected";

            return new AuditLog(
                AuditAction.Reject,
                "Document",
                submissionId,
                rejectedBy,
                rejectorName,
                rejectorRole,
                $"Document {documentType} {(requiresResubmission ? "marked for resubmission" : "rejected")}: {reason}",
                AuditSeverity.Medium,
                oldValues: "Status: UnderReview",
                newValues: $"Status: {newStatus}",
                ipAddress: ipAddress,
                userAgent: userAgent,
                additionalData: additionalData
            );
        }

        public static AuditLog CreateDocumentUploadLog(Guid submissionId, string documentType, Guid userId,
            string userName, string fileName, string ipAddress, string userAgent)
        {
            var additionalData = new Dictionary<string, object>
            {
                ["document_type"] = documentType,
                ["file_name"] = fileName
            };

            return new AuditLog(
                AuditAction.Upload,
                "Document",
                submissionId,
                userId,
                userName,
                "User",
                $"Document {documentType} uploaded: {fileName}",
                AuditSeverity.Low,
                newValues: "Status: Uploaded",
                ipAddress: ipAddress,
                userAgent: userAgent,
                additionalData: additionalData
            );
        }

        public static AuditLog CreateOcrProcessingLog(Guid submissionId, string documentType, decimal confidenceScore,
            bool autoApproved, string systemUser = "System")
        {
            var additionalData = new Dictionary<string, object>
            {
                ["document_type"] = documentType,
                ["confidence_score"] = confidenceScore,
                ["auto_approved"] = autoApproved
            };

            return new AuditLog(
                AuditAction.Review,
                "Document",
                submissionId,
                null,
                systemUser,
                "System",
                $"Document {documentType} processed with OCR. Confidence: {confidenceScore:P2}. Auto-approved: {autoApproved}",
                AuditSeverity.Low,
                additionalData: additionalData
            );
        }

        public static AuditLog CreateBulkOperationLog(string operationType, int itemCount, Guid performedBy,
            string performerName, string performerRole, string ipAddress, string userAgent,
            Dictionary<string, object>? additionalData = null)
        {
            var data = additionalData ?? new Dictionary<string, object>();
            data["operation_type"] = operationType;
            data["item_count"] = itemCount;

            return new AuditLog(
                AuditAction.BulkOperation,
                "Multiple",
                null,
                performedBy,
                performerName,
                performerRole,
                $"Bulk operation: {operationType} performed on {itemCount} items",
                AuditSeverity.High,
                ipAddress: ipAddress,
                userAgent: userAgent,
                additionalData: data
            );
        }

        public static AuditLog CreateAccountActionLog(Guid userId, string userName, Guid performedBy,
            string performerName, string performerRole, string action, string actionResult, string reason,
            string notes, string ipAddress, string userAgent)
        {
            var additionalData = new Dictionary<string, object>
            {
                ["action"] = action,
                ["action_result"] = actionResult,
                ["reason"] = reason,
                ["notes"] = notes
            };

            return new AuditLog(
                AuditAction.StatusChange,
                "UserAccount",
                userId,
                performedBy,
                performerName,
                performerRole,
                $"Account action '{action}' performed on user {userName}. Result: {actionResult}. Reason: {reason}",
                AuditSeverity.High,
                ipAddress: ipAddress,
                userAgent: userAgent,
                additionalData: additionalData
            );
        }
    }
}
