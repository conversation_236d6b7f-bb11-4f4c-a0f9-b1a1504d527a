using CommunicationNotification.Domain.Enums;
using System.ComponentModel.DataAnnotations;

namespace CommunicationNotification.Infrastructure.Models;

/// <summary>
/// Response model for message history queries
/// </summary>
public class MessageHistoryResponse
{
    /// <summary>
    /// List of messages
    /// </summary>
    public List<MessageHistoryItem> Messages { get; set; } = new();

    /// <summary>
    /// Total count of messages
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// Current page number
    /// </summary>
    public int PageNumber { get; set; }

    /// <summary>
    /// Page size
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// Total pages
    /// </summary>
    public int TotalPages { get; set; }

    /// <summary>
    /// Whether there is a next page
    /// </summary>
    public bool HasNextPage { get; set; }

    /// <summary>
    /// Whether there is a previous page
    /// </summary>
    public bool HasPreviousPage { get; set; }
}

/// <summary>
/// Message history item
/// </summary>
public class MessageHistoryItem
{
    /// <summary>
    /// Message ID
    /// </summary>
    public Guid MessageId { get; set; }

    /// <summary>
    /// Message content
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Message subject
    /// </summary>
    public string? Subject { get; set; }

    /// <summary>
    /// Message type
    /// </summary>
    public string MessageType { get; set; } = string.Empty;

    /// <summary>
    /// Delivery channel
    /// </summary>
    public string Channel { get; set; } = string.Empty;

    /// <summary>
    /// Message status
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// Message priority
    /// </summary>
    public string Priority { get; set; } = string.Empty;

    /// <summary>
    /// Message language
    /// </summary>
    public string Language { get; set; } = string.Empty;

    /// <summary>
    /// Sent timestamp
    /// </summary>
    public DateTime SentAt { get; set; }

    /// <summary>
    /// Delivered timestamp
    /// </summary>
    public DateTime? DeliveredAt { get; set; }

    /// <summary>
    /// Read timestamp
    /// </summary>
    public DateTime? ReadAt { get; set; }

    /// <summary>
    /// External provider message ID
    /// </summary>
    public string? ExternalId { get; set; }

    /// <summary>
    /// Message tags
    /// </summary>
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// Template ID if template-based
    /// </summary>
    public string? TemplateId { get; set; }

    /// <summary>
    /// Error message if failed
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// Detailed message response
/// </summary>
public class MessageDetailResponse
{
    /// <summary>
    /// Message ID
    /// </summary>
    public Guid MessageId { get; set; }

    /// <summary>
    /// User ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Message content
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Message subject
    /// </summary>
    public string? Subject { get; set; }

    /// <summary>
    /// Message type
    /// </summary>
    public string MessageType { get; set; } = string.Empty;

    /// <summary>
    /// Delivery channel
    /// </summary>
    public string Channel { get; set; } = string.Empty;

    /// <summary>
    /// Message status
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// Message priority
    /// </summary>
    public string Priority { get; set; } = string.Empty;

    /// <summary>
    /// Message language
    /// </summary>
    public string Language { get; set; } = string.Empty;

    /// <summary>
    /// Sent timestamp
    /// </summary>
    public DateTime SentAt { get; set; }

    /// <summary>
    /// Delivered timestamp
    /// </summary>
    public DateTime? DeliveredAt { get; set; }

    /// <summary>
    /// Read timestamp
    /// </summary>
    public DateTime? ReadAt { get; set; }

    /// <summary>
    /// External provider message ID
    /// </summary>
    public string? ExternalId { get; set; }

    /// <summary>
    /// Message tags
    /// </summary>
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// Template ID if template-based
    /// </summary>
    public string? TemplateId { get; set; }

    /// <summary>
    /// Template parameters
    /// </summary>
    public Dictionary<string, object> TemplateParameters { get; set; } = new();

    /// <summary>
    /// Error message if failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Created by user ID
    /// </summary>
    public Guid CreatedBy { get; set; }

    /// <summary>
    /// Created timestamp
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Delivery attempts
    /// </summary>
    public List<DeliveryAttemptDetail> DeliveryAttempts { get; set; } = new();

    /// <summary>
    /// Audit trail
    /// </summary>
    public List<AuditTrailItem> AuditTrail { get; set; } = new();
}

/// <summary>
/// Delivery attempt detail
/// </summary>
public class DeliveryAttemptDetail
{
    /// <summary>
    /// Attempt number
    /// </summary>
    public int AttemptNumber { get; set; }

    /// <summary>
    /// Channel used for attempt
    /// </summary>
    public string Channel { get; set; } = string.Empty;

    /// <summary>
    /// Attempt timestamp
    /// </summary>
    public DateTime AttemptedAt { get; set; }

    /// <summary>
    /// Whether attempt was successful
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// Error message if failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Response time
    /// </summary>
    public TimeSpan? ResponseTime { get; set; }

    /// <summary>
    /// External provider message ID
    /// </summary>
    public string? ExternalId { get; set; }

    /// <summary>
    /// Provider response data
    /// </summary>
    public Dictionary<string, object> ProviderResponse { get; set; } = new();
}

/// <summary>
/// Audit trail item
/// </summary>
public class AuditTrailItem
{
    /// <summary>
    /// Action performed
    /// </summary>
    public string Action { get; set; } = string.Empty;

    /// <summary>
    /// Action timestamp
    /// </summary>
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// User who performed the action
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Action details
    /// </summary>
    public Dictionary<string, object> Details { get; set; } = new();

    /// <summary>
    /// IP address
    /// </summary>
    public string? IpAddress { get; set; }

    /// <summary>
    /// User agent
    /// </summary>
    public string? UserAgent { get; set; }
}

/// <summary>
/// Message action response
/// </summary>
public class MessageActionResponse
{
    /// <summary>
    /// Message ID
    /// </summary>
    public Guid MessageId { get; set; }

    /// <summary>
    /// Action performed
    /// </summary>
    public string Action { get; set; } = string.Empty;

    /// <summary>
    /// Whether action was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Action timestamp
    /// </summary>
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// Command ID for tracking
    /// </summary>
    public Guid CommandId { get; set; }

    /// <summary>
    /// Error message if failed
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// Message statistics response
/// </summary>
public class MessageStatisticsResponse
{
    /// <summary>
    /// User ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// From date
    /// </summary>
    public DateTime FromDate { get; set; }

    /// <summary>
    /// To date
    /// </summary>
    public DateTime ToDate { get; set; }

    /// <summary>
    /// Total messages
    /// </summary>
    public int TotalMessages { get; set; }

    /// <summary>
    /// Delivered messages
    /// </summary>
    public int DeliveredMessages { get; set; }

    /// <summary>
    /// Read messages
    /// </summary>
    public int ReadMessages { get; set; }

    /// <summary>
    /// Failed messages
    /// </summary>
    public int FailedMessages { get; set; }

    /// <summary>
    /// Delivery rate percentage
    /// </summary>
    public double DeliveryRate { get; set; }

    /// <summary>
    /// Read rate percentage
    /// </summary>
    public double ReadRate { get; set; }

    /// <summary>
    /// Channel breakdown
    /// </summary>
    public Dictionary<string, int> ChannelBreakdown { get; set; } = new();

    /// <summary>
    /// Message type breakdown
    /// </summary>
    public Dictionary<string, int> MessageTypeBreakdown { get; set; } = new();

    /// <summary>
    /// Daily statistics
    /// </summary>
    public List<DailyMessageStats> DailyStats { get; set; } = new();
}

/// <summary>
/// Daily message statistics
/// </summary>
public class DailyMessageStats
{
    /// <summary>
    /// Date
    /// </summary>
    public DateTime Date { get; set; }

    /// <summary>
    /// Total messages for the day
    /// </summary>
    public int TotalMessages { get; set; }

    /// <summary>
    /// Delivered messages for the day
    /// </summary>
    public int DeliveredMessages { get; set; }

    /// <summary>
    /// Read messages for the day
    /// </summary>
    public int ReadMessages { get; set; }

    /// <summary>
    /// Failed messages for the day
    /// </summary>
    public int FailedMessages { get; set; }
}

/// <summary>
/// Message search request
/// </summary>
public class MessageSearchRequest
{
    /// <summary>
    /// Search term
    /// </summary>
    [StringLength(500)]
    public string? SearchTerm { get; set; }

    /// <summary>
    /// User IDs to filter by
    /// </summary>
    public List<Guid>? UserIds { get; set; }

    /// <summary>
    /// Channels to filter by
    /// </summary>
    public List<NotificationChannel>? Channels { get; set; }

    /// <summary>
    /// Message types to filter by
    /// </summary>
    public List<MessageType>? MessageTypes { get; set; }

    /// <summary>
    /// Statuses to filter by
    /// </summary>
    public List<MessageStatus>? Statuses { get; set; }

    /// <summary>
    /// From date
    /// </summary>
    public DateTime? FromDate { get; set; }

    /// <summary>
    /// To date
    /// </summary>
    public DateTime? ToDate { get; set; }

    /// <summary>
    /// Tags to filter by
    /// </summary>
    public List<string>? Tags { get; set; }

    /// <summary>
    /// Page number
    /// </summary>
    [Range(1, int.MaxValue)]
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Page size
    /// </summary>
    [Range(1, 100)]
    public int PageSize { get; set; } = 20;

    /// <summary>
    /// Sort by field
    /// </summary>
    public string SortBy { get; set; } = "SentAt";

    /// <summary>
    /// Sort direction
    /// </summary>
    public string SortDirection { get; set; } = "DESC";
}

/// <summary>
/// Message search response
/// </summary>
public class MessageSearchResponse
{
    /// <summary>
    /// Search results
    /// </summary>
    public List<MessageSearchItem> Messages { get; set; } = new();

    /// <summary>
    /// Total count of matching messages
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// Current page number
    /// </summary>
    public int PageNumber { get; set; }

    /// <summary>
    /// Page size
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// Total pages
    /// </summary>
    public int TotalPages { get; set; }

    /// <summary>
    /// Search term used
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Search execution time
    /// </summary>
    public TimeSpan ExecutionTime { get; set; }
}

/// <summary>
/// Message search result item
/// </summary>
public class MessageSearchItem
{
    /// <summary>
    /// Message ID
    /// </summary>
    public Guid MessageId { get; set; }

    /// <summary>
    /// User ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Message content (truncated)
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Message subject
    /// </summary>
    public string? Subject { get; set; }

    /// <summary>
    /// Message type
    /// </summary>
    public string MessageType { get; set; } = string.Empty;

    /// <summary>
    /// Delivery channel
    /// </summary>
    public string Channel { get; set; } = string.Empty;

    /// <summary>
    /// Message status
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// Sent timestamp
    /// </summary>
    public DateTime SentAt { get; set; }

    /// <summary>
    /// Delivered timestamp
    /// </summary>
    public DateTime? DeliveredAt { get; set; }

    /// <summary>
    /// Message tags
    /// </summary>
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// Template ID if template-based
    /// </summary>
    public string? TemplateId { get; set; }
}
