using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OrderManagement.Domain.Entities;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Infrastructure.Persistence.Configurations;

public class RfqConfiguration : IEntityTypeConfiguration<RFQ>
{
    public void Configure(EntityTypeBuilder<RFQ> builder)
    {
        builder.ToTable("Rfqs");

        builder.HasKey(r => r.Id);

        builder.Property(r => r.Id)
            .ValueGeneratedNever();

        builder.Property(r => r.TransportCompanyId)
            .IsRequired();

        builder.Property(r => r.RfqNumber)
            .IsRequired()
            .HasMaxLength(50);

        builder.HasIndex(r => r.RfqNumber)
            .IsUnique();

        builder.Property(r => r.Title)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(r => r.Description)
            .IsRequired()
            .HasMaxLength(2000);

        builder.Property(r => r.Status)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(r => r.CreatedAt)
            .IsRequired();

        builder.Property(r => r.ExpiresAt);

        builder.Property(r => r.ClosedAt);

        builder.Property(r => r.ClosureReason)
            .HasMaxLength(500);

        builder.Property(r => r.SpecialInstructions)
            .HasMaxLength(1000);

        builder.Property(r => r.IsUrgent)
            .IsRequired();

        builder.Property(r => r.ContactPerson)
            .HasMaxLength(100);

        builder.Property(r => r.ContactPhone)
            .HasMaxLength(20);

        builder.Property(r => r.ContactEmail)
            .HasMaxLength(100);

        // Timeframe configuration
        builder.OwnsOne(r => r.Timeframe, timeframe =>
        {
            timeframe.Property(t => t.Duration)
                .IsRequired();

            timeframe.Property(t => t.Unit)
                .IsRequired()
                .HasConversion<string>();

            timeframe.Property(t => t.CreatedAt)
                .IsRequired();

            timeframe.Property(t => t.ExpiresAt)
                .IsRequired();

            timeframe.Property(t => t.AllowExtensions)
                .IsRequired();

            timeframe.Property(t => t.MaxExtensions)
                .IsRequired();

            timeframe.Property(t => t.ExtensionCount)
                .IsRequired();
        });

        // Timeframe extensions configuration
        builder.OwnsMany(r => r.TimeframeExtensions, extension =>
        {
            extension.Property(e => e.Duration)
                .IsRequired();

            extension.Property(e => e.Unit)
                .IsRequired()
                .HasConversion<string>();

            extension.Property(e => e.Reason)
                .IsRequired()
                .HasMaxLength(500);

            extension.Property(e => e.ExtendedAt)
                .IsRequired();

            extension.Property(e => e.ExtendedBy)
                .IsRequired();

            extension.Property(e => e.NewExpiresAt)
                .IsRequired();
        });

        // Configure PriceExpectations as owned entity
        builder.OwnsOne(r => r.PriceExpectations, pe =>
        {
            pe.OwnsOne(p => p.MinExpectedPrice, money =>
            {
                money.Property(m => m.Amount)
                    .HasColumnName("PriceExpectations_MinExpectedPrice_Amount")
                    .HasPrecision(18, 2);
                money.Property(m => m.Currency)
                    .HasColumnName("PriceExpectations_MinExpectedPrice_Currency")
                    .HasMaxLength(3);
            });

            pe.OwnsOne(p => p.MaxExpectedPrice, money =>
            {
                money.Property(m => m.Amount)
                    .HasColumnName("PriceExpectations_MaxExpectedPrice_Amount")
                    .HasPrecision(18, 2);
                money.Property(m => m.Currency)
                    .HasColumnName("PriceExpectations_MaxExpectedPrice_Currency")
                    .HasMaxLength(3);
            });

            pe.OwnsOne(p => p.TargetPrice, money =>
            {
                money.Property(m => m.Amount)
                    .HasColumnName("PriceExpectations_TargetPrice_Amount")
                    .HasPrecision(18, 2);
                money.Property(m => m.Currency)
                    .HasColumnName("PriceExpectations_TargetPrice_Currency")
                    .HasMaxLength(3);
            });

            pe.Property(p => p.ExpectationType)
                .HasColumnName("PriceExpectations_ExpectationType")
                .HasConversion<int>();

            pe.Property(p => p.IsFlexible)
                .HasColumnName("PriceExpectations_IsFlexible");

            pe.Property(p => p.PriceJustification)
                .HasColumnName("PriceExpectations_PriceJustification")
                .HasMaxLength(1000);

            pe.Property(p => p.PriceValidUntil)
                .HasColumnName("PriceExpectations_PriceValidUntil");

            pe.Property(p => p.AllowCounterOffers)
                .HasColumnName("PriceExpectations_AllowCounterOffers");

            pe.Property(p => p.MaxCounterOfferVariance)
                .HasColumnName("PriceExpectations_MaxCounterOfferVariance")
                .HasPrecision(5, 2);

            pe.Property(p => p.PricingNotes)
                .HasColumnName("PriceExpectations_PricingNotes")
                .HasMaxLength(2000);
        });

        // Configure ReverseAuctionSettings as owned entity
        builder.OwnsOne(r => r.ReverseAuctionSettings, ras =>
        {
            ras.Property(ra => ra.IsEnabled)
                .HasColumnName("ReverseAuction_IsEnabled");

            ras.Property(ra => ra.StartTime)
                .HasColumnName("ReverseAuction_StartTime");

            ras.Property(ra => ra.EndTime)
                .HasColumnName("ReverseAuction_EndTime");

            ras.OwnsOne(ra => ra.StartingPrice, money =>
            {
                money.Property(m => m.Amount)
                    .HasColumnName("ReverseAuction_StartingPrice_Amount")
                    .HasPrecision(18, 2);
                money.Property(m => m.Currency)
                    .HasColumnName("ReverseAuction_StartingPrice_Currency")
                    .HasMaxLength(3);
            });

            ras.OwnsOne(ra => ra.ReservePrice, money =>
            {
                money.Property(m => m.Amount)
                    .HasColumnName("ReverseAuction_ReservePrice_Amount")
                    .HasPrecision(18, 2);
                money.Property(m => m.Currency)
                    .HasColumnName("ReverseAuction_ReservePrice_Currency")
                    .HasMaxLength(3);
            });

            ras.Property(ra => ra.MinimumBidDecrement)
                .HasColumnName("ReverseAuction_MinimumBidDecrement")
                .HasPrecision(18, 2);

            ras.Property(ra => ra.MaxBidders)
                .HasColumnName("ReverseAuction_MaxBidders");

            ras.Property(ra => ra.AllowBidExtensions)
                .HasColumnName("ReverseAuction_AllowBidExtensions");

            ras.Property(ra => ra.ExtensionMinutes)
                .HasColumnName("ReverseAuction_ExtensionMinutes");

            ras.Property(ra => ra.IsPublicAuction)
                .HasColumnName("ReverseAuction_IsPublicAuction");

            ras.Property(ra => ra.AuctionRules)
                .HasColumnName("ReverseAuction_AuctionRules")
                .HasMaxLength(2000);
        });

        // Relationships
        builder.HasMany(r => r.Bids)
            .WithOne(b => b.Rfq)
            .HasForeignKey(b => b.RfqId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(r => r.Documents)
            .WithOne(d => d.Rfq)
            .HasForeignKey(d => d.RfqId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(r => r.TimelineEvents)
            .WithOne(te => te.RFQ)
            .HasForeignKey(te => te.RfqId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(r => r.RoutingHistory)
            .WithOne(rh => rh.RFQ)
            .HasForeignKey(rh => rh.RfqId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(r => r.Tags)
            .WithOne(t => t.RFQ)
            .HasForeignKey(t => t.RfqId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(r => r.MilestoneAssignments)
            .WithOne(ma => ma.RFQ)
            .HasForeignKey(ma => ma.RfqId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(r => r.BrokerComments)
            .WithOne(bc => bc.RFQ)
            .HasForeignKey(bc => bc.RfqId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(r => r.TransportCompanyId);
        builder.HasIndex(r => r.Status);
        builder.HasIndex(r => r.CreatedAt);
        builder.HasIndex(r => r.ExpiresAt);
        builder.HasIndex(r => new { r.Status, r.CreatedAt });
        builder.HasIndex(r => new { r.TransportCompanyId, r.Status });

        // Timeframe-related indexes for performance
        builder.HasIndex(r => new { r.Status, r.ExpiresAt })
            .HasDatabaseName("IX_Rfqs_Status_ExpiresAt");

        builder.HasIndex("Timeframe_ExpiresAt")
            .HasDatabaseName("IX_Rfqs_Timeframe_ExpiresAt");
    }
}
