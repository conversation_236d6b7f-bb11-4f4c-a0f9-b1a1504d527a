using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Domain.Entities;
using System.Text;
using System.Text.Json;

namespace FinancialPayment.Infrastructure.Services;

public class FinancialReportGenerator : IFinancialReportGenerator
{
    private readonly ILogger<FinancialReportGenerator> _logger;
    private readonly FinancialReportConfiguration _configuration;

    public FinancialReportGenerator(
        ILogger<FinancialReportGenerator> logger,
        IOptions<FinancialReportConfiguration> configuration)
    {
        _logger = logger;
        _configuration = configuration.Value;
    }

    public async Task<string> GenerateExcelReportAsync(FinancialReport report, FinancialMetrics metrics, RevenueBreakdown revenue, ExpenseBreakdown expenses)
    {
        _logger.LogInformation("Generating Excel financial report {ReportName}", report.ReportName);

        try
        {
            var fileName = $"{report.ReportName}_{DateTime.UtcNow:yyyyMMddHHmmss}.xlsx";
            var filePath = Path.Combine(_configuration.ReportsStoragePath, fileName);
            
            Directory.CreateDirectory(_configuration.ReportsStoragePath);

            var content = new StringBuilder();
            
            // Report Header
            content.AppendLine("FINANCIAL REPORT");
            content.AppendLine("================");
            content.AppendLine($"Report Name: {report.ReportName}");
            content.AppendLine($"Report Type: {report.ReportType}");
            content.AppendLine($"Period: {report.PeriodStart:yyyy-MM-dd} to {report.PeriodEnd:yyyy-MM-dd}");
            content.AppendLine($"Generated: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss}");
            content.AppendLine();

            // Executive Summary
            content.AppendLine("EXECUTIVE SUMMARY");
            content.AppendLine("=================");
            content.AppendLine($"Total Revenue,{metrics.TotalRevenue.Amount:N2} {metrics.TotalRevenue.Currency}");
            content.AppendLine($"Total Expenses,{metrics.TotalExpenses.Amount:N2} {metrics.TotalExpenses.Currency}");
            content.AppendLine($"Net Income,{metrics.NetIncome.Amount:N2} {metrics.NetIncome.Currency}");
            content.AppendLine($"Gross Profit,{metrics.GrossProfit.Amount:N2} {metrics.GrossProfit.Currency}");
            content.AppendLine($"Gross Profit Margin,{metrics.GrossProfitMargin:F2}%");
            content.AppendLine($"Net Profit Margin,{metrics.NetProfitMargin:F2}%");
            content.AppendLine($"Total Transactions,{metrics.TotalTransactionCount:N0}");
            content.AppendLine($"Average Transaction Value,{metrics.AverageTransactionValue.Amount:N2} {metrics.AverageTransactionValue.Currency}");
            content.AppendLine();

            // Revenue Analysis
            content.AppendLine("REVENUE ANALYSIS");
            content.AppendLine("================");
            content.AppendLine("Revenue by Payment Method:");
            foreach (var method in revenue.ByPaymentMethod)
            {
                content.AppendLine($"{method.Key},{method.Value.Amount:N2} {method.Value.Currency}");
            }
            content.AppendLine();

            content.AppendLine("Revenue by Gateway:");
            foreach (var gateway in revenue.ByGateway)
            {
                content.AppendLine($"{gateway.Key},{gateway.Value.Amount:N2} {gateway.Value.Currency}");
            }
            content.AppendLine();

            // Expense Analysis
            content.AppendLine("EXPENSE ANALYSIS");
            content.AppendLine("================");
            content.AppendLine($"Payment Processing Fees,{expenses.PaymentProcessingFees.Amount:N2} {expenses.PaymentProcessingFees.Currency}");
            content.AppendLine($"Gateway Fees,{expenses.GatewayFees.Amount:N2} {expenses.GatewayFees.Currency}");
            content.AppendLine($"Operational Expenses,{expenses.OperationalExpenses.Amount:N2} {expenses.OperationalExpenses.Currency}");
            content.AppendLine($"Technology Expenses,{expenses.TechnologyExpenses.Amount:N2} {expenses.TechnologyExpenses.Currency}");
            content.AppendLine($"Compliance Expenses,{expenses.ComplianceExpenses.Amount:N2} {expenses.ComplianceExpenses.Currency}");
            content.AppendLine($"Marketing Expenses,{expenses.MarketingExpenses.Amount:N2} {expenses.MarketingExpenses.Currency}");
            content.AppendLine();

            // Daily Revenue Trends
            content.AppendLine("DAILY REVENUE TRENDS");
            content.AppendLine("====================");
            content.AppendLine("Date,Revenue,Transactions,Commissions,Taxes");
            foreach (var daily in revenue.DailyBreakdown.Take(30)) // Last 30 days
            {
                content.AppendLine($"{daily.Date:yyyy-MM-dd},{daily.Revenue.Amount:N2},{daily.TransactionCount},{daily.Commissions.Amount:N2},{daily.Taxes.Amount:N2}");
            }

            await File.WriteAllTextAsync(filePath, content.ToString());

            _logger.LogInformation("Excel financial report generated at {FilePath}", filePath);
            return filePath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating Excel financial report");
            throw;
        }
    }

    public async Task<string> GeneratePdfReportAsync(FinancialReport report, FinancialMetrics metrics, RevenueBreakdown revenue, ExpenseBreakdown expenses)
    {
        _logger.LogInformation("Generating PDF financial report {ReportName}", report.ReportName);

        try
        {
            var fileName = $"{report.ReportName}_{DateTime.UtcNow:yyyyMMddHHmmss}.pdf";
            var filePath = Path.Combine(_configuration.ReportsStoragePath, fileName);
            
            Directory.CreateDirectory(_configuration.ReportsStoragePath);

            var content = new StringBuilder();
            
            // Report Header
            content.AppendLine("FINANCIAL REPORT");
            content.AppendLine("================");
            content.AppendLine();
            content.AppendLine($"Report Name: {report.ReportName}");
            content.AppendLine($"Report Type: {report.ReportType}");
            content.AppendLine($"Description: {report.Description}");
            content.AppendLine($"Period: {report.PeriodStart:yyyy-MM-dd} to {report.PeriodEnd:yyyy-MM-dd}");
            content.AppendLine($"Generated: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss}");
            content.AppendLine();

            // Key Performance Indicators
            content.AppendLine("KEY PERFORMANCE INDICATORS");
            content.AppendLine("===========================");
            content.AppendLine($"Total Revenue: {metrics.TotalRevenue.Amount:C} {metrics.TotalRevenue.Currency}");
            content.AppendLine($"Net Income: {metrics.NetIncome.Amount:C} {metrics.NetIncome.Currency}");
            content.AppendLine($"Gross Profit Margin: {metrics.GrossProfitMargin:F2}%");
            content.AppendLine($"Net Profit Margin: {metrics.NetProfitMargin:F2}%");
            content.AppendLine($"Total Transactions: {metrics.TotalTransactionCount:N0}");
            content.AppendLine($"Average Transaction Value: {metrics.AverageTransactionValue.Amount:C}");
            content.AppendLine($"Refund Rate: {metrics.RefundRate:F2}%");
            content.AppendLine($"Chargeback Rate: {metrics.ChargebackRate:F2}%");
            content.AppendLine();

            // Financial Summary
            content.AppendLine("FINANCIAL SUMMARY");
            content.AppendLine("=================");
            content.AppendLine($"Revenue: {metrics.TotalRevenue.Amount:C}");
            content.AppendLine($"Less: Total Expenses: ({metrics.TotalExpenses.Amount:C})");
            content.AppendLine($"Net Income: {metrics.NetIncome.Amount:C}");
            content.AppendLine();
            content.AppendLine($"Commissions Earned: {metrics.TotalCommissions.Amount:C}");
            content.AppendLine($"Taxes Collected: {metrics.TotalTaxes.Amount:C}");
            content.AppendLine($"Refunds Processed: {metrics.TotalRefunds.Amount:C}");
            content.AppendLine($"Chargebacks: {metrics.TotalChargebacks.Amount:C}");
            content.AppendLine();

            // Revenue Breakdown
            content.AppendLine("REVENUE BREAKDOWN");
            content.AppendLine("=================");
            content.AppendLine("By Payment Method:");
            foreach (var method in revenue.ByPaymentMethod)
            {
                var percentage = metrics.TotalRevenue.Amount > 0 ? (method.Value.Amount / metrics.TotalRevenue.Amount) * 100 : 0;
                content.AppendLine($"  {method.Key}: {method.Value.Amount:C} ({percentage:F1}%)");
            }
            content.AppendLine();

            content.AppendLine("By Payment Gateway:");
            foreach (var gateway in revenue.ByGateway)
            {
                var percentage = metrics.TotalRevenue.Amount > 0 ? (gateway.Value.Amount / metrics.TotalRevenue.Amount) * 100 : 0;
                content.AppendLine($"  {gateway.Key}: {gateway.Value.Amount:C} ({percentage:F1}%)");
            }
            content.AppendLine();

            // Expense Analysis
            content.AppendLine("EXPENSE ANALYSIS");
            content.AppendLine("================");
            content.AppendLine($"Payment Processing Fees: {expenses.PaymentProcessingFees.Amount:C}");
            content.AppendLine($"Gateway Fees: {expenses.GatewayFees.Amount:C}");
            content.AppendLine($"Operational Expenses: {expenses.OperationalExpenses.Amount:C}");
            content.AppendLine($"Technology Expenses: {expenses.TechnologyExpenses.Amount:C}");
            content.AppendLine($"Compliance Expenses: {expenses.ComplianceExpenses.Amount:C}");
            content.AppendLine($"Marketing Expenses: {expenses.MarketingExpenses.Amount:C}");
            content.AppendLine();

            // Monthly Trends
            if (revenue.MonthlyBreakdown.Any())
            {
                content.AppendLine("MONTHLY TRENDS");
                content.AppendLine("==============");
                foreach (var monthly in revenue.MonthlyBreakdown.TakeLast(12)) // Last 12 months
                {
                    content.AppendLine($"{monthly.Year}-{monthly.Month:D2}: {monthly.Revenue.Amount:C} ({monthly.TransactionCount:N0} transactions)");
                }
                content.AppendLine();
            }

            await File.WriteAllTextAsync(filePath, content.ToString());

            _logger.LogInformation("PDF financial report generated at {FilePath}", filePath);
            return filePath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating PDF financial report");
            throw;
        }
    }

    public async Task<string> GenerateCsvReportAsync(FinancialReport report, List<FinancialTrend> trends)
    {
        _logger.LogInformation("Generating CSV financial report {ReportName}", report.ReportName);

        try
        {
            var fileName = $"{report.ReportName}_{DateTime.UtcNow:yyyyMMddHHmmss}.csv";
            var filePath = Path.Combine(_configuration.ReportsStoragePath, fileName);
            
            Directory.CreateDirectory(_configuration.ReportsStoragePath);

            var content = new StringBuilder();
            content.AppendLine("Date,MetricType,Value,Label");
            
            foreach (var trend in trends)
            {
                content.AppendLine($"{trend.Date:yyyy-MM-dd},{trend.MetricType},{trend.Value},{trend.Label}");
            }

            await File.WriteAllTextAsync(filePath, content.ToString());

            _logger.LogInformation("CSV financial report generated at {FilePath}", filePath);
            return filePath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating CSV financial report");
            throw;
        }
    }

    public async Task<byte[]> GenerateExcelBytesAsync(FinancialReport report, FinancialMetrics metrics, RevenueBreakdown revenue, ExpenseBreakdown expenses)
    {
        try
        {
            var filePath = await GenerateExcelReportAsync(report, metrics, revenue, expenses);
            return await File.ReadAllBytesAsync(filePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating Excel bytes for report {ReportId}", report.Id);
            throw;
        }
    }

    public async Task<byte[]> GeneratePdfBytesAsync(FinancialReport report, FinancialMetrics metrics, RevenueBreakdown revenue, ExpenseBreakdown expenses)
    {
        try
        {
            var filePath = await GeneratePdfReportAsync(report, metrics, revenue, expenses);
            return await File.ReadAllBytesAsync(filePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating PDF bytes for report {ReportId}", report.Id);
            throw;
        }
    }
}
