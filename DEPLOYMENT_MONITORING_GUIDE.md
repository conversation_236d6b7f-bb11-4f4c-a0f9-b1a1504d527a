# Broker App Features - Deployment & Monitoring Guide

## Overview

This guide provides comprehensive instructions for deploying the new Broker App features across the TLI microservices architecture, including monitoring, alerting, and operational procedures.

## Pre-Deployment Checklist

### 1. Infrastructure Requirements

#### Database Requirements
- **PostgreSQL 13+** with TimescaleDB extension for analytics
- **Redis 6+** for caching and session management
- **RabbitMQ 3.8+** for message queuing
- **Minimum Storage**: Additional 50GB for new tables and indexes

#### External Service Dependencies
- **KYC Verification APIs**: PAN, GST, Aadhar verification services
- **Payment Gateway**: RazorPay integration for auto-renewals
- **OCR Services**: Azure Computer Vision or Google Vision API
- **Email/SMS Services**: For notifications and alerts

#### Resource Requirements per Service
```yaml
UserManagement:
  cpu: 2 cores
  memory: 4GB
  storage: 20GB

SubscriptionManagement:
  cpu: 2 cores
  memory: 4GB
  storage: 15GB

OrderManagement:
  cpu: 4 cores
  memory: 8GB
  storage: 30GB

FinancialPayment:
  cpu: 3 cores
  memory: 6GB
  storage: 25GB

AnalyticsBIService:
  cpu: 4 cores
  memory: 8GB
  storage: 50GB
```

### 2. Configuration Management

#### Environment Variables
```bash
# KYC Verification Services
KYC_PAN_API_ENDPOINT=https://api.panverification.com/v1/verify
KYC_PAN_API_KEY=your-pan-api-key
KYC_GST_API_ENDPOINT=https://api.gstverification.com/v1/verify
KYC_GST_API_KEY=your-gst-api-key
KYC_AADHAR_API_ENDPOINT=https://api.aadharverification.com/v1/verify
KYC_AADHAR_API_KEY=your-aadhar-api-key

# Auto-Renewal Configuration
RENEWAL_PROCESSING_INTERVAL=3600000  # 1 hour in milliseconds
RENEWAL_RETRY_DELAY=300000          # 5 minutes in milliseconds
RENEWAL_MAX_RETRIES=3

# Markup Configuration
MARKUP_TIER_LIMITS='{"basic":10,"standard":15,"premium":20,"enterprise":30}'

# Invoice Generation
INVOICE_AUTO_GENERATION_ENABLED=true
INVOICE_GENERATION_DELAY=30000      # 30 seconds delay after order confirmation

# Monitoring and Logging
SERILOG_MINIMUM_LEVEL=Information
ELASTICSEARCH_URL=https://elasticsearch.tli.com:9200
JAEGER_AGENT_HOST=jaeger-agent
JAEGER_AGENT_PORT=6831
```

## Deployment Strategy

### Phase 1: Database Migration (Week 1)

#### Step 1: Backup Current Database
```bash
# Create full backup
pg_dump -h localhost -U postgres -d tli_production > tli_backup_$(date +%Y%m%d_%H%M%S).sql

# Verify backup integrity
pg_restore --list tli_backup_$(date +%Y%m%d_%H%M%S).sql
```

#### Step 2: Apply Database Migrations
```bash
# Apply migrations in order
psql -h localhost -U postgres -d tli_production -f DATABASE_MIGRATION_SCRIPTS.sql

# Verify migration success
psql -h localhost -U postgres -d tli_production -c "SELECT version FROM schema_migrations ORDER BY applied_at DESC LIMIT 1;"
```

#### Step 3: Validate Data Integrity
```sql
-- Verify new tables exist
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('kyc_verification_history', 'subscription_renewal_attempts', 'broker_markup_history');

-- Check constraints are applied
SELECT constraint_name, constraint_type FROM information_schema.table_constraints 
WHERE table_name IN ('user_profiles', 'rfq_bids', 'subscriptions');
```

### Phase 2: Service Deployment (Week 2)

#### Deployment Order
1. **UserManagement Service** (KYC functionality)
2. **SubscriptionManagement Service** (Auto-renewal)
3. **OrderManagement Service** (Broker markup)
4. **FinancialPayment Service** (Auto-invoice generation)
5. **AnalyticsBIService** (Funnel tracking)
6. **CommunicationNotification Service** (Alert types)

#### Blue-Green Deployment Script
```bash
#!/bin/bash
# deploy-broker-features.sh

SERVICE_NAME=$1
VERSION=$2
ENVIRONMENT=$3

echo "Deploying $SERVICE_NAME version $VERSION to $ENVIRONMENT"

# Build and tag Docker image
docker build -t tli/$SERVICE_NAME:$VERSION .
docker tag tli/$SERVICE_NAME:$VERSION tli/$SERVICE_NAME:latest

# Deploy to staging slot
kubectl set image deployment/$SERVICE_NAME-staging $SERVICE_NAME=tli/$SERVICE_NAME:$VERSION

# Wait for deployment to be ready
kubectl rollout status deployment/$SERVICE_NAME-staging --timeout=300s

# Run health checks
./scripts/health-check.sh $SERVICE_NAME-staging

if [ $? -eq 0 ]; then
    echo "Health checks passed. Switching traffic to new version."
    
    # Switch traffic to new version
    kubectl patch service $SERVICE_NAME -p '{"spec":{"selector":{"version":"'$VERSION'"}}}'
    
    # Wait for traffic switch
    sleep 30
    
    # Final health check
    ./scripts/health-check.sh $SERVICE_NAME
    
    if [ $? -eq 0 ]; then
        echo "Deployment successful. Cleaning up old version."
        kubectl delete deployment $SERVICE_NAME-old
    else
        echo "Final health check failed. Rolling back."
        kubectl patch service $SERVICE_NAME -p '{"spec":{"selector":{"version":"previous"}}}'
        exit 1
    fi
else
    echo "Health checks failed. Deployment aborted."
    exit 1
fi
```

### Phase 3: Feature Flag Rollout (Week 3)

#### Feature Flag Configuration
```json
{
  "kyc_auto_verification": {
    "enabled": true,
    "rollout_percentage": 10,
    "user_types": ["Broker"],
    "regions": ["IN"]
  },
  "auto_renewal_toggle": {
    "enabled": true,
    "rollout_percentage": 25,
    "subscription_tiers": ["Premium", "Enterprise"]
  },
  "broker_markup_editing": {
    "enabled": true,
    "rollout_percentage": 50,
    "user_types": ["Broker"]
  },
  "auto_invoice_generation": {
    "enabled": true,
    "rollout_percentage": 100,
    "order_types": ["Standard", "Express"]
  }
}
```

#### Gradual Rollout Schedule
- **Week 3**: 10% of users
- **Week 4**: 25% of users
- **Week 5**: 50% of users
- **Week 6**: 75% of users
- **Week 7**: 100% of users

## Monitoring and Alerting

### 1. Application Metrics

#### KYC Verification Metrics
```csharp
// Custom metrics in UserManagement service
public class KycMetrics
{
    private static readonly Counter KycVerificationAttempts = Metrics
        .CreateCounter("kyc_verification_attempts_total", "Total KYC verification attempts", 
        new[] { "document_type", "status" });

    private static readonly Histogram KycProcessingTime = Metrics
        .CreateHistogram("kyc_processing_duration_seconds", "KYC processing time in seconds",
        new[] { "document_type", "provider" });

    private static readonly Gauge KycConfidenceScore = Metrics
        .CreateGauge("kyc_confidence_score", "KYC verification confidence score",
        new[] { "document_type" });

    public static void RecordVerificationAttempt(DocumentType docType, KycVerificationStatus status)
    {
        KycVerificationAttempts.WithLabels(docType.ToString(), status.ToString()).Inc();
    }

    public static void RecordProcessingTime(DocumentType docType, string provider, double seconds)
    {
        KycProcessingTime.WithLabels(docType.ToString(), provider).Observe(seconds);
    }
}
```

#### Auto-Renewal Metrics
```csharp
public class RenewalMetrics
{
    private static readonly Counter RenewalAttempts = Metrics
        .CreateCounter("subscription_renewal_attempts_total", "Total renewal attempts",
        new[] { "status", "tier" });

    private static readonly Histogram RenewalProcessingTime = Metrics
        .CreateHistogram("renewal_processing_duration_seconds", "Renewal processing time");

    private static readonly Gauge PendingRenewals = Metrics
        .CreateGauge("pending_renewals_count", "Number of pending renewals");
}
```

### 2. Health Checks

#### Comprehensive Health Check Endpoint
```csharp
[HttpGet("health")]
public async Task<IActionResult> GetHealth()
{
    var healthChecks = new Dictionary<string, object>
    {
        ["database"] = await CheckDatabaseHealth(),
        ["external_apis"] = await CheckExternalApiHealth(),
        ["message_queue"] = await CheckMessageQueueHealth(),
        ["cache"] = await CheckCacheHealth(),
        ["feature_flags"] = await CheckFeatureFlagHealth()
    };

    var overallHealth = healthChecks.Values.All(h => h.ToString() == "Healthy") ? "Healthy" : "Unhealthy";

    return Ok(new
    {
        status = overallHealth,
        timestamp = DateTime.UtcNow,
        checks = healthChecks,
        version = Assembly.GetExecutingAssembly().GetName().Version?.ToString()
    });
}
```

### 3. Alerting Rules

#### Prometheus Alerting Rules
```yaml
groups:
- name: broker_features_alerts
  rules:
  - alert: KycVerificationFailureRate
    expr: rate(kyc_verification_attempts_total{status="Failed"}[5m]) > 0.1
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "High KYC verification failure rate"
      description: "KYC verification failure rate is {{ $value }} per second"

  - alert: AutoRenewalProcessingDelay
    expr: pending_renewals_count > 100
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "Auto-renewal processing backlog"
      description: "{{ $value }} renewals are pending processing"

  - alert: MarkupValidationErrors
    expr: rate(markup_validation_errors_total[5m]) > 0.05
    for: 1m
    labels:
      severity: warning
    annotations:
      summary: "High markup validation error rate"

  - alert: InvoiceGenerationFailures
    expr: rate(invoice_generation_failures_total[5m]) > 0.02
    for: 2m
    labels:
      severity: critical
    annotations:
      summary: "Invoice generation failures detected"
```

### 4. Dashboard Configuration

#### Grafana Dashboard JSON
```json
{
  "dashboard": {
    "title": "Broker App Features Dashboard",
    "panels": [
      {
        "title": "KYC Verification Success Rate",
        "type": "stat",
        "targets": [
          {
            "expr": "rate(kyc_verification_attempts_total{status=\"Verified\"}[5m]) / rate(kyc_verification_attempts_total[5m]) * 100"
          }
        ]
      },
      {
        "title": "Auto-Renewal Processing",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(subscription_renewal_attempts_total[5m])",
            "legendFormat": "Renewal Rate"
          }
        ]
      },
      {
        "title": "Markup Usage by Tier",
        "type": "piechart",
        "targets": [
          {
            "expr": "sum by (tier) (markup_applications_total)"
          }
        ]
      }
    ]
  }
}
```

## Operational Procedures

### 1. Incident Response

#### KYC Verification Service Down
```bash
# Check external API status
curl -f https://api.panverification.com/health || echo "PAN API Down"

# Switch to manual verification mode
kubectl patch configmap kyc-config -p '{"data":{"fallback_mode":"manual"}}'

# Scale up manual review capacity
kubectl scale deployment manual-review-service --replicas=5
```

#### Auto-Renewal Processing Stuck
```bash
# Check renewal queue depth
kubectl exec -it rabbitmq-0 -- rabbitmqctl list_queues name messages

# Clear stuck renewals
kubectl exec -it subscription-service -- dotnet run --clear-stuck-renewals

# Restart renewal processing service
kubectl rollout restart deployment subscription-renewal-service
```

### 2. Performance Optimization

#### Database Query Optimization
```sql
-- Monitor slow queries
SELECT query, mean_time, calls 
FROM pg_stat_statements 
WHERE mean_time > 1000 
ORDER BY mean_time DESC;

-- Add missing indexes
CREATE INDEX CONCURRENTLY idx_kyc_history_user_status 
ON kyc_verification_history(user_id, status) 
WHERE status IN ('Pending', 'InProgress');
```

#### Cache Warming
```bash
# Warm up subscription tier cache
curl -X POST http://subscription-service/api/admin/cache/warm-tiers

# Warm up markup limits cache
curl -X POST http://order-service/api/admin/cache/warm-markup-limits
```

### 3. Data Maintenance

#### Cleanup Old Data
```sql
-- Archive old KYC verification history (older than 2 years)
INSERT INTO kyc_verification_history_archive 
SELECT * FROM kyc_verification_history 
WHERE attempted_at < NOW() - INTERVAL '2 years';

DELETE FROM kyc_verification_history 
WHERE attempted_at < NOW() - INTERVAL '2 years';

-- Cleanup old renewal attempts (older than 1 year)
DELETE FROM subscription_renewal_attempts 
WHERE attempted_at < NOW() - INTERVAL '1 year' 
AND status IN ('Successful', 'Failed');
```

## Security Considerations

### 1. API Security
- All endpoints require JWT authentication
- Rate limiting implemented per user and endpoint
- Input validation and sanitization
- HTTPS only communication

### 2. Data Protection
- PII data encrypted at rest
- KYC documents stored with encryption
- Audit logging for all sensitive operations
- GDPR compliance for data retention

### 3. External API Security
- API keys stored in secure key vault
- Circuit breaker pattern for external calls
- Request/response logging (excluding sensitive data)
- Regular API key rotation

## Rollback Procedures

### Emergency Rollback
```bash
#!/bin/bash
# emergency-rollback.sh

echo "Initiating emergency rollback..."

# Disable feature flags
kubectl patch configmap feature-flags -p '{"data":{"kyc_auto_verification":"false","auto_renewal_toggle":"false","broker_markup_editing":"false","auto_invoice_generation":"false"}}'

# Rollback to previous version
kubectl rollout undo deployment/user-management-service
kubectl rollout undo deployment/subscription-management-service
kubectl rollout undo deployment/order-management-service
kubectl rollout undo deployment/financial-payment-service

# Wait for rollback completion
kubectl rollout status deployment/user-management-service
kubectl rollout status deployment/subscription-management-service
kubectl rollout status deployment/order-management-service
kubectl rollout status deployment/financial-payment-service

echo "Emergency rollback completed"
```

This deployment and monitoring guide ensures a smooth rollout of the Broker App features with comprehensive observability and operational procedures.
