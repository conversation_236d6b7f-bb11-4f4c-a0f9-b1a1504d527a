using System.Reflection;
using Microsoft.Extensions.DependencyInjection;
using FluentValidation;
using OrderManagement.Application.Services;

namespace OrderManagement.Application;

public static class DependencyInjection
{
    public static IServiceCollection AddApplication(this IServiceCollection services)
    {
        services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly()));
        services.AddAutoMapper(Assembly.GetExecutingAssembly());
        services.AddValidatorsFromAssembly(Assembly.GetExecutingAssembly());

        // Add application services
        services.AddScoped<IOrderMetricsService, OrderMetricsService>();

        return services;
    }
}
