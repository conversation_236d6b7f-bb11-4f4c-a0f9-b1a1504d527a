using MediatR;
using FluentValidation;
using UserManagement.Domain.Entities;

namespace UserManagement.Application.Documents.Commands.ProcessDocumentWithOcr
{
    public class ProcessDocumentWithOcrCommand : IRequest<ProcessDocumentWithOcrResponse>
    {
        public Guid SubmissionId { get; set; }
        public DocumentType DocumentType { get; set; }
        public string FilePath { get; set; } = string.Empty;
        public decimal MinimumConfidenceThreshold { get; set; } = 0.7m;
        public bool AutoApproveIfHighConfidence { get; set; } = false;
        public decimal AutoApprovalThreshold { get; set; } = 0.9m;
    }

    public class ProcessDocumentWithOcrResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public string ExtractedText { get; set; } = string.Empty;
        public Dictionary<string, object> ExtractedData { get; set; } = new();
        public decimal ConfidenceScore { get; set; }
        public bool RequiresManualReview { get; set; }
        public bool AutoApproved { get; set; }
        public List<string> ValidationErrors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public DateTime ProcessedAt { get; set; }
    }

    public class ProcessDocumentWithOcrCommandValidator : AbstractValidator<ProcessDocumentWithOcrCommand>
    {
        public ProcessDocumentWithOcrCommandValidator()
        {
            RuleFor(x => x.SubmissionId)
                .NotEmpty()
                .WithMessage("Submission ID is required");

            RuleFor(x => x.DocumentType)
                .IsInEnum()
                .WithMessage("Valid document type is required");

            RuleFor(x => x.FilePath)
                .NotEmpty()
                .WithMessage("File path is required");

            RuleFor(x => x.MinimumConfidenceThreshold)
                .InclusiveBetween(0m, 1m)
                .WithMessage("Minimum confidence threshold must be between 0 and 1");

            RuleFor(x => x.AutoApprovalThreshold)
                .InclusiveBetween(0m, 1m)
                .WithMessage("Auto approval threshold must be between 0 and 1");

            RuleFor(x => x.AutoApprovalThreshold)
                .GreaterThanOrEqualTo(x => x.MinimumConfidenceThreshold)
                .WithMessage("Auto approval threshold must be greater than or equal to minimum confidence threshold");
        }
    }
}
