using OrderManagement.Domain.Enums;

namespace OrderManagement.Domain.ValueObjects;

/// <summary>
/// Value object representing price expectations for an RFQ
/// </summary>
public record PriceExpectations
{
    public Money? MinExpectedPrice { get; init; }
    public Money? MaxExpectedPrice { get; init; }
    public Money? TargetPrice { get; init; }
    public PriceExpectationType ExpectationType { get; init; }
    public bool IsFlexible { get; init; }
    public string? PriceJustification { get; init; }
    public DateTime? PriceValidUntil { get; init; }
    public bool AllowCounterOffers { get; init; }
    public decimal? MaxCounterOfferVariance { get; init; }
    public string? PricingNotes { get; init; }

    // Parameterless constructor for EF Core
    public PriceExpectations() { }

    public PriceExpectations(
        Money? minExpectedPrice = null,
        Money? maxExpectedPrice = null,
        Money? targetPrice = null,
        PriceExpectationType expectationType = PriceExpectationType.Range,
        bool isFlexible = true,
        string? priceJustification = null,
        DateTime? priceValidUntil = null,
        bool allowCounterOffers = true,
        decimal? maxCounterOfferVariance = null,
        string? pricingNotes = null)
    {
        // Validation
        if (minExpectedPrice != null && maxExpectedPrice != null)
        {
            if (minExpectedPrice.Currency != maxExpectedPrice.Currency)
                throw new ArgumentException("Min and max expected prices must have the same currency");
            
            if (minExpectedPrice.Amount > maxExpectedPrice.Amount)
                throw new ArgumentException("Min expected price cannot be greater than max expected price");
        }

        if (targetPrice != null && minExpectedPrice != null && targetPrice.Currency != minExpectedPrice.Currency)
            throw new ArgumentException("Target price must have the same currency as expected prices");

        if (maxCounterOfferVariance.HasValue && (maxCounterOfferVariance < 0 || maxCounterOfferVariance > 100))
            throw new ArgumentException("Counter offer variance must be between 0 and 100 percent");

        MinExpectedPrice = minExpectedPrice;
        MaxExpectedPrice = maxExpectedPrice;
        TargetPrice = targetPrice;
        ExpectationType = expectationType;
        IsFlexible = isFlexible;
        PriceJustification = priceJustification;
        PriceValidUntil = priceValidUntil;
        AllowCounterOffers = allowCounterOffers;
        MaxCounterOfferVariance = maxCounterOfferVariance;
        PricingNotes = pricingNotes;
    }

    public bool IsWithinExpectedRange(Money bidPrice)
    {
        if (bidPrice == null) return false;

        if (MinExpectedPrice != null && bidPrice.Amount < MinExpectedPrice.Amount) return false;
        if (MaxExpectedPrice != null && bidPrice.Amount > MaxExpectedPrice.Amount) return false;

        return true;
    }

    public bool IsExpired()
    {
        return PriceValidUntil.HasValue && DateTime.UtcNow > PriceValidUntil.Value;
    }

    public decimal? GetVarianceFromTarget(Money bidPrice)
    {
        if (TargetPrice == null || bidPrice == null) return null;
        if (TargetPrice.Currency != bidPrice.Currency) return null;

        var variance = ((bidPrice.Amount - TargetPrice.Amount) / TargetPrice.Amount) * 100;
        return variance;
    }

    public bool IsCounterOfferAllowed(Money counterOfferPrice)
    {
        if (!AllowCounterOffers || counterOfferPrice == null) return false;
        if (MaxCounterOfferVariance == null) return true;

        var variance = GetVarianceFromTarget(counterOfferPrice);
        if (variance == null) return true;

        return Math.Abs(variance.Value) <= MaxCounterOfferVariance.Value;
    }
}

/// <summary>
/// Value object representing reverse auction settings
/// </summary>
public record ReverseAuctionSettings
{
    public bool IsEnabled { get; init; }
    public DateTime? StartTime { get; init; }
    public DateTime? EndTime { get; init; }
    public Money? StartingPrice { get; init; }
    public Money? ReservePrice { get; init; }
    public decimal? MinimumBidDecrement { get; init; }
    public int? MaxBidders { get; init; }
    public bool AllowBidExtensions { get; init; }
    public int? ExtensionMinutes { get; init; }
    public bool IsPublicAuction { get; init; }
    public string? AuctionRules { get; init; }

    // Parameterless constructor for EF Core
    public ReverseAuctionSettings() { }

    public ReverseAuctionSettings(
        bool isEnabled,
        DateTime? startTime = null,
        DateTime? endTime = null,
        Money? startingPrice = null,
        Money? reservePrice = null,
        decimal? minimumBidDecrement = null,
        int? maxBidders = null,
        bool allowBidExtensions = true,
        int? extensionMinutes = 15,
        bool isPublicAuction = false,
        string? auctionRules = null)
    {
        if (isEnabled)
        {
            if (startTime == null || endTime == null)
                throw new ArgumentException("Start and end times are required when reverse auction is enabled");

            if (startTime >= endTime)
                throw new ArgumentException("Start time must be before end time");

            if (startingPrice != null && reservePrice != null)
            {
                if (startingPrice.Currency != reservePrice.Currency)
                    throw new ArgumentException("Starting price and reserve price must have the same currency");

                if (reservePrice.Amount > startingPrice.Amount)
                    throw new ArgumentException("Reserve price cannot be higher than starting price");
            }
        }

        IsEnabled = isEnabled;
        StartTime = startTime;
        EndTime = endTime;
        StartingPrice = startingPrice;
        ReservePrice = reservePrice;
        MinimumBidDecrement = minimumBidDecrement;
        MaxBidders = maxBidders;
        AllowBidExtensions = allowBidExtensions;
        ExtensionMinutes = extensionMinutes;
        IsPublicAuction = isPublicAuction;
        AuctionRules = auctionRules;
    }

    public bool IsActive()
    {
        if (!IsEnabled || StartTime == null || EndTime == null) return false;
        var now = DateTime.UtcNow;
        return now >= StartTime && now <= EndTime;
    }

    public bool HasStarted()
    {
        return IsEnabled && StartTime.HasValue && DateTime.UtcNow >= StartTime.Value;
    }

    public bool HasEnded()
    {
        return IsEnabled && EndTime.HasValue && DateTime.UtcNow > EndTime.Value;
    }

    public TimeSpan? GetRemainingTime()
    {
        if (!IsEnabled || EndTime == null) return null;
        var remaining = EndTime.Value - DateTime.UtcNow;
        return remaining > TimeSpan.Zero ? remaining : TimeSpan.Zero;
    }

    public bool CanExtend()
    {
        return IsEnabled && AllowBidExtensions && ExtensionMinutes.HasValue && !HasEnded();
    }

    public ReverseAuctionSettings ExtendAuction()
    {
        if (!CanExtend()) throw new InvalidOperationException("Cannot extend auction");

        return this with
        {
            EndTime = EndTime?.AddMinutes(ExtensionMinutes!.Value)
        };
    }
}
