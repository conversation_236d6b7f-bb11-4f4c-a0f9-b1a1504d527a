using CommunicationNotification.Application.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace CommunicationNotification.Infrastructure.ExternalServices;

/// <summary>
/// Background service for monitoring external service provider health
/// </summary>
public class ExternalServiceHealthCheckService : BackgroundService
{
    private readonly IExternalServiceFactory _serviceFactory;
    private readonly ILogger<ExternalServiceHealthCheckService> _logger;
    private readonly ExternalServiceHealthSettings _settings;
    private readonly Dictionary<string, ProviderHealthStatus> _healthStatuses = new();

    public ExternalServiceHealthCheckService(
        IExternalServiceFactory serviceFactory,
        IConfiguration configuration,
        ILogger<ExternalServiceHealthCheckService> logger)
    {
        _serviceFactory = serviceFactory;
        _logger = logger;
        _settings = configuration.GetSection("ExternalServiceHealth").Get<ExternalServiceHealthSettings>() ?? new();
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("External service health check service started");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await PerformHealthChecksAsync(stoppingToken);
                await Task.Delay(_settings.CheckInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in health check cycle");
                await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken); // Wait before retrying
            }
        }

        _logger.LogInformation("External service health check service stopped");
    }

    private async Task PerformHealthChecksAsync(CancellationToken cancellationToken)
    {
        _logger.LogDebug("Starting health check cycle");

        var healthStatuses = await _serviceFactory.CheckAllProvidersHealthAsync(cancellationToken);

        foreach (var status in healthStatuses)
        {
            var key = $"{status.Channel}_{status.ProviderType}";
            var previousStatus = _healthStatuses.GetValueOrDefault(key);

            _healthStatuses[key] = status;

            // Log status changes
            if (previousStatus != null && previousStatus.IsHealthy != status.IsHealthy)
            {
                if (status.IsHealthy)
                {
                    _logger.LogInformation("Provider {Provider} for {Channel} is now healthy",
                        status.ProviderType, status.Channel);
                }
                else
                {
                    _logger.LogWarning("Provider {Provider} for {Channel} is now unhealthy: {Error}",
                        status.ProviderType, status.Channel, status.ErrorMessage);
                }

                // Send alerts if configured
                if (_settings.EnableAlerts)
                {
                    await SendHealthAlertAsync(status, previousStatus, cancellationToken);
                }
            }

            // Log periodic status
            if (_settings.EnablePeriodicLogging)
            {
                _logger.LogInformation("Health check: {Provider} for {Channel} - {Status} (Response: {ResponseTime}ms)",
                    status.ProviderType, status.Channel, 
                    status.IsHealthy ? "Healthy" : "Unhealthy",
                    status.ResponseTime?.TotalMilliseconds ?? 0);
            }
        }

        // Check for degraded performance
        await CheckPerformanceDegradationAsync(healthStatuses, cancellationToken);

        _logger.LogDebug("Health check cycle completed");
    }

    private async Task CheckPerformanceDegradationAsync(
        List<ProviderHealthStatus> healthStatuses,
        CancellationToken cancellationToken)
    {
        foreach (var status in healthStatuses.Where(s => s.IsHealthy && s.ResponseTime.HasValue))
        {
            var threshold = GetResponseTimeThreshold(status.Channel);
            
            if (status.ResponseTime.Value.TotalMilliseconds > threshold)
            {
                _logger.LogWarning("Provider {Provider} for {Channel} has degraded performance: {ResponseTime}ms (threshold: {Threshold}ms)",
                    status.ProviderType, status.Channel, 
                    status.ResponseTime.Value.TotalMilliseconds, threshold);

                if (_settings.EnablePerformanceAlerts)
                {
                    await SendPerformanceAlertAsync(status, threshold, cancellationToken);
                }
            }
        }
    }

    private async Task SendHealthAlertAsync(
        ProviderHealthStatus currentStatus,
        ProviderHealthStatus previousStatus,
        CancellationToken cancellationToken)
    {
        try
        {
            var alertType = currentStatus.IsHealthy ? "Recovery" : "Failure";
            var message = $"Provider {currentStatus.ProviderType} for {currentStatus.Channel} - {alertType}";
            
            if (!currentStatus.IsHealthy)
            {
                message += $": {currentStatus.ErrorMessage}";
            }

            _logger.LogInformation("Sending health alert: {Message}", message);

            // In a real implementation, this would send alerts via configured channels
            // (email, Slack, Teams, etc.)
            await SendAlertNotificationAsync(message, AlertSeverity.High, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send health alert");
        }
    }

    private async Task SendPerformanceAlertAsync(
        ProviderHealthStatus status,
        double threshold,
        CancellationToken cancellationToken)
    {
        try
        {
            var message = $"Performance degradation detected: {status.ProviderType} for {status.Channel} " +
                         $"responded in {status.ResponseTime?.TotalMilliseconds:F0}ms (threshold: {threshold}ms)";

            _logger.LogInformation("Sending performance alert: {Message}", message);

            await SendAlertNotificationAsync(message, AlertSeverity.Medium, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send performance alert");
        }
    }

    private async Task SendAlertNotificationAsync(string message, AlertSeverity severity, CancellationToken cancellationToken)
    {
        // In a real implementation, this would integrate with notification systems
        // For now, just log the alert
        _logger.LogWarning("ALERT [{Severity}]: {Message}", severity, message);
        
        // Could integrate with:
        // - Email notifications
        // - Slack webhooks
        // - Microsoft Teams webhooks
        // - PagerDuty
        // - Custom webhook endpoints
        
        await Task.CompletedTask;
    }

    private double GetResponseTimeThreshold(NotificationChannel channel)
    {
        return channel switch
        {
            NotificationChannel.Sms => _settings.SmsResponseThresholdMs,
            NotificationChannel.Email => _settings.EmailResponseThresholdMs,
            NotificationChannel.Push => _settings.PushResponseThresholdMs,
            NotificationChannel.Voice => _settings.VoiceResponseThresholdMs,
            NotificationChannel.WhatsApp => _settings.WhatsAppResponseThresholdMs,
            _ => _settings.DefaultResponseThresholdMs
        };
    }

    public Dictionary<string, ProviderHealthStatus> GetCurrentHealthStatuses()
    {
        return new Dictionary<string, ProviderHealthStatus>(_healthStatuses);
    }

    public ProviderHealthStatus? GetProviderHealth(NotificationChannel channel, string providerType)
    {
        var key = $"{channel}_{providerType}";
        return _healthStatuses.GetValueOrDefault(key);
    }

    public bool IsProviderHealthy(NotificationChannel channel, string providerType)
    {
        var health = GetProviderHealth(channel, providerType);
        return health?.IsHealthy ?? false;
    }

    public List<ProviderHealthStatus> GetUnhealthyProviders()
    {
        return _healthStatuses.Values.Where(s => !s.IsHealthy).ToList();
    }

    public HealthSummary GetHealthSummary()
    {
        var allStatuses = _healthStatuses.Values.ToList();
        var healthyCount = allStatuses.Count(s => s.IsHealthy);
        var totalCount = allStatuses.Count;

        return new HealthSummary
        {
            TotalProviders = totalCount,
            HealthyProviders = healthyCount,
            UnhealthyProviders = totalCount - healthyCount,
            OverallHealthPercentage = totalCount > 0 ? (double)healthyCount / totalCount * 100 : 0,
            LastChecked = allStatuses.Any() ? allStatuses.Max(s => s.CheckedAt) : DateTime.MinValue,
            ProviderStatuses = allStatuses
        };
    }
}

/// <summary>
/// External service health check settings
/// </summary>
public class ExternalServiceHealthSettings
{
    public bool EnableHealthChecks { get; set; } = true;
    public TimeSpan CheckInterval { get; set; } = TimeSpan.FromMinutes(5);
    public bool EnableAlerts { get; set; } = true;
    public bool EnablePerformanceAlerts { get; set; } = true;
    public bool EnablePeriodicLogging { get; set; } = false;
    
    // Response time thresholds in milliseconds
    public double DefaultResponseThresholdMs { get; set; } = 5000;
    public double SmsResponseThresholdMs { get; set; } = 3000;
    public double EmailResponseThresholdMs { get; set; } = 5000;
    public double PushResponseThresholdMs { get; set; } = 2000;
    public double VoiceResponseThresholdMs { get; set; } = 10000;
    public double WhatsAppResponseThresholdMs { get; set; } = 4000;
    
    // Alert settings
    public List<string> AlertEmails { get; set; } = new();
    public string? SlackWebhookUrl { get; set; }
    public string? TeamsWebhookUrl { get; set; }
    public bool EnableEmailAlerts { get; set; } = true;
    public bool EnableSlackAlerts { get; set; } = false;
    public bool EnableTeamsAlerts { get; set; } = false;
    
    // Circuit breaker settings
    public int FailureThreshold { get; set; } = 5;
    public TimeSpan CircuitBreakerTimeout { get; set; } = TimeSpan.FromMinutes(5);
    public TimeSpan CircuitBreakerRetryTimeout { get; set; } = TimeSpan.FromMinutes(1);
}

/// <summary>
/// Health summary model
/// </summary>
public class HealthSummary
{
    public int TotalProviders { get; set; }
    public int HealthyProviders { get; set; }
    public int UnhealthyProviders { get; set; }
    public double OverallHealthPercentage { get; set; }
    public DateTime LastChecked { get; set; }
    public List<ProviderHealthStatus> ProviderStatuses { get; set; } = new();
}

/// <summary>
/// Alert severity levels
/// </summary>
public enum AlertSeverity
{
    Low,
    Medium,
    High,
    Critical
}
