using FluentAssertions;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Exceptions;
using SubscriptionManagement.Domain.ValueObjects;
using Xunit;

namespace SubscriptionManagement.Tests.Unit.Domain
{
    public class SubscriptionPaymentProofTests
    {
        [Fact]
        public void Create_WithValidData_ShouldCreatePaymentProof()
        {
            // Arrange
            var subscriptionId = Guid.NewGuid();
            var userId = Guid.NewGuid();
            var amount = new Money(1000, "INR");
            var paymentDate = DateTime.UtcNow.AddDays(-1);
            var proofImageUrl = "https://example.com/proof.jpg";
            var notes = "Bank transfer payment";

            // Act
            var paymentProof = SubscriptionPaymentProof.Create(
                subscriptionId, userId, amount, paymentDate, proofImageUrl, notes);

            // Assert
            paymentProof.Should().NotBeNull();
            paymentProof.SubscriptionId.Should().Be(subscriptionId);
            paymentProof.UserId.Should().Be(userId);
            paymentProof.Amount.Should().Be(amount);
            paymentProof.PaymentDate.Should().Be(paymentDate);
            paymentProof.ProofImageUrl.Should().Be(proofImageUrl);
            paymentProof.Notes.Should().Be(notes);
            paymentProof.Status.Should().Be(PaymentProofStatus.Pending);
            paymentProof.IsPending().Should().BeTrue();
        }

        [Fact]
        public void Create_WithNullAmount_ShouldThrowException()
        {
            // Arrange
            var subscriptionId = Guid.NewGuid();
            var userId = Guid.NewGuid();
            var paymentDate = DateTime.UtcNow.AddDays(-1);
            var proofImageUrl = "https://example.com/proof.jpg";

            // Act & Assert
            var action = () => SubscriptionPaymentProof.Create(
                subscriptionId, userId, null!, paymentDate, proofImageUrl);

            action.Should().Throw<SubscriptionDomainException>()
                .WithMessage("Payment amount cannot be null");
        }

        [Fact]
        public void Create_WithZeroAmount_ShouldThrowException()
        {
            // Arrange
            var subscriptionId = Guid.NewGuid();
            var userId = Guid.NewGuid();
            var amount = new Money(0, "INR");
            var paymentDate = DateTime.UtcNow.AddDays(-1);
            var proofImageUrl = "https://example.com/proof.jpg";

            // Act & Assert
            var action = () => SubscriptionPaymentProof.Create(
                subscriptionId, userId, amount, paymentDate, proofImageUrl);

            action.Should().Throw<SubscriptionDomainException>()
                .WithMessage("Payment amount must be greater than zero");
        }

        [Fact]
        public void Create_WithEmptyProofImageUrl_ShouldThrowException()
        {
            // Arrange
            var subscriptionId = Guid.NewGuid();
            var userId = Guid.NewGuid();
            var amount = new Money(1000, "INR");
            var paymentDate = DateTime.UtcNow.AddDays(-1);

            // Act & Assert
            var action = () => SubscriptionPaymentProof.Create(
                subscriptionId, userId, amount, paymentDate, string.Empty);

            action.Should().Throw<SubscriptionDomainException>()
                .WithMessage("Proof image URL cannot be empty");
        }

        [Fact]
        public void Create_WithFuturePaymentDate_ShouldThrowException()
        {
            // Arrange
            var subscriptionId = Guid.NewGuid();
            var userId = Guid.NewGuid();
            var amount = new Money(1000, "INR");
            var paymentDate = DateTime.UtcNow.AddDays(1);
            var proofImageUrl = "https://example.com/proof.jpg";

            // Act & Assert
            var action = () => SubscriptionPaymentProof.Create(
                subscriptionId, userId, amount, paymentDate, proofImageUrl);

            action.Should().Throw<SubscriptionDomainException>()
                .WithMessage("Payment date cannot be in the future");
        }

        [Fact]
        public void Verify_WithPendingStatus_ShouldUpdateStatusAndTimestamp()
        {
            // Arrange
            var paymentProof = CreateValidPaymentProof();
            var verifiedByUserId = Guid.NewGuid();
            var verificationNotes = "Payment verified successfully";

            // Act
            paymentProof.Verify(verifiedByUserId, verificationNotes);

            // Assert
            paymentProof.Status.Should().Be(PaymentProofStatus.Verified);
            paymentProof.VerifiedByUserId.Should().Be(verifiedByUserId);
            paymentProof.VerifiedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
            paymentProof.VerificationNotes.Should().Be(verificationNotes);
            paymentProof.IsVerified().Should().BeTrue();
        }

        [Fact]
        public void Verify_WithVerifiedStatus_ShouldThrowException()
        {
            // Arrange
            var paymentProof = CreateValidPaymentProof();
            var verifiedByUserId = Guid.NewGuid();
            paymentProof.Verify(verifiedByUserId);

            // Act & Assert
            var action = () => paymentProof.Verify(verifiedByUserId);

            action.Should().Throw<SubscriptionDomainException>();
        }

        [Fact]
        public void Reject_WithPendingStatus_ShouldUpdateStatusAndReason()
        {
            // Arrange
            var paymentProof = CreateValidPaymentProof();
            var rejectedByUserId = Guid.NewGuid();
            var rejectionReason = "Invalid payment proof";

            // Act
            paymentProof.Reject(rejectedByUserId, rejectionReason);

            // Assert
            paymentProof.Status.Should().Be(PaymentProofStatus.Rejected);
            paymentProof.VerifiedByUserId.Should().Be(rejectedByUserId);
            paymentProof.RejectionReason.Should().Be(rejectionReason);
            paymentProof.IsRejected().Should().BeTrue();
        }

        [Fact]
        public void Reject_WithEmptyReason_ShouldThrowException()
        {
            // Arrange
            var paymentProof = CreateValidPaymentProof();
            var rejectedByUserId = Guid.NewGuid();

            // Act & Assert
            var action = () => paymentProof.Reject(rejectedByUserId, string.Empty);

            action.Should().Throw<SubscriptionDomainException>()
                .WithMessage("Rejection reason is required");
        }

        [Fact]
        public void RequestAdditionalInfo_WithPendingStatus_ShouldUpdateStatus()
        {
            // Arrange
            var paymentProof = CreateValidPaymentProof();
            var requestedByUserId = Guid.NewGuid();
            var requestReason = "Need clearer image";

            // Act
            paymentProof.RequestAdditionalInfo(requestedByUserId, requestReason);

            // Assert
            paymentProof.Status.Should().Be(PaymentProofStatus.RequiresAdditionalInfo);
            paymentProof.VerifiedByUserId.Should().Be(requestedByUserId);
            paymentProof.VerificationNotes.Should().Be(requestReason);
            paymentProof.RequiresAdditionalInfo().Should().BeTrue();
        }

        [Fact]
        public void UpdateProofImage_WithRequiresAdditionalInfoStatus_ShouldResetToPending()
        {
            // Arrange
            var paymentProof = CreateValidPaymentProof();
            var requestedByUserId = Guid.NewGuid();
            paymentProof.RequestAdditionalInfo(requestedByUserId, "Need clearer image");
            
            var newImageUrl = "https://example.com/new-proof.jpg";
            var additionalNotes = "Updated with clearer image";

            // Act
            paymentProof.UpdateProofImage(newImageUrl, additionalNotes);

            // Assert
            paymentProof.ProofImageUrl.Should().Be(newImageUrl);
            paymentProof.Status.Should().Be(PaymentProofStatus.Pending);
            paymentProof.Notes.Should().Contain(additionalNotes);
            paymentProof.VerifiedByUserId.Should().BeNull();
            paymentProof.VerifiedAt.Should().BeNull();
        }

        [Fact]
        public void CanBeVerified_WithPendingStatus_ShouldReturnTrue()
        {
            // Arrange
            var paymentProof = CreateValidPaymentProof();

            // Act & Assert
            paymentProof.CanBeVerified().Should().BeTrue();
        }

        [Fact]
        public void CanBeVerified_WithVerifiedStatus_ShouldReturnFalse()
        {
            // Arrange
            var paymentProof = CreateValidPaymentProof();
            paymentProof.Verify(Guid.NewGuid());

            // Act & Assert
            paymentProof.CanBeVerified().Should().BeFalse();
        }

        [Fact]
        public void GetStatusDescription_ShouldReturnCorrectDescription()
        {
            // Arrange
            var paymentProof = CreateValidPaymentProof();

            // Act & Assert
            paymentProof.GetStatusDescription().Should().Be("Payment proof is pending review");

            paymentProof.Verify(Guid.NewGuid());
            paymentProof.GetStatusDescription().Should().Be("Payment proof has been verified");
        }

        private SubscriptionPaymentProof CreateValidPaymentProof()
        {
            return SubscriptionPaymentProof.Create(
                Guid.NewGuid(),
                Guid.NewGuid(),
                new Money(1000, "INR"),
                DateTime.UtcNow.AddDays(-1),
                "https://example.com/proof.jpg",
                "Test payment proof");
        }
    }
}
