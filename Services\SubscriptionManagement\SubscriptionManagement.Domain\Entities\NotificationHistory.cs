using Shared.Domain.Common;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Exceptions;

namespace SubscriptionManagement.Domain.Entities
{
    /// <summary>
    /// Represents the history and tracking of sent notifications
    /// </summary>
    public class NotificationHistory : BaseEntity
    {
        public Guid? SubscriptionId { get; private set; }
        public Guid? UserId { get; private set; }
        public NotificationType Type { get; private set; }
        public string Channel { get; private set; }
        public string Subject { get; private set; }
        public string Body { get; private set; }
        public string Status { get; private set; } // Pending, Sent, Delivered, Failed, Cancelled
        public string? ExternalNotificationId { get; private set; }
        public Guid TriggeredByUserId { get; private set; }
        public DateTime? ScheduledAt { get; private set; }
        public DateTime? SentAt { get; private set; }
        public DateTime? DeliveredAt { get; private set; }
        public DateTime? ReadAt { get; private set; }
        public DateTime? ClickedAt { get; private set; }
        public string? ErrorMessage { get; private set; }
        public string? ErrorCode { get; private set; }
        public int RetryCount { get; private set; }
        public DateTime? LastRetryAt { get; private set; }
        public Dictionary<string, string> Metadata { get; private set; }
        public Dictionary<string, string> DeliveryMetadata { get; private set; }

        private NotificationHistory()
        {
            Channel = string.Empty;
            Subject = string.Empty;
            Body = string.Empty;
            Status = "Pending";
            Metadata = new Dictionary<string, string>();
            DeliveryMetadata = new Dictionary<string, string>();
        }

        public NotificationHistory(
            Guid? subscriptionId,
            Guid? userId,
            NotificationType type,
            string channel,
            string subject,
            string body,
            Guid triggeredByUserId,
            DateTime? scheduledAt = null,
            Dictionary<string, string>? metadata = null)
        {
            if (string.IsNullOrWhiteSpace(channel))
                throw new SubscriptionDomainException("Channel cannot be empty");

            if (string.IsNullOrWhiteSpace(body))
                throw new SubscriptionDomainException("Body cannot be empty");

            if (!subscriptionId.HasValue && !userId.HasValue)
                throw new SubscriptionDomainException("Either SubscriptionId or UserId must be provided");

            SubscriptionId = subscriptionId;
            UserId = userId;
            Type = type;
            Channel = channel;
            Subject = subject ?? string.Empty;
            Body = body;
            Status = "Pending";
            TriggeredByUserId = triggeredByUserId;
            ScheduledAt = scheduledAt;
            Metadata = metadata ?? new Dictionary<string, string>();
            DeliveryMetadata = new Dictionary<string, string>();
            RetryCount = 0;
        }

        public static NotificationHistory Create(
            Guid? subscriptionId,
            Guid? userId,
            NotificationType type,
            string channel,
            string subject,
            string body,
            Guid triggeredByUserId,
            DateTime? scheduledAt = null,
            Dictionary<string, string>? metadata = null)
        {
            return new NotificationHistory(subscriptionId, userId, type, channel, subject, body, triggeredByUserId, scheduledAt, metadata);
        }

        public void MarkAsSent(string? externalNotificationId = null)
        {
            if (Status == "Cancelled")
                throw new SubscriptionDomainException("Cannot mark cancelled notification as sent");

            Status = "Sent";
            SentAt = DateTime.UtcNow;
            ExternalNotificationId = externalNotificationId;
            SetUpdatedAt();
        }

        public void MarkAsDelivered(Dictionary<string, string>? deliveryMetadata = null)
        {
            if (Status != "Sent")
                throw new SubscriptionDomainException("Only sent notifications can be marked as delivered");

            Status = "Delivered";
            DeliveredAt = DateTime.UtcNow;
            
            if (deliveryMetadata != null)
            {
                foreach (var kv in deliveryMetadata)
                {
                    DeliveryMetadata[kv.Key] = kv.Value;
                }
            }
            
            SetUpdatedAt();
        }

        public void MarkAsRead()
        {
            if (Status != "Delivered")
                throw new SubscriptionDomainException("Only delivered notifications can be marked as read");

            ReadAt = DateTime.UtcNow;
            SetUpdatedAt();
        }

        public void MarkAsClicked()
        {
            if (Status != "Delivered")
                throw new SubscriptionDomainException("Only delivered notifications can be marked as clicked");

            ClickedAt = DateTime.UtcNow;
            SetUpdatedAt();
        }

        public void MarkAsFailed(string errorMessage, string? errorCode = null)
        {
            Status = "Failed";
            ErrorMessage = errorMessage;
            ErrorCode = errorCode;
            SetUpdatedAt();
        }

        public void MarkAsCancelled()
        {
            if (Status == "Sent" || Status == "Delivered")
                throw new SubscriptionDomainException("Cannot cancel notification that has already been sent or delivered");

            Status = "Cancelled";
            SetUpdatedAt();
        }

        public void IncrementRetryCount()
        {
            RetryCount++;
            LastRetryAt = DateTime.UtcNow;
            SetUpdatedAt();
        }

        public void AddMetadata(string key, string value)
        {
            if (string.IsNullOrWhiteSpace(key))
                throw new SubscriptionDomainException("Metadata key cannot be empty");

            Metadata[key] = value ?? string.Empty;
            SetUpdatedAt();
        }

        public void AddDeliveryMetadata(string key, string value)
        {
            if (string.IsNullOrWhiteSpace(key))
                throw new SubscriptionDomainException("Delivery metadata key cannot be empty");

            DeliveryMetadata[key] = value ?? string.Empty;
            SetUpdatedAt();
        }

        public bool IsPending() => Status == "Pending";
        public bool IsSent() => Status == "Sent";
        public bool IsDelivered() => Status == "Delivered";
        public bool IsFailed() => Status == "Failed";
        public bool IsCancelled() => Status == "Cancelled";
        public bool IsRead() => ReadAt.HasValue;
        public bool IsClicked() => ClickedAt.HasValue;

        public bool IsScheduled() => ScheduledAt.HasValue && ScheduledAt.Value > DateTime.UtcNow;
        public bool IsOverdue() => ScheduledAt.HasValue && ScheduledAt.Value <= DateTime.UtcNow && IsPending();

        public TimeSpan? GetDeliveryTime()
        {
            if (SentAt.HasValue && DeliveredAt.HasValue)
                return DeliveredAt.Value - SentAt.Value;
            return null;
        }

        public TimeSpan? GetReadTime()
        {
            if (DeliveredAt.HasValue && ReadAt.HasValue)
                return ReadAt.Value - DeliveredAt.Value;
            return null;
        }

        public TimeSpan? GetClickTime()
        {
            if (DeliveredAt.HasValue && ClickedAt.HasValue)
                return ClickedAt.Value - DeliveredAt.Value;
            return null;
        }

        public string GetStatusDescription()
        {
            return Status switch
            {
                "Pending" => IsScheduled() ? $"Scheduled for {ScheduledAt:yyyy-MM-dd HH:mm}" : "Pending",
                "Sent" => $"Sent at {SentAt:yyyy-MM-dd HH:mm}",
                "Delivered" => $"Delivered at {DeliveredAt:yyyy-MM-dd HH:mm}",
                "Failed" => $"Failed: {ErrorMessage}",
                "Cancelled" => "Cancelled",
                _ => "Unknown status"
            };
        }

        public bool CanRetry()
        {
            return IsFailed() && RetryCount < 3; // Max 3 retries
        }

        public bool ShouldRetry()
        {
            if (!CanRetry()) return false;
            
            // Exponential backoff: 1 min, 5 min, 15 min
            var backoffMinutes = RetryCount switch
            {
                0 => 1,
                1 => 5,
                2 => 15,
                _ => 60
            };

            return LastRetryAt == null || LastRetryAt.Value.AddMinutes(backoffMinutes) <= DateTime.UtcNow;
        }
    }
}
