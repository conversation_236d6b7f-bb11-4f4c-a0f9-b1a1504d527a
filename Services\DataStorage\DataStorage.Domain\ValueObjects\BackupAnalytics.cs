using DataStorage.Domain.Enums;

namespace DataStorage.Domain.ValueObjects;

public class BackupVerificationResult
{
    public bool IsSuccessful { get; private set; }
    public string? ErrorMessage { get; private set; }
    public Guid BackupId { get; private set; }
    public DateTime VerifiedAt { get; private set; }
    public BackupVerificationStatus Status { get; private set; }
    public List<BackupVerificationCheck> Checks { get; private set; }
    public BackupIntegrityScore IntegrityScore { get; private set; }

    private BackupVerificationResult()
    {
        Checks = new List<BackupVerificationCheck>();
        IntegrityScore = new BackupIntegrityScore();
    }

    public static BackupVerificationResult Success(
        Guid backupId,
        DateTime verifiedAt,
        BackupVerificationStatus status,
        List<BackupVerificationCheck> checks,
        BackupIntegrityScore integrityScore)
    {
        return new BackupVerificationResult
        {
            IsSuccessful = true,
            BackupId = backupId,
            VerifiedAt = verifiedAt,
            Status = status,
            Checks = checks ?? new List<BackupVerificationCheck>(),
            IntegrityScore = integrityScore ?? new BackupIntegrityScore()
        };
    }

    public static BackupVerificationResult Failure(
        Guid backupId,
        string errorMessage,
        DateTime verifiedAt,
        List<BackupVerificationCheck> checks)
    {
        return new BackupVerificationResult
        {
            IsSuccessful = false,
            BackupId = backupId,
            ErrorMessage = errorMessage,
            VerifiedAt = verifiedAt,
            Status = BackupVerificationStatus.VerificationFailed,
            Checks = checks ?? new List<BackupVerificationCheck>(),
            IntegrityScore = new BackupIntegrityScore()
        };
    }
}

public class BackupVerificationCheck
{
    public BackupVerificationCheckType Type { get; private set; }
    public string Name { get; private set; }
    public BackupVerificationCheckStatus Status { get; private set; }
    public string? ErrorMessage { get; private set; }
    public Dictionary<string, object> Results { get; private set; }

    public BackupVerificationCheck(
        BackupVerificationCheckType type,
        string name,
        BackupVerificationCheckStatus status,
        string? errorMessage = null,
        Dictionary<string, object>? results = null)
    {
        Type = type;
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Status = status;
        ErrorMessage = errorMessage;
        Results = results ?? new Dictionary<string, object>();
    }
}

public class BackupIntegrityScore
{
    public double OverallScore { get; private set; }
    public double ChecksumScore { get; private set; }
    public double CompletenessScore { get; private set; }
    public double AccessibilityScore { get; private set; }
    public BackupIntegrityLevel Level { get; private set; }

    public BackupIntegrityScore(
        double overallScore = 0,
        double checksumScore = 0,
        double completenessScore = 0,
        double accessibilityScore = 0)
    {
        OverallScore = Math.Max(0, Math.Min(100, overallScore));
        ChecksumScore = Math.Max(0, Math.Min(100, checksumScore));
        CompletenessScore = Math.Max(0, Math.Min(100, completenessScore));
        AccessibilityScore = Math.Max(0, Math.Min(100, accessibilityScore));
        Level = CalculateIntegrityLevel(OverallScore);
    }

    private static BackupIntegrityLevel CalculateIntegrityLevel(double score)
    {
        return score switch
        {
            >= 95 => BackupIntegrityLevel.Excellent,
            >= 85 => BackupIntegrityLevel.Good,
            >= 70 => BackupIntegrityLevel.Fair,
            >= 50 => BackupIntegrityLevel.Poor,
            _ => BackupIntegrityLevel.Critical
        };
    }
}

public class BackupHealthReport
{
    public DateTime GeneratedAt { get; private set; }
    public BackupHealthStatus OverallHealth { get; private set; }
    public BackupHealthSummary Summary { get; private set; }
    public List<BackupHealthIssue> Issues { get; private set; }
    public List<BackupHealthRecommendation> Recommendations { get; private set; }
    public BackupHealthTrends Trends { get; private set; }

    public BackupHealthReport(
        BackupHealthStatus overallHealth,
        BackupHealthSummary summary,
        List<BackupHealthIssue> issues,
        List<BackupHealthRecommendation> recommendations,
        BackupHealthTrends trends)
    {
        GeneratedAt = DateTime.UtcNow;
        OverallHealth = overallHealth;
        Summary = summary ?? throw new ArgumentNullException(nameof(summary));
        Issues = issues ?? new List<BackupHealthIssue>();
        Recommendations = recommendations ?? new List<BackupHealthRecommendation>();
        Trends = trends ?? throw new ArgumentNullException(nameof(trends));
    }
}

public class BackupHealthSummary
{
    public int TotalBackups { get; private set; }
    public int HealthyBackups { get; private set; }
    public int WarningBackups { get; private set; }
    public int CriticalBackups { get; private set; }
    public double HealthPercentage { get; private set; }
    public long TotalBackupSize { get; private set; }
    public DateTime LastSuccessfulBackup { get; private set; }

    public BackupHealthSummary(
        int totalBackups,
        int healthyBackups,
        int warningBackups,
        int criticalBackups,
        long totalBackupSize,
        DateTime lastSuccessfulBackup)
    {
        TotalBackups = totalBackups;
        HealthyBackups = healthyBackups;
        WarningBackups = warningBackups;
        CriticalBackups = criticalBackups;
        HealthPercentage = totalBackups > 0 ? (double)healthyBackups / totalBackups * 100 : 0;
        TotalBackupSize = totalBackupSize;
        LastSuccessfulBackup = lastSuccessfulBackup;
    }
}

public class BackupHealthIssue
{
    public Guid IssueId { get; private set; }
    public BackupHealthIssueType Type { get; private set; }
    public BackupHealthIssueSeverity Severity { get; private set; }
    public string Title { get; private set; }
    public string Description { get; private set; }
    public List<Guid> AffectedBackups { get; private set; }
    public DateTime DetectedAt { get; private set; }
    public string? Resolution { get; private set; }

    public BackupHealthIssue(
        Guid issueId,
        BackupHealthIssueType type,
        BackupHealthIssueSeverity severity,
        string title,
        string description,
        List<Guid> affectedBackups,
        DateTime detectedAt,
        string? resolution = null)
    {
        IssueId = issueId;
        Type = type;
        Severity = severity;
        Title = title ?? throw new ArgumentNullException(nameof(title));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        AffectedBackups = affectedBackups ?? new List<Guid>();
        DetectedAt = detectedAt;
        Resolution = resolution;
    }
}

public class BackupHealthRecommendation
{
    public string Title { get; private set; }
    public string Description { get; private set; }
    public BackupHealthRecommendationPriority Priority { get; private set; }
    public List<string> ActionItems { get; private set; }
    public TimeSpan EstimatedImplementationTime { get; private set; }

    public BackupHealthRecommendation(
        string title,
        string description,
        BackupHealthRecommendationPriority priority,
        List<string> actionItems,
        TimeSpan estimatedImplementationTime)
    {
        Title = title ?? throw new ArgumentNullException(nameof(title));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        Priority = priority;
        ActionItems = actionItems ?? new List<string>();
        EstimatedImplementationTime = estimatedImplementationTime;
    }
}

public class BackupHealthTrends
{
    public List<BackupHealthTrendPoint> HealthTrend { get; private set; }
    public List<BackupHealthTrendPoint> SizeTrend { get; private set; }
    public List<BackupHealthTrendPoint> SuccessRateTrend { get; private set; }

    public BackupHealthTrends(
        List<BackupHealthTrendPoint> healthTrend,
        List<BackupHealthTrendPoint> sizeTrend,
        List<BackupHealthTrendPoint> successRateTrend)
    {
        HealthTrend = healthTrend ?? new List<BackupHealthTrendPoint>();
        SizeTrend = sizeTrend ?? new List<BackupHealthTrendPoint>();
        SuccessRateTrend = successRateTrend ?? new List<BackupHealthTrendPoint>();
    }
}

public class BackupHealthTrendPoint
{
    public DateTime Date { get; private set; }
    public double Value { get; private set; }

    public BackupHealthTrendPoint(DateTime date, double value)
    {
        Date = date;
        Value = value;
    }
}

public class BackupAnalytics
{
    public TimeSpan Period { get; private set; }
    public BackupAnalyticsSummary Summary { get; private set; }
    public List<BackupTrend> Trends { get; private set; }
    public BackupPerformanceMetrics Performance { get; private set; }
    public Dictionary<BackupType, BackupTypeAnalytics> AnalyticsByType { get; private set; }

    public BackupAnalytics(
        TimeSpan period,
        BackupAnalyticsSummary summary,
        List<BackupTrend> trends,
        BackupPerformanceMetrics performance,
        Dictionary<BackupType, BackupTypeAnalytics> analyticsByType)
    {
        Period = period;
        Summary = summary ?? throw new ArgumentNullException(nameof(summary));
        Trends = trends ?? new List<BackupTrend>();
        Performance = performance ?? throw new ArgumentNullException(nameof(performance));
        AnalyticsByType = analyticsByType ?? new Dictionary<BackupType, BackupTypeAnalytics>();
    }
}

public class BackupAnalyticsSummary
{
    public int TotalBackups { get; private set; }
    public int SuccessfulBackups { get; private set; }
    public int FailedBackups { get; private set; }
    public long TotalDataBackedUp { get; private set; }
    public double AverageBackupTime { get; private set; }
    public double SuccessRate { get; private set; }

    public BackupAnalyticsSummary(
        int totalBackups,
        int successfulBackups,
        int failedBackups,
        long totalDataBackedUp,
        double averageBackupTime,
        double successRate)
    {
        TotalBackups = totalBackups;
        SuccessfulBackups = successfulBackups;
        FailedBackups = failedBackups;
        TotalDataBackedUp = totalDataBackedUp;
        AverageBackupTime = averageBackupTime;
        SuccessRate = Math.Max(0, Math.Min(100, successRate));
    }
}

public class BackupTrend
{
    public DateTime Date { get; private set; }
    public int BackupCount { get; private set; }
    public long DataSize { get; private set; }
    public double SuccessRate { get; private set; }
    public double AverageTime { get; private set; }

    public BackupTrend(
        DateTime date,
        int backupCount,
        long dataSize,
        double successRate,
        double averageTime)
    {
        Date = date;
        BackupCount = backupCount;
        DataSize = dataSize;
        SuccessRate = Math.Max(0, Math.Min(100, successRate));
        AverageTime = averageTime;
    }
}

public class BackupPerformanceMetrics
{
    public double AverageBackupSpeed { get; private set; }
    public double AverageCompressionRatio { get; private set; }
    public double AverageDeduplicationRatio { get; private set; }
    public TimeSpan AverageRecoveryTime { get; private set; }

    public BackupPerformanceMetrics(
        double averageBackupSpeed,
        double averageCompressionRatio,
        double averageDeduplicationRatio,
        TimeSpan averageRecoveryTime)
    {
        AverageBackupSpeed = averageBackupSpeed;
        AverageCompressionRatio = averageCompressionRatio;
        AverageDeduplicationRatio = averageDeduplicationRatio;
        AverageRecoveryTime = averageRecoveryTime;
    }
}

public class BackupTypeAnalytics
{
    public BackupType Type { get; private set; }
    public int Count { get; private set; }
    public long TotalSize { get; private set; }
    public double SuccessRate { get; private set; }
    public TimeSpan AverageTime { get; private set; }

    public BackupTypeAnalytics(
        BackupType type,
        int count,
        long totalSize,
        double successRate,
        TimeSpan averageTime)
    {
        Type = type;
        Count = count;
        TotalSize = totalSize;
        SuccessRate = Math.Max(0, Math.Min(100, successRate));
        AverageTime = averageTime;
    }
}

public class BackupMetrics
{
    public DateTime Date { get; private set; }
    public BackupType? Type { get; private set; }
    public int BackupCount { get; private set; }
    public long TotalSizeBytes { get; private set; }
    public double SuccessRate { get; private set; }
    public TimeSpan AverageTime { get; private set; }

    public BackupMetrics(
        DateTime date,
        int backupCount,
        long totalSizeBytes,
        double successRate,
        TimeSpan averageTime,
        BackupType? type = null)
    {
        Date = date;
        Type = type;
        BackupCount = backupCount;
        TotalSizeBytes = totalSizeBytes;
        SuccessRate = Math.Max(0, Math.Min(100, successRate));
        AverageTime = averageTime;
    }
}

public class BackupMetricsRequest
{
    public DateTime StartDate { get; private set; }
    public DateTime EndDate { get; private set; }
    public BackupType? Type { get; private set; }
    public MetricsGroupBy GroupBy { get; private set; }

    public BackupMetricsRequest(
        DateTime startDate,
        DateTime endDate,
        MetricsGroupBy groupBy = MetricsGroupBy.Day,
        BackupType? type = null)
    {
        StartDate = startDate;
        EndDate = endDate;
        GroupBy = groupBy;
        Type = type;
    }
}

public class BackupAlert
{
    public Guid AlertId { get; private set; }
    public BackupAlertType Type { get; private set; }
    public BackupAlertSeverity Severity { get; private set; }
    public string Title { get; private set; }
    public string Description { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? AcknowledgedAt { get; private set; }
    public Guid? AcknowledgedBy { get; private set; }
    public string? AcknowledgmentNotes { get; private set; }
    public BackupAlertStatus Status { get; private set; }
    public List<Guid> AffectedBackups { get; private set; }

    public BackupAlert(
        Guid alertId,
        BackupAlertType type,
        BackupAlertSeverity severity,
        string title,
        string description,
        DateTime createdAt,
        BackupAlertStatus status,
        List<Guid>? affectedBackups = null,
        DateTime? acknowledgedAt = null,
        Guid? acknowledgedBy = null,
        string? acknowledgmentNotes = null)
    {
        AlertId = alertId;
        Type = type;
        Severity = severity;
        Title = title ?? throw new ArgumentNullException(nameof(title));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        CreatedAt = createdAt;
        AcknowledgedAt = acknowledgedAt;
        AcknowledgedBy = acknowledgedBy;
        AcknowledgmentNotes = acknowledgmentNotes;
        Status = status;
        AffectedBackups = affectedBackups ?? new List<Guid>();
    }
}
