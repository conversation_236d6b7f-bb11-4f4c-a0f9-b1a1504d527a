using Microsoft.Extensions.Logging;
using MonitoringObservability.Domain.Entities;
using MonitoringObservability.Domain.Enums;
using MonitoringObservability.Domain.Interfaces;
using System.Collections.Concurrent;

namespace MonitoringObservability.Infrastructure.Services;

/// <summary>
/// AI-based anomaly detection service
/// </summary>
public class AnomalyDetectionService : IAnomalyDetectionService
{
    private readonly IAnomalyDetectionRepository _anomalyRepository;
    private readonly IMetricRepository _metricRepository;
    private readonly IAlertRepository _alertRepository;
    private readonly ILogger<AnomalyDetectionService> _logger;
    
    // Cache for trained models
    private readonly ConcurrentDictionary<Guid, AnomalyDetectionModel> _trainedModels = new();
    private readonly ConcurrentDictionary<string, List<double>> _metricBaselines = new();

    public AnomalyDetectionService(
        IAnomalyDetectionRepository anomalyRepository,
        IMetricRepository metricRepository,
        IAlertRepository alertRepository,
        ILogger<AnomalyDetectionService> logger)
    {
        _anomalyRepository = anomalyRepository ?? throw new ArgumentNullException(nameof(anomalyRepository));
        _metricRepository = metricRepository ?? throw new ArgumentNullException(nameof(metricRepository));
        _alertRepository = alertRepository ?? throw new ArgumentNullException(nameof(alertRepository));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task InitializeAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Initializing anomaly detection service");

            // Load trained models
            var trainedModels = await _anomalyRepository.GetTrainedModelsAsync(cancellationToken);
            foreach (var model in trainedModels)
            {
                _trainedModels.TryAdd(model.Id, model);
            }

            _logger.LogInformation("Loaded {ModelCount} trained anomaly detection models", _trainedModels.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize anomaly detection service");
            throw;
        }
    }

    public async Task<AnomalyDetectionModel> CreateModelAsync(string name, string serviceName, string metricName,
        AnomalyDetectionAlgorithm algorithm, Dictionary<string, object>? parameters = null, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating anomaly detection model {ModelName} for {ServiceName}.{MetricName}",
                name, serviceName, metricName);

            var model = new AnomalyDetectionModel(name, serviceName, metricName, algorithm, parameters);
            
            await _anomalyRepository.AddModelAsync(model, cancellationToken);
            
            _logger.LogInformation("Created anomaly detection model {ModelId}", model.Id);
            return model;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create anomaly detection model {ModelName}", name);
            throw;
        }
    }

    public async Task<bool> TrainModelAsync(Guid modelId, TimeSpan? trainingPeriod = null, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var model = await _anomalyRepository.GetModelByIdAsync(modelId, cancellationToken);
            if (model == null)
            {
                _logger.LogWarning("Model {ModelId} not found for training", modelId);
                return false;
            }

            _logger.LogInformation("Training anomaly detection model {ModelId} using {Algorithm}",
                modelId, model.Algorithm);

            var sessionId = Guid.NewGuid();
            var period = trainingPeriod ?? model.TrainingWindow;
            
            // Get training data
            var trainingData = await GetTrainingDataAsync(model.ServiceName, model.MetricName, period, cancellationToken);
            
            if (trainingData.Count < model.MinDataPoints)
            {
                _logger.LogWarning("Insufficient training data for model {ModelId}. Required: {Required}, Available: {Available}",
                    modelId, model.MinDataPoints, trainingData.Count);
                return false;
            }

            // Start training session
            model.StartTraining(sessionId, trainingData.Count);
            await _anomalyRepository.UpdateModelAsync(model, cancellationToken);

            // Train the model based on algorithm
            var trainingResult = await TrainModelWithAlgorithmAsync(model, trainingData, cancellationToken);
            
            // Complete training session
            model.CompleteTraining(sessionId, trainingResult.Success, trainingResult.Accuracy, 
                trainingResult.Precision, trainingResult.Recall, trainingResult.ErrorMessage);
            
            await _anomalyRepository.UpdateModelAsync(model, cancellationToken);

            if (trainingResult.Success)
            {
                _trainedModels.TryAdd(modelId, model);
                _logger.LogInformation("Successfully trained model {ModelId} with accuracy {Accuracy:F2}",
                    modelId, trainingResult.Accuracy);
            }
            else
            {
                _logger.LogError("Failed to train model {ModelId}: {Error}", modelId, trainingResult.ErrorMessage);
            }

            return trainingResult.Success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error training model {ModelId}", modelId);
            return false;
        }
    }

    public async Task<List<AnomalyDetectionResult>> DetectAnomaliesAsync(
        Dictionary<string, double> metrics, DateTime timestamp, CancellationToken cancellationToken = default)
    {
        try
        {
            var results = new List<AnomalyDetectionResult>();

            foreach (var metric in metrics)
            {
                var applicableModels = _trainedModels.Values
                    .Where(m => m.ShouldDetect() && m.MetricName == metric.Key)
                    .ToList();

                foreach (var model in applicableModels)
                {
                    var result = await DetectAnomalyForModelAsync(model, metric.Value, timestamp, cancellationToken);
                    if (result != null)
                    {
                        results.Add(result);
                        
                        // Record result in model
                        model.RecordDetectionResult(result);
                        await _anomalyRepository.UpdateModelAsync(model, cancellationToken);
                        
                        // Create alert if anomaly detected
                        if (result.IsAnomaly)
                        {
                            await CreateAnomalyAlertAsync(model, result, cancellationToken);
                        }
                    }
                }
            }

            _logger.LogDebug("Detected {AnomalyCount} anomalies from {MetricCount} metrics",
                results.Count(r => r.IsAnomaly), metrics.Count);

            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error detecting anomalies");
            throw;
        }
    }

    public async Task<List<AnomalyDetectionResult>> GetAnomaliesAsync(string? serviceName = null, 
        string? metricName = null, TimeSpan? period = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var fromDate = period.HasValue ? DateTime.UtcNow.Subtract(period.Value) : (DateTime?)null;
            
            var results = await _anomalyRepository.GetAnomaliesAsync(serviceName, metricName, fromDate, cancellationToken);
            
            return results.ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting anomalies");
            throw;
        }
    }

    public async Task<AnomalyStatisticsDto> GetAnomalyStatisticsAsync(string? serviceName = null, 
        TimeSpan? period = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var fromDate = period.HasValue ? DateTime.UtcNow.Subtract(period.Value) : DateTime.UtcNow.AddDays(-7);
            
            var anomalies = await _anomalyRepository.GetAnomaliesAsync(serviceName, null, fromDate, cancellationToken);
            var models = await _anomalyRepository.GetModelsByServiceAsync(serviceName, cancellationToken);

            var statistics = new AnomalyStatisticsDto
            {
                ServiceName = serviceName,
                Period = period ?? TimeSpan.FromDays(7),
                TotalAnomalies = anomalies.Count(),
                ConfirmedAnomalies = anomalies.Count(a => a.IsConfirmed),
                FalsePositives = anomalies.Count(a => a.IsFalsePositive),
                UnreviewedAnomalies = anomalies.Count(a => !a.IsConfirmed && !a.IsFalsePositive),
                AverageAnomalyScore = anomalies.Any() ? anomalies.Average(a => a.AnomalyScore) : 0,
                TopAnomalousMetrics = GetTopAnomalousMetrics(anomalies),
                AnomaliesByHour = GetAnomaliesByHour(anomalies),
                ModelAccuracy = models.Where(m => m.Accuracy.HasValue).Average(m => m.Accuracy.Value),
                GeneratedAt = DateTime.UtcNow
            };

            return statistics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting anomaly statistics");
            throw;
        }
    }

    public async Task ProvideUserFeedbackAsync(Guid resultId, bool isAnomaly, string? feedback = null, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var result = await _anomalyRepository.GetResultByIdAsync(resultId, cancellationToken);
            if (result == null)
            {
                _logger.LogWarning("Anomaly detection result {ResultId} not found", resultId);
                return;
            }

            if (isAnomaly)
            {
                result.ConfirmAnomaly(feedback);
            }
            else
            {
                result.MarkAsFalsePositive(feedback);
            }

            await _anomalyRepository.UpdateResultAsync(result, cancellationToken);
            
            _logger.LogInformation("User feedback provided for result {ResultId}: {IsAnomaly}", resultId, isAnomaly);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error providing user feedback for result {ResultId}", resultId);
            throw;
        }
    }

    public async Task RetrainModelsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting automatic model retraining");

            var modelsNeedingRetraining = _trainedModels.Values
                .Where(m => m.NeedsRetraining)
                .ToList();

            var retrainTasks = modelsNeedingRetraining.Select(model => 
                TrainModelAsync(model.Id, cancellationToken: cancellationToken));

            var results = await Task.WhenAll(retrainTasks);
            var successCount = results.Count(r => r);

            _logger.LogInformation("Retrained {SuccessCount}/{TotalCount} models",
                successCount, modelsNeedingRetraining.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during automatic model retraining");
            throw;
        }
    }

    private async Task<List<double>> GetTrainingDataAsync(string serviceName, string metricName, 
        TimeSpan period, CancellationToken cancellationToken)
    {
        var fromDate = DateTime.UtcNow.Subtract(period);
        var metrics = await _metricRepository.GetMetricsByServiceAsync(serviceName, fromDate, cancellationToken);
        
        return metrics
            .Where(m => m.Name == metricName)
            .OrderBy(m => m.Timestamp)
            .Select(m => m.Value)
            .ToList();
    }

    private async Task<ModelTrainingResult> TrainModelWithAlgorithmAsync(AnomalyDetectionModel model, 
        List<double> trainingData, CancellationToken cancellationToken)
    {
        try
        {
            return model.Algorithm switch
            {
                AnomalyDetectionAlgorithm.StatisticalZScore => TrainStatisticalModel(trainingData),
                AnomalyDetectionAlgorithm.MovingAverage => TrainMovingAverageModel(trainingData),
                AnomalyDetectionAlgorithm.IsolationForest => await TrainIsolationForestAsync(trainingData, cancellationToken),
                AnomalyDetectionAlgorithm.LSTM => await TrainLSTMModelAsync(trainingData, cancellationToken),
                _ => new ModelTrainingResult { Success = false, ErrorMessage = "Unsupported algorithm" }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error training model with algorithm {Algorithm}", model.Algorithm);
            return new ModelTrainingResult { Success = false, ErrorMessage = ex.Message };
        }
    }

    private ModelTrainingResult TrainStatisticalModel(List<double> data)
    {
        if (data.Count < 10)
            return new ModelTrainingResult { Success = false, ErrorMessage = "Insufficient data for statistical model" };

        var mean = data.Average();
        var variance = data.Sum(x => Math.Pow(x - mean, 2)) / data.Count;
        var stdDev = Math.Sqrt(variance);

        // Store baseline statistics
        var baselineKey = $"statistical_{mean}_{stdDev}";
        _metricBaselines.TryAdd(baselineKey, new List<double> { mean, stdDev });

        return new ModelTrainingResult
        {
            Success = true,
            Accuracy = 0.85, // Estimated accuracy for statistical models
            Precision = 0.80,
            Recall = 0.75
        };
    }

    private ModelTrainingResult TrainMovingAverageModel(List<double> data)
    {
        if (data.Count < 20)
            return new ModelTrainingResult { Success = false, ErrorMessage = "Insufficient data for moving average model" };

        // Calculate moving averages and store as baseline
        var windowSize = Math.Min(10, data.Count / 4);
        var movingAverages = new List<double>();
        
        for (int i = windowSize; i < data.Count; i++)
        {
            var avg = data.Skip(i - windowSize).Take(windowSize).Average();
            movingAverages.Add(avg);
        }

        var baselineKey = $"moving_average_{windowSize}";
        _metricBaselines.TryAdd(baselineKey, movingAverages);

        return new ModelTrainingResult
        {
            Success = true,
            Accuracy = 0.75,
            Precision = 0.70,
            Recall = 0.80
        };
    }

    private async Task<ModelTrainingResult> TrainIsolationForestAsync(List<double> data, CancellationToken cancellationToken)
    {
        // Placeholder for Isolation Forest implementation
        // In a real implementation, this would use a machine learning library
        await Task.Delay(1000, cancellationToken); // Simulate training time
        
        return new ModelTrainingResult
        {
            Success = true,
            Accuracy = 0.90,
            Precision = 0.85,
            Recall = 0.88
        };
    }

    private async Task<ModelTrainingResult> TrainLSTMModelAsync(List<double> data, CancellationToken cancellationToken)
    {
        // Placeholder for LSTM implementation
        // In a real implementation, this would use a deep learning framework
        await Task.Delay(5000, cancellationToken); // Simulate training time
        
        return new ModelTrainingResult
        {
            Success = true,
            Accuracy = 0.92,
            Precision = 0.90,
            Recall = 0.89
        };
    }

    private async Task<AnomalyDetectionResult?> DetectAnomalyForModelAsync(AnomalyDetectionModel model, 
        double value, DateTime timestamp, CancellationToken cancellationToken)
    {
        try
        {
            return model.Algorithm switch
            {
                AnomalyDetectionAlgorithm.StatisticalZScore => DetectWithStatistical(model, value, timestamp),
                AnomalyDetectionAlgorithm.MovingAverage => DetectWithMovingAverage(model, value, timestamp),
                AnomalyDetectionAlgorithm.IsolationForest => await DetectWithIsolationForestAsync(model, value, timestamp, cancellationToken),
                _ => null
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error detecting anomaly for model {ModelId}", model.Id);
            return null;
        }
    }

    private AnomalyDetectionResult DetectWithStatistical(AnomalyDetectionModel model, double value, DateTime timestamp)
    {
        // Get baseline statistics (in real implementation, these would be stored in the model)
        var mean = 100.0; // Placeholder
        var stdDev = 20.0; // Placeholder
        
        var zScore = Math.Abs((value - mean) / stdDev);
        var threshold = model.GetSensitivityThreshold();
        var isAnomaly = zScore > threshold;
        
        return new AnomalyDetectionResult(model.Id, value, timestamp, isAnomaly, zScore, mean, 
            confidence: 1.0 - (zScore / (threshold * 2)), 
            explanation: $"Z-score: {zScore:F2}, Threshold: {threshold:F2}");
    }

    private AnomalyDetectionResult DetectWithMovingAverage(AnomalyDetectionModel model, double value, DateTime timestamp)
    {
        // Simplified moving average detection
        var expectedValue = 100.0; // Placeholder - would be calculated from recent data
        var deviation = Math.Abs(value - expectedValue);
        var threshold = expectedValue * 0.2; // 20% deviation threshold
        var isAnomaly = deviation > threshold;
        
        var anomalyScore = deviation / threshold;
        
        return new AnomalyDetectionResult(model.Id, value, timestamp, isAnomaly, anomalyScore, expectedValue,
            confidence: Math.Max(0.1, 1.0 - (anomalyScore / 2)),
            explanation: $"Deviation: {deviation:F2}, Threshold: {threshold:F2}");
    }

    private async Task<AnomalyDetectionResult> DetectWithIsolationForestAsync(AnomalyDetectionModel model, 
        double value, DateTime timestamp, CancellationToken cancellationToken)
    {
        // Placeholder for Isolation Forest detection
        await Task.Delay(10, cancellationToken);
        
        var anomalyScore = new Random().NextDouble(); // Placeholder
        var isAnomaly = anomalyScore > 0.7;
        
        return new AnomalyDetectionResult(model.Id, value, timestamp, isAnomaly, anomalyScore, 
            confidence: anomalyScore, explanation: "Isolation Forest prediction");
    }

    private async Task CreateAnomalyAlertAsync(AnomalyDetectionModel model, AnomalyDetectionResult result, 
        CancellationToken cancellationToken)
    {
        try
        {
            var severity = result.AnomalyScore switch
            {
                > 0.9 => AlertSeverity.Critical,
                > 0.7 => AlertSeverity.High,
                > 0.5 => AlertSeverity.Medium,
                _ => AlertSeverity.Low
            };

            var title = $"Anomaly detected in {model.ServiceName}.{model.MetricName}";
            var description = $"Anomaly detected with score {result.AnomalyScore:F2}. " +
                            $"Value: {result.MetricValue:F2}, Expected: {result.ExpectedValue:F2}. " +
                            $"Explanation: {result.Explanation}";

            var threshold = new AlertThreshold(model.MetricName, result.MetricValue, 
                result.ExpectedValue ?? 0, "anomaly", model.DetectionWindow);

            var alert = new Alert(title, description, severity, "AnomalyDetection", 
                model.ServiceName, model.MetricName, result.MetricValue, threshold);

            alert.AddTag("anomaly_score", result.AnomalyScore.ToString("F2"));
            alert.AddTag("model_id", model.Id.ToString());
            alert.AddTag("algorithm", model.Algorithm.ToString());

            await _alertRepository.AddAsync(alert, cancellationToken);
            
            _logger.LogInformation("Created anomaly alert {AlertId} for model {ModelId}", alert.Id, model.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating anomaly alert for model {ModelId}", model.Id);
        }
    }

    private Dictionary<string, int> GetTopAnomalousMetrics(IEnumerable<AnomalyDetectionResult> anomalies)
    {
        return anomalies
            .GroupBy(a => a.Model.MetricName)
            .OrderByDescending(g => g.Count())
            .Take(10)
            .ToDictionary(g => g.Key, g => g.Count());
    }

    private Dictionary<int, int> GetAnomaliesByHour(IEnumerable<AnomalyDetectionResult> anomalies)
    {
        return anomalies
            .GroupBy(a => a.Timestamp.Hour)
            .ToDictionary(g => g.Key, g => g.Count());
    }

    private class ModelTrainingResult
    {
        public bool Success { get; set; }
        public double? Accuracy { get; set; }
        public double? Precision { get; set; }
        public double? Recall { get; set; }
        public string? ErrorMessage { get; set; }
    }
}
