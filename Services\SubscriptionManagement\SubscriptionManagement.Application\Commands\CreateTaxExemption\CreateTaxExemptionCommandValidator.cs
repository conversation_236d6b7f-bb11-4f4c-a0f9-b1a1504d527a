using FluentValidation;

namespace SubscriptionManagement.Application.Commands.CreateTaxExemption
{
    public class CreateTaxExemptionCommandValidator : AbstractValidator<CreateTaxExemptionCommand>
    {
        public CreateTaxExemptionCommandValidator()
        {
            RuleFor(x => x.UserId)
                .NotEmpty()
                .WithMessage("User ID is required");

            RuleFor(x => x.ExemptionType)
                .NotEmpty()
                .WithMessage("Exemption type is required")
                .MaximumLength(100)
                .WithMessage("Exemption type cannot exceed 100 characters");

            RuleFor(x => x.ExemptionNumber)
                .NotEmpty()
                .WithMessage("Exemption number is required")
                .MaximumLength(50)
                .WithMessage("Exemption number cannot exceed 50 characters");

            RuleFor(x => x.IssuingAuthority)
                .NotEmpty()
                .WithMessage("Issuing authority is required")
                .MaximumLength(200)
                .WithMessage("Issuing authority cannot exceed 200 characters");

            RuleFor(x => x.ValidFrom)
                .NotEmpty()
                .WithMessage("Valid from date is required")
                .Must(date => date >= DateTime.UtcNow.Date.AddYears(-10))
                .WithMessage("Valid from date cannot be more than 10 years in the past");

            RuleFor(x => x.ValidTo)
                .NotEmpty()
                .WithMessage("Valid to date is required")
                .GreaterThan(x => x.ValidFrom)
                .WithMessage("Valid to date must be after valid from date")
                .Must(date => date <= DateTime.UtcNow.Date.AddYears(20))
                .WithMessage("Valid to date cannot be more than 20 years in the future");

            RuleFor(x => x.ExemptTaxTypes)
                .NotEmpty()
                .WithMessage("At least one exempt tax type must be specified")
                .Must(types => types.All(t => Enum.IsDefined(typeof(Domain.Enums.TaxType), t)))
                .WithMessage("All tax types must be valid");

            RuleFor(x => x.ApplicableRegions)
                .NotEmpty()
                .WithMessage("At least one applicable region must be specified")
                .Must(regions => regions.All(r => !string.IsNullOrWhiteSpace(r)))
                .WithMessage("All regions must be valid")
                .Must(regions => regions.All(r => r.Length >= 2 && r.Length <= 10))
                .WithMessage("Region codes must be between 2 and 10 characters");

            RuleFor(x => x.CreatedByUserId)
                .NotEmpty()
                .WithMessage("Created by user ID is required");

            RuleFor(x => x.DocumentPath)
                .MaximumLength(500)
                .WithMessage("Document path cannot exceed 500 characters");
        }
    }
}
