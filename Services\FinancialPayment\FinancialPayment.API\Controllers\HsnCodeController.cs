using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Application.DTOs;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class HsnCodeController : ControllerBase
{
    private readonly ITaxConfigurationService _taxConfigurationService;
    private readonly ILogger<HsnCodeController> _logger;

    public HsnCodeController(
        ITaxConfigurationService taxConfigurationService,
        ILogger<HsnCodeController> logger)
    {
        _taxConfigurationService = taxConfigurationService;
        _logger = logger;
    }

    /// <summary>
    /// Create a new HSN code
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<Guid>> CreateHsnCode([FromBody] CreateHsnCodeDto request)
    {
        try
        {
            var codeDetails = new HsnCodeDetails(
                request.CodeDetails.Code,
                request.CodeDetails.Description,
                request.CodeDetails.Category,
                request.CodeDetails.ApplicableGstRate);

            var hsnCode = await _taxConfigurationService.CreateHsnCodeAsync(
                codeDetails,
                request.Chapter,
                request.Section,
                request.EffectiveFrom,
                request.CreatedBy,
                request.EffectiveTo,
                request.AdditionalNotes);

            return CreatedAtAction(nameof(GetHsnCode), new { id = hsnCode.Id }, hsnCode.Id);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while creating HSN code");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating HSN code");
            return StatusCode(500, "An error occurred while creating HSN code");
        }
    }

    /// <summary>
    /// Get HSN code by ID
    /// </summary>
    [HttpGet("{id}")]
    [Authorize(Roles = "Admin,Finance")]
    public async Task<ActionResult<HsnCodeDto>> GetHsnCode(Guid id)
    {
        try
        {
            var hsnCode = await _taxConfigurationService.GetHsnCodeAsync(id);
            if (hsnCode == null)
                return NotFound($"HSN code {id} not found");

            var dto = MapToDto(hsnCode);
            return Ok(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving HSN code {Id}", id);
            return StatusCode(500, "An error occurred while retrieving HSN code");
        }
    }

    /// <summary>
    /// Get HSN code by code
    /// </summary>
    [HttpGet("by-code/{code}")]
    [Authorize(Roles = "Admin,Finance,Broker,TransportCompany")]
    public async Task<ActionResult<HsnCodeDto>> GetHsnCodeByCode(string code)
    {
        try
        {
            var hsnCode = await _taxConfigurationService.GetHsnCodeByCodeAsync(code);
            if (hsnCode == null)
                return NotFound($"HSN code {code} not found");

            var dto = MapToDto(hsnCode);
            return Ok(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving HSN code {Code}", code);
            return StatusCode(500, "An error occurred while retrieving HSN code");
        }
    }

    /// <summary>
    /// Get all active HSN codes
    /// </summary>
    [HttpGet("active")]
    [Authorize(Roles = "Admin,Finance")]
    public async Task<ActionResult<List<HsnCodeDto>>> GetActiveHsnCodes()
    {
        try
        {
            var hsnCodes = await _taxConfigurationService.GetActiveHsnCodesAsync();
            var dtos = hsnCodes.Select(MapToDto).ToList();
            return Ok(dtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving active HSN codes");
            return StatusCode(500, "An error occurred while retrieving HSN codes");
        }
    }

    /// <summary>
    /// Search HSN codes
    /// </summary>
    [HttpGet("search")]
    [Authorize(Roles = "Admin,Finance,Broker,TransportCompany")]
    public async Task<ActionResult<List<HsnCodeDto>>> SearchHsnCodes(
        [FromQuery] string searchTerm,
        [FromQuery] int skip = 0,
        [FromQuery] int take = 50)
    {
        try
        {
            var hsnCodes = await _taxConfigurationService.SearchHsnCodesAsync(searchTerm, skip, take);
            var dtos = hsnCodes.Select(MapToDto).ToList();
            return Ok(dtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching HSN codes");
            return StatusCode(500, "An error occurred while searching HSN codes");
        }
    }

    /// <summary>
    /// Get applicable GST rate for HSN code
    /// </summary>
    [HttpGet("{code}/gst-rate")]
    [Authorize(Roles = "Admin,Finance,Broker,TransportCompany")]
    public async Task<ActionResult<GstRate?>> GetApplicableGstRate(string code, [FromQuery] DateTime? date = null)
    {
        try
        {
            var effectiveDate = date ?? DateTime.UtcNow;
            var gstRate = await _taxConfigurationService.GetApplicableGstRateForHsnAsync(code, effectiveDate);
            return Ok(gstRate);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving GST rate for HSN code {Code}", code);
            return StatusCode(500, "An error occurred while retrieving GST rate");
        }
    }

    /// <summary>
    /// Update HSN code
    /// </summary>
    [HttpPut("{id}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<HsnCodeDto>> UpdateHsnCode(Guid id, [FromBody] UpdateHsnCodeDto request)
    {
        try
        {
            var codeDetails = new HsnCodeDetails(
                request.CodeDetails.Code,
                request.CodeDetails.Description,
                request.CodeDetails.Category,
                request.CodeDetails.ApplicableGstRate);

            var updatedHsnCode = await _taxConfigurationService.UpdateHsnCodeAsync(
                id,
                codeDetails,
                request.Chapter,
                request.Section,
                request.ModifiedBy,
                request.AdditionalNotes);

            var dto = MapToDto(updatedHsnCode);
            return Ok(dto);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while updating HSN code {Id}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating HSN code {Id}", id);
            return StatusCode(500, "An error occurred while updating HSN code");
        }
    }

    /// <summary>
    /// Add GST mapping to HSN code
    /// </summary>
    [HttpPost("{id}/gst-mapping")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> AddGstMapping(Guid id, [FromBody] AddHsnCodeGstMappingDto request)
    {
        try
        {
            await _taxConfigurationService.AddGstMappingToHsnAsync(
                id,
                request.GstRate,
                request.EffectiveFrom,
                request.EffectiveTo,
                request.AddedBy);

            return Ok();
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while adding GST mapping to HSN code {Id}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding GST mapping to HSN code {Id}", id);
            return StatusCode(500, "An error occurred while adding GST mapping");
        }
    }

    /// <summary>
    /// Bulk import HSN codes
    /// </summary>
    [HttpPost("bulk-import")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<BulkImportResultDto>> BulkImportHsnCodes([FromBody] BulkImportHsnCodeDto request)
    {
        try
        {
            var hsnCodes = new List<Domain.Entities.HsnCode>();
            var errors = new List<string>();
            var warnings = new List<string>();

            foreach (var hsnCodeDto in request.HsnCodes)
            {
                try
                {
                    var codeDetails = new HsnCodeDetails(
                        hsnCodeDto.CodeDetails.Code,
                        hsnCodeDto.CodeDetails.Description,
                        hsnCodeDto.CodeDetails.Category,
                        hsnCodeDto.CodeDetails.ApplicableGstRate);

                    var hsnCode = new Domain.Entities.HsnCode(
                        codeDetails,
                        hsnCodeDto.Chapter,
                        hsnCodeDto.Section,
                        hsnCodeDto.EffectiveFrom,
                        request.ImportedBy,
                        hsnCodeDto.EffectiveTo,
                        hsnCodeDto.AdditionalNotes);

                    hsnCodes.Add(hsnCode);
                }
                catch (Exception ex)
                {
                    errors.Add($"Error processing HSN code {hsnCodeDto.CodeDetails.Code}: {ex.Message}");
                }
            }

            if (request.ValidateOnly)
            {
                return Ok(new BulkImportResultDto
                {
                    TotalRecords = request.HsnCodes.Count,
                    SuccessfulImports = hsnCodes.Count,
                    FailedImports = errors.Count,
                    Errors = errors,
                    Warnings = warnings
                });
            }

            var importedCodes = await _taxConfigurationService.BulkImportHsnCodesAsync(hsnCodes);

            return Ok(new BulkImportResultDto
            {
                TotalRecords = request.HsnCodes.Count,
                SuccessfulImports = importedCodes.Count,
                FailedImports = errors.Count,
                Errors = errors,
                Warnings = warnings,
                ImportedHsnCodes = importedCodes.Select(MapToDto).ToList()
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during bulk import of HSN codes");
            return StatusCode(500, "An error occurred during bulk import");
        }
    }

    /// <summary>
    /// Activate HSN code
    /// </summary>
    [HttpPost("{id}/activate")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> ActivateHsnCode(Guid id, [FromBody] string activatedBy)
    {
        try
        {
            await _taxConfigurationService.ActivateHsnCodeAsync(id, activatedBy);
            return Ok();
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while activating HSN code {Id}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error activating HSN code {Id}", id);
            return StatusCode(500, "An error occurred while activating HSN code");
        }
    }

    /// <summary>
    /// Deactivate HSN code
    /// </summary>
    [HttpPost("{id}/deactivate")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> DeactivateHsnCode(Guid id, [FromBody] DeactivateHsnRequest request)
    {
        try
        {
            await _taxConfigurationService.DeactivateHsnCodeAsync(id, request.DeactivatedBy, request.Reason);
            return Ok();
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while deactivating HSN code {Id}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deactivating HSN code {Id}", id);
            return StatusCode(500, "An error occurred while deactivating HSN code");
        }
    }

    /// <summary>
    /// Deprecate HSN code
    /// </summary>
    [HttpPost("{id}/deprecate")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> DeprecateHsnCode(Guid id, [FromBody] DeprecateHsnRequest request)
    {
        try
        {
            await _taxConfigurationService.DeprecateHsnCodeAsync(id, request.DeprecatedBy, request.Reason);
            return Ok();
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while deprecating HSN code {Id}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deprecating HSN code {Id}", id);
            return StatusCode(500, "An error occurred while deprecating HSN code");
        }
    }

    private static HsnCodeDto MapToDto(Domain.Entities.HsnCode hsnCode)
    {
        return new HsnCodeDto
        {
            Id = hsnCode.Id,
            CodeDetails = new HsnCodeDetailsDto
            {
                Code = hsnCode.CodeDetails.Code,
                Description = hsnCode.CodeDetails.Description,
                Category = hsnCode.CodeDetails.Category,
                ApplicableGstRate = hsnCode.CodeDetails.ApplicableGstRate
            },
            Chapter = hsnCode.Chapter,
            Section = hsnCode.Section,
            Status = hsnCode.Status,
            EffectiveFrom = hsnCode.EffectiveFrom,
            EffectiveTo = hsnCode.EffectiveTo,
            AdditionalNotes = hsnCode.AdditionalNotes,
            CreatedBy = hsnCode.CreatedBy,
            ModifiedBy = hsnCode.ModifiedBy,
            ModifiedAt = hsnCode.ModifiedAt,
            CreatedAt = hsnCode.CreatedAt,
            UpdatedAt = hsnCode.UpdatedAt,
            History = hsnCode.History.Select(h => new HsnCodeHistoryDto
            {
                Id = h.Id,
                HsnCodeId = h.HsnCodeId,
                Action = h.Action,
                Details = h.Details,
                ModifiedBy = h.ModifiedBy,
                ModifiedAt = h.ModifiedAt
            }).ToList(),
            GstMappings = hsnCode.GstMappings.Select(m => new HsnCodeGstMappingDto
            {
                Id = m.Id,
                HsnCodeId = m.HsnCodeId,
                GstRate = m.GstRate,
                EffectiveFrom = m.EffectiveFrom,
                EffectiveTo = m.EffectiveTo,
                Status = m.Status,
                CreatedBy = m.CreatedBy,
                CreatedAt = m.CreatedAt
            }).ToList()
        };
    }
}

public class DeactivateHsnRequest
{
    public string DeactivatedBy { get; set; } = string.Empty;
    public string Reason { get; set; } = string.Empty;
}

public class DeprecateHsnRequest
{
    public string DeprecatedBy { get; set; } = string.Empty;
    public string Reason { get; set; } = string.Empty;
}
