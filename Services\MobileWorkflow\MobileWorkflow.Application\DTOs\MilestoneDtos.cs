namespace MobileWorkflow.Application.DTOs;

// Request DTOs
public class CreateMilestoneTemplateRequest
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public Dictionary<string, object> Configuration { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
    public List<CreateMilestoneStepRequest> Steps { get; set; } = new();
}

public class UpdateMilestoneTemplateRequest
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public Dictionary<string, object> Configuration { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class CreateMilestoneStepRequest
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int SequenceNumber { get; set; }
    public bool IsRequired { get; set; } = true;
    public string? TriggerCondition { get; set; }
    public Dictionary<string, object> Configuration { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
    public List<CreateMilestonePayoutRuleRequest> PayoutRules { get; set; } = new();
}

public class UpdateMilestoneStepRequest
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public bool IsRequired { get; set; }
    public string? TriggerCondition { get; set; }
    public Dictionary<string, object> Configuration { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class CreateMilestonePayoutRuleRequest
{
    public decimal PayoutPercentage { get; set; }
    public string? TriggerCondition { get; set; }
    public string? Description { get; set; }
    public Dictionary<string, object> Configuration { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class UpdateMilestonePayoutRuleRequest
{
    public decimal PayoutPercentage { get; set; }
    public string? TriggerCondition { get; set; }
    public string? Description { get; set; }
    public Dictionary<string, object> Configuration { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class CreateRoleTemplateMappingRequest
{
    public string RoleName { get; set; } = string.Empty;
    public Guid MilestoneTemplateId { get; set; }
    public bool IsDefault { get; set; } = false;
    public int Priority { get; set; } = 100;
    public string? Conditions { get; set; }
    public Dictionary<string, object> Configuration { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class UpdateRoleTemplateMappingRequest
{
    public bool IsDefault { get; set; }
    public int Priority { get; set; }
    public string? Conditions { get; set; }
    public Dictionary<string, object> Configuration { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class ReorderMilestoneStepsRequest
{
    public Dictionary<Guid, int> StepSequenceMap { get; set; } = new();
}

// Response DTOs
public class MilestoneTemplateDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public bool IsDefault { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public string? UpdatedBy { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public int UsageCount { get; set; }
    public Dictionary<string, object> Configuration { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
    public List<MilestoneStepDto> Steps { get; set; } = new();
    public List<RoleTemplateMappingDto> RoleMappings { get; set; } = new();
    public decimal TotalPayoutPercentage { get; set; }
    public bool IsValid { get; set; }
    public List<string> ValidationErrors { get; set; } = new();
}

public class MilestoneStepDto
{
    public Guid Id { get; set; }
    public Guid MilestoneTemplateId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int SequenceNumber { get; set; }
    public bool IsRequired { get; set; }
    public bool IsActive { get; set; }
    public string? TriggerCondition { get; set; }
    public DateTime CreatedAt { get; set; }
    public Dictionary<string, object> Configuration { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
    public List<MilestonePayoutRuleDto> PayoutRules { get; set; } = new();
    public decimal TotalPayoutPercentage { get; set; }
}

public class MilestonePayoutRuleDto
{
    public Guid Id { get; set; }
    public Guid MilestoneStepId { get; set; }
    public decimal PayoutPercentage { get; set; }
    public string? TriggerCondition { get; set; }
    public bool IsActive { get; set; }
    public string? Description { get; set; }
    public DateTime CreatedAt { get; set; }
    public Dictionary<string, object> Configuration { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class RoleTemplateMappingDto
{
    public Guid Id { get; set; }
    public string RoleName { get; set; } = string.Empty;
    public Guid MilestoneTemplateId { get; set; }
    public string MilestoneTemplateName { get; set; } = string.Empty;
    public bool IsDefault { get; set; }
    public bool IsActive { get; set; }
    public int Priority { get; set; }
    public string? Conditions { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public string? UpdatedBy { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public Dictionary<string, object> Configuration { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

// Query DTOs
public class MilestoneTemplateSearchRequest
{
    public string? SearchTerm { get; set; }
    public string? Type { get; set; }
    public string? Category { get; set; }
    public bool? IsActive { get; set; }
    public bool? IsDefault { get; set; }
    public string? CreatedBy { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public string SortBy { get; set; } = "Name";
    public string SortDirection { get; set; } = "ASC";
}

public class RoleTemplateMappingSearchRequest
{
    public string? RoleName { get; set; }
    public Guid? MilestoneTemplateId { get; set; }
    public bool? IsActive { get; set; }
    public bool? IsDefault { get; set; }
    public int? MinPriority { get; set; }
    public string? CreatedBy { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public string SortBy { get; set; } = "RoleName";
    public string SortDirection { get; set; } = "ASC";
}

// Validation DTOs
public class MilestoneTemplateValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public decimal TotalPayoutPercentage { get; set; }
    public Dictionary<string, object> ValidationDetails { get; set; } = new();
}

// Preview DTOs
public class MilestoneTemplatePreviewDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public int StepCount { get; set; }
    public decimal TotalPayoutPercentage { get; set; }
    public bool IsValid { get; set; }
    public bool IsActive { get; set; }
    public bool IsDefault { get; set; }
    public int UsageCount { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

// Bulk operation DTOs
public class BulkMilestoneTemplateOperationRequest
{
    public List<Guid> TemplateIds { get; set; } = new();
    public string Operation { get; set; } = string.Empty; // "activate", "deactivate", "delete"
    public string PerformedBy { get; set; } = string.Empty;
    public Dictionary<string, object> Parameters { get; set; } = new();
}

public class BulkOperationResult
{
    public int TotalRequested { get; set; }
    public int Successful { get; set; }
    public int Failed { get; set; }
    public List<BulkOperationError> Errors { get; set; } = new();
}

public class BulkOperationError
{
    public Guid Id { get; set; }
    public string Error { get; set; } = string.Empty;
}
