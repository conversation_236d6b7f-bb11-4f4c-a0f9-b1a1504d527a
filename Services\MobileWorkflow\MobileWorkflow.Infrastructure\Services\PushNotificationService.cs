using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using MobileWorkflow.Domain.Entities;
using MobileWorkflow.Domain.Repositories;
using MobileWorkflow.Application.DTOs;
using System.Text.Json;

namespace MobileWorkflow.Infrastructure.Services;

public interface IPushNotificationService
{
    Task<NotificationTemplateDto> CreateTemplateAsync(CreateNotificationTemplateRequest request, CancellationToken cancellationToken = default);
    Task<NotificationTemplateDto?> GetTemplateAsync(Guid templateId, CancellationToken cancellationToken = default);
    Task<List<NotificationTemplateDto>> GetTemplatesAsync(string? category = null, string? type = null, CancellationToken cancellationToken = default);
    Task<bool> UpdateTemplateAsync(Guid templateId, UpdateNotificationTemplateRequest request, CancellationToken cancellationToken = default);
    Task<NotificationCampaignDto> CreateCampaignAsync(CreateNotificationCampaignRequest request, CancellationToken cancellationToken = default);
    Task<NotificationCampaignDto?> GetCampaignAsync(Guid campaignId, CancellationToken cancellationToken = default);
    Task<List<NotificationCampaignDto>> GetCampaignsAsync(string? status = null, CancellationToken cancellationToken = default);
    Task<bool> UpdateCampaignAsync(Guid campaignId, UpdateNotificationCampaignRequest request, CancellationToken cancellationToken = default);
    Task<bool> ScheduleCampaignAsync(Guid campaignId, DateTime scheduledAt, CancellationToken cancellationToken = default);
    Task<bool> StartCampaignAsync(Guid campaignId, CancellationToken cancellationToken = default);
    Task<bool> PauseCampaignAsync(Guid campaignId, CancellationToken cancellationToken = default);
    Task<bool> ResumeCampaignAsync(Guid campaignId, CancellationToken cancellationToken = default);
    Task<bool> CancelCampaignAsync(Guid campaignId, CancellationToken cancellationToken = default);
    Task<bool> SendImmediateNotificationAsync(SendImmediateNotificationRequest request, CancellationToken cancellationToken = default);
    Task<bool> RegisterDeviceTokenAsync(RegisterDeviceTokenRequest request, CancellationToken cancellationToken = default);
    Task<bool> UnregisterDeviceTokenAsync(Guid userId, string token, CancellationToken cancellationToken = default);
    Task<List<NotificationDeliveryDto>> GetDeliveriesAsync(Guid campaignId, string? status = null, CancellationToken cancellationToken = default);
    Task<CampaignPerformanceDto> GetCampaignPerformanceAsync(Guid campaignId, CancellationToken cancellationToken = default);
    Task<bool> TrackNotificationOpenAsync(Guid deliveryId, CancellationToken cancellationToken = default);
    Task<bool> TrackNotificationClickAsync(Guid deliveryId, CancellationToken cancellationToken = default);
    Task ProcessScheduledCampaignsAsync(CancellationToken cancellationToken = default);
    Task ProcessRetryDeliveriesAsync(CancellationToken cancellationToken = default);
}

public class PushNotificationService : IPushNotificationService
{
    private readonly INotificationTemplateRepository _templateRepository;
    private readonly INotificationCampaignRepository _campaignRepository;
    private readonly INotificationDeliveryRepository _deliveryRepository;
    private readonly IDeviceTokenRepository _deviceTokenRepository;
    private readonly INotificationProviderService _providerService;
    private readonly IUserTargetingService _targetingService;
    private readonly ILogger<PushNotificationService> _logger;
    private readonly IMemoryCache _cache;
    private readonly IConfiguration _configuration;

    public PushNotificationService(
        INotificationTemplateRepository templateRepository,
        INotificationCampaignRepository campaignRepository,
        INotificationDeliveryRepository deliveryRepository,
        IDeviceTokenRepository deviceTokenRepository,
        INotificationProviderService providerService,
        IUserTargetingService targetingService,
        ILogger<PushNotificationService> logger,
        IMemoryCache cache,
        IConfiguration configuration)
    {
        _templateRepository = templateRepository;
        _campaignRepository = campaignRepository;
        _deliveryRepository = deliveryRepository;
        _deviceTokenRepository = deviceTokenRepository;
        _providerService = providerService;
        _targetingService = targetingService;
        _logger = logger;
        _cache = cache;
        _configuration = configuration;
    }

    public async Task<NotificationTemplateDto> CreateTemplateAsync(CreateNotificationTemplateRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating notification template {Name} of type {Type}", request.Name, request.Type);

            var template = new NotificationTemplate(
                request.Name,
                request.DisplayName,
                request.Description,
                request.Category,
                request.Type,
                request.Content,
                request.SupportedPlatforms,
                request.CreatedBy);

            if (request.Styling != null)
            {
                template.UpdateStyling(request.Styling, request.CreatedBy);
            }

            if (request.Configuration != null)
            {
                template.UpdateConfiguration(request.Configuration, request.CreatedBy);
            }

            if (request.PlatformSpecific != null)
            {
                foreach (var platformConfig in request.PlatformSpecific)
                {
                    template.SetPlatformSpecific(platformConfig.Key, platformConfig.Value);
                }
            }

            if (request.DefaultValues != null)
            {
                foreach (var defaultValue in request.DefaultValues)
                {
                    template.SetDefaultValue(defaultValue.Key, defaultValue.Value);
                }
            }

            _templateRepository.Add(template);
            await _templateRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Notification template {TemplateId} created successfully", template.Id);

            return MapToNotificationTemplateDto(template);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating notification template {Name}", request.Name);
            throw;
        }
    }

    public async Task<NotificationTemplateDto?> GetTemplateAsync(Guid templateId, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"template_{templateId}";
            if (_cache.TryGetValue(cacheKey, out NotificationTemplateDto? cachedTemplate))
            {
                return cachedTemplate;
            }

            var template = await _templateRepository.GetByIdAsync(templateId, cancellationToken);
            if (template == null)
            {
                return null;
            }

            var templateDto = MapToNotificationTemplateDto(template);
            _cache.Set(cacheKey, templateDto, TimeSpan.FromMinutes(15));

            return templateDto;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting notification template {TemplateId}", templateId);
            return null;
        }
    }

    public async Task<List<NotificationTemplateDto>> GetTemplatesAsync(string? category = null, string? type = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"templates_{category}_{type}";
            if (_cache.TryGetValue(cacheKey, out List<NotificationTemplateDto>? cachedTemplates))
            {
                return cachedTemplates!;
            }

            var templates = await _templateRepository.GetByCategoryAndTypeAsync(category, type, cancellationToken);
            var templateDtos = templates.Select(MapToNotificationTemplateDto).ToList();

            _cache.Set(cacheKey, templateDtos, TimeSpan.FromMinutes(10));

            return templateDtos;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting notification templates");
            return new List<NotificationTemplateDto>();
        }
    }

    public async Task<bool> UpdateTemplateAsync(Guid templateId, UpdateNotificationTemplateRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var template = await _templateRepository.GetByIdAsync(templateId, cancellationToken);
            if (template == null)
            {
                return false;
            }

            if (request.Content != null)
            {
                template.UpdateContent(request.Content, request.UpdatedBy);
            }

            if (request.Styling != null)
            {
                template.UpdateStyling(request.Styling, request.UpdatedBy);
            }

            if (request.Configuration != null)
            {
                template.UpdateConfiguration(request.Configuration, request.UpdatedBy);
            }

            if (request.PlatformSpecific != null)
            {
                foreach (var platformConfig in request.PlatformSpecific)
                {
                    template.SetPlatformSpecific(platformConfig.Key, platformConfig.Value);
                }
            }

            await _templateRepository.SaveChangesAsync(cancellationToken);

            // Clear cache
            _cache.Remove($"template_{templateId}");

            _logger.LogInformation("Notification template {TemplateId} updated successfully", templateId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating notification template {TemplateId}", templateId);
            return false;
        }
    }

    public async Task<NotificationCampaignDto> CreateCampaignAsync(CreateNotificationCampaignRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating notification campaign {Name} with template {TemplateId}", request.Name, request.TemplateId);

            // Validate template exists and is active
            var template = await _templateRepository.GetByIdAsync(request.TemplateId, cancellationToken);
            if (template == null || !template.IsActive)
            {
                throw new InvalidOperationException("Template not found or inactive");
            }

            var campaign = new NotificationCampaign(
                request.Name,
                request.DisplayName,
                request.Description,
                request.TemplateId,
                request.Type,
                request.TargetingCriteria,
                request.TargetPlatforms,
                request.CreatedBy);

            if (request.Variables != null)
            {
                campaign.UpdateVariables(request.Variables, request.CreatedBy);
            }

            if (request.Configuration != null)
            {
                campaign.UpdateConfiguration(request.Configuration, request.CreatedBy);
            }

            // Estimate recipients
            var estimatedRecipients = await _targetingService.EstimateRecipientsAsync(request.TargetingCriteria, request.TargetPlatforms, cancellationToken);
            campaign.SetEstimatedRecipients(estimatedRecipients);

            _campaignRepository.Add(campaign);
            await _campaignRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Notification campaign {CampaignId} created successfully with {EstimatedRecipients} estimated recipients",
                campaign.Id, estimatedRecipients);

            return MapToNotificationCampaignDto(campaign);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating notification campaign {Name}", request.Name);
            throw;
        }
    }

    public async Task<NotificationCampaignDto?> GetCampaignAsync(Guid campaignId, CancellationToken cancellationToken = default)
    {
        try
        {
            var campaign = await _campaignRepository.GetByIdAsync(campaignId, cancellationToken);
            if (campaign == null)
            {
                return null;
            }

            return MapToNotificationCampaignDto(campaign);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting notification campaign {CampaignId}", campaignId);
            return null;
        }
    }

    public async Task<List<NotificationCampaignDto>> GetCampaignsAsync(string? status = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var campaigns = await _campaignRepository.GetByStatusAsync(status, cancellationToken);
            return campaigns.Select(MapToNotificationCampaignDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting notification campaigns");
            return new List<NotificationCampaignDto>();
        }
    }

    public async Task<bool> UpdateCampaignAsync(Guid campaignId, UpdateNotificationCampaignRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var campaign = await _campaignRepository.GetByIdAsync(campaignId, cancellationToken);
            if (campaign == null || !campaign.CanBeModified())
            {
                return false;
            }

            if (request.TargetingCriteria != null)
            {
                campaign.UpdateTargetingCriteria(request.TargetingCriteria, request.UpdatedBy);

                // Re-estimate recipients
                var estimatedRecipients = await _targetingService.EstimateRecipientsAsync(request.TargetingCriteria, campaign.TargetPlatforms, cancellationToken);
                campaign.SetEstimatedRecipients(estimatedRecipients);
            }

            if (request.Variables != null)
            {
                campaign.UpdateVariables(request.Variables, request.UpdatedBy);
            }

            if (request.Configuration != null)
            {
                campaign.UpdateConfiguration(request.Configuration, request.UpdatedBy);
            }

            await _campaignRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Notification campaign {CampaignId} updated successfully", campaignId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating notification campaign {CampaignId}", campaignId);
            return false;
        }
    }

    public async Task<bool> ScheduleCampaignAsync(Guid campaignId, DateTime scheduledAt, CancellationToken cancellationToken = default)
    {
        try
        {
            var campaign = await _campaignRepository.GetByIdAsync(campaignId, cancellationToken);
            if (campaign == null)
            {
                return false;
            }

            campaign.Schedule(scheduledAt, "System");
            await _campaignRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Notification campaign {CampaignId} scheduled for {ScheduledAt}", campaignId, scheduledAt);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error scheduling notification campaign {CampaignId}", campaignId);
            return false;
        }
    }

    public async Task<bool> StartCampaignAsync(Guid campaignId, CancellationToken cancellationToken = default)
    {
        try
        {
            var campaign = await _campaignRepository.GetByIdAsync(campaignId, cancellationToken);
            if (campaign == null || !campaign.CanBeStarted())
            {
                return false;
            }

            // Get actual recipients
            var recipients = await _targetingService.GetRecipientsAsync(campaign.TargetingCriteria, campaign.TargetPlatforms, cancellationToken);

            campaign.Start(recipients.Count);
            await _campaignRepository.SaveChangesAsync(cancellationToken);

            // Create deliveries
            await CreateDeliveriesAsync(campaign, recipients, cancellationToken);

            // Start processing deliveries
            _ = Task.Run(async () => await ProcessCampaignDeliveriesAsync(campaignId, CancellationToken.None));

            _logger.LogInformation("Notification campaign {CampaignId} started with {RecipientCount} recipients", campaignId, recipients.Count);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting notification campaign {CampaignId}", campaignId);
            return false;
        }
    }

    public async Task<bool> PauseCampaignAsync(Guid campaignId, CancellationToken cancellationToken = default)
    {
        try
        {
            var campaign = await _campaignRepository.GetByIdAsync(campaignId, cancellationToken);
            if (campaign == null)
            {
                return false;
            }

            campaign.Pause("System");
            await _campaignRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Notification campaign {CampaignId} paused", campaignId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error pausing notification campaign {CampaignId}", campaignId);
            return false;
        }
    }

    public async Task<bool> ResumeCampaignAsync(Guid campaignId, CancellationToken cancellationToken = default)
    {
        try
        {
            var campaign = await _campaignRepository.GetByIdAsync(campaignId, cancellationToken);
            if (campaign == null)
            {
                return false;
            }

            campaign.Resume("System");
            await _campaignRepository.SaveChangesAsync(cancellationToken);

            // Resume processing deliveries
            _ = Task.Run(async () => await ProcessCampaignDeliveriesAsync(campaignId, CancellationToken.None));

            _logger.LogInformation("Notification campaign {CampaignId} resumed", campaignId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resuming notification campaign {CampaignId}", campaignId);
            return false;
        }
    }

    public async Task<bool> CancelCampaignAsync(Guid campaignId, CancellationToken cancellationToken = default)
    {
        try
        {
            var campaign = await _campaignRepository.GetByIdAsync(campaignId, cancellationToken);
            if (campaign == null)
            {
                return false;
            }

            campaign.Cancel("System");
            await _campaignRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Notification campaign {CampaignId} cancelled", campaignId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling notification campaign {CampaignId}", campaignId);
            return false;
        }
    }

    public async Task<bool> SendImmediateNotificationAsync(SendImmediateNotificationRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Sending immediate notification to user {UserId}", request.UserId);

            // Get user's device tokens
            var deviceTokens = await _deviceTokenRepository.GetActiveByUserIdAsync(request.UserId, cancellationToken);
            if (!deviceTokens.Any())
            {
                _logger.LogWarning("No active device tokens found for user {UserId}", request.UserId);
                return false;
            }

            // Filter by platform if specified
            if (request.Platform != null)
            {
                deviceTokens = deviceTokens.Where(dt => dt.Platform.Equals(request.Platform, StringComparison.OrdinalIgnoreCase)).ToList();
            }

            var successCount = 0;
            foreach (var deviceToken in deviceTokens)
            {
                try
                {
                    var result = await _providerService.SendNotificationAsync(
                        deviceToken.Token,
                        deviceToken.Platform,
                        request.Content,
                        cancellationToken);

                    if (result.IsSuccess)
                    {
                        successCount++;
                        deviceToken.MarkAsUsed();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error sending immediate notification to device {DeviceToken}", deviceToken.Token);
                }
            }

            await _deviceTokenRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Immediate notification sent to {SuccessCount}/{TotalCount} devices for user {UserId}",
                successCount, deviceTokens.Count, request.UserId);

            return successCount > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending immediate notification to user {UserId}", request.UserId);
            return false;
        }
    }

    public async Task<bool> RegisterDeviceTokenAsync(RegisterDeviceTokenRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            // Check if token already exists
            var existingToken = await _deviceTokenRepository.GetByTokenAsync(request.Token, cancellationToken);
            if (existingToken != null)
            {
                if (existingToken.UserId == request.UserId)
                {
                    // Update existing token
                    existingToken.UpdateToken(request.Token, request.AppVersion, request.DeviceInfo);
                    existingToken.Reactivate();
                }
                else
                {
                    // Token moved to different user, deactivate old and create new
                    existingToken.Deactivate();
                    var newToken = new DeviceToken(request.UserId, request.Token, request.Platform, request.AppVersion, request.DeviceInfo);
                    _deviceTokenRepository.Add(newToken);
                }
            }
            else
            {
                // Create new token
                var deviceToken = new DeviceToken(request.UserId, request.Token, request.Platform, request.AppVersion, request.DeviceInfo);
                _deviceTokenRepository.Add(deviceToken);
            }

            await _deviceTokenRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Device token registered for user {UserId} on platform {Platform}", request.UserId, request.Platform);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error registering device token for user {UserId}", request.UserId);
            return false;
        }
    }

    public async Task<bool> UnregisterDeviceTokenAsync(Guid userId, string token, CancellationToken cancellationToken = default)
    {
        try
        {
            var deviceToken = await _deviceTokenRepository.GetByTokenAsync(token, cancellationToken);
            if (deviceToken != null && deviceToken.UserId == userId)
            {
                deviceToken.Deactivate();
                await _deviceTokenRepository.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("Device token unregistered for user {UserId}", userId);
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error unregistering device token for user {UserId}", userId);
            return false;
        }
    }

    public async Task<List<NotificationDeliveryDto>> GetDeliveriesAsync(Guid campaignId, string? status = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var deliveries = await _deliveryRepository.GetByCampaignIdAsync(campaignId, status, cancellationToken);
            return deliveries.Select(MapToNotificationDeliveryDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting deliveries for campaign {CampaignId}", campaignId);
            return new List<NotificationDeliveryDto>();
        }
    }

    public async Task<CampaignPerformanceDto> GetCampaignPerformanceAsync(Guid campaignId, CancellationToken cancellationToken = default)
    {
        try
        {
            var campaign = await _campaignRepository.GetByIdAsync(campaignId, cancellationToken);
            if (campaign == null)
            {
                return new CampaignPerformanceDto { CampaignId = campaignId };
            }

            var deliveries = await _deliveryRepository.GetByCampaignIdAsync(campaignId, null, cancellationToken);

            var performance = new CampaignPerformanceDto
            {
                CampaignId = campaignId,
                CampaignName = campaign.Name,
                Status = campaign.Status,
                EstimatedRecipients = campaign.EstimatedRecipients,
                ActualRecipients = campaign.ActualRecipients,
                DeliveredCount = campaign.DeliveredCount,
                FailedCount = campaign.FailedCount,
                OpenedCount = campaign.OpenedCount,
                ClickedCount = campaign.ClickedCount,
                DeliveryRate = campaign.GetDeliveryRate(),
                OpenRate = campaign.GetOpenRate(),
                ClickRate = campaign.GetClickRate(),
                ClickThroughRate = campaign.GetClickThroughRate(),
                CreatedAt = campaign.CreatedAt,
                StartedAt = campaign.StartedAt,
                CompletedAt = campaign.CompletedAt,
                Duration = campaign.GetDuration()?.TotalMinutes,
                PlatformBreakdown = deliveries.GroupBy(d => d.Platform).ToDictionary(g => g.Key, g => g.Count()),
                StatusBreakdown = deliveries.GroupBy(d => d.Status).ToDictionary(g => g.Key, g => g.Count()),
                HourlyDeliveries = deliveries.Where(d => d.SentAt.HasValue)
                    .GroupBy(d => d.SentAt!.Value.Hour)
                    .ToDictionary(g => g.Key, g => g.Count())
            };

            return performance;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting campaign performance for {CampaignId}", campaignId);
            return new CampaignPerformanceDto { CampaignId = campaignId };
        }
    }

    public async Task<bool> TrackNotificationOpenAsync(Guid deliveryId, CancellationToken cancellationToken = default)
    {
        try
        {
            var delivery = await _deliveryRepository.GetByIdAsync(deliveryId, cancellationToken);
            if (delivery == null)
            {
                return false;
            }

            delivery.MarkAsOpened();
            await _deliveryRepository.SaveChangesAsync(cancellationToken);

            // Update campaign metrics
            var campaign = await _campaignRepository.GetByIdAsync(delivery.CampaignId, cancellationToken);
            if (campaign != null)
            {
                campaign.RecordOpen();
                await _campaignRepository.SaveChangesAsync(cancellationToken);
            }

            _logger.LogDebug("Notification open tracked for delivery {DeliveryId}", deliveryId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error tracking notification open for delivery {DeliveryId}", deliveryId);
            return false;
        }
    }

    public async Task<bool> TrackNotificationClickAsync(Guid deliveryId, CancellationToken cancellationToken = default)
    {
        try
        {
            var delivery = await _deliveryRepository.GetByIdAsync(deliveryId, cancellationToken);
            if (delivery == null)
            {
                return false;
            }

            delivery.MarkAsClicked();
            await _deliveryRepository.SaveChangesAsync(cancellationToken);

            // Update campaign metrics
            var campaign = await _campaignRepository.GetByIdAsync(delivery.CampaignId, cancellationToken);
            if (campaign != null)
            {
                campaign.RecordClick();
                await _campaignRepository.SaveChangesAsync(cancellationToken);
            }

            _logger.LogDebug("Notification click tracked for delivery {DeliveryId}", deliveryId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error tracking notification click for delivery {DeliveryId}", deliveryId);
            return false;
        }
    }

    public async Task ProcessScheduledCampaignsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var scheduledCampaigns = await _campaignRepository.GetScheduledForNowAsync(cancellationToken);

            foreach (var campaign in scheduledCampaigns)
            {
                try
                {
                    await StartCampaignAsync(campaign.Id, cancellationToken);
                    _logger.LogInformation("Scheduled campaign {CampaignId} started automatically", campaign.Id);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error starting scheduled campaign {CampaignId}", campaign.Id);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing scheduled campaigns");
        }
    }

    public async Task ProcessRetryDeliveriesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var maxRetries = _configuration.GetValue<int>("Notifications:MaxRetries", 3);
            var retryDeliveries = await _deliveryRepository.GetRetryableAsync(maxRetries, cancellationToken);

            foreach (var delivery in retryDeliveries)
            {
                try
                {
                    var result = await _providerService.SendNotificationAsync(
                        delivery.DeviceToken,
                        delivery.Platform,
                        delivery.Content,
                        cancellationToken);

                    if (result.IsSuccess)
                    {
                        delivery.MarkAsSent(result.ExternalId);
                        _logger.LogDebug("Retry delivery {DeliveryId} sent successfully", delivery.Id);
                    }
                    else
                    {
                        if (delivery.RetryCount >= maxRetries - 1)
                        {
                            delivery.MarkAsFailed($"Max retries exceeded: {result.ErrorMessage}", result.ErrorCode);
                        }
                        else
                        {
                            var retryDelay = TimeSpan.FromMinutes(Math.Pow(2, delivery.RetryCount)); // Exponential backoff
                            delivery.ScheduleRetry(retryDelay);
                        }
                    }

                    delivery.SetProviderResponse(result.ProviderResponse);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing retry delivery {DeliveryId}", delivery.Id);
                    delivery.MarkAsFailed($"Retry failed: {ex.Message}");
                }
            }

            if (retryDeliveries.Any())
            {
                await _deliveryRepository.SaveChangesAsync(cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing retry deliveries");
        }
    }

    private async Task CreateDeliveriesAsync(NotificationCampaign campaign, List<UserDeviceInfo> recipients, CancellationToken cancellationToken)
    {
        var template = await _templateRepository.GetByIdAsync(campaign.TemplateId, cancellationToken);
        if (template == null)
        {
            throw new InvalidOperationException("Template not found");
        }

        var deliveries = new List<NotificationDelivery>();

        foreach (var recipient in recipients)
        {
            try
            {
                // Render content for this recipient
                var renderedContent = template.RenderContent(campaign.Variables, recipient.Platform);

                var delivery = new NotificationDelivery(
                    campaign.Id,
                    recipient.UserId,
                    recipient.DeviceToken,
                    recipient.Platform,
                    renderedContent);

                deliveries.Add(delivery);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating delivery for user {UserId}", recipient.UserId);
            }
        }

        foreach (var delivery in deliveries)
        {
            _deliveryRepository.Add(delivery);
        }

        await _deliveryRepository.SaveChangesAsync(cancellationToken);
        _logger.LogInformation("Created {DeliveryCount} deliveries for campaign {CampaignId}", deliveries.Count, campaign.Id);
    }

    private async Task ProcessCampaignDeliveriesAsync(Guid campaignId, CancellationToken cancellationToken)
    {
        try
        {
            var campaign = await _campaignRepository.GetByIdAsync(campaignId, cancellationToken);
            if (campaign == null || campaign.Status != "Running")
            {
                return;
            }

            var batchSize = campaign.GetConfiguration<int>("batch_size", 1000);
            var delayBetweenBatches = campaign.GetConfiguration<int>("delay_between_batches", 1000);

            var pendingDeliveries = await _deliveryRepository.GetPendingByCampaignIdAsync(campaignId, cancellationToken);
            var batches = pendingDeliveries.Chunk(batchSize);

            foreach (var batch in batches)
            {
                // Check if campaign is still running
                campaign = await _campaignRepository.GetByIdAsync(campaignId, cancellationToken);
                if (campaign?.Status != "Running")
                {
                    break;
                }

                await ProcessDeliveryBatchAsync(batch.ToList(), cancellationToken);

                if (delayBetweenBatches > 0)
                {
                    await Task.Delay(delayBetweenBatches, cancellationToken);
                }
            }

            // Check if campaign is complete
            var remainingDeliveries = await _deliveryRepository.GetPendingByCampaignIdAsync(campaignId, cancellationToken);
            if (!remainingDeliveries.Any())
            {
                campaign = await _campaignRepository.GetByIdAsync(campaignId, cancellationToken);
                if (campaign?.Status == "Running")
                {
                    campaign.Complete();
                    await _campaignRepository.SaveChangesAsync(cancellationToken);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing campaign deliveries for {CampaignId}", campaignId);
        }
    }

    private async Task ProcessDeliveryBatchAsync(List<NotificationDelivery> deliveries, CancellationToken cancellationToken)
    {
        var tasks = deliveries.Select(async delivery =>
        {
            try
            {
                var result = await _providerService.SendNotificationAsync(
                    delivery.DeviceToken,
                    delivery.Platform,
                    delivery.Content,
                    cancellationToken);

                if (result.IsSuccess)
                {
                    delivery.MarkAsSent(result.ExternalId);
                }
                else
                {
                    delivery.MarkAsFailed(result.ErrorMessage, result.ErrorCode);
                }

                delivery.SetProviderResponse(result.ProviderResponse);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending notification for delivery {DeliveryId}", delivery.Id);
                delivery.MarkAsFailed($"Send failed: {ex.Message}");
            }
        });

        await Task.WhenAll(tasks);
        await _deliveryRepository.SaveChangesAsync(cancellationToken);

        _logger.LogDebug("Processed batch of {BatchSize} deliveries", deliveries.Count);
    }

    // Mapping methods
    private NotificationTemplateDto MapToNotificationTemplateDto(NotificationTemplate template)
    {
        return new NotificationTemplateDto
        {
            Id = template.Id,
            Name = template.Name,
            DisplayName = template.DisplayName,
            Description = template.Description,
            Category = template.Category,
            Type = template.Type,
            IsActive = template.IsActive,
            Content = template.Content,
            Styling = template.Styling,
            Configuration = template.Configuration,
            SupportedPlatforms = template.SupportedPlatforms,
            PlatformSpecific = template.PlatformSpecific,
            Variables = template.Variables,
            DefaultValues = template.DefaultValues,
            PreviewUrl = template.PreviewUrl,
            CreatedAt = template.CreatedAt,
            UpdatedAt = template.UpdatedAt,
            CreatedBy = template.CreatedBy,
            UpdatedBy = template.UpdatedBy
        };
    }

    private NotificationCampaignDto MapToNotificationCampaignDto(NotificationCampaign campaign)
    {
        return new NotificationCampaignDto
        {
            Id = campaign.Id,
            Name = campaign.Name,
            DisplayName = campaign.DisplayName,
            Description = campaign.Description,
            TemplateId = campaign.TemplateId,
            Status = campaign.Status,
            Type = campaign.Type,
            ScheduledAt = campaign.ScheduledAt,
            StartedAt = campaign.StartedAt,
            CompletedAt = campaign.CompletedAt,
            TargetingCriteria = campaign.TargetingCriteria,
            Variables = campaign.Variables,
            Configuration = campaign.Configuration,
            TargetPlatforms = campaign.TargetPlatforms,
            EstimatedRecipients = campaign.EstimatedRecipients,
            ActualRecipients = campaign.ActualRecipients,
            DeliveredCount = campaign.DeliveredCount,
            FailedCount = campaign.FailedCount,
            OpenedCount = campaign.OpenedCount,
            ClickedCount = campaign.ClickedCount,
            DeliveryRate = campaign.GetDeliveryRate(),
            OpenRate = campaign.GetOpenRate(),
            ClickRate = campaign.GetClickRate(),
            ClickThroughRate = campaign.GetClickThroughRate(),
            CreatedAt = campaign.CreatedAt,
            UpdatedAt = campaign.UpdatedAt,
            CreatedBy = campaign.CreatedBy,
            UpdatedBy = campaign.UpdatedBy,
            Duration = campaign.GetDuration()?.TotalMinutes,
            IsActive = campaign.IsActive(),
            CanBeModified = campaign.CanBeModified()
        };
    }

    private NotificationDeliveryDto MapToNotificationDeliveryDto(NotificationDelivery delivery)
    {
        return new NotificationDeliveryDto
        {
            Id = delivery.Id,
            CampaignId = delivery.CampaignId,
            UserId = delivery.UserId,
            DeviceToken = delivery.DeviceToken,
            Platform = delivery.Platform,
            Status = delivery.Status,
            Content = delivery.Content,
            CreatedAt = delivery.CreatedAt,
            SentAt = delivery.SentAt,
            DeliveredAt = delivery.DeliveredAt,
            OpenedAt = delivery.OpenedAt,
            ClickedAt = delivery.ClickedAt,
            ErrorMessage = delivery.ErrorMessage,
            ErrorCode = delivery.ErrorCode,
            RetryCount = delivery.RetryCount,
            NextRetryAt = delivery.NextRetryAt,
            ExternalId = delivery.ExternalId,
            ProviderResponse = delivery.ProviderResponse,
            DeliveryTimeMs = delivery.GetDeliveryTime()?.TotalMilliseconds,
            TimeToOpenMs = delivery.GetTimeToOpen()?.TotalMilliseconds,
            TimeToClickMs = delivery.GetTimeToClick()?.TotalMilliseconds,
            IsSuccessful = delivery.IsSuccessful(),
            IsEngaged = delivery.IsEngaged()
        };
    }
}

// Supporting interfaces and classes
public interface INotificationProviderService
{
    Task<NotificationSendResult> SendNotificationAsync(string deviceToken, string platform, Dictionary<string, object> content, CancellationToken cancellationToken = default);
}

public interface IUserTargetingService
{
    Task<int> EstimateRecipientsAsync(Dictionary<string, object> targetingCriteria, List<string> platforms, CancellationToken cancellationToken = default);
    Task<List<UserDeviceInfo>> GetRecipientsAsync(Dictionary<string, object> targetingCriteria, List<string> platforms, CancellationToken cancellationToken = default);
}

public class NotificationSendResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public string? ErrorCode { get; set; }
    public string? ExternalId { get; set; }
    public Dictionary<string, object> ProviderResponse { get; set; } = new();
}

public class UserDeviceInfo
{
    public Guid UserId { get; set; }
    public string DeviceToken { get; set; } = string.Empty;
    public string Platform { get; set; } = string.Empty;
    public Dictionary<string, object> UserAttributes { get; set; } = new();
}
