using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Domain.Common;
using SubscriptionManagement.Application.DTOs;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Interfaces;
using SubscriptionManagement.Domain.ValueObjects;

namespace SubscriptionManagement.Application.Commands.UploadPaymentProof
{
    public class UploadPaymentProofCommandHandler : IRequestHandler<UploadPaymentProofCommand, UploadPaymentProofResponse>
    {
        private readonly ISubscriptionRepository _subscriptionRepository;
        private readonly ISubscriptionPaymentProofRepository _paymentProofRepository;
        private readonly IPaymentProofFileService _fileService;
        private readonly ILogger<UploadPaymentProofCommandHandler> _logger;

        public UploadPaymentProofCommandHandler(
            ISubscriptionRepository subscriptionRepository,
            ISubscriptionPaymentProofRepository paymentProofRepository,
            IPaymentProofFileService fileService,
            ILogger<UploadPaymentProofCommandHandler> logger)
        {
            _subscriptionRepository = subscriptionRepository;
            _paymentProofRepository = paymentProofRepository;
            _fileService = fileService;
            _logger = logger;
        }

        public async Task<UploadPaymentProofResponse> Handle(UploadPaymentProofCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Processing payment proof upload for subscription {SubscriptionId} by user {UserId}",
                request.SubscriptionId, request.UserId);

            // Validate subscription exists and belongs to user
            var subscription = await _subscriptionRepository.GetByIdAsync(request.SubscriptionId);
            if (subscription == null)
            {
                throw new ArgumentException($"Subscription with ID {request.SubscriptionId} not found");
            }

            if (subscription.UserId != request.UserId)
            {
                throw new UnauthorizedAccessException("User is not authorized to upload payment proof for this subscription");
            }

            // Check if there's already a pending payment proof for this subscription
            var hasPendingProof = await _paymentProofRepository.HasPendingProofForSubscriptionAsync(request.SubscriptionId, cancellationToken);
            if (hasPendingProof)
            {
                throw new InvalidOperationException("There is already a pending payment proof for this subscription");
            }

            // Validate file
            if (request.ProofImage == null || request.ProofImage.Length == 0)
            {
                throw new ArgumentException("Proof image is required");
            }

            if (!_fileService.IsValidFileType(request.ProofImage.ContentType))
            {
                throw new ArgumentException($"File type '{request.ProofImage.ContentType}' is not allowed");
            }

            if (!_fileService.IsValidFileSize(request.ProofImage.Length))
            {
                throw new ArgumentException("File size exceeds the maximum allowed limit");
            }

            try
            {
                // Upload the file
                var proofImageUrl = await _fileService.UploadPaymentProofAsync(
                    request.ProofImage,
                    request.SubscriptionId,
                    request.UserId,
                    cancellationToken);

                // Create payment proof entity
                var amount = new Money(request.Amount, request.Currency);
                var paymentProof = SubscriptionPaymentProof.Create(
                    request.SubscriptionId,
                    request.UserId,
                    amount,
                    request.PaymentDate,
                    proofImageUrl,
                    request.Notes,
                    request.PaymentMethod,
                    request.TransactionReference);

                // Save to database
                await _paymentProofRepository.AddAsync(paymentProof, cancellationToken);

                _logger.LogInformation("Payment proof uploaded successfully with ID {PaymentProofId} for subscription {SubscriptionId}",
                    paymentProof.Id, request.SubscriptionId);

                return new UploadPaymentProofResponse
                {
                    PaymentProofId = paymentProof.Id,
                    ProofImageUrl = proofImageUrl,
                    Status = paymentProof.Status,
                    Message = "Payment proof uploaded successfully and is pending review"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to upload payment proof for subscription {SubscriptionId}", request.SubscriptionId);
                throw new InvalidOperationException($"Failed to upload payment proof: {ex.Message}", ex);
            }
        }
    }
}

