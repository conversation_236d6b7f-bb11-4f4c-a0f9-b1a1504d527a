# Server 2 - Applications Setup Guide

## Overview

Server 2 hosts all TLI microservices and the API Gateway. This server connects to the infrastructure services running on Server 1 and serves as the application layer for the TLI platform.

## Server Specifications

### Minimum Requirements

- **CPU**: 8 cores
- **RAM**: 16GB
- **Storage**: 200GB SSD
- **OS**: Ubuntu 24.04 LTS

### Recommended Requirements

- **CPU**: 16 cores
- **RAM**: 32GB
- **Storage**: 500GB SSD
- **Network**: 1Gbps connection

## Step 1: Initial Server Setup

### 1.1 Update System

```bash
# Update package lists and upgrade system
sudo apt update && sudo apt upgrade -y

# Install essential packages
sudo apt install -y curl wget git unzip software-properties-common apt-transport-https ca-certificates gnupg lsb-release

# Set timezone (adjust as needed)
sudo timedatectl set-timezone Asia/Kolkata

# Configure hostname
sudo hostnamectl set-hostname tli-applications
```

### 1.2 Create TLI User

```bash
# Create dedicated user for TLI services
sudo useradd -m -s /bin/bash tli
sudo usermod -aG sudo tli

# Set password for tli user
sudo passwd tli

# Create directories
sudo mkdir -p /opt/tli/{apps,logs,config,data}
sudo chown -R tli:tli /opt/tli
```

### 1.3 Configure Firewall

```bash
# Enable UFW firewall
sudo ufw enable

# Allow SSH
sudo ufw allow ssh

# Allow HTTP and HTTPS
sudo ufw allow 80
sudo ufw allow 443

# Allow API Gateway
sudo ufw allow 5000

# Allow individual microservices (for direct access if needed)
sudo ufw allow 5001:5013/tcp

# Allow from admin networks only
sudo ufw allow from ADMIN_NETWORK to any port 5001:5013

# Check firewall status
sudo ufw status verbose
```

## Step 2: Install Docker and Docker Compose

### 2.1 Install Docker

```bash
# Add Docker's official GPG key
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

# Add Docker repository
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# Update package lists
sudo apt update

# Install Docker
sudo apt install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin

# Add tli user to docker group
sudo usermod -aG docker tli

# Enable and start Docker
sudo systemctl enable docker
sudo systemctl start docker

# Verify installation
docker --version
docker compose version
```

### 2.2 Configure Docker

```bash
# Create Docker daemon configuration
sudo tee /etc/docker/daemon.json > /dev/null <<EOF
{
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  },
  "storage-driver": "overlay2",
  "data-root": "/opt/tli/data/docker"
}
EOF

# Restart Docker
sudo systemctl restart docker
```

## Step 3: Install .NET 8 SDK

### 3.1 Install .NET 8

```bash
# Add Microsoft package repository
wget https://packages.microsoft.com/config/ubuntu/24.04/packages-microsoft-prod.deb -O packages-microsoft-prod.deb
sudo dpkg -i packages-microsoft-prod.deb
rm packages-microsoft-prod.deb

# Update package lists
sudo apt update

# Install .NET 8 SDK
sudo apt install -y dotnet-sdk-8.0

# Verify installation
dotnet --version
```

## Step 4: Clone and Setup TLI Repository

### 4.1 Clone Repository

```bash
# Switch to tli user
sudo su - tli

# Clone the TLI repository
cd /opt/tli/apps
git clone https://github.com/your-org/TLIMicroservices.git
cd TLIMicroservices

# Verify repository structure
ls -la
```

### 4.2 Configure Environment Variables

```bash
# Create environment configuration file
cat > /opt/tli/config/tli-environment.env << 'EOF'
# Infrastructure Server Configuration
SERVER1_IP=**********  # Replace with actual Server 1 IP

# Database Configuration
POSTGRES_HOST=${SERVER1_IP}
POSTGRES_PORT=5432
POSTGRES_USER=tli_admin
POSTGRES_PASSWORD=*****************

# Redis Configuration
REDIS_HOST=${SERVER1_IP}
REDIS_PORT=6379
REDIS_PASSWORD=TLI_Redis_2024!@#

# RabbitMQ Configuration
RABBITMQ_HOST=${SERVER1_IP}
RABBITMQ_PORT=5672
RABBITMQ_USER=tli_service
RABBITMQ_PASSWORD=TLI_Service_2024!@#
RABBITMQ_VHOST=tli

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-that-is-at-least-32-characters-long-for-production
JWT_ISSUER=TLI.Identity
JWT_AUDIENCE=TLI.Services
JWT_EXPIRY_MINUTES=60

# Application Environment
ASPNETCORE_ENVIRONMENT=Production
ASPNETCORE_URLS=http://+:80

# Logging Configuration
LOGGING_LEVEL=Information
SERILOG_MINIMUM_LEVEL=Information

# External Services (Configure as needed)
GOOGLE_MAPS_API_KEY=your-google-maps-api-key
RAZORPAY_KEY_ID=your-razorpay-key-id
RAZORPAY_KEY_SECRET=your-razorpay-key-secret
SMS_PROVIDER_API_KEY=your-sms-provider-api-key
EMAIL_SMTP_HOST=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_SMTP_USER=<EMAIL>
EMAIL_SMTP_PASSWORD=your-email-password
EOF

# Set proper permissions
chmod 600 /opt/tli/config/tli-environment.env
```

## Step 5: Build TLI Microservices

### 5.1 Build All Services

```bash
# Navigate to repository
cd /opt/tli/apps/TLIMicroservices

# Create build script
cat > /opt/tli/build-services.sh << 'EOF'
#!/bin/bash

echo "Building TLI Microservices..."
cd /opt/tli/apps/TLIMicroservices

# Build API Gateway
echo "Building API Gateway..."
docker build -t tli/apigateway:latest -f ApiGateway/Dockerfile .

# Build Identity Service
echo "Building Identity Service..."
docker build -t tli/identity:latest -f Identity/Identity.API/Dockerfile .

# Build User Management Service
echo "Building User Management Service..."
docker build -t tli/usermanagement:latest -f Services/UserManagement/UserManagement.API/Dockerfile .

# Build Subscription Management Service
echo "Building Subscription Management Service..."
docker build -t tli/subscription:latest -f Services/SubscriptionManagement/Dockerfile .

# Build Order Management Service
echo "Building Order Management Service..."
docker build -t tli/order:latest -f Services/OrderManagement/Dockerfile .

# Build Trip Management Service
echo "Building Trip Management Service..."
docker build -t tli/trip:latest -f Services/TripManagement/TripManagement.API/Dockerfile .

# Build Network Fleet Management Service
echo "Building Network Fleet Management Service..."
docker build -t tli/fleet:latest -f Services/NetworkFleetManagement/Dockerfile .

# Build Financial Payment Service
echo "Building Financial Payment Service..."
docker build -t tli/payment:latest -f Services/FinancialPayment/Dockerfile .

# Build Communication Notification Service
echo "Building Communication Notification Service..."
docker build -t tli/communication:latest -f Services/CommunicationNotification/CommunicationNotification.API/Dockerfile .

# Build Analytics BI Service
echo "Building Analytics BI Service..."
docker build -t tli/analytics:latest -f Services/AnalyticsBIService/Dockerfile .

# Build Data Storage Service
echo "Building Data Storage Service..."
docker build -t tli/datastorage:latest -f Services/DataStorage/DataStorage.API/Dockerfile .

# Build Audit Compliance Service
echo "Building Audit Compliance Service..."
docker build -t tli/audit:latest -f Services/AuditCompliance/Dockerfile .

# Build Monitoring Observability Service
echo "Building Monitoring Observability Service..."
docker build -t tli/monitoring:latest -f Services/MonitoringObservability/MonitoringObservability.API/Dockerfile .

# Build Mobile Workflow Service
echo "Building Mobile Workflow Service..."
docker build -t tli/mobile:latest -f Services/MobileWorkflow/MobileWorkflow.API/Dockerfile .

echo "All services built successfully!"
EOF

chmod +x /opt/tli/build-services.sh

# Run the build
/opt/tli/build-services.sh
```

## Step 6: Create Production Docker Compose

### 6.1 Production Compose Configuration

```bash
# Create production docker compose file
cat > /opt/tli/tli-microservices.yml << 'EOF'
version: '3.8'

services:
  # API Gateway
  apigateway:
    image: tli/apigateway:latest
    container_name: tli-apigateway
    restart: unless-stopped
    ports:
      - "5000:80"
      - "5443:443"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
    env_file:
      - /opt/tli/config/tli-environment.env
    volumes:
      - /opt/tli/logs/apigateway:/app/logs
      - /opt/tli/config/ssl:/app/ssl:ro
    networks:
      - tli-applications
    depends_on:
      - identity
      - usermanagement
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Identity Service
  identity:
    image: tli/identity:latest
    container_name: tli-identity
    restart: unless-stopped
    ports:
      - "5001:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Host=${SERVER1_IP};Port=5432;Database=TLI_Identity;User Id=tli_admin;Password=*****************
    env_file:
      - /opt/tli/config/tli-environment.env
    volumes:
      - /opt/tli/logs/identity:/app/logs
    networks:
      - tli-applications
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # User Management Service
  usermanagement:
    image: tli/usermanagement:latest
    container_name: tli-usermanagement
    restart: unless-stopped
    ports:
      - "5002:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Host=${SERVER1_IP};Port=5432;Database=TLI_UserManagement;User Id=tli_admin;Password=*****************
    env_file:
      - /opt/tli/config/tli-environment.env
    volumes:
      - /opt/tli/logs/usermanagement:/app/logs
    networks:
      - tli-applications
    depends_on:
      - identity
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Subscription Management Service
  subscription:
    image: tli/subscription:latest
    container_name: tli-subscription
    restart: unless-stopped
    ports:
      - "5003:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Host=${SERVER1_IP};Port=5432;Database=TLI_SubscriptionManagement;User Id=tli_admin;Password=*****************
    env_file:
      - /opt/tli/config/tli-environment.env
    volumes:
      - /opt/tli/logs/subscription:/app/logs
    networks:
      - tli-applications
    depends_on:
      - identity
      - usermanagement
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Order Management Service
  order:
    image: tli/order:latest
    container_name: tli-order
    restart: unless-stopped
    ports:
      - "5004:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Host=${SERVER1_IP};Port=5432;Database=TLI_OrderManagement;User Id=tli_admin;Password=*****************
    env_file:
      - /opt/tli/config/tli-environment.env
    volumes:
      - /opt/tli/logs/order:/app/logs
    networks:
      - tli-applications
    depends_on:
      - identity
      - subscription
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Trip Management Service
  trip:
    image: tli/trip:latest
    container_name: tli-trip
    restart: unless-stopped
    ports:
      - "5005:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Host=${SERVER1_IP};Port=5432;Database=TLI_TripManagement;User Id=tli_admin;Password=*****************
    env_file:
      - /opt/tli/config/tli-environment.env
    volumes:
      - /opt/tli/logs/trip:/app/logs
    networks:
      - tli-applications
    depends_on:
      - identity
      - order
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Network Fleet Management Service
  fleet:
    image: tli/fleet:latest
    container_name: tli-fleet
    restart: unless-stopped
    ports:
      - "5006:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Host=${SERVER1_IP};Port=5432;Database=TLI_NetworkFleetManagement;User Id=tli_admin;Password=*****************
    env_file:
      - /opt/tli/config/tli-environment.env
    volumes:
      - /opt/tli/logs/fleet:/app/logs
    networks:
      - tli-applications
    depends_on:
      - identity
      - usermanagement
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Financial Payment Service
  payment:
    image: tli/payment:latest
    container_name: tli-payment
    restart: unless-stopped
    ports:
      - "5007:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Host=${SERVER1_IP};Port=5432;Database=TLI_FinancialPayment;User Id=tli_admin;Password=*****************
    env_file:
      - /opt/tli/config/tli-environment.env
    volumes:
      - /opt/tli/logs/payment:/app/logs
    networks:
      - tli-applications
    depends_on:
      - identity
      - order
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Communication Notification Service
  communication:
    image: tli/communication:latest
    container_name: tli-communication
    restart: unless-stopped
    ports:
      - "5008:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Host=${SERVER1_IP};Port=5432;Database=TLI_CommunicationNotification;User Id=tli_admin;Password=*****************
    env_file:
      - /opt/tli/config/tli-environment.env
    volumes:
      - /opt/tli/logs/communication:/app/logs
    networks:
      - tli-applications
    depends_on:
      - identity
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
  # Analytics BI Service
  analytics:
    image: tli/analytics:latest
    container_name: tli-analytics
    restart: unless-stopped
    ports:
      - "5009:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Host=${SERVER1_IP};Port=5432;Database=TLI_AnalyticsBIService;User Id=tli_admin;Password=*****************
    env_file:
      - /opt/tli/config/tli-environment.env
    volumes:
      - /opt/tli/logs/analytics:/app/logs
    networks:
      - tli-applications
    depends_on:
      - identity
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Data Storage Service
  datastorage:
    image: tli/datastorage:latest
    container_name: tli-datastorage
    restart: unless-stopped
    ports:
      - "5010:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Host=${SERVER1_IP};Port=5432;Database=TLI_DataStorage;User Id=tli_admin;Password=*****************
    env_file:
      - /opt/tli/config/tli-environment.env
    volumes:
      - /opt/tli/logs/datastorage:/app/logs
      - /opt/tli/data/storage:/app/storage
    networks:
      - tli-applications
    depends_on:
      - identity
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Audit Compliance Service
  audit:
    image: tli/audit:latest
    container_name: tli-audit
    restart: unless-stopped
    ports:
      - "5011:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Host=${SERVER1_IP};Port=5432;Database=TLI_AuditCompliance;User Id=tli_admin;Password=*****************
    env_file:
      - /opt/tli/config/tli-environment.env
    volumes:
      - /opt/tli/logs/audit:/app/logs
    networks:
      - tli-applications
    depends_on:
      - identity
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring Observability Service
  monitoring:
    image: tli/monitoring:latest
    container_name: tli-monitoring
    restart: unless-stopped
    ports:
      - "5012:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Host=${SERVER1_IP};Port=5432;Database=TLI_MonitoringObservability;User Id=tli_admin;Password=*****************
    env_file:
      - /opt/tli/config/tli-environment.env
    volumes:
      - /opt/tli/logs/monitoring:/app/logs
    networks:
      - tli-applications
    depends_on:
      - identity
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Mobile Workflow Service
  mobile:
    image: tli/mobile:latest
    container_name: tli-mobile
    restart: unless-stopped
    ports:
      - "5013:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Host=${SERVER1_IP};Port=5432;Database=TLI_MobileWorkflow;User Id=tli_admin;Password=*****************
    env_file:
      - /opt/tli/config/tli-environment.env
    volumes:
      - /opt/tli/logs/mobile:/app/logs
    networks:
      - tli-applications
    depends_on:
      - identity
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  tli-applications:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  storage_data:
EOF
```

## Step 7: Database Migrations

### 7.1 Run Database Migrations

```bash
# Create migration script
cat > /opt/tli/run-migrations.sh << 'EOF'
#!/bin/bash

echo "Running TLI Database Migrations..."

# Set environment variables
export SERVER1_IP="**********"  # Replace with actual Server 1 IP
export POSTGRES_CONNECTION="Host=${SERVER1_IP};Port=5432;User Id=tli_admin;Password=*****************"

# Array of services and their databases
declare -A SERVICES=(
  ["identity"]="TLI_Identity"
  ["usermanagement"]="TLI_UserManagement"
  ["subscription"]="TLI_SubscriptionManagement"
  ["order"]="TLI_OrderManagement"
  ["trip"]="TLI_TripManagement"
  ["fleet"]="TLI_NetworkFleetManagement"
  ["payment"]="TLI_FinancialPayment"
  ["communication"]="TLI_CommunicationNotification"
  ["analytics"]="TLI_AnalyticsBIService"
  ["datastorage"]="TLI_DataStorage"
  ["audit"]="TLI_AuditCompliance"
  ["monitoring"]="TLI_MonitoringObservability"
  ["mobile"]="TLI_MobileWorkflow"
)

cd /opt/tli/apps/TLIMicroservices

for service in "${!SERVICES[@]}"; do
  database="${SERVICES[$service]}"
  echo "Running migrations for $service ($database)..."

  case $service in
    "identity")
      docker run --rm -v $(pwd):/app -w /app/Identity/Identity.API \
        -e ConnectionStrings__DefaultConnection="${POSTGRES_CONNECTION};Database=${database}" \
        mcr.microsoft.com/dotnet/sdk:8.0 \
        dotnet ef database update --no-build
      ;;
    "usermanagement")
      docker run --rm -v $(pwd):/app -w /app/Services/UserManagement/UserManagement.API \
        -e ConnectionStrings__DefaultConnection="${POSTGRES_CONNECTION};Database=${database}" \
        mcr.microsoft.com/dotnet/sdk:8.0 \
        dotnet ef database update --no-build
      ;;
    *)
      echo "Migration for $service not implemented yet"
      ;;
  esac
done

echo "Database migrations completed!"
EOF

chmod +x /opt/tli/run-migrations.sh

# Run migrations
/opt/tli/run-migrations.sh
```

## Step 8: Application Management

### 8.1 Create Management Scripts

```bash
# Create application management script
cat > /opt/tli/manage-applications.sh << 'EOF'
#!/bin/bash

SCRIPT_DIR="/opt/tli"
COMPOSE_FILE="$SCRIPT_DIR/tli-microservices.yml"

case "$1" in
  start)
    echo "Starting TLI Microservices..."
    cd $SCRIPT_DIR
    docker compose -f $COMPOSE_FILE up -d
    echo "TLI Microservices started successfully!"
    ;;
  stop)
    echo "Stopping TLI Microservices..."
    cd $SCRIPT_DIR
    docker compose -f $COMPOSE_FILE down
    echo "TLI Microservices stopped successfully!"
    ;;
  restart)
    echo "Restarting TLI Microservices..."
    cd $SCRIPT_DIR
    docker compose -f $COMPOSE_FILE down
    sleep 10
    docker compose -f $COMPOSE_FILE up -d
    echo "TLI Microservices restarted successfully!"
    ;;
  status)
    echo "TLI Microservices Status:"
    cd $SCRIPT_DIR
    docker compose -f $COMPOSE_FILE ps
    ;;
  logs)
    if [ -z "$2" ]; then
      echo "TLI Microservices Logs (All Services):"
      cd $SCRIPT_DIR
      docker compose -f $COMPOSE_FILE logs -f
    else
      echo "TLI Microservices Logs ($2):"
      cd $SCRIPT_DIR
      docker compose -f $COMPOSE_FILE logs -f $2
    fi
    ;;
  build)
    echo "Building TLI Microservices..."
    /opt/tli/build-services.sh
    ;;
  update)
    echo "Updating TLI Microservices..."
    cd /opt/tli/apps/TLIMicroservices
    git pull
    /opt/tli/build-services.sh
    cd $SCRIPT_DIR
    docker compose -f $COMPOSE_FILE down
    docker compose -f $COMPOSE_FILE up -d
    echo "TLI Microservices updated successfully!"
    ;;
  *)
    echo "Usage: $0 {start|stop|restart|status|logs [service]|build|update}"
    exit 1
    ;;
esac
EOF

chmod +x /opt/tli/manage-applications.sh

# Create systemd service for applications
sudo tee /etc/systemd/system/tli-applications.service > /dev/null << 'EOF'
[Unit]
Description=TLI Applications Stack
Requires=docker.service
After=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
User=tli
Group=tli
WorkingDirectory=/opt/tli
ExecStart=/opt/tli/manage-applications.sh start
ExecStop=/opt/tli/manage-applications.sh stop
TimeoutStartSec=0

[Install]
WantedBy=multi-user.target
EOF

# Enable and start the service
sudo systemctl daemon-reload
sudo systemctl enable tli-applications.service
```

## Step 9: Health Checks and Monitoring

### 9.1 Create Health Check Script

```bash
# Create comprehensive health check script
cat > /opt/tli/health-check.sh << 'EOF'
#!/bin/bash

echo "=== TLI Applications Health Check ==="
echo "Date: $(date)"
echo

# Check Docker
echo "1. Docker Status:"
systemctl is-active docker
echo

# Check connectivity to Server 1
echo "2. Infrastructure Connectivity:"
SERVER1_IP="**********"  # Replace with actual Server 1 IP

# Test PostgreSQL connectivity
echo "  - PostgreSQL:"
nc -z $SERVER1_IP 5432 && echo "    ✓ PostgreSQL is reachable" || echo "    ✗ PostgreSQL is not reachable"

# Test Redis connectivity
echo "  - Redis:"
nc -z $SERVER1_IP 6379 && echo "    ✓ Redis is reachable" || echo "    ✗ Redis is not reachable"

# Test RabbitMQ connectivity
echo "  - RabbitMQ:"
nc -z $SERVER1_IP 5672 && echo "    ✓ RabbitMQ is reachable" || echo "    ✗ RabbitMQ is not reachable"

echo

# Check TLI Services
echo "3. TLI Services Status:"
SERVICES=(
  "apigateway:5000"
  "identity:5001"
  "usermanagement:5002"
  "subscription:5003"
  "order:5004"
  "trip:5005"
  "fleet:5006"
  "payment:5007"
  "communication:5008"
  "analytics:5009"
  "datastorage:5010"
  "audit:5011"
  "monitoring:5012"
  "mobile:5013"
)

for service_port in "${SERVICES[@]}"; do
  service=$(echo $service_port | cut -d: -f1)
  port=$(echo $service_port | cut -d: -f2)

  echo "  - $service:"

  # Check if container is running
  if docker ps --format "table {{.Names}}" | grep -q "tli-$service"; then
    echo "    ✓ Container is running"

    # Check if service is responding
    if curl -s -f http://localhost:$port/health > /dev/null 2>&1; then
      echo "    ✓ Service is healthy"
    else
      echo "    ⚠ Service is not responding to health checks"
    fi
  else
    echo "    ✗ Container is not running"
  fi
done

echo
echo "=== Health Check Complete ==="
EOF

chmod +x /opt/tli/health-check.sh
```

### 9.2 Create Log Monitoring Script

```bash
# Create log monitoring script
cat > /opt/tli/monitor-logs.sh << 'EOF'
#!/bin/bash

LOG_DIR="/opt/tli/logs"
ALERT_KEYWORDS=("ERROR" "FATAL" "Exception" "Failed")

echo "=== TLI Log Monitor ==="
echo "Monitoring logs for errors and exceptions..."
echo "Press Ctrl+C to stop"
echo

# Function to check logs for errors
check_logs() {
  local service=$1
  local log_file="$LOG_DIR/$service/app.log"

  if [ -f "$log_file" ]; then
    for keyword in "${ALERT_KEYWORDS[@]}"; do
      if tail -n 100 "$log_file" | grep -q "$keyword"; then
        echo "[$(date)] ALERT: $keyword found in $service logs"
        tail -n 5 "$log_file" | grep "$keyword" | tail -n 1
        echo "---"
      fi
    done
  fi
}

# Monitor logs continuously
while true; do
  for service in identity usermanagement subscription order trip fleet payment communication analytics datastorage audit monitoring mobile; do
    check_logs $service
  done
  sleep 30
done
EOF

chmod +x /opt/tli/monitor-logs.sh
```

## Step 10: SSL Configuration (Optional)

### 10.1 Setup SSL Certificates

```bash
# Create SSL directory
mkdir -p /opt/tli/config/ssl

# If you have SSL certificates, copy them here
# cp your-certificate.crt /opt/tli/config/ssl/
# cp your-private-key.key /opt/tli/config/ssl/

# For development, create self-signed certificates
openssl req -x509 -newkey rsa:4096 -keyout /opt/tli/config/ssl/tli.key -out /opt/tli/config/ssl/tli.crt -days 365 -nodes -subj "/C=IN/ST=State/L=City/O=TLI/CN=tli.local"

# Set proper permissions
chmod 600 /opt/tli/config/ssl/tli.key
chmod 644 /opt/tli/config/ssl/tli.crt
```

## Step 11: Start Applications

### 11.1 Deploy All Services

```bash
# Create log directories
mkdir -p /opt/tli/logs/{apigateway,identity,usermanagement,subscription,order,trip,fleet,payment,communication,analytics,datastorage,audit,monitoring,mobile}

# Create storage directory
mkdir -p /opt/tli/data/storage

# Set proper permissions
chown -R tli:tli /opt/tli/logs
chown -R tli:tli /opt/tli/data

# Start all services
/opt/tli/manage-applications.sh start

# Wait for services to start
sleep 60

# Check status
/opt/tli/manage-applications.sh status

# Run health check
/opt/tli/health-check.sh
```

## Step 12: Verification and Testing

### 12.1 Service Endpoints Testing

```bash
# Create endpoint testing script
cat > /opt/tli/test-endpoints.sh << 'EOF'
#!/bin/bash

echo "=== TLI Endpoints Testing ==="
echo "Date: $(date)"
echo

BASE_URL="http://localhost"
ENDPOINTS=(
  "5000:/health:API Gateway"
  "5001:/health:Identity Service"
  "5002:/health:User Management"
  "5003:/health:Subscription Management"
  "5004:/health:Order Management"
  "5005:/health:Trip Management"
  "5006:/health:Fleet Management"
  "5007:/health:Payment Service"
  "5008:/health:Communication Service"
  "5009:/health:Analytics Service"
  "5010:/health:Data Storage"
  "5011:/health:Audit Service"
  "5012:/health:Monitoring Service"
  "5013:/health:Mobile Workflow"
)

for endpoint in "${ENDPOINTS[@]}"; do
  port=$(echo $endpoint | cut -d: -f1)
  path=$(echo $endpoint | cut -d: -f2)
  name=$(echo $endpoint | cut -d: -f3)

  echo "Testing $name ($BASE_URL:$port$path):"

  response=$(curl -s -w "%{http_code}" -o /dev/null "$BASE_URL:$port$path")

  if [ "$response" = "200" ]; then
    echo "  ✓ Service is responding (HTTP $response)"
  else
    echo "  ✗ Service is not responding (HTTP $response)"
  fi
done

echo
echo "=== Endpoint Testing Complete ==="
EOF

chmod +x /opt/tli/test-endpoints.sh

# Run endpoint tests
/opt/tli/test-endpoints.sh
```

## Next Steps

1. **Configure Environment Variables**: Update `/opt/tli/config/tli-environment.env` with actual values
2. **Setup External Services**: Configure Google Maps API, payment gateways, SMS providers
3. **Network Configuration**: Follow the network configuration guide
4. **Security Setup**: Follow the security configuration guide
5. **Load Balancer**: Setup nginx or similar for load balancing
6. **Monitoring**: Configure monitoring and alerting

## Important Notes

- Replace `SERVER1_IP` with the actual IP address of Server 1
- Update all passwords and secrets for production use
- Configure external service API keys and credentials
- Setup proper SSL certificates for production
- Monitor resource usage and scale as needed
- Regular backups and updates are essential

## Troubleshooting

- Check logs: `/opt/tli/manage-applications.sh logs [service]`
- Check container status: `docker ps`
- Check connectivity: `nc -z SERVER1_IP PORT`
- Check health: `/opt/tli/health-check.sh`
- Monitor logs: `/opt/tli/monitor-logs.sh`
