using FluentAssertions;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Moq;
using MobileWorkflow.Domain.Entities;
using MobileWorkflow.Domain.Repositories;
using MobileWorkflow.Infrastructure.Services;
using Xunit;

namespace MobileWorkflow.Tests.Services;

public class CrossPlatformSyncServiceTests
{
    private readonly Mock<IOfflineDataRepository> _mockOfflineDataRepository;
    private readonly Mock<IMobileSessionRepository> _mockSessionRepository;
    private readonly Mock<ILogger<CrossPlatformSyncService>> _mockLogger;
    private readonly IMemoryCache _memoryCache;
    private readonly CrossPlatformSyncService _service;

    public CrossPlatformSyncServiceTests()
    {
        _mockOfflineDataRepository = new Mock<IOfflineDataRepository>();
        _mockSessionRepository = new Mock<IMobileSessionRepository>();
        _mockLogger = new Mock<ILogger<CrossPlatformSyncService>>();
        _memoryCache = new MemoryCache(new MemoryCacheOptions());
        
        _service = new CrossPlatformSyncService(
            _mockOfflineDataRepository.Object,
            _mockSessionRepository.Object,
            _memoryCache,
            _mockLogger.Object);
    }

    [Fact]
    public async Task SyncOfflineDataAsync_WithValidData_ShouldReturnSuccess()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var offlineData = new List<OfflineData>
        {
            CreateTestOfflineData(userId, "TripUpdate", 1),
            CreateTestOfflineData(userId, "PODUpload", 2)
        };

        _mockOfflineDataRepository
            .Setup(x => x.UpdateAsync(It.IsAny<OfflineData>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((OfflineData data, CancellationToken _) => data);

        // Act
        var result = await _service.SyncOfflineDataAsync(userId, offlineData);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.TotalItems.Should().Be(2);
        result.SuccessfulItems.Should().Be(2);
        result.FailedItems.Should().Be(0);
        result.UserId.Should().Be(userId);
    }

    [Fact]
    public async Task SyncOfflineDataAsync_WithEmptyData_ShouldReturnSuccess()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var offlineData = new List<OfflineData>();

        // Act
        var result = await _service.SyncOfflineDataAsync(userId, offlineData);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.TotalItems.Should().Be(0);
        result.SuccessfulItems.Should().Be(0);
        result.FailedItems.Should().Be(0);
    }

    [Fact]
    public async Task SyncUserDataAsync_WithActiveSession_ShouldReturnSuccess()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var userData = new Dictionary<string, object>
        {
            { "preference1", "value1" },
            { "preference2", "value2" }
        };
        var session = CreateTestMobileSession(userId);

        _mockSessionRepository
            .Setup(x => x.GetActiveSessionByUserIdAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(session);

        _mockSessionRepository
            .Setup(x => x.UpdateAsync(It.IsAny<MobileSession>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((MobileSession s, CancellationToken _) => s);

        // Act
        var result = await _service.SyncUserDataAsync(userId, userData);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.SuccessfulItems.Should().Be(2);
        result.UserId.Should().Be(userId);
    }

    [Fact]
    public async Task SyncUserDataAsync_WithNoActiveSession_ShouldReturnFailure()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var userData = new Dictionary<string, object> { { "test", "data" } };

        _mockSessionRepository
            .Setup(x => x.GetActiveSessionByUserIdAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((MobileSession?)null);

        // Act
        var result = await _service.SyncUserDataAsync(userId, userData);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeFalse();
        result.ErrorMessage.Should().Be("No active session found for user");
    }

    [Fact]
    public async Task ResolveDataConflictAsync_WithClientWinsStrategy_ShouldReturnClientData()
    {
        // Arrange
        var localData = CreateTestOfflineData(Guid.NewGuid(), "TripUpdate", 1);
        localData.SetConflictResolution("client_wins");
        
        var serverData = new Dictionary<string, object>
        {
            { "status", "server_status" },
            { "timestamp", DateTime.UtcNow.AddMinutes(-5) }
        };

        // Act
        var result = await _service.ResolveDataConflictAsync(localData, serverData);

        // Assert
        result.Should().NotBeNull();
        result.IsResolved.Should().BeTrue();
        result.Resolution.Should().Be("Client data preserved");
        result.ResolvedData.Should().BeEquivalentTo(localData.Data);
    }

    [Fact]
    public async Task ResolveDataConflictAsync_WithServerWinsStrategy_ShouldReturnServerData()
    {
        // Arrange
        var localData = CreateTestOfflineData(Guid.NewGuid(), "TripUpdate", 1);
        var serverData = new Dictionary<string, object>
        {
            { "status", "server_status" },
            { "timestamp", DateTime.UtcNow }
        };

        // Act
        var result = await _service.ResolveDataConflictAsync(localData, serverData);

        // Assert
        result.Should().NotBeNull();
        result.IsResolved.Should().BeTrue();
        result.Resolution.Should().Be("Server data accepted");
        result.ResolvedData.Should().BeEquivalentTo(serverData);
    }

    [Fact]
    public async Task GetPendingSyncDataAsync_ShouldReturnUnsyncedData()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var unsyncedData = new List<OfflineData>
        {
            CreateTestOfflineData(userId, "TripUpdate", 1),
            CreateTestOfflineData(userId, "PODUpload", 2)
        };

        _mockOfflineDataRepository
            .Setup(x => x.GetUnsyncedDataByUserIdAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(unsyncedData);

        // Act
        var result = await _service.GetPendingSyncDataAsync(userId);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(2);
        result.Should().OnlyContain(d => !d.IsSynced);
    }

    [Theory]
    [InlineData("tripupdate", true)]
    [InlineData("podupload", true)]
    [InlineData("documentupload", true)]
    [InlineData("unknown", true)]
    [InlineData("", false)]
    public async Task ValidateDataIntegrityAsync_WithVariousDataTypes_ShouldReturnExpectedResult(string dataType, bool expectedResult)
    {
        // Arrange
        var data = CreateTestOfflineData(Guid.NewGuid(), dataType, 1);
        
        if (dataType == "tripupdate")
        {
            data.Data["tripId"] = Guid.NewGuid();
            data.Data["status"] = "InTransit";
            data.Data["location"] = "12.9716,77.5946";
        }
        else if (dataType == "podupload")
        {
            data.Data["tripId"] = Guid.NewGuid();
            data.Data["signature"] = "base64signature";
            data.Data["photos"] = new List<string> { "photo1.jpg" };
        }
        else if (dataType == "documentupload")
        {
            data.Data["documentType"] = "license";
            data.Data["fileUrl"] = "https://example.com/file.pdf";
            data.Data["userId"] = Guid.NewGuid();
        }
        else if (dataType == "")
        {
            data = CreateTestOfflineData(Guid.NewGuid(), dataType, 1);
            data.Data.Clear(); // Empty data
        }

        // Act
        var result = await _service.ValidateDataIntegrityAsync(data);

        // Assert
        result.Should().Be(expectedResult);
    }

    [Fact]
    public async Task GetSyncConfigurationAsync_ShouldReturnDefaultConfiguration()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var platform = "Android";

        // Act
        var result = await _service.GetSyncConfigurationAsync(userId, platform);

        // Assert
        result.Should().NotBeNull();
        result.AutoSyncEnabled.Should().BeTrue();
        result.SyncInterval.Should().Be(TimeSpan.FromMinutes(5));
        result.MaxRetryAttempts.Should().Be(3);
        result.PriorityDataTypes.Should().Contain("Emergency");
        result.PriorityDataTypes.Should().Contain("TripUpdate");
        result.PriorityDataTypes.Should().Contain("PODUpload");
    }

    [Fact]
    public async Task GetSyncConfigurationAsync_ShouldCacheConfiguration()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var platform = "iOS";

        // Act
        var result1 = await _service.GetSyncConfigurationAsync(userId, platform);
        var result2 = await _service.GetSyncConfigurationAsync(userId, platform);

        // Assert
        result1.Should().NotBeNull();
        result2.Should().NotBeNull();
        result1.Should().BeEquivalentTo(result2);
    }

    [Theory]
    [InlineData("Emergency", true, true, false, true)]
    [InlineData("TripUpdate", true, true, false, true)]
    [InlineData("PODUpload", true, true, false, true)]
    [InlineData("LocationUpdate", true, false, false, false)]
    [InlineData("TripUpdate", false, true, false, false)]
    [InlineData("TripUpdate", true, true, true, false)]
    public async Task ShouldSync_WithVariousConditions_ShouldReturnExpectedResult(
        string dataType, bool autoSyncEnabled, bool isWiFiConnected, bool isLowBattery, bool expectedResult)
    {
        // Arrange
        var userId = Guid.NewGuid();
        var platform = "Android";
        var config = await _service.GetSyncConfigurationAsync(userId, platform);
        var lastSync = DateTime.UtcNow.AddMinutes(-10);

        // Modify config for test
        var testConfig = new MobileWorkflow.Domain.ValueObjects.SyncConfiguration(
            autoSyncEnabled: autoSyncEnabled,
            syncOnWiFiOnly: false,
            syncOnLowBattery: false);

        // Act
        var result = testConfig.ShouldSync(dataType, lastSync, isWiFiConnected, isLowBattery);

        // Assert
        result.Should().Be(expectedResult);
    }

    private static OfflineData CreateTestOfflineData(Guid userId, string dataType, int priority)
    {
        var data = new Dictionary<string, object>
        {
            { "id", Guid.NewGuid() },
            { "timestamp", DateTime.UtcNow },
            { "action", "update" }
        };

        return new OfflineData(
            userId,
            Guid.NewGuid(),
            dataType,
            "Update",
            data,
            priority);
    }

    private static MobileSession CreateTestMobileSession(Guid userId)
    {
        return new MobileSession(
            userId,
            Guid.NewGuid(),
            "device123",
            "Test Device",
            "1.0.0",
            "Android",
            "11.0",
            true);
    }
}
