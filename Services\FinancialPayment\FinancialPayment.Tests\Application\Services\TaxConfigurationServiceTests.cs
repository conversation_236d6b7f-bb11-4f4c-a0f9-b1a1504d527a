using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Application.Interfaces.Repositories;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;
using FinancialPayment.Infrastructure.Services;
using SharedKernel.Application.Interfaces;
using Xunit;

namespace FinancialPayment.Tests.Application.Services;

public class TaxConfigurationServiceTests
{
    private readonly Mock<ITaxConfigurationRepository> _taxConfigRepositoryMock;
    private readonly Mock<IGstConfigurationRepository> _gstConfigRepositoryMock;
    private readonly Mock<ITdsConfigurationRepository> _tdsConfigRepositoryMock;
    private readonly Mock<IHsnCodeRepository> _hsnCodeRepositoryMock;
    private readonly Mock<IUnitOfWork> _unitOfWorkMock;
    private readonly Mock<ILogger<TaxConfigurationService>> _loggerMock;
    private readonly TaxConfigurationService _service;

    public TaxConfigurationServiceTests()
    {
        _taxConfigRepositoryMock = new Mock<ITaxConfigurationRepository>();
        _gstConfigRepositoryMock = new Mock<IGstConfigurationRepository>();
        _tdsConfigRepositoryMock = new Mock<ITdsConfigurationRepository>();
        _hsnCodeRepositoryMock = new Mock<IHsnCodeRepository>();
        _unitOfWorkMock = new Mock<IUnitOfWork>();
        _loggerMock = new Mock<ILogger<TaxConfigurationService>>();

        _service = new TaxConfigurationService(
            _taxConfigRepositoryMock.Object,
            _gstConfigRepositoryMock.Object,
            _tdsConfigRepositoryMock.Object,
            _hsnCodeRepositoryMock.Object,
            _unitOfWorkMock.Object,
            _loggerMock.Object);
    }

    [Fact]
    public async Task CalculateComprehensiveTaxAsync_ShouldCalculateTax_WithValidParameters()
    {
        // Arrange
        var baseAmount = new Money(10000m, "INR");
        var serviceCategory = ServiceCategory.Transportation;
        var jurisdiction = new TaxJurisdiction("India", "Maharashtra", "Mumbai", LocationType.City);
        var entityType = EntityType.Company;
        var tdsSection = TdsSection.Section194C;
        var hsnCode = "9967";
        var hasPan = true;

        var gstConfig = CreateGstConfiguration(serviceCategory, 5.0m);
        var tdsConfig = CreateTdsConfiguration(tdsSection, entityType, 2.0m);
        var hsnCodeEntity = CreateHsnCode(hsnCode, GstRate.Rate5);

        _gstConfigRepositoryMock
            .Setup(x => x.GetActiveConfigurationAsync(serviceCategory, jurisdiction, It.IsAny<CancellationToken>()))
            .ReturnsAsync(gstConfig);

        _tdsConfigRepositoryMock
            .Setup(x => x.GetActiveConfigurationAsync(tdsSection.Value, entityType, It.IsAny<CancellationToken>()))
            .ReturnsAsync(tdsConfig);

        _hsnCodeRepositoryMock
            .Setup(x => x.GetByCodeAsync(hsnCode, It.IsAny<CancellationToken>()))
            .ReturnsAsync(hsnCodeEntity);

        // Act
        var result = await _service.CalculateComprehensiveTaxAsync(
            baseAmount, serviceCategory, jurisdiction, entityType, tdsSection, hsnCode, hasPan);

        // Assert
        result.Should().NotBeNull();
        result.BaseAmount.Should().Be(baseAmount);
        result.GstAmount.Should().Be(new Money(500m, "INR")); // 5% of 10000
        result.TdsAmount.Should().Be(new Money(200m, "INR")); // 2% of 10000
        result.TotalTaxAmount.Should().Be(new Money(700m, "INR")); // 500 + 200
        result.NetAmount.Should().Be(new Money(10300m, "INR")); // 10000 + 500 - 200
        result.EffectiveGstRate.Should().Be(5.0m);
        result.EffectiveTdsRate.Should().Be(2.0m);
        result.IsReverseChargeApplicable.Should().BeFalse();
        result.AppliedHsnCode.Should().Be(hsnCode);
        result.AppliedRules.Should().NotBeEmpty();
        result.Warnings.Should().BeEmpty();
    }

    [Fact]
    public async Task CalculateComprehensiveTaxAsync_ShouldApplyHigherTdsRate_WhenNoPan()
    {
        // Arrange
        var baseAmount = new Money(10000m, "INR");
        var serviceCategory = ServiceCategory.Transportation;
        var jurisdiction = new TaxJurisdiction("India", "Maharashtra", "Mumbai", LocationType.City);
        var entityType = EntityType.Individual;
        var tdsSection = TdsSection.Section194C;
        var hasPan = false; // No PAN

        var gstConfig = CreateGstConfiguration(serviceCategory, 5.0m);
        var tdsConfig = CreateTdsConfiguration(tdsSection, entityType, 2.0m, higherRateWithoutPan: 20.0m);

        _gstConfigRepositoryMock
            .Setup(x => x.GetActiveConfigurationAsync(serviceCategory, jurisdiction, It.IsAny<CancellationToken>()))
            .ReturnsAsync(gstConfig);

        _tdsConfigRepositoryMock
            .Setup(x => x.GetActiveConfigurationAsync(tdsSection.Value, entityType, It.IsAny<CancellationToken>()))
            .ReturnsAsync(tdsConfig);

        // Act
        var result = await _service.CalculateComprehensiveTaxAsync(
            baseAmount, serviceCategory, jurisdiction, entityType, tdsSection, null, hasPan);

        // Assert
        result.Should().NotBeNull();
        result.TdsAmount.Should().Be(new Money(2000m, "INR")); // 20% of 10000 (higher rate without PAN)
        result.EffectiveTdsRate.Should().Be(20.0m);
        result.Warnings.Should().Contain("Higher TDS rate applied due to absence of PAN");
    }

    [Fact]
    public async Task CalculateComprehensiveTaxAsync_ShouldNotApplyTds_WhenBelowThreshold()
    {
        // Arrange
        var baseAmount = new Money(1000m, "INR"); // Below threshold
        var serviceCategory = ServiceCategory.Transportation;
        var jurisdiction = new TaxJurisdiction("India", "Maharashtra", "Mumbai", LocationType.City);
        var entityType = EntityType.Company;
        var tdsSection = TdsSection.Section194C;

        var gstConfig = CreateGstConfiguration(serviceCategory, 5.0m);
        var tdsConfig = CreateTdsConfiguration(tdsSection, entityType, 2.0m, thresholdAmount: 30000m);

        _gstConfigRepositoryMock
            .Setup(x => x.GetActiveConfigurationAsync(serviceCategory, jurisdiction, It.IsAny<CancellationToken>()))
            .ReturnsAsync(gstConfig);

        _tdsConfigRepositoryMock
            .Setup(x => x.GetActiveConfigurationAsync(tdsSection.Value, entityType, It.IsAny<CancellationToken>()))
            .ReturnsAsync(tdsConfig);

        // Act
        var result = await _service.CalculateComprehensiveTaxAsync(
            baseAmount, serviceCategory, jurisdiction, entityType, tdsSection, null, true);

        // Assert
        result.Should().NotBeNull();
        result.GstAmount.Should().Be(new Money(50m, "INR")); // 5% of 1000
        result.TdsAmount.Should().Be(Money.Zero("INR")); // Below threshold
        result.EffectiveTdsRate.Should().Be(0m);
        result.Warnings.Should().Contain("TDS not applicable - amount below threshold");
    }

    [Fact]
    public async Task CalculateComprehensiveTaxAsync_ShouldUseDefaultRates_WhenConfigurationsNotFound()
    {
        // Arrange
        var baseAmount = new Money(10000m, "INR");
        var serviceCategory = ServiceCategory.Transportation;
        var jurisdiction = new TaxJurisdiction("India", "Maharashtra", "Mumbai", LocationType.City);
        var entityType = EntityType.Company;

        _gstConfigRepositoryMock
            .Setup(x => x.GetActiveConfigurationAsync(serviceCategory, jurisdiction, It.IsAny<CancellationToken>()))
            .ReturnsAsync((GstConfiguration?)null);

        _tdsConfigRepositoryMock
            .Setup(x => x.GetActiveConfigurationAsync(It.IsAny<TdsSection>(), entityType, It.IsAny<CancellationToken>()))
            .ReturnsAsync((TdsConfiguration?)null);

        // Act
        var result = await _service.CalculateComprehensiveTaxAsync(
            baseAmount, serviceCategory, jurisdiction, entityType, null, null, true);

        // Assert
        result.Should().NotBeNull();
        result.GstAmount.Should().Be(new Money(1800m, "INR")); // Default 18% GST
        result.TdsAmount.Should().Be(Money.Zero("INR")); // No TDS without configuration
        result.EffectiveGstRate.Should().Be(18.0m);
        result.EffectiveTdsRate.Should().Be(0m);
        result.Warnings.Should().Contain("Using default GST rate - no specific configuration found");
        result.Warnings.Should().Contain("TDS not calculated - no configuration found");
    }

    [Fact]
    public async Task CalculateComprehensiveTaxAsync_ShouldThrowException_WhenBaseAmountIsZero()
    {
        // Arrange
        var baseAmount = Money.Zero("INR");
        var serviceCategory = ServiceCategory.Transportation;
        var jurisdiction = new TaxJurisdiction("India", "Maharashtra", "Mumbai", LocationType.City);
        var entityType = EntityType.Company;

        // Act & Assert
        var action = async () => await _service.CalculateComprehensiveTaxAsync(
            baseAmount, serviceCategory, jurisdiction, entityType, null, null, true);

        await action.Should().ThrowAsync<ArgumentException>()
            .WithMessage("Base amount must be greater than zero*");
    }

    [Fact]
    public async Task CalculateGstAsync_ShouldCalculateGst_WithValidConfiguration()
    {
        // Arrange
        var baseAmount = new Money(10000m, "INR");
        var serviceCategory = ServiceCategory.Transportation;
        var jurisdiction = new TaxJurisdiction("India", "Maharashtra", "Mumbai", LocationType.City);
        var gstConfig = CreateGstConfiguration(serviceCategory, 5.0m);

        _gstConfigRepositoryMock
            .Setup(x => x.GetActiveConfigurationAsync(serviceCategory, jurisdiction, It.IsAny<CancellationToken>()))
            .ReturnsAsync(gstConfig);

        // Act
        var result = await _service.CalculateGstAsync(baseAmount, serviceCategory, jurisdiction);

        // Assert
        result.Should().NotBeNull();
        result.GstAmount.Should().Be(new Money(500m, "INR"));
        result.EffectiveRate.Should().Be(5.0m);
        result.AppliedHsnCode.Should().Be("9967");
        result.IsReverseChargeApplicable.Should().BeFalse();
    }

    [Fact]
    public async Task CalculateTdsAsync_ShouldCalculateTds_WithValidConfiguration()
    {
        // Arrange
        var baseAmount = new Money(50000m, "INR");
        var tdsSection = TdsSection.Section194C;
        var entityType = EntityType.Company;
        var tdsConfig = CreateTdsConfiguration(tdsSection, entityType, 2.0m, thresholdAmount: 30000m);

        _tdsConfigRepositoryMock
            .Setup(x => x.GetActiveConfigurationAsync(tdsSection, entityType, It.IsAny<CancellationToken>()))
            .ReturnsAsync(tdsConfig);

        // Act
        var result = await _service.CalculateTdsAsync(baseAmount, tdsSection, entityType, true);

        // Assert
        result.Should().NotBeNull();
        result.TdsAmount.Should().Be(new Money(1000m, "INR")); // 2% of 50000
        result.EffectiveRate.Should().Be(2.0m);
        result.IsApplicable.Should().BeTrue();
        result.ThresholdAmount.Should().Be(new Money(30000m, "INR"));
    }

    [Fact]
    public async Task ValidateHsnCodeAsync_ShouldReturnTrue_WhenHsnCodeExists()
    {
        // Arrange
        var hsnCode = "9967";
        var hsnCodeEntity = CreateHsnCode(hsnCode, GstRate.Rate5);

        _hsnCodeRepositoryMock
            .Setup(x => x.GetByCodeAsync(hsnCode, It.IsAny<CancellationToken>()))
            .ReturnsAsync(hsnCodeEntity);

        // Act
        var result = await _service.ValidateHsnCodeAsync(hsnCode);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task ValidateHsnCodeAsync_ShouldReturnFalse_WhenHsnCodeDoesNotExist()
    {
        // Arrange
        var hsnCode = "invalid_code";

        _hsnCodeRepositoryMock
            .Setup(x => x.GetByCodeAsync(hsnCode, It.IsAny<CancellationToken>()))
            .ReturnsAsync((HsnCode?)null);

        // Act
        var result = await _service.ValidateHsnCodeAsync(hsnCode);

        // Assert
        result.Should().BeFalse();
    }

    private GstConfiguration CreateGstConfiguration(ServiceCategory serviceCategory, decimal rate, string hsnCode = "9967")
    {
        var jurisdiction = new TaxJurisdiction("India", "Maharashtra", "Mumbai", LocationType.City);
        var taxRate = new TaxRate(rate, TaxCalculationMethod.Percentage, DateTime.UtcNow, null);
        
        return new GstConfiguration(
            $"{serviceCategory} GST Configuration",
            $"GST configuration for {serviceCategory}",
            serviceCategory,
            jurisdiction,
            rate < 10 ? GstRate.Rate5 : GstRate.Rate18,
            taxRate,
            Money.Zero("INR"),
            new Money(10000000, "INR"),
            "System",
            hsnCode,
            false,
            null);
    }

    private TdsConfiguration CreateTdsConfiguration(
        TdsSection tdsSection, 
        EntityType entityType, 
        decimal rate, 
        decimal thresholdAmount = 30000m,
        decimal higherRateWithoutPan = 20.0m)
    {
        var taxRate = new TaxRate(rate, TaxCalculationMethod.Percentage, DateTime.UtcNow, null);
        var threshold = new TdsThreshold(
            new Money(thresholdAmount, "INR"),
            new Money(100000, "INR"),
            entityType,
            true);

        return new TdsConfiguration(
            $"Section {tdsSection} TDS Configuration",
            $"TDS configuration for {tdsSection}",
            tdsSection,
            entityType,
            taxRate,
            threshold,
            "System",
            true,
            higherRateWithoutPan,
            null);
    }

    private HsnCode CreateHsnCode(string code, GstRate applicableGstRate)
    {
        var hsnCodeDetails = new HsnCodeDetails(code, "Test HSN Code", "Test Category", applicableGstRate);
        return new HsnCode(hsnCodeDetails, "99", "Services", DateTime.UtcNow, "System");
    }
}
