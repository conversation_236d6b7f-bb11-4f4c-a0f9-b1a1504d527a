namespace MobileWorkflow.Application.DTOs;

// Driver Profile DTOs
public class UpdateDriverProfileRequest
{
    public string? PhoneNumber { get; set; }
    public string? Email { get; set; }
    public string? ProfilePhotoUrl { get; set; }
    public List<string> PreferredRoutes { get; set; } = new();
    public List<string> OperationalAreas { get; set; } = new();
    public DriverPreferences Preferences { get; set; } = new();
}

public class DriverPreferences
{
    public bool NotificationsEnabled { get; set; } = true;
    public bool LocationSharingEnabled { get; set; } = true;
    public string PreferredLanguage { get; set; } = "en";
    public string TimeZone { get; set; } = "UTC";
    public bool AutoAcceptTrips { get; set; } = false;
    public int MaxTripDistance { get; set; } = 500; // km
    public List<string> PreferredTripTypes { get; set; } = new();
    public Dictionary<string, object> CustomSettings { get; set; } = new();
}

// Entity Assignment DTOs
public class EntityAssignmentDetails
{
    public Guid CarrierId { get; set; }
    public string CarrierName { get; set; } = string.Empty;
    public string CarrierType { get; set; } = string.Empty;
    public string CarrierStatus { get; set; } = string.Empty;
    public ContactDetails CarrierContact { get; set; } = new();
    public string? FleetName { get; set; }
    public string? FleetManager { get; set; }
    public List<EntityHierarchy> Hierarchy { get; set; } = new();
    public DateTime AssignedAt { get; set; }
}

public class ContactDetails
{
    public string? Phone { get; set; }
    public string? Email { get; set; }
    public string? Address { get; set; }
}

public class EntityHierarchy
{
    public string Level { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Manager { get; set; } = string.Empty;
    public string Contact { get; set; } = string.Empty;
}

// Document Status DTOs
public class DocumentStatusSummary
{
    public int TotalDocuments { get; set; }
    public int ValidDocuments { get; set; }
    public int ExpiredDocuments { get; set; }
    public int ExpiringDocuments { get; set; }
    public List<DocumentStatusDto> Documents { get; set; } = new();
    public DateTime LastUpdated { get; set; }
}

public class DocumentStatusDto
{
    public Guid Id { get; set; }
    public string DocumentType { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime? ExpiryDate { get; set; }
    public int? DaysUntilExpiry { get; set; }
    public bool IsExpired { get; set; }
    public bool IsExpiringSoon { get; set; }
}

// Enhanced Dashboard DTOs
public class EnhancedDashboardRequest
{
    public bool IncludeRatingStats { get; set; } = true;
    public bool IncludeCompletedTrips { get; set; } = true;
    public bool IncludeFeedback { get; set; } = true;
    public bool IncludeMapShortcuts { get; set; } = true;
    public bool IncludePerformanceMetrics { get; set; } = true;
    public int RecentTripsCount { get; set; } = 5;
    public int RecentFeedbackCount { get; set; } = 3;
    public int PerformanceDays { get; set; } = 30;
}

public class EnhancedDashboardResponse
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public EnhancedDashboard? Dashboard { get; set; }
}

public class EnhancedDashboard
{
    public Guid DriverId { get; set; }
    public string DriverName { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public CurrentTripInfo? CurrentTrip { get; set; }
    public RatingStatistics RatingStats { get; set; } = new();
    public List<CompletedTripSummary> RecentCompletedTrips { get; set; } = new();
    public List<FeedbackSummary> RecentFeedback { get; set; } = new();
    public List<MapShortcutDto> MapShortcuts { get; set; } = new();
    public PerformanceMetrics PerformanceMetrics { get; set; } = new();
    public QuickStats QuickStats { get; set; } = new();
    public EarningsSummary Earnings { get; set; } = new();
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
}

// Map Shortcuts DTOs
public class MapShortcutDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public string Address { get; set; } = string.Empty;
    public string ShortcutType { get; set; } = string.Empty;
    public string NavigationUrl { get; set; } = string.Empty;
    public double? DistanceFromCurrentKm { get; set; }
    public int? EstimatedTimeMinutes { get; set; }
    public bool IsFavorite { get; set; }
}

public class AddMapShortcutRequest
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public string Address { get; set; } = string.Empty;
    public string ShortcutType { get; set; } = string.Empty;
    public bool IsFavorite { get; set; } = false;
}

// Enhanced Profile DTOs
public class EnhancedProfileRequest
{
    public bool IncludeEntityDetails { get; set; } = true;
    public bool IncludePerformanceMetrics { get; set; } = true;
    public bool IncludeDocumentStatus { get; set; } = true;
    public bool IncludeVehicleAssignments { get; set; } = true;
    public bool IncludeLocationHistory { get; set; } = false;
    public bool IncludeTripHistory { get; set; } = false;
    public int HistoryDays { get; set; } = 30;
}

public class EnhancedDriverProfileResponse
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public EnhancedDriverProfile? Profile { get; set; }
}

public class EnhancedDriverProfile
{
    public Guid Id { get; set; }
    public string FullName { get; set; } = string.Empty;
    public string PhoneNumber { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string? ProfilePhotoUrl { get; set; }
    public string Status { get; set; } = string.Empty;
    public bool IsVerified { get; set; }
    public EntityAssignmentDetails EntityAssignment { get; set; } = new();
    public DriverPerformanceSummary Performance { get; set; } = new();
    public DocumentStatusSummary DocumentStatus { get; set; } = new();
    public List<VehicleAssignmentInfo> VehicleAssignments { get; set; } = new();
    public CurrentLocationInfo? CurrentLocation { get; set; }
    public CurrentTripInfo? CurrentTrip { get; set; }
    public DateTime LastActiveAt { get; set; }
}

// Trip and Feedback Summary DTOs
public class CompletedTripSummary
{
    public Guid TripId { get; set; }
    public string TripNumber { get; set; } = string.Empty;
    public string Origin { get; set; } = string.Empty;
    public string Destination { get; set; } = string.Empty;
    public DateTime CompletedAt { get; set; }
    public double DistanceKm { get; set; }
    public TimeSpan Duration { get; set; }
    public decimal Earnings { get; set; }
    public double Rating { get; set; }
    public string Status { get; set; } = string.Empty;
    public string? CustomerName { get; set; }
    public string? LoadType { get; set; }
}

public class FeedbackSummary
{
    public Guid FeedbackId { get; set; }
    public Guid TripId { get; set; }
    public string TripNumber { get; set; } = string.Empty;
    public double Rating { get; set; }
    public string? Comment { get; set; }
    public string CustomerName { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public List<string> Tags { get; set; } = new();
    public string FeedbackType { get; set; } = string.Empty;
}

// Supporting DTOs
public class CurrentTripInfo
{
    public Guid TripId { get; set; }
    public string TripNumber { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string Origin { get; set; } = string.Empty;
    public string Destination { get; set; } = string.Empty;
    public DateTime StartedAt { get; set; }
    public DateTime? EstimatedArrival { get; set; }
    public double ProgressPercentage { get; set; }
    public double DistanceRemainingKm { get; set; }
}

public class RatingStatistics
{
    public double AverageRating { get; set; }
    public int TotalRatings { get; set; }
    public Dictionary<int, int> RatingDistribution { get; set; } = new();
    public double RecentRating { get; set; }
    public int RecentRatingCount { get; set; }
    public string RatingGrade { get; set; } = string.Empty;
    public List<string> RatingHighlights { get; set; } = new();
}

public class PerformanceMetrics
{
    public double OnTimePerformance { get; set; }
    public double SafetyScore { get; set; }
    public double FuelEfficiency { get; set; }
    public double CustomerSatisfaction { get; set; }
    public int TotalTripsCompleted { get; set; }
    public string PerformanceGrade { get; set; } = string.Empty;
    public List<string> Alerts { get; set; } = new();
}

public class QuickStats
{
    public int TripsToday { get; set; }
    public decimal EarningsToday { get; set; }
    public double HoursWorked { get; set; }
    public double AverageRatingToday { get; set; }
    public int PendingTrips { get; set; }
}

public class EarningsSummary
{
    public decimal TotalEarnings { get; set; }
    public decimal EarningsThisWeek { get; set; }
    public decimal EarningsThisMonth { get; set; }
    public decimal AverageEarningsPerTrip { get; set; }
    public decimal PendingPayments { get; set; }
    public DateTime LastPaymentDate { get; set; }
}

public class DriverPerformanceSummary
{
    public double AverageRating { get; set; }
    public int TotalTrips { get; set; }
    public double OnTimePerformance { get; set; }
    public double SafetyScore { get; set; }
    public string PerformanceGrade { get; set; } = string.Empty;
    public List<string> Alerts { get; set; } = new();
}

public class VehicleAssignmentInfo
{
    public Guid VehicleId { get; set; }
    public string VehicleNumber { get; set; } = string.Empty;
    public string Make { get; set; } = string.Empty;
    public string Model { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public DateTime AssignedAt { get; set; }
    public bool IsActive { get; set; }
}

public class CurrentLocationInfo
{
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public string Address { get; set; } = string.Empty;
    public DateTime LastUpdated { get; set; }
    public double? Speed { get; set; }
    public double? Heading { get; set; }
}
