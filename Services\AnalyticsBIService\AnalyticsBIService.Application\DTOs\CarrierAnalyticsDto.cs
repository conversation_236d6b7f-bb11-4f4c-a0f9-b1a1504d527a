namespace AnalyticsBIService.Application.DTOs;

/// <summary>
/// Carrier dashboard data transfer object
/// </summary>
public class CarrierDashboardDto
{
    public Guid CarrierId { get; set; }
    public DateTime GeneratedAt { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public string Period { get; set; } = string.Empty;

    // Performance Metrics
    public CarrierPerformanceMetricsDto PerformanceMetrics { get; set; } = new();

    // Delivery Analytics
    public CarrierDeliveryCompletionAnalyticsDto DeliveryAnalytics { get; set; } = new();

    // Efficiency Tracking
    public CarrierEfficiencyTrackingDto EfficiencyTracking { get; set; } = new();

    // Earnings Analytics
    public CarrierEarningsAnalyticsDto EarningsAnalytics { get; set; } = new();

    // Growth Opportunities
    public CarrierGrowthOpportunitiesDto GrowthOpportunities { get; set; } = new();

    // Service Quality
    public CarrierServiceQualityMonitoringDto ServiceQuality { get; set; } = new();

    // Financial Performance
    public CarrierFinancialPerformanceDto FinancialPerformance { get; set; } = new();

    // Key Metrics Summary
    public List<KPIPerformanceDto> KeyMetrics { get; set; } = new();

    // Recent Alerts
    public List<AlertDto> RecentAlerts { get; set; } = new();

    // Additional properties required by GetCarrierAnalyticsQueryHandler
    public CarrierPerformanceAnalyticsDto Performance { get; set; } = new();
    public CarrierDeliveryPerformanceDto DeliveryPerformance { get; set; } = new();
    public CarrierRoutePerformanceDto RoutePerformance { get; set; } = new();
}

/// <summary>
/// Comprehensive carrier performance summary for dashboard
/// </summary>
public class CarrierPerformanceSummaryDto
{
    public Guid CarrierId { get; set; }
    public string CarrierName { get; set; } = string.Empty;
    public DateTime GeneratedAt { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;

    // Key Performance Indicators
    public CarrierKPISummaryDto KPISummary { get; set; } = new();

    // Earnings Visualization
    public CarrierEarningsVisualizationDto EarningsVisualization { get; set; } = new();

    // Order Metrics
    public CarrierOrderMetricsDto OrderMetrics { get; set; } = new();

    // On-Time Delivery Performance
    public CarrierOnTimeDeliveryDto OnTimeDelivery { get; set; } = new();

    // Performance Trends
    public CarrierPerformanceTrendsDto Trends { get; set; } = new();

    // Performance Comparisons
    public CarrierPerformanceComparisonsDto Comparisons { get; set; } = new();

    // Quick Actions and Recommendations
    public List<CarrierRecommendationDto> Recommendations { get; set; } = new();

    // Performance Alerts
    public List<CarrierPerformanceAlertDto> Alerts { get; set; } = new();
}

public class CarrierKPISummaryDto
{
    public decimal OverallPerformanceScore { get; set; }
    public decimal PerformanceScoreChange { get; set; }
    public string PerformanceTrend { get; set; } = string.Empty; // Improving, Declining, Stable

    public decimal TotalEarnings { get; set; }
    public decimal EarningsChange { get; set; }
    public string EarningsTrend { get; set; } = string.Empty;

    public int CompletedOrders { get; set; }
    public int OrdersChange { get; set; }
    public string OrdersTrend { get; set; } = string.Empty;

    public decimal OnTimeDeliveryRate { get; set; }
    public decimal OnTimeDeliveryChange { get; set; }
    public string OnTimeDeliveryTrend { get; set; } = string.Empty;

    public decimal CustomerSatisfactionScore { get; set; }
    public decimal CustomerSatisfactionChange { get; set; }
    public string CustomerSatisfactionTrend { get; set; } = string.Empty;

    public decimal VehicleUtilizationRate { get; set; }
    public decimal VehicleUtilizationChange { get; set; }
    public string VehicleUtilizationTrend { get; set; } = string.Empty;
}

public class CarrierEarningsVisualizationDto
{
    public decimal TotalEarnings { get; set; }
    public decimal NetEarnings { get; set; }
    public decimal GrossEarnings { get; set; }
    public decimal AverageEarningsPerTrip { get; set; }
    public decimal AverageEarningsPerKm { get; set; }

    // Earnings breakdown
    public List<EarningsBreakdownDto> EarningsBreakdown { get; set; } = new();

    // Time-based earnings
    public List<TimeBasedEarningsDto> DailyEarnings { get; set; } = new();
    public List<TimeBasedEarningsDto> WeeklyEarnings { get; set; } = new();
    public List<TimeBasedEarningsDto> MonthlyEarnings { get; set; } = new();

    // Top earning routes
    public List<RouteEarningsDto> TopEarningRoutes { get; set; } = new();

    // Earnings projections
    public EarningsProjectionDto Projection { get; set; } = new();
}

public class CarrierOrderMetricsDto
{
    public int TotalOrders { get; set; }
    public int CompletedOrders { get; set; }
    public int PendingOrders { get; set; }
    public int CancelledOrders { get; set; }
    public int InProgressOrders { get; set; }

    public decimal OrderCompletionRate { get; set; }
    public decimal OrderCancellationRate { get; set; }
    public decimal AverageOrderValue { get; set; }
    public decimal TotalOrderValue { get; set; }

    // Order trends
    public List<OrderTrendDto> OrderTrends { get; set; } = new();

    // Order types breakdown
    public List<OrderTypeBreakdownDto> OrderTypeBreakdown { get; set; } = new();

    // Top customers by order volume
    public List<CustomerOrderVolumeDto> TopCustomers { get; set; } = new();
}

public class CarrierOnTimeDeliveryDto
{
    public decimal OnTimeDeliveryRate { get; set; }
    public decimal EarlyDeliveryRate { get; set; }
    public decimal LateDeliveryRate { get; set; }
    public decimal AverageDeliveryTime { get; set; }
    public TimeSpan AverageDelay { get; set; }

    // On-time performance by route
    public List<RouteOnTimePerformanceDto> RoutePerformance { get; set; } = new();

    // On-time performance trends
    public List<OnTimeDeliveryTrendDto> OnTimeTrends { get; set; } = new();

    // Delivery performance factors
    public List<DeliveryPerformanceFactorDto> PerformanceFactors { get; set; } = new();
}

public class CarrierPerformanceTrendsDto
{
    public List<PerformanceTrendDataDto> PerformanceTrends { get; set; } = new();
    public List<EarningsTrendDataDto> EarningsTrends { get; set; } = new();
    public List<OrderVolumeTrendDto> OrderVolumeTrends { get; set; } = new();
    public List<OnTimeDeliveryTrendDto> OnTimeDeliveryTrends { get; set; } = new();
    public List<CustomerSatisfactionTrendDto> CustomerSatisfactionTrends { get; set; } = new();
}

public class CarrierPerformanceComparisonsDto
{
    // Industry benchmarks
    public IndustryBenchmarkDto IndustryBenchmark { get; set; } = new();

    // Peer comparisons
    public List<PeerComparisonDto> PeerComparisons { get; set; } = new();

    // Historical comparisons
    public HistoricalComparisonDto HistoricalComparison { get; set; } = new();

    // Market position
    public MarketPositionDto MarketPosition { get; set; } = new();
}

/// <summary>
/// Carrier quoting history and analytics
/// </summary>
public class CarrierQuotingHistoryDto
{
    public Guid CarrierId { get; set; }
    public string CarrierName { get; set; } = string.Empty;
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;

    // RFQ Response History
    public CarrierRFQResponseHistoryDto RFQResponseHistory { get; set; } = new();

    // Quote Success Rate Analytics
    public CarrierQuoteSuccessRateDto QuoteSuccessRate { get; set; } = new();

    // Pricing Analytics
    public CarrierPricingAnalyticsDto PricingAnalytics { get; set; } = new();

    // Market Comparison
    public CarrierMarketComparisonDto MarketComparison { get; set; } = new();

    // Quoting Trends
    public CarrierQuotingTrendsDto QuotingTrends { get; set; } = new();

    // Performance Insights
    public List<CarrierQuotingInsightDto> Insights { get; set; } = new();

    // Improvement Recommendations
    public List<CarrierQuotingRecommendationDto> Recommendations { get; set; } = new();
}

public class CarrierRFQResponseHistoryDto
{
    public int TotalRFQsReceived { get; set; }
    public int RFQsResponded { get; set; }
    public int RFQsIgnored { get; set; }
    public decimal ResponseRate { get; set; }
    public TimeSpan AverageResponseTime { get; set; }
    public TimeSpan FastestResponseTime { get; set; }
    public TimeSpan SlowestResponseTime { get; set; }

    // Response patterns
    public List<RFQResponsePatternDto> ResponsePatterns { get; set; } = new();

    // Route-wise response history
    public List<RouteRFQResponseDto> RouteResponseHistory { get; set; } = new();

    // Broker-wise response history
    public List<BrokerRFQResponseDto> BrokerResponseHistory { get; set; } = new();

    // Time-based response trends
    public List<RFQResponseTrendDto> ResponseTrends { get; set; } = new();
}

public class CarrierQuoteSuccessRateDto
{
    public int TotalQuotesSubmitted { get; set; }
    public int QuotesAccepted { get; set; }
    public int QuotesRejected { get; set; }
    public int QuotesPending { get; set; }
    public decimal OverallSuccessRate { get; set; }
    public decimal AcceptanceRate { get; set; }
    public decimal RejectionRate { get; set; }

    // Success rate by factors
    public List<QuoteSuccessFactorDto> SuccessFactors { get; set; } = new();

    // Route-wise success rates
    public List<RouteQuoteSuccessDto> RouteSuccessRates { get; set; } = new();

    // Broker-wise success rates
    public List<BrokerQuoteSuccessDto> BrokerSuccessRates { get; set; } = new();

    // Time-based success trends
    public List<QuoteSuccessTrendDto> SuccessTrends { get; set; } = new();

    // Competitive analysis
    public QuoteCompetitivePositionDto CompetitivePosition { get; set; } = new();
}

public class CarrierPricingAnalyticsDto
{
    public decimal AverageQuotePrice { get; set; }
    public decimal MedianQuotePrice { get; set; }
    public decimal HighestQuotePrice { get; set; }
    public decimal LowestQuotePrice { get; set; }
    public decimal PriceVariability { get; set; }
    public decimal PriceStandardDeviation { get; set; }

    // Pricing by route
    public List<RoutePricingAnalyticsDto> RoutePricing { get; set; } = new();

    // Pricing by service type
    public List<ServiceTypePricingDto> ServiceTypePricing { get; set; } = new();

    // Pricing trends over time
    public List<PricingTrendDto> PricingTrends { get; set; } = new();

    // Price optimization opportunities
    public List<PriceOptimizationOpportunityDto> OptimizationOpportunities { get; set; } = new();

    // Margin analysis
    public PriceMarginAnalysisDto MarginAnalysis { get; set; } = new();
}

public class CarrierMarketComparisonDto
{
    public decimal MarketAveragePrice { get; set; }
    public decimal CarrierAveragePrice { get; set; }
    public decimal PriceCompetitiveness { get; set; } // Percentage vs market average
    public int MarketRanking { get; set; }
    public int TotalCompetitors { get; set; }
    public decimal MarketShare { get; set; }

    // Competitor analysis
    public List<CompetitorComparisonDto> CompetitorComparisons { get; set; } = new();

    // Market positioning
    public MarketPositioningDto MarketPositioning { get; set; } = new();

    // Competitive advantages
    public List<CompetitiveAdvantageDto> CompetitiveAdvantages { get; set; } = new();

    // Market opportunities
    public List<MarketOpportunityDto> MarketOpportunities { get; set; } = new();
}

public class CarrierQuotingTrendsDto
{
    // Quote volume trends
    public List<QuoteVolumeTrendDto> QuoteVolumeTrends { get; set; } = new();

    // Success rate trends
    public List<QuoteSuccessTrendDto> SuccessRateTrends { get; set; } = new();

    // Pricing trends
    public List<PricingTrendDto> PricingTrends { get; set; } = new();

    // Response time trends
    public List<ResponseTimeTrendDto> ResponseTimeTrends { get; set; } = new();

    // Market share trends
    public List<MarketShareTrendDto> MarketShareTrends { get; set; } = new();
}

// Supporting DTOs
public class RFQResponsePatternDto
{
    public string Pattern { get; set; } = string.Empty; // e.g., "Quick Response", "Delayed Response"
    public int Count { get; set; }
    public decimal Percentage { get; set; }
    public decimal SuccessRate { get; set; }
}

public class RouteRFQResponseDto
{
    public string RouteId { get; set; } = string.Empty;
    public string RouteName { get; set; } = string.Empty;
    public int RFQsReceived { get; set; }
    public int RFQsResponded { get; set; }
    public decimal ResponseRate { get; set; }
    public TimeSpan AverageResponseTime { get; set; }
    public decimal SuccessRate { get; set; }
}

public class BrokerRFQResponseDto
{
    public Guid BrokerId { get; set; }
    public string BrokerName { get; set; } = string.Empty;
    public int RFQsReceived { get; set; }
    public int RFQsResponded { get; set; }
    public decimal ResponseRate { get; set; }
    public TimeSpan AverageResponseTime { get; set; }
    public decimal SuccessRate { get; set; }
}

public class RFQResponseTrendDto
{
    public DateTime Date { get; set; }
    public int RFQsReceived { get; set; }
    public int RFQsResponded { get; set; }
    public decimal ResponseRate { get; set; }
    public TimeSpan AverageResponseTime { get; set; }
}

// QuoteSuccessFactorDto is already defined in BrokerAnalyticsDto.cs - using that instead

public class RouteQuoteSuccessDto
{
    public string RouteId { get; set; } = string.Empty;
    public string RouteName { get; set; } = string.Empty;
    public int QuotesSubmitted { get; set; }
    public int QuotesAccepted { get; set; }
    public decimal SuccessRate { get; set; }
    public decimal AveragePrice { get; set; }
    public decimal MarketAveragePrice { get; set; }
}

public class BrokerQuoteSuccessDto
{
    public Guid BrokerId { get; set; }
    public string BrokerName { get; set; } = string.Empty;
    public int QuotesSubmitted { get; set; }
    public int QuotesAccepted { get; set; }
    public decimal SuccessRate { get; set; }
    public decimal AveragePrice { get; set; }
    public string RelationshipStrength { get; set; } = string.Empty; // Strong, Medium, Weak
}

// QuoteSuccessTrendDto is already defined in BrokerAnalyticsDto.cs - using that instead

public class QuoteCompetitivePositionDto
{
    public int Ranking { get; set; }
    public int TotalCarriers { get; set; }
    public decimal SuccessRatePercentile { get; set; }
    public decimal PricePercentile { get; set; }
    public decimal ResponseTimePercentile { get; set; }
    public string OverallPosition { get; set; } = string.Empty; // Leader, Challenger, Follower, Niche
}

public class RoutePricingAnalyticsDto
{
    public string RouteId { get; set; } = string.Empty;
    public string RouteName { get; set; } = string.Empty;
    public decimal AveragePrice { get; set; }
    public decimal MedianPrice { get; set; }
    public decimal HighestPrice { get; set; }
    public decimal LowestPrice { get; set; }
    public decimal MarketAveragePrice { get; set; }
    public decimal PriceCompetitiveness { get; set; }
    public int QuoteCount { get; set; }
}

public class ServiceTypePricingDto
{
    public string ServiceType { get; set; } = string.Empty; // FTL, LTL, Express, etc.
    public decimal AveragePrice { get; set; }
    public decimal MarketAveragePrice { get; set; }
    public decimal PriceCompetitiveness { get; set; }
    public int QuoteCount { get; set; }
    public decimal SuccessRate { get; set; }

    // Additional properties required by query handlers
    public decimal PriceGrowthRate { get; set; }
    public decimal MarketShare { get; set; }
    public decimal ProfitMargin { get; set; }
    public string PricingStrategy { get; set; } = string.Empty;
}



public class PriceOptimizationOpportunityDto
{
    public string OpportunityType { get; set; } = string.Empty; // Route, Service Type, Time Period
    public string Description { get; set; } = string.Empty;
    public decimal CurrentPrice { get; set; }
    public decimal RecommendedPrice { get; set; }
    public decimal PotentialImpact { get; set; } // Expected change in success rate
    public string Priority { get; set; } = string.Empty; // High, Medium, Low
}

public class PriceMarginAnalysisDto
{
    public decimal AverageMargin { get; set; }
    public decimal MedianMargin { get; set; }
    public decimal HighestMargin { get; set; }
    public decimal LowestMargin { get; set; }
    public decimal MarginVariability { get; set; }
    public List<RouteMarginDto> RouteMargins { get; set; } = new();
}

public class RouteMarginDto
{
    public string RouteId { get; set; } = string.Empty;
    public string RouteName { get; set; } = string.Empty;
    public decimal AverageMargin { get; set; }
    public decimal MarginTrend { get; set; }
    public string MarginCategory { get; set; } = string.Empty; // High, Medium, Low
}

public class CompetitorComparisonDto
{
    public string CompetitorName { get; set; } = string.Empty;
    public decimal AveragePrice { get; set; }
    public decimal SuccessRate { get; set; }
    public TimeSpan AverageResponseTime { get; set; }
    public decimal MarketShare { get; set; }
    public List<string> Strengths { get; set; } = new();
    public List<string> Weaknesses { get; set; } = new();
}

public class MarketPositioningDto
{
    public string Position { get; set; } = string.Empty; // Premium, Value, Budget
    public string Strategy { get; set; } = string.Empty; // Cost Leadership, Differentiation, Focus
    public decimal PricePositioning { get; set; } // Percentage vs market average
    public decimal QualityPositioning { get; set; }
    public decimal ServicePositioning { get; set; }
}

public class CompetitiveAdvantageDto
{
    public string Advantage { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal Impact { get; set; }
    public string Sustainability { get; set; } = string.Empty; // High, Medium, Low
}

// MarketOpportunityDto is already defined in AdminAnalyticsDto.cs - using that instead

public class QuoteVolumeTrendDto
{
    public DateTime Date { get; set; }
    public int QuoteVolume { get; set; }
    public int RFQVolume { get; set; }
    public decimal ResponseRate { get; set; }
}

public class ResponseTimeTrendDto
{
    public DateTime Date { get; set; }
    public TimeSpan AverageResponseTime { get; set; }
    public TimeSpan MedianResponseTime { get; set; }
    public decimal ResponseTimeImprovement { get; set; }
}

public class MarketShareTrendDto
{
    public DateTime Date { get; set; }
    public decimal MarketShare { get; set; }
    public decimal MarketShareChange { get; set; }
    public int TotalMarketVolume { get; set; }
}

public class CarrierQuotingInsightDto
{
    public string InsightType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Impact { get; set; } = string.Empty; // High, Medium, Low
    public List<string> SupportingData { get; set; } = new();
    public DateTime GeneratedAt { get; set; }
}

public class CarrierQuotingRecommendationDto
{
    public string RecommendationType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Priority { get; set; } = string.Empty; // High, Medium, Low
    public decimal ExpectedImpact { get; set; }
    public List<string> ActionItems { get; set; } = new();
    public string Timeline { get; set; } = string.Empty;
}

/// <summary>
/// Carrier ratings and feedback analytics
/// </summary>
public class CarrierRatingsAnalyticsDto
{
    public Guid CarrierId { get; set; }
    public string CarrierName { get; set; } = string.Empty;
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;

    // Overall Rating Summary
    public CarrierRatingSummaryDto RatingSummary { get; set; } = new();

    // Broker Ratings
    public CarrierBrokerRatingsDto BrokerRatings { get; set; } = new();

    // Shipper Ratings
    public CarrierShipperRatingsDto ShipperRatings { get; set; } = new();

    // Rating Trends
    public CarrierRatingTrendsDto RatingTrends { get; set; } = new();

    // Feedback Analysis
    public CarrierFeedbackAnalysisDto FeedbackAnalysis { get; set; } = new();

    // Competitive Analysis
    public CarrierRatingCompetitiveAnalysisDto CompetitiveAnalysis { get; set; } = new();

    // Improvement Insights
    public List<CarrierRatingInsightDto> Insights { get; set; } = new();

    // Action Recommendations
    public List<CarrierRatingRecommendationDto> Recommendations { get; set; } = new();
}

public class CarrierRatingSummaryDto
{
    public decimal OverallRating { get; set; }
    public decimal PreviousPeriodRating { get; set; }
    public decimal RatingChange { get; set; }
    public string RatingTrend { get; set; } = string.Empty; // Improving, Declining, Stable
    public int TotalRatings { get; set; }
    public int TotalReviews { get; set; }

    // Rating Distribution
    public List<RatingDistributionDto> RatingDistribution { get; set; } = new();

    // Category Ratings
    public List<CategoryRatingDto> CategoryRatings { get; set; } = new();

    // Rating Sources
    public List<RatingSourceDto> RatingSources { get; set; } = new();
}

public class CarrierBrokerRatingsDto
{
    public decimal AverageBrokerRating { get; set; }
    public int TotalBrokerRatings { get; set; }
    public int UniqueBrokers { get; set; }
    public decimal BrokerSatisfactionScore { get; set; }

    // Top Rated Brokers
    public List<BrokerRatingDto> TopRatedBrokers { get; set; } = new();

    // Broker Rating Trends
    public List<BrokerRatingTrendDto> BrokerRatingTrends { get; set; } = new();

    // Broker Feedback Themes
    public List<FeedbackThemeDto> BrokerFeedbackThemes { get; set; } = new();
}

public class CarrierShipperRatingsDto
{
    public decimal AverageShipperRating { get; set; }
    public int TotalShipperRatings { get; set; }
    public int UniqueShippers { get; set; }
    public decimal ShipperSatisfactionScore { get; set; }

    // Top Rated Shippers
    public List<ShipperRatingDto> TopRatedShippers { get; set; } = new();

    // Shipper Rating Trends
    public List<ShipperRatingTrendDto> ShipperRatingTrends { get; set; } = new();

    // Shipper Feedback Themes
    public List<FeedbackThemeDto> ShipperFeedbackThemes { get; set; } = new();
}

public class CarrierRatingTrendsDto
{
    // Overall rating trends
    public List<RatingTrendDataDto> OverallRatingTrends { get; set; } = new();

    // Category-wise trends
    public List<CategoryRatingTrendDto> CategoryRatingTrends { get; set; } = new();

    // Source-wise trends
    public List<SourceRatingTrendDto> SourceRatingTrends { get; set; } = new();

    // Seasonal patterns
    public List<SeasonalRatingPatternDto> SeasonalPatterns { get; set; } = new();
}

public class CarrierFeedbackAnalysisDto
{
    public int TotalFeedbackCount { get; set; }
    public decimal PositiveFeedbackPercentage { get; set; }
    public decimal NeutralFeedbackPercentage { get; set; }
    public decimal NegativeFeedbackPercentage { get; set; }

    // Sentiment Analysis
    public FeedbackSentimentDto SentimentAnalysis { get; set; } = new();

    // Common Themes
    public List<FeedbackThemeDto> CommonThemes { get; set; } = new();

    // Improvement Areas
    public List<ImprovementAreaDto> ImprovementAreas { get; set; } = new();

    // Positive Highlights
    public List<PositiveHighlightDto> PositiveHighlights { get; set; } = new();

    // Recent Feedback
    public List<RecentFeedbackDto> RecentFeedback { get; set; } = new();
}

public class CarrierRatingCompetitiveAnalysisDto
{
    public decimal IndustryAverageRating { get; set; }
    public decimal CarrierRating { get; set; }
    public decimal CompetitivePosition { get; set; } // Percentile ranking
    public int IndustryRanking { get; set; }
    public int TotalCompetitors { get; set; }

    // Competitor Comparisons
    public List<CompetitorRatingComparisonDto> CompetitorComparisons { get; set; } = new();

    // Benchmarking
    public RatingBenchmarkingDto Benchmarking { get; set; } = new();

    // Market Position
    public RatingMarketPositionDto MarketPosition { get; set; } = new();
}

// Supporting DTOs
public class RatingDistributionDto
{
    public int StarRating { get; set; } // 1-5 stars
    public int Count { get; set; }
    public decimal Percentage { get; set; }
}

public class CategoryRatingDto
{
    public string Category { get; set; } = string.Empty; // Service Quality, Communication, Timeliness, etc.
    public decimal Rating { get; set; }
    public decimal PreviousRating { get; set; }
    public decimal Change { get; set; }
    public string Trend { get; set; } = string.Empty;
    public int RatingCount { get; set; }
}

public class RatingSourceDto
{
    public string Source { get; set; } = string.Empty; // Broker, Shipper, Platform
    public decimal AverageRating { get; set; }
    public int RatingCount { get; set; }
    public decimal Percentage { get; set; }
}

public class BrokerRatingDto
{
    public Guid BrokerId { get; set; }
    public string BrokerName { get; set; } = string.Empty;
    public decimal Rating { get; set; }
    public int RatingCount { get; set; }
    public DateTime LastRatingDate { get; set; }
    public string RelationshipStrength { get; set; } = string.Empty;
    public List<string> PositiveComments { get; set; } = new();
    public List<string> ImprovementAreas { get; set; } = new();
}

public class ShipperRatingDto
{
    public Guid ShipperId { get; set; }
    public string ShipperName { get; set; } = string.Empty;
    public decimal Rating { get; set; }
    public int RatingCount { get; set; }
    public DateTime LastRatingDate { get; set; }
    public string BusinessVolume { get; set; } = string.Empty; // High, Medium, Low
    public List<string> PositiveComments { get; set; } = new();
    public List<string> ImprovementAreas { get; set; } = new();
}

public class BrokerRatingTrendDto
{
    public DateTime Date { get; set; }
    public decimal AverageRating { get; set; }
    public int RatingCount { get; set; }
    public decimal RatingChange { get; set; }
}

public class ShipperRatingTrendDto
{
    public DateTime Date { get; set; }
    public decimal AverageRating { get; set; }
    public int RatingCount { get; set; }
    public decimal RatingChange { get; set; }
}

public class FeedbackThemeDto
{
    public string Theme { get; set; } = string.Empty; // Communication, Timeliness, Service Quality, etc.
    public int MentionCount { get; set; }
    public decimal Percentage { get; set; }
    public string Sentiment { get; set; } = string.Empty; // Positive, Negative, Neutral
    public decimal ImpactScore { get; set; }
    public List<string> SampleComments { get; set; } = new();
}

public class RatingTrendDataDto
{
    public DateTime Date { get; set; }
    public decimal AverageRating { get; set; }
    public int RatingCount { get; set; }
    public decimal RatingChange { get; set; }
    public string TrendDirection { get; set; } = string.Empty;
}

public class CategoryRatingTrendDto
{
    public string Category { get; set; } = string.Empty;
    public List<RatingTrendDataDto> TrendData { get; set; } = new();
    public decimal OverallTrend { get; set; }
    public string TrendDirection { get; set; } = string.Empty;
}

public class SourceRatingTrendDto
{
    public string Source { get; set; } = string.Empty;
    public List<RatingTrendDataDto> TrendData { get; set; } = new();
    public decimal OverallTrend { get; set; }
    public string TrendDirection { get; set; } = string.Empty;
}

public class SeasonalRatingPatternDto
{
    public string Season { get; set; } = string.Empty; // Q1, Q2, Q3, Q4 or Month names
    public decimal AverageRating { get; set; }
    public decimal RatingVariability { get; set; }
    public string Pattern { get; set; } = string.Empty; // Peak, Low, Stable
    public List<string> InfluencingFactors { get; set; } = new();
}

public class FeedbackSentimentDto
{
    public decimal PositiveSentimentScore { get; set; }
    public decimal NeutralSentimentScore { get; set; }
    public decimal NegativeSentimentScore { get; set; }
    public decimal OverallSentimentScore { get; set; }
    public string DominantSentiment { get; set; } = string.Empty;
    public List<SentimentTrendDto> SentimentTrends { get; set; } = new();
}

public class SentimentTrendDto
{
    public DateTime Date { get; set; }
    public decimal PositiveSentiment { get; set; }
    public decimal NeutralSentiment { get; set; }
    public decimal NegativeSentiment { get; set; }
}

// ImprovementAreaDto is already defined in BrokerAnalyticsDTOs.cs - using that instead

public class PositiveHighlightDto
{
    public string Highlight { get; set; } = string.Empty;
    public decimal StrengthScore { get; set; }
    public int MentionCount { get; set; }
    public List<string> SampleComments { get; set; } = new();
}

public class RecentFeedbackDto
{
    public Guid FeedbackId { get; set; }
    public string Source { get; set; } = string.Empty; // Broker, Shipper
    public string SourceName { get; set; } = string.Empty;
    public decimal Rating { get; set; }
    public string Comment { get; set; } = string.Empty;
    public string Sentiment { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public string Category { get; set; } = string.Empty;
}

public class CompetitorRatingComparisonDto
{
    public string CompetitorName { get; set; } = string.Empty;
    public decimal AverageRating { get; set; }
    public int TotalRatings { get; set; }
    public decimal MarketShare { get; set; }
    public List<string> Strengths { get; set; } = new();
    public List<string> Weaknesses { get; set; } = new();
}

public class RatingBenchmarkingDto
{
    public decimal IndustryBenchmark { get; set; }
    public decimal RegionalBenchmark { get; set; }
    public decimal ServiceTypeBenchmark { get; set; }
    public decimal SizeBenchmark { get; set; } // Similar sized carriers
    public string BenchmarkPosition { get; set; } = string.Empty; // Above/Below/At benchmark
}

public class RatingMarketPositionDto
{
    public string Position { get; set; } = string.Empty; // Leader, Challenger, Follower, Niche
    public decimal PositionScore { get; set; }
    public List<string> PositionStrengths { get; set; } = new();
    public List<string> PositionOpportunities { get; set; } = new();
}

public class CarrierRatingInsightDto
{
    public string InsightType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Impact { get; set; } = string.Empty; // High, Medium, Low
    public decimal ConfidenceScore { get; set; }
    public List<string> SupportingData { get; set; } = new();
    public DateTime GeneratedAt { get; set; }
}

public class CarrierRatingRecommendationDto
{
    public string RecommendationType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Priority { get; set; } = string.Empty; // High, Medium, Low
    public decimal ExpectedImpact { get; set; }
    public List<string> ActionItems { get; set; } = new();
    public string Timeline { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty; // Service, Communication, Process, etc.
}

/// <summary>
/// Carrier performance metrics data transfer object
/// </summary>
public class CarrierPerformanceMetricsDto
{
    public Guid CarrierId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;

    // Delivery Performance
    public decimal DeliveryCompletionRate { get; set; }
    public decimal OnTimeDeliveryRate { get; set; }
    public decimal EarlyDeliveryRate { get; set; }
    public decimal LateDeliveryRate { get; set; }
    public decimal AverageDeliveryTime { get; set; }

    // Quality Metrics
    public decimal CustomerSatisfactionRating { get; set; }
    public decimal ServiceQualityScore { get; set; }
    public decimal DamageRate { get; set; }
    public decimal ComplaintRate { get; set; }

    // Efficiency Metrics
    public decimal VehicleUtilizationRate { get; set; }
    public decimal FuelEfficiency { get; set; }
    public decimal RouteOptimizationScore { get; set; }
    public decimal OperationalEfficiencyScore { get; set; }

    // Financial Metrics
    public decimal TotalEarnings { get; set; }
    public decimal AverageEarningsPerTrip { get; set; }
    public decimal EarningsGrowthRate { get; set; }
    public decimal ProfitMargin { get; set; }

    // Trends
    public List<CarrierPerformanceTrendDto> PerformanceTrends { get; set; } = new();
    public List<TopRoutePerformanceDto> TopRoutes { get; set; } = new();
    public List<BrokerRelationshipDto> TopBrokers { get; set; } = new();
}

/// <summary>
/// Carrier performance trend over time
/// </summary>
public class CarrierPerformanceTrendDto
{
    public DateTime Date { get; set; }
    public decimal DeliveryCompletionRate { get; set; }
    public decimal OnTimeDeliveryRate { get; set; }
    public decimal CustomerSatisfactionRating { get; set; }
    public decimal TotalEarnings { get; set; }
    public long TripsCompleted { get; set; }
}

/// <summary>
/// Top route performance for carrier
/// </summary>
public class TopRoutePerformanceDto
{
    public string RouteId { get; set; } = string.Empty;
    public string OriginCity { get; set; } = string.Empty;
    public string DestinationCity { get; set; } = string.Empty;
    public long TripsCompleted { get; set; }
    public decimal OnTimeDeliveryRate { get; set; }
    public decimal AverageEarnings { get; set; }
    public decimal CustomerSatisfaction { get; set; }
    public string PerformanceRating { get; set; } = string.Empty;
}

/// <summary>
/// Broker relationship data for carrier
/// </summary>
public class BrokerRelationshipDto
{
    public Guid BrokerId { get; set; }
    public string BrokerName { get; set; } = string.Empty;
    public long TripsCompleted { get; set; }
    public decimal TotalEarnings { get; set; }
    public decimal AverageRating { get; set; }
    public decimal PaymentTimeliness { get; set; }
    public string RelationshipStrength { get; set; } = string.Empty;
}

/// <summary>
/// Carrier delivery completion analytics data transfer object
/// </summary>
public class CarrierDeliveryCompletionAnalyticsDto
{
    public Guid CarrierId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;

    // Overall Delivery Metrics
    public long TotalTripsAssigned { get; set; }
    public long TripsCompleted { get; set; }
    public long TripsInProgress { get; set; }
    public long TripsCancelled { get; set; }
    public decimal CompletionRate { get; set; }

    // Delivery Performance
    public decimal OnTimeDeliveryRate { get; set; }
    public decimal EarlyDeliveryRate { get; set; }
    public decimal LateDeliveryRate { get; set; }
    public decimal AverageDeliveryTime { get; set; }
    public decimal DeliveryVariability { get; set; }

    // Route Analysis
    public List<RouteDeliveryPerformanceDto> RoutePerformance { get; set; } = new();

    // Service Level Analysis
    public List<ServiceLevelDeliveryDto> ServiceLevelPerformance { get; set; } = new();

    // Time-based Analysis
    public List<DeliveryCompletionTrendDto> CompletionTrends { get; set; } = new();

    // Issue Analysis
    public List<DeliveryIssueDto> DeliveryIssues { get; set; } = new();

    // Improvement Opportunities
    public List<DeliveryImprovementOpportunityDto> ImprovementOpportunities { get; set; } = new();
}

/// <summary>
/// Service level delivery performance
/// </summary>
public class ServiceLevelDeliveryDto
{
    public string ServiceLevel { get; set; } = string.Empty;
    public long TotalDeliveries { get; set; }
    public decimal CompletionRate { get; set; }
    public decimal OnTimeRate { get; set; }
    public decimal CustomerSatisfaction { get; set; }
    public decimal PremiumEarnings { get; set; }
}

/// <summary>
/// Delivery completion trend over time
/// </summary>
public class DeliveryCompletionTrendDto
{
    public DateTime Date { get; set; }
    public long TripsAssigned { get; set; }
    public long TripsCompleted { get; set; }
    public decimal CompletionRate { get; set; }
    public decimal OnTimeDeliveryRate { get; set; }
    public decimal CustomerSatisfaction { get; set; }
}

/// <summary>
/// Carrier efficiency tracking data transfer object
/// </summary>
public class CarrierEfficiencyTrackingDto
{
    public Guid CarrierId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;

    // Overall Efficiency Metrics
    public decimal OverallEfficiencyScore { get; set; }
    public decimal VehicleUtilizationRate { get; set; }
    public decimal FuelEfficiency { get; set; }
    public decimal RouteOptimizationScore { get; set; }
    public decimal TimeUtilizationRate { get; set; }

    // On-Time Delivery Performance
    public decimal OnTimeDeliveryRate { get; set; }
    public decimal DeliveryPredictability { get; set; }
    public decimal ScheduleAdherence { get; set; }

    // Service Quality Metrics
    public decimal ServiceQualityScore { get; set; }
    public decimal CustomerSatisfactionScore { get; set; }
    public decimal ServiceConsistency { get; set; }
    public decimal QualityImprovementRate { get; set; }

    // Efficiency Trends
    public List<EfficiencyTrendDto> EfficiencyTrends { get; set; } = new();

    // Efficiency Factors
    public List<EfficiencyFactorDto> EfficiencyFactors { get; set; } = new();

    // Benchmarking
    public EfficiencyBenchmarkingDto Benchmarking { get; set; } = new();

    // Improvement Recommendations
    public List<EfficiencyImprovementDto> ImprovementRecommendations { get; set; } = new();

    // Additional properties required by GetCarrierAnalyticsQueryHandler
    public List<EfficiencyTrendDto> EfficiencyTrend { get; set; } = new(); // Alias for EfficiencyTrends
    public decimal VehicleUtilization { get; set; } // Alias for VehicleUtilizationRate
    public decimal RouteEfficiency { get; set; } // Alias for RouteOptimizationScore
}

/// <summary>
/// Efficiency trend over time
/// </summary>
public class EfficiencyTrendDto
{
    public DateTime Date { get; set; }
    public decimal OverallEfficiencyScore { get; set; }
    public decimal VehicleUtilizationRate { get; set; }
    public decimal FuelEfficiency { get; set; }
    public decimal OnTimeDeliveryRate { get; set; }
    public decimal ServiceQualityScore { get; set; }
}

/// <summary>
/// Efficiency factor analysis
/// </summary>
public class EfficiencyFactorDto
{
    public string FactorName { get; set; } = string.Empty;
    public string FactorType { get; set; } = string.Empty;
    public decimal ImpactScore { get; set; }
    public string ImpactDirection { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<string> OptimizationTips { get; set; } = new();
}

/// <summary>
/// Efficiency benchmarking data
/// </summary>
public class EfficiencyBenchmarkingDto
{
    public decimal IndustryAverageEfficiency { get; set; }
    public decimal CarrierEfficiency { get; set; }
    public decimal EfficiencyRanking { get; set; }
    public string BenchmarkRating { get; set; } = string.Empty;
    public List<BenchmarkMetricDto> MetricBenchmarks { get; set; } = new();
}

/// <summary>
/// Benchmark metric comparison
/// </summary>
public class BenchmarkMetricDto
{
    public string MetricName { get; set; } = string.Empty;
    public decimal CarrierValue { get; set; }
    public decimal IndustryAverage { get; set; }
    public decimal TopPerformerValue { get; set; }
    public decimal PerformanceGap { get; set; }
    public string PerformanceRating { get; set; } = string.Empty;
}

/// <summary>
/// Efficiency improvement recommendation
/// </summary>
public class EfficiencyImprovementDto
{
    public string ImprovementType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal PotentialImpact { get; set; }
    public decimal ImplementationEffort { get; set; }
    public string Priority { get; set; } = string.Empty;
    public List<string> ActionSteps { get; set; } = new();
    public decimal ExpectedROI { get; set; }
}

/// <summary>
/// Carrier earnings analytics data transfer object
/// </summary>
public class CarrierEarningsAnalyticsDto
{
    public Guid CarrierId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;

    // Overall Earnings Metrics
    public decimal TotalEarnings { get; set; }
    public decimal AverageEarningsPerTrip { get; set; }
    public decimal AverageEarningsPerKilometer { get; set; }
    public decimal AverageEarningsPerHour { get; set; }
    public decimal EarningsGrowthRate { get; set; }

    // Income Trends
    public List<EarningsTrendDto> EarningsTrends { get; set; } = new();

    // Performance Benchmarking
    public EarningsBenchmarkingDto PerformanceBenchmarking { get; set; } = new();

    // Earnings Breakdown
    public List<EarningsBreakdownDto> EarningsBreakdown { get; set; } = new();

    // Route Profitability
    public List<RouteProfitabilityDto> RouteProfitability { get; set; } = new();

    // Broker Earnings Analysis
    public List<BrokerEarningsDto> BrokerEarnings { get; set; } = new();

    // Income Optimization
    public List<IncomeOptimizationOpportunityDto> OptimizationOpportunities { get; set; } = new();

    // Additional properties required by GetCarrierAnalyticsQueryHandler
    public decimal NetEarnings { get; set; }
    public List<EarningsTrendDto> IncomeTrends { get; set; } = new(); // Alias for EarningsTrends
}

/// <summary>
/// Earnings trend over time
/// </summary>
public class EarningsTrendDto
{
    public DateTime Date { get; set; }
    public decimal TotalEarnings { get; set; }
    public decimal AverageEarningsPerTrip { get; set; }
    public long TripsCompleted { get; set; }
    public decimal EarningsGrowthRate { get; set; }
}

/// <summary>
/// Earnings benchmarking data
/// </summary>
public class EarningsBenchmarkingDto
{
    public decimal IndustryAverageEarnings { get; set; }
    public decimal CarrierEarnings { get; set; }
    public decimal EarningsRanking { get; set; }
    public string PerformanceRating { get; set; } = string.Empty;
    public List<EarningsBenchmarkMetricDto> BenchmarkMetrics { get; set; } = new();
}

/// <summary>
/// Earnings benchmark metric
/// </summary>
public class EarningsBenchmarkMetricDto
{
    public string MetricName { get; set; } = string.Empty;
    public decimal CarrierValue { get; set; }
    public decimal IndustryAverage { get; set; }
    public decimal TopPerformerValue { get; set; }
    public decimal PerformanceGap { get; set; }
    public string Unit { get; set; } = string.Empty;
}

/// <summary>
/// Earnings breakdown by category
/// </summary>
public class EarningsBreakdownDto
{
    public string EarningsCategory { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty; // Alias for EarningsCategory
    public decimal Amount { get; set; }
    public decimal Percentage { get; set; }
    public decimal GrowthRate { get; set; }
    public string Description { get; set; } = string.Empty;
}

/// <summary>
/// Broker earnings analysis for carrier
/// </summary>
public class BrokerEarningsDto
{
    public Guid BrokerId { get; set; }
    public string BrokerName { get; set; } = string.Empty;
    public decimal TotalEarnings { get; set; }
    public decimal AverageEarningsPerTrip { get; set; }
    public long TripsCompleted { get; set; }
    public decimal EarningsShare { get; set; }
    public decimal PaymentTimeliness { get; set; }
    public string EarningsRating { get; set; } = string.Empty;
}

/// <summary>
/// Income optimization opportunity
/// </summary>
public class IncomeOptimizationOpportunityDto
{
    public string OpportunityType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal PotentialIncomeIncrease { get; set; }
    public decimal ImplementationEffort { get; set; }
    public string Priority { get; set; } = string.Empty;
    public List<string> ActionSteps { get; set; } = new();
    public decimal ExpectedROI { get; set; }
}

/// <summary>
/// Carrier growth opportunities data transfer object
/// </summary>
public class CarrierGrowthOpportunitiesDto
{
    public Guid CarrierId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }

    // Service Improvement Opportunities
    public List<ServiceImprovementOpportunityDto> ServiceImprovements { get; set; } = new();

    // Income Optimization Opportunities
    public List<IncomeOptimizationOpportunityDto> IncomeOptimizations { get; set; } = new();

    // Market Expansion Opportunities
    public List<MarketExpansionOpportunityDto> MarketExpansions { get; set; } = new();

    // Technology Adoption Opportunities
    public List<TechnologyAdoptionOpportunityDto> TechnologyAdoptions { get; set; } = new();

    // Partnership Opportunities
    public List<PartnershipOpportunityDto> PartnershipOpportunities { get; set; } = new();

    // Skill Development Opportunities
    public List<SkillDevelopmentOpportunityDto> SkillDevelopments { get; set; } = new();

    // Growth Potential Assessment
    public GrowthPotentialAssessmentDto GrowthPotential { get; set; } = new();
}

/// <summary>
/// Service improvement opportunity
/// </summary>
public class ServiceImprovementOpportunityDto
{
    public string ImprovementType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal CurrentPerformance { get; set; }
    public decimal TargetPerformance { get; set; }
    public decimal PotentialImpact { get; set; }
    public string Priority { get; set; } = string.Empty;
    public List<string> ActionSteps { get; set; } = new();
    public string Timeline { get; set; } = string.Empty;
}

/// <summary>
/// Market expansion opportunity
/// </summary>
public class MarketExpansionOpportunityDto
{
    public string MarketType { get; set; } = string.Empty;
    public string GeographicArea { get; set; } = string.Empty;
    public string ServiceType { get; set; } = string.Empty;
    public decimal MarketSize { get; set; }
    public decimal PotentialRevenue { get; set; }
    public decimal CompetitionLevel { get; set; }
    public decimal EntryBarriers { get; set; }
    public string Attractiveness { get; set; } = string.Empty;
    public List<string> RequiredCapabilities { get; set; } = new();
}

/// <summary>
/// Technology adoption opportunity
/// </summary>
public class TechnologyAdoptionOpportunityDto
{
    public string TechnologyType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal PotentialBenefit { get; set; }
    public decimal ImplementationCost { get; set; }
    public decimal ROI { get; set; }
    public string AdoptionDifficulty { get; set; } = string.Empty;
    public List<string> Benefits { get; set; } = new();
}

/// <summary>
/// Partnership opportunity
/// </summary>
public class PartnershipOpportunityDto
{
    public string PartnershipType { get; set; } = string.Empty;
    public string PartnerName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal PotentialValue { get; set; }
    public string PartnershipStrength { get; set; } = string.Empty;
    public List<string> Benefits { get; set; } = new();
    public List<string> Requirements { get; set; } = new();
}

/// <summary>
/// Skill development opportunity
/// </summary>
public class SkillDevelopmentOpportunityDto
{
    public string SkillType { get; set; } = string.Empty;
    public string SkillName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal CurrentLevel { get; set; }
    public decimal TargetLevel { get; set; }
    public decimal PotentialImpact { get; set; }
    public string DevelopmentPath { get; set; } = string.Empty;
    public List<string> LearningResources { get; set; } = new();
}

/// <summary>
/// Growth potential assessment
/// </summary>
public class GrowthPotentialAssessmentDto
{
    public decimal OverallGrowthScore { get; set; }
    public decimal ServiceGrowthPotential { get; set; }
    public decimal IncomeGrowthPotential { get; set; }
    public decimal MarketGrowthPotential { get; set; }
    public decimal TechnologyGrowthPotential { get; set; }
    public List<GrowthFactorDto> GrowthFactors { get; set; } = new();
    public List<GrowthBarrierDto> GrowthBarriers { get; set; } = new();
    public List<string> GrowthRecommendations { get; set; } = new();
}

/// <summary>
/// Growth factor analysis
/// </summary>
public class GrowthFactorDto
{
    public string FactorName { get; set; } = string.Empty;
    public decimal StrengthScore { get; set; }
    public string Impact { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<string> LeverageStrategies { get; set; } = new();
}

/// <summary>
/// Growth barrier identification
/// </summary>
public class GrowthBarrierDto
{
    public string BarrierName { get; set; } = string.Empty;
    public decimal SeverityScore { get; set; }
    public string Impact { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<string> MitigationStrategies { get; set; } = new();
}
