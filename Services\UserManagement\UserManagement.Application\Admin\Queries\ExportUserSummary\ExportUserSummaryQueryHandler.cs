using System.Text;
using MediatR;
using Microsoft.Extensions.Logging;
using UserManagement.Application.Admin.Queries.GetUsersAdvanced;
using UserManagement.Application.Services;
using UserManagement.Domain.Repositories;
using UserManagement.Domain.ValueObjects;
using UserManagement.Application.Admin.Queries.GetUsersAdvanced;

namespace UserManagement.Application.Admin.Queries.ExportUserSummary
{
    public class ExportUserSummaryQueryHandler : IRequestHandler<ExportUserSummaryQuery, UserSummaryExportResponse>
    {
        private readonly IUserProfileRepository _userProfileRepository;
        private readonly IDocumentSubmissionRepository _documentSubmissionRepository;
        private readonly IFileStorageService _fileStorageService;
        private readonly IAuditService _auditService;
        private readonly ILogger<ExportUserSummaryQueryHandler> _logger;

        public ExportUserSummaryQueryHandler(
            IUserProfileRepository userProfileRepository,
            IDocumentSubmissionRepository documentSubmissionRepository,
            IFileStorageService fileStorageService,
            IAuditService auditService,
            ILogger<ExportUserSummaryQueryHandler> logger)
        {
            _userProfileRepository = userProfileRepository;
            _documentSubmissionRepository = documentSubmissionRepository;
            _fileStorageService = fileStorageService;
            _auditService = auditService;
            _logger = logger;
        }

        public async Task<UserSummaryExportResponse> Handle(ExportUserSummaryQuery request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Starting user summary export for user {UserId} with format {Format}",
                request.RequestedBy, request.Format);

            try
            {
                // Log audit trail for export request
                await _auditService.LogDataExportAsync(
                    "UserSummary",
                    request.RequestedBy,
                    request.RequestedByRole,
                    request.IpAddress,
                    request.UserAgent,
                    new Dictionary<string, object>
                    {
                        ["Format"] = request.Format.ToString(),
                        ["FromDate"] = request.FromDate?.ToString("yyyy-MM-dd"),
                        ["ToDate"] = request.ToDate?.ToString("yyyy-MM-dd"),
                        ["UserType"] = request.UserType?.ToString(),
                        ["Status"] = request.Status?.ToString(),
                        ["Location"] = request.Location,
                        ["MaxRecords"] = request.MaxRecords
                    });

                // Get filtered users data
                var users = await GetFilteredUsersAsync(request);

                // Apply record limit if specified
                if (request.MaxRecords.HasValue && users.Count > request.MaxRecords.Value)
                {
                    users = users.Take(request.MaxRecords.Value).ToList();
                }

                // Generate export content based on format
                var exportContent = await GenerateExportContentAsync(users, request);

                // Generate unique filename
                var exportId = Guid.NewGuid().ToString("N")[..8];
                var timestamp = DateTime.UtcNow.ToString("yyyyMMdd_HHmmss");
                var fileName = $"user_summary_{timestamp}_{exportId}.{GetFileExtension(request.Format)}";

                // Save to file storage
                var fileUrl = await _fileStorageService.SaveExportFileAsync(fileName, exportContent, GetContentType(request.Format));

                var response = new UserSummaryExportResponse
                {
                    FileName = fileName,
                    FileUrl = fileUrl,
                    Format = request.Format,
                    RecordCount = users.Count,
                    FileSizeBytes = Encoding.UTF8.GetByteCount(exportContent),
                    GeneratedAt = DateTime.UtcNow,
                    ExpiresAt = DateTime.UtcNow.AddDays(7), // Files expire after 7 days
                    ExportId = exportId
                };

                _logger.LogInformation("Successfully generated user summary export: {FileName} with {RecordCount} records",
                    fileName, users.Count);

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating user summary export for user {UserId}", request.RequestedBy);
                throw;
            }
        }

        private async Task<List<UserAdvancedDto>> GetFilteredUsersAsync(ExportUserSummaryQuery request)
        {
            // Build filter criteria
            var filters = new Dictionary<string, object>();

            if (request.FromDate.HasValue)
                filters["FromDate"] = request.FromDate.Value;

            if (request.ToDate.HasValue)
                filters["ToDate"] = request.ToDate.Value;

            if (request.UserType.HasValue)
                filters["UserType"] = request.UserType.Value;

            if (request.Status.HasValue)
                filters["Status"] = request.Status.Value;

            if (!string.IsNullOrWhiteSpace(request.Location))
                filters["Location"] = request.Location;

            if (!string.IsNullOrWhiteSpace(request.Region))
                filters["Region"] = request.Region;

            // Get users with applied filters
            var users = await _userProfileRepository.GetUsersAdvancedAsync(filters, 1, int.MaxValue);

            return users.Select(MapToUserAdvancedDto).ToList();
        }

        private UserAdvancedDto MapToUserAdvancedDto(Domain.Entities.UserProfile user)
        {
            return new UserAdvancedDto
            {
                Id = user.Id,
                UserId = user.UserId,
                UserType = user.UserType,
                Status = user.Status,
                PersonalInfo = new PersonalInfo(
                    user.FirstName,
                    user.LastName,
                    user.Email,
                    user.PhoneNumber
                ),
                CompanyInfo = new CompanyInfo(
                    user.CompanyName,
                    gstNumber: user.GstNumber,
                    panNumber: user.PanNumber
                ),
                AddressInfo = new AddressInfo
                {
                    Street = user.Address,
                    City = user.City,
                    State = user.State,
                    Country = user.Country,
                    PostalCode = user.PostalCode,
                    Region = user.Region
                },
                ActivityInfo = new ActivityInfo
                {
                    LastLoginAt = user.LastLoginAt,
                    LastLoginIp = user.LastLoginIp,
                    RfqCount = user.RfqCount ?? 0,
                    OrderCount = user.OrderCount ?? 0,
                    TripCount = user.TripCount ?? 0,
                    TotalBusinessValue = user.TotalBusinessValue
                },
                DocumentSummary = new DocumentSummary
                {
                    TotalDocuments = user.TotalDocuments ?? 0,
                    ApprovedDocuments = user.ApprovedDocuments ?? 0,
                    PendingDocuments = user.PendingDocuments ?? 0,
                    RejectedDocuments = user.RejectedDocuments ?? 0,
                    LastDocumentUpload = user.LastDocumentUpload
                },
                CreatedAt = user.CreatedAt,
                UpdatedAt = user.UpdatedAt,
                ApprovedAt = user.ApprovedAt,
                RejectedAt = user.RejectedAt
            };
        }

        private async Task<string> GenerateExportContentAsync(List<UserAdvancedDto> users, ExportUserSummaryQuery request)
        {
            return request.Format switch
            {
                ExportFormat.CSV => await GenerateCsvContentAsync(users, request),
                ExportFormat.PDF => await GeneratePdfContentAsync(users, request),
                ExportFormat.Excel => await GenerateExcelContentAsync(users, request),
                _ => throw new ArgumentException($"Unsupported export format: {request.Format}")
            };
        }

        private async Task<string> GenerateCsvContentAsync(List<UserAdvancedDto> users, ExportUserSummaryQuery request)
        {
            var csv = new StringBuilder();

            // Generate headers based on selected columns or default columns
            var columns = GetSelectedColumns(request);
            csv.AppendLine(string.Join(",", columns.Select(c => $"\"{c}\"")));

            // Generate data rows
            foreach (var user in users)
            {
                var row = new List<string>();

                foreach (var column in columns)
                {
                    var value = GetColumnValue(user, column);
                    row.Add($"\"{value?.ToString()?.Replace("\"", "\"\"") ?? ""}\"");
                }

                csv.AppendLine(string.Join(",", row));
            }

            return csv.ToString();
        }

        private async Task<string> GeneratePdfContentAsync(List<UserAdvancedDto> users, ExportUserSummaryQuery request)
        {
            // For now, return a simple text representation
            // In a real implementation, you would use a PDF library like iTextSharp or PdfSharp
            var content = new StringBuilder();
            content.AppendLine("USER SUMMARY REPORT");
            content.AppendLine($"Generated: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC");
            content.AppendLine($"Total Records: {users.Count}");
            content.AppendLine(new string('=', 50));

            foreach (var user in users)
            {
                content.AppendLine($"User ID: {user.UserId}");
                content.AppendLine($"Name: {user.PersonalInfo?.FirstName} {user.PersonalInfo?.LastName}");
                content.AppendLine($"Email: {user.PersonalInfo?.Email}");
                content.AppendLine($"Type: {user.UserType}");
                content.AppendLine($"Status: {user.Status}");
                content.AppendLine($"Company: {user.CompanyInfo?.CompanyName}");
                content.AppendLine($"Created: {user.CreatedAt:yyyy-MM-dd}");
                content.AppendLine(new string('-', 30));
            }

            return content.ToString();
        }

        private async Task<string> GenerateExcelContentAsync(List<UserAdvancedDto> users, ExportUserSummaryQuery request)
        {
            // For now, return CSV format
            // In a real implementation, you would use a library like EPPlus or ClosedXML
            return await GenerateCsvContentAsync(users, request);
        }

        private List<string> GetSelectedColumns(ExportUserSummaryQuery request)
        {
            if (request.SelectedColumns?.Any() == true)
            {
                return request.SelectedColumns;
            }

            // Default columns
            var columns = new List<string>
            {
                "UserId", "FirstName", "LastName", "Email", "PhoneNumber",
                "UserType", "Status", "CompanyName", "City", "State", "CreatedAt"
            };

            if (request.IncludeActivityMetrics)
            {
                columns.AddRange(new[] { "RfqCount", "OrderCount", "TripCount", "TotalBusinessValue", "LastLoginAt" });
            }

            if (request.IncludeDocumentSummary)
            {
                columns.AddRange(new[] { "TotalDocuments", "ApprovedDocuments", "PendingDocuments", "RejectedDocuments" });
            }

            return columns;
        }

        private object? GetColumnValue(UserAdvancedDto user, string column)
        {
            return column switch
            {
                "UserId" => user.UserId,
                "FirstName" => user.PersonalInfo?.FirstName,
                "LastName" => user.PersonalInfo?.LastName,
                "Email" => user.PersonalInfo?.Email,
                "PhoneNumber" => user.PersonalInfo?.PhoneNumber,
                "UserType" => user.UserType,
                "Status" => user.Status,
                "CompanyName" => user.CompanyInfo?.CompanyName,
                "GstNumber" => user.CompanyInfo?.GstNumber,
                "PanNumber" => user.CompanyInfo?.PanNumber,
                "City" => user.AddressInfo?.City,
                "State" => user.AddressInfo?.State,
                "Country" => user.AddressInfo?.Country,
                "Region" => user.AddressInfo?.Region,
                "RfqCount" => user.ActivityInfo?.RfqCount,
                "OrderCount" => user.ActivityInfo?.OrderCount,
                "TripCount" => user.ActivityInfo?.TripCount,
                "TotalBusinessValue" => user.ActivityInfo?.TotalBusinessValue,
                "LastLoginAt" => user.ActivityInfo?.LastLoginAt?.ToString("yyyy-MM-dd HH:mm:ss"),
                "TotalDocuments" => user.DocumentSummary?.TotalDocuments,
                "ApprovedDocuments" => user.DocumentSummary?.ApprovedDocuments,
                "PendingDocuments" => user.DocumentSummary?.PendingDocuments,
                "RejectedDocuments" => user.DocumentSummary?.RejectedDocuments,
                "CreatedAt" => user.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss"),
                "UpdatedAt" => user.UpdatedAt?.ToString("yyyy-MM-dd HH:mm:ss"),
                "ApprovedAt" => user.ApprovedAt?.ToString("yyyy-MM-dd HH:mm:ss"),
                _ => null
            };
        }

        private static string GetFileExtension(ExportFormat format)
        {
            return format switch
            {
                ExportFormat.CSV => "csv",
                ExportFormat.PDF => "pdf",
                ExportFormat.Excel => "xlsx",
                _ => "txt"
            };
        }

        private static string GetContentType(ExportFormat format)
        {
            return format switch
            {
                ExportFormat.CSV => "text/csv",
                ExportFormat.PDF => "application/pdf",
                ExportFormat.Excel => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                _ => "text/plain"
            };
        }
    }
}
