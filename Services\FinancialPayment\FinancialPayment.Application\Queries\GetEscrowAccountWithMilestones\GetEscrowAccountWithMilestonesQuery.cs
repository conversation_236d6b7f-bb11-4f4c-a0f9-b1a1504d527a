using MediatR;
using FinancialPayment.Application.DTOs;

namespace FinancialPayment.Application.Queries.GetEscrowAccountWithMilestones;

public class GetEscrowAccountWithMilestonesQuery : IRequest<EscrowAccountDto?>
{
    public Guid EscrowAccountId { get; set; }
    public Guid RequestingUserId { get; set; }
    public bool IncludeTransactions { get; set; } = true;
    public bool IncludeMilestoneDocuments { get; set; } = true;
    public bool IncludeDisputeInfo { get; set; } = true;
    public bool CalculateSummary { get; set; } = true;
}
