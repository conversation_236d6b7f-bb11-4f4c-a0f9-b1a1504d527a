using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using TripManagement.Application.Commands.CollectDigitalSignature;
using TripManagement.Application.Commands.UploadDeliveryPhoto;
using TripManagement.Application.Commands.VerifyRecipient;
using TripManagement.Application.Interfaces;
using TripManagement.Application.Queries.GetProofOfDelivery;
using TripManagement.Domain.Enums;
using TripManagement.Infrastructure.Persistence;
using TripManagement.Infrastructure.Repositories;
using Xunit;

namespace TripManagement.Tests.Integration;

public class ProofOfDeliveryIntegrationTests : IDisposable
{
    private readonly TripManagementDbContext _context;
    private readonly ITripRepository _tripRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly Mock<IMessageBroker> _messageBrokerMock;
    private readonly Mock<IFileStorageService> _fileStorageServiceMock;
    private readonly Mock<INotificationService> _notificationServiceMock;

    public ProofOfDeliveryIntegrationTests()
    {
        var options = new DbContextOptionsBuilder<TripManagementDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new TripManagementDbContext(options);
        _tripRepository = new TripRepository(_context);
        _unitOfWork = new UnitOfWork(_context);
        _messageBrokerMock = new Mock<IMessageBroker>();
        _fileStorageServiceMock = new Mock<IFileStorageService>();
        _notificationServiceMock = new Mock<INotificationService>();
    }

    [Fact]
    public async Task CollectDigitalSignature_EndToEnd_Should_Work_Successfully()
    {
        // Arrange
        var trip = await CreateTestTripWithStops();
        var driverId = Guid.NewGuid();
        trip.AssignDriverAndVehicle(driverId, Guid.NewGuid());
        _tripRepository.Update(trip);
        await _unitOfWork.SaveChangesAsync();

        var tripStop = trip.Stops.First();
        var loggerMock = new Mock<ILogger<CollectDigitalSignatureCommandHandler>>();
        
        var handler = new CollectDigitalSignatureCommandHandler(
            _tripRepository,
            _unitOfWork,
            loggerMock.Object,
            _messageBrokerMock.Object);

        var command = new CollectDigitalSignatureCommand
        {
            TripStopId = tripStop.Id,
            DriverId = driverId,
            RecipientName = "John Doe",
            SignatureData = "base64encodedSignatureData",
            Notes = "Package delivered successfully",
            DeliveredAt = DateTime.UtcNow
        };

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeTrue();

        var updatedTrip = await _tripRepository.GetByIdAsync(trip.Id);
        var updatedStop = updatedTrip!.Stops.First(s => s.Id == tripStop.Id);
        
        updatedStop.ProofOfDeliveries.Should().HaveCount(1);
        var pod = updatedStop.ProofOfDeliveries.First();
        pod.RecipientName.Should().Be("John Doe");
        pod.RecipientSignature.Should().Be("base64encodedSignatureData");
        pod.IsDigitalSignature.Should().BeTrue();
        pod.Notes.Should().Be("Package delivered successfully");

        // Verify message broker was called
        _messageBrokerMock.Verify(m => m.PublishAsync(
            "trip.signature_collected",
            It.IsAny<object>(),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task UploadDeliveryPhoto_EndToEnd_Should_Work_Successfully()
    {
        // Arrange
        var trip = await CreateTestTripWithStops();
        var driverId = Guid.NewGuid();
        trip.AssignDriverAndVehicle(driverId, Guid.NewGuid());
        _tripRepository.Update(trip);
        await _unitOfWork.SaveChangesAsync();

        var tripStop = trip.Stops.First();
        var photoUrl = "https://storage.example.com/photos/delivery_photo_123.jpg";
        
        _fileStorageServiceMock
            .Setup(f => f.UploadFileAsync(It.IsAny<Stream>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(photoUrl);

        var loggerMock = new Mock<ILogger<UploadDeliveryPhotoCommandHandler>>();
        var handler = new UploadDeliveryPhotoCommandHandler(
            _tripRepository,
            _unitOfWork,
            _fileStorageServiceMock.Object,
            loggerMock.Object,
            _messageBrokerMock.Object);

        // Create mock photo file
        var photoMock = new Mock<Microsoft.AspNetCore.Http.IFormFile>();
        photoMock.Setup(f => f.Length).Returns(1024 * 1024); // 1MB
        photoMock.Setup(f => f.ContentType).Returns("image/jpeg");
        photoMock.Setup(f => f.OpenReadStream()).Returns(new MemoryStream(new byte[1024]));

        var command = new UploadDeliveryPhotoCommand
        {
            TripStopId = tripStop.Id,
            DriverId = driverId,
            Photo = photoMock.Object,
            Description = "Package at doorstep",
            TakenAt = DateTime.UtcNow
        };

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().Be(photoUrl);

        var updatedTrip = await _tripRepository.GetByIdAsync(trip.Id);
        var updatedStop = updatedTrip!.Stops.First(s => s.Id == tripStop.Id);
        
        updatedStop.ProofOfDeliveries.Should().HaveCount(1);
        var pod = updatedStop.ProofOfDeliveries.First();
        pod.PhotoUrl.Should().Be(photoUrl);

        // Verify file storage service was called
        _fileStorageServiceMock.Verify(f => f.UploadFileAsync(
            It.IsAny<Stream>(),
            It.IsAny<string>(),
            "image/jpeg",
            It.IsAny<CancellationToken>()), Times.Once);

        // Verify message broker was called
        _messageBrokerMock.Verify(m => m.PublishAsync(
            "trip.photo_uploaded",
            It.IsAny<object>(),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task VerifyRecipient_EndToEnd_Should_Work_Successfully()
    {
        // Arrange
        var trip = await CreateTestTripWithStops();
        var driverId = Guid.NewGuid();
        trip.AssignDriverAndVehicle(driverId, Guid.NewGuid());
        _tripRepository.Update(trip);
        await _unitOfWork.SaveChangesAsync();

        var tripStop = trip.Stops.First();
        var loggerMock = new Mock<ILogger<VerifyRecipientCommandHandler>>();
        
        var handler = new VerifyRecipientCommandHandler(
            _tripRepository,
            _unitOfWork,
            loggerMock.Object,
            _messageBrokerMock.Object);

        var command = new VerifyRecipientCommand
        {
            TripStopId = tripStop.Id,
            DriverId = driverId,
            RecipientName = "Jane Smith",
            RecipientPhone = "******-0123",
            RecipientEmail = "<EMAIL>",
            IdentificationNumber = "DL123456789",
            IdentificationType = "Driver License",
            Notes = "ID verified successfully"
        };

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeTrue();

        var updatedTrip = await _tripRepository.GetByIdAsync(trip.Id);
        var updatedStop = updatedTrip!.Stops.First(s => s.Id == tripStop.Id);
        
        updatedStop.ContactName.Should().Be("Jane Smith");
        updatedStop.ContactPhone.Should().Be("******-0123");

        // Verify message broker was called
        _messageBrokerMock.Verify(m => m.PublishAsync(
            "trip.recipient_verified",
            It.IsAny<object>(),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetProofOfDelivery_Should_Return_Correct_POD()
    {
        // Arrange
        var trip = await CreateTestTripWithStops();
        var tripStop = trip.Stops.First();
        
        // Add POD to the trip stop
        var pod = new TripManagement.Domain.Entities.ProofOfDelivery(
            tripStopId: tripStop.Id,
            recipientName: "Test Recipient",
            recipientSignature: "signature_data",
            notes: "Test delivery",
            isDigitalSignature: true,
            deliveredBy: "driver123");

        tripStop.AddProofOfDelivery(pod);
        _tripRepository.Update(trip);
        await _unitOfWork.SaveChangesAsync();

        var mapperMock = new Mock<AutoMapper.IMapper>();
        mapperMock.Setup(m => m.Map<TripManagement.Application.DTOs.ProofOfDeliveryDto>(It.IsAny<TripManagement.Domain.Entities.ProofOfDelivery>()))
            .Returns((TripManagement.Domain.Entities.ProofOfDelivery p) => new TripManagement.Application.DTOs.ProofOfDeliveryDto
            {
                Id = p.Id,
                TripStopId = p.TripStopId,
                RecipientName = p.RecipientName,
                RecipientSignature = p.RecipientSignature,
                PhotoUrl = p.PhotoUrl,
                Notes = p.Notes,
                IsDigitalSignature = p.IsDigitalSignature,
                DeliveredAt = p.DeliveredAt,
                DeliveredBy = p.DeliveredBy
            });

        var loggerMock = new Mock<ILogger<GetProofOfDeliveryQueryHandler>>();
        var queryHandler = new GetProofOfDeliveryQueryHandler(
            _tripRepository,
            mapperMock.Object,
            loggerMock.Object);

        var query = new GetProofOfDeliveryQuery { TripStopId = tripStop.Id };

        // Act
        var result = await queryHandler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result!.TripStopId.Should().Be(tripStop.Id);
        result.RecipientName.Should().Be("Test Recipient");
        result.IsDigitalSignature.Should().BeTrue();
    }

    private async Task<TripManagement.Domain.Entities.Trip> CreateTestTripWithStops()
    {
        var startLocation = new TripManagement.Domain.ValueObjects.Location(40.7128, -74.0060, DateTime.UtcNow, address: "New York, NY");
        var endLocation = new TripManagement.Domain.ValueObjects.Location(34.0522, -118.2437, DateTime.UtcNow, address: "Los Angeles, CA");
        var route = new TripManagement.Domain.ValueObjects.Route(startLocation, endLocation, estimatedDistanceKm: 3944);

        var trip = new TripManagement.Domain.Entities.Trip(
            Guid.NewGuid(),
            Guid.NewGuid(),
            route,
            DateTime.UtcNow.AddHours(1),
            DateTime.UtcNow.AddHours(41),
            "Test trip with stops",
            false,
            3944m);

        // Add stops
        var pickupStop = new TripManagement.Domain.Entities.TripStop(
            trip.Id,
            TripStopType.Pickup,
            1,
            startLocation,
            DateTime.UtcNow.AddHours(1));

        var deliveryStop = new TripManagement.Domain.Entities.TripStop(
            trip.Id,
            TripStopType.Delivery,
            2,
            endLocation,
            DateTime.UtcNow.AddHours(40));

        trip.AddStop(pickupStop);
        trip.AddStop(deliveryStop);

        await _tripRepository.AddAsync(trip);
        await _unitOfWork.SaveChangesAsync();

        return trip;
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
