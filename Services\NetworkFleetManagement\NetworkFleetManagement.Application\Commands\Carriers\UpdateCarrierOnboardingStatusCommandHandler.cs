using AutoMapper;
using MediatR;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Repositories;
using Shared.Messaging;

namespace NetworkFleetManagement.Application.Commands.Carriers;

public class UpdateCarrierOnboardingStatusCommandHandler : IRequestHandler<UpdateCarrierOnboardingStatusCommand, CarrierDto>
{
    private readonly ICarrierRepository _carrierRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly IMessageBroker _messageBroker;

    public UpdateCarrierOnboardingStatusCommandHandler(
        ICarrierRepository carrierRepository,
        IUnitOfWork unitOfWork,
        IMapper mapper,
        IMessageBroker messageBroker)
    {
        _carrierRepository = carrierRepository;
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _messageBroker = messageBroker;
    }

    public async Task<CarrierDto> Handle(UpdateCarrierOnboardingStatusCommand request, CancellationToken cancellationToken)
    {
        var carrier = await _carrierRepository.GetByIdAsync(request.CarrierId, cancellationToken);
        if (carrier == null)
        {
            throw new InvalidOperationException($"Carrier with ID {request.CarrierId} not found");
        }

        var oldStatus = carrier.OnboardingStatus;
        carrier.UpdateOnboardingStatus(request.StatusDto.Status);

        _carrierRepository.Update(carrier);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        // Publish integration event
        await _messageBroker.PublishAsync("carrier.onboarding_status_changed", new
        {
            CarrierId = carrier.Id,
            UserId = carrier.UserId,
            CompanyName = carrier.CompanyName,
            OldOnboardingStatus = oldStatus.ToString(),
            NewOnboardingStatus = carrier.OnboardingStatus.ToString(),
            UpdatedAt = carrier.UpdatedAt
        }, cancellationToken);

        var result = _mapper.Map<CarrierDto>(carrier);
        return result;
    }
}
