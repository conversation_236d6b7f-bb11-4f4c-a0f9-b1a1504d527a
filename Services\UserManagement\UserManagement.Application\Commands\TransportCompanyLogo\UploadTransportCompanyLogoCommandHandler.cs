using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Primitives;
using Shared.Domain.Common;
using UserManagement.Application.Interfaces;
using UserManagement.Domain.Entities;
using UserManagement.Domain.Repositories;
using TransportCompanyLogoEntity = UserManagement.Application.Interfaces.TransportCompanyLogo;

namespace UserManagement.Application.Commands.TransportCompanyLogo;

/// <summary>
/// Handler for uploading Transport Company logo
/// </summary>
public class UploadTransportCompanyLogoCommandHandler :
    IRequestHandler<UploadTransportCompanyLogoCommand, UploadTransportCompanyLogoResult>
{
    private readonly IImageProcessingService _imageProcessingService;
    private readonly IFileStorageService _fileStorageService;
    private readonly ITransportCompanyRepository _transportCompanyRepository;
    private readonly ITransportCompanyLogoRepository _logoRepository;
    private readonly IConfiguration _configuration;
    private readonly ILogger<UploadTransportCompanyLogoCommandHandler> _logger;

    // Configuration constants
    private readonly int _maxFileSizeBytes;
    private readonly int _maxWidthPixels;
    private readonly int _maxHeightPixels;
    private readonly List<string> _allowedContentTypes;

    public UploadTransportCompanyLogoCommandHandler(
        IImageProcessingService imageProcessingService,
        IFileStorageService fileStorageService,
        ITransportCompanyRepository transportCompanyRepository,
        ITransportCompanyLogoRepository logoRepository,
        IConfiguration configuration,
        ILogger<UploadTransportCompanyLogoCommandHandler> logger)
    {
        _imageProcessingService = imageProcessingService;
        _fileStorageService = fileStorageService;
        _transportCompanyRepository = transportCompanyRepository;
        _logoRepository = logoRepository;
        _configuration = configuration;
        _logger = logger;

        // Load configuration
        _maxFileSizeBytes = _configuration.GetValue<int>("Logo:MaxFileSizeBytes", 5 * 1024 * 1024); // 5MB default
        _maxWidthPixels = _configuration.GetValue<int>("Logo:MaxWidthPixels", 2000);
        _maxHeightPixels = _configuration.GetValue<int>("Logo:MaxHeightPixels", 2000);
        _allowedContentTypes = _configuration.GetSection("Logo:AllowedContentTypes").Get<List<string>>() ??
            new List<string> { "image/jpeg", "image/png", "image/gif", "image/webp", "image/svg+xml" };
    }

    public async Task<UploadTransportCompanyLogoResult> Handle(
        UploadTransportCompanyLogoCommand request,
        CancellationToken cancellationToken)
    {
        var startTime = DateTime.UtcNow;

        _logger.LogInformation("Processing logo upload for Transport Company {TransportCompanyId}", request.TransportCompanyId);

        try
        {
            // Validate request
            var validationResult = await ValidateUploadRequest(request, cancellationToken);
            if (!validationResult.IsValid)
            {
                return new UploadTransportCompanyLogoResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Validation failed",
                    ValidationWarnings = validationResult.ValidationErrors,
                    UploadedAt = DateTime.UtcNow
                };
            }

            // Check if transport company exists
            var transportCompany = await _transportCompanyRepository.GetByIdAsync(request.TransportCompanyId);
            if (transportCompany == null)
            {
                return new UploadTransportCompanyLogoResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Transport company not found",
                    UploadedAt = DateTime.UtcNow
                };
            }

            // Delete existing logo if replacing
            if (request.ReplaceExisting)
            {
                await DeleteExistingLogo(request.TransportCompanyId, cancellationToken);
            }

            // Process and optimize image
            var processingStartTime = DateTime.UtcNow;
            var processedImage = await ProcessImage(request, cancellationToken);
            var processingTime = DateTime.UtcNow - processingStartTime;

            // Upload original image
            var uploadStartTime = DateTime.UtcNow;
            var originalImageUrl = await UploadImage(
                processedImage.OptimizedImageData,
                GenerateFileName(request.TransportCompanyId, "original", processedImage.Format),
                processedImage.ContentType,
                "logos",
                cancellationToken);
            var uploadTime = DateTime.UtcNow - uploadStartTime;

            // Generate thumbnail
            var thumbnailStartTime = DateTime.UtcNow;
            using var imageStream = new MemoryStream(processedImage.OptimizedImageData);
            var formFile = CreateFormFileFromBytes(processedImage.OptimizedImageData, "logo.png");
            var thumbnailData = await _imageProcessingService.GenerateThumbnailAsync(formFile, 150);

            var thumbnailUrl = await UploadImage(
                thumbnailData,
                GenerateFileName(request.TransportCompanyId, "thumbnail", "png"),
                "image/png",
                "logos/thumbnails",
                cancellationToken);
            var thumbnailTime = DateTime.UtcNow - thumbnailStartTime;

            // Generate variants if requested
            var variants = new List<LogoVariantDto>();
            if (request.GenerateThumbnails)
            {
                variants = await GenerateStandardVariants(
                    request.TransportCompanyId,
                    processedImage.OptimizedImageData,
                    processedImage.Format,
                    cancellationToken);
            }

            // Create logo entity
            var logo = new TransportCompanyLogoEntity
            {
                Id = Guid.NewGuid(),
                CompanyId = request.TransportCompanyId,
                LogoUrl = originalImageUrl,
                UploadedAt = DateTime.UtcNow
            };

            // Add variants to logo
            foreach (var variant in variants)
            {
                logo.AddVariant(variant.Name, variant.Url, variant.Width, variant.Height, variant.Format, variant.IsOptimized);
            }

            // Save to repository
            await _logoRepository.AddAsync(logo);
            await _logoRepository.SaveChangesAsync();

            var totalTime = DateTime.UtcNow - startTime;

            var result = new UploadTransportCompanyLogoResult
            {
                IsSuccess = true,
                LogoId = logo.Id,
                LogoUrl = originalImageUrl,
                ThumbnailUrl = thumbnailUrl,
                LogoVariants = variants,
                UploadedAt = DateTime.UtcNow,
                ProcessingMetrics = new LogoProcessingMetricsDto
                {
                    UploadTime = uploadTime,
                    ProcessingTime = processingTime,
                    OptimizationTime = processedImage.OptimizationTime,
                    ThumbnailGenerationTime = thumbnailTime,
                    OriginalSize = request.FileSize,
                    OptimizedSize = processedImage.OptimizedImageData.Length,
                    CompressionRatio = (decimal)processedImage.OptimizedImageData.Length / request.FileSize,
                    VariantsGenerated = variants.Count,
                    QualityCheckPassed = validationResult.QualityAnalysis.OverallScore >= 70,
                    ProcessingSteps = new List<string>
                    {
                        "Validation",
                        "Image Processing",
                        "Optimization",
                        "Upload",
                        "Thumbnail Generation",
                        "Variant Generation"
                    },
                    PerformanceMetrics = new Dictionary<string, object>
                    {
                        ["TotalProcessingTime"] = totalTime.TotalMilliseconds,
                        ["CompressionRatio"] = (decimal)processedImage.OptimizedImageData.Length / request.FileSize,
                        ["QualityScore"] = validationResult.QualityAnalysis.OverallScore
                    }
                },
                ValidationWarnings = validationResult.ValidationWarnings
            };

            _logger.LogInformation("Logo uploaded successfully for Transport Company {TransportCompanyId}. LogoId: {LogoId}, Processing time: {ProcessingTime}ms",
                request.TransportCompanyId, logo.Id, totalTime.TotalMilliseconds);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading logo for Transport Company {TransportCompanyId }", request.TransportCompanyId);

            return new UploadTransportCompanyLogoResult
            {
                IsSuccess = false,
                ErrorMessage = ex.Message,
                UploadedAt = DateTime.UtcNow
            };
        }
    }

    private async Task<ValidateLogoUploadResult> ValidateUploadRequest(
        UploadTransportCompanyLogoCommand request,
        CancellationToken cancellationToken)
    {
        var result = new ValidateLogoUploadResult { IsValid = true };

        // File size validation
        if (request.FileSize > _maxFileSizeBytes)
        {
            result.IsValid = false;
            result.ValidationErrors.Add($"File size ({request.FileSize / 1024 / 1024:F1}MB) exceeds maximum allowed size ({_maxFileSizeBytes / 1024 / 1024}MB)");
        }

        // Content type validation
        if (!_allowedContentTypes.Contains(request.ContentType.ToLowerInvariant()))
        {
            result.IsValid = false;
            result.ValidationErrors.Add($"File type '{request.ContentType}' is not allowed. Allowed types: {string.Join(", ", _allowedContentTypes)}");
        }

        // Image data validation
        if (request.ImageData.Length == 0)
        {
            result.IsValid = false;
            result.ValidationErrors.Add("Image data is empty");
        }

        // Analyze image quality if valid so far
        if (result.IsValid)
        {
            try
            {
                result.QualityAnalysis = await AnalyzeImageQuality(request.ImageData, cancellationToken);

                // Add quality-based warnings
                if (result.QualityAnalysis.OverallScore < 50)
                {
                    result.ValidationWarnings.Add("Image quality is below recommended standards");
                }

                if (result.QualityAnalysis.ResolutionScore < 60)
                {
                    result.ValidationWarnings.Add("Image resolution may be too low for optimal display");
                }

                // Add recommendations
                if (!result.QualityAnalysis.IsWebOptimized)
                {
                    result.Recommendations.Add("Consider using a transparent background for better visual appeal");
                }

                if (!result.QualityAnalysis.HasTransparency && request.ContentType != "image/jpeg")
                {
                    result.Recommendations.Add("Consider optimizing image for web use");
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to analyze image quality for Transport Company {TransportCompanyId }", request.TransportCompanyId);
                result.ValidationWarnings.Add("Could not analyze image quality");
            }
        }

        return result;
    }

    private async Task<ProcessedImageResult> ProcessImage(
        UploadTransportCompanyLogoCommand request,
        CancellationToken cancellationToken)
    {
        var optimizationStartTime = DateTime.UtcNow;

        // Analyze original image
        var analysisFormFile = CreateFormFileFromBytes(request.ImageData, "logo.png");
        var imageInfo = await _imageProcessingService.AnalyzeImageAsync(analysisFormFile);

        // Resize if too large
        var processedData = request.ImageData;
        if (imageInfo.Width > _maxWidthPixels || imageInfo.Height > _maxHeightPixels)
        {
            processedData = await _imageProcessingService.ResizeImageAsync(
                request.ImageData,
                _maxWidthPixels,
                _maxHeightPixels,
                "jpeg", // format
                85); // quality
        }

        // Optimize for web if requested
        if (request.OptimizeForWeb)
        {
            var optimizationFormFile = CreateFormFileFromBytes(processedData, request.FileName);
            processedData = await _imageProcessingService.OptimizeForWebAsync(optimizationFormFile, 85);
        }

        var optimizationTime = DateTime.UtcNow - optimizationStartTime;

        return new ProcessedImageResult
        {
            OptimizedImageData = processedData,
            ContentType = request.ContentType,
            Format = GetImageFormat(request.ContentType),
            OptimizationTime = optimizationTime,
            Metadata = new LogoMetadataDto
            {
                OriginalFileName = request.FileName,
                ContentType = request.ContentType,
                OriginalFileSize = request.FileSize,
                OriginalWidth = imageInfo.Width,
                OriginalHeight = imageInfo.Height,
                ColorProfile = imageInfo.ColorProfile ?? "sRGB",
                HasTransparency = imageInfo.HasTransparency,
                DominantColors = string.Join(",", imageInfo.DominantColors ?? new List<string>()),
                ProcessingHistory = new Dictionary<string, object>
                {
                    ["ProcessedAt"] = DateTime.UtcNow,
                    ["OptimizationApplied"] = request.OptimizeForWeb,
                    ["ResizeApplied"] = imageInfo.Width > _maxWidthPixels || imageInfo.Height > _maxHeightPixels,
                    ["OriginalSize"] = request.FileSize,
                    ["ProcessedSize"] = processedData.Length
                }
            }
        };
    }

    private async Task<string> UploadImage(
        byte[] imageData,
        string fileName,
        string contentType,
        string containerName,
        CancellationToken cancellationToken)
    {
        var formFile = CreateFormFileFromBytes(imageData, fileName);
        return await _fileStorageService.UploadFileAsync(formFile, containerName);
    }

    private async Task<List<LogoVariantDto>> GenerateStandardVariants(
        Guid transportCompanyId,
        byte[] originalImageData,
        string format,
        CancellationToken cancellationToken)
    {
        var variants = new List<LogoVariantDto>();
        var standardSizes = new[]
        {
            new { Name = "small", Width = 100, Height = 50 },
            new { Name = "medium", Width = 200, Height = 100 },
            new { Name = "large", Width = 400, Height = 200 },
            new { Name = "favicon", Width = 32, Height = 32 }
        };

        foreach (var size in standardSizes)
        {
            try
            {
                var variantData = await _imageProcessingService.ResizeImageAsync(
                    originalImageData, size.Width, size.Height, format, 85);

                var variantUrl = await UploadImage(
                    variantData,
                    GenerateFileName(transportCompanyId, size.Name, format),
                    GetContentTypeFromFormat(format),
                    $"logos/variants",
                    cancellationToken);

                variants.Add(new LogoVariantDto
                {
                    Id = Guid.NewGuid(),
                    Name = size.Name,
                    Url = variantUrl,
                    Width = size.Width,
                    Height = size.Height,
                    Format = format,
                    FileSize = variantData.Length,
                    CreatedAt = DateTime.UtcNow,
                    IsOptimized = true
                });
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to generate {VariantName } variant for Transport Company {TransportCompanyId}",
                    size.Name, transportCompanyId);
            }
        }

        return variants;
    }

    private async Task DeleteExistingLogo(Guid transportCompanyId, CancellationToken cancellationToken)
    {
        var existingLogo = await _logoRepository.GetByTransportCompanyIdAsync(transportCompanyId);
        if (existingLogo != null)
        {
            // Delete files from storage
            try
            {
                await _fileStorageService.DeleteFileAsync(existingLogo.OriginalUrl, "logos");
                await _fileStorageService.DeleteFileAsync(existingLogo.ThumbnailUrl, "logos");

                foreach (var variant in existingLogo.Variants)
                {
                    await _fileStorageService.DeleteFileAsync(variant.Url, "logos");
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to delete some logo files for Transport Company {TransportCompanyId }", transportCompanyId);
            }

            // Delete from repository
            await _logoRepository.DeleteAsync(existingLogo.Id);
            await _logoRepository.SaveChangesAsync();
        }
    }

    private async Task<LogoQualityAnalysis> AnalyzeImageQuality(byte[] imageData, CancellationToken cancellationToken)
    {
        var formFile = CreateFormFileFromBytes(imageData, "temp.jpg");
        var analysis = await _imageProcessingService.AnalyzeImageQualityAsync(formFile);

        return new LogoQualityAnalysis
        {
            OverallScore = (decimal)analysis.QualityScore,
            ResolutionScore = (decimal)(analysis.Metrics.ContainsKey("ResolutionScore") ? analysis.Metrics["ResolutionScore"] : 0.0),
            CompressionScore = (decimal)(analysis.Metrics.ContainsKey("CompressionScore") ? analysis.Metrics["CompressionScore"] : 0.0),
            ColorScore = (decimal)(analysis.Metrics.ContainsKey("ColorScore") ? analysis.Metrics["ColorScore"] : 0.0),
            ContrastScore = (decimal)(analysis.Metrics.ContainsKey("ContrastScore") ? analysis.Metrics["ContrastScore"] : 0.0),
            IsVectorBased = (bool)(analysis.Metrics.ContainsKey("IsVectorBased") ? analysis.Metrics["IsVectorBased"] : false),
            HasTransparency = (bool)(analysis.Metrics.ContainsKey("HasTransparency") ? analysis.Metrics["HasTransparency"] : false),
            IsWebOptimized = analysis.IsAcceptable,
            QualityIssues = analysis.QualityIssues,
            QualityStrengths = new List<string>() // Default empty list since not available in ImageQualityResult
        };
    }

    private string GenerateFileName(Guid transportCompanyId, string variant, string format)
    {
        var timestamp = DateTime.UtcNow.ToString("yyyyMMdd_HHmmss");
        var extension = GetFileExtension(format);
        return $"logo_{transportCompanyId:N}_{variant}_{timestamp}.{extension}";
    }

    private string GetImageFormat(string contentType)
    {
        return contentType.ToLowerInvariant() switch
        {
            "image/jpeg" => "jpeg",
            "image/png" => "png",
            "image/gif" => "gif",
            "image/webp" => "webp",
            "image/svg+xml" => "svg",
            _ => "png"
        };
    }

    private string GetContentTypeFromFormat(string format)
    {
        return format.ToLowerInvariant() switch
        {
            "jpeg" => "image/jpeg",
            "png" => "image/png",
            "gif" => "image/gif",
            "webp" => "image/webp",
            "svg" => "image/svg+xml",
            _ => "image/png"
        };
    }

    private string GetFileExtension(string format)
    {
        return format.ToLowerInvariant() switch
        {
            "jpeg" => "jpg",
            "png" => "png",
            "gif" => "gif",
            "webp" => "webp",
            "svg" => "svg",
            _ => "png"
        };
    }

    private class ProcessedImageResult
    {
        public byte[] OptimizedImageData { get; set; } = Array.Empty<byte>();
        public string ContentType { get; set; } = string.Empty;
        public string Format { get; set; } = string.Empty;
        public TimeSpan OptimizationTime { get; set; }
        public LogoMetadataDto Metadata { get; set; } = new();
    }

    private IFormFile CreateFormFileFromBytes(byte[] data, string fileName)
    {
        var stream = new MemoryStream(data);
        return new FormFileWrapper(stream, 0, data.Length, "file", fileName);
    }

    private class FormFileWrapper : IFormFile
    {
        private readonly Stream _stream;
        private readonly long _length;
        private readonly string _name;
        private readonly string _fileName;

        public FormFileWrapper(Stream stream, long baseStreamOffset, long length, string name, string fileName)
        {
            _stream = stream;
            _length = length;
            _name = name;
            _fileName = fileName;
        }

        public string ContentType { get; set; } = "image/png";
        public string ContentDisposition { get; set; } = "";
        public IHeaderDictionary Headers { get; set; } = new HeaderDictionary();
        public long Length => _length;
        public string Name => _name;
        public string FileName => _fileName;

        public void CopyTo(Stream target) => _stream.CopyTo(target);
        public Task CopyToAsync(Stream target, CancellationToken cancellationToken = default) => _stream.CopyToAsync(target, cancellationToken);
        public Stream OpenReadStream() => _stream;
    }
}



