namespace NetworkFleetManagement.Infrastructure.MessageBus;

public class AzureServiceBusSettings
{
    public const string SectionName = "AzureServiceBus";
    
    public string ConnectionString { get; set; } = string.Empty;
    public string TopicName { get; set; } = "tli-events";
    public string SubscriptionName { get; set; } = "network-fleet-subscription";
    public int MaxConcurrentCalls { get; set; } = 10;
    public TimeSpan MessageTimeToLive { get; set; } = TimeSpan.FromDays(14);
    public int MaxDeliveryCount { get; set; } = 10;
    public bool EnableDeadLettering { get; set; } = true;
    public TimeSpan LockDuration { get; set; } = TimeSpan.FromMinutes(5);
}
