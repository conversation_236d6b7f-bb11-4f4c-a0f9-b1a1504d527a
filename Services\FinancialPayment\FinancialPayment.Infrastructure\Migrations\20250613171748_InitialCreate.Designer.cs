﻿// <auto-generated />
using System;
using FinancialPayment.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace FinancialPayment.Infrastructure.Migrations
{
    [DbContext(typeof(FinancialPaymentDbContext))]
    [Migration("20250613171748_InitialCreate")]
    partial class InitialCreate
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasDefaultSchema("financial_payment")
                .HasAnnotation("ProductVersion", "8.0.11")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("FinancialPayment.Domain.Entities.Commission", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("BrokerId")
                        .HasColumnType("uuid")
                        .HasColumnName("broker_id");

                    b.Property<Guid>("CarrierId")
                        .HasColumnType("uuid")
                        .HasColumnName("carrier_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("notes");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("uuid")
                        .HasColumnName("order_id");

                    b.Property<DateTime?>("ProcessedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("processed_at");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<Guid>("TransportCompanyId")
                        .HasColumnType("uuid")
                        .HasColumnName("transport_company_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("BrokerId")
                        .HasDatabaseName("ix_commissions_broker_id");

                    b.HasIndex("CarrierId")
                        .HasDatabaseName("ix_commissions_carrier_id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("ix_commissions_created_at");

                    b.HasIndex("OrderId")
                        .IsUnique()
                        .HasDatabaseName("ix_commissions_order_id");

                    b.HasIndex("Status")
                        .HasDatabaseName("ix_commissions_status");

                    b.HasIndex("TransportCompanyId")
                        .HasDatabaseName("ix_commissions_transport_company_id");

                    b.ToTable("commissions", "financial_payment");
                });

            modelBuilder.Entity("FinancialPayment.Domain.Entities.CommissionAdjustment", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("CommissionId")
                        .HasColumnType("uuid")
                        .HasColumnName("commission_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("created_by");

                    b.Property<string>("Reason")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("reason");

                    b.Property<int>("Type")
                        .HasColumnType("integer")
                        .HasColumnName("type");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("CommissionId")
                        .HasDatabaseName("ix_commission_adjustments_commission_id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("ix_commission_adjustments_created_at");

                    b.HasIndex("Type")
                        .HasDatabaseName("ix_commission_adjustments_type");

                    b.ToTable("commission_adjustments", "financial_payment");
                });

            modelBuilder.Entity("FinancialPayment.Domain.Entities.DisputeComment", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("AuthorId")
                        .HasColumnType("uuid")
                        .HasColumnName("author_id");

                    b.Property<int>("AuthorRole")
                        .HasColumnType("integer")
                        .HasColumnName("author_role");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("content");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid>("DisputeId")
                        .HasColumnType("uuid")
                        .HasColumnName("dispute_id");

                    b.Property<bool>("IsInternal")
                        .HasColumnType("boolean")
                        .HasColumnName("is_internal");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("AuthorId")
                        .HasDatabaseName("ix_dispute_comments_author_id");

                    b.HasIndex("AuthorRole")
                        .HasDatabaseName("ix_dispute_comments_author_role");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("ix_dispute_comments_created_at");

                    b.HasIndex("DisputeId")
                        .HasDatabaseName("ix_dispute_comments_dispute_id");

                    b.HasIndex("IsInternal")
                        .HasDatabaseName("ix_dispute_comments_is_internal");

                    b.ToTable("dispute_comments", "financial_payment");
                });

            modelBuilder.Entity("FinancialPayment.Domain.Entities.DisputeDocument", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("description");

                    b.Property<Guid>("DisputeId")
                        .HasColumnType("uuid")
                        .HasColumnName("dispute_id");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("file_name");

                    b.Property<string>("FileUrl")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("file_url");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("UploadedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("uploaded_at");

                    b.Property<Guid>("UploadedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("uploaded_by");

                    b.HasKey("Id");

                    b.HasIndex("DisputeId")
                        .HasDatabaseName("ix_dispute_documents_dispute_id");

                    b.HasIndex("UploadedAt")
                        .HasDatabaseName("ix_dispute_documents_uploaded_at");

                    b.HasIndex("UploadedBy")
                        .HasDatabaseName("ix_dispute_documents_uploaded_by");

                    b.ToTable("dispute_documents", "financial_payment");
                });

            modelBuilder.Entity("FinancialPayment.Domain.Entities.EscrowAccount", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("BrokerId")
                        .HasColumnType("uuid")
                        .HasColumnName("broker_id");

                    b.Property<Guid>("CarrierId")
                        .HasColumnType("uuid")
                        .HasColumnName("carrier_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("EscrowAccountNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("escrow_account_number");

                    b.Property<DateTime?>("FundedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("funded_at");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("notes");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("uuid")
                        .HasColumnName("order_id");

                    b.Property<string>("ReleaseReason")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("release_reason");

                    b.Property<DateTime?>("ReleasedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("released_at");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<Guid>("TransportCompanyId")
                        .HasColumnType("uuid")
                        .HasColumnName("transport_company_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("BrokerId")
                        .HasDatabaseName("ix_escrow_accounts_broker_id");

                    b.HasIndex("CarrierId")
                        .HasDatabaseName("ix_escrow_accounts_carrier_id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("ix_escrow_accounts_created_at");

                    b.HasIndex("EscrowAccountNumber")
                        .IsUnique()
                        .HasDatabaseName("ix_escrow_accounts_account_number");

                    b.HasIndex("OrderId")
                        .IsUnique()
                        .HasDatabaseName("ix_escrow_accounts_order_id");

                    b.HasIndex("Status")
                        .HasDatabaseName("ix_escrow_accounts_status");

                    b.HasIndex("TransportCompanyId")
                        .HasDatabaseName("ix_escrow_accounts_transport_company_id");

                    b.ToTable("escrow_accounts", "financial_payment");
                });

            modelBuilder.Entity("FinancialPayment.Domain.Entities.EscrowMilestone", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("completed_at");

                    b.Property<string>("CompletionNotes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("completion_notes");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("description");

                    b.Property<DateTime?>("DueDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("due_date");

                    b.Property<Guid>("EscrowAccountId")
                        .HasColumnType("uuid")
                        .HasColumnName("escrow_account_id");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("ix_escrow_milestones_created_at");

                    b.HasIndex("DueDate")
                        .HasDatabaseName("ix_escrow_milestones_due_date");

                    b.HasIndex("EscrowAccountId")
                        .HasDatabaseName("ix_escrow_milestones_escrow_account_id");

                    b.HasIndex("Status")
                        .HasDatabaseName("ix_escrow_milestones_status");

                    b.ToTable("escrow_milestones", "financial_payment");
                });

            modelBuilder.Entity("FinancialPayment.Domain.Entities.EscrowTransaction", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid>("EscrowAccountId")
                        .HasColumnType("uuid")
                        .HasColumnName("escrow_account_id");

                    b.Property<string>("FailureReason")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("failure_reason");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("notes");

                    b.Property<Guid?>("ParticipantId")
                        .HasColumnType("uuid")
                        .HasColumnName("participant_id");

                    b.Property<string>("PaymentGatewayTransactionId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("payment_gateway_transaction_id");

                    b.Property<DateTime?>("ProcessedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("processed_at");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<int>("Type")
                        .HasColumnType("integer")
                        .HasColumnName("type");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("ix_escrow_transactions_created_at");

                    b.HasIndex("EscrowAccountId")
                        .HasDatabaseName("ix_escrow_transactions_escrow_account_id");

                    b.HasIndex("ParticipantId")
                        .HasDatabaseName("ix_escrow_transactions_participant_id");

                    b.HasIndex("PaymentGatewayTransactionId")
                        .HasDatabaseName("ix_escrow_transactions_gateway_transaction_id");

                    b.HasIndex("Status")
                        .HasDatabaseName("ix_escrow_transactions_status");

                    b.HasIndex("Type")
                        .HasDatabaseName("ix_escrow_transactions_type");

                    b.ToTable("escrow_transactions", "financial_payment");
                });

            modelBuilder.Entity("FinancialPayment.Domain.Entities.PaymentDispute", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<int>("Category")
                        .HasColumnType("integer")
                        .HasColumnName("category");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("description");

                    b.Property<string>("DisputeNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("dispute_number");

                    b.Property<Guid?>("EscrowAccountId")
                        .HasColumnType("uuid")
                        .HasColumnName("escrow_account_id");

                    b.Property<Guid>("InitiatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("initiated_by");

                    b.Property<int>("InitiatorRole")
                        .HasColumnType("integer")
                        .HasColumnName("initiator_role");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("uuid")
                        .HasColumnName("order_id");

                    b.Property<int>("Priority")
                        .HasColumnType("integer")
                        .HasColumnName("priority");

                    b.Property<string>("Resolution")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("resolution");

                    b.Property<DateTime?>("ResolvedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("resolved_at");

                    b.Property<Guid?>("ResolvedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("resolved_by");

                    b.Property<Guid?>("SettlementId")
                        .HasColumnType("uuid")
                        .HasColumnName("settlement_id");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("title");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("Category")
                        .HasDatabaseName("ix_payment_disputes_category");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("ix_payment_disputes_created_at");

                    b.HasIndex("DisputeNumber")
                        .IsUnique()
                        .HasDatabaseName("ix_payment_disputes_dispute_number");

                    b.HasIndex("EscrowAccountId")
                        .HasDatabaseName("ix_payment_disputes_escrow_account_id");

                    b.HasIndex("InitiatedBy")
                        .HasDatabaseName("ix_payment_disputes_initiated_by");

                    b.HasIndex("InitiatorRole")
                        .HasDatabaseName("ix_payment_disputes_initiator_role");

                    b.HasIndex("OrderId")
                        .HasDatabaseName("ix_payment_disputes_order_id");

                    b.HasIndex("Priority")
                        .HasDatabaseName("ix_payment_disputes_priority");

                    b.HasIndex("SettlementId")
                        .HasDatabaseName("ix_payment_disputes_settlement_id");

                    b.HasIndex("Status")
                        .HasDatabaseName("ix_payment_disputes_status");

                    b.ToTable("payment_disputes", "financial_payment");
                });

            modelBuilder.Entity("FinancialPayment.Domain.Entities.Settlement", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid>("EscrowAccountId")
                        .HasColumnType("uuid")
                        .HasColumnName("escrow_account_id");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("notes");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("uuid")
                        .HasColumnName("order_id");

                    b.Property<DateTime?>("ProcessedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("processed_at");

                    b.Property<string>("SettlementNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("settlement_number");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<Guid>("TripId")
                        .HasColumnType("uuid")
                        .HasColumnName("trip_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("ix_settlements_created_at");

                    b.HasIndex("EscrowAccountId")
                        .HasDatabaseName("ix_settlements_escrow_account_id");

                    b.HasIndex("OrderId")
                        .IsUnique()
                        .HasDatabaseName("ix_settlements_order_id");

                    b.HasIndex("SettlementNumber")
                        .IsUnique()
                        .HasDatabaseName("ix_settlements_settlement_number");

                    b.HasIndex("Status")
                        .HasDatabaseName("ix_settlements_status");

                    b.HasIndex("TripId")
                        .IsUnique()
                        .HasDatabaseName("ix_settlements_trip_id");

                    b.ToTable("settlements", "financial_payment");
                });

            modelBuilder.Entity("FinancialPayment.Domain.Entities.SettlementDistribution", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("description");

                    b.Property<string>("FailureReason")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("failure_reason");

                    b.Property<string>("PaymentGatewayTransactionId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("payment_gateway_transaction_id");

                    b.Property<DateTime?>("ProcessedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("processed_at");

                    b.Property<Guid>("RecipientId")
                        .HasColumnType("uuid")
                        .HasColumnName("recipient_id");

                    b.Property<int>("RecipientRole")
                        .HasColumnType("integer")
                        .HasColumnName("recipient_role");

                    b.Property<Guid>("SettlementId")
                        .HasColumnType("uuid")
                        .HasColumnName("settlement_id");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<int>("Type")
                        .HasColumnType("integer")
                        .HasColumnName("type");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("ix_settlement_distributions_created_at");

                    b.HasIndex("PaymentGatewayTransactionId")
                        .HasDatabaseName("ix_settlement_distributions_gateway_transaction_id");

                    b.HasIndex("RecipientId")
                        .HasDatabaseName("ix_settlement_distributions_recipient_id");

                    b.HasIndex("RecipientRole")
                        .HasDatabaseName("ix_settlement_distributions_recipient_role");

                    b.HasIndex("SettlementId")
                        .HasDatabaseName("ix_settlement_distributions_settlement_id");

                    b.HasIndex("Status")
                        .HasDatabaseName("ix_settlement_distributions_status");

                    b.HasIndex("Type")
                        .HasDatabaseName("ix_settlement_distributions_type");

                    b.ToTable("settlement_distributions", "financial_payment");
                });

            modelBuilder.Entity("FinancialPayment.Domain.Entities.Commission", b =>
                {
                    b.OwnsOne("FinancialPayment.Domain.ValueObjects.Money", "CalculatedAmount", b1 =>
                        {
                            b1.Property<Guid>("CommissionId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,2)")
                                .HasColumnName("calculated_amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("calculated_currency");

                            b1.HasKey("CommissionId");

                            b1.ToTable("commissions", "financial_payment");

                            b1.WithOwner()
                                .HasForeignKey("CommissionId");
                        });

                    b.OwnsOne("FinancialPayment.Domain.ValueObjects.Money", "OrderAmount", b1 =>
                        {
                            b1.Property<Guid>("CommissionId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,2)")
                                .HasColumnName("order_amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("order_currency");

                            b1.HasKey("CommissionId");

                            b1.ToTable("commissions", "financial_payment");

                            b1.WithOwner()
                                .HasForeignKey("CommissionId");
                        });

                    b.OwnsOne("FinancialPayment.Domain.ValueObjects.CommissionStructure", "CommissionStructure", b1 =>
                        {
                            b1.Property<Guid>("CommissionId")
                                .HasColumnType("uuid");

                            b1.Property<string>("Description")
                                .HasMaxLength(500)
                                .HasColumnType("character varying(500)")
                                .HasColumnName("commission_description");

                            b1.Property<decimal?>("MaximumAmount")
                                .HasColumnType("decimal(18,2)")
                                .HasColumnName("commission_maximum_amount");

                            b1.Property<decimal?>("MinimumAmount")
                                .HasColumnType("decimal(18,2)")
                                .HasColumnName("commission_minimum_amount");

                            b1.Property<decimal>("Rate")
                                .HasColumnType("decimal(18,4)")
                                .HasColumnName("commission_rate");

                            b1.Property<int>("Type")
                                .HasColumnType("integer")
                                .HasColumnName("commission_type");

                            b1.HasKey("CommissionId");

                            b1.ToTable("commissions", "financial_payment");

                            b1.WithOwner()
                                .HasForeignKey("CommissionId");
                        });

                    b.Navigation("CalculatedAmount")
                        .IsRequired();

                    b.Navigation("CommissionStructure")
                        .IsRequired();

                    b.Navigation("OrderAmount")
                        .IsRequired();
                });

            modelBuilder.Entity("FinancialPayment.Domain.Entities.CommissionAdjustment", b =>
                {
                    b.HasOne("FinancialPayment.Domain.Entities.Commission", "Commission")
                        .WithMany("Adjustments")
                        .HasForeignKey("CommissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("FinancialPayment.Domain.ValueObjects.Money", "Amount", b1 =>
                        {
                            b1.Property<Guid>("CommissionAdjustmentId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,2)")
                                .HasColumnName("amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("currency");

                            b1.HasKey("CommissionAdjustmentId");

                            b1.ToTable("commission_adjustments", "financial_payment");

                            b1.WithOwner()
                                .HasForeignKey("CommissionAdjustmentId");
                        });

                    b.Navigation("Amount")
                        .IsRequired();

                    b.Navigation("Commission");
                });

            modelBuilder.Entity("FinancialPayment.Domain.Entities.DisputeComment", b =>
                {
                    b.HasOne("FinancialPayment.Domain.Entities.PaymentDispute", "Dispute")
                        .WithMany("Comments")
                        .HasForeignKey("DisputeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Dispute");
                });

            modelBuilder.Entity("FinancialPayment.Domain.Entities.DisputeDocument", b =>
                {
                    b.HasOne("FinancialPayment.Domain.Entities.PaymentDispute", "Dispute")
                        .WithMany("Documents")
                        .HasForeignKey("DisputeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Dispute");
                });

            modelBuilder.Entity("FinancialPayment.Domain.Entities.EscrowAccount", b =>
                {
                    b.OwnsOne("FinancialPayment.Domain.ValueObjects.Money", "AvailableAmount", b1 =>
                        {
                            b1.Property<Guid>("EscrowAccountId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,2)")
                                .HasColumnName("available_amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("available_currency");

                            b1.HasKey("EscrowAccountId");

                            b1.ToTable("escrow_accounts", "financial_payment");

                            b1.WithOwner()
                                .HasForeignKey("EscrowAccountId");
                        });

                    b.OwnsOne("FinancialPayment.Domain.ValueObjects.Money", "ReservedAmount", b1 =>
                        {
                            b1.Property<Guid>("EscrowAccountId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,2)")
                                .HasColumnName("reserved_amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("reserved_currency");

                            b1.HasKey("EscrowAccountId");

                            b1.ToTable("escrow_accounts", "financial_payment");

                            b1.WithOwner()
                                .HasForeignKey("EscrowAccountId");
                        });

                    b.OwnsOne("FinancialPayment.Domain.ValueObjects.Money", "TotalAmount", b1 =>
                        {
                            b1.Property<Guid>("EscrowAccountId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,2)")
                                .HasColumnName("total_amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("total_currency");

                            b1.HasKey("EscrowAccountId");

                            b1.ToTable("escrow_accounts", "financial_payment");

                            b1.WithOwner()
                                .HasForeignKey("EscrowAccountId");
                        });

                    b.Navigation("AvailableAmount")
                        .IsRequired();

                    b.Navigation("ReservedAmount")
                        .IsRequired();

                    b.Navigation("TotalAmount")
                        .IsRequired();
                });

            modelBuilder.Entity("FinancialPayment.Domain.Entities.EscrowMilestone", b =>
                {
                    b.HasOne("FinancialPayment.Domain.Entities.EscrowAccount", "EscrowAccount")
                        .WithMany("Milestones")
                        .HasForeignKey("EscrowAccountId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("FinancialPayment.Domain.ValueObjects.Money", "Amount", b1 =>
                        {
                            b1.Property<Guid>("EscrowMilestoneId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,2)")
                                .HasColumnName("amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("currency");

                            b1.HasKey("EscrowMilestoneId");

                            b1.ToTable("escrow_milestones", "financial_payment");

                            b1.WithOwner()
                                .HasForeignKey("EscrowMilestoneId");
                        });

                    b.Navigation("Amount")
                        .IsRequired();

                    b.Navigation("EscrowAccount");
                });

            modelBuilder.Entity("FinancialPayment.Domain.Entities.EscrowTransaction", b =>
                {
                    b.HasOne("FinancialPayment.Domain.Entities.EscrowAccount", "EscrowAccount")
                        .WithMany("Transactions")
                        .HasForeignKey("EscrowAccountId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("FinancialPayment.Domain.ValueObjects.Money", "Amount", b1 =>
                        {
                            b1.Property<Guid>("EscrowTransactionId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,2)")
                                .HasColumnName("amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("currency");

                            b1.HasKey("EscrowTransactionId");

                            b1.ToTable("escrow_transactions", "financial_payment");

                            b1.WithOwner()
                                .HasForeignKey("EscrowTransactionId");
                        });

                    b.Navigation("Amount")
                        .IsRequired();

                    b.Navigation("EscrowAccount");
                });

            modelBuilder.Entity("FinancialPayment.Domain.Entities.PaymentDispute", b =>
                {
                    b.HasOne("FinancialPayment.Domain.Entities.EscrowAccount", "EscrowAccount")
                        .WithMany()
                        .HasForeignKey("EscrowAccountId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("FinancialPayment.Domain.Entities.Settlement", "Settlement")
                        .WithMany()
                        .HasForeignKey("SettlementId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.OwnsOne("FinancialPayment.Domain.ValueObjects.Money", "DisputedAmount", b1 =>
                        {
                            b1.Property<Guid>("PaymentDisputeId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,2)")
                                .HasColumnName("disputed_amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("disputed_currency");

                            b1.HasKey("PaymentDisputeId");

                            b1.ToTable("payment_disputes", "financial_payment");

                            b1.WithOwner()
                                .HasForeignKey("PaymentDisputeId");
                        });

                    b.OwnsOne("FinancialPayment.Domain.ValueObjects.Money", "ResolvedAmount", b1 =>
                        {
                            b1.Property<Guid>("PaymentDisputeId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,2)")
                                .HasColumnName("resolved_amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("resolved_currency");

                            b1.HasKey("PaymentDisputeId");

                            b1.ToTable("payment_disputes", "financial_payment");

                            b1.WithOwner()
                                .HasForeignKey("PaymentDisputeId");
                        });

                    b.Navigation("DisputedAmount")
                        .IsRequired();

                    b.Navigation("EscrowAccount");

                    b.Navigation("ResolvedAmount");

                    b.Navigation("Settlement");
                });

            modelBuilder.Entity("FinancialPayment.Domain.Entities.Settlement", b =>
                {
                    b.HasOne("FinancialPayment.Domain.Entities.EscrowAccount", "EscrowAccount")
                        .WithMany()
                        .HasForeignKey("EscrowAccountId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.OwnsOne("FinancialPayment.Domain.ValueObjects.Money", "TotalAmount", b1 =>
                        {
                            b1.Property<Guid>("SettlementId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,2)")
                                .HasColumnName("total_amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("total_currency");

                            b1.HasKey("SettlementId");

                            b1.ToTable("settlements", "financial_payment");

                            b1.WithOwner()
                                .HasForeignKey("SettlementId");
                        });

                    b.Navigation("EscrowAccount");

                    b.Navigation("TotalAmount")
                        .IsRequired();
                });

            modelBuilder.Entity("FinancialPayment.Domain.Entities.SettlementDistribution", b =>
                {
                    b.HasOne("FinancialPayment.Domain.Entities.Settlement", "Settlement")
                        .WithMany("Distributions")
                        .HasForeignKey("SettlementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("FinancialPayment.Domain.ValueObjects.Money", "Amount", b1 =>
                        {
                            b1.Property<Guid>("SettlementDistributionId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,2)")
                                .HasColumnName("amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("currency");

                            b1.HasKey("SettlementDistributionId");

                            b1.ToTable("settlement_distributions", "financial_payment");

                            b1.WithOwner()
                                .HasForeignKey("SettlementDistributionId");
                        });

                    b.Navigation("Amount")
                        .IsRequired();

                    b.Navigation("Settlement");
                });

            modelBuilder.Entity("FinancialPayment.Domain.Entities.Commission", b =>
                {
                    b.Navigation("Adjustments");
                });

            modelBuilder.Entity("FinancialPayment.Domain.Entities.EscrowAccount", b =>
                {
                    b.Navigation("Milestones");

                    b.Navigation("Transactions");
                });

            modelBuilder.Entity("FinancialPayment.Domain.Entities.PaymentDispute", b =>
                {
                    b.Navigation("Comments");

                    b.Navigation("Documents");
                });

            modelBuilder.Entity("FinancialPayment.Domain.Entities.Settlement", b =>
                {
                    b.Navigation("Distributions");
                });
#pragma warning restore 612, 618
        }
    }
}
