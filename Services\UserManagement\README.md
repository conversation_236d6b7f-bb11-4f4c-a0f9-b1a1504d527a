# User Management Service

The User Management Service is responsible for managing user profiles, KYC document verification, and admin panel functionality for the TLI Logistics platform.

## Features

### User Profile Management
- Create and manage user profiles for different user types
- Support for multiple user types: Admin, Transport Company, Broker, Carrier, Driver, Shipper
- Complete profile management with personal and company details
- Address management and contact information

### KYC Document Management
- Document upload and verification workflow
- Support for multiple document types based on user type
- Document status tracking (Pending, Uploaded, Under Review, Approved, Rejected)
- Bulk document processing capabilities
- AI/OCR integration ready for automated verification

### Admin Panel Features
- Review and approve user profiles
- Document verification and approval workflow
- Bulk processing tools for administrators
- Dashboard with statistics and pending approvals
- User account management and role assignments

## User Types and Required Documents

### Transport Company
- **Required Documents**: GST Certificate, Trade License, PAN Card, Profile Photo
- **Profile Requirements**: Company details, GST number, PAN number

### Broker
- **Required Documents**: GST Certificate, PAN Card, Profile Photo
- **Profile Requirements**: Company details, GST number, PAN number

### Driver
- **Required Documents**: Driving License, Aadhar Card, Profile Photo
- **Profile Requirements**: License number, Aadhar number

### Carrier
- **Required Documents**: Aadhar Card, Vehicle Registration, Vehicle Insurance, Profile Photo
- **Profile Requirements**: Aadhar number, vehicle details

### Shipper
- **Required Documents**: Aadhar Card, Profile Photo
- **Profile Requirements**: Basic personal information

### Admin
- **Required Documents**: Profile Photo
- **Profile Requirements**: Basic personal information

## API Endpoints

### User Profiles
- `POST /api/userprofiles` - Create user profile
- `GET /api/userprofiles/{id}` - Get profile by ID
- `GET /api/userprofiles/user/{userId}` - Get profile by user ID
- `PUT /api/userprofiles/{id}/personal-details` - Update personal details
- `PUT /api/userprofiles/{id}/company-details` - Update company details
- `PUT /api/userprofiles/{id}/address` - Update address
- `POST /api/userprofiles/{id}/submit-for-review` - Submit profile for review

### Document Management
- `POST /api/documents/upload` - Upload document
- `GET /api/documents/user/{userId}` - Get user documents
- `GET /api/documents/submission/{submissionId}` - Get document submission
- `POST /api/documents/submission/{submissionId}/submit` - Submit documents for review
- `GET /api/documents/download/{documentId}` - Download document
- `DELETE /api/documents/{documentId}` - Delete document

### Admin Panel
- `GET /api/admin/pending-approvals` - Get pending approvals
- `GET /api/admin/dashboard-stats` - Get dashboard statistics
- `POST /api/admin/users/{userId}/approve` - Approve user
- `POST /api/admin/users/{userId}/reject` - Reject user
- `POST /api/admin/documents/{submissionId}/approve` - Approve documents
- `POST /api/admin/documents/{submissionId}/reject` - Reject documents

## Architecture

The service follows Clean Architecture principles with the following layers:

### Domain Layer (`UserManagement.Domain`)
- **Entities**: UserProfile, DocumentSubmission, Document
- **Value Objects**: User types, document types, status enums
- **Repository Interfaces**: IUserProfileRepository, IDocumentSubmissionRepository
- **Domain Events**: Profile created, documents submitted, etc.

### Application Layer (`UserManagement.Application`)
- **Commands**: Create profile, upload document, approve/reject operations
- **Queries**: Get profiles, get documents, get pending approvals
- **Handlers**: CQRS pattern implementation with MediatR
- **DTOs**: Data transfer objects for API responses

### Infrastructure Layer (`UserManagement.Infrastructure`)
- **Data Access**: Entity Framework Core with PostgreSQL
- **Repositories**: Concrete implementations of domain repositories
- **External Services**: File storage, messaging, etc.

### API Layer (`UserManagement.API`)
- **Controllers**: REST API endpoints
- **Authentication**: JWT token validation
- **Authorization**: Role-based access control
- **Swagger**: API documentation

## Database Schema

### UserProfiles Table
- User profile information for all user types
- Personal details, company details, address information
- Status tracking and approval workflow

### DocumentSubmissions Table
- Overall document submission status per user
- Tracks submission progress and review status

### Documents Table (Owned by DocumentSubmissions)
- Individual document details and status
- File information and verification results
- Review notes and rejection reasons

## Configuration

### Database Connection
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=TLI_UserManagement;Username=********;Password=********"
  }
}
```

### JWT Settings
```json
{
  "JwtSettings": {
    "Secret": "your-secret-key",
    "Issuer": "TLI.Identity",
    "Audience": "TLI.Services",
    "ExpiryInMinutes": 60
  }
}
```

## Running the Service

### Prerequisites
- .NET 8 SDK
- PostgreSQL database
- Valid JWT configuration

### Development
```bash
cd Services/UserManagement/UserManagement.API
dotnet run
```

### Docker
```bash
docker-compose up usermanagement-api
```

The service will be available at:
- **HTTP**: http://localhost:5002
- **Swagger**: http://localhost:5002/swagger

## Integration with Identity Service

The User Management Service works closely with the Identity Service:

1. **User Registration**: When a user registers through Identity Service, a corresponding user profile is created in User Management Service
2. **Authentication**: User Management Service validates JWT tokens issued by Identity Service
3. **Authorization**: Role-based permissions are enforced based on user types
4. **Profile Completion**: KYC completion status affects user activation in Identity Service

## Future Enhancements

- AI/OCR integration for automated document verification
- Real-time notifications for status updates
- Advanced search and filtering capabilities
- Audit trail for all admin actions
- Integration with external verification services
- Mobile app support for document uploads
