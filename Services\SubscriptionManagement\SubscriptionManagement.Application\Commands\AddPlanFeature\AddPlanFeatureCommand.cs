using MediatR;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Application.Commands.AddPlanFeature;

public class AddPlanFeatureCommand : IRequest<bool>
{
    public Guid PlanId { get; set; }
    public FeatureType FeatureType { get; set; }
    public FeatureAccessType AccessType { get; set; }
    public int? LimitValue { get; set; }
    public string? Description { get; set; }
    
    public AddPlanFeatureCommand(Guid planId, FeatureType featureType, FeatureAccessType accessType, int? limitValue = null, string? description = null)
    {
        PlanId = planId;
        FeatureType = featureType;
        AccessType = accessType;
        LimitValue = limitValue;
        Description = description;
    }
}
