using Microsoft.EntityFrameworkCore;
using SubscriptionManagement.Domain.Interfaces;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Infrastructure.Persistence;

namespace SubscriptionManagement.Infrastructure.Repositories
{
    public class UsageRecordRepository : IUsageRecordRepository
    {
        private readonly SubscriptionDbContext _context;

        public UsageRecordRepository(SubscriptionDbContext context)
        {
            _context = context;
        }

        public async Task<UsageRecord?> GetByIdAsync(Guid id)
        {
            return await _context.UsageRecords
                .Include(u => u.Subscription)
                .FirstOrDefaultAsync(u => u.Id == id);
        }

        public async Task<List<UsageRecord>> GetByUserIdAsync(Guid userId)
        {
            return await _context.UsageRecords
                .Include(u => u.Subscription)
                .Where(u => u.UserId == userId)
                .OrderByDescending(u => u.UsageDate)
                .ToListAsync();
        }

        public async Task<List<UsageRecord>> GetBySubscriptionIdAsync(Guid subscriptionId)
        {
            return await _context.UsageRecords
                .Include(u => u.Subscription)
                .Where(u => u.SubscriptionId == subscriptionId)
                .OrderByDescending(u => u.UsageDate)
                .ToListAsync();
        }

        public async Task<List<UsageRecord>> GetByFeatureTypeAsync(FeatureType featureType)
        {
            return await _context.UsageRecords
                .Include(u => u.Subscription)
                .Where(u => u.FeatureType == featureType)
                .OrderByDescending(u => u.UsageDate)
                .ToListAsync();
        }

        public async Task<List<UsageRecord>> GetUsageHistoryAsync(Guid userId, FeatureType? featureType = null, DateTime? from = null, DateTime? to = null)
        {
            var query = _context.UsageRecords
                .Include(u => u.Subscription)
                .Where(u => u.UserId == userId);

            if (featureType.HasValue)
            {
                query = query.Where(u => u.FeatureType == featureType.Value);
            }

            if (from.HasValue)
            {
                query = query.Where(u => u.UsageDate >= from.Value);
            }

            if (to.HasValue)
            {
                query = query.Where(u => u.UsageDate <= to.Value);
            }

            return await query
                .OrderByDescending(u => u.UsageDate)
                .ToListAsync();
        }

        public async Task<Dictionary<FeatureType, int>> GetUsageSummaryAsync(Guid userId, DateTime from, DateTime to)
        {
            var usageRecords = await _context.UsageRecords
                .Where(u => u.UserId == userId && 
                           u.UsageDate >= from && 
                           u.UsageDate <= to)
                .GroupBy(u => u.FeatureType)
                .Select(g => new { FeatureType = g.Key, TotalUsage = g.Sum(u => u.UsageCount) })
                .ToListAsync();

            return usageRecords.ToDictionary(u => u.FeatureType, u => u.TotalUsage);
        }

        public async Task<List<UsageRecord>> GetTopUsersAsync(FeatureType featureType, int count, DateTime from, DateTime to)
        {
            return await _context.UsageRecords
                .Include(u => u.Subscription)
                .Where(u => u.FeatureType == featureType && 
                           u.UsageDate >= from && 
                           u.UsageDate <= to)
                .GroupBy(u => u.UserId)
                .Select(g => new { UserId = g.Key, TotalUsage = g.Sum(u => u.UsageCount), LastRecord = g.OrderByDescending(u => u.UsageDate).First() })
                .OrderByDescending(g => g.TotalUsage)
                .Take(count)
                .Select(g => g.LastRecord)
                .ToListAsync();
        }

        public async Task<Dictionary<FeatureType, double>> GetAverageUsageAsync(DateTime from, DateTime to)
        {
            var usageData = await _context.UsageRecords
                .Where(u => u.UsageDate >= from && u.UsageDate <= to)
                .GroupBy(u => new { u.FeatureType, u.UserId })
                .Select(g => new { g.Key.FeatureType, TotalUsage = g.Sum(u => u.UsageCount) })
                .GroupBy(x => x.FeatureType)
                .Select(g => new { FeatureType = g.Key, AverageUsage = g.Average(x => x.TotalUsage) })
                .ToListAsync();

            return usageData.ToDictionary(u => u.FeatureType, u => u.AverageUsage);
        }

        public async Task<UsageRecord> AddAsync(UsageRecord usageRecord)
        {
            _context.UsageRecords.Add(usageRecord);
            await _context.SaveChangesAsync();
            return usageRecord;
        }

        public async Task UpdateAsync(UsageRecord usageRecord)
        {
            _context.UsageRecords.Update(usageRecord);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteAsync(Guid id)
        {
            var usageRecord = await _context.UsageRecords.FindAsync(id);
            if (usageRecord != null)
            {
                _context.UsageRecords.Remove(usageRecord);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<int> GetTotalUsageCountAsync(Guid userId, FeatureType featureType, DateTime from, DateTime to)
        {
            return await _context.UsageRecords
                .Where(u => u.UserId == userId && 
                           u.FeatureType == featureType && 
                           u.UsageDate >= from && 
                           u.UsageDate <= to)
                .SumAsync(u => u.UsageCount);
        }

        public async Task<List<UsageRecord>> GetUsageByPeriodAsync(Guid userId, DateTime periodStart, DateTime periodEnd)
        {
            return await _context.UsageRecords
                .Include(u => u.Subscription)
                .Where(u => u.UserId == userId && 
                           u.PeriodStart == periodStart && 
                           u.PeriodEnd == periodEnd)
                .OrderByDescending(u => u.UsageDate)
                .ToListAsync();
        }
    }
}
