using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ApiExplorer;
using Microsoft.OpenApi.Models;
using System.Reflection;

namespace AuditCompliance.API.Configuration;

/// <summary>
/// Configuration for API versioning
/// </summary>
public static class ApiVersioningConfiguration
{
    /// <summary>
    /// Configure API versioning services
    /// </summary>
    public static IServiceCollection AddApiVersioningConfiguration(this IServiceCollection services)
    {
        // Add API versioning
        services.AddApiVersioning(options =>
        {
            // Default version
            options.DefaultApiVersion = new ApiVersion(1, 0);
            options.AssumeDefaultVersionWhenUnspecified = true;

            // Version reading strategies
            options.ApiVersionReader = ApiVersionReader.Combine(
                new UrlSegmentApiVersionReader(),
                new QueryStringApiVersionReader("version"),
                new HeaderApiVersionReader("X-Version"),
                new MediaTypeApiVersionReader("ver")
            );

            // Version selection
            options.ApiVersionSelector = new CurrentImplementationApiVersionSelector(options);
        });

        // Add API explorer for versioned APIs
        services.AddVersionedApiExplorer(setup =>
        {
            setup.GroupNameFormat = "'v'VVV";
            setup.SubstituteApiVersionInUrl = true;
            setup.AssumeDefaultVersionWhenUnspecified = true;
        });

        return services;
    }

    /// <summary>
    /// Configure Swagger for API versioning
    /// </summary>
    public static IServiceCollection AddVersionedSwagger(this IServiceCollection services)
    {
        services.AddSwaggerGen(options =>
        {
            var provider = services.BuildServiceProvider().GetRequiredService<IApiVersionDescriptionProvider>();

            foreach (var description in provider.ApiVersionDescriptions)
            {
                options.SwaggerDoc(description.GroupName, CreateVersionInfo(description));
            }

            // Include XML comments
            var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
            var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
            if (File.Exists(xmlPath))
            {
                options.IncludeXmlComments(xmlPath);
            }

            // Add security definition
            options.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
            {
                Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
                Name = "Authorization",
                In = ParameterLocation.Header,
                Type = SecuritySchemeType.ApiKey,
                Scheme = "Bearer"
            });

            options.AddSecurityRequirement(new OpenApiSecurityRequirement
            {
                {
                    new OpenApiSecurityScheme
                    {
                        Reference = new OpenApiReference
                        {
                            Type = ReferenceType.SecurityScheme,
                            Id = "Bearer"
                        }
                    },
                    Array.Empty<string>()
                }
            });

            // Custom operation filters for versioning
            options.OperationFilter<ApiVersionOperationFilter>();
            options.DocumentFilter<ApiVersionDocumentFilter>();
        });

        return services;
    }

    /// <summary>
    /// Configure Swagger UI for API versioning
    /// </summary>
    public static IApplicationBuilder UseVersionedSwaggerUI(this IApplicationBuilder app)
    {
        var provider = app.ApplicationServices.GetRequiredService<IApiVersionDescriptionProvider>();

        app.UseSwaggerUI(options =>
        {
            foreach (var description in provider.ApiVersionDescriptions.Reverse())
            {
                options.SwaggerEndpoint(
                    $"/swagger/{description.GroupName}/swagger.json",
                    $"Audit & Compliance API {description.GroupName.ToUpperInvariant()}");
            }

            options.RoutePrefix = "swagger";
            options.DocumentTitle = "Audit & Compliance API Documentation";
            options.DefaultModelsExpandDepth(-1);
            options.DocExpansion(Swashbuckle.AspNetCore.SwaggerUI.DocExpansion.None);
            options.EnableDeepLinking();
            options.EnableFilter();
            options.ShowExtensions();
        });

        return app;
    }

    private static OpenApiInfo CreateVersionInfo(ApiVersionDescription description)
    {
        var info = new OpenApiInfo
        {
            Title = "Audit & Compliance API",
            Version = description.ApiVersion.ToString(),
            Description = GetVersionDescription(description.ApiVersion),
            Contact = new OpenApiContact
            {
                Name = "TLI Support Team",
                Email = "<EMAIL>",
                Url = new Uri("https://tli.com/support")
            },
            License = new OpenApiLicense
            {
                Name = "TLI License",
                Url = new Uri("https://tli.com/license")
            }
        };

        if (description.IsDeprecated)
        {
            info.Description += " (DEPRECATED)";
        }

        return info;
    }

    private static string GetVersionDescription(ApiVersion version)
    {
        return version.MajorVersion switch
        {
            1 => "Initial version of the Audit & Compliance API with basic functionality for audit logging, compliance reporting, and service provider ratings.",
            2 => "Enhanced version with advanced analytics, real-time dashboards, mobile support, improved response formats, and comprehensive metadata.",
            _ => "Audit & Compliance API for managing audit logs, compliance reports, and service provider ratings."
        };
    }
}

/// <summary>
/// Operation filter for API versioning
/// </summary>
public class ApiVersionOperationFilter : IOperationFilter
{
    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
        var apiDescription = context.ApiDescription;
        operation.Deprecated |= apiDescription.IsDeprecated();

        if (operation.Parameters == null)
            return;

        foreach (var parameter in operation.Parameters)
        {
            var description = apiDescription.ParameterDescriptions
                .First(p => p.Name == parameter.Name);

            parameter.Description ??= description.ModelMetadata?.Description;
        }
    }
}

/// <summary>
/// Document filter for API versioning
/// </summary>
public class ApiVersionDocumentFilter : IDocumentFilter
{
    public void Apply(OpenApiDocument swaggerDoc, DocumentFilterContext context)
    {
        var apiDescription = context.ApiDescriptions
            .FirstOrDefault(x => x.GroupName == swaggerDoc.Info.Version);

        if (apiDescription?.IsDeprecated() == true)
        {
            swaggerDoc.Info.Description += "\n\n**This API version is deprecated.**";
        }

        // Add version-specific information
        swaggerDoc.Info.Extensions.Add("x-api-version", new Microsoft.OpenApi.Any.OpenApiString(swaggerDoc.Info.Version));
        swaggerDoc.Info.Extensions.Add("x-release-date", new Microsoft.OpenApi.Any.OpenApiString(GetReleaseDate(swaggerDoc.Info.Version)));
    }

    private string GetReleaseDate(string version)
    {
        return version switch
        {
            "1.0" => "2024-01-01",
            "2.0" => "2024-06-01",
            _ => DateTime.UtcNow.ToString("yyyy-MM-dd")
        };
    }
}

/// <summary>
/// API version information controller
/// </summary>
[ApiController]
[Route("api/version")]
[AllowAnonymous]
public class ApiVersionController : ControllerBase
{
    private readonly IApiVersionDescriptionProvider _provider;

    public ApiVersionController(IApiVersionDescriptionProvider provider)
    {
        _provider = provider;
    }

    /// <summary>
    /// Get available API versions
    /// </summary>
    [HttpGet]
    public ActionResult<object> GetVersions()
    {
        var versions = _provider.ApiVersionDescriptions.Select(description => new
        {
            Version = description.ApiVersion.ToString(),
            IsDeprecated = description.IsDeprecated(),
            GroupName = description.GroupName,
            Links = new
            {
                Documentation = $"/swagger/{description.GroupName}/swagger.json",
                SwaggerUI = $"/swagger/index.html"
            }
        }).ToList();

        return Ok(new
        {
            CurrentVersion = "2.0",
            SupportedVersions = versions,
            DeprecationPolicy = new
            {
                NoticePeroid = "6 months",
                SupportPeriod = "12 months after deprecation",
                MigrationGuide = "/docs/migration-guide"
            },
            Timestamp = DateTime.UtcNow
        });
    }

    /// <summary>
    /// Get specific version information
    /// </summary>
    [HttpGet("{version}")]
    public ActionResult<object> GetVersionInfo(string version)
    {
        var description = _provider.ApiVersionDescriptions
            .FirstOrDefault(d => d.ApiVersion.ToString() == version);

        if (description == null)
        {
            return NotFound(new { Error = "Version not found", Version = version });
        }

        var versionInfo = new
        {
            Version = description.ApiVersion.ToString(),
            IsDeprecated = description.IsDeprecated(),
            GroupName = description.GroupName,
            Features = GetVersionFeatures(version),
            BreakingChanges = GetBreakingChanges(version),
            ReleaseDate = GetReleaseDate(version),
            Links = new
            {
                Documentation = $"/swagger/{description.GroupName}/swagger.json",
                SwaggerUI = $"/swagger/index.html",
                MigrationGuide = version == "2.0" ? "/docs/v1-to-v2-migration" : null
            }
        };

        return Ok(versionInfo);
    }

    private List<string> GetVersionFeatures(string version)
    {
        return version switch
        {
            "1.0" => new List<string>
            {
                "Basic audit logging",
                "Compliance reporting",
                "Service provider ratings",
                "Preferred provider networks",
                "JWT authentication"
            },
            "2.0" => new List<string>
            {
                "All V1 features",
                "Advanced analytics with ML",
                "Real-time dashboards",
                "Mobile support",
                "Enhanced response formats",
                "Comprehensive metadata",
                "Export functionality",
                "Security event analysis"
            },
            _ => new List<string>()
        };
    }

    private List<string> GetBreakingChanges(string version)
    {
        return version switch
        {
            "2.0" => new List<string>
            {
                "Response format changes for enhanced metadata",
                "New required fields in some request DTOs",
                "Updated error response structure",
                "Pagination format changes"
            },
            _ => new List<string>()
        };
    }

    private string GetReleaseDate(string version)
    {
        return version switch
        {
            "1.0" => "2024-01-01",
            "2.0" => "2024-06-01",
            _ => DateTime.UtcNow.ToString("yyyy-MM-dd")
        };
    }
}
