using BenchmarkDotNet.Running;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Tests.Performance.Benchmarks;
using SubscriptionManagement.Tests.Performance.LoadTests;
using System.CommandLine;

namespace SubscriptionManagement.Tests.Performance
{
    public class PerformanceTestRunner
    {
        public static async Task<int> Main(string[] args)
        {
            var rootCommand = new RootCommand("Subscription Management Performance Tests");

            var benchmarkCommand = new Command("benchmark", "Run BenchmarkDotNet performance benchmarks");
            var loadTestCommand = new Command("loadtest", "Run NBomber load tests");
            var stressTestCommand = new Command("stress", "Run stress tests");
            var allTestsCommand = new Command("all", "Run all performance tests");

            // Benchmark command options
            var benchmarkTypeOption = new Option<string>(
                "--type",
                description: "Type of benchmark to run (subscription, cache, metrics, all)")
            {
                IsRequired = false
            };
            benchmarkTypeOption.SetDefaultValue("all");
            benchmarkCommand.AddOption(benchmarkTypeOption);

            // Load test command options
            var loadTestTypeOption = new Option<string>(
                "--type",
                description: "Type of load test to run (create, read, mixed, all)")
            {
                IsRequired = false
            };
            loadTestTypeOption.SetDefaultValue("all");
            loadTestCommand.AddOption(loadTestTypeOption);

            var durationOption = new Option<int>(
                "--duration",
                description: "Duration of load test in minutes")
            {
                IsRequired = false
            };
            durationOption.SetDefaultValue(5);
            loadTestCommand.AddOption(durationOption);

            var concurrencyOption = new Option<int>(
                "--concurrency",
                description: "Number of concurrent users")
            {
                IsRequired = false
            };
            concurrencyOption.SetDefaultValue(50);
            loadTestCommand.AddOption(concurrencyOption);

            // Command handlers
            benchmarkCommand.SetHandler(async (string type) =>
            {
                await RunBenchmarks(type);
            }, benchmarkTypeOption);

            loadTestCommand.SetHandler(async (string type, int duration, int concurrency) =>
            {
                await RunLoadTests(type, duration, concurrency);
            }, loadTestTypeOption, durationOption, concurrencyOption);

            stressTestCommand.SetHandler(async () =>
            {
                await RunStressTests();
            });

            allTestsCommand.SetHandler(async () =>
            {
                await RunAllTests();
            });

            rootCommand.AddCommand(benchmarkCommand);
            rootCommand.AddCommand(loadTestCommand);
            rootCommand.AddCommand(stressTestCommand);
            rootCommand.AddCommand(allTestsCommand);

            return await rootCommand.InvokeAsync(args);
        }

        private static async Task RunBenchmarks(string type)
        {
            Console.WriteLine($"Running {type} benchmarks...");

            switch (type.ToLower())
            {
                case "subscription":
                    BenchmarkRunner.Run<SubscriptionBenchmarks>();
                    break;
                case "all":
                default:
                    BenchmarkRunner.Run<SubscriptionBenchmarks>();
                    break;
            }

            Console.WriteLine("Benchmarks completed!");
        }

        private static async Task RunLoadTests(string type, int duration, int concurrency)
        {
            Console.WriteLine($"Running {type} load tests for {duration} minutes with {concurrency} concurrent users...");

            var serviceProvider = BuildServiceProvider();
            var logger = serviceProvider.GetRequiredService<ILogger<SubscriptionLoadTests>>();
            var loadTests = new SubscriptionLoadTests(serviceProvider, logger);

            switch (type.ToLower())
            {
                case "create":
                    loadTests.RunCreateSubscriptionLoadTest();
                    break;
                case "read":
                    loadTests.RunGetSubscriptionLoadTest();
                    break;
                case "mixed":
                    loadTests.RunMixedWorkloadTest();
                    break;
                case "all":
                default:
                    loadTests.RunCreateSubscriptionLoadTest();
                    await Task.Delay(TimeSpan.FromMinutes(1)); // Brief pause between tests
                    loadTests.RunGetSubscriptionLoadTest();
                    await Task.Delay(TimeSpan.FromMinutes(1));
                    loadTests.RunMixedWorkloadTest();
                    break;
            }

            Console.WriteLine("Load tests completed!");
        }

        private static async Task RunStressTests()
        {
            Console.WriteLine("Running stress tests...");

            var serviceProvider = BuildServiceProvider();
            var logger = serviceProvider.GetRequiredService<ILogger<SubscriptionLoadTests>>();
            var loadTests = new SubscriptionLoadTests(serviceProvider, logger);

            loadTests.RunStressTest();

            Console.WriteLine("Stress tests completed!");
        }

        private static async Task RunAllTests()
        {
            Console.WriteLine("Running all performance tests...");

            // Run benchmarks first
            await RunBenchmarks("all");

            // Brief pause
            await Task.Delay(TimeSpan.FromMinutes(1));

            // Run load tests
            await RunLoadTests("all", 5, 50);

            // Brief pause
            await Task.Delay(TimeSpan.FromMinutes(1));

            // Run stress tests
            await RunStressTests();

            Console.WriteLine("All performance tests completed!");
        }

        private static IServiceProvider BuildServiceProvider()
        {
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: true)
                .AddJsonFile("appsettings.Performance.json", optional: true)
                .AddEnvironmentVariables()
                .Build();

            var services = new ServiceCollection();

            // Add configuration
            services.AddSingleton<IConfiguration>(configuration);

            // Add logging
            services.AddLogging(builder =>
            {
                builder.AddConsole();
                builder.AddDebug();
                builder.SetMinimumLevel(LogLevel.Information);
            });

            // Add MediatR
            services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(typeof(PerformanceTestRunner).Assembly));

            // Add subscription management services
            // Note: In a real implementation, you'd add your actual DI configuration here
            // services.AddSubscriptionManagementInfrastructure(configuration);

            return services.BuildServiceProvider();
        }
    }

    public class PerformanceTestConfiguration
    {
        public BenchmarkConfiguration Benchmark { get; set; } = new();
        public LoadTestConfiguration LoadTest { get; set; } = new();
        public StressTestConfiguration StressTest { get; set; } = new();
    }

    public class BenchmarkConfiguration
    {
        public bool RunMemoryDiagnoser { get; set; } = true;
        public bool RunCpuDiagnoser { get; set; } = true;
        public string OutputDirectory { get; set; } = "BenchmarkResults";
        public List<string> Categories { get; set; } = new() { "Subscription", "Cache", "Metrics" };
    }

    public class LoadTestConfiguration
    {
        public int DefaultDurationMinutes { get; set; } = 5;
        public int DefaultConcurrency { get; set; } = 50;
        public int WarmupDurationSeconds { get; set; } = 30;
        public string ReportDirectory { get; set; } = "LoadTestReports";
        public List<string> ReportFormats { get; set; } = new() { "Html", "Csv" };
    }

    public class StressTestConfiguration
    {
        public int MaxConcurrency { get; set; } = 500;
        public int RampUpDurationMinutes { get; set; } = 10;
        public int SustainDurationMinutes { get; set; } = 5;
        public double ErrorThresholdPercentage { get; set; } = 5.0;
        public int ResponseTimeThresholdMs { get; set; } = 2000;
    }
}
