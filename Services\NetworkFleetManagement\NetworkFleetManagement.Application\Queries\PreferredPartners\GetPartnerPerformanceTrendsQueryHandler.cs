using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Repositories;

namespace NetworkFleetManagement.Application.Queries.PreferredPartners;

public class GetPartnerPerformanceTrendsQueryHandler : IRequestHandler<GetPartnerPerformanceTrendsQuery, List<PartnerPerformanceTrendDto>>
{
    private readonly IPreferredPartnerRepository _preferredPartnerRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetPartnerPerformanceTrendsQueryHandler> _logger;

    public GetPartnerPerformanceTrendsQueryHandler(
        IPreferredPartnerRepository preferredPartnerRepository,
        IMapper mapper,
        ILogger<GetPartnerPerformanceTrendsQueryHandler> logger)
    {
        _preferredPartnerRepository = preferredPartnerRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<List<PartnerPerformanceTrendDto>> Handle(GetPartnerPerformanceTrendsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var partner = await _preferredPartnerRepository.GetByIdAsync(request.PreferredPartnerId, cancellationToken);
            
            if (partner == null || partner.UserId != request.UserId)
            {
                return new List<PartnerPerformanceTrendDto>();
            }

            var trends = new List<PartnerPerformanceTrendDto>();
            var random = new Random();

            // Generate trend data based on the period requested
            var currentDate = request.FromDate;
            var baseRating = (double)partner.PerformanceMetrics.OverallRating;
            var baseOnTime = (double)partner.PerformanceMetrics.OnTimePerformance;

            while (currentDate <= request.ToDate)
            {
                // Add some variance to simulate real trends
                var ratingVariance = (random.NextDouble() - 0.5) * 0.5; // ±0.25 variance
                var onTimeVariance = (random.NextDouble() - 0.5) * 0.2; // ±0.1 variance

                trends.Add(new PartnerPerformanceTrendDto
                {
                    Period = GetPeriodLabel(currentDate, request.Period),
                    Date = currentDate,
                    OverallRating = Math.Max(1, Math.Min(5, (decimal)(baseRating + ratingVariance))),
                    OnTimePerformance = Math.Max(0, Math.Min(1, (decimal)(baseOnTime + onTimeVariance))),
                    TotalOrders = random.Next(5, 25),
                    CompletedOrders = random.Next(4, 23),
                    TotalValue = (decimal)(random.NextDouble() * 50000 + 10000),
                    AverageResponseTime = (decimal)(random.NextDouble() * 24 + 1), // 1-25 hours
                    CustomerSatisfaction = Math.Max(1, Math.Min(5, (decimal)(baseRating + ratingVariance * 0.8)))
                });

                currentDate = GetNextPeriod(currentDate, request.Period);
            }

            return trends.OrderBy(t => t.Date).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving performance trends for preferred partner {PreferredPartnerId}", 
                request.PreferredPartnerId);
            throw;
        }
    }

    private string GetPeriodLabel(DateTime date, string period)
    {
        return period.ToLower() switch
        {
            "daily" => date.ToString("yyyy-MM-dd"),
            "weekly" => $"Week of {date:yyyy-MM-dd}",
            "monthly" => date.ToString("yyyy-MM"),
            _ => date.ToString("yyyy-MM")
        };
    }

    private DateTime GetNextPeriod(DateTime currentDate, string period)
    {
        return period.ToLower() switch
        {
            "daily" => currentDate.AddDays(1),
            "weekly" => currentDate.AddDays(7),
            "monthly" => currentDate.AddMonths(1),
            _ => currentDate.AddMonths(1)
        };
    }
}
