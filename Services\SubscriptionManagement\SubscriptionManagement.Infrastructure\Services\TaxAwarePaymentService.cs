using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Interfaces;
using SubscriptionManagement.Domain.ValueObjects;
using Shared.Messaging;

namespace SubscriptionManagement.Infrastructure.Services
{
    public class TaxAwarePaymentService : IPaymentService
    {
        private readonly ITaxCalculationService _taxCalculationService;
        private readonly IPaymentService _basePaymentService;
        private readonly IMessageBroker _messageBroker;
        private readonly ILogger<TaxAwarePaymentService> _logger;

        public TaxAwarePaymentService(
            ITaxCalculationService taxCalculationService,
            IPaymentService basePaymentService,
            IMessageBroker messageBroker,
            ILogger<TaxAwarePaymentService> logger)
        {
            _taxCalculationService = taxCalculationService;
            _basePaymentService = basePaymentService;
            _messageBroker = messageBroker;
            _logger = logger;
        }

        public async Task<PaymentResult> ProcessPaymentAsync(PaymentRequest request)
        {
            _logger.LogInformation("Processing tax-aware payment for subscription {SubscriptionId}", request.SubscriptionId);

            try
            {
                // Calculate tax for the payment amount
                var taxBreakdown = await _taxCalculationService.GetTaxBreakdownAsync(
                    Money.Create(request.Amount, request.Currency), 
                    request.CustomerRegion, 
                    request.CustomerId);

                // Create enhanced payment request with tax details
                var enhancedRequest = new TaxAwarePaymentRequest
                {
                    SubscriptionId = request.SubscriptionId,
                    CustomerId = request.CustomerId,
                    Amount = request.Amount,
                    Currency = request.Currency,
                    PaymentMethodId = request.PaymentMethodId,
                    CustomerRegion = request.CustomerRegion,
                    
                    // Tax details
                    BaseAmount = taxBreakdown.BaseAmount.Amount,
                    TaxAmount = taxBreakdown.TotalTaxAmount.Amount,
                    TotalAmount = taxBreakdown.TotalAmount.Amount,
                    IsTaxInclusive = taxBreakdown.IsTaxInclusive,
                    TaxBreakdown = taxBreakdown.TaxItems.Select(item => new PaymentTaxItem
                    {
                        TaxType = item.TaxType.ToString(),
                        TaxName = item.TaxName,
                        Rate = item.Rate,
                        TaxableAmount = item.TaxableAmount.Amount,
                        TaxAmount = item.TaxAmount.Amount,
                        IsExempt = item.IsExempt,
                        ExemptionReason = item.ExemptionReason
                    }).ToList(),
                    
                    Metadata = request.Metadata ?? new Dictionary<string, string>()
                };

                // Add tax metadata
                enhancedRequest.Metadata["tax_region"] = request.CustomerRegion;
                enhancedRequest.Metadata["tax_amount"] = taxBreakdown.TotalTaxAmount.Amount.ToString("F2");
                enhancedRequest.Metadata["tax_inclusive"] = taxBreakdown.IsTaxInclusive.ToString();
                enhancedRequest.Metadata["tax_calculation_date"] = taxBreakdown.CalculatedAt.ToString("O");

                // Process payment with the total amount (including tax)
                var baseRequest = new PaymentRequest
                {
                    SubscriptionId = request.SubscriptionId,
                    CustomerId = request.CustomerId,
                    Amount = enhancedRequest.TotalAmount,
                    Currency = request.Currency,
                    PaymentMethodId = request.PaymentMethodId,
                    CustomerRegion = request.CustomerRegion,
                    Metadata = enhancedRequest.Metadata
                };

                var result = await _basePaymentService.ProcessPaymentAsync(baseRequest);

                // If payment successful, publish tax reporting event
                if (result.IsSuccess)
                {
                    await PublishTaxReportingEventAsync(enhancedRequest, result);
                }

                // Return enhanced result with tax details
                return new TaxAwarePaymentResult
                {
                    IsSuccess = result.IsSuccess,
                    PaymentId = result.PaymentId,
                    TransactionId = result.TransactionId,
                    ErrorMessage = result.ErrorMessage,
                    ProcessedAt = result.ProcessedAt,
                    
                    // Tax details
                    BaseAmount = enhancedRequest.BaseAmount,
                    TaxAmount = enhancedRequest.TaxAmount,
                    TotalAmount = enhancedRequest.TotalAmount,
                    TaxBreakdown = enhancedRequest.TaxBreakdown,
                    CustomerRegion = request.CustomerRegion
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing tax-aware payment for subscription {SubscriptionId}", request.SubscriptionId);
                throw;
            }
        }

        public async Task<bool> ValidatePaymentMethodAsync(string paymentMethodId, Guid customerId)
        {
            return await _basePaymentService.ValidatePaymentMethodAsync(paymentMethodId, customerId);
        }

        public async Task<PaymentMethod> CreatePaymentMethodAsync(CreatePaymentMethodRequest request)
        {
            return await _basePaymentService.CreatePaymentMethodAsync(request);
        }

        public async Task<RefundResult> ProcessRefundAsync(RefundRequest request)
        {
            _logger.LogInformation("Processing tax-aware refund for payment {PaymentId}", request.PaymentId);

            try
            {
                // For refunds, we need to calculate the tax portion to refund
                var refundResult = await _basePaymentService.ProcessRefundAsync(request);

                // If refund successful, publish tax reporting event
                if (refundResult.IsSuccess)
                {
                    await PublishTaxRefundEventAsync(request, refundResult);
                }

                return refundResult;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing tax-aware refund for payment {PaymentId}", request.PaymentId);
                throw;
            }
        }

        private async Task PublishTaxReportingEventAsync(TaxAwarePaymentRequest request, PaymentResult result)
        {
            try
            {
                await _messageBroker.PublishAsync("tax.payment_processed", new
                {
                    PaymentId = result.PaymentId,
                    SubscriptionId = request.SubscriptionId,
                    CustomerId = request.CustomerId,
                    CustomerRegion = request.CustomerRegion,
                    BaseAmount = request.BaseAmount,
                    TaxAmount = request.TaxAmount,
                    TotalAmount = request.TotalAmount,
                    Currency = request.Currency,
                    IsTaxInclusive = request.IsTaxInclusive,
                    TaxBreakdown = request.TaxBreakdown,
                    ProcessedAt = result.ProcessedAt,
                    TransactionId = result.TransactionId
                });

                _logger.LogInformation("Published tax reporting event for payment {PaymentId}", result.PaymentId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error publishing tax reporting event for payment {PaymentId}", result.PaymentId);
                // Don't throw - payment was successful, reporting is secondary
            }
        }

        private async Task PublishTaxRefundEventAsync(RefundRequest request, RefundResult result)
        {
            try
            {
                await _messageBroker.PublishAsync("tax.refund_processed", new
                {
                    RefundId = result.RefundId,
                    PaymentId = request.PaymentId,
                    RefundAmount = request.Amount,
                    Currency = request.Currency,
                    Reason = request.Reason,
                    ProcessedAt = result.ProcessedAt
                });

                _logger.LogInformation("Published tax refund event for refund {RefundId}", result.RefundId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error publishing tax refund event for refund {RefundId}", result.RefundId);
                // Don't throw - refund was successful, reporting is secondary
            }
        }
    }

    // Enhanced payment models with tax details
    public class TaxAwarePaymentRequest : PaymentRequest
    {
        public decimal BaseAmount { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal TotalAmount { get; set; }
        public bool IsTaxInclusive { get; set; }
        public List<PaymentTaxItem> TaxBreakdown { get; set; } = new();
    }

    public class TaxAwarePaymentResult : PaymentResult
    {
        public decimal BaseAmount { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal TotalAmount { get; set; }
        public List<PaymentTaxItem> TaxBreakdown { get; set; } = new();
        public string CustomerRegion { get; set; } = string.Empty;
    }

    public class PaymentTaxItem
    {
        public string TaxType { get; set; } = string.Empty;
        public string TaxName { get; set; } = string.Empty;
        public decimal Rate { get; set; }
        public decimal TaxableAmount { get; set; }
        public decimal TaxAmount { get; set; }
        public bool IsExempt { get; set; }
        public string? ExemptionReason { get; set; }
    }

    // Base interfaces and models (these would typically be in the Application layer)
    public class PaymentRequest
    {
        public Guid SubscriptionId { get; set; }
        public Guid CustomerId { get; set; }
        public decimal Amount { get; set; }
        public string Currency { get; set; } = string.Empty;
        public string PaymentMethodId { get; set; } = string.Empty;
        public string CustomerRegion { get; set; } = string.Empty;
        public Dictionary<string, string>? Metadata { get; set; }
    }

    public class PaymentResult
    {
        public bool IsSuccess { get; set; }
        public string PaymentId { get; set; } = string.Empty;
        public string TransactionId { get; set; } = string.Empty;
        public string? ErrorMessage { get; set; }
        public DateTime ProcessedAt { get; set; }
    }

    public class RefundRequest
    {
        public string PaymentId { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string Currency { get; set; } = string.Empty;
        public string? Reason { get; set; }
    }

    public class RefundResult
    {
        public bool IsSuccess { get; set; }
        public string RefundId { get; set; } = string.Empty;
        public string? ErrorMessage { get; set; }
        public DateTime ProcessedAt { get; set; }
    }

    public class CreatePaymentMethodRequest
    {
        public Guid CustomerId { get; set; }
        public string Type { get; set; } = string.Empty;
        public Dictionary<string, string> Details { get; set; } = new();
    }

    public class PaymentMethod
    {
        public string Id { get; set; } = string.Empty;
        public Guid CustomerId { get; set; }
        public string Type { get; set; } = string.Empty;
        public string LastFour { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public bool IsDefault { get; set; }
    }
}
