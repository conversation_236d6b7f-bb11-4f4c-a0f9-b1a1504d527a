using Microsoft.EntityFrameworkCore;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Entities;
using OrderManagement.Domain.Enums;
using OrderManagement.Infrastructure.Persistence;

namespace OrderManagement.Infrastructure.Repositories;

public class RfqTimelineRepository : IRfqTimelineRepository
{
    private readonly OrderManagementDbContext _context;

    public RfqTimelineRepository(OrderManagementDbContext context)
    {
        _context = context;
    }

    public async Task<List<RfqTimeline>> GetByRfqIdAsync(
        Guid rfqId,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        List<RfqTimelineEventType>? eventTypes = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.RfqTimelines
            .Where(rt => rt.RfqId == rfqId);

        if (fromDate.HasValue)
        {
            query = query.Where(rt => rt.EventTimestamp >= fromDate.Value);
        }

        if (toDate.HasValue)
        {
            query = query.Where(rt => rt.EventTimestamp <= toDate.Value);
        }

        if (eventTypes?.Any() == true)
        {
            query = query.Where(rt => eventTypes.Contains(rt.EventType));
        }

        return await query
            .OrderBy(rt => rt.EventTimestamp)
            .ToListAsync(cancellationToken);
    }

    public async Task<RfqTimeline?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.RfqTimelines
            .FirstOrDefaultAsync(rt => rt.Id == id, cancellationToken);
    }

    public async Task AddAsync(RfqTimeline timelineEvent, CancellationToken cancellationToken = default)
    {
        await _context.RfqTimelines.AddAsync(timelineEvent, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task AddRangeAsync(List<RfqTimeline> timelineEvents, CancellationToken cancellationToken = default)
    {
        await _context.RfqTimelines.AddRangeAsync(timelineEvents, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public void Update(RfqTimeline timelineEvent)
    {
        _context.RfqTimelines.Update(timelineEvent);
    }

    public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var timelineEvent = await GetByIdAsync(id, cancellationToken);
        if (timelineEvent != null)
        {
            _context.RfqTimelines.Remove(timelineEvent);
            await _context.SaveChangesAsync(cancellationToken);
        }
    }
}
