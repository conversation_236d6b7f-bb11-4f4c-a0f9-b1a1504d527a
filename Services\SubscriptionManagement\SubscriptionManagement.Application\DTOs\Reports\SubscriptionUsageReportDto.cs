using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Application.DTOs.Reports;

/// <summary>
/// DTO for subscription usage report
/// </summary>
public class SubscriptionUsageReportDto
{
    public Guid ReportId { get; set; }
    public string ReportName { get; set; } = "Subscription Usage Report";
    public DateTime GeneratedAt { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public ReportSummaryDto Summary { get; set; } = new();
    public List<UserUsageSummaryDto> UserUsageSummaries { get; set; } = new();
    public List<FeatureUsageBreakdownDto> FeatureBreakdowns { get; set; } = new();
    public List<BillingPeriodAnalysisDto> BillingAnalysis { get; set; } = new();
    public List<UsageTrendDto> UsageTrends { get; set; } = new();
    public ReportMetadataDto Metadata { get; set; } = new();
}

/// <summary>
/// DTO for report summary
/// </summary>
public class ReportSummaryDto
{
    public int TotalUsers { get; set; }
    public int ActiveSubscriptions { get; set; }
    public int TotalUsageRecords { get; set; }
    public Dictionary<FeatureType, long> TotalUsageByFeature { get; set; } = new();
    public Dictionary<string, int> SubscriptionsByPlan { get; set; } = new();
    public decimal TotalRevenue { get; set; }
    public decimal AverageUsagePerUser { get; set; }
    public DateTime ReportPeriodStart { get; set; }
    public DateTime ReportPeriodEnd { get; set; }
}

/// <summary>
/// DTO for user usage summary
/// </summary>
public class UserUsageSummaryDto
{
    public Guid UserId { get; set; }
    public string UserName { get; set; } = string.Empty;
    public string UserEmail { get; set; } = string.Empty;
    public Guid SubscriptionId { get; set; }
    public string PlanName { get; set; } = string.Empty;
    public SubscriptionStatus Status { get; set; }
    public DateTime SubscriptionStartDate { get; set; }
    public DateTime? SubscriptionEndDate { get; set; }
    public Dictionary<FeatureType, int> UsageByFeature { get; set; } = new();
    public Dictionary<FeatureType, int> LimitsByFeature { get; set; } = new();
    public Dictionary<FeatureType, double> UsagePercentageByFeature { get; set; } = new();
    public int TotalUsageCount { get; set; }
    public DateTime LastUsageDate { get; set; }
    public decimal BillingAmount { get; set; }
    public string BillingCycle { get; set; } = string.Empty;
    public List<UsageDetailDto> UsageDetails { get; set; } = new();
}

/// <summary>
/// DTO for feature usage breakdown
/// </summary>
public class FeatureUsageBreakdownDto
{
    public FeatureType FeatureType { get; set; }
    public string FeatureName { get; set; } = string.Empty;
    public long TotalUsage { get; set; }
    public int UniqueUsers { get; set; }
    public double AverageUsagePerUser { get; set; }
    public int UsersAtLimit { get; set; }
    public int UsersNearLimit { get; set; } // Above 80% of limit
    public Dictionary<string, long> UsageByPlan { get; set; } = new();
    public List<TopUserUsageDto> TopUsers { get; set; } = new();
}

/// <summary>
/// DTO for billing period analysis
/// </summary>
public class BillingPeriodAnalysisDto
{
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
    public string PeriodName { get; set; } = string.Empty;
    public int ActiveSubscriptions { get; set; }
    public int NewSubscriptions { get; set; }
    public int CancelledSubscriptions { get; set; }
    public decimal TotalRevenue { get; set; }
    public decimal AverageRevenuePerUser { get; set; }
    public Dictionary<FeatureType, long> TotalUsageByFeature { get; set; } = new();
    public Dictionary<string, decimal> RevenueByPlan { get; set; } = new();
    public double ChurnRate { get; set; }
    public double GrowthRate { get; set; }
}

/// <summary>
/// DTO for usage trends
/// </summary>
public class UsageTrendDto
{
    public DateTime Date { get; set; }
    public FeatureType? FeatureType { get; set; }
    public string? PlanName { get; set; }
    public long UsageCount { get; set; }
    public int ActiveUsers { get; set; }
    public double AverageUsagePerUser { get; set; }
    public double GrowthPercentage { get; set; }
}

/// <summary>
/// DTO for usage details
/// </summary>
public class UsageDetailDto
{
    public Guid Id { get; set; }
    public FeatureType FeatureType { get; set; }
    public int UsageCount { get; set; }
    public DateTime UsageDate { get; set; }
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
    public string? Metadata { get; set; }
}

/// <summary>
/// DTO for top user usage
/// </summary>
public class TopUserUsageDto
{
    public Guid UserId { get; set; }
    public string UserName { get; set; } = string.Empty;
    public string PlanName { get; set; } = string.Empty;
    public long UsageCount { get; set; }
    public double UsagePercentage { get; set; }
}

/// <summary>
/// DTO for report metadata
/// </summary>
public class ReportMetadataDto
{
    public Guid RequestedBy { get; set; }
    public string RequestedByRole { get; set; } = string.Empty;
    public DateTime RequestedAt { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
    public TimeSpan GenerationTime { get; set; }
    public int TotalRecords { get; set; }
    public bool IsFiltered { get; set; }
    public Dictionary<string, object> FilterCriteria { get; set; } = new();
    public string ReportVersion { get; set; } = "1.0";
}
