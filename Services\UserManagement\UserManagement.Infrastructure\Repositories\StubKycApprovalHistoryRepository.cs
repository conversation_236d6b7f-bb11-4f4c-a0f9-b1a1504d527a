using UserManagement.Domain.Repositories;

namespace UserManagement.Infrastructure.Repositories
{
    public class StubKycApprovalHistoryRepository : IKycApprovalHistoryRepository
    {
        public Task<object> GetByIdAsync(int id)
        {
            // Stub implementation - return null for now
            return Task.FromResult<object>(null);
        }

        public Task<object> CreateAsync(object entity)
        {
            // Stub implementation
            return Task.FromResult(entity);
        }

        public Task<object> UpdateAsync(object entity)
        {
            // Stub implementation
            return Task.FromResult(entity);
        }

        public Task<bool> DeleteAsync(int id)
        {
            // Stub implementation
            return Task.FromResult(true);
        }

        public Task<IEnumerable<object>> GetAllAsync()
        {
            // Stub implementation
            return Task.FromResult(Enumerable.Empty<object>());
        }
    }
}
