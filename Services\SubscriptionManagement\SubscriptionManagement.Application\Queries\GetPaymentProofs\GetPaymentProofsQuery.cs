using MediatR;
using SubscriptionManagement.Application.DTOs;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Application.Queries.GetPaymentProofs
{
    public class GetPaymentProofsQuery : IRequest<PaymentProofListDto>
    {
        public PaymentProofStatus? Status { get; set; }
        public Guid? UserId { get; set; }
        public Guid? SubscriptionId { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 20;
    }
}
