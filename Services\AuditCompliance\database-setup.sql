-- Audit & Compliance Service Database Setup
-- This script creates the database and user for the Audit & Compliance Service

-- Create database
CREATE DATABASE "AuditComplianceDb" WITH ENCODING 'UTF8';
CREATE DATABASE "AuditComplianceDb_Dev" WITH ENCODING 'UTF8';

-- Connect to the database
\c "AuditComplianceDb";

-- Create user if not exists
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'timescale') THEN
        CREATE USER timescale WITH PASSWORD 'timescale';
    END IF;
END
$$;

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE "AuditComplianceDb" TO timescale;
GRANT ALL PRIVILEGES ON DATABASE "AuditComplianceDb_Dev" TO timescale;

-- Grant schema privileges
GRANT ALL ON SCHEMA public TO timescale;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO timescale;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO timescale;

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Connect to development database and repeat
\c "AuditComplianceDb_Dev";

GRANT ALL ON SCHEMA public TO timescale;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO timescale;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO timescale;

CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create indexes for better performance (will be created by EF migrations, but good to have as reference)
-- These will be created automatically by Entity Framework migrations

COMMENT ON DATABASE "AuditComplianceDb" IS 'Audit & Compliance Service Database - Production';
COMMENT ON DATABASE "AuditComplianceDb_Dev" IS 'Audit & Compliance Service Database - Development';
