using ContentManagement.Domain.Enums;

namespace ContentManagement.Application.DTOs;

/// <summary>
/// Data transfer object for FAQ items
/// </summary>
public class FaqItemDto : ContentDto
{
    public string Question { get; set; } = string.Empty;
    public string Answer { get; set; } = string.Empty;
    public FaqCategory Category { get; set; }
    public List<string> RoleVisibility { get; set; } = new();
    public bool IsHighlighted { get; set; }
    public int ViewCount { get; set; }
    public decimal HelpfulnessRating { get; set; }
    public int TotalRatings { get; set; }
}

/// <summary>
/// Simplified FAQ DTO for lists
/// </summary>
public class FaqSummaryDto
{
    public Guid Id { get; set; }
    public string Question { get; set; } = string.Empty;
    public string Answer { get; set; } = string.Empty;
    public FaqCategory Category { get; set; }
    public ContentStatus Status { get; set; }
    public bool IsHighlighted { get; set; }
    public int ViewCount { get; set; }
    public decimal HelpfulnessRating { get; set; }
    public int SortOrder { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public List<string> Tags { get; set; } = new();
}

/// <summary>
/// FAQ search result DTO
/// </summary>
public class FaqSearchResultDto
{
    public List<FaqSummaryDto> Items { get; set; } = new();
    public int TotalCount { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public int TotalPages { get; set; }
    public bool HasNextPage { get; set; }
    public bool HasPreviousPage { get; set; }
    public Dictionary<FaqCategory, int> CategoryCounts { get; set; } = new();
}

/// <summary>
/// FAQ category statistics DTO
/// </summary>
public class FaqCategoryStatsDto
{
    public FaqCategory Category { get; set; }
    public int TotalCount { get; set; }
    public int PublishedCount { get; set; }
    public int DraftCount { get; set; }
    public int HighlightedCount { get; set; }
    public decimal AverageRating { get; set; }
    public int TotalViews { get; set; }
}
