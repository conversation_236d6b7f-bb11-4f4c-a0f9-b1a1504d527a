using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AnalyticsBIService.Domain.Services
{
    /// <summary>
    /// Interface for caching service operations
    /// </summary>
    public interface ICacheService
    {
        /// <summary>
        /// Get cached value by key
        /// </summary>
        /// <typeparam name="T">Type of cached value</typeparam>
        /// <param name="key">Cache key</param>
        /// <returns>Cached value or default if not found</returns>
        Task<T?> GetAsync<T>(string key) where T : class;

        /// <summary>
        /// Set cached value with expiration
        /// </summary>
        /// <typeparam name="T">Type of value to cache</typeparam>
        /// <param name="key">Cache key</param>
        /// <param name="value">Value to cache</param>
        /// <param name="expiration">Cache expiration time</param>
        Task SetAsync<T>(string key, T value, TimeSpan expiration) where T : class;

        /// <summary>
        /// Set cached value with default expiration
        /// </summary>
        /// <typeparam name="T">Type of value to cache</typeparam>
        /// <param name="key">Cache key</param>
        /// <param name="value">Value to cache</param>
        Task SetAsync<T>(string key, T value) where T : class;

        /// <summary>
        /// Remove cached value by key
        /// </summary>
        /// <param name="key">Cache key</param>
        Task RemoveAsync(string key);

        /// <summary>
        /// Remove multiple cached values by pattern
        /// </summary>
        /// <param name="pattern">Key pattern to match</param>
        Task RemoveByPatternAsync(string pattern);

        /// <summary>
        /// Check if key exists in cache
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <returns>True if key exists</returns>
        Task<bool> ExistsAsync(string key);

        /// <summary>
        /// Get multiple cached values by keys
        /// </summary>
        /// <typeparam name="T">Type of cached values</typeparam>
        /// <param name="keys">Cache keys</param>
        /// <returns>Dictionary of key-value pairs</returns>
        Task<Dictionary<string, T?>> GetManyAsync<T>(IEnumerable<string> keys) where T : class;

        /// <summary>
        /// Set multiple cached values with expiration
        /// </summary>
        /// <typeparam name="T">Type of values to cache</typeparam>
        /// <param name="keyValuePairs">Key-value pairs to cache</param>
        /// <param name="expiration">Cache expiration time</param>
        Task SetManyAsync<T>(Dictionary<string, T> keyValuePairs, TimeSpan expiration) where T : class;

        /// <summary>
        /// Increment numeric value in cache
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <param name="value">Value to increment by</param>
        /// <returns>New value after increment</returns>
        Task<long> IncrementAsync(string key, long value = 1);

        /// <summary>
        /// Decrement numeric value in cache
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <param name="value">Value to decrement by</param>
        /// <returns>New value after decrement</returns>
        Task<long> DecrementAsync(string key, long value = 1);

        /// <summary>
        /// Get cache statistics
        /// </summary>
        /// <returns>Cache statistics</returns>
        Task<CacheStatistics> GetStatisticsAsync();
    }

    /// <summary>
    /// Cache statistics information
    /// </summary>
    public class CacheStatistics
    {
        public long TotalKeys { get; set; }
        public long HitCount { get; set; }
        public long MissCount { get; set; }
        public double HitRatio => TotalRequests > 0 ? (double)HitCount / TotalRequests : 0;
        public long TotalRequests => HitCount + MissCount;
        public long MemoryUsage { get; set; }
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
    }
}
