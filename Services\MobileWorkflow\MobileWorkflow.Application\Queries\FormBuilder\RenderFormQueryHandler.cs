using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.Extensions.Logging;
using MobileWorkflow.Application.Services;

namespace MobileWorkflow.Application.Queries.FormBuilder
{
    public class RenderFormQueryHandler : IRequestHandler<RenderFormQuery, Dictionary<string, object>>
    {
        private readonly IFormBuilderService _formBuilderService;
        private readonly ILogger<RenderFormQueryHandler> _logger;

        public RenderFormQueryHandler(
            IFormBuilderService formBuilderService,
            ILogger<RenderFormQueryHandler> logger)
        {
            _formBuilderService = formBuilderService;
            _logger = logger;
        }

        public async Task<Dictionary<string, object>> Handle(RenderFormQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Rendering form {FormId} for platform {Platform}",
                    request.FormDefinitionId, request.Platform);

                // Add user context if provided
                if (request.UserId.HasValue)
                {
                    request.Context["userId"] = request.UserId.Value;
                }

                if (!string.IsNullOrEmpty(request.DeviceId))
                {
                    request.Context["deviceId"] = request.DeviceId;
                }

                var result = await _formBuilderService.RenderFormAsync(
                    request.FormDefinitionId,
                    request.Context,
                    cancellationToken);

                _logger.LogInformation("Successfully rendered form {FormId}", request.FormDefinitionId);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error rendering form {FormId}", request.FormDefinitionId);
                throw;
            }
        }
    }
}
