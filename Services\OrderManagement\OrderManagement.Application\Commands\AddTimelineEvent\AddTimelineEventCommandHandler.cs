using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Domain.Entities;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Exceptions;

namespace OrderManagement.Application.Commands.AddTimelineEvent;

public class AddTimelineEventCommandHandler : IRequestHandler<AddTimelineEventCommand, Guid>
{
    private readonly IOrderRepository _orderRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<AddTimelineEventCommandHandler> _logger;

    public AddTimelineEventCommandHandler(
        IOrderRepository orderRepository,
        IUnitOfWork unitOfWork,
        ILogger<AddTimelineEventCommandHandler> logger)
    {
        _orderRepository = orderRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<Guid> Handle(AddTimelineEventCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Adding timeline event for order {OrderId}: {EventType}",
                request.OrderId, request.EventType);

            // Get the order
            var order = await _orderRepository.GetByIdAsync(request.OrderId, cancellationToken);
            if (order == null)
            {
                _logger.LogWarning("Order {OrderId} not found", request.OrderId);
                throw new OrderManagementException("Order not found");
            }

            // Create timeline event
            var timelineEvent = OrderTimeline.CreateEvent(
                request.OrderId,
                request.EventType,
                request.EventDescription,
                request.ActorId,
                request.ActorName,
                request.ActorRole,
                request.AdditionalData,
                request.PreviousStatus,
                request.NewStatus,
                request.Notes,
                request.IpAddress,
                request.UserAgent,
                request.CorrelationId,
                request.SessionId,
                request.Metadata);

            // Add to order
            order.AddTimelineEvent(timelineEvent);

            // Save changes
            await _orderRepository.UpdateAsync(order, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Timeline event {EventId} added successfully for order {OrderId}",
                timelineEvent.Id, request.OrderId);

            return timelineEvent.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding timeline event for order {OrderId}", request.OrderId);
            throw;
        }
    }
}
