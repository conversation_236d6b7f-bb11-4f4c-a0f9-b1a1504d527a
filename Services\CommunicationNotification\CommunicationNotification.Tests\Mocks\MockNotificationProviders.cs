using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Domain.Enums;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace CommunicationNotification.Tests.Mocks;

/// <summary>
/// Mock SMS provider for testing
/// </summary>
public class MockSmsProvider : ISmsProvider
{
    private readonly ILogger<MockSmsProvider> _logger;
    private readonly MockProviderSettings _settings;
    private readonly Random _random = new();

    public MockSmsProvider(ILogger<MockSmsProvider> logger, IConfiguration configuration)
    {
        _logger = logger;
        _settings = configuration.GetSection("MockProviders:Sms").Get<MockProviderSettings>() ?? new();
    }

    public async Task<NotificationResult> SendSmsAsync(
        string phoneNumber, 
        string message, 
        Dictionary<string, string>? metadata = null, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Mock SMS: Sending to {PhoneNumber}, Message: {Message}", phoneNumber, message);

        // Simulate delay
        if (_settings.SimulateDelay)
        {
            await Task.Delay(_settings.DelayMs, cancellationToken);
        }

        // Simulate failure
        if (_random.NextDouble() < _settings.FailureRate)
        {
            return NotificationResult.Failed("Mock SMS failure", "MOCK_SMS_ERROR");
        }

        var externalId = $"mock_sms_{Guid.NewGuid():N}";
        
        return NotificationResult.Success(externalId, new Dictionary<string, object>
        {
            ["provider"] = "MockSms",
            ["phone_number"] = phoneNumber,
            ["message_length"] = message.Length,
            ["sent_at"] = DateTime.UtcNow,
            ["delivery_receipt_enabled"] = _settings.EnableDeliveryReceipts
        });
    }

    public async Task<ProviderHealthStatus> CheckHealthAsync(CancellationToken cancellationToken = default)
    {
        await Task.Delay(10, cancellationToken);
        
        return new ProviderHealthStatus
        {
            IsHealthy = true,
            ProviderType = "MockSms",
            Channel = NotificationChannel.Sms,
            ResponseTime = TimeSpan.FromMilliseconds(10),
            LastChecked = DateTime.UtcNow,
            AdditionalInfo = new Dictionary<string, object>
            {
                ["mock"] = true,
                ["failure_rate"] = _settings.FailureRate
            }
        };
    }

    public Task<bool> ValidatePhoneNumberAsync(string phoneNumber, CancellationToken cancellationToken = default)
    {
        // Simple validation for testing
        var isValid = !string.IsNullOrWhiteSpace(phoneNumber) && 
                     phoneNumber.StartsWith("+") && 
                     phoneNumber.Length >= 10;
        
        return Task.FromResult(isValid);
    }
}

/// <summary>
/// Mock email provider for testing
/// </summary>
public class MockEmailProvider : IEmailProvider
{
    private readonly ILogger<MockEmailProvider> _logger;
    private readonly MockProviderSettings _settings;
    private readonly Random _random = new();

    public MockEmailProvider(ILogger<MockEmailProvider> logger, IConfiguration configuration)
    {
        _logger = logger;
        _settings = configuration.GetSection("MockProviders:Email").Get<MockProviderSettings>() ?? new();
    }

    public async Task<NotificationResult> SendEmailAsync(
        string toEmail, 
        string subject, 
        string htmlContent, 
        string? textContent = null, 
        Dictionary<string, string>? metadata = null, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Mock Email: Sending to {Email}, Subject: {Subject}", toEmail, subject);

        // Simulate delay
        if (_settings.SimulateDelay)
        {
            await Task.Delay(_settings.DelayMs, cancellationToken);
        }

        // Simulate failure
        if (_random.NextDouble() < _settings.FailureRate)
        {
            return NotificationResult.Failed("Mock email failure", "MOCK_EMAIL_ERROR");
        }

        var externalId = $"mock_email_{Guid.NewGuid():N}";
        
        return NotificationResult.Success(externalId, new Dictionary<string, object>
        {
            ["provider"] = "MockEmail",
            ["to_email"] = toEmail,
            ["subject"] = subject,
            ["html_length"] = htmlContent.Length,
            ["text_length"] = textContent?.Length ?? 0,
            ["sent_at"] = DateTime.UtcNow,
            ["delivery_receipt_enabled"] = _settings.EnableDeliveryReceipts
        });
    }

    public async Task<ProviderHealthStatus> CheckHealthAsync(CancellationToken cancellationToken = default)
    {
        await Task.Delay(15, cancellationToken);
        
        return new ProviderHealthStatus
        {
            IsHealthy = true,
            ProviderType = "MockEmail",
            Channel = NotificationChannel.Email,
            ResponseTime = TimeSpan.FromMilliseconds(15),
            LastChecked = DateTime.UtcNow,
            AdditionalInfo = new Dictionary<string, object>
            {
                ["mock"] = true,
                ["failure_rate"] = _settings.FailureRate
            }
        };
    }

    public Task<bool> ValidateEmailAsync(string email, CancellationToken cancellationToken = default)
    {
        // Simple validation for testing
        var isValid = !string.IsNullOrWhiteSpace(email) && 
                     email.Contains("@") && 
                     email.Contains(".");
        
        return Task.FromResult(isValid);
    }
}

/// <summary>
/// Mock push notification provider for testing
/// </summary>
public class MockPushNotificationProvider : IPushNotificationProvider
{
    private readonly ILogger<MockPushNotificationProvider> _logger;
    private readonly MockProviderSettings _settings;
    private readonly Random _random = new();

    public MockPushNotificationProvider(ILogger<MockPushNotificationProvider> logger, IConfiguration configuration)
    {
        _logger = logger;
        _settings = configuration.GetSection("MockProviders:Push").Get<MockProviderSettings>() ?? new();
    }

    public async Task<NotificationResult> SendPushNotificationAsync(
        string deviceToken, 
        string title, 
        string body, 
        Dictionary<string, object>? data = null, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Mock Push: Sending to {DeviceToken}, Title: {Title}", deviceToken, title);

        // Simulate delay
        if (_settings.SimulateDelay)
        {
            await Task.Delay(_settings.DelayMs, cancellationToken);
        }

        // Simulate failure
        if (_random.NextDouble() < _settings.FailureRate)
        {
            return NotificationResult.Failed("Mock push notification failure", "MOCK_PUSH_ERROR");
        }

        var externalId = $"mock_push_{Guid.NewGuid():N}";
        
        return NotificationResult.Success(externalId, new Dictionary<string, object>
        {
            ["provider"] = "MockPush",
            ["device_token"] = deviceToken,
            ["title"] = title,
            ["body"] = body,
            ["data_keys"] = data?.Keys.ToList() ?? new List<string>(),
            ["sent_at"] = DateTime.UtcNow,
            ["delivery_receipt_enabled"] = _settings.EnableDeliveryReceipts
        });
    }

    public async Task<NotificationResult> SendBulkPushNotificationAsync(
        List<string> deviceTokens, 
        string title, 
        string body, 
        Dictionary<string, object>? data = null, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Mock Push: Sending bulk to {Count} devices, Title: {Title}", deviceTokens.Count, title);

        // Simulate delay
        if (_settings.SimulateDelay)
        {
            await Task.Delay(_settings.DelayMs * deviceTokens.Count / 10, cancellationToken);
        }

        var successCount = 0;
        var failureCount = 0;

        foreach (var token in deviceTokens)
        {
            if (_random.NextDouble() < _settings.FailureRate)
            {
                failureCount++;
            }
            else
            {
                successCount++;
            }
        }

        var externalId = $"mock_bulk_push_{Guid.NewGuid():N}";
        
        return NotificationResult.Success(externalId, new Dictionary<string, object>
        {
            ["provider"] = "MockPush",
            ["total_devices"] = deviceTokens.Count,
            ["success_count"] = successCount,
            ["failure_count"] = failureCount,
            ["title"] = title,
            ["body"] = body,
            ["sent_at"] = DateTime.UtcNow
        });
    }

    public async Task<ProviderHealthStatus> CheckHealthAsync(CancellationToken cancellationToken = default)
    {
        await Task.Delay(8, cancellationToken);
        
        return new ProviderHealthStatus
        {
            IsHealthy = true,
            ProviderType = "MockPush",
            Channel = NotificationChannel.Push,
            ResponseTime = TimeSpan.FromMilliseconds(8),
            LastChecked = DateTime.UtcNow,
            AdditionalInfo = new Dictionary<string, object>
            {
                ["mock"] = true,
                ["failure_rate"] = _settings.FailureRate
            }
        };
    }

    public Task<bool> ValidateDeviceTokenAsync(string deviceToken, CancellationToken cancellationToken = default)
    {
        // Simple validation for testing
        var isValid = !string.IsNullOrWhiteSpace(deviceToken) && deviceToken.Length > 10;
        
        return Task.FromResult(isValid);
    }
}

/// <summary>
/// Mock WhatsApp provider for testing
/// </summary>
public class MockWhatsAppProvider : IWhatsAppProvider
{
    private readonly ILogger<MockWhatsAppProvider> _logger;
    private readonly MockProviderSettings _settings;
    private readonly Random _random = new();

    public MockWhatsAppProvider(ILogger<MockWhatsAppProvider> logger, IConfiguration configuration)
    {
        _logger = logger;
        _settings = configuration.GetSection("MockProviders:WhatsApp").Get<MockProviderSettings>() ?? new();
    }

    public async Task<NotificationResult> SendWhatsAppMessageAsync(
        string phoneNumber, 
        string message, 
        Dictionary<string, string>? metadata = null, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Mock WhatsApp: Sending to {PhoneNumber}, Message: {Message}", phoneNumber, message);

        // Simulate delay
        if (_settings.SimulateDelay)
        {
            await Task.Delay(_settings.DelayMs, cancellationToken);
        }

        // Simulate failure
        if (_random.NextDouble() < _settings.FailureRate)
        {
            return NotificationResult.Failed("Mock WhatsApp failure", "MOCK_WHATSAPP_ERROR");
        }

        var externalId = $"mock_whatsapp_{Guid.NewGuid():N}";
        
        return NotificationResult.Success(externalId, new Dictionary<string, object>
        {
            ["provider"] = "MockWhatsApp",
            ["phone_number"] = phoneNumber,
            ["message_length"] = message.Length,
            ["sent_at"] = DateTime.UtcNow,
            ["delivery_receipt_enabled"] = _settings.EnableDeliveryReceipts
        });
    }

    public async Task<NotificationResult> SendWhatsAppTemplateAsync(
        string phoneNumber, 
        string templateName, 
        Dictionary<string, string> parameters, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Mock WhatsApp Template: Sending to {PhoneNumber}, Template: {Template}", phoneNumber, templateName);

        // Simulate delay
        if (_settings.SimulateDelay)
        {
            await Task.Delay(_settings.DelayMs, cancellationToken);
        }

        // Simulate failure
        if (_random.NextDouble() < _settings.FailureRate)
        {
            return NotificationResult.Failed("Mock WhatsApp template failure", "MOCK_WHATSAPP_TEMPLATE_ERROR");
        }

        var externalId = $"mock_whatsapp_template_{Guid.NewGuid():N}";
        
        return NotificationResult.Success(externalId, new Dictionary<string, object>
        {
            ["provider"] = "MockWhatsApp",
            ["phone_number"] = phoneNumber,
            ["template_name"] = templateName,
            ["parameters"] = parameters,
            ["sent_at"] = DateTime.UtcNow,
            ["delivery_receipt_enabled"] = _settings.EnableDeliveryReceipts
        });
    }

    public async Task<ProviderHealthStatus> CheckHealthAsync(CancellationToken cancellationToken = default)
    {
        await Task.Delay(12, cancellationToken);
        
        return new ProviderHealthStatus
        {
            IsHealthy = true,
            ProviderType = "MockWhatsApp",
            Channel = NotificationChannel.WhatsApp,
            ResponseTime = TimeSpan.FromMilliseconds(12),
            LastChecked = DateTime.UtcNow,
            AdditionalInfo = new Dictionary<string, object>
            {
                ["mock"] = true,
                ["failure_rate"] = _settings.FailureRate
            }
        };
    }

    public Task<bool> ValidatePhoneNumberAsync(string phoneNumber, CancellationToken cancellationToken = default)
    {
        // Simple validation for testing
        var isValid = !string.IsNullOrWhiteSpace(phoneNumber) && 
                     phoneNumber.StartsWith("+") && 
                     phoneNumber.Length >= 10;
        
        return Task.FromResult(isValid);
    }
}

/// <summary>
/// Mock voice provider for testing
/// </summary>
public class MockVoiceProvider : IVoiceProvider
{
    private readonly ILogger<MockVoiceProvider> _logger;
    private readonly MockProviderSettings _settings;
    private readonly Random _random = new();

    public MockVoiceProvider(ILogger<MockVoiceProvider> logger, IConfiguration configuration)
    {
        _logger = logger;
        _settings = configuration.GetSection("MockProviders:Voice").Get<MockProviderSettings>() ?? new();
    }

    public async Task<NotificationResult> MakeVoiceCallAsync(
        string phoneNumber, 
        string message, 
        Dictionary<string, string>? metadata = null, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Mock Voice: Calling {PhoneNumber}, Message: {Message}", phoneNumber, message);

        // Simulate delay
        if (_settings.SimulateDelay)
        {
            await Task.Delay(_settings.DelayMs, cancellationToken);
        }

        // Simulate failure
        if (_random.NextDouble() < _settings.FailureRate)
        {
            return NotificationResult.Failed("Mock voice call failure", "MOCK_VOICE_ERROR");
        }

        var externalId = $"mock_voice_{Guid.NewGuid():N}";
        
        return NotificationResult.Success(externalId, new Dictionary<string, object>
        {
            ["provider"] = "MockVoice",
            ["phone_number"] = phoneNumber,
            ["message_length"] = message.Length,
            ["call_duration_seconds"] = _random.Next(30, 120),
            ["sent_at"] = DateTime.UtcNow,
            ["delivery_receipt_enabled"] = _settings.EnableDeliveryReceipts
        });
    }

    public async Task<ProviderHealthStatus> CheckHealthAsync(CancellationToken cancellationToken = default)
    {
        await Task.Delay(20, cancellationToken);
        
        return new ProviderHealthStatus
        {
            IsHealthy = true,
            ProviderType = "MockVoice",
            Channel = NotificationChannel.Voice,
            ResponseTime = TimeSpan.FromMilliseconds(20),
            LastChecked = DateTime.UtcNow,
            AdditionalInfo = new Dictionary<string, object>
            {
                ["mock"] = true,
                ["failure_rate"] = _settings.FailureRate
            }
        };
    }

    public Task<bool> ValidatePhoneNumberAsync(string phoneNumber, CancellationToken cancellationToken = default)
    {
        // Simple validation for testing
        var isValid = !string.IsNullOrWhiteSpace(phoneNumber) && 
                     phoneNumber.StartsWith("+") && 
                     phoneNumber.Length >= 10;
        
        return Task.FromResult(isValid);
    }
}

/// <summary>
/// Mock provider settings
/// </summary>
public class MockProviderSettings
{
    public bool SimulateDelay { get; set; } = true;
    public int DelayMs { get; set; } = 100;
    public double FailureRate { get; set; } = 0.1;
    public bool EnableDeliveryReceipts { get; set; } = true;
}

/// <summary>
/// Extension methods for registering mock providers
/// </summary>
public static class MockProviderExtensions
{
    /// <summary>
    /// Add mock external services
    /// </summary>
    public static IServiceCollection AddMockExternalServices(this IServiceCollection services)
    {
        // Replace real providers with mock providers
        services.AddScoped<ISmsProvider, MockSmsProvider>();
        services.AddScoped<IEmailProvider, MockEmailProvider>();
        services.AddScoped<IPushNotificationProvider, MockPushNotificationProvider>();
        services.AddScoped<IWhatsAppProvider, MockWhatsAppProvider>();
        services.AddScoped<IVoiceProvider, MockVoiceProvider>();

        return services;
    }
}
