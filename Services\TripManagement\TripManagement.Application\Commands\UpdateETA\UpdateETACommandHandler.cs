using MediatR;
using Microsoft.Extensions.Logging;
using TripManagement.Application.Interfaces;
using TripManagement.Application.DTOs;

namespace TripManagement.Application.Commands.UpdateETA;

public class UpdateETACommandHandler : IRequestHandler<UpdateETACommand, ETAUpdateResult>
{
    private readonly ITripRepository _tripRepository;
    private readonly IETACalculationService _etaCalculationService;
    private readonly ITrafficService _trafficService;
    private readonly IWeatherService _weatherService;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<UpdateETACommandHandler> _logger;
    private readonly IMessageBroker _messageBroker;

    public UpdateETACommandHandler(
        ITripRepository tripRepository,
        IETACalculationService etaCalculationService,
        ITrafficService trafficService,
        IWeatherService weatherService,
        IUnitOfWork unitOfWork,
        ILogger<UpdateETACommandHandler> logger,
        IMessageBroker messageBroker)
    {
        _tripRepository = tripRepository;
        _etaCalculationService = etaCalculationService;
        _trafficService = trafficService;
        _weatherService = weatherService;
        _unitOfWork = unitOfWork;
        _logger = logger;
        _messageBroker = messageBroker;
    }

    public async Task<ETAUpdateResult> Handle(UpdateETACommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Updating ETA for trip {TripId}", request.TripId);

            // Get the trip
            var trip = await _tripRepository.GetByIdAsync(request.TripId, cancellationToken);
            if (trip == null)
            {
                throw new ArgumentException($"Trip {request.TripId} not found");
            }

            var previousETA = trip.EstimatedEndTime;

            // Prepare ETA calculation parameters
            var etaParams = new ETACalculationParams
            {
                TripId = request.TripId,
                CurrentLocation = request.CurrentLocation,
                CurrentSpeed = request.CurrentSpeed,
                RemainingStops = trip.Stops.Where(s => s.Status == Domain.Enums.TripStopStatus.Pending).ToList(),
                Route = trip.Route,
                VehicleType = trip.Vehicle?.VehicleType,
                DriverId = trip.DriverId
            };

            // Get traffic conditions if requested
            if (request.ConsiderTraffic)
            {
                etaParams.TrafficConditions = await _trafficService.GetTrafficConditionsAsync(
                    request.CurrentLocation, 
                    trip.Route.EndLocation, 
                    cancellationToken);
            }

            // Get weather conditions if requested
            if (request.ConsiderWeather)
            {
                etaParams.WeatherConditions = await _weatherService.GetWeatherConditionsAsync(
                    request.CurrentLocation, 
                    cancellationToken);
            }

            // Calculate updated ETA
            var etaResult = await _etaCalculationService.CalculateETAAsync(etaParams, cancellationToken);

            // Update trip with new ETA
            trip.UpdateEstimatedEndTime(etaResult.UpdatedETA);

            // Update stop ETAs
            foreach (var stopETA in etaResult.StopETAs)
            {
                var stop = trip.Stops.FirstOrDefault(s => s.Id == stopETA.StopId);
                if (stop != null)
                {
                    stop.UpdateScheduledArrival(stopETA.EstimatedArrival);
                }
            }

            // Save changes
            _tripRepository.Update(trip);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Check if ETA change is significant and send notifications
            var etaChange = etaResult.UpdatedETA - previousETA;
            if (Math.Abs(etaChange.TotalMinutes) > 15) // Significant change threshold
            {
                await _messageBroker.PublishAsync("trip.eta_updated", new
                {
                    TripId = request.TripId,
                    PreviousETA = previousETA,
                    UpdatedETA = etaResult.UpdatedETA,
                    ETAChange = etaChange,
                    UpdateReason = etaResult.UpdateReason,
                    ConfidenceLevel = etaResult.ConfidenceLevel,
                    UpdatedAt = DateTime.UtcNow
                }, cancellationToken);

                _logger.LogInformation("Significant ETA change for trip {TripId}: {ETAChange} minutes", 
                    request.TripId, etaChange.TotalMinutes);
            }

            return etaResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating ETA for trip {TripId}", request.TripId);
            throw;
        }
    }
}

public record ETACalculationParams
{
    public Guid TripId { get; init; }
    public LocationDto CurrentLocation { get; init; } = null!;
    public double? CurrentSpeed { get; init; }
    public List<Domain.Entities.TripStop> RemainingStops { get; init; } = new();
    public Domain.ValueObjects.Route Route { get; init; } = null!;
    public Domain.Enums.VehicleType? VehicleType { get; init; }
    public Guid? DriverId { get; init; }
    public TrafficConditions? TrafficConditions { get; init; }
    public WeatherConditions? WeatherConditions { get; init; }
}

public record TrafficConditions
{
    public TrafficLevel TrafficLevel { get; init; }
    public double DelayFactor { get; init; } // Multiplier for travel time
    public List<TrafficIncident> Incidents { get; init; } = new();
}

public record WeatherConditions
{
    public WeatherType WeatherType { get; init; }
    public double VisibilityKm { get; init; }
    public double WindSpeedKmh { get; init; }
    public double DelayFactor { get; init; } // Multiplier for travel time
}

public record TrafficIncident
{
    public string Description { get; init; } = string.Empty;
    public LocationDto Location { get; init; } = null!;
    public TimeSpan EstimatedDelay { get; init; }
}

public enum TrafficLevel
{
    Light = 0,
    Moderate = 1,
    Heavy = 2,
    Severe = 3
}

public enum WeatherType
{
    Clear = 0,
    Cloudy = 1,
    Rain = 2,
    Snow = 3,
    Fog = 4,
    Storm = 5
}
