using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Domain.Repositories;
using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace AnalyticsBIService.Application.Queries.Carrier;

/// <summary>
/// Handler for carrier rating summary queries
/// </summary>
public class GetCarrierRatingSummaryQueryHandler : IRequestHandler<GetCarrierRatingSummaryQuery, CarrierRatingSummaryDto>
{
    private readonly IAnalyticsEventRepository _analyticsEventRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetCarrierRatingSummaryQueryHandler> _logger;

    public GetCarrierRatingSummaryQueryHandler(
        IAnalyticsEventRepository analyticsEventRepository,
        IMapper mapper,
        ILogger<GetCarrierRatingSummaryQueryHandler> logger)
    {
        _analyticsEventRepository = analyticsEventRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<CarrierRatingSummaryDto> Handle(GetCarrierRatingSummaryQuery request, CancellationToken cancellationToken)
    {
        _logger.LogDebug("Getting rating summary for carrier {CarrierId}", request.CarrierId);

        // Get rating-related events for this carrier
        var allEvents = await _analyticsEventRepository.GetByDateRangeAsync(request.FromDate, request.ToDate, 1, 1000, cancellationToken);
        var ratingEvents = allEvents.Items
            .Where(e => e.UserId == request.CarrierId &&
                       e.EventName.Contains("Rating"))
            .ToList();

        // Calculate basic metrics (using mock data for demonstration)
        var overallRating = 4.2m;
        var previousPeriodRating = 4.0m;
        var ratingChange = overallRating - previousPeriodRating;
        var ratingTrend = ratingChange > 0.1m ? "Improving" : ratingChange < -0.1m ? "Declining" : "Stable";
        var totalRatings = 156;
        var totalReviews = 89;

        // Generate rating distribution
        var ratingDistribution = new List<RatingDistributionDto>
        {
            new() { StarRating = 5, Count = 78, Percentage = 50.0m },
            new() { StarRating = 4, Count = 47, Percentage = 30.1m },
            new() { StarRating = 3, Count = 23, Percentage = 14.7m },
            new() { StarRating = 2, Count = 6, Percentage = 3.8m },
            new() { StarRating = 1, Count = 2, Percentage = 1.3m }
        };

        // Generate category ratings
        var categoryRatings = new List<CategoryRatingDto>
        {
            new() { Category = "Service Quality", Rating = 4.3m, PreviousRating = 4.1m, Change = 0.2m, Trend = "Improving", RatingCount = 145 },
            new() { Category = "Communication", Rating = 4.1m, PreviousRating = 3.9m, Change = 0.2m, Trend = "Improving", RatingCount = 132 },
            new() { Category = "Timeliness", Rating = 4.0m, PreviousRating = 4.0m, Change = 0.0m, Trend = "Stable", RatingCount = 156 },
            new() { Category = "Professionalism", Rating = 4.4m, PreviousRating = 4.2m, Change = 0.2m, Trend = "Improving", RatingCount = 128 }
        };

        // Generate rating sources
        var ratingSources = new List<RatingSourceDto>
        {
            new() { Source = "Broker", AverageRating = 4.3m, RatingCount = 89, Percentage = 57.1m },
            new() { Source = "Shipper", AverageRating = 4.1m, RatingCount = 67, Percentage = 42.9m }
        };

        return new CarrierRatingSummaryDto
        {
            OverallRating = overallRating,
            PreviousPeriodRating = previousPeriodRating,
            RatingChange = ratingChange,
            RatingTrend = ratingTrend,
            TotalRatings = totalRatings,
            TotalReviews = totalReviews,
            RatingDistribution = ratingDistribution,
            CategoryRatings = categoryRatings,
            RatingSources = ratingSources
        };
    }
}

/// <summary>
/// Main handler for comprehensive carrier ratings analytics (aggregates all focused queries)
/// </summary>
public class GetCarrierRatingsQueryHandler : IRequestHandler<GetCarrierRatingsQuery, CarrierRatingsAnalyticsDto>
{
    private readonly IMediator _mediator;
    private readonly ILogger<GetCarrierRatingsQueryHandler> _logger;

    public GetCarrierRatingsQueryHandler(
        IMediator mediator,
        ILogger<GetCarrierRatingsQueryHandler> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    public async Task<CarrierRatingsAnalyticsDto> Handle(GetCarrierRatingsQuery request, CancellationToken cancellationToken)
    {
        _logger.LogDebug("Getting comprehensive ratings analytics for carrier {CarrierId}", request.CarrierId);

        // Initialize result
        var result = new CarrierRatingsAnalyticsDto
        {
            CarrierId = request.CarrierId,
            CarrierName = "Carrier", // Would be fetched from carrier service
            FromDate = request.FromDate,
            ToDate = request.ToDate,
            Period = request.Period.ToString()
        };

        // Get rating summary
        var summaryQuery = new GetCarrierRatingSummaryQuery(
            request.CarrierId, request.FromDate, request.ToDate, request.RatingCategory);
        result.RatingSummary = await _mediator.Send(summaryQuery, cancellationToken);

        // Get broker ratings (if requested)
        if (request.IncludeBrokerRatings)
        {
            var brokerQuery = new GetCarrierBrokerRatingsQuery(
                request.CarrierId, request.FromDate, request.ToDate, request.Period);
            result.BrokerRatings = await _mediator.Send(brokerQuery, cancellationToken);
        }

        // Get shipper ratings (if requested)
        if (request.IncludeShipperRatings)
        {
            var shipperQuery = new GetCarrierShipperRatingsQuery(
                request.CarrierId, request.FromDate, request.ToDate, request.Period);
            result.ShipperRatings = await _mediator.Send(shipperQuery, cancellationToken);
        }

        // Get rating trends (if requested)
        if (request.IncludeRatingTrends)
        {
            var trendsQuery = new GetCarrierRatingTrendsQuery(
                request.CarrierId, request.FromDate, request.ToDate, request.Period, request.RatingCategory);
            result.RatingTrends = await _mediator.Send(trendsQuery, cancellationToken);
        }

        // Get feedback analysis (if requested)
        if (request.IncludeFeedbackAnalysis)
        {
            var feedbackQuery = new GetCarrierFeedbackAnalysisQuery(
                request.CarrierId, request.FromDate, request.ToDate);
            result.FeedbackAnalysis = await _mediator.Send(feedbackQuery, cancellationToken);
        }

        // Get competitive analysis (if requested)
        if (request.IncludeCompetitiveAnalysis)
        {
            var competitiveQuery = new GetCarrierRatingCompetitiveAnalysisQuery(
                request.CarrierId, request.FromDate, request.ToDate);
            result.CompetitiveAnalysis = await _mediator.Send(competitiveQuery, cancellationToken);
        }

        // Generate insights and recommendations
        result.Insights = GenerateRatingInsights(result);
        result.Recommendations = GenerateRatingRecommendations(result);

        return result;
    }

    private List<CarrierRatingInsightDto> GenerateRatingInsights(CarrierRatingsAnalyticsDto data)
    {
        var insights = new List<CarrierRatingInsightDto>();

        // Rating trend insight
        if (data.RatingSummary.RatingTrend == "Declining")
        {
            insights.Add(new CarrierRatingInsightDto
            {
                InsightType = "Rating Trend",
                Title = "Declining Rating Trend Detected",
                Description = "Your overall rating has declined compared to the previous period, requiring immediate attention.",
                Impact = "High",
                ConfidenceScore = 85.0m,
                SupportingData = new List<string>
                {
                    $"Current rating: {data.RatingSummary.OverallRating:F1}",
                    $"Previous rating: {data.RatingSummary.PreviousPeriodRating:F1}",
                    $"Change: {data.RatingSummary.RatingChange:F2}"
                },
                GeneratedAt = DateTime.UtcNow
            });
        }

        // Competitive position insight
        if (data.CompetitiveAnalysis != null && data.CompetitiveAnalysis.CompetitivePosition > 70)
        {
            insights.Add(new CarrierRatingInsightDto
            {
                InsightType = "Competitive Position",
                Title = "Strong Competitive Position",
                Description = "You are performing well compared to industry competitors and have opportunities for growth.",
                Impact = "Medium",
                ConfidenceScore = 78.0m,
                SupportingData = new List<string>
                {
                    $"Industry ranking: {data.CompetitiveAnalysis.IndustryRanking} of {data.CompetitiveAnalysis.TotalCompetitors}",
                    $"Competitive position: {data.CompetitiveAnalysis.CompetitivePosition:F1}th percentile",
                    $"Above industry average by {data.CompetitiveAnalysis.CarrierRating - data.CompetitiveAnalysis.IndustryAverageRating:F1} points"
                },
                GeneratedAt = DateTime.UtcNow
            });
        }

        return insights;
    }

    private List<CarrierRatingRecommendationDto> GenerateRatingRecommendations(CarrierRatingsAnalyticsDto data)
    {
        var recommendations = new List<CarrierRatingRecommendationDto>();

        // Communication improvement recommendation
        if (data.FeedbackAnalysis != null &&
            data.FeedbackAnalysis.ImprovementAreas.Any(ia => ia.Area.Contains("Communication") && ia.Priority == "High"))
        {
            recommendations.Add(new CarrierRatingRecommendationDto
            {
                RecommendationType = "Service Improvement",
                Title = "Enhance Communication Processes",
                Description = "Implement better communication protocols to address feedback concerns and improve customer satisfaction.",
                Priority = "High",
                ExpectedImpact = 15.0m,
                ActionItems = new List<string>
                {
                    "Implement real-time tracking and notifications",
                    "Train staff on effective communication",
                    "Set up automated status updates",
                    "Create customer feedback response protocols"
                },
                Timeline = "4-6 weeks",
                Category = "Communication"
            });
        }

        // Service quality enhancement recommendation
        if (data.RatingSummary.CategoryRatings.Any(cr => cr.Category == "Service Quality" && cr.Rating < 4.0m))
        {
            recommendations.Add(new CarrierRatingRecommendationDto
            {
                RecommendationType = "Quality Enhancement",
                Title = "Improve Service Quality Standards",
                Description = "Focus on enhancing service quality to meet customer expectations and improve ratings.",
                Priority = "High",
                ExpectedImpact = 20.0m,
                ActionItems = new List<string>
                {
                    "Review and update service standards",
                    "Implement quality control measures",
                    "Provide additional staff training",
                    "Monitor service delivery closely"
                },
                Timeline = "6-8 weeks",
                Category = "Service"
            });
        }

        return recommendations;
    }
}