using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using MobileWorkflow.Domain.Entities;
using MobileWorkflow.Domain.Repositories;
using Newtonsoft.Json;

namespace MobileWorkflow.Infrastructure.Services;

public interface IWorkflowExecutionEngine
{
    Task<WorkflowExecutionResult> ExecuteWorkflowAsync(Guid workflowId, Guid triggeredBy, Dictionary<string, object> inputData, string? triggerSource = null, CancellationToken cancellationToken = default);
    Task<WorkflowExecutionResult> ContinueExecutionAsync(Guid executionId, CancellationToken cancellationToken = default);
    Task<bool> PauseExecutionAsync(Guid executionId, CancellationToken cancellationToken = default);
    Task<bool> CancelExecutionAsync(Guid executionId, string reason, CancellationToken cancellationToken = default);
    Task<List<WorkflowExecution>> GetRunningExecutionsAsync(CancellationToken cancellationToken = default);
    Task ProcessPendingExecutionsAsync(CancellationToken cancellationToken = default);
}

public class WorkflowExecutionEngine : IWorkflowExecutionEngine
{
    private readonly IWorkflowRepository _workflowRepository;
    private readonly IWorkflowExecutionRepository _executionRepository;
    private readonly IWorkflowTaskRepository _taskRepository;
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<WorkflowExecutionEngine> _logger;

    public WorkflowExecutionEngine(
        IWorkflowRepository workflowRepository,
        IWorkflowExecutionRepository executionRepository,
        IWorkflowTaskRepository taskRepository,
        IServiceProvider serviceProvider,
        ILogger<WorkflowExecutionEngine> logger)
    {
        _workflowRepository = workflowRepository;
        _executionRepository = executionRepository;
        _taskRepository = taskRepository;
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    public async Task<WorkflowExecutionResult> ExecuteWorkflowAsync(Guid workflowId, Guid triggeredBy, Dictionary<string, object> inputData, string? triggerSource = null, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting workflow execution for workflow {WorkflowId} triggered by {TriggeredBy}", workflowId, triggeredBy);

        try
        {
            var workflow = await _workflowRepository.GetByIdAsync(workflowId, cancellationToken);
            if (workflow == null)
            {
                return new WorkflowExecutionResult { IsSuccess = false, ErrorMessage = "Workflow not found" };
            }

            if (!workflow.CanExecute())
            {
                return new WorkflowExecutionResult { IsSuccess = false, ErrorMessage = "Workflow cannot be executed" };
            }

            var execution = workflow.StartExecution(triggeredBy, inputData, triggerSource);
            await _executionRepository.AddAsync(execution, cancellationToken);
            await _workflowRepository.UpdateAsync(workflow, cancellationToken);

            // Start executing the workflow steps
            var result = await ExecuteWorkflowStepsAsync(execution, cancellationToken);
            
            return new WorkflowExecutionResult 
            { 
                IsSuccess = true, 
                ExecutionId = execution.Id,
                Status = execution.Status,
                Message = "Workflow execution started successfully"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing workflow {WorkflowId}", workflowId);
            return new WorkflowExecutionResult { IsSuccess = false, ErrorMessage = ex.Message };
        }
    }

    public async Task<WorkflowExecutionResult> ContinueExecutionAsync(Guid executionId, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Continuing workflow execution {ExecutionId}", executionId);

        try
        {
            var execution = await _executionRepository.GetByIdAsync(executionId, cancellationToken);
            if (execution == null)
            {
                return new WorkflowExecutionResult { IsSuccess = false, ErrorMessage = "Execution not found" };
            }

            if (execution.Status != "Running")
            {
                return new WorkflowExecutionResult { IsSuccess = false, ErrorMessage = $"Cannot continue execution with status: {execution.Status}" };
            }

            await ExecuteWorkflowStepsAsync(execution, cancellationToken);

            return new WorkflowExecutionResult 
            { 
                IsSuccess = true, 
                ExecutionId = execution.Id,
                Status = execution.Status,
                Message = "Workflow execution continued successfully"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error continuing workflow execution {ExecutionId}", executionId);
            return new WorkflowExecutionResult { IsSuccess = false, ErrorMessage = ex.Message };
        }
    }

    public async Task<bool> PauseExecutionAsync(Guid executionId, CancellationToken cancellationToken = default)
    {
        try
        {
            var execution = await _executionRepository.GetByIdAsync(executionId, cancellationToken);
            if (execution == null) return false;

            // In a real implementation, you would add a "Paused" status to the execution
            execution.UpdateContext("paused_at", DateTime.UtcNow);
            await _executionRepository.UpdateAsync(execution, cancellationToken);

            _logger.LogInformation("Workflow execution {ExecutionId} paused", executionId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error pausing workflow execution {ExecutionId}", executionId);
            return false;
        }
    }

    public async Task<bool> CancelExecutionAsync(Guid executionId, string reason, CancellationToken cancellationToken = default)
    {
        try
        {
            var execution = await _executionRepository.GetByIdAsync(executionId, cancellationToken);
            if (execution == null) return false;

            execution.Cancel();
            execution.UpdateContext("cancellation_reason", reason);
            await _executionRepository.UpdateAsync(execution, cancellationToken);

            // Cancel all pending tasks
            var pendingTasks = await _taskRepository.GetByWorkflowExecutionIdAsync(executionId, cancellationToken);
            foreach (var task in pendingTasks.Where(t => !t.IsCompleted()))
            {
                task.Cancel();
                await _taskRepository.UpdateAsync(task, cancellationToken);
            }

            _logger.LogInformation("Workflow execution {ExecutionId} cancelled: {Reason}", executionId, reason);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling workflow execution {ExecutionId}", executionId);
            return false;
        }
    }

    public async Task<List<WorkflowExecution>> GetRunningExecutionsAsync(CancellationToken cancellationToken = default)
    {
        return (await _executionRepository.GetRunningExecutionsAsync(cancellationToken)).ToList();
    }

    public async Task ProcessPendingExecutionsAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Processing pending workflow executions");

        var runningExecutions = await GetRunningExecutionsAsync(cancellationToken);
        
        foreach (var execution in runningExecutions)
        {
            try
            {
                await ContinueExecutionAsync(execution.Id, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing execution {ExecutionId}", execution.Id);
            }
        }
    }

    private async Task<bool> ExecuteWorkflowStepsAsync(WorkflowExecution execution, CancellationToken cancellationToken)
    {
        try
        {
            var workflow = await _workflowRepository.GetByIdAsync(execution.WorkflowId, cancellationToken);
            if (workflow == null) return false;

            var steps = GetWorkflowSteps(workflow.Definition);
            
            while (execution.CurrentStepIndex < steps.Count && execution.Status == "Running")
            {
                var step = steps[execution.CurrentStepIndex];
                var stepResult = await ExecuteStepAsync(execution, step, cancellationToken);

                if (!stepResult.IsSuccess)
                {
                    execution.Fail(stepResult.ErrorMessage ?? "Step execution failed");
                    await _executionRepository.UpdateAsync(execution, cancellationToken);
                    return false;
                }

                if (stepResult.ShouldWait)
                {
                    // Step requires waiting (e.g., manual approval)
                    break;
                }

                execution.MoveToNextStep(step.Id);
                await _executionRepository.UpdateAsync(execution, cancellationToken);
            }

            // Check if workflow is complete
            if (execution.CurrentStepIndex >= steps.Count && execution.Status == "Running")
            {
                execution.Complete();
                await _executionRepository.UpdateAsync(execution, cancellationToken);
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing workflow steps for execution {ExecutionId}", execution.Id);
            execution.Fail(ex.Message);
            await _executionRepository.UpdateAsync(execution, cancellationToken);
            return false;
        }
    }

    private async Task<StepExecutionResult> ExecuteStepAsync(WorkflowExecution execution, WorkflowStep step, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Executing step {StepId} of type {StepType} for execution {ExecutionId}", step.Id, step.Type, execution.Id);

        try
        {
            switch (step.Type.ToLower())
            {
                case "manual":
                    return await ExecuteManualStepAsync(execution, step, cancellationToken);
                case "automated":
                    return await ExecuteAutomatedStepAsync(execution, step, cancellationToken);
                case "approval":
                    return await ExecuteApprovalStepAsync(execution, step, cancellationToken);
                case "notification":
                    return await ExecuteNotificationStepAsync(execution, step, cancellationToken);
                case "condition":
                    return await ExecuteConditionStepAsync(execution, step, cancellationToken);
                case "delay":
                    return await ExecuteDelayStepAsync(execution, step, cancellationToken);
                default:
                    return new StepExecutionResult { IsSuccess = false, ErrorMessage = $"Unknown step type: {step.Type}" };
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing step {StepId}", step.Id);
            return new StepExecutionResult { IsSuccess = false, ErrorMessage = ex.Message };
        }
    }

    private async Task<StepExecutionResult> ExecuteManualStepAsync(WorkflowExecution execution, WorkflowStep step, CancellationToken cancellationToken)
    {
        // Create a manual task
        var task = execution.CreateTask(
            step.Name,
            "Manual",
            step.Parameters,
            GetAssignedUser(step.Parameters)
        );

        await _taskRepository.AddAsync(task, cancellationToken);

        return new StepExecutionResult 
        { 
            IsSuccess = true, 
            ShouldWait = true, // Wait for manual completion
            Message = "Manual task created"
        };
    }

    private async Task<StepExecutionResult> ExecuteAutomatedStepAsync(WorkflowExecution execution, WorkflowStep step, CancellationToken cancellationToken)
    {
        // Execute automated logic based on step configuration
        var action = step.Parameters.GetValueOrDefault("action")?.ToString();
        
        switch (action?.ToLower())
        {
            case "send_email":
                return await ExecuteEmailActionAsync(execution, step, cancellationToken);
            case "update_database":
                return await ExecuteDatabaseActionAsync(execution, step, cancellationToken);
            case "call_api":
                return await ExecuteApiActionAsync(execution, step, cancellationToken);
            default:
                return new StepExecutionResult { IsSuccess = true, Message = "Automated step completed" };
        }
    }

    private async Task<StepExecutionResult> ExecuteApprovalStepAsync(WorkflowExecution execution, WorkflowStep step, CancellationToken cancellationToken)
    {
        var approvers = GetApprovers(step.Parameters);
        
        foreach (var approver in approvers)
        {
            var task = execution.CreateTask(
                $"Approval: {step.Name}",
                "Approval",
                step.Parameters,
                approver
            );

            await _taskRepository.AddAsync(task, cancellationToken);
        }

        return new StepExecutionResult 
        { 
            IsSuccess = true, 
            ShouldWait = true, // Wait for approval
            Message = "Approval tasks created"
        };
    }

    private async Task<StepExecutionResult> ExecuteNotificationStepAsync(WorkflowExecution execution, WorkflowStep step, CancellationToken cancellationToken)
    {
        // In real implementation, this would integrate with the Communication & Notification Service
        var recipients = GetNotificationRecipients(step.Parameters);
        var message = step.Parameters.GetValueOrDefault("message")?.ToString() ?? "Workflow notification";

        _logger.LogInformation("Sending notification to {Count} recipients: {Message}", recipients.Count, message);

        return new StepExecutionResult { IsSuccess = true, Message = "Notification sent" };
    }

    private async Task<StepExecutionResult> ExecuteConditionStepAsync(WorkflowExecution execution, WorkflowStep step, CancellationToken cancellationToken)
    {
        var condition = step.Parameters.GetValueOrDefault("condition")?.ToString();
        var conditionResult = EvaluateCondition(condition, execution.Context);

        execution.UpdateContext($"condition_{step.Id}_result", conditionResult);

        return new StepExecutionResult { IsSuccess = true, Message = $"Condition evaluated: {conditionResult}" };
    }

    private async Task<StepExecutionResult> ExecuteDelayStepAsync(WorkflowExecution execution, WorkflowStep step, CancellationToken cancellationToken)
    {
        var delayMinutes = Convert.ToInt32(step.Parameters.GetValueOrDefault("delay_minutes", 5));
        var delayUntil = DateTime.UtcNow.AddMinutes(delayMinutes);

        execution.UpdateContext("delay_until", delayUntil);

        return new StepExecutionResult 
        { 
            IsSuccess = true, 
            ShouldWait = true, // Wait for delay period
            Message = $"Delayed until {delayUntil}"
        };
    }

    private async Task<StepExecutionResult> ExecuteEmailActionAsync(WorkflowExecution execution, WorkflowStep step, CancellationToken cancellationToken)
    {
        // Mock email sending
        await Task.Delay(100, cancellationToken);
        return new StepExecutionResult { IsSuccess = true, Message = "Email sent" };
    }

    private async Task<StepExecutionResult> ExecuteDatabaseActionAsync(WorkflowExecution execution, WorkflowStep step, CancellationToken cancellationToken)
    {
        // Mock database update
        await Task.Delay(50, cancellationToken);
        return new StepExecutionResult { IsSuccess = true, Message = "Database updated" };
    }

    private async Task<StepExecutionResult> ExecuteApiActionAsync(WorkflowExecution execution, WorkflowStep step, CancellationToken cancellationToken)
    {
        // Mock API call
        await Task.Delay(200, cancellationToken);
        return new StepExecutionResult { IsSuccess = true, Message = "API called" };
    }

    private List<WorkflowStep> GetWorkflowSteps(Dictionary<string, object> definition)
    {
        var steps = new List<WorkflowStep>();
        
        if (definition.TryGetValue("steps", out var stepsObj) && stepsObj is List<object> stepsList)
        {
            foreach (var stepObj in stepsList)
            {
                if (stepObj is Dictionary<string, object> stepDict)
                {
                    steps.Add(new WorkflowStep
                    {
                        Id = stepDict.GetValueOrDefault("id")?.ToString() ?? Guid.NewGuid().ToString(),
                        Name = stepDict.GetValueOrDefault("name")?.ToString() ?? "Unnamed Step",
                        Type = stepDict.GetValueOrDefault("type")?.ToString() ?? "Manual",
                        Parameters = stepDict.GetValueOrDefault("parameters") as Dictionary<string, object> ?? new Dictionary<string, object>()
                    });
                }
            }
        }

        return steps;
    }

    private Guid? GetAssignedUser(Dictionary<string, object> parameters)
    {
        if (parameters.TryGetValue("assigned_to", out var assignedTo) && Guid.TryParse(assignedTo.ToString(), out var userId))
        {
            return userId;
        }
        return null;
    }

    private List<Guid> GetApprovers(Dictionary<string, object> parameters)
    {
        var approvers = new List<Guid>();
        
        if (parameters.TryGetValue("approvers", out var approversObj) && approversObj is List<object> approversList)
        {
            foreach (var approver in approversList)
            {
                if (Guid.TryParse(approver.ToString(), out var approverId))
                {
                    approvers.Add(approverId);
                }
            }
        }

        return approvers;
    }

    private List<Guid> GetNotificationRecipients(Dictionary<string, object> parameters)
    {
        var recipients = new List<Guid>();
        
        if (parameters.TryGetValue("recipients", out var recipientsObj) && recipientsObj is List<object> recipientsList)
        {
            foreach (var recipient in recipientsList)
            {
                if (Guid.TryParse(recipient.ToString(), out var recipientId))
                {
                    recipients.Add(recipientId);
                }
            }
        }

        return recipients;
    }

    private bool EvaluateCondition(string? condition, Dictionary<string, object> context)
    {
        if (string.IsNullOrEmpty(condition)) return true;

        // Simple condition evaluation - in real implementation, use a proper expression evaluator
        return condition.ToLower().Contains("true");
    }
}

// Supporting classes
public class WorkflowExecutionResult
{
    public bool IsSuccess { get; set; }
    public Guid ExecutionId { get; set; }
    public string Status { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string? ErrorMessage { get; set; }
}

public class StepExecutionResult
{
    public bool IsSuccess { get; set; }
    public bool ShouldWait { get; set; }
    public string Message { get; set; } = string.Empty;
    public string? ErrorMessage { get; set; }
}

public class WorkflowStep
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public Dictionary<string, object> Parameters { get; set; } = new();
}
