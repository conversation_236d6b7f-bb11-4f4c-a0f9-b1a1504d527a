using MonitoringObservability.Domain.Enums;
using MonitoringObservability.Domain.Events;

namespace MonitoringObservability.Domain.Entities;

/// <summary>
/// Represents a distributed trace across multiple services
/// </summary>
public class Trace
{
    private readonly List<Span> _spans = new();
    private readonly List<TraceEvent> _events = new();

    public Trace(string traceId, string rootOperationName, string rootServiceName, 
        Dictionary<string, string>? tags = null)
    {
        Id = Guid.NewGuid();
        TraceId = traceId ?? throw new ArgumentNullException(nameof(traceId));
        RootOperationName = rootOperationName ?? throw new ArgumentNullException(nameof(rootOperationName));
        RootServiceName = rootServiceName ?? throw new ArgumentNullException(nameof(rootServiceName));
        Tags = tags ?? new Dictionary<string, string>();
        StartTime = DateTime.UtcNow;
        Status = TraceStatus.Ok;
        IsActive = true;
    }

    // Required for EF Core
    private Trace() { }

    public Guid Id { get; private set; }
    public string TraceId { get; private set; } = string.Empty;
    public string RootOperationName { get; private set; } = string.Empty;
    public string RootServiceName { get; private set; } = string.Empty;
    public DateTime StartTime { get; private set; }
    public DateTime? EndTime { get; private set; }
    public TimeSpan? Duration => EndTime?.Subtract(StartTime);
    public TraceStatus Status { get; private set; }
    public string? ErrorMessage { get; private set; }
    public bool IsActive { get; private set; }
    public Dictionary<string, string> Tags { get; private set; } = new();
    public Dictionary<string, object> Metadata { get; private set; } = new();

    // Navigation properties
    public IReadOnlyList<Span> Spans => _spans.AsReadOnly();
    public IReadOnlyList<TraceEvent> Events => _events.AsReadOnly();

    // Computed properties
    public int SpanCount => _spans.Count;
    public int ServiceCount => _spans.Select(s => s.ServiceName).Distinct().Count();
    public string[] ServiceNames => _spans.Select(s => s.ServiceName).Distinct().ToArray();
    public bool HasErrors => _spans.Any(s => s.HasError) || Status == TraceStatus.Error;
    public TimeSpan? TotalServiceTime => _spans.Sum(s => s.Duration?.TotalMilliseconds) is double total ? TimeSpan.FromMilliseconds(total) : null;

    public void AddSpan(Span span)
    {
        if (span == null) throw new ArgumentNullException(nameof(span));
        if (span.TraceId != TraceId) throw new ArgumentException("Span trace ID must match trace ID");

        _spans.Add(span);
        
        // Update trace metadata based on spans
        UpdateTraceMetadata();
    }

    public void EndTrace(TraceStatus status, string? errorMessage = null)
    {
        if (!IsActive) throw new InvalidOperationException("Trace is already ended");

        EndTime = DateTime.UtcNow;
        Status = status;
        ErrorMessage = errorMessage;
        IsActive = false;

        // Add trace ended event
        AddEvent(new TraceEvent(TraceId, "trace.ended", $"Trace ended with status: {status}", 
            new Dictionary<string, object> 
            { 
                ["status"] = status.ToString(),
                ["duration_ms"] = Duration?.TotalMilliseconds ?? 0,
                ["span_count"] = SpanCount,
                ["service_count"] = ServiceCount
            }));
    }

    public void AddEvent(TraceEvent traceEvent)
    {
        if (traceEvent == null) throw new ArgumentNullException(nameof(traceEvent));
        _events.Add(traceEvent);
    }

    public void AddTag(string key, string value)
    {
        if (string.IsNullOrWhiteSpace(key)) throw new ArgumentException("Tag key cannot be null or empty", nameof(key));
        Tags[key] = value ?? string.Empty;
    }

    public void AddMetadata(string key, object value)
    {
        if (string.IsNullOrWhiteSpace(key)) throw new ArgumentException("Metadata key cannot be null or empty", nameof(key));
        Metadata[key] = value;
    }

    public Span? GetRootSpan()
    {
        return _spans.FirstOrDefault(s => s.ParentSpanId == null);
    }

    public IEnumerable<Span> GetSpansByService(string serviceName)
    {
        return _spans.Where(s => s.ServiceName.Equals(serviceName, StringComparison.OrdinalIgnoreCase));
    }

    public IEnumerable<Span> GetSpansByOperation(string operationName)
    {
        return _spans.Where(s => s.OperationName.Equals(operationName, StringComparison.OrdinalIgnoreCase));
    }

    public IEnumerable<Span> GetErrorSpans()
    {
        return _spans.Where(s => s.HasError);
    }

    public IEnumerable<Span> GetSlowSpans(TimeSpan threshold)
    {
        return _spans.Where(s => s.Duration >= threshold);
    }

    private void UpdateTraceMetadata()
    {
        if (_spans.Any())
        {
            // Update start time to earliest span
            var earliestSpan = _spans.MinBy(s => s.StartTime);
            if (earliestSpan != null && earliestSpan.StartTime < StartTime)
            {
                StartTime = earliestSpan.StartTime;
            }

            // Update metadata
            Metadata["total_spans"] = SpanCount;
            Metadata["unique_services"] = ServiceCount;
            Metadata["service_names"] = ServiceNames;
            Metadata["has_errors"] = HasErrors;
            
            if (TotalServiceTime.HasValue)
            {
                Metadata["total_service_time_ms"] = TotalServiceTime.Value.TotalMilliseconds;
            }
        }
    }
}
