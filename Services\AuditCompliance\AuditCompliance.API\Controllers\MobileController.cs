using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using AuditCompliance.Application.Interfaces;
using AuditCompliance.Application.DTOs.Mobile;

namespace AuditCompliance.API.Controllers;

/// <summary>
/// Mobile-specific API endpoints for compliance and rating functionality
/// </summary>
[ApiController]
[Route("api/mobile")]
[Authorize]
public class MobileController : ControllerBase
{
    private readonly IMobileService _mobileService;
    private readonly ILogger<MobileController> _logger;

    public MobileController(
        IMobileService mobileService,
        ILogger<MobileController> logger)
    {
        _mobileService = mobileService;
        _logger = logger;
    }

    /// <summary>
    /// Submit rating from mobile app
    /// </summary>
    [HttpPost("ratings")]
    public async Task<ActionResult<MobileRatingResponseDto>> SubmitRating(
        [FromBody] MobileRatingSubmissionDto rating)
    {
        try
        {
            var userId = User.Identity?.Name ?? "Unknown";
            var response = await _mobileService.SubmitRatingAsync(rating, userId);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error submitting mobile rating");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Report issue from mobile app
    /// </summary>
    [HttpPost("issues")]
    public async Task<ActionResult<MobileIssueReportResponseDto>> ReportIssue(
        [FromBody] MobileIssueReportDto issue)
    {
        try
        {
            var userId = User.Identity?.Name ?? "Unknown";
            var response = await _mobileService.ReportIssueAsync(issue, userId);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reporting mobile issue");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get compliance status for mobile display
    /// </summary>
    [HttpGet("compliance/{entityId}")]
    public async Task<ActionResult<MobileComplianceStatusDto>> GetComplianceStatus(
        Guid entityId,
        [FromQuery] string entityType = "Organization")
    {
        try
        {
            var status = await _mobileService.GetComplianceStatusAsync(entityId, entityType);
            return Ok(status);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting mobile compliance status for entity {EntityId}", entityId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get mobile dashboard summary
    /// </summary>
    [HttpGet("dashboard")]
    public async Task<ActionResult<MobileDashboardSummaryDto>> GetDashboardSummary(
        [FromQuery] Guid? organizationId = null)
    {
        try
        {
            var userId = User.Identity?.Name ?? "Unknown";
            var summary = await _mobileService.GetDashboardSummaryAsync(userId, organizationId);
            return Ok(summary);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting mobile dashboard summary");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get user profile for mobile app
    /// </summary>
    [HttpGet("profile")]
    public async Task<ActionResult<MobileUserProfileDto>> GetUserProfile()
    {
        try
        {
            var userId = User.Identity?.Name ?? "Unknown";
            var profile = await _mobileService.GetUserProfileAsync(userId);
            return Ok(profile);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting mobile user profile");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Update notification settings
    /// </summary>
    [HttpPut("profile/notifications")]
    public async Task<ActionResult<object>> UpdateNotificationSettings(
        [FromBody] MobileNotificationSettingsDto settings)
    {
        try
        {
            var userId = User.Identity?.Name ?? "Unknown";
            var success = await _mobileService.UpdateNotificationSettingsAsync(userId, settings);
            
            if (success)
            {
                return Ok(new { Message = "Notification settings updated successfully" });
            }
            
            return BadRequest("Failed to update notification settings");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating notification settings");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Search content from mobile app
    /// </summary>
    [HttpPost("search")]
    public async Task<ActionResult<MobileSearchResponseDto>> Search(
        [FromBody] MobileSearchRequestDto request)
    {
        try
        {
            var userId = User.Identity?.Name ?? "Unknown";
            var results = await _mobileService.SearchAsync(request, userId);
            return Ok(results);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing mobile search");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get compliance alerts for mobile display
    /// </summary>
    [HttpGet("alerts")]
    public async Task<ActionResult<List<MobileComplianceAlertDto>>> GetComplianceAlerts(
        [FromQuery] Guid? organizationId = null,
        [FromQuery] int pageSize = 20,
        [FromQuery] int pageNumber = 1)
    {
        try
        {
            var userId = User.Identity?.Name ?? "Unknown";
            var alerts = await _mobileService.GetComplianceAlertsAsync(userId, organizationId, pageSize, pageNumber);
            return Ok(alerts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting mobile compliance alerts");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Mark alert as read
    /// </summary>
    [HttpPut("alerts/{alertId}/read")]
    public async Task<ActionResult<object>> MarkAlertAsRead(Guid alertId)
    {
        try
        {
            var userId = User.Identity?.Name ?? "Unknown";
            var success = await _mobileService.MarkAlertAsReadAsync(alertId, userId);
            
            if (success)
            {
                return Ok(new { Message = "Alert marked as read" });
            }
            
            return BadRequest("Failed to mark alert as read");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error marking alert as read");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get service provider ratings for mobile display
    /// </summary>
    [HttpGet("service-providers/{serviceProviderId}/ratings")]
    public async Task<ActionResult<List<MobileServiceProviderRatingDto>>> GetServiceProviderRatings(
        Guid serviceProviderId,
        [FromQuery] int pageSize = 20,
        [FromQuery] int pageNumber = 1)
    {
        try
        {
            var ratings = await _mobileService.GetServiceProviderRatingsAsync(serviceProviderId, pageSize, pageNumber);
            return Ok(ratings);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting service provider ratings");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get user's submitted ratings
    /// </summary>
    [HttpGet("my-ratings")]
    public async Task<ActionResult<List<MobileUserRatingDto>>> GetUserRatings(
        [FromQuery] int pageSize = 20,
        [FromQuery] int pageNumber = 1)
    {
        try
        {
            var userId = User.Identity?.Name ?? "Unknown";
            var ratings = await _mobileService.GetUserRatingsAsync(userId, pageSize, pageNumber);
            return Ok(ratings);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user ratings");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get user's reported issues
    /// </summary>
    [HttpGet("my-issues")]
    public async Task<ActionResult<List<MobileUserIssueDto>>> GetUserIssues(
        [FromQuery] int pageSize = 20,
        [FromQuery] int pageNumber = 1)
    {
        try
        {
            var userId = User.Identity?.Name ?? "Unknown";
            var issues = await _mobileService.GetUserIssuesAsync(userId, pageSize, pageNumber);
            return Ok(issues);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user issues");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get issue details
    /// </summary>
    [HttpGet("issues/{issueId}")]
    public async Task<ActionResult<MobileIssueDetailDto>> GetIssueDetails(Guid issueId)
    {
        try
        {
            var userId = User.Identity?.Name ?? "Unknown";
            var issue = await _mobileService.GetIssueDetailsAsync(issueId, userId);
            return Ok(issue);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting issue details");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Upload attachment for mobile
    /// </summary>
    [HttpPost("attachments")]
    public async Task<ActionResult<object>> UploadAttachment(
        [FromBody] MobileAttachmentDto attachment)
    {
        try
        {
            var userId = User.Identity?.Name ?? "Unknown";
            var url = await _mobileService.UploadAttachmentAsync(attachment, userId);
            return Ok(new { Url = url, Message = "Attachment uploaded successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading attachment");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get quick actions for mobile dashboard
    /// </summary>
    [HttpGet("quick-actions")]
    public async Task<ActionResult<List<MobileQuickActionDto>>> GetQuickActions(
        [FromQuery] Guid? organizationId = null)
    {
        try
        {
            var userId = User.Identity?.Name ?? "Unknown";
            var actions = await _mobileService.GetQuickActionsAsync(userId, organizationId);
            return Ok(actions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting quick actions");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get compliance trends for mobile charts
    /// </summary>
    [HttpGet("compliance/{entityId}/trends")]
    public async Task<ActionResult<MobileComplianceTrendDto>> GetComplianceTrends(
        Guid entityId,
        [FromQuery] string entityType = "Organization",
        [FromQuery] int days = 30)
    {
        try
        {
            var trends = await _mobileService.GetComplianceTrendsAsync(entityId, entityType, days);
            return Ok(trends);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting compliance trends");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Validate mobile app version
    /// </summary>
    [HttpGet("app-version")]
    [AllowAnonymous]
    public async Task<ActionResult<MobileAppVersionDto>> ValidateAppVersion(
        [FromQuery] string currentVersion,
        [FromQuery] string platform = "iOS")
    {
        try
        {
            var versionInfo = await _mobileService.ValidateAppVersionAsync(currentVersion, platform);
            return Ok(versionInfo);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating app version");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get mobile app configuration
    /// </summary>
    [HttpGet("config")]
    public async Task<ActionResult<object>> GetMobileConfig()
    {
        try
        {
            var userId = User.Identity?.Name ?? "Unknown";
            var userRoles = User.Claims
                .Where(c => c.Type == "role" || c.Type == "http://schemas.microsoft.com/ws/2008/06/identity/claims/role")
                .Select(c => c.Value)
                .ToList();

            var config = new
            {
                UserId = userId,
                Roles = userRoles,
                Features = new
                {
                    RatingSubmission = true,
                    IssueReporting = true,
                    ComplianceMonitoring = userRoles.Contains("Admin") || userRoles.Contains("ComplianceOfficer"),
                    Analytics = userRoles.Contains("Admin") || userRoles.Contains("Auditor"),
                    RealTimeNotifications = true
                },
                Limits = new
                {
                    MaxAttachmentSize = 10 * 1024 * 1024, // 10MB
                    MaxAttachmentsPerSubmission = 5,
                    RatingCooldownHours = 24
                },
                UI = new
                {
                    Theme = "light",
                    PrimaryColor = "#2196F3",
                    SecondaryColor = "#FFC107",
                    RefreshInterval = 30000 // 30 seconds
                },
                Endpoints = new
                {
                    SignalRHub = "/hubs/compliance-monitoring",
                    SupportEmail = "<EMAIL>",
                    SupportPhone = "******-SUPPORT"
                }
            };

            return Ok(config);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting mobile config");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Health check for mobile app
    /// </summary>
    [HttpGet("health")]
    [AllowAnonymous]
    public ActionResult<object> GetMobileHealth()
    {
        try
        {
            var health = new
            {
                Status = "Healthy",
                Timestamp = DateTime.UtcNow,
                Version = "1.0.0",
                Services = new
                {
                    API = "Online",
                    Database = "Connected",
                    SignalR = "Active",
                    Analytics = "Running"
                }
            };

            return Ok(health);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting mobile health status");
            return StatusCode(500, "Internal server error");
        }
    }
}
