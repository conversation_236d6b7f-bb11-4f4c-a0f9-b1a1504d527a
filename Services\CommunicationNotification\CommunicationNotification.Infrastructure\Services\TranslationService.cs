using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace CommunicationNotification.Infrastructure.Services;

/// <summary>
/// Translation service implementation
/// </summary>
public class TranslationService : ITranslationService
{
    private readonly IMemoryCache _cache;
    private readonly ILogger<TranslationService> _logger;
    private readonly ITranslationProvider _translationProvider;
    private readonly ILocalizationService _localizationService;
    private readonly TimeSpan _cacheExpiration = TimeSpan.FromHours(24);

    public TranslationService(
        IMemoryCache cache,
        ILogger<TranslationService> logger,
        ITranslationProvider translationProvider,
        ILocalizationService localizationService)
    {
        _cache = cache;
        _logger = logger;
        _translationProvider = translationProvider;
        _localizationService = localizationService;
    }

    public async Task<string> TranslateTextAsync(
        string text,
        Language sourceLanguage,
        Language targetLanguage,
        CancellationToken cancellationToken = default)
    {
        if (sourceLanguage == targetLanguage)
        {
            return text;
        }

        try
        {
            var cacheKey = $"translation_{text.GetHashCode()}_{sourceLanguage}_{targetLanguage}";
            
            if (_cache.TryGetValue(cacheKey, out string? cachedTranslation) && !string.IsNullOrEmpty(cachedTranslation))
            {
                return cachedTranslation;
            }

            var translation = await _translationProvider.TranslateAsync(text, sourceLanguage, targetLanguage, cancellationToken);
            
            if (!string.IsNullOrEmpty(translation))
            {
                _cache.Set(cacheKey, translation, _cacheExpiration);
            }

            return translation ?? text; // Return original text if translation fails
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to translate text from '{SourceLanguage}' to '{TargetLanguage}'", sourceLanguage, targetLanguage);
            return text; // Return original text on error
        }
    }

    public async Task<MessageContent> TranslateMessageContentAsync(
        MessageContent content,
        Language targetLanguage,
        CancellationToken cancellationToken = default)
    {
        if (content.Language == targetLanguage)
        {
            return content;
        }

        try
        {
            var translatedTitle = await TranslateTextAsync(content.Title, content.Language, targetLanguage, cancellationToken);
            var translatedBody = await TranslateTextAsync(content.Body, content.Language, targetLanguage, cancellationToken);

            return MessageContent.Create(translatedTitle, translatedBody, targetLanguage);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to translate message content to '{TargetLanguage}'", targetLanguage);
            return content; // Return original content on error
        }
    }

    public async Task<Dictionary<Language, string>> TranslateToMultipleLanguagesAsync(
        string text,
        Language sourceLanguage,
        List<Language> targetLanguages,
        CancellationToken cancellationToken = default)
    {
        var results = new Dictionary<Language, string>();

        foreach (var targetLanguage in targetLanguages)
        {
            try
            {
                var translation = await TranslateTextAsync(text, sourceLanguage, targetLanguage, cancellationToken);
                results[targetLanguage] = translation;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to translate text to '{TargetLanguage}'", targetLanguage);
                results[targetLanguage] = text; // Use original text as fallback
            }
        }

        return results;
    }

    public async Task<Dictionary<Language, MessageContent>> TranslateMessageToMultipleLanguagesAsync(
        MessageContent content,
        List<Language> targetLanguages,
        CancellationToken cancellationToken = default)
    {
        var results = new Dictionary<Language, MessageContent>();

        foreach (var targetLanguage in targetLanguages)
        {
            try
            {
                var translatedContent = await TranslateMessageContentAsync(content, targetLanguage, cancellationToken);
                results[targetLanguage] = translatedContent;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to translate message content to '{TargetLanguage}'", targetLanguage);
                results[targetLanguage] = content; // Use original content as fallback
            }
        }

        return results;
    }

    public async Task<bool> IsTranslationSupportedAsync(Language sourceLanguage, Language targetLanguage)
    {
        try
        {
            return await _translationProvider.IsSupportedAsync(sourceLanguage, targetLanguage);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check translation support for '{SourceLanguage}' to '{TargetLanguage}'", sourceLanguage, targetLanguage);
            return false;
        }
    }

    public async Task<TranslationQuality> GetTranslationQualityAsync(
        string originalText,
        string translatedText,
        Language sourceLanguage,
        Language targetLanguage,
        CancellationToken cancellationToken = default)
    {
        try
        {
            return await _translationProvider.GetQualityScoreAsync(originalText, translatedText, sourceLanguage, targetLanguage, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get translation quality score");
            return new TranslationQuality { Score = 0.5, Confidence = "Unknown" };
        }
    }

    public async Task<string> DetectLanguageAsync(string text, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _translationProvider.DetectLanguageAsync(text, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to detect language for text");
            return "en"; // Default to English
        }
    }

    public List<Language> GetSupportedLanguages()
    {
        return _localizationService.GetSupportedLanguages();
    }

    public async Task ClearTranslationCacheAsync(Language? sourceLanguage = null, Language? targetLanguage = null)
    {
        try
        {
            // In a real implementation, you would have a more sophisticated cache invalidation strategy
            _logger.LogInformation("Translation cache clear requested for source: {SourceLanguage}, target: {TargetLanguage}", 
                sourceLanguage, targetLanguage);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to clear translation cache");
        }
    }
}

/// <summary>
/// Translation provider interface
/// </summary>
public interface ITranslationProvider
{
    /// <summary>
    /// Translate text between languages
    /// </summary>
    Task<string?> TranslateAsync(string text, Language sourceLanguage, Language targetLanguage, CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if translation is supported between languages
    /// </summary>
    Task<bool> IsSupportedAsync(Language sourceLanguage, Language targetLanguage);

    /// <summary>
    /// Get translation quality score
    /// </summary>
    Task<TranslationQuality> GetQualityScoreAsync(string originalText, string translatedText, Language sourceLanguage, Language targetLanguage, CancellationToken cancellationToken = default);

    /// <summary>
    /// Detect language of text
    /// </summary>
    Task<string> DetectLanguageAsync(string text, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get supported languages
    /// </summary>
    List<Language> GetSupportedLanguages();
}

/// <summary>
/// Google Translate provider implementation
/// </summary>
public class GoogleTranslateProvider : ITranslationProvider
{
    private readonly ILogger<GoogleTranslateProvider> _logger;
    private readonly string? _apiKey;
    private readonly HttpClient _httpClient;

    public GoogleTranslateProvider(ILogger<GoogleTranslateProvider> logger, IConfiguration configuration, HttpClient httpClient)
    {
        _logger = logger;
        _apiKey = configuration["GoogleTranslate:ApiKey"];
        _httpClient = httpClient;
    }

    public async Task<string?> TranslateAsync(string text, Language sourceLanguage, Language targetLanguage, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(_apiKey))
        {
            _logger.LogWarning("Google Translate API key not configured");
            return null;
        }

        try
        {
            var sourceCode = GetLanguageCode(sourceLanguage);
            var targetCode = GetLanguageCode(targetLanguage);

            // This is a simplified implementation - in production you would use the actual Google Translate API
            _logger.LogDebug("Translating text from '{SourceLanguage}' to '{TargetLanguage}'", sourceLanguage, targetLanguage);
            
            // For demo purposes, return a mock translation
            return await Task.FromResult($"[Translated to {targetLanguage}] {text}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Google Translate API call failed");
            return null;
        }
    }

    public async Task<bool> IsSupportedAsync(Language sourceLanguage, Language targetLanguage)
    {
        // Google Translate supports all our target languages
        var supportedLanguages = GetSupportedLanguages();
        return await Task.FromResult(supportedLanguages.Contains(sourceLanguage) && supportedLanguages.Contains(targetLanguage));
    }

    public async Task<TranslationQuality> GetQualityScoreAsync(string originalText, string translatedText, Language sourceLanguage, Language targetLanguage, CancellationToken cancellationToken = default)
    {
        // Mock implementation - in production you would calculate actual quality metrics
        return await Task.FromResult(new TranslationQuality
        {
            Score = 0.85,
            Confidence = "High",
            Metrics = new Dictionary<string, double>
            {
                ["length_ratio"] = (double)translatedText.Length / originalText.Length,
                ["word_count_ratio"] = (double)translatedText.Split(' ').Length / originalText.Split(' ').Length
            }
        });
    }

    public async Task<string> DetectLanguageAsync(string text, CancellationToken cancellationToken = default)
    {
        // Mock implementation - in production you would use actual language detection
        return await Task.FromResult("en");
    }

    public List<Language> GetSupportedLanguages()
    {
        return new List<Language> { Language.English, Language.Hindi, Language.Kannada };
    }

    private static string GetLanguageCode(Language language)
    {
        return language switch
        {
            Language.English => "en",
            Language.Hindi => "hi",
            Language.Kannada => "kn",
            _ => "en"
        };
    }
}

/// <summary>
/// Azure Translator provider implementation
/// </summary>
public class AzureTranslatorProvider : ITranslationProvider
{
    private readonly ILogger<AzureTranslatorProvider> _logger;
    private readonly string? _subscriptionKey;
    private readonly string? _region;
    private readonly HttpClient _httpClient;

    public AzureTranslatorProvider(ILogger<AzureTranslatorProvider> logger, IConfiguration configuration, HttpClient httpClient)
    {
        _logger = logger;
        _subscriptionKey = configuration["AzureTranslator:SubscriptionKey"];
        _region = configuration["AzureTranslator:Region"];
        _httpClient = httpClient;
    }

    public async Task<string?> TranslateAsync(string text, Language sourceLanguage, Language targetLanguage, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(_subscriptionKey))
        {
            _logger.LogWarning("Azure Translator subscription key not configured");
            return null;
        }

        try
        {
            // This is a simplified implementation - in production you would use the actual Azure Translator API
            _logger.LogDebug("Translating text using Azure Translator from '{SourceLanguage}' to '{TargetLanguage}'", sourceLanguage, targetLanguage);
            
            // For demo purposes, return a mock translation
            return await Task.FromResult($"[Azure Translated to {targetLanguage}] {text}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Azure Translator API call failed");
            return null;
        }
    }

    public async Task<bool> IsSupportedAsync(Language sourceLanguage, Language targetLanguage)
    {
        var supportedLanguages = GetSupportedLanguages();
        return await Task.FromResult(supportedLanguages.Contains(sourceLanguage) && supportedLanguages.Contains(targetLanguage));
    }

    public async Task<TranslationQuality> GetQualityScoreAsync(string originalText, string translatedText, Language sourceLanguage, Language targetLanguage, CancellationToken cancellationToken = default)
    {
        return await Task.FromResult(new TranslationQuality
        {
            Score = 0.90,
            Confidence = "High",
            Provider = "Azure Translator"
        });
    }

    public async Task<string> DetectLanguageAsync(string text, CancellationToken cancellationToken = default)
    {
        return await Task.FromResult("en");
    }

    public List<Language> GetSupportedLanguages()
    {
        return new List<Language> { Language.English, Language.Hindi, Language.Kannada };
    }
}

/// <summary>
/// Translation quality information
/// </summary>
public class TranslationQuality
{
    public double Score { get; set; } // 0.0 to 1.0
    public string Confidence { get; set; } = "Unknown";
    public string? Provider { get; set; }
    public Dictionary<string, double> Metrics { get; set; } = new();
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}
