using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Repositories;

namespace NetworkFleetManagement.Application.Queries.PreferredPartners;

public class GetAutoAssignEligiblePartnersQueryHandler : IRequestHandler<GetAutoAssignEligiblePartnersQuery, List<PreferredPartnerSummaryDto>>
{
    private readonly IPreferredPartnerRepository _preferredPartnerRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetAutoAssignEligiblePartnersQueryHandler> _logger;

    public GetAutoAssignEligiblePartnersQueryHandler(
        IPreferredPartnerRepository preferredPartnerRepository,
        IMapper mapper,
        ILogger<GetAutoAssignEligiblePartnersQueryHandler> logger)
    {
        _preferredPartnerRepository = preferredPartnerRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<List<PreferredPartnerSummaryDto>> Handle(GetAutoAssignEligiblePartnersQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var partners = await _preferredPartnerRepository.GetAutoAssignEligiblePartnersAsync(
                request.UserId, 
                request.PartnerType, 
                request.Route, 
                request.LoadType, 
                cancellationToken);

            var result = _mapper.Map<List<PreferredPartnerSummaryDto>>(partners);

            if (request.Limit.HasValue)
            {
                result = result.Take(request.Limit.Value).ToList();
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving auto-assign eligible partners for user {UserId}, partner type {PartnerType}", 
                request.UserId, request.PartnerType);
            throw;
        }
    }
}
