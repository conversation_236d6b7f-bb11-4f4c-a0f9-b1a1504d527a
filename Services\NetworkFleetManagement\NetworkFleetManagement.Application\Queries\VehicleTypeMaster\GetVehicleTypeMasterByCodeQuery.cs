using MediatR;
using NetworkFleetManagement.Application.DTOs;

namespace NetworkFleetManagement.Application.Queries.VehicleTypeMaster;

/// <summary>
/// Query to get a vehicle type master by code
/// </summary>
public class GetVehicleTypeMasterByCodeQuery : IRequest<VehicleTypeMasterDto?>
{
    public string Code { get; set; }

    public GetVehicleTypeMasterByCodeQuery(string code)
    {
        Code = code;
    }
}
