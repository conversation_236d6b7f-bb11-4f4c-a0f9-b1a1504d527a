# PowerShell script to run comprehensive tests for Communication & Notification Service

param(
    [Parameter(Mandatory=$false)]
    [string]$TestType = "All",
    
    [Parameter(Mandatory=$false)]
    [string]$Configuration = "Debug",
    
    [Parameter(Mandatory=$false)]
    [switch]$Coverage = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$Performance = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$Integration = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$Unit = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$Parallel = $true,
    
    [Parameter(Mandatory=$false)]
    [switch]$Verbose = $false,
    
    [Parameter(Mandatory=$false)]
    [string]$Filter = "",
    
    [Parameter(Mandatory=$false)]
    [string]$OutputPath = "TestResults",
    
    [Parameter(Mandatory=$false)]
    [switch]$GenerateReport = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$OpenReport = $false
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Colors for output
$Green = "Green"
$Yellow = "Yellow"
$Red = "Red"
$Cyan = "Cyan"

function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

function Write-Header {
    param([string]$Title)
    Write-Host ""
    Write-ColorOutput "=" * 60 -Color $Cyan
    Write-ColorOutput "  $Title" -Color $Cyan
    Write-ColorOutput "=" * 60 -Color $Cyan
    Write-Host ""
}

function Write-Step {
    param([string]$Message)
    Write-ColorOutput "▶ $Message" -Color $Yellow
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "✓ $Message" -Color $Green
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "✗ $Message" -Color $Red
}

# Main script
try {
    Write-Header "TLI Communication & Notification Service - Test Runner"
    
    # Set working directory to the test project
    $ScriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
    $TestProjectPath = Join-Path $ScriptPath "..\CommunicationNotification.Tests"
    
    if (-not (Test-Path $TestProjectPath)) {
        Write-Error "Test project not found at: $TestProjectPath"
        exit 1
    }
    
    Set-Location $TestProjectPath
    Write-Step "Working directory: $TestProjectPath"
    
    # Create output directory
    if (-not (Test-Path $OutputPath)) {
        New-Item -ItemType Directory -Path $OutputPath -Force | Out-Null
        Write-Step "Created output directory: $OutputPath"
    }
    
    # Check if dotnet CLI is available
    Write-Step "Checking .NET CLI availability..."
    try {
        $dotnetVersion = dotnet --version
        Write-Success ".NET CLI found - Version: $dotnetVersion"
    }
    catch {
        Write-Error ".NET CLI not found. Please install .NET SDK."
        exit 1
    }
    
    # Restore packages
    Write-Step "Restoring NuGet packages..."
    dotnet restore --verbosity quiet
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Package restore failed"
        exit 1
    }
    Write-Success "Packages restored successfully"
    
    # Build the test project
    Write-Step "Building test project..."
    dotnet build --configuration $Configuration --no-restore --verbosity quiet
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Build failed"
        exit 1
    }
    Write-Success "Test project built successfully"
    
    # Prepare test arguments
    $testArgs = @(
        "test"
        "--configuration", $Configuration
        "--no-build"
        "--logger", "trx;LogFileName=TestResults.trx"
        "--logger", "console;verbosity=normal"
        "--results-directory", $OutputPath
    )
    
    # Add coverage arguments if requested
    if ($Coverage) {
        Write-Step "Enabling code coverage collection..."
        $testArgs += @(
            "--collect", "XPlat Code Coverage"
            "--settings", "coverlet.runsettings"
        )
    }
    
    # Add parallel execution settings
    if ($Parallel) {
        $testArgs += @("--parallel")
    }
    else {
        $testArgs += @("--parallel", "false")
    }
    
    # Add verbosity settings
    if ($Verbose) {
        $testArgs += @("--verbosity", "detailed")
    }
    
    # Determine which tests to run based on parameters
    $testCategories = @()
    
    if ($Unit -or $TestType -eq "Unit" -or $TestType -eq "All") {
        $testCategories += "Unit"
    }
    
    if ($Integration -or $TestType -eq "Integration" -or $TestType -eq "All") {
        $testCategories += "Integration"
    }
    
    if ($Performance -or $TestType -eq "Performance" -or $TestType -eq "All") {
        $testCategories += "Performance"
    }
    
    # If no specific categories selected, run all
    if ($testCategories.Count -eq 0) {
        $testCategories = @("Unit", "Integration", "Performance")
    }
    
    # Run tests for each category
    $totalTests = 0
    $passedTests = 0
    $failedTests = 0
    $skippedTests = 0
    
    foreach ($category in $testCategories) {
        Write-Header "Running $category Tests"
        
        # Build filter for test category
        $categoryFilter = ""
        switch ($category) {
            "Unit" { $categoryFilter = "FullyQualifiedName~Unit" }
            "Integration" { $categoryFilter = "FullyQualifiedName~Integration" }
            "Performance" { $categoryFilter = "FullyQualifiedName~Performance" }
        }
        
        # Combine with user filter if provided
        $finalFilter = $categoryFilter
        if ($Filter) {
            $finalFilter = "($categoryFilter)&($Filter)"
        }
        
        # Add filter to test arguments
        $categoryTestArgs = $testArgs + @("--filter", $finalFilter)
        
        Write-Step "Running tests with filter: $finalFilter"
        
        # Execute tests
        $testOutput = dotnet @categoryTestArgs 2>&1
        $testExitCode = $LASTEXITCODE
        
        # Parse test results
        $testOutput | ForEach-Object {
            if ($_ -match "Total tests: (\d+)") {
                $totalTests += [int]$matches[1]
            }
            if ($_ -match "Passed: (\d+)") {
                $passedTests += [int]$matches[1]
            }
            if ($_ -match "Failed: (\d+)") {
                $failedTests += [int]$matches[1]
            }
            if ($_ -match "Skipped: (\d+)") {
                $skippedTests += [int]$matches[1]
            }
        }
        
        if ($testExitCode -eq 0) {
            Write-Success "$category tests completed successfully"
        }
        else {
            Write-Error "$category tests failed"
            if ($Verbose) {
                Write-Host $testOutput
            }
        }
        
        Write-Host ""
    }
    
    # Generate coverage report if requested
    if ($Coverage -and $GenerateReport) {
        Write-Header "Generating Coverage Report"
        
        Write-Step "Installing ReportGenerator tool..."
        dotnet tool install --global dotnet-reportgenerator-globaltool --ignore-failed-sources 2>$null
        
        $coverageFiles = Get-ChildItem -Path $OutputPath -Filter "coverage.cobertura.xml" -Recurse
        if ($coverageFiles.Count -gt 0) {
            $coverageFile = $coverageFiles[0].FullName
            $reportPath = Join-Path $OutputPath "CoverageReport"
            
            Write-Step "Generating HTML coverage report..."
            reportgenerator -reports:$coverageFile -targetdir:$reportPath -reporttypes:Html
            
            if ($LASTEXITCODE -eq 0) {
                Write-Success "Coverage report generated at: $reportPath"
                
                if ($OpenReport) {
                    $indexFile = Join-Path $reportPath "index.html"
                    if (Test-Path $indexFile) {
                        Start-Process $indexFile
                        Write-Success "Coverage report opened in browser"
                    }
                }
            }
            else {
                Write-Error "Failed to generate coverage report"
            }
        }
        else {
            Write-Error "No coverage files found"
        }
    }
    
    # Display test summary
    Write-Header "Test Summary"
    Write-Host "Total Tests:  $totalTests"
    Write-ColorOutput "Passed:       $passedTests" -Color $Green
    if ($failedTests -gt 0) {
        Write-ColorOutput "Failed:       $failedTests" -Color $Red
    }
    else {
        Write-ColorOutput "Failed:       $failedTests" -Color $Green
    }
    Write-ColorOutput "Skipped:      $skippedTests" -Color $Yellow
    
    if ($totalTests -gt 0) {
        $passRate = [math]::Round(($passedTests / $totalTests) * 100, 2)
        Write-Host "Pass Rate:    $passRate%"
    }
    
    # Display test result files
    Write-Host ""
    Write-Step "Test result files:"
    Get-ChildItem -Path $OutputPath -Filter "*.trx" | ForEach-Object {
        Write-Host "  - $($_.FullName)"
    }
    
    if ($Coverage) {
        Get-ChildItem -Path $OutputPath -Filter "*.xml" | ForEach-Object {
            Write-Host "  - $($_.FullName)"
        }
    }
    
    # Exit with appropriate code
    if ($failedTests -gt 0) {
        Write-Error "Some tests failed. Check the test results for details."
        exit 1
    }
    else {
        Write-Success "All tests passed successfully!"
        exit 0
    }
}
catch {
    Write-Error "Test execution failed with error: $($_.Exception.Message)"
    Write-Host $_.ScriptStackTrace
    exit 1
}

Write-Host ""
Write-ColorOutput "Test execution completed." -Color $Green
