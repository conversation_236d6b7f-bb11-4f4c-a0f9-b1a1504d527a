using NetworkFleetManagement.Domain.Enums;

namespace NetworkFleetManagement.Application.DTOs;

public class BrokerCarrierNetworkDto
{
    public Guid Id { get; set; }
    public Guid BrokerId { get; set; }
    public Guid CarrierId { get; set; }
    public NetworkRelationshipStatus Status { get; set; }
    public DateTime EstablishedAt { get; set; }
    public DateTime? ActivatedAt { get; set; }
    public DateTime? SuspendedAt { get; set; }
    public DateTime? TerminatedAt { get; set; }
    public string? SuspensionReason { get; set; }
    public string? TerminationReason { get; set; }
    public PerformanceMetricsDto PerformanceMetrics { get; set; } = new();
    public decimal? PreferredRate { get; set; }
    public int Priority { get; set; }
    public string? Notes { get; set; }
    public bool IsExclusive { get; set; }
    public DateTime? ExclusivityExpiresAt { get; set; }
    public decimal? MinimumRate { get; set; }
    public decimal? MaximumRate { get; set; }
    public int? PaymentTermsDays { get; set; }
    public string? ContractNumber { get; set; }
    public DateTime? ContractStartDate { get; set; }
    public DateTime? ContractEndDate { get; set; }
    public bool IsActive { get; set; }
    public bool IsContractValid { get; set; }
    public bool IsExclusivityActive { get; set; }
    public string CarrierCompanyName { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

public class CreateBrokerCarrierNetworkDto
{
    public Guid BrokerId { get; set; }
    public Guid CarrierId { get; set; }
    public decimal? PreferredRate { get; set; }
    public int Priority { get; set; } = 10;
    public bool IsExclusive { get; set; }
    public DateTime? ExclusivityExpiresAt { get; set; }
    public decimal? MinimumRate { get; set; }
    public decimal? MaximumRate { get; set; }
    public int? PaymentTermsDays { get; set; }
    public string? ContractNumber { get; set; }
    public DateTime? ContractStartDate { get; set; }
    public DateTime? ContractEndDate { get; set; }
    public string? Notes { get; set; }
}

public class UpdateNetworkStatusDto
{
    public NetworkRelationshipStatus Status { get; set; }
    public string? Reason { get; set; }
}

public class UpdateNetworkRatesDto
{
    public decimal? PreferredRate { get; set; }
    public decimal? MinimumRate { get; set; }
    public decimal? MaximumRate { get; set; }
}

public class ActivateNetworkDto
{
    public string? Notes { get; set; }
}

public class SuspendNetworkDto
{
    public string Reason { get; set; } = string.Empty;
    public DateTime? SuspensionDate { get; set; }
}

public class TerminateNetworkDto
{
    public string Reason { get; set; } = string.Empty;
    public DateTime? TerminationDate { get; set; }
}

public class UpdateNetworkPriorityDto
{
    public int Priority { get; set; }
}

public class NetworkPerformanceDto
{
    public Guid NetworkId { get; set; }
    public PerformanceMetricsDto PerformanceMetrics { get; set; } = new();
    public List<NetworkPerformanceRecordDto> PerformanceRecords { get; set; } = new();
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public decimal AverageRating { get; set; }
    public decimal OnTimePercentage { get; set; }
    public decimal CompletionRate { get; set; }
    public decimal TotalRevenue { get; set; }
}

public class NetworkPerformanceRecordDto
{
    public Guid Id { get; set; }
    public Guid NetworkId { get; set; }
    public Guid? TripId { get; set; }
    public Guid? OrderId { get; set; }
    public DateTime RecordDate { get; set; }
    public decimal? QuoteAmount { get; set; }
    public decimal? FinalAmount { get; set; }
    public bool WasOnTime { get; set; }
    public int? DelayMinutes { get; set; }
    public decimal? CustomerRating { get; set; }
    public string? CustomerFeedback { get; set; }
    public bool WasCancelled { get; set; }
    public string? CancellationReason { get; set; }
    public decimal? PenaltyAmount { get; set; }
    public decimal? BonusAmount { get; set; }
    public string? Notes { get; set; }
    public decimal? NetAmount { get; set; }
    public bool IsSuccessful { get; set; }
    public DateTime CreatedAt { get; set; }
}
