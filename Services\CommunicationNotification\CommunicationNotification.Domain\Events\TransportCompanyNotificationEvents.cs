using Shared.Domain.Common;
using Shared.Domain.Common;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;

namespace CommunicationNotification.Domain.Events;

/// <summary>
/// Event raised when transport company notification configuration is created
/// </summary>
public class TransportCompanyNotificationConfigurationCreatedEvent : DomainEvent
{
    public Guid ConfigurationId { get; }
    public Guid TransportCompanyId { get; }
    public Guid CreatedByUserId { get; }
    public DateTime CreatedAt { get; }

    public TransportCompanyNotificationConfigurationCreatedEvent(
        Guid configurationId,
        Guid transportCompanyId,
        Guid createdByUserId,
        DateTime createdAt)
    {
        ConfigurationId = configurationId;
        TransportCompanyId = transportCompanyId;
        CreatedByUserId = createdByUserId;
        CreatedAt = createdAt;
    }
}

/// <summary>
/// Event raised when notification configuration is updated
/// </summary>
public class NotificationConfigurationUpdatedEvent : DomainEvent
{
    public Guid ConfigurationId { get; }
    public Guid TransportCompanyId { get; }
    public string ConfigurationSection { get; }
    public Guid UpdatedByUserId { get; }
    public DateTime UpdatedAt { get; }

    public NotificationConfigurationUpdatedEvent(
        Guid configurationId,
        Guid transportCompanyId,
        string configurationSection,
        Guid updatedByUserId,
        DateTime updatedAt)
    {
        ConfigurationId = configurationId;
        TransportCompanyId = transportCompanyId;
        ConfigurationSection = configurationSection;
        UpdatedByUserId = updatedByUserId;
        UpdatedAt = updatedAt;
    }
}

/// <summary>
/// Event raised when notification type is added
/// </summary>
public class NotificationTypeAddedEvent : DomainEvent
{
    public Guid ConfigurationId { get; }
    public Guid TransportCompanyId { get; }
    public MessageType MessageType { get; }
    public Guid AddedByUserId { get; }
    public DateTime AddedAt { get; }

    public NotificationTypeAddedEvent(
        Guid configurationId,
        Guid transportCompanyId,
        MessageType messageType,
        Guid addedByUserId,
        DateTime addedAt)
    {
        ConfigurationId = configurationId;
        TransportCompanyId = transportCompanyId;
        MessageType = messageType;
        AddedByUserId = addedByUserId;
        AddedAt = addedAt;
    }
}

/// <summary>
/// Event raised when notification type is updated
/// </summary>
public class NotificationTypeUpdatedEvent : DomainEvent
{
    public Guid ConfigurationId { get; }
    public Guid TransportCompanyId { get; }
    public MessageType MessageType { get; }
    public Guid UpdatedByUserId { get; }
    public DateTime UpdatedAt { get; }

    public NotificationTypeUpdatedEvent(
        Guid configurationId,
        Guid transportCompanyId,
        MessageType messageType,
        Guid updatedByUserId,
        DateTime updatedAt)
    {
        ConfigurationId = configurationId;
        TransportCompanyId = transportCompanyId;
        MessageType = messageType;
        UpdatedByUserId = updatedByUserId;
        UpdatedAt = updatedAt;
    }
}

/// <summary>
/// Event raised when notification type is removed
/// </summary>
public class NotificationTypeRemovedEvent : DomainEvent
{
    public Guid ConfigurationId { get; }
    public Guid TransportCompanyId { get; }
    public MessageType MessageType { get; }
    public Guid RemovedByUserId { get; }
    public DateTime RemovedAt { get; }

    public NotificationTypeRemovedEvent(
        Guid configurationId,
        Guid transportCompanyId,
        MessageType messageType,
        Guid removedByUserId,
        DateTime removedAt)
    {
        ConfigurationId = configurationId;
        TransportCompanyId = transportCompanyId;
        MessageType = messageType;
        RemovedByUserId = removedByUserId;
        RemovedAt = removedAt;
    }
}

/// <summary>
/// Event raised when notification recipient is added
/// </summary>
public class NotificationRecipientAddedEvent : DomainEvent
{
    public Guid ConfigurationId { get; }
    public Guid TransportCompanyId { get; }
    public Guid UserId { get; }
    public NotificationRecipientRole Role { get; }
    public Guid AddedByUserId { get; }
    public DateTime AddedAt { get; }

    public NotificationRecipientAddedEvent(
        Guid configurationId,
        Guid transportCompanyId,
        Guid userId,
        NotificationRecipientRole role,
        Guid addedByUserId,
        DateTime addedAt)
    {
        ConfigurationId = configurationId;
        TransportCompanyId = transportCompanyId;
        UserId = userId;
        Role = role;
        AddedByUserId = addedByUserId;
        AddedAt = addedAt;
    }
}

/// <summary>
/// Event raised when notification recipient is removed
/// </summary>
public class NotificationRecipientRemovedEvent : DomainEvent
{
    public Guid ConfigurationId { get; }
    public Guid TransportCompanyId { get; }
    public Guid UserId { get; }
    public NotificationRecipientRole Role { get; }
    public Guid RemovedByUserId { get; }
    public DateTime RemovedAt { get; }

    public NotificationRecipientRemovedEvent(
        Guid configurationId,
        Guid transportCompanyId,
        Guid userId,
        NotificationRecipientRole role,
        Guid removedByUserId,
        DateTime removedAt)
    {
        ConfigurationId = configurationId;
        TransportCompanyId = transportCompanyId;
        UserId = userId;
        Role = role;
        RemovedByUserId = removedByUserId;
        RemovedAt = removedAt;
    }
}

/// <summary>
/// Event raised when notification configuration is enabled
/// </summary>
public class NotificationConfigurationEnabledEvent : DomainEvent
{
    public Guid ConfigurationId { get; }
    public Guid TransportCompanyId { get; }
    public Guid EnabledByUserId { get; }
    public DateTime EnabledAt { get; }

    public NotificationConfigurationEnabledEvent(
        Guid configurationId,
        Guid transportCompanyId,
        Guid enabledByUserId,
        DateTime enabledAt)
    {
        ConfigurationId = configurationId;
        TransportCompanyId = transportCompanyId;
        EnabledByUserId = enabledByUserId;
        EnabledAt = enabledAt;
    }
}

/// <summary>
/// Event raised when notification configuration is disabled
/// </summary>
public class NotificationConfigurationDisabledEvent : DomainEvent
{
    public Guid ConfigurationId { get; }
    public Guid TransportCompanyId { get; }
    public Guid DisabledByUserId { get; }
    public DateTime DisabledAt { get; }

    public NotificationConfigurationDisabledEvent(
        Guid configurationId,
        Guid transportCompanyId,
        Guid disabledByUserId,
        DateTime disabledAt)
    {
        ConfigurationId = configurationId;
        TransportCompanyId = transportCompanyId;
        DisabledByUserId = disabledByUserId;
        DisabledAt = disabledAt;
    }
}

/// <summary>
/// Event raised when notification digest is generated
/// </summary>
public class NotificationDigestGeneratedEvent : DomainEvent
{
    public Guid TransportCompanyId { get; }
    public string DigestType { get; }
    public int NotificationCount { get; }
    public DateTime GeneratedAt { get; }
    public List<Guid> RecipientIds { get; }

    public NotificationDigestGeneratedEvent(
        Guid transportCompanyId,
        string digestType,
        int notificationCount,
        DateTime generatedAt,
        List<Guid> recipientIds)
    {
        TransportCompanyId = transportCompanyId;
        DigestType = digestType;
        NotificationCount = notificationCount;
        GeneratedAt = generatedAt;
        RecipientIds = recipientIds;
    }
}

/// <summary>
/// Event raised when notification is grouped
/// </summary>
public class NotificationGroupedEvent : DomainEvent
{
    public Guid TransportCompanyId { get; }
    public MessageType MessageType { get; }
    public int GroupedNotificationCount { get; }
    public DateTime GroupedAt { get; }
    public List<Guid> GroupedNotificationIds { get; }

    public NotificationGroupedEvent(
        Guid transportCompanyId,
        MessageType messageType,
        int groupedNotificationCount,
        DateTime groupedAt,
        List<Guid> groupedNotificationIds)
    {
        TransportCompanyId = transportCompanyId;
        MessageType = messageType;
        GroupedNotificationCount = groupedNotificationCount;
        GroupedAt = groupedAt;
        GroupedNotificationIds = groupedNotificationIds;
    }
}

/// <summary>
/// Event raised when notification frequency limit is exceeded
/// </summary>
public class NotificationFrequencyLimitExceededEvent : DomainEvent
{
    public Guid TransportCompanyId { get; }
    public MessageType MessageType { get; }
    public int AttemptedCount { get; }
    public int AllowedCount { get; }
    public string LimitType { get; } // "Hourly" or "Daily"
    public DateTime OccurredAt { get; }

    public NotificationFrequencyLimitExceededEvent(
        Guid transportCompanyId,
        MessageType messageType,
        int attemptedCount,
        int allowedCount,
        string limitType,
        DateTime occurredAt)
    {
        TransportCompanyId = transportCompanyId;
        MessageType = messageType;
        AttemptedCount = attemptedCount;
        AllowedCount = allowedCount;
        LimitType = limitType;
        OccurredAt = occurredAt;
    }
}

/// <summary>
/// Event raised when notification is suppressed due to quiet hours
/// </summary>
public class NotificationSuppressedDueToQuietHoursEvent : DomainEvent
{
    public Guid TransportCompanyId { get; }
    public MessageType MessageType { get; }
    public DateTime SuppressedAt { get; }
    public DateTime QuietHoursStart { get; }
    public DateTime QuietHoursEnd { get; }

    public NotificationSuppressedDueToQuietHoursEvent(
        Guid transportCompanyId,
        MessageType messageType,
        DateTime suppressedAt,
        DateTime quietHoursStart,
        DateTime quietHoursEnd)
    {
        TransportCompanyId = transportCompanyId;
        MessageType = messageType;
        SuppressedAt = suppressedAt;
        QuietHoursStart = quietHoursStart;
        QuietHoursEnd = quietHoursEnd;
    }
}


