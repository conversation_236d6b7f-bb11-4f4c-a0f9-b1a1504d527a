using TripManagement.Domain.Entities;

namespace TripManagement.Domain.Repositories;

public interface IMilestoneConfirmationConfigurationRepository
{
    Task<MilestoneConfirmationConfigurationEntity?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<MilestoneConfirmationConfigurationEntity?> GetByTripAndMilestoneAsync(Guid tripId, Guid? milestoneId, CancellationToken cancellationToken = default);
    Task<List<MilestoneConfirmationConfigurationEntity>> GetByTripIdAsync(Guid tripId, CancellationToken cancellationToken = default);
    Task<List<MilestoneConfirmationConfigurationEntity>> GetActiveConfigurationsAsync(CancellationToken cancellationToken = default);
    Task<List<MilestoneConfirmationConfigurationEntity>> GetByRequirementTypeAsync(string requirementType, bool enabled, CancellationToken cancellationToken = default);
    Task<List<MilestoneConfirmationConfigurationEntity>> GetConfigurationsRequiringApprovalAsync(CancellationToken cancellationToken = default);
    Task<List<MilestoneConfirmationConfigurationEntity>> GetConfigurationsWithSequenceValidationAsync(CancellationToken cancellationToken = default);
    Task<Dictionary<string, int>> GetConfigurationStatisticsAsync(CancellationToken cancellationToken = default);
    Task AddAsync(MilestoneConfirmationConfigurationEntity configuration, CancellationToken cancellationToken = default);
    void Update(MilestoneConfirmationConfigurationEntity configuration);
    void Delete(MilestoneConfirmationConfigurationEntity configuration);
    Task<bool> ExistsAsync(Guid tripId, Guid? milestoneId, CancellationToken cancellationToken = default);
}
