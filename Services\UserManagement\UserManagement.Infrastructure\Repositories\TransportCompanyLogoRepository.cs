using Microsoft.EntityFrameworkCore;
using UserManagement.Application.Interfaces;
using UserManagement.Infrastructure.Data;

namespace UserManagement.Infrastructure.Repositories
{
    public class TransportCompanyLogoRepository : ITransportCompanyLogoRepository
    {
        private readonly UserManagementDbContext _context;

        public TransportCompanyLogoRepository(UserManagementDbContext context)
        {
            _context = context;
        }

        public async Task<TransportCompanyLogo?> GetByIdAsync(Guid id)
        {
            return await _context.Set<TransportCompanyLogo>()
                .FirstOrDefaultAsync(tcl => tcl.Id == id);
        }

        public async Task<TransportCompanyLogo?> GetByCompanyIdAsync(Guid companyId)
        {
            return await _context.Set<TransportCompanyLogo>()
                .FirstOrDefaultAsync(tcl => tcl.CompanyId == companyId);
        }

        public async Task<TransportCompanyLogo?> GetByTransportCompanyIdAsync(Guid transportCompanyId)
        {
            return await _context.Set<TransportCompanyLogo>()
                .FirstOrDefaultAsync(tcl => tcl.CompanyId == transportCompanyId);
        }

        public async Task<TransportCompanyLogo> AddAsync(TransportCompanyLogo logo)
        {
            await _context.Set<TransportCompanyLogo>().AddAsync(logo);
            return logo;
        }

        public async Task<TransportCompanyLogo> UpdateAsync(TransportCompanyLogo logo)
        {
            _context.Set<TransportCompanyLogo>().Update(logo);
            return logo;
        }

        public async Task DeleteAsync(Guid id)
        {
            var logo = await GetByIdAsync(id);
            if (logo != null)
            {
                _context.Set<TransportCompanyLogo>().Remove(logo);
            }
        }

        public async Task SaveChangesAsync()
        {
            await _context.SaveChangesAsync();
        }
    }
}
