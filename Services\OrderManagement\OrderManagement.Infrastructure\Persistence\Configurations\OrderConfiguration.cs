using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OrderManagement.Domain.Entities;

namespace OrderManagement.Infrastructure.Persistence.Configurations;

public class OrderConfiguration : IEntityTypeConfiguration<Order>
{
    public void Configure(EntityTypeBuilder<Order> builder)
    {
        builder.ToTable("Orders");

        builder.HasKey(o => o.Id);

        builder.Property(o => o.Id)
            .ValueGeneratedNever();

        builder.Property(o => o.TransportCompanyId)
            .IsRequired();

        builder.Property(o => o.BrokerId)
            .IsRequired();

        builder.Property(o => o.CarrierId);

        builder.Property(o => o.RfqId);

        builder.Property(o => o.AcceptedBidId);

        builder.Property(o => o.OrderNumber)
            .IsRequired()
            .HasMaxLength(50);

        builder.HasIndex(o => o.OrderNumber)
            .IsUnique();

        builder.Property(o => o.Title)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(o => o.Description)
            .IsRequired()
            .HasMaxLength(2000);

        builder.Property(o => o.Status)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(o => o.CreatedAt)
            .IsRequired();

        builder.Property(o => o.ConfirmedAt);

        builder.Property(o => o.CompletedAt);

        builder.Property(o => o.CancelledAt);

        builder.Property(o => o.CancellationReason)
            .HasMaxLength(500);

        builder.Property(o => o.SpecialInstructions)
            .HasMaxLength(1000);

        builder.Property(o => o.IsUrgent)
            .IsRequired();

        builder.Property(o => o.PaymentStatus)
            .IsRequired()
            .HasConversion<string>();

        // Relationships
        builder.HasOne(o => o.Rfq)
            .WithMany()
            .HasForeignKey(o => o.RfqId)
            .OnDelete(DeleteBehavior.SetNull);

        builder.HasOne(o => o.AcceptedBid)
            .WithMany()
            .HasForeignKey(o => o.AcceptedBidId)
            .OnDelete(DeleteBehavior.SetNull);

        builder.HasMany(o => o.Documents)
            .WithOne(d => d.Order)
            .HasForeignKey(d => d.OrderId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(o => o.StatusHistory)
            .WithOne(h => h.Order)
            .HasForeignKey(h => h.OrderId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(o => o.Invoices)
            .WithOne(i => i.Order)
            .HasForeignKey(i => i.OrderId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(o => o.TransportCompanyId);
        builder.HasIndex(o => o.BrokerId);
        builder.HasIndex(o => o.CarrierId);
        builder.HasIndex(o => o.Status);
        builder.HasIndex(o => o.PaymentStatus);
        builder.HasIndex(o => o.CreatedAt);
        builder.HasIndex(o => new { o.Status, o.CreatedAt });
        builder.HasIndex(o => new { o.TransportCompanyId, o.Status });
        builder.HasIndex(o => new { o.BrokerId, o.Status });
        builder.HasIndex(o => new { o.CarrierId, o.Status });
    }
}
