using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using MediatR;
using DataStorage.Application.Commands;
using DataStorage.Application.Queries;
using DataStorage.Application.DTOs;
using DataStorage.Domain.Enums;

namespace DataStorage.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class DocumentsController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<DocumentsController> _logger;

    public DocumentsController(IMediator mediator, ILogger<DocumentsController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Upload a new document
    /// </summary>
    [HttpPost("upload")]
    public async Task<ActionResult<DocumentDto>> UploadDocument([FromForm] UploadDocumentRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            
            var command = new UploadDocumentCommand
            {
                Title = request.Title,
                Description = request.Description,
                DocumentType = request.DocumentType,
                Category = request.Category,
                AccessLevel = request.AccessLevel,
                FileStream = request.File.OpenReadStream(),
                FileName = request.File.FileName,
                ContentType = request.File.ContentType,
                OwnerId = userId,
                OwnerType = GetCurrentUserType(),
                ExpiresAt = request.ExpiresAt,
                Tags = request.Tags,
                ExternalReference = request.ExternalReference,
                PreferredStorageProvider = request.PreferredStorageProvider
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to upload document");
            return StatusCode(500, "Failed to upload document");
        }
    }

    /// <summary>
    /// Get document by ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<DocumentDto>> GetDocument(Guid id, [FromQuery] bool includeVersions = false)
    {
        try
        {
            var userId = GetCurrentUserId();
            
            var query = new GetDocumentByIdQuery
            {
                DocumentId = id,
                UserId = userId,
                IncludeVersions = includeVersions
            };

            var result = await _mediator.Send(query);
            
            if (result == null)
                return NotFound();

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get document {DocumentId}", id);
            return StatusCode(500, "Failed to retrieve document");
        }
    }

    /// <summary>
    /// Search documents
    /// </summary>
    [HttpGet("search")]
    public async Task<ActionResult<DocumentSearchResultDto>> SearchDocuments([FromQuery] SearchDocumentsRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            
            var query = new SearchDocumentsQuery
            {
                SearchTerm = request.SearchTerm,
                DocumentType = request.DocumentType,
                Category = request.Category,
                Status = request.Status,
                OwnerId = request.OwnerId,
                OwnerType = request.OwnerType,
                CreatedAfter = request.CreatedAfter,
                CreatedBefore = request.CreatedBefore,
                PageNumber = request.PageNumber,
                PageSize = request.PageSize,
                RequestingUserId = userId,
                SortBy = request.SortBy,
                SortDescending = request.SortDescending
            };

            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to search documents");
            return StatusCode(500, "Failed to search documents");
        }
    }

    /// <summary>
    /// Update document metadata
    /// </summary>
    [HttpPut("{id}")]
    public async Task<ActionResult<DocumentDto>> UpdateDocument(Guid id, [FromBody] UpdateDocumentRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            
            var command = new UpdateDocumentCommand
            {
                DocumentId = id,
                Title = request.Title,
                Description = request.Description,
                Tags = request.Tags,
                UserId = userId
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update document {DocumentId}", id);
            return StatusCode(500, "Failed to update document");
        }
    }

    /// <summary>
    /// Change document status
    /// </summary>
    [HttpPatch("{id}/status")]
    public async Task<ActionResult<DocumentDto>> ChangeDocumentStatus(Guid id, [FromBody] ChangeDocumentStatusRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            
            var command = new ChangeDocumentStatusCommand
            {
                DocumentId = id,
                NewStatus = request.NewStatus,
                Reason = request.Reason,
                UserId = userId
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to change document status {DocumentId}", id);
            return StatusCode(500, "Failed to change document status");
        }
    }

    /// <summary>
    /// Grant access to document
    /// </summary>
    [HttpPost("{id}/access")]
    public async Task<ActionResult> GrantDocumentAccess(Guid id, [FromBody] GrantDocumentAccessRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            
            var command = new GrantDocumentAccessCommand
            {
                DocumentId = id,
                TargetUserId = request.TargetUserId,
                TargetUserType = request.TargetUserType,
                AccessLevel = request.AccessLevel,
                ExpiresAt = request.ExpiresAt,
                RequestingUserId = userId
            };

            await _mediator.Send(command);
            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to grant document access {DocumentId}", id);
            return StatusCode(500, "Failed to grant document access");
        }
    }

    /// <summary>
    /// Get document download URL
    /// </summary>
    [HttpGet("{id}/download")]
    public async Task<ActionResult<string>> GetDocumentDownloadUrl(Guid id, [FromQuery] int? versionNumber = null, [FromQuery] int expiryHours = 1)
    {
        try
        {
            var userId = GetCurrentUserId();
            
            var query = new GetDocumentDownloadUrlQuery
            {
                DocumentId = id,
                VersionNumber = versionNumber,
                ExpiryDuration = TimeSpan.FromHours(expiryHours),
                UserId = userId
            };

            var result = await _mediator.Send(query);
            return Ok(new { downloadUrl = result });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get download URL for document {DocumentId}", id);
            return StatusCode(500, "Failed to get download URL");
        }
    }

    /// <summary>
    /// Delete document
    /// </summary>
    [HttpDelete("{id}")]
    public async Task<ActionResult> DeleteDocument(Guid id, [FromQuery] bool permanent = false)
    {
        try
        {
            var userId = GetCurrentUserId();
            
            var command = new DeleteDocumentCommand
            {
                DocumentId = id,
                UserId = userId,
                PermanentDelete = permanent
            };

            await _mediator.Send(command);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete document {DocumentId}", id);
            return StatusCode(500, "Failed to delete document");
        }
    }

    /// <summary>
    /// Get document analytics
    /// </summary>
    [HttpGet("analytics")]
    public async Task<ActionResult<DocumentAnalyticsDto>> GetDocumentAnalytics([FromQuery] GetDocumentAnalyticsRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            
            var query = new GetDocumentAnalyticsQuery
            {
                OwnerId = request.OwnerId,
                FromDate = request.FromDate,
                ToDate = request.ToDate,
                RequestingUserId = userId
            };

            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get document analytics");
            return StatusCode(500, "Failed to get document analytics");
        }
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst("sub") ?? User.FindFirst("user_id");
        if (userIdClaim != null && Guid.TryParse(userIdClaim.Value, out var userId))
        {
            return userId;
        }
        throw new UnauthorizedAccessException("User ID not found in token");
    }

    private string GetCurrentUserType()
    {
        var userTypeClaim = User.FindFirst("user_type") ?? User.FindFirst("role");
        return userTypeClaim?.Value ?? "User";
    }
}

// Request DTOs
public class UploadDocumentRequest
{
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; }
    public DocumentType DocumentType { get; set; }
    public DocumentCategory Category { get; set; }
    public AccessLevel AccessLevel { get; set; } = AccessLevel.Private;
    public IFormFile File { get; set; } = null!;
    public DateTime? ExpiresAt { get; set; }
    public Dictionary<string, string>? Tags { get; set; }
    public string? ExternalReference { get; set; }
    public StorageProvider? PreferredStorageProvider { get; set; }
}

public class SearchDocumentsRequest
{
    public string? SearchTerm { get; set; }
    public DocumentType? DocumentType { get; set; }
    public DocumentCategory? Category { get; set; }
    public DocumentStatus? Status { get; set; }
    public Guid? OwnerId { get; set; }
    public string? OwnerType { get; set; }
    public DateTime? CreatedAfter { get; set; }
    public DateTime? CreatedBefore { get; set; }
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 20;
    public string? SortBy { get; set; } = "CreatedAt";
    public bool SortDescending { get; set; } = true;
}

public class UpdateDocumentRequest
{
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; }
    public Dictionary<string, string>? Tags { get; set; }
}

public class ChangeDocumentStatusRequest
{
    public DocumentStatus NewStatus { get; set; }
    public string? Reason { get; set; }
}

public class GrantDocumentAccessRequest
{
    public Guid TargetUserId { get; set; }
    public string TargetUserType { get; set; } = string.Empty;
    public AccessLevel AccessLevel { get; set; }
    public DateTime? ExpiresAt { get; set; }
}

public class GetDocumentAnalyticsRequest
{
    public Guid? OwnerId { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
}
