using FluentValidation;

namespace UserManagement.Application.Admin.Queries.ExportKycDocuments
{
    public class ExportKycDocumentsQueryValidator : AbstractValidator<ExportKycDocumentsQuery>
    {
        public ExportKycDocumentsQueryValidator()
        {
            RuleFor(x => x.RequestedBy)
                .NotEmpty()
                .WithMessage("RequestedBy is required");

            RuleFor(x => x.RequestedByRole)
                .NotEmpty()
                .WithMessage("RequestedByRole is required");

            RuleFor(x => x.UserIds)
                .Must(userIds => userIds == null || userIds.Count <= 1000)
                .WithMessage("Maximum 1000 users can be selected for export");

            RuleFor(x => x.DocumentTypes)
                .Must(types => types == null || types.Count <= 10)
                .WithMessage("Maximum 10 document types can be selected");

            RuleFor(x => x.FromDate)
                .LessThan(x => x.ToDate)
                .When(x => x.FromDate.HasValue && x.ToDate.HasValue)
                .WithMessage("FromDate must be before ToDate");

            RuleFor(x => x.MaxDocuments)
                .GreaterThan(0)
                .LessThanOrEqualTo(50000)
                .When(x => x.MaxDocuments.HasValue)
                .WithMessage("MaxDocuments must be between 1 and 50,000");

            // Security validation - only compliance officers and admins can export
            RuleFor(x => x.RequestedByRole)
                .Must(role => role == "Admin" || role == "ComplianceOfficer")
                .WithMessage("Only Admin and ComplianceOfficer roles can export KYC documents");
        }
    }
}
