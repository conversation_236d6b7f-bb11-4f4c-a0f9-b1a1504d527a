{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "SubscriptionManagement": "Debug"}}, "ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=SubscriptionManagement_Test;Trusted_Connection=true;MultipleActiveResultSets=true", "Redis": "localhost:6379"}, "RazorPay": {"KeyId": "test_key_id", "KeySecret": "test_key_secret", "WebhookSecret": "test_webhook_secret"}, "RabbitMQ": {"HostName": "localhost", "Port": 5672, "UserName": "guest", "Password": "guest", "VirtualHost": "/", "ExchangeName": "subscription_events_test"}, "Cache": {"Redis": {"ConnectionString": "localhost:6379", "InstanceName": "SubscriptionManagement_Test", "DefaultExpiration": "00:05:00", "SlidingExpiration": "00:02:00"}, "Memory": {"SizeLimit": 52428800, "CompactionPercentage": 0.3}}, "TestSettings": {"UseInMemoryDatabase": true, "UseTestContainers": false, "EnablePerformanceTests": true, "EnableIntegrationTests": true, "TestDataSeed": true}, "PerformanceTest": {"LoadTest": {"Duration": "00:02:00", "Concurrency": 10, "RampUpDuration": "00:00:30"}, "StressTest": {"MaxConcurrency": 100, "Duration": "00:01:00"}}}