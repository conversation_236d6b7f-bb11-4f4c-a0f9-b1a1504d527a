using Shared.Domain.Common;
using MonitoringObservability.Domain.Enums;
using MonitoringObservability.Domain.Events;

namespace MonitoringObservability.Domain.Entities;

public class Incident : AggregateRoot
{
    public string Title { get; private set; }
    public string Description { get; private set; }
    public IncidentSeverity Severity { get; private set; }
    public IncidentStatus Status { get; private set; }
    public string ServiceName { get; private set; }
    public string? Component { get; private set; }
    
    // Timeline
    public DateTime CreatedAt { get; private set; }
    public DateTime? DetectedAt { get; private set; }
    public DateTime? AcknowledgedAt { get; private set; }
    public DateTime? ResolvedAt { get; private set; }
    public DateTime? ClosedAt { get; private set; }
    
    // Assignment and ownership
    public Guid CreatedByUserId { get; private set; }
    public Guid? AssignedToUserId { get; private set; }
    public string? AssignedToUserName { get; private set; }
    public Guid? ResolvedByUserId { get; private set; }
    
    // Impact and urgency
    public int ImpactLevel { get; private set; } // 1-5 scale
    public int UrgencyLevel { get; private set; } // 1-5 scale
    public string? ImpactDescription { get; private set; }
    
    // Resolution
    public string? ResolutionSummary { get; private set; }
    public string? RootCause { get; private set; }
    public string? PreventiveMeasures { get; private set; }
    
    // Metadata
    public Dictionary<string, string> Tags { get; private set; }
    public Dictionary<string, object> CustomFields { get; private set; }
    
    // Collections
    private readonly List<IncidentUpdate> _updates = new();
    private readonly List<IncidentAlert> _relatedAlerts = new();
    private readonly List<IncidentComment> _comments = new();
    
    public IReadOnlyCollection<IncidentUpdate> Updates => _updates.AsReadOnly();
    public IReadOnlyCollection<IncidentAlert> RelatedAlerts => _relatedAlerts.AsReadOnly();
    public IReadOnlyCollection<IncidentComment> Comments => _comments.AsReadOnly();

    private Incident()
    {
        Title = string.Empty;
        Description = string.Empty;
        ServiceName = string.Empty;
        Tags = new Dictionary<string, string>();
        CustomFields = new Dictionary<string, object>();
    }

    public Incident(
        string title,
        string description,
        IncidentSeverity severity,
        string serviceName,
        Guid createdByUserId,
        string? component = null,
        int impactLevel = 3,
        int urgencyLevel = 3,
        string? impactDescription = null,
        Dictionary<string, string>? tags = null)
    {
        if (string.IsNullOrWhiteSpace(title))
            throw new ArgumentException("Title cannot be empty", nameof(title));

        if (string.IsNullOrWhiteSpace(serviceName))
            throw new ArgumentException("Service name cannot be empty", nameof(serviceName));

        Id = Guid.NewGuid();
        Title = title;
        Description = description;
        Severity = severity;
        Status = IncidentStatus.Open;
        ServiceName = serviceName;
        Component = component;
        CreatedAt = DateTime.UtcNow;
        DetectedAt = DateTime.UtcNow;
        CreatedByUserId = createdByUserId;
        ImpactLevel = Math.Clamp(impactLevel, 1, 5);
        UrgencyLevel = Math.Clamp(urgencyLevel, 1, 5);
        ImpactDescription = impactDescription;
        Tags = tags ?? new Dictionary<string, string>();
        CustomFields = new Dictionary<string, object>();

        // Add initial update
        AddUpdate(IncidentStatus.Open, "Incident created", createdByUserId);

        // Raise domain event
        AddDomainEvent(new IncidentCreatedEvent(Id, Title, Severity, ServiceName));
    }

    public void UpdateStatus(IncidentStatus newStatus, string updateMessage, Guid updatedByUserId)
    {
        if (Status == newStatus)
            return;

        var oldStatus = Status;
        Status = newStatus;

        // Update timestamps based on status
        switch (newStatus)
        {
            case IncidentStatus.Investigating:
                if (!AcknowledgedAt.HasValue)
                    AcknowledgedAt = DateTime.UtcNow;
                break;
            case IncidentStatus.Resolved:
                ResolvedAt = DateTime.UtcNow;
                ResolvedByUserId = updatedByUserId;
                break;
            case IncidentStatus.Closed:
                ClosedAt = DateTime.UtcNow;
                if (!ResolvedAt.HasValue)
                {
                    ResolvedAt = DateTime.UtcNow;
                    ResolvedByUserId = updatedByUserId;
                }
                break;
        }

        // Add update
        AddUpdate(newStatus, updateMessage, updatedByUserId);

        // Raise domain event
        AddDomainEvent(new IncidentStatusChangedEvent(Id, oldStatus, newStatus, updatedByUserId));

        if (newStatus == IncidentStatus.Resolved)
        {
            var duration = ResolvedAt!.Value - CreatedAt;
            AddDomainEvent(new IncidentResolvedEvent(Id, updatedByUserId, ResolutionSummary, duration));
        }
    }

    public void Assign(Guid userId, string userName, Guid assignedByUserId)
    {
        AssignedToUserId = userId;
        AssignedToUserName = userName;

        AddUpdate(Status, $"Incident assigned to {userName}", assignedByUserId);
    }

    public void UpdateSeverity(IncidentSeverity newSeverity, string reason, Guid updatedByUserId)
    {
        if (Severity == newSeverity)
            return;

        var oldSeverity = Severity;
        Severity = newSeverity;

        AddUpdate(Status, $"Severity changed from {oldSeverity} to {newSeverity}. Reason: {reason}", updatedByUserId);
    }

    public void UpdateImpactAndUrgency(int impactLevel, int urgencyLevel, string? impactDescription, Guid updatedByUserId)
    {
        ImpactLevel = Math.Clamp(impactLevel, 1, 5);
        UrgencyLevel = Math.Clamp(urgencyLevel, 1, 5);
        ImpactDescription = impactDescription;

        AddUpdate(Status, $"Impact level: {ImpactLevel}, Urgency level: {UrgencyLevel}", updatedByUserId);
    }

    public void SetResolution(string resolutionSummary, string? rootCause, string? preventiveMeasures, Guid resolvedByUserId)
    {
        ResolutionSummary = resolutionSummary;
        RootCause = rootCause;
        PreventiveMeasures = preventiveMeasures;

        if (Status != IncidentStatus.Resolved && Status != IncidentStatus.Closed)
        {
            UpdateStatus(IncidentStatus.Resolved, "Incident resolved", resolvedByUserId);
        }
    }

    public void AddComment(Guid userId, string userName, string comment, bool isInternal = false)
    {
        if (string.IsNullOrWhiteSpace(comment))
            throw new ArgumentException("Comment cannot be empty", nameof(comment));

        var incidentComment = new IncidentComment(Id, userId, userName, comment, isInternal);
        _comments.Add(incidentComment);
    }

    public void LinkAlert(Guid alertId, string alertTitle)
    {
        var existingLink = _relatedAlerts.FirstOrDefault(a => a.AlertId == alertId);
        if (existingLink == null)
        {
            var incidentAlert = new IncidentAlert(Id, alertId, alertTitle);
            _relatedAlerts.Add(incidentAlert);
        }
    }

    public void UnlinkAlert(Guid alertId)
    {
        var alertToRemove = _relatedAlerts.FirstOrDefault(a => a.AlertId == alertId);
        if (alertToRemove != null)
        {
            _relatedAlerts.Remove(alertToRemove);
        }
    }

    public void AddCustomField(string key, object value)
    {
        CustomFields[key] = value;
    }

    public void RemoveCustomField(string key)
    {
        CustomFields.Remove(key);
    }

    public void AddTag(string key, string value)
    {
        Tags[key] = value;
    }

    public void RemoveTag(string key)
    {
        Tags.Remove(key);
    }

    public TimeSpan? GetResolutionTime()
    {
        return ResolvedAt.HasValue ? ResolvedAt.Value - CreatedAt : null;
    }

    public TimeSpan? GetTimeToAcknowledge()
    {
        return AcknowledgedAt.HasValue ? AcknowledgedAt.Value - CreatedAt : null;
    }

    public int CalculatePriority()
    {
        // Priority matrix: Impact x Urgency
        return ImpactLevel * UrgencyLevel;
    }

    private void AddUpdate(IncidentStatus status, string message, Guid updatedByUserId)
    {
        var update = new IncidentUpdate(Id, status, message, updatedByUserId);
        _updates.Add(update);
    }
}

public class IncidentUpdate : BaseEntity
{
    public Guid IncidentId { get; private set; }
    public IncidentStatus Status { get; private set; }
    public string Message { get; private set; }
    public Guid UpdatedByUserId { get; private set; }
    public DateTime UpdatedAt { get; private set; }

    private IncidentUpdate()
    {
        Message = string.Empty;
    }

    public IncidentUpdate(Guid incidentId, IncidentStatus status, string message, Guid updatedByUserId)
    {
        Id = Guid.NewGuid();
        IncidentId = incidentId;
        Status = status;
        Message = message;
        UpdatedByUserId = updatedByUserId;
        UpdatedAt = DateTime.UtcNow;
    }
}

public class IncidentAlert : BaseEntity
{
    public Guid IncidentId { get; private set; }
    public Guid AlertId { get; private set; }
    public string AlertTitle { get; private set; }
    public DateTime LinkedAt { get; private set; }

    private IncidentAlert()
    {
        AlertTitle = string.Empty;
    }

    public IncidentAlert(Guid incidentId, Guid alertId, string alertTitle)
    {
        Id = Guid.NewGuid();
        IncidentId = incidentId;
        AlertId = alertId;
        AlertTitle = alertTitle;
        LinkedAt = DateTime.UtcNow;
    }
}

public class IncidentComment : BaseEntity
{
    public Guid IncidentId { get; private set; }
    public Guid UserId { get; private set; }
    public string UserName { get; private set; }
    public string Comment { get; private set; }
    public bool IsInternal { get; private set; }
    public DateTime CreatedAt { get; private set; }

    private IncidentComment()
    {
        UserName = string.Empty;
        Comment = string.Empty;
    }

    public IncidentComment(Guid incidentId, Guid userId, string userName, string comment, bool isInternal = false)
    {
        Id = Guid.NewGuid();
        IncidentId = incidentId;
        UserId = userId;
        UserName = userName;
        Comment = comment;
        IsInternal = isInternal;
        CreatedAt = DateTime.UtcNow;
    }
}
