using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.Enums;

namespace FinancialPayment.Application.Interfaces.Repositories;

public interface IPaymentDisputeRepository
{
    Task<PaymentDispute?> GetByIdAsync(Guid id);
    Task<PaymentDispute?> GetByDisputeNumberAsync(string disputeNumber);
    Task<List<PaymentDispute>> GetByOrderIdAsync(Guid orderId);
    Task<List<PaymentDispute>> GetByInitiatorIdAsync(Guid initiatorId);
    Task<List<PaymentDispute>> GetByStatusAsync(DisputeStatus status);
    Task<List<PaymentDispute>> GetByCategoryAsync(DisputeCategory category);
    Task<List<PaymentDispute>> GetByPriorityAsync(DisputePriority priority);
    Task<List<PaymentDispute>> GetByDateRangeAsync(DateTime from, DateTime to);
    Task<PaymentDispute> AddAsync(PaymentDispute dispute);
    Task UpdateAsync(PaymentDispute dispute);
    Task DeleteAsync(Guid id);
    Task<bool> ExistsAsync(Guid id);
    Task<List<PaymentDispute>> GetOpenDisputesAsync();
    Task<List<PaymentDispute>> GetOverdueDisputesAsync();
    Task<int> GetDisputeCountByStatusAsync(DisputeStatus status);
}
