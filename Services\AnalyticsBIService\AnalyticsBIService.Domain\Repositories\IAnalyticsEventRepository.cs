using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Enums;
using Shared.Domain.Common;
using Shared.Infrastructure.Repositories;

namespace AnalyticsBIService.Domain.Repositories;

/// <summary>
/// Repository interface for AnalyticsEvent entity
/// </summary>
public interface IAnalyticsEventRepository : IRepositoryBase<AnalyticsEvent, Guid>
{
    /// <summary>
    /// Get analytics events by user ID
    /// </summary>
    Task<PagedResult<AnalyticsEvent>> GetByUserIdAsync(
        Guid userId,
        int page,
        int pageSize,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get analytics events by event type
    /// </summary>
    Task<PagedResult<AnalyticsEvent>> GetByEventTypeAsync(
        string eventType,
        int page,
        int pageSize,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get analytics events by date range
    /// </summary>
    Task<PagedResult<AnalyticsEvent>> GetByDateRangeAsync(
        DateTime startDate,
        DateTime endDate,
        int page,
        int pageSize,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get analytics events by data source
    /// </summary>
    Task<PagedResult<AnalyticsEvent>> GetByDataSourceAsync(
        DataSourceType dataSource,
        int page,
        int pageSize,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get event statistics for a date range
    /// </summary>
    Task<Dictionary<string, int>> GetEventStatisticsAsync(
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get top events by count
    /// </summary>
    Task<List<(string EventType, int Count)>> GetTopEventsAsync(
        int topCount,
        DateTime? startDate = null,
        DateTime? endDate = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get user activity events
    /// </summary>
    Task<PagedResult<AnalyticsEvent>> GetUserActivityEventsAsync(
        Guid? userId,
        DateTime? startDate,
        DateTime? endDate,
        int page,
        int pageSize,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get business transaction events
    /// </summary>
    Task<PagedResult<AnalyticsEvent>> GetBusinessTransactionEventsAsync(
        DateTime? startDate,
        DateTime? endDate,
        int page,
        int pageSize,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Bulk insert analytics events
    /// </summary>
    Task BulkInsertAsync(
        IEnumerable<AnalyticsEvent> events,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete old events based on retention policy
    /// </summary>
    Task DeleteOldEventsAsync(
        DateTime cutoffDate,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get events by date range for aggregation
    /// </summary>
    Task<List<AnalyticsEvent>> GetEventsByDateRangeAsync(
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get unprocessed events
    /// </summary>
    Task<List<AnalyticsEvent>> GetUnprocessedEventsAsync(
        int batchSize = 100,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete old processed events
    /// </summary>
    Task DeleteOldProcessedEventsAsync(
        DateTime cutoffDate,
        CancellationToken cancellationToken = default);
}
