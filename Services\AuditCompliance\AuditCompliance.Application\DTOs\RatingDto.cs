using AuditCompliance.Domain.Enums;

namespace AuditCompliance.Application.DTOs;

public class ServiceProviderRatingDto
{
    public Guid Id { get; set; }
    public Guid ShipperId { get; set; }
    public string ShipperName { get; set; } = string.Empty;
    public Guid TransportCompanyId { get; set; }
    public string TransportCompanyName { get; set; } = string.Empty;
    public Guid? OrderId { get; set; }
    public string? OrderNumber { get; set; }
    public Guid? TripId { get; set; }
    public string? TripNumber { get; set; }
    public decimal OverallRating { get; set; }
    public RatingStatus Status { get; set; }
    public string? ReviewTitle { get; set; }
    public string? ReviewComment { get; set; }
    public bool IsAnonymous { get; set; }
    public DateTime ServiceCompletedAt { get; set; }
    public DateTime? ReviewSubmittedAt { get; set; }
    public DateTime CreatedAt { get; set; }
    public List<CategoryRatingDto> CategoryRatings { get; set; } = new();
    public List<ServiceIssueDto> ReportedIssues { get; set; } = new();
}

public class CategoryRatingDto
{
    public Guid Id { get; set; }
    public RatingCategory Category { get; set; }
    public string CategoryName { get; set; } = string.Empty;
    public decimal Rating { get; set; }
    public string? Comment { get; set; }
}

public class ServiceIssueDto
{
    public Guid Id { get; set; }
    public ServiceIssueType IssueType { get; set; }
    public string IssueTypeName { get; set; } = string.Empty;
    public IssuePriority Priority { get; set; }
    public string Description { get; set; } = string.Empty;
    public string? Evidence { get; set; }
    public IssueResolutionStatus Status { get; set; }
    public DateTime ReportedAt { get; set; }
    public DateTime? ResolvedAt { get; set; }
    public string? Resolution { get; set; }
    public string? ResolvedByName { get; set; }
}

public class CreateServiceProviderRatingDto
{
    public Guid TransportCompanyId { get; set; }
    public string TransportCompanyName { get; set; } = string.Empty;
    public Guid? OrderId { get; set; }
    public string? OrderNumber { get; set; }
    public Guid? TripId { get; set; }
    public string? TripNumber { get; set; }
    public decimal OverallRating { get; set; }
    public string? ReviewTitle { get; set; }
    public string? ReviewComment { get; set; }
    public bool IsAnonymous { get; set; } = false;
    public DateTime ServiceCompletedAt { get; set; }
    public List<CreateCategoryRatingDto> CategoryRatings { get; set; } = new();
}

public class CreateCategoryRatingDto
{
    public RatingCategory Category { get; set; }
    public decimal Rating { get; set; }
    public string? Comment { get; set; }
}

public class ReportServiceIssueDto
{
    public Guid ServiceProviderRatingId { get; set; }
    public ServiceIssueType IssueType { get; set; }
    public IssuePriority Priority { get; set; }
    public string Description { get; set; } = string.Empty;
    public string? Evidence { get; set; }
}

public class PreferredProviderNetworkDto
{
    public Guid Id { get; set; }
    public Guid ShipperId { get; set; }
    public string ShipperName { get; set; } = string.Empty;
    public Guid TransportCompanyId { get; set; }
    public string TransportCompanyName { get; set; } = string.Empty;
    public decimal AverageRating { get; set; }
    public int TotalOrders { get; set; }
    public int CompletedOrders { get; set; }
    public decimal CompletionRate { get; set; }
    public DateTime FirstOrderDate { get; set; }
    public DateTime LastOrderDate { get; set; }
    public bool IsActive { get; set; }
    public string? Notes { get; set; }
    public int PreferenceRank { get; set; }
    public DateTime CreatedAt { get; set; }
}

public class RatingQueryDto
{
    public Guid? ShipperId { get; set; }
    public Guid? TransportCompanyId { get; set; }
    public RatingStatus? Status { get; set; }
    public decimal? MinRating { get; set; }
    public decimal? MaxRating { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 20;
}

public class RatingResultDto
{
    public List<ServiceProviderRatingDto> Ratings { get; set; } = new();
    public int TotalCount { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public int TotalPages { get; set; }
    public bool HasNextPage { get; set; }
    public bool HasPreviousPage { get; set; }
}

public class TransportCompanyRatingSummaryDto
{
    public Guid TransportCompanyId { get; set; }
    public string TransportCompanyName { get; set; } = string.Empty;
    public decimal AverageRating { get; set; }
    public int TotalRatings { get; set; }
    public int FiveStarRatings { get; set; }
    public int FourStarRatings { get; set; }
    public int ThreeStarRatings { get; set; }
    public int TwoStarRatings { get; set; }
    public int OneStarRatings { get; set; }
    public List<CategoryRatingSummaryDto> CategoryAverages { get; set; } = new();
    public int TotalIssuesReported { get; set; }
    public int ResolvedIssues { get; set; }
    public DateTime LastRatingDate { get; set; }
}

public class CategoryRatingSummaryDto
{
    public RatingCategory Category { get; set; }
    public string CategoryName { get; set; } = string.Empty;
    public decimal AverageRating { get; set; }
    public int TotalRatings { get; set; }
}
