using MediatR;
using FinancialPayment.Domain.ValueObjects;
using FinancialPayment.Application.DTOs;

namespace FinancialPayment.Application.Commands.CalculateCommission;

public class CalculateCommissionCommand : IRequest<Guid>
{
    public Guid OrderId { get; set; }
    public Guid BrokerId { get; set; }
    public Guid TransportCompanyId { get; set; }
    public Guid CarrierId { get; set; }
    public CreateMoneyDto OrderAmount { get; set; } = new();
    public CommissionStructureDto CommissionStructure { get; set; } = new();
    public string? Notes { get; set; }
}

public class CommissionStructureDto
{
    public string Type { get; set; } = string.Empty; // "Percentage" or "FixedAmount"
    public decimal Rate { get; set; }
    public decimal? MinimumAmount { get; set; }
    public decimal? MaximumAmount { get; set; }
    public string? Description { get; set; }
}


