using AnalyticsBIService.Domain.Entities;
using Shared.Infrastructure.Repositories;

namespace AnalyticsBIService.Domain.Repositories;

/// <summary>
/// Repository interface for order data operations
/// </summary>
public interface IOrderDataRepository : IRepositoryBase<OrderData, Guid>
{
    /// <summary>
    /// Get order data by date range
    /// </summary>
    Task<IEnumerable<OrderData>> GetByDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get order data by shipper ID
    /// </summary>
    Task<IEnumerable<OrderData>> GetByShipperIdAsync(Guid shipperId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get order data by carrier ID
    /// </summary>
    Task<IEnumerable<OrderData>> GetByCarrierIdAsync(Guid carrierId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get order data by status
    /// </summary>
    Task<IEnumerable<OrderData>> GetByStatusAsync(string status, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get order analytics summary
    /// </summary>
    Task<object> GetOrderAnalyticsSummaryAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);
}
