using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OrderManagement.Domain.Entities;

namespace OrderManagement.Infrastructure.Persistence.Configurations;

public class RfqBidConfiguration : IEntityTypeConfiguration<RfqBid>
{
    public void Configure(EntityTypeBuilder<RfqBid> builder)
    {
        builder.ToTable("RfqBids");

        builder.<PERSON>Key(b => b.Id);

        builder.Property(b => b.Id)
            .ValueGeneratedNever();

        builder.Property(b => b.RfqId)
            .IsRequired();

        builder.Property(b => b.BrokerId)
            .IsRequired();

        builder.Property(b => b.BidNumber)
            .IsRequired()
            .HasMaxLength(50);

        builder.HasIndex(b => b.BidNumber)
            .IsUnique();

        builder.Property(b => b.ProposedTerms)
            .HasMaxLength(1000);

        builder.Property(b => b.EstimatedPickupDate)
            .IsRequired();

        builder.Property(b => b.EstimatedDeliveryDate)
            .IsRequired();

        builder.Property(b => b.VehicleDetails)
            .HasMaxLength(500);

        builder.Property(b => b.DriverDetails)
            .HasMaxLength(500);

        builder.Property(b => b.AdditionalServices)
            .HasMaxLength(500);

        builder.Property(b => b.Status)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(b => b.SubmittedAt)
            .IsRequired();

        builder.Property(b => b.AcceptedAt);

        builder.Property(b => b.RejectedAt);

        builder.Property(b => b.RejectionReason)
            .HasMaxLength(500);

        builder.Property(b => b.Notes)
            .HasMaxLength(1000);

        builder.Property(b => b.IsCounterOffer)
            .IsRequired();

        builder.Property(b => b.OriginalBidId);

        // Relationships
        builder.HasOne(b => b.Rfq)
            .WithMany(r => r.Bids)
            .HasForeignKey(b => b.RfqId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(b => b.Documents)
            .WithOne(d => d.Bid)
            .HasForeignKey(d => d.BidId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(b => b.RfqId);
        builder.HasIndex(b => b.BrokerId);
        builder.HasIndex(b => b.Status);
        builder.HasIndex(b => b.SubmittedAt);
        builder.HasIndex(b => new { b.RfqId, b.BrokerId });
        builder.HasIndex(b => new { b.BrokerId, b.Status });
        builder.HasIndex(b => new { b.RfqId, b.Status });
    }
}
