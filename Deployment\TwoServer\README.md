# TLI Microservices - Two Server Deployment Guide

## Overview

This deployment guide provides comprehensive instructions for deploying TLI microservices across two Ubuntu 24 servers:

- **Server 1 (Infrastructure)**: Database, Redis, RabbitMQ, and other third-party services
- **Server 2 (Applications)**: All TLI microservices and API Gateway

## Architecture Diagram

```
┌─────────────────────────────────────────────────────────────────┐
│                        INTERNET                                 │
└─────────────────────┬───────────────────────────────────────────┘
                      │
                      │ HTTPS/HTTP Traffic
                      │
┌─────────────────────▼───────────────────────────────────────────┐
│                 SERVER 2 (Applications)                        │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                API Gateway (Port 5000)                  │   │
│  └─────────────────────┬───────────────────────────────────┘   │
│                        │                                       │
│  ┌─────────────────────▼───────────────────────────────────┐   │
│  │              TLI Microservices                          │   │
│  │  • Identity Service (5001)                              │   │
│  │  • User Management (5002)                               │   │
│  │  • Subscription Management (5003)                       │   │
│  │  • Order Management (5004)                              │   │
│  │  • Trip Management (5005)                               │   │
│  │  • Network & Fleet Management (5006)                    │   │
│  │  • Financial & Payment (5007)                           │   │
│  │  • Communication & Notification (5008)                  │   │
│  │  • Analytics & BI Service (5009)                        │   │
│  │  • Data & Storage (5010)                                │   │
│  │  • Audit & Compliance (5011)                            │   │
│  │  • Monitoring & Observability (5012)                    │   │
│  │  • Mobile Workflow (5013)                               │   │
│  └─────────────────────────────────────────────────────────┘   │
└─────────────────────┬───────────────────────────────────────────┘
                      │
                      │ Internal Network Connection
                      │
┌─────────────────────▼───────────────────────────────────────────┐
│                SERVER 1 (Infrastructure)                       │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                PostgreSQL 15                            │   │
│  │                (Port 5432)                              │   │
│  │  • TLI_Identity                                         │   │
│  │  • TLI_UserManagement                                   │   │
│  │  • TLI_SubscriptionManagement                           │   │
│  │  • TLI_OrderManagement                                  │   │
│  │  • TLI_TripManagement                                   │   │
│  │  • TLI_NetworkFleetManagement                           │   │
│  │  • TLI_FinancialPayment                                 │   │
│  │  • TLI_CommunicationNotification                        │   │
│  │  • TLI_AnalyticsBIService                               │   │
│  │  • TLI_DataStorage                                      │   │
│  │  • TLI_AuditCompliance                                  │   │
│  │  • TLI_MonitoringObservability                          │   │
│  │  • TLI_MobileWorkflow                                   │   │
│  └─────────────────────────────────────────────────────────┘   │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                Redis 7 (Port 6379)                     │   │
│  │  • Caching                                              │   │
│  │  • Session Storage                                      │   │
│  │  • Rate Limiting                                        │   │
│  └─────────────────────────────────────────────────────────┘   │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │              RabbitMQ 3.12 (Port 5672)                 │   │
│  │  • Message Broker                                       │   │
│  │  • Event Bus                                            │   │
│  │  • Management UI (Port 15672)                           │   │
│  └─────────────────────────────────────────────────────────┘   │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │              Monitoring Stack                           │   │
│  │  • Prometheus (Port 9090)                               │   │
│  │  • Grafana (Port 3000)                                  │   │
│  │  • Jaeger (Port 16686)                                  │   │
│  └─────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
```

## Documentation Structure

1. **[Server 1 Setup](./01-Server1-Infrastructure-Setup.md)** - Infrastructure services installation
2. **[Server 2 Setup](./02-Server2-Applications-Setup.md)** - Microservices deployment
3. **[Network Configuration](./03-Network-Configuration.md)** - Inter-server communication
4. **[Security Configuration](./04-Security-Configuration.md)** - SSL, firewall, and security
5. **[Testing Guide](./05-Testing-Guide.md)** - Service validation and testing
6. **[Monitoring Setup](./06-Monitoring-Setup.md)** - Observability and monitoring
7. **[Troubleshooting Guide](./07-Troubleshooting-Guide.md)** - Common issues and solutions
8. **[Maintenance Guide](./08-Maintenance-Guide.md)** - Backup, updates, and maintenance

## Quick Start

1. **Prepare Servers**: Ensure both Ubuntu 24 servers are ready with required specifications
2. **Setup Infrastructure**: Follow Server 1 setup guide to install databases and message brokers
3. **Deploy Applications**: Follow Server 2 setup guide to deploy microservices
4. **Configure Network**: Setup secure communication between servers
5. **Test Services**: Validate all services are working correctly
6. **Setup Monitoring**: Configure monitoring and alerting

## Prerequisites

- Two Ubuntu 24 servers with minimum 8GB RAM each (16GB recommended)
- Static IP addresses for both servers
- SSH access to both servers
- Domain name with SSL certificates (recommended)
- Basic knowledge of Docker and Linux administration

## Support

For issues and questions, refer to the troubleshooting guide or contact the development team.
