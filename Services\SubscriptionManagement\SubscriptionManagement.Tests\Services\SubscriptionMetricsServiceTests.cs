using Microsoft.Extensions.Logging;
using Moq;
using SubscriptionManagement.Infrastructure.Services;
using System.Diagnostics.Metrics;
using Xunit;

namespace SubscriptionManagement.Tests.Services
{
    public class SubscriptionMetricsServiceTests : IDisposable
    {
        private readonly Mock<ILogger<SubscriptionMetricsService>> _loggerMock;
        private readonly SubscriptionMetricsService _metricsService;
        private readonly MeterListener _meterListener;
        private readonly List<KeyValuePair<string, object?>> _recordedMeasurements;

        public SubscriptionMetricsServiceTests()
        {
            _loggerMock = new Mock<ILogger<SubscriptionMetricsService>>();
            _metricsService = new SubscriptionMetricsService(_loggerMock.Object);
            _recordedMeasurements = new List<KeyValuePair<string, object?>>();

            // Set up meter listener to capture metrics
            _meterListener = new MeterListener();
            _meterListener.InstrumentPublished = (instrument, listener) =>
            {
                if (instrument.Meter.Name == "SubscriptionManagement")
                {
                    listener.EnableMeasurementEvents(instrument, null);
                }
            };

            _meterListener.SetMeasurementEventCallback<long>((instrument, measurement, tags, state) =>
            {
                _recordedMeasurements.Add(new KeyValuePair<string, object?>(instrument.Name, measurement));
            });

            _meterListener.SetMeasurementEventCallback<double>((instrument, measurement, tags, state) =>
            {
                _recordedMeasurements.Add(new KeyValuePair<string, object?>(instrument.Name, measurement));
            });

            _meterListener.Start();
        }

        [Fact]
        public void RecordSubscriptionCreated_ShouldRecordMetric()
        {
            // Arrange
            var planName = "Premium Plan";
            var amount = 99.99m;
            var currency = "USD";

            // Act
            _metricsService.RecordSubscriptionCreated(planName, amount, currency);

            // Assert
            Assert.Contains(_recordedMeasurements, m => m.Key == "subscription_created_total" && (long)m.Value! == 1);
            Assert.Contains(_recordedMeasurements, m => m.Key == "subscription_amount" && (double)m.Value! == (double)amount);
        }

        [Fact]
        public void RecordSubscriptionUpgraded_ShouldRecordMetric()
        {
            // Arrange
            var fromPlan = "Basic Plan";
            var toPlan = "Premium Plan";
            var amount = 50.00m;

            // Act
            _metricsService.RecordSubscriptionUpgraded(fromPlan, toPlan, amount);

            // Assert
            Assert.Contains(_recordedMeasurements, m => m.Key == "subscription_upgraded_total" && (long)m.Value! == 1);
        }

        [Fact]
        public void RecordSubscriptionDowngraded_ShouldRecordMetric()
        {
            // Arrange
            var fromPlan = "Premium Plan";
            var toPlan = "Basic Plan";
            var amount = 25.00m;

            // Act
            _metricsService.RecordSubscriptionDowngraded(fromPlan, toPlan, amount);

            // Assert
            Assert.Contains(_recordedMeasurements, m => m.Key == "subscription_downgraded_total" && (long)m.Value! == 1);
        }

        [Fact]
        public void RecordSubscriptionCancelled_ShouldRecordMetric()
        {
            // Arrange
            var planName = "Premium Plan";
            var reason = "User requested cancellation";

            // Act
            _metricsService.RecordSubscriptionCancelled(planName, reason);

            // Assert
            Assert.Contains(_recordedMeasurements, m => m.Key == "subscription_cancelled_total" && (long)m.Value! == 1);
        }

        [Fact]
        public void RecordPaymentSuccess_ShouldRecordMetric()
        {
            // Arrange
            var amount = 99.99m;
            var currency = "USD";
            var paymentMethod = "credit_card";

            // Act
            _metricsService.RecordPaymentSuccess(amount, currency, paymentMethod);

            // Assert
            Assert.Contains(_recordedMeasurements, m => m.Key == "payment_success_total" && (long)m.Value! == 1);
            Assert.Contains(_recordedMeasurements, m => m.Key == "payment_amount" && (double)m.Value! == (double)amount);
        }

        [Fact]
        public void RecordPaymentFailure_ShouldRecordMetric()
        {
            // Arrange
            var amount = 99.99m;
            var currency = "USD";
            var paymentMethod = "credit_card";
            var errorCode = "insufficient_funds";

            // Act
            _metricsService.RecordPaymentFailure(amount, currency, paymentMethod, errorCode);

            // Assert
            Assert.Contains(_recordedMeasurements, m => m.Key == "payment_failure_total" && (long)m.Value! == 1);
        }

        [Fact]
        public void RecordFeatureUsage_ShouldRecordMetric()
        {
            // Arrange
            var featureType = "api_calls";
            var count = 5;
            var userId = Guid.NewGuid();

            // Act
            _metricsService.RecordFeatureUsage(featureType, count, userId);

            // Assert
            Assert.Contains(_recordedMeasurements, m => m.Key == "feature_usage_total" && (long)m.Value! == count);
        }

        [Fact]
        public void RecordUsageLimitReached_ShouldRecordMetric()
        {
            // Arrange
            var featureType = "api_calls";
            var userId = Guid.NewGuid();

            // Act
            _metricsService.RecordUsageLimitReached(featureType, userId);

            // Assert
            Assert.Contains(_recordedMeasurements, m => m.Key == "usage_limit_reached_total" && (long)m.Value! == 1);
        }

        [Fact]
        public void RecordApiCall_ShouldRecordMetric()
        {
            // Arrange
            var endpoint = "/api/subscriptions";
            var duration = TimeSpan.FromMilliseconds(150);
            var success = true;

            // Act
            _metricsService.RecordApiCall(endpoint, duration, success);

            // Assert
            Assert.Contains(_recordedMeasurements, m => m.Key == "api_calls_total" && (long)m.Value! == 1);
            Assert.Contains(_recordedMeasurements, m => m.Key == "api_duration_seconds" && Math.Abs((double)m.Value! - duration.TotalSeconds) < 0.001);
        }

        [Fact]
        public void RecordCacheHit_ShouldRecordMetric()
        {
            // Arrange
            var cacheType = "plan";

            // Act
            _metricsService.RecordCacheHit(cacheType);

            // Assert
            Assert.Contains(_recordedMeasurements, m => m.Key == "cache_hits_total" && (long)m.Value! == 1);
        }

        [Fact]
        public void RecordCacheMiss_ShouldRecordMetric()
        {
            // Arrange
            var cacheType = "subscription";

            // Act
            _metricsService.RecordCacheMiss(cacheType);

            // Assert
            Assert.Contains(_recordedMeasurements, m => m.Key == "cache_misses_total" && (long)m.Value! == 1);
        }

        [Fact]
        public void RecordFeatureFlagEvaluation_ShouldRecordMetric()
        {
            // Arrange
            var flagKey = "new_feature";
            var variant = "enabled";
            var userId = Guid.NewGuid();

            // Act
            _metricsService.RecordFeatureFlagEvaluation(flagKey, variant, userId);

            // Assert
            Assert.Contains(_recordedMeasurements, m => m.Key == "feature_flag_evaluations_total" && (long)m.Value! == 1);
        }

        [Fact]
        public void RecordHealthCheck_ShouldRecordMetric()
        {
            // Arrange
            var component = "database";
            var healthy = true;
            var responseTime = TimeSpan.FromMilliseconds(50);

            // Act
            _metricsService.RecordHealthCheck(component, healthy, responseTime);

            // Assert
            Assert.Contains(_recordedMeasurements, m => m.Key == "health_checks_total" && (long)m.Value! == 1);
        }

        [Fact]
        public void RecordMonthlyRecurringRevenue_ShouldUpdateGauge()
        {
            // Arrange
            var amount = 50000m;
            var currency = "USD";

            // Act
            _metricsService.RecordMonthlyRecurringRevenue(amount, currency);

            // Assert - Gauge values are not captured by the listener in the same way
            // We verify the method doesn't throw and logs appropriately
            _loggerMock.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Updated MRR")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Fact]
        public void RecordChurnRate_ShouldUpdateGauge()
        {
            // Arrange
            var rate = 0.05; // 5%

            // Act
            _metricsService.RecordChurnRate(rate);

            // Assert
            _loggerMock.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Updated churn rate")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Fact]
        public void MultipleMetrics_ShouldRecordAllCorrectly()
        {
            // Arrange & Act
            _metricsService.RecordSubscriptionCreated("Plan A", 10m, "USD");
            _metricsService.RecordSubscriptionCreated("Plan B", 20m, "USD");
            _metricsService.RecordPaymentSuccess(30m, "USD", "credit_card");
            _metricsService.RecordFeatureUsage("api_calls", 3, Guid.NewGuid());

            // Assert
            var subscriptionCreatedCount = _recordedMeasurements.Count(m => m.Key == "subscription_created_total");
            var paymentSuccessCount = _recordedMeasurements.Count(m => m.Key == "payment_success_total");
            var featureUsageCount = _recordedMeasurements.Count(m => m.Key == "feature_usage_total");

            Assert.Equal(2, subscriptionCreatedCount);
            Assert.Equal(1, paymentSuccessCount);
            Assert.Equal(1, featureUsageCount);

            // Verify feature usage count is correct
            var featureUsageMeasurement = _recordedMeasurements.First(m => m.Key == "feature_usage_total");
            Assert.Equal(3L, featureUsageMeasurement.Value);
        }

        public void Dispose()
        {
            _meterListener?.Dispose();
            _metricsService?.Dispose();
        }
    }
}
