using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using MobileWorkflow.Application.DTOs;
using MobileWorkflow.Domain.Entities;
using MobileWorkflow.Domain.Repositories;

namespace MobileWorkflow.Application.Commands.Milestone;

public class CreateMilestoneTemplateCommandHandler : IRequestHandler<CreateMilestoneTemplateCommand, MilestoneTemplateDto>
{
    private readonly IMilestoneTemplateRepository _templateRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<CreateMilestoneTemplateCommandHandler> _logger;

    public CreateMilestoneTemplateCommandHandler(
        IMilestoneTemplateRepository templateRepository,
        IMapper mapper,
        ILogger<CreateMilestoneTemplateCommandHandler> logger)
    {
        _templateRepository = templateRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<MilestoneTemplateDto> Handle(CreateMilestoneTemplateCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Creating milestone template: {Name} by {CreatedBy}", request.Name, request.CreatedBy);

            // Check if template with same name already exists
            var existingTemplate = await _templateRepository.GetByNameAsync(request.Name, cancellationToken);
            if (existingTemplate != null)
            {
                throw new InvalidOperationException($"Milestone template with name '{request.Name}' already exists");
            }

            // Create the milestone template
            var template = new MilestoneTemplate(
                request.Name,
                request.Description,
                request.Type,
                request.Category,
                request.CreatedBy);

            // Update configuration and metadata
            if (request.Configuration.Any())
            {
                template.UpdateConfiguration(request.Configuration, request.CreatedBy);
            }

            foreach (var kvp in request.Metadata)
            {
                template.AddMetadata(kvp.Key, kvp.Value);
            }

            // Add steps if provided
            foreach (var stepRequest in request.Steps.OrderBy(s => s.SequenceNumber))
            {
                var step = template.AddStep(
                    stepRequest.Name,
                    stepRequest.Description,
                    stepRequest.SequenceNumber,
                    stepRequest.IsRequired);

                // Update step configuration and metadata
                if (stepRequest.Configuration.Any())
                {
                    step.UpdateConfiguration(stepRequest.Configuration);
                }

                foreach (var kvp in stepRequest.Metadata)
                {
                    step.AddMetadata(kvp.Key, kvp.Value);
                }

                // Set trigger condition if provided
                if (!string.IsNullOrEmpty(stepRequest.TriggerCondition))
                {
                    step.SetTriggerCondition(stepRequest.TriggerCondition);
                }

                // Add payout rules
                foreach (var payoutRequest in stepRequest.PayoutRules)
                {
                    var payoutRule = step.AddPayoutRule(
                        payoutRequest.PayoutPercentage,
                        payoutRequest.TriggerCondition);

                    if (!string.IsNullOrEmpty(payoutRequest.Description))
                    {
                        payoutRule.UpdateDescription(payoutRequest.Description);
                    }

                    if (payoutRequest.Configuration.Any())
                    {
                        payoutRule.UpdateConfiguration(payoutRequest.Configuration);
                    }

                    foreach (var kvp in payoutRequest.Metadata)
                    {
                        payoutRule.AddMetadata(kvp.Key, kvp.Value);
                    }
                }
            }

            // Validate the template before saving
            var validationErrors = template.GetValidationErrors();
            if (validationErrors.Any())
            {
                throw new InvalidOperationException($"Template validation failed: {string.Join(", ", validationErrors)}");
            }

            // Save the template
            var savedTemplate = await _templateRepository.AddAsync(template, cancellationToken);

            _logger.LogInformation("Milestone template created successfully with ID: {TemplateId}", savedTemplate.Id);

            // Map to DTO and return
            var templateDto = _mapper.Map<MilestoneTemplateDto>(savedTemplate);
            templateDto.TotalPayoutPercentage = savedTemplate.Steps
                .SelectMany(s => s.PayoutRules)
                .Sum(r => r.PayoutPercentage);
            templateDto.IsValid = !validationErrors.Any();
            templateDto.ValidationErrors = validationErrors;

            return templateDto;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating milestone template: {Name}", request.Name);
            throw;
        }
    }
}
