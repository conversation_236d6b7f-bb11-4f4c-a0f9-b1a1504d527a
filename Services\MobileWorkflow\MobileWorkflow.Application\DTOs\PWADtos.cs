namespace MobileWorkflow.Application.DTOs;

public class PWAInstallationDto
{
    public Guid Id { get; set; }
    public Guid UserId { get; set; }
    public string DeviceId { get; set; } = string.Empty;
    public string Platform { get; set; } = string.Empty;
    public string UserAgent { get; set; } = string.Empty;
    public string AppVersion { get; set; } = string.Empty;
    public DateTime InstalledAt { get; set; }
    public DateTime? LastActiveAt { get; set; }
    public bool IsActive { get; set; }
    public Dictionary<string, object> InstallationData { get; set; } = new();
    public Dictionary<string, object> DeviceCapabilities { get; set; } = new();
    public TimeSpan? TimeSinceLastActive { get; set; }
}

public class PWASessionDto
{
    public Guid Id { get; set; }
    public Guid PWAInstallationId { get; set; }
    public Guid UserId { get; set; }
    public DateTime StartedAt { get; set; }
    public DateTime? EndedAt { get; set; }
    public TimeSpan? Duration { get; set; }
    public string SessionType { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public Dictionary<string, object> SessionData { get; set; } = new();
    public Dictionary<string, object> PerformanceMetrics { get; set; } = new();
}

public class ServiceWorkerEventDto
{
    public Guid Id { get; set; }
    public Guid PWAInstallationId { get; set; }
    public string EventType { get; set; } = string.Empty;
    public DateTime EventTime { get; set; }
    public string? EventData { get; set; }
    public bool IsSuccessful { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object> EventContext { get; set; } = new();
    public Dictionary<string, object> PerformanceData { get; set; } = new();
}

public class PWAManifestDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string ShortName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string StartUrl { get; set; } = string.Empty;
    public string Display { get; set; } = string.Empty;
    public string Orientation { get; set; } = string.Empty;
    public string ThemeColor { get; set; } = string.Empty;
    public string BackgroundColor { get; set; } = string.Empty;
    public string Scope { get; set; } = string.Empty;
    public List<PWAIconDto> Icons { get; set; } = new();
    public Dictionary<string, object> ManifestData { get; set; } = new();
    public string Version { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public string? UpdatedBy { get; set; }
}

public class PWAIconDto
{
    public string Src { get; set; } = string.Empty;
    public string Sizes { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Purpose { get; set; } = "any";
}

public class OfflineCacheDto
{
    public Guid Id { get; set; }
    public string CacheName { get; set; } = string.Empty;
    public string CacheVersion { get; set; } = string.Empty;
    public List<string> CachedUrls { get; set; } = new();
    public long TotalSize { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? LastAccessedAt { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public string CacheStrategy { get; set; } = string.Empty;
    public Dictionary<string, object> CacheMetadata { get; set; } = new();
    public bool IsActive { get; set; }
    public bool IsExpired { get; set; }
}

public class PWAAnalyticsDto
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public int TotalInstallations { get; set; }
    public int ActiveInstallations { get; set; }
    public int TotalSessions { get; set; }
    public int ActiveSessions { get; set; }
    public double AverageSessionDuration { get; set; }
    public Dictionary<string, int> InstallationsByPlatform { get; set; } = new();
    public Dictionary<string, int> SessionsByType { get; set; } = new();
    public Dictionary<string, int> ServiceWorkerEventsByType { get; set; } = new();
    public Dictionary<DateTime, int> DailyActiveUsers { get; set; } = new();
    public Dictionary<string, double> PerformanceMetrics { get; set; } = new();
}

// Request DTOs
public class RegisterPWAInstallationRequest
{
    public Guid UserId { get; set; }
    public string DeviceId { get; set; } = string.Empty;
    public string Platform { get; set; } = string.Empty;
    public string UserAgent { get; set; } = string.Empty;
    public string AppVersion { get; set; } = string.Empty;
    public Dictionary<string, object> InstallationData { get; set; } = new();
    public Dictionary<string, object> DeviceCapabilities { get; set; } = new();
}

public class UpdatePWAInstallationRequest
{
    public Dictionary<string, object>? DeviceCapabilities { get; set; }
    public bool? IsActive { get; set; }
}

public class StartPWASessionRequest
{
    public Guid InstallationId { get; set; }
    public string SessionType { get; set; } = string.Empty;
    public Dictionary<string, object>? SessionData { get; set; }
}

public class RecordServiceWorkerEventRequest
{
    public Guid InstallationId { get; set; }
    public string EventType { get; set; } = string.Empty;
    public Dictionary<string, object> EventContext { get; set; } = new();
    public bool IsSuccessful { get; set; } = true;
    public string? EventData { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object>? PerformanceData { get; set; }
}

public class CreatePWAManifestRequest
{
    public string Name { get; set; } = string.Empty;
    public string ShortName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string StartUrl { get; set; } = string.Empty;
    public string Display { get; set; } = "standalone";
    public string ThemeColor { get; set; } = string.Empty;
    public string BackgroundColor { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public string CreatedBy { get; set; } = string.Empty;
    public string? Scope { get; set; }
    public string? Orientation { get; set; }
    public List<PWAIconDto>? Icons { get; set; }
    public Dictionary<string, object>? ManifestData { get; set; }
}

public class UpdatePWAManifestRequest
{
    public string? Name { get; set; }
    public string? ShortName { get; set; }
    public string? Description { get; set; }
    public string? StartUrl { get; set; }
    public string? Display { get; set; }
    public string? ThemeColor { get; set; }
    public string? BackgroundColor { get; set; }
    public string? Scope { get; set; }
    public string? Orientation { get; set; }
    public bool? IsActive { get; set; }
    public string UpdatedBy { get; set; } = string.Empty;
}

public class CreateOfflineCacheRequest
{
    public string CacheName { get; set; } = string.Empty;
    public string CacheVersion { get; set; } = string.Empty;
    public string CacheStrategy { get; set; } = string.Empty;
    public List<string>? InitialUrls { get; set; }
    public DateTime? ExpiresAt { get; set; }
}

public class UpdateOfflineCacheRequest
{
    public List<string>? UrlsToAdd { get; set; }
    public List<string>? UrlsToRemove { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public bool? IsActive { get; set; }
}

public class PWACapabilitiesDto
{
    public bool SupportsServiceWorker { get; set; }
    public bool SupportsWebManifest { get; set; }
    public bool SupportsPushNotifications { get; set; }
    public bool SupportsBackgroundSync { get; set; }
    public bool SupportsOfflineStorage { get; set; }
    public bool SupportsInstallPrompt { get; set; }
    public bool SupportsFullscreen { get; set; }
    public bool SupportsStandalone { get; set; }
    public bool SupportsWebShare { get; set; }
    public bool SupportsWebPayments { get; set; }
    public bool SupportsGeolocation { get; set; }
    public bool SupportsCamera { get; set; }
    public bool SupportsBiometrics { get; set; }
    public Dictionary<string, object> AdditionalCapabilities { get; set; } = new();
}

public class PWAPerformanceDto
{
    public Guid InstallationId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public double AverageLoadTime { get; set; }
    public double AverageResponseTime { get; set; }
    public double CacheHitRatio { get; set; }
    public long TotalDataTransferred { get; set; }
    public long CachedDataServed { get; set; }
    public int TotalRequests { get; set; }
    public int CachedRequests { get; set; }
    public int FailedRequests { get; set; }
    public Dictionary<string, double> PerformanceMetrics { get; set; } = new();
}

public class PWAUpdateDto
{
    public Guid Id { get; set; }
    public string FromVersion { get; set; } = string.Empty;
    public string ToVersion { get; set; } = string.Empty;
    public DateTime UpdatedAt { get; set; }
    public string UpdateType { get; set; } = string.Empty; // Manifest, ServiceWorker, Cache
    public bool IsSuccessful { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object> UpdateDetails { get; set; } = new();
}

public class PWAOfflineStatusDto
{
    public Guid InstallationId { get; set; }
    public bool IsOnline { get; set; }
    public DateTime LastOnlineAt { get; set; }
    public DateTime? LastOfflineAt { get; set; }
    public TimeSpan? OfflineDuration { get; set; }
    public int OfflineRequestsQueued { get; set; }
    public int OfflineRequestsProcessed { get; set; }
    public Dictionary<string, object> OfflineData { get; set; } = new();
}

public class PWAInstallPromptDto
{
    public Guid InstallationId { get; set; }
    public DateTime PromptShownAt { get; set; }
    public string PromptResult { get; set; } = string.Empty; // Accepted, Dismissed, Ignored
    public string PromptTrigger { get; set; } = string.Empty; // Manual, Automatic, Criteria
    public Dictionary<string, object> PromptContext { get; set; } = new();
}

public class PWAEngagementDto
{
    public Guid UserId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public int TotalSessions { get; set; }
    public double TotalSessionTime { get; set; }
    public double AverageSessionTime { get; set; }
    public int DaysActive { get; set; }
    public double EngagementScore { get; set; }
    public Dictionary<string, int> FeatureUsage { get; set; } = new();
    public Dictionary<DateTime, double> DailyEngagement { get; set; } = new();
}

public class PWAHealthCheckDto
{
    public string Component { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty; // Healthy, Warning, Critical
    public string? Message { get; set; }
    public Dictionary<string, object> HealthMetrics { get; set; } = new();
    public DateTime CheckedAt { get; set; }
    public TimeSpan ResponseTime { get; set; }
    public List<string> Issues { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
}
