using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetworkFleetManagement.Domain.Entities;
using NetworkFleetManagement.Domain.ValueObjects;

namespace NetworkFleetManagement.Infrastructure.Persistence.Configurations;

public class CarrierConfiguration : IEntityTypeConfiguration<Carrier>
{
    public void Configure(EntityTypeBuilder<Carrier> builder)
    {
        builder.ToTable("carriers");

        builder.HasKey(c => c.Id);

        builder.Property(c => c.Id)
            .ValueGeneratedNever();

        builder.Property(c => c.UserId)
            .IsRequired();

        builder.Property(c => c.CompanyName)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(c => c.ContactPersonName)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(c => c.Email)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(c => c.PhoneNumber)
            .IsRequired()
            .HasMaxLength(20);

        builder.Property(c => c.BusinessLicenseNumber)
            .HasMaxLength(50);

        builder.Property(c => c.TaxIdentificationNumber)
            .HasMaxLength(50);

        builder.Property(c => c.Status)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(c => c.OnboardingStatus)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(c => c.Notes)
            .HasMaxLength(1000);

        builder.Property(c => c.CreatedAt)
            .IsRequired();

        builder.Property(c => c.UpdatedAt);

        builder.Property(c => c.VerifiedAt);

        // Configure PerformanceMetrics as owned entity
        builder.OwnsOne(c => c.PerformanceMetrics, pm =>
        {
            pm.Property(p => p.Rating)
                .HasColumnName("performance_rating")
                .HasPrecision(3, 2);

            pm.Property(p => p.TotalTrips)
                .HasColumnName("performance_total_trips");

            pm.Property(p => p.CompletedTrips)
                .HasColumnName("performance_completed_trips");

            pm.Property(p => p.CancelledTrips)
                .HasColumnName("performance_cancelled_trips");

            pm.Property(p => p.OnTimePercentage)
                .HasColumnName("performance_on_time_percentage")
                .HasPrecision(5, 2);

            pm.Property(p => p.CustomerSatisfactionScore)
                .HasColumnName("performance_customer_satisfaction")
                .HasPrecision(3, 2);

            pm.Property(p => p.LastUpdated)
                .HasColumnName("performance_last_updated");
        });

        // Configure BusinessAddress as owned entity
        builder.OwnsOne(c => c.BusinessAddress, ba =>
        {
            ba.Property(l => l.Latitude)
                .HasColumnName("business_latitude")
                .HasPrecision(10, 8);

            ba.Property(l => l.Longitude)
                .HasColumnName("business_longitude")
                .HasPrecision(11, 8);

            ba.Property(l => l.Address)
                .HasColumnName("business_address")
                .HasMaxLength(500);

            ba.Property(l => l.City)
                .HasColumnName("business_city")
                .HasMaxLength(100);

            ba.Property(l => l.State)
                .HasColumnName("business_state")
                .HasMaxLength(100);

            ba.Property(l => l.Country)
                .HasColumnName("business_country")
                .HasMaxLength(100);

            ba.Property(l => l.PostalCode)
                .HasColumnName("business_postal_code")
                .HasMaxLength(20);

            ba.Property(l => l.Timestamp)
                .HasColumnName("business_address_timestamp");
        });

        // Configure relationships
        builder.HasMany(c => c.Vehicles)
            .WithOne(v => v.Carrier)
            .HasForeignKey(v => v.CarrierId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(c => c.Drivers)
            .WithOne(d => d.Carrier)
            .HasForeignKey(d => d.CarrierId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(c => c.Documents)
            .WithOne(d => d.Carrier)
            .HasForeignKey(d => d.CarrierId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(c => c.NetworkRelationships)
            .WithOne(n => n.Carrier)
            .HasForeignKey(n => n.CarrierId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(c => c.UserId)
            .IsUnique();

        builder.HasIndex(c => c.Email);

        builder.HasIndex(c => c.Status);

        builder.HasIndex(c => c.OnboardingStatus);

        builder.HasIndex(c => c.CompanyName);
    }
}
