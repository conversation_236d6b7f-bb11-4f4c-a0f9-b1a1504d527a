using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Entities;
using NetworkFleetManagement.Domain.Repositories;

namespace NetworkFleetManagement.Application.Queries.PreferredPartners;

public class GetPartnerRecommendationsQueryHandler : IRequestHandler<GetPartnerRecommendationsQuery, List<PartnerRecommendationDto>>
{
    private readonly IPreferredPartnerRepository _preferredPartnerRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetPartnerRecommendationsQueryHandler> _logger;

    public GetPartnerRecommendationsQueryHandler(
        IPreferredPartnerRepository preferredPartnerRepository,
        IMapper mapper,
        ILogger<GetPartnerRecommendationsQueryHandler> logger)
    {
        _preferredPartnerRepository = preferredPartnerRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<List<PartnerRecommendationDto>> Handle(GetPartnerRecommendationsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var recommendations = new List<PartnerRecommendationDto>();

            // Get existing preferred partners to avoid recommending duplicates
            var existingPartners = await _preferredPartnerRepository.GetActiveByUserIdAsync(request.UserId, cancellationToken);
            var existingPartnerIds = existingPartners.Select(p => p.PartnerId).ToHashSet();

            // This would typically integrate with other services to find potential partners
            // For now, generate mock recommendations based on user role and criteria
            var random = new Random();
            var partnerTypeToRecommend = GetPartnerTypeForUserRole(request.UserRole);

            for (int i = 0; i < request.Limit; i++)
            {
                var partnerId = Guid.NewGuid();
                
                // Skip if already a preferred partner
                if (existingPartnerIds.Contains(partnerId))
                    continue;

                var score = (decimal)(random.NextDouble() * 40 + 60); // Score between 60-100
                var matchingCriteria = GetMatchingCriteria(request, random);

                recommendations.Add(new PartnerRecommendationDto
                {
                    PartnerId = partnerId,
                    PartnerName = $"Recommended {partnerTypeToRecommend} {i + 1}",
                    PartnerType = partnerTypeToRecommend,
                    RecommendationScore = score,
                    RecommendationReason = GetRecommendationReason(score, matchingCriteria),
                    MatchingCriteria = matchingCriteria,
                    PerformanceMetrics = GeneratePerformanceMetrics(random),
                    IsCurrentlyPreferred = false,
                    ContactInfo = $"contact{i + 1}@{partnerTypeToRecommend.ToLower()}.com"
                });
            }

            return recommendations.OrderByDescending(r => r.RecommendationScore).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating partner recommendations for user {UserId}", request.UserId);
            throw;
        }
    }

    private string GetPartnerTypeForUserRole(string userRole)
    {
        return userRole.ToLower() switch
        {
            var role when role.Contains("broker") => "Carrier",
            var role when role.Contains("transporter") || role.Contains("carrier") => "Broker",
            var role when role.Contains("shipper") => "Transporter",
            _ => "Partner"
        };
    }

    private List<string> GetMatchingCriteria(GetPartnerRecommendationsQuery request, Random random)
    {
        var criteria = new List<string>();

        if (!string.IsNullOrEmpty(request.Route))
            criteria.Add($"Operates on {request.Route} route");

        if (!string.IsNullOrEmpty(request.LoadType))
            criteria.Add($"Specializes in {request.LoadType}");

        // Add some random criteria
        var possibleCriteria = new[]
        {
            "High performance rating",
            "Competitive pricing",
            "Excellent on-time delivery",
            "Strong customer reviews",
            "Advanced tracking capabilities",
            "Flexible scheduling",
            "Insurance coverage",
            "Environmental compliance"
        };

        var additionalCount = random.Next(2, 5);
        var selectedCriteria = possibleCriteria.OrderBy(x => random.Next()).Take(additionalCount);
        criteria.AddRange(selectedCriteria);

        return criteria;
    }

    private string GetRecommendationReason(decimal score, List<string> criteria)
    {
        if (score >= 90)
            return $"Excellent match based on {criteria.Count} criteria including {string.Join(", ", criteria.Take(2))}";
        else if (score >= 80)
            return $"Good match with strong performance in {string.Join(" and ", criteria.Take(2))}";
        else if (score >= 70)
            return $"Suitable partner with {string.Join(", ", criteria.Take(1))}";
        else
            return "Potential partner worth considering";
    }

    private PartnerPerformanceMetricsDto GeneratePerformanceMetrics(Random random)
    {
        return new PartnerPerformanceMetricsDto
        {
            OverallRating = (decimal)(random.NextDouble() * 2 + 3), // 3-5 rating
            OnTimePerformance = (decimal)(random.NextDouble() * 0.3 + 0.7), // 70-100%
            QualityScore = (decimal)(random.NextDouble() * 2 + 3), // 3-5 rating
            CommunicationRating = (decimal)(random.NextDouble() * 2 + 3), // 3-5 rating
            TotalOrders = random.Next(50, 500),
            CompletedOrders = random.Next(45, 480),
            CancelledOrders = random.Next(0, 10),
            AverageResponseTime = (decimal)(random.NextDouble() * 12 + 1), // 1-13 hours
            LastUpdated = DateTime.UtcNow.AddDays(-random.Next(1, 30))
        };
    }
}
