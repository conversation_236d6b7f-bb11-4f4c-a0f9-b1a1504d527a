
using Shared.Domain.Common;
using MobileWorkflow.Domain.Events;


namespace MobileWorkflow.Domain.Entities;

public class OfflineData : AggregateRoot
{
    public Guid UserId { get; private set; }
    public Guid MobileSessionId { get; private set; }
    public Guid? MobileAppId { get; private set; }
    public string DataType { get; private set; } // TripUpdate, PODUpload, DocumentUpload, etc.
    public string Action { get; private set; } // Create, Update, Delete
    public Dictionary<string, object> Data { get; private set; }
    public DateTime CreatedOffline { get; private set; }
    public DateTime? SyncedAt { get; private set; }
    public bool IsSynced { get; private set; }
    public string? SyncError { get; private set; }
    public int SyncAttempts { get; private set; }
    public int Priority { get; private set; } // 1 = High (Emergency), 2 = Medium, 3 = Low
    public string? ConflictResolution { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    public virtual MobileSession MobileSession { get; private set; } = null!;
    public virtual MobileApp? MobileApp { get; private set; }

    private OfflineData() { } // EF Core

    public OfflineData(
        Guid userId,
        Guid mobileSessionId,
        string dataType,
        string action,
        Dictionary<string, object> data,
        int priority = 2,
        Guid? mobileAppId = null)
    {
        UserId = userId;
        MobileSessionId = mobileSessionId;
        MobileAppId = mobileAppId;
        DataType = dataType ?? throw new ArgumentNullException(nameof(dataType));
        Action = action ?? throw new ArgumentNullException(nameof(action));
        Data = data ?? throw new ArgumentNullException(nameof(data));
        CreatedOffline = DateTime.UtcNow;
        IsSynced = false;
        SyncAttempts = 0;
        Priority = priority;
        Metadata = new Dictionary<string, object>();

        AddDomainEvent(new OfflineDataCreatedEvent(Id, UserId, DataType, Action, Priority));
    }

    public void MarkAsSynced()
    {
        if (IsSynced)
            throw new InvalidOperationException("Data is already synced");

        IsSynced = true;
        SyncedAt = DateTime.UtcNow;
        SyncError = null;

        AddDomainEvent(new OfflineDataSyncedEvent(Id, UserId, DataType, SyncedAt.Value));
    }

    public void RecordSyncError(string error)
    {
        SyncAttempts++;
        SyncError = error ?? throw new ArgumentNullException(nameof(error));

        AddDomainEvent(new OfflineDataSyncFailedEvent(Id, UserId, DataType, SyncAttempts, error));
    }

    public void UpdateData(Dictionary<string, object> newData)
    {
        if (IsSynced)
            throw new InvalidOperationException("Cannot update synced data");

        Data = newData ?? throw new ArgumentNullException(nameof(newData));
        AddDomainEvent(new OfflineDataUpdatedEvent(Id, UserId, DataType));
    }

    public void SetConflictResolution(string resolution)
    {
        ConflictResolution = resolution ?? throw new ArgumentNullException(nameof(resolution));
        AddDomainEvent(new OfflineDataConflictResolvedEvent(Id, UserId, DataType, resolution));
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    public bool ShouldRetrySync()
    {
        return !IsSynced && SyncAttempts < GetMaxRetryAttempts();
    }

    private int GetMaxRetryAttempts()
    {
        return Priority switch
        {
            1 => 10, // High priority - more retries
            2 => 5,  // Medium priority
            3 => 3,  // Low priority - fewer retries
            _ => 5
        };
    }

    public TimeSpan GetRetryDelay()
    {
        // Exponential backoff based on attempts
        var baseDelay = TimeSpan.FromMinutes(1);
        return TimeSpan.FromTicks(baseDelay.Ticks * (long)Math.Pow(2, Math.Min(SyncAttempts, 6)));
    }
}



