using CommunicationNotification.Domain.Entities;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;

namespace CommunicationNotification.Application.Interfaces;

/// <summary>
/// Service interface for IVR system management
/// </summary>
public interface IIVRService
{
    /// <summary>
    /// Create a new IVR system
    /// </summary>
    Task<IVRSystem> CreateIVRSystemAsync(
        string name,
        string description,
        string welcomeMessage,
        string defaultLanguage,
        Guid createdByUserId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get IVR system by ID
    /// </summary>
    Task<IVRSystem?> GetIVRSystemAsync(
        Guid ivrSystemId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Update IVR system
    /// </summary>
    Task<IVRSystem> UpdateIVRSystemAsync(
        Guid ivrSystemId,
        IVRSystem updatedSystem,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete IVR system
    /// </summary>
    Task DeleteIVRSystemAsync(
        Guid ivrSystemId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all IVR systems
    /// </summary>
    Task<List<IVRSystem>> GetAllIVRSystemsAsync(
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Activate IVR system
    /// </summary>
    Task<IVRSystem> ActivateIVRSystemAsync(
        Guid ivrSystemId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Deactivate IVR system
    /// </summary>
    Task<IVRSystem> DeactivateIVRSystemAsync(
        Guid ivrSystemId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Start IVR session for incoming call
    /// </summary>
    Task<IVRSession> StartIVRSessionAsync(
        string callId,
        string phoneNumber,
        Guid ivrSystemId,
        string language = "en-US",
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Process user input in IVR session
    /// </summary>
    Task<IVRProcessingResult> ProcessInputAsync(
        Guid sessionId,
        string input,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// End IVR session
    /// </summary>
    Task EndIVRSessionAsync(
        Guid sessionId,
        IVRSessionStatus endStatus,
        string? reason = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get IVR session by ID
    /// </summary>
    Task<IVRSession?> GetIVRSessionAsync(
        Guid sessionId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get active IVR sessions
    /// </summary>
    Task<List<IVRSession>> GetActiveSessionsAsync(
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get IVR analytics for system
    /// </summary>
    Task<IVRAnalytics> GetIVRAnalyticsAsync(
        Guid ivrSystemId,
        DateTime? startDate = null,
        DateTime? endDate = null,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Service interface for voice recognition
/// </summary>
public interface IVoiceRecognitionService
{
    /// <summary>
    /// Recognize speech from audio
    /// </summary>
    Task<VoiceRecognitionResult> RecognizeSpeechAsync(
        string audioUrl,
        string language = "en-US",
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Recognize speech from audio stream
    /// </summary>
    Task<VoiceRecognitionResult> RecognizeSpeechStreamAsync(
        Stream audioStream,
        string language = "en-US",
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get supported languages
    /// </summary>
    Task<List<string>> GetSupportedLanguagesAsync(
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Configure voice recognition settings
    /// </summary>
    Task ConfigureRecognitionAsync(
        VoiceRecognitionConfig config,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Service interface for TwiML generation
/// </summary>
public interface ITwiMLService
{
    /// <summary>
    /// Generate TwiML for IVR menu
    /// </summary>
    Task<string> GenerateMenuTwiMLAsync(
        IVRMenu menu,
        IVRSession session,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Generate TwiML for voice message
    /// </summary>
    Task<string> GenerateMessageTwiMLAsync(
        string message,
        string language = "en-US",
        VoiceSettings? voiceSettings = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Generate TwiML for input collection
    /// </summary>
    Task<string> GenerateInputCollectionTwiMLAsync(
        string prompt,
        int maxDigits,
        int timeoutSeconds,
        string finishOnKey = "#",
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Generate TwiML for call transfer
    /// </summary>
    Task<string> GenerateTransferTwiMLAsync(
        string transferNumber,
        string? transferMessage = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Generate TwiML for voice recognition
    /// </summary>
    Task<string> GenerateVoiceRecognitionTwiMLAsync(
        string prompt,
        List<string> expectedPhrases,
        string language = "en-US",
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for IVR systems
/// </summary>
public interface IIVRRepository
{
    /// <summary>
    /// Add IVR system
    /// </summary>
    Task<IVRSystem> AddAsync(
        IVRSystem ivrSystem,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Update IVR system
    /// </summary>
    Task<IVRSystem> UpdateAsync(
        IVRSystem ivrSystem,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get IVR system by ID
    /// </summary>
    Task<IVRSystem?> GetByIdAsync(
        Guid ivrSystemId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all IVR systems
    /// </summary>
    Task<List<IVRSystem>> GetAllAsync(
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get active IVR systems
    /// </summary>
    Task<List<IVRSystem>> GetActiveSystemsAsync(
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete IVR system
    /// </summary>
    Task DeleteAsync(
        Guid ivrSystemId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if IVR system name exists
    /// </summary>
    Task<bool> ExistsAsync(
        string name,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for IVR sessions
/// </summary>
public interface IIVRSessionRepository
{
    /// <summary>
    /// Add IVR session
    /// </summary>
    Task<IVRSession> AddAsync(
        IVRSession session,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Update IVR session
    /// </summary>
    Task<IVRSession> UpdateAsync(
        IVRSession session,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get IVR session by ID
    /// </summary>
    Task<IVRSession?> GetByIdAsync(
        Guid sessionId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get IVR session by call ID
    /// </summary>
    Task<IVRSession?> GetByCallIdAsync(
        string callId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get active sessions
    /// </summary>
    Task<List<IVRSession>> GetActiveSessionsAsync(
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get sessions by IVR system
    /// </summary>
    Task<List<IVRSession>> GetByIVRSystemAsync(
        Guid ivrSystemId,
        DateTime? startDate = null,
        DateTime? endDate = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete old sessions
    /// </summary>
    Task DeleteOldSessionsAsync(
        DateTime cutoffDate,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// IVR processing result
/// </summary>
public class IVRProcessingResult
{
    public bool IsSuccess { get; set; }
    public string? TwiML { get; set; }
    public string? ErrorMessage { get; set; }
    public IVRActionType? NextAction { get; set; }
    public string? NextActionValue { get; set; }
    public Dictionary<string, string> Variables { get; set; } = new();
    public bool ShouldEndCall { get; set; }
    public string? TransferNumber { get; set; }

    public static IVRProcessingResult Success(string twiML, IVRActionType? nextAction = null, string? nextActionValue = null)
    {
        return new IVRProcessingResult
        {
            IsSuccess = true,
            TwiML = twiML,
            NextAction = nextAction,
            NextActionValue = nextActionValue
        };
    }

    public static IVRProcessingResult Failure(string errorMessage)
    {
        return new IVRProcessingResult
        {
            IsSuccess = false,
            ErrorMessage = errorMessage
        };
    }

    public static IVRProcessingResult EndCall()
    {
        return new IVRProcessingResult
        {
            IsSuccess = true,
            ShouldEndCall = true,
            TwiML = "<Response><Hangup/></Response>"
        };
    }

    public static IVRProcessingResult Transfer(string transferNumber, string? message = null)
    {
        return new IVRProcessingResult
        {
            IsSuccess = true,
            TransferNumber = transferNumber,
            NextAction = IVRActionType.TransferCall,
            NextActionValue = transferNumber
        };
    }
}

/// <summary>
/// Voice recognition configuration
/// </summary>
public class VoiceRecognitionConfig
{
    public string Language { get; set; } = "en-US";
    public decimal ConfidenceThreshold { get; set; } = 0.7m;
    public int MaxAlternatives { get; set; } = 1;
    public bool EnableProfanityFilter { get; set; } = true;
    public bool EnableAutomaticPunctuation { get; set; } = true;
    public List<string> SpeechContexts { get; set; } = new();
    public Dictionary<string, object> ProviderSettings { get; set; } = new();
}
