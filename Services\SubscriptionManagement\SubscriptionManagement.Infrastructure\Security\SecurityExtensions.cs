using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;

namespace SubscriptionManagement.Infrastructure.Security
{
    public static class SecurityExtensions
    {
        public static IServiceCollection AddSubscriptionSecurity(
            this IServiceCollection services, 
            IConfiguration configuration)
        {
            // Configure authorization policies
            services.AddAuthorization(options =>
            {
                options.AddPolicy("AdminOnly", policy =>
                {
                    policy.Requirements.Add(new AdminRequirement());
                });

                options.AddPolicy("PaymentVerification", policy =>
                {
                    policy.RequireAuthenticatedUser();
                    policy.RequireAssertion(context =>
                        context.User.IsInRole("Admin") ||
                        context.User.IsInRole("PaymentVerifier") ||
                        context.User.HasClaim("permission", "admin.payments"));
                });

                options.AddPolicy("NotificationManagement", policy =>
                {
                    policy.RequireAuthenticatedUser();
                    policy.RequireAssertion(context =>
                        context.User.IsInRole("Admin") ||
                        context.User.IsInRole("NotificationManager") ||
                        context.User.HasClaim("permission", "admin.notifications"));
                });

                options.AddPolicy("SubscriptionManagement", policy =>
                {
                    policy.RequireAuthenticatedUser();
                    policy.RequireAssertion(context =>
                        context.User.IsInRole("Admin") ||
                        context.User.IsInRole("SubscriptionManager") ||
                        context.User.HasClaim("permission", "admin.subscriptions"));
                });
            });

            // Register authorization handlers
            services.AddScoped<IAuthorizationHandler, AdminAuthorizationHandler>();

            // Configure rate limiting options
            var rateLimitOptions = new RateLimitOptions();
            configuration.GetSection("RateLimit").Bind(rateLimitOptions);
            services.AddSingleton(rateLimitOptions);

            // Configure idempotency options
            var idempotencyOptions = new IdempotencyOptions();
            configuration.GetSection("Idempotency").Bind(idempotencyOptions);
            services.AddSingleton(idempotencyOptions);

            return services;
        }
    }
}
