using FinancialPayment.Domain.Enums;
using Shared.Domain.Common;
using FinancialPayment.Domain.ValueObjects;


namespace FinancialPayment.Domain.Entities;

/// <summary>
/// Invoice entity with comprehensive tax calculations and compliance features
/// </summary>
public class TaxAwareInvoice : AggregateRoot
{
    public string InvoiceNumber { get; private set; }
    public Guid OrderId { get; private set; }
    public Money BaseAmount { get; private set; }
    public string Description { get; private set; }
    public CommissionTaxDetails TaxDetails { get; private set; }
    public Money TotalAmount { get; private set; }
    public ServiceCategory ServiceCategory { get; private set; }
    public TaxJurisdiction Jurisdiction { get; private set; }
    public EntityType EntityType { get; private set; }
    public TdsSection? TdsSection { get; private set; }
    public string? HsnCode { get; private set; }
    public bool HasPan { get; private set; }
    public DateTime InvoiceDate { get; private set; }
    public DateTime DueDate { get; private set; }
    public InvoiceStatus Status { get; private set; }
    public string? Notes { get; private set; }
    public Guid? CommissionId { get; private set; }
    public string? PaymentTerms { get; private set; }
    public string? TaxRegistrationNumber { get; private set; }

    // Navigation properties
    private readonly List<InvoiceLineItem> _lineItems = new();
    public IReadOnlyList<InvoiceLineItem> LineItems => _lineItems.AsReadOnly();

    private readonly List<InvoicePayment> _payments = new();
    public IReadOnlyList<InvoicePayment> Payments => _payments.AsReadOnly();

    private TaxAwareInvoice() { }

    public TaxAwareInvoice(
        Guid orderId,
        Money baseAmount,
        string invoiceNumber,
        string description,
        CommissionTaxDetails taxDetails,
        ServiceCategory serviceCategory,
        TaxJurisdiction jurisdiction,
        EntityType entityType,
        List<InvoiceLineItem> lineItems,
        TdsSection? tdsSection = null,
        string? hsnCode = null,
        bool hasPan = true,
        string? notes = null,
        int dueDays = 30)
    {
        if (taxDetails == null)
            throw new ArgumentNullException(nameof(taxDetails));

        if (lineItems == null || !lineItems.Any())
            throw new ArgumentException("Invoice must have at least one line item", nameof(lineItems));

        InvoiceNumber = invoiceNumber ?? GenerateInvoiceNumber();
        OrderId = orderId;
        BaseAmount = baseAmount;
        Description = description;

        TaxDetails = new CommissionTaxDetails(
            taxDetails.GstAmount,
            taxDetails.TdsAmount,
            taxDetails.TotalTaxAmount,
            taxDetails.NetAmount,
            taxDetails.EffectiveGstRate,
            taxDetails.EffectiveTdsRate,
            taxDetails.IsReverseChargeApplicable,
            taxDetails.AppliedHsnCode,
            taxDetails.AppliedRules.ToList(),
            taxDetails.Warnings.ToList());

        // Calculate total amount (base + GST - TDS for invoice total)
        TotalAmount = baseAmount + taxDetails.GstAmount;

        ServiceCategory = serviceCategory;
        Jurisdiction = jurisdiction;
        EntityType = entityType;
        TdsSection = tdsSection;
        HsnCode = hsnCode;
        HasPan = hasPan;
        Notes = notes;
        InvoiceDate = DateTime.UtcNow;
        DueDate = InvoiceDate.AddDays(dueDays);
        Status = InvoiceStatus.Draft;
        PaymentTerms = $"Net {dueDays} days";

        // Add line items
        foreach (var lineItem in lineItems)
        {
            _lineItems.Add(lineItem);
        }

        ValidateInvoice();

        AddDomainEvent(new TaxAwareInvoiceCreatedEvent(Id, OrderId, InvoiceNumber, TotalAmount, TaxDetails));
    }

    public void SetCommissionReference(Guid commissionId)
    {
        CommissionId = commissionId;
        SetUpdatedAt();
    }

    public void SetTaxRegistrationNumber(string taxRegistrationNumber)
    {
        if (string.IsNullOrWhiteSpace(taxRegistrationNumber))
            throw new ArgumentException("Tax registration number cannot be empty", nameof(taxRegistrationNumber));

        TaxRegistrationNumber = taxRegistrationNumber;
        SetUpdatedAt();
    }

    public void UpdatePaymentTerms(string paymentTerms, int dueDays)
    {
        if (Status != InvoiceStatus.Draft)
            throw new InvalidOperationException("Cannot update payment terms for non-draft invoice");

        PaymentTerms = paymentTerms;
        DueDate = InvoiceDate.AddDays(dueDays);
        SetUpdatedAt();
    }

    public void AddLineItem(InvoiceLineItem lineItem)
    {
        if (Status != InvoiceStatus.Draft)
            throw new InvalidOperationException("Cannot add line items to non-draft invoice");

        _lineItems.Add(lineItem);
        RecalculateAmounts();
        SetUpdatedAt();
    }

    public void RemoveLineItem(Guid lineItemId)
    {
        if (Status != InvoiceStatus.Draft)
            throw new InvalidOperationException("Cannot remove line items from non-draft invoice");

        var lineItem = _lineItems.FirstOrDefault(li => li.Id == lineItemId);
        if (lineItem != null)
        {
            _lineItems.Remove(lineItem);
            RecalculateAmounts();
            SetUpdatedAt();
        }
    }

    public void Finalize()
    {
        if (Status != InvoiceStatus.Draft)
            throw new InvalidOperationException($"Cannot finalize invoice with status {Status}");

        ValidateInvoice();
        Status = InvoiceStatus.Sent;
        SetUpdatedAt();

        AddDomainEvent(new InvoiceFinalizedEvent(Id, InvoiceNumber, TotalAmount));
    }

    public void MarkAsPaid(Money paidAmount, string paymentReference, DateTime paidDate)
    {
        if (Status == InvoiceStatus.Paid)
            throw new InvalidOperationException("Invoice is already paid");

        if (Status == InvoiceStatus.Cancelled)
            throw new InvalidOperationException("Cannot mark cancelled invoice as paid");

        var payment = new InvoicePayment(Id, paidAmount, paymentReference, paidDate);
        _payments.Add(payment);

        var totalPaid = GetTotalPaidAmount();
        if (totalPaid.Amount >= TotalAmount.Amount)
        {
            Status = InvoiceStatus.Paid;
            AddDomainEvent(new InvoicePaidEvent(Id, InvoiceNumber, TotalAmount, paidDate));
        }
        else
        {
            Status = InvoiceStatus.PartiallyPaid;
            AddDomainEvent(new InvoicePartiallyPaidEvent(Id, InvoiceNumber, paidAmount, totalPaid));
        }

        SetUpdatedAt();
    }

    public void Cancel(string reason)
    {
        if (Status == InvoiceStatus.Paid)
            throw new InvalidOperationException("Cannot cancel paid invoice");

        Status = InvoiceStatus.Cancelled;
        Notes = $"{Notes}\nCancelled: {reason}";
        SetUpdatedAt();

        AddDomainEvent(new InvoiceCancelledEvent(Id, InvoiceNumber, reason));
    }

    public Money GetTotalPaidAmount()
    {
        return _payments.Aggregate(Money.Zero(TotalAmount.Currency), (sum, payment) => sum + payment.Amount);
    }

    public Money GetOutstandingAmount()
    {
        return TotalAmount - GetTotalPaidAmount();
    }

    public bool IsOverdue()
    {
        return Status != InvoiceStatus.Paid &&
               Status != InvoiceStatus.Cancelled &&
               DateTime.UtcNow > DueDate;
    }

    public int GetDaysOverdue()
    {
        if (!IsOverdue())
            return 0;

        return (DateTime.UtcNow - DueDate).Days;
    }

    public string GetTaxSummary()
    {
        return $"Base: {BaseAmount}, " +
               $"GST: {TaxDetails.GstAmount} ({TaxDetails.EffectiveGstRate:F2}%), " +
               $"TDS: {TaxDetails.TdsAmount} ({TaxDetails.EffectiveTdsRate:F2}%), " +
               $"Total: {TotalAmount}";
    }

    public InvoiceComplianceInfo GetComplianceInfo()
    {
        return new InvoiceComplianceInfo
        {
            InvoiceNumber = InvoiceNumber,
            InvoiceDate = InvoiceDate,
            TaxRegistrationNumber = TaxRegistrationNumber,
            HsnCode = HsnCode,
            GstAmount = TaxDetails.GstAmount,
            TdsAmount = TaxDetails.TdsAmount,
            IsReverseChargeApplicable = TaxDetails.IsReverseChargeApplicable,
            HasPan = HasPan,
            EntityType = EntityType,
            Jurisdiction = Jurisdiction.GetFullJurisdiction(),
            ComplianceWarnings = TaxDetails.TaxWarnings.ToList()
        };
    }

    private void ValidateInvoice()
    {
        if (!_lineItems.Any())
            throw new InvalidOperationException("Invoice must have at least one line item");

        if (BaseAmount.Amount <= 0)
            throw new InvalidOperationException("Invoice base amount must be greater than zero");

        if (string.IsNullOrWhiteSpace(Description))
            throw new InvalidOperationException("Invoice description is required");

        if (string.IsNullOrWhiteSpace(InvoiceNumber))
            throw new InvalidOperationException("Invoice number is required");
    }

    private void RecalculateAmounts()
    {
        // Recalculate base amount from line items
        var newBaseAmount = _lineItems.Aggregate(Money.Zero(BaseAmount.Currency),
            (sum, item) => sum + item.GetTotalAmount());

        if (newBaseAmount != BaseAmount)
        {
            BaseAmount = newBaseAmount;
            // Note: In a real implementation, you might want to recalculate taxes here
            // For now, we'll keep the existing tax calculation
            TotalAmount = BaseAmount + TaxDetails.GstAmount;
        }
    }

    private static string GenerateInvoiceNumber()
    {
        var prefix = "INV";
        var year = DateTime.UtcNow.Year;
        var month = DateTime.UtcNow.Month;
        var timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

        return $"{prefix}-{year:D4}{month:D2}-{timestamp}";
    }
}

/// <summary>
/// Invoice line item entity
/// </summary>
public class InvoiceLineItem : BaseEntity
{
    public string Description { get; private set; }
    public string? DetailedDescription { get; private set; }
    public decimal Quantity { get; private set; }
    public Money UnitPrice { get; private set; }
    public decimal TaxRate { get; private set; }
    public string? HsnCode { get; private set; }

    private InvoiceLineItem() { }

    public InvoiceLineItem(
        string description,
        string? detailedDescription,
        decimal quantity,
        Money unitPrice,
        decimal taxRate = 0,
        string? hsnCode = null)
    {
        if (string.IsNullOrWhiteSpace(description))
            throw new ArgumentException("Description cannot be empty", nameof(description));

        if (quantity <= 0)
            throw new ArgumentException("Quantity must be greater than zero", nameof(quantity));

        if (unitPrice.Amount < 0)
            throw new ArgumentException("Unit price cannot be negative", nameof(unitPrice));

        Description = description;
        DetailedDescription = detailedDescription;
        Quantity = quantity;
        UnitPrice = unitPrice;
        TaxRate = taxRate;
        HsnCode = hsnCode;
    }

    public Money GetTotalAmount()
    {
        return new Money(UnitPrice.Amount * Quantity, UnitPrice.Currency);
    }

    public Money GetTaxAmount()
    {
        var totalAmount = GetTotalAmount();
        return new Money(totalAmount.Amount * (TaxRate / 100), totalAmount.Currency);
    }
}

/// <summary>
/// Invoice payment record
/// </summary>
public class InvoicePayment : BaseEntity
{
    public Guid InvoiceId { get; private set; }
    public Money Amount { get; private set; }
    public string PaymentReference { get; private set; }
    public DateTime PaidDate { get; private set; }

    private InvoicePayment() { }

    public InvoicePayment(Guid invoiceId, Money amount, string paymentReference, DateTime paidDate)
    {
        InvoiceId = invoiceId;
        Amount = amount;
        PaymentReference = paymentReference;
        PaidDate = paidDate;
    }
}

/// <summary>
/// Invoice status enumeration
/// </summary>
public enum InvoiceStatus
{
    Draft = 1,
    Sent = 2,
    PartiallyPaid = 3,
    Paid = 4,
    Overdue = 5,
    Cancelled = 6
}

/// <summary>
/// Invoice compliance information
/// </summary>
public class InvoiceComplianceInfo
{
    public string InvoiceNumber { get; set; } = string.Empty;
    public DateTime InvoiceDate { get; set; }
    public string? TaxRegistrationNumber { get; set; }
    public string? HsnCode { get; set; }
    public Money GstAmount { get; set; } = Money.Zero("INR");
    public Money TdsAmount { get; set; } = Money.Zero("INR");
    public bool IsReverseChargeApplicable { get; set; }
    public bool HasPan { get; set; }
    public EntityType EntityType { get; set; }
    public string Jurisdiction { get; set; } = string.Empty;
    public List<string> ComplianceWarnings { get; set; } = new();
}

/// <summary>
/// Domain events for tax-aware invoices
/// </summary>
public class TaxAwareInvoiceCreatedEvent : DomainEvent
{
    public Guid InvoiceId { get; }
    public Guid OrderId { get; }
    public string InvoiceNumber { get; }
    public Money TotalAmount { get; }
    public CommissionTaxDetails TaxDetails { get; }

    public TaxAwareInvoiceCreatedEvent(Guid invoiceId, Guid orderId, string invoiceNumber, Money totalAmount, CommissionTaxDetails taxDetails)
    {
        InvoiceId = invoiceId;
        OrderId = orderId;
        InvoiceNumber = invoiceNumber;
        TotalAmount = totalAmount;
        TaxDetails = taxDetails;
    }
}

public class InvoiceFinalizedEvent : DomainEvent
{
    public Guid InvoiceId { get; }
    public string InvoiceNumber { get; }
    public Money TotalAmount { get; }

    public InvoiceFinalizedEvent(Guid invoiceId, string invoiceNumber, Money totalAmount)
    {
        InvoiceId = invoiceId;
        InvoiceNumber = invoiceNumber;
        TotalAmount = totalAmount;
    }
}

public class InvoicePaidEvent : DomainEvent
{
    public Guid InvoiceId { get; }
    public string InvoiceNumber { get; }
    public Money TotalAmount { get; }
    public DateTime PaidDate { get; }

    public InvoicePaidEvent(Guid invoiceId, string invoiceNumber, Money totalAmount, DateTime paidDate)
    {
        InvoiceId = invoiceId;
        InvoiceNumber = invoiceNumber;
        TotalAmount = totalAmount;
        PaidDate = paidDate;
    }
}

public class InvoicePartiallyPaidEvent : DomainEvent
{
    public Guid InvoiceId { get; }
    public string InvoiceNumber { get; }
    public Money PaidAmount { get; }
    public Money TotalPaid { get; }

    public InvoicePartiallyPaidEvent(Guid invoiceId, string invoiceNumber, Money paidAmount, Money totalPaid)
    {
        InvoiceId = invoiceId;
        InvoiceNumber = invoiceNumber;
        PaidAmount = paidAmount;
        TotalPaid = totalPaid;
    }
}

public class InvoiceCancelledEvent : DomainEvent
{
    public Guid InvoiceId { get; }
    public string InvoiceNumber { get; }
    public string Reason { get; }

    public InvoiceCancelledEvent(Guid invoiceId, string invoiceNumber, string reason)
    {
        InvoiceId = invoiceId;
        InvoiceNumber = invoiceNumber;
        Reason = reason;
    }
}


