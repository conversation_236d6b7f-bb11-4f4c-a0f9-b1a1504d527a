using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Domain.Entities;
using System.Text.Json;

namespace FinancialPayment.Infrastructure.Services;

public class FeatureFlagService : IFeatureFlagService
{
    private readonly IFeatureFlagRepository _repository;
    private readonly IFeatureFlagCache _cache;
    private readonly IFeatureFlagAnalyticsService _analyticsService;
    private readonly ILogger<FeatureFlagService> _logger;
    private readonly FeatureFlagConfiguration _configuration;

    public FeatureFlagService(
        IFeatureFlagRepository repository,
        IFeatureFlagCache cache,
        IFeatureFlagAnalyticsService analyticsService,
        ILogger<FeatureFlagService> logger,
        IOptions<FeatureFlagConfiguration> configuration)
    {
        _repository = repository;
        _cache = cache;
        _analyticsService = analyticsService;
        _logger = logger;
        _configuration = configuration.Value;
    }

    public async Task<bool> IsEnabledAsync(string flagKey, FeatureFlagContext? context = null)
    {
        try
        {
            var evaluation = await EvaluateAsync(flagKey, context);
            return evaluation.IsEnabled;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error evaluating feature flag {FlagKey}", flagKey);
            return false;
        }
    }

    public async Task<T> GetValueAsync<T>(string flagKey, T defaultValue, FeatureFlagContext? context = null)
    {
        try
        {
            var evaluation = await EvaluateAsync(flagKey, context);
            
            if (!evaluation.IsEnabled || !evaluation.Value.Any())
            {
                return defaultValue;
            }

            // Try to get the value from the evaluation
            if (evaluation.Value.ContainsKey("value"))
            {
                var value = evaluation.Value["value"];
                if (value is T typedValue)
                {
                    return typedValue;
                }
                
                // Try to convert the value
                try
                {
                    return (T)Convert.ChangeType(value, typeof(T));
                }
                catch
                {
                    return defaultValue;
                }
            }

            return defaultValue;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting value for feature flag {FlagKey}", flagKey);
            return defaultValue;
        }
    }

    public async Task<FeatureFlagEvaluation> EvaluateAsync(string flagKey, FeatureFlagContext? context = null)
    {
        try
        {
            var flag = await GetFlagFromCacheOrRepository(flagKey);
            
            if (flag == null)
            {
                var evaluation = new FeatureFlagEvaluation
                {
                    FlagKey = flagKey,
                    IsEnabled = false,
                    EvaluatedAt = DateTime.UtcNow,
                    Reason = "Flag not found"
                };

                if (_configuration.EnableAnalytics)
                {
                    await _analyticsService.RecordEvaluationAsync(flagKey, evaluation, context);
                }

                return evaluation;
            }

            var effectiveContext = context ?? new FeatureFlagContext();
            var isEnabled = flag.EvaluateFlag(effectiveContext);
            var variant = flag.GetVariant(effectiveContext);

            var result = new FeatureFlagEvaluation
            {
                FlagKey = flagKey,
                IsEnabled = isEnabled,
                Value = isEnabled ? (variant?.Value ?? flag.DefaultValue) : new Dictionary<string, object>(),
                VariantKey = variant?.VariantKey,
                EvaluatedAt = DateTime.UtcNow,
                Reason = isEnabled ? "Flag enabled" : "Flag disabled"
            };

            if (_configuration.EnableAnalytics)
            {
                await _analyticsService.RecordEvaluationAsync(flagKey, result, context);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error evaluating feature flag {FlagKey}", flagKey);
            
            return new FeatureFlagEvaluation
            {
                FlagKey = flagKey,
                IsEnabled = false,
                EvaluatedAt = DateTime.UtcNow,
                Reason = $"Evaluation error: {ex.Message}"
            };
        }
    }

    public async Task<Dictionary<string, FeatureFlagEvaluation>> EvaluateAllAsync(FeatureFlagContext? context = null)
    {
        try
        {
            var flags = await _repository.GetEnabledFlagsAsync(_configuration.DefaultEnvironment);
            var evaluations = new Dictionary<string, FeatureFlagEvaluation>();

            foreach (var flag in flags)
            {
                var evaluation = await EvaluateAsync(flag.FlagKey, context);
                evaluations[flag.FlagKey] = evaluation;
            }

            return evaluations;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error evaluating all feature flags");
            return new Dictionary<string, FeatureFlagEvaluation>();
        }
    }

    public async Task<FeatureFlag> CreateFlagAsync(CreateFeatureFlagRequest request)
    {
        _logger.LogInformation("Creating feature flag {FlagKey}", request.FlagKey);

        try
        {
            // Check if flag already exists
            var existingFlag = await _repository.GetByKeyAsync(request.FlagKey);
            if (existingFlag != null)
            {
                throw new InvalidOperationException($"Feature flag with key '{request.FlagKey}' already exists");
            }

            var flag = new FeatureFlag(
                request.FlagKey,
                request.Name,
                request.Description,
                request.Type,
                request.Environment,
                request.CreatedBy);

            // Set default value and configuration
            flag.UpdateConfiguration(request.Configuration);
            foreach (var defaultValue in request.DefaultValue)
            {
                flag.DefaultValue[defaultValue.Key] = defaultValue.Value;
            }

            // Add rules
            foreach (var ruleRequest in request.Rules)
            {
                flag.AddRule(
                    ruleRequest.RuleName,
                    ruleRequest.Condition,
                    ruleRequest.Parameters,
                    ruleRequest.Priority);
            }

            // Add variants
            foreach (var variantRequest in request.Variants)
            {
                flag.AddVariant(
                    variantRequest.VariantKey,
                    variantRequest.Name,
                    variantRequest.Weight,
                    variantRequest.Value);
            }

            await _repository.AddAsync(flag);

            // Update cache
            if (_configuration.EnableCaching)
            {
                await _cache.SetAsync(flag.FlagKey, flag, TimeSpan.FromMinutes(_configuration.CacheExpiryMinutes));
            }

            _logger.LogInformation("Feature flag {FlagKey} created successfully", request.FlagKey);
            return flag;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating feature flag {FlagKey}", request.FlagKey);
            throw;
        }
    }

    public async Task<FeatureFlag> UpdateFlagAsync(Guid flagId, UpdateFeatureFlagRequest request)
    {
        _logger.LogInformation("Updating feature flag {FlagId}", flagId);

        try
        {
            var flag = await _repository.GetByIdAsync(flagId);
            if (flag == null)
            {
                throw new InvalidOperationException($"Feature flag {flagId} not found");
            }

            // Update properties
            if (!string.IsNullOrEmpty(request.Name))
            {
                // Note: In a real implementation, you'd need to add a method to update the name
                // flag.UpdateName(request.Name, request.ModifiedBy);
            }

            if (request.IsEnabled.HasValue)
            {
                if (request.IsEnabled.Value)
                    flag.Enable(request.ModifiedBy);
                else
                    flag.Disable(request.ModifiedBy);
            }

            if (request.Configuration != null)
            {
                flag.UpdateConfiguration(request.Configuration, request.ModifiedBy);
            }

            if (request.DefaultValue != null)
            {
                foreach (var defaultValue in request.DefaultValue)
                {
                    flag.DefaultValue[defaultValue.Key] = defaultValue.Value;
                }
            }

            await _repository.UpdateAsync(flag);

            // Update cache
            if (_configuration.EnableCaching)
            {
                await _cache.SetAsync(flag.FlagKey, flag, TimeSpan.FromMinutes(_configuration.CacheExpiryMinutes));
            }

            _logger.LogInformation("Feature flag {FlagId} updated successfully", flagId);
            return flag;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating feature flag {FlagId}", flagId);
            throw;
        }
    }

    public async Task<bool> DeleteFlagAsync(Guid flagId)
    {
        _logger.LogInformation("Deleting feature flag {FlagId}", flagId);

        try
        {
            var flag = await _repository.GetByIdAsync(flagId);
            if (flag == null)
            {
                return false;
            }

            await _repository.DeleteAsync(flagId);

            // Remove from cache
            if (_configuration.EnableCaching)
            {
                await _cache.RemoveAsync(flag.FlagKey);
            }

            _logger.LogInformation("Feature flag {FlagId} deleted successfully", flagId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting feature flag {FlagId}", flagId);
            throw;
        }
    }

    public async Task<List<FeatureFlag>> GetFlagsAsync(string? environment = null)
    {
        try
        {
            if (!string.IsNullOrEmpty(environment))
            {
                return await _repository.GetByEnvironmentAsync(environment);
            }
            else
            {
                return await _repository.GetAllAsync();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting feature flags");
            throw;
        }
    }

    public async Task<FeatureFlag?> GetFlagAsync(Guid flagId)
    {
        try
        {
            return await _repository.GetByIdAsync(flagId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting feature flag {FlagId}", flagId);
            throw;
        }
    }

    public async Task<FeatureFlag?> GetFlagByKeyAsync(string flagKey)
    {
        try
        {
            return await GetFlagFromCacheOrRepository(flagKey);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting feature flag {FlagKey}", flagKey);
            throw;
        }
    }

    public async Task<bool> EnableFlagAsync(Guid flagId, Guid? modifiedBy = null)
    {
        try
        {
            var flag = await _repository.GetByIdAsync(flagId);
            if (flag == null)
            {
                return false;
            }

            flag.Enable(modifiedBy);
            await _repository.UpdateAsync(flag);

            // Update cache
            if (_configuration.EnableCaching)
            {
                await _cache.SetAsync(flag.FlagKey, flag, TimeSpan.FromMinutes(_configuration.CacheExpiryMinutes));
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error enabling feature flag {FlagId}", flagId);
            throw;
        }
    }

    public async Task<bool> DisableFlagAsync(Guid flagId, Guid? modifiedBy = null)
    {
        try
        {
            var flag = await _repository.GetByIdAsync(flagId);
            if (flag == null)
            {
                return false;
            }

            flag.Disable(modifiedBy);
            await _repository.UpdateAsync(flag);

            // Update cache
            if (_configuration.EnableCaching)
            {
                await _cache.SetAsync(flag.FlagKey, flag, TimeSpan.FromMinutes(_configuration.CacheExpiryMinutes));
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disabling feature flag {FlagId}", flagId);
            throw;
        }
    }

    public async Task<List<FeatureFlagAuditLog>> GetAuditLogsAsync(Guid flagId)
    {
        try
        {
            var flag = await _repository.GetByIdAsync(flagId);
            return flag?.AuditLogs.ToList() ?? new List<FeatureFlagAuditLog>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting audit logs for feature flag {FlagId}", flagId);
            throw;
        }
    }

    public async Task<FeatureFlagAnalytics> GetAnalyticsAsync(string flagKey, DateTime? fromDate = null, DateTime? toDate = null)
    {
        try
        {
            return await _analyticsService.GetAnalyticsAsync(flagKey, fromDate, toDate);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting analytics for feature flag {FlagKey}", flagKey);
            throw;
        }
    }

    private async Task<FeatureFlag?> GetFlagFromCacheOrRepository(string flagKey)
    {
        if (_configuration.EnableCaching)
        {
            var cachedFlag = await _cache.GetAsync(flagKey);
            if (cachedFlag != null)
            {
                return cachedFlag;
            }
        }

        var flag = await _repository.GetByKeyAsync(flagKey);
        
        if (flag != null && _configuration.EnableCaching)
        {
            await _cache.SetAsync(flagKey, flag, TimeSpan.FromMinutes(_configuration.CacheExpiryMinutes));
        }

        return flag;
    }
}
