using MediatR;

namespace FinancialPayment.Application.Common;

/// <summary>
/// Base interface for queries
/// </summary>
/// <typeparam name="TResponse">The response type</typeparam>
public interface IQuery<out TResponse> : IRequest<TResponse>
{
}

/// <summary>
/// Base interface for query handlers
/// </summary>
/// <typeparam name="TQuery">The query type</typeparam>
/// <typeparam name="TResponse">The response type</typeparam>
public interface IQueryHandler<in TQuery, TResponse> : IRequestHandler<TQuery, TResponse>
    where TQuery : IQuery<TResponse>
{
}

/// <summary>
/// Base abstract class for queries
/// </summary>
/// <typeparam name="TResponse">The response type</typeparam>
public abstract class QueryBase<TResponse> : IQuery<TResponse>
{
    /// <summary>
    /// Unique identifier for the query
    /// </summary>
    public Guid QueryId { get; } = Guid.NewGuid();

    /// <summary>
    /// Timestamp when the query was created
    /// </summary>
    public DateTime CreatedAt { get; } = DateTime.UtcNow;

    /// <summary>
    /// User who initiated the query
    /// </summary>
    public Guid? InitiatedBy { get; set; }

    /// <summary>
    /// Correlation ID for tracking across services
    /// </summary>
    public string? CorrelationId { get; set; }

    /// <summary>
    /// Additional metadata for the query
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Query result wrapper
/// </summary>
/// <typeparam name="T">The result data type</typeparam>
public class QueryResult<T>
{
    public bool IsSuccess { get; set; }
    public T? Data { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime ExecutedAt { get; set; } = DateTime.UtcNow;
    public Guid QueryId { get; set; }

    public static QueryResult<T> Success(T data, Guid queryId)
    {
        return new QueryResult<T>
        {
            IsSuccess = true,
            Data = data,
            QueryId = queryId
        };
    }

    public static QueryResult<T> Failure(string errorMessage, Guid queryId)
    {
        return new QueryResult<T>
        {
            IsSuccess = false,
            ErrorMessage = errorMessage,
            QueryId = queryId
        };
    }
}

/// <summary>
/// Pagination parameters
/// </summary>
public class PaginationParameters
{
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public string? SortBy { get; set; }
    public string? SortDirection { get; set; } = "asc";

    public int Skip => (PageNumber - 1) * PageSize;
    public int Take => PageSize;
}

/// <summary>
/// Paginated result wrapper
/// </summary>
/// <typeparam name="T">The item type</typeparam>
public class PagedResult<T>
{
    public List<T> Items { get; set; } = new();
    public int TotalCount { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
    public bool HasPreviousPage => PageNumber > 1;
    public bool HasNextPage => PageNumber < TotalPages;

    public static PagedResult<T> Create(List<T> items, int totalCount, int pageNumber, int pageSize)
    {
        return new PagedResult<T>
        {
            Items = items,
            TotalCount = totalCount,
            PageNumber = pageNumber,
            PageSize = pageSize
        };
    }
}

/// <summary>
/// Base filtering parameters
/// </summary>
public class FilteringParameters
{
    public string? SearchTerm { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public Dictionary<string, object> Filters { get; set; } = new();
}

/// <summary>
/// Combined query parameters for common scenarios
/// </summary>
public class QueryParameters
{
    public PaginationParameters Pagination { get; set; } = new();
    public FilteringParameters Filtering { get; set; } = new();
}
