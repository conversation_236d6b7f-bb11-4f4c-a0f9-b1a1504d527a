using Microsoft.EntityFrameworkCore;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Entities;
using OrderManagement.Domain.Enums;
using OrderManagement.Infrastructure.Persistence;

namespace OrderManagement.Infrastructure.Repositories;

public class RfqBidRepository : IRfqBidRepository
{
    private readonly OrderManagementDbContext _context;

    public RfqBidRepository(OrderManagementDbContext context)
    {
        _context = context;
    }

    public async Task<RfqBid?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.RfqBids
            .Include(b => b.Rfq)
            .Include(b => b.Documents)
            .FirstOrDefaultAsync(b => b.Id == id, cancellationToken);
    }

    public async Task<IEnumerable<RfqBid>> GetByRfqIdAsync(Guid rfqId, CancellationToken cancellationToken = default)
    {
        return await _context.RfqBids
            .Include(b => b.Documents)
            .Where(b => b.RfqId == rfqId)
            .OrderByDescending(b => b.SubmittedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<RfqBid>> GetByBrokerIdAsync(Guid brokerId, CancellationToken cancellationToken = default)
    {
        return await _context.RfqBids
            .Include(b => b.Rfq)
            .Include(b => b.Documents)
            .Where(b => b.BrokerId == brokerId)
            .OrderByDescending(b => b.SubmittedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<RfqBid?> GetByBidNumberAsync(string bidNumber, CancellationToken cancellationToken = default)
    {
        return await _context.RfqBids
            .Include(b => b.Rfq)
            .Include(b => b.Documents)
            .FirstOrDefaultAsync(b => b.BidNumber == bidNumber, cancellationToken);
    }

    public async Task<IEnumerable<RfqBid>> GetBidsByStatusAsync(BidStatus status, CancellationToken cancellationToken = default)
    {
        return await _context.RfqBids
            .Include(b => b.Rfq)
            .Where(b => b.Status == status)
            .OrderByDescending(b => b.SubmittedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<(IEnumerable<RfqBid> Bids, int TotalCount)> GetPagedAsync(
        int page,
        int pageSize,
        Guid? rfqId = null,
        Guid? brokerId = null,
        BidStatus? status = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.RfqBids.AsQueryable();

        if (rfqId.HasValue)
        {
            query = query.Where(b => b.RfqId == rfqId.Value);
        }

        if (brokerId.HasValue)
        {
            query = query.Where(b => b.BrokerId == brokerId.Value);
        }

        if (status.HasValue)
        {
            query = query.Where(b => b.Status == status.Value);
        }

        var totalCount = await query.CountAsync(cancellationToken);

        var bids = await query
            .Include(b => b.Rfq)
            .Include(b => b.Documents)
            .OrderByDescending(b => b.SubmittedAt)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return (bids, totalCount);
    }

    public async Task AddAsync(RfqBid bid, CancellationToken cancellationToken = default)
    {
        await _context.RfqBids.AddAsync(bid, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task<List<RfqBid>> GetByBrokerAsync(
        Guid brokerId,
        int page,
        int pageSize,
        BidStatus? status = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.RfqBids
            .Where(b => b.BrokerId == brokerId);

        if (status.HasValue)
        {
            query = query.Where(b => b.Status == status.Value);
        }

        if (fromDate.HasValue)
        {
            query = query.Where(b => b.SubmittedAt >= fromDate.Value);
        }

        if (toDate.HasValue)
        {
            query = query.Where(b => b.SubmittedAt <= toDate.Value);
        }

        return await query
            .Include(b => b.Rfq)
            .Include(b => b.Documents)
            .OrderByDescending(b => b.SubmittedAt)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);
    }

    public async Task<int> GetCountByBrokerAsync(
        Guid brokerId,
        BidStatus? status = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.RfqBids
            .Where(b => b.BrokerId == brokerId);

        if (status.HasValue)
        {
            query = query.Where(b => b.Status == status.Value);
        }

        if (fromDate.HasValue)
        {
            query = query.Where(b => b.SubmittedAt >= fromDate.Value);
        }

        if (toDate.HasValue)
        {
            query = query.Where(b => b.SubmittedAt <= toDate.Value);
        }

        return await query.CountAsync(cancellationToken);
    }

    public async Task UpdateAsync(RfqBid bid, CancellationToken cancellationToken = default)
    {
        _context.RfqBids.Update(bid);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public void Update(RfqBid bid)
    {
        _context.RfqBids.Update(bid);
    }

    public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var bid = await _context.RfqBids.FindAsync(new object[] { id }, cancellationToken);
        if (bid != null)
        {
            _context.RfqBids.Remove(bid);
            await _context.SaveChangesAsync(cancellationToken);
        }
    }

    public async Task<List<RfqBid>> GetBrokerBidsWithFiltersAsync(
        Guid brokerId,
        DateTime? fromDate,
        DateTime? toDate,
        List<Guid>? transportCompanyIds,
        List<LoadType>? loadTypes,
        List<string>? routes,
        CancellationToken cancellationToken = default)
    {
        var query = _context.RfqBids
            .Include(b => b.Rfq)
            .Where(b => b.BrokerId == brokerId);

        if (fromDate.HasValue)
            query = query.Where(b => b.SubmittedAt >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(b => b.SubmittedAt <= toDate.Value);

        if (transportCompanyIds?.Any() == true)
            query = query.Where(b => transportCompanyIds.Contains(b.Rfq.TransportCompanyId));

        if (loadTypes?.Any() == true)
            query = query.Where(b => loadTypes.Contains(b.Rfq.LoadDetails.LoadType));

        if (routes?.Any() == true)
        {
            query = query.Where(b => routes.Contains(
                b.Rfq.RouteDetails.PickupAddress.City + "-" + b.Rfq.RouteDetails.DeliveryAddress.City));
        }

        return await query.ToListAsync(cancellationToken);
    }
}
