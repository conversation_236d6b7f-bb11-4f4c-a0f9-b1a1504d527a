﻿using Shared.Domain.ValueObjects;
using Shared.Domain.Common;
using AuditCompliance.Domain.Enums;

namespace AuditCompliance.Domain.ValueObjects;

/// <summary>
/// Data retention policy configuration
/// </summary>
public class RetentionPolicy : ValueObject
{
    public TimeSpan RetentionPeriod { get; private set; }
    public bool AutoDelete { get; private set; }
    public bool RequireApproval { get; private set; }
    public List<ComplianceStandard> ApplicableStandards { get; private set; }
    public string? Description { get; private set; }

    private RetentionPolicy()
    {
        ApplicableStandards = new List<ComplianceStandard>();
    }

    public RetentionPolicy(
        TimeSpan retentionPeriod,
        bool autoDelete = false,
        bool requireApproval = true,
        List<ComplianceStandard>? applicableStandards = null,
        string? description = null)
    {
        if (retentionPeriod <= TimeSpan.Zero)
            throw new ArgumentException("Retention period must be positive", nameof(retentionPeriod));

        RetentionPeriod = retentionPeriod;
        AutoDelete = autoDelete;
        RequireApproval = requireApproval;
        ApplicableStandards = applicableStandards ?? new List<ComplianceStandard>();
        Description = description;
    }

    public static RetentionPolicy CreateDefault()
    {
        return new RetentionPolicy(
            TimeSpan.FromDays(2555), // 7 years
            autoDelete: false,
            requireApproval: true,
            new List<ComplianceStandard> { ComplianceStandard.DataRetention },
            "Default retention policy - 7 years with manual approval for deletion");
    }

    public static RetentionPolicy CreateForGDPR()
    {
        return new RetentionPolicy(
            TimeSpan.FromDays(1095), // 3 years
            autoDelete: true,
            requireApproval: false,
            new List<ComplianceStandard> { ComplianceStandard.GDPR },
            "GDPR compliant retention policy - 3 years with automatic deletion");
    }

    public static RetentionPolicy CreateForFinancialData()
    {
        return new RetentionPolicy(
            TimeSpan.FromDays(2555), // 7 years
            autoDelete: false,
            requireApproval: true,
            new List<ComplianceStandard> { ComplianceStandard.SOX, ComplianceStandard.AML },
            "Financial data retention policy - 7 years as per regulatory requirements");
    }

    public bool IsExpired(DateTime createdAt)
    {
        return DateTime.UtcNow - createdAt > RetentionPeriod;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return RetentionPeriod;
        yield return AutoDelete;
        yield return RequireApproval;
        yield return Description ?? string.Empty;
        
        foreach (var standard in ApplicableStandards.OrderBy(x => x))
        {
            yield return standard;
        }
    }
}


