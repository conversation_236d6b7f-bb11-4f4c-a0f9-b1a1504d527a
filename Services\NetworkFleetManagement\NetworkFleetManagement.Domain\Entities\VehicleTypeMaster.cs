using Shared.Domain;
using Shared.Domain.Common;
using NetworkFleetManagement.Domain.Events;

namespace NetworkFleetManagement.Domain.Entities;

/// <summary>
/// Master data entity for vehicle types
/// </summary>
public class VehicleTypeMaster : AggregateRoot
{
    public string Code { get; private set; } = string.Empty;
    public string Name { get; private set; } = string.Empty;
    public string Description { get; private set; } = string.Empty;
    public string Category { get; private set; } = string.Empty;
    public decimal? MinLoadCapacityKg { get; private set; }
    public decimal? MaxLoadCapacityKg { get; private set; }
    public decimal? MinVolumeCapacityM3 { get; private set; }
    public decimal? MaxVolumeCapacityM3 { get; private set; }
    public string? SpecialRequirements { get; private set; }
    public bool IsActive { get; private set; }
    public int SortOrder { get; private set; }
    public string? IconUrl { get; private set; }
    public Dictionary<string, object> AdditionalProperties { get; private set; } = new();

    // Private constructor for EF Core
    private VehicleTypeMaster() { }

    public VehicleTypeMaster(
        string code,
        string name,
        string description,
        string category,
        decimal? minLoadCapacityKg = null,
        decimal? maxLoadCapacityKg = null,
        decimal? minVolumeCapacityM3 = null,
        decimal? maxVolumeCapacityM3 = null,
        string? specialRequirements = null,
        int sortOrder = 0,
        string? iconUrl = null,
        Dictionary<string, object>? additionalProperties = null)
    {
        if (string.IsNullOrWhiteSpace(code))
            throw new ArgumentException("Vehicle type code cannot be empty", nameof(code));

        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Vehicle type name cannot be empty", nameof(name));

        if (string.IsNullOrWhiteSpace(category))
            throw new ArgumentException("Vehicle type category cannot be empty", nameof(category));

        Code = code.Trim().ToUpperInvariant();
        Name = name.Trim();
        Description = description?.Trim() ?? string.Empty;
        Category = category.Trim();
        MinLoadCapacityKg = minLoadCapacityKg;
        MaxLoadCapacityKg = maxLoadCapacityKg;
        MinVolumeCapacityM3 = minVolumeCapacityM3;
        MaxVolumeCapacityM3 = maxVolumeCapacityM3;
        SpecialRequirements = specialRequirements?.Trim();
        IsActive = true;
        SortOrder = sortOrder;
        IconUrl = iconUrl?.Trim();
        AdditionalProperties = additionalProperties ?? new Dictionary<string, object>();

        // Validate capacity ranges
        ValidateCapacityRanges();

        // Add domain event
        AddDomainEvent(new VehicleTypeMasterCreatedEvent(Id, Code, Name, Category));
    }

    public void UpdateDetails(
        string name,
        string description,
        string category,
        decimal? minLoadCapacityKg = null,
        decimal? maxLoadCapacityKg = null,
        decimal? minVolumeCapacityM3 = null,
        decimal? maxVolumeCapacityM3 = null,
        string? specialRequirements = null,
        int sortOrder = 0,
        string? iconUrl = null,
        Dictionary<string, object>? additionalProperties = null)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Vehicle type name cannot be empty", nameof(name));

        if (string.IsNullOrWhiteSpace(category))
            throw new ArgumentException("Vehicle type category cannot be empty", nameof(category));

        var oldName = Name;
        var oldCategory = Category;

        Name = name.Trim();
        Description = description?.Trim() ?? string.Empty;
        Category = category.Trim();
        MinLoadCapacityKg = minLoadCapacityKg;
        MaxLoadCapacityKg = maxLoadCapacityKg;
        MinVolumeCapacityM3 = minVolumeCapacityM3;
        MaxVolumeCapacityM3 = maxVolumeCapacityM3;
        SpecialRequirements = specialRequirements?.Trim();
        SortOrder = sortOrder;
        IconUrl = iconUrl?.Trim();

        if (additionalProperties != null)
        {
            AdditionalProperties = additionalProperties;
        }

        // Validate capacity ranges
        ValidateCapacityRanges();

        SetUpdatedAt();

        // Add domain event if significant changes
        if (oldName != Name || oldCategory != Category)
        {
            AddDomainEvent(new VehicleTypeMasterUpdatedEvent(Id, Code, Name, Category, oldName, oldCategory));
        }
    }

    public void Activate()
    {
        if (IsActive) return;

        IsActive = true;
        SetUpdatedAt();

        AddDomainEvent(new VehicleTypeMasterActivatedEvent(Id, Code, Name));
    }

    public void Deactivate()
    {
        if (!IsActive) return;

        IsActive = false;
        SetUpdatedAt();

        AddDomainEvent(new VehicleTypeMasterDeactivatedEvent(Id, Code, Name));
    }

    public void UpdateSortOrder(int newSortOrder)
    {
        if (SortOrder == newSortOrder) return;

        SortOrder = newSortOrder;
        SetUpdatedAt();
    }

    public void AddOrUpdateProperty(string key, object value)
    {
        if (string.IsNullOrWhiteSpace(key))
            throw new ArgumentException("Property key cannot be empty", nameof(key));

        AdditionalProperties[key] = value;
        SetUpdatedAt();
    }

    public void RemoveProperty(string key)
    {
        if (string.IsNullOrWhiteSpace(key)) return;

        if (AdditionalProperties.Remove(key))
        {
            SetUpdatedAt();
        }
    }

    public bool IsCapacityInRange(decimal loadCapacityKg, decimal volumeCapacityM3)
    {
        var loadInRange = (!MinLoadCapacityKg.HasValue || loadCapacityKg >= MinLoadCapacityKg.Value) &&
                         (!MaxLoadCapacityKg.HasValue || loadCapacityKg <= MaxLoadCapacityKg.Value);

        var volumeInRange = (!MinVolumeCapacityM3.HasValue || volumeCapacityM3 >= MinVolumeCapacityM3.Value) &&
                           (!MaxVolumeCapacityM3.HasValue || volumeCapacityM3 <= MaxVolumeCapacityM3.Value);

        return loadInRange && volumeInRange;
    }

    private void ValidateCapacityRanges()
    {
        if (MinLoadCapacityKg.HasValue && MaxLoadCapacityKg.HasValue && MinLoadCapacityKg.Value > MaxLoadCapacityKg.Value)
            throw new ArgumentException("Minimum load capacity cannot be greater than maximum load capacity");

        if (MinVolumeCapacityM3.HasValue && MaxVolumeCapacityM3.HasValue && MinVolumeCapacityM3.Value > MaxVolumeCapacityM3.Value)
            throw new ArgumentException("Minimum volume capacity cannot be greater than maximum volume capacity");

        if (MinLoadCapacityKg.HasValue && MinLoadCapacityKg.Value < 0)
            throw new ArgumentException("Load capacity cannot be negative");

        if (MinVolumeCapacityM3.HasValue && MinVolumeCapacityM3.Value < 0)
            throw new ArgumentException("Volume capacity cannot be negative");
    }
}


