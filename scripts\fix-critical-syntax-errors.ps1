#!/usr/bin/env pwsh

Write-Host "Fixing critical syntax errors..." -ForegroundColor Green

# Fix package version conflicts
Write-Host "Fixing package version conflicts..." -ForegroundColor Yellow

# Update MobileWorkflow.Infrastructure to use consistent package versions
$mobileInfraPath = "Services/MobileWorkflow/MobileWorkflow.Infrastructure/MobileWorkflow.Infrastructure.csproj"
if (Test-Path $mobileInfraPath) {
    $content = Get-Content $mobileInfraPath -Raw
    $content = $content -replace '<PackageReference Include="Microsoft\.Extensions\.Caching\.Memory" Version="8\.0\.0"', '<PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="8.0.1"'
    $content = $content -replace '<PackageReference Include="StackExchange\.Redis" Version="2\.7\.20"', '<PackageReference Include="StackExchange.Redis" Version="2.8.16"'
    Set-Content $mobileInfraPath $content -NoNewline
    Write-Host "Updated MobileWorkflow.Infrastructure package versions" -ForegroundColor Green
}

# Update MobileWorkflow.Tests to use consistent package versions
$mobileTestsPath = "Services/MobileWorkflow/MobileWorkflow.Tests/MobileWorkflow.Tests.csproj"
if (Test-Path $mobileTestsPath) {
    $content = Get-Content $mobileTestsPath -Raw
    $content = $content -replace '<PackageReference Include="Microsoft\.Extensions\.Caching\.Memory" Version="8\.0\.0"', '<PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="8.0.1"'
    Set-Content $mobileTestsPath $content -NoNewline
    Write-Host "Updated MobileWorkflow.Tests package versions" -ForegroundColor Green
}

# Fix missing ValueObject base class references
Write-Host "Adding missing ValueObject base class references..." -ForegroundColor Yellow

$valueObjectFiles = @(
    "Services/MobileWorkflow/MobileWorkflow.Domain/ValueObjects/DeviceInfo.cs",
    "Services/MobileWorkflow/MobileWorkflow.Domain/ValueObjects/SyncConfiguration.cs",
    "Services/CommunicationNotification/CommunicationNotification.Domain/ValueObjects/ABTestResults.cs"
)

foreach ($file in $valueObjectFiles) {
    if (Test-Path $file) {
        $content = Get-Content $file -Raw
        if ($content -notmatch "using Shared\.Domain\.ValueObjects;") {
            $content = $content -replace "(using [^;]+;)", "`$1`nusing Shared.Domain.ValueObjects;"
            Set-Content $file $content -NoNewline
            Write-Host "Added ValueObject using statement to $file" -ForegroundColor Green
        }
    }
}

Write-Host "Critical syntax error fixes completed!" -ForegroundColor Green
