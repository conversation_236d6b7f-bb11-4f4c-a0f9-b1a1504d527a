using AuditCompliance.Domain.Enums;

namespace AuditCompliance.Application.DTOs.Reports;

/// <summary>
/// DTO for feedback summary report
/// </summary>
public class FeedbackSummaryReportDto
{
    public Guid ReportId { get; set; }
    public string ReportName { get; set; } = "Feedback Summary Report";
    public DateTime GeneratedAt { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public FeedbackReportSummaryDto Summary { get; set; } = new();
    public List<CompanyFeedbackSummaryDto> CompanySummaries { get; set; } = new();
    public List<TagAnalysisDto> TagAnalysis { get; set; } = new();
    public List<RatingTrendDto> RatingTrends { get; set; } = new();
    public List<CompanyComparisonDto> CompanyComparisons { get; set; } = new();
    public List<DetailedFeedbackDto> DetailedFeedback { get; set; } = new();
    public ReportMetadataDto Metadata { get; set; } = new();
}

/// <summary>
/// DTO for feedback report summary
/// </summary>
public class FeedbackReportSummaryDto
{
    public int TotalFeedbackCount { get; set; }
    public int UniqueShippers { get; set; }
    public int UniqueTransportCompanies { get; set; }
    public decimal AverageRating { get; set; }
    public decimal MedianRating { get; set; }
    public Dictionary<int, int> RatingDistribution { get; set; } = new(); // Rating -> Count
    public Dictionary<RatingStatus, int> StatusDistribution { get; set; } = new();
    public Dictionary<string, int> TopTags { get; set; } = new();
    public decimal PositiveFeedbackPercentage { get; set; }
    public decimal NegativeFeedbackPercentage { get; set; }
    public DateTime ReportPeriodStart { get; set; }
    public DateTime ReportPeriodEnd { get; set; }
}

/// <summary>
/// DTO for company feedback summary
/// </summary>
public class CompanyFeedbackSummaryDto
{
    public Guid TransportCompanyId { get; set; }
    public string CompanyName { get; set; } = string.Empty;
    public int TotalFeedbackCount { get; set; }
    public decimal AverageRating { get; set; }
    public decimal MedianRating { get; set; }
    public Dictionary<int, int> RatingDistribution { get; set; } = new();
    public Dictionary<string, int> TagFrequency { get; set; } = new();
    public int PositiveFeedbackCount { get; set; }
    public int NegativeFeedbackCount { get; set; }
    public int NeutralFeedbackCount { get; set; }
    public decimal ImprovementTrend { get; set; } // Percentage change from previous period
    public DateTime LastFeedbackDate { get; set; }
    public List<string> TopPositiveTags { get; set; } = new();
    public List<string> TopNegativeTags { get; set; } = new();
    public decimal ResponseRate { get; set; } // Percentage of trips with feedback
}

/// <summary>
/// DTO for tag analysis
/// </summary>
public class TagAnalysisDto
{
    public string TagName { get; set; } = string.Empty;
    public string TagCategory { get; set; } = string.Empty;
    public string TagType { get; set; } = string.Empty; // Positive, Negative, Neutral
    public int UsageCount { get; set; }
    public decimal UsagePercentage { get; set; }
    public decimal AverageRatingWithTag { get; set; }
    public int UniqueCompanies { get; set; }
    public int UniqueShippers { get; set; }
    public Dictionary<string, int> CompanyUsage { get; set; } = new();
    public List<string> FrequentlyPairedTags { get; set; } = new();
    public decimal TrendPercentage { get; set; } // Change from previous period
}

/// <summary>
/// DTO for rating trends
/// </summary>
public class RatingTrendDto
{
    public DateTime Date { get; set; }
    public string Period { get; set; } = string.Empty; // Daily, Weekly, Monthly
    public decimal AverageRating { get; set; }
    public int FeedbackCount { get; set; }
    public int PositiveCount { get; set; }
    public int NegativeCount { get; set; }
    public int NeutralCount { get; set; }
    public decimal PositivePercentage { get; set; }
    public decimal NegativePercentage { get; set; }
    public decimal TrendChange { get; set; } // Change from previous period
    public Dictionary<string, int> TopTagsForPeriod { get; set; } = new();
}

/// <summary>
/// DTO for company comparison
/// </summary>
public class CompanyComparisonDto
{
    public Guid TransportCompanyId { get; set; }
    public string CompanyName { get; set; } = string.Empty;
    public decimal AverageRating { get; set; }
    public int TotalFeedback { get; set; }
    public decimal MarketSharePercentage { get; set; }
    public int RankByRating { get; set; }
    public int RankByVolume { get; set; }
    public decimal RatingVariance { get; set; }
    public decimal ConsistencyScore { get; set; }
    public List<string> StrengthAreas { get; set; } = new();
    public List<string> ImprovementAreas { get; set; } = new();
    public decimal BenchmarkDifference { get; set; } // Difference from industry average
}

/// <summary>
/// DTO for detailed feedback
/// </summary>
public class DetailedFeedbackDto
{
    public Guid FeedbackId { get; set; }
    public Guid ShipperId { get; set; }
    public string ShipperName { get; set; } = string.Empty;
    public Guid TransportCompanyId { get; set; }
    public string TransportCompanyName { get; set; } = string.Empty;
    public string? OrderNumber { get; set; }
    public string? TripNumber { get; set; }
    public decimal OverallRating { get; set; }
    public string? ReviewTitle { get; set; }
    public string? ReviewComment { get; set; }
    public List<string> Tags { get; set; } = new();
    public Dictionary<string, decimal> CategoryRatings { get; set; } = new();
    public RatingStatus Status { get; set; }
    public DateTime SubmittedAt { get; set; }
    public DateTime? ReviewedAt { get; set; }
    public bool IsAnonymous { get; set; }
    public string Sentiment { get; set; } = string.Empty; // Positive, Negative, Neutral
}

/// <summary>
/// DTO for report metadata
/// </summary>
public class ReportMetadataDto
{
    public Guid RequestedBy { get; set; }
    public string RequestedByRole { get; set; } = string.Empty;
    public DateTime RequestedAt { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
    public TimeSpan GenerationTime { get; set; }
    public int TotalRecords { get; set; }
    public bool IsFiltered { get; set; }
    public Dictionary<string, object> FilterCriteria { get; set; } = new();
    public string ReportVersion { get; set; } = "1.0";
    public string DataSource { get; set; } = "ServiceProviderRatings";
}
