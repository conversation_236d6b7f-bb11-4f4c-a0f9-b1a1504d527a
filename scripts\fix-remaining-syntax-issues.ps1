# Fix remaining syntax and missing type issues
Write-Host "Fixing remaining syntax and missing type issues..." -ForegroundColor Green

# Fix NetworkFleetManagement IPreferredPartnerRepository syntax error
Write-Host "Fixing NetworkFleetManagement IPreferredPartnerRepository syntax error..." -ForegroundColor Yellow
$preferredPartnerRepoFile = "Services\NetworkFleetManagement\NetworkFleetManagement.Domain\Repositories\IPreferredPartnerRepository.cs"
if (Test-Path $preferredPartnerRepoFile) {
    $content = Get-Content $preferredPartnerRepoFile -Raw
    
    # Fix any syntax errors at the end of the file
    $lines = $content -split "`n"
    $newLines = @()
    
    foreach ($line in $lines) {
        # Skip any malformed lines at the end
        if ($line -match "^\s*\}\s*$" -or $line -match "^\s*namespace" -or $line -match "^\s*using" -or $line -match "^\s*public" -or $line -match "^\s*interface" -or $line -match "^\s*Task" -or $line -match "^\s*//" -or $line.Trim() -eq "") {
            $newLines += $line
        }
    }
    
    # Ensure proper closing
    if ($newLines[-1] -notmatch "^\s*\}\s*$") {
        $newLines += "}"
    }
    
    $newContent = $newLines -join "`n"
    Set-Content -Path $preferredPartnerRepoFile -Value $newContent -Encoding UTF8
    Write-Host "  Fixed syntax error in IPreferredPartnerRepository.cs" -ForegroundColor Green
}

# Fix AnalyticsBIService Dashboard.cs method return type
Write-Host "Fixing AnalyticsBIService Dashboard.cs method return type..." -ForegroundColor Yellow
$dashboardFile = "Services\AnalyticsBIService\AnalyticsBIService.Domain\Entities\Dashboard.cs"
if (Test-Path $dashboardFile) {
    $content = Get-Content $dashboardFile -Raw
    
    # Fix method without return type
    $content = $content -replace "(\s+)(\w+\s*\([^)]*\)\s*\{)", "`$1public void `$2"
    
    Set-Content -Path $dashboardFile -Value $content -Encoding UTF8
    Write-Host "  Fixed method return type in Dashboard.cs" -ForegroundColor Green
}

# Fix TripManagement Application references
Write-Host "Fixing TripManagement Application references..." -ForegroundColor Yellow
$tripFiles = @(
    "Services\TripManagement\TripManagement.Domain\Entities\MilestoneConfirmationConfiguration.cs",
    "Services\TripManagement\TripManagement.Domain\Entities\Driver.cs"
)

foreach ($tripFile in $tripFiles) {
    if (Test-Path $tripFile) {
        $content = Get-Content $tripFile -Raw
        $modified = $false
        
        # Remove TripManagement.Application references
        if ($content -match "using TripManagement\.Application") {
            $content = $content -replace "using TripManagement\.Application[^;]*;", ""
            $modified = $true
        }
        
        # Fix Application type references
        if ($content -match "\bApplication\b" -and $content -notmatch "System\.Application") {
            $content = $content -replace "\bApplication\b", "// Application"
            $modified = $true
        }
        
        if ($modified) {
            Set-Content -Path $tripFile -Value $content -Encoding UTF8
            Write-Host "  Fixed Application references in $(Split-Path $tripFile -Leaf)" -ForegroundColor Green
        }
    }
}

# Fix UserManagement command handler syntax errors
Write-Host "Fixing UserManagement command handler syntax errors..." -ForegroundColor Yellow
$userMgmtFiles = @(
    "Services\UserManagement\UserManagement.Application\Commands\TransportCompanyLogo\UploadTransportCompanyLogoCommandHandler.cs",
    "Services\UserManagement\UserManagement.Application\Commands\TransportCompanyOCR\ProcessTransportCompanyDocumentWithOCRCommandHandler.cs"
)

foreach ($userFile in $userMgmtFiles) {
    if (Test-Path $userFile) {
        $content = Get-Content $userFile -Raw
        $modified = $false
        
        # Fix identifier expected errors (likely missing variable names)
        if ($content -match "catch\s*\(\s*Exception\s*\)") {
            $content = $content -replace "catch\s*\(\s*Exception\s*\)", "catch (Exception ex)"
            $modified = $true
        }
        
        # Fix any other syntax issues
        $content = $content -replace "catch\s*\(\s*([A-Za-z]+Exception)\s*\)", "catch (`$1 ex)"
        
        if ($modified) {
            Set-Content -Path $userFile -Value $content -Encoding UTF8
            Write-Host "  Fixed syntax errors in $(Split-Path $userFile -Leaf)" -ForegroundColor Green
        }
    }
}

# Fix Shared.Infrastructure PerformanceMetrics type conversion errors
Write-Host "Fixing Shared.Infrastructure PerformanceMetrics type conversion errors..." -ForegroundColor Yellow
$perfMetricsFile = "Shared\Shared.Infrastructure\Monitoring\PerformanceMetrics.cs"
if (Test-Path $perfMetricsFile) {
    $content = Get-Content $perfMetricsFile -Raw
    
    # Fix string to int conversion errors
    $content = $content -replace "Interlocked\.Increment\(([^)]+)\)", "Interlocked.Increment(ref `$1)"
    $content = $content -replace "Interlocked\.Decrement\(([^)]+)\)", "Interlocked.Decrement(ref `$1)"
    
    Set-Content -Path $perfMetricsFile -Value $content -Encoding UTF8
    Write-Host "  Fixed type conversion errors in PerformanceMetrics.cs" -ForegroundColor Green
}

# Fix Identity ambiguous repository references
Write-Host "Fixing Identity ambiguous repository references..." -ForegroundColor Yellow
$identityFiles = @(
    "Identity\Identity.Application\Roles\Commands\CreateRole\CreateRoleCommandHandler.cs",
    "Identity\Identity.Application\SubAdmins\Commands\CreateSubAdmin\CreateSubAdminCommandHandler.cs"
)

foreach ($identityFile in $identityFiles) {
    if (Test-Path $identityFile) {
        $content = Get-Content $identityFile -Raw
        $modified = $false
        
        # Use fully qualified names to resolve ambiguity
        if ($content -match "IRoleRepository" -and $content -notmatch "Identity\.Domain\.Repositories\.IRoleRepository") {
            $content = $content -replace "\bIRoleRepository\b", "Identity.Domain.Repositories.IRoleRepository"
            $modified = $true
        }
        
        if ($content -match "IPermissionRepository" -and $content -notmatch "Identity\.Domain\.Repositories\.IPermissionRepository") {
            $content = $content -replace "\bIPermissionRepository\b", "Identity.Domain.Repositories.IPermissionRepository"
            $modified = $true
        }
        
        if ($modified) {
            Set-Content -Path $identityFile -Value $content -Encoding UTF8
            Write-Host "  Fixed ambiguous references in $(Split-Path $identityFile -Leaf)" -ForegroundColor Green
        }
    }
}

# Add missing using statements for Entity and ValueObject
Write-Host "Adding missing using statements for Entity and ValueObject..." -ForegroundColor Yellow
$domainFiles = Get-ChildItem -Path "Services" -Recurse -Filter "*.cs" | Where-Object { $_.FullName -match "Domain" }

foreach ($file in $domainFiles) {
    $content = Get-Content $file.FullName -Raw
    $modified = $false
    
    # Check if file uses Entity but doesn't have using statement
    if ($content -match "\bEntity\b" -and $content -notmatch "using Shared\.Domain\.Common;" -and $content -notmatch "namespace.*\.Common") {
        $content = "using Shared.Domain.Common;`n" + $content
        $modified = $true
    }
    
    # Check if file uses ValueObject but doesn't have using statement
    if ($content -match "\bValueObject\b" -and $content -notmatch "using Shared\.Domain\.Common;" -and $content -notmatch "namespace.*\.Common") {
        if ($content -notmatch "using Shared\.Domain\.Common;") {
            $content = "using Shared.Domain.Common;`n" + $content
            $modified = $true
        }
    }
    
    if ($modified) {
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8
        Write-Host "  Added using statements to $(Split-Path $file.FullName -Leaf)" -ForegroundColor Cyan
    }
}

Write-Host "Remaining syntax issues fix completed!" -ForegroundColor Green
