using CommunicationNotification.Domain.Entities;
using CommunicationNotification.Infrastructure.Persistence.Configurations;
using Microsoft.EntityFrameworkCore;
using Shared.Domain.Common;

namespace CommunicationNotification.Infrastructure.Persistence;

/// <summary>
/// Communication and notification database context
/// </summary>
public class CommunicationDbContext : DbContext
{
    public CommunicationDbContext(DbContextOptions<CommunicationDbContext> options) : base(options)
    {
    }

    // Entity sets
    public DbSet<Notification> Notifications => Set<Notification>();
    public DbSet<Message> Messages => Set<Message>();
    public DbSet<UserPreference> UserPreferences => Set<UserPreference>();
    public DbSet<ConversationThread> ConversationThreads => Set<ConversationThread>();
    public DbSet<ConversationParticipant> ConversationParticipants => Set<ConversationParticipant>();
    public DbSet<Template> Templates => Set<Template>();
    public DbSet<TemplateVersion> TemplateVersions => Set<TemplateVersion>();
    public DbSet<DeliveryAttempt> DeliveryAttempts => Set<DeliveryAttempt>();
    public DbSet<MessageArchive> MessageArchives => Set<MessageArchive>();
    public DbSet<AuditLog> AuditLogs => Set<AuditLog>();
    public DbSet<ComplianceRecord> ComplianceRecords => Set<ComplianceRecord>();

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Apply entity configurations
        modelBuilder.ApplyConfiguration(new NotificationConfiguration());
        modelBuilder.ApplyConfiguration(new MessageConfiguration());
        modelBuilder.ApplyConfiguration(new UserPreferenceConfiguration());
        modelBuilder.ApplyConfiguration(new ConversationThreadConfiguration());
        modelBuilder.ApplyConfiguration(new ConversationParticipantConfiguration());
        modelBuilder.ApplyConfiguration(new TemplateConfiguration());
        modelBuilder.ApplyConfiguration(new TemplateVersionConfiguration());
        modelBuilder.ApplyConfiguration(new DeliveryAttemptConfiguration());
        modelBuilder.ApplyConfiguration(new MessageArchiveConfiguration());
        modelBuilder.ApplyConfiguration(new AuditLogConfiguration());
        modelBuilder.ApplyConfiguration(new ComplianceRecordConfiguration());

        // Configure schema
        modelBuilder.HasDefaultSchema("communication");

        // Configure value objects
        ConfigureValueObjects(modelBuilder);

        // Configure indexes for performance
        ConfigureIndexes(modelBuilder);

        // Configure TimescaleDB hypertables
        ConfigureTimescaleDB(modelBuilder);
    }

    private void ConfigureValueObjects(ModelBuilder modelBuilder)
    {
        // Configure MessageContent value object
        modelBuilder.Entity<Notification>()
            .OwnsOne(n => n.Content, content =>
            {
                content.Property(c => c.Subject).HasMaxLength(500);
                content.Property(c => c.Body).HasMaxLength(4000);
                content.Property(c => c.Language).HasConversion<string>();
            });

        modelBuilder.Entity<Message>()
            .OwnsOne(m => m.Content, content =>
            {
                content.Property(c => c.Subject).HasMaxLength(500);
                content.Property(c => c.Body).HasMaxLength(4000);
                content.Property(c => c.Language).HasConversion<string>();
            });

        // Configure ContactInfo value object
        modelBuilder.Entity<UserPreference>()
            .OwnsOne(up => up.ContactInfo, contact =>
            {
                contact.Property(c => c.PhoneNumber).HasMaxLength(20);
                contact.Property(c => c.Email).HasMaxLength(255);
                contact.Property(c => c.WhatsAppNumber).HasMaxLength(20);
                contact.Property(c => c.PushDeviceToken).HasMaxLength(500);
            });

        // Configure NotificationSettings value object
        modelBuilder.Entity<UserPreference>()
            .OwnsOne(up => up.NotificationSettings, settings =>
            {
                settings.Property(s => s.EnableSms).HasDefaultValue(true);
                settings.Property(s => s.EnableEmail).HasDefaultValue(true);
                settings.Property(s => s.EnablePush).HasDefaultValue(true);
                settings.Property(s => s.EnableWhatsApp).HasDefaultValue(true);
                settings.Property(s => s.EnableVoice).HasDefaultValue(false);
                settings.Property(s => s.QuietHoursStart).HasConversion<TimeSpan?>();
                settings.Property(s => s.QuietHoursEnd).HasConversion<TimeSpan?>();
                settings.Property(s => s.PreferredChannels).HasConversion(
                    v => string.Join(',', v.Select(e => e.ToString())),
                    v => v.Split(',', StringSplitOptions.RemoveEmptyEntries)
                          .Select(e => Enum.Parse<Domain.Enums.NotificationChannel>(e))
                          .ToList());
            });
    }

    private void ConfigureIndexes(ModelBuilder modelBuilder)
    {
        // Notification indexes
        modelBuilder.Entity<Notification>()
            .HasIndex(n => n.UserId)
            .HasDatabaseName("IX_Notifications_UserId");

        modelBuilder.Entity<Notification>()
            .HasIndex(n => n.Status)
            .HasDatabaseName("IX_Notifications_Status");

        modelBuilder.Entity<Notification>()
            .HasIndex(n => n.ScheduledAt)
            .HasDatabaseName("IX_Notifications_ScheduledAt");

        modelBuilder.Entity<Notification>()
            .HasIndex(n => new { n.UserId, n.Status })
            .HasDatabaseName("IX_Notifications_UserId_Status");

        // Message indexes
        modelBuilder.Entity<Message>()
            .HasIndex(m => m.SenderId)
            .HasDatabaseName("IX_Messages_SenderId");

        modelBuilder.Entity<Message>()
            .HasIndex(m => m.ReceiverId)
            .HasDatabaseName("IX_Messages_ReceiverId");

        modelBuilder.Entity<Message>()
            .HasIndex(m => m.ConversationThreadId)
            .HasDatabaseName("IX_Messages_ConversationThreadId");

        modelBuilder.Entity<Message>()
            .HasIndex(m => m.SentAt)
            .HasDatabaseName("IX_Messages_SentAt");

        modelBuilder.Entity<Message>()
            .HasIndex(m => new { m.SenderId, m.ReceiverId, m.SentAt })
            .HasDatabaseName("IX_Messages_SenderId_ReceiverId_SentAt");

        // UserPreference indexes
        modelBuilder.Entity<UserPreference>()
            .HasIndex(up => up.UserId)
            .IsUnique()
            .HasDatabaseName("IX_UserPreferences_UserId");

        // ConversationThread indexes
        modelBuilder.Entity<ConversationThread>()
            .HasIndex(ct => ct.CreatedBy)
            .HasDatabaseName("IX_ConversationThreads_CreatedBy");

        modelBuilder.Entity<ConversationThread>()
            .HasIndex(ct => ct.RelatedEntityId)
            .HasDatabaseName("IX_ConversationThreads_RelatedEntityId");

        modelBuilder.Entity<ConversationThread>()
            .HasIndex(ct => ct.IsActive)
            .HasDatabaseName("IX_ConversationThreads_IsActive");

        // ConversationParticipant indexes
        modelBuilder.Entity<ConversationParticipant>()
            .HasIndex(cp => cp.UserId)
            .HasDatabaseName("IX_ConversationParticipants_UserId");

        modelBuilder.Entity<ConversationParticipant>()
            .HasIndex(cp => cp.ConversationThreadId)
            .HasDatabaseName("IX_ConversationParticipants_ConversationThreadId");

        modelBuilder.Entity<ConversationParticipant>()
            .HasIndex(cp => new { cp.ConversationThreadId, cp.UserId })
            .IsUnique()
            .HasDatabaseName("IX_ConversationParticipants_ConversationThreadId_UserId");

        // Template indexes
        modelBuilder.Entity<Template>()
            .HasIndex(t => t.Name)
            .IsUnique()
            .HasDatabaseName("IX_Templates_Name");

        modelBuilder.Entity<Template>()
            .HasIndex(t => new { t.MessageType, t.Channel, t.Language })
            .HasDatabaseName("IX_Templates_MessageType_Channel_Language");

        // DeliveryAttempt indexes
        modelBuilder.Entity<DeliveryAttempt>()
            .HasIndex(da => da.NotificationId)
            .HasDatabaseName("IX_DeliveryAttempts_NotificationId");

        modelBuilder.Entity<DeliveryAttempt>()
            .HasIndex(da => da.AttemptedAt)
            .HasDatabaseName("IX_DeliveryAttempts_AttemptedAt");

        // AuditLog indexes
        modelBuilder.Entity<AuditLog>()
            .HasIndex(al => al.UserId)
            .HasDatabaseName("IX_AuditLogs_UserId");

        modelBuilder.Entity<AuditLog>()
            .HasIndex(al => al.Timestamp)
            .HasDatabaseName("IX_AuditLogs_Timestamp");

        modelBuilder.Entity<AuditLog>()
            .HasIndex(al => al.EntityType)
            .HasDatabaseName("IX_AuditLogs_EntityType");

        modelBuilder.Entity<AuditLog>()
            .HasIndex(al => al.EntityId)
            .HasDatabaseName("IX_AuditLogs_EntityId");
    }

    private void ConfigureTimescaleDB(ModelBuilder modelBuilder)
    {
        // Configure hypertables for time-series data
        // These will be created in migrations as raw SQL

        // Messages hypertable (partitioned by SentAt)
        modelBuilder.Entity<Message>()
            .ToTable("messages")
            .HasComment("Hypertable partitioned by sent_at for time-series optimization");

        // Notifications hypertable (partitioned by ScheduledAt)
        modelBuilder.Entity<Notification>()
            .ToTable("notifications")
            .HasComment("Hypertable partitioned by scheduled_at for time-series optimization");

        // DeliveryAttempts hypertable (partitioned by AttemptedAt)
        modelBuilder.Entity<DeliveryAttempt>()
            .ToTable("delivery_attempts")
            .HasComment("Hypertable partitioned by attempted_at for time-series optimization");

        // AuditLogs hypertable (partitioned by Timestamp)
        modelBuilder.Entity<AuditLog>()
            .ToTable("audit_logs")
            .HasComment("Hypertable partitioned by timestamp for time-series optimization");
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        // Update audit fields for BaseEntity
        foreach (var entry in ChangeTracker.Entries<BaseEntity>())
        {
            switch (entry.State)
            {
                case EntityState.Added:
                    entry.Entity.CreatedAt = DateTime.UtcNow;
                    entry.Entity.UpdatedAt = DateTime.UtcNow;
                    break;
                case EntityState.Modified:
                    entry.Entity.UpdatedAt = DateTime.UtcNow;
                    break;
            }
        }

        return await base.SaveChangesAsync(cancellationToken);
    }
}
