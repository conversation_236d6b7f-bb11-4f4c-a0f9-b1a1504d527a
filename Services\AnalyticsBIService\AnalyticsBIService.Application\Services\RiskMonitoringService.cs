using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Application.Interfaces;
using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Enums;
using AnalyticsBIService.Domain.Repositories;
using Microsoft.Extensions.Logging;
using Shared.Domain.Common;

namespace AnalyticsBIService.Application.Services;

/// <summary>
/// Service for risk monitoring, feedback analysis, and trend detection
/// </summary>
public interface IRiskMonitoringService
{
    Task<RiskMonitoringDto> GetRiskMonitoringDashboardAsync(Guid? userId = null, CancellationToken cancellationToken = default);
    Task<int> CountIssueFlaggedFeedbackAsync(Guid? entityId = null, string? entityType = null, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default);
    Task<List<RedFlagTrendDto>> DetectRedFlagTrendsAsync(Guid? entityId = null, string? entityType = null, int lookbackDays = 30, CancellationToken cancellationToken = default);
    Task<List<EntityPerformanceRankingDto>> GetEntityPerformanceRankingsAsync(string entityType, int topCount = 10, CancellationToken cancellationToken = default);
    Task<RiskAssessmentDto> AssessEntityRiskAsync(Guid entityId, string entityType, CancellationToken cancellationToken = default);
    Task<RiskAssessmentDto> GetRiskAssessmentAsync(Guid entityId, string entityType, CancellationToken cancellationToken = default);
    Task<List<RiskAlertDto>> GetActiveRiskAlertsAsync(Guid? userId = null, CancellationToken cancellationToken = default);
    Task ScheduleRiskAnalysisAsync(CancellationToken cancellationToken = default);
}

public class RiskMonitoringService : IRiskMonitoringService
{
    private readonly IAnalyticsEventRepository _analyticsEventRepository;
    private readonly IAlertRepository _alertRepository;
    private readonly ICacheService _cacheService;
    private readonly ILogger<RiskMonitoringService> _logger;

    private const string CACHE_KEY_PREFIX = "risk_monitoring";
    private static readonly TimeSpan DefaultCacheExpiration = TimeSpan.FromMinutes(15);
    private static readonly TimeSpan RiskAnalysisCacheExpiration = TimeSpan.FromHours(1);

    // Risk thresholds
    private const decimal CRITICAL_FEEDBACK_THRESHOLD = 0.3m; // 30% negative feedback
    private const decimal WARNING_FEEDBACK_THRESHOLD = 0.2m;  // 20% negative feedback
    private const int MIN_FEEDBACK_COUNT_FOR_ANALYSIS = 5;
    private const decimal RED_FLAG_TREND_THRESHOLD = 0.15m;   // 15% increase in negative feedback

    public RiskMonitoringService(
        IAnalyticsEventRepository analyticsEventRepository,
        IAlertRepository alertRepository,
        ICacheService cacheService,
        ILogger<RiskMonitoringService> logger)
    {
        _analyticsEventRepository = analyticsEventRepository;
        _alertRepository = alertRepository;
        _cacheService = cacheService;
        _logger = logger;
    }

    public async Task<RiskMonitoringDto> GetRiskMonitoringDashboardAsync(Guid? userId = null, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting risk monitoring dashboard for user {UserId}", userId);

            var cacheKey = GenerateCacheKey("dashboard", userId);
            var cachedDashboard = await _cacheService.GetAsync<RiskMonitoringDto>(cacheKey, cancellationToken);

            if (cachedDashboard != null)
            {
                return cachedDashboard;
            }

            var dashboard = new RiskMonitoringDto
            {
                GeneratedAt = DateTime.UtcNow,
                UserId = userId
            };

            // Get issue-flagged feedback counts
            dashboard.TotalIssueFlaggedFeedback = await CountIssueFlaggedFeedbackAsync(cancellationToken: cancellationToken);
            dashboard.RecentIssueFlaggedFeedback = await CountIssueFlaggedFeedbackAsync(
                fromDate: DateTime.UtcNow.AddDays(-7), cancellationToken: cancellationToken);

            // Detect red flag trends
            dashboard.RedFlagTrends = await DetectRedFlagTrendsAsync(cancellationToken: cancellationToken);

            // Get entity performance rankings
            dashboard.CarrierRankings = await GetEntityPerformanceRankingsAsync("Carrier", cancellationToken: cancellationToken);
            dashboard.BrokerRankings = await GetEntityPerformanceRankingsAsync("Broker", cancellationToken: cancellationToken);
            dashboard.ShipperRankings = await GetEntityPerformanceRankingsAsync("Shipper", cancellationToken: cancellationToken);

            // Get active risk alerts
            dashboard.ActiveRiskAlerts = await GetActiveRiskAlertsAsync(userId, cancellationToken);

            // Calculate risk summary statistics
            await CalculateRiskSummaryAsync(dashboard, cancellationToken);

            // Cache the results
            await _cacheService.SetAsync(cacheKey, dashboard, DefaultCacheExpiration, cancellationToken);

            return dashboard;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting risk monitoring dashboard for user {UserId}", userId);
            throw;
        }
    }

    public async Task<int> CountIssueFlaggedFeedbackAsync(Guid? entityId = null, string? entityType = null, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = GenerateCacheKey("issue_feedback_count", entityId, entityType, fromDate, toDate);
            var cachedCount = await _cacheService.GetAsync<int?>(cacheKey, cancellationToken);

            if (cachedCount.HasValue)
            {
                return cachedCount.Value;
            }

            var startDate = fromDate ?? DateTime.UtcNow.AddDays(-30);
            var endDate = toDate ?? DateTime.UtcNow;

            var events = await _analyticsEventRepository.GetByDateRangeAsync(startDate, endDate, 1, 1000, cancellationToken);

            var filteredEvents = events.Items
                .Where(e => e.EventName == "FeedbackSubmitted" &&
                           e.Properties.ContainsKey("isIssue") &&
                           e.GetProperty<bool>("isIssue"));

            if (entityId.HasValue)
            {
                filteredEvents = filteredEvents.Where(e => e.EntityId == entityId.Value);
            }

            if (!string.IsNullOrEmpty(entityType))
            {
                filteredEvents = filteredEvents.Where(e => e.EntityType == entityType);
            }

            var count = filteredEvents.Count();

            await _cacheService.SetAsync(cacheKey, count, DefaultCacheExpiration, cancellationToken);
            return count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error counting issue-flagged feedback");
            return 0;
        }
    }

    public async Task<List<RedFlagTrendDto>> DetectRedFlagTrendsAsync(Guid? entityId = null, string? entityType = null, int lookbackDays = 30, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = GenerateCacheKey("red_flag_trends", entityId, entityType, lookbackDays);
            var cachedTrends = await _cacheService.GetAsync<List<RedFlagTrendDto>>(cacheKey, cancellationToken);

            if (cachedTrends != null)
            {
                return cachedTrends;
            }

            _logger.LogDebug("Detecting red flag trends for entity {EntityId} of type {EntityType}", entityId, entityType);

            var trends = new List<RedFlagTrendDto>();
            var endDate = DateTime.UtcNow;
            var startDate = endDate.AddDays(-lookbackDays);
            var midDate = endDate.AddDays(-lookbackDays / 2);

            // Get feedback data for analysis
            var feedbackData = await GetFeedbackDataForTrendAnalysisAsync(entityId, entityType, startDate, endDate, cancellationToken);

            // Group entities for analysis
            var entityGroups = feedbackData.GroupBy(f => new { f.EntityId, f.EntityType });

            foreach (var entityGroup in entityGroups)
            {
                var firstHalfFeedback = entityGroup.Where(f => f.Timestamp < midDate).ToList();
                var secondHalfFeedback = entityGroup.Where(f => f.Timestamp >= midDate).ToList();

                if (firstHalfFeedback.Count >= MIN_FEEDBACK_COUNT_FOR_ANALYSIS &&
                    secondHalfFeedback.Count >= MIN_FEEDBACK_COUNT_FOR_ANALYSIS)
                {
                    var trend = AnalyzeFeedbackTrend(entityGroup.Key.EntityId, entityGroup.Key.EntityType,
                        firstHalfFeedback, secondHalfFeedback);

                    if (trend.IsRedFlag)
                    {
                        trends.Add(trend);
                    }
                }
            }

            // Sort by severity (highest trend increase first)
            trends = trends.OrderByDescending(t => t.TrendPercentage).ToList();

            await _cacheService.SetAsync(cacheKey, trends, RiskAnalysisCacheExpiration, cancellationToken);
            return trends;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error detecting red flag trends");
            return new List<RedFlagTrendDto>();
        }
    }

    public async Task<List<EntityPerformanceRankingDto>> GetEntityPerformanceRankingsAsync(string entityType, int topCount = 10, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = GenerateCacheKey("entity_rankings", entityType, topCount);
            var cachedRankings = await _cacheService.GetAsync<List<EntityPerformanceRankingDto>>(cacheKey, cancellationToken);

            if (cachedRankings != null)
            {
                return cachedRankings;
            }

            _logger.LogDebug("Getting entity performance rankings for type {EntityType}", entityType);

            var last30Days = DateTime.UtcNow.AddDays(-30);

            // Get feedback and performance data for entities
            var entityPerformanceData = await GetEntityPerformanceDataAsync(entityType, last30Days, cancellationToken);

            var rankings = entityPerformanceData
                .Where(e => e.TotalFeedbackCount >= MIN_FEEDBACK_COUNT_FOR_ANALYSIS)
                .Select((e, index) => new EntityPerformanceRankingDto
                {
                    EntityId = e.EntityId,
                    EntityType = entityType,
                    Rank = index + 1,
                    PerformanceScore = e.PerformanceScore,
                    TotalFeedbackCount = e.TotalFeedbackCount,
                    PositiveFeedbackPercentage = e.PositiveFeedbackPercentage,
                    NegativeFeedbackPercentage = e.NegativeFeedbackPercentage,
                    AverageRating = e.AverageRating,
                    RiskLevel = DetermineRiskLevel(e.NegativeFeedbackPercentage, e.TotalFeedbackCount),
                    LastUpdated = DateTime.UtcNow
                })
                .OrderByDescending(r => r.PerformanceScore)
                .Take(topCount)
                .ToList();

            await _cacheService.SetAsync(cacheKey, rankings, DefaultCacheExpiration, cancellationToken);
            return rankings;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting entity performance rankings for type {EntityType}", entityType);
            return new List<EntityPerformanceRankingDto>();
        }
    }

    public async Task<RiskAssessmentDto> AssessEntityRiskAsync(Guid entityId, string entityType, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = GenerateCacheKey("risk_assessment", entityId, entityType);
            var cachedAssessment = await _cacheService.GetAsync<RiskAssessmentDto>(cacheKey, cancellationToken);

            if (cachedAssessment != null)
            {
                return cachedAssessment;
            }

            _logger.LogDebug("Assessing risk for entity {EntityId} of type {EntityType}", entityId, entityType);

            var assessment = new RiskAssessmentDto
            {
                EntityId = entityId,
                EntityType = entityType,
                AssessmentDate = DateTime.UtcNow
            };

            var last30Days = DateTime.UtcNow.AddDays(-30);
            var last90Days = DateTime.UtcNow.AddDays(-90);

            // Calculate risk factors
            assessment.RecentNegativeFeedbackCount = await CountIssueFlaggedFeedbackAsync(entityId, entityType, last30Days, cancellationToken: cancellationToken);
            assessment.TotalFeedbackCount = await GetTotalFeedbackCountAsync(entityId, entityType, last30Days, cancellationToken);
            assessment.NegativeFeedbackPercentage = assessment.TotalFeedbackCount > 0
                ? (decimal)assessment.RecentNegativeFeedbackCount / assessment.TotalFeedbackCount * 100
                : 0;

            // Check for trend deterioration
            var trends = await DetectRedFlagTrendsAsync(entityId, entityType, 30, cancellationToken);
            assessment.HasNegativeTrend = trends.Any(t => t.EntityId == entityId);

            // Calculate performance metrics
            var performanceData = await GetEntityPerformanceDataAsync(entityType, last30Days, cancellationToken);
            var entityPerformance = performanceData.FirstOrDefault(p => p.EntityId == entityId);

            if (entityPerformance != null)
            {
                assessment.PerformanceScore = entityPerformance.PerformanceScore;
                assessment.AverageRating = entityPerformance.AverageRating;
            }

            // Determine overall risk level
            assessment.RiskLevel = CalculateOverallRiskLevel(assessment);
            assessment.RiskScore = CalculateRiskScore(assessment);

            // Generate risk factors and recommendations
            assessment.RiskFactors = GenerateRiskFactors(assessment);
            assessment.Recommendations = GenerateRiskRecommendations(assessment);

            await _cacheService.SetAsync(cacheKey, assessment, DefaultCacheExpiration, cancellationToken);
            return assessment;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assessing risk for entity {EntityId}", entityId);
            throw;
        }
    }

    public async Task<RiskAssessmentDto> GetRiskAssessmentAsync(Guid entityId, string entityType, CancellationToken cancellationToken = default)
    {
        // For now, delegate to AssessEntityRiskAsync
        return await AssessEntityRiskAsync(entityId, entityType, cancellationToken);
    }

    public async Task<List<RiskAlertDto>> GetActiveRiskAlertsAsync(Guid? userId = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = GenerateCacheKey("active_alerts", userId);
            var cachedAlerts = await _cacheService.GetAsync<List<RiskAlertDto>>(cacheKey, cancellationToken);

            if (cachedAlerts != null)
            {
                return cachedAlerts;
            }

            var alerts = new List<RiskAlertDto>();

            // Get alerts from the database
            var allAlerts = await _alertRepository.GetAllAsync(1, 100, cancellationToken);
            var dbAlerts = allAlerts.Items
                .Where(a => a.IsActive && !a.IsAcknowledged &&
                           (userId == null || a.UserId == userId) &&
                           (a.AlertType == AnalyticsBIService.Domain.Enums.AlertType.RiskAlert || a.AlertType == AnalyticsBIService.Domain.Enums.AlertType.QualityAlert))
                .OrderByDescending(a => a.TriggeredAt)
                .Take(50)
                .ToList();

            alerts = dbAlerts.Select(a => new RiskAlertDto
            {
                Id = a.Id,
                EntityId = a.EntityId,
                EntityType = a.EntityType,
                AlertType = a.AlertType.ToString(),
                Severity = a.Severity.ToString(),
                Title = a.Title,
                Message = a.Message,
                RiskLevel = DetermineRiskLevelFromSeverity(a.Severity),
                TriggeredAt = a.TriggeredAt,
                IsAcknowledged = a.IsAcknowledged,
                RecommendedActions = ExtractRecommendedActions(a.Context)
            }).ToList();

            await _cacheService.SetAsync(cacheKey, alerts, TimeSpan.FromMinutes(5), cancellationToken);
            return alerts;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active risk alerts");
            return new List<RiskAlertDto>();
        }
    }

    public async Task ScheduleRiskAnalysisAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Scheduling risk analysis jobs");

            // This would integrate with a background job scheduler
            // For now, we'll perform immediate analysis for demonstration
            await PerformScheduledRiskAnalysisAsync(cancellationToken);

            _logger.LogInformation("Risk analysis scheduling completed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error scheduling risk analysis");
            throw;
        }
    }

    // Private helper methods
    private async Task<List<FeedbackAnalysisData>> GetFeedbackDataForTrendAnalysisAsync(Guid? entityId, string? entityType, DateTime startDate, DateTime endDate, CancellationToken cancellationToken)
    {
        var allEvents = await _analyticsEventRepository.GetByDateRangeAsync(startDate, endDate, 1, 1000, cancellationToken);

        var filteredEvents = allEvents.Items
            .Where(e => e.EventName == "FeedbackSubmitted");

        if (entityId.HasValue)
        {
            filteredEvents = filteredEvents.Where(e => e.EntityId == entityId.Value);
        }

        if (!string.IsNullOrEmpty(entityType))
        {
            filteredEvents = filteredEvents.Where(e => e.EntityType == entityType);
        }

        var events = filteredEvents.ToList();

        return events.Select(e => new FeedbackAnalysisData
        {
            EntityId = e.EntityId ?? Guid.Empty,
            EntityType = e.EntityType ?? "Unknown",
            Timestamp = e.Timestamp,
            IsNegative = e.GetProperty<bool>("isIssue"),
            Rating = e.GetProperty<decimal?>("rating") ?? 0,
            Severity = e.GetProperty<string>("severity") ?? "Low"
        }).ToList();
    }

    private RedFlagTrendDto AnalyzeFeedbackTrend(Guid entityId, string entityType, List<FeedbackAnalysisData> firstHalf, List<FeedbackAnalysisData> secondHalf)
    {
        var firstHalfNegativeRate = firstHalf.Count > 0 ? (decimal)firstHalf.Count(f => f.IsNegative) / firstHalf.Count : 0;
        var secondHalfNegativeRate = secondHalf.Count > 0 ? (decimal)secondHalf.Count(f => f.IsNegative) / secondHalf.Count : 0;

        var trendPercentage = secondHalfNegativeRate - firstHalfNegativeRate;
        var isRedFlag = trendPercentage >= RED_FLAG_TREND_THRESHOLD && secondHalfNegativeRate >= WARNING_FEEDBACK_THRESHOLD;

        return new RedFlagTrendDto
        {
            EntityId = entityId,
            EntityType = entityType,
            TrendPercentage = trendPercentage * 100,
            CurrentNegativeFeedbackRate = secondHalfNegativeRate * 100,
            PreviousNegativeFeedbackRate = firstHalfNegativeRate * 100,
            IsRedFlag = isRedFlag,
            Severity = DetermineTrendSeverity(trendPercentage, secondHalfNegativeRate),
            DetectedAt = DateTime.UtcNow,
            RecommendedActions = GenerateTrendRecommendations(trendPercentage, secondHalfNegativeRate)
        };
    }

    // Additional helper methods (placeholder implementations)
    private async Task CalculateRiskSummaryAsync(RiskMonitoringDto dashboard, CancellationToken cancellationToken)
    {
        dashboard.HighRiskEntitiesCount = dashboard.CarrierRankings.Count(r => r.RiskLevel == "High") +
                                         dashboard.BrokerRankings.Count(r => r.RiskLevel == "High") +
                                         dashboard.ShipperRankings.Count(r => r.RiskLevel == "High");

        dashboard.CriticalAlertsCount = dashboard.ActiveRiskAlerts.Count(a => a.Severity == "Critical");
        dashboard.TrendingIssuesCount = dashboard.RedFlagTrends.Count(t => t.IsRedFlag);
    }

    private async Task<int> GetTotalFeedbackCountAsync(Guid entityId, string entityType, DateTime since, CancellationToken cancellationToken)
    {
        var events = await _analyticsEventRepository.GetByDateRangeAsync(since, DateTime.UtcNow, 1, int.MaxValue, cancellationToken);
        return events.Items
            .Where(e => e.EntityId == entityId && e.EntityType == entityType &&
                       e.EventName == "FeedbackSubmitted")
            .Count();
    }

    private async Task<List<EntityPerformanceData>> GetEntityPerformanceDataAsync(string entityType, DateTime since, CancellationToken cancellationToken)
    {
        var allEvents = await _analyticsEventRepository.GetByDateRangeAsync(since, DateTime.UtcNow, 1, int.MaxValue, cancellationToken);
        var feedbackEvents = allEvents.Items
            .Where(e => e.EntityType == entityType &&
                       e.EventName == "FeedbackSubmitted" && e.EntityId.HasValue)
            .ToList();

        return feedbackEvents
            .GroupBy(e => e.EntityId!.Value)
            .Select(g => new EntityPerformanceData
            {
                EntityId = g.Key,
                TotalFeedbackCount = g.Count(),
                PositiveFeedbackPercentage = g.Count(e => !e.GetProperty<bool>("isIssue")) * 100m / g.Count(),
                NegativeFeedbackPercentage = g.Count(e => e.GetProperty<bool>("isIssue")) * 100m / g.Count(),
                AverageRating = g.Where(e => e.Properties.ContainsKey("rating"))
                                 .Average(e => e.GetProperty<decimal>("rating")),
                PerformanceScore = CalculatePerformanceScore(g.ToList())
            })
            .OrderByDescending(p => p.PerformanceScore)
            .ToList();
    }

    private decimal CalculatePerformanceScore(List<AnalyticsEvent> feedbackEvents)
    {
        if (!feedbackEvents.Any()) return 0;

        var avgRating = feedbackEvents.Where(e => e.Properties.ContainsKey("rating"))
                                     .Average(e => e.GetProperty<decimal>("rating"));
        var negativePercentage = feedbackEvents.Count(e => e.GetProperty<bool>("isIssue")) * 100m / feedbackEvents.Count;

        // Score based on rating (0-5) and negative feedback percentage
        return (avgRating * 20) - negativePercentage; // Max 100, adjusted for negative feedback
    }

    private string DetermineRiskLevel(decimal negativeFeedbackPercentage, int totalFeedbackCount)
    {
        if (totalFeedbackCount < MIN_FEEDBACK_COUNT_FOR_ANALYSIS) return "Unknown";
        if (negativeFeedbackPercentage >= CRITICAL_FEEDBACK_THRESHOLD * 100) return "High";
        if (negativeFeedbackPercentage >= WARNING_FEEDBACK_THRESHOLD * 100) return "Medium";
        return "Low";
    }

    private string CalculateOverallRiskLevel(RiskAssessmentDto assessment)
    {
        var riskFactors = 0;

        if (assessment.NegativeFeedbackPercentage >= CRITICAL_FEEDBACK_THRESHOLD * 100) riskFactors += 3;
        else if (assessment.NegativeFeedbackPercentage >= WARNING_FEEDBACK_THRESHOLD * 100) riskFactors += 2;

        if (assessment.HasNegativeTrend) riskFactors += 2;
        if (assessment.PerformanceScore < 60) riskFactors += 1;
        if (assessment.AverageRating < 3.0m) riskFactors += 1;

        return riskFactors switch
        {
            >= 5 => "Critical",
            >= 3 => "High",
            >= 2 => "Medium",
            _ => "Low"
        };
    }

    private decimal CalculateRiskScore(RiskAssessmentDto assessment)
    {
        var score = 0m;
        score += assessment.NegativeFeedbackPercentage * 2; // Weight negative feedback heavily
        score += assessment.HasNegativeTrend ? 20 : 0;
        score += Math.Max(0, 100 - assessment.PerformanceScore); // Inverse of performance score
        score += Math.Max(0, (5 - assessment.AverageRating) * 10); // Inverse of rating

        return Math.Min(100, score); // Cap at 100
    }

    private List<string> GenerateRiskFactors(RiskAssessmentDto assessment)
    {
        var factors = new List<string>();

        if (assessment.NegativeFeedbackPercentage >= CRITICAL_FEEDBACK_THRESHOLD * 100)
            factors.Add($"High negative feedback rate: {assessment.NegativeFeedbackPercentage:F1}%");

        if (assessment.HasNegativeTrend)
            factors.Add("Deteriorating feedback trend detected");

        if (assessment.PerformanceScore < 60)
            factors.Add($"Low performance score: {assessment.PerformanceScore:F1}");

        if (assessment.AverageRating < 3.0m)
            factors.Add($"Low average rating: {assessment.AverageRating:F1}/5");

        return factors;
    }

    private List<string> GenerateRiskRecommendations(RiskAssessmentDto assessment)
    {
        var recommendations = new List<string>();

        if (assessment.NegativeFeedbackPercentage >= WARNING_FEEDBACK_THRESHOLD * 100)
            recommendations.Add("Implement immediate quality improvement measures");

        if (assessment.HasNegativeTrend)
            recommendations.Add("Investigate root causes of declining performance");

        if (assessment.PerformanceScore < 70)
            recommendations.Add("Provide additional training and support");

        recommendations.Add("Increase monitoring frequency");
        recommendations.Add("Schedule performance review meeting");

        return recommendations;
    }

    private string DetermineTrendSeverity(decimal trendPercentage, decimal currentRate)
    {
        if (trendPercentage >= 0.25m || currentRate >= CRITICAL_FEEDBACK_THRESHOLD) return "Critical";
        if (trendPercentage >= RED_FLAG_TREND_THRESHOLD || currentRate >= WARNING_FEEDBACK_THRESHOLD) return "High";
        return "Medium";
    }

    private List<string> GenerateTrendRecommendations(decimal trendPercentage, decimal currentRate)
    {
        var recommendations = new List<string>();

        if (trendPercentage >= 0.25m)
            recommendations.Add("Immediate intervention required - escalate to management");

        recommendations.Add("Analyze recent feedback for common issues");
        recommendations.Add("Implement corrective action plan");

        if (currentRate >= CRITICAL_FEEDBACK_THRESHOLD)
            recommendations.Add("Consider temporary suspension pending investigation");

        return recommendations;
    }

    private string DetermineRiskLevelFromSeverity(AlertSeverity severity)
    {
        return severity switch
        {
            AlertSeverity.Critical => "Critical",
            AlertSeverity.High => "High",
            AlertSeverity.Medium => "Medium",
            _ => "Low"
        };
    }

    private List<string> ExtractRecommendedActions(Dictionary<string, object> context)
    {
        if (context.TryGetValue("recommendedActions", out var actions) && actions is List<string> actionList)
        {
            return actionList;
        }
        return new List<string> { "Review and investigate the alert" };
    }

    private async Task PerformScheduledRiskAnalysisAsync(CancellationToken cancellationToken)
    {
        // This would perform comprehensive risk analysis
        _logger.LogInformation("Performing scheduled risk analysis");

        // Analyze trends for all entity types
        await DetectRedFlagTrendsAsync(cancellationToken: cancellationToken);

        // Update entity rankings
        await GetEntityPerformanceRankingsAsync("Carrier", cancellationToken: cancellationToken);
        await GetEntityPerformanceRankingsAsync("Broker", cancellationToken: cancellationToken);
        await GetEntityPerformanceRankingsAsync("Shipper", cancellationToken: cancellationToken);
    }

    private string GenerateCacheKey(string prefix, params object?[] parameters)
    {
        var keyParts = new List<string> { CACHE_KEY_PREFIX, prefix };

        foreach (var param in parameters)
        {
            if (param != null)
            {
                keyParts.Add(param.ToString()!);
            }
        }

        return string.Join(":", keyParts);
    }
}

// Supporting classes
public class FeedbackAnalysisData
{
    public Guid EntityId { get; set; }
    public string EntityType { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public bool IsNegative { get; set; }
    public decimal Rating { get; set; }
    public string Severity { get; set; } = string.Empty;
}

public class EntityPerformanceData
{
    public Guid EntityId { get; set; }
    public int TotalFeedbackCount { get; set; }
    public decimal PositiveFeedbackPercentage { get; set; }
    public decimal NegativeFeedbackPercentage { get; set; }
    public decimal AverageRating { get; set; }
    public decimal PerformanceScore { get; set; }
}

