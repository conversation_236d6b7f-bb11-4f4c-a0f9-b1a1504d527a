using MediatR;
using FinancialPayment.Domain.ValueObjects;
using FinancialPayment.Application.DTOs;

namespace FinancialPayment.Application.Commands.CreateEscrowAccount;

public class CreateEscrowAccountCommand : IRequest<Guid>
{
    public Guid OrderId { get; set; }
    public Guid TransportCompanyId { get; set; }
    public Guid BrokerId { get; set; }
    public Guid CarrierId { get; set; }
    public CreateMoneyDto TotalAmount { get; set; } = new();
    public string? Notes { get; set; }
}


