using ContentManagement.Application.DTOs;
using ContentManagement.Domain.Enums;
using MediatR;

namespace ContentManagement.Application.Queries.Content;

/// <summary>
/// Query to get content by ID
/// </summary>
public class GetContentByIdQuery : IRequest<ContentDto?>
{
    public Guid Id { get; set; }
    public bool IncludeVersions { get; set; } = false;
    public bool IncludeAttachments { get; set; } = false;
    
    // User context for visibility checks
    public List<string> UserRoles { get; set; } = new();
    public bool IsAuthenticated { get; set; } = false;
}

/// <summary>
/// Query to get content by slug
/// </summary>
public class GetContentBySlugQuery : IRequest<ContentDto?>
{
    public string Slug { get; set; } = string.Empty;
    public bool IncludeVersions { get; set; } = false;
    public bool IncludeAttachments { get; set; } = false;
    
    // User context for visibility checks
    public List<string> UserRoles { get; set; } = new();
    public bool IsAuthenticated { get; set; } = false;
}

/// <summary>
/// Query to search content with filters
/// </summary>
public class SearchContentQuery : IRequest<ContentSearchResultDto>
{
    public string? SearchTerm { get; set; }
    public ContentType? Type { get; set; }
    public ContentStatus? Status { get; set; }
    public List<string>? Tags { get; set; }
    public Guid? CreatedBy { get; set; }
    public DateTime? CreatedFrom { get; set; }
    public DateTime? CreatedTo { get; set; }
    public DateTime? UpdatedFrom { get; set; }
    public DateTime? UpdatedTo { get; set; }
    public string? SortBy { get; set; } = "CreatedAt";
    public bool SortDescending { get; set; } = true;
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 20;
    
    // User context for visibility checks
    public List<string> UserRoles { get; set; } = new();
    public bool IsAuthenticated { get; set; } = false;
}

/// <summary>
/// Query to get content versions
/// </summary>
public class GetContentVersionsQuery : IRequest<List<ContentVersionDto>>
{
    public Guid ContentId { get; set; }
    public bool PublishedOnly { get; set; } = false;
}

/// <summary>
/// Query to get content by version number
/// </summary>
public class GetContentVersionQuery : IRequest<ContentVersionDto?>
{
    public Guid ContentId { get; set; }
    public int VersionNumber { get; set; }
}

/// <summary>
/// Query to get content pending approval
/// </summary>
public class GetContentPendingApprovalQuery : IRequest<ContentSearchResultDto>
{
    public ContentType? Type { get; set; }
    public Guid? CreatedBy { get; set; }
    public DateTime? SubmittedFrom { get; set; }
    public DateTime? SubmittedTo { get; set; }
    public string? SortBy { get; set; } = "UpdatedAt";
    public bool SortDescending { get; set; } = true;
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 20;
}

/// <summary>
/// Query to get content statistics
/// </summary>
public class GetContentStatisticsQuery : IRequest<ContentStatisticsDto>
{
    public ContentType? Type { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
}

/// <summary>
/// Content statistics DTO
/// </summary>
public class ContentStatisticsDto
{
    public int TotalContent { get; set; }
    public int PublishedContent { get; set; }
    public int DraftContent { get; set; }
    public int PendingReviewContent { get; set; }
    public int ArchivedContent { get; set; }
    public Dictionary<ContentType, int> ContentByType { get; set; } = new();
    public Dictionary<ContentStatus, int> ContentByStatus { get; set; } = new();
    public Dictionary<string, int> ContentByCreator { get; set; } = new();
    public DateTime? LastUpdated { get; set; }
}
