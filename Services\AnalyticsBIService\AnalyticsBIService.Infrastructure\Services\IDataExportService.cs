using AnalyticsBIService.Domain.Enums;

namespace AnalyticsBIService.Infrastructure.Services;

/// <summary>
/// Interface for data export service
/// </summary>
public interface IDataExportService
{
    /// <summary>
    /// Export data to Excel format
    /// </summary>
    Task<ExportResultDto> ExportToExcelAsync(ExportRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Export data to PDF format
    /// </summary>
    Task<ExportResultDto> ExportToPdfAsync(ExportRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Export data to CSV format
    /// </summary>
    Task<ExportResultDto> ExportToCsvAsync(ExportRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Export data to JSON format
    /// </summary>
    Task<ExportResultDto> ExportToJsonAsync(ExportRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Export data to XML format
    /// </summary>
    Task<ExportResultDto> ExportToXmlAsync(ExportRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Export dashboard as image
    /// </summary>
    Task<ExportResultDto> ExportDashboardAsImageAsync(DashboardExportRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Export chart as image
    /// </summary>
    Task<ExportResultDto> ExportChartAsImageAsync(ChartExportRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Bulk export multiple datasets
    /// </summary>
    Task<BulkExportResultDto> BulkExportAsync(BulkExportRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get export status
    /// </summary>
    Task<ExportStatusDto> GetExportStatusAsync(Guid exportId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get export history for a user
    /// </summary>
    Task<List<ExportHistoryDto>> GetExportHistoryAsync(Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Download exported file
    /// </summary>
    Task<FileDownloadDto> DownloadExportAsync(Guid exportId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Schedule recurring export
    /// </summary>
    Task<ScheduledExportDto> ScheduleRecurringExportAsync(ScheduleExportRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Cancel export job
    /// </summary>
    Task<bool> CancelExportAsync(Guid exportId, Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get available export templates
    /// </summary>
    Task<List<ExportTemplateDto>> GetExportTemplatesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate export request
    /// </summary>
    Task<ExportValidationResult> ValidateExportRequestAsync(ExportRequest request, CancellationToken cancellationToken = default);
}

/// <summary>
/// Export request data transfer object
/// </summary>
public class ExportRequest
{
    public Guid UserId { get; set; }
    public UserType UserType { get; set; }
    public string DataSource { get; set; } = string.Empty;
    public List<string> Fields { get; set; } = new();
    public List<ExportFilterDto> Filters { get; set; } = new();
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public string Format { get; set; } = "CSV";
    public ExportOptionsDto Options { get; set; } = new();
    public string FileName { get; set; } = string.Empty;
    public bool IncludeHeaders { get; set; } = true;
    public int? MaxRows { get; set; }
    public string Compression { get; set; } = "None";
}

/// <summary>
/// Export filter data transfer object
/// </summary>
public class ExportFilterDto
{
    public string Field { get; set; } = string.Empty;
    public string Operator { get; set; } = string.Empty;
    public object Value { get; set; } = new();
    public string LogicalOperator { get; set; } = "AND";
}

/// <summary>
/// Export options data transfer object
/// </summary>
public class ExportOptionsDto
{
    public string DateFormat { get; set; } = "yyyy-MM-dd";
    public string NumberFormat { get; set; } = "0.00";
    public string Delimiter { get; set; } = ",";
    public string Encoding { get; set; } = "UTF-8";
    public bool IncludeMetadata { get; set; } = true;
    public bool IncludeHeaders { get; set; } = true;
    public Dictionary<string, object> CustomOptions { get; set; } = new();
    public ExportFormattingDto Formatting { get; set; } = new();
}

/// <summary>
/// Export formatting data transfer object
/// </summary>
public class ExportFormattingDto
{
    public string FontFamily { get; set; } = "Arial";
    public int FontSize { get; set; } = 12;
    public string HeaderColor { get; set; } = "#000000";
    public string BackgroundColor { get; set; } = "#FFFFFF";
    public bool AlternateRowColors { get; set; } = true;
    public Dictionary<string, string> ColumnFormats { get; set; } = new();
}

/// <summary>
/// Dashboard export request data transfer object
/// </summary>
public class DashboardExportRequest
{
    public Guid DashboardId { get; set; }
    public Guid UserId { get; set; }
    public string Format { get; set; } = "PNG";
    public int Width { get; set; } = 1920;
    public int Height { get; set; } = 1080;
    public int Quality { get; set; } = 90;
    public bool IncludeFilters { get; set; } = true;
    public Dictionary<string, object> FilterValues { get; set; } = new();
    public string Theme { get; set; } = "Default";
}

/// <summary>
/// Chart export request data transfer object
/// </summary>
public class ChartExportRequest
{
    public Guid ChartId { get; set; }
    public Guid UserId { get; set; }
    public string Format { get; set; } = "PNG";
    public int Width { get; set; } = 800;
    public int Height { get; set; } = 600;
    public int Quality { get; set; } = 90;
    public bool IncludeLegend { get; set; } = true;
    public bool IncludeTitle { get; set; } = true;
    public string BackgroundColor { get; set; } = "#FFFFFF";
}

/// <summary>
/// Bulk export request data transfer object
/// </summary>
public class BulkExportRequest
{
    public Guid UserId { get; set; }
    public List<ExportRequest> Exports { get; set; } = new();
    public string PackageFormat { get; set; } = "ZIP";
    public string PackageName { get; set; } = string.Empty;
    public bool NotifyOnCompletion { get; set; } = true;
    public string DeliveryMethod { get; set; } = "Download";
    public Dictionary<string, object> DeliveryOptions { get; set; } = new();
}

/// <summary>
/// Schedule export request data transfer object
/// </summary>
public class ScheduleExportRequest
{
    public Guid UserId { get; set; }
    public ExportRequest ExportRequest { get; set; } = new();
    public string ScheduleType { get; set; } = "Daily";
    public string CronExpression { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public string DeliveryMethod { get; set; } = "Email";
    public Dictionary<string, object> DeliveryOptions { get; set; } = new();
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// Export result data transfer object
/// </summary>
public class ExportResultDto
{
    public Guid ExportId { get; set; }
    public string Status { get; set; } = string.Empty;
    public string FileName { get; set; } = string.Empty;
    public string FilePath { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public string Format { get; set; } = string.Empty;
    public int RecordCount { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public TimeSpan? ProcessingTime { get; set; }
    public string? ErrorMessage { get; set; }
    public ExportMetadataDto Metadata { get; set; } = new();
    public string DownloadUrl { get; set; } = string.Empty;
    public DateTime? ExpiresAt { get; set; }
    // Missing properties that services are trying to access
    public string FileUrl { get; set; } = string.Empty;
    public long FileSizeBytes { get; set; }
    public DateTime GeneratedAt { get; set; }
}

/// <summary>
/// Export metadata data transfer object
/// </summary>
public class ExportMetadataDto
{
    public string DataSource { get; set; } = string.Empty;
    public List<string> Fields { get; set; } = new();
    public DateTime? DataPeriodStart { get; set; }
    public DateTime? DataPeriodEnd { get; set; }
    public List<ExportFilterDto> AppliedFilters { get; set; } = new();
    public Dictionary<string, object> Statistics { get; set; } = new();
    public string GeneratedBy { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
}

/// <summary>
/// Bulk export result data transfer object
/// </summary>
public class BulkExportResultDto
{
    public Guid BulkExportId { get; set; }
    public string Status { get; set; } = string.Empty;
    public List<ExportResultDto> Exports { get; set; } = new();
    public string PackageFileName { get; set; } = string.Empty;
    public string PackageFilePath { get; set; } = string.Empty;
    public long PackageFileSize { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public TimeSpan? ProcessingTime { get; set; }
    public int SuccessfulExports { get; set; }
    public int FailedExports { get; set; }
    public string DownloadUrl { get; set; } = string.Empty;
}

/// <summary>
/// Export status data transfer object
/// </summary>
public class ExportStatusDto
{
    public Guid ExportId { get; set; }
    public string Status { get; set; } = string.Empty;
    public decimal ProgressPercentage { get; set; }
    public string CurrentStep { get; set; } = string.Empty;
    public int ProcessedRecords { get; set; }
    public int TotalRecords { get; set; }
    public DateTime StartedAt { get; set; }
    public TimeSpan ElapsedTime { get; set; }
    public TimeSpan? EstimatedTimeRemaining { get; set; }
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// Export history data transfer object
/// </summary>
public class ExportHistoryDto
{
    public Guid ExportId { get; set; }
    public string FileName { get; set; } = string.Empty;
    public string Format { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public int RecordCount { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public bool IsAvailable { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public string DataSource { get; set; } = string.Empty;
}

/// <summary>
/// File download data transfer object
/// </summary>
public class FileDownloadDto
{
    public string FileName { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    public byte[] FileContent { get; set; } = Array.Empty<byte>();
    public long FileSize { get; set; }
    public DateTime LastModified { get; set; }
}

/// <summary>
/// Scheduled export data transfer object
/// </summary>
public class ScheduledExportDto
{
    public Guid ScheduleId { get; set; }
    public string Name { get; set; } = string.Empty;
    public ExportRequest ExportRequest { get; set; } = new();
    public string ScheduleType { get; set; } = string.Empty;
    public string CronExpression { get; set; } = string.Empty;
    public DateTime NextRunDate { get; set; }
    public DateTime? LastRunDate { get; set; }
    public string Status { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public List<ScheduledExportRunDto> RecentRuns { get; set; } = new();
}

/// <summary>
/// Scheduled export run data transfer object
/// </summary>
public class ScheduledExportRunDto
{
    public Guid RunId { get; set; }
    public DateTime RunDate { get; set; }
    public string Status { get; set; } = string.Empty;
    public Guid? ExportId { get; set; }
    public string? ErrorMessage { get; set; }
    public TimeSpan? Duration { get; set; }
}

/// <summary>
/// Export template data transfer object
/// </summary>
public class ExportTemplateDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string DataSource { get; set; } = string.Empty;
    public List<string> DefaultFields { get; set; } = new();
    public ExportOptionsDto DefaultOptions { get; set; } = new();
    public bool IsCustomizable { get; set; }
    public string PreviewImage { get; set; } = string.Empty;
}

/// <summary>
/// Export validation result data transfer object
/// </summary>
public class ExportValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public EstimatedExportSizeDto EstimatedSize { get; set; } = new();
    public TimeSpan EstimatedDuration { get; set; }
}

/// <summary>
/// Estimated export size data transfer object
/// </summary>
public class EstimatedExportSizeDto
{
    public int EstimatedRecords { get; set; }
    public long EstimatedFileSizeBytes { get; set; }
    public string EstimatedFileSizeFormatted { get; set; } = string.Empty;
    public bool ExceedsLimits { get; set; }
    public string? LimitExceededMessage { get; set; }
}
