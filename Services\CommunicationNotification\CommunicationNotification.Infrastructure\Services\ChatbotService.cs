using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Domain.Entities;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace CommunicationNotification.Infrastructure.Services;

/// <summary>
/// Service for AI-powered chatbot management and conversation handling
/// </summary>
public class ChatbotService : IChatbotService
{
    private readonly IChatbotRepository _chatbotRepository;
    private readonly IChatbotConversationRepository _conversationRepository;
    private readonly INLPService _nlpService;
    private readonly IConversationFlowService _conversationFlowService;
    private readonly IDistributedCache _cache;
    private readonly ILogger<ChatbotService> _logger;
    private readonly TimeSpan _cacheExpiration = TimeSpan.FromMinutes(30);

    public ChatbotService(
        IChatbotRepository chatbotRepository,
        IChatbotConversationRepository conversationRepository,
        INLPService nlpService,
        IConversationFlowService conversationFlowService,
        IDistributedCache cache,
        ILogger<ChatbotService> logger)
    {
        _chatbotRepository = chatbotRepository;
        _conversationRepository = conversationRepository;
        _nlpService = nlpService;
        _conversationFlowService = conversationFlowService;
        _cache = cache;
        _logger = logger;
    }

    public async Task<Chatbot> CreateChatbotAsync(
        string name,
        string description,
        ChatbotType type,
        string defaultLanguage,
        string welcomeMessage,
        Guid createdByUserId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating chatbot {Name} by user {UserId}", name, createdByUserId);

            // Check if name already exists
            if (await _chatbotRepository.ExistsAsync(name, cancellationToken))
            {
                throw new InvalidOperationException($"Chatbot with name '{name}' already exists");
            }

            var chatbot = Chatbot.Create(name, description, type, defaultLanguage, welcomeMessage, createdByUserId);
            var createdChatbot = await _chatbotRepository.AddAsync(chatbot, cancellationToken);

            _logger.LogInformation("Successfully created chatbot {ChatbotId} with name {Name}",
                createdChatbot.Id, createdChatbot.Name);

            return createdChatbot;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating chatbot {Name}", name);
            throw;
        }
    }

    public async Task<Chatbot?> GetChatbotAsync(
        Guid chatbotId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting chatbot {ChatbotId}", chatbotId);

            var cacheKey = $"chatbot_{chatbotId}";
            var cachedChatbot = await GetFromCacheAsync<Chatbot>(cacheKey, cancellationToken);
            if (cachedChatbot != null)
            {
                return cachedChatbot;
            }

            var chatbot = await _chatbotRepository.GetByIdAsync(chatbotId, cancellationToken);
            if (chatbot != null)
            {
                await SetCacheAsync(cacheKey, chatbot, cancellationToken);
            }

            return chatbot;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting chatbot {ChatbotId}", chatbotId);
            throw;
        }
    }

    public async Task<Chatbot> UpdateChatbotAsync(
        Guid chatbotId,
        Chatbot updatedChatbot,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Updating chatbot {ChatbotId}", chatbotId);

            var chatbot = await _chatbotRepository.UpdateAsync(updatedChatbot, cancellationToken);
            await InvalidateChatbotCacheAsync(chatbotId, cancellationToken);

            _logger.LogInformation("Successfully updated chatbot {ChatbotId}", chatbotId);
            return chatbot;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating chatbot {ChatbotId}", chatbotId);
            throw;
        }
    }

    public async Task DeleteChatbotAsync(
        Guid chatbotId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Deleting chatbot {ChatbotId}", chatbotId);

            await _chatbotRepository.DeleteAsync(chatbotId, cancellationToken);
            await InvalidateChatbotCacheAsync(chatbotId, cancellationToken);

            _logger.LogInformation("Successfully deleted chatbot {ChatbotId}", chatbotId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting chatbot {ChatbotId}", chatbotId);
            throw;
        }
    }

    public async Task<List<Chatbot>> GetAllChatbotsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting all chatbots");

            var cacheKey = "all_chatbots";
            var cachedChatbots = await GetFromCacheAsync<List<Chatbot>>(cacheKey, cancellationToken);
            if (cachedChatbots != null)
            {
                return cachedChatbots;
            }

            var chatbots = await _chatbotRepository.GetAllAsync(cancellationToken);
            await SetCacheAsync(cacheKey, chatbots, cancellationToken);

            _logger.LogDebug("Retrieved {Count} chatbots", chatbots.Count);
            return chatbots;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all chatbots");
            throw;
        }
    }

    public async Task<bool> TrainChatbotAsync(
        Guid chatbotId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Training chatbot {ChatbotId}", chatbotId);

            var chatbot = await _chatbotRepository.GetByIdAsync(chatbotId, cancellationToken);
            if (chatbot == null)
            {
                throw new ArgumentException($"Chatbot with ID {chatbotId} not found");
            }

            chatbot.Train();
            await _chatbotRepository.UpdateAsync(chatbot, cancellationToken);

            // Train the NLP model
            var trainingResult = await _nlpService.TrainModelAsync(
                chatbot.Intents, chatbot.Entities, chatbotId.ToString(), cancellationToken);

            if (trainingResult)
            {
                chatbot.CompleteTraining();
                await _chatbotRepository.UpdateAsync(chatbot, cancellationToken);
                await InvalidateChatbotCacheAsync(chatbotId, cancellationToken);

                _logger.LogInformation("Successfully trained chatbot {ChatbotId}", chatbotId);
                return true;
            }
            else
            {
                _logger.LogError("Failed to train NLP model for chatbot {ChatbotId}", chatbotId);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error training chatbot {ChatbotId}", chatbotId);
            throw;
        }
    }

    public async Task<Chatbot> ActivateChatbotAsync(
        Guid chatbotId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Activating chatbot {ChatbotId}", chatbotId);

            var chatbot = await _chatbotRepository.GetByIdAsync(chatbotId, cancellationToken);
            if (chatbot == null)
            {
                throw new ArgumentException($"Chatbot with ID {chatbotId} not found");
            }

            chatbot.Activate();
            var updatedChatbot = await _chatbotRepository.UpdateAsync(chatbot, cancellationToken);
            await InvalidateChatbotCacheAsync(chatbotId, cancellationToken);

            _logger.LogInformation("Successfully activated chatbot {ChatbotId}", chatbotId);
            return updatedChatbot;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error activating chatbot {ChatbotId}", chatbotId);
            throw;
        }
    }

    public async Task<Chatbot> DeactivateChatbotAsync(
        Guid chatbotId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Deactivating chatbot {ChatbotId}", chatbotId);

            var chatbot = await _chatbotRepository.GetByIdAsync(chatbotId, cancellationToken);
            if (chatbot == null)
            {
                throw new ArgumentException($"Chatbot with ID {chatbotId} not found");
            }

            chatbot.Deactivate();
            var updatedChatbot = await _chatbotRepository.UpdateAsync(chatbot, cancellationToken);
            await InvalidateChatbotCacheAsync(chatbotId, cancellationToken);

            _logger.LogInformation("Successfully deactivated chatbot {ChatbotId}", chatbotId);
            return updatedChatbot;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deactivating chatbot {ChatbotId}", chatbotId);
            throw;
        }
    }

    public async Task<ChatbotConversation> StartConversationAsync(
        Guid chatbotId,
        Guid userId,
        string userName,
        NotificationChannel channel,
        string language = "en-US",
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting conversation with chatbot {ChatbotId} for user {UserId}",
                chatbotId, userId);

            var chatbot = await GetChatbotAsync(chatbotId, cancellationToken);
            if (chatbot == null)
            {
                throw new ArgumentException($"Chatbot with ID {chatbotId} not found");
            }

            if (chatbot.Status != ChatbotStatus.Active)
            {
                throw new InvalidOperationException($"Chatbot {chatbotId} is not active");
            }

            if (!chatbot.IsLanguageSupported(language))
            {
                language = chatbot.DefaultLanguage;
                _logger.LogWarning("Language not supported, using default language {Language} for chatbot {ChatbotId}",
                    language, chatbotId);
            }

            var conversation = ChatbotConversation.Create(chatbotId, userId, userName, channel, language);

            // Add welcome message
            var welcomeMessage = ChatbotMessage.FromBot(chatbot.WelcomeMessage);
            conversation.AddMessage(welcomeMessage);

            var createdConversation = await _conversationRepository.AddAsync(conversation, cancellationToken);

            _logger.LogInformation("Successfully started conversation {SessionId} with chatbot {ChatbotId}",
                createdConversation.SessionId, chatbotId);

            return createdConversation;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting conversation with chatbot {ChatbotId} for user {UserId}",
                chatbotId, userId);
            throw;
        }
    }

    public async Task<ChatbotProcessingResult> ProcessMessageAsync(
        Guid sessionId,
        string userMessage,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Processing message for session {SessionId}: {Message}", sessionId, userMessage);

            var conversation = await _conversationRepository.GetBySessionIdAsync(sessionId, cancellationToken);
            if (conversation == null)
            {
                return ChatbotProcessingResult.Failure($"Conversation {sessionId} not found");
            }

            if (conversation.Status != ConversationStatus.Active)
            {
                return ChatbotProcessingResult.Failure($"Conversation {sessionId} is not active");
            }

            var chatbot = await GetChatbotAsync(conversation.ChatbotId, cancellationToken);
            if (chatbot == null)
            {
                return ChatbotProcessingResult.Failure($"Chatbot {conversation.ChatbotId} not found");
            }

            // Process user message with NLP
            var nlpResult = await _nlpService.ProcessTextAsync(
                userMessage, conversation.Language, chatbot.Intents, chatbot.Entities, cancellationToken);

            // Add user message to conversation
            var userMsg = ChatbotMessage.FromUser(
                userMessage, nlpResult.Intent, nlpResult.Confidence, nlpResult.Entities, nlpResult.Sentiment);
            conversation.AddMessage(userMsg);

            // Update conversation context with extracted entities
            foreach (var entity in nlpResult.Entities)
            {
                conversation.AddExtractedEntity(entity.Key, entity.Value);
            }

            // Set current intent
            if (!string.IsNullOrEmpty(nlpResult.Intent))
            {
                conversation.SetCurrentIntent(nlpResult.Intent);
            }

            // Determine next action based on conversation flow
            var flowResult = await _conversationFlowService.DetermineNextActionAsync(
                conversation, nlpResult, cancellationToken);

            string botResponse;
            bool shouldEndConversation = false;
            bool shouldTransferToAgent = false;

            if (nlpResult.Confidence < chatbot.ConfidenceThreshold)
            {
                // Low confidence, use fallback message
                botResponse = chatbot.FallbackMessage;
            }
            else if (!string.IsNullOrEmpty(nlpResult.Intent))
            {
                var intent = chatbot.GetIntent(nlpResult.Intent);
                if (intent != null)
                {
                    // Check if all required entities are collected
                    var missingEntities = await _conversationFlowService.GetMissingRequiredEntitiesAsync(
                        intent, conversation, cancellationToken);

                    if (missingEntities.Any())
                    {
                        // Ask for missing entities
                        var missingEntity = missingEntities.First();
                        botResponse = $"I need some more information. Could you please provide your {missingEntity}?";
                    }
                    else
                    {
                        // Execute intent actions
                        botResponse = await ExecuteIntentActionsAsync(intent, conversation, nlpResult.Entities, cancellationToken);

                        // Check if any action requires ending conversation or transferring
                        foreach (var action in intent.Actions)
                        {
                            if (action.Type == ActionType.EndConversation)
                                shouldEndConversation = true;
                            else if (action.Type == ActionType.TransferToAgent)
                                shouldTransferToAgent = true;
                        }
                    }
                }
                else
                {
                    botResponse = chatbot.FallbackMessage;
                }
            }
            else
            {
                botResponse = chatbot.FallbackMessage;
            }

            // Add bot response to conversation
            var botMsg = ChatbotMessage.FromBot(botResponse);
            conversation.AddMessage(botMsg);

            // Update conversation
            await _conversationRepository.UpdateAsync(conversation, cancellationToken);

            var result = ChatbotProcessingResult.Success(
                botResponse, nlpResult.Intent, nlpResult.Confidence, nlpResult.Entities, nlpResult.Sentiment);

            result.ShouldEndConversation = shouldEndConversation;
            result.ShouldTransferToAgent = shouldTransferToAgent;

            _logger.LogDebug("Successfully processed message for session {SessionId}", sessionId);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing message for session {SessionId}", sessionId);
            return ChatbotProcessingResult.Failure($"Error processing message: {ex.Message}");
        }
    }

    public async Task EndConversationAsync(
        Guid sessionId,
        ConversationStatus endStatus,
        decimal? satisfactionScore = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Ending conversation {SessionId} with status {Status}", sessionId, endStatus);

            var conversation = await _conversationRepository.GetBySessionIdAsync(sessionId, cancellationToken);
            if (conversation == null)
            {
                _logger.LogWarning("Conversation {SessionId} not found when trying to end", sessionId);
                return;
            }

            switch (endStatus)
            {
                case ConversationStatus.Completed:
                    conversation.Complete(satisfactionScore);
                    break;
                case ConversationStatus.Abandoned:
                    conversation.Abandon();
                    break;
                case ConversationStatus.TransferredToAgent:
                    conversation.TransferToAgent();
                    break;
            }

            await _conversationRepository.UpdateAsync(conversation, cancellationToken);

            _logger.LogInformation("Successfully ended conversation {SessionId}", sessionId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error ending conversation {SessionId}", sessionId);
            throw;
        }
    }

    public async Task<ChatbotConversation?> GetConversationAsync(
        Guid sessionId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            return await _conversationRepository.GetBySessionIdAsync(sessionId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting conversation {SessionId}", sessionId);
            throw;
        }
    }

    public async Task<List<ChatbotConversation>> GetActiveConversationsAsync(
        Guid chatbotId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            return await _conversationRepository.GetActiveConversationsAsync(chatbotId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active conversations for chatbot {ChatbotId}", chatbotId);
            throw;
        }
    }

    public async Task<ChatbotAnalytics> GetChatbotAnalyticsAsync(
        Guid chatbotId,
        DateTime? startDate = null,
        DateTime? endDate = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Getting chatbot analytics for {ChatbotId}", chatbotId);

            var start = startDate ?? DateTime.UtcNow.AddDays(-30);
            var end = endDate ?? DateTime.UtcNow;

            var conversations = await _conversationRepository.GetByChatbotAsync(chatbotId, start, end, cancellationToken);

            var totalConversations = conversations.Count;
            var activeConversations = conversations.Count(c => c.Status == ConversationStatus.Active);
            var completedConversations = conversations.Count(c => c.Status == ConversationStatus.Completed);
            var abandonedConversations = conversations.Count(c => c.Status == ConversationStatus.Abandoned);

            var averageDuration = conversations.Any()
                ? (decimal)conversations.Average(c => c.GetDuration().TotalMinutes)
                : 0;

            // Calculate other metrics
            var satisfactionScores = conversations
                .Where(c => c.SatisfactionScore.HasValue)
                .Select(c => c.SatisfactionScore!.Value)
                .ToList();

            var averageSatisfaction = satisfactionScores.Any() ? satisfactionScores.Average() : 0;

            // Calculate intent usage
            var topIntents = conversations
                .SelectMany(c => c.Messages.Where(m => m.IsFromUser && !string.IsNullOrEmpty(m.Intent)))
                .GroupBy(m => m.Intent!)
                .ToDictionary(g => g.Key, g => g.Count());

            // Calculate unrecognized queries (low confidence)
            var unrecognizedQueries = conversations
                .SelectMany(c => c.Messages.Where(m => m.IsFromUser && m.Confidence < 0.5m))
                .GroupBy(m => m.Text)
                .ToDictionary(g => g.Key, g => g.Count());

            // Calculate language usage
            var languageUsage = conversations
                .GroupBy(c => c.Language)
                .ToDictionary(g => g.Key, g => (decimal)g.Count() / totalConversations * 100);

            var analytics = new ChatbotAnalytics(
                totalConversations, activeConversations, completedConversations, abandonedConversations,
                averageDuration, 2.5m, averageSatisfaction, 85.0m, 15.0m,
                topIntents, unrecognizedQueries, languageUsage);

            _logger.LogInformation("Generated chatbot analytics for {ChatbotId}: {TotalConversations} conversations",
                chatbotId, totalConversations);

            return analytics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting chatbot analytics for {ChatbotId}", chatbotId);
            throw;
        }
    }

    // Private helper methods
    private async Task<string> ExecuteIntentActionsAsync(
        ChatbotIntent intent,
        ChatbotConversation conversation,
        Dictionary<string, object> entities,
        CancellationToken cancellationToken)
    {
        var response = intent.Responses.FirstOrDefault()?.GetRandomVariation() ?? "I understand.";

        foreach (var action in intent.Actions.Where(a => a.IsEnabled))
        {
            try
            {
                var actionResult = await _conversationFlowService.ExecuteActionAsync(
                    action, conversation, entities, cancellationToken);

                if (!string.IsNullOrEmpty(actionResult.Response))
                {
                    response = actionResult.Response;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing action {ActionName} for intent {IntentName}",
                    action.Name, intent.Name);
            }
        }

        return response;
    }

    private async Task<T?> GetFromCacheAsync<T>(string key, CancellationToken cancellationToken) where T : class
    {
        try
        {
            var cachedData = await _cache.GetStringAsync(key, cancellationToken);
            if (!string.IsNullOrEmpty(cachedData))
            {
                return JsonSerializer.Deserialize<T>(cachedData);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error retrieving data from cache for key {Key}", key);
        }

        return null;
    }

    private async Task SetCacheAsync<T>(string key, T data, CancellationToken cancellationToken)
    {
        try
        {
            var serializedData = JsonSerializer.Serialize(data);
            await _cache.SetStringAsync(key, serializedData, new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = _cacheExpiration
            }, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error setting cache for key {Key}", key);
        }
    }

    private async Task InvalidateChatbotCacheAsync(Guid chatbotId, CancellationToken cancellationToken)
    {
        var keysToInvalidate = new[]
        {
            $"chatbot_{chatbotId}",
            "all_chatbots"
        };

        foreach (var key in keysToInvalidate)
        {
            try
            {
                await _cache.RemoveAsync(key, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error invalidating cache key {Key}", key);
            }
        }
    }
}
