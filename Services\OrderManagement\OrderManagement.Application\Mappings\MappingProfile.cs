using AutoMapper;
using OrderManagement.Domain.Entities;
using OrderManagement.Domain.ValueObjects;
using OrderManagement.Application.DTOs;

namespace OrderManagement.Application.Mappings;

public class MappingProfile : Profile
{
    public MappingProfile()
    {
        // RFQ mappings
        CreateMap<RFQ, RfqDetailDto>()
            .ForMember(dest => dest.Bids, opt => opt.MapFrom(src => src.Bids))
            .ForMember(dest => dest.Documents, opt => opt.MapFrom(src => src.Documents));

        CreateMap<RFQ, RfqSummaryDto>()
            .ForMember(dest => dest.LoadType, opt => opt.MapFrom(src => src.LoadDetails.LoadType))
            .ForMember(dest => dest.RequiredVehicleType, opt => opt.MapFrom(src => src.Requirements.RequiredVehicleType))
            .ForMember(dest => dest.PickupCity, opt => opt.MapFrom(src => src.RouteDetails.PickupAddress.City))
            .ForMember(dest => dest.PickupState, opt => opt.MapFrom(src => src.RouteDetails.PickupAddress.State))
            .ForMember(dest => dest.DeliveryCity, opt => opt.MapFrom(src => src.RouteDetails.DeliveryAddress.City))
            .ForMember(dest => dest.DeliveryState, opt => opt.MapFrom(src => src.RouteDetails.DeliveryAddress.State))
            .ForMember(dest => dest.PreferredPickupDate, opt => opt.MapFrom(src => src.RouteDetails.PreferredPickupDate))
            .ForMember(dest => dest.PreferredDeliveryDate, opt => opt.MapFrom(src => src.RouteDetails.PreferredDeliveryDate))
            .ForMember(dest => dest.BidCount, opt => opt.MapFrom(src => src.Bids.Count));

        // Bid mappings
        CreateMap<RfqBid, RfqBidDetailDto>()
            .ForMember(dest => dest.Documents, opt => opt.MapFrom(src => src.Documents));

        CreateMap<RfqBid, RfqBidSummaryDto>();

        // New bid mappings for query handlers
        CreateMap<RfqBid, BidDetailDto>()
            .ForMember(dest => dest.Rfq, opt => opt.MapFrom(src => src.Rfq))
            .ForMember(dest => dest.Documents, opt => opt.MapFrom(src => src.Documents));

        CreateMap<RfqBid, BidSummaryDto>()
            .ForMember(dest => dest.RfqTitle, opt => opt.MapFrom(src => src.Rfq != null ? src.Rfq.Title : ""))
            .ForMember(dest => dest.RfqNumber, opt => opt.MapFrom(src => src.Rfq != null ? src.Rfq.RfqNumber : ""))
            .ForMember(dest => dest.PickupCity, opt => opt.MapFrom(src => src.Rfq != null ? src.Rfq.RouteDetails.PickupAddress.City : ""))
            .ForMember(dest => dest.DeliveryCity, opt => opt.MapFrom(src => src.Rfq != null ? src.Rfq.RouteDetails.DeliveryAddress.City : ""));

        // Order mappings
        CreateMap<Order, OrderDetailDto>()
            .ForMember(dest => dest.Documents, opt => opt.MapFrom(src => src.Documents))
            .ForMember(dest => dest.StatusHistory, opt => opt.MapFrom(src => src.StatusHistory))
            .ForMember(dest => dest.Invoices, opt => opt.MapFrom(src => src.Invoices));

        // Invoice mappings
        CreateMap<Invoice, InvoiceDetailDto>()
            .ForMember(dest => dest.LineItems, opt => opt.MapFrom(src => src.LineItems));

        CreateMap<InvoiceLineItem, InvoiceLineItemDto>();

        // Value object mappings
        CreateMap<LoadDetails, LoadDetailsDto>();
        CreateMap<RouteDetails, RouteDetailsDto>();
        CreateMap<RfqRequirements, RfqRequirementsDto>();
        CreateMap<Address, AddressDto>()
            .ForMember(dest => dest.FullAddress, opt => opt.MapFrom(src => src.FullAddress));
        CreateMap<Weight, WeightDto>()
            .ForMember(dest => dest.ValueInKg, opt => opt.MapFrom(src => src.ToKilograms()));
        CreateMap<Volume, VolumeDto>()
            .ForMember(dest => dest.ValueInCubicMeters, opt => opt.MapFrom(src => src.ToCubicMeters()));
        CreateMap<Distance, DistanceDto>()
            .ForMember(dest => dest.ValueInKm, opt => opt.MapFrom(src => src.ToKilometers()));
        CreateMap<Dimensions, DimensionsDto>();
        CreateMap<TemperatureRange, TemperatureRangeDto>();
        CreateMap<TimeWindow, TimeWindowDto>()
            .ForMember(dest => dest.DisplayTime, opt => opt.MapFrom(src => $"{src.StartTime:hh\\:mm} - {src.EndTime:hh\\:mm}"));
        CreateMap<Money, MoneyDto>()
            .ForMember(dest => dest.FormattedAmount, opt => opt.MapFrom(src => src.ToString()));
        CreateMap<string, CustomerDetailsDto>();
        CreateMap<string, BillingDetailsDto>();

        // Document mappings
        CreateMap<RfqDocument, DocumentDto>();
        CreateMap<BidDocument, DocumentDto>();
        CreateMap<OrderDocument, DocumentDto>();

        // Status history mapping
        CreateMap<OrderStatusHistory, OrderStatusHistoryDto>();

        // Timeline mapping
        CreateMap<RfqTimeline, RfqTimelineEventDto>()
            .ForMember(dest => dest.EventType, opt => opt.MapFrom(src => src.EventType.ToString()))
            .ForMember(dest => dest.PreviousStatus, opt => opt.MapFrom(src => src.PreviousStatus.HasValue ? src.PreviousStatus.ToString() : null))
            .ForMember(dest => dest.NewStatus, opt => opt.MapFrom(src => src.NewStatus.HasValue ? src.NewStatus.ToString() : null));

        // Routing history mapping
        CreateMap<RfqRoutingHistory, RfqRoutingHistoryDto>()
            .ForMember(dest => dest.Action, opt => opt.MapFrom(src => src.Action.ToString()))
            .ForMember(dest => dest.FromEntityType, opt => opt.MapFrom(src => src.FromEntityType.HasValue ? src.FromEntityType.ToString() : null))
            .ForMember(dest => dest.ToEntityType, opt => opt.MapFrom(src => src.ToEntityType.HasValue ? src.ToEntityType.ToString() : null))
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status.ToString()));

        // Tag mapping
        CreateMap<RfqTag, RfqTagDto>()
            .ForMember(dest => dest.TagType, opt => opt.MapFrom(src => src.TagType.ToString()));
    }
}

// Additional DTOs that were missing
public class OrderDetailDto
{
    public Guid Id { get; set; }
    public Guid TransportCompanyId { get; set; }
    public Guid BrokerId { get; set; }
    public Guid? CarrierId { get; set; }
    public Guid? RfqId { get; set; }
    public Guid? AcceptedBidId { get; set; }
    public string OrderNumber { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public LoadDetailsDto LoadDetails { get; set; } = new();
    public RouteDetailsDto RouteDetails { get; set; } = new();
    public MoneyDto AgreedPrice { get; set; } = new();
    public string Status { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime? ConfirmedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public DateTime? CancelledAt { get; set; }
    public string? CancellationReason { get; set; }
    public CustomerDetailsDto CustomerDetails { get; set; } = new();
    public BillingDetailsDto BillingDetails { get; set; } = new();
    public string? SpecialInstructions { get; set; }
    public bool IsUrgent { get; set; }
    public string PaymentStatus { get; set; } = string.Empty;
    public List<DocumentDto> Documents { get; set; } = new();
    public List<OrderStatusHistoryDto> StatusHistory { get; set; } = new();
    public List<InvoiceDetailDto> Invoices { get; set; } = new();
}

public class InvoiceDetailDto
{
    public Guid Id { get; set; }
    public Guid OrderId { get; set; }
    public string InvoiceNumber { get; set; } = string.Empty;
    public DateTime InvoiceDate { get; set; }
    public DateTime DueDate { get; set; }
    public MoneyDto Amount { get; set; } = new();
    public MoneyDto? TaxAmount { get; set; }
    public MoneyDto TotalAmount { get; set; } = new();
    public string Status { get; set; } = string.Empty;
    public CustomerDetailsDto CustomerDetails { get; set; } = new();
    public BillingDetailsDto BillingDetails { get; set; } = new();
    public string? Notes { get; set; }
    public DateTime? PaidDate { get; set; }
    public string? PaymentReference { get; set; }
    public DateTime CreatedAt { get; set; }
    public List<InvoiceLineItemDto> LineItems { get; set; } = new();
}

public class InvoiceLineItemDto
{
    public Guid Id { get; set; }
    public string Description { get; set; } = string.Empty;
    public int Quantity { get; set; }
    public MoneyDto UnitPrice { get; set; } = new();
    public MoneyDto TotalPrice { get; set; } = new();
}

public class CustomerDetailsDto
{
    public string CompanyName { get; set; } = string.Empty;
    public string ContactPerson { get; set; } = string.Empty;
    public string ContactEmail { get; set; } = string.Empty;
    public string ContactPhone { get; set; } = string.Empty;
    public AddressDto BillingAddress { get; set; } = new();
    public string? TaxId { get; set; }
    public string? CustomerReference { get; set; }
    public string? PurchaseOrderNumber { get; set; }
}

public class BillingDetailsDto
{
    public string BillingMethod { get; set; } = string.Empty;
    public int PaymentTermsDays { get; set; }
    public string? PaymentInstructions { get; set; }
    public bool RequiresPurchaseOrder { get; set; }
    public string? PreferredInvoiceFormat { get; set; }
    public string? BillingContact { get; set; }
    public string? BillingEmail { get; set; }
}

public class OrderStatusHistoryDto
{
    public Guid Id { get; set; }
    public string Status { get; set; } = string.Empty;
    public string Notes { get; set; } = string.Empty;
    public DateTime ChangedAt { get; set; }
    public Guid? ChangedBy { get; set; }
}


