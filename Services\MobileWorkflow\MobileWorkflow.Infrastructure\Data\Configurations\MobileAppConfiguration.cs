using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using MobileWorkflow.Domain.Entities;

namespace MobileWorkflow.Infrastructure.Data.Configurations;

public class MobileAppConfiguration : IEntityTypeConfiguration<MobileApp>
{
    public void Configure(EntityTypeBuilder<MobileApp> builder)
    {
        builder.ToTable("mobile_apps");

        builder.HasKey(e => e.Id);

        builder.Property(e => e.Id)
            .HasColumnName("id")
            .ValueGeneratedOnAdd();

        builder.Property(e => e.Name)
            .HasColumnName("name")
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(e => e.Version)
            .HasColumnName("version")
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(e => e.Platform)
            .HasColumnName("platform")
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(e => e.PackageId)
            .HasColumnName("package_id")
            .HasMaxLength(500)
            .IsRequired();

        builder.Property(e => e.IsActive)
            .HasColumnName("is_active")
            .IsRequired();

        builder.Property(e => e.ReleaseDate)
            .HasColumnName("release_date")
            .IsRequired();

        builder.Property(e => e.MinimumOSVersion)
            .HasColumnName("minimum_os_version")
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(e => e.SupportsOffline)
            .HasColumnName("supports_offline")
            .IsRequired();

        builder.Property(e => e.DownloadUrl)
            .HasColumnName("download_url")
            .HasMaxLength(1000)
            .IsRequired();

        builder.Property(e => e.FileSize)
            .HasColumnName("file_size")
            .IsRequired();

        builder.Property(e => e.Checksum)
            .HasColumnName("checksum")
            .HasMaxLength(128)
            .IsRequired();

        builder.Property(e => e.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(e => e.UpdatedAt)
            .HasColumnName("updated_at")
            .IsRequired();

        // Navigation properties
        builder.HasMany(e => e.Sessions)
            .WithOne(s => s.MobileApp)
            .HasForeignKey(s => s.MobileAppId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.OfflineData)
            .WithOne(o => o.MobileApp)
            .HasForeignKey(o => o.MobileAppId)
            .OnDelete(DeleteBehavior.SetNull);
    }
}
