using Shared.Domain.Common;

namespace NetworkFleetManagement.Domain.Entities;

public class NetworkPerformanceRecord : BaseEntity
{
    public Guid NetworkId { get; private set; }
    public Guid? TripId { get; private set; }
    public Guid? OrderId { get; private set; }
    public DateTime RecordDate { get; private set; }
    public decimal? QuoteAmount { get; private set; }
    public decimal? FinalAmount { get; private set; }
    public bool WasOnTime { get; private set; }
    public int? DelayMinutes { get; private set; }
    public decimal? CustomerRating { get; private set; }
    public string? CustomerFeedback { get; private set; }
    public bool WasCancelled { get; private set; }
    public string? CancellationReason { get; private set; }
    public decimal? PenaltyAmount { get; private set; }
    public decimal? BonusAmount { get; private set; }
    public string? Notes { get; private set; }

    // Navigation properties
    public BrokerCarrierNetwork Network { get; private set; } = null!;

    // Parameterless constructor for EF Core
    private NetworkPerformanceRecord() { }

    public NetworkPerformanceRecord(
        Guid networkId,
        DateTime recordDate,
        Guid? tripId = null,
        Guid? orderId = null,
        decimal? quoteAmount = null,
        decimal? finalAmount = null,
        bool wasOnTime = true,
        int? delayMinutes = null,
        decimal? customerRating = null,
        string? customerFeedback = null,
        bool wasCancelled = false,
        string? cancellationReason = null,
        decimal? penaltyAmount = null,
        decimal? bonusAmount = null,
        string? notes = null)
    {
        NetworkId = networkId;
        RecordDate = recordDate;
        TripId = tripId;
        OrderId = orderId;
        QuoteAmount = quoteAmount;
        FinalAmount = finalAmount;
        WasOnTime = wasOnTime;
        DelayMinutes = delayMinutes;
        CustomerRating = customerRating;
        CustomerFeedback = customerFeedback;
        WasCancelled = wasCancelled;
        CancellationReason = cancellationReason;
        PenaltyAmount = penaltyAmount;
        BonusAmount = bonusAmount;
        Notes = notes;
    }

    public decimal? NetAmount => (FinalAmount ?? 0) + (BonusAmount ?? 0) - (PenaltyAmount ?? 0);
    public bool IsSuccessful => !WasCancelled && WasOnTime;
}
