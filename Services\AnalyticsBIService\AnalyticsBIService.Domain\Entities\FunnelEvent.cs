using Shared.Domain.Common;
using System.Text.Json;

namespace AnalyticsBIService.Domain.Entities;

/// <summary>
/// Funnel event domain entity
/// </summary>
public class FunnelEvent : BaseEntity
{
    public Guid FunnelId { get; private set; }
    public Guid UserId { get; private set; }
    public string EventName { get; private set; }
    public int StepOrder { get; private set; }
    public DateTime Timestamp { get; private set; }
    public string Properties { get; private set; }
    public string SessionId { get; private set; }

    // Required for EF Core
    private FunnelEvent()
    {
        EventName = string.Empty;
        Properties = string.Empty;
        SessionId = string.Empty;
    }

    public FunnelEvent(
        Guid funnelId,
        Guid userId,
        string eventName,
        int stepOrder,
        string sessionId,
        Dictionary<string, object>? properties = null)
    {
        if (string.IsNullOrWhiteSpace(eventName))
            throw new ArgumentException("Event name cannot be null or empty", nameof(eventName));
        if (string.IsNullOrWhiteSpace(sessionId))
            throw new ArgumentException("Session ID cannot be null or empty", nameof(sessionId));
        if (stepOrder < 0)
            throw new ArgumentException("Step order cannot be negative", nameof(stepOrder));

        Id = Guid.NewGuid();
        FunnelId = funnelId;
        UserId = userId;
        EventName = eventName;
        StepOrder = stepOrder;
        SessionId = sessionId;
        Timestamp = DateTime.UtcNow;
        Properties = properties != null ? JsonSerializer.Serialize(properties) : "{}";
        CreatedAt = DateTime.UtcNow;
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Update event properties
    /// </summary>
    public void UpdateProperties(Dictionary<string, object> properties)
    {
        if (properties == null)
            throw new ArgumentNullException(nameof(properties));

        Properties = JsonSerializer.Serialize(properties);
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Update step order
    /// </summary>
    public void UpdateStepOrder(int stepOrder)
    {
        if (stepOrder < 0)
            throw new ArgumentException("Step order cannot be negative", nameof(stepOrder));

        StepOrder = stepOrder;
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Update timestamp
    /// </summary>
    public void UpdateTimestamp(DateTime timestamp)
    {
        Timestamp = timestamp;
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Get properties as dictionary
    /// </summary>
    public Dictionary<string, object> GetPropertiesAsDictionary()
    {
        try
        {
            return string.IsNullOrWhiteSpace(Properties)
                ? new Dictionary<string, object>()
                : JsonSerializer.Deserialize<Dictionary<string, object>>(Properties) ?? new Dictionary<string, object>();
        }
        catch
        {
            return new Dictionary<string, object>();
        }
    }
}
