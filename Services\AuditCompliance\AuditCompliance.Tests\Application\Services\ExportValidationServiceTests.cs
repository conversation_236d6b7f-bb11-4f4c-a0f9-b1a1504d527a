using AuditCompliance.Application.DTOs;
using AuditCompliance.Application.Interfaces;
using AuditCompliance.Application.Services;
using AuditCompliance.Domain.Enums;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace AuditCompliance.Tests.Application.Services
{
    /// <summary>
    /// Unit tests for ExportValidationService
    /// </summary>
    public class ExportValidationServiceTests
    {
        private readonly Mock<ILogger<ExportValidationService>> _loggerMock;
        private readonly Mock<IModuleRegistryService> _moduleRegistryServiceMock;
        private readonly ExportValidationService _exportValidationService;

        public ExportValidationServiceTests()
        {
            _loggerMock = new Mock<ILogger<ExportValidationService>>();
            _moduleRegistryServiceMock = new Mock<IModuleRegistryService>();
            _exportValidationService = new ExportValidationService(_loggerMock.Object, _moduleRegistryServiceMock.Object);
        }

        [Fact]
        public async Task ValidateExportRequestAsync_WithValidRequest_ShouldReturnNoErrors()
        {
            // Arrange
            var request = CreateValidExportRequest();
            SetupModuleRegistryMock();

            // Act
            var result = await _exportValidationService.ValidateExportRequestAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Where(v => v.Type == "Error").Should().BeEmpty();
        }

        [Fact]
        public async Task ValidateExportRequestAsync_WithMissingRole_ShouldReturnError()
        {
            // Arrange
            var request = CreateValidExportRequest();
            request.RequestedByRole = string.Empty;
            SetupModuleRegistryMock();

            // Act
            var result = await _exportValidationService.ValidateExportRequestAsync(request);

            // Assert
            result.Should().Contain(v => v.Type == "Error" && v.Code == "MISSING_ROLE");
        }

        [Fact]
        public async Task ValidateExportRequestAsync_WithEmptyUserId_ShouldReturnError()
        {
            // Arrange
            var request = CreateValidExportRequest();
            request.RequestedBy = Guid.Empty;
            SetupModuleRegistryMock();

            // Act
            var result = await _exportValidationService.ValidateExportRequestAsync(request);

            // Assert
            result.Should().Contain(v => v.Type == "Error" && v.Code == "MISSING_USER_ID");
        }

        [Fact]
        public async Task ValidateExportRequestAsync_WithInvalidDateRange_ShouldReturnError()
        {
            // Arrange
            var request = CreateValidExportRequest();
            request.FromDate = DateTime.UtcNow;
            request.ToDate = DateTime.UtcNow.AddDays(-1); // Invalid: from date after to date
            SetupModuleRegistryMock();

            // Act
            var result = await _exportValidationService.ValidateExportRequestAsync(request);

            // Assert
            result.Should().Contain(v => v.Type == "Error" && v.Code == "INVALID_DATE_RANGE");
        }

        [Fact]
        public async Task ValidateExportRequestAsync_WithLargeDateRange_ShouldReturnWarning()
        {
            // Arrange
            var request = CreateValidExportRequest();
            request.FromDate = DateTime.UtcNow.AddDays(-400); // More than 365 days
            request.ToDate = DateTime.UtcNow;
            SetupModuleRegistryMock();

            // Act
            var result = await _exportValidationService.ValidateExportRequestAsync(request);

            // Assert
            result.Should().Contain(v => v.Type == "Warning" && v.Code == "LARGE_DATE_RANGE");
        }

        [Fact]
        public async Task ValidateExportRequestAsync_WithNonExistentModule_ShouldReturnError()
        {
            // Arrange
            var request = CreateValidExportRequest();
            request.ModuleNames = new List<string> { "NonExistentModule" };

            _moduleRegistryServiceMock
                .Setup(x => x.GetModuleByNameAsync("NonExistentModule"))
                .ReturnsAsync((ModuleInfoDto?)null);

            // Act
            var result = await _exportValidationService.ValidateExportRequestAsync(request);

            // Assert
            result.Should().Contain(v => v.Type == "Error" && v.Code == "MODULE_NOT_FOUND");
        }

        [Fact]
        public async Task ValidateExportRequestAsync_WithInactiveModule_ShouldReturnWarning()
        {
            // Arrange
            var request = CreateValidExportRequest();
            request.ModuleNames = new List<string> { "InactiveModule" };

            var inactiveModule = new ModuleInfoDto
            {
                ModuleName = "InactiveModule",
                IsActive = false
            };

            _moduleRegistryServiceMock
                .Setup(x => x.GetModuleByNameAsync("InactiveModule"))
                .ReturnsAsync(inactiveModule);

            // Act
            var result = await _exportValidationService.ValidateExportRequestAsync(request);

            // Assert
            result.Should().Contain(v => v.Type == "Warning" && v.Code == "MODULE_INACTIVE");
        }

        [Fact]
        public async Task ValidateExportRequestAsync_WithHighRecordLimit_ShouldReturnWarning()
        {
            // Arrange
            var request = CreateValidExportRequest();
            request.MaxRecords = 2000000; // Very high limit
            SetupModuleRegistryMock();

            // Act
            var result = await _exportValidationService.ValidateExportRequestAsync(request);

            // Assert
            result.Should().Contain(v => v.Type == "Warning" && v.Code == "HIGH_RECORD_LIMIT");
        }

        [Fact]
        public async Task ValidateExportRequestAsync_WithLargePageSize_ShouldReturnWarning()
        {
            // Arrange
            var request = CreateValidExportRequest();
            request.PageSize = 100000; // Very large page size
            SetupModuleRegistryMock();

            // Act
            var result = await _exportValidationService.ValidateExportRequestAsync(request);

            // Assert
            result.Should().Contain(v => v.Type == "Warning" && v.Code == "LARGE_PAGE_SIZE");
        }

        [Fact]
        public async Task ValidateBulkExportRequestAsync_WithTooManyExports_ShouldReturnError()
        {
            // Arrange
            var bulkRequest = new BulkAuditExportRequestDto
            {
                ExportRequests = Enumerable.Range(0, 15) // More than 10
                    .Select(_ => CreateValidExportRequest())
                    .ToList()
            };

            // Act
            var result = await _exportValidationService.ValidateBulkExportRequestAsync(bulkRequest);

            // Assert
            result.Should().Contain(v => v.Type == "Error" && v.Code == "BULK_LIMIT_EXCEEDED");
        }

        [Fact]
        public async Task ValidateBulkExportRequestAsync_WithParallelProcessingWarning_ShouldReturnWarning()
        {
            // Arrange
            var bulkRequest = new BulkAuditExportRequestDto
            {
                ProcessInParallel = true,
                ExportRequests = Enumerable.Range(0, 8) // More than 5
                    .Select(_ => CreateValidExportRequest())
                    .ToList()
            };

            SetupModuleRegistryMock();

            // Act
            var result = await _exportValidationService.ValidateBulkExportRequestAsync(bulkRequest);

            // Assert
            result.Should().Contain(v => v.Type == "Warning" && v.Code == "PERFORMANCE_WARNING");
        }

        [Theory]
        [InlineData("Admin", true, true)]
        [InlineData("Auditor", false, true)]
        [InlineData("ComplianceOfficer", false, true)]
        [InlineData("User", false, false)]
        [InlineData("Guest", false, false)]
        public async Task HasExportPermissionAsync_WithDifferentRoles_ShouldReturnExpectedResult(
            string userRole, bool includeSensitiveData, bool expectedResult)
        {
            // Arrange
            var userId = Guid.NewGuid();
            var request = CreateValidExportRequest();
            request.IncludeSensitiveData = includeSensitiveData;

            SetupModuleRegistryMock();

            // Act
            var result = await _exportValidationService.HasExportPermissionAsync(userId, userRole, request);

            // Assert
            result.Should().Be(expectedResult);
        }

        [Fact]
        public async Task HasExportPermissionAsync_WithoutModuleAccess_ShouldReturnFalse()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var request = CreateValidExportRequest();
            request.ModuleNames = new List<string> { "RestrictedModule" };

            _moduleRegistryServiceMock
                .Setup(x => x.HasModuleAccessAsync(userId, "RestrictedModule"))
                .ReturnsAsync(false);

            // Act
            var result = await _exportValidationService.HasExportPermissionAsync(userId, "Admin", request);

            // Assert
            result.Should().BeFalse();
        }

        [Fact]
        public async Task ValidateFormatConfigurationAsync_WithExcelAndTooManyColumns_ShouldReturnError()
        {
            // Arrange
            var selectedColumns = Enumerable.Range(0, 300) // More than 256
                .Select(i => $"Column{i}")
                .ToList();

            // Act
            var result = await _exportValidationService.ValidateFormatConfigurationAsync(ExportFormat.Excel, selectedColumns);

            // Assert
            result.Should().Contain(v => v.Type == "Error" && v.Code == "EXCEL_COLUMN_LIMIT");
        }

        [Fact]
        public async Task ValidateFormatConfigurationAsync_WithPdfAndManyColumns_ShouldReturnWarning()
        {
            // Arrange
            var selectedColumns = Enumerable.Range(0, 25) // More than 20
                .Select(i => $"Column{i}")
                .ToList();

            // Act
            var result = await _exportValidationService.ValidateFormatConfigurationAsync(ExportFormat.PDF, selectedColumns);

            // Assert
            result.Should().Contain(v => v.Type == "Warning" && v.Code == "PDF_COLUMN_WARNING");
        }

        [Fact]
        public async Task ValidateFormatConfigurationAsync_WithManyColumns_ShouldReturnWarning()
        {
            // Arrange
            var selectedColumns = Enumerable.Range(0, 60) // More than 50
                .Select(i => $"Column{i}")
                .ToList();

            // Act
            var result = await _exportValidationService.ValidateFormatConfigurationAsync(ExportFormat.CSV, selectedColumns);

            // Assert
            result.Should().Contain(v => v.Type == "Warning" && v.Code == "COLUMN_COUNT_WARNING");
        }

        private static EnhancedAuditExportRequestDto CreateValidExportRequest()
        {
            return new EnhancedAuditExportRequestDto
            {
                RequestedBy = Guid.NewGuid(),
                RequestedByRole = "Admin",
                ModuleNames = new List<string> { "TestModule" },
                FromDate = DateTime.UtcNow.AddDays(-30),
                ToDate = DateTime.UtcNow,
                Format = ExportFormat.CSV,
                SelectedColumns = new List<string> { "Id", "Action", "Timestamp" },
                MaxRecords = 1000,
                PageSize = 100,
                IncludeSensitiveData = false
            };
        }

        private void SetupModuleRegistryMock()
        {
            var testModule = new ModuleInfoDto
            {
                ModuleName = "TestModule",
                IsActive = true
            };

            _moduleRegistryServiceMock
                .Setup(x => x.GetModuleByNameAsync("TestModule"))
                .ReturnsAsync(testModule);

            _moduleRegistryServiceMock
                .Setup(x => x.HasModuleAccessAsync(It.IsAny<Guid>(), "TestModule"))
                .ReturnsAsync(true);
        }
    }
}
