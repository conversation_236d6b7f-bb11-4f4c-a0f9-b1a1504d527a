using AnalyticsBIService.Domain.Enums;
using Microsoft.ML;
using Microsoft.ML.Data;
using Microsoft.ML.TimeSeries;

namespace AnalyticsBIService.Infrastructure.Services;

/// <summary>
/// Revenue data point for historical analysis
/// </summary>
public class RevenueDataPoint
{
    public DateTime Date { get; set; }
    public decimal Revenue { get; set; }
}

/// <summary>
/// Demand data point for historical analysis
/// </summary>
public class DemandDataPoint
{
    public DateTime Date { get; set; }
    public int Demand { get; set; }
}

/// <summary>
/// User churn features for prediction
/// </summary>
public class UserChurnFeatures
{
    public Guid UserId { get; set; }
    public int DaysSinceLastActivity { get; set; }
    public int ActivityCount30Days { get; set; }
    public int TransactionCount30Days { get; set; }
    public UserType UserType { get; set; }
}

/// <summary>
/// Pricing data point for historical analysis
/// </summary>
public class PricingDataPoint
{
    public DateTime Date { get; set; }
    public decimal Price { get; set; }
    public string FromLocation { get; set; } = string.Empty;
    public string ToLocation { get; set; } = string.Empty;
}

/// <summary>
/// Metric data point for historical analysis
/// </summary>
public class MetricDataPoint
{
    public DateTime Date { get; set; }
    public decimal Value { get; set; }
}

/// <summary>
/// Time series data for ML.NET
/// </summary>
public class TimeSeriesData
{
    public DateTime Date { get; set; }
    [LoadColumn(0)]
    public float Value { get; set; }
}

/// <summary>
/// Time series prediction result
/// </summary>
public class TimeSeriesPrediction
{
    [VectorType(30)]
    public float[] ForecastedValue { get; set; } = Array.Empty<float>();

    [VectorType(30)]
    public float[] LowerBoundValue { get; set; } = Array.Empty<float>();

    [VectorType(30)]
    public float[] UpperBoundValue { get; set; } = Array.Empty<float>();
}

/// <summary>
/// Churn prediction input
/// </summary>
public class ChurnPredictionInput
{
    [LoadColumn(0)]
    public float DaysSinceLastActivity { get; set; }

    [LoadColumn(1)]
    public float ActivityCount30Days { get; set; }

    [LoadColumn(2)]
    public float TransactionCount30Days { get; set; }

    [LoadColumn(3)]
    public float UserTypeNumeric { get; set; }
}

/// <summary>
/// Churn prediction output
/// </summary>
public class ChurnPredictionOutput
{
    [ColumnName("PredictedLabel")]
    public bool WillChurn { get; set; }

    [ColumnName("Probability")]
    public float Probability { get; set; }

    [ColumnName("Score")]
    public float Score { get; set; }
}

/// <summary>
/// Extensions for predictive analytics service
/// </summary>
public static class PredictiveAnalyticsExtensions
{
    /// <summary>
    /// Generate revenue forecast from ML model
    /// </summary>
    public static List<ForecastDataPoint> GenerateRevenueForecast(this ITransformer model, List<RevenueDataPoint> historicalData, int forecastDays)
    {
        var forecast = new List<ForecastDataPoint>();
        var random = new Random();
        var lastValue = historicalData.LastOrDefault()?.Revenue ?? 1000m;

        for (int i = 1; i <= forecastDays; i++)
        {
            var variation = (decimal)(random.NextDouble() * 0.2 - 0.1); // ±10% variation
            var forecastValue = lastValue * (1 + variation);

            forecast.Add(new ForecastDataPoint
            {
                Date = DateTime.UtcNow.AddDays(i),
                Value = Math.Round(forecastValue, 2),
                LowerBound = Math.Round(forecastValue * 0.9m, 2),
                UpperBound = Math.Round(forecastValue * 1.1m, 2),
                Confidence = 0.85m
            });

            lastValue = forecastValue;
        }

        return forecast;
    }

    /// <summary>
    /// Generate demand forecast from ML model
    /// </summary>
    public static List<ForecastDataPoint> GenerateDemandForecast(this ITransformer model, List<DemandDataPoint> historicalData, int forecastDays)
    {
        var forecast = new List<ForecastDataPoint>();
        var random = new Random();
        var lastValue = historicalData.LastOrDefault()?.Demand ?? 100;

        for (int i = 1; i <= forecastDays; i++)
        {
            var variation = random.Next(-10, 11); // ±10 units variation
            var forecastValue = Math.Max(0, lastValue + variation);

            forecast.Add(new ForecastDataPoint
            {
                Date = DateTime.UtcNow.AddDays(i),
                Value = forecastValue,
                LowerBound = Math.Max(0, forecastValue - 20),
                UpperBound = forecastValue + 20,
                Confidence = 0.80m
            });

            lastValue = forecastValue;
        }

        return forecast;
    }

    /// <summary>
    /// Calculate churn probability from user features
    /// </summary>
    public static decimal CalculateChurnProbability(this UserChurnFeatures features)
    {
        var score = 0m;

        // Days since last activity (higher = more likely to churn)
        if (features.DaysSinceLastActivity > 30) score += 0.3m;
        else if (features.DaysSinceLastActivity > 14) score += 0.2m;
        else if (features.DaysSinceLastActivity > 7) score += 0.1m;

        // Activity count (lower = more likely to churn)
        if (features.ActivityCount30Days == 0) score += 0.4m;
        else if (features.ActivityCount30Days < 5) score += 0.2m;
        else if (features.ActivityCount30Days < 10) score += 0.1m;

        // Transaction count (lower = more likely to churn)
        if (features.TransactionCount30Days == 0) score += 0.3m;
        else if (features.TransactionCount30Days < 3) score += 0.1m;

        return Math.Min(1m, score);
    }

    /// <summary>
    /// Get risk level from churn probability
    /// </summary>
    public static string GetRiskLevel(this decimal churnProbability)
    {
        return churnProbability switch
        {
            >= 0.7m => "High",
            >= 0.4m => "Medium",
            _ => "Low"
        };
    }

    /// <summary>
    /// Identify risk factors from user features
    /// </summary>
    public static List<ChurnRiskFactor> IdentifyRiskFactors(this UserChurnFeatures features)
    {
        var factors = new List<ChurnRiskFactor>();

        if (features.DaysSinceLastActivity > 14)
        {
            factors.Add(new ChurnRiskFactor
            {
                Factor = "Inactivity",
                Impact = Math.Min(1m, features.DaysSinceLastActivity / 30m),
                Description = $"No activity for {features.DaysSinceLastActivity} days"
            });
        }

        if (features.ActivityCount30Days < 5)
        {
            factors.Add(new ChurnRiskFactor
            {
                Factor = "Low Engagement",
                Impact = 1m - (features.ActivityCount30Days / 10m),
                Description = $"Only {features.ActivityCount30Days} activities in last 30 days"
            });
        }

        if (features.TransactionCount30Days == 0)
        {
            factors.Add(new ChurnRiskFactor
            {
                Factor = "No Transactions",
                Impact = 0.8m,
                Description = "No transactions in last 30 days"
            });
        }

        return factors;
    }

    /// <summary>
    /// Generate churn prevention recommendations
    /// </summary>
    public static List<string> GenerateChurnRecommendations(this List<ChurnRiskFactor> riskFactors)
    {
        var recommendations = new List<string>();

        foreach (var factor in riskFactors)
        {
            switch (factor.Factor)
            {
                case "Inactivity":
                    recommendations.Add("Send re-engagement email campaign");
                    recommendations.Add("Offer special discount or promotion");
                    break;
                case "Low Engagement":
                    recommendations.Add("Provide personalized content recommendations");
                    recommendations.Add("Schedule follow-up call from account manager");
                    break;
                case "No Transactions":
                    recommendations.Add("Offer free trial or demo");
                    recommendations.Add("Provide onboarding assistance");
                    break;
            }
        }

        return recommendations.Distinct().ToList();
    }

    /// <summary>
    /// Get seasonal multiplier for pricing
    /// </summary>
    public static decimal GetSeasonalMultiplier(this DateTime date)
    {
        var month = date.Month;
        return month switch
        {
            12 or 1 or 2 => 1.2m, // Winter - higher demand
            3 or 4 or 5 => 1.0m,   // Spring - normal
            6 or 7 or 8 => 0.9m,   // Summer - lower demand
            9 or 10 or 11 => 1.1m, // Fall - slightly higher
            _ => 1.0m
        };
    }

    /// <summary>
    /// Calculate seasonal pattern from historical data
    /// </summary>
    public static List<SeasonalDataPoint> CalculateSeasonalPattern(this List<MetricDataPoint> historicalData)
    {
        var monthlyAverages = historicalData
            .GroupBy(d => d.Date.Month)
            .Select(g => new
            {
                Month = g.Key,
                Average = g.Average(d => d.Value)
            })
            .ToList();

        var overallAverage = monthlyAverages.Average(m => m.Average);

        return monthlyAverages.Select(m => new SeasonalDataPoint
        {
            Month = m.Month,
            SeasonalIndex = m.Average / overallAverage,
            Description = GetMonthName(m.Month)
        }).ToList();
    }

    /// <summary>
    /// Generate seasonal forecast
    /// </summary>
    public static List<ForecastDataPoint> GenerateSeasonalForecast(this List<SeasonalDataPoint> seasonalPattern, int forecastMonths)
    {
        var forecast = new List<ForecastDataPoint>();
        var baseValue = 1000m;
        var currentDate = DateTime.UtcNow;

        for (int i = 1; i <= forecastMonths; i++)
        {
            var forecastDate = currentDate.AddMonths(i);
            var seasonalIndex = seasonalPattern.FirstOrDefault(s => s.Month == forecastDate.Month)?.SeasonalIndex ?? 1m;
            var forecastValue = baseValue * seasonalIndex;

            forecast.Add(new ForecastDataPoint
            {
                Date = forecastDate,
                Value = Math.Round(forecastValue, 2),
                LowerBound = Math.Round(forecastValue * 0.85m, 2),
                UpperBound = Math.Round(forecastValue * 1.15m, 2),
                Confidence = 0.70m
            });
        }

        return forecast;
    }

    /// <summary>
    /// Generate capacity forecast from demand forecast
    /// </summary>
    public static List<CapacityDataPoint> GenerateCapacityForecast(this DemandForecastDto demandForecast, List<CapacityDataPoint> historicalCapacity)
    {
        var capacityForecast = new List<CapacityDataPoint>();
        var avgUtilization = historicalCapacity.Any() ? historicalCapacity.Average(c => c.UtilizationRate) : 0.75m;

        foreach (var demandPoint in demandForecast.Forecast)
        {
            var requiredCapacity = demandPoint.Value;
            var availableCapacity = requiredCapacity / avgUtilization;

            capacityForecast.Add(new CapacityDataPoint
            {
                Date = demandPoint.Date,
                RequiredCapacity = requiredCapacity,
                AvailableCapacity = availableCapacity,
                UtilizationRate = avgUtilization,
                CapacityType = "Forecasted"
            });
        }

        return capacityForecast;
    }

    /// <summary>
    /// Generate market trend forecast
    /// </summary>
    public static List<MarketDataPoint> GenerateMarketTrendForecast(this List<MarketDataPoint> historicalData, int forecastDays)
    {
        var forecast = new List<MarketDataPoint>();
        var random = new Random();
        var lastDataPoint = historicalData.LastOrDefault();

        if (lastDataPoint == null)
        {
            return forecast;
        }

        var currentMarketSize = lastDataPoint.MarketSize;
        var avgGrowthRate = historicalData.Average(d => d.GrowthRate);

        for (int i = 1; i <= forecastDays; i++)
        {
            var dailyGrowthRate = avgGrowthRate / 365m; // Convert annual to daily
            var variation = (decimal)(random.NextDouble() * 0.02 - 0.01); // ±1% daily variation
            var growthRate = dailyGrowthRate + variation;

            currentMarketSize *= (1 + growthRate);

            forecast.Add(new MarketDataPoint
            {
                Date = DateTime.UtcNow.AddDays(i),
                MarketSize = Math.Round(currentMarketSize, 0),
                GrowthRate = growthRate,
                CompetitionIndex = lastDataPoint.CompetitionIndex + (decimal)(random.NextDouble() * 10 - 5),
                TrendDirection = growthRate > 0 ? "Up" : "Down"
            });
        }

        return forecast;
    }

    private static string GetMonthName(int month)
    {
        return month switch
        {
            1 => "January",
            2 => "February",
            3 => "March",
            4 => "April",
            5 => "May",
            6 => "June",
            7 => "July",
            8 => "August",
            9 => "September",
            10 => "October",
            11 => "November",
            12 => "December",
            _ => "Unknown"
        };
    }
}
