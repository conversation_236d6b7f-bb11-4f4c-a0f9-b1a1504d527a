using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using AnalyticsBIService.Domain.Entities;

namespace AnalyticsBIService.Infrastructure.Persistence.Configurations;

public class ReportExportConfiguration : IEntityTypeConfiguration<ReportExport>
{
    public void Configure(EntityTypeBuilder<ReportExport> builder)
    {
        builder.ToTable("report_exports");

        builder.HasKey(re => re.Id);

        builder.Property(re => re.Format)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(re => re.Status)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(re => re.FileName)
            .IsRequired()
            .HasMaxLength(500);

        builder.Property(re => re.FilePath)
            .HasMaxLength(1000);

        builder.Property(re => re.FileSize)
            .IsRequired(false);

        builder.Property(re => re.RequestedBy)
            .IsRequired();

        builder.Property(re => re.RequestedAt)
            .IsRequired();

        builder.Property(re => re.CompletedAt)
            .IsRequired(false);

        builder.Property(re => re.ExpiresAt)
            .IsRequired(false);

        builder.Property(re => re.ErrorMessage)
            .HasMaxLength(2000);

        // Configure Parameters as JSON
        builder.Property(re => re.Parameters)
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        // Configure indexes
        builder.HasIndex(re => re.Format)
            .HasDatabaseName("IX_report_exports_format");

        builder.HasIndex(re => re.Status)
            .HasDatabaseName("IX_report_exports_status");

        builder.HasIndex(re => re.RequestedBy)
            .HasDatabaseName("IX_report_exports_requested_by");

        builder.HasIndex(re => re.RequestedAt)
            .HasDatabaseName("IX_report_exports_requested_at");

        builder.HasIndex(re => re.ExpiresAt)
            .HasDatabaseName("IX_report_exports_expires_at");

        // Foreign key will be configured by Report entity
        builder.HasIndex("ReportId")
            .HasDatabaseName("IX_report_exports_report_id");
    }
}
