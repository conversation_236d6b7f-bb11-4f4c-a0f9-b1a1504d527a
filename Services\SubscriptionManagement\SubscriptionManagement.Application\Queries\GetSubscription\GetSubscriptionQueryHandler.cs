using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.DTOs;
using SubscriptionManagement.Application.Interfaces;

namespace SubscriptionManagement.Application.Queries.GetSubscription;

public class GetSubscriptionQueryHandler : IRequestHandler<GetSubscriptionQuery, SubscriptionDto?>
{
    private readonly ISubscriptionRepository _subscriptionRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetSubscriptionQueryHandler> _logger;

    public GetSubscriptionQueryHandler(
        ISubscriptionRepository subscriptionRepository,
        IMapper mapper,
        ILogger<GetSubscriptionQueryHandler> logger)
    {
        _subscriptionRepository = subscriptionRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<SubscriptionDto?> Handle(GetSubscriptionQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting subscription with ID: {SubscriptionId}", request.SubscriptionId);

        var subscription = await _subscriptionRepository.GetByIdAsync(request.SubscriptionId);
        
        if (subscription == null)
        {
            _logger.LogWarning("Subscription with ID {SubscriptionId} not found", request.SubscriptionId);
            return null;
        }

        // Authorization check - users can only access their own subscriptions
        if (request.UserId.HasValue && subscription.UserId != request.UserId.Value)
        {
            _logger.LogWarning("User {UserId} attempted to access subscription {SubscriptionId} belonging to user {OwnerUserId}", 
                request.UserId.Value, request.SubscriptionId, subscription.UserId);
            return null;
        }

        var result = _mapper.Map<SubscriptionDto>(subscription);

        _logger.LogInformation("Successfully retrieved subscription {SubscriptionId} for user {UserId}", 
            subscription.Id, subscription.UserId);

        return result;
    }
}
