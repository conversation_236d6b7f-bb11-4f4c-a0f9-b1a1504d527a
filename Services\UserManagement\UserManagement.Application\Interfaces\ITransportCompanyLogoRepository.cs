namespace UserManagement.Application.Interfaces;

public class TransportCompanyLogo
{
    public Guid Id { get; set; }
    public Guid CompanyId { get; set; }
    public string LogoUrl { get; set; } = string.Empty;
    public string OriginalUrl { get; set; } = string.Empty;
    public string ThumbnailUrl { get; set; } = string.Empty;
    public DateTime UploadedAt { get; set; }
    public List<LogoVariant> Variants { get; set; } = new List<LogoVariant>();

    public void AddVariant(string size, string url, int width, int height, string format, bool isOptimized)
    {
        Variants.Add(new LogoVariant
        {
            Size = size,
            Url = url,
            Width = width,
            Height = height,
            Format = format,
            IsOptimized = isOptimized
        });
    }
}

public class LogoVariant
{
    public string Size { get; set; } = string.Empty;
    public string Url { get; set; } = string.Empty;
    public int Width { get; set; }
    public int Height { get; set; }
    public string Format { get; set; } = string.Empty;
    public bool IsOptimized { get; set; }
}

public interface ITransportCompanyLogoRepository
{
    Task<TransportCompanyLogo?> GetByIdAsync(Guid id);
    Task<TransportCompanyLogo?> GetByCompanyIdAsync(Guid companyId);
    Task<TransportCompanyLogo?> GetByTransportCompanyIdAsync(Guid transportCompanyId);
    Task<TransportCompanyLogo> AddAsync(TransportCompanyLogo logo);
    Task<TransportCompanyLogo> UpdateAsync(TransportCompanyLogo logo);
    Task DeleteAsync(Guid id);
    Task SaveChangesAsync();
}
