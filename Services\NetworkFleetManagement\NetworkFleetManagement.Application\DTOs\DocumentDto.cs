﻿using NetworkFleetManagement.Domain.Enums;

namespace NetworkFleetManagement.Application.DTOs;

public class CarrierDocumentDto
{
    public Guid Id { get; set; }
    public Guid CarrierId { get; set; }
    public DocumentType DocumentType { get; set; }
    public string DocumentName { get; set; } = string.Empty;
    public string DocumentNumber { get; set; } = string.Empty;
    public string FilePath { get; set; } = string.Empty;
    public string? FileUrl { get; set; }
    public DateTime? ExpiryDate { get; set; }
    public bool IsVerified { get; set; }
    public DateTime? VerifiedAt { get; set; }
    public Guid? VerifiedBy { get; set; }
    public string? VerificationNotes { get; set; }
    public string? RejectionReason { get; set; }
    public long FileSizeBytes { get; set; }
    public string? MimeType { get; set; }
    public bool IsExpired { get; set; }
    public bool IsExpiringSoon { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

public class VehicleDocumentDto
{
    public Guid Id { get; set; }
    public Guid VehicleId { get; set; }
    public DocumentType DocumentType { get; set; }
    public string DocumentName { get; set; } = string.Empty;
    public string DocumentNumber { get; set; } = string.Empty;
    public string FilePath { get; set; } = string.Empty;
    public string? FileUrl { get; set; }
    public DateTime? ExpiryDate { get; set; }
    public bool IsVerified { get; set; }
    public DateTime? VerifiedAt { get; set; }
    public Guid? VerifiedBy { get; set; }
    public string? VerificationNotes { get; set; }
    public string? RejectionReason { get; set; }
    public long FileSizeBytes { get; set; }
    public string? MimeType { get; set; }
    public bool IsExpired { get; set; }
    public bool IsExpiringSoon { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

public class DriverDocumentDto
{
    public Guid Id { get; set; }
    public Guid DriverId { get; set; }
    public DocumentType DocumentType { get; set; }
    public string DocumentName { get; set; } = string.Empty;
    public string DocumentNumber { get; set; } = string.Empty;
    public string FilePath { get; set; } = string.Empty;
    public string? FileUrl { get; set; }
    public DateTime? ExpiryDate { get; set; }
    public bool IsVerified { get; set; }
    public DateTime? VerifiedAt { get; set; }
    public Guid? VerifiedBy { get; set; }
    public string? VerificationNotes { get; set; }
    public string? RejectionReason { get; set; }
    public long FileSizeBytes { get; set; }
    public string? MimeType { get; set; }
    public bool IsExpired { get; set; }
    public bool IsExpiringSoon { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

public class UploadCarrierDocumentDto
{
    public DocumentType DocumentType { get; set; }
    public string DocumentName { get; set; } = string.Empty;
    public string DocumentNumber { get; set; } = string.Empty;
    public FileUploadDto File { get; set; } = null!;
    public DateTime? ExpiryDate { get; set; }
}

public class UploadVehicleDocumentDto
{
    public DocumentType DocumentType { get; set; }
    public string DocumentName { get; set; } = string.Empty;
    public string DocumentNumber { get; set; } = string.Empty;
    public FileUploadDto File { get; set; } = null!;
    public DateTime? ExpiryDate { get; set; }
}

public class UploadDriverDocumentDto
{
    public DocumentType DocumentType { get; set; }
    public string DocumentName { get; set; } = string.Empty;
    public string DocumentNumber { get; set; } = string.Empty;
    public FileUploadDto File { get; set; } = null!;
    public DateTime? ExpiryDate { get; set; }
}

public class VerifyDocumentDto
{
    public string? Notes { get; set; }
}

public class RejectDocumentDto
{
    public string Reason { get; set; } = string.Empty;
}

public class ExpiredDocumentsDto
{
    public List<CarrierDocumentDto> CarrierDocuments { get; set; } = new();
    public List<VehicleDocumentDto> VehicleDocuments { get; set; } = new();
    public List<DriverDocumentDto> DriverDocuments { get; set; } = new();
    public int TotalCount { get; set; }
}

public class DocumentsRequiringVerificationDto
{
    public List<CarrierDocumentDto> CarrierDocuments { get; set; } = new();
    public List<VehicleDocumentDto> VehicleDocuments { get; set; } = new();
    public List<DriverDocumentDto> DriverDocuments { get; set; } = new();
    public int TotalCount { get; set; }
}

public class DocumentsExpiringSoonDto
{
    public List<CarrierDocumentDto> CarrierDocuments { get; set; } = new();
    public List<VehicleDocumentDto> VehicleDocuments { get; set; } = new();
    public List<DriverDocumentDto> DriverDocuments { get; set; } = new();
    public int TotalCount { get; set; }
    public int DaysThreshold { get; set; }
}

public class DocumentDownloadDto
{
    public byte[] FileContent { get; set; } = Array.Empty<byte>();
    public string MimeType { get; set; } = string.Empty;
    public string FileName { get; set; } = string.Empty;
}

// Custom file DTO to replace IFormFile dependency
public class FileUploadDto
{
    public string FileName { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    public long Length { get; set; }
    public Stream Content { get; set; } = null!;
}