using ContentManagement.Domain.Entities;
using ContentManagement.Domain.Enums;

namespace ContentManagement.Application.Interfaces;

/// <summary>
/// Service interface for content audit logging
/// </summary>
public interface IContentAuditService
{
    /// <summary>
    /// Log content creation event
    /// </summary>
    Task LogContentCreatedAsync(
        Content content,
        Guid userId,
        string userName,
        string userRole,
        string? ipAddress = null,
        string? userAgent = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Log content update event
    /// </summary>
    Task LogContentUpdatedAsync(
        Content content,
        object oldValues,
        Guid userId,
        string userName,
        string userRole,
        string? ipAddress = null,
        string? userAgent = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Log content status change event
    /// </summary>
    Task LogContentStatusChangedAsync(
        Content content,
        ContentStatus oldStatus,
        ContentStatus newStatus,
        Guid userId,
        string userName,
        string userRole,
        string? reason = null,
        string? ipAddress = null,
        string? userAgent = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Log content visibility change event
    /// </summary>
    Task LogContentVisibilityChangedAsync(
        Content content,
        object oldVisibility,
        object newVisibility,
        Guid userId,
        string userName,
        string userRole,
        string? ipAddress = null,
        string? userAgent = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Log content deletion event
    /// </summary>
    Task LogContentDeletedAsync(
        Guid contentId,
        string contentTitle,
        ContentType contentType,
        Guid userId,
        string userName,
        string userRole,
        string? reason = null,
        string? ipAddress = null,
        string? userAgent = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Log FAQ view event
    /// </summary>
    Task LogFaqViewedAsync(
        FaqItem faqItem,
        Guid? userId = null,
        string? userName = null,
        string? userRole = null,
        string? ipAddress = null,
        string? userAgent = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Log FAQ rating event
    /// </summary>
    Task LogFaqRatedAsync(
        FaqItem faqItem,
        int rating,
        Guid userId,
        string userName,
        string userRole,
        string? ipAddress = null,
        string? userAgent = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Log policy acceptance event
    /// </summary>
    Task LogPolicyAcceptedAsync(
        PolicyDocument policy,
        PolicyAcceptance acceptance,
        string? ipAddress = null,
        string? userAgent = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Log policy version creation event
    /// </summary>
    Task LogPolicyVersionCreatedAsync(
        PolicyDocument policy,
        string newVersion,
        Guid userId,
        string userName,
        string userRole,
        string? ipAddress = null,
        string? userAgent = null,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Service interface for content search and indexing
/// </summary>
public interface IContentSearchService
{
    /// <summary>
    /// Index content for search
    /// </summary>
    Task IndexContentAsync(Content content, CancellationToken cancellationToken = default);

    /// <summary>
    /// Remove content from search index
    /// </summary>
    Task RemoveFromIndexAsync(Guid contentId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update content in search index
    /// </summary>
    Task UpdateIndexAsync(Content content, CancellationToken cancellationToken = default);

    /// <summary>
    /// Search content with full-text search capabilities
    /// </summary>
    Task<(List<Guid> ContentIds, int TotalCount)> SearchAsync(
        string searchTerm,
        ContentType? type = null,
        List<string>? tags = null,
        List<string>? userRoles = null,
        bool isAuthenticated = false,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get search suggestions
    /// </summary>
    Task<List<string>> GetSearchSuggestionsAsync(
        string partialTerm,
        ContentType? type = null,
        int maxSuggestions = 10,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Rebuild search index
    /// </summary>
    Task RebuildIndexAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Service interface for content publishing and scheduling
/// </summary>
public interface IContentPublishingService
{
    /// <summary>
    /// Process scheduled content publishing
    /// </summary>
    Task ProcessScheduledPublishingAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Process scheduled content unpublishing
    /// </summary>
    Task ProcessScheduledUnpublishingAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get content scheduled for publishing
    /// </summary>
    Task<List<Content>> GetContentScheduledForPublishingAsync(
        DateTime? beforeDate = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get content scheduled for unpublishing
    /// </summary>
    Task<List<Content>> GetContentScheduledForUnpublishingAsync(
        DateTime? beforeDate = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate publish schedule
    /// </summary>
    Task<bool> ValidatePublishScheduleAsync(
        DateTime? publishAt,
        DateTime? unpublishAt,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Service interface for content notifications
/// </summary>
public interface IContentNotificationService
{
    /// <summary>
    /// Send content approval notification
    /// </summary>
    Task SendContentApprovalNotificationAsync(
        Content content,
        Guid approvedBy,
        string approvedByName,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Send content rejection notification
    /// </summary>
    Task SendContentRejectionNotificationAsync(
        Content content,
        Guid rejectedBy,
        string rejectedByName,
        string reason,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Send content published notification
    /// </summary>
    Task SendContentPublishedNotificationAsync(
        Content content,
        Guid publishedBy,
        string publishedByName,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Send policy acceptance reminder
    /// </summary>
    Task SendPolicyAcceptanceReminderAsync(
        PolicyDocument policy,
        Guid userId,
        string userName,
        string userEmail,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Send policy update notification
    /// </summary>
    Task SendPolicyUpdateNotificationAsync(
        PolicyDocument policy,
        List<Guid> affectedUserIds,
        CancellationToken cancellationToken = default);
}
