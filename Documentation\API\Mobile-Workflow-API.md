# 📱 Mobile & Workflow Service API Documentation

## 📋 Overview

The Mobile & Workflow Service provides comprehensive mobile application management, cross-platform synchronization, workflow automation, and business process management. This service is 60% complete with core mobile features production-ready and advanced workflow capabilities in development.

**Base URL**: `/api/v1/mobile` (via API Gateway)  
**Direct URL**: `http://localhost:5014`  
**Authentication**: Required for all endpoints  
**Authorization**: Role-based access control

## 🔐 Authentication & Permissions

### Required Permissions

| Operation                 | Permission               | Roles                    |
| ------------------------- | ------------------------ | ------------------------ |
| Manage mobile apps        | `mobile.apps.manage`     | Admin                    |
| Create mobile sessions    | `mobile.sessions.create` | All authenticated users  |
| Access driver features    | `mobile.driver.access`   | Driver, Carrier          |
| Manage workflows          | `workflow.manage`        | Admin, Manager           |
| Execute workflows         | `workflow.execute`       | All authenticated users  |
| Manage business processes | `process.manage`         | Admin, Manager           |
| Access offline data       | `mobile.offline.access`  | All authenticated users  |
| Manage device settings    | `mobile.device.manage`   | Admin, User (own device) |

## 📊 Core Endpoints

### 1. Mobile Application Management

#### Register Mobile App

```http
POST /api/v1/mobile/apps
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "TLI Driver App",
  "description": "Mobile application for drivers to manage trips and deliveries",
  "platform": "Android",
  "version": "2.1.0",
  "packageName": "com.tli.driver",
  "bundleId": "com.tli.driver",
  "supportedFeatures": [
    "OfflineMode",
    "PushNotifications",
    "LocationTracking",
    "CameraAccess",
    "QRCodeScanner",
    "Biometrics"
  ],
  "configuration": {
    "offlineStorageLimit": 100,
    "syncInterval": 300,
    "locationUpdateInterval": 30,
    "maxCacheSize": 50,
    "enableAnalytics": true,
    "enableCrashReporting": true
  },
  "targetAudience": ["Driver", "Carrier"],
  "minimumOSVersion": "8.0",
  "permissions": [
    "CAMERA",
    "LOCATION",
    "STORAGE",
    "NETWORK",
    "BIOMETRIC"
  ]
}
```

**Response:**

```json
{
  "id": "app-uuid",
  "name": "TLI Driver App",
  "description": "Mobile application for drivers to manage trips and deliveries",
  "platform": "Android",
  "version": "2.1.0",
  "packageName": "com.tli.driver",
  "status": "Active",
  "registeredAt": "2024-01-15T10:30:00Z",
  "lastUpdated": "2024-01-15T10:30:00Z",
  "supportedFeatures": [
    "OfflineMode",
    "PushNotifications",
    "LocationTracking",
    "CameraAccess",
    "QRCodeScanner",
    "Biometrics"
  ],
  "configuration": {
    "offlineStorageLimit": 100,
    "syncInterval": 300,
    "locationUpdateInterval": 30,
    "maxCacheSize": 50,
    "enableAnalytics": true,
    "enableCrashReporting": true
  },
  "downloadUrl": "https://play.google.com/store/apps/details?id=com.tli.driver",
  "apiKey": "app-api-key-uuid",
  "statistics": {
    "totalInstalls": 0,
    "activeUsers": 0,
    "averageRating": 0.0
  }
}
```

#### Get Mobile Apps

```http
GET /api/v1/mobile/apps
Authorization: Bearer <token>
```

**Query Parameters:**

- `platform`: Filter by platform (`iOS|Android|Web|PWA`)
- `status`: Filter by status (`Active|Inactive|Deprecated`)
- `targetAudience`: Filter by target audience
- `page`: Page number (default: 1)
- `pageSize`: Items per page (default: 20)

**Response:**

```json
{
  "data": [
    {
      "id": "app-uuid",
      "name": "TLI Driver App",
      "platform": "Android",
      "version": "2.1.0",
      "status": "Active",
      "targetAudience": ["Driver", "Carrier"],
      "lastUpdated": "2024-01-15T10:30:00Z",
      "statistics": {
        "totalInstalls": 1250,
        "activeUsers": 980,
        "averageRating": 4.6
      }
    }
  ],
  "pagination": {
    "currentPage": 1,
    "pageSize": 20,
    "totalItems": 8,
    "totalPages": 1
  }
}
```

#### Update Mobile App

```http
PUT /api/v1/mobile/apps/{appId}
Authorization: Bearer <token>
Content-Type: application/json

{
  "version": "2.2.0",
  "configuration": {
    "offlineStorageLimit": 150,
    "syncInterval": 240,
    "enableAnalytics": true
  },
  "supportedFeatures": [
    "OfflineMode",
    "PushNotifications",
    "LocationTracking",
    "CameraAccess",
    "QRCodeScanner",
    "Biometrics",
    "NFC"
  ]
}
```

### 2. Mobile Sessions

#### Create Mobile Session

```http
POST /api/v1/mobile/sessions
Authorization: Bearer <token>
Content-Type: application/json

{
  "appId": "app-uuid",
  "deviceInfo": {
    "deviceId": "device-unique-id",
    "platform": "Android",
    "osVersion": "13.0",
    "appVersion": "2.1.0",
    "deviceModel": "Samsung Galaxy S23",
    "screenResolution": "1080x2400",
    "timezone": "Asia/Kolkata",
    "language": "en-IN"
  },
  "location": {
    "latitude": 19.0760,
    "longitude": 72.8777,
    "accuracy": 10.5,
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "capabilities": {
    "supportsOffline": true,
    "supportsPushNotifications": true,
    "supportsLocationTracking": true,
    "supportsBiometrics": true,
    "supportsCamera": true,
    "supportsNFC": false
  }
}
```

**Response:**

```json
{
  "sessionId": "session-uuid",
  "userId": "user-uuid",
  "appId": "app-uuid",
  "status": "Active",
  "createdAt": "2024-01-15T10:30:00Z",
  "expiresAt": "2024-01-15T22:30:00Z",
  "deviceInfo": {
    "deviceId": "device-unique-id",
    "platform": "Android",
    "osVersion": "13.0",
    "appVersion": "2.1.0",
    "deviceModel": "Samsung Galaxy S23"
  },
  "sessionToken": "session-jwt-token",
  "refreshToken": "refresh-jwt-token",
  "configuration": {
    "syncInterval": 300,
    "locationUpdateInterval": 30,
    "offlineStorageLimit": 100,
    "features": {
      "offlineMode": true,
      "pushNotifications": true,
      "locationTracking": true,
      "biometrics": true
    }
  },
  "serverTime": "2024-01-15T10:30:00Z"
}
```

#### Get Session Status

```http
GET /api/v1/mobile/sessions/{sessionId}
Authorization: Bearer <token>
```

#### Update Session

```http
PUT /api/v1/mobile/sessions/{sessionId}
Authorization: Bearer <token>
Content-Type: application/json

{
  "location": {
    "latitude": 19.0850,
    "longitude": 72.8950,
    "accuracy": 8.2,
    "timestamp": "2024-01-15T10:35:00Z"
  },
  "status": "Active",
  "lastActivity": "2024-01-15T10:35:00Z"
}
```

### 3. Driver Mobile Features

#### Get Driver Dashboard

```http
GET /api/v1/mobile/driver/dashboard
Authorization: Bearer <token>
```

**Response:**

```json
{
  "driverId": "driver-uuid",
  "status": "Available",
  "currentTrip": {
    "tripId": "trip-uuid",
    "status": "InProgress",
    "pickupLocation": "Mumbai Airport",
    "deliveryLocation": "Delhi Railway Station",
    "estimatedArrival": "2024-01-15T18:00:00Z",
    "progress": 65.5
  },
  "pendingTasks": [
    {
      "taskId": "task-uuid",
      "type": "PODUpload",
      "description": "Upload proof of delivery for trip TRP-001",
      "priority": "High",
      "dueDate": "2024-01-15T12:00:00Z"
    }
  ],
  "todayStats": {
    "tripsCompleted": 3,
    "totalDistance": 450.5,
    "totalEarnings": {
      "amount": 2500.0,
      "currency": "INR"
    },
    "hoursWorked": 8.5,
    "fuelConsumed": 35.2
  },
  "notifications": [
    {
      "id": "notification-uuid",
      "type": "TripAssignment",
      "message": "New trip assigned: Mumbai to Pune",
      "timestamp": "2024-01-15T10:25:00Z",
      "isRead": false
    }
  ],
  "vehicleStatus": {
    "vehicleId": "vehicle-uuid",
    "fuelLevel": 75.5,
    "batteryLevel": 85.0,
    "lastMaintenance": "2024-01-10T00:00:00Z",
    "nextMaintenance": "2024-02-10T00:00:00Z",
    "healthScore": 92.5
  }
}
```

#### Accept Trip Assignment

```http
POST /api/v1/mobile/driver/trip-assignments/{tripId}/accept
Authorization: Bearer <token>
Content-Type: application/json

{
  "acceptedAt": "2024-01-15T10:30:00Z",
  "estimatedArrival": "2024-01-15T11:00:00Z",
  "notes": "Will reach pickup location in 30 minutes"
}
```

#### Update Driver Location

```http
POST /api/v1/mobile/driver/location
Authorization: Bearer <token>
Content-Type: application/json

{
  "latitude": 19.0760,
  "longitude": 72.8777,
  "accuracy": 10.5,
  "speed": 45.2,
  "heading": 180.0,
  "altitude": 15.5,
  "timestamp": "2024-01-15T10:30:00Z",
  "tripId": "trip-uuid"
}
```

#### Upload Proof of Delivery

```http
POST /api/v1/mobile/driver/trips/{tripId}/pod
Authorization: Bearer <token>
Content-Type: multipart/form-data

{
  "deliveryPhoto": [binary image data],
  "recipientSignature": [binary signature data],
  "recipientName": "John Doe",
  "recipientId": "ID123456789",
  "deliveryNotes": "Package delivered in good condition",
  "deliveryLocation": {
    "latitude": 19.0760,
    "longitude": 72.8777,
    "accuracy": 5.0,
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "additionalDocuments": [binary file data array]
}
```

### 4. Workflow Management

#### Get All Workflows

```http
GET /api/v1/mobile/workflows
Authorization: Bearer <token>
```

**Query Parameters:**

- `category`: Filter by category
- `isActive`: Filter by active status
- `triggerType`: Filter by trigger type
- `page`: Page number
- `pageSize`: Items per page

**Response:**

```json
{
  "data": [
    {
      "id": "workflow-uuid",
      "name": "Driver Onboarding",
      "description": "Complete workflow for onboarding new drivers",
      "category": "UserOnboarding",
      "version": "1.0",
      "isActive": true,
      "triggerType": "Manual",
      "lastExecuted": "2024-01-15T09:00:00Z",
      "executionCount": 25,
      "averageExecutionTime": "00:15:30",
      "successRate": 96.0,
      "createdBy": "<EMAIL>",
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ]
}
```

#### Create Workflow

```http
POST /api/v1/mobile/workflows
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "Trip Completion Workflow",
  "description": "Automated workflow for trip completion processing",
  "category": "TripManagement",
  "version": "1.0",
  "definition": {
    "steps": [
      {
        "id": "validate_pod",
        "name": "Validate Proof of Delivery",
        "type": "Automated",
        "parameters": {
          "required_fields": ["photo", "signature", "recipient_name"],
          "validation_rules": ["photo_quality", "signature_validity"]
        },
        "nextSteps": ["update_trip_status"]
      },
      {
        "id": "update_trip_status",
        "name": "Update Trip Status",
        "type": "Automated",
        "parameters": {
          "status": "Completed",
          "completion_time": "current_timestamp"
        },
        "nextSteps": ["notify_customer"]
      },
      {
        "id": "notify_customer",
        "name": "Notify Customer",
        "type": "Notification",
        "parameters": {
          "template": "trip_completion",
          "channels": ["email", "sms"]
        },
        "nextSteps": ["generate_invoice"]
      }
    ]
  },
  "triggerType": "Event",
  "triggerConfiguration": {
    "eventType": "PODUploaded",
    "conditions": {
      "trip_status": "InProgress"
    }
  },
  "isActive": true
}
```

#### Execute Workflow

```http
POST /api/v1/mobile/workflows/{workflowId}/execute
Authorization: Bearer <token>
Content-Type: application/json

{
  "triggerData": {
    "tripId": "trip-uuid",
    "driverId": "driver-uuid",
    "podData": {
      "photoUrl": "https://storage.tli.com/pod/photo-uuid.jpg",
      "signatureUrl": "https://storage.tli.com/pod/signature-uuid.png",
      "recipientName": "John Doe"
    }
  },
  "priority": "Normal",
  "scheduledAt": "2024-01-15T10:30:00Z"
}
```

**Response:**

```json
{
  "executionId": "execution-uuid",
  "workflowId": "workflow-uuid",
  "status": "Running",
  "startedAt": "2024-01-15T10:30:00Z",
  "estimatedCompletionTime": "2024-01-15T10:35:00Z",
  "currentStep": "validate_pod",
  "progress": 33.3,
  "executedBy": "<EMAIL>",
  "triggerData": {
    "tripId": "trip-uuid",
    "driverId": "driver-uuid"
  }
}
```

#### Get Workflow Execution Status

```http
GET /api/v1/mobile/workflows/executions/{executionId}
Authorization: Bearer <token>
```

### 5. Offline Data Management

#### Sync Offline Data

```http
POST /api/v1/mobile/offline/sync
Authorization: Bearer <token>
Content-Type: application/json

{
  "deviceId": "device-unique-id",
  "lastSyncTimestamp": "2024-01-15T09:00:00Z",
  "offlineData": [
    {
      "id": "offline-data-uuid",
      "entityType": "TripUpdate",
      "entityId": "trip-uuid",
      "action": "Update",
      "data": {
        "status": "InProgress",
        "currentLocation": {
          "latitude": 19.0760,
          "longitude": 72.8777
        },
        "timestamp": "2024-01-15T10:15:00Z"
      },
      "createdAt": "2024-01-15T10:15:00Z",
      "priority": "High"
    }
  ],
  "conflictResolution": "ServerWins"
}
```

**Response:**

```json
{
  "syncId": "sync-uuid",
  "status": "Completed",
  "processedItems": 15,
  "successfulItems": 14,
  "failedItems": 1,
  "conflicts": 0,
  "serverTimestamp": "2024-01-15T10:30:00Z",
  "nextSyncRecommended": "2024-01-15T10:35:00Z",
  "downloadData": [
    {
      "entityType": "Trip",
      "entityId": "new-trip-uuid",
      "action": "Create",
      "data": {
        "tripId": "new-trip-uuid",
        "pickupLocation": "Mumbai Central",
        "deliveryLocation": "Pune Station",
        "scheduledTime": "2024-01-15T14:00:00Z"
      },
      "timestamp": "2024-01-15T10:25:00Z"
    }
  ],
  "errors": [
    {
      "itemId": "offline-data-uuid-2",
      "error": "Validation failed: Invalid location coordinates",
      "code": "VALIDATION_ERROR"
    }
  ]
}
```

#### Get Offline Data Status

```http
GET /api/v1/mobile/offline/status
Authorization: Bearer <token>
```

**Query Parameters:**

- `deviceId`: Device identifier

**Response:**

```json
{
  "deviceId": "device-unique-id",
  "lastSyncTimestamp": "2024-01-15T10:30:00Z",
  "pendingItems": 5,
  "storageUsed": 25.5,
  "storageLimit": 100.0,
  "syncStatus": "Completed",
  "nextScheduledSync": "2024-01-15T10:35:00Z",
  "offlineCapabilities": {
    "maxOfflineDuration": "24h",
    "supportedOperations": ["TripUpdate", "LocationUpdate", "PODUpload"],
    "conflictResolutionStrategies": ["ServerWins", "ClientWins", "Manual"]
  }
}
```

### 6. Progressive Web App (PWA) Features

#### Get PWA Configuration

```http
GET /api/v1/mobile/pwa/config
Authorization: Bearer <token>
```

**Response:**

```json
{
  "manifestUrl": "/manifest.json",
  "serviceWorkerUrl": "/sw.js",
  "capabilities": {
    "supportsServiceWorker": true,
    "supportsWebManifest": true,
    "supportsPushNotifications": true,
    "supportsBackgroundSync": true,
    "supportsOfflineStorage": true,
    "supportsInstallPrompt": true,
    "supportsFullscreen": true,
    "supportsStandalone": true,
    "supportsWebShare": true,
    "supportsGeolocation": true,
    "supportsCamera": true,
    "supportsBiometrics": false
  },
  "installPrompt": {
    "enabled": true,
    "minVisits": 3,
    "minEngagementTime": 300,
    "showAfterDays": 7
  },
  "offlineStrategy": {
    "cacheFirst": ["images", "fonts", "css"],
    "networkFirst": ["api", "dynamic-content"],
    "staleWhileRevalidate": ["static-content"]
  },
  "pushNotifications": {
    "vapidPublicKey": "vapid-public-key",
    "subscriptionEndpoint": "/api/v1/mobile/pwa/push/subscribe"
  }
}
```

#### Subscribe to Push Notifications

```http
POST /api/v1/mobile/pwa/push/subscribe
Authorization: Bearer <token>
Content-Type: application/json

{
  "subscription": {
    "endpoint": "https://fcm.googleapis.com/fcm/send/...",
    "keys": {
      "p256dh": "key-data",
      "auth": "auth-data"
    }
  },
  "deviceInfo": {
    "userAgent": "Mozilla/5.0...",
    "platform": "Web",
    "language": "en-US"
  }
}
```

### 7. Business Process Automation

#### Get Active Business Processes

```http
GET /api/v1/mobile/business-processes
Authorization: Bearer <token>
```

#### Trigger Business Process

```http
POST /api/v1/mobile/business-processes/{processName}/trigger
Authorization: Bearer <token>
Content-Type: application/json

{
  "triggerData": {
    "orderId": "order-uuid",
    "customerId": "customer-uuid",
    "priority": "High"
  },
  "context": {
    "source": "MobileApp",
    "triggeredBy": "<EMAIL>"
  }
}
```

## 🔄 Real-time Updates

### SignalR Hub: `/api/v1/mobile/hub`

**Events:**

- `TripAssigned` - New trip assigned to driver
- `TripStatusChanged` - Trip status updated
- `LocationUpdateReceived` - Driver location updated
- `WorkflowExecutionCompleted` - Workflow execution finished
- `OfflineSyncCompleted` - Offline data sync completed
- `NotificationReceived` - New notification for user
- `DeviceStatusChanged` - Device status changed

**Example Usage:**

```typescript
const connection = new signalR.HubConnectionBuilder()
  .withUrl('/api/v1/mobile/hub')
  .build()

// Listen for trip assignments
connection.on('TripAssigned', (data) => {
  console.log('New trip assigned:', data)
  // Show trip assignment notification
})

// Listen for workflow updates
connection.on('WorkflowExecutionCompleted', (data) => {
  console.log('Workflow completed:', data)
  // Update workflow status in UI
})
```

## ❌ Error Responses

### Common Error Codes

| Code | Message                       | Description                                    |
| ---- | ----------------------------- | ---------------------------------------------- |
| 400  | `INVALID_DEVICE_INFO`         | Invalid device information provided            |
| 400  | `INVALID_WORKFLOW_DEFINITION` | Invalid workflow definition                    |
| 400  | `UNSUPPORTED_PLATFORM`        | Platform not supported                         |
| 401  | `UNAUTHORIZED`                | Authentication required                        |
| 403  | `INSUFFICIENT_PERMISSIONS`    | Insufficient permissions for mobile operations |
| 404  | `APP_NOT_FOUND`               | Mobile app not found                           |
| 404  | `SESSION_NOT_FOUND`           | Mobile session not found                       |
| 404  | `WORKFLOW_NOT_FOUND`          | Workflow not found                             |
| 409  | `SESSION_ALREADY_EXISTS`      | Active session already exists for device       |
| 422  | `SYNC_CONFLICT`               | Data synchronization conflict                  |
| 422  | `VALIDATION_FAILED`           | Input validation failed                        |
| 429  | `RATE_LIMIT_EXCEEDED`         | Too many requests                              |
| 503  | `OFFLINE_MODE_UNAVAILABLE`    | Offline mode temporarily unavailable           |

### Error Response Format

```json
{
  "error": {
    "code": "SYNC_CONFLICT",
    "message": "Data synchronization conflict detected",
    "details": "Server data has been modified since last sync",
    "timestamp": "2024-01-01T00:00:00Z",
    "traceId": "trace-uuid",
    "conflictData": {
      "entityId": "trip-uuid",
      "serverVersion": 5,
      "clientVersion": 3
    }
  }
}
```

## 📋 Integration Examples

### Frontend Integration Example (React Native)

```typescript
// Mobile Service Client
class MobileServiceClient {
  private apiClient = ApiClient.getInstance()

  async createSession(deviceInfo: DeviceInfo): Promise<MobileSession> {
    const response = await this.apiClient.axios.post('/mobile/sessions', {
      appId: 'tli-driver-app',
      deviceInfo,
      capabilities: await this.getDeviceCapabilities(),
    })
    return response.data
  }

  async syncOfflineData(offlineData: OfflineDataItem[]): Promise<SyncResult> {
    const response = await this.apiClient.axios.post('/mobile/offline/sync', {
      deviceId: await this.getDeviceId(),
      lastSyncTimestamp: await this.getLastSyncTimestamp(),
      offlineData,
      conflictResolution: 'ServerWins',
    })
    return response.data
  }

  async updateDriverLocation(location: LocationData): Promise<void> {
    await this.apiClient.axios.post('/mobile/driver/location', location)
  }
}

// React Native Component Example
function DriverDashboard() {
  const [dashboard, setDashboard] = useState<DriverDashboard | null>(null)
  const [isOnline, setIsOnline] = useState(true)
  const mobileClient = new MobileServiceClient()

  // SignalR integration for real-time updates
  const { connection } = useSignalR({
    hubName: 'Mobile',
    hubUrl: '/api/v1/mobile/hub',
  })

  useEffect(() => {
    const loadDashboard = async () => {
      try {
        const dashboardData = await mobileClient.getDriverDashboard()
        setDashboard(dashboardData)
      } catch (error) {
        console.error('Failed to load dashboard:', error)
      }
    }

    loadDashboard()
  }, [])

  useEffect(() => {
    if (connection) {
      // Listen for trip assignments
      const unsubscribeTripAssigned = connection.on('TripAssigned', (trip) => {
        setDashboard((prev) =>
          prev
            ? {
                ...prev,
                pendingTasks: [
                  ...prev.pendingTasks,
                  {
                    taskId: trip.id,
                    type: 'TripAcceptance',
                    description: `Accept trip: ${trip.pickupLocation} to ${trip.deliveryLocation}`,
                    priority: 'High',
                    dueDate: trip.acceptanceDeadline,
                  },
                ],
              }
            : null
        )

        // Show notification
        showNotification(
          'New Trip Assignment',
          `Trip from ${trip.pickupLocation} to ${trip.deliveryLocation}`
        )
      })

      return () => {
        unsubscribeTripAssigned()
      }
    }
  }, [connection])

  // Offline data sync
  useEffect(() => {
    const syncInterval = setInterval(async () => {
      if (isOnline) {
        try {
          const offlineData = await getStoredOfflineData()
          if (offlineData.length > 0) {
            const syncResult = await mobileClient.syncOfflineData(offlineData)
            if (syncResult.status === 'Completed') {
              await clearStoredOfflineData()
            }
          }
        } catch (error) {
          console.error('Sync failed:', error)
        }
      }
    }, 30000) // Sync every 30 seconds

    return () => clearInterval(syncInterval)
  }, [isOnline])

  const handleAcceptTrip = async (tripId: string) => {
    try {
      await mobileClient.acceptTrip(tripId)
      // Update dashboard
      setDashboard((prev) =>
        prev
          ? {
              ...prev,
              currentTrip: { ...prev.currentTrip, status: 'Accepted' },
              pendingTasks: prev.pendingTasks.filter(
                (task) => task.taskId !== tripId
              ),
            }
          : null
      )
    } catch (error) {
      console.error('Failed to accept trip:', error)
    }
  }

  if (!dashboard) return <LoadingSpinner />

  return (
    <ScrollView style={styles.container}>
      <StatusCard status={dashboard.status} />

      {dashboard.currentTrip && (
        <CurrentTripCard
          trip={dashboard.currentTrip}
          onUpdateLocation={mobileClient.updateDriverLocation}
        />
      )}

      <PendingTasksList
        tasks={dashboard.pendingTasks}
        onAcceptTrip={handleAcceptTrip}
      />

      <TodayStatsCard stats={dashboard.todayStats} />

      <VehicleStatusCard status={dashboard.vehicleStatus} />
    </ScrollView>
  )
}

// Offline data management
async function storeOfflineData(data: OfflineDataItem) {
  const storage = await AsyncStorage.getItem('offlineData')
  const offlineData = storage ? JSON.parse(storage) : []
  offlineData.push(data)
  await AsyncStorage.setItem('offlineData', JSON.stringify(offlineData))
}

async function getStoredOfflineData(): Promise<OfflineDataItem[]> {
  const storage = await AsyncStorage.getItem('offlineData')
  return storage ? JSON.parse(storage) : []
}
```
