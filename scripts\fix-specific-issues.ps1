# Fix specific compilation issues
Write-Host "Fixing specific compilation issues..." -ForegroundColor Green

# Fix OrderManagement duplicate enum definitions
$orderEnumsFile = "Services\OrderManagement\OrderManagement.Domain\Enums\OrderEnums.cs"
if (Test-Path $orderEnumsFile) {
    Write-Host "Fixing duplicate enums in OrderEnums.cs..." -ForegroundColor Yellow
    $content = Get-Content $orderEnumsFile -Raw
    
    # Remove duplicate AdministrativeAction enum (keep only the first one)
    $content = $content -replace "(?s)(\s+public enum AdministrativeAction.*?\})\s+public enum AdministrativeAction.*?\}", "`$1"
    
    # Remove duplicate AuditSeverity enum (keep only the first one)
    $content = $content -replace "(?s)(\s+public enum AuditSeverity.*?\})\s+public enum AuditSeverity.*?\}", "`$1"
    
    Set-Content -Path $orderEnumsFile -Value $content -Encoding UTF8
    Write-Host "  Fixed duplicate enums in OrderEnums.cs" -ForegroundColor Green
}

# Fix MobileWorkflow SyncItem syntax error
$syncItemFile = "Services\MobileWorkflow\MobileWorkflow.Domain\Entities\SyncItem.cs"
if (Test-Path $syncItemFile) {
    Write-Host "Fixing syntax error in SyncItem.cs..." -ForegroundColor Yellow
    $content = Get-Content $syncItemFile -Raw
    
    # Fix missing identifier after comma
    $content = $content -replace ",\s*\}", "}"
    $content = $content -replace ",\s*;", ";"
    
    Set-Content -Path $syncItemFile -Value $content -Encoding UTF8
    Write-Host "  Fixed syntax error in SyncItem.cs" -ForegroundColor Green
}

# Fix UserManagement command handler syntax errors
$logoHandlerFile = "Services\UserManagement\UserManagement.Application\Commands\TransportCompanyLogo\UploadTransportCompanyLogoCommandHandler.cs"
if (Test-Path $logoHandlerFile) {
    Write-Host "Fixing syntax errors in UploadTransportCompanyLogoCommandHandler.cs..." -ForegroundColor Yellow
    $content = Get-Content $logoHandlerFile -Raw
    
    # Fix missing identifiers
    $content = $content -replace "catch\s*\(\s*\)", "catch (Exception ex)"
    $content = $content -replace "throw\s*;", "throw;"
    
    Set-Content -Path $logoHandlerFile -Value $content -Encoding UTF8
    Write-Host "  Fixed syntax errors in UploadTransportCompanyLogoCommandHandler.cs" -ForegroundColor Green
}

$ocrHandlerFile = "Services\UserManagement\UserManagement.Application\Commands\TransportCompanyOCR\ProcessTransportCompanyDocumentWithOCRCommandHandler.cs"
if (Test-Path $ocrHandlerFile) {
    Write-Host "Fixing syntax errors in ProcessTransportCompanyDocumentWithOCRCommandHandler.cs..." -ForegroundColor Yellow
    $content = Get-Content $ocrHandlerFile -Raw
    
    # Fix missing identifiers
    $content = $content -replace "catch\s*\(\s*\)", "catch (Exception ex)"
    $content = $content -replace "throw\s*;", "throw;"
    
    Set-Content -Path $ocrHandlerFile -Value $content -Encoding UTF8
    Write-Host "  Fixed syntax errors in ProcessTransportCompanyDocumentWithOCRCommandHandler.cs" -ForegroundColor Green
}

# Fix TripManagement Vehicle.cs Application references
$vehicleFile = "Services\TripManagement\TripManagement.Domain\Entities\Vehicle.cs"
if (Test-Path $vehicleFile) {
    Write-Host "Fixing Application references in Vehicle.cs..." -ForegroundColor Yellow
    $content = Get-Content $vehicleFile -Raw
    
    # Replace Application with proper namespace or remove if not needed
    $content = $content -replace "\bApplication\b", "System.Application"
    
    Set-Content -Path $vehicleFile -Value $content -Encoding UTF8
    Write-Host "  Fixed Application references in Vehicle.cs" -ForegroundColor Green
}

# Fix Shared.Infrastructure IEntity reference
$optimizedRepoFile = "Shared\Shared.Infrastructure\Repositories\OptimizedRepositoryBase.cs"
if (Test-Path $optimizedRepoFile) {
    Write-Host "Fixing IEntity reference in OptimizedRepositoryBase.cs..." -ForegroundColor Yellow
    $content = Get-Content $optimizedRepoFile -Raw
    
    # Fix the using statement
    $content = $content -replace "using Shared\.Domain\.Entities;", "using Shared.Domain.Common;"
    
    # Fix IEntity references
    $content = $content -replace "\bIEntity<", "IBaseEntity<"
    
    Set-Content -Path $optimizedRepoFile -Value $content -Encoding UTF8
    Write-Host "  Fixed IEntity reference in OptimizedRepositoryBase.cs" -ForegroundColor Green
}

Write-Host "Specific issues fixes completed!" -ForegroundColor Green
