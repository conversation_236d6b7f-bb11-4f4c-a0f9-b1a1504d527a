using FluentValidation;
using DataStorage.Application.Commands;
using DataStorage.Application.Queries;
using DataStorage.Domain.Enums;

namespace DataStorage.Application.Validators;

public class UploadDocumentCommandValidator : AbstractValidator<UploadDocumentCommand>
{
    public UploadDocumentCommandValidator()
    {
        RuleFor(x => x.Title)
            .NotEmpty()
            .WithMessage("Document title is required")
            .MaximumLength(255)
            .WithMessage("Document title cannot exceed 255 characters");

        RuleFor(x => x.Description)
            .MaximumLength(1000)
            .WithMessage("Description cannot exceed 1000 characters");

        RuleFor(x => x.FileName)
            .NotEmpty()
            .WithMessage("File name is required")
            .MaximumLength(255)
            .WithMessage("File name cannot exceed 255 characters");

        RuleFor(x => x.ContentType)
            .NotEmpty()
            .WithMessage("Content type is required");

        RuleFor(x => x.OwnerId)
            .NotEmpty()
            .WithMessage("Owner ID is required");

        RuleFor(x => x.OwnerType)
            .NotEmpty()
            .WithMessage("Owner type is required")
            .Must(BeValidOwnerType)
            .WithMessage("Invalid owner type");

        RuleFor(x => x.DocumentType)
            .IsInEnum()
            .WithMessage("Invalid document type");

        RuleFor(x => x.Category)
            .IsInEnum()
            .WithMessage("Invalid document category");

        RuleFor(x => x.AccessLevel)
            .IsInEnum()
            .WithMessage("Invalid access level");

        RuleFor(x => x.FileStream)
            .NotNull()
            .WithMessage("File stream is required")
            .Must(BeReadableStream)
            .WithMessage("File stream must be readable");

        RuleFor(x => x.ExpiresAt)
            .GreaterThan(DateTime.UtcNow)
            .When(x => x.ExpiresAt.HasValue)
            .WithMessage("Expiry date must be in the future");
    }

    private static bool BeValidOwnerType(string ownerType)
    {
        var validTypes = new[] { "Admin", "TransportCompany", "Broker", "Carrier", "Driver", "Shipper" };
        return validTypes.Contains(ownerType, StringComparer.OrdinalIgnoreCase);
    }

    private static bool BeReadableStream(Stream? stream)
    {
        return stream != null && stream.CanRead;
    }
}

public class UpdateDocumentCommandValidator : AbstractValidator<UpdateDocumentCommand>
{
    public UpdateDocumentCommandValidator()
    {
        RuleFor(x => x.DocumentId)
            .NotEmpty()
            .WithMessage("Document ID is required");

        RuleFor(x => x.Title)
            .NotEmpty()
            .WithMessage("Document title is required")
            .MaximumLength(255)
            .WithMessage("Document title cannot exceed 255 characters");

        RuleFor(x => x.Description)
            .MaximumLength(1000)
            .WithMessage("Description cannot exceed 1000 characters");

        RuleFor(x => x.UserId)
            .NotEmpty()
            .WithMessage("User ID is required");
    }
}

public class ChangeDocumentStatusCommandValidator : AbstractValidator<ChangeDocumentStatusCommand>
{
    public ChangeDocumentStatusCommandValidator()
    {
        RuleFor(x => x.DocumentId)
            .NotEmpty()
            .WithMessage("Document ID is required");

        RuleFor(x => x.NewStatus)
            .IsInEnum()
            .WithMessage("Invalid document status");

        RuleFor(x => x.UserId)
            .NotEmpty()
            .WithMessage("User ID is required");

        RuleFor(x => x.Reason)
            .MaximumLength(500)
            .WithMessage("Reason cannot exceed 500 characters");
    }
}

public class GrantDocumentAccessCommandValidator : AbstractValidator<GrantDocumentAccessCommand>
{
    public GrantDocumentAccessCommandValidator()
    {
        RuleFor(x => x.DocumentId)
            .NotEmpty()
            .WithMessage("Document ID is required");

        RuleFor(x => x.TargetUserId)
            .NotEmpty()
            .WithMessage("Target user ID is required");

        RuleFor(x => x.TargetUserType)
            .NotEmpty()
            .WithMessage("Target user type is required");

        RuleFor(x => x.AccessLevel)
            .IsInEnum()
            .WithMessage("Invalid access level");

        RuleFor(x => x.RequestingUserId)
            .NotEmpty()
            .WithMessage("Requesting user ID is required");

        RuleFor(x => x.ExpiresAt)
            .GreaterThan(DateTime.UtcNow)
            .When(x => x.ExpiresAt.HasValue)
            .WithMessage("Expiry date must be in the future");
    }
}

public class SearchDocumentsQueryValidator : AbstractValidator<SearchDocumentsQuery>
{
    public SearchDocumentsQueryValidator()
    {
        RuleFor(x => x.PageNumber)
            .GreaterThan(0)
            .WithMessage("Page number must be greater than 0");

        RuleFor(x => x.PageSize)
            .GreaterThan(0)
            .WithMessage("Page size must be greater than 0")
            .LessThanOrEqualTo(100)
            .WithMessage("Page size cannot exceed 100");

        RuleFor(x => x.RequestingUserId)
            .NotEmpty()
            .WithMessage("Requesting user ID is required");

        RuleFor(x => x.CreatedAfter)
            .LessThan(x => x.CreatedBefore)
            .When(x => x.CreatedAfter.HasValue && x.CreatedBefore.HasValue)
            .WithMessage("Created after date must be before created before date");
    }
}

public class GetDocumentByIdQueryValidator : AbstractValidator<GetDocumentByIdQuery>
{
    public GetDocumentByIdQueryValidator()
    {
        RuleFor(x => x.DocumentId)
            .NotEmpty()
            .WithMessage("Document ID is required");

        RuleFor(x => x.UserId)
            .NotEmpty()
            .WithMessage("User ID is required");
    }
}
