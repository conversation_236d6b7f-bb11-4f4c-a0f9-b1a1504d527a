using TripManagement.Application.Commands.DelayAlert;
using TripManagement.Application.Services;
using TripManagement.Domain.Repositories;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace TripManagement.API.Controllers;

/// <summary>
/// Controller for Transport Company delay alert management
/// </summary>
[ApiController]
[Route("api/transport-company/delay-alerts")]
[Authorize(Roles = "TransportCompany,Admin")]
public class TransportCompanyDelayAlertController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly IDelayAlertMonitoringService _delayAlertService;
    private readonly IDelayAlertRepository _delayAlertRepository;
    private readonly ILogger<TransportCompanyDelayAlertController> _logger;

    public TransportCompanyDelayAlertController(
        IMediator mediator,
        IDelayAlertMonitoringService delayAlertService,
        IDelayAlertRepository delayAlertRepository,
        ILogger<TransportCompanyDelayAlertController> logger)
    {
        _mediator = mediator;
        _delayAlertService = delayAlertService;
        _delayAlertRepository = delayAlertRepository;
        _logger = logger;
    }

    /// <summary>
    /// Get active delay alerts for a transport company
    /// </summary>
    [HttpGet("company/{transportCompanyId}/active")]
    [ProducesResponseType(typeof(List<DelayAlertSummaryDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<List<DelayAlertSummaryDto>>> GetActiveDelayAlerts(Guid transportCompanyId)
    {
        try
        {
            if (!await CanViewDelayAlerts(transportCompanyId))
            {
                return Forbid("You don't have permission to view delay alerts for this transport company");
            }

            var alerts = await _delayAlertRepository.GetActiveAlertsByTransportCompanyAsync(
                transportCompanyId, HttpContext.RequestAborted);

            var alertSummaries = alerts.Select(a => new DelayAlertSummaryDto
            {
                Id = a.Id,
                TripId = a.TripId,
                AlertType = a.AlertType,
                Severity = a.Severity,
                Status = a.Status,
                Title = a.Title,
                DelayDuration = a.DelayDuration,
                EscalationLevel = a.EscalationLevel,
                CreatedAt = a.CreatedAt,
                AcknowledgedAt = a.AcknowledgedAt,
                IsExpired = a.IsExpired()
            }).ToList();

            return Ok(alertSummaries);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active delay alerts for transport company {TransportCompanyId}",
                transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get delay alert details by ID
    /// </summary>
    [HttpGet("{id}")]
    [ProducesResponseType(typeof(DelayAlertDetailsDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<DelayAlertDetailsDto>> GetDelayAlert(Guid id)
    {
        try
        {
            var alert = await _delayAlertRepository.GetByIdAsync(id, HttpContext.RequestAborted);
            if (alert == null)
            {
                return NotFound("Delay alert not found");
            }

            if (!await CanViewDelayAlerts(alert.TransportCompanyId))
            {
                return Forbid("You don't have permission to view this delay alert");
            }

            var alertDetails = new DelayAlertDetailsDto
            {
                Id = alert.Id,
                TripId = alert.TripId,
                TripStopId = alert.TripStopId,
                TransportCompanyId = alert.TransportCompanyId,
                ShipperId = alert.ShipperId,
                AlertType = alert.AlertType,
                Severity = alert.Severity,
                Status = alert.Status,
                Title = alert.Title,
                Description = alert.Description,
                DelayDuration = alert.DelayDuration,
                ScheduledTime = alert.ScheduledTime,
                EstimatedTime = alert.EstimatedTime,
                ActualTime = alert.ActualTime,
                DelayReason = alert.DelayReason,
                DelayReasonDescription = alert.DelayReasonDescription,
                EscalationLevel = alert.EscalationLevel,
                LastEscalatedAt = alert.LastEscalatedAt,
                AcknowledgedAt = alert.AcknowledgedAt,
                AcknowledgedByUserId = alert.AcknowledgedByUserId,
                AcknowledgmentNotes = alert.AcknowledgmentNotes,
                ResolvedAt = alert.ResolvedAt,
                ResolutionNotes = alert.ResolutionNotes,
                CreatedAt = alert.CreatedAt,
                IsExpired = alert.IsExpired(),
                NotificationCount = alert.Notifications.Count,
                ImpactAssessment = alert.ImpactAssessment != null ? new DelayImpactAssessmentDto
                {
                    FinancialImpact = alert.ImpactAssessment.FinancialImpact,
                    CustomerImpact = alert.ImpactAssessment.CustomerImpact,
                    OperationalImpact = alert.ImpactAssessment.OperationalImpact,
                    AffectedShipments = alert.ImpactAssessment.AffectedShipments,
                    AffectedCustomers = alert.ImpactAssessment.AffectedCustomers,
                    MitigationActions = alert.ImpactAssessment.MitigationActions,
                    AssessedAt = alert.ImpactAssessment.AssessedAt,
                    AssessedByUserId = alert.ImpactAssessment.AssessedByUserId
                } : null
            };

            return Ok(alertDetails);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting delay alert {DelayAlertId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Acknowledge a delay alert
    /// </summary>
    [HttpPost("{id}/acknowledge")]
    [ProducesResponseType(typeof(AcknowledgeDelayAlertResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<AcknowledgeDelayAlertResult>> AcknowledgeDelayAlert(
        Guid id,
        [FromBody] AcknowledgeDelayAlertCommand command)
    {
        try
        {
            var alert = await _delayAlertRepository.GetByIdAsync(id, HttpContext.RequestAborted);
            if (alert == null)
            {
                return NotFound("Delay alert not found");
            }

            if (!await CanManageDelayAlerts(alert.TransportCompanyId))
            {
                return Forbid("You don't have permission to acknowledge this delay alert");
            }

            command.DelayAlertId = id;
            command.AcknowledgedByUserId = GetCurrentUserId();

            var result = await _mediator.Send(command);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Delay alert {DelayAlertId} acknowledged by user {UserId}",
                    id, command.AcknowledgedByUserId);
                return Ok(result);
            }

            _logger.LogWarning("Failed to acknowledge delay alert {DelayAlertId}: {Error}",
                id, result.ErrorMessage);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error acknowledging delay alert {DelayAlertId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Escalate a delay alert
    /// </summary>
    [HttpPost("{id}/escalate")]
    [ProducesResponseType(typeof(EscalateDelayAlertResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<EscalateDelayAlertResult>> EscalateDelayAlert(
        Guid id,
        [FromBody] EscalateDelayAlertCommand command)
    {
        try
        {
            var alert = await _delayAlertRepository.GetByIdAsync(id, HttpContext.RequestAborted);
            if (alert == null)
            {
                return NotFound("Delay alert not found");
            }

            if (!await CanManageDelayAlerts(alert.TransportCompanyId))
            {
                return Forbid("You don't have permission to escalate this delay alert");
            }

            command.DelayAlertId = id;
            command.EscalatedByUserId = GetCurrentUserId();

            var result = await _mediator.Send(command);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Delay alert {DelayAlertId} escalated to level {EscalationLevel} by user {UserId}",
                    id, result.NewEscalationLevel, command.EscalatedByUserId);
                return Ok(result);
            }

            _logger.LogWarning("Failed to escalate delay alert {DelayAlertId}: {Error}",
                id, result.ErrorMessage);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error escalating delay alert {DelayAlertId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Resolve a delay alert
    /// </summary>
    [HttpPost("{id}/resolve")]
    [ProducesResponseType(typeof(ResolveDelayAlertResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<ResolveDelayAlertResult>> ResolveDelayAlert(
        Guid id,
        [FromBody] ResolveDelayAlertCommand command)
    {
        try
        {
            var alert = await _delayAlertRepository.GetByIdAsync(id, HttpContext.RequestAborted);
            if (alert == null)
            {
                return NotFound("Delay alert not found");
            }

            if (!await CanManageDelayAlerts(alert.TransportCompanyId))
            {
                return Forbid("You don't have permission to resolve this delay alert");
            }

            command.DelayAlertId = id;
            command.ResolvedByUserId = GetCurrentUserId();

            var result = await _mediator.Send(command);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Delay alert {DelayAlertId} resolved by user {UserId}",
                    id, command.ResolvedByUserId);
                return Ok(result);
            }

            _logger.LogWarning("Failed to resolve delay alert {DelayAlertId}: {Error}",
                id, result.ErrorMessage);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resolving delay alert {DelayAlertId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get delay analytics for a transport company
    /// </summary>
    [HttpGet("company/{transportCompanyId}/analytics")]
    [ProducesResponseType(typeof(DelayAlertAnalyticsDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<DelayAlertAnalyticsDto>> GetDelayAnalytics(
        Guid transportCompanyId,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        try
        {
            if (!await CanViewDelayAlerts(transportCompanyId))
            {
                return Forbid("You don't have permission to view delay analytics for this transport company");
            }

            var from = fromDate ?? DateTime.UtcNow.AddDays(-30);
            var to = toDate ?? DateTime.UtcNow;

            var analytics = await _delayAlertService.GetDelayAnalyticsAsync(
                transportCompanyId, from, to, HttpContext.RequestAborted);

            var analyticsDto = new DelayAlertAnalyticsDto
            {
                TransportCompanyId = transportCompanyId,
                FromDate = from,
                ToDate = to,
                GeneratedAt = DateTime.UtcNow,
                TotalAlerts = analytics.TotalAlerts,
                ActiveAlerts = analytics.ActiveAlerts,
                AcknowledgedAlerts = analytics.AcknowledgedAlerts,
                ResolvedAlerts = analytics.ResolvedAlerts,
                CancelledAlerts = analytics.CancelledAlerts,
                AverageDelayDuration = analytics.AverageDelayDuration,
                AverageResolutionTime = analytics.AverageResolutionTime,
                AlertsBySeverity = analytics.AlertsBySeverity.ToDictionary(
                    kvp => kvp.Key.ToString(), kvp => kvp.Value),
                AlertsByReason = analytics.AlertsByReason.ToDictionary(
                    kvp => kvp.Key.ToString(), kvp => kvp.Value),
                AlertsByHour = analytics.AlertsByHour,
                FinancialImpactByDay = analytics.FinancialImpactByDay
            };

            return Ok(analyticsDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting delay analytics for transport company {TransportCompanyId}",
                transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    private async Task<bool> CanManageDelayAlerts(Guid transportCompanyId)
    {
        var currentUserId = GetCurrentUserId();
        var userRoles = GetCurrentUserRoles();

        return userRoles.Contains("Admin") || 
               (userRoles.Contains("TransportCompany") && await IsUserFromTransportCompany(currentUserId, transportCompanyId));
    }

    private async Task<bool> CanViewDelayAlerts(Guid transportCompanyId)
    {
        return await CanManageDelayAlerts(transportCompanyId);
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst("sub") ?? User.FindFirst("userId");
        return userIdClaim != null && Guid.TryParse(userIdClaim.Value, out var userId) ? userId : Guid.Empty;
    }

    private List<string> GetCurrentUserRoles()
    {
        return User.FindAll("role").Select(c => c.Value).ToList();
    }

    private async Task<bool> IsUserFromTransportCompany(Guid userId, Guid transportCompanyId)
    {
        // This would typically check the user's company association
        return await Task.FromResult(true);
    }
}
