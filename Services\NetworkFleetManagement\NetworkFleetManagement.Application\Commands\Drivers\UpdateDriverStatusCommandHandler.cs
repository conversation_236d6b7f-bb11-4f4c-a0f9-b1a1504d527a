using AutoMapper;
using MediatR;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Repositories;
using Shared.Messaging;

namespace NetworkFleetManagement.Application.Commands.Drivers;

public class UpdateDriverStatusCommandHandler : IRequestHandler<UpdateDriverStatusCommand, DriverDto>
{
    private readonly IDriverRepository _driverRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly IMessageBroker _messageBroker;

    public UpdateDriverStatusCommandHandler(
        IDriverRepository driverRepository,
        IUnitOfWork unitOfWork,
        IMapper mapper,
        IMessageBroker messageBroker)
    {
        _driverRepository = driverRepository;
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _messageBroker = messageBroker;
    }

    public async Task<DriverDto> Handle(UpdateDriverStatusCommand request, CancellationToken cancellationToken)
    {
        var driver = await _driverRepository.GetByIdAsync(request.DriverId, cancellationToken);
        if (driver == null)
        {
            throw new InvalidOperationException($"Driver with ID {request.DriverId} not found");
        }

        var oldStatus = driver.Status;
        driver.UpdateStatus(request.StatusDto.Status);

        _driverRepository.Update(driver);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        // Publish integration event
        await _messageBroker.PublishAsync("driver.status_changed", new
        {
            DriverId = driver.Id,
            CarrierId = driver.CarrierId,
            UserId = driver.UserId,
            FirstName = driver.FirstName,
            LastName = driver.LastName,
            LicenseNumber = driver.LicenseNumber,
            OldStatus = oldStatus.ToString(),
            NewStatus = driver.Status.ToString(),
            UpdatedAt = driver.UpdatedAt
        }, cancellationToken);

        var result = _mapper.Map<DriverDto>(driver);
        return result;
    }
}
