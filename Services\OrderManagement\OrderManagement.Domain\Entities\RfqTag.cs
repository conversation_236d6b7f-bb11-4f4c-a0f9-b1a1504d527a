using Shared.Domain.Common;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Domain.Entities;

/// <summary>
/// Entity to represent tags applied to RFQs for categorization and filtering
/// </summary>
public class RfqTag : BaseEntity
{
    public Guid RfqId { get; private set; }
    public RfqTagType TagType { get; private set; }
    public string TagName { get; private set; } = string.Empty;
    public string? TagDescription { get; private set; }
    public string? TagColor { get; private set; }
    public DateTime AppliedAt { get; private set; }
    public Guid? AppliedBy { get; private set; }
    public string? AppliedByName { get; private set; }
    public bool IsSystemGenerated { get; private set; }
    public string? AutoTagReason { get; private set; }
    public Dictionary<string, object>? TagMetadata { get; private set; }
    public bool IsActive { get; private set; }
    public DateTime? RemovedAt { get; private set; }
    public Guid? RemovedBy { get; private set; }
    public string? RemovalReason { get; private set; }

    // Navigation property
    public RFQ RFQ { get; private set; } = null!;

    private RfqTag() { } // EF Constructor

    public RfqTag(
        Guid rfqId,
        RfqTagType tagType,
        string tagName,
        string? tagDescription = null,
        string? tagColor = null,
        Guid? appliedBy = null,
        string? appliedByName = null,
        bool isSystemGenerated = false,
        string? autoTagReason = null,
        Dictionary<string, object>? tagMetadata = null)
    {
        RfqId = rfqId;
        TagType = tagType;
        TagName = tagName ?? throw new ArgumentNullException(nameof(tagName));
        TagDescription = tagDescription;
        TagColor = tagColor;
        AppliedAt = DateTime.UtcNow;
        AppliedBy = appliedBy;
        AppliedByName = appliedByName;
        IsSystemGenerated = isSystemGenerated;
        AutoTagReason = autoTagReason;
        TagMetadata = tagMetadata;
        IsActive = true;
    }

    public void Remove(Guid? removedBy = null, string? removalReason = null)
    {
        IsActive = false;
        RemovedAt = DateTime.UtcNow;
        RemovedBy = removedBy;
        RemovalReason = removalReason;
    }

    public void UpdateMetadata(Dictionary<string, object> metadata)
    {
        TagMetadata = metadata;
    }

    // Factory methods for common system-generated tags
    public static RfqTag CreateRoutingFailureTag(
        Guid rfqId,
        string reason,
        Dictionary<string, object>? metadata = null)
    {
        return new RfqTag(
            rfqId,
            RfqTagType.RoutingFailure,
            "Routing Failure",
            $"RFQ failed to route successfully: {reason}",
            "#FF6B6B", // Red color
            null,
            "System",
            true,
            reason,
            metadata);
    }

    public static RfqTag CreateNoResponseTag(
        Guid rfqId,
        int hoursWithoutResponse,
        Dictionary<string, object>? metadata = null)
    {
        return new RfqTag(
            rfqId,
            RfqTagType.NoResponse,
            "No Response",
            $"No response received for {hoursWithoutResponse} hours",
            "#FFA500", // Orange color
            null,
            "System",
            true,
            $"No response for {hoursWithoutResponse} hours",
            metadata);
    }

    public static RfqTag CreateLowBidCountTag(
        Guid rfqId,
        int bidCount,
        int expectedMinimum,
        Dictionary<string, object>? metadata = null)
    {
        return new RfqTag(
            rfqId,
            RfqTagType.LowBidCount,
            "Low Bid Count",
            $"Only {bidCount} bids received (expected minimum: {expectedMinimum})",
            "#FFD700", // Gold color
            null,
            "System",
            true,
            $"Low bid count: {bidCount}/{expectedMinimum}",
            metadata);
    }

    public static RfqTag CreateHighValueTag(
        Guid rfqId,
        decimal value,
        string currency,
        Dictionary<string, object>? metadata = null)
    {
        return new RfqTag(
            rfqId,
            RfqTagType.HighValue,
            "High Value",
            $"High value load: {value:C} {currency}",
            "#4CAF50", // Green color
            null,
            "System",
            true,
            $"High value: {value} {currency}",
            metadata);
    }

    public static RfqTag CreateUrgentLoadTag(
        Guid rfqId,
        Dictionary<string, object>? metadata = null)
    {
        return new RfqTag(
            rfqId,
            RfqTagType.UrgentLoad,
            "Urgent Load",
            "Load marked as urgent requiring immediate attention",
            "#FF4444", // Bright red color
            null,
            "System",
            true,
            "Urgent load detected",
            metadata);
    }

    public static RfqTag CreateSpecialHandlingTag(
        Guid rfqId,
        string handlingRequirements,
        Dictionary<string, object>? metadata = null)
    {
        return new RfqTag(
            rfqId,
            RfqTagType.SpecialHandling,
            "Special Handling",
            $"Requires special handling: {handlingRequirements}",
            "#9C27B0", // Purple color
            null,
            "System",
            true,
            $"Special handling: {handlingRequirements}",
            metadata);
    }

    public static RfqTag CreateGeographicChallengeTag(
        Guid rfqId,
        string challenge,
        Dictionary<string, object>? metadata = null)
    {
        return new RfqTag(
            rfqId,
            RfqTagType.GeographicChallenge,
            "Geographic Challenge",
            $"Geographic routing challenge: {challenge}",
            "#795548", // Brown color
            null,
            "System",
            true,
            $"Geographic challenge: {challenge}",
            metadata);
    }

    public static RfqTag CreateCustomTag(
        Guid rfqId,
        string tagName,
        string? description = null,
        string? color = null,
        Guid? appliedBy = null,
        string? appliedByName = null,
        Dictionary<string, object>? metadata = null)
    {
        return new RfqTag(
            rfqId,
            RfqTagType.Custom,
            tagName,
            description,
            color ?? "#607D8B", // Blue grey default
            appliedBy,
            appliedByName,
            false,
            null,
            metadata);
    }
}
