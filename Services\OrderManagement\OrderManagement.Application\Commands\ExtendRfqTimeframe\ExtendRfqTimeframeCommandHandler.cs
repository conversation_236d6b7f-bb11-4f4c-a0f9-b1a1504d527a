using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Exceptions;

namespace OrderManagement.Application.Commands.ExtendRfqTimeframe;

public class ExtendRfqTimeframeCommandHandler : IRequestHandler<ExtendRfqTimeframeCommand, bool>
{
    private readonly IRfqRepository _rfqRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<ExtendRfqTimeframeCommandHandler> _logger;

    public ExtendRfqTimeframeCommandHandler(
        IRfqRepository rfqRepository,
        IUnitOfWork unitOfWork,
        IMessageBroker messageBroker,
        ILogger<ExtendRfqTimeframeCommandHandler> logger)
    {
        _rfqRepository = rfqRepository;
        _unitOfWork = unitOfWork;
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task<bool> Handle(ExtendRfqTimeframeCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Extending RFQ timeframe for RFQ {RfqId} by transport company {TransportCompanyId}",
            request.RfqId, request.TransportCompanyId);

        var rfq = await _rfqRepository.GetByIdAsync(request.RfqId, cancellationToken);
        if (rfq == null)
        {
            _logger.LogWarning("RFQ {RfqId} not found", request.RfqId);
            throw new OrderManagementException("RFQ not found");
        }

        // Authorization check
        if (rfq.TransportCompanyId != request.TransportCompanyId)
        {
            _logger.LogWarning("Transport company {TransportCompanyId} is not authorized to extend RFQ {RfqId}",
                request.TransportCompanyId, request.RfqId);
            throw new OrderManagementException("You are not authorized to extend this RFQ timeframe");
        }

        try
        {
            // Extend the timeframe
            rfq.ExtendTimeframe(request.Duration, request.Unit, request.Reason, request.TransportCompanyId);

            // Update in repository
            _rfqRepository.Update(rfq);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Publish integration event
            await _messageBroker.PublishAsync("rfq.timeframe.extended", new
            {
                RfqId = rfq.Id,
                RfqNumber = rfq.RfqNumber,
                TransportCompanyId = rfq.TransportCompanyId,
                ExtensionDuration = request.Duration,
                ExtensionUnit = request.Unit.ToString(),
                Reason = request.Reason,
                NewExpiresAt = rfq.ExpiresAt,
                ExtendedAt = DateTime.UtcNow,
                ExtendedBy = request.TransportCompanyId
            }, cancellationToken);

            _logger.LogInformation("Successfully extended RFQ {RfqId} timeframe by {Duration} {Unit}",
                request.RfqId, request.Duration, request.Unit);

            return true;
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning("Failed to extend RFQ {RfqId} timeframe: {Error}", request.RfqId, ex.Message);
            throw new OrderManagementException(ex.Message);
        }
    }
}
