using System.Net.Http.Json;
using System.Text.Json;
using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Xunit.Abstractions;

namespace Tests.Shared.Utilities;

/// <summary>
/// Utility methods for testing
/// </summary>
public static class TestUtilities
{
    private static readonly JsonSerializerOptions JsonOptions = new()
    {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        WriteIndented = true
    };

    /// <summary>
    /// Serialize object to JSON string
    /// </summary>
    public static string ToJson<T>(T obj) => JsonSerializer.Serialize(obj, JsonOptions);

    /// <summary>
    /// Deserialize JSON string to object
    /// </summary>
    public static T? FromJson<T>(string json) => JsonSerializer.Deserialize<T>(json, JsonOptions);

    /// <summary>
    /// Create HTTP content from object
    /// </summary>
    public static JsonContent CreateJsonContent<T>(T obj) => JsonContent.Create(obj, options: JsonOptions);

    /// <summary>
    /// Assert that HTTP response is successful
    /// </summary>
    public static async Task<T> ShouldBeSuccessfulWith<T>(this HttpResponseMessage response)
    {
        response.IsSuccessStatusCode.Should().BeTrue($"Expected successful response but got {response.StatusCode}");
        var content = await response.Content.ReadAsStringAsync();
        var result = FromJson<T>(content);
        result.Should().NotBeNull();
        return result!;
    }

    /// <summary>
    /// Assert that HTTP response has specific status code
    /// </summary>
    public static HttpResponseMessage ShouldHaveStatusCode(this HttpResponseMessage response, System.Net.HttpStatusCode expectedStatusCode)
    {
        response.StatusCode.Should().Be(expectedStatusCode);
        return response;
    }

    /// <summary>
    /// Wait for a condition to be true with timeout
    /// </summary>
    public static async Task WaitForConditionAsync(Func<Task<bool>> condition, TimeSpan timeout, TimeSpan? interval = null)
    {
        interval ??= TimeSpan.FromMilliseconds(100);
        var endTime = DateTime.UtcNow.Add(timeout);

        while (DateTime.UtcNow < endTime)
        {
            if (await condition())
                return;

            await Task.Delay(interval.Value);
        }

        throw new TimeoutException($"Condition was not met within {timeout}");
    }

    /// <summary>
    /// Wait for a condition to be true with timeout (synchronous version)
    /// </summary>
    public static async Task WaitForConditionAsync(Func<bool> condition, TimeSpan timeout, TimeSpan? interval = null)
    {
        await WaitForConditionAsync(() => Task.FromResult(condition()), timeout, interval);
    }

    /// <summary>
    /// Generate a random string
    /// </summary>
    public static string RandomString(int length = 10)
    {
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        var random = new Random();
        return new string(Enumerable.Repeat(chars, length)
            .Select(s => s[random.Next(s.Length)]).ToArray());
    }

    /// <summary>
    /// Generate a random email
    /// </summary>
    public static string RandomEmail() => $"{RandomString(8)}@test.com";

    /// <summary>
    /// Generate a random phone number
    /// </summary>
    public static string RandomPhoneNumber() => $"+1{Random.Shared.Next(**********, int.MaxValue)}";

    /// <summary>
    /// Create a test logger that outputs to xUnit test output
    /// </summary>
    public static ILogger<T> CreateTestLogger<T>(ITestOutputHelper output)
    {
        var serviceCollection = new ServiceCollection();
        serviceCollection.AddLogging(builder => builder.AddXUnit(output));
        var serviceProvider = serviceCollection.BuildServiceProvider();
        return serviceProvider.GetRequiredService<ILogger<T>>();
    }
}

/// <summary>
/// Extensions for xUnit test output logging
/// </summary>
public static class LoggingExtensions
{
    public static ILoggingBuilder AddXUnit(this ILoggingBuilder builder, ITestOutputHelper output)
    {
        builder.AddProvider(new XUnitLoggerProvider(output));
        return builder;
    }
}

/// <summary>
/// xUnit logger provider
/// </summary>
public class XUnitLoggerProvider : ILoggerProvider
{
    private readonly ITestOutputHelper _output;

    public XUnitLoggerProvider(ITestOutputHelper output)
    {
        _output = output;
    }

    public ILogger CreateLogger(string categoryName) => new XUnitLogger(_output, categoryName);

    public void Dispose() { }
}

/// <summary>
/// xUnit logger implementation
/// </summary>
public class XUnitLogger : ILogger
{
    private readonly ITestOutputHelper _output;
    private readonly string _categoryName;

    public XUnitLogger(ITestOutputHelper output, string categoryName)
    {
        _output = output;
        _categoryName = categoryName;
    }

    public IDisposable BeginScope<TState>(TState state) => new NoOpDisposable();

    public bool IsEnabled(LogLevel logLevel) => true;

    public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception? exception, Func<TState, Exception?, string> formatter)
    {
        var message = formatter(state, exception);
        _output.WriteLine($"[{logLevel}] {_categoryName}: {message}");
        
        if (exception != null)
        {
            _output.WriteLine($"Exception: {exception}");
        }
    }

    private class NoOpDisposable : IDisposable
    {
        public void Dispose() { }
    }
}
