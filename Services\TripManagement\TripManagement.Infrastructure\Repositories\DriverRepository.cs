using Microsoft.EntityFrameworkCore;
using TripManagement.Application.Interfaces;
using TripManagement.Domain.Entities;
using TripManagement.Domain.Enums;
using TripManagement.Infrastructure.Persistence;

namespace TripManagement.Infrastructure.Repositories;

public class DriverRepository : IDriverRepository
{
    private readonly TripManagementDbContext _context;

    public DriverRepository(TripManagementDbContext context)
    {
        _context = context;
    }

    public async Task<Driver?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.Drivers
            .Include(d => d.Documents)
            .FirstOrDefaultAsync(d => d.Id == id, cancellationToken);
    }

    public async Task<Driver?> GetByUserIdAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await _context.Drivers
            .Include(d => d.Documents)
            .FirstOrDefaultAsync(d => d.UserId == userId, cancellationToken);
    }

    public async Task<List<Driver>> GetAvailableDriversAsync(CancellationToken cancellationToken = default)
    {
        return await _context.Drivers
            .Where(d => d.Status == DriverStatus.Available && d.LicenseExpiryDate > DateTime.UtcNow)
            .OrderBy(d => d.FirstName)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<Driver>> GetByCarrierIdAsync(Guid carrierId, CancellationToken cancellationToken = default)
    {
        // Note: This assumes drivers are associated with carriers through a relationship
        // You may need to adjust this based on your actual domain model
        return await _context.Drivers
            .Where(d => d.Trips.Any(t => t.CarrierId == carrierId))
            .Distinct()
            .OrderBy(d => d.FirstName)
            .ToListAsync(cancellationToken);
    }

    public async Task AddAsync(Driver driver, CancellationToken cancellationToken = default)
    {
        await _context.Drivers.AddAsync(driver, cancellationToken);
    }

    public void Update(Driver driver)
    {
        _context.Drivers.Update(driver);
    }

    public void Delete(Driver driver)
    {
        _context.Drivers.Remove(driver);
    }
}
