using Microsoft.Extensions.Logging;
using Shared.Domain.Common;
using System.Security.Cryptography;
using System.Text;
using TLI.Shared.Infrastructure.Messaging;
using TripManagement.Domain.Entities;
using TripManagement.Domain.Repositories;

namespace TripManagement.Application.Commands.UploadPODWithPreview;

public class UploadPODWithPreviewCommandHandler : IRequestHandler<UploadPODWithPreviewCommand, UploadPODWithPreviewResponse>
{
    private readonly ITripRepository _tripRepository;
    private readonly IPODRepository _podRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<UploadPODWithPreviewCommandHandler> _logger;

    public UploadPODWithPreviewCommandHandler(
        ITripRepository tripRepository,
        IPODRepository podRepository,
        IUnitOfWork unitOfWork,
        IMessageBroker messageBroker,
        ILogger<UploadPODWithPreviewCommandHandler> logger)
    {
        _tripRepository = tripRepository;
        _podRepository = podRepository;
        _unitOfWork = unitOfWork;
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task<UploadPODWithPreviewResponse> Handle(UploadPODWithPreviewCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Processing POD upload for trip {TripId}, preview mode: {IsPreview}", request.TripId, request.IsPreviewMode);

            // Validate trip and driver
            var trip = await _tripRepository.GetByIdAsync(request.TripId, cancellationToken);
            if (trip == null)
            {
                return new UploadPODWithPreviewResponse
                {
                    IsSuccess = false,
                    ErrorMessage = "Trip not found"
                };
            }

            if (trip.DriverId != request.DriverId)
            {
                return new UploadPODWithPreviewResponse
                {
                    IsSuccess = false,
                    ErrorMessage = "Driver not assigned to this trip"
                };
            }

            // Validate POD data
            var validationIssues = await ValidatePODData(request.PODData, trip, cancellationToken);

            if (request.IsPreviewMode)
            {
                // Generate preview
                var previewData = await GeneratePreviewData(request.PODData, trip, cancellationToken);
                var confirmationToken = GenerateConfirmationToken(request.TripId, request.DriverId);

                return new UploadPODWithPreviewResponse
                {
                    IsSuccess = true,
                    PreviewData = previewData,
                    ConfirmationToken = confirmationToken,
                    ValidationIssues = validationIssues
                };
            }
            else
            {
                // Validate confirmation token for final submission
                if (request.RequireConfirmation && !ValidateConfirmationToken(request.ConfirmationToken, request.TripId, request.DriverId))
                {
                    return new UploadPODWithPreviewResponse
                    {
                        IsSuccess = false,
                        ErrorMessage = "Invalid confirmation token"
                    };
                }

                // Check for blocking validation issues
                var blockingIssues = validationIssues.Where(v => v.IsBlocking).ToList();
                if (blockingIssues.Any())
                {
                    return new UploadPODWithPreviewResponse
                    {
                        IsSuccess = false,
                        ErrorMessage = "Cannot submit POD due to validation errors",
                        ValidationIssues = validationIssues
                    };
                }

                // Process final submission
                var result = await ProcessPODSubmission(request.PODData, trip, cancellationToken);

                return new UploadPODWithPreviewResponse
                {
                    IsSuccess = true,
                    Result = result,
                    ValidationIssues = validationIssues
                };
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing POD upload for trip {TripId}", request.TripId);
            return new UploadPODWithPreviewResponse
            {
                IsSuccess = false,
                ErrorMessage = ex.Message
            };
        }
    }

    private async Task<List<ValidationIssue>> ValidatePODData(PODUploadData podData, Trip trip, CancellationToken cancellationToken)
    {
        var issues = new List<ValidationIssue>();

        // Validate required documents
        if (!podData.Documents.Any())
        {
            issues.Add(new ValidationIssue
            {
                Code = "NO_DOCUMENTS",
                Message = "At least one document is required",
                Severity = "Error",
                IsBlocking = true,
                SuggestedAction = "Upload at least one photo or document"
            });
        }

        // Validate signature if required
        if (podData.RequireRecipientVerification && podData.Signature == null)
        {
            issues.Add(new ValidationIssue
            {
                Code = "SIGNATURE_REQUIRED",
                Message = "Recipient signature is required",
                Severity = "Error",
                Field = "Signature",
                IsBlocking = true,
                SuggestedAction = "Obtain recipient signature"
            });
        }

        // Validate recipient information
        if (string.IsNullOrWhiteSpace(podData.RecipientName))
        {
            issues.Add(new ValidationIssue
            {
                Code = "RECIPIENT_NAME_REQUIRED",
                Message = "Recipient name is required",
                Severity = "Error",
                Field = "RecipientName",
                IsBlocking = true,
                SuggestedAction = "Enter recipient name"
            });
        }

        // Validate delivery location
        if (!podData.DeliveryLatitude.HasValue || !podData.DeliveryLongitude.HasValue)
        {
            issues.Add(new ValidationIssue
            {
                Code = "LOCATION_REQUIRED",
                Message = "Delivery location is required",
                Severity = "Warning",
                IsBlocking = false,
                SuggestedAction = "Enable location services"
            });
        }

        // Validate file sizes and types
        foreach (var doc in podData.Documents)
        {
            if (doc.FileSize > 10 * 1024 * 1024) // 10MB limit
            {
                issues.Add(new ValidationIssue
                {
                    Code = "FILE_TOO_LARGE",
                    Message = $"File {doc.FileName} exceeds 10MB limit",
                    Severity = "Error",
                    IsBlocking = true,
                    SuggestedAction = "Compress or resize the file"
                });
            }

            if (!IsValidFileType(doc.ContentType))
            {
                issues.Add(new ValidationIssue
                {
                    Code = "INVALID_FILE_TYPE",
                    Message = $"File type {doc.ContentType} is not supported",
                    Severity = "Error",
                    IsBlocking = true,
                    SuggestedAction = "Use supported file types (JPG, PNG, PDF)"
                });
            }
        }

        return issues;
    }

    private async Task<PODPreviewData> GeneratePreviewData(PODUploadData podData, Trip trip, CancellationToken cancellationToken)
    {
        var previewData = new PODPreviewData
        {
            DocumentPreviews = await GenerateDocumentPreviews(podData.Documents),
            SignaturePreview = GenerateSignaturePreview(podData.Signature),
            DeliveryInfo = new DeliveryInfoPreview
            {
                RecipientName = podData.RecipientName ?? string.Empty,
                RecipientTitle = podData.RecipientTitle ?? string.Empty,
                DeliveryDateTime = podData.DeliveryDateTime,
                DeliveryAddress = podData.DeliveryAddress ?? string.Empty,
                DeliveryConditions = podData.DeliveryConditions,
                DeliveryNotes = podData.DeliveryNotes,
                LocationVerified = podData.DeliveryLatitude.HasValue && podData.DeliveryLongitude.HasValue,
                RecipientVerified = !string.IsNullOrWhiteSpace(podData.RecipientName)
            }
        };

        // Determine if ready for submission
        previewData.IsReadyForSubmission = podData.Documents.Any() &&
                                         !string.IsNullOrWhiteSpace(podData.RecipientName) &&
                                         (podData.Signature != null || !podData.RequireRecipientVerification);

        // Add required actions
        if (!previewData.IsReadyForSubmission)
        {
            if (!podData.Documents.Any())
                previewData.RequiredActions.Add("Upload at least one document");

            if (string.IsNullOrWhiteSpace(podData.RecipientName))
                previewData.RequiredActions.Add("Enter recipient name");

            if (podData.RequireRecipientVerification && podData.Signature == null)
                previewData.RequiredActions.Add("Obtain recipient signature");
        }

        return previewData;
    }

    private async Task<List<DocumentPreview>> GenerateDocumentPreviews(List<PODDocument> documents)
    {
        var previews = new List<DocumentPreview>();

        foreach (var doc in documents)
        {
            var preview = new DocumentPreview
            {
                DocumentType = doc.DocumentType,
                FileName = doc.FileName,
                FileSize = doc.FileSize,
                IsValid = IsValidDocument(doc),
                PreviewUrl = $"/api/pod/preview/{Guid.NewGuid()}", // Mock URL
                ThumbnailUrl = $"/api/pod/thumbnail/{Guid.NewGuid()}" // Mock URL
            };

            if (!preview.IsValid)
            {
                preview.ValidationMessages.Add("Document validation failed");
            }

            previews.Add(preview);
        }

        return previews;
    }

    private SignaturePreview? GenerateSignaturePreview(PODSignature? signature)
    {
        if (signature == null) return null;

        return new SignaturePreview
        {
            PreviewUrl = $"/api/pod/signature-preview/{Guid.NewGuid()}", // Mock URL
            SignerName = signature.SignerName ?? string.Empty,
            SignedAt = signature.SignedAt,
            IsValid = signature.SignatureData.Length > 0
        };
    }

    private async Task<PODUploadResult> ProcessPODSubmission(PODUploadData podData, Trip trip, CancellationToken cancellationToken)
    {
        var podId = Guid.NewGuid();
        var podNumber = $"POD-{trip.TripNumber}-{DateTime.UtcNow:yyyyMMdd}";

        // Create POD entity
        var pod = new POD(
            podId,
            trip.Id,
            trip.DriverId,
            podData.RecipientName ?? string.Empty,
            podData.DeliveryDateTime,
            podData.DeliveryLatitude,
            podData.DeliveryLongitude);

        // Add documents and signature
        foreach (var doc in podData.Documents)
        {
            pod.AddDocument(doc.DocumentType, doc.FileName, doc.Content, doc.ContentType);
        }

        if (podData.Signature != null)
        {
            pod.AddSignature(podData.Signature.SignatureData, podData.Signature.SignerName);
        }

        // Save POD
        await _podRepository.AddAsync(pod, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        // Update trip status
        trip.CompletePOD(podId);
        _tripRepository.Update(trip);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        // Publish integration events
        await _messageBroker.PublishAsync("pod.uploaded", new
        {
            PODId = podId,
            TripId = trip.Id,
            DriverId = trip.DriverId,
            PODNumber = podNumber,
            UploadedAt = DateTime.UtcNow,
            DocumentCount = podData.Documents.Count,
            HasSignature = podData.Signature != null
        }, cancellationToken);

        return new PODUploadResult
        {
            PODId = podId,
            PODNumber = podNumber,
            Status = PODStatus.Submitted,
            UploadedAt = DateTime.UtcNow,
            UploadedDocuments = podData.Documents.Select(d => d.FileName).ToList(),
            ProcessingInfo = new PODProcessingInfo
            {
                ProcessingStatus = "Completed",
                ProcessingProgress = 100.0,
                CompletedAt = DateTime.UtcNow,
                ProcessingSteps = new List<ProcessingStep>
                {
                    new ProcessingStep { StepName = "Upload", Status = "Completed", CompletedAt = DateTime.UtcNow },
                    new ProcessingStep { StepName = "Validation", Status = "Completed", CompletedAt = DateTime.UtcNow },
                    new ProcessingStep { StepName = "Storage", Status = "Completed", CompletedAt = DateTime.UtcNow }
                }
            }
        };
    }

    private string GenerateConfirmationToken(Guid tripId, Guid driverId)
    {
        var data = $"{tripId}:{driverId}:{DateTime.UtcNow:yyyyMMddHHmm}";
        using var sha256 = SHA256.Create();
        var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(data));
        return Convert.ToBase64String(hash)[..16]; // Take first 16 characters
    }

    private bool ValidateConfirmationToken(string? token, Guid tripId, Guid driverId)
    {
        if (string.IsNullOrWhiteSpace(token)) return false;

        // In real implementation, would validate against stored token or regenerate and compare
        var expectedToken = GenerateConfirmationToken(tripId, driverId);
        return token == expectedToken;
    }

    private bool IsValidFileType(string contentType)
    {
        var allowedTypes = new[] { "image/jpeg", "image/png", "image/gif", "application/pdf" };
        return allowedTypes.Contains(contentType.ToLower());
    }

    private bool IsValidDocument(PODDocument document)
    {
        return document.Content.Length > 0 &&
               !string.IsNullOrWhiteSpace(document.FileName) &&
               IsValidFileType(document.ContentType);
    }
}

