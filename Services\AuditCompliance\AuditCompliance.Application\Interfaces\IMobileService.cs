using AuditCompliance.Application.DTOs.Mobile;

namespace AuditCompliance.Application.Interfaces;

/// <summary>
/// Service for mobile-specific functionality
/// </summary>
public interface IMobileService
{
    /// <summary>
    /// Submit rating from mobile app
    /// </summary>
    Task<MobileRatingResponseDto> SubmitRatingAsync(
        MobileRatingSubmissionDto rating,
        string userId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Report issue from mobile app
    /// </summary>
    Task<MobileIssueReportResponseDto> ReportIssueAsync(
        MobileIssueReportDto issue,
        string userId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get compliance status for mobile display
    /// </summary>
    Task<MobileComplianceStatusDto> GetComplianceStatusAsync(
        Guid entityId,
        string entityType,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get mobile dashboard summary
    /// </summary>
    Task<MobileDashboardSummaryDto> GetDashboardSummaryAsync(
        string userId,
        Guid? organizationId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get user profile for mobile app
    /// </summary>
    Task<MobileUserProfileDto> GetUserProfileAsync(
        string userId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Update user notification settings
    /// </summary>
    Task<bool> UpdateNotificationSettingsAsync(
        string userId,
        MobileNotificationSettingsDto settings,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Search content from mobile app
    /// </summary>
    Task<MobileSearchResponseDto> SearchAsync(
        MobileSearchRequestDto request,
        string userId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get compliance alerts for mobile display
    /// </summary>
    Task<List<MobileComplianceAlertDto>> GetComplianceAlertsAsync(
        string userId,
        Guid? organizationId = null,
        int pageSize = 20,
        int pageNumber = 1,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Mark alert as read
    /// </summary>
    Task<bool> MarkAlertAsReadAsync(
        Guid alertId,
        string userId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get service provider ratings for mobile display
    /// </summary>
    Task<List<MobileServiceProviderRatingDto>> GetServiceProviderRatingsAsync(
        Guid serviceProviderId,
        int pageSize = 20,
        int pageNumber = 1,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get user's submitted ratings
    /// </summary>
    Task<List<MobileUserRatingDto>> GetUserRatingsAsync(
        string userId,
        int pageSize = 20,
        int pageNumber = 1,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get user's reported issues
    /// </summary>
    Task<List<MobileUserIssueDto>> GetUserIssuesAsync(
        string userId,
        int pageSize = 20,
        int pageNumber = 1,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get issue details
    /// </summary>
    Task<MobileIssueDetailDto> GetIssueDetailsAsync(
        Guid issueId,
        string userId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Upload attachment for mobile
    /// </summary>
    Task<string> UploadAttachmentAsync(
        MobileAttachmentDto attachment,
        string userId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get quick actions for mobile dashboard
    /// </summary>
    Task<List<MobileQuickActionDto>> GetQuickActionsAsync(
        string userId,
        Guid? organizationId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get compliance trends for mobile charts
    /// </summary>
    Task<MobileComplianceTrendDto> GetComplianceTrendsAsync(
        Guid entityId,
        string entityType,
        int days = 30,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate mobile app version
    /// </summary>
    Task<MobileAppVersionDto> ValidateAppVersionAsync(
        string currentVersion,
        string platform,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Mobile service provider rating DTO
/// </summary>
public class MobileServiceProviderRatingDto
{
    public Guid Id { get; set; }
    public int OverallRating { get; set; }
    public string? Comments { get; set; }
    public DateTime SubmittedAt { get; set; }
    public string SubmittedBy { get; set; } = string.Empty;
    public bool IsVerified { get; set; }
    public List<string> Tags { get; set; } = new();
    public string Status { get; set; } = string.Empty;
}

/// <summary>
/// Mobile user rating DTO
/// </summary>
public class MobileUserRatingDto
{
    public Guid Id { get; set; }
    public Guid ServiceProviderId { get; set; }
    public string ServiceProviderName { get; set; } = string.Empty;
    public int OverallRating { get; set; }
    public string? Comments { get; set; }
    public DateTime SubmittedAt { get; set; }
    public string Status { get; set; } = string.Empty;
    public bool CanEdit { get; set; }
    public bool CanDelete { get; set; }
}

/// <summary>
/// Mobile user issue DTO
/// </summary>
public class MobileUserIssueDto
{
    public Guid Id { get; set; }
    public Guid ServiceProviderId { get; set; }
    public string ServiceProviderName { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string IssueType { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public DateTime ReportedAt { get; set; }
    public string Status { get; set; } = string.Empty;
    public string StatusColor { get; set; } = string.Empty;
    public string? ReferenceNumber { get; set; }
    public DateTime? LastUpdated { get; set; }
}

/// <summary>
/// Mobile issue detail DTO
/// </summary>
public class MobileIssueDetailDto
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string IssueType { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime ReportedAt { get; set; }
    public DateTime? ResolvedAt { get; set; }
    public string ReferenceNumber { get; set; } = string.Empty;
    public Guid ServiceProviderId { get; set; }
    public string ServiceProviderName { get; set; } = string.Empty;
    public MobileLocationDto? Location { get; set; }
    public List<MobileAttachmentDto> Attachments { get; set; } = new();
    public List<MobileIssueUpdateDto> Updates { get; set; } = new();
    public string? AssignedTo { get; set; }
    public DateTime? ExpectedResolutionDate { get; set; }
    public string? ResolutionNotes { get; set; }
}

/// <summary>
/// Mobile issue update DTO
/// </summary>
public class MobileIssueUpdateDto
{
    public DateTime UpdatedAt { get; set; }
    public string UpdatedBy { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string Notes { get; set; } = string.Empty;
    public bool IsInternal { get; set; }
}

/// <summary>
/// Mobile app version DTO
/// </summary>
public class MobileAppVersionDto
{
    public string CurrentVersion { get; set; } = string.Empty;
    public string LatestVersion { get; set; } = string.Empty;
    public bool IsUpdateRequired { get; set; }
    public bool IsUpdateAvailable { get; set; }
    public string? UpdateMessage { get; set; }
    public string? DownloadUrl { get; set; }
    public List<string> NewFeatures { get; set; } = new();
    public List<string> BugFixes { get; set; } = new();
    public DateTime ReleaseDate { get; set; }
}
