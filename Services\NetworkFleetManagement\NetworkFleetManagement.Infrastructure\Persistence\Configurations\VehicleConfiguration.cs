using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetworkFleetManagement.Domain.Entities;

namespace NetworkFleetManagement.Infrastructure.Persistence.Configurations;

public class VehicleConfiguration : IEntityTypeConfiguration<Vehicle>
{
    public void Configure(EntityTypeBuilder<Vehicle> builder)
    {
        builder.ToTable("vehicles");

        builder.HasKey(v => v.Id);

        builder.Property(v => v.Id)
            .ValueGeneratedNever();

        builder.Property(v => v.CarrierId)
            .IsRequired();

        builder.Property(v => v.RegistrationNumber)
            .IsRequired()
            .HasMaxLength(20);

        builder.Property(v => v.VehicleType)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(v => v.Make)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(v => v.Model)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(v => v.Year)
            .IsRequired();

        builder.Property(v => v.Color)
            .IsRequired()
            .HasMaxLength(30);

        builder.Property(v => v.Status)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(v => v.InsuranceNumber)
            .HasMaxLength(50);

        builder.Property(v => v.InsuranceExpiryDate);

        builder.Property(v => v.FitnessNumber)
            .HasMaxLength(50);

        builder.Property(v => v.FitnessExpiryDate);

        builder.Property(v => v.PermitNumber)
            .HasMaxLength(50);

        builder.Property(v => v.PermitExpiryDate);

        builder.Property(v => v.Notes)
            .HasMaxLength(1000);

        builder.Property(v => v.DailyEarnings)
            .HasPrecision(10, 2);

        builder.Property(v => v.MonthlyEarnings)
            .HasPrecision(10, 2);

        builder.Property(v => v.UtilizationHours);

        builder.Property(v => v.LastMaintenanceDate);

        builder.Property(v => v.NextMaintenanceDate);

        builder.Property(v => v.CreatedAt)
            .IsRequired();

        builder.Property(v => v.UpdatedAt);

        // Configure Specifications as owned entity
        builder.OwnsOne(v => v.Specifications, s =>
        {
            s.Property(spec => spec.LoadCapacityKg)
                .HasColumnName("load_capacity_kg")
                .HasPrecision(10, 2)
                .IsRequired();

            s.Property(spec => spec.VolumeCapacityM3)
                .HasColumnName("volume_capacity_m3")
                .HasPrecision(10, 2)
                .IsRequired();

            s.Property(spec => spec.FuelCapacityLiters)
                .HasColumnName("fuel_capacity_liters")
                .HasPrecision(8, 2);

            s.Property(spec => spec.MaxSpeed)
                .HasColumnName("max_speed");

            s.Property(spec => spec.Length)
                .HasColumnName("length")
                .HasPrecision(6, 2);

            s.Property(spec => spec.Width)
                .HasColumnName("width")
                .HasPrecision(6, 2);

            s.Property(spec => spec.Height)
                .HasColumnName("height")
                .HasPrecision(6, 2);

            s.Property(spec => spec.EngineType)
                .HasColumnName("engine_type")
                .HasMaxLength(50);

            s.Property(spec => spec.FuelType)
                .HasColumnName("fuel_type")
                .HasMaxLength(30);
        });

        // Configure CurrentLocation as owned entity
        builder.OwnsOne(v => v.CurrentLocation, cl =>
        {
            cl.Property(l => l.Latitude)
                .HasColumnName("current_latitude")
                .HasPrecision(10, 8);

            cl.Property(l => l.Longitude)
                .HasColumnName("current_longitude")
                .HasPrecision(11, 8);

            cl.Property(l => l.Address)
                .HasColumnName("current_address")
                .HasMaxLength(500);

            cl.Property(l => l.City)
                .HasColumnName("current_city")
                .HasMaxLength(100);

            cl.Property(l => l.State)
                .HasColumnName("current_state")
                .HasMaxLength(100);

            cl.Property(l => l.Country)
                .HasColumnName("current_country")
                .HasMaxLength(100);

            cl.Property(l => l.PostalCode)
                .HasColumnName("current_postal_code")
                .HasMaxLength(20);

            cl.Property(l => l.Timestamp)
                .HasColumnName("current_location_timestamp");
        });

        // Configure relationships
        builder.HasMany(v => v.Documents)
            .WithOne(d => d.Vehicle)
            .HasForeignKey(d => d.VehicleId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(v => v.MaintenanceRecords)
            .WithOne(m => m.Vehicle)
            .HasForeignKey(m => m.VehicleId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(v => v.DriverAssignments)
            .WithOne(da => da.Vehicle)
            .HasForeignKey(da => da.VehicleId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(v => v.RegistrationNumber)
            .IsUnique();

        builder.HasIndex(v => v.CarrierId);

        builder.HasIndex(v => v.Status);

        builder.HasIndex(v => v.VehicleType);

        builder.HasIndex(v => v.Make);

        builder.HasIndex(v => v.InsuranceExpiryDate);

        builder.HasIndex(v => v.FitnessExpiryDate);

        builder.HasIndex(v => v.NextMaintenanceDate);
    }
}
