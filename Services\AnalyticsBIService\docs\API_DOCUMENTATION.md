# Analytics & BI Service API Documentation

## Overview

This document provides comprehensive API documentation for the Analytics & Business Intelligence Service, including all endpoints, request/response formats, authentication requirements, and usage examples.

## Base URL
```
https://api.tli-platform.com/analytics-bi
```

## Authentication

All API endpoints require JWT Bearer token authentication unless otherwise specified.

```http
Authorization: Bearer <your-jwt-token>
```

### Supported Roles
- `Admin`: Full platform access
- `TransportCompany`: Transport company analytics
- `Broker`: Broker analytics and operations
- `Carrier`: Carrier performance and earnings
- `Shipper`: Shipper logistics and cost analytics

## Common Parameters

### Time Period Parameters
- `fromDate`: Start date (ISO 8601 format, e.g., "2024-01-01")
- `toDate`: End date (ISO 8601 format, e.g., "2024-01-31")
- `period`: Time granularity (`Daily`, `Weekly`, `Monthly`, `Quarterly`, `Yearly`)

### Pagination Parameters
- `page`: Page number (default: 1)
- `pageSize`: Items per page (default: 50, max: 100)

## Admin Analytics Endpoints

### Get Platform Dashboard
```http
GET /api/admin/analytics/dashboard
```

**Parameters:**
- `fromDate` (optional): Start date for analytics period
- `toDate` (optional): End date for analytics period
- `period` (optional): Time period granularity (default: Daily)

**Response:**
```json
{
  "generatedAt": "2024-01-15T10:30:00Z",
  "fromDate": "2024-01-01T00:00:00Z",
  "toDate": "2024-01-31T23:59:59Z",
  "period": "Daily",
  "platformMetrics": {
    "totalUsers": 12500,
    "activeUsers": 8750,
    "totalRevenue": 2500000,
    "systemPerformanceScore": 98.5
  },
  "userGrowth": {
    "totalUsers": 12500,
    "newUsers": 450,
    "activeUsers": 8750,
    "userGrowthRate": 15.2
  },
  "revenueAnalytics": {
    "totalRevenue": 2500000,
    "revenueGrowthRate": 12.8,
    "averageRevenuePerUser": 200
  }
}
```

### Get Real-time KPI Monitoring
```http
GET /api/admin/analytics/kpis/realtime
```

**Parameters:**
- `kpiNames` (optional): Comma-separated list of KPI names to monitor
- `refreshInterval` (optional): Refresh interval in seconds (default: 30)

**Response:**
```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "kpis": [
    {
      "kpiName": "ActiveUsers",
      "currentValue": 8750,
      "targetValue": 10000,
      "performancePercentage": 87.5,
      "status": "Good",
      "trend": "Increasing"
    }
  ]
}
```

### Get Revenue Analytics
```http
GET /api/admin/analytics/revenue
```

**Parameters:**
- `fromDate`: Start date for revenue analysis
- `toDate`: End date for revenue analysis
- `period` (optional): Time period granularity
- `includeBreakdown` (optional): Include revenue breakdown by user type
- `includeForecasting` (optional): Include revenue forecasting
- `includeTrends` (optional): Include revenue trends

**Response:**
```json
{
  "fromDate": "2024-01-01T00:00:00Z",
  "toDate": "2024-01-31T23:59:59Z",
  "period": "Daily",
  "totalRevenue": 2500000,
  "revenueGrowthRate": 12.8,
  "revenueByUserType": [
    {
      "userType": "TransportCompany",
      "revenue": 1200000,
      "percentage": 48.0
    }
  ],
  "revenueTrends": [
    {
      "date": "2024-01-01T00:00:00Z",
      "revenue": 80000,
      "cumulativeRevenue": 80000
    }
  ]
}
```

## Transport Company Analytics Endpoints

### Get Transport Company Dashboard
```http
GET /api/transport-company/analytics/{id}/dashboard
```

**Parameters:**
- `id`: Transport company ID (UUID)
- `fromDate` (optional): Start date for analytics period
- `toDate` (optional): End date for analytics period
- `period` (optional): Time period granularity

**Response:**
```json
{
  "transportCompanyId": "123e4567-e89b-12d3-a456-************",
  "generatedAt": "2024-01-15T10:30:00Z",
  "fromDate": "2024-01-01T00:00:00Z",
  "toDate": "2024-01-31T23:59:59Z",
  "period": "Daily",
  "performance": {
    "rfqConversionRate": 68.5,
    "averageResponseTime": 18.5,
    "deliveryPerformanceScore": 92.3,
    "customerSatisfactionScore": 4.2
  },
  "costAnalysis": {
    "totalCosts": 125000,
    "costPerTrip": 2500,
    "costGrowthRate": 8.5
  },
  "marketIntelligence": {
    "marketShare": 12.5,
    "competitivePosition": "Strong",
    "growthOpportunities": 3
  }
}
```

### Get RFQ Conversion Analytics
```http
GET /api/transport-company/analytics/{id}/rfq-conversion
```

**Parameters:**
- `id`: Transport company ID
- `fromDate`: Start date for RFQ analysis
- `toDate`: End date for RFQ analysis
- `period` (optional): Time period granularity
- `brokerId` (optional): Filter by specific broker
- `includeConversionFunnel` (optional): Include conversion funnel analysis

**Response:**
```json
{
  "transportCompanyId": "123e4567-e89b-12d3-a456-************",
  "fromDate": "2024-01-01T00:00:00Z",
  "toDate": "2024-01-31T23:59:59Z",
  "period": "Daily",
  "overallConversionRate": 68.5,
  "totalRFQsReceived": 450,
  "successfulConversions": 308,
  "conversionFunnel": {
    "rfqsReceived": 450,
    "rfqsViewed": 425,
    "quotesGenerated": 380,
    "quotesAccepted": 308
  },
  "conversionTrends": [
    {
      "date": "2024-01-01T00:00:00Z",
      "rfqsReceived": 15,
      "conversions": 10,
      "conversionRate": 66.7
    }
  ]
}
```

## Broker Analytics Endpoints

### Get Broker Dashboard
```http
GET /api/broker/analytics/{id}/dashboard
```

**Parameters:**
- `id`: Broker ID (UUID)
- `fromDate` (optional): Start date for analytics period
- `toDate` (optional): End date for analytics period
- `period` (optional): Time period granularity

**Response:**
```json
{
  "brokerId": "123e4567-e89b-12d3-a456-************",
  "generatedAt": "2024-01-15T10:30:00Z",
  "operational": {
    "rfqConversionRate": 72.3,
    "quoteSuccessRate": 68.5,
    "carrierNetworkUtilization": 85.2,
    "operationalEfficiencyScore": 88.7
  },
  "marginAnalysis": {
    "grossMargin": 125000,
    "netMargin": 87500,
    "marginGrowthRate": 12.8
  },
  "businessGrowth": {
    "revenueGrowthRate": 15.7,
    "customerGrowthRate": 18.1,
    "marketShareGrowth": 15.7
  }
}
```

### Get Quote Success Analytics
```http
GET /api/broker/analytics/{id}/quote-success
```

**Parameters:**
- `id`: Broker ID
- `fromDate`: Start date for quote analysis
- `toDate`: End date for quote analysis
- `period` (optional): Time period granularity
- `quoteType` (optional): Filter by quote type
- `serviceLevel` (optional): Filter by service level

**Response:**
```json
{
  "brokerId": "123e4567-e89b-12d3-a456-************",
  "fromDate": "2024-01-01T00:00:00Z",
  "toDate": "2024-01-31T23:59:59Z",
  "totalQuotesGenerated": 850,
  "acceptedQuotes": 582,
  "overallSuccessRate": 68.5,
  "averageQuoteValue": 24000,
  "averageMargin": 15.5,
  "quoteTypeAnalysis": [
    {
      "quoteType": "Express",
      "totalQuotes": 250,
      "acceptedQuotes": 185,
      "successRate": 74.0,
      "averageValue": 28000
    }
  ]
}
```

## Carrier Analytics Endpoints

### Get Carrier Dashboard
```http
GET /api/carrier/analytics/{id}/dashboard
```

**Parameters:**
- `id`: Carrier ID (UUID)
- `fromDate` (optional): Start date for analytics period
- `toDate` (optional): End date for analytics period
- `period` (optional): Time period granularity

**Response:**
```json
{
  "carrierId": "123e4567-e89b-12d3-a456-************",
  "generatedAt": "2024-01-15T10:30:00Z",
  "performance": {
    "deliveryCompletionRate": 95.2,
    "onTimeDeliveryRate": 92.5,
    "customerSatisfactionScore": 4.3,
    "performanceScore": 85.8
  },
  "earningsAnalytics": {
    "totalEarnings": 125000,
    "netEarnings": 87500,
    "earningsGrowthRate": 12.8,
    "averageEarningsPerTrip": 2500
  },
  "efficiencyTracking": {
    "overallEfficiencyScore": 82.1,
    "vehicleUtilizationRate": 78.5,
    "fuelEfficiencyRate": 6.8
  }
}
```

### Get Earnings Analytics
```http
GET /api/carrier/analytics/{id}/earnings
```

**Parameters:**
- `id`: Carrier ID
- `fromDate`: Start date for earnings analysis
- `toDate`: End date for earnings analysis
- `period` (optional): Time period granularity
- `includeIncomeTrends` (optional): Include income trends
- `includeBenchmarking` (optional): Include performance benchmarking
- `includeOptimization` (optional): Include optimization opportunities

**Response:**
```json
{
  "carrierId": "123e4567-e89b-12d3-a456-************",
  "fromDate": "2024-01-01T00:00:00Z",
  "toDate": "2024-01-31T23:59:59Z",
  "totalEarnings": 125000,
  "netEarnings": 87500,
  "earningsGrowthRate": 12.8,
  "averageEarningsPerTrip": 2500,
  "performanceBenchmarking": {
    "industryAverageEarnings": 115000,
    "earningsCompetitiveness": 108.7,
    "benchmarkRating": "Good"
  },
  "optimizationOpportunities": [
    {
      "opportunityType": "Route Optimization",
      "title": "Optimize high-value routes",
      "potentialIncrease": 15000,
      "priority": "High"
    }
  ]
}
```

## Shipper Analytics Endpoints

### Get Shipper Dashboard
```http
GET /api/shipper/analytics/{id}/dashboard
```

**Parameters:**
- `id`: Shipper ID (UUID)
- `fromDate` (optional): Start date for analytics period
- `toDate` (optional): End date for analytics period
- `period` (optional): Time period granularity

**Response:**
```json
{
  "shipperId": "123e4567-e89b-12d3-a456-************",
  "generatedAt": "2024-01-15T10:30:00Z",
  "slaPerformance": {
    "overallSLACompliance": 92.5,
    "onTimeDeliveryRate": 94.2,
    "averageDeliveryTime": 48.5
  },
  "costAnalysis": {
    "totalShippingCosts": 125000,
    "costPerTrip": 2500,
    "costGrowthRate": 8.5
  },
  "providerComparison": {
    "totalProviders": 5,
    "topPerformingProvider": "Express Logistics",
    "averagePerformanceScore": 85.2
  }
}
```

### Get SLA Performance
```http
GET /api/shipper/analytics/{id}/sla-performance
```

**Parameters:**
- `id`: Shipper ID
- `fromDate`: Start date for SLA analysis
- `toDate`: End date for SLA analysis
- `period` (optional): Time period granularity
- `includeSLAMetrics` (optional): Include detailed SLA metrics
- `includeKPIPerformance` (optional): Include KPI performance analysis
- `includeComplianceAnalysis` (optional): Include compliance analysis

**Response:**
```json
{
  "shipperId": "123e4567-e89b-12d3-a456-************",
  "fromDate": "2024-01-01T00:00:00Z",
  "toDate": "2024-01-31T23:59:59Z",
  "overallSLACompliance": 92.5,
  "onTimeDeliveryRate": 94.2,
  "slaViolations": 15,
  "averageDeliveryTime": 48.5,
  "slaMetrics": [
    {
      "metricName": "On-Time Delivery",
      "currentValue": 94.2,
      "targetValue": 95.0,
      "complianceStatus": "Non-Compliant"
    }
  ],
  "complianceAnalysis": {
    "overallComplianceRate": 92.5,
    "complianceByProvider": [
      {
        "providerName": "Express Logistics",
        "complianceRate": 95.2,
        "complianceStatus": "Good"
      }
    ]
  }
}
```

## Real-time Analytics Endpoints

### Get Real-time KPIs
```http
GET /api/realtime/kpis
```

**Response:**
```json
[
  {
    "kpiName": "Active Users",
    "currentValue": 1250,
    "unit": "count",
    "status": "Normal",
    "trend": "Increasing",
    "lastUpdated": "2024-01-15T10:30:00Z"
  }
]
```

### Process Real-time Event
```http
POST /api/realtime/events
```

**Request Body:**
```json
{
  "userId": "123e4567-e89b-12d3-a456-************",
  "eventType": "UserAction",
  "eventName": "PageView",
  "eventData": {
    "page": "/dashboard",
    "duration": "30"
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "sessionId": "session-123",
  "userAgent": "Mozilla/5.0...",
  "ipAddress": "*************"
}
```

**Response:**
```json
{
  "message": "Event processed successfully"
}
```

## Data Export Endpoints

### Export Data
```http
POST /api/data-export/export
```

**Request Body:**
```json
{
  "dataType": "Analytics",
  "format": "CSV",
  "fileName": "analytics_export",
  "filters": {
    "dateRange": "last_30_days",
    "userRole": "TransportCompany"
  },
  "columns": ["id", "name", "value", "timestamp"]
}
```

**Response:**
```json
{
  "fileName": "analytics_export_20240115_103000.csv",
  "fileUrl": "https://storage.tli-platform.com/exports/analytics_export_20240115_103000.csv",
  "format": "CSV",
  "recordCount": 1500,
  "fileSizeBytes": 245760,
  "generatedAt": "2024-01-15T10:30:00Z",
  "expiresAt": "2024-01-22T10:30:00Z"
}
```

### Get Supported Export Formats
```http
GET /api/data-export/formats
```

**Response:**
```json
[
  {
    "format": "CSV",
    "name": "CSV",
    "description": "Comma-separated values",
    "mimeType": "text/csv",
    "extension": ".csv",
    "maxRecords": 1000000
  },
  {
    "format": "Excel",
    "name": "Excel",
    "description": "Microsoft Excel spreadsheet",
    "mimeType": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "extension": ".xlsx",
    "maxRecords": 500000
  }
]
```

## Error Responses

### Standard Error Format
```json
{
  "error": {
    "code": "INVALID_REQUEST",
    "message": "The request parameters are invalid",
    "details": "The 'fromDate' parameter must be before 'toDate'",
    "timestamp": "2024-01-15T10:30:00Z",
    "traceId": "trace-123456"
  }
}
```

### Common Error Codes
- `400 Bad Request`: Invalid request parameters
- `401 Unauthorized`: Missing or invalid authentication token
- `403 Forbidden`: Insufficient permissions for the requested resource
- `404 Not Found`: Requested resource not found
- `429 Too Many Requests`: Rate limit exceeded
- `500 Internal Server Error`: Server error occurred

## Rate Limiting

API requests are rate-limited per user:
- **Standard Users**: 1000 requests per hour
- **Premium Users**: 5000 requests per hour
- **Enterprise Users**: 10000 requests per hour

Rate limit headers are included in responses:
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1642248600
```

## Webhooks

The service supports webhooks for real-time notifications:

### Alert Webhooks
```http
POST https://your-webhook-url.com/analytics-alerts
```

**Payload:**
```json
{
  "alertId": "123e4567-e89b-12d3-a456-************",
  "userId": "123e4567-e89b-12d3-a456-************",
  "alertType": "Performance",
  "severity": "Critical",
  "title": "High CPU Usage",
  "message": "CPU usage is above 90%",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## SDK and Libraries

Official SDKs are available for:
- **JavaScript/TypeScript**: `@tli-platform/analytics-sdk`
- **Python**: `tli-analytics-python`
- **C#**: `TLI.Analytics.SDK`
- **Java**: `com.tli.analytics.sdk`

## Support

For API support and questions:
- **Documentation**: https://docs.tli-platform.com/analytics-bi
- **Support Email**: <EMAIL>
- **Developer Portal**: https://developers.tli-platform.com
