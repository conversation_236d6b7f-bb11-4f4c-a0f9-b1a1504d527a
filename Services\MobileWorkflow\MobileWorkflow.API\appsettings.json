{"ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=mobileworkflow_db;Username=timescale;Password=timescale"}, "Jwt": {"Key": "YourSuperSecretKeyThatIsAtLeast32CharactersLong!", "Issuer": "TLI.MobileWorkflow", "Audience": "TLI.MobileWorkflow.Users", "ExpiryInMinutes": 60}, "Services": {"IdentityService": {"BaseUrl": "http://localhost:5001"}, "UserManagement": {"BaseUrl": "http://localhost:5002"}, "TripManagement": {"BaseUrl": "http://localhost:5003"}, "OrderManagement": {"BaseUrl": "http://localhost:5004"}, "NetworkFleetManagement": {"BaseUrl": "http://localhost:5005"}, "CommunicationNotification": {"BaseUrl": "http://localhost:5006"}, "FinancialPayment": {"BaseUrl": "http://localhost:5007"}, "SubscriptionManagement": {"BaseUrl": "http://localhost:5008"}, "AnalyticsBI": {"BaseUrl": "http://localhost:5009"}, "APIGateway": {"BaseUrl": "http://localhost:5010"}, "DataStorage": {"BaseUrl": "http://localhost:5011"}, "Monitoring": {"BaseUrl": "http://localhost:5012"}, "AuditCompliance": {"BaseUrl": "http://localhost:5013"}}, "MobileApp": {"DefaultSyncInterval": 300, "MaxOfflineDataSize": 104857600, "MaxOfflineDataAge": 86400, "SupportedPlatforms": ["Android", "iOS", "Web", "PWA"], "Features": {"OfflineMode": true, "PushNotifications": true, "BackgroundSync": true, "BiometricAuth": true, "LocationTracking": true, "CameraAccess": true, "FileUpload": true, "VoiceRecording": true, "QRScanner": true}}, "PWA": {"CacheName": "tli-mobile-v1", "CacheStrategy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "OfflinePageUrl": "/offline.html", "MaxCacheSize": 52428800, "MaxCacheAge": 604800, "UpdateCheckInterval": 21600}, "Workflow": {"MaxConcurrentExecutions": 100, "DefaultTaskSLA": "24:00:00", "AutoRetryFailedTasks": true, "MaxRetryAttempts": 3, "RetryDelay": "00:05:00", "EnableWorkflowMetrics": true, "WorkflowTimeout": "72:00:00"}, "Sync": {"BatchSize": 50, "MaxRetryAttempts": 5, "RetryDelay": "00:01:00", "ConflictResolution": {"Default": "server_wins", "TripUpdate": "latest_timestamp", "PODUpload": "merge", "Emergency": "client_wins", "LocationUpdate": "latest_timestamp", "DocumentUpload": "client_wins"}, "PriorityDataTypes": ["Emergency", "TripUpdate", "PODUpload"], "SyncOnWiFiOnly": false, "SyncOnLowBattery": false}, "Integration": {"Timeout": "00:00:30", "RetryPolicy": {"MaxRetryAttempts": 3, "RetryDelay": "00:00:05", "ExponentialBackoff": true}, "CircuitBreaker": {"FailureThreshold": 5, "RecoveryTimeout": "00:01:00"}}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/mobileworkflow-.txt", "rollingInterval": "Day", "retainedFileCountLimit": 30, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}, "AllowedHosts": "*", "Kestrel": {"Endpoints": {"Http": {"Url": "http://localhost:5014"}, "Https": {"Url": "https://localhost:7014"}}}}