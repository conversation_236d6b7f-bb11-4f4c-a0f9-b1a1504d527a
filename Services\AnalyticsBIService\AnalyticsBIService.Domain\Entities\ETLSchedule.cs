using Shared.Domain.Common;

namespace AnalyticsBIService.Domain.Entities;

/// <summary>
/// ETL schedule entity for scheduling ETL pipeline executions
/// </summary>
public class ETLSchedule : BaseEntity
{
    public Guid PipelineId { get; private set; }
    public string Name { get; private set; }
    public string ScheduleType { get; private set; } // OneTime, Daily, Weekly, Monthly, Custom
    public string CronExpression { get; private set; }
    public DateTime StartDate { get; private set; }
    public DateTime? EndDate { get; private set; }
    public DateTime NextRunDate { get; private set; }
    public DateTime? LastRunDate { get; private set; }
    public bool IsActive { get; private set; }
    public string? ExecutionOptions { get; private set; } // JSON serialized execution options

    private ETLSchedule()
    {
        Name = string.Empty;
        ScheduleType = string.Empty;
        CronExpression = string.Empty;
    }

    public ETLSchedule(
        Guid pipelineId,
        string name,
        string scheduleType,
        string cronExpression,
        DateTime startDate,
        DateTime? endDate = null,
        string? executionOptions = null)
    {
        PipelineId = pipelineId;
        Name = name ?? throw new ArgumentNullException(nameof(name));
        ScheduleType = scheduleType ?? throw new ArgumentNullException(nameof(scheduleType));
        CronExpression = cronExpression ?? throw new ArgumentNullException(nameof(cronExpression));
        StartDate = startDate;
        EndDate = endDate;
        ExecutionOptions = executionOptions;
        IsActive = true;
        NextRunDate = CalculateNextRunDate();
    }

    public void UpdateSchedule(string scheduleType, string cronExpression, DateTime startDate, DateTime? endDate = null)
    {
        ScheduleType = scheduleType ?? throw new ArgumentNullException(nameof(scheduleType));
        CronExpression = cronExpression ?? throw new ArgumentNullException(nameof(cronExpression));
        StartDate = startDate;
        EndDate = endDate;
        NextRunDate = CalculateNextRunDate();
        SetUpdatedAt();
    }

    public void UpdateExecutionOptions(string executionOptions)
    {
        ExecutionOptions = executionOptions;
        SetUpdatedAt();
    }

    public void Activate()
    {
        IsActive = true;
        NextRunDate = CalculateNextRunDate();
        SetUpdatedAt();
    }

    public void Deactivate()
    {
        IsActive = false;
        SetUpdatedAt();
    }

    public void RecordExecution()
    {
        LastRunDate = DateTime.UtcNow;
        NextRunDate = CalculateNextRunDate();
        SetUpdatedAt();
    }

    private DateTime CalculateNextRunDate()
    {
        // Simple implementation - in real scenario would use proper cron parser
        return ScheduleType switch
        {
            "Daily" => DateTime.UtcNow.AddDays(1),
            "Weekly" => DateTime.UtcNow.AddDays(7),
            "Monthly" => DateTime.UtcNow.AddMonths(1),
            _ => StartDate > DateTime.UtcNow ? StartDate : DateTime.UtcNow.AddHours(1)
        };
    }
}
