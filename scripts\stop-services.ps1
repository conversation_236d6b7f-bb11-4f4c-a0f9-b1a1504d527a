# PowerShell script to stop all services
Write-Host "Stopping TLI Microservices..." -ForegroundColor Yellow

# Stop Docker containers
Write-Host "Stopping Docker containers..." -ForegroundColor Yellow
docker-compose down

# Kill any running dotnet processes for our services
Write-Host "Stopping .NET processes..." -ForegroundColor Yellow

$processes = Get-Process -Name "dotnet" -ErrorAction SilentlyContinue | Where-Object {
    $_.MainWindowTitle -like "*Identity.API*" -or
    $_.MainWindowTitle -like "*ApiGateway*" -or
    $_.CommandLine -like "*Identity.API*" -or
    $_.CommandLine -like "*ApiGateway*"
}

if ($processes) {
    $processes | ForEach-Object {
        Write-Host "Stopping process: $($_.ProcessName) (ID: $($_.Id))" -ForegroundColor Red
        Stop-Process -Id $_.Id -Force -ErrorAction SilentlyContinue
    }
} else {
    Write-Host "No running service processes found." -ForegroundColor Green
}

Write-Host "All services stopped." -ForegroundColor Green
