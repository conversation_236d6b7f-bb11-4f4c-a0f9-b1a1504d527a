using Microsoft.EntityFrameworkCore;
using MonitoringObservability.Domain.Entities;
using MonitoringObservability.Domain.Interfaces;
using MonitoringObservability.Domain.Enums;
using MonitoringObservability.Infrastructure.Data;

namespace MonitoringObservability.Infrastructure.Repositories;

public class MetricRepository : IMetricRepository
{
    private readonly MonitoringDbContext _context;

    public MetricRepository(MonitoringDbContext context)
    {
        _context = context;
    }

    public async Task<Metric?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.Metrics
            .FirstOrDefaultAsync(m => m.Id == id, cancellationToken);
    }

    public async Task<Metric?> GetByNameAndServiceAsync(string name, string serviceName, CancellationToken cancellationToken = default)
    {
        return await _context.Metrics
            .FirstOrDefaultAsync(m => m.Name == name && m.ServiceName == serviceName, cancellationToken);
    }

    public async Task<IEnumerable<Metric>> GetByServiceAsync(string serviceName, CancellationToken cancellationToken = default)
    {
        return await _context.Metrics
            .Where(m => m.ServiceName == serviceName)
            .OrderBy(m => m.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task AddAsync(Metric metric, CancellationToken cancellationToken = default)
    {
        await _context.Metrics.AddAsync(metric, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task UpdateAsync(Metric metric, CancellationToken cancellationToken = default)
    {
        _context.Metrics.Update(metric);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var metric = await GetByIdAsync(id, cancellationToken);
        if (metric != null)
        {
            _context.Metrics.Remove(metric);
            await _context.SaveChangesAsync(cancellationToken);
        }
    }

    public async Task<IEnumerable<Metric>> SearchAsync(
        string? serviceName = null,
        MetricType? type = null,
        MonitoringCategory? category = null,
        bool? isActive = null,
        bool? alertingEnabled = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.Metrics.AsQueryable();

        if (!string.IsNullOrWhiteSpace(serviceName))
            query = query.Where(m => m.ServiceName == serviceName);

        if (type.HasValue)
            query = query.Where(m => m.Type == type.Value);

        if (category.HasValue)
            query = query.Where(m => m.Category == category.Value);

        if (isActive.HasValue)
            query = query.Where(m => m.IsActive == isActive.Value);

        if (alertingEnabled.HasValue)
            query = query.Where(m => m.AlertingEnabled == alertingEnabled.Value);

        return await query
            .OrderBy(m => m.ServiceName)
            .ThenBy(m => m.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<MetricDataPoint>> GetDataPointsAsync(
        Guid metricId,
        DateTime fromDate,
        DateTime toDate,
        CancellationToken cancellationToken = default)
    {
        return await _context.MetricDataPoints
            .Where(mdp => mdp.MetricId == metricId && 
                         mdp.Timestamp >= fromDate && 
                         mdp.Timestamp <= toDate)
            .OrderBy(mdp => mdp.Timestamp)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<MetricDataPoint>> GetLatestDataPointsAsync(
        string serviceName,
        TimeSpan period,
        CancellationToken cancellationToken = default)
    {
        var cutoff = DateTime.UtcNow - period;
        
        return await _context.MetricDataPoints
            .Join(_context.Metrics,
                mdp => mdp.MetricId,
                m => m.Id,
                (mdp, m) => new { DataPoint = mdp, Metric = m })
            .Where(x => x.Metric.ServiceName == serviceName && x.DataPoint.Timestamp >= cutoff)
            .Select(x => x.DataPoint)
            .OrderByDescending(mdp => mdp.Timestamp)
            .ToListAsync(cancellationToken);
    }

    public async Task<double> GetAverageValueAsync(Guid metricId, TimeSpan period, CancellationToken cancellationToken = default)
    {
        var cutoff = DateTime.UtcNow - period;
        
        var dataPoints = await _context.MetricDataPoints
            .Where(mdp => mdp.MetricId == metricId && mdp.Timestamp >= cutoff)
            .ToListAsync(cancellationToken);

        return dataPoints.Any() ? dataPoints.Average(mdp => mdp.Value) : 0;
    }

    public async Task<double> GetMaxValueAsync(Guid metricId, TimeSpan period, CancellationToken cancellationToken = default)
    {
        var cutoff = DateTime.UtcNow - period;
        
        var dataPoints = await _context.MetricDataPoints
            .Where(mdp => mdp.MetricId == metricId && mdp.Timestamp >= cutoff)
            .ToListAsync(cancellationToken);

        return dataPoints.Any() ? dataPoints.Max(mdp => mdp.Value) : 0;
    }

    public async Task<double> GetMinValueAsync(Guid metricId, TimeSpan period, CancellationToken cancellationToken = default)
    {
        var cutoff = DateTime.UtcNow - period;
        
        var dataPoints = await _context.MetricDataPoints
            .Where(mdp => mdp.MetricId == metricId && mdp.Timestamp >= cutoff)
            .ToListAsync(cancellationToken);

        return dataPoints.Any() ? dataPoints.Min(mdp => mdp.Value) : 0;
    }

    public async Task CleanupOldDataPointsAsync(TimeSpan retentionPeriod, CancellationToken cancellationToken = default)
    {
        var cutoff = DateTime.UtcNow - retentionPeriod;
        
        var oldDataPoints = await _context.MetricDataPoints
            .Where(mdp => mdp.Timestamp < cutoff)
            .ToListAsync(cancellationToken);

        if (oldDataPoints.Any())
        {
            _context.MetricDataPoints.RemoveRange(oldDataPoints);
            await _context.SaveChangesAsync(cancellationToken);
        }
    }
}

public class HealthCheckRepository : IHealthCheckRepository
{
    private readonly MonitoringDbContext _context;

    public HealthCheckRepository(MonitoringDbContext context)
    {
        _context = context;
    }

    public async Task<HealthCheck?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.HealthChecks
            .FirstOrDefaultAsync(hc => hc.Id == id, cancellationToken);
    }

    public async Task<HealthCheck?> GetByNameAndServiceAsync(string name, string serviceName, CancellationToken cancellationToken = default)
    {
        return await _context.HealthChecks
            .FirstOrDefaultAsync(hc => hc.Name == name && hc.ServiceName == serviceName, cancellationToken);
    }

    public async Task<IEnumerable<HealthCheck>> GetByServiceAsync(string serviceName, CancellationToken cancellationToken = default)
    {
        return await _context.HealthChecks
            .Where(hc => hc.ServiceName == serviceName)
            .OrderBy(hc => hc.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task AddAsync(HealthCheck healthCheck, CancellationToken cancellationToken = default)
    {
        await _context.HealthChecks.AddAsync(healthCheck, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task UpdateAsync(HealthCheck healthCheck, CancellationToken cancellationToken = default)
    {
        _context.HealthChecks.Update(healthCheck);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var healthCheck = await GetByIdAsync(id, cancellationToken);
        if (healthCheck != null)
        {
            _context.HealthChecks.Remove(healthCheck);
            await _context.SaveChangesAsync(cancellationToken);
        }
    }

    public async Task<IEnumerable<HealthCheck>> GetAllActiveAsync(CancellationToken cancellationToken = default)
    {
        return await _context.HealthChecks
            .Where(hc => hc.IsEnabled)
            .OrderBy(hc => hc.ServiceName)
            .ThenBy(hc => hc.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<HealthCheck>> GetByStatusAsync(HealthStatus status, CancellationToken cancellationToken = default)
    {
        return await _context.HealthChecks
            .Where(hc => hc.CurrentStatus == status)
            .OrderBy(hc => hc.ServiceName)
            .ThenBy(hc => hc.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<HealthCheck>> GetDueForCheckAsync(CancellationToken cancellationToken = default)
    {
        var now = DateTime.UtcNow;
        
        return await _context.HealthChecks
            .Where(hc => hc.IsEnabled && 
                        EF.Functions.DateDiffSecond(hc.LastChecked, now) >= hc.Interval.TotalSeconds)
            .OrderBy(hc => hc.LastChecked)
            .ToListAsync(cancellationToken);
    }

    public async Task<Dictionary<HealthStatus, int>> GetHealthStatusCountAsync(CancellationToken cancellationToken = default)
    {
        return await _context.HealthChecks
            .GroupBy(hc => hc.CurrentStatus)
            .ToDictionaryAsync(g => g.Key, g => g.Count(), cancellationToken);
    }

    public async Task<Dictionary<string, HealthStatus>> GetServiceHealthSummaryAsync(CancellationToken cancellationToken = default)
    {
        var serviceHealthChecks = await _context.HealthChecks
            .Where(hc => hc.IsEnabled)
            .GroupBy(hc => hc.ServiceName)
            .ToListAsync(cancellationToken);

        var result = new Dictionary<string, HealthStatus>();

        foreach (var group in serviceHealthChecks)
        {
            var healthChecks = group.ToList();
            
            // Determine overall service health based on worst health check status
            var worstStatus = healthChecks
                .Select(hc => hc.CurrentStatus)
                .OrderByDescending(status => (int)status)
                .FirstOrDefault();

            result[group.Key] = worstStatus;
        }

        return result;
    }
}

public class IncidentRepository : IIncidentRepository
{
    private readonly MonitoringDbContext _context;

    public IncidentRepository(MonitoringDbContext context)
    {
        _context = context;
    }

    public async Task<Incident?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.Incidents
            .FirstOrDefaultAsync(i => i.Id == id, cancellationToken);
    }

    public async Task<Incident?> GetByIdWithDetailsAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.Incidents
            .Include(i => i.Updates)
            .Include(i => i.RelatedAlerts)
            .Include(i => i.Comments)
            .FirstOrDefaultAsync(i => i.Id == id, cancellationToken);
    }

    public async Task<IEnumerable<Incident>> GetByServiceAsync(string serviceName, CancellationToken cancellationToken = default)
    {
        return await _context.Incidents
            .Where(i => i.ServiceName == serviceName)
            .OrderByDescending(i => i.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task AddAsync(Incident incident, CancellationToken cancellationToken = default)
    {
        await _context.Incidents.AddAsync(incident, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task UpdateAsync(Incident incident, CancellationToken cancellationToken = default)
    {
        _context.Incidents.Update(incident);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var incident = await GetByIdAsync(id, cancellationToken);
        if (incident != null)
        {
            _context.Incidents.Remove(incident);
            await _context.SaveChangesAsync(cancellationToken);
        }
    }

    public async Task<IEnumerable<Incident>> SearchAsync(
        string? serviceName = null,
        IncidentSeverity? severity = null,
        IncidentStatus? status = null,
        DateTime? createdAfter = null,
        DateTime? createdBefore = null,
        Guid? assignedToUserId = null,
        int skip = 0,
        int take = 50,
        CancellationToken cancellationToken = default)
    {
        var query = _context.Incidents.AsQueryable();

        if (!string.IsNullOrWhiteSpace(serviceName))
            query = query.Where(i => i.ServiceName == serviceName);

        if (severity.HasValue)
            query = query.Where(i => i.Severity == severity.Value);

        if (status.HasValue)
            query = query.Where(i => i.Status == status.Value);

        if (createdAfter.HasValue)
            query = query.Where(i => i.CreatedAt >= createdAfter.Value);

        if (createdBefore.HasValue)
            query = query.Where(i => i.CreatedAt <= createdBefore.Value);

        if (assignedToUserId.HasValue)
            query = query.Where(i => i.AssignedToUserId == assignedToUserId.Value);

        return await query
            .OrderByDescending(i => i.CreatedAt)
            .Skip(skip)
            .Take(take)
            .ToListAsync(cancellationToken);
    }

    public async Task<int> CountAsync(
        string? serviceName = null,
        IncidentSeverity? severity = null,
        IncidentStatus? status = null,
        DateTime? createdAfter = null,
        DateTime? createdBefore = null,
        Guid? assignedToUserId = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.Incidents.AsQueryable();

        if (!string.IsNullOrWhiteSpace(serviceName))
            query = query.Where(i => i.ServiceName == serviceName);

        if (severity.HasValue)
            query = query.Where(i => i.Severity == severity.Value);

        if (status.HasValue)
            query = query.Where(i => i.Status == status.Value);

        if (createdAfter.HasValue)
            query = query.Where(i => i.CreatedAt >= createdAfter.Value);

        if (createdBefore.HasValue)
            query = query.Where(i => i.CreatedAt <= createdBefore.Value);

        if (assignedToUserId.HasValue)
            query = query.Where(i => i.AssignedToUserId == assignedToUserId.Value);

        return await query.CountAsync(cancellationToken);
    }

    public async Task<IEnumerable<Incident>> GetOpenIncidentsAsync(CancellationToken cancellationToken = default)
    {
        return await _context.Incidents
            .Where(i => i.Status == IncidentStatus.Open || 
                       i.Status == IncidentStatus.Investigating || 
                       i.Status == IncidentStatus.Identified)
            .OrderByDescending(i => i.Severity)
            .ThenByDescending(i => i.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Incident>> GetCriticalIncidentsAsync(CancellationToken cancellationToken = default)
    {
        return await _context.Incidents
            .Where(i => i.Severity == IncidentSeverity.Critical && 
                       (i.Status == IncidentStatus.Open || 
                        i.Status == IncidentStatus.Investigating || 
                        i.Status == IncidentStatus.Identified))
            .OrderByDescending(i => i.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Incident>> GetIncidentsAssignedToUserAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await _context.Incidents
            .Where(i => i.AssignedToUserId == userId && 
                       (i.Status == IncidentStatus.Investigating || 
                        i.Status == IncidentStatus.Identified))
            .OrderByDescending(i => i.Severity)
            .ThenByDescending(i => i.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<Dictionary<IncidentSeverity, int>> GetIncidentCountBySeverityAsync(DateTime? fromDate = null, CancellationToken cancellationToken = default)
    {
        var query = _context.Incidents.AsQueryable();

        if (fromDate.HasValue)
            query = query.Where(i => i.CreatedAt >= fromDate.Value);

        return await query
            .GroupBy(i => i.Severity)
            .ToDictionaryAsync(g => g.Key, g => g.Count(), cancellationToken);
    }

    public async Task<Dictionary<string, int>> GetIncidentCountByServiceAsync(DateTime? fromDate = null, CancellationToken cancellationToken = default)
    {
        var query = _context.Incidents.AsQueryable();

        if (fromDate.HasValue)
            query = query.Where(i => i.CreatedAt >= fromDate.Value);

        return await query
            .GroupBy(i => i.ServiceName)
            .ToDictionaryAsync(g => g.Key, g => g.Count(), cancellationToken);
    }

    public async Task<double> GetAverageResolutionTimeAsync(DateTime? fromDate = null, CancellationToken cancellationToken = default)
    {
        var query = _context.Incidents.AsQueryable();

        if (fromDate.HasValue)
            query = query.Where(i => i.CreatedAt >= fromDate.Value);

        var resolvedIncidents = await query
            .Where(i => i.ResolvedAt.HasValue)
            .Select(i => new { i.CreatedAt, i.ResolvedAt })
            .ToListAsync(cancellationToken);

        if (!resolvedIncidents.Any())
            return 0;

        var resolutionTimes = resolvedIncidents
            .Select(i => (i.ResolvedAt!.Value - i.CreatedAt).TotalMinutes)
            .ToList();

        return resolutionTimes.Average();
    }
}
