using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Exceptions;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Application.Commands.WithdrawBid;

public class WithdrawBidCommandHandler : IRequestHandler<WithdrawBidCommand, bool>
{
    private readonly IRfqBidRepository _bidRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<WithdrawBidCommandHandler> _logger;

    public WithdrawBidCommandHandler(
        IRfqBidRepository bidRepository,
        IUnitOfWork unitOfWork,
        ILogger<WithdrawBidCommandHandler> logger)
    {
        _bidRepository = bidRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<bool> Handle(WithdrawBidCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Withdrawing bid {BidId} by broker {BrokerId}", 
            request.BidId, request.BrokerId);

        var bid = await _bidRepository.GetByIdAsync(request.BidId, cancellationToken);
        
        if (bid == null)
        {
            _logger.LogWarning("Bid {BidId} not found", request.BidId);
            throw new OrderManagementException("Bid not found");
        }

        // Authorization check
        if (bid.BrokerId != request.BrokerId)
        {
            _logger.LogWarning("Broker {BrokerId} is not authorized to withdraw bid {BidId}", 
                request.BrokerId, request.BidId);
            throw new OrderManagementException("You are not authorized to withdraw this bid");
        }

        // Business rule checks
        if (bid.Status != BidStatus.Submitted)
        {
            _logger.LogWarning("Cannot withdraw bid {BidId} with status {Status}", request.BidId, bid.Status);
            throw new OrderManagementException($"Cannot withdraw bid with status {bid.Status}");
        }

        // Withdraw the bid
        bid.Withdraw(request.WithdrawalReason ?? "Bid withdrawn by broker");

        _bidRepository.Update(bid);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Successfully withdrew bid {BidId}", request.BidId);
        
        return true;
    }
}
