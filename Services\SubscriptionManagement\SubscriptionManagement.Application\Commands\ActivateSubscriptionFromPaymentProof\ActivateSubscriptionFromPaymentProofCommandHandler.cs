using MediatR;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Interfaces;
using SubscriptionManagement.Domain.ValueObjects;

namespace SubscriptionManagement.Application.Commands.ActivateSubscriptionFromPaymentProof
{
    public class ActivateSubscriptionFromPaymentProofCommandHandler : IRequestHandler<ActivateSubscriptionFromPaymentProofCommand, bool>
    {
        private readonly ISubscriptionRepository _subscriptionRepository;
        private readonly IPaymentRepository _paymentRepository;
        private readonly IPlanRepository _planRepository;
        private readonly IInvoiceService _invoiceService;
        private readonly ILogger<ActivateSubscriptionFromPaymentProofCommandHandler> _logger;

        public ActivateSubscriptionFromPaymentProofCommandHandler(
            ISubscriptionRepository subscriptionRepository,
            IPaymentRepository paymentRepository,
            IPlanRepository planRepository,
            IInvoiceService invoiceService,
            ILogger<ActivateSubscriptionFromPaymentProofCommandHandler> logger)
        {
            _subscriptionRepository = subscriptionRepository;
            _paymentRepository = paymentRepository;
            _planRepository = planRepository;
            _invoiceService = invoiceService;
            _logger = logger;
        }

        public async Task<bool> Handle(ActivateSubscriptionFromPaymentProofCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Activating subscription {SubscriptionId} from verified payment proof {PaymentProofId}",
                request.SubscriptionId, request.PaymentProofId);

            try
            {
                // Get subscription
                var subscription = await _subscriptionRepository.GetByIdAsync(request.SubscriptionId);
                if (subscription == null)
                {
                    throw new ArgumentException($"Subscription with ID {request.SubscriptionId} not found");
                }

                // Get plan details for billing calculations
                var plan = await _planRepository.GetByIdAsync(subscription.PlanId);
                if (plan == null)
                {
                    throw new ArgumentException($"Plan with ID {subscription.PlanId} not found");
                }

                // Create payment record for the verified payment
                var paymentAmount = new Money(request.PaymentAmount, request.Currency);
                var payment = new Payment(request.SubscriptionId, subscription.UserId, paymentAmount, "Offline Payment");

                // Mark payment as completed since it's verified
                payment.MarkAsCompleted($"PaymentProof_{request.PaymentProofId}", "Verified offline payment");

                // Add payment to repository
                await _paymentRepository.AddAsync(payment);

                // Calculate subscription extension based on payment amount and plan price
                var extensionPeriod = CalculateExtensionPeriod(paymentAmount, plan.Price, plan.BillingCycle);

                // Activate or extend subscription based on current status
                if (subscription.Status == SubscriptionStatus.Pending || subscription.Status == SubscriptionStatus.Expired)
                {
                    // Activate subscription
                    subscription.Activate();
                    _logger.LogInformation("Subscription {SubscriptionId} activated", request.SubscriptionId);
                }
                else if (subscription.Status == SubscriptionStatus.Active)
                {
                    // Extend active subscription
                    var currentEndDate = subscription.EndDate ?? subscription.NextBillingDate;
                    var newEndDate = currentEndDate.Add(extensionPeriod);

                    // Update next billing date
                    subscription.ExtendBillingPeriod(newEndDate);
                    _logger.LogInformation("Subscription {SubscriptionId} extended until {NewEndDate}",
                        request.SubscriptionId, newEndDate);
                }
                else if (subscription.Status == SubscriptionStatus.Suspended)
                {
                    // Resume suspended subscription and extend
                    subscription.Resume();
                    var newEndDate = DateTime.UtcNow.Add(extensionPeriod);
                    subscription.ExtendBillingPeriod(newEndDate);
                    _logger.LogInformation("Subscription {SubscriptionId} resumed and extended until {NewEndDate}",
                        request.SubscriptionId, newEndDate);
                }

                // Add payment to subscription
                subscription.AddPayment(payment);

                // Record the activation/extension as a subscription change
                subscription.RecordChange("Payment Verified",
                    $"Offline payment of {paymentAmount.Amount} {paymentAmount.Currency} verified by admin. " +
                    $"Payment Proof ID: {request.PaymentProofId}");

                // Save subscription changes
                await _subscriptionRepository.UpdateAsync(subscription);

                // Generate invoice/receipt for the verified payment
                try
                {
                    var invoiceReference = await _invoiceService.GenerateInvoiceForVerifiedPaymentAsync(
                        subscription, payment, request.PaymentProofId);

                    var receiptReference = await _invoiceService.GenerateReceiptForVerifiedPaymentAsync(
                        subscription, payment, request.PaymentProofId);

                    // Send invoice to user
                    await _invoiceService.SendInvoiceToUserAsync(subscription.UserId, invoiceReference);

                    _logger.LogInformation("Invoice {InvoiceReference} and receipt {ReceiptReference} generated for subscription {SubscriptionId}",
                        invoiceReference, receiptReference, request.SubscriptionId);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to generate invoice/receipt for subscription {SubscriptionId}, but subscription was activated successfully",
                        request.SubscriptionId);
                }

                _logger.LogInformation("Subscription {SubscriptionId} successfully processed from payment proof {PaymentProofId}. " +
                    "Payment amount: {Amount} {Currency}, Extension period: {ExtensionDays} days",
                    request.SubscriptionId, request.PaymentProofId, request.PaymentAmount, request.Currency, extensionPeriod.TotalDays);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to activate subscription {SubscriptionId} from payment proof {PaymentProofId}",
                    request.SubscriptionId, request.PaymentProofId);
                throw;
            }
        }

        private TimeSpan CalculateExtensionPeriod(Money paymentAmount, Money planPrice, BillingCycle billingCycle)
        {
            // Calculate how many billing cycles the payment covers
            var cyclesCount = Math.Floor(paymentAmount.Amount / planPrice.Amount);

            // If payment doesn't cover even one cycle, provide a proportional extension
            if (cyclesCount < 1)
            {
                var proportion = paymentAmount.Amount / planPrice.Amount;
                var basePeriod = GetBillingCycleDays(billingCycle);
                return TimeSpan.FromDays(basePeriod * proportion);
            }

            // Calculate full extension period
            var totalDays = cyclesCount * GetBillingCycleDays(billingCycle);
            return TimeSpan.FromDays(totalDays);
        }

        private double GetBillingCycleDays(BillingCycle billingCycle)
        {
            return billingCycle.Interval switch
            {
                BillingInterval.Weekly => 7,
                BillingInterval.BiWeekly => 14,
                BillingInterval.Monthly => 30,
                BillingInterval.Quarterly => 90,
                BillingInterval.SemiAnnually => 180,
                BillingInterval.Annually => 365,
                BillingInterval.Custom => billingCycle.IntervalCount * 30, // Assume custom is in months
                _ => 30 // Default to monthly
            };
        }
    }
}
