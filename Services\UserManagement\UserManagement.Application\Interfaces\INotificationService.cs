namespace UserManagement.Application.Interfaces;

public interface INotificationService
{
    Task SendNotificationAsync(Guid userId, string title, string message);
    Task SendEmailAsync(string email, string subject, string body);
    Task SendSmsAsync(string phoneNumber, string message);
    Task SendKycReminderAsync(Guid userId, string userType, string displayName, string email);
    Task SendKycUploadPromptAsync(Guid userId, string userType, string displayName, string email);
    Task SendLanguageChangeNotificationAsync(Guid userId, string email, string userName, string newLanguage, string oldLanguage);
    Task SendAccountActionNotificationAsync(Guid userId, string userType, string displayName, string email, string notificationType, string actionResult, string reason, DateTime actionDate);
    Task SendKycStatusNotificationAsync(Guid userId, string userType, string displayName, string email, string notificationType, string decisionMessage, DateTime processedDate);
}
