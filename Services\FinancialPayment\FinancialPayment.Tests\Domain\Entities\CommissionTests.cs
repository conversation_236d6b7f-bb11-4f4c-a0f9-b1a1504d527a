using FluentAssertions;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;
using FinancialPayment.Domain.Events;
using Xunit;

namespace FinancialPayment.Tests.Domain.Entities;

public class CommissionTests
{
    private readonly Guid _orderId = Guid.NewGuid();
    private readonly Guid _brokerId = Guid.NewGuid();
    private readonly Guid _transportCompanyId = Guid.NewGuid();
    private readonly Guid _carrierId = Guid.NewGuid();
    private readonly Money _orderAmount = new(10000m, "INR");

    [Fact]
    public void Constructor_ShouldCreateCommission_WithPercentageStructure()
    {
        // Arrange
        var commissionStructure = CommissionStructure.Percentage(5m, 100m, 1000m, "5% commission");

        // Act
        var commission = new Commission(
            _orderId,
            _brokerId,
            _transportCompanyId,
            _carrierId,
            _orderAmount,
            commissionStructure,
            "Test commission");

        // Assert
        commission.OrderId.Should().Be(_orderId);
        commission.BrokerId.Should().Be(_brokerId);
        commission.TransportCompanyId.Should().Be(_transportCompanyId);
        commission.CarrierId.Should().Be(_carrierId);
        commission.OrderAmount.Should().Be(_orderAmount);
        commission.CommissionStructure.Should().Be(commissionStructure);
        commission.CalculatedAmount.Should().Be(new Money(500m, "INR")); // 5% of 10000
        commission.Status.Should().Be(CommissionStatus.Calculated);
        commission.Notes.Should().Be("Test commission");
        commission.Adjustments.Should().BeEmpty();
    }

    [Fact]
    public void Constructor_ShouldCreateCommission_WithFixedAmountStructure()
    {
        // Arrange
        var commissionStructure = CommissionStructure.FixedAmount(300m, "Fixed commission");

        // Act
        var commission = new Commission(
            _orderId,
            _brokerId,
            _transportCompanyId,
            _carrierId,
            _orderAmount,
            commissionStructure);

        // Assert
        commission.CalculatedAmount.Should().Be(new Money(300m, "INR"));
        commission.Status.Should().Be(CommissionStatus.Calculated);
    }

    [Fact]
    public void Constructor_ShouldApplyMinimumAmount_WhenCalculatedAmountIsBelowMinimum()
    {
        // Arrange
        var smallOrderAmount = new Money(1000m, "INR");
        var commissionStructure = CommissionStructure.Percentage(2m, 100m, 1000m, "2% with minimum");

        // Act
        var commission = new Commission(
            _orderId,
            _brokerId,
            _transportCompanyId,
            _carrierId,
            smallOrderAmount,
            commissionStructure);

        // Assert
        commission.CalculatedAmount.Should().Be(new Money(100m, "INR")); // Minimum applied instead of 20 (2% of 1000)
    }

    [Fact]
    public void Constructor_ShouldApplyMaximumAmount_WhenCalculatedAmountExceedsMaximum()
    {
        // Arrange
        var largeOrderAmount = new Money(100000m, "INR");
        var commissionStructure = CommissionStructure.Percentage(5m, 100m, 1000m, "5% with maximum");

        // Act
        var commission = new Commission(
            _orderId,
            _brokerId,
            _transportCompanyId,
            _carrierId,
            largeOrderAmount,
            commissionStructure);

        // Assert
        commission.CalculatedAmount.Should().Be(new Money(1000m, "INR")); // Maximum applied instead of 5000 (5% of 100000)
    }

    [Fact]
    public void Constructor_ShouldThrowException_WhenOrderAmountIsZero()
    {
        // Arrange
        var zeroAmount = Money.Zero("INR");
        var commissionStructure = CommissionStructure.Percentage(5m);

        // Act & Assert
        var action = () => new Commission(
            _orderId,
            _brokerId,
            _transportCompanyId,
            _carrierId,
            zeroAmount,
            commissionStructure);

        action.Should().Throw<ArgumentException>()
            .WithMessage("Order amount must be greater than zero");
    }

    [Fact]
    public void Constructor_ShouldThrowException_WhenCommissionStructureIsNull()
    {
        // Act & Assert
        var action = () => new Commission(
            _orderId,
            _brokerId,
            _transportCompanyId,
            _carrierId,
            _orderAmount,
            null!);

        action.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void AddAdjustment_ShouldIncreaseCalculatedAmount_WhenAdjustmentTypeIsIncrease()
    {
        // Arrange
        var commission = CreateCommission();
        var adjustmentAmount = new Money(100m, "INR");

        // Act
        commission.AddAdjustment(adjustmentAmount, CommissionAdjustmentType.Increase, "Performance bonus");

        // Assert
        commission.CalculatedAmount.Should().Be(new Money(600m, "INR")); // 500 + 100
        commission.Adjustments.Should().HaveCount(1);
        commission.Adjustments.First().Amount.Should().Be(adjustmentAmount);
        commission.Adjustments.First().Type.Should().Be(CommissionAdjustmentType.Increase);
        commission.Adjustments.First().Reason.Should().Be("Performance bonus");
    }

    [Fact]
    public void AddAdjustment_ShouldDecreaseCalculatedAmount_WhenAdjustmentTypeIsDecrease()
    {
        // Arrange
        var commission = CreateCommission();
        var adjustmentAmount = new Money(50m, "INR");

        // Act
        commission.AddAdjustment(adjustmentAmount, CommissionAdjustmentType.Decrease, "Late delivery penalty");

        // Assert
        commission.CalculatedAmount.Should().Be(new Money(450m, "INR")); // 500 - 50
        commission.Adjustments.Should().HaveCount(1);
        commission.Adjustments.First().Type.Should().Be(CommissionAdjustmentType.Decrease);
    }

    [Fact]
    public void AddAdjustment_ShouldNotAllowNegativeAmount_WhenAdjustmentExceedsOriginal()
    {
        // Arrange
        var commission = CreateCommission();
        var largeAdjustment = new Money(600m, "INR"); // More than the original 500

        // Act
        commission.AddAdjustment(largeAdjustment, CommissionAdjustmentType.Decrease, "Large penalty");

        // Assert
        commission.CalculatedAmount.Should().Be(Money.Zero("INR")); // Should not go negative
    }

    [Fact]
    public void AddAdjustment_ShouldThrowException_WhenCommissionIsPaid()
    {
        // Arrange
        var commission = CreateCommission();
        commission.Approve("admin");
        commission.Pay("txn_123");
        var adjustmentAmount = new Money(100m, "INR");

        // Act & Assert
        var action = () => commission.AddAdjustment(adjustmentAmount, CommissionAdjustmentType.Increase, "Late adjustment");

        action.Should().Throw<InvalidOperationException>()
            .WithMessage("Cannot adjust paid commissions");
    }

    [Fact]
    public void Approve_ShouldChangeStatusToApproved()
    {
        // Arrange
        var commission = CreateCommission();
        var approvedBy = "admin_user";

        // Act
        commission.Approve(approvedBy);

        // Assert
        commission.Status.Should().Be(CommissionStatus.Approved);
        commission.Notes.Should().Be($"Approved by: {approvedBy}");
    }

    [Fact]
    public void Approve_ShouldThrowException_WhenNotCalculated()
    {
        // Arrange
        var commission = CreateCommission();
        commission.Approve("admin");
        var approvedBy = "another_admin";

        // Act & Assert
        var action = () => commission.Approve(approvedBy);

        action.Should().Throw<InvalidOperationException>()
            .WithMessage("Only calculated commissions can be approved");
    }

    [Fact]
    public void Pay_ShouldChangeStatusToPaid()
    {
        // Arrange
        var commission = CreateCommission();
        commission.Approve("admin");
        var transactionId = "txn_123456";

        // Act
        commission.Pay(transactionId);

        // Assert
        commission.Status.Should().Be(CommissionStatus.Paid);
        commission.ProcessedAt.Should().NotBeNull();
        commission.ProcessedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        commission.Notes.Should().Be($"Paid via transaction: {transactionId}");
    }

    [Fact]
    public void Pay_ShouldThrowException_WhenNotApproved()
    {
        // Arrange
        var commission = CreateCommission();
        var transactionId = "txn_123456";

        // Act & Assert
        var action = () => commission.Pay(transactionId);

        action.Should().Throw<InvalidOperationException>()
            .WithMessage("Only approved commissions can be paid");
    }

    [Fact]
    public void Dispute_ShouldChangeStatusToDisputed()
    {
        // Arrange
        var commission = CreateCommission();
        var disputeReason = "Incorrect calculation";

        // Act
        commission.Dispute(disputeReason);

        // Assert
        commission.Status.Should().Be(CommissionStatus.Disputed);
        commission.Notes.Should().Be($"Disputed: {disputeReason}");
    }

    [Fact]
    public void Dispute_ShouldThrowException_WhenCommissionIsPaid()
    {
        // Arrange
        var commission = CreateCommission();
        commission.Approve("admin");
        commission.Pay("txn_123");
        var disputeReason = "Late dispute";

        // Act & Assert
        var action = () => commission.Dispute(disputeReason);

        action.Should().Throw<InvalidOperationException>()
            .WithMessage("Cannot dispute paid commissions");
    }

    [Fact]
    public void ResolveDispute_ShouldChangeStatusToApproved_WithResolvedAmount()
    {
        // Arrange
        var commission = CreateCommission();
        commission.Dispute("Calculation error");
        var resolvedAmount = new Money(450m, "INR");
        var resolution = "Recalculated with correct rates";

        // Act
        commission.ResolveDispute(resolvedAmount, resolution);

        // Assert
        commission.Status.Should().Be(CommissionStatus.Approved);
        commission.CalculatedAmount.Should().Be(resolvedAmount);
        commission.Notes.Should().Be($"Dispute resolved: {resolution}");
    }

    [Fact]
    public void ResolveDispute_ShouldThrowException_WhenNotDisputed()
    {
        // Arrange
        var commission = CreateCommission();
        var resolvedAmount = new Money(450m, "INR");
        var resolution = "Resolution attempt";

        // Act & Assert
        var action = () => commission.ResolveDispute(resolvedAmount, resolution);

        action.Should().Throw<InvalidOperationException>()
            .WithMessage("Only disputed commissions can be resolved");
    }

    [Fact]
    public void DomainEvents_ShouldBeRaised_WhenCommissionIsCalculated()
    {
        // Act
        var commission = CreateCommission();

        // Assert
        commission.DomainEvents.Should().HaveCount(1);
        commission.DomainEvents.First().Should().BeOfType<CommissionCalculatedEvent>();

        var calculatedEvent = (CommissionCalculatedEvent)commission.DomainEvents.First();
        calculatedEvent.CommissionId.Should().Be(commission.Id);
        calculatedEvent.OrderId.Should().Be(_orderId);
        calculatedEvent.BrokerId.Should().Be(_brokerId);
        calculatedEvent.Amount.Should().Be(commission.CalculatedAmount);
    }

    [Fact]
    public void DomainEvents_ShouldBeRaised_WhenCommissionIsApproved()
    {
        // Arrange
        var commission = CreateCommission();
        commission.ClearDomainEvents(); // Clear calculation event

        // Act
        commission.Approve("admin");

        // Assert
        commission.DomainEvents.Should().HaveCount(1);
        commission.DomainEvents.First().Should().BeOfType<CommissionApprovedEvent>();
    }

    [Fact]
    public void DomainEvents_ShouldBeRaised_WhenCommissionIsPaid()
    {
        // Arrange
        var commission = CreateCommission();
        commission.Approve("admin");
        commission.ClearDomainEvents(); // Clear previous events
        var transactionId = "txn_123";

        // Act
        commission.Pay(transactionId);

        // Assert
        commission.DomainEvents.Should().HaveCount(1);
        commission.DomainEvents.First().Should().BeOfType<CommissionPaidEvent>();

        var paidEvent = (CommissionPaidEvent)commission.DomainEvents.First();
        paidEvent.PaymentGatewayTransactionId.Should().Be(transactionId);
    }

    private Commission CreateCommission()
    {
        var commissionStructure = CommissionStructure.Percentage(5m, 100m, 1000m, "5% commission");
        return new Commission(
            _orderId,
            _brokerId,
            _transportCompanyId,
            _carrierId,
            _orderAmount,
            commissionStructure,
            "Test commission");
    }
}
