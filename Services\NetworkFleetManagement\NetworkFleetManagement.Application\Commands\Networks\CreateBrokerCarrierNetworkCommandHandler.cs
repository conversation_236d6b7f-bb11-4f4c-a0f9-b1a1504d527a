using AutoMapper;
using MediatR;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Entities;
using NetworkFleetManagement.Domain.Repositories;
using Shared.Domain.Common;
using Shared.Messaging;

namespace NetworkFleetManagement.Application.Commands.Networks;

public class CreateBrokerCarrierNetworkCommandHandler : IRequestHandler<CreateBrokerCarrierNetworkCommand, BrokerCarrierNetworkDto>
{
    private readonly IBrokerCarrierNetworkRepository _networkRepository;
    private readonly ICarrierRepository _carrierRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly IMessageBroker _messageBroker;

    public CreateBrokerCarrierNetworkCommandHandler(
        IBrokerCarrierNetworkRepository networkRepository,
        ICarrierRepository carrierRepository,
        IUnitOfWork unitOfWork,
        I<PERSON><PERSON><PERSON> mapper,
        IMessageBroker messageBroker)
    {
        _networkRepository = networkRepository;
        _carrierRepository = carrierRepository;
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _messageBroker = messageBroker;
    }

    public async Task<BrokerCarrierNetworkDto> Handle(CreateBrokerCarrierNetworkCommand request, CancellationToken cancellationToken)
    {
        var dto = request.NetworkDto;

        // Check if carrier exists
        var carrier = await _carrierRepository.GetByIdAsync(dto.CarrierId, cancellationToken);
        if (carrier == null)
        {
            throw new InvalidOperationException($"Carrier with ID {dto.CarrierId} not found");
        }

        // Check if network relationship already exists
        var existingNetwork = await _networkRepository.GetByBrokerAndCarrierAsync(dto.BrokerId, dto.CarrierId, cancellationToken);
        if (existingNetwork != null)
        {
            throw new InvalidOperationException($"Network relationship already exists between broker {dto.BrokerId} and carrier {dto.CarrierId}");
        }

        // Create network entity
        var network = new BrokerCarrierNetwork(
            dto.BrokerId,
            dto.CarrierId,
            dto.PreferredRate,
            dto.Priority,
            dto.IsExclusive,
            dto.ExclusivityExpiresAt,
            dto.MinimumRate,
            dto.MaximumRate,
            dto.PaymentTermsDays,
            dto.ContractNumber,
            dto.ContractStartDate,
            dto.ContractEndDate,
            dto.Notes);

        // Add to repository
        _networkRepository.Add(network);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        // Publish integration event
        await _messageBroker.PublishAsync("network.relationship_established", new
        {
            NetworkId = network.Id,
            BrokerId = network.BrokerId,
            CarrierId = network.CarrierId,
            CarrierCompanyName = carrier.CompanyName,
            Status = network.Status.ToString(),
            Priority = network.Priority,
            IsExclusive = network.IsExclusive,
            EstablishedAt = network.EstablishedAt
        }, cancellationToken);

        // Map to DTO and return
        var result = _mapper.Map<BrokerCarrierNetworkDto>(network);
        return result;
    }
}

