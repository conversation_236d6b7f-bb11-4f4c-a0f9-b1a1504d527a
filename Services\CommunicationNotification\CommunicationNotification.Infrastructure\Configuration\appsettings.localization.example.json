{"Localization": {"DefaultLanguage": "English", "SupportedLanguages": ["English", "Hindi", "Kannada"], "FallbackLanguage": "English", "CacheExpirationHours": 1, "EnableAutoTranslation": true, "EnableTranslationCache": true, "TranslationProvider": "GoogleTranslate", "TemplateSettings": {"MaxTemplateSize": 10000, "AllowedPlaceholderFormats": ["{{key}}", "{key}", "[key]", "%key%"], "ValidateParametersOnRender": true, "CacheTemplates": true}}, "GoogleTranslate": {"ApiKey": "your-google-translate-api-key", "ProjectId": "your-google-cloud-project-id", "BaseUrl": "https://translation.googleapis.com/language/translate/v2", "MaxTextLength": 5000, "RequestTimeoutSeconds": 30, "RateLimitPerMinute": 100}, "AzureTranslator": {"SubscriptionKey": "your-azure-translator-key", "Region": "your-azure-region", "BaseUrl": "https://api.cognitive.microsofttranslator.com", "MaxTextLength": 10000, "RequestTimeoutSeconds": 30, "RateLimitPerMinute": 200}, "TemplateCategories": {"notification": {"Name": "Notifications", "Description": "General notification templates", "Templates": ["welcome_notification", "account_created", "password_reset", "email_verification"]}, "trip": {"Name": "Trip Management", "Description": "Trip-related notification templates", "Templates": ["trip_started", "trip_completed", "trip_cancelled", "trip_delayed", "driver_assigned", "driver_arrived", "trip_payment_due"]}, "order": {"Name": "Order Management", "Description": "Order-related notification templates", "Templates": ["order_confirmation", "order_shipped", "order_delivered", "order_cancelled", "payment_received", "invoice_generated"]}, "driver": {"Name": "Driver Communications", "Description": "Driver-specific notification templates", "Templates": ["new_trip_assignment", "trip_instructions", "payment_processed", "document_required", "vehicle_inspection_due"]}, "customer": {"Name": "Customer Communications", "Description": "Customer-specific notification templates", "Templates": ["booking_confirmation", "driver_details", "trip_tracking", "feedback_request", "loyalty_points_earned"]}, "broker": {"Name": "Broker Communications", "Description": "Broker-specific notification templates", "Templates": ["rfq_received", "quote_submitted", "carrier_assigned", "commission_earned", "monthly_report"]}}, "LanguageSettings": {"English": {"Code": "en", "Culture": "en-US", "DisplayName": "English", "NativeName": "English", "Direction": "ltr", "DateFormat": "MM/dd/yyyy", "TimeFormat": "HH:mm", "CurrencySymbol": "₹", "NumberFormat": "en-US"}, "Hindi": {"Code": "hi", "Culture": "hi-IN", "DisplayName": "Hindi", "NativeName": "हिन्दी", "Direction": "ltr", "DateFormat": "dd/MM/yyyy", "TimeFormat": "HH:mm", "CurrencySymbol": "₹", "NumberFormat": "hi-IN"}, "Kannada": {"Code": "kn", "Culture": "kn-IN", "DisplayName": "Kannada", "NativeName": "ಕನ್ನಡ", "Direction": "ltr", "DateFormat": "dd/MM/yyyy", "TimeFormat": "HH:mm", "CurrencySymbol": "₹", "NumberFormat": "kn-IN"}}, "TranslationQuality": {"MinimumScore": 0.7, "EnableQualityCheck": true, "FallbackOnLowQuality": true, "LogQualityMetrics": true}, "ContentValidation": {"MaxTextLength": 5000, "AllowedHtmlTags": ["b", "i", "u", "br", "p", "strong", "em"], "ValidateUrls": true, "ValidatePhoneNumbers": true, "ValidateEmails": true}, "Caching": {"EnableDistributedCache": false, "RedisConnectionString": "localhost:6379", "CacheKeyPrefix": "TLI-Localization", "DefaultExpirationMinutes": 60, "SlidingExpirationMinutes": 30}, "Logging": {"LogLevel": {"CommunicationNotification.Infrastructure.Services.LocalizationService": "Debug", "CommunicationNotification.Infrastructure.Services.TemplateService": "Debug", "CommunicationNotification.Infrastructure.Services.TranslationService": "Information"}}}