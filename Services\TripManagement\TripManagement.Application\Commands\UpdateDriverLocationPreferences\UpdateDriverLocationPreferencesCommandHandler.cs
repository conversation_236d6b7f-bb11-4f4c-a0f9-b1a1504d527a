using MediatR;
using Microsoft.Extensions.Logging;
using TripManagement.Domain.Entities;
using TripManagement.Domain.Repositories;
using TLI.Shared.Infrastructure.Messaging;

namespace TripManagement.Application.Commands.UpdateDriverLocationPreferences;

public class UpdateDriverLocationPreferencesCommandHandler : IRequestHandler<UpdateDriverLocationPreferencesCommand, UpdateDriverLocationPreferencesResponse>
{
    private readonly IDriverRepository _driverRepository;
    private readonly IDriverLocationPreferencesRepository _preferencesRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<UpdateDriverLocationPreferencesCommandHandler> _logger;

    public UpdateDriverLocationPreferencesCommandHandler(
        IDriverRepository driverRepository,
        IDriverLocationPreferencesRepository preferencesRepository,
        IUnitOfWork unitOfWork,
        IMessageBroker messageBroker,
        ILogger<UpdateDriverLocationPreferencesCommandHandler> logger)
    {
        _driverRepository = driverRepository;
        _preferencesRepository = preferencesRepository;
        _unitOfWork = unitOfWork;
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task<UpdateDriverLocationPreferencesResponse> Handle(UpdateDriverLocationPreferencesCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Updating location preferences for driver {DriverId}", request.DriverId);

            // Validate driver exists
            var driver = await _driverRepository.GetByIdAsync(request.DriverId, cancellationToken);
            if (driver == null)
            {
                return new UpdateDriverLocationPreferencesResponse
                {
                    IsSuccess = false,
                    ErrorMessage = "Driver not found"
                };
            }

            // Get existing preferences or create new
            var preferences = await _preferencesRepository.GetByDriverIdAsync(request.DriverId, cancellationToken);
            
            if (preferences == null)
            {
                // Create new preferences
                preferences = new DriverLocationPreferencesEntity(
                    request.DriverId,
                    request.LocationSharingEnabled,
                    request.ManualToggleAllowed,
                    request.LocationUpdateIntervalSeconds,
                    request.ShowGpsTimestamp,
                    request.HighAccuracyMode,
                    request.BackgroundLocationEnabled,
                    request.GeofenceAlertsEnabled,
                    request.LocationAccuracyThresholdMeters,
                    request.AdditionalSettings);

                await _preferencesRepository.AddAsync(preferences, cancellationToken);
            }
            else
            {
                // Update existing preferences
                preferences.UpdatePreferences(
                    request.LocationSharingEnabled,
                    request.ManualToggleAllowed,
                    request.LocationUpdateIntervalSeconds,
                    request.ShowGpsTimestamp,
                    request.HighAccuracyMode,
                    request.BackgroundLocationEnabled,
                    request.GeofenceAlertsEnabled,
                    request.LocationAccuracyThresholdMeters,
                    request.AdditionalSettings);

                _preferencesRepository.Update(preferences);
            }

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Publish integration event to notify other services
            await _messageBroker.PublishAsync("driver.location_preferences.updated", new
            {
                DriverId = request.DriverId,
                LocationSharingEnabled = request.LocationSharingEnabled,
                ManualToggleAllowed = request.ManualToggleAllowed,
                LocationUpdateIntervalSeconds = request.LocationUpdateIntervalSeconds,
                ShowGpsTimestamp = request.ShowGpsTimestamp,
                HighAccuracyMode = request.HighAccuracyMode,
                BackgroundLocationEnabled = request.BackgroundLocationEnabled,
                GeofenceAlertsEnabled = request.GeofenceAlertsEnabled,
                LocationAccuracyThresholdMeters = request.LocationAccuracyThresholdMeters,
                UpdatedAt = DateTime.UtcNow
            }, cancellationToken);

            // Map to response DTO
            var responsePreferences = new DriverLocationPreferences
            {
                DriverId = preferences.DriverId,
                LocationSharingEnabled = preferences.LocationSharingEnabled,
                ManualToggleAllowed = preferences.ManualToggleAllowed,
                LocationUpdateIntervalSeconds = preferences.LocationUpdateIntervalSeconds,
                ShowGpsTimestamp = preferences.ShowGpsTimestamp,
                HighAccuracyMode = preferences.HighAccuracyMode,
                BackgroundLocationEnabled = preferences.BackgroundLocationEnabled,
                GeofenceAlertsEnabled = preferences.GeofenceAlertsEnabled,
                LocationAccuracyThresholdMeters = preferences.LocationAccuracyThresholdMeters,
                LastLocationUpdate = preferences.LastLocationUpdate,
                LastKnownLocation = preferences.LastKnownLocation,
                LastLatitude = preferences.LastLatitude,
                LastLongitude = preferences.LastLongitude,
                LastAccuracy = preferences.LastAccuracy,
                LastGpsTimestamp = preferences.LastGpsTimestamp,
                AdditionalSettings = preferences.AdditionalSettings,
                CreatedAt = preferences.CreatedAt,
                UpdatedAt = preferences.UpdatedAt
            };

            _logger.LogInformation("Location preferences updated successfully for driver {DriverId}", request.DriverId);

            return new UpdateDriverLocationPreferencesResponse
            {
                IsSuccess = true,
                UpdatedPreferences = responsePreferences,
                UpdatedAt = preferences.UpdatedAt
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating location preferences for driver {DriverId}", request.DriverId);
            return new UpdateDriverLocationPreferencesResponse
            {
                IsSuccess = false,
                ErrorMessage = ex.Message,
                UpdatedAt = DateTime.UtcNow
            };
        }
    }
}
