using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using System.Security.Claims;

namespace UserManagement.API.Hubs
{
    [Authorize]
    public class UserManagementHub : Hub<IUserManagementClient>
    {
        private readonly ILogger<UserManagementHub> _logger;

        public UserManagementHub(ILogger<UserManagementHub> logger)
        {
            _logger = logger;
        }

        public override async Task OnConnectedAsync()
        {
            var userId = GetCurrentUserId();
            var userRole = GetCurrentUserRole();
            
            _logger.LogInformation("User connected to UserManagement hub: {UserId}, Role: {Role}", userId, userRole);

            // Join user-specific group
            await Groups.AddToGroupAsync(Context.ConnectionId, $"User_{userId}");

            // Join role-specific groups
            if (userRole == "Admin")
            {
                await Groups.AddToGroupAsync(Context.ConnectionId, "Admins");
            }

            await base.OnConnectedAsync();
        }

        public override async Task OnDisconnectedAsync(Exception? exception)
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("User disconnected from UserManagement hub: {UserId}", userId);

            await base.OnDisconnectedAsync(exception);
        }

        public async Task JoinAdminGroup()
        {
            if (IsAdmin())
            {
                await Groups.AddToGroupAsync(Context.ConnectionId, "Admins");
                _logger.LogInformation("User {UserId} joined admin group", GetCurrentUserId());
            }
        }

        public async Task LeaveAdminGroup()
        {
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, "Admins");
            _logger.LogInformation("User {UserId} left admin group", GetCurrentUserId());
        }

        private Guid GetCurrentUserId()
        {
            var userIdClaim = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out var userId))
            {
                throw new UnauthorizedAccessException("User ID not found in token");
            }
            return userId;
        }

        private string GetCurrentUserRole()
        {
            return Context.User?.FindFirst(ClaimTypes.Role)?.Value ?? string.Empty;
        }

        private bool IsAdmin()
        {
            return Context.User?.IsInRole("Admin") ?? false;
        }
    }

    public interface IUserManagementClient
    {
        Task UserApproved(UserApprovedNotification notification);
        Task UserRejected(UserRejectedNotification notification);
        Task DocumentsApproved(DocumentsApprovedNotification notification);
        Task DocumentsRejected(DocumentsRejectedNotification notification);
        Task DocumentOcrProcessed(DocumentOcrProcessedNotification notification);
        Task ProfileStatusChanged(ProfileStatusChangedNotification notification);
        Task NewUserRegistered(NewUserRegisteredNotification notification);
        Task DocumentSubmitted(DocumentSubmittedNotification notification);
        Task SystemNotification(SystemNotification notification);
    }

    // Notification Models
    public class UserApprovedNotification
    {
        public Guid UserId { get; set; }
        public string UserType { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public Guid ApprovedBy { get; set; }
        public DateTime ApprovedAt { get; set; }
        public string Notes { get; set; } = string.Empty;
    }

    public class UserRejectedNotification
    {
        public Guid UserId { get; set; }
        public string UserType { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public Guid RejectedBy { get; set; }
        public DateTime RejectedAt { get; set; }
        public string Reason { get; set; } = string.Empty;
        public string Notes { get; set; } = string.Empty;
    }

    public class DocumentsApprovedNotification
    {
        public Guid SubmissionId { get; set; }
        public Guid UserId { get; set; }
        public string UserType { get; set; } = string.Empty;
        public List<string> ApprovedDocuments { get; set; } = new();
        public Guid ApprovedBy { get; set; }
        public DateTime ProcessedAt { get; set; }
        public string Notes { get; set; } = string.Empty;
    }

    public class DocumentsRejectedNotification
    {
        public Guid SubmissionId { get; set; }
        public Guid UserId { get; set; }
        public string UserType { get; set; } = string.Empty;
        public List<DocumentRejectionInfo> RejectedDocuments { get; set; } = new();
        public Guid RejectedBy { get; set; }
        public DateTime ProcessedAt { get; set; }
        public string Notes { get; set; } = string.Empty;
    }

    public class DocumentRejectionInfo
    {
        public string DocumentType { get; set; } = string.Empty;
        public string Action { get; set; } = string.Empty;
        public string Reason { get; set; } = string.Empty;
    }

    public class DocumentOcrProcessedNotification
    {
        public Guid SubmissionId { get; set; }
        public Guid UserId { get; set; }
        public string DocumentType { get; set; } = string.Empty;
        public decimal ConfidenceScore { get; set; }
        public bool RequiresManualReview { get; set; }
        public bool AutoApproved { get; set; }
        public List<string> ValidationErrors { get; set; } = new();
        public DateTime ProcessedAt { get; set; }
    }

    public class ProfileStatusChangedNotification
    {
        public Guid UserId { get; set; }
        public string UserType { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string PreviousStatus { get; set; } = string.Empty;
        public string NewStatus { get; set; } = string.Empty;
        public DateTime ChangedAt { get; set; }
        public string Reason { get; set; } = string.Empty;
    }

    public class NewUserRegisteredNotification
    {
        public Guid UserId { get; set; }
        public string UserType { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public DateTime RegisteredAt { get; set; }
    }

    public class DocumentSubmittedNotification
    {
        public Guid SubmissionId { get; set; }
        public Guid UserId { get; set; }
        public string UserType { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public int DocumentCount { get; set; }
        public DateTime SubmittedAt { get; set; }
    }

    public class SystemNotification
    {
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string Type { get; set; } = "info"; // info, warning, error, success
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public Dictionary<string, object> Data { get; set; } = new();
    }
}
