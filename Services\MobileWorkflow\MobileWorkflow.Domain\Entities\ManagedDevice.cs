using Shared.Domain.Common;
using MobileWorkflow.Domain.Events;

namespace MobileWorkflow.Domain.Entities;

public class ManagedDevice : AggregateRoot
{
    public Guid UserId { get; private set; }
    public string DeviceId { get; private set; } // Unique device identifier
    public string DeviceName { get; private set; }
    public string Platform { get; private set; } // iOS, Android, Windows, macOS
    public string PlatformVersion { get; private set; }
    public string AppVersion { get; private set; }
    public string Status { get; private set; } // Active, Inactive, Suspended, Compromised, Lost
    public bool IsManaged { get; private set; }
    public bool IsCompliant { get; private set; }
    public DateTime LastSeen { get; private set; }
    public DateTime EnrolledAt { get; private set; }
    public DateTime? LastPolicyUpdate { get; private set; }
    public Dictionary<string, object> DeviceInfo { get; private set; }
    public Dictionary<string, object> SecurityInfo { get; private set; }
    public Dictionary<string, object> ComplianceStatus { get; private set; }
    public List<Guid> AssignedPolicyIds { get; private set; }
    public Dictionary<string, object> Configuration { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    public virtual ICollection<DevicePolicyAssignment> PolicyAssignments { get; private set; } = new List<DevicePolicyAssignment>();
    public virtual ICollection<DeviceComplianceCheck> ComplianceChecks { get; private set; } = new List<DeviceComplianceCheck>();
    public virtual ICollection<DeviceCommand> Commands { get; private set; } = new List<DeviceCommand>();

    private ManagedDevice() { } // EF Core

    public ManagedDevice(
        Guid userId,
        string deviceId,
        string deviceName,
        string platform,
        string platformVersion,
        string appVersion,
        Dictionary<string, object> deviceInfo)
    {
        UserId = userId;
        DeviceId = deviceId ?? throw new ArgumentNullException(nameof(deviceId));
        DeviceName = deviceName ?? throw new ArgumentNullException(nameof(deviceName));
        Platform = platform ?? throw new ArgumentNullException(nameof(platform));
        PlatformVersion = platformVersion ?? throw new ArgumentNullException(nameof(platformVersion));
        AppVersion = appVersion ?? throw new ArgumentNullException(nameof(appVersion));
        DeviceInfo = deviceInfo ?? throw new ArgumentNullException(nameof(deviceInfo));

        Status = "Active";
        IsManaged = true;
        IsCompliant = false; // Will be determined after first compliance check
        EnrolledAt = DateTime.UtcNow;
        LastSeen = DateTime.UtcNow;
        SecurityInfo = new Dictionary<string, object>();
        ComplianceStatus = new Dictionary<string, object>();
        AssignedPolicyIds = new List<Guid>();
        Configuration = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();

        SetDefaultConfiguration();

        AddDomainEvent(new ManagedDeviceEnrolledEvent(Id, UserId, DeviceId, Platform));
    }

    public void UpdateDeviceInfo(Dictionary<string, object> deviceInfo, string appVersion)
    {
        DeviceInfo = deviceInfo ?? throw new ArgumentNullException(nameof(deviceInfo));
        AppVersion = appVersion ?? throw new ArgumentNullException(nameof(appVersion));
        LastSeen = DateTime.UtcNow;

        AddDomainEvent(new ManagedDeviceInfoUpdatedEvent(Id, UserId, DeviceId));
    }

    public void UpdateSecurityInfo(Dictionary<string, object> securityInfo)
    {
        SecurityInfo = securityInfo ?? throw new ArgumentNullException(nameof(securityInfo));
        LastSeen = DateTime.UtcNow;

        AddDomainEvent(new ManagedDeviceSecurityInfoUpdatedEvent(Id, UserId, DeviceId));
    }

    public void UpdateComplianceStatus(Dictionary<string, object> complianceStatus, bool isCompliant)
    {
        ComplianceStatus = complianceStatus ?? throw new ArgumentNullException(nameof(complianceStatus));
        var wasCompliant = IsCompliant;
        IsCompliant = isCompliant;
        LastSeen = DateTime.UtcNow;

        if (wasCompliant != isCompliant)
        {
            AddDomainEvent(new ManagedDeviceComplianceChangedEvent(Id, UserId, DeviceId, isCompliant, wasCompliant));
        }
    }

    public void AssignPolicy(Guid policyId)
    {
        if (!AssignedPolicyIds.Contains(policyId))
        {
            AssignedPolicyIds.Add(policyId);
            LastPolicyUpdate = DateTime.UtcNow;

            AddDomainEvent(new ManagedDevicePolicyAssignedEvent(Id, UserId, DeviceId, policyId));
        }
    }

    public void UnassignPolicy(Guid policyId)
    {
        if (AssignedPolicyIds.Remove(policyId))
        {
            LastPolicyUpdate = DateTime.UtcNow;

            AddDomainEvent(new ManagedDevicePolicyUnassignedEvent(Id, UserId, DeviceId, policyId));
        }
    }

    public void UpdateConfiguration(Dictionary<string, object> configuration)
    {
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        LastSeen = DateTime.UtcNow;

        AddDomainEvent(new ManagedDeviceConfigurationUpdatedEvent(Id, UserId, DeviceId));
    }

    public void Suspend(string reason)
    {
        if (Status == "Active")
        {
            Status = "Suspended";
            AddMetadata("suspension_reason", reason);
            AddMetadata("suspended_at", DateTime.UtcNow);

            AddDomainEvent(new ManagedDeviceSuspendedEvent(Id, UserId, DeviceId, reason));
        }
    }

    public void Reactivate()
    {
        if (Status == "Suspended")
        {
            Status = "Active";
            AddMetadata("reactivated_at", DateTime.UtcNow);
            Metadata.Remove("suspension_reason");

            AddDomainEvent(new ManagedDeviceReactivatedEvent(Id, UserId, DeviceId));
        }
    }

    public void MarkAsCompromised(string reason)
    {
        Status = "Compromised";
        AddMetadata("compromise_reason", reason);
        AddMetadata("compromised_at", DateTime.UtcNow);

        AddDomainEvent(new ManagedDeviceCompromisedEvent(Id, UserId, DeviceId, reason));
    }

    public void MarkAsLost()
    {
        Status = "Lost";
        AddMetadata("lost_at", DateTime.UtcNow);

        AddDomainEvent(new ManagedDeviceLostEvent(Id, UserId, DeviceId));
    }

    public void Unenroll()
    {
        IsManaged = false;
        Status = "Inactive";
        AddMetadata("unenrolled_at", DateTime.UtcNow);

        AddDomainEvent(new ManagedDeviceUnenrolledEvent(Id, UserId, DeviceId));
    }

    public void UpdateLastSeen()
    {
        LastSeen = DateTime.UtcNow;
    }

    public bool IsOnline(TimeSpan threshold)
    {
        return DateTime.UtcNow - LastSeen <= threshold;
    }

    public bool RequiresPolicyUpdate()
    {
        return !LastPolicyUpdate.HasValue ||
               DateTime.UtcNow - LastPolicyUpdate.Value > TimeSpan.FromHours(24);
    }

    public bool IsStale(TimeSpan threshold)
    {
        return DateTime.UtcNow - LastSeen > threshold;
    }

    public bool IsSecure()
    {
        return Status == "Active" && IsCompliant && IsManaged;
    }

    public string GetRiskLevel()
    {
        if (Status == "Compromised" || Status == "Lost")
            return "Critical";

        if (!IsCompliant || !IsManaged)
            return "High";

        if (IsStale(TimeSpan.FromDays(7)))
            return "Medium";

        return "Low";
    }

    public Dictionary<string, object> GetHealthStatus()
    {
        var health = new Dictionary<string, object>
        {
            ["status"] = Status,
            ["isManaged"] = IsManaged,
            ["isCompliant"] = IsCompliant,
            ["isOnline"] = IsOnline(TimeSpan.FromMinutes(15)),
            ["lastSeen"] = LastSeen,
            ["riskLevel"] = GetRiskLevel(),
            ["daysSinceEnrollment"] = (DateTime.UtcNow - EnrolledAt).Days,
            ["assignedPolicies"] = AssignedPolicyIds.Count
        };

        if (SecurityInfo.TryGetValue("jailbroken", out var jailbroken))
            health["isJailbroken"] = jailbroken;

        if (SecurityInfo.TryGetValue("rooted", out var rooted))
            health["isRooted"] = rooted;

        if (DeviceInfo.TryGetValue("battery_level", out var battery))
            health["batteryLevel"] = battery;

        if (DeviceInfo.TryGetValue("storage_available", out var storage))
            health["storageAvailable"] = storage;

        return health;
    }

    private void SetDefaultConfiguration()
    {
        Configuration["auto_update"] = true;
        Configuration["location_tracking"] = false;
        Configuration["remote_wipe_enabled"] = true;
        Configuration["screen_lock_required"] = true;
        Configuration["encryption_required"] = true;
        Configuration["backup_allowed"] = true;
        Configuration["camera_disabled"] = false;
        Configuration["app_installation_restricted"] = false;
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetDeviceInfo<T>(string key, T? defaultValue = default)
    {
        if (DeviceInfo.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetSecurityInfo<T>(string key, T? defaultValue = default)
    {
        if (SecurityInfo.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetConfiguration<T>(string key, T? defaultValue = default)
    {
        if (Configuration.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public bool HasPolicy(Guid policyId)
    {
        return AssignedPolicyIds.Contains(policyId);
    }

    public List<string> GetViolations()
    {
        var violations = new List<string>();

        if (ComplianceStatus.TryGetValue("violations", out var violationsObj) &&
            violationsObj is List<string> violationsList)
        {
            violations.AddRange(violationsList);
        }

        return violations;
    }

    public void SetDeviceName(string newName)
    {
        DeviceName = newName ?? throw new ArgumentNullException(nameof(newName));
        LastSeen = DateTime.UtcNow;
    }
}


