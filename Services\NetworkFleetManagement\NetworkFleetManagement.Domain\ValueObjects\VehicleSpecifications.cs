using Shared.Domain.ValueObjects;

namespace NetworkFleetManagement.Domain.ValueObjects;

public class VehicleSpecifications : ValueObject
{
    public decimal LoadCapacityKg { get; private set; }
    public decimal VolumeCapacityM3 { get; private set; }
    public decimal? FuelCapacityLiters { get; private set; }
    public int? MaxSpeed { get; private set; }
    public decimal? Length { get; private set; }
    public decimal? Width { get; private set; }
    public decimal? Height { get; private set; }
    public string? EngineType { get; private set; }
    public string? FuelType { get; private set; }

    private VehicleSpecifications() { }

    public VehicleSpecifications(
        decimal loadCapacityKg,
        decimal volumeCapacityM3,
        decimal? fuelCapacityLiters = null,
        int? maxSpeed = null,
        decimal? length = null,
        decimal? width = null,
        decimal? height = null,
        string? engineType = null,
        string? fuelType = null)
    {
        if (loadCapacityKg <= 0)
            throw new ArgumentException("Load capacity must be greater than zero", nameof(loadCapacityKg));

        if (volumeCapacityM3 <= 0)
            throw new ArgumentException("Volume capacity must be greater than zero", nameof(volumeCapacityM3));

        LoadCapacityKg = loadCapacityKg;
        VolumeCapacityM3 = volumeCapacityM3;
        FuelCapacityLiters = fuelCapacityLiters;
        MaxSpeed = maxSpeed;
        Length = length;
        Width = width;
        Height = height;
        EngineType = engineType;
        FuelType = fuelType;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return LoadCapacityKg;
        yield return VolumeCapacityM3;
        yield return FuelCapacityLiters ?? 0;
        yield return MaxSpeed ?? 0;
        yield return Length ?? 0;
        yield return Width ?? 0;
        yield return Height ?? 0;
        yield return EngineType ?? string.Empty;
        yield return FuelType ?? string.Empty;
    }
}
