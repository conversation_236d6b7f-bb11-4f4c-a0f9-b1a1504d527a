using FinancialPayment.Domain.Enums;
using Shared.Domain.Common;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Domain.Entities;

/// <summary>
/// Domain entity representing HSN (Harmonized System of Nomenclature) codes
/// </summary>
public class HsnCode : AggregateRoot
{
    public HsnCodeDetails CodeDetails { get; private set; }
    public string Chapter { get; private set; }
    public string Section { get; private set; }
    public HsnCodeStatus Status { get; private set; }
    public DateTime EffectiveFrom { get; private set; }
    public DateTime? EffectiveTo { get; private set; }
    public string? AdditionalNotes { get; private set; }
    public string CreatedBy { get; private set; }
    public string? ModifiedBy { get; private set; }
    public DateTime? ModifiedAt { get; private set; }

    // Navigation properties
    private readonly List<HsnCodeHistory> _history = new();
    public IReadOnlyList<HsnCodeHistory> History => _history.AsReadOnly();

    private readonly List<HsnCodeGstMapping> _gstMappings = new();
    public IReadOnlyList<HsnCodeGstMapping> GstMappings => _gstMappings.AsReadOnly();

    private HsnCode() { }

    public HsnCode(
        HsnCodeDetails codeDetails,
        string chapter,
        string section,
        DateTime effectiveFrom,
        string createdBy,
        DateTime? effectiveTo = null,
        string? additionalNotes = null)
    {
        if (string.IsNullOrWhiteSpace(chapter))
            throw new ArgumentException("Chapter cannot be empty", nameof(chapter));

        if (string.IsNullOrWhiteSpace(section))
            throw new ArgumentException("Section cannot be empty", nameof(section));

        if (string.IsNullOrWhiteSpace(createdBy))
            throw new ArgumentException("Created by cannot be empty", nameof(createdBy));

        if (effectiveTo.HasValue && effectiveTo.Value <= effectiveFrom)
            throw new ArgumentException("Effective to date must be after effective from date", nameof(effectiveTo));

        CodeDetails = codeDetails;
        Chapter = chapter.Trim();
        Section = section.Trim();
        EffectiveFrom = effectiveFrom;
        EffectiveTo = effectiveTo;
        AdditionalNotes = additionalNotes?.Trim();
        Status = HsnCodeStatus.Active;
        CreatedBy = createdBy.Trim();

        AddHistoryEntry("Created", $"HSN code created by {createdBy}");
    }

    public void UpdateDetails(
        HsnCodeDetails codeDetails,
        string chapter,
        string section,
        string modifiedBy,
        string? additionalNotes = null)
    {
        if (Status == HsnCodeStatus.Deprecated)
            throw new InvalidOperationException("Cannot update deprecated HSN code");

        if (string.IsNullOrWhiteSpace(modifiedBy))
            throw new ArgumentException("Modified by cannot be empty", nameof(modifiedBy));

        var changes = new List<string>();

        if (!CodeDetails.Equals(codeDetails))
        {
            changes.Add($"Code Details updated: {CodeDetails.Code} → {codeDetails.Code}");
            CodeDetails = codeDetails;
        }

        if (Chapter != chapter.Trim())
        {
            changes.Add($"Chapter: {Chapter} → {chapter.Trim()}");
            Chapter = chapter.Trim();
        }

        if (Section != section.Trim())
        {
            changes.Add($"Section: {Section} → {section.Trim()}");
            Section = section.Trim();
        }

        if (AdditionalNotes != additionalNotes?.Trim())
        {
            changes.Add("Additional Notes updated");
            AdditionalNotes = additionalNotes?.Trim();
        }

        if (changes.Any())
        {
            ModifiedBy = modifiedBy.Trim();
            ModifiedAt = DateTime.UtcNow;
            SetUpdatedAt();

            AddHistoryEntry("Updated", $"HSN code updated by {modifiedBy}: {string.Join(", ", changes)}");
        }
    }

    public void UpdateEffectiveDates(DateTime effectiveFrom, DateTime? effectiveTo, string modifiedBy)
    {
        if (Status == HsnCodeStatus.Deprecated)
            throw new InvalidOperationException("Cannot update effective dates for deprecated HSN code");

        if (string.IsNullOrWhiteSpace(modifiedBy))
            throw new ArgumentException("Modified by cannot be empty", nameof(modifiedBy));

        if (effectiveTo.HasValue && effectiveTo.Value <= effectiveFrom)
            throw new ArgumentException("Effective to date must be after effective from date", nameof(effectiveTo));

        var changes = new List<string>();

        if (EffectiveFrom != effectiveFrom)
        {
            changes.Add($"Effective From: {EffectiveFrom:yyyy-MM-dd} → {effectiveFrom:yyyy-MM-dd}");
            EffectiveFrom = effectiveFrom;
        }

        if (EffectiveTo != effectiveTo)
        {
            var oldDate = EffectiveTo?.ToString("yyyy-MM-dd") ?? "None";
            var newDate = effectiveTo?.ToString("yyyy-MM-dd") ?? "None";
            changes.Add($"Effective To: {oldDate} → {newDate}");
            EffectiveTo = effectiveTo;
        }

        if (changes.Any())
        {
            ModifiedBy = modifiedBy.Trim();
            ModifiedAt = DateTime.UtcNow;
            SetUpdatedAt();

            AddHistoryEntry("Effective Dates Updated", $"Dates updated by {modifiedBy}: {string.Join(", ", changes)}");
        }
    }

    public void Activate(string activatedBy)
    {
        if (Status == HsnCodeStatus.Deprecated)
            throw new InvalidOperationException("Cannot activate deprecated HSN code");

        if (string.IsNullOrWhiteSpace(activatedBy))
            throw new ArgumentException("Activated by cannot be empty", nameof(activatedBy));

        if (Status != HsnCodeStatus.Active)
        {
            Status = HsnCodeStatus.Active;
            ModifiedBy = activatedBy.Trim();
            ModifiedAt = DateTime.UtcNow;
            SetUpdatedAt();

            AddHistoryEntry("Activated", $"HSN code activated by {activatedBy}");
        }
    }

    public void Deactivate(string deactivatedBy, string reason)
    {
        if (Status == HsnCodeStatus.Deprecated)
            throw new InvalidOperationException("Cannot deactivate deprecated HSN code");

        if (string.IsNullOrWhiteSpace(deactivatedBy))
            throw new ArgumentException("Deactivated by cannot be empty", nameof(deactivatedBy));

        if (Status != HsnCodeStatus.Inactive)
        {
            Status = HsnCodeStatus.Inactive;
            ModifiedBy = deactivatedBy.Trim();
            ModifiedAt = DateTime.UtcNow;
            SetUpdatedAt();

            AddHistoryEntry("Deactivated", $"HSN code deactivated by {deactivatedBy}. Reason: {reason}");
        }
    }

    public void Deprecate(string deprecatedBy, string reason)
    {
        if (string.IsNullOrWhiteSpace(deprecatedBy))
            throw new ArgumentException("Deprecated by cannot be empty", nameof(deprecatedBy));

        Status = HsnCodeStatus.Deprecated;
        EffectiveTo = DateTime.UtcNow;
        ModifiedBy = deprecatedBy.Trim();
        ModifiedAt = DateTime.UtcNow;
        SetUpdatedAt();

        AddHistoryEntry("Deprecated", $"HSN code deprecated by {deprecatedBy}. Reason: {reason}");
    }

    public void AddGstMapping(GstRate gstRate, DateTime effectiveFrom, DateTime? effectiveTo, string addedBy)
    {
        if (Status != HsnCodeStatus.Active)
            throw new InvalidOperationException("Cannot add GST mapping to inactive HSN code");

        if (string.IsNullOrWhiteSpace(addedBy))
            throw new ArgumentException("Added by cannot be empty", nameof(addedBy));

        // Check for overlapping mappings
        var overlapping = _gstMappings.Any(m =>
            m.Status == HsnCodeStatus.Active &&
            m.EffectiveFrom <= (effectiveTo ?? DateTime.MaxValue) &&
            (m.EffectiveTo ?? DateTime.MaxValue) >= effectiveFrom);

        if (overlapping)
            throw new InvalidOperationException("GST mapping dates overlap with existing active mapping");

        var mapping = new HsnCodeGstMapping(Id, gstRate, effectiveFrom, effectiveTo, addedBy);
        _gstMappings.Add(mapping);

        AddHistoryEntry("GST Mapping Added", $"GST rate {gstRate}% mapping added by {addedBy} (Effective: {effectiveFrom:yyyy-MM-dd})");
    }

    public GstRate? GetApplicableGstRate(DateTime date)
    {
        if (Status != HsnCodeStatus.Active || !IsEffectiveOn(date))
            return null;

        var applicableMapping = _gstMappings
            .Where(m => m.Status == HsnCodeStatus.Active && m.IsEffectiveOn(date))
            .OrderByDescending(m => m.EffectiveFrom)
            .FirstOrDefault();

        return applicableMapping?.GstRate;
    }

    public bool IsEffectiveOn(DateTime date)
    {
        return date >= EffectiveFrom && (!EffectiveTo.HasValue || date <= EffectiveTo.Value);
    }

    public bool IsValidForService(ServiceCategory serviceCategory)
    {
        // This is a simplified validation - in reality, you'd have more complex mapping logic
        return Status == HsnCodeStatus.Active && IsEffectiveOn(DateTime.UtcNow);
    }

    private void AddHistoryEntry(string action, string details)
    {
        var historyEntry = new HsnCodeHistory(Id, action, details, ModifiedBy ?? CreatedBy);
        _history.Add(historyEntry);
    }
}

/// <summary>
/// Entity representing HSN code GST rate mappings
/// </summary>
public class HsnCodeGstMapping : BaseEntity
{
    public Guid HsnCodeId { get; private set; }
    public GstRate GstRate { get; private set; }
    public DateTime EffectiveFrom { get; private set; }
    public DateTime? EffectiveTo { get; private set; }
    public HsnCodeStatus Status { get; private set; }
    public string CreatedBy { get; private set; }

    private HsnCodeGstMapping() { }

    public HsnCodeGstMapping(Guid hsnCodeId, GstRate gstRate, DateTime effectiveFrom, DateTime? effectiveTo, string createdBy)
    {
        HsnCodeId = hsnCodeId;
        GstRate = gstRate;
        EffectiveFrom = effectiveFrom;
        EffectiveTo = effectiveTo;
        Status = HsnCodeStatus.Active;
        CreatedBy = createdBy;
    }

    public bool IsEffectiveOn(DateTime date)
    {
        return date >= EffectiveFrom && (!EffectiveTo.HasValue || date <= EffectiveTo.Value);
    }

    public void Deactivate()
    {
        Status = HsnCodeStatus.Inactive;
    }
}

/// <summary>
/// Entity representing HSN code history for audit purposes
/// </summary>
public class HsnCodeHistory : BaseEntity
{
    public Guid HsnCodeId { get; private set; }
    public string Action { get; private set; }
    public string Details { get; private set; }
    public string ModifiedBy { get; private set; }
    public DateTime ModifiedAt { get; private set; }

    private HsnCodeHistory() { }

    public HsnCodeHistory(Guid hsnCodeId, string action, string details, string modifiedBy)
    {
        HsnCodeId = hsnCodeId;
        Action = action;
        Details = details;
        ModifiedBy = modifiedBy;
        ModifiedAt = DateTime.UtcNow;
    }
}


