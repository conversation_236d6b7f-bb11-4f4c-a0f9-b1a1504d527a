using Microsoft.EntityFrameworkCore;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Infrastructure.Persistence;

namespace SubscriptionManagement.Infrastructure.Repositories;

public class PaymentRepository : IPaymentRepository
{
    private readonly SubscriptionDbContext _context;

    public PaymentRepository(SubscriptionDbContext context)
    {
        _context = context;
    }

    public async Task<Payment?> GetByIdAsync(Guid id)
    {
        return await _context.Payments
            .FirstOrDefaultAsync(p => p.Id == id);
    }

    public async Task<List<Payment>> GetBySubscriptionIdAsync(Guid subscriptionId)
    {
        return await _context.Payments
            .Where(p => p.SubscriptionId == subscriptionId)
            .OrderByDescending(p => p.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<Payment>> GetByUserIdAsync(Guid userId)
    {
        return await _context.Payments
            .Where(p => p.UserId == userId)
            .OrderByDescending(p => p.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<Payment>> GetByStatusAsync(PaymentStatus status)
    {
        return await _context.Payments
            .Where(p => p.Status == status)
            .OrderByDescending(p => p.CreatedAt)
            .ToListAsync();
    }

    public async Task<Payment?> GetByTransactionIdAsync(string transactionId)
    {
        return await _context.Payments
            .FirstOrDefaultAsync(p => p.PaymentGatewayTransactionId == transactionId);
    }

    public async Task<List<Payment>> GetFailedPaymentsAsync()
    {
        return await _context.Payments
            .Where(p => p.Status == PaymentStatus.Failed)
            .OrderByDescending(p => p.FailedAt)
            .ToListAsync();
    }

    public async Task<List<Payment>> GetPendingRetryPaymentsAsync()
    {
        return await _context.Payments
            .Where(p => p.Status == PaymentStatus.Failed &&
                       p.NextRetryAt.HasValue &&
                       p.NextRetryAt.Value <= DateTime.UtcNow)
            .OrderBy(p => p.NextRetryAt)
            .ToListAsync();
    }

    public async Task<Payment> AddAsync(Payment payment)
    {
        _context.Payments.Add(payment);
        await _context.SaveChangesAsync();
        return payment;
    }

    public async Task UpdateAsync(Payment payment)
    {
        _context.Payments.Update(payment);
        await _context.SaveChangesAsync();
    }

    public async Task DeleteAsync(Guid id)
    {
        var payment = await _context.Payments.FindAsync(id);
        if (payment != null)
        {
            _context.Payments.Remove(payment);
            await _context.SaveChangesAsync();
        }
    }

    public async Task<List<Payment>> GetPaymentHistoryAsync(Guid userId, DateTime? from = null, DateTime? to = null)
    {
        var query = _context.Payments
            .Where(p => p.UserId == userId);

        if (from.HasValue)
        {
            query = query.Where(p => p.CreatedAt >= from.Value);
        }

        if (to.HasValue)
        {
            query = query.Where(p => p.CreatedAt <= to.Value);
        }

        return await query
            .OrderByDescending(p => p.CreatedAt)
            .ToListAsync();
    }

    public async Task<decimal> GetTotalRevenueAsync(DateTime? from = null, DateTime? to = null)
    {
        var query = _context.Payments
            .Where(p => p.Status == PaymentStatus.Completed);

        if (from.HasValue)
        {
            query = query.Where(p => p.ProcessedAt >= from.Value);
        }

        if (to.HasValue)
        {
            query = query.Where(p => p.ProcessedAt <= to.Value);
        }

        return await query.SumAsync(p => p.Amount.Amount);
    }

    public async Task<Dictionary<string, decimal>> GetRevenueByPeriodAsync(DateTime from, DateTime to, string groupBy = "month")
    {
        var payments = await _context.Payments
            .Where(p => p.Status == PaymentStatus.Completed &&
                       p.ProcessedAt >= from &&
                       p.ProcessedAt <= to)
            .ToListAsync();

        return groupBy.ToLower() switch
        {
            "day" => payments
                .GroupBy(p => p.ProcessedAt!.Value.Date)
                .ToDictionary(g => g.Key.ToString("yyyy-MM-dd"), g => g.Sum(p => p.Amount.Amount)),
            "week" => payments
                .GroupBy(p => GetWeekOfYear(p.ProcessedAt!.Value))
                .ToDictionary(g => g.Key, g => g.Sum(p => p.Amount.Amount)),
            "month" => payments
                .GroupBy(p => p.ProcessedAt!.Value.ToString("yyyy-MM"))
                .ToDictionary(g => g.Key, g => g.Sum(p => p.Amount.Amount)),
            "year" => payments
                .GroupBy(p => p.ProcessedAt!.Value.Year)
                .ToDictionary(g => g.Key.ToString(), g => g.Sum(p => p.Amount.Amount)),
            _ => payments
                .GroupBy(p => p.ProcessedAt!.Value.ToString("yyyy-MM"))
                .ToDictionary(g => g.Key, g => g.Sum(p => p.Amount.Amount))
        };
    }

    private string GetWeekOfYear(DateTime date)
    {
        var culture = System.Globalization.CultureInfo.CurrentCulture;
        var calendar = culture.Calendar;
        var weekOfYear = calendar.GetWeekOfYear(date, culture.DateTimeFormat.CalendarWeekRule, culture.DateTimeFormat.FirstDayOfWeek);
        return $"{date.Year}-W{weekOfYear:00}";
    }
}
