using CommunicationNotification.Infrastructure.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Text.RegularExpressions;

namespace CommunicationNotification.Infrastructure.Services;

/// <summary>
/// Database query optimization service with performance monitoring and recommendations
/// </summary>
public interface IQueryOptimizationService
{
    Task<IDisposable> TrackQueryAsync(string queryType, string sql = "");
    Task<List<QueryOptimizationRecommendation>> GetOptimizationRecommendationsAsync();
    Task<Dictionary<string, object>> GetQueryPerformanceMetricsAsync();
    Task<List<SlowQuery>> GetSlowQueriesAsync(TimeSpan? threshold = null);
    Task OptimizeConnectionPoolAsync();
    Task<QueryExecutionPlan> AnalyzeQueryPlanAsync(string sql);
    Task<List<IndexRecommendation>> GetIndexRecommendationsAsync();
    void RecordQueryExecution(string queryType, TimeSpan duration, bool isSuccess, string? errorMessage = null);
}

public class QueryOptimizationService : IQueryOptimizationService
{
    private readonly ILogger<QueryOptimizationService> _logger;
    private readonly QueryOptimizationConfiguration _configuration;
    private readonly ConcurrentDictionary<string, QueryPerformanceTracker> _queryTrackers;
    private readonly ConcurrentQueue<QueryExecution> _queryExecutions;
    private readonly IPerformanceMonitoringService _performanceMonitoring;

    public QueryOptimizationService(
        ILogger<QueryOptimizationService> logger,
        IOptions<QueryOptimizationConfiguration> configuration,
        IPerformanceMonitoringService performanceMonitoring)
    {
        _logger = logger;
        _configuration = configuration.Value;
        _queryTrackers = new ConcurrentDictionary<string, QueryPerformanceTracker>();
        _queryExecutions = new ConcurrentQueue<QueryExecution>();
        _performanceMonitoring = performanceMonitoring;
    }

    public async Task<IDisposable> TrackQueryAsync(string queryType, string sql = "")
    {
        var tracker = new QueryTracker(queryType, sql, this);
        await Task.CompletedTask;
        return tracker;
    }

    public async Task<List<QueryOptimizationRecommendation>> GetOptimizationRecommendationsAsync()
    {
        try
        {
            var recommendations = new List<QueryOptimizationRecommendation>();
            var cutoffTime = DateTime.UtcNow.Subtract(TimeSpan.FromHours(_configuration.AnalysisWindowHours));

            var recentExecutions = _queryExecutions
                .Where(e => e.Timestamp >= cutoffTime)
                .ToList();

            // Analyze slow queries
            var slowQueries = recentExecutions
                .Where(e => e.Duration.TotalMilliseconds > _configuration.SlowQueryThresholdMs)
                .GroupBy(e => e.QueryType)
                .Select(g => new
                {
                    QueryType = g.Key,
                    AverageTime = g.Average(e => e.Duration.TotalMilliseconds),
                    Count = g.Count(),
                    ErrorRate = g.Count(e => !e.IsSuccess) / (double)g.Count()
                })
                .OrderByDescending(q => q.AverageTime)
                .ToList();

            foreach (var query in slowQueries)
            {
                var recommendation = new QueryOptimizationRecommendation
                {
                    QueryType = query.QueryType,
                    AverageExecutionTime = query.AverageTime,
                    ExecutionCount = query.Count,
                    PotentialImprovement = CalculatePotentialImprovement(query.AverageTime, query.Count)
                };

                if (query.AverageTime > 1000) // > 1 second
                {
                    recommendation.Recommendation = "Consider adding database indexes or optimizing query structure";
                    recommendation.RecommendationType = "Index";
                }
                else if (query.AverageTime > 500) // > 500ms
                {
                    recommendation.Recommendation = "Review query execution plan and consider query optimization";
                    recommendation.RecommendationType = "Query";
                }
                else if (query.ErrorRate > 0.05) // > 5% error rate
                {
                    recommendation.Recommendation = "High error rate detected, review query logic and error handling";
                    recommendation.RecommendationType = "Error";
                }

                recommendations.Add(recommendation);
            }

            // Analyze frequently executed queries
            var frequentQueries = recentExecutions
                .GroupBy(e => e.QueryType)
                .Where(g => g.Count() > _configuration.FrequentQueryThreshold)
                .Select(g => new
                {
                    QueryType = g.Key,
                    Count = g.Count(),
                    AverageTime = g.Average(e => e.Duration.TotalMilliseconds),
                    TotalTime = g.Sum(e => e.Duration.TotalMilliseconds)
                })
                .OrderByDescending(q => q.TotalTime)
                .ToList();

            foreach (var query in frequentQueries.Take(5))
            {
                if (query.AverageTime > 100) // > 100ms for frequent queries
                {
                    recommendations.Add(new QueryOptimizationRecommendation
                    {
                        QueryType = query.QueryType,
                        AverageExecutionTime = query.AverageTime,
                        ExecutionCount = query.Count,
                        Recommendation = "Frequently executed query with moderate execution time. Consider caching results or optimizing query.",
                        RecommendationType = "Cache",
                        PotentialImprovement = CalculatePotentialImprovement(query.AverageTime, query.Count)
                    });
                }
            }

            _logger.LogInformation("Generated {Count} query optimization recommendations", recommendations.Count);
            return recommendations;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating query optimization recommendations");
            return new List<QueryOptimizationRecommendation>();
        }
    }

    public async Task<Dictionary<string, object>> GetQueryPerformanceMetricsAsync()
    {
        try
        {
            var cutoffTime = DateTime.UtcNow.Subtract(TimeSpan.FromHours(1));
            var recentExecutions = _queryExecutions
                .Where(e => e.Timestamp >= cutoffTime)
                .ToList();

            var metrics = new Dictionary<string, object>
            {
                ["totalQueries"] = recentExecutions.Count,
                ["averageExecutionTime"] = recentExecutions.Any() ? recentExecutions.Average(e => e.Duration.TotalMilliseconds) : 0,
                ["slowQueries"] = recentExecutions.Count(e => e.Duration.TotalMilliseconds > _configuration.SlowQueryThresholdMs),
                ["errorRate"] = recentExecutions.Any() ? recentExecutions.Count(e => !e.IsSuccess) / (double)recentExecutions.Count : 0,
                ["queriesPerMinute"] = recentExecutions.Count / 60.0,
                ["topQueryTypes"] = recentExecutions
                    .GroupBy(e => e.QueryType)
                    .OrderByDescending(g => g.Count())
                    .Take(5)
                    .ToDictionary(g => g.Key, g => g.Count()),
                ["performanceTrend"] = CalculatePerformanceTrend(recentExecutions)
            };

            return metrics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting query performance metrics");
            return new Dictionary<string, object>();
        }
    }

    public async Task<List<SlowQuery>> GetSlowQueriesAsync(TimeSpan? threshold = null)
    {
        try
        {
            var queryThreshold = threshold ?? TimeSpan.FromMilliseconds(_configuration.SlowQueryThresholdMs);
            var cutoffTime = DateTime.UtcNow.Subtract(TimeSpan.FromHours(_configuration.AnalysisWindowHours));

            var slowQueries = _queryExecutions
                .Where(e => e.Timestamp >= cutoffTime && e.Duration > queryThreshold)
                .GroupBy(e => e.QueryType)
                .Select(g => new SlowQuery
                {
                    QueryType = g.Key,
                    AverageExecutionTime = g.Average(e => e.Duration.TotalMilliseconds),
                    MaxExecutionTime = g.Max(e => e.Duration.TotalMilliseconds),
                    ExecutionCount = g.Count(),
                    LastExecuted = g.Max(e => e.Timestamp),
                    ErrorCount = g.Count(e => !e.IsSuccess),
                    SampleSql = g.FirstOrDefault()?.Sql ?? ""
                })
                .OrderByDescending(q => q.AverageExecutionTime)
                .ToList();

            return slowQueries;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting slow queries");
            return new List<SlowQuery>();
        }
    }

    public async Task OptimizeConnectionPoolAsync()
    {
        try
        {
            _logger.LogInformation("Optimizing database connection pool");

            // Analyze connection pool usage patterns
            var metrics = await GetQueryPerformanceMetricsAsync();
            var queriesPerMinute = (double)(metrics.GetValueOrDefault("queriesPerMinute", 0.0));

            // Calculate optimal pool size based on query load
            var recommendedPoolSize = Math.Max(5, Math.Min(50, (int)(queriesPerMinute / 10)));

            _logger.LogInformation("Recommended connection pool size: {PoolSize} based on {QueriesPerMinute} queries/minute",
                recommendedPoolSize, queriesPerMinute);

            // Here you would apply the optimization to your DbContext configuration
            // This is a placeholder for the actual implementation
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error optimizing connection pool");
        }
    }

    public async Task<QueryExecutionPlan> AnalyzeQueryPlanAsync(string sql)
    {
        try
        {
            // This would integrate with your database to get actual execution plans
            // For now, returning a mock analysis
            var plan = new QueryExecutionPlan
            {
                Sql = sql,
                EstimatedCost = CalculateEstimatedCost(sql),
                Operations = AnalyzeQueryOperations(sql),
                Recommendations = GenerateQueryRecommendations(sql),
                IndexUsage = AnalyzeIndexUsage(sql)
            };

            return plan;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing query plan for SQL: {Sql}", sql);
            return new QueryExecutionPlan { Sql = sql };
        }
    }

    public async Task<List<IndexRecommendation>> GetIndexRecommendationsAsync()
    {
        try
        {
            var recommendations = new List<IndexRecommendation>();
            var cutoffTime = DateTime.UtcNow.Subtract(TimeSpan.FromDays(_configuration.IndexAnalysisWindowDays));

            var recentExecutions = _queryExecutions
                .Where(e => e.Timestamp >= cutoffTime)
                .ToList();

            // Analyze query patterns to suggest indexes
            var tableAccess = AnalyzeTableAccessPatterns(recentExecutions);

            foreach (var table in tableAccess)
            {
                if (table.Value.SlowQueryCount > _configuration.IndexRecommendationThreshold)
                {
                    recommendations.Add(new IndexRecommendation
                    {
                        TableName = table.Key,
                        RecommendedColumns = table.Value.FrequentColumns,
                        IndexType = "BTREE",
                        EstimatedImprovement = table.Value.AverageExecutionTime * 0.3, // 30% improvement estimate
                        Justification = $"Table accessed {table.Value.AccessCount} times with {table.Value.SlowQueryCount} slow queries"
                    });
                }
            }

            return recommendations;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting index recommendations");
            return new List<IndexRecommendation>();
        }
    }

    public void RecordQueryExecution(string queryType, TimeSpan duration, bool isSuccess, string? errorMessage = null)
    {
        try
        {
            var execution = new QueryExecution
            {
                QueryType = queryType,
                Duration = duration,
                IsSuccess = isSuccess,
                ErrorMessage = errorMessage,
                Timestamp = DateTime.UtcNow
            };

            _queryExecutions.Enqueue(execution);

            // Trim old executions to prevent memory issues
            if (_queryExecutions.Count > _configuration.MaxExecutionHistory)
            {
                for (int i = 0; i < _configuration.MaxExecutionHistory / 4; i++)
                {
                    _queryExecutions.TryDequeue(out _);
                }
            }

            // Update performance tracker
            var tracker = _queryTrackers.GetOrAdd(queryType, _ => new QueryPerformanceTracker(queryType));
            tracker.RecordExecution(duration, isSuccess);

            // Record in performance monitoring
            _performanceMonitoring.RecordDatabaseQuery(queryType, duration, isSuccess);

            _logger.LogDebug("Recorded query execution: {QueryType} in {Duration}ms, Success: {IsSuccess}",
                queryType, duration.TotalMilliseconds, isSuccess);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording query execution for {QueryType}", queryType);
        }
    }

    internal void CompleteQueryTracking(string queryType, string sql, TimeSpan duration, bool isSuccess)
    {
        RecordQueryExecution(queryType, duration, isSuccess);
    }

    private double CalculatePotentialImprovement(double averageTime, int count)
    {
        // Calculate potential time savings if query is optimized by 50%
        var timeSavingsPerExecution = averageTime * 0.5;
        var totalTimeSavings = timeSavingsPerExecution * count;
        return totalTimeSavings;
    }

    private double CalculatePerformanceTrend(List<QueryExecution> executions)
    {
        if (executions.Count < 10) return 0;

        var sortedExecutions = executions.OrderBy(e => e.Timestamp).ToList();
        var firstHalf = sortedExecutions.Take(sortedExecutions.Count / 2);
        var secondHalf = sortedExecutions.Skip(sortedExecutions.Count / 2);

        var firstHalfAvg = firstHalf.Average(e => e.Duration.TotalMilliseconds);
        var secondHalfAvg = secondHalf.Average(e => e.Duration.TotalMilliseconds);

        return (secondHalfAvg - firstHalfAvg) / firstHalfAvg * 100; // Percentage change
    }

    private double CalculateEstimatedCost(string sql)
    {
        // Simple heuristic for estimating query cost
        var cost = 1.0;
        
        if (sql.Contains("JOIN", StringComparison.OrdinalIgnoreCase))
            cost *= 2;
        if (sql.Contains("ORDER BY", StringComparison.OrdinalIgnoreCase))
            cost *= 1.5;
        if (sql.Contains("GROUP BY", StringComparison.OrdinalIgnoreCase))
            cost *= 1.5;
        if (sql.Contains("LIKE", StringComparison.OrdinalIgnoreCase))
            cost *= 1.3;

        return cost;
    }

    private List<string> AnalyzeQueryOperations(string sql)
    {
        var operations = new List<string>();
        
        if (sql.Contains("SELECT", StringComparison.OrdinalIgnoreCase))
            operations.Add("Table Scan");
        if (sql.Contains("JOIN", StringComparison.OrdinalIgnoreCase))
            operations.Add("Join");
        if (sql.Contains("WHERE", StringComparison.OrdinalIgnoreCase))
            operations.Add("Filter");
        if (sql.Contains("ORDER BY", StringComparison.OrdinalIgnoreCase))
            operations.Add("Sort");
        if (sql.Contains("GROUP BY", StringComparison.OrdinalIgnoreCase))
            operations.Add("Group");

        return operations;
    }

    private List<string> GenerateQueryRecommendations(string sql)
    {
        var recommendations = new List<string>();

        if (sql.Contains("SELECT *", StringComparison.OrdinalIgnoreCase))
            recommendations.Add("Avoid SELECT *, specify only needed columns");
        
        if (sql.Contains("LIKE '%", StringComparison.OrdinalIgnoreCase))
            recommendations.Add("Leading wildcard in LIKE prevents index usage");
        
        if (!sql.Contains("WHERE", StringComparison.OrdinalIgnoreCase) && sql.Contains("SELECT", StringComparison.OrdinalIgnoreCase))
            recommendations.Add("Consider adding WHERE clause to limit result set");

        return recommendations;
    }

    private List<string> AnalyzeIndexUsage(string sql)
    {
        var indexes = new List<string>();
        
        // Extract table and column references (simplified)
        var tableMatches = Regex.Matches(sql, @"FROM\s+(\w+)", RegexOptions.IgnoreCase);
        var whereMatches = Regex.Matches(sql, @"WHERE\s+(\w+)", RegexOptions.IgnoreCase);

        foreach (Match match in tableMatches)
        {
            indexes.Add($"Table: {match.Groups[1].Value}");
        }

        foreach (Match match in whereMatches)
        {
            indexes.Add($"Potential index on: {match.Groups[1].Value}");
        }

        return indexes;
    }

    private Dictionary<string, TableAccessPattern> AnalyzeTableAccessPatterns(List<QueryExecution> executions)
    {
        var patterns = new Dictionary<string, TableAccessPattern>();

        foreach (var execution in executions)
        {
            // Extract table name from query type (simplified)
            var tableName = ExtractTableName(execution.QueryType);
            
            if (!patterns.ContainsKey(tableName))
            {
                patterns[tableName] = new TableAccessPattern { TableName = tableName };
            }

            var pattern = patterns[tableName];
            pattern.AccessCount++;
            pattern.TotalExecutionTime += execution.Duration.TotalMilliseconds;
            pattern.AverageExecutionTime = pattern.TotalExecutionTime / pattern.AccessCount;

            if (execution.Duration.TotalMilliseconds > _configuration.SlowQueryThresholdMs)
            {
                pattern.SlowQueryCount++;
            }
        }

        return patterns;
    }

    private string ExtractTableName(string queryType)
    {
        // Simple extraction logic - in real implementation, this would be more sophisticated
        var parts = queryType.Split('_', '.');
        return parts.Length > 1 ? parts[1] : queryType;
    }
}

// Supporting classes
public class QueryTracker : IDisposable
{
    private readonly string _queryType;
    private readonly string _sql;
    private readonly Stopwatch _stopwatch;
    private readonly QueryOptimizationService _service;
    private bool _disposed;

    public QueryTracker(string queryType, string sql, QueryOptimizationService service)
    {
        _queryType = queryType;
        _sql = sql;
        _service = service;
        _stopwatch = Stopwatch.StartNew();
    }

    public void Dispose()
    {
        if (_disposed) return;

        _stopwatch.Stop();
        _service.CompleteQueryTracking(_queryType, _sql, _stopwatch.Elapsed, true);
        _disposed = true;
    }

    public void MarkAsError()
    {
        if (_disposed) return;

        _stopwatch.Stop();
        _service.CompleteQueryTracking(_queryType, _sql, _stopwatch.Elapsed, false);
        _disposed = true;
    }
}

public class QueryPerformanceTracker
{
    public string QueryType { get; }
    public int ExecutionCount { get; private set; }
    public double TotalExecutionTime { get; private set; }
    public double AverageExecutionTime => ExecutionCount > 0 ? TotalExecutionTime / ExecutionCount : 0;
    public int ErrorCount { get; private set; }
    public DateTime LastExecuted { get; private set; }

    public QueryPerformanceTracker(string queryType)
    {
        QueryType = queryType;
    }

    public void RecordExecution(TimeSpan duration, bool isSuccess)
    {
        ExecutionCount++;
        TotalExecutionTime += duration.TotalMilliseconds;
        LastExecuted = DateTime.UtcNow;

        if (!isSuccess)
        {
            ErrorCount++;
        }
    }
}

public class QueryExecution
{
    public string QueryType { get; set; } = string.Empty;
    public string Sql { get; set; } = string.Empty;
    public TimeSpan Duration { get; set; }
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime Timestamp { get; set; }
}

public class SlowQuery
{
    public string QueryType { get; set; } = string.Empty;
    public double AverageExecutionTime { get; set; }
    public double MaxExecutionTime { get; set; }
    public int ExecutionCount { get; set; }
    public DateTime LastExecuted { get; set; }
    public int ErrorCount { get; set; }
    public string SampleSql { get; set; } = string.Empty;
}

public class QueryExecutionPlan
{
    public string Sql { get; set; } = string.Empty;
    public double EstimatedCost { get; set; }
    public List<string> Operations { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
    public List<string> IndexUsage { get; set; } = new();
}

public class IndexRecommendation
{
    public string TableName { get; set; } = string.Empty;
    public List<string> RecommendedColumns { get; set; } = new();
    public string IndexType { get; set; } = string.Empty;
    public double EstimatedImprovement { get; set; }
    public string Justification { get; set; } = string.Empty;
}

public class TableAccessPattern
{
    public string TableName { get; set; } = string.Empty;
    public int AccessCount { get; set; }
    public double TotalExecutionTime { get; set; }
    public double AverageExecutionTime { get; set; }
    public int SlowQueryCount { get; set; }
    public List<string> FrequentColumns { get; set; } = new();
}

public class QueryOptimizationConfiguration
{
    public bool EnableOptimization { get; set; } = true;
    public double SlowQueryThresholdMs { get; set; } = 500;
    public int FrequentQueryThreshold { get; set; } = 100;
    public int AnalysisWindowHours { get; set; } = 24;
    public int IndexAnalysisWindowDays { get; set; } = 7;
    public int IndexRecommendationThreshold { get; set; } = 10;
    public int MaxExecutionHistory { get; set; } = 10000;
    public bool EnableQueryPlanAnalysis { get; set; } = true;
    public List<string> ExcludedQueryTypes { get; set; } = new();
}
