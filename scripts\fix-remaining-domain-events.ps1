# Fix remaining domain events in ContentManagement
Write-Host "Fixing remaining domain events..." -ForegroundColor Green

# Fix ContentEvents.cs
$contentEventsFile = "Services\ContentManagement\ContentManagement.Domain\Events\ContentEvents.cs"
if (Test-Path $contentEventsFile) {
    $content = Get-Content $contentEventsFile -Raw
    
    # Fix all remaining record events to include Id and OccurredOn properties
    $patterns = @(
        'public record (\w+Event)\([^)]+\) : IDomainEvent;'
    )
    
    foreach ($pattern in $patterns) {
        $content = [regex]::Replace($content, $pattern, {
            param($match)
            $eventName = $match.Groups[1].Value
            $fullMatch = $match.Groups[0].Value
            $replacement = $fullMatch -replace ';$', "`n{`n    public Guid Id { get; init; } = Guid.NewGuid();`n    public DateTime OccurredOn { get; init; } = DateTime.UtcNow;`n}"
            return $replacement
        })
    }
    
    Set-Content -Path $contentEventsFile -Value $content -Encoding UTF8
    Write-Host "  Fixed ContentEvents.cs" -ForegroundColor Green
}

# Fix FaqEvents.cs
$faqEventsFile = "Services\ContentManagement\ContentManagement.Domain\Events\FaqEvents.cs"
if (Test-Path $faqEventsFile) {
    $content = Get-Content $faqEventsFile -Raw
    
    # Fix all remaining record events to include Id and OccurredOn properties
    $patterns = @(
        'public record (\w+Event)\([^)]+\) : IDomainEvent;'
    )
    
    foreach ($pattern in $patterns) {
        $content = [regex]::Replace($content, $pattern, {
            param($match)
            $eventName = $match.Groups[1].Value
            $fullMatch = $match.Groups[0].Value
            $replacement = $fullMatch -replace ';$', "`n{`n    public Guid Id { get; init; } = Guid.NewGuid();`n    public DateTime OccurredOn { get; init; } = DateTime.UtcNow;`n}"
            return $replacement
        })
    }
    
    Set-Content -Path $faqEventsFile -Value $content -Encoding UTF8
    Write-Host "  Fixed FaqEvents.cs" -ForegroundColor Green
}

# Fix PolicyEvents.cs
$policyEventsFile = "Services\ContentManagement\ContentManagement.Domain\Events\PolicyEvents.cs"
if (Test-Path $policyEventsFile) {
    $content = Get-Content $policyEventsFile -Raw
    
    # Fix all remaining record events to include Id and OccurredOn properties
    $patterns = @(
        'public record (\w+Event)\([^)]+\) : IDomainEvent;'
    )
    
    foreach ($pattern in $patterns) {
        $content = [regex]::Replace($content, $pattern, {
            param($match)
            $eventName = $match.Groups[1].Value
            $fullMatch = $match.Groups[0].Value
            $replacement = $fullMatch -replace ';$', "`n{`n    public Guid Id { get; init; } = Guid.NewGuid();`n    public DateTime OccurredOn { get; init; } = DateTime.UtcNow;`n}"
            return $replacement
        })
    }
    
    Set-Content -Path $policyEventsFile -Value $content -Encoding UTF8
    Write-Host "  Fixed PolicyEvents.cs" -ForegroundColor Green
}

# Fix AuditCompliance TenantEvents.cs
$tenantEventsFile = "Services\AuditCompliance\AuditCompliance.Domain\Events\TenantEvents.cs"
if (Test-Path $tenantEventsFile) {
    $content = Get-Content $tenantEventsFile -Raw
    
    # Fix all remaining record events to include Id and OccurredOn properties
    $patterns = @(
        'public record (\w+Event)\([^)]+\) : IDomainEvent;'
    )
    
    foreach ($pattern in $patterns) {
        $content = [regex]::Replace($content, $pattern, {
            param($match)
            $eventName = $match.Groups[1].Value
            $fullMatch = $match.Groups[0].Value
            $replacement = $fullMatch -replace ';$', "`n{`n    public Guid Id { get; init; } = Guid.NewGuid();`n    public DateTime OccurredOn { get; init; } = DateTime.UtcNow;`n}"
            return $replacement
        })
    }
    
    Set-Content -Path $tenantEventsFile -Value $content -Encoding UTF8
    Write-Host "  Fixed TenantEvents.cs" -ForegroundColor Green
}

Write-Host "Domain events fix completed!" -ForegroundColor Green
