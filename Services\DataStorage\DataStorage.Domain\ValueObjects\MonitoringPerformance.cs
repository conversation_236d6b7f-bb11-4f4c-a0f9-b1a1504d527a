using DataStorage.Domain.Enums;

namespace DataStorage.Domain.ValueObjects;

public class SystemHealthStatus
{
    public HealthStatus OverallHealth { get; private set; }
    public DateTime LastUpdated { get; private set; }
    public List<ComponentHealth> ComponentHealths { get; private set; }
    public SystemHealthMetrics Metrics { get; private set; }
    public List<HealthIssue> Issues { get; private set; }

    public SystemHealthStatus(
        HealthStatus overallHealth,
        List<ComponentHealth> componentHealths,
        SystemHealthMetrics metrics,
        List<HealthIssue> issues)
    {
        OverallHealth = overallHealth;
        LastUpdated = DateTime.UtcNow;
        ComponentHealths = componentHealths ?? new List<ComponentHealth>();
        Metrics = metrics ?? throw new ArgumentNullException(nameof(metrics));
        Issues = issues ?? new List<HealthIssue>();
    }
}

public class ComponentHealth
{
    public string ComponentName { get; private set; }
    public HealthStatus Status { get; private set; }
    public double HealthScore { get; private set; }
    public DateTime LastChecked { get; private set; }
    public string? StatusMessage { get; private set; }
    public Dictionary<string, object> Metrics { get; private set; }

    public ComponentHealth(
        string componentName,
        HealthStatus status,
        double healthScore,
        DateTime lastChecked,
        string? statusMessage = null,
        Dictionary<string, object>? metrics = null)
    {
        ComponentName = componentName ?? throw new ArgumentNullException(nameof(componentName));
        Status = status;
        HealthScore = Math.Max(0, Math.Min(100, healthScore));
        LastChecked = lastChecked;
        StatusMessage = statusMessage;
        Metrics = metrics ?? new Dictionary<string, object>();
    }
}

public class SystemHealthMetrics
{
    public double CpuUsagePercentage { get; private set; }
    public double MemoryUsagePercentage { get; private set; }
    public double DiskUsagePercentage { get; private set; }
    public double NetworkThroughputMbps { get; private set; }
    public int ActiveConnections { get; private set; }
    public TimeSpan Uptime { get; private set; }
    public double ResponseTimeMs { get; private set; }
    public double ThroughputRequestsPerSecond { get; private set; }

    public SystemHealthMetrics(
        double cpuUsagePercentage,
        double memoryUsagePercentage,
        double diskUsagePercentage,
        double networkThroughputMbps,
        int activeConnections,
        TimeSpan uptime,
        double responseTimeMs,
        double throughputRequestsPerSecond)
    {
        CpuUsagePercentage = Math.Max(0, Math.Min(100, cpuUsagePercentage));
        MemoryUsagePercentage = Math.Max(0, Math.Min(100, memoryUsagePercentage));
        DiskUsagePercentage = Math.Max(0, Math.Min(100, diskUsagePercentage));
        NetworkThroughputMbps = networkThroughputMbps;
        ActiveConnections = activeConnections;
        Uptime = uptime;
        ResponseTimeMs = responseTimeMs;
        ThroughputRequestsPerSecond = throughputRequestsPerSecond;
    }
}

public class HealthIssue
{
    public Guid IssueId { get; private set; }
    public HealthIssueSeverity Severity { get; private set; }
    public string Component { get; private set; }
    public string Title { get; private set; }
    public string Description { get; private set; }
    public DateTime DetectedAt { get; private set; }
    public HealthIssueStatus Status { get; private set; }
    public string? Resolution { get; private set; }

    public HealthIssue(
        Guid issueId,
        HealthIssueSeverity severity,
        string component,
        string title,
        string description,
        DateTime detectedAt,
        HealthIssueStatus status,
        string? resolution = null)
    {
        IssueId = issueId;
        Severity = severity;
        Component = component ?? throw new ArgumentNullException(nameof(component));
        Title = title ?? throw new ArgumentNullException(nameof(title));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        DetectedAt = detectedAt;
        Status = status;
        Resolution = resolution;
    }
}

public class SystemMetric
{
    public string Name { get; private set; }
    public string Category { get; private set; }
    public double Value { get; private set; }
    public string Unit { get; private set; }
    public DateTime Timestamp { get; private set; }
    public Dictionary<string, string> Tags { get; private set; }

    public SystemMetric(
        string name,
        string category,
        double value,
        string unit,
        DateTime timestamp,
        Dictionary<string, string>? tags = null)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Category = category ?? throw new ArgumentNullException(nameof(category));
        Value = value;
        Unit = unit ?? throw new ArgumentNullException(nameof(unit));
        Timestamp = timestamp;
        Tags = tags ?? new Dictionary<string, string>();
    }
}

public class SystemMetricsRequest
{
    public DateTime StartTime { get; private set; }
    public DateTime EndTime { get; private set; }
    public List<string> MetricNames { get; private set; }
    public List<string> Categories { get; private set; }
    public MetricsAggregation Aggregation { get; private set; }
    public TimeSpan Interval { get; private set; }

    public SystemMetricsRequest(
        DateTime startTime,
        DateTime endTime,
        List<string>? metricNames = null,
        List<string>? categories = null,
        MetricsAggregation aggregation = MetricsAggregation.Average,
        TimeSpan interval = default)
    {
        StartTime = startTime;
        EndTime = endTime;
        MetricNames = metricNames ?? new List<string>();
        Categories = categories ?? new List<string>();
        Aggregation = aggregation;
        Interval = interval == default ? TimeSpan.FromMinutes(5) : interval;
    }
}

public class PerformanceTestResult
{
    public Guid TestId { get; private set; }
    public string TestName { get; private set; }
    public PerformanceTestType Type { get; private set; }
    public DateTime StartedAt { get; private set; }
    public DateTime? CompletedAt { get; private set; }
    public PerformanceTestStatus Status { get; private set; }
    public TimeSpan Duration { get; private set; }
    public PerformanceTestMetrics Metrics { get; private set; }
    public List<PerformanceTestScenario> Scenarios { get; private set; }
    public string? ErrorMessage { get; private set; }

    public PerformanceTestResult(
        Guid testId,
        string testName,
        PerformanceTestType type,
        DateTime startedAt,
        PerformanceTestStatus status,
        TimeSpan duration,
        PerformanceTestMetrics metrics,
        List<PerformanceTestScenario> scenarios,
        DateTime? completedAt = null,
        string? errorMessage = null)
    {
        TestId = testId;
        TestName = testName ?? throw new ArgumentNullException(nameof(testName));
        Type = type;
        StartedAt = startedAt;
        CompletedAt = completedAt;
        Status = status;
        Duration = duration;
        Metrics = metrics ?? throw new ArgumentNullException(nameof(metrics));
        Scenarios = scenarios ?? new List<PerformanceTestScenario>();
        ErrorMessage = errorMessage;
    }
}

public class PerformanceTestRequest
{
    public string TestName { get; private set; }
    public PerformanceTestType Type { get; private set; }
    public List<PerformanceTestScenario> Scenarios { get; private set; }
    public PerformanceTestConfiguration Configuration { get; private set; }
    public Guid RequestedBy { get; private set; }

    public PerformanceTestRequest(
        string testName,
        PerformanceTestType type,
        List<PerformanceTestScenario> scenarios,
        PerformanceTestConfiguration configuration,
        Guid requestedBy)
    {
        TestName = testName ?? throw new ArgumentNullException(nameof(testName));
        Type = type;
        Scenarios = scenarios ?? new List<PerformanceTestScenario>();
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        RequestedBy = requestedBy;
    }
}

public class PerformanceTestScenario
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public List<PerformanceTestStep> Steps { get; private set; }
    public PerformanceTestScenarioMetrics Metrics { get; private set; }
    public PerformanceTestScenarioStatus Status { get; private set; }

    public PerformanceTestScenario(
        string name,
        string description,
        List<PerformanceTestStep> steps,
        PerformanceTestScenarioMetrics metrics,
        PerformanceTestScenarioStatus status)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        Steps = steps ?? new List<PerformanceTestStep>();
        Metrics = metrics ?? throw new ArgumentNullException(nameof(metrics));
        Status = status;
    }
}

public class PerformanceTestStep
{
    public string Name { get; private set; }
    public PerformanceTestStepType Type { get; private set; }
    public Dictionary<string, object> Parameters { get; private set; }
    public PerformanceTestStepMetrics Metrics { get; private set; }

    public PerformanceTestStep(
        string name,
        PerformanceTestStepType type,
        Dictionary<string, object> parameters,
        PerformanceTestStepMetrics metrics)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Type = type;
        Parameters = parameters ?? new Dictionary<string, object>();
        Metrics = metrics ?? throw new ArgumentNullException(nameof(metrics));
    }
}

public class PerformanceTestConfiguration
{
    public int ConcurrentUsers { get; private set; }
    public TimeSpan Duration { get; private set; }
    public TimeSpan RampUpTime { get; private set; }
    public TimeSpan RampDownTime { get; private set; }
    public int MaxRequestsPerSecond { get; private set; }
    public Dictionary<string, object> Settings { get; private set; }

    public PerformanceTestConfiguration(
        int concurrentUsers,
        TimeSpan duration,
        TimeSpan rampUpTime,
        TimeSpan rampDownTime,
        int maxRequestsPerSecond,
        Dictionary<string, object>? settings = null)
    {
        ConcurrentUsers = concurrentUsers;
        Duration = duration;
        RampUpTime = rampUpTime;
        RampDownTime = rampDownTime;
        MaxRequestsPerSecond = maxRequestsPerSecond;
        Settings = settings ?? new Dictionary<string, object>();
    }
}
