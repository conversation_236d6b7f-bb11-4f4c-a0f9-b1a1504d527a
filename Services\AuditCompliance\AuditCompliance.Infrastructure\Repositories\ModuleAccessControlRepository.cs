using AuditCompliance.Application.Interfaces;
using AuditCompliance.Domain.Entities;
using AuditCompliance.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace AuditCompliance.Infrastructure.Repositories
{
    /// <summary>
    /// Repository implementation for ModuleAccessControl entity
    /// </summary>
    public class ModuleAccessControlRepository : IModuleAccessControlRepository
    {
        private readonly AuditComplianceDbContext _context;
        private readonly ILogger<ModuleAccessControlRepository> _logger;

        public ModuleAccessControlRepository(
            AuditComplianceDbContext context,
            ILogger<ModuleAccessControlRepository> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<ModuleAccessControl?> GetByUserAndModuleAsync(Guid userId, string moduleName)
        {
            try
            {
                return await _context.ModuleAccessControls
                    .FirstOrDefaultAsync(mac => mac.UserId == userId && mac.ModuleName == moduleName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving module access control for user {UserId} and module {ModuleName}", userId, moduleName);
                throw;
            }
        }

        public async Task<List<ModuleAccessControl>> GetByUserIdAsync(Guid userId)
        {
            try
            {
                return await _context.ModuleAccessControls
                    .Where(mac => mac.UserId == userId)
                    .OrderBy(mac => mac.ModuleName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving module access controls for user {UserId}", userId);
                throw;
            }
        }

        public async Task<List<ModuleAccessControl>> GetByModuleNameAsync(string moduleName)
        {
            try
            {
                return await _context.ModuleAccessControls
                    .Where(mac => mac.ModuleName == moduleName)
                    .OrderBy(mac => mac.GrantedAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving module access controls for module {ModuleName}", moduleName);
                throw;
            }
        }

        public async Task<ModuleAccessControl> AddAsync(ModuleAccessControl accessControl)
        {
            try
            {
                _context.ModuleAccessControls.Add(accessControl);
                await _context.SaveChangesAsync();
                
                _logger.LogInformation("Module access control added for user {UserId} and module {ModuleName}", 
                    accessControl.UserId, accessControl.ModuleName);
                return accessControl;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding module access control for user {UserId} and module {ModuleName}", 
                    accessControl.UserId, accessControl.ModuleName);
                throw;
            }
        }

        public async Task UpdateAsync(ModuleAccessControl accessControl)
        {
            try
            {
                _context.ModuleAccessControls.Update(accessControl);
                await _context.SaveChangesAsync();
                
                _logger.LogInformation("Module access control updated for user {UserId} and module {ModuleName}", 
                    accessControl.UserId, accessControl.ModuleName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating module access control for user {UserId} and module {ModuleName}", 
                    accessControl.UserId, accessControl.ModuleName);
                throw;
            }
        }

        public async Task DeleteAsync(Guid id)
        {
            try
            {
                var accessControl = await _context.ModuleAccessControls.FindAsync(id);
                if (accessControl != null)
                {
                    _context.ModuleAccessControls.Remove(accessControl);
                    await _context.SaveChangesAsync();
                    
                    _logger.LogInformation("Module access control deleted: {Id}", id);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting module access control {Id}", id);
                throw;
            }
        }

        public async Task<List<ModuleAccessControl>> GetActiveAccessControlsAsync()
        {
            try
            {
                return await _context.ModuleAccessControls
                    .Where(mac => mac.IsActive && (!mac.ExpiresAt.HasValue || mac.ExpiresAt.Value > DateTime.UtcNow))
                    .OrderBy(mac => mac.ModuleName)
                    .ThenBy(mac => mac.UserId)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving active module access controls");
                throw;
            }
        }

        public async Task<List<ModuleAccessControl>> GetExpiredAccessControlsAsync()
        {
            try
            {
                return await _context.ModuleAccessControls
                    .Where(mac => mac.IsActive && mac.ExpiresAt.HasValue && mac.ExpiresAt.Value <= DateTime.UtcNow)
                    .OrderBy(mac => mac.ExpiresAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving expired module access controls");
                throw;
            }
        }

        public async Task<List<ModuleAccessControl>> GetAccessControlsExpiringInDaysAsync(int days)
        {
            try
            {
                var expiryThreshold = DateTime.UtcNow.AddDays(days);
                return await _context.ModuleAccessControls
                    .Where(mac => mac.IsActive && 
                                 mac.ExpiresAt.HasValue && 
                                 mac.ExpiresAt.Value <= expiryThreshold &&
                                 mac.ExpiresAt.Value > DateTime.UtcNow)
                    .OrderBy(mac => mac.ExpiresAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving access controls expiring in {Days} days", days);
                throw;
            }
        }

        public async Task<bool> HasUserAccessToModuleAsync(Guid userId, string moduleName)
        {
            try
            {
                return await _context.ModuleAccessControls
                    .AnyAsync(mac => mac.UserId == userId && 
                                    mac.ModuleName == moduleName && 
                                    mac.IsActive &&
                                    (!mac.ExpiresAt.HasValue || mac.ExpiresAt.Value > DateTime.UtcNow));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking user access for user {UserId} and module {ModuleName}", userId, moduleName);
                throw;
            }
        }

        public async Task<bool> HasUserAccessToActionAsync(Guid userId, string moduleName, string action)
        {
            try
            {
                var accessControl = await GetByUserAndModuleAsync(userId, moduleName);
                return accessControl?.HasAccessToAction(action) ?? false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking user action access for user {UserId}, module {ModuleName}, action {Action}", 
                    userId, moduleName, action);
                throw;
            }
        }

        public async Task<List<string>> GetUserAccessibleModulesAsync(Guid userId)
        {
            try
            {
                return await _context.ModuleAccessControls
                    .Where(mac => mac.UserId == userId && 
                                 mac.IsActive &&
                                 (!mac.ExpiresAt.HasValue || mac.ExpiresAt.Value > DateTime.UtcNow))
                    .Select(mac => mac.ModuleName)
                    .Distinct()
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving accessible modules for user {UserId}", userId);
                throw;
            }
        }

        public async Task<List<Guid>> GetModuleAuthorizedUsersAsync(string moduleName)
        {
            try
            {
                return await _context.ModuleAccessControls
                    .Where(mac => mac.ModuleName == moduleName && 
                                 mac.IsActive &&
                                 (!mac.ExpiresAt.HasValue || mac.ExpiresAt.Value > DateTime.UtcNow))
                    .Select(mac => mac.UserId)
                    .Distinct()
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving authorized users for module {ModuleName}", moduleName);
                throw;
            }
        }

        public async Task BulkRevokeExpiredAccessAsync()
        {
            try
            {
                var expiredAccess = await GetExpiredAccessControlsAsync();
                foreach (var access in expiredAccess)
                {
                    access.RevokeAccess(Guid.Empty, "Automatic expiration");
                }

                await _context.SaveChangesAsync();
                _logger.LogInformation("Bulk revoked {Count} expired access controls", expiredAccess.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error bulk revoking expired access controls");
                throw;
            }
        }

        public async Task<Dictionary<string, int>> GetAccessControlStatisticsAsync()
        {
            try
            {
                var stats = new Dictionary<string, int>();
                
                stats["TotalAccessControls"] = await _context.ModuleAccessControls.CountAsync();
                stats["ActiveAccessControls"] = await _context.ModuleAccessControls
                    .CountAsync(mac => mac.IsActive && (!mac.ExpiresAt.HasValue || mac.ExpiresAt.Value > DateTime.UtcNow));
                stats["ExpiredAccessControls"] = await _context.ModuleAccessControls
                    .CountAsync(mac => mac.IsActive && mac.ExpiresAt.HasValue && mac.ExpiresAt.Value <= DateTime.UtcNow);
                stats["RevokedAccessControls"] = await _context.ModuleAccessControls
                    .CountAsync(mac => !mac.IsActive);
                stats["UniqueUsers"] = await _context.ModuleAccessControls
                    .Select(mac => mac.UserId)
                    .Distinct()
                    .CountAsync();
                stats["UniqueModules"] = await _context.ModuleAccessControls
                    .Select(mac => mac.ModuleName)
                    .Distinct()
                    .CountAsync();

                return stats;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving access control statistics");
                throw;
            }
        }
    }
}
