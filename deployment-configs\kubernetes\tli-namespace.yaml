apiVersion: v1
kind: Namespace
metadata:
  name: tli-production
  labels:
    name: tli-production
    environment: production
    app: tli-microservices
---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: tli-resource-quota
  namespace: tli-production
spec:
  hard:
    requests.cpu: "20"
    requests.memory: 40Gi
    limits.cpu: "40"
    limits.memory: 80Gi
    persistentvolumeclaims: "10"
    services: "20"
    secrets: "10"
    configmaps: "10"
---
apiVersion: v1
kind: LimitRange
metadata:
  name: tli-limit-range
  namespace: tli-production
spec:
  limits:
  - default:
      cpu: "1"
      memory: "2Gi"
    defaultRequest:
      cpu: "100m"
      memory: "128Mi"
    type: Container
  - default:
      storage: "10Gi"
    type: PersistentVolumeClaim
