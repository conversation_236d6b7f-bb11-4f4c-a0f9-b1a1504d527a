using System.ComponentModel.DataAnnotations;
using AnalyticsBIService.Domain.Enums;

namespace AnalyticsBIService.Application.DTOs;

/// <summary>
/// Create report schedule data transfer object
/// </summary>
public class CreateReportScheduleDto
{
    [Required]
    public string ReportName { get; set; } = string.Empty;

    public string Description { get; set; } = string.Empty;

    [Required]
    public string ReportType { get; set; } = string.Empty; // "Performance", "Financial", "Operational", "Custom"

    [Required]
    public string ScheduleType { get; set; } = string.Empty; // "Daily", "Weekly", "Monthly", "Quarterly", "Yearly", "Once"

    public string CronExpression { get; set; } = string.Empty;
    public string ScheduleExpression { get; set; } = string.Empty;
    public DateTime? ScheduledDate { get; set; }
    public TimeSpan? ScheduledTime { get; set; }

    [Required]
    public List<string> Recipients { get; set; } = new();
    public List<string> EmailRecipients { get; set; } = new();
    public List<string> ExportFormats { get; set; } = new();

    [Required]
    public ExportFormat Format { get; set; }

    public Dictionary<string, object> ReportParameters { get; set; } = new();
    public Dictionary<string, object> Parameters { get; set; } = new();
    public List<FilterCriteriaDto> Filters { get; set; } = new();
    public bool IsActive { get; set; } = true;
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public string TimeZone { get; set; } = "UTC";
    public Guid UserId { get; set; }
}

/// <summary>
/// Update report schedule data transfer object
/// </summary>
public class UpdateReportScheduleDto
{
    [Required]
    public string ScheduleId { get; set; } = string.Empty;

    public string? ReportName { get; set; }
    public string? Description { get; set; }
    public string? ScheduleType { get; set; }
    public string? CronExpression { get; set; }
    public DateTime? ScheduledDate { get; set; }
    public TimeSpan? ScheduledTime { get; set; }
    public List<string>? Recipients { get; set; }
    public ExportFormat? Format { get; set; }
    public Dictionary<string, object>? ReportParameters { get; set; }
    public List<FilterCriteriaDto>? Filters { get; set; }
    public bool? IsActive { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public string? TimeZone { get; set; }

    // Additional properties required by ReportSchedulingService
    public string ScheduleExpression { get; set; } = string.Empty;
    public Dictionary<string, object> Parameters { get; set; } = new();
    public List<string> EmailRecipients { get; set; } = new();
    public List<ExportFormat> ExportFormats { get; set; } = new();
}

/// <summary>
/// Report execution result data transfer object
/// </summary>
public class ReportExecutionResultDto
{
    public Guid Id { get; set; }
    public string ExecutionId { get; set; } = string.Empty;
    public Guid ScheduleId { get; set; }
    public string ReportName { get; set; } = string.Empty;
    public DateTime ExecutionStartTime { get; set; }
    public DateTime? ExecutionEndTime { get; set; }
    public string Status { get; set; } = string.Empty; // "Running", "Completed", "Failed", "Cancelled"
    public string? ErrorMessage { get; set; }
    public string? FilePath { get; set; }
    public string? FileName { get; set; }
    public long? FileSize { get; set; }
    public ExportFormat Format { get; set; }
    public int RecordCount { get; set; }
    public decimal ExecutionDuration { get; set; }
    public List<string> Recipients { get; set; } = new();

    // Properties required by ReportSchedulingService
    public bool IsManualExecution { get; set; }
    public object? ReportData { get; set; }
    public bool EmailSent { get; set; }
    public DateTime? EmailSentTime { get; set; }
    public Dictionary<string, object> ExecutionMetadata { get; set; } = new();
}

/// <summary>
/// Report template data transfer object
/// </summary>
public class ReportTemplateDto
{
    public string TemplateId { get; set; } = string.Empty;
    public string TemplateName { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty; // "Financial", "Operational", "Performance", "Compliance"
    public string ReportType { get; set; } = string.Empty;
    public List<ReportSectionDto> Sections { get; set; } = new();
    public List<ReportParameterDto> Parameters { get; set; } = new();
    public List<ExportFormat> SupportedFormats { get; set; } = new();
    public string CreatedBy { get; set; } = string.Empty;

    // Additional properties required by AdvancedReportingService
    public Guid Id { get; set; }
    public Guid UserId { get; set; }
    public DateTime CreatedAt { get; set; }
    public ReportConfigurationDto Configuration { get; set; } = new();
    public bool IsPublic { get; set; }
    public List<string> Tags { get; set; } = new();

    // Additional properties required by TransportCompanyReportService
    public List<string> DataSources { get; set; } = new();
    public Dictionary<string, object> DefaultParameters { get; set; } = new();
    public bool IsBuiltIn { get; set; }
    public TimeSpan EstimatedExecutionTime { get; set; }
}

/// <summary>
/// Report section data transfer object
/// </summary>
public class ReportSectionDto
{
    public string SectionId { get; set; } = string.Empty;
    public string SectionName { get; set; } = string.Empty;
    public string SectionType { get; set; } = string.Empty; // "Chart", "Table", "KPI", "Text", "Image"
    public int Order { get; set; }
    public string DataSource { get; set; } = string.Empty;
    public string Query { get; set; } = string.Empty;
    public List<string> Columns { get; set; } = new();
    public Dictionary<string, object> Formatting { get; set; } = new();
    public Dictionary<string, object> ChartOptions { get; set; } = new();
    public bool IsVisible { get; set; } = true;
    public List<FilterCriteriaDto> Filters { get; set; } = new();

    // Additional properties required by AdvancedReportingService
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public object Data { get; set; } = new();
    public ReportSectionMetricsDto Metrics { get; set; } = new();
    public string Type { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public List<ReportVisualizationDto> Visualizations { get; set; } = new();
}

/// <summary>
/// Report metric data transfer object
/// </summary>
public class ReportMetricDto
{
    public string MetricId { get; set; } = string.Empty;
    public string MetricName { get; set; } = string.Empty;
    public decimal Value { get; set; }
    public string Unit { get; set; } = string.Empty;
    public string Format { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public decimal? Target { get; set; }
    public decimal? Variance { get; set; }
    public string Trend { get; set; } = string.Empty; // "Up", "Down", "Stable"
}

/// <summary>
/// Report parameter data transfer object
/// </summary>
public class ReportParameterDto
{
    public string ParameterName { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string ParameterType { get; set; } = string.Empty; // "String", "Number", "Date", "Boolean", "List"
    public string Type { get; set; } = string.Empty;
    public object? DefaultValue { get; set; }
    public bool IsRequired { get; set; } = false;
    public bool Required { get; set; } = false;
    public List<ParameterOptionDto> Options { get; set; } = new();
    public string ValidationRule { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<object> PossibleValues { get; set; } = new();
}

/// <summary>
/// Parameter option data transfer object
/// </summary>
public class ParameterOptionDto
{
    public string Label { get; set; } = string.Empty;
    public object Value { get; set; } = new();
    public bool IsDefault { get; set; } = false;
}

/// <summary>
/// Report data source data transfer object
/// </summary>
public class ReportDataSourceDto
{
    public string DataSourceId { get; set; } = string.Empty;
    public string DataSourceName { get; set; } = string.Empty;
    public string DataSourceType { get; set; } = string.Empty; // "Database", "API", "File", "Service"
    public string ConnectionString { get; set; } = string.Empty;
    public Dictionary<string, object> Configuration { get; set; } = new();
    public bool IsActive { get; set; } = true;
    public DateTime LastTested { get; set; }
    public string TestStatus { get; set; } = string.Empty; // "Success", "Failed", "Not Tested"
    public List<DataSourceTableDto> Tables { get; set; } = new();
}

/// <summary>
/// Data source table data transfer object
/// </summary>
public class DataSourceTableDto
{
    public string TableName { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<DataSourceColumnDto> Columns { get; set; } = new();
    public bool IsView { get; set; } = false;
    public long RecordCount { get; set; }
}

/// <summary>
/// Data source column data transfer object
/// </summary>
public class DataSourceColumnDto
{
    public string ColumnName { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string DataType { get; set; } = string.Empty;
    public bool IsNullable { get; set; } = true;
    public bool IsPrimaryKey { get; set; } = false;
    public bool IsForeignKey { get; set; } = false;
    public string Description { get; set; } = string.Empty;
}

/// <summary>
/// Report analytics data transfer object
/// </summary>
public class ReportAnalyticsDto
{
    public string ReportId { get; set; } = string.Empty;
    public string ReportName { get; set; } = string.Empty;
    public int TotalExecutions { get; set; }
    public int SuccessfulExecutions { get; set; }
    public int FailedExecutions { get; set; }
    public decimal SuccessRate { get; set; }
    public decimal AverageExecutionTime { get; set; }
    public decimal AverageFileSize { get; set; }
    public int UniqueUsers { get; set; }
    public DateTime LastExecution { get; set; }
    public List<ReportUsageDto> UsageByUser { get; set; } = new();
    public List<ReportUsageDto> UsageByPeriod { get; set; } = new();
    public List<string> CommonErrors { get; set; } = new();
}

/// <summary>
/// Report usage data transfer object
/// </summary>
public class ReportUsageDto
{
    public string UserId { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public string Period { get; set; } = string.Empty; // For period-based usage
    public int ExecutionCount { get; set; }
    public DateTime LastExecution { get; set; }
    public decimal AverageExecutionTime { get; set; }
    public List<ExportFormat> PreferredFormats { get; set; } = new();
}

/// <summary>
/// Notification data transfer object
/// </summary>
public class NotificationDto
{
    public Guid Id { get; set; }
    public Guid UserId { get; set; }
    public string NotificationId { get; set; } = string.Empty;
    public string NotificationType { get; set; } = string.Empty; // "Alert", "Report", "System", "Threshold"
    public string Type { get; set; } = string.Empty; // "Alert", "Report", "System", "Threshold"
    public string Title { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty; // "Info", "Warning", "Error", "Critical"
    public string RecipientId { get; set; } = string.Empty;
    public string RecipientType { get; set; } = string.Empty; // "User", "Role", "Group"
    public List<string> DeliveryChannels { get; set; } = new(); // "Email", "SMS", "Push", "InApp"
    public DateTime CreatedAt { get; set; }
    public DateTime? SentAt { get; set; }
    public DateTime? ReadAt { get; set; }
    public bool IsRead { get; set; }
    public string Status { get; set; } = string.Empty; // "Pending", "Sent", "Delivered", "Read", "Failed"
    public Dictionary<string, object> Data { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
    public string? RelatedEntityId { get; set; }
    public string? RelatedEntityType { get; set; }
}

/// <summary>
/// Notification preferences data transfer object
/// </summary>
public class NotificationPreferencesDto
{
    public string UserId { get; set; } = string.Empty;
    public Dictionary<string, NotificationChannelPreferenceDto> ChannelPreferences { get; set; } = new();
    public Dictionary<string, bool> NotificationTypePreferences { get; set; } = new();
    public string TimeZone { get; set; } = "UTC";
    public TimeSpan? QuietHoursStart { get; set; }
    public TimeSpan? QuietHoursEnd { get; set; }
    public List<string> QuietDays { get; set; } = new(); // "Monday", "Tuesday", etc.
    public bool EnableDigestMode { get; set; } = false;
    public string DigestFrequency { get; set; } = "Daily"; // "Hourly", "Daily", "Weekly"

    // Additional properties required by NotificationService
    public bool EmailNotificationsEnabled { get; set; } = true;
    public bool SMSNotificationsEnabled { get; set; } = false;
    public bool PushNotificationsEnabled { get; set; } = true;
    public bool AlertNotificationsEnabled { get; set; } = true;
    public bool KPINotificationsEnabled { get; set; } = true;
    public bool ReportNotificationsEnabled { get; set; } = true;
    public bool ThresholdNotificationsEnabled { get; set; } = true;
    public string AlertSeverityFilter { get; set; } = "Warning"; // Minimum severity
    public string NotificationFrequency { get; set; } = "Immediate";
    public bool QuietHoursEnabled { get; set; } = false;
}

/// <summary>
/// Notification channel preference data transfer object
/// </summary>
public class NotificationChannelPreferenceDto
{
    public bool IsEnabled { get; set; } = true;
    public string Address { get; set; } = string.Empty; // Email address, phone number, etc.
    public Dictionary<string, bool> NotificationTypes { get; set; } = new();
    public string Priority { get; set; } = "Normal"; // "Low", "Normal", "High"
}

/// <summary>
/// Report execution entity (missing domain entity)
/// </summary>
public class ReportExecution
{
    public Guid Id { get; set; }
    public Guid ReportScheduleId { get; set; }
    public DateTime ExecutionStartTime { get; set; }
    public DateTime? ExecutionEndTime { get; set; }
    public string Status { get; set; } = string.Empty;
    public string? ErrorMessage { get; set; }
    public string? FilePath { get; set; }
    public string? FileName { get; set; }
    public long? FileSize { get; set; }
    public int RecordCount { get; set; }
    public Dictionary<string, object> ExecutionMetadata { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }

    // Missing property referenced in ReportSchedulingService
    public object? ReportData { get; set; }
}

/// <summary>
/// Report schedule entity (missing domain entity)
/// </summary>
public class ReportSchedule
{
    public Guid Id { get; set; }
    public string ReportName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string ReportType { get; set; } = string.Empty;
    public string ScheduleType { get; set; } = string.Empty;
    public string CronExpression { get; set; } = string.Empty;
    public DateTime? ScheduledDate { get; set; }
    public TimeSpan? ScheduledTime { get; set; }
    public List<string> Recipients { get; set; } = new();
    public ExportFormat Format { get; set; }
    public Dictionary<string, object> ReportParameters { get; set; } = new();
    public bool IsActive { get; set; } = true;
    public DateTime? StartDate { get; set; }

    // Additional properties required by services
    public ScheduleFrequency Frequency { get; set; }
    public TimeSpan? ExecutionTime { get; set; }
    public DayOfWeek? DayOfWeek { get; set; }
    public int? DayOfMonth { get; set; }
    public DateTime? EndDate { get; set; }
    public string TimeZone { get; set; } = "UTC";
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

/// <summary>
/// Notification entity (missing domain entity)
/// </summary>
public class Notification
{
    public Guid Id { get; set; }
    public string NotificationType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public string RecipientId { get; set; } = string.Empty;
    public string RecipientType { get; set; } = string.Empty;
    public List<string> DeliveryChannels { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime? SentAt { get; set; }
    public DateTime? ReadAt { get; set; }
    public string Status { get; set; } = string.Empty;
    public Dictionary<string, object> Metadata { get; set; } = new();
    public string? RelatedEntityId { get; set; }
    public string? RelatedEntityType { get; set; }
}

/// <summary>
/// Export template data transfer object
/// </summary>
public class ExportTemplate
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public ExportType ExportType { get; set; }
    public ExportFormat Format { get; set; }
    public Dictionary<string, object> Configuration { get; set; } = new();
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

/// <summary>
/// Export type enumeration
/// </summary>
public enum ExportType
{
    Order,
    Orders,
    Trip,
    Trips,
    Timeline,
    Performance,
    PerformanceReport,
    Financial,
    Analytics,
    Custom
}



/// <summary>
/// Report data collection
/// </summary>
public class ReportDataCollection
{
    public Dictionary<string, object> Data { get; set; } = new();
    public List<string> Columns { get; set; } = new();
    public int TotalRecords { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Report data entity
/// </summary>
public class ReportData
{
    public Dictionary<string, object> Fields { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Report template entity
/// </summary>
public class ReportTemplate
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string ReportType { get; set; } = string.Empty;
    public Dictionary<string, object> Configuration { get; set; } = new();
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

/// <summary>
/// Comparison operator enumeration
/// </summary>
public enum ComparisonOperator
{
    Equal,
    NotEqual,
    GreaterThan,
    GreaterThanOrEqual,
    LessThan,
    LessThanOrEqual,
    Contains,
    StartsWith,
    EndsWith
}
