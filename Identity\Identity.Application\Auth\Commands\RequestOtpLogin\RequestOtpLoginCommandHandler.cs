using System;
using System.Threading;
using System.Threading.Tasks;
using Identity.Application.Common.Interfaces;
using Identity.Domain.Entities;
using Identity.Domain.Repositories;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Identity.Application.Auth.Commands.RequestOtpLogin
{
    public class RequestOtpLoginCommandHandler : IRequestHandler<RequestOtpLoginCommand, RequestOtpLoginResponse>
    {
        private readonly IUserRepository _userRepository;
        private readonly IOtpService _otpService;
        private readonly ILogger<RequestOtpLoginCommandHandler> _logger;

        public RequestOtpLoginCommandHandler(
            IUserRepository userRepository,
            IOtpService otpService,
            ILogger<RequestOtpLoginCommandHandler> logger)
        {
            _userRepository = userRepository;
            _otpService = otpService;
            _logger = logger;
        }

        public async Task<RequestOtpLoginResponse> Handle(RequestOtpLoginCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing OTP login request for username: {Username}", request.Username);

                // Find user by username or email
                var user = await _userRepository.GetByUsernameAsync(request.Username) ??
                          await _userRepository.GetByEmailAsync(request.Username);

                if (user == null)
                {
                    _logger.LogWarning("User not found for OTP login request: {Username}", request.Username);
                    return new RequestOtpLoginResponse
                    {
                        Success = false,
                        Message = "User not found"
                    };
                }

                // Check if user account is active
                if (user.Status != UserStatus.Active)
                {
                    _logger.LogWarning("Inactive user attempted OTP login: {UserId}", user.Id);
                    return new RequestOtpLoginResponse
                    {
                        Success = false,
                        Message = "Account is not active"
                    };
                }

                // Check if user is locked out
                if (user.LockoutEnd.HasValue && user.LockoutEnd > DateTime.UtcNow)
                {
                    _logger.LogWarning("Locked out user attempted OTP login: {UserId}", user.Id);
                    return new RequestOtpLoginResponse
                    {
                        Success = false,
                        Message = "Account is temporarily locked"
                    };
                }

                // Check rate limiting
                if (!await _otpService.CanRequestOtpAsync(user.Id, OtpPurpose.Login))
                {
                    return new RequestOtpLoginResponse
                    {
                        Success = false,
                        Message = "Please wait before requesting another OTP"
                    };
                }

                // Determine delivery method and address
                string deliveryAddress;
                if (request.DeliveryMethod.ToLower() == "sms")
                {
                    if (string.IsNullOrEmpty(user.PhoneNumber) || !user.PhoneNumberConfirmed)
                    {
                        return new RequestOtpLoginResponse
                        {
                            Success = false,
                            Message = "Phone number not verified"
                        };
                    }
                    deliveryAddress = user.PhoneNumber;
                }
                else if (request.DeliveryMethod.ToLower() == "email")
                {
                    if (!user.EmailConfirmed)
                    {
                        return new RequestOtpLoginResponse
                        {
                            Success = false,
                            Message = "Email not verified"
                        };
                    }
                    deliveryAddress = user.Email.Value;
                }
                else
                {
                    return new RequestOtpLoginResponse
                    {
                        Success = false,
                        Message = "Invalid delivery method. Use 'SMS' or 'Email'"
                    };
                }

                // Generate OTP
                var otp = await _otpService.GenerateOtpAsync(
                    user.Id,
                    OtpPurpose.Login,
                    request.DeliveryMethod,
                    deliveryAddress,
                    expirationMinutes: 5,
                    maxAttempts: 3);

                // Send OTP
                var sent = await _otpService.SendOtpAsync(
                    request.DeliveryMethod,
                    deliveryAddress,
                    otp,
                    OtpPurpose.Login);

                if (!sent)
                {
                    return new RequestOtpLoginResponse
                    {
                        Success = false,
                        Message = "Failed to send OTP. Please try again."
                    };
                }

                _logger.LogInformation("OTP login request processed successfully for user: {UserId}", user.Id);

                return new RequestOtpLoginResponse
                {
                    Success = true,
                    Message = "OTP sent successfully",
                    DeliveryAddress = MaskAddress(deliveryAddress),
                    ExpirationMinutes = 5,
                    MaxAttempts = 3
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing OTP login request for username: {Username}", request.Username);
                return new RequestOtpLoginResponse
                {
                    Success = false,
                    Message = "An error occurred while processing your request"
                };
            }
        }

        private string MaskAddress(string address)
        {
            if (string.IsNullOrEmpty(address))
                return address;

            if (address.Contains("@"))
            {
                // Email masking
                var parts = address.Split('@');
                if (parts[0].Length <= 2)
                    return $"**@{parts[1]}";
                return $"{parts[0][..2]}***@{parts[1]}";
            }
            else
            {
                // Phone number masking
                if (address.Length <= 4)
                    return "****";
                return $"****{address[^4..]}";
            }
        }
    }
}
