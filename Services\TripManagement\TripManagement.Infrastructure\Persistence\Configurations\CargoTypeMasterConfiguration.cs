using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TripManagement.Domain.Entities;
using System.Text.Json;

namespace TripManagement.Infrastructure.Persistence.Configurations;

public class CargoTypeMasterConfiguration : IEntityTypeConfiguration<CargoTypeMaster>
{
    public void Configure(EntityTypeBuilder<CargoTypeMaster> builder)
    {
        builder.ToTable("cargo_type_masters");

        builder.<PERSON>Key(c => c.Id);

        builder.Property(c => c.Id)
            .ValueGeneratedNever();

        builder.Property(c => c.Code)
            .IsRequired()
            .HasMaxLength(20);

        builder.Property(c => c.Name)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(c => c.Description)
            .HasMaxLength(500);

        builder.Property(c => c.Category)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(c => c.RequiresSpecialHandling)
            .IsRequired()
            .HasDefaultValue(false);

        builder.Property(c => c.<PERSON>)
            .IsRequired()
            .HasDefaultValue(false);

        builder.Property(c => c.RequiresTemperatureControl)
            .IsRequired()
            .HasDefaultValue(false);

        builder.Property(c => c.MinTemperatureCelsius)
            .HasColumnType("decimal(5,2)");

        builder.Property(c => c.MaxTemperatureCelsius)
            .HasColumnType("decimal(5,2)");

        builder.Property(c => c.HandlingInstructions)
            .HasMaxLength(1000);

        builder.Property(c => c.SafetyRequirements)
            .HasMaxLength(1000);

        builder.Property(c => c.IsActive)
            .IsRequired()
            .HasDefaultValue(true);

        builder.Property(c => c.SortOrder)
            .IsRequired()
            .HasDefaultValue(0);

        builder.Property(c => c.IconUrl)
            .HasMaxLength(500);

        // Configure AdditionalProperties as JSON
        builder.Property(c => c.AdditionalProperties)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        builder.Property(c => c.CreatedAt)
            .IsRequired();

        builder.Property(c => c.UpdatedAt)
            .IsRequired();

        // Indexes
        builder.HasIndex(c => c.Code)
            .IsUnique()
            .HasDatabaseName("IX_CargoTypeMasters_Code");

        builder.HasIndex(c => c.Name)
            .HasDatabaseName("IX_CargoTypeMasters_Name");

        builder.HasIndex(c => c.Category)
            .HasDatabaseName("IX_CargoTypeMasters_Category");

        builder.HasIndex(c => c.IsActive)
            .HasDatabaseName("IX_CargoTypeMasters_IsActive");

        builder.HasIndex(c => c.SortOrder)
            .HasDatabaseName("IX_CargoTypeMasters_SortOrder");

        builder.HasIndex(c => new { c.Category, c.IsActive, c.SortOrder })
            .HasDatabaseName("IX_CargoTypeMasters_Category_IsActive_SortOrder");

        builder.HasIndex(c => new { c.RequiresSpecialHandling, c.IsHazardous, c.RequiresTemperatureControl })
            .HasDatabaseName("IX_CargoTypeMasters_SpecialHandling");

        builder.HasIndex(c => new { c.MinTemperatureCelsius, c.MaxTemperatureCelsius })
            .HasDatabaseName("IX_CargoTypeMasters_TemperatureRange");

        // Constraints
        builder.HasCheckConstraint("CK_CargoTypeMasters_TemperatureRange", 
            "\"MinTemperatureCelsius\" IS NULL OR \"MaxTemperatureCelsius\" IS NULL OR \"MinTemperatureCelsius\" <= \"MaxTemperatureCelsius\"");
    }
}
