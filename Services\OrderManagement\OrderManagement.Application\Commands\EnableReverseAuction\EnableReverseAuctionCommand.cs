using MediatR;
using OrderManagement.Domain.ValueObjects;

namespace OrderManagement.Application.Commands.EnableReverseAuction;

public class EnableReverseAuctionCommand : IRequest<bool>
{
    public Guid RfqId { get; set; }
    public Guid EnabledBy { get; set; }
    public string? EnabledByName { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public Money? StartingPrice { get; set; }
    public Money? ReservePrice { get; set; }
    public decimal? MinimumBidDecrement { get; set; }
    public int? MaxBidders { get; set; }
    public bool AllowBidExtensions { get; set; } = true;
    public int? ExtensionMinutes { get; set; } = 15;
    public bool IsPublicAuction { get; set; } = false;
    public string? AuctionRules { get; set; }
    public bool CreateTimelineEvent { get; set; } = true;
}
