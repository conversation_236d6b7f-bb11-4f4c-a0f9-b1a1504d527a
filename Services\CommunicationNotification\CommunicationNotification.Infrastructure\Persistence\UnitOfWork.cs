using CommunicationNotification.Application.Interfaces;

namespace CommunicationNotification.Infrastructure.Persistence;

/// <summary>
/// Unit of Work implementation for communication service
/// </summary>
public class UnitOfWork : IUnitOfWork
{
    private readonly CommunicationDbContext _context;
    private bool _disposed = false;

    // Lazy-loaded repositories
    private INotificationRepository? _notifications;
    private IMessageRepository? _messages;
    private IUserPreferenceRepository? _userPreferences;
    private IConversationRepository? _conversations;

    public UnitOfWork(CommunicationDbContext context)
    {
        _context = context ?? throw new ArgumentNullException(nameof(context));
    }

    /// <summary>
    /// Notification repository
    /// </summary>
    public INotificationRepository Notifications
    {
        get
        {
            _notifications ??= new Repositories.NotificationRepository(_context);
            return _notifications;
        }
    }

    /// <summary>
    /// Message repository
    /// </summary>
    public IMessageRepository Messages
    {
        get
        {
            _messages ??= new Repositories.MessageRepository(_context);
            return _messages;
        }
    }

    /// <summary>
    /// User preference repository
    /// </summary>
    public IUserPreferenceRepository UserPreferences
    {
        get
        {
            _userPreferences ??= new Repositories.UserPreferenceRepository(_context);
            return _userPreferences;
        }
    }

    /// <summary>
    /// Conversation repository
    /// </summary>
    public IConversationRepository Conversations
    {
        get
        {
            _conversations ??= new Repositories.ConversationRepository(_context);
            return _conversations;
        }
    }

    /// <summary>
    /// Save all changes
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of affected records</returns>
    public async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        return await _context.SaveChangesAsync(cancellationToken);
    }

    /// <summary>
    /// Dispose resources
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// Dispose resources
    /// </summary>
    /// <param name="disposing">Whether disposing</param>
    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            _context.Dispose();
            _disposed = true;
        }
    }
}
