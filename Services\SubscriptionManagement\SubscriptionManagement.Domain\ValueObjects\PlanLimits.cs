using SubscriptionManagement.Domain.Exceptions;

namespace SubscriptionManagement.Domain.ValueObjects
{
    public class PlanLimits : IEquatable<PlanLimits>
    {
        public int? RfqLimit { get; private set; }
        public int? VehicleLimit { get; private set; }
        public int? CarrierLimit { get; private set; }
        public bool IsUnlimited { get; private set; }

        private PlanLimits() { }

        public PlanLimits(int? rfqLimit = null, int? vehicleLimit = null, int? carrierLimit = null, bool isUnlimited = false)
        {
            if (isUnlimited && (rfqLimit.HasValue || vehicleLimit.HasValue || carrierLimit.HasValue))
                throw new SubscriptionDomainException("Cannot set specific limits when plan is unlimited");

            if (!isUnlimited && !rfqLimit.HasValue && !vehicleLimit.HasValue && !carrierLimit.HasValue)
                throw new SubscriptionDomainException("At least one limit must be specified for non-unlimited plans");

            if (rfqLimit.HasValue && rfqLimit.Value < 0)
                throw new SubscriptionDomainException("RFQ limit cannot be negative");

            if (vehicleLimit.HasValue && vehicleLimit.Value < 0)
                throw new SubscriptionDomainException("Vehicle limit cannot be negative");

            if (carrierLimit.HasValue && carrierLimit.Value < 0)
                throw new SubscriptionDomainException("Carrier limit cannot be negative");

            RfqLimit = rfqLimit;
            VehicleLimit = vehicleLimit;
            CarrierLimit = carrierLimit;
            IsUnlimited = isUnlimited;
        }

        public static PlanLimits Unlimited()
        {
            return new PlanLimits(isUnlimited: true);
        }

        public static PlanLimits ForTransportCompany(int rfqLimit)
        {
            return new PlanLimits(rfqLimit: rfqLimit);
        }

        public static PlanLimits ForBroker(int rfqLimit, int carrierLimit)
        {
            return new PlanLimits(rfqLimit: rfqLimit, carrierLimit: carrierLimit);
        }

        public static PlanLimits ForCarrier(int rfqLimit, int vehicleLimit)
        {
            return new PlanLimits(rfqLimit: rfqLimit, vehicleLimit: vehicleLimit);
        }

        public bool CanCreateRfq(int currentRfqCount)
        {
            if (IsUnlimited) return true;
            if (!RfqLimit.HasValue) return true;
            return currentRfqCount < RfqLimit.Value;
        }

        public bool CanAddVehicle(int currentVehicleCount)
        {
            if (IsUnlimited) return true;
            if (!VehicleLimit.HasValue) return true;
            return currentVehicleCount < VehicleLimit.Value;
        }

        public bool CanAddCarrier(int currentCarrierCount)
        {
            if (IsUnlimited) return true;
            if (!CarrierLimit.HasValue) return true;
            return currentCarrierCount < CarrierLimit.Value;
        }

        public int GetRemainingRfqs(int currentRfqCount)
        {
            if (IsUnlimited || !RfqLimit.HasValue) return int.MaxValue;
            return Math.Max(0, RfqLimit.Value - currentRfqCount);
        }

        public int GetRemainingVehicles(int currentVehicleCount)
        {
            if (IsUnlimited || !VehicleLimit.HasValue) return int.MaxValue;
            return Math.Max(0, VehicleLimit.Value - currentVehicleCount);
        }

        public int GetRemainingCarriers(int currentCarrierCount)
        {
            if (IsUnlimited || !CarrierLimit.HasValue) return int.MaxValue;
            return Math.Max(0, CarrierLimit.Value - currentCarrierCount);
        }

        public bool IsWithinLimits(int rfqCount, int vehicleCount, int carrierCount)
        {
            return CanCreateRfq(rfqCount) && CanAddVehicle(vehicleCount) && CanAddCarrier(carrierCount);
        }

        public PlanLimits IncreaseLimits(int? additionalRfqs = null, int? additionalVehicles = null, int? additionalCarriers = null)
        {
            if (IsUnlimited) return this;

            var newRfqLimit = RfqLimit.HasValue && additionalRfqs.HasValue ? RfqLimit.Value + additionalRfqs.Value : RfqLimit;
            var newVehicleLimit = VehicleLimit.HasValue && additionalVehicles.HasValue ? VehicleLimit.Value + additionalVehicles.Value : VehicleLimit;
            var newCarrierLimit = CarrierLimit.HasValue && additionalCarriers.HasValue ? CarrierLimit.Value + additionalCarriers.Value : CarrierLimit;

            return new PlanLimits(newRfqLimit, newVehicleLimit, newCarrierLimit, false);
        }

        public bool Equals(PlanLimits? other)
        {
            if (other is null) return false;
            return RfqLimit == other.RfqLimit &&
                   VehicleLimit == other.VehicleLimit &&
                   CarrierLimit == other.CarrierLimit &&
                   IsUnlimited == other.IsUnlimited;
        }

        public override bool Equals(object? obj)
        {
            return Equals(obj as PlanLimits);
        }

        public override int GetHashCode()
        {
            return HashCode.Combine(RfqLimit, VehicleLimit, CarrierLimit, IsUnlimited);
        }

        public static bool operator ==(PlanLimits? left, PlanLimits? right)
        {
            return EqualityComparer<PlanLimits>.Default.Equals(left, right);
        }

        public static bool operator !=(PlanLimits? left, PlanLimits? right)
        {
            return !(left == right);
        }

        public override string ToString()
        {
            if (IsUnlimited) return "Unlimited";

            var limits = new List<string>();
            if (RfqLimit.HasValue) limits.Add($"RFQs: {RfqLimit}");
            if (VehicleLimit.HasValue) limits.Add($"Vehicles: {VehicleLimit}");
            if (CarrierLimit.HasValue) limits.Add($"Carriers: {CarrierLimit}");

            return string.Join(", ", limits);
        }
    }
}
