using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.ValueObjects;
using AutoMapper;

namespace AnalyticsBIService.Application.Mappings;

/// <summary>
/// AutoMapper profile for analytics entities and DTOs
/// </summary>
public class AnalyticsMappingProfile : Profile
{
    public AnalyticsMappingProfile()
    {
        CreateMap<AnalyticsEvent, AnalyticsEventDto>()
            .ForMember(dest => dest.EventType, opt => opt.MapFrom(src => src.EventType.ToString()))
            .ForMember(dest => dest.DataSource, opt => opt.MapFrom(src => src.DataSource.ToString()))
            .ForMember(dest => dest.UserType, opt => opt.MapFrom(src => src.UserType.HasValue ? src.UserType.ToString() : null));

        CreateMap<Metric, MetricDto>()
            .ForMember(dest => dest.Type, opt => opt.MapFrom(src => src.Type.ToString()))
            .ForMember(dest => dest.Category, opt => opt.MapFrom(src => src.Category.ToString()))
            .ForMember(dest => dest.DataSource, opt => opt.MapFrom(src => src.DataSource.ToString()))
            .ForMember(dest => dest.UserType, opt => opt.MapFrom(src => src.UserType.HasValue ? src.UserType.ToString() : null))
            .ForMember(dest => dest.Value, opt => opt.MapFrom(src => src.Value.Value))
            .ForMember(dest => dest.Unit, opt => opt.MapFrom(src => src.Value.Unit))
            .ForMember(dest => dest.Timestamp, opt => opt.MapFrom(src => src.Value.Timestamp))
            .ForMember(dest => dest.Metadata, opt => opt.MapFrom(src => src.Value.Metadata))
            .ForMember(dest => dest.PeriodStart, opt => opt.MapFrom(src => src.Period.StartDate))
            .ForMember(dest => dest.PeriodEnd, opt => opt.MapFrom(src => src.Period.EndDate))
            .ForMember(dest => dest.Period, opt => opt.MapFrom(src => src.Period.Period.ToString()))
            .ForMember(dest => dest.Target, opt => opt.MapFrom(src => src.Target))
            .ForMember(dest => dest.PerformanceStatus, opt => opt.MapFrom(src => src.GetPerformanceStatus().ToString()))
            .ForMember(dest => dest.TargetAchievementPercentage, opt => opt.MapFrom(src => src.GetTargetAchievementPercentage()));

        CreateMap<KPITarget, KPITargetDto>();

        CreateMap<Dashboard, DashboardDto>()
            .ForMember(dest => dest.Type, opt => opt.MapFrom(src => src.Type.ToString()))
            .ForMember(dest => dest.UserType, opt => opt.MapFrom(src => src.UserType.HasValue ? src.UserType.ToString() : null))
            .ForMember(dest => dest.Widgets, opt => opt.MapFrom(src => src.Widgets));

        CreateMap<DashboardWidget, DashboardWidgetDto>()
            .ForMember(dest => dest.DefaultPeriodStart, opt => opt.MapFrom(src => src.DefaultTimePeriod != null ? src.DefaultTimePeriod.StartDate : (DateTime?)null))
            .ForMember(dest => dest.DefaultPeriodEnd, opt => opt.MapFrom(src => src.DefaultTimePeriod != null ? src.DefaultTimePeriod.EndDate : (DateTime?)null))
            .ForMember(dest => dest.DefaultPeriodType, opt => opt.MapFrom(src => src.DefaultTimePeriod != null ? src.DefaultTimePeriod.Period.ToString() : null));

        CreateMap<Report, ReportDto>()
            .ForMember(dest => dest.Type, opt => opt.MapFrom(src => src.Type.ToString()))
            .ForMember(dest => dest.UserType, opt => opt.MapFrom(src => src.UserType.ToString()))
            .ForMember(dest => dest.PeriodStart, opt => opt.MapFrom(src => src.Period.StartDate))
            .ForMember(dest => dest.PeriodEnd, opt => opt.MapFrom(src => src.Period.EndDate))
            .ForMember(dest => dest.Period, opt => opt.MapFrom(src => src.Period.Period.ToString()))
            .ForMember(dest => dest.Sections, opt => opt.MapFrom(src => src.Sections))
            .ForMember(dest => dest.Exports, opt => opt.MapFrom(src => src.Exports))
            .ForMember(dest => dest.IsExpired, opt => opt.MapFrom(src => src.IsExpired));

        CreateMap<ReportSection, ReportSectionDto>();

        CreateMap<ReportExport, ReportExportDto>()
            .ForMember(dest => dest.Format, opt => opt.MapFrom(src => src.Format.ToString()))
            .ForMember(dest => dest.IsExpired, opt => opt.MapFrom(src => src.IsExpired));

        CreateMap<Alert, AlertDto>()
            .ForMember(dest => dest.Severity, opt => opt.MapFrom(src => src.Severity.ToString()))
            .ForMember(dest => dest.ResolutionTime, opt => opt.MapFrom(src => src.GetResolutionTime()))
            .ForMember(dest => dest.ActiveTime, opt => opt.MapFrom(src => src.GetActiveTime()));

        CreateMap<AlertRule, AlertRuleDto>()
            .ForMember(dest => dest.Category, opt => opt.MapFrom(src => src.Category.ToString()))
            .ForMember(dest => dest.Severity, opt => opt.MapFrom(src => src.Severity.ToString()))
            .ForMember(dest => dest.UserType, opt => opt.MapFrom(src => src.UserType.ToString()));

        // Reverse mappings for commands/updates
        CreateMap<AnalyticsEventDto, AnalyticsEvent>()
            .ForMember(dest => dest.EventType, opt => opt.MapFrom(src => Enum.Parse<Domain.Enums.AnalyticsEventType>(src.EventType)))
            .ForMember(dest => dest.DataSource, opt => opt.MapFrom(src => Enum.Parse<Domain.Enums.DataSourceType>(src.DataSource)))
            .ForMember(dest => dest.UserType, opt => opt.MapFrom(src => !string.IsNullOrEmpty(src.UserType) ? Enum.Parse<Domain.Enums.UserType>(src.UserType) : (Domain.Enums.UserType?)null))
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore());

        CreateMap<MetricDto, Metric>()
            .ForMember(dest => dest.Type, opt => opt.MapFrom(src => Enum.Parse<Domain.Enums.MetricType>(src.Type)))
            .ForMember(dest => dest.Category, opt => opt.MapFrom(src => Enum.Parse<Domain.Enums.KPICategory>(src.Category)))
            .ForMember(dest => dest.DataSource, opt => opt.MapFrom(src => Enum.Parse<Domain.Enums.DataSourceType>(src.DataSource)))
            .ForMember(dest => dest.UserType, opt => opt.MapFrom(src => !string.IsNullOrEmpty(src.UserType) ? Enum.Parse<Domain.Enums.UserType>(src.UserType) : (Domain.Enums.UserType?)null))
            .ForMember(dest => dest.Value, opt => opt.MapFrom(src => MetricValue.Create(src.Value, Enum.Parse<Domain.Enums.MetricType>(src.Type), src.Unit, src.Timestamp, src.Metadata)))
            .ForMember(dest => dest.Period, opt => opt.MapFrom(src => TimePeriodValue.Create(src.PeriodStart, src.PeriodEnd, Enum.Parse<Domain.Enums.TimePeriod>(src.Period))))
            .ForMember(dest => dest.Target, opt => opt.MapFrom(src => src.Target))
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore());

        CreateMap<KPITargetDto, KPITarget>()
            .ConstructUsing(src => new KPITarget(
                src.TargetValue,
                src.Unit,
                src.IsHigherBetter,
                src.MinThreshold,
                src.MaxThreshold,
                src.WarningThreshold,
                src.CriticalThreshold));

        // Additional mappings for complex DTOs
        CreateMap<MetricValue, MetricDataPointDto>()
            .ForMember(dest => dest.Timestamp, opt => opt.MapFrom(src => src.Timestamp))
            .ForMember(dest => dest.Value, opt => opt.MapFrom(src => src.Value))
            .ForMember(dest => dest.Metadata, opt => opt.MapFrom(src => src.Metadata));

        // Custom value resolvers for complex calculations
        CreateMap<List<Metric>, MetricSummaryDto>()
            .ConvertUsing<MetricSummaryConverter>();
    }
}

/// <summary>
/// Custom converter for metric summary calculations
/// </summary>
public class MetricSummaryConverter : ITypeConverter<List<Metric>, MetricSummaryDto>
{
    public MetricSummaryDto Convert(List<Metric> source, MetricSummaryDto destination, ResolutionContext context)
    {
        if (!source.Any())
        {
            return new MetricSummaryDto();
        }

        var values = source.Select(m => m.Value.Value).ToList();

        var summary = new MetricSummaryDto
        {
            Average = values.Average(),
            Minimum = values.Min(),
            Maximum = values.Max(),
            Sum = values.Sum(),
            Count = values.Count
        };

        // Calculate standard deviation
        if (values.Count > 1)
        {
            var average = summary.Average;
            var sumOfSquares = values.Sum(v => (double)Math.Pow((double)(v - average), 2));
            summary.StandardDeviation = (decimal)Math.Sqrt(sumOfSquares / (values.Count - 1));
        }

        // Calculate trend direction
        if (values.Count >= 2)
        {
            var firstHalf = values.Take(values.Count / 2).Average();
            var secondHalf = values.Skip(values.Count / 2).Average();
            var difference = secondHalf - firstHalf;
            var threshold = firstHalf * 0.05m; // 5% threshold

            summary.TrendDirection = Math.Abs(difference) < threshold ? "Stable" :
                                   difference > 0 ? "Increasing" : "Decreasing";

            // Calculate growth rate
            var firstValue = values.First();
            var lastValue = values.Last();
            if (firstValue != 0)
            {
                summary.GrowthRate = (lastValue - firstValue) / firstValue;
            }
        }
        else
        {
            summary.TrendDirection = "Stable";
        }

        return summary;
    }
}
