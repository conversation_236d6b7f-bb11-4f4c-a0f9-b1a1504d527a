using Shared.Domain.Common;
using TripManagement.Domain.Enums;

namespace TripManagement.Domain.Events;

/// <summary>
/// Event raised when a delay alert is created
/// </summary>
public class DelayAlertCreatedEvent : DomainEvent
{
    public Guid DelayAlertId { get; }
    public Guid TripId { get; }
    public Guid TransportCompanyId { get; }
    public DelayAlertType AlertType { get; }
    public DelayAlertSeverity Severity { get; }
    public TimeSpan DelayDuration { get; }
    public DateTime ScheduledTime { get; }

    public DelayAlertCreatedEvent(
        Guid delayAlertId,
        Guid tripId,
        Guid transportCompanyId,
        DelayAlertType alertType,
        DelayAlertSeverity severity,
        TimeSpan delayDuration,
        DateTime scheduledTime)
    {
        DelayAlertId = delayAlertId;
        TripId = tripId;
        TransportCompanyId = transportCompanyId;
        AlertType = alertType;
        Severity = severity;
        DelayDuration = delayDuration;
        ScheduledTime = scheduledTime;
    }
}

/// <summary>
/// Event raised when a delay alert is updated
/// </summary>
public class DelayAlertUpdatedEvent : DomainEvent
{
    public Guid DelayAlertId { get; }
    public Guid TripId { get; }
    public TimeSpan PreviousDelayDuration { get; }
    public TimeSpan NewDelayDuration { get; }
    public DateTime? EstimatedTime { get; }

    public DelayAlertUpdatedEvent(
        Guid delayAlertId,
        Guid tripId,
        TimeSpan previousDelayDuration,
        TimeSpan newDelayDuration,
        DateTime? estimatedTime)
    {
        DelayAlertId = delayAlertId;
        TripId = tripId;
        PreviousDelayDuration = previousDelayDuration;
        NewDelayDuration = newDelayDuration;
        EstimatedTime = estimatedTime;
    }
}

/// <summary>
/// Event raised when delay alert severity changes
/// </summary>
public class DelayAlertSeverityChangedEvent : DomainEvent
{
    public Guid DelayAlertId { get; }
    public Guid TripId { get; }
    public DelayAlertSeverity PreviousSeverity { get; }
    public DelayAlertSeverity NewSeverity { get; }
    public TimeSpan PreviousDelayDuration { get; }
    public TimeSpan NewDelayDuration { get; }

    public DelayAlertSeverityChangedEvent(
        Guid delayAlertId,
        Guid tripId,
        DelayAlertSeverity previousSeverity,
        DelayAlertSeverity newSeverity,
        TimeSpan previousDelayDuration,
        TimeSpan newDelayDuration)
    {
        DelayAlertId = delayAlertId;
        TripId = tripId;
        PreviousSeverity = previousSeverity;
        NewSeverity = newSeverity;
        PreviousDelayDuration = previousDelayDuration;
        NewDelayDuration = newDelayDuration;
    }
}

/// <summary>
/// Event raised when a delay alert is escalated
/// </summary>
public class DelayAlertEscalatedEvent : DomainEvent
{
    public Guid DelayAlertId { get; }
    public Guid TripId { get; }
    public int EscalationLevel { get; }
    public string Reason { get; }
    public DateTime EscalatedAt { get; }

    public DelayAlertEscalatedEvent(
        Guid delayAlertId,
        Guid tripId,
        int escalationLevel,
        string reason,
        DateTime escalatedAt)
    {
        DelayAlertId = delayAlertId;
        TripId = tripId;
        EscalationLevel = escalationLevel;
        Reason = reason;
        EscalatedAt = escalatedAt;
    }
}

/// <summary>
/// Event raised when a delay alert is acknowledged
/// </summary>
public class DelayAlertAcknowledgedEvent : DomainEvent
{
    public Guid DelayAlertId { get; }
    public Guid TripId { get; }
    public Guid AcknowledgedByUserId { get; }
    public DateTime AcknowledgedAt { get; }
    public string? Notes { get; }

    public DelayAlertAcknowledgedEvent(
        Guid delayAlertId,
        Guid tripId,
        Guid acknowledgedByUserId,
        DateTime acknowledgedAt,
        string? notes)
    {
        DelayAlertId = delayAlertId;
        TripId = tripId;
        AcknowledgedByUserId = acknowledgedByUserId;
        AcknowledgedAt = acknowledgedAt;
        Notes = notes;
    }
}

/// <summary>
/// Event raised when a delay alert is resolved
/// </summary>
public class DelayAlertResolvedEvent : DomainEvent
{
    public Guid DelayAlertId { get; }
    public Guid TripId { get; }
    public DateTime ResolvedAt { get; }
    public string ResolutionNotes { get; }
    public DateTime? ActualTime { get; }

    public DelayAlertResolvedEvent(
        Guid delayAlertId,
        Guid tripId,
        DateTime resolvedAt,
        string resolutionNotes,
        DateTime? actualTime)
    {
        DelayAlertId = delayAlertId;
        TripId = tripId;
        ResolvedAt = resolvedAt;
        ResolutionNotes = resolutionNotes;
        ActualTime = actualTime;
    }
}

/// <summary>
/// Event raised when a delay alert is cancelled
/// </summary>
public class DelayAlertCancelledEvent : DomainEvent
{
    public Guid DelayAlertId { get; }
    public Guid TripId { get; }
    public string Reason { get; }
    public DateTime CancelledAt { get; }

    public DelayAlertCancelledEvent(
        Guid delayAlertId,
        Guid tripId,
        string reason,
        DateTime cancelledAt)
    {
        DelayAlertId = delayAlertId;
        TripId = tripId;
        Reason = reason;
        CancelledAt = cancelledAt;
    }
}

/// <summary>
/// Event raised when a notification is sent for a delay alert
/// </summary>
public class DelayAlertNotificationSentEvent : DomainEvent
{
    public Guid DelayAlertId { get; }
    public Guid TripId { get; }
    public string Channel { get; }
    public List<string> Recipients { get; }
    public DateTime SentAt { get; }

    public DelayAlertNotificationSentEvent(
        Guid delayAlertId,
        Guid tripId,
        string channel,
        List<string> recipients,
        DateTime sentAt)
    {
        DelayAlertId = delayAlertId;
        TripId = tripId;
        Channel = channel;
        Recipients = recipients;
        SentAt = sentAt;
    }
}

/// <summary>
/// Event raised when impact assessment is performed for a delay alert
/// </summary>
public class DelayAlertImpactAssessedEvent : DomainEvent
{
    public Guid DelayAlertId { get; }
    public Guid TripId { get; }
    public decimal FinancialImpact { get; }
    public string CustomerImpact { get; }
    public string OperationalImpact { get; }

    public DelayAlertImpactAssessedEvent(
        Guid delayAlertId,
        Guid tripId,
        decimal financialImpact,
        string customerImpact,
        string operationalImpact)
    {
        DelayAlertId = delayAlertId;
        TripId = tripId;
        FinancialImpact = financialImpact;
        CustomerImpact = customerImpact;
        OperationalImpact = operationalImpact;
    }
}

/// <summary>
/// Event raised when a delay alert expires
/// </summary>
public class DelayAlertExpiredEvent : DomainEvent
{
    public Guid DelayAlertId { get; }
    public Guid TripId { get; }
    public DateTime ExpiredAt { get; }

    public DelayAlertExpiredEvent(
        Guid delayAlertId,
        Guid tripId,
        DateTime expiredAt)
    {
        DelayAlertId = delayAlertId;
        TripId = tripId;
        ExpiredAt = expiredAt;
    }
}


