using System;
using System.Threading.Tasks;
using Identity.Application.Common.Models;
using Identity.Domain.Entities;

namespace Identity.Application.Common.Interfaces
{
    public interface ITokenService
    {
        Task<TokenResponse> GenerateTokensAsync(Guid userId);
        Task<TokenResponse> RefreshTokenAsync(string refreshToken);
        Task RevokeTokenAsync(string refreshToken);
        Task<bool> ValidateTokenAsync(string token);
        Task<Guid> GetUserIdFromTokenAsync(string token);
    }
}
