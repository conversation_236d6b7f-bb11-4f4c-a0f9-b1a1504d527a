using Shared.Domain.Common;
using MobileWorkflow.Domain.Enums;
using MobileWorkflow.Domain.Events;

namespace MobileWorkflow.Domain.Entities;

public class WorkflowStep : AggregateRoot
{
    public Guid WorkflowId { get; private set; }
    public string Name { get; private set; }
    public string DisplayName { get; private set; }
    public string Description { get; private set; }
    public string StepType { get; private set; } // Task, Decision, Parallel, Loop, Delay, etc.
    public int Order { get; private set; }
    public bool IsRequired { get; private set; }
    public Dictionary<string, object> Configuration { get; private set; }
    public Dictionary<string, object> InputSchema { get; private set; }
    public Dictionary<string, object> OutputSchema { get; private set; }
    public Dictionary<string, object> Conditions { get; private set; }
    public List<string> NextSteps { get; private set; }
    public List<string> PreviousSteps { get; private set; }
    public string? ParentStepId { get; private set; }
    public bool IsParallel { get; private set; }
    public TimeSpan? Timeout { get; private set; }
    public int MaxRetries { get; private set; }
    public Dictionary<string, object> RetryPolicy { get; private set; }
    public Dictionary<string, object> ErrorHandling { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    public virtual Workflow Workflow { get; private set; } = null!;
    public virtual ICollection<WorkflowStepExecution> Executions { get; private set; } = new List<WorkflowStepExecution>();

    private WorkflowStep() { } // EF Core

    public WorkflowStep(
        Guid workflowId,
        string name,
        string displayName,
        string description,
        string stepType,
        int order,
        Dictionary<string, object> configuration)
    {
        WorkflowId = workflowId;
        Name = name ?? throw new ArgumentNullException(nameof(name));
        DisplayName = displayName ?? throw new ArgumentNullException(nameof(displayName));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        StepType = stepType ?? throw new ArgumentNullException(nameof(stepType));
        Order = order;
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));

        IsRequired = true;
        IsParallel = false;
        MaxRetries = 3;
        InputSchema = new Dictionary<string, object>();
        OutputSchema = new Dictionary<string, object>();
        Conditions = new Dictionary<string, object>();
        NextSteps = new List<string>();
        PreviousSteps = new List<string>();
        RetryPolicy = new Dictionary<string, object>();
        ErrorHandling = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();

        AddDomainEvent(new WorkflowStepCreatedEvent(Id, WorkflowId, Name, StepType, Order));
    }

    public void UpdateConfiguration(Dictionary<string, object> newConfiguration)
    {
        Configuration = newConfiguration ?? throw new ArgumentNullException(nameof(newConfiguration));

        AddDomainEvent(new WorkflowStepConfigurationUpdatedEvent(Id, WorkflowId, Name));
    }

    public void SetInputSchema(Dictionary<string, object> schema)
    {
        InputSchema = schema ?? throw new ArgumentNullException(nameof(schema));

        AddDomainEvent(new WorkflowStepInputSchemaSetEvent(Id, WorkflowId, Name));
    }

    public void SetOutputSchema(Dictionary<string, object> schema)
    {
        OutputSchema = schema ?? throw new ArgumentNullException(nameof(schema));

        AddDomainEvent(new WorkflowStepOutputSchemaSetEvent(Id, WorkflowId, Name));
    }

    public void AddCondition(string conditionName, object conditionValue)
    {
        Conditions[conditionName] = conditionValue;

        AddDomainEvent(new WorkflowStepConditionAddedEvent(Id, WorkflowId, Name, conditionName));
    }

    public void RemoveCondition(string conditionName)
    {
        if (Conditions.Remove(conditionName))
        {
            AddDomainEvent(new WorkflowStepConditionRemovedEvent(Id, WorkflowId, Name, conditionName));
        }
    }

    public void AddNextStep(string stepName)
    {
        if (!NextSteps.Contains(stepName, StringComparer.OrdinalIgnoreCase))
        {
            NextSteps.Add(stepName);

            AddDomainEvent(new WorkflowStepNextStepAddedEvent(Id, WorkflowId, Name, stepName));
        }
    }

    public void RemoveNextStep(string stepName)
    {
        if (NextSteps.RemoveAll(s => s.Equals(stepName, StringComparison.OrdinalIgnoreCase)) > 0)
        {
            AddDomainEvent(new WorkflowStepNextStepRemovedEvent(Id, WorkflowId, Name, stepName));
        }
    }

    public void AddPreviousStep(string stepName)
    {
        if (!PreviousSteps.Contains(stepName, StringComparer.OrdinalIgnoreCase))
        {
            PreviousSteps.Add(stepName);

            AddDomainEvent(new WorkflowStepPreviousStepAddedEvent(Id, WorkflowId, Name, stepName));
        }
    }

    public void RemovePreviousStep(string stepName)
    {
        if (PreviousSteps.RemoveAll(s => s.Equals(stepName, StringComparison.OrdinalIgnoreCase)) > 0)
        {
            AddDomainEvent(new WorkflowStepPreviousStepRemovedEvent(Id, WorkflowId, Name, stepName));
        }
    }

    public void SetAsParallel(string? parentStepId = null)
    {
        IsParallel = true;
        ParentStepId = parentStepId;

        AddDomainEvent(new WorkflowStepSetAsParallelEvent(Id, WorkflowId, Name, ParentStepId));
    }

    public void SetAsSequential()
    {
        IsParallel = false;
        ParentStepId = null;

        AddDomainEvent(new WorkflowStepSetAsSequentialEvent(Id, WorkflowId, Name));
    }

    public void SetTimeout(TimeSpan timeout)
    {
        Timeout = timeout;

        AddDomainEvent(new WorkflowStepTimeoutSetEvent(Id, WorkflowId, Name, timeout.TotalMinutes));
    }

    public void SetRetryPolicy(int maxRetries, Dictionary<string, object> retryPolicy)
    {
        MaxRetries = maxRetries;
        RetryPolicy = retryPolicy ?? throw new ArgumentNullException(nameof(retryPolicy));

        AddDomainEvent(new WorkflowStepRetryPolicySetEvent(Id, WorkflowId, Name, MaxRetries));
    }

    public void SetErrorHandling(Dictionary<string, object> errorHandling)
    {
        ErrorHandling = errorHandling ?? throw new ArgumentNullException(nameof(errorHandling));

        AddDomainEvent(new WorkflowStepErrorHandlingSetEvent(Id, WorkflowId, Name));
    }

    public void SetAsRequired()
    {
        IsRequired = true;

        AddDomainEvent(new WorkflowStepSetAsRequiredEvent(Id, WorkflowId, Name));
    }

    public void SetAsOptional()
    {
        IsRequired = false;

        AddDomainEvent(new WorkflowStepSetAsOptionalEvent(Id, WorkflowId, Name));
    }

    public void UpdateOrder(int newOrder)
    {
        var oldOrder = Order;
        Order = newOrder;

        AddDomainEvent(new WorkflowStepOrderUpdatedEvent(Id, WorkflowId, Name, oldOrder, newOrder));
    }

    public bool EvaluateConditions(Dictionary<string, object> context)
    {
        if (Conditions.Count == 0)
        {
            return true; // No conditions means always proceed
        }

        foreach (var condition in Conditions)
        {
            if (!EvaluateCondition(condition.Key, condition.Value, context))
            {
                return false;
            }
        }

        return true;
    }

    private bool EvaluateCondition(string conditionName, object conditionValue, Dictionary<string, object> context)
    {
        // Simple condition evaluation - can be enhanced with expression engine
        if (conditionValue is Dictionary<string, object> conditionDef)
        {
            if (conditionDef.TryGetValue("operator", out var op) && op is string operatorStr)
            {
                if (conditionDef.TryGetValue("field", out var field) && field is string fieldName)
                {
                    if (context.TryGetValue(fieldName, out var contextValue))
                    {
                        if (conditionDef.TryGetValue("value", out var expectedValue))
                        {
                            return EvaluateOperator(operatorStr, contextValue, expectedValue);
                        }
                    }
                }
            }
        }

        return true; // Default to true if condition cannot be evaluated
    }

    private bool EvaluateOperator(string operatorStr, object contextValue, object expectedValue)
    {
        return operatorStr.ToLowerInvariant() switch
        {
            "equals" or "eq" => Equals(contextValue, expectedValue),
            "not_equals" or "ne" => !Equals(contextValue, expectedValue),
            "greater_than" or "gt" => CompareValues(contextValue, expectedValue) > 0,
            "greater_than_or_equal" or "gte" => CompareValues(contextValue, expectedValue) >= 0,
            "less_than" or "lt" => CompareValues(contextValue, expectedValue) < 0,
            "less_than_or_equal" or "lte" => CompareValues(contextValue, expectedValue) <= 0,
            "contains" => contextValue?.ToString()?.Contains(expectedValue?.ToString() ?? "") == true,
            "starts_with" => contextValue?.ToString()?.StartsWith(expectedValue?.ToString() ?? "") == true,
            "ends_with" => contextValue?.ToString()?.EndsWith(expectedValue?.ToString() ?? "") == true,
            "is_null" => contextValue == null,
            "is_not_null" => contextValue != null,
            _ => true
        };
    }

    private int CompareValues(object value1, object value2)
    {
        if (value1 is IComparable comparable1 && value2 is IComparable comparable2)
        {
            try
            {
                return comparable1.CompareTo(comparable2);
            }
            catch
            {
                return 0;
            }
        }
        return 0;
    }

    public List<string> GetNextStepsForCondition(Dictionary<string, object> context)
    {
        if (EvaluateConditions(context))
        {
            return NextSteps.ToList();
        }

        // Check for conditional next steps
        if (Configuration.TryGetValue("conditional_next_steps", out var conditionalSteps) &&
            conditionalSteps is Dictionary<string, object> conditionalStepsDict)
        {
            foreach (var kvp in conditionalStepsDict)
            {
                if (kvp.Value is Dictionary<string, object> stepCondition)
                {
                    if (EvaluateCondition(kvp.Key, stepCondition, context))
                    {
                        if (stepCondition.TryGetValue("next_steps", out var steps) && steps is List<string> stepsList)
                        {
                            return stepsList;
                        }
                    }
                }
            }
        }

        return new List<string>();
    }

    public bool CanExecute(Dictionary<string, object> context)
    {
        return EvaluateConditions(context);
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetConfiguration<T>(string key, T? defaultValue = default)
    {
        if (Configuration.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }
}


