# 📚 TLI Platform API Documentation

## 📋 Overview

This directory contains comprehensive API documentation for all TLI microservices. Each service provides RESTful APIs with consistent patterns, authentication, and error handling.

## 🏗️ Architecture Overview

The TLI platform consists of 13 core microservices plus an API Gateway, all accessible through a unified endpoint structure:

```
API Gateway (Port 5000) → Individual Services
├── /api/v1/identity/*     → Identity Service (Port 5001)
├── /api/v1/users/*        → User Management (Port 5002)
├── /api/v1/subscriptions/* → Subscription Management (Port 5003)
├── /api/v1/orders/*       → Order Management (Port 5004)
├── /api/v1/trips/*        → Trip Management (Port 5005)
├── /api/v1/fleet/*        → Network & Fleet (Port 5006)
├── /api/v1/payments/*     → Financial & Payment (Port 5007)
├── /api/v1/notifications/* → Communication & Notification (Port 5008)
├── /api/v1/analytics/*    → Analytics & BI (Port 5009)
├── /api/v1/storage/*      → Data & Storage (Port 5010)
├── /api/v1/monitoring/*   → Monitoring & Observability (Port 5011)
├── /api/v1/audit/*        → Audit & Compliance (Port 5012)
└── /api/v1/mobile/*       → Mobile & Workflow (Port 5013)
```

## 📖 Service Documentation

### Core Business Services

| Service                                                         | Status          | Documentation                                   | Description                             |
| --------------------------------------------------------------- | --------------- | ----------------------------------------------- | --------------------------------------- |
| **[User Management](./User-Management-API.md)**                 | ✅ 85% Complete | [📖 API Docs](./User-Management-API.md)         | User profiles, KYC, document management |
| **[Order Management](./Order-Management-API.md)**               | ✅ 96% Complete | [📖 API Docs](./Order-Management-API.md)        | RFQ, bidding, order processing          |
| **[Trip Management](./Trip-Management-API.md)**                 | ✅ 94% Complete | [📖 API Docs](./Trip-Management-API.md)         | Trip tracking, POD, route optimization  |
| **[Subscription Management](./Subscription-Management-API.md)** | ✅ 98% Complete | [📖 API Docs](./Subscription-Management-API.md) | Plans, billing, feature flags           |

### Supporting Services

| Service                                                                 | Status          | Documentation                                      | Description                                |
| ----------------------------------------------------------------------- | --------------- | -------------------------------------------------- | ------------------------------------------ |
| **[Network & Fleet Management](./Network-Fleet-Management-API.md)**     | ✅ 95% Complete | [📖 API Docs](./Network-Fleet-Management-API.md)   | Vehicle, driver, carrier management        |
| **[Financial & Payment](./Financial-Payment-API.md)**                   | ✅ 94% Complete | [📖 API Docs](./Financial-Payment-API.md)          | Escrow, payments, settlements, tax config  |
| **[Communication & Notification](./Communication-Notification-API.md)** | ✅ 80% Complete | [📖 API Docs](./Communication-Notification-API.md) | WhatsApp, SMS, email, push, chat           |
| **[Analytics & BI](./Analytics-BI-API.md)**                             | ✅ 60% Complete | [📖 API Docs](./Analytics-BI-API.md)               | Dashboards, reports, predictive analytics  |
| **[Data & Storage](./Data-Storage-API.md)**                             | ✅ 75% Complete | [📖 API Docs](./Data-Storage-API.md)               | File management, CDN, search, archiving    |
| **[Monitoring & Observability](./Monitoring-Observability-API.md)**     | ✅ 65% Complete | [📖 API Docs](./Monitoring-Observability-API.md)   | Health checks, metrics, SLA monitoring     |
| **[Audit & Compliance](./Audit-Compliance-API.md)**                     | ✅ 70% Complete | [📖 API Docs](./Audit-Compliance-API.md)           | Audit trails, compliance reporting         |
| **[Mobile & Workflow](./Mobile-Workflow-API.md)**                       | ✅ 60% Complete | [📖 API Docs](./Mobile-Workflow-API.md)            | Mobile APIs, workflow engine, offline sync |

## 🔐 Authentication

All APIs use JWT Bearer token authentication:

```http
Authorization: Bearer <your-jwt-token>
```

### Getting Started

1. **Register/Login** via Identity Service
2. **Obtain JWT tokens** (access + refresh)
3. **Include Bearer token** in all API requests
4. **Refresh tokens** automatically when expired

**Example:**

```typescript
// Login to get tokens
const response = await fetch('/api/v1/identity/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    username: '<EMAIL>',
    password: 'password123',
  }),
})

const { accessToken, refreshToken } = await response.json()

// Use token in subsequent requests
const ordersResponse = await fetch('/api/v1/orders', {
  headers: {
    Authorization: `Bearer ${accessToken}`,
    'Content-Type': 'application/json',
  },
})
```

## 📊 Common Patterns

### Request/Response Format

All APIs follow consistent patterns:

**Request Headers:**

```http
Content-Type: application/json
Authorization: Bearer <token>
Accept: application/json
```

**Success Response:**

```json
{
  "data": {
    /* response data */
  },
  "success": true,
  "message": "Operation completed successfully",
  "timestamp": "2024-01-01T10:00:00Z"
}
```

**Error Response:**

```json
{
  "error": {
    "code": "ERROR_CODE",
    "message": "Human-readable error message",
    "details": "Additional error context",
    "timestamp": "2024-01-01T10:00:00Z",
    "traceId": "trace-uuid"
  }
}
```

### Pagination

List endpoints support consistent pagination:

**Request:**

```http
GET /api/v1/orders?page=1&pageSize=20&sortBy=createdAt&sortDirection=desc
```

**Response:**

```json
{
  "data": [
    /* array of items */
  ],
  "pagination": {
    "currentPage": 1,
    "pageSize": 20,
    "totalItems": 150,
    "totalPages": 8,
    "hasNextPage": true,
    "hasPreviousPage": false
  }
}
```

### Filtering & Search

Most list endpoints support filtering:

**Common Parameters:**

- `searchTerm`: Text search across relevant fields
- `status`: Filter by status
- `fromDate`/`toDate`: Date range filtering
- `sortBy`: Field to sort by
- `sortDirection`: `asc` or `desc`

**Example:**

```http
GET /api/v1/orders?searchTerm=electronics&status=InProgress&fromDate=2024-01-01&sortBy=createdAt&sortDirection=desc
```

## 🔄 Real-time Features

All services provide SignalR hubs for real-time updates:

### Hub Connections

| Service          | Hub URL                     | Purpose                        |
| ---------------- | --------------------------- | ------------------------------ |
| User Management  | `/api/v1/users/hub`         | Profile updates, KYC status    |
| Order Management | `/api/v1/orders/hub`        | RFQ/bid/order updates          |
| Trip Management  | `/api/v1/trips/hub`         | Location tracking, POD updates |
| Notifications    | `/api/v1/notifications/hub` | Real-time notifications        |

### Example Usage

```typescript
import * as signalR from '@microsoft/signalr'

// Connect to order management hub
const connection = new signalR.HubConnectionBuilder()
  .withUrl('/api/v1/orders/hub', {
    accessTokenFactory: () => getAccessToken(),
  })
  .build()

// Listen for events
connection.on('BidSubmitted', (data) => {
  console.log('New bid received:', data)
  // Update UI
})

// Start connection
await connection.start()
```

## 📱 Mobile Considerations

### Optimized Endpoints

Mobile-specific optimizations:

- Reduced payload sizes
- Offline-capable endpoints
- Batch operations
- Progressive data loading

### Example Mobile Patterns

```typescript
// Batch requests for mobile
POST /api/v1/mobile/batch
{
  "requests": [
    { "method": "GET", "url": "/orders/summary" },
    { "method": "GET", "url": "/trips/active" },
    { "method": "GET", "url": "/notifications/unread" }
  ]
}

// Offline sync
POST /api/v1/mobile/sync
{
  "lastSyncTimestamp": "2024-01-01T10:00:00Z",
  "pendingActions": [
    { "action": "updateLocation", "data": {...} },
    { "action": "submitPOD", "data": {...} }
  ]
}
```

## 🧪 Testing APIs

### Postman Collection

Import our Postman collection for easy API testing:

- [Download Collection](./postman/TLI-Platform-APIs.postman_collection.json)
- [Environment Variables](./postman/TLI-Environment.postman_environment.json)

### cURL Examples

```bash
# Login
curl -X POST http://localhost:5000/api/v1/identity/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"<EMAIL>","password":"password123"}'

# Get orders (with token)
curl -X GET http://localhost:5000/api/v1/orders \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Accept: application/json"

# Create RFQ
curl -X POST http://localhost:5000/api/v1/orders/rfq \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d @rfq-payload.json
```

## 🔧 Development Tools

### OpenAPI Specifications

Each service provides OpenAPI 3.0 specifications:

- **Swagger UI**: Available at `{service-url}/swagger`
- **OpenAPI JSON**: Available at `{service-url}/swagger/v1/swagger.json`

### SDK Generation

Generate client SDKs using OpenAPI specifications:

```bash
# Generate TypeScript client
npx @openapitools/openapi-generator-cli generate \
  -i http://localhost:5002/swagger/v1/swagger.json \
  -g typescript-axios \
  -o ./src/clients/user-management

# Generate C# client
dotnet tool install --global Microsoft.dotnet-openapi
dotnet openapi add url http://localhost:5002/swagger/v1/swagger.json
```

## 📈 Rate Limits

### Default Limits

| User Type         | Requests/Minute | Burst Limit |
| ----------------- | --------------- | ----------- |
| **Anonymous**     | 100             | 200         |
| **Authenticated** | 1,000           | 2,000       |
| **Premium**       | 5,000           | 10,000      |
| **Admin**         | 10,000          | 20,000      |

### Rate Limit Headers

```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: **********
```

## 🆘 Support & Troubleshooting

### Common Issues

1. **401 Unauthorized**: Check token validity and permissions
2. **429 Rate Limited**: Implement exponential backoff
3. **422 Validation Error**: Check request payload format
4. **500 Server Error**: Check service health and retry

### Getting Help

- **Documentation**: This comprehensive guide
- **API Status**: [Status Page](http://localhost:5011/health)
- **Support**: Contact development team
- **Issues**: Report via issue tracking system

### Health Checks

Monitor service health:

```http
GET /api/v1/monitoring/health
```

Response:

```json
{
  "status": "Healthy",
  "services": {
    "UserManagement": "Healthy",
    "OrderManagement": "Healthy",
    "TripManagement": "Healthy"
  },
  "timestamp": "2024-01-01T10:00:00Z"
}
```

## 🔄 API Versioning

All APIs are versioned using URL path versioning:

- Current version: `v1`
- Future versions: `v2`, `v3`, etc.
- Backward compatibility maintained for at least 12 months

**Example:**

```
/api/v1/orders  (current)
/api/v2/orders  (future)
```

## 📋 Next Steps

1. **Start with Authentication**: [Authentication Guide](../Authentication-Guide.md)
2. **Explore Core APIs**: Begin with User Management and Order Management
3. **Implement Real-time Features**: [Real-time Features Guide](../Real-time-Features.md)
4. **Handle Errors Gracefully**: [Error Handling Guide](../Error-Handling.md)
5. **Optimize Performance**: [Performance Guidelines](../Performance-Guidelines.md)

---

**Last Updated**: January 2024  
**API Version**: v1  
**Documentation Version**: 1.0
