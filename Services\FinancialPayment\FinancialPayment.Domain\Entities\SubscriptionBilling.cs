using FinancialPayment.Domain.ValueObjects;
using Shared.Domain.Common;

namespace FinancialPayment.Domain.Entities;

public class SubscriptionPlan : AggregateRoot
{
    public string PlanName { get; private set; }
    public string Description { get; private set; }
    public Money Price { get; private set; }
    public BillingInterval BillingInterval { get; private set; }
    public int BillingIntervalCount { get; private set; } // e.g., 1 for monthly, 3 for quarterly
    public int? TrialPeriodDays { get; private set; }
    public bool IsActive { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public string? PlanCode { get; private set; }
    public Dictionary<string, object> Features { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    private readonly List<SubscriptionPlanTier> _tiers = new();
    public IReadOnlyList<SubscriptionPlanTier> Tiers => _tiers.AsReadOnly();

    private SubscriptionPlan() { }

    public SubscriptionPlan(
        string planName,
        string description,
        Money price,
        BillingInterval billingInterval,
        int billingIntervalCount = 1,
        int? trialPeriodDays = null,
        string? planCode = null)
    {
        if (string.IsNullOrWhiteSpace(planName))
            throw new ArgumentException("Plan name cannot be empty", nameof(planName));

        if (billingIntervalCount <= 0)
            throw new ArgumentException("Billing interval count must be positive", nameof(billingIntervalCount));

        PlanName = planName;
        Description = description;
        Price = price;
        BillingInterval = billingInterval;
        BillingIntervalCount = billingIntervalCount;
        TrialPeriodDays = trialPeriodDays;
        IsActive = true;
        CreatedAt = DateTime.UtcNow;
        PlanCode = planCode ?? GeneratePlanCode();
        Features = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();
    }

    public void UpdatePrice(Money newPrice)
    {
        Price = newPrice;
        SetUpdatedAt();
    }

    public void AddTier(string tierName, Money tierPrice, int? userLimit = null, Dictionary<string, object>? features = null)
    {
        var tier = new SubscriptionPlanTier(Id, tierName, tierPrice, userLimit, features);
        _tiers.Add(tier);
        SetUpdatedAt();
    }

    public void Activate() => IsActive = true;
    public void Deactivate() => IsActive = false;

    public DateTime CalculateNextBillingDate(DateTime currentDate)
    {
        return BillingInterval switch
        {
            BillingInterval.Daily => currentDate.AddDays(BillingIntervalCount),
            BillingInterval.Weekly => currentDate.AddDays(7 * BillingIntervalCount),
            BillingInterval.Monthly => currentDate.AddMonths(BillingIntervalCount),
            BillingInterval.Quarterly => currentDate.AddMonths(3 * BillingIntervalCount),
            BillingInterval.Yearly => currentDate.AddYears(BillingIntervalCount),
            _ => currentDate.AddMonths(BillingIntervalCount)
        };
    }

    private static string GeneratePlanCode()
    {
        return $"PLAN_{DateTime.UtcNow:yyyyMMdd}_{Guid.NewGuid():N}";
    }
}

public class SubscriptionPlanTier : BaseEntity
{
    public Guid SubscriptionPlanId { get; private set; }
    public string TierName { get; private set; }
    public Money TierPrice { get; private set; }
    public int? UserLimit { get; private set; }
    public Dictionary<string, object> Features { get; private set; }

    private SubscriptionPlanTier() { }

    public SubscriptionPlanTier(
        Guid subscriptionPlanId,
        string tierName,
        Money tierPrice,
        int? userLimit = null,
        Dictionary<string, object>? features = null)
    {
        SubscriptionPlanId = subscriptionPlanId;
        TierName = tierName;
        TierPrice = tierPrice;
        UserLimit = userLimit;
        Features = features ?? new Dictionary<string, object>();
    }
}

public class Subscription : AggregateRoot
{
    public Guid UserId { get; private set; }
    public Guid SubscriptionPlanId { get; private set; }
    public SubscriptionStatus Status { get; private set; }
    public DateTime StartDate { get; private set; }
    public DateTime? EndDate { get; private set; }
    public DateTime NextBillingDate { get; private set; }
    public DateTime? TrialEndDate { get; private set; }
    public Money CurrentPrice { get; private set; }
    public string? PaymentMethodId { get; private set; }
    public string? CustomerId { get; private set; }
    public int BillingCycleCount { get; private set; }
    public DateTime? CancelledAt { get; private set; }
    public string? CancellationReason { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    private readonly List<BillingCycle> _billingCycles = new();
    public IReadOnlyList<BillingCycle> BillingCycles => _billingCycles.AsReadOnly();

    private readonly List<SubscriptionEvent> _events = new();
    public IReadOnlyList<SubscriptionEvent> Events => _events.AsReadOnly();

    private Subscription() { }

    public Subscription(
        Guid userId,
        Guid subscriptionPlanId,
        Money currentPrice,
        DateTime startDate,
        DateTime nextBillingDate,
        string? paymentMethodId = null,
        string? customerId = null,
        DateTime? trialEndDate = null)
    {
        UserId = userId;
        SubscriptionPlanId = subscriptionPlanId;
        Status = SubscriptionStatus.Active;
        StartDate = startDate;
        NextBillingDate = nextBillingDate;
        TrialEndDate = trialEndDate;
        CurrentPrice = currentPrice;
        PaymentMethodId = paymentMethodId;
        CustomerId = customerId;
        BillingCycleCount = 0;
        Metadata = new Dictionary<string, object>();

        AddEvent(SubscriptionEventType.Created, "Subscription created");
    }

    public void UpdatePaymentMethod(string paymentMethodId)
    {
        PaymentMethodId = paymentMethodId;
        AddEvent(SubscriptionEventType.PaymentMethodUpdated, $"Payment method updated to {paymentMethodId}");
        SetUpdatedAt();
    }

    public void Pause(string reason)
    {
        if (Status != SubscriptionStatus.Active)
            throw new InvalidOperationException("Only active subscriptions can be paused");

        Status = SubscriptionStatus.Paused;
        AddEvent(SubscriptionEventType.Paused, reason);
        SetUpdatedAt();
    }

    public void Resume()
    {
        if (Status != SubscriptionStatus.Paused)
            throw new InvalidOperationException("Only paused subscriptions can be resumed");

        Status = SubscriptionStatus.Active;
        AddEvent(SubscriptionEventType.Resumed, "Subscription resumed");
        SetUpdatedAt();
    }

    public void Cancel(string reason)
    {
        if (Status == SubscriptionStatus.Cancelled)
            throw new InvalidOperationException("Subscription is already cancelled");

        Status = SubscriptionStatus.Cancelled;
        CancelledAt = DateTime.UtcNow;
        CancellationReason = reason;
        EndDate = DateTime.UtcNow;
        AddEvent(SubscriptionEventType.Cancelled, reason);
        SetUpdatedAt();
    }

    public void Expire()
    {
        Status = SubscriptionStatus.Expired;
        EndDate = DateTime.UtcNow;
        AddEvent(SubscriptionEventType.Expired, "Subscription expired");
        SetUpdatedAt();
    }

    public BillingCycle StartNewBillingCycle(DateTime cycleStart, DateTime cycleEnd, Money amount)
    {
        BillingCycleCount++;
        var billingCycle = new BillingCycle(
            Id,
            BillingCycleCount,
            cycleStart,
            cycleEnd,
            amount);

        _billingCycles.Add(billingCycle);
        NextBillingDate = cycleEnd;
        SetUpdatedAt();

        return billingCycle;
    }

    public void UpdateNextBillingDate(DateTime nextBillingDate)
    {
        NextBillingDate = nextBillingDate;
        SetUpdatedAt();
    }

    public void UpdatePrice(Money newPrice, string reason)
    {
        CurrentPrice = newPrice;
        AddEvent(SubscriptionEventType.PriceChanged, $"Price updated to {newPrice}. Reason: {reason}");
        SetUpdatedAt();
    }

    private void AddEvent(SubscriptionEventType eventType, string description)
    {
        var subscriptionEvent = new SubscriptionEvent(Id, eventType, description);
        _events.Add(subscriptionEvent);
    }

    public bool IsInTrial()
    {
        return TrialEndDate.HasValue && DateTime.UtcNow <= TrialEndDate.Value;
    }

    public bool ShouldBeBilled()
    {
        return Status == SubscriptionStatus.Active &&
               DateTime.UtcNow >= NextBillingDate &&
               !IsInTrial();
    }
}

public class BillingCycle : BaseEntity
{
    public Guid SubscriptionId { get; private set; }
    public int CycleNumber { get; private set; }
    public DateTime CycleStart { get; private set; }
    public DateTime CycleEnd { get; private set; }
    public Money Amount { get; private set; }
    public BillingCycleStatus Status { get; private set; }
    public DateTime? BilledAt { get; private set; }
    public DateTime? PaidAt { get; private set; }
    public string? PaymentTransactionId { get; private set; }
    public string? FailureReason { get; private set; }
    public int RetryCount { get; private set; }
    public DateTime? NextRetryAt { get; private set; }

    private BillingCycle() { }

    public BillingCycle(
        Guid subscriptionId,
        int cycleNumber,
        DateTime cycleStart,
        DateTime cycleEnd,
        Money amount)
    {
        SubscriptionId = subscriptionId;
        CycleNumber = cycleNumber;
        CycleStart = cycleStart;
        CycleEnd = cycleEnd;
        Amount = amount;
        Status = BillingCycleStatus.Pending;
        RetryCount = 0;
    }

    public void MarkAsBilled(string paymentTransactionId)
    {
        Status = BillingCycleStatus.Billed;
        BilledAt = DateTime.UtcNow;
        PaymentTransactionId = paymentTransactionId;
    }

    public void MarkAsPaid()
    {
        Status = BillingCycleStatus.Paid;
        PaidAt = DateTime.UtcNow;
    }

    public void MarkAsFailed(string failureReason, DateTime? nextRetryAt = null)
    {
        Status = BillingCycleStatus.Failed;
        FailureReason = failureReason;
        RetryCount++;
        NextRetryAt = nextRetryAt;
    }

    public void MarkAsSkipped(string reason)
    {
        Status = BillingCycleStatus.Skipped;
        FailureReason = reason;
    }
}

public class SubscriptionEvent : BaseEntity
{
    public Guid SubscriptionId { get; private set; }
    public SubscriptionEventType EventType { get; private set; }
    public string Description { get; private set; }
    public DateTime OccurredAt { get; private set; }
    public Dictionary<string, object> EventData { get; private set; }

    private SubscriptionEvent() { }

    public SubscriptionEvent(Guid subscriptionId, SubscriptionEventType eventType, string description)
    {
        SubscriptionId = subscriptionId;
        EventType = eventType;
        Description = description;
        OccurredAt = DateTime.UtcNow;
        EventData = new Dictionary<string, object>();
    }
}

// Enums
public enum BillingInterval
{
    Daily = 1,
    Weekly = 2,
    Monthly = 3,
    Quarterly = 4,
    Yearly = 5
}

public enum SubscriptionStatus
{
    Active = 1,
    Paused = 2,
    Cancelled = 3,
    Expired = 4,
    PastDue = 5,
    Trialing = 6
}

public enum BillingCycleStatus
{
    Pending = 1,
    Billed = 2,
    Paid = 3,
    Failed = 4,
    Skipped = 5,
    Refunded = 6
}

public enum SubscriptionEventType
{
    Created = 1,
    Activated = 2,
    Paused = 3,
    Resumed = 4,
    Cancelled = 5,
    Expired = 6,
    PriceChanged = 7,
    PaymentMethodUpdated = 8,
    BillingFailed = 9,
    BillingSucceeded = 10,
    TrialStarted = 11,
    TrialEnded = 12
}


