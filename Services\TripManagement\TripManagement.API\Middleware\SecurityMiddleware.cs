using Microsoft.Extensions.Options;
using System.Net;
using System.Text.Json;

namespace TripManagement.API.Middleware;

public class SecurityMiddleware
{
    private readonly RequestDelegate _next;
    private readonly SecurityConfiguration _config;
    private readonly ILogger<SecurityMiddleware> _logger;

    public SecurityMiddleware(
        RequestDelegate next,
        IOptions<SecurityConfiguration> config,
        ILogger<SecurityMiddleware> logger)
    {
        _next = next;
        _config = config.Value;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            // Add security headers
            AddSecurityHeaders(context);

            // Check rate limiting
            if (!await CheckRateLimitAsync(context))
            {
                await WriteErrorResponseAsync(context, HttpStatusCode.TooManyRequests, "Rate limit exceeded");
                return;
            }

            // Validate request size
            if (!ValidateRequestSize(context))
            {
                await WriteErrorResponseAsync(context, HttpStatusCode.RequestEntityTooLarge, "Request too large");
                return;
            }

            // Check for suspicious patterns
            if (ContainsSuspiciousPatterns(context))
            {
                _logger.LogWarning("Suspicious request detected from {RemoteIpAddress}: {Path}", 
                    context.Connection.RemoteIpAddress, context.Request.Path);
                
                await WriteErrorResponseAsync(context, HttpStatusCode.BadRequest, "Invalid request");
                return;
            }

            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in security middleware");
            await WriteErrorResponseAsync(context, HttpStatusCode.InternalServerError, "Internal server error");
        }
    }

    private void AddSecurityHeaders(HttpContext context)
    {
        var headers = context.Response.Headers;

        // Prevent clickjacking
        headers.Add("X-Frame-Options", "DENY");

        // Prevent MIME type sniffing
        headers.Add("X-Content-Type-Options", "nosniff");

        // Enable XSS protection
        headers.Add("X-XSS-Protection", "1; mode=block");

        // Strict transport security (HTTPS only)
        if (context.Request.IsHttps)
        {
            headers.Add("Strict-Transport-Security", "max-age=31536000; includeSubDomains");
        }

        // Content Security Policy
        headers.Add("Content-Security-Policy", 
            "default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self'");

        // Referrer policy
        headers.Add("Referrer-Policy", "strict-origin-when-cross-origin");

        // Permissions policy
        headers.Add("Permissions-Policy", "geolocation=(), microphone=(), camera=()");
    }

    private async Task<bool> CheckRateLimitAsync(HttpContext context)
    {
        if (!_config.EnableRateLimiting)
            return true;

        var clientIp = GetClientIpAddress(context);
        var endpoint = context.Request.Path.Value ?? "";

        // Simple in-memory rate limiting (in production, use Redis or similar)
        var key = $"{clientIp}:{endpoint}";
        var now = DateTime.UtcNow;

        // This is a simplified implementation - in production, use a proper rate limiting library
        return true; // Placeholder
    }

    private bool ValidateRequestSize(HttpContext context)
    {
        if (context.Request.ContentLength.HasValue)
        {
            return context.Request.ContentLength.Value <= _config.MaxRequestSizeBytes;
        }

        return true;
    }

    private bool ContainsSuspiciousPatterns(HttpContext context)
    {
        var request = context.Request;
        
        // Check URL for suspicious patterns
        var path = request.Path.Value?.ToLowerInvariant() ?? "";
        var query = request.QueryString.Value?.ToLowerInvariant() ?? "";
        
        var suspiciousPatterns = new[]
        {
            "script", "javascript:", "vbscript:", "onload", "onerror",
            "select", "union", "insert", "delete", "drop", "exec",
            "../", "..\\", "/etc/passwd", "cmd.exe", "powershell",
            "<script", "</script>", "eval(", "alert(", "document.cookie"
        };

        foreach (var pattern in suspiciousPatterns)
        {
            if (path.Contains(pattern) || query.Contains(pattern))
            {
                return true;
            }
        }

        // Check headers for suspicious content
        foreach (var header in request.Headers)
        {
            var headerValue = header.Value.ToString().ToLowerInvariant();
            foreach (var pattern in suspiciousPatterns)
            {
                if (headerValue.Contains(pattern))
                {
                    return true;
                }
            }
        }

        return false;
    }

    private string GetClientIpAddress(HttpContext context)
    {
        // Check for forwarded IP first (when behind proxy/load balancer)
        var forwardedFor = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
        if (!string.IsNullOrEmpty(forwardedFor))
        {
            return forwardedFor.Split(',')[0].Trim();
        }

        var realIp = context.Request.Headers["X-Real-IP"].FirstOrDefault();
        if (!string.IsNullOrEmpty(realIp))
        {
            return realIp;
        }

        return context.Connection.RemoteIpAddress?.ToString() ?? "unknown";
    }

    private async Task WriteErrorResponseAsync(HttpContext context, HttpStatusCode statusCode, string message)
    {
        context.Response.StatusCode = (int)statusCode;
        context.Response.ContentType = "application/json";

        var response = new
        {
            Error = message,
            StatusCode = (int)statusCode,
            Timestamp = DateTime.UtcNow
        };

        var json = JsonSerializer.Serialize(response);
        await context.Response.WriteAsync(json);
    }
}

public class SecurityConfiguration
{
    public bool EnableRateLimiting { get; set; } = true;
    public int RateLimitRequestsPerMinute { get; set; } = 100;
    public long MaxRequestSizeBytes { get; set; } = 10 * 1024 * 1024; // 10MB
    public bool EnableSuspiciousPatternDetection { get; set; } = true;
    public List<string> TrustedProxies { get; set; } = new();
    public List<string> AllowedOrigins { get; set; } = new();
}

public static class SecurityMiddlewareExtensions
{
    public static IApplicationBuilder UseSecurityMiddleware(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<SecurityMiddleware>();
    }
}
