using OrderManagement.Domain.Enums;

namespace OrderManagement.Domain.ValueObjects;

public record RouteDetails
{
    public Address PickupAddress { get; init; } = null!;
    public Address DeliveryAddress { get; init; } = null!;
    public DateTime PreferredPickupDate { get; init; }
    public DateTime PreferredDeliveryDate { get; init; }
    public TimeWindow? PickupTimeWindow { get; init; }
    public TimeWindow? DeliveryTimeWindow { get; init; }
    public Distance? EstimatedDistance { get; init; }
    public TimeSpan? EstimatedDuration { get; init; }
    public List<Address> IntermediateStops { get; init; } = new();
    public string? RouteNotes { get; init; }
    public bool IsFlexiblePickup { get; init; }
    public bool IsFlexibleDelivery { get; init; }

    // Parameterless constructor for EF Core
    public RouteDetails() { }

    public RouteDetails(
        Address pickupAddress,
        Address deliveryAddress,
        DateTime preferredPickupDate,
        DateTime preferredDeliveryDate,
        TimeWindow? pickupTimeWindow = null,
        TimeWindow? deliveryTimeWindow = null,
        Distance? estimatedDistance = null,
        TimeSpan? estimatedDuration = null,
        List<Address>? intermediateStops = null,
        string? routeNotes = null,
        bool isFlexiblePickup = false,
        bool isFlexibleDelivery = false)
    {
        if (preferredDeliveryDate <= preferredPickupDate)
            throw new ArgumentException("Delivery date must be after pickup date");

        PickupAddress = pickupAddress ?? throw new ArgumentNullException(nameof(pickupAddress));
        DeliveryAddress = deliveryAddress ?? throw new ArgumentNullException(nameof(deliveryAddress));
        PreferredPickupDate = preferredPickupDate;
        PreferredDeliveryDate = preferredDeliveryDate;
        PickupTimeWindow = pickupTimeWindow;
        DeliveryTimeWindow = deliveryTimeWindow;
        EstimatedDistance = estimatedDistance;
        EstimatedDuration = estimatedDuration;
        IntermediateStops = intermediateStops ?? new List<Address>();
        RouteNotes = routeNotes;
        IsFlexiblePickup = isFlexiblePickup;
        IsFlexibleDelivery = isFlexibleDelivery;
    }
}

public record Address
{
    public string Street { get; init; } = string.Empty;
    public string City { get; init; } = string.Empty;
    public string State { get; init; } = string.Empty;
    public string PostalCode { get; init; } = string.Empty;
    public string Country { get; init; } = string.Empty;
    public decimal? Latitude { get; init; }
    public decimal? Longitude { get; init; }
    public string? ContactPerson { get; init; }
    public string? ContactPhone { get; init; }
    public string? SpecialInstructions { get; init; }

    // Parameterless constructor for EF Core
    public Address() { }

    public Address(
        string street,
        string city,
        string state,
        string postalCode,
        string country,
        decimal? latitude = null,
        decimal? longitude = null,
        string? contactPerson = null,
        string? contactPhone = null,
        string? specialInstructions = null)
    {
        if (string.IsNullOrWhiteSpace(street))
            throw new ArgumentException("Street cannot be empty", nameof(street));
        if (string.IsNullOrWhiteSpace(city))
            throw new ArgumentException("City cannot be empty", nameof(city));
        if (string.IsNullOrWhiteSpace(country))
            throw new ArgumentException("Country cannot be empty", nameof(country));

        Street = street;
        City = city;
        State = state;
        PostalCode = postalCode;
        Country = country;
        Latitude = latitude;
        Longitude = longitude;
        ContactPerson = contactPerson;
        ContactPhone = contactPhone;
        SpecialInstructions = specialInstructions;
    }

    public string FullAddress => $"{Street}, {City}, {State} {PostalCode}, {Country}";
}

public record TimeWindow
{
    public TimeSpan StartTime { get; init; }
    public TimeSpan EndTime { get; init; }

    // Parameterless constructor for EF Core
    public TimeWindow() { }

    public TimeWindow(TimeSpan startTime, TimeSpan endTime)
    {
        StartTime = startTime;
        EndTime = endTime;
    }

    public bool IsWithin(TimeSpan time) => time >= StartTime && time <= EndTime;
}

public record Distance
{
    public decimal Value { get; init; }
    public DistanceUnit Unit { get; init; }

    // Parameterless constructor for EF Core
    public Distance() { }

    public Distance(decimal value, DistanceUnit unit)
    {
        Value = value;
        Unit = unit;
    }

    public decimal ToKilometers() => Unit switch
    {
        DistanceUnit.Kilometer => Value,
        DistanceUnit.Mile => Value * 1.60934m,
        _ => throw new ArgumentException("Invalid distance unit")
    };
}
