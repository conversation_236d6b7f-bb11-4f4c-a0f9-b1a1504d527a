using Shared.Domain.Common;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Domain.Events;

// Settlement Events
public record SettlementCreatedEvent(Guid SettlementId, Guid OrderId, Guid TripId, Money TotalAmount) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record SettlementProcessingStartedEvent(Guid SettlementId, Guid OrderId, Guid TripId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record SettlementCompletedEvent(Guid SettlementId, Guid OrderId, Guid TripId, Money TotalAmount) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record SettlementFailedEvent(Guid SettlementId, Guid OrderId, Guid TripId, string Reason) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// Settlement Distribution Events
public record SettlementDistributionAddedEvent(Guid DistributionId, Guid SettlementId, Guid RecipientId, Money Amount) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}
