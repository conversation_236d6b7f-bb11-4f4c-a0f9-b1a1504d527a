using AuditCompliance.Application.DTOs;
using AuditCompliance.Application.Interfaces;
using AuditCompliance.Application.Queries;
using AuditCompliance.Domain.Entities;
using AuditCompliance.Domain.Enums;
using MediatR;
using Microsoft.Extensions.Logging;

namespace AuditCompliance.Application.Handlers
{
    /// <summary>
    /// Query handler for getting custom report template by ID
    /// </summary>
    public class GetCustomReportTemplateByIdQueryHandler : IRequestHandler<GetCustomReportTemplateByIdQuery, CustomComplianceReportTemplateDto?>
    {
        private readonly ICustomComplianceReportTemplateRepository _templateRepository;
        private readonly ILogger<GetCustomReportTemplateByIdQueryHandler> _logger;

        public GetCustomReportTemplateByIdQueryHandler(
            ICustomComplianceReportTemplateRepository templateRepository,
            ILogger<GetCustomReportTemplateByIdQueryHandler> logger)
        {
            _templateRepository = templateRepository;
            _logger = logger;
        }

        public async Task<CustomComplianceReportTemplateDto?> Handle(GetCustomReportTemplateByIdQuery request, CancellationToken cancellationToken)
        {
            try
            {
                var template = await _templateRepository.GetByIdAsync(request.TemplateId);
                if (template == null)
                {
                    return null;
                }

                // Check permissions
                if (!template.HasPermission(request.RequestedBy, TemplatePermissionType.View))
                {
                    _logger.LogWarning("User {UserId} does not have permission to view template {TemplateId}",
                        request.RequestedBy, request.TemplateId);
                    return null;
                }

                return MapToDto(template);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving template {TemplateId}", request.TemplateId);
                throw;
            }
        }

        private static CustomComplianceReportTemplateDto MapToDto(CustomComplianceReportTemplate template)
        {
            return new CustomComplianceReportTemplateDto
            {
                Id = template.Id,
                TemplateName = template.TemplateName,
                Description = template.Description,
                ComplianceStandard = template.ComplianceStandard,
                TemplateType = template.TemplateType,
                TemplateContent = template.TemplateContent,
                Sections = template.Sections.Select(s => new ReportSectionDto
                {
                    Id = s.Id,
                    Name = s.Name,
                    Description = s.Description,
                    Order = s.Order,
                    IsRequired = s.IsRequired,
                    Template = s.Template,
                    DataSources = s.DataSources,
                    Configuration = s.Configuration,
                    Type = s.Type
                }).ToList(),
                Parameters = template.Parameters.Select(p => new ReportParameterDto
                {
                    Id = p.Id,
                    Name = p.Name,
                    DisplayName = p.DisplayName,
                    Description = p.Description,
                    Type = p.Type,
                    IsRequired = p.IsRequired,
                    DefaultValue = p.DefaultValue,
                    Options = p.Options.Select(o => new ParameterOptionDto
                    {
                        Value = o.Value,
                        Label = o.Label,
                        IsDefault = o.IsDefault
                    }).ToList(),
                    Validation = p.Validation
                }).ToList(),
                Schedule = template.Schedule != null ? new ReportScheduleDto
                {
                    CronExpression = template.Schedule.CronExpression,
                    StartDate = template.Schedule.StartDate,
                    EndDate = template.Schedule.EndDate,
                    IsActive = template.Schedule.IsActive,
                    Recipients = template.Schedule.Recipients,
                    Frequency = template.Schedule.Frequency,
                    Settings = template.Schedule.Settings
                } : null,
                Tags = template.Tags,
                IsPublic = template.IsPublic,
                IsActive = template.IsActive,
                CreatedBy = template.CreatedBy,
                CreatedByName = template.CreatedByName,
                CreatedAt = template.CreatedAt,
                UpdatedAt = template.UpdatedAt,
                LastUsed = template.LastUsed,
                UsageCount = template.UsageCount,
                Permissions = template.Permissions.Select(p => new TemplatePermissionDto
                {
                    UserId = p.UserId,
                    UserName = p.UserName,
                    PermissionType = p.PermissionType,
                    GrantedAt = p.GrantedAt,
                    GrantedBy = p.GrantedBy,
                    ExpiresAt = p.ExpiresAt,
                    IsActive = p.IsActive,
                    RevokedAt = p.RevokedAt,
                    RevokedBy = p.RevokedBy
                }).ToList(),
                Version = template.Version,
                Metadata = template.Metadata
            };
        }
    }

    /// <summary>
    /// Query handler for searching custom report templates
    /// </summary>
    public class SearchCustomReportTemplatesQueryHandler : IRequestHandler<SearchCustomReportTemplatesQuery, TemplateSearchResultDto>
    {
        private readonly ICustomComplianceReportTemplateRepository _templateRepository;
        private readonly ILogger<SearchCustomReportTemplatesQueryHandler> _logger;

        public SearchCustomReportTemplatesQueryHandler(
            ICustomComplianceReportTemplateRepository templateRepository,
            ILogger<SearchCustomReportTemplatesQueryHandler> logger)
        {
            _templateRepository = templateRepository;
            _logger = logger;
        }

        public async Task<TemplateSearchResultDto> Handle(SearchCustomReportTemplatesQuery request, CancellationToken cancellationToken)
        {
            try
            {
                // Get accessible templates for the user
                var accessibleTemplates = await _templateRepository.GetAccessibleTemplatesAsync(request.RequestedBy);

                // Apply filters
                var filteredTemplates = ApplyFilters(accessibleTemplates, request);

                // Apply sorting
                var sortedTemplates = ApplySorting(filteredTemplates, request.SortBy, request.SortDescending);

                // Calculate pagination
                var totalCount = sortedTemplates.Count();
                var pagedTemplates = sortedTemplates
                    .Skip((request.PageNumber - 1) * request.PageSize)
                    .Take(request.PageSize)
                    .ToList();

                // Generate statistics
                var tagCounts = accessibleTemplates
                    .SelectMany(t => t.Tags)
                    .GroupBy(tag => tag)
                    .ToDictionary(g => g.Key, g => g.Count());

                var standardCounts = accessibleTemplates
                    .GroupBy(t => t.ComplianceStandard)
                    .ToDictionary(g => g.Key, g => g.Count());

                var typeCounts = accessibleTemplates
                    .GroupBy(t => t.TemplateType)
                    .ToDictionary(g => g.Key, g => g.Count());

                return new TemplateSearchResultDto
                {
                    Templates = pagedTemplates.Select(MapToDto).ToList(),
                    TotalCount = totalCount,
                    PageNumber = request.PageNumber,
                    PageSize = request.PageSize,
                    HasNextPage = (request.PageNumber * request.PageSize) < totalCount,
                    HasPreviousPage = request.PageNumber > 1,
                    TagCounts = tagCounts,
                    StandardCounts = standardCounts,
                    TypeCounts = typeCounts
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching templates");
                throw;
            }
        }

        private static IEnumerable<CustomComplianceReportTemplate> ApplyFilters(
            List<CustomComplianceReportTemplate> templates,
            SearchCustomReportTemplatesQuery request)
        {
            var filtered = templates.AsEnumerable();

            if (!string.IsNullOrEmpty(request.SearchTerm))
            {
                var term = request.SearchTerm.ToLower();
                filtered = filtered.Where(t =>
                    t.TemplateName.ToLower().Contains(term) ||
                    t.Description.ToLower().Contains(term) ||
                    t.Tags.Any(tag => tag.ToLower().Contains(term)));
            }

            if (request.ComplianceStandard.HasValue)
            {
                filtered = filtered.Where(t => t.ComplianceStandard == request.ComplianceStandard.Value);
            }

            if (request.TemplateType.HasValue)
            {
                filtered = filtered.Where(t => t.TemplateType == request.TemplateType.Value);
            }

            if (request.IsPublic.HasValue)
            {
                filtered = filtered.Where(t => t.IsPublic == request.IsPublic.Value);
            }

            if (request.IsActive.HasValue)
            {
                filtered = filtered.Where(t => t.IsActive == request.IsActive.Value);
            }

            if (request.CreatedBy.HasValue)
            {
                filtered = filtered.Where(t => t.CreatedBy == request.CreatedBy.Value);
            }

            if (request.CreatedAfter.HasValue)
            {
                filtered = filtered.Where(t => t.CreatedAt >= request.CreatedAfter.Value);
            }

            if (request.CreatedBefore.HasValue)
            {
                filtered = filtered.Where(t => t.CreatedAt <= request.CreatedBefore.Value);
            }

            if (request.Tags.Any())
            {
                filtered = filtered.Where(t => request.Tags.Any(tag => t.Tags.Contains(tag)));
            }

            return filtered;
        }

        private static IEnumerable<CustomComplianceReportTemplate> ApplySorting(
            IEnumerable<CustomComplianceReportTemplate> templates,
            string sortBy,
            bool sortDescending)
        {
            return sortBy.ToLower() switch
            {
                "templatename" => sortDescending
                    ? templates.OrderByDescending(t => t.TemplateName)
                    : templates.OrderBy(t => t.TemplateName),
                "createdat" => sortDescending
                    ? templates.OrderByDescending(t => t.CreatedAt)
                    : templates.OrderBy(t => t.CreatedAt),
                "updatedat" => sortDescending
                    ? templates.OrderByDescending(t => t.UpdatedAt)
                    : templates.OrderBy(t => t.UpdatedAt),
                "usagecount" => sortDescending
                    ? templates.OrderByDescending(t => t.UsageCount)
                    : templates.OrderBy(t => t.UsageCount),
                "lastused" => sortDescending
                    ? templates.OrderByDescending(t => t.LastUsed)
                    : templates.OrderBy(t => t.LastUsed),
                _ => sortDescending
                    ? templates.OrderByDescending(t => t.CreatedAt)
                    : templates.OrderBy(t => t.CreatedAt)
            };
        }

        private static CustomComplianceReportTemplateDto MapToDto(CustomComplianceReportTemplate template)
        {
            // Use the same mapping logic as in GetCustomReportTemplateByIdQueryHandler
            return new CustomComplianceReportTemplateDto
            {
                Id = template.Id,
                TemplateName = template.TemplateName,
                Description = template.Description,
                ComplianceStandard = template.ComplianceStandard,
                TemplateType = template.TemplateType,
                IsPublic = template.IsPublic,
                IsActive = template.IsActive,
                CreatedBy = template.CreatedBy,
                CreatedByName = template.CreatedByName,
                CreatedAt = template.CreatedAt,
                UpdatedAt = template.UpdatedAt,
                LastUsed = template.LastUsed,
                UsageCount = template.UsageCount,
                Tags = template.Tags,
                Version = template.Version
                // Simplified DTO for search results - full details available via GetById
            };
        }
    }

    /// <summary>
    /// Query handler for getting retention policy by ID
    /// </summary>
    public class GetRetentionPolicyByIdQueryHandler : IRequestHandler<GetRetentionPolicyByIdQuery, RetentionPolicyManagementDto?>
    {
        private readonly IRetentionPolicyManagementRepository _policyRepository;
        private readonly ILogger<GetRetentionPolicyByIdQueryHandler> _logger;

        public GetRetentionPolicyByIdQueryHandler(
            IRetentionPolicyManagementRepository policyRepository,
            ILogger<GetRetentionPolicyByIdQueryHandler> logger)
        {
            _policyRepository = policyRepository;
            _logger = logger;
        }

        public async Task<RetentionPolicyManagementDto?> Handle(GetRetentionPolicyByIdQuery request, CancellationToken cancellationToken)
        {
            try
            {
                var policy = await _policyRepository.GetByIdAsync(request.PolicyId);
                if (policy == null)
                {
                    return null;
                }

                return MapToDto(policy);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving retention policy {PolicyId}", request.PolicyId);
                throw;
            }
        }

        private static RetentionPolicyManagementDto MapToDto(RetentionPolicyManagement policy)
        {
            return new RetentionPolicyManagementDto
            {
                Id = policy.Id,
                PolicyName = policy.PolicyName,
                Description = policy.Description,
                RetentionPolicy = new RetentionPolicyDto
                {
                    RetentionPeriod = policy.RetentionPolicy.RetentionPeriod,
                    AutoDelete = policy.RetentionPolicy.AutoDelete,
                    RequireApproval = policy.RetentionPolicy.RequireApproval,
                    Description = policy.RetentionPolicy.Description,
                    ApplicableStandards = policy.RetentionPolicy.ApplicableStandards
                },
                ApplicableStandards = policy.ApplicableStandards,
                ApplicableEntityTypes = policy.ApplicableEntityTypes,
                ApplicableModules = policy.ApplicableModules,
                Priority = policy.Priority,
                IsActive = policy.IsActive,
                RequiresApproval = policy.RequiresApproval,
                ApprovalStatus = policy.ApprovalStatus,
                CreatedBy = policy.CreatedBy,
                CreatedByName = policy.CreatedByName,
                CreatedAt = policy.CreatedAt,
                UpdatedAt = policy.UpdatedAt,
                ApprovedAt = policy.ApprovedAt,
                ApprovedBy = policy.ApprovedBy,
                ApprovedByName = policy.ApprovedByName,
                LastExecuted = policy.LastExecuted,
                ExecutionCount = policy.ExecutionCount,
                RecordsAffected = policy.RecordsAffected,
                ExecutionHistory = policy.ExecutionHistory.Select(e => new PolicyExecutionDto
                {
                    ExecutedAt = e.ExecutedAt,
                    RecordsProcessed = e.RecordsProcessed,
                    RecordsDeleted = e.RecordsDeleted,
                    ExecutionTime = e.ExecutionTime,
                    IsSuccessful = e.IsSuccessful,
                    ErrorMessage = e.ErrorMessage
                }).ToList(),
                ImpactAnalysis = policy.ImpactAnalysis != null ? new PolicyImpactAnalysisDto
                {
                    EstimatedRecordsAffected = policy.ImpactAnalysis.EstimatedRecordsAffected,
                    RecordsByEntityType = policy.ImpactAnalysis.RecordsByEntityType,
                    RecordsByModule = policy.ImpactAnalysis.RecordsByModule,
                    EstimatedExecutionTime = policy.ImpactAnalysis.EstimatedExecutionTime,
                    AnalyzedAt = policy.ImpactAnalysis.AnalyzedAt,
                    Warnings = policy.ImpactAnalysis.Warnings,
                    Recommendations = policy.ImpactAnalysis.Recommendations
                } : null,
                Configuration = policy.Configuration,
                Tags = policy.Tags,
                ExecutionSummary = new PolicyExecutionSummaryDto
                {
                    TotalExecutions = policy.ExecutionCount,
                    TotalRecordsAffected = policy.RecordsAffected,
                    LastExecuted = policy.LastExecuted,
                    SuccessfulExecutions = policy.ExecutionHistory.Count(e => e.IsSuccessful),
                    FailedExecutions = policy.ExecutionHistory.Count(e => !e.IsSuccessful),
                    AverageExecutionTimeSeconds = policy.ExecutionHistory.Any()
                        ? policy.ExecutionHistory.Average(e => e.ExecutionTime.TotalSeconds)
                        : 0
                }
            };
        }
    }

    /// <summary>
    /// Query handler for searching retention policies
    /// </summary>
    public class SearchRetentionPoliciesQueryHandler : IRequestHandler<SearchRetentionPoliciesQuery, PolicySearchResultDto>
    {
        private readonly IRetentionPolicyManagementRepository _policyRepository;
        private readonly ILogger<SearchRetentionPoliciesQueryHandler> _logger;

        public SearchRetentionPoliciesQueryHandler(
            IRetentionPolicyManagementRepository policyRepository,
            ILogger<SearchRetentionPoliciesQueryHandler> logger)
        {
            _policyRepository = policyRepository;
            _logger = logger;
        }

        public async Task<PolicySearchResultDto> Handle(SearchRetentionPoliciesQuery request, CancellationToken cancellationToken)
        {
            try
            {
                // Get all policies and apply filters
                var allPolicies = await _policyRepository.GetAllAsync();
                var filteredPolicies = ApplyFilters(allPolicies, request);
                var sortedPolicies = ApplySorting(filteredPolicies, request.SortBy, request.SortDescending);

                // Calculate pagination
                var totalCount = sortedPolicies.Count();
                var pagedPolicies = sortedPolicies
                    .Skip((request.PageNumber - 1) * request.PageSize)
                    .Take(request.PageSize)
                    .ToList();

                // Generate statistics
                var statusCounts = allPolicies
                    .GroupBy(p => p.ApprovalStatus)
                    .ToDictionary(g => g.Key, g => g.Count());

                var priorityCounts = allPolicies
                    .GroupBy(p => p.Priority)
                    .ToDictionary(g => g.Key, g => g.Count());

                var tagCounts = allPolicies
                    .SelectMany(p => p.Tags)
                    .GroupBy(tag => tag)
                    .ToDictionary(g => g.Key, g => g.Count());

                return new PolicySearchResultDto
                {
                    Policies = pagedPolicies.Select(MapToDto).ToList(),
                    TotalCount = totalCount,
                    PageNumber = request.PageNumber,
                    PageSize = request.PageSize,
                    HasNextPage = (request.PageNumber * request.PageSize) < totalCount,
                    HasPreviousPage = request.PageNumber > 1,
                    StatusCounts = statusCounts,
                    PriorityCounts = priorityCounts,
                    TagCounts = tagCounts
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching retention policies");
                throw;
            }
        }

        private static IEnumerable<RetentionPolicyManagement> ApplyFilters(
            List<RetentionPolicyManagement> policies,
            SearchRetentionPoliciesQuery request)
        {
            var filtered = policies.AsEnumerable();

            if (!string.IsNullOrEmpty(request.SearchTerm))
            {
                var term = request.SearchTerm.ToLower();
                filtered = filtered.Where(p =>
                    p.PolicyName.ToLower().Contains(term) ||
                    p.Description.ToLower().Contains(term) ||
                    p.Tags.Any(tag => tag.ToLower().Contains(term)));
            }

            if (request.ApprovalStatus.HasValue)
            {
                filtered = filtered.Where(p => p.ApprovalStatus == request.ApprovalStatus.Value);
            }

            if (request.IsActive.HasValue)
            {
                filtered = filtered.Where(p => p.IsActive == request.IsActive.Value);
            }

            if (request.Priority.HasValue)
            {
                filtered = filtered.Where(p => p.Priority == request.Priority.Value);
            }

            if (request.ApplicableStandards.Any())
            {
                filtered = filtered.Where(p => request.ApplicableStandards.Any(s => p.ApplicableStandards.Contains(s)));
            }

            if (request.ApplicableEntityTypes.Any())
            {
                filtered = filtered.Where(p => request.ApplicableEntityTypes.Any(et => p.ApplicableEntityTypes.Contains(et)));
            }

            if (request.ApplicableModules.Any())
            {
                filtered = filtered.Where(p => request.ApplicableModules.Any(m => p.ApplicableModules.Contains(m)));
            }

            if (request.Tags.Any())
            {
                filtered = filtered.Where(p => request.Tags.Any(tag => p.Tags.Contains(tag)));
            }

            if (request.CreatedBy.HasValue)
            {
                filtered = filtered.Where(p => p.CreatedBy == request.CreatedBy.Value);
            }

            if (request.CreatedAfter.HasValue)
            {
                filtered = filtered.Where(p => p.CreatedAt >= request.CreatedAfter.Value);
            }

            if (request.CreatedBefore.HasValue)
            {
                filtered = filtered.Where(p => p.CreatedAt <= request.CreatedBefore.Value);
            }

            return filtered;
        }

        private static IEnumerable<RetentionPolicyManagement> ApplySorting(
            IEnumerable<RetentionPolicyManagement> policies,
            string sortBy,
            bool sortDescending)
        {
            return sortBy.ToLower() switch
            {
                "policyname" => sortDescending
                    ? policies.OrderByDescending(p => p.PolicyName)
                    : policies.OrderBy(p => p.PolicyName),
                "createdat" => sortDescending
                    ? policies.OrderByDescending(p => p.CreatedAt)
                    : policies.OrderBy(p => p.CreatedAt),
                "priority" => sortDescending
                    ? policies.OrderByDescending(p => p.Priority)
                    : policies.OrderBy(p => p.Priority),
                "approvalstatus" => sortDescending
                    ? policies.OrderByDescending(p => p.ApprovalStatus)
                    : policies.OrderBy(p => p.ApprovalStatus),
                "lastexecuted" => sortDescending
                    ? policies.OrderByDescending(p => p.LastExecuted)
                    : policies.OrderBy(p => p.LastExecuted),
                _ => sortDescending
                    ? policies.OrderByDescending(p => p.CreatedAt)
                    : policies.OrderBy(p => p.CreatedAt)
            };
        }

        private static RetentionPolicyManagementDto MapToDto(RetentionPolicyManagement policy)
        {
            // Use the same mapping logic as in GetRetentionPolicyByIdQueryHandler
            return new RetentionPolicyManagementDto
            {
                Id = policy.Id,
                PolicyName = policy.PolicyName,
                Description = policy.Description,
                Priority = policy.Priority,
                IsActive = policy.IsActive,
                ApprovalStatus = policy.ApprovalStatus,
                CreatedBy = policy.CreatedBy,
                CreatedByName = policy.CreatedByName,
                CreatedAt = policy.CreatedAt,
                UpdatedAt = policy.UpdatedAt,
                LastExecuted = policy.LastExecuted,
                ExecutionCount = policy.ExecutionCount,
                RecordsAffected = policy.RecordsAffected,
                Tags = policy.Tags
                // Simplified DTO for search results
            };
        }
    }
}
