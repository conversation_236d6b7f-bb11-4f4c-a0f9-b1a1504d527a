using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Domain.Enums;
using MediatR;

namespace AnalyticsBIService.Application.Queries.Carrier;

/// <summary>
/// Query to get carrier rating summary
/// </summary>
public record GetCarrierRatingSummaryQuery(
    Guid CarrierId,
    DateTime FromDate,
    DateTime ToDate,
    string? RatingCategory = null
) : IRequest<CarrierRatingSummaryDto>;

/// <summary>
/// Query to get carrier broker ratings
/// </summary>
public record GetCarrierBrokerRatingsQuery(
    Guid CarrierId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    string? BrokerFilter = null
) : IRequest<CarrierBrokerRatingsDto>;

/// <summary>
/// Query to get carrier shipper ratings
/// </summary>
public record GetCarrierShipperRatingsQuery(
    Guid CarrierId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    string? ShipperFilter = null
) : IRequest<CarrierShipperRatingsDto>;

/// <summary>
/// Query to get carrier rating trends
/// </summary>
public record GetCarrierRatingTrendsQuery(
    Guid CarrierId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    string? Category = null
) : IRequest<CarrierRatingTrendsDto>;

/// <summary>
/// Query to get carrier feedback analysis
/// </summary>
public record GetCarrierFeedbackAnalysisQuery(
    Guid CarrierId,
    DateTime FromDate,
    DateTime ToDate,
    string? FeedbackSource = null, // Broker, Shipper, All
    string? SentimentFilter = null // Positive, Negative, Neutral
) : IRequest<CarrierFeedbackAnalysisDto>;

/// <summary>
/// Query to get carrier rating competitive analysis
/// </summary>
public record GetCarrierRatingCompetitiveAnalysisQuery(
    Guid CarrierId,
    DateTime FromDate,
    DateTime ToDate,
    string? MarketSegment = null,
    string? GeographicRegion = null
) : IRequest<CarrierRatingCompetitiveAnalysisDto>;

/// <summary>
/// Comprehensive query that aggregates all rating analytics
/// </summary>
public record GetCarrierRatingsQuery(
    Guid CarrierId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    bool IncludeBrokerRatings = true,
    bool IncludeShipperRatings = true,
    bool IncludeRatingTrends = true,
    bool IncludeFeedbackAnalysis = true,
    bool IncludeCompetitiveAnalysis = true,
    string? RatingCategory = null,
    decimal? MinRating = null,
    decimal? MaxRating = null
) : IRequest<CarrierRatingsAnalyticsDto>;
