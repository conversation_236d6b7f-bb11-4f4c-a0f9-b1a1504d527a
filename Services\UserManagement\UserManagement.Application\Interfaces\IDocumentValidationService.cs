using Microsoft.AspNetCore.Http;

namespace UserManagement.Application.Interfaces;

public class ValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
}

public interface IDocumentValidationService
{
    Task<ValidationResult> ValidateDocumentAsync(IFormFile document);
    Task<ValidationResult> ValidateDocumentTypeAsync(IFormFile document, string expectedType);
    Task<DocumentValidationResult> ValidateDocumentDataAsync(string documentType, Dictionary<string, string> extractedFields, ValidationConfiguration validationConfig, CancellationToken cancellationToken = default);
}

public class DocumentValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public Dictionary<string, object> ValidatedFields { get; set; } = new();
    public decimal ConfidenceScore { get; set; }
}

public class ValidationConfiguration
{
    public Dictionary<string, FieldValidationRule> FieldRules { get; set; } = new();
    public List<string> RequiredFields { get; set; } = new();
    public decimal MinimumConfidenceThreshold { get; set; } = 0.7m;
}

public class FieldValidationRule
{
    public string FieldName { get; set; } = string.Empty;
    public bool IsRequired { get; set; }
    public string? Pattern { get; set; }
    public int? MinLength { get; set; }
    public int? MaxLength { get; set; }
    public List<string> AllowedValues { get; set; } = new();
}
