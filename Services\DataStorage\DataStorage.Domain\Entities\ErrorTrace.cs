using System;
using Shared.Domain.Common;

namespace DataStorage.Domain.Entities
{
    public class ErrorTrace : BaseEntity
    {
        public string ErrorId { get; private set; }
        public string Message { get; private set; }
        public string StackTrace { get; private set; }
        public string Source { get; private set; }
        public string Severity { get; private set; }
        public DateTime Timestamp { get; private set; }
        public string UserId { get; private set; }
        public string RequestId { get; private set; }
        public string AdditionalData { get; private set; }

        private ErrorTrace() { }

        public ErrorTrace(
            string errorId,
            string message,
            string stackTrace,
            string source,
            string severity,
            string userId = null,
            string requestId = null,
            string additionalData = null)
        {
            ErrorId = errorId ?? Guid.NewGuid().ToString();
            Message = message ?? throw new ArgumentNullException(nameof(message));
            StackTrace = stackTrace;
            Source = source ?? throw new ArgumentNullException(nameof(source));
            Severity = severity ?? "Error";
            Timestamp = DateTime.UtcNow;
            UserId = userId;
            RequestId = requestId;
            AdditionalData = additionalData;
            CreatedAt = DateTime.UtcNow;
        }
    }
}
