using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using FinancialPayment.Application.Commands.CreateEscrowAccount;
using FinancialPayment.Application.DTOs;
using FinancialPayment.Application.Interfaces.Repositories;
using FinancialPayment.Domain.Entities;
using Shared.Infrastructure.Interfaces;
using Xunit;

namespace FinancialPayment.Tests.Application.Commands;

public class CreateEscrowAccountCommandHandlerTests
{
    private readonly Mock<IEscrowAccountRepository> _mockRepository;
    private readonly Mock<IUnitOfWork> _mockUnitOfWork;
    private readonly Mock<ILogger<CreateEscrowAccountCommandHandler>> _mockLogger;
    private readonly CreateEscrowAccountCommandHandler _handler;

    public CreateEscrowAccountCommandHandlerTests()
    {
        _mockRepository = new Mock<IEscrowAccountRepository>();
        _mockUnitOfWork = new Mock<IUnitOfWork>();
        _mockLogger = new Mock<ILogger<CreateEscrowAccountCommandHandler>>();

        _handler = new CreateEscrowAccountCommandHandler(
            _mockRepository.Object,
            _mockUnitOfWork.Object,
            _mockLogger.Object);
    }

    [Fact]
    public async Task Handle_ShouldCreateEscrowAccount_WhenValidCommand()
    {
        // Arrange
        var command = new CreateEscrowAccountCommand
        {
            OrderId = Guid.NewGuid(),
            TransportCompanyId = Guid.NewGuid(),
            BrokerId = Guid.NewGuid(),
            CarrierId = Guid.NewGuid(),
            TotalAmount = new CreateMoneyDto { Amount = 10000m, Currency = "INR" },
            Notes = "Test escrow account"
        };

        _mockRepository.Setup(r => r.GetByOrderIdAsync(command.OrderId))
            .ReturnsAsync((EscrowAccount?)null);

        _mockRepository.Setup(r => r.AddAsync(It.IsAny<EscrowAccount>()))
            .ReturnsAsync((EscrowAccount account) => account);

        _mockUnitOfWork.Setup(u => u.SaveChangesAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(1);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeEmpty();

        _mockRepository.Verify(r => r.GetByOrderIdAsync(command.OrderId), Times.Once);
        _mockRepository.Verify(r => r.AddAsync(It.Is<EscrowAccount>(e =>
            e.OrderId == command.OrderId &&
            e.TransportCompanyId == command.TransportCompanyId &&
            e.BrokerId == command.BrokerId &&
            e.CarrierId == command.CarrierId &&
            e.TotalAmount.Amount == 10000m &&
            e.TotalAmount.Currency == "INR" &&
            e.Notes == "Test escrow account")), Times.Once);
        _mockUnitOfWork.Verify(u => u.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldThrowException_WhenEscrowAccountAlreadyExists()
    {
        // Arrange
        var command = new CreateEscrowAccountCommand
        {
            OrderId = Guid.NewGuid(),
            TransportCompanyId = Guid.NewGuid(),
            BrokerId = Guid.NewGuid(),
            CarrierId = Guid.NewGuid(),
            TotalAmount = new CreateMoneyDto { Amount = 10000m, Currency = "INR" }
        };

        var existingEscrowAccount = new EscrowAccount(
            command.OrderId,
            command.TransportCompanyId,
            command.BrokerId,
            command.CarrierId,
            command.TotalAmount.ToMoney());

        _mockRepository.Setup(r => r.GetByOrderIdAsync(command.OrderId))
            .ReturnsAsync(existingEscrowAccount);

        // Act & Assert
        var action = async () => await _handler.Handle(command, CancellationToken.None);

        await action.Should().ThrowAsync<InvalidOperationException>()
            .WithMessage($"Escrow account already exists for order {command.OrderId}");

        _mockRepository.Verify(r => r.GetByOrderIdAsync(command.OrderId), Times.Once);
        _mockRepository.Verify(r => r.AddAsync(It.IsAny<EscrowAccount>()), Times.Never);
        _mockUnitOfWork.Verify(u => u.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task Handle_ShouldLogInformation_WhenSuccessful()
    {
        // Arrange
        var command = new CreateEscrowAccountCommand
        {
            OrderId = Guid.NewGuid(),
            TransportCompanyId = Guid.NewGuid(),
            BrokerId = Guid.NewGuid(),
            CarrierId = Guid.NewGuid(),
            TotalAmount = new CreateMoneyDto { Amount = 10000m, Currency = "INR" }
        };

        _mockRepository.Setup(r => r.GetByOrderIdAsync(command.OrderId))
            .ReturnsAsync((EscrowAccount?)null);

        _mockRepository.Setup(r => r.AddAsync(It.IsAny<EscrowAccount>()))
            .ReturnsAsync((EscrowAccount account) => account);

        _mockUnitOfWork.Setup(u => u.SaveChangesAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(1);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeEmpty();

        // Verify logging calls
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains($"Creating escrow account for order {command.OrderId}")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);

        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Successfully created escrow account") && v.ToString()!.Contains(command.OrderId.ToString())),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldLogError_WhenExceptionOccurs()
    {
        // Arrange
        var command = new CreateEscrowAccountCommand
        {
            OrderId = Guid.NewGuid(),
            TransportCompanyId = Guid.NewGuid(),
            BrokerId = Guid.NewGuid(),
            CarrierId = Guid.NewGuid(),
            TotalAmount = new CreateMoneyDto { Amount = 10000m, Currency = "INR" }
        };

        var expectedException = new Exception("Database error");

        _mockRepository.Setup(r => r.GetByOrderIdAsync(command.OrderId))
            .ThrowsAsync(expectedException);

        // Act & Assert
        var action = async () => await _handler.Handle(command, CancellationToken.None);

        await action.Should().ThrowAsync<Exception>()
            .WithMessage("Database error");

        // Verify error logging
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains($"Error creating escrow account for order {command.OrderId}")),
                expectedException,
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldCreateEscrowAccount_WithCorrectProperties()
    {
        // Arrange
        var orderId = Guid.NewGuid();
        var transportCompanyId = Guid.NewGuid();
        var brokerId = Guid.NewGuid();
        var carrierId = Guid.NewGuid();

        var command = new CreateEscrowAccountCommand
        {
            OrderId = orderId,
            TransportCompanyId = transportCompanyId,
            BrokerId = brokerId,
            CarrierId = carrierId,
            TotalAmount = new CreateMoneyDto { Amount = 15000.50m, Currency = "USD" },
            Notes = "International shipment escrow"
        };

        EscrowAccount? capturedEscrowAccount = null;

        _mockRepository.Setup(r => r.GetByOrderIdAsync(command.OrderId))
            .ReturnsAsync((EscrowAccount?)null);

        _mockRepository.Setup(r => r.AddAsync(It.IsAny<EscrowAccount>()))
            .Callback<EscrowAccount>(account => capturedEscrowAccount = account)
            .ReturnsAsync((EscrowAccount account) => account);

        _mockUnitOfWork.Setup(u => u.SaveChangesAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(1);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeEmpty();
        capturedEscrowAccount.Should().NotBeNull();
        capturedEscrowAccount!.OrderId.Should().Be(orderId);
        capturedEscrowAccount.TransportCompanyId.Should().Be(transportCompanyId);
        capturedEscrowAccount.BrokerId.Should().Be(brokerId);
        capturedEscrowAccount.CarrierId.Should().Be(carrierId);
        capturedEscrowAccount.TotalAmount.Amount.Should().Be(15000.50m);
        capturedEscrowAccount.TotalAmount.Currency.Should().Be("USD");
        capturedEscrowAccount.Notes.Should().Be("International shipment escrow");
        capturedEscrowAccount.EscrowAccountNumber.Should().NotBeNullOrEmpty();
        capturedEscrowAccount.Status.Should().Be(FinancialPayment.Domain.Enums.EscrowStatus.Created);
    }
}
