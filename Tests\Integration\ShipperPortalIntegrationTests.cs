using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Net.Http.Json;
using System.Text.Json;
using Xunit;
using Xunit.Abstractions;

namespace Tests.Integration;

/// <summary>
/// Comprehensive integration tests for all Shipper Portal features
/// Tests cross-service communication, event-driven architecture, and end-to-end workflows
/// </summary>
public class ShipperPortalIntegrationTests : IClassFixture<ShipperPortalTestFixture>
{
    private readonly ShipperPortalTestFixture _fixture;
    private readonly ITestOutputHelper _output;
    private readonly HttpClient _orderManagementClient;
    private readonly HttpClient _tripManagementClient;
    private readonly HttpClient _financialPaymentClient;
    private readonly HttpClient _networkFleetClient;
    private readonly HttpClient _analyticsClient;

    public ShipperPortalIntegrationTests(ShipperPortalTestFixture fixture, ITestOutputHelper output)
    {
        _fixture = fixture;
        _output = output;
        _orderManagementClient = _fixture.CreateOrderManagementClient();
        _tripManagementClient = _fixture.CreateTripManagementClient();
        _financialPaymentClient = _fixture.CreateFinancialPaymentClient();
        _networkFleetClient = _fixture.CreateNetworkFleetClient();
        _analyticsClient = _fixture.CreateAnalyticsClient();
    }

    [Fact]
    public async Task CompleteShipperWorkflow_ShouldExecuteSuccessfully()
    {
        // Arrange
        var testData = await SetupTestDataAsync();
        
        _output.WriteLine("Starting complete shipper workflow integration test");

        try
        {
            // Act & Assert - Execute complete workflow
            await ExecuteRFQManagementWorkflow(testData);
            await ExecuteQuoteReviewAndFinalization(testData);
            await ExecuteMultiLegTripManagement(testData);
            await ExecuteEscrowPaymentWorkflow(testData);
            await ExecutePartnerManagementWorkflow(testData);
            await ExecuteReportingAndAnalytics(testData);

            _output.WriteLine("Complete shipper workflow executed successfully");
        }
        catch (Exception ex)
        {
            _output.WriteLine($"Workflow failed: {ex.Message}");
            throw;
        }
    }

    [Fact]
    public async Task RFQManagementEnhancements_ShouldWorkCorrectly()
    {
        // Arrange
        var testData = await SetupTestDataAsync();

        // Test Preferred Partner System
        var createPartnerRequest = new
        {
            PartnerId = testData.CarrierId,
            PartnerType = "Carrier",
            PreferenceLevel = "Primary",
            Priority = 1,
            PreferredCommissionRate = 5.0m,
            AutoAssignEnabled = true
        };

        var partnerResponse = await _orderManagementClient.PostAsJsonAsync(
            "/api/preferred-partners", createPartnerRequest);
        Assert.True(partnerResponse.IsSuccessStatusCode);

        // Test Advanced Negotiation Workflow
        var negotiationRequest = new
        {
            RFQId = testData.RFQId,
            QuoteId = testData.QuoteId,
            CounterOfferAmount = 1500.00m,
            CounterOfferCurrency = "USD",
            ExpiresAt = DateTime.UtcNow.AddDays(2),
            Notes = "Integration test counter offer"
        };

        var negotiationResponse = await _orderManagementClient.PostAsJsonAsync(
            "/api/negotiations/counter-offer", negotiationRequest);
        Assert.True(negotiationResponse.IsSuccessStatusCode);

        // Test Quote Comparison
        var comparisonRequest = new
        {
            RFQId = testData.RFQId,
            QuoteIds = new[] { testData.QuoteId, testData.AlternativeQuoteId },
            ComparisonCriteria = new[] { "Price", "DeliveryTime", "Rating" }
        };

        var comparisonResponse = await _orderManagementClient.PostAsJsonAsync(
            "/api/quotes/compare", comparisonRequest);
        Assert.True(comparisonResponse.IsSuccessStatusCode);

        _output.WriteLine("RFQ Management Enhancements tested successfully");
    }

    [Fact]
    public async Task MultiLegTripManagement_ShouldHandleComplexTrips()
    {
        // Arrange
        var testData = await SetupTestDataAsync();

        // Test Multi-Leg Trip Creation
        var tripRequest = new
        {
            OrderId = testData.OrderId,
            TripLegs = new[]
            {
                new
                {
                    SequenceNumber = 1,
                    OriginLocation = new { Latitude = 40.7128m, Longitude = -74.0060m, Address = "New York, NY" },
                    DestinationLocation = new { Latitude = 41.8781m, Longitude = -87.6298m, Address = "Chicago, IL" },
                    ScheduledStartTime = DateTime.UtcNow.AddDays(1),
                    ScheduledEndTime = DateTime.UtcNow.AddDays(2),
                    EstimatedDistance = 790.5m,
                    EstimatedDuration = TimeSpan.FromHours(12)
                },
                new
                {
                    SequenceNumber = 2,
                    OriginLocation = new { Latitude = 41.8781m, Longitude = -87.6298m, Address = "Chicago, IL" },
                    DestinationLocation = new { Latitude = 39.7392m, Longitude = -104.9903m, Address = "Denver, CO" },
                    ScheduledStartTime = DateTime.UtcNow.AddDays(2),
                    ScheduledEndTime = DateTime.UtcNow.AddDays(3),
                    EstimatedDistance = 920.3m,
                    EstimatedDuration = TimeSpan.FromHours(14)
                }
            }
        };

        var tripResponse = await _tripManagementClient.PostAsJsonAsync(
            "/api/trips/multi-leg", tripRequest);
        Assert.True(tripResponse.IsSuccessStatusCode);

        var tripResult = await tripResponse.Content.ReadFromJsonAsync<dynamic>();
        var tripId = tripResult?.GetProperty("tripId").GetGuid();

        // Test Resource Assignment
        var assignmentRequest = new
        {
            TripId = tripId,
            LegAssignments = new[]
            {
                new
                {
                    LegSequence = 1,
                    VehicleId = testData.VehicleId,
                    DriverId = testData.DriverId
                },
                new
                {
                    LegSequence = 2,
                    VehicleId = testData.AlternativeVehicleId,
                    DriverId = testData.AlternativeDriverId
                }
            }
        };

        var assignmentResponse = await _tripManagementClient.PostAsJsonAsync(
            $"/api/trips/{tripId}/assign-resources", assignmentRequest);
        Assert.True(assignmentResponse.IsSuccessStatusCode);

        // Test Trip Plan Generation
        var planResponse = await _tripManagementClient.GetAsync(
            $"/api/trips/{tripId}/plan?includeResourceAllocation=true");
        Assert.True(planResponse.IsSuccessStatusCode);

        _output.WriteLine("Multi-Leg Trip Management tested successfully");
    }

    [Fact]
    public async Task EscrowPaymentIntegration_ShouldProcessMilestonePayments()
    {
        // Arrange
        var testData = await SetupTestDataAsync();

        // Test Escrow Account Creation
        var escrowRequest = new
        {
            OrderId = testData.OrderId,
            TransportCompanyId = testData.TransportCompanyId,
            BrokerId = testData.BrokerId,
            CarrierId = testData.CarrierId,
            TotalAmount = new { Amount = 2000.00m, Currency = "USD" },
            FundingSource = "CreditCard"
        };

        var escrowResponse = await _financialPaymentClient.PostAsJsonAsync(
            "/api/escrow", escrowRequest);
        Assert.True(escrowResponse.IsSuccessStatusCode);

        var escrowResult = await escrowResponse.Content.ReadFromJsonAsync<dynamic>();
        var escrowAccountId = escrowResult?.GetProperty("escrowAccountId").GetGuid();

        // Test Payment Milestone Creation
        var milestoneRequest = new
        {
            EscrowAccountId = escrowAccountId,
            Name = "Pickup Completion",
            Description = "Payment released upon successful pickup",
            Amount = new { Amount = 800.00m, Currency = "USD" },
            PayoutPercentage = 40.0m,
            SequenceNumber = 1,
            DueDate = DateTime.UtcNow.AddDays(1),
            IsRequired = true,
            RequiresApproval = false,
            CompletionCriteria = new[] { "POD Uploaded", "Customer Confirmation" }
        };

        var milestoneResponse = await _financialPaymentClient.PostAsJsonAsync(
            $"/api/escrow/{escrowAccountId}/milestones", milestoneRequest);
        Assert.True(milestoneResponse.IsSuccessStatusCode);

        var milestoneResult = await milestoneResponse.Content.ReadFromJsonAsync<dynamic>();
        var milestoneId = milestoneResult?.GetProperty("milestoneId").GetGuid();

        // Test Enhanced Escrow Account Details
        var detailsResponse = await _financialPaymentClient.GetAsync(
            $"/api/escrow/{escrowAccountId}/enhanced?includeTransactions=true&includeMilestoneDocuments=true");
        Assert.True(detailsResponse.IsSuccessStatusCode);

        _output.WriteLine("Escrow Payment Integration tested successfully");
    }

    [Fact]
    public async Task PreferredPartnerManagement_ShouldProvideIntelligentRecommendations()
    {
        // Arrange
        var testData = await SetupTestDataAsync();

        // Test Partner Wishlist Creation
        var wishlistRequest = new
        {
            WishlistName = "Premium Carriers",
            Description = "High-performance carriers for critical shipments",
            PartnerType = "Carrier",
            Partners = new[]
            {
                new
                {
                    PartnerId = testData.CarrierId,
                    PreferenceLevel = "Primary",
                    Priority = 1,
                    AutoAssignEnabled = true,
                    PreferredRoutes = new[] { "NY-CA", "TX-FL" },
                    NotifyOnNewOpportunities = true
                }
            },
            Settings = new
            {
                AutoAddHighPerformers = true,
                AutoAddThreshold = 4.5m,
                EnablePerformanceAlerts = true
            }
        };

        var wishlistResponse = await _networkFleetClient.PostAsJsonAsync(
            "/api/preferred-partners/wishlist", wishlistRequest);
        Assert.True(wishlistResponse.IsSuccessStatusCode);

        // Test Performance Analytics Generation
        var analyticsRequest = new
        {
            PartnerType = "Carrier",
            FromDate = DateTime.UtcNow.AddDays(-90),
            ToDate = DateTime.UtcNow,
            Scope = "PreferredPartnersOnly",
            Metrics = new[] { "OnTimeDeliveryRate", "CustomerSatisfactionScore", "SafetyRating" },
            IncludePredictiveInsights = true,
            IncludeRecommendations = true,
            IncludeBenchmarking = true
        };

        var analyticsResponse = await _networkFleetClient.PostAsJsonAsync(
            "/api/preferred-partners/analytics/generate", analyticsRequest);
        Assert.True(analyticsResponse.IsSuccessStatusCode);

        // Test Enhanced Partner Recommendations
        var recommendationsResponse = await _networkFleetClient.GetAsync(
            "/api/preferred-partners/recommendations/enhanced?criteria=Balanced&maxRecommendations=10&includePerformanceAnalysis=true");
        Assert.True(recommendationsResponse.IsSuccessStatusCode);

        _output.WriteLine("Preferred Partner Management tested successfully");
    }

    [Fact]
    public async Task AdvancedReportingAnalytics_ShouldGenerateComprehensiveReports()
    {
        // Arrange
        var testData = await SetupTestDataAsync();

        // Test Comprehensive Report Generation
        var reportRequest = new
        {
            ReportType = "ShipperPortalSummary",
            DateRange = new
            {
                StartDate = DateTime.UtcNow.AddDays(-30),
                EndDate = DateTime.UtcNow,
                Period = "Monthly"
            },
            DataSources = new[] { "OrderManagement", "TripManagement", "FinancialPayment", "NetworkFleetManagement" },
            Metrics = new[] { "TotalOrders", "OnTimeDeliveryRate", "AverageOrderValue", "PartnerPerformance" },
            IncludeVisualizations = true,
            IncludeInsights = true,
            IncludePredictiveAnalytics = true,
            OutputFormat = "PDF"
        };

        var reportResponse = await _analyticsClient.PostAsJsonAsync(
            "/api/comprehensive-reporting/generate", reportRequest);
        Assert.True(reportResponse.IsSuccessStatusCode);

        // Test Cross-Service Analytics
        var crossServiceRequest = new
        {
            Services = new[] { "OrderManagement", "TripManagement", "FinancialPayment" },
            DateRange = new
            {
                StartDate = DateTime.UtcNow.AddDays(-30),
                EndDate = DateTime.UtcNow
            },
            Metrics = new[] { "OrderVolume", "TripCompletionRate", "PaymentProcessingTime" },
            IncludeCorrelations = true,
            IncludeInsights = true,
            CorrelationThreshold = 0.5m,
            Depth = "Standard"
        };

        var crossServiceResponse = await _analyticsClient.PostAsJsonAsync(
            "/api/comprehensive-reporting/cross-service-analytics", crossServiceRequest);
        Assert.True(crossServiceResponse.IsSuccessStatusCode);

        // Test Custom Report Template Creation
        var templateRequest = new
        {
            Name = "Daily Operations Report",
            Description = "Daily summary of operations metrics",
            Category = "Operations",
            Configuration = new
            {
                DataSources = new[] { "OrderManagement", "TripManagement" },
                Fields = new[]
                {
                    new { FieldName = "OrderCount", DisplayName = "Total Orders", DataType = "int", Source = "OrderManagement" },
                    new { FieldName = "CompletedTrips", DisplayName = "Completed Trips", DataType = "int", Source = "TripManagement" }
                },
                Visualizations = new[]
                {
                    new { Type = "Bar", Title = "Daily Order Volume", DataFields = new[] { "OrderCount" } }
                }
            }
        };

        var templateResponse = await _analyticsClient.PostAsJsonAsync(
            "/api/comprehensive-reporting/templates", templateRequest);
        Assert.True(templateResponse.IsSuccessStatusCode);

        _output.WriteLine("Advanced Reporting & Analytics tested successfully");
    }

    [Fact]
    public async Task EventDrivenCommunication_ShouldWorkAcrossServices()
    {
        // Arrange
        var testData = await SetupTestDataAsync();

        // Test Order Creation Event Propagation
        var orderRequest = new
        {
            ShipperId = testData.ShipperId,
            OriginLocation = new { Address = "New York, NY" },
            DestinationLocation = new { Address = "Los Angeles, CA" },
            ScheduledPickupDate = DateTime.UtcNow.AddDays(1),
            ScheduledDeliveryDate = DateTime.UtcNow.AddDays(3),
            LoadDetails = new { Weight = 1000, Volume = 50 }
        };

        var orderResponse = await _orderManagementClient.PostAsJsonAsync(
            "/api/orders", orderRequest);
        Assert.True(orderResponse.IsSuccessStatusCode);

        var orderResult = await orderResponse.Content.ReadFromJsonAsync<dynamic>();
        var orderId = orderResult?.GetProperty("orderId").GetGuid();

        // Wait for event propagation
        await Task.Delay(2000);

        // Verify Trip was created automatically
        var tripResponse = await _tripManagementClient.GetAsync(
            $"/api/trips/by-order/{orderId}");
        Assert.True(tripResponse.IsSuccessStatusCode);

        // Verify Escrow Account was created
        var escrowResponse = await _financialPaymentClient.GetAsync(
            $"/api/escrow/by-order/{orderId}");
        Assert.True(escrowResponse.IsSuccessStatusCode);

        _output.WriteLine("Event-driven communication tested successfully");
    }

    [Fact]
    public async Task DataConsistency_ShouldBeMaintainedAcrossServices()
    {
        // Arrange
        var testData = await SetupTestDataAsync();

        // Test data consistency across services
        var orderId = testData.OrderId;

        // Get order from OrderManagement
        var orderResponse = await _orderManagementClient.GetAsync($"/api/orders/{orderId}");
        Assert.True(orderResponse.IsSuccessStatusCode);
        var order = await orderResponse.Content.ReadFromJsonAsync<dynamic>();

        // Get related trip from TripManagement
        var tripResponse = await _tripManagementClient.GetAsync($"/api/trips/by-order/{orderId}");
        Assert.True(tripResponse.IsSuccessStatusCode);
        var trip = await tripResponse.Content.ReadFromJsonAsync<dynamic>();

        // Get related escrow from FinancialPayment
        var escrowResponse = await _financialPaymentClient.GetAsync($"/api/escrow/by-order/{orderId}");
        Assert.True(escrowResponse.IsSuccessStatusCode);
        var escrow = await escrowResponse.Content.ReadFromJsonAsync<dynamic>();

        // Verify data consistency
        Assert.Equal(order?.GetProperty("id").GetGuid(), trip?.GetProperty("orderId").GetGuid());
        Assert.Equal(order?.GetProperty("id").GetGuid(), escrow?.GetProperty("orderId").GetGuid());

        _output.WriteLine("Data consistency verified across services");
    }

    [Fact]
    public async Task ErrorHandling_ShouldBeRobustAcrossServices()
    {
        // Test error handling and resilience

        // Test invalid order creation
        var invalidOrderRequest = new
        {
            ShipperId = Guid.Empty, // Invalid shipper ID
            OriginLocation = new { Address = "" }, // Empty address
            DestinationLocation = new { Address = "" }
        };

        var invalidOrderResponse = await _orderManagementClient.PostAsJsonAsync(
            "/api/orders", invalidOrderRequest);
        Assert.False(invalidOrderResponse.IsSuccessStatusCode);

        // Test non-existent resource access
        var nonExistentId = Guid.NewGuid();
        var notFoundResponse = await _tripManagementClient.GetAsync($"/api/trips/{nonExistentId}");
        Assert.Equal(System.Net.HttpStatusCode.NotFound, notFoundResponse.StatusCode);

        // Test unauthorized access
        var unauthorizedResponse = await _financialPaymentClient.GetAsync("/api/escrow/admin-only-endpoint");
        Assert.True(unauthorizedResponse.StatusCode == System.Net.HttpStatusCode.Unauthorized ||
                   unauthorizedResponse.StatusCode == System.Net.HttpStatusCode.Forbidden);

        _output.WriteLine("Error handling tested successfully");
    }

    // Helper methods for workflow execution
    private async Task ExecuteRFQManagementWorkflow(TestData testData)
    {
        // Implementation of RFQ workflow steps
        _output.WriteLine("Executing RFQ Management workflow...");
        // Add specific workflow steps here
    }

    private async Task ExecuteQuoteReviewAndFinalization(TestData testData)
    {
        // Implementation of quote review workflow
        _output.WriteLine("Executing Quote Review and Finalization workflow...");
        // Add specific workflow steps here
    }

    private async Task ExecuteMultiLegTripManagement(TestData testData)
    {
        // Implementation of multi-leg trip workflow
        _output.WriteLine("Executing Multi-Leg Trip Management workflow...");
        // Add specific workflow steps here
    }

    private async Task ExecuteEscrowPaymentWorkflow(TestData testData)
    {
        // Implementation of escrow payment workflow
        _output.WriteLine("Executing Escrow Payment workflow...");
        // Add specific workflow steps here
    }

    private async Task ExecutePartnerManagementWorkflow(TestData testData)
    {
        // Implementation of partner management workflow
        _output.WriteLine("Executing Partner Management workflow...");
        // Add specific workflow steps here
    }

    private async Task ExecuteReportingAndAnalytics(TestData testData)
    {
        // Implementation of reporting and analytics workflow
        _output.WriteLine("Executing Reporting and Analytics workflow...");
        // Add specific workflow steps here
    }

    private async Task<TestData> SetupTestDataAsync()
    {
        // Setup test data for integration tests
        return new TestData
        {
            ShipperId = Guid.NewGuid(),
            OrderId = Guid.NewGuid(),
            RFQId = Guid.NewGuid(),
            QuoteId = Guid.NewGuid(),
            AlternativeQuoteId = Guid.NewGuid(),
            CarrierId = Guid.NewGuid(),
            TransportCompanyId = Guid.NewGuid(),
            BrokerId = Guid.NewGuid(),
            VehicleId = Guid.NewGuid(),
            DriverId = Guid.NewGuid(),
            AlternativeVehicleId = Guid.NewGuid(),
            AlternativeDriverId = Guid.NewGuid()
        };
    }
}

public class TestData
{
    public Guid ShipperId { get; set; }
    public Guid OrderId { get; set; }
    public Guid RFQId { get; set; }
    public Guid QuoteId { get; set; }
    public Guid AlternativeQuoteId { get; set; }
    public Guid CarrierId { get; set; }
    public Guid TransportCompanyId { get; set; }
    public Guid BrokerId { get; set; }
    public Guid VehicleId { get; set; }
    public Guid DriverId { get; set; }
    public Guid AlternativeVehicleId { get; set; }
    public Guid AlternativeDriverId { get; set; }
}
