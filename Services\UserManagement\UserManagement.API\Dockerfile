FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy project files
COPY ["Services/UserManagement/UserManagement.API/UserManagement.API.csproj", "Services/UserManagement/UserManagement.API/"]
COPY ["Services/UserManagement/UserManagement.Application/UserManagement.Application.csproj", "Services/UserManagement/UserManagement.Application/"]
COPY ["Services/UserManagement/UserManagement.Domain/UserManagement.Domain.csproj", "Services/UserManagement/UserManagement.Domain/"]
COPY ["Services/UserManagement/UserManagement.Infrastructure/UserManagement.Infrastructure.csproj", "Services/UserManagement/UserManagement.Infrastructure/"]
COPY ["Shared/Shared.Domain/Shared.Domain.csproj", "Shared/Shared.Domain/"]
COPY ["Shared/Shared.Infrastructure/Shared.Infrastructure.csproj", "Shared/Shared.Infrastructure/"]
COPY ["Shared/Shared.Messaging/Shared.Messaging.csproj", "Shared/Shared.Messaging/"]

# Restore dependencies
RUN dotnet restore "Services/UserManagement/UserManagement.API/UserManagement.API.csproj"

# Copy source code
COPY . .

# Build the application
WORKDIR "/src/Services/UserManagement/UserManagement.API"
RUN dotnet build "UserManagement.API.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "UserManagement.API.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "UserManagement.API.dll"]
