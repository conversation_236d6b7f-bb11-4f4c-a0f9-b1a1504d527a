using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Domain.Entities;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace CommunicationNotification.Infrastructure.Controllers;

/// <summary>
/// IVR API controller for managing Interactive Voice Response systems
/// </summary>
[ApiController]
[Route("api/communication/ivr")]
[Authorize]
public class IVRController : ControllerBase
{
    private readonly IIVRService _ivrService;
    private readonly ITwiMLService _twimlService;
    private readonly IVoiceRecognitionService _voiceRecognitionService;

    public IVRController(
        IIVRService ivrService,
        ITwiMLService twimlService,
        IVoiceRecognitionService voiceRecognitionService)
    {
        _ivrService = ivrService;
        _twimlService = twimlService;
        _voiceRecognitionService = voiceRecognitionService;
    }

    /// <summary>
    /// Create a new IVR system
    /// </summary>
    [HttpPost("systems")]
    public async Task<IActionResult> CreateIVRSystem([FromBody] CreateIVRSystemRequest request)
    {
        var userId = GetCurrentUserId();
        if (userId == Guid.Empty)
        {
            return Unauthorized("User ID not found");
        }

        var ivrSystem = await _ivrService.CreateIVRSystemAsync(
            request.Name,
            request.Description,
            request.WelcomeMessage,
            request.DefaultLanguage,
            userId);

        return Ok(ivrSystem);
    }

    /// <summary>
    /// Get IVR system by ID
    /// </summary>
    [HttpGet("systems/{systemId}")]
    public async Task<IActionResult> GetIVRSystem(Guid systemId)
    {
        var ivrSystem = await _ivrService.GetIVRSystemAsync(systemId);
        if (ivrSystem == null)
        {
            return NotFound($"IVR system {systemId} not found");
        }

        return Ok(ivrSystem);
    }

    /// <summary>
    /// Get all IVR systems
    /// </summary>
    [HttpGet("systems")]
    public async Task<IActionResult> GetAllIVRSystems()
    {
        var systems = await _ivrService.GetAllIVRSystemsAsync();
        return Ok(systems);
    }

    /// <summary>
    /// Update IVR system
    /// </summary>
    [HttpPut("systems/{systemId}")]
    public async Task<IActionResult> UpdateIVRSystem(Guid systemId, [FromBody] IVRSystem updatedSystem)
    {
        var system = await _ivrService.UpdateIVRSystemAsync(systemId, updatedSystem);
        return Ok(system);
    }

    /// <summary>
    /// Delete IVR system
    /// </summary>
    [HttpDelete("systems/{systemId}")]
    public async Task<IActionResult> DeleteIVRSystem(Guid systemId)
    {
        await _ivrService.DeleteIVRSystemAsync(systemId);
        return NoContent();
    }

    /// <summary>
    /// Activate IVR system
    /// </summary>
    [HttpPost("systems/{systemId}/activate")]
    public async Task<IActionResult> ActivateIVRSystem(Guid systemId)
    {
        var system = await _ivrService.ActivateIVRSystemAsync(systemId);
        return Ok(system);
    }

    /// <summary>
    /// Deactivate IVR system
    /// </summary>
    [HttpPost("systems/{systemId}/deactivate")]
    public async Task<IActionResult> DeactivateIVRSystem(Guid systemId)
    {
        var system = await _ivrService.DeactivateIVRSystemAsync(systemId);
        return Ok(system);
    }

    /// <summary>
    /// Get IVR analytics
    /// </summary>
    [HttpGet("systems/{systemId}/analytics")]
    public async Task<IActionResult> GetIVRAnalytics(
        Guid systemId,
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null)
    {
        var analytics = await _ivrService.GetIVRAnalyticsAsync(systemId, startDate, endDate);
        return Ok(analytics);
    }

    /// <summary>
    /// Webhook endpoint for incoming calls (Twilio)
    /// </summary>
    [HttpPost("webhook/incoming")]
    [AllowAnonymous]
    public async Task<IActionResult> HandleIncomingCall([FromForm] TwilioIncomingCallRequest request)
    {
        try
        {
            // Start IVR session for the incoming call
            // For demo, use a default IVR system - in production, this would be determined by the called number
            var defaultSystemId = Guid.Parse("00000000-0000-0000-0000-000000000001"); // Replace with actual system ID
            
            var session = await _ivrService.StartIVRSessionAsync(
                request.CallSid,
                request.From,
                defaultSystemId,
                "en-US");

            // Get the IVR system and main menu
            var ivrSystem = await _ivrService.GetIVRSystemAsync(defaultSystemId);
            if (ivrSystem == null)
            {
                return Content("<Response><Say>Service temporarily unavailable</Say><Hangup/></Response>", "application/xml");
            }

            var mainMenu = ivrSystem.GetMainMenu();
            if (mainMenu == null)
            {
                return Content("<Response><Say>Service temporarily unavailable</Say><Hangup/></Response>", "application/xml");
            }

            // Generate TwiML for the main menu
            var twiml = await _twimlService.GenerateMenuTwiMLAsync(mainMenu, session);
            return Content(twiml, "application/xml");
        }
        catch (Exception ex)
        {
            // Log error and return fallback TwiML
            return Content("<Response><Say>An error occurred. Please try again later.</Say><Hangup/></Response>", "application/xml");
        }
    }

    /// <summary>
    /// Webhook endpoint for processing user input
    /// </summary>
    [HttpPost("sessions/{sessionId}/input")]
    [AllowAnonymous]
    public async Task<IActionResult> ProcessUserInput(Guid sessionId, [FromForm] TwilioInputRequest request)
    {
        try
        {
            var input = request.Digits ?? request.SpeechResult ?? "";
            var result = await _ivrService.ProcessInputAsync(sessionId, input);

            if (result.IsSuccess)
            {
                if (result.ShouldEndCall)
                {
                    return Content("<Response><Hangup/></Response>", "application/xml");
                }

                if (!string.IsNullOrEmpty(result.TransferNumber))
                {
                    var transferTwiML = await _twimlService.GenerateTransferTwiMLAsync(result.TransferNumber);
                    return Content(transferTwiML, "application/xml");
                }

                return Content(result.TwiML ?? "<Response><Say>Processing...</Say></Response>", "application/xml");
            }
            else
            {
                return Content($"<Response><Say>{result.ErrorMessage}</Say><Hangup/></Response>", "application/xml");
            }
        }
        catch (Exception ex)
        {
            return Content("<Response><Say>An error occurred processing your input.</Say><Hangup/></Response>", "application/xml");
        }
    }

    /// <summary>
    /// Webhook endpoint for call status updates
    /// </summary>
    [HttpPost("webhook/status")]
    [AllowAnonymous]
    public async Task<IActionResult> HandleCallStatus([FromForm] TwilioCallStatusRequest request)
    {
        try
        {
            // Find session by call ID and update status
            var session = await _ivrService.GetIVRSessionAsync(Guid.Parse(request.CallSid));
            if (session != null)
            {
                var endStatus = request.CallStatus switch
                {
                    "completed" => IVRSessionStatus.Completed,
                    "failed" => IVRSessionStatus.Failed,
                    "busy" => IVRSessionStatus.Failed,
                    "no-answer" => IVRSessionStatus.Abandoned,
                    _ => IVRSessionStatus.Abandoned
                };

                await _ivrService.EndIVRSessionAsync(session.SessionId, endStatus, request.CallStatus);
            }

            return Ok();
        }
        catch (Exception ex)
        {
            return Ok(); // Always return OK to Twilio to prevent retries
        }
    }

    /// <summary>
    /// Get active IVR sessions
    /// </summary>
    [HttpGet("sessions/active")]
    public async Task<IActionResult> GetActiveSessions()
    {
        var sessions = await _ivrService.GetActiveSessionsAsync();
        return Ok(sessions);
    }

    /// <summary>
    /// Get IVR session details
    /// </summary>
    [HttpGet("sessions/{sessionId}")]
    public async Task<IActionResult> GetIVRSession(Guid sessionId)
    {
        var session = await _ivrService.GetIVRSessionAsync(sessionId);
        if (session == null)
        {
            return NotFound($"IVR session {sessionId} not found");
        }

        return Ok(session);
    }

    /// <summary>
    /// End IVR session manually
    /// </summary>
    [HttpPost("sessions/{sessionId}/end")]
    public async Task<IActionResult> EndIVRSession(Guid sessionId, [FromBody] EndSessionRequest request)
    {
        await _ivrService.EndIVRSessionAsync(sessionId, request.Status, request.Reason);
        return Ok();
    }

    /// <summary>
    /// Get supported languages for voice recognition
    /// </summary>
    [HttpGet("voice-recognition/languages")]
    public async Task<IActionResult> GetSupportedLanguages()
    {
        var languages = await _voiceRecognitionService.GetSupportedLanguagesAsync();
        return Ok(languages);
    }

    /// <summary>
    /// Test voice recognition with audio file
    /// </summary>
    [HttpPost("voice-recognition/test")]
    public async Task<IActionResult> TestVoiceRecognition([FromBody] VoiceRecognitionTestRequest request)
    {
        var result = await _voiceRecognitionService.RecognizeSpeechAsync(request.AudioUrl, request.Language);
        return Ok(result);
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return Guid.TryParse(userIdClaim, out var userId) ? userId : Guid.Empty;
    }
}

// Request/Response DTOs
public class CreateIVRSystemRequest
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string WelcomeMessage { get; set; } = string.Empty;
    public string DefaultLanguage { get; set; } = "en-US";
}

public class TwilioIncomingCallRequest
{
    public string CallSid { get; set; } = string.Empty;
    public string From { get; set; } = string.Empty;
    public string To { get; set; } = string.Empty;
    public string CallStatus { get; set; } = string.Empty;
}

public class TwilioInputRequest
{
    public string? Digits { get; set; }
    public string? SpeechResult { get; set; }
    public string? Confidence { get; set; }
}

public class TwilioCallStatusRequest
{
    public string CallSid { get; set; } = string.Empty;
    public string CallStatus { get; set; } = string.Empty;
    public string? CallDuration { get; set; }
}

public class EndSessionRequest
{
    public IVRSessionStatus Status { get; set; }
    public string? Reason { get; set; }
}

public class VoiceRecognitionTestRequest
{
    public string AudioUrl { get; set; } = string.Empty;
    public string Language { get; set; } = "en-US";
}
