using MediatR;
using Microsoft.Extensions.Logging;
using TripManagement.Domain.Repositories;
using TripManagement.Domain.Enums;

namespace TripManagement.Application.Queries.GetDriverLocationStatus;

public class GetDriverLocationStatusQueryHandler : IRequestHandler<GetDriverLocationStatusQuery, GetDriverLocationStatusResponse>
{
    private readonly IDriverRepository _driverRepository;
    private readonly IDriverLocationPreferencesRepository _preferencesRepository;
    private readonly ITripRepository _tripRepository;
    private readonly ILogger<GetDriverLocationStatusQueryHandler> _logger;

    public GetDriverLocationStatusQueryHandler(
        IDriverRepository driverRepository,
        IDriverLocationPreferencesRepository preferencesRepository,
        ITripRepository tripRepository,
        ILogger<GetDriverLocationStatusQueryHandler> logger)
    {
        _driverRepository = driverRepository;
        _preferencesRepository = preferencesRepository;
        _tripRepository = tripRepository;
        _logger = logger;
    }

    public async Task<GetDriverLocationStatusResponse> Handle(GetDriverLocationStatusQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Getting location status for driver {DriverId}", request.DriverId);

            // Get driver
            var driver = await _driverRepository.GetByIdAsync(request.DriverId, cancellationToken);
            if (driver == null)
            {
                return new GetDriverLocationStatusResponse
                {
                    IsSuccess = false,
                    ErrorMessage = "Driver not found"
                };
            }

            // Get location preferences
            var preferences = await _preferencesRepository.GetByDriverIdAsync(request.DriverId, cancellationToken);
            if (preferences == null)
            {
                return new GetDriverLocationStatusResponse
                {
                    IsSuccess = false,
                    ErrorMessage = "Location preferences not found for driver"
                };
            }

            // Get current trip if any
            var currentTrip = await GetCurrentTripForDriver(request.DriverId, cancellationToken);

            // Build location status DTO
            var locationStatus = new DriverLocationStatusDto
            {
                DriverId = driver.Id,
                DriverName = driver.FullName,
                LocationSharingEnabled = preferences.LocationSharingEnabled,
                ManualToggleAllowed = preferences.ManualToggleAllowed,
                LocationUpdateIntervalSeconds = preferences.LocationUpdateIntervalSeconds,
                ShowGpsTimestamp = preferences.ShowGpsTimestamp,
                HighAccuracyMode = preferences.HighAccuracyMode,
                BackgroundLocationEnabled = preferences.BackgroundLocationEnabled,
                GeofenceAlertsEnabled = preferences.GeofenceAlertsEnabled,
                LocationAccuracyThresholdMeters = preferences.LocationAccuracyThresholdMeters,
                
                // Current location data
                CurrentLatitude = preferences.LastLatitude,
                CurrentLongitude = preferences.LastLongitude,
                CurrentAccuracy = preferences.LastAccuracy,
                LastGpsTimestamp = preferences.LastGpsTimestamp,
                LastKnownLocation = preferences.LastKnownLocation,
                LastLocationUpdate = preferences.LastLocationUpdate,
                
                // Status indicators
                IsLocationStale = preferences.IsLocationDataStale(),
                IsLocationAccurate = preferences.IsLocationAccurate(),
                TimeSinceLastUpdate = preferences.GetTimeSinceLastUpdate(),
                LocationStatus = DetermineLocationStatus(preferences),
                
                // Trip context
                CurrentTripId = currentTrip?.Id,
                CurrentTripNumber = currentTrip?.TripNumber,
                IsOnTrip = currentTrip != null && currentTrip.Status == TripStatus.InProgress,
                
                // Additional settings
                AdditionalSettings = preferences.AdditionalSettings,
                PreferencesUpdatedAt = preferences.UpdatedAt
            };

            // Add location history if requested
            if (request.IncludeLocationHistory)
            {
                locationStatus.LocationHistory = await GetLocationHistory(request.DriverId, request.HistoryHours, cancellationToken);
            }

            return new GetDriverLocationStatusResponse
            {
                IsSuccess = true,
                LocationStatus = locationStatus
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting location status for driver {DriverId}", request.DriverId);
            return new GetDriverLocationStatusResponse
            {
                IsSuccess = false,
                ErrorMessage = ex.Message
            };
        }
    }

    private async Task<Domain.Entities.Trip?> GetCurrentTripForDriver(Guid driverId, CancellationToken cancellationToken)
    {
        try
        {
            // Get active trips for the driver
            var activeTrips = await _tripRepository.GetActiveByDriverIdAsync(driverId, cancellationToken);
            return activeTrips.FirstOrDefault(t => t.Status == TripStatus.InProgress || t.Status == TripStatus.Assigned);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error getting current trip for driver {DriverId}", driverId);
            return null;
        }
    }

    private string DetermineLocationStatus(Domain.Entities.DriverLocationPreferencesEntity preferences)
    {
        if (!preferences.LocationSharingEnabled)
            return "Disabled";

        if (preferences.LastGpsTimestamp == null)
            return "Unknown";

        if (preferences.IsLocationDataStale())
            return "Stale";

        if (!preferences.IsLocationAccurate())
            return "Inaccurate";

        return "Active";
    }

    private async Task<List<LocationHistoryDto>> GetLocationHistory(Guid driverId, int historyHours, CancellationToken cancellationToken)
    {
        try
        {
            // In a real implementation, this would query a location history table or service
            // For now, returning empty list as this would require additional infrastructure
            _logger.LogDebug("Location history requested for driver {DriverId} for last {Hours} hours", driverId, historyHours);
            
            // This would typically query from a dedicated location tracking table
            // that stores historical location updates with timestamps
            return new List<LocationHistoryDto>();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error getting location history for driver {DriverId}", driverId);
            return new List<LocationHistoryDto>();
        }
    }
}
