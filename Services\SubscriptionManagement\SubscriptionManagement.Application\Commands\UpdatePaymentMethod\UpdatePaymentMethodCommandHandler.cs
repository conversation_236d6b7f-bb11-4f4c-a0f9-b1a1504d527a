using MediatR;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Exceptions;
using SubscriptionManagement.Domain.Enums;
using Shared.Messaging;

namespace SubscriptionManagement.Application.Commands.UpdatePaymentMethod
{
    public class UpdatePaymentMethodCommandHandler : IRequestHandler<UpdatePaymentMethodCommand, bool>
    {
        private readonly ISubscriptionRepository _subscriptionRepository;
        private readonly IPaymentService _paymentService;
        private readonly IMessageBroker _messageBroker;
        private readonly ILogger<UpdatePaymentMethodCommandHandler> _logger;

        public UpdatePaymentMethodCommandHandler(
            ISubscriptionRepository subscriptionRepository,
            IPaymentService paymentService,
            IMessageBroker messageBroker,
            ILogger<UpdatePaymentMethodCommandHandler> logger)
        {
            _subscriptionRepository = subscriptionRepository;
            _paymentService = paymentService;
            _messageBroker = messageBroker;
            _logger = logger;
        }

        public async Task<bool> Handle(UpdatePaymentMethodCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Updating payment method for subscription {SubscriptionId} for user {UserId}", 
                request.SubscriptionId, request.UserId);

            // Get subscription
            var subscription = await _subscriptionRepository.GetByIdAsync(request.SubscriptionId);
            if (subscription == null)
            {
                throw new SubscriptionDomainException("Subscription not found");
            }

            // Authorization check
            if (subscription.UserId != request.UserId)
            {
                throw new SubscriptionDomainException("You can only update payment method for your own subscription");
            }

            // Validate payment method if requested
            if (request.ValidatePaymentMethod)
            {
                var validationResult = await _paymentService.ValidatePaymentMethodAsync(request.NewPaymentMethodId);
                if (!validationResult.IsSuccess)
                {
                    throw new SubscriptionDomainException($"Invalid payment method: {validationResult.ErrorMessage}");
                }
            }

            var oldPaymentMethodId = subscription.PaymentMethodId;

            // Update payment method
            subscription.UpdatePaymentMethod(request.NewPaymentMethodId, request.UpdateReason);
            
            // Save changes
            await _subscriptionRepository.UpdateAsync(subscription);

            // Publish payment method update event
            await _messageBroker.PublishAsync("subscription.payment_method_updated", new
            {
                SubscriptionId = subscription.Id,
                UserId = subscription.UserId,
                PlanId = subscription.PlanId,
                OldPaymentMethodId = oldPaymentMethodId,
                NewPaymentMethodId = request.NewPaymentMethodId,
                UpdateReason = request.UpdateReason,
                UpdatedAt = DateTime.UtcNow
            });

            _logger.LogInformation("Successfully updated payment method for subscription {SubscriptionId}", 
                request.SubscriptionId);

            return true;
        }
    }
}
