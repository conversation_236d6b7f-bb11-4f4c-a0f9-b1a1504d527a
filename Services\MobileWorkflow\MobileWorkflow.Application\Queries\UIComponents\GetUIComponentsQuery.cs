using System.Collections.Generic;
using MediatR;
using MobileWorkflow.Application.DTOs;

namespace MobileWorkflow.Application.Queries.UIComponents
{
    public class GetUIComponentsQuery : IRequest<List<UIComponentDto>>
    {
        public string? Category { get; set; }
        public string? ComponentType { get; set; }
        public string? Platform { get; set; }
        public List<string>? Tags { get; set; }
        public bool IncludeTemplates { get; set; } = true;
        public bool IncludeInactive { get; set; } = false;
        public bool PublicOnly { get; set; } = true;
        public string? SearchTerm { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 20;
    }
}
