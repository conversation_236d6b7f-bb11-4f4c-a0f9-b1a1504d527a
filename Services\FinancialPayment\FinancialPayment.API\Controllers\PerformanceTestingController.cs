using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using FinancialPayment.Application.Interfaces;

namespace FinancialPayment.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class PerformanceTestingController : ControllerBase
{
    private readonly IPerformanceTestingService _performanceTestingService;
    private readonly ILogger<PerformanceTestingController> _logger;

    public PerformanceTestingController(
        IPerformanceTestingService performanceTestingService,
        ILogger<PerformanceTestingController> logger)
    {
        _performanceTestingService = performanceTestingService;
        _logger = logger;
    }

    /// <summary>
    /// Run a load test
    /// </summary>
    [HttpPost("load-test")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<PerformanceTestResult>> RunLoadTest([FromBody] LoadTestConfiguration configuration)
    {
        try
        {
            var result = await _performanceTestingService.RunLoadTestAsync(configuration);
            
            _logger.LogInformation("Load test completed: {TestName} - Status: {Status}, Grade: {Grade}",
                configuration.TestName, result.Status, result.Grade);
            
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error running load test {TestName}", configuration.TestName);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Run a stress test
    /// </summary>
    [HttpPost("stress-test")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<PerformanceTestResult>> RunStressTest([FromBody] StressTestConfiguration configuration)
    {
        try
        {
            var result = await _performanceTestingService.RunStressTestAsync(configuration);
            
            _logger.LogInformation("Stress test completed: {TestName} - Status: {Status}, Grade: {Grade}",
                configuration.TestName, result.Status, result.Grade);
            
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error running stress test {TestName}", configuration.TestName);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Run a spike test
    /// </summary>
    [HttpPost("spike-test")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<PerformanceTestResult>> RunSpikeTest([FromBody] SpikeTestConfiguration configuration)
    {
        try
        {
            var result = await _performanceTestingService.RunSpikeTestAsync(configuration);
            
            _logger.LogInformation("Spike test completed: {TestName} - Status: {Status}, Grade: {Grade}",
                configuration.TestName, result.Status, result.Grade);
            
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error running spike test {TestName}", configuration.TestName);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Run an endurance test
    /// </summary>
    [HttpPost("endurance-test")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<PerformanceTestResult>> RunEnduranceTest([FromBody] EnduranceTestConfiguration configuration)
    {
        try
        {
            var result = await _performanceTestingService.RunEnduranceTestAsync(configuration);
            
            _logger.LogInformation("Endurance test completed: {TestName} - Status: {Status}, Grade: {Grade}",
                configuration.TestName, result.Status, result.Grade);
            
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error running endurance test {TestName}", configuration.TestName);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get test results
    /// </summary>
    [HttpGet("results")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<List<PerformanceTestResult>>> GetTestResults(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        try
        {
            var results = await _performanceTestingService.GetTestResultsAsync(fromDate, toDate);
            return Ok(results);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting test results");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get test result by ID
    /// </summary>
    [HttpGet("results/{testId}")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<PerformanceTestResult>> GetTestResult(Guid testId)
    {
        try
        {
            var result = await _performanceTestingService.GetTestResultAsync(testId);
            if (result == null)
            {
                return NotFound($"Test result {testId} not found");
            }

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting test result {TestId}", testId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Stop a running test
    /// </summary>
    [HttpPost("stop/{testId}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> StopTest(Guid testId)
    {
        try
        {
            var success = await _performanceTestingService.StopTestAsync(testId);
            if (!success)
            {
                return NotFound($"Test {testId} not found or not running");
            }

            _logger.LogInformation("Test {TestId} stopped", testId);
            return Ok(new { message = "Test stopped successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping test {TestId}", testId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get test scenarios
    /// </summary>
    [HttpGet("scenarios")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<List<PerformanceTestScenario>>> GetTestScenarios()
    {
        try
        {
            var scenarios = await _performanceTestingService.GetTestScenariosAsync();
            return Ok(scenarios);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting test scenarios");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Create test scenario
    /// </summary>
    [HttpPost("scenarios")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<PerformanceTestScenario>> CreateTestScenario([FromBody] CreateTestScenarioRequest request)
    {
        try
        {
            var scenario = await _performanceTestingService.CreateTestScenarioAsync(request);
            
            _logger.LogInformation("Test scenario created: {ScenarioName}", request.Name);
            return CreatedAtAction(nameof(GetTestScenarios), scenario);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating test scenario {ScenarioName}", request.Name);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get performance benchmark
    /// </summary>
    [HttpGet("benchmark")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<PerformanceBenchmark>> GetPerformanceBenchmark()
    {
        try
        {
            var benchmark = await _performanceTestingService.GetPerformanceBenchmarkAsync();
            return Ok(benchmark);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting performance benchmark");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Compare test results
    /// </summary>
    [HttpPost("compare")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<PerformanceComparison>> CompareTestResults([FromBody] CompareTestResultsRequest request)
    {
        try
        {
            var comparison = await _performanceTestingService.CompareTestResultsAsync(
                request.BaselineTestId, 
                request.ComparisonTestId);
            
            return Ok(comparison);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid test comparison request");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error comparing test results");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get performance test summary
    /// </summary>
    [HttpGet("summary")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<PerformanceTestSummary>> GetPerformanceTestSummary(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        try
        {
            var results = await _performanceTestingService.GetTestResultsAsync(fromDate, toDate);
            
            var summary = new PerformanceTestSummary
            {
                TotalTests = results.Count,
                SuccessfulTests = results.Count(r => r.Status == TestStatus.Completed),
                FailedTests = results.Count(r => r.Status == TestStatus.Failed),
                AverageGrade = results.Any() ? results.Average(r => (int)r.Grade) : 0,
                TestsByType = results.GroupBy(r => r.TestType).ToDictionary(g => g.Key, g => g.Count()),
                GradeDistribution = results.GroupBy(r => r.Grade).ToDictionary(g => g.Key.ToString(), g => g.Count()),
                RecentTests = results.OrderByDescending(r => r.StartTime).Take(10).ToList(),
                PeriodStart = fromDate ?? DateTime.UtcNow.AddDays(-30),
                PeriodEnd = toDate ?? DateTime.UtcNow
            };

            return Ok(summary);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting performance test summary");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Run quick performance check
    /// </summary>
    [HttpPost("quick-check")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<PerformanceTestResult>> RunQuickPerformanceCheck()
    {
        try
        {
            var configuration = new LoadTestConfiguration
            {
                TestName = $"Quick Check {DateTime.UtcNow:yyyy-MM-dd HH:mm}",
                ConcurrentUsers = 5,
                Duration = TimeSpan.FromMinutes(2),
                RampUpTime = TimeSpan.FromSeconds(30),
                Thresholds = new PerformanceThresholds
                {
                    MaxResponseTime = TimeSpan.FromSeconds(3),
                    MinSuccessRate = 95.0m,
                    MaxErrorRate = 5
                }
            };

            var result = await _performanceTestingService.RunLoadTestAsync(configuration);
            
            _logger.LogInformation("Quick performance check completed - Grade: {Grade}", result.Grade);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error running quick performance check");
            return StatusCode(500, "Internal server error");
        }
    }
}

// Request DTOs
public class CompareTestResultsRequest
{
    public Guid BaselineTestId { get; set; }
    public Guid ComparisonTestId { get; set; }
}

public class PerformanceTestSummary
{
    public int TotalTests { get; set; }
    public int SuccessfulTests { get; set; }
    public int FailedTests { get; set; }
    public double AverageGrade { get; set; }
    public Dictionary<string, int> TestsByType { get; set; } = new();
    public Dictionary<string, int> GradeDistribution { get; set; } = new();
    public List<PerformanceTestResult> RecentTests { get; set; } = new();
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
}
