using System;
using System.Collections.Generic;

namespace AnalyticsBIService.Application.DTOs;

/// <summary>
/// Route earnings data transfer object
/// </summary>
public class RouteEarningsDto
{
    public string RouteId { get; set; } = string.Empty;
    public string RouteName { get; set; } = string.Empty;
    public decimal TotalEarnings { get; set; }
    public decimal AverageEarningsPerTrip { get; set; }
    public int TotalTrips { get; set; }
    public int TripCount { get; set; } // Alias for TotalTrips
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
}

/// <summary>
/// Earnings projection data transfer object
/// </summary>
public class EarningsProjectionDto
{
    public DateTime ProjectionDate { get; set; }
    public decimal ProjectedEarnings { get; set; }
    public decimal ProjectedMonthlyEarnings { get; set; }
    public decimal ProjectedQuarterlyEarnings { get; set; }
    public decimal ProjectedYearlyEarnings { get; set; }
    public decimal ConfidenceLevel { get; set; }
    public string ProjectionMethod { get; set; } = string.Empty;
    public List<ProjectionFactorDto> Factors { get; set; } = new();
}

/// <summary>
/// Projection factor data transfer object
/// </summary>
public class ProjectionFactorDto
{
    public string FactorName { get; set; } = string.Empty;
    public decimal Impact { get; set; }
    public string Description { get; set; } = string.Empty;
}

/// <summary>
/// Fuel efficiency data transfer object
/// </summary>
public class FuelEfficiencyDto
{
    public decimal FuelEfficiencyRate { get; set; }
    public decimal FuelCostPerKilometer { get; set; }
    public List<FuelConsumptionTrendDto> FuelConsumptionTrends { get; set; } = new();
    public List<EfficiencyBenchmarkDto> EfficiencyBenchmarks { get; set; } = new();

    // Additional property required by GetCarrierAnalyticsQueryHandler
    public List<EarningsOptimizationOpportunityDto> ImprovementRecommendations { get; set; } = new();
}



/// <summary>
/// Efficiency benchmark data transfer object
/// </summary>
public class EfficiencyBenchmarkDto
{
    public string BenchmarkType { get; set; } = string.Empty;
    public decimal Value { get; set; }
    public string Comparison { get; set; } = string.Empty;
    public decimal Variance { get; set; }
}

/// <summary>
/// Carrier performance benchmarking data transfer object
/// </summary>
public class CarrierPerformanceBenchmarkingDto
{
    public decimal IndustryAverageEarnings { get; set; }
    public decimal CarrierEarnings { get; set; }
    public decimal EarningsCompetitiveness { get; set; }
    public string BenchmarkRating { get; set; } = string.Empty;
    public List<CategoryBenchmarkDto> CategoryBenchmarks { get; set; } = new();
}

/// <summary>
/// Category benchmark data transfer object
/// </summary>
public class CategoryBenchmarkDto
{
    public string CategoryName { get; set; } = string.Empty;
    public decimal CarrierValue { get; set; }
    public decimal IndustryAverage { get; set; }
    public decimal BestInClass { get; set; }
    public decimal Variance { get; set; }
    public string Performance { get; set; } = string.Empty;
}

/// <summary>
/// Earnings optimization opportunity data transfer object
/// </summary>
public class EarningsOptimizationOpportunityDto
{
    public string OpportunityType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal PotentialIncrease { get; set; }
    public decimal ImplementationEffort { get; set; }
    public string Priority { get; set; } = string.Empty;
    public List<string> ActionSteps { get; set; } = new();
    public decimal ROI { get; set; }
}

/// <summary>
/// Order type breakdown data transfer object
/// </summary>
public class OrderTypeBreakdownDto
{
    public string OrderType { get; set; } = string.Empty;
    public int Count { get; set; }
    public decimal Percentage { get; set; }
    public decimal TotalValue { get; set; }
    public decimal AverageValue { get; set; }
}

/// <summary>
/// Customer order volume data transfer object
/// </summary>
public class CustomerOrderVolumeDto
{
    public Guid CustomerId { get; set; }
    public string CustomerName { get; set; } = string.Empty;
    public int OrderCount { get; set; }
    public decimal TotalVolume { get; set; }
    public decimal TotalValue { get; set; }
    public decimal AverageOrderValue { get; set; }
    public DateTime FirstOrderDate { get; set; }
    public DateTime LastOrderDate { get; set; }
}

/// <summary>
/// Route on-time performance data transfer object
/// </summary>
public class RouteOnTimePerformanceDto
{
    public string RouteId { get; set; } = string.Empty;
    public string RouteName { get; set; } = string.Empty;
    public decimal OnTimePercentage { get; set; }
    public decimal OnTimeRate { get; set; } // Alias for OnTimePercentage
    public int TotalDeliveries { get; set; }
    public int OnTimeDeliveries { get; set; }
    public int LateDeliveries { get; set; }
    public decimal AverageDelayMinutes { get; set; }
    public List<DelayReasonDto> DelayReasons { get; set; } = new();
}

/// <summary>
/// Delay reason data transfer object
/// </summary>
public class DelayReasonDto
{
    public string Reason { get; set; } = string.Empty;
    public int Count { get; set; }
    public decimal Percentage { get; set; }
    public decimal AverageDelayMinutes { get; set; }
}

/// <summary>
/// Delivery performance factor data transfer object
/// </summary>
public class DeliveryPerformanceFactorDto
{
    public string FactorName { get; set; } = string.Empty;
    public string Factor { get; set; } = string.Empty; // Alias for FactorName
    public decimal Impact { get; set; }
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public bool IsControllable { get; set; }
}
