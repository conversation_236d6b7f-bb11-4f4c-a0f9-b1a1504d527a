# Financial & Payment Service Dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 8080
EXPOSE 8081

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy shared projects first
COPY ["Shared/Shared.Domain/Shared.Domain.csproj", "Shared/Shared.Domain/"]
COPY ["Shared/Shared.Infrastructure/Shared.Infrastructure.csproj", "Shared/Shared.Infrastructure/"]

# Copy Financial & Payment Service projects
COPY ["Services/FinancialPayment/FinancialPayment.API/FinancialPayment.API.csproj", "Services/FinancialPayment/FinancialPayment.API/"]
COPY ["Services/FinancialPayment/FinancialPayment.Application/FinancialPayment.Application.csproj", "Services/FinancialPayment/FinancialPayment.Application/"]
COPY ["Services/FinancialPayment/FinancialPayment.Domain/FinancialPayment.Domain.csproj", "Services/FinancialPayment/FinancialPayment.Domain/"]
COPY ["Services/FinancialPayment/FinancialPayment.Infrastructure/FinancialPayment.Infrastructure.csproj", "Services/FinancialPayment/FinancialPayment.Infrastructure/"]

# Restore dependencies
RUN dotnet restore "Services/FinancialPayment/FinancialPayment.API/FinancialPayment.API.csproj"

# Copy all source code
COPY . .

# Build the application
WORKDIR "/src/Services/FinancialPayment/FinancialPayment.API"
RUN dotnet build "FinancialPayment.API.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "FinancialPayment.API.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

# Create logs directory
RUN mkdir -p /app/logs

ENTRYPOINT ["dotnet", "FinancialPayment.API.dll"]
