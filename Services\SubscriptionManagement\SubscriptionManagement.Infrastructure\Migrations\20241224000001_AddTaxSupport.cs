using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SubscriptionManagement.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddTaxSupport : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Create TaxCategories table
            migrationBuilder.CreateTable(
                name: "TaxCategories",
                schema: "subscription",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    Code = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TaxCategories", x => x.Id);
                });

            // Create TaxCategoryConfigurations table
            migrationBuilder.CreateTable(
                name: "TaxCategoryConfigurations",
                schema: "subscription",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TaxCategoryId = table.Column<Guid>(type: "uuid", nullable: false),
                    TaxType = table.Column<string>(type: "text", nullable: false),
                    Rate = table.Column<decimal>(type: "numeric(5,4)", nullable: false),
                    IsIncluded = table.Column<bool>(type: "boolean", nullable: false),
                    EffectiveDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ExpirationDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ApplicableRegions = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TaxCategoryConfigurations", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TaxCategoryConfigurations_TaxCategories_TaxCategoryId",
                        column: x => x.TaxCategoryId,
                        principalSchema: "subscription",
                        principalTable: "TaxCategories",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            // Create TaxExemptions table
            migrationBuilder.CreateTable(
                name: "TaxExemptions",
                schema: "subscription",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    UserId = table.Column<Guid>(type: "uuid", nullable: false),
                    ExemptionType = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    ExemptionNumber = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    IssuingAuthority = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    ValidFrom = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ValidTo = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ExemptTaxTypes = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    ApplicableRegions = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    DocumentPath = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    VerifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    VerifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    VerificationNotes = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TaxExemptions", x => x.Id);
                });

            // Create GlobalTaxConfigurations table
            migrationBuilder.CreateTable(
                name: "GlobalTaxConfigurations",
                schema: "subscription",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TaxType = table.Column<string>(type: "text", nullable: false),
                    Rate = table.Column<decimal>(type: "numeric(5,4)", nullable: false),
                    IsIncluded = table.Column<bool>(type: "boolean", nullable: false),
                    EffectiveDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ExpirationDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ApplicableRegions = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    Priority = table.Column<int>(type: "integer", nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: false),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_GlobalTaxConfigurations", x => x.Id);
                });

            // Create PlanTaxConfigurations table
            migrationBuilder.CreateTable(
                name: "PlanTaxConfigurations",
                schema: "subscription",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    PlanId = table.Column<Guid>(type: "uuid", nullable: false),
                    TaxType = table.Column<string>(type: "text", nullable: false),
                    Rate = table.Column<decimal>(type: "numeric(5,4)", nullable: false),
                    IsIncluded = table.Column<bool>(type: "boolean", nullable: false),
                    TaxEffectiveDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    TaxExpirationDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ApplicableRegions = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    EffectiveFrom = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    EffectiveTo = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Notes = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PlanTaxConfigurations", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PlanTaxConfigurations_Plans_PlanId",
                        column: x => x.PlanId,
                        principalSchema: "subscription",
                        principalTable: "Plans",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            // Add TaxCategoryId to Plans table
            migrationBuilder.AddColumn<Guid>(
                name: "TaxCategoryId",
                schema: "subscription",
                table: "Plans",
                type: "uuid",
                nullable: true);

            // Create indexes for TaxCategories
            migrationBuilder.CreateIndex(
                name: "IX_TaxCategories_Code",
                schema: "subscription",
                table: "TaxCategories",
                column: "Code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_TaxCategories_Name",
                schema: "subscription",
                table: "TaxCategories",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_TaxCategories_IsActive",
                schema: "subscription",
                table: "TaxCategories",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_TaxCategories_CreatedAt",
                schema: "subscription",
                table: "TaxCategories",
                column: "CreatedAt");

            // Create indexes for TaxCategoryConfigurations
            migrationBuilder.CreateIndex(
                name: "IX_TaxCategoryConfigurations_TaxCategoryId",
                schema: "subscription",
                table: "TaxCategoryConfigurations",
                column: "TaxCategoryId");

            // Create indexes for TaxExemptions
            migrationBuilder.CreateIndex(
                name: "IX_TaxExemptions_UserId",
                schema: "subscription",
                table: "TaxExemptions",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_TaxExemptions_ExemptionNumber",
                schema: "subscription",
                table: "TaxExemptions",
                column: "ExemptionNumber");

            migrationBuilder.CreateIndex(
                name: "IX_TaxExemptions_IsActive",
                schema: "subscription",
                table: "TaxExemptions",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_TaxExemptions_ValidFrom",
                schema: "subscription",
                table: "TaxExemptions",
                column: "ValidFrom");

            migrationBuilder.CreateIndex(
                name: "IX_TaxExemptions_ValidTo",
                schema: "subscription",
                table: "TaxExemptions",
                column: "ValidTo");

            migrationBuilder.CreateIndex(
                name: "IX_TaxExemptions_VerifiedAt",
                schema: "subscription",
                table: "TaxExemptions",
                column: "VerifiedAt");

            migrationBuilder.CreateIndex(
                name: "IX_TaxExemptions_CreatedAt",
                schema: "subscription",
                table: "TaxExemptions",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_TaxExemptions_UserId_IsActive",
                schema: "subscription",
                table: "TaxExemptions",
                columns: new[] { "UserId", "IsActive" });

            migrationBuilder.CreateIndex(
                name: "IX_TaxExemptions_ExemptionNumber_UserId",
                schema: "subscription",
                table: "TaxExemptions",
                columns: new[] { "ExemptionNumber", "UserId" },
                unique: true);

            // Create indexes for GlobalTaxConfigurations
            migrationBuilder.CreateIndex(
                name: "IX_GlobalTaxConfigurations_IsActive",
                schema: "subscription",
                table: "GlobalTaxConfigurations",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_GlobalTaxConfigurations_Priority",
                schema: "subscription",
                table: "GlobalTaxConfigurations",
                column: "Priority");

            migrationBuilder.CreateIndex(
                name: "IX_GlobalTaxConfigurations_CreatedByUserId",
                schema: "subscription",
                table: "GlobalTaxConfigurations",
                column: "CreatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_GlobalTaxConfigurations_CreatedAt",
                schema: "subscription",
                table: "GlobalTaxConfigurations",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_GlobalTaxConfigurations_TaxType",
                schema: "subscription",
                table: "GlobalTaxConfigurations",
                column: "TaxType");

            migrationBuilder.CreateIndex(
                name: "IX_GlobalTaxConfigurations_EffectiveDate",
                schema: "subscription",
                table: "GlobalTaxConfigurations",
                column: "EffectiveDate");

            migrationBuilder.CreateIndex(
                name: "IX_GlobalTaxConfigurations_ExpirationDate",
                schema: "subscription",
                table: "GlobalTaxConfigurations",
                column: "ExpirationDate");

            migrationBuilder.CreateIndex(
                name: "IX_GlobalTaxConfigurations_IsActive_Priority",
                schema: "subscription",
                table: "GlobalTaxConfigurations",
                columns: new[] { "IsActive", "Priority" });

            migrationBuilder.CreateIndex(
                name: "IX_GlobalTaxConfigurations_TaxType_IsActive",
                schema: "subscription",
                table: "GlobalTaxConfigurations",
                columns: new[] { "TaxType", "IsActive" });

            // Create indexes for PlanTaxConfigurations
            migrationBuilder.CreateIndex(
                name: "IX_PlanTaxConfigurations_PlanId",
                schema: "subscription",
                table: "PlanTaxConfigurations",
                column: "PlanId");

            migrationBuilder.CreateIndex(
                name: "IX_PlanTaxConfigurations_IsActive",
                schema: "subscription",
                table: "PlanTaxConfigurations",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_PlanTaxConfigurations_EffectiveFrom",
                schema: "subscription",
                table: "PlanTaxConfigurations",
                column: "EffectiveFrom");

            migrationBuilder.CreateIndex(
                name: "IX_PlanTaxConfigurations_EffectiveTo",
                schema: "subscription",
                table: "PlanTaxConfigurations",
                column: "EffectiveTo");

            migrationBuilder.CreateIndex(
                name: "IX_PlanTaxConfigurations_CreatedAt",
                schema: "subscription",
                table: "PlanTaxConfigurations",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_PlanTaxConfigurations_TaxType",
                schema: "subscription",
                table: "PlanTaxConfigurations",
                column: "TaxType");

            migrationBuilder.CreateIndex(
                name: "IX_PlanTaxConfigurations_PlanId_IsActive",
                schema: "subscription",
                table: "PlanTaxConfigurations",
                columns: new[] { "PlanId", "IsActive" });

            migrationBuilder.CreateIndex(
                name: "IX_PlanTaxConfigurations_PlanId_EffectiveFrom_EffectiveTo",
                schema: "subscription",
                table: "PlanTaxConfigurations",
                columns: new[] { "PlanId", "EffectiveFrom", "EffectiveTo" });

            migrationBuilder.CreateIndex(
                name: "IX_PlanTaxConfigurations_PlanId_TaxType",
                schema: "subscription",
                table: "PlanTaxConfigurations",
                columns: new[] { "PlanId", "TaxType" },
                unique: true,
                filter: "\"IsActive\" = true");

            // Add foreign key for Plans.TaxCategoryId
            migrationBuilder.CreateIndex(
                name: "IX_Plans_TaxCategoryId",
                schema: "subscription",
                table: "Plans",
                column: "TaxCategoryId");

            migrationBuilder.AddForeignKey(
                name: "FK_Plans_TaxCategories_TaxCategoryId",
                schema: "subscription",
                table: "Plans",
                column: "TaxCategoryId",
                principalSchema: "subscription",
                principalTable: "TaxCategories",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Drop foreign keys
            migrationBuilder.DropForeignKey(
                name: "FK_Plans_TaxCategories_TaxCategoryId",
                schema: "subscription",
                table: "Plans");

            // Drop indexes
            migrationBuilder.DropIndex(
                name: "IX_Plans_TaxCategoryId",
                schema: "subscription",
                table: "Plans");

            // Drop columns
            migrationBuilder.DropColumn(
                name: "TaxCategoryId",
                schema: "subscription",
                table: "Plans");

            // Drop tables
            migrationBuilder.DropTable(
                name: "TaxCategoryConfigurations",
                schema: "subscription");

            migrationBuilder.DropTable(
                name: "PlanTaxConfigurations",
                schema: "subscription");

            migrationBuilder.DropTable(
                name: "TaxExemptions",
                schema: "subscription");

            migrationBuilder.DropTable(
                name: "GlobalTaxConfigurations",
                schema: "subscription");

            migrationBuilder.DropTable(
                name: "TaxCategories",
                schema: "subscription");
        }
    }
}
