using AnalyticsBIService.Domain.Enums;
using Shared.Domain.Common;

namespace AnalyticsBIService.Domain.Events;

/// <summary>
/// Domain event fired when an analytics event is created
/// </summary>
public record AnalyticsEventCreatedEvent(
    Guid AnalyticsEventId,
    string EventName,
    AnalyticsEventType EventType,
    DataSourceType DataSource,
    Guid? UserId,
    DateTime Timestamp) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when a metric is calculated
/// </summary>
public record MetricCalculatedEvent(
    Guid MetricId,
    string MetricName,
    decimal Value,
    MetricType MetricType,
    MetricCategory Category) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when a metric value is updated
/// </summary>
public record MetricUpdatedEvent(
    Guid MetricId,
    string MetricName,
    decimal OldValue,
    decimal NewValue,
    MetricType MetricType,
    MetricCategory Category) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when a metric target is set
/// </summary>
public record MetricTargetSetEvent(
    Guid MetricId,
    string MetricName,
    decimal TargetValue,
    MetricType MetricType,
    MetricCategory Category) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when a dashboard is created
/// </summary>
public record DashboardCreatedEvent(
    Guid DashboardId,
    string Name,
    DashboardType Type,
    Guid? UserId,
    UserType? UserType) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when a dashboard is accessed
/// </summary>
public record DashboardAccessedEvent(
    Guid DashboardId,
    Guid UserId,
    UserType UserType,
    DateTime AccessedAt) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when a report is generated
/// </summary>
public record ReportGeneratedEvent(
    Guid ReportId,
    string ReportName,
    ReportType ReportType,
    Guid GeneratedBy,
    UserType UserType,
    DateTime GeneratedAt) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when a report is exported
/// </summary>
public record ReportExportedEvent(
    Guid ReportId,
    ExportFormat Format,
    Guid ExportedBy,
    DateTime ExportedAt) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when an alert is triggered
/// </summary>
public record AlertTriggeredEvent(
    Guid AlertId,
    string AlertName,
    AlertSeverity Severity,
    string Message,
    Guid? MetricId,
    DateTime TriggeredAt) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when an alert is resolved
/// </summary>
public record AlertResolvedEvent(
    Guid AlertId,
    Guid? ResolvedBy,
    string? Resolution,
    DateTime ResolvedAt) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when a KPI threshold is breached
/// </summary>
public record KPIThresholdBreachedEvent(
    Guid MetricId,
    string MetricName,
    decimal CurrentValue,
    decimal ThresholdValue,
    AlertSeverity Severity,
    Guid? UserId,
    UserType? UserType) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when analytics data is aggregated
/// </summary>
public record DataAggregatedEvent(
    string AggregationName,
    TimePeriod Period,
    DateTime PeriodStart,
    DateTime PeriodEnd,
    int RecordsProcessed,
    DataSourceType DataSource) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when real-time analytics are processed
/// </summary>
public record RealTimeAnalyticsProcessedEvent(
    string ProcessorName,
    int EventsProcessed,
    int MetricsUpdated,
    DateTime ProcessedAt) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when a custom report is created
/// </summary>
public record CustomReportCreatedEvent(
    Guid ReportId,
    string ReportName,
    Guid CreatedBy,
    UserType UserType,
    Dictionary<string, object> Configuration) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when analytics configuration is updated
/// </summary>
public record AnalyticsConfigurationUpdatedEvent(
    string ConfigurationKey,
    object OldValue,
    object NewValue,
    Guid UpdatedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// Dashboard Widget Events
/// <summary>
/// Domain event fired when a dashboard widget is created
/// </summary>
public record DashboardWidgetCreatedEvent(
    Guid WidgetId,
    string Title,
    WidgetType WidgetType,
    int Position) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when a dashboard widget is updated
/// </summary>
public record DashboardWidgetUpdatedEvent(
    Guid WidgetId,
    string Title,
    WidgetType WidgetType,
    int Position) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when a dashboard widget position is updated
/// </summary>
public record DashboardWidgetPositionUpdatedEvent(
    Guid WidgetId,
    int Position,
    int Width,
    int Height) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when a dashboard widget configuration is updated
/// </summary>
public record DashboardWidgetConfigurationUpdatedEvent(
    Guid WidgetId,
    Dictionary<string, object> Configuration) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when a dashboard widget visibility is changed
/// </summary>
public record DashboardWidgetVisibilityChangedEvent(
    Guid WidgetId,
    bool IsVisible) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// Report Section Events
/// <summary>
/// Domain event fired when a report section is created
/// </summary>
public record ReportSectionCreatedEvent(
    Guid SectionId,
    string Title,
    ReportSectionType SectionType,
    int Order) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when a report section is updated
/// </summary>
public record ReportSectionUpdatedEvent(
    Guid SectionId,
    string Title,
    ReportSectionType SectionType,
    int Order) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when a report section order is updated
/// </summary>
public record ReportSectionOrderUpdatedEvent(
    Guid SectionId,
    int Order) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when a report section data is updated
/// </summary>
public record ReportSectionDataUpdatedEvent(
    Guid SectionId,
    Dictionary<string, object> Data) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// Report Export Events
/// <summary>
/// Domain event fired when a report export is created
/// </summary>
public record ReportExportCreatedEvent(
    Guid ExportId,
    Guid ReportId,
    ExportFormat Format,
    string FileName,
    long FileSizeBytes) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when a report export status is updated
/// </summary>
public record ReportExportStatusUpdatedEvent(
    Guid ExportId,
    Guid ReportId,
    ExportStatus Status) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when a report export is downloaded
/// </summary>
public record ReportExportDownloadedEvent(
    Guid ExportId,
    Guid ReportId,
    int DownloadCount) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// Alert Rule Events
/// <summary>
/// Domain event fired when an alert rule is created
/// </summary>
public record AlertRuleCreatedEvent(
    Guid AlertRuleId,
    string Name,
    string MetricName,
    AlertSeverity Severity,
    decimal ThresholdValue) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when an alert rule is updated
/// </summary>
public record AlertRuleUpdatedEvent(
    Guid AlertRuleId,
    string Name,
    string MetricName,
    AlertSeverity Severity,
    decimal ThresholdValue) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when an alert rule threshold is updated
/// </summary>
public record AlertRuleThresholdUpdatedEvent(
    Guid AlertRuleId,
    string Name,
    string MetricName,
    decimal ThresholdValue) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when an alert rule is enabled
/// </summary>
public record AlertRuleEnabledEvent(
    Guid AlertRuleId,
    string Name,
    string MetricName) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when an alert rule is disabled
/// </summary>
public record AlertRuleDisabledEvent(
    Guid AlertRuleId,
    string Name,
    string MetricName) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when an alert rule is triggered
/// </summary>
public record AlertRuleTriggeredEvent(
    Guid AlertRuleId,
    string Name,
    string MetricName,
    AlertSeverity Severity,
    int TriggerCount) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}
