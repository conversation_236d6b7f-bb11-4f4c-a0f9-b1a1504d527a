using AnalyticsBIService.Domain.Enums;
using AnalyticsBIService.Domain.Events;
using AnalyticsBIService.Domain.ValueObjects;
using Shared.Domain.Common;

namespace AnalyticsBIService.Domain.Entities;

/// <summary>
/// Alert entity for monitoring and notification of critical metrics
/// </summary>
public class Alert : BaseEntity
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public AlertSeverity Severity { get; private set; }
    public string Message { get; private set; }
    public Guid? MetricId { get; private set; }
    public string? MetricName { get; private set; }
    public decimal? TriggerValue { get; private set; }
    public decimal? ThresholdValue { get; private set; }
    public DateTime TriggeredAt { get; private set; }
    public DateTime? ResolvedAt { get; private set; }
    public Guid? ResolvedBy { get; private set; }
    public string? Resolution { get; private set; }
    public bool IsActive { get; private set; }
    public bool IsAcknowledged { get; private set; }
    public Guid? AcknowledgedBy { get; private set; }
    public DateTime? AcknowledgedAt { get; private set; }
    public Dictionary<string, object> Context { get; private set; }
    public List<Guid> NotifiedUsers { get; private set; }

    // Additional properties referenced in the codebase
    public Guid? UserId { get; private set; }
    public AlertType AlertType { get; private set; }
    public Guid? EntityId { get; private set; }
    public string? EntityType { get; private set; }
    public string Title { get; private set; }
    public AlertStatus Status { get; private set; }
    public Guid? MonitorId { get; private set; }
    public Guid? ThresholdRuleId { get; private set; }
    public string? AcknowledgmentNotes { get; private set; }
    public Dictionary<string, object> Properties { get; private set; }
    public string? KPIName { get; private set; }
    public decimal? CurrentValue { get; private set; }
    public string? RuleName { get; private set; }

    private Alert()
    {
        Name = string.Empty;
        Description = string.Empty;
        Message = string.Empty;
        Title = string.Empty;
        EntityType = string.Empty;
        Context = new Dictionary<string, object>();
        NotifiedUsers = new List<Guid>();
        Properties = new Dictionary<string, object>();
    }

    public Alert(
        string name,
        string description,
        AlertSeverity severity,
        string message,
        Guid? metricId = null,
        string? metricName = null,
        decimal? triggerValue = null,
        decimal? thresholdValue = null,
        Dictionary<string, object>? context = null,
        Guid? userId = null,
        AlertType alertType = AlertType.System,
        Guid? entityId = null,
        string? entityType = null)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        Severity = severity;
        Message = message ?? throw new ArgumentNullException(nameof(message));
        MetricId = metricId;
        MetricName = metricName;
        TriggerValue = triggerValue;
        ThresholdValue = thresholdValue;
        TriggeredAt = DateTime.UtcNow;
        IsActive = true;
        IsAcknowledged = false;
        Context = context ?? new Dictionary<string, object>();
        NotifiedUsers = new List<Guid>();
        Properties = new Dictionary<string, object>();

        // Initialize new properties
        UserId = userId;
        AlertType = alertType;
        EntityId = entityId;
        EntityType = entityType;
        Title = name; // Use name as title
        Status = AlertStatus.Active;

        // Set initial UpdatedAt timestamp
        SetUpdatedAt();

        // Add domain event
        AddDomainEvent(new AlertTriggeredEvent(Id, Name, Severity, Message, MetricId, TriggeredAt));
    }

    public static Alert CreateKPIAlert(
        string name,
        Guid metricId,
        string metricName,
        decimal currentValue,
        decimal thresholdValue,
        AlertSeverity severity)
    {
        var message = $"KPI '{metricName}' has {(severity == AlertSeverity.Critical ? "critically" : "")} breached threshold. Current: {currentValue}, Threshold: {thresholdValue}";

        return new Alert(
            name,
            $"KPI threshold breach for {metricName}",
            severity,
            message,
            metricId,
            metricName,
            currentValue,
            thresholdValue);
    }

    public static Alert CreateSystemAlert(
        string name,
        string description,
        AlertSeverity severity,
        string message,
        Dictionary<string, object>? context = null)
    {
        return new Alert(name, description, severity, message, context: context);
    }

    public static Alert CreateBusinessAlert(
        string name,
        string description,
        AlertSeverity severity,
        string message,
        Dictionary<string, object>? context = null)
    {
        return new Alert(name, description, severity, message, context: context);
    }

    public void Acknowledge(Guid acknowledgedBy)
    {
        if (IsAcknowledged)
            throw new InvalidOperationException("Alert is already acknowledged");

        IsAcknowledged = true;
        AcknowledgedBy = acknowledgedBy;
        AcknowledgedAt = DateTime.UtcNow;
        SetUpdatedAt();
    }

    public void Resolve(Guid? resolvedBy = null, string? resolution = null)
    {
        if (!IsActive)
            throw new InvalidOperationException("Alert is already resolved");

        if (resolution == null)
            throw new ArgumentNullException(nameof(resolution), "Resolution is required when resolving an alert");

        IsActive = false;
        Status = AlertStatus.Resolved;
        ResolvedAt = DateTime.UtcNow;
        ResolvedBy = resolvedBy;
        Resolution = resolution;
        SetUpdatedAt();

        // Add domain event
        AddDomainEvent(new AlertResolvedEvent(Id, ResolvedBy, Resolution, ResolvedAt.Value));
    }

    public void AddNotifiedUser(Guid userId)
    {
        if (!NotifiedUsers.Contains(userId))
        {
            NotifiedUsers.Add(userId);
            SetUpdatedAt();
        }
    }

    public void UpdateSeverity(AlertSeverity newSeverity)
    {
        Severity = newSeverity;
        SetUpdatedAt();
    }

    public void AddContext(string key, object value)
    {
        Context[key] = value;
        SetUpdatedAt();
    }

    public TimeSpan? GetResolutionTime()
    {
        return ResolvedAt.HasValue ? ResolvedAt.Value - TriggeredAt : null;
    }

    public TimeSpan GetActiveTime()
    {
        var endTime = ResolvedAt ?? DateTime.UtcNow;
        return endTime - TriggeredAt;
    }

    public bool ShouldEscalate(TimeSpan escalationThreshold)
    {
        return IsActive && !IsAcknowledged && GetActiveTime() > escalationThreshold;
    }

    public void UpdateLastTriggered()
    {
        TriggeredAt = DateTime.UtcNow;
        SetUpdatedAt();
    }

    public void Acknowledge(Guid acknowledgedBy, string? notes = null)
    {
        if (!IsActive)
            throw new InvalidOperationException("Cannot acknowledge resolved alert");

        IsAcknowledged = true;
        AcknowledgedAt = DateTime.UtcNow;
        AcknowledgedBy = acknowledgedBy;
        AcknowledgmentNotes = notes;
        SetUpdatedAt();
    }

    public void UpdateCurrentValue(decimal currentValue)
    {
        CurrentValue = currentValue;
        SetUpdatedAt();
    }

    private readonly List<IDomainEvent> _domainEvents = new();
    public IReadOnlyCollection<IDomainEvent> DomainEvents => _domainEvents.AsReadOnly();

    protected void AddDomainEvent(IDomainEvent domainEvent)
    {
        _domainEvents.Add(domainEvent);
    }

    public void ClearDomainEvents()
    {
        _domainEvents.Clear();
    }
}

/// <summary>
/// Alert rule entity for defining alert conditions
/// </summary>
public class AlertRule : BaseEntity
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public string MetricName { get; private set; }
    public KPICategory Category { get; private set; }
    public AlertSeverity Severity { get; private set; }
    public string Condition { get; private set; }
    public decimal ThresholdValue { get; private set; }
    public TimeSpan EvaluationWindow { get; private set; }
    public bool IsEnabled { get; private set; }
    public bool IsActive => IsEnabled; // Alias for compatibility
    public Guid CreatedBy { get; private set; }
    public Guid? UserId { get; private set; } // Added for compatibility
    public string? ThresholdType { get; private set; }
    public DateTime? LastEvaluatedAt { get; private set; }
    public DateTime? LastTriggeredAt { get; private set; }

    // Navigation properties
    public ICollection<Alert> Alerts { get; private set; }
    public UserType UserType { get; private set; }
    public List<Guid> NotificationTargets { get; private set; }
    public Dictionary<string, object> Configuration { get; private set; }
    public int TriggerCount { get; private set; }

    // Additional properties required by services
    public string Operator { get; private set; }
    public List<string> Conditions { get; private set; }
    public List<string> Actions { get; private set; }

    private AlertRule()
    {
        Name = string.Empty;
        Description = string.Empty;
        MetricName = string.Empty;
        Condition = string.Empty;
        Operator = string.Empty;
        NotificationTargets = new List<Guid>();
        Configuration = new Dictionary<string, object>();
        Conditions = new List<string>();
        Actions = new List<string>();
    }

    public AlertRule(
        string name,
        string description,
        string metricName,
        KPICategory category,
        AlertSeverity severity,
        string condition,
        decimal thresholdValue,
        TimeSpan evaluationWindow,
        Guid createdBy,
        UserType userType,
        List<Guid>? notificationTargets = null,
        Dictionary<string, object>? configuration = null)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        MetricName = metricName ?? throw new ArgumentNullException(nameof(metricName));
        Category = category;
        Severity = severity;
        Condition = condition ?? throw new ArgumentNullException(nameof(condition));
        ThresholdValue = thresholdValue;
        EvaluationWindow = evaluationWindow;
        CreatedBy = createdBy;
        UserType = userType;
        IsEnabled = true;
        NotificationTargets = notificationTargets ?? new List<Guid>();
        Configuration = configuration ?? new Dictionary<string, object>();
        TriggerCount = 0;
        Operator = condition; // Use condition as operator for now
        Conditions = new List<string> { condition };
        Actions = new List<string>();
        ThresholdType = "GreaterThan"; // Default threshold type
        Alerts = new List<Alert>();
    }

    public bool EvaluateCondition(decimal currentValue)
    {
        LastEvaluatedAt = DateTime.UtcNow;
        SetUpdatedAt();

        return Condition.ToLowerInvariant() switch
        {
            "greater_than" or ">" => currentValue > ThresholdValue,
            "less_than" or "<" => currentValue < ThresholdValue,
            "greater_than_or_equal" or ">=" => currentValue >= ThresholdValue,
            "less_than_or_equal" or "<=" => currentValue <= ThresholdValue,
            "equals" or "==" => Math.Abs(currentValue - ThresholdValue) < 0.001m,
            "not_equals" or "!=" => Math.Abs(currentValue - ThresholdValue) >= 0.001m,
            _ => false
        };
    }

    public void RecordTrigger()
    {
        LastTriggeredAt = DateTime.UtcNow;
        TriggerCount++;
        SetUpdatedAt();
    }

    public void Enable()
    {
        IsEnabled = true;
        SetUpdatedAt();
    }

    public void Disable()
    {
        IsEnabled = false;
        SetUpdatedAt();
    }

    public void UpdateThreshold(decimal newThreshold)
    {
        ThresholdValue = newThreshold;
        SetUpdatedAt();
    }

    public void AddNotificationTarget(Guid userId)
    {
        if (!NotificationTargets.Contains(userId))
        {
            NotificationTargets.Add(userId);
            SetUpdatedAt();
        }
    }

    public void RemoveNotificationTarget(Guid userId)
    {
        if (NotificationTargets.Remove(userId))
        {
            SetUpdatedAt();
        }
    }

    public void UpdateLastEvaluated()
    {
        LastEvaluatedAt = DateTime.UtcNow;
        SetUpdatedAt();
    }

    public void Activate()
    {
        IsEnabled = true;
        SetUpdatedAt();
    }

    public void Deactivate()
    {
        IsEnabled = false;
        SetUpdatedAt();
    }

    public void UpdateLastEvaluated(DateTime evaluatedAt)
    {
        LastEvaluatedAt = evaluatedAt;
        SetUpdatedAt();
    }

    public void UpdateLastTriggered(DateTime triggeredAt)
    {
        LastTriggeredAt = triggeredAt;
        SetUpdatedAt();
    }
}
