{"ConnectionStrings": {"DefaultConnection": "Host=**************;Port=5432;Database=TLI_Identity;Username=postgres;Password=************"}, "JwtSettings": {"Secret": "ThisIsAVerySecureKeyThatShouldBeStoredInASecureVault", "Issuer": "TriTrackzIdentity", "Audience": "TriTrackzServices", "AccessTokenExpirationMinutes": 2, "RefreshTokenExpirationDays": 7}, "ApiGateway": {"Url": "https://localhost:7000"}, "RabbitMQ": {"Host": "localhost", "UserName": "guest", "Password": "guest"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}