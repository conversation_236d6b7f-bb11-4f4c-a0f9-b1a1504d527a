using System;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using UserManagement.API;
using UserManagement.API.Controllers;
using UserManagement.Domain.Entities;
using UserManagement.Infrastructure.Data;
using UserManagement.Tests.Helpers;
using Xunit;

namespace UserManagement.Tests.Integration
{
    public class UserProfilesControllerTests : IClassFixture<TestWebApplicationFactory<Program>>
    {
        private readonly TestWebApplicationFactory<Program> _factory;
        private readonly HttpClient _client;

        public UserProfilesControllerTests(TestWebApplicationFactory<Program> factory)
        {
            _factory = factory;
            _client = _factory.CreateClient();
        }

        [Fact]
        public async Task CreateProfile_WithValidData_ShouldReturnCreated()
        {
            // Arrange
            var request = new CreateUserProfileRequest
            {
                UserId = Guid.NewGuid(),
                UserType = UserType.Shipper,
                Email = "<EMAIL>",
                FirstName = "John",
                LastName = "Doe",
                PhoneNumber = "+**********"
            };

            var json = JsonSerializer.Serialize(request);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            // Act
            var response = await _client.PostAsync("/api/userprofiles", content);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.Created);
        }

        [Fact]
        public async Task CreateProfile_WithInvalidEmail_ShouldReturnBadRequest()
        {
            // Arrange
            var request = new CreateUserProfileRequest
            {
                UserId = Guid.NewGuid(),
                UserType = UserType.Shipper,
                Email = "invalid-email",
                FirstName = "John",
                LastName = "Doe",
                PhoneNumber = "+**********"
            };

            var json = JsonSerializer.Serialize(request);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            // Act
            var response = await _client.PostAsync("/api/userprofiles", content);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        }

        [Fact]
        public async Task GetProfile_WithExistingId_ShouldReturnOk()
        {
            // Arrange
            var profileId = await CreateTestProfile();

            // Act
            var response = await _client.GetAsync($"/api/userprofiles/{profileId}");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
        }

        [Fact]
        public async Task GetProfile_WithNonExistingId_ShouldReturnNotFound()
        {
            // Arrange
            var nonExistingId = Guid.NewGuid();

            // Act
            var response = await _client.GetAsync($"/api/userprofiles/{nonExistingId}");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.NotFound);
        }

        private async Task<Guid> CreateTestProfile()
        {
            using var scope = _factory.Services.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<UserManagementDbContext>();

            var profile = new UserProfile(Guid.NewGuid(), UserType.Shipper, "<EMAIL>");
            profile.UpdatePersonalDetails("John", "Doe", "+**********");

            context.UserProfiles.Add(profile);
            await context.SaveChangesAsync();

            return profile.Id;
        }
    }
}
