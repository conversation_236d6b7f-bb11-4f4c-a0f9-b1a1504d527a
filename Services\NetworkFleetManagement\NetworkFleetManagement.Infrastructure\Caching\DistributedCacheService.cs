using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace NetworkFleetManagement.Infrastructure.Caching;

public interface IDistributedCacheService
{
    Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default) where T : class;
    Task SetAsync<T>(string key, T value, TimeSpan? expiration = null, CancellationToken cancellationToken = default) where T : class;
    Task RemoveAsync(string key, CancellationToken cancellationToken = default);
    Task RemoveByPatternAsync(string pattern, CancellationToken cancellationToken = default);
    Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiration = null, CancellationToken cancellationToken = default) where T : class;
}

public class DistributedCacheService : IDistributedCacheService
{
    private readonly IDistributedCache _distributedCache;
    private readonly ILogger<DistributedCacheService> _logger;
    private readonly JsonSerializerOptions _jsonOptions;

    public DistributedCacheService(IDistributedCache distributedCache, ILogger<DistributedCacheService> logger)
    {
        _distributedCache = distributedCache;
        _logger = logger;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false
        };
    }

    public async Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            var cachedValue = await _distributedCache.GetStringAsync(key, cancellationToken);
            
            if (string.IsNullOrEmpty(cachedValue))
            {
                return null;
            }

            var result = JsonSerializer.Deserialize<T>(cachedValue, _jsonOptions);
            _logger.LogDebug("Cache hit for key: {Key}", key);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving from cache for key: {Key}", key);
            return null;
        }
    }

    public async Task SetAsync<T>(string key, T value, TimeSpan? expiration = null, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            var serializedValue = JsonSerializer.Serialize(value, _jsonOptions);
            
            var options = new DistributedCacheEntryOptions();
            if (expiration.HasValue)
            {
                options.SetAbsoluteExpiration(expiration.Value);
            }
            else
            {
                options.SetSlidingExpiration(TimeSpan.FromMinutes(15)); // Default sliding expiration
            }

            await _distributedCache.SetStringAsync(key, serializedValue, options, cancellationToken);
            _logger.LogDebug("Cache set for key: {Key}, expiration: {Expiration}", key, expiration);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting cache for key: {Key}", key);
        }
    }

    public async Task RemoveAsync(string key, CancellationToken cancellationToken = default)
    {
        try
        {
            await _distributedCache.RemoveAsync(key, cancellationToken);
            _logger.LogDebug("Cache removed for key: {Key}", key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing cache for key: {Key}", key);
        }
    }

    public async Task RemoveByPatternAsync(string pattern, CancellationToken cancellationToken = default)
    {
        // Note: This is a simplified implementation. In production, you might want to use Redis-specific features
        // or maintain a separate index of cache keys for pattern-based removal
        try
        {
            _logger.LogWarning("Pattern-based cache removal is not fully implemented for distributed cache. Pattern: {Pattern}", pattern);
            // This would require Redis-specific implementation or a key tracking mechanism
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing cache by pattern: {Pattern}", pattern);
        }
    }

    public async Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiration = null, CancellationToken cancellationToken = default) where T : class
    {
        var cachedValue = await GetAsync<T>(key, cancellationToken);
        
        if (cachedValue != null)
        {
            return cachedValue;
        }

        _logger.LogDebug("Cache miss for key: {Key}, executing factory", key);
        
        var value = await factory();
        await SetAsync(key, value, expiration, cancellationToken);
        
        return value;
    }
}

public static class CacheKeys
{
    private const string Prefix = "networkfleet:";
    
    public static string PreferredPartner(Guid id) => $"{Prefix}partner:{id}";
    public static string UserPreferredPartners(Guid userId) => $"{Prefix}user:{userId}:partners";
    public static string UserPreferredPartnersByType(Guid userId, string partnerType) => $"{Prefix}user:{userId}:partners:{partnerType}";
    public static string UserActivePartners(Guid userId) => $"{Prefix}user:{userId}:active";
    public static string UserDashboard(Guid userId) => $"{Prefix}user:{userId}:dashboard";
    public static string AutoAssignEligible(Guid userId, string partnerType, string? route = null, string? loadType = null) 
        => $"{Prefix}user:{userId}:autoassign:{partnerType}:{route ?? "any"}:{loadType ?? "any"}";
    public static string PartnerAnalytics(Guid partnerId, DateTime fromDate, DateTime toDate) 
        => $"{Prefix}analytics:{partnerId}:{fromDate:yyyyMMdd}:{toDate:yyyyMMdd}";
    public static string UserPattern(Guid userId) => $"{Prefix}user:{userId}:*";
}

public class CacheConfiguration
{
    public static readonly TimeSpan ShortTerm = TimeSpan.FromMinutes(5);
    public static readonly TimeSpan MediumTerm = TimeSpan.FromMinutes(15);
    public static readonly TimeSpan LongTerm = TimeSpan.FromHours(1);
    public static readonly TimeSpan Dashboard = TimeSpan.FromMinutes(10);
    public static readonly TimeSpan Analytics = TimeSpan.FromMinutes(30);
    public static readonly TimeSpan AutoAssign = TimeSpan.FromMinutes(5); // Critical path - shorter cache
}
