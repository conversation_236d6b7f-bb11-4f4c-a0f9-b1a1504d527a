using System;
using System.Linq;
using System.Threading.Tasks;
using Identity.Domain.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Identity.API.Controllers
{
    [Authorize]
    public class PermissionsController : BaseController
    {
        /// <summary>
        /// Get all permissions grouped by module
        /// </summary>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetAllPermissions(
            [FromQuery] string module = null,
            [FromQuery] string category = null,
            [FromQuery] string type = null)
        {
            var permissions = PermissionRegistry.GetAllPermissions();

            if (!string.IsNullOrEmpty(module))
            {
                permissions = permissions.Where(p => p.Module.Equals(module, StringComparison.OrdinalIgnoreCase));
            }

            if (!string.IsNullOrEmpty(category))
            {
                permissions = permissions.Where(p => p.Category.Equals(category, StringComparison.OrdinalIgnoreCase));
            }

            if (!string.IsNullOrEmpty(type) && Enum.TryParse<Identity.Domain.Entities.PermissionType>(type, true, out var permissionType))
            {
                permissions = permissions.Where(p => p.Type == permissionType);
            }

            var result = permissions.Select(p => new
            {
                p.Id,
                p.Name,
                p.Description,
                p.Module,
                p.Category,
                Type = p.Type.ToString(),
                p.Resource,
                p.Action,
                p.IsSystemPermission,
                p.Priority
            }).ToList();

            return Ok(result);
        }

        /// <summary>
        /// Get permissions grouped by module
        /// </summary>
        [HttpGet("by-module")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetPermissionsByModule()
        {
            var groupedPermissions = PermissionRegistry.GetPermissionsByModuleGrouped();
            
            var result = groupedPermissions.ToDictionary(
                kvp => kvp.Key,
                kvp => kvp.Value.Select(p => new
                {
                    p.Id,
                    p.Name,
                    p.Description,
                    p.Category,
                    Type = p.Type.ToString(),
                    p.Resource,
                    p.Action,
                    p.Priority
                }).ToList()
            );

            return Ok(result);
        }

        /// <summary>
        /// Get all modules
        /// </summary>
        [HttpGet("modules")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetAllModules()
        {
            var modules = PermissionRegistry.GetAllModules().ToList();
            return Ok(modules);
        }

        /// <summary>
        /// Get all categories
        /// </summary>
        [HttpGet("categories")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetAllCategories()
        {
            var categories = PermissionRegistry.GetAllCategories().ToList();
            return Ok(categories);
        }

        /// <summary>
        /// Get permission by name
        /// </summary>
        [HttpGet("{name}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetPermission(string name)
        {
            var permission = PermissionRegistry.GetPermission(name);
            
            if (permission == null)
            {
                return NotFound($"Permission '{name}' not found");
            }

            var result = new
            {
                permission.Id,
                permission.Name,
                permission.Description,
                permission.Module,
                permission.Category,
                Type = permission.Type.ToString(),
                permission.Resource,
                permission.Action,
                permission.IsSystemPermission,
                permission.Priority
            };

            return Ok(result);
        }

        /// <summary>
        /// Check if user has specific permission
        /// </summary>
        [HttpGet("check/{permission}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> CheckPermission(string permission)
        {
            var userId = GetCurrentUserId();
            
            // This would typically query the database for user permissions
            // For now, return a placeholder response
            var hasPermission = User.IsInRole("Admin") || User.IsInRole("SuperAdmin");
            
            return Ok(new { 
                userId, 
                permission, 
                hasPermission,
                message = hasPermission ? "Permission granted" : "Permission denied"
            });
        }

        /// <summary>
        /// Get user's permissions
        /// </summary>
        [HttpGet("user/{userId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetUserPermissions(Guid userId)
        {
            // This would be implemented with a GetUserPermissionsQuery
            return Ok(new { message = "GetUserPermissions endpoint - to be implemented" });
        }

        /// <summary>
        /// Get role's permissions
        /// </summary>
        [HttpGet("role/{roleId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetRolePermissions(Guid roleId)
        {
            // This would be implemented with a GetRolePermissionsQuery
            return Ok(new { message = "GetRolePermissions endpoint - to be implemented" });
        }

        /// <summary>
        /// Get permission matrix for UI (modules vs permissions)
        /// </summary>
        [HttpGet("matrix")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetPermissionMatrix()
        {
            var modules = PermissionRegistry.GetAllModules().ToList();
            var matrix = new System.Collections.Generic.Dictionary<string, object>();

            foreach (var module in modules)
            {
                var modulePermissions = PermissionRegistry.GetPermissionsByModule(module);
                matrix[module] = modulePermissions.Select(p => new
                {
                    p.Id,
                    p.Name,
                    p.Description,
                    Type = p.Type.ToString(),
                    p.Resource,
                    p.Action,
                    p.Priority
                }).ToList();
            }

            return Ok(new
            {
                modules,
                matrix,
                totalPermissions = PermissionRegistry.GetAllPermissions().Count()
            });
        }
    }
}
