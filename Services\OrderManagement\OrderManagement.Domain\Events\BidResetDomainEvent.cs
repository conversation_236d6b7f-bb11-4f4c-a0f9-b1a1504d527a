using Shared.Domain.Common;

namespace OrderManagement.Domain.Events;

public class BidResetDomainEvent : DomainEvent
{
    public Guid BidId { get; }
    public Guid RfqId { get; }
    public Guid BrokerId { get; }
    public string ResetReason { get; }

    public BidResetDomainEvent(Guid bidId, Guid rfqId, Guid brokerId, string resetReason)
    {
        BidId = bidId;
        RfqId = rfqId;
        BrokerId = brokerId;
        ResetReason = resetReason;
    }
}
