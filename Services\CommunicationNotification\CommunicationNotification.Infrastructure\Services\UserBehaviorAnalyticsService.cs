using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Domain.Enums;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace CommunicationNotification.Infrastructure.Services;

/// <summary>
/// Service for analyzing user behavior patterns to optimize message delivery
/// </summary>
public class UserBehaviorAnalyticsService : IUserBehaviorAnalyticsService
{
    private readonly IDistributedCache _cache;
    private readonly ILogger<UserBehaviorAnalyticsService> _logger;
    private readonly TimeSpan _cacheExpiration = TimeSpan.FromHours(1);

    public UserBehaviorAnalyticsService(
        IDistributedCache cache,
        ILogger<UserBehaviorAnalyticsService> logger)
    {
        _cache = cache;
        _logger = logger;
    }

    public async Task<UserEngagementPattern> GetUserEngagementPatternAsync(
        string userId,
        TimeSpan lookbackPeriod,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting engagement pattern for user {UserId} with lookback {LookbackPeriod}", 
                userId, lookbackPeriod);

            var cacheKey = $"user_engagement_{userId}_{lookbackPeriod.TotalDays}";
            var cachedPattern = await GetFromCacheAsync<UserEngagementPattern>(cacheKey, cancellationToken);
            if (cachedPattern != null)
            {
                return cachedPattern;
            }

            // Simulate user engagement pattern analysis
            // In a real implementation, this would query user interaction data
            var pattern = await AnalyzeUserEngagementAsync(userId, lookbackPeriod, cancellationToken);
            
            await SetCacheAsync(cacheKey, pattern, cancellationToken);

            _logger.LogDebug("Generated engagement pattern for user {UserId}", userId);
            return pattern;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting engagement pattern for user {UserId}", userId);
            return CreateDefaultEngagementPattern(userId);
        }
    }

    public async Task<DateTime?> GetOptimalSendTimeAsync(
        string userId,
        DateTime baseTime,
        string timeZone,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting optimal send time for user {UserId} at base time {BaseTime}", 
                userId, baseTime);

            var pattern = await GetUserEngagementPatternAsync(userId, TimeSpan.FromDays(30), cancellationToken);
            
            // Convert base time to user's timezone
            var userLocalTime = ConvertToTimeZone(baseTime, timeZone);
            
            // Find the closest active hour
            var optimalTime = FindOptimalTimeSlot(userLocalTime, pattern.ActiveHours, timeZone);
            
            _logger.LogDebug("Optimal send time for user {UserId}: {OptimalTime}", userId, optimalTime);
            return optimalTime;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting optimal send time for user {UserId}", userId);
            return baseTime; // Fallback to base time
        }
    }

    public async Task<NotificationChannel?> GetPreferredChannelAsync(
        string userId,
        MessageType messageType,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting preferred channel for user {UserId} and message type {MessageType}", 
                userId, messageType);

            var pattern = await GetUserEngagementPatternAsync(userId, TimeSpan.FromDays(30), cancellationToken);
            
            // Find the channel with highest preference for this message type
            var preferredChannel = pattern.ChannelPreferences
                .OrderByDescending(cp => cp.Value)
                .FirstOrDefault().Key;

            // Apply message type specific logic
            var optimizedChannel = OptimizeChannelForMessageType(preferredChannel, messageType);
            
            _logger.LogDebug("Preferred channel for user {UserId}: {Channel}", userId, optimizedChannel);
            return optimizedChannel;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting preferred channel for user {UserId}", userId);
            return null; // Let the system decide
        }
    }

    public async Task UpdateUserEngagementAsync(
        string userId,
        NotificationChannel channel,
        DateTime sentAt,
        DateTime? openedAt,
        DateTime? clickedAt,
        bool wasDelivered,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Updating engagement data for user {UserId} on channel {Channel}", userId, channel);

            // In a real implementation, this would store the engagement data in a database
            // For now, we'll simulate updating the cached pattern
            var pattern = await GetUserEngagementPatternAsync(userId, TimeSpan.FromDays(30), cancellationToken);
            
            // Update engagement metrics
            if (openedAt.HasValue)
            {
                var responseTime = (openedAt.Value - sentAt).TotalMinutes;
                pattern.AverageResponseTime = (pattern.AverageResponseTime + (decimal)responseTime) / 2;
            }

            // Update channel preferences based on engagement
            if (pattern.ChannelPreferences.ContainsKey(channel))
            {
                var currentPreference = pattern.ChannelPreferences[channel];
                var engagementBoost = openedAt.HasValue ? 0.1m : (clickedAt.HasValue ? 0.2m : -0.05m);
                pattern.ChannelPreferences[channel] = Math.Max(0, Math.Min(1, currentPreference + engagementBoost));
            }

            // Update engagement score
            var engagementPoints = wasDelivered ? 1 : 0;
            engagementPoints += openedAt.HasValue ? 2 : 0;
            engagementPoints += clickedAt.HasValue ? 3 : 0;
            pattern.EngagementScore = (pattern.EngagementScore + engagementPoints) / 2;

            pattern.LastUpdated = DateTime.UtcNow;

            // Update cache
            var cacheKey = $"user_engagement_{userId}_30";
            await SetCacheAsync(cacheKey, pattern, cancellationToken);

            _logger.LogDebug("Updated engagement data for user {UserId}", userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating engagement data for user {UserId}", userId);
        }
    }

    public async Task<List<TimeSpan>> GetUserActiveHoursAsync(
        string userId,
        string timeZone,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var pattern = await GetUserEngagementPatternAsync(userId, TimeSpan.FromDays(30), cancellationToken);
            return pattern.ActiveHours;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active hours for user {UserId}", userId);
            return GetDefaultActiveHours();
        }
    }

    public async Task<decimal> PredictResponseProbabilityAsync(
        string userId,
        MessageType messageType,
        NotificationChannel channel,
        DateTime proposedSendTime,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Predicting response probability for user {UserId}", userId);

            var pattern = await GetUserEngagementPatternAsync(userId, TimeSpan.FromDays(30), cancellationToken);
            
            var baseProbability = pattern.EngagementScore / 100m; // Convert to 0-1 scale
            
            // Adjust based on channel preference
            if (pattern.ChannelPreferences.TryGetValue(channel, out var channelPreference))
            {
                baseProbability *= channelPreference;
            }
            
            // Adjust based on message type engagement
            if (pattern.MessageTypeEngagement.TryGetValue(messageType, out var messageTypeEngagement))
            {
                baseProbability *= messageTypeEngagement / 100m;
            }
            
            // Adjust based on time of day
            var timeOfDay = proposedSendTime.TimeOfDay;
            var isActiveHour = pattern.ActiveHours.Any(ah => Math.Abs((ah - timeOfDay).TotalMinutes) < 60);
            if (isActiveHour)
            {
                baseProbability *= 1.2m; // 20% boost for active hours
            }
            else
            {
                baseProbability *= 0.7m; // 30% reduction for inactive hours
            }
            
            // Adjust based on day of week
            var dayOfWeek = proposedSendTime.DayOfWeek;
            var isActiveDay = pattern.ActiveDays.Contains(dayOfWeek);
            if (isActiveDay)
            {
                baseProbability *= 1.1m; // 10% boost for active days
            }
            else
            {
                baseProbability *= 0.8m; // 20% reduction for inactive days
            }
            
            // Ensure probability is between 0 and 1
            var probability = Math.Max(0, Math.Min(1, baseProbability));
            
            _logger.LogDebug("Predicted response probability for user {UserId}: {Probability}", userId, probability);
            return probability;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error predicting response probability for user {UserId}", userId);
            return 0.5m; // Default 50% probability
        }
    }

    // Private helper methods
    private async Task<UserEngagementPattern> AnalyzeUserEngagementAsync(
        string userId,
        TimeSpan lookbackPeriod,
        CancellationToken cancellationToken)
    {
        // Simulate user engagement analysis
        // In a real implementation, this would analyze historical interaction data
        var random = new Random(userId.GetHashCode()); // Consistent randomization per user
        
        var activeHours = GenerateActiveHours(random);
        var activeDays = GenerateActiveDays(random);
        var channelPreferences = GenerateChannelPreferences(random);
        var messageTypeEngagement = GenerateMessageTypeEngagement(random);
        
        return new UserEngagementPattern
        {
            UserId = userId,
            ActiveHours = activeHours,
            ActiveDays = activeDays,
            ChannelPreferences = channelPreferences,
            MessageTypeEngagement = messageTypeEngagement,
            AverageResponseTime = (decimal)(random.NextDouble() * 120 + 30), // 30-150 minutes
            EngagementScore = (decimal)(random.NextDouble() * 40 + 60), // 60-100%
            LastUpdated = DateTime.UtcNow
        };
    }

    private List<TimeSpan> GenerateActiveHours(Random random)
    {
        var activeHours = new List<TimeSpan>();
        
        // Morning hours (7-10 AM)
        if (random.NextDouble() > 0.3)
        {
            activeHours.Add(TimeSpan.FromHours(7 + random.NextDouble() * 3));
        }
        
        // Lunch hours (12-2 PM)
        if (random.NextDouble() > 0.4)
        {
            activeHours.Add(TimeSpan.FromHours(12 + random.NextDouble() * 2));
        }
        
        // Evening hours (6-9 PM)
        if (random.NextDouble() > 0.2)
        {
            activeHours.Add(TimeSpan.FromHours(18 + random.NextDouble() * 3));
        }
        
        return activeHours;
    }

    private List<DayOfWeek> GenerateActiveDays(Random random)
    {
        var activeDays = new List<DayOfWeek>();
        
        // Weekdays are more likely to be active
        foreach (var day in new[] { DayOfWeek.Monday, DayOfWeek.Tuesday, DayOfWeek.Wednesday, DayOfWeek.Thursday, DayOfWeek.Friday })
        {
            if (random.NextDouble() > 0.2) // 80% chance for weekdays
            {
                activeDays.Add(day);
            }
        }
        
        // Weekends are less likely
        foreach (var day in new[] { DayOfWeek.Saturday, DayOfWeek.Sunday })
        {
            if (random.NextDouble() > 0.6) // 40% chance for weekends
            {
                activeDays.Add(day);
            }
        }
        
        return activeDays;
    }

    private Dictionary<NotificationChannel, decimal> GenerateChannelPreferences(Random random)
    {
        var preferences = new Dictionary<NotificationChannel, decimal>();
        
        // Generate preferences for each channel
        preferences[NotificationChannel.WhatsApp] = (decimal)(random.NextDouble() * 0.4 + 0.6); // 60-100%
        preferences[NotificationChannel.SMS] = (decimal)(random.NextDouble() * 0.3 + 0.4); // 40-70%
        preferences[NotificationChannel.Email] = (decimal)(random.NextDouble() * 0.5 + 0.3); // 30-80%
        preferences[NotificationChannel.PushNotification] = (decimal)(random.NextDouble() * 0.6 + 0.4); // 40-100%
        preferences[NotificationChannel.RealTimeChat] = (decimal)(random.NextDouble() * 0.3 + 0.2); // 20-50%
        
        return preferences;
    }

    private Dictionary<MessageType, decimal> GenerateMessageTypeEngagement(Random random)
    {
        var engagement = new Dictionary<MessageType, decimal>();
        
        // Generate engagement rates for different message types
        engagement[MessageType.TripConfirmation] = (decimal)(random.NextDouble() * 20 + 80); // 80-100%
        engagement[MessageType.TripUpdate] = (decimal)(random.NextDouble() * 25 + 70); // 70-95%
        engagement[MessageType.PaymentReminder] = (decimal)(random.NextDouble() * 30 + 60); // 60-90%
        engagement[MessageType.Promotional] = (decimal)(random.NextDouble() * 40 + 30); // 30-70%
        engagement[MessageType.SystemAlert] = (decimal)(random.NextDouble() * 20 + 75); // 75-95%
        
        return engagement;
    }

    private UserEngagementPattern CreateDefaultEngagementPattern(string userId)
    {
        return new UserEngagementPattern
        {
            UserId = userId,
            ActiveHours = GetDefaultActiveHours(),
            ActiveDays = new List<DayOfWeek> { DayOfWeek.Monday, DayOfWeek.Tuesday, DayOfWeek.Wednesday, DayOfWeek.Thursday, DayOfWeek.Friday },
            ChannelPreferences = new Dictionary<NotificationChannel, decimal>
            {
                [NotificationChannel.WhatsApp] = 0.8m,
                [NotificationChannel.SMS] = 0.6m,
                [NotificationChannel.Email] = 0.5m,
                [NotificationChannel.PushNotification] = 0.7m,
                [NotificationChannel.RealTimeChat] = 0.3m
            },
            MessageTypeEngagement = new Dictionary<MessageType, decimal>
            {
                [MessageType.TripConfirmation] = 90m,
                [MessageType.TripUpdate] = 80m,
                [MessageType.PaymentReminder] = 70m,
                [MessageType.Promotional] = 50m,
                [MessageType.SystemAlert] = 85m
            },
            AverageResponseTime = 60m,
            EngagementScore = 75m,
            LastUpdated = DateTime.UtcNow
        };
    }

    private List<TimeSpan> GetDefaultActiveHours()
    {
        return new List<TimeSpan>
        {
            TimeSpan.FromHours(9),  // 9 AM
            TimeSpan.FromHours(13), // 1 PM
            TimeSpan.FromHours(19)  // 7 PM
        };
    }

    private DateTime FindOptimalTimeSlot(DateTime userLocalTime, List<TimeSpan> activeHours, string timeZone)
    {
        if (!activeHours.Any())
        {
            return userLocalTime;
        }

        var timeOfDay = userLocalTime.TimeOfDay;
        
        // Find the closest active hour
        var closestActiveHour = activeHours
            .OrderBy(ah => Math.Abs((ah - timeOfDay).TotalMinutes))
            .First();

        // If the closest active hour is within 2 hours, use it
        if (Math.Abs((closestActiveHour - timeOfDay).TotalMinutes) <= 120)
        {
            var optimalLocalTime = userLocalTime.Date.Add(closestActiveHour);
            return ConvertFromTimeZone(optimalLocalTime, timeZone);
        }

        // Otherwise, use the next active hour
        var nextActiveHour = activeHours
            .Where(ah => ah > timeOfDay)
            .OrderBy(ah => ah)
            .FirstOrDefault();

        if (nextActiveHour != default)
        {
            var optimalLocalTime = userLocalTime.Date.Add(nextActiveHour);
            return ConvertFromTimeZone(optimalLocalTime, timeZone);
        }

        // If no active hour today, use the first active hour tomorrow
        var firstActiveHour = activeHours.OrderBy(ah => ah).First();
        var tomorrowOptimalTime = userLocalTime.Date.AddDays(1).Add(firstActiveHour);
        return ConvertFromTimeZone(tomorrowOptimalTime, timeZone);
    }

    private NotificationChannel OptimizeChannelForMessageType(NotificationChannel preferredChannel, MessageType messageType)
    {
        // Apply message type specific channel optimization
        return messageType switch
        {
            MessageType.TripConfirmation => NotificationChannel.WhatsApp, // High priority, immediate
            MessageType.TripUpdate => preferredChannel, // Use user preference
            MessageType.PaymentReminder => NotificationChannel.SMS, // Reliable delivery
            MessageType.Promotional => NotificationChannel.Email, // Less intrusive
            MessageType.SystemAlert => NotificationChannel.PushNotification, // Immediate attention
            _ => preferredChannel
        };
    }

    private DateTime ConvertToTimeZone(DateTime utcDateTime, string timeZone)
    {
        try
        {
            var timeZoneInfo = TimeZoneInfo.FindSystemTimeZoneById(timeZone);
            return TimeZoneInfo.ConvertTimeFromUtc(utcDateTime, timeZoneInfo);
        }
        catch
        {
            return utcDateTime;
        }
    }

    private DateTime ConvertFromTimeZone(DateTime localDateTime, string timeZone)
    {
        try
        {
            var timeZoneInfo = TimeZoneInfo.FindSystemTimeZoneById(timeZone);
            return TimeZoneInfo.ConvertTimeToUtc(localDateTime, timeZoneInfo);
        }
        catch
        {
            return localDateTime;
        }
    }

    private async Task<T?> GetFromCacheAsync<T>(string key, CancellationToken cancellationToken) where T : class
    {
        try
        {
            var cachedData = await _cache.GetStringAsync(key, cancellationToken);
            if (!string.IsNullOrEmpty(cachedData))
            {
                return JsonSerializer.Deserialize<T>(cachedData);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error retrieving data from cache for key {Key}", key);
        }
        
        return null;
    }

    private async Task SetCacheAsync<T>(string key, T data, CancellationToken cancellationToken)
    {
        try
        {
            var serializedData = JsonSerializer.Serialize(data);
            await _cache.SetStringAsync(key, serializedData, new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = _cacheExpiration
            }, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error setting cache for key {Key}", key);
        }
    }
}
