using System.Threading.Tasks;

namespace Shared.Messaging
{
    public interface IMessageBroker
    {
        Task PublishAsync<T>(string routingKey, T message) where T : class;
        Task SubscribeAsync<T>(string queueName, string routingKey, Func<T, Task> handler) where T : class;
        Task SubscribeAsync<T>(string queueName, string routingKey, Func<T, Task<bool>> handler) where T : class;
    }
}
