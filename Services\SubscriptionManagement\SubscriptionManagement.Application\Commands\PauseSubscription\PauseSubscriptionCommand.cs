using MediatR;

namespace SubscriptionManagement.Application.Commands.PauseSubscription
{
    public class PauseSubscriptionCommand : IRequest<bool>
    {
        public Guid SubscriptionId { get; set; }
        public Guid UserId { get; set; }
        public DateTime? PauseUntil { get; set; } // If null, paused indefinitely
        public string? PauseReason { get; set; }
        public bool ProcessRefund { get; set; } = false; // Whether to refund unused portion
    }
}
