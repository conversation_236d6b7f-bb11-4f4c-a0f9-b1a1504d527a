using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Application.Interfaces;

public interface IFraudDetectionService
{
    Task<FraudAssessment> AssessTransactionAsync(FraudAssessmentRequest request);
    Task<FraudAssessment> ReassessTransactionAsync(Guid assessmentId);
    Task<List<FraudAssessment>> GetHighRiskTransactionsAsync(DateTime? fromDate = null, DateTime? toDate = null);
    Task<List<FraudAssessment>> GetPendingReviewsAsync();
    Task<bool> ReviewAssessmentAsync(Guid assessmentId, Guid reviewedBy, string reviewNotes, FraudAction finalAction);
    Task<FraudDetectionStatistics> GetStatisticsAsync(DateTime? fromDate = null, DateTime? toDate = null);
    Task<List<FraudDetectionRule>> GetActiveRulesAsync();
    Task<bool> UpdateRuleAsync(Guid ruleId, int newRiskScore, bool isActive);
}

public interface IFraudDetectionRuleRepository
{
    Task<FraudDetectionRule?> GetByIdAsync(Guid id);
    Task<List<FraudDetectionRule>> GetActiveRulesAsync();
    Task<List<FraudDetectionRule>> GetByTypeAsync(FraudRuleType ruleType);
    Task AddAsync(FraudDetectionRule rule);
    Task UpdateAsync(FraudDetectionRule rule);
    Task DeleteAsync(Guid id);
}

public interface IFraudAssessmentRepository
{
    Task<FraudAssessment?> GetByIdAsync(Guid id);
    Task<FraudAssessment?> GetByTransactionIdAsync(Guid transactionId);
    Task<List<FraudAssessment>> GetByRiskLevelAsync(FraudRiskLevel riskLevel, DateTime? fromDate = null, DateTime? toDate = null);
    Task<List<FraudAssessment>> GetByStatusAsync(FraudAssessmentStatus status);
    Task<List<FraudAssessment>> GetByUserIdAsync(Guid userId, DateTime? fromDate = null, DateTime? toDate = null);
    Task AddAsync(FraudAssessment assessment);
    Task UpdateAsync(FraudAssessment assessment);
    Task DeleteAsync(Guid id);
}

public interface IFraudPatternAnalyzer
{
    Task<List<FraudPattern>> AnalyzeUserPatternsAsync(Guid userId);
    Task<List<FraudPattern>> AnalyzeTransactionPatternsAsync(DateTime fromDate, DateTime toDate);
    Task<bool> IsVelocityExceededAsync(Guid userId, Money amount, TimeSpan timeWindow);
    Task<bool> IsLocationSuspiciousAsync(string ipAddress, Guid? userId = null);
    Task<bool> IsDeviceSuspiciousAsync(string deviceFingerprint, Guid? userId = null);
}

public interface IBlacklistService
{
    Task<bool> IsUserBlacklistedAsync(Guid userId);
    Task<bool> IsEmailBlacklistedAsync(string email);
    Task<bool> IsIpAddressBlacklistedAsync(string ipAddress);
    Task<bool> IsDeviceBlacklistedAsync(string deviceFingerprint);
    Task AddToBlacklistAsync(BlacklistEntry entry);
    Task RemoveFromBlacklistAsync(Guid entryId);
}

public class FraudAssessmentRequest
{
    public Guid TransactionId { get; set; }
    public Guid? UserId { get; set; }
    public Money TransactionAmount { get; set; } = Money.Zero("INR");
    public string PaymentMethod { get; set; } = string.Empty;
    public string IpAddress { get; set; } = string.Empty;
    public string? DeviceFingerprint { get; set; }
    public string? UserAgent { get; set; }
    public string? BillingAddress { get; set; }
    public string? ShippingAddress { get; set; }
    public string? Email { get; set; }
    public string? PhoneNumber { get; set; }
    public Dictionary<string, object> AdditionalData { get; set; } = new();
}

public class FraudDetectionStatistics
{
    public int TotalAssessments { get; set; }
    public int LowRiskCount { get; set; }
    public int MediumRiskCount { get; set; }
    public int HighRiskCount { get; set; }
    public int CriticalRiskCount { get; set; }
    public int BlockedTransactions { get; set; }
    public int ReviewedTransactions { get; set; }
    public decimal FalsePositiveRate { get; set; }
    public decimal TruePositiveRate { get; set; }
    public Money TotalBlockedAmount { get; set; } = Money.Zero("INR");
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
}

public class FraudPattern
{
    public string PatternType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int Frequency { get; set; }
    public decimal RiskScore { get; set; }
    public DateTime FirstOccurrence { get; set; }
    public DateTime LastOccurrence { get; set; }
    public Dictionary<string, object> PatternData { get; set; } = new();
}

public class BlacklistEntry
{
    public Guid Id { get; set; }
    public BlacklistType Type { get; set; }
    public string Value { get; set; } = string.Empty;
    public string Reason { get; set; } = string.Empty;
    public DateTime AddedAt { get; set; }
    public Guid AddedBy { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public bool IsActive { get; set; }
}

public enum BlacklistType
{
    UserId = 1,
    Email = 2,
    IpAddress = 3,
    DeviceFingerprint = 4,
    PhoneNumber = 5,
    CreditCard = 6
}

public class FraudDetectionConfiguration
{
    public bool EnableFraudDetection { get; set; } = true;
    public bool EnableRealTimeAssessment { get; set; } = true;
    public bool EnablePatternAnalysis { get; set; } = true;
    public bool EnableBlacklist { get; set; } = true;
    public int DefaultRiskThreshold { get; set; } = 60;
    public int VelocityCheckWindowMinutes { get; set; } = 60;
    public int MaxTransactionsPerWindow { get; set; } = 5;
    public decimal MaxAmountPerWindow { get; set; } = 10000;
    public bool EnableGeolocationCheck { get; set; } = true;
    public bool EnableDeviceFingerprinting { get; set; } = true;
    public List<string> TrustedIpRanges { get; set; } = new();
    public List<string> HighRiskCountries { get; set; } = new();
    public Dictionary<string, int> PaymentMethodRiskScores { get; set; } = new();
}

public class VelocityCheckResult
{
    public bool IsExceeded { get; set; }
    public int TransactionCount { get; set; }
    public Money TotalAmount { get; set; } = Money.Zero("INR");
    public TimeSpan TimeWindow { get; set; }
    public string Details { get; set; } = string.Empty;
}

public class GeolocationCheckResult
{
    public bool IsSuspicious { get; set; }
    public string Country { get; set; } = string.Empty;
    public string City { get; set; } = string.Empty;
    public bool IsHighRiskLocation { get; set; }
    public bool IsUnusualLocation { get; set; }
    public string Details { get; set; } = string.Empty;
}
