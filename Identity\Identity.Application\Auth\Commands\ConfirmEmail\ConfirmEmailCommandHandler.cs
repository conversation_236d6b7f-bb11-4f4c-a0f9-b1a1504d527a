using System;
using System.Threading;
using System.Threading.Tasks;
using Identity.Application.Common.Interfaces;
using Identity.Domain.Repositories;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Messaging;
using Shared.Messaging.Events;

namespace Identity.Application.Auth.Commands.ConfirmEmail
{
    public class ConfirmEmailCommandHandler : IRequestHandler<ConfirmEmailCommand, Unit>
    {
        private readonly IUserRepository _userRepository;
        private readonly IEmailConfirmationService _emailConfirmationService;
        private readonly IMessageBroker _messageBroker;
        private readonly ILogger<ConfirmEmailCommandHandler> _logger;

        public ConfirmEmailCommandHandler(
            IUserRepository userRepository,
            IEmailConfirmationService emailConfirmationService,
            IMessageBroker messageBroker,
            ILogger<ConfirmEmailCommandHandler> logger)
        {
            _userRepository = userRepository;
            _emailConfirmationService = emailConfirmationService;
            _messageBroker = messageBroker;
            _logger = logger;
        }

        public async Task<Unit> Handle(ConfirmEmailCommand request, CancellationToken cancellationToken)
        {
            var user = await _userRepository.GetByIdAsync(request.UserId);
            if (user == null)
            {
                throw new ApplicationException("User not found");
            }

            // Verify the token
            var isValid = await _emailConfirmationService.ValidateEmailConfirmationTokenAsync(user.Id, request.Token);
            if (!isValid)
            {
                throw new ApplicationException("Invalid or expired token");
            }

            // Confirm the email
            user.ConfirmEmail();
            await _userRepository.UpdateAsync(user);

            // Publish email confirmed event
            await _messageBroker.PublishAsync("user.email.confirmed", new UserEmailConfirmedEvent
            {
                UserId = user.Id,
                Email = user.Email.Value,
                ConfirmedAt = DateTime.UtcNow
            });

            _logger.LogInformation("Email confirmed for user {UserId}", user.Id);

            return Unit.Value;
        }
    }
}
