using MediatR;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Application.Commands.TriggerSubscriptionAlerts
{
    public class TriggerSubscriptionAlertsCommand : IRequest<TriggerSubscriptionAlertsResponse>
    {
        public List<SubscriptionStatus>? SubscriptionStatuses { get; set; }
        public DateTime? ExpiryDateFrom { get; set; }
        public DateTime? ExpiryDateTo { get; set; }
        public List<UserType>? UserTypes { get; set; }
        public List<PlanType>? PlanTypes { get; set; }
        public List<Guid>? SubscriptionIds { get; set; }
        public NotificationType AlertType { get; set; } = NotificationType.SubscriptionReminder;
        public List<string> Channels { get; set; } = new();
        public string? CustomMessage { get; set; }
        public Guid TriggeredByUserId { get; set; }
        public string Language { get; set; } = "en";
        public bool DryRun { get; set; } = false;
        public int BatchSize { get; set; } = 100;
    }

    public class TriggerSubscriptionAlertsResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public int TotalSubscriptionsFound { get; set; }
        public int NotificationsSent { get; set; }
        public int NotificationsFailed { get; set; }
        public List<string> Channels { get; set; } = new();
        public List<string> Errors { get; set; } = new();
        public List<SubscriptionAlertResult> Results { get; set; } = new();
        public bool WasDryRun { get; set; }
        public DateTime ProcessedAt { get; set; }
        public TimeSpan ProcessingTime { get; set; }
    }

    public class SubscriptionAlertResult
    {
        public Guid SubscriptionId { get; set; }
        public Guid UserId { get; set; }
        public string PlanName { get; set; } = string.Empty;
        public SubscriptionStatus Status { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public bool Success { get; set; }
        public List<string> ChannelResults { get; set; } = new();
        public List<string> Errors { get; set; } = new();
    }
}
