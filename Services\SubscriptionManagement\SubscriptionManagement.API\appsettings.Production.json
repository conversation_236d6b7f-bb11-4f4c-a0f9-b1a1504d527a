{"Logging": {"LogLevel": {"Default": "Warning", "Microsoft.AspNetCore": "Warning", "SubscriptionManagement": "Information"}}, "ConnectionStrings": {"DefaultConnection": "${SQL_CONNECTION_STRING}", "Redis": "${REDIS_CONNECTION_STRING}"}, "RazorPay": {"KeyId": "${RAZORPAY_KEY_ID}", "KeySecret": "${RAZORPAY_KEY_SECRET}", "WebhookSecret": "${RAZORPAY_WEBHOOK_SECRET}"}, "RabbitMQ": {"HostName": "${RABBITMQ_HOST}", "Port": "${RABBITMQ_PORT:5672}", "UserName": "${RABBITMQ_USERNAME}", "Password": "${RABBITMQ_PASSWORD}", "VirtualHost": "${RABBITMQ_VHOST:/}", "ExchangeName": "subscription_events"}, "Cache": {"Redis": {"ConnectionString": "${REDIS_CONNECTION_STRING}", "InstanceName": "SubscriptionManagement_Prod", "DefaultExpiration": "01:00:00", "SlidingExpiration": "00:30:00"}, "Memory": {"SizeLimit": 268435456, "CompactionPercentage": 0.2}}, "Metrics": {"Enabled": true, "ExportInterval": "00:01:00", "Endpoints": {"Prometheus": "/metrics"}}, "HealthChecks": {"Enabled": true, "Endpoint": "/health", "DetailedEndpoint": "/health/detailed", "CheckInterval": "00:05:00"}, "PerformanceMonitoring": {"Enabled": true, "SampleRate": 0.1, "SlowQueryThreshold": "00:00:02", "EnableSqlCommandText": false}}