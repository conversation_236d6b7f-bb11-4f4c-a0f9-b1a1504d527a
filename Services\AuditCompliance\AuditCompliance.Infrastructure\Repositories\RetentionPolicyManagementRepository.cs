using AuditCompliance.Application.Interfaces;
using AuditCompliance.Domain.Entities;
using AuditCompliance.Domain.Enums;
using AuditCompliance.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace AuditCompliance.Infrastructure.Repositories
{
    /// <summary>
    /// Repository implementation for RetentionPolicyManagement entity
    /// </summary>
    public class RetentionPolicyManagementRepository : IRetentionPolicyManagementRepository
    {
        private readonly AuditComplianceDbContext _context;
        private readonly ILogger<RetentionPolicyManagementRepository> _logger;

        public RetentionPolicyManagementRepository(
            AuditComplianceDbContext context,
            ILogger<RetentionPolicyManagementRepository> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<RetentionPolicyManagement?> GetByIdAsync(Guid id)
        {
            try
            {
                return await _context.RetentionPolicyManagements
                    .FirstOrDefaultAsync(p => p.Id == id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving retention policy by ID {Id}", id);
                throw;
            }
        }

        public async Task<RetentionPolicyManagement?> GetByNameAsync(string policyName)
        {
            try
            {
                return await _context.RetentionPolicyManagements
                    .FirstOrDefaultAsync(p => p.PolicyName == policyName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving retention policy by name {PolicyName}", policyName);
                throw;
            }
        }

        public async Task<List<RetentionPolicyManagement>> GetAllAsync()
        {
            try
            {
                return await _context.RetentionPolicyManagements
                    .OrderBy(p => p.PolicyName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all retention policies");
                throw;
            }
        }

        public async Task<List<RetentionPolicyManagement>> GetActivePoliciesAsync()
        {
            try
            {
                return await _context.RetentionPolicyManagements
                    .Where(p => p.IsActive)
                    .OrderBy(p => p.Priority)
                    .ThenBy(p => p.PolicyName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving active retention policies");
                throw;
            }
        }

        public async Task<List<RetentionPolicyManagement>> GetPendingApprovalPoliciesAsync()
        {
            try
            {
                return await _context.RetentionPolicyManagements
                    .Where(p => p.ApprovalStatus == PolicyApprovalStatus.Pending)
                    .OrderBy(p => p.CreatedAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving pending approval retention policies");
                throw;
            }
        }

        public async Task<List<RetentionPolicyManagement>> GetPoliciesByStatusAsync(PolicyApprovalStatus status)
        {
            try
            {
                return await _context.RetentionPolicyManagements
                    .Where(p => p.ApprovalStatus == status)
                    .OrderBy(p => p.CreatedAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving retention policies by status {Status}", status);
                throw;
            }
        }

        public async Task<List<RetentionPolicyManagement>> GetPoliciesByPriorityAsync(PolicyPriority priority)
        {
            try
            {
                return await _context.RetentionPolicyManagements
                    .Where(p => p.Priority == priority && p.IsActive)
                    .OrderBy(p => p.PolicyName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving retention policies by priority {Priority}", priority);
                throw;
            }
        }

        public async Task<List<RetentionPolicyManagement>> GetPoliciesByCreatorAsync(Guid createdBy)
        {
            try
            {
                return await _context.RetentionPolicyManagements
                    .Where(p => p.CreatedBy == createdBy)
                    .OrderByDescending(p => p.CreatedAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving retention policies by creator {CreatedBy}", createdBy);
                throw;
            }
        }

        public async Task<List<RetentionPolicyManagement>> GetPoliciesForEntityTypeAsync(string entityType)
        {
            try
            {
                return await _context.RetentionPolicyManagements
                    .Where(p => p.IsActive && p.ApplicableEntityTypes.Contains(entityType))
                    .OrderBy(p => p.Priority)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving retention policies for entity type {EntityType}", entityType);
                throw;
            }
        }

        public async Task<List<RetentionPolicyManagement>> GetPoliciesForModuleAsync(string moduleName)
        {
            try
            {
                return await _context.RetentionPolicyManagements
                    .Where(p => p.IsActive && p.ApplicableModules.Contains(moduleName))
                    .OrderBy(p => p.Priority)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving retention policies for module {ModuleName}", moduleName);
                throw;
            }
        }

        public async Task<List<RetentionPolicyManagement>> GetPoliciesForComplianceStandardAsync(ComplianceStandard standard)
        {
            try
            {
                return await _context.RetentionPolicyManagements
                    .Where(p => p.IsActive && p.ApplicableStandards.Contains(standard))
                    .OrderBy(p => p.Priority)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving retention policies for compliance standard {Standard}", standard);
                throw;
            }
        }

        public async Task<List<RetentionPolicyManagement>> SearchPoliciesAsync(string searchTerm)
        {
            try
            {
                var term = searchTerm.ToLower();
                return await _context.RetentionPolicyManagements
                    .Where(p => p.PolicyName.ToLower().Contains(term) ||
                               p.Description.ToLower().Contains(term) ||
                               p.Tags.Any(tag => tag.ToLower().Contains(term)))
                    .OrderBy(p => p.PolicyName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching retention policies with term {SearchTerm}", searchTerm);
                throw;
            }
        }

        public async Task<List<RetentionPolicyManagement>> GetPoliciesByTagsAsync(List<string> tags)
        {
            try
            {
                return await _context.RetentionPolicyManagements
                    .Where(p => p.IsActive && tags.Any(tag => p.Tags.Contains(tag)))
                    .OrderBy(p => p.PolicyName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving retention policies by tags");
                throw;
            }
        }

        public async Task<List<RetentionPolicyManagement>> GetPoliciesReadyForExecutionAsync()
        {
            try
            {
                return await _context.RetentionPolicyManagements
                    .Where(p => p.IsActive && p.ApprovalStatus == PolicyApprovalStatus.Approved)
                    .OrderBy(p => p.Priority)
                    .ThenBy(p => p.LastExecuted ?? DateTime.MinValue)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving policies ready for execution");
                throw;
            }
        }

        public async Task<RetentionPolicyManagement> AddAsync(RetentionPolicyManagement policy)
        {
            try
            {
                _context.RetentionPolicyManagements.Add(policy);
                await _context.SaveChangesAsync();
                
                _logger.LogInformation("Retention policy added: {PolicyName}", policy.PolicyName);
                return policy;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding retention policy {PolicyName}", policy.PolicyName);
                throw;
            }
        }

        public async Task UpdateAsync(RetentionPolicyManagement policy)
        {
            try
            {
                _context.RetentionPolicyManagements.Update(policy);
                await _context.SaveChangesAsync();
                
                _logger.LogInformation("Retention policy updated: {PolicyName}", policy.PolicyName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating retention policy {PolicyName}", policy.PolicyName);
                throw;
            }
        }

        public async Task DeleteAsync(Guid id)
        {
            try
            {
                var policy = await GetByIdAsync(id);
                if (policy != null)
                {
                    _context.RetentionPolicyManagements.Remove(policy);
                    await _context.SaveChangesAsync();
                    
                    _logger.LogInformation("Retention policy deleted: {Id}", id);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting retention policy {Id}", id);
                throw;
            }
        }

        public async Task<bool> PolicyNameExistsAsync(string policyName, Guid? excludeId = null)
        {
            try
            {
                var query = _context.RetentionPolicyManagements
                    .Where(p => p.PolicyName == policyName);

                if (excludeId.HasValue)
                {
                    query = query.Where(p => p.Id != excludeId.Value);
                }

                return await query.AnyAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking policy name existence {PolicyName}", policyName);
                throw;
            }
        }

        public async Task<Dictionary<string, int>> GetPolicyStatisticsAsync()
        {
            try
            {
                var stats = new Dictionary<string, int>();
                
                stats["TotalPolicies"] = await _context.RetentionPolicyManagements.CountAsync();
                stats["ActivePolicies"] = await _context.RetentionPolicyManagements.CountAsync(p => p.IsActive);
                stats["PendingApproval"] = await _context.RetentionPolicyManagements.CountAsync(p => p.ApprovalStatus == PolicyApprovalStatus.Pending);
                stats["ApprovedPolicies"] = await _context.RetentionPolicyManagements.CountAsync(p => p.ApprovalStatus == PolicyApprovalStatus.Approved);
                stats["RejectedPolicies"] = await _context.RetentionPolicyManagements.CountAsync(p => p.ApprovalStatus == PolicyApprovalStatus.Rejected);
                stats["TotalExecutions"] = await _context.RetentionPolicyManagements.SumAsync(p => p.ExecutionCount);
                stats["TotalRecordsAffected"] = await _context.RetentionPolicyManagements.SumAsync(p => p.RecordsAffected);

                return stats;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving policy statistics");
                throw;
            }
        }

        public async Task<List<string>> GetAllTagsAsync()
        {
            try
            {
                return await _context.RetentionPolicyManagements
                    .SelectMany(p => p.Tags)
                    .Distinct()
                    .OrderBy(tag => tag)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all policy tags");
                throw;
            }
        }

        public async Task<List<RetentionPolicyManagement>> GetConflictingPoliciesAsync(RetentionPolicyManagement policy)
        {
            try
            {
                return await _context.RetentionPolicyManagements
                    .Where(p => p.Id != policy.Id &&
                               p.IsActive &&
                               p.ApprovalStatus == PolicyApprovalStatus.Approved &&
                               (p.ApplicableEntityTypes.Any(et => policy.ApplicableEntityTypes.Contains(et)) ||
                                p.ApplicableModules.Any(m => policy.ApplicableModules.Contains(m))))
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving conflicting policies");
                throw;
            }
        }
    }
}
