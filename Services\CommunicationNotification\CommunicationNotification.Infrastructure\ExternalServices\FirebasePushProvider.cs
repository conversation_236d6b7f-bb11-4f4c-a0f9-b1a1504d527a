using CommunicationNotification.Application.Interfaces;
using FirebaseAdmin;
using FirebaseAdmin.Messaging;
using Google.Apis.Auth.OAuth2;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace CommunicationNotification.Infrastructure.ExternalServices;

/// <summary>
/// Firebase Cloud Messaging push notification provider
/// </summary>
public class FirebasePushProvider : IPushNotificationProvider
{
    private readonly ILogger<FirebasePushProvider> _logger;
    private readonly FirebaseSettings _settings;
    private readonly FirebaseMessaging _messaging;

    public FirebasePushProvider(
        IConfiguration configuration,
        ILogger<FirebasePushProvider> logger)
    {
        _logger = logger;
        _settings = configuration.GetSection("Firebase").Get<FirebaseSettings>() ?? new();

        // Initialize Firebase if not already initialized
        if (FirebaseApp.DefaultInstance == null)
        {
            InitializeFirebase();
        }

        _messaging = FirebaseMessaging.DefaultInstance;
    }

    public async Task<PushNotificationResult> SendPushNotificationAsync(
        PushNotificationRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Sending push notification to device {DeviceToken} via Firebase", 
                request.DeviceToken?.Substring(0, Math.Min(10, request.DeviceToken.Length ?? 0)) + "...");

            // Validate request
            var validationResult = ValidateRequest(request);
            if (!validationResult.IsValid)
            {
                return PushNotificationResult.Failure(string.Join(", ", validationResult.Errors));
            }

            // Create Firebase message
            var message = CreateFirebaseMessage(request);

            // Send notification
            var response = await _messaging.SendAsync(message, cancellationToken);

            var result = new PushNotificationResult
            {
                IsSuccess = !string.IsNullOrEmpty(response),
                MessageId = response,
                ExternalId = response,
                Status = !string.IsNullOrEmpty(response) ? PushNotificationStatus.Sent : PushNotificationStatus.Failed,
                SentAt = DateTime.UtcNow,
                Metadata = new Dictionary<string, object>
                {
                    ["firebaseMessageId"] = response ?? "",
                    ["platform"] = request.Platform.ToString(),
                    ["hasData"] = request.Data?.Any().ToString() ?? "false",
                    ["priority"] = request.Priority.ToString()
                }
            };

            if (result.IsSuccess)
            {
                _logger.LogInformation("Push notification sent successfully. Firebase MessageId: {MessageId}", response);
            }
            else
            {
                _logger.LogError("Failed to send push notification. No message ID returned from Firebase");
            }

            return result;
        }
        catch (FirebaseMessagingException ex)
        {
            _logger.LogError(ex, "Firebase messaging error sending push notification: {ErrorCode} - {ErrorMessage}",
                ex.ErrorCode, ex.Message);

            return PushNotificationResult.Failure($"Firebase error: {ex.Message}", ex.ErrorCode?.ToString());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error sending push notification");
            return PushNotificationResult.Failure($"Unexpected error: {ex.Message}");
        }
    }

    public async Task<PushNotificationResult> SendBulkPushNotificationAsync(
        BulkPushNotificationRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Sending bulk push notification to {DeviceCount} devices via Firebase", 
                request.DeviceTokens.Count);

            // Validate request
            var validationResult = ValidateBulkRequest(request);
            if (!validationResult.IsValid)
            {
                return PushNotificationResult.Failure(string.Join(", ", validationResult.Errors));
            }

            // Create multicast message
            var message = CreateMulticastMessage(request);

            // Send notifications
            var response = await _messaging.SendMulticastAsync(message, cancellationToken);

            var result = new PushNotificationResult
            {
                IsSuccess = response.SuccessCount > 0,
                MessageId = Guid.NewGuid().ToString(),
                Status = response.SuccessCount > 0 ? PushNotificationStatus.Sent : PushNotificationStatus.Failed,
                SentAt = DateTime.UtcNow,
                Metadata = new Dictionary<string, object>
                {
                    ["totalDevices"] = request.DeviceTokens.Count.ToString(),
                    ["successCount"] = response.SuccessCount.ToString(),
                    ["failureCount"] = response.FailureCount.ToString(),
                    ["responses"] = response.Responses.Select((r, i) => new
                    {
                        Index = i,
                        IsSuccess = r.IsSuccess,
                        MessageId = r.MessageId,
                        Exception = r.Exception?.Message
                    }).ToList()
                }
            };

            _logger.LogInformation("Bulk push notification completed. Success: {SuccessCount}/{TotalCount}",
                response.SuccessCount, request.DeviceTokens.Count);

            return result;
        }
        catch (FirebaseMessagingException ex)
        {
            _logger.LogError(ex, "Firebase messaging error sending bulk push notification: {ErrorCode} - {ErrorMessage}",
                ex.ErrorCode, ex.Message);

            return PushNotificationResult.Failure($"Firebase error: {ex.Message}", ex.ErrorCode?.ToString());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error sending bulk push notification");
            return PushNotificationResult.Failure($"Unexpected error: {ex.Message}");
        }
    }

    public async Task<PushNotificationResult> SendToTopicAsync(
        TopicPushNotificationRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Sending push notification to topic {Topic} via Firebase", request.Topic);

            // Validate request
            var validationResult = ValidateTopicRequest(request);
            if (!validationResult.IsValid)
            {
                return PushNotificationResult.Failure(string.Join(", ", validationResult.Errors));
            }

            // Create topic message
            var message = CreateTopicMessage(request);

            // Send notification
            var response = await _messaging.SendAsync(message, cancellationToken);

            var result = new PushNotificationResult
            {
                IsSuccess = !string.IsNullOrEmpty(response),
                MessageId = response,
                ExternalId = response,
                Status = !string.IsNullOrEmpty(response) ? PushNotificationStatus.Sent : PushNotificationStatus.Failed,
                SentAt = DateTime.UtcNow,
                Metadata = new Dictionary<string, object>
                {
                    ["firebaseMessageId"] = response ?? "",
                    ["topic"] = request.Topic,
                    ["condition"] = request.Condition ?? "",
                    ["hasData"] = request.Data?.Any().ToString() ?? "false"
                }
            };

            if (result.IsSuccess)
            {
                _logger.LogInformation("Topic push notification sent successfully to {Topic}. Firebase MessageId: {MessageId}",
                    request.Topic, response);
            }

            return result;
        }
        catch (FirebaseMessagingException ex)
        {
            _logger.LogError(ex, "Firebase messaging error sending topic push notification: {ErrorCode} - {ErrorMessage}",
                ex.ErrorCode, ex.Message);

            return PushNotificationResult.Failure($"Firebase error: {ex.Message}", ex.ErrorCode?.ToString());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error sending topic push notification");
            return PushNotificationResult.Failure($"Unexpected error: {ex.Message}");
        }
    }

    public async Task<bool> ValidateDeviceTokenAsync(string deviceToken, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(deviceToken))
                return false;

            // Basic token validation - Firebase tokens are typically 152+ characters
            return deviceToken.Length >= 140 && !deviceToken.Contains(" ");
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Device token validation failed for token");
            return false;
        }
    }

    public async Task<bool> SubscribeToTopicAsync(string deviceToken, string topic, CancellationToken cancellationToken = default)
    {
        try
        {
            await _messaging.SubscribeToTopicAsync(new List<string> { deviceToken }, topic, cancellationToken);
            _logger.LogInformation("Device subscribed to topic {Topic}", topic);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to subscribe device to topic {Topic}", topic);
            return false;
        }
    }

    public async Task<bool> UnsubscribeFromTopicAsync(string deviceToken, string topic, CancellationToken cancellationToken = default)
    {
        try
        {
            await _messaging.UnsubscribeFromTopicAsync(new List<string> { deviceToken }, topic, cancellationToken);
            _logger.LogInformation("Device unsubscribed from topic {Topic}", topic);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to unsubscribe device from topic {Topic}", topic);
            return false;
        }
    }

    private void InitializeFirebase()
    {
        try
        {
            GoogleCredential credential;

            if (!string.IsNullOrEmpty(_settings.ServiceAccountKeyPath))
            {
                credential = GoogleCredential.FromFile(_settings.ServiceAccountKeyPath);
            }
            else if (!string.IsNullOrEmpty(_settings.ServiceAccountKeyJson))
            {
                credential = GoogleCredential.FromJson(_settings.ServiceAccountKeyJson);
            }
            else
            {
                // Use default credentials (for Google Cloud environments)
                credential = GoogleCredential.GetApplicationDefault();
            }

            FirebaseApp.Create(new AppOptions()
            {
                Credential = credential,
                ProjectId = _settings.ProjectId
            });

            _logger.LogInformation("Firebase initialized successfully for project {ProjectId}", _settings.ProjectId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize Firebase");
            throw;
        }
    }

    private Message CreateFirebaseMessage(PushNotificationRequest request)
    {
        var messageBuilder = new Message()
        {
            Token = request.DeviceToken,
            Notification = new Notification()
            {
                Title = request.Title,
                Body = request.Body,
                ImageUrl = request.ImageUrl
            },
            Data = request.Data ?? new Dictionary<string, string>()
        };

        // Add platform-specific configuration
        if (request.Platform == DevicePlatform.Android)
        {
            messageBuilder.Android = new AndroidConfig()
            {
                Priority = request.Priority == NotificationPriority.High ? Priority.High : Priority.Normal,
                Notification = new AndroidNotification()
                {
                    Icon = request.Icon,
                    Color = request.Color,
                    Sound = request.Sound,
                    ChannelId = request.ChannelId
                }
            };
        }
        else if (request.Platform == DevicePlatform.iOS)
        {
            messageBuilder.Apns = new ApnsConfig()
            {
                Aps = new Aps()
                {
                    Alert = new ApsAlert()
                    {
                        Title = request.Title,
                        Body = request.Body
                    },
                    Badge = request.Badge,
                    Sound = request.Sound ?? "default"
                }
            };
        }

        return messageBuilder;
    }

    private MulticastMessage CreateMulticastMessage(BulkPushNotificationRequest request)
    {
        return new MulticastMessage()
        {
            Tokens = request.DeviceTokens,
            Notification = new Notification()
            {
                Title = request.Title,
                Body = request.Body,
                ImageUrl = request.ImageUrl
            },
            Data = request.Data ?? new Dictionary<string, string>()
        };
    }

    private Message CreateTopicMessage(TopicPushNotificationRequest request)
    {
        var message = new Message()
        {
            Notification = new Notification()
            {
                Title = request.Title,
                Body = request.Body,
                ImageUrl = request.ImageUrl
            },
            Data = request.Data ?? new Dictionary<string, string>()
        };

        if (!string.IsNullOrEmpty(request.Topic))
        {
            message.Topic = request.Topic;
        }
        else if (!string.IsNullOrEmpty(request.Condition))
        {
            message.Condition = request.Condition;
        }

        return message;
    }

    private PushValidationResult ValidateRequest(PushNotificationRequest request)
    {
        var errors = new List<string>();

        if (string.IsNullOrWhiteSpace(request.DeviceToken))
            errors.Add("Device token is required");

        if (string.IsNullOrWhiteSpace(request.Title) && string.IsNullOrWhiteSpace(request.Body))
            errors.Add("Either title or body is required");

        if (request.Title?.Length > _settings.MaxTitleLength)
            errors.Add($"Title exceeds maximum length of {_settings.MaxTitleLength} characters");

        if (request.Body?.Length > _settings.MaxBodyLength)
            errors.Add($"Body exceeds maximum length of {_settings.MaxBodyLength} characters");

        return new PushValidationResult
        {
            IsValid = !errors.Any(),
            Errors = errors
        };
    }

    private PushValidationResult ValidateBulkRequest(BulkPushNotificationRequest request)
    {
        var errors = new List<string>();

        if (!request.DeviceTokens?.Any() == true)
            errors.Add("At least one device token is required");

        if (request.DeviceTokens?.Count > _settings.MaxBulkDevices)
            errors.Add($"Cannot send to more than {_settings.MaxBulkDevices} devices at once");

        if (string.IsNullOrWhiteSpace(request.Title) && string.IsNullOrWhiteSpace(request.Body))
            errors.Add("Either title or body is required");

        return new PushValidationResult
        {
            IsValid = !errors.Any(),
            Errors = errors
        };
    }

    private PushValidationResult ValidateTopicRequest(TopicPushNotificationRequest request)
    {
        var errors = new List<string>();

        if (string.IsNullOrWhiteSpace(request.Topic) && string.IsNullOrWhiteSpace(request.Condition))
            errors.Add("Either topic or condition is required");

        if (string.IsNullOrWhiteSpace(request.Title) && string.IsNullOrWhiteSpace(request.Body))
            errors.Add("Either title or body is required");

        return new PushValidationResult
        {
            IsValid = !errors.Any(),
            Errors = errors
        };
    }
}

/// <summary>
/// Firebase configuration settings
/// </summary>
public class FirebaseSettings
{
    public string ProjectId { get; set; } = string.Empty;
    public string ServiceAccountKeyPath { get; set; } = string.Empty;
    public string ServiceAccountKeyJson { get; set; } = string.Empty;
    public int MaxTitleLength { get; set; } = 100;
    public int MaxBodyLength { get; set; } = 4000;
    public int MaxBulkDevices { get; set; } = 500;
    public int TimeoutSeconds { get; set; } = 30;
    public int RetryAttempts { get; set; } = 3;
}

/// <summary>
/// Push notification validation result
/// </summary>
public class PushValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
}
