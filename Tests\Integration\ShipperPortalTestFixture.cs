using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Net.Http;

namespace Tests.Integration;

/// <summary>
/// Test fixture for Shipper Portal integration tests
/// Provides HTTP clients for all microservices and manages test environment setup
/// </summary>
public class ShipperPortalTestFixture : IDisposable
{
    private readonly Dictionary<string, WebApplicationFactory<Program>> _factories;
    private readonly Dictionary<string, HttpClient> _clients;
    private bool _disposed = false;

    public ShipperPortalTestFixture()
    {
        _factories = new Dictionary<string, WebApplicationFactory<Program>>();
        _clients = new Dictionary<string, HttpClient>();
        
        InitializeTestEnvironment();
    }

    public HttpClient CreateOrderManagementClient()
    {
        return GetOrCreateClient("OrderManagement", 5001);
    }

    public HttpClient CreateTripManagementClient()
    {
        return GetOrCreateClient("TripManagement", 5002);
    }

    public HttpClient CreateFinancialPaymentClient()
    {
        return GetOrCreateClient("FinancialPayment", 5003);
    }

    public HttpClient CreateNetworkFleetClient()
    {
        return GetOrCreateClient("NetworkFleetManagement", 5004);
    }

    public HttpClient CreateAnalyticsClient()
    {
        return GetOrCreateClient("AnalyticsBIService", 5005);
    }

    public HttpClient CreateUserManagementClient()
    {
        return GetOrCreateClient("UserManagement", 5006);
    }

    public HttpClient CreateCommunicationClient()
    {
        return GetOrCreateClient("CommunicationNotification", 5007);
    }

    private HttpClient GetOrCreateClient(string serviceName, int port)
    {
        if (_clients.ContainsKey(serviceName))
        {
            return _clients[serviceName];
        }

        var factory = CreateWebApplicationFactory(serviceName, port);
        var client = factory.CreateClient();
        
        // Configure client with authentication and headers
        ConfigureHttpClient(client, serviceName);
        
        _factories[serviceName] = factory;
        _clients[serviceName] = client;
        
        return client;
    }

    private WebApplicationFactory<Program> CreateWebApplicationFactory(string serviceName, int port)
    {
        return new WebApplicationFactory<Program>()
            .WithWebHostBuilder(builder =>
            {
                builder.UseEnvironment("Testing");
                builder.UseUrls($"http://localhost:{port}");
                
                builder.ConfigureAppConfiguration((context, config) =>
                {
                    config.AddInMemoryCollection(GetTestConfiguration(serviceName));
                });

                builder.ConfigureServices(services =>
                {
                    ConfigureTestServices(services, serviceName);
                });

                builder.ConfigureLogging(logging =>
                {
                    logging.ClearProviders();
                    logging.AddConsole();
                    logging.SetMinimumLevel(LogLevel.Warning);
                });
            });
    }

    private void ConfigureHttpClient(HttpClient client, string serviceName)
    {
        // Add authentication headers
        client.DefaultRequestHeaders.Add("Authorization", "Bearer test-token");
        client.DefaultRequestHeaders.Add("X-User-Id", "test-user-id");
        client.DefaultRequestHeaders.Add("X-Service-Name", serviceName);
        client.DefaultRequestHeaders.Add("X-Test-Environment", "true");
        
        // Set timeout
        client.Timeout = TimeSpan.FromMinutes(5);
    }

    private Dictionary<string, string> GetTestConfiguration(string serviceName)
    {
        var baseConfig = new Dictionary<string, string>
        {
            ["Environment"] = "Testing",
            ["ConnectionStrings:DefaultConnection"] = "Server=(localdb)\\mssqllocaldb;Database=TLI_Test;Trusted_Connection=true;MultipleActiveResultSets=true",
            ["ConnectionStrings:Redis"] = "localhost:6379",
            ["MessageBroker:ConnectionString"] = "amqp://guest:guest@localhost:5672/",
            ["MessageBroker:ExchangeName"] = "tli-test-exchange",
            ["Logging:LogLevel:Default"] = "Warning",
            ["Logging:LogLevel:Microsoft"] = "Warning",
            ["Authentication:JwtBearer:Authority"] = "https://test-authority.com",
            ["Authentication:JwtBearer:Audience"] = "tli-api",
            ["ServiceDiscovery:ConsulUrl"] = "http://localhost:8500",
            ["Monitoring:ApplicationInsights:InstrumentationKey"] = "test-key"
        };

        // Service-specific configurations
        var serviceConfigs = new Dictionary<string, Dictionary<string, string>>
        {
            ["OrderManagement"] = new()
            {
                ["Services:TripManagement:BaseUrl"] = "http://localhost:5002",
                ["Services:FinancialPayment:BaseUrl"] = "http://localhost:5003",
                ["Services:NetworkFleetManagement:BaseUrl"] = "http://localhost:5004",
                ["Services:CommunicationNotification:BaseUrl"] = "http://localhost:5007"
            },
            ["TripManagement"] = new()
            {
                ["Services:OrderManagement:BaseUrl"] = "http://localhost:5001",
                ["Services:NetworkFleetManagement:BaseUrl"] = "http://localhost:5004",
                ["Services:CommunicationNotification:BaseUrl"] = "http://localhost:5007"
            },
            ["FinancialPayment"] = new()
            {
                ["Services:OrderManagement:BaseUrl"] = "http://localhost:5001",
                ["Services:TripManagement:BaseUrl"] = "http://localhost:5002",
                ["PaymentGateway:ApiKey"] = "test-api-key",
                ["PaymentGateway:BaseUrl"] = "https://test-payment-gateway.com"
            },
            ["NetworkFleetManagement"] = new()
            {
                ["Services:OrderManagement:BaseUrl"] = "http://localhost:5001",
                ["Services:TripManagement:BaseUrl"] = "http://localhost:5002",
                ["Services:AnalyticsBIService:BaseUrl"] = "http://localhost:5005"
            },
            ["AnalyticsBIService"] = new()
            {
                ["Services:OrderManagement:BaseUrl"] = "http://localhost:5001",
                ["Services:TripManagement:BaseUrl"] = "http://localhost:5002",
                ["Services:FinancialPayment:BaseUrl"] = "http://localhost:5003",
                ["Services:NetworkFleetManagement:BaseUrl"] = "http://localhost:5004",
                ["DataWarehouse:ConnectionString"] = "Server=(localdb)\\mssqllocaldb;Database=TLI_DataWarehouse_Test;Trusted_Connection=true"
            }
        };

        if (serviceConfigs.ContainsKey(serviceName))
        {
            foreach (var kvp in serviceConfigs[serviceName])
            {
                baseConfig[kvp.Key] = kvp.Value;
            }
        }

        return baseConfig;
    }

    private void ConfigureTestServices(IServiceCollection services, string serviceName)
    {
        // Replace external dependencies with test implementations
        ConfigureTestDatabase(services, serviceName);
        ConfigureTestMessageBroker(services);
        ConfigureTestExternalServices(services, serviceName);
        ConfigureTestAuthentication(services);
    }

    private void ConfigureTestDatabase(IServiceCollection services, string serviceName)
    {
        // Configure in-memory database for testing
        // This would typically use Entity Framework InMemory provider
        // or a test database container
    }

    private void ConfigureTestMessageBroker(IServiceCollection services)
    {
        // Configure test message broker (could be in-memory or test RabbitMQ)
        // services.AddSingleton<IMessageBroker, TestMessageBroker>();
    }

    private void ConfigureTestExternalServices(IServiceCollection services, string serviceName)
    {
        // Configure mock external services
        switch (serviceName)
        {
            case "FinancialPayment":
                // services.AddSingleton<IPaymentGatewayService, MockPaymentGatewayService>();
                break;
            case "NetworkFleetManagement":
                // services.AddSingleton<ILocationService, MockLocationService>();
                break;
            case "CommunicationNotification":
                // services.AddSingleton<IEmailService, MockEmailService>();
                // services.AddSingleton<ISmsService, MockSmsService>();
                break;
        }
    }

    private void ConfigureTestAuthentication(IServiceCollection services)
    {
        // Configure test authentication
        // services.AddAuthentication("Test")
        //     .AddScheme<TestAuthenticationSchemeOptions, TestAuthenticationHandler>("Test", options => { });
    }

    private void InitializeTestEnvironment()
    {
        // Initialize test environment
        SetupTestDatabase();
        SetupTestMessageBroker();
        SeedTestData();
    }

    private void SetupTestDatabase()
    {
        // Setup test databases for all services
        // This could involve creating test databases, running migrations, etc.
    }

    private void SetupTestMessageBroker()
    {
        // Setup test message broker
        // This could involve starting a test RabbitMQ container or using in-memory implementation
    }

    private void SeedTestData()
    {
        // Seed test data across all services
        // This ensures consistent test data for integration tests
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            foreach (var client in _clients.Values)
            {
                client?.Dispose();
            }

            foreach (var factory in _factories.Values)
            {
                factory?.Dispose();
            }

            _clients.Clear();
            _factories.Clear();

            CleanupTestEnvironment();
            _disposed = true;
        }
    }

    private void CleanupTestEnvironment()
    {
        // Cleanup test environment
        CleanupTestDatabases();
        CleanupTestMessageBroker();
    }

    private void CleanupTestDatabases()
    {
        // Cleanup test databases
    }

    private void CleanupTestMessageBroker()
    {
        // Cleanup test message broker
    }
}

/// <summary>
/// Test utilities for integration testing
/// </summary>
public static class TestUtilities
{
    public static async Task<T> WaitForEventualConsistency<T>(
        Func<Task<T>> operation,
        Func<T, bool> condition,
        TimeSpan timeout,
        TimeSpan interval)
    {
        var endTime = DateTime.UtcNow.Add(timeout);
        
        while (DateTime.UtcNow < endTime)
        {
            var result = await operation();
            if (condition(result))
            {
                return result;
            }
            
            await Task.Delay(interval);
        }
        
        throw new TimeoutException($"Condition not met within {timeout}");
    }

    public static async Task WaitForEventProcessing(TimeSpan delay)
    {
        // Wait for event processing to complete
        await Task.Delay(delay);
    }

    public static string GenerateTestEmail()
    {
        return $"test-{Guid.NewGuid():N}@example.com";
    }

    public static string GenerateTestPhoneNumber()
    {
        var random = new Random();
        return $"+1{random.Next(100, 999)}{random.Next(100, 999)}{random.Next(1000, 9999)}";
    }

    public static Dictionary<string, object> CreateTestMetadata()
    {
        return new Dictionary<string, object>
        {
            ["TestRun"] = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"),
            ["TestId"] = Guid.NewGuid().ToString(),
            ["Environment"] = "Testing"
        };
    }
}

/// <summary>
/// Test data builder for creating consistent test data
/// </summary>
public class TestDataBuilder
{
    private readonly Dictionary<string, object> _data = new();

    public TestDataBuilder WithShipper(Guid? shipperId = null)
    {
        _data["ShipperId"] = shipperId ?? Guid.NewGuid();
        _data["ShipperEmail"] = TestUtilities.GenerateTestEmail();
        _data["ShipperPhone"] = TestUtilities.GenerateTestPhoneNumber();
        return this;
    }

    public TestDataBuilder WithCarrier(Guid? carrierId = null)
    {
        _data["CarrierId"] = carrierId ?? Guid.NewGuid();
        _data["CarrierName"] = $"Test Carrier {Guid.NewGuid():N}";
        _data["CarrierEmail"] = TestUtilities.GenerateTestEmail();
        return this;
    }

    public TestDataBuilder WithOrder(Guid? orderId = null)
    {
        _data["OrderId"] = orderId ?? Guid.NewGuid();
        _data["OrderNumber"] = $"ORD-{DateTime.UtcNow:yyyyMMdd}-{Guid.NewGuid():N[..8]}";
        return this;
    }

    public TestDataBuilder WithTrip(Guid? tripId = null)
    {
        _data["TripId"] = tripId ?? Guid.NewGuid();
        _data["TripNumber"] = $"TRP-{DateTime.UtcNow:yyyyMMdd}-{Guid.NewGuid():N[..8]}";
        return this;
    }

    public TestDataBuilder WithVehicle(Guid? vehicleId = null)
    {
        _data["VehicleId"] = vehicleId ?? Guid.NewGuid();
        _data["VehicleNumber"] = $"VEH-{Guid.NewGuid():N[..8]}";
        return this;
    }

    public TestDataBuilder WithDriver(Guid? driverId = null)
    {
        _data["DriverId"] = driverId ?? Guid.NewGuid();
        _data["DriverName"] = $"Test Driver {Guid.NewGuid():N[..8]}";
        _data["DriverLicense"] = $"DL{Guid.NewGuid():N[..10]}";
        return this;
    }

    public T Build<T>() where T : new()
    {
        var result = new T();
        var properties = typeof(T).GetProperties();
        
        foreach (var property in properties)
        {
            if (_data.ContainsKey(property.Name))
            {
                property.SetValue(result, _data[property.Name]);
            }
        }
        
        return result;
    }

    public Dictionary<string, object> BuildDictionary()
    {
        return new Dictionary<string, object>(_data);
    }
}
