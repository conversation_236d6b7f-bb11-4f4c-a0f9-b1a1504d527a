using System;
using System.IO;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using UserManagement.API;
using UserManagement.Domain.Entities;
using UserManagement.Infrastructure.Data;
using UserManagement.Tests.Helpers;
using Xunit;

namespace UserManagement.Tests.Integration
{
    public class DocumentsControllerTests : IClassFixture<TestWebApplicationFactory<Program>>
    {
        private readonly TestWebApplicationFactory<Program> _factory;
        private readonly HttpClient _client;

        public DocumentsControllerTests(TestWebApplicationFactory<Program> factory)
        {
            _factory = factory;
            _client = _factory.CreateClient();
        }

        [Fact]
        public async Task UploadDocument_WithValidFile_ShouldReturnOk()
        {
            // Arrange
            var userId = await CreateTestUser();
            
            var content = new MultipartFormDataContent();
            content.Add(new StringContent(userId.ToString()), "UserId");
            content.Add(new StringContent(DocumentType.ProfilePhoto.ToString()), "DocumentType");
            
            var fileContent = new ByteArrayContent(new byte[] { 1, 2, 3, 4 });
            fileContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("image/jpeg");
            content.Add(fileContent, "File", "test.jpg");

            // Act
            var response = await _client.PostAsync("/api/documents/upload", content);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
        }

        [Fact]
        public async Task GetUserDocuments_WithExistingUser_ShouldReturnOk()
        {
            // Arrange
            var userId = await CreateTestUser();

            // Act
            var response = await _client.GetAsync($"/api/documents/user/{userId}");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
        }

        [Fact]
        public async Task GetUserDocuments_WithNonExistingUser_ShouldReturnNotFound()
        {
            // Arrange
            var nonExistingUserId = Guid.NewGuid();

            // Act
            var response = await _client.GetAsync($"/api/documents/user/{nonExistingUserId}");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.NotFound);
        }

        private async Task<Guid> CreateTestUser()
        {
            using var scope = _factory.Services.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<UserManagementDbContext>();

            var userId = Guid.NewGuid();
            var profile = new UserProfile(userId, UserType.Driver, "<EMAIL>");
            var documentSubmission = new DocumentSubmission(userId, UserType.Driver);

            context.UserProfiles.Add(profile);
            context.DocumentSubmissions.Add(documentSubmission);
            await context.SaveChangesAsync();

            return userId;
        }
    }
}
