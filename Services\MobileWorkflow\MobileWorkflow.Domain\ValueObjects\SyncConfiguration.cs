
using Shared.Domain.Common;
using Shared.Domain.ValueObjects;

namespace MobileWorkflow.Domain.ValueObjects;

public class SyncConfiguration : ValueObject
{
    public bool AutoSyncEnabled { get; }
    public TimeSpan SyncInterval { get; }
    public int MaxRetryAttempts { get; }
    public TimeSpan RetryDelay { get; }
    public bool SyncOnWiFiOnly { get; }
    public bool SyncOnLowBattery { get; }
    public int MaxOfflineDataSize { get; } // in MB
    public int MaxOfflineDataAge { get; } // in hours
    public List<string> PriorityDataTypes { get; }
    public Dictionary<string, object> ConflictResolutionRules { get; }

    public SyncConfiguration(
        bool autoSyncEnabled = true,
        TimeSpan? syncInterval = null,
        int maxRetryAttempts = 3,
        TimeSpan? retryDelay = null,
        bool syncOnWiFiOnly = false,
        bool syncOnLowBattery = false,
        int maxOfflineDataSize = 100,
        int maxOfflineDataAge = 24,
        List<string>? priorityDataTypes = null,
        Dictionary<string, object>? conflictResolutionRules = null)
    {
        AutoSyncEnabled = autoSyncEnabled;
        SyncInterval = syncInterval ?? TimeSpan.FromMinutes(5);
        MaxRetryAttempts = maxRetryAttempts;
        RetryDelay = retryDelay ?? TimeSpan.FromMinutes(1);
        SyncOnWiFiOnly = syncOnWiFiOnly;
        SyncOnLowBattery = syncOnLowBattery;
        MaxOfflineDataSize = maxOfflineDataSize;
        MaxOfflineDataAge = maxOfflineDataAge;
        PriorityDataTypes = priorityDataTypes ?? new List<string> { "Emergency", "TripUpdate", "PODUpload" };
        ConflictResolutionRules = conflictResolutionRules ?? new Dictionary<string, object>
        {
            { "default", "server_wins" },
            { "TripUpdate", "latest_timestamp" },
            { "PODUpload", "merge" },
            { "Emergency", "client_wins" }
        };
    }

    public bool ShouldSync(string dataType, DateTime lastSync, bool isWiFiConnected, bool isLowBattery)
    {
        if (!AutoSyncEnabled)
            return false;

        if (SyncOnWiFiOnly && !isWiFiConnected)
            return false;

        if (!SyncOnLowBattery && isLowBattery)
            return false;

        if (PriorityDataTypes.Contains(dataType))
            return true; // Always sync priority data

        return DateTime.UtcNow - lastSync >= SyncInterval;
    }

    public string GetConflictResolutionStrategy(string dataType)
    {
        if (ConflictResolutionRules.ContainsKey(dataType))
            return ConflictResolutionRules[dataType].ToString() ?? "server_wins";

        return ConflictResolutionRules.GetValueOrDefault("default", "server_wins").ToString() ?? "server_wins";
    }

    public TimeSpan GetRetryDelay(int attemptNumber)
    {
        // Exponential backoff
        var multiplier = Math.Pow(2, Math.Min(attemptNumber - 1, 5));
        return TimeSpan.FromTicks((long)(RetryDelay.Ticks * multiplier));
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return AutoSyncEnabled;
        yield return SyncInterval;
        yield return MaxRetryAttempts;
        yield return RetryDelay;
        yield return SyncOnWiFiOnly;
        yield return SyncOnLowBattery;
        yield return MaxOfflineDataSize;
        yield return MaxOfflineDataAge;
        yield return string.Join(",", PriorityDataTypes);
    }
}


