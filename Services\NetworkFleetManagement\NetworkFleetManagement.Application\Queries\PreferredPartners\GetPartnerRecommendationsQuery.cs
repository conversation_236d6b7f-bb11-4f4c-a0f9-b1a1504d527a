using MediatR;
using NetworkFleetManagement.Application.DTOs;

namespace NetworkFleetManagement.Application.Queries.PreferredPartners;

public class GetPartnerRecommendationsQuery : IRequest<List<PartnerRecommendationDto>>
{
    public Guid UserId { get; set; }
    public string UserRole { get; set; } = string.Empty;
    public string? PartnerType { get; set; }
    public string? Route { get; set; }
    public string? LoadType { get; set; }
    public int Limit { get; set; } = 10;
}
