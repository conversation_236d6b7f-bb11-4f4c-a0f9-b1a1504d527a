using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Identity.Domain.Repositories;
using MediatR;

namespace Identity.Application.Users.Queries.CheckUserPermission
{
    public class CheckUserPermissionQueryHandler : IRequestHandler<CheckUserPermissionQuery, bool>
    {
        private readonly IUserRepository _userRepository;
        private readonly IRoleRepository _roleRepository;
        private readonly IPermissionRepository _permissionRepository;

        public CheckUserPermissionQueryHandler(
            IUserRepository userRepository,
            IRoleRepository roleRepository,
            IPermissionRepository permissionRepository)
        {
            _userRepository = userRepository;
            _roleRepository = roleRepository;
            _permissionRepository = permissionRepository;
        }

        public async Task<bool> Handle(CheckUserPermissionQuery request, CancellationToken cancellationToken)
        {
            var user = await _userRepository.GetByIdAsync(request.UserId);
            if (user == null)
            {
                return false;
            }

            // Get user's roles
            var userRoles = await _roleRepository.GetByUserIdAsync(request.UserId);

            // Get permissions for all user roles
            foreach (var role in userRoles)
            {
                var rolePermissions = await _permissionRepository.GetByRoleIdAsync(role.Id);
                if (rolePermissions.Any(p => p.Name.Equals(request.Permission, System.StringComparison.OrdinalIgnoreCase)))
                {
                    return true;
                }
            }

            return false;
        }
    }
}
