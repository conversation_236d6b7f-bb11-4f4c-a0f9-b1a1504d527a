# High Priority Features - Technical Specifications

## 1. KYC Auto-check Functionality

### Overview

Implement automated KYC verification with external API integration for PAN, GST, and Aadhar verification in the UserManagement service.

### Current Foundation Analysis

- **Existing Infrastructure**: Document entity with VerificationMethod enum (Manual, AI_OCR, Hybrid)
- **Document Types**: PanCard, GstCertificate, AadharCard already defined
- **Status Tracking**: DocumentStatus enum with Pending, Uploaded, UnderReview, Approved, Rejected states

### Technical Implementation

#### 1.1 Domain Layer Enhancements

**New Value Objects**:

```csharp
// Services/UserManagement/UserManagement.Domain/ValueObjects/KycVerificationResult.cs
public class KycVerificationResult : ValueObject
{
    public string VerificationId { get; private set; }
    public KycVerificationStatus Status { get; private set; }
    public string ProviderName { get; private set; }
    public decimal ConfidenceScore { get; private set; }
    public Dictionary<string, object> ExtractedData { get; private set; }
    public List<string> ValidationErrors { get; private set; }
    public DateTime VerifiedAt { get; private set; }
    public DateTime? ExpiresAt { get; private set; }
}

public enum KycVerificationStatus
{
    Pending,
    InProgress,
    Verified,
    Failed,
    Expired,
    RequiresManualReview
}
```

**Enhanced Document Entity**:

```csharp
// Add to existing Document entity
public KycVerificationResult? KycVerificationResult { get; private set; }
public bool IsAutoVerificationEnabled { get; private set; }
public int VerificationAttempts { get; private set; }
public DateTime? LastVerificationAttempt { get; private set; }

public void StartAutoVerification()
{
    if (Status != DocumentStatus.Uploaded)
        throw new UserManagementDomainException("Document must be uploaded before verification");

    Status = DocumentStatus.UnderReview;
    VerificationMethod = VerificationMethod.AI_OCR;
    IsAutoVerificationEnabled = true;
    LastVerificationAttempt = DateTime.UtcNow;
    VerificationAttempts++;
}

public void CompleteAutoVerification(KycVerificationResult result)
{
    KycVerificationResult = result;

    if (result.Status == KycVerificationStatus.Verified && result.ConfidenceScore >= 0.85m)
    {
        Status = DocumentStatus.Approved;
        ReviewedAt = DateTime.UtcNow;
    }
    else if (result.Status == KycVerificationStatus.Failed || result.ConfidenceScore < 0.85m)
    {
        Status = DocumentStatus.RequiresResubmission;
        RejectionReason = "Auto-verification failed. Manual review required.";
    }
}
```

#### 1.2 Application Layer Implementation

**Commands**:

```csharp
// Services/UserManagement/UserManagement.Application/Commands/StartKycAutoVerification/StartKycAutoVerificationCommand.cs
public class StartKycAutoVerificationCommand : IRequest<bool>
{
    public Guid DocumentId { get; set; }
    public Guid UserId { get; set; }
    public DocumentType DocumentType { get; set; }
    public bool ForceVerification { get; set; } = false;
}

// Handler implementation
public class StartKycAutoVerificationCommandHandler : IRequestHandler<StartKycAutoVerificationCommand, bool>
{
    private readonly IDocumentSubmissionRepository _repository;
    private readonly IKycVerificationService _kycService;
    private readonly IMessageBroker _messageBroker;

    public async Task<bool> Handle(StartKycAutoVerificationCommand request, CancellationToken cancellationToken)
    {
        var submission = await _repository.GetByUserIdAsync(request.UserId, cancellationToken);
        var document = submission.Documents.FirstOrDefault(d => d.Id == request.DocumentId);

        if (document == null)
            throw new NotFoundException($"Document {request.DocumentId} not found");

        // Start auto-verification
        document.StartAutoVerification();

        // Queue verification job
        await _messageBroker.PublishAsync("kyc.verification.requested", new
        {
            DocumentId = document.Id,
            UserId = request.UserId,
            DocumentType = document.DocumentType.ToString(),
            FilePath = document.FilePath,
            RequestedAt = DateTime.UtcNow
        }, cancellationToken);

        await _repository.UpdateAsync(submission, cancellationToken);
        return true;
    }
}
```

**Queries**:

```csharp
// Services/UserManagement/UserManagement.Application/Queries/GetKycVerificationStatus/GetKycVerificationStatusQuery.cs
public class GetKycVerificationStatusQuery : IRequest<KycVerificationStatusDto>
{
    public Guid UserId { get; set; }
    public DocumentType? DocumentType { get; set; }
}

public class KycVerificationStatusDto
{
    public Guid UserId { get; set; }
    public List<DocumentVerificationDto> Documents { get; set; } = new();
    public decimal OverallCompletionPercentage { get; set; }
    public KycVerificationStatus OverallStatus { get; set; }
    public DateTime? LastVerificationAttempt { get; set; }
    public List<string> PendingDocuments { get; set; } = new();
}
```

#### 1.3 Infrastructure Layer Implementation

**External API Service**:

```csharp
// Services/UserManagement/UserManagement.Infrastructure/Services/KycVerificationService.cs
public interface IKycVerificationService
{
    Task<KycVerificationResult> VerifyPanCardAsync(string filePath, CancellationToken cancellationToken = default);
    Task<KycVerificationResult> VerifyGstCertificateAsync(string filePath, CancellationToken cancellationToken = default);
    Task<KycVerificationResult> VerifyAadharCardAsync(string filePath, CancellationToken cancellationToken = default);
}

public class KycVerificationService : IKycVerificationService
{
    private readonly HttpClient _httpClient;
    private readonly IConfiguration _configuration;
    private readonly ILogger<KycVerificationService> _logger;

    public async Task<KycVerificationResult> VerifyPanCardAsync(string filePath, CancellationToken cancellationToken = default)
    {
        try
        {
            // Implementation for PAN verification API call
            var apiEndpoint = _configuration["KycVerification:PanApi:Endpoint"];
            var apiKey = _configuration["KycVerification:PanApi:ApiKey"];

            using var fileStream = File.OpenRead(filePath);
            using var content = new MultipartFormDataContent();
            content.Add(new StreamContent(fileStream), "file", Path.GetFileName(filePath));

            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {apiKey}");
            var response = await _httpClient.PostAsync(apiEndpoint, content, cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
                var apiResult = JsonSerializer.Deserialize<PanVerificationApiResponse>(responseContent);

                return new KycVerificationResult(
                    verificationId: apiResult.VerificationId,
                    status: MapApiStatusToKycStatus(apiResult.Status),
                    providerName: "PAN Verification API",
                    confidenceScore: apiResult.ConfidenceScore,
                    extractedData: apiResult.ExtractedData,
                    validationErrors: apiResult.Errors ?? new List<string>(),
                    verifiedAt: DateTime.UtcNow,
                    expiresAt: DateTime.UtcNow.AddYears(1)
                );
            }

            throw new KycVerificationException($"PAN verification failed: {response.StatusCode}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error verifying PAN card for file {FilePath}", filePath);
            throw;
        }
    }
}
```

#### 1.4 API Layer Implementation

**Controller Endpoints**:

```csharp
// Services/UserManagement/UserManagement.API/Controllers/KycController.cs
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class KycController : ControllerBase
{
    private readonly IMediator _mediator;

    [HttpPost("verify/{documentId}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<bool>> StartAutoVerification(Guid documentId, [FromBody] StartKycAutoVerificationRequest request)
    {
        var command = new StartKycAutoVerificationCommand
        {
            DocumentId = documentId,
            UserId = GetCurrentUserId(),
            DocumentType = request.DocumentType,
            ForceVerification = request.ForceVerification
        };

        var result = await _mediator.Send(command);
        return Ok(result);
    }

    [HttpGet("status")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<KycVerificationStatusDto>> GetVerificationStatus([FromQuery] DocumentType? documentType = null)
    {
        var query = new GetKycVerificationStatusQuery
        {
            UserId = GetCurrentUserId(),
            DocumentType = documentType
        };

        var result = await _mediator.Send(query);
        return Ok(result);
    }
}
```

### Configuration Requirements

**appsettings.json**:

```json
{
  "KycVerification": {
    "PanApi": {
      "Endpoint": "https://api.panverification.com/v1/verify",
      "ApiKey": "your-pan-api-key",
      "Timeout": 30000
    },
    "GstApi": {
      "Endpoint": "https://api.gstverification.com/v1/verify",
      "ApiKey": "your-gst-api-key",
      "Timeout": 30000
    },
    "AadharApi": {
      "Endpoint": "https://api.aadharverification.com/v1/verify",
      "ApiKey": "your-aadhar-api-key",
      "Timeout": 30000
    },
    "RetryPolicy": {
      "MaxRetries": 3,
      "DelayBetweenRetries": 5000
    }
  }
}
```

## 2. Auto-renew Toggle Implementation

### Overview

Enhance the existing auto-renewal functionality in SubscriptionManagement service with user-controlled toggle, scheduled processing, and comprehensive notification system.

### Current Foundation Analysis

- **Existing Infrastructure**: Subscription entity with AutoRenew boolean property
- **Renewal Logic**: Basic renewal method in Subscription entity
- **Payment Integration**: Integration with FinancialPayment service

### Technical Implementation

#### 2.1 Domain Layer Enhancements

**Enhanced Subscription Entity**:

```csharp
// Add to existing Subscription entity
public AutoRenewalPreferences AutoRenewalPreferences { get; private set; }
public DateTime? NextRenewalAttemptAt { get; private set; }
public int RenewalFailureCount { get; private set; }
public List<RenewalAttempt> RenewalAttempts { get; private set; } = new();

public void UpdateAutoRenewalPreferences(AutoRenewalPreferences preferences)
{
    AutoRenewalPreferences = preferences;
    AutoRenew = preferences.IsEnabled;

    if (preferences.IsEnabled)
    {
        NextRenewalAttemptAt = CalculateNextRenewalAttempt();
        RenewalFailureCount = 0;
    }
    else
    {
        NextRenewalAttemptAt = null;
    }

    UpdatedAt = DateTime.UtcNow;
}

private DateTime CalculateNextRenewalAttempt()
{
    var daysBeforeExpiry = AutoRenewalPreferences?.DaysBeforeExpiry ?? 7;
    return NextBillingDate.AddDays(-daysBeforeExpiry);
}
```

**New Value Objects**:

```csharp
// Services/SubscriptionManagement/SubscriptionManagement.Domain/ValueObjects/AutoRenewalPreferences.cs
public class AutoRenewalPreferences : ValueObject
{
    public bool IsEnabled { get; private set; }
    public int DaysBeforeExpiry { get; private set; }
    public int MaxRetryAttempts { get; private set; }
    public bool NotifyBeforeRenewal { get; private set; }
    public bool NotifyAfterRenewal { get; private set; }
    public bool NotifyOnFailure { get; private set; }
    public List<string> NotificationChannels { get; private set; }

    public AutoRenewalPreferences(
        bool isEnabled,
        int daysBeforeExpiry = 7,
        int maxRetryAttempts = 3,
        bool notifyBeforeRenewal = true,
        bool notifyAfterRenewal = true,
        bool notifyOnFailure = true,
        List<string>? notificationChannels = null)
    {
        IsEnabled = isEnabled;
        DaysBeforeExpiry = Math.Max(1, Math.Min(daysBeforeExpiry, 30)); // 1-30 days
        MaxRetryAttempts = Math.Max(1, Math.Min(maxRetryAttempts, 5)); // 1-5 attempts
        NotifyBeforeRenewal = notifyBeforeRenewal;
        NotifyAfterRenewal = notifyAfterRenewal;
        NotifyOnFailure = notifyOnFailure;
        NotificationChannels = notificationChannels ?? new List<string> { "email" };
    }
}

public class RenewalAttempt : BaseEntity
{
    public Guid SubscriptionId { get; private set; }
    public DateTime AttemptedAt { get; private set; }
    public RenewalAttemptStatus Status { get; private set; }
    public string? FailureReason { get; private set; }
    public decimal AttemptedAmount { get; private set; }
    public string? PaymentReference { get; private set; }
    public DateTime? NextRetryAt { get; private set; }
}

public enum RenewalAttemptStatus
{
    Pending,
    Processing,
    Successful,
    Failed,
    Cancelled
}
```

#### 2.2 Application Layer Implementation

**Commands**:

```csharp
// Services/SubscriptionManagement/SubscriptionManagement.Application/Commands/UpdateAutoRenewalPreferences/UpdateAutoRenewalPreferencesCommand.cs
public class UpdateAutoRenewalPreferencesCommand : IRequest<bool>
{
    public Guid SubscriptionId { get; set; }
    public Guid UserId { get; set; }
    public bool IsEnabled { get; set; }
    public int DaysBeforeExpiry { get; set; } = 7;
    public int MaxRetryAttempts { get; set; } = 3;
    public bool NotifyBeforeRenewal { get; set; } = true;
    public bool NotifyAfterRenewal { get; set; } = true;
    public bool NotifyOnFailure { get; set; } = true;
    public List<string> NotificationChannels { get; set; } = new();
}

// Handler implementation
public class UpdateAutoRenewalPreferencesCommandHandler : IRequestHandler<UpdateAutoRenewalPreferencesCommand, bool>
{
    private readonly ISubscriptionRepository _repository;
    private readonly IMessageBroker _messageBroker;

    public async Task<bool> Handle(UpdateAutoRenewalPreferencesCommand request, CancellationToken cancellationToken)
    {
        var subscription = await _repository.GetByIdAsync(request.SubscriptionId, cancellationToken);

        if (subscription == null)
            throw new NotFoundException($"Subscription {request.SubscriptionId} not found");

        if (subscription.UserId != request.UserId)
            throw new UnauthorizedAccessException("User not authorized to modify this subscription");

        var preferences = new AutoRenewalPreferences(
            request.IsEnabled,
            request.DaysBeforeExpiry,
            request.MaxRetryAttempts,
            request.NotifyBeforeRenewal,
            request.NotifyAfterRenewal,
            request.NotifyOnFailure,
            request.NotificationChannels);

        subscription.UpdateAutoRenewalPreferences(preferences);

        await _repository.UpdateAsync(subscription, cancellationToken);

        // Publish event for background processing
        await _messageBroker.PublishAsync("subscription.auto_renewal.preferences_updated", new
        {
            SubscriptionId = subscription.Id,
            UserId = subscription.UserId,
            IsEnabled = preferences.IsEnabled,
            NextRenewalAttemptAt = subscription.NextRenewalAttemptAt,
            UpdatedAt = DateTime.UtcNow
        }, cancellationToken);

        return true;
    }
}
```

#### 2.3 Background Service Implementation

**Renewal Processing Service**:

```csharp
// Services/SubscriptionManagement/SubscriptionManagement.Infrastructure/Services/SubscriptionRenewalService.cs
public class SubscriptionRenewalService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<SubscriptionRenewalService> _logger;
    private readonly TimeSpan _processingInterval = TimeSpan.FromHours(1);

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await ProcessPendingRenewalsAsync(stoppingToken);
                await Task.Delay(_processingInterval, stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing subscription renewals");
                await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken);
            }
        }
    }

    private async Task ProcessPendingRenewalsAsync(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var subscriptionRepository = scope.ServiceProvider.GetRequiredService<ISubscriptionRepository>();
        var paymentService = scope.ServiceProvider.GetRequiredService<IPaymentService>();
        var messageBroker = scope.ServiceProvider.GetRequiredService<IMessageBroker>();

        var pendingRenewals = await subscriptionRepository.GetPendingRenewalsAsync(DateTime.UtcNow, cancellationToken);

        foreach (var subscription in pendingRenewals)
        {
            try
            {
                await ProcessSubscriptionRenewalAsync(subscription, paymentService, messageBroker, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing renewal for subscription {SubscriptionId}", subscription.Id);
            }
        }
    }
}
```

## 3. Broker Markup Editing System

### Overview

Implement broker markup editing functionality in OrderManagement service with validation, subscription tier-based limits, and real-time calculation.

### Current Foundation Analysis

- **Existing Infrastructure**: RfqBid entity with pricing structure
- **Quote Management**: Existing quote submission and management workflow
- **Subscription Integration**: Connection to SubscriptionManagement service for tier validation

### Technical Implementation

#### 3.1 Domain Layer Enhancements

**Enhanced RfqBid Entity**:

```csharp
// Add to existing RfqBid entity
public BrokerMarkup? BrokerMarkup { get; private set; }
public decimal OriginalQuoteAmount { get; private set; }
public decimal FinalQuoteAmount { get; private set; }
public bool IsMarkupEditable { get; private set; }
public DateTime? MarkupLastModified { get; private set; }
public Guid? MarkupModifiedBy { get; private set; }

public void ApplyBrokerMarkup(BrokerMarkup markup, Guid modifiedBy, decimal subscriptionTierLimit)
{
    if (!IsMarkupEditable)
        throw new OrderManagementDomainException("Markup editing is not allowed for this bid");

    if (markup.MarkupPercentage > subscriptionTierLimit)
        throw new OrderManagementDomainException($"Markup percentage {markup.MarkupPercentage}% exceeds subscription tier limit of {subscriptionTierLimit}%");

    BrokerMarkup = markup;
    OriginalQuoteAmount = QuotedAmount.Amount;

    // Calculate final amount with markup
    var markupAmount = OriginalQuoteAmount * (markup.MarkupPercentage / 100);
    FinalQuoteAmount = OriginalQuoteAmount + markupAmount;

    // Update the quoted amount
    QuotedAmount = new Money(FinalQuoteAmount, QuotedAmount.Currency);

    MarkupLastModified = DateTime.UtcNow;
    MarkupModifiedBy = modifiedBy;
    UpdatedAt = DateTime.UtcNow;
}
```

**New Value Objects**:

```csharp
// Services/OrderManagement/OrderManagement.Domain/ValueObjects/BrokerMarkup.cs
public class BrokerMarkup : ValueObject
{
    public decimal MarkupPercentage { get; private set; }
    public decimal MarkupAmount { get; private set; }
    public MarkupCalculationMethod CalculationMethod { get; private set; }
    public string? Justification { get; private set; }
    public bool IsAutoCalculated { get; private set; }
    public Dictionary<string, object> CalculationParameters { get; private set; }

    public BrokerMarkup(
        decimal markupPercentage,
        decimal baseAmount,
        MarkupCalculationMethod calculationMethod = MarkupCalculationMethod.Percentage,
        string? justification = null,
        bool isAutoCalculated = false,
        Dictionary<string, object>? calculationParameters = null)
    {
        if (markupPercentage < 0 || markupPercentage > 100)
            throw new ArgumentException("Markup percentage must be between 0 and 100");

        MarkupPercentage = markupPercentage;
        MarkupAmount = baseAmount * (markupPercentage / 100);
        CalculationMethod = calculationMethod;
        Justification = justification;
        IsAutoCalculated = isAutoCalculated;
        CalculationParameters = calculationParameters ?? new Dictionary<string, object>();
    }
}

public enum MarkupCalculationMethod
{
    Percentage,
    FixedAmount,
    Tiered,
    Dynamic
}
```

#### 3.2 Application Layer Implementation

**Commands**:

```csharp
// Services/OrderManagement/OrderManagement.Application/Commands/UpdateBrokerMarkup/UpdateBrokerMarkupCommand.cs
public class UpdateBrokerMarkupCommand : IRequest<UpdateBrokerMarkupResponse>
{
    public Guid BidId { get; set; }
    public Guid BrokerId { get; set; }
    public decimal MarkupPercentage { get; set; }
    public MarkupCalculationMethod CalculationMethod { get; set; } = MarkupCalculationMethod.Percentage;
    public string? Justification { get; set; }
    public bool PreviewOnly { get; set; } = false;
}

public class UpdateBrokerMarkupResponse
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public decimal OriginalAmount { get; set; }
    public decimal MarkupAmount { get; set; }
    public decimal FinalAmount { get; set; }
    public decimal MarkupPercentage { get; set; }
    public decimal SubscriptionTierLimit { get; set; }
    public bool IsWithinLimit { get; set; }
    public List<string> ValidationWarnings { get; set; } = new();
}

// Handler implementation
public class UpdateBrokerMarkupCommandHandler : IRequestHandler<UpdateBrokerMarkupCommand, UpdateBrokerMarkupResponse>
{
    private readonly IRfqBidRepository _bidRepository;
    private readonly ISubscriptionService _subscriptionService;
    private readonly IMessageBroker _messageBroker;

    public async Task<UpdateBrokerMarkupResponse> Handle(UpdateBrokerMarkupCommand request, CancellationToken cancellationToken)
    {
        var bid = await _bidRepository.GetByIdAsync(request.BidId, cancellationToken);
        if (bid == null)
            throw new NotFoundException($"Bid {request.BidId} not found");

        if (bid.BrokerId != request.BrokerId)
            throw new UnauthorizedAccessException("Broker not authorized to modify this bid");

        // Get subscription tier limits
        var subscriptionTier = await _subscriptionService.GetUserSubscriptionTierAsync(request.BrokerId, cancellationToken);
        var markupLimit = GetMarkupLimitForTier(subscriptionTier);

        var response = new UpdateBrokerMarkupResponse
        {
            OriginalAmount = bid.QuotedAmount.Amount,
            MarkupPercentage = request.MarkupPercentage,
            SubscriptionTierLimit = markupLimit,
            IsWithinLimit = request.MarkupPercentage <= markupLimit
        };

        // Calculate markup amounts
        var markupAmount = response.OriginalAmount * (request.MarkupPercentage / 100);
        response.MarkupAmount = markupAmount;
        response.FinalAmount = response.OriginalAmount + markupAmount;

        // Validation
        var validationWarnings = ValidateMarkup(request, subscriptionTier, response);
        response.ValidationWarnings = validationWarnings;

        if (!request.PreviewOnly && response.IsWithinLimit)
        {
            var markup = new BrokerMarkup(
                request.MarkupPercentage,
                response.OriginalAmount,
                request.CalculationMethod,
                request.Justification);

            bid.ApplyBrokerMarkup(markup, request.BrokerId, markupLimit);
            await _bidRepository.UpdateAsync(bid, cancellationToken);

            // Publish event
            await _messageBroker.PublishAsync("bid.markup.updated", new
            {
                BidId = bid.Id,
                RfqId = bid.RfqId,
                BrokerId = request.BrokerId,
                OriginalAmount = response.OriginalAmount,
                MarkupPercentage = request.MarkupPercentage,
                FinalAmount = response.FinalAmount,
                UpdatedAt = DateTime.UtcNow
            }, cancellationToken);

            response.Success = true;
            response.Message = "Markup updated successfully";
        }
        else if (!response.IsWithinLimit)
        {
            response.Success = false;
            response.Message = $"Markup percentage {request.MarkupPercentage}% exceeds subscription tier limit of {markupLimit}%";
        }
        else
        {
            response.Success = true;
            response.Message = "Preview calculated successfully";
        }

        return response;
    }
}
```

## 4. Auto-generate Invoice at Order Confirmation

### Overview

Implement automatic invoice generation in FinancialPayment service triggered by order confirmation events, leveraging existing TaxAwareInvoice infrastructure.

### Current Foundation Analysis

- **Existing Infrastructure**: TaxAwareInvoice entity and TaxAwareInvoiceService
- **Event System**: Integration events from OrderManagement service
- **Tax Calculation**: Existing tax calculation engine

### Technical Implementation

#### 4.1 Event Handler Implementation

**Order Confirmation Event Handler**:

```csharp
// Services/FinancialPayment/FinancialPayment.Application/EventHandlers/OrderConfirmedEventHandler.cs
public class OrderConfirmedEventHandler : INotificationHandler<OrderConfirmedEvent>
{
    private readonly ITaxAwareInvoiceService _invoiceService;
    private readonly IOrderService _orderService;
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<OrderConfirmedEventHandler> _logger;

    public async Task Handle(OrderConfirmedEvent notification, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Processing auto-invoice generation for order {OrderId}", notification.OrderId);

            // Get order details
            var orderDetails = await _orderService.GetOrderDetailsAsync(notification.OrderId, cancellationToken);

            // Generate invoice
            var invoice = await GenerateInvoiceFromOrderAsync(orderDetails, cancellationToken);

            // Publish invoice generated event
            await _messageBroker.PublishAsync("invoice.auto_generated", new
            {
                InvoiceId = invoice.Id,
                OrderId = notification.OrderId,
                InvoiceNumber = invoice.InvoiceNumber,
                Amount = new { Amount = invoice.TotalAmount.Amount, Currency = invoice.TotalAmount.Currency },
                GeneratedAt = DateTime.UtcNow,
                DueDate = invoice.DueDate
            }, cancellationToken);

            _logger.LogInformation("Successfully generated invoice {InvoiceId} for order {OrderId}",
                invoice.Id, notification.OrderId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate invoice for order {OrderId}", notification.OrderId);

            // Publish failure event for manual processing
            await _messageBroker.PublishAsync("invoice.auto_generation.failed", new
            {
                OrderId = notification.OrderId,
                Error = ex.Message,
                FailedAt = DateTime.UtcNow
            }, cancellationToken);
        }
    }

    private async Task<TaxAwareInvoice> GenerateInvoiceFromOrderAsync(OrderDetailsDto orderDetails, CancellationToken cancellationToken)
    {
        // Create line items from order
        var lineItems = CreateLineItemsFromOrder(orderDetails);

        // Determine service category and jurisdiction
        var serviceCategory = DetermineServiceCategory(orderDetails);
        var jurisdiction = DetermineJurisdiction(orderDetails);

        // Generate invoice
        var invoice = await _invoiceService.GenerateInvoiceWithTaxAsync(
            orderDetails.Id,
            new Money(orderDetails.TotalAmount, orderDetails.Currency),
            orderDetails.CustomerDetails,
            orderDetails.BillingDetails,
            serviceCategory,
            jurisdiction,
            EntityType.Business, // Default for logistics
            lineItems,
            tdsSection: TdsSection.Section194C, // Transport services
            hsnCode: "996511", // Goods transport by road
            hasPan: !string.IsNullOrEmpty(orderDetails.CustomerDetails.PanNumber),
            notes: $"Auto-generated invoice for order {orderDetails.OrderNumber}"
        );

        return invoice;
    }
}
```

#### 4.2 Enhanced Invoice Service

**Auto-Generation Service**:

```csharp
// Services/FinancialPayment/FinancialPayment.Application/Services/AutoInvoiceGenerationService.cs
public interface IAutoInvoiceGenerationService
{
    Task<TaxAwareInvoice> GenerateInvoiceFromOrderAsync(Guid orderId, CancellationToken cancellationToken = default);
    Task<List<TaxAwareInvoice>> ProcessPendingInvoiceGenerationAsync(CancellationToken cancellationToken = default);
    Task<bool> ValidateOrderForInvoiceGenerationAsync(Guid orderId, CancellationToken cancellationToken = default);
}

public class AutoInvoiceGenerationService : IAutoInvoiceGenerationService
{
    private readonly ITaxAwareInvoiceService _invoiceService;
    private readonly IOrderService _orderService;
    private readonly IInvoiceRepository _invoiceRepository;
    private readonly IInvoiceNumberGenerator _numberGenerator;

    public async Task<TaxAwareInvoice> GenerateInvoiceFromOrderAsync(Guid orderId, CancellationToken cancellationToken = default)
    {
        // Validate order is ready for invoice generation
        var isValid = await ValidateOrderForInvoiceGenerationAsync(orderId, cancellationToken);
        if (!isValid)
            throw new InvalidOperationException($"Order {orderId} is not ready for invoice generation");

        // Check if invoice already exists
        var existingInvoice = await _invoiceRepository.GetByOrderIdAsync(orderId, cancellationToken);
        if (existingInvoice != null)
            throw new InvalidOperationException($"Invoice already exists for order {orderId}");

        var orderDetails = await _orderService.GetOrderDetailsAsync(orderId, cancellationToken);

        // Generate unique invoice number
        var invoiceNumber = await _numberGenerator.GenerateInvoiceNumberAsync(cancellationToken);

        // Create invoice with auto-generation metadata
        var invoice = await _invoiceService.GenerateInvoiceWithTaxAsync(
            orderId,
            new Money(orderDetails.TotalAmount, orderDetails.Currency),
            orderDetails.CustomerDetails,
            orderDetails.BillingDetails,
            ServiceCategory.Transportation,
            DetermineJurisdiction(orderDetails),
            EntityType.Business,
            CreateLineItemsFromOrder(orderDetails),
            tdsSection: TdsSection.Section194C,
            hsnCode: "996511",
            hasPan: !string.IsNullOrEmpty(orderDetails.CustomerDetails.PanNumber),
            notes: $"Auto-generated invoice for order {orderDetails.OrderNumber}"
        );

        // Mark as auto-generated
        invoice.AddMetadata("auto_generated", true);
        invoice.AddMetadata("generation_timestamp", DateTime.UtcNow);
        invoice.AddMetadata("order_confirmation_date", orderDetails.ConfirmedAt);

        return invoice;
    }
}
```

This technical specification provides comprehensive implementation details for all four high-priority features, leveraging existing infrastructure while adding the necessary enhancements for broker operations.
