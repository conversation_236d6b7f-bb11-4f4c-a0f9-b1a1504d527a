namespace CommunicationNotification.Domain.Enums;

public enum NotificationChannel
{
    /// <summary>
    /// SMS text message
    /// </summary>
    Sms = 1,
    
    /// <summary>
    /// Email notification
    /// </summary>
    Email = 2,
    
    /// <summary>
    /// Push notification for mobile apps
    /// </summary>
    Push = 3,
    
    /// <summary>
    /// In-app notification
    /// </summary>
    InApp = 4,
    
    /// <summary>
    /// Real-time chat message
    /// </summary>
    Chat = 5,
    
    /// <summary>
    /// Voice call or voice message
    /// </summary>
    Voice = 6,
    
    /// <summary>
    /// WhatsApp message (future implementation)
    /// </summary>
    WhatsApp = 7
}
