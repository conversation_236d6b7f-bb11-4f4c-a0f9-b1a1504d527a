using DataStorage.Domain.Enums;

namespace DataStorage.Domain.ValueObjects;

public class DisasterRecoveryPlan
{
    public Guid Id { get; private set; }
    public string Name { get; private set; }
    public string Description { get; private set; }
    public DisasterRecoveryType Type { get; private set; }
    public List<DisasterRecoveryStep> Steps { get; private set; }
    public TimeSpan EstimatedRecoveryTime { get; private set; }
    public TimeSpan RecoveryPointObjective { get; private set; }
    public Guid CreatedBy { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime LastTested { get; private set; }
    public DisasterRecoveryPlanStatus Status { get; private set; }

    public DisasterRecoveryPlan(
        Guid id,
        string name,
        string description,
        DisasterRecoveryType type,
        List<DisasterRecoveryStep> steps,
        TimeSpan estimatedRecoveryTime,
        TimeSpan recoveryPointObjective,
        Guid createdBy,
        DateTime createdAt,
        DateTime lastTested,
        DisasterRecoveryPlanStatus status)
    {
        Id = id;
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        Type = type;
        Steps = steps ?? new List<DisasterRecoveryStep>();
        EstimatedRecoveryTime = estimatedRecoveryTime;
        RecoveryPointObjective = recoveryPointObjective;
        CreatedBy = createdBy;
        CreatedAt = createdAt;
        LastTested = lastTested;
        Status = status;
    }
}

public class DisasterRecoveryPlanRequest
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public DisasterRecoveryType Type { get; private set; }
    public List<DisasterRecoveryStep> Steps { get; private set; }
    public TimeSpan EstimatedRecoveryTime { get; private set; }
    public TimeSpan RecoveryPointObjective { get; private set; }
    public Guid CreatedBy { get; private set; }

    public DisasterRecoveryPlanRequest(
        string name,
        string description,
        DisasterRecoveryType type,
        List<DisasterRecoveryStep> steps,
        TimeSpan estimatedRecoveryTime,
        TimeSpan recoveryPointObjective,
        Guid createdBy)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        Type = type;
        Steps = steps ?? new List<DisasterRecoveryStep>();
        EstimatedRecoveryTime = estimatedRecoveryTime;
        RecoveryPointObjective = recoveryPointObjective;
        CreatedBy = createdBy;
    }
}

public class DisasterRecoveryStep
{
    public int Order { get; private set; }
    public string Name { get; private set; }
    public string Description { get; private set; }
    public DisasterRecoveryStepType Type { get; private set; }
    public TimeSpan EstimatedDuration { get; private set; }
    public bool IsAutomated { get; private set; }
    public Dictionary<string, object> Parameters { get; private set; }

    public DisasterRecoveryStep(
        int order,
        string name,
        string description,
        DisasterRecoveryStepType type,
        TimeSpan estimatedDuration,
        bool isAutomated,
        Dictionary<string, object>? parameters = null)
    {
        Order = order;
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        Type = type;
        EstimatedDuration = estimatedDuration;
        IsAutomated = isAutomated;
        Parameters = parameters ?? new Dictionary<string, object>();
    }
}

public class DisasterRecoveryTestResult
{
    public Guid TestId { get; private set; }
    public Guid PlanId { get; private set; }
    public DateTime StartedAt { get; private set; }
    public DateTime? CompletedAt { get; private set; }
    public DisasterRecoveryTestStatus Status { get; private set; }
    public TimeSpan ActualRecoveryTime { get; private set; }
    public List<DisasterRecoveryStepResult> StepResults { get; private set; }
    public string? ErrorMessage { get; private set; }
    public List<string> Recommendations { get; private set; }

    public DisasterRecoveryTestResult(
        Guid testId,
        Guid planId,
        DateTime startedAt,
        DisasterRecoveryTestStatus status,
        TimeSpan actualRecoveryTime,
        List<DisasterRecoveryStepResult> stepResults,
        DateTime? completedAt = null,
        string? errorMessage = null,
        List<string>? recommendations = null)
    {
        TestId = testId;
        PlanId = planId;
        StartedAt = startedAt;
        CompletedAt = completedAt;
        Status = status;
        ActualRecoveryTime = actualRecoveryTime;
        StepResults = stepResults ?? new List<DisasterRecoveryStepResult>();
        ErrorMessage = errorMessage;
        Recommendations = recommendations ?? new List<string>();
    }
}

public class DisasterRecoveryTestRequest
{
    public Guid PlanId { get; private set; }
    public Guid RequestedBy { get; private set; }
    public bool IsFullTest { get; private set; }
    public List<int> StepsToTest { get; private set; }
    public string? Notes { get; private set; }

    public DisasterRecoveryTestRequest(
        Guid planId,
        Guid requestedBy,
        bool isFullTest = true,
        List<int>? stepsToTest = null,
        string? notes = null)
    {
        PlanId = planId;
        RequestedBy = requestedBy;
        IsFullTest = isFullTest;
        StepsToTest = stepsToTest ?? new List<int>();
        Notes = notes;
    }
}

public class DisasterRecoveryStepResult
{
    public int StepOrder { get; private set; }
    public string StepName { get; private set; }
    public DisasterRecoveryStepStatus Status { get; private set; }
    public DateTime StartedAt { get; private set; }
    public DateTime? CompletedAt { get; private set; }
    public TimeSpan Duration { get; private set; }
    public string? ErrorMessage { get; private set; }
    public string? Notes { get; private set; }

    public DisasterRecoveryStepResult(
        int stepOrder,
        string stepName,
        DisasterRecoveryStepStatus status,
        DateTime startedAt,
        TimeSpan duration,
        DateTime? completedAt = null,
        string? errorMessage = null,
        string? notes = null)
    {
        StepOrder = stepOrder;
        StepName = stepName ?? throw new ArgumentNullException(nameof(stepName));
        Status = status;
        StartedAt = startedAt;
        CompletedAt = completedAt;
        Duration = duration;
        ErrorMessage = errorMessage;
        Notes = notes;
    }
}

public class DisasterRecoveryExecutionResult
{
    public bool IsSuccessful { get; private set; }
    public string? ErrorMessage { get; private set; }
    public Guid ExecutionId { get; private set; }
    public Guid PlanId { get; private set; }
    public DateTime StartedAt { get; private set; }
    public DateTime? CompletedAt { get; private set; }
    public TimeSpan TotalDuration { get; private set; }
    public List<DisasterRecoveryStepResult> StepResults { get; private set; }
    public DisasterRecoveryExecutionStatistics Statistics { get; private set; }

    private DisasterRecoveryExecutionResult()
    {
        StepResults = new List<DisasterRecoveryStepResult>();
        Statistics = new DisasterRecoveryExecutionStatistics();
    }

    public static DisasterRecoveryExecutionResult Success(
        Guid executionId,
        Guid planId,
        DateTime startedAt,
        DateTime completedAt,
        List<DisasterRecoveryStepResult> stepResults,
        DisasterRecoveryExecutionStatistics statistics)
    {
        return new DisasterRecoveryExecutionResult
        {
            IsSuccessful = true,
            ExecutionId = executionId,
            PlanId = planId,
            StartedAt = startedAt,
            CompletedAt = completedAt,
            TotalDuration = completedAt - startedAt,
            StepResults = stepResults ?? new List<DisasterRecoveryStepResult>(),
            Statistics = statistics ?? new DisasterRecoveryExecutionStatistics()
        };
    }

    public static DisasterRecoveryExecutionResult Failure(
        Guid executionId,
        Guid planId,
        string errorMessage,
        DateTime startedAt,
        List<DisasterRecoveryStepResult> stepResults)
    {
        return new DisasterRecoveryExecutionResult
        {
            IsSuccessful = false,
            ExecutionId = executionId,
            PlanId = planId,
            ErrorMessage = errorMessage,
            StartedAt = startedAt,
            StepResults = stepResults ?? new List<DisasterRecoveryStepResult>(),
            Statistics = new DisasterRecoveryExecutionStatistics()
        };
    }
}

public class DisasterRecoveryExecutionRequest
{
    public Guid PlanId { get; private set; }
    public Guid ExecutedBy { get; private set; }
    public DisasterRecoveryTrigger Trigger { get; private set; }
    public string? Reason { get; private set; }
    public bool IsTestExecution { get; private set; }

    public DisasterRecoveryExecutionRequest(
        Guid planId,
        Guid executedBy,
        DisasterRecoveryTrigger trigger,
        string? reason = null,
        bool isTestExecution = false)
    {
        PlanId = planId;
        ExecutedBy = executedBy;
        Trigger = trigger;
        Reason = reason;
        IsTestExecution = isTestExecution;
    }
}

public class DisasterRecoveryExecutionStatistics
{
    public int TotalSteps { get; private set; }
    public int CompletedSteps { get; private set; }
    public int FailedSteps { get; private set; }
    public int SkippedSteps { get; private set; }
    public long DataRecovered { get; private set; }
    public int FilesRecovered { get; private set; }

    public DisasterRecoveryExecutionStatistics(
        int totalSteps = 0,
        int completedSteps = 0,
        int failedSteps = 0,
        int skippedSteps = 0,
        long dataRecovered = 0,
        int filesRecovered = 0)
    {
        TotalSteps = totalSteps;
        CompletedSteps = completedSteps;
        FailedSteps = failedSteps;
        SkippedSteps = skippedSteps;
        DataRecovered = dataRecovered;
        FilesRecovered = filesRecovered;
    }
}

public class RecoveryValidationResult
{
    public bool IsValid { get; private set; }
    public string? ErrorMessage { get; private set; }
    public Guid ValidationId { get; private set; }
    public DateTime ValidatedAt { get; private set; }
    public List<RecoveryValidationCheck> Checks { get; private set; }
    public RecoveryValidationSummary Summary { get; private set; }

    private RecoveryValidationResult()
    {
        Checks = new List<RecoveryValidationCheck>();
        Summary = new RecoveryValidationSummary();
    }

    public static RecoveryValidationResult Success(
        Guid validationId,
        DateTime validatedAt,
        List<RecoveryValidationCheck> checks,
        RecoveryValidationSummary summary)
    {
        return new RecoveryValidationResult
        {
            IsValid = true,
            ValidationId = validationId,
            ValidatedAt = validatedAt,
            Checks = checks ?? new List<RecoveryValidationCheck>(),
            Summary = summary ?? new RecoveryValidationSummary()
        };
    }

    public static RecoveryValidationResult Failure(
        Guid validationId,
        string errorMessage,
        DateTime validatedAt,
        List<RecoveryValidationCheck> checks)
    {
        return new RecoveryValidationResult
        {
            IsValid = false,
            ValidationId = validationId,
            ErrorMessage = errorMessage,
            ValidatedAt = validatedAt,
            Checks = checks ?? new List<RecoveryValidationCheck>(),
            Summary = new RecoveryValidationSummary()
        };
    }
}

public class RecoveryValidationRequest
{
    public Guid BackupId { get; private set; }
    public List<RecoveryValidationType> ValidationTypes { get; private set; }
    public Guid RequestedBy { get; private set; }

    public RecoveryValidationRequest(
        Guid backupId,
        List<RecoveryValidationType> validationTypes,
        Guid requestedBy)
    {
        BackupId = backupId;
        ValidationTypes = validationTypes ?? new List<RecoveryValidationType>();
        RequestedBy = requestedBy;
    }
}

public class RecoveryValidationCheck
{
    public RecoveryValidationType Type { get; private set; }
    public string Name { get; private set; }
    public RecoveryValidationStatus Status { get; private set; }
    public string? ErrorMessage { get; private set; }
    public Dictionary<string, object> Results { get; private set; }

    public RecoveryValidationCheck(
        RecoveryValidationType type,
        string name,
        RecoveryValidationStatus status,
        string? errorMessage = null,
        Dictionary<string, object>? results = null)
    {
        Type = type;
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Status = status;
        ErrorMessage = errorMessage;
        Results = results ?? new Dictionary<string, object>();
    }
}

public class RecoveryValidationSummary
{
    public int TotalChecks { get; private set; }
    public int PassedChecks { get; private set; }
    public int FailedChecks { get; private set; }
    public int WarningChecks { get; private set; }
    public double SuccessRate { get; private set; }

    public RecoveryValidationSummary(
        int totalChecks = 0,
        int passedChecks = 0,
        int failedChecks = 0,
        int warningChecks = 0)
    {
        TotalChecks = totalChecks;
        PassedChecks = passedChecks;
        FailedChecks = failedChecks;
        WarningChecks = warningChecks;
        SuccessRate = totalChecks > 0 ? (double)passedChecks / totalChecks * 100 : 0;
    }
}
