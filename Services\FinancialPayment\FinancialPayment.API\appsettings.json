{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=tli_financial_payment;User Id=timescale;Password=timescale"}, "JwtSettings": {"Secret": "YourSuperSecretKeyThatIsAtLeast32CharactersLong!", "Issuer": "TLI.FinancialPayment", "Audience": "TLI.FinancialPayment.Users", "ExpiryInMinutes": 60}, "PaymentSettings": {"DefaultGateway": "razorpay", "EnableMultipleGateways": true, "GatewayFailoverEnabled": true}, "TaxConfiguration": {"DefaultJurisdiction": "IN", "DefaultCurrency": "INR", "EnableTaxCalculation": true, "EnableAutomaticTaxApplication": true, "TaxCalculationTimeoutSeconds": 30, "SupportedJurisdictions": [{"Code": "IN", "Name": "India", "Type": "Country", "IsActive": true, "SupportedTaxTypes": ["GST", "IGST", "CGST", "SGST"]}, {"Code": "US", "Name": "United States", "Type": "Country", "IsActive": true, "SupportedTaxTypes": ["Sales Tax", "Use Tax"]}]}, "ReconciliationConfiguration": {"EnableAutomaticReconciliation": true, "ReconciliationIntervalHours": 24, "DefaultToleranceAmount": 0.01, "DefaultToleranceHours": 24, "RequireManualReviewForVariances": true, "VarianceThresholdForReview": 100.0, "EnabledGateways": ["razorpay", "stripe", "paypal", "square"], "GatewaySettings": {"razorpay": {"GatewayName": "razorpay", "IsEnabled": true, "ToleranceAmount": 0.01, "ToleranceHours": 24, "ApiEndpoint": "https://api.razorpay.com/v1/", "MaxRetryAttempts": 3, "RetryDelaySeconds": 30}, "stripe": {"GatewayName": "stripe", "IsEnabled": true, "ToleranceAmount": 0.01, "ToleranceHours": 24, "ApiEndpoint": "https://api.stripe.com/v1/", "MaxRetryAttempts": 3, "RetryDelaySeconds": 30}}}, "FraudDetectionConfiguration": {"EnableFraudDetection": true, "EnableRealTimeAssessment": true, "EnablePatternAnalysis": true, "EnableBlacklist": true, "DefaultRiskThreshold": 60, "VelocityCheckWindowMinutes": 60, "MaxTransactionsPerWindow": 5, "MaxAmountPerWindow": 10000, "EnableGeolocationCheck": true, "EnableDeviceFingerprinting": true, "TrustedIpRanges": ["***********/16", "10.0.0.0/8"], "HighRiskCountries": ["XX", "YY", "ZZ"], "PaymentMethodRiskScores": {"card": 10, "netbanking": 5, "wallet": 15, "upi": 5, "crypto": 50}}, "AnalyticsConfiguration": {"EnableRealTimeAnalytics": true, "MetricsRetentionDays": 365, "ReportRetentionDays": 90, "MaxReportsPerUser": 50, "MaxDashboardsPerUser": 10, "MaxWidgetsPerDashboard": 20, "AllowedReportFormats": ["excel", "pdf", "csv"], "AllowedChartTypes": ["line", "bar", "pie", "doughnut"], "DefaultColors": {"primary": "#007bff", "secondary": "#6c757d", "success": "#28a745", "danger": "#dc3545", "warning": "#ffc107", "info": "#17a2b8"}, "ReportsStoragePath": "reports"}, "SubscriptionConfiguration": {"EnableAutomaticBilling": true, "BillingRetryAttempts": 3, "BillingRetryIntervalHours": 24, "TrialEndingNotificationDays": 3, "PaymentFailureGracePeriodDays": 7, "EnableProration": true, "EnableDowngrades": true, "EnableUpgrades": true, "DefaultCurrency": "INR", "SupportedCurrencies": ["INR", "USD", "EUR"], "NotificationTemplates": {"billing_success": "Your subscription billing was successful", "billing_failure": "Your subscription billing failed", "trial_ending": "Your trial is ending soon", "subscription_cancelled": "Your subscription has been cancelled", "payment_method_update_required": "Please update your payment method"}}, "FinancialReportConfiguration": {"EnableScheduledReports": true, "MaxReportsPerUser": 100, "ReportRetentionDays": 365, "SupportedFormats": ["excel", "pdf", "csv"], "SupportedReportTypes": ["financial_summary", "profit_loss", "cash_flow", "revenue_analysis", "expense_analysis", "tax_summary", "commission_report", "reconciliation_report"], "ReportsStoragePath": "financial_reports", "DefaultTemplates": {"financial_summary": "default_financial_summary", "profit_loss": "default_profit_loss", "cash_flow": "default_cash_flow"}, "EnableRealTimeReporting": true, "MaxConcurrentReportGeneration": 5}, "FeatureFlagConfiguration": {"EnableCaching": true, "CacheExpiryMinutes": 15, "EnableAnalytics": true, "EnableAuditLogging": true, "DefaultEnvironment": "production", "SupportedEnvironments": ["development", "staging", "production"], "MaxRulesPerFlag": 10, "MaxVariantsPerFlag": 5, "EnableRealTimeUpdates": true, "DefaultValues": {"boolean": false, "string": "", "number": 0}}, "MonitoringConfiguration": {"EnableMetricsCollection": true, "EnableUsageAnalytics": true, "EnableHealthChecks": true, "EnableAlerting": true, "MetricsRetentionDays": 90, "UsageAnalyticsRetentionDays": 365, "HealthCheckIntervalSeconds": 30, "AlertEvaluationIntervalSeconds": 60, "NotificationChannels": ["email", "slack", "webhook"], "NotificationSettings": {"email_smtp_server": "smtp.example.com", "slack_webhook_url": "https://hooks.slack.com/services/...", "webhook_url": "https://api.example.com/webhooks/alerts"}}, "CacheConfiguration": {"EnableCaching": true, "EnableDistributedCaching": true, "ConnectionString": "localhost:6379", "InstanceName": "FinancialPayment", "DefaultExpiryMinutes": 60, "PaymentCacheExpiryMinutes": 30, "SubscriptionCacheExpiryMinutes": 60, "PlanCacheExpiryMinutes": 240, "TaxRulesCacheExpiryMinutes": 1440, "FraudRulesCacheExpiryMinutes": 60, "AnalyticsCacheExpiryMinutes": 15, "FeatureFlagCacheExpiryMinutes": 15, "MetricsCacheExpiryMinutes": 5, "EnableCacheCompression": true, "EnableCacheEncryption": false, "CustomExpiryMinutes": {"user_sessions": 30, "api_responses": 5, "search_results": 10}}, "PerformanceTestConfiguration": {"EnablePerformanceTesting": true, "MaxConcurrentTests": 3, "TestResultRetentionDays": 90, "TestDataDirectory": "performance_test_data", "DefaultEndpoints": {"payments": "/api/payments", "subscriptions": "/api/subscriptions", "analytics": "/api/analytics", "plans": "/api/subscription-plans"}, "DefaultThresholds": {"MaxResponseTime": "00:00:02", "MinSuccessRate": 95.0, "MaxErrorRate": 5, "MaxCpuUsage": 80.0, "MaxMemoryUsage": 80.0, "MaxDatabaseConnections": 100, "MaxDatabaseResponseTime": "00:00:00.500"}, "EnableRealTimeMonitoring": true, "MetricsCollectionIntervalSeconds": 5, "NotificationChannels": ["email", "slack"]}, "RazorPay": {"KeyId": "your_razorpay_key_id", "KeySecret": "your_razorpay_key_secret", "WebhookSecret": "your_razorpay_webhook_secret"}, "Stripe": {"PublishableKey": "pk_test_your_stripe_publishable_key", "SecretKey": "sk_test_your_stripe_secret_key", "WebhookSecret": "whsec_your_stripe_webhook_secret"}, "PayPal": {"ClientId": "your_paypal_client_id", "ClientSecret": "your_paypal_client_secret", "WebhookId": "your_paypal_webhook_id", "IsSandbox": true}, "Square": {"ApplicationId": "your_square_application_id", "AccessToken": "your_square_access_token", "LocationId": "your_square_location_id", "WebhookSignatureKey": "your_square_webhook_signature_key", "IsSandbox": true}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/financial-payment-.txt", "rollingInterval": "Day", "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext"]}}