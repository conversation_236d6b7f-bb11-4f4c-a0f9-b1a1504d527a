using MediatR;

namespace OrderManagement.Application.Commands.SubmitBid;

public class SubmitBidCommand : IRequest<Guid>
{
    public Guid RfqId { get; set; }
    public Guid BrokerId { get; set; }
    public CreateMoneyDto QuotedPrice { get; set; } = new();
    public DateTime EstimatedPickupDate { get; set; }
    public DateTime EstimatedDeliveryDate { get; set; }
    public string? ProposedTerms { get; set; }
    public string? VehicleDetails { get; set; }
    public string? DriverDetails { get; set; }
    public string? AdditionalServices { get; set; }
    public string? Notes { get; set; }
    public bool IsCounterOffer { get; set; }
    public Guid? OriginalBidId { get; set; }
}

public class CreateMoneyDto
{
    public decimal Amount { get; set; }
    public string Currency { get; set; } = "USD";
}
