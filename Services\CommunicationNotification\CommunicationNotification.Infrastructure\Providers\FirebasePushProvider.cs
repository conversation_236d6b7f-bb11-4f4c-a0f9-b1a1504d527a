using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;
using CommunicationNotification.Infrastructure.Configuration;
using FirebaseAdmin;
using FirebaseAdmin.Messaging;
using Google.Apis.Auth.OAuth2;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace CommunicationNotification.Infrastructure.Providers;

/// <summary>
/// Firebase push notification provider implementation
/// </summary>
public class FirebasePushProvider : IPushNotificationProvider
{
    private readonly ILogger<FirebasePushProvider> _logger;
    private readonly FirebaseConfiguration _configuration;
    private readonly FirebaseMessaging _messaging;

    public NotificationChannel Channel => NotificationChannel.Push;

    public FirebasePushProvider(
        IConfiguration configuration,
        ILogger<FirebasePushProvider> logger)
    {
        _logger = logger;
        _configuration = configuration.GetSection("Firebase").Get<FirebaseConfiguration>()
            ?? throw new InvalidOperationException("Firebase configuration is missing");

        InitializeFirebase();
        _messaging = FirebaseMessaging.DefaultInstance;
    }

    public async Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(_configuration.ServiceAccountKeyPath) && 
                string.IsNullOrEmpty(_configuration.ServiceAccountKeyJson))
            {
                return false;
            }

            // Test Firebase connectivity by creating a test message (without sending)
            var testMessage = new Message
            {
                Token = "test-token",
                Notification = new Notification
                {
                    Title = "Test",
                    Body = "Test"
                }
            };

            // Validate message without sending
            await _messaging.SendAsync(testMessage, dryRun: true, cancellationToken);
            return true;
        }
        catch (FirebaseMessagingException ex) when (ex.MessagingErrorCode == MessagingErrorCode.InvalidArgument)
        {
            // Expected error for test token, means Firebase is available
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check Firebase availability");
            return false;
        }
    }

    public async Task<NotificationResult> SendAsync(
        ContactInfo recipient,
        MessageContent content,
        Dictionary<string, string>? metadata = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (!ValidateRecipient(recipient))
            {
                return NotificationResult.Failure("Invalid push token");
            }

            var pushRequest = new PushNotificationRequest
            {
                DeviceToken = recipient.PushToken!,
                Title = content.Subject,
                Body = content.Body,
                Data = metadata
            };

            var result = await SendPushNotificationAsync(pushRequest, cancellationToken);

            return result.IsSuccess
                ? NotificationResult.Success(result.MessageId!, DeliveryStatus.Sent)
                : NotificationResult.Failure(result.ErrorMessage!);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send push notification to {Token}", recipient.PushToken);
            return NotificationResult.Failure($"Failed to send push notification: {ex.Message}");
        }
    }

    public async Task<DeliveryStatus> GetDeliveryStatusAsync(
        string externalId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Firebase doesn't provide a direct API to get message delivery status
            // This would typically be handled through analytics or custom tracking
            _logger.LogDebug("Getting push notification delivery status for {MessageId}", externalId);
            return DeliveryStatus.Sent;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get push notification delivery status for {MessageId}", externalId);
            return DeliveryStatus.Failed;
        }
    }

    public bool ValidateRecipient(ContactInfo recipient)
    {
        return !string.IsNullOrWhiteSpace(recipient.PushToken);
    }

    public async Task<PushNotificationResult> SendPushNotificationAsync(
        PushNotificationRequest pushRequest,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var message = new Message
            {
                Token = pushRequest.DeviceToken,
                Notification = new Notification
                {
                    Title = pushRequest.Title,
                    Body = pushRequest.Body,
                    ImageUrl = pushRequest.ImageUrl
                },
                Data = pushRequest.Data ?? new Dictionary<string, string>(),
                Android = CreateAndroidConfig(pushRequest),
                Apns = CreateApnsConfig(pushRequest)
            };

            var messageId = await _messaging.SendAsync(message, cancellationToken);

            _logger.LogInformation("Push notification sent successfully to {Token}, MessageId: {MessageId}",
                pushRequest.DeviceToken, messageId);

            return PushNotificationResult.Success(messageId);
        }
        catch (FirebaseMessagingException ex)
        {
            _logger.LogError(ex, "Firebase messaging error while sending push notification to {Token}: {ErrorCode}",
                pushRequest.DeviceToken, ex.MessagingErrorCode);

            return PushNotificationResult.Failure($"Firebase error: {ex.MessagingErrorCode}", ex.ErrorCode);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception occurred while sending push notification to {Token}",
                pushRequest.DeviceToken);
            return PushNotificationResult.Failure($"Exception: {ex.Message}");
        }
    }

    public async Task<MulticastPushResult> SendMulticastPushAsync(
        MulticastPushRequest multicastRequest,
        CancellationToken cancellationToken = default)
    {
        var result = new MulticastPushResult
        {
            TotalDevices = multicastRequest.DeviceTokens.Count
        };

        try
        {
            var message = new MulticastMessage
            {
                Tokens = multicastRequest.DeviceTokens,
                Notification = new Notification
                {
                    Title = multicastRequest.Title,
                    Body = multicastRequest.Body,
                    ImageUrl = multicastRequest.ImageUrl
                },
                Data = multicastRequest.Data ?? new Dictionary<string, string>(),
                Android = CreateAndroidConfig(multicastRequest),
                Apns = CreateApnsConfig(multicastRequest)
            };

            var batchResponse = await _messaging.SendMulticastAsync(message, cancellationToken);

            result.SuccessfulDevices = batchResponse.SuccessCount;
            result.FailedDevices = batchResponse.FailureCount;

            // Process individual responses
            for (int i = 0; i < batchResponse.Responses.Count; i++)
            {
                var response = batchResponse.Responses[i];
                var token = multicastRequest.DeviceTokens[i];

                if (response.IsSuccess)
                {
                    result.Results.Add(PushNotificationResult.Success(response.MessageId));
                }
                else
                {
                    result.Results.Add(PushNotificationResult.Failure(
                        response.Exception?.Message ?? "Unknown error",
                        response.Exception?.ErrorCode));

                    // Track invalid tokens
                    if (IsInvalidToken(response.Exception))
                    {
                        result.InvalidTokens ??= new List<string>();
                        result.InvalidTokens.Add(token);
                    }
                }
            }

            _logger.LogInformation("Multicast push notification completed. Total: {Total}, Success: {Success}, Failed: {Failed}",
                result.TotalDevices, result.SuccessfulDevices, result.FailedDevices);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception occurred while sending multicast push notification");
            
            result.FailedDevices = result.TotalDevices;
            result.SuccessfulDevices = 0;

            // Create failed results for all tokens
            foreach (var token in multicastRequest.DeviceTokens)
            {
                result.Results.Add(PushNotificationResult.Failure($"Exception: {ex.Message}"));
            }
        }

        return result;
    }

    public async Task<TopicPushResult> SendTopicPushAsync(
        TopicPushRequest topicRequest,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var message = new Message
            {
                Topic = topicRequest.Topic,
                Condition = topicRequest.Condition,
                Notification = new Notification
                {
                    Title = topicRequest.Title,
                    Body = topicRequest.Body,
                    ImageUrl = topicRequest.ImageUrl
                },
                Data = topicRequest.Data ?? new Dictionary<string, string>(),
                Android = CreateAndroidConfig(topicRequest),
                Apns = CreateApnsConfig(topicRequest)
            };

            var messageId = await _messaging.SendAsync(message, cancellationToken);

            _logger.LogInformation("Topic push notification sent successfully to {Topic}, MessageId: {MessageId}",
                topicRequest.Topic, messageId);

            return TopicPushResult.Success(messageId);
        }
        catch (FirebaseMessagingException ex)
        {
            _logger.LogError(ex, "Firebase messaging error while sending topic push notification to {Topic}: {ErrorCode}",
                topicRequest.Topic, ex.MessagingErrorCode);

            return TopicPushResult.Failure($"Firebase error: {ex.MessagingErrorCode}", ex.ErrorCode);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception occurred while sending topic push notification to {Topic}",
                topicRequest.Topic);
            return TopicPushResult.Failure($"Exception: {ex.Message}");
        }
    }

    public async Task<bool> SubscribeToTopicAsync(
        string deviceToken,
        string topic,
        CancellationToken cancellationToken = default)
    {
        try
        {
            await _messaging.SubscribeToTopicAsync(new[] { deviceToken }, topic, cancellationToken);
            
            _logger.LogInformation("Device {Token} subscribed to topic {Topic}", deviceToken, topic);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to subscribe device {Token} to topic {Topic}", deviceToken, topic);
            return false;
        }
    }

    public async Task<bool> UnsubscribeFromTopicAsync(
        string deviceToken,
        string topic,
        CancellationToken cancellationToken = default)
    {
        try
        {
            await _messaging.UnsubscribeFromTopicAsync(new[] { deviceToken }, topic, cancellationToken);
            
            _logger.LogInformation("Device {Token} unsubscribed from topic {Topic}", deviceToken, topic);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to unsubscribe device {Token} from topic {Topic}", deviceToken, topic);
            return false;
        }
    }

    public async Task<bool> ValidateDeviceTokenAsync(
        string deviceToken,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var testMessage = new Message
            {
                Token = deviceToken,
                Notification = new Notification
                {
                    Title = "Test",
                    Body = "Test"
                }
            };

            // Validate token without sending
            await _messaging.SendAsync(testMessage, dryRun: true, cancellationToken);
            return true;
        }
        catch (FirebaseMessagingException ex) when (
            ex.MessagingErrorCode == MessagingErrorCode.InvalidArgument ||
            ex.MessagingErrorCode == MessagingErrorCode.Unregistered)
        {
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to validate device token {Token}", deviceToken);
            return false;
        }
    }

    private void InitializeFirebase()
    {
        if (FirebaseApp.DefaultInstance == null)
        {
            GoogleCredential credential;

            if (!string.IsNullOrEmpty(_configuration.ServiceAccountKeyPath))
            {
                credential = GoogleCredential.FromFile(_configuration.ServiceAccountKeyPath);
            }
            else if (!string.IsNullOrEmpty(_configuration.ServiceAccountKeyJson))
            {
                credential = GoogleCredential.FromJson(_configuration.ServiceAccountKeyJson);
            }
            else
            {
                throw new InvalidOperationException("Firebase service account key is required");
            }

            FirebaseApp.Create(new AppOptions
            {
                Credential = credential,
                ProjectId = _configuration.ProjectId
            });

            _logger.LogInformation("Firebase initialized for project {ProjectId}", _configuration.ProjectId);
        }
    }

    private AndroidConfig? CreateAndroidConfig(PushNotificationRequest request)
    {
        return new AndroidConfig
        {
            Priority = request.Priority == PushNotificationPriority.High ? Priority.High : Priority.Normal,
            Notification = new AndroidNotification
            {
                Title = request.Title,
                Body = request.Body,
                Icon = "ic_notification",
                Color = "#FF0000",
                Sound = request.Sound ?? "default",
                ClickAction = request.ClickAction
            },
            Data = request.Data
        };
    }

    private AndroidConfig? CreateAndroidConfig(MulticastPushRequest request)
    {
        return new AndroidConfig
        {
            Priority = request.Priority == PushNotificationPriority.High ? Priority.High : Priority.Normal,
            Notification = new AndroidNotification
            {
                Title = request.Title,
                Body = request.Body,
                Icon = "ic_notification",
                Color = "#FF0000",
                Sound = request.Sound ?? "default",
                ClickAction = request.ClickAction
            },
            Data = request.Data
        };
    }

    private AndroidConfig? CreateAndroidConfig(TopicPushRequest request)
    {
        return new AndroidConfig
        {
            Priority = request.Priority == PushNotificationPriority.High ? Priority.High : Priority.Normal,
            Notification = new AndroidNotification
            {
                Title = request.Title,
                Body = request.Body,
                Icon = "ic_notification",
                Color = "#FF0000",
                Sound = request.Sound ?? "default",
                ClickAction = request.ClickAction
            },
            Data = request.Data
        };
    }

    private ApnsConfig? CreateApnsConfig(PushNotificationRequest request)
    {
        return new ApnsConfig
        {
            Aps = new Aps
            {
                Alert = new ApsAlert
                {
                    Title = request.Title,
                    Body = request.Body
                },
                Badge = request.Badge,
                Sound = request.Sound ?? "default",
                ContentAvailable = request.ContentAvailable,
                MutableContent = request.MutableContent
            }
        };
    }

    private ApnsConfig? CreateApnsConfig(MulticastPushRequest request)
    {
        return new ApnsConfig
        {
            Aps = new Aps
            {
                Alert = new ApsAlert
                {
                    Title = request.Title,
                    Body = request.Body
                },
                Badge = request.Badge,
                Sound = request.Sound ?? "default"
            }
        };
    }

    private ApnsConfig? CreateApnsConfig(TopicPushRequest request)
    {
        return new ApnsConfig
        {
            Aps = new Aps
            {
                Alert = new ApsAlert
                {
                    Title = request.Title,
                    Body = request.Body
                },
                Sound = request.Sound ?? "default"
            }
        };
    }

    private static bool IsInvalidToken(FirebaseMessagingException? exception)
    {
        return exception?.MessagingErrorCode == MessagingErrorCode.Unregistered ||
               exception?.MessagingErrorCode == MessagingErrorCode.InvalidArgument;
    }
}
