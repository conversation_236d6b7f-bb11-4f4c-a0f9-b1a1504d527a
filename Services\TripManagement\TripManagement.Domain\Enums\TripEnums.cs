﻿namespace TripManagement.Domain.Enums;

public enum TripStatus
{
    Created = 0,
    Assigned = 1,
    InProgress = 2,
    Completed = 3,
    Cancelled = 4,
    OnHold = 5,
    Exception = 6
}

public enum TripStopType
{
    Pickup = 0,
    Delivery = 1,
    Waypoint = 2,
    RestStop = 3
}

public enum TripStopStatus
{
    Pending = 0,
    Arrived = 1,
    InProgress = 2,
    Completed = 3,
    Skipped = 4,
    Failed = 5
}

public enum VehicleType
{
    Truck = 0,
    Van = 1,
    Trailer = 2,
    Container = 3,
    Flatbed = 4,
    Refrigerated = 5,
    Tanker = 6,
    Other = 7
}

public enum DriverStatus
{
    Available = 0,
    OnTrip = 1,
    OffDuty = 2,
    Unavailable = 3,
    Suspended = 4
}

public enum VehicleStatus
{
    Available = 0,
    InUse = 1,
    Maintenance = 2,
    OutOfService = 3,
    Retired = 4
}

public enum DocumentType
{
    ProofOfDelivery = 0,
    BillOfLading = 1,
    Invoice = 2,
    Photo = 3,
    Signature = 4,
    DamageReport = 5,
    Other = 6
}

public enum ExceptionType
{
    Delay = 0,
    Breakdown = 1,
    Accident = 2,
    WeatherDelay = 3,
    TrafficDelay = 4,
    CustomerUnavailable = 5,
    LoadDamage = 6,
    RouteDeviation = 7,
    FuelIssue = 8,
    DriverIssue = 9,
    Other = 10
}

public enum LocationUpdateSource
{
    GPS = 0,
    Manual = 1,
    Geofence = 2,
    Mobile = 3,
    Telematics = 4
}

public enum GeofenceStatus
{
    Unknown = 0,
    Outside = 1,
    Entering = 2,
    Inside = 3,
    Exiting = 4,
    Nearby = 5
}

public enum ETACalculationMethod
{
    Simple = 0,
    TrafficAware = 1,
    HistoricalData = 2,
    MachineLearning = 3,
    Hybrid = 4
}

public enum MilestoneType
{
    Pickup = 0,
    Delivery = 1,
    Checkpoint = 2,
    RestStop = 3,
    FuelStop = 4,
    Custom = 5
}

/// <summary>
/// Type of delay alert
/// </summary>
public enum DelayAlertType
{
    TripDelay = 0,
    StopDelay = 1,
    PickupDelay = 2,
    DeliveryDelay = 3,
    RouteDeviation = 4,
    VehicleBreakdown = 5,
    TrafficDelay = 6,
    WeatherDelay = 7,
    CustomDelay = 8
}

/// <summary>
/// Severity level of delay alert
/// </summary>
public enum DelayAlertSeverity
{
    Low = 0,
    Medium = 1,
    High = 2,
    Critical = 3
}

/// <summary>
/// Status of delay alert
/// </summary>
public enum DelayAlertStatus
{
    Active = 0,
    Acknowledged = 1,
    Resolved = 2,
    Cancelled = 3,
    Expired = 4
}

/// <summary>
/// Reason for delay
/// </summary>
public enum DelayReason
{
    Unknown = 0,
    Traffic = 1,
    Weather = 2,
    VehicleBreakdown = 3,
    DriverIssue = 4,
    CustomerDelay = 5,
    DocumentIssue = 6,
    RouteChange = 7,
    FuelShortage = 8,
    Accident = 9,
    RoadClosure = 10,
    Maintenance = 11,
    Other = 12
}

public enum ExceptionSeverity
{
    Low = 1,
    Medium = 2,
    High = 3,
    Critical = 4
}
