namespace MobileWorkflow.Application.DTOs;

public class MobileAppDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public string Platform { get; set; } = string.Empty;
    public string PackageId { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public DateTime ReleaseDate { get; set; }
    public string MinimumOSVersion { get; set; } = string.Empty;
    public Dictionary<string, object> Features { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
    public bool SupportsOffline { get; set; }
    public string DownloadUrl { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public string Checksum { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class CreateMobileAppDto
{
    public string Name { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public string Platform { get; set; } = string.Empty;
    public string PackageId { get; set; } = string.Empty;
    public DateTime ReleaseDate { get; set; }
    public string MinimumOSVersion { get; set; } = string.Empty;
    public Dictionary<string, object> Features { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
    public bool SupportsOffline { get; set; }
    public string DownloadUrl { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public string Checksum { get; set; } = string.Empty;
}

public class UpdateMobileAppDto
{
    public string? Name { get; set; }
    public string? Version { get; set; }
    public Dictionary<string, object>? Features { get; set; }
    public Dictionary<string, object>? Configuration { get; set; }
    public bool? SupportsOffline { get; set; }
    public string? DownloadUrl { get; set; }
    public long? FileSize { get; set; }
    public string? Checksum { get; set; }
    public bool? IsActive { get; set; }
}

public class MobileSessionDto
{
    public Guid Id { get; set; }
    public Guid UserId { get; set; }
    public Guid MobileAppId { get; set; }
    public string DeviceId { get; set; } = string.Empty;
    public string DeviceInfo { get; set; } = string.Empty;
    public string AppVersion { get; set; } = string.Empty;
    public string Platform { get; set; } = string.Empty;
    public string OSVersion { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public bool IsActive { get; set; }
    public string? LastKnownLocation { get; set; }
    public Dictionary<string, object> SessionData { get; set; } = new();
    public bool IsOfflineCapable { get; set; }
    public DateTime LastSyncTime { get; set; }
    public int OfflineActionsCount { get; set; }
    public MobileAppDto? MobileApp { get; set; }
}

public class CreateMobileSessionDto
{
    public Guid UserId { get; set; }
    public Guid MobileAppId { get; set; }
    public string DeviceId { get; set; } = string.Empty;
    public string DeviceInfo { get; set; } = string.Empty;
    public string AppVersion { get; set; } = string.Empty;
    public string Platform { get; set; } = string.Empty;
    public string OSVersion { get; set; } = string.Empty;
    public bool IsOfflineCapable { get; set; }
}

public class OfflineDataDto
{
    public Guid Id { get; set; }
    public Guid UserId { get; set; }
    public Guid MobileSessionId { get; set; }
    public Guid? MobileAppId { get; set; }
    public string DataType { get; set; } = string.Empty;
    public string Action { get; set; } = string.Empty;
    public Dictionary<string, object> Data { get; set; } = new();
    public DateTime CreatedOffline { get; set; }
    public DateTime? SyncedAt { get; set; }
    public bool IsSynced { get; set; }
    public string? SyncError { get; set; }
    public int SyncAttempts { get; set; }
    public int Priority { get; set; }
    public string? ConflictResolution { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class CreateOfflineDataDto
{
    public Guid UserId { get; set; }
    public Guid MobileSessionId { get; set; }
    public string DataType { get; set; } = string.Empty;
    public string Action { get; set; } = string.Empty;
    public Dictionary<string, object> Data { get; set; } = new();
    public int Priority { get; set; } = 2;
    public Guid? MobileAppId { get; set; }
}
