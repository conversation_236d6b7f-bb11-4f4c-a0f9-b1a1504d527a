using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Entities;
using NetworkFleetManagement.IntegrationTests.TestFixtures;
using System.Net;
using System.Net.Http.Json;
using System.Security.Claims;
using System.Text;
using System.Text.Json;
using Xunit;

namespace NetworkFleetManagement.IntegrationTests.Controllers;

[Collection("NetworkFleet")]
public class PreferredPartnersControllerTests : IClassFixture<NetworkFleetTestFixture>
{
    private readonly NetworkFleetTestFixture _fixture;
    private readonly HttpClient _client;
    private readonly Guid _testUserId = Guid.NewGuid();

    public PreferredPartnersControllerTests(NetworkFleetTestFixture fixture)
    {
        _fixture = fixture;
        _client = _fixture.CreateClient();
        
        // Add test user claims
        _client.DefaultRequestHeaders.Add("X-User-Id", _testUserId.ToString());
        _client.DefaultRequestHeaders.Add("X-User-Role", "Broker");
    }

    [Fact]
    public async Task CreatePreferredPartner_ValidRequest_ReturnsCreated()
    {
        // Arrange
        await _fixture.ResetDatabaseAsync();
        
        var createDto = new CreatePreferredPartnerDto
        {
            PartnerId = Guid.NewGuid(),
            PartnerType = "Carrier",
            PreferenceLevel = "High",
            Priority = 1,
            PreferredCommissionRate = 0.15m,
            AutoAssignEnabled = true,
            Notes = "Test preferred carrier",
            PreferredRoutes = new List<string> { "Route1", "Route2" },
            PreferredLoadTypes = new List<string> { "Dry Van", "Refrigerated" }
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/v1/preferredpartners", createDto);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Created);
        
        var partnerId = await response.Content.ReadFromJsonAsync<Guid>();
        partnerId.Should().NotBeEmpty();

        // Verify in database
        using var context = _fixture.GetDbContext();
        var partner = await context.PreferredPartners.FindAsync(partnerId);
        partner.Should().NotBeNull();
        partner!.UserId.Should().Be(_testUserId);
        partner.PartnerType.Should().Be(PreferredPartnerType.Carrier);
        partner.PreferenceLevel.Should().Be(PreferenceLevel.High);
    }

    [Fact]
    public async Task GetPreferredPartners_WithFilters_ReturnsFilteredResults()
    {
        // Arrange
        await _fixture.ResetDatabaseAsync();
        await SeedTestDataAsync();

        // Act
        var response = await _client.GetAsync("/api/v1/preferredpartners?partnerTypes=Carrier&isActive=true&pageSize=5");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var result = await response.Content.ReadFromJsonAsync<PagedResult<PreferredPartnerSummaryDto>>();
        result.Should().NotBeNull();
        result!.Items.Should().NotBeEmpty();
        result.Items.Should().AllSatisfy(p => p.PartnerType.Should().Be("Carrier"));
    }

    [Fact]
    public async Task GetPreferredCarriers_AsBroker_ReturnsCarriers()
    {
        // Arrange
        await _fixture.ResetDatabaseAsync();
        await SeedTestDataAsync();

        // Act
        var response = await _client.GetAsync("/api/v1/preferredpartners/carriers?activeOnly=true&limit=10");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var carriers = await response.Content.ReadFromJsonAsync<List<PreferredPartnerSummaryDto>>();
        carriers.Should().NotBeNull();
        carriers!.Should().AllSatisfy(c => c.PartnerType.Should().Be("Carrier"));
    }

    [Fact]
    public async Task GetDashboard_ValidRequest_ReturnsDashboardData()
    {
        // Arrange
        await _fixture.ResetDatabaseAsync();
        await SeedTestDataAsync();

        // Act
        var response = await _client.GetAsync("/api/v1/preferredpartners/dashboard");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var dashboard = await response.Content.ReadFromJsonAsync<PreferredPartnerDashboardDto>();
        dashboard.Should().NotBeNull();
        dashboard!.UserId.Should().Be(_testUserId);
        dashboard.TotalPreferredPartners.Should().BeGreaterThan(0);
    }

    [Fact]
    public async Task UpdateAutoAssignmentSettings_ValidRequest_ReturnsOk()
    {
        // Arrange
        await _fixture.ResetDatabaseAsync();
        var partnerId = await CreateTestPartnerAsync();

        var updateDto = new UpdateAutoAssignmentSettingsDto
        {
            AutoAssignEnabled = true,
            AutoAssignThreshold = 4.5m,
            PreferredRoutes = new List<string> { "Route1", "Route3" },
            PreferredLoadTypes = new List<string> { "Dry Van" }
        };

        // Act
        var response = await _client.PutAsJsonAsync($"/api/v1/preferredpartners/{partnerId}/auto-assignment", updateDto);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        // Verify in database
        using var context = _fixture.GetDbContext();
        var partner = await context.PreferredPartners.FindAsync(partnerId);
        partner.Should().NotBeNull();
        partner!.AutoAssignEnabled.Should().BeTrue();
        partner.AutoAssignThreshold.Should().Be(4.5m);
    }

    [Fact]
    public async Task TestAutoAssignment_ValidCriteria_ReturnsEligiblePartners()
    {
        // Arrange
        await _fixture.ResetDatabaseAsync();
        await SeedTestDataAsync();

        var testRequest = new TestAutoAssignmentRequest
        {
            PartnerType = "Carrier",
            Route = "Route1",
            LoadType = "Dry Van",
            MinRating = 4.0m,
            MaxResults = 5
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/v1/preferredpartners/test-auto-assignment", testRequest);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var eligiblePartners = await response.Content.ReadFromJsonAsync<List<PreferredPartnerSummaryDto>>();
        eligiblePartners.Should().NotBeNull();
        eligiblePartners!.Should().AllSatisfy(p => 
        {
            p.PartnerType.Should().Be("Carrier");
            p.AutoAssignEnabled.Should().BeTrue();
        });
    }

    [Fact]
    public async Task GetPartnerAnalytics_ValidPartnerId_ReturnsAnalytics()
    {
        // Arrange
        await _fixture.ResetDatabaseAsync();
        var partnerId = await CreateTestPartnerAsync();

        // Act
        var response = await _client.GetAsync($"/api/v1/preferredpartners/{partnerId}/analytics?fromDate=2024-01-01&toDate=2024-12-31");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var analytics = await response.Content.ReadFromJsonAsync<PartnerAnalyticsDto>();
        analytics.Should().NotBeNull();
        analytics!.PreferredPartnerId.Should().Be(partnerId);
        analytics.PerformanceMetrics.Should().NotBeNull();
    }

    [Fact]
    public async Task ActivatePreferredPartner_ValidId_ReturnsOk()
    {
        // Arrange
        await _fixture.ResetDatabaseAsync();
        var partnerId = await CreateTestPartnerAsync();

        // Act
        var response = await _client.PostAsync($"/api/v1/preferredpartners/{partnerId}/activate", null);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        // Verify in database
        using var context = _fixture.GetDbContext();
        var partner = await context.PreferredPartners.FindAsync(partnerId);
        partner.Should().NotBeNull();
        partner!.Status.Should().Be(PreferenceStatus.Active);
        partner.ActivatedAt.Should().NotBeNull();
    }

    [Fact]
    public async Task DeactivatePreferredPartner_ValidId_ReturnsOk()
    {
        // Arrange
        await _fixture.ResetDatabaseAsync();
        var partnerId = await CreateTestPartnerAsync();

        var deactivateRequest = new DeactivatePreferredPartnerRequest
        {
            Reason = "Test deactivation"
        };

        // Act
        var response = await _client.PostAsJsonAsync($"/api/v1/preferredpartners/{partnerId}/deactivate", deactivateRequest);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        // Verify in database
        using var context = _fixture.GetDbContext();
        var partner = await context.PreferredPartners.FindAsync(partnerId);
        partner.Should().NotBeNull();
        partner!.Status.Should().Be(PreferenceStatus.Inactive);
        partner.DeactivationReason.Should().Be("Test deactivation");
    }

    private async Task<Guid> CreateTestPartnerAsync()
    {
        var partner = PreferredPartner.Create(
            _testUserId,
            Guid.NewGuid(),
            PreferredPartnerType.Carrier,
            PreferenceLevel.High,
            1);

        using var context = _fixture.GetDbContext();
        context.PreferredPartners.Add(partner);
        await context.SaveChangesAsync();

        return partner.Id;
    }

    private async Task SeedTestDataAsync()
    {
        using var context = _fixture.GetDbContext();

        var partners = new[]
        {
            PreferredPartner.Create(_testUserId, Guid.NewGuid(), PreferredPartnerType.Carrier, PreferenceLevel.High, 1),
            PreferredPartner.Create(_testUserId, Guid.NewGuid(), PreferredPartnerType.Carrier, PreferenceLevel.Medium, 2),
            PreferredPartner.Create(_testUserId, Guid.NewGuid(), PreferredPartnerType.Broker, PreferenceLevel.High, 1)
        };

        foreach (var partner in partners)
        {
            partner.Activate();
            partner.UpdateAutoAssignSettings(true, 4.0m, 
                new List<string> { "Route1", "Route2" }, 
                new List<string> { "Dry Van", "Refrigerated" },
                new List<string>(),
                new List<string>());
        }

        context.PreferredPartners.AddRange(partners);
        await context.SaveChangesAsync();
    }
}
