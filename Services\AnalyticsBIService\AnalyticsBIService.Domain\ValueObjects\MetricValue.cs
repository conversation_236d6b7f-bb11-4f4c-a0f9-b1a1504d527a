﻿using AnalyticsBIService.Domain.Enums;
using Shared.Domain.Common;
using Shared.Domain.ValueObjects;

namespace AnalyticsBIService.Domain.ValueObjects;

/// <summary>
/// Value object representing a metric value with its type and metadata
/// </summary>
public class MetricValue : ValueObject
{
    public decimal Value { get; private set; }
    public MetricType Type { get; private set; }
    public string Unit { get; private set; }
    public DateTime Timestamp { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Compatibility property for Infrastructure services
    public decimal NumericValue => Value;

    private MetricValue()
    {
        Unit = string.Empty;
        Metadata = new Dictionary<string, object>();
    }

    public MetricValue(decimal value, MetricType type, string unit, DateTime timestamp, Dictionary<string, object>? metadata = null)
    {
        Value = value;
        Type = type;
        Unit = unit ?? throw new ArgumentNullException(nameof(unit));
        Timestamp = timestamp;
        Metadata = metadata ?? new Dictionary<string, object>();
    }

    public static MetricValue Create(decimal value, MetricType type, string unit, DateTime? timestamp = null, Dictionary<string, object>? metadata = null)
    {
        return new MetricValue(value, type, unit, timestamp ?? DateTime.UtcNow, metadata);
    }

    public static MetricValue Counter(decimal value, DateTime? timestamp = null, Dictionary<string, object>? metadata = null)
    {
        return new MetricValue(value, MetricType.Counter, "count", timestamp ?? DateTime.UtcNow, metadata);
    }

    public static MetricValue Percentage(decimal value, DateTime? timestamp = null, Dictionary<string, object>? metadata = null)
    {
        if (value < 0 || value > 100)
            throw new ArgumentException("Percentage value must be between 0 and 100");

        return new MetricValue(value, MetricType.Percentage, "%", timestamp ?? DateTime.UtcNow, metadata);
    }

    public static MetricValue Currency(decimal value, string currency, DateTime? timestamp = null, Dictionary<string, object>? metadata = null)
    {
        return new MetricValue(value, MetricType.Gauge, currency, timestamp ?? DateTime.UtcNow, metadata);
    }

    public static MetricValue Rate(decimal value, string rateUnit, DateTime? timestamp = null, Dictionary<string, object>? metadata = null)
    {
        return new MetricValue(value, MetricType.Rate, rateUnit, timestamp ?? DateTime.UtcNow, metadata);
    }

    public MetricValue WithMetadata(string key, object value)
    {
        var newMetadata = new Dictionary<string, object>(Metadata)
        {
            [key] = value
        };
        return new MetricValue(Value, Type, Unit, Timestamp, newMetadata);
    }

    public bool IsPositiveTrend()
    {
        return Type switch
        {
            MetricType.Counter => Value > 0,
            MetricType.Gauge => Value > 0,
            MetricType.Percentage => Value > 50,
            MetricType.Rate => Value > 0,
            _ => false
        };
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Value;
        yield return Type;
        yield return Unit;
        yield return Timestamp;
        foreach (var kvp in Metadata.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }
    }

    public override string ToString()
    {
        return $"{Value} {Unit} ({Type})";
    }
}

/// <summary>
/// Value object representing a time period for analytics
/// </summary>
public class TimePeriodValue : ValueObject
{
    public DateTime StartDate { get; private set; }
    public DateTime EndDate { get; private set; }
    public TimePeriod Period { get; private set; }

    private TimePeriodValue() { }

    public TimePeriodValue(DateTime startDate, DateTime endDate, TimePeriod period)
    {
        if (startDate >= endDate)
            throw new ArgumentException("Start date must be before end date");

        StartDate = startDate;
        EndDate = endDate;
        Period = period;
    }

    public static TimePeriodValue Create(DateTime startDate, DateTime endDate, TimePeriod period)
    {
        return new TimePeriodValue(startDate, endDate, period);
    }

    public static TimePeriodValue Today()
    {
        var today = DateTime.UtcNow.Date;
        return new TimePeriodValue(today, today.AddDays(1).AddTicks(-1), TimePeriod.Daily);
    }

    public static TimePeriodValue ThisWeek()
    {
        var today = DateTime.UtcNow.Date;
        var startOfWeek = today.AddDays(-(int)today.DayOfWeek);
        var endOfWeek = startOfWeek.AddDays(7).AddTicks(-1);
        return new TimePeriodValue(startOfWeek, endOfWeek, TimePeriod.Weekly);
    }

    public static TimePeriodValue ThisMonth()
    {
        var today = DateTime.UtcNow.Date;
        var startOfMonth = new DateTime(today.Year, today.Month, 1);
        var endOfMonth = startOfMonth.AddMonths(1).AddTicks(-1);
        return new TimePeriodValue(startOfMonth, endOfMonth, TimePeriod.Monthly);
    }

    public static TimePeriodValue LastNDays(int days)
    {
        var endDate = DateTime.UtcNow;
        var startDate = endDate.AddDays(-days);
        return new TimePeriodValue(startDate, endDate, TimePeriod.Daily);
    }

    public TimeSpan Duration => EndDate - StartDate;

    public bool Contains(DateTime dateTime)
    {
        return dateTime >= StartDate && dateTime <= EndDate;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return StartDate;
        yield return EndDate;
        yield return Period;
    }

    public override string ToString()
    {
        return $"{StartDate:yyyy-MM-dd} to {EndDate:yyyy-MM-dd} ({Period})";
    }
}


