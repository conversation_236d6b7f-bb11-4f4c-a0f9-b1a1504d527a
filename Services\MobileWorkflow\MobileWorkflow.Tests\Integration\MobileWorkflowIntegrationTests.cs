using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Net.Http.Json;
using System.Text.Json;
using Xunit;
using Xunit.Abstractions;
using MobileWorkflow.Application.DTOs;

namespace MobileWorkflow.Tests.Integration;

public class MobileWorkflowIntegrationTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;
    private readonly ITestOutputHelper _output;
    private readonly JsonSerializerOptions _jsonOptions;

    public MobileWorkflowIntegrationTests(WebApplicationFactory<Program> factory, ITestOutputHelper output)
    {
        _factory = factory;
        _output = output;
        _client = _factory.CreateClient();
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
    }

    [Fact]
    public async Task HealthCheck_ShouldReturnHealthy()
    {
        // Act
        var response = await _client.GetAsync("/health");
        
        // Assert
        response.EnsureSuccessStatusCode();
        var healthCheck = await response.Content.ReadFromJsonAsync<HealthCheckResultDto>(_jsonOptions);
        
        Assert.NotNull(healthCheck);
        Assert.Equal("Healthy", healthCheck.OverallStatus);
        Assert.NotEmpty(healthCheck.Checks);
        
        _output.WriteLine($"Health check completed with {healthCheck.Checks.Count} checks");
    }

    [Fact]
    public async Task WorkflowEngine_CreateAndExecuteWorkflow_ShouldSucceed()
    {
        // Arrange
        var createWorkflowRequest = new CreateWorkflowDefinitionRequest
        {
            Name = "Test Integration Workflow",
            Description = "Integration test workflow",
            WorkflowType = "Sequential",
            IsActive = true,
            CreatedBy = "integration-test"
        };

        // Act - Create workflow
        var createResponse = await _client.PostAsJsonAsync("/api/workflows", createWorkflowRequest, _jsonOptions);
        createResponse.EnsureSuccessStatusCode();
        
        var workflow = await createResponse.Content.ReadFromJsonAsync<WorkflowDefinitionDto>(_jsonOptions);
        Assert.NotNull(workflow);
        
        // Act - Execute workflow
        var executeRequest = new StartWorkflowExecutionRequest
        {
            WorkflowDefinitionId = workflow.Id,
            ExecutedBy = "integration-test",
            InputData = new Dictionary<string, object> { ["testKey"] = "testValue" }
        };
        
        var executeResponse = await _client.PostAsJsonAsync("/api/workflows/execute", executeRequest, _jsonOptions);
        executeResponse.EnsureSuccessStatusCode();
        
        var execution = await executeResponse.Content.ReadFromJsonAsync<WorkflowExecutionDto>(_jsonOptions);
        
        // Assert
        Assert.NotNull(execution);
        Assert.Equal(workflow.Id, execution.WorkflowDefinitionId);
        Assert.Equal("Running", execution.Status);
        
        _output.WriteLine($"Workflow {workflow.Id} executed successfully with execution {execution.Id}");
    }

    [Fact]
    public async Task FormBuilder_CreateAndSubmitForm_ShouldSucceed()
    {
        // Arrange
        var createFormRequest = new CreateFormDefinitionRequest
        {
            Name = "Test Integration Form",
            Description = "Integration test form",
            FormType = "DataCollection",
            IsActive = true,
            CreatedBy = "integration-test"
        };

        // Act - Create form
        var createResponse = await _client.PostAsJsonAsync("/api/forms", createFormRequest, _jsonOptions);
        createResponse.EnsureSuccessStatusCode();
        
        var form = await createResponse.Content.ReadFromJsonAsync<FormDefinitionDto>(_jsonOptions);
        Assert.NotNull(form);
        
        // Act - Submit form
        var submitRequest = new SubmitFormRequest
        {
            FormDefinitionId = form.Id,
            SubmittedBy = "integration-test",
            FormData = new Dictionary<string, object> { ["field1"] = "value1", ["field2"] = "value2" }
        };
        
        var submitResponse = await _client.PostAsJsonAsync("/api/forms/submit", submitRequest, _jsonOptions);
        submitResponse.EnsureSuccessStatusCode();
        
        var submission = await submitResponse.Content.ReadFromJsonAsync<FormSubmissionDto>(_jsonOptions);
        
        // Assert
        Assert.NotNull(submission);
        Assert.Equal(form.Id, submission.FormDefinitionId);
        Assert.Equal("Submitted", submission.Status);
        
        _output.WriteLine($"Form {form.Id} submitted successfully with submission {submission.Id}");
    }

    [Fact]
    public async Task PushNotifications_SendNotification_ShouldSucceed()
    {
        // Arrange
        var sendNotificationRequest = new SendPushNotificationRequest
        {
            Title = "Integration Test Notification",
            Body = "This is a test notification from integration tests",
            Recipients = new List<NotificationRecipientDto>
            {
                new() { Type = "User", Value = "test-user-id" }
            },
            NotificationData = new Dictionary<string, object> { ["testKey"] = "testValue" },
            SentBy = "integration-test"
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/notifications/push", sendNotificationRequest, _jsonOptions);
        response.EnsureSuccessStatusCode();
        
        var notification = await response.Content.ReadFromJsonAsync<PushNotificationDto>(_jsonOptions);
        
        // Assert
        Assert.NotNull(notification);
        Assert.Equal("Integration Test Notification", notification.Title);
        Assert.Equal("Sent", notification.Status);
        
        _output.WriteLine($"Push notification {notification.Id} sent successfully");
    }

    [Fact]
    public async Task DeviceManagement_RegisterAndUpdateDevice_ShouldSucceed()
    {
        // Arrange
        var registerDeviceRequest = new RegisterDeviceRequest
        {
            DeviceId = $"test-device-{Guid.NewGuid()}",
            UserId = Guid.NewGuid(),
            Platform = "iOS",
            DeviceModel = "iPhone 14",
            OSVersion = "16.0",
            AppVersion = "1.0.0",
            DeviceCapabilities = new Dictionary<string, object>
            {
                ["camera"] = true,
                ["gps"] = true,
                ["biometrics"] = true
            }
        };

        // Act - Register device
        var registerResponse = await _client.PostAsJsonAsync("/api/devices/register", registerDeviceRequest, _jsonOptions);
        registerResponse.EnsureSuccessStatusCode();
        
        var device = await registerResponse.Content.ReadFromJsonAsync<ManagedDeviceDto>(_jsonOptions);
        Assert.NotNull(device);
        
        // Act - Update device
        var updateRequest = new UpdateDeviceRequest
        {
            DeviceCapabilities = new Dictionary<string, object>
            {
                ["camera"] = true,
                ["gps"] = true,
                ["biometrics"] = true,
                ["nfc"] = false
            }
        };
        
        var updateResponse = await _client.PutAsJsonAsync($"/api/devices/{device.Id}", updateRequest, _jsonOptions);
        updateResponse.EnsureSuccessStatusCode();
        
        // Assert
        var updatedDevice = await updateResponse.Content.ReadFromJsonAsync<ManagedDeviceDto>(_jsonOptions);
        Assert.NotNull(updatedDevice);
        Assert.Equal(device.Id, updatedDevice.Id);
        
        _output.WriteLine($"Device {device.DeviceId} registered and updated successfully");
    }

    [Fact]
    public async Task Analytics_RecordAndRetrieveEvents_ShouldSucceed()
    {
        // Arrange
        var recordEventRequest = new RecordAnalyticsEventRequest
        {
            EventName = "integration_test_event",
            EventCategory = "Testing",
            UserId = Guid.NewGuid(),
            DeviceId = "test-device-123",
            EventData = new Dictionary<string, object>
            {
                ["test_property"] = "test_value",
                ["event_count"] = 1
            }
        };

        // Act - Record event
        var recordResponse = await _client.PostAsJsonAsync("/api/analytics/events", recordEventRequest, _jsonOptions);
        recordResponse.EnsureSuccessStatusCode();
        
        var analyticsEvent = await recordResponse.Content.ReadFromJsonAsync<AnalyticsEventDto>(_jsonOptions);
        Assert.NotNull(analyticsEvent);
        
        // Act - Get analytics
        var analyticsResponse = await _client.GetAsync("/api/analytics/summary?fromDate=" + DateTime.UtcNow.AddDays(-1).ToString("yyyy-MM-dd"));
        analyticsResponse.EnsureSuccessStatusCode();
        
        var analytics = await analyticsResponse.Content.ReadFromJsonAsync<AnalyticsSummaryDto>(_jsonOptions);
        
        // Assert
        Assert.NotNull(analytics);
        Assert.True(analytics.TotalEvents > 0);
        
        _output.WriteLine($"Analytics event {analyticsEvent.Id} recorded and retrieved successfully");
    }

    [Fact]
    public async Task BackgroundProcessing_CreateAndExecuteJob_ShouldSucceed()
    {
        // Arrange
        var createJobRequest = new CreateBackgroundJobRequest
        {
            JobName = "integration-test-job",
            JobType = "TestJob",
            JobData = new Dictionary<string, object> { ["testData"] = "integration test" },
            ScheduledFor = DateTime.UtcNow.AddSeconds(5),
            CreatedBy = "integration-test"
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/jobs", createJobRequest, _jsonOptions);
        response.EnsureSuccessStatusCode();
        
        var job = await response.Content.ReadFromJsonAsync<BackgroundJobDto>(_jsonOptions);
        
        // Assert
        Assert.NotNull(job);
        Assert.Equal("integration-test-job", job.JobName);
        Assert.Equal("Scheduled", job.Status);
        
        _output.WriteLine($"Background job {job.Id} created successfully");
    }

    [Fact]
    public async Task Geofencing_CreateGeofenceAndProcessLocation_ShouldSucceed()
    {
        // Arrange
        var createGeofenceRequest = new CreateGeofenceRequest
        {
            Name = "Test Integration Geofence",
            Description = "Integration test geofence",
            Type = "Circular",
            Coordinates = new Dictionary<string, object>
            {
                ["latitude"] = 37.7749,
                ["longitude"] = -122.4194
            },
            Radius = 100.0,
            TriggerType = "Enter",
            CreatedBy = "integration-test"
        };

        // Act - Create geofence
        var createResponse = await _client.PostAsJsonAsync("/api/geofences", createGeofenceRequest, _jsonOptions);
        createResponse.EnsureSuccessStatusCode();
        
        var geofence = await createResponse.Content.ReadFromJsonAsync<GeofenceDto>(_jsonOptions);
        Assert.NotNull(geofence);
        
        // Act - Process location update
        var locationRequest = new ProcessLocationUpdateRequest
        {
            UserId = Guid.NewGuid(),
            Latitude = 37.7749,
            Longitude = -122.4194,
            Accuracy = 5.0,
            Timestamp = DateTime.UtcNow
        };
        
        var locationResponse = await _client.PostAsJsonAsync("/api/geofences/location", locationRequest, _jsonOptions);
        locationResponse.EnsureSuccessStatusCode();
        
        // Assert
        Assert.Equal("Test Integration Geofence", geofence.Name);
        Assert.True(geofence.IsActive);
        
        _output.WriteLine($"Geofence {geofence.Id} created and location processed successfully");
    }

    [Fact]
    public async Task FileSync_CreateAndSyncFile_ShouldSucceed()
    {
        // Arrange
        var createFileRequest = new CreateSyncFileRequest
        {
            FileName = "test-integration-file.txt",
            FilePath = "/test/integration/file.txt",
            FileHash = "abc123def456",
            FileSize = 1024,
            MimeType = "text/plain",
            UserId = Guid.NewGuid(),
            DeviceId = "test-device-123"
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/files/sync", createFileRequest, _jsonOptions);
        response.EnsureSuccessStatusCode();
        
        var syncFile = await response.Content.ReadFromJsonAsync<SyncFileDto>(_jsonOptions);
        
        // Assert
        Assert.NotNull(syncFile);
        Assert.Equal("test-integration-file.txt", syncFile.FileName);
        Assert.Equal("Pending", syncFile.Status);
        
        _output.WriteLine($"Sync file {syncFile.Id} created successfully");
    }

    [Fact]
    public async Task Performance_GetSystemHealth_ShouldReturnMetrics()
    {
        // Act
        var response = await _client.GetAsync("/api/monitoring/health");
        response.EnsureSuccessStatusCode();
        
        var systemHealth = await response.Content.ReadFromJsonAsync<SystemHealthDto>(_jsonOptions);
        
        // Assert
        Assert.NotNull(systemHealth);
        Assert.True(systemHealth.CpuUsagePercent >= 0);
        Assert.True(systemHealth.MemoryUsageMB > 0);
        Assert.NotEqual("Error", systemHealth.OverallStatus);
        
        _output.WriteLine($"System health check completed: {systemHealth.OverallStatus}");
    }

    [Fact]
    public async Task EndToEnd_CompleteWorkflow_ShouldProcessSuccessfully()
    {
        // This test demonstrates a complete end-to-end workflow
        _output.WriteLine("Starting end-to-end integration test...");

        // 1. Create a workflow
        var workflowRequest = new CreateWorkflowDefinitionRequest
        {
            Name = "E2E Test Workflow",
            Description = "End-to-end integration test workflow",
            WorkflowType = "Sequential",
            IsActive = true,
            CreatedBy = "e2e-test"
        };

        var workflowResponse = await _client.PostAsJsonAsync("/api/workflows", workflowRequest, _jsonOptions);
        workflowResponse.EnsureSuccessStatusCode();
        var workflow = await workflowResponse.Content.ReadFromJsonAsync<WorkflowDefinitionDto>(_jsonOptions);
        Assert.NotNull(workflow);

        // 2. Create a form
        var formRequest = new CreateFormDefinitionRequest
        {
            Name = "E2E Test Form",
            Description = "End-to-end test form",
            FormType = "DataCollection",
            IsActive = true,
            CreatedBy = "e2e-test"
        };

        var formResponse = await _client.PostAsJsonAsync("/api/forms", formRequest, _jsonOptions);
        formResponse.EnsureSuccessStatusCode();
        var form = await formResponse.Content.ReadFromJsonAsync<FormDefinitionDto>(_jsonOptions);
        Assert.NotNull(form);

        // 3. Register a device
        var deviceRequest = new RegisterDeviceRequest
        {
            DeviceId = $"e2e-device-{Guid.NewGuid()}",
            UserId = Guid.NewGuid(),
            Platform = "Android",
            DeviceModel = "Samsung Galaxy S23",
            OSVersion = "13.0",
            AppVersion = "1.0.0"
        };

        var deviceResponse = await _client.PostAsJsonAsync("/api/devices/register", deviceRequest, _jsonOptions);
        deviceResponse.EnsureSuccessStatusCode();
        var device = await deviceResponse.Content.ReadFromJsonAsync<ManagedDeviceDto>(_jsonOptions);
        Assert.NotNull(device);

        // 4. Submit form data
        var submitRequest = new SubmitFormRequest
        {
            FormDefinitionId = form.Id,
            SubmittedBy = "e2e-test",
            FormData = new Dictionary<string, object>
            {
                ["customer_name"] = "John Doe",
                ["order_amount"] = 150.00,
                ["delivery_address"] = "123 Main St, City, State"
            }
        };

        var submitResponse = await _client.PostAsJsonAsync("/api/forms/submit", submitRequest, _jsonOptions);
        submitResponse.EnsureSuccessStatusCode();
        var submission = await submitResponse.Content.ReadFromJsonAsync<FormSubmissionDto>(_jsonOptions);
        Assert.NotNull(submission);

        // 5. Execute workflow with form data
        var executeRequest = new StartWorkflowExecutionRequest
        {
            WorkflowDefinitionId = workflow.Id,
            ExecutedBy = "e2e-test",
            InputData = new Dictionary<string, object>
            {
                ["form_submission_id"] = submission.Id,
                ["device_id"] = device.DeviceId,
                ["customer_data"] = submission.FormData
            }
        };

        var executeResponse = await _client.PostAsJsonAsync("/api/workflows/execute", executeRequest, _jsonOptions);
        executeResponse.EnsureSuccessStatusCode();
        var execution = await executeResponse.Content.ReadFromJsonAsync<WorkflowExecutionDto>(_jsonOptions);
        Assert.NotNull(execution);

        // 6. Send notification about workflow completion
        var notificationRequest = new SendPushNotificationRequest
        {
            Title = "Workflow Completed",
            Body = $"E2E test workflow {workflow.Name} has been completed successfully",
            Recipients = new List<NotificationRecipientDto>
            {
                new() { Type = "Device", Value = device.DeviceId }
            },
            SentBy = "e2e-test"
        };

        var notificationResponse = await _client.PostAsJsonAsync("/api/notifications/push", notificationRequest, _jsonOptions);
        notificationResponse.EnsureSuccessStatusCode();
        var notification = await notificationResponse.Content.ReadFromJsonAsync<PushNotificationDto>(_jsonOptions);
        Assert.NotNull(notification);

        // 7. Record analytics event
        var analyticsRequest = new RecordAnalyticsEventRequest
        {
            EventName = "e2e_workflow_completed",
            EventCategory = "Integration",
            UserId = device.UserId,
            DeviceId = device.DeviceId,
            EventData = new Dictionary<string, object>
            {
                ["workflow_id"] = workflow.Id,
                ["execution_id"] = execution.Id,
                ["form_id"] = form.Id,
                ["submission_id"] = submission.Id
            }
        };

        var analyticsResponse = await _client.PostAsJsonAsync("/api/analytics/events", analyticsRequest, _jsonOptions);
        analyticsResponse.EnsureSuccessStatusCode();
        var analyticsEvent = await analyticsResponse.Content.ReadFromJsonAsync<AnalyticsEventDto>(_jsonOptions);
        Assert.NotNull(analyticsEvent);

        // Assert all components worked together
        Assert.Equal("Running", execution.Status);
        Assert.Equal("Submitted", submission.Status);
        Assert.Equal("Sent", notification.Status);
        Assert.Equal("e2e_workflow_completed", analyticsEvent.EventName);

        _output.WriteLine("End-to-end integration test completed successfully!");
        _output.WriteLine($"Workflow: {workflow.Id}, Form: {form.Id}, Device: {device.Id}");
        _output.WriteLine($"Execution: {execution.Id}, Submission: {submission.Id}, Notification: {notification.Id}");
    }
}
