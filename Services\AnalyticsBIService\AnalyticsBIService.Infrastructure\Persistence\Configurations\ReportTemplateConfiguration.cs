using AnalyticsBIService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace AnalyticsBIService.Infrastructure.Persistence.Configurations;

/// <summary>
/// Entity configuration for ReportTemplate
/// </summary>
public class ReportTemplateConfiguration : IEntityTypeConfiguration<ReportTemplate>
{
    public void Configure(EntityTypeBuilder<ReportTemplate> builder)
    {
        builder.ToTable("report_templates");

        builder.HasKey(rt => rt.Id);

        builder.Property(rt => rt.Name)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(rt => rt.Description)
            .HasMaxLength(1000);

        builder.Property(rt => rt.Category)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(rt => rt.TemplateContent)
            .IsRequired()
            .HasColumnType("text");

        builder.Property(rt => rt.Parameters)
            .HasColumnType("jsonb");

        builder.Property(rt => rt.UserType)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(rt => rt.IsActive)
            .IsRequired()
            .HasDefaultValue(true);

        builder.Property(rt => rt.CreatedDate)
            .IsRequired();

        builder.Property(rt => rt.LastModifiedDate);

        builder.Property(rt => rt.CreatedBy)
            .IsRequired();

        builder.Property(rt => rt.LastModifiedBy);

        // Indexes
        builder.HasIndex(rt => rt.Category)
            .HasDatabaseName("IX_ReportTemplates_Category");

        builder.HasIndex(rt => rt.UserType)
            .HasDatabaseName("IX_ReportTemplates_UserType");

        builder.HasIndex(rt => rt.IsActive)
            .HasDatabaseName("IX_ReportTemplates_IsActive");

        builder.HasIndex(rt => new { rt.Name, rt.Category })
            .HasDatabaseName("IX_ReportTemplates_Name_Category");
    }
}
