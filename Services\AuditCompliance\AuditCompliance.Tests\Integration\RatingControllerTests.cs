using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.EntityFrameworkCore;
using System.Net.Http.Json;
using System.Net;
using Xunit;
using FluentAssertions;
using AuditCompliance.Infrastructure.Persistence;
using AuditCompliance.Application.Commands;
using AuditCompliance.Domain.Enums;

namespace AuditCompliance.Tests.Integration;

public class RatingControllerTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;

    public RatingControllerTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureServices(services =>
            {
                // Remove the existing DbContext registration
                var descriptor = services.SingleOrDefault(
                    d => d.ServiceType == typeof(DbContextOptions<AuditComplianceDbContext>));
                if (descriptor != null)
                    services.Remove(descriptor);

                // Add in-memory database for testing
                services.AddDbContext<AuditComplianceDbContext>(options =>
                {
                    options.UseInMemoryDatabase("TestDb_Rating");
                });

                // Build the service provider and create the database
                var sp = services.BuildServiceProvider();
                using var scope = sp.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<AuditComplianceDbContext>();
                context.Database.EnsureCreated();
            });
        });

        _client = _factory.CreateClient();
    }

    [Fact]
    public async Task CreateServiceProviderRating_WithValidData_ShouldReturnOk()
    {
        // Arrange
        var command = new CreateServiceProviderRatingCommand
        {
            TransportCompanyId = Guid.NewGuid(),
            TransportCompanyName = "Test Transport Company",
            OverallRating = 4.5m,
            ReviewTitle = "Great service",
            ReviewComment = "Very professional and timely delivery",
            ServiceCompletedAt = DateTime.UtcNow.AddDays(-1),
            CategoryRatings = new List<CreateCategoryRatingDto>
            {
                new() { Category = RatingCategory.Timeliness, Rating = 5.0m, Comment = "On time" },
                new() { Category = RatingCategory.Communication, Rating = 4.0m, Comment = "Good communication" }
            }
        };

        // Add authorization header for shipper role
        _client.DefaultRequestHeaders.Authorization = 
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", "shipper-token");

        // Act
        var response = await _client.PostAsJsonAsync("/api/rating", command);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        content.Should().Contain("RatingId");
    }

    [Fact]
    public async Task GetServiceProviderRating_WithValidId_ShouldReturnRating()
    {
        // Arrange
        var ratingId = await CreateTestRating();
        
        _client.DefaultRequestHeaders.Authorization = 
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", "shipper-token");

        // Act
        var response = await _client.GetAsync($"/api/rating/{ratingId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        content.Should().Contain(ratingId.ToString());
        content.Should().Contain("Test Transport Company");
    }

    [Fact]
    public async Task GetServiceProviderRating_WithInvalidId_ShouldReturnNotFound()
    {
        // Arrange
        var invalidId = Guid.NewGuid();
        
        _client.DefaultRequestHeaders.Authorization = 
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", "shipper-token");

        // Act
        var response = await _client.GetAsync($"/api/rating/{invalidId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task SubmitServiceProviderRating_WithValidId_ShouldReturnOk()
    {
        // Arrange
        var ratingId = await CreateTestRating();
        
        _client.DefaultRequestHeaders.Authorization = 
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", "shipper-token");

        // Act
        var response = await _client.PostAsync($"/api/rating/{ratingId}/submit", null);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        content.Should().Contain("Service provider rating submitted");
    }

    [Fact]
    public async Task FlagServiceProviderRating_WithValidData_ShouldReturnOk()
    {
        // Arrange
        var ratingId = await CreateTestRating();
        var flagCommand = new { Reason = "Inappropriate content" };
        
        _client.DefaultRequestHeaders.Authorization = 
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", "admin-token");

        // Act
        var response = await _client.PostAsJsonAsync($"/api/rating/{ratingId}/flag", flagCommand);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        content.Should().Contain("Service provider rating flagged for review");
    }

    [Fact]
    public async Task ReportServiceIssue_WithValidData_ShouldReturnOk()
    {
        // Arrange
        var ratingId = await CreateTestRating();
        var issueCommand = new ReportServiceIssueCommand
        {
            ServiceProviderRatingId = ratingId,
            IssueType = ServiceIssueType.DelayedDelivery,
            Priority = IssuePriority.High,
            Description = "Package was delivered 2 days late",
            Evidence = "Tracking number: ABC123"
        };
        
        _client.DefaultRequestHeaders.Authorization = 
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", "shipper-token");

        // Act
        var response = await _client.PostAsJsonAsync("/api/rating/issues", issueCommand);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        content.Should().Contain("IssueId");
    }

    [Fact]
    public async Task GetTransportCompanyRatingSummary_WithValidId_ShouldReturnSummary()
    {
        // Arrange
        var transportCompanyId = Guid.NewGuid();
        await CreateTestRatingForCompany(transportCompanyId);
        
        _client.DefaultRequestHeaders.Authorization = 
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", "test-token");

        // Act
        var response = await _client.GetAsync($"/api/rating/transport-company/{transportCompanyId}/summary");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        content.Should().Contain("TransportCompanyId");
        content.Should().Contain("AverageRating");
        content.Should().Contain("TotalRatings");
    }

    [Fact]
    public async Task CreateServiceProviderRating_WithoutAuthorization_ShouldReturnUnauthorized()
    {
        // Arrange
        var command = new CreateServiceProviderRatingCommand
        {
            TransportCompanyId = Guid.NewGuid(),
            TransportCompanyName = "Test Transport Company",
            OverallRating = 4.5m,
            ServiceCompletedAt = DateTime.UtcNow.AddDays(-1)
        };

        // Act (without authorization header)
        var response = await _client.PostAsJsonAsync("/api/rating", command);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
    }

    [Fact]
    public async Task SearchServiceProviderRatings_WithValidQuery_ShouldReturnResults()
    {
        // Arrange
        await CreateTestRating();
        
        var query = new
        {
            PageNumber = 1,
            PageSize = 10
        };

        _client.DefaultRequestHeaders.Authorization = 
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", "admin-token");

        // Act
        var response = await _client.PostAsJsonAsync("/api/rating/search", query);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        content.Should().Contain("Ratings");
        content.Should().Contain("TotalCount");
    }

    private async Task<Guid> CreateTestRating()
    {
        var command = new CreateServiceProviderRatingCommand
        {
            TransportCompanyId = Guid.NewGuid(),
            TransportCompanyName = "Test Transport Company",
            OverallRating = 4.5m,
            ReviewTitle = "Great service",
            ReviewComment = "Very professional and timely delivery",
            ServiceCompletedAt = DateTime.UtcNow.AddDays(-1),
            CategoryRatings = new List<CreateCategoryRatingDto>
            {
                new() { Category = RatingCategory.Timeliness, Rating = 5.0m },
                new() { Category = RatingCategory.Communication, Rating = 4.0m }
            }
        };

        _client.DefaultRequestHeaders.Authorization = 
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", "shipper-token");

        var response = await _client.PostAsJsonAsync("/api/rating", command);
        var content = await response.Content.ReadAsStringAsync();
        
        // Extract the GUID from the response (simplified for testing)
        var startIndex = content.IndexOf("\"RatingId\":\"") + 12;
        var endIndex = content.IndexOf("\"", startIndex);
        var guidString = content.Substring(startIndex, endIndex - startIndex);
        
        return Guid.Parse(guidString);
    }

    private async Task<Guid> CreateTestRatingForCompany(Guid transportCompanyId)
    {
        var command = new CreateServiceProviderRatingCommand
        {
            TransportCompanyId = transportCompanyId,
            TransportCompanyName = "Test Transport Company",
            OverallRating = 4.0m,
            ServiceCompletedAt = DateTime.UtcNow.AddDays(-1)
        };

        _client.DefaultRequestHeaders.Authorization = 
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", "shipper-token");

        var response = await _client.PostAsJsonAsync("/api/rating", command);
        var content = await response.Content.ReadAsStringAsync();
        
        var startIndex = content.IndexOf("\"RatingId\":\"") + 12;
        var endIndex = content.IndexOf("\"", startIndex);
        var guidString = content.Substring(startIndex, endIndex - startIndex);
        
        return Guid.Parse(guidString);
    }
}
