using AutoMapper;
using MediatR;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Repositories;

namespace NetworkFleetManagement.Application.Queries.Carriers;

public class GetCarrierByIdQueryHandler : IRequestHandler<GetCarrierByIdQuery, CarrierDto?>
{
    private readonly ICarrierRepository _carrierRepository;
    private readonly IMapper _mapper;

    public GetCarrierByIdQueryHandler(ICarrierRepository carrierRepository, IMapper mapper)
    {
        _carrierRepository = carrierRepository;
        _mapper = mapper;
    }

    public async Task<CarrierDto?> Handle(GetCarrierByIdQuery request, CancellationToken cancellationToken)
    {
        var carrier = await _carrierRepository.GetByIdAsync(request.CarrierId, cancellationToken);
        return carrier != null ? _mapper.Map<CarrierDto>(carrier) : null;
    }
}
