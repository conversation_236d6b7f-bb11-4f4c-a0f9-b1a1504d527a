using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Domain.Entities;

namespace FinancialPayment.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class ReconciliationController : ControllerBase
{
    private readonly IReconciliationService _reconciliationService;
    private readonly ILogger<ReconciliationController> _logger;

    public ReconciliationController(
        IReconciliationService reconciliationService,
        ILogger<ReconciliationController> logger)
    {
        _reconciliationService = reconciliationService;
        _logger = logger;
    }

    /// <summary>
    /// Start a new reconciliation process
    /// </summary>
    [HttpPost("start")]
    [Authorize(Roles = "Admin,Finance")]
    public async Task<ActionResult<PaymentReconciliation>> StartReconciliation([FromBody] StartReconciliationRequest request)
    {
        try
        {
            var reconciliationRequest = new ReconciliationRequest
            {
                PeriodStart = request.PeriodStart,
                PeriodEnd = request.PeriodEnd,
                PaymentGateway = request.PaymentGateway,
                Currency = request.Currency,
                AutoProcess = request.AutoProcess,
                ToleranceAmount = request.ToleranceAmount,
                ToleranceHours = request.ToleranceHours
            };

            var result = await _reconciliationService.StartReconciliationAsync(reconciliationRequest);
            
            _logger.LogInformation("Reconciliation started: {ReconciliationNumber} for gateway {Gateway}",
                result.ReconciliationNumber, request.PaymentGateway);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting reconciliation for gateway {Gateway}", request.PaymentGateway);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Process an existing reconciliation
    /// </summary>
    [HttpPost("{reconciliationId}/process")]
    [Authorize(Roles = "Admin,Finance")]
    public async Task<ActionResult<PaymentReconciliation>> ProcessReconciliation(Guid reconciliationId)
    {
        try
        {
            var result = await _reconciliationService.ProcessReconciliationAsync(reconciliationId);
            
            _logger.LogInformation("Reconciliation processed: {ReconciliationId}", reconciliationId);
            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid reconciliation operation for {ReconciliationId}", reconciliationId);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing reconciliation {ReconciliationId}", reconciliationId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Complete a reconciliation
    /// </summary>
    [HttpPost("{reconciliationId}/complete")]
    [Authorize(Roles = "Admin,Finance")]
    public async Task<ActionResult<PaymentReconciliation>> CompleteReconciliation(
        Guid reconciliationId, 
        [FromBody] CompleteReconciliationRequest request)
    {
        try
        {
            var result = await _reconciliationService.CompleteReconciliationAsync(
                reconciliationId, 
                request.CompletedBy, 
                request.Notes);
            
            _logger.LogInformation("Reconciliation completed: {ReconciliationId} by {CompletedBy}",
                reconciliationId, request.CompletedBy);

            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid reconciliation completion for {ReconciliationId}", reconciliationId);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error completing reconciliation {ReconciliationId}", reconciliationId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get reconciliation summary
    /// </summary>
    [HttpGet("{reconciliationId}/summary")]
    public async Task<ActionResult<ReconciliationSummary>> GetReconciliationSummary(Guid reconciliationId)
    {
        try
        {
            var summary = await _reconciliationService.GetReconciliationSummaryAsync(reconciliationId);
            return Ok(summary);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Reconciliation not found {ReconciliationId}", reconciliationId);
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting reconciliation summary for {ReconciliationId}", reconciliationId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get reconciliation history
    /// </summary>
    [HttpGet("history")]
    public async Task<ActionResult<List<PaymentReconciliation>>> GetReconciliationHistory(
        [FromQuery] string? paymentGateway = null,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        try
        {
            var history = await _reconciliationService.GetReconciliationHistoryAsync(paymentGateway, fromDate, toDate);
            return Ok(history);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting reconciliation history");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get unresolved discrepancies
    /// </summary>
    [HttpGet("discrepancies/unresolved")]
    public async Task<ActionResult<List<ReconciliationDiscrepancy>>> GetUnresolvedDiscrepancies(
        [FromQuery] string? paymentGateway = null)
    {
        try
        {
            var discrepancies = await _reconciliationService.GetUnresolvedDiscrepanciesAsync(paymentGateway);
            return Ok(discrepancies);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting unresolved discrepancies");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Resolve a discrepancy
    /// </summary>
    [HttpPost("discrepancies/{discrepancyId}/resolve")]
    [Authorize(Roles = "Admin,Finance")]
    public async Task<ActionResult<bool>> ResolveDiscrepancy(
        Guid discrepancyId, 
        [FromBody] ResolveDiscrepancyRequest request)
    {
        try
        {
            var result = await _reconciliationService.ResolveDiscrepancyAsync(
                discrepancyId, 
                request.Resolution, 
                request.ResolvedBy);
            
            if (result)
            {
                _logger.LogInformation("Discrepancy resolved: {DiscrepancyId} by {ResolvedBy}",
                    discrepancyId, request.ResolvedBy);
                return Ok(result);
            }
            else
            {
                return BadRequest("Failed to resolve discrepancy");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resolving discrepancy {DiscrepancyId}", discrepancyId);
            return StatusCode(500, "Internal server error");
        }
    }
}

// Request DTOs
public class StartReconciliationRequest
{
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
    public string PaymentGateway { get; set; } = string.Empty;
    public string Currency { get; set; } = "INR";
    public bool AutoProcess { get; set; } = true;
    public decimal ToleranceAmount { get; set; } = 0.01m;
    public int ToleranceHours { get; set; } = 24;
}

public class CompleteReconciliationRequest
{
    public Guid CompletedBy { get; set; }
    public string? Notes { get; set; }
}

public class ResolveDiscrepancyRequest
{
    public string Resolution { get; set; } = string.Empty;
    public Guid ResolvedBy { get; set; }
}
