using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Domain.Enums;
using MediatR;

namespace AnalyticsBIService.Application.Queries.Dashboard;

/// <summary>
/// Query to get dashboard metrics for a specific user
/// </summary>
public class GetDashboardMetricsQuery : IRequest<DashboardMetricsDto>
{
    public Guid? UserId { get; set; }
    public UserType? UserType { get; set; }
    public bool ForceRefresh { get; set; } = false;

    public GetDashboardMetricsQuery(Guid? userId = null, UserType? userType = null, bool forceRefresh = false)
    {
        UserId = userId;
        UserType = userType;
        ForceRefresh = forceRefresh;
    }
}

/// <summary>
/// Query to get average order fulfillment time
/// </summary>
public class GetAverageOrderFulfillmentTimeQuery : IRequest<decimal>
{
    public Guid? UserId { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }

    public GetAverageOrderFulfillmentTimeQuery(Guid? userId = null, DateTime? fromDate = null, DateTime? toDate = null)
    {
        UserId = userId;
        FromDate = fromDate;
        ToDate = toDate;
    }
}

/// <summary>
/// Query to get order fulfillment trends
/// </summary>
public class GetOrderFulfillmentTrendsQuery : IRequest<List<MetricDataPointDto>>
{
    public Guid? UserId { get; set; }
    public TimePeriod Period { get; set; } = TimePeriod.Daily;
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }

    public GetOrderFulfillmentTrendsQuery(Guid? userId = null, TimePeriod period = TimePeriod.Daily, DateTime? fromDate = null, DateTime? toDate = null)
    {
        UserId = userId;
        Period = period;
        FromDate = fromDate;
        ToDate = toDate;
    }
}

/// <summary>
/// Query to get real-time metrics
/// </summary>
public class GetRealTimeMetricsQuery : IRequest<Dictionary<string, decimal>>
{
    public Guid? UserId { get; set; }

    public GetRealTimeMetricsQuery(Guid? userId = null)
    {
        UserId = userId;
    }
}
