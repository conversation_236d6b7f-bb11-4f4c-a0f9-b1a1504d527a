using System.ComponentModel.DataAnnotations;

namespace AnalyticsBIService.Application.DTOs;

/// <summary>
/// Carrier performance analytics data transfer object
/// </summary>
public class CarrierPerformanceAnalyticsDto
{
    public string CarrierId { get; set; } = string.Empty;
    public string CarrierName { get; set; } = string.Empty;
    public decimal OnTimeDeliveryRate { get; set; }
    public decimal AverageDeliveryTime { get; set; }
    public decimal DamageRate { get; set; }
    public decimal CustomerSatisfactionScore { get; set; }
    public int TotalTrips { get; set; }
    public int CompletedTrips { get; set; }
    public int CancelledTrips { get; set; }
    public decimal TotalRevenue { get; set; }
    public decimal AverageRevenuePerTrip { get; set; }
    public decimal FuelEfficiency { get; set; }
    public decimal MaintenanceCost { get; set; }
    public DateTime AnalyticsPeriodStart { get; set; }
    public DateTime AnalyticsPeriodEnd { get; set; }
    public List<CarrierKPIDto> KPIs { get; set; } = new();
    public List<CarrierTrendDto> Trends { get; set; } = new();

    // Properties required by GetCarrierAnalyticsQueryHandler
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;
    public decimal DeliveryCompletionRate { get; set; }
    public decimal PerformanceScore { get; set; }
    public decimal ReliabilityScore { get; set; }
    public decimal EfficiencyScore { get; set; }
    public decimal QualityScore { get; set; }
    public int TotalTripsCompleted { get; set; }
    public decimal VehicleUtilizationRate { get; set; }
    public List<CarrierPerformanceTrendDto> PerformanceTrends { get; set; } = new();
    public List<BrokerFeedbackDto> BrokerFeedback { get; set; } = new();
    public List<ImprovementAreaDto> ImprovementAreas { get; set; } = new();
}

/// <summary>
/// Carrier KPI data transfer object
/// </summary>
public class CarrierKPIDto
{
    public string KPIName { get; set; } = string.Empty;
    public decimal CurrentValue { get; set; }
    public decimal TargetValue { get; set; }
    public decimal PreviousValue { get; set; }
    public string Unit { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty; // "On Track", "At Risk", "Behind"
    public decimal VarianceFromTarget { get; set; }
    public decimal VarianceFromPrevious { get; set; }
}

/// <summary>
/// Carrier trend data transfer object
/// </summary>
public class CarrierTrendDto
{
    public string MetricName { get; set; } = string.Empty;
    public string TrendDirection { get; set; } = string.Empty; // "Improving", "Declining", "Stable"
    public decimal TrendPercentage { get; set; }
    public List<TrendDataPointDto> DataPoints { get; set; } = new();
    public string TimeFrame { get; set; } = string.Empty; // "Daily", "Weekly", "Monthly"
}

// TrendDataPointDto is already defined in AdvancedReportingDto.cs - using that instead

/// <summary>
/// Carrier fleet analytics data transfer object
/// </summary>
public class CarrierFleetAnalyticsDto
{
    public string CarrierId { get; set; } = string.Empty;
    public int TotalVehicles { get; set; }
    public int ActiveVehicles { get; set; }
    public int MaintenanceVehicles { get; set; }
    public decimal FleetUtilizationRate { get; set; }
    public decimal AverageVehicleAge { get; set; }
    public decimal TotalMileage { get; set; }
    public decimal AverageMileagePerVehicle { get; set; }
    public decimal FuelConsumption { get; set; }
    public decimal MaintenanceCosts { get; set; }
    public List<VehiclePerformanceDto> VehiclePerformance { get; set; } = new();
}

/// <summary>
/// Vehicle performance data transfer object
/// </summary>
public class VehiclePerformanceDto
{
    public string VehicleId { get; set; } = string.Empty;
    public string VehicleNumber { get; set; } = string.Empty;
    public string VehicleType { get; set; } = string.Empty;
    public decimal Mileage { get; set; }
    public decimal FuelEfficiency { get; set; }
    public decimal UtilizationRate { get; set; }
    public int TripsCompleted { get; set; }
    public decimal MaintenanceCost { get; set; }
    public DateTime LastMaintenanceDate { get; set; }
    public DateTime NextMaintenanceDate { get; set; }
    public string Status { get; set; } = string.Empty;
}

/// <summary>
/// Carrier driver analytics data transfer object
/// </summary>
public class CarrierDriverAnalyticsDto
{
    public string CarrierId { get; set; } = string.Empty;
    public int TotalDrivers { get; set; }
    public int ActiveDrivers { get; set; }
    public int InactiveDrivers { get; set; }
    public decimal AverageDriverRating { get; set; }
    public decimal DriverRetentionRate { get; set; }
    public decimal AverageTripsPerDriver { get; set; }
    public decimal AverageRevenuePerDriver { get; set; }
    public List<DriverPerformanceSummaryDto> TopPerformers { get; set; } = new();
    public List<DriverPerformanceSummaryDto> UnderPerformers { get; set; } = new();
}

/// <summary>
/// Driver performance summary data transfer object
/// </summary>
public class DriverPerformanceSummaryDto
{
    public string DriverId { get; set; } = string.Empty;
    public string DriverName { get; set; } = string.Empty;
    public decimal Rating { get; set; }
    public int TripsCompleted { get; set; }
    public decimal OnTimeRate { get; set; }
    public decimal SafetyScore { get; set; }
    public decimal Revenue { get; set; }
    public string Status { get; set; } = string.Empty;
}

/// <summary>
/// Carrier financial analytics data transfer object
/// </summary>
public class CarrierFinancialAnalyticsDto
{
    public string CarrierId { get; set; } = string.Empty;
    public decimal TotalRevenue { get; set; }
    public decimal TotalExpenses { get; set; }
    public decimal NetProfit { get; set; }
    public decimal ProfitMargin { get; set; }
    public decimal OperatingCosts { get; set; }
    public decimal FuelCosts { get; set; }
    public decimal MaintenanceCosts { get; set; }
    public decimal DriverCosts { get; set; }
    public decimal InsuranceCosts { get; set; }
    public decimal AverageRevenuePerMile { get; set; }
    public decimal AverageCostPerMile { get; set; }
    public List<FinancialTrendDto> RevenueTrends { get; set; } = new();
    public List<FinancialTrendDto> ExpenseTrends { get; set; } = new();
}

/// <summary>
/// Financial trend data transfer object
/// </summary>
public class FinancialTrendDto
{
    public string Category { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public DateTime Date { get; set; }
    public decimal PercentageChange { get; set; }
    public string TrendDirection { get; set; } = string.Empty;
}

/// <summary>
/// Carrier route analytics data transfer object
/// </summary>
public class CarrierRouteAnalyticsDto
{
    public string CarrierId { get; set; } = string.Empty;
    public List<RoutePerformanceDto> TopRoutes { get; set; } = new();
    public List<RoutePerformanceDto> UnderperformingRoutes { get; set; } = new();
    public decimal AverageRouteDistance { get; set; }
    public decimal AverageRouteTime { get; set; }
    public decimal RouteOptimizationScore { get; set; }
    public int TotalUniqueRoutes { get; set; }
}

/// <summary>
/// Route performance data transfer object
/// </summary>
public class RoutePerformanceDto
{
    public string RouteId { get; set; } = string.Empty;
    public string OriginCity { get; set; } = string.Empty;
    public string DestinationCity { get; set; } = string.Empty;
    public decimal Distance { get; set; }
    public decimal AverageTime { get; set; }
    public decimal Revenue { get; set; }
    public int TripCount { get; set; }
    public decimal OnTimeRate { get; set; }
    public decimal FuelEfficiency { get; set; }
    public decimal Profitability { get; set; }
}

/// <summary>
/// Carrier compliance analytics data transfer object
/// </summary>
public class CarrierComplianceAnalyticsDto
{
    public string CarrierId { get; set; } = string.Empty;
    public decimal ComplianceScore { get; set; }
    public int TotalViolations { get; set; }
    public int ResolvedViolations { get; set; }
    public int PendingViolations { get; set; }
    public List<ComplianceViolationDto> RecentViolations { get; set; } = new();
    public List<ComplianceCategoryDto> ComplianceByCategory { get; set; } = new();
    public DateTime LastAuditDate { get; set; }
    public DateTime NextAuditDate { get; set; }
}

// ComplianceViolationDto is already defined in AdminAnalyticsDto.cs - using that instead

/// <summary>
/// Compliance category data transfer object
/// </summary>
public class ComplianceCategoryDto
{
    public string Category { get; set; } = string.Empty;
    public decimal Score { get; set; }
    public int ViolationCount { get; set; }
    public string Status { get; set; } = string.Empty;
    public List<string> RequiredActions { get; set; } = new();
}

// BrokerFeedbackDto and ImprovementAreaDto are already defined in other DTO files
// CarrierPerformanceTrendDto is already defined in CarrierAnalyticsDto.cs - using that instead
