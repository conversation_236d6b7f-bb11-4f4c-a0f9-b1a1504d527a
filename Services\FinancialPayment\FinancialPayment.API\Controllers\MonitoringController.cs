using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Domain.Entities;

namespace FinancialPayment.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class MonitoringController : ControllerBase
{
    private readonly IMonitoringService _monitoringService;
    private readonly IUsageAnalyticsService _usageAnalyticsService;
    private readonly IHealthCheckService _healthCheckService;
    private readonly ILogger<MonitoringController> _logger;

    public MonitoringController(
        IMonitoringService monitoringService,
        IUsageAnalyticsService usageAnalyticsService,
        IHealthCheckService healthCheckService,
        ILogger<MonitoringController> logger)
    {
        _monitoringService = monitoringService;
        _usageAnalyticsService = usageAnalyticsService;
        _healthCheckService = healthCheckService;
        _logger = logger;
    }

    /// <summary>
    /// Get system health summary
    /// </summary>
    [HttpGet("health")]
    [AllowAnonymous]
    public async Task<ActionResult<SystemHealthSummary>> GetSystemHealth()
    {
        try
        {
            var health = await _monitoringService.GetSystemHealthAsync();
            return Ok(health);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting system health");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get detailed health checks
    /// </summary>
    [HttpGet("health/detailed")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<List<HealthCheck>>> GetDetailedHealthChecks([FromQuery] string? serviceName = null)
    {
        try
        {
            var healthChecks = await _healthCheckService.GetHealthChecksAsync(serviceName);
            return Ok(healthChecks);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting detailed health checks");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Perform health check for a specific service
    /// </summary>
    [HttpPost("health/check")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<HealthCheck>> PerformHealthCheck([FromBody] PerformHealthCheckRequest request)
    {
        try
        {
            var healthCheck = await _healthCheckService.PerformHealthCheckAsync(request.ServiceName, request.CheckName);
            return Ok(healthCheck);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing health check for {ServiceName}/{CheckName}", 
                request.ServiceName, request.CheckName);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get system metrics
    /// </summary>
    [HttpGet("metrics")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<List<SystemMetric>>> GetMetrics(
        [FromQuery] string? metricName = null,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        try
        {
            var metrics = await _monitoringService.GetMetricsAsync(metricName, fromDate, toDate);
            return Ok(metrics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting metrics");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get performance metrics
    /// </summary>
    [HttpGet("performance")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<List<PerformanceMetric>>> GetPerformanceMetrics(
        [FromQuery] string? operationName = null,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        try
        {
            var metrics = await _monitoringService.GetPerformanceMetricsAsync(operationName, fromDate, toDate);
            return Ok(metrics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting performance metrics");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get active alerts
    /// </summary>
    [HttpGet("alerts")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<List<Alert>>> GetActiveAlerts()
    {
        try
        {
            var alerts = await _monitoringService.GetActiveAlertsAsync();
            return Ok(alerts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active alerts");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Create an alert
    /// </summary>
    [HttpPost("alerts")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<Alert>> CreateAlert([FromBody] CreateAlertRequest request)
    {
        try
        {
            var alert = await _monitoringService.CreateAlertAsync(
                request.AlertName,
                request.Severity,
                request.Message,
                request.Source,
                request.AlertData);

            return CreatedAtAction(nameof(GetActiveAlerts), alert);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating alert {AlertName}", request.AlertName);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Acknowledge an alert
    /// </summary>
    [HttpPost("alerts/{alertId}/acknowledge")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult> AcknowledgeAlert(Guid alertId, [FromBody] AcknowledgeAlertRequest request)
    {
        try
        {
            var success = await _monitoringService.AcknowledgeAlertAsync(alertId, request.AcknowledgedBy, request.Notes);
            if (!success)
            {
                return NotFound($"Alert {alertId} not found");
            }

            return Ok(new { message = "Alert acknowledged successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error acknowledging alert {AlertId}", alertId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Resolve an alert
    /// </summary>
    [HttpPost("alerts/{alertId}/resolve")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult> ResolveAlert(Guid alertId, [FromBody] ResolveAlertRequest request)
    {
        try
        {
            var success = await _monitoringService.ResolveAlertAsync(alertId, request.ResolutionNotes);
            if (!success)
            {
                return NotFound($"Alert {alertId} not found");
            }

            return Ok(new { message = "Alert resolved successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resolving alert {AlertId}", alertId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get usage analytics summary
    /// </summary>
    [HttpGet("usage/summary")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<UsageAnalyticsSummary>> GetUsageAnalytics(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] string? resourceType = null)
    {
        try
        {
            var analytics = await _usageAnalyticsService.GetUsageAnalyticsAsync(fromDate, toDate, resourceType);
            return Ok(analytics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting usage analytics");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get top used resources
    /// </summary>
    [HttpGet("usage/top-resources")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<List<TopUsedResource>>> GetTopUsedResources(
        [FromQuery] string resourceType,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] int limit = 10)
    {
        try
        {
            var topResources = await _usageAnalyticsService.GetTopUsedResourcesAsync(resourceType, fromDate, toDate, limit);
            return Ok(topResources);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting top used resources");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get user activity summary
    /// </summary>
    [HttpGet("usage/user-activity")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<List<UserActivitySummary>>> GetUserActivity(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] int limit = 100)
    {
        try
        {
            var userActivity = await _usageAnalyticsService.GetUserActivityAsync(fromDate, toDate, limit);
            return Ok(userActivity);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user activity");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get API usage metrics
    /// </summary>
    [HttpGet("usage/api")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<ApiUsageMetrics>> GetApiUsageMetrics(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        try
        {
            var apiMetrics = await _usageAnalyticsService.GetApiUsageMetricsAsync(fromDate, toDate);
            return Ok(apiMetrics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting API usage metrics");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Create monitoring dashboard
    /// </summary>
    [HttpPost("dashboards")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<MonitoringDashboard>> CreateDashboard([FromBody] CreateMonitoringDashboardRequest request)
    {
        try
        {
            var dashboard = await _monitoringService.CreateDashboardAsync(request);
            
            _logger.LogInformation("Monitoring dashboard created: {DashboardName}", request.DashboardName);
            return CreatedAtAction(nameof(GetDashboard), new { id = dashboard.Id }, dashboard);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating monitoring dashboard");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get monitoring dashboards
    /// </summary>
    [HttpGet("dashboards")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<List<MonitoringDashboard>>> GetDashboards([FromQuery] Guid? userId = null)
    {
        try
        {
            var dashboards = await _monitoringService.GetDashboardsAsync(userId);
            return Ok(dashboards);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting monitoring dashboards");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get monitoring dashboard by ID
    /// </summary>
    [HttpGet("dashboards/{id}")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<MonitoringDashboard>> GetDashboard(Guid id)
    {
        try
        {
            var dashboard = await _monitoringService.GetDashboardAsync(id);
            if (dashboard == null)
            {
                return NotFound($"Monitoring dashboard {id} not found");
            }

            return Ok(dashboard);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting monitoring dashboard {DashboardId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Record custom metric
    /// </summary>
    [HttpPost("metrics")]
    [Authorize(Roles = "Admin,System")]
    public async Task<ActionResult> RecordMetric([FromBody] RecordMetricRequest request)
    {
        try
        {
            await _monitoringService.RecordMetricAsync(
                request.MetricName,
                request.Value,
                request.Unit,
                request.Source,
                request.Tags);

            return Ok(new { message = "Metric recorded successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording metric {MetricName}", request.MetricName);
            return StatusCode(500, "Internal server error");
        }
    }
}

// Request DTOs
public class PerformHealthCheckRequest
{
    public string ServiceName { get; set; } = string.Empty;
    public string CheckName { get; set; } = string.Empty;
}

public class CreateAlertRequest
{
    public string AlertName { get; set; } = string.Empty;
    public AlertSeverity Severity { get; set; }
    public string Message { get; set; } = string.Empty;
    public string Source { get; set; } = string.Empty;
    public Dictionary<string, object> AlertData { get; set; } = new();
}

public class AcknowledgeAlertRequest
{
    public Guid AcknowledgedBy { get; set; }
    public string? Notes { get; set; }
}

public class ResolveAlertRequest
{
    public string? ResolutionNotes { get; set; }
}

public class RecordMetricRequest
{
    public string MetricName { get; set; } = string.Empty;
    public decimal Value { get; set; }
    public string Unit { get; set; } = string.Empty;
    public string Source { get; set; } = string.Empty;
    public Dictionary<string, string> Tags { get; set; } = new();
}
