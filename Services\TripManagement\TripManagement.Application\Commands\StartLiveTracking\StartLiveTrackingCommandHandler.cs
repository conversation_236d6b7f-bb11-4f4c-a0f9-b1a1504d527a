using Shared.Domain.Common;
using Microsoft.Extensions.Logging;
using Shared.Domain.Common;
using Microsoft.Extensions.Configuration;
using Shared.Domain.Common;
using Shared.Messaging;
using Shared.Domain.Common;
using TripManagement.Domain.Repositories;
using Shared.Domain.Common;
using TripManagement.Domain.Entities;
using Shared.Domain.Common;
using TripManagement.Application.Services;
using Shared.Domain.Common;

namespace TripManagement.Application.Commands.StartLiveTracking;

public class StartLiveTrackingCommandHandler : IRequestHandler<StartLiveTrackingCommand, StartLiveTrackingResponse>
{
    private readonly ITripRepository _tripRepository;
    private readonly ITrackingSessionRepository _trackingSessionRepository;
    private readonly IGeofencingService _geofencingService;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMessageBroker _messageBroker;
    private readonly IConfiguration _configuration;
    private readonly ILogger<StartLiveTrackingCommandHandler> _logger;

    public StartLiveTrackingCommandHandler(
        ITripRepository tripRepository,
        ITrackingSessionRepository trackingSessionRepository,
        IGeofencingService geofencingService,
        IUnitOfWork unitOfWork,
        IMessageBroker messageBroker,
        IConfiguration configuration,
        ILogger<StartLiveTrackingCommandHandler> logger)
    {
        _tripRepository = tripRepository;
        _trackingSessionRepository = trackingSessionRepository;
        _geofencingService = geofencingService;
        _unitOfWork = unitOfWork;
        _messageBroker = messageBroker;
        _configuration = configuration;
        _logger = logger;
    }

    public async Task<StartLiveTrackingResponse> Handle(StartLiveTrackingCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Starting live tracking for trip {TripId} with driver {DriverId}", 
            request.TripId, request.DriverId);

        try
        {
            // Get trip details
            var trip = await _tripRepository.GetByIdAsync(request.TripId, cancellationToken);
            if (trip == null)
            {
                return new StartLiveTrackingResponse
                {
                    Success = false,
                    Message = $"Trip {request.TripId} not found"
                };
            }

            // Validate driver assignment
            if (trip.DriverId != request.DriverId)
            {
                return new StartLiveTrackingResponse
                {
                    Success = false,
                    Message = "Driver is not assigned to this trip"
                };
            }

            // Check if tracking is already active
            var existingSession = await _trackingSessionRepository.GetActiveSessionByTripIdAsync(request.TripId, cancellationToken);
            if (existingSession != null)
            {
                return new StartLiveTrackingResponse
                {
                    Success = false,
                    Message = "Live tracking is already active for this trip",
                    TrackingSessionId = existingSession.Id
                };
            }

            // Create tracking session
            var trackingSession = new TrackingSession(
                request.TripId,
                request.DriverId,
                request.TrackingIntervalSeconds,
                request.EnableGeofencing,
                request.EnableETAUpdates,
                request.EnableSpeedMonitoring,
                request.EnableRouteDeviation);

            await _trackingSessionRepository.AddAsync(trackingSession);

            // Setup geofences if enabled
            var activeGeofences = new List<GeofenceZoneDto>();
            if (request.EnableGeofencing)
            {
                var geofenceZones = await _geofencingService.GetActiveZonesForTripAsync(request.TripId, cancellationToken);
                activeGeofences = geofenceZones.Select(z => new GeofenceZoneDto
                {
                    Id = z.Id,
                    Name = z.Name,
                    ZoneType = z.ZoneType.ToString(),
                    CenterLocation = new LocationDto
                    {
                        Latitude = z.CenterLocation.Latitude,
                        Longitude = z.CenterLocation.Longitude,
                        Altitude = z.CenterLocation.Altitude
                    },
                    RadiusMeters = z.RadiusMeters,
                    IsActive = z.IsActive
                }).ToList();
            }

            // Save changes
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Publish tracking started event
            await _messageBroker.PublishAsync("trip.tracking.started", new
            {
                TripId = request.TripId,
                DriverId = request.DriverId,
                TrackingSessionId = trackingSession.Id,
                Configuration = new
                {
                    TrackingIntervalSeconds = request.TrackingIntervalSeconds,
                    GeofencingEnabled = request.EnableGeofencing,
                    ETAUpdatesEnabled = request.EnableETAUpdates,
                    SpeedMonitoringEnabled = request.EnableSpeedMonitoring,
                    RouteDeviationEnabled = request.EnableRouteDeviation
                },
                ActiveGeofences = activeGeofences.Count,
                StartedAt = DateTime.UtcNow,
                NotificationChannels = request.NotificationChannels
            }, cancellationToken);

            // Send notifications to stakeholders
            await SendTrackingStartedNotifications(trip, trackingSession, request.NotificationChannels, cancellationToken);

            var signalRUrl = _configuration["SignalR:HubUrl"] ?? "/hubs/triptracking";

            var response = new StartLiveTrackingResponse
            {
                Success = true,
                Message = "Live tracking started successfully",
                TrackingSessionId = trackingSession.Id,
                SignalRConnectionUrl = signalRUrl,
                ActiveGeofences = activeGeofences,
                Configuration = new TrackingConfigurationDto
                {
                    TrackingIntervalSeconds = request.TrackingIntervalSeconds,
                    GeofencingEnabled = request.EnableGeofencing,
                    ETAUpdatesEnabled = request.EnableETAUpdates,
                    SpeedMonitoringEnabled = request.EnableSpeedMonitoring,
                    RouteDeviationEnabled = request.EnableRouteDeviation,
                    MaxSpeedKmh = double.Parse(_configuration["Tracking:MaxSpeedKmh"] ?? "120"),
                    RouteDeviationThresholdMeters = double.Parse(_configuration["Tracking:RouteDeviationThresholdMeters"] ?? "500")
                },
                StartedAt = DateTime.UtcNow
            };

            _logger.LogInformation("Live tracking started successfully for trip {TripId}. Session ID: {SessionId}", 
                request.TripId, trackingSession.Id);

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting live tracking for trip {TripId}", request.TripId);
            return new StartLiveTrackingResponse
            {
                Success = false,
                Message = $"Error starting live tracking: {ex.Message}"
            };
        }
    }

    private async Task SendTrackingStartedNotifications(
        Trip trip, 
        TrackingSession trackingSession, 
        List<string> notificationChannels,
        CancellationToken cancellationToken)
    {
        try
        {
            if (!notificationChannels.Any())
                return;

            await _messageBroker.PublishAsync("communication.send.notification", new
            {
                MessageType = "LiveTrackingStarted",
                Subject = $"Live Tracking Started: Trip {trip.TripNumber}",
                Content = $"Live tracking has been activated for trip {trip.TripNumber}. You can now monitor real-time location updates.",
                Priority = "Medium",
                RelatedEntityId = trip.Id,
                RelatedEntityType = "Trip",
                Recipients = new[]
                {
                    new { UserId = trip.CarrierId, UserType = "Carrier" },
                    new { UserId = trip.OrderId, UserType = "Order" } // This should be mapped to actual order stakeholders
                },
                Channels = notificationChannels,
                Tags = new[] { "trip", "tracking", "live-updates" },
                Metadata = new
                {
                    TripId = trip.Id,
                    TripNumber = trip.TripNumber,
                    TrackingSessionId = trackingSession.Id,
                    DriverId = trip.DriverId,
                    VehicleId = trip.VehicleId,
                    StartedAt = DateTime.UtcNow
                }
            }, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to send tracking started notifications for trip {TripId}", trip.Id);
        }
    }
}

// Supporting entity that might need to be added to the domain
public class TrackingSession
{
    public Guid Id { get; private set; }
    public Guid TripId { get; private set; }
    public Guid DriverId { get; private set; }
    public int TrackingIntervalSeconds { get; private set; }
    public bool GeofencingEnabled { get; private set; }
    public bool ETAUpdatesEnabled { get; private set; }
    public bool SpeedMonitoringEnabled { get; private set; }
    public bool RouteDeviationEnabled { get; private set; }
    public DateTime StartedAt { get; private set; }
    public DateTime? EndedAt { get; private set; }
    public bool IsActive { get; private set; }

    public TrackingSession(
        Guid tripId,
        Guid driverId,
        int trackingIntervalSeconds,
        bool geofencingEnabled,
        bool etaUpdatesEnabled,
        bool speedMonitoringEnabled,
        bool routeDeviationEnabled)
    {
        Id = Guid.NewGuid();
        TripId = tripId;
        DriverId = driverId;
        TrackingIntervalSeconds = trackingIntervalSeconds;
        GeofencingEnabled = geofencingEnabled;
        ETAUpdatesEnabled = etaUpdatesEnabled;
        SpeedMonitoringEnabled = speedMonitoringEnabled;
        RouteDeviationEnabled = routeDeviationEnabled;
        StartedAt = DateTime.UtcNow;
        IsActive = true;
    }

    public void EndSession()
    {
        EndedAt = DateTime.UtcNow;
        IsActive = false;
    }
}

// Repository interface that might need to be added
public interface ITrackingSessionRepository
{
    Task<TrackingSession?> GetActiveSessionByTripIdAsync(Guid tripId, CancellationToken cancellationToken = default);
    Task AddAsync(TrackingSession trackingSession);
    Task UpdateAsync(TrackingSession trackingSession);
}

