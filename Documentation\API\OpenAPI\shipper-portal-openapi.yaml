openapi: 3.0.3
info:
  title: TLI Shipper Portal API
  description: |
    Comprehensive RESTful API for the TLI Shipper Portal, providing enterprise-grade transportation logistics management capabilities across multiple microservices.
    
    ## Features
    - **Enhanced RFQ Management**: Advanced negotiation workflows and AI-powered quote comparison
    - **Multi-Leg Trip Management**: Complex trip planning with real-time tracking and milestone management
    - **Escrow Payment Integration**: Milestone-based payments with dispute resolution
    - **Preferred Partner Management**: AI-powered recommendations and performance analytics
    - **Comprehensive Reporting**: Cross-service analytics and business intelligence
    - **Real-time Notifications**: Event-driven communication system
    
    ## Authentication
    All endpoints require JWT Bearer token authentication unless otherwise specified.
    
    ## Rate Limiting
    - Standard Users: 1000 requests/hour
    - Premium Users: 5000 requests/hour
    - Enterprise Users: 10000 requests/hour
    
  version: 2.0.0
  contact:
    name: TLI API Support
    email: <EMAIL>
    url: https://docs.tli.com
  license:
    name: Proprietary
    url: https://tli.com/license
  termsOfService: https://tli.com/terms

servers:
  - url: https://api.tli.com
    description: Production server
  - url: https://staging-api.tli.com
    description: Staging server
  - url: https://dev-api.tli.com
    description: Development server

security:
  - BearerAuth: []

tags:
  - name: Order Management
    description: RFQ management, quotes, and negotiations
  - name: Trip Management
    description: Multi-leg trips, tracking, and milestones
  - name: Financial & Payment
    description: Escrow accounts, settlements, and invoicing
  - name: Network & Fleet
    description: Carriers, vehicles, and driver management
  - name: Analytics & BI
    description: Reports, dashboards, and business intelligence
  - name: User Management
    description: Authentication, profiles, and role management
  - name: Communication
    description: Notifications and messaging
  - name: Data & Storage
    description: File uploads and document management

paths:
  # Order Management Endpoints
  /api/orders/rfqs:
    post:
      tags:
        - Order Management
      summary: Create RFQ with Advanced Features
      description: |
        Creates a new Request for Quote (RFQ) with advanced features including:
        - Preferred carrier selection
        - Auto-assignment settings
        - Standard timeframe configuration
        - Budget range specification
      operationId: createAdvancedRFQ
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateRFQRequest'
            examples:
              standard_rfq:
                summary: Standard RFQ Example
                value:
                  shipperId: "550e8400-e29b-41d4-a716-446655440000"
                  title: "Freight shipment from NY to CA"
                  description: "Temperature-controlled shipment"
                  originLocation:
                    address: "123 Main St, New York, NY 10001"
                    latitude: 40.7128
                    longitude: -74.0060
                    contactPerson: "John Doe"
                    contactPhone: "******-0123"
                  destinationLocation:
                    address: "456 Oak Ave, Los Angeles, CA 90210"
                    latitude: 34.0522
                    longitude: -118.2437
                    contactPerson: "Jane Smith"
                    contactPhone: "******-0456"
                  loadDetails:
                    weight: 5000
                    volume: 100
                    dimensions:
                      length: 10
                      width: 8
                      height: 6
                    loadType: "General Freight"
                    specialRequirements: ["Temperature Controlled", "Fragile"]
                  scheduledPickupDate: "2024-02-01T09:00:00Z"
                  scheduledDeliveryDate: "2024-02-03T17:00:00Z"
                  budgetRange:
                    min: 2000
                    max: 3000
                    currency: "USD"
      responses:
        '201':
          description: RFQ created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateRFQResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '422':
          $ref: '#/components/responses/ValidationError'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /api/orders/quotes/compare:
    post:
      tags:
        - Order Management
      summary: Advanced Quote Comparison
      description: |
        Compares multiple quotes using AI-powered analysis with:
        - Weighted scoring criteria
        - Performance analytics
        - Intelligent recommendations
        - Risk assessment
      operationId: compareQuotes
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/QuoteComparisonRequest'
      responses:
        '200':
          description: Quote comparison completed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuoteComparisonResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'

  /api/orders/negotiations/counter-offer:
    post:
      tags:
        - Order Management
      summary: Submit Counter Offer
      description: |
        Submits a counter offer in the negotiation workflow with:
        - Price negotiation
        - Service modifications
        - Terms adjustment
        - Expiration management
      operationId: submitCounterOffer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CounterOfferRequest'
      responses:
        '201':
          description: Counter offer submitted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CounterOfferResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  # Trip Management Endpoints
  /api/trips/multi-leg:
    post:
      tags:
        - Trip Management
      summary: Create Multi-Leg Trip
      description: |
        Creates a complex multi-leg trip with:
        - Independent leg management
        - Resource allocation
        - Milestone configuration
        - Route optimization
      operationId: createMultiLegTrip
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateMultiLegTripRequest'
      responses:
        '201':
          description: Multi-leg trip created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateTripResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /api/trips/{tripId}/status/visualization:
    get:
      tags:
        - Trip Management
      summary: Get Enhanced Trip Status Visualization
      description: |
        Retrieves comprehensive trip status with:
        - Interactive timeline
        - Real-time location tracking
        - Delay alerts and notifications
        - Performance metrics
        - Exception handling
      operationId: getTripStatusVisualization
      parameters:
        - name: tripId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: Unique identifier for the trip
        - name: includeTimeline
          in: query
          schema:
            type: boolean
            default: true
          description: Include timeline visualization
        - name: includeMilestones
          in: query
          schema:
            type: boolean
            default: true
          description: Include milestone details
        - name: includeNotifications
          in: query
          schema:
            type: boolean
            default: true
          description: Include active notifications
        - name: includeDelayAlerts
          in: query
          schema:
            type: boolean
            default: true
          description: Include delay alerts
        - name: includeMetrics
          in: query
          schema:
            type: boolean
            default: true
          description: Include performance metrics
        - name: includeLocationTracking
          in: query
          schema:
            type: boolean
            default: true
          description: Include real-time location data
        - name: detailLevel
          in: query
          schema:
            type: string
            enum: [Basic, Standard, Detailed, Comprehensive]
            default: Standard
          description: Level of detail in the response
      responses:
        '200':
          description: Trip status visualization retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TripStatusVisualizationResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'

  # Financial & Payment Endpoints
  /api/payments/escrow:
    post:
      tags:
        - Financial & Payment
      summary: Create Escrow Account with Milestones
      description: |
        Creates an escrow account with milestone-based payment structure:
        - Multi-party settlement
        - Automated release conditions
        - Dispute resolution
        - Document verification
      operationId: createEscrowAccount
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateEscrowAccountRequest'
      responses:
        '201':
          description: Escrow account created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateEscrowAccountResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /api/payments/escrow/{escrowAccountId}/enhanced:
    get:
      tags:
        - Financial & Payment
      summary: Get Enhanced Escrow Account Details
      description: |
        Retrieves comprehensive escrow account information including:
        - Transaction history
        - Milestone progress
        - Document status
        - Dispute history
      operationId: getEnhancedEscrowDetails
      parameters:
        - name: escrowAccountId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: Unique identifier for the escrow account
        - name: includeTransactions
          in: query
          schema:
            type: boolean
            default: false
          description: Include transaction history
        - name: includeMilestoneDocuments
          in: query
          schema:
            type: boolean
            default: false
          description: Include milestone document details
        - name: includeDisputeHistory
          in: query
          schema:
            type: boolean
            default: false
          description: Include dispute history
      responses:
        '200':
          description: Enhanced escrow details retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EnhancedEscrowDetailsResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT Bearer token authentication

  responses:
    BadRequest:
      description: Bad request - Invalid input parameters
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            success: false
            error:
              code: "BAD_REQUEST"
              message: "Invalid request parameters"
              details: []
            timestamp: "2024-01-15T10:30:00Z"
            requestId: "req_123456789"

    Unauthorized:
      description: Unauthorized - Authentication required
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            success: false
            error:
              code: "UNAUTHORIZED"
              message: "Authentication required"
              details: []
            timestamp: "2024-01-15T10:30:00Z"
            requestId: "req_123456789"

    Forbidden:
      description: Forbidden - Insufficient permissions
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            success: false
            error:
              code: "FORBIDDEN"
              message: "Insufficient permissions"
              details: []
            timestamp: "2024-01-15T10:30:00Z"
            requestId: "req_123456789"

    NotFound:
      description: Not found - Resource does not exist
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            success: false
            error:
              code: "NOT_FOUND"
              message: "Resource not found"
              details: []
            timestamp: "2024-01-15T10:30:00Z"
            requestId: "req_123456789"

    ValidationError:
      description: Validation error - Invalid input data
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            success: false
            error:
              code: "VALIDATION_ERROR"
              message: "Invalid input data"
              details:
                - field: "email"
                  message: "Invalid email format"
                - field: "weight"
                  message: "Weight must be greater than 0"
            timestamp: "2024-01-15T10:30:00Z"
            requestId: "req_123456789"

    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            success: false
            error:
              code: "INTERNAL_SERVER_ERROR"
              message: "An unexpected error occurred"
              details: []
            timestamp: "2024-01-15T10:30:00Z"
            requestId: "req_123456789"

  schemas:
    # Base Response Schemas
    BaseResponse:
      type: object
      properties:
        success:
          type: boolean
          description: Indicates if the operation was successful
        message:
          type: string
          description: Human-readable message about the operation
        timestamp:
          type: string
          format: date-time
          description: ISO 8601 timestamp of the response
        requestId:
          type: string
          description: Unique identifier for the request
      required:
        - success
        - timestamp
        - requestId

    ErrorResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            error:
              type: object
              properties:
                code:
                  type: string
                  description: Error code
                message:
                  type: string
                  description: Error message
                details:
                  type: array
                  items:
                    type: object
                    properties:
                      field:
                        type: string
                      message:
                        type: string
              required:
                - code
                - message
          required:
            - error

    # Common Schemas
    Location:
      type: object
      properties:
        address:
          type: string
          description: Full street address
          example: "123 Main St, New York, NY 10001"
        latitude:
          type: number
          format: double
          minimum: -90
          maximum: 90
          description: Latitude coordinate
          example: 40.7128
        longitude:
          type: number
          format: double
          minimum: -180
          maximum: 180
          description: Longitude coordinate
          example: -74.0060
        city:
          type: string
          description: City name
          example: "New York"
        state:
          type: string
          description: State or province
          example: "NY"
        country:
          type: string
          description: Country code
          example: "US"
        postalCode:
          type: string
          description: Postal or ZIP code
          example: "10001"
        contactPerson:
          type: string
          description: Contact person at this location
          example: "John Doe"
        contactPhone:
          type: string
          description: Contact phone number
          example: "******-0123"
      required:
        - address
        - latitude
        - longitude

    Money:
      type: object
      properties:
        amount:
          type: number
          format: decimal
          minimum: 0
          description: Monetary amount
          example: 2500.00
        currency:
          type: string
          pattern: '^[A-Z]{3}$'
          description: ISO 4217 currency code
          example: "USD"
      required:
        - amount
        - currency
