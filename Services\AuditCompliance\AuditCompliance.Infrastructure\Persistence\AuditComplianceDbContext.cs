using Microsoft.EntityFrameworkCore;
using AuditCompliance.Domain.Entities;
using AuditCompliance.Domain.Enums;
using AuditCompliance.Domain.ValueObjects;
using System.Text.Json;

namespace AuditCompliance.Infrastructure.Persistence;

public class AuditComplianceDbContext : DbContext
{
    public AuditComplianceDbContext(DbContextOptions<AuditComplianceDbContext> options) : base(options)
    {
    }

    public DbSet<AuditLog> AuditLogs { get; set; } = null!;
    public DbSet<ComplianceReport> ComplianceReports { get; set; } = null!;
    public DbSet<ComplianceReportItem> ComplianceReportItems { get; set; } = null!;
    public DbSet<ServiceProviderRating> ServiceProviderRatings { get; set; } = null!;
    public DbSet<CategoryRating> CategoryRatings { get; set; } = null!;
    public DbSet<ServiceIssue> ServiceIssues { get; set; } = null!;
    public DbSet<PreferredProviderNetwork> PreferredProviderNetworks { get; set; } = null!;
    public DbSet<Tenant> Tenants { get; set; } = null!;

    // Enhanced audit & compliance entities
    public DbSet<ModuleRegistry> ModuleRegistries { get; set; } = null!;
    public DbSet<ModuleAccessControl> ModuleAccessControls { get; set; } = null!;
    public DbSet<CustomComplianceReportTemplate> CustomComplianceReportTemplates { get; set; } = null!;
    public DbSet<RetentionPolicyManagement> RetentionPolicyManagements { get; set; } = null!;

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure AuditLog
        modelBuilder.Entity<AuditLog>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).ValueGeneratedNever();

            entity.Property(e => e.EventType).HasConversion<int>();
            entity.Property(e => e.Severity).HasConversion<int>();
            entity.Property(e => e.EntityType).HasMaxLength(100).IsRequired();
            entity.Property(e => e.Action).HasMaxLength(200).IsRequired();
            entity.Property(e => e.Description).HasMaxLength(1000).IsRequired();
            entity.Property(e => e.UserName).HasMaxLength(200);
            entity.Property(e => e.UserRole).HasMaxLength(100);
            entity.Property(e => e.OldValues).HasColumnType("text");
            entity.Property(e => e.NewValues).HasColumnType("text");
            entity.Property(e => e.EncryptionKeyId).HasMaxLength(100);

            // Configure AuditContext as owned type
            entity.OwnsOne(e => e.Context, context =>
            {
                context.Property(c => c.IpAddress).HasMaxLength(45);
                context.Property(c => c.UserAgent).HasMaxLength(500);
                context.Property(c => c.SessionId).HasMaxLength(100);
                context.Property(c => c.CorrelationId).HasMaxLength(100);
                context.Property(c => c.RequestId).HasMaxLength(100);
                context.Property(c => c.AdditionalData)
                    .HasConversion(
                        v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                        v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>())
                    .HasColumnType("jsonb");
            });

            // Configure RetentionPolicy as owned type
            entity.OwnsOne(e => e.RetentionPolicy, policy =>
            {
                policy.Property(p => p.RetentionPeriod).HasConversion(
                    v => v.Ticks,
                    v => new TimeSpan(v));
                policy.Property(p => p.AutoDelete);
                policy.Property(p => p.RequireApproval);
                policy.Property(p => p.Description).HasMaxLength(500);
                policy.Property(p => p.ApplicableStandards)
                    .HasConversion(
                        v => JsonSerializer.Serialize(v.Select(s => (int)s).ToList(), (JsonSerializerOptions?)null),
                        v => JsonSerializer.Deserialize<List<int>>(v, (JsonSerializerOptions?)null)?.Select(i => (ComplianceStandard)i).ToList() ?? new List<ComplianceStandard>())
                    .HasColumnType("jsonb");
            });

            // Configure ComplianceFlags
            entity.Property(e => e.ComplianceFlags)
                .HasConversion(
                    v => JsonSerializer.Serialize(v.Select(s => (int)s).ToList(), (JsonSerializerOptions?)null),
                    v => JsonSerializer.Deserialize<List<int>>(v, (JsonSerializerOptions?)null)?.Select(i => (ComplianceStandard)i).ToList() ?? new List<ComplianceStandard>())
                .HasColumnType("jsonb");

            entity.HasIndex(e => e.EventType);
            entity.HasIndex(e => e.Severity);
            entity.HasIndex(e => e.UserId);
            entity.HasIndex(e => e.EntityType);
            entity.HasIndex(e => e.EntityId);
            entity.HasIndex(e => e.EventTimestamp);
            entity.HasIndex(e => e.CreatedAt);
        });

        // Configure ComplianceReport
        modelBuilder.Entity<ComplianceReport>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).ValueGeneratedNever();

            entity.Property(e => e.ReportNumber).HasMaxLength(50).IsRequired();
            entity.Property(e => e.Title).HasMaxLength(200).IsRequired();
            entity.Property(e => e.Description).HasMaxLength(1000).IsRequired();
            entity.Property(e => e.Standard).HasConversion<int>();
            entity.Property(e => e.Status).HasConversion<int>();
            entity.Property(e => e.GeneratedByName).HasMaxLength(200).IsRequired();
            entity.Property(e => e.FilePath).HasMaxLength(500);
            entity.Property(e => e.FileHash).HasMaxLength(128);
            entity.Property(e => e.Findings).HasColumnType("text");
            entity.Property(e => e.Recommendations).HasColumnType("text");
            entity.Property(e => e.ClosureReason).HasMaxLength(500);

            entity.HasMany(e => e.Items)
                .WithOne(i => i.ComplianceReport)
                .HasForeignKey(i => i.ComplianceReportId)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasIndex(e => e.ReportNumber).IsUnique();
            entity.HasIndex(e => e.Standard);
            entity.HasIndex(e => e.Status);
            entity.HasIndex(e => e.GeneratedBy);
            entity.HasIndex(e => e.CreatedAt);
        });

        // Configure ComplianceReportItem
        modelBuilder.Entity<ComplianceReportItem>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).ValueGeneratedNever();

            entity.Property(e => e.CheckName).HasMaxLength(200).IsRequired();
            entity.Property(e => e.Description).HasMaxLength(1000).IsRequired();
            entity.Property(e => e.Severity).HasConversion<int>();
            entity.Property(e => e.Details).HasColumnType("text");
            entity.Property(e => e.Recommendation).HasColumnType("text");
            entity.Property(e => e.Evidence).HasColumnType("text");
            entity.Property(e => e.ReferenceData).HasColumnType("text");

            entity.HasIndex(e => e.ComplianceReportId);
            entity.HasIndex(e => e.IsViolation);
            entity.HasIndex(e => e.Severity);
        });

        ConfigureRatingEntities(modelBuilder);
        ConfigureTenantEntity(modelBuilder);
        ConfigureEnhancedAuditEntities(modelBuilder);
    }

    private void ConfigureRatingEntities(ModelBuilder modelBuilder)
    {
        // Configure ServiceProviderRating
        modelBuilder.Entity<ServiceProviderRating>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).ValueGeneratedNever();

            entity.Property(e => e.ShipperName).HasMaxLength(200).IsRequired();
            entity.Property(e => e.TransportCompanyName).HasMaxLength(200).IsRequired();
            entity.Property(e => e.OrderNumber).HasMaxLength(50);
            entity.Property(e => e.TripNumber).HasMaxLength(50);
            entity.Property(e => e.Status).HasConversion<int>();
            entity.Property(e => e.ReviewTitle).HasMaxLength(200);
            entity.Property(e => e.ReviewComment).HasMaxLength(2000);

            // Configure OverallRating as owned type
            entity.OwnsOne(e => e.OverallRating, rating =>
            {
                rating.Property(r => r.Scale).HasConversion<int>();
                rating.Property(r => r.NumericValue).HasColumnType("decimal(3,2)");
                rating.Property(r => r.Comment).HasMaxLength(500);
            });

            entity.HasMany(e => e.CategoryRatings)
                .WithOne(cr => cr.ServiceProviderRating)
                .HasForeignKey(cr => cr.ServiceProviderRatingId)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasMany(e => e.ReportedIssues)
                .WithOne(si => si.ServiceProviderRating)
                .HasForeignKey(si => si.ServiceProviderRatingId)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasIndex(e => e.ShipperId);
            entity.HasIndex(e => e.TransportCompanyId);
            entity.HasIndex(e => e.OrderId);
            entity.HasIndex(e => e.TripId);
            entity.HasIndex(e => e.Status);
            entity.HasIndex(e => e.ServiceCompletedAt);
        });

        // Configure CategoryRating
        modelBuilder.Entity<CategoryRating>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).ValueGeneratedNever();

            entity.Property(e => e.Category).HasConversion<int>();
            entity.Property(e => e.Comment).HasMaxLength(500);

            // Configure Rating as owned type
            entity.OwnsOne(e => e.Rating, rating =>
            {
                rating.Property(r => r.Scale).HasConversion<int>();
                rating.Property(r => r.NumericValue).HasColumnType("decimal(3,2)");
                rating.Property(r => r.Comment).HasMaxLength(500);
            });

            entity.HasIndex(e => e.ServiceProviderRatingId);
            entity.HasIndex(e => e.Category);
        });

        // Configure ServiceIssue
        modelBuilder.Entity<ServiceIssue>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).ValueGeneratedNever();

            entity.Property(e => e.IssueType).HasConversion<int>();
            entity.Property(e => e.Priority).HasConversion<int>();
            entity.Property(e => e.Status).HasConversion<int>();
            entity.Property(e => e.Description).HasMaxLength(1000).IsRequired();
            entity.Property(e => e.Evidence).HasColumnType("text");
            entity.Property(e => e.Resolution).HasColumnType("text");
            entity.Property(e => e.ResolvedByName).HasMaxLength(200);

            entity.HasIndex(e => e.ServiceProviderRatingId);
            entity.HasIndex(e => e.IssueType);
            entity.HasIndex(e => e.Priority);
            entity.HasIndex(e => e.Status);
            entity.HasIndex(e => e.ReportedAt);
        });

        // Configure PreferredProviderNetwork
        modelBuilder.Entity<PreferredProviderNetwork>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).ValueGeneratedNever();

            entity.Property(e => e.ShipperName).HasMaxLength(200).IsRequired();
            entity.Property(e => e.TransportCompanyName).HasMaxLength(200).IsRequired();
            entity.Property(e => e.AverageRating).HasColumnType("decimal(3,2)");
            entity.Property(e => e.CompletionRate).HasColumnType("decimal(5,2)");
            entity.Property(e => e.Notes).HasMaxLength(1000);

            entity.HasIndex(e => e.ShipperId);
            entity.HasIndex(e => e.TransportCompanyId);
            entity.HasIndex(e => new { e.ShipperId, e.TransportCompanyId }).IsUnique();
            entity.HasIndex(e => e.IsActive);
            entity.HasIndex(e => e.PreferenceRank);
        });
    }

    private void ConfigureTenantEntity(ModelBuilder modelBuilder)
    {
        // Configure Tenant
        modelBuilder.Entity<Tenant>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).ValueGeneratedNever();

            entity.Property(e => e.Name).HasMaxLength(200).IsRequired();
            entity.Property(e => e.Code).HasMaxLength(50).IsRequired();
            entity.Property(e => e.Description).HasMaxLength(1000);
            entity.Property(e => e.Status).HasConversion<int>();
            entity.Property(e => e.Plan).HasConversion<int>();
            entity.Property(e => e.ContactEmail).HasMaxLength(200);
            entity.Property(e => e.ContactPhone).HasMaxLength(50);
            entity.Property(e => e.BillingEmail).HasMaxLength(200);
            entity.Property(e => e.DeactivationReason).HasMaxLength(500);

            // Configure TenantConfiguration as owned type
            entity.OwnsOne(e => e.Configuration, config =>
            {
                config.Property(c => c.TimeZone).HasMaxLength(50);
                config.Property(c => c.DateFormat).HasMaxLength(20);
                config.Property(c => c.Currency).HasMaxLength(10);
                config.Property(c => c.DataRetentionDays);
                config.Property(c => c.EnableAuditLogging);
                config.Property(c => c.EnableRealTimeNotifications);
                config.Property(c => c.EnableAdvancedAnalytics);
                config.Property(c => c.EnableMobileAccess);
                config.Property(c => c.EnableApiAccess);
                config.Property(c => c.RequireTwoFactorAuth);

                config.Property(c => c.EnabledFeatures)
                    .HasConversion(
                        v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                        v => JsonSerializer.Deserialize<List<string>>(v, (JsonSerializerOptions?)null) ?? new List<string>())
                    .HasColumnType("jsonb");

                config.Property(c => c.Settings)
                    .HasConversion(
                        v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                        v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>())
                    .HasColumnType("jsonb");

                config.Property(c => c.AllowedIpRanges)
                    .HasConversion(
                        v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                        v => JsonSerializer.Deserialize<List<string>>(v, (JsonSerializerOptions?)null) ?? new List<string>())
                    .HasColumnType("jsonb");
            });

            // Configure TenantLimits as owned type
            entity.OwnsOne(e => e.Limits, limits =>
            {
                limits.Property(l => l.MaxUsers);
                limits.Property(l => l.MaxAuditLogsPerMonth);
                limits.Property(l => l.MaxComplianceReports);
                limits.Property(l => l.MaxServiceProviders);
                limits.Property(l => l.MaxStorageBytes);
                limits.Property(l => l.MaxApiCallsPerHour);
                limits.Property(l => l.MaxConcurrentSessions);
            });

            // Configure EnabledComplianceStandards
            entity.Property(e => e.EnabledComplianceStandards)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                    v => JsonSerializer.Deserialize<List<string>>(v, (JsonSerializerOptions?)null) ?? new List<string>())
                .HasColumnType("jsonb");

            // Configure ComplianceSettings
            entity.Property(e => e.ComplianceSettings)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                    v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>())
                .HasColumnType("jsonb");

            entity.HasIndex(e => e.Code).IsUnique();
            entity.HasIndex(e => e.Name);
            entity.HasIndex(e => e.Status);
            entity.HasIndex(e => e.Plan);
            entity.HasIndex(e => e.CreatedAt);
        });
    }

    private void ConfigureEnhancedAuditEntities(ModelBuilder modelBuilder)
    {
        // Configure ModuleRegistry
        modelBuilder.Entity<ModuleRegistry>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).ValueGeneratedNever();

            entity.Property(e => e.ModuleName).HasMaxLength(100).IsRequired();
            entity.Property(e => e.DisplayName).HasMaxLength(200).IsRequired();
            entity.Property(e => e.Description).HasMaxLength(1000);
            entity.Property(e => e.Version).HasMaxLength(50).IsRequired();
            entity.Property(e => e.CreatedByName).HasMaxLength(200).IsRequired();
            entity.Property(e => e.HealthCheckEndpoint).HasMaxLength(500);
            entity.Property(e => e.LastHealthCheckError).HasMaxLength(1000);

            // Configure list properties as JSON
            entity.Property(e => e.SupportedEntityTypes)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                    v => JsonSerializer.Deserialize<List<string>>(v, (JsonSerializerOptions?)null) ?? new List<string>())
                .HasColumnType("jsonb");

            entity.Property(e => e.SupportedActions)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                    v => JsonSerializer.Deserialize<List<string>>(v, (JsonSerializerOptions?)null) ?? new List<string>())
                .HasColumnType("jsonb");

            entity.Property(e => e.RequiredRoles)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                    v => JsonSerializer.Deserialize<List<string>>(v, (JsonSerializerOptions?)null) ?? new List<string>())
                .HasColumnType("jsonb");

            entity.Property(e => e.Configuration)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                    v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>())
                .HasColumnType("jsonb");

            entity.HasIndex(e => e.ModuleName).IsUnique();
            entity.HasIndex(e => e.IsActive);
            entity.HasIndex(e => e.IsHealthy);
            entity.HasIndex(e => e.RegisteredAt);
        });

        // Configure ModuleAccessControl
        modelBuilder.Entity<ModuleAccessControl>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).ValueGeneratedNever();

            entity.Property(e => e.ModuleName).HasMaxLength(100).IsRequired();

            entity.Property(e => e.AllowedActions)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                    v => JsonSerializer.Deserialize<List<string>>(v, (JsonSerializerOptions?)null) ?? new List<string>())
                .HasColumnType("jsonb");

            entity.Property(e => e.DeniedActions)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                    v => JsonSerializer.Deserialize<List<string>>(v, (JsonSerializerOptions?)null) ?? new List<string>())
                .HasColumnType("jsonb");

            entity.Property(e => e.Reason).HasMaxLength(500);

            entity.HasIndex(e => new { e.UserId, e.ModuleName }).IsUnique();
            entity.HasIndex(e => e.UserId);
            entity.HasIndex(e => e.ModuleName);
            entity.HasIndex(e => e.IsActive);
            entity.HasIndex(e => e.ExpiresAt);
        });

        // Configure CustomComplianceReportTemplate
        modelBuilder.Entity<CustomComplianceReportTemplate>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).ValueGeneratedNever();

            entity.Property(e => e.TemplateName).HasMaxLength(200).IsRequired();
            entity.Property(e => e.Description).HasMaxLength(1000);
            entity.Property(e => e.ComplianceStandard).HasConversion<int>();
            entity.Property(e => e.TemplateType).HasConversion<int>();
            entity.Property(e => e.TemplateContent).HasColumnType("text").IsRequired();
            entity.Property(e => e.CreatedByName).HasMaxLength(200).IsRequired();
            entity.Property(e => e.Version).HasMaxLength(20).IsRequired();

            // Configure complex properties as JSON
            entity.Property(e => e.Sections)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                    v => JsonSerializer.Deserialize<List<ReportSection>>(v, (JsonSerializerOptions?)null) ?? new List<ReportSection>())
                .HasColumnType("jsonb");

            entity.Property(e => e.Parameters)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                    v => JsonSerializer.Deserialize<List<ReportParameter>>(v, (JsonSerializerOptions?)null) ?? new List<ReportParameter>())
                .HasColumnType("jsonb");

            entity.Property(e => e.Schedule)
                .HasConversion(
                    v => v != null ? JsonSerializer.Serialize(v, (JsonSerializerOptions?)null) : null,
                    v => v != null ? JsonSerializer.Deserialize<ReportSchedule>(v, (JsonSerializerOptions?)null) : null)
                .HasColumnType("jsonb");

            entity.Property(e => e.Tags)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                    v => JsonSerializer.Deserialize<List<string>>(v, (JsonSerializerOptions?)null) ?? new List<string>())
                .HasColumnType("jsonb");

            entity.Property(e => e.Permissions)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                    v => JsonSerializer.Deserialize<List<TemplatePermission>>(v, (JsonSerializerOptions?)null) ?? new List<TemplatePermission>())
                .HasColumnType("jsonb");

            entity.Property(e => e.Metadata)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                    v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>())
                .HasColumnType("jsonb");

            entity.HasIndex(e => e.TemplateName).IsUnique();
            entity.HasIndex(e => e.ComplianceStandard);
            entity.HasIndex(e => e.TemplateType);
            entity.HasIndex(e => e.IsPublic);
            entity.HasIndex(e => e.IsActive);
            entity.HasIndex(e => e.CreatedBy);
            entity.HasIndex(e => e.CreatedAt);
        });

        // Configure RetentionPolicyManagement
        modelBuilder.Entity<RetentionPolicyManagement>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).ValueGeneratedNever();

            entity.Property(e => e.PolicyName).HasMaxLength(200).IsRequired();
            entity.Property(e => e.Description).HasMaxLength(1000);
            entity.Property(e => e.Priority).HasConversion<int>();
            entity.Property(e => e.ApprovalStatus).HasConversion<int>();
            entity.Property(e => e.CreatedByName).HasMaxLength(200).IsRequired();
            entity.Property(e => e.ApprovedByName).HasMaxLength(200);

            // Configure RetentionPolicy as owned entity
            entity.OwnsOne(e => e.RetentionPolicy, rp =>
            {
                rp.Property(p => p.RetentionPeriod).IsRequired();
                rp.Property(p => p.AutoDelete).IsRequired();
                rp.Property(p => p.RequireApproval).IsRequired();
                rp.Property(p => p.Description).HasMaxLength(500);

                rp.Property(p => p.ApplicableStandards)
                    .HasConversion(
                        v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                        v => JsonSerializer.Deserialize<List<ComplianceStandard>>(v, (JsonSerializerOptions?)null) ?? new List<ComplianceStandard>())
                    .HasColumnType("jsonb");
            });

            // Configure list properties as JSON
            entity.Property(e => e.ApplicableStandards)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                    v => JsonSerializer.Deserialize<List<ComplianceStandard>>(v, (JsonSerializerOptions?)null) ?? new List<ComplianceStandard>())
                .HasColumnType("jsonb");

            entity.Property(e => e.ApplicableEntityTypes)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                    v => JsonSerializer.Deserialize<List<string>>(v, (JsonSerializerOptions?)null) ?? new List<string>())
                .HasColumnType("jsonb");

            entity.Property(e => e.ApplicableModules)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                    v => JsonSerializer.Deserialize<List<string>>(v, (JsonSerializerOptions?)null) ?? new List<string>())
                .HasColumnType("jsonb");

            entity.Property(e => e.ExecutionHistory)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                    v => JsonSerializer.Deserialize<List<PolicyExecution>>(v, (JsonSerializerOptions?)null) ?? new List<PolicyExecution>())
                .HasColumnType("jsonb");

            entity.Property(e => e.ImpactAnalysis)
                .HasConversion(
                    v => v != null ? JsonSerializer.Serialize(v, (JsonSerializerOptions?)null) : null,
                    v => v != null ? JsonSerializer.Deserialize<PolicyImpactAnalysis>(v, (JsonSerializerOptions?)null) : null)
                .HasColumnType("jsonb");

            entity.Property(e => e.Configuration)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                    v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>())
                .HasColumnType("jsonb");

            entity.Property(e => e.Tags)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                    v => JsonSerializer.Deserialize<List<string>>(v, (JsonSerializerOptions?)null) ?? new List<string>())
                .HasColumnType("jsonb");

            entity.HasIndex(e => e.PolicyName).IsUnique();
            entity.HasIndex(e => e.Priority);
            entity.HasIndex(e => e.IsActive);
            entity.HasIndex(e => e.ApprovalStatus);
            entity.HasIndex(e => e.CreatedBy);
            entity.HasIndex(e => e.CreatedAt);
            entity.HasIndex(e => e.LastExecuted);
        });
    }
}
