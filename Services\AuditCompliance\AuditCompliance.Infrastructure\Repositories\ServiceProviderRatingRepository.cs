using Microsoft.EntityFrameworkCore;
using AuditCompliance.Application.Interfaces;
using AuditCompliance.Domain.Entities;
using AuditCompliance.Domain.Enums;
using AuditCompliance.Infrastructure.Persistence;

namespace AuditCompliance.Infrastructure.Repositories;

public class ServiceProviderRatingRepository : IServiceProviderRatingRepository
{
    private readonly AuditComplianceDbContext _context;

    public ServiceProviderRatingRepository(AuditComplianceDbContext context)
    {
        _context = context;
    }

    public async Task<ServiceProviderRating?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.ServiceProviderRatings
            .Include(spr => spr.CategoryRatings)
            .Include(spr => spr.ReportedIssues)
            .FirstOrDefaultAsync(spr => spr.Id == id, cancellationToken);
    }

    public async Task<List<ServiceProviderRating>> GetRatingsAsync(
        Guid? shipperId = null,
        Guid? transportCompanyId = null,
        RatingStatus? status = null,
        decimal? minRating = null,
        decimal? maxRating = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default)
    {
        var query = _context.ServiceProviderRatings
            .Include(spr => spr.CategoryRatings)
            .Include(spr => spr.ReportedIssues)
            .AsQueryable();

        if (shipperId.HasValue)
            query = query.Where(spr => spr.ShipperId == shipperId.Value);

        if (transportCompanyId.HasValue)
            query = query.Where(spr => spr.TransportCompanyId == transportCompanyId.Value);

        if (status.HasValue)
            query = query.Where(spr => spr.Status == status.Value);

        if (minRating.HasValue)
            query = query.Where(spr => spr.OverallRating.NumericValue >= minRating.Value);

        if (maxRating.HasValue)
            query = query.Where(spr => spr.OverallRating.NumericValue <= maxRating.Value);

        if (fromDate.HasValue)
            query = query.Where(spr => spr.ServiceCompletedAt >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(spr => spr.ServiceCompletedAt <= toDate.Value);

        return await query
            .OrderByDescending(spr => spr.CreatedAt)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);
    }

    public async Task<int> GetRatingsCountAsync(
        Guid? shipperId = null,
        Guid? transportCompanyId = null,
        RatingStatus? status = null,
        decimal? minRating = null,
        decimal? maxRating = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.ServiceProviderRatings.AsQueryable();

        if (shipperId.HasValue)
            query = query.Where(spr => spr.ShipperId == shipperId.Value);

        if (transportCompanyId.HasValue)
            query = query.Where(spr => spr.TransportCompanyId == transportCompanyId.Value);

        if (status.HasValue)
            query = query.Where(spr => spr.Status == status.Value);

        if (minRating.HasValue)
            query = query.Where(spr => spr.OverallRating.NumericValue >= minRating.Value);

        if (maxRating.HasValue)
            query = query.Where(spr => spr.OverallRating.NumericValue <= maxRating.Value);

        if (fromDate.HasValue)
            query = query.Where(spr => spr.ServiceCompletedAt >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(spr => spr.ServiceCompletedAt <= toDate.Value);

        return await query.CountAsync(cancellationToken);
    }

    public async Task<List<ServiceProviderRating>> GetRatingsByTransportCompanyAsync(
        Guid transportCompanyId,
        CancellationToken cancellationToken = default)
    {
        return await _context.ServiceProviderRatings
            .Include(spr => spr.CategoryRatings)
            .Include(spr => spr.ReportedIssues)
            .Where(spr => spr.TransportCompanyId == transportCompanyId)
            .OrderByDescending(spr => spr.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<ServiceProviderRating> AddAsync(ServiceProviderRating rating, CancellationToken cancellationToken = default)
    {
        _context.ServiceProviderRatings.Add(rating);
        await _context.SaveChangesAsync(cancellationToken);
        return rating;
    }

    public async Task UpdateAsync(ServiceProviderRating rating, CancellationToken cancellationToken = default)
    {
        _context.ServiceProviderRatings.Update(rating);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task DeleteAsync(ServiceProviderRating rating, CancellationToken cancellationToken = default)
    {
        _context.ServiceProviderRatings.Remove(rating);
        await _context.SaveChangesAsync(cancellationToken);
    }
}
