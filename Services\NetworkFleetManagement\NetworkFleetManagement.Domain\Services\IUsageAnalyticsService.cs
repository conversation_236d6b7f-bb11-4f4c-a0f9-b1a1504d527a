using NetworkFleetManagement.Domain.Enums;

namespace NetworkFleetManagement.Domain.Services;

public interface IUsageAnalyticsService
{
    // Fleet Analytics
    Task<Dictionary<string, object>> GetFleetUtilizationAnalyticsAsync(Guid carrierId, DateTime from, DateTime to);
    Task<Dictionary<string, object>> GetVehiclePerformanceAnalyticsAsync(Guid vehicleId, DateTime from, DateTime to);
    Task<Dictionary<string, object>> GetFleetOverviewAnalyticsAsync(Guid carrierId, DateTime from, DateTime to);
    Task<List<Dictionary<string, object>>> GetTopPerformingVehiclesAsync(Guid carrierId, int count = 10, DateTime? from = null, DateTime? to = null);
    Task<List<Dictionary<string, object>>> GetUnderutilizedVehiclesAsync(Guid carrierId, double utilizationThreshold = 0.5, DateTime? from = null, DateTime? to = null);

    // Driver Analytics
    Task<Dictionary<string, object>> GetDriverPerformanceAnalyticsAsync(Guid driverId, DateTime from, DateTime to);
    Task<Dictionary<string, object>> GetDriverComplianceAnalyticsAsync(Guid driverId, DateTime from, DateTime to);
    Task<List<Dictionary<string, object>>> GetTopPerformingDriversAsync(Guid carrierId, int count = 10, DateTime? from = null, DateTime? to = null);
    Task<Dictionary<string, object>> GetDriverSafetyScoreAsync(Guid driverId, DateTime from, DateTime to);
    Task<Dictionary<string, object>> GetDriverEarningsAnalyticsAsync(Guid driverId, DateTime from, DateTime to);

    // Network Analytics
    Task<Dictionary<string, object>> GetNetworkPerformanceAnalyticsAsync(Guid networkId, DateTime from, DateTime to);
    Task<Dictionary<string, object>> GetCarrierNetworkAnalyticsAsync(Guid carrierId, DateTime from, DateTime to);
    Task<Dictionary<string, object>> GetBrokerNetworkAnalyticsAsync(Guid brokerId, DateTime from, DateTime to);
    Task<List<Dictionary<string, object>>> GetNetworkTrendsAsync(DateTime from, DateTime to);

    // Operational Analytics
    Task<Dictionary<string, object>> GetMaintenanceAnalyticsAsync(Guid carrierId, DateTime from, DateTime to);
    Task<Dictionary<string, object>> GetDocumentComplianceAnalyticsAsync(Guid carrierId, DateTime from, DateTime to);
    Task<Dictionary<string, object>> GetOnboardingAnalyticsAsync(DateTime from, DateTime to);
    Task<Dictionary<string, object>> GetSystemHealthAnalyticsAsync(DateTime from, DateTime to);

    // Real-time Analytics
    Task<Dictionary<string, object>> GetRealTimeFleetStatusAsync(Guid carrierId);
    Task<Dictionary<string, object>> GetRealTimeDriverStatusAsync(Guid carrierId);
    Task<Dictionary<string, object>> GetRealTimeNetworkStatusAsync();
    Task<List<Dictionary<string, object>>> GetActiveAlertsAsync(Guid? carrierId = null);

    // Predictive Analytics
    Task<Dictionary<string, object>> GetPredictiveMaintenanceAnalyticsAsync(Guid vehicleId);
    Task<Dictionary<string, object>> GetDemandForecastAnalyticsAsync(Guid carrierId, DateTime from, DateTime to);
    Task<Dictionary<string, object>> GetCapacityPlanningAnalyticsAsync(Guid carrierId, DateTime from, DateTime to);

    // Custom Analytics
    Task<Dictionary<string, object>> GetCustomAnalyticsAsync(string analyticsType, Dictionary<string, object> parameters);
    Task<List<Dictionary<string, object>>> GetAnalyticsReportAsync(string reportType, Dictionary<string, object> parameters);

    // Usage Tracking
    Task TrackVehicleUsageAsync(Guid vehicleId, VehicleUsageType usageType, Dictionary<string, object>? metadata = null);
    Task TrackDriverActivityAsync(Guid driverId, DriverActivityType activityType, Dictionary<string, object>? metadata = null);
    Task TrackNetworkEventAsync(Guid networkId, NetworkEventType eventType, Dictionary<string, object>? metadata = null);
    Task TrackSystemEventAsync(SystemEventType eventType, Dictionary<string, object>? metadata = null);

    // Aggregation and Reporting
    Task<Dictionary<string, object>> GetAggregatedMetricsAsync(string metricType, DateTime from, DateTime to, string aggregationType = "daily");
    Task<byte[]> ExportAnalyticsReportAsync(string reportType, Dictionary<string, object> parameters, string format = "pdf");
    Task<bool> ScheduleAnalyticsReportAsync(string reportType, Dictionary<string, object> parameters, string schedule, List<string> recipients);
}

public enum VehicleUsageType
{
    TripStarted,
    TripCompleted,
    MaintenanceScheduled,
    MaintenanceCompleted,
    FuelRefill,
    LocationUpdate,
    StatusChange,
    PerformanceUpdate
}

public enum DriverActivityType
{
    LoginActivity,
    TripActivity,
    DocumentUpdate,
    ComplianceCheck,
    TrainingCompleted,
    SafetyIncident,
    PerformanceReview,
    StatusChange
}

public enum NetworkEventType
{
    NetworkCreated,
    NetworkActivated,
    NetworkSuspended,
    NetworkTerminated,
    PerformanceUpdate,
    RateUpdate,
    PriorityChange,
    ComplianceCheck
}

public enum SystemEventType
{
    UserLogin,
    SystemError,
    PerformanceAlert,
    SecurityEvent,
    DataBackup,
    SystemMaintenance,
    CapacityAlert,
    IntegrationEvent
}
