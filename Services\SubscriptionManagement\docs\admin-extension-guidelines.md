# Admin Guidelines: Subscription Extensions

## Overview

This document provides guidelines for administrators on when and how to use the subscription extension functionality. Proper use of this feature ensures customer satisfaction while maintaining business integrity.

## When to Use Extensions

### Regular Extensions (Billing Cycle Adjustment)

**Appropriate Scenarios:**

- Customer made payment but it was processed late
- Billing system error caused incorrect billing date
- Customer upgrade/downgrade timing adjustment
- Promotional extensions for customer retention
- Service outage compensation

**Example:**
Customer paid on time but payment processor delayed the transaction by 3 days. Extend subscription by 3 days to compensate.

### Grace Period Extensions (Temporary Access)

**Appropriate Scenarios:**

- Payment method expired and customer needs time to update
- Temporary financial hardship with payment plan arrangement
- Payment dispute resolution in progress
- Bank/payment processor technical issues
- Customer traveling and unable to process payment

**Example:**
Customer's credit card expired and they're traveling. Apply 7-day grace period while they update payment method.

## Decision Matrix

| Scenario                    | Extension Type | Typical Duration | Approval Required |
| --------------------------- | -------------- | ---------------- | ----------------- |
| Late payment processing     | Regular        | 1-5 days         | No                |
| Payment method update       | Grace Period   | 3-7 days         | No                |
| Financial hardship          | Grace Period   | 7-15 days        | Manager           |
| Service outage compensation | Regular        | 1-3 days         | No                |
| Retention offer             | Regular        | 7-30 days        | Manager           |
| Payment dispute             | Grace Period   | 15-30 days       | Manager           |

## Best Practices

### Documentation Requirements

1. **Always Provide Reason**: Include clear, specific reason for extension
2. **Customer Communication**: Document any customer communication
3. **Follow-up Required**: Note if follow-up actions are needed
4. **Escalation Path**: Document if manager approval was obtained

### Extension Limits

1. **Maximum Single Extension**: 90 days (system enforced)
2. **Recommended Limits**:
   - Regular extensions: 30 days maximum
   - Grace periods: 15 days maximum
   - Multiple extensions: Require manager approval

### Customer Communication

**Before Extension:**

- Explain what type of extension is being applied
- Clarify impact on billing cycle
- Set expectations for next billing date
- Confirm customer understanding

**After Extension:**

- Send confirmation email with new dates
- Update customer account notes
- Schedule follow-up if needed

## Step-by-Step Process

### 1. Assess the Situation

- Review customer account history
- Understand the specific issue
- Determine appropriate extension type
- Check for previous extensions

### 2. Choose Extension Type

**For Regular Extension:**

```
Use when: Billing cycle needs adjustment
Effect: Moves next billing date forward
Customer sees: Extended subscription period
```

**For Grace Period:**

```
Use when: Temporary access needed
Effect: Adds grace period without changing billing
Customer sees: Additional time to resolve payment
```

### 3. Apply Extension

**API Call Example:**

```bash
POST /api/subscriptions/{subscription-id}/extend
{
  "extensionDays": 15,
  "reason": "Customer credit card expired, updating payment method",
  "applyAsGracePeriod": true
}
```

### 4. Document and Communicate

- Update customer service ticket
- Send customer notification
- Add account notes
- Schedule follow-up if needed

## Common Scenarios & Solutions

### Scenario 1: Expired Credit Card

**Situation**: Customer's subscription failed due to expired credit card
**Solution**:

- Apply 7-day grace period
- Contact customer to update payment method
- Monitor for payment update

**Extension Settings:**

```json
{
  "extensionDays": 7,
  "reason": "Credit card expired - customer updating payment method",
  "applyAsGracePeriod": true
}
```

### Scenario 2: Service Outage Compensation

**Situation**: Service was down for 2 days, customer requests compensation
**Solution**:

- Apply regular extension for outage duration
- Document as service credit

**Extension Settings:**

```json
{
  "extensionDays": 2,
  "reason": "Service outage compensation - 2 days downtime",
  "applyAsGracePeriod": false
}
```

### Scenario 3: Payment Dispute

**Situation**: Customer disputes charge, payment processor investigating
**Solution**:

- Apply grace period for investigation duration
- Monitor dispute status
- Require manager approval for >15 days

**Extension Settings:**

```json
{
  "extensionDays": 20,
  "reason": "Payment dispute investigation - case #12345",
  "applyAsGracePeriod": true
}
```

## Monitoring & Reporting

### Daily Monitoring

- Review all extensions applied
- Check for unusual patterns
- Follow up on pending actions

### Weekly Reports

- Extension volume and trends
- Customer satisfaction impact
- Revenue impact analysis

### Monthly Analysis

- Extension effectiveness
- Customer retention correlation
- Process improvement opportunities

## Escalation Guidelines

### Require Manager Approval:

- Extensions >15 days
- Multiple extensions for same customer
- Extensions for disputed accounts
- Promotional/retention extensions
- Unusual circumstances

### Require Director Approval:

- Extensions >30 days
- Revenue impact >$1000
- Policy exceptions
- Bulk extensions

## Compliance & Audit

### Audit Trail Requirements

- All extensions logged with admin ID
- Reason required for all extensions
- Customer communication documented
- Manager approvals recorded

### Compliance Checks

- Monthly audit of all extensions
- Review of extension patterns
- Customer satisfaction correlation
- Revenue impact assessment

## Training Requirements

### New Admin Training

- Complete extension guidelines training
- Shadow experienced admin for 5 extensions
- Pass extension scenario quiz
- Manager sign-off required

### Ongoing Training

- Quarterly guideline updates
- Annual compliance training
- New feature training as released

## Support Contacts

- **Technical Issues**: IT Support (ext. 1234)
- **Policy Questions**: Customer Success Manager
- **Escalations**: Operations Manager
- **System Access**: Admin Team Lead

## Revision History

| Version | Date       | Changes            | Author      |
| ------- | ---------- | ------------------ | ----------- |
| 1.0     | 2024-06-24 | Initial guidelines | System Team |

---

**Remember**: Extensions should always serve the customer's best interest while maintaining business integrity. When in doubt, escalate to your manager.

## Quick Reference

### Extension Commands

```bash
# Regular Extension (30 days)
POST /api/subscriptions/{id}/extend
{"extensionDays": 30, "reason": "...", "applyAsGracePeriod": false}

# Grace Period (15 days)
POST /api/subscriptions/{id}/extend
{"extensionDays": 15, "reason": "...", "applyAsGracePeriod": true}
```

### Common Reasons

- "Credit card expired - customer updating payment method"
- "Service outage compensation - X days downtime"
- "Payment dispute investigation - case #XXXXX"
- "Customer retention offer - manager approved"
- "Payment processor technical issues"
