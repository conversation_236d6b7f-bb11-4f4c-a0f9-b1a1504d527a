using Microsoft.EntityFrameworkCore;
using OrderManagement.Domain.Entities;
using OrderManagement.Infrastructure.Persistence.Configurations;

namespace OrderManagement.Infrastructure.Persistence;

public class OrderManagementDbContext : DbContext
{
    public DbSet<RFQ> Rfqs { get; set; }
    public DbSet<RfqBid> RfqBids { get; set; }
    public DbSet<Order> Orders { get; set; }
    public DbSet<Invoice> Invoices { get; set; }
    public DbSet<InvoiceLineItem> InvoiceLineItems { get; set; }
    public DbSet<RfqDocument> RfqDocuments { get; set; }
    public DbSet<BidDocument> BidDocuments { get; set; }
    public DbSet<OrderDocument> OrderDocuments { get; set; }
    public DbSet<OrderStatusHistory> OrderStatusHistories { get; set; }
    public DbSet<OrderTimeline> OrderTimelines { get; set; }
    public DbSet<RfqTimeline> RfqTimelines { get; set; }
    public DbSet<RfqRoutingHistory> RfqRoutingHistories { get; set; }
    public DbSet<RfqTag> RfqTags { get; set; }
    public DbSet<MilestoneTemplate> MilestoneTemplates { get; set; }
    public DbSet<MilestoneStep> MilestoneSteps { get; set; }
    public DbSet<RfqMilestoneAssignment> RfqMilestoneAssignments { get; set; }
    public DbSet<RfqMilestoneProgress> RfqMilestoneProgress { get; set; }
    public DbSet<BrokerComment> BrokerComments { get; set; }
    public DbSet<AdministrativeAuditLog> AdministrativeAuditLogs { get; set; }

    public OrderManagementDbContext(DbContextOptions<OrderManagementDbContext> options)
        : base(options)
    {
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Set default schema
        modelBuilder.HasDefaultSchema("order_management");

        // Apply configurations
        modelBuilder.ApplyConfiguration(new RfqConfiguration());
        modelBuilder.ApplyConfiguration(new RfqBidConfiguration());
        modelBuilder.ApplyConfiguration(new OrderConfiguration());
        modelBuilder.ApplyConfiguration(new InvoiceConfiguration());
        modelBuilder.ApplyConfiguration(new InvoiceLineItemConfiguration());
        modelBuilder.ApplyConfiguration(new RfqDocumentConfiguration());
        modelBuilder.ApplyConfiguration(new BidDocumentConfiguration());
        modelBuilder.ApplyConfiguration(new OrderDocumentConfiguration());
        modelBuilder.ApplyConfiguration(new OrderStatusHistoryConfiguration());
        modelBuilder.ApplyConfiguration(new OrderTimelineConfiguration());
        modelBuilder.ApplyConfiguration(new RfqTimelineConfiguration());
        modelBuilder.ApplyConfiguration(new RfqRoutingHistoryConfiguration());
        modelBuilder.ApplyConfiguration(new RfqTagConfiguration());
        modelBuilder.ApplyConfiguration(new MilestoneTemplateConfiguration());
        modelBuilder.ApplyConfiguration(new MilestoneStepConfiguration());
        modelBuilder.ApplyConfiguration(new RfqMilestoneAssignmentConfiguration());
        modelBuilder.ApplyConfiguration(new RfqMilestoneProgressConfiguration());
        modelBuilder.ApplyConfiguration(new BrokerCommentConfiguration());
        modelBuilder.ApplyConfiguration(new AdministrativeAuditLogConfiguration());

        // Configure value objects
        ConfigureValueObjects(modelBuilder);
    }

    private static void ConfigureValueObjects(ModelBuilder modelBuilder)
    {
        // Configure Money value objects
        modelBuilder.Entity<RFQ>()
            .OwnsOne(r => r.BudgetRange, money =>
            {
                money.Property(m => m.Amount).HasColumnName("BudgetAmount").HasPrecision(18, 2);
                money.Property(m => m.Currency).HasColumnName("BudgetCurrency").HasMaxLength(3);
            });

        modelBuilder.Entity<RfqBid>()
            .OwnsOne(b => b.QuotedPrice, money =>
            {
                money.Property(m => m.Amount).HasColumnName("QuotedAmount").HasPrecision(18, 2);
                money.Property(m => m.Currency).HasColumnName("QuotedCurrency").HasMaxLength(3);
            });

        modelBuilder.Entity<Order>()
            .OwnsOne(o => o.AgreedPrice, money =>
            {
                money.Property(m => m.Amount).HasColumnName("AgreedAmount").HasPrecision(18, 2);
                money.Property(m => m.Currency).HasColumnName("AgreedCurrency").HasMaxLength(3);
            });

        modelBuilder.Entity<Invoice>()
            .OwnsOne(i => i.Amount, money =>
            {
                money.Property(m => m.Amount).HasColumnName("Amount").HasPrecision(18, 2);
                money.Property(m => m.Currency).HasColumnName("Currency").HasMaxLength(3);
            });

        modelBuilder.Entity<Invoice>()
            .OwnsOne(i => i.TaxAmount, money =>
            {
                money.Property(m => m.Amount).HasColumnName("TaxAmount").HasPrecision(18, 2);
                money.Property(m => m.Currency).HasColumnName("TaxCurrency").HasMaxLength(3);
            });

        modelBuilder.Entity<Invoice>()
            .OwnsOne(i => i.TotalAmount, money =>
            {
                money.Property(m => m.Amount).HasColumnName("TotalAmount").HasPrecision(18, 2);
                money.Property(m => m.Currency).HasColumnName("TotalCurrency").HasMaxLength(3);
            });

        // Configure complex value objects
        ConfigureLoadDetails(modelBuilder);
        ConfigureRouteDetails(modelBuilder);
        ConfigureRfqRequirements(modelBuilder);
        ConfigureCustomerDetails(modelBuilder);
        ConfigureBillingDetails(modelBuilder);
    }

    private static void ConfigureLoadDetails(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<RFQ>()
            .OwnsOne(r => r.LoadDetails, ld =>
            {
                ld.Property(l => l.Description).HasColumnName("LoadDescription").HasMaxLength(1000);
                ld.Property(l => l.LoadType).HasColumnName("LoadType");
                ld.Property(l => l.Quantity).HasColumnName("LoadQuantity");
                ld.Property(l => l.PackagingType).HasColumnName("PackagingType").HasMaxLength(100);
                ld.Property(l => l.RequiredVehicleType).HasColumnName("RequiredVehicleType");
                ld.Property(l => l.RequiresSpecialHandling).HasColumnName("RequiresSpecialHandling");
                ld.Property(l => l.SpecialHandlingInstructions).HasColumnName("SpecialHandlingInstructions").HasMaxLength(500);
                ld.Property(l => l.IsHazardous).HasColumnName("IsHazardous");
                ld.Property(l => l.HazardousClassification).HasColumnName("HazardousClassification").HasMaxLength(100);
                ld.Property(l => l.RequiresTemperatureControl).HasColumnName("RequiresTemperatureControl");

                // Weight
                ld.OwnsOne(l => l.Weight, w =>
                {
                    w.Property(weight => weight.Value).HasColumnName("WeightValue").HasPrecision(18, 3);
                    w.Property(weight => weight.Unit).HasColumnName("WeightUnit");
                });

                // Volume
                ld.OwnsOne(l => l.Volume, v =>
                {
                    v.Property(vol => vol.Value).HasColumnName("VolumeValue").HasPrecision(18, 3);
                    v.Property(vol => vol.Unit).HasColumnName("VolumeUnit");
                });

                // Dimensions
                ld.OwnsOne(l => l.Dimensions, d =>
                {
                    d.Property(dim => dim.Length).HasColumnName("DimensionLength").HasPrecision(18, 2);
                    d.Property(dim => dim.Width).HasColumnName("DimensionWidth").HasPrecision(18, 2);
                    d.Property(dim => dim.Height).HasColumnName("DimensionHeight").HasPrecision(18, 2);
                    d.Property(dim => dim.Unit).HasColumnName("DimensionUnit").HasMaxLength(10);
                });

                // Temperature Range
                ld.OwnsOne(l => l.TemperatureRange, tr =>
                {
                    tr.Property(temp => temp.MinTemperature).HasColumnName("MinTemperature").HasPrecision(5, 2);
                    tr.Property(temp => temp.MaxTemperature).HasColumnName("MaxTemperature").HasPrecision(5, 2);
                    tr.Property(temp => temp.Unit).HasColumnName("TemperatureUnit").HasMaxLength(1);
                });

                // Photos as JSON
                ld.Property(l => l.Photos)
                    .HasColumnName("LoadPhotos")
                    .HasColumnType("jsonb");
            });

        // Similar configuration for Order LoadDetails
        modelBuilder.Entity<Order>()
            .OwnsOne(o => o.LoadDetails, ld =>
            {
                // Same configuration as RFQ LoadDetails
                ld.Property(l => l.Description).HasColumnName("LoadDescription").HasMaxLength(1000);
                ld.Property(l => l.LoadType).HasColumnName("LoadType");
                ld.Property(l => l.Quantity).HasColumnName("LoadQuantity");
                ld.Property(l => l.PackagingType).HasColumnName("PackagingType").HasMaxLength(100);
                ld.Property(l => l.RequiredVehicleType).HasColumnName("RequiredVehicleType");
                ld.Property(l => l.RequiresSpecialHandling).HasColumnName("RequiresSpecialHandling");
                ld.Property(l => l.SpecialHandlingInstructions).HasColumnName("SpecialHandlingInstructions").HasMaxLength(500);
                ld.Property(l => l.IsHazardous).HasColumnName("IsHazardous");
                ld.Property(l => l.HazardousClassification).HasColumnName("HazardousClassification").HasMaxLength(100);
                ld.Property(l => l.RequiresTemperatureControl).HasColumnName("RequiresTemperatureControl");

                ld.OwnsOne(l => l.Weight, w =>
                {
                    w.Property(weight => weight.Value).HasColumnName("WeightValue").HasPrecision(18, 3);
                    w.Property(weight => weight.Unit).HasColumnName("WeightUnit");
                });

                ld.OwnsOne(l => l.Volume, v =>
                {
                    v.Property(vol => vol.Value).HasColumnName("VolumeValue").HasPrecision(18, 3);
                    v.Property(vol => vol.Unit).HasColumnName("VolumeUnit");
                });

                ld.OwnsOne(l => l.Dimensions, d =>
                {
                    d.Property(dim => dim.Length).HasColumnName("DimensionLength").HasPrecision(18, 2);
                    d.Property(dim => dim.Width).HasColumnName("DimensionWidth").HasPrecision(18, 2);
                    d.Property(dim => dim.Height).HasColumnName("DimensionHeight").HasPrecision(18, 2);
                    d.Property(dim => dim.Unit).HasColumnName("DimensionUnit").HasMaxLength(10);
                });

                ld.OwnsOne(l => l.TemperatureRange, tr =>
                {
                    tr.Property(temp => temp.MinTemperature).HasColumnName("MinTemperature").HasPrecision(5, 2);
                    tr.Property(temp => temp.MaxTemperature).HasColumnName("MaxTemperature").HasPrecision(5, 2);
                    tr.Property(temp => temp.Unit).HasColumnName("TemperatureUnit").HasMaxLength(1);
                });

                ld.Property(l => l.Photos)
                    .HasColumnName("LoadPhotos")
                    .HasColumnType("jsonb");
            });
    }

    private static void ConfigureRouteDetails(ModelBuilder modelBuilder)
    {
        // Configure for RFQ
        modelBuilder.Entity<RFQ>()
            .OwnsOne(r => r.RouteDetails, rd =>
            {
                rd.Property(route => route.PreferredPickupDate).HasColumnName("PreferredPickupDate");
                rd.Property(route => route.PreferredDeliveryDate).HasColumnName("PreferredDeliveryDate");
                rd.Property(route => route.EstimatedDuration).HasColumnName("EstimatedDuration");
                rd.Property(route => route.RouteNotes).HasColumnName("RouteNotes").HasMaxLength(1000);
                rd.Property(route => route.IsFlexiblePickup).HasColumnName("IsFlexiblePickup");
                rd.Property(route => route.IsFlexibleDelivery).HasColumnName("IsFlexibleDelivery");

                // Pickup Address
                rd.OwnsOne(route => route.PickupAddress, addr =>
                {
                    addr.Property(a => a.Street).HasColumnName("PickupStreet").HasMaxLength(200);
                    addr.Property(a => a.City).HasColumnName("PickupCity").HasMaxLength(100);
                    addr.Property(a => a.State).HasColumnName("PickupState").HasMaxLength(100);
                    addr.Property(a => a.PostalCode).HasColumnName("PickupPostalCode").HasMaxLength(20);
                    addr.Property(a => a.Country).HasColumnName("PickupCountry").HasMaxLength(100);
                    addr.Property(a => a.Latitude).HasColumnName("PickupLatitude").HasPrecision(10, 7);
                    addr.Property(a => a.Longitude).HasColumnName("PickupLongitude").HasPrecision(10, 7);
                    addr.Property(a => a.ContactPerson).HasColumnName("PickupContactPerson").HasMaxLength(100);
                    addr.Property(a => a.ContactPhone).HasColumnName("PickupContactPhone").HasMaxLength(20);
                    addr.Property(a => a.SpecialInstructions).HasColumnName("PickupSpecialInstructions").HasMaxLength(500);
                });

                // Delivery Address
                rd.OwnsOne(route => route.DeliveryAddress, addr =>
                {
                    addr.Property(a => a.Street).HasColumnName("DeliveryStreet").HasMaxLength(200);
                    addr.Property(a => a.City).HasColumnName("DeliveryCity").HasMaxLength(100);
                    addr.Property(a => a.State).HasColumnName("DeliveryState").HasMaxLength(100);
                    addr.Property(a => a.PostalCode).HasColumnName("DeliveryPostalCode").HasMaxLength(20);
                    addr.Property(a => a.Country).HasColumnName("DeliveryCountry").HasMaxLength(100);
                    addr.Property(a => a.Latitude).HasColumnName("DeliveryLatitude").HasPrecision(10, 7);
                    addr.Property(a => a.Longitude).HasColumnName("DeliveryLongitude").HasPrecision(10, 7);
                    addr.Property(a => a.ContactPerson).HasColumnName("DeliveryContactPerson").HasMaxLength(100);
                    addr.Property(a => a.ContactPhone).HasColumnName("DeliveryContactPhone").HasMaxLength(20);
                    addr.Property(a => a.SpecialInstructions).HasColumnName("DeliverySpecialInstructions").HasMaxLength(500);
                });

                // Time Windows
                rd.OwnsOne(route => route.PickupTimeWindow, tw =>
                {
                    tw.Property(t => t.StartTime).HasColumnName("PickupTimeWindowStart");
                    tw.Property(t => t.EndTime).HasColumnName("PickupTimeWindowEnd");
                });

                rd.OwnsOne(route => route.DeliveryTimeWindow, tw =>
                {
                    tw.Property(t => t.StartTime).HasColumnName("DeliveryTimeWindowStart");
                    tw.Property(t => t.EndTime).HasColumnName("DeliveryTimeWindowEnd");
                });

                // Distance
                rd.OwnsOne(route => route.EstimatedDistance, dist =>
                {
                    dist.Property(d => d.Value).HasColumnName("EstimatedDistanceValue").HasPrecision(10, 2);
                    dist.Property(d => d.Unit).HasColumnName("EstimatedDistanceUnit");
                });

                // Intermediate stops as JSON
                rd.Property(route => route.IntermediateStops)
                    .HasColumnName("IntermediateStops")
                    .HasColumnType("jsonb");
            });

        // Similar configuration for Order RouteDetails
        modelBuilder.Entity<Order>()
            .OwnsOne(o => o.RouteDetails, rd =>
            {
                // Same configuration as RFQ RouteDetails
                rd.Property(route => route.PreferredPickupDate).HasColumnName("PreferredPickupDate");
                rd.Property(route => route.PreferredDeliveryDate).HasColumnName("PreferredDeliveryDate");
                rd.Property(route => route.EstimatedDuration).HasColumnName("EstimatedDuration");
                rd.Property(route => route.RouteNotes).HasColumnName("RouteNotes").HasMaxLength(1000);
                rd.Property(route => route.IsFlexiblePickup).HasColumnName("IsFlexiblePickup");
                rd.Property(route => route.IsFlexibleDelivery).HasColumnName("IsFlexibleDelivery");

                rd.OwnsOne(route => route.PickupAddress, addr =>
                {
                    addr.Property(a => a.Street).HasColumnName("PickupStreet").HasMaxLength(200);
                    addr.Property(a => a.City).HasColumnName("PickupCity").HasMaxLength(100);
                    addr.Property(a => a.State).HasColumnName("PickupState").HasMaxLength(100);
                    addr.Property(a => a.PostalCode).HasColumnName("PickupPostalCode").HasMaxLength(20);
                    addr.Property(a => a.Country).HasColumnName("PickupCountry").HasMaxLength(100);
                    addr.Property(a => a.Latitude).HasColumnName("PickupLatitude").HasPrecision(10, 7);
                    addr.Property(a => a.Longitude).HasColumnName("PickupLongitude").HasPrecision(10, 7);
                    addr.Property(a => a.ContactPerson).HasColumnName("PickupContactPerson").HasMaxLength(100);
                    addr.Property(a => a.ContactPhone).HasColumnName("PickupContactPhone").HasMaxLength(20);
                    addr.Property(a => a.SpecialInstructions).HasColumnName("PickupSpecialInstructions").HasMaxLength(500);
                });

                rd.OwnsOne(route => route.DeliveryAddress, addr =>
                {
                    addr.Property(a => a.Street).HasColumnName("DeliveryStreet").HasMaxLength(200);
                    addr.Property(a => a.City).HasColumnName("DeliveryCity").HasMaxLength(100);
                    addr.Property(a => a.State).HasColumnName("DeliveryState").HasMaxLength(100);
                    addr.Property(a => a.PostalCode).HasColumnName("DeliveryPostalCode").HasMaxLength(20);
                    addr.Property(a => a.Country).HasColumnName("DeliveryCountry").HasMaxLength(100);
                    addr.Property(a => a.Latitude).HasColumnName("DeliveryLatitude").HasPrecision(10, 7);
                    addr.Property(a => a.Longitude).HasColumnName("DeliveryLongitude").HasPrecision(10, 7);
                    addr.Property(a => a.ContactPerson).HasColumnName("DeliveryContactPerson").HasMaxLength(100);
                    addr.Property(a => a.ContactPhone).HasColumnName("DeliveryContactPhone").HasMaxLength(20);
                    addr.Property(a => a.SpecialInstructions).HasColumnName("DeliverySpecialInstructions").HasMaxLength(500);
                });

                rd.OwnsOne(route => route.PickupTimeWindow, tw =>
                {
                    tw.Property(t => t.StartTime).HasColumnName("PickupTimeWindowStart");
                    tw.Property(t => t.EndTime).HasColumnName("PickupTimeWindowEnd");
                });

                rd.OwnsOne(route => route.DeliveryTimeWindow, tw =>
                {
                    tw.Property(t => t.StartTime).HasColumnName("DeliveryTimeWindowStart");
                    tw.Property(t => t.EndTime).HasColumnName("DeliveryTimeWindowEnd");
                });

                rd.OwnsOne(route => route.EstimatedDistance, dist =>
                {
                    dist.Property(d => d.Value).HasColumnName("EstimatedDistanceValue").HasPrecision(10, 2);
                    dist.Property(d => d.Unit).HasColumnName("EstimatedDistanceUnit");
                });

                rd.Property(route => route.IntermediateStops)
                    .HasColumnName("IntermediateStops")
                    .HasColumnType("jsonb");
            });
    }

    private static void ConfigureRfqRequirements(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<RFQ>()
            .OwnsOne(r => r.Requirements, req =>
            {
                req.Property(r => r.RequiredVehicleType).HasColumnName("RequiredVehicleType");
                req.Property(r => r.MinVehicleCapacity).HasColumnName("MinVehicleCapacity");
                req.Property(r => r.RequiresInsurance).HasColumnName("RequiresInsurance");
                req.Property(r => r.MinInsuranceAmount).HasColumnName("MinInsuranceAmount").HasPrecision(18, 2);
                req.Property(r => r.RequiresLicense).HasColumnName("RequiresLicense");
                req.Property(r => r.RequiresExperience).HasColumnName("RequiresExperience");
                req.Property(r => r.MinYearsExperience).HasColumnName("MinYearsExperience");
                req.Property(r => r.RequiresBackground).HasColumnName("RequiresBackground");
                req.Property(r => r.RequiresTracking).HasColumnName("RequiresTracking");
                req.Property(r => r.RequiresTemperatureControl).HasColumnName("RequiresTemperatureControl");

                req.Property(r => r.RequiredLicenses)
                    .HasColumnName("RequiredLicenses")
                    .HasColumnType("jsonb");

                req.Property(r => r.AdditionalRequirements)
                    .HasColumnName("AdditionalRequirements")
                    .HasColumnType("jsonb");

                req.OwnsOne(r => r.TemperatureRange, tr =>
                {
                    tr.Property(temp => temp.MinTemperature).HasColumnName("RequiredMinTemperature").HasPrecision(5, 2);
                    tr.Property(temp => temp.MaxTemperature).HasColumnName("RequiredMaxTemperature").HasPrecision(5, 2);
                    tr.Property(temp => temp.Unit).HasColumnName("RequiredTemperatureUnit").HasMaxLength(1);
                });
            });
    }

    private static void ConfigureCustomerDetails(ModelBuilder modelBuilder)
    {
        // Note: Order and Invoice entities do not have CustomerDetails properties
        // This configuration has been removed as these properties don't exist in the domain entities
    }

    private static void ConfigureBillingDetails(ModelBuilder modelBuilder)
    {
        // Note: Order and Invoice entities do not have BillingDetails properties
        // This configuration has been removed as these properties don't exist in the domain entities
    }
}
