using System;
using Identity.Domain.Common;

namespace Identity.Domain.Entities
{
    public class RolePermission : Entity
    {
        public Guid RoleId { get; private set; }
        public Guid PermissionId { get; private set; }

        private RolePermission() { }

        public RolePermission(Guid roleId, Guid permissionId)
        {
            RoleId = roleId;
            PermissionId = permissionId;
            CreatedAt = DateTime.UtcNow;
        }
    }
}
