using Microsoft.EntityFrameworkCore;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Interfaces;
using SubscriptionManagement.Infrastructure.Persistence;

namespace SubscriptionManagement.Infrastructure.Repositories
{
    public class TaxCategoryRepository : ITaxCategoryRepository
    {
        private readonly SubscriptionDbContext _context;

        public TaxCategoryRepository(SubscriptionDbContext context)
        {
            _context = context;
        }

        public async Task<TaxCategory?> GetByIdAsync(Guid id)
        {
            return await _context.TaxCategories
                .FirstOrDefaultAsync(tc => tc.Id == id);
        }

        public async Task<TaxCategory?> GetByCodeAsync(string code)
        {
            return await _context.TaxCategories
                .FirstOrDefaultAsync(tc => tc.Code == code.ToUpperInvariant());
        }

        public async Task<List<TaxCategory>> GetAllAsync()
        {
            return await _context.TaxCategories
                .OrderBy(tc => tc.Name)
                .ToListAsync();
        }

        public async Task<List<TaxCategory>> GetActiveAsync()
        {
            return await _context.TaxCategories
                .Where(tc => tc.IsActive)
                .OrderBy(tc => tc.Name)
                .ToListAsync();
        }

        public async Task<List<TaxCategory>> GetByRegionAsync(string region)
        {
            var upperRegion = region.ToUpperInvariant();
            return await _context.TaxCategories
                .Where(tc => tc.IsActive && 
                            tc.TaxConfigurations.Any(config => 
                                config.ApplicableRegions.Contains(upperRegion) &&
                                config.IsActiveOn(DateTime.UtcNow)))
                .OrderBy(tc => tc.Name)
                .ToListAsync();
        }

        public async Task<TaxCategory> AddAsync(TaxCategory taxCategory)
        {
            _context.TaxCategories.Add(taxCategory);
            await _context.SaveChangesAsync();
            return taxCategory;
        }

        public async Task<TaxCategory> UpdateAsync(TaxCategory taxCategory)
        {
            _context.TaxCategories.Update(taxCategory);
            await _context.SaveChangesAsync();
            return taxCategory;
        }

        public async Task DeleteAsync(Guid id)
        {
            var taxCategory = await GetByIdAsync(id);
            if (taxCategory != null)
            {
                _context.TaxCategories.Remove(taxCategory);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<bool> ExistsAsync(Guid id)
        {
            return await _context.TaxCategories
                .AnyAsync(tc => tc.Id == id);
        }

        public async Task<bool> ExistsByCodeAsync(string code)
        {
            return await _context.TaxCategories
                .AnyAsync(tc => tc.Code == code.ToUpperInvariant());
        }

        public async Task<List<TaxCategory>> GetByTaxTypeAsync(TaxType taxType)
        {
            return await _context.TaxCategories
                .Where(tc => tc.IsActive && 
                            tc.TaxConfigurations.Any(config => 
                                config.TaxType == taxType &&
                                config.IsActiveOn(DateTime.UtcNow)))
                .OrderBy(tc => tc.Name)
                .ToListAsync();
        }

        public async Task<List<TaxCategory>> SearchAsync(string searchTerm, int page = 1, int pageSize = 10)
        {
            var query = _context.TaxCategories.AsQueryable();

            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                var lowerSearchTerm = searchTerm.ToLowerInvariant();
                query = query.Where(tc => 
                    tc.Name.ToLower().Contains(lowerSearchTerm) ||
                    tc.Description.ToLower().Contains(lowerSearchTerm) ||
                    tc.Code.ToLower().Contains(lowerSearchTerm));
            }

            return await query
                .OrderBy(tc => tc.Name)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
        }

        public async Task<int> GetCountAsync()
        {
            return await _context.TaxCategories.CountAsync();
        }
    }
}
