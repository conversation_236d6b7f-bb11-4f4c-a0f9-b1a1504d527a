
using Shared.Domain.Common;
using MobileWorkflow.Domain.Events;


namespace MobileWorkflow.Domain.Entities;

public class Workflow : AggregateRoot
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public string Category { get; private set; } // OrderProcessing, TripManagement, DocumentVerification, etc.
    public string Version { get; private set; }
    public bool IsActive { get; private set; }
    public Dictionary<string, object> Definition { get; private set; } // Workflow definition in JSON
    public Dictionary<string, object> Configuration { get; private set; }
    public string TriggerType { get; private set; } // Manual, Event, Schedule, API
    public Dictionary<string, object> TriggerConfiguration { get; private set; }
    public string CreatedBy { get; private set; }
    public DateTime? LastExecuted { get; private set; }
    public int ExecutionCount { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    public virtual ICollection<WorkflowExecution> Executions { get; private set; } = new List<WorkflowExecution>();
    public virtual ICollection<WorkflowTask> Tasks { get; private set; } = new List<WorkflowTask>();

    private Workflow() { } // EF Core

    public Workflow(
        string name,
        string description,
        string category,
        string version,
        Dictionary<string, object> definition,
        Dictionary<string, object> configuration,
        string triggerType,
        Dictionary<string, object> triggerConfiguration,
        string createdBy)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        Category = category ?? throw new ArgumentNullException(nameof(category));
        Version = version ?? throw new ArgumentNullException(nameof(version));
        Definition = definition ?? throw new ArgumentNullException(nameof(definition));
        Configuration = configuration ?? new Dictionary<string, object>();
        TriggerType = triggerType ?? throw new ArgumentNullException(nameof(triggerType));
        TriggerConfiguration = triggerConfiguration ?? new Dictionary<string, object>();
        CreatedBy = createdBy ?? throw new ArgumentNullException(nameof(createdBy));
        IsActive = true;
        ExecutionCount = 0;
        Metadata = new Dictionary<string, object>();

        AddDomainEvent(new WorkflowCreatedEvent(Id, Name, Category, CreatedBy));
    }

    public void UpdateDefinition(Dictionary<string, object> definition, string version)
    {
        Definition = definition ?? throw new ArgumentNullException(nameof(definition));
        Version = version ?? throw new ArgumentNullException(nameof(version));

        AddDomainEvent(new WorkflowDefinitionUpdatedEvent(Id, Name, Version));
    }

    public void UpdateConfiguration(Dictionary<string, object> configuration)
    {
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        AddDomainEvent(new WorkflowConfigurationUpdatedEvent(Id, Name));
    }

    public void Activate()
    {
        IsActive = true;
        AddDomainEvent(new WorkflowActivatedEvent(Id, Name, Category));
    }

    public void Deactivate()
    {
        IsActive = false;
        AddDomainEvent(new WorkflowDeactivatedEvent(Id, Name, Category));
    }

    public WorkflowExecution StartExecution(Guid triggeredBy, Dictionary<string, object> inputData, string? triggerSource = null)
    {
        if (!IsActive)
            throw new InvalidOperationException($"Cannot execute inactive workflow: {Name}");

        var execution = new WorkflowExecution(Id, triggeredBy, inputData, triggerSource);
        Executions.Add(execution);

        ExecutionCount++;
        LastExecuted = DateTime.UtcNow;

        AddDomainEvent(new WorkflowExecutionStartedEvent(Id, Id, Name, triggeredBy.ToString()));

        return execution;
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    public bool CanExecute()
    {
        return IsActive && Definition.Count > 0;
    }
}



