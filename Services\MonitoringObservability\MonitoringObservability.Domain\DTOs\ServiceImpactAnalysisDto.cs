namespace MonitoringObservability.Domain.DTOs;

/// <summary>
/// DTO for service impact analysis
/// </summary>
public class ServiceImpactAnalysisDto
{
    public string ServiceName { get; set; } = string.Empty;
    public string ImpactLevel { get; set; } = string.Empty;
    public List<string> AffectedServices { get; set; } = new();
    public List<string> DependentServices { get; set; } = new();
    public ImpactMetrics Metrics { get; set; } = new();
    public List<ImpactScenario> Scenarios { get; set; } = new();
    public DateTime AnalyzedAt { get; set; }
}

public class ImpactMetrics
{
    public int TotalAffectedServices { get; set; }
    public int CriticalDependencies { get; set; }
    public double ImpactScore { get; set; }
    public TimeSpan EstimatedRecoveryTime { get; set; }
}

public class ImpactScenario
{
    public string ScenarioName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public double Probability { get; set; }
    public string ImpactLevel { get; set; } = string.Empty;
    public List<string> MitigationSteps { get; set; } = new();
}
