using FluentAssertions;
using OrderManagement.Domain.Enums;
using OrderManagement.Domain.ValueObjects;
using Xunit;

namespace OrderManagement.Tests.Domain.ValueObjects;

public class RfqTimeframeTests
{
    [Fact]
    public void Constructor_WithValidParameters_ShouldCreateTimeframe()
    {
        // Arrange
        var duration = 72;
        var unit = TimeframeUnit.Hours;
        var allowExtensions = true;
        var maxExtensions = 3;

        // Act
        var timeframe = new RfqTimeframe(duration, unit, allowExtensions, maxExtensions);

        // Assert
        timeframe.Duration.Should().Be(duration);
        timeframe.Unit.Should().Be(unit);
        timeframe.AllowExtensions.Should().Be(allowExtensions);
        timeframe.MaxExtensions.Should().Be(maxExtensions);
        timeframe.ExtensionCount.Should().Be(0);
        timeframe.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        timeframe.ExpiresAt.Should().BeCloseTo(DateTime.UtcNow.AddHours(72), TimeSpan.FromSeconds(1));
    }

    [Theory]
    [InlineData(0)]
    [InlineData(-1)]
    [InlineData(-10)]
    public void Constructor_WithInvalidDuration_ShouldThrowArgumentException(int invalidDuration)
    {
        // Act & Assert
        var act = () => new RfqTimeframe(invalidDuration, TimeframeUnit.Hours);
        act.Should().Throw<ArgumentException>()
            .WithMessage("Duration must be greater than zero*");
    }

    [Theory]
    [InlineData(-1)]
    [InlineData(-5)]
    public void Constructor_WithNegativeMaxExtensions_ShouldThrowArgumentException(int invalidMaxExtensions)
    {
        // Act & Assert
        var act = () => new RfqTimeframe(24, TimeframeUnit.Hours, true, invalidMaxExtensions);
        act.Should().Throw<ArgumentException>()
            .WithMessage("Max extensions cannot be negative*");
    }

    [Fact]
    public void IsExpired_WhenTimeframeHasExpired_ShouldReturnTrue()
    {
        // Arrange
        var timeframe = new RfqTimeframe(1, TimeframeUnit.Minutes);
        Thread.Sleep(61000); // Wait for 61 seconds

        // Act
        var isExpired = timeframe.IsExpired;

        // Assert
        isExpired.Should().BeTrue();
    }

    [Fact]
    public void IsExpired_WhenTimeframeHasNotExpired_ShouldReturnFalse()
    {
        // Arrange
        var timeframe = new RfqTimeframe(24, TimeframeUnit.Hours);

        // Act
        var isExpired = timeframe.IsExpired;

        // Assert
        isExpired.Should().BeFalse();
    }

    [Fact]
    public void IsApproachingExpiration_WhenWithin24Hours_ShouldReturnTrue()
    {
        // Arrange
        var timeframe = new RfqTimeframe(12, TimeframeUnit.Hours);

        // Act
        var isApproaching = timeframe.IsApproachingExpiration;

        // Assert
        isApproaching.Should().BeTrue();
    }

    [Fact]
    public void IsExpiringSoon_WithSpecificHours_ShouldReturnCorrectValue()
    {
        // Arrange
        var timeframe = new RfqTimeframe(6, TimeframeUnit.Hours);

        // Act
        var isExpiringSoon12Hours = timeframe.IsExpiringSoon(12);
        var isExpiringSoon3Hours = timeframe.IsExpiringSoon(3);

        // Assert
        isExpiringSoon12Hours.Should().BeTrue();
        isExpiringSoon3Hours.Should().BeFalse();
    }

    [Fact]
    public void CanExtend_WhenExtensionsAllowedAndUnderLimit_ShouldReturnTrue()
    {
        // Arrange
        var timeframe = new RfqTimeframe(24, TimeframeUnit.Hours, true, 3);

        // Act
        var canExtend = timeframe.CanExtend;

        // Assert
        canExtend.Should().BeTrue();
    }

    [Fact]
    public void CanExtend_WhenExtensionsNotAllowed_ShouldReturnFalse()
    {
        // Arrange
        var timeframe = new RfqTimeframe(24, TimeframeUnit.Hours, false, 3);

        // Act
        var canExtend = timeframe.CanExtend;

        // Assert
        canExtend.Should().BeFalse();
    }

    [Fact]
    public void TotalMinutes_ShouldCalculateCorrectly()
    {
        // Arrange & Act
        var minutesTimeframe = new RfqTimeframe(30, TimeframeUnit.Minutes);
        var hoursTimeframe = new RfqTimeframe(2, TimeframeUnit.Hours);
        var daysTimeframe = new RfqTimeframe(1, TimeframeUnit.Days);

        // Assert
        minutesTimeframe.TotalMinutes.Should().Be(30);
        hoursTimeframe.TotalMinutes.Should().Be(120);
        daysTimeframe.TotalMinutes.Should().Be(1440);
    }

    [Fact]
    public void Extend_WithValidParameters_ShouldCreateExtendedTimeframe()
    {
        // Arrange
        var originalTimeframe = new RfqTimeframe(24, TimeframeUnit.Hours, true, 3);
        var extensionDuration = 12;
        var extensionUnit = TimeframeUnit.Hours;

        // Act
        var extendedTimeframe = originalTimeframe.Extend(extensionDuration, extensionUnit);

        // Assert
        extendedTimeframe.ExtensionCount.Should().Be(1);
        extendedTimeframe.ExpiresAt.Should().BeCloseTo(
            originalTimeframe.ExpiresAt.AddHours(12), TimeSpan.FromSeconds(1));
        extendedTimeframe.Duration.Should().Be(originalTimeframe.Duration);
        extendedTimeframe.Unit.Should().Be(originalTimeframe.Unit);
    }

    [Fact]
    public void Extend_WhenCannotExtend_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var timeframe = new RfqTimeframe(24, TimeframeUnit.Hours, false, 3);

        // Act & Assert
        var act = () => timeframe.Extend(12, TimeframeUnit.Hours);
        act.Should().Throw<InvalidOperationException>()
            .WithMessage("Cannot extend RFQ timeframe*");
    }

    [Fact]
    public void RemainingTime_ShouldCalculateCorrectly()
    {
        // Arrange
        var timeframe = new RfqTimeframe(1, TimeframeUnit.Hours);

        // Act
        var remainingTime = timeframe.RemainingTime;

        // Assert
        remainingTime.Should().BeCloseTo(TimeSpan.FromHours(1), TimeSpan.FromSeconds(1));
    }
}

public class TimeframeExtensionTests
{
    [Fact]
    public void Constructor_WithValidParameters_ShouldCreateExtension()
    {
        // Arrange
        var duration = 12;
        var unit = TimeframeUnit.Hours;
        var reason = "Need more time to review bids";
        var extendedBy = Guid.NewGuid();
        var newExpiresAt = DateTime.UtcNow.AddHours(36);

        // Act
        var extension = new TimeframeExtension(duration, unit, reason, extendedBy, newExpiresAt);

        // Assert
        extension.Duration.Should().Be(duration);
        extension.Unit.Should().Be(unit);
        extension.Reason.Should().Be(reason);
        extension.ExtendedBy.Should().Be(extendedBy);
        extension.NewExpiresAt.Should().Be(newExpiresAt);
        extension.ExtendedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Theory]
    [InlineData(0)]
    [InlineData(-1)]
    public void Constructor_WithInvalidDuration_ShouldThrowArgumentException(int invalidDuration)
    {
        // Act & Assert
        var act = () => new TimeframeExtension(invalidDuration, TimeframeUnit.Hours, "reason", Guid.NewGuid(), DateTime.UtcNow);
        act.Should().Throw<ArgumentException>()
            .WithMessage("Extension duration must be greater than zero*");
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public void Constructor_WithInvalidReason_ShouldThrowArgumentException(string invalidReason)
    {
        // Act & Assert
        var act = () => new TimeframeExtension(12, TimeframeUnit.Hours, invalidReason, Guid.NewGuid(), DateTime.UtcNow);
        act.Should().Throw<ArgumentException>()
            .WithMessage("Extension reason is required*");
    }

    [Fact]
    public void ExtensionMinutes_ShouldCalculateCorrectly()
    {
        // Arrange & Act
        var minutesExtension = new TimeframeExtension(30, TimeframeUnit.Minutes, "reason", Guid.NewGuid(), DateTime.UtcNow);
        var hoursExtension = new TimeframeExtension(2, TimeframeUnit.Hours, "reason", Guid.NewGuid(), DateTime.UtcNow);
        var daysExtension = new TimeframeExtension(1, TimeframeUnit.Days, "reason", Guid.NewGuid(), DateTime.UtcNow);

        // Assert
        minutesExtension.ExtensionMinutes.Should().Be(30);
        hoursExtension.ExtensionMinutes.Should().Be(120);
        daysExtension.ExtensionMinutes.Should().Be(1440);
    }
}
