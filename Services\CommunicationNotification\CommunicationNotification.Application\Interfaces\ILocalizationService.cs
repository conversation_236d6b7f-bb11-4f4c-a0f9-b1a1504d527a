using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;

namespace CommunicationNotification.Application.Interfaces;

/// <summary>
/// Interface for localization and multi-language support
/// </summary>
public interface ILocalizationService
{
    /// <summary>
    /// Translate message content to target language
    /// </summary>
    /// <param name="content">Original message content</param>
    /// <param name="targetLanguage">Target language</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Translated message content</returns>
    Task<MessageContent> TranslateAsync(
        MessageContent content,
        Language targetLanguage,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get localized template
    /// </summary>
    /// <param name="templateKey">Template key</param>
    /// <param name="language">Target language</param>
    /// <param name="parameters">Template parameters</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Localized message content</returns>
    Task<MessageContent> GetLocalizedTemplateAsync(
        string templateKey,
        Language language,
        Dictionary<string, object>? parameters = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get supported languages
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of supported languages</returns>
    Task<List<LanguageInfo>> GetSupportedLanguagesAsync(
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Detect language of text
    /// </summary>
    /// <param name="text">Text to analyze</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Detected language</returns>
    Task<Language> DetectLanguageAsync(
        string text,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get localized string
    /// </summary>
    /// <param name="key">Localization key</param>
    /// <param name="language">Target language</param>
    /// <param name="parameters">Parameters for string formatting</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Localized string</returns>
    Task<string> GetLocalizedStringAsync(
        string key,
        Language language,
        Dictionary<string, object>? parameters = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Add or update localized template
    /// </summary>
    /// <param name="templateKey">Template key</param>
    /// <param name="language">Language</param>
    /// <param name="template">Template content</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if successful</returns>
    Task<bool> SetLocalizedTemplateAsync(
        string templateKey,
        Language language,
        LocalizedTemplate template,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all templates for a language
    /// </summary>
    /// <param name="language">Language</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Dictionary of templates</returns>
    Task<Dictionary<string, LocalizedTemplate>> GetTemplatesAsync(
        Language language,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface for template management
/// </summary>
public interface ITemplateService
{
    /// <summary>
    /// Get message template
    /// </summary>
    /// <param name="templateKey">Template key</param>
    /// <param name="language">Language</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Message template</returns>
    Task<MessageTemplate?> GetTemplateAsync(
        string templateKey,
        Language language,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Create message from template
    /// </summary>
    /// <param name="templateKey">Template key</param>
    /// <param name="language">Language</param>
    /// <param name="parameters">Template parameters</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Generated message content</returns>
    Task<MessageContent> CreateMessageFromTemplateAsync(
        string templateKey,
        Language language,
        Dictionary<string, object> parameters,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate template parameters
    /// </summary>
    /// <param name="templateKey">Template key</param>
    /// <param name="parameters">Parameters to validate</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Validation result</returns>
    Task<TemplateValidationResult> ValidateTemplateParametersAsync(
        string templateKey,
        Dictionary<string, object> parameters,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all available templates
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of available templates</returns>
    Task<List<TemplateInfo>> GetAvailableTemplatesAsync(
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Create or update template
    /// </summary>
    /// <param name="template">Template to create or update</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if successful</returns>
    Task<bool> SaveTemplateAsync(
        MessageTemplate template,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete template
    /// </summary>
    /// <param name="templateKey">Template key</param>
    /// <param name="language">Language</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if deleted successfully</returns>
    Task<bool> DeleteTemplateAsync(
        string templateKey,
        Language language,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Language information
/// </summary>
public class LanguageInfo
{
    public Language Language { get; set; }
    public string Code { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string NativeName { get; set; } = string.Empty;
    public bool IsRightToLeft { get; set; }
    public bool IsSupported { get; set; } = true;
}

/// <summary>
/// Localized template
/// </summary>
public class LocalizedTemplate
{
    public string Key { get; set; } = string.Empty;
    public Language Language { get; set; }
    public string Subject { get; set; } = string.Empty;
    public string Body { get; set; } = string.Empty;
    public List<string> RequiredParameters { get; set; } = new();
    public Dictionary<string, string> Metadata { get; set; } = new();
}

/// <summary>
/// Message template
/// </summary>
public class MessageTemplate
{
    public string Key { get; set; } = string.Empty;
    public Language Language { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Subject { get; set; } = string.Empty;
    public string Body { get; set; } = string.Empty;
    public List<TemplateParameter> Parameters { get; set; } = new();
    public MessageType MessageType { get; set; }
    public List<NotificationChannel> SupportedChannels { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// Template parameter
/// </summary>
public class TemplateParameter
{
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = "string";
    public bool IsRequired { get; set; } = true;
    public string? DefaultValue { get; set; }
    public string? Description { get; set; }
    public List<string>? AllowedValues { get; set; }
}

/// <summary>
/// Template validation result
/// </summary>
public class TemplateValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> MissingParameters { get; set; } = new();
    public List<string> InvalidParameters { get; set; } = new();

    public static TemplateValidationResult Success()
    {
        return new TemplateValidationResult { IsValid = true };
    }

    public static TemplateValidationResult Failure(List<string> errors)
    {
        return new TemplateValidationResult
        {
            IsValid = false,
            Errors = errors
        };
    }
}

/// <summary>
/// Template information
/// </summary>
public class TemplateInfo
{
    public string Key { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public MessageType MessageType { get; set; }
    public List<Language> AvailableLanguages { get; set; } = new();
    public List<NotificationChannel> SupportedChannels { get; set; } = new();
    public List<string> RequiredParameters { get; set; } = new();
    public bool IsActive { get; set; } = true;
}
