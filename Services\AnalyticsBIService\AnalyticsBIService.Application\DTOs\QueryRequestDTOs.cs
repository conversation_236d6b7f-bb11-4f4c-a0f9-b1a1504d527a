using System.ComponentModel.DataAnnotations;
using AnalyticsBIService.Domain.Enums;

namespace AnalyticsBIService.Application.DTOs;

/// <summary>
/// Get broker financial performance request
/// </summary>
public class GetBrokerFinancialPerformanceRequest
{
    [Required]
    public string BrokerId { get; set; } = string.Empty;

    [Required]
    public DateTime StartDate { get; set; }

    [Required]
    public DateTime EndDate { get; set; }

    public List<string> MetricTypes { get; set; } = new();
    public bool IncludeComparisons { get; set; } = true;
    public bool IncludeTrends { get; set; } = true;
    public string ComparisonPeriod { get; set; } = "PreviousPeriod"; // "PreviousPeriod", "SamePeriodLastYear"
}

/// <summary>
/// Get broker market analytics query
/// </summary>
public class GetBrokerMarketAnalyticsQuery
{
    [Required]
    public string BrokerId { get; set; } = string.Empty;

    [Required]
    public DateTime StartDate { get; set; }

    [Required]
    public DateTime EndDate { get; set; }

    public List<string> Markets { get; set; } = new();
    public List<string> ServiceTypes { get; set; } = new();
    public bool IncludeCompetitorAnalysis { get; set; } = true;
    public bool IncludeMarketTrends { get; set; } = true;
}

/// <summary>
/// Get broker operational analytics request
/// </summary>
public class GetBrokerOperationalAnalyticsRequest
{
    [Required]
    public string BrokerId { get; set; } = string.Empty;

    [Required]
    public DateTime StartDate { get; set; }

    [Required]
    public DateTime EndDate { get; set; }

    public List<string> OperationalAreas { get; set; } = new();
    public bool IncludeEfficiencyMetrics { get; set; } = true;
    public bool IncludePerformanceTrends { get; set; } = true;
}

/// <summary>
/// Get broker relationship analytics query
/// </summary>
public class GetBrokerRelationshipAnalyticsQuery
{
    [Required]
    public string BrokerId { get; set; } = string.Empty;

    [Required]
    public DateTime StartDate { get; set; }

    [Required]
    public DateTime EndDate { get; set; }

    public List<string> RelationshipTypes { get; set; } = new(); // "Carrier", "Shipper", "Partner"
    public bool IncludeSatisfactionMetrics { get; set; } = true;
    public bool IncludeRetentionAnalysis { get; set; } = true;
}

/// <summary>
/// Get broker performance benchmarking query
/// </summary>
public class GetBrokerPerformanceBenchmarkingQuery
{
    [Required]
    public string BrokerId { get; set; } = string.Empty;

    [Required]
    public DateTime StartDate { get; set; }

    [Required]
    public DateTime EndDate { get; set; }

    public List<string> BenchmarkCategories { get; set; } = new();
    public bool IncludeIndustryComparison { get; set; } = true;
    public bool IncludePeerComparison { get; set; } = true;
    public string Region { get; set; } = string.Empty;
}

/// <summary>
/// Get broker quote analytics query
/// </summary>
public class GetBrokerQuoteAnalyticsQuery
{
    [Required]
    public string BrokerId { get; set; } = string.Empty;

    [Required]
    public DateTime StartDate { get; set; }

    [Required]
    public DateTime EndDate { get; set; }

    public List<string> QuoteTypes { get; set; } = new();
    public bool IncludeConversionRates { get; set; } = true;
    public bool IncludeCompetitiveAnalysis { get; set; } = true;
}

/// <summary>
/// Get driver benchmarking query
/// </summary>
public class GetDriverBenchmarkingQuery
{
    [Required]
    public string DriverId { get; set; } = string.Empty;

    [Required]
    public DateTime StartDate { get; set; }

    [Required]
    public DateTime EndDate { get; set; }

    public List<string> BenchmarkCategories { get; set; } = new();
    public bool IncludePeerComparison { get; set; } = true;
    public bool IncludeIndustryStandards { get; set; } = true;
    public string ComparisonGroup { get; set; } = "SameCompany"; // "SameCompany", "SameRegion", "Industry"
}

/// <summary>
/// Get driver trip data query
/// </summary>
public class GetDriverTripDataQuery
{
    [Required]
    public string DriverId { get; set; } = string.Empty;

    [Required]
    public DateTime StartDate { get; set; }

    [Required]
    public DateTime EndDate { get; set; }

    public List<string> TripTypes { get; set; } = new();
    public List<string> Routes { get; set; } = new();
    public bool IncludePerformanceMetrics { get; set; } = true;
    public bool IncludeSafetyData { get; set; } = true;
}

/// <summary>
/// Get driver KPI analytics query
/// </summary>
public class GetDriverKPIAnalyticsQuery
{
    [Required]
    public string DriverId { get; set; } = string.Empty;

    [Required]
    public DateTime StartDate { get; set; }

    [Required]
    public DateTime EndDate { get; set; }

    public List<string> KPICategories { get; set; } = new();
    public bool IncludeTargets { get; set; } = true;
    public bool IncludeTrends { get; set; } = true;
}

/// <summary>
/// Get driver performance insight query
/// </summary>
public class GetDriverPerformanceInsightQuery
{
    [Required]
    public string DriverId { get; set; } = string.Empty;

    [Required]
    public DateTime StartDate { get; set; }

    [Required]
    public DateTime EndDate { get; set; }

    public List<string> InsightTypes { get; set; } = new();
    public bool IncludeRecommendations { get; set; } = true;
    public bool IncludeActionPlans { get; set; } = true;
}

/// <summary>
/// Get driver trend analysis query
/// </summary>
public class GetDriverTrendAnalysisQuery
{
    [Required]
    public string DriverId { get; set; } = string.Empty;

    [Required]
    public DateTime StartDate { get; set; }

    [Required]
    public DateTime EndDate { get; set; }

    public List<string> TrendCategories { get; set; } = new();
    public string TrendPeriod { get; set; } = "Monthly"; // "Daily", "Weekly", "Monthly", "Quarterly"
    public bool IncludeForecasting { get; set; } = false;
}

/// <summary>
/// Get driver safety analytics query
/// </summary>
public class GetDriverSafetyAnalyticsQuery
{
    [Required]
    public string DriverId { get; set; } = string.Empty;

    [Required]
    public DateTime StartDate { get; set; }

    [Required]
    public DateTime EndDate { get; set; }

    public List<string> SafetyCategories { get; set; } = new();
    public bool IncludeIncidents { get; set; } = true;
    public bool IncludeViolations { get; set; } = true;
    public bool IncludeTrainingRecommendations { get; set; } = true;
}

/// <summary>
/// Get driver efficiency analytics query
/// </summary>
public class GetDriverEfficiencyAnalyticsQuery
{
    [Required]
    public string DriverId { get; set; } = string.Empty;

    [Required]
    public DateTime StartDate { get; set; }

    [Required]
    public DateTime EndDate { get; set; }

    public List<string> EfficiencyMetrics { get; set; } = new();
    public bool IncludeFuelEfficiency { get; set; } = true;
    public bool IncludeTimeEfficiency { get; set; } = true;
    public bool IncludeRouteOptimization { get; set; } = true;
}

/// <summary>
/// Get shipper business reporting request
/// </summary>
public class GetShipperBusinessReportingRequest
{
    [Required]
    public string ShipperId { get; set; } = string.Empty;

    [Required]
    public DateTime StartDate { get; set; }

    [Required]
    public DateTime EndDate { get; set; }

    public List<string> ReportCategories { get; set; } = new();
    public bool IncludeFinancialMetrics { get; set; } = true;
    public bool IncludeOperationalMetrics { get; set; } = true;
    public bool IncludePerformanceMetrics { get; set; } = true;
}

/// <summary>
/// Get shipper route efficiency request
/// </summary>
public class GetShipperRouteEfficiencyRequest
{
    [Required]
    public string ShipperId { get; set; } = string.Empty;

    [Required]
    public DateTime StartDate { get; set; }

    [Required]
    public DateTime EndDate { get; set; }

    public List<string> Routes { get; set; } = new();
    public bool IncludeOptimizationSuggestions { get; set; } = true;
    public bool IncludeCostAnalysis { get; set; } = true;
}

/// <summary>
/// Get shipper service quality request
/// </summary>
public class GetShipperServiceQualityRequest
{
    [Required]
    public string ShipperId { get; set; } = string.Empty;

    [Required]
    public DateTime StartDate { get; set; }

    [Required]
    public DateTime EndDate { get; set; }

    public List<string> ServiceCategories { get; set; } = new();
    public bool IncludeCarrierPerformance { get; set; } = true;
    public bool IncludeCustomerFeedback { get; set; } = true;
}

/// <summary>
/// Get shipper financial performance query
/// </summary>
public class GetShipperFinancialPerformanceQuery
{
    [Required]
    public string ShipperId { get; set; } = string.Empty;

    [Required]
    public DateTime StartDate { get; set; }

    [Required]
    public DateTime EndDate { get; set; }

    public List<string> FinancialCategories { get; set; } = new();
    public bool IncludeCostBreakdown { get; set; } = true;
    public bool IncludeBudgetComparison { get; set; } = true;
}

/// <summary>
/// Get shipper carrier performance query
/// </summary>
public class GetShipperCarrierPerformanceQuery
{
    [Required]
    public string ShipperId { get; set; } = string.Empty;

    [Required]
    public DateTime StartDate { get; set; }

    [Required]
    public DateTime EndDate { get; set; }

    public List<string> CarrierIds { get; set; } = new();
    public bool IncludeRankings { get; set; } = true;
    public bool IncludePerformanceComparisons { get; set; } = true;
}

/// <summary>
/// Get shipper sustainability query
/// </summary>
public class GetShipperSustainabilityQuery
{
    [Required]
    public string ShipperId { get; set; } = string.Empty;

    [Required]
    public DateTime StartDate { get; set; }

    [Required]
    public DateTime EndDate { get; set; }

    public List<string> SustainabilityMetrics { get; set; } = new();
    public bool IncludeEmissionsData { get; set; } = true;
    public bool IncludeGreenInitiatives { get; set; } = true;
}

/// <summary>
/// Get custom visualization request
/// </summary>
public class GetCustomVisualizationRequest
{
    [Required]
    public string UserId { get; set; } = string.Empty;

    [Required]
    public string VisualizationType { get; set; } = string.Empty; // "Chart", "Graph", "Map", "Table", "Dashboard"

    [Required]
    public List<string> DataSources { get; set; } = new();

    [Required]
    public Dictionary<string, object> Configuration { get; set; } = new();

    public List<FilterCriteriaDto> Filters { get; set; } = new();
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
}

// FilterCriteriaDto is already defined in DashboardDTOs.cs - using that instead

/// <summary>
/// Get dashboard widget request
/// </summary>
public class GetDashboardWidgetRequest
{
    [Required]
    public string WidgetId { get; set; } = string.Empty;

    [Required]
    public string UserId { get; set; } = string.Empty;

    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public List<FilterCriteriaDto> Filters { get; set; } = new();
    public Dictionary<string, object> Parameters { get; set; } = new();
    public bool RefreshData { get; set; } = false;
}

/// <summary>
/// Widget interaction data transfer object
/// </summary>
public class GetWidgetInteractionDto
{
    [Required]
    public string WidgetId { get; set; } = string.Empty;

    [Required]
    public string UserId { get; set; } = string.Empty;

    [Required]
    public string InteractionType { get; set; } = string.Empty; // "Click", "Hover", "Filter", "Export", "Refresh"

    public Dictionary<string, object> InteractionData { get; set; } = new();
    public DateTime InteractionTime { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Create custom widget request
/// </summary>
public class CreateCustomWidgetRequest
{
    [Required]
    public string WidgetName { get; set; } = string.Empty;

    [Required]
    public string WidgetType { get; set; } = string.Empty; // "Chart", "KPI", "Table", "Map", "Gauge"

    [Required]
    public string UserId { get; set; } = string.Empty;

    [Required]
    public List<string> DataSources { get; set; } = new();

    [Required]
    public Dictionary<string, object> Configuration { get; set; } = new();

    public string Description { get; set; } = string.Empty;
    public List<FilterCriteriaDto> DefaultFilters { get; set; } = new();
    public Dictionary<string, object> Layout { get; set; } = new();
    public int RefreshInterval { get; set; } = 300; // seconds
    public bool IsPublic { get; set; } = false;
}

/// <summary>
/// Get custom dashboard request
/// </summary>
public class GetCustomDashboardRequest
{
    [Required]
    public string DashboardId { get; set; } = string.Empty;

    [Required]
    public string UserId { get; set; } = string.Empty;

    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public List<FilterCriteriaDto> GlobalFilters { get; set; } = new();
    public bool RefreshData { get; set; } = false;
}

/// <summary>
/// Get dashboard insight request
/// </summary>
public class GetDashboardInsightRequest
{
    [Required]
    public string DashboardId { get; set; } = string.Empty;

    [Required]
    public string UserId { get; set; } = string.Empty;

    public List<string> InsightTypes { get; set; } = new(); // "Trend", "Anomaly", "Correlation", "Forecast"
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public Dictionary<string, object> Parameters { get; set; } = new();
}

/// <summary>
/// Get dashboard export request
/// </summary>
public class GetDashboardExportRequest
{
    [Required]
    public string DashboardId { get; set; } = string.Empty;

    [Required]
    public string UserId { get; set; } = string.Empty;

    [Required]
    public ExportFormat Format { get; set; }

    public List<string> WidgetIds { get; set; } = new(); // Empty means export all widgets
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public bool IncludeData { get; set; } = true;
    public bool IncludeCharts { get; set; } = true;
    public Dictionary<string, object> ExportOptions { get; set; } = new();
}

/// <summary>
/// Get real-time KPI request
/// </summary>
public class GetRealTimeKPIRequest
{
    [Required]
    public string UserId { get; set; } = string.Empty;

    [Required]
    public UserType UserType { get; set; }

    public List<string> KPINames { get; set; } = new();
    public List<FilterCriteriaDto> Filters { get; set; } = new();
    public int RefreshInterval { get; set; } = 30; // seconds
    public bool IncludeHistoricalData { get; set; } = false;
}

/// <summary>
/// Get aggregated data request
/// </summary>
public class GetAggregatedDataRequest
{
    [Required]
    public List<string> DataSources { get; set; } = new();

    [Required]
    public string AggregationType { get; set; } = string.Empty; // "Sum", "Average", "Count", "Min", "Max", "GroupBy"

    [Required]
    public DateTime StartDate { get; set; }

    [Required]
    public DateTime EndDate { get; set; }

    public List<string> GroupByFields { get; set; } = new();
    public List<FilterCriteriaDto> Filters { get; set; } = new();
    public string TimeGranularity { get; set; } = "Daily"; // "Hourly", "Daily", "Weekly", "Monthly"
}

/// <summary>
/// Get visualization request
/// </summary>
public class GetVisualizationRequest
{
    [Required]
    public string VisualizationType { get; set; } = string.Empty;

    [Required]
    public List<string> DataSources { get; set; } = new();

    [Required]
    public Dictionary<string, object> Configuration { get; set; } = new();

    public List<FilterCriteriaDto> Filters { get; set; } = new();
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public string Title { get; set; } = string.Empty;
    public Dictionary<string, object> Styling { get; set; } = new();
}
