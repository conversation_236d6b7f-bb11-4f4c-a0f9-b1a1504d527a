using MediatR;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Exceptions;
using SubscriptionManagement.Domain.Enums;
using Shared.Messaging;

namespace SubscriptionManagement.Application.Commands.ProcessBilling
{
    public class ProcessBillingCommandHandler : IRequestHandler<ProcessBillingCommand, bool>
    {
        private readonly ISubscriptionRepository _subscriptionRepository;
        private readonly IPaymentService _paymentService;
        private readonly IMessageBroker _messageBroker;
        private readonly ILogger<ProcessBillingCommandHandler> _logger;

        public ProcessBillingCommandHandler(
            ISubscriptionRepository subscriptionRepository,
            IPaymentService paymentService,
            IMessageBroker messageBroker,
            ILogger<ProcessBillingCommandHandler> logger)
        {
            _subscriptionRepository = subscriptionRepository;
            _paymentService = paymentService;
            _messageBroker = messageBroker;
            _logger = logger;
        }

        public async Task<bool> Handle(ProcessBillingCommand request, CancellationToken cancellationToken)
        {
            var billingDate = request.BillingDate ?? DateTime.UtcNow;
            _logger.LogInformation("Processing billing for date {BillingDate}", billingDate);

            var subscriptions = request.SubscriptionId.HasValue
                ? new[] { await _subscriptionRepository.GetByIdAsync(request.SubscriptionId.Value) }.Where(s => s != null).ToList()
                : await _subscriptionRepository.GetSubscriptionsDueForBillingAsync(billingDate);

            if (!subscriptions.Any())
            {
                _logger.LogInformation("No subscriptions found for billing");
                return true;
            }

            var successCount = 0;
            var failureCount = 0;

            foreach (var subscription in subscriptions)
            {
                try
                {
                    if (subscription == null) continue;

                    // Skip if not due for billing (unless forced)
                    if (!request.ForceProcessing && subscription.NextBillingDate > billingDate)
                    {
                        continue;
                    }

                    // Skip if subscription is not active
                    if (subscription.Status != SubscriptionStatus.Active)
                    {
                        _logger.LogWarning("Skipping billing for inactive subscription {SubscriptionId}", subscription.Id);
                        continue;
                    }

                    // Process billing for this subscription
                    var billingResult = await ProcessSubscriptionBilling(subscription, billingDate, request.MaxRetries);
                    
                    if (billingResult)
                    {
                        successCount++;
                        _logger.LogInformation("Successfully processed billing for subscription {SubscriptionId}", subscription.Id);
                    }
                    else
                    {
                        failureCount++;
                        _logger.LogError("Failed to process billing for subscription {SubscriptionId}", subscription.Id);
                    }
                }
                catch (Exception ex)
                {
                    failureCount++;
                    _logger.LogError(ex, "Error processing billing for subscription {SubscriptionId}", subscription?.Id);
                }
            }

            // Publish billing summary event
            await _messageBroker.PublishAsync("billing.processed", new
            {
                BillingDate = billingDate,
                TotalSubscriptions = subscriptions.Count,
                SuccessCount = successCount,
                FailureCount = failureCount,
                ProcessedAt = DateTime.UtcNow
            });

            _logger.LogInformation("Billing processing completed. Success: {SuccessCount}, Failures: {FailureCount}", 
                successCount, failureCount);

            return failureCount == 0;
        }

        private async Task<bool> ProcessSubscriptionBilling(Domain.Entities.Subscription subscription, DateTime billingDate, int maxRetries)
        {
            var retryCount = 0;
            
            while (retryCount < maxRetries)
            {
                try
                {
                    // Process payment
                    var paymentResult = await _paymentService.ProcessSubscriptionPaymentAsync(
                        subscription, subscription.PaymentMethodId);

                    if (paymentResult.IsSuccess)
                    {
                        // Update subscription billing cycle
                        subscription.ProcessBilling(billingDate);
                        await _subscriptionRepository.UpdateAsync(subscription);

                        // Publish successful billing event
                        await _messageBroker.PublishAsync("subscription.billed", new
                        {
                            SubscriptionId = subscription.Id,
                            UserId = subscription.UserId,
                            PlanId = subscription.PlanId,
                            Amount = subscription.CurrentPrice.Amount,
                            Currency = subscription.CurrentPrice.Currency,
                            BillingDate = billingDate,
                            NextBillingDate = subscription.NextBillingDate,
                            PaymentId = paymentResult.PaymentId,
                            RetryAttempt = retryCount + 1
                        });

                        return true;
                    }
                    else
                    {
                        retryCount++;
                        _logger.LogWarning("Billing attempt {RetryCount} failed for subscription {SubscriptionId}: {Error}", 
                            retryCount, subscription.Id, paymentResult.ErrorMessage);

                        if (retryCount >= maxRetries)
                        {
                            // Mark subscription as suspended after max retries
                            subscription.Suspend($"Payment failed after {maxRetries} attempts: {paymentResult.ErrorMessage}");
                            await _subscriptionRepository.UpdateAsync(subscription);

                            // Publish billing failure event
                            await _messageBroker.PublishAsync("subscription.billing_failed", new
                            {
                                SubscriptionId = subscription.Id,
                                UserId = subscription.UserId,
                                PlanId = subscription.PlanId,
                                Amount = subscription.CurrentPrice.Amount,
                                Currency = subscription.CurrentPrice.Currency,
                                BillingDate = billingDate,
                                Error = paymentResult.ErrorMessage,
                                RetryAttempts = maxRetries,
                                SuspendedAt = DateTime.UtcNow
                            });

                            return false;
                        }

                        // Wait before retry (exponential backoff)
                        await Task.Delay(TimeSpan.FromSeconds(Math.Pow(2, retryCount)));
                    }
                }
                catch (Exception ex)
                {
                    retryCount++;
                    _logger.LogError(ex, "Exception during billing attempt {RetryCount} for subscription {SubscriptionId}", 
                        retryCount, subscription.Id);

                    if (retryCount >= maxRetries)
                    {
                        return false;
                    }

                    await Task.Delay(TimeSpan.FromSeconds(Math.Pow(2, retryCount)));
                }
            }

            return false;
        }
    }
}
