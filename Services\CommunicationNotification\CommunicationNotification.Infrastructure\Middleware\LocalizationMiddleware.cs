using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Domain.Enums;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System.Globalization;

namespace CommunicationNotification.Infrastructure.Middleware;

/// <summary>
/// Middleware for automatic language detection and culture setting
/// </summary>
public class LocalizationMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<LocalizationMiddleware> _logger;

    public LocalizationMiddleware(RequestDelegate next, ILogger<LocalizationMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context, ILocalizationService localizationService)
    {
        try
        {
            var language = DetectLanguage(context, localizationService);
            var culture = localizationService.GetCultureInfo(language);

            // Set the culture for the current thread
            CultureInfo.CurrentCulture = culture;
            CultureInfo.CurrentUICulture = culture;

            // Add language information to the context
            context.Items["CurrentLanguage"] = language;
            context.Items["CurrentCulture"] = culture;

            _logger.LogDebug("Language detected: {Language}, Culture set: {Culture}", language, culture.Name);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to detect language, using default");
            
            // Set default culture
            var defaultCulture = new CultureInfo("en-US");
            CultureInfo.CurrentCulture = defaultCulture;
            CultureInfo.CurrentUICulture = defaultCulture;
            context.Items["CurrentLanguage"] = Language.English;
            context.Items["CurrentCulture"] = defaultCulture;
        }

        await _next(context);
    }

    private static Language DetectLanguage(HttpContext context, ILocalizationService localizationService)
    {
        // Priority order for language detection:
        // 1. Query parameter
        // 2. Header
        // 3. User preference (from claims)
        // 4. Accept-Language header
        // 5. Default language

        // 1. Check query parameter
        if (context.Request.Query.TryGetValue("lang", out var langParam))
        {
            if (TryParseLanguage(langParam.ToString(), out var queryLanguage))
            {
                return queryLanguage;
            }
        }

        // 2. Check custom header
        if (context.Request.Headers.TryGetValue("X-Language", out var langHeader))
        {
            if (TryParseLanguage(langHeader.ToString(), out var headerLanguage))
            {
                return headerLanguage;
            }
        }

        // 3. Check user preference from claims
        var userLanguageClaim = context.User?.FindFirst("preferred_language")?.Value;
        if (!string.IsNullOrEmpty(userLanguageClaim))
        {
            if (TryParseLanguage(userLanguageClaim, out var userLanguage))
            {
                return userLanguage;
            }
        }

        // 4. Check Accept-Language header
        if (context.Request.Headers.TryGetValue("Accept-Language", out var acceptLanguage))
        {
            var detectedLanguage = localizationService.DetectLanguageFromCulture(acceptLanguage.ToString());
            if (detectedLanguage != Language.English) // Only use if not default
            {
                return detectedLanguage;
            }
        }

        // 5. Default to English
        return Language.English;
    }

    private static bool TryParseLanguage(string languageString, out Language language)
    {
        language = Language.English;

        if (string.IsNullOrWhiteSpace(languageString))
        {
            return false;
        }

        return languageString.ToLowerInvariant() switch
        {
            "en" or "english" or "en-us" or "en-gb" => SetLanguage(Language.English, out language),
            "hi" or "hindi" or "hi-in" => SetLanguage(Language.Hindi, out language),
            "kn" or "kannada" or "kn-in" => SetLanguage(Language.Kannada, out language),
            _ => false
        };
    }

    private static bool SetLanguage(Language lang, out Language language)
    {
        language = lang;
        return true;
    }
}

/// <summary>
/// Extension methods for localization middleware
/// </summary>
public static class LocalizationMiddlewareExtensions
{
    /// <summary>
    /// Add localization middleware to the pipeline
    /// </summary>
    public static IApplicationBuilder UseLocalization(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<LocalizationMiddleware>();
    }
}

/// <summary>
/// Helper service for accessing current language context
/// </summary>
public interface ILanguageContext
{
    /// <summary>
    /// Get current language from context
    /// </summary>
    Language GetCurrentLanguage();

    /// <summary>
    /// Get current culture from context
    /// </summary>
    CultureInfo GetCurrentCulture();

    /// <summary>
    /// Set language for current context
    /// </summary>
    void SetCurrentLanguage(Language language);
}

/// <summary>
/// Implementation of language context service
/// </summary>
public class LanguageContext : ILanguageContext
{
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly ILocalizationService _localizationService;

    public LanguageContext(IHttpContextAccessor httpContextAccessor, ILocalizationService localizationService)
    {
        _httpContextAccessor = httpContextAccessor;
        _localizationService = localizationService;
    }

    public Language GetCurrentLanguage()
    {
        var context = _httpContextAccessor.HttpContext;
        
        if (context?.Items.TryGetValue("CurrentLanguage", out var language) == true && language is Language lang)
        {
            return lang;
        }

        return Language.English; // Default fallback
    }

    public CultureInfo GetCurrentCulture()
    {
        var context = _httpContextAccessor.HttpContext;
        
        if (context?.Items.TryGetValue("CurrentCulture", out var culture) == true && culture is CultureInfo cultureInfo)
        {
            return cultureInfo;
        }

        return new CultureInfo("en-US"); // Default fallback
    }

    public void SetCurrentLanguage(Language language)
    {
        var context = _httpContextAccessor.HttpContext;
        
        if (context != null)
        {
            var culture = _localizationService.GetCultureInfo(language);
            
            context.Items["CurrentLanguage"] = language;
            context.Items["CurrentCulture"] = culture;
            
            CultureInfo.CurrentCulture = culture;
            CultureInfo.CurrentUICulture = culture;
        }
    }
}

/// <summary>
/// Localized notification service that uses current language context
/// </summary>
public class LocalizedNotificationService
{
    private readonly INotificationService _notificationService;
    private readonly ITemplateService _templateService;
    private readonly ILanguageContext _languageContext;
    private readonly ILogger<LocalizedNotificationService> _logger;

    public LocalizedNotificationService(
        INotificationService notificationService,
        ITemplateService templateService,
        ILanguageContext languageContext,
        ILogger<LocalizedNotificationService> logger)
    {
        _notificationService = notificationService;
        _templateService = templateService;
        _languageContext = languageContext;
        _logger = logger;
    }

    /// <summary>
    /// Send localized notification using current language context
    /// </summary>
    public async Task<NotificationSendResult> SendLocalizedNotificationAsync(
        Guid userId,
        string templateKey,
        Dictionary<string, object> parameters,
        MessageType messageType = MessageType.Notification,
        Priority priority = Priority.Normal,
        NotificationChannel? preferredChannel = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var currentLanguage = _languageContext.GetCurrentLanguage();
            
            var content = await _templateService.RenderNotificationTemplateAsync(
                templateKey,
                currentLanguage,
                parameters,
                cancellationToken);

            var request = new NotificationRequest
            {
                UserId = userId,
                MessageType = messageType,
                Content = content,
                Priority = priority,
                PreferredChannel = preferredChannel,
                Metadata = new Dictionary<string, string>
                {
                    ["templateKey"] = templateKey,
                    ["language"] = currentLanguage.ToString(),
                    ["culture"] = _languageContext.GetCurrentCulture().Name
                }
            };

            return await _notificationService.SendNotificationAsync(request, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send localized notification for template '{TemplateKey}' to user {UserId}", templateKey, userId);
            throw;
        }
    }

    /// <summary>
    /// Send localized notification to multiple users with their preferred languages
    /// </summary>
    public async Task<List<NotificationSendResult>> SendLocalizedBulkNotificationAsync(
        Dictionary<Guid, Language> userLanguages,
        string templateKey,
        Dictionary<string, object> parameters,
        MessageType messageType = MessageType.Notification,
        Priority priority = Priority.Normal,
        CancellationToken cancellationToken = default)
    {
        var results = new List<NotificationSendResult>();

        foreach (var userLanguage in userLanguages)
        {
            try
            {
                var content = await _templateService.RenderNotificationTemplateAsync(
                    templateKey,
                    userLanguage.Value,
                    parameters,
                    cancellationToken);

                var request = new NotificationRequest
                {
                    UserId = userLanguage.Key,
                    MessageType = messageType,
                    Content = content,
                    Priority = priority,
                    Metadata = new Dictionary<string, string>
                    {
                        ["templateKey"] = templateKey,
                        ["language"] = userLanguage.Value.ToString()
                    }
                };

                var result = await _notificationService.SendNotificationAsync(request, cancellationToken);
                results.Add(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send localized notification to user {UserId} in language {Language}", 
                    userLanguage.Key, userLanguage.Value);
                
                results.Add(NotificationSendResult.Failure($"Failed to send to user {userLanguage.Key}: {ex.Message}"));
            }
        }

        return results;
    }
}
