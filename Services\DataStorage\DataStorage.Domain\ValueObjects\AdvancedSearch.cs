using DataStorage.Domain.Enums;

namespace DataStorage.Domain.ValueObjects;

public class SearchableDocument
{
    public Guid Id { get; private set; }
    public string Title { get; private set; }
    public string Content { get; private set; }
    public string? Summary { get; private set; }
    public DocumentType DocumentType { get; private set; }
    public DocumentCategory Category { get; private set; }
    public string ContentType { get; private set; }
    public long SizeBytes { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime ModifiedAt { get; private set; }
    public Guid OwnerId { get; private set; }
    public string OwnerType { get; private set; }
    public AccessLevel AccessLevel { get; private set; }
    public List<string> Tags { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }
    public GeoLocation? Location { get; private set; }

    public SearchableDocument(
        Guid id,
        string title,
        string content,
        DocumentType documentType,
        DocumentCategory category,
        string contentType,
        long sizeBytes,
        DateTime createdAt,
        DateTime modifiedAt,
        Guid ownerId,
        string ownerType,
        AccessLevel accessLevel,
        string? summary = null,
        List<string>? tags = null,
        Dictionary<string, object>? metadata = null,
        GeoLocation? location = null)
    {
        Id = id;
        Title = title ?? throw new ArgumentNullException(nameof(title));
        Content = content ?? throw new ArgumentNullException(nameof(content));
        Summary = summary;
        DocumentType = documentType;
        Category = category;
        ContentType = contentType ?? throw new ArgumentNullException(nameof(contentType));
        SizeBytes = sizeBytes;
        CreatedAt = createdAt;
        ModifiedAt = modifiedAt;
        OwnerId = ownerId;
        OwnerType = ownerType ?? throw new ArgumentNullException(nameof(ownerType));
        AccessLevel = accessLevel;
        Tags = tags ?? new List<string>();
        Metadata = metadata ?? new Dictionary<string, object>();
        Location = location;
    }
}

public class SearchQuery
{
    public string Query { get; private set; }
    public List<SearchField> Fields { get; private set; }
    public List<SearchFilter> Filters { get; private set; }
    public SearchSort Sort { get; private set; }
    public int PageNumber { get; private set; }
    public int PageSize { get; private set; }
    public bool IncludeHighlights { get; private set; }
    public bool IncludeFacets { get; private set; }
    public Guid? RequestingUserId { get; private set; }

    public SearchQuery(
        string query,
        List<SearchField>? fields = null,
        List<SearchFilter>? filters = null,
        SearchSort? sort = null,
        int pageNumber = 1,
        int pageSize = 20,
        bool includeHighlights = false,
        bool includeFacets = false,
        Guid? requestingUserId = null)
    {
        Query = query ?? throw new ArgumentNullException(nameof(query));
        Fields = fields ?? new List<SearchField> { SearchField.All };
        Filters = filters ?? new List<SearchFilter>();
        Sort = sort ?? SearchSort.Relevance;
        PageNumber = Math.Max(1, pageNumber);
        PageSize = Math.Max(1, Math.Min(100, pageSize));
        IncludeHighlights = includeHighlights;
        IncludeFacets = includeFacets;
        RequestingUserId = requestingUserId;
    }
}

public class SearchResult
{
    public List<SearchHit> Hits { get; private set; }
    public long TotalHits { get; private set; }
    public double MaxScore { get; private set; }
    public TimeSpan SearchTime { get; private set; }
    public int PageNumber { get; private set; }
    public int PageSize { get; private set; }
    public int TotalPages { get; private set; }
    public List<SearchFacet> Facets { get; private set; }
    public SearchAggregations? Aggregations { get; private set; }

    public SearchResult(
        List<SearchHit> hits,
        long totalHits,
        double maxScore,
        TimeSpan searchTime,
        int pageNumber,
        int pageSize,
        List<SearchFacet>? facets = null,
        SearchAggregations? aggregations = null)
    {
        Hits = hits ?? new List<SearchHit>();
        TotalHits = totalHits;
        MaxScore = maxScore;
        SearchTime = searchTime;
        PageNumber = pageNumber;
        PageSize = pageSize;
        TotalPages = (int)Math.Ceiling((double)totalHits / pageSize);
        Facets = facets ?? new List<SearchFacet>();
        Aggregations = aggregations;
    }
}

public class SearchHit
{
    public Guid DocumentId { get; private set; }
    public double Score { get; private set; }
    public string Title { get; private set; }
    public string? Summary { get; private set; }
    public DocumentType DocumentType { get; private set; }
    public DocumentCategory Category { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime ModifiedAt { get; private set; }
    public List<string> Tags { get; private set; }
    public Dictionary<string, List<string>> Highlights { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    public SearchHit(
        Guid documentId,
        double score,
        string title,
        DocumentType documentType,
        DocumentCategory category,
        DateTime createdAt,
        DateTime modifiedAt,
        string? summary = null,
        List<string>? tags = null,
        Dictionary<string, List<string>>? highlights = null,
        Dictionary<string, object>? metadata = null)
    {
        DocumentId = documentId;
        Score = score;
        Title = title ?? throw new ArgumentNullException(nameof(title));
        Summary = summary;
        DocumentType = documentType;
        Category = category;
        CreatedAt = createdAt;
        ModifiedAt = modifiedAt;
        Tags = tags ?? new List<string>();
        Highlights = highlights ?? new Dictionary<string, List<string>>();
        Metadata = metadata ?? new Dictionary<string, object>();
    }
}

public class IndexingResult
{
    public bool IsSuccessful { get; private set; }
    public string? ErrorMessage { get; private set; }
    public int DocumentsIndexed { get; private set; }
    public int DocumentsFailed { get; private set; }
    public TimeSpan IndexingTime { get; private set; }
    public List<IndexingError> Errors { get; private set; }

    private IndexingResult()
    {
        Errors = new List<IndexingError>();
    }

    public static IndexingResult Success(int documentsIndexed, TimeSpan indexingTime)
    {
        return new IndexingResult
        {
            IsSuccessful = true,
            DocumentsIndexed = documentsIndexed,
            DocumentsFailed = 0,
            IndexingTime = indexingTime,
            Errors = new List<IndexingError>()
        };
    }

    public static IndexingResult Failure(string errorMessage, int documentsFailed = 1, List<IndexingError>? errors = null)
    {
        return new IndexingResult
        {
            IsSuccessful = false,
            ErrorMessage = errorMessage,
            DocumentsIndexed = 0,
            DocumentsFailed = documentsFailed,
            IndexingTime = TimeSpan.Zero,
            Errors = errors ?? new List<IndexingError>()
        };
    }

    public static IndexingResult Partial(int documentsIndexed, int documentsFailed, TimeSpan indexingTime, List<IndexingError> errors)
    {
        return new IndexingResult
        {
            IsSuccessful = documentsIndexed > 0,
            DocumentsIndexed = documentsIndexed,
            DocumentsFailed = documentsFailed,
            IndexingTime = indexingTime,
            Errors = errors ?? new List<IndexingError>()
        };
    }
}

public class IndexingError
{
    public Guid DocumentId { get; private set; }
    public string ErrorMessage { get; private set; }
    public string? StackTrace { get; private set; }
    public DateTime Timestamp { get; private set; }

    public IndexingError(Guid documentId, string errorMessage, string? stackTrace = null)
    {
        DocumentId = documentId;
        ErrorMessage = errorMessage ?? throw new ArgumentNullException(nameof(errorMessage));
        StackTrace = stackTrace;
        Timestamp = DateTime.UtcNow;
    }
}

public class IndexStatistics
{
    public long TotalDocuments { get; private set; }
    public long IndexSizeBytes { get; private set; }
    public DateTime LastIndexed { get; private set; }
    public Dictionary<DocumentType, long> DocumentsByType { get; private set; }
    public Dictionary<DocumentCategory, long> DocumentsByCategory { get; private set; }
    public Dictionary<string, long> DocumentsByContentType { get; private set; }
    public IndexPerformanceMetrics Performance { get; private set; }

    public IndexStatistics(
        long totalDocuments,
        long indexSizeBytes,
        DateTime lastIndexed,
        Dictionary<DocumentType, long> documentsByType,
        Dictionary<DocumentCategory, long> documentsByCategory,
        Dictionary<string, long> documentsByContentType,
        IndexPerformanceMetrics performance)
    {
        TotalDocuments = totalDocuments;
        IndexSizeBytes = indexSizeBytes;
        LastIndexed = lastIndexed;
        DocumentsByType = documentsByType ?? new Dictionary<DocumentType, long>();
        DocumentsByCategory = documentsByCategory ?? new Dictionary<DocumentCategory, long>();
        DocumentsByContentType = documentsByContentType ?? new Dictionary<string, long>();
        Performance = performance ?? new IndexPerformanceMetrics();
    }
}

public class IndexPerformanceMetrics
{
    public double AverageIndexingTime { get; private set; }
    public double AverageSearchTime { get; private set; }
    public long TotalSearches { get; private set; }
    public double CacheHitRatio { get; private set; }

    public IndexPerformanceMetrics(
        double averageIndexingTime = 0,
        double averageSearchTime = 0,
        long totalSearches = 0,
        double cacheHitRatio = 0)
    {
        AverageIndexingTime = averageIndexingTime;
        AverageSearchTime = averageSearchTime;
        TotalSearches = totalSearches;
        CacheHitRatio = cacheHitRatio;
    }
}

public class GeoLocation
{
    public double Latitude { get; private set; }
    public double Longitude { get; private set; }
    public string? Address { get; private set; }
    public string? City { get; private set; }
    public string? Country { get; private set; }

    public GeoLocation(double latitude, double longitude, string? address = null, string? city = null, string? country = null)
    {
        Latitude = latitude;
        Longitude = longitude;
        Address = address;
        City = city;
        Country = country;
    }
}
