using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Infrastructure.Repositories;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace CommunicationNotification.Infrastructure.Controllers;

/// <summary>
/// Compliance and audit management API controller
/// </summary>
[ApiController]
[Route("api/compliance")]
[Authorize(Roles = "Admin,ComplianceOfficer,Auditor")]
public class ComplianceController : ControllerBase
{
    private readonly ICommunicationAuditService _auditService;
    private readonly IComplianceRepository _complianceRepository;
    private readonly IAuditRepository _auditRepository;

    public ComplianceController(
        ICommunicationAuditService auditService,
        IComplianceRepository complianceRepository,
        IAuditRepository auditRepository)
    {
        _auditService = auditService;
        _complianceRepository = complianceRepository;
        _auditRepository = auditRepository;
    }

    /// <summary>
    /// Get audit trail for investigation
    /// </summary>
    [HttpPost("audit-trail")]
    [Authorize(Roles = "Admin,ComplianceOfficer,Auditor")]
    public async Task<IActionResult> GetAuditTrail(
        [FromBody] AuditTrailQueryRequest request,
        CancellationToken cancellationToken = default)
    {
        var query = new AuditTrailQuery
        {
            UserId = request.UserId,
            MessageId = request.MessageId,
            ConversationId = request.ConversationId,
            EventType = request.EventType,
            Channel = request.Channel,
            FromDate = request.FromDate,
            ToDate = request.ToDate,
            MinSeverity = request.MinSeverity,
            ComplianceFlags = request.ComplianceFlags,
            IncludeSensitiveData = request.IncludeSensitiveData && User.IsInRole("Admin"),
            RequestedBy = GetCurrentUserId(),
            PageSize = Math.Min(request.PageSize, 1000), // Limit page size
            PageNumber = request.PageNumber,
            SearchTerm = request.SearchTerm
        };

        var auditTrail = await _auditService.GetAuditTrailAsync(query, cancellationToken);

        return Ok(new
        {
            Query = new
            {
                request.UserId,
                request.MessageId,
                request.FromDate,
                request.ToDate,
                request.PageSize,
                request.PageNumber
            },
            Results = auditTrail.Select(record => new
            {
                record.Id,
                record.EventType,
                record.UserId,
                record.MessageId,
                record.ConversationId,
                record.Channel,
                record.Timestamp,
                record.Severity,
                record.ComplianceFlags,
                EventData = query.IncludeSensitiveData ? record.EventData : "[REDACTED]",
                record.IpAddress,
                record.SessionId,
                record.CorrelationId
            }),
            TotalCount = auditTrail.Count,
            HasSensitiveData = query.IncludeSensitiveData
        });
    }

    /// <summary>
    /// Generate compliance report
    /// </summary>
    [HttpPost("reports/generate")]
    [Authorize(Roles = "Admin,ComplianceOfficer")]
    public async Task<IActionResult> GenerateComplianceReport(
        [FromBody] ComplianceReportGenerationRequest request,
        CancellationToken cancellationToken = default)
    {
        var reportRequest = new ComplianceReportRequest
        {
            ReportType = request.ReportType,
            Period = request.Period,
            RequestedBy = GetCurrentUserId(),
            IncludeFlags = request.IncludeFlags ?? new(),
            IncludeSensitiveData = request.IncludeSensitiveData && User.IsInRole("Admin"),
            CustomFilters = request.CustomFilters
        };

        var report = await _auditService.GenerateComplianceReportAsync(reportRequest, cancellationToken);

        return Ok(new
        {
            report.Id,
            report.ReportType,
            report.Period,
            report.GeneratedAt,
            report.Status,
            SectionCount = report.Sections.Count,
            Summary = report.Summary,
            Sections = report.Sections.Select(s => new
            {
                s.Title,
                s.Content,
                s.Metrics,
                s.Status,
                RecommendationCount = s.Recommendations.Count
            })
        });
    }

    /// <summary>
    /// Get compliance reports
    /// </summary>
    [HttpGet("reports")]
    public async Task<IActionResult> GetComplianceReports(
        [FromQuery] ComplianceReportType? reportType = null,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] ComplianceReportStatus? status = null,
        [FromQuery] int pageSize = 50,
        [FromQuery] int pageNumber = 1,
        CancellationToken cancellationToken = default)
    {
        var query = new ComplianceReportQuery
        {
            ReportType = reportType,
            FromDate = fromDate,
            ToDate = toDate,
            Status = status,
            PageSize = Math.Min(pageSize, 100),
            PageNumber = pageNumber
        };

        var reports = await _complianceRepository.GetReportsAsync(query, cancellationToken);

        return Ok(new
        {
            Reports = reports.Select(r => new
            {
                r.Id,
                r.ReportType,
                r.Period,
                r.GeneratedAt,
                r.CompletedAt,
                r.GeneratedBy,
                r.Status,
                SectionCount = r.Sections.Count
            }),
            Query = new { reportType, fromDate, toDate, status, pageSize, pageNumber },
            TotalCount = reports.Count
        });
    }

    /// <summary>
    /// Get specific compliance report
    /// </summary>
    [HttpGet("reports/{reportId}")]
    public async Task<IActionResult> GetComplianceReport(
        Guid reportId,
        CancellationToken cancellationToken = default)
    {
        var report = await _complianceRepository.GetReportByIdAsync(reportId, cancellationToken);

        if (report == null)
        {
            return NotFound(new { Message = $"Compliance report {reportId} not found" });
        }

        return Ok(report);
    }

    /// <summary>
    /// Archive a message for compliance
    /// </summary>
    [HttpPost("archive-message")]
    [Authorize(Roles = "Admin,ComplianceOfficer")]
    public async Task<IActionResult> ArchiveMessage(
        [FromBody] MessageArchiveRequest request,
        CancellationToken cancellationToken = default)
    {
        var archive = await _auditService.ArchiveMessageAsync(
            request.MessageId,
            request.Reason,
            request.RetentionPeriod,
            cancellationToken);

        return Ok(new
        {
            archive.Id,
            archive.MessageId,
            archive.ArchiveReason,
            archive.ArchivedAt,
            archive.RetentionUntil,
            archive.ComplianceFlags,
            archive.IsEncrypted
        });
    }

    /// <summary>
    /// Get dispute resolution data
    /// </summary>
    [HttpPost("dispute-resolution")]
    [Authorize(Roles = "Admin,ComplianceOfficer,LegalTeam")]
    public async Task<IActionResult> GetDisputeResolutionData(
        [FromBody] DisputeResolutionRequest request,
        CancellationToken cancellationToken = default)
    {
        var context = new DisputeContext
        {
            DisputeId = request.DisputeId,
            DisputeType = request.DisputeType,
            Description = request.Description,
            RequestedBy = GetCurrentUserId(),
            RequestedAt = DateTime.UtcNow,
            Priority = request.Priority,
            Metadata = request.Metadata ?? new()
        };

        var disputeData = await _auditService.GetDisputeResolutionDataAsync(
            request.MessageId,
            context,
            cancellationToken);

        return Ok(new
        {
            disputeData.MessageId,
            DisputeContext = disputeData.DisputeContext,
            AuditTrailCount = disputeData.AuditTrail.Count,
            HasMessageArchive = disputeData.MessageArchive != null,
            DeliveryConfirmationCount = disputeData.DeliveryConfirmations.Count,
            disputeData.ComplianceFlags,
            disputeData.LegalHoldStatus,
            disputeData.GeneratedAt,
            AuditTrail = disputeData.AuditTrail.Select(a => new
            {
                a.Id,
                a.EventType,
                a.Timestamp,
                a.Severity,
                a.ComplianceFlags
            }),
            MessageArchive = disputeData.MessageArchive != null ? new
            {
                disputeData.MessageArchive.Id,
                disputeData.MessageArchive.ArchiveReason,
                disputeData.MessageArchive.ArchivedAt,
                disputeData.MessageArchive.RetentionUntil,
                disputeData.MessageArchive.ComplianceFlags
            } : null
        });
    }

    /// <summary>
    /// Get data retention policies
    /// </summary>
    [HttpGet("retention-policies")]
    public async Task<IActionResult> GetRetentionPolicies(CancellationToken cancellationToken = default)
    {
        var policies = await _complianceRepository.GetActiveRetentionPoliciesAsync(cancellationToken);

        return Ok(new
        {
            Policies = policies.Select(p => new
            {
                p.Id,
                p.Name,
                p.Description,
                p.Action,
                RetentionPeriodDays = p.RetentionPeriod.TotalDays,
                p.ApplicableEventTypes,
                p.ApplicableFlags,
                p.IsActive,
                p.CreatedAt,
                p.LastExecuted
            }),
            TotalCount = policies.Count
        });
    }

    /// <summary>
    /// Apply data retention policy manually
    /// </summary>
    [HttpPost("retention-policies/{policyId}/apply")]
    [Authorize(Roles = "Admin,ComplianceOfficer")]
    public async Task<IActionResult> ApplyRetentionPolicy(
        Guid policyId,
        CancellationToken cancellationToken = default)
    {
        var policy = await _complianceRepository.GetRetentionPolicyByIdAsync(policyId, cancellationToken);

        if (policy == null)
        {
            return NotFound(new { Message = $"Retention policy {policyId} not found" });
        }

        var result = await _auditService.ApplyDataRetentionPolicyAsync(policy, cancellationToken);

        return Ok(new
        {
            PolicyId = policyId,
            PolicyName = policy.Name,
            result.ExecutedAt,
            result.ProcessedRecords,
            result.DeletedRecords,
            result.ArchivedRecords,
            ErrorCount = result.Errors.Count,
            Errors = result.Errors
        });
    }

    /// <summary>
    /// Get compliance dashboard
    /// </summary>
    [HttpGet("dashboard")]
    public async Task<IActionResult> GetComplianceDashboard(CancellationToken cancellationToken = default)
    {
        var dashboard = await _complianceRepository.GetComplianceDashboardAsync(cancellationToken);

        return Ok(dashboard);
    }

    /// <summary>
    /// Get audit statistics
    /// </summary>
    [HttpGet("statistics")]
    public async Task<IActionResult> GetAuditStatistics(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        CancellationToken cancellationToken = default)
    {
        var from = fromDate ?? DateTime.UtcNow.AddDays(-30);
        var to = toDate ?? DateTime.UtcNow;

        var eventTypeStats = await _auditRepository.GetEventTypeStatisticsAsync(from, to, cancellationToken);
        var channelStats = await _auditRepository.GetChannelStatisticsAsync(from, to, cancellationToken);
        var totalRecords = await _auditRepository.GetTotalRecordCountAsync(cancellationToken);
        var storageSize = await _auditRepository.GetStorageSizeEstimateAsync(cancellationToken);

        return Ok(new
        {
            Period = new { From = from, To = to },
            TotalRecords = totalRecords,
            StorageSizeBytes = storageSize,
            EventTypeStatistics = eventTypeStats,
            ChannelStatistics = channelStats
        });
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (Guid.TryParse(userIdClaim, out var userId))
        {
            return userId;
        }
        throw new UnauthorizedAccessException("User ID not found in claims");
    }
}

// Request models
public class AuditTrailQueryRequest
{
    public Guid? UserId { get; set; }
    public Guid? MessageId { get; set; }
    public Guid? ConversationId { get; set; }
    public AuditEventType? EventType { get; set; }
    public NotificationChannel? Channel { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public AuditSeverity? MinSeverity { get; set; }
    public ComplianceFlag? ComplianceFlags { get; set; }
    public bool IncludeSensitiveData { get; set; }
    public int PageSize { get; set; } = 100;
    public int PageNumber { get; set; } = 1;
    public string? SearchTerm { get; set; }
}

public class ComplianceReportGenerationRequest
{
    public ComplianceReportType ReportType { get; set; }
    public DateTimeOffset Period { get; set; }
    public List<ComplianceFlag>? IncludeFlags { get; set; }
    public bool IncludeSensitiveData { get; set; }
    public string? CustomFilters { get; set; }
}

public class MessageArchiveRequest
{
    public Guid MessageId { get; set; }
    public ArchiveReason Reason { get; set; }
    public TimeSpan RetentionPeriod { get; set; }
}

public class DisputeResolutionRequest
{
    public Guid MessageId { get; set; }
    public Guid DisputeId { get; set; }
    public DisputeType DisputeType { get; set; }
    public string Description { get; set; } = string.Empty;
    public DisputePriority Priority { get; set; } = DisputePriority.Medium;
    public Dictionary<string, string>? Metadata { get; set; }
}
