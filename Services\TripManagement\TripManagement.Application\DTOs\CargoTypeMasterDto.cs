using Shared.Domain.Common;

namespace TripManagement.Application.DTOs;

/// <summary>
/// DTO for CargoTypeMaster entity
/// </summary>
public class CargoTypeMasterDto
{
    public Guid Id { get; set; }
    public string Code { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public bool RequiresSpecialHandling { get; set; }
    public bool IsHazardous { get; set; }
    public bool RequiresTemperatureControl { get; set; }
    public decimal? MinTemperatureCelsius { get; set; }
    public decimal? MaxTemperatureCelsius { get; set; }
    public string? HandlingInstructions { get; set; }
    public string? SafetyRequirements { get; set; }
    public bool IsActive { get; set; }
    public int SortOrder { get; set; }
    public string? IconUrl { get; set; }
    public Dictionary<string, object> AdditionalProperties { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

/// <summary>
/// Summary DTO for CargoTypeMaster entity (for lists and dropdowns)
/// </summary>
public class CargoTypeMasterSummaryDto
{
    public Guid Id { get; set; }
    public string Code { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public bool RequiresSpecialHandling { get; set; }
    public bool IsHazardous { get; set; }
    public bool RequiresTemperatureControl { get; set; }
    public bool IsActive { get; set; }
    public int SortOrder { get; set; }
    public string? IconUrl { get; set; }
    public string TemperatureRange { get; set; } = string.Empty;
    public string SpecialRequirements { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for creating CargoTypeMaster
/// </summary>
public class CreateCargoTypeMasterDto
{
    public string Code { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public bool RequiresSpecialHandling { get; set; }
    public bool IsHazardous { get; set; }
    public bool RequiresTemperatureControl { get; set; }
    public decimal? MinTemperatureCelsius { get; set; }
    public decimal? MaxTemperatureCelsius { get; set; }
    public string? HandlingInstructions { get; set; }
    public string? SafetyRequirements { get; set; }
    public int SortOrder { get; set; }
    public string? IconUrl { get; set; }
    public Dictionary<string, object>? AdditionalProperties { get; set; }
}

/// <summary>
/// Request DTO for updating CargoTypeMaster
/// </summary>
public class UpdateCargoTypeMasterDto
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public bool RequiresSpecialHandling { get; set; }
    public bool IsHazardous { get; set; }
    public bool RequiresTemperatureControl { get; set; }
    public decimal? MinTemperatureCelsius { get; set; }
    public decimal? MaxTemperatureCelsius { get; set; }
    public string? HandlingInstructions { get; set; }
    public string? SafetyRequirements { get; set; }
    public int SortOrder { get; set; }
    public string? IconUrl { get; set; }
    public Dictionary<string, object>? AdditionalProperties { get; set; }
}

/// <summary>
/// Response DTO for paginated cargo type master results
/// </summary>
public class CargoTypeMasterPagedResultDto
{
    public IEnumerable<CargoTypeMasterSummaryDto> Items { get; set; } = new List<CargoTypeMasterSummaryDto>();
    public int TotalCount { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
    public bool HasPreviousPage => PageNumber > 1;
    public bool HasNextPage => PageNumber < TotalPages;
}

/// <summary>
/// DTO for cargo type master statistics
/// </summary>
public class CargoTypeMasterStatsDto
{
    public int TotalCount { get; set; }
    public int ActiveCount { get; set; }
    public int InactiveCount { get; set; }
    public Dictionary<string, int> CategoryCounts { get; set; } = new();
    public Dictionary<string, int> UsageStatistics { get; set; } = new();
    public Dictionary<string, int> SpecialHandlingStatistics { get; set; } = new();
}

