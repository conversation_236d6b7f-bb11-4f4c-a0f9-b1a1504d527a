{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information",
      "Microsoft.EntityFrameworkCore": "Warning",
      "System.Net.Http.HttpClient": "Warning"
    },
    "Console": {
      "IncludeScopes": true,
      "TimestampFormat": "yyyy-MM-dd HH:mm:ss "
    }
  },
  "AllowedHosts": "*",
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Port=5432;Database=TLI_Default;Username=********;Password=********;",
    "Redis": "localhost:6379",
    "RabbitMQ": "amqp://rabbitmq:rabbitmq123@localhost:5672/"
  },
  "Authentication": {
    "Jwt": {
      "Issuer": "TLI-Microservices",
      "Audience": "TLI-API",
      "SecretKey": "TLI-Super-Secret-Key-For-JWT-Token-Generation-2024",
      "ExpiryInMinutes": 60,
      "RefreshTokenExpiryInDays": 7
    },
    "Google": {
      "ClientId": "",
      "ClientSecret": ""
    },
    "Microsoft": {
      "ClientId": "",
      "ClientSecret": ""
    }
  },
  "Cors": {
    "AllowedOrigins": [
      "http://localhost:3000",
      "http://localhost:3001",
      "http://localhost:4200",
      "https://localhost:3000",
      "https://localhost:3001",
      "https://localhost:4200"
    ],
    "AllowedMethods": ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
    "AllowedHeaders": ["*"],
    "AllowCredentials": true
  },
  "RateLimiting": {
    "EnableRateLimiting": true,
    "GeneralRules": {
      "PermitLimit": 100,
      "Window": "00:01:00",
      "ReplenishmentPeriod": "00:00:10",
      "TokensPerPeriod": 10,
      "QueueLimit": 10
    },
    "AuthenticationRules": {
      "PermitLimit": 5,
      "Window": "00:01:00",
      "ReplenishmentPeriod": "00:00:10",
      "TokensPerPeriod": 1,
      "QueueLimit": 2
    }
  },
  "Caching": {
    "Redis": {
      "Enabled": true,
      "DefaultExpirationMinutes": 30,
      "ConnectionString": "localhost:6379",
      "Database": 0,
      "KeyPrefix": "TLI:"
    },
    "Memory": {
      "Enabled": true,
      "DefaultExpirationMinutes": 15,
      "SizeLimit": 100
    }
  },
  "MessageBroker": {
    "RabbitMQ": {
      "Enabled": true,
      "ConnectionString": "amqp://rabbitmq:rabbitmq123@localhost:5672/",
      "ExchangeName": "tli.events",
      "QueuePrefix": "tli.",
      "RetryCount": 3,
      "RetryDelay": "00:00:05"
    }
  },
  "HealthChecks": {
    "Enabled": true,
    "DetailedErrors": true,
    "Timeout": "00:00:30",
    "Endpoints": {
      "Health": "/health",
      "Ready": "/health/ready",
      "Live": "/health/live"
    }
  },
  "Monitoring": {
    "ApplicationInsights": {
      "Enabled": false,
      "InstrumentationKey": ""
    },
    "Prometheus": {
      "Enabled": true,
      "Endpoint": "/metrics",
      "Port": 9090
    },
    "Jaeger": {
      "Enabled": true,
      "AgentHost": "localhost",
      "AgentPort": 6831,
      "ServiceName": "TLI-Service"
    },
    "Seq": {
      "Enabled": true,
      "ServerUrl": "http://localhost:5341",
      "ApiKey": ""
    }
  },
  "Security": {
    "EnableHttpsRedirection": true,
    "EnableHsts": true,
    "HstsMaxAge": "365.00:00:00",
    "ContentSecurityPolicy": "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';",
    "ApiKey": {
      "Enabled": false,
      "HeaderName": "X-API-Key",
      "ValidKeys": []
    }
  },
  "Swagger": {
    "Enabled": true,
    "Title": "TLI Microservices API",
    "Version": "v1",
    "Description": "Transport and Logistics India - Microservices API Documentation",
    "ContactName": "TLI Development Team",
    "ContactEmail": "<EMAIL>",
    "IncludeXmlComments": true,
    "EnableAnnotations": true
  },
  "FileStorage": {
    "Provider": "Local", // Local, Azure, AWS, MinIO
    "Local": {
      "BasePath": "./uploads",
      "MaxFileSize": ********,
      "AllowedExtensions": [".jpg", ".jpeg", ".png", ".pdf", ".doc", ".docx", ".xls", ".xlsx"]
    },
    "Azure": {
      "ConnectionString": "",
      "ContainerName": "tli-documents"
    },
    "AWS": {
      "AccessKey": "",
      "SecretKey": "",
      "BucketName": "tli-documents",
      "Region": "us-east-1"
    },
    "MinIO": {
      "Endpoint": "localhost:9000",
      "AccessKey": "minioadmin",
      "SecretKey": "minioadmin123",
      "BucketName": "tli-documents",
      "UseSSL": false
    }
  },
  "Email": {
    "Provider": "SMTP", // SMTP, SendGrid, AWS SES
    "SMTP": {
      "Host": "smtp.gmail.com",
      "Port": 587,
      "EnableSsl": true,
      "Username": "",
      "Password": "",
      "FromEmail": "<EMAIL>",
      "FromName": "TLI Platform"
    },
    "SendGrid": {
      "ApiKey": "",
      "FromEmail": "<EMAIL>",
      "FromName": "TLI Platform"
    }
  },
  "SMS": {
    "Provider": "Twilio", // Twilio, AWS SNS
    "Twilio": {
      "AccountSid": "",
      "AuthToken": "",
      "FromNumber": ""
    }
  },
  "Payment": {
    "Razorpay": {
      "KeyId": "",
      "KeySecret": "",
      "WebhookSecret": ""
    },
    "Stripe": {
      "PublishableKey": "",
      "SecretKey": "",
      "WebhookSecret": ""
    }
  },
  "Maps": {
    "GoogleMaps": {
      "ApiKey": ""
    },
    "MapBox": {
      "AccessToken": ""
    }
  },
  "Features": {
    "EnableUserRegistration": true,
    "EnableEmailVerification": true,
    "EnablePhoneVerification": true,
    "EnableTwoFactorAuth": false,
    "EnableSocialLogin": true,
    "EnableFileUpload": true,
    "EnableRealTimeNotifications": true,
    "EnableAnalytics": true,
    "EnableAuditLogging": true,
    "MaintenanceMode": false
  },
  "BusinessRules": {
    "MaxOrderValue": 1000000,
    "MinOrderValue": 100,
    "DefaultCurrency": "INR",
    "SupportedCurrencies": ["INR", "USD"],
    "MaxFileUploadSize": ********,
    "SessionTimeoutMinutes": 30,
    "PasswordPolicy": {
      "MinLength": 8,
      "RequireUppercase": true,
      "RequireLowercase": true,
      "RequireDigit": true,
      "RequireSpecialChar": true
    }
  },
  "Localization": {
    "DefaultCulture": "en-IN",
    "SupportedCultures": ["en-IN", "hi-IN", "en-US"],
    "ResourcePath": "Resources"
  }
}
