# Grafana datasources configuration for Analytics & BI Service

apiVersion: 1

datasources:
  # Prometheus datasource
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
    jsonData:
      httpMethod: POST
      manageAlerts: true
      prometheusType: Prometheus
      prometheusVersion: 2.40.0
      cacheLevel: 'High'
      disableMetricsLookup: false
      customQueryParameters: ''
      timeInterval: '30s'
    secureJsonData:
      httpHeaderValue1: 'Bearer ${PROMETHEUS_API_TOKEN}'
    version: 1

  # TimescaleDB datasource
  - name: TimescaleDB
    type: postgres
    access: proxy
    url: timescaledb:5432
    database: tli_analytics_prod
    user: timescale
    editable: true
    jsonData:
      sslmode: disable
      maxOpenConns: 100
      maxIdleConns: 100
      maxIdleConnsAuto: true
      connMaxLifetime: 14400
      postgresVersion: 1500
      timescaledb: true
    secureJsonData:
      password: '${POSTGRES_PASSWORD}'
    version: 1

  # Redis datasource (for real-time metrics)
  - name: Redis
    type: redis-datasource
    access: proxy
    url: redis://redis:6379
    editable: true
    jsonData:
      client: 'standalone'
      poolSize: 5
      timeout: 10
      pingInterval: 0
      pipelineWindow: 0
    secureJsonData:
      password: '${REDIS_PASSWORD}'
    version: 1

  # Loki for logs (if available)
  - name: Loki
    type: loki
    access: proxy
    url: http://loki:3100
    editable: true
    jsonData:
      maxLines: 1000
      derivedFields:
        - datasourceUid: 'prometheus'
          matcherRegex: 'trace_id=(\w+)'
          name: 'TraceID'
          url: '$${__value.raw}'
    version: 1

  # Jaeger for tracing (if available)
  - name: Jaeger
    type: jaeger
    access: proxy
    url: http://jaeger:16686
    editable: true
    jsonData:
      tracesToLogs:
        datasourceUid: 'loki'
        tags: ['job', 'instance', 'pod', 'namespace']
        mappedTags: [{ key: 'service.name', value: 'service' }]
        mapTagNamesEnabled: false
        spanStartTimeShift: '1h'
        spanEndTimeShift: '1h'
        filterByTraceID: false
        filterBySpanID: false
      tracesToMetrics:
        datasourceUid: 'prometheus'
        tags: [{ key: 'service.name', value: 'service' }, { key: 'job' }]
        queries:
          - name: 'Sample query'
            query: 'sum(rate(traces_spanmetrics_latency_bucket{$$__tags}[5m]))'
      serviceMap:
        datasourceUid: 'prometheus'
      nodeGraph:
        enabled: true
    version: 1

  # TestData datasource for testing
  - name: TestData
    type: testdata
    access: proxy
    editable: true
    jsonData: {}
    version: 1
