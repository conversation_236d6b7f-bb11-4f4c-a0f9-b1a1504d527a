using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using AnalyticsBIService.Application.DTOs;
using Shared.Infrastructure.Repositories;

namespace AnalyticsBIService.Application.Interfaces;

/// <summary>
/// Repository interface for order data operations
/// </summary>
public interface IOrderDataRepository
{
    /// <summary>
    /// Gets order data for export
    /// </summary>
    Task<PagedResult<OrderDataDto>> GetOrderDataAsync(
        DateTime startDate,
        DateTime endDate,
        int page = 1,
        int pageSize = 100,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets order analytics data
    /// </summary>
    Task<List<OrderAnalyticsDto>> GetOrderAnalyticsAsync(
        DateTime startDate,
        DateTime endDate,
        Guid? customerId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets order summary data
    /// </summary>
    Task<OrderSummaryDto> GetOrderSummaryAsync(
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default);

    // Additional methods required by OrderTripExportService
    Task<List<OrderDataDto>> GetOrdersForExportAsync(OrderDataFilter filter, CancellationToken cancellationToken = default);
    Task<List<OrderTimelineDto>> GetOrderTimelineForExportAsync(Guid orderId, CancellationToken cancellationToken = default);
    Task<List<PerformanceDataDto>> GetPerformanceDataAsync(PerformanceDataFilter filter, CancellationToken cancellationToken = default);
}

/// <summary>
/// Order data transfer object
/// </summary>
public class OrderDataDto
{
    public Guid OrderId { get; set; }
    public string OrderNumber { get; set; } = string.Empty;
    public Guid CustomerId { get; set; }
    public string CustomerName { get; set; } = string.Empty;
    public DateTime OrderDate { get; set; }
    public decimal OrderValue { get; set; }
    public string Status { get; set; } = string.Empty;
    public string Origin { get; set; } = string.Empty;
    public string Destination { get; set; } = string.Empty;
    public decimal Weight { get; set; }
    public string VehicleType { get; set; } = string.Empty;
}

/// <summary>
/// Order analytics data transfer object
/// </summary>
public class OrderAnalyticsDto
{
    public DateTime Date { get; set; }
    public int OrderCount { get; set; }
    public decimal TotalValue { get; set; }
    public decimal AverageValue { get; set; }
    public string Status { get; set; } = string.Empty;
}

/// <summary>
/// Order summary data transfer object
/// </summary>
public class OrderSummaryDto
{
    public int TotalOrders { get; set; }
    public decimal TotalValue { get; set; }
    public decimal AverageOrderValue { get; set; }
    public int CompletedOrders { get; set; }
    public int PendingOrders { get; set; }
    public int CancelledOrders { get; set; }
}

/// <summary>
/// Order data filter for export operations
/// </summary>
public class OrderDataFilter
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string? Status { get; set; }
    public Guid? TransportCompanyId { get; set; }
    public Guid? BrokerId { get; set; }
    public Guid? CarrierId { get; set; }
    public bool IncludeTimeline { get; set; }
    public bool IncludeDocuments { get; set; }
    public bool IncludeFinancials { get; set; }
}

/// <summary>
/// Order timeline data transfer object
/// </summary>
public class OrderTimelineDto
{
    public Guid Id { get; set; }
    public Guid OrderId { get; set; }
    public string EventType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime EventTimestamp { get; set; }
    public string EventBy { get; set; } = string.Empty;
    public Dictionary<string, object> EventData { get; set; } = new();
}

/// <summary>
/// Performance data transfer object
/// </summary>
public class PerformanceDataDto
{
    public Guid Id { get; set; }
    public string MetricType { get; set; } = string.Empty;
    public string MetricName { get; set; } = string.Empty;
    public decimal Value { get; set; }
    public DateTime MeasuredAt { get; set; }
    public Guid? EntityId { get; set; }
    public string EntityType { get; set; } = string.Empty;
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Performance data filter for export operations
/// </summary>
public class PerformanceDataFilter
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public Guid? TransportCompanyId { get; set; }
    public Guid? CarrierId { get; set; }
    public List<string> MetricTypes { get; set; } = new();
}
