using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.DTOs;
using SubscriptionManagement.Application.Interfaces;

namespace SubscriptionManagement.Application.Queries.GetPlansByUserType;

public class GetPlansByUserTypeQueryHandler : IRequestHandler<GetPlansByUserTypeQuery, List<PlanSummaryDto>>
{
    private readonly IPlanRepository _planRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetPlansByUserTypeQueryHandler> _logger;

    public GetPlansByUserTypeQueryHandler(
        IPlanRepository planRepository,
        IMapper mapper,
        ILogger<GetPlansByUserTypeQueryHandler> logger)
    {
        _planRepository = planRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<List<PlanSummaryDto>> Handle(GetPlansByUserTypeQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting plans for user type: {UserType}", request.UserType);

        var plans = await _planRepository.GetByUserTypeAsync(request.UserType);

        // Filter to only public and active plans
        var publicPlans = plans.Where(p => p.IsActive && p.IsPublic).ToList();

        var result = _mapper.Map<List<PlanSummaryDto>>(publicPlans);

        _logger.LogInformation("Found {Count} plans for user type {UserType}", result.Count, request.UserType);

        return result;
    }
}
