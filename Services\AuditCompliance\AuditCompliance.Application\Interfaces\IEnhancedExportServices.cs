using AuditCompliance.Application.DTOs;
using AuditCompliance.Domain.Entities;

namespace AuditCompliance.Application.Interfaces
{
    /// <summary>
    /// Interface for digital signature service
    /// </summary>
    public interface IDigitalSignatureService
    {
        /// <summary>
        /// Sign content with digital signature
        /// </summary>
        Task<string> SignContentAsync(string content);

        /// <summary>
        /// Sign content with digital signature
        /// </summary>
        Task<string> SignContentAsync(byte[] content);

        /// <summary>
        /// Verify digital signature
        /// </summary>
        Task<bool> VerifySignatureAsync(string content, string signature);

        /// <summary>
        /// Verify digital signature
        /// </summary>
        Task<bool> VerifySignatureAsync(byte[] content, string signature);

        /// <summary>
        /// Get signature metadata
        /// </summary>
        Task<DigitalSignatureMetadataDto> GetSignatureMetadataAsync(string signature);
    }

    /// <summary>
    /// Interface for background job service
    /// </summary>
    public interface IBackgroundJobService
    {
        /// <summary>
        /// Queue export job for background processing
        /// </summary>
        Task<string> QueueExportJobAsync(ExportJobDto job);

        /// <summary>
        /// Get job status
        /// </summary>
        Task<ExportJobStatusDto?> GetJobStatusAsync(string jobId);

        /// <summary>
        /// Cancel job
        /// </summary>
        Task<bool> CancelJobAsync(string jobId, string reason);

        /// <summary>
        /// Get job history
        /// </summary>
        Task<List<ExportJobStatusDto>> GetJobHistoryAsync(Guid? requestedBy = null, int pageNumber = 1, int pageSize = 20);

        /// <summary>
        /// Update job progress
        /// </summary>
        Task UpdateJobProgressAsync(string jobId, int progressPercentage, string? statusMessage = null);

        /// <summary>
        /// Complete job
        /// </summary>
        Task CompleteJobAsync(string jobId, ExportJobResultDto result);

        /// <summary>
        /// Fail job
        /// </summary>
        Task FailJobAsync(string jobId, string errorMessage, Exception? exception = null);
    }

    /// <summary>
    /// Interface for file storage service
    /// </summary>
    public interface IFileStorageService
    {
        /// <summary>
        /// Save export file
        /// </summary>
        Task<string> SaveExportFileAsync(string fileName, string content, string contentType);

        /// <summary>
        /// Save export file
        /// </summary>
        Task<string> SaveExportFileAsync(string fileName, byte[] content, string contentType);

        /// <summary>
        /// Get file content
        /// </summary>
        Task<byte[]> GetFileContentAsync(string fileUrl);

        /// <summary>
        /// Delete file
        /// </summary>
        Task<bool> DeleteFileAsync(string fileUrl);

        /// <summary>
        /// Check if file exists
        /// </summary>
        Task<bool> FileExistsAsync(string fileUrl);

        /// <summary>
        /// Get file metadata
        /// </summary>
        Task<FileMetadataDto> GetFileMetadataAsync(string fileUrl);

        /// <summary>
        /// Generate secure download URL
        /// </summary>
        Task<string> GenerateSecureDownloadUrlAsync(string fileUrl, TimeSpan expiration);
    }

    /// <summary>
    /// Interface for notification service
    /// </summary>
    public interface INotificationService
    {
        /// <summary>
        /// Send export completion notification
        /// </summary>
        Task SendExportCompletionNotificationAsync(string email, EnhancedAuditExportResponseDto exportResult);

        /// <summary>
        /// Send export failure notification
        /// </summary>
        Task SendExportFailureNotificationAsync(string email, string exportId, string errorMessage);

        /// <summary>
        /// Send bulk export completion notification
        /// </summary>
        Task SendBulkExportCompletionNotificationAsync(string email, BulkAuditExportResponseDto bulkResult);

        /// <summary>
        /// Send scheduled export notification
        /// </summary>
        Task SendScheduledExportNotificationAsync(List<string> emails, ScheduledExportDto schedule, EnhancedAuditExportResponseDto result);
    }

    /// <summary>
    /// Interface for module registry service
    /// </summary>
    public interface IModuleRegistryService
    {
        /// <summary>
        /// Get all registered modules
        /// </summary>
        Task<List<ModuleInfoDto>> GetAllModulesAsync();

        /// <summary>
        /// Get module by name
        /// </summary>
        Task<ModuleInfoDto?> GetModuleByNameAsync(string moduleName);

        /// <summary>
        /// Register module
        /// </summary>
        Task RegisterModuleAsync(ModuleInfoDto module);

        /// <summary>
        /// Update module
        /// </summary>
        Task UpdateModuleAsync(ModuleInfoDto module);

        /// <summary>
        /// Check if user has access to module
        /// </summary>
        Task<bool> HasModuleAccessAsync(Guid userId, string moduleName);

        /// <summary>
        /// Get modules accessible by user
        /// </summary>
        Task<List<ModuleInfoDto>> GetAccessibleModulesAsync(Guid userId);

        /// <summary>
        /// Grant module access to user
        /// </summary>
        Task GrantModuleAccessAsync(Guid userId, string moduleName, List<string> allowedActions, Guid grantedBy, DateTime? expiresAt = null, string? reason = null);

        /// <summary>
        /// Revoke module access from user
        /// </summary>
        Task RevokeModuleAccessAsync(Guid userId, string moduleName, Guid revokedBy, string? reason = null);

        /// <summary>
        /// Update module health status
        /// </summary>
        Task UpdateModuleHealthAsync(string moduleName, bool isHealthy, string? errorMessage = null);
    }

    /// <summary>
    /// Interface for module registry repository
    /// </summary>
    public interface IModuleRegistryRepository
    {
        Task<List<ModuleRegistry>> GetAllAsync();
        Task<ModuleRegistry?> GetByIdAsync(Guid id);
        Task<ModuleRegistry?> GetByModuleNameAsync(string moduleName);
        Task<List<ModuleRegistry>> GetActiveModulesAsync();
        Task<ModuleRegistry> AddAsync(ModuleRegistry module);
        Task UpdateAsync(ModuleRegistry module);
        Task DeleteAsync(Guid id);
    }

    /// <summary>
    /// Interface for module access control repository
    /// </summary>
    public interface IModuleAccessControlRepository
    {
        Task<ModuleAccessControl?> GetByUserAndModuleAsync(Guid userId, string moduleName);
        Task<List<ModuleAccessControl>> GetByUserIdAsync(Guid userId);
        Task<List<ModuleAccessControl>> GetByModuleNameAsync(string moduleName);
        Task<ModuleAccessControl> AddAsync(ModuleAccessControl accessControl);
        Task UpdateAsync(ModuleAccessControl accessControl);
        Task DeleteAsync(Guid id);
    }

    /// <summary>
    /// Interface for custom compliance report template repository
    /// </summary>
    public interface ICustomComplianceReportTemplateRepository
    {
        Task<CustomComplianceReportTemplate?> GetByIdAsync(Guid id);
        Task<CustomComplianceReportTemplate?> GetByNameAsync(string templateName);
        Task<List<CustomComplianceReportTemplate>> GetAllAsync();
        Task<List<CustomComplianceReportTemplate>> GetActiveTemplatesAsync();
        Task<List<CustomComplianceReportTemplate>> GetPublicTemplatesAsync();
        Task<List<CustomComplianceReportTemplate>> GetTemplatesByCreatorAsync(Guid createdBy);
        Task<CustomComplianceReportTemplate> AddAsync(CustomComplianceReportTemplate template);
        Task UpdateAsync(CustomComplianceReportTemplate template);
        Task DeleteAsync(Guid id);
    }

    /// <summary>
    /// Interface for retention policy management repository
    /// </summary>
    public interface IRetentionPolicyManagementRepository
    {
        Task<RetentionPolicyManagement?> GetByIdAsync(Guid id);
        Task<RetentionPolicyManagement?> GetByNameAsync(string policyName);
        Task<List<RetentionPolicyManagement>> GetAllAsync();
        Task<List<RetentionPolicyManagement>> GetActivePoliciesAsync();
        Task<List<RetentionPolicyManagement>> GetPendingApprovalPoliciesAsync();
        Task<RetentionPolicyManagement> AddAsync(RetentionPolicyManagement policy);
        Task UpdateAsync(RetentionPolicyManagement policy);
        Task DeleteAsync(Guid id);
    }

    /// <summary>
    /// Interface for export validation service
    /// </summary>
    public interface IExportValidationService
    {
        /// <summary>
        /// Validate export request
        /// </summary>
        Task<List<ExportValidationDto>> ValidateExportRequestAsync(EnhancedAuditExportRequestDto request);

        /// <summary>
        /// Validate bulk export request
        /// </summary>
        Task<List<ExportValidationDto>> ValidateBulkExportRequestAsync(BulkAuditExportRequestDto request);

        /// <summary>
        /// Check export permissions
        /// </summary>
        Task<bool> HasExportPermissionAsync(Guid userId, string userRole, EnhancedAuditExportRequestDto request);

        /// <summary>
        /// Validate export format configuration
        /// </summary>
        Task<List<ExportValidationDto>> ValidateFormatConfigurationAsync(ExportFormat format, List<string> selectedColumns);
    }

    /// <summary>
    /// Digital signature metadata DTO
    /// </summary>
    public class DigitalSignatureMetadataDto
    {
        public string SignatureId { get; set; } = string.Empty;
        public string Algorithm { get; set; } = string.Empty;
        public DateTime SignedAt { get; set; }
        public string SignedBy { get; set; } = string.Empty;
        public string CertificateThumbprint { get; set; } = string.Empty;
        public bool IsValid { get; set; }
        public DateTime? ExpiresAt { get; set; }
        public Dictionary<string, object> AdditionalInfo { get; set; } = new();
    }

    /// <summary>
    /// Export job DTO
    /// </summary>
    public class ExportJobDto
    {
        public string JobId { get; set; } = string.Empty;
        public string JobType { get; set; } = string.Empty;
        public string Parameters { get; set; } = string.Empty;
        public Guid RequestedBy { get; set; }
        public DateTime RequestedAt { get; set; }
        public string? NotificationEmail { get; set; }
        public int Priority { get; set; } = 0;
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// Export job result DTO
    /// </summary>
    public class ExportJobResultDto
    {
        public string JobId { get; set; } = string.Empty;
        public bool IsSuccess { get; set; }
        public string? FileUrl { get; set; }
        public string? FileName { get; set; }
        public long FileSizeBytes { get; set; }
        public int RecordCount { get; set; }
        public TimeSpan ProcessingTime { get; set; }
        public string? ErrorMessage { get; set; }
        public Dictionary<string, object> ResultMetadata { get; set; } = new();
    }

    /// <summary>
    /// File metadata DTO
    /// </summary>
    public class FileMetadataDto
    {
        public string FileName { get; set; } = string.Empty;
        public string ContentType { get; set; } = string.Empty;
        public long FileSizeBytes { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? LastModified { get; set; }
        public string? ETag { get; set; }
        public Dictionary<string, string> CustomMetadata { get; set; } = new();
    }

    /// <summary>
    /// Module information DTO
    /// </summary>
    public class ModuleInfoDto
    {
        public string ModuleName { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Version { get; set; } = string.Empty;
        public bool IsActive { get; set; }
        public List<string> SupportedEntityTypes { get; set; } = new();
        public List<string> SupportedActions { get; set; } = new();
        public List<string> RequiredRoles { get; set; } = new();
        public Dictionary<string, object> Configuration { get; set; } = new();
        public DateTime RegisteredAt { get; set; }
        public DateTime? LastUpdated { get; set; }
    }

    /// <summary>
    /// Enhanced audit trail query DTO with module support
    /// </summary>
    public class AuditTrailQueryDto
    {
        public List<Guid> UserIds { get; set; } = new();
        public List<string> EntityTypes { get; set; } = new();
        public List<string> ModuleNames { get; set; } = new(); // New
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public bool IncludeSensitiveData { get; set; } = false;
        public int PageSize { get; set; } = 50;
        public int? MaxRecords { get; set; }
        public List<string> ComplianceFlags { get; set; } = new(); // New
        public Dictionary<string, object> CustomFilters { get; set; } = new(); // New
    }

    /// <summary>
    /// Audit trail result DTO
    /// </summary>
    public class AuditTrailResultDto
    {
        public List<object> Items { get; set; } = new();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public bool HasNextPage { get; set; }
        public bool HasPreviousPage { get; set; }
    }
}
