using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TripManagement.Domain.Entities;
using System.Text.Json;

namespace TripManagement.Infrastructure.Persistence.Configurations;

public class RoutingFailureConfiguration : IEntityTypeConfiguration<RoutingFailure>
{
    public void Configure(EntityTypeBuilder<RoutingFailure> builder)
    {
        builder.ToTable("routing_failures");

        builder.HasKey(rf => rf.Id);

        builder.Property(rf => rf.Id)
            .ValueGeneratedNever();

        builder.Property(rf => rf.TripId)
            .IsRequired();

        builder.Property(rf => rf.OrderNumber)
            .HasMaxLength(50);

        builder.Property(rf => rf.FailureType)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(rf => rf.Severity)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(rf => rf.FailureReason)
            .IsRequired()
            .HasMaxLength(500);

        builder.Property(rf => rf.Description)
            .HasMaxLength(1000);

        builder.Property(rf => rf.Category)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(rf => rf.FailureTime)
            .IsRequired();

        builder.Property(rf => rf.DetectedAt);

        builder.Property(rf => rf.ResolvedAt);

        builder.Property(rf => rf.Status)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(rf => rf.ResolutionAction)
            .HasMaxLength(1000);

        builder.Property(rf => rf.ResolvedBy);

        builder.Property(rf => rf.ImpactCost)
            .HasColumnType("decimal(10,2)");

        builder.Property(rf => rf.AffectedCustomers);

        builder.Property(rf => rf.IsRecurring)
            .IsRequired()
            .HasDefaultValue(false);

        builder.Property(rf => rf.RootCause)
            .HasMaxLength(500);

        builder.Property(rf => rf.PreventiveAction)
            .HasMaxLength(1000);

        // Configure TechnicalDetails as JSON
        builder.Property(rf => rf.TechnicalDetails)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        // Configure Metadata as JSON
        builder.Property(rf => rf.Metadata)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        // Configure FailureLocation as owned entity
        builder.OwnsOne(rf => rf.FailureLocation, location =>
        {
            location.Property(l => l.Latitude)
                .HasColumnName("FailureLatitude")
                .HasColumnType("decimal(10,8)");
            
            location.Property(l => l.Longitude)
                .HasColumnName("FailureLongitude")
                .HasColumnType("decimal(11,8)");
            
            location.Property(l => l.Address)
                .HasColumnName("FailureAddress")
                .HasMaxLength(500);
        });

        // Configure DelayTime as TimeSpan
        builder.Property(rf => rf.DelayTime)
            .HasConversion(
                v => v.HasValue ? v.Value.Ticks : (long?)null,
                v => v.HasValue ? new TimeSpan(v.Value) : (TimeSpan?)null);

        // Configure ResolutionTime as TimeSpan
        builder.Property(rf => rf.ResolutionTime)
            .HasConversion(
                v => v.HasValue ? v.Value.Ticks : (long?)null,
                v => v.HasValue ? new TimeSpan(v.Value) : (TimeSpan?)null);

        builder.Property(rf => rf.CreatedAt)
            .IsRequired();

        builder.Property(rf => rf.UpdatedAt)
            .IsRequired();

        // Relationships
        builder.HasOne(rf => rf.Trip)
            .WithMany()
            .HasForeignKey(rf => rf.TripId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(rf => rf.TripId)
            .HasDatabaseName("IX_RoutingFailures_TripId");

        builder.HasIndex(rf => rf.FailureType)
            .HasDatabaseName("IX_RoutingFailures_FailureType");

        builder.HasIndex(rf => rf.Severity)
            .HasDatabaseName("IX_RoutingFailures_Severity");

        builder.HasIndex(rf => rf.Status)
            .HasDatabaseName("IX_RoutingFailures_Status");

        builder.HasIndex(rf => rf.Category)
            .HasDatabaseName("IX_RoutingFailures_Category");

        builder.HasIndex(rf => rf.FailureTime)
            .HasDatabaseName("IX_RoutingFailures_FailureTime");

        builder.HasIndex(rf => rf.DetectedAt)
            .HasDatabaseName("IX_RoutingFailures_DetectedAt");

        builder.HasIndex(rf => rf.ResolvedAt)
            .HasDatabaseName("IX_RoutingFailures_ResolvedAt");

        builder.HasIndex(rf => rf.IsRecurring)
            .HasDatabaseName("IX_RoutingFailures_IsRecurring");

        builder.HasIndex(rf => new { rf.FailureType, rf.Severity, rf.Status })
            .HasDatabaseName("IX_RoutingFailures_Type_Severity_Status");

        builder.HasIndex(rf => new { rf.FailureTime, rf.Status })
            .HasDatabaseName("IX_RoutingFailures_FailureTime_Status");

        builder.HasIndex(rf => new { rf.TripId, rf.Status })
            .HasDatabaseName("IX_RoutingFailures_TripId_Status");
    }
}
