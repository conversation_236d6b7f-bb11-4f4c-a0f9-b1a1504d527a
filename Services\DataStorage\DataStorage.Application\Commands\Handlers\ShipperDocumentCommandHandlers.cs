using AutoMapper;
using DataStorage.Application.DTOs;
using DataStorage.Domain.Entities;
using DataStorage.Domain.Enums;
using DataStorage.Domain.Exceptions;
using DataStorage.Domain.Interfaces;
using DataStorage.Domain.ValueObjects;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Domain.Common;

namespace DataStorage.Application.Commands.Handlers;

public class UploadEWayBillCommandHandler : IRequestHandler<UploadEWayBillCommand, DocumentDto>
{
    private readonly IDocumentRepository _documentRepository;
    private readonly IFileStorageService _fileStorageService;
    private readonly IDocumentProcessingService _documentProcessingService;
    private readonly IMapper _mapper;
    private readonly ILogger<UploadEWayBillCommandHandler> _logger;

    public UploadEWayBillCommandHandler(
        IDocumentRepository documentRepository,
        IFileStorageService fileStorageService,
        IDocumentProcessingService documentProcessingService,
        IMapper mapper,
        ILogger<UploadEWayBillCommandHandler> logger)
    {
        _documentRepository = documentRepository;
        _fileStorageService = fileStorageService;
        _documentProcessingService = documentProcessingService;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<DocumentDto> Handle(UploadEWayBillCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Uploading E-Way Bill for shipper {ShipperId}", request.ShipperId);

            // Validate file format (should be PDF or image)
            if (!IsValidEWayBillFormat(request.ContentType))
            {
                throw new InvalidFileFormatException(request.FileName, "PDF or Image", request.ContentType);
            }

            // Upload file to storage
            var storageLocation = await _fileStorageService.UploadFileAsync(
                request.FileStream,
                request.FileName,
                request.ContentType,
                cancellationToken: cancellationToken);

            // Get file metadata
            var fileMetadata = await _fileStorageService.GetFileMetadataAsync(storageLocation, cancellationToken);

            // Calculate checksum
            var checksum = await _documentProcessingService.CalculateChecksumAsync(storageLocation, cancellationToken);
            fileMetadata = fileMetadata.WithChecksum(checksum);

            // Prepare tags with E-Way Bill specific information
            var tags = new Dictionary<string, string>(request.AdditionalMetadata ?? new Dictionary<string, string>());
            if (!string.IsNullOrWhiteSpace(request.EWayBillNumber))
            {
                tags["eway_bill_number"] = request.EWayBillNumber;
            }
            if (request.ValidUntil.HasValue)
            {
                tags["valid_until"] = request.ValidUntil.Value.ToString("O");
            }
            tags["document_subtype"] = "eway_bill";

            // Create document entity
            var document = new Document(
                request.Title,
                request.Description,
                DocumentType.EWayBill,
                DocumentCategory.Legal,
                fileMetadata,
                storageLocation,
                request.ShipperId,
                "Shipper",
                AccessLevel.Private,
                request.ValidUntil,
                tags);

            // Save to repository
            await _documentRepository.AddAsync(document, cancellationToken);

            _logger.LogInformation("E-Way Bill uploaded successfully. DocumentId: {DocumentId}", document.Id);

            var documentDto = _mapper.Map<DocumentDto>(document);
            documentDto.AccessUrl = storageLocation.GetAccessUrl();

            return documentDto;
        }
        catch (Exception ex) when (!(ex is DataStorageDomainException))
        {
            _logger.LogError(ex, "Failed to upload E-Way Bill for shipper {ShipperId}", request.ShipperId);
            throw new FileUploadException(request.FileName, ex);
        }
    }

    private static bool IsValidEWayBillFormat(string contentType)
    {
        return contentType.Equals("application/pdf", StringComparison.OrdinalIgnoreCase) ||
               contentType.StartsWith("image/", StringComparison.OrdinalIgnoreCase);
    }
}

public class GenerateDigitalInvoiceCommandHandler : IRequestHandler<GenerateDigitalInvoiceCommand, DocumentDto>
{
    private readonly IDocumentRepository _documentRepository;
    private readonly IFileStorageService _fileStorageService;
    private readonly IMapper _mapper;
    private readonly ILogger<GenerateDigitalInvoiceCommandHandler> _logger;

    public GenerateDigitalInvoiceCommandHandler(
        IDocumentRepository documentRepository,
        IFileStorageService fileStorageService,
        IMapper mapper,
        ILogger<GenerateDigitalInvoiceCommandHandler> logger)
    {
        _documentRepository = documentRepository;
        _fileStorageService = fileStorageService;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<DocumentDto> Handle(GenerateDigitalInvoiceCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Generating digital invoice {InvoiceNumber} for shipper {ShipperId}",
                request.InvoiceNumber, request.ShipperId);

            // Generate invoice content (this would typically use a template engine)
            var invoiceContent = await GenerateInvoiceContentAsync(request, cancellationToken);
            var fileName = $"Invoice_{request.InvoiceNumber}_{DateTime.UtcNow:yyyyMMdd}.pdf";

            // Upload generated invoice
            var storageLocation = await _fileStorageService.UploadFileAsync(
                invoiceContent,
                fileName,
                "application/pdf",
                cancellationToken: cancellationToken);

            // Get file metadata
            var fileMetadata = await _fileStorageService.GetFileMetadataAsync(storageLocation, cancellationToken);

            // Prepare tags with invoice specific information
            var tags = new Dictionary<string, string>
            {
                ["invoice_number"] = request.InvoiceNumber,
                ["order_id"] = request.OrderId.ToString(),
                ["amount"] = request.Amount.ToString("F2"),
                ["currency"] = request.Currency,
                ["invoice_date"] = request.InvoiceDate.ToString("O"),
                ["due_date"] = request.DueDate.ToString("O"),
                ["document_subtype"] = "digital_invoice",
                ["auto_generated"] = request.AutoGenerate.ToString()
            };

            // Create document entity
            var document = new Document(
                $"Digital Invoice - {request.InvoiceNumber}",
                $"Auto-generated digital invoice for order {request.OrderId}",
                DocumentType.DigitalInvoice,
                DocumentCategory.Financial,
                fileMetadata,
                storageLocation,
                request.ShipperId,
                "Shipper",
                AccessLevel.Internal,
                expiresAt: null,
                tags);

            // Save to repository
            await _documentRepository.AddAsync(document, cancellationToken);

            _logger.LogInformation("Digital invoice generated successfully. DocumentId: {DocumentId}", document.Id);

            var documentDto = _mapper.Map<DocumentDto>(document);
            documentDto.AccessUrl = storageLocation.GetAccessUrl();

            return documentDto;
        }
        catch (Exception ex) when (!(ex is DataStorageDomainException))
        {
            _logger.LogError(ex, "Failed to generate digital invoice for shipper {ShipperId}", request.ShipperId);
            throw new DocumentProcessingException("generate digital invoice", request.InvoiceNumber, ex);
        }
    }

    private async Task<byte[]> GenerateInvoiceContentAsync(GenerateDigitalInvoiceCommand request, CancellationToken cancellationToken)
    {
        // This is a placeholder implementation
        // In a real implementation, you would use a PDF generation library like iText7
        // or a template engine to generate the actual invoice content

        var invoiceText = $@"
DIGITAL INVOICE
===============

Invoice Number: {request.InvoiceNumber}
Order ID: {request.OrderId}
Invoice Date: {request.InvoiceDate:yyyy-MM-dd}
Due Date: {request.DueDate:yyyy-MM-dd}

Amount: {request.Amount:F2} {request.Currency}

Generated on: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC
";

        return System.Text.Encoding.UTF8.GetBytes(invoiceText);
    }
}

public class UploadTaxDocumentCommandHandler : IRequestHandler<UploadTaxDocumentCommand, DocumentDto>
{
    private readonly IDocumentRepository _documentRepository;
    private readonly IFileStorageService _fileStorageService;
    private readonly IDocumentProcessingService _documentProcessingService;
    private readonly IMapper _mapper;
    private readonly ILogger<UploadTaxDocumentCommandHandler> _logger;

    public UploadTaxDocumentCommandHandler(
        IDocumentRepository documentRepository,
        IFileStorageService fileStorageService,
        IDocumentProcessingService documentProcessingService,
        IMapper mapper,
        ILogger<UploadTaxDocumentCommandHandler> logger)
    {
        _documentRepository = documentRepository;
        _fileStorageService = fileStorageService;
        _documentProcessingService = documentProcessingService;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<DocumentDto> Handle(UploadTaxDocumentCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Uploading tax document for shipper {ShipperId}, type: {TaxDocumentType}",
                request.ShipperId, request.TaxDocumentType);

            // Upload file to storage
            var storageLocation = await _fileStorageService.UploadFileAsync(
                request.FileStream,
                request.FileName,
                request.ContentType,
                cancellationToken: cancellationToken);

            // Get file metadata
            var fileMetadata = await _fileStorageService.GetFileMetadataAsync(storageLocation, cancellationToken);

            // Calculate checksum
            var checksum = await _documentProcessingService.CalculateChecksumAsync(storageLocation, cancellationToken);
            fileMetadata = fileMetadata.WithChecksum(checksum);

            // Prepare tags with tax document specific information
            var tags = new Dictionary<string, string>
            {
                ["tax_document_type"] = request.TaxDocumentType.ToString(),
                ["document_subtype"] = "tax_document"
            };

            if (!string.IsNullOrWhiteSpace(request.GSTNumber))
                tags["gst_number"] = request.GSTNumber;

            if (!string.IsNullOrWhiteSpace(request.PANNumber))
                tags["pan_number"] = request.PANNumber;

            if (request.TaxPeriodStart.HasValue)
                tags["tax_period_start"] = request.TaxPeriodStart.Value.ToString("O");

            if (request.TaxPeriodEnd.HasValue)
                tags["tax_period_end"] = request.TaxPeriodEnd.Value.ToString("O");

            // Create document entity
            var document = new Document(
                request.Title,
                request.Description,
                DocumentType.TaxDocument,
                DocumentCategory.Financial,
                fileMetadata,
                storageLocation,
                request.ShipperId,
                "Shipper",
                AccessLevel.Private,
                expiresAt: null,
                tags);

            // Save to repository
            await _documentRepository.AddAsync(document, cancellationToken);

            _logger.LogInformation("Tax document uploaded successfully. DocumentId: {DocumentId}", document.Id);

            var documentDto = _mapper.Map<DocumentDto>(document);
            documentDto.AccessUrl = storageLocation.GetAccessUrl();

            return documentDto;
        }
        catch (Exception ex) when (!(ex is DataStorageDomainException))
        {
            _logger.LogError(ex, "Failed to upload tax document for shipper {ShipperId}", request.ShipperId);
            throw new FileUploadException(request.FileName, ex);
        }
    }
}

