using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using TripManagement.Application.Commands.ReportException;
using TripManagement.Application.Commands.ResolveException;
using TripManagement.Application.Commands.DetectExceptions;
using TripManagement.Application.DTOs;
using TripManagement.Domain.Enums;

namespace TripManagement.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class ExceptionsController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<ExceptionsController> _logger;

    public ExceptionsController(IMediator mediator, ILogger<ExceptionsController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Report a new exception for a trip
    /// </summary>
    [HttpPost("report")]
    [Authorize(Roles = "Driver,Carrier,Admin")]
    public async Task<ActionResult<Guid>> ReportException([FromBody] ReportExceptionRequest request)
    {
        try
        {
            var command = new ReportExceptionCommand
            {
                TripId = request.TripId,
                ReportedBy = request.ReportedBy,
                ExceptionType = request.ExceptionType,
                Description = request.Description,
                Location = request.Location,
                Severity = request.Severity,
                RequiresImmediateAttention = request.RequiresImmediateAttention,
                SuggestedResolution = request.SuggestedResolution,
                AttachmentUrls = request.AttachmentUrls
            };

            var exceptionId = await _mediator.Send(command);
            return CreatedAtAction(nameof(GetException), new { id = exceptionId }, exceptionId);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid argument while reporting exception for trip {TripId}", 
                request.TripId);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reporting exception for trip {TripId}", request.TripId);
            return StatusCode(500, "An error occurred while reporting the exception");
        }
    }

    /// <summary>
    /// Resolve an existing exception
    /// </summary>
    [HttpPost("{id:guid}/resolve")]
    [Authorize(Roles = "Carrier,Admin,Driver")]
    public async Task<ActionResult> ResolveException(Guid id, [FromBody] ResolveExceptionRequest request)
    {
        try
        {
            var command = new ResolveExceptionCommand
            {
                TripId = request.TripId,
                ExceptionId = id,
                Resolution = request.Resolution,
                ResolvedBy = request.ResolvedBy,
                ResolvedAt = request.ResolvedAt,
                Notes = request.Notes
            };

            var result = await _mediator.Send(command);

            if (result)
                return Ok(new { Message = "Exception resolved successfully" });

            return BadRequest("Failed to resolve exception");
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid argument while resolving exception {ExceptionId}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resolving exception {ExceptionId}", id);
            return StatusCode(500, "An error occurred while resolving the exception");
        }
    }

    /// <summary>
    /// Detect exceptions automatically
    /// </summary>
    [HttpPost("detect")]
    [Authorize(Roles = "System,Admin")]
    public async Task<ActionResult<List<DetectedExceptionDto>>> DetectExceptions([FromBody] DetectExceptionsRequest request)
    {
        try
        {
            var command = new DetectExceptionsCommand
            {
                TripId = request.TripId,
                Rules = request.Rules
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error detecting exceptions for trip {TripId}", request.TripId);
            return StatusCode(500, "An error occurred while detecting exceptions");
        }
    }

    /// <summary>
    /// Get exception details
    /// </summary>
    [HttpGet("{id:guid}")]
    [Authorize(Roles = "Admin,Carrier,Driver,Broker")]
    public async Task<ActionResult<TripExceptionDto>> GetException(Guid id)
    {
        try
        {
            // This would require a query handler - placeholder for now
            return NotFound("Exception not found");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting exception {ExceptionId}", id);
            return StatusCode(500, "An error occurred while retrieving the exception");
        }
    }

    /// <summary>
    /// Get exceptions for a trip
    /// </summary>
    [HttpGet("trip/{tripId:guid}")]
    [Authorize(Roles = "Admin,Carrier,Driver,Broker")]
    public async Task<ActionResult<List<TripExceptionDto>>> GetTripExceptions(Guid tripId)
    {
        try
        {
            // This would require a query handler - placeholder for now
            return Ok(new List<TripExceptionDto>());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting exceptions for trip {TripId}", tripId);
            return StatusCode(500, "An error occurred while retrieving trip exceptions");
        }
    }
}

// Request DTOs
public record ReportExceptionRequest
{
    public Guid TripId { get; init; }
    public Guid ReportedBy { get; init; }
    public ExceptionType ExceptionType { get; init; }
    public string Description { get; init; } = string.Empty;
    public LocationDto? Location { get; init; }
    public ExceptionSeverity Severity { get; init; } = ExceptionSeverity.Medium;
    public bool RequiresImmediateAttention { get; init; }
    public string? SuggestedResolution { get; init; }
    public List<string>? AttachmentUrls { get; init; }
}

public record ResolveExceptionRequest
{
    public Guid TripId { get; init; }
    public string Resolution { get; init; } = string.Empty;
    public string ResolvedBy { get; init; } = string.Empty;
    public DateTime? ResolvedAt { get; init; }
    public string? Notes { get; init; }
}

public record DetectExceptionsRequest
{
    public Guid? TripId { get; init; }
    public List<ExceptionDetectionRule> Rules { get; init; } = new();
}
