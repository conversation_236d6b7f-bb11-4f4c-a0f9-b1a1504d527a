using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Repositories;

namespace NetworkFleetManagement.Application.Queries.PreferredPartners;

public class GetAutoAssignmentSettingsQueryHandler : IRequestHandler<GetAutoAssignmentSettingsQuery, AutoAssignmentSettingsDto?>
{
    private readonly IPreferredPartnerRepository _preferredPartnerRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetAutoAssignmentSettingsQueryHandler> _logger;

    public GetAutoAssignmentSettingsQueryHandler(
        IPreferredPartnerRepository preferredPartnerRepository,
        IMapper mapper,
        ILogger<GetAutoAssignmentSettingsQueryHandler> logger)
    {
        _preferredPartnerRepository = preferredPartnerRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<AutoAssignmentSettingsDto?> Handle(GetAutoAssignmentSettingsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var partner = await _preferredPartnerRepository.GetByIdAsync(request.PreferredPartnerId, cancellationToken);
            
            if (partner == null || partner.UserId != request.UserId)
            {
                return null;
            }

            return new AutoAssignmentSettingsDto
            {
                PreferredPartnerId = partner.Id,
                AutoAssignEnabled = partner.AutoAssignEnabled,
                AutoAssignThreshold = partner.AutoAssignThreshold,
                PreferredRoutes = partner.PreferredRoutes.ToList(),
                PreferredLoadTypes = partner.PreferredLoadTypes.ToList(),
                ExcludedRoutes = partner.ExcludedRoutes.ToList(),
                ExcludedLoadTypes = partner.ExcludedLoadTypes.ToList(),
                LastUpdated = partner.UpdatedAt ?? partner.CreatedAt
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving auto-assignment settings for preferred partner {PreferredPartnerId}", 
                request.PreferredPartnerId);
            throw;
        }
    }
}
