using CommunicationNotification.Tests.Infrastructure;
using FluentAssertions;
using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using Xunit;
using Xunit.Abstractions;

namespace CommunicationNotification.Tests.Integration;

/// <summary>
/// Integration tests for message API endpoints
/// </summary>
public class MessageApiTests : IntegrationTestBase
{
    public MessageApiTests(ITestOutputHelper output) : base(output)
    {
    }

    [Fact]
    public async Task GetUserMessageHistory_WithValidUser_ShouldReturnMessages()
    {
        // Arrange
        await WebApplicationFactory.SeedTestDataAsync();
        using var client = WebApplicationFactory.CreateCustomerClient();

        // Act
        var response = await client.GetAsync($"/api/messages/user/{TestConstants.TestCustomerId}/history");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        WriteOutput($"Response: {content}");
        
        var result = JsonSerializer.Deserialize<JsonElement>(content);
        result.GetProperty("success").GetBoolean().Should().BeTrue();
        result.GetProperty("data").GetProperty("messages").GetArrayLength().Should().BeGreaterOrEqualTo(0);
    }

    [Fact]
    public async Task GetUserMessageHistory_WithPagination_ShouldReturnPagedResults()
    {
        // Arrange
        await WebApplicationFactory.SeedTestDataAsync();
        using var client = WebApplicationFactory.CreateCustomerClient();

        // Act
        var response = await client.GetAsync($"/api/messages/user/{TestConstants.TestCustomerId}/history?pageNumber=1&pageSize=5");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<JsonElement>(content);
        result.GetProperty("success").GetBoolean().Should().BeTrue();
        
        var pagination = result.GetProperty("data").GetProperty("pagination");
        pagination.GetProperty("pageNumber").GetInt32().Should().Be(1);
        pagination.GetProperty("pageSize").GetInt32().Should().Be(5);
    }

    [Fact]
    public async Task GetUserMessageHistory_WithFilters_ShouldReturnFilteredResults()
    {
        // Arrange
        await WebApplicationFactory.SeedTestDataAsync();
        using var client = WebApplicationFactory.CreateCustomerClient();

        var fromDate = DateTime.UtcNow.AddDays(-7).ToString("O");
        var toDate = DateTime.UtcNow.ToString("O");

        // Act
        var response = await client.GetAsync(
            $"/api/messages/user/{TestConstants.TestCustomerId}/history?messageType=Support&fromDate={fromDate}&toDate={toDate}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<JsonElement>(content);
        result.GetProperty("success").GetBoolean().Should().BeTrue();
    }

    [Fact]
    public async Task GetUserMessageHistory_ForOtherUser_ShouldReturnForbidden()
    {
        // Arrange
        using var client = WebApplicationFactory.CreateCustomerClient();

        // Act - Try to access another user's messages
        var response = await client.GetAsync($"/api/messages/user/{TestConstants.TestDriverId}/history");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Forbidden);
    }

    [Fact]
    public async Task GetUserMessageHistory_AsAdmin_ShouldAccessAnyUser()
    {
        // Arrange
        await WebApplicationFactory.SeedTestDataAsync();
        using var client = WebApplicationFactory.CreateAdminClient();

        // Act - Admin accessing any user's messages
        var response = await client.GetAsync($"/api/messages/user/{TestConstants.TestCustomerId}/history");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
    }

    [Fact]
    public async Task GetMessageDetails_WithValidId_ShouldReturnDetails()
    {
        // Arrange
        await WebApplicationFactory.SeedTestDataAsync();
        using var client = WebApplicationFactory.CreateCustomerClient();

        // First get message history to find a message ID
        var historyResponse = await client.GetAsync($"/api/messages/user/{TestConstants.TestCustomerId}/history");
        var historyContent = await historyResponse.Content.ReadAsStringAsync();
        var historyResult = JsonSerializer.Deserialize<JsonElement>(historyContent);
        
        var messages = historyResult.GetProperty("data").GetProperty("messages");
        if (messages.GetArrayLength() == 0)
        {
            // Skip test if no messages exist
            return;
        }

        var messageId = messages[0].GetProperty("id").GetString();

        // Act
        var response = await client.GetAsync($"/api/messages/{messageId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<JsonElement>(content);
        result.GetProperty("success").GetBoolean().Should().BeTrue();
        result.GetProperty("data").GetProperty("id").GetString().Should().Be(messageId);
    }

    [Fact]
    public async Task GetMessageDetails_WithInvalidId_ShouldReturnNotFound()
    {
        // Arrange
        using var client = WebApplicationFactory.CreateCustomerClient();
        var invalidId = Guid.NewGuid();

        // Act
        var response = await client.GetAsync($"/api/messages/{invalidId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task MarkMessagesAsRead_WithValidIds_ShouldReturnSuccess()
    {
        // Arrange
        await WebApplicationFactory.SeedTestDataAsync();
        using var client = WebApplicationFactory.CreateCustomerClient();

        // First get some message IDs
        var historyResponse = await client.GetAsync($"/api/messages/user/{TestConstants.TestCustomerId}/history");
        var historyContent = await historyResponse.Content.ReadAsStringAsync();
        var historyResult = JsonSerializer.Deserialize<JsonElement>(historyContent);
        
        var messages = historyResult.GetProperty("data").GetProperty("messages");
        if (messages.GetArrayLength() == 0)
        {
            // Skip test if no messages exist
            return;
        }

        var messageIds = new List<string>();
        for (int i = 0; i < Math.Min(messages.GetArrayLength(), 3); i++)
        {
            messageIds.Add(messages[i].GetProperty("id").GetString()!);
        }

        var request = new
        {
            MessageIds = messageIds
        };

        // Act
        var response = await client.PostAsJsonAsync("/api/messages/mark-read", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<JsonElement>(content);
        result.GetProperty("success").GetBoolean().Should().BeTrue();
    }

    [Fact]
    public async Task ArchiveMessages_WithValidIds_ShouldReturnSuccess()
    {
        // Arrange
        await WebApplicationFactory.SeedTestDataAsync();
        using var client = WebApplicationFactory.CreateCustomerClient();

        // First get some message IDs
        var historyResponse = await client.GetAsync($"/api/messages/user/{TestConstants.TestCustomerId}/history");
        var historyContent = await historyResponse.Content.ReadAsStringAsync();
        var historyResult = JsonSerializer.Deserialize<JsonElement>(historyContent);
        
        var messages = historyResult.GetProperty("data").GetProperty("messages");
        if (messages.GetArrayLength() == 0)
        {
            // Skip test if no messages exist
            return;
        }

        var messageIds = new List<string> { messages[0].GetProperty("id").GetString()! };

        var request = new
        {
            MessageIds = messageIds,
            Reason = "User requested archival"
        };

        // Act
        var response = await client.PostAsJsonAsync("/api/messages/archive", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<JsonElement>(content);
        result.GetProperty("success").GetBoolean().Should().BeTrue();
    }

    [Fact]
    public async Task SearchMessages_WithValidQuery_ShouldReturnResults()
    {
        // Arrange
        await WebApplicationFactory.SeedTestDataAsync();
        using var client = WebApplicationFactory.CreateAdminClient(); // Admin can search all messages

        var request = new
        {
            Query = "test",
            PageNumber = 1,
            PageSize = 10,
            MessageType = "Support",
            FromDate = DateTime.UtcNow.AddDays(-30).ToString("O"),
            ToDate = DateTime.UtcNow.ToString("O")
        };

        // Act
        var response = await client.PostAsJsonAsync("/api/messages/search", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<JsonElement>(content);
        result.GetProperty("success").GetBoolean().Should().BeTrue();
    }

    [Fact]
    public async Task SearchMessages_AsNonAdmin_ShouldReturnForbidden()
    {
        // Arrange
        using var client = WebApplicationFactory.CreateCustomerClient();

        var request = new
        {
            Query = "test",
            PageNumber = 1,
            PageSize = 10
        };

        // Act
        var response = await client.PostAsJsonAsync("/api/messages/search", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Forbidden);
    }

    [Fact]
    public async Task GetMessageStatistics_AsAdmin_ShouldReturnStats()
    {
        // Arrange
        await WebApplicationFactory.SeedTestDataAsync();
        using var client = WebApplicationFactory.CreateAdminClient();

        var fromDate = DateTime.UtcNow.AddDays(-30).ToString("O");
        var toDate = DateTime.UtcNow.ToString("O");

        // Act
        var response = await client.GetAsync($"/api/messages/statistics?fromDate={fromDate}&toDate={toDate}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        WriteOutput($"Statistics Response: {content}");
        
        var result = JsonSerializer.Deserialize<JsonElement>(content);
        result.GetProperty("success").GetBoolean().Should().BeTrue();
        
        var stats = result.GetProperty("data");
        stats.GetProperty("totalMessages").GetInt32().Should().BeGreaterOrEqualTo(0);
        stats.GetProperty("readMessages").GetInt32().Should().BeGreaterOrEqualTo(0);
        stats.GetProperty("unreadMessages").GetInt32().Should().BeGreaterOrEqualTo(0);
    }

    [Fact]
    public async Task GetMessageStatistics_AsNonAdmin_ShouldReturnForbidden()
    {
        // Arrange
        using var client = WebApplicationFactory.CreateCustomerClient();

        // Act
        var response = await client.GetAsync("/api/messages/statistics");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Forbidden);
    }

    [Fact]
    public async Task GetUserMessageHistory_WithInvalidUserId_ShouldReturnBadRequest()
    {
        // Arrange
        using var client = WebApplicationFactory.CreateCustomerClient();

        // Act
        var response = await client.GetAsync("/api/messages/user/invalid-guid/history");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task MarkMessagesAsRead_WithEmptyList_ShouldReturnBadRequest()
    {
        // Arrange
        using var client = WebApplicationFactory.CreateCustomerClient();

        var request = new
        {
            MessageIds = new string[0]
        };

        // Act
        var response = await client.PostAsJsonAsync("/api/messages/mark-read", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task MarkMessagesAsRead_WithInvalidIds_ShouldReturnPartialSuccess()
    {
        // Arrange
        using var client = WebApplicationFactory.CreateCustomerClient();

        var request = new
        {
            MessageIds = new[] { Guid.NewGuid().ToString(), Guid.NewGuid().ToString() }
        };

        // Act
        var response = await client.PostAsJsonAsync("/api/messages/mark-read", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<JsonElement>(content);
        result.GetProperty("success").GetBoolean().Should().BeTrue();
        result.GetProperty("data").GetProperty("processedCount").GetInt32().Should().Be(0);
    }

    [Theory]
    [InlineData(0, 20)]
    [InlineData(-1, 20)]
    [InlineData(1, 0)]
    [InlineData(1, -1)]
    [InlineData(1, 101)] // Exceeds max page size
    public async Task GetUserMessageHistory_WithInvalidPagination_ShouldReturnBadRequest(int pageNumber, int pageSize)
    {
        // Arrange
        using var client = WebApplicationFactory.CreateCustomerClient();

        // Act
        var response = await client.GetAsync(
            $"/api/messages/user/{TestConstants.TestCustomerId}/history?pageNumber={pageNumber}&pageSize={pageSize}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task GetUserMessageHistory_WithFutureDates_ShouldReturnEmptyResults()
    {
        // Arrange
        await WebApplicationFactory.SeedTestDataAsync();
        using var client = WebApplicationFactory.CreateCustomerClient();

        var futureFromDate = DateTime.UtcNow.AddDays(1).ToString("O");
        var futureToDate = DateTime.UtcNow.AddDays(2).ToString("O");

        // Act
        var response = await client.GetAsync(
            $"/api/messages/user/{TestConstants.TestCustomerId}/history?fromDate={futureFromDate}&toDate={futureToDate}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<JsonElement>(content);
        result.GetProperty("success").GetBoolean().Should().BeTrue();
        result.GetProperty("data").GetProperty("messages").GetArrayLength().Should().Be(0);
    }
}
