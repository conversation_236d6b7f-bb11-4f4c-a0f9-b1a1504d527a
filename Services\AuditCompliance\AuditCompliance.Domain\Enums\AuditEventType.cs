namespace AuditCompliance.Domain.Enums;

/// <summary>
/// Types of audit events that can be logged
/// </summary>
public enum AuditEventType
{
    // User Management Events
    UserLogin = 1,
    UserLogout = 2,
    UserRegistration = 3,
    UserProfileUpdate = 4,
    PasswordChange = 5,
    PasswordReset = 6,
    AccountLocked = 7,
    AccountUnlocked = 8,
    
    // RFQ and Order Events
    RfqCreated = 10,
    RfqUpdated = 11,
    RfqClosed = 12,
    BidSubmitted = 13,
    BidAccepted = 14,
    BidRejected = 15,
    OrderCreated = 16,
    OrderUpdated = 17,
    OrderCancelled = 18,
    OrderCompleted = 19,
    
    // Trip Management Events
    TripCreated = 20,
    TripStarted = 21,
    TripCompleted = 22,
    TripCancelled = 23,
    LocationUpdated = 24,
    DeliveryConfirmed = 25,
    
    // Financial Events
    PaymentInitiated = 30,
    PaymentCompleted = 31,
    PaymentFailed = 32,
    EscrowCreated = 33,
    EscrowReleased = 34,
    DisputeCreated = 35,
    DisputeResolved = 36,
    CommissionCalculated = 37,
    SettlementProcessed = 38,
    
    // Data Management Events
    DataExport = 40,
    DataImport = 41,
    DataDeletion = 42,
    DataBackup = 43,
    DataRestore = 44,
    DocumentUploaded = 45,
    DocumentDeleted = 46,
    
    // System Events
    SystemStartup = 50,
    SystemShutdown = 51,
    ConfigurationChanged = 52,
    SecurityPolicyUpdated = 53,
    
    // Compliance Events
    ComplianceReportGenerated = 60,
    RegulatorySubmission = 61,
    AuditTrailRequested = 62,
    DataRetentionApplied = 63,
    PrivacyPolicyAccepted = 64,
    
    // Security Events
    UnauthorizedAccess = 70,
    SuspiciousActivity = 71,
    SecurityBreach = 72,
    EncryptionKeyRotated = 73,
    
    // Rating and Review Events
    RatingSubmitted = 80,
    ReviewCreated = 81,
    ReviewUpdated = 82,
    PreferredProviderAdded = 83,
    PreferredProviderRemoved = 84,
    ServiceIssueReported = 85
}
