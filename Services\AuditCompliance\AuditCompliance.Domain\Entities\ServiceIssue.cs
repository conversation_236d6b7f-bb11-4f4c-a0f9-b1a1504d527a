using Shared.Domain.Common;
using AuditCompliance.Domain.Enums;

namespace AuditCompliance.Domain.Entities;

/// <summary>
/// Service issue reported by shippers
/// </summary>
public class ServiceIssue : BaseEntity
{
    public Guid ServiceProviderRatingId { get; private set; }
    public ServiceIssueType IssueType { get; private set; }
    public IssuePriority Priority { get; private set; }
    public string Description { get; private set; }
    public string? Evidence { get; private set; }
    public IssueResolutionStatus Status { get; private set; }
    public DateTime ReportedAt { get; private set; }
    public DateTime? AcknowledgedAt { get; private set; }
    public DateTime? ResolvedAt { get; private set; }
    public string? Resolution { get; private set; }
    public Guid? ResolvedBy { get; private set; }
    public string? ResolvedByName { get; private set; }

    // Navigation property
    public ServiceProviderRating ServiceProviderRating { get; private set; } = null!;

    private ServiceIssue() 
    {
        Description = string.Empty;
    }

    public ServiceIssue(
        Guid serviceProviderRatingId,
        ServiceIssueType issueType,
        IssuePriority priority,
        string description,
        string? evidence = null)
    {
        if (string.IsNullOrWhiteSpace(description))
            throw new ArgumentException("Description cannot be empty", nameof(description));

        ServiceProviderRatingId = serviceProviderRatingId;
        IssueType = issueType;
        Priority = priority;
        Description = description;
        Evidence = evidence;
        Status = IssueResolutionStatus.Reported;
        ReportedAt = DateTime.UtcNow;
    }

    public void Acknowledge()
    {
        if (Status != IssueResolutionStatus.Reported)
            throw new InvalidOperationException("Can only acknowledge reported issues");

        Status = IssueResolutionStatus.Acknowledged;
        AcknowledgedAt = DateTime.UtcNow;
        SetUpdatedAt();
    }

    public void StartResolution()
    {
        if (Status != IssueResolutionStatus.Acknowledged)
            throw new InvalidOperationException("Can only start resolution on acknowledged issues");

        Status = IssueResolutionStatus.InProgress;
        SetUpdatedAt();
    }

    public void Resolve(string resolution, Guid resolvedBy, string resolvedByName)
    {
        if (Status != IssueResolutionStatus.InProgress)
            throw new InvalidOperationException("Can only resolve issues that are in progress");

        if (string.IsNullOrWhiteSpace(resolution))
            throw new ArgumentException("Resolution cannot be empty", nameof(resolution));

        if (string.IsNullOrWhiteSpace(resolvedByName))
            throw new ArgumentException("Resolved by name cannot be empty", nameof(resolvedByName));

        Status = IssueResolutionStatus.Resolved;
        Resolution = resolution;
        ResolvedBy = resolvedBy;
        ResolvedByName = resolvedByName;
        ResolvedAt = DateTime.UtcNow;
        SetUpdatedAt();
    }

    public void Close()
    {
        if (Status != IssueResolutionStatus.Resolved)
            throw new InvalidOperationException("Can only close resolved issues");

        Status = IssueResolutionStatus.Closed;
        SetUpdatedAt();
    }

    public void Escalate()
    {
        if (Status == IssueResolutionStatus.Closed)
            throw new InvalidOperationException("Cannot escalate closed issues");

        Status = IssueResolutionStatus.Escalated;
        SetUpdatedAt();
    }

    public string GetIssueTypeName()
    {
        return IssueType switch
        {
            ServiceIssueType.DelayedDelivery => "Delayed Delivery",
            ServiceIssueType.DamagedGoods => "Damaged Goods",
            ServiceIssueType.PoorCommunication => "Poor Communication",
            ServiceIssueType.UnprofessionalBehavior => "Unprofessional Behavior",
            ServiceIssueType.IncorrectDocumentation => "Incorrect Documentation",
            ServiceIssueType.SafetyConcerns => "Safety Concerns",
            ServiceIssueType.PricingDispute => "Pricing Dispute",
            ServiceIssueType.ServiceNotProvided => "Service Not Provided",
            ServiceIssueType.QualityIssues => "Quality Issues",
            ServiceIssueType.Other => "Other",
            _ => "Unknown Issue Type"
        };
    }

    public bool IsHighPriority()
    {
        return Priority >= IssuePriority.High;
    }

    public TimeSpan GetResolutionTime()
    {
        if (ResolvedAt.HasValue)
            return ResolvedAt.Value - ReportedAt;
        
        return DateTime.UtcNow - ReportedAt;
    }
}
