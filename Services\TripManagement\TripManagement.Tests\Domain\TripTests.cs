using FluentAssertions;
using TripManagement.Domain.Entities;
using TripManagement.Domain.Enums;
using TripManagement.Domain.ValueObjects;
using Xunit;

namespace TripManagement.Tests.Domain;

public class TripTests
{
    private readonly Guid _orderId = Guid.NewGuid();
    private readonly Guid _carrierId = Guid.NewGuid();
    private readonly Route _testRoute;

    public TripTests()
    {
        var startLocation = new Location(40.7128, -74.0060, DateTime.UtcNow, address: "New York, NY");
        var endLocation = new Location(34.0522, -118.2437, DateTime.UtcNow, address: "Los Angeles, CA");
        _testRoute = new Route(startLocation, endLocation);
    }

    [Fact]
    public void Trip_Creation_Should_Set_Initial_Properties()
    {
        // Arrange
        var estimatedStartTime = DateTime.UtcNow.AddHours(1);
        var estimatedEndTime = DateTime.UtcNow.AddHours(10);
        var specialInstructions = "Handle with care";

        // Act
        var trip = new Trip(
            _orderId,
            _carrierId,
            _testRoute,
            estimatedStartTime,
            estimatedEndTime,
            specialInstructions,
            isUrgent: true,
            estimatedDistanceKm: 1000m);

        // Assert
        trip.OrderId.Should().Be(_orderId);
        trip.CarrierId.Should().Be(_carrierId);
        trip.Status.Should().Be(TripStatus.Created);
        trip.EstimatedStartTime.Should().Be(estimatedStartTime);
        trip.EstimatedEndTime.Should().Be(estimatedEndTime);
        trip.SpecialInstructions.Should().Be(specialInstructions);
        trip.IsUrgent.Should().BeTrue();
        trip.EstimatedDistanceKm.Should().Be(1000m);
        trip.TripNumber.Should().StartWith("TRP-");
        trip.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
    }

    [Fact]
    public void Trip_Creation_Should_Generate_Unique_Trip_Numbers()
    {
        // Arrange & Act
        var trip1 = new Trip(_orderId, _carrierId, _testRoute, DateTime.UtcNow.AddHours(1), DateTime.UtcNow.AddHours(10));
        var trip2 = new Trip(Guid.NewGuid(), _carrierId, _testRoute, DateTime.UtcNow.AddHours(1), DateTime.UtcNow.AddHours(10));

        // Assert
        trip1.TripNumber.Should().NotBe(trip2.TripNumber);
    }

    [Fact]
    public void AssignDriverAndVehicle_Should_Update_Status_And_Properties()
    {
        // Arrange
        var trip = CreateTestTrip();
        var driverId = Guid.NewGuid();
        var vehicleId = Guid.NewGuid();

        // Act
        trip.AssignDriverAndVehicle(driverId, vehicleId);

        // Assert
        trip.DriverId.Should().Be(driverId);
        trip.VehicleId.Should().Be(vehicleId);
        trip.Status.Should().Be(TripStatus.Assigned);
        trip.AssignedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
    }

    [Fact]
    public void AssignDriverAndVehicle_Should_Throw_When_Trip_Not_Created()
    {
        // Arrange
        var trip = CreateTestTrip();
        trip.AssignDriverAndVehicle(Guid.NewGuid(), Guid.NewGuid());
        var newDriverId = Guid.NewGuid();
        var newVehicleId = Guid.NewGuid();

        // Act & Assert
        var action = () => trip.AssignDriverAndVehicle(newDriverId, newVehicleId);
        action.Should().Throw<InvalidOperationException>()
            .WithMessage("Can only assign driver and vehicle to created trips");
    }

    [Fact]
    public void Start_Should_Update_Status_And_StartedAt()
    {
        // Arrange
        var trip = CreateTestTrip();
        trip.AssignDriverAndVehicle(Guid.NewGuid(), Guid.NewGuid());

        // Act
        trip.Start();

        // Assert
        trip.Status.Should().Be(TripStatus.InProgress);
        trip.StartedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
    }

    [Fact]
    public void Start_Should_Throw_When_Trip_Not_Assigned()
    {
        // Arrange
        var trip = CreateTestTrip();

        // Act & Assert
        var action = () => trip.Start();
        action.Should().Throw<InvalidOperationException>()
            .WithMessage("Can only start assigned trips");
    }

    [Fact]
    public void Start_Should_Throw_When_Driver_Or_Vehicle_Not_Assigned()
    {
        // Arrange
        var trip = CreateTestTrip();
        // Don't assign driver and vehicle

        // Act & Assert
        var action = () => trip.Start();
        action.Should().Throw<InvalidOperationException>();
    }

    [Fact]
    public void Complete_Should_Update_Status_And_CompletedAt()
    {
        // Arrange
        var trip = CreateTestTrip();
        trip.AssignDriverAndVehicle(Guid.NewGuid(), Guid.NewGuid());
        trip.Start();

        // Act
        trip.Complete();

        // Assert
        trip.Status.Should().Be(TripStatus.Completed);
        trip.CompletedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
    }

    [Fact]
    public void Complete_Should_Throw_When_Trip_Not_InProgress()
    {
        // Arrange
        var trip = CreateTestTrip();

        // Act & Assert
        var action = () => trip.Complete();
        action.Should().Throw<InvalidOperationException>()
            .WithMessage("Can only complete in-progress trips");
    }

    [Fact]
    public void Cancel_Should_Update_Status_And_Properties()
    {
        // Arrange
        var trip = CreateTestTrip();
        var reason = "Customer cancelled";

        // Act
        trip.Cancel(reason);

        // Assert
        trip.Status.Should().Be(TripStatus.Cancelled);
        trip.CancelledAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
        trip.CancellationReason.Should().Be(reason);
    }

    [Fact]
    public void Cancel_Should_Throw_When_Trip_Already_Completed()
    {
        // Arrange
        var trip = CreateTestTrip();
        trip.AssignDriverAndVehicle(Guid.NewGuid(), Guid.NewGuid());
        trip.Start();
        trip.Complete();

        // Act & Assert
        var action = () => trip.Cancel("Test reason");
        action.Should().Throw<InvalidOperationException>()
            .WithMessage("Cannot cancel completed trips");
    }

    [Fact]
    public void UpdateLocation_Should_Add_Location_Update()
    {
        // Arrange
        var trip = CreateTestTrip();
        trip.AssignDriverAndVehicle(Guid.NewGuid(), Guid.NewGuid());
        trip.Start();
        var location = new Location(40.7589, -73.9851, DateTime.UtcNow);

        // Act
        trip.UpdateLocation(location);

        // Assert
        trip.LocationUpdates.Should().HaveCount(1);
        trip.GetCurrentLocation().Should().NotBeNull();
        trip.GetCurrentLocation()!.Latitude.Should().Be(location.Latitude);
        trip.GetCurrentLocation()!.Longitude.Should().Be(location.Longitude);
    }

    [Fact]
    public void UpdateLocation_Should_Throw_When_Trip_Not_InProgress()
    {
        // Arrange
        var trip = CreateTestTrip();
        var location = new Location(40.7589, -73.9851, DateTime.UtcNow);

        // Act & Assert
        var action = () => trip.UpdateLocation(location);
        action.Should().Throw<InvalidOperationException>()
            .WithMessage("Can only update location for in-progress trips");
    }

    [Fact]
    public void AddException_Should_Add_Exception_And_Update_Status()
    {
        // Arrange
        var trip = CreateTestTrip();
        trip.AssignDriverAndVehicle(Guid.NewGuid(), Guid.NewGuid());
        trip.Start();

        // Act
        trip.AddException(ExceptionType.Delay, "Traffic jam");

        // Assert
        trip.Exceptions.Should().HaveCount(1);
        trip.Status.Should().Be(TripStatus.Exception);
        trip.Exceptions.First().ExceptionType.Should().Be(ExceptionType.Delay);
        trip.Exceptions.First().Description.Should().Be("Traffic jam");
    }

    [Fact]
    public void IsDelayed_Should_Return_True_When_Trip_Is_Delayed()
    {
        // Arrange
        var startTime = DateTime.UtcNow.AddMinutes(-60); // Past start time
        var endTime = DateTime.UtcNow.AddMinutes(-30);   // Past end time
        var trip = CreateTestTrip(estimatedStartTime: startTime, estimatedEndTime: endTime);

        // Act & Assert
        trip.IsDelayed().Should().BeTrue();
    }

    [Fact]
    public void IsDelayed_Should_Return_False_When_Trip_Is_OnTime()
    {
        // Arrange
        var trip = CreateTestTrip(estimatedEndTime: DateTime.UtcNow.AddHours(2)); // Future end time

        // Act & Assert
        trip.IsDelayed().Should().BeFalse();
    }

    [Fact]
    public void GetActualDuration_Should_Return_Correct_Duration()
    {
        // Arrange
        var trip = CreateTestTrip();
        trip.AssignDriverAndVehicle(Guid.NewGuid(), Guid.NewGuid());
        trip.Start();

        // Simulate some time passing
        Thread.Sleep(100);

        // Act
        var duration = trip.GetActualDuration();

        // Assert
        duration.Should().NotBeNull();
        duration!.Value.Should().BeGreaterThan(TimeSpan.Zero);
    }

    private Trip CreateTestTrip(DateTime? estimatedStartTime = null, DateTime? estimatedEndTime = null)
    {
        return new Trip(
            _orderId,
            _carrierId,
            _testRoute,
            estimatedStartTime ?? DateTime.UtcNow.AddHours(1),
            estimatedEndTime ?? DateTime.UtcNow.AddHours(10));
    }
}
