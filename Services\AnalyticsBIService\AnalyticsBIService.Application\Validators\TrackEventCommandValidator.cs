using AnalyticsBIService.Application.Commands.TrackEvent;
using FluentValidation;
using Shared.Domain.Common;

namespace AnalyticsBIService.Application.Validators;

/// <summary>
/// Validator for TrackEventCommand
/// </summary>
public class TrackEventCommandValidator : AbstractValidator<TrackEventCommand>
{
    public TrackEventCommandValidator()
    {
        RuleFor(x => x.EventName)
            .NotEmpty()
            .WithMessage("Event name is required")
            .MaximumLength(200)
            .WithMessage("Event name must not exceed 200 characters");

        RuleFor(x => x.EventType)
            .IsInEnum()
            .WithMessage("Invalid event type");

        RuleFor(x => x.DataSource)
            .IsInEnum()
            .WithMessage("Invalid data source");

        RuleFor(x => x.EntityType)
            .MaximumLength(100)
            .WithMessage("Entity type must not exceed 100 characters")
            .When(x => !string.IsNullOrEmpty(x.EntityType));

        RuleFor(x => x.SessionId)
            .MaximumLength(100)
            .WithMessage("Session ID must not exceed 100 characters")
            .When(x => !string.IsNullOrEmpty(x.SessionId));

        RuleFor(x => x.IpAddress)
            .MaximumLength(45)
            .WithMessage("IP address must not exceed 45 characters")
            .When(x => !string.IsNullOrEmpty(x.IpAddress));

        RuleFor(x => x.UserAgent)
            .MaximumLength(500)
            .WithMessage("User agent must not exceed 500 characters")
            .When(x => !string.IsNullOrEmpty(x.UserAgent));

        RuleFor(x => x.Properties)
            .Must(BeValidJsonSize)
            .WithMessage("Properties JSON size must not exceed 64KB")
            .When(x => x.Properties != null);

        RuleFor(x => x.Metadata)
            .Must(BeValidJsonSize)
            .WithMessage("Metadata JSON size must not exceed 64KB")
            .When(x => x.Metadata != null);
    }

    private static bool BeValidJsonSize(Dictionary<string, object>? properties)
    {
        if (properties == null) return true;

        try
        {
            var json = System.Text.Json.JsonSerializer.Serialize(properties);
            return json.Length <= 65536; // 64KB limit
        }
        catch
        {
            return false;
        }
    }
}

/// <summary>
/// Validator for TrackUserActivityCommand
/// </summary>
public class TrackUserActivityCommandValidator : AbstractValidator<TrackUserActivityCommand>
{
    public TrackUserActivityCommandValidator()
    {
        RuleFor(x => x.EventName)
            .NotEmpty()
            .WithMessage("Event name is required")
            .MaximumLength(200)
            .WithMessage("Event name must not exceed 200 characters");

        RuleFor(x => x.UserId)
            .NotEmpty()
            .WithMessage("User ID is required");

        RuleFor(x => x.UserType)
            .IsInEnum()
            .WithMessage("Invalid user type");

        RuleFor(x => x.SessionId)
            .MaximumLength(100)
            .WithMessage("Session ID must not exceed 100 characters")
            .When(x => !string.IsNullOrEmpty(x.SessionId));

        RuleFor(x => x.Properties)
            .Must(BeValidJsonSize)
            .WithMessage("Properties JSON size must not exceed 64KB")
            .When(x => x.Properties != null);
    }

    private static bool BeValidJsonSize(Dictionary<string, object>? properties)
    {
        if (properties == null) return true;

        try
        {
            var json = System.Text.Json.JsonSerializer.Serialize(properties);
            return json.Length <= 65536; // 64KB limit
        }
        catch
        {
            return false;
        }
    }
}

/// <summary>
/// Validator for TrackBusinessTransactionCommand
/// </summary>
public class TrackBusinessTransactionCommandValidator : AbstractValidator<TrackBusinessTransactionCommand>
{
    public TrackBusinessTransactionCommandValidator()
    {
        RuleFor(x => x.EventName)
            .NotEmpty()
            .WithMessage("Event name is required")
            .MaximumLength(200)
            .WithMessage("Event name must not exceed 200 characters");

        RuleFor(x => x.DataSource)
            .IsInEnum()
            .WithMessage("Invalid data source");

        RuleFor(x => x.EntityId)
            .NotEmpty()
            .WithMessage("Entity ID is required");

        RuleFor(x => x.EntityType)
            .NotEmpty()
            .WithMessage("Entity type is required")
            .MaximumLength(100)
            .WithMessage("Entity type must not exceed 100 characters");

        RuleFor(x => x.Properties)
            .Must(BeValidJsonSize)
            .WithMessage("Properties JSON size must not exceed 64KB")
            .When(x => x.Properties != null);
    }

    private static bool BeValidJsonSize(Dictionary<string, object>? properties)
    {
        if (properties == null) return true;

        try
        {
            var json = System.Text.Json.JsonSerializer.Serialize(properties);
            return json.Length <= 65536; // 64KB limit
        }
        catch
        {
            return false;
        }
    }
}

/// <summary>
/// Validator for CalculateMetricCommand
/// </summary>
public class CalculateMetricCommandValidator : AbstractValidator<CalculateMetricCommand>
{
    public CalculateMetricCommandValidator()
    {
        RuleFor(x => x.MetricName)
            .NotEmpty()
            .WithMessage("Metric name is required")
            .MaximumLength(200)
            .WithMessage("Metric name must not exceed 200 characters");

        RuleFor(x => x.Description)
            .NotEmpty()
            .WithMessage("Description is required")
            .MaximumLength(1000)
            .WithMessage("Description must not exceed 1000 characters");

        RuleFor(x => x.Type)
            .IsInEnum()
            .WithMessage("Invalid metric type");

        RuleFor(x => x.Category)
            .IsInEnum()
            .WithMessage("Invalid KPI category");

        RuleFor(x => x.Value)
            .GreaterThanOrEqualTo(0)
            .WithMessage("Metric value must be non-negative")
            .When(x => x.Type != Domain.Enums.MetricType.Gauge); // Gauge can be negative

        RuleFor(x => x.Unit)
            .NotEmpty()
            .WithMessage("Unit is required")
            .MaximumLength(50)
            .WithMessage("Unit must not exceed 50 characters");

        RuleFor(x => x.DataSource)
            .IsInEnum()
            .WithMessage("Invalid data source");

        RuleFor(x => x.Period)
            .IsInEnum()
            .WithMessage("Invalid time period");

        RuleFor(x => x.PeriodStart)
            .LessThan(x => x.PeriodEnd)
            .WithMessage("Period start must be before period end")
            .When(x => x.PeriodStart.HasValue && x.PeriodEnd.HasValue);

        RuleFor(x => x.TargetValue)
            .GreaterThan(0)
            .WithMessage("Target value must be positive")
            .When(x => x.TargetValue.HasValue);

        RuleFor(x => x.TargetUnit)
            .MaximumLength(50)
            .WithMessage("Target unit must not exceed 50 characters")
            .When(x => !string.IsNullOrEmpty(x.TargetUnit));

        RuleFor(x => x.WarningThreshold)
            .GreaterThan(0)
            .WithMessage("Warning threshold must be positive")
            .When(x => x.WarningThreshold.HasValue);

        RuleFor(x => x.CriticalThreshold)
            .GreaterThan(0)
            .WithMessage("Critical threshold must be positive")
            .When(x => x.CriticalThreshold.HasValue);

        RuleFor(x => x.Tags)
            .Must(BeValidTagsSize)
            .WithMessage("Tags JSON size must not exceed 32KB")
            .When(x => x.Tags != null);

        RuleFor(x => x.Metadata)
            .Must(BeValidMetadataSize)
            .WithMessage("Metadata JSON size must not exceed 64KB")
            .When(x => x.Metadata != null);
    }

    private static bool BeValidTagsSize(Dictionary<string, string>? tags)
    {
        if (tags == null) return true;

        try
        {
            var json = System.Text.Json.JsonSerializer.Serialize(tags);
            return json.Length <= 32768; // 32KB limit
        }
        catch
        {
            return false;
        }
    }

    private static bool BeValidMetadataSize(Dictionary<string, object>? metadata)
    {
        if (metadata == null) return true;

        try
        {
            var json = System.Text.Json.JsonSerializer.Serialize(metadata);
            return json.Length <= 65536; // 64KB limit
        }
        catch
        {
            return false;
        }
    }
}

/// <summary>
/// Validator for UpdateMetricCommand
/// </summary>
public class UpdateMetricCommandValidator : AbstractValidator<UpdateMetricCommand>
{
    public UpdateMetricCommandValidator()
    {
        RuleFor(x => x.MetricId)
            .NotEmpty()
            .WithMessage("Metric ID is required");

        RuleFor(x => x.NewValue)
            .GreaterThanOrEqualTo(0)
            .WithMessage("New value must be non-negative");

        RuleFor(x => x.Metadata)
            .Must(BeValidMetadataSize)
            .WithMessage("Metadata JSON size must not exceed 64KB")
            .When(x => x.Metadata != null);
    }

    private static bool BeValidMetadataSize(Dictionary<string, object>? metadata)
    {
        if (metadata == null) return true;

        try
        {
            var json = System.Text.Json.JsonSerializer.Serialize(metadata);
            return json.Length <= 65536; // 64KB limit
        }
        catch
        {
            return false;
        }
    }
}

/// <summary>
/// Validator for SetMetricTargetCommand
/// </summary>
public class SetMetricTargetCommandValidator : AbstractValidator<SetMetricTargetCommand>
{
    public SetMetricTargetCommandValidator()
    {
        RuleFor(x => x.MetricId)
            .NotEmpty()
            .WithMessage("Metric ID is required");

        RuleFor(x => x.TargetValue)
            .GreaterThan(0)
            .WithMessage("Target value must be positive");

        RuleFor(x => x.Unit)
            .NotEmpty()
            .WithMessage("Unit is required")
            .MaximumLength(50)
            .WithMessage("Unit must not exceed 50 characters");

        RuleFor(x => x.WarningThreshold)
            .GreaterThan(0)
            .WithMessage("Warning threshold must be positive")
            .When(x => x.WarningThreshold.HasValue);

        RuleFor(x => x.CriticalThreshold)
            .GreaterThan(0)
            .WithMessage("Critical threshold must be positive")
            .When(x => x.CriticalThreshold.HasValue);
    }
}

