using TripManagement.Domain.Enums;
using TripManagement.Domain.ValueObjects;
using MediatR;

namespace TripManagement.Application.Commands.DelayAlert;

/// <summary>
/// Command to create a delay alert
/// </summary>
public class CreateDelayAlertCommand : IRequest<CreateDelayAlertResult>
{
    public Guid TripId { get; set; }
    public Guid? TripStopId { get; set; }
    public DelayAlertType AlertType { get; set; }
    public TimeSpan DelayDuration { get; set; }
    public DateTime ScheduledTime { get; set; }
    public DateTime? EstimatedTime { get; set; }
    public DelayReason DelayReason { get; set; } = DelayReason.Unknown;
    public string? DelayReasonDescription { get; set; }
    public string? CustomTitle { get; set; }
    public string? CustomDescription { get; set; }
    public Guid CreatedByUserId { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Result of creating a delay alert
/// </summary>
public class CreateDelayAlertResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public Guid? DelayAlertId { get; set; }
    public DelayAlertSeverity? Severity { get; set; }
    public DateTime CreatedAt { get; set; }
    public bool NotificationSent { get; set; }
    public List<string> ValidationErrors { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Command to acknowledge a delay alert
/// </summary>
public class AcknowledgeDelayAlertCommand : IRequest<AcknowledgeDelayAlertResult>
{
    public Guid DelayAlertId { get; set; }
    public Guid AcknowledgedByUserId { get; set; }
    public string? Notes { get; set; }
    public DateTime? AcknowledgedAt { get; set; }
}

/// <summary>
/// Result of acknowledging a delay alert
/// </summary>
public class AcknowledgeDelayAlertResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime? AcknowledgedAt { get; set; }
    public bool NotificationSent { get; set; }
}

/// <summary>
/// Command to escalate a delay alert
/// </summary>
public class EscalateDelayAlertCommand : IRequest<EscalateDelayAlertResult>
{
    public Guid DelayAlertId { get; set; }
    public string Reason { get; set; } = string.Empty;
    public Guid EscalatedByUserId { get; set; }
    public bool ForceEscalation { get; set; } = false;
}

/// <summary>
/// Result of escalating a delay alert
/// </summary>
public class EscalateDelayAlertResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public int? NewEscalationLevel { get; set; }
    public DateTime? EscalatedAt { get; set; }
    public bool NotificationSent { get; set; }
}

/// <summary>
/// Command to resolve a delay alert
/// </summary>
public class ResolveDelayAlertCommand : IRequest<ResolveDelayAlertResult>
{
    public Guid DelayAlertId { get; set; }
    public string ResolutionNotes { get; set; } = string.Empty;
    public DateTime? ActualTime { get; set; }
    public Guid ResolvedByUserId { get; set; }
    public Dictionary<string, object> ResolutionMetadata { get; set; } = new();
}

/// <summary>
/// Result of resolving a delay alert
/// </summary>
public class ResolveDelayAlertResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime? ResolvedAt { get; set; }
    public bool NotificationSent { get; set; }
}

/// <summary>
/// Command to update delay alert configuration for a transport company
/// </summary>
public class UpdateDelayAlertConfigurationCommand : IRequest<UpdateDelayAlertConfigurationResult>
{
    public Guid TransportCompanyId { get; set; }
    public bool IsEnabled { get; set; } = true;
    public List<DelayThresholdDto> DelayThresholds { get; set; } = new();
    public List<int> EscalationIntervalMinutes { get; set; } = new();
    public int MaxEscalationLevel { get; set; } = 3;
    public List<string> NotificationChannels { get; set; } = new();
    public List<string> EscalationRecipients { get; set; } = new();
    public bool AutoAcknowledge { get; set; } = false;
    public int? AutoAcknowledgeDelayMinutes { get; set; }
    public bool AutoResolve { get; set; } = false;
    public int? AutoResolveDelayMinutes { get; set; }
    public int? AlertExpiryHours { get; set; }
    public Dictionary<string, object> CustomSettings { get; set; } = new();
    public Guid UpdatedByUserId { get; set; }
}

/// <summary>
/// DTO for delay threshold configuration
/// </summary>
public class DelayThresholdDto
{
    public DelayAlertType AlertType { get; set; }
    public DelayAlertSeverity Severity { get; set; }
    public int ThresholdMinutes { get; set; }
    public bool IsEnabled { get; set; } = true;
}

/// <summary>
/// Result of updating delay alert configuration
/// </summary>
public class UpdateDelayAlertConfigurationResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime UpdatedAt { get; set; }
    public List<string> ValidationErrors { get; set; } = new();
    public Dictionary<string, object> ConfigurationSummary { get; set; } = new();
}

/// <summary>
/// Command to process automatic delay alert escalations
/// </summary>
public class ProcessDelayAlertEscalationsCommand : IRequest<ProcessDelayAlertEscalationsResult>
{
    public Guid? TransportCompanyId { get; set; } // If null, process for all companies
    public DateTime? ProcessingTime { get; set; } // If null, use current time
}

/// <summary>
/// Result of processing delay alert escalations
/// </summary>
public class ProcessDelayAlertEscalationsResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public int AlertsProcessed { get; set; }
    public int AlertsEscalated { get; set; }
    public int AlertsExpired { get; set; }
    public DateTime ProcessedAt { get; set; }
    public List<Guid> EscalatedAlertIds { get; set; } = new();
    public List<Guid> ExpiredAlertIds { get; set; } = new();
    public Dictionary<string, object> ProcessingMetadata { get; set; } = new();
}

/// <summary>
/// Command to update delay alert impact assessment
/// </summary>
public class UpdateDelayAlertImpactCommand : IRequest<UpdateDelayAlertImpactResult>
{
    public Guid DelayAlertId { get; set; }
    public decimal FinancialImpact { get; set; }
    public string CustomerImpact { get; set; } = string.Empty;
    public string OperationalImpact { get; set; } = string.Empty;
    public int AffectedShipments { get; set; }
    public int AffectedCustomers { get; set; }
    public List<string> MitigationActions { get; set; } = new();
    public Guid AssessedByUserId { get; set; }
}

/// <summary>
/// Result of updating delay alert impact assessment
/// </summary>
public class UpdateDelayAlertImpactResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime AssessedAt { get; set; }
}

/// <summary>
/// Command to send delay alert notifications
/// </summary>
public class SendDelayAlertNotificationCommand : IRequest<SendDelayAlertNotificationResult>
{
    public Guid DelayAlertId { get; set; }
    public List<string> Channels { get; set; } = new(); // Email, SMS, Push
    public List<string> Recipients { get; set; } = new();
    public string? CustomMessage { get; set; }
    public bool IsUrgent { get; set; } = false;
    public Guid SentByUserId { get; set; }
}

/// <summary>
/// Result of sending delay alert notification
/// </summary>
public class SendDelayAlertNotificationResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public int NotificationsSent { get; set; }
    public int NotificationsFailed { get; set; }
    public List<string> SuccessfulChannels { get; set; } = new();
    public List<string> FailedChannels { get; set; } = new();
    public DateTime SentAt { get; set; }
}

/// <summary>
/// Command to bulk acknowledge delay alerts
/// </summary>
public class BulkAcknowledgeDelayAlertsCommand : IRequest<BulkAcknowledgeDelayAlertsResult>
{
    public List<Guid> DelayAlertIds { get; set; } = new();
    public Guid AcknowledgedByUserId { get; set; }
    public string? Notes { get; set; }
    public DateTime? AcknowledgedAt { get; set; }
}

/// <summary>
/// Result of bulk acknowledging delay alerts
/// </summary>
public class BulkAcknowledgeDelayAlertsResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public int TotalAlerts { get; set; }
    public int SuccessfulAcknowledgments { get; set; }
    public int FailedAcknowledgments { get; set; }
    public List<Guid> SuccessfulAlertIds { get; set; } = new();
    public List<Guid> FailedAlertIds { get; set; } = new();
    public DateTime ProcessedAt { get; set; }
}
