using MediatR;
using UserManagement.Domain.Entities;

namespace UserManagement.Application.Documents.Commands.PreviewOcrData;

public class PreviewOcrDataCommand : IRequest<PreviewOcrDataResponse>
{
    public string FilePath { get; set; } = string.Empty;
    public DocumentType DocumentType { get; set; }
    public bool IncludeRawText { get; set; } = false;
    public decimal MinimumConfidenceThreshold { get; set; } = 0.5m;
}

public class PreviewOcrDataResponse
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public Dictionary<string, object> ExtractedData { get; set; } = new();
    public Dictionary<string, FormFieldMapping> FormFieldMappings { get; set; } = new();
    public decimal ConfidenceScore { get; set; }
    public string? RawText { get; set; }
    public List<string> ValidationWarnings { get; set; } = new();
    public List<string> Suggestions { get; set; } = new();
    public DateTime ProcessedAt { get; set; }
}

public class FormFieldMapping
{
    public string FieldName { get; set; } = string.Empty;
    public string ExtractedValue { get; set; } = string.Empty;
    public decimal Confidence { get; set; }
    public bool RequiresVerification { get; set; }
    public string? SuggestedCorrection { get; set; }
    public List<string> AlternativeValues { get; set; } = new();
}
