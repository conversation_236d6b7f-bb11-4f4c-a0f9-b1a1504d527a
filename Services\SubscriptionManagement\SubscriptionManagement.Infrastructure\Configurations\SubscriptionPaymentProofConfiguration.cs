using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Infrastructure.Configurations
{
    public class SubscriptionPaymentProofConfiguration : IEntityTypeConfiguration<SubscriptionPaymentProof>
    {
        public void Configure(EntityTypeBuilder<SubscriptionPaymentProof> builder)
        {
            builder.ToTable("SubscriptionPaymentProofs");

            builder.HasKey(p => p.Id);

            builder.Property(p => p.Id)
                .IsRequired()
                .ValueGeneratedNever();

            builder.Property(p => p.SubscriptionId)
                .IsRequired();

            builder.Property(p => p.UserId)
                .IsRequired();

            // Configure Money value object
            builder.OwnsOne(p => p.Amount, money =>
            {
                money.Property(m => m.Amount)
                    .HasColumnName("Amount")
                    .HasColumnType("decimal(18,2)")
                    .IsRequired();

                money.Property(m => m.Currency)
                    .HasColumnName("Currency")
                    .HasMaxLength(3)
                    .IsRequired();
            });

            builder.Property(p => p.PaymentDate)
                .IsRequired()
                .HasColumnType("datetime2");

            builder.Property(p => p.ProofImageUrl)
                .IsRequired()
                .HasMaxLength(2000);

            builder.Property(p => p.Notes)
                .HasMaxLength(2000);

            builder.Property(p => p.Status)
                .IsRequired()
                .HasConversion<int>();

            builder.Property(p => p.PaymentMethod)
                .HasMaxLength(100);

            builder.Property(p => p.TransactionReference)
                .HasMaxLength(200);

            builder.Property(p => p.VerifiedByUserId);

            builder.Property(p => p.VerifiedAt)
                .HasColumnType("datetime2");

            builder.Property(p => p.VerificationNotes)
                .HasMaxLength(2000);

            builder.Property(p => p.RejectionReason)
                .HasMaxLength(2000);

            builder.Property(p => p.CreatedAt)
                .IsRequired()
                .HasColumnType("datetime2");

            builder.Property(p => p.UpdatedAt)
                .HasColumnType("datetime2");

            // Configure relationships
            builder.HasOne(p => p.Subscription)
                .WithMany()
                .HasForeignKey(p => p.SubscriptionId)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure indexes
            builder.HasIndex(p => p.SubscriptionId)
                .HasDatabaseName("IX_SubscriptionPaymentProofs_SubscriptionId");

            builder.HasIndex(p => p.UserId)
                .HasDatabaseName("IX_SubscriptionPaymentProofs_UserId");

            builder.HasIndex(p => p.Status)
                .HasDatabaseName("IX_SubscriptionPaymentProofs_Status");

            builder.HasIndex(p => p.PaymentDate)
                .HasDatabaseName("IX_SubscriptionPaymentProofs_PaymentDate");

            builder.HasIndex(p => p.VerifiedAt)
                .HasDatabaseName("IX_SubscriptionPaymentProofs_VerifiedAt");

            builder.HasIndex(p => p.CreatedAt)
                .HasDatabaseName("IX_SubscriptionPaymentProofs_CreatedAt");

            // Composite indexes for common queries
            builder.HasIndex(p => new { p.Status, p.CreatedAt })
                .HasDatabaseName("IX_SubscriptionPaymentProofs_Status_CreatedAt");

            builder.HasIndex(p => new { p.UserId, p.Status })
                .HasDatabaseName("IX_SubscriptionPaymentProofs_UserId_Status");

            builder.HasIndex(p => new { p.SubscriptionId, p.Status })
                .HasDatabaseName("IX_SubscriptionPaymentProofs_SubscriptionId_Status");
        }
    }
}
