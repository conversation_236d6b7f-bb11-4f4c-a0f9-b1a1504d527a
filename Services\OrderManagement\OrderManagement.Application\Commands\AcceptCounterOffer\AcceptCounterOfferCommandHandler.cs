using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.ValueObjects;

namespace OrderManagement.Application.Commands.AcceptCounterOffer;

public class AcceptCounterOfferCommandHandler : IRequestHandler<AcceptCounterOfferCommand, bool>
{
    private readonly IRfqRepository _rfqRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<AcceptCounterOfferCommandHandler> _logger;

    public AcceptCounterOfferCommandHandler(
        IRfqRepository rfqRepository,
        IUnitOfWork unitOfWork,
        IMapper mapper,
        IMessageBroker messageBroker,
        ILogger<AcceptCounterOfferCommandHandler> logger)
    {
        _rfqRepository = rfqRepository;
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task<bool> Handle(AcceptCounterOfferCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Accepting counter offer {CounterOfferId} in negotiation {NegotiationId} by user {UserId}",
            request.CounterOfferId, request.NegotiationId, request.RequestingUserId);

        try
        {
            // Get the RFQ with negotiations
            var rfq = await _rfqRepository.GetByIdWithNegotiationsAsync(request.NegotiationId, cancellationToken);
            if (rfq == null)
            {
                _logger.LogWarning("RFQ not found for negotiation {NegotiationId}", request.NegotiationId);
                throw new ArgumentException($"RFQ not found for negotiation {request.NegotiationId}");
            }

            // Find the negotiation
            var negotiation = rfq.Negotiations.FirstOrDefault(n => n.Id == request.NegotiationId);
            if (negotiation == null)
            {
                _logger.LogWarning("Negotiation {NegotiationId} not found", request.NegotiationId);
                throw new ArgumentException($"Negotiation {request.NegotiationId} not found");
            }

            // Find the counter offer
            var counterOffer = negotiation.CounterOffers.FirstOrDefault(co => co.Id == request.CounterOfferId);
            if (counterOffer == null)
            {
                _logger.LogWarning("Counter offer {CounterOfferId} not found in negotiation {NegotiationId}",
                    request.CounterOfferId, request.NegotiationId);
                throw new ArgumentException($"Counter offer {request.CounterOfferId} not found");
            }

            // Validate user has permission to accept counter offer
            bool hasPermission = false;
            if (counterOffer.OfferType == OrderManagement.Domain.Entities.CounterOfferType.Broker)
            {
                // Shipper can accept broker's counter offer
                hasPermission = rfq.TransportCompanyId == request.RequestingUserId;
            }
            else if (counterOffer.OfferType == OrderManagement.Domain.Entities.CounterOfferType.Shipper)
            {
                // Broker can accept shipper's counter offer
                hasPermission = negotiation.BrokerId == request.RequestingUserId;
            }

            if (!hasPermission)
            {
                _logger.LogWarning("User {UserId} does not have permission to accept counter offer {CounterOfferId}",
                    request.RequestingUserId, request.CounterOfferId);
                throw new UnauthorizedAccessException("You do not have permission to accept this counter offer");
            }

            // Validate counter offer can be accepted
            if (!counterOffer.IsPending)
            {
                _logger.LogWarning("Cannot accept counter offer {CounterOfferId} with status {Status}",
                    request.CounterOfferId, counterOffer.Status);
                throw new InvalidOperationException($"Cannot accept counter offer with status {counterOffer.Status}");
            }

            // Convert DTO to domain value object
            var finalPrice = new Money(request.FinalPrice.Amount, request.FinalPrice.Currency);

            // Accept the counter offer and complete the negotiation
            negotiation.AcceptCounterOffer(request.CounterOfferId, finalPrice, request.FinalTerms);

            // Save changes
            _rfqRepository.Update(rfq);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Publish integration events
            await PublishIntegrationEvents(rfq, negotiation, counterOffer, request, cancellationToken);

            _logger.LogInformation("Successfully accepted counter offer {CounterOfferId} and completed negotiation {NegotiationId}",
                request.CounterOfferId, request.NegotiationId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error accepting counter offer {CounterOfferId} in negotiation {NegotiationId}",
                request.CounterOfferId, request.NegotiationId);
            throw;
        }
    }

    private async Task PublishIntegrationEvents(
        Domain.Entities.RFQ rfq,
        Domain.Entities.Negotiation negotiation,
        Domain.Entities.CounterOffer counterOffer,
        AcceptCounterOfferCommand request,
        CancellationToken cancellationToken)
    {
        try
        {
            // Publish counter offer accepted event
            await _messageBroker.PublishAsync("counter_offer.accepted", new
            {
                CounterOfferId = request.CounterOfferId,
                NegotiationId = request.NegotiationId,
                RfqId = rfq.Id,
                RfqNumber = rfq.RfqNumber,
                BrokerId = negotiation.BrokerId,
                TransportCompanyId = rfq.TransportCompanyId,
                FinalPrice = new
                {
                    Amount = request.FinalPrice.Amount,
                    Currency = request.FinalPrice.Currency
                },
                FinalTerms = request.FinalTerms,
                AcceptedBy = request.RequestingUserId,
                AcceptedAt = DateTime.UtcNow,
                AcceptanceNotes = request.AcceptanceNotes,
                CreateOrder = request.CreateOrder,
                NotifyStakeholders = request.NotifyStakeholders
            }, cancellationToken);

            // Publish negotiation completed event
            await _messageBroker.PublishAsync("negotiation.completed", new
            {
                NegotiationId = request.NegotiationId,
                RfqId = rfq.Id,
                RfqNumber = rfq.RfqNumber,
                BrokerId = negotiation.BrokerId,
                TransportCompanyId = rfq.TransportCompanyId,
                TotalRounds = negotiation.TotalRounds,
                StartedAt = negotiation.StartedAt,
                CompletedAt = negotiation.CompletedAt,
                OriginalPrice = new
                {
                    Amount = negotiation.OriginalPrice.Amount,
                    Currency = negotiation.OriginalPrice.Currency
                },
                FinalPrice = new
                {
                    Amount = request.FinalPrice.Amount,
                    Currency = request.FinalPrice.Currency
                },
                PriceDifference = request.FinalPrice.Amount - negotiation.OriginalPrice.Amount,
                CompletedBy = request.RequestingUserId
            }, cancellationToken);

            _logger.LogInformation("Successfully published integration events for accepted counter offer {CounterOfferId}",
                request.CounterOfferId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing integration events for counter offer {CounterOfferId}",
                request.CounterOfferId);
            // Don't throw here as the main operation succeeded
        }
    }
}
