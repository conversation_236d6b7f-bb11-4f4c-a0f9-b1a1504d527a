using Xunit;
using FluentAssertions;
using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Enums;
using AnalyticsBIService.Tests.Infrastructure;

namespace AnalyticsBIService.Tests.Unit.Domain;

/// <summary>
/// Unit tests for Alert domain entity
/// </summary>
public class AlertTests
{
    [Fact]
    public void Alert_Constructor_Should_Create_Valid_Alert()
    {
        // Arrange
        var name = "Test Alert";
        var description = "Test Description";
        var severity = AlertSeverity.High;
        var message = "Test alert message";
        var userId = Guid.NewGuid();

        // Act
        var alert = new Alert(name, description, severity, message, userId: userId);

        // Assert
        alert.Should().NotBeNull();
        alert.Id.Should().NotBeEmpty();
        alert.Name.Should().Be(name);
        alert.Description.Should().Be(description);
        alert.Severity.Should().Be(severity);
        alert.Message.Should().Be(message);
        alert.UserId.Should().Be(userId);
        alert.IsActive.Should().BeTrue();
        alert.Status.Should().Be(AlertStatus.Active);
        alert.IsAcknowledged.Should().BeFalse();
        alert.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        alert.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        alert.TriggeredAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void Alert_Constructor_Should_Throw_When_Name_Is_Null()
    {
        // Act & Assert
        var action = () => new Alert(null!, "Description", AlertSeverity.High, "Message", userId: Guid.NewGuid());

        action.Should().Throw<ArgumentNullException>()
            .WithParameterName("name");
    }

    [Fact]
    public void Alert_Constructor_Should_Throw_When_Description_Is_Null()
    {
        // Act & Assert
        var action = () => new Alert("Test Alert", null!, AlertSeverity.High, "Message", userId: Guid.NewGuid());

        action.Should().Throw<ArgumentNullException>()
            .WithParameterName("description");
    }

    [Fact]
    public void Alert_Constructor_Should_Throw_When_Message_Is_Null()
    {
        // Act & Assert
        var action = () => new Alert("Test Alert", "Description", AlertSeverity.High, null!, userId: Guid.NewGuid());

        action.Should().Throw<ArgumentNullException>()
            .WithParameterName("message");
    }

    [Theory]
    [InlineData(AlertSeverity.Low)]
    [InlineData(AlertSeverity.Medium)]
    [InlineData(AlertSeverity.High)]
    [InlineData(AlertSeverity.Critical)]
    public void Alert_Should_Accept_Valid_AlertSeverities(AlertSeverity severity)
    {
        // Act
        var alert = new Alert("Test", "Description", severity, "Message", userId: Guid.NewGuid());

        // Assert
        alert.Severity.Should().Be(severity);
    }

    [Theory]
    [InlineData(AlertType.System)]
    [InlineData(AlertType.Performance)]
    [InlineData(AlertType.Business)]
    [InlineData(AlertType.Security)]
    public void Alert_Should_Accept_Valid_AlertTypes(AlertType alertType)
    {
        // Act
        var alert = new Alert("Test", "Description", AlertSeverity.High, "Message",
            userId: Guid.NewGuid(), alertType: alertType);

        // Assert
        alert.AlertType.Should().Be(alertType);
    }

    [Fact]
    public void Alert_Constructor_Should_Handle_Optional_Parameters()
    {
        // Arrange
        var metricId = Guid.NewGuid();
        var metricName = "CPU Usage";
        var triggerValue = 85.5m;
        var thresholdValue = 80.0m;
        var context = new Dictionary<string, object> { { "server", "web-01" } };
        var userId = Guid.NewGuid();
        var alertType = AlertType.Performance;

        // Act
        var alert = new Alert("High CPU Alert", "CPU usage exceeded threshold", AlertSeverity.High,
            "CPU usage is at 85.5%", metricId, metricName, triggerValue, thresholdValue, context, userId, alertType);

        // Assert
        alert.MetricId.Should().Be(metricId);
        alert.MetricName.Should().Be(metricName);
        alert.TriggerValue.Should().Be(triggerValue);
        alert.ThresholdValue.Should().Be(thresholdValue);
        alert.Context.Should().BeEquivalentTo(context);
        alert.UserId.Should().Be(userId);
        alert.AlertType.Should().Be(alertType);
    }

    [Fact]
    public void Alert_Acknowledge_Should_Update_Acknowledgment_Properties()
    {
        // Arrange
        var alert = TestDataFactory.CreateTestAlert();
        var acknowledgedBy = Guid.NewGuid();

        // Act
        alert.Acknowledge(acknowledgedBy);

        // Assert
        alert.IsAcknowledged.Should().BeTrue();
        alert.AcknowledgedBy.Should().Be(acknowledgedBy);
        alert.AcknowledgedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void Alert_Acknowledge_Should_Update_Timestamp()
    {
        // Arrange
        var alert = TestDataFactory.CreateTestAlert();
        var originalUpdatedAt = alert.UpdatedAt;

        // Wait a small amount to ensure timestamp difference
        Thread.Sleep(10);

        // Act
        alert.Acknowledge(Guid.NewGuid());

        // Assert
        alert.UpdatedAt.Should().BeAfter(originalUpdatedAt.Value);
    }

    [Fact]
    public void Alert_Resolve_Should_Update_Resolution_Properties()
    {
        // Arrange
        var alert = TestDataFactory.CreateTestAlert();
        var resolvedBy = Guid.NewGuid();
        var resolution = "Fixed the underlying issue";

        // Act
        alert.Resolve(resolvedBy, resolution);

        // Assert
        alert.IsActive.Should().BeFalse();
        alert.Status.Should().Be(AlertStatus.Resolved);
        alert.ResolvedBy.Should().Be(resolvedBy);
        alert.Resolution.Should().Be(resolution);
        alert.ResolvedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void Alert_Resolve_Should_Update_Timestamp()
    {
        // Arrange
        var alert = TestDataFactory.CreateTestAlert();
        var originalUpdatedAt = alert.UpdatedAt;

        // Wait a small amount to ensure timestamp difference
        Thread.Sleep(10);

        // Act
        alert.Resolve(Guid.NewGuid(), "Fixed");

        // Assert
        alert.UpdatedAt.Should().BeAfter(originalUpdatedAt.Value);
    }

    [Fact]
    public void Alert_Resolve_Should_Throw_When_Resolution_Is_Null()
    {
        // Arrange
        var alert = TestDataFactory.CreateTestAlert();

        // Act & Assert
        var action = () => alert.Resolve(Guid.NewGuid(), null!);
        action.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void Alert_UpdateSeverity_Should_Update_Severity_And_Timestamp()
    {
        // Arrange
        var alert = TestDataFactory.CreateTestAlert(severity: AlertSeverity.Medium);
        var originalUpdatedAt = alert.UpdatedAt;
        var newSeverity = AlertSeverity.Critical;

        // Wait a small amount to ensure timestamp difference
        Thread.Sleep(10);

        // Act
        alert.UpdateSeverity(newSeverity);

        // Assert
        alert.Severity.Should().Be(newSeverity);
        alert.UpdatedAt.Should().BeAfter(originalUpdatedAt.Value);
    }

    [Fact]
    public void Alert_AddContext_Should_Update_Context_And_Timestamp()
    {
        // Arrange
        var alert = TestDataFactory.CreateTestAlert();
        var originalUpdatedAt = alert.UpdatedAt;
        var contextKey = "server";
        var contextValue = "web-01";

        // Wait a small amount to ensure timestamp difference
        Thread.Sleep(10);

        // Act
        alert.AddContext(contextKey, contextValue);

        // Assert
        alert.Context.Should().ContainKey(contextKey);
        alert.Context[contextKey].Should().Be(contextValue);
        alert.UpdatedAt.Should().BeAfter(originalUpdatedAt.Value);
    }

    [Fact]
    public void Alert_UpdateLastTriggered_Should_Update_Timestamp()
    {
        // Arrange
        var alert = TestDataFactory.CreateTestAlert();
        var originalTriggeredAt = alert.TriggeredAt;
        var originalUpdatedAt = alert.UpdatedAt;

        // Wait a small amount to ensure timestamp difference
        Thread.Sleep(10);

        // Act
        alert.UpdateLastTriggered();

        // Assert
        alert.TriggeredAt.Should().BeAfter(originalTriggeredAt);
        alert.UpdatedAt.Should().BeAfter(originalUpdatedAt.Value);
    }

    [Fact]
    public void Alert_Should_Have_Unique_Ids()
    {
        // Arrange & Act
        var alert1 = TestDataFactory.CreateTestAlert();
        var alert2 = TestDataFactory.CreateTestAlert();

        // Assert
        alert1.Id.Should().NotBe(alert2.Id);
    }

    [Fact]
    public void Alert_Factory_Methods_Should_Create_Specific_Alert_Types()
    {
        // Arrange
        var userId = Guid.NewGuid();

        // Act
        var highSeverityAlert = TestDataFactory.CreateHighSeverityAlert(userId);

        // Assert
        highSeverityAlert.Should().NotBeNull();
        highSeverityAlert.Severity.Should().Be(AlertSeverity.High);
        highSeverityAlert.UserId.Should().Be(userId);
        highSeverityAlert.AlertType.Should().Be(AlertType.System);
        highSeverityAlert.IsActive.Should().BeTrue();
        highSeverityAlert.Name.Should().Contain("Critical");
    }

    [Fact]
    public void Alert_Should_Handle_Null_Optional_Parameters()
    {
        // Act
        var alert = new Alert("Test Alert", "Description", AlertSeverity.Medium, "Test message");

        // Assert
        alert.MetricId.Should().BeNull();
        alert.MetricName.Should().BeNull();
        alert.TriggerValue.Should().BeNull();
        alert.ThresholdValue.Should().BeNull();
        alert.Context.Should().NotBeNull();
        alert.Context.Should().BeEmpty();
        alert.UserId.Should().BeNull();
        alert.AlertType.Should().Be(AlertType.System);
    }

    [Fact]
    public void Alert_Should_Support_Context_Updates()
    {
        // Arrange
        var alert = TestDataFactory.CreateTestAlert();
        var originalUpdatedAt = alert.UpdatedAt;

        // Wait a small amount to ensure timestamp difference
        Thread.Sleep(10);

        // Act
        alert.AddContext("server", "web-02");
        alert.AddContext("region", "us-east-1");
        alert.AddContext("environment", "production");

        // Assert
        alert.Context.Should().ContainKey("server");
        alert.Context.Should().ContainKey("region");
        alert.Context.Should().ContainKey("environment");
        alert.Context["server"].Should().Be("web-02");
        alert.Context["region"].Should().Be("us-east-1");
        alert.Context["environment"].Should().Be("production");
        alert.UpdatedAt.Should().BeAfter(originalUpdatedAt.Value);
    }

    [Fact]
    public void Alert_Should_Track_Creation_And_Update_Times()
    {
        // Arrange
        var alert = TestDataFactory.CreateTestAlert();
        var createdAt = alert.CreatedAt;
        var originalUpdatedAt = alert.UpdatedAt;

        // Wait a small amount to ensure timestamp difference
        Thread.Sleep(10);

        // Act
        alert.UpdateSeverity(AlertSeverity.Critical);

        // Assert
        alert.CreatedAt.Should().Be(createdAt); // Should not change
        alert.UpdatedAt.Should().BeAfter(originalUpdatedAt.Value); // Should be updated
    }

    [Fact]
    public void Alert_Should_Support_Different_Severities_And_Statuses()
    {
        // Arrange & Act
        var lowAlert = TestDataFactory.CreateTestAlert(severity: AlertSeverity.Low);
        var mediumAlert = TestDataFactory.CreateTestAlert(severity: AlertSeverity.Medium);
        var highAlert = TestDataFactory.CreateTestAlert(severity: AlertSeverity.High);
        var criticalAlert = TestDataFactory.CreateTestAlert(severity: AlertSeverity.Critical);

        // Assert
        lowAlert.Severity.Should().Be(AlertSeverity.Low);
        mediumAlert.Severity.Should().Be(AlertSeverity.Medium);
        highAlert.Severity.Should().Be(AlertSeverity.High);
        criticalAlert.Severity.Should().Be(AlertSeverity.Critical);

        // All should start as active
        lowAlert.IsActive.Should().BeTrue();
        mediumAlert.IsActive.Should().BeTrue();
        highAlert.IsActive.Should().BeTrue();
        criticalAlert.IsActive.Should().BeTrue();
    }

    [Fact]
    public void Alert_Acknowledge_And_Resolve_Workflow_Should_Work()
    {
        // Arrange
        var alert = TestDataFactory.CreateTestAlert();
        var acknowledgedBy = Guid.NewGuid();
        var resolvedBy = Guid.NewGuid();

        // Act - Acknowledge first
        alert.Acknowledge(acknowledgedBy);

        // Assert acknowledgment
        alert.IsAcknowledged.Should().BeTrue();
        alert.AcknowledgedBy.Should().Be(acknowledgedBy);
        alert.IsActive.Should().BeTrue(); // Still active after acknowledgment

        // Act - Then resolve
        alert.Resolve(resolvedBy, "Issue has been fixed");

        // Assert resolution
        alert.IsActive.Should().BeFalse();
        alert.Status.Should().Be(AlertStatus.Resolved);
        alert.ResolvedBy.Should().Be(resolvedBy);
        alert.Resolution.Should().Be("Issue has been fixed");
        alert.IsAcknowledged.Should().BeTrue(); // Should still be acknowledged
    }
}
