using DataStorage.Domain.Enums;

namespace DataStorage.Domain.ValueObjects;

public class TranscodingResult
{
    public bool IsSuccessful { get; private set; }
    public string? ErrorMessage { get; private set; }
    public StorageLocation? OutputLocation { get; private set; }
    public TimeSpan ProcessingTime { get; private set; }
    public long InputSizeBytes { get; private set; }
    public long OutputSizeBytes { get; private set; }
    public double CompressionRatio { get; private set; }
    public TranscodingStatistics Statistics { get; private set; }

    private TranscodingResult()
    {
        Statistics = new TranscodingStatistics();
    }

    public static TranscodingResult Success(
        StorageLocation outputLocation,
        TimeSpan processingTime,
        long inputSizeBytes,
        long outputSizeBytes,
        TranscodingStatistics statistics)
    {
        return new TranscodingResult
        {
            IsSuccessful = true,
            OutputLocation = outputLocation,
            ProcessingTime = processingTime,
            InputSizeBytes = inputSizeBytes,
            OutputSizeBytes = outputSizeBytes,
            CompressionRatio = inputSizeBytes > 0 ? (double)outputSizeBytes / inputSizeBytes : 0,
            Statistics = statistics ?? new TranscodingStatistics()
        };
    }

    public static TranscodingResult Failure(string errorMessage)
    {
        return new TranscodingResult
        {
            IsSuccessful = false,
            ErrorMessage = errorMessage,
            ProcessingTime = TimeSpan.Zero,
            InputSizeBytes = 0,
            OutputSizeBytes = 0,
            CompressionRatio = 0,
            Statistics = new TranscodingStatistics()
        };
    }
}

public class VideoTranscodingOptions
{
    public VideoFormat OutputFormat { get; private set; }
    public VideoCodec Codec { get; private set; }
    public VideoQuality Quality { get; private set; }
    public Resolution? Resolution { get; private set; }
    public int? Bitrate { get; private set; }
    public double? FrameRate { get; private set; }
    public bool EnableHardwareAcceleration { get; private set; }
    public Dictionary<string, string> CustomParameters { get; private set; }

    public VideoTranscodingOptions(
        VideoFormat outputFormat,
        VideoCodec codec,
        VideoQuality quality,
        Resolution? resolution = null,
        int? bitrate = null,
        double? frameRate = null,
        bool enableHardwareAcceleration = true,
        Dictionary<string, string>? customParameters = null)
    {
        OutputFormat = outputFormat;
        Codec = codec;
        Quality = quality;
        Resolution = resolution;
        Bitrate = bitrate;
        FrameRate = frameRate;
        EnableHardwareAcceleration = enableHardwareAcceleration;
        CustomParameters = customParameters ?? new Dictionary<string, string>();
    }
}

public class AudioTranscodingOptions
{
    public AudioFormat OutputFormat { get; private set; }
    public AudioCodec Codec { get; private set; }
    public AudioQuality Quality { get; private set; }
    public int? Bitrate { get; private set; }
    public int? SampleRate { get; private set; }
    public int? Channels { get; private set; }
    public Dictionary<string, string> CustomParameters { get; private set; }

    public AudioTranscodingOptions(
        AudioFormat outputFormat,
        AudioCodec codec,
        AudioQuality quality,
        int? bitrate = null,
        int? sampleRate = null,
        int? channels = null,
        Dictionary<string, string>? customParameters = null)
    {
        OutputFormat = outputFormat;
        Codec = codec;
        Quality = quality;
        Bitrate = bitrate;
        SampleRate = sampleRate;
        Channels = channels;
        CustomParameters = customParameters ?? new Dictionary<string, string>();
    }
}

public class VideoMetadata
{
    public TimeSpan Duration { get; private set; }
    public Resolution Resolution { get; private set; }
    public double FrameRate { get; private set; }
    public int Bitrate { get; private set; }
    public VideoCodec Codec { get; private set; }
    public VideoFormat Format { get; private set; }
    public bool HasAudio { get; private set; }
    public AudioMetadata? AudioTrack { get; private set; }
    public long SizeBytes { get; private set; }
    public Dictionary<string, string> CustomMetadata { get; private set; }

    public VideoMetadata(
        TimeSpan duration,
        Resolution resolution,
        double frameRate,
        int bitrate,
        VideoCodec codec,
        VideoFormat format,
        bool hasAudio,
        AudioMetadata? audioTrack,
        long sizeBytes,
        Dictionary<string, string>? customMetadata = null)
    {
        Duration = duration;
        Resolution = resolution;
        FrameRate = frameRate;
        Bitrate = bitrate;
        Codec = codec;
        Format = format;
        HasAudio = hasAudio;
        AudioTrack = audioTrack;
        SizeBytes = sizeBytes;
        CustomMetadata = customMetadata ?? new Dictionary<string, string>();
    }
}

public class AudioMetadata
{
    public TimeSpan Duration { get; private set; }
    public int Bitrate { get; private set; }
    public int SampleRate { get; private set; }
    public int Channels { get; private set; }
    public AudioCodec Codec { get; private set; }
    public AudioFormat Format { get; private set; }
    public long SizeBytes { get; private set; }
    public Dictionary<string, string> CustomMetadata { get; private set; }

    public AudioMetadata(
        TimeSpan duration,
        int bitrate,
        int sampleRate,
        int channels,
        AudioCodec codec,
        AudioFormat format,
        long sizeBytes,
        Dictionary<string, string>? customMetadata = null)
    {
        Duration = duration;
        Bitrate = bitrate;
        SampleRate = sampleRate;
        Channels = channels;
        Codec = codec;
        Format = format;
        SizeBytes = sizeBytes;
        CustomMetadata = customMetadata ?? new Dictionary<string, string>();
    }
}

public class Resolution
{
    public int Width { get; private set; }
    public int Height { get; private set; }
    public double AspectRatio => Height > 0 ? (double)Width / Height : 0;
    public string DisplayName => $"{Width}x{Height}";

    public Resolution(int width, int height)
    {
        Width = width;
        Height = height;
    }

    public static Resolution HD => new(1280, 720);
    public static Resolution FullHD => new(1920, 1080);
    public static Resolution UHD4K => new(3840, 2160);
    public static Resolution UHD8K => new(7680, 4320);
}

public class TranscodingStatistics
{
    public double AverageSpeed { get; private set; }
    public double PeakSpeed { get; private set; }
    public int FramesProcessed { get; private set; }
    public int FramesDropped { get; private set; }
    public double CpuUsage { get; private set; }
    public double MemoryUsage { get; private set; }
    public bool UsedHardwareAcceleration { get; private set; }

    public TranscodingStatistics(
        double averageSpeed = 0,
        double peakSpeed = 0,
        int framesProcessed = 0,
        int framesDropped = 0,
        double cpuUsage = 0,
        double memoryUsage = 0,
        bool usedHardwareAcceleration = false)
    {
        AverageSpeed = averageSpeed;
        PeakSpeed = peakSpeed;
        FramesProcessed = framesProcessed;
        FramesDropped = framesDropped;
        CpuUsage = cpuUsage;
        MemoryUsage = memoryUsage;
        UsedHardwareAcceleration = usedHardwareAcceleration;
    }
}

public class BatchTranscodingJob
{
    public Guid JobId { get; private set; }
    public StorageLocation SourceLocation { get; private set; }
    public VideoTranscodingOptions? VideoOptions { get; private set; }
    public AudioTranscodingOptions? AudioOptions { get; private set; }
    public int Priority { get; private set; }
    public Dictionary<string, string> Metadata { get; private set; }

    public BatchTranscodingJob(
        StorageLocation sourceLocation,
        VideoTranscodingOptions? videoOptions = null,
        AudioTranscodingOptions? audioOptions = null,
        int priority = 0,
        Dictionary<string, string>? metadata = null)
    {
        JobId = Guid.NewGuid();
        SourceLocation = sourceLocation ?? throw new ArgumentNullException(nameof(sourceLocation));
        VideoOptions = videoOptions;
        AudioOptions = audioOptions;
        Priority = priority;
        Metadata = metadata ?? new Dictionary<string, string>();
    }
}

public class BatchTranscodingResult
{
    public List<TranscodingJobResult> Results { get; private set; }
    public int SuccessfulJobs { get; private set; }
    public int FailedJobs { get; private set; }
    public TimeSpan TotalProcessingTime { get; private set; }

    public BatchTranscodingResult(List<TranscodingJobResult> results, TimeSpan totalProcessingTime)
    {
        Results = results ?? new List<TranscodingJobResult>();
        SuccessfulJobs = Results.Count(r => r.Result.IsSuccessful);
        FailedJobs = Results.Count(r => !r.Result.IsSuccessful);
        TotalProcessingTime = totalProcessingTime;
    }
}

public class TranscodingJobResult
{
    public Guid JobId { get; private set; }
    public TranscodingResult Result { get; private set; }
    public DateTime StartTime { get; private set; }
    public DateTime EndTime { get; private set; }

    public TranscodingJobResult(Guid jobId, TranscodingResult result, DateTime startTime, DateTime endTime)
    {
        JobId = jobId;
        Result = result ?? throw new ArgumentNullException(nameof(result));
        StartTime = startTime;
        EndTime = endTime;
    }
}

public class TranscodingJobStatus
{
    public Guid JobId { get; private set; }
    public TranscodingStatus Status { get; private set; }
    public double ProgressPercentage { get; private set; }
    public TimeSpan? EstimatedTimeRemaining { get; private set; }
    public string? CurrentOperation { get; private set; }
    public TranscodingResult? Result { get; private set; }

    public TranscodingJobStatus(
        Guid jobId,
        TranscodingStatus status,
        double progressPercentage = 0,
        TimeSpan? estimatedTimeRemaining = null,
        string? currentOperation = null,
        TranscodingResult? result = null)
    {
        JobId = jobId;
        Status = status;
        ProgressPercentage = Math.Max(0, Math.Min(100, progressPercentage));
        EstimatedTimeRemaining = estimatedTimeRemaining;
        CurrentOperation = currentOperation;
        Result = result;
    }
}
