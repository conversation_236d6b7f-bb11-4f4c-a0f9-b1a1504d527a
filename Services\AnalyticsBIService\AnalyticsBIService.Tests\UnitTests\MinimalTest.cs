using Xunit;

namespace AnalyticsBIService.Tests.UnitTests;

public class MinimalTest
{
    [Fact]
    public void SimpleTest_ShouldPass()
    {
        // Arrange
        var expected = 2;
        
        // Act
        var actual = 1 + 1;
        
        // Assert
        Assert.Equal(expected, actual);
    }
    
    [Fact]
    public void AnotherSimpleTest_ShouldPass()
    {
        // Arrange
        var text = "Hello World";
        
        // Act & Assert
        Assert.NotNull(text);
        Assert.Contains("World", text);
    }
}
