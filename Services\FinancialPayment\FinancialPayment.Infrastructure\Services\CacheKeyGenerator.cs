using FinancialPayment.Application.Interfaces;

namespace FinancialPayment.Infrastructure.Services;

public class CacheKeyGenerator : ICacheKeyGenerator
{
    private const string PAYMENT_PREFIX = "payment";
    private const string SUBSCRIPTION_PREFIX = "subscription";
    private const string PLAN_PREFIX = "plan";
    private const string USER_PAYMENT_METHODS_PREFIX = "user_payment_methods";
    private const string TAX_RULES_PREFIX = "tax_rules";
    private const string FRAUD_RULES_PREFIX = "fraud_rules";
    private const string ANALYTICS_PREFIX = "analytics";
    private const string FEATURE_FLAG_PREFIX = "feature_flag";
    private const string METRIC_PREFIX = "metric";
    private const string SESSION_PREFIX = "session";
    private const string RATE_LIMIT_PREFIX = "rate_limit";

    public string GeneratePaymentKey(Guid paymentId)
    {
        return $"{PAYMENT_PREFIX}:{paymentId}";
    }

    public string GenerateSubscriptionKey(Guid subscriptionId)
    {
        return $"{SUBSCRIPTION_PREFIX}:{subscriptionId}";
    }

    public string GeneratePlanKey(Guid planId)
    {
        return $"{PLAN_PREFIX}:{planId}";
    }

    public string GenerateUserPaymentMethodsKey(Guid userId)
    {
        return $"{USER_PAYMENT_METHODS_PREFIX}:{userId}";
    }

    public string GenerateTaxRulesKey(string jurisdiction)
    {
        return $"{TAX_RULES_PREFIX}:{jurisdiction.ToLowerInvariant()}";
    }

    public string GenerateFraudRulesKey()
    {
        return $"{FRAUD_RULES_PREFIX}:all";
    }

    public string GenerateAnalyticsKey(string analyticsType, params string[] parameters)
    {
        var parameterString = string.Join(":", parameters.Select(p => p.ToLowerInvariant()));
        return $"{ANALYTICS_PREFIX}:{analyticsType.ToLowerInvariant()}:{parameterString}";
    }

    public string GenerateFeatureFlagKey(string flagKey)
    {
        return $"{FEATURE_FLAG_PREFIX}:{flagKey.ToLowerInvariant()}";
    }

    public string GenerateMetricKey(string metricName, DateTime timestamp)
    {
        var dateKey = timestamp.ToString("yyyyMMddHH"); // Group by hour
        return $"{METRIC_PREFIX}:{metricName.ToLowerInvariant()}:{dateKey}";
    }

    public string GenerateSessionKey(string sessionId)
    {
        return $"{SESSION_PREFIX}:{sessionId}";
    }

    public string GenerateRateLimitKey(string identifier, string resource)
    {
        return $"{RATE_LIMIT_PREFIX}:{identifier.ToLowerInvariant()}:{resource.ToLowerInvariant()}";
    }

    // Additional specialized key generators
    public string GenerateUserSubscriptionsKey(Guid userId)
    {
        return $"user_subscriptions:{userId}";
    }

    public string GenerateActivePlansKey()
    {
        return "plans:active";
    }

    public string GenerateGatewayStatusKey(string gatewayName)
    {
        return $"gateway_status:{gatewayName.ToLowerInvariant()}";
    }

    public string GenerateCommissionRulesKey(string ruleType)
    {
        return $"commission_rules:{ruleType.ToLowerInvariant()}";
    }

    public string GenerateReconciliationDataKey(string gatewayName, DateTime date)
    {
        var dateKey = date.ToString("yyyyMMdd");
        return $"reconciliation:{gatewayName.ToLowerInvariant()}:{dateKey}";
    }

    public string GenerateReportDataKey(string reportType, DateTime fromDate, DateTime toDate)
    {
        var fromKey = fromDate.ToString("yyyyMMdd");
        var toKey = toDate.ToString("yyyyMMdd");
        return $"report:{reportType.ToLowerInvariant()}:{fromKey}:{toKey}";
    }

    public string GenerateDashboardDataKey(Guid dashboardId, string widgetType)
    {
        return $"dashboard:{dashboardId}:{widgetType.ToLowerInvariant()}";
    }

    public string GenerateHealthCheckKey(string serviceName, string checkName)
    {
        return $"health:{serviceName.ToLowerInvariant()}:{checkName.ToLowerInvariant()}";
    }

    public string GenerateAlertRulesKey()
    {
        return "alert_rules:all";
    }

    public string GenerateUserActivityKey(Guid userId, DateTime date)
    {
        var dateKey = date.ToString("yyyyMMdd");
        return $"user_activity:{userId}:{dateKey}";
    }

    public string GenerateApiUsageKey(string endpoint, DateTime date)
    {
        var dateKey = date.ToString("yyyyMMddHH");
        return $"api_usage:{endpoint.ToLowerInvariant().Replace("/", "_")}:{dateKey}";
    }

    public string GenerateBlacklistKey(string listType)
    {
        return $"blacklist:{listType.ToLowerInvariant()}";
    }

    public string GenerateConfigurationKey(string configType)
    {
        return $"config:{configType.ToLowerInvariant()}";
    }

    public string GenerateTemporaryKey(string keyType, string identifier, TimeSpan duration)
    {
        var expiryTimestamp = DateTimeOffset.UtcNow.Add(duration).ToUnixTimeSeconds();
        return $"temp:{keyType.ToLowerInvariant()}:{identifier}:{expiryTimestamp}";
    }

    public string GenerateLockKey(string resource, string operation)
    {
        return $"lock:{resource.ToLowerInvariant()}:{operation.ToLowerInvariant()}";
    }

    public string GenerateQueueKey(string queueName)
    {
        return $"queue:{queueName.ToLowerInvariant()}";
    }

    public string GenerateNotificationKey(Guid userId, string notificationType)
    {
        return $"notification:{userId}:{notificationType.ToLowerInvariant()}";
    }

    public string GenerateAuditLogKey(string entityType, Guid entityId, DateTime date)
    {
        var dateKey = date.ToString("yyyyMMdd");
        return $"audit:{entityType.ToLowerInvariant()}:{entityId}:{dateKey}";
    }

    public string GenerateSearchResultsKey(string searchType, string query, int page, int pageSize)
    {
        var queryHash = Math.Abs(query.GetHashCode()).ToString();
        return $"search:{searchType.ToLowerInvariant()}:{queryHash}:{page}:{pageSize}";
    }

    public string GenerateAggregationKey(string aggregationType, string groupBy, DateTime date)
    {
        var dateKey = date.ToString("yyyyMMdd");
        return $"aggregation:{aggregationType.ToLowerInvariant()}:{groupBy.ToLowerInvariant()}:{dateKey}";
    }

    public string GenerateExperimentKey(string experimentName, string variantKey)
    {
        return $"experiment:{experimentName.ToLowerInvariant()}:{variantKey.ToLowerInvariant()}";
    }

    public string GeneratePerformanceMetricKey(string operation, string service, DateTime timestamp)
    {
        var timeKey = timestamp.ToString("yyyyMMddHHmm"); // Group by minute
        return $"performance:{service.ToLowerInvariant()}:{operation.ToLowerInvariant()}:{timeKey}";
    }

    public string GenerateCircuitBreakerKey(string serviceName, string operation)
    {
        return $"circuit_breaker:{serviceName.ToLowerInvariant()}:{operation.ToLowerInvariant()}";
    }

    public string GenerateBulkOperationKey(string operationType, Guid operationId)
    {
        return $"bulk_operation:{operationType.ToLowerInvariant()}:{operationId}";
    }

    public string GenerateWebhookKey(string webhookType, string targetUrl)
    {
        var urlHash = Math.Abs(targetUrl.GetHashCode()).ToString();
        return $"webhook:{webhookType.ToLowerInvariant()}:{urlHash}";
    }

    public string GenerateTokenKey(string tokenType, string tokenValue)
    {
        var tokenHash = Math.Abs(tokenValue.GetHashCode()).ToString();
        return $"token:{tokenType.ToLowerInvariant()}:{tokenHash}";
    }

    public string GenerateGeolocationKey(string ipAddress)
    {
        return $"geolocation:{ipAddress}";
    }

    public string GenerateExchangeRateKey(string fromCurrency, string toCurrency, DateTime date)
    {
        var dateKey = date.ToString("yyyyMMdd");
        return $"exchange_rate:{fromCurrency.ToUpperInvariant()}:{toCurrency.ToUpperInvariant()}:{dateKey}";
    }

    public string GenerateComplianceCheckKey(string checkType, string identifier)
    {
        return $"compliance:{checkType.ToLowerInvariant()}:{identifier}";
    }

    public string GenerateBackupKey(string backupType, DateTime timestamp)
    {
        var timeKey = timestamp.ToString("yyyyMMddHHmmss");
        return $"backup:{backupType.ToLowerInvariant()}:{timeKey}";
    }
}
