using Microsoft.EntityFrameworkCore;
using TripManagement.Domain.Entities;
using TripManagement.Domain.Enums;
using TripManagement.Domain.Interfaces;
using TripManagement.Infrastructure.Persistence;

namespace TripManagement.Infrastructure.Repositories;

/// <summary>
/// Repository implementation for RoutingFailure entity
/// </summary>
public class RoutingFailureRepository : IRoutingFailureRepository
{
    private readonly TripManagementDbContext _context;

    public RoutingFailureRepository(TripManagementDbContext context)
    {
        _context = context;
    }

    public async Task<RoutingFailure?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.RoutingFailures
            .Include(rf => rf.Trip)
            .FirstOrDefaultAsync(rf => rf.Id == id, cancellationToken);
    }

    public async Task<IEnumerable<RoutingFailure>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        return await _context.RoutingFailures
            .Include(rf => rf.Trip)
            .OrderByDescending(rf => rf.FailureTime)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<RoutingFailure>> GetByTripIdAsync(Guid tripId, CancellationToken cancellationToken = default)
    {
        return await _context.RoutingFailures
            .Where(rf => rf.TripId == tripId)
            .OrderByDescending(rf => rf.FailureTime)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<RoutingFailure>> GetActiveFailuresAsync(CancellationToken cancellationToken = default)
    {
        return await _context.RoutingFailures
            .Include(rf => rf.Trip)
            .Where(rf => rf.Status == FailureStatus.Open || rf.Status == FailureStatus.InProgress || rf.Status == FailureStatus.Escalated)
            .OrderByDescending(rf => rf.Severity)
            .ThenByDescending(rf => rf.FailureTime)
            .ToListAsync(cancellationToken);
    }

    public async Task<(IEnumerable<RoutingFailure> Items, int TotalCount)> GetPagedAsync(
        int pageNumber, 
        int pageSize, 
        DateTime? fromDate = null,
        DateTime? toDate = null,
        List<FailureType>? failureTypes = null,
        List<FailureSeverity>? severities = null,
        List<FailureStatus>? statuses = null,
        List<string>? categories = null,
        bool? isRecurring = null,
        decimal? minImpactCost = null,
        decimal? maxImpactCost = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.RoutingFailures.Include(rf => rf.Trip).AsQueryable();

        // Apply filters
        if (fromDate.HasValue)
            query = query.Where(rf => rf.FailureTime >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(rf => rf.FailureTime <= toDate.Value);

        if (failureTypes?.Any() == true)
            query = query.Where(rf => failureTypes.Contains(rf.FailureType));

        if (severities?.Any() == true)
            query = query.Where(rf => severities.Contains(rf.Severity));

        if (statuses?.Any() == true)
            query = query.Where(rf => statuses.Contains(rf.Status));

        if (categories?.Any() == true)
            query = query.Where(rf => categories.Contains(rf.Category));

        if (isRecurring.HasValue)
            query = query.Where(rf => rf.IsRecurring == isRecurring.Value);

        if (minImpactCost.HasValue)
            query = query.Where(rf => rf.ImpactCost >= minImpactCost.Value);

        if (maxImpactCost.HasValue)
            query = query.Where(rf => rf.ImpactCost <= maxImpactCost.Value);

        var totalCount = await query.CountAsync(cancellationToken);

        var items = await query
            .OrderByDescending(rf => rf.Severity)
            .ThenByDescending(rf => rf.FailureTime)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return (items, totalCount);
    }

    public async Task<IEnumerable<RoutingFailure>> GetFailuresForReportAsync(
        DateTime? fromDate = null,
        DateTime? toDate = null,
        List<FailureType>? failureTypes = null,
        List<FailureSeverity>? severities = null,
        List<FailureStatus>? statuses = null,
        bool includeResolved = true,
        CancellationToken cancellationToken = default)
    {
        var query = _context.RoutingFailures.Include(rf => rf.Trip).AsQueryable();

        if (fromDate.HasValue)
            query = query.Where(rf => rf.FailureTime >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(rf => rf.FailureTime <= toDate.Value);

        if (failureTypes?.Any() == true)
            query = query.Where(rf => failureTypes.Contains(rf.FailureType));

        if (severities?.Any() == true)
            query = query.Where(rf => severities.Contains(rf.Severity));

        if (statuses?.Any() == true)
            query = query.Where(rf => statuses.Contains(rf.Status));

        if (!includeResolved)
            query = query.Where(rf => rf.Status != FailureStatus.Resolved);

        return await query
            .OrderByDescending(rf => rf.FailureTime)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<RoutingFailure>> GetRecurringFailuresAsync(CancellationToken cancellationToken = default)
    {
        return await _context.RoutingFailures
            .Include(rf => rf.Trip)
            .Where(rf => rf.IsRecurring)
            .OrderByDescending(rf => rf.FailureTime)
            .ToListAsync(cancellationToken);
    }

    public async Task<Dictionary<FailureType, int>> GetFailureCountsByTypeAsync(DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default)
    {
        var query = _context.RoutingFailures.AsQueryable();

        if (fromDate.HasValue)
            query = query.Where(rf => rf.FailureTime >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(rf => rf.FailureTime <= toDate.Value);

        return await query
            .GroupBy(rf => rf.FailureType)
            .ToDictionaryAsync(g => g.Key, g => g.Count(), cancellationToken);
    }

    public async Task<Dictionary<FailureSeverity, int>> GetFailureCountsBySeverityAsync(DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default)
    {
        var query = _context.RoutingFailures.AsQueryable();

        if (fromDate.HasValue)
            query = query.Where(rf => rf.FailureTime >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(rf => rf.FailureTime <= toDate.Value);

        return await query
            .GroupBy(rf => rf.Severity)
            .ToDictionaryAsync(g => g.Key, g => g.Count(), cancellationToken);
    }

    public async Task<Dictionary<string, int>> GetFailureCountsByCategoryAsync(DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default)
    {
        var query = _context.RoutingFailures.AsQueryable();

        if (fromDate.HasValue)
            query = query.Where(rf => rf.FailureTime >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(rf => rf.FailureTime <= toDate.Value);

        return await query
            .GroupBy(rf => rf.Category)
            .ToDictionaryAsync(g => g.Key, g => g.Count(), cancellationToken);
    }

    public async Task<decimal> GetTotalImpactCostAsync(DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default)
    {
        var query = _context.RoutingFailures.AsQueryable();

        if (fromDate.HasValue)
            query = query.Where(rf => rf.FailureTime >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(rf => rf.FailureTime <= toDate.Value);

        return await query
            .Where(rf => rf.ImpactCost.HasValue)
            .SumAsync(rf => rf.ImpactCost!.Value, cancellationToken);
    }

    public async Task<TimeSpan> GetTotalDelayTimeAsync(DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default)
    {
        var query = _context.RoutingFailures.AsQueryable();

        if (fromDate.HasValue)
            query = query.Where(rf => rf.FailureTime >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(rf => rf.FailureTime <= toDate.Value);

        var totalTicks = await query
            .Where(rf => rf.DelayTime.HasValue)
            .SumAsync(rf => rf.DelayTime!.Value.Ticks, cancellationToken);

        return new TimeSpan(totalTicks);
    }

    public async Task<int> GetTotalAffectedCustomersAsync(DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default)
    {
        var query = _context.RoutingFailures.AsQueryable();

        if (fromDate.HasValue)
            query = query.Where(rf => rf.FailureTime >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(rf => rf.FailureTime <= toDate.Value);

        return await query
            .Where(rf => rf.AffectedCustomers.HasValue)
            .SumAsync(rf => rf.AffectedCustomers!.Value, cancellationToken);
    }

    public async Task<double> GetAverageResolutionTimeHoursAsync(DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default)
    {
        var query = _context.RoutingFailures.AsQueryable();

        if (fromDate.HasValue)
            query = query.Where(rf => rf.FailureTime >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(rf => rf.FailureTime <= toDate.Value);

        var resolutionTimes = await query
            .Where(rf => rf.ResolutionTime.HasValue)
            .Select(rf => rf.ResolutionTime!.Value.TotalHours)
            .ToListAsync(cancellationToken);

        return resolutionTimes.Any() ? resolutionTimes.Average() : 0;
    }

    public async Task<decimal> GetResolutionRateAsync(DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default)
    {
        var query = _context.RoutingFailures.AsQueryable();

        if (fromDate.HasValue)
            query = query.Where(rf => rf.FailureTime >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(rf => rf.FailureTime <= toDate.Value);

        var totalFailures = await query.CountAsync(cancellationToken);
        var resolvedFailures = await query.CountAsync(rf => rf.Status == FailureStatus.Resolved, cancellationToken);

        return totalFailures > 0 ? (decimal)resolvedFailures / totalFailures * 100 : 0;
    }

    public async Task<IEnumerable<RoutingFailure>> GetLongRunningFailuresAsync(int hoursThreshold = 4, CancellationToken cancellationToken = default)
    {
        var thresholdTime = DateTime.UtcNow.AddHours(-hoursThreshold);

        return await _context.RoutingFailures
            .Include(rf => rf.Trip)
            .Where(rf => rf.DetectedAt.HasValue && rf.DetectedAt.Value <= thresholdTime && rf.Status != FailureStatus.Resolved)
            .OrderByDescending(rf => rf.DetectedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<Dictionary<DateTime, int>> GetDailyFailureCountsAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default)
    {
        return await _context.RoutingFailures
            .Where(rf => rf.FailureTime >= fromDate && rf.FailureTime <= toDate)
            .GroupBy(rf => rf.FailureTime.Date)
            .ToDictionaryAsync(g => g.Key, g => g.Count(), cancellationToken);
    }

    public async Task<Dictionary<DateTime, int>> GetDailyResolutionCountsAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default)
    {
        return await _context.RoutingFailures
            .Where(rf => rf.ResolvedAt.HasValue && rf.ResolvedAt.Value >= fromDate && rf.ResolvedAt.Value <= toDate)
            .GroupBy(rf => rf.ResolvedAt!.Value.Date)
            .ToDictionaryAsync(g => g.Key, g => g.Count(), cancellationToken);
    }

    public async Task<Dictionary<string, int>> GetRootCauseFrequencyAsync(DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default)
    {
        var query = _context.RoutingFailures.AsQueryable();

        if (fromDate.HasValue)
            query = query.Where(rf => rf.FailureTime >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(rf => rf.FailureTime <= toDate.Value);

        return await query
            .Where(rf => !string.IsNullOrEmpty(rf.RootCause))
            .GroupBy(rf => rf.RootCause!)
            .ToDictionaryAsync(g => g.Key, g => g.Count(), cancellationToken);
    }

    public async Task<IEnumerable<RoutingFailure>> GetFailuresByRootCauseAsync(string rootCause, CancellationToken cancellationToken = default)
    {
        return await _context.RoutingFailures
            .Include(rf => rf.Trip)
            .Where(rf => rf.RootCause == rootCause)
            .OrderByDescending(rf => rf.FailureTime)
            .ToListAsync(cancellationToken);
    }

    public async Task<bool> ExistsAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.RoutingFailures
            .AnyAsync(rf => rf.Id == id, cancellationToken);
    }

    public async Task<bool> HasActiveFailuresForTripAsync(Guid tripId, CancellationToken cancellationToken = default)
    {
        return await _context.RoutingFailures
            .AnyAsync(rf => rf.TripId == tripId && 
                           (rf.Status == FailureStatus.Open || rf.Status == FailureStatus.InProgress || rf.Status == FailureStatus.Escalated), 
                      cancellationToken);
    }

    public void Add(RoutingFailure routingFailure)
    {
        _context.RoutingFailures.Add(routingFailure);
    }

    public void Update(RoutingFailure routingFailure)
    {
        _context.RoutingFailures.Update(routingFailure);
    }

    public void Delete(RoutingFailure routingFailure)
    {
        _context.RoutingFailures.Remove(routingFailure);
    }

    public async Task<IEnumerable<RoutingFailure>> GetByIdsAsync(IEnumerable<Guid> ids, CancellationToken cancellationToken = default)
    {
        return await _context.RoutingFailures
            .Include(rf => rf.Trip)
            .Where(rf => ids.Contains(rf.Id))
            .ToListAsync(cancellationToken);
    }

    public async Task<int> GetTotalCountAsync(CancellationToken cancellationToken = default)
    {
        return await _context.RoutingFailures.CountAsync(cancellationToken);
    }

    public async Task<int> GetActiveCountAsync(CancellationToken cancellationToken = default)
    {
        return await _context.RoutingFailures
            .CountAsync(rf => rf.Status == FailureStatus.Open || rf.Status == FailureStatus.InProgress || rf.Status == FailureStatus.Escalated, 
                       cancellationToken);
    }

    public async Task<int> GetCriticalCountAsync(CancellationToken cancellationToken = default)
    {
        return await _context.RoutingFailures
            .CountAsync(rf => rf.Severity == FailureSeverity.Critical, cancellationToken);
    }
}
