using Shared.Domain.ValueObjects;

namespace DataStorage.Domain.ValueObjects;

/// <summary>
/// Data governance dashboard information
/// </summary>
public class DataGovernanceDashboard : ValueObject
{
    public DateTime GeneratedAt { get; init; }
    public ComplianceOverview ComplianceOverview { get; init; }
    public DataQualityMetrics DataQuality { get; init; }
    public SecurityMetrics Security { get; init; }
    public RetentionMetrics Retention { get; init; }
    public List<ComplianceAlert> RecentAlerts { get; init; }
    public List<DataGovernanceMetric> KeyMetrics { get; init; }

    public DataGovernanceDashboard(
        DateTime generatedAt,
        ComplianceOverview complianceOverview,
        DataQualityMetrics dataQuality,
        SecurityMetrics security,
        RetentionMetrics retention,
        List<ComplianceAlert> recentAlerts,
        List<DataGovernanceMetric> keyMetrics)
    {
        GeneratedAt = generatedAt;
        ComplianceOverview = complianceOverview;
        DataQuality = dataQuality;
        Security = security;
        Retention = retention;
        RecentAlerts = recentAlerts;
        KeyMetrics = keyMetrics;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return GeneratedAt;
        yield return ComplianceOverview;
        yield return DataQuality;
        yield return Security;
        yield return Retention;

        foreach (var alert in RecentAlerts.OrderBy(x => x.AlertId))
        {
            yield return alert;
        }

        foreach (var metric in KeyMetrics.OrderBy(x => x.Name))
        {
            yield return metric;
        }
    }
}

public class ComplianceOverview : ValueObject
{
    public int TotalPolicies { get; init; }
    public int ActivePolicies { get; init; }
    public int ViolationCount { get; init; }
    public double ComplianceScore { get; init; }

    public ComplianceOverview(int totalPolicies, int activePolicies, int violationCount, double complianceScore)
    {
        TotalPolicies = totalPolicies;
        ActivePolicies = activePolicies;
        ViolationCount = violationCount;
        ComplianceScore = complianceScore;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return TotalPolicies;
        yield return ActivePolicies;
        yield return ViolationCount;
        yield return ComplianceScore;
    }
}

public class DataQualityMetrics : ValueObject
{
    public double QualityScore { get; init; }
    public int IssueCount { get; init; }
    public Dictionary<string, int> IssuesByType { get; init; }

    public DataQualityMetrics(double qualityScore, int issueCount, Dictionary<string, int> issuesByType)
    {
        QualityScore = qualityScore;
        IssueCount = issueCount;
        IssuesByType = issuesByType;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return QualityScore;
        yield return IssueCount;

        foreach (var issue in IssuesByType.OrderBy(x => x.Key))
        {
            yield return issue.Key;
            yield return issue.Value;
        }
    }
}

public class SecurityMetrics : ValueObject
{
    public int SecurityIncidents { get; init; }
    public int AccessViolations { get; init; }
    public double SecurityScore { get; init; }

    public SecurityMetrics(int securityIncidents, int accessViolations, double securityScore)
    {
        SecurityIncidents = securityIncidents;
        AccessViolations = accessViolations;
        SecurityScore = securityScore;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return SecurityIncidents;
        yield return AccessViolations;
        yield return SecurityScore;
    }
}

public class RetentionMetrics : ValueObject
{
    public int DocumentsNearExpiry { get; init; }
    public int ExpiredDocuments { get; init; }
    public long StorageUsed { get; init; }
    public long StorageLimit { get; init; }

    public RetentionMetrics(int documentsNearExpiry, int expiredDocuments, long storageUsed, long storageLimit)
    {
        DocumentsNearExpiry = documentsNearExpiry;
        ExpiredDocuments = expiredDocuments;
        StorageUsed = storageUsed;
        StorageLimit = storageLimit;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return DocumentsNearExpiry;
        yield return ExpiredDocuments;
        yield return StorageUsed;
        yield return StorageLimit;
    }
}

public class DataGovernanceMetric : ValueObject
{
    public string Name { get; init; }
    public double Value { get; init; }
    public string Unit { get; init; }
    public string Trend { get; init; }

    public DataGovernanceMetric(string name, double value, string unit, string trend)
    {
        Name = name;
        Value = value;
        Unit = unit;
        Trend = trend;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Name;
        yield return Value;
        yield return Unit;
        yield return Trend;
    }
}
