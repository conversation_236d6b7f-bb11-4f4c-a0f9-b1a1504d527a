using OrderManagement.Domain.Enums;

namespace OrderManagement.Domain.ValueObjects;

public record LoadDetails
{
    public string Description { get; init; } = string.Empty;
    public LoadType LoadType { get; init; }
    public Weight Weight { get; init; } = null!;
    public Volume? Volume { get; init; }
    public Dimensions? Dimensions { get; init; }
    public int Quantity { get; init; }
    public string? PackagingType { get; init; }
    public VehicleType RequiredVehicleType { get; init; }
    public bool RequiresSpecialHandling { get; init; }
    public string? SpecialHandlingInstructions { get; init; }
    public bool IsHazardous { get; init; }
    public string? HazardousClassification { get; init; }
    public bool RequiresTemperatureControl { get; init; }
    public TemperatureRange? TemperatureRange { get; init; }
    public List<string> Photos { get; init; } = new();

    // Parameterless constructor for EF Core
    public LoadDetails() { }

    public LoadDetails(
        string description,
        LoadType loadType,
        Weight weight,
        int quantity,
        VehicleType requiredVehicleType,
        Volume? volume = null,
        Dimensions? dimensions = null,
        string? packagingType = null,
        bool requiresSpecialHandling = false,
        string? specialHandlingInstructions = null,
        bool isHazardous = false,
        string? hazardousClassification = null,
        bool requiresTemperatureControl = false,
        TemperatureRange? temperatureRange = null,
        List<string>? photos = null)
    {
        if (string.IsNullOrWhiteSpace(description))
            throw new ArgumentException("Load description cannot be empty", nameof(description));

        if (quantity <= 0)
            throw new ArgumentException("Quantity must be greater than zero", nameof(quantity));

        if (isHazardous && string.IsNullOrWhiteSpace(hazardousClassification))
            throw new ArgumentException("Hazardous classification is required for hazardous loads", nameof(hazardousClassification));

        if (requiresTemperatureControl && temperatureRange == null)
            throw new ArgumentException("Temperature range is required for temperature-controlled loads", nameof(temperatureRange));

        Description = description;
        LoadType = loadType;
        Weight = weight;
        Volume = volume;
        Dimensions = dimensions;
        Quantity = quantity;
        PackagingType = packagingType;
        RequiredVehicleType = requiredVehicleType;
        RequiresSpecialHandling = requiresSpecialHandling;
        SpecialHandlingInstructions = specialHandlingInstructions;
        IsHazardous = isHazardous;
        HazardousClassification = hazardousClassification;
        RequiresTemperatureControl = requiresTemperatureControl;
        TemperatureRange = temperatureRange;
        Photos = photos ?? new List<string>();
    }
}

public record Weight
{
    public decimal Value { get; init; }
    public WeightUnit Unit { get; init; }

    // Parameterless constructor for EF Core
    public Weight() { }

    public Weight(decimal value, WeightUnit unit)
    {
        Value = value;
        Unit = unit;
    }

    public decimal ToKilograms() => Unit switch
    {
        WeightUnit.Kg => Value,
        WeightUnit.Ton => Value * 1000,
        WeightUnit.Pound => Value * 0.453592m,
        _ => throw new ArgumentException("Invalid weight unit")
    };
}

public record Volume
{
    public decimal Value { get; init; }
    public VolumeUnit Unit { get; init; }

    // Parameterless constructor for EF Core
    public Volume() { }

    public Volume(decimal value, VolumeUnit unit)
    {
        Value = value;
        Unit = unit;
    }

    public decimal ToCubicMeters() => Unit switch
    {
        VolumeUnit.CubicMeter => Value,
        VolumeUnit.CubicFeet => Value * 0.0283168m,
        VolumeUnit.Liter => Value * 0.001m,
        _ => throw new ArgumentException("Invalid volume unit")
    };
}

public record Dimensions
{
    public decimal Length { get; init; }
    public decimal Width { get; init; }
    public decimal Height { get; init; }
    public string Unit { get; init; } = "cm";

    // Parameterless constructor for EF Core
    public Dimensions() { }

    public Dimensions(decimal length, decimal width, decimal height, string unit = "cm")
    {
        Length = length;
        Width = width;
        Height = height;
        Unit = unit;
    }

    public decimal Volume => Length * Width * Height;
}

public record TemperatureRange
{
    public decimal MinTemperature { get; init; }
    public decimal MaxTemperature { get; init; }
    public string Unit { get; init; } = "C";

    // Parameterless constructor for EF Core
    public TemperatureRange() { }

    public TemperatureRange(decimal minTemperature, decimal maxTemperature, string unit = "C")
    {
        MinTemperature = minTemperature;
        MaxTemperature = maxTemperature;
        Unit = unit;
    }
};
