using AuditCompliance.Application.Interfaces;
using AuditCompliance.Domain.Entities;
using AuditCompliance.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace AuditCompliance.Infrastructure.Repositories
{
    /// <summary>
    /// Repository implementation for ModuleRegistry entity
    /// </summary>
    public class ModuleRegistryRepository : IModuleRegistryRepository
    {
        private readonly AuditComplianceDbContext _context;
        private readonly ILogger<ModuleRegistryRepository> _logger;

        public ModuleRegistryRepository(
            AuditComplianceDbContext context,
            ILogger<ModuleRegistryRepository> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<List<ModuleRegistry>> GetAllAsync()
        {
            try
            {
                return await _context.ModuleRegistries
                    .OrderBy(m => m.ModuleName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all module registries");
                throw;
            }
        }

        public async Task<ModuleRegistry?> GetByIdAsync(Guid id)
        {
            try
            {
                return await _context.ModuleRegistries
                    .FirstOrDefaultAsync(m => m.Id == id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving module registry by ID {Id}", id);
                throw;
            }
        }

        public async Task<ModuleRegistry?> GetByModuleNameAsync(string moduleName)
        {
            try
            {
                return await _context.ModuleRegistries
                    .FirstOrDefaultAsync(m => m.ModuleName == moduleName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving module registry by name {ModuleName}", moduleName);
                throw;
            }
        }

        public async Task<List<ModuleRegistry>> GetActiveModulesAsync()
        {
            try
            {
                return await _context.ModuleRegistries
                    .Where(m => m.IsActive)
                    .OrderBy(m => m.ModuleName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving active module registries");
                throw;
            }
        }

        public async Task<ModuleRegistry> AddAsync(ModuleRegistry module)
        {
            try
            {
                _context.ModuleRegistries.Add(module);
                await _context.SaveChangesAsync();
                
                _logger.LogInformation("Module registry added: {ModuleName}", module.ModuleName);
                return module;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding module registry {ModuleName}", module.ModuleName);
                throw;
            }
        }

        public async Task UpdateAsync(ModuleRegistry module)
        {
            try
            {
                _context.ModuleRegistries.Update(module);
                await _context.SaveChangesAsync();
                
                _logger.LogInformation("Module registry updated: {ModuleName}", module.ModuleName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating module registry {ModuleName}", module.ModuleName);
                throw;
            }
        }

        public async Task DeleteAsync(Guid id)
        {
            try
            {
                var module = await GetByIdAsync(id);
                if (module != null)
                {
                    _context.ModuleRegistries.Remove(module);
                    await _context.SaveChangesAsync();
                    
                    _logger.LogInformation("Module registry deleted: {Id}", id);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting module registry {Id}", id);
                throw;
            }
        }

        public async Task<List<ModuleRegistry>> GetModulesByEntityTypeAsync(string entityType)
        {
            try
            {
                return await _context.ModuleRegistries
                    .Where(m => m.IsActive && m.SupportedEntityTypes.Contains(entityType))
                    .OrderBy(m => m.ModuleName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving modules by entity type {EntityType}", entityType);
                throw;
            }
        }

        public async Task<List<ModuleRegistry>> GetModulesByActionAsync(string action)
        {
            try
            {
                return await _context.ModuleRegistries
                    .Where(m => m.IsActive && m.SupportedActions.Contains(action))
                    .OrderBy(m => m.ModuleName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving modules by action {Action}", action);
                throw;
            }
        }

        public async Task<List<ModuleRegistry>> GetUnhealthyModulesAsync()
        {
            try
            {
                return await _context.ModuleRegistries
                    .Where(m => m.IsActive && !m.IsHealthy)
                    .OrderBy(m => m.LastHealthCheck)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving unhealthy modules");
                throw;
            }
        }

        public async Task<List<ModuleRegistry>> SearchModulesAsync(string searchTerm)
        {
            try
            {
                var term = searchTerm.ToLower();
                return await _context.ModuleRegistries
                    .Where(m => m.ModuleName.ToLower().Contains(term) ||
                               m.DisplayName.ToLower().Contains(term) ||
                               m.Description.ToLower().Contains(term))
                    .OrderBy(m => m.ModuleName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching modules with term {SearchTerm}", searchTerm);
                throw;
            }
        }

        public async Task<int> GetTotalModuleCountAsync()
        {
            try
            {
                return await _context.ModuleRegistries.CountAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting total module count");
                throw;
            }
        }

        public async Task<int> GetActiveModuleCountAsync()
        {
            try
            {
                return await _context.ModuleRegistries.CountAsync(m => m.IsActive);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting active module count");
                throw;
            }
        }

        public async Task<Dictionary<string, int>> GetModuleCountByVersionAsync()
        {
            try
            {
                return await _context.ModuleRegistries
                    .GroupBy(m => m.Version)
                    .ToDictionaryAsync(g => g.Key, g => g.Count());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting module count by version");
                throw;
            }
        }

        public async Task BulkUpdateHealthStatusAsync(List<(string ModuleName, bool IsHealthy, string? ErrorMessage)> healthUpdates)
        {
            try
            {
                foreach (var (moduleName, isHealthy, errorMessage) in healthUpdates)
                {
                    var module = await GetByModuleNameAsync(moduleName);
                    if (module != null)
                    {
                        module.UpdateHealthStatus(isHealthy, errorMessage);
                    }
                }

                await _context.SaveChangesAsync();
                _logger.LogInformation("Bulk updated health status for {Count} modules", healthUpdates.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error bulk updating module health status");
                throw;
            }
        }
    }
}
