using System;
using System.Collections.Generic;
using Shared.Domain.Common;

namespace DataStorage.Domain.Entities
{
    public class SlaReport : BaseEntity
    {
        public string Name { get; private set; }
        public string Description { get; private set; }
        public DateTime PeriodStart { get; private set; }
        public DateTime PeriodEnd { get; private set; }
        public string ServiceName { get; private set; }
        public double UptimePercentage { get; private set; }
        public double AvailabilityTarget { get; private set; }
        public bool SlaMetTarget { get; private set; }
        public TimeSpan TotalDowntime { get; private set; }
        public int IncidentCount { get; private set; }
        public double AverageResponseTime { get; private set; }
        public double ResponseTimeTarget { get; private set; }
        public Dictionary<string, double> PerformanceMetrics { get; private set; }
        public List<string> Violations { get; private set; }
        public string ReportData { get; private set; } // JSON detailed data
        public Guid GeneratedBy { get; private set; }
        public ReportStatus Status { get; private set; }
        public string FilePath { get; private set; }

        private SlaReport()
        {
            PerformanceMetrics = new Dictionary<string, double>();
            Violations = new List<string>();
        }

        public SlaReport(
            string name,
            string description,
            DateTime periodStart,
            DateTime periodEnd,
            string serviceName,
            double availabilityTarget,
            double responseTimeTarget,
            Guid generatedBy)
        {
            Name = name ?? throw new ArgumentNullException(nameof(name));
            Description = description;
            PeriodStart = periodStart;
            PeriodEnd = periodEnd;
            ServiceName = serviceName ?? throw new ArgumentNullException(nameof(serviceName));
            AvailabilityTarget = availabilityTarget;
            ResponseTimeTarget = responseTimeTarget;
            GeneratedBy = generatedBy;
            Status = ReportStatus.Pending;
            PerformanceMetrics = new Dictionary<string, double>();
            Violations = new List<string>();
            CreatedAt = DateTime.UtcNow;
        }

        public void UpdateSlaMetrics(
            double uptimePercentage,
            TimeSpan totalDowntime,
            int incidentCount,
            double averageResponseTime)
        {
            UptimePercentage = uptimePercentage;
            TotalDowntime = totalDowntime;
            IncidentCount = incidentCount;
            AverageResponseTime = averageResponseTime;

            // Determine if SLA targets were met
            SlaMetTarget = uptimePercentage >= AvailabilityTarget &&
                          averageResponseTime <= ResponseTimeTarget;

            UpdatedAt = DateTime.UtcNow;
        }

        public void AddPerformanceMetric(string metricName, double value)
        {
            PerformanceMetrics[metricName] = value;
            UpdatedAt = DateTime.UtcNow;
        }

        public void AddViolation(string violation)
        {
            if (!string.IsNullOrWhiteSpace(violation) && !Violations.Contains(violation))
            {
                Violations.Add(violation);
                UpdatedAt = DateTime.UtcNow;
            }
        }

        public void SetReportData(string reportData)
        {
            ReportData = reportData;
            UpdatedAt = DateTime.UtcNow;
        }

        public void CompleteReport(string filePath)
        {
            Status = ReportStatus.Completed;
            FilePath = filePath;
            UpdatedAt = DateTime.UtcNow;
        }

        public void FailReport()
        {
            Status = ReportStatus.Failed;
            UpdatedAt = DateTime.UtcNow;
        }

        public double GetAvailabilityGap()
        {
            return Math.Max(0, AvailabilityTarget - UptimePercentage);
        }

        public double GetResponseTimeGap()
        {
            return Math.Max(0, AverageResponseTime - ResponseTimeTarget);
        }

        public bool HasCriticalViolations()
        {
            return !SlaMetTarget || Violations.Count > 0;
        }
    }
}
