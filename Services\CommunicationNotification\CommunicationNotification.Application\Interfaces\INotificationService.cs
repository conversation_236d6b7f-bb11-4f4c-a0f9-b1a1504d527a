using CommunicationNotification.Domain.Entities;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;

namespace CommunicationNotification.Application.Interfaces;

/// <summary>
/// Main notification service interface for orchestrating notifications
/// </summary>
public interface INotificationService
{
    /// <summary>
    /// Send notification using the best available channel based on user preferences
    /// </summary>
    /// <param name="request">Notification request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Notification send result</returns>
    Task<NotificationSendResult> SendNotificationAsync(
        NotificationRequest request,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Send notification to multiple recipients
    /// </summary>
    /// <param name="request">Bulk notification request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Bulk notification results</returns>
    Task<BulkNotificationResult> SendBulkNotificationAsync(
        BulkNotificationRequest request,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Schedule notification for future delivery
    /// </summary>
    /// <param name="request">Scheduled notification request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Scheduled notification result</returns>
    Task<ScheduledNotificationResult> ScheduleNotificationAsync(
        ScheduledNotificationRequest request,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get notification delivery status
    /// </summary>
    /// <param name="notificationId">Notification ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Notification status</returns>
    Task<NotificationStatus> GetNotificationStatusAsync(
        Guid notificationId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Cancel scheduled notification
    /// </summary>
    /// <param name="notificationId">Notification ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if cancelled successfully</returns>
    Task<bool> CancelNotificationAsync(
        Guid notificationId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Retry failed notification
    /// </summary>
    /// <param name="notificationId">Notification ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Retry result</returns>
    Task<NotificationSendResult> RetryNotificationAsync(
        Guid notificationId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get user's notification preferences
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>User preferences</returns>
    Task<UserPreference?> GetUserPreferencesAsync(
        Guid userId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Update user's notification preferences
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="preferences">New preferences</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if updated successfully</returns>
    Task<bool> UpdateUserPreferencesAsync(
        Guid userId,
        NotificationSettings preferences,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Notification request
/// </summary>
public class NotificationRequest
{
    public Guid UserId { get; set; }
    public MessageType MessageType { get; set; }
    public MessageContent Content { get; set; } = MessageContent.Create("", "", Language.English);
    public Priority Priority { get; set; } = Priority.Normal;
    public NotificationChannel? PreferredChannel { get; set; }
    public Guid? RelatedEntityId { get; set; }
    public string? RelatedEntityType { get; set; }
    public Dictionary<string, string>? Metadata { get; set; }
    public DateTime? ScheduledAt { get; set; }
}

/// <summary>
/// Bulk notification request
/// </summary>
public class BulkNotificationRequest
{
    public List<Guid> UserIds { get; set; } = new();
    public MessageType MessageType { get; set; }
    public MessageContent Content { get; set; } = MessageContent.Create("", "", Language.English);
    public Priority Priority { get; set; } = Priority.Normal;
    public NotificationChannel? PreferredChannel { get; set; }
    public Guid? RelatedEntityId { get; set; }
    public string? RelatedEntityType { get; set; }
    public Dictionary<string, string>? Metadata { get; set; }
    public DateTime? ScheduledAt { get; set; }
    public int BatchSize { get; set; } = 100;
}

/// <summary>
/// Scheduled notification request
/// </summary>
public class ScheduledNotificationRequest : NotificationRequest
{
    public new DateTime ScheduledAt { get; set; }
    public TimeSpan? ExpiresAfter { get; set; }
    public int MaxRetries { get; set; } = 3;
}

/// <summary>
/// Notification send result
/// </summary>
public class NotificationSendResult
{
    public bool IsSuccess { get; set; }
    public Guid? NotificationId { get; set; }
    public NotificationChannel? ChannelUsed { get; set; }
    public string? ExternalId { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime SentAt { get; set; } = DateTime.UtcNow;

    public static NotificationSendResult Success(Guid notificationId, NotificationChannel channelUsed, string? externalId = null)
    {
        return new NotificationSendResult
        {
            IsSuccess = true,
            NotificationId = notificationId,
            ChannelUsed = channelUsed,
            ExternalId = externalId
        };
    }

    public static NotificationSendResult Failure(string errorMessage)
    {
        return new NotificationSendResult
        {
            IsSuccess = false,
            ErrorMessage = errorMessage
        };
    }
}

/// <summary>
/// Bulk notification result
/// </summary>
public class BulkNotificationResult
{
    public int TotalNotifications { get; set; }
    public int SuccessfulNotifications { get; set; }
    public int FailedNotifications { get; set; }
    public List<NotificationSendResult> Results { get; set; } = new();
    public TimeSpan ProcessingTime { get; set; }

    public bool IsPartialSuccess => SuccessfulNotifications > 0 && FailedNotifications > 0;
    public bool IsCompleteSuccess => SuccessfulNotifications == TotalNotifications;
    public bool IsCompleteFailure => FailedNotifications == TotalNotifications;
}

/// <summary>
/// Scheduled notification result
/// </summary>
public class ScheduledNotificationResult
{
    public bool IsSuccess { get; set; }
    public Guid? NotificationId { get; set; }
    public DateTime ScheduledAt { get; set; }
    public string? ErrorMessage { get; set; }

    public static ScheduledNotificationResult Success(Guid notificationId, DateTime scheduledAt)
    {
        return new ScheduledNotificationResult
        {
            IsSuccess = true,
            NotificationId = notificationId,
            ScheduledAt = scheduledAt
        };
    }

    public static ScheduledNotificationResult Failure(string errorMessage)
    {
        return new ScheduledNotificationResult
        {
            IsSuccess = false,
            ErrorMessage = errorMessage
        };
    }
}

/// <summary>
/// Notification status
/// </summary>
public class NotificationStatus
{
    public Guid NotificationId { get; set; }
    public Guid UserId { get; set; }
    public MessageType MessageType { get; set; }
    public NotificationChannel Channel { get; set; }
    public MessageStatus Status { get; set; }
    public DateTime ScheduledAt { get; set; }
    public DateTime? SentAt { get; set; }
    public DateTime? DeliveredAt { get; set; }
    public DateTime? ReadAt { get; set; }
    public string? ExternalId { get; set; }
    public int RetryCount { get; set; }
    public string? FailureReason { get; set; }
    public Dictionary<string, string> Metadata { get; set; } = new();
}
