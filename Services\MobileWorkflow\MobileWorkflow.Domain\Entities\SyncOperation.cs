using Shared.Domain.Common;
using MobileWorkflow.Domain.Enums;
using MobileWorkflow.Domain.Events;

namespace MobileWorkflow.Domain.Entities;

public class SyncOperation : AggregateRoot
{
    public Guid UserId { get; private set; }
    public Guid DeviceId { get; private set; }
    public string OperationType { get; private set; } // Full, Incremental, Priority, Conflict
    public SyncStatus Status { get; private set; }
    public DateTime StartTime { get; private set; }
    public DateTime? EndTime { get; private set; }
    public int TotalItems { get; private set; }
    public int ProcessedItems { get; private set; }
    public int FailedItems { get; private set; }
    public int ConflictItems { get; private set; }
    public long DataSizeBytes { get; private set; }
    public long TransferredBytes { get; private set; }
    public string? ErrorMessage { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }
    public Dictionary<string, object> ConflictResolutions { get; private set; }
    public int RetryCount { get; private set; }
    public DateTime? NextRetryTime { get; private set; }
    public string? NetworkType { get; private set; } // WiFi, Cellular, Unknown
    public double? BandwidthKbps { get; private set; }
    public TimeSpan? EstimatedTimeRemaining { get; private set; }

    // Navigation properties
    public virtual ICollection<SyncItem> SyncItems { get; private set; } = new List<SyncItem>();
    public virtual ICollection<ConflictResolution> Conflicts { get; private set; } = new List<ConflictResolution>();

    private SyncOperation() { } // EF Core

    public SyncOperation(
        Guid userId,
        Guid deviceId,
        string operationType,
        int totalItems = 0,
        long dataSizeBytes = 0,
        string? networkType = null)
    {
        UserId = userId;
        DeviceId = deviceId;
        OperationType = operationType ?? throw new ArgumentNullException(nameof(operationType));
        Status = SyncStatus.Pending;
        StartTime = DateTime.UtcNow;
        TotalItems = totalItems;
        ProcessedItems = 0;
        FailedItems = 0;
        ConflictItems = 0;
        DataSizeBytes = dataSizeBytes;
        TransferredBytes = 0;
        Metadata = new Dictionary<string, object>();
        ConflictResolutions = new Dictionary<string, object>();
        RetryCount = 0;
        NetworkType = networkType;

        AddDomainEvent(new SyncOperationStartedEvent(Id, OperationType, DeviceId.ToString()));
    }

    public void Start()
    {
        if (Status != SyncStatus.Pending)
            throw new InvalidOperationException($"Cannot start sync operation with status: {Status}");

        Status = SyncStatus.InProgress;
        StartTime = DateTime.UtcNow;

        AddDomainEvent(new SyncOperationProgressEvent(Id, OperationType, DeviceId.ToString(), 0)); // 0% progress when starting
    }

    public void UpdateProgress(int processedItems, long transferredBytes, double? bandwidthKbps = null)
    {
        ProcessedItems = processedItems;
        TransferredBytes = transferredBytes;
        BandwidthKbps = bandwidthKbps;

        // Calculate estimated time remaining
        if (TotalItems > 0 && ProcessedItems > 0 && BandwidthKbps.HasValue && BandwidthKbps > 0)
        {
            var remainingItems = TotalItems - ProcessedItems;
            var avgBytesPerItem = DataSizeBytes / (double)TotalItems;
            var remainingBytes = remainingItems * avgBytesPerItem;
            var remainingSeconds = (remainingBytes * 8) / (BandwidthKbps.Value * 1024); // Convert to seconds
            EstimatedTimeRemaining = TimeSpan.FromSeconds(remainingSeconds);
        }

        AddDomainEvent(new SyncOperationProgressUpdatedEvent(Id, UserId, ProcessedItems, TotalItems, GetProgressPercentage()));
    }

    public void RecordConflict(string itemId, string conflictType, Dictionary<string, object> conflictData)
    {
        ConflictItems++;
        ConflictResolutions[itemId] = new { Type = conflictType, Data = conflictData, Timestamp = DateTime.UtcNow };

        AddDomainEvent(new SyncOperationConflictDetectedEvent(Id, UserId, itemId, conflictType));
    }

    public void ResolveConflict(string itemId, string resolution, Dictionary<string, object> resolutionData)
    {
        if (!ConflictResolutions.ContainsKey(itemId))
            throw new InvalidOperationException($"No conflict found for item: {itemId}");

        var conflict = ConflictResolutions[itemId] as dynamic;
        ConflictResolutions[itemId] = new
        {
            Type = conflict.Type,
            Data = conflict.Data,
            Timestamp = conflict.Timestamp,
            Resolution = resolution,
            ResolutionData = resolutionData,
            ResolvedAt = DateTime.UtcNow
        };

        AddDomainEvent(new SyncOperationConflictResolvedEvent(Id, UserId, itemId, resolution));
    }

    public void RecordFailure(string itemId, string error)
    {
        FailedItems++;
        Metadata[$"failure_{itemId}"] = new { Error = error, Timestamp = DateTime.UtcNow };

        AddDomainEvent(new SyncOperationItemFailedEvent(Id, UserId, itemId, error));
    }

    public void Complete()
    {
        if (Status != SyncStatus.InProgress)
            throw new InvalidOperationException($"Cannot complete sync operation with status: {Status}");

        Status = SyncStatus.Completed;
        EndTime = DateTime.UtcNow;

        var duration = EndTime.Value - StartTime;
        var successRate = TotalItems > 0 ? (double)(ProcessedItems - FailedItems) / TotalItems * 100 : 100;

        AddDomainEvent(new SyncOperationCompletedEvent(Id, OperationType, DeviceId.ToString(), true));
    }

    public void Fail(string errorMessage)
    {
        Status = SyncStatus.Failed;
        EndTime = DateTime.UtcNow;
        ErrorMessage = errorMessage;

        AddDomainEvent(new SyncOperationFailedEvent(Id, OperationType, DeviceId.ToString(), errorMessage));
    }

    public void Cancel()
    {
        if (Status == SyncStatus.Completed || Status == SyncStatus.Failed)
            throw new InvalidOperationException($"Cannot cancel sync operation with status: {Status}");

        Status = SyncStatus.Cancelled;
        EndTime = DateTime.UtcNow;

        AddDomainEvent(new SyncOperationCompletedEvent(Id, OperationType, DeviceId.ToString(), false)); // Cancelled = not successful
    }

    public void ScheduleRetry(TimeSpan delay)
    {
        RetryCount++;
        NextRetryTime = DateTime.UtcNow.Add(delay);
        Status = SyncStatus.Pending;

        AddDomainEvent(new SyncOperationRetryScheduledEvent(Id, UserId, DeviceId, RetryCount, NextRetryTime));
    }

    public double GetProgressPercentage()
    {
        return TotalItems > 0 ? (double)ProcessedItems / TotalItems * 100 : 0;
    }

    public TimeSpan GetDuration()
    {
        var endTime = EndTime ?? DateTime.UtcNow;
        return endTime - StartTime;
    }

    public double GetAverageBandwidthKbps()
    {
        var duration = GetDuration();
        if (duration.TotalSeconds > 0 && TransferredBytes > 0)
        {
            return (TransferredBytes * 8) / (duration.TotalSeconds * 1024);
        }
        return 0;
    }

    public bool ShouldRetry()
    {
        return Status == SyncStatus.Failed && RetryCount < GetMaxRetries() &&
               (NextRetryTime == null || DateTime.UtcNow >= NextRetryTime);
    }

    private int GetMaxRetries()
    {
        return OperationType switch
        {
            "Priority" => 5,
            "Full" => 3,
            "Incremental" => 3,
            "Conflict" => 2,
            _ => 3
        };
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }
}


