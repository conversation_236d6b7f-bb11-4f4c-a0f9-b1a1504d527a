namespace FinancialPayment.Application.DTOs;

public class CommissionDto
{
    public Guid Id { get; set; }
    public Guid OrderId { get; set; }
    public Guid BrokerId { get; set; }
    public Guid TransportCompanyId { get; set; }
    public Guid CarrierId { get; set; }
    public decimal OrderAmount { get; set; }
    public string OrderCurrency { get; set; } = string.Empty;
    public string CommissionType { get; set; } = string.Empty;
    public decimal CommissionRate { get; set; }
    public decimal? CommissionMinimumAmount { get; set; }
    public decimal? CommissionMaximumAmount { get; set; }
    public string? CommissionDescription { get; set; }
    public decimal CalculatedAmount { get; set; }
    public string CalculatedCurrency { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime? ProcessedAt { get; set; }
    public string? Notes { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public List<CommissionAdjustmentDto> Adjustments { get; set; } = new();
}

public class CommissionAdjustmentDto
{
    public Guid Id { get; set; }
    public Guid CommissionId { get; set; }
    public decimal Amount { get; set; }
    public string Currency { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Reason { get; set; } = string.Empty;
    public string? CreatedBy { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}
