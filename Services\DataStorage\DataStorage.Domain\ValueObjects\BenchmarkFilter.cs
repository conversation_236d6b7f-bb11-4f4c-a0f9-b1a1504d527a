using Shared.Domain.ValueObjects;

namespace DataStorage.Domain.ValueObjects;

/// <summary>
/// Filter for benchmark queries
/// </summary>
public class BenchmarkFilter : ValueObject
{
    public string? BenchmarkName { get; init; }
    public string? BenchmarkType { get; init; }
    public DateTime? FromDate { get; init; }
    public DateTime? ToDate { get; init; }
    public string? Environment { get; init; }
    public List<string>? Tags { get; init; }
    public string? Status { get; init; }
    public int Skip { get; init; }
    public int Take { get; init; }

    public BenchmarkFilter(
        string? benchmarkName = null,
        string? benchmarkType = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        string? environment = null,
        List<string>? tags = null,
        string? status = null,
        int skip = 0,
        int take = 50)
    {
        BenchmarkName = benchmarkName;
        BenchmarkType = benchmarkType;
        FromDate = fromDate;
        ToDate = toDate;
        Environment = environment;
        Tags = tags;
        Status = status;
        Skip = skip;
        Take = take;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return BenchmarkName ?? string.Empty;
        yield return BenchmarkType ?? string.Empty;
        yield return FromDate?.ToString() ?? string.Empty;
        yield return ToDate?.ToString() ?? string.Empty;
        yield return Environment ?? string.Empty;
        yield return Status ?? string.Empty;
        yield return Skip;
        yield return Take;
        
        if (Tags != null)
        {
            foreach (var tag in Tags.OrderBy(x => x))
            {
                yield return tag;
            }
        }
    }
}
