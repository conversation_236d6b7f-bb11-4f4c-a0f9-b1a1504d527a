using FluentAssertions;
using Identity.Application.Auth.Commands.RegisterUser;
using Identity.Application.Common.Interfaces;
using Identity.Domain.Entities;
using Identity.Domain.Repositories;
using Identity.Domain.ValueObjects;
using Microsoft.Extensions.Logging;
using Moq;
using Shared.Messaging;
using Shared.Messaging.Events;

namespace Identity.Tests;

public class RegisterUserCommandHandlerTests
{
    private readonly Mock<IUserRepository> _mockUserRepository;
    private readonly Mock<IPasswordHasher> _mockPasswordHasher;
    private readonly Mock<IMessageBroker> _mockMessageBroker;
    private readonly Mock<ILogger<RegisterUserCommandHandler>> _mockLogger;
    private readonly RegisterUserCommandHandler _handler;

    public RegisterUserCommandHandlerTests()
    {
        _mockUserRepository = new Mock<IUserRepository>();
        _mockPasswordHasher = new Mock<IPasswordHasher>();
        _mockMessageBroker = new Mock<IMessageBroker>();
        _mockLogger = new Mock<ILogger<RegisterUserCommandHandler>>();

        _handler = new RegisterUserCommandHandler(
            _mockUserRepository.Object,
            _mockPasswordHasher.Object,
            _mockMessageBroker.Object,
            _mockLogger.Object);
    }

    [Theory]
    [InlineData(UserType.TransportCompany)]
    [InlineData(UserType.Broker)]
    [InlineData(UserType.Carrier)]
    [InlineData(UserType.Driver)]
    [InlineData(UserType.Shipper)]
    public async Task Handle_ShouldCreateUserWithCorrectUserType(UserType userType)
    {
        // Arrange
        var command = new RegisterUserCommand
        {
            Username = "testuser",
            Email = "<EMAIL>",
            Password = "password123",
            UserType = userType,
            PhoneNumber = "+1234567890",
            FirstName = "John",
            LastName = "Doe"
        };

        _mockUserRepository.Setup(x => x.ExistsByUsernameAsync(command.Username))
            .ReturnsAsync(false);
        _mockUserRepository.Setup(x => x.ExistsByEmailAsync(command.Email))
            .ReturnsAsync(false);
        _mockPasswordHasher.Setup(x => x.HashPassword(command.Password))
            .Returns("hashedpassword");
        _mockUserRepository.Setup(x => x.AddAsync(It.IsAny<User>()))
            .Returns(Task.CompletedTask);
        _mockMessageBroker.Setup(x => x.PublishAsync(It.IsAny<string>(), It.IsAny<UserRegisteredEvent>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBe(Guid.Empty);

        // Verify user was created with correct UserType
        _mockUserRepository.Verify(x => x.AddAsync(It.Is<User>(u => u.UserType == userType)), Times.Once);

        // Verify integration event was published with correct UserType
        _mockMessageBroker.Verify(x => x.PublishAsync("user.registered", 
            It.Is<UserRegisteredEvent>(e => e.UserType == userType.ToString())), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldThrowException_WhenUsernameAlreadyExists()
    {
        // Arrange
        var command = new RegisterUserCommand
        {
            Username = "existinguser",
            Email = "<EMAIL>",
            Password = "password123",
            UserType = UserType.Shipper
        };

        _mockUserRepository.Setup(x => x.ExistsByUsernameAsync(command.Username))
            .ReturnsAsync(true);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<ApplicationException>(
            () => _handler.Handle(command, CancellationToken.None));
        
        exception.Message.Should().Be("Username is already taken");
    }

    [Fact]
    public async Task Handle_ShouldThrowException_WhenEmailAlreadyExists()
    {
        // Arrange
        var command = new RegisterUserCommand
        {
            Username = "testuser",
            Email = "<EMAIL>",
            Password = "password123",
            UserType = UserType.Shipper
        };

        _mockUserRepository.Setup(x => x.ExistsByUsernameAsync(command.Username))
            .ReturnsAsync(false);
        _mockUserRepository.Setup(x => x.ExistsByEmailAsync(command.Email))
            .ReturnsAsync(true);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<ApplicationException>(
            () => _handler.Handle(command, CancellationToken.None));
        
        exception.Message.Should().Be("Email is already registered");
    }

    [Fact]
    public async Task Handle_ShouldSetPhoneNumber_WhenProvided()
    {
        // Arrange
        var command = new RegisterUserCommand
        {
            Username = "testuser",
            Email = "<EMAIL>",
            Password = "password123",
            UserType = UserType.Shipper,
            PhoneNumber = "+1234567890"
        };

        _mockUserRepository.Setup(x => x.ExistsByUsernameAsync(command.Username))
            .ReturnsAsync(false);
        _mockUserRepository.Setup(x => x.ExistsByEmailAsync(command.Email))
            .ReturnsAsync(false);
        _mockPasswordHasher.Setup(x => x.HashPassword(command.Password))
            .Returns("hashedpassword");
        _mockUserRepository.Setup(x => x.AddAsync(It.IsAny<User>()))
            .Returns(Task.CompletedTask);
        _mockMessageBroker.Setup(x => x.PublishAsync(It.IsAny<string>(), It.IsAny<UserRegisteredEvent>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBe(Guid.Empty);
        _mockUserRepository.Verify(x => x.AddAsync(It.Is<User>(u => u.PhoneNumber == command.PhoneNumber)), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldPublishCorrectIntegrationEvent()
    {
        // Arrange
        var command = new RegisterUserCommand
        {
            Username = "testuser",
            Email = "<EMAIL>",
            Password = "password123",
            UserType = UserType.TransportCompany
        };

        _mockUserRepository.Setup(x => x.ExistsByUsernameAsync(command.Username))
            .ReturnsAsync(false);
        _mockUserRepository.Setup(x => x.ExistsByEmailAsync(command.Email))
            .ReturnsAsync(false);
        _mockPasswordHasher.Setup(x => x.HashPassword(command.Password))
            .Returns("hashedpassword");
        _mockUserRepository.Setup(x => x.AddAsync(It.IsAny<User>()))
            .Returns(Task.CompletedTask);
        _mockMessageBroker.Setup(x => x.PublishAsync(It.IsAny<string>(), It.IsAny<UserRegisteredEvent>()))
            .Returns(Task.CompletedTask);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockMessageBroker.Verify(x => x.PublishAsync("user.registered", 
            It.Is<UserRegisteredEvent>(e => 
                e.Email == command.Email &&
                e.Username == command.Username &&
                e.UserType == UserType.TransportCompany.ToString())), Times.Once);
    }
}
