using FluentValidation;
using MobileWorkflow.Application.Commands.UIComponents;

namespace MobileWorkflow.Application.Validators
{
    public class CreateUIComponentCommandValidator : AbstractValidator<CreateUIComponentCommand>
    {
        public CreateUIComponentCommandValidator()
        {
            RuleFor(x => x.Name)
                .NotEmpty()
                .WithMessage("Component name is required")
                .MaximumLength(100)
                .WithMessage("Component name cannot exceed 100 characters");

            RuleFor(x => x.DisplayName)
                .NotEmpty()
                .WithMessage("Display name is required")
                .MaximumLength(200)
                .WithMessage("Display name cannot exceed 200 characters");

            RuleFor(x => x.ComponentType)
                .NotEmpty()
                .WithMessage("Component type is required")
                .Must(BeValidComponentType)
                .WithMessage("Invalid component type");

            RuleFor(x => x.Category)
                .NotEmpty()
                .WithMessage("Category is required")
                .MaximumLength(50)
                .WithMessage("Category cannot exceed 50 characters");

            RuleFor(x => x.CreatedBy)
                .NotEmpty()
                .WithMessage("CreatedBy is required");

            RuleFor(x => x.SupportedPlatforms)
                .NotEmpty()
                .WithMessage("At least one supported platform is required")
                .Must(HaveValidPlatforms)
                .WithMessage("Invalid platform specified");

            RuleFor(x => x.Version)
                .NotEmpty()
                .WithMessage("Version is required")
                .Matches(@"^\d+\.\d+(\.\d+)?$")
                .WithMessage("Version must be in format x.y or x.y.z");
        }

        private bool BeValidComponentType(string componentType)
        {
            var validTypes = new[] { "button", "input", "card", "list", "modal", "navigation", "form", "chart", "table", "image", "video", "map" };
            return validTypes.Contains(componentType.ToLowerInvariant());
        }

        private bool HaveValidPlatforms(List<string> platforms)
        {
            var validPlatforms = new[] { "ios", "android", "web", "pwa" };
            return platforms.All(p => validPlatforms.Contains(p.ToLowerInvariant()));
        }
    }

    public class CreateThemeCommandValidator : AbstractValidator<CreateThemeCommand>
    {
        public CreateThemeCommandValidator()
        {
            RuleFor(x => x.Name)
                .NotEmpty()
                .WithMessage("Theme name is required")
                .MaximumLength(100)
                .WithMessage("Theme name cannot exceed 100 characters");

            RuleFor(x => x.DisplayName)
                .NotEmpty()
                .WithMessage("Display name is required")
                .MaximumLength(200)
                .WithMessage("Display name cannot exceed 200 characters");

            RuleFor(x => x.CreatedBy)
                .NotEmpty()
                .WithMessage("CreatedBy is required");

            RuleFor(x => x.SupportedPlatforms)
                .NotEmpty()
                .WithMessage("At least one supported platform is required")
                .Must(HaveValidPlatforms)
                .WithMessage("Invalid platform specified");

            RuleFor(x => x.ColorPalette)
                .NotNull()
                .WithMessage("Color palette is required");

            RuleFor(x => x.Version)
                .NotEmpty()
                .WithMessage("Version is required")
                .Matches(@"^\d+\.\d+(\.\d+)?$")
                .WithMessage("Version must be in format x.y or x.y.z");
        }

        private bool HaveValidPlatforms(List<string> platforms)
        {
            var validPlatforms = new[] { "ios", "android", "web", "pwa" };
            return platforms.All(p => validPlatforms.Contains(p.ToLowerInvariant()));
        }
    }
}
