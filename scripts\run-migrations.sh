#!/bin/bash

# TLI Microservices - Database Migration and Seeding Script
# This script runs Entity Framework migrations and seeds data for all services

set -e  # Exit on any error

# Default values
ENVIRONMENT="Development"
SEED_DATA=true
FORCE=false
SERVICES=("DataStorage" "MonitoringObservability")

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        --no-seed)
            SEED_DATA=false
            shift
            ;;
        --force)
            FORCE=true
            shift
            ;;
        --services)
            IFS=',' read -ra SERVICES <<< "$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --environment ENV    Set environment (default: Development)"
            echo "  --no-seed           Skip data seeding"
            echo "  --force             Continue on errors"
            echo "  --services LIST     Comma-separated list of services"
            echo "  --help              Show this help"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${CYAN}🚀 TLI Microservices - Database Migration and Seeding${NC}"
    echo -e "${CYAN}Environment: $ENVIRONMENT${NC}"
    echo -e "${CYAN}Services: ${SERVICES[*]}${NC}"
    echo ""
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_processing() {
    echo -e "${YELLOW}🔄 $1${NC}"
}

# Function to check if PostgreSQL is running
check_postgresql() {
    print_processing "Checking PostgreSQL connection..."
    
    if command -v psql >/dev/null 2>&1; then
        if PGPASSWORD=timescale psql -h localhost -p 5432 -U timescale -d postgres -c '\q' >/dev/null 2>&1; then
            print_success "PostgreSQL connection successful"
            return 0
        fi
    fi
    
    print_error "Cannot connect to PostgreSQL. Please ensure PostgreSQL is running and accessible."
    print_info "Connection details: Host=localhost, Port=5432, User=timescale"
    print_info "Run the setup-databases.sql script first if you haven't already."
    return 1
}

# Function to check prerequisites
check_prerequisites() {
    print_processing "Checking prerequisites..."
    
    # Check .NET CLI
    if command -v dotnet >/dev/null 2>&1; then
        DOTNET_VERSION=$(dotnet --version)
        print_success ".NET CLI found: $DOTNET_VERSION"
    else
        print_error ".NET CLI not found. Please install .NET 8 SDK."
        exit 1
    fi
    
    # Check Entity Framework tools
    if dotnet ef --version >/dev/null 2>&1; then
        print_success "Entity Framework tools found"
    else
        print_warning "Installing Entity Framework tools..."
        dotnet tool install --global dotnet-ef
        if [ $? -ne 0 ]; then
            print_error "Failed to install Entity Framework tools"
            exit 1
        fi
    fi
    
    # Check PostgreSQL
    if ! check_postgresql; then
        exit 1
    fi
}

# Function to run EF migrations
run_migrations() {
    local service_name=$1
    local service_path=$2
    local project_name=$3
    local context_name=$4
    local assembly_name=$5
    
    print_processing "Running migrations for $service_name..."
    
    local original_dir=$(pwd)
    cd "$service_path" || exit 1
    
    # Check if migrations exist
    local migrations_path="../$assembly_name/Migrations"
    if [ ! -d "$migrations_path" ]; then
        print_warning "No migrations found for $service_name, creating initial migration..."
        
        # Create initial migration
        dotnet ef migrations add InitialCreate \
            --context "$context_name" \
            --project "../$assembly_name" \
            --startup-project "$project_name"
        
        if [ $? -ne 0 ]; then
            print_error "Failed to create initial migration for $service_name"
            cd "$original_dir"
            return 1
        fi
    fi
    
    # Apply migrations
    local update_args=(
        "ef" "database" "update"
        "--context" "$context_name"
        "--project" "../$assembly_name"
        "--startup-project" "$project_name"
        "--environment" "$ENVIRONMENT"
    )
    
    if [ "$FORCE" = true ]; then
        update_args+=("--force")
    fi
    
    dotnet "${update_args[@]}"
    
    if [ $? -ne 0 ]; then
        print_error "Failed to apply migrations for $service_name"
        cd "$original_dir"
        return 1
    fi
    
    cd "$original_dir"
    print_success "Migrations completed for $service_name"
    return 0
}

# Function to configure data seeding
configure_seeding() {
    local service_name=$1
    
    print_processing "Configuring data seeding for $service_name..."
    
    print_info "Data seeding will occur on application startup for $service_name"
    print_info "Set environment variable SEED_DATA=true when starting the service"
    
    print_success "Data seeding configured for $service_name"
}

# Main execution
print_status

check_prerequisites

echo ""

# Process each service
for service in "${SERVICES[@]}"; do
    case $service in
        "DataStorage")
            service_path="Services/DataStorage/DataStorage.API"
            project_name="DataStorage.API.csproj"
            context_name="DataStorageDbContext"
            assembly_name="DataStorage.Infrastructure"
            ;;
        "MonitoringObservability")
            service_path="Services/MonitoringObservability/MonitoringObservability.API"
            project_name="MonitoringObservability.API.csproj"
            context_name="MonitoringDbContext"
            assembly_name="MonitoringObservability.Infrastructure"
            ;;
        *)
            print_warning "Unknown service: $service. Skipping..."
            continue
            ;;
    esac
    
    echo -e "${CYAN}📦 Processing $service...${NC}"
    
    # Check if service project exists
    if [ ! -d "$service_path" ]; then
        print_warning "Service path not found: $service_path. Skipping..."
        continue
    fi
    
    # Run migrations
    if run_migrations "$service" "$service_path" "$project_name" "$context_name" "$assembly_name"; then
        # Configure seeding if requested
        if [ "$SEED_DATA" = true ]; then
            configure_seeding "$service"
        fi
        
        print_success "$service completed successfully"
    else
        print_error "Failed to process $service"
        if [ "$FORCE" != true ]; then
            print_info "Use --force parameter to continue on errors"
            exit 1
        fi
    fi
    
    echo ""
done

print_success "Migration and seeding process completed!"
echo ""
echo -e "${CYAN}Next steps:${NC}"
echo -e "${NC}1. Start the services using docker-compose up or individual dotnet run commands${NC}"
echo -e "${NC}2. Verify services are healthy by checking their /health endpoints${NC}"
echo -e "${NC}3. Test the API Gateway at http://localhost:5000${NC}"
echo ""
echo -e "${CYAN}Service endpoints:${NC}"
echo -e "${NC}- API Gateway: http://localhost:5000${NC}"
echo -e "${NC}- Data Storage: http://localhost:5010${NC}"
echo -e "${NC}- Monitoring: http://localhost:5011${NC}"
