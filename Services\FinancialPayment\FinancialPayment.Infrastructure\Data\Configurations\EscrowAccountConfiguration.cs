using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.Enums;

namespace FinancialPayment.Infrastructure.Data.Configurations;

public class EscrowAccountConfiguration : IEntityTypeConfiguration<EscrowAccount>
{
    public void Configure(EntityTypeBuilder<EscrowAccount> builder)
    {
        builder.ToTable("escrow_accounts");

        builder.<PERSON><PERSON>ey(e => e.Id);

        builder.Property(e => e.Id)
            .HasColumnName("id")
            .ValueGeneratedNever();

        builder.Property(e => e.OrderId)
            .HasColumnName("order_id")
            .IsRequired();

        builder.Property(e => e.TransportCompanyId)
            .HasColumnName("transport_company_id")
            .IsRequired();

        builder.Property(e => e.BrokerId)
            .HasColumnName("broker_id")
            .IsRequired();

        builder.Property(e => e.CarrierId)
            .HasColumnName("carrier_id")
            .IsRequired();

        builder.Property(e => e.EscrowAccountNumber)
            .HasColumnName("escrow_account_number")
            .HasMaxLength(50)
            .IsRequired();

        // Money value objects
        builder.OwnsOne(e => e.TotalAmount, money =>
        {
            money.Property(m => m.Amount)
                .HasColumnName("total_amount")
                .HasColumnType("decimal(18,2)")
                .IsRequired();

            money.Property(m => m.Currency)
                .HasColumnName("total_currency")
                .HasMaxLength(3)
                .IsRequired();
        });

        builder.OwnsOne(e => e.AvailableAmount, money =>
        {
            money.Property(m => m.Amount)
                .HasColumnName("available_amount")
                .HasColumnType("decimal(18,2)")
                .IsRequired();

            money.Property(m => m.Currency)
                .HasColumnName("available_currency")
                .HasMaxLength(3)
                .IsRequired();
        });

        builder.OwnsOne(e => e.ReservedAmount, money =>
        {
            money.Property(m => m.Amount)
                .HasColumnName("reserved_amount")
                .HasColumnType("decimal(18,2)")
                .IsRequired();

            money.Property(m => m.Currency)
                .HasColumnName("reserved_currency")
                .HasMaxLength(3)
                .IsRequired();
        });

        builder.Property(e => e.Status)
            .HasColumnName("status")
            .HasConversion<int>()
            .IsRequired();

        builder.Property(e => e.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(e => e.FundedAt)
            .HasColumnName("funded_at");

        builder.Property(e => e.ReleasedAt)
            .HasColumnName("released_at");

        builder.Property(e => e.ReleaseReason)
            .HasColumnName("release_reason")
            .HasMaxLength(500);

        builder.Property(e => e.Notes)
            .HasColumnName("notes")
            .HasMaxLength(1000);

        // Relationships
        builder.HasMany(e => e.Transactions)
            .WithOne(t => t.EscrowAccount)
            .HasForeignKey(t => t.EscrowAccountId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.Milestones)
            .WithOne(m => m.EscrowAccount)
            .HasForeignKey(m => m.EscrowAccountId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(e => e.OrderId)
            .IsUnique()
            .HasDatabaseName("ix_escrow_accounts_order_id");

        builder.HasIndex(e => e.EscrowAccountNumber)
            .IsUnique()
            .HasDatabaseName("ix_escrow_accounts_account_number");

        builder.HasIndex(e => e.TransportCompanyId)
            .HasDatabaseName("ix_escrow_accounts_transport_company_id");

        builder.HasIndex(e => e.BrokerId)
            .HasDatabaseName("ix_escrow_accounts_broker_id");

        builder.HasIndex(e => e.CarrierId)
            .HasDatabaseName("ix_escrow_accounts_carrier_id");

        builder.HasIndex(e => e.Status)
            .HasDatabaseName("ix_escrow_accounts_status");

        builder.HasIndex(e => e.CreatedAt)
            .HasDatabaseName("ix_escrow_accounts_created_at");
    }
}
