using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MobileWorkflow.Application.Commands.Milestone;
using MobileWorkflow.Application.DTOs;
using MobileWorkflow.Application.Queries.Milestone;

namespace MobileWorkflow.API.Controllers;

[ApiController]
[Route("api/milestone-templates/{templateId}/[controller]")]
[Authorize]
public class MilestoneStepController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<MilestoneStepController> _logger;

    public MilestoneStepController(IMediator mediator, ILogger<MilestoneStepController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get all milestone steps for a template
    /// </summary>
    [HttpGet]
    [Authorize(Roles = "Admin,Manager,User")]
    public async Task<ActionResult<IEnumerable<MilestoneStepDto>>> GetMilestoneSteps(
        Guid templateId,
        [FromQuery] bool? activeOnly = null)
    {
        try
        {
            IEnumerable<MilestoneStepDto> steps;

            if (activeOnly == true)
            {
                steps = await _mediator.Send(new GetActiveMilestoneStepsByTemplateIdQuery { TemplateId = templateId });
            }
            else
            {
                steps = await _mediator.Send(new GetMilestoneStepsByTemplateIdQuery { TemplateId = templateId });
            }

            return Ok(steps);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting milestone steps for template: {TemplateId}", templateId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get required milestone steps for a template
    /// </summary>
    [HttpGet("required")]
    [Authorize(Roles = "Admin,Manager,User")]
    public async Task<ActionResult<IEnumerable<MilestoneStepDto>>> GetRequiredMilestoneSteps(Guid templateId)
    {
        try
        {
            var steps = await _mediator.Send(new GetRequiredMilestoneStepsByTemplateIdQuery { TemplateId = templateId });
            return Ok(steps);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting required milestone steps for template: {TemplateId}", templateId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get milestone step by ID
    /// </summary>
    [HttpGet("{stepId}")]
    [Authorize(Roles = "Admin,Manager,User")]
    public async Task<ActionResult<MilestoneStepDto>> GetMilestoneStep(Guid templateId, Guid stepId)
    {
        try
        {
            var step = await _mediator.Send(new GetMilestoneStepByIdQuery { Id = stepId });
            if (step == null)
            {
                return NotFound($"Milestone step with ID {stepId} not found");
            }

            if (step.MilestoneTemplateId != templateId)
            {
                return BadRequest("Step does not belong to the specified template");
            }

            return Ok(step);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting milestone step: {StepId}", stepId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Create a new milestone step
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<MilestoneStepDto>> CreateMilestoneStep(Guid templateId, [FromBody] CreateMilestoneStepRequest request)
    {
        try
        {
            var command = new CreateMilestoneStepCommand
            {
                MilestoneTemplateId = templateId,
                Name = request.Name,
                Description = request.Description,
                SequenceNumber = request.SequenceNumber,
                IsRequired = request.IsRequired,
                TriggerCondition = request.TriggerCondition,
                Configuration = request.Configuration,
                Metadata = request.Metadata,
                PayoutRules = request.PayoutRules
            };

            var result = await _mediator.Send(command);
            return CreatedAtAction(nameof(GetMilestoneStep), new { templateId, stepId = result.Id }, result);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating milestone step for template: {TemplateId}", templateId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Update milestone step
    /// </summary>
    [HttpPut("{stepId}")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<MilestoneStepDto>> UpdateMilestoneStep(Guid templateId, Guid stepId, [FromBody] UpdateMilestoneStepRequest request)
    {
        try
        {
            var command = new UpdateMilestoneStepCommand
            {
                Id = stepId,
                Name = request.Name,
                Description = request.Description,
                IsRequired = request.IsRequired,
                TriggerCondition = request.TriggerCondition,
                Configuration = request.Configuration,
                Metadata = request.Metadata
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating milestone step: {StepId}", stepId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Delete milestone step
    /// </summary>
    [HttpDelete("{stepId}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> DeleteMilestoneStep(Guid templateId, Guid stepId)
    {
        try
        {
            var command = new DeleteMilestoneStepCommand { Id = stepId };
            var result = await _mediator.Send(command);

            if (result)
            {
                return NoContent();
            }

            return BadRequest("Failed to delete milestone step");
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting milestone step: {StepId}", stepId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Activate milestone step
    /// </summary>
    [HttpPost("{stepId}/activate")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<MilestoneStepDto>> ActivateMilestoneStep(Guid templateId, Guid stepId)
    {
        try
        {
            var command = new ActivateMilestoneStepCommand { Id = stepId };
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error activating milestone step: {StepId}", stepId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Deactivate milestone step
    /// </summary>
    [HttpPost("{stepId}/deactivate")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<MilestoneStepDto>> DeactivateMilestoneStep(Guid templateId, Guid stepId)
    {
        try
        {
            var command = new DeactivateMilestoneStepCommand { Id = stepId };
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deactivating milestone step: {StepId}", stepId);
            return StatusCode(500, "Internal server error");
        }
    }
}

[ApiController]
[Route("api/milestone-steps/{stepId}/payout-rules")]
[Authorize]
public class MilestonePayoutRuleController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<MilestonePayoutRuleController> _logger;

    public MilestonePayoutRuleController(IMediator mediator, ILogger<MilestonePayoutRuleController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get all payout rules for a milestone step
    /// </summary>
    [HttpGet]
    [Authorize(Roles = "Admin,Manager,User")]
    public async Task<ActionResult<IEnumerable<MilestonePayoutRuleDto>>> GetMilestonePayoutRules(Guid stepId)
    {
        try
        {
            var rules = await _mediator.Send(new GetMilestonePayoutRulesByStepIdQuery { StepId = stepId });
            return Ok(rules);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting payout rules for step: {StepId}", stepId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get payout rule by ID
    /// </summary>
    [HttpGet("{ruleId}")]
    [Authorize(Roles = "Admin,Manager,User")]
    public async Task<ActionResult<MilestonePayoutRuleDto>> GetMilestonePayoutRule(Guid stepId, Guid ruleId)
    {
        try
        {
            var rule = await _mediator.Send(new GetMilestonePayoutRuleByIdQuery { Id = ruleId });
            if (rule == null)
            {
                return NotFound($"Payout rule with ID {ruleId} not found");
            }

            if (rule.MilestoneStepId != stepId)
            {
                return BadRequest("Payout rule does not belong to the specified step");
            }

            return Ok(rule);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting payout rule: {RuleId}", ruleId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Create a new payout rule
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<MilestonePayoutRuleDto>> CreateMilestonePayoutRule(Guid stepId, [FromBody] CreateMilestonePayoutRuleRequest request)
    {
        try
        {
            var command = new CreateMilestonePayoutRuleCommand
            {
                MilestoneStepId = stepId,
                PayoutPercentage = request.PayoutPercentage,
                TriggerCondition = request.TriggerCondition,
                Description = request.Description,
                Configuration = request.Configuration,
                Metadata = request.Metadata
            };

            var result = await _mediator.Send(command);
            return CreatedAtAction(nameof(GetMilestonePayoutRule), new { stepId, ruleId = result.Id }, result);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating payout rule for step: {StepId}", stepId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Update payout rule
    /// </summary>
    [HttpPut("{ruleId}")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<MilestonePayoutRuleDto>> UpdateMilestonePayoutRule(Guid stepId, Guid ruleId, [FromBody] UpdateMilestonePayoutRuleRequest request)
    {
        try
        {
            var command = new UpdateMilestonePayoutRuleCommand
            {
                Id = ruleId,
                PayoutPercentage = request.PayoutPercentage,
                TriggerCondition = request.TriggerCondition,
                Description = request.Description,
                Configuration = request.Configuration,
                Metadata = request.Metadata
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating payout rule: {RuleId}", ruleId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Delete payout rule
    /// </summary>
    [HttpDelete("{ruleId}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> DeleteMilestonePayoutRule(Guid stepId, Guid ruleId)
    {
        try
        {
            var command = new DeleteMilestonePayoutRuleCommand { Id = ruleId };
            var result = await _mediator.Send(command);

            if (result)
            {
                return NoContent();
            }

            return BadRequest("Failed to delete payout rule");
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting payout rule: {RuleId}", ruleId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get total payout percentage for template
    /// </summary>
    [HttpGet("~/api/milestone-templates/{templateId}/total-payout")]
    [Authorize(Roles = "Admin,Manager,User")]
    public async Task<ActionResult<decimal>> GetTotalPayoutPercentage(Guid templateId)
    {
        try
        {
            var total = await _mediator.Send(new GetTotalPayoutPercentageByTemplateQuery { TemplateId = templateId });
            return Ok(total);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting total payout percentage for template: {TemplateId}", templateId);
            return StatusCode(500, "Internal server error");
        }
    }
}
