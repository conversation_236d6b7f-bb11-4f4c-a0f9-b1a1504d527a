# Use the official .NET SDK image for building
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy project files
COPY ["AuditCompliance.API/AuditCompliance.API.csproj", "AuditCompliance.API/"]
COPY ["AuditCompliance.Application/AuditCompliance.Application.csproj", "AuditCompliance.Application/"]
COPY ["AuditCompliance.Domain/AuditCompliance.Domain.csproj", "AuditCompliance.Domain/"]
COPY ["AuditCompliance.Infrastructure/AuditCompliance.Infrastructure.csproj", "AuditCompliance.Infrastructure/"]

# Copy shared projects
COPY ["../../Shared/Shared.Domain/Shared.Domain.csproj", "../../Shared/Shared.Domain/"]
COPY ["../../Shared/Shared.Infrastructure/Shared.Infrastructure.csproj", "../../Shared/Shared.Infrastructure/"]

# Restore dependencies
RUN dotnet restore "AuditCompliance.API/AuditCompliance.API.csproj"

# Copy all source code
COPY . .

# Build the application
WORKDIR "/src/AuditCompliance.API"
RUN dotnet build "AuditCompliance.API.csproj" -c Release -o /app/build

# Publish the application
FROM build AS publish
RUN dotnet publish "AuditCompliance.API.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Use the official .NET runtime image
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS final
WORKDIR /app

# Install curl for health checks
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

# Copy the published application
COPY --from=publish /app/publish .

# Create logs directory
RUN mkdir -p /app/logs

# Expose ports
EXPOSE 80
EXPOSE 443

# Set the entry point
ENTRYPOINT ["dotnet", "AuditCompliance.API.dll"]
