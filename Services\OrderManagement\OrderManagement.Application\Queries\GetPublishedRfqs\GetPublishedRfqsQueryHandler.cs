using MediatR;
using AutoMapper;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;
using OrderManagement.Application.DTOs;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Application.Queries.GetPublishedRfqs;

public class GetPublishedRfqsQueryHandler : IRequestHandler<GetPublishedRfqsQuery, PagedResult<RfqSummaryDto>>
{
    private readonly IRfqRepository _rfqRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetPublishedRfqsQueryHandler> _logger;

    public GetPublishedRfqsQueryHandler(
        IRfqRepository rfqRepository,
        IMapper mapper,
        ILogger<GetPublishedRfqsQueryHandler> logger)
    {
        _rfqRepository = rfqRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<PagedResult<RfqSummaryDto>> Handle(GetPublishedRfqsQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting published RFQs, Page: {Page}, PageSize: {PageSize}", 
            request.Page, request.PageSize);

        // Parse load type filter if provided
        LoadType? loadTypeFilter = null;
        if (!string.IsNullOrEmpty(request.LoadType) && Enum.TryParse<LoadType>(request.LoadType, true, out var parsedLoadType))
        {
            loadTypeFilter = parsedLoadType;
        }

        // Get published RFQs with filters
        var rfqs = await _rfqRepository.GetPublishedRfqsAsync(
            request.Page,
            request.PageSize,
            request.FromDate,
            request.ToDate,
            request.PickupCity,
            request.DeliveryCity,
            loadTypeFilter,
            request.MaxBudget,
            request.ExcludeExpired,
            cancellationToken);

        // Get total count for pagination
        var totalCount = await _rfqRepository.GetPublishedRfqsCountAsync(
            request.FromDate,
            request.ToDate,
            request.PickupCity,
            request.DeliveryCity,
            loadTypeFilter,
            request.MaxBudget,
            request.ExcludeExpired,
            cancellationToken);

        var rfqDtos = _mapper.Map<List<RfqSummaryDto>>(rfqs);

        _logger.LogInformation("Retrieved {Count} published RFQs out of {TotalCount}",
            rfqs.Count, totalCount);

        return new PagedResult<RfqSummaryDto>
        {
            Items = rfqDtos,
            TotalCount = totalCount,
            Page = request.Page,
            PageSize = request.PageSize
        };
    }
}
