using Shared.Domain.Common;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Domain.Entities;

public class EscrowTransaction : BaseEntity
{
    public Guid EscrowAccountId { get; private set; }
    public EscrowTransactionType Type { get; private set; }
    public Money Amount { get; private set; }
    public Guid? ParticipantId { get; private set; } // TransportCompany, Broker, Carrier, etc.
    public string? PaymentGatewayTransactionId { get; private set; }
    public string? Notes { get; private set; }

    public DateTime? ProcessedAt { get; private set; }
    public EscrowTransactionStatus Status { get; private set; }
    public string? FailureReason { get; private set; }

    // Navigation properties
    public EscrowAccount EscrowAccount { get; private set; } = null!;

    private EscrowTransaction() { }

    public EscrowTransaction(
        Guid escrowAccountId,
        EscrowTransactionType type,
        Money amount,
        Guid? participantId,
        string? paymentGatewayTransactionId = null,
        string? notes = null)
    {
        if (amount == null || amount.Amount <= 0)
            throw new ArgumentException("Amount must be greater than zero");

        Id = Guid.NewGuid();
        EscrowAccountId = escrowAccountId;
        Type = type;
        Amount = amount;
        ParticipantId = participantId;
        PaymentGatewayTransactionId = paymentGatewayTransactionId;
        Notes = notes;
        CreatedAt = DateTime.UtcNow;
        Status = EscrowTransactionStatus.Pending;
    }

    public void MarkAsProcessed(string? paymentGatewayTransactionId = null)
    {
        if (Status != EscrowTransactionStatus.Pending)
            throw new InvalidOperationException("Only pending transactions can be marked as processed");

        Status = EscrowTransactionStatus.Completed;
        ProcessedAt = DateTime.UtcNow;

        if (!string.IsNullOrEmpty(paymentGatewayTransactionId))
        {
            PaymentGatewayTransactionId = paymentGatewayTransactionId;
        }
    }

    public void MarkAsFailed(string failureReason)
    {
        if (Status != EscrowTransactionStatus.Pending)
            throw new InvalidOperationException("Only pending transactions can be marked as failed");

        Status = EscrowTransactionStatus.Failed;
        FailureReason = failureReason;
        ProcessedAt = DateTime.UtcNow;
    }

    public void Retry()
    {
        if (Status != EscrowTransactionStatus.Failed)
            throw new InvalidOperationException("Only failed transactions can be retried");

        Status = EscrowTransactionStatus.Pending;
        FailureReason = null;
        ProcessedAt = null;
    }
}
