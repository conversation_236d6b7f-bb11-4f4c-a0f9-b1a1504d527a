using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NetworkFleetManagement.Application.Commands.Documents;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Application.Queries.Documents;
using NetworkFleetManagement.Domain.Enums;

namespace NetworkFleetManagement.API.Controllers;

[ApiController]
[Route("api/v1/[controller]")]
[Authorize]
public class DocumentsController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<DocumentsController> _logger;

    public DocumentsController(IMediator mediator, ILogger<DocumentsController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Upload carrier document
    /// </summary>
    [HttpPost("carrier/{carrierId:guid}")]
    public async Task<ActionResult<CarrierDocumentDto>> UploadCarrierDocument(
        Guid carrierId,
        [FromForm] UploadCarrierDocumentDto uploadDto,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var command = new UploadCarrierDocumentCommand(carrierId, uploadDto);
            var result = await _mediator.Send(command, cancellationToken);
            
            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while uploading carrier document");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading carrier document for {CarrierId}", carrierId);
            return StatusCode(500, "An error occurred while uploading the document");
        }
    }

    /// <summary>
    /// Upload vehicle document
    /// </summary>
    [HttpPost("vehicle/{vehicleId:guid}")]
    public async Task<ActionResult<VehicleDocumentDto>> UploadVehicleDocument(
        Guid vehicleId,
        [FromForm] UploadVehicleDocumentDto uploadDto,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var command = new UploadVehicleDocumentCommand(vehicleId, uploadDto);
            var result = await _mediator.Send(command, cancellationToken);
            
            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while uploading vehicle document");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading vehicle document for {VehicleId}", vehicleId);
            return StatusCode(500, "An error occurred while uploading the document");
        }
    }

    /// <summary>
    /// Upload driver document
    /// </summary>
    [HttpPost("driver/{driverId:guid}")]
    public async Task<ActionResult<DriverDocumentDto>> UploadDriverDocument(
        Guid driverId,
        [FromForm] UploadDriverDocumentDto uploadDto,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var command = new UploadDriverDocumentCommand(driverId, uploadDto);
            var result = await _mediator.Send(command, cancellationToken);
            
            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while uploading driver document");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading driver document for {DriverId}", driverId);
            return StatusCode(500, "An error occurred while uploading the document");
        }
    }

    /// <summary>
    /// Verify document
    /// </summary>
    [HttpPut("{documentId:guid}/verify")]
    public async Task<ActionResult> VerifyDocument(
        Guid documentId,
        [FromBody] VerifyDocumentDto verifyDto,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var command = new VerifyDocumentCommand(documentId, verifyDto);
            await _mediator.Send(command, cancellationToken);
            
            return Ok();
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while verifying document");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error verifying document {DocumentId}", documentId);
            return StatusCode(500, "An error occurred while verifying the document");
        }
    }

    /// <summary>
    /// Reject document
    /// </summary>
    [HttpPut("{documentId:guid}/reject")]
    public async Task<ActionResult> RejectDocument(
        Guid documentId,
        [FromBody] RejectDocumentDto rejectDto,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var command = new RejectDocumentCommand(documentId, rejectDto);
            await _mediator.Send(command, cancellationToken);
            
            return Ok();
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while rejecting document");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error rejecting document {DocumentId}", documentId);
            return StatusCode(500, "An error occurred while rejecting the document");
        }
    }

    /// <summary>
    /// Get carrier documents
    /// </summary>
    [HttpGet("carrier/{carrierId:guid}")]
    public async Task<ActionResult<IEnumerable<CarrierDocumentDto>>> GetCarrierDocuments(
        Guid carrierId,
        [FromQuery] DocumentType? documentType = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetCarrierDocumentsQuery(carrierId, documentType);
            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving carrier documents for {CarrierId}", carrierId);
            return StatusCode(500, "An error occurred while retrieving carrier documents");
        }
    }

    /// <summary>
    /// Get vehicle documents
    /// </summary>
    [HttpGet("vehicle/{vehicleId:guid}")]
    public async Task<ActionResult<IEnumerable<VehicleDocumentDto>>> GetVehicleDocuments(
        Guid vehicleId,
        [FromQuery] DocumentType? documentType = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetVehicleDocumentsQuery(vehicleId, documentType);
            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving vehicle documents for {VehicleId}", vehicleId);
            return StatusCode(500, "An error occurred while retrieving vehicle documents");
        }
    }

    /// <summary>
    /// Get driver documents
    /// </summary>
    [HttpGet("driver/{driverId:guid}")]
    public async Task<ActionResult<IEnumerable<DriverDocumentDto>>> GetDriverDocuments(
        Guid driverId,
        [FromQuery] DocumentType? documentType = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetDriverDocumentsQuery(driverId, documentType);
            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving driver documents for {DriverId}", driverId);
            return StatusCode(500, "An error occurred while retrieving driver documents");
        }
    }

    /// <summary>
    /// Get expired documents
    /// </summary>
    [HttpGet("expired")]
    public async Task<ActionResult<ExpiredDocumentsDto>> GetExpiredDocuments(
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetExpiredDocumentsQuery();
            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving expired documents");
            return StatusCode(500, "An error occurred while retrieving expired documents");
        }
    }

    /// <summary>
    /// Get documents requiring verification
    /// </summary>
    [HttpGet("verification-required")]
    public async Task<ActionResult<DocumentsRequiringVerificationDto>> GetDocumentsRequiringVerification(
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetDocumentsRequiringVerificationQuery();
            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving documents requiring verification");
            return StatusCode(500, "An error occurred while retrieving documents requiring verification");
        }
    }

    /// <summary>
    /// Get documents expiring soon
    /// </summary>
    [HttpGet("expiring-soon")]
    public async Task<ActionResult<DocumentsExpiringSoonDto>> GetDocumentsExpiringSoon(
        [FromQuery] int daysThreshold = 30,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetDocumentsExpiringSoonQuery(daysThreshold);
            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving documents expiring soon");
            return StatusCode(500, "An error occurred while retrieving documents expiring soon");
        }
    }

    /// <summary>
    /// Download document
    /// </summary>
    [HttpGet("{documentId:guid}/download")]
    public async Task<ActionResult> DownloadDocument(
        Guid documentId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new DownloadDocumentQuery(documentId);
            var result = await _mediator.Send(query, cancellationToken);
            
            if (result == null)
                return NotFound($"Document with ID {documentId} not found");
            
            return File(result.FileContent, result.MimeType, result.FileName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error downloading document {DocumentId}", documentId);
            return StatusCode(500, "An error occurred while downloading the document");
        }
    }
}
