# 👥 User Management Service API Documentation

## 📋 Overview

The User Management Service handles user profiles, KYC verification, document management, and user lifecycle operations. This service is **85% complete** with comprehensive admin features, real-time notifications, and advanced KYC processing capabilities.

**Base URL**: `/api/v1/users` (via API Gateway)
**Direct URL**: `http://localhost:5002`
**Authentication**: Required for all endpoints
**Authorization**: Role-based access control
**Real-time**: SignalR hub for live updates

## 🚀 Current Implementation Status

### ✅ Fully Implemented Features

- **User Profile Management**: Complete CRUD operations with validation
- **Admin Panel**: Advanced user search, approval workflows, account management
- **KYC Processing**: OCR integration, document verification, approval tracking
- **Real-time Notifications**: SignalR hub for live status updates
- **Document Management**: Upload, verification, and approval workflows
- **Audit Trail**: Comprehensive logging of all admin actions

### 🔄 In Progress Features

- **Advanced Search**: Enhanced filtering and search capabilities
- **Bulk Operations**: Mass user operations and data export
- **Mobile Integration**: Enhanced mobile app support

## 🔐 Authentication & Permissions

### Required Permissions

| Operation          | Permission          | Roles                    |
| ------------------ | ------------------- | ------------------------ |
| View own profile   | `users.read.own`    | All authenticated users  |
| View any profile   | `users.read.all`    | Admin, Broker (limited)  |
| Create profile     | `users.create`      | Admin, Self-registration |
| Update own profile | `users.update.own`  | All authenticated users  |
| Update any profile | `users.update.all`  | Admin                    |
| Delete profile     | `users.delete`      | Admin only               |
| Approve/Reject KYC | `users.kyc.approve` | Admin, KYC Officer       |
| Bulk operations    | `users.bulk`        | Admin only               |

## 📊 Core Endpoints

### 1. User Profile Management (`/api/userprofiles`)

#### Create User Profile

```http
POST /api/userprofiles
Authorization: Bearer <token>
Content-Type: application/json

{
  "userId": "123e4567-e89b-12d3-a456-************",
  "userType": "Broker|TransportCompany|Carrier|Driver|Shipper",
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "phoneNumber": "+1234567890"
}
```

#### Get User Profile by ID

```http
GET /api/userprofiles/{id}
Authorization: Bearer <token>
```

#### Get User Profile by User ID

```http
GET /api/userprofiles/user/{userId}
Authorization: Bearer <token>
```

#### Update Personal Details

```http
PUT /api/userprofiles/{id}/personal-details
Authorization: Bearer <token>
Content-Type: application/json

{
  "firstName": "John",
  "lastName": "Doe",
  "phoneNumber": "+1234567890"
}
```

#### Update Company Details

```http
PUT /api/userprofiles/{id}/company-details
Authorization: Bearer <token>
Content-Type: application/json

{
  "companyName": "ABC Logistics",
  "companyType": "Private",
  "registrationNumber": "REG123456",
  "taxIdentificationNumber": "TAX123456"
}
```

#### Update Address Details

```http
PUT /api/userprofiles/{id}/address
Authorization: Bearer <token>
Content-Type: application/json

{
  "street": "123 Main St",
  "city": "New York",
  "state": "NY",
  "postalCode": "10001",
  "country": "USA"
}
```

#### Submit Profile for Review

```http
POST /api/userprofiles/{id}/submit-for-review
Authorization: Bearer <token>
```

**Response:**

```json
{
  "id": "123e4567-e89b-12d3-a456-************",
  "userId": "user-uuid",
  "userType": "Broker|Transporter|Shipper",
  "status": "Pending|Approved|Rejected|Suspended",
  "personalDetails": {
    "firstName": "John",
    "lastName": "Doe",
    "dateOfBirth": "1990-01-01",
    "phoneNumber": "+1234567890",
    "alternatePhoneNumber": "+**********",
    "email": "<EMAIL>",
    "alternateEmail": "<EMAIL>",
    "profilePictureUrl": "https://storage.tli.com/profiles/user-id.jpg"
  },
  "companyDetails": {
    "companyName": "ABC Logistics",
    "companyType": "Private|Public|Partnership|Sole Proprietorship",
    "registrationNumber": "REG123456",
    "taxIdentificationNumber": "TAX123456",
    "incorporationDate": "2020-01-01",
    "website": "https://abclogistics.com",
    "description": "Leading logistics provider",
    "employeeCount": "1-10|11-50|51-200|201-500|500+",
    "annualRevenue": "< 1M|1M-10M|10M-50M|50M+",
    "businessLicense": "LIC123456",
    "companyLogoUrl": "https://storage.tli.com/logos/company-id.jpg"
  },
  "addressDetails": {
    "street": "123 Main St",
    "city": "New York",
    "state": "NY",
    "postalCode": "10001",
    "country": "USA",
    "coordinates": {
      "latitude": 40.7128,
      "longitude": -74.006
    }
  },
  "kycStatus": {
    "overallStatus": "Pending|InReview|Approved|Rejected",
    "documentsSubmitted": true,
    "documentsApproved": false,
    "profileCompleted": true,
    "lastReviewDate": "2024-01-01T00:00:00Z",
    "reviewComments": "Additional documents required"
  },
  "metadata": {
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z",
    "lastLoginAt": "2024-01-01T00:00:00Z",
    "profileCompletionPercentage": 85,
    "isActive": true,
    "tags": ["premium-customer", "verified"]
  }
}
```

#### Get Current User Profile

```http
GET /api/v1/users/profile/me
Authorization: Bearer <token>
```

#### Create User Profile

```http
POST /api/v1/users/profile
Authorization: Bearer <token>
Content-Type: application/json

{
  "userType": "Broker",
  "personalDetails": {
    "firstName": "John",
    "lastName": "Doe",
    "dateOfBirth": "1990-01-01",
    "phoneNumber": "+1234567890",
    "email": "<EMAIL>"
  },
  "companyDetails": {
    "companyName": "ABC Logistics",
    "companyType": "Private",
    "registrationNumber": "REG123456"
  },
  "addressDetails": {
    "street": "123 Main St",
    "city": "New York",
    "state": "NY",
    "postalCode": "10001",
    "country": "USA"
  }
}
```

#### Update Personal Details

```http
PUT /api/v1/users/profile/{userId}/personal
Authorization: Bearer <token>
Content-Type: application/json

{
  "firstName": "John",
  "lastName": "Doe",
  "dateOfBirth": "1990-01-01",
  "phoneNumber": "+1234567890",
  "alternatePhoneNumber": "+**********",
  "email": "<EMAIL>",
  "alternateEmail": "<EMAIL>"
}
```

#### Update Company Details

```http
PUT /api/v1/users/profile/{userId}/company
Authorization: Bearer <token>
Content-Type: application/json

{
  "companyName": "ABC Logistics Updated",
  "companyType": "Private",
  "registrationNumber": "REG123456",
  "taxIdentificationNumber": "TAX123456",
  "website": "https://abclogistics.com",
  "description": "Updated description",
  "employeeCount": "51-200",
  "annualRevenue": "10M-50M"
}
```

#### Update Address Details

```http
PUT /api/v1/users/profile/{userId}/address
Authorization: Bearer <token>
Content-Type: application/json

{
  "street": "456 Updated St",
  "city": "New York",
  "state": "NY",
  "postalCode": "10002",
  "country": "USA",
  "coordinates": {
    "latitude": 40.7128,
    "longitude": -74.0060
  }
}
```

### 2. Document Management

#### Get User Documents

```http
GET /api/v1/users/{userId}/documents
Authorization: Bearer <token>
```

**Query Parameters:**

- `documentType` (optional): Filter by document type
- `status` (optional): Filter by approval status
- `page` (default: 1): Page number
- `pageSize` (default: 20): Items per page

**Response:**

```json
{
  "data": [
    {
      "id": "doc-uuid",
      "documentType": "PAN|Aadhaar|DrivingLicense|CompanyRegistration|TaxCertificate|BankStatement",
      "fileName": "pan_card.pdf",
      "fileUrl": "https://storage.tli.com/documents/doc-uuid.pdf",
      "fileSize": 1024000,
      "mimeType": "application/pdf",
      "uploadedAt": "2024-01-01T00:00:00Z",
      "status": "Pending|Approved|Rejected",
      "reviewComments": "Document is clear and valid",
      "reviewedAt": "2024-01-01T00:00:00Z",
      "reviewedBy": "admin-uuid",
      "expiryDate": "2030-01-01",
      "ocrData": {
        "extractedText": "PAN Card details...",
        "confidence": 0.95,
        "fields": {
          "panNumber": "**********",
          "name": "John Doe",
          "fatherName": "Father Name"
        }
      }
    }
  ],
  "pagination": {
    "currentPage": 1,
    "pageSize": 20,
    "totalItems": 5,
    "totalPages": 1,
    "hasNextPage": false,
    "hasPreviousPage": false
  }
}
```

#### Upload Document

```http
POST /api/v1/users/{userId}/documents
Authorization: Bearer <token>
Content-Type: multipart/form-data

{
  "documentType": "PAN",
  "file": <binary-file-data>,
  "description": "PAN Card for KYC verification"
}
```

#### Update Document

```http
PUT /api/v1/users/{userId}/documents/{documentId}
Authorization: Bearer <token>
Content-Type: multipart/form-data

{
  "file": <binary-file-data>,
  "description": "Updated PAN Card"
}
```

#### Delete Document

```http
DELETE /api/v1/users/{userId}/documents/{documentId}
Authorization: Bearer <token>
```

### 3. KYC Management

#### Submit Profile for Review

```http
POST /api/v1/users/{userId}/submit-for-review
Authorization: Bearer <token>
```

#### Approve User Profile

```http
POST /api/v1/users/{userId}/approve
Authorization: Bearer <token>
Content-Type: application/json

{
  "comments": "Profile approved after verification",
  "approvedBy": "admin-uuid"
}
```

#### Reject User Profile

```http
POST /api/v1/users/{userId}/reject
Authorization: Bearer <token>
Content-Type: application/json

{
  "reason": "Incomplete documentation",
  "comments": "Please submit valid PAN card",
  "rejectedBy": "admin-uuid"
}
```

#### Get KYC Status

```http
GET /api/v1/users/{userId}/kyc-status
Authorization: Bearer <token>
```

**Response:**

```json
{
  "userId": "user-uuid",
  "overallStatus": "Pending|InReview|Approved|Rejected",
  "documentsStatus": {
    "totalRequired": 5,
    "submitted": 4,
    "approved": 2,
    "rejected": 1,
    "pending": 1
  },
  "profileCompletionStatus": {
    "personalDetails": true,
    "companyDetails": true,
    "addressDetails": false,
    "overallCompletion": 85
  },
  "timeline": [
    {
      "status": "ProfileCreated",
      "timestamp": "2024-01-01T00:00:00Z",
      "comments": "Profile created successfully"
    },
    {
      "status": "DocumentsSubmitted",
      "timestamp": "2024-01-02T00:00:00Z",
      "comments": "KYC documents uploaded"
    }
  ]
}
```

## 🔧 Admin Management Endpoints (`/api/admin`)

### Get Pending Approvals

```http
GET /api/admin/pending-approvals
Authorization: Bearer <token>
```

**Query Parameters:**

- `pageNumber`: Page number (default: 1)
- `pageSize`: Items per page (default: 20)

### Get Dashboard Statistics

```http
GET /api/admin/dashboard-stats
Authorization: Bearer <token>
```

### Advanced User Search

```http
GET /api/admin/users/advanced
Authorization: Bearer <token>
```

**Query Parameters:**

- `searchTerm`: Search across multiple fields
- `userType`: Filter by user type
- `status`: Filter by approval status
- `kycStatus`: Filter by KYC status
- `registrationDateFrom`: Filter by registration date
- `registrationDateTo`: Filter by registration date
- `lastLoginFrom`: Filter by last login date
- `lastLoginTo`: Filter by last login date
- `pageNumber`: Page number
- `pageSize`: Items per page

### Approve User

```http
POST /api/admin/users/{userId}/approve
Authorization: Bearer <token>
Content-Type: application/json

{
  "notes": "All documents verified successfully"
}
```

### Reject User

```http
POST /api/admin/users/{userId}/reject
Authorization: Bearer <token>
Content-Type: application/json

{
  "reason": "Invalid documentation",
  "notes": "PAN card details do not match"
}
```

### Approve Documents

```http
POST /api/admin/documents/{documentSubmissionId}/approve
Authorization: Bearer <token>
Content-Type: application/json

{
  "notes": "All documents are valid and verified"
}
```

### Reject Documents

```http
POST /api/admin/documents/{documentSubmissionId}/reject
Authorization: Bearer <token>
Content-Type: application/json

{
  "reason": "Invalid documents",
  "notes": "GST certificate is expired"
}
```

### Process KYC with OCR

```http
POST /api/admin/kyc/process-ocr
Authorization: Bearer <token>
Content-Type: application/json

{
  "documentSubmissionId": "123e4567-e89b-12d3-a456-************",
  "documentType": "PAN|GST|Aadhar|DrivingLicense",
  "ocrProvider": "AWS|Azure|Google"
}
```

### Manage User Account

```http
POST /api/admin/users/{userId}/manage
Authorization: Bearer <token>
Content-Type: application/json

{
  "action": "Activate|Deactivate|Suspend|ResetPassword|UpdateSubscription",
  "reason": "Account management action",
  "notes": "Additional notes for the action"
}
```

### Export User Summary

```http
GET /api/admin/export/user-summary
Authorization: Bearer <token>
```

**Query Parameters:**

- `format`: CSV|Excel|PDF
- `userType`: Filter by user type
- `status`: Filter by status
- `dateFrom`: Filter by date range
- `dateTo`: Filter by date range

### Export KYC Documents

```http
GET /api/admin/export/kyc-documents
Authorization: Bearer <token>
```

**Query Parameters:**

- `format`: ZIP|PDF
- `userIds`: Comma-separated user IDs
- `documentTypes`: Comma-separated document types

## 📄 Document Management (`/api/documents`)

### Upload Document

```http
POST /api/documents/upload
Authorization: Bearer <token>
Content-Type: multipart/form-data

{
  "userId": "123e4567-e89b-12d3-a456-************",
  "documentType": "PAN|GST|Aadhar|DrivingLicense|CompanyRegistration",
  "file": <binary-file-data>
}
```

### Get User Documents

```http
GET /api/documents/user/{userId}
Authorization: Bearer <token>
```

## 🔄 Real-time Features (SignalR Hub)

### Connection Endpoint

```
/hubs/usermanagement
```

### Client Events

- `UserApproved`: User approval notification
- `UserRejected`: User rejection notification
- `DocumentsApproved`: Document approval notification
- `DocumentsRejected`: Document rejection notification
- `DocumentOcrProcessed`: OCR processing completion
- `ProfileStatusChanged`: Profile status updates
- `NewUserRegistered`: New user registration alerts
- `DocumentSubmitted`: Document submission notifications

## 📋 Additional Endpoints

### User Search & Filtering

```http
GET /api/v1/users/search
Authorization: Bearer <token>
```

**Query Parameters:**

- `searchTerm`: Search in name, email, company
- `userType`: Filter by user type
- `status`: Filter by approval status
- `companyName`: Filter by company
- `city`: Filter by city
- `state`: Filter by state
- `country`: Filter by country
- `createdFrom`: Filter by creation date (from)
- `createdTo`: Filter by creation date (to)
- `page`: Page number
- `pageSize`: Items per page
- `sortBy`: Sort field (name, createdAt, etc.)
- `sortDirection`: asc|desc

### Bulk Operations

```http
POST /api/v1/users/bulk/approve
Authorization: Bearer <token>
Content-Type: application/json

{
  "userIds": ["uuid1", "uuid2", "uuid3"],
  "comments": "Bulk approval after verification"
}
```

### User Statistics

```http
GET /api/v1/users/statistics
Authorization: Bearer <token>
```

**Response:**

```json
{
  "totalUsers": 1000,
  "usersByType": {
    "Broker": 300,
    "Transporter": 500,
    "Shipper": 200
  },
  "usersByStatus": {
    "Pending": 100,
    "Approved": 800,
    "Rejected": 50,
    "Suspended": 50
  },
  "kycStatistics": {
    "pendingReview": 150,
    "approvedToday": 25,
    "rejectedToday": 5
  },
  "registrationTrends": [
    {
      "date": "2024-01-01",
      "registrations": 15
    }
  ]
}
```

## 🔄 Real-time Updates

### SignalR Hub: `/api/v1/users/hub`

**Events:**

- `UserApproved` - User profile approved
- `UserRejected` - User profile rejected
- `DocumentsApproved` - Documents approved
- `DocumentsRejected` - Documents rejected
- `DocumentOcrProcessed` - OCR processing completed
- `ProfileStatusChanged` - Profile status updated
- `NewUserRegistered` - New user registration

**Example Usage:**

```typescript
const connection = new signalR.HubConnectionBuilder()
  .withUrl('/api/v1/users/hub')
  .build()

connection.on('UserApproved', (data) => {
  console.log('User approved:', data)
  // Update UI accordingly
})
```

## ❌ Error Responses

### Common Error Codes

| Code | Message                    | Description                     |
| ---- | -------------------------- | ------------------------------- |
| 400  | `INVALID_USER_TYPE`        | Invalid user type provided      |
| 400  | `PROFILE_INCOMPLETE`       | Profile is incomplete           |
| 400  | `INVALID_DOCUMENT_TYPE`    | Invalid document type           |
| 401  | `UNAUTHORIZED`             | Authentication required         |
| 403  | `INSUFFICIENT_PERMISSIONS` | Insufficient permissions        |
| 404  | `USER_NOT_FOUND`           | User profile not found          |
| 404  | `DOCUMENT_NOT_FOUND`       | Document not found              |
| 409  | `PROFILE_ALREADY_EXISTS`   | Profile already exists for user |
| 409  | `PROFILE_ALREADY_APPROVED` | Profile is already approved     |
| 422  | `VALIDATION_FAILED`        | Input validation failed         |
| 429  | `RATE_LIMIT_EXCEEDED`      | Too many requests               |

### Error Response Format

```json
{
  "error": {
    "code": "VALIDATION_FAILED",
    "message": "Input validation failed",
    "details": [
      {
        "field": "personalDetails.email",
        "message": "Invalid email format"
      }
    ],
    "timestamp": "2024-01-01T00:00:00Z",
    "traceId": "trace-uuid"
  }
}
```
