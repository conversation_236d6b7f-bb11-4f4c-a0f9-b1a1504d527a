using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace AnalyticsBIService.Infrastructure.Persistence;

public class AnalyticsEventConfiguration : IEntityTypeConfiguration<AnalyticsEvent>
{
    public void Configure(EntityTypeBuilder<AnalyticsEvent> builder)
    {
        builder.ToTable("analytics_events", "analytics");

        builder.HasKey(e => e.Id);

        builder.Property(e => e.Id)
            .HasColumnName("id")
            .ValueGeneratedNever();

        builder.Property(e => e.EventName)
            .HasColumnName("event_name")
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(e => e.EventType)
            .HasColumnName("event_type")
            .HasConversion<string>()
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(e => e.DataSource)
            .HasColumnName("data_source")
            .HasConversion<string>()
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(e => e.UserId)
            .HasColumnName("user_id");

        builder.Property(e => e.UserType)
            .HasColumnName("user_type")
            .HasConversion<string>()
            .HasMaxLength(50);

        builder.Property(e => e.EntityId)
            .HasColumnName("entity_id");

        builder.Property(e => e.EntityType)
            .HasColumnName("entity_type")
            .HasMaxLength(100);

        builder.Property(e => e.Timestamp)
            .HasColumnName("timestamp")
            .IsRequired();

        builder.Property(e => e.Properties)
            .HasColumnName("properties")
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        builder.Property(e => e.Metadata)
            .HasColumnName("metadata")
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        builder.Property(e => e.SessionId)
            .HasColumnName("session_id")
            .HasMaxLength(100);

        builder.Property(e => e.IpAddress)
            .HasColumnName("ip_address")
            .HasMaxLength(45);

        builder.Property(e => e.UserAgent)
            .HasColumnName("user_agent")
            .HasMaxLength(500);

        builder.Property(e => e.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(e => e.UpdatedAt)
            .HasColumnName("updated_at");

        // Indexes
        builder.HasIndex(e => e.Timestamp)
            .HasDatabaseName("IX_analytics_events_timestamp");

        builder.HasIndex(e => new { e.EventType, e.Timestamp })
            .HasDatabaseName("IX_analytics_events_event_type_timestamp");

        builder.HasIndex(e => new { e.UserId, e.Timestamp })
            .HasDatabaseName("IX_analytics_events_user_id_timestamp");

        builder.HasIndex(e => new { e.DataSource, e.Timestamp })
            .HasDatabaseName("IX_analytics_events_data_source_timestamp");

        builder.HasIndex(e => e.EventName)
            .HasDatabaseName("IX_analytics_events_event_name");

        // GIN indexes for JSONB columns
        builder.HasIndex(e => e.Properties)
            .HasDatabaseName("IX_analytics_events_properties_gin")
            .HasMethod("gin");

        builder.HasIndex(e => e.Metadata)
            .HasDatabaseName("IX_analytics_events_metadata_gin")
            .HasMethod("gin");
    }
}

public class MetricConfiguration : IEntityTypeConfiguration<Metric>
{
    public void Configure(EntityTypeBuilder<Metric> builder)
    {
        builder.ToTable("metrics", "analytics");

        builder.HasKey(m => m.Id);

        builder.Property(m => m.Id)
            .HasColumnName("id")
            .ValueGeneratedNever();

        builder.Property(m => m.Name)
            .HasColumnName("name")
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(m => m.Description)
            .HasColumnName("description")
            .HasMaxLength(1000)
            .IsRequired();

        builder.Property(m => m.Type)
            .HasColumnName("type")
            .HasConversion<string>()
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(m => m.Category)
            .HasColumnName("category")
            .HasConversion<string>()
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(m => m.UserId)
            .HasColumnName("user_id");

        builder.Property(m => m.UserType)
            .HasColumnName("user_type")
            .HasConversion<string>()
            .HasMaxLength(50);

        builder.Property(m => m.DataSource)
            .HasColumnName("data_source")
            .HasConversion<string>()
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(m => m.Tags)
            .HasColumnName("tags")
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new Dictionary<string, string>())
            .HasColumnType("jsonb");

        builder.Property(m => m.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(m => m.UpdatedAt)
            .HasColumnName("updated_at");

        // Indexes
        builder.HasIndex(m => new { m.Name, m.UserId })
            .HasDatabaseName("IX_metrics_name_user_id");

        builder.HasIndex(m => new { m.Category, m.Type })
            .HasDatabaseName("IX_metrics_category_type");

        builder.HasIndex(m => m.CreatedAt)
            .HasDatabaseName("IX_metrics_created_at");

        // GIN index for tags
        builder.HasIndex(m => m.Tags)
            .HasDatabaseName("IX_metrics_tags_gin")
            .HasMethod("gin");
    }
}

public class DashboardConfiguration : IEntityTypeConfiguration<Dashboard>
{
    public void Configure(EntityTypeBuilder<Dashboard> builder)
    {
        builder.ToTable("dashboards", "analytics");

        builder.HasKey(d => d.Id);

        builder.Property(d => d.Id)
            .HasColumnName("id")
            .ValueGeneratedNever();

        builder.Property(d => d.Name)
            .HasColumnName("name")
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(d => d.Description)
            .HasColumnName("description")
            .HasMaxLength(1000)
            .IsRequired();

        builder.Property(d => d.Type)
            .HasColumnName("type")
            .HasConversion<string>()
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(d => d.UserId)
            .HasColumnName("user_id");

        builder.Property(d => d.UserType)
            .HasColumnName("user_type")
            .HasConversion<string>()
            .HasMaxLength(50);

        builder.Property(d => d.IsDefault)
            .HasColumnName("is_default")
            .IsRequired();

        builder.Property(d => d.IsPublic)
            .HasColumnName("is_public")
            .IsRequired();

        builder.Property(d => d.Configuration)
            .HasColumnName("configuration")
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        builder.Property(d => d.LastAccessedAt)
            .HasColumnName("last_accessed_at");

        builder.Property(d => d.AccessCount)
            .HasColumnName("access_count")
            .IsRequired();

        builder.Property(d => d.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(d => d.UpdatedAt)
            .HasColumnName("updated_at");

        // Relationships
        builder.HasMany(d => d.Widgets)
            .WithOne()
            .HasForeignKey("DashboardId")
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(d => new { d.UserId, d.Type })
            .HasDatabaseName("IX_dashboards_user_id_type");

        builder.HasIndex(d => d.IsDefault)
            .HasDatabaseName("IX_dashboards_is_default");
    }
}
