using Shared.Domain.ValueObjects;

namespace UserManagement.Domain.ValueObjects;

/// <summary>
/// Value object representing personal information
/// </summary>
public class PersonalInfo : ValueObject
{
    public string FirstName { get; private set; }
    public string LastName { get; private set; }
    public string Email { get; private set; }
    public string PhoneNumber { get; private set; }
    public string? AlternatePhoneNumber { get; private set; }
    public string? AlternateEmail { get; private set; }
    public DateTime? DateOfBirth { get; private set; }
    public string? ProfilePictureUrl { get; private set; }

    private PersonalInfo()
    {
        FirstName = string.Empty;
        LastName = string.Empty;
        Email = string.Empty;
        PhoneNumber = string.Empty;
    }

    public PersonalInfo(
        string firstName,
        string lastName,
        string email,
        string phoneNumber,
        string? alternatePhoneNumber = null,
        string? alternateEmail = null,
        DateTime? dateOfBirth = null,
        string? profilePictureUrl = null)
    {
        if (string.IsNullOrWhiteSpace(firstName))
            throw new ArgumentException("First name cannot be empty", nameof(firstName));

        if (string.IsNullOrWhiteSpace(lastName))
            throw new ArgumentException("Last name cannot be empty", nameof(lastName));

        if (string.IsNullOrWhiteSpace(email))
            throw new ArgumentException("Email cannot be empty", nameof(email));

        if (string.IsNullOrWhiteSpace(phoneNumber))
            throw new ArgumentException("Phone number cannot be empty", nameof(phoneNumber));

        FirstName = firstName.Trim();
        LastName = lastName.Trim();
        Email = email.Trim().ToLowerInvariant();
        PhoneNumber = phoneNumber.Trim();
        AlternatePhoneNumber = alternatePhoneNumber?.Trim();
        AlternateEmail = alternateEmail?.Trim().ToLowerInvariant();
        DateOfBirth = dateOfBirth;
        ProfilePictureUrl = profilePictureUrl?.Trim();
    }

    public static PersonalInfo Create(
        string firstName,
        string lastName,
        string email,
        string phoneNumber,
        string? alternatePhoneNumber = null,
        string? alternateEmail = null,
        DateTime? dateOfBirth = null,
        string? profilePictureUrl = null)
    {
        return new PersonalInfo(firstName, lastName, email, phoneNumber, 
            alternatePhoneNumber, alternateEmail, dateOfBirth, profilePictureUrl);
    }

    public PersonalInfo WithFirstName(string firstName)
    {
        return new PersonalInfo(firstName, LastName, Email, PhoneNumber, 
            AlternatePhoneNumber, AlternateEmail, DateOfBirth, ProfilePictureUrl);
    }

    public PersonalInfo WithLastName(string lastName)
    {
        return new PersonalInfo(FirstName, lastName, Email, PhoneNumber, 
            AlternatePhoneNumber, AlternateEmail, DateOfBirth, ProfilePictureUrl);
    }

    public PersonalInfo WithEmail(string email)
    {
        return new PersonalInfo(FirstName, LastName, email, PhoneNumber, 
            AlternatePhoneNumber, AlternateEmail, DateOfBirth, ProfilePictureUrl);
    }

    public PersonalInfo WithPhoneNumber(string phoneNumber)
    {
        return new PersonalInfo(FirstName, LastName, Email, phoneNumber, 
            AlternatePhoneNumber, AlternateEmail, DateOfBirth, ProfilePictureUrl);
    }

    public PersonalInfo WithProfilePicture(string? profilePictureUrl)
    {
        return new PersonalInfo(FirstName, LastName, Email, PhoneNumber, 
            AlternatePhoneNumber, AlternateEmail, DateOfBirth, profilePictureUrl);
    }

    public string GetFullName() => $"{FirstName} {LastName}";

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return FirstName;
        yield return LastName;
        yield return Email;
        yield return PhoneNumber;
        yield return AlternatePhoneNumber ?? string.Empty;
        yield return AlternateEmail ?? string.Empty;
        yield return DateOfBirth ?? DateTime.MinValue;
        yield return ProfilePictureUrl ?? string.Empty;
    }
}
