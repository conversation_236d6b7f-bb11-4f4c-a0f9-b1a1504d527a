using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Domain.Repositories;
using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace AnalyticsBIService.Application.Queries.Carrier;

/// <summary>
/// Handler for carrier rating trends queries
/// </summary>
public class GetCarrierRatingTrendsQueryHandler : IRequestHandler<GetCarrierRatingTrendsQuery, CarrierRatingTrendsDto>
{
    private readonly IAnalyticsEventRepository _analyticsEventRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetCarrierRatingTrendsQueryHandler> _logger;

    public GetCarrierRatingTrendsQueryHandler(
        IAnalyticsEventRepository analyticsEventRepository,
        IMapper mapper,
        ILogger<GetCarrierRatingTrendsQueryHandler> logger)
    {
        _analyticsEventRepository = analyticsEventRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<CarrierRatingTrendsDto> Handle(GetCarrierRatingTrendsQuery request, CancellationToken cancellationToken)
    {
        _logger.LogDebug("Getting rating trends for carrier {CarrierId}", request.CarrierId);

        // Get rating events for this carrier
        var allEvents = await _analyticsEventRepository.GetByDateRangeAsync(request.FromDate, request.ToDate, 1, 10000, cancellationToken);
        var ratingEvents = allEvents.Items
            .Where(e => e.UserId == request.CarrierId &&
                       e.EventName.Contains("Rating"))
            .ToList();

        // Generate overall rating trends
        var overallRatingTrends = GenerateOverallRatingTrends(request.FromDate, request.ToDate, 4.2m);

        // Generate category-wise trends
        var categoryRatingTrends = new List<CategoryRatingTrendDto>
        {
            new()
            {
                Category = "Service Quality",
                TrendData = GenerateRatingTrendData(request.FromDate, request.ToDate, 4.3m),
                OverallTrend = 0.15m,
                TrendDirection = "Improving"
            },
            new()
            {
                Category = "Communication",
                TrendData = GenerateRatingTrendData(request.FromDate, request.ToDate, 4.1m),
                OverallTrend = 0.08m,
                TrendDirection = "Improving"
            },
            new()
            {
                Category = "Timeliness",
                TrendData = GenerateRatingTrendData(request.FromDate, request.ToDate, 4.0m),
                OverallTrend = -0.02m,
                TrendDirection = "Stable"
            },
            new()
            {
                Category = "Professionalism",
                TrendData = GenerateRatingTrendData(request.FromDate, request.ToDate, 4.4m),
                OverallTrend = 0.12m,
                TrendDirection = "Improving"
            }
        };

        // Generate source-wise trends
        var sourceRatingTrends = new List<SourceRatingTrendDto>
        {
            new()
            {
                Source = "Broker",
                TrendData = GenerateRatingTrendData(request.FromDate, request.ToDate, 4.3m),
                OverallTrend = 0.18m,
                TrendDirection = "Improving"
            },
            new()
            {
                Source = "Shipper",
                TrendData = GenerateRatingTrendData(request.FromDate, request.ToDate, 4.1m),
                OverallTrend = 0.05m,
                TrendDirection = "Stable"
            }
        };

        // Generate seasonal patterns
        var seasonalPatterns = new List<SeasonalRatingPatternDto>
        {
            new()
            {
                Season = "Q1",
                AverageRating = 4.1m,
                RatingVariability = 0.3m,
                Pattern = "Stable",
                InfluencingFactors = new List<string> { "Post-holiday adjustment", "Weather challenges" }
            },
            new()
            {
                Season = "Q2",
                AverageRating = 4.3m,
                RatingVariability = 0.2m,
                Pattern = "Peak",
                InfluencingFactors = new List<string> { "Good weather", "Increased business activity" }
            },
            new()
            {
                Season = "Q3",
                AverageRating = 4.2m,
                RatingVariability = 0.25m,
                Pattern = "Stable",
                InfluencingFactors = new List<string> { "Summer operations", "Vacation periods" }
            },
            new()
            {
                Season = "Q4",
                AverageRating = 4.0m,
                RatingVariability = 0.4m,
                Pattern = "Low",
                InfluencingFactors = new List<string> { "Holiday rush", "Weather challenges", "High demand" }
            }
        };

        return new CarrierRatingTrendsDto
        {
            OverallRatingTrends = overallRatingTrends,
            CategoryRatingTrends = categoryRatingTrends,
            SourceRatingTrends = sourceRatingTrends,
            SeasonalPatterns = seasonalPatterns
        };
    }

    private List<RatingTrendDataDto> GenerateOverallRatingTrends(DateTime fromDate, DateTime toDate, decimal baseRating)
    {
        return GenerateRatingTrendData(fromDate, toDate, baseRating);
    }

    private List<RatingTrendDataDto> GenerateRatingTrendData(DateTime fromDate, DateTime toDate, decimal baseRating)
    {
        var days = (toDate - fromDate).Days;
        var trends = new List<RatingTrendDataDto>();
        var previousRating = baseRating;

        for (var date = fromDate; date <= toDate; date = date.AddDays(7)) // Weekly trends
        {
            var variance = (decimal)(new Random().NextDouble() * 0.3 - 0.15); // ±0.15 rating variance
            var rating = Math.Max(1, Math.Min(5, baseRating + variance));
            var ratingCount = new Random().Next(3, 12);
            var ratingChange = rating - previousRating;
            var trendDirection = Math.Abs(ratingChange) < 0.05m ? "Stable" :
                               ratingChange > 0 ? "Improving" : "Declining";

            trends.Add(new RatingTrendDataDto
            {
                Date = date,
                AverageRating = rating,
                RatingCount = ratingCount,
                RatingChange = ratingChange,
                TrendDirection = trendDirection
            });

            previousRating = rating;
        }

        return trends;
    }
}
