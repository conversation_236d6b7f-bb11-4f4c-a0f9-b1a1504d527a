using AuditCompliance.Application.Commands;
using AuditCompliance.Application.DTOs;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace AuditCompliance.API.Controllers
{
    /// <summary>
    /// Enhanced export controller with comprehensive audit export features
    /// </summary>
    [ApiController]
    [Route("api/audit/export")]
    [Authorize]
    public class EnhancedExportController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly ILogger<EnhancedExportController> _logger;

        public EnhancedExportController(IMediator mediator, ILogger<EnhancedExportController> logger)
        {
            _mediator = mediator;
            _logger = logger;
        }

        /// <summary>
        /// Export audit trail with enhanced features
        /// </summary>
        [HttpPost]
        [Authorize(Roles = "Admin,Auditor,ComplianceOfficer")]
        public async Task<ActionResult<EnhancedAuditExportResponseDto>> ExportAuditTrail([FromBody] EnhancedAuditExportRequestDto request)
        {
            try
            {
                // Set audit fields
                request.RequestedBy = GetCurrentUserId();
                request.RequestedByRole = GetCurrentUserRole();
                request.IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
                request.UserAgent = HttpContext.Request.Headers["User-Agent"].ToString();

                var command = new EnhancedAuditExportCommand
                {
                    UserIds = request.UserIds,
                    ActionTypes = request.ActionTypes,
                    EntityTypes = request.EntityTypes,
                    ModuleNames = request.ModuleNames,
                    FromDate = request.FromDate,
                    ToDate = request.ToDate,
                    SeverityLevels = request.SeverityLevels,
                    IpAddresses = request.IpAddresses,
                    ComplianceFlags = request.ComplianceFlags,
                    IncludeSystemActions = request.IncludeSystemActions,
                    IncludeSensitiveData = request.IncludeSensitiveData && User.IsInRole("Admin"), // Only admins can access sensitive data
                    Format = request.Format,
                    SelectedColumns = request.SelectedColumns,
                    MaxRecords = request.MaxRecords,
                    PageSize = request.PageSize,
                    EnableBackgroundProcessing = request.EnableBackgroundProcessing,
                    RequireDigitalSignature = request.RequireDigitalSignature,
                    NotificationEmail = request.NotificationEmail,
                    EnableCompression = request.EnableCompression,
                    ExportTitle = request.ExportTitle,
                    CustomFilters = request.CustomFilters,
                    RequestedBy = request.RequestedBy,
                    RequestedByRole = request.RequestedByRole,
                    IpAddress = request.IpAddress,
                    UserAgent = request.UserAgent
                };

                var result = await _mediator.Send(command);

                if (result.IsBackgroundProcessing)
                {
                    return Accepted(result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing enhanced audit export");
                return StatusCode(500, new { Message = "Internal server error", Error = ex.Message });
            }
        }

        /// <summary>
        /// Export multiple audit trails in bulk
        /// </summary>
        [HttpPost("bulk")]
        [Authorize(Roles = "Admin,Auditor")]
        public async Task<ActionResult<BulkAuditExportResponseDto>> BulkExportAuditTrail([FromBody] BulkAuditExportRequestDto request)
        {
            try
            {
                // Set audit fields for all requests
                var userId = GetCurrentUserId();
                var userRole = GetCurrentUserRole();
                var ipAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
                var userAgent = HttpContext.Request.Headers["User-Agent"].ToString();

                var command = new BulkAuditExportCommand
                {
                    ExportCommands = request.ExportRequests.Select(r => new EnhancedAuditExportCommand
                    {
                        UserIds = r.UserIds,
                        ActionTypes = r.ActionTypes,
                        EntityTypes = r.EntityTypes,
                        ModuleNames = r.ModuleNames,
                        FromDate = r.FromDate,
                        ToDate = r.ToDate,
                        SeverityLevels = r.SeverityLevels,
                        IpAddresses = r.IpAddresses,
                        ComplianceFlags = r.ComplianceFlags,
                        IncludeSystemActions = r.IncludeSystemActions,
                        IncludeSensitiveData = r.IncludeSensitiveData && User.IsInRole("Admin"),
                        Format = r.Format,
                        SelectedColumns = r.SelectedColumns,
                        MaxRecords = r.MaxRecords,
                        PageSize = r.PageSize,
                        EnableBackgroundProcessing = true, // Force background processing for bulk
                        RequireDigitalSignature = r.RequireDigitalSignature,
                        NotificationEmail = r.NotificationEmail,
                        EnableCompression = r.EnableCompression,
                        ExportTitle = r.ExportTitle,
                        CustomFilters = r.CustomFilters,
                        RequestedBy = userId,
                        RequestedByRole = userRole,
                        IpAddress = ipAddress,
                        UserAgent = userAgent
                    }).ToList(),
                    ProcessInParallel = request.ProcessInParallel,
                    BatchName = request.BatchName,
                    CreateZipArchive = request.CreateZipArchive,
                    NotificationEmail = request.NotificationEmail,
                    GlobalSettings = request.GlobalSettings,
                    RequestedBy = userId,
                    RequestedByRole = userRole,
                    IpAddress = ipAddress,
                    UserAgent = userAgent
                };

                var result = await _mediator.Send(command);
                return Accepted(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing bulk audit export");
                return StatusCode(500, new { Message = "Internal server error", Error = ex.Message });
            }
        }

        /// <summary>
        /// Get export job status
        /// </summary>
        [HttpGet("{jobId}/status")]
        [Authorize(Roles = "Admin,Auditor,ComplianceOfficer")]
        public async Task<ActionResult<ExportJobStatusDto>> GetExportStatus(string jobId)
        {
            try
            {
                var command = new GetExportJobStatusCommand
                {
                    JobId = jobId,
                    RequestedBy = GetCurrentUserId()
                };

                var result = await _mediator.Send(command);
                
                if (result == null)
                {
                    return NotFound(new { Message = "Export job not found" });
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting export status for job {JobId}", jobId);
                return StatusCode(500, new { Message = "Internal server error", Error = ex.Message });
            }
        }

        /// <summary>
        /// Cancel export job
        /// </summary>
        [HttpPost("{jobId}/cancel")]
        [Authorize(Roles = "Admin,Auditor")]
        public async Task<ActionResult<bool>> CancelExport(string jobId, [FromBody] CancelExportRequestDto request)
        {
            try
            {
                var command = new CancelExportJobCommand
                {
                    JobId = jobId,
                    RequestedBy = GetCurrentUserId(),
                    Reason = request.Reason
                };

                var result = await _mediator.Send(command);
                return Ok(new { Success = result, Message = result ? "Export cancelled successfully" : "Failed to cancel export" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cancelling export job {JobId}", jobId);
                return StatusCode(500, new { Message = "Internal server error", Error = ex.Message });
            }
        }

        /// <summary>
        /// Download export file
        /// </summary>
        [HttpGet("{exportId}/download")]
        [Authorize(Roles = "Admin,Auditor,ComplianceOfficer")]
        public async Task<ActionResult> DownloadExport(string exportId, [FromQuery] bool verifySignature = true)
        {
            try
            {
                var command = new DownloadExportFileCommand
                {
                    ExportId = exportId,
                    RequestedBy = GetCurrentUserId(),
                    VerifyDigitalSignature = verifySignature
                };

                var result = await _mediator.Send(command);
                
                if (result == null)
                {
                    return NotFound(new { Message = "Export file not found or expired" });
                }

                // Set response headers
                foreach (var header in result.Headers)
                {
                    Response.Headers.Add(header.Key, header.Value);
                }

                return File(result.FileContent, result.ContentType, result.FileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error downloading export file {ExportId}", exportId);
                return StatusCode(500, new { Message = "Internal server error", Error = ex.Message });
            }
        }

        /// <summary>
        /// Get supported export formats
        /// </summary>
        [HttpGet("formats")]
        [Authorize(Roles = "Admin,Auditor,ComplianceOfficer")]
        public async Task<ActionResult<List<ExportFormatConfigDto>>> GetSupportedFormats([FromQuery] bool includeAdvanced = true)
        {
            try
            {
                var command = new GetSupportedExportFormatsCommand
                {
                    IncludeAdvancedFormats = includeAdvanced,
                    UserRole = GetCurrentUserRole()
                };

                var result = await _mediator.Send(command);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting supported export formats");
                return StatusCode(500, new { Message = "Internal server error", Error = ex.Message });
            }
        }

        /// <summary>
        /// Validate export request
        /// </summary>
        [HttpPost("validate")]
        [Authorize(Roles = "Admin,Auditor,ComplianceOfficer")]
        public async Task<ActionResult<List<ExportValidationDto>>> ValidateExportRequest([FromBody] EnhancedAuditExportRequestDto request)
        {
            try
            {
                var command = new ValidateExportRequestCommand
                {
                    ExportCommand = new EnhancedAuditExportCommand
                    {
                        UserIds = request.UserIds,
                        ActionTypes = request.ActionTypes,
                        EntityTypes = request.EntityTypes,
                        ModuleNames = request.ModuleNames,
                        FromDate = request.FromDate,
                        ToDate = request.ToDate,
                        SeverityLevels = request.SeverityLevels,
                        IpAddresses = request.IpAddresses,
                        ComplianceFlags = request.ComplianceFlags,
                        IncludeSystemActions = request.IncludeSystemActions,
                        IncludeSensitiveData = request.IncludeSensitiveData,
                        Format = request.Format,
                        SelectedColumns = request.SelectedColumns,
                        MaxRecords = request.MaxRecords,
                        PageSize = request.PageSize,
                        EnableBackgroundProcessing = request.EnableBackgroundProcessing,
                        RequireDigitalSignature = request.RequireDigitalSignature,
                        NotificationEmail = request.NotificationEmail,
                        EnableCompression = request.EnableCompression,
                        ExportTitle = request.ExportTitle,
                        CustomFilters = request.CustomFilters,
                        RequestedBy = GetCurrentUserId(),
                        RequestedByRole = GetCurrentUserRole()
                    },
                    PerformDeepValidation = true
                };

                var result = await _mediator.Send(command);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating export request");
                return StatusCode(500, new { Message = "Internal server error", Error = ex.Message });
            }
        }

        /// <summary>
        /// Get export history
        /// </summary>
        [HttpGet("history")]
        [Authorize(Roles = "Admin,Auditor,ComplianceOfficer")]
        public async Task<ActionResult<List<EnhancedAuditExportResponseDto>>> GetExportHistory(
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] ExportStatus? status = null,
            [FromQuery] ExportFormat? format = null,
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 20)
        {
            try
            {
                var command = new GetExportHistoryCommand
                {
                    RequestedBy = User.IsInRole("Admin") ? null : GetCurrentUserId(), // Admins can see all exports
                    FromDate = fromDate,
                    ToDate = toDate,
                    Status = status,
                    Format = format,
                    PageNumber = pageNumber,
                    PageSize = pageSize
                };

                var result = await _mediator.Send(command);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting export history");
                return StatusCode(500, new { Message = "Internal server error", Error = ex.Message });
            }
        }

        private Guid GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            return Guid.TryParse(userIdClaim, out var userId) ? userId : Guid.Empty;
        }

        private string GetCurrentUserRole()
        {
            return User.FindFirst(ClaimTypes.Role)?.Value ?? "Unknown";
        }
    }

    /// <summary>
    /// Cancel export request DTO
    /// </summary>
    public class CancelExportRequestDto
    {
        public string Reason { get; set; } = string.Empty;
    }
}
