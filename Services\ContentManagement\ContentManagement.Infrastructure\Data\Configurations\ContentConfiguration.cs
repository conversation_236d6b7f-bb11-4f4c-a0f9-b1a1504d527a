using ContentManagement.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace ContentManagement.Infrastructure.Data.Configurations;

/// <summary>
/// Entity Framework configuration for Content entity
/// </summary>
public class ContentConfiguration : IEntityTypeConfiguration<Content>
{
    public void Configure(EntityTypeBuilder<Content> builder)
    {
        // Table configuration
        builder.ToTable("contents");

        // Primary key
        builder.HasKey(c => c.Id);

        // Properties
        builder.Property(c => c.Id)
            .HasColumnName("id")
            .IsRequired();

        builder.Property(c => c.Title)
            .HasColumnName("title")
            .HasMaxLength(500)
            .IsRequired();

        builder.Property(c => c.Slug)
            .HasColumnName("slug")
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(c => c.Description)
            .HasColumnName("description")
            .HasMaxLength(2000);

        builder.Property(c => c.Type)
            .HasColumnName("type")
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(c => c.Status)
            .HasColumnName("status")
            .HasMaxLength(20)
            .IsRequired();

        builder.Property(c => c.ContentBody)
            .HasColumnName("content_body")
            .IsRequired();

        builder.Property(c => c.MetaTitle)
            .HasColumnName("meta_title")
            .HasMaxLength(200);

        builder.Property(c => c.MetaDescription)
            .HasColumnName("meta_description")
            .HasMaxLength(500);

        builder.Property(c => c.Tags)
            .HasColumnName("tags")
            .HasMaxLength(2000);

        builder.Property(c => c.SortOrder)
            .HasColumnName("sort_order")
            .HasDefaultValue(0);

        builder.Property(c => c.CreatedBy)
            .HasColumnName("created_by")
            .IsRequired();

        builder.Property(c => c.CreatedByName)
            .HasColumnName("created_by_name")
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(c => c.ApprovedBy)
            .HasColumnName("approved_by");

        builder.Property(c => c.ApprovedByName)
            .HasColumnName("approved_by_name")
            .HasMaxLength(200);

        builder.Property(c => c.ApprovedAt)
            .HasColumnName("approved_at");

        builder.Property(c => c.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(c => c.UpdatedAt)
            .HasColumnName("updated_at");

        // Indexes
        builder.HasIndex(c => c.Slug)
            .IsUnique()
            .HasDatabaseName("ix_contents_slug");

        builder.HasIndex(c => c.Type)
            .HasDatabaseName("ix_contents_type");

        builder.HasIndex(c => c.Status)
            .HasDatabaseName("ix_contents_status");

        builder.HasIndex(c => c.CreatedBy)
            .HasDatabaseName("ix_contents_created_by");

        builder.HasIndex(c => c.CreatedAt)
            .HasDatabaseName("ix_contents_created_at");

        builder.HasIndex(c => c.SortOrder)
            .HasDatabaseName("ix_contents_sort_order");

        // Relationships
        builder.HasMany(c => c.Versions)
            .WithOne()
            .HasForeignKey(v => v.ContentId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(c => c.Attachments)
            .WithOne()
            .HasForeignKey(a => a.ContentId)
            .OnDelete(DeleteBehavior.Cascade);

        // Ignore domain events (they are handled separately)
        builder.Ignore(c => c.DomainEvents);
    }
}

/// <summary>
/// Entity Framework configuration for ContentVersion entity
/// </summary>
public class ContentVersionConfiguration : IEntityTypeConfiguration<ContentVersion>
{
    public void Configure(EntityTypeBuilder<ContentVersion> builder)
    {
        // Table configuration
        builder.ToTable("content_versions");

        // Primary key
        builder.HasKey(cv => cv.Id);

        // Properties
        builder.Property(cv => cv.Id)
            .HasColumnName("id")
            .IsRequired();

        builder.Property(cv => cv.ContentId)
            .HasColumnName("content_id")
            .IsRequired();

        builder.Property(cv => cv.VersionNumber)
            .HasColumnName("version_number")
            .IsRequired();

        builder.Property(cv => cv.ContentBody)
            .HasColumnName("content_body")
            .IsRequired();

        builder.Property(cv => cv.ChangeDescription)
            .HasColumnName("change_description")
            .HasMaxLength(1000);

        builder.Property(cv => cv.CreatedBy)
            .HasColumnName("created_by")
            .IsRequired();

        builder.Property(cv => cv.CreatedByName)
            .HasColumnName("created_by_name")
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(cv => cv.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(cv => cv.IsPublished)
            .HasColumnName("is_published")
            .HasDefaultValue(false);

        // Indexes
        builder.HasIndex(cv => cv.ContentId)
            .HasDatabaseName("ix_content_versions_content_id");

        builder.HasIndex(cv => new { cv.ContentId, cv.VersionNumber })
            .IsUnique()
            .HasDatabaseName("ix_content_versions_content_version");

        builder.HasIndex(cv => cv.CreatedAt)
            .HasDatabaseName("ix_content_versions_created_at");
    }
}

/// <summary>
/// Entity Framework configuration for ContentAttachment entity
/// </summary>
public class ContentAttachmentConfiguration : IEntityTypeConfiguration<ContentAttachment>
{
    public void Configure(EntityTypeBuilder<ContentAttachment> builder)
    {
        // Table configuration
        builder.ToTable("content_attachments");

        // Primary key
        builder.HasKey(ca => ca.Id);

        // Properties
        builder.Property(ca => ca.Id)
            .HasColumnName("id")
            .IsRequired();

        builder.Property(ca => ca.ContentId)
            .HasColumnName("content_id")
            .IsRequired();

        builder.Property(ca => ca.DocumentId)
            .HasColumnName("document_id")
            .IsRequired();

        builder.Property(ca => ca.Type)
            .HasColumnName("type")
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(ca => ca.DisplayName)
            .HasColumnName("display_name")
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(ca => ca.Description)
            .HasColumnName("description")
            .HasMaxLength(500);

        builder.Property(ca => ca.SortOrder)
            .HasColumnName("sort_order")
            .HasDefaultValue(0);

        builder.Property(ca => ca.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(ca => ca.CreatedBy)
            .HasColumnName("created_by")
            .IsRequired();

        builder.Property(ca => ca.CreatedByName)
            .HasColumnName("created_by_name")
            .HasMaxLength(200)
            .IsRequired();

        // Indexes
        builder.HasIndex(ca => ca.ContentId)
            .HasDatabaseName("ix_content_attachments_content_id");

        builder.HasIndex(ca => ca.DocumentId)
            .HasDatabaseName("ix_content_attachments_document_id");

        builder.HasIndex(ca => ca.SortOrder)
            .HasDatabaseName("ix_content_attachments_sort_order");
    }
}
