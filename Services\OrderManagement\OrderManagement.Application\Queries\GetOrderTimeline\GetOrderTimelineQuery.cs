using MediatR;
using OrderManagement.Application.DTOs;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Application.Queries.GetOrderTimeline;

public class GetOrderTimelineQuery : IRequest<PagedResult<OrderTimelineDto>>
{
    public Guid OrderId { get; set; }
    public OrderTimelineEventType? EventType { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public Guid? ActorId { get; set; }
    public string? ActorRole { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 20;
    public string? SortBy { get; set; } = "EventTimestamp";
    public string? SortDirection { get; set; } = "desc";
    public bool IncludeMetadata { get; set; } = false;
}
