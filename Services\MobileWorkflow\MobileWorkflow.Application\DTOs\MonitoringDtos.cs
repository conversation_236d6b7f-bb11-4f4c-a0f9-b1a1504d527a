namespace MobileWorkflow.Application.DTOs;

public class HealthCheckResultDto
{
    public DateTime CheckedAt { get; set; }
    public string OverallStatus { get; set; } = string.Empty; // Healthy, Warning, Critical
    public List<HealthCheckItemDto> Checks { get; set; } = new();
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object> AdditionalData { get; set; } = new();
}

public class HealthCheckItemDto
{
    public string Name { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty; // Healthy, Warning, Critical
    public TimeSpan? ResponseTime { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object> Details { get; set; } = new();
}

public class ServiceStatusDto
{
    public string ServiceName { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty; // Healthy, Warning, Critical, Unknown
    public string Endpoint { get; set; } = string.Empty;
    public DateTime LastChecked { get; set; }
    public TimeSpan? ResponseTime { get; set; }
    public string? Version { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class MetricsCollectionDto
{
    public DateTime CollectedAt { get; set; }
    public Dictionary<string, double> SystemMetrics { get; set; } = new();
    public Dictionary<string, double> ApplicationMetrics { get; set; } = new();
    public Dictionary<string, double> BusinessMetrics { get; set; } = new();
    public Dictionary<string, double> CustomMetrics { get; set; } = new();
}

public class AlertDto
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty; // Info, Warning, Critical
    public string Source { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime? ResolvedAt { get; set; }
    public string? ResolvedBy { get; set; }
    public bool IsActive { get; set; }
    public Dictionary<string, object> Properties { get; set; } = new();
    public List<string> Actions { get; set; } = new();
}

public class DashboardDto
{
    public string DashboardType { get; set; } = string.Empty;
    public DateTime GeneratedAt { get; set; }
    public List<DashboardWidgetDto> Widgets { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
}

public class DashboardWidgetDto
{
    public string Type { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public object? Data { get; set; }
    public Dictionary<string, object> Configuration { get; set; } = new();
    public int Order { get; set; }
    public string Size { get; set; } = "medium"; // small, medium, large
}

public class LogEntryDto
{
    public Guid Id { get; set; }
    public DateTime Timestamp { get; set; }
    public string Level { get; set; } = string.Empty; // Debug, Info, Warning, Error, Critical
    public string Source { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string? Exception { get; set; }
    public Dictionary<string, object> Properties { get; set; } = new();
    public string? TraceId { get; set; }
    public string? SpanId { get; set; }
}

public class TraceDto
{
    public string TraceId { get; set; } = string.Empty;
    public string OperationName { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public TimeSpan? Duration { get; set; }
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object> Properties { get; set; } = new();
    public List<TraceSpanDto> Spans { get; set; } = new();
}

public class TraceSpanDto
{
    public string SpanId { get; set; } = string.Empty;
    public string? ParentSpanId { get; set; }
    public string OperationName { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public TimeSpan? Duration { get; set; }
    public Dictionary<string, object> Tags { get; set; } = new();
    public List<TraceLogDto> Logs { get; set; } = new();
}

public class TraceLogDto
{
    public DateTime Timestamp { get; set; }
    public string Level { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public Dictionary<string, object> Fields { get; set; } = new();
}

public class MonitoringConfigurationDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty; // HealthCheck, Alert, Dashboard, Metric
    public Dictionary<string, object> Configuration { get; set; } = new();
    public bool IsEnabled { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public string? UpdatedBy { get; set; }
}

public class AlertRuleDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string MetricName { get; set; } = string.Empty;
    public string Condition { get; set; } = string.Empty; // GreaterThan, LessThan, Equals
    public double Threshold { get; set; }
    public TimeSpan EvaluationWindow { get; set; }
    public string Severity { get; set; } = string.Empty;
    public bool IsEnabled { get; set; }
    public List<string> NotificationChannels { get; set; } = new();
    public Dictionary<string, object> RuleConfiguration { get; set; } = new();
}

public class NotificationChannelDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty; // Email, SMS, Slack, Webhook
    public Dictionary<string, object> Configuration { get; set; } = new();
    public bool IsEnabled { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

public class AuditLogDto
{
    public Guid Id { get; set; }
    public DateTime Timestamp { get; set; }
    public string UserId { get; set; } = string.Empty;
    public string Action { get; set; } = string.Empty;
    public string Resource { get; set; } = string.Empty;
    public string? ResourceId { get; set; }
    public Dictionary<string, object> OldValues { get; set; } = new();
    public Dictionary<string, object> NewValues { get; set; } = new();
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
}

public class MetricDefinitionDto
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty; // Counter, Gauge, Histogram, Summary
    public string Unit { get; set; } = string.Empty;
    public List<string> Labels { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
}

public class SystemInfoDto
{
    public string MachineName { get; set; } = string.Empty;
    public string OperatingSystem { get; set; } = string.Empty;
    public string ProcessorCount { get; set; } = string.Empty;
    public string TotalMemory { get; set; } = string.Empty;
    public string AvailableMemory { get; set; } = string.Empty;
    public string ApplicationVersion { get; set; } = string.Empty;
    public string RuntimeVersion { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public TimeSpan Uptime { get; set; }
    public Dictionary<string, object> EnvironmentVariables { get; set; } = new();
}

public class ErrorSummaryDto
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public int TotalErrors { get; set; }
    public Dictionary<string, int> ErrorsByType { get; set; } = new();
    public Dictionary<string, int> ErrorsBySource { get; set; } = new();
    public Dictionary<DateTime, int> ErrorsByHour { get; set; } = new();
    public List<TopErrorDto> TopErrors { get; set; } = new();
    public double ErrorRate { get; set; }
}

public class TopErrorDto
{
    public string ErrorType { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public int Count { get; set; }
    public DateTime FirstOccurrence { get; set; }
    public DateTime LastOccurrence { get; set; }
    public List<string> AffectedOperations { get; set; } = new();
}

public class PerformanceSummaryDto
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public double AverageResponseTime { get; set; }
    public double P95ResponseTime { get; set; }
    public double P99ResponseTime { get; set; }
    public double ThroughputPerSecond { get; set; }
    public double ErrorRate { get; set; }
    public Dictionary<string, double> OperationPerformance { get; set; } = new();
    public List<SlowOperationDto> SlowestOperations { get; set; } = new();
}

// Request DTOs
public class CreateAlertRequest
{
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public string Source { get; set; } = string.Empty;
    public Dictionary<string, object>? Properties { get; set; }
}

public class GetLogsRequest
{
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public string? Level { get; set; }
    public string? Source { get; set; }
    public string? SearchText { get; set; }
    public int? PageSize { get; set; }
    public int? PageNumber { get; set; }
}

public class CreateAlertRuleRequest
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string MetricName { get; set; } = string.Empty;
    public string Condition { get; set; } = string.Empty;
    public double Threshold { get; set; }
    public TimeSpan EvaluationWindow { get; set; }
    public string Severity { get; set; } = string.Empty;
    public List<string> NotificationChannels { get; set; } = new();
    public Dictionary<string, object>? RuleConfiguration { get; set; }
}

public class CreateNotificationChannelRequest
{
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public Dictionary<string, object> Configuration { get; set; } = new();
    public string CreatedBy { get; set; } = string.Empty;
}

public class UpdateDashboardRequest
{
    public string DashboardType { get; set; } = string.Empty;
    public List<DashboardWidgetDto> Widgets { get; set; } = new();
    public Dictionary<string, object>? Configuration { get; set; }
    public string UpdatedBy { get; set; } = string.Empty;
}

public class MonitoringReportDto
{
    public string ReportType { get; set; } = string.Empty;
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public DateTime GeneratedAt { get; set; }
    public string GeneratedBy { get; set; } = string.Empty;
    public HealthCheckResultDto? HealthSummary { get; set; }
    public PerformanceSummaryDto? PerformanceSummary { get; set; }
    public ErrorSummaryDto? ErrorSummary { get; set; }
    public List<AlertDto> CriticalAlerts { get; set; } = new();
    public Dictionary<string, object> CustomData { get; set; } = new();
}

public class ServiceDependencyDto
{
    public string ServiceName { get; set; } = string.Empty;
    public List<string> Dependencies { get; set; } = new();
    public List<string> Dependents { get; set; } = new();
    public string Status { get; set; } = string.Empty;
    public DateTime LastChecked { get; set; }
    public Dictionary<string, object> HealthMetrics { get; set; } = new();
}

public class CapacityPlanningDto
{
    public string ResourceType { get; set; } = string.Empty; // CPU, Memory, Storage, Network
    public double CurrentUsage { get; set; }
    public double PredictedUsage { get; set; }
    public DateTime PredictionDate { get; set; }
    public double CapacityThreshold { get; set; }
    public TimeSpan TimeToThreshold { get; set; }
    public List<string> Recommendations { get; set; } = new();
    public Dictionary<string, object> TrendData { get; set; } = new();
}

public class IncidentDto
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty; // Open, InProgress, Resolved, Closed
    public DateTime CreatedAt { get; set; }
    public DateTime? ResolvedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public string? AssignedTo { get; set; }
    public List<Guid> RelatedAlerts { get; set; } = new();
    public List<string> AffectedServices { get; set; } = new();
    public Dictionary<string, object> IncidentData { get; set; } = new();
}

public class SLAMetricDto
{
    public string ServiceName { get; set; } = string.Empty;
    public string MetricName { get; set; } = string.Empty;
    public double Target { get; set; }
    public double Actual { get; set; }
    public double Compliance { get; set; }
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
    public string Status { get; set; } = string.Empty; // Met, AtRisk, Breached
    public Dictionary<string, object> Details { get; set; } = new();
}
