using MediatR;
using OrderManagement.Domain.Enums;
using OrderManagement.Domain.ValueObjects;

namespace OrderManagement.Application.Commands.SetPriceExpectations;

public class SetPriceExpectationsCommand : IRequest<bool>
{
    public Guid RfqId { get; set; }
    public Guid SetBy { get; set; }
    public string? SetByName { get; set; }
    public Money? MinExpectedPrice { get; set; }
    public Money? MaxExpectedPrice { get; set; }
    public Money? TargetPrice { get; set; }
    public PriceExpectationType ExpectationType { get; set; } = PriceExpectationType.Range;
    public bool IsFlexible { get; set; } = true;
    public string? PriceJustification { get; set; }
    public DateTime? PriceValidUntil { get; set; }
    public bool AllowCounterOffers { get; set; } = true;
    public decimal? MaxCounterOfferVariance { get; set; }
    public string? PricingNotes { get; set; }
    public bool CreateTimelineEvent { get; set; } = true;
}
