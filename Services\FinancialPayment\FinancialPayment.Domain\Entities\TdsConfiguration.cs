using FinancialPayment.Domain.Enums;
using Shared.Domain.Common;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Domain.Entities;

/// <summary>
/// Domain entity representing TDS configuration for different entity types and sections
/// </summary>
public class TdsConfiguration : AggregateRoot
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public TdsSection Section { get; private set; }
    public EntityType EntityType { get; private set; }
    public TaxRate TaxRate { get; private set; }
    public TdsThreshold Threshold { get; private set; }
    public TaxConfigurationStatus Status { get; private set; }
    public bool RequiresPan { get; private set; }
    public decimal HigherRateWithoutPan { get; private set; }
    public string? SpecialConditions { get; private set; }
    public string CreatedBy { get; private set; }
    public string? ModifiedBy { get; private set; }
    public DateTime? ModifiedAt { get; private set; }

    // Navigation properties
    private readonly List<TdsConfigurationHistory> _history = new();
    public IReadOnlyList<TdsConfigurationHistory> History => _history.AsReadOnly();

    private TdsConfiguration() { }

    public TdsConfiguration(
        string name,
        string description,
        TdsSection section,
        EntityType entityType,
        TaxRate taxRate,
        TdsThreshold threshold,
        string createdBy,
        bool requiresPan = true,
        decimal higherRateWithoutPan = 0,
        string? specialConditions = null)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("TDS configuration name cannot be empty", nameof(name));

        if (string.IsNullOrWhiteSpace(description))
            throw new ArgumentException("TDS configuration description cannot be empty", nameof(description));

        if (string.IsNullOrWhiteSpace(createdBy))
            throw new ArgumentException("Created by cannot be empty", nameof(createdBy));

        if (requiresPan && higherRateWithoutPan > 0 && higherRateWithoutPan <= taxRate.Rate)
            throw new ArgumentException("Higher rate without PAN must be greater than normal rate", nameof(higherRateWithoutPan));

        ValidateTdsRateForSection(section, taxRate.Rate);

        Name = name.Trim();
        Description = description.Trim();
        Section = section;
        EntityType = entityType;
        TaxRate = taxRate;
        Threshold = threshold;
        RequiresPan = requiresPan;
        HigherRateWithoutPan = higherRateWithoutPan;
        SpecialConditions = specialConditions?.Trim();
        Status = TaxConfigurationStatus.Draft;
        CreatedBy = createdBy.Trim();

        AddHistoryEntry("Created", $"TDS configuration created by {createdBy}");
    }

    public void UpdateConfiguration(
        string name,
        string description,
        TdsSection section,
        EntityType entityType,
        TaxRate taxRate,
        TdsThreshold threshold,
        string modifiedBy,
        bool requiresPan = true,
        decimal higherRateWithoutPan = 0,
        string? specialConditions = null)
    {
        if (Status == TaxConfigurationStatus.Archived)
            throw new InvalidOperationException("Cannot update archived TDS configuration");

        if (string.IsNullOrWhiteSpace(modifiedBy))
            throw new ArgumentException("Modified by cannot be empty", nameof(modifiedBy));

        ValidateTdsRateForSection(section, taxRate.Rate);

        var changes = new List<string>();

        if (Name != name.Trim())
        {
            changes.Add($"Name: {Name} → {name.Trim()}");
            Name = name.Trim();
        }

        if (Description != description.Trim())
        {
            changes.Add("Description updated");
            Description = description.Trim();
        }

        if (Section != section)
        {
            changes.Add($"Section: {Section} → {section}");
            Section = section;
        }

        if (EntityType != entityType)
        {
            changes.Add($"Entity Type: {EntityType} → {entityType}");
            EntityType = entityType;
        }

        if (!TaxRate.Equals(taxRate))
        {
            changes.Add($"Tax Rate: {TaxRate.Rate}% → {taxRate.Rate}%");
            TaxRate = taxRate;
        }

        if (!Threshold.Equals(threshold))
        {
            changes.Add("Threshold updated");
            Threshold = threshold;
        }

        if (RequiresPan != requiresPan)
        {
            changes.Add($"Requires PAN: {RequiresPan} → {requiresPan}");
            RequiresPan = requiresPan;
        }

        if (Math.Abs(HigherRateWithoutPan - higherRateWithoutPan) > 0.01m)
        {
            changes.Add($"Higher Rate Without PAN: {HigherRateWithoutPan}% → {higherRateWithoutPan}%");
            HigherRateWithoutPan = higherRateWithoutPan;
        }

        if (SpecialConditions != specialConditions?.Trim())
        {
            changes.Add("Special Conditions updated");
            SpecialConditions = specialConditions?.Trim();
        }

        if (changes.Any())
        {
            ModifiedBy = modifiedBy.Trim();
            ModifiedAt = DateTime.UtcNow;
            SetUpdatedAt();

            AddHistoryEntry("Updated", $"Configuration updated by {modifiedBy}: {string.Join(", ", changes)}");
        }
    }

    public void Activate(string activatedBy)
    {
        if (Status == TaxConfigurationStatus.Archived)
            throw new InvalidOperationException("Cannot activate archived TDS configuration");

        if (string.IsNullOrWhiteSpace(activatedBy))
            throw new ArgumentException("Activated by cannot be empty", nameof(activatedBy));

        if (Status != TaxConfigurationStatus.Active)
        {
            Status = TaxConfigurationStatus.Active;
            ModifiedBy = activatedBy.Trim();
            ModifiedAt = DateTime.UtcNow;
            SetUpdatedAt();

            AddHistoryEntry("Activated", $"Configuration activated by {activatedBy}");
        }
    }

    public void Deactivate(string deactivatedBy, string reason)
    {
        if (Status == TaxConfigurationStatus.Archived)
            throw new InvalidOperationException("Cannot deactivate archived TDS configuration");

        if (string.IsNullOrWhiteSpace(deactivatedBy))
            throw new ArgumentException("Deactivated by cannot be empty", nameof(deactivatedBy));

        if (Status != TaxConfigurationStatus.Inactive)
        {
            Status = TaxConfigurationStatus.Inactive;
            ModifiedBy = deactivatedBy.Trim();
            ModifiedAt = DateTime.UtcNow;
            SetUpdatedAt();

            AddHistoryEntry("Deactivated", $"Configuration deactivated by {deactivatedBy}. Reason: {reason}");
        }
    }

    public void Archive(string archivedBy, string reason)
    {
        if (string.IsNullOrWhiteSpace(archivedBy))
            throw new ArgumentException("Archived by cannot be empty", nameof(archivedBy));

        Status = TaxConfigurationStatus.Archived;
        ModifiedBy = archivedBy.Trim();
        ModifiedAt = DateTime.UtcNow;
        SetUpdatedAt();

        AddHistoryEntry("Archived", $"Configuration archived by {archivedBy}. Reason: {reason}");
    }

    public Money CalculateTds(Money baseAmount, bool hasPan = true)
    {
        if (Status != TaxConfigurationStatus.Active)
            throw new InvalidOperationException("Cannot calculate TDS using inactive configuration");

        if (!TaxRate.IsEffectiveOn(DateTime.UtcNow))
            throw new InvalidOperationException("Tax rate is not effective for current date");

        if (!Threshold.IsApplicable(baseAmount, EntityType, hasPan))
            return Money.Zero(baseAmount.Currency);

        var applicableRate = hasPan || !RequiresPan ? TaxRate.Rate : HigherRateWithoutPan;
        var tdsAmount = baseAmount.Amount * (applicableRate / 100);

        return new Money(tdsAmount, baseAmount.Currency);
    }

    public bool IsApplicableFor(EntityType entityType, TdsSection section, DateTime date, bool hasPan = true)
    {
        return Status == TaxConfigurationStatus.Active &&
               EntityType == entityType &&
               Section == section &&
               TaxRate.IsEffectiveOn(date) &&
               (!RequiresPan || hasPan);
    }

    private void AddHistoryEntry(string action, string details)
    {
        var historyEntry = new TdsConfigurationHistory(Id, action, details, ModifiedBy ?? CreatedBy);
        _history.Add(historyEntry);
    }

    private static void ValidateTdsRateForSection(TdsSection section, decimal rate)
    {
        // Validate TDS rates based on common sections
        var validRanges = new Dictionary<TdsSection, (decimal min, decimal max)>
        {
            { TdsSection.Section194C, (1.0m, 2.0m) },
            { TdsSection.Section194J, (10.0m, 10.0m) },
            { TdsSection.Section194I, (10.0m, 10.0m) },
            { TdsSection.Section194H, (5.0m, 5.0m) },
            { TdsSection.Section194A, (10.0m, 10.0m) },
            { TdsSection.Section194O, (1.0m, 1.0m) },
            { TdsSection.Section194Q, (0.1m, 0.1m) },
            { TdsSection.Section194S, (1.0m, 1.0m) }
        };

        if (validRanges.TryGetValue(section, out var range))
        {
            if (rate < range.min || rate > range.max)
            {
                throw new ArgumentException($"TDS rate {rate}% is not valid for section {section}. Expected range: {range.min}% - {range.max}%");
            }
        }
    }
}

/// <summary>
/// Entity representing TDS configuration history for audit purposes
/// </summary>
public class TdsConfigurationHistory : BaseEntity
{
    public Guid TdsConfigurationId { get; private set; }
    public string Action { get; private set; }
    public string Details { get; private set; }
    public string ModifiedBy { get; private set; }
    public DateTime ModifiedAt { get; private set; }

    private TdsConfigurationHistory() { }

    public TdsConfigurationHistory(Guid tdsConfigurationId, string action, string details, string modifiedBy)
    {
        TdsConfigurationId = tdsConfigurationId;
        Action = action;
        Details = details;
        ModifiedBy = modifiedBy;
        ModifiedAt = DateTime.UtcNow;
    }
}


