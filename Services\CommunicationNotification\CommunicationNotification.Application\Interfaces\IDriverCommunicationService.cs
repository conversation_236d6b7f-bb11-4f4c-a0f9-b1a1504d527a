using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Infrastructure.Services;

namespace CommunicationNotification.Application.Interfaces;

/// <summary>
/// Interface for driver-specific communication services
/// </summary>
public interface IDriverCommunicationService
{
    /// <summary>
    /// Send trip assignment notification to driver
    /// </summary>
    Task<DriverCommunicationResult> SendTripAssignmentAsync(
        DriverTripAssignment assignment,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Send trip update notification to driver
    /// </summary>
    Task<DriverCommunicationResult> SendTripUpdateAsync(
        DriverTripUpdate update,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Send navigation instruction to driver
    /// </summary>
    Task<DriverCommunicationResult> SendNavigationInstructionAsync(
        DriverNavigationInstruction instruction,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Send emergency alert to driver
    /// </summary>
    Task<DriverCommunicationResult> SendEmergencyAlertAsync(
        DriverEmergencyAlert alert,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get offline messages for driver
    /// </summary>
    Task<List<OfflineMessage>> GetOfflineMessagesAsync(
        Guid driverId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Mark messages as delivered
    /// </summary>
    Task<bool> MarkMessagesAsDeliveredAsync(
        Guid driverId,
        List<Guid> messageIds,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if driver is online
    /// </summary>
    Task<bool> IsDriverOnlineAsync(
        Guid driverId,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface for voice instruction services
/// </summary>
public interface IVoiceInstructionService
{
    /// <summary>
    /// Send voice instruction to driver
    /// </summary>
    Task<VoiceInstructionResult> SendVoiceInstructionAsync(
        VoiceInstruction instruction,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Send voice navigation instruction
    /// </summary>
    Task<VoiceInstructionResult> SendVoiceNavigationAsync(
        DriverNavigationInstruction navigation,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Send emergency voice alert
    /// </summary>
    Task<VoiceInstructionResult> SendEmergencyVoiceAlertAsync(
        DriverEmergencyAlert alert,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get voice instruction history
    /// </summary>
    Task<List<VoiceInstructionHistory>> GetVoiceHistoryAsync(
        Guid driverId,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get driver voice settings
    /// </summary>
    Task<VoiceInstructionSettings> GetDriverVoiceSettingsAsync(
        Guid driverId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Update driver voice settings
    /// </summary>
    Task<bool> UpdateDriverVoiceSettingsAsync(
        Guid driverId,
        VoiceInstructionSettings settings,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface for driver offline capabilities
/// </summary>
public interface IDriverOfflineService
{
    /// <summary>
    /// Store offline data for driver
    /// </summary>
    Task<OfflineCapabilityResult> StoreOfflineDataAsync(
        Guid driverId,
        DriverOfflineData data,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get offline data for driver
    /// </summary>
    Task<DriverOfflineData?> GetOfflineDataAsync(
        Guid driverId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Sync offline data when driver comes back online
    /// </summary>
    Task<OfflineCapabilityResult> SyncOfflineDataAsync(
        Guid driverId,
        List<DriverOfflineAction> actions,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get pending messages for driver
    /// </summary>
    Task<List<OfflineMessage>> GetPendingMessagesAsync(
        Guid driverId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Handle driver reconnection
    /// </summary>
    Task<OfflineCapabilityResult> HandleDriverReconnectionAsync(
        Guid driverId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get driver offline status
    /// </summary>
    Task<DriverOfflineStatus> GetDriverOfflineStatusAsync(
        Guid driverId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Clear offline data for driver
    /// </summary>
    Task<OfflineCapabilityResult> ClearOfflineDataAsync(
        Guid driverId,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Driver trip assignment model
/// </summary>
public class DriverTripAssignment
{
    public Guid DriverId { get; set; }
    public Guid TripId { get; set; }
    public string PickupLocation { get; set; } = string.Empty;
    public string DropoffLocation { get; set; } = string.Empty;
    public DateTime PickupTime { get; set; }
    public string CustomerName { get; set; } = string.Empty;
    public double EstimatedDistance { get; set; }
    public TimeSpan EstimatedDuration { get; set; }
    public decimal EstimatedFare { get; set; }
    public Language PreferredLanguage { get; set; } = Language.English;
    public bool EnableVoiceInstructions { get; set; } = true;
    public TripAssignmentType AssignmentType { get; set; } = TripAssignmentType.Normal;
    public bool IsUrgent { get; set; }
    public Dictionary<string, string> Metadata { get; set; } = new();
}

/// <summary>
/// Driver trip update model
/// </summary>
public class DriverTripUpdate
{
    public Guid DriverId { get; set; }
    public Guid TripId { get; set; }
    public TripUpdateType UpdateType { get; set; }
    public Language PreferredLanguage { get; set; } = Language.English;
    public Priority Priority { get; set; } = Priority.Normal;
    public Dictionary<string, object> Parameters { get; set; } = new();
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Driver navigation instruction model
/// </summary>
public class DriverNavigationInstruction
{
    public Guid DriverId { get; set; }
    public Guid? TripId { get; set; }
    public string Instruction { get; set; } = string.Empty;
    public double Distance { get; set; }
    public string Direction { get; set; } = string.Empty;
    public string? StreetName { get; set; }
    public string? Landmark { get; set; }
    public NavigationInstructionType InstructionType { get; set; }
    public GeoLocation CurrentLocation { get; set; } = new();
    public Language PreferredLanguage { get; set; } = Language.English;
    public bool EnableVoice { get; set; } = true;
    public string? PhoneNumber { get; set; }
}

/// <summary>
/// Driver emergency alert model
/// </summary>
public class DriverEmergencyAlert
{
    public Guid DriverId { get; set; }
    public Guid? TripId { get; set; }
    public EmergencyAlertType AlertType { get; set; }
    public EmergencySeverity Severity { get; set; }
    public string Message { get; set; } = string.Empty;
    public string? Location { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public Language PreferredLanguage { get; set; } = Language.English;
    public bool RequiresResponse { get; set; } = true;
    public string? PhoneNumber { get; set; }
    public Dictionary<string, string> Metadata { get; set; } = new();
}

/// <summary>
/// Driver communication result
/// </summary>
public class DriverCommunicationResult
{
    public bool IsSuccess { get; set; }
    public Guid? MessageId { get; set; }
    public bool DriverWasOnline { get; set; }
    public string? Message { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    public static DriverCommunicationResult Success(Guid? messageId, bool driverOnline, string? message = null)
    {
        return new DriverCommunicationResult
        {
            IsSuccess = true,
            MessageId = messageId,
            DriverWasOnline = driverOnline,
            Message = message
        };
    }

    public static DriverCommunicationResult Failure(string errorMessage)
    {
        return new DriverCommunicationResult
        {
            IsSuccess = false,
            ErrorMessage = errorMessage
        };
    }
}

/// <summary>
/// Driver offline data model
/// </summary>
public class DriverOfflineData
{
    public Guid DriverId { get; set; }
    public DateTime LastSyncTime { get; set; } = DateTime.UtcNow;
    public List<DriverOfflineAction> PendingActions { get; set; } = new();
    public Dictionary<string, string> CachedData { get; set; } = new();
    public int EstimatedSizeKB { get; set; }
}

/// <summary>
/// Driver offline action model
/// </summary>
public class DriverOfflineAction
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public OfflineActionType ActionType { get; set; }
    public string Data { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public int RetryCount { get; set; }
    public Dictionary<string, string> Metadata { get; set; } = new();
}

/// <summary>
/// Driver offline status model
/// </summary>
public class DriverOfflineStatus
{
    public Guid DriverId { get; set; }
    public bool IsOnline { get; set; }
    public DateTime LastSeen { get; set; }
    public int PendingMessageCount { get; set; }
    public int OfflineDataSize { get; set; }
    public DateTime? LastSyncTime { get; set; }
    public bool OfflineCapabilityEnabled { get; set; }
}

/// <summary>
/// Offline capability result
/// </summary>
public class OfflineCapabilityResult
{
    public bool IsSuccess { get; set; }
    public string Message { get; set; } = string.Empty;
    public object? Data { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    public static OfflineCapabilityResult Success(string message, object? data = null)
    {
        return new OfflineCapabilityResult
        {
            IsSuccess = true,
            Message = message,
            Data = data
        };
    }

    public static OfflineCapabilityResult Failure(string message)
    {
        return new OfflineCapabilityResult
        {
            IsSuccess = false,
            Message = message
        };
    }
}

/// <summary>
/// Offline sync result
/// </summary>
public class OfflineSyncResult
{
    public Guid ActionId { get; set; }
    public bool IsSuccess { get; set; }
    public string Message { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    public static OfflineSyncResult Success(Guid actionId, string message)
    {
        return new OfflineSyncResult
        {
            ActionId = actionId,
            IsSuccess = true,
            Message = message
        };
    }

    public static OfflineSyncResult Failure(Guid actionId, string message)
    {
        return new OfflineSyncResult
        {
            ActionId = actionId,
            IsSuccess = false,
            Message = message
        };
    }
}

/// <summary>
/// Geographic location model
/// </summary>
public class GeoLocation
{
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public double? Altitude { get; set; }
    public double? Accuracy { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Trip assignment types
/// </summary>
public enum TripAssignmentType
{
    Normal,
    Urgent,
    Scheduled,
    Recurring
}

/// <summary>
/// Trip update types
/// </summary>
public enum TripUpdateType
{
    Started,
    CustomerPickedUp,
    InTransit,
    Completed,
    Cancelled,
    Delayed
}

/// <summary>
/// Navigation instruction types
/// </summary>
public enum NavigationInstructionType
{
    Turn,
    Continue,
    Arrive,
    Reroute,
    Warning
}

/// <summary>
/// Emergency alert types
/// </summary>
public enum EmergencyAlertType
{
    Accident,
    Breakdown,
    Medical,
    Security,
    Weather,
    Traffic,
    Other
}

/// <summary>
/// Emergency severity levels
/// </summary>
public enum EmergencySeverity
{
    Low,
    Medium,
    High,
    Critical
}

/// <summary>
/// Offline action types
/// </summary>
public enum OfflineActionType
{
    TripStatusUpdate,
    LocationUpdate,
    MessageSent,
    EmergencyAlert
}
