using MediatR;
using Microsoft.Extensions.Logging;
using TripManagement.Application.Interfaces;
using TripManagement.Domain.Enums;


namespace TripManagement.Application.Commands.StartTrip;

public class StartTripCommandHandler : IRequestHandler<StartTripCommand, bool>
{
    private readonly ITripRepository _tripRepository;
    private readonly IDriverRepository _driverRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<StartTripCommandHandler> _logger;

    public StartTripCommandHandler(
        ITripRepository tripRepository,
        IDriverRepository driverRepository,
        IUnitOfWork unitOfWork,
        IMessageBroker messageBroker,
        ILogger<StartTripCommandHandler> logger)
    {
        _tripRepository = tripRepository;
        _driverRepository = driverRepository;
        _unitOfWork = unitOfWork;
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task<bool> Handle(StartTripCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Starting trip {TripId} by driver {DriverId}",
            request.TripId, request.DriverId);

        // Get trip
        var trip = await _tripRepository.GetByIdAsync(request.TripId, cancellationToken);
        if (trip == null)
        {
            _logger.LogWarning("Trip {TripId} not found", request.TripId);
            throw new ArgumentException($"Trip {request.TripId} not found");
        }

        // Validate trip can be started
        if (trip.Status != TripStatus.Assigned)
        {
            _logger.LogWarning("Trip {TripId} cannot be started. Current status: {Status}",
                request.TripId, trip.Status);
            throw new InvalidOperationException($"Trip cannot be started. Current status: {trip.Status}");
        }

        // Validate driver is assigned to this trip
        if (trip.DriverId != request.DriverId)
        {
            _logger.LogWarning("Driver {DriverId} is not assigned to trip {TripId}",
                request.DriverId, request.TripId);
            throw new InvalidOperationException("Driver is not assigned to this trip");
        }

        // Get driver to update status
        var driver = await _driverRepository.GetByIdAsync(request.DriverId, cancellationToken);
        if (driver == null)
        {
            _logger.LogWarning("Driver {DriverId} not found", request.DriverId);
            throw new ArgumentException($"Driver {request.DriverId} not found");
        }

        // Start the trip
        trip.Start();

        // Update driver status
        driver.UpdateStatus(DriverStatus.OnTrip);

        // Save changes
        _tripRepository.Update(trip);
        _driverRepository.Update(driver);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        // Publish integration event
        await _messageBroker.PublishAsync("trip.started", new
        {
            TripId = trip.Id,
            TripNumber = trip.TripNumber,
            OrderId = trip.OrderId,
            CarrierId = trip.CarrierId,
            DriverId = request.DriverId,
            VehicleId = trip.VehicleId,
            ActualStartTime = request.ActualStartTime,
            EstimatedEndTime = trip.EstimatedEndTime,
            Status = trip.Status.ToString(),
            Notes = request.Notes,
            StartedAt = DateTime.UtcNow
        });

        _logger.LogInformation("Successfully started trip {TripId} by driver {DriverId}",
            request.TripId, request.DriverId);

        return true;
    }
}
