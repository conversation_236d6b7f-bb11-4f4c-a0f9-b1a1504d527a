# AnalyticsBIService Environment Configuration Template
# Copy this file to .env and fill in the actual values for your environment

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
VERSION=latest
ASPNETCORE_ENVIRONMENT=Production

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# PostgreSQL/TimescaleDB Configuration
POSTGRES_DB=TLI_AnalyticsBI
POSTGRES_USER=timescale
POSTGRES_PASSWORD=your_secure_postgres_password_here
TIMESCALEDB_PORT=5432

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_PASSWORD=your_secure_redis_password_here
REDIS_PORT=6379

# =============================================================================
# JWT AUTHENTICATION
# =============================================================================
JWT_SECRET_KEY=your_super_secure_jwt_secret_key_minimum_256_bits_long
JWT_ISSUER=TLI.Analytics.Production
JWT_AUDIENCE=TLI.Users.Production

# =============================================================================
# ANALYTICS SETTINGS
# =============================================================================
ANALYTICS_DATA_RETENTION_DAYS=365
ANALYTICS_METRIC_AGGREGATION_INTERVAL=00:05:00
ANALYTICS_ENABLE_REALTIME=true
ANALYTICS_ENABLE_PREDICTIVE=true
ANALYTICS_CACHE_EXPIRATION_MINUTES=30

# =============================================================================
# REPORT SETTINGS
# =============================================================================
REPORT_MAX_SIZE=104857600
REPORT_STORAGE_PATH=/app/data/reports
REPORT_RETENTION_DAYS=90
REPORT_ENABLE_SCHEDULED=true
REPORT_MAX_SCHEDULED_PER_USER=10
REPORT_GENERATION_CONCURRENCY=5

# =============================================================================
# DASHBOARD SETTINGS
# =============================================================================
DASHBOARD_MAX_WIDGETS=50
DASHBOARD_REFRESH_INTERVAL=00:01:00
DASHBOARD_MAX_PER_USER=20
DASHBOARD_ENABLE_REALTIME=true
DASHBOARD_CACHE_WIDGETS=true
DASHBOARD_WIDGET_CACHE_EXPIRATION=15

# =============================================================================
# ALERT SETTINGS
# =============================================================================
ALERT_ENABLE=true
ALERT_EVALUATION_INTERVAL=00:01:00
ALERT_MAX_PER_HOUR=100
ALERT_RETENTION_DAYS=30
ALERT_DEFAULT_COOLDOWN=5
ALERT_MAX_ESCALATION_LEVELS=3

# =============================================================================
# EXPORT SETTINGS
# =============================================================================
EXPORT_MAX_SIZE=52428800
EXPORT_TIMEOUT=00:10:00
EXPORT_STORAGE_PATH=/app/data/exports
EXPORT_RETENTION_DAYS=7
EXPORT_ENABLE_ASYNC=true

# =============================================================================
# MACHINE LEARNING SETTINGS
# =============================================================================
ML_ENABLE=true
ML_TRAINING_INTERVAL=24:00:00
ML_PREDICTION_CACHE_HOURS=4
ML_ANOMALY_THRESHOLD=0.95
ML_MODEL_STORAGE_PATH=/app/data/models
ML_MAX_TRAINING_DATA_POINTS=1000000

# =============================================================================
# PERFORMANCE SETTINGS
# =============================================================================
PERFORMANCE_QUERY_TIMEOUT=00:00:30
PERFORMANCE_MAX_CONCURRENT_QUERIES=50
PERFORMANCE_ENABLE_QUERY_OPTIMIZATION=true
PERFORMANCE_ENABLE_DATA_COMPRESSION=true
PERFORMANCE_BATCH_SIZE=1000
PERFORMANCE_PARALLEL_PROCESSING=true

# =============================================================================
# SECURITY SETTINGS
# =============================================================================
SECURITY_ENABLE_DATA_MASKING=true
SECURITY_ENABLE_AUDIT_LOGGING=true
SECURITY_REQUIRE_DATA_ACCESS=true
SECURITY_ENABLE_ROW_LEVEL_SECURITY=true
SECURITY_ENABLE_DATA_CLASSIFICATION=true

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
CORS_ALLOWED_ORIGINS=https://your-frontend-domain.com,https://your-admin-domain.com

# =============================================================================
# FILE STORAGE (AWS S3)
# =============================================================================
S3_BUCKET_NAME=your-analytics-bucket
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key

# =============================================================================
# MONITORING & OBSERVABILITY
# =============================================================================
APPINSIGHTS_INSTRUMENTATIONKEY=your_application_insights_key

# =============================================================================
# SERVICE PORTS
# =============================================================================
API_PORT=5014
NGINX_HTTP_PORT=80
NGINX_HTTPS_PORT=443
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000
FLUENTD_PORT=24224

# =============================================================================
# GRAFANA CONFIGURATION
# =============================================================================
GRAFANA_ADMIN_USER=admin
GRAFANA_ADMIN_PASSWORD=your_secure_grafana_password
GRAFANA_ROOT_URL=https://your-grafana-domain.com

# =============================================================================
# SSL/TLS CONFIGURATION
# =============================================================================
SSL_CERTIFICATE_PATH=/etc/nginx/ssl/cert.pem
SSL_PRIVATE_KEY_PATH=/etc/nginx/ssl/private.key

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=your-backup-bucket

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=Information
LOG_RETENTION_DAYS=30
LOG_MAX_FILE_SIZE=100MB

# =============================================================================
# HEALTH CHECK CONFIGURATION
# =============================================================================
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10
HEALTH_CHECK_RETRIES=3

# =============================================================================
# RATE LIMITING
# =============================================================================
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=1000
RATE_LIMIT_REQUESTS_PER_HOUR=10000

# =============================================================================
# CACHE CONFIGURATION
# =============================================================================
CACHE_DEFAULT_EXPIRATION=00:30:00
CACHE_MAX_SIZE=1GB
CACHE_COMPRESSION_ENABLED=true
