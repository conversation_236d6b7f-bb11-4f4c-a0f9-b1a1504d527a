using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Domain.Enums;

namespace AnalyticsBIService.Infrastructure.Services;

/// <summary>
/// Interface for scheduled report service
/// </summary>
public interface IScheduledReportService
{
    /// <summary>
    /// Create a new scheduled report
    /// </summary>
    Task<ScheduledReportDto> CreateScheduledReportAsync(CreateScheduledReportRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get scheduled report by ID
    /// </summary>
    Task<ScheduledReportDto?> GetScheduledReportAsync(Guid scheduleId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all scheduled reports for a user
    /// </summary>
    Task<List<ScheduledReportDto>> GetUserScheduledReportsAsync(Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update scheduled report
    /// </summary>
    Task<bool> UpdateScheduledReportAsync(Guid scheduleId, UpdateScheduledReportRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete scheduled report
    /// </summary>
    Task<bool> DeleteScheduledReportAsync(Guid scheduleId, Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Execute scheduled report immediately
    /// </summary>
    Task<ReportExecutionResultDto> ExecuteScheduledReportAsync(Guid scheduleId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get scheduled report execution history
    /// </summary>
    Task<List<ReportExecutionHistoryDto>> GetExecutionHistoryAsync(Guid scheduleId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get pending scheduled reports
    /// </summary>
    Task<List<ScheduledReportDto>> GetPendingReportsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Process scheduled reports (background service method)
    /// </summary>
    Task ProcessScheduledReportsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get available report templates
    /// </summary>
    Task<List<ReportTemplateDto>> GetReportTemplatesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get available delivery methods
    /// </summary>
    Task<List<DeliveryMethodDto>> GetDeliveryMethodsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Test delivery configuration
    /// </summary>
    Task<DeliveryTestResult> TestDeliveryConfigurationAsync(DeliveryConfigurationDto configuration, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get report schedule statistics
    /// </summary>
    Task<ScheduleStatisticsDto> GetScheduleStatisticsAsync(Guid userId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Scheduled report data transfer object
/// </summary>
public class ScheduledReportDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public ReportConfigurationDto ReportConfiguration { get; set; } = new();
    public ScheduleConfigurationDto ScheduleConfiguration { get; set; } = new();
    public DeliveryConfigurationDto DeliveryConfiguration { get; set; } = new();
    public string Status { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public Guid CreatedBy { get; set; }
    public UserType UserType { get; set; }
    public bool IsActive { get; set; }
    public DateTime? NextRunDate { get; set; }
    public DateTime? LastRunDate { get; set; }
    public string? LastRunStatus { get; set; }
    public int ExecutionCount { get; set; }
}





/// <summary>
/// Report visualization data transfer object
/// </summary>
public class ReportVisualizationDto
{
    public string Type { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public List<string> XAxisFields { get; set; } = new();
    public List<string> YAxisFields { get; set; } = new();
    public Dictionary<string, object> Options { get; set; } = new();
    public int Order { get; set; }
}



/// <summary>
/// Schedule configuration data transfer object
/// </summary>
public class ScheduleConfigurationDto
{
    public string ScheduleType { get; set; } = string.Empty;
    public string CronExpression { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public string TimeZone { get; set; } = "UTC";
    public List<int> DaysOfWeek { get; set; } = new();
    public List<int> DaysOfMonth { get; set; } = new();
    public int? Hour { get; set; }
    public int? Minute { get; set; }
    public Dictionary<string, object> CustomSettings { get; set; } = new();
}

/// <summary>
/// Delivery configuration data transfer object
/// </summary>
public class DeliveryConfigurationDto
{
    public string Method { get; set; } = string.Empty;
    public List<string> Recipients { get; set; } = new();
    public string Subject { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public Dictionary<string, object> Settings { get; set; } = new();
    public bool AttachReport { get; set; } = true;
    public bool EmbedInEmail { get; set; } = false;
    public List<string> CcRecipients { get; set; } = new();
    public List<string> BccRecipients { get; set; } = new();
}

/// <summary>
/// Report execution result data transfer object
/// </summary>
public class ReportExecutionResultDto
{
    public Guid ExecutionId { get; set; }
    public Guid ScheduleId { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public TimeSpan? Duration { get; set; }
    public string? ReportFilePath { get; set; }
    public long? ReportFileSize { get; set; }
    public int RecordCount { get; set; }
    public bool DeliverySuccessful { get; set; }
    public string? ErrorMessage { get; set; }
    public List<DeliveryResultDto> DeliveryResults { get; set; } = new();
    public Dictionary<string, object> Metrics { get; set; } = new();
}

/// <summary>
/// Delivery result data transfer object
/// </summary>
public class DeliveryResultDto
{
    public string Method { get; set; } = string.Empty;
    public string Recipient { get; set; } = string.Empty;
    public bool IsSuccessful { get; set; }
    public DateTime DeliveredAt { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Report execution history data transfer object
/// </summary>
public class ReportExecutionHistoryDto
{
    public Guid ExecutionId { get; set; }
    public DateTime StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string Status { get; set; } = string.Empty;
    public TimeSpan? Duration { get; set; }
    public int RecordCount { get; set; }
    public bool DeliverySuccessful { get; set; }
    public string? ErrorMessage { get; set; }
    public string TriggerType { get; set; } = string.Empty;
}

/// <summary>
/// Report template data transfer object
/// </summary>
public class ReportTemplateDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public ReportConfigurationDto DefaultConfiguration { get; set; } = new();
    public List<string> RequiredParameters { get; set; } = new();
    public bool IsCustomizable { get; set; }
    public string PreviewImage { get; set; } = string.Empty;
}

/// <summary>
/// Delivery method data transfer object
/// </summary>
public class DeliveryMethodDto
{
    public string Method { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<DeliveryParameterDto> RequiredParameters { get; set; } = new();
    public List<DeliveryParameterDto> OptionalParameters { get; set; } = new();
    public List<string> SupportedFormats { get; set; } = new();
    public bool IsEnabled { get; set; }
}

/// <summary>
/// Delivery parameter data transfer object
/// </summary>
public class DeliveryParameterDto
{
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public bool IsRequired { get; set; }
    public object? DefaultValue { get; set; }
    public List<object> AllowedValues { get; set; } = new();
}

/// <summary>
/// Delivery test result data transfer object
/// </summary>
public class DeliveryTestResult
{
    public bool IsSuccessful { get; set; }
    public string? ErrorMessage { get; set; }
    public TimeSpan ResponseTime { get; set; }
    public Dictionary<string, object> TestResults { get; set; } = new();
}

/// <summary>
/// Schedule statistics data transfer object
/// </summary>
public class ScheduleStatisticsDto
{
    public int TotalSchedules { get; set; }
    public int ActiveSchedules { get; set; }
    public int InactiveSchedules { get; set; }
    public int TotalExecutions { get; set; }
    public int SuccessfulExecutions { get; set; }
    public int FailedExecutions { get; set; }
    public decimal SuccessRate { get; set; }
    public DateTime? LastExecution { get; set; }
    public DateTime? NextExecution { get; set; }
    public List<ScheduleFrequencyStatsDto> FrequencyStats { get; set; } = new();
    public List<DeliveryMethodStatsDto> DeliveryStats { get; set; } = new();
}

/// <summary>
/// Schedule frequency statistics data transfer object
/// </summary>
public class ScheduleFrequencyStatsDto
{
    public string ScheduleType { get; set; } = string.Empty;
    public int Count { get; set; }
    public decimal Percentage { get; set; }
}

/// <summary>
/// Delivery method statistics data transfer object
/// </summary>
public class DeliveryMethodStatsDto
{
    public string Method { get; set; } = string.Empty;
    public int Count { get; set; }
    public decimal SuccessRate { get; set; }
}

// Request DTOs
/// <summary>
/// Create scheduled report request
/// </summary>
public class CreateScheduledReportRequest
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public ReportConfigurationDto ReportConfiguration { get; set; } = new();
    public ScheduleConfigurationDto ScheduleConfiguration { get; set; } = new();
    public DeliveryConfigurationDto DeliveryConfiguration { get; set; } = new();
    public Guid UserId { get; set; }
    public UserType UserType { get; set; }
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// Update scheduled report request
/// </summary>
public class UpdateScheduledReportRequest
{
    public string? Name { get; set; }
    public string? Description { get; set; }
    public ReportConfigurationDto? ReportConfiguration { get; set; }
    public ScheduleConfigurationDto? ScheduleConfiguration { get; set; }
    public DeliveryConfigurationDto? DeliveryConfiguration { get; set; }
    public bool? IsActive { get; set; }
}
