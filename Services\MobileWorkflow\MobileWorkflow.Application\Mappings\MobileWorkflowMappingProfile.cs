using AutoMapper;
using MobileWorkflow.Application.DTOs;
using MobileWorkflow.Domain.Entities;

namespace MobileWorkflow.Application.Mappings;

public class MobileWorkflowMappingProfile : Profile
{
    public MobileWorkflowMappingProfile()
    {
        // Mobile App mappings
        CreateMap<MobileApp, MobileAppDto>();
        CreateMap<CreateMobileAppDto, MobileApp>()
            .ConstructUsing(src => new MobileApp(
                src.Name,
                src.Version,
                src.Platform,
                src.PackageId,
                src.ReleaseDate,
                src.MinimumOSVersion,
                src.Features,
                src.Configuration,
                src.SupportsOffline,
                src.DownloadUrl,
                src.FileSize,
                src.Checksum));

        // Mobile Session mappings
        CreateMap<MobileSession, MobileSessionDto>();
        CreateMap<CreateMobileSessionDto, MobileSession>()
            .ConstructUsing(src => new MobileSession(
                src.UserId,
                src.MobileAppId,
                src.DeviceId,
                src.DeviceInfo,
                src.AppVersion,
                src.Platform,
                src.OSVersion,
                src.IsOfflineCapable));

        // Offline Data mappings
        CreateMap<MobileWorkflow.Domain.Entities.OfflineData, MobileWorkflow.Application.DTOs.OfflineDataDto>();
        CreateMap<MobileWorkflow.Application.DTOs.CreateOfflineDataDto, MobileWorkflow.Domain.Entities.OfflineData>()
            .ConstructUsing(src => new MobileWorkflow.Domain.Entities.OfflineData(
                src.UserId,
                src.MobileSessionId,
                src.DataType,
                src.Action,
                src.Data,
                src.Priority,
                src.MobileAppId));

        // Workflow mappings
        CreateMap<Workflow, WorkflowDto>();
        CreateMap<CreateWorkflowDto, Workflow>()
            .ConstructUsing(src => new Workflow(
                src.Name,
                src.Description,
                src.Category,
                src.Version,
                src.Definition,
                src.Configuration,
                src.TriggerType,
                src.TriggerConfiguration,
                src.CreatedBy));

        // Workflow Execution mappings
        CreateMap<WorkflowExecution, WorkflowExecutionDto>();
        CreateMap<StartWorkflowExecutionDto, WorkflowExecution>()
            .ConstructUsing(src => new WorkflowExecution(
                src.WorkflowId,
                src.TriggeredBy,
                src.InputData,
                src.TriggerSource));

        // Workflow Task mappings
        CreateMap<WorkflowTask, WorkflowTaskDto>();
        CreateMap<CreateWorkflowTaskDto, WorkflowTask>()
            .ConstructUsing(src => src.WorkflowExecutionId.HasValue
                ? new WorkflowTask(
                    src.WorkflowExecutionId,
                    src.Name,
                    src.Type,
                    src.Parameters,
                    src.AssignedTo,
                    src.Description,
                    src.Priority,
                    src.SLA)
                : new WorkflowTask(
                    src.Name,
                    src.Type,
                    src.Parameters,
                    src.AssignedTo,
                    src.AssignedToRole,
                    src.Description,
                    src.Priority,
                    src.SLA));

        // Milestone Template mappings
        CreateMap<MilestoneTemplate, MilestoneTemplateDto>()
            .ForMember(dest => dest.TotalPayoutPercentage, opt => opt.Ignore())
            .ForMember(dest => dest.IsValid, opt => opt.Ignore())
            .ForMember(dest => dest.ValidationErrors, opt => opt.Ignore());

        CreateMap<CreateMilestoneTemplateRequest, MilestoneTemplate>()
            .ConstructUsing(src => new MilestoneTemplate(
                src.Name,
                src.Description,
                src.Type,
                src.Category,
                "system")) // CreatedBy will be set by the handler
            .ForMember(dest => dest.Steps, opt => opt.Ignore())
            .ForMember(dest => dest.RoleMappings, opt => opt.Ignore());

        CreateMap<MilestoneTemplate, MilestoneTemplatePreviewDto>()
            .ForMember(dest => dest.StepCount, opt => opt.Ignore())
            .ForMember(dest => dest.TotalPayoutPercentage, opt => opt.Ignore())
            .ForMember(dest => dest.IsValid, opt => opt.Ignore());

        // Milestone Step mappings
        CreateMap<MilestoneStep, MilestoneStepDto>()
            .ForMember(dest => dest.TotalPayoutPercentage, opt => opt.Ignore());

        CreateMap<CreateMilestoneStepRequest, MilestoneStep>()
            .ConstructUsing(src => new MilestoneStep(
                Guid.Empty, // TemplateId will be set by the handler
                src.Name,
                src.Description,
                src.SequenceNumber,
                src.IsRequired))
            .ForMember(dest => dest.PayoutRules, opt => opt.Ignore());

        // Milestone Payout Rule mappings
        CreateMap<MilestonePayoutRule, MilestonePayoutRuleDto>();

        CreateMap<CreateMilestonePayoutRuleRequest, MilestonePayoutRule>()
            .ConstructUsing(src => new MilestonePayoutRule(
                Guid.Empty, // StepId will be set by the handler
                src.PayoutPercentage,
                src.TriggerCondition,
                src.Description));

        // Role Template Mappings
        CreateMap<RoleTemplateMappings, RoleTemplateMappingDto>()
            .ForMember(dest => dest.MilestoneTemplateName, opt => opt.MapFrom(src => src.MilestoneTemplate.Name));

        CreateMap<CreateRoleTemplateMappingRequest, RoleTemplateMappings>()
            .ConstructUsing(src => new RoleTemplateMappings(
                src.RoleName,
                src.MilestoneTemplateId,
                "system", // CreatedBy will be set by the handler
                src.IsDefault,
                src.Priority));
    }
}
