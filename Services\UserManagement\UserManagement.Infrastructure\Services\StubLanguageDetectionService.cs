using UserManagement.Application.Interfaces;

namespace UserManagement.Infrastructure.Services
{
    public class StubLanguageDetectionService : ILanguageDetectionService
    {
        public Task<string> DetectLanguageAsync(string text)
        {
            // Stub implementation - always return English
            return Task.FromResult("en");
        }

        public Task<LanguageDetectionResult> DetectLanguageWithConfidenceAsync(string text)
        {
            // Stub implementation - return English with high confidence
            return Task.FromResult(new LanguageDetectionResult
            {
                Language = "en",
                LanguageName = "English",
                Confidence = 0.95,
                IsReliable = true
            });
        }

        public Task<List<LanguageDetectionResult>> DetectMultipleLanguagesAsync(string text)
        {
            // Stub implementation - return only English
            return Task.FromResult(new List<LanguageDetectionResult>
            {
                new LanguageDetectionResult
                {
                    Language = "en",
                    LanguageName = "English",
                    Confidence = 0.95,
                    IsReliable = true
                }
            });
        }

        public Task<bool> IsLanguageSupportedAsync(string languageCode)
        {
            // Stub implementation - support common languages
            var supportedLanguages = new[] { "en", "es", "fr", "de", "it", "pt", "hi", "zh", "ja", "ko" };
            return Task.FromResult(supportedLanguages.Contains(languageCode.ToLowerInvariant()));
        }
    }

    public class LanguageDetectionResult
    {
        public string Language { get; set; } = string.Empty;
        public string LanguageName { get; set; } = string.Empty;
        public double Confidence { get; set; }
        public bool IsReliable { get; set; }
    }
}
