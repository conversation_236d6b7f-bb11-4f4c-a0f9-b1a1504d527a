using Microsoft.Extensions.Logging;
using NetworkFleetManagement.Infrastructure.Services;
using NetworkFleetManagement.Infrastructure.Monitoring.Metrics;

namespace NetworkFleetManagement.Infrastructure.Monitoring.Alerting;

public interface IFleetAlertingService
{
    Task TriggerVehicleMaintenanceAlertAsync(Guid vehicleId, string alertType, Dictionary<string, object>? alertData = null);
    Task TriggerDriverComplianceAlertAsync(Guid driverId, string alertType, Dictionary<string, object>? alertData = null);
    Task TriggerFleetCapacityAlertAsync(Guid carrierId, string alertType, Dictionary<string, object>? alertData = null);
    Task TriggerNetworkPerformanceAlertAsync(Guid networkId, string alertType, Dictionary<string, object>? alertData = null);
    Task TriggerSystemHealthAlertAsync(string alertType, string message, Dictionary<string, object>? alertData = null);
    Task TriggerSecurityAlertAsync(string alertType, string message, Dictionary<string, object>? alertData = null);
    Task ProcessAlertRulesAsync();
}

public class FleetAlertingService : IFleetAlertingService
{
    private readonly IRealTimeTrackingService _realTimeService;
    private readonly FleetMetricsCollector _metricsCollector;
    private readonly ILogger<FleetAlertingService> _logger;
    private readonly Dictionary<string, AlertRule> _alertRules;

    public FleetAlertingService(
        IRealTimeTrackingService realTimeService,
        FleetMetricsCollector metricsCollector,
        ILogger<FleetAlertingService> logger)
    {
        _realTimeService = realTimeService;
        _metricsCollector = metricsCollector;
        _logger = logger;
        _alertRules = InitializeAlertRules();
    }

    public async Task TriggerVehicleMaintenanceAlertAsync(Guid vehicleId, string alertType, Dictionary<string, object>? alertData = null)
    {
        try
        {
            var severity = GetAlertSeverity(alertType);
            var alert = CreateAlert(alertType, severity, alertData);
            
            // Record metrics
            _metricsCollector.RecordAlert(alertType, severity, new Dictionary<string, object>
            {
                ["entity_type"] = "vehicle",
                ["entity_id"] = vehicleId.ToString()
            });

            // Broadcast alert
            await _realTimeService.BroadcastVehicleMaintenanceAlertAsync(vehicleId, alertType, alert);
            
            _logger.LogInformation("Vehicle maintenance alert triggered: {VehicleId} - {AlertType}", vehicleId, alertType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error triggering vehicle maintenance alert for {VehicleId}", vehicleId);
        }
    }

    public async Task TriggerDriverComplianceAlertAsync(Guid driverId, string alertType, Dictionary<string, object>? alertData = null)
    {
        try
        {
            var severity = GetAlertSeverity(alertType);
            var alert = CreateAlert(alertType, severity, alertData);
            
            // Record metrics
            _metricsCollector.RecordAlert(alertType, severity, new Dictionary<string, object>
            {
                ["entity_type"] = "driver",
                ["entity_id"] = driverId.ToString()
            });

            // Broadcast alert
            await _realTimeService.BroadcastDriverAlertAsync(driverId, alertType, alert);
            
            _logger.LogInformation("Driver compliance alert triggered: {DriverId} - {AlertType}", driverId, alertType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error triggering driver compliance alert for {DriverId}", driverId);
        }
    }

    public async Task TriggerFleetCapacityAlertAsync(Guid carrierId, string alertType, Dictionary<string, object>? alertData = null)
    {
        try
        {
            var severity = GetAlertSeverity(alertType);
            var alert = CreateAlert(alertType, severity, alertData);
            var message = GenerateAlertMessage(alertType, alertData);
            
            // Record metrics
            _metricsCollector.RecordAlert(alertType, severity, new Dictionary<string, object>
            {
                ["entity_type"] = "carrier",
                ["entity_id"] = carrierId.ToString()
            });

            // Broadcast alert
            await _realTimeService.BroadcastFleetAlertAsync(carrierId, alertType, message, alert);
            
            _logger.LogInformation("Fleet capacity alert triggered: {CarrierId} - {AlertType}", carrierId, alertType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error triggering fleet capacity alert for {CarrierId}", carrierId);
        }
    }

    public async Task TriggerNetworkPerformanceAlertAsync(Guid networkId, string alertType, Dictionary<string, object>? alertData = null)
    {
        try
        {
            var severity = GetAlertSeverity(alertType);
            var alert = CreateAlert(alertType, severity, alertData);
            
            // Record metrics
            _metricsCollector.RecordAlert(alertType, severity, new Dictionary<string, object>
            {
                ["entity_type"] = "network",
                ["entity_id"] = networkId.ToString()
            });

            // Broadcast alert
            await _realTimeService.BroadcastNetworkAlertAsync(networkId, alertType, alert);
            
            _logger.LogInformation("Network performance alert triggered: {NetworkId} - {AlertType}", networkId, alertType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error triggering network performance alert for {NetworkId}", networkId);
        }
    }

    public async Task TriggerSystemHealthAlertAsync(string alertType, string message, Dictionary<string, object>? alertData = null)
    {
        try
        {
            var severity = GetAlertSeverity(alertType);
            var alert = CreateAlert(alertType, severity, alertData);
            
            // Record metrics
            _metricsCollector.RecordAlert(alertType, severity, new Dictionary<string, object>
            {
                ["entity_type"] = "system"
            });

            // Broadcast alert
            await _realTimeService.BroadcastSystemAlertAsync(alertType, message, alert);
            
            _logger.LogWarning("System health alert triggered: {AlertType} - {Message}", alertType, message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error triggering system health alert: {AlertType}", alertType);
        }
    }

    public async Task TriggerSecurityAlertAsync(string alertType, string message, Dictionary<string, object>? alertData = null)
    {
        try
        {
            var severity = "critical"; // Security alerts are always critical
            var alert = CreateAlert(alertType, severity, alertData);
            
            // Record metrics
            _metricsCollector.RecordAlert(alertType, severity, new Dictionary<string, object>
            {
                ["entity_type"] = "security"
            });

            // Broadcast alert
            await _realTimeService.BroadcastSystemAlertAsync(alertType, message, alert);
            
            _logger.LogCritical("Security alert triggered: {AlertType} - {Message}", alertType, message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error triggering security alert: {AlertType}", alertType);
        }
    }

    public async Task ProcessAlertRulesAsync()
    {
        try
        {
            foreach (var rule in _alertRules.Values)
            {
                if (await ShouldTriggerAlert(rule))
                {
                    await TriggerRuleBasedAlert(rule);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing alert rules");
        }
    }

    // Helper methods
    private static string GetAlertSeverity(string alertType)
    {
        return alertType.ToLowerInvariant() switch
        {
            var type when type.Contains("critical") || type.Contains("emergency") || type.Contains("security") => "critical",
            var type when type.Contains("warning") || type.Contains("maintenance") || type.Contains("compliance") => "warning",
            var type when type.Contains("info") || type.Contains("notification") => "info",
            _ => "medium"
        };
    }

    private static Dictionary<string, object> CreateAlert(string alertType, string severity, Dictionary<string, object>? alertData)
    {
        var alert = new Dictionary<string, object>
        {
            ["alertType"] = alertType,
            ["severity"] = severity,
            ["timestamp"] = DateTime.UtcNow,
            ["id"] = Guid.NewGuid()
        };

        if (alertData != null)
        {
            foreach (var kvp in alertData)
            {
                alert[kvp.Key] = kvp.Value;
            }
        }

        return alert;
    }

    private static string GenerateAlertMessage(string alertType, Dictionary<string, object>? alertData)
    {
        return alertType switch
        {
            "fleet_capacity_low" => "Fleet capacity is running low. Consider adding more vehicles or drivers.",
            "fleet_utilization_high" => "Fleet utilization is very high. Monitor for potential overload.",
            "maintenance_overdue" => "Multiple vehicles have overdue maintenance.",
            "driver_compliance_low" => "Driver compliance rates are below acceptable levels.",
            "network_performance_degraded" => "Network performance has degraded significantly.",
            _ => $"Alert: {alertType}"
        };
    }

    private Dictionary<string, AlertRule> InitializeAlertRules()
    {
        return new Dictionary<string, AlertRule>
        {
            ["fleet_capacity"] = new AlertRule
            {
                Name = "Fleet Capacity Monitor",
                Type = "fleet_capacity_low",
                Condition = "available_vehicles < 10",
                Severity = "warning",
                Enabled = true,
                CooldownMinutes = 30
            },
            ["maintenance_overdue"] = new AlertRule
            {
                Name = "Maintenance Overdue Monitor",
                Type = "maintenance_overdue",
                Condition = "overdue_maintenance_count > 5",
                Severity = "warning",
                Enabled = true,
                CooldownMinutes = 60
            },
            ["driver_compliance"] = new AlertRule
            {
                Name = "Driver Compliance Monitor",
                Type = "driver_compliance_low",
                Condition = "compliance_rate < 0.8",
                Severity = "warning",
                Enabled = true,
                CooldownMinutes = 120
            }
        };
    }

    private async Task<bool> ShouldTriggerAlert(AlertRule rule)
    {
        if (!rule.Enabled)
            return false;

        if (rule.LastTriggered.HasValue && 
            DateTime.UtcNow - rule.LastTriggered.Value < TimeSpan.FromMinutes(rule.CooldownMinutes))
            return false;

        // Implement condition evaluation logic here
        // This would typically involve checking metrics or database values
        return false; // Placeholder
    }

    private async Task TriggerRuleBasedAlert(AlertRule rule)
    {
        try
        {
            rule.LastTriggered = DateTime.UtcNow;
            
            var alertData = new Dictionary<string, object>
            {
                ["rule_name"] = rule.Name,
                ["condition"] = rule.Condition
            };

            await TriggerSystemHealthAlertAsync(rule.Type, rule.Name, alertData);
            
            _logger.LogInformation("Rule-based alert triggered: {RuleName}", rule.Name);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error triggering rule-based alert: {RuleName}", rule.Name);
        }
    }
}

public class AlertRule
{
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Condition { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public bool Enabled { get; set; }
    public int CooldownMinutes { get; set; }
    public DateTime? LastTriggered { get; set; }
}
