using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using DataStorage.Domain.Entities;

namespace DataStorage.Infrastructure.Configurations;

public class DocumentConfiguration : IEntityTypeConfiguration<Document>
{
    public void Configure(EntityTypeBuilder<Document> builder)
    {
        builder.ToTable("documents");

        builder.HasKey(d => d.Id);

        builder.Property(d => d.Id)
            .ValueGeneratedNever();

        builder.Property(d => d.Title)
            .HasMaxLength(255)
            .IsRequired();

        builder.Property(d => d.Description)
            .HasMaxLength(1000);

        builder.Property(d => d.OwnerId)
            .IsRequired();

        builder.Property(d => d.OwnerType)
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(d => d.Version)
            .IsRequired();

        builder.Property(d => d.IsLatestVersion)
            .IsRequired();

        builder.Property(d => d.CreatedAt)
            .IsRequired();

        builder.Property(d => d.UpdatedAt);

        builder.Property(d => d.ExpiresAt);

        builder.Property(d => d.ExternalReference)
            .HasMaxLength(255);

        // Configure Tags as JSON
        builder.Property(d => d.Tags)
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new Dictionary<string, string>())
            .HasColumnType("jsonb");

        // Configure relationships
        builder.HasMany<DocumentVersion>()
            .WithOne()
            .HasForeignKey(dv => dv.DocumentId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany<DocumentAccess>()
            .WithOne()
            .HasForeignKey(da => da.DocumentId)
            .OnDelete(DeleteBehavior.Cascade);

        // Configure indexes
        builder.HasIndex(d => d.OwnerId)
            .HasDatabaseName("IX_Documents_OwnerId");

        builder.HasIndex(d => d.DocumentType)
            .HasDatabaseName("IX_Documents_DocumentType");

        builder.HasIndex(d => d.Status)
            .HasDatabaseName("IX_Documents_Status");

        builder.HasIndex(d => d.CreatedAt)
            .HasDatabaseName("IX_Documents_CreatedAt");

        builder.HasIndex(d => d.ExpiresAt)
            .HasDatabaseName("IX_Documents_ExpiresAt");

        builder.HasIndex(d => new { d.OwnerId, d.DocumentType })
            .HasDatabaseName("IX_Documents_Owner_Type");

        builder.HasIndex(d => new { d.Status, d.CreatedAt })
            .HasDatabaseName("IX_Documents_Status_CreatedAt");

        // Configure full-text search index for title and description
        builder.HasIndex(d => d.Title)
            .HasDatabaseName("IX_Documents_Title_FullText");

        // Ignore domain events (handled by base class)
        builder.Ignore(d => d.DomainEvents);
    }
}

public class DocumentVersionConfiguration : IEntityTypeConfiguration<DocumentVersion>
{
    public void Configure(EntityTypeBuilder<DocumentVersion> builder)
    {
        builder.ToTable("document_versions");

        builder.HasKey(dv => dv.Id);

        builder.Property(dv => dv.Id)
            .ValueGeneratedNever();

        builder.Property(dv => dv.DocumentId)
            .IsRequired();

        builder.Property(dv => dv.VersionNumber)
            .IsRequired();

        builder.Property(dv => dv.ChangeDescription)
            .HasMaxLength(500);

        builder.Property(dv => dv.CreatedAt)
            .IsRequired();

        builder.Property(dv => dv.CreatedBy)
            .IsRequired();

        builder.Property(dv => dv.IsActive)
            .IsRequired();

        // Configure indexes
        builder.HasIndex(dv => dv.DocumentId)
            .HasDatabaseName("IX_DocumentVersions_DocumentId");

        builder.HasIndex(dv => new { dv.DocumentId, dv.VersionNumber })
            .IsUnique()
            .HasDatabaseName("IX_DocumentVersions_Document_Version");

        builder.HasIndex(dv => dv.CreatedAt)
            .HasDatabaseName("IX_DocumentVersions_CreatedAt");
    }
}

public class DocumentAccessConfiguration : IEntityTypeConfiguration<DocumentAccess>
{
    public void Configure(EntityTypeBuilder<DocumentAccess> builder)
    {
        builder.ToTable("document_access");

        builder.HasKey(da => da.Id);

        builder.Property(da => da.Id)
            .ValueGeneratedNever();

        builder.Property(da => da.DocumentId)
            .IsRequired();

        builder.Property(da => da.UserId)
            .IsRequired();

        builder.Property(da => da.UserType)
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(da => da.GrantedAt)
            .IsRequired();

        builder.Property(da => da.ExpiresAt);

        // Configure indexes
        builder.HasIndex(da => da.DocumentId)
            .HasDatabaseName("IX_DocumentAccess_DocumentId");

        builder.HasIndex(da => da.UserId)
            .HasDatabaseName("IX_DocumentAccess_UserId");

        builder.HasIndex(da => new { da.DocumentId, da.UserId })
            .IsUnique()
            .HasDatabaseName("IX_DocumentAccess_Document_User");

        builder.HasIndex(da => da.ExpiresAt)
            .HasDatabaseName("IX_DocumentAccess_ExpiresAt");
    }
}
