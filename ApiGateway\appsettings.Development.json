{"Logging": {"LogLevel": {"Default": "Debug", "Microsoft.AspNetCore": "Information", "Ocelot": "Debug"}}, "AllowedHosts": "*", "JwtSettings": {"Secret": "ThisIsAVerySecureKeyThatShouldBeStoredInASecureVault", "Issuer": "TriTrackzIdentity", "Audience": "TriTrackzServices", "AccessTokenExpirationMinutes": 120, "RefreshTokenExpirationDays": 7}, "IpRateLimiting": {"EnableEndpointRateLimiting": false, "GeneralRules": [{"Endpoint": "*", "Period": "1m", "Limit": 10000}]}, "ThirdPartyServices": {"GoogleMaps": {"ApiKey": "your-google-maps-api-key-dev", "BaseUrl": "https://maps.googleapis.com", "RateLimitPerMinute": 10000, "TimeoutSeconds": 60}, "Razorpay": {"KeyId": "rzp_test_your_key_id", "KeySecret": "your_test_key_secret", "WebhookSecret": "your_test_webhook_secret", "BaseUrl": "https://api.razorpay.com/v1", "TimeoutSeconds": 60}}, "Security": {"EnableHttpsRedirection": false, "EnableSecurityHeaders": true, "AllowedOrigins": ["http://localhost:3000", "http://localhost:3001", "http://localhost:4200", "https://localhost:3000", "https://localhost:3001", "https://localhost:4200"]}, "Monitoring": {"EnableHealthChecks": true, "EnableMetrics": true, "HealthCheckIntervalSeconds": 10}}