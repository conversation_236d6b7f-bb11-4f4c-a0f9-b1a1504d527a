using MediatR;
using Microsoft.Extensions.Logging;
using UserManagement.Application.Services;
using UserManagement.Domain.Repositories;
using UserManagement.Domain.Exceptions;
using Shared.Messaging;
using System.Text.Json;

namespace UserManagement.Application.Documents.Commands.ProcessDocumentWithOcr
{
    public class ProcessDocumentWithOcrCommandHandler : IRequestHandler<ProcessDocumentWithOcrCommand, ProcessDocumentWithOcrResponse>
    {
        private readonly IDocumentSubmissionRepository _documentSubmissionRepository;
        private readonly IOcrService _ocrService;
        private readonly IMessageBroker _messageBroker;
        private readonly ILogger<ProcessDocumentWithOcrCommandHandler> _logger;

        public ProcessDocumentWithOcrCommandHandler(
            IDocumentSubmissionRepository documentSubmissionRepository,
            IOcrService ocrService,
            IMessageBroker messageBroker,
            ILogger<ProcessDocumentWithOcrCommandHandler> logger)
        {
            _documentSubmissionRepository = documentSubmissionRepository;
            _ocrService = ocrService;
            _messageBroker = messageBroker;
            _logger = logger;
        }

        public async Task<ProcessDocumentWithOcrResponse> Handle(ProcessDocumentWithOcrCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Processing document with OCR: SubmissionId={SubmissionId}, DocumentType={DocumentType}", 
                request.SubmissionId, request.DocumentType);

            try
            {
                // Get the document submission
                var submission = await _documentSubmissionRepository.GetByIdAsync(request.SubmissionId);
                if (submission == null)
                {
                    _logger.LogWarning("Document submission not found: {SubmissionId}", request.SubmissionId);
                    return new ProcessDocumentWithOcrResponse
                    {
                        Success = false,
                        Message = "Document submission not found"
                    };
                }

                // Check if document type is supported
                var isSupported = await _ocrService.IsDocumentTypeSupported(request.DocumentType);
                if (!isSupported)
                {
                    _logger.LogWarning("Document type not supported for OCR: {DocumentType}", request.DocumentType);
                    return new ProcessDocumentWithOcrResponse
                    {
                        Success = false,
                        Message = $"Document type {request.DocumentType} is not supported for OCR processing"
                    };
                }

                // Extract data using OCR
                var ocrResult = await _ocrService.ExtractDataAsync(request.FilePath, request.DocumentType, cancellationToken);
                
                if (!ocrResult.Success)
                {
                    _logger.LogError("OCR extraction failed: {ErrorMessage}", ocrResult.ErrorMessage);
                    return new ProcessDocumentWithOcrResponse
                    {
                        Success = false,
                        Message = $"OCR extraction failed: {ocrResult.ErrorMessage}"
                    };
                }

                // Find the specific document in the submission
                var document = submission.Documents.FirstOrDefault(d => d.DocumentType == request.DocumentType);
                if (document == null)
                {
                    _logger.LogWarning("Document not found in submission: DocumentType={DocumentType}", request.DocumentType);
                    return new ProcessDocumentWithOcrResponse
                    {
                        Success = false,
                        Message = $"Document of type {request.DocumentType} not found in submission"
                    };
                }

                // Update document with OCR results
                var extractedDataJson = JsonSerializer.Serialize(ocrResult.ExtractedData);
                document.SetAIExtractionResults(extractedDataJson, ocrResult.ConfidenceScore);

                var response = new ProcessDocumentWithOcrResponse
                {
                    Success = true,
                    ExtractedText = ocrResult.ExtractedText,
                    ExtractedData = ocrResult.ExtractedData,
                    ConfidenceScore = ocrResult.ConfidenceScore,
                    ValidationErrors = ocrResult.ValidationResult.Errors.Select(e => e.Message).ToList(),
                    Warnings = ocrResult.Warnings,
                    ProcessedAt = DateTime.UtcNow
                };

                // Determine if manual review is required
                var requiresManualReview = ocrResult.ConfidenceScore < request.MinimumConfidenceThreshold ||
                                         ocrResult.ValidationResult.Errors.Any();

                response.RequiresManualReview = requiresManualReview;

                // Auto-approve if confidence is high enough and no validation errors
                if (!requiresManualReview && 
                    request.AutoApproveIfHighConfidence && 
                    ocrResult.ConfidenceScore >= request.AutoApprovalThreshold &&
                    !ocrResult.ValidationResult.Errors.Any())
                {
                    try
                    {
                        submission.ApproveDocument(request.DocumentType, "Auto-approved based on high OCR confidence");
                        response.AutoApproved = true;
                        response.Message = "Document processed and auto-approved based on high confidence OCR results";
                        
                        _logger.LogInformation("Document auto-approved: SubmissionId={SubmissionId}, DocumentType={DocumentType}, Confidence={Confidence}", 
                            request.SubmissionId, request.DocumentType, ocrResult.ConfidenceScore);
                    }
                    catch (UserManagementDomainException ex)
                    {
                        _logger.LogWarning("Failed to auto-approve document: {Message}", ex.Message);
                        response.Warnings.Add($"Auto-approval failed: {ex.Message}");
                    }
                }
                else
                {
                    response.Message = requiresManualReview 
                        ? "Document processed but requires manual review due to low confidence or validation errors"
                        : "Document processed successfully";
                }

                // Update the submission
                await _documentSubmissionRepository.UpdateAsync(submission);

                // Publish integration event
                await _messageBroker.PublishAsync("document.ocr.processed", new
                {
                    SubmissionId = request.SubmissionId,
                    UserId = submission.UserId,
                    DocumentType = request.DocumentType.ToString(),
                    ConfidenceScore = ocrResult.ConfidenceScore,
                    RequiresManualReview = requiresManualReview,
                    AutoApproved = response.AutoApproved,
                    ExtractedData = ocrResult.ExtractedData,
                    ValidationErrors = response.ValidationErrors,
                    ProcessedAt = response.ProcessedAt
                });

                _logger.LogInformation("OCR processing completed: SubmissionId={SubmissionId}, DocumentType={DocumentType}, Confidence={Confidence}, RequiresReview={RequiresReview}", 
                    request.SubmissionId, request.DocumentType, ocrResult.ConfidenceScore, requiresManualReview);

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing document with OCR: SubmissionId={SubmissionId}, DocumentType={DocumentType}", 
                    request.SubmissionId, request.DocumentType);
                
                return new ProcessDocumentWithOcrResponse
                {
                    Success = false,
                    Message = "An error occurred while processing the document with OCR"
                };
            }
        }
    }
}
