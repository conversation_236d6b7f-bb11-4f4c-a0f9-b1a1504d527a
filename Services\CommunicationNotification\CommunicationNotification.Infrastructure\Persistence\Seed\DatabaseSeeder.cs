using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace CommunicationNotification.Infrastructure.Persistence.Seed;

/// <summary>
/// Database seeder service for initializing data
/// </summary>
public class DatabaseSeeder : IHostedService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<DatabaseSeeder> _logger;

    public DatabaseSeeder(IServiceProvider serviceProvider, ILogger<DatabaseSeeder> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    public async Task StartAsync(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<CommunicationDbContext>();

        try
        {
            _logger.LogInformation("Starting database seeding process");

            // Ensure database is created and migrations are applied
            await context.Database.MigrateAsync(cancellationToken);
            _logger.LogInformation("Database migrations applied successfully");

            // Seed initial data
            await CommunicationSeedData.SeedAsync(context, _logger);

            _logger.LogInformation("Database seeding completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during database seeding");
            throw;
        }
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }
}

/// <summary>
/// Extension methods for database seeding
/// </summary>
public static class DatabaseSeederExtensions
{
    /// <summary>
    /// Add database seeder service
    /// </summary>
    public static IServiceCollection AddDatabaseSeeder(this IServiceCollection services)
    {
        services.AddHostedService<DatabaseSeeder>();
        return services;
    }

    /// <summary>
    /// Manually seed database (for development/testing)
    /// </summary>
    public static async Task SeedDatabaseAsync(this IServiceProvider serviceProvider)
    {
        using var scope = serviceProvider.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<CommunicationDbContext>();
        var logger = scope.ServiceProvider.GetRequiredService<ILogger<DatabaseSeeder>>();

        try
        {
            logger.LogInformation("Manual database seeding started");

            // Ensure database is created
            await context.Database.EnsureCreatedAsync();

            // Apply migrations
            await context.Database.MigrateAsync();

            // Seed data
            await CommunicationSeedData.SeedAsync(context, logger);

            logger.LogInformation("Manual database seeding completed");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during manual database seeding");
            throw;
        }
    }
}

/// <summary>
/// Development data seeder for testing purposes
/// </summary>
public static class DevelopmentDataSeeder
{
    /// <summary>
    /// Seed development test data
    /// </summary>
    public static async Task SeedDevelopmentDataAsync(CommunicationDbContext context, ILogger logger)
    {
        if (!await ShouldSeedDevelopmentData(context))
        {
            logger.LogInformation("Development data already exists, skipping seed");
            return;
        }

        logger.LogInformation("Seeding development test data");

        try
        {
            // Seed test notifications
            await SeedTestNotificationsAsync(context, logger);

            // Seed test messages
            await SeedTestMessagesAsync(context, logger);

            // Seed test delivery attempts
            await SeedTestDeliveryAttemptsAsync(context, logger);

            await context.SaveChangesAsync();
            logger.LogInformation("Development test data seeded successfully");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error seeding development test data");
            throw;
        }
    }

    private static async Task<bool> ShouldSeedDevelopmentData(CommunicationDbContext context)
    {
        // Only seed if no notifications exist
        return !await context.Notifications.AnyAsync();
    }

    private static async Task SeedTestNotificationsAsync(CommunicationDbContext context, ILogger logger)
    {
        var customerUserId = Guid.Parse("*************-3333-3333-************");
        var driverUserId = Guid.Parse("*************-2222-2222-************");

        var notifications = new List<Domain.Entities.Notification>
        {
            new Domain.Entities.Notification
            {
                Id = Guid.NewGuid(),
                UserId = customerUserId,
                MessageType = Domain.Enums.MessageType.TripConfirmation,
                Priority = Domain.Enums.NotificationPriority.High,
                Channel = Domain.Enums.NotificationChannel.WhatsApp,
                Status = Domain.Enums.NotificationStatus.Delivered,
                ScheduledAt = DateTime.UtcNow.AddMinutes(-30),
                SentAt = DateTime.UtcNow.AddMinutes(-29),
                DeliveredAt = DateTime.UtcNow.AddMinutes(-28),
                ReadAt = DateTime.UtcNow.AddMinutes(-25),
                RelatedEntityId = Guid.NewGuid(),
                RelatedEntityType = "Trip",
                ExternalId = "wa_msg_12345",
                RetryCount = 0,
                Metadata = new Dictionary<string, string>
                {
                    ["template_id"] = "trip_confirmation_whatsapp",
                    ["booking_id"] = "TLI_001",
                    ["driver_name"] = "Rajesh Kumar"
                },
                Content = new Domain.ValueObjects.MessageContent
                {
                    Subject = "Trip Confirmation",
                    Body = "🚛 *Trip Confirmed!*\n\n📋 Booking: TLI_001\n👨‍💼 Driver: Rajesh Kumar\n🚗 Vehicle: KA01AB1234\n⏰ Pickup: 09:00 AM\n📍 Location: Bangalore Airport",
                    Language = Domain.Enums.Language.English
                },
                CreatedAt = DateTime.UtcNow.AddMinutes(-30),
                UpdatedAt = DateTime.UtcNow.AddMinutes(-25)
            },

            new Domain.Entities.Notification
            {
                Id = Guid.NewGuid(),
                UserId = driverUserId,
                MessageType = Domain.Enums.MessageType.TripOpportunity,
                Priority = Domain.Enums.NotificationPriority.Normal,
                Channel = Domain.Enums.NotificationChannel.Push,
                Status = Domain.Enums.NotificationStatus.Delivered,
                ScheduledAt = DateTime.UtcNow.AddMinutes(-15),
                SentAt = DateTime.UtcNow.AddMinutes(-14),
                DeliveredAt = DateTime.UtcNow.AddMinutes(-13),
                RelatedEntityId = Guid.NewGuid(),
                RelatedEntityType = "TripOpportunity",
                ExternalId = "fcm_msg_67890",
                RetryCount = 0,
                Metadata = new Dictionary<string, string>
                {
                    ["route"] = "Bangalore to Mysore",
                    ["fare"] = "2500",
                    ["distance"] = "150km"
                },
                Content = new Domain.ValueObjects.MessageContent
                {
                    Subject = "New Trip Opportunity",
                    Body = "New trip available: Bangalore to Mysore. Fare: ₹2,500. Distance: 150km. Tap to accept.",
                    Language = Domain.Enums.Language.English
                },
                CreatedAt = DateTime.UtcNow.AddMinutes(-15),
                UpdatedAt = DateTime.UtcNow.AddMinutes(-13)
            },

            new Domain.Entities.Notification
            {
                Id = Guid.NewGuid(),
                UserId = customerUserId,
                MessageType = Domain.Enums.MessageType.TripUpdate,
                Priority = Domain.Enums.NotificationPriority.High,
                Channel = Domain.Enums.NotificationChannel.Sms,
                Status = Domain.Enums.NotificationStatus.Failed,
                ScheduledAt = DateTime.UtcNow.AddMinutes(-5),
                SentAt = DateTime.UtcNow.AddMinutes(-4),
                RelatedEntityId = Guid.NewGuid(),
                RelatedEntityType = "Trip",
                RetryCount = 2,
                FailureReason = "Invalid phone number format",
                Metadata = new Dictionary<string, string>
                {
                    ["trip_id"] = "TLI_002",
                    ["eta"] = "10 minutes"
                },
                Content = new Domain.ValueObjects.MessageContent
                {
                    Subject = null,
                    Body = "Your driver is 2km away and will arrive in 10 minutes. Trip ID: TLI_002",
                    Language = Domain.Enums.Language.English
                },
                CreatedAt = DateTime.UtcNow.AddMinutes(-5),
                UpdatedAt = DateTime.UtcNow.AddMinutes(-3)
            }
        };

        await context.Notifications.AddRangeAsync(notifications);
        logger.LogInformation("Seeded {Count} test notifications", notifications.Count);
    }

    private static async Task SeedTestMessagesAsync(CommunicationDbContext context, ILogger logger)
    {
        var customerUserId = Guid.Parse("*************-3333-3333-************");
        var adminUserId = Guid.Parse("11111111-1111-1111-1111-111111111111");

        // Get the conversation thread we created in seed data
        var conversationThread = await context.ConversationThreads.FirstOrDefaultAsync();
        if (conversationThread == null)
        {
            logger.LogWarning("No conversation thread found for seeding messages");
            return;
        }

        var messages = new List<Domain.Entities.Message>
        {
            new Domain.Entities.Message
            {
                Id = Guid.NewGuid(),
                SenderId = customerUserId,
                ReceiverId = adminUserId,
                MessageType = Domain.Enums.MessageType.Support,
                Status = Domain.Enums.MessageStatus.Read,
                Priority = Domain.Enums.MessagePriority.Normal,
                SentAt = DateTime.UtcNow.AddMinutes(-20),
                DeliveredAt = DateTime.UtcNow.AddMinutes(-19),
                ReadAt = DateTime.UtcNow.AddMinutes(-18),
                ConversationThreadId = conversationThread.Id,
                RelatedEntityId = Guid.NewGuid(),
                RelatedEntityType = "Trip",
                RetryCount = 0,
                Metadata = new Dictionary<string, string>
                {
                    ["message_type"] = "support_request",
                    ["category"] = "booking_issue"
                },
                Content = new Domain.ValueObjects.MessageContent
                {
                    Subject = "Booking Issue",
                    Body = "Hi, I'm having trouble with my trip booking. The pickup location seems incorrect. Can you help?",
                    Language = Domain.Enums.Language.English
                },
                CreatedAt = DateTime.UtcNow.AddMinutes(-20),
                UpdatedAt = DateTime.UtcNow.AddMinutes(-18)
            },

            new Domain.Entities.Message
            {
                Id = Guid.NewGuid(),
                SenderId = adminUserId,
                ReceiverId = customerUserId,
                MessageType = Domain.Enums.MessageType.Support,
                Status = Domain.Enums.MessageStatus.Delivered,
                Priority = Domain.Enums.MessagePriority.Normal,
                SentAt = DateTime.UtcNow.AddMinutes(-15),
                DeliveredAt = DateTime.UtcNow.AddMinutes(-14),
                ConversationThreadId = conversationThread.Id,
                RelatedEntityId = Guid.NewGuid(),
                RelatedEntityType = "Trip",
                RetryCount = 0,
                Metadata = new Dictionary<string, string>
                {
                    ["message_type"] = "support_response",
                    ["agent_id"] = adminUserId.ToString()
                },
                Content = new Domain.ValueObjects.MessageContent
                {
                    Subject = "Re: Booking Issue",
                    Body = "Hello! I can help you with that. Let me check your booking details and update the pickup location. I'll get back to you within 5 minutes.",
                    Language = Domain.Enums.Language.English
                },
                CreatedAt = DateTime.UtcNow.AddMinutes(-15),
                UpdatedAt = DateTime.UtcNow.AddMinutes(-14)
            }
        };

        await context.Messages.AddRangeAsync(messages);
        logger.LogInformation("Seeded {Count} test messages", messages.Count);
    }

    private static async Task SeedTestDeliveryAttemptsAsync(CommunicationDbContext context, ILogger logger)
    {
        var notifications = await context.Notifications.Take(2).ToListAsync();
        if (!notifications.Any())
        {
            logger.LogWarning("No notifications found for seeding delivery attempts");
            return;
        }

        var deliveryAttempts = new List<Domain.Entities.DeliveryAttempt>
        {
            new Domain.Entities.DeliveryAttempt
            {
                Id = Guid.NewGuid(),
                NotificationId = notifications[0].Id,
                AttemptNumber = 1,
                Channel = Domain.Enums.NotificationChannel.WhatsApp,
                AttemptedAt = DateTime.UtcNow.AddMinutes(-29),
                IsSuccess = true,
                ExternalId = "wa_msg_12345",
                ResponseTime = TimeSpan.FromMilliseconds(1250),
                ProviderResponse = new Dictionary<string, object>
                {
                    ["status"] = "sent",
                    ["message_id"] = "wa_msg_12345",
                    ["timestamp"] = DateTime.UtcNow.AddMinutes(-29).ToString("O")
                },
                CreatedAt = DateTime.UtcNow.AddMinutes(-29),
                UpdatedAt = DateTime.UtcNow.AddMinutes(-29)
            },

            new Domain.Entities.DeliveryAttempt
            {
                Id = Guid.NewGuid(),
                NotificationId = notifications[1].Id,
                AttemptNumber = 1,
                Channel = Domain.Enums.NotificationChannel.Push,
                AttemptedAt = DateTime.UtcNow.AddMinutes(-14),
                IsSuccess = true,
                ExternalId = "fcm_msg_67890",
                ResponseTime = TimeSpan.FromMilliseconds(850),
                ProviderResponse = new Dictionary<string, object>
                {
                    ["status"] = "sent",
                    ["message_id"] = "fcm_msg_67890",
                    ["success"] = 1,
                    ["failure"] = 0
                },
                CreatedAt = DateTime.UtcNow.AddMinutes(-14),
                UpdatedAt = DateTime.UtcNow.AddMinutes(-14)
            }
        };

        await context.DeliveryAttempts.AddRangeAsync(deliveryAttempts);
        logger.LogInformation("Seeded {Count} test delivery attempts", deliveryAttempts.Count);
    }
}
