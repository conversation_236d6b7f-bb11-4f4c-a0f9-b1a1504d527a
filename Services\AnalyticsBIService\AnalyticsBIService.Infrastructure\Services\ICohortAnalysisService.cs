using AnalyticsBIService.Domain.Enums;

namespace AnalyticsBIService.Infrastructure.Services;

/// <summary>
/// Interface for cohort analysis service
/// </summary>
public interface ICohortAnalysisService
{
    /// <summary>
    /// Create a new cohort analysis
    /// </summary>
    Task<CohortAnalysisDto> CreateCohortAnalysisAsync(CreateCohortAnalysisRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get cohort analysis by ID
    /// </summary>
    Task<CohortAnalysisDto?> GetCohortAnalysisAsync(Guid cohortId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get user cohorts for a specific user type
    /// </summary>
    Task<List<CohortAnalysisDto>> GetUserCohortsAsync(Guid userId, UserType userType, CancellationToken cancellationToken = default);

    /// <summary>
    /// Calculate retention rates for a cohort
    /// </summary>
    Task<CohortRetentionDto> CalculateRetentionRatesAsync(Guid cohortId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get cohort performance metrics
    /// </summary>
    Task<CohortPerformanceDto> GetCohortPerformanceAsync(Guid cohortId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Compare multiple cohorts
    /// </summary>
    Task<CohortComparisonDto> CompareCohortPerformanceAsync(List<Guid> cohortIds, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get cohort segmentation analysis
    /// </summary>
    Task<CohortSegmentationDto> GetCohortSegmentationAsync(CohortSegmentationRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Calculate lifetime value for cohort
    /// </summary>
    Task<CohortLifetimeValueDto> CalculateLifetimeValueAsync(Guid cohortId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get cohort behavior patterns
    /// </summary>
    Task<CohortBehaviorDto> GetCohortBehaviorPatternsAsync(Guid cohortId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update cohort definition
    /// </summary>
    Task<bool> UpdateCohortDefinitionAsync(Guid cohortId, UpdateCohortRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete cohort analysis
    /// </summary>
    Task<bool> DeleteCohortAnalysisAsync(Guid cohortId, Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get available cohort templates
    /// </summary>
    Task<List<CohortTemplateDto>> GetCohortTemplatesAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Cohort analysis data transfer object
/// </summary>
public class CohortAnalysisDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public CohortDefinitionDto Definition { get; set; } = new();
    public CohortMetricsDto Metrics { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public Guid CreatedBy { get; set; }
    public UserType UserType { get; set; }
    public bool IsActive { get; set; }
    public int TotalUsers { get; set; }
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
}

/// <summary>
/// Cohort definition data transfer object
/// </summary>
public class CohortDefinitionDto
{
    public string CohortType { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public List<CohortCriteriaDto> Criteria { get; set; } = new();
    public string TimeGranularity { get; set; } = "Monthly";
    public List<string> MetricsToTrack { get; set; } = new();
    public Dictionary<string, object> CustomParameters { get; set; } = new();
}

/// <summary>
/// Cohort criteria data transfer object
/// </summary>
public class CohortCriteriaDto
{
    public string Field { get; set; } = string.Empty;
    public string Operator { get; set; } = string.Empty;
    public object Value { get; set; } = new();
    public string LogicalOperator { get; set; } = "AND";
}

/// <summary>
/// Cohort metrics data transfer object
/// </summary>
public class CohortMetricsDto
{
    public decimal RetentionRate { get; set; }
    public decimal ChurnRate { get; set; }
    public decimal AverageLifetimeValue { get; set; }
    public decimal AverageSessionDuration { get; set; }
    public int AverageSessionsPerUser { get; set; }
    public decimal ConversionRate { get; set; }
    public DateTime LastCalculated { get; set; }
}

/// <summary>
/// Cohort retention data transfer object
/// </summary>
public class CohortRetentionDto
{
    public Guid CohortId { get; set; }
    public List<RetentionPeriodDto> RetentionPeriods { get; set; } = new();
    public RetentionSummaryDto Summary { get; set; } = new();
    public List<RetentionTrendDto> Trends { get; set; } = new();
    public DateTime CalculatedAt { get; set; }
}

/// <summary>
/// Retention period data transfer object
/// </summary>
public class RetentionPeriodDto
{
    public int Period { get; set; }
    public string PeriodLabel { get; set; } = string.Empty;
    public int TotalUsers { get; set; }
    public int RetainedUsers { get; set; }
    public decimal RetentionRate { get; set; }
    public decimal ChurnRate { get; set; }
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
}

/// <summary>
/// Retention summary data transfer object
/// </summary>
public class RetentionSummaryDto
{
    public decimal AverageRetentionRate { get; set; }
    public decimal Day1Retention { get; set; }
    public decimal Day7Retention { get; set; }
    public decimal Day30Retention { get; set; }
    public decimal Day90Retention { get; set; }
    public int TotalCohortSize { get; set; }
    public string RetentionTrend { get; set; } = string.Empty;
}

/// <summary>
/// Retention trend data transfer object
/// </summary>
public class RetentionTrendDto
{
    public DateTime Date { get; set; }
    public decimal RetentionRate { get; set; }
    public int ActiveUsers { get; set; }
    public string TrendDirection { get; set; } = string.Empty;
}

/// <summary>
/// Cohort performance data transfer object
/// </summary>
public class CohortPerformanceDto
{
    public Guid CohortId { get; set; }
    public List<PerformanceMetricDto> Metrics { get; set; } = new();
    public List<PerformanceTrendDto> Trends { get; set; } = new();
    public PerformanceBenchmarkDto Benchmark { get; set; } = new();
    public DateTime CalculatedAt { get; set; }
}

/// <summary>
/// Performance trend data transfer object
/// </summary>
public class PerformanceTrendDto
{
    public DateTime Date { get; set; }
    public string MetricName { get; set; } = string.Empty;
    public decimal Value { get; set; }
    public decimal ChangeFromPrevious { get; set; }
    public string TrendDirection { get; set; } = string.Empty;
}

/// <summary>
/// Performance benchmark data transfer object
/// </summary>
public class PerformanceBenchmarkDto
{
    public decimal IndustryAverage { get; set; }
    public decimal CompanyAverage { get; set; }
    public decimal CohortPerformance { get; set; }
    public string PerformanceRating { get; set; } = string.Empty;
    public List<string> Recommendations { get; set; } = new();
}

/// <summary>
/// Cohort comparison data transfer object
/// </summary>
public class CohortComparisonDto
{
    public List<Guid> CohortIds { get; set; } = new();
    public List<CohortComparisonItemDto> Comparisons { get; set; } = new();
    public ComparisonSummaryDto Summary { get; set; } = new();
    public DateTime ComparedAt { get; set; }
}

/// <summary>
/// Cohort comparison item data transfer object
/// </summary>
public class CohortComparisonItemDto
{
    public Guid CohortId { get; set; }
    public string CohortName { get; set; } = string.Empty;
    public Dictionary<string, decimal> Metrics { get; set; } = new();
    public string PerformanceRank { get; set; } = string.Empty;
}

/// <summary>
/// Comparison summary data transfer object
/// </summary>
public class ComparisonSummaryDto
{
    public Guid BestPerformingCohort { get; set; }
    public Guid WorstPerformingCohort { get; set; }
    public string KeyDifferentiator { get; set; } = string.Empty;
    public List<string> Insights { get; set; } = new();
}

/// <summary>
/// Cohort segmentation data transfer object
/// </summary>
public class CohortSegmentationDto
{
    public List<CohortSegmentDto> Segments { get; set; } = new();
    public SegmentationSummaryDto Summary { get; set; } = new();
    public DateTime SegmentedAt { get; set; }
}

/// <summary>
/// Cohort segment data transfer object
/// </summary>
public class CohortSegmentDto
{
    public string SegmentName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int UserCount { get; set; }
    public decimal Percentage { get; set; }
    public Dictionary<string, object> Characteristics { get; set; } = new();
    public List<string> BehaviorPatterns { get; set; } = new();
}

/// <summary>
/// Segmentation summary data transfer object
/// </summary>
public class SegmentationSummaryDto
{
    public int TotalSegments { get; set; }
    public string LargestSegment { get; set; } = string.Empty;
    public string HighestValueSegment { get; set; } = string.Empty;
    public decimal SegmentationQuality { get; set; }
    public List<string> RecommendedActions { get; set; } = new();
}

/// <summary>
/// Cohort lifetime value data transfer object
/// </summary>
public class CohortLifetimeValueDto
{
    public Guid CohortId { get; set; }
    public decimal AverageLifetimeValue { get; set; }
    public decimal MedianLifetimeValue { get; set; }
    public decimal TotalLifetimeValue { get; set; }
    public List<LifetimeValueDistributionDto> Distribution { get; set; } = new();
    public List<LifetimeValueTrendDto> Trends { get; set; } = new();
    public DateTime CalculatedAt { get; set; }
}

/// <summary>
/// Lifetime value distribution data transfer object
/// </summary>
public class LifetimeValueDistributionDto
{
    public string Range { get; set; } = string.Empty;
    public int UserCount { get; set; }
    public decimal Percentage { get; set; }
    public decimal TotalValue { get; set; }
}

/// <summary>
/// Lifetime value trend data transfer object
/// </summary>
public class LifetimeValueTrendDto
{
    public DateTime Date { get; set; }
    public decimal CumulativeValue { get; set; }
    public decimal PeriodValue { get; set; }
    public decimal AverageValuePerUser { get; set; }
}

/// <summary>
/// Cohort behavior data transfer object
/// </summary>
public class CohortBehaviorDto
{
    public Guid CohortId { get; set; }
    public List<BehaviorPatternDto> Patterns { get; set; } = new();
    public List<UserJourneyDto> CommonJourneys { get; set; } = new();
    public BehaviorSummaryDto Summary { get; set; } = new();
    public DateTime AnalyzedAt { get; set; }
}

/// <summary>
/// Behavior pattern data transfer object
/// </summary>
public class BehaviorPatternDto
{
    public string PatternName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal Frequency { get; set; }
    public List<string> Actions { get; set; } = new();
    public string Impact { get; set; } = string.Empty;
}

/// <summary>
/// User journey data transfer object
/// </summary>
public class UserJourneyDto
{
    public string JourneyName { get; set; } = string.Empty;
    public List<string> Steps { get; set; } = new();
    public decimal CompletionRate { get; set; }
    public TimeSpan AverageDuration { get; set; }
    public List<string> DropOffPoints { get; set; } = new();
}

/// <summary>
/// Behavior summary data transfer object
/// </summary>
public class BehaviorSummaryDto
{
    public string MostCommonAction { get; set; } = string.Empty;
    public string LeastCommonAction { get; set; } = string.Empty;
    public decimal EngagementScore { get; set; }
    public List<string> KeyInsights { get; set; } = new();
}

/// <summary>
/// Cohort template data transfer object
/// </summary>
public class CohortTemplateDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public CohortDefinitionDto DefaultDefinition { get; set; } = new();
    public List<string> RecommendedMetrics { get; set; } = new();
    public bool IsCustomizable { get; set; }
}

// Request DTOs
/// <summary>
/// Create cohort analysis request
/// </summary>
public class CreateCohortAnalysisRequest
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public CohortDefinitionDto Definition { get; set; } = new();
    public Guid UserId { get; set; }
    public UserType UserType { get; set; }
}

/// <summary>
/// Update cohort request
/// </summary>
public class UpdateCohortRequest
{
    public string? Name { get; set; }
    public string? Description { get; set; }
    public CohortDefinitionDto? Definition { get; set; }
    public bool? IsActive { get; set; }
}

/// <summary>
/// Cohort segmentation request
/// </summary>
public class CohortSegmentationRequest
{
    public Guid CohortId { get; set; }
    public string SegmentationType { get; set; } = string.Empty;
    public List<string> SegmentationCriteria { get; set; } = new();
    public int MaxSegments { get; set; } = 10;
    public Guid UserId { get; set; }
}
