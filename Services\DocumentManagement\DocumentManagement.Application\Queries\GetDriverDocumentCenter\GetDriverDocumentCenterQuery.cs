using MediatR;

namespace DocumentManagement.Application.Queries.GetDriverDocumentCenter;

public class GetDriverDocumentCenterQuery : IRequest<GetDriverDocumentCenterResponse>
{
    public Guid DriverId { get; set; }
    public bool IncludeExpiredDocuments { get; set; } = true;
    public bool IncludeExpiringDocuments { get; set; } = true;
    public bool IncludeDocumentHistory { get; set; } = false;
    public bool IncludePreviewUrls { get; set; } = true;
    public string? DocumentTypeFilter { get; set; }
    public string? StatusFilter { get; set; }
    public int ExpiryWarningDays { get; set; } = 30;
}

public class GetDriverDocumentCenterResponse
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DriverDocumentCenterDto? DocumentCenter { get; set; }
}

public class DriverDocumentCenterDto
{
    public Guid DriverId { get; set; }
    public string DriverName { get; set; } = string.Empty;
    
    // Document categories
    public List<DocumentCategoryDto> DocumentCategories { get; set; } = new();
    
    // Document summary
    public DocumentSummaryDto Summary { get; set; } = new();
    
    // Expiry alerts and notifications
    public DocumentExpiryAlertsDto ExpiryAlerts { get; set; } = new();
    
    // Recent activity
    public List<DocumentActivityDto> RecentActivity { get; set; } = new();
    
    // Upload requirements
    public List<DocumentRequirementDto> UploadRequirements { get; set; } = new();
    
    // Quick actions
    public List<string> AvailableActions { get; set; } = new();
    
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
}

public class DocumentCategoryDto
{
    public string CategoryName { get; set; } = string.Empty;
    public string CategoryDescription { get; set; } = string.Empty;
    public List<DriverDocumentDto> Documents { get; set; } = new();
    public int TotalDocuments { get; set; }
    public int ValidDocuments { get; set; }
    public int ExpiredDocuments { get; set; }
    public int ExpiringDocuments { get; set; }
    public bool IsRequired { get; set; }
    public string? CategoryIcon { get; set; }
}

public class DriverDocumentDto
{
    public Guid DocumentId { get; set; }
    public string DocumentType { get; set; } = string.Empty;
    public string DocumentName { get; set; } = string.Empty;
    public string FileName { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime UploadedAt { get; set; }
    public DateTime? ExpiryDate { get; set; }
    public DateTime? VerifiedAt { get; set; }
    public string? VerifiedBy { get; set; }
    
    // File information
    public string ContentType { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public string FileSizeFormatted { get; set; } = string.Empty;
    
    // URLs and access
    public string? PreviewUrl { get; set; }
    public string? DownloadUrl { get; set; }
    public string? ThumbnailUrl { get; set; }
    
    // Status indicators
    public bool IsExpired { get; set; }
    public bool IsExpiringSoon { get; set; }
    public bool IsValid { get; set; }
    public bool RequiresRenewal { get; set; }
    public int? DaysUntilExpiry { get; set; }
    
    // Verification and compliance
    public DocumentVerificationDto? Verification { get; set; }
    public List<DocumentIssueDto> Issues { get; set; } = new();
    
    // Actions available
    public List<string> AvailableActions { get; set; } = new();
    
    // Metadata
    public Dictionary<string, object> Metadata { get; set; } = new();
    public List<string> Tags { get; set; } = new();
}

public class DocumentSummaryDto
{
    public int TotalDocuments { get; set; }
    public int ValidDocuments { get; set; }
    public int ExpiredDocuments { get; set; }
    public int ExpiringDocuments { get; set; }
    public int PendingVerification { get; set; }
    public int RejectedDocuments { get; set; }
    public double ComplianceScore { get; set; }
    public string ComplianceGrade { get; set; } = string.Empty;
    public DateTime LastDocumentUpload { get; set; }
    public DateTime? NextExpiryDate { get; set; }
    public List<string> CriticalIssues { get; set; } = new();
}

public class DocumentExpiryAlertsDto
{
    public List<ExpiryAlertDto> CriticalAlerts { get; set; } = new(); // Expired
    public List<ExpiryAlertDto> WarningAlerts { get; set; } = new(); // Expiring soon
    public List<ExpiryAlertDto> InfoAlerts { get; set; } = new(); // Renewal reminders
    public ExpiryNotificationSettingsDto NotificationSettings { get; set; } = new();
    public DateTime NextScheduledAlert { get; set; }
    public bool HasCriticalAlerts => CriticalAlerts.Any();
    public bool HasWarningAlerts => WarningAlerts.Any();
}

public class ExpiryAlertDto
{
    public Guid DocumentId { get; set; }
    public string DocumentType { get; set; } = string.Empty;
    public string DocumentName { get; set; } = string.Empty;
    public DateTime? ExpiryDate { get; set; }
    public int? DaysUntilExpiry { get; set; }
    public string AlertType { get; set; } = string.Empty; // Critical, Warning, Info
    public string AlertMessage { get; set; } = string.Empty;
    public string? ActionRequired { get; set; }
    public string? RenewalInstructions { get; set; }
    public DateTime AlertCreatedAt { get; set; }
    public bool IsAcknowledged { get; set; }
}

public class ExpiryNotificationSettingsDto
{
    public bool EmailNotificationsEnabled { get; set; } = true;
    public bool SMSNotificationsEnabled { get; set; } = false;
    public bool PushNotificationsEnabled { get; set; } = true;
    public List<int> NotificationDays { get; set; } = new() { 30, 15, 7, 1 }; // Days before expiry
    public string NotificationTime { get; set; } = "09:00"; // Time of day for notifications
    public List<string> NotificationRecipients { get; set; } = new();
}

public class DocumentActivityDto
{
    public Guid ActivityId { get; set; }
    public string ActivityType { get; set; } = string.Empty; // Upload, Verify, Expire, Renew, Delete
    public string ActivityDescription { get; set; } = string.Empty;
    public DateTime ActivityDate { get; set; }
    public string? PerformedBy { get; set; }
    public Guid? DocumentId { get; set; }
    public string? DocumentName { get; set; }
    public Dictionary<string, object> ActivityData { get; set; } = new();
}

public class DocumentRequirementDto
{
    public string DocumentType { get; set; } = string.Empty;
    public string RequirementName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public bool IsRequired { get; set; }
    public bool IsMissing { get; set; }
    public DateTime? DueDate { get; set; }
    public string Priority { get; set; } = string.Empty; // High, Medium, Low
    public List<string> AcceptedFormats { get; set; } = new();
    public long? MaxFileSize { get; set; }
    public string? UploadInstructions { get; set; }
    public List<string> ValidationRules { get; set; } = new();
}

public class DocumentVerificationDto
{
    public Guid VerificationId { get; set; }
    public string VerificationStatus { get; set; } = string.Empty; // Pending, Verified, Rejected
    public DateTime? VerificationDate { get; set; }
    public string? VerifiedBy { get; set; }
    public string? VerificationMethod { get; set; } // Manual, Automated, Hybrid
    public List<VerificationCheckDto> Checks { get; set; } = new();
    public string? VerificationNotes { get; set; }
    public double? VerificationScore { get; set; }
}

public class VerificationCheckDto
{
    public string CheckName { get; set; } = string.Empty;
    public string CheckType { get; set; } = string.Empty;
    public string Result { get; set; } = string.Empty; // Pass, Fail, Warning
    public string? Details { get; set; }
    public double? Score { get; set; }
}

public class DocumentIssueDto
{
    public Guid IssueId { get; set; }
    public string IssueType { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty; // Critical, High, Medium, Low
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty; // Open, InProgress, Resolved
    public DateTime ReportedAt { get; set; }
    public DateTime? ResolvedAt { get; set; }
    public string? Resolution { get; set; }
    public List<string> SuggestedActions { get; set; } = new();
}
