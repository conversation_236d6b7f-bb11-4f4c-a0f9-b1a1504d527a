using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SubscriptionManagement.Application.Commands.CreateSubscription;
using SubscriptionManagement.Application.Commands.CancelSubscription;
using SubscriptionManagement.Application.Commands.ExtendSubscription;
using SubscriptionManagement.Application.Commands.UploadPaymentProof;
using SubscriptionManagement.Application.Queries.GetSubscription;
using SubscriptionManagement.Application.Queries.GetMySubscription;
using SubscriptionManagement.Application.DTOs;
using System.Security.Claims;

namespace SubscriptionManagement.API.Controllers
{
    [Authorize]
    public class SubscriptionsController : BaseController
    {
        /// <summary>
        /// Create a new subscription
        /// </summary>
        [HttpPost]
        [ProducesResponseType(typeof(Guid), StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> CreateSubscription([FromBody] CreateSubscriptionDto request)
        {
            try
            {
                var command = new CreateSubscriptionCommand
                {
                    UserId = GetCurrentUserId(),
                    PlanId = request.PlanId,
                    StartDate = request.StartDate,
                    TrialEndDate = request.TrialEndDate,
                    AutoRenew = request.AutoRenew,
                    ProrationMode = request.ProrationMode
                };

                var subscriptionId = await Mediator.Send(command);
                return CreatedAtAction(nameof(GetSubscription), new { id = subscriptionId }, subscriptionId);
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get subscription by ID
        /// </summary>
        [HttpGet("{id}")]
        [ProducesResponseType(typeof(SubscriptionDto), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> GetSubscription(Guid id)
        {
            try
            {
                var userId = GetCurrentUserId();
                var query = new GetSubscriptionQuery(id, userId);
                var subscription = await Mediator.Send(query);

                if (subscription == null)
                {
                    return NotFound($"Subscription with ID {id} not found");
                }

                return Ok(subscription);
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get current user's subscription
        /// </summary>
        [HttpGet("my-subscription")]
        [ProducesResponseType(typeof(SubscriptionDto), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> GetMySubscription()
        {
            try
            {
                var userId = GetCurrentUserId();
                var query = new GetMySubscriptionQuery(userId);
                var subscription = await Mediator.Send(query);

                if (subscription == null)
                {
                    return NotFound("No active subscription found");
                }

                return Ok(subscription);
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Cancel subscription
        /// </summary>
        [HttpPost("{id}/cancel")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> CancelSubscription(Guid id, [FromBody] CancelSubscriptionDto request)
        {
            try
            {
                var userId = GetCurrentUserId();
                var command = new CancelSubscriptionCommand(id, userId, request.CancellationReason)
                {
                    ImmediateCancellation = request.ImmediateCancellation,
                    RefundProration = request.RefundProration
                };

                var result = await Mediator.Send(command);

                if (result)
                {
                    return Ok(new { message = "Subscription cancelled successfully" });
                }

                return BadRequest(new { message = "Failed to cancel subscription" });
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Extend subscription (Admin only)
        /// </summary>
        [HttpPost("{id}/extend")]
        [Authorize(Roles = "Admin")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> ExtendSubscription(Guid id, [FromBody] ExtendSubscriptionDto request)
        {
            try
            {
                var command = new ExtendSubscriptionCommand
                {
                    SubscriptionId = id,
                    UserId = Guid.Empty, // Will be set by the handler from the subscription
                    ExtensionDays = request.ExtensionDays,
                    Reason = request.Reason,
                    ApplyAsGracePeriod = request.ApplyAsGracePeriod,
                    ExtendedByUserId = GetCurrentUserId() // Admin who is performing the extension
                };

                var result = await Mediator.Send(command);

                if (result)
                {
                    return Ok(new
                    {
                        message = "Subscription extended successfully",
                        extensionDays = request.ExtensionDays,
                        applyAsGracePeriod = request.ApplyAsGracePeriod,
                        reason = request.Reason
                    });
                }

                return BadRequest(new { message = "Failed to extend subscription" });
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Upgrade subscription plan
        /// </summary>
        [HttpPost("{id}/upgrade")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> UpgradeSubscription(Guid id, [FromBody] ChangePlanDto request)
        {
            try
            {
                // TODO: Implement UpgradeSubscriptionCommand
                return Ok(new { message = "Upgrade subscription endpoint - to be implemented", id, request });
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Downgrade subscription plan
        /// </summary>
        [HttpPost("{id}/downgrade")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> DowngradeSubscription(Guid id, [FromBody] ChangePlanDto request)
        {
            try
            {
                // TODO: Implement DowngradeSubscriptionCommand
                return Ok(new { message = "Downgrade subscription endpoint - to be implemented", id, request });
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get subscription usage and limits
        /// </summary>
        [HttpGet("{id}/usage")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> GetSubscriptionUsage(Guid id)
        {
            try
            {
                // TODO: Implement GetSubscriptionUsageQuery
                return Ok(new { message = "Get subscription usage endpoint - to be implemented", id });
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get all subscriptions (Admin only)
        /// </summary>
        [HttpGet]
        [Authorize(Roles = "Admin")]
        [ProducesResponseType(typeof(List<SubscriptionSummaryDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetAllSubscriptions([FromQuery] int page = 1, [FromQuery] int pageSize = 10)
        {
            try
            {
                // TODO: Implement GetAllSubscriptionsQuery
                return Ok(new { message = "Get all subscriptions endpoint - to be implemented", page, pageSize });
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Upload payment proof for offline payment
        /// </summary>
        [HttpPost("{id}/payment-proof")]
        [ProducesResponseType(typeof(UploadPaymentProofResponse), StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> UploadPaymentProof(Guid id, [FromForm] UploadPaymentProofRequest request)
        {
            try
            {
                var userId = GetCurrentUserId();
                var command = new UploadPaymentProofCommand
                {
                    SubscriptionId = id,
                    UserId = userId,
                    Amount = request.Amount,
                    Currency = request.Currency,
                    PaymentDate = request.PaymentDate,
                    ProofImage = request.ProofImage,
                    Notes = request.Notes,
                    PaymentMethod = request.PaymentMethod,
                    TransactionReference = request.TransactionReference
                };

                var result = await Mediator.Send(command);
                return CreatedAtAction(nameof(GetSubscription), new { id }, result);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (UnauthorizedAccessException ex)
            {
                return Forbid(ex.Message);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }
    }
}
