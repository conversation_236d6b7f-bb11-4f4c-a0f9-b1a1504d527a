using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Enums;
using System.Diagnostics;
using System.Diagnostics.Metrics;

namespace SubscriptionManagement.Infrastructure.Services
{
    public interface ISubscriptionMetricsService
    {
        // Subscription metrics
        void RecordSubscriptionCreated(string planName, decimal amount, string currency);
        void RecordSubscriptionUpgraded(string fromPlan, string toPlan, decimal amount);
        void RecordSubscriptionDowngraded(string fromPlan, string toPlan, decimal amount);
        void RecordSubscriptionCancelled(string planName, string reason);
        void RecordSubscriptionPaused(string planName);
        void RecordSubscriptionResumed(string planName);

        // Payment metrics
        void RecordPaymentSuccess(decimal amount, string currency, string paymentMethod);
        void RecordPaymentFailure(decimal amount, string currency, string paymentMethod, string errorCode);
        void RecordRefundProcessed(decimal amount, string currency, string reason);

        // Usage metrics
        void RecordFeatureUsage(string featureType, int count, Guid userId);
        void RecordUsageLimitReached(string featureType, Guid userId);
        void RecordUsageAlert(string featureType, Guid userId, double usagePercentage);

        // API performance metrics
        void RecordApiCall(string endpoint, TimeSpan duration, bool success);
        void RecordCacheHit(string cacheType);
        void RecordCacheMiss(string cacheType);

        // Business metrics
        void RecordMonthlyRecurringRevenue(decimal amount, string currency);
        void RecordChurnRate(double rate);
        void RecordCustomerLifetimeValue(decimal value, string currency);

        // Feature flag metrics
        void RecordFeatureFlagEvaluation(string flagKey, string variant, Guid userId);
        void RecordFeatureFlagToggle(string flagKey, bool enabled);

        // Health metrics
        void RecordHealthCheck(string component, bool healthy, TimeSpan responseTime);
    }

    public class SubscriptionMetricsService : ISubscriptionMetricsService
    {
        private readonly ILogger<SubscriptionMetricsService> _logger;
        private readonly Meter _meter;

        // Counters
        private readonly Counter<long> _subscriptionCreatedCounter;
        private readonly Counter<long> _subscriptionUpgradedCounter;
        private readonly Counter<long> _subscriptionDowngradedCounter;
        private readonly Counter<long> _subscriptionCancelledCounter;
        private readonly Counter<long> _subscriptionPausedCounter;
        private readonly Counter<long> _subscriptionResumedCounter;

        private readonly Counter<long> _paymentSuccessCounter;
        private readonly Counter<long> _paymentFailureCounter;
        private readonly Counter<long> _refundCounter;

        private readonly Counter<long> _featureUsageCounter;
        private readonly Counter<long> _usageLimitReachedCounter;
        private readonly Counter<long> _usageAlertCounter;

        private readonly Counter<long> _apiCallCounter;
        private readonly Counter<long> _cacheHitCounter;
        private readonly Counter<long> _cacheMissCounter;

        private readonly Counter<long> _featureFlagEvaluationCounter;
        private readonly Counter<long> _featureFlagToggleCounter;

        private readonly Counter<long> _healthCheckCounter;

        // Histograms
        private readonly Histogram<double> _apiDurationHistogram;
        private readonly Histogram<double> _paymentAmountHistogram;
        private readonly Histogram<double> _subscriptionAmountHistogram;

        // Gauges (using ObservableGauge)
        private readonly ObservableGauge<double> _monthlyRecurringRevenueGauge;
        private readonly ObservableGauge<double> _churnRateGauge;
        private readonly ObservableGauge<double> _customerLifetimeValueGauge;

        private double _currentMrr = 0;
        private double _currentChurnRate = 0;
        private double _currentClv = 0;

        public SubscriptionMetricsService(ILogger<SubscriptionMetricsService> logger)
        {
            _logger = logger;
            _meter = new Meter("SubscriptionManagement", "1.0.0");

            // Initialize counters
            _subscriptionCreatedCounter = _meter.CreateCounter<long>("subscription_created_total", "count", "Total number of subscriptions created");
            _subscriptionUpgradedCounter = _meter.CreateCounter<long>("subscription_upgraded_total", "count", "Total number of subscription upgrades");
            _subscriptionDowngradedCounter = _meter.CreateCounter<long>("subscription_downgraded_total", "count", "Total number of subscription downgrades");
            _subscriptionCancelledCounter = _meter.CreateCounter<long>("subscription_cancelled_total", "count", "Total number of subscription cancellations");
            _subscriptionPausedCounter = _meter.CreateCounter<long>("subscription_paused_total", "count", "Total number of subscription pauses");
            _subscriptionResumedCounter = _meter.CreateCounter<long>("subscription_resumed_total", "count", "Total number of subscription resumes");

            _paymentSuccessCounter = _meter.CreateCounter<long>("payment_success_total", "count", "Total number of successful payments");
            _paymentFailureCounter = _meter.CreateCounter<long>("payment_failure_total", "count", "Total number of failed payments");
            _refundCounter = _meter.CreateCounter<long>("refund_processed_total", "count", "Total number of refunds processed");

            _featureUsageCounter = _meter.CreateCounter<long>("feature_usage_total", "count", "Total feature usage events");
            _usageLimitReachedCounter = _meter.CreateCounter<long>("usage_limit_reached_total", "count", "Total usage limit reached events");
            _usageAlertCounter = _meter.CreateCounter<long>("usage_alert_total", "count", "Total usage alerts sent");

            _apiCallCounter = _meter.CreateCounter<long>("api_calls_total", "count", "Total API calls");
            _cacheHitCounter = _meter.CreateCounter<long>("cache_hits_total", "count", "Total cache hits");
            _cacheMissCounter = _meter.CreateCounter<long>("cache_misses_total", "count", "Total cache misses");

            _featureFlagEvaluationCounter = _meter.CreateCounter<long>("feature_flag_evaluations_total", "count", "Total feature flag evaluations");
            _featureFlagToggleCounter = _meter.CreateCounter<long>("feature_flag_toggles_total", "count", "Total feature flag toggles");

            _healthCheckCounter = _meter.CreateCounter<long>("health_checks_total", "count", "Total health checks");

            // Initialize histograms
            _apiDurationHistogram = _meter.CreateHistogram<double>("api_duration_seconds", "seconds", "API call duration");
            _paymentAmountHistogram = _meter.CreateHistogram<double>("payment_amount", "currency", "Payment amounts");
            _subscriptionAmountHistogram = _meter.CreateHistogram<double>("subscription_amount", "currency", "Subscription amounts");

            // Initialize gauges
            _monthlyRecurringRevenueGauge = _meter.CreateObservableGauge<double>("monthly_recurring_revenue", "currency", "Monthly recurring revenue", () => _currentMrr);
            _churnRateGauge = _meter.CreateObservableGauge<double>("churn_rate", "percentage", "Customer churn rate", () => _currentChurnRate);
            _customerLifetimeValueGauge = _meter.CreateObservableGauge<double>("customer_lifetime_value", "currency", "Customer lifetime value", () => _currentClv);
        }

        // Subscription metrics
        public void RecordSubscriptionCreated(string planName, decimal amount, string currency)
        {
            _subscriptionCreatedCounter.Add(1, new KeyValuePair<string, object?>("plan", planName), new KeyValuePair<string, object?>("currency", currency));
            _subscriptionAmountHistogram.Record((double)amount, new KeyValuePair<string, object?>("plan", planName), new KeyValuePair<string, object?>("currency", currency));
            _logger.LogInformation("Recorded subscription created: {PlanName}, {Amount} {Currency}", planName, amount, currency);
        }

        public void RecordSubscriptionUpgraded(string fromPlan, string toPlan, decimal amount)
        {
            _subscriptionUpgradedCounter.Add(1, new KeyValuePair<string, object?>("from_plan", fromPlan), new KeyValuePair<string, object?>("to_plan", toPlan));
            _logger.LogInformation("Recorded subscription upgrade: {FromPlan} -> {ToPlan}, {Amount}", fromPlan, toPlan, amount);
        }

        public void RecordSubscriptionDowngraded(string fromPlan, string toPlan, decimal amount)
        {
            _subscriptionDowngradedCounter.Add(1, new KeyValuePair<string, object?>("from_plan", fromPlan), new KeyValuePair<string, object?>("to_plan", toPlan));
            _logger.LogInformation("Recorded subscription downgrade: {FromPlan} -> {ToPlan}, {Amount}", fromPlan, toPlan, amount);
        }

        public void RecordSubscriptionCancelled(string planName, string reason)
        {
            _subscriptionCancelledCounter.Add(1, new KeyValuePair<string, object?>("plan", planName), new KeyValuePair<string, object?>("reason", reason));
            _logger.LogInformation("Recorded subscription cancellation: {PlanName}, reason: {Reason}", planName, reason);
        }

        public void RecordSubscriptionPaused(string planName)
        {
            _subscriptionPausedCounter.Add(1, new KeyValuePair<string, object?>("plan", planName));
            _logger.LogInformation("Recorded subscription pause: {PlanName}", planName);
        }

        public void RecordSubscriptionResumed(string planName)
        {
            _subscriptionResumedCounter.Add(1, new KeyValuePair<string, object?>("plan", planName));
            _logger.LogInformation("Recorded subscription resume: {PlanName}", planName);
        }

        // Payment metrics
        public void RecordPaymentSuccess(decimal amount, string currency, string paymentMethod)
        {
            _paymentSuccessCounter.Add(1, new KeyValuePair<string, object?>("currency", currency), new KeyValuePair<string, object?>("payment_method", paymentMethod));
            _paymentAmountHistogram.Record((double)amount, new KeyValuePair<string, object?>("currency", currency), new KeyValuePair<string, object?>("payment_method", paymentMethod));
            _logger.LogInformation("Recorded payment success: {Amount} {Currency} via {PaymentMethod}", amount, currency, paymentMethod);
        }

        public void RecordPaymentFailure(decimal amount, string currency, string paymentMethod, string errorCode)
        {
            _paymentFailureCounter.Add(1, new KeyValuePair<string, object?>("currency", currency), new KeyValuePair<string, object?>("payment_method", paymentMethod), new KeyValuePair<string, object?>("error_code", errorCode));
            _logger.LogWarning("Recorded payment failure: {Amount} {Currency} via {PaymentMethod}, error: {ErrorCode}", amount, currency, paymentMethod, errorCode);
        }

        public void RecordRefundProcessed(decimal amount, string currency, string reason)
        {
            _refundCounter.Add(1, new KeyValuePair<string, object?>("currency", currency), new KeyValuePair<string, object?>("reason", reason));
            _logger.LogInformation("Recorded refund: {Amount} {Currency}, reason: {Reason}", amount, currency, reason);
        }

        // Usage metrics
        public void RecordFeatureUsage(string featureType, int count, Guid userId)
        {
            _featureUsageCounter.Add(count, new KeyValuePair<string, object?>("feature_type", featureType));
            _logger.LogDebug("Recorded feature usage: {FeatureType}, count: {Count}, user: {UserId}", featureType, count, userId);
        }

        public void RecordUsageLimitReached(string featureType, Guid userId)
        {
            _usageLimitReachedCounter.Add(1, new KeyValuePair<string, object?>("feature_type", featureType));
            _logger.LogWarning("Recorded usage limit reached: {FeatureType}, user: {UserId}", featureType, userId);
        }

        public void RecordUsageAlert(string featureType, Guid userId, double usagePercentage)
        {
            _usageAlertCounter.Add(1, new KeyValuePair<string, object?>("feature_type", featureType));
            _logger.LogInformation("Recorded usage alert: {FeatureType}, user: {UserId}, usage: {UsagePercentage}%", featureType, userId, usagePercentage * 100);
        }

        // API performance metrics
        public void RecordApiCall(string endpoint, TimeSpan duration, bool success)
        {
            _apiCallCounter.Add(1, new KeyValuePair<string, object?>("endpoint", endpoint), new KeyValuePair<string, object?>("success", success));
            _apiDurationHistogram.Record(duration.TotalSeconds, new KeyValuePair<string, object?>("endpoint", endpoint), new KeyValuePair<string, object?>("success", success));
            _logger.LogDebug("Recorded API call: {Endpoint}, duration: {Duration}ms, success: {Success}", endpoint, duration.TotalMilliseconds, success);
        }

        public void RecordCacheHit(string cacheType)
        {
            _cacheHitCounter.Add(1, new KeyValuePair<string, object?>("cache_type", cacheType));
            _logger.LogDebug("Recorded cache hit: {CacheType}", cacheType);
        }

        public void RecordCacheMiss(string cacheType)
        {
            _cacheMissCounter.Add(1, new KeyValuePair<string, object?>("cache_type", cacheType));
            _logger.LogDebug("Recorded cache miss: {CacheType}", cacheType);
        }

        // Business metrics
        public void RecordMonthlyRecurringRevenue(decimal amount, string currency)
        {
            _currentMrr = (double)amount;
            _logger.LogInformation("Updated MRR: {Amount} {Currency}", amount, currency);
        }

        public void RecordChurnRate(double rate)
        {
            _currentChurnRate = rate;
            _logger.LogInformation("Updated churn rate: {Rate}%", rate * 100);
        }

        public void RecordCustomerLifetimeValue(decimal value, string currency)
        {
            _currentClv = (double)value;
            _logger.LogInformation("Updated CLV: {Value} {Currency}", value, currency);
        }

        // Feature flag metrics
        public void RecordFeatureFlagEvaluation(string flagKey, string variant, Guid userId)
        {
            _featureFlagEvaluationCounter.Add(1, new KeyValuePair<string, object?>("flag_key", flagKey), new KeyValuePair<string, object?>("variant", variant));
            _logger.LogDebug("Recorded feature flag evaluation: {FlagKey}, variant: {Variant}, user: {UserId}", flagKey, variant, userId);
        }

        public void RecordFeatureFlagToggle(string flagKey, bool enabled)
        {
            _featureFlagToggleCounter.Add(1, new KeyValuePair<string, object?>("flag_key", flagKey), new KeyValuePair<string, object?>("enabled", enabled));
            _logger.LogInformation("Recorded feature flag toggle: {FlagKey}, enabled: {Enabled}", flagKey, enabled);
        }

        // Health metrics
        public void RecordHealthCheck(string component, bool healthy, TimeSpan responseTime)
        {
            _healthCheckCounter.Add(1, new KeyValuePair<string, object?>("component", component), new KeyValuePair<string, object?>("healthy", healthy));
            _logger.LogDebug("Recorded health check: {Component}, healthy: {Healthy}, response time: {ResponseTime}ms", component, healthy, responseTime.TotalMilliseconds);
        }

        public void Dispose()
        {
            _meter?.Dispose();
        }
    }
}
