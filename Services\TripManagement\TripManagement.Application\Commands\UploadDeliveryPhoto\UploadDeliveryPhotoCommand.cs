using MediatR;
using Microsoft.AspNetCore.Http;

namespace TripManagement.Application.Commands.UploadDeliveryPhoto;

public record UploadDeliveryPhotoCommand : IRequest<string>
{
    public Guid TripStopId { get; init; }
    public Guid DriverId { get; init; }
    public IFormFile Photo { get; init; } = null!;
    public string? Description { get; init; }
    public DateTime? TakenAt { get; init; }
}
