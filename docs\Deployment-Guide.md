# TLI Microservices Deployment Guide

## Overview

This guide covers deployment strategies for the TLI Microservices Platform across different environments and cloud providers.

## Deployment Options

### 1. Local Development
### 2. Docker Compose
### 3. Kubernetes
### 4. Azure Container Apps
### 5. AWS ECS/EKS
### 6. Google Cloud Run/GKE

## Prerequisites

### General Requirements

- .NET 8 Runtime
- PostgreSQL 15+
- Redis 7+
- RabbitMQ 3.12+
- SSL Certificates (for production)

### Container Requirements

- Docker 24.0+
- Docker Compose 2.0+
- Kubernetes 1.28+ (for K8s deployments)

## 1. Local Development Deployment

### Quick Start

```bash
# Clone repository
git clone https://github.com/your-org/tli-microservices.git
cd tli-microservices

# Start infrastructure
docker-compose -f docker-compose.infrastructure.yml up -d

# Initialize databases
./scripts/initialize-databases.ps1

# Start all services
./scripts/start-all-services.ps1
```

### Manual Service Start

```bash
# Start each service individually
cd Services/UserManagement/UserManagement.API
dotnet run --environment Development

# Or use Visual Studio/VS Code
```

## 2. Docker Compose Deployment

### Production Docker Compose

Create `docker-compose.production.yml`:

```yaml
version: '3.8'

services:
  # API Gateway
  api-gateway:
    build:
      context: .
      dockerfile: ApiGateway/Dockerfile
    ports:
      - "80:80"
      - "443:443"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=https://+:443;http://+:80
    volumes:
      - ./certs:/app/certs
    depends_on:
      - identity-service
      - user-management-service

  # Identity Service
  identity-service:
    build:
      context: .
      dockerfile: Identity/Dockerfile
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ConnectionStrings__DefaultConnection=${DB_CONNECTION_STRING}
      - Authentication__Jwt__SecretKey=${JWT_SECRET_KEY}
    depends_on:
      - postgres
      - redis

  # User Management Service
  user-management-service:
    build:
      context: .
      dockerfile: Services/UserManagement/Dockerfile
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ConnectionStrings__DefaultConnection=${DB_CONNECTION_STRING}
    depends_on:
      - postgres
      - redis

  # Infrastructure Services
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: ${DB_NAME}
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    command: redis-server --requirepass ${REDIS_PASSWORD}
    ports:
      - "6379:6379"

  rabbitmq:
    image: rabbitmq:3-management-alpine
    environment:
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_USER}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_PASSWORD}
    ports:
      - "5672:5672"
      - "15672:15672"

volumes:
  postgres_data:

networks:
  tli-network:
    driver: bridge
```

### Environment Configuration

Create `.env` file:

```bash
# Database
DB_CONNECTION_STRING=Host=postgres;Port=5432;Database=TLI_Production;Username=tli_user;Password=secure_password
DB_NAME=TLI_Production
DB_USER=tli_user
DB_PASSWORD=secure_password

# Authentication
JWT_SECRET_KEY=your-super-secure-jwt-secret-key-here

# Redis
REDIS_PASSWORD=secure_redis_password

# RabbitMQ
RABBITMQ_USER=tli_user
RABBITMQ_PASSWORD=secure_rabbitmq_password

# External Services
SENDGRID_API_KEY=your-sendgrid-api-key
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
```

### Deploy with Docker Compose

```bash
# Build and start all services
docker-compose -f docker-compose.production.yml up -d

# Check service status
docker-compose -f docker-compose.production.yml ps

# View logs
docker-compose -f docker-compose.production.yml logs -f

# Scale services
docker-compose -f docker-compose.production.yml up -d --scale user-management-service=3
```

## 3. Kubernetes Deployment

### Namespace Setup

```yaml
# k8s/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: tli-microservices
```

### ConfigMap for Shared Configuration

```yaml
# k8s/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: tli-config
  namespace: tli-microservices
data:
  ASPNETCORE_ENVIRONMENT: "Production"
  Logging__LogLevel__Default: "Information"
  HealthChecks__Enabled: "true"
```

### Secrets

```yaml
# k8s/secrets.yaml
apiVersion: v1
kind: Secret
metadata:
  name: tli-secrets
  namespace: tli-microservices
type: Opaque
data:
  db-connection-string: <base64-encoded-connection-string>
  jwt-secret-key: <base64-encoded-jwt-secret>
  sendgrid-api-key: <base64-encoded-sendgrid-key>
```

### Service Deployment Example

```yaml
# k8s/user-management-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-management-service
  namespace: tli-microservices
spec:
  replicas: 3
  selector:
    matchLabels:
      app: user-management-service
  template:
    metadata:
      labels:
        app: user-management-service
    spec:
      containers:
      - name: user-management
        image: tli/user-management-service:latest
        ports:
        - containerPort: 80
        env:
        - name: ASPNETCORE_ENVIRONMENT
          valueFrom:
            configMapKeyRef:
              name: tli-config
              key: ASPNETCORE_ENVIRONMENT
        - name: ConnectionStrings__DefaultConnection
          valueFrom:
            secretKeyRef:
              name: tli-secrets
              key: db-connection-string
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health/live
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: user-management-service
  namespace: tli-microservices
spec:
  selector:
    app: user-management-service
  ports:
  - port: 80
    targetPort: 80
  type: ClusterIP
```

### Ingress Configuration

```yaml
# k8s/ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: tli-ingress
  namespace: tli-microservices
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  tls:
  - hosts:
    - api.tli.com
    secretName: tli-tls
  rules:
  - host: api.tli.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: api-gateway-service
            port:
              number: 80
```

### Deploy to Kubernetes

```bash
# Apply all configurations
kubectl apply -f k8s/

# Check deployment status
kubectl get pods -n tli-microservices

# Check services
kubectl get services -n tli-microservices

# View logs
kubectl logs -f deployment/user-management-service -n tli-microservices

# Scale deployment
kubectl scale deployment user-management-service --replicas=5 -n tli-microservices
```

## 4. Azure Container Apps Deployment

### Azure CLI Setup

```bash
# Login to Azure
az login

# Create resource group
az group create --name tli-microservices --location eastus

# Create Container Apps environment
az containerapp env create \
  --name tli-environment \
  --resource-group tli-microservices \
  --location eastus
```

### Deploy Services

```bash
# Deploy User Management Service
az containerapp create \
  --name user-management-service \
  --resource-group tli-microservices \
  --environment tli-environment \
  --image tli/user-management-service:latest \
  --target-port 80 \
  --ingress external \
  --min-replicas 1 \
  --max-replicas 10 \
  --cpu 0.5 \
  --memory 1Gi \
  --env-vars ASPNETCORE_ENVIRONMENT=Production \
  --secrets db-connection-string="Host=..." jwt-secret-key="..."
```

### Azure Container Apps YAML

```yaml
# azure-container-apps.yaml
apiVersion: 2022-03-01
location: eastus
resourceGroup: tli-microservices
properties:
  managedEnvironmentId: /subscriptions/.../tli-environment
  configuration:
    ingress:
      external: true
      targetPort: 80
    secrets:
    - name: db-connection-string
      value: "Host=..."
    - name: jwt-secret-key
      value: "..."
  template:
    containers:
    - name: user-management
      image: tli/user-management-service:latest
      resources:
        cpu: 0.5
        memory: 1Gi
      env:
      - name: ASPNETCORE_ENVIRONMENT
        value: Production
      - name: ConnectionStrings__DefaultConnection
        secretRef: db-connection-string
    scale:
      minReplicas: 1
      maxReplicas: 10
```

## 5. AWS ECS Deployment

### ECS Task Definition

```json
{
  "family": "user-management-service",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "512",
  "memory": "1024",
  "executionRoleArn": "arn:aws:iam::account:role/ecsTaskExecutionRole",
  "containerDefinitions": [
    {
      "name": "user-management",
      "image": "tli/user-management-service:latest",
      "portMappings": [
        {
          "containerPort": 80,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "ASPNETCORE_ENVIRONMENT",
          "value": "Production"
        }
      ],
      "secrets": [
        {
          "name": "ConnectionStrings__DefaultConnection",
          "valueFrom": "arn:aws:secretsmanager:region:account:secret:db-connection"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/user-management-service",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "ecs"
        }
      }
    }
  ]
}
```

## 6. Monitoring and Observability

### Prometheus Configuration

```yaml
# monitoring/prometheus-config.yaml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'tli-services'
    kubernetes_sd_configs:
    - role: pod
    relabel_configs:
    - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
      action: keep
      regex: true
    - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
      action: replace
      target_label: __metrics_path__
      regex: (.+)
```

### Grafana Dashboards

Import pre-configured dashboards from `monitoring/grafana/dashboards/`

## 7. CI/CD Pipeline

### GitHub Actions Workflow

```yaml
# .github/workflows/deploy.yml
name: Deploy TLI Microservices

on:
  push:
    branches: [main]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup .NET
      uses: actions/setup-dotnet@v3
      with:
        dotnet-version: '8.0.x'
    
    - name: Build and Test
      run: |
        dotnet build
        dotnet test
    
    - name: Build Docker Images
      run: |
        docker build -t tli/user-management-service:${{ github.sha }} -f Services/UserManagement/Dockerfile .
        docker build -t tli/api-gateway:${{ github.sha }} -f ApiGateway/Dockerfile .
    
    - name: Deploy to Production
      run: |
        # Deploy to your chosen platform
        kubectl set image deployment/user-management-service user-management=tli/user-management-service:${{ github.sha }}
```

## 8. Security Considerations

### SSL/TLS Configuration

- Use valid SSL certificates
- Enable HTTPS redirection
- Configure HSTS headers
- Implement certificate rotation

### Network Security

- Use private networks/VPCs
- Configure firewalls
- Implement network policies
- Use service mesh for inter-service communication

### Secrets Management

- Use cloud-native secret stores
- Rotate secrets regularly
- Never commit secrets to code
- Use environment-specific configurations

## 9. Backup and Disaster Recovery

### Database Backups

```bash
# Automated PostgreSQL backup
pg_dump -h $DB_HOST -U $DB_USER -d $DB_NAME > backup_$(date +%Y%m%d_%H%M%S).sql
```

### Application Data Backup

- Configure automated backups
- Test restore procedures
- Implement cross-region replication
- Document recovery procedures

## 10. Troubleshooting

### Common Issues

1. **Service Discovery Issues**: Check network connectivity
2. **Database Connection Failures**: Verify connection strings
3. **Authentication Problems**: Check JWT configuration
4. **Performance Issues**: Review resource limits

### Debugging Commands

```bash
# Check service logs
kubectl logs -f deployment/user-management-service

# Check service health
curl https://api.tli.com/health

# Check resource usage
kubectl top pods

# Debug networking
kubectl exec -it pod-name -- nslookup service-name
```

## Support

For deployment support:
- **Documentation**: Check deployment guides
- **Issues**: Create GitHub issue with deployment tag
- **Email**: <EMAIL>
