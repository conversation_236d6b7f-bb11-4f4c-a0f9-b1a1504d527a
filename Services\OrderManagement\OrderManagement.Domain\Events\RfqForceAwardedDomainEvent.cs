using Shared.Domain.Common;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Domain.Events;

public class RfqForceAwardedDomainEvent : DomainEvent
{
    public Guid RfqId { get; }
    public Guid AwardedBidId { get; }
    public Guid AwardedBidderId { get; }
    public string AwardedBidderName { get; }
    public Guid AdminUserId { get; }
    public string ForceAwardReason { get; }
    public RfqStatus PreviousStatus { get; }
    public Guid? PreviousAwardedBidId { get; }

    public RfqForceAwardedDomainEvent(
        Guid rfqId,
        Guid awardedBidId,
        Guid awardedBidderId,
        string awardedBidderName,
        Guid adminUserId,
        string forceAwardReason,
        RfqStatus previousStatus,
        Guid? previousAwardedBidId)
    {
        RfqId = rfqId;
        AwardedBidId = awardedBidId;
        AwardedBidderId = awardedBidderId;
        AwardedBidderName = awardedBidderName;
        AdminUserId = adminUserId;
        ForceAwardReason = forceAwardReason;
        PreviousStatus = previousStatus;
        PreviousAwardedBidId = previousAwardedBidId;
    }
}
