using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Application.Services;

public interface IOrderMetricsService
{
    Task RecordOrderCreatedAsync(Guid orderId, string orderNumber, bool isUrgent);
    Task RecordOrderStatusChangeAsync(Guid orderId, OrderStatus fromStatus, OrderStatus toStatus, TimeSpan duration);
    Task RecordInvoiceCreatedAsync(Guid invoiceId, Guid orderId, decimal amount, string currency);
    Task RecordInvoicePaidAsync(Guid invoiceId, decimal amount, TimeSpan timeToPay);
    Task RecordPaymentProcessedAsync(Guid orderId, string paymentStatus, decimal amount);
    Task<OrderMetrics> GetOrderMetricsAsync(DateTime fromDate, DateTime toDate);
}

public class OrderMetricsService : IOrderMetricsService
{
    private readonly ILogger<OrderMetricsService> _logger;
    private readonly IOrderRepository _orderRepository;
    private readonly IInvoiceRepository _invoiceRepository;

    public OrderMetricsService(
        ILogger<OrderMetricsService> logger,
        IOrderRepository orderRepository,
        IInvoiceRepository invoiceRepository)
    {
        _logger = logger;
        _orderRepository = orderRepository;
        _invoiceRepository = invoiceRepository;
    }

    public async Task RecordOrderCreatedAsync(Guid orderId, string orderNumber, bool isUrgent)
    {
        _logger.LogInformation("Order Created - OrderId: {OrderId}, OrderNumber: {OrderNumber}, IsUrgent: {IsUrgent}, Timestamp: {Timestamp}",
            orderId, orderNumber, isUrgent, DateTime.UtcNow);

        // In a real implementation, you might send this to a metrics collection service
        // like Prometheus, Application Insights, or CloudWatch
        await Task.CompletedTask;
    }

    public async Task RecordOrderStatusChangeAsync(Guid orderId, OrderStatus fromStatus, OrderStatus toStatus, TimeSpan duration)
    {
        _logger.LogInformation("Order Status Change - OrderId: {OrderId}, From: {FromStatus}, To: {ToStatus}, Duration: {Duration}ms, Timestamp: {Timestamp}",
            orderId, fromStatus, toStatus, duration.TotalMilliseconds, DateTime.UtcNow);

        // Record specific metrics for important transitions
        switch (toStatus)
        {
            case OrderStatus.Confirmed:
                _logger.LogInformation("Order Confirmed - OrderId: {OrderId}, TimeToConfirm: {Duration}ms", 
                    orderId, duration.TotalMilliseconds);
                break;

            case OrderStatus.InProgress:
                _logger.LogInformation("Order Started - OrderId: {OrderId}, TimeToStart: {Duration}ms", 
                    orderId, duration.TotalMilliseconds);
                break;

            case OrderStatus.Completed:
                _logger.LogInformation("Order Completed - OrderId: {OrderId}, TotalDuration: {Duration}ms", 
                    orderId, duration.TotalMilliseconds);
                break;

            case OrderStatus.Cancelled:
                _logger.LogWarning("Order Cancelled - OrderId: {OrderId}, CancelledAfter: {Duration}ms", 
                    orderId, duration.TotalMilliseconds);
                break;
        }

        await Task.CompletedTask;
    }

    public async Task RecordInvoiceCreatedAsync(Guid invoiceId, Guid orderId, decimal amount, string currency)
    {
        _logger.LogInformation("Invoice Created - InvoiceId: {InvoiceId}, OrderId: {OrderId}, Amount: {Amount} {Currency}, Timestamp: {Timestamp}",
            invoiceId, orderId, amount, currency, DateTime.UtcNow);

        await Task.CompletedTask;
    }

    public async Task RecordInvoicePaidAsync(Guid invoiceId, decimal amount, TimeSpan timeToPay)
    {
        _logger.LogInformation("Invoice Paid - InvoiceId: {InvoiceId}, Amount: {Amount}, TimeToPay: {TimeToPay}ms, Timestamp: {Timestamp}",
            invoiceId, amount, timeToPay.TotalMilliseconds, DateTime.UtcNow);

        await Task.CompletedTask;
    }

    public async Task RecordPaymentProcessedAsync(Guid orderId, string paymentStatus, decimal amount)
    {
        _logger.LogInformation("Payment Processed - OrderId: {OrderId}, Status: {PaymentStatus}, Amount: {Amount}, Timestamp: {Timestamp}",
            orderId, paymentStatus, amount, DateTime.UtcNow);

        if (paymentStatus.ToLower() == "failed")
        {
            _logger.LogWarning("Payment Failed - OrderId: {OrderId}, Amount: {Amount}", orderId, amount);
        }

        await Task.CompletedTask;
    }

    public async Task<OrderMetrics> GetOrderMetricsAsync(DateTime fromDate, DateTime toDate)
    {
        _logger.LogInformation("Calculating order metrics from {FromDate} to {ToDate}", fromDate, toDate);

        try
        {
            // Get orders in the date range
            var (orders, totalCount) = await _orderRepository.GetPagedAsync(1, int.MaxValue, cancellationToken: default);
            var filteredOrders = orders.Where(o => o.CreatedAt >= fromDate && o.CreatedAt <= toDate).ToList();

            // Get invoices in the date range
            var (invoices, _) = await _invoiceRepository.GetPagedAsync(1, int.MaxValue, cancellationToken: default);
            var filteredInvoices = invoices.Where(i => i.CreatedAt >= fromDate && i.CreatedAt <= toDate).ToList();

            var metrics = new OrderMetrics
            {
                FromDate = fromDate,
                ToDate = toDate,
                TotalOrders = filteredOrders.Count,
                OrdersByStatus = filteredOrders.GroupBy(o => o.Status)
                    .ToDictionary(g => g.Key.ToString(), g => g.Count()),
                UrgentOrders = filteredOrders.Count(o => o.IsUrgent),
                AverageOrderValue = filteredOrders.Any() ? filteredOrders.Average(o => o.AgreedPrice.Amount) : 0,
                TotalOrderValue = filteredOrders.Sum(o => o.AgreedPrice.Amount),
                TotalInvoices = filteredInvoices.Count,
                InvoicesByStatus = filteredInvoices.GroupBy(i => i.Status)
                    .ToDictionary(g => g.Key.ToString(), g => g.Count()),
                TotalInvoiceValue = filteredInvoices.Sum(i => i.TotalAmount.Amount),
                PaidInvoices = filteredInvoices.Count(i => i.Status == InvoiceStatus.Paid),
                OverdueInvoices = filteredInvoices.Count(i => i.DueDate < DateTime.UtcNow && i.Status != InvoiceStatus.Paid && i.Status != InvoiceStatus.Cancelled),
                AverageTimeToComplete = CalculateAverageTimeToComplete(filteredOrders),
                AverageTimeToPay = CalculateAverageTimeToPay(filteredInvoices)
            };

            _logger.LogInformation("Order metrics calculated - Total Orders: {TotalOrders}, Total Value: {TotalValue}, Completion Rate: {CompletionRate}%",
                metrics.TotalOrders, metrics.TotalOrderValue, 
                metrics.TotalOrders > 0 ? (metrics.OrdersByStatus.GetValueOrDefault("Completed", 0) * 100.0 / metrics.TotalOrders) : 0);

            return metrics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating order metrics");
            throw;
        }
    }

    private static double CalculateAverageTimeToComplete(List<Domain.Entities.Order> orders)
    {
        var completedOrders = orders.Where(o => o.Status == OrderStatus.Completed && o.CompletedAt.HasValue).ToList();
        if (!completedOrders.Any()) return 0;

        var totalHours = completedOrders.Sum(o => (o.CompletedAt!.Value - o.CreatedAt).TotalHours);
        return totalHours / completedOrders.Count;
    }

    private static double CalculateAverageTimeToPay(List<Domain.Entities.Invoice> invoices)
    {
        var paidInvoices = invoices.Where(i => i.Status == InvoiceStatus.Paid && i.PaidDate.HasValue).ToList();
        if (!paidInvoices.Any()) return 0;

        var totalHours = paidInvoices.Sum(i => (i.PaidDate!.Value - i.InvoiceDate).TotalHours);
        return totalHours / paidInvoices.Count;
    }
}

public class OrderMetrics
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public int TotalOrders { get; set; }
    public Dictionary<string, int> OrdersByStatus { get; set; } = new();
    public int UrgentOrders { get; set; }
    public decimal AverageOrderValue { get; set; }
    public decimal TotalOrderValue { get; set; }
    public int TotalInvoices { get; set; }
    public Dictionary<string, int> InvoicesByStatus { get; set; } = new();
    public decimal TotalInvoiceValue { get; set; }
    public int PaidInvoices { get; set; }
    public int OverdueInvoices { get; set; }
    public double AverageTimeToComplete { get; set; } // in hours
    public double AverageTimeToPay { get; set; } // in hours
}
