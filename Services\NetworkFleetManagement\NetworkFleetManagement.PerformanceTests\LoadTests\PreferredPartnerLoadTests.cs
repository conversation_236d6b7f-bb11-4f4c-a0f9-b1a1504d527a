using NBomber.Contracts;
using NBomber.CSharp;
using NBomber.Http.CSharp;
using System.Net.Http.Json;
using System.Text.Json;
using Xunit;

namespace NetworkFleetManagement.PerformanceTests.LoadTests;

public class PreferredPartnerLoadTests
{
    private const string BaseUrl = "http://localhost:5000";
    private readonly List<Guid> _testUserIds;
    private readonly List<Guid> _testPartnerIds;

    public PreferredPartnerLoadTests()
    {
        _testUserIds = Enumerable.Range(1, 100).Select(_ => Guid.NewGuid()).ToList();
        _testPartnerIds = Enumerable.Range(1, 1000).Select(_ => Guid.NewGuid()).ToList();
    }

    [Fact]
    public async Task GetPreferredPartners_LoadTest_HandlesHighConcurrency()
    {
        var scenario = Scenario.Create("get_preferred_partners", async context =>
        {
            var userId = _testUserIds[Random.Shared.Next(_testUserIds.Count)];
            
            var request = Http.CreateRequest("GET", $"{BaseUrl}/api/v1/preferredpartners")
                .WithHeader("X-User-Id", userId.ToString())
                .WithHeader("X-User-Role", "Broker")
                .WithQuery("pageSize", "20")
                .WithQuery("isActive", "true");

            var response = await Http.Send(request, context);
            return response;
        })
        .WithLoadSimulations(
            Simulation.InjectPerSec(rate: 100, during: TimeSpan.FromMinutes(2)),
            Simulation.KeepConstant(copies: 50, during: TimeSpan.FromMinutes(3))
        );

        var stats = NBomberRunner
            .RegisterScenarios(scenario)
            .WithReportFolder("load-test-results")
            .Run();

        // Assert performance requirements
        var scnStats = stats.AllScenarios.First();
        scnStats.Ok.Request.Mean.Should().BeLessThan(TimeSpan.FromMilliseconds(500));
        scnStats.Ok.Request.Count.Should().BeGreaterThan(10000);
        scnStats.Fail.Request.Count.Should().BeLessThan(scnStats.Ok.Request.Count * 0.01); // Less than 1% failure rate
    }

    [Fact]
    public async Task CreatePreferredPartner_LoadTest_HandlesHighThroughput()
    {
        var scenario = Scenario.Create("create_preferred_partner", async context =>
        {
            var userId = _testUserIds[Random.Shared.Next(_testUserIds.Count)];
            var partnerId = _testPartnerIds[Random.Shared.Next(_testPartnerIds.Count)];

            var createDto = new
            {
                PartnerId = partnerId,
                PartnerType = "Carrier",
                PreferenceLevel = "High",
                Priority = Random.Shared.Next(1, 10),
                PreferredCommissionRate = 0.15m,
                AutoAssignEnabled = true,
                Notes = $"Load test partner {context.ScenarioInfo.ThreadId}",
                PreferredRoutes = new[] { "Route1", "Route2" },
                PreferredLoadTypes = new[] { "Dry Van" }
            };

            var request = Http.CreateRequest("POST", $"{BaseUrl}/api/v1/preferredpartners")
                .WithHeader("X-User-Id", userId.ToString())
                .WithHeader("X-User-Role", "Broker")
                .WithHeader("Content-Type", "application/json")
                .WithBody(new StringContent(JsonSerializer.Serialize(createDto)));

            var response = await Http.Send(request, context);
            return response;
        })
        .WithLoadSimulations(
            Simulation.InjectPerSec(rate: 50, during: TimeSpan.FromMinutes(2))
        );

        var stats = NBomberRunner
            .RegisterScenarios(scenario)
            .WithReportFolder("load-test-results")
            .Run();

        var scnStats = stats.AllScenarios.First();
        scnStats.Ok.Request.Mean.Should().BeLessThan(TimeSpan.FromMilliseconds(1000));
        scnStats.Fail.Request.Count.Should().BeLessThan(scnStats.Ok.Request.Count * 0.02); // Less than 2% failure rate
    }

    [Fact]
    public async Task AutoAssignmentQuery_LoadTest_OptimizedPerformance()
    {
        var scenario = Scenario.Create("auto_assignment_query", async context =>
        {
            var userId = _testUserIds[Random.Shared.Next(_testUserIds.Count)];

            var testRequest = new
            {
                PartnerType = "Carrier",
                Route = $"Route{Random.Shared.Next(1, 10)}",
                LoadType = Random.Shared.Next(2) == 0 ? "Dry Van" : "Refrigerated",
                MinRating = 4.0m,
                MaxResults = 10
            };

            var request = Http.CreateRequest("POST", $"{BaseUrl}/api/v1/preferredpartners/test-auto-assignment")
                .WithHeader("X-User-Id", userId.ToString())
                .WithHeader("X-User-Role", "Broker")
                .WithHeader("Content-Type", "application/json")
                .WithBody(new StringContent(JsonSerializer.Serialize(testRequest)));

            var response = await Http.Send(request, context);
            return response;
        })
        .WithLoadSimulations(
            Simulation.InjectPerSec(rate: 200, during: TimeSpan.FromMinutes(2)),
            Simulation.KeepConstant(copies: 100, during: TimeSpan.FromMinutes(1))
        );

        var stats = NBomberRunner
            .RegisterScenarios(scenario)
            .WithReportFolder("load-test-results")
            .Run();

        var scnStats = stats.AllScenarios.First();
        scnStats.Ok.Request.Mean.Should().BeLessThan(TimeSpan.FromMilliseconds(300)); // Critical path - must be fast
        scnStats.Ok.Request.Count.Should().BeGreaterThan(15000);
    }

    [Fact]
    public async Task DashboardQuery_LoadTest_CacheEffectiveness()
    {
        var scenario = Scenario.Create("dashboard_query", async context =>
        {
            var userId = _testUserIds[Random.Shared.Next(_testUserIds.Count)];

            var request = Http.CreateRequest("GET", $"{BaseUrl}/api/v1/preferredpartners/dashboard")
                .WithHeader("X-User-Id", userId.ToString())
                .WithHeader("X-User-Role", "Broker");

            var response = await Http.Send(request, context);
            return response;
        })
        .WithLoadSimulations(
            Simulation.InjectPerSec(rate: 150, during: TimeSpan.FromMinutes(3))
        );

        var stats = NBomberRunner
            .RegisterScenarios(scenario)
            .WithReportFolder("load-test-results")
            .Run();

        var scnStats = stats.AllScenarios.First();
        scnStats.Ok.Request.Mean.Should().BeLessThan(TimeSpan.FromMilliseconds(400));
        scnStats.Ok.Request.P95.Should().BeLessThan(TimeSpan.FromMilliseconds(800));
    }

    [Fact]
    public async Task MixedWorkload_LoadTest_RealisticUsage()
    {
        var readScenario = Scenario.Create("read_operations", async context =>
        {
            var userId = _testUserIds[Random.Shared.Next(_testUserIds.Count)];
            var operations = new[]
            {
                $"{BaseUrl}/api/v1/preferredpartners",
                $"{BaseUrl}/api/v1/preferredpartners/carriers",
                $"{BaseUrl}/api/v1/preferredpartners/dashboard"
            };

            var endpoint = operations[Random.Shared.Next(operations.Length)];
            var request = Http.CreateRequest("GET", endpoint)
                .WithHeader("X-User-Id", userId.ToString())
                .WithHeader("X-User-Role", "Broker");

            var response = await Http.Send(request, context);
            return response;
        })
        .WithWeight(80) // 80% read operations
        .WithLoadSimulations(
            Simulation.KeepConstant(copies: 40, during: TimeSpan.FromMinutes(5))
        );

        var writeScenario = Scenario.Create("write_operations", async context =>
        {
            var userId = _testUserIds[Random.Shared.Next(_testUserIds.Count)];
            var partnerId = _testPartnerIds[Random.Shared.Next(_testPartnerIds.Count)];

            var operations = new[]
            {
                () => CreatePartnerRequest(userId, partnerId),
                () => UpdatePartnerRequest(userId, partnerId),
                () => ActivatePartnerRequest(partnerId)
            };

            var operation = operations[Random.Shared.Next(operations.Length)];
            var request = operation();

            var response = await Http.Send(request, context);
            return response;
        })
        .WithWeight(20) // 20% write operations
        .WithLoadSimulations(
            Simulation.KeepConstant(copies: 10, during: TimeSpan.FromMinutes(5))
        );

        var stats = NBomberRunner
            .RegisterScenarios(readScenario, writeScenario)
            .WithReportFolder("load-test-results")
            .Run();

        // Assert overall system performance
        var allRequests = stats.AllScenarios.SelectMany(s => new[] { s.Ok.Request, s.Fail.Request });
        var totalRequests = allRequests.Sum(r => r.Count);
        var totalFailures = stats.AllScenarios.Sum(s => s.Fail.Request.Count);
        
        (totalFailures / (double)totalRequests).Should().BeLessThan(0.01); // Less than 1% overall failure rate
    }

    private HttpRequestMessage CreatePartnerRequest(Guid userId, Guid partnerId)
    {
        var createDto = new
        {
            PartnerId = partnerId,
            PartnerType = "Carrier",
            PreferenceLevel = "Medium",
            Priority = Random.Shared.Next(1, 5),
            AutoAssignEnabled = Random.Shared.Next(2) == 0
        };

        return Http.CreateRequest("POST", $"{BaseUrl}/api/v1/preferredpartners")
            .WithHeader("X-User-Id", userId.ToString())
            .WithHeader("X-User-Role", "Broker")
            .WithBody(new StringContent(JsonSerializer.Serialize(createDto)));
    }

    private HttpRequestMessage UpdatePartnerRequest(Guid userId, Guid partnerId)
    {
        var updateDto = new
        {
            PreferenceLevel = "High",
            Priority = 1,
            AutoAssignEnabled = true
        };

        return Http.CreateRequest("PUT", $"{BaseUrl}/api/v1/preferredpartners/{partnerId}")
            .WithHeader("X-User-Id", userId.ToString())
            .WithHeader("X-User-Role", "Broker")
            .WithBody(new StringContent(JsonSerializer.Serialize(updateDto)));
    }

    private HttpRequestMessage ActivatePartnerRequest(Guid partnerId)
    {
        return Http.CreateRequest("POST", $"{BaseUrl}/api/v1/preferredpartners/{partnerId}/activate")
            .WithHeader("X-User-Role", "Broker");
    }
}
