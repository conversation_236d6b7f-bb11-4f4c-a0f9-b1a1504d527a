using MonitoringObservability.Domain.Enums;

namespace MonitoringObservability.Application.DTOs;

public class AlertDto
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public AlertSeverity Severity { get; set; }
    public AlertStatus Status { get; set; }
    public string Source { get; set; } = string.Empty;
    public string ServiceName { get; set; } = string.Empty;
    public string MetricName { get; set; } = string.Empty;
    public double CurrentValue { get; set; }
    public double ThresholdValue { get; set; }

    // Timestamps
    public DateTime TriggeredAt { get; set; }
    public DateTime? AcknowledgedAt { get; set; }
    public DateTime? ResolvedAt { get; set; }
    public DateTime? ClosedAt { get; set; }

    // Assignment
    public Guid? AssignedToUserId { get; set; }
    public string? AssignedToUserName { get; set; }
    public Guid? AcknowledgedByUserId { get; set; }
    public Guid? ResolvedByUserId { get; set; }

    // Additional info
    public Dictionary<string, string> Tags { get; set; } = new();
    public string? ResolutionNotes { get; set; }
    public int EscalationLevel { get; set; }

    // Collections
    public List<AlertNotificationDto> Notifications { get; set; } = new();
    public List<AlertCommentDto> Comments { get; set; } = new();

    // Computed properties
    public TimeSpan? Duration => ResolvedAt.HasValue ? ResolvedAt.Value - TriggeredAt : DateTime.UtcNow - TriggeredAt;
    public bool IsOpen => Status == AlertStatus.Open;
    public bool IsResolved => Status == AlertStatus.Resolved || Status == AlertStatus.Closed;
}

public class AlertNotificationDto
{
    public Guid Id { get; set; }
    public Guid AlertId { get; set; }
    public NotificationChannel Channel { get; set; }
    public string Recipient { get; set; } = string.Empty;
    public bool Sent { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? SentAt { get; set; }
    public string? ErrorMessage { get; set; }
}

public class AlertCommentDto
{
    public Guid Id { get; set; }
    public Guid AlertId { get; set; }
    public Guid UserId { get; set; }
    public string UserName { get; set; } = string.Empty;
    public string Comment { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
}

public class MetricDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string ServiceName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public MetricType Type { get; set; }
    public string Unit { get; set; } = string.Empty;
    public MonitoringCategory Category { get; set; }

    // Current state
    public MetricValueDto CurrentValue { get; set; } = null!;
    public DateTime LastUpdated { get; set; }
    public bool IsActive { get; set; }

    // Thresholds
    public AlertThresholdDto? AlertThreshold { get; set; }
    public bool AlertingEnabled { get; set; }

    // Metadata
    public Dictionary<string, string> Tags { get; set; } = new();
    public Dictionary<string, string> Metadata { get; set; } = new();

    // Configuration
    public TimeSpan RetentionPeriod { get; set; }
    public TimeSpan AggregationInterval { get; set; }
}

public class MetricValueDto
{
    public double Value { get; set; }
    public string Unit { get; set; } = string.Empty;
    public MetricType Type { get; set; }
    public DateTime Timestamp { get; set; }
    public Dictionary<string, string> Tags { get; set; } = new();
}

public class AlertThresholdDto
{
    public string MetricName { get; set; } = string.Empty;
    public double WarningThreshold { get; set; }
    public double CriticalThreshold { get; set; }
    public string Operator { get; set; } = string.Empty;
    public TimeSpan EvaluationWindow { get; set; }
}

public class MetricDataPointDto
{
    public Guid Id { get; set; }
    public Guid MetricId { get; set; }
    public double Value { get; set; }
    public DateTime Timestamp { get; set; }
    public Dictionary<string, string> Tags { get; set; } = new();
}

public class HealthCheckDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string ServiceName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Endpoint { get; set; } = string.Empty;
    public TimeSpan Interval { get; set; }
    public TimeSpan Timeout { get; set; }
    public bool IsEnabled { get; set; }

    // Current state
    public HealthStatus CurrentStatus { get; set; }
    public DateTime LastChecked { get; set; }
    public TimeSpan LastDuration { get; set; }
    public string? LastErrorMessage { get; set; }

    // Statistics
    public int ConsecutiveFailures { get; set; }
    public int TotalChecks { get; set; }
    public int SuccessfulChecks { get; set; }
    public double SuccessRate { get; set; }

    // Configuration
    public int MaxRetries { get; set; }
    public Dictionary<string, string> Headers { get; set; } = new();
    public Dictionary<string, string> Tags { get; set; } = new();
}

public class IncidentDto
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public IncidentSeverity Severity { get; set; }
    public IncidentStatus Status { get; set; }
    public string ServiceName { get; set; } = string.Empty;
    public string? Component { get; set; }

    // Timeline
    public DateTime CreatedAt { get; set; }
    public DateTime? DetectedAt { get; set; }
    public DateTime? AcknowledgedAt { get; set; }
    public DateTime? ResolvedAt { get; set; }
    public DateTime? ClosedAt { get; set; }

    // Assignment
    public Guid CreatedByUserId { get; set; }
    public Guid? AssignedToUserId { get; set; }
    public string? AssignedToUserName { get; set; }
    public Guid? ResolvedByUserId { get; set; }

    // Impact and urgency
    public int ImpactLevel { get; set; }
    public int UrgencyLevel { get; set; }
    public string? ImpactDescription { get; set; }

    // Resolution
    public string? ResolutionSummary { get; set; }
    public string? RootCause { get; set; }
    public string? PreventiveMeasures { get; set; }

    // Metadata
    public Dictionary<string, string> Tags { get; set; } = new();
    public Dictionary<string, object> CustomFields { get; set; } = new();

    // Collections
    public List<IncidentUpdateDto> Updates { get; set; } = new();
    public List<IncidentAlertDto> RelatedAlerts { get; set; } = new();
    public List<IncidentCommentDto> Comments { get; set; } = new();

    // Computed properties
    public TimeSpan? ResolutionTime => ResolvedAt.HasValue ? ResolvedAt.Value - CreatedAt : null;
    public TimeSpan? TimeToAcknowledge => AcknowledgedAt.HasValue ? AcknowledgedAt.Value - CreatedAt : null;
    public int Priority => ImpactLevel * UrgencyLevel;
}

public class IncidentUpdateDto
{
    public Guid Id { get; set; }
    public Guid IncidentId { get; set; }
    public IncidentStatus Status { get; set; }
    public string Message { get; set; } = string.Empty;
    public Guid UpdatedByUserId { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class IncidentAlertDto
{
    public Guid Id { get; set; }
    public Guid IncidentId { get; set; }
    public Guid AlertId { get; set; }
    public string AlertTitle { get; set; } = string.Empty;
    public DateTime LinkedAt { get; set; }
}

public class IncidentCommentDto
{
    public Guid Id { get; set; }
    public Guid IncidentId { get; set; }
    public Guid UserId { get; set; }
    public string UserName { get; set; } = string.Empty;
    public string Comment { get; set; } = string.Empty;
    public bool IsInternal { get; set; }
    public DateTime CreatedAt { get; set; }
}

public class PerformanceMetricsDto
{
    public double ResponseTime { get; set; }
    public double Throughput { get; set; }
    public double ErrorRate { get; set; }
    public double CpuUsage { get; set; }
    public double MemoryUsage { get; set; }
    public double DiskUsage { get; set; }
    public DateTime MeasuredAt { get; set; }
    public HealthStatus HealthStatus { get; set; }
    public bool IsHealthy { get; set; }
}

public class ServiceHealthSummaryDto
{
    public string ServiceName { get; set; } = string.Empty;
    public HealthStatus OverallStatus { get; set; }
    public List<HealthCheckDto> HealthChecks { get; set; } = new();
    public PerformanceMetricsDto? PerformanceMetrics { get; set; }
    public int ActiveAlerts { get; set; }
    public int OpenIncidents { get; set; }
    public double UptimePercentage { get; set; }
    public DateTime LastUpdated { get; set; }
}

public class MonitoringDashboardDto
{
    public string Name { get; set; } = string.Empty;
    public DashboardType Type { get; set; }
    public List<ServiceHealthSummaryDto> Services { get; set; } = new();
    public List<AlertDto> RecentAlerts { get; set; } = new();
    public List<IncidentDto> ActiveIncidents { get; set; } = new();
    public Dictionary<string, object> Metrics { get; set; } = new();
    public DateTime GeneratedAt { get; set; }
}

// Distributed Tracing DTOs
public class TraceDto
{
    public Guid Id { get; set; }
    public string TraceId { get; set; } = string.Empty;
    public string RootOperationName { get; set; } = string.Empty;
    public string RootServiceName { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public TimeSpan? Duration { get; set; }
    public TraceStatus Status { get; set; }
    public string? ErrorMessage { get; set; }
    public bool IsActive { get; set; }
    public Dictionary<string, string> Tags { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();

    // Collections
    public List<SpanDto> Spans { get; set; } = new();
    public List<TraceEventDto> Events { get; set; } = new();

    // Computed properties
    public int SpanCount { get; set; }
    public int ServiceCount { get; set; }
    public string[] ServiceNames { get; set; } = Array.Empty<string>();
    public bool HasErrors { get; set; }
    public TimeSpan? TotalServiceTime { get; set; }
}

public class SpanDto
{
    public Guid Id { get; set; }
    public string TraceId { get; set; } = string.Empty;
    public string SpanId { get; set; } = string.Empty;
    public string? ParentSpanId { get; set; }
    public string OperationName { get; set; } = string.Empty;
    public string ServiceName { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public TimeSpan? Duration { get; set; }
    public bool IsActive { get; set; }
    public bool HasError { get; set; }
    public string? ErrorMessage { get; set; }
    public string? ErrorType { get; set; }
    public Dictionary<string, string> Tags { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();

    // Collections
    public List<SpanEventDto> Events { get; set; } = new();
    public List<SpanLogDto> Logs { get; set; } = new();

    // Computed properties
    public bool IsRoot { get; set; }
    public int Depth { get; set; }
    public string Component { get; set; } = string.Empty;
    public string HttpMethod { get; set; } = string.Empty;
    public string HttpUrl { get; set; } = string.Empty;
    public string HttpStatusCode { get; set; } = string.Empty;
    public string DatabaseType { get; set; } = string.Empty;
    public string DatabaseStatement { get; set; } = string.Empty;
}

public class TraceEventDto
{
    public Guid Id { get; set; }
    public string TraceId { get; set; } = string.Empty;
    public string EventName { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class SpanEventDto
{
    public Guid Id { get; set; }
    public string SpanId { get; set; } = string.Empty;
    public string EventName { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class SpanLogDto
{
    public Guid Id { get; set; }
    public string SpanId { get; set; } = string.Empty;
    public string Level { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public Dictionary<string, object> Fields { get; set; } = new();
}

public class ServiceDependencyDto
{
    public Guid Id { get; set; }
    public string FromService { get; set; } = string.Empty;
    public string ToService { get; set; } = string.Empty;
    public string OperationType { get; set; } = string.Empty;
    public int CallCount { get; set; }
    public TimeSpan? AverageLatency { get; set; }
    public DateTime FirstSeen { get; set; }
    public DateTime LastSeen { get; set; }
    public double ErrorRate { get; set; }
    public int ErrorCount { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class ServiceDependencyGraphDto
{
    public List<ServiceNodeDto> Nodes { get; set; } = new();
    public List<ServiceEdgeDto> Edges { get; set; } = new();
    public DateTime GeneratedAt { get; set; }
}

public class ServiceNodeDto
{
    public string ServiceName { get; set; } = string.Empty;
    public HealthStatus HealthStatus { get; set; }
    public int ActiveAlerts { get; set; }
    public int OpenIncidents { get; set; }
    public double UptimePercentage { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class ServiceEdgeDto
{
    public string FromService { get; set; } = string.Empty;
    public string ToService { get; set; } = string.Empty;
    public string OperationType { get; set; } = string.Empty;
    public int CallCount { get; set; }
    public TimeSpan? AverageLatency { get; set; }
    public double ErrorRate { get; set; }
    public double Weight { get; set; } // For visualization
}

// Service Dependency Mapping DTOs
public class ServiceImpactAnalysisDto
{
    public string ServiceName { get; set; } = string.Empty;
    public ServiceImpactType ImpactType { get; set; }
    public List<string> AffectedServices { get; set; } = new();
    public double ImpactScore { get; set; }
    public string Description { get; set; } = string.Empty;
    public List<string> Recommendations { get; set; } = new();
    public DateTime AnalyzedAt { get; set; }
}

public class ServiceTopologyDto
{
    public string? RootService { get; set; }
    public int MaxDepth { get; set; }
    public List<ServiceLayerDto> Layers { get; set; } = new();
    public DateTime GeneratedAt { get; set; }
}

public class ServiceLayerDto
{
    public int Depth { get; set; }
    public List<ServiceTopologyNodeDto> Services { get; set; } = new();
}

public class ServiceTopologyNodeDto
{
    public string ServiceName { get; set; } = string.Empty;
    public int Depth { get; set; }
    public int DependencyCount { get; set; }
    public int DependentCount { get; set; }
    public bool IsLeaf { get; set; }
    public bool IsRoot { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public enum ServiceImpactType
{
    Upstream = 0,
    Downstream = 1,
    Bidirectional = 2
}

// Anomaly Detection DTOs
public class AnomalyDetectionModelDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string ServiceName { get; set; } = string.Empty;
    public string MetricName { get; set; } = string.Empty;
    public AnomalyDetectionAlgorithm Algorithm { get; set; }
    public AnomalySensitivity SensitivityLevel { get; set; }
    public bool IsEnabled { get; set; }
    public bool IsTrained { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime LastModified { get; set; }
    public DateTime? LastTrained { get; set; }
    public DateTime? LastDetection { get; set; }
    public TimeSpan TrainingWindow { get; set; }
    public TimeSpan DetectionWindow { get; set; }
    public int MinDataPoints { get; set; }
    public double? Accuracy { get; set; }
    public double? Precision { get; set; }
    public double? Recall { get; set; }
    public Dictionary<string, object> Parameters { get; set; } = new();
    public Dictionary<string, string> Tags { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();

    // Computed properties
    public int TotalDetections { get; set; }
    public int AnomaliesDetected { get; set; }
    public double AnomalyRate { get; set; }
    public bool NeedsRetraining { get; set; }
}

public class AnomalyDetectionResultDto
{
    public Guid Id { get; set; }
    public Guid ModelId { get; set; }
    public string ModelName { get; set; } = string.Empty;
    public string ServiceName { get; set; } = string.Empty;
    public string MetricName { get; set; } = string.Empty;
    public double MetricValue { get; set; }
    public DateTime Timestamp { get; set; }
    public bool IsAnomaly { get; set; }
    public double AnomalyScore { get; set; }
    public double? ExpectedValue { get; set; }
    public double? Confidence { get; set; }
    public string? Explanation { get; set; }
    public DateTime CreatedAt { get; set; }
    public bool IsConfirmed { get; set; }
    public bool IsFalsePositive { get; set; }
    public string? UserFeedback { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();

    // Computed properties
    public double? Deviation { get; set; }
    public double? DeviationPercentage { get; set; }
}

public class AnomalyStatisticsDto
{
    public string? ServiceName { get; set; }
    public TimeSpan Period { get; set; }
    public int TotalAnomalies { get; set; }
    public int ConfirmedAnomalies { get; set; }
    public int FalsePositives { get; set; }
    public int UnreviewedAnomalies { get; set; }
    public double AverageAnomalyScore { get; set; }
    public Dictionary<string, int> TopAnomalousMetrics { get; set; } = new();
    public Dictionary<int, int> AnomaliesByHour { get; set; } = new();
    public double ModelAccuracy { get; set; }
    public DateTime GeneratedAt { get; set; }
}

public class ModelTrainingSessionDto
{
    public Guid Id { get; set; }
    public Guid ModelId { get; set; }
    public string ModelName { get; set; } = string.Empty;
    public AnomalyDetectionAlgorithm Algorithm { get; set; }
    public int DataPointCount { get; set; }
    public DateTime StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public TrainingStatus Status { get; set; }
    public double? Accuracy { get; set; }
    public double? Precision { get; set; }
    public double? Recall { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();

    // Computed properties
    public TimeSpan? Duration { get; set; }
}

// Custom Dashboard DTOs
public class CustomDashboardDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public Guid OwnerId { get; set; }
    public DashboardVisibility Visibility { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime LastModified { get; set; }
    public DateTime? LastViewed { get; set; }
    public TimeSpan RefreshInterval { get; set; }
    public DashboardLayout Layout { get; set; } = new();
    public Dictionary<string, string> Tags { get; set; } = new();
    public Dictionary<string, object> Settings { get; set; } = new();

    // Collections
    public List<DashboardWidgetDto> Widgets { get; set; } = new();
    public List<DashboardFilterDto> Filters { get; set; } = new();
    public List<DashboardPermissionDto> Permissions { get; set; } = new();

    // Computed properties
    public int WidgetCount { get; set; }
    public int ViewCount { get; set; }
    public bool IsShared { get; set; }
    public bool HasFilters { get; set; }
}

public class DashboardWidgetDto
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public WidgetType Type { get; set; }
    public WidgetPosition Position { get; set; } = new();
    public WidgetSize Size { get; set; } = new();
    public bool IsVisible { get; set; }
    public TimeSpan RefreshInterval { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime LastModified { get; set; }
    public Dictionary<string, object> Configuration { get; set; } = new();
}

public class DashboardFilterDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public FilterType Type { get; set; }
    public string Field { get; set; } = string.Empty;
    public FilterOperator Operator { get; set; }
    public object Value { get; set; } = new();
    public bool IsEnabled { get; set; }
    public DateTime CreatedAt { get; set; }
}

public class DashboardPermissionDto
{
    public Guid Id { get; set; }
    public Guid? UserId { get; set; }
    public string? RoleName { get; set; }
    public DashboardAccessLevel AccessLevel { get; set; }
    public DateTime GrantedAt { get; set; }
}

public class DashboardExportDto
{
    public Guid DashboardId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public ExportFormat Format { get; set; }
    public DateTime ExportedAt { get; set; }
    public object Data { get; set; } = new();
}

public class DashboardImportDto
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DashboardVisibility Visibility { get; set; }
    public List<DashboardWidgetDto> Widgets { get; set; } = new();
    public List<DashboardFilterDto> Filters { get; set; } = new();
    public DashboardLayout Layout { get; set; } = new();
    public Dictionary<string, string> Tags { get; set; } = new();
}

public class DashboardAnalyticsDto
{
    public Guid DashboardId { get; set; }
    public string DashboardName { get; set; } = string.Empty;
    public TimeSpan Period { get; set; }
    public int TotalViews { get; set; }
    public int WidgetCount { get; set; }
    public DateTime? LastViewed { get; set; }
    public DateTime CreatedAt { get; set; }
    public bool IsShared { get; set; }
    public Dictionary<DateTime, int> ViewsByDay { get; set; } = new();
    public Dictionary<string, int> PopularWidgets { get; set; } = new();
    public DateTime GeneratedAt { get; set; }
}

public enum ExportFormat
{
    JSON = 0,
    PDF = 1,
    PNG = 2,
    CSV = 3
}

// SLA Monitoring DTOs
public class SLAComplianceDto
{
    public Guid SLAId { get; set; }
    public string SLAName { get; set; } = string.Empty;
    public string ServiceName { get; set; } = string.Empty;
    public TimeSpan Period { get; set; }
    public double OverallCompliance { get; set; }
    public SLAStatus Status { get; set; }
    public List<SLAObjectiveComplianceDto> ObjectiveCompliances { get; set; } = new();
    public List<SLABreachDto> RecentBreaches { get; set; } = new();
    public DateTime GeneratedAt { get; set; }
}

public class SLAObjectiveComplianceDto
{
    public Guid ObjectiveId { get; set; }
    public string ObjectiveName { get; set; } = string.Empty;
    public SLAObjectiveType Type { get; set; }
    public double TargetValue { get; set; }
    public double CurrentValue { get; set; }
    public double Compliance { get; set; }
    public double Weight { get; set; }
    public bool IsInCompliance { get; set; }
    public DateTime? LastEvaluated { get; set; }
}

public class SLABreachDto
{
    public Guid Id { get; set; }
    public Guid SLAId { get; set; }
    public Guid ObjectiveId { get; set; }
    public double ActualValue { get; set; }
    public double TargetValue { get; set; }
    public BreachSeverity Severity { get; set; }
    public BreachStatus Status { get; set; }
    public string Description { get; set; } = string.Empty;
    public DateTime DetectedAt { get; set; }
    public DateTime? ResolvedAt { get; set; }
    public TimeSpan? Duration { get; set; }
    public double DeviationPercentage { get; set; }
    public string? ResolutionNotes { get; set; }
    public Guid? ResolvedBy { get; set; }
}

public class SLADashboardDto
{
    public string? ServiceName { get; set; }
    public int TotalSLAs { get; set; }
    public int SLAsInCompliance { get; set; }
    public int SLAsAtRisk { get; set; }
    public int SLAsInBreach { get; set; }
    public double AverageCompliance { get; set; }
    public int ActiveBreaches { get; set; }
    public List<SLAStatusDto> SLAStatuses { get; set; } = new();
    public DateTime GeneratedAt { get; set; }
}

public class SLAStatusDto
{
    public Guid SLAId { get; set; }
    public string SLAName { get; set; } = string.Empty;
    public string ServiceName { get; set; } = string.Empty;
    public SLAStatus Status { get; set; }
    public double Compliance { get; set; }
    public int ActiveBreaches { get; set; }
    public DateTime? LastEvaluated { get; set; }
}

// Capacity Planning DTOs
public class CapacityAnalysisDto
{
    public string ServiceName { get; set; } = string.Empty;
    public TimeSpan AnalysisWindow { get; set; }
    public DateTime AnalyzedAt { get; set; }
    public Dictionary<ResourceType, ResourceUtilizationDto> ResourceUtilization { get; set; } = new();
    public Dictionary<ResourceType, TrendAnalysisDto> TrendAnalysis { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
}

public class ResourceUtilizationDto
{
    public double Current { get; set; }
    public double Average { get; set; }
    public double Maximum { get; set; }
    public double Minimum { get; set; }
    public double Percentile95 { get; set; }
    public double Percentile99 { get; set; }
}

public class TrendAnalysisDto
{
    public TrendDirection Direction { get; set; }
    public double ChangePercentage { get; set; }
    public double Slope { get; set; }
}

public class CapacityDashboardDto
{
    public string? ServiceName { get; set; }
    public int TotalPlans { get; set; }
    public int ActiveAlerts { get; set; }
    public int PendingRecommendations { get; set; }
    public List<CapacityPlanStatusDto> PlanStatuses { get; set; } = new();
    public Dictionary<ResourceType, double> ResourceUtilization { get; set; } = new();
    public DateTime GeneratedAt { get; set; }
}

public class CapacityPlanStatusDto
{
    public Guid PlanId { get; set; }
    public string PlanName { get; set; } = string.Empty;
    public string ServiceName { get; set; } = string.Empty;
    public CapacityPlanStatus Status { get; set; }
    public DateTime? LastExecuted { get; set; }
    public DateTime? NextExecution { get; set; }
    public int ActiveAlerts { get; set; }
    public int PendingRecommendations { get; set; }
    public double? Accuracy { get; set; }
}

public enum TrendDirection
{
    Decreasing = 0,
    Stable = 1,
    Increasing = 2
}

// Missing enums for dashboard functionality
public enum DashboardLayout
{
    Grid = 0,
    Masonry = 1,
    Flexible = 2,
    Fixed = 3
}

public enum WidgetPosition
{
    TopLeft = 0,
    TopCenter = 1,
    TopRight = 2,
    MiddleLeft = 3,
    MiddleCenter = 4,
    MiddleRight = 5,
    BottomLeft = 6,
    BottomCenter = 7,
    BottomRight = 8
}

public enum WidgetSize
{
    Small = 0,
    Medium = 1,
    Large = 2,
    ExtraLarge = 3
}
