{"AuditSettings": {"EnableAuditLogging": true, "EncryptSensitiveData": true, "EncryptArchivedMessages": true, "MessageRetentionDays": 365, "DeliveryRetentionDays": 90, "EmergencyRetentionDays": 2555, "LoginRetentionDays": 90, "DefaultRetentionDays": 365, "AutoApplyRetentionPolicies": true, "RetentionPolicyIntervalHours": 24, "AuditEventTypes": {"MessageSent": {"Enabled": true, "Severity": "Medium", "RetentionDays": 365, "RequireEncryption": false}, "MessageDelivered": {"Enabled": true, "Severity": "Low", "RetentionDays": 90, "RequireEncryption": false}, "EmergencyAlert": {"Enabled": true, "Severity": "Critical", "RetentionDays": 2555, "RequireEncryption": true}, "UserLogin": {"Enabled": true, "Severity": "Medium", "RetentionDays": 90, "RequireEncryption": false}, "DataExport": {"Enabled": true, "Severity": "High", "RetentionDays": 1095, "RequireEncryption": true}}}, "ComplianceMonitoring": {"EnableComplianceMonitoring": true, "MonitoringIntervalMinutes": 60, "EnableAutomaticRetention": true, "EnableScheduledReports": true, "EnableViolationDetection": true, "EnableRetentionMonitoring": true, "PolicyExecutionIntervalHours": 24, "DailyReports": true, "WeeklyReports": true, "MonthlyReports": true, "DailyReportHour": 2, "WeeklyReportDay": "Monday", "WeeklyReportHour": 3, "MonthlyReportDay": 1, "MonthlyReportHour": 4, "EmailReports": true, "ComplianceTeamEmails": ["<EMAIL>", "<EMAIL>", "<EMAIL>"], "AlertOnViolations": true, "AlertOnRetentionDeadlines": true, "RetentionDeadlineWarningDays": 7, "MaxOverdueRecords": 100, "MaxViolationRate": 0.01}, "ComplianceFrameworks": {"GDPR": {"Enabled": true, "DataRetentionMaxDays": 1095, "RequireExplicitConsent": true, "EnableRightToBeForgotten": true, "DataPortabilityEnabled": true, "BreachNotificationHours": 72, "DPOContact": "<EMAIL>", "LegalBasis": "Legitimate Interest", "DataProcessingPurposes": ["Service Delivery", "Customer Communication", "Legal Compliance"]}, "CCPA": {"Enabled": false, "DataRetentionMaxDays": 365, "EnableDataSaleOptOut": true, "RequirePrivacyNotice": true}, "DataLocalization": {"Enabled": true, "RequiredRegions": ["India"], "ProhibitedRegions": [], "CrossBorderTransferApproval": true, "DataResidencyValidation": true}}, "DataRetentionPolicies": {"DefaultPolicies": [{"Name": "Standard Message Retention", "Description": "Standard retention policy for regular messages", "Action": "Archive", "RetentionPeriodDays": 365, "ApplicableEventTypes": ["MessageSent", "MessageDelivered"], "ApplicableFlags": [], "IsActive": true}, {"Name": "Emergency Alert <PERSON>tion", "Description": "Extended retention for emergency alerts", "Action": "Archive", "RetentionPeriodDays": 2555, "ApplicableEventTypes": ["<PERSON><PERSON><PERSON><PERSON>"], "ApplicableFlags": [], "IsActive": true}, {"Name": "GDPR Personal Data Deletion", "Description": "GDPR-compliant deletion of personal data", "Action": "Anonymize", "RetentionPeriodDays": 1095, "ApplicableEventTypes": [], "ApplicableFlags": ["GDPR"], "IsActive": true}, {"Name": "Login Activity Cleanup", "Description": "Regular cleanup of login activity logs", "Action": "Delete", "RetentionPeriodDays": 90, "ApplicableEventTypes": ["UserLogin", "UserLogout"], "ApplicableFlags": [], "IsActive": true}]}, "DisputeResolution": {"EnableDisputeResolution": true, "MaxDisputeResolutionDays": 30, "RequireLegalReview": true, "AutoArchiveDisputeData": true, "DisputeDataRetentionDays": 2555, "EscalationSettings": {"Level1TimeoutHours": 24, "Level2TimeoutHours": 72, "Level3TimeoutHours": 168, "AutoEscalateToLegal": true}, "NotificationSettings": {"NotifyOnNewDispute": true, "NotifyOnEscalation": true, "NotifyOnResolution": true, "ComplianceTeamNotification": true}}, "MessageArchiving": {"EnableMessageArchiving": true, "ArchiveEncryption": true, "CompressionEnabled": true, "ArchiveStorageLocation": "secure-archive", "ArchiveRetentionYears": 7, "AutoArchiveThresholds": {"MessageAgeDays": 365, "ConversationInactivityDays": 180, "UserInactivityDays": 730}, "ArchiveReasons": {"LegalHold": {"RetentionYears": 10, "RequireApproval": true, "NotifyLegalTeam": true}, "ComplianceRequirement": {"RetentionYears": 7, "RequireApproval": false, "NotifyLegalTeam": false}, "DisputeResolution": {"RetentionYears": 7, "RequireApproval": true, "NotifyLegalTeam": true}}}, "SecurityAudit": {"EnableSecurityAudit": true, "LogSecurityEvents": true, "MonitorUnauthorizedAccess": true, "DetectAnomalousActivity": true, "SecurityEventRetentionDays": 1095, "AlertThresholds": {"FailedLoginAttempts": 5, "UnauthorizedAccessAttempts": 3, "DataExportRequests": 10, "SuspiciousActivityScore": 0.8}, "SecurityNotifications": {"NotifySecurityTeam": true, "NotifyComplianceTeam": true, "SecurityTeamEmails": ["<EMAIL>", "<EMAIL>"], "EscalateToManagement": true}}, "ReportGeneration": {"EnableAutomaticReports": true, "ReportFormats": ["PDF", "Excel", "JSON"], "DefaultReportFormat": "PDF", "ReportRetentionDays": 1095, "ScheduledReports": {"Daily": {"Enabled": true, "ReportTypes": ["MessageDelivery"], "Recipients": ["<EMAIL>"], "GenerationTime": "02:00"}, "Weekly": {"Enabled": true, "ReportTypes": ["DataRetention", "SecurityAudit"], "Recipients": ["<EMAIL>", "<EMAIL>"], "GenerationDay": "Monday", "GenerationTime": "03:00"}, "Monthly": {"Enabled": true, "ReportTypes": ["Comprehensive"], "Recipients": ["<EMAIL>", "<EMAIL>"], "GenerationDay": 1, "GenerationTime": "04:00"}}, "CustomReports": {"MaxCustomReportsPerUser": 10, "MaxReportDataRange": 365, "RequireApprovalForSensitiveData": true}}, "DataPrivacy": {"EnableDataPrivacy": true, "PIIDetection": true, "AutoRedactPII": false, "RequireConsentForProcessing": true, "ConsentRetentionDays": 1095, "DataSubjectRights": {"EnableDataPortability": true, "EnableRightToRectification": true, "EnableRightToErasure": true, "EnableRightToRestriction": true, "ProcessingTimeMaxDays": 30}, "ConsentManagement": {"RequireExplicitConsent": true, "ConsentGranularity": "Purpose", "AllowConsentWithdrawal": true, "ConsentRenewalDays": 365}}, "Integration": {"LegalHoldSystem": {"Enabled": false, "ApiEndpoint": "https://api.legalholds.com", "ApiKey": "legal-hold-api-key", "AutoCheckLegalHolds": true}, "ComplianceManagementSystem": {"Enabled": false, "ApiEndpoint": "https://api.compliance.com", "ApiKey": "compliance-api-key", "SyncInterval": "24:00:00"}, "DataLossPreventionSystem": {"Enabled": false, "ApiEndpoint": "https://api.dlp.com", "ApiKey": "dlp-api-key", "RealTimeScanning": true}}, "Logging": {"LogLevel": {"CommunicationNotification.Infrastructure.Services.CommunicationAuditService": "Debug", "CommunicationNotification.Infrastructure.Services.ComplianceMonitoringService": "Information", "CommunicationNotification.Infrastructure.Repositories.AuditRepository": "Debug", "CommunicationNotification.Infrastructure.Repositories.ComplianceRepository": "Information"}}}