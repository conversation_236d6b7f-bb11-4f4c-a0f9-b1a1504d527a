using ContentManagement.Infrastructure.Data;
using ContentManagement.Domain.Entities;
using ContentManagement.Domain.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace ContentManagement.Tests;

/// <summary>
/// Test configuration and utilities for Content Management tests
/// </summary>
public static class TestConfiguration
{
    /// <summary>
    /// Create an in-memory database context for testing
    /// </summary>
    public static ContentManagementDbContext CreateInMemoryContext(string databaseName = "")
    {
        if (string.IsNullOrEmpty(databaseName))
        {
            databaseName = Guid.NewGuid().ToString();
        }

        var options = new DbContextOptionsBuilder<ContentManagementDbContext>()
            .UseInMemoryDatabase(databaseName)
            .EnableSensitiveDataLogging()
            .Options;

        return new ContentManagementDbContext(options);
    }

    /// <summary>
    /// Create a service collection for testing with in-memory database
    /// </summary>
    public static IServiceCollection CreateTestServices(string databaseName = "")
    {
        var services = new ServiceCollection();

        // Add logging
        services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Warning));

        // Add in-memory database
        services.AddDbContext<ContentManagementDbContext>(options =>
            options.UseInMemoryDatabase(databaseName ?? Guid.NewGuid().ToString()));

        return services;
    }

    /// <summary>
    /// Seed test data into the database context
    /// </summary>
    public static async Task SeedTestDataAsync(ContentManagementDbContext context)
    {
        // Clear existing data
        context.Contents.RemoveRange(context.Contents);
        context.FaqItems.RemoveRange(context.FaqItems);
        context.PolicyDocuments.RemoveRange(context.PolicyDocuments);
        context.PolicyAcceptances.RemoveRange(context.PolicyAcceptances);
        await context.SaveChangesAsync();

        // Seed content
        var testContent = new Content(
            "Test Content",
            "test-content",
            ContentType.General,
            "This is test content for testing purposes",
            Guid.NewGuid(),
            "Test User",
            "Test content description");

        context.Contents.Add(testContent);

        // Seed FAQ
        var testFaq = new Domain.Entities.FaqItem(
            "What is a test?",
            "A test is a procedure to verify functionality",
            Domain.Enums.FaqCategory.General,
            Guid.NewGuid(),
            "FAQ Creator");

        context.FaqItems.Add(testFaq);

        // Seed Policy
        var testPolicy = new Domain.Entities.PolicyDocument(
            Domain.Enums.PolicyType.PrivacyPolicy,
            "Test Privacy Policy",
            "1.0",
            "This is a test privacy policy",
            DateTime.UtcNow,
            Guid.NewGuid(),
            "Policy Creator",
            requiresAcceptance: true);

        context.PolicyDocuments.Add(testPolicy);

        await context.SaveChangesAsync();
    }

    /// <summary>
    /// Test data factory for creating test entities
    /// </summary>
    public static class TestDataFactory
    {
        public static Content CreateTestContent(
            string title = "Test Content",
            string slug = "test-content",
            ContentType type = ContentType.General,
            string body = "Test content body")
        {
            return new Content(
                title,
                slug,
                type,
                body,
                Guid.NewGuid(),
                "Test User");
        }

        public static FaqItem CreateTestFaq(
            string question = "Test Question?",
            string answer = "Test Answer",
            FaqCategory category = FaqCategory.General)
        {
            return new FaqItem(
                question,
                answer,
                category,
                Guid.NewGuid(),
                "Test User");
        }

        public static PolicyDocument CreateTestPolicy(
            PolicyType type = PolicyType.PrivacyPolicy,
            string title = "Test Policy",
            string version = "1.0",
            bool requiresAcceptance = true)
        {
            return new PolicyDocument(
                type,
                title,
                version,
                "Test policy content",
                DateTime.UtcNow,
                Guid.NewGuid(),
                "Test User",
                requiresAcceptance: requiresAcceptance);
        }

        public static PolicyAcceptance CreateTestPolicyAcceptance(
            Guid policyId,
            Guid userId,
            string version = "1.0")
        {
            return new PolicyAcceptance(
                policyId,
                userId,
                "Test User",
                "User",
                version);
        }
    }

    /// <summary>
    /// Test assertions and utilities
    /// </summary>
    public static class TestAssertions
    {
        public static void AssertContentEquals(Content expected, Content actual)
        {
            if (expected == null && actual == null) return;
            if (expected == null || actual == null) throw new ArgumentException("One content is null");

            if (expected.Title != actual.Title) throw new ArgumentException($"Title mismatch: {expected.Title} != {actual.Title}");
            if (expected.Slug != actual.Slug) throw new ArgumentException($"Slug mismatch: {expected.Slug} != {actual.Slug}");
            if (expected.Type != actual.Type) throw new ArgumentException($"Type mismatch: {expected.Type} != {actual.Type}");
            if (expected.ContentBody != actual.ContentBody) throw new ArgumentException($"Content body mismatch");
        }

        public static void AssertFaqEquals(FaqItem expected, FaqItem actual)
        {
            if (expected == null && actual == null) return;
            if (expected == null || actual == null) throw new ArgumentException("One FAQ is null");

            if (expected.Question != actual.Question) throw new ArgumentException($"Question mismatch: {expected.Question} != {actual.Question}");
            if (expected.Answer != actual.Answer) throw new ArgumentException($"Answer mismatch: {expected.Answer} != {actual.Answer}");
            if (expected.Category != actual.Category) throw new ArgumentException($"Category mismatch: {expected.Category} != {actual.Category}");
        }

        public static void AssertPolicyEquals(PolicyDocument expected, PolicyDocument actual)
        {
            if (expected == null && actual == null) return;
            if (expected == null || actual == null) throw new ArgumentException("One policy is null");

            if (expected.PolicyType != actual.PolicyType) throw new ArgumentException($"Policy type mismatch: {expected.PolicyType} != {actual.PolicyType}");
            if (expected.Version != actual.Version) throw new ArgumentException($"Version mismatch: {expected.Version} != {actual.Version}");
            if (expected.RequiresAcceptance != actual.RequiresAcceptance) throw new ArgumentException($"Requires acceptance mismatch");
        }
    }

    /// <summary>
    /// Performance test utilities
    /// </summary>
    public static class PerformanceTestUtils
    {
        public static async Task<TimeSpan> MeasureExecutionTimeAsync(Func<Task> action)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            await action();
            stopwatch.Stop();
            return stopwatch.Elapsed;
        }

        public static TimeSpan MeasureExecutionTime(Action action)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            action();
            stopwatch.Stop();
            return stopwatch.Elapsed;
        }

        public static async Task<(T Result, TimeSpan Duration)> MeasureExecutionTimeAsync<T>(Func<Task<T>> func)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            var result = await func();
            stopwatch.Stop();
            return (result, stopwatch.Elapsed);
        }

        public static (T Result, TimeSpan Duration) MeasureExecutionTime<T>(Func<T> func)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            var result = func();
            stopwatch.Stop();
            return (result, stopwatch.Elapsed);
        }
    }
}
