using MediatR;
using Microsoft.Extensions.Logging;
using DocumentManagement.Domain.Repositories;
using DocumentManagement.Domain.Enums;

namespace DocumentManagement.Application.Queries.GetDriverDocumentCenter;

public class GetDriverDocumentCenterQueryHandler : IRequestHandler<GetDriverDocumentCenterQuery, GetDriverDocumentCenterResponse>
{
    private readonly IDriverDocumentRepository _documentRepository;
    private readonly IDocumentExpiryAlertRepository _alertRepository;
    private readonly ILogger<GetDriverDocumentCenterQueryHandler> _logger;

    public GetDriverDocumentCenterQueryHandler(
        IDriverDocumentRepository documentRepository,
        IDocumentExpiryAlertRepository alertRepository,
        ILogger<GetDriverDocumentCenterQueryHandler> logger)
    {
        _documentRepository = documentRepository;
        _alertRepository = alertRepository;
        _logger = logger;
    }

    public async Task<GetDriverDocumentCenterResponse> Handle(GetDriverDocumentCenterQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Getting document center for driver {DriverId}", request.DriverId);

            // Get driver documents
            var documents = await _documentRepository.GetByDriverIdAsync(request.DriverId, cancellationToken);
            
            // Filter documents based on request
            if (!string.IsNullOrWhiteSpace(request.DocumentTypeFilter))
            {
                documents = documents.Where(d => d.DocumentType.ToString().Equals(request.DocumentTypeFilter, StringComparison.OrdinalIgnoreCase)).ToList();
            }

            if (!string.IsNullOrWhiteSpace(request.StatusFilter))
            {
                documents = documents.Where(d => d.Status.ToString().Equals(request.StatusFilter, StringComparison.OrdinalIgnoreCase)).ToList();
            }

            // Build document center
            var documentCenter = new DriverDocumentCenterDto
            {
                DriverId = request.DriverId,
                DriverName = "Driver Name", // Would come from driver service
                DocumentCategories = BuildDocumentCategories(documents, request),
                Summary = BuildDocumentSummary(documents, request.ExpiryWarningDays),
                ExpiryAlerts = await BuildExpiryAlerts(request.DriverId, documents, request.ExpiryWarningDays, cancellationToken),
                RecentActivity = await BuildRecentActivity(request.DriverId, cancellationToken),
                UploadRequirements = BuildUploadRequirements(documents),
                AvailableActions = GetAvailableActions(documents)
            };

            return new GetDriverDocumentCenterResponse
            {
                IsSuccess = true,
                DocumentCenter = documentCenter
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting document center for driver {DriverId}", request.DriverId);
            return new GetDriverDocumentCenterResponse
            {
                IsSuccess = false,
                ErrorMessage = ex.Message
            };
        }
    }

    private List<DocumentCategoryDto> BuildDocumentCategories(List<Domain.Entities.DriverDocument> documents, GetDriverDocumentCenterQuery request)
    {
        var categories = new List<DocumentCategoryDto>();
        var documentGroups = documents.GroupBy(d => GetDocumentCategory(d.DocumentType));

        foreach (var group in documentGroups)
        {
            var categoryDocuments = group.Select(d => MapToDriverDocumentDto(d, request)).ToList();
            
            categories.Add(new DocumentCategoryDto
            {
                CategoryName = group.Key,
                CategoryDescription = GetCategoryDescription(group.Key),
                Documents = categoryDocuments,
                TotalDocuments = categoryDocuments.Count,
                ValidDocuments = categoryDocuments.Count(d => d.IsValid && !d.IsExpired),
                ExpiredDocuments = categoryDocuments.Count(d => d.IsExpired),
                ExpiringDocuments = categoryDocuments.Count(d => d.IsExpiringSoon),
                IsRequired = IsRequiredCategory(group.Key),
                CategoryIcon = GetCategoryIcon(group.Key)
            });
        }

        return categories.OrderBy(c => c.CategoryName).ToList();
    }

    private DriverDocumentDto MapToDriverDocumentDto(Domain.Entities.DriverDocument document, GetDriverDocumentCenterQuery request)
    {
        var now = DateTime.UtcNow;
        var daysUntilExpiry = document.ExpiryDate?.Subtract(now).Days;
        var isExpired = document.ExpiryDate <= now;
        var isExpiringSoon = !isExpired && daysUntilExpiry <= request.ExpiryWarningDays;

        return new DriverDocumentDto
        {
            DocumentId = document.Id,
            DocumentType = document.DocumentType.ToString(),
            DocumentName = document.DocumentName,
            FileName = document.FileName,
            Status = document.Status.ToString(),
            UploadedAt = document.UploadedAt,
            ExpiryDate = document.ExpiryDate,
            VerifiedAt = document.VerifiedAt,
            VerifiedBy = document.VerifiedBy,
            ContentType = document.ContentType,
            FileSize = document.FileSize,
            FileSizeFormatted = FormatFileSize(document.FileSize),
            PreviewUrl = request.IncludePreviewUrls ? $"/api/documents/{document.Id}/preview" : null,
            DownloadUrl = $"/api/documents/{document.Id}/download",
            ThumbnailUrl = request.IncludePreviewUrls ? $"/api/documents/{document.Id}/thumbnail" : null,
            IsExpired = isExpired,
            IsExpiringSoon = isExpiringSoon,
            IsValid = document.Status == DocumentStatus.Verified,
            RequiresRenewal = isExpired || isExpiringSoon,
            DaysUntilExpiry = daysUntilExpiry,
            Verification = BuildVerificationDto(document),
            Issues = BuildDocumentIssues(document),
            AvailableActions = GetDocumentActions(document),
            Metadata = document.Metadata,
            Tags = document.Tags.ToList()
        };
    }

    private DocumentSummaryDto BuildDocumentSummary(List<Domain.Entities.DriverDocument> documents, int expiryWarningDays)
    {
        var now = DateTime.UtcNow;
        var expiredCount = documents.Count(d => d.ExpiryDate <= now);
        var expiringCount = documents.Count(d => d.ExpiryDate > now && d.ExpiryDate <= now.AddDays(expiryWarningDays));
        var validCount = documents.Count(d => d.Status == DocumentStatus.Verified && d.ExpiryDate > now);
        var pendingCount = documents.Count(d => d.Status == DocumentStatus.Pending);
        var rejectedCount = documents.Count(d => d.Status == DocumentStatus.Rejected);

        var complianceScore = documents.Any() ? (double)validCount / documents.Count * 100 : 0;
        var complianceGrade = GetComplianceGrade(complianceScore);

        return new DocumentSummaryDto
        {
            TotalDocuments = documents.Count,
            ValidDocuments = validCount,
            ExpiredDocuments = expiredCount,
            ExpiringDocuments = expiringCount,
            PendingVerification = pendingCount,
            RejectedDocuments = rejectedCount,
            ComplianceScore = Math.Round(complianceScore, 1),
            ComplianceGrade = complianceGrade,
            LastDocumentUpload = documents.Any() ? documents.Max(d => d.UploadedAt) : DateTime.MinValue,
            NextExpiryDate = documents.Where(d => d.ExpiryDate > now).Min(d => d.ExpiryDate),
            CriticalIssues = GetCriticalIssues(documents)
        };
    }

    private async Task<DocumentExpiryAlertsDto> BuildExpiryAlerts(Guid driverId, List<Domain.Entities.DriverDocument> documents, int warningDays, CancellationToken cancellationToken)
    {
        var now = DateTime.UtcNow;
        var criticalAlerts = new List<ExpiryAlertDto>();
        var warningAlerts = new List<ExpiryAlertDto>();
        var infoAlerts = new List<ExpiryAlertDto>();

        foreach (var document in documents.Where(d => d.ExpiryDate.HasValue))
        {
            var daysUntilExpiry = (document.ExpiryDate.Value - now).Days;
            
            if (daysUntilExpiry <= 0)
            {
                criticalAlerts.Add(CreateExpiryAlert(document, "Critical", "Document has expired"));
            }
            else if (daysUntilExpiry <= 7)
            {
                warningAlerts.Add(CreateExpiryAlert(document, "Warning", $"Document expires in {daysUntilExpiry} days"));
            }
            else if (daysUntilExpiry <= warningDays)
            {
                infoAlerts.Add(CreateExpiryAlert(document, "Info", $"Document expires in {daysUntilExpiry} days"));
            }
        }

        return new DocumentExpiryAlertsDto
        {
            CriticalAlerts = criticalAlerts,
            WarningAlerts = warningAlerts,
            InfoAlerts = infoAlerts,
            NotificationSettings = await GetNotificationSettings(driverId, cancellationToken),
            NextScheduledAlert = CalculateNextScheduledAlert(documents)
        };
    }

    private async Task<List<DocumentActivityDto>> BuildRecentActivity(Guid driverId, CancellationToken cancellationToken)
    {
        // Mock implementation - would query document activity log
        return new List<DocumentActivityDto>
        {
            new DocumentActivityDto
            {
                ActivityId = Guid.NewGuid(),
                ActivityType = "Upload",
                ActivityDescription = "License document uploaded",
                ActivityDate = DateTime.UtcNow.AddDays(-1),
                PerformedBy = "Driver",
                DocumentName = "Driving License"
            },
            new DocumentActivityDto
            {
                ActivityId = Guid.NewGuid(),
                ActivityType = "Verify",
                ActivityDescription = "Insurance document verified",
                ActivityDate = DateTime.UtcNow.AddDays(-2),
                PerformedBy = "System",
                DocumentName = "Vehicle Insurance"
            }
        };
    }

    private List<DocumentRequirementDto> BuildUploadRequirements(List<Domain.Entities.DriverDocument> documents)
    {
        var requirements = new List<DocumentRequirementDto>();
        var requiredDocTypes = new[] { "License", "Insurance", "Registration", "PAN", "Aadhar" };
        var existingTypes = documents.Where(d => d.Status == DocumentStatus.Verified).Select(d => d.DocumentType.ToString()).ToList();

        foreach (var docType in requiredDocTypes)
        {
            if (!existingTypes.Contains(docType))
            {
                requirements.Add(new DocumentRequirementDto
                {
                    DocumentType = docType,
                    RequirementName = $"{docType} Document",
                    Description = $"Valid {docType} document is required",
                    IsRequired = true,
                    IsMissing = true,
                    Priority = "High",
                    AcceptedFormats = new List<string> { "PDF", "JPG", "PNG" },
                    MaxFileSize = 5 * 1024 * 1024, // 5MB
                    UploadInstructions = $"Please upload a clear, readable copy of your {docType}"
                });
            }
        }

        return requirements;
    }

    // Helper methods
    private string GetDocumentCategory(DocumentType documentType)
    {
        return documentType switch
        {
            DocumentType.License => "Identity & Licenses",
            DocumentType.Insurance => "Insurance Documents",
            DocumentType.Registration => "Vehicle Documents",
            DocumentType.PAN => "Tax Documents",
            DocumentType.Aadhar => "Identity Documents",
            _ => "Other Documents"
        };
    }

    private string GetCategoryDescription(string category)
    {
        return category switch
        {
            "Identity & Licenses" => "Driving licenses and identity verification documents",
            "Insurance Documents" => "Vehicle and personal insurance policies",
            "Vehicle Documents" => "Vehicle registration and related documents",
            "Tax Documents" => "Tax identification and related documents",
            "Identity Documents" => "Government issued identity documents",
            _ => "Miscellaneous documents"
        };
    }

    private bool IsRequiredCategory(string category)
    {
        return category != "Other Documents";
    }

    private string GetCategoryIcon(string category)
    {
        return category switch
        {
            "Identity & Licenses" => "id-card",
            "Insurance Documents" => "shield",
            "Vehicle Documents" => "car",
            "Tax Documents" => "receipt",
            "Identity Documents" => "user",
            _ => "document"
        };
    }

    private string FormatFileSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB" };
        double len = bytes;
        int order = 0;
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }
        return $"{len:0.##} {sizes[order]}";
    }

    private DocumentVerificationDto? BuildVerificationDto(Domain.Entities.DriverDocument document)
    {
        if (document.Status == DocumentStatus.Pending) return null;

        return new DocumentVerificationDto
        {
            VerificationId = Guid.NewGuid(),
            VerificationStatus = document.Status.ToString(),
            VerificationDate = document.VerifiedAt,
            VerifiedBy = document.VerifiedBy,
            VerificationMethod = "Automated",
            VerificationScore = document.Status == DocumentStatus.Verified ? 95.0 : 45.0,
            Checks = new List<VerificationCheckDto>
            {
                new VerificationCheckDto { CheckName = "Document Quality", Result = "Pass", Score = 98.0 },
                new VerificationCheckDto { CheckName = "Text Recognition", Result = "Pass", Score = 92.0 }
            }
        };
    }

    private List<DocumentIssueDto> BuildDocumentIssues(Domain.Entities.DriverDocument document)
    {
        var issues = new List<DocumentIssueDto>();
        
        if (document.Status == DocumentStatus.Rejected)
        {
            issues.Add(new DocumentIssueDto
            {
                IssueId = Guid.NewGuid(),
                IssueType = "Verification",
                Severity = "High",
                Title = "Document Rejected",
                Description = "Document did not pass verification",
                Status = "Open",
                ReportedAt = document.VerifiedAt ?? DateTime.UtcNow,
                SuggestedActions = new List<string> { "Upload a clearer image", "Ensure all text is readable" }
            });
        }

        if (document.ExpiryDate <= DateTime.UtcNow)
        {
            issues.Add(new DocumentIssueDto
            {
                IssueId = Guid.NewGuid(),
                IssueType = "Expiry",
                Severity = "Critical",
                Title = "Document Expired",
                Description = "This document has expired and needs renewal",
                Status = "Open",
                ReportedAt = DateTime.UtcNow,
                SuggestedActions = new List<string> { "Renew the document", "Upload updated version" }
            });
        }

        return issues;
    }

    private List<string> GetDocumentActions(Domain.Entities.DriverDocument document)
    {
        var actions = new List<string> { "View", "Download" };

        if (document.Status == DocumentStatus.Rejected)
        {
            actions.Add("Re-upload");
        }

        if (document.ExpiryDate <= DateTime.UtcNow.AddDays(30))
        {
            actions.Add("Renew");
        }

        return actions;
    }

    private List<string> GetAvailableActions(List<Domain.Entities.DriverDocument> documents)
    {
        var actions = new List<string> { "Upload Document", "View All", "Filter" };

        if (documents.Any(d => d.Status == DocumentStatus.Rejected))
        {
            actions.Add("Fix Issues");
        }

        if (documents.Any(d => d.ExpiryDate <= DateTime.UtcNow.AddDays(30)))
        {
            actions.Add("Renew Expiring");
        }

        return actions;
    }

    private string GetComplianceGrade(double score)
    {
        return score switch
        {
            >= 90 => "A",
            >= 80 => "B",
            >= 70 => "C",
            >= 60 => "D",
            _ => "F"
        };
    }

    private List<string> GetCriticalIssues(List<Domain.Entities.DriverDocument> documents)
    {
        var issues = new List<string>();
        
        var expiredCount = documents.Count(d => d.ExpiryDate <= DateTime.UtcNow);
        if (expiredCount > 0)
        {
            issues.Add($"{expiredCount} document(s) expired");
        }

        var rejectedCount = documents.Count(d => d.Status == DocumentStatus.Rejected);
        if (rejectedCount > 0)
        {
            issues.Add($"{rejectedCount} document(s) rejected");
        }

        return issues;
    }

    private ExpiryAlertDto CreateExpiryAlert(Domain.Entities.DriverDocument document, string alertType, string message)
    {
        return new ExpiryAlertDto
        {
            DocumentId = document.Id,
            DocumentType = document.DocumentType.ToString(),
            DocumentName = document.DocumentName,
            ExpiryDate = document.ExpiryDate,
            DaysUntilExpiry = document.ExpiryDate?.Subtract(DateTime.UtcNow).Days,
            AlertType = alertType,
            AlertMessage = message,
            ActionRequired = alertType == "Critical" ? "Immediate renewal required" : "Plan for renewal",
            AlertCreatedAt = DateTime.UtcNow,
            IsAcknowledged = false
        };
    }

    private async Task<ExpiryNotificationSettingsDto> GetNotificationSettings(Guid driverId, CancellationToken cancellationToken)
    {
        // Mock implementation - would query user preferences
        return new ExpiryNotificationSettingsDto
        {
            EmailNotificationsEnabled = true,
            SMSNotificationsEnabled = false,
            PushNotificationsEnabled = true,
            NotificationDays = new List<int> { 30, 15, 7, 1 },
            NotificationTime = "09:00"
        };
    }

    private DateTime CalculateNextScheduledAlert(List<Domain.Entities.DriverDocument> documents)
    {
        var nextExpiry = documents
            .Where(d => d.ExpiryDate > DateTime.UtcNow)
            .Min(d => d.ExpiryDate);

        return nextExpiry?.AddDays(-30) ?? DateTime.UtcNow.AddDays(30);
    }
}
