# Fix domain events and missing DTOs
Write-Host "Fixing domain events and missing DTOs..." -ForegroundColor Green

# Fix ContentManagement domain events to implement IDomainEvent properly
Write-Host "Fixing ContentManagement domain events..." -ForegroundColor Yellow
$contentEventFiles = @(
    "Services\ContentManagement\ContentManagement.Domain\Events\ContentEvents.cs",
    "Services\ContentManagement\ContentManagement.Domain\Events\FaqEvents.cs",
    "Services\ContentManagement\ContentManagement.Domain\Events\PolicyEvents.cs"
)

foreach ($eventFile in $contentEventFiles) {
    if (Test-Path $eventFile) {
        $content = Get-Content $eventFile -Raw
        $modified = $false
        
        # Add base implementation for IDomainEvent
        if ($content -match "public record \w+Event.*: IDomainEvent") {
            # Add Id and OccurredOn properties to records that implement IDomainEvent
            $content = $content -replace "(public record (\w+Event)[^{]*: IDomainEvent[^{]*\{)", "`$1`n    public Guid Id { get; init; } = Guid.NewGuid();`n    public DateTime OccurredOn { get; init; } = DateTime.UtcNow;"
            $modified = $true
        }
        
        if ($modified) {
            Set-Content -Path $eventFile -Value $content -Encoding UTF8
            Write-Host "  Fixed domain events in $(Split-Path $eventFile -Leaf)" -ForegroundColor Green
        }
    }
}

# Fix AuditCompliance domain events
Write-Host "Fixing AuditCompliance domain events..." -ForegroundColor Yellow
$auditEventFile = "Services\AuditCompliance\AuditCompliance.Domain\Events\TenantEvents.cs"
if (Test-Path $auditEventFile) {
    $content = Get-Content $auditEventFile -Raw
    
    # Add base implementation for IDomainEvent
    if ($content -match "public record \w+Event.*: IDomainEvent") {
        $content = $content -replace "(public record (\w+Event)[^{]*: IDomainEvent[^{]*\{)", "`$1`n    public Guid Id { get; init; } = Guid.NewGuid();`n    public DateTime OccurredOn { get; init; } = DateTime.UtcNow;"
        
        Set-Content -Path $auditEventFile -Value $content -Encoding UTF8
        Write-Host "  Fixed domain events in TenantEvents.cs" -ForegroundColor Green
    }
}

# Create missing DTOs for MonitoringObservability
Write-Host "Creating missing DTOs for MonitoringObservability..." -ForegroundColor Yellow
$monitoringDtosDir = "Services\MonitoringObservability\MonitoringObservability.Application\DTOs"
if (-not (Test-Path $monitoringDtosDir)) {
    New-Item -ItemType Directory -Path $monitoringDtosDir -Force | Out-Null
    Write-Host "  Created DTOs directory" -ForegroundColor Green
}

# Create missing DTO files
$missingDtos = @{
    "ServiceDependencyGraphDto.cs" = @"
using System;
using System.Collections.Generic;

namespace MonitoringObservability.Application.DTOs
{
    public class ServiceDependencyGraphDto
    {
        public string ServiceName { get; set; } = string.Empty;
        public List<ServiceDependencyDto> Dependencies { get; set; } = new();
    }
}
"@
    "ServiceImpactAnalysisDto.cs" = @"
using System;

namespace MonitoringObservability.Application.DTOs
{
    public class ServiceImpactAnalysisDto
    {
        public string ServiceName { get; set; } = string.Empty;
        public string ImpactLevel { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
    }
}
"@
    "ServiceTopologyDto.cs" = @"
using System;

namespace MonitoringObservability.Application.DTOs
{
    public class ServiceTopologyDto
    {
        public string ServiceName { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
    }
}
"@
    "ServiceDependencyDto.cs" = @"
using System;

namespace MonitoringObservability.Application.DTOs
{
    public class ServiceDependencyDto
    {
        public string ServiceName { get; set; } = string.Empty;
        public string DependencyType { get; set; } = string.Empty;
    }
}
"@
    "AnomalyStatisticsDto.cs" = @"
using System;

namespace MonitoringObservability.Application.DTOs
{
    public class AnomalyStatisticsDto
    {
        public int TotalAnomalies { get; set; }
        public int CriticalAnomalies { get; set; }
        public DateTime LastUpdated { get; set; }
    }
}
"@
    "CapacityAnalysisDto.cs" = @"
using System;

namespace MonitoringObservability.Application.DTOs
{
    public class CapacityAnalysisDto
    {
        public string ResourceName { get; set; } = string.Empty;
        public double CurrentUsage { get; set; }
        public double MaxCapacity { get; set; }
    }
}
"@
    "SLAComplianceDto.cs" = @"
using System;

namespace MonitoringObservability.Application.DTOs
{
    public class SLAComplianceDto
    {
        public string ServiceName { get; set; } = string.Empty;
        public double CompliancePercentage { get; set; }
        public DateTime LastChecked { get; set; }
    }
}
"@
    "CapacityDashboardDto.cs" = @"
using System;
using System.Collections.Generic;

namespace MonitoringObservability.Application.DTOs
{
    public class CapacityDashboardDto
    {
        public List<CapacityAnalysisDto> CapacityMetrics { get; set; } = new();
        public DateTime LastUpdated { get; set; }
    }
}
"@
    "SLADashboardDto.cs" = @"
using System;
using System.Collections.Generic;

namespace MonitoringObservability.Application.DTOs
{
    public class SLADashboardDto
    {
        public List<SLAComplianceDto> SLAMetrics { get; set; } = new();
        public DateTime LastUpdated { get; set; }
    }
}
"@
    "DashboardExportDto.cs" = @"
using System;

namespace MonitoringObservability.Application.DTOs
{
    public class DashboardExportDto
    {
        public string ExportId { get; set; } = string.Empty;
        public string Format { get; set; } = string.Empty;
        public byte[] Data { get; set; } = Array.Empty<byte>();
    }
}
"@
    "DashboardImportDto.cs" = @"
using System;

namespace MonitoringObservability.Application.DTOs
{
    public class DashboardImportDto
    {
        public string ImportId { get; set; } = string.Empty;
        public byte[] Data { get; set; } = Array.Empty<byte>();
        public string Format { get; set; } = string.Empty;
    }
}
"@
    "DashboardAnalyticsDto.cs" = @"
using System;

namespace MonitoringObservability.Application.DTOs
{
    public class DashboardAnalyticsDto
    {
        public string DashboardId { get; set; } = string.Empty;
        public int ViewCount { get; set; }
        public DateTime LastViewed { get; set; }
    }
}
"@
}

foreach ($dtoFile in $missingDtos.Keys) {
    $dtoPath = Join-Path $monitoringDtosDir $dtoFile
    if (-not (Test-Path $dtoPath)) {
        Set-Content -Path $dtoPath -Value $missingDtos[$dtoFile] -Encoding UTF8
        Write-Host "  Created $dtoFile" -ForegroundColor Green
    }
}

# Create ExportFormat enum
$exportFormatFile = "$monitoringDtosDir\ExportFormat.cs"
if (-not (Test-Path $exportFormatFile)) {
    $exportFormatContent = @"
namespace MonitoringObservability.Application.DTOs
{
    public enum ExportFormat
    {
        Json = 1,
        Xml = 2,
        Csv = 3,
        Excel = 4
    }
}
"@
    Set-Content -Path $exportFormatFile -Value $exportFormatContent -Encoding UTF8
    Write-Host "  Created ExportFormat.cs" -ForegroundColor Green
}

Write-Host "Domain events and DTOs fix completed!" -ForegroundColor Green
