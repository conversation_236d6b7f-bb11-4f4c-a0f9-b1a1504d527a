using Microsoft.AspNetCore.Http;

namespace SubscriptionManagement.Application.Interfaces
{
    public interface IPaymentProofFileService
    {
        /// <summary>
        /// Upload a payment proof file
        /// </summary>
        /// <param name="file">The file to upload</param>
        /// <param name="subscriptionId">The subscription ID</param>
        /// <param name="userId">The user ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The URL of the uploaded file</returns>
        Task<string> UploadPaymentProofAsync(
            IFormFile file, 
            Guid subscriptionId, 
            Guid userId, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get a payment proof file stream
        /// </summary>
        /// <param name="fileUrl">The file URL</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>File stream</returns>
        Task<Stream> GetPaymentProofAsync(string fileUrl, CancellationToken cancellationToken = default);

        /// <summary>
        /// Delete a payment proof file
        /// </summary>
        /// <param name="fileUrl">The file URL to delete</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if deleted successfully</returns>
        Task<bool> DeletePaymentProofAsync(string fileUrl, CancellationToken cancellationToken = default);

        /// <summary>
        /// Check if a payment proof file exists
        /// </summary>
        /// <param name="fileUrl">The file URL</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if file exists</returns>
        Task<bool> FileExistsAsync(string fileUrl, CancellationToken cancellationToken = default);

        /// <summary>
        /// Get the size of a payment proof file
        /// </summary>
        /// <param name="fileUrl">The file URL</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>File size in bytes</returns>
        Task<long> GetFileSizeAsync(string fileUrl, CancellationToken cancellationToken = default);

        /// <summary>
        /// Validate if the file type is allowed
        /// </summary>
        /// <param name="contentType">The content type to validate</param>
        /// <returns>True if valid</returns>
        bool IsValidFileType(string contentType);

        /// <summary>
        /// Validate if the file size is within limits
        /// </summary>
        /// <param name="fileSizeBytes">The file size in bytes</param>
        /// <returns>True if valid</returns>
        bool IsValidFileSize(long fileSizeBytes);
    }
}
