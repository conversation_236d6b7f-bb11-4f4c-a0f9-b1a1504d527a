using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using NetworkFleetManagement.Domain.Repositories;
using Shared.Domain.Common;

namespace NetworkFleetManagement.Application.Queries.Drivers.GetEnhancedDriverProfile;

public class GetEnhancedDriverProfileQueryHandler : IRequestHandler<GetEnhancedDriverProfileQuery, GetEnhancedDriverProfileResponse>
{
    private readonly NetworkFleetDbContext _context;
    private readonly IMapper _mapper;
    private readonly ILogger<GetEnhancedDriverProfileQueryHandler> _logger;

    public GetEnhancedDriverProfileQueryHandler(
        NetworkFleetDbContext context,
        IMapper mapper,
        ILogger<GetEnhancedDriverProfileQueryHandler> logger)
    {
        _context = context;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<GetEnhancedDriverProfileResponse> Handle(GetEnhancedDriverProfileQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Getting enhanced driver profile for driver {DriverId}", request.DriverId);

            // Get driver with all related data
            var driver = await _context.Drivers
                .Include(d => d.Carrier)
                .Include(d => d.Documents)
                .Include(d => d.VehicleAssignments.Where(va => va.IsActive))
                    .ThenInclude(va => va.Vehicle)
                .FirstOrDefaultAsync(d => d.Id == request.DriverId, cancellationToken);

            if (driver == null)
            {
                return new GetEnhancedDriverProfileResponse
                {
                    IsSuccess = false,
                    ErrorMessage = "Driver not found"
                };
            }

            // Build enhanced profile
            var profile = new EnhancedDriverProfileDto
            {
                // Basic Information
                Id = driver.Id,
                UserId = driver.UserId,
                FirstName = driver.FirstName,
                LastName = driver.LastName,
                FullName = driver.FullName,
                PhoneNumber = driver.PhoneNumber,
                Email = driver.Email,
                ProfilePhotoUrl = driver.ProfilePhotoUrl,

                // License Information
                LicenseNumber = driver.LicenseNumber,
                LicenseExpiryDate = driver.LicenseExpiryDate,
                IsLicenseValid = driver.IsLicenseValid,
                LicenseExpiryDays = (int)(driver.LicenseExpiryDate - DateTime.UtcNow).TotalDays,

                // Identity Documents
                AadharNumber = driver.AadharNumber,
                PANNumber = driver.PANNumber,

                // Status Information
                Status = driver.Status.ToString(),
                OnboardingStatus = driver.OnboardingStatus.ToString(),
                IsVerified = driver.IsVerified,
                VerifiedAt = driver.VerifiedAt,
                IsAvailable = driver.IsAvailable,
                LastActiveAt = driver.LastActiveAt,

                // Operational Information
                PreferredRoutes = driver.PreferredRoutes.ToList(),
                OperationalAreas = driver.OperationalAreas.ToList(),
                Notes = driver.Notes,

                // Timestamps
                CreatedAt = driver.CreatedAt,
                UpdatedAt = driver.UpdatedAt
            };

            // Build entity assignment information
            if (request.IncludeEntityDetails)
            {
                profile.EntityAssignment = await BuildEntityAssignmentInfo(driver, cancellationToken);
            }

            // Build performance metrics
            if (request.IncludePerformanceMetrics)
            {
                profile.PerformanceMetrics = BuildPerformanceMetrics(driver);
            }

            // Build document status
            if (request.IncludeDocumentStatus)
            {
                profile.DocumentStatus = BuildDocumentStatus(driver);
            }

            // Build vehicle assignments
            if (request.IncludeVehicleAssignments)
            {
                profile.VehicleAssignments = BuildVehicleAssignments(driver);
            }

            // Build current location
            profile.CurrentLocation = BuildCurrentLocationInfo(driver);

            // Build preferences
            profile.Preferences = BuildDriverPreferences(driver);

            // Get additional data if requested
            if (request.IncludeTripHistory)
            {
                profile.RecentTrips = await GetRecentTrips(request.DriverId, request.HistoryDays, cancellationToken);
                profile.CurrentTrip = await GetCurrentTrip(request.DriverId, cancellationToken);
            }

            if (request.IncludeLocationHistory)
            {
                profile.LocationHistory = await GetLocationHistory(request.DriverId, request.HistoryDays, cancellationToken);
            }

            return new GetEnhancedDriverProfileResponse
            {
                IsSuccess = true,
                Profile = profile
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting enhanced driver profile for driver {DriverId}", request.DriverId);
            return new GetEnhancedDriverProfileResponse
            {
                IsSuccess = false,
                ErrorMessage = ex.Message
            };
        }
    }

    private async Task<EntityAssignmentInfo> BuildEntityAssignmentInfo(Domain.Entities.Driver driver, CancellationToken cancellationToken)
    {
        var entityInfo = new EntityAssignmentInfo
        {
            CarrierId = driver.CarrierId,
            CarrierName = driver.Carrier.CompanyName,
            CarrierCompanyName = driver.Carrier.CompanyName,
            CarrierType = driver.Carrier.CarrierType.ToString(),
            CarrierStatus = driver.Carrier.Status.ToString(),
            AssignedAt = driver.CreatedAt,
            IsActiveAssignment = driver.Status != Domain.Enums.DriverStatus.Inactive,

            CarrierContact = new ContactInfo
            {
                PrimaryPhone = driver.Carrier.PrimaryContactPhone,
                Email = driver.Carrier.PrimaryContactEmail,
                Address = driver.Carrier.Address,
                City = driver.Carrier.City,
                State = driver.Carrier.State,
                Country = driver.Carrier.Country,
                PostalCode = driver.Carrier.PostalCode
            }
        };

        // Build entity hierarchy
        entityInfo.EntityHierarchy = new List<EntityHierarchyLevel>
        {
            new EntityHierarchyLevel
            {
                Level = "Company",
                Name = driver.Carrier.CompanyName,
                Manager = driver.Carrier.PrimaryContactName,
                Contact = driver.Carrier.PrimaryContactPhone
            }
        };

        // Add fleet information if available (mock for now)
        if (driver.VehicleAssignments.Any())
        {
            entityInfo.FleetId = Guid.NewGuid(); // Mock fleet ID
            entityInfo.FleetName = "Fleet A";
            entityInfo.FleetManager = "Fleet Manager";
            entityInfo.FleetContact = new ContactInfo
            {
                PrimaryPhone = "******-0123",
                Email = "<EMAIL>"
            };

            entityInfo.EntityHierarchy.Add(new EntityHierarchyLevel
            {
                Level = "Fleet",
                Name = "Fleet A",
                Manager = "Fleet Manager",
                Contact = "******-0123"
            });
        }

        return entityInfo;
    }

    private DriverPerformanceMetricsDto BuildPerformanceMetrics(Domain.Entities.Driver driver)
    {
        var metrics = driver.PerformanceMetrics;

        return new DriverPerformanceMetricsDto
        {
            AverageRating = metrics.AverageRating,
            TotalTrips = metrics.TotalTrips,
            CompletedTrips = metrics.CompletedTrips,
            CancelledTrips = metrics.CancelledTrips,
            OnTimePerformance = metrics.OnTimePerformance,
            SafetyScore = metrics.SafetyScore,
            FuelEfficiency = metrics.FuelEfficiency,
            TotalEarnings = metrics.TotalEarnings,
            LastUpdated = metrics.LastUpdated,
            PerformanceGrade = CalculatePerformanceGrade(metrics),
            Alerts = GeneratePerformanceAlerts(metrics)
        };
    }

    private string CalculatePerformanceGrade(Domain.ValueObjects.PerformanceMetrics metrics)
    {
        var score = (metrics.AverageRating * 0.3) +
                   (metrics.OnTimePerformance * 0.3) +
                   (metrics.SafetyScore * 0.4);

        return score switch
        {
            >= 4.5 => "A",
            >= 4.0 => "B",
            >= 3.5 => "C",
            >= 3.0 => "D",
            _ => "F"
        };
    }

    private List<PerformanceAlert> GeneratePerformanceAlerts(Domain.ValueObjects.PerformanceMetrics metrics)
    {
        var alerts = new List<PerformanceAlert>();

        if (metrics.AverageRating < 3.5)
        {
            alerts.Add(new PerformanceAlert
            {
                Type = "Rating",
                Message = "Average rating is below acceptable threshold",
                Severity = "Warning",
                CreatedAt = DateTime.UtcNow
            });
        }

        if (metrics.OnTimePerformance < 80)
        {
            alerts.Add(new PerformanceAlert
            {
                Type = "OnTime",
                Message = "On-time performance needs improvement",
                Severity = "Warning",
                CreatedAt = DateTime.UtcNow
            });
        }

        if (metrics.SafetyScore < 85)
        {
            alerts.Add(new PerformanceAlert
            {
                Type = "Safety",
                Message = "Safety score requires attention",
                Severity = "High",
                CreatedAt = DateTime.UtcNow
            });
        }

        return alerts;
    }

    private DocumentStatusSummary BuildDocumentStatus(Domain.Entities.Driver driver)
    {
        var documents = driver.Documents.ToList();
        var now = DateTime.UtcNow;

        return new DocumentStatusSummary
        {
            TotalDocuments = documents.Count,
            ValidDocuments = documents.Count(d => d.ExpiryDate > now),
            ExpiredDocuments = documents.Count(d => d.ExpiryDate <= now),
            ExpiringDocuments = documents.Count(d => d.ExpiryDate > now && d.ExpiryDate <= now.AddDays(30)),
            Documents = documents.Select(d => new DocumentStatusDto
            {
                Id = d.Id,
                DocumentType = d.DocumentType.ToString(),
                Status = d.Status.ToString(),
                ExpiryDate = d.ExpiryDate,
                DaysUntilExpiry = d.ExpiryDate.HasValue ? (int)(d.ExpiryDate.Value - now).TotalDays : null,
                IsExpired = d.ExpiryDate <= now,
                IsExpiringSoon = d.ExpiryDate > now && d.ExpiryDate <= now.AddDays(30),
                DocumentUrl = d.DocumentUrl
            }).ToList(),
            LastUpdated = DateTime.UtcNow
        };
    }

    private List<VehicleAssignmentDto> BuildVehicleAssignments(Domain.Entities.Driver driver)
    {
        return driver.VehicleAssignments.Select(va => new VehicleAssignmentDto
        {
            VehicleId = va.VehicleId,
            VehicleNumber = va.Vehicle.VehicleNumber,
            Make = va.Vehicle.Make,
            Model = va.Vehicle.Model,
            VehicleType = va.Vehicle.VehicleType.ToString(),
            IsActive = va.IsActive,
            AssignedAt = va.AssignedAt,
            UnassignedAt = va.UnassignedAt,
            AssignmentNotes = va.Notes
        }).ToList();
    }

    private CurrentLocationInfo? BuildCurrentLocationInfo(Domain.Entities.Driver driver)
    {
        if (driver.CurrentLocation == null) return null;

        return new CurrentLocationInfo
        {
            Latitude = driver.CurrentLocation.Latitude,
            Longitude = driver.CurrentLocation.Longitude,
            Address = driver.CurrentLocation.Address,
            Timestamp = driver.CurrentLocation.Timestamp,
            Accuracy = driver.CurrentLocation.Accuracy,
            IsLocationSharingEnabled = true, // Would come from preferences
            TimeSinceLastUpdate = DateTime.UtcNow - driver.CurrentLocation.Timestamp
        };
    }

    private DriverPreferencesDto BuildDriverPreferences(Domain.Entities.Driver driver)
    {
        // Mock preferences - in real implementation, this would come from a preferences service
        return new DriverPreferencesDto
        {
            PreferredLanguage = "en",
            TimeZone = "UTC",
            LocationSharingEnabled = true,
            NotificationsEnabled = true,
            CustomSettings = new Dictionary<string, object>()
        };
    }

    private async Task<List<RecentTripDto>> GetRecentTrips(Guid driverId, int days, CancellationToken cancellationToken)
    {
        // Mock implementation - would integrate with TripManagement service
        return new List<RecentTripDto>();
    }

    private async Task<CurrentTripInfo?> GetCurrentTrip(Guid driverId, CancellationToken cancellationToken)
    {
        // Mock implementation - would integrate with TripManagement service
        return null;
    }

    private async Task<List<LocationHistoryDto>> GetLocationHistory(Guid driverId, int days, CancellationToken cancellationToken)
    {
        // Mock implementation - would integrate with location tracking service
        return new List<LocationHistoryDto>();
    }
}

