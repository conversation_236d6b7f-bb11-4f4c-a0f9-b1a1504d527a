using System.ComponentModel.DataAnnotations;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Application.DTOs;

public class CreateMoneyDto
{
    [Required]
    [Range(0.01, double.MaxValue, ErrorMessage = "Amount must be greater than zero")]
    public decimal Amount { get; set; }

    [Required]
    [StringLength(3, MinimumLength = 3, ErrorMessage = "Currency must be a 3-character code")]
    public string Currency { get; set; } = string.Empty;

    public Money ToMoney() => new(Amount, Currency);
}
