namespace ContentManagement.Domain.Enums;

/// <summary>
/// Types of content in the CMS
/// </summary>
public enum ContentType
{
    General = 1,
    Faq = 2,
    Policy = 3,
    StaticPage = 4,
    NewsArticle = 5,
    Announcement = 6
}

/// <summary>
/// Content status in the approval workflow
/// </summary>
public enum ContentStatus
{
    Draft = 1,
    PendingReview = 2,
    Approved = 3,
    Published = 4,
    Archived = 5,
    Rejected = 6
}

/// <summary>
/// Visibility levels for content
/// </summary>
public enum VisibilityLevel
{
    Public = 1,
    Authenticated = 2,
    RoleSpecific = 3,
    Private = 4
}

/// <summary>
/// FAQ categories for organization
/// </summary>
public enum FaqCategory
{
    General = 1,
    Account = 2,
    Billing = 3,
    Technical = 4,
    Shipping = 5,
    Orders = 6,
    Payments = 7,
    Support = 8,
    Legal = 9,
    Privacy = 10
}

/// <summary>
/// Types of policy documents
/// </summary>
public enum PolicyType
{
    TermsOfService = 1,
    PrivacyPolicy = 2,
    CookiePolicy = 3,
    DataProtection = 4,
    UserAgreement = 5,
    ServiceLevelAgreement = 6,
    AcceptableUsePolicy = 7,
    SecurityPolicy = 8,
    CompliancePolicy = 9,
    RefundPolicy = 10
}

/// <summary>
/// Types of content attachments
/// </summary>
public enum AttachmentType
{
    Image = 1,
    Document = 2,
    Video = 3,
    Audio = 4,
    Archive = 5,
    Other = 6
}

/// <summary>
/// Recurrence patterns for scheduled publishing
/// </summary>
public enum RecurrencePattern
{
    None = 0,
    Daily = 1,
    Weekly = 2,
    Monthly = 3,
    Quarterly = 4,
    Yearly = 5
}
