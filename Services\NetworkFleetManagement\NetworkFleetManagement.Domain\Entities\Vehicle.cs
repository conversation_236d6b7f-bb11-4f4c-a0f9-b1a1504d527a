using NetworkFleetManagement.Domain.Enums;
using NetworkFleetManagement.Domain.ValueObjects;
using Shared.Domain.Common;

namespace NetworkFleetManagement.Domain.Entities;

public class Vehicle : BaseEntity
{
    public Guid CarrierId { get; private set; }
    public string RegistrationNumber { get; private set; } = string.Empty;
    public VehicleType VehicleType { get; private set; }
    public string Make { get; private set; } = string.Empty;
    public string Model { get; private set; } = string.Empty;
    public int Year { get; private set; }
    public string Color { get; private set; } = string.Empty;
    public VehicleSpecifications Specifications { get; private set; }
    public VehicleStatus Status { get; private set; }
    public Location? CurrentLocation { get; private set; }
    public DateTime? LastMaintenanceDate { get; private set; }
    public DateTime? NextMaintenanceDate { get; private set; }
    public string? InsuranceNumber { get; private set; }
    public DateTime? InsuranceExpiryDate { get; private set; }
    public string? FitnessNumber { get; private set; }
    public DateTime? FitnessExpiryDate { get; private set; }
    public string? PermitNumber { get; private set; }
    public DateTime? PermitExpiryDate { get; private set; }
    public string? Notes { get; private set; }
    public decimal? DailyEarnings { get; private set; }
    public decimal? MonthlyEarnings { get; private set; }
    public int? UtilizationHours { get; private set; }

    // Navigation properties
    public Carrier Carrier { get; private set; } = null!;
    
    private readonly List<VehicleDocument> _documents = new();
    public IReadOnlyCollection<VehicleDocument> Documents => _documents.AsReadOnly();

    private readonly List<VehicleMaintenanceRecord> _maintenanceRecords = new();
    public IReadOnlyCollection<VehicleMaintenanceRecord> MaintenanceRecords => _maintenanceRecords.AsReadOnly();

    private readonly List<DriverVehicleAssignment> _driverAssignments = new();
    public IReadOnlyCollection<DriverVehicleAssignment> DriverAssignments => _driverAssignments.AsReadOnly();

    // Parameterless constructor for EF Core
    private Vehicle() 
    {
        Specifications = new VehicleSpecifications(1000, 10);
    }

    public Vehicle(
        Guid carrierId,
        string registrationNumber,
        VehicleType vehicleType,
        string make,
        string model,
        int year,
        string color,
        VehicleSpecifications specifications,
        string? insuranceNumber = null,
        DateTime? insuranceExpiryDate = null,
        string? fitnessNumber = null,
        DateTime? fitnessExpiryDate = null,
        string? permitNumber = null,
        DateTime? permitExpiryDate = null,
        string? notes = null)
    {
        if (string.IsNullOrWhiteSpace(registrationNumber))
            throw new ArgumentException("Registration number cannot be empty", nameof(registrationNumber));
        
        if (string.IsNullOrWhiteSpace(make))
            throw new ArgumentException("Make cannot be empty", nameof(make));
        
        if (string.IsNullOrWhiteSpace(model))
            throw new ArgumentException("Model cannot be empty", nameof(model));
        
        if (year < 1900 || year > DateTime.UtcNow.Year + 1)
            throw new ArgumentException("Invalid year", nameof(year));

        CarrierId = carrierId;
        RegistrationNumber = registrationNumber;
        VehicleType = vehicleType;
        Make = make;
        Model = model;
        Year = year;
        Color = color;
        Specifications = specifications ?? throw new ArgumentNullException(nameof(specifications));
        Status = VehicleStatus.Available;
        InsuranceNumber = insuranceNumber;
        InsuranceExpiryDate = insuranceExpiryDate;
        FitnessNumber = fitnessNumber;
        FitnessExpiryDate = fitnessExpiryDate;
        PermitNumber = permitNumber;
        PermitExpiryDate = permitExpiryDate;
        Notes = notes;
        DailyEarnings = 0;
        MonthlyEarnings = 0;
        UtilizationHours = 0;
    }

    public string DisplayName => $"{Make} {Model} ({RegistrationNumber})";

    public bool IsInsuranceValid => !InsuranceExpiryDate.HasValue || InsuranceExpiryDate.Value > DateTime.UtcNow;
    public bool IsFitnessValid => !FitnessExpiryDate.HasValue || FitnessExpiryDate.Value > DateTime.UtcNow;
    public bool IsPermitValid => !PermitExpiryDate.HasValue || PermitExpiryDate.Value > DateTime.UtcNow;
    public bool IsAvailable => Status == VehicleStatus.Available && IsInsuranceValid && IsFitnessValid && IsPermitValid;

    public void UpdateStatus(VehicleStatus newStatus)
    {
        var oldStatus = Status;
        Status = newStatus;
        SetUpdatedAt();
        
        // Add domain event for status change
        AddDomainEvent(new VehicleStatusChangedEvent(Id, CarrierId, oldStatus, newStatus));
    }

    public void UpdateLocation(Location location)
    {
        CurrentLocation = location;
        SetUpdatedAt();
    }

    public void ScheduleMaintenance(DateTime scheduledDate, string? notes = null)
    {
        NextMaintenanceDate = scheduledDate;
        SetUpdatedAt();
        
        AddDomainEvent(new VehicleMaintenanceScheduledEvent(Id, CarrierId, scheduledDate, notes));
    }

    public void CompleteMaintenance(DateTime completedDate, string? notes = null, decimal? cost = null)
    {
        LastMaintenanceDate = completedDate;
        NextMaintenanceDate = null;
        
        var maintenanceRecord = new VehicleMaintenanceRecord(
            Id, completedDate, notes, cost);
        _maintenanceRecords.Add(maintenanceRecord);
        
        SetUpdatedAt();
        
        AddDomainEvent(new VehicleMaintenanceCompletedEvent(Id, CarrierId, completedDate, notes, cost));
    }

    public void UpdateEarnings(decimal dailyEarnings, decimal monthlyEarnings)
    {
        DailyEarnings = dailyEarnings;
        MonthlyEarnings = monthlyEarnings;
        SetUpdatedAt();
    }

    public void UpdateUtilization(int hours)
    {
        UtilizationHours = hours;
        SetUpdatedAt();
    }

    public void AddDocument(VehicleDocument document)
    {
        if (document.VehicleId != Id)
            throw new InvalidOperationException("Document must belong to this vehicle");
        
        _documents.Add(document);
        SetUpdatedAt();
    }

    public bool RequiresMaintenanceSoon(int daysThreshold = 7)
    {
        return NextMaintenanceDate.HasValue && 
               NextMaintenanceDate.Value <= DateTime.UtcNow.AddDays(daysThreshold);
    }

    public bool HasExpiredDocuments()
    {
        return !IsInsuranceValid || !IsFitnessValid || !IsPermitValid;
    }

    private void AddDomainEvent(IDomainEvent domainEvent)
    {
        // Implementation would be added when integrating with shared domain events
    }
}

// Domain Events
public record VehicleStatusChangedEvent(Guid VehicleId, Guid CarrierId, VehicleStatus OldStatus, VehicleStatus NewStatus) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}
public record VehicleMaintenanceScheduledEvent(Guid VehicleId, Guid CarrierId, DateTime ScheduledDate, string? Notes) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}
public record VehicleMaintenanceCompletedEvent(Guid VehicleId, Guid CarrierId, DateTime CompletedDate, string? Notes, decimal? Cost) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}
