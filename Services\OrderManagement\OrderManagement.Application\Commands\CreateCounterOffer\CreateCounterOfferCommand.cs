using MediatR;
using OrderManagement.Application.DTOs;

namespace OrderManagement.Application.Commands.CreateCounterOffer;

public class CreateCounterOfferCommand : IRequest<Guid>
{
    public Guid NegotiationId { get; set; }
    public Guid BidId { get; set; }
    public OrderManagement.Domain.Entities.CounterOfferType OfferType { get; set; }
    public MoneyDto OfferedPrice { get; set; } = new();
    public string? OfferedTerms { get; set; }
    public DateTime? OfferedPickupDate { get; set; }
    public DateTime? OfferedDeliveryDate { get; set; }
    public string? AdditionalServices { get; set; }
    public string? CounterOfferReason { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public Guid RequestingUserId { get; set; }
}
