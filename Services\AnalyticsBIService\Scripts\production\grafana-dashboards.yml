# Grafana dashboards configuration for Analytics & BI Service

apiVersion: 1

providers:
  # Analytics & BI Service dashboards
  - name: 'analytics-dashboards'
    orgId: 1
    folder: 'Analytics & BI Service'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards/analytics

  # Infrastructure dashboards
  - name: 'infrastructure-dashboards'
    orgId: 1
    folder: 'Infrastructure'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards/infrastructure

  # Application dashboards
  - name: 'application-dashboards'
    orgId: 1
    folder: 'Application Metrics'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards/application

  # Business dashboards
  - name: 'business-dashboards'
    orgId: 1
    folder: 'Business Intelligence'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards/business
