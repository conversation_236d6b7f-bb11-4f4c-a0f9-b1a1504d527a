using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Diagnostics.Metrics;

namespace NetworkFleetManagement.Infrastructure.Monitoring;

public interface IPerformanceMonitoringService
{
    void RecordQueryDuration(string queryName, TimeSpan duration);
    void RecordCacheHit(string cacheKey);
    void RecordCacheMiss(string cacheKey);
    void RecordApiRequest(string endpoint, int statusCode, TimeSpan duration);
    void RecordDatabaseConnection(bool successful);
    void RecordMessageBusEvent(string eventType, bool successful);
    Task<PerformanceReport> GenerateReportAsync(TimeSpan period);
    void IncrementCounter(string counterName, string[]? tags = null);
    void RecordHistogram(string histogramName, double value, string[]? tags = null);
}

public class PerformanceMonitoringService : IPerformanceMonitoringService
{
    private readonly ILogger<PerformanceMonitoringService> _logger;
    private readonly Meter _meter;
    private readonly ConcurrentDictionary<string, PerformanceMetric> _metrics;
    
    // Counters
    private readonly Counter<long> _queryCounter;
    private readonly Counter<long> _cacheHitCounter;
    private readonly Counter<long> _cacheMissCounter;
    private readonly Counter<long> _apiRequestCounter;
    private readonly Counter<long> _databaseConnectionCounter;
    private readonly Counter<long> _messageBusEventCounter;
    
    // Histograms
    private readonly Histogram<double> _queryDurationHistogram;
    private readonly Histogram<double> _apiRequestDurationHistogram;

    public PerformanceMonitoringService(ILogger<PerformanceMonitoringService> logger)
    {
        _logger = logger;
        _meter = new Meter("NetworkFleetManagement.Performance");
        _metrics = new ConcurrentDictionary<string, PerformanceMetric>();

        // Initialize counters
        _queryCounter = _meter.CreateCounter<long>("queries_total", "count", "Total number of database queries");
        _cacheHitCounter = _meter.CreateCounter<long>("cache_hits_total", "count", "Total number of cache hits");
        _cacheMissCounter = _meter.CreateCounter<long>("cache_misses_total", "count", "Total number of cache misses");
        _apiRequestCounter = _meter.CreateCounter<long>("api_requests_total", "count", "Total number of API requests");
        _databaseConnectionCounter = _meter.CreateCounter<long>("database_connections_total", "count", "Total number of database connections");
        _messageBusEventCounter = _meter.CreateCounter<long>("messagebus_events_total", "count", "Total number of message bus events");

        // Initialize histograms
        _queryDurationHistogram = _meter.CreateHistogram<double>("query_duration_ms", "milliseconds", "Database query duration");
        _apiRequestDurationHistogram = _meter.CreateHistogram<double>("api_request_duration_ms", "milliseconds", "API request duration");
    }

    public void RecordQueryDuration(string queryName, TimeSpan duration)
    {
        var durationMs = duration.TotalMilliseconds;
        
        _queryCounter.Add(1, new KeyValuePair<string, object?>("query_name", queryName));
        _queryDurationHistogram.Record(durationMs, new KeyValuePair<string, object?>("query_name", queryName));

        var metric = _metrics.AddOrUpdate($"query_{queryName}", 
            new PerformanceMetric { Name = queryName, Type = "Query" },
            (key, existing) => existing);

        metric.RecordDuration(duration);

        if (durationMs > 1000) // Log slow queries
        {
            _logger.LogWarning("Slow query detected: {QueryName} took {DurationMs}ms", queryName, durationMs);
        }
    }

    public void RecordCacheHit(string cacheKey)
    {
        _cacheHitCounter.Add(1, new KeyValuePair<string, object?>("cache_key", cacheKey));
        
        var metric = _metrics.AddOrUpdate($"cache_{cacheKey}", 
            new PerformanceMetric { Name = cacheKey, Type = "Cache" },
            (key, existing) => existing);

        metric.RecordCacheHit();
    }

    public void RecordCacheMiss(string cacheKey)
    {
        _cacheMissCounter.Add(1, new KeyValuePair<string, object?>("cache_key", cacheKey));
        
        var metric = _metrics.AddOrUpdate($"cache_{cacheKey}", 
            new PerformanceMetric { Name = cacheKey, Type = "Cache" },
            (key, existing) => existing);

        metric.RecordCacheMiss();
    }

    public void RecordApiRequest(string endpoint, int statusCode, TimeSpan duration)
    {
        var durationMs = duration.TotalMilliseconds;
        var tags = new[]
        {
            new KeyValuePair<string, object?>("endpoint", endpoint),
            new KeyValuePair<string, object?>("status_code", statusCode)
        };

        _apiRequestCounter.Add(1, tags);
        _apiRequestDurationHistogram.Record(durationMs, tags);

        var metric = _metrics.AddOrUpdate($"api_{endpoint}", 
            new PerformanceMetric { Name = endpoint, Type = "API" },
            (key, existing) => existing);

        metric.RecordApiRequest(statusCode, duration);

        if (durationMs > 2000) // Log slow API requests
        {
            _logger.LogWarning("Slow API request: {Endpoint} took {DurationMs}ms with status {StatusCode}", 
                endpoint, durationMs, statusCode);
        }
    }

    public void RecordDatabaseConnection(bool successful)
    {
        _databaseConnectionCounter.Add(1, new KeyValuePair<string, object?>("successful", successful));
        
        if (!successful)
        {
            _logger.LogError("Database connection failed");
        }
    }

    public void RecordMessageBusEvent(string eventType, bool successful)
    {
        var tags = new[]
        {
            new KeyValuePair<string, object?>("event_type", eventType),
            new KeyValuePair<string, object?>("successful", successful)
        };

        _messageBusEventCounter.Add(1, tags);

        if (!successful)
        {
            _logger.LogError("Message bus event failed: {EventType}", eventType);
        }
    }

    public void IncrementCounter(string counterName, string[]? tags = null)
    {
        var kvpTags = tags?.Select((tag, index) => new KeyValuePair<string, object?>(tag, index)).ToArray() ?? Array.Empty<KeyValuePair<string, object?>>();
        _meter.CreateCounter<long>(counterName).Add(1, kvpTags);
    }

    public void RecordHistogram(string histogramName, double value, string[]? tags = null)
    {
        var kvpTags = tags?.Select((tag, index) => new KeyValuePair<string, object?>(tag, index)).ToArray() ?? Array.Empty<KeyValuePair<string, object?>>();
        _meter.CreateHistogram<double>(histogramName).Record(value, kvpTags);
    }

    public async Task<PerformanceReport> GenerateReportAsync(TimeSpan period)
    {
        var cutoffTime = DateTime.UtcNow - period;
        var relevantMetrics = _metrics.Values
            .Where(m => m.LastUpdated >= cutoffTime)
            .ToList();

        var report = new PerformanceReport
        {
            GeneratedAt = DateTime.UtcNow,
            Period = period,
            TotalQueries = relevantMetrics.Where(m => m.Type == "Query").Sum(m => m.Count),
            AverageQueryDuration = relevantMetrics.Where(m => m.Type == "Query" && m.Count > 0)
                .DefaultIfEmpty(new PerformanceMetric())
                .Average(m => m.AverageDuration.TotalMilliseconds),
            TotalApiRequests = relevantMetrics.Where(m => m.Type == "API").Sum(m => m.Count),
            AverageApiDuration = relevantMetrics.Where(m => m.Type == "API" && m.Count > 0)
                .DefaultIfEmpty(new PerformanceMetric())
                .Average(m => m.AverageDuration.TotalMilliseconds),
            CacheHitRate = CalculateCacheHitRate(relevantMetrics),
            SlowQueries = relevantMetrics.Where(m => m.Type == "Query" && m.AverageDuration.TotalMilliseconds > 1000)
                .Select(m => new SlowQueryInfo { Name = m.Name, AverageDuration = m.AverageDuration, Count = m.Count })
                .ToList(),
            TopEndpoints = relevantMetrics.Where(m => m.Type == "API")
                .OrderByDescending(m => m.Count)
                .Take(10)
                .Select(m => new EndpointInfo { Name = m.Name, RequestCount = m.Count, AverageDuration = m.AverageDuration })
                .ToList()
        };

        _logger.LogInformation("Performance report generated: {TotalQueries} queries, {TotalApiRequests} API requests, {CacheHitRate:P2} cache hit rate",
            report.TotalQueries, report.TotalApiRequests, report.CacheHitRate);

        return report;
    }

    private double CalculateCacheHitRate(List<PerformanceMetric> cacheMetrics)
    {
        var totalHits = cacheMetrics.Where(m => m.Type == "Cache").Sum(m => m.CacheHits);
        var totalMisses = cacheMetrics.Where(m => m.Type == "Cache").Sum(m => m.CacheMisses);
        var total = totalHits + totalMisses;
        
        return total > 0 ? (double)totalHits / total : 0;
    }
}

public class PerformanceMetric
{
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public long Count { get; private set; }
    public TimeSpan TotalDuration { get; private set; }
    public TimeSpan AverageDuration => Count > 0 ? TimeSpan.FromTicks(TotalDuration.Ticks / Count) : TimeSpan.Zero;
    public long CacheHits { get; private set; }
    public long CacheMisses { get; private set; }
    public long SuccessfulRequests { get; private set; }
    public long FailedRequests { get; private set; }
    public DateTime LastUpdated { get; private set; } = DateTime.UtcNow;

    public void RecordDuration(TimeSpan duration)
    {
        Count++;
        TotalDuration = TotalDuration.Add(duration);
        LastUpdated = DateTime.UtcNow;
    }

    public void RecordCacheHit()
    {
        CacheHits++;
        LastUpdated = DateTime.UtcNow;
    }

    public void RecordCacheMiss()
    {
        CacheMisses++;
        LastUpdated = DateTime.UtcNow;
    }

    public void RecordApiRequest(int statusCode, TimeSpan duration)
    {
        Count++;
        TotalDuration = TotalDuration.Add(duration);
        
        if (statusCode >= 200 && statusCode < 400)
            SuccessfulRequests++;
        else
            FailedRequests++;
            
        LastUpdated = DateTime.UtcNow;
    }
}

public class PerformanceReport
{
    public DateTime GeneratedAt { get; set; }
    public TimeSpan Period { get; set; }
    public long TotalQueries { get; set; }
    public double AverageQueryDuration { get; set; }
    public long TotalApiRequests { get; set; }
    public double AverageApiDuration { get; set; }
    public double CacheHitRate { get; set; }
    public List<SlowQueryInfo> SlowQueries { get; set; } = new();
    public List<EndpointInfo> TopEndpoints { get; set; } = new();
}

public class SlowQueryInfo
{
    public string Name { get; set; } = string.Empty;
    public TimeSpan AverageDuration { get; set; }
    public long Count { get; set; }
}

public class EndpointInfo
{
    public string Name { get; set; } = string.Empty;
    public long RequestCount { get; set; }
    public TimeSpan AverageDuration { get; set; }
}
