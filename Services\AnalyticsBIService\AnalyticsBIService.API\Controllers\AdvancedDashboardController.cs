using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using AnalyticsBIService.Application.Services;
using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Infrastructure.Services;
using System.Security.Claims;

namespace AnalyticsBIService.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class AdvancedDashboardController : ControllerBase
{
    private readonly IAdvancedAnalyticsDashboardService _advancedDashboardService;
    private readonly ILogger<AdvancedDashboardController> _logger;

    public AdvancedDashboardController(
        IAdvancedAnalyticsDashboardService advancedDashboardService,
        ILogger<AdvancedDashboardController> logger)
    {
        _advancedDashboardService = advancedDashboardService;
        _logger = logger;
    }

    /// <summary>
    /// Get advanced analytics dashboard
    /// </summary>
    [HttpGet("advanced")]
    [ProducesResponseType(typeof(AdvancedDashboardDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetAdvancedDashboard([FromQuery] AdvancedDashboardRequestDto request)
    {
        try
        {
            var userId = GetCurrentUserId();
            var userRole = GetCurrentUserRole();

            var dashboardRequest = new AdvancedDashboardRequest
            {
                DateRange = new DateRangeDto
                {
                    StartDate = request.StartDate ?? DateTime.UtcNow.AddDays(-30),
                    EndDate = request.EndDate ?? DateTime.UtcNow
                },
                IncludePredictiveAnalytics = request.IncludePredictiveAnalytics,
                PredictionHorizonDays = request.PredictionHorizonDays,
                PredictionTypes = request.PredictionTypes ?? new List<string>(),
                IncludeRealTimeData = request.IncludeRealTimeData,
                CustomFilters = request.CustomFilters ?? new Dictionary<string, object>()
            };

            var dashboard = await _advancedDashboardService.GetAdvancedDashboardAsync(userId, userRole, dashboardRequest);
            return Ok(dashboard);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting advanced dashboard");
            return StatusCode(500, "An error occurred while retrieving advanced dashboard");
        }
    }

    /// <summary>
    /// Get real-time analytics
    /// </summary>
    [HttpGet("real-time")]
    [ProducesResponseType(typeof(RealTimeAnalyticsDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetRealTimeAnalytics()
    {
        try
        {
            var userId = GetCurrentUserId();
            var userRole = GetCurrentUserRole();

            var realTimeAnalytics = await _advancedDashboardService.GetRealTimeAnalyticsAsync(userId, userRole);
            return Ok(realTimeAnalytics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting real-time analytics");
            return StatusCode(500, "An error occurred while retrieving real-time analytics");
        }
    }

    /// <summary>
    /// Get predictive analytics
    /// </summary>
    [HttpGet("predictive")]
    [ProducesResponseType(typeof(PredictiveAnalyticsDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetPredictiveAnalytics([FromQuery] PredictiveAnalyticsRequestDto request)
    {
        try
        {
            var userId = GetCurrentUserId();
            var userRole = GetCurrentUserRole();

            var predictiveRequest = new PredictiveAnalyticsRequest
            {
                PredictionHorizonDays = request.PredictionHorizonDays,
                PredictionTypes = request.PredictionTypes ?? new List<string> { "Revenue", "Orders", "Performance" },
                IncludeConfidenceIntervals = request.IncludeConfidenceIntervals,
                ModelTypes = request.ModelTypes ?? new List<string>()
            };

            var predictiveAnalytics = await _advancedDashboardService.GetPredictiveAnalyticsAsync(userId, userRole, predictiveRequest);
            return Ok(predictiveAnalytics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting predictive analytics");
            return StatusCode(500, "An error occurred while retrieving predictive analytics");
        }
    }

    /// <summary>
    /// Create custom dashboard
    /// </summary>
    [HttpPost("custom")]
    [ProducesResponseType(typeof(CustomDashboardDto), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> CreateCustomDashboard([FromBody] CreateCustomDashboardRequestDto request)
    {
        try
        {
            var userId = GetCurrentUserId();

            var createRequest = new CreateCustomDashboardRequest
            {
                Name = request.Name,
                Description = request.Description,
                IsPublic = request.IsPublic,
                Layout = new Dictionary<string, object>
                {
                    ["columns"] = request.Layout?.Columns ?? 12,
                    ["rowHeight"] = request.Layout?.RowHeight ?? 100,
                    ["isDraggable"] = request.Layout?.IsDraggable ?? true,
                    ["isResizable"] = request.Layout?.IsResizable ?? true
                },
                Widgets = request.Widgets?.Select(w => new DashboardWidgetRequest
                {
                    WidgetType = w.WidgetType,
                    Title = w.Title,
                    Description = w.Configuration?.Description ?? string.Empty,
                    VisualizationConfig = w.Configuration,
                    PositionX = w.PositionX ?? 0,
                    PositionY = w.PositionY ?? 0,
                    Width = w.Width,
                    Height = w.Height
                }).ToList() ?? new List<DashboardWidgetRequest>(),
                GlobalFilters = request.GlobalFilters ?? new Dictionary<string, object>(),
                RefreshInterval = request.RefreshInterval ?? TimeSpan.FromMinutes(5),
                Theme = request.Theme ?? "Default",
                Tags = request.Tags ?? new List<string>()
            };

            var customDashboard = await _advancedDashboardService.CreateCustomDashboardAsync(userId, createRequest);
            return CreatedAtAction(nameof(GetCustomDashboard), new { id = customDashboard.Id }, customDashboard);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating custom dashboard");
            return StatusCode(500, "An error occurred while creating custom dashboard");
        }
    }

    /// <summary>
    /// Get custom dashboard by ID
    /// </summary>
    [HttpGet("custom/{id:guid}")]
    [ProducesResponseType(typeof(CustomDashboardDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetCustomDashboard(Guid id)
    {
        try
        {
            // This would be implemented to retrieve a specific custom dashboard
            return Ok(new { Message = "Custom dashboard retrieval not yet implemented", DashboardId = id });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting custom dashboard {DashboardId}", id);
            return StatusCode(500, "An error occurred while retrieving custom dashboard");
        }
    }

    /// <summary>
    /// Get dashboard insights
    /// </summary>
    [HttpGet("insights")]
    [ProducesResponseType(typeof(List<DashboardInsightDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetDashboardInsights()
    {
        try
        {
            var userId = GetCurrentUserId();
            var userRole = GetCurrentUserRole();

            var insights = await _advancedDashboardService.GenerateDashboardInsightsAsync(userId, userRole);
            return Ok(insights);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting dashboard insights");
            return StatusCode(500, "An error occurred while retrieving dashboard insights");
        }
    }

    /// <summary>
    /// Get dashboard performance metrics
    /// </summary>
    [HttpGet("{dashboardId:guid}/performance")]
    [ProducesResponseType(typeof(DashboardPerformanceDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetDashboardPerformance(Guid dashboardId)
    {
        try
        {
            var performance = await _advancedDashboardService.GetDashboardPerformanceMetricsAsync(dashboardId);
            return Ok(performance);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting dashboard performance for {DashboardId}", dashboardId);
            return StatusCode(500, "An error occurred while retrieving dashboard performance");
        }
    }

    /// <summary>
    /// Get dashboard alerts
    /// </summary>
    [HttpGet("alerts")]
    [ProducesResponseType(typeof(List<DashboardAlertDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetDashboardAlerts()
    {
        try
        {
            var userId = GetCurrentUserId();
            var userRole = GetCurrentUserRole();

            var alerts = await _advancedDashboardService.GetDashboardAlertsAsync(userId, userRole);
            return Ok(alerts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting dashboard alerts");
            return StatusCode(500, "An error occurred while retrieving dashboard alerts");
        }
    }

    // Helper methods
    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst("sub") ?? User.FindFirst("userId");
        return userIdClaim != null ? Guid.Parse(userIdClaim.Value) : Guid.Empty;
    }

    private string GetCurrentUserRole()
    {
        var roleClaim = User.FindFirst("role") ?? User.FindFirst(ClaimTypes.Role);
        return roleClaim?.Value ?? "User";
    }
}

// Request DTOs
public class AdvancedDashboardRequestDto
{
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public bool IncludePredictiveAnalytics { get; set; } = false;
    public int PredictionHorizonDays { get; set; } = 30;
    public List<string>? PredictionTypes { get; set; }
    public bool IncludeRealTimeData { get; set; } = true;
    public Dictionary<string, object>? CustomFilters { get; set; }
}

public class PredictiveAnalyticsRequestDto
{
    public int PredictionHorizonDays { get; set; } = 30;
    public List<string>? PredictionTypes { get; set; }
    public bool IncludeConfidenceIntervals { get; set; } = true;
    public List<string>? ModelTypes { get; set; }
}

public class CreateCustomDashboardRequestDto
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public bool IsPublic { get; set; } = false;
    public DashboardLayoutDto? Layout { get; set; }
    public List<CreateWidgetRequest>? Widgets { get; set; }
    public Dictionary<string, object>? GlobalFilters { get; set; }
    public TimeSpan? RefreshInterval { get; set; }
    public string? Theme { get; set; }
    public List<string>? Tags { get; set; }
}

// Supporting DTOs would be defined here or in separate files
