using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using TripManagement.Application.Commands.CargoTypeMaster;
using TripManagement.Application.DTOs;
using TripManagement.Application.Queries.CargoTypeMaster;

namespace TripManagement.API.Controllers;

/// <summary>
/// Controller for managing cargo type master data
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class CargoTypeMasterController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<CargoTypeMasterController> _logger;

    public CargoTypeMasterController(IMediator mediator, ILogger<CargoTypeMasterController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get paginated cargo type masters with filtering
    /// </summary>
    [HttpGet("paged")]
    public async Task<ActionResult<CargoTypeMasterPagedResultDto>> GetCargoTypeMastersPaged(
        [FromQuery] string? searchTerm = null,
        [FromQuery] string? category = null,
        [FromQuery] bool? isActive = null,
        [FromQuery] bool? requiresSpecialHandling = null,
        [FromQuery] bool? isHazardous = null,
        [FromQuery] bool? requiresTemperatureControl = null,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetCargoTypeMastersPagedQuery(
                searchTerm, category, isActive, requiresSpecialHandling, 
                isHazardous, requiresTemperatureControl, pageNumber, pageSize);
            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving paginated cargo type masters");
            return StatusCode(500, "An error occurred while retrieving cargo type masters");
        }
    }

    /// <summary>
    /// Create a new cargo type master
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<CargoTypeMasterDto>> CreateCargoTypeMaster(
        [FromBody] CreateCargoTypeMasterDto request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var command = new CreateCargoTypeMasterCommand
            {
                Code = request.Code,
                Name = request.Name,
                Description = request.Description,
                Category = request.Category,
                RequiresSpecialHandling = request.RequiresSpecialHandling,
                IsHazardous = request.IsHazardous,
                RequiresTemperatureControl = request.RequiresTemperatureControl,
                MinTemperatureCelsius = request.MinTemperatureCelsius,
                MaxTemperatureCelsius = request.MaxTemperatureCelsius,
                HandlingInstructions = request.HandlingInstructions,
                SafetyRequirements = request.SafetyRequirements,
                SortOrder = request.SortOrder,
                IconUrl = request.IconUrl,
                AdditionalProperties = request.AdditionalProperties
            };

            var result = await _mediator.Send(command, cancellationToken);
            return CreatedAtAction(nameof(GetCargoTypeMastersPaged), new { }, result);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while creating cargo type master");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating cargo type master");
            return StatusCode(500, "An error occurred while creating the cargo type master");
        }
    }
}
