using FinancialPayment.Application.DTOs;
using FinancialPayment.Application.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace FinancialPayment.API.Controllers;

/// <summary>
/// Controller for Trip Ledger management
/// </summary>
[ApiController]
[Route("api/trip-ledger")]
[Authorize]
public class TripLedgerController : ControllerBase
{
    private readonly ITripLedgerService _tripLedgerService;
    private readonly ILogger<TripLedgerController> _logger;

    public TripLedgerController(
        ITripLedgerService tripLedgerService,
        ILogger<TripLedgerController> logger)
    {
        _tripLedgerService = tripLedgerService;
        _logger = logger;
    }

    /// <summary>
    /// Get trip ledger by trip ID
    /// </summary>
    [HttpGet("trip/{tripId}")]
    [ProducesResponseType(typeof(TripLedgerDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<TripLedgerDto>> GetTripLedger(Guid tripId)
    {
        try
        {
            _logger.LogInformation("Getting trip ledger for Trip {TripId}", tripId);

            var ledger = await _tripLedgerService.GetTripLedgerAsync(tripId);
            return Ok(ledger);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Trip ledger not found for Trip {TripId}", tripId);
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting trip ledger for Trip {TripId}", tripId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get trip ledger by ledger ID
    /// </summary>
    [HttpGet("{ledgerId}")]
    [ProducesResponseType(typeof(TripLedgerDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<TripLedgerDto>> GetTripLedgerById(Guid ledgerId)
    {
        try
        {
            if (!await CanViewLedger(ledgerId))
            {
                return Forbid("You don't have permission to view this ledger");
            }

            _logger.LogInformation("Getting trip ledger {LedgerId}", ledgerId);

            var ledger = await _tripLedgerService.GetTripLedgerByIdAsync(ledgerId);
            return Ok(ledger);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Trip ledger {LedgerId} not found", ledgerId);
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting trip ledger {LedgerId}", ledgerId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get trip ledgers for a transport company
    /// </summary>
    [HttpGet("transport-company/{transportCompanyId}")]
    [ProducesResponseType(typeof(List<TripLedgerSummaryDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<List<TripLedgerSummaryDto>>> GetTripLedgersByTransportCompany(
        Guid transportCompanyId,
        [FromQuery] TripLedgerFilterDto filter)
    {
        try
        {
            if (!await CanViewTransportCompanyLedgers(transportCompanyId))
            {
                return Forbid("You don't have permission to view ledgers for this transport company");
            }

            _logger.LogInformation("Getting trip ledgers for Transport Company {TransportCompanyId}", transportCompanyId);

            var ledgers = await _tripLedgerService.GetTripLedgersByTransportCompanyAsync(transportCompanyId, filter);
            return Ok(ledgers);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting trip ledgers for Transport Company {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Create a new trip ledger
    /// </summary>
    [HttpPost]
    [ProducesResponseType(typeof(TripLedgerDto), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [Authorize(Roles = "Admin,TransportCompany,Broker")]
    public async Task<ActionResult<TripLedgerDto>> CreateTripLedger([FromBody] CreateTripLedgerDto request)
    {
        try
        {
            if (!await CanManageLedgers(request.TransportCompanyId))
            {
                return Forbid("You don't have permission to create ledgers for this transport company");
            }

            _logger.LogInformation("Creating trip ledger for Trip {TripId}", request.TripId);

            var ledger = await _tripLedgerService.CreateTripLedgerAsync(request);
            
            return CreatedAtAction(
                nameof(GetTripLedgerById), 
                new { ledgerId = ledger.Id }, 
                ledger);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Failed to create trip ledger for Trip {TripId}", request.TripId);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating trip ledger for Trip {TripId}", request.TripId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Add entry to trip ledger
    /// </summary>
    [HttpPost("{ledgerId}/entries")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [Authorize(Roles = "Admin,TransportCompany,Broker")]
    public async Task<IActionResult> AddLedgerEntry(
        Guid ledgerId, 
        [FromBody] AddTripLedgerEntryDto request)
    {
        try
        {
            if (!await CanManageLedger(ledgerId))
            {
                return Forbid("You don't have permission to modify this ledger");
            }

            _logger.LogInformation("Adding entry to trip ledger {LedgerId}", ledgerId);

            await _tripLedgerService.AddLedgerEntryAsync(ledgerId, request);
            
            return Ok(new { message = "Entry added successfully" });
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Failed to add entry to trip ledger {LedgerId}", ledgerId);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding entry to trip ledger {LedgerId}", ledgerId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Add milestone to trip ledger
    /// </summary>
    [HttpPost("{ledgerId}/milestones")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [Authorize(Roles = "Admin,TransportCompany,Broker")]
    public async Task<IActionResult> AddMilestone(
        Guid ledgerId, 
        [FromBody] AddTripLedgerMilestoneDto request)
    {
        try
        {
            if (!await CanManageLedger(ledgerId))
            {
                return Forbid("You don't have permission to modify this ledger");
            }

            _logger.LogInformation("Adding milestone to trip ledger {LedgerId}", ledgerId);

            await _tripLedgerService.AddMilestoneAsync(ledgerId, request);
            
            return Ok(new { message = "Milestone added successfully" });
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Failed to add milestone to trip ledger {LedgerId}", ledgerId);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding milestone to trip ledger {LedgerId}", ledgerId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Complete milestone in trip ledger
    /// </summary>
    [HttpPut("{ledgerId}/milestones/{milestoneId}/complete")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [Authorize(Roles = "Admin,TransportCompany,Broker")]
    public async Task<IActionResult> CompleteMilestone(
        Guid ledgerId, 
        Guid milestoneId,
        [FromBody] CompleteMilestoneDto request)
    {
        try
        {
            if (!await CanManageLedger(ledgerId))
            {
                return Forbid("You don't have permission to modify this ledger");
            }

            _logger.LogInformation("Completing milestone {MilestoneId} in trip ledger {LedgerId}", milestoneId, ledgerId);

            await _tripLedgerService.CompleteMilestoneAsync(ledgerId, milestoneId, request);
            
            return Ok(new { message = "Milestone completed successfully" });
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Failed to complete milestone {MilestoneId} in trip ledger {LedgerId}", milestoneId, ledgerId);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error completing milestone {MilestoneId} in trip ledger {LedgerId}", milestoneId, ledgerId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Add adjustment to trip ledger
    /// </summary>
    [HttpPost("{ledgerId}/adjustments")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [Authorize(Roles = "Admin,TransportCompany,Broker")]
    public async Task<IActionResult> AddAdjustment(
        Guid ledgerId, 
        [FromBody] AddTripLedgerAdjustmentDto request)
    {
        try
        {
            if (!await CanManageLedger(ledgerId))
            {
                return Forbid("You don't have permission to modify this ledger");
            }

            request.AdjustedBy = GetCurrentUserId();

            _logger.LogInformation("Adding adjustment to trip ledger {LedgerId}", ledgerId);

            await _tripLedgerService.AddAdjustmentAsync(ledgerId, request);
            
            return Ok(new { message = "Adjustment added successfully" });
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Failed to add adjustment to trip ledger {LedgerId}", ledgerId);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding adjustment to trip ledger {LedgerId}", ledgerId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get ledger balance
    /// </summary>
    [HttpGet("{ledgerId}/balance")]
    [ProducesResponseType(typeof(TripLedgerBalanceDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<TripLedgerBalanceDto>> GetLedgerBalance(Guid ledgerId)
    {
        try
        {
            if (!await CanViewLedger(ledgerId))
            {
                return Forbid("You don't have permission to view this ledger");
            }

            _logger.LogInformation("Getting balance for trip ledger {LedgerId}", ledgerId);

            var balance = await _tripLedgerService.GetLedgerBalanceAsync(ledgerId);
            return Ok(balance);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Trip ledger {LedgerId} not found", ledgerId);
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting balance for trip ledger {LedgerId}", ledgerId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get ledger entries
    /// </summary>
    [HttpGet("{ledgerId}/entries")]
    [ProducesResponseType(typeof(List<TripLedgerEntryDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<List<TripLedgerEntryDto>>> GetLedgerEntries(
        Guid ledgerId,
        [FromQuery] TripLedgerEntryFilterDto filter)
    {
        try
        {
            if (!await CanViewLedger(ledgerId))
            {
                return Forbid("You don't have permission to view this ledger");
            }

            _logger.LogInformation("Getting entries for trip ledger {LedgerId}", ledgerId);

            var entries = await _tripLedgerService.GetLedgerEntriesAsync(ledgerId, filter);
            return Ok(entries);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting entries for trip ledger {LedgerId}", ledgerId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Export ledger
    /// </summary>
    [HttpGet("{ledgerId}/export")]
    [ProducesResponseType(typeof(FileResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> ExportLedger(
        Guid ledgerId,
        [FromQuery] ExportFormat format = ExportFormat.Excel)
    {
        try
        {
            if (!await CanViewLedger(ledgerId))
            {
                return Forbid("You don't have permission to view this ledger");
            }

            _logger.LogInformation("Exporting trip ledger {LedgerId} in {Format} format", ledgerId, format);

            var fileBytes = await _tripLedgerService.ExportLedgerAsync(ledgerId, format);
            
            var contentType = format switch
            {
                ExportFormat.Excel => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                ExportFormat.PDF => "application/pdf",
                ExportFormat.CSV => "text/csv",
                ExportFormat.JSON => "application/json",
                _ => "application/octet-stream"
            };

            var fileName = $"trip-ledger-{ledgerId}-{DateTime.UtcNow:yyyyMMdd}.{format.ToString().ToLower()}";

            return File(fileBytes, contentType, fileName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting trip ledger {LedgerId}", ledgerId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get ledger analytics for transport company
    /// </summary>
    [HttpGet("analytics/transport-company/{transportCompanyId}")]
    [ProducesResponseType(typeof(TripLedgerAnalyticsDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<TripLedgerAnalyticsDto>> GetLedgerAnalytics(
        Guid transportCompanyId,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        try
        {
            if (!await CanViewTransportCompanyLedgers(transportCompanyId))
            {
                return Forbid("You don't have permission to view analytics for this transport company");
            }

            var from = fromDate ?? DateTime.UtcNow.AddMonths(-12);
            var to = toDate ?? DateTime.UtcNow;

            _logger.LogInformation("Getting ledger analytics for Transport Company {TransportCompanyId}", transportCompanyId);

            var analytics = await _tripLedgerService.GetLedgerAnalyticsAsync(transportCompanyId, from, to);
            return Ok(analytics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting ledger analytics for Transport Company {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    private async Task<bool> CanViewLedger(Guid ledgerId)
    {
        // Implementation would check user permissions for the specific ledger
        return await Task.FromResult(true);
    }

    private async Task<bool> CanManageLedger(Guid ledgerId)
    {
        // Implementation would check user permissions for managing the specific ledger
        return await Task.FromResult(true);
    }

    private async Task<bool> CanViewTransportCompanyLedgers(Guid transportCompanyId)
    {
        var currentUserId = GetCurrentUserId();
        var userRoles = GetCurrentUserRoles();

        return userRoles.Contains("Admin") || 
               (userRoles.Contains("TransportCompany") && await IsUserFromTransportCompany(currentUserId, transportCompanyId));
    }

    private async Task<bool> CanManageLedgers(Guid transportCompanyId)
    {
        return await CanViewTransportCompanyLedgers(transportCompanyId);
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst("sub") ?? User.FindFirst("userId");
        return userIdClaim != null && Guid.TryParse(userIdClaim.Value, out var userId) ? userId : Guid.Empty;
    }

    private List<string> GetCurrentUserRoles()
    {
        return User.FindAll("role").Select(c => c.Value).ToList();
    }

    private async Task<bool> IsUserFromTransportCompany(Guid userId, Guid transportCompanyId)
    {
        // This would typically check the user's company association
        return await Task.FromResult(true);
    }
}
