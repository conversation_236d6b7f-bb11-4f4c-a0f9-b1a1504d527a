# ⚡ Performance Guidelines

## 📋 Overview

This guide provides performance optimization strategies and best practices for frontend applications integrating with the TLI platform. Following these guidelines ensures fast, responsive user experiences across all application types.

## 🎯 Performance Targets

### Core Web Vitals

| Metric | Target | Description |
|--------|--------|-------------|
| **First Contentful Paint (FCP)** | < 1.8s | Time to first content render |
| **Largest Contentful Paint (LCP)** | < 2.5s | Time to largest content render |
| **First Input Delay (FID)** | < 100ms | Time to first user interaction |
| **Cumulative Layout Shift (CLS)** | < 0.1 | Visual stability score |
| **Time to Interactive (TTI)** | < 3.5s | Time until page is fully interactive |

### API Performance Targets

| Operation | Target | Description |
|-----------|--------|-------------|
| **Authentication** | < 500ms | Login/token refresh |
| **Data Fetching** | < 1s | List/detail API calls |
| **Search Operations** | < 800ms | Search and filtering |
| **Real-time Updates** | < 100ms | SignalR message delivery |
| **File Uploads** | Progress feedback | Visual upload progress |

## 🚀 Frontend Optimization Strategies

### 1. Code Splitting & Lazy Loading

```typescript
// Route-based code splitting
import { lazy, Suspense } from 'react';
import { Routes, Route } from 'react-router-dom';

// Lazy load components
const Dashboard = lazy(() => import('./pages/Dashboard'));
const OrderManagement = lazy(() => import('./pages/OrderManagement'));
const TripTracking = lazy(() => import('./pages/TripTracking'));
const UserProfile = lazy(() => import('./pages/UserProfile'));

// Loading component
function LoadingSpinner() {
  return (
    <div className="loading-spinner">
      <div className="spinner"></div>
      <p>Loading...</p>
    </div>
  );
}

function App() {
  return (
    <Suspense fallback={<LoadingSpinner />}>
      <Routes>
        <Route path="/dashboard" element={<Dashboard />} />
        <Route path="/orders" element={<OrderManagement />} />
        <Route path="/trips" element={<TripTracking />} />
        <Route path="/profile" element={<UserProfile />} />
      </Routes>
    </Suspense>
  );
}

// Component-level lazy loading
const HeavyChart = lazy(() => 
  import('./components/HeavyChart').then(module => ({
    default: module.HeavyChart
  }))
);

function Dashboard() {
  const [showChart, setShowChart] = useState(false);

  return (
    <div>
      <h1>Dashboard</h1>
      <button onClick={() => setShowChart(true)}>
        Show Analytics
      </button>
      
      {showChart && (
        <Suspense fallback={<div>Loading chart...</div>}>
          <HeavyChart />
        </Suspense>
      )}
    </div>
  );
}
```

### 2. Data Fetching Optimization

```typescript
// React Query for efficient data fetching
import { useQuery, useInfiniteQuery, useMutation, useQueryClient } from 'react-query';

// Optimized data fetching with caching
function useOrders(filters: OrderFilters) {
  return useQuery(
    ['orders', filters],
    () => orderService.getOrders(filters),
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      refetchOnWindowFocus: false,
      keepPreviousData: true, // Keep previous data while fetching new
      select: (data) => ({
        ...data,
        orders: data.orders.map(order => ({
          ...order,
          // Transform data if needed
          displayName: `${order.orderNumber} - ${order.status}`
        }))
      })
    }
  );
}

// Infinite scrolling for large lists
function useInfiniteOrders(filters: OrderFilters) {
  return useInfiniteQuery(
    ['orders', 'infinite', filters],
    ({ pageParam = 1 }) => 
      orderService.getOrders({ ...filters, page: pageParam }),
    {
      getNextPageParam: (lastPage) => 
        lastPage.pagination.hasNextPage 
          ? lastPage.pagination.currentPage + 1 
          : undefined,
      staleTime: 5 * 60 * 1000
    }
  );
}

// Optimistic updates for better UX
function useUpdateOrder() {
  const queryClient = useQueryClient();

  return useMutation(
    (orderUpdate: OrderUpdate) => orderService.updateOrder(orderUpdate),
    {
      onMutate: async (orderUpdate) => {
        // Cancel outgoing refetches
        await queryClient.cancelQueries(['orders']);

        // Snapshot previous value
        const previousOrders = queryClient.getQueryData(['orders']);

        // Optimistically update
        queryClient.setQueryData(['orders'], (old: any) => ({
          ...old,
          orders: old.orders.map((order: any) =>
            order.id === orderUpdate.id
              ? { ...order, ...orderUpdate }
              : order
          )
        }));

        return { previousOrders };
      },
      onError: (err, orderUpdate, context) => {
        // Rollback on error
        if (context?.previousOrders) {
          queryClient.setQueryData(['orders'], context.previousOrders);
        }
      },
      onSettled: () => {
        // Refetch after mutation
        queryClient.invalidateQueries(['orders']);
      }
    }
  );
}
```

### 3. Virtual Scrolling for Large Lists

```typescript
import { FixedSizeList as List } from 'react-window';
import { useMemo } from 'react';

interface VirtualizedOrderListProps {
  orders: Order[];
  onOrderClick: (order: Order) => void;
}

function VirtualizedOrderList({ orders, onOrderClick }: VirtualizedOrderListProps) {
  const Row = useMemo(() => 
    ({ index, style }: { index: number; style: React.CSSProperties }) => {
      const order = orders[index];
      
      return (
        <div style={style} className="order-row">
          <OrderCard 
            order={order} 
            onClick={() => onOrderClick(order)}
          />
        </div>
      );
    }, [orders, onOrderClick]
  );

  return (
    <List
      height={600} // Container height
      itemCount={orders.length}
      itemSize={120} // Row height
      width="100%"
    >
      {Row}
    </List>
  );
}

// Memoized order card to prevent unnecessary re-renders
const OrderCard = React.memo(({ order, onClick }: {
  order: Order;
  onClick: () => void;
}) => {
  return (
    <div className="order-card" onClick={onClick}>
      <h3>{order.orderNumber}</h3>
      <p>Status: {order.status}</p>
      <p>Amount: {order.amount}</p>
    </div>
  );
});
```

### 4. Image Optimization

```typescript
// Lazy loading images with intersection observer
import { useState, useRef, useEffect } from 'react';

interface LazyImageProps {
  src: string;
  alt: string;
  placeholder?: string;
  className?: string;
}

function LazyImage({ src, alt, placeholder, className }: LazyImageProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, []);

  return (
    <div className={`lazy-image ${className}`} ref={imgRef}>
      {isInView && (
        <img
          src={src}
          alt={alt}
          onLoad={() => setIsLoaded(true)}
          style={{
            opacity: isLoaded ? 1 : 0,
            transition: 'opacity 0.3s ease'
          }}
        />
      )}
      {!isLoaded && placeholder && (
        <div className="image-placeholder">
          <img src={placeholder} alt="" />
        </div>
      )}
    </div>
  );
}

// Progressive image loading
function ProgressiveImage({ src, placeholder, alt }: {
  src: string;
  placeholder: string;
  alt: string;
}) {
  const [currentSrc, setCurrentSrc] = useState(placeholder);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const img = new Image();
    img.onload = () => {
      setCurrentSrc(src);
      setIsLoading(false);
    };
    img.src = src;
  }, [src]);

  return (
    <div className="progressive-image">
      <img
        src={currentSrc}
        alt={alt}
        style={{
          filter: isLoading ? 'blur(5px)' : 'none',
          transition: 'filter 0.3s ease'
        }}
      />
      {isLoading && <div className="loading-overlay">Loading...</div>}
    </div>
  );
}
```

### 5. State Management Optimization

```typescript
// Efficient state updates with immer
import { produce } from 'immer';
import { create } from 'zustand';

interface AppState {
  orders: Order[];
  selectedOrder: Order | null;
  filters: OrderFilters;
  updateOrder: (orderId: string, updates: Partial<Order>) => void;
  setSelectedOrder: (order: Order | null) => void;
  updateFilters: (filters: Partial<OrderFilters>) => void;
}

const useAppStore = create<AppState>((set) => ({
  orders: [],
  selectedOrder: null,
  filters: {},
  
  updateOrder: (orderId, updates) =>
    set(produce((state) => {
      const orderIndex = state.orders.findIndex(o => o.id === orderId);
      if (orderIndex !== -1) {
        Object.assign(state.orders[orderIndex], updates);
      }
    })),

  setSelectedOrder: (order) =>
    set({ selectedOrder: order }),

  updateFilters: (newFilters) =>
    set(produce((state) => {
      Object.assign(state.filters, newFilters);
    }))
}));

// Selector pattern for efficient subscriptions
const useOrdersCount = () => useAppStore(state => state.orders.length);
const useSelectedOrderId = () => useAppStore(state => state.selectedOrder?.id);
```

## 🌐 API Optimization

### 1. Request Batching

```typescript
// Batch multiple API requests
class RequestBatcher {
  private batches: Map<string, any[]> = new Map();
  private timeouts: Map<string, NodeJS.Timeout> = new Map();

  batch<T>(
    key: string,
    request: any,
    batchProcessor: (requests: any[]) => Promise<T[]>,
    delay: number = 50
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      // Add request to batch
      if (!this.batches.has(key)) {
        this.batches.set(key, []);
      }
      
      const batch = this.batches.get(key)!;
      batch.push({ request, resolve, reject });

      // Clear existing timeout
      if (this.timeouts.has(key)) {
        clearTimeout(this.timeouts.get(key)!);
      }

      // Set new timeout to process batch
      const timeout = setTimeout(async () => {
        const currentBatch = this.batches.get(key) || [];
        this.batches.delete(key);
        this.timeouts.delete(key);

        try {
          const requests = currentBatch.map(item => item.request);
          const results = await batchProcessor(requests);
          
          currentBatch.forEach((item, index) => {
            item.resolve(results[index]);
          });
        } catch (error) {
          currentBatch.forEach(item => {
            item.reject(error);
          });
        }
      }, delay);

      this.timeouts.set(key, timeout);
    });
  }
}

// Usage
const batcher = new RequestBatcher();

async function getUserDetails(userId: string) {
  return batcher.batch(
    'users',
    userId,
    async (userIds: string[]) => {
      // Batch API call
      const response = await apiClient.post('/users/batch', { userIds });
      return response.data;
    }
  );
}
```

### 2. Response Caching

```typescript
// Service Worker for API caching
class ApiCache {
  private cache: Cache | null = null;
  private readonly CACHE_NAME = 'tli-api-cache-v1';

  async init() {
    if ('caches' in window) {
      this.cache = await caches.open(this.CACHE_NAME);
    }
  }

  async get(url: string): Promise<Response | null> {
    if (!this.cache) return null;
    
    const cachedResponse = await this.cache.match(url);
    if (cachedResponse) {
      const cacheTime = cachedResponse.headers.get('cache-time');
      const maxAge = cachedResponse.headers.get('max-age');
      
      if (cacheTime && maxAge) {
        const age = Date.now() - parseInt(cacheTime);
        if (age < parseInt(maxAge) * 1000) {
          return cachedResponse;
        }
      }
    }
    
    return null;
  }

  async set(url: string, response: Response, maxAge: number = 300) {
    if (!this.cache) return;

    const responseClone = response.clone();
    responseClone.headers.set('cache-time', Date.now().toString());
    responseClone.headers.set('max-age', maxAge.toString());
    
    await this.cache.put(url, responseClone);
  }

  async invalidate(pattern: string) {
    if (!this.cache) return;

    const keys = await this.cache.keys();
    const deletePromises = keys
      .filter(request => request.url.includes(pattern))
      .map(request => this.cache!.delete(request));
    
    await Promise.all(deletePromises);
  }
}

// HTTP client with caching
class CachedApiClient {
  private cache = new ApiCache();

  constructor() {
    this.cache.init();
  }

  async get(url: string, options: { cache?: boolean; maxAge?: number } = {}) {
    const { cache = true, maxAge = 300 } = options;

    // Try cache first
    if (cache) {
      const cachedResponse = await this.cache.get(url);
      if (cachedResponse) {
        return cachedResponse.json();
      }
    }

    // Fetch from network
    const response = await fetch(url);
    
    // Cache successful responses
    if (cache && response.ok) {
      await this.cache.set(url, response, maxAge);
    }

    return response.json();
  }
}
```

### 3. Debounced Search

```typescript
import { useMemo, useState, useEffect } from 'react';
import { debounce } from 'lodash';

function useDebounceSearch<T>(
  searchFunction: (query: string) => Promise<T[]>,
  delay: number = 300
) {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<T[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const debouncedSearch = useMemo(
    () => debounce(async (searchQuery: string) => {
      if (!searchQuery.trim()) {
        setResults([]);
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      try {
        const searchResults = await searchFunction(searchQuery);
        setResults(searchResults);
      } catch (error) {
        console.error('Search error:', error);
        setResults([]);
      } finally {
        setIsLoading(false);
      }
    }, delay),
    [searchFunction, delay]
  );

  useEffect(() => {
    debouncedSearch(query);
    return () => debouncedSearch.cancel();
  }, [query, debouncedSearch]);

  return {
    query,
    setQuery,
    results,
    isLoading
  };
}

// Usage in component
function OrderSearch() {
  const { query, setQuery, results, isLoading } = useDebounceSearch(
    async (searchQuery: string) => {
      const response = await orderService.searchOrders(searchQuery);
      return response.data;
    },
    300
  );

  return (
    <div>
      <input
        type="text"
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        placeholder="Search orders..."
      />
      
      {isLoading && <div>Searching...</div>}
      
      <div className="search-results">
        {results.map(order => (
          <OrderCard key={order.id} order={order} />
        ))}
      </div>
    </div>
  );
}
```

## 📱 Mobile Performance

### 1. Touch Optimization

```typescript
// Optimized touch interactions
function useTouchOptimization() {
  useEffect(() => {
    // Disable 300ms click delay
    const viewport = document.querySelector('meta[name=viewport]');
    if (viewport) {
      viewport.setAttribute('content', 
        'width=device-width, initial-scale=1, user-scalable=no'
      );
    }

    // Add touch-action CSS for better scrolling
    document.body.style.touchAction = 'manipulation';

    return () => {
      document.body.style.touchAction = '';
    };
  }, []);
}

// Fast click implementation
function useFastClick(callback: () => void) {
  const [isPressed, setIsPressed] = useState(false);

  const handleTouchStart = useCallback(() => {
    setIsPressed(true);
  }, []);

  const handleTouchEnd = useCallback(() => {
    if (isPressed) {
      callback();
      setIsPressed(false);
    }
  }, [isPressed, callback]);

  const handleTouchCancel = useCallback(() => {
    setIsPressed(false);
  }, []);

  return {
    onTouchStart: handleTouchStart,
    onTouchEnd: handleTouchEnd,
    onTouchCancel: handleTouchCancel,
    onClick: callback // Fallback for non-touch devices
  };
}
```

### 2. Offline Support

```typescript
// Service Worker for offline functionality
class OfflineManager {
  private isOnline = navigator.onLine;
  private pendingRequests: any[] = [];

  constructor() {
    window.addEventListener('online', this.handleOnline.bind(this));
    window.addEventListener('offline', this.handleOffline.bind(this));
  }

  private handleOnline() {
    this.isOnline = true;
    this.processPendingRequests();
  }

  private handleOffline() {
    this.isOnline = false;
  }

  async makeRequest(request: any) {
    if (this.isOnline) {
      try {
        return await this.executeRequest(request);
      } catch (error) {
        if (this.isNetworkError(error)) {
          this.queueRequest(request);
          throw new Error('Request queued for when online');
        }
        throw error;
      }
    } else {
      this.queueRequest(request);
      throw new Error('Offline - request queued');
    }
  }

  private queueRequest(request: any) {
    this.pendingRequests.push(request);
  }

  private async processPendingRequests() {
    const requests = [...this.pendingRequests];
    this.pendingRequests = [];

    for (const request of requests) {
      try {
        await this.executeRequest(request);
      } catch (error) {
        // Re-queue failed requests
        this.queueRequest(request);
      }
    }
  }

  private async executeRequest(request: any) {
    // Implementation depends on your API client
    return fetch(request.url, request.options);
  }

  private isNetworkError(error: any): boolean {
    return error.name === 'NetworkError' || 
           error.message.includes('fetch');
  }
}
```

## 📊 Performance Monitoring

### 1. Performance Metrics Collection

```typescript
// Performance monitoring utility
class PerformanceMonitor {
  private metrics: Map<string, number> = new Map();

  startTiming(label: string) {
    this.metrics.set(`${label}_start`, performance.now());
  }

  endTiming(label: string) {
    const startTime = this.metrics.get(`${label}_start`);
    if (startTime) {
      const duration = performance.now() - startTime;
      this.metrics.set(label, duration);
      this.reportMetric(label, duration);
    }
  }

  measureApiCall<T>(
    label: string,
    apiCall: () => Promise<T>
  ): Promise<T> {
    this.startTiming(label);
    
    return apiCall()
      .then(result => {
        this.endTiming(label);
        return result;
      })
      .catch(error => {
        this.endTiming(label);
        throw error;
      });
  }

  private reportMetric(label: string, duration: number) {
    // Send to analytics service
    console.log(`Performance: ${label} took ${duration.toFixed(2)}ms`);
    
    // Report to monitoring service
    if (window.gtag) {
      window.gtag('event', 'timing_complete', {
        name: label,
        value: Math.round(duration)
      });
    }
  }

  getMetrics() {
    return Object.fromEntries(this.metrics);
  }
}

// Usage
const performanceMonitor = new PerformanceMonitor();

// Measure API calls
async function fetchOrders() {
  return performanceMonitor.measureApiCall(
    'fetch_orders',
    () => orderService.getOrders()
  );
}

// Measure component render time
function ExpensiveComponent() {
  useEffect(() => {
    performanceMonitor.startTiming('expensive_component_render');
    
    return () => {
      performanceMonitor.endTiming('expensive_component_render');
    };
  }, []);

  return <div>Expensive component content</div>;
}
```

This performance guide provides comprehensive strategies for optimizing frontend applications, ensuring fast and responsive user experiences across all devices and network conditions.
