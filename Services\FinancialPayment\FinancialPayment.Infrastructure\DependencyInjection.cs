using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.EntityFrameworkCore;
using StackExchange.Redis;
using FinancialPayment.Infrastructure.Data;
using FinancialPayment.Infrastructure.Repositories;
using FinancialPayment.Infrastructure.Services;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Application.Interfaces.Repositories;
using Shared.Infrastructure.Interfaces;
using Shared.Infrastructure.Persistence;

namespace FinancialPayment.Infrastructure;

public static class DependencyInjection
{
    public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        // Database
        services.AddDbContext<FinancialPaymentDbContext>(options =>
            options.UseNpgsql(configuration.GetConnectionString("DefaultConnection")));

        // Unit of Work
        services.AddScoped<IUnitOfWork, UnitOfWork<FinancialPaymentDbContext>>();

        // Repositories
        services.AddScoped<IEscrowAccountRepository, EscrowAccountRepository>();
        services.AddScoped<ISettlementRepository, SettlementRepository>();
        services.AddScoped<ICommissionRepository, CommissionRepository>();
        services.AddScoped<IPaymentDisputeRepository, PaymentDisputeRepository>();
        services.AddScoped<ITaxRuleRepository, TaxRuleRepository>();
        services.AddScoped<ITaxCalculationRepository, TaxCalculationRepository>();
        services.AddScoped<ITaxConfigurationRepository, TaxConfigurationRepository>();
        services.AddScoped<IGstConfigurationRepository, GstConfigurationRepository>();
        services.AddScoped<ITdsConfigurationRepository, TdsConfigurationRepository>();
        services.AddScoped<IHsnCodeRepository, HsnCodeRepository>();
        services.AddScoped<IReconciliationRepository, ReconciliationRepository>();
        services.AddScoped<IFraudDetectionRuleRepository, FraudDetectionRuleRepository>();
        services.AddScoped<IFraudAssessmentRepository, FraudAssessmentRepository>();

        // Services
        services.AddScoped<IEscrowService, EscrowService>();
        services.AddScoped<ISettlementService, SettlementService>();
        services.AddScoped<ICommissionService, CommissionService>();
        services.AddScoped<IPaymentDisputeService, PaymentDisputeService>();
        services.AddScoped<IEnhancedPaymentService, EnhancedPaymentService>();
        services.AddScoped<ITaxCalculationService, TaxCalculationService>();
        services.AddScoped<ITaxConfigurationService, TaxConfigurationService>();
        services.AddScoped<IReconciliationService, ReconciliationService>();
        services.AddScoped<IPlatformTransactionProvider, PlatformTransactionProvider>();
        services.AddScoped<IFraudDetectionService, FraudDetectionService>();
        services.AddScoped<IFraudPatternAnalyzer, FraudPatternAnalyzer>();
        services.AddScoped<IBlacklistService, BlacklistService>();
        services.AddScoped<IPaymentAnalyticsService, PaymentAnalyticsService>();
        services.AddScoped<IPaymentDataAggregator, PaymentDataAggregator>();
        services.AddScoped<IReportGenerator, ReportGenerator>();
        services.AddScoped<ISubscriptionBillingService, SubscriptionBillingService>();
        services.AddScoped<IBillingProcessor, BillingProcessor>();
        services.AddScoped<ISubscriptionNotificationService, SubscriptionNotificationService>();
        services.AddScoped<IFinancialReportingService, FinancialReportingService>();
        services.AddScoped<IFinancialDataAggregator, FinancialDataAggregator>();
        services.AddScoped<IFinancialReportGenerator, FinancialReportGenerator>();
        services.AddScoped<IFeatureFlagService, FeatureFlagService>();
        services.AddScoped<IFeatureFlagCache, FeatureFlagCache>();
        services.AddScoped<IFeatureFlagAnalyticsService, FeatureFlagAnalyticsService>();
        services.AddScoped<IMonitoringService, MonitoringService>();
        services.AddScoped<IUsageAnalyticsService, UsageAnalyticsService>();
        services.AddScoped<IHealthCheckService, HealthCheckService>();

        // Caching services
        services.AddStackExchangeRedisCache(options =>
        {
            var cacheConfig = configuration.GetSection("CacheConfiguration").Get<CacheConfiguration>();
            if (cacheConfig?.EnableDistributedCaching == true)
            {
                options.Configuration = cacheConfig.ConnectionString;
                options.InstanceName = cacheConfig.InstanceName;
            }
        });
        services.AddSingleton<IConnectionMultiplexer>(provider =>
        {
            var cacheConfig = configuration.GetSection("CacheConfiguration").Get<CacheConfiguration>();
            return ConnectionMultiplexer.Connect(cacheConfig?.ConnectionString ?? "localhost:6379");
        });
        services.AddScoped<IDistributedCacheService, RedisCacheService>();
        services.AddScoped<IPaymentCacheService, PaymentCacheService>();
        services.AddScoped<ICacheKeyGenerator, CacheKeyGenerator>();
        services.AddScoped<ICacheManagementService, CacheManagementService>();

        // Performance testing services
        services.AddScoped<IPerformanceTestingService, PerformanceTestingService>();
        services.AddScoped<ILoadGenerator, LoadGenerator>();
        services.AddScoped<IPerformanceMetricsCollector, PerformanceMetricsCollector>();

        // Reconciliation providers
        services.AddScoped<IPaymentGatewayReconciliationProvider, RazorpayReconciliationProvider>();
        services.AddScoped<IPaymentGatewayReconciliationProvider, StripeReconciliationProvider>();

        // External Services & Payment Gateway Settings
        services.Configure<RazorPaySettings>(configuration.GetSection("RazorPay"));
        services.Configure<StripeSettings>(configuration.GetSection("Stripe"));
        services.Configure<PayPalSettings>(configuration.GetSection("PayPal"));
        services.Configure<SquareSettings>(configuration.GetSection("Square"));
        services.Configure<TaxConfiguration>(configuration.GetSection("TaxConfiguration"));
        services.Configure<ReconciliationConfiguration>(configuration.GetSection("ReconciliationConfiguration"));
        services.Configure<FraudDetectionConfiguration>(configuration.GetSection("FraudDetectionConfiguration"));
        services.Configure<AnalyticsConfiguration>(configuration.GetSection("AnalyticsConfiguration"));
        services.Configure<SubscriptionConfiguration>(configuration.GetSection("SubscriptionConfiguration"));
        services.Configure<FinancialReportConfiguration>(configuration.GetSection("FinancialReportConfiguration"));
        services.Configure<FeatureFlagConfiguration>(configuration.GetSection("FeatureFlagConfiguration"));
        services.Configure<MonitoringConfiguration>(configuration.GetSection("MonitoringConfiguration"));
        services.Configure<CacheConfiguration>(configuration.GetSection("CacheConfiguration"));
        services.Configure<PerformanceTestConfiguration>(configuration.GetSection("PerformanceTestConfiguration"));

        // Payment Gateways
        services.AddScoped<RazorpayGateway>();
        services.AddScoped<StripeGateway>();
        services.AddScoped<PayPalGateway>();
        services.AddScoped<SquareGateway>();
        services.AddScoped<IPaymentGatewayFactory, PaymentGatewayFactory>();

        // HTTP Clients for payment gateways
        services.AddHttpClient<StripeGateway>();
        services.AddHttpClient<PayPalGateway>();
        services.AddHttpClient<SquareGateway>();

        return services;
    }
}

public class RazorPaySettings
{
    public string KeyId { get; set; } = string.Empty;
    public string KeySecret { get; set; } = string.Empty;
    public string WebhookSecret { get; set; } = string.Empty;
}
