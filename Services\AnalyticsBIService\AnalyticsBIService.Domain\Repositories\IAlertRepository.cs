using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Enums;
using Shared.Domain.Common;
using Shared.Infrastructure.Repositories;

namespace AnalyticsBIService.Domain.Repositories;

/// <summary>
/// Repository interface for Alert entity
/// </summary>
public interface IAlertRepository : IRepositoryBase<Alert, Guid>
{
    /// <summary>
    /// Get alerts by user ID
    /// </summary>
    Task<PagedResult<Alert>> GetByUserIdAsync(
        Guid userId,
        int page,
        int pageSize,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get active alerts
    /// </summary>
    Task<PagedResult<Alert>> GetActiveAlertsAsync(
        int page,
        int pageSize,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get alerts by severity
    /// </summary>
    Task<PagedResult<Alert>> GetBySeverityAsync(
        AlertSeverity severity,
        int page,
        int pageSize,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get alerts by rule ID
    /// </summary>
    Task<PagedResult<Alert>> GetByRuleIdAsync(
        Guid ruleId,
        int page,
        int pageSize,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get alerts by date range
    /// </summary>
    Task<PagedResult<Alert>> GetByDateRangeAsync(
        DateTime startDate,
        DateTime endDate,
        int page,
        int pageSize,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get unacknowledged alerts
    /// </summary>
    Task<PagedResult<Alert>> GetUnacknowledgedAlertsAsync(
        Guid? userId = null,
        int page = 1,
        int pageSize = 10,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Acknowledge alert
    /// </summary>
    Task AcknowledgeAlertAsync(
        Guid alertId,
        Guid acknowledgedBy,
        string? notes = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Resolve alert
    /// </summary>
    Task ResolveAlertAsync(
        Guid alertId,
        Guid resolvedBy,
        string? resolution = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get alert statistics
    /// </summary>
    Task<Dictionary<AlertSeverity, int>> GetAlertStatisticsAsync(
        DateTime? startDate = null,
        DateTime? endDate = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Bulk acknowledge alerts
    /// </summary>
    Task BulkAcknowledgeAsync(
        List<Guid> alertIds,
        Guid acknowledgedBy,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all alerts with pagination
    /// </summary>
    Task<PagedResult<Alert>> GetAllAsync(
        int page,
        int pageSize,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get pending alerts with limit
    /// </summary>
    Task<IEnumerable<Alert>> GetPendingAlertsAsync(
        int limit,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get pending alerts
    /// </summary>
    Task<List<Alert>> GetPendingAlertsAsync(
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get old pending alerts
    /// </summary>
    Task<List<Alert>> GetOldPendingAlertsAsync(
        DateTime cutoffTime,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete old resolved alerts
    /// </summary>
    Task DeleteOldResolvedAlertsAsync(
        DateTime cutoffDate,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get recent alert by entity details
    /// </summary>
    Task<Alert?> GetRecentAlertAsync(
        Guid entityId,
        string entityType,
        string ruleName,
        TimeSpan timeWindow,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get recent alert by metric ID
    /// </summary>
    Task<Alert?> GetRecentAlertAsync(
        Guid metricId,
        TimeSpan timeWindow,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for AlertRule entity
/// </summary>
public interface IAlertRuleRepository : IRepositoryBase<AlertRule, Guid>
{
    /// <summary>
    /// Get alert rules by user ID
    /// </summary>
    Task<PagedResult<AlertRule>> GetByUserIdAsync(
        Guid userId,
        int page,
        int pageSize,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get active alert rules
    /// </summary>
    Task<IEnumerable<AlertRule>> GetActiveRulesAsync(
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get alert rules by metric
    /// </summary>
    Task<List<AlertRule>> GetByMetricAsync(
        string metricName,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get alert rule by name
    /// </summary>
    Task<AlertRule?> GetByNameAsync(
        string name,
        Guid userId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Enable/disable alert rule
    /// </summary>
    Task SetEnabledAsync(
        Guid ruleId,
        bool enabled,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Update rule last triggered
    /// </summary>
    Task UpdateLastTriggeredAsync(
        Guid ruleId,
        DateTime triggeredAt,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get rules due for evaluation
    /// </summary>
    Task<List<AlertRule>> GetRulesDueForEvaluationAsync(
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get pending alerts
    /// </summary>
    Task<List<Alert>> GetPendingAlertsAsync(
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get old pending alerts
    /// </summary>
    Task<List<Alert>> GetOldPendingAlertsAsync(
        DateTime cutoffTime,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete old resolved alerts
    /// </summary>
    Task DeleteOldResolvedAlertsAsync(
        DateTime cutoffDate,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get recent alert
    /// </summary>
    Task<Alert?> GetRecentAlertAsync(
        Guid metricId,
        TimeSpan timeWindow,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get rule statistics
    /// </summary>
    Task<Dictionary<string, int>> GetRuleStatisticsAsync(
        Guid? userId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get alert rules by user ID (simple version)
    /// </summary>
    Task<IEnumerable<AlertRule>> GetByUserIdAsync(
        Guid userId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get alert rules by category
    /// </summary>
    Task<IEnumerable<AlertRule>> GetByCategoryAsync(
        KPICategory category,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get alert rules by severity
    /// </summary>
    Task<IEnumerable<AlertRule>> GetBySeverityAsync(
        AlertSeverity severity,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get enabled alert rules
    /// </summary>
    Task<IEnumerable<AlertRule>> GetEnabledRulesAsync(
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get alert rule by metric name
    /// </summary>
    Task<AlertRule?> GetByMetricNameAsync(
        string metricName,
        CancellationToken cancellationToken = default);
}
