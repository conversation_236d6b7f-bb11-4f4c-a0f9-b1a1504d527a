using MediatR;
using Microsoft.Extensions.Logging;
using NetworkFleetManagement.Application.Services;
using NetworkFleetManagement.Domain.Entities;
using System.Text.Json;

namespace NetworkFleetManagement.Application.Events;

// Event handlers for preferred partner domain events
public class PreferredPartnerActivatedEventHandler : INotificationHandler<PreferredPartnerActivatedEvent>
{
    private readonly ILogger<PreferredPartnerActivatedEventHandler> _logger;
    private readonly IIntegrationEventPublisher _eventPublisher;

    public PreferredPartnerActivatedEventHandler(
        ILogger<PreferredPartnerActivatedEventHandler> logger,
        IIntegrationEventPublisher eventPublisher)
    {
        _logger = logger;
        _eventPublisher = eventPublisher;
    }

    public async Task Handle(PreferredPartnerActivatedEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Preferred partner activated: UserId={UserId}, PartnerId={PartnerId}, PreferenceId={PreferenceId}, PartnerType={PartnerType}",
            notification.UserId, notification.PartnerId, notification.PreferenceId, notification.PartnerType);

        // Publish integration event to notify other services
        await _eventPublisher.PublishAsync("PreferredPartnerActivated", new
        {
            notification.UserId,
            notification.PartnerId,
            notification.PreferenceId,
            PartnerType = notification.PartnerType.ToString(),
            notification.OccurredOn
        }, cancellationToken);

        // Here you could also:
        // 1. Update analytics/reporting services
        // 2. Send notifications to relevant parties
        // 3. Update caches
        // 4. Trigger workflows in other services
    }
}

public class PreferredPartnerDeactivatedEventHandler : INotificationHandler<PreferredPartnerDeactivatedEvent>
{
    private readonly ILogger<PreferredPartnerDeactivatedEventHandler> _logger;
    private readonly IIntegrationEventPublisher _eventPublisher;

    public PreferredPartnerDeactivatedEventHandler(
        ILogger<PreferredPartnerDeactivatedEventHandler> logger,
        IIntegrationEventPublisher eventPublisher)
    {
        _logger = logger;
        _eventPublisher = eventPublisher;
    }

    public async Task Handle(PreferredPartnerDeactivatedEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Preferred partner deactivated: UserId={UserId}, PartnerId={PartnerId}, PreferenceId={PreferenceId}, PartnerType={PartnerType}, Reason={Reason}",
            notification.UserId, notification.PartnerId, notification.PreferenceId, notification.PartnerType, notification.Reason);

        await _eventPublisher.PublishAsync("PreferredPartnerDeactivated", new
        {
            notification.UserId,
            notification.PartnerId,
            notification.PreferenceId,
            PartnerType = notification.PartnerType.ToString(),
            notification.Reason,
            notification.OccurredOn
        }, cancellationToken);
    }
}

public class PreferredPartnerPreferenceLevelUpdatedEventHandler : INotificationHandler<PreferredPartnerPreferenceLevelUpdatedEvent>
{
    private readonly ILogger<PreferredPartnerPreferenceLevelUpdatedEventHandler> _logger;

    public PreferredPartnerPreferenceLevelUpdatedEventHandler(ILogger<PreferredPartnerPreferenceLevelUpdatedEventHandler> logger)
    {
        _logger = logger;
    }

    public async Task Handle(PreferredPartnerPreferenceLevelUpdatedEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Preferred partner preference level updated: UserId={UserId}, PartnerId={PartnerId}, PreferenceId={PreferenceId}, NewLevel={NewLevel}, NewPriority={NewPriority}",
            notification.UserId, notification.PartnerId, notification.PreferenceId, notification.NewLevel, notification.NewPriority);

        var integrationEvent = new
        {
            EventType = "PreferredPartnerPreferenceLevelUpdated",
            EventId = Guid.NewGuid(),
            Timestamp = DateTime.UtcNow,
            Data = new
            {
                notification.UserId,
                notification.PartnerId,
                notification.PreferenceId,
                NewLevel = notification.NewLevel.ToString(),
                notification.NewPriority
            }
        };

        _logger.LogInformation("Publishing integration event: {Event}", JsonSerializer.Serialize(integrationEvent));

        await Task.CompletedTask;
    }
}

public class PreferredPartnerExclusivityUpdatedEventHandler : INotificationHandler<PreferredPartnerExclusivityUpdatedEvent>
{
    private readonly ILogger<PreferredPartnerExclusivityUpdatedEventHandler> _logger;

    public PreferredPartnerExclusivityUpdatedEventHandler(ILogger<PreferredPartnerExclusivityUpdatedEventHandler> logger)
    {
        _logger = logger;
    }

    public async Task Handle(PreferredPartnerExclusivityUpdatedEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Preferred partner exclusivity updated: UserId={UserId}, PartnerId={PartnerId}, PreferenceId={PreferenceId}, IsExclusive={IsExclusive}, ExpiresAt={ExpiresAt}",
            notification.UserId, notification.PartnerId, notification.PreferenceId, notification.IsExclusive, notification.ExpiresAt);

        var integrationEvent = new
        {
            EventType = "PreferredPartnerExclusivityUpdated",
            EventId = Guid.NewGuid(),
            Timestamp = DateTime.UtcNow,
            Data = new
            {
                notification.UserId,
                notification.PartnerId,
                notification.PreferenceId,
                notification.IsExclusive,
                notification.ExpiresAt
            }
        };

        _logger.LogInformation("Publishing integration event: {Event}", JsonSerializer.Serialize(integrationEvent));

        await Task.CompletedTask;
    }
}

public class PreferredPartnerPerformanceChangedEventHandler : INotificationHandler<PreferredPartnerPerformanceChangedEvent>
{
    private readonly ILogger<PreferredPartnerPerformanceChangedEventHandler> _logger;

    public PreferredPartnerPerformanceChangedEventHandler(ILogger<PreferredPartnerPerformanceChangedEventHandler> logger)
    {
        _logger = logger;
    }

    public async Task Handle(PreferredPartnerPerformanceChangedEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Preferred partner performance changed: UserId={UserId}, PartnerId={PartnerId}, PreferenceId={PreferenceId}",
            notification.UserId, notification.PartnerId, notification.PreferenceId);

        var integrationEvent = new
        {
            EventType = "PreferredPartnerPerformanceChanged",
            EventId = Guid.NewGuid(),
            Timestamp = DateTime.UtcNow,
            Data = new
            {
                notification.UserId,
                notification.PartnerId,
                notification.PreferenceId,
                OldMetrics = new
                {
                    notification.OldMetrics.OverallRating,
                    notification.OldMetrics.OnTimePerformance,
                    notification.OldMetrics.QualityScore,
                    notification.OldMetrics.TotalOrders
                },
                NewMetrics = new
                {
                    notification.NewMetrics.OverallRating,
                    notification.NewMetrics.OnTimePerformance,
                    notification.NewMetrics.QualityScore,
                    notification.NewMetrics.TotalOrders
                }
            }
        };

        _logger.LogInformation("Publishing integration event: {Event}", JsonSerializer.Serialize(integrationEvent));

        await Task.CompletedTask;
    }
}
