using MobileWorkflow.Domain.Entities;

namespace MobileWorkflow.Domain.Repositories;

public interface IMobileAppRepository
{
    Task<MobileApp?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<MobileApp?> GetByPackageIdAsync(string packageId, CancellationToken cancellationToken = default);
    Task<IEnumerable<MobileApp>> GetByPlatformAsync(string platform, CancellationToken cancellationToken = default);
    Task<IEnumerable<MobileApp>> GetActiveAppsAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<MobileApp>> GetAllAsync(CancellationToken cancellationToken = default);
    Task<MobileApp> AddAsync(MobileApp mobileApp, CancellationToken cancellationToken = default);
    Task<MobileApp> UpdateAsync(MobileApp mobileApp, CancellationToken cancellationToken = default);
    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);
    Task<bool> ExistsAsync(Guid id, CancellationToken cancellationToken = default);
    Task<bool> ExistsByPackageIdAsync(string packageId, CancellationToken cancellationToken = default);
    Task<IEnumerable<MobileApp>> GetAppsWithFeaturesAsync(List<string> features, CancellationToken cancellationToken = default);
    Task<MobileApp?> GetLatestVersionAsync(string platform, CancellationToken cancellationToken = default);
}

public interface IMobileSessionRepository
{
    Task<MobileSession?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<MobileSession?> GetActiveSessionByUserIdAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<MobileSession?> GetActiveSessionByDeviceIdAsync(string deviceId, CancellationToken cancellationToken = default);
    Task<IEnumerable<MobileSession>> GetByUserIdAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<IEnumerable<MobileSession>> GetActiveSessionsAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<MobileSession>> GetSessionsByPlatformAsync(string platform, CancellationToken cancellationToken = default);
    Task<MobileSession> AddAsync(MobileSession session, CancellationToken cancellationToken = default);
    Task<MobileSession> UpdateAsync(MobileSession session, CancellationToken cancellationToken = default);
    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);
    Task<bool> ExistsAsync(Guid id, CancellationToken cancellationToken = default);
    Task<int> GetActiveSessionCountAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<MobileSession>> GetSessionsRequiringSyncAsync(CancellationToken cancellationToken = default);
}


