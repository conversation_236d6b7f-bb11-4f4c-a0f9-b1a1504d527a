# TLI Microservices Platform

## Overview

The Transport and Logistics India (TLI) Microservices Platform is a comprehensive, cloud-native solution designed to revolutionize the transportation and logistics industry in India. Built using modern microservices architecture with .NET 8, the platform provides scalable, reliable, and efficient services for managing the entire logistics ecosystem.

## 🏗️ Architecture

### Microservices Overview

| Service | Port | Description | Status |
|---------|------|-------------|--------|
| **Identity** | 7000 | Authentication & Authorization | ✅ Active |
| **User Management** | 7001 | User profiles, KYC, Company management | ✅ Active |
| **Order Management** | 7002 | RFQ, Orders, Quotes, Load management | ✅ Active |
| **Subscription Management** | 7003 | Plans, Billing, Feature flags | ✅ Active |
| **Trip Management** | 7004 | Trip tracking, POD, Route optimization | ✅ Active |
| **Network Fleet Management** | 7005 | Vehicle & Driver management | ✅ Active |
| **Financial Payment** | 7006 | Payments, Invoicing, Settlements | ✅ Active |
| **Communication Notification** | 7007 | SMS, Email, Push notifications | ✅ Active |
| **Analytics BI Service** | 7008 | Reports, Dashboards, Analytics | ✅ Active |
| **Data Storage** | 7009 | Document management, File storage | ✅ Active |
| **Monitoring Observability** | 7010 | Health checks, Metrics, Alerting | ✅ Active |
| **Audit Compliance** | 7011 | Audit trails, Compliance, Ratings | ✅ Active |
| **Mobile Workflow** | 7012 | Mobile apps, Workflow engine | ✅ Active |
| **API Gateway** | 5000 | Central entry point, Routing | ✅ Active |

### Technology Stack

- **Backend**: .NET 8, ASP.NET Core Web API
- **Architecture**: Clean Architecture, CQRS with MediatR
- **Database**: PostgreSQL with Entity Framework Core
- **Caching**: Redis
- **Message Broker**: RabbitMQ
- **API Gateway**: Ocelot
- **Authentication**: JWT Bearer tokens
- **Documentation**: Swagger/OpenAPI
- **Monitoring**: Prometheus, Grafana, Jaeger
- **Logging**: Serilog, Seq, Elasticsearch
- **Testing**: xUnit, Moq, FluentAssertions
- **Containerization**: Docker, Docker Compose

## 🚀 Quick Start

### Prerequisites

- .NET 8 SDK
- Docker Desktop
- PostgreSQL 15+
- Redis 7+
- RabbitMQ 3.12+
- Visual Studio 2022 or VS Code

### 1. Clone the Repository

```bash
git clone https://github.com/your-org/tli-microservices.git
cd tli-microservices
```

### 2. Start Infrastructure Services

```bash
# Start PostgreSQL, Redis, RabbitMQ, and monitoring tools
docker-compose -f docker-compose.infrastructure.yml up -d
```

### 3. Initialize Databases

```bash
# Run database setup and migrations
./scripts/initialize-databases.ps1
```

### 4. Start All Services

```bash
# Start all microservices
./scripts/start-all-services.ps1
```

### 5. Access the Platform

- **API Gateway**: https://localhost:5000
- **Swagger Documentation**: https://localhost:5000/swagger
- **Health Checks**: https://localhost:5000/health

## 📊 Monitoring & Observability

### Monitoring Stack

- **Grafana**: http://localhost:3000 (admin/admin123)
- **Prometheus**: http://localhost:9090
- **Jaeger Tracing**: http://localhost:16686
- **Kibana**: http://localhost:5601
- **Seq Logging**: http://localhost:5341

### Health Checks

Each service exposes health check endpoints:

- `/health` - Overall health status
- `/health/ready` - Readiness probe
- `/health/live` - Liveness probe

## 🔧 Development

### Project Structure

```
TLIMicroservices/
├── Services/                    # Individual microservices
│   ├── UserManagement/
│   ├── OrderManagement/
│   ├── SubscriptionManagement/
│   └── ...
├── Shared/                      # Shared libraries
│   ├── Shared.Domain/
│   ├── Shared.Infrastructure/
│   └── Shared.Messaging/
├── Identity/                    # Identity service
├── ApiGateway/                  # API Gateway
├── Tests/                       # Test projects
├── scripts/                     # Automation scripts
├── config/                      # Configuration files
├── docs/                        # Documentation
└── docker-compose.*.yml         # Docker compositions
```

### Service Structure (Clean Architecture)

Each microservice follows Clean Architecture principles:

```
ServiceName/
├── ServiceName.API/             # Web API layer
├── ServiceName.Application/     # Application layer (CQRS)
├── ServiceName.Domain/          # Domain layer
├── ServiceName.Infrastructure/  # Infrastructure layer
└── ServiceName.Tests/           # Unit & Integration tests
```

### Adding a New Service

1. Create service structure using the template
2. Update API Gateway routing in `ApiGateway/ocelot.json`
3. Add database configuration in `scripts/run-migrations.ps1`
4. Update startup scripts in `scripts/start-all-services.ps1`
5. Add monitoring configuration in `monitoring/prometheus.yml`

## 🧪 Testing

### Running Tests

```bash
# Run all tests
./scripts/run-tests.ps1

# Run specific test category
./scripts/run-tests.ps1 -TestType unit
./scripts/run-tests.ps1 -TestType integration
./scripts/run-tests.ps1 -TestType performance

# Run tests with coverage
./scripts/run-tests.ps1 -Coverage
```

### Test Categories

- **Unit Tests**: Fast, isolated tests for business logic
- **Integration Tests**: Tests with real dependencies
- **Performance Tests**: Load and stress testing

## 🔒 Security

### Authentication & Authorization

- JWT Bearer token authentication
- Role-based access control (RBAC)
- API key authentication for service-to-service
- OAuth 2.0 integration (Google, Microsoft)

### Security Features

- HTTPS enforcement
- CORS configuration
- Rate limiting
- Input validation
- SQL injection prevention
- XSS protection

## 📈 Performance

### Optimization Features

- Redis caching
- Database query optimization
- Connection pooling
- Async/await patterns
- Response compression
- CDN integration

### Scalability

- Horizontal scaling support
- Load balancing ready
- Database sharding support
- Event-driven architecture
- Circuit breaker pattern

## 🚀 Deployment

### Development Environment

```bash
# Start development environment
./scripts/start-all-services.ps1 -Environment Development
```

### Production Deployment

1. **Container Deployment**:
   ```bash
   docker-compose -f docker-compose.production.yml up -d
   ```

2. **Kubernetes Deployment**:
   ```bash
   kubectl apply -f k8s/
   ```

3. **Azure Container Apps**:
   ```bash
   az containerapp up --source .
   ```

### Environment Variables

Key environment variables for production:

```bash
# Database
DB_HOST=your-postgres-host
DB_USER=your-db-user
DB_PASSWORD=your-db-password

# Authentication
JWT_SECRET_KEY=your-jwt-secret

# External Services
SENDGRID_API_KEY=your-sendgrid-key
TWILIO_ACCOUNT_SID=your-twilio-sid
RAZORPAY_KEY_ID=your-razorpay-key
```

## 📚 API Documentation

### Swagger/OpenAPI

Each service provides comprehensive API documentation:

- **API Gateway**: https://localhost:5000/swagger
- **Individual Services**: https://localhost:{port}/swagger

### Postman Collection

Import the Postman collection from `docs/postman/TLI-Microservices.postman_collection.json`

## 🤝 Contributing

### Development Workflow

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run the test suite
6. Submit a pull request

### Code Standards

- Follow C# coding conventions
- Use Clean Architecture principles
- Write comprehensive tests
- Document public APIs
- Follow semantic versioning

## 📞 Support

### Getting Help

- **Documentation**: Check the `docs/` directory
- **Issues**: Create an issue on GitHub
- **Discussions**: Use GitHub Discussions
- **Email**: <EMAIL>

### Troubleshooting

Common issues and solutions:

1. **Services not starting**: Check Docker containers are running
2. **Database connection errors**: Verify PostgreSQL is accessible
3. **Authentication failures**: Check JWT configuration
4. **Performance issues**: Review monitoring dashboards

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- .NET Community for excellent frameworks
- Open source contributors
- TLI development team

---

**Built with ❤️ by the TLI Development Team**
