namespace CommunicationNotification.Domain.Enums;

public enum Priority
{
    /// <summary>
    /// Low priority - can be delayed
    /// </summary>
    Low = 1,
    
    /// <summary>
    /// Normal priority - standard delivery
    /// </summary>
    Normal = 2,
    
    /// <summary>
    /// High priority - expedited delivery
    /// </summary>
    High = 3,
    
    /// <summary>
    /// Critical priority - immediate delivery required
    /// </summary>
    Critical = 4,
    
    /// <summary>
    /// Emergency - highest priority, bypass normal queues
    /// </summary>
    Emergency = 5
}
