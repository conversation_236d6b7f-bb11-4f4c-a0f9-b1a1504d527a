{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=TLI_MonitoringObservability;User Id=timescale;Password=timescale"}, "JwtSettings": {"Secret": "your-super-secret-jwt-key-that-is-at-least-32-characters-long", "Issuer": "TLI.Identity", "Audience": "TLI.Services", "ExpiryMinutes": 60}, "MonitoringSettings": {"HealthCheckInterval": "00:00:30", "MetricRetentionDays": 30, "AlertRetentionDays": 90, "ThresholdEvaluationInterval": "00:01:00", "DataCleanupInterval": "06:00:00", "DefaultMetricAggregationInterval": "00:01:00"}, "NotificationSettings": {"Email": {"SmtpServer": "smtp.gmail.com", "SmtpPort": 587, "Username": "", "Password": "", "FromAddress": "<EMAIL>", "FromName": "TLI Monitoring"}, "SMS": {"Provider": "<PERSON><PERSON><PERSON>", "AccountSid": "", "AuthToken": "", "FromNumber": ""}, "Slack": {"WebhookUrl": "", "Channel": "#alerts", "Username": "TLI-Monitor"}, "Teams": {"WebhookUrl": ""}, "Webhook": {"DefaultTimeout": "00:00:30", "RetryAttempts": 3}}, "AlertingRules": {"AutoEscalationEnabled": true, "EscalationTimeoutMinutes": 30, "MaxEscalationLevel": 3, "AutoIncidentCreation": {"Enabled": true, "CriticalAlertThreshold": 1, "WarningAlertThreshold": 5, "TimeWindowMinutes": 15}}, "PerformanceThresholds": {"ResponseTime": {"WarningMs": 1000, "CriticalMs": 5000}, "ErrorRate": {"WarningPercent": 5.0, "CriticalPercent": 20.0}, "CpuUsage": {"WarningPercent": 80.0, "CriticalPercent": 95.0}, "MemoryUsage": {"WarningPercent": 85.0, "CriticalPercent": 95.0}, "DiskUsage": {"WarningPercent": 90.0, "CriticalPercent": 95.0}}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/monitoring-.log", "rollingInterval": "Day", "retainedFileCountLimit": 7, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}]}}