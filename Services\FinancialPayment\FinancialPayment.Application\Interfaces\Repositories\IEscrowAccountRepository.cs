using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.Enums;

namespace FinancialPayment.Application.Interfaces.Repositories;

public interface IEscrowAccountRepository
{
    Task<EscrowAccount?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<EscrowAccount?> GetByIdWithTransactionsAsync(Guid id, CancellationToken cancellationToken = default);
    Task<EscrowAccount?> GetByIdWithMilestonesAsync(Guid id, CancellationToken cancellationToken = default);
    Task<EscrowAccount?> GetByOrderIdAsync(Guid orderId, CancellationToken cancellationToken = default);
    Task<EscrowAccount?> GetByEscrowAccountNumberAsync(string escrowAccountNumber, CancellationToken cancellationToken = default);
    Task<List<EscrowAccount>> GetByTransportCompanyIdAsync(Guid transportCompanyId, CancellationToken cancellationToken = default);
    Task<List<EscrowAccount>> GetByBrokerIdAsync(Guid brokerId, CancellationToken cancellationToken = default);
    Task<List<EscrowAccount>> GetByCarrierIdAsync(Guid carrierId, CancellationToken cancellationToken = default);
    Task<List<EscrowAccount>> GetByStatusAsync(EscrowStatus status, CancellationToken cancellationToken = default);
    Task<List<EscrowAccount>> GetByDateRangeAsync(DateTime from, DateTime to, CancellationToken cancellationToken = default);
    Task<EscrowAccount> AddAsync(EscrowAccount escrowAccount, CancellationToken cancellationToken = default);
    Task UpdateAsync(EscrowAccount escrowAccount, CancellationToken cancellationToken = default);
    void Update(EscrowAccount escrowAccount);
    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);
    Task<bool> ExistsAsync(Guid id, CancellationToken cancellationToken = default);
    Task<decimal> GetTotalEscrowAmountAsync(Guid participantId, CancellationToken cancellationToken = default);
    Task<List<EscrowAccount>> GetOverdueEscrowAccountsAsync(CancellationToken cancellationToken = default);
}
