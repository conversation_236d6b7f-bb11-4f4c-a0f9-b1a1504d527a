using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Repositories;

namespace NetworkFleetManagement.Application.Queries.PreferredPartners;

public class GetPreferredPartnersByRoleQueryHandler : IRequestHandler<GetPreferredPartnersByRoleQuery, List<PreferredPartnerSummaryDto>>
{
    private readonly IPreferredPartnerRepository _preferredPartnerRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetPreferredPartnersByRoleQueryHandler> _logger;

    public GetPreferredPartnersByRoleQueryHandler(
        IPreferredPartnerRepository preferredPartnerRepository,
        IMapper mapper,
        ILogger<GetPreferredPartnersByRoleQueryHandler> logger)
    {
        _preferredPartnerRepository = preferredPartnerRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<List<PreferredPartnerSummaryDto>> Handle(GetPreferredPartnersByRoleQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var partners = await _preferredPartnerRepository.GetByUserIdAndPartnerTypeAsync(
                request.UserId, 
                request.PartnerType, 
                request.ActiveOnly, 
                cancellationToken);

            var result = _mapper.Map<List<PreferredPartnerSummaryDto>>(partners);

            if (request.Limit.HasValue)
            {
                result = result.Take(request.Limit.Value).ToList();
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving preferred partners by role for user {UserId}, partner type {PartnerType}", 
                request.UserId, request.PartnerType);
            throw;
        }
    }
}
