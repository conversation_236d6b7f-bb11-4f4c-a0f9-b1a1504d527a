using Shared.Domain.Common;

namespace TripManagement.Domain.Events;

/// <summary>
/// Domain event raised when a service zone master is created
/// </summary>
public class ServiceZoneMasterCreatedEvent : DomainEvent
{
    public Guid ServiceZoneMasterId { get; }
    public string Code { get; }
    public string Name { get; }
    public string ZoneType { get; }
    public string Country { get; }

    public ServiceZoneMasterCreatedEvent(Guid serviceZoneMasterId, string code, string name, string zoneType, string country)
    {
        ServiceZoneMasterId = serviceZoneMasterId;
        Code = code;
        Name = name;
        ZoneType = zoneType;
        Country = country;
    }
}

/// <summary>
/// Domain event raised when a service zone master is updated
/// </summary>
public class ServiceZoneMasterUpdatedEvent : DomainEvent
{
    public Guid ServiceZoneMasterId { get; }
    public string Code { get; }
    public string Name { get; }
    public string ZoneType { get; }
    public string OldName { get; }
    public string OldZoneType { get; }

    public ServiceZoneMasterUpdatedEvent(Guid serviceZoneMasterId, string code, string name, string zoneType, string oldName, string oldZoneType)
    {
        ServiceZoneMasterId = serviceZoneMasterId;
        Code = code;
        Name = name;
        ZoneType = zoneType;
        OldName = oldName;
        OldZoneType = oldZoneType;
    }
}

/// <summary>
/// Domain event raised when a service zone master is activated
/// </summary>
public class ServiceZoneMasterActivatedEvent : DomainEvent
{
    public Guid ServiceZoneMasterId { get; }
    public string Code { get; }
    public string Name { get; }

    public ServiceZoneMasterActivatedEvent(Guid serviceZoneMasterId, string code, string name)
    {
        ServiceZoneMasterId = serviceZoneMasterId;
        Code = code;
        Name = name;
    }
}

/// <summary>
/// Domain event raised when a service zone master is deactivated
/// </summary>
public class ServiceZoneMasterDeactivatedEvent : DomainEvent
{
    public Guid ServiceZoneMasterId { get; }
    public string Code { get; }
    public string Name { get; }

    public ServiceZoneMasterDeactivatedEvent(Guid serviceZoneMasterId, string code, string name)
    {
        ServiceZoneMasterId = serviceZoneMasterId;
        Code = code;
        Name = name;
    }
}


