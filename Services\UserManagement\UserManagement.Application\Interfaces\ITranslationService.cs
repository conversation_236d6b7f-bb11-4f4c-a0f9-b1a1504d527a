namespace UserManagement.Application.Interfaces;

public interface ITranslationService
{
    Task<string> TranslateAsync(string text, string fromLanguage, string toLanguage);
    Task<Dictionary<string, string>> TranslateBatchAsync(Dictionary<string, string> texts, string fromLanguage, string toLanguage);
    Task<bool> IsServiceAvailableAsync(CancellationToken cancellationToken = default);
    Task<decimal> GetTranslationQualityAsync(string fromLanguage, string toLanguage, CancellationToken cancellationToken = default);
}
