using MediatR;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Entities;

namespace NetworkFleetManagement.Application.Commands.CreatePreferredPartnerWishlist;

public class CreatePreferredPartnerWishlistCommand : IRequest<Guid>
{
    public Guid UserId { get; set; }
    public string WishlistName { get; set; } = string.Empty;
    public string? Description { get; set; }
    public PreferredPartnerType PartnerType { get; set; }
    public List<WishlistPartnerRequest> Partners { get; set; } = new();
    public WishlistSettings Settings { get; set; } = new();
    public Guid RequestingUserId { get; set; }
}

public class WishlistPartnerRequest
{
    public Guid PartnerId { get; set; }
    public PreferenceLevel PreferenceLevel { get; set; } = PreferenceLevel.Secondary;
    public int Priority { get; set; } = 10;
    public decimal? PreferredCommissionRate { get; set; }
    public decimal? PreferredServiceRate { get; set; }
    public bool AutoAssignEnabled { get; set; } = false;
    public decimal? AutoAssignThreshold { get; set; }
    public List<string> PreferredRoutes { get; set; } = new();
    public List<string> PreferredLoadTypes { get; set; } = new();
    public List<string> ExcludedRoutes { get; set; } = new();
    public List<string> ExcludedLoadTypes { get; set; } = new();
    public string? Notes { get; set; }
    public bool NotifyOnNewOpportunities { get; set; } = true;
    public bool NotifyOnPerformanceChanges { get; set; } = true;
}

public class WishlistSettings
{
    public bool AutoAddHighPerformers { get; set; } = false;
    public decimal? AutoAddThreshold { get; set; }
    public bool AutoRemovePoorPerformers { get; set; } = false;
    public decimal? AutoRemoveThreshold { get; set; }
    public int? MaxPartners { get; set; }
    public bool EnablePerformanceAlerts { get; set; } = true;
    public bool EnableContractExpiryAlerts { get; set; } = true;
    public int? AlertDaysBefore { get; set; } = 30;
}
