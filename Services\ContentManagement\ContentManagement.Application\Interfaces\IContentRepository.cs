using ContentManagement.Domain.Entities;
using ContentManagement.Domain.Enums;

namespace ContentManagement.Application.Interfaces;

/// <summary>
/// Repository interface for content management
/// </summary>
public interface IContentRepository
{
    // Basic CRUD operations
    Task<Content?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<Content?> GetBySlugAsync(string slug, CancellationToken cancellationToken = default);
    Task<List<Content>> GetAllAsync(CancellationToken cancellationToken = default);
    Task AddAsync(Content content, CancellationToken cancellationToken = default);
    Task UpdateAsync(Content content, CancellationToken cancellationToken = default);
    Task DeleteAsync(Content content, CancellationToken cancellationToken = default);

    // Search and filtering
    Task<(List<Content> Items, int TotalCount)> SearchAsync(
        string? searchTerm = null,
        ContentType? type = null,
        ContentStatus? status = null,
        List<string>? tags = null,
        Guid? createdBy = null,
        DateTime? createdFrom = null,
        DateTime? createdTo = null,
        DateTime? updatedFrom = null,
        DateTime? updatedTo = null,
        List<string>? userRoles = null,
        bool isAuthenticated = false,
        string sortBy = "CreatedAt",
        bool sortDescending = true,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default);

    // Content-specific queries
    Task<List<Content>> GetByTypeAsync(ContentType type, CancellationToken cancellationToken = default);
    Task<List<Content>> GetByStatusAsync(ContentStatus status, CancellationToken cancellationToken = default);
    Task<List<Content>> GetByCreatorAsync(Guid createdBy, CancellationToken cancellationToken = default);
    Task<List<Content>> GetPendingApprovalAsync(CancellationToken cancellationToken = default);
    Task<List<Content>> GetPublishedAsync(CancellationToken cancellationToken = default);
    Task<List<Content>> GetScheduledForPublishingAsync(DateTime? beforeDate = null, CancellationToken cancellationToken = default);

    // Slug management
    Task<bool> IsSlugUniqueAsync(string slug, Guid? excludeId = null, CancellationToken cancellationToken = default);
    Task<string> GenerateUniqueSlugAsync(string baseSlug, CancellationToken cancellationToken = default);

    // Statistics
    Task<Dictionary<ContentStatus, int>> GetContentCountByStatusAsync(CancellationToken cancellationToken = default);
    Task<Dictionary<ContentType, int>> GetContentCountByTypeAsync(CancellationToken cancellationToken = default);
    Task<Dictionary<string, int>> GetContentCountByCreatorAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for FAQ items
/// </summary>
public interface IFaqRepository
{
    // Basic CRUD operations
    Task<FaqItem?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<List<FaqItem>> GetAllAsync(CancellationToken cancellationToken = default);
    Task AddAsync(FaqItem faqItem, CancellationToken cancellationToken = default);
    Task UpdateAsync(FaqItem faqItem, CancellationToken cancellationToken = default);
    Task DeleteAsync(FaqItem faqItem, CancellationToken cancellationToken = default);

    // FAQ-specific queries
    Task<(List<FaqItem> Items, int TotalCount)> SearchAsync(
        string? searchTerm = null,
        FaqCategory? category = null,
        ContentStatus? status = null,
        bool? isHighlighted = null,
        List<string>? tags = null,
        List<string>? userRoles = null,
        bool isAuthenticated = false,
        string sortBy = "SortOrder",
        bool sortDescending = false,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default);

    Task<List<FaqItem>> GetByCategoryAsync(FaqCategory category, CancellationToken cancellationToken = default);
    Task<List<FaqItem>> GetHighlightedAsync(int? limit = null, CancellationToken cancellationToken = default);
    Task<List<FaqItem>> GetMostViewedAsync(int? limit = null, FaqCategory? category = null, DateTime? fromDate = null, CancellationToken cancellationToken = default);
    Task<List<FaqItem>> GetHighestRatedAsync(int? limit = null, FaqCategory? category = null, int? minimumRatings = null, CancellationToken cancellationToken = default);

    // Statistics
    Task<Dictionary<FaqCategory, int>> GetFaqCountByCategoryAsync(CancellationToken cancellationToken = default);
    Task<Dictionary<FaqCategory, decimal>> GetAverageRatingByCategoryAsync(CancellationToken cancellationToken = default);
    Task<Dictionary<FaqCategory, int>> GetTotalViewsByCategoryAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for policy documents
/// </summary>
public interface IPolicyRepository
{
    // Basic CRUD operations
    Task<PolicyDocument?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<List<PolicyDocument>> GetAllAsync(CancellationToken cancellationToken = default);
    Task AddAsync(PolicyDocument policyDocument, CancellationToken cancellationToken = default);
    Task UpdateAsync(PolicyDocument policyDocument, CancellationToken cancellationToken = default);
    Task DeleteAsync(PolicyDocument policyDocument, CancellationToken cancellationToken = default);

    // Policy-specific queries
    Task<PolicyDocument?> GetByTypeAsync(PolicyType policyType, bool currentVersionOnly = true, CancellationToken cancellationToken = default);
    Task<List<PolicyDocument>> GetVersionsAsync(PolicyType policyType, CancellationToken cancellationToken = default);
    Task<List<PolicyDocument>> GetApplicablePoliciesAsync(List<string> userRoles, bool requiresAcceptanceOnly = false, bool currentVersionOnly = true, bool effectiveOnly = true, CancellationToken cancellationToken = default);
    Task<List<PolicyDocument>> GetPoliciesRequiringAcceptanceAsync(List<string> userRoles, bool currentVersionOnly = true, bool effectiveOnly = true, CancellationToken cancellationToken = default);

    // Search and filtering
    Task<(List<PolicyDocument> Items, int TotalCount)> SearchAsync(
        string? searchTerm = null,
        PolicyType? policyType = null,
        ContentStatus? status = null,
        bool? requiresAcceptance = null,
        bool? isCurrentVersion = null,
        DateTime? effectiveFrom = null,
        DateTime? effectiveTo = null,
        List<string>? userRoles = null,
        bool isAuthenticated = false,
        string sortBy = "EffectiveDate",
        bool sortDescending = true,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default);

    // Statistics
    Task<Dictionary<PolicyType, int>> GetPolicyCountByTypeAsync(CancellationToken cancellationToken = default);
    Task<Dictionary<PolicyType, decimal>> GetComplianceRateByTypeAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for policy acceptances
/// </summary>
public interface IPolicyAcceptanceRepository
{
    Task<PolicyAcceptance?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<List<PolicyAcceptance>> GetByPolicyIdAsync(Guid policyId, CancellationToken cancellationToken = default);
    Task<List<PolicyAcceptance>> GetByUserIdAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<PolicyAcceptance?> GetByPolicyAndUserAsync(Guid policyId, Guid userId, CancellationToken cancellationToken = default);
    Task AddAsync(PolicyAcceptance acceptance, CancellationToken cancellationToken = default);
    Task<bool> HasUserAcceptedPolicyAsync(Guid policyId, Guid userId, CancellationToken cancellationToken = default);
    Task<int> GetAcceptanceCountAsync(Guid policyId, CancellationToken cancellationToken = default);
    Task<List<PolicyAcceptance>> GetAcceptanceHistoryAsync(
        Guid? policyId = null,
        Guid? userId = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        string sortBy = "AcceptedAt",
        bool sortDescending = true,
        int pageNumber = 1,
        int pageSize = 50,
        CancellationToken cancellationToken = default);
}
