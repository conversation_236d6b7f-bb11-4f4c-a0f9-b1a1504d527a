{"TestConfiguration": {"Environment": "Testing", "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=tli_test;Username=testuser;Password=testpass", "Redis": "localhost:6379", "RabbitMQ": "amqp://testuser:testpass@localhost:5672/"}, "TestContainers": {"PostgreSQL": {"Image": "postgres:15", "Database": "tli_test", "Username": "testuser", "Password": "testpass", "Port": 5432}, "Redis": {"Image": "redis:7-alpine", "Port": 6379}, "RabbitMQ": {"Image": "rabbitmq:3-management", "Username": "testuser", "Password": "testpass", "Port": 5672, "ManagementPort": 15672}}, "Performance": {"LoadTest": {"DefaultConcurrentUsers": 10, "DefaultDuration": "00:01:00", "MaxResponseTime": "00:00:05", "MinOperationsPerSecond": 100, "MaxErrorRate": 0.01}, "Benchmarks": {"DatabaseOperations": {"MaxInsertTime": "00:00:00.100", "MaxQueryTime": "00:00:00.050", "MaxUpdateTime": "00:00:00.100"}, "ApiEndpoints": {"MaxResponseTime": "00:00:01", "MaxMemoryUsage": 10485760}}}, "Integration": {"Services": [{"Name": "UserManagement", "BaseUrl": "https://localhost:7001", "HealthCheckEndpoint": "/health", "Timeout": "00:00:30"}, {"Name": "OrderManagement", "BaseUrl": "https://localhost:7002", "HealthCheckEndpoint": "/health", "Timeout": "00:00:30"}, {"Name": "SubscriptionManagement", "BaseUrl": "https://localhost:7003", "HealthCheckEndpoint": "/health", "Timeout": "00:00:30"}, {"Name": "TripManagement", "BaseUrl": "https://localhost:7004", "HealthCheckEndpoint": "/health", "Timeout": "00:00:30"}, {"Name": "NetworkFleetManagement", "BaseUrl": "https://localhost:7005", "HealthCheckEndpoint": "/health", "Timeout": "00:00:30"}, {"Name": "FinancialPayment", "BaseUrl": "https://localhost:7006", "HealthCheckEndpoint": "/health", "Timeout": "00:00:30"}, {"Name": "CommunicationNotification", "BaseUrl": "https://localhost:7007", "HealthCheckEndpoint": "/health", "Timeout": "00:00:30"}, {"Name": "AnalyticsBIService", "BaseUrl": "https://localhost:7008", "HealthCheckEndpoint": "/health", "Timeout": "00:00:30"}, {"Name": "DataStorage", "BaseUrl": "https://localhost:7009", "HealthCheckEndpoint": "/health", "Timeout": "00:00:30"}, {"Name": "MonitoringObservability", "BaseUrl": "https://localhost:7010", "HealthCheckEndpoint": "/health", "Timeout": "00:00:30"}, {"Name": "AuditCompliance", "BaseUrl": "https://localhost:7011", "HealthCheckEndpoint": "/health", "Timeout": "00:00:30"}, {"Name": "MobileWorkflow", "BaseUrl": "https://localhost:7012", "HealthCheckEndpoint": "/health", "Timeout": "00:00:30"}]}, "TestData": {"SeedData": {"Users": 100, "Orders": 500, "Subscriptions": 50, "Trips": 200}, "CleanupAfterTests": true, "UseInMemoryDatabase": false}, "Parallel": {"MaxDegreeOfParallelism": 4, "EnableParallelExecution": true}, "Timeouts": {"DefaultTestTimeout": "00:05:00", "IntegrationTestTimeout": "00:10:00", "PerformanceTestTimeout": "00:15:00"}}}