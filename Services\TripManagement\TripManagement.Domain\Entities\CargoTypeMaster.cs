using Shared.Domain.Common;
using TripManagement.Domain.Events;

namespace TripManagement.Domain.Entities;

/// <summary>
/// Master data entity for cargo types
/// </summary>
public class CargoTypeMaster : AggregateRoot
{
    public string Code { get; private set; } = string.Empty;
    public string Name { get; private set; } = string.Empty;
    public string Description { get; private set; } = string.Empty;
    public string Category { get; private set; } = string.Empty;
    public bool RequiresSpecialHandling { get; private set; }
    public bool IsHazardous { get; private set; }
    public bool RequiresTemperatureControl { get; private set; }
    public decimal? MinTemperatureCelsius { get; private set; }
    public decimal? MaxTemperatureCelsius { get; private set; }
    public string? HandlingInstructions { get; private set; }
    public string? SafetyRequirements { get; private set; }
    public bool IsActive { get; private set; }
    public int SortOrder { get; private set; }
    public string? IconUrl { get; private set; }
    public Dictionary<string, object> AdditionalProperties { get; private set; } = new();

    // Private constructor for EF Core
    private CargoTypeMaster() { }

    public CargoTypeMaster(
        string code,
        string name,
        string description,
        string category,
        bool requiresSpecialHandling = false,
        bool isHazardous = false,
        bool requiresTemperatureControl = false,
        decimal? minTemperatureCelsius = null,
        decimal? maxTemperatureCelsius = null,
        string? handlingInstructions = null,
        string? safetyRequirements = null,
        int sortOrder = 0,
        string? iconUrl = null,
        Dictionary<string, object>? additionalProperties = null)
    {
        if (string.IsNullOrWhiteSpace(code))
            throw new ArgumentException("Cargo type code cannot be empty", nameof(code));

        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Cargo type name cannot be empty", nameof(name));

        if (string.IsNullOrWhiteSpace(category))
            throw new ArgumentException("Cargo type category cannot be empty", nameof(category));

        Code = code.Trim().ToUpperInvariant();
        Name = name.Trim();
        Description = description?.Trim() ?? string.Empty;
        Category = category.Trim();
        RequiresSpecialHandling = requiresSpecialHandling;
        IsHazardous = isHazardous;
        RequiresTemperatureControl = requiresTemperatureControl;
        MinTemperatureCelsius = minTemperatureCelsius;
        MaxTemperatureCelsius = maxTemperatureCelsius;
        HandlingInstructions = handlingInstructions?.Trim();
        SafetyRequirements = safetyRequirements?.Trim();
        IsActive = true;
        SortOrder = sortOrder;
        IconUrl = iconUrl?.Trim();
        AdditionalProperties = additionalProperties ?? new Dictionary<string, object>();

        // Validate temperature ranges
        ValidateTemperatureRanges();

        // Add domain event
        AddDomainEvent(new CargoTypeMasterCreatedEvent(Id, Code, Name, Category));
    }

    public void UpdateDetails(
        string name,
        string description,
        string category,
        bool requiresSpecialHandling = false,
        bool isHazardous = false,
        bool requiresTemperatureControl = false,
        decimal? minTemperatureCelsius = null,
        decimal? maxTemperatureCelsius = null,
        string? handlingInstructions = null,
        string? safetyRequirements = null,
        int sortOrder = 0,
        string? iconUrl = null,
        Dictionary<string, object>? additionalProperties = null)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Cargo type name cannot be empty", nameof(name));

        if (string.IsNullOrWhiteSpace(category))
            throw new ArgumentException("Cargo type category cannot be empty", nameof(category));

        var oldName = Name;
        var oldCategory = Category;

        Name = name.Trim();
        Description = description?.Trim() ?? string.Empty;
        Category = category.Trim();
        RequiresSpecialHandling = requiresSpecialHandling;
        IsHazardous = isHazardous;
        RequiresTemperatureControl = requiresTemperatureControl;
        MinTemperatureCelsius = minTemperatureCelsius;
        MaxTemperatureCelsius = maxTemperatureCelsius;
        HandlingInstructions = handlingInstructions?.Trim();
        SafetyRequirements = safetyRequirements?.Trim();
        SortOrder = sortOrder;
        IconUrl = iconUrl?.Trim();

        if (additionalProperties != null)
        {
            AdditionalProperties = additionalProperties;
        }

        // Validate temperature ranges
        ValidateTemperatureRanges();

        SetUpdatedAt();

        // Add domain event if significant changes
        if (oldName != Name || oldCategory != Category)
        {
            AddDomainEvent(new CargoTypeMasterUpdatedEvent(Id, Code, Name, Category, oldName, oldCategory));
        }
    }

    public void Activate()
    {
        if (IsActive) return;

        IsActive = true;
        SetUpdatedAt();

        AddDomainEvent(new CargoTypeMasterActivatedEvent(Id, Code, Name));
    }

    public void Deactivate()
    {
        if (!IsActive) return;

        IsActive = false;
        SetUpdatedAt();

        AddDomainEvent(new CargoTypeMasterDeactivatedEvent(Id, Code, Name));
    }

    public void UpdateSortOrder(int newSortOrder)
    {
        if (SortOrder == newSortOrder) return;

        SortOrder = newSortOrder;
        SetUpdatedAt();
    }

    public void AddOrUpdateProperty(string key, object value)
    {
        if (string.IsNullOrWhiteSpace(key))
            throw new ArgumentException("Property key cannot be empty", nameof(key));

        AdditionalProperties[key] = value;
        SetUpdatedAt();
    }

    public void RemoveProperty(string key)
    {
        if (string.IsNullOrWhiteSpace(key)) return;

        if (AdditionalProperties.Remove(key))
        {
            SetUpdatedAt();
        }
    }

    public bool IsTemperatureInRange(decimal temperatureCelsius)
    {
        if (!RequiresTemperatureControl) return true;

        var minInRange = !MinTemperatureCelsius.HasValue || temperatureCelsius >= MinTemperatureCelsius.Value;
        var maxInRange = !MaxTemperatureCelsius.HasValue || temperatureCelsius <= MaxTemperatureCelsius.Value;

        return minInRange && maxInRange;
    }

    public bool RequiresSpecialVehicle()
    {
        return RequiresSpecialHandling || IsHazardous || RequiresTemperatureControl;
    }

    private void ValidateTemperatureRanges()
    {
        if (MinTemperatureCelsius.HasValue && MaxTemperatureCelsius.HasValue &&
            MinTemperatureCelsius.Value > MaxTemperatureCelsius.Value)
            throw new ArgumentException("Minimum temperature cannot be greater than maximum temperature");
    }
}


