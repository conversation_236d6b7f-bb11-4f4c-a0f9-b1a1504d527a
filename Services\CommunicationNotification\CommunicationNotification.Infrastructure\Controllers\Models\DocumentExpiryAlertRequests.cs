using CommunicationNotification.Domain.Enums;
using System.ComponentModel.DataAnnotations;

namespace CommunicationNotification.Infrastructure.Controllers.Models;

/// <summary>
/// Request model for sending document expiry alert
/// </summary>
public class SendDocumentExpiryAlertRequest
{
    [Required]
    public Guid CarrierId { get; set; }

    [Required]
    [StringLength(100)]
    public string DocumentType { get; set; } = string.Empty;

    [Required]
    [StringLength(50)]
    public string EntityType { get; set; } = string.Empty;

    [Required]
    public Guid EntityId { get; set; }

    [Required]
    [StringLength(200)]
    public string EntityName { get; set; } = string.Empty;

    [Required]
    public DateTime ExpiryDate { get; set; }

    [Range(0, int.MaxValue)]
    public int DaysUntilExpiry { get; set; }

    [Required]
    [StringLength(50)]
    public string AlertType { get; set; } = string.Empty;

    [Range(0, 365)]
    public int ThresholdDays { get; set; }

    public Dictionary<string, object> DocumentDetails { get; set; } = new();
    public List<NotificationChannel> PreferredChannels { get; set; } = new();
    public Priority Priority { get; set; } = Priority.Normal;
    public bool RequireAcknowledgment { get; set; } = false;
    public List<string> Tags { get; set; } = new();
    public string? CorrelationId { get; set; }
}

/// <summary>
/// Request model for sending bulk document expiry alerts
/// </summary>
public class SendBulkDocumentExpiryAlertsRequest
{
    [Required]
    public Guid CarrierId { get; set; }

    [Required]
    [MinLength(1)]
    public List<DocumentExpiryAlertRequestInfo> ExpiringDocuments { get; set; } = new();

    [Range(0, 365)]
    public int ThresholdDays { get; set; }

    [Required]
    [StringLength(50)]
    public string AlertType { get; set; } = string.Empty;

    public List<NotificationChannel> PreferredChannels { get; set; } = new();
    public Priority Priority { get; set; } = Priority.Normal;
    public bool RequireAcknowledgment { get; set; } = false;
    public bool GroupByEntityType { get; set; } = true;
    public List<string> Tags { get; set; } = new();
    public string? CorrelationId { get; set; }
}

/// <summary>
/// Request model for scheduling document expiry monitoring
/// </summary>
public class ScheduleDocumentExpiryMonitoringRequest
{
    [Required]
    public Guid CarrierId { get; set; }

    public List<int> AlertThresholds { get; set; } = new() { 30, 15, 7, 0 };
    public List<string> DocumentTypes { get; set; } = new();
    public List<string> EntityTypes { get; set; } = new();

    [StringLength(100)]
    public string CronExpression { get; set; } = "0 9 * * *";

    public bool IsActive { get; set; } = true;
    public List<NotificationChannel> PreferredChannels { get; set; } = new();
    public Dictionary<string, object> MonitoringSettings { get; set; } = new();
    public string? CorrelationId { get; set; }
}

/// <summary>
/// Document expiry alert information for bulk requests
/// </summary>
public class DocumentExpiryAlertRequestInfo
{
    [Required]
    [StringLength(100)]
    public string DocumentType { get; set; } = string.Empty;

    [Required]
    [StringLength(50)]
    public string EntityType { get; set; } = string.Empty;

    [Required]
    public Guid EntityId { get; set; }

    [Required]
    [StringLength(200)]
    public string EntityName { get; set; } = string.Empty;

    [Required]
    public DateTime ExpiryDate { get; set; }

    [Range(0, int.MaxValue)]
    public int DaysUntilExpiry { get; set; }

    public Dictionary<string, object> DocumentDetails { get; set; } = new();
}

/// <summary>
/// Request model for sending performance alert
/// </summary>
public class SendPerformanceAlertRequest
{
    [Required]
    public Guid CarrierId { get; set; }

    [Required]
    [StringLength(100)]
    public string PerformanceMetric { get; set; } = string.Empty;

    [Required]
    [StringLength(50)]
    public string AlertType { get; set; } = string.Empty;

    [Required]
    public decimal CurrentValue { get; set; }

    public decimal? PreviousValue { get; set; }
    public decimal? TargetValue { get; set; }
    public decimal? ThresholdValue { get; set; }

    [Required]
    [StringLength(50)]
    public string ChangeDirection { get; set; } = string.Empty;

    public decimal ChangePercentage { get; set; }

    [Required]
    [StringLength(50)]
    public string TimePeriod { get; set; } = string.Empty;

    [Required]
    public DateTime MeasurementDate { get; set; }

    public Dictionary<string, object> PerformanceData { get; set; } = new();
    public List<NotificationChannel> PreferredChannels { get; set; } = new();
    public Priority Priority { get; set; } = Priority.Normal;
    public bool RequireAcknowledgment { get; set; } = false;
    public List<string> Tags { get; set; } = new();
    public string? CorrelationId { get; set; }
}

/// <summary>
/// Request model for sending rating change alert
/// </summary>
public class SendRatingChangeAlertRequest
{
    [Required]
    public Guid CarrierId { get; set; }

    [Required]
    [Range(0, 5)]
    public decimal CurrentRating { get; set; }

    [Required]
    [Range(0, 5)]
    public decimal PreviousRating { get; set; }

    [Required]
    public decimal RatingChange { get; set; }

    [Required]
    [StringLength(100)]
    public string RatingCategory { get; set; } = string.Empty;

    [Required]
    [StringLength(50)]
    public string ChangeType { get; set; } = string.Empty;

    [Range(0, int.MaxValue)]
    public int ReviewCount { get; set; }

    [Required]
    public DateTime RatingDate { get; set; }

    public List<string> RecentFeedback { get; set; } = new();
    public Dictionary<string, object> RatingDetails { get; set; } = new();
    public List<NotificationChannel> PreferredChannels { get; set; } = new();
    public Priority Priority { get; set; } = Priority.Normal;
    public bool RequireAcknowledgment { get; set; } = false;
    public List<string> Tags { get; set; } = new();
    public string? CorrelationId { get; set; }
}

/// <summary>
/// Request model for sending performance milestone alert
/// </summary>
public class SendPerformanceMilestoneAlertRequest
{
    [Required]
    public Guid CarrierId { get; set; }

    [Required]
    [StringLength(100)]
    public string MilestoneType { get; set; } = string.Empty;

    [Required]
    [StringLength(200)]
    public string MilestoneName { get; set; } = string.Empty;

    [Required]
    public decimal MilestoneValue { get; set; }

    [Required]
    [StringLength(50)]
    public string MilestoneUnit { get; set; } = string.Empty;

    [Required]
    public DateTime AchievedDate { get; set; }

    [Required]
    [StringLength(50)]
    public string AchievementLevel { get; set; } = string.Empty;

    public Dictionary<string, object> MilestoneData { get; set; } = new();
    public List<string> Rewards { get; set; } = new();

    [StringLength(200)]
    public string NextMilestone { get; set; } = string.Empty;

    public decimal? NextMilestoneTarget { get; set; }
    public List<NotificationChannel> PreferredChannels { get; set; } = new();
    public Priority Priority { get; set; } = Priority.Normal;
    public bool RequireAcknowledgment { get; set; } = false;
    public List<string> Tags { get; set; } = new();
    public string? CorrelationId { get; set; }
}
