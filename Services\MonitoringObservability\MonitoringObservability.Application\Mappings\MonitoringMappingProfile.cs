using AutoMapper;
using MonitoringObservability.Application.DTOs;
using MonitoringObservability.Domain.Entities;
using MonitoringObservability.Domain.ValueObjects;

namespace MonitoringObservability.Application.Mappings;

public class MonitoringMappingProfile : Profile
{
    public MonitoringMappingProfile()
    {
        // Alert mappings
        CreateMap<Alert, AlertDto>()
            .ForMember(dest => dest.Duration, opt => opt.MapFrom(src => 
                src.ResolvedAt.HasValue ? src.ResolvedAt.Value - src.TriggeredAt : DateTime.UtcNow - src.TriggeredAt))
            .ForMember(dest => dest.IsOpen, opt => opt.MapFrom(src => src.Status == Domain.Enums.AlertStatus.Open))
            .ForMember(dest => dest.IsResolved, opt => opt.MapFrom(src => 
                src.Status == Domain.Enums.AlertStatus.Resolved || src.Status == Domain.Enums.AlertStatus.Closed));

        CreateMap<AlertNotification, AlertNotificationDto>();
        CreateMap<AlertComment, AlertCommentDto>();

        // Metric mappings
        CreateMap<Metric, MetricDto>();
        CreateMap<MetricValue, MetricValueDto>();
        CreateMap<MetricDataPoint, MetricDataPointDto>();
        CreateMap<AlertThreshold, AlertThresholdDto>();

        // Health check mappings
        CreateMap<HealthCheck, HealthCheckDto>()
            .ForMember(dest => dest.SuccessRate, opt => opt.MapFrom(src => 
                src.TotalChecks > 0 ? (double)src.SuccessfulChecks / src.TotalChecks * 100 : 0));

        // Incident mappings
        CreateMap<Incident, IncidentDto>()
            .ForMember(dest => dest.ResolutionTime, opt => opt.MapFrom(src => 
                src.ResolvedAt.HasValue ? src.ResolvedAt.Value - src.CreatedAt : (TimeSpan?)null))
            .ForMember(dest => dest.TimeToAcknowledge, opt => opt.MapFrom(src => 
                src.AcknowledgedAt.HasValue ? src.AcknowledgedAt.Value - src.CreatedAt : (TimeSpan?)null))
            .ForMember(dest => dest.Priority, opt => opt.MapFrom(src => src.ImpactLevel * src.UrgencyLevel));

        CreateMap<IncidentUpdate, IncidentUpdateDto>();
        CreateMap<IncidentAlert, IncidentAlertDto>();
        CreateMap<IncidentComment, IncidentCommentDto>();

        // Performance metrics mappings
        CreateMap<PerformanceMetrics, PerformanceMetricsDto>()
            .ForMember(dest => dest.HealthStatus, opt => opt.MapFrom(src => src.GetHealthStatus()))
            .ForMember(dest => dest.IsHealthy, opt => opt.MapFrom(src => src.IsHealthy()));

        // Reverse mappings for commands
        CreateMap<AlertThresholdDto, AlertThreshold>()
            .ConstructUsing(src => new AlertThreshold(
                src.MetricName,
                src.WarningThreshold,
                src.CriticalThreshold,
                src.Operator,
                src.EvaluationWindow));

        CreateMap<MetricValueDto, MetricValue>()
            .ConstructUsing(src => new MetricValue(
                src.Value,
                src.Unit,
                src.Type,
                src.Timestamp,
                src.Tags));

        CreateMap<PerformanceMetricsDto, PerformanceMetrics>()
            .ConstructUsing(src => new PerformanceMetrics(
                src.ResponseTime,
                src.Throughput,
                src.ErrorRate,
                src.CpuUsage,
                src.MemoryUsage,
                src.DiskUsage,
                src.MeasuredAt));
    }
}
