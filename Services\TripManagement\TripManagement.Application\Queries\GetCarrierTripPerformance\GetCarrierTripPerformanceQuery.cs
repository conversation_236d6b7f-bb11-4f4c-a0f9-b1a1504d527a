using MediatR;
using TripManagement.Application.DTOs;

namespace TripManagement.Application.Queries.GetCarrierTripPerformance;

public record GetCarrierTripPerformanceQuery(
    Guid CarrierId,
    DateTime FromDate,
    DateTime ToDate,
    bool IncludeTrends = true,
    bool IncludeDriverPerformance = true,
    bool IncludeVehiclePerformance = true,
    int TopPerformersCount = 5) : IRequest<CarrierTripPerformanceDto>;
