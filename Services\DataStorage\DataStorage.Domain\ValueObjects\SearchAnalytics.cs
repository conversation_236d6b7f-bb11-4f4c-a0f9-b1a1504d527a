using DataStorage.Domain.Enums;

namespace DataStorage.Domain.ValueObjects;

public class AutoCompleteResult
{
    public List<AutoCompleteSuggestion> Suggestions { get; private set; }
    public TimeSpan ResponseTime { get; private set; }

    public AutoCompleteResult(List<AutoCompleteSuggestion> suggestions, TimeSpan responseTime)
    {
        Suggestions = suggestions ?? new List<AutoCompleteSuggestion>();
        ResponseTime = responseTime;
    }
}

public class AutoCompleteSuggestion
{
    public string Text { get; private set; }
    public double Score { get; private set; }
    public string? Category { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    public AutoCompleteSuggestion(
        string text,
        double score,
        string? category = null,
        Dictionary<string, object>? metadata = null)
    {
        Text = text ?? throw new ArgumentNullException(nameof(text));
        Score = score;
        Category = category;
        Metadata = metadata ?? new Dictionary<string, object>();
    }
}

public class SearchSuggestion
{
    public string Query { get; private set; }
    public double Score { get; private set; }
    public long ResultCount { get; private set; }
    public SuggestionType Type { get; private set; }

    public SearchSuggestion(string query, double score, long resultCount, SuggestionType type)
    {
        Query = query ?? throw new ArgumentNullException(nameof(query));
        Score = score;
        ResultCount = resultCount;
        Type = type;
    }
}

public class SearchAnalytics
{
    public TimeSpan Period { get; private set; }
    public long TotalSearches { get; private set; }
    public long UniqueUsers { get; private set; }
    public double AverageResponseTime { get; private set; }
    public double AverageResultsPerQuery { get; private set; }
    public double ZeroResultsRate { get; private set; }
    public List<PopularQuery> TopQueries { get; private set; }
    public List<SearchTrend> Trends { get; private set; }
    public Dictionary<string, long> SearchesByCategory { get; private set; }
    public Dictionary<string, double> AverageResponseTimeByCategory { get; private set; }

    public SearchAnalytics(
        TimeSpan period,
        long totalSearches,
        long uniqueUsers,
        double averageResponseTime,
        double averageResultsPerQuery,
        double zeroResultsRate,
        List<PopularQuery> topQueries,
        List<SearchTrend> trends,
        Dictionary<string, long> searchesByCategory,
        Dictionary<string, double> averageResponseTimeByCategory)
    {
        Period = period;
        TotalSearches = totalSearches;
        UniqueUsers = uniqueUsers;
        AverageResponseTime = averageResponseTime;
        AverageResultsPerQuery = averageResultsPerQuery;
        ZeroResultsRate = Math.Max(0, Math.Min(100, zeroResultsRate));
        TopQueries = topQueries ?? new List<PopularQuery>();
        Trends = trends ?? new List<SearchTrend>();
        SearchesByCategory = searchesByCategory ?? new Dictionary<string, long>();
        AverageResponseTimeByCategory = averageResponseTimeByCategory ?? new Dictionary<string, double>();
    }
}

public class PopularQuery
{
    public string Query { get; private set; }
    public long SearchCount { get; private set; }
    public long UniqueUsers { get; private set; }
    public double AverageResultCount { get; private set; }
    public double AverageResponseTime { get; private set; }
    public DateTime FirstSeen { get; private set; }
    public DateTime LastSeen { get; private set; }

    public PopularQuery(
        string query,
        long searchCount,
        long uniqueUsers,
        double averageResultCount,
        double averageResponseTime,
        DateTime firstSeen,
        DateTime lastSeen)
    {
        Query = query ?? throw new ArgumentNullException(nameof(query));
        SearchCount = searchCount;
        UniqueUsers = uniqueUsers;
        AverageResultCount = averageResultCount;
        AverageResponseTime = averageResponseTime;
        FirstSeen = firstSeen;
        LastSeen = lastSeen;
    }
}

public class SearchTrend
{
    public string Query { get; private set; }
    public DateTime Date { get; private set; }
    public long SearchCount { get; private set; }
    public double TrendScore { get; private set; }
    public TrendDirection Direction { get; private set; }

    public SearchTrend(
        string query,
        DateTime date,
        long searchCount,
        double trendScore,
        TrendDirection direction)
    {
        Query = query ?? throw new ArgumentNullException(nameof(query));
        Date = date;
        SearchCount = searchCount;
        TrendScore = trendScore;
        Direction = direction;
    }
}

public class IndexConfiguration
{
    public string IndexName { get; private set; }
    public Dictionary<string, FieldMapping> FieldMappings { get; private set; }
    public IndexSettings Settings { get; private set; }
    public List<string> Analyzers { get; private set; }

    public IndexConfiguration(
        string indexName,
        Dictionary<string, FieldMapping> fieldMappings,
        IndexSettings? settings = null,
        List<string>? analyzers = null)
    {
        IndexName = indexName ?? throw new ArgumentNullException(nameof(indexName));
        FieldMappings = fieldMappings ?? throw new ArgumentNullException(nameof(fieldMappings));
        Settings = settings ?? IndexSettings.Default;
        Analyzers = analyzers ?? new List<string>();
    }
}

public class FieldMapping
{
    public FieldType Type { get; private set; }
    public bool IsSearchable { get; private set; }
    public bool IsFilterable { get; private set; }
    public bool IsSortable { get; private set; }
    public bool IsFacetable { get; private set; }
    public string? Analyzer { get; private set; }
    public double Boost { get; private set; }

    public FieldMapping(
        FieldType type,
        bool isSearchable = true,
        bool isFilterable = true,
        bool isSortable = false,
        bool isFacetable = false,
        string? analyzer = null,
        double boost = 1.0)
    {
        Type = type;
        IsSearchable = isSearchable;
        IsFilterable = isFilterable;
        IsSortable = isSortable;
        IsFacetable = isFacetable;
        Analyzer = analyzer;
        Boost = Math.Max(0.1, boost);
    }
}

public class IndexSettings
{
    public int NumberOfShards { get; private set; }
    public int NumberOfReplicas { get; private set; }
    public string DefaultAnalyzer { get; private set; }
    public Dictionary<string, object> CustomSettings { get; private set; }

    public IndexSettings(
        int numberOfShards = 1,
        int numberOfReplicas = 1,
        string defaultAnalyzer = "standard",
        Dictionary<string, object>? customSettings = null)
    {
        NumberOfShards = Math.Max(1, numberOfShards);
        NumberOfReplicas = Math.Max(0, numberOfReplicas);
        DefaultAnalyzer = defaultAnalyzer ?? throw new ArgumentNullException(nameof(defaultAnalyzer));
        CustomSettings = customSettings ?? new Dictionary<string, object>();
    }

    public static IndexSettings Default => new();
}

public class IndexHealth
{
    public IndexHealthStatus Status { get; private set; }
    public long TotalDocuments { get; private set; }
    public long IndexSizeBytes { get; private set; }
    public int ActiveShards { get; private set; }
    public int RelocatingShards { get; private set; }
    public int InitializingShards { get; private set; }
    public int UnassignedShards { get; private set; }
    public double AverageResponseTime { get; private set; }
    public List<IndexIssue> Issues { get; private set; }

    public IndexHealth(
        IndexHealthStatus status,
        long totalDocuments,
        long indexSizeBytes,
        int activeShards,
        int relocatingShards,
        int initializingShards,
        int unassignedShards,
        double averageResponseTime,
        List<IndexIssue>? issues = null)
    {
        Status = status;
        TotalDocuments = totalDocuments;
        IndexSizeBytes = indexSizeBytes;
        ActiveShards = activeShards;
        RelocatingShards = relocatingShards;
        InitializingShards = initializingShards;
        UnassignedShards = unassignedShards;
        AverageResponseTime = averageResponseTime;
        Issues = issues ?? new List<IndexIssue>();
    }
}

public class IndexIssue
{
    public IndexIssueType Type { get; private set; }
    public IndexIssueSeverity Severity { get; private set; }
    public string Description { get; private set; }
    public string? Recommendation { get; private set; }
    public DateTime DetectedAt { get; private set; }

    public IndexIssue(
        IndexIssueType type,
        IndexIssueSeverity severity,
        string description,
        string? recommendation = null)
    {
        Type = type;
        Severity = severity;
        Description = description ?? throw new ArgumentNullException(nameof(description));
        Recommendation = recommendation;
        DetectedAt = DateTime.UtcNow;
    }
}

// Helper classes for search configuration
public class SearchSort
{
    public string Field { get; private set; }
    public SortDirection Direction { get; private set; }
    public SortMode Mode { get; private set; }

    public SearchSort(string field, SortDirection direction = SortDirection.Ascending, SortMode mode = SortMode.Default)
    {
        Field = field ?? throw new ArgumentNullException(nameof(field));
        Direction = direction;
        Mode = mode;
    }

    public static SearchSort Relevance => new("_score", SortDirection.Descending);
    public static SearchSort CreatedDate => new("createdAt", SortDirection.Descending);
    public static SearchSort ModifiedDate => new("modifiedAt", SortDirection.Descending);
    public static SearchSort Title => new("title", SortDirection.Ascending);
}

public class SearchField
{
    public string Name { get; private set; }
    public double Boost { get; private set; }

    public SearchField(string name, double boost = 1.0)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Boost = Math.Max(0.1, boost);
    }

    public static SearchField All => new("_all");
    public static SearchField Title => new("title", 2.0);
    public static SearchField Content => new("content");
    public static SearchField Summary => new("summary", 1.5);
    public static SearchField Tags => new("tags", 1.2);
}
