using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Logging;
using System.Security.Claims;

namespace Shared.Infrastructure.Authorization
{
    /// <summary>
    /// Custom authorization attribute for export endpoints with enhanced security
    /// </summary>
    public class ExportAuthorizationAttribute : Attribute, IAuthorizationFilter
    {
        private readonly string[] _allowedRoles;
        private readonly ExportType _exportType;
        private readonly bool _requiresComplianceApproval;

        public ExportAuthorizationAttribute(ExportType exportType, params string[] allowedRoles)
        {
            _exportType = exportType;
            _allowedRoles = allowedRoles ?? new[] { "Admin" };
            _requiresComplianceApproval = exportType == ExportType.KycDocuments || exportType == ExportType.AdminAuditTrail;
        }

        public void OnAuthorization(AuthorizationFilterContext context)
        {
            var user = context.HttpContext.User;
            var logger = context.HttpContext.RequestServices.GetService(typeof(ILogger<ExportAuthorizationAttribute>)) as ILogger<ExportAuthorizationAttribute>;

            // Check if user is authenticated
            if (!user.Identity?.IsAuthenticated ?? true)
            {
                logger?.LogWarning("Unauthorized export attempt for {ExportType} from IP {IP}", 
                    _exportType, context.HttpContext.Connection.RemoteIpAddress);
                
                context.Result = new UnauthorizedResult();
                return;
            }

            // Check role authorization
            if (!HasRequiredRole(user))
            {
                logger?.LogWarning("Insufficient permissions for export {ExportType} by user {UserId} with roles {Roles}", 
                    _exportType, GetUserId(user), string.Join(",", GetUserRoles(user)));
                
                context.Result = new ForbidResult();
                return;
            }

            // Additional security checks for sensitive exports
            if (_requiresComplianceApproval && !HasCompliancePermission(user))
            {
                logger?.LogWarning("Compliance permission required for export {ExportType} by user {UserId}", 
                    _exportType, GetUserId(user));
                
                context.Result = new ForbidResult();
                return;
            }

            // Check for suspicious activity patterns
            if (IsSuspiciousActivity(context))
            {
                logger?.LogWarning("Suspicious export activity detected for {ExportType} by user {UserId} from IP {IP}", 
                    _exportType, GetUserId(user), context.HttpContext.Connection.RemoteIpAddress);
                
                // Could implement additional security measures here
                // For now, we'll allow but log the activity
            }

            // Log successful authorization
            logger?.LogInformation("Export authorization granted for {ExportType} to user {UserId}", 
                _exportType, GetUserId(user));
        }

        private bool HasRequiredRole(ClaimsPrincipal user)
        {
            var userRoles = GetUserRoles(user);
            return _allowedRoles.Any(role => userRoles.Contains(role, StringComparer.OrdinalIgnoreCase));
        }

        private bool HasCompliancePermission(ClaimsPrincipal user)
        {
            var userRoles = GetUserRoles(user);
            return userRoles.Contains("Admin", StringComparer.OrdinalIgnoreCase) ||
                   userRoles.Contains("ComplianceOfficer", StringComparer.OrdinalIgnoreCase) ||
                   userRoles.Contains("Auditor", StringComparer.OrdinalIgnoreCase);
        }

        private bool IsSuspiciousActivity(AuthorizationFilterContext context)
        {
            // Implement suspicious activity detection logic
            // For example:
            // - Multiple export requests in short time
            // - Unusual IP address patterns
            // - Large data export requests
            // - Off-hours access patterns
            
            var user = context.HttpContext.User;
            var ipAddress = context.HttpContext.Connection.RemoteIpAddress?.ToString();
            var userAgent = context.HttpContext.Request.Headers["User-Agent"].ToString();
            
            // Simple checks (in a real implementation, you'd use more sophisticated detection)
            var isOffHours = DateTime.UtcNow.Hour < 6 || DateTime.UtcNow.Hour > 22;
            var isUnusualUserAgent = string.IsNullOrEmpty(userAgent) || userAgent.Length < 10;
            
            return isOffHours && isUnusualUserAgent;
        }

        private string GetUserId(ClaimsPrincipal user)
        {
            return user.FindFirst("sub")?.Value ?? 
                   user.FindFirst("id")?.Value ?? 
                   user.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? 
                   "Unknown";
        }

        private List<string> GetUserRoles(ClaimsPrincipal user)
        {
            var roles = new List<string>();
            
            // Try different claim types for roles
            roles.AddRange(user.FindAll("role").Select(c => c.Value));
            roles.AddRange(user.FindAll(ClaimTypes.Role).Select(c => c.Value));
            roles.AddRange(user.FindAll("http://schemas.microsoft.com/ws/2008/06/identity/claims/role").Select(c => c.Value));
            
            return roles.Distinct().ToList();
        }
    }

    /// <summary>
    /// Export type enumeration for authorization
    /// </summary>
    public enum ExportType
    {
        UserSummary,
        KycDocuments,
        UsageLogs,
        FeedbackHistory,
        AdminAuditTrail,
        FinancialReports,
        AnalyticsData
    }

    /// <summary>
    /// Export permission levels
    /// </summary>
    public static class ExportPermissions
    {
        public const string Admin = "Admin";
        public const string ComplianceOfficer = "ComplianceOfficer";
        public const string Auditor = "Auditor";
        public const string Manager = "Manager";
        public const string Analyst = "Analyst";
        
        public static readonly Dictionary<ExportType, string[]> RequiredRoles = new()
        {
            { ExportType.UserSummary, new[] { Admin, Manager } },
            { ExportType.KycDocuments, new[] { Admin, ComplianceOfficer } },
            { ExportType.UsageLogs, new[] { Admin, Auditor, Analyst } },
            { ExportType.FeedbackHistory, new[] { Admin, ComplianceOfficer, Auditor } },
            { ExportType.AdminAuditTrail, new[] { Admin, Auditor } },
            { ExportType.FinancialReports, new[] { Admin, Manager } },
            { ExportType.AnalyticsData, new[] { Admin, Analyst, Manager } }
        };
    }

    /// <summary>
    /// Export security configuration
    /// </summary>
    public class ExportSecurityOptions
    {
        public bool EnableSuspiciousActivityDetection { get; set; } = true;
        public bool RequireComplianceApprovalForSensitiveData { get; set; } = true;
        public bool LogAllExportAttempts { get; set; } = true;
        public bool EnableIpWhitelisting { get; set; } = false;
        public List<string> WhitelistedIpAddresses { get; set; } = new();
        public TimeSpan MaxExportDuration { get; set; } = TimeSpan.FromHours(2);
        public int MaxConcurrentExportsPerUser { get; set; } = 3;
        public int MaxDailyExportsPerUser { get; set; } = 20;
    }
}
