using Microsoft.AspNetCore.Http;

namespace UserManagement.Application.Interfaces;

public class OCRResult
{
    public string Text { get; set; } = string.Empty;
    public Dictionary<string, string> ExtractedFields { get; set; } = new();
    public float Confidence { get; set; }
    public Dictionary<string, object> ExtractedData { get; set; } = new();
}

public interface IOCRService
{
    Task<OCRResult> ProcessDocumentAsync(IFormFile document);
    Task<OCRResult> ProcessImageAsync(byte[] imageData);
    Task<OCRResult> ExtractDataAsync(string filePath, string documentType, CancellationToken cancellationToken = default);
}
