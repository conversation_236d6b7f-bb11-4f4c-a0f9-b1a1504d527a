using Shared.Domain.ValueObjects;

namespace DataStorage.Domain.ValueObjects;

/// <summary>
/// Application performance metrics
/// </summary>
public class ApplicationPerformance : ValueObject
{
    public string ApplicationName { get; init; }
    public DateTime MeasuredAt { get; init; }
    public PerformanceMetrics Metrics { get; init; }
    public List<PerformanceAlert> Alerts { get; init; }
    public Dictionary<string, object> CustomMetrics { get; init; }

    public ApplicationPerformance(
        string applicationName,
        DateTime measuredAt,
        PerformanceMetrics metrics,
        List<PerformanceAlert>? alerts = null,
        Dictionary<string, object>? customMetrics = null)
    {
        ApplicationName = applicationName;
        MeasuredAt = measuredAt;
        Metrics = metrics;
        Alerts = alerts ?? new List<PerformanceAlert>();
        CustomMetrics = customMetrics ?? new Dictionary<string, object>();
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return ApplicationName;
        yield return MeasuredAt;
        yield return Metrics;
        
        foreach (var alert in Alerts.OrderBy(x => x.AlertType))
        {
            yield return alert;
        }
        
        foreach (var metric in CustomMetrics.OrderBy(x => x.Key))
        {
            yield return metric.Key;
            yield return metric.Value?.ToString() ?? string.Empty;
        }
    }
}

public class PerformanceMetrics : ValueObject
{
    public double ResponseTimeMs { get; init; }
    public double ThroughputRps { get; init; }
    public double ErrorRate { get; init; }
    public double CpuUtilization { get; init; }
    public double MemoryUtilization { get; init; }
    public int ActiveConnections { get; init; }
    public double AvailabilityPercentage { get; init; }

    public PerformanceMetrics(
        double responseTimeMs,
        double throughputRps,
        double errorRate,
        double cpuUtilization,
        double memoryUtilization,
        int activeConnections,
        double availabilityPercentage)
    {
        ResponseTimeMs = responseTimeMs;
        ThroughputRps = throughputRps;
        ErrorRate = errorRate;
        CpuUtilization = cpuUtilization;
        MemoryUtilization = memoryUtilization;
        ActiveConnections = activeConnections;
        AvailabilityPercentage = availabilityPercentage;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return ResponseTimeMs;
        yield return ThroughputRps;
        yield return ErrorRate;
        yield return CpuUtilization;
        yield return MemoryUtilization;
        yield return ActiveConnections;
        yield return AvailabilityPercentage;
    }
}

public class PerformanceAlert : ValueObject
{
    public string AlertType { get; init; }
    public string Severity { get; init; }
    public string Message { get; init; }
    public DateTime TriggeredAt { get; init; }
    public Dictionary<string, object> Context { get; init; }

    public PerformanceAlert(
        string alertType,
        string severity,
        string message,
        DateTime triggeredAt,
        Dictionary<string, object>? context = null)
    {
        AlertType = alertType;
        Severity = severity;
        Message = message;
        TriggeredAt = triggeredAt;
        Context = context ?? new Dictionary<string, object>();
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return AlertType;
        yield return Severity;
        yield return Message;
        yield return TriggeredAt;
        
        foreach (var ctx in Context.OrderBy(x => x.Key))
        {
            yield return ctx.Key;
            yield return ctx.Value?.ToString() ?? string.Empty;
        }
    }
}
