using AnalyticsBIService.Domain.Enums;
using AnalyticsBIService.Domain.Events;
using AnalyticsBIService.Domain.ValueObjects;
using Shared.Domain.Common;

namespace AnalyticsBIService.Domain.Entities;

/// <summary>
/// Dashboard entity for role-based analytics views
/// </summary>
public class Dashboard : BaseEntity
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public DashboardType Type { get; private set; }
    public Guid? UserId { get; private set; }
    public UserType? UserType { get; private set; }
    public bool IsDefault { get; private set; }
    public bool IsPublic { get; private set; }
    public bool IsActive { get; private set; } = true;
    public Dictionary<string, object> Configuration { get; private set; }
    public DateTime? LastAccessedAt { get; private set; }
    public int AccessCount { get; private set; }
    public Guid? CreatedBy => UserId; // Alias for UserId
    public bool IsShared => IsPublic; // Alias for IsPublic
    public List<string> Tags { get; private set; }
    public string? Layout { get; private set; }
    public int? RefreshInterval { get; private set; }
    public DateTime? LastRefreshed { get; private set; }

    private readonly List<DashboardWidget> _widgets = new();
    public IReadOnlyCollection<DashboardWidget> Widgets => _widgets.AsReadOnly();

    private Dashboard()
    {
        Name = string.Empty;
        Description = string.Empty;
        Configuration = new Dictionary<string, object>();
        Tags = new List<string>();
    }

    public Dashboard(
        string name,
        string description,
        DashboardType type,
        Guid? userId = null,
        UserType? userType = null,
        bool isDefault = false,
        bool isPublic = false,
        Dictionary<string, object>? configuration = null)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        Type = type;
        UserId = userId;
        UserType = userType;
        IsDefault = isDefault;
        IsPublic = isPublic;
        Configuration = configuration ?? new Dictionary<string, object>();
        AccessCount = 0;
        Tags = new List<string>();

        // Set initial UpdatedAt timestamp
        SetUpdatedAt();

        // Add domain event
        AddDomainEvent(new DashboardCreatedEvent(Id, Name, Type, UserId, UserType));
    }

    public static Dashboard CreateAdminPlatformDashboard(string name, string description)
    {
        return new Dashboard(name, description, DashboardType.AdminPlatformPerformance, isPublic: false);
    }

    public static Dashboard CreateUserDashboard(string name, string description, DashboardType type, Guid userId, UserType userType)
    {
        return new Dashboard(name, description, type, userId, userType, isPublic: false);
    }

    public void RecordAccess(Guid userId, UserType userType)
    {
        LastAccessedAt = DateTime.UtcNow;
        AccessCount++;
        SetUpdatedAt();

        // Add domain event
        AddDomainEvent(new DashboardAccessedEvent(Id, userId, userType, LastAccessedAt.Value));
    }

    public void UpdateConfiguration(Dictionary<string, object> configuration)
    {
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        SetUpdatedAt();
    }

    public void SetAsDefault()
    {
        IsDefault = true;
        SetUpdatedAt();
    }

    public void SetPublic(bool isPublic)
    {
        IsPublic = isPublic;
        SetUpdatedAt();
    }

    public void UpdateLayout(string? layout)
    {
        Layout = layout;
        SetUpdatedAt();
    }

    public void SetRefreshInterval(int? refreshInterval)
    {
        RefreshInterval = refreshInterval;
        SetUpdatedAt();
    }

    public void MarkAsRefreshed()
    {
        LastRefreshed = DateTime.UtcNow;
        SetUpdatedAt();
    }

    public void UpdateTags(List<string> tags)
    {
        Tags = tags ?? new List<string>();
        SetUpdatedAt();
    }

    private readonly List<IDomainEvent> _domainEvents = new();
    public IReadOnlyCollection<IDomainEvent> DomainEvents => _domainEvents.AsReadOnly();

    protected void AddDomainEvent(IDomainEvent domainEvent)
    {
        _domainEvents.Add(domainEvent);
    }

    public void ClearDomainEvents()
    {
        _domainEvents.Clear();
    }
}

/// <summary>
/// Dashboard widget entity for individual dashboard components
/// </summary>
public class DashboardWidget : BaseEntity
{
    public string Title { get; private set; }
    public string WidgetType { get; private set; }
    public int Position { get; private set; }
    public int Width { get; private set; }
    public int Height { get; private set; }
    public Dictionary<string, object> Configuration { get; private set; }
    public bool IsVisible { get; private set; }
    public bool IsActive { get; private set; } = true;
    public Guid? DashboardId { get; private set; }
    public Guid? UserId { get; private set; }
    public string? DataSource { get; private set; }
    public TimePeriodValue? DefaultTimePeriod { get; private set; }

    // Navigation properties
    public Dashboard? Dashboard { get; private set; }

    private DashboardWidget()
    {
        Title = string.Empty;
        WidgetType = string.Empty;
        Configuration = new Dictionary<string, object>();
    }

    public DashboardWidget(
        string title,
        string widgetType,
        int position,
        int width = 1,
        int height = 1,
        Dictionary<string, object>? configuration = null,
        string? dataSource = null,
        TimePeriodValue? defaultTimePeriod = null)
    {
        Title = title ?? throw new ArgumentNullException(nameof(title));
        WidgetType = widgetType ?? throw new ArgumentNullException(nameof(widgetType));
        Position = position;
        Width = width > 0 ? width : throw new ArgumentException("Width must be positive");
        Height = height > 0 ? height : throw new ArgumentException("Height must be positive");
        Configuration = configuration ?? new Dictionary<string, object>();
        IsVisible = true;
        DataSource = dataSource;
        DefaultTimePeriod = defaultTimePeriod;
    }

    public static DashboardWidget CreateKPIWidget(string title, string metricName, int position, KPITarget? target = null)
    {
        var config = new Dictionary<string, object>
        {
            ["metricName"] = metricName,
            ["showTarget"] = target is not null,
            ["showTrend"] = true
        };

        if (target is not null)
        {
            config["target"] = target;
        }

        return new DashboardWidget(title, "KPI", position, 1, 1, config);
    }

    public static DashboardWidget CreateChartWidget(string title, string chartType, int position, string dataSource, int width = 2, int height = 2)
    {
        var config = new Dictionary<string, object>
        {
            ["chartType"] = chartType,
            ["dataSource"] = dataSource
        };

        return new DashboardWidget(title, "Chart", position, width, height, config, dataSource);
    }

    public static DashboardWidget CreateTableWidget(string title, int position, string dataSource, int width = 3, int height = 2)
    {
        var config = new Dictionary<string, object>
        {
            ["showPagination"] = true,
            ["pageSize"] = 10,
            ["sortable"] = true
        };

        return new DashboardWidget(title, "Table", position, width, height, config, dataSource);
    }



    public void UpdateConfiguration(Dictionary<string, object>? configuration)
    {
        if (configuration != null)
        {
            Configuration = configuration;
        }
        SetUpdatedAt();
    }

    public void UpdatePosition(int position)
    {
        Position = position;
        SetUpdatedAt();
    }

    public void Resize(int width, int height)
    {
        Width = width > 0 ? width : throw new ArgumentException("Width must be positive");
        Height = height > 0 ? height : throw new ArgumentException("Height must be positive");
        SetUpdatedAt();
    }

    public void SetVisibility(bool isVisible)
    {
        IsVisible = isVisible;
        SetUpdatedAt();
    }

    public void SetDataSource(string dataSource)
    {
        DataSource = dataSource;
        SetUpdatedAt();
    }

    public void SetDefaultTimePeriod(TimePeriodValue timePeriod)
    {
        DefaultTimePeriod = timePeriod;
        SetUpdatedAt();
    }
}

