using Shared.Domain.Common;
using MobileWorkflow.Domain.Events;

namespace MobileWorkflow.Domain.Entities;

public class ComponentUsage : AggregateRoot
{
    public Guid ComponentId { get; private set; }
    public Guid UserId { get; private set; }
    public string ApplicationId { get; private set; }
    public string Platform { get; private set; }
    public string Context { get; private set; } // Screen, Form, Modal, etc.
    public Dictionary<string, object> UsageData { get; private set; }
    public Dictionary<string, object> Configuration { get; private set; }
    public DateTime UsedAt { get; private set; }
    public TimeSpan? Duration { get; private set; }
    public bool IsSuccessful { get; private set; }
    public string? ErrorMessage { get; private set; }
    public Dictionary<string, object> PerformanceMetrics { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    public virtual UIComponent Component { get; private set; } = null!;

    private ComponentUsage() { } // EF Core

    public ComponentUsage(
        Guid componentId,
        Guid userId,
        string applicationId,
        string platform,
        string context,
        Dictionary<string, object> configuration)
    {
        ComponentId = componentId;
        UserId = userId;
        ApplicationId = applicationId ?? throw new ArgumentNullException(nameof(applicationId));
        Platform = platform ?? throw new ArgumentNullException(nameof(platform));
        Context = context ?? throw new ArgumentNullException(nameof(context));
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));

        UsedAt = DateTime.UtcNow;
        IsSuccessful = true;
        UsageData = new Dictionary<string, object>();
        PerformanceMetrics = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();

        AddDomainEvent(new ComponentUsageRecordedEvent(Id, ComponentId, UserId, Platform, Context));
    }

    public void RecordSuccess(TimeSpan duration, Dictionary<string, object>? performanceMetrics = null)
    {
        IsSuccessful = true;
        Duration = duration;

        if (performanceMetrics != null)
        {
            PerformanceMetrics = performanceMetrics;
        }

        AddDomainEvent(new ComponentUsageSuccessEvent(Id, ComponentId, UserId, duration.TotalMilliseconds));
    }

    public void RecordFailure(string errorMessage, TimeSpan? duration = null)
    {
        IsSuccessful = false;
        ErrorMessage = errorMessage;
        Duration = duration;

        AddDomainEvent(new ComponentUsageFailedEvent(Id, ComponentId, UserId, errorMessage));
    }

    public void AddUsageData(string key, object value)
    {
        UsageData[key] = value;
    }

    public void AddPerformanceMetric(string metric, object value)
    {
        PerformanceMetrics[metric] = value;
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    public T? GetUsageData<T>(string key, T? defaultValue = default)
    {
        if (UsageData.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetPerformanceMetric<T>(string metric, T? defaultValue = default)
    {
        if (PerformanceMetrics.TryGetValue(metric, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }
}

public class ComponentLibrary : AggregateRoot
{
    public string Name { get; private set; }
    public string DisplayName { get; private set; }
    public string Description { get; private set; }
    public string Version { get; private set; }
    public bool IsActive { get; private set; }
    public List<string> SupportedPlatforms { get; private set; }
    public Dictionary<string, object> Configuration { get; private set; }
    public Dictionary<string, object> Dependencies { get; private set; }
    public string? RepositoryUrl { get; private set; }
    public string? DocumentationUrl { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? UpdatedAt { get; private set; }
    public string CreatedBy { get; private set; }
    public string? UpdatedBy { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    public virtual ICollection<UIComponent> Components { get; private set; } = new List<UIComponent>();
    public virtual ICollection<ComponentTheme> Themes { get; private set; } = new List<ComponentTheme>();

    private ComponentLibrary() { } // EF Core

    public ComponentLibrary(
        string name,
        string displayName,
        string description,
        string version,
        List<string> supportedPlatforms,
        string createdBy)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        DisplayName = displayName ?? throw new ArgumentNullException(nameof(displayName));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        Version = version ?? throw new ArgumentNullException(nameof(version));
        SupportedPlatforms = supportedPlatforms ?? throw new ArgumentNullException(nameof(supportedPlatforms));
        CreatedBy = createdBy ?? throw new ArgumentNullException(nameof(createdBy));

        IsActive = true;
        CreatedAt = DateTime.UtcNow;
        Configuration = new Dictionary<string, object>();
        Dependencies = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();

        AddDomainEvent(new ComponentLibraryCreatedEvent(Id, Name, Version, CreatedBy));
    }

    public void UpdateVersion(string newVersion, string updatedBy)
    {
        var oldVersion = Version;
        Version = newVersion ?? throw new ArgumentNullException(nameof(newVersion));
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new ComponentLibraryVersionUpdatedEvent(Id, Name, oldVersion, newVersion, UpdatedBy));
    }

    public void UpdateConfiguration(Dictionary<string, object> newConfiguration, string updatedBy)
    {
        Configuration = newConfiguration ?? throw new ArgumentNullException(nameof(newConfiguration));
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new ComponentLibraryConfigurationUpdatedEvent(Id, Name, UpdatedBy));
    }

    public void AddDependency(string dependencyName, string version, string type = "library")
    {
        Dependencies[dependencyName] = new { Version = version, Type = type, AddedAt = DateTime.UtcNow };
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new ComponentLibraryDependencyAddedEvent(Id, Name, dependencyName, version));
    }

    public void RemoveDependency(string dependencyName)
    {
        if (Dependencies.Remove(dependencyName))
        {
            UpdatedAt = DateTime.UtcNow;
            AddDomainEvent(new ComponentLibraryDependencyRemovedEvent(Id, Name, dependencyName));
        }
    }

    public void SetRepositoryUrl(string repositoryUrl)
    {
        RepositoryUrl = repositoryUrl;
        UpdatedAt = DateTime.UtcNow;
    }

    public void SetDocumentationUrl(string documentationUrl)
    {
        DocumentationUrl = documentationUrl;
        UpdatedAt = DateTime.UtcNow;
    }

    public void Activate()
    {
        IsActive = true;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new ComponentLibraryActivatedEvent(Id, Name));
    }

    public void Deactivate()
    {
        IsActive = false;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new ComponentLibraryDeactivatedEvent(Id, Name));
    }

    public bool SupportsPlatform(string platform)
    {
        return SupportedPlatforms.Contains(platform, StringComparer.OrdinalIgnoreCase);
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
        UpdatedAt = DateTime.UtcNow;
    }

    public List<UIComponent> GetComponentsByCategory(string category)
    {
        return Components.Where(c => c.Category.Equals(category, StringComparison.OrdinalIgnoreCase) && c.IsActive).ToList();
    }

    public List<UIComponent> GetComponentsByType(string componentType)
    {
        return Components.Where(c => c.ComponentType.Equals(componentType, StringComparison.OrdinalIgnoreCase) && c.IsActive).ToList();
    }

    public UIComponent? GetComponent(string componentName)
    {
        return Components.FirstOrDefault(c => c.Name.Equals(componentName, StringComparison.OrdinalIgnoreCase) && c.IsActive);
    }

    public ComponentTheme? GetTheme(string themeName)
    {
        return Themes.FirstOrDefault(t => t.Name.Equals(themeName, StringComparison.OrdinalIgnoreCase) && t.IsActive);
    }

    public ComponentTheme? GetDefaultTheme()
    {
        return Themes.FirstOrDefault(t => t.IsDefault && t.IsActive);
    }
}


