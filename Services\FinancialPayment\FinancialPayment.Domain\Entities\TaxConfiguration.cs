using FinancialPayment.Domain.Enums;
using Shared.Domain.Common;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Domain.Entities;

/// <summary>
/// Main tax configuration aggregate root that manages overall tax settings
/// </summary>
public class TaxConfiguration : AggregateRoot
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public TaxJurisdiction Jurisdiction { get; private set; }
    public TaxConfigurationStatus Status { get; private set; }
    public DateTime EffectiveFrom { get; private set; }
    public DateTime? EffectiveTo { get; private set; }
    public bool IsDefault { get; private set; }
    public int Priority { get; private set; }
    public string CreatedBy { get; private set; }
    public string? ModifiedBy { get; private set; }
    public DateTime? ModifiedAt { get; private set; }

    // Tax configuration settings
    public bool EnableGstCalculation { get; private set; }
    public bool EnableTdsCalculation { get; private set; }
    public bool EnableReverseCharge { get; private set; }
    public bool RequireHsnCode { get; private set; }
    public decimal DefaultGstRate { get; private set; }
    public decimal DefaultTdsRate { get; private set; }
    public string DefaultCurrency { get; private set; }

    // Navigation properties
    private readonly List<TaxConfigurationHistory> _history = new();
    public IReadOnlyList<TaxConfigurationHistory> History => _history.AsReadOnly();

    private readonly List<TaxConfigurationRule> _rules = new();
    public IReadOnlyList<TaxConfigurationRule> Rules => _rules.AsReadOnly();

    private TaxConfiguration() { }

    public TaxConfiguration(
        string name,
        string description,
        TaxJurisdiction jurisdiction,
        DateTime effectiveFrom,
        string createdBy,
        DateTime? effectiveTo = null,
        bool isDefault = false,
        int priority = 0)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Tax configuration name cannot be empty", nameof(name));

        if (string.IsNullOrWhiteSpace(description))
            throw new ArgumentException("Tax configuration description cannot be empty", nameof(description));

        if (string.IsNullOrWhiteSpace(createdBy))
            throw new ArgumentException("Created by cannot be empty", nameof(createdBy));

        if (effectiveTo.HasValue && effectiveTo.Value <= effectiveFrom)
            throw new ArgumentException("Effective to date must be after effective from date", nameof(effectiveTo));

        Name = name.Trim();
        Description = description.Trim();
        Jurisdiction = jurisdiction;
        EffectiveFrom = effectiveFrom;
        EffectiveTo = effectiveTo;
        IsDefault = isDefault;
        Priority = priority;
        Status = TaxConfigurationStatus.Draft;
        CreatedBy = createdBy.Trim();

        // Set default values
        EnableGstCalculation = true;
        EnableTdsCalculation = true;
        EnableReverseCharge = false;
        RequireHsnCode = false;
        DefaultGstRate = 18.0m; // Default GST rate for India
        DefaultTdsRate = 2.0m;  // Default TDS rate
        DefaultCurrency = "INR";

        AddHistoryEntry("Created", $"Tax configuration created by {createdBy}");
    }

    public void UpdateBasicDetails(
        string name,
        string description,
        TaxJurisdiction jurisdiction,
        DateTime effectiveFrom,
        DateTime? effectiveTo,
        string modifiedBy,
        int priority = 0)
    {
        if (Status == TaxConfigurationStatus.Archived)
            throw new InvalidOperationException("Cannot update archived tax configuration");

        if (string.IsNullOrWhiteSpace(modifiedBy))
            throw new ArgumentException("Modified by cannot be empty", nameof(modifiedBy));

        var changes = new List<string>();

        if (Name != name.Trim())
        {
            changes.Add($"Name: {Name} → {name.Trim()}");
            Name = name.Trim();
        }

        if (Description != description.Trim())
        {
            changes.Add("Description updated");
            Description = description.Trim();
        }

        if (!Jurisdiction.Equals(jurisdiction))
        {
            changes.Add($"Jurisdiction: {Jurisdiction.GetFullJurisdiction()} → {jurisdiction.GetFullJurisdiction()}");
            Jurisdiction = jurisdiction;
        }

        if (EffectiveFrom != effectiveFrom)
        {
            changes.Add($"Effective From: {EffectiveFrom:yyyy-MM-dd} → {effectiveFrom:yyyy-MM-dd}");
            EffectiveFrom = effectiveFrom;
        }

        if (EffectiveTo != effectiveTo)
        {
            var oldDate = EffectiveTo?.ToString("yyyy-MM-dd") ?? "None";
            var newDate = effectiveTo?.ToString("yyyy-MM-dd") ?? "None";
            changes.Add($"Effective To: {oldDate} → {newDate}");
            EffectiveTo = effectiveTo;
        }

        if (Priority != priority)
        {
            changes.Add($"Priority: {Priority} → {priority}");
            Priority = priority;
        }

        if (changes.Any())
        {
            ModifiedBy = modifiedBy.Trim();
            ModifiedAt = DateTime.UtcNow;
            SetUpdatedAt();

            AddHistoryEntry("Updated", $"Basic details updated by {modifiedBy}: {string.Join(", ", changes)}");
        }
    }

    public void UpdateTaxSettings(
        bool enableGstCalculation,
        bool enableTdsCalculation,
        bool enableReverseCharge,
        bool requireHsnCode,
        decimal defaultGstRate,
        decimal defaultTdsRate,
        string defaultCurrency,
        string modifiedBy)
    {
        if (Status == TaxConfigurationStatus.Archived)
            throw new InvalidOperationException("Cannot update archived tax configuration");

        if (string.IsNullOrWhiteSpace(modifiedBy))
            throw new ArgumentException("Modified by cannot be empty", nameof(modifiedBy));

        if (defaultGstRate < 0 || defaultGstRate > 100)
            throw new ArgumentException("Default GST rate must be between 0 and 100", nameof(defaultGstRate));

        if (defaultTdsRate < 0 || defaultTdsRate > 100)
            throw new ArgumentException("Default TDS rate must be between 0 and 100", nameof(defaultTdsRate));

        var changes = new List<string>();

        if (EnableGstCalculation != enableGstCalculation)
        {
            changes.Add($"Enable GST Calculation: {EnableGstCalculation} → {enableGstCalculation}");
            EnableGstCalculation = enableGstCalculation;
        }

        if (EnableTdsCalculation != enableTdsCalculation)
        {
            changes.Add($"Enable TDS Calculation: {EnableTdsCalculation} → {enableTdsCalculation}");
            EnableTdsCalculation = enableTdsCalculation;
        }

        if (EnableReverseCharge != enableReverseCharge)
        {
            changes.Add($"Enable Reverse Charge: {EnableReverseCharge} → {enableReverseCharge}");
            EnableReverseCharge = enableReverseCharge;
        }

        if (RequireHsnCode != requireHsnCode)
        {
            changes.Add($"Require HSN Code: {RequireHsnCode} → {requireHsnCode}");
            RequireHsnCode = requireHsnCode;
        }

        if (Math.Abs(DefaultGstRate - defaultGstRate) > 0.01m)
        {
            changes.Add($"Default GST Rate: {DefaultGstRate}% → {defaultGstRate}%");
            DefaultGstRate = defaultGstRate;
        }

        if (Math.Abs(DefaultTdsRate - defaultTdsRate) > 0.01m)
        {
            changes.Add($"Default TDS Rate: {DefaultTdsRate}% → {defaultTdsRate}%");
            DefaultTdsRate = defaultTdsRate;
        }

        if (DefaultCurrency != defaultCurrency)
        {
            changes.Add($"Default Currency: {DefaultCurrency} → {defaultCurrency}");
            DefaultCurrency = defaultCurrency;
        }

        if (changes.Any())
        {
            ModifiedBy = modifiedBy.Trim();
            ModifiedAt = DateTime.UtcNow;
            SetUpdatedAt();

            AddHistoryEntry("Tax Settings Updated", $"Settings updated by {modifiedBy}: {string.Join(", ", changes)}");
        }
    }

    public void SetAsDefault(string modifiedBy)
    {
        if (Status != TaxConfigurationStatus.Active)
            throw new InvalidOperationException("Only active tax configurations can be set as default");

        if (!IsDefault)
        {
            IsDefault = true;
            ModifiedBy = modifiedBy.Trim();
            ModifiedAt = DateTime.UtcNow;
            SetUpdatedAt();

            AddHistoryEntry("Set as Default", $"Configuration set as default by {modifiedBy}");
        }
    }

    public void RemoveAsDefault(string modifiedBy)
    {
        if (IsDefault)
        {
            IsDefault = false;
            ModifiedBy = modifiedBy.Trim();
            ModifiedAt = DateTime.UtcNow;
            SetUpdatedAt();

            AddHistoryEntry("Removed as Default", $"Configuration removed as default by {modifiedBy}");
        }
    }

    public void Activate(string activatedBy)
    {
        if (Status == TaxConfigurationStatus.Archived)
            throw new InvalidOperationException("Cannot activate archived tax configuration");

        if (Status != TaxConfigurationStatus.Active)
        {
            Status = TaxConfigurationStatus.Active;
            ModifiedBy = activatedBy.Trim();
            ModifiedAt = DateTime.UtcNow;
            SetUpdatedAt();

            AddHistoryEntry("Activated", $"Configuration activated by {activatedBy}");
        }
    }

    public void Deactivate(string deactivatedBy, string reason)
    {
        if (Status == TaxConfigurationStatus.Archived)
            throw new InvalidOperationException("Cannot deactivate archived tax configuration");

        if (IsDefault)
            throw new InvalidOperationException("Cannot deactivate default tax configuration");

        if (Status != TaxConfigurationStatus.Inactive)
        {
            Status = TaxConfigurationStatus.Inactive;
            ModifiedBy = deactivatedBy.Trim();
            ModifiedAt = DateTime.UtcNow;
            SetUpdatedAt();

            AddHistoryEntry("Deactivated", $"Configuration deactivated by {deactivatedBy}. Reason: {reason}");
        }
    }

    public void Archive(string archivedBy, string reason)
    {
        if (IsDefault)
            throw new InvalidOperationException("Cannot archive default tax configuration");

        Status = TaxConfigurationStatus.Archived;
        ModifiedBy = archivedBy.Trim();
        ModifiedAt = DateTime.UtcNow;
        SetUpdatedAt();

        AddHistoryEntry("Archived", $"Configuration archived by {archivedBy}. Reason: {reason}");
    }

    public void AddRule(string name, string condition, string action, string addedBy)
    {
        if (Status == TaxConfigurationStatus.Archived)
            throw new InvalidOperationException("Cannot add rules to archived tax configuration");

        var rule = new TaxConfigurationRule(Id, name, condition, action, addedBy);
        _rules.Add(rule);

        AddHistoryEntry("Rule Added", $"Rule '{name}' added by {addedBy}");
    }

    public bool IsEffectiveOn(DateTime date)
    {
        return Status == TaxConfigurationStatus.Active &&
               date >= EffectiveFrom &&
               (!EffectiveTo.HasValue || date <= EffectiveTo.Value);
    }

    private void AddHistoryEntry(string action, string details)
    {
        var historyEntry = new TaxConfigurationHistory(Id, action, details, ModifiedBy ?? CreatedBy);
        _history.Add(historyEntry);
    }
}

/// <summary>
/// Entity representing tax configuration rules
/// </summary>
public class TaxConfigurationRule : BaseEntity
{
    public Guid TaxConfigurationId { get; private set; }
    public string Name { get; private set; }
    public string Condition { get; private set; }
    public string Action { get; private set; }
    public bool IsActive { get; private set; }
    public string CreatedBy { get; private set; }

    private TaxConfigurationRule() { }

    public TaxConfigurationRule(Guid taxConfigurationId, string name, string condition, string action, string createdBy)
    {
        TaxConfigurationId = taxConfigurationId;
        Name = name;
        Condition = condition;
        Action = action;
        IsActive = true;
        CreatedBy = createdBy;
    }

    public void Deactivate()
    {
        IsActive = false;
    }
}

/// <summary>
/// Entity representing tax configuration history for audit purposes
/// </summary>
public class TaxConfigurationHistory : BaseEntity
{
    public Guid TaxConfigurationId { get; private set; }
    public string Action { get; private set; }
    public string Details { get; private set; }
    public string ModifiedBy { get; private set; }
    public DateTime ModifiedAt { get; private set; }

    private TaxConfigurationHistory() { }

    public TaxConfigurationHistory(Guid taxConfigurationId, string action, string details, string modifiedBy)
    {
        TaxConfigurationId = taxConfigurationId;
        Action = action;
        Details = details;
        ModifiedBy = modifiedBy;
        ModifiedAt = DateTime.UtcNow;
    }
}


