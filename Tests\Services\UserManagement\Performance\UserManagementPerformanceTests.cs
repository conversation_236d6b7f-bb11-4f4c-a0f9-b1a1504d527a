using System.Net.Http.Json;
using FluentAssertions;
using Tests.Shared.Builders;
using Tests.Shared.Infrastructure;
using Tests.Shared.Performance;
using UserManagement.API;
using Xunit;
using Xunit.Abstractions;

namespace Tests.Services.UserManagement.Performance;

[Trait("Category", "Performance")]
[Trait("Service", "UserManagement")]
public class UserManagementPerformanceTests : BaseIntegrationTest<Program>
{
    private readonly PerformanceTestBase _performanceTest;
    private readonly UserTestDataBuilder _userBuilder;

    public UserManagementPerformanceTests(
        IntegrationTestWebApplicationFactory<Program> factory,
        ITestOutputHelper output) : base(factory)
    {
        _performanceTest = new TestPerformanceRunner(output);
        _userBuilder = new UserTestDataBuilder();
    }

    [Fact]
    public async Task CreateUser_PerformanceTest_ShouldMeetResponseTimeRequirements()
    {
        // Arrange
        var userData = _userBuilder.Build();
        var createRequest = new CreateUserRequest
        {
            Email = userData.Email,
            FirstName = userData.FirstName,
            LastName = userData.LastName,
            PhoneNumber = userData.PhoneNumber,
            CompanyName = userData.CompanyName
        };

        // Act
        var result = await _performanceTest.MeasureAsync(async () =>
        {
            var response = await Client.PostAsJsonAsync("/api/users", createRequest);
            response.EnsureSuccessStatusCode();
        }, "CreateUser");

        // Assert
        _performanceTest.ShouldCompleteWithin(result, TimeSpan.FromMilliseconds(500));
    }

    [Fact]
    public async Task GetUser_PerformanceTest_ShouldMeetResponseTimeRequirements()
    {
        // Arrange
        var user = await CreateTestUserAsync();

        // Act
        var result = await _performanceTest.MeasureAsync(async () =>
        {
            var response = await Client.GetAsync($"/api/users/{user.Id}");
            response.EnsureSuccessStatusCode();
        }, "GetUser");

        // Assert
        _performanceTest.ShouldCompleteWithin(result, TimeSpan.FromMilliseconds(200));
    }

    [Fact]
    public async Task CreateUser_LoadTest_ShouldHandleConcurrentRequests()
    {
        // Arrange
        var concurrentUsers = 50;
        var testDuration = TimeSpan.FromMinutes(1);

        // Act
        var result = await _performanceTest.RunLoadTestAsync(
            async () =>
            {
                var userData = _userBuilder.Build();
                var createRequest = new CreateUserRequest
                {
                    Email = userData.Email,
                    FirstName = userData.FirstName,
                    LastName = userData.LastName,
                    PhoneNumber = userData.PhoneNumber,
                    CompanyName = userData.CompanyName
                };

                var response = await Client.PostAsJsonAsync("/api/users", createRequest);
                response.EnsureSuccessStatusCode();
            },
            concurrentUsers,
            testDuration,
            "CreateUser_LoadTest");

        // Assert
        _performanceTest.ShouldMeetPerformanceCriteria(
            result,
            minOperationsPerSecond: 100,
            maxAverageResponseTime: TimeSpan.FromSeconds(2),
            maxErrorRate: 0.01);
    }

    [Fact]
    public async Task GetUsersByCompany_PerformanceTest_ShouldHandleLargeDatasets()
    {
        // Arrange
        var companyName = "Performance Test Company";
        
        // Create 1000 users for the company
        var createTasks = new List<Task>();
        for (int i = 0; i < 1000; i++)
        {
            createTasks.Add(CreateTestUserAsync(_userBuilder.WithCompany(companyName).Build()));
        }
        await Task.WhenAll(createTasks);

        // Act
        var result = await _performanceTest.MeasureAsync(async () =>
        {
            var response = await Client.GetAsync($"/api/users/company/{Uri.EscapeDataString(companyName)}");
            response.EnsureSuccessStatusCode();
        }, "GetUsersByCompany_LargeDataset");

        // Assert
        _performanceTest.ShouldCompleteWithin(result, TimeSpan.FromSeconds(2));
    }

    [Fact]
    public async Task UpdateUser_PerformanceTest_ShouldMeetResponseTimeRequirements()
    {
        // Arrange
        var user = await CreateTestUserAsync();
        var updateRequest = new UpdateUserRequest
        {
            FirstName = "Updated",
            LastName = "Name",
            PhoneNumber = "+9876543210"
        };

        // Act
        var result = await _performanceTest.MeasureAsync(async () =>
        {
            var response = await Client.PutAsJsonAsync($"/api/users/{user.Id}", updateRequest);
            response.EnsureSuccessStatusCode();
        }, "UpdateUser");

        // Assert
        _performanceTest.ShouldCompleteWithin(result, TimeSpan.FromMilliseconds(300));
    }

    [Fact]
    public async Task DeleteUser_PerformanceTest_ShouldMeetResponseTimeRequirements()
    {
        // Arrange
        var user = await CreateTestUserAsync();

        // Act
        var result = await _performanceTest.MeasureAsync(async () =>
        {
            var response = await Client.DeleteAsync($"/api/users/{user.Id}");
            response.EnsureSuccessStatusCode();
        }, "DeleteUser");

        // Assert
        _performanceTest.ShouldCompleteWithin(result, TimeSpan.FromMilliseconds(200));
    }

    [Fact]
    public async Task MixedOperations_LoadTest_ShouldHandleRealisticWorkload()
    {
        // Arrange
        var concurrentUsers = 20;
        var testDuration = TimeSpan.FromMinutes(2);
        var createdUsers = new List<Guid>();

        // Act
        var result = await _performanceTest.RunLoadTestAsync(
            async () =>
            {
                var random = new Random();
                var operation = random.Next(1, 5);

                switch (operation)
                {
                    case 1: // Create user (30%)
                    case 2:
                    case 3:
                        var userData = _userBuilder.Build();
                        var createRequest = new CreateUserRequest
                        {
                            Email = userData.Email,
                            FirstName = userData.FirstName,
                            LastName = userData.LastName,
                            PhoneNumber = userData.PhoneNumber,
                            CompanyName = userData.CompanyName
                        };
                        var createResponse = await Client.PostAsJsonAsync("/api/users", createRequest);
                        createResponse.EnsureSuccessStatusCode();
                        break;

                    case 4: // Get user (40%)
                        if (createdUsers.Any())
                        {
                            var userId = createdUsers[random.Next(createdUsers.Count)];
                            var getResponse = await Client.GetAsync($"/api/users/{userId}");
                            // Don't fail if user was deleted
                        }
                        break;

                    case 5: // Update user (30%)
                        if (createdUsers.Any())
                        {
                            var userId = createdUsers[random.Next(createdUsers.Count)];
                            var updateRequest = new UpdateUserRequest
                            {
                                FirstName = "Updated",
                                LastName = "Name",
                                PhoneNumber = "+9876543210"
                            };
                            var updateResponse = await Client.PutAsJsonAsync($"/api/users/{userId}", updateRequest);
                            // Don't fail if user was deleted
                        }
                        break;
                }
            },
            concurrentUsers,
            testDuration,
            "MixedOperations_LoadTest");

        // Assert
        _performanceTest.ShouldMeetPerformanceCriteria(
            result,
            minOperationsPerSecond: 50,
            maxAverageResponseTime: TimeSpan.FromSeconds(3),
            maxErrorRate: 0.05);
    }

    private async Task<UserResponse> CreateTestUserAsync(UserTestData? userData = null)
    {
        userData ??= _userBuilder.Build();
        
        var createRequest = new CreateUserRequest
        {
            Email = userData.Email,
            FirstName = userData.FirstName,
            LastName = userData.LastName,
            PhoneNumber = userData.PhoneNumber,
            CompanyName = userData.CompanyName
        };

        var response = await Client.PostAsJsonAsync("/api/users", createRequest);
        response.EnsureSuccessStatusCode();
        
        var content = await response.Content.ReadAsStringAsync();
        return System.Text.Json.JsonSerializer.Deserialize<UserResponse>(content, new System.Text.Json.JsonSerializerOptions
        {
            PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase
        })!;
    }

    private class TestPerformanceRunner : PerformanceTestBase
    {
        public TestPerformanceRunner(ITestOutputHelper output) : base(output) { }
    }
}
