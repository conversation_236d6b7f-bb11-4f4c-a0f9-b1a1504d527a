using Microsoft.Extensions.Logging;
using Moq;
using UserManagement.Application.Admin.Queries.GetDashboardStats;
using UserManagement.Domain.Entities;
using UserManagement.Domain.Repositories;
using Xunit;

namespace UserManagement.Tests.Unit.Application.Admin.Queries
{
    public class GetDashboardStatsQueryHandlerTests
    {
        private readonly Mock<IUserProfileRepository> _mockUserProfileRepository;
        private readonly Mock<IDocumentSubmissionRepository> _mockDocumentSubmissionRepository;
        private readonly Mock<ILogger<GetDashboardStatsQueryHandler>> _mockLogger;
        private readonly GetDashboardStatsQueryHandler _handler;

        public GetDashboardStatsQueryHandlerTests()
        {
            _mockUserProfileRepository = new Mock<IUserProfileRepository>();
            _mockDocumentSubmissionRepository = new Mock<IDocumentSubmissionRepository>();
            _mockLogger = new Mock<ILogger<GetDashboardStatsQueryHandler>>();
            
            _handler = new GetDashboardStatsQueryHandler(
                _mockUserProfileRepository.Object,
                _mockDocumentSubmissionRepository.Object,
                _mockLogger.Object);
        }

        [Fact]
        public async Task Handle_ValidRequest_ShouldReturnDashboardStats()
        {
            // Arrange
            var query = new GetDashboardStatsQuery();

            _mockUserProfileRepository.Setup(x => x.GetTotalUsersCountAsync()).ReturnsAsync(100);
            _mockUserProfileRepository.Setup(x => x.GetCountByStatusAsync(ProfileStatus.UnderReview)).ReturnsAsync(15);
            _mockUserProfileRepository.Setup(x => x.GetApprovedTodayCountAsync()).ReturnsAsync(5);
            _mockUserProfileRepository.Setup(x => x.GetRejectedTodayCountAsync()).ReturnsAsync(2);
            
            _mockUserProfileRepository.Setup(x => x.GetCountByUserTypeAsync(UserType.TransportCompany)).ReturnsAsync(20);
            _mockUserProfileRepository.Setup(x => x.GetCountByUserTypeAsync(UserType.Broker)).ReturnsAsync(15);
            _mockUserProfileRepository.Setup(x => x.GetCountByUserTypeAsync(UserType.Carrier)).ReturnsAsync(30);
            _mockUserProfileRepository.Setup(x => x.GetCountByUserTypeAsync(UserType.Driver)).ReturnsAsync(25);
            _mockUserProfileRepository.Setup(x => x.GetCountByUserTypeAsync(UserType.Shipper)).ReturnsAsync(10);
            
            _mockUserProfileRepository.Setup(x => x.GetCountByStatusAsync(ProfileStatus.Incomplete)).ReturnsAsync(20);
            _mockUserProfileRepository.Setup(x => x.GetCountByStatusAsync(ProfileStatus.Complete)).ReturnsAsync(25);
            _mockUserProfileRepository.Setup(x => x.GetCountByStatusAsync(ProfileStatus.Approved)).ReturnsAsync(40);
            _mockUserProfileRepository.Setup(x => x.GetCountByStatusAsync(ProfileStatus.Rejected)).ReturnsAsync(15);

            _mockDocumentSubmissionRepository.Setup(x => x.GetPendingDocumentsCountAsync()).ReturnsAsync(12);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(100, result.TotalUsers);
            Assert.Equal(15, result.PendingApprovals);
            Assert.Equal(12, result.PendingDocuments);
            Assert.Equal(5, result.ApprovedToday);
            Assert.Equal(2, result.RejectedToday);

            // Verify user type breakdown
            Assert.Equal(20, result.UserTypeBreakdown.TransportCompanies);
            Assert.Equal(15, result.UserTypeBreakdown.Brokers);
            Assert.Equal(30, result.UserTypeBreakdown.Carriers);
            Assert.Equal(25, result.UserTypeBreakdown.Drivers);
            Assert.Equal(10, result.UserTypeBreakdown.Shippers);

            // Verify status breakdown
            Assert.Equal(20, result.StatusBreakdown.Incomplete);
            Assert.Equal(25, result.StatusBreakdown.Complete);
            Assert.Equal(15, result.StatusBreakdown.UnderReview);
            Assert.Equal(40, result.StatusBreakdown.Approved);
            Assert.Equal(15, result.StatusBreakdown.Rejected);

            Assert.True(result.LastUpdated <= DateTime.UtcNow);
        }

        [Fact]
        public async Task Handle_RepositoryThrowsException_ShouldPropagateException()
        {
            // Arrange
            var query = new GetDashboardStatsQuery();

            _mockUserProfileRepository
                .Setup(x => x.GetTotalUsersCountAsync())
                .ThrowsAsync(new Exception("Database error"));

            // Act & Assert
            await Assert.ThrowsAsync<Exception>(() => _handler.Handle(query, CancellationToken.None));
        }
    }
}
