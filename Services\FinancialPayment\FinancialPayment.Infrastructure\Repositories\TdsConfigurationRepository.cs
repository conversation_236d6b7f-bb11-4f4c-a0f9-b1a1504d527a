using Microsoft.EntityFrameworkCore;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;
using FinancialPayment.Application.Interfaces.Repositories;
using FinancialPayment.Infrastructure.Data;

namespace FinancialPayment.Infrastructure.Repositories;

public class TdsConfigurationRepository : ITdsConfigurationRepository
{
    private readonly FinancialPaymentDbContext _context;

    public TdsConfigurationRepository(FinancialPaymentDbContext context)
    {
        _context = context;
    }

    public async Task<TdsConfiguration?> GetByIdAsync(Guid id)
    {
        return await _context.TdsConfigurations
            .Include(t => t.History)
            .FirstOrDefaultAsync(t => t.Id == id);
    }

    public async Task<List<TdsConfiguration>> GetAllAsync()
    {
        return await _context.TdsConfigurations
            .Include(t => t.History)
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<TdsConfiguration>> GetActiveAsync()
    {
        return await _context.TdsConfigurations
            .Include(t => t.History)
            .Where(t => t.Status == TaxConfigurationStatus.Active)
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<TdsConfiguration>> GetByStatusAsync(TaxConfigurationStatus status)
    {
        return await _context.TdsConfigurations
            .Include(t => t.History)
            .Where(t => t.Status == status)
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<TdsConfiguration>> GetBySectionAsync(TdsSection section)
    {
        return await _context.TdsConfigurations
            .Include(t => t.History)
            .Where(t => t.Section == section)
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<TdsConfiguration>> GetByEntityTypeAsync(EntityType entityType)
    {
        return await _context.TdsConfigurations
            .Include(t => t.History)
            .Where(t => t.EntityType == entityType)
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<TdsConfiguration>> GetBySectionAndEntityTypeAsync(TdsSection section, EntityType entityType)
    {
        return await _context.TdsConfigurations
            .Include(t => t.History)
            .Where(t => t.Section == section && t.EntityType == entityType)
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<TdsConfiguration>> GetEffectiveOnDateAsync(DateTime date)
    {
        return await _context.TdsConfigurations
            .Include(t => t.History)
            .Where(t => t.Status == TaxConfigurationStatus.Active &&
                       t.TaxRate.EffectiveFrom <= date &&
                       (t.TaxRate.EffectiveTo == null || t.TaxRate.EffectiveTo >= date))
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync();
    }

    public async Task<TdsConfiguration?> GetApplicableConfigurationAsync(TdsSection section, EntityType entityType, DateTime date, bool hasPan = true)
    {
        return await _context.TdsConfigurations
            .Include(t => t.History)
            .Where(t => t.Status == TaxConfigurationStatus.Active &&
                       t.Section == section &&
                       t.EntityType == entityType &&
                       t.TaxRate.EffectiveFrom <= date &&
                       (t.TaxRate.EffectiveTo == null || t.TaxRate.EffectiveTo >= date) &&
                       (!t.RequiresPan || hasPan))
            .OrderByDescending(t => t.CreatedAt)
            .FirstOrDefaultAsync();
    }

    public async Task<List<TdsConfiguration>> GetByTaxRateRangeAsync(decimal minRate, decimal maxRate)
    {
        return await _context.TdsConfigurations
            .Include(t => t.History)
            .Where(t => t.TaxRate.Rate >= minRate && t.TaxRate.Rate <= maxRate)
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<TdsConfiguration>> GetRequiringPanAsync()
    {
        return await _context.TdsConfigurations
            .Include(t => t.History)
            .Where(t => t.RequiresPan)
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<TdsConfiguration>> GetWithHigherRateWithoutPanAsync()
    {
        return await _context.TdsConfigurations
            .Include(t => t.History)
            .Where(t => t.HigherRateWithoutPan > 0)
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<TdsConfiguration>> GetByThresholdAmountAsync(Money thresholdAmount)
    {
        return await _context.TdsConfigurations
            .Include(t => t.History)
            .Where(t => t.Threshold.ThresholdAmount.Amount <= thresholdAmount.Amount &&
                       t.Threshold.ThresholdAmount.Currency == thresholdAmount.Currency)
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<TdsConfiguration>> GetByCreatedByAsync(string createdBy)
    {
        return await _context.TdsConfigurations
            .Include(t => t.History)
            .Where(t => t.CreatedBy == createdBy)
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<TdsConfiguration>> GetByDateRangeAsync(DateTime from, DateTime to)
    {
        return await _context.TdsConfigurations
            .Include(t => t.History)
            .Where(t => t.CreatedAt >= from && t.CreatedAt <= to)
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync();
    }

    public async Task<TdsConfiguration> AddAsync(TdsConfiguration tdsConfiguration)
    {
        _context.TdsConfigurations.Add(tdsConfiguration);
        return tdsConfiguration;
    }

    public async Task UpdateAsync(TdsConfiguration tdsConfiguration)
    {
        _context.TdsConfigurations.Update(tdsConfiguration);
        await Task.CompletedTask;
    }

    public async Task DeleteAsync(Guid id)
    {
        var tdsConfiguration = await _context.TdsConfigurations.FindAsync(id);
        if (tdsConfiguration != null)
        {
            _context.TdsConfigurations.Remove(tdsConfiguration);
        }
    }

    public async Task<bool> ExistsAsync(Guid id)
    {
        return await _context.TdsConfigurations.AnyAsync(t => t.Id == id);
    }

    public async Task<bool> ExistsByNameAsync(string name, Guid? excludeId = null)
    {
        var query = _context.TdsConfigurations.Where(t => t.Name == name);
        if (excludeId.HasValue)
            query = query.Where(t => t.Id != excludeId.Value);
        
        return await query.AnyAsync();
    }

    public async Task<bool> ExistsBySectionAndEntityTypeAsync(TdsSection section, EntityType entityType, Guid? excludeId = null)
    {
        var query = _context.TdsConfigurations.Where(t => t.Section == section && t.EntityType == entityType);
        if (excludeId.HasValue)
            query = query.Where(t => t.Id != excludeId.Value);
        
        return await query.AnyAsync();
    }

    public async Task<int> GetCountByStatusAsync(TaxConfigurationStatus status)
    {
        return await _context.TdsConfigurations.CountAsync(t => t.Status == status);
    }

    public async Task<List<TdsConfiguration>> SearchAsync(string searchTerm, int skip = 0, int take = 50)
    {
        return await _context.TdsConfigurations
            .Include(t => t.History)
            .Where(t => t.Name.Contains(searchTerm) || 
                       t.Description.Contains(searchTerm) ||
                       (t.SpecialConditions != null && t.SpecialConditions.Contains(searchTerm)))
            .OrderByDescending(t => t.CreatedAt)
            .Skip(skip)
            .Take(take)
            .ToListAsync();
    }

    public async Task<List<TdsConfigurationHistory>> GetHistoryAsync(Guid tdsConfigurationId)
    {
        return await _context.Set<TdsConfigurationHistory>()
            .Where(h => h.TdsConfigurationId == tdsConfigurationId)
            .OrderByDescending(h => h.ModifiedAt)
            .ToListAsync();
    }
}
