namespace MonitoringObservability.Domain.DTOs;

/// <summary>
/// DTO for service topology information
/// </summary>
public class ServiceTopologyDto
{
    public string ServiceName { get; set; } = string.Empty;
    public string ServiceType { get; set; } = string.Empty;
    public string Environment { get; set; } = string.Empty;
    public List<ServiceInstanceDto> Instances { get; set; } = new();
    public List<ServiceConnectionDto> Connections { get; set; } = new();
    public ServiceHealthDto Health { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
    public DateTime LastUpdated { get; set; }
}

public class ServiceInstanceDto
{
    public string InstanceId { get; set; } = string.Empty;
    public string Host { get; set; } = string.Empty;
    public int Port { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public Dictionary<string, string> Tags { get; set; } = new();
}

public class ServiceConnectionDto
{
    public string TargetService { get; set; } = string.Empty;
    public string ConnectionType { get; set; } = string.Empty;
    public string Protocol { get; set; } = string.Empty;
    public bool IsHealthy { get; set; }
    public double LatencyMs { get; set; }
    public int RequestCount { get; set; }
    public double ErrorRate { get; set; }
}

public class ServiceHealthDto
{
    public string Status { get; set; } = string.Empty;
    public double HealthScore { get; set; }
    public List<HealthCheckDto> Checks { get; set; } = new();
    public DateTime LastChecked { get; set; }
}

public class HealthCheckDto
{
    public string Name { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string? Description { get; set; }
    public TimeSpan Duration { get; set; }
    public Dictionary<string, object> Data { get; set; } = new();
}
