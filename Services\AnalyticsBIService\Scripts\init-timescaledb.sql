-- TimescaleDB initialization script for TLI Analytics & Business Intelligence Service
-- This script sets up the TimescaleDB extension and basic configuration

-- Enable TimescaleDB extension
CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;

-- Enable additional useful extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";
CREATE EXTENSION IF NOT EXISTS "btree_gist";

-- Create custom functions for analytics operations
CREATE OR REPLACE FUNCTION analytics.calculate_percentage_change(current_value numeric, previous_value numeric)
RETURNS numeric
LANGUAGE sql
IMMUTABLE
AS $$
    SELECT CASE 
        WHEN previous_value = 0 THEN 
            CASE WHEN current_value = 0 THEN 0 ELSE 100 END
        ELSE 
            ROUND(((current_value - previous_value) / previous_value * 100)::numeric, 2)
    END;
$$;

CREATE OR REPLACE FUNCTION analytics.calculate_growth_rate(current_value numeric, previous_value numeric)
RETURNS numeric
LANGUAGE sql
IMMUTABLE
AS $$
    SELECT CASE 
        WHEN previous_value = 0 OR previous_value IS NULL THEN NULL
        ELSE ROUND(((current_value - previous_value) / previous_value)::numeric, 4)
    END;
$$;

CREATE OR REPLACE FUNCTION analytics.get_trend_direction(values numeric[])
RETURNS text
LANGUAGE plpgsql
IMMUTABLE
AS $$
DECLARE
    trend_slope numeric;
    variance_threshold numeric := 0.1;
BEGIN
    -- Calculate simple linear trend
    IF array_length(values, 1) < 2 THEN
        RETURN 'Stable';
    END IF;
    
    -- Simple trend calculation (last value vs first value)
    trend_slope := (values[array_length(values, 1)] - values[1]) / array_length(values, 1);
    
    IF abs(trend_slope) < variance_threshold THEN
        RETURN 'Stable';
    ELSIF trend_slope > 0 THEN
        RETURN 'Increasing';
    ELSE
        RETURN 'Decreasing';
    END IF;
END;
$$;

-- Create function for KPI performance evaluation
CREATE OR REPLACE FUNCTION analytics.evaluate_kpi_performance(
    current_value numeric,
    target_value numeric,
    is_higher_better boolean DEFAULT true
)
RETURNS text
LANGUAGE sql
IMMUTABLE
AS $$
    SELECT CASE 
        WHEN target_value = 0 OR target_value IS NULL THEN 'Average'
        WHEN is_higher_better THEN
            CASE 
                WHEN current_value >= target_value * 1.1 THEN 'Excellent'
                WHEN current_value >= target_value * 0.95 THEN 'Good'
                WHEN current_value >= target_value * 0.8 THEN 'Average'
                ELSE 'BelowAverage'
            END
        ELSE
            CASE 
                WHEN current_value <= target_value * 0.9 THEN 'Excellent'
                WHEN current_value <= target_value * 1.05 THEN 'Good'
                WHEN current_value <= target_value * 1.2 THEN 'Average'
                ELSE 'BelowAverage'
            END
    END;
$$;

-- Create function for alert severity evaluation
CREATE OR REPLACE FUNCTION analytics.evaluate_alert_severity(
    current_value numeric,
    warning_threshold numeric,
    critical_threshold numeric,
    is_higher_better boolean DEFAULT true
)
RETURNS text
LANGUAGE sql
IMMUTABLE
AS $$
    SELECT CASE 
        WHEN critical_threshold IS NOT NULL THEN
            CASE 
                WHEN is_higher_better AND current_value <= critical_threshold THEN 'Critical'
                WHEN NOT is_higher_better AND current_value >= critical_threshold THEN 'Critical'
                WHEN warning_threshold IS NOT NULL THEN
                    CASE 
                        WHEN is_higher_better AND current_value <= warning_threshold THEN 'Warning'
                        WHEN NOT is_higher_better AND current_value >= warning_threshold THEN 'Warning'
                        ELSE 'Info'
                    END
                ELSE 'Info'
            END
        WHEN warning_threshold IS NOT NULL THEN
            CASE 
                WHEN is_higher_better AND current_value <= warning_threshold THEN 'Warning'
                WHEN NOT is_higher_better AND current_value >= warning_threshold THEN 'Warning'
                ELSE 'Info'
            END
        ELSE 'Info'
    END;
$$;

-- Create aggregation functions for analytics
CREATE OR REPLACE FUNCTION analytics.aggregate_metrics_by_period(
    metric_name text,
    period_type text,
    start_date timestamp,
    end_date timestamp
)
RETURNS TABLE(
    period_start timestamp,
    period_end timestamp,
    avg_value numeric,
    min_value numeric,
    max_value numeric,
    sum_value numeric,
    count_value bigint
)
LANGUAGE sql
STABLE
AS $$
    SELECT 
        time_bucket(
            CASE period_type
                WHEN 'hourly' THEN INTERVAL '1 hour'
                WHEN 'daily' THEN INTERVAL '1 day'
                WHEN 'weekly' THEN INTERVAL '1 week'
                WHEN 'monthly' THEN INTERVAL '1 month'
                ELSE INTERVAL '1 day'
            END,
            m.created_at
        ) as period_start,
        time_bucket(
            CASE period_type
                WHEN 'hourly' THEN INTERVAL '1 hour'
                WHEN 'daily' THEN INTERVAL '1 day'
                WHEN 'weekly' THEN INTERVAL '1 week'
                WHEN 'monthly' THEN INTERVAL '1 month'
                ELSE INTERVAL '1 day'
            END,
            m.created_at
        ) + CASE period_type
                WHEN 'hourly' THEN INTERVAL '1 hour'
                WHEN 'daily' THEN INTERVAL '1 day'
                WHEN 'weekly' THEN INTERVAL '1 week'
                WHEN 'monthly' THEN INTERVAL '1 month'
                ELSE INTERVAL '1 day'
            END - INTERVAL '1 second' as period_end,
        AVG(m.metric_value) as avg_value,
        MIN(m.metric_value) as min_value,
        MAX(m.metric_value) as max_value,
        SUM(m.metric_value) as sum_value,
        COUNT(*) as count_value
    FROM analytics.metrics m
    WHERE m.name = metric_name
        AND m.created_at >= start_date
        AND m.created_at <= end_date
    GROUP BY period_start
    ORDER BY period_start;
$$;

-- Set up TimescaleDB configuration for better performance
ALTER SYSTEM SET shared_preload_libraries = 'timescaledb';
ALTER SYSTEM SET max_connections = 200;
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = 100;
ALTER SYSTEM SET random_page_cost = 1.1;
ALTER SYSTEM SET effective_io_concurrency = 200;
ALTER SYSTEM SET work_mem = '4MB';
ALTER SYSTEM SET min_wal_size = '1GB';
ALTER SYSTEM SET max_wal_size = '4GB';

-- Create database roles for analytics service
DO $$
BEGIN
    -- Application role for the analytics service
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'tli_analytics_app') THEN
        CREATE ROLE tli_analytics_app WITH LOGIN PASSWORD 'analytics_secure_password_2024';
    END IF;
    
    -- Read-only role for reporting and BI tools
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'tli_analytics_readonly') THEN
        CREATE ROLE tli_analytics_readonly WITH LOGIN PASSWORD 'readonly_secure_password_2024';
    END IF;
    
    -- Backup role for database maintenance
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'tli_analytics_backup') THEN
        CREATE ROLE tli_analytics_backup WITH LOGIN PASSWORD 'backup_secure_password_2024';
    END IF;
END
$$;

-- Grant appropriate permissions
GRANT CONNECT ON DATABASE tli_analytics_dev TO tli_analytics_app;
GRANT CONNECT ON DATABASE tli_analytics_dev TO tli_analytics_readonly;
GRANT CONNECT ON DATABASE tli_analytics_dev TO tli_analytics_backup;

-- Create schema for analytics service
CREATE SCHEMA IF NOT EXISTS analytics;

-- Grant schema permissions
GRANT USAGE ON SCHEMA analytics TO tli_analytics_app;
GRANT USAGE ON SCHEMA analytics TO tli_analytics_readonly;
GRANT USAGE ON SCHEMA analytics TO tli_analytics_backup;

GRANT CREATE ON SCHEMA analytics TO tli_analytics_app;
GRANT SELECT ON ALL TABLES IN SCHEMA analytics TO tli_analytics_readonly;
GRANT SELECT ON ALL TABLES IN SCHEMA analytics TO tli_analytics_backup;

-- Set default privileges for future tables
ALTER DEFAULT PRIVILEGES IN SCHEMA analytics GRANT SELECT ON TABLES TO tli_analytics_readonly;
ALTER DEFAULT PRIVILEGES IN SCHEMA analytics GRANT SELECT ON TABLES TO tli_analytics_backup;

-- Create audit trigger function for tracking changes
CREATE OR REPLACE FUNCTION analytics.audit_trigger_function()
RETURNS trigger
LANGUAGE plpgsql
AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- Log insert operations for critical tables
        IF TG_TABLE_NAME IN ('analytics_events', 'metrics', 'alerts') THEN
            INSERT INTO analytics.audit_logs (
                id,
                user_id,
                action,
                entity_type,
                entity_id,
                timestamp,
                changes,
                additional_data,
                created_at,
                updated_at
            ) VALUES (
                gen_random_uuid(),
                COALESCE(NEW.user_id, '00000000-0000-0000-0000-000000000000'::uuid),
                'INSERT',
                TG_TABLE_NAME,
                NEW.id,
                NOW(),
                to_jsonb(NEW),
                jsonb_build_object('table', TG_TABLE_NAME, 'operation', TG_OP),
                NOW(),
                NOW()
            );
        END IF;
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        -- Log update operations for critical tables
        IF TG_TABLE_NAME IN ('metrics', 'alerts', 'alert_rules') THEN
            INSERT INTO analytics.audit_logs (
                id,
                user_id,
                action,
                entity_type,
                entity_id,
                timestamp,
                changes,
                additional_data,
                created_at,
                updated_at
            ) VALUES (
                gen_random_uuid(),
                COALESCE(NEW.user_id, OLD.user_id, '00000000-0000-0000-0000-000000000000'::uuid),
                'UPDATE',
                TG_TABLE_NAME,
                NEW.id,
                NOW(),
                jsonb_build_object('old', to_jsonb(OLD), 'new', to_jsonb(NEW)),
                jsonb_build_object('table', TG_TABLE_NAME, 'operation', TG_OP),
                NOW(),
                NOW()
            );
        END IF;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        -- Log delete operations for critical tables
        IF TG_TABLE_NAME IN ('metrics', 'alerts', 'alert_rules') THEN
            INSERT INTO analytics.audit_logs (
                id,
                user_id,
                action,
                entity_type,
                entity_id,
                timestamp,
                changes,
                additional_data,
                created_at,
                updated_at
            ) VALUES (
                gen_random_uuid(),
                COALESCE(OLD.user_id, '00000000-0000-0000-0000-000000000000'::uuid),
                'DELETE',
                TG_TABLE_NAME,
                OLD.id,
                NOW(),
                to_jsonb(OLD),
                jsonb_build_object('table', TG_TABLE_NAME, 'operation', TG_OP),
                NOW(),
                NOW()
            );
        END IF;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$;

-- Log successful initialization
DO $$
BEGIN
    RAISE NOTICE 'TimescaleDB initialization completed successfully for TLI Analytics & BI Service';
    RAISE NOTICE 'Extensions enabled: timescaledb, uuid-ossp, pg_stat_statements, pg_trgm, btree_gin, btree_gist';
    RAISE NOTICE 'Custom functions created for analytics operations';
    RAISE NOTICE 'Database roles created: tli_analytics_app, tli_analytics_readonly, tli_analytics_backup';
    RAISE NOTICE 'Schema created: analytics';
END
$$;
