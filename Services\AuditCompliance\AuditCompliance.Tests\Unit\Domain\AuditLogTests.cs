using Xunit;
using FluentAssertions;
using AuditCompliance.Domain.Entities;
using AuditCompliance.Domain.Enums;
using AuditCompliance.Domain.ValueObjects;

namespace AuditCompliance.Tests.Unit.Domain;

public class AuditLogTests
{
    [Fact]
    public void AuditLog_Creation_ShouldSetPropertiesCorrectly()
    {
        // Arrange
        var eventType = AuditEventType.UserLogin;
        var severity = AuditSeverity.Info;
        var entityType = "User";
        var action = "Login";
        var description = "User logged in successfully";
        var userId = Guid.NewGuid();
        var userName = "testuser";
        var userRole = "Admin";
        var entityId = Guid.NewGuid();

        // Act
        var auditLog = new AuditLog(
            eventType,
            severity,
            entityType,
            action,
            description,
            userId,
            userName,
            userRole,
            entityId);

        // Assert
        auditLog.EventType.Should().Be(eventType);
        auditLog.Severity.Should().Be(severity);
        auditLog.EntityType.Should().Be(entityType);
        auditLog.Action.Should().Be(action);
        auditLog.Description.Should().Be(description);
        auditLog.UserId.Should().Be(userId);
        auditLog.UserName.Should().Be(userName);
        auditLog.UserRole.Should().Be(userRole);
        auditLog.EntityId.Should().Be(entityId);
        auditLog.EventTimestamp.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void AuditLog_CreateUserEvent_ShouldCreateCorrectAuditLog()
    {
        // Arrange
        var eventType = AuditEventType.UserLogin;
        var userId = Guid.NewGuid();
        var userName = "testuser";
        var userRole = "Admin";
        var action = "Login";
        var description = "User logged in successfully";

        // Act
        var auditLog = AuditLog.CreateUserEvent(
            eventType,
            userId,
            userName,
            userRole,
            action,
            description);

        // Assert
        auditLog.EventType.Should().Be(eventType);
        auditLog.Severity.Should().Be(AuditSeverity.Info);
        auditLog.EntityType.Should().Be("User");
        auditLog.UserId.Should().Be(userId);
        auditLog.UserName.Should().Be(userName);
        auditLog.UserRole.Should().Be(userRole);
        auditLog.EntityId.Should().Be(userId);
    }

    [Fact]
    public void AuditLog_CreateSystemEvent_ShouldCreateCorrectAuditLog()
    {
        // Arrange
        var eventType = AuditEventType.SystemStartup;
        var severity = AuditSeverity.Info;
        var action = "Startup";
        var description = "System started successfully";

        // Act
        var auditLog = AuditLog.CreateSystemEvent(
            eventType,
            severity,
            action,
            description);

        // Assert
        auditLog.EventType.Should().Be(eventType);
        auditLog.Severity.Should().Be(severity);
        auditLog.EntityType.Should().Be("System");
        auditLog.UserId.Should().BeNull();
        auditLog.UserName.Should().BeNull();
    }

    [Fact]
    public void AuditLog_CreateSecurityEvent_ShouldCreateHighSeverityAuditLog()
    {
        // Arrange
        var eventType = AuditEventType.UnauthorizedAccess;
        var action = "Unauthorized Access Attempt";
        var description = "Failed login attempt detected";
        var userId = Guid.NewGuid();
        var userName = "testuser";

        // Act
        var auditLog = AuditLog.CreateSecurityEvent(
            eventType,
            action,
            description,
            userId,
            userName);

        // Assert
        auditLog.EventType.Should().Be(eventType);
        auditLog.Severity.Should().Be(AuditSeverity.High);
        auditLog.EntityType.Should().Be("Security");
        auditLog.ComplianceFlags.Should().Contain(ComplianceStandard.AccessControl);
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    public void AuditLog_Creation_WithInvalidEntityType_ShouldThrowException(string entityType)
    {
        // Arrange & Act & Assert
        Assert.Throws<ArgumentException>(() => new AuditLog(
            AuditEventType.UserLogin,
            AuditSeverity.Info,
            entityType,
            "Login",
            "User logged in"));
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    public void AuditLog_Creation_WithInvalidAction_ShouldThrowException(string action)
    {
        // Arrange & Act & Assert
        Assert.Throws<ArgumentException>(() => new AuditLog(
            AuditEventType.UserLogin,
            AuditSeverity.Info,
            "User",
            action,
            "User logged in"));
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    public void AuditLog_Creation_WithInvalidDescription_ShouldThrowException(string description)
    {
        // Arrange & Act & Assert
        Assert.Throws<ArgumentException>(() => new AuditLog(
            AuditEventType.UserLogin,
            AuditSeverity.Info,
            "User",
            "Login",
            description));
    }

    [Fact]
    public void AuditLog_RequiresEncryption_WithGDPRFlag_ShouldReturnTrue()
    {
        // Arrange
        var auditLog = new AuditLog(
            AuditEventType.DataExport,
            AuditSeverity.Info,
            "User",
            "Export",
            "Data exported",
            complianceFlags: new List<ComplianceStandard> { ComplianceStandard.GDPR });

        // Act & Assert
        auditLog.RequiresEncryption().Should().BeTrue();
    }

    [Fact]
    public void AuditLog_RequiresEncryption_WithHighSeverity_ShouldReturnTrue()
    {
        // Arrange
        var auditLog = new AuditLog(
            AuditEventType.SecurityBreach,
            AuditSeverity.High,
            "Security",
            "Breach",
            "Security breach detected");

        // Act & Assert
        auditLog.RequiresEncryption().Should().BeTrue();
    }

    [Fact]
    public void AuditLog_RequiresEncryption_WithLowSeverityAndNoFlags_ShouldReturnFalse()
    {
        // Arrange
        var auditLog = new AuditLog(
            AuditEventType.UserLogin,
            AuditSeverity.Info,
            "User",
            "Login",
            "User logged in");

        // Act & Assert
        auditLog.RequiresEncryption().Should().BeFalse();
    }
}
