using Shared.Domain.ValueObjects;

namespace NetworkFleetManagement.Domain.ValueObjects;

/// <summary>
/// Performance metrics specifically for preferred partner relationships
/// </summary>
public class PartnerPerformanceMetrics : ValueObject
{
    public decimal OverallRating { get; private set; }
    public decimal OnTimePerformance { get; private set; }
    public decimal QualityRating { get; private set; }
    public decimal CommunicationRating { get; private set; }
    public decimal CostEffectiveness { get; private set; }
    public decimal ReliabilityScore { get; private set; }

    // Collaboration metrics
    public int TotalCollaborations { get; private set; }
    public int SuccessfulCollaborations { get; private set; }
    public int FailedCollaborations { get; private set; }
    public int CancelledCollaborations { get; private set; }

    // Financial metrics
    public decimal TotalRevenue { get; private set; }
    public decimal AverageOrderValue { get; private set; }
    public decimal PaymentTimeliness { get; private set; } // Percentage of on-time payments

    // Response and service metrics
    public decimal AverageResponseTimeHours { get; private set; }
    public decimal ServiceCompletionRate { get; private set; }
    public decimal CustomerSatisfactionScore { get; private set; }

    // Issue and dispute metrics
    public int TotalIssues { get; private set; }
    public int ResolvedIssues { get; private set; }
    public decimal IssueResolutionRate { get; private set; }
    public decimal AverageIssueResolutionTimeHours { get; private set; }

    // Trend indicators
    public decimal PerformanceTrend { get; private set; } // Positive = improving, Negative = declining
    public DateTime LastUpdated { get; private set; }
    public DateTime LastCollaboration { get; private set; }

    private PartnerPerformanceMetrics() { }

    public PartnerPerformanceMetrics(
        decimal overallRating = 0,
        decimal onTimePerformance = 0,
        decimal qualityRating = 0,
        decimal communicationRating = 0,
        decimal costEffectiveness = 0,
        decimal reliabilityScore = 0,
        int totalCollaborations = 0,
        int successfulCollaborations = 0,
        int failedCollaborations = 0,
        int cancelledCollaborations = 0,
        decimal totalRevenue = 0,
        decimal averageOrderValue = 0,
        decimal paymentTimeliness = 0,
        decimal averageResponseTimeHours = 0,
        decimal serviceCompletionRate = 0,
        decimal customerSatisfactionScore = 0,
        int totalIssues = 0,
        int resolvedIssues = 0,
        decimal averageIssueResolutionTimeHours = 0,
        decimal performanceTrend = 0,
        DateTime? lastCollaboration = null)
    {
        ValidateRating(overallRating, nameof(overallRating));
        ValidatePercentage(onTimePerformance, nameof(onTimePerformance));
        ValidateRating(qualityRating, nameof(qualityRating));
        ValidateRating(communicationRating, nameof(communicationRating));
        ValidateRating(costEffectiveness, nameof(costEffectiveness));
        ValidateRating(reliabilityScore, nameof(reliabilityScore));
        ValidatePercentage(paymentTimeliness, nameof(paymentTimeliness));
        ValidatePercentage(serviceCompletionRate, nameof(serviceCompletionRate));
        ValidateRating(customerSatisfactionScore, nameof(customerSatisfactionScore));

        if (totalCollaborations < 0)
            throw new ArgumentException("Total collaborations cannot be negative", nameof(totalCollaborations));

        if (successfulCollaborations < 0)
            throw new ArgumentException("Successful collaborations cannot be negative", nameof(successfulCollaborations));

        if (failedCollaborations < 0)
            throw new ArgumentException("Failed collaborations cannot be negative", nameof(failedCollaborations));

        if (cancelledCollaborations < 0)
            throw new ArgumentException("Cancelled collaborations cannot be negative", nameof(cancelledCollaborations));

        if (totalRevenue < 0)
            throw new ArgumentException("Total revenue cannot be negative", nameof(totalRevenue));

        if (averageOrderValue < 0)
            throw new ArgumentException("Average order value cannot be negative", nameof(averageOrderValue));

        if (averageResponseTimeHours < 0)
            throw new ArgumentException("Average response time cannot be negative", nameof(averageResponseTimeHours));

        if (totalIssues < 0)
            throw new ArgumentException("Total issues cannot be negative", nameof(totalIssues));

        if (resolvedIssues < 0)
            throw new ArgumentException("Resolved issues cannot be negative", nameof(resolvedIssues));

        if (averageIssueResolutionTimeHours < 0)
            throw new ArgumentException("Average issue resolution time cannot be negative", nameof(averageIssueResolutionTimeHours));

        OverallRating = overallRating;
        OnTimePerformance = onTimePerformance;
        QualityRating = qualityRating;
        CommunicationRating = communicationRating;
        CostEffectiveness = costEffectiveness;
        ReliabilityScore = reliabilityScore;
        TotalCollaborations = totalCollaborations;
        SuccessfulCollaborations = successfulCollaborations;
        FailedCollaborations = failedCollaborations;
        CancelledCollaborations = cancelledCollaborations;
        TotalRevenue = totalRevenue;
        AverageOrderValue = averageOrderValue;
        PaymentTimeliness = paymentTimeliness;
        AverageResponseTimeHours = averageResponseTimeHours;
        ServiceCompletionRate = serviceCompletionRate;
        CustomerSatisfactionScore = customerSatisfactionScore;
        TotalIssues = totalIssues;
        ResolvedIssues = resolvedIssues;
        IssueResolutionRate = totalIssues > 0 ? (decimal)resolvedIssues / totalIssues * 100 : 0;
        AverageIssueResolutionTimeHours = averageIssueResolutionTimeHours;
        PerformanceTrend = performanceTrend;
        LastUpdated = DateTime.UtcNow;
        LastCollaboration = lastCollaboration ?? DateTime.MinValue;
    }

    // Calculated properties
    public decimal SuccessRate => TotalCollaborations > 0 ? (decimal)SuccessfulCollaborations / TotalCollaborations * 100 : 0;
    public decimal FailureRate => TotalCollaborations > 0 ? (decimal)FailedCollaborations / TotalCollaborations * 100 : 0;
    public decimal CancellationRate => TotalCollaborations > 0 ? (decimal)CancelledCollaborations / TotalCollaborations * 100 : 0;
    public bool IsActivePartner => LastCollaboration > DateTime.UtcNow.AddDays(-30); // Active if collaborated in last 30 days
    public bool IsHighPerformer => OverallRating >= 4.0m && OnTimePerformance >= 90m && SuccessRate >= 95m;
    public bool IsReliablePartner => ReliabilityScore >= 4.0m && IssueResolutionRate >= 90m;
    public bool IsImprovingPerformance => PerformanceTrend > 0;

    // Performance level classification
    public PerformanceLevel GetPerformanceLevel()
    {
        if (OverallRating >= 4.5m && OnTimePerformance >= 95m && SuccessRate >= 98m)
            return PerformanceLevel.Excellent;

        if (OverallRating >= 4.0m && OnTimePerformance >= 90m && SuccessRate >= 95m)
            return PerformanceLevel.Good;

        if (OverallRating >= 3.0m && OnTimePerformance >= 80m && SuccessRate >= 90m)
            return PerformanceLevel.Average;

        if (OverallRating >= 2.0m && OnTimePerformance >= 70m && SuccessRate >= 80m)
            return PerformanceLevel.BelowAverage;

        return PerformanceLevel.Poor;
    }

    // Risk assessment
    public RiskLevel GetRiskLevel()
    {
        var riskScore = 0;

        if (FailureRate > 10) riskScore += 3;
        else if (FailureRate > 5) riskScore += 2;
        else if (FailureRate > 2) riskScore += 1;

        if (CancellationRate > 15) riskScore += 3;
        else if (CancellationRate > 10) riskScore += 2;
        else if (CancellationRate > 5) riskScore += 1;

        if (PaymentTimeliness < 80) riskScore += 2;
        else if (PaymentTimeliness < 90) riskScore += 1;

        if (IssueResolutionRate < 70) riskScore += 2;
        else if (IssueResolutionRate < 85) riskScore += 1;

        if (PerformanceTrend < -0.5m) riskScore += 2;
        else if (PerformanceTrend < -0.2m) riskScore += 1;

        return riskScore switch
        {
            >= 8 => RiskLevel.High,
            >= 5 => RiskLevel.Medium,
            >= 2 => RiskLevel.Low,
            _ => RiskLevel.Minimal
        };
    }

    public PartnerPerformanceMetrics UpdateMetrics(
        decimal? newOverallRating = null,
        decimal? newOnTimePerformance = null,
        decimal? newQualityRating = null,
        decimal? newCommunicationRating = null,
        decimal? newCostEffectiveness = null,
        decimal? newReliabilityScore = null,
        int? additionalCollaborations = null,
        int? additionalSuccessful = null,
        int? additionalFailed = null,
        int? additionalCancelled = null,
        decimal? additionalRevenue = null,
        decimal? newAverageOrderValue = null,
        decimal? newPaymentTimeliness = null,
        decimal? newAverageResponseTime = null,
        decimal? newServiceCompletionRate = null,
        decimal? newCustomerSatisfactionScore = null,
        int? additionalIssues = null,
        int? additionalResolvedIssues = null,
        decimal? newAverageIssueResolutionTime = null,
        decimal? newPerformanceTrend = null,
        DateTime? newLastCollaboration = null)
    {
        return new PartnerPerformanceMetrics(
            newOverallRating ?? OverallRating,
            newOnTimePerformance ?? OnTimePerformance,
            newQualityRating ?? QualityRating,
            newCommunicationRating ?? CommunicationRating,
            newCostEffectiveness ?? CostEffectiveness,
            newReliabilityScore ?? ReliabilityScore,
            TotalCollaborations + (additionalCollaborations ?? 0),
            SuccessfulCollaborations + (additionalSuccessful ?? 0),
            FailedCollaborations + (additionalFailed ?? 0),
            CancelledCollaborations + (additionalCancelled ?? 0),
            TotalRevenue + (additionalRevenue ?? 0),
            newAverageOrderValue ?? AverageOrderValue,
            newPaymentTimeliness ?? PaymentTimeliness,
            newAverageResponseTime ?? AverageResponseTimeHours,
            newServiceCompletionRate ?? ServiceCompletionRate,
            newCustomerSatisfactionScore ?? CustomerSatisfactionScore,
            TotalIssues + (additionalIssues ?? 0),
            ResolvedIssues + (additionalResolvedIssues ?? 0),
            newAverageIssueResolutionTime ?? AverageIssueResolutionTimeHours,
            newPerformanceTrend ?? PerformanceTrend,
            newLastCollaboration ?? LastCollaboration
        );
    }

    private static void ValidateRating(decimal rating, string paramName)
    {
        if (rating < 0 || rating > 5)
            throw new ArgumentException($"{paramName} must be between 0 and 5", paramName);
    }

    private static void ValidatePercentage(decimal percentage, string paramName)
    {
        if (percentage < 0 || percentage > 100)
            throw new ArgumentException($"{paramName} must be between 0 and 100", paramName);
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return OverallRating;
        yield return OnTimePerformance;
        yield return QualityRating;
        yield return CommunicationRating;
        yield return CostEffectiveness;
        yield return ReliabilityScore;
        yield return TotalCollaborations;
        yield return SuccessfulCollaborations;
        yield return FailedCollaborations;
        yield return CancelledCollaborations;
        yield return TotalRevenue;
        yield return AverageOrderValue;
        yield return PaymentTimeliness;
        yield return AverageResponseTimeHours;
        yield return ServiceCompletionRate;
        yield return CustomerSatisfactionScore;
        yield return TotalIssues;
        yield return ResolvedIssues;
        yield return AverageIssueResolutionTimeHours;
        yield return PerformanceTrend;
    }
}

// Supporting enums
public enum PerformanceLevel
{
    Poor = 1,
    BelowAverage = 2,
    Average = 3,
    Good = 4,
    Excellent = 5
}

public enum RiskLevel
{
    Minimal = 1,
    Low = 2,
    Medium = 3,
    High = 4
}
