using Microsoft.EntityFrameworkCore;
using TripManagement.Domain.Entities;
using TripManagement.Infrastructure.Configurations;

namespace TripManagement.Infrastructure.Persistence;

public class TripManagementDbContext : DbContext
{
    public TripManagementDbContext(DbContextOptions<TripManagementDbContext> options) : base(options)
    {
    }

    public DbSet<Trip> Trips { get; set; } = null!;
    public DbSet<TripStop> TripStops { get; set; } = null!;
    public DbSet<TripLocationUpdate> TripLocationUpdates { get; set; } = null!;
    public DbSet<TripException> TripExceptions { get; set; } = null!;
    public DbSet<TripDocument> TripDocuments { get; set; } = null!;
    public DbSet<ProofOfDelivery> ProofOfDeliveries { get; set; } = null!;
    public DbSet<Driver> Drivers { get; set; } = null!;
    public DbSet<DriverDocument> DriverDocuments { get; set; } = null!;
    public DbSet<Vehicle> Vehicles { get; set; } = null!;
    public DbSet<VehicleDocument> VehicleDocuments { get; set; } = null!;
    public DbSet<CargoTypeMaster> CargoTypeMasters { get; set; } = null!;
    public DbSet<ServiceZoneMaster> ServiceZoneMasters { get; set; } = null!;
    public DbSet<CancellationReasonMaster> CancellationReasonMasters { get; set; } = null!;
    public DbSet<RejectReasonMaster> RejectReasonMasters { get; set; } = null!;
    public DbSet<RoutingFailure> RoutingFailures { get; set; } = null!;

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Apply configurations
        modelBuilder.ApplyConfiguration(new TripConfiguration());
        modelBuilder.ApplyConfiguration(new TripStopConfiguration());
        modelBuilder.ApplyConfiguration(new TripLocationUpdateConfiguration());
        modelBuilder.ApplyConfiguration(new TripExceptionConfiguration());
        modelBuilder.ApplyConfiguration(new TripDocumentConfiguration());
        modelBuilder.ApplyConfiguration(new ProofOfDeliveryConfiguration());
        modelBuilder.ApplyConfiguration(new DriverConfiguration());
        modelBuilder.ApplyConfiguration(new DriverDocumentConfiguration());
        modelBuilder.ApplyConfiguration(new VehicleConfiguration());
        modelBuilder.ApplyConfiguration(new VehicleDocumentConfiguration());
        modelBuilder.ApplyConfiguration(new CargoTypeMasterConfiguration());
        modelBuilder.ApplyConfiguration(new RoutingFailureConfiguration());

        // Configure schema
        modelBuilder.HasDefaultSchema("trip_management");

        // Configure enums
        modelBuilder.HasPostgresEnum<Domain.Enums.TripStatus>();
        modelBuilder.HasPostgresEnum<Domain.Enums.TripStopType>();
        modelBuilder.HasPostgresEnum<Domain.Enums.TripStopStatus>();
        modelBuilder.HasPostgresEnum<Domain.Enums.VehicleType>();
        modelBuilder.HasPostgresEnum<Domain.Enums.DriverStatus>();
        modelBuilder.HasPostgresEnum<Domain.Enums.VehicleStatus>();
        modelBuilder.HasPostgresEnum<Domain.Enums.DocumentType>();
        modelBuilder.HasPostgresEnum<Domain.Enums.ExceptionType>();
        modelBuilder.HasPostgresEnum<Domain.Enums.LocationUpdateSource>();
    }
}
