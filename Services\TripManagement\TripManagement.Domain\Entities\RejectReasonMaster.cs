using Shared.Domain.Common;
using TripManagement.Domain.Events;

namespace TripManagement.Domain.Entities;

/// <summary>
/// Master data entity for reject reasons
/// </summary>
public class RejectReasonMaster : AggregateRoot
{
    public string Code { get; private set; } = string.Empty;
    public string Name { get; private set; } = string.Empty;
    public string Description { get; private set; } = string.Empty;
    public string Category { get; private set; } = string.Empty; // Driver, Vehicle, Route, Load, Schedule, etc.
    public string ApplicableRole { get; private set; } = string.Empty; // Driver, Carrier, System
    public bool RequiresComment { get; private set; }
    public bool RequiresPhoto { get; private set; }
    public bool RequiresLocation { get; private set; }
    public bool IsTemporary { get; private set; } // Can be resolved later
    public bool IsPermanent { get; private set; } // Cannot be resolved
    public int CooldownMinutes { get; private set; } // Time before driver can be assigned again
    public bool AffectsDriverRating { get; private set; }
    public decimal RatingImpact { get; private set; } // Negative impact on driver rating
    public bool IsActive { get; private set; }
    public int SortOrder { get; private set; }
    public string? IconUrl { get; private set; }
    public Dictionary<string, object> AdditionalProperties { get; private set; } = new();

    // Private constructor for EF Core
    private RejectReasonMaster() { }

    public RejectReasonMaster(
        string code,
        string name,
        string description,
        string category,
        string applicableRole = "Driver",
        bool requiresComment = false,
        bool requiresPhoto = false,
        bool requiresLocation = false,
        bool isTemporary = true,
        bool isPermanent = false,
        int cooldownMinutes = 0,
        bool affectsDriverRating = false,
        decimal ratingImpact = 0m,
        int sortOrder = 0,
        string? iconUrl = null,
        Dictionary<string, object>? additionalProperties = null)
    {
        if (string.IsNullOrWhiteSpace(code))
            throw new ArgumentException("Reject reason code cannot be empty", nameof(code));

        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Reject reason name cannot be empty", nameof(name));

        if (string.IsNullOrWhiteSpace(category))
            throw new ArgumentException("Category cannot be empty", nameof(category));

        Code = code.Trim().ToUpperInvariant();
        Name = name.Trim();
        Description = description?.Trim() ?? string.Empty;
        Category = category.Trim();
        ApplicableRole = applicableRole.Trim();
        RequiresComment = requiresComment;
        RequiresPhoto = requiresPhoto;
        RequiresLocation = requiresLocation;
        IsTemporary = isTemporary;
        IsPermanent = isPermanent;
        CooldownMinutes = cooldownMinutes;
        AffectsDriverRating = affectsDriverRating;
        RatingImpact = ratingImpact;
        IsActive = true;
        SortOrder = sortOrder;
        IconUrl = iconUrl?.Trim();
        AdditionalProperties = additionalProperties ?? new Dictionary<string, object>();

        // Validate business rules
        ValidateBusinessRules();

        // Add domain event
        AddDomainEvent(new RejectReasonMasterCreatedEvent(Id, Code, Name, Category));
    }

    public void UpdateDetails(
        string name,
        string description,
        string category,
        string applicableRole = "Driver",
        bool requiresComment = false,
        bool requiresPhoto = false,
        bool requiresLocation = false,
        bool isTemporary = true,
        bool isPermanent = false,
        int cooldownMinutes = 0,
        bool affectsDriverRating = false,
        decimal ratingImpact = 0m,
        int sortOrder = 0,
        string? iconUrl = null,
        Dictionary<string, object>? additionalProperties = null)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Reject reason name cannot be empty", nameof(name));

        if (string.IsNullOrWhiteSpace(category))
            throw new ArgumentException("Category cannot be empty", nameof(category));

        var oldName = Name;
        var oldCategory = Category;

        Name = name.Trim();
        Description = description?.Trim() ?? string.Empty;
        Category = category.Trim();
        ApplicableRole = applicableRole.Trim();
        RequiresComment = requiresComment;
        RequiresPhoto = requiresPhoto;
        RequiresLocation = requiresLocation;
        IsTemporary = isTemporary;
        IsPermanent = isPermanent;
        CooldownMinutes = cooldownMinutes;
        AffectsDriverRating = affectsDriverRating;
        RatingImpact = ratingImpact;
        SortOrder = sortOrder;
        IconUrl = iconUrl?.Trim();

        if (additionalProperties != null)
        {
            AdditionalProperties = additionalProperties;
        }

        // Validate business rules
        ValidateBusinessRules();

        SetUpdatedAt();

        // Add domain event if significant changes
        if (oldName != Name || oldCategory != Category)
        {
            AddDomainEvent(new RejectReasonMasterUpdatedEvent(Id, Code, Name, Category, oldName, oldCategory));
        }
    }

    public void Activate()
    {
        if (IsActive) return;

        IsActive = true;
        SetUpdatedAt();

        AddDomainEvent(new RejectReasonMasterActivatedEvent(Id, Code, Name));
    }

    public void Deactivate()
    {
        if (!IsActive) return;

        IsActive = false;
        SetUpdatedAt();

        AddDomainEvent(new RejectReasonMasterDeactivatedEvent(Id, Code, Name));
    }

    public void UpdateSortOrder(int newSortOrder)
    {
        if (SortOrder == newSortOrder) return;

        SortOrder = newSortOrder;
        SetUpdatedAt();
    }

    public void AddOrUpdateProperty(string key, object value)
    {
        if (string.IsNullOrWhiteSpace(key))
            throw new ArgumentException("Property key cannot be empty", nameof(key));

        AdditionalProperties[key] = value;
        SetUpdatedAt();
    }

    public void RemoveProperty(string key)
    {
        if (string.IsNullOrWhiteSpace(key)) return;

        if (AdditionalProperties.Remove(key))
        {
            SetUpdatedAt();
        }
    }

    public DateTime CalculateNextAvailableTime(DateTime rejectionTime)
    {
        return rejectionTime.AddMinutes(CooldownMinutes);
    }

    public bool IsDriverEligibleForReassignment(DateTime rejectionTime, DateTime currentTime)
    {
        if (CooldownMinutes == 0) return true;

        var nextAvailableTime = CalculateNextAvailableTime(rejectionTime);
        return currentTime >= nextAvailableTime;
    }

    public bool RequiresAdditionalData()
    {
        return RequiresComment || RequiresPhoto || RequiresLocation;
    }

    public bool IsHighImpact()
    {
        return AffectsDriverRating && Math.Abs(RatingImpact) >= 0.5m;
    }

    private void ValidateBusinessRules()
    {
        // Cannot be both temporary and permanent
        if (IsTemporary && IsPermanent)
            throw new ArgumentException("Reject reason cannot be both temporary and permanent");

        // Cooldown minutes should be non-negative
        if (CooldownMinutes < 0)
            throw new ArgumentException("Cooldown minutes cannot be negative");

        // Rating impact should be reasonable
        if (Math.Abs(RatingImpact) > 5.0m)
            throw new ArgumentException("Rating impact should be between -5.0 and 5.0");
    }
}


