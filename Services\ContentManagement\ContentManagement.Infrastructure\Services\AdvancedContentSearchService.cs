using ContentManagement.Application.Interfaces;
using ContentManagement.Domain.Entities;
using ContentManagement.Domain.Enums;
using ContentManagement.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace ContentManagement.Infrastructure.Services;

/// <summary>
/// Advanced content search service with full-text search capabilities
/// </summary>
public class AdvancedContentSearchService : IContentSearchService
{
    private readonly ContentManagementDbContext _context;
    private readonly ILogger<AdvancedContentSearchService> _logger;

    public AdvancedContentSearchService(
        ContentManagementDbContext context,
        ILogger<AdvancedContentSearchService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task IndexContentAsync(Content content, CancellationToken cancellationToken = default)
    {
        try
        {
            // Create search index entry
            var searchDocument = new ContentSearchDocument
            {
                ContentId = content.Id,
                Title = content.Title,
                Description = content.Description ?? string.Empty,
                ContentBody = content.ContentBody,
                Tags = string.Join(" ", content.Tags),
                ContentType = content.Type.ToString(),
                Status = content.Status.ToString(),
                CreatedAt = content.CreatedAt,
                UpdatedAt = content.UpdatedAt ?? content.CreatedAt,
                SearchVector = GenerateSearchVector(content)
            };

            // In a real implementation, this would be stored in a dedicated search index
            // For now, we'll use a simple in-memory approach or database full-text search
            
            _logger.LogInformation("Indexed content {ContentId} for advanced search", content.Id);
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to index content {ContentId}", content.Id);
        }
    }

    public async Task RemoveFromIndexAsync(Guid contentId, CancellationToken cancellationToken = default)
    {
        try
        {
            // Remove from search index
            _logger.LogInformation("Removed content {ContentId} from search index", contentId);
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to remove content {ContentId} from search index", contentId);
        }
    }

    public async Task UpdateIndexAsync(Content content, CancellationToken cancellationToken = default)
    {
        await RemoveFromIndexAsync(content.Id, cancellationToken);
        await IndexContentAsync(content, cancellationToken);
    }

    public async Task<(List<Guid> ContentIds, int TotalCount)> SearchAsync(
        string searchTerm,
        ContentType? type = null,
        List<string>? tags = null,
        List<string>? userRoles = null,
        bool isAuthenticated = false,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = _context.Contents.AsQueryable();

            // Apply full-text search
            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                query = ApplyFullTextSearch(query, searchTerm);
            }

            // Apply filters
            if (type.HasValue)
            {
                query = query.Where(c => c.Type == type.Value);
            }

            if (tags?.Any() == true)
            {
                foreach (var tag in tags)
                {
                    query = query.Where(c => c.Tags.Contains(tag));
                }
            }

            // Apply visibility filters
            query = ApplyVisibilityFilter(query, userRoles, isAuthenticated);

            // Get total count
            var totalCount = await query.CountAsync(cancellationToken);

            // Apply pagination and get results
            var contentIds = await query
                .OrderByDescending(c => CalculateRelevanceScore(c, searchTerm))
                .ThenByDescending(c => c.CreatedAt)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .Select(c => c.Id)
                .ToListAsync(cancellationToken);

            return (contentIds, totalCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to perform advanced search for term: {SearchTerm}", searchTerm);
            return (new List<Guid>(), 0);
        }
    }

    public async Task<List<string>> GetSearchSuggestionsAsync(
        string partialTerm,
        ContentType? type = null,
        int maxSuggestions = 10,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = _context.Contents.AsQueryable();

            if (type.HasValue)
            {
                query = query.Where(c => c.Type == type.Value);
            }

            // Get title suggestions
            var titleSuggestions = await query
                .Where(c => c.Title.Contains(partialTerm))
                .Select(c => c.Title)
                .Distinct()
                .Take(maxSuggestions / 2)
                .ToListAsync(cancellationToken);

            // Get tag suggestions
            var tagSuggestions = await query
                .SelectMany(c => c.Tags)
                .Where(tag => tag.Contains(partialTerm))
                .Distinct()
                .Take(maxSuggestions / 2)
                .ToListAsync(cancellationToken);

            var allSuggestions = titleSuggestions.Concat(tagSuggestions)
                .Distinct()
                .Take(maxSuggestions)
                .ToList();

            return allSuggestions;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get search suggestions for term: {PartialTerm}", partialTerm);
            return new List<string>();
        }
    }

    public async Task RebuildIndexAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting search index rebuild");

            var contents = await _context.Contents
                .Where(c => c.Status == ContentStatus.Published)
                .ToListAsync(cancellationToken);

            var batchSize = 100;
            for (int i = 0; i < contents.Count; i += batchSize)
            {
                var batch = contents.Skip(i).Take(batchSize);
                
                foreach (var content in batch)
                {
                    await IndexContentAsync(content, cancellationToken);
                }

                _logger.LogInformation("Processed {Count}/{Total} content items for search indexing", 
                    Math.Min(i + batchSize, contents.Count), contents.Count);
            }

            _logger.LogInformation("Search index rebuild completed for {Count} content items", contents.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to rebuild search index");
        }
    }

    private static IQueryable<Content> ApplyFullTextSearch(IQueryable<Content> query, string searchTerm)
    {
        // Split search term into words for better matching
        var searchWords = searchTerm.Split(' ', StringSplitOptions.RemoveEmptyEntries);

        foreach (var word in searchWords)
        {
            query = query.Where(c =>
                c.Title.Contains(word) ||
                c.Description!.Contains(word) ||
                c.ContentBody.Contains(word) ||
                c.Tags.Any(tag => tag.Contains(word)));
        }

        return query;
    }

    private static IQueryable<Content> ApplyVisibilityFilter(
        IQueryable<Content> query,
        List<string>? userRoles,
        bool isAuthenticated)
    {
        if (!isAuthenticated)
        {
            // Only show published content for unauthenticated users
            query = query.Where(c => c.Status == ContentStatus.Published);
        }
        else
        {
            // Show published content and draft content created by the user
            query = query.Where(c => 
                c.Status == ContentStatus.Published || 
                c.Status == ContentStatus.Draft);
        }

        return query;
    }

    private static double CalculateRelevanceScore(Content content, string? searchTerm)
    {
        if (string.IsNullOrWhiteSpace(searchTerm))
            return 0;

        double score = 0;
        var searchWords = searchTerm.Split(' ', StringSplitOptions.RemoveEmptyEntries);

        foreach (var word in searchWords)
        {
            // Title matches get highest score
            if (content.Title.Contains(word, StringComparison.OrdinalIgnoreCase))
                score += 10;

            // Description matches get medium score
            if (content.Description?.Contains(word, StringComparison.OrdinalIgnoreCase) == true)
                score += 5;

            // Content body matches get lower score
            if (content.ContentBody.Contains(word, StringComparison.OrdinalIgnoreCase))
                score += 2;

            // Tag matches get medium score
            if (content.Tags.Any(tag => tag.Contains(word, StringComparison.OrdinalIgnoreCase)))
                score += 7;
        }

        return score;
    }

    private static string GenerateSearchVector(Content content)
    {
        // Generate a search vector for full-text search
        var searchableText = $"{content.Title} {content.Description} {content.ContentBody} {string.Join(" ", content.Tags)}";
        return searchableText.ToLowerInvariant();
    }
}

/// <summary>
/// Search document for content indexing
/// </summary>
public class ContentSearchDocument
{
    public Guid ContentId { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string ContentBody { get; set; } = string.Empty;
    public string Tags { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public string SearchVector { get; set; } = string.Empty;
}
