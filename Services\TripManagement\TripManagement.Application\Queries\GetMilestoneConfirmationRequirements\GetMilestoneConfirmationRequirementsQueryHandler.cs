using MediatR;
using Microsoft.Extensions.Logging;
using TripManagement.Domain.Repositories;
using TripManagement.Domain.Enums;

namespace TripManagement.Application.Queries.GetMilestoneConfirmationRequirements;

public class GetMilestoneConfirmationRequirementsQueryHandler : IRequestHandler<GetMilestoneConfirmationRequirementsQuery, GetMilestoneConfirmationRequirementsResponse>
{
    private readonly ITripRepository _tripRepository;
    private readonly IMilestoneConfirmationConfigurationRepository _configurationRepository;
    private readonly ILogger<GetMilestoneConfirmationRequirementsQueryHandler> _logger;

    public GetMilestoneConfirmationRequirementsQueryHandler(
        ITripRepository tripRepository,
        IMilestoneConfirmationConfigurationRepository configurationRepository,
        ILogger<GetMilestoneConfirmationRequirementsQueryHandler> logger)
    {
        _tripRepository = tripRepository;
        _configurationRepository = configurationRepository;
        _logger = logger;
    }

    public async Task<GetMilestoneConfirmationRequirementsResponse> Handle(GetMilestoneConfirmationRequirementsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Getting milestone confirmation requirements for trip {TripId}", request.TripId);

            // Get trip
            var trip = await _tripRepository.GetByIdAsync(request.TripId, cancellationToken);
            if (trip == null)
            {
                return new GetMilestoneConfirmationRequirementsResponse
                {
                    IsSuccess = false,
                    ErrorMessage = "Trip not found"
                };
            }

            // Get configuration
            var configuration = await _configurationRepository.GetByTripAndMilestoneAsync(request.TripId, request.MilestoneId, cancellationToken);
            
            // Build requirements DTO
            var requirements = new MilestoneConfirmationRequirementsDto
            {
                TripId = request.TripId,
                MilestoneId = request.MilestoneId,
                TripNumber = trip.TripNumber,
                MilestoneName = await GetMilestoneName(request.MilestoneId, cancellationToken),
                
                // Default settings if no configuration exists
                RequireConfirmationPopup = configuration?.Settings.RequireConfirmationPopup ?? true,
                RequireDoubleConfirmation = configuration?.Settings.RequireDoubleConfirmation ?? false,
                RequireReasonForCompletion = configuration?.Settings.RequireReasonForCompletion ?? false,
                RequirePhotoEvidence = configuration?.Settings.RequirePhotoEvidence ?? false,
                RequireLocationVerification = configuration?.Settings.RequireLocationVerification ?? true,
                RequireSignature = configuration?.Settings.RequireSignature ?? false,
                RequireManagerApproval = configuration?.Settings.RequireManagerApproval ?? false,
                AllowSkipWithReason = configuration?.Settings.AllowSkipWithReason ?? false,
                
                EnableSequenceValidation = configuration?.Settings.EnableSequenceValidation ?? true,
                AllowOutOfSequenceCompletion = configuration?.Settings.AllowOutOfSequenceCompletion ?? false,
                
                ConfirmationTimeoutSeconds = configuration?.Settings.ConfirmationTimeoutSeconds ?? 30,
                ConfirmationMessage = configuration?.Settings.ConfirmationMessage ?? "Are you sure you want to complete this milestone?",
                WarningMessage = configuration?.Settings.WarningMessage ?? "Please ensure all requirements are met before confirming.",
                RequiredFields = configuration?.Settings.RequiredFields ?? new List<string>(),
                CustomValidationRules = configuration?.Settings.CustomValidationRules ?? new Dictionary<string, object>(),
                
                LocalizedMessages = GetLocalizedMessages(request.LanguageCode, configuration),
                SequenceInfo = await BuildSequenceInfo(request.TripId, request.MilestoneId, cancellationToken)
            };

            // Validate current state
            await ValidateCurrentState(requirements, request, cancellationToken);

            return new GetMilestoneConfirmationRequirementsResponse
            {
                IsSuccess = true,
                Requirements = requirements
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting milestone confirmation requirements for trip {TripId}", request.TripId);
            return new GetMilestoneConfirmationRequirementsResponse
            {
                IsSuccess = false,
                ErrorMessage = ex.Message
            };
        }
    }

    private async Task<string?> GetMilestoneName(Guid? milestoneId, CancellationToken cancellationToken)
    {
        if (!milestoneId.HasValue) return null;
        
        // In real implementation, this would query milestone repository
        return "Sample Milestone";
    }

    private Dictionary<string, string> GetLocalizedMessages(string? languageCode, Domain.Entities.MilestoneConfirmationConfigurationEntity? configuration)
    {
        var messages = new Dictionary<string, string>();
        
        // Get from configuration first
        if (configuration?.Settings.LocalizedMessages != null)
        {
            foreach (var kvp in configuration.Settings.LocalizedMessages)
            {
                messages[kvp.Key] = kvp.Value;
            }
        }

        // Add default messages based on language
        var defaultMessages = GetDefaultLocalizedMessages(languageCode ?? "en");
        foreach (var kvp in defaultMessages)
        {
            if (!messages.ContainsKey(kvp.Key))
            {
                messages[kvp.Key] = kvp.Value;
            }
        }

        return messages;
    }

    private Dictionary<string, string> GetDefaultLocalizedMessages(string languageCode)
    {
        return languageCode.ToLower() switch
        {
            "es" => new Dictionary<string, string>
            {
                { "confirm_completion", "Confirmar finalización" },
                { "milestone_complete", "Hito completado" },
                { "reason_required", "Se requiere motivo" },
                { "photo_required", "Se requiere foto" },
                { "signature_required", "Se requiere firma" },
                { "location_verification", "Verificación de ubicación" },
                { "sequence_warning", "Advertencia de secuencia" },
                { "manager_approval", "Aprobación del gerente requerida" }
            },
            "hi" => new Dictionary<string, string>
            {
                { "confirm_completion", "पूर्णता की पुष्टि करें" },
                { "milestone_complete", "मील का पत्थर पूरा" },
                { "reason_required", "कारण आवश्यक है" },
                { "photo_required", "फोटो आवश्यक है" },
                { "signature_required", "हस्ताक्षर आवश्यक है" },
                { "location_verification", "स्थान सत्यापन" },
                { "sequence_warning", "अनुक्रम चेतावनी" },
                { "manager_approval", "प्रबंधक अनुमोदन आवश्यक" }
            },
            _ => new Dictionary<string, string>
            {
                { "confirm_completion", "Confirm Completion" },
                { "milestone_complete", "Milestone Complete" },
                { "reason_required", "Reason Required" },
                { "photo_required", "Photo Required" },
                { "signature_required", "Signature Required" },
                { "location_verification", "Location Verification" },
                { "sequence_warning", "Sequence Warning" },
                { "manager_approval", "Manager Approval Required" }
            }
        };
    }

    private async Task<SequenceValidationInfo> BuildSequenceInfo(Guid tripId, Guid? milestoneId, CancellationToken cancellationToken)
    {
        // Mock implementation - in real scenario, this would query milestone data
        return new SequenceValidationInfo
        {
            CurrentSequenceNumber = 1,
            TotalMilestones = 3,
            IsFirstMilestone = true,
            IsLastMilestone = false,
            CompletionPercentage = 0.0,
            HasSequenceViolations = false,
            PendingPreviousMilestones = new List<PendingMilestoneDto>(),
            CompletedMilestones = new List<CompletedMilestoneDto>()
        };
    }

    private async Task ValidateCurrentState(MilestoneConfirmationRequirementsDto requirements, GetMilestoneConfirmationRequirementsQuery request, CancellationToken cancellationToken)
    {
        var blockingIssues = new List<string>();
        var warnings = new List<string>();

        // Check trip status
        var trip = await _tripRepository.GetByIdAsync(request.TripId, cancellationToken);
        if (trip?.Status != TripStatus.InProgress)
        {
            blockingIssues.Add($"Trip status is {trip?.Status}, expected InProgress");
        }

        // Check driver assignment
        if (trip?.DriverId != request.DriverId)
        {
            blockingIssues.Add("Driver is not assigned to this trip");
        }

        // Check sequence validation
        if (requirements.EnableSequenceValidation && requirements.SequenceInfo.HasSequenceViolations)
        {
            if (!requirements.AllowOutOfSequenceCompletion)
            {
                blockingIssues.Add("Previous milestones must be completed first");
            }
            else
            {
                warnings.Add("Completing out of sequence may affect workflow");
            }
        }

        requirements.BlockingIssues = blockingIssues;
        requirements.Warnings = warnings;
        requirements.CanProceed = blockingIssues.Count == 0;
    }
}
