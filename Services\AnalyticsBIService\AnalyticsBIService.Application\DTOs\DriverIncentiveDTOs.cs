using AnalyticsBIService.Domain.Enums;
using AnalyticsBIService.Application.DTOs;

namespace AnalyticsBIService.Application.DTOs;

/// <summary>
/// Driver incentive calculation data transfer object
/// </summary>
public class DriverIncentiveCalculationDto
{
    public Guid DriverId { get; set; }
    public string DriverName { get; set; } = string.Empty;
    public DateTime CalculationPeriodStart { get; set; }
    public DateTime CalculationPeriodEnd { get; set; }
    public decimal BaseIncentive { get; set; }
    public decimal PerformanceBonus { get; set; }
    public decimal SafetyBonus { get; set; }
    public decimal FuelEfficiencyBonus { get; set; }
    public decimal OnTimeDeliveryBonus { get; set; }
    public decimal CustomerRatingBonus { get; set; }
    public decimal TotalIncentive { get; set; }
    public List<IncentiveComponentDto> Components { get; set; } = new();
    public string CalculationNotes { get; set; } = string.Empty;
    public DateTime CalculatedAt { get; set; }
    public string CalculatedBy { get; set; } = string.Empty;

    // Properties required by DriverPerformanceAnalyticsService
    public DateRangeDto DateRange { get; set; } = new();
    public decimal BaseEarnings { get; set; }
    public List<IncentiveComponentDto> IncentiveBreakdown { get; set; } = new();
    public decimal TotalIncentives { get; set; }
    public decimal TotalEarnings { get; set; }
    public decimal IncentivePercentage { get; set; }
}

/// <summary>
/// Driver performance report data transfer object
/// </summary>
public class DriverPerformanceReportDto
{
    public Guid DriverId { get; set; }
    public string DriverName { get; set; } = string.Empty;
    public string LicenseNumber { get; set; } = string.Empty;
    public DateTime ReportPeriodStart { get; set; }
    public DateTime ReportPeriodEnd { get; set; }
    public int TotalTrips { get; set; }
    public decimal TotalDistance { get; set; }
    public decimal TotalEarnings { get; set; }
    public decimal AverageRating { get; set; }
    public int OnTimeDeliveries { get; set; }
    public int LateDeliveries { get; set; }
    public decimal OnTimePercentage { get; set; }
    public int SafetyIncidents { get; set; }
    public decimal FuelEfficiencyScore { get; set; }

    // Properties required by DriverPerformanceAnalyticsService
    public DateTime GeneratedAt { get; set; }
    public DateRangeDto DateRange { get; set; } = new();
    public string ExecutiveSummary { get; set; } = string.Empty;
    public DriverPerformanceDashboardDto Dashboard { get; set; } = new();
    public DriverBenchmarkingDto Benchmarking { get; set; } = new();
    public DriverPerformanceInsightDto Insights { get; set; } = new();
    public List<RecommendationDto> Recommendations { get; set; } = new();
    public ActionPlanDto ActionPlan { get; set; } = new();
    public List<DriverTripDataDto> TripData { get; set; } = new();
    public List<DriverComplianceDataDto> ComplianceData { get; set; } = new();
    public List<DriverEarningsDataDto> EarningsData { get; set; } = new();
    public string ReportNotes { get; set; } = string.Empty;
}

/// <summary>
/// Driver feedback data transfer object
/// </summary>
public class DriverFeedbackDataDto
{
    public Guid FeedbackId { get; set; }
    public Guid DriverId { get; set; }
    public Guid TripId { get; set; }
    public Guid CustomerId { get; set; }
    public string CustomerName { get; set; } = string.Empty;
    public int Rating { get; set; }
    public string Comments { get; set; } = string.Empty;
    public List<string> Categories { get; set; } = new();
    public DateTime FeedbackDate { get; set; }
    public bool IsVerified { get; set; }

    // Properties required by DriverPerformanceAnalyticsService
    public decimal AverageRating { get; set; }
    public int TotalFeedbacks { get; set; }
    public int PositiveFeedbacks { get; set; }
    public int NegativeFeedbacks { get; set; }
    public decimal ServiceQualityRating { get; set; }
    public decimal CommunicationRating { get; set; }
    public decimal ProfessionalismRating { get; set; }
}

/// <summary>
/// Driver compliance data transfer object
/// </summary>
public class DriverComplianceDataDto
{
    public Guid DriverId { get; set; }
    public string ComplianceType { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime LastCheckDate { get; set; }
    public DateTime ExpiryDate { get; set; }
    public bool IsCompliant { get; set; }
    public string Notes { get; set; } = string.Empty;
    public List<string> RequiredDocuments { get; set; } = new();
    public List<string> SubmittedDocuments { get; set; } = new();

    // Additional properties required by DriverPerformanceAnalyticsService
    public string LicenseStatus { get; set; } = string.Empty;
    public decimal DocumentComplianceScore { get; set; }
    public int SafetyViolations { get; set; }
    public decimal TrainingCompletionRate { get; set; }
    public int ComplianceAlerts { get; set; }
}

/// <summary>
/// Driver earnings data transfer object
/// </summary>
public class DriverEarningsDataDto
{
    public Guid DriverId { get; set; }
    public DateTime EarningsDate { get; set; }
    public decimal BaseEarnings { get; set; }
    public decimal Bonuses { get; set; }
    public decimal Deductions { get; set; }
    public decimal NetEarnings { get; set; }
    public string EarningsType { get; set; } = string.Empty;
    public Guid? TripId { get; set; }
    public string Description { get; set; } = string.Empty;

    // Properties required by DriverPerformanceAnalyticsService
    public decimal TotalEarnings { get; set; }
    public decimal IncentiveEarnings { get; set; }
    public decimal BonusEarnings { get; set; }
    public decimal AverageEarningsPerTrip { get; set; }
    public decimal AverageEarningsPerKm { get; set; }
}

// DriverTripDataDto is already defined in another DTO file

/// <summary>
/// Incentive component data transfer object
/// </summary>
public class IncentiveComponentDto
{
    public string ComponentName { get; set; } = string.Empty;
    public string ComponentType { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public string Calculation { get; set; } = string.Empty;
    public Dictionary<string, object> Metrics { get; set; } = new();
    public string Description { get; set; } = string.Empty;

    // Properties required by DriverPerformanceAnalyticsService
    public string Type { get; set; } = string.Empty;
    public string Criteria { get; set; } = string.Empty;
    public decimal AchievedValue { get; set; }
}

/// <summary>
/// Action plan data transfer object
/// </summary>
public class ActionPlanDto
{
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<ActionItemDto> ActionItems { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime TargetCompletionDate { get; set; }
    public string Priority { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
}

/// <summary>
/// Action item data transfer object
/// </summary>
public class ActionItemDto
{
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime DueDate { get; set; }
    public string Priority { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string AssignedTo { get; set; } = string.Empty;
    public List<string> Dependencies { get; set; } = new();
}
