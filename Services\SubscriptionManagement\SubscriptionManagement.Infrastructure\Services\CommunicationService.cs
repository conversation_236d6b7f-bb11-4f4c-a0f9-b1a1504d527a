using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Enums;
using System.Diagnostics;
using System.Text.Json;

namespace SubscriptionManagement.Infrastructure.Services
{
    public class CommunicationService : ICommunicationService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<CommunicationService> _logger;
        private readonly string _communicationServiceBaseUrl;
        private readonly string _apiKey;

        public CommunicationService(
            HttpClient httpClient,
            IConfiguration configuration,
            ILogger<CommunicationService> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
            _communicationServiceBaseUrl = configuration.GetValue<string>("CommunicationService:BaseUrl") ?? "https://localhost:7002";
            _apiKey = configuration.GetValue<string>("CommunicationService:ApiKey") ?? "dev-api-key";

            // Configure HttpClient
            _httpClient.BaseAddress = new Uri(_communicationServiceBaseUrl);
            _httpClient.DefaultRequestHeaders.Add("X-API-Key", _apiKey);
            _httpClient.DefaultRequestHeaders.Add("User-Agent", "SubscriptionManagement/1.0");
        }

        public async Task<NotificationResult> SendNotificationAsync(
            NotificationRequest request,
            CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Sending notification via Communication Service. Channel: {Channel}, Type: {Type}, UserId: {UserId}",
                request.Channel, request.Type, request.UserId);

            try
            {
                var payload = new
                {
                    userId = request.UserId,
                    email = request.Email,
                    phoneNumber = request.PhoneNumber,
                    channel = request.Channel,
                    subject = request.Subject,
                    body = request.Body,
                    type = request.Type.ToString(),
                    priority = request.Priority,
                    metadata = request.Metadata,
                    templateId = request.TemplateId,
                    templateVariables = request.TemplateVariables
                };

                var json = JsonSerializer.Serialize(payload);
                var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync("/api/notifications/send", content, cancellationToken);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
                    var result = JsonSerializer.Deserialize<NotificationResult>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    if (result != null)
                    {
                        _logger.LogInformation("Notification sent successfully. NotificationId: {NotificationId}, Channel: {Channel}",
                            result.NotificationId, request.Channel);
                        return result;
                    }
                }

                _logger.LogWarning("Failed to send notification. Status: {StatusCode}, Response: {Response}",
                    response.StatusCode, await response.Content.ReadAsStringAsync(cancellationToken));

                return new NotificationResult
                {
                    Success = false,
                    Channel = request.Channel,
                    ErrorCode = response.StatusCode.ToString(),
                    ErrorMessage = $"Communication service returned {response.StatusCode}",
                    SentAt = DateTime.UtcNow
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send notification via Communication Service");
                return new NotificationResult
                {
                    Success = false,
                    Channel = request.Channel,
                    ErrorMessage = ex.Message,
                    SentAt = DateTime.UtcNow
                };
            }
        }

        public async Task<BulkNotificationResult> SendBulkNotificationsAsync(
            List<NotificationRequest> requests,
            CancellationToken cancellationToken = default)
        {
            var stopwatch = Stopwatch.StartNew();
            var processedAt = DateTime.UtcNow;

            _logger.LogInformation("Sending bulk notifications via Communication Service. Count: {Count}", requests.Count);

            var result = new BulkNotificationResult
            {
                BatchId = Guid.NewGuid().ToString(),
                TotalRequests = requests.Count,
                ProcessedAt = processedAt
            };

            try
            {
                var payload = new
                {
                    batchId = result.BatchId,
                    notifications = requests.Select(r => new
                    {
                        userId = r.UserId,
                        email = r.Email,
                        phoneNumber = r.PhoneNumber,
                        channel = r.Channel,
                        subject = r.Subject,
                        body = r.Body,
                        type = r.Type.ToString(),
                        priority = r.Priority,
                        metadata = r.Metadata,
                        templateId = r.TemplateId,
                        templateVariables = r.TemplateVariables
                    }).ToList()
                };

                var json = JsonSerializer.Serialize(payload);
                var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync("/api/notifications/bulk-send", content, cancellationToken);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
                    var bulkResult = JsonSerializer.Deserialize<BulkNotificationResult>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    if (bulkResult != null)
                    {
                        stopwatch.Stop();
                        bulkResult.ProcessingTime = stopwatch.Elapsed;

                        _logger.LogInformation("Bulk notifications sent. BatchId: {BatchId}, Successful: {Successful}, Failed: {Failed}",
                            bulkResult.BatchId, bulkResult.SuccessfulSends, bulkResult.FailedSends);

                        return bulkResult;
                    }
                }

                _logger.LogWarning("Failed to send bulk notifications. Status: {StatusCode}", response.StatusCode);

                result.Success = false;
                result.FailedSends = requests.Count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send bulk notifications via Communication Service");
                result.Success = false;
                result.FailedSends = requests.Count;
            }

            stopwatch.Stop();
            result.ProcessingTime = stopwatch.Elapsed;
            return result;
        }

        public async Task<NotificationResult> ScheduleNotificationAsync(
            NotificationRequest request,
            DateTime scheduledAt,
            CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Scheduling notification via Communication Service. Channel: {Channel}, ScheduledAt: {ScheduledAt}",
                request.Channel, scheduledAt);

            try
            {
                var payload = new
                {
                    userId = request.UserId,
                    email = request.Email,
                    phoneNumber = request.PhoneNumber,
                    channel = request.Channel,
                    subject = request.Subject,
                    body = request.Body,
                    type = request.Type.ToString(),
                    priority = request.Priority,
                    metadata = request.Metadata,
                    templateId = request.TemplateId,
                    templateVariables = request.TemplateVariables,
                    scheduledAt = scheduledAt
                };

                var json = JsonSerializer.Serialize(payload);
                var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync("/api/notifications/schedule", content, cancellationToken);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
                    var result = JsonSerializer.Deserialize<NotificationResult>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    if (result != null)
                    {
                        _logger.LogInformation("Notification scheduled successfully. NotificationId: {NotificationId}", result.NotificationId);
                        return result;
                    }
                }

                return new NotificationResult
                {
                    Success = false,
                    Channel = request.Channel,
                    ErrorCode = response.StatusCode.ToString(),
                    ErrorMessage = $"Failed to schedule notification: {response.StatusCode}",
                    SentAt = DateTime.UtcNow
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to schedule notification via Communication Service");
                return new NotificationResult
                {
                    Success = false,
                    Channel = request.Channel,
                    ErrorMessage = ex.Message,
                    SentAt = DateTime.UtcNow
                };
            }
        }

        public async Task<NotificationStatus> GetNotificationStatusAsync(
            string notificationId,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var response = await _httpClient.GetAsync($"/api/notifications/{notificationId}/status", cancellationToken);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
                    var status = JsonSerializer.Deserialize<NotificationStatus>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    return status ?? new NotificationStatus
                    {
                        NotificationId = notificationId,
                        Status = "Unknown"
                    };
                }

                return new NotificationStatus
                {
                    NotificationId = notificationId,
                    Status = "Error",
                    ErrorMessage = $"Failed to get status: {response.StatusCode}"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get notification status for {NotificationId}", notificationId);
                return new NotificationStatus
                {
                    NotificationId = notificationId,
                    Status = "Error",
                    ErrorMessage = ex.Message
                };
            }
        }

        public async Task<bool> CancelScheduledNotificationAsync(
            string notificationId,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var response = await _httpClient.DeleteAsync($"/api/notifications/{notificationId}/cancel", cancellationToken);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Scheduled notification cancelled successfully. NotificationId: {NotificationId}", notificationId);
                    return true;
                }

                _logger.LogWarning("Failed to cancel scheduled notification. NotificationId: {NotificationId}, Status: {StatusCode}",
                    notificationId, response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to cancel scheduled notification {NotificationId}", notificationId);
                return false;
            }
        }
    }
}
