using AutoMapper;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using MobileWorkflow.Application.DTOs;
using MobileWorkflow.Application.Queries.Milestone;
using MobileWorkflow.Domain.Entities;
using MobileWorkflow.Domain.Repositories;
using Moq;
using Xunit;

namespace MobileWorkflow.Tests.Application.Queries;

public class GetMilestoneTemplateByIdQueryHandlerTests
{
    private readonly Mock<IMilestoneTemplateRepository> _mockRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly Mock<ILogger<GetMilestoneTemplateByIdQueryHandler>> _mockLogger;
    private readonly GetMilestoneTemplateByIdQueryHandler _handler;

    public GetMilestoneTemplateByIdQueryHandlerTests()
    {
        _mockRepository = new Mock<IMilestoneTemplateRepository>();
        _mockMapper = new Mock<IMapper>();
        _mockLogger = new Mock<ILogger<GetMilestoneTemplateByIdQueryHandler>>();
        _handler = new GetMilestoneTemplateByIdQueryHandler(_mockRepository.Object, _mockMapper.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task Handle_WithValidId_ShouldReturnMappedTemplate()
    {
        // Arrange
        var templateId = Guid.NewGuid();
        var query = new GetMilestoneTemplateByIdQuery { Id = templateId };

        var template = new MilestoneTemplate("Test Template", "Description", "Trip", "Logistics", "admin");
        var expectedDto = new MilestoneTemplateDto
        {
            Id = template.Id,
            Name = template.Name,
            Description = template.Description,
            Type = template.Type,
            Category = template.Category
        };

        _mockRepository.Setup(r => r.GetByIdAsync(templateId, It.IsAny<CancellationToken>()))
                      .ReturnsAsync(template);

        _mockMapper.Setup(m => m.Map<MilestoneTemplateDto>(template))
                   .Returns(expectedDto);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEquivalentTo(expectedDto);

        _mockRepository.Verify(r => r.GetByIdAsync(templateId, It.IsAny<CancellationToken>()), Times.Once);
        _mockMapper.Verify(m => m.Map<MilestoneTemplateDto>(template), Times.Once);
    }

    [Fact]
    public async Task Handle_WithNonExistentId_ShouldReturnNull()
    {
        // Arrange
        var templateId = Guid.NewGuid();
        var query = new GetMilestoneTemplateByIdQuery { Id = templateId };

        _mockRepository.Setup(r => r.GetByIdAsync(templateId, It.IsAny<CancellationToken>()))
                      .ReturnsAsync((MilestoneTemplate?)null);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().BeNull();

        _mockRepository.Verify(r => r.GetByIdAsync(templateId, It.IsAny<CancellationToken>()), Times.Once);
        _mockMapper.Verify(m => m.Map<MilestoneTemplateDto>(It.IsAny<MilestoneTemplate>()), Times.Never);
    }

    [Fact]
    public async Task Handle_WhenRepositoryThrows_ShouldPropagateException()
    {
        // Arrange
        var templateId = Guid.NewGuid();
        var query = new GetMilestoneTemplateByIdQuery { Id = templateId };

        _mockRepository.Setup(r => r.GetByIdAsync(templateId, It.IsAny<CancellationToken>()))
                      .ThrowsAsync(new Exception("Database error"));

        // Act & Assert
        var act = async () => await _handler.Handle(query, CancellationToken.None);
        await act.Should().ThrowAsync<Exception>()
                 .WithMessage("Database error");

        _mockRepository.Verify(r => r.GetByIdAsync(templateId, It.IsAny<CancellationToken>()), Times.Once);
        _mockMapper.Verify(m => m.Map<MilestoneTemplateDto>(It.IsAny<MilestoneTemplate>()), Times.Never);
    }

    [Fact]
    public async Task Handle_WithTemplateContainingSteps_ShouldReturnCompleteTemplate()
    {
        // Arrange
        var templateId = Guid.NewGuid();
        var query = new GetMilestoneTemplateByIdQuery { Id = templateId };

        var template = new MilestoneTemplate("Test Template", "Description", "Trip", "Logistics", "admin");
        var step = template.AddStep("Test Step", "Step description", 1, true);
        step.AddPayoutRule(100.0m, "status=completed", "Full payment");

        var expectedDto = new MilestoneTemplateDto
        {
            Id = template.Id,
            Name = template.Name,
            Description = template.Description,
            Type = template.Type,
            Category = template.Category,
            TotalPayoutPercentage = 100.0m,
            IsValid = true
        };

        _mockRepository.Setup(r => r.GetByIdAsync(templateId, It.IsAny<CancellationToken>()))
                      .ReturnsAsync(template);

        _mockMapper.Setup(m => m.Map<MilestoneTemplateDto>(template))
                   .Returns(expectedDto);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.TotalPayoutPercentage.Should().Be(100.0m);
        result.IsValid.Should().BeTrue();

        _mockRepository.Verify(r => r.GetByIdAsync(templateId, It.IsAny<CancellationToken>()), Times.Once);
        _mockMapper.Verify(m => m.Map<MilestoneTemplateDto>(template), Times.Once);
    }

    [Fact]
    public async Task Handle_WithCancellationToken_ShouldPassTokenToRepository()
    {
        // Arrange
        var templateId = Guid.NewGuid();
        var query = new GetMilestoneTemplateByIdQuery { Id = templateId };
        var cancellationToken = new CancellationToken(true);

        _mockRepository.Setup(r => r.GetByIdAsync(templateId, cancellationToken))
                      .ReturnsAsync((MilestoneTemplate?)null);

        // Act & Assert
        var act = async () => await _handler.Handle(query, cancellationToken);
        await act.Should().ThrowAsync<OperationCanceledException>();

        _mockRepository.Verify(r => r.GetByIdAsync(templateId, cancellationToken), Times.Once);
    }
}

public class GetAllMilestoneTemplatesQueryHandlerTests
{
    private readonly Mock<IMilestoneTemplateRepository> _mockRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly Mock<ILogger<GetAllMilestoneTemplatesQueryHandler>> _mockLogger;
    private readonly GetAllMilestoneTemplatesQueryHandler _handler;

    public GetAllMilestoneTemplatesQueryHandlerTests()
    {
        _mockRepository = new Mock<IMilestoneTemplateRepository>();
        _mockMapper = new Mock<IMapper>();
        _mockLogger = new Mock<ILogger<GetAllMilestoneTemplatesQueryHandler>>();
        _handler = new GetAllMilestoneTemplatesQueryHandler(_mockRepository.Object, _mockMapper.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task Handle_WithTemplates_ShouldReturnMappedTemplates()
    {
        // Arrange
        var query = new GetAllMilestoneTemplatesQuery();

        var templates = new List<MilestoneTemplate>
        {
            new MilestoneTemplate("Template 1", "Description 1", "Trip", "Logistics", "admin1"),
            new MilestoneTemplate("Template 2", "Description 2", "Order", "Delivery", "admin2")
        };

        var expectedDtos = templates.Select(t => new MilestoneTemplateDto
        {
            Id = t.Id,
            Name = t.Name,
            Description = t.Description,
            Type = t.Type,
            Category = t.Category
        }).ToList();

        _mockRepository.Setup(r => r.GetAllAsync(It.IsAny<CancellationToken>()))
                      .ReturnsAsync(templates);

        _mockMapper.Setup(m => m.Map<IEnumerable<MilestoneTemplateDto>>(templates))
                   .Returns(expectedDtos);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(2);
        result.Should().BeEquivalentTo(expectedDtos);

        _mockRepository.Verify(r => r.GetAllAsync(It.IsAny<CancellationToken>()), Times.Once);
        _mockMapper.Verify(m => m.Map<IEnumerable<MilestoneTemplateDto>>(templates), Times.Once);
    }

    [Fact]
    public async Task Handle_WithNoTemplates_ShouldReturnEmptyList()
    {
        // Arrange
        var query = new GetAllMilestoneTemplatesQuery();
        var emptyTemplates = new List<MilestoneTemplate>();
        var emptyDtos = new List<MilestoneTemplateDto>();

        _mockRepository.Setup(r => r.GetAllAsync(It.IsAny<CancellationToken>()))
                      .ReturnsAsync(emptyTemplates);

        _mockMapper.Setup(m => m.Map<IEnumerable<MilestoneTemplateDto>>(emptyTemplates))
                   .Returns(emptyDtos);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEmpty();

        _mockRepository.Verify(r => r.GetAllAsync(It.IsAny<CancellationToken>()), Times.Once);
        _mockMapper.Verify(m => m.Map<IEnumerable<MilestoneTemplateDto>>(emptyTemplates), Times.Once);
    }

    [Fact]
    public async Task Handle_WhenRepositoryThrows_ShouldPropagateException()
    {
        // Arrange
        var query = new GetAllMilestoneTemplatesQuery();

        _mockRepository.Setup(r => r.GetAllAsync(It.IsAny<CancellationToken>()))
                      .ThrowsAsync(new Exception("Database connection failed"));

        // Act & Assert
        var act = async () => await _handler.Handle(query, CancellationToken.None);
        await act.Should().ThrowAsync<Exception>()
                 .WithMessage("Database connection failed");

        _mockRepository.Verify(r => r.GetAllAsync(It.IsAny<CancellationToken>()), Times.Once);
        _mockMapper.Verify(m => m.Map<IEnumerable<MilestoneTemplateDto>>(It.IsAny<IEnumerable<MilestoneTemplate>>()), Times.Never);
    }
}
