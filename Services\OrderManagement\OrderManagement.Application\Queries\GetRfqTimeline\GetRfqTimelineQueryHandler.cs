using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.DTOs;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Application.Queries.GetRfqTimeline;

public class GetRfqTimelineQueryHandler : IRequestHandler<GetRfqTimelineQuery, List<RfqTimelineEventDto>>
{
    private readonly IRfqTimelineRepository _timelineRepository;
    private readonly IRfqRepository _rfqRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetRfqTimelineQueryHandler> _logger;

    public GetRfqTimelineQueryHandler(
        IRfqTimelineRepository timelineRepository,
        IRfqRepository rfqRepository,
        IMapper mapper,
        ILogger<GetRfqTimelineQueryHandler> logger)
    {
        _timelineRepository = timelineRepository;
        _rfqRepository = rfqRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<List<RfqTimelineEventDto>> Handle(GetRfqTimelineQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting timeline for RFQ {RfqId} by user {UserId}", 
            request.RfqId, request.RequestingUserId);

        // Verify RFQ exists and user has access
        var rfq = await _rfqRepository.GetByIdAsync(request.RfqId, cancellationToken);
        if (rfq == null)
        {
            _logger.LogWarning("RFQ {RfqId} not found", request.RfqId);
            return new List<RfqTimelineEventDto>();
        }

        // TODO: Add authorization logic here
        // Check if the requesting user has permission to view this RFQ timeline

        // Parse event types filter if provided
        List<RfqTimelineEventType>? eventTypeFilters = null;
        if (request.EventTypes?.Any() == true)
        {
            eventTypeFilters = new List<RfqTimelineEventType>();
            foreach (var eventTypeStr in request.EventTypes)
            {
                if (Enum.TryParse<RfqTimelineEventType>(eventTypeStr, true, out var eventType))
                {
                    eventTypeFilters.Add(eventType);
                }
            }
        }

        // Get timeline events with filters
        var timelineEvents = await _timelineRepository.GetByRfqIdAsync(
            request.RfqId,
            request.FromDate,
            request.ToDate,
            eventTypeFilters,
            cancellationToken);

        var timelineEventDtos = _mapper.Map<List<RfqTimelineEventDto>>(timelineEvents);

        _logger.LogInformation("Retrieved {Count} timeline events for RFQ {RfqId}",
            timelineEventDtos.Count, request.RfqId);

        return timelineEventDtos;
    }
}
