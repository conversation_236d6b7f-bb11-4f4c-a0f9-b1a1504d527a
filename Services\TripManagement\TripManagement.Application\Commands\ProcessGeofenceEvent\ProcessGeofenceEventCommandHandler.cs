using MediatR;
using Microsoft.Extensions.Logging;
using TripManagement.Application.Interfaces;
using TripManagement.Domain.Enums;

namespace TripManagement.Application.Commands.ProcessGeofenceEvent;

public class ProcessGeofenceEventCommandHandler : IRequestHandler<ProcessGeofenceEventCommand, bool>
{
    private readonly ITripRepository _tripRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<ProcessGeofenceEventCommandHandler> _logger;
    private readonly IMessageBroker _messageBroker;
    private readonly INotificationService _notificationService;

    public ProcessGeofenceEventCommandHandler(
        ITripRepository tripRepository,
        IUnitOfWork unitOfWork,
        ILogger<ProcessGeofenceEventCommandHandler> logger,
        IMessageBroker messageBroker,
        INotificationService notificationService)
    {
        _tripRepository = tripRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
        _messageBroker = messageBroker;
        _notificationService = notificationService;
    }

    public async Task<bool> Handle(ProcessGeofenceEventCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Processing geofence event {EventType} for trip {TripId} at geofence {GeofenceName}", 
                request.EventType, request.TripId, request.GeofenceName);

            // Get the trip
            var trip = await _tripRepository.GetByIdAsync(request.TripId, cancellationToken);
            if (trip == null)
            {
                _logger.LogWarning("Trip {TripId} not found", request.TripId);
                return false;
            }

            // Verify driver authorization
            if (trip.DriverId != request.DriverId)
            {
                _logger.LogWarning("Driver {DriverId} is not authorized for trip {TripId}", 
                    request.DriverId, request.TripId);
                return false;
            }

            // Process different geofence events
            await ProcessGeofenceEventAsync(trip, request, cancellationToken);

            // Update trip location
            trip.UpdateCurrentLocation(new Domain.ValueObjects.Location(
                request.CurrentLocation.Latitude,
                request.CurrentLocation.Longitude,
                request.CurrentLocation.Altitude,
                request.CurrentLocation.Accuracy,
                request.CurrentLocation.Address,
                request.CurrentLocation.City,
                request.CurrentLocation.State,
                request.CurrentLocation.Country,
                request.CurrentLocation.PostalCode));

            // Save changes
            _tripRepository.Update(trip);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Publish integration event
            await _messageBroker.PublishAsync("trip.geofence_event", new
            {
                TripId = request.TripId,
                DriverId = request.DriverId,
                EventType = request.EventType.ToString(),
                GeofenceName = request.GeofenceName,
                Location = request.CurrentLocation,
                EventTime = request.EventTime
            }, cancellationToken);

            _logger.LogInformation("Geofence event processed successfully for trip {TripId}", request.TripId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing geofence event for trip {TripId}", request.TripId);
            throw;
        }
    }

    private async Task ProcessGeofenceEventAsync(Domain.Entities.Trip trip, ProcessGeofenceEventCommand request, CancellationToken cancellationToken)
    {
        switch (request.EventType)
        {
            case GeofenceEventType.Enter:
                await HandleGeofenceEnterAsync(trip, request, cancellationToken);
                break;
            case GeofenceEventType.Exit:
                await HandleGeofenceExitAsync(trip, request, cancellationToken);
                break;
            case GeofenceEventType.Dwell:
                await HandleGeofenceDwellAsync(trip, request, cancellationToken);
                break;
            case GeofenceEventType.Approach:
                await HandleGeofenceApproachAsync(trip, request, cancellationToken);
                break;
        }
    }

    private async Task HandleGeofenceEnterAsync(Domain.Entities.Trip trip, ProcessGeofenceEventCommand request, CancellationToken cancellationToken)
    {
        // Check if this is a delivery/pickup location
        var relevantStop = trip.Stops.FirstOrDefault(s => 
            s.Location.Address?.Contains(request.GeofenceName, StringComparison.OrdinalIgnoreCase) == true ||
            s.ContactName?.Contains(request.GeofenceName, StringComparison.OrdinalIgnoreCase) == true);

        if (relevantStop != null && relevantStop.Status == Domain.Enums.TripStopStatus.Pending)
        {
            relevantStop.MarkArrived();
            
            // Send arrival notification
            await _notificationService.SendNotificationAsync(
                trip.CarrierId,
                "Driver Arrived",
                $"Driver has arrived at {request.GeofenceName} for trip {trip.TripNumber}",
                cancellationToken);
        }
    }

    private async Task HandleGeofenceExitAsync(Domain.Entities.Trip trip, ProcessGeofenceEventCommand request, CancellationToken cancellationToken)
    {
        // Check if driver is leaving without completing required stop
        var relevantStop = trip.Stops.FirstOrDefault(s => 
            s.Location.Address?.Contains(request.GeofenceName, StringComparison.OrdinalIgnoreCase) == true &&
            s.Status != Domain.Enums.TripStopStatus.Completed);

        if (relevantStop != null)
        {
            // Create exception for incomplete stop
            trip.AddException(ExceptionType.RouteDeviation, 
                $"Driver left {request.GeofenceName} without completing required stop");

            // Send alert notification
            await _notificationService.SendNotificationAsync(
                trip.CarrierId,
                "Incomplete Stop Alert",
                $"Driver left {request.GeofenceName} without completing stop for trip {trip.TripNumber}",
                cancellationToken);
        }
    }

    private async Task HandleGeofenceDwellAsync(Domain.Entities.Trip trip, ProcessGeofenceEventCommand request, CancellationToken cancellationToken)
    {
        // Handle extended dwell time - could indicate delays or issues
        await _notificationService.SendNotificationAsync(
            trip.CarrierId,
            "Extended Stop Time",
            $"Driver has been at {request.GeofenceName} for an extended period for trip {trip.TripNumber}",
            cancellationToken);
    }

    private async Task HandleGeofenceApproachAsync(Domain.Entities.Trip trip, ProcessGeofenceEventCommand request, CancellationToken cancellationToken)
    {
        // Send approach notification to relevant parties
        var relevantStop = trip.Stops.FirstOrDefault(s => 
            s.Location.Address?.Contains(request.GeofenceName, StringComparison.OrdinalIgnoreCase) == true);

        if (relevantStop != null && !string.IsNullOrEmpty(relevantStop.ContactPhone))
        {
            await _notificationService.SendNotificationAsync(
                trip.CarrierId,
                "Driver Approaching",
                $"Driver is approaching {request.GeofenceName} for trip {trip.TripNumber}. ETA: 5-10 minutes",
                cancellationToken);
        }
    }
}
