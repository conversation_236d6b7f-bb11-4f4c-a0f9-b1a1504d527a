namespace MonitoringObservability.Domain.DTOs;

/// <summary>
/// DTO for service dependency graph
/// </summary>
public class ServiceDependencyGraphDto
{
    public List<ServiceNodeDto> Services { get; set; } = new();
    public List<DependencyEdgeDto> Dependencies { get; set; } = new();
    public DateTime GeneratedAt { get; set; }
    public string? Environment { get; set; }
}

public class ServiceNodeDto
{
    public string ServiceName { get; set; } = string.Empty;
    public string ServiceType { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class DependencyEdgeDto
{
    public string FromService { get; set; } = string.Empty;
    public string ToService { get; set; } = string.Empty;
    public string DependencyType { get; set; } = string.Empty;
    public bool IsCritical { get; set; }
    public Dictionary<string, object> Properties { get; set; } = new();
}
