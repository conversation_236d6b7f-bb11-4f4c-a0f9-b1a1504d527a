# Trip Management Service

A comprehensive trip management microservice built with .NET 8, featuring clean architecture principles, domain-driven design, and real-time tracking capabilities for the TLI Logistics platform.

## 🏗️ Architecture Overview

This service follows Clean Architecture principles with the following layers:

### Domain Layer (`TripManagement.Domain`)
- **Entities**: Core business entities (Trip, Driver, Vehicle, TripStop, etc.)
- **Value Objects**: Location, Route, TimeWindow
- **Domain Events**: Trip lifecycle events
- **Enums**: TripStatus, DriverStatus, VehicleStatus, etc.

### Application Layer (`TripManagement.Application`)
- **Commands**: CQRS commands for write operations
- **Queries**: CQRS queries for read operations
- **DTOs**: Data transfer objects
- **Interfaces**: Repository and service contracts
- **Mappings**: AutoMapper profiles

### Infrastructure Layer (`TripManagement.Infrastructure`)
- **Database**: Entity Framework Core with PostgreSQL/TimescaleDB
- **Repositories**: Data access implementations
- **Configurations**: Entity configurations

### API Layer (`TripManagement.API`)
- **Controllers**: REST API endpoints
- **Authentication**: JWT token validation
- **Swagger**: API documentation

## 🚀 Features

### Trip Management
- Create trips from confirmed orders
- Assign drivers and vehicles to trips
- Track trip lifecycle from creation to completion
- Real-time location updates and tracking
- Trip exception handling and resolution

### Driver Management
- Driver registration and profile management
- License verification and expiry tracking
- Driver status management (Available, OnTrip, OffDuty, etc.)
- Performance tracking and ratings
- Document management (license, certifications, etc.)

### Vehicle Management
- Vehicle registration and specifications
- Insurance and fitness certificate tracking
- Maintenance scheduling and history
- Vehicle availability management
- Document management (RC, insurance, fitness, etc.)

### Real-time Tracking
- GPS location updates from mobile devices
- Route optimization and navigation
- Geofencing and alerts
- ETA calculations and updates
- Exception reporting and handling

### Proof of Delivery (POD)
- Digital signature collection
- Photo documentation
- Delivery confirmation workflow
- Recipient verification
- Document storage and retrieval

## 🛠️ Technology Stack

- **.NET 8**: Latest .NET framework
- **Entity Framework Core**: ORM for database operations
- **PostgreSQL/TimescaleDB**: Primary database for time-series data
- **MediatR**: CQRS implementation
- **AutoMapper**: Object mapping
- **FluentValidation**: Input validation
- **Swagger**: API documentation
- **Serilog**: Structured logging
- **RabbitMQ**: Message broker for events

## 🔧 Configuration

### Database Connection
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Port=5432;Database=TLI_TripManagement;User Id=timescale;Password=timescale"
  }
}
```

### JWT Authentication
```json
{
  "JwtSettings": {
    "SecretKey": "YourSuperSecretKeyThatIsAtLeast32CharactersLong!",
    "Issuer": "TLI.TripManagement",
    "Audience": "TLI.Users",
    "ExpiryMinutes": 60
  }
}
```

## 📡 API Endpoints

### Trips
- `POST /api/trips` - Create a new trip
- `GET /api/trips/{id}` - Get trip details
- `POST /api/trips/{id}/assign` - Assign driver and vehicle
- `POST /api/trips/{id}/location` - Update trip location
- `POST /api/trips/{id}/start` - Start trip
- `POST /api/trips/{id}/complete` - Complete trip

### Drivers
- `GET /api/drivers` - Get available drivers
- `GET /api/drivers/{id}` - Get driver details
- `POST /api/drivers` - Register new driver
- `PUT /api/drivers/{id}/status` - Update driver status

### Vehicles
- `GET /api/vehicles` - Get available vehicles
- `GET /api/vehicles/{id}` - Get vehicle details
- `POST /api/vehicles` - Register new vehicle
- `PUT /api/vehicles/{id}/status` - Update vehicle status

## 🔄 Integration Events

### Published Events
- `trip.created` - When a trip is created
- `trip.assigned` - When driver and vehicle are assigned
- `trip.started` - When trip execution begins
- `trip.location_updated` - Real-time location updates
- `trip.completed` - When trip is completed
- `trip.exception` - When exceptions occur
- `driver.assigned_to_trip` - Driver assignment events
- `vehicle.assigned_to_trip` - Vehicle assignment events

### Consumed Events
- `order.confirmed` - From Order Management Service
- `payment.escrowed` - From Financial Service
- `user.registered` - From User Management Service

## 🚀 Getting Started

### Prerequisites
- .NET 8 SDK
- PostgreSQL/TimescaleDB
- RabbitMQ (optional, for messaging)

### Setup
1. Clone the repository
2. Navigate to the Trip Management service directory
3. Update connection strings in `appsettings.json`
4. Run database migrations:
   ```bash
   dotnet ef database update
   ```
5. Start the service:
   ```bash
   dotnet run
   ```

### Docker Support
```bash
docker build -t trip-management-service .
docker run -p 5003:80 trip-management-service
```

## 📊 Database Schema

### Core Tables
- `trips` - Main trip information
- `trip_stops` - Pickup and delivery stops
- `trip_location_updates` - Real-time location tracking
- `trip_exceptions` - Exception handling
- `drivers` - Driver profiles and information
- `vehicles` - Vehicle specifications and status
- `proof_of_deliveries` - Delivery confirmations

### Time-Series Data
- Location updates are optimized for time-series queries
- Efficient indexing for real-time tracking
- Historical data retention policies

## 🔒 Security

### Authentication
- JWT bearer token authentication
- Role-based authorization (Admin, Carrier, Driver, Broker)

### Data Protection
- Sensitive data encryption
- Secure API endpoints
- Input validation and sanitization

## 📈 Monitoring and Observability

### Health Checks
- Database connectivity
- Message broker connectivity
- External service dependencies

### Logging
- Structured logging with Serilog
- Request/response logging
- Error tracking and alerting

### Metrics
- Trip completion rates
- Driver performance metrics
- Vehicle utilization
- Real-time tracking accuracy

## 🤝 Contributing

1. Follow clean architecture principles
2. Write comprehensive tests
3. Use proper logging
4. Follow naming conventions
5. Document API changes

## 📝 License

This project is part of the TLI Logistics platform.
