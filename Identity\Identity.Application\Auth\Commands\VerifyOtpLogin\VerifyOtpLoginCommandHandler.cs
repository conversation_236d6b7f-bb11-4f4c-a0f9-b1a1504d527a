using System;
using System.Threading;
using System.Threading.Tasks;
using Identity.Application.Common.Interfaces;
using Identity.Application.Common.Models;
using Identity.Domain.Entities;
using Identity.Domain.Repositories;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Identity.Application.Auth.Commands.VerifyOtpLogin
{
    public class VerifyOtpLoginCommandHandler : IRequestHandler<VerifyOtpLoginCommand, TokenResponse>
    {
        private readonly IUserRepository _userRepository;
        private readonly IUserSessionRepository _sessionRepository;
        private readonly IOtpService _otpService;
        private readonly ITokenService _tokenService;
        private readonly ILogger<VerifyOtpLoginCommandHandler> _logger;

        public VerifyOtpLoginCommandHandler(
            IUserRepository userRepository,
            IUserSessionRepository sessionRepository,
            IOtpService otpService,
            ITokenService tokenService,
            ILogger<VerifyOtpLoginCommandHandler> logger)
        {
            _userRepository = userRepository;
            _sessionRepository = sessionRepository;
            _otpService = otpService;
            _tokenService = tokenService;
            _logger = logger;
        }

        public async Task<TokenResponse> Handle(VerifyOtpLoginCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing OTP verification for username: {Username}", request.Username);

                // Find user by username or email
                var user = await _userRepository.GetByUsernameAsync(request.Username) ??
                          await _userRepository.GetByEmailAsync(request.Username);

                if (user == null)
                {
                    _logger.LogWarning("User not found for OTP verification: {Username}", request.Username);
                    throw new ApplicationException("Invalid credentials");
                }

                // Check if user account is active
                if (user.Status != UserStatus.Active)
                {
                    _logger.LogWarning("Inactive user attempted OTP verification: {UserId}", user.Id);
                    throw new ApplicationException("Account is not active");
                }

                // Check if user is locked out
                if (user.LockoutEnd.HasValue && user.LockoutEnd > DateTime.UtcNow)
                {
                    _logger.LogWarning("Locked out user attempted OTP verification: {UserId}", user.Id);
                    throw new ApplicationException("Account is temporarily locked");
                }

                // Validate OTP
                var isValidOtp = await _otpService.ValidateOtpAsync(user.Id, request.OtpToken, OtpPurpose.Login);
                
                if (!isValidOtp)
                {
                    // Increment failed login attempts
                    user.IncrementAccessFailedCount();
                    await _userRepository.UpdateAsync(user);

                    // Check if account should be locked
                    if (user.AccessFailedCount >= 5 && user.LockoutEnabled)
                    {
                        user.LockoutUser(TimeSpan.FromMinutes(15));
                        await _userRepository.UpdateAsync(user);
                        _logger.LogWarning("User {UserId} account locked due to multiple failed OTP attempts", user.Id);
                        throw new ApplicationException("Account locked due to multiple failed attempts");
                    }

                    _logger.LogWarning("Invalid OTP provided for user: {UserId}", user.Id);
                    throw new ApplicationException("Invalid OTP");
                }

                // Reset failed login attempts and update user
                user.ResetAccessFailedCount();
                await _userRepository.UpdateAsync(user);

                // Detach the user entity to avoid tracking conflicts
                await _userRepository.DetachAsync(user);

                // Generate tokens
                var tokenResponse = await _tokenService.GenerateTokensAsync(user.Id);

                // Create user session
                var session = new UserSession(
                    user.Id,
                    tokenResponse.AccessToken,
                    request.DeviceInfo,
                    request.IpAddress,
                    tokenResponse.ExpiresAt);

                await _sessionRepository.AddAsync(session);

                // Invalidate any remaining OTP tokens for this user
                await _otpService.InvalidateUserOtpsAsync(user.Id, OtpPurpose.Login);

                _logger.LogInformation("User {UserId} logged in successfully via OTP", user.Id);

                return tokenResponse;
            }
            catch (ApplicationException)
            {
                throw; // Re-throw application exceptions as-is
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing OTP verification for username: {Username}", request.Username);
                throw new ApplicationException("An error occurred while processing your request");
            }
        }
    }
}
