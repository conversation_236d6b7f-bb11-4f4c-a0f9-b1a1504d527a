using Shared.Domain.ValueObjects;

namespace MobileWorkflow.Domain.ValueObjects;

public class WorkflowTemplateRating : ValueObject
{
    public decimal AverageRating { get; private set; }
    public int TotalRatings { get; private set; }
    public DateTime LastUpdated { get; private set; }

    private WorkflowTemplateRating() { }

    public WorkflowTemplateRating(decimal averageRating, int totalRatings)
    {
        AverageRating = averageRating;
        TotalRatings = totalRatings;
        LastUpdated = DateTime.UtcNow;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return AverageRating;
        yield return TotalRatings;
    }
}