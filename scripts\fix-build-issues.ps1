# TLI Microservices - Build Issues Resolution Script
# This script systematically fixes compilation errors identified in the build

param(
    [switch]$DryRun = $false,
    [switch]$Verbose = $false,
    [string]$Service = ""
)

$ErrorActionPreference = "Continue"

Write-Host "🔧 TLI Microservices - Build Issues Resolution" -ForegroundColor Cyan
Write-Host "=============================================" -ForegroundColor Cyan
Write-Host "Dry Run: $DryRun" -ForegroundColor White
Write-Host "Verbose: $Verbose" -ForegroundColor White
Write-Host ""

# Function to log messages
function Write-LogMessage {
    param(
        [string]$Message,
        [string]$Level = "INFO",
        [string]$Color = "White"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    if ($Verbose -or $Level -eq "ERROR" -or $Level -eq "WARN") {
        Write-Host $logMessage -ForegroundColor $Color
    }
}

# Function to update package references
function Update-PackageReference {
    param(
        [string]$ProjectPath,
        [string]$PackageName,
        [string]$Version
    )
    
    Write-LogMessage "Updating $PackageName to version $Version in $ProjectPath" "INFO"
    
    if (-not $DryRun) {
        try {
            $content = Get-Content $ProjectPath -Raw
            $pattern = "<PackageReference Include=`"$PackageName`" Version=`"[^`"]*`" />"
            $replacement = "<PackageReference Include=`"$PackageName`" Version=`"$Version`" />"
            $newContent = $content -replace $pattern, $replacement
            Set-Content -Path $ProjectPath -Value $newContent -NoNewline
            Write-LogMessage "Updated $PackageName successfully" "INFO" "Green"
        }
        catch {
            Write-LogMessage "Failed to update $PackageName`: $_" "ERROR" "Red"
        }
    }
}

# Function to add missing package references
function Add-PackageReference {
    param(
        [string]$ProjectPath,
        [string]$PackageName,
        [string]$Version
    )
    
    Write-LogMessage "Adding $PackageName version $Version to $ProjectPath" "INFO"
    
    if (-not $DryRun) {
        try {
            $content = Get-Content $ProjectPath -Raw
            
            # Find the ItemGroup with PackageReference elements
            $itemGroupPattern = '(<ItemGroup>\s*<PackageReference[^>]*>.*?</ItemGroup>)'
            if ($content -match $itemGroupPattern) {
                $newPackageRef = "    <PackageReference Include=`"$PackageName`" Version=`"$Version`" />"
                $replacement = $matches[1] -replace '(</ItemGroup>)', "$newPackageRef`r`n  `$1"
                $newContent = $content -replace [regex]::Escape($matches[1]), $replacement
                Set-Content -Path $ProjectPath -Value $newContent -NoNewline
                Write-LogMessage "Added $PackageName successfully" "INFO" "Green"
            }
        }
        catch {
            Write-LogMessage "Failed to add $PackageName`: $_" "ERROR" "Red"
        }
    }
}

# Function to fix namespace issues
function Fix-NamespaceIssues {
    param(
        [string]$FilePath,
        [hashtable]$NamespaceFixes
    )
    
    Write-LogMessage "Fixing namespace issues in $FilePath" "INFO"
    
    if (-not $DryRun) {
        try {
            $content = Get-Content $FilePath -Raw
            $modified = $false
            
            foreach ($namespace in $NamespaceFixes.Keys) {
                if ($content -notmatch "using $namespace;") {
                    $content = "using $namespace;`r`n" + $content
                    $modified = $true
                }
            }
            
            if ($modified) {
                Set-Content -Path $FilePath -Value $content -NoNewline
                Write-LogMessage "Fixed namespace issues in $FilePath" "INFO" "Green"
            }
        }
        catch {
            Write-LogMessage "Failed to fix namespace issues in $FilePath`: $_" "ERROR" "Red"
        }
    }
}

# Step 1: Fix Package Version Conflicts
Write-Host "🔧 Step 1: Fixing Package Version Conflicts..." -ForegroundColor Cyan

$packageUpdates = @{
    "Microsoft.Extensions.DependencyInjection.Abstractions" = "9.0.5"
    "Microsoft.Extensions.Configuration" = "9.0.5"
    "Microsoft.Extensions.Configuration.Abstractions" = "9.0.5"
    "Microsoft.AspNetCore.Http.Features" = "6.0.36"
    "MediatR" = "12.4.1"
}

$projectFiles = Get-ChildItem -Path "." -Recurse -Filter "*.csproj"

foreach ($projectFile in $projectFiles) {
    Write-LogMessage "Processing project: $($projectFile.FullName)" "INFO"
    
    foreach ($package in $packageUpdates.Keys) {
        $version = $packageUpdates[$package]
        
        # Check if package exists in project
        $content = Get-Content $projectFile.FullName -Raw
        if ($content -match "<PackageReference Include=`"$package`"") {
            Update-PackageReference -ProjectPath $projectFile.FullName -PackageName $package -Version $version
        }
    }
}

# Step 2: Add Missing Package References
Write-Host "`n🔧 Step 2: Adding Missing Package References..." -ForegroundColor Cyan

$missingPackages = @{
    "Services/SubscriptionManagement/SubscriptionManagement.Application/SubscriptionManagement.Application.csproj" = @{
        "Microsoft.AspNetCore.Http.Features" = "6.0.36"
        "Microsoft.Extensions.Configuration.Abstractions" = "9.0.5"
    }
    "Services/UserManagement/UserManagement.Application/UserManagement.Application.csproj" = @{
        "Microsoft.Extensions.Configuration.Abstractions" = "9.0.5"
    }
}

foreach ($projectPath in $missingPackages.Keys) {
    if (Test-Path $projectPath) {
        $packages = $missingPackages[$projectPath]
        foreach ($package in $packages.Keys) {
            $version = $packages[$package]
            Add-PackageReference -ProjectPath $projectPath -PackageName $package -Version $version
        }
    }
}

# Step 3: Fix Missing Interface Implementations
Write-Host "`n🔧 Step 3: Creating Missing Interface Implementations..." -ForegroundColor Cyan

$missingInterfaces = @(
    @{
        Path = "Services/SubscriptionManagement/SubscriptionManagement.Infrastructure/Services/SubscriptionMetricsService.cs"
        Content = @'
using SubscriptionManagement.Application.Interfaces;

namespace SubscriptionManagement.Infrastructure.Services;

public class SubscriptionMetricsService : ISubscriptionMetricsService
{
    public Task TrackSubscriptionCreatedAsync(Guid subscriptionId, string planName)
    {
        // Implementation placeholder
        return Task.CompletedTask;
    }

    public Task TrackSubscriptionCancelledAsync(Guid subscriptionId, string reason)
    {
        // Implementation placeholder
        return Task.CompletedTask;
    }
}
'@
    }
    @{
        Path = "Services/SubscriptionManagement/SubscriptionManagement.Application/Interfaces/ISubscriptionMetricsService.cs"
        Content = @'
namespace SubscriptionManagement.Application.Interfaces;

public interface ISubscriptionMetricsService
{
    Task TrackSubscriptionCreatedAsync(Guid subscriptionId, string planName);
    Task TrackSubscriptionCancelledAsync(Guid subscriptionId, string reason);
}
'@
    }
)

foreach ($interface in $missingInterfaces) {
    $directory = Split-Path -Parent $interface.Path
    if (-not (Test-Path $directory)) {
        New-Item -ItemType Directory -Path $directory -Force | Out-Null
    }
    
    if (-not $DryRun) {
        Set-Content -Path $interface.Path -Value $interface.Content
        Write-LogMessage "Created missing interface: $($interface.Path)" "INFO" "Green"
    }
}

# Step 4: Fix Domain Event Issues
Write-Host "`n🔧 Step 4: Fixing Domain Event Issues..." -ForegroundColor Cyan

$domainEventFixes = @{
    "Services/SubscriptionManagement/SubscriptionManagement.Domain/Events/PaymentProofRejectedEvent.cs" = @'
using MediatR;

namespace SubscriptionManagement.Domain.Events;

public class PaymentProofRejectedEvent : INotification
{
    public Guid SubscriptionId { get; set; }
    public string RejectionReason { get; set; } = string.Empty;
    public DateTime RejectedAt { get; set; }
}
'@
    "Services/SubscriptionManagement/SubscriptionManagement.Domain/Events/PaymentProofVerifiedEvent.cs" = @'
using MediatR;

namespace SubscriptionManagement.Domain.Events;

public class PaymentProofVerifiedEvent : INotification
{
    public Guid SubscriptionId { get; set; }
    public DateTime VerifiedAt { get; set; }
    public string VerifiedBy { get; set; } = string.Empty;
}
'@
}

foreach ($filePath in $domainEventFixes.Keys) {
    $directory = Split-Path -Parent $filePath
    if (-not (Test-Path $directory)) {
        New-Item -ItemType Directory -Path $directory -Force | Out-Null
    }
    
    if (-not $DryRun) {
        Set-Content -Path $filePath -Value $domainEventFixes[$filePath]
        Write-LogMessage "Fixed domain event: $filePath" "INFO" "Green"
    }
}

# Step 5: Fix Missing Types and Enums
Write-Host "`n🔧 Step 5: Creating Missing Types and Enums..." -ForegroundColor Cyan

$missingTypes = @{
    "Services/UserManagement/UserManagement.Domain/Enums/UserType.cs" = @'
namespace UserManagement.Domain.Enums;

public enum UserType
{
    Shipper = 1,
    Transporter = 2,
    Broker = 3,
    Admin = 4
}
'@
    "Services/UserManagement/UserManagement.Domain/Enums/ProfileStatus.cs" = @'
namespace UserManagement.Domain.Enums;

public enum ProfileStatus
{
    Pending = 1,
    Active = 2,
    Suspended = 3,
    Deactivated = 4
}
'@
    "Services/UserManagement/UserManagement.Domain/Enums/UserStatus.cs" = @'
namespace UserManagement.Domain.Enums;

public enum UserStatus
{
    Active = 1,
    Inactive = 2,
    Suspended = 3,
    Banned = 4
}
'@
}

foreach ($filePath in $missingTypes.Keys) {
    $directory = Split-Path -Parent $filePath
    if (-not (Test-Path $directory)) {
        New-Item -ItemType Directory -Path $directory -Force | Out-Null
    }
    
    if (-not $DryRun) {
        Set-Content -Path $filePath -Value $missingTypes[$filePath]
        Write-LogMessage "Created missing type: $filePath" "INFO" "Green"
    }
}

# Step 6: Run Build Test
Write-Host "`n🔧 Step 6: Testing Build..." -ForegroundColor Cyan

if (-not $DryRun) {
    Write-LogMessage "Running build test..." "INFO"
    $buildResult = & dotnet build TLIMicroservices.sln --configuration Debug --verbosity minimal
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Build successful after fixes!" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Build still has issues. Manual intervention may be required." -ForegroundColor Yellow
        Write-LogMessage "Build output: $buildResult" "WARN" "Yellow"
    }
} else {
    Write-Host "🔍 Dry run completed. Use -DryRun:`$false to apply fixes." -ForegroundColor Yellow
}

Write-Host "`n✅ Build issues resolution completed!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Summary of Actions:" -ForegroundColor Cyan
Write-Host "- Updated package version conflicts" -ForegroundColor White
Write-Host "- Added missing package references" -ForegroundColor White
Write-Host "- Created missing interface implementations" -ForegroundColor White
Write-Host "- Fixed domain event issues" -ForegroundColor White
Write-Host "- Created missing types and enums" -ForegroundColor White
