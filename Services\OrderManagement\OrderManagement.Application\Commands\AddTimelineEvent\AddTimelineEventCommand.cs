using MediatR;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Application.Commands.AddTimelineEvent;

public class AddTimelineEventCommand : IRequest<Guid>
{
    public Guid OrderId { get; set; }
    public OrderTimelineEventType EventType { get; set; }
    public string EventDescription { get; set; } = string.Empty;
    public Guid? ActorId { get; set; }
    public string? ActorName { get; set; }
    public string? ActorRole { get; set; }
    public string? AdditionalData { get; set; }
    public OrderStatus? PreviousStatus { get; set; }
    public OrderStatus? NewStatus { get; set; }
    public string? Notes { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
    public Guid? CorrelationId { get; set; }
    public string? SessionId { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}
