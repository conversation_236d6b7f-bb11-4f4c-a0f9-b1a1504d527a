using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OrderManagement.Application.Commands.CreateOrder;
using OrderManagement.Application.Commands.ConfirmOrder;
using OrderManagement.Application.Commands.CancelOrder;
using OrderManagement.Application.Commands.UpdateOrderStatus;
using OrderManagement.Application.Queries.GetOrders;
using OrderManagement.Application.Queries.GetOrderById;
using OrderManagement.Application.Queries.GetOrdersByStatus;
using OrderManagement.Domain.Enums;

namespace OrderManagement.API.Controllers;

[Authorize]
public class OrderController : BaseController
{
    /// <summary>
    /// Create a new order
    /// </summary>
    [HttpPost]
    [ProducesResponseType(typeof(Guid), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> CreateOrder([FromBody] CreateOrderCommand command)
    {
        try
        {
            // Set the transport company ID from the current user
            // Note: This assumes the current user is a transport company
            // In a real system, you might need more sophisticated role-based logic
            command.TransportCompanyId = GetCurrentUserId();
            
            var orderId = await Mediator.Send(command);
            return CreatedAtAction(nameof(GetOrder), new { id = orderId }, orderId);
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Get order by ID
    /// </summary>
    [HttpGet("{id}")]
    [ProducesResponseType(typeof(Application.DTOs.OrderDetailDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> GetOrder(Guid id)
    {
        try
        {
            var query = new GetOrderByIdQuery
            {
                OrderId = id,
                UserId = GetCurrentUserId(),
                UserRole = GetCurrentUserRole()
            };

            var order = await Mediator.Send(query);
            
            if (order == null)
                return NotFound(new { message = "Order not found" });

            return Ok(order);
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Get orders with filtering and pagination
    /// </summary>
    [HttpGet]
    [ProducesResponseType(typeof(Application.DTOs.PagedResult<Application.DTOs.OrderSummaryDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetOrders(
        [FromQuery] OrderStatus? status = null,
        [FromQuery] PaymentStatus? paymentStatus = null,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] string? searchTerm = null,
        [FromQuery] bool? isUrgent = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20,
        [FromQuery] string? sortBy = "CreatedAt",
        [FromQuery] string? sortDirection = "desc")
    {
        try
        {
            var query = new GetOrdersQuery
            {
                UserId = GetCurrentUserId(),
                UserRole = GetCurrentUserRole(),
                Status = status,
                PaymentStatus = paymentStatus,
                FromDate = fromDate,
                ToDate = toDate,
                SearchTerm = searchTerm,
                IsUrgent = isUrgent,
                Page = page,
                PageSize = pageSize,
                SortBy = sortBy,
                SortDirection = sortDirection
            };

            var result = await Mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Get orders by status
    /// </summary>
    [HttpGet("status/{status}")]
    [ProducesResponseType(typeof(Application.DTOs.PagedResult<Application.DTOs.OrderSummaryDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetOrdersByStatus(
        OrderStatus status,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20,
        [FromQuery] string? sortBy = "CreatedAt",
        [FromQuery] string? sortDirection = "desc")
    {
        try
        {
            var query = new GetOrdersByStatusQuery
            {
                Status = status,
                UserId = GetCurrentUserId(),
                UserRole = GetCurrentUserRole(),
                FromDate = fromDate,
                ToDate = toDate,
                Page = page,
                PageSize = pageSize,
                SortBy = sortBy,
                SortDirection = sortDirection
            };

            var result = await Mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Confirm an order with a carrier
    /// </summary>
    [HttpPost("{id}/confirm")]
    [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> ConfirmOrder(Guid id, [FromBody] ConfirmOrderRequest request)
    {
        try
        {
            var command = new ConfirmOrderCommand
            {
                OrderId = id,
                CarrierId = request.CarrierId,
                TransportCompanyId = GetCurrentUserId(),
                Notes = request.Notes
            };

            var result = await Mediator.Send(command);
            return Ok(new { success = result, message = "Order confirmed successfully" });
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Cancel an order
    /// </summary>
    [HttpPost("{id}/cancel")]
    [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> CancelOrder(Guid id, [FromBody] CancelOrderRequest request)
    {
        try
        {
            var command = new CancelOrderCommand
            {
                OrderId = id,
                UserId = GetCurrentUserId(),
                CancellationReason = request.CancellationReason
            };

            var result = await Mediator.Send(command);
            return Ok(new { success = result, message = "Order cancelled successfully" });
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Update order status
    /// </summary>
    [HttpPut("{id}/status")]
    [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> UpdateOrderStatus(Guid id, [FromBody] UpdateOrderStatusRequest request)
    {
        try
        {
            var command = new UpdateOrderStatusCommand
            {
                OrderId = id,
                NewStatus = request.NewStatus,
                UserId = GetCurrentUserId(),
                Notes = request.Notes
            };

            var result = await Mediator.Send(command);
            return Ok(new { success = result, message = "Order status updated successfully" });
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Start order progress (move from Confirmed to InProgress)
    /// </summary>
    [HttpPost("{id}/start")]
    [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> StartOrder(Guid id, [FromBody] StartOrderRequest? request = null)
    {
        try
        {
            var command = new UpdateOrderStatusCommand
            {
                OrderId = id,
                NewStatus = OrderStatus.InProgress,
                UserId = GetCurrentUserId(),
                Notes = request?.Notes ?? "Order started"
            };

            var result = await Mediator.Send(command);
            return Ok(new { success = result, message = "Order started successfully" });
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Complete an order (move from InProgress to Completed)
    /// </summary>
    [HttpPost("{id}/complete")]
    [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> CompleteOrder(Guid id, [FromBody] CompleteOrderRequest? request = null)
    {
        try
        {
            var command = new UpdateOrderStatusCommand
            {
                OrderId = id,
                NewStatus = OrderStatus.Completed,
                UserId = GetCurrentUserId(),
                Notes = request?.Notes ?? "Order completed"
            };

            var result = await Mediator.Send(command);
            return Ok(new { success = result, message = "Order completed successfully" });
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }
}

// Request DTOs for the controller
public class ConfirmOrderRequest
{
    public Guid CarrierId { get; set; }
    public string? Notes { get; set; }
}

public class CancelOrderRequest
{
    public string CancellationReason { get; set; } = string.Empty;
}

public class UpdateOrderStatusRequest
{
    public OrderStatus NewStatus { get; set; }
    public string? Notes { get; set; }
}

public class StartOrderRequest
{
    public string? Notes { get; set; }
}

public class CompleteOrderRequest
{
    public string? Notes { get; set; }
}
