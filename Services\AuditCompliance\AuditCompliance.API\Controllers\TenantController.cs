using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using AuditCompliance.Application.Interfaces;
using AuditCompliance.Domain.Entities;
using AuditCompliance.Infrastructure.MultiTenant;

namespace AuditCompliance.API.Controllers;

/// <summary>
/// Controller for tenant management operations
/// </summary>
[ApiController]
[Route("api/v{version:apiVersion}/[controller]")]
[Route("api/[controller]")] // Backward compatibility
[ApiVersion("1.0")]
[ApiVersion("2.0")]
[Authorize(Roles = "Admin,TenantAdmin")]
public class TenantController : ControllerBase
{
    private readonly ITenantService _tenantService;
    private readonly ITenantContext _tenantContext;
    private readonly ILogger<TenantController> _logger;

    public TenantController(
        ITenantService tenantService,
        ITenantContext tenantContext,
        ILogger<TenantController> logger)
    {
        _tenantService = tenantService;
        _tenantContext = tenantContext;
        _logger = logger;
    }

    /// <summary>
    /// Get all tenants with pagination
    /// </summary>
    [HttpGet]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<PagedResult<TenantDto>>> GetTenants(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 50,
        [FromQuery] string? searchTerm = null)
    {
        try
        {
            var result = await _tenantService.GetTenantsAsync(pageNumber, pageSize, searchTerm);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tenants");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get tenant by ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<TenantDto>> GetTenant(Guid id)
    {
        try
        {
            // Check if user can access this tenant
            if (!CanAccessTenant(id))
            {
                return Forbid("Access denied to this tenant");
            }

            var tenant = await _tenantService.GetTenantAsync(id);
            if (tenant == null)
            {
                return NotFound($"Tenant with ID {id} not found");
            }

            return Ok(tenant);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tenant {TenantId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get current tenant information
    /// </summary>
    [HttpGet("current")]
    [RequireTenant]
    public async Task<ActionResult<TenantDto>> GetCurrentTenant()
    {
        try
        {
            if (!_tenantContext.HasTenant)
            {
                return BadRequest("No tenant context available");
            }

            var tenant = await _tenantService.GetTenantAsync(_tenantContext.TenantId!.Value);
            if (tenant == null)
            {
                return NotFound("Current tenant not found");
            }

            return Ok(tenant);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting current tenant");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Create a new tenant
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<object>> CreateTenant([FromBody] CreateTenantDto createTenantDto)
    {
        try
        {
            var tenantId = await _tenantService.CreateTenantAsync(createTenantDto);
            
            var response = new
            {
                TenantId = tenantId,
                Message = "Tenant created successfully",
                Links = new
                {
                    Self = $"/api/tenant/{tenantId}",
                    Configuration = $"/api/tenant/{tenantId}/configuration",
                    Usage = $"/api/tenant/{tenantId}/usage"
                }
            };

            return CreatedAtAction(nameof(GetTenant), new { id = tenantId }, response);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating tenant");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Update tenant details
    /// </summary>
    [HttpPut("{id}")]
    public async Task<ActionResult<object>> UpdateTenant(Guid id, [FromBody] UpdateTenantDto updateTenantDto)
    {
        try
        {
            if (!CanAccessTenant(id))
            {
                return Forbid("Access denied to this tenant");
            }

            var success = await _tenantService.UpdateTenantAsync(id, updateTenantDto);
            if (!success)
            {
                return NotFound($"Tenant with ID {id} not found");
            }

            return Ok(new { Message = "Tenant updated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating tenant {TenantId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Update tenant configuration
    /// </summary>
    [HttpPut("{id}/configuration")]
    public async Task<ActionResult<object>> UpdateTenantConfiguration(
        Guid id, 
        [FromBody] TenantConfigurationDto configuration)
    {
        try
        {
            if (!CanAccessTenant(id))
            {
                return Forbid("Access denied to this tenant");
            }

            var success = await _tenantService.UpdateTenantConfigurationAsync(id, configuration);
            if (!success)
            {
                return NotFound($"Tenant with ID {id} not found");
            }

            return Ok(new { Message = "Tenant configuration updated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating tenant configuration {TenantId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Update tenant plan
    /// </summary>
    [HttpPut("{id}/plan")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<object>> UpdateTenantPlan(Guid id, [FromBody] UpdateTenantPlanDto planDto)
    {
        try
        {
            if (!Enum.TryParse<TenantPlan>(planDto.Plan, true, out var plan))
            {
                return BadRequest($"Invalid plan: {planDto.Plan}");
            }

            var success = await _tenantService.UpdateTenantPlanAsync(id, plan);
            if (!success)
            {
                return NotFound($"Tenant with ID {id} not found");
            }

            return Ok(new { Message = "Tenant plan updated successfully", NewPlan = plan.ToString() });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating tenant plan {TenantId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Deactivate tenant
    /// </summary>
    [HttpPost("{id}/deactivate")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<object>> DeactivateTenant(Guid id, [FromBody] DeactivateTenantDto deactivateDto)
    {
        try
        {
            var success = await _tenantService.DeactivateTenantAsync(id, deactivateDto.Reason);
            if (!success)
            {
                return NotFound($"Tenant with ID {id} not found");
            }

            return Ok(new { Message = "Tenant deactivated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deactivating tenant {TenantId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Reactivate tenant
    /// </summary>
    [HttpPost("{id}/reactivate")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<object>> ReactivateTenant(Guid id)
    {
        try
        {
            var success = await _tenantService.ReactivateTenantAsync(id);
            if (!success)
            {
                return NotFound($"Tenant with ID {id} not found");
            }

            return Ok(new { Message = "Tenant reactivated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reactivating tenant {TenantId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Suspend tenant
    /// </summary>
    [HttpPost("{id}/suspend")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<object>> SuspendTenant(Guid id, [FromBody] SuspendTenantDto suspendDto)
    {
        try
        {
            var success = await _tenantService.SuspendTenantAsync(id, suspendDto.Reason);
            if (!success)
            {
                return NotFound($"Tenant with ID {id} not found");
            }

            return Ok(new { Message = "Tenant suspended successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error suspending tenant {TenantId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Enable compliance standard for tenant
    /// </summary>
    [HttpPost("{id}/compliance-standards/{standard}/enable")]
    public async Task<ActionResult<object>> EnableComplianceStandard(Guid id, string standard)
    {
        try
        {
            if (!CanAccessTenant(id))
            {
                return Forbid("Access denied to this tenant");
            }

            var success = await _tenantService.EnableComplianceStandardAsync(id, standard);
            if (!success)
            {
                return NotFound($"Tenant with ID {id} not found");
            }

            return Ok(new { Message = $"Compliance standard '{standard}' enabled successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error enabling compliance standard for tenant {TenantId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Disable compliance standard for tenant
    /// </summary>
    [HttpPost("{id}/compliance-standards/{standard}/disable")]
    public async Task<ActionResult<object>> DisableComplianceStandard(Guid id, string standard)
    {
        try
        {
            if (!CanAccessTenant(id))
            {
                return Forbid("Access denied to this tenant");
            }

            var success = await _tenantService.DisableComplianceStandardAsync(id, standard);
            if (!success)
            {
                return NotFound($"Tenant with ID {id} not found");
            }

            return Ok(new { Message = $"Compliance standard '{standard}' disabled successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disabling compliance standard for tenant {TenantId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get tenant usage statistics
    /// </summary>
    [HttpGet("{id}/usage")]
    public async Task<ActionResult<TenantUsageDto>> GetTenantUsage(Guid id)
    {
        try
        {
            if (!CanAccessTenant(id))
            {
                return Forbid("Access denied to this tenant");
            }

            var usage = await _tenantService.GetTenantUsageAsync(id);
            return Ok(usage);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tenant usage {TenantId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Validate tenant limits
    /// </summary>
    [HttpPost("{id}/validate-limits")]
    public async Task<ActionResult<TenantLimitValidationResult>> ValidateTenantLimits(
        Guid id, 
        [FromBody] ValidateLimitsDto validateDto)
    {
        try
        {
            if (!CanAccessTenant(id))
            {
                return Forbid("Access denied to this tenant");
            }

            var result = await _tenantService.ValidateTenantLimitsAsync(
                id, 
                validateDto.ResourceType, 
                validateDto.RequestedAmount);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating tenant limits {TenantId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get tenant context information
    /// </summary>
    [HttpGet("context")]
    public ActionResult<object> GetTenantContext()
    {
        var context = new
        {
            HasTenant = _tenantContext.HasTenant,
            TenantId = _tenantContext.TenantId,
            TenantCode = _tenantContext.TenantCode,
            TenantName = _tenantContext.TenantName,
            Features = _tenantContext.HasTenant ? GetAvailableFeatures() : new List<string>(),
            Limits = _tenantContext.HasTenant ? _tenantContext.GetTenantLimits() : null
        };

        return Ok(context);
    }

    // Private helper methods
    private bool CanAccessTenant(Guid tenantId)
    {
        // Admin can access any tenant
        if (User.IsInRole("Admin"))
            return true;

        // TenantAdmin can only access their own tenant
        if (User.IsInRole("TenantAdmin"))
            return _tenantContext.TenantId == tenantId;

        return false;
    }

    private List<string> GetAvailableFeatures()
    {
        var allFeatures = new List<string>
        {
            "AuditLogging",
            "ComplianceReporting",
            "ServiceProviderRatings",
            "RealTimeNotifications",
            "AdvancedAnalytics",
            "MobileAccess",
            "ApiAccess"
        };

        return allFeatures.Where(feature => _tenantContext.HasFeatureAccess(feature)).ToList();
    }
}

// DTOs for tenant operations
public class UpdateTenantPlanDto
{
    public string Plan { get; set; } = string.Empty;
}

public class DeactivateTenantDto
{
    public string Reason { get; set; } = string.Empty;
}

public class SuspendTenantDto
{
    public string Reason { get; set; } = string.Empty;
}

public class ValidateLimitsDto
{
    public string ResourceType { get; set; } = string.Empty;
    public int RequestedAmount { get; set; } = 1;
}
