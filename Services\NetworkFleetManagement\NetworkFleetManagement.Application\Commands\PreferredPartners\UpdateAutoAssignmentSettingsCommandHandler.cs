using MediatR;
using Microsoft.Extensions.Logging;
using NetworkFleetManagement.Domain.Repositories;

namespace NetworkFleetManagement.Application.Commands.PreferredPartners;

public class UpdateAutoAssignmentSettingsCommandHandler : IRequestHandler<UpdateAutoAssignmentSettingsCommand, bool>
{
    private readonly IPreferredPartnerRepository _preferredPartnerRepository;
    private readonly ILogger<UpdateAutoAssignmentSettingsCommandHandler> _logger;

    public UpdateAutoAssignmentSettingsCommandHandler(
        IPreferredPartnerRepository preferredPartnerRepository,
        ILogger<UpdateAutoAssignmentSettingsCommandHandler> logger)
    {
        _preferredPartnerRepository = preferredPartnerRepository;
        _logger = logger;
    }

    public async Task<bool> Handle(UpdateAutoAssignmentSettingsCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var partner = await _preferredPartnerRepository.GetByIdAsync(request.PreferredPartnerId, cancellationToken);
            
            if (partner == null || partner.UserId != request.UserId)
            {
                _logger.LogWarning("Preferred partner {Id} not found or user {UserId} doesn't have permission to update it", 
                    request.PreferredPartnerId, request.UserId);
                return false;
            }

            partner.UpdateAutoAssignSettings(
                request.AutoAssignEnabled,
                request.AutoAssignThreshold,
                request.PreferredRoutes,
                request.PreferredLoadTypes,
                request.ExcludedRoutes,
                request.ExcludedLoadTypes);

            await _preferredPartnerRepository.UpdateAsync(partner, cancellationToken);

            _logger.LogInformation("Auto-assignment settings updated for preferred partner {Id} by user {UserId}", 
                request.PreferredPartnerId, request.UserId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating auto-assignment settings for preferred partner {Id}", 
                request.PreferredPartnerId);
            throw;
        }
    }
}
