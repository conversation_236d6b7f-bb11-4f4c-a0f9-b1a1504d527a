-- Order Management Service Database Setup
-- This script creates the database and schema for the Order Management Service

-- Create database if it doesn't exist
SELECT 'CREATE DATABASE "TLI_OrderManagement"'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'TLI_OrderManagement')\gexec

-- Connect to the database
\c TLI_OrderManagement;

-- Create schema
CREATE SCHEMA IF NOT EXISTS order_management;

-- Set search path
SET search_path TO order_management, public;

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Grant permissions to timescale user
GRANT ALL PRIVILEGES ON SCHEMA order_management TO timescale;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA order_management TO timescale;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA order_management TO timescale;

-- Set default privileges for future tables
ALTER DEFAULT PRIVILEGES IN SCHEMA order_management GRANT ALL ON TABLES TO timescale;
ALTER DEFAULT PRIVILEGES IN SCHEMA order_management GRANT ALL ON SEQUENCES TO timescale;

COMMIT;
