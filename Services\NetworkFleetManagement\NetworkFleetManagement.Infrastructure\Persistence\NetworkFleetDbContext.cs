using Microsoft.EntityFrameworkCore;
using NetworkFleetManagement.Domain.Entities;
using NetworkFleetManagement.Infrastructure.Persistence.Configurations;

namespace NetworkFleetManagement.Infrastructure.Persistence;

public class NetworkFleetDbContext : DbContext
{
    public NetworkFleetDbContext(DbContextOptions<NetworkFleetDbContext> options) : base(options)
    {
    }

    public DbSet<Carrier> Carriers { get; set; }
    public DbSet<Vehicle> Vehicles { get; set; }
    public DbSet<Driver> Drivers { get; set; }
    public DbSet<BrokerCarrierNetwork> BrokerCarrierNetworks { get; set; }
    public DbSet<PreferredPartner> PreferredPartners { get; set; }
    public DbSet<CarrierDocument> CarrierDocuments { get; set; }
    public DbSet<VehicleDocument> VehicleDocuments { get; set; }
    public DbSet<DriverDocument> DriverDocuments { get; set; }
    public DbSet<DriverVehicleAssignment> DriverVehicleAssignments { get; set; }
    public DbSet<VehicleMaintenanceRecord> VehicleMaintenanceRecords { get; set; }
    public DbSet<NetworkPerformanceRecord> NetworkPerformanceRecords { get; set; }
    public DbSet<FeatureFlag> FeatureFlags { get; set; }
    public DbSet<VehicleTypeMaster> VehicleTypeMasters { get; set; }
    public DbSet<UsageAnalyticsRecord> UsageAnalyticsRecords { get; set; }
    public DbSet<AnalyticsAggregation> AnalyticsAggregations { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Apply configurations
        modelBuilder.ApplyConfiguration(new CarrierConfiguration());
        modelBuilder.ApplyConfiguration(new VehicleConfiguration());
        modelBuilder.ApplyConfiguration(new DriverConfiguration());
        modelBuilder.ApplyConfiguration(new BrokerCarrierNetworkConfiguration());
        modelBuilder.ApplyConfiguration(new PreferredPartnerConfiguration());
        modelBuilder.ApplyConfiguration(new CarrierDocumentConfiguration());
        modelBuilder.ApplyConfiguration(new VehicleDocumentConfiguration());
        modelBuilder.ApplyConfiguration(new DriverDocumentConfiguration());
        modelBuilder.ApplyConfiguration(new DriverVehicleAssignmentConfiguration());
        modelBuilder.ApplyConfiguration(new VehicleMaintenanceRecordConfiguration());
        modelBuilder.ApplyConfiguration(new NetworkPerformanceRecordConfiguration());
        modelBuilder.ApplyConfiguration(new FeatureFlagConfiguration());
        modelBuilder.ApplyConfiguration(new VehicleTypeMasterConfiguration());

        // Configure schema
        modelBuilder.HasDefaultSchema("networkfleet");
    }
}
