
using Shared.Domain.Common;
using MobileWorkflow.Domain.Events;


namespace MobileWorkflow.Domain.Entities;

public class MobileSession : AggregateRoot
{
    public Guid UserId { get; private set; }
    public Guid MobileAppId { get; private set; }
    public string DeviceId { get; private set; }
    public string DeviceInfo { get; private set; }
    public string AppVersion { get; private set; }
    public string Platform { get; private set; }
    public string OSVersion { get; private set; }
    public DateTime StartTime { get; private set; }
    public DateTime? EndTime { get; private set; }
    public bool IsActive { get; private set; }
    public string? LastKnownLocation { get; private set; }
    public Dictionary<string, object> SessionData { get; private set; }
    public bool IsOfflineCapable { get; private set; }
    public DateTime LastSyncTime { get; private set; }
    public int OfflineActionsCount { get; private set; }

    // Navigation properties
    public virtual MobileApp MobileApp { get; private set; } = null!;
    public virtual ICollection<OfflineData> OfflineData { get; private set; } = new List<OfflineData>();

    private MobileSession() { } // EF Core

    public MobileSession(
        Guid userId,
        Guid mobileAppId,
        string deviceId,
        string deviceInfo,
        string appVersion,
        string platform,
        string osVersion,
        bool isOfflineCapable)
    {
        UserId = userId;
        MobileAppId = mobileAppId;
        DeviceId = deviceId ?? throw new ArgumentNullException(nameof(deviceId));
        DeviceInfo = deviceInfo ?? throw new ArgumentNullException(nameof(deviceInfo));
        AppVersion = appVersion ?? throw new ArgumentNullException(nameof(appVersion));
        Platform = platform ?? throw new ArgumentNullException(nameof(platform));
        OSVersion = osVersion ?? throw new ArgumentNullException(nameof(osVersion));
        StartTime = DateTime.UtcNow;
        IsActive = true;
        SessionData = new Dictionary<string, object>();
        IsOfflineCapable = isOfflineCapable;
        LastSyncTime = DateTime.UtcNow;
        OfflineActionsCount = 0;

        AddDomainEvent(new MobileSessionStartedEvent(Id, UserId, Platform, DeviceId));
    }

    public void UpdateLocation(string location)
    {
        LastKnownLocation = location;
        UpdateSessionData("last_location_update", DateTime.UtcNow);
    }

    public void UpdateSessionData(string key, object value)
    {
        SessionData[key] = value;
        AddDomainEvent(new MobileSessionDataUpdatedEvent(Id, UserId, key));
    }

    public void RecordOfflineAction()
    {
        OfflineActionsCount++;
        UpdateSessionData("offline_actions_count", OfflineActionsCount);
    }

    public void SyncCompleted()
    {
        LastSyncTime = DateTime.UtcNow;
        OfflineActionsCount = 0; // Reset after successful sync
        UpdateSessionData("last_sync", LastSyncTime);
        AddDomainEvent(new MobileSessionSyncCompletedEvent(Id, UserId, LastSyncTime));
    }

    public void EndSession()
    {
        if (!IsActive)
            throw new InvalidOperationException("Session is already ended");

        EndTime = DateTime.UtcNow;
        IsActive = false;

        var duration = EndTime.Value - StartTime;
        AddDomainEvent(new MobileSessionEndedEvent(Id, UserId, duration.TotalMinutes));
    }

    public TimeSpan GetSessionDuration()
    {
        var endTime = EndTime ?? DateTime.UtcNow;
        return endTime - StartTime;
    }
}



