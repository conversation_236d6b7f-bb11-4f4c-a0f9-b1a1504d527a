using Shared.Domain.Common;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Domain.Events;

// Trip Ledger Events
public record TripLedgerCreatedEvent(Guid TripLedgerId, Guid TripId, Guid OrderId, Money TotalAmount) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record TripLedgerEntryAddedEvent(Guid TripLedgerId, Guid EntryId, string EntryType, Money Amount, string Description) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record TripLedgerMilestoneAddedEvent(Guid TripLedgerId, Guid MilestoneId, string MilestoneType, Money Amount, DateTime DueDate) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record TripLedgerMilestoneCompletedEvent(Guid TripLedgerId, Guid MilestoneId, string MilestoneType, Money Amount, DateTime CompletedAt) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record TripLedgerAdjustmentAddedEvent(Guid TripLedgerId, Guid AdjustmentId, string AdjustmentType, Money Amount, string Reason) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record TripLedgerCompletedEvent(Guid TripLedgerId, Guid TripId, Money FinalAmount, DateTime CompletedAt) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record TripLedgerSettledEvent(Guid TripLedgerId, Guid TripId, Money SettledAmount, DateTime SettledAt, string SettlementReference) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record TripLedgerClosedEvent(Guid TripLedgerId, Guid TripId, Money FinalAmount, DateTime ClosedAt, string ClosureReason) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// Trip Ledger Entry Events
public record TripLedgerEntryUpdatedEvent(Guid TripLedgerId, Guid EntryId, Money PreviousAmount, Money NewAmount, string UpdateReason) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record TripLedgerEntryRemovedEvent(Guid TripLedgerId, Guid EntryId, Money Amount, string RemovalReason) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// Trip Ledger Balance Events
public record TripLedgerBalanceUpdatedEvent(Guid TripLedgerId, Money PreviousBalance, Money NewBalance, string UpdateReason) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record TripLedgerReconciliationCompletedEvent(Guid TripLedgerId, Money ExpectedAmount, Money ActualAmount, Money VarianceAmount, DateTime ReconciledAt) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}
