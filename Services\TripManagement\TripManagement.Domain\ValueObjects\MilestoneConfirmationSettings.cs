using Shared.Domain.ValueObjects;

namespace TripManagement.Domain.ValueObjects;

/// <summary>
/// Settings for milestone confirmation
/// </summary>
public class MilestoneConfirmationSettings : ValueObject
{
    public bool RequirePhotoConfirmation { get; init; }
    public bool RequireSignature { get; init; }
    public bool RequireGpsLocation { get; init; }
    public bool RequireOtpVerification { get; init; }
    public int MaxConfirmationTimeMinutes { get; init; }
    public List<string> AllowedConfirmationMethods { get; init; } = new();
    public bool EnableAutoConfirmation { get; init; }
    public int AutoConfirmationDelayMinutes { get; init; }

    public MilestoneConfirmationSettings(
        bool requirePhotoConfirmation = false,
        bool requireSignature = false,
        bool requireGpsLocation = true,
        bool requireOtpVerification = false,
        int maxConfirmationTimeMinutes = 30,
        List<string>? allowedConfirmationMethods = null,
        bool enableAutoConfirmation = false,
        int autoConfirmationDelayMinutes = 0)
    {
        RequirePhotoConfirmation = requirePhotoConfirmation;
        RequireSignature = requireSignature;
        RequireGpsLocation = requireGpsLocation;
        RequireOtpVerification = requireOtpVerification;
        MaxConfirmationTimeMinutes = maxConfirmationTimeMinutes;
        AllowedConfirmationMethods = allowedConfirmationMethods ?? new List<string>();
        EnableAutoConfirmation = enableAutoConfirmation;
        AutoConfirmationDelayMinutes = autoConfirmationDelayMinutes;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return RequirePhotoConfirmation;
        yield return RequireSignature;
        yield return RequireGpsLocation;
        yield return RequireOtpVerification;
        yield return MaxConfirmationTimeMinutes;
        yield return EnableAutoConfirmation;
        yield return AutoConfirmationDelayMinutes;
        
        foreach (var method in AllowedConfirmationMethods.OrderBy(x => x))
        {
            yield return method;
        }
    }
}
