using AnalyticsBIService.Domain.Enums;

namespace AnalyticsBIService.Infrastructure.Services;

/// <summary>
/// Interface for data warehouse integration service
/// </summary>
public interface IDataWarehouseService
{
    /// <summary>
    /// Create a new data warehouse connection
    /// </summary>
    Task<DataWarehouseConnectionDto> CreateConnectionAsync(CreateDataWarehouseConnectionRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get data warehouse connection by ID
    /// </summary>
    Task<DataWarehouseConnectionDto?> GetConnectionAsync(Guid connectionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all connections for a user
    /// </summary>
    Task<List<DataWarehouseConnectionDto>> GetUserConnectionsAsync(Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Test data warehouse connection
    /// </summary>
    Task<ConnectionTestResult> TestConnectionAsync(Guid connectionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Synchronize data from warehouse
    /// </summary>
    Task<DataSyncResultDto> SynchronizeDataAsync(Guid connectionId, DataSyncOptionsDto options, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get available schemas from warehouse
    /// </summary>
    Task<List<SchemaDto>> GetSchemasAsync(Guid connectionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get tables from a specific schema
    /// </summary>
    Task<List<TableDto>> GetTablesAsync(Guid connectionId, string schemaName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get table metadata including columns and relationships
    /// </summary>
    Task<TableMetadataDto> GetTableMetadataAsync(Guid connectionId, string schemaName, string tableName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Execute query against data warehouse
    /// </summary>
    Task<QueryResultDto> ExecuteQueryAsync(Guid connectionId, string query, QueryOptionsDto? options = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Create or update schema mapping
    /// </summary>
    Task<SchemaMappingDto> CreateSchemaMappingAsync(CreateSchemaMappingRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get schema mappings for a connection
    /// </summary>
    Task<List<SchemaMappingDto>> GetSchemaMappingsAsync(Guid connectionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Schedule data synchronization
    /// </summary>
    Task<DataSyncScheduleDto> ScheduleSynchronizationAsync(ScheduleDataSyncRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get synchronization history
    /// </summary>
    Task<List<DataSyncHistoryDto>> GetSyncHistoryAsync(Guid connectionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Optimize query performance
    /// </summary>
    Task<QueryOptimizationResult> OptimizeQueryAsync(string query, Guid connectionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get data warehouse statistics
    /// </summary>
    Task<DataWarehouseStatsDto> GetWarehouseStatisticsAsync(Guid connectionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update connection configuration
    /// </summary>
    Task<bool> UpdateConnectionAsync(Guid connectionId, UpdateDataWarehouseConnectionRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete data warehouse connection
    /// </summary>
    Task<bool> DeleteConnectionAsync(Guid connectionId, Guid userId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Data warehouse connection data transfer object
/// </summary>
public class DataWarehouseConnectionDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string ConnectionType { get; set; } = string.Empty;
    public string Server { get; set; } = string.Empty;
    public string Database { get; set; } = string.Empty;
    public int Port { get; set; }
    public string Username { get; set; } = string.Empty;
    public bool UseIntegratedSecurity { get; set; }
    public Dictionary<string, object> ConnectionProperties { get; set; } = new();
    public string Status { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public Guid CreatedBy { get; set; }
    public bool IsActive { get; set; }
    public DateTime? LastConnected { get; set; }
    public DateTime? LastSynchronized { get; set; }
}

/// <summary>
/// Connection test result data transfer object
/// </summary>
public class ConnectionTestResult
{
    public bool IsSuccessful { get; set; }
    public string? ErrorMessage { get; set; }
    public TimeSpan ResponseTime { get; set; }
    public string ServerVersion { get; set; } = string.Empty;
    public Dictionary<string, object> ConnectionInfo { get; set; } = new();
    public List<string> AvailableSchemas { get; set; } = new();
}

/// <summary>
/// Data synchronization result data transfer object
/// </summary>
public class DataSyncResultDto
{
    public Guid SyncId { get; set; }
    public Guid ConnectionId { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public TimeSpan? Duration { get; set; }
    public int TablesProcessed { get; set; }
    public long RecordsProcessed { get; set; }
    public long RecordsInserted { get; set; }
    public long RecordsUpdated { get; set; }
    public long RecordsDeleted { get; set; }
    public List<TableSyncResultDto> TableResults { get; set; } = new();
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object> Metrics { get; set; } = new();
}

/// <summary>
/// Table synchronization result data transfer object
/// </summary>
public class TableSyncResultDto
{
    public string SchemaName { get; set; } = string.Empty;
    public string TableName { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public long RecordsProcessed { get; set; }
    public long RecordsInserted { get; set; }
    public long RecordsUpdated { get; set; }
    public long RecordsDeleted { get; set; }
    public TimeSpan Duration { get; set; }
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// Data synchronization options data transfer object
/// </summary>
public class DataSyncOptionsDto
{
    public List<string> IncludedSchemas { get; set; } = new();
    public List<string> ExcludedSchemas { get; set; } = new();
    public List<string> IncludedTables { get; set; } = new();
    public List<string> ExcludedTables { get; set; } = new();
    public string SyncMode { get; set; } = "Incremental";
    public DateTime? SyncFromDate { get; set; }
    public DateTime? SyncToDate { get; set; }
    public int BatchSize { get; set; } = 1000;
    public bool ValidateData { get; set; } = true;
    public Dictionary<string, object> CustomOptions { get; set; } = new();
}

/// <summary>
/// Schema data transfer object
/// </summary>
public class SchemaDto
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Owner { get; set; } = string.Empty;
    public int TableCount { get; set; }
    public int ViewCount { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public Dictionary<string, object> Properties { get; set; } = new();
}

/// <summary>
/// Table data transfer object
/// </summary>
public class TableDto
{
    public string Name { get; set; } = string.Empty;
    public string Schema { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public long RowCount { get; set; }
    public long SizeInBytes { get; set; }
    public int ColumnCount { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public List<string> PrimaryKeys { get; set; } = new();
    public List<string> Indexes { get; set; } = new();
}

/// <summary>
/// Table metadata data transfer object
/// </summary>
public class TableMetadataDto
{
    public string SchemaName { get; set; } = string.Empty;
    public string TableName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<ColumnMetadataDto> Columns { get; set; } = new();
    public List<IndexMetadataDto> Indexes { get; set; } = new();
    public List<ForeignKeyDto> ForeignKeys { get; set; } = new();
    public List<string> PrimaryKeys { get; set; } = new();
    public TableStatisticsDto Statistics { get; set; } = new();
}

/// <summary>
/// Column metadata data transfer object
/// </summary>
public class ColumnMetadataDto
{
    public string Name { get; set; } = string.Empty;
    public string DataType { get; set; } = string.Empty;
    public bool IsNullable { get; set; }
    public bool IsPrimaryKey { get; set; }
    public bool IsForeignKey { get; set; }
    public int? MaxLength { get; set; }
    public int? Precision { get; set; }
    public int? Scale { get; set; }
    public string? DefaultValue { get; set; }
    public string Description { get; set; } = string.Empty;
    public int OrdinalPosition { get; set; }
}

/// <summary>
/// Index metadata data transfer object
/// </summary>
public class IndexMetadataDto
{
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public bool IsUnique { get; set; }
    public bool IsPrimary { get; set; }
    public List<string> Columns { get; set; } = new();
    public string? FilterCondition { get; set; }
}

/// <summary>
/// Foreign key data transfer object
/// </summary>
public class ForeignKeyDto
{
    public string Name { get; set; } = string.Empty;
    public string Column { get; set; } = string.Empty;
    public string ReferencedSchema { get; set; } = string.Empty;
    public string ReferencedTable { get; set; } = string.Empty;
    public string ReferencedColumn { get; set; } = string.Empty;
    public string UpdateRule { get; set; } = string.Empty;
    public string DeleteRule { get; set; } = string.Empty;
}

/// <summary>
/// Table statistics data transfer object
/// </summary>
public class TableStatisticsDto
{
    public long RowCount { get; set; }
    public long SizeInBytes { get; set; }
    public long IndexSizeInBytes { get; set; }
    public DateTime LastUpdated { get; set; }
    public Dictionary<string, object> AdditionalStats { get; set; } = new();
}

/// <summary>
/// Query result data transfer object
/// </summary>
public class QueryResultDto
{
    public List<Dictionary<string, object>> Data { get; set; } = new();
    public List<QueryColumnDto> Columns { get; set; } = new();
    public int RowCount { get; set; }
    public TimeSpan ExecutionTime { get; set; }
    public string? ErrorMessage { get; set; }
    public bool IsSuccessful { get; set; }
    public QueryMetricsDto Metrics { get; set; } = new();
}

/// <summary>
/// Query column data transfer object
/// </summary>
public class QueryColumnDto
{
    public string Name { get; set; } = string.Empty;
    public string DataType { get; set; } = string.Empty;
    public bool IsNullable { get; set; }
    public int OrdinalPosition { get; set; }
}

/// <summary>
/// Query metrics data transfer object
/// </summary>
public class QueryMetricsDto
{
    public TimeSpan CompilationTime { get; set; }
    public TimeSpan ExecutionTime { get; set; }
    public long LogicalReads { get; set; }
    public long PhysicalReads { get; set; }
    public long MemoryUsage { get; set; }
    public decimal CpuTime { get; set; }
}

/// <summary>
/// Query options data transfer object
/// </summary>
public class QueryOptionsDto
{
    public int? TimeoutSeconds { get; set; }
    public int? MaxRows { get; set; }
    public bool IncludeExecutionPlan { get; set; }
    public bool OptimizeQuery { get; set; }
    public Dictionary<string, object> Parameters { get; set; } = new();
}

/// <summary>
/// Schema mapping data transfer object
/// </summary>
public class SchemaMappingDto
{
    public Guid Id { get; set; }
    public Guid ConnectionId { get; set; }
    public string SourceSchema { get; set; } = string.Empty;
    public string SourceTable { get; set; } = string.Empty;
    public string TargetSchema { get; set; } = string.Empty;
    public string TargetTable { get; set; } = string.Empty;
    public List<FieldMappingDto> FieldMappings { get; set; } = new();
    public string MappingType { get; set; } = string.Empty;
    public Dictionary<string, object> TransformationRules { get; set; } = new();
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

/// <summary>
/// Field mapping data transfer object
/// </summary>
public class FieldMappingDto
{
    public string SourceField { get; set; } = string.Empty;
    public string TargetField { get; set; } = string.Empty;
    public string SourceDataType { get; set; } = string.Empty;
    public string TargetDataType { get; set; } = string.Empty;
    public string? TransformationFunction { get; set; }
    public Dictionary<string, object> TransformationParameters { get; set; } = new();
    public bool IsRequired { get; set; }
}

/// <summary>
/// Data synchronization schedule data transfer object
/// </summary>
public class DataSyncScheduleDto
{
    public Guid Id { get; set; }
    public Guid ConnectionId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string ScheduleType { get; set; } = string.Empty;
    public string CronExpression { get; set; } = string.Empty;
    public DateTime NextRunDate { get; set; }
    public DateTime? LastRunDate { get; set; }
    public DataSyncOptionsDto SyncOptions { get; set; } = new();
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
}

/// <summary>
/// Data synchronization history data transfer object
/// </summary>
public class DataSyncHistoryDto
{
    public Guid SyncId { get; set; }
    public DateTime StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string Status { get; set; } = string.Empty;
    public TimeSpan? Duration { get; set; }
    public long RecordsProcessed { get; set; }
    public string? ErrorMessage { get; set; }
    public string TriggerType { get; set; } = string.Empty;
}

/// <summary>
/// Query optimization result data transfer object
/// </summary>
public class QueryOptimizationResult
{
    public string OriginalQuery { get; set; } = string.Empty;
    public string OptimizedQuery { get; set; } = string.Empty;
    public List<string> Recommendations { get; set; } = new();
    public QueryPerformanceComparisonDto PerformanceComparison { get; set; } = new();
    public List<IndexRecommendationDto> IndexRecommendations { get; set; } = new();
}

/// <summary>
/// Query performance comparison data transfer object
/// </summary>
public class QueryPerformanceComparisonDto
{
    public TimeSpan OriginalExecutionTime { get; set; }
    public TimeSpan OptimizedExecutionTime { get; set; }
    public decimal PerformanceImprovement { get; set; }
    public long OriginalLogicalReads { get; set; }
    public long OptimizedLogicalReads { get; set; }
    public decimal CpuTimeReduction { get; set; }
}

/// <summary>
/// Index recommendation data transfer object
/// </summary>
public class IndexRecommendationDto
{
    public string TableName { get; set; } = string.Empty;
    public List<string> Columns { get; set; } = new();
    public string IndexType { get; set; } = string.Empty;
    public decimal EstimatedImpact { get; set; }
    public string Reason { get; set; } = string.Empty;
    public string CreateStatement { get; set; } = string.Empty;
}

/// <summary>
/// Data warehouse statistics data transfer object
/// </summary>
public class DataWarehouseStatsDto
{
    public Guid ConnectionId { get; set; }
    public int TotalSchemas { get; set; }
    public int TotalTables { get; set; }
    public int TotalViews { get; set; }
    public long TotalRows { get; set; }
    public long TotalSizeInBytes { get; set; }
    public DateTime LastSynchronized { get; set; }
    public List<SchemaStatsDto> SchemaStatistics { get; set; } = new();
    public List<TableStatsDto> LargestTables { get; set; } = new();
    public Dictionary<string, object> DatabaseInfo { get; set; } = new();
}

/// <summary>
/// Schema statistics data transfer object
/// </summary>
public class SchemaStatsDto
{
    public string SchemaName { get; set; } = string.Empty;
    public int TableCount { get; set; }
    public int ViewCount { get; set; }
    public long TotalRows { get; set; }
    public long SizeInBytes { get; set; }
}

/// <summary>
/// Table statistics data transfer object
/// </summary>
public class TableStatsDto
{
    public string SchemaName { get; set; } = string.Empty;
    public string TableName { get; set; } = string.Empty;
    public long RowCount { get; set; }
    public long SizeInBytes { get; set; }
    public DateTime LastUpdated { get; set; }
}

// Request DTOs
/// <summary>
/// Create data warehouse connection request
/// </summary>
public class CreateDataWarehouseConnectionRequest
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string ConnectionType { get; set; } = string.Empty;
    public string Server { get; set; } = string.Empty;
    public string Database { get; set; } = string.Empty;
    public int Port { get; set; }
    public string Username { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public bool UseIntegratedSecurity { get; set; }
    public Dictionary<string, object> ConnectionProperties { get; set; } = new();
    public Guid UserId { get; set; }
}

/// <summary>
/// Update data warehouse connection request
/// </summary>
public class UpdateDataWarehouseConnectionRequest
{
    public string? Name { get; set; }
    public string? Description { get; set; }
    public string? Server { get; set; }
    public string? Database { get; set; }
    public int? Port { get; set; }
    public string? Username { get; set; }
    public string? Password { get; set; }
    public bool? UseIntegratedSecurity { get; set; }
    public Dictionary<string, object>? ConnectionProperties { get; set; }
    public bool? IsActive { get; set; }
}

/// <summary>
/// Create schema mapping request
/// </summary>
public class CreateSchemaMappingRequest
{
    public Guid ConnectionId { get; set; }
    public string SourceSchema { get; set; } = string.Empty;
    public string SourceTable { get; set; } = string.Empty;
    public string TargetSchema { get; set; } = string.Empty;
    public string TargetTable { get; set; } = string.Empty;
    public List<FieldMappingDto> FieldMappings { get; set; } = new();
    public string MappingType { get; set; } = string.Empty;
    public Dictionary<string, object> TransformationRules { get; set; } = new();
    public Guid UserId { get; set; }
}

/// <summary>
/// Schedule data synchronization request
/// </summary>
public class ScheduleDataSyncRequest
{
    public Guid ConnectionId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string ScheduleType { get; set; } = string.Empty;
    public string CronExpression { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public DataSyncOptionsDto SyncOptions { get; set; } = new();
    public bool IsActive { get; set; } = true;
}
