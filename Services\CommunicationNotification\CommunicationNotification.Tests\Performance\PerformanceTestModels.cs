namespace CommunicationNotification.Tests.Performance;

/// <summary>
/// Performance test configuration
/// </summary>
public class PerformanceTestConfiguration
{
    public LoadTestConfiguration LoadTest { get; set; } = new();
    public StressTestConfiguration StressTest { get; set; } = new();
    public EnduranceTestConfiguration EnduranceTest { get; set; } = new();
    public SpikeTestConfiguration SpikeTest { get; set; } = new();
    public VolumeTestConfiguration VolumeTest { get; set; } = new();
    public ConcurrentUserTestConfiguration ConcurrentUserTest { get; set; } = new();
    public string BaseUrl { get; set; } = "https://localhost:7001";
    public int TimeoutSeconds { get; set; } = 30;
    public bool GenerateReports { get; set; } = true;
    public string ReportOutputPath { get; set; } = "performance-reports";
}

public class LoadTestConfiguration
{
    public int RequestsPerSecond { get; set; } = 50;
    public TimeSpan Duration { get; set; } = TimeSpan.FromMinutes(5);
    public double AcceptableErrorRate { get; set; } = 0.01; // 1%
    public double AcceptableResponseTime { get; set; } = 500; // 500ms
}

public class StressTestConfiguration
{
    public int MaxRequestsPerSecond { get; set; } = 200;
    public TimeSpan Duration { get; set; } = TimeSpan.FromMinutes(10);
    public double AcceptableErrorRate { get; set; } = 0.05; // 5%
    public double AcceptableResponseTime { get; set; } = 1000; // 1000ms
}

public class EnduranceTestConfiguration
{
    public int RequestsPerSecond { get; set; } = 30;
    public TimeSpan Duration { get; set; } = TimeSpan.FromHours(1);
    public double AcceptableMemoryIncrease { get; set; } = 0.1; // 10%
    public double AcceptablePerformanceDegradation { get; set; } = 0.05; // 5%
}

public class SpikeTestConfiguration
{
    public int NormalLoad { get; set; } = 10;
    public int SpikeLoad { get; set; } = 100;
    public TimeSpan SpikeDuration { get; set; } = TimeSpan.FromSeconds(30);
    public TimeSpan RecoveryTime { get; set; } = TimeSpan.FromMinutes(2);
}

public class VolumeTestConfiguration
{
    public int LargePayloadSizeKB { get; set; } = 5;
    public int BulkNotificationSize { get; set; } = 1000;
    public int RequestsPerSecond { get; set; } = 5;
    public TimeSpan Duration { get; set; } = TimeSpan.FromMinutes(3);
}

public class ConcurrentUserTestConfiguration
{
    public int UsersPerSecond { get; set; } = 25;
    public TimeSpan Duration { get; set; } = TimeSpan.FromMinutes(10);
    public double AcceptableFailureRate { get; set; } = 0.02; // 2%
    public double MinUserExperienceScore { get; set; } = 80;
}

/// <summary>
/// Performance test results container
/// </summary>
public class PerformanceTestResults
{
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public TimeSpan TotalDuration { get; set; }
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public PerformanceTestConfiguration TestConfiguration { get; set; } = new();
    public LoadTestResults? LoadTestResults { get; set; }
    public StressTestResults? StressTestResults { get; set; }
    public EnduranceTestResults? EnduranceTestResults { get; set; }
    public SpikeTestResults? SpikeTestResults { get; set; }
    public VolumeTestResults? VolumeTestResults { get; set; }
    public ConcurrentUserTestResults? ConcurrentUserTestResults { get; set; }
    public PerformanceReport? PerformanceReport { get; set; }
}

/// <summary>
/// Load test results
/// </summary>
public class LoadTestResults
{
    public string TestName { get; set; } = string.Empty;
    public TimeSpan Duration { get; set; }
    public int RequestsPerSecond { get; set; }
    public long TotalRequests { get; set; }
    public long SuccessfulRequests { get; set; }
    public long FailedRequests { get; set; }
    public double AverageResponseTime { get; set; }
    public double MaxResponseTime { get; set; }
    public double MinResponseTime { get; set; }
    public double ThroughputPerSecond { get; set; }
    public double ErrorRate { get; set; }
    public bool Success { get; set; }
}

/// <summary>
/// Stress test results
/// </summary>
public class StressTestResults
{
    public string TestName { get; set; } = string.Empty;
    public TimeSpan Duration { get; set; }
    public int MaxRequestsPerSecond { get; set; }
    public long TotalRequests { get; set; }
    public long SuccessfulRequests { get; set; }
    public long FailedRequests { get; set; }
    public double AverageResponseTime { get; set; }
    public double MaxResponseTime { get; set; }
    public double BreakingPoint { get; set; }
    public ResourceUtilization ResourceUtilization { get; set; } = new();
    public bool Success { get; set; }
}

/// <summary>
/// Endurance test results
/// </summary>
public class EnduranceTestResults
{
    public string TestName { get; set; } = string.Empty;
    public TimeSpan Duration { get; set; }
    public int RequestsPerSecond { get; set; }
    public long TotalRequests { get; set; }
    public long SuccessfulRequests { get; set; }
    public long FailedRequests { get; set; }
    public double AverageResponseTime { get; set; }
    public bool MemoryLeakDetected { get; set; }
    public long MemoryUsageBefore { get; set; }
    public long MemoryUsageAfter { get; set; }
    public double PerformanceDegradation { get; set; }
    public bool Success { get; set; }
}

/// <summary>
/// Spike test results
/// </summary>
public class SpikeTestResults
{
    public string TestName { get; set; } = string.Empty;
    public int NormalLoad { get; set; }
    public int SpikeLoad { get; set; }
    public TimeSpan SpikeDuration { get; set; }
    public long TotalRequests { get; set; }
    public long SuccessfulRequests { get; set; }
    public long FailedRequests { get; set; }
    public TimeSpan RecoveryTime { get; set; }
    public double SystemStability { get; set; }
    public bool Success { get; set; }
}

/// <summary>
/// Volume test results
/// </summary>
public class VolumeTestResults
{
    public string TestName { get; set; } = string.Empty;
    public int LargePayloadSize { get; set; }
    public int BulkNotificationSize { get; set; }
    public long TotalRequests { get; set; }
    public long SuccessfulRequests { get; set; }
    public long FailedRequests { get; set; }
    public double AverageResponseTime { get; set; }
    public double DataThroughput { get; set; }
    public bool Success { get; set; }
}

/// <summary>
/// Concurrent user test results
/// </summary>
public class ConcurrentUserTestResults
{
    public string TestName { get; set; } = string.Empty;
    public int ConcurrentUsers { get; set; }
    public TimeSpan Duration { get; set; }
    public long TotalUserJourneys { get; set; }
    public long SuccessfulJourneys { get; set; }
    public long FailedJourneys { get; set; }
    public double AverageJourneyTime { get; set; }
    public double UserExperienceScore { get; set; }
    public bool Success { get; set; }
}

/// <summary>
/// Resource utilization metrics
/// </summary>
public class ResourceUtilization
{
    public double CpuUsagePercent { get; set; }
    public long MemoryUsageMB { get; set; }
    public int ThreadCount { get; set; }
    public int HandleCount { get; set; }
}

/// <summary>
/// Performance metrics container
/// </summary>
public class PerformanceMetrics
{
    public string Name { get; set; } = string.Empty;
    public double Value { get; set; }
    public string Unit { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public Dictionary<string, object> Tags { get; set; } = new();
}

/// <summary>
/// Performance report
/// </summary>
public class PerformanceReport
{
    public DateTime GeneratedAt { get; set; }
    public TimeSpan TestDuration { get; set; }
    public double OverallScore { get; set; }
    public string Summary { get; set; } = string.Empty;
    public List<string> Recommendations { get; set; } = new();
    public Dictionary<string, double> Benchmarks { get; set; } = new();
    public Dictionary<string, object> TrendAnalysis { get; set; } = new();
}

/// <summary>
/// Performance test scenario
/// </summary>
public class PerformanceTestScenario
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public TestType Type { get; set; }
    public int RequestsPerSecond { get; set; }
    public TimeSpan Duration { get; set; }
    public Dictionary<string, object> Parameters { get; set; } = new();
    public List<TestStep> Steps { get; set; } = new();
}

/// <summary>
/// Test step definition
/// </summary>
public class TestStep
{
    public string Name { get; set; } = string.Empty;
    public string Method { get; set; } = "GET";
    public string Endpoint { get; set; } = string.Empty;
    public object? Payload { get; set; }
    public Dictionary<string, string> Headers { get; set; } = new();
    public TimeSpan? Delay { get; set; }
    public double Weight { get; set; } = 1.0;
}

/// <summary>
/// Test type enumeration
/// </summary>
public enum TestType
{
    Load = 1,
    Stress = 2,
    Endurance = 3,
    Spike = 4,
    Volume = 5,
    ConcurrentUser = 6,
    Smoke = 7,
    Baseline = 8
}

/// <summary>
/// Performance benchmark
/// </summary>
public class PerformanceBenchmark
{
    public string Name { get; set; } = string.Empty;
    public double Value { get; set; }
    public string Unit { get; set; } = string.Empty;
    public BenchmarkType Type { get; set; }
    public DateTime CreatedAt { get; set; }
    public string Environment { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
}

/// <summary>
/// Benchmark type enumeration
/// </summary>
public enum BenchmarkType
{
    Throughput = 1,
    ResponseTime = 2,
    ErrorRate = 3,
    ResourceUtilization = 4,
    UserExperience = 5
}

/// <summary>
/// Performance test execution context
/// </summary>
public class PerformanceTestContext
{
    public string TestId { get; set; } = Guid.NewGuid().ToString();
    public string Environment { get; set; } = "Development";
    public string Version { get; set; } = "1.0.0";
    public DateTime StartTime { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
    public List<string> Tags { get; set; } = new();
}

/// <summary>
/// Performance test runner configuration
/// </summary>
public class PerformanceTestRunnerConfiguration
{
    public bool EnableDetailedLogging { get; set; } = true;
    public bool EnableResourceMonitoring { get; set; } = true;
    public bool EnableReporting { get; set; } = true;
    public bool FailOnThresholdViolation { get; set; } = false;
    public int MaxConcurrentTests { get; set; } = 1;
    public TimeSpan TestTimeout { get; set; } = TimeSpan.FromHours(2);
    public string OutputDirectory { get; set; } = "performance-test-results";
    public List<string> ReportFormats { get; set; } = new() { "Html", "Json", "Csv" };
}
