using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SubscriptionManagement.Domain.Entities;

namespace SubscriptionManagement.Infrastructure.Persistence.Configurations;

public class UsageRecordConfiguration : IEntityTypeConfiguration<UsageRecord>
{
    public void Configure(EntityTypeBuilder<UsageRecord> builder)
    {
        builder.ToTable("UsageRecords");

        builder.HasKey(u => u.Id);

        builder.Property(u => u.SubscriptionId)
            .IsRequired();

        builder.Property(u => u.UserId)
            .IsRequired();

        builder.Property(u => u.FeatureType)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(u => u.UsageCount)
            .IsRequired()
            .HasDefaultValue(1);

        builder.Property(u => u.UsageDate)
            .IsRequired();

        builder.Property(u => u.Metadata)
            .HasMaxLength(1000);

        builder.Property(u => u.PeriodStart)
            .IsRequired();

        builder.Property(u => u.PeriodEnd)
            .IsRequired();

        builder.Property(u => u.CreatedAt)
            .IsRequired();

        builder.Property(u => u.UpdatedAt);

        // Indexes
        builder.HasIndex(u => u.UserId);
        builder.HasIndex(u => u.SubscriptionId);
        builder.HasIndex(u => u.FeatureType);
        builder.HasIndex(u => u.UsageDate);
        builder.HasIndex(u => new { u.UserId, u.FeatureType, u.PeriodStart, u.PeriodEnd });
        builder.HasIndex(u => new { u.SubscriptionId, u.FeatureType, u.PeriodStart, u.PeriodEnd });

        // Relationships
        builder.HasOne(u => u.Subscription)
            .WithMany()
            .HasForeignKey(u => u.SubscriptionId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
