using Microsoft.EntityFrameworkCore;
using DataStorage.Domain.Entities;
using DataStorage.Infrastructure.Configurations;
using Shared.Domain.Common;

namespace DataStorage.Infrastructure.Data;

public class DataStorageDbContext : DbContext
{
    public DataStorageDbContext(DbContextOptions<DataStorageDbContext> options) : base(options)
    {
    }

    public DbSet<Document> Documents { get; set; }
    public DbSet<DocumentVersion> DocumentVersions { get; set; }
    public DbSet<DocumentAccess> DocumentAccess { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Apply configurations
        modelBuilder.ApplyConfiguration(new DocumentConfiguration());
        modelBuilder.ApplyConfiguration(new DocumentVersionConfiguration());
        modelBuilder.ApplyConfiguration(new DocumentAccessConfiguration());

        // Configure schema
        modelBuilder.HasDefaultSchema("datastorage");

        // Configure value objects and enums
        ConfigureValueObjects(modelBuilder);
        ConfigureEnums(modelBuilder);
    }

    private static void ConfigureValueObjects(ModelBuilder modelBuilder)
    {
        // Configure FileMetadata value object
        modelBuilder.Entity<Document>()
            .OwnsOne(d => d.FileMetadata, fm =>
            {
                fm.Property(x => x.FileName).HasMaxLength(255).IsRequired();
                fm.Property(x => x.ContentType).HasMaxLength(100).IsRequired();
                fm.Property(x => x.FileSizeBytes).IsRequired();
                fm.Property(x => x.FileExtension).HasMaxLength(10);
                fm.Property(x => x.Checksum).HasMaxLength(128);
                fm.Property(x => x.CustomProperties)
                    .HasConversion(
                        v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                        v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new Dictionary<string, string>())
                    .HasColumnType("jsonb");
            });

        modelBuilder.Entity<DocumentVersion>()
            .OwnsOne(dv => dv.FileMetadata, fm =>
            {
                fm.Property(x => x.FileName).HasMaxLength(255).IsRequired();
                fm.Property(x => x.ContentType).HasMaxLength(100).IsRequired();
                fm.Property(x => x.FileSizeBytes).IsRequired();
                fm.Property(x => x.FileExtension).HasMaxLength(10);
                fm.Property(x => x.Checksum).HasMaxLength(128);
                fm.Property(x => x.CustomProperties)
                    .HasConversion(
                        v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                        v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new Dictionary<string, string>())
                    .HasColumnType("jsonb");
            });

        // Configure StorageLocation value object
        modelBuilder.Entity<Document>()
            .OwnsOne(d => d.StorageLocation, sl =>
            {
                sl.Property(x => x.Provider).IsRequired();
                sl.Property(x => x.ContainerName).HasMaxLength(100).IsRequired();
                sl.Property(x => x.FilePath).HasMaxLength(500).IsRequired();
                sl.Property(x => x.CDNUrl).HasMaxLength(500);
                sl.Property(x => x.Region).HasMaxLength(50);
                sl.Property(x => x.ProviderSpecificSettings)
                    .HasConversion(
                        v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                        v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new Dictionary<string, string>())
                    .HasColumnType("jsonb");
            });

        modelBuilder.Entity<DocumentVersion>()
            .OwnsOne(dv => dv.StorageLocation, sl =>
            {
                sl.Property(x => x.Provider).IsRequired();
                sl.Property(x => x.ContainerName).HasMaxLength(100).IsRequired();
                sl.Property(x => x.FilePath).HasMaxLength(500).IsRequired();
                sl.Property(x => x.CDNUrl).HasMaxLength(500);
                sl.Property(x => x.Region).HasMaxLength(50);
                sl.Property(x => x.ProviderSpecificSettings)
                    .HasConversion(
                        v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                        v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new Dictionary<string, string>())
                    .HasColumnType("jsonb");
            });
    }

    private static void ConfigureEnums(ModelBuilder modelBuilder)
    {
        // Configure enums as strings for better readability
        modelBuilder.Entity<Document>()
            .Property(d => d.DocumentType)
            .HasConversion<string>();

        modelBuilder.Entity<Document>()
            .Property(d => d.Category)
            .HasConversion<string>();

        modelBuilder.Entity<Document>()
            .Property(d => d.Status)
            .HasConversion<string>();

        modelBuilder.Entity<Document>()
            .Property(d => d.AccessLevel)
            .HasConversion<string>();

        modelBuilder.Entity<DocumentAccess>()
            .Property(da => da.AccessLevel)
            .HasConversion<string>();
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        // Handle domain events before saving
        var domainEvents = ChangeTracker
            .Entries<AggregateRoot>()
            .Where(x => x.Entity.DomainEvents.Any())
            .SelectMany(x => x.Entity.DomainEvents)
            .ToList();

        var result = await base.SaveChangesAsync(cancellationToken);

        // Clear domain events after saving
        foreach (var entry in ChangeTracker.Entries<AggregateRoot>())
        {
            entry.Entity.ClearDomainEvents();
        }

        return result;
    }
}
