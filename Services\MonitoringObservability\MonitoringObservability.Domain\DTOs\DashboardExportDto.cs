using MonitoringObservability.Domain.Enums;

namespace MonitoringObservability.Domain.DTOs;

/// <summary>
/// DTO for dashboard export
/// </summary>
public class DashboardExportDto
{
    public Guid DashboardId { get; set; }
    public string DashboardName { get; set; } = string.Empty;
    public ExportFormat Format { get; set; }
    public DateTime ExportedAt { get; set; }
    public string ExportedBy { get; set; } = string.Empty;
    public List<DashboardWidgetExportDto> Widgets { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
    public byte[] Data { get; set; } = Array.Empty<byte>();
    public string FileName { get; set; } = string.Empty;
    public long FileSizeBytes { get; set; }
}

public class DashboardWidgetExportDto
{
    public Guid WidgetId { get; set; }
    public string WidgetName { get; set; } = string.Empty;
    public string WidgetType { get; set; } = string.Empty;
    public Dictionary<string, object> Data { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
}
