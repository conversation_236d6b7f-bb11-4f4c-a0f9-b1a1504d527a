using MediatR;
using Microsoft.Extensions.Logging;
using TripManagement.Domain.Entities;
using TripManagement.Domain.Repositories;
using TLI.Shared.Application;

namespace TripManagement.Application.Commands.ConfigureMilestoneConfirmation;

public class ConfigureMilestoneConfirmationCommandHandler : IRequestHandler<ConfigureMilestoneConfirmationCommand, ConfigureMilestoneConfirmationResponse>
{
    private readonly ITripRepository _tripRepository;
    private readonly IMilestoneConfirmationConfigurationRepository _configurationRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<ConfigureMilestoneConfirmationCommandHandler> _logger;

    public ConfigureMilestoneConfirmationCommandHandler(
        ITripRepository tripRepository,
        IMilestoneConfirmationConfigurationRepository configurationRepository,
        IUnitOfWork unitOfWork,
        IMessageBroker messageBroker,
        ILogger<ConfigureMilestoneConfirmationCommandHandler> logger)
    {
        _tripRepository = tripRepository;
        _configurationRepository = configurationRepository;
        _unitOfWork = unitOfWork;
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task<ConfigureMilestoneConfirmationResponse> Handle(ConfigureMilestoneConfirmationCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Configuring milestone confirmation for trip {TripId}", request.TripId);

            // Validate trip exists
            var trip = await _tripRepository.GetByIdAsync(request.TripId, cancellationToken);
            if (trip == null)
            {
                return new ConfigureMilestoneConfirmationResponse
                {
                    IsSuccess = false,
                    ErrorMessage = "Trip not found"
                };
            }

            // Validate milestone if specified
            if (request.MilestoneId.HasValue)
            {
                // In a real implementation, validate that the milestone exists and belongs to the trip
                _logger.LogDebug("Configuring confirmation for specific milestone {MilestoneId}", request.MilestoneId);
            }

            // Validate settings
            var validationResult = ValidateConfirmationSettings(request.Settings);
            if (!validationResult.IsValid)
            {
                return new ConfigureMilestoneConfirmationResponse
                {
                    IsSuccess = false,
                    ErrorMessage = validationResult.ErrorMessage
                };
            }

            // Get existing configuration or create new
            var configuration = await _configurationRepository.GetByTripAndMilestoneAsync(request.TripId, request.MilestoneId, cancellationToken);

            if (configuration == null)
            {
                // Create new configuration
                configuration = new MilestoneConfirmationConfigurationEntity(
                    request.TripId,
                    request.MilestoneId,
                    request.Settings);

                await _configurationRepository.AddAsync(configuration, cancellationToken);
            }
            else
            {
                // Update existing configuration
                configuration.UpdateSettings(request.Settings);
                _configurationRepository.Update(configuration);
            }

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Publish integration event
            await _messageBroker.PublishAsync("milestone.confirmation.configured", new
            {
                TripId = request.TripId,
                MilestoneId = request.MilestoneId,
                ConfigurationId = configuration.Id,
                Settings = request.Settings,
                ConfiguredAt = DateTime.UtcNow
            }, cancellationToken);

            // Map to response DTO
            var responseConfiguration = new MilestoneConfirmationConfiguration
            {
                Id = configuration.Id,
                TripId = configuration.TripId,
                MilestoneId = configuration.MilestoneId,
                Settings = configuration.Settings,
                IsActive = configuration.IsActive,
                CreatedAt = configuration.CreatedAt,
                UpdatedAt = configuration.UpdatedAt,
                CreatedBy = configuration.CreatedBy,
                UpdatedBy = configuration.UpdatedBy,
                Metadata = configuration.Metadata
            };

            _logger.LogInformation("Milestone confirmation configured successfully for trip {TripId}", request.TripId);

            return new ConfigureMilestoneConfirmationResponse
            {
                IsSuccess = true,
                Configuration = responseConfiguration,
                ConfiguredAt = configuration.UpdatedAt
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error configuring milestone confirmation for trip {TripId}", request.TripId);
            return new ConfigureMilestoneConfirmationResponse
            {
                IsSuccess = false,
                ErrorMessage = ex.Message,
                ConfiguredAt = DateTime.UtcNow
            };
        }
    }

    private ValidationResult ValidateConfirmationSettings(MilestoneConfirmationSettings settings)
    {
        var errors = new List<string>();

        if (settings.ConfirmationTimeoutSeconds < 5 || settings.ConfirmationTimeoutSeconds > 300)
        {
            errors.Add("Confirmation timeout must be between 5 and 300 seconds");
        }

        if (string.IsNullOrWhiteSpace(settings.ConfirmationMessage))
        {
            errors.Add("Confirmation message cannot be empty");
        }

        if (settings.RequireDoubleConfirmation && !settings.RequireConfirmationPopup)
        {
            errors.Add("Double confirmation requires confirmation popup to be enabled");
        }

        if (settings.RequireManagerApproval && settings.AllowOutOfSequenceCompletion)
        {
            errors.Add("Manager approval and out-of-sequence completion cannot both be enabled");
        }

        return new ValidationResult
        {
            IsValid = errors.Count == 0,
            ErrorMessage = errors.Count > 0 ? string.Join("; ", errors) : null
        };
    }

    private class ValidationResult
    {
        public bool IsValid { get; set; }
        public string? ErrorMessage { get; set; }
    }
}
