using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Application.Interfaces;

public interface IEscrowService
{
    Task<EscrowAccount> CreateEscrowAccountAsync(
        Guid orderId,
        Guid transportCompanyId,
        Guid brokerId,
        Guid carrierId,
        Money totalAmount,
        string? notes = null);

    Task<bool> FundEscrowAccountAsync(
        Guid escrowAccountId,
        Money amount,
        string paymentMethodId);

    Task<bool> ReleaseEscrowFundsAsync(
        Guid escrowAccountId,
        Money amount,
        Guid recipientId,
        string reason);

    Task<bool> RefundEscrowFundsAsync(
        Guid escrowAccountId,
        Money amount,
        string reason);

    Task<EscrowAccount?> GetEscrowAccountByOrderIdAsync(Guid orderId);
    Task<EscrowAccount?> GetEscrowAccountByIdAsync(Guid escrowAccountId);
    Task<List<EscrowAccount>> GetEscrowAccountsByTransportCompanyAsync(Guid transportCompanyId);
    Task<List<EscrowAccount>> GetEscrowAccountsByBrokerAsync(Guid brokerId);
    Task<List<EscrowAccount>> GetEscrowAccountsByCarrierAsync(Guid carrierId);
}

public class EscrowFundingResult
{
    public bool IsSuccess { get; set; }
    public string? TransactionId { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime ProcessedAt { get; set; }
}

public class EscrowReleaseResult
{
    public bool IsSuccess { get; set; }
    public string? TransactionId { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime ProcessedAt { get; set; }
}
