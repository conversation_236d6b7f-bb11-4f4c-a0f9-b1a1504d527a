using MediatR;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Domain.Interfaces;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Exceptions;
using SubscriptionManagement.Domain.Enums;
using Shared.Messaging;

namespace SubscriptionManagement.Application.Commands.CreateFeatureFlag
{
    public class CreateFeatureFlagCommandHandler : IRequestHandler<CreateFeatureFlagCommand, Guid>
    {
        private readonly IFeatureFlagRepository _featureFlagRepository;
        private readonly IMessageBroker _messageBroker;
        private readonly ILogger<CreateFeatureFlagCommandHandler> _logger;

        public CreateFeatureFlagCommandHandler(
            IFeatureFlagRepository featureFlagRepository,
            IMessageBroker messageBroker,
            ILogger<CreateFeatureFlagCommandHandler> logger)
        {
            _featureFlagRepository = featureFlagRepository;
            _messageBroker = messageBroker;
            _logger = logger;
        }

        public async Task<Guid> Handle(CreateFeatureFlagCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Creating feature flag {Key} with name {Name}", request.Key, request.Name);

            // Validate key uniqueness
            var existingFlag = await _featureFlagRepository.GetByKeyAsync(request.Key);
            if (existingFlag != null)
            {
                throw new SubscriptionDomainException($"Feature flag with key '{request.Key}' already exists");
            }

            // Create feature flag
            var featureFlag = new FeatureFlag(
                request.Key,
                request.Name,
                request.Description,
                request.IsEnabled,
                ParseConfiguration(request));

            featureFlag.SetType(request.Type);
            featureFlag.SetDefaultValue(request.DefaultValue);

            // Set rollout percentage
            if (request.RolloutPercentage > 0)
            {
                featureFlag.SetRolloutPercentage(request.RolloutPercentage);
            }

            // Configure A/B test if applicable
            if (request.Type == FeatureFlagType.ABTest && !string.IsNullOrEmpty(request.ABTestConfiguration))
            {
                featureFlag.ConfigureABTest(request.ABTestConfiguration, request.Variants ?? "[]");
            }

            // Set activation dates
            if (request.StartDate.HasValue || request.EndDate.HasValue)
            {
                featureFlag.SetActivationPeriod(request.StartDate, request.EndDate);
            }

            // Save feature flag
            var savedFlag = await _featureFlagRepository.AddAsync(featureFlag);

            // Publish creation event
            await _messageBroker.PublishAsync("feature_flag.created", new
            {
                FeatureFlagId = savedFlag.Id,
                Key = savedFlag.Key,
                Name = savedFlag.Name,
                Type = savedFlag.Type.ToString(),
                IsEnabled = savedFlag.IsEnabled,
                RolloutPercentage = request.RolloutPercentage,
                CreatedAt = DateTime.UtcNow
            });

            _logger.LogInformation("Successfully created feature flag {FeatureFlagId} with key {Key}", 
                savedFlag.Id, request.Key);

            return savedFlag.Id;
        }

        private Dictionary<string, object> ParseConfiguration(CreateFeatureFlagCommand request)
        {
            var config = new Dictionary<string, object>();

            if (request.RolloutPercentage > 0)
            {
                config["rollout_percentage"] = request.RolloutPercentage;
            }

            if (!string.IsNullOrEmpty(request.TargetUserTypes))
            {
                try
                {
                    config["target_user_types"] = System.Text.Json.JsonSerializer.Deserialize<string[]>(request.TargetUserTypes);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to parse target user types: {TargetUserTypes}", request.TargetUserTypes);
                }
            }

            if (request.StartDate.HasValue)
            {
                config["start_date"] = request.StartDate.Value;
            }

            if (request.EndDate.HasValue)
            {
                config["end_date"] = request.EndDate.Value;
            }

            return config;
        }
    }
}
