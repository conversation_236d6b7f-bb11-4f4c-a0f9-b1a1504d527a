using MediatR;
using AuditCompliance.Application.DTOs.Reports;
using AuditCompliance.Domain.Enums;

namespace AuditCompliance.Application.Commands.Reports;

/// <summary>
/// Command to generate feedback summary report
/// </summary>
public class GenerateFeedbackSummaryReportCommand : IRequest<FeedbackSummaryReportDto>
{
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public Guid? ShipperId { get; set; }
    public Guid? TransportCompanyId { get; set; }
    public RatingStatus? Status { get; set; }
    public decimal? MinRating { get; set; }
    public decimal? MaxRating { get; set; }
    public List<string>? Tags { get; set; }
    public string? Category { get; set; }
    public ReportFormat Format { get; set; } = ReportFormat.Json;
    public bool IncludeTagAnalysis { get; set; } = true;
    public bool IncludeRatingTrends { get; set; } = true;
    public bool IncludeCompanyComparison { get; set; } = false;
    public bool IncludeDetailedFeedback { get; set; } = false;
    public int? MaxRecords { get; set; }
    public string? GroupBy { get; set; } // Company, Category, Rating, Date
    public Guid RequestedBy { get; set; }
    public string RequestedByRole { get; set; } = string.Empty;
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
}

/// <summary>
/// Enum for report formats
/// </summary>
public enum ReportFormat
{
    Json = 0,
    Csv = 1,
    Excel = 2,
    Pdf = 3
}
