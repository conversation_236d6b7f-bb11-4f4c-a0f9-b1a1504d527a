using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Domain.Entities;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace CommunicationNotification.Infrastructure.Services;

/// <summary>
/// Service for IVR system management and call processing
/// </summary>
public class IVRService : IIVRService
{
    private readonly IIVRRepository _ivrRepository;
    private readonly IIVRSessionRepository _sessionRepository;
    private readonly ITwiMLService _twimlService;
    private readonly IVoiceRecognitionService _voiceRecognitionService;
    private readonly IDistributedCache _cache;
    private readonly ILogger<IVRService> _logger;
    private readonly TimeSpan _cacheExpiration = TimeSpan.FromMinutes(30);

    public IVRService(
        IIVRRepository ivrRepository,
        IIVRSessionRepository sessionRepository,
        ITwiMLService twimlService,
        IVoiceRecognitionService voiceRecognitionService,
        IDistributedCache cache,
        ILogger<IVRService> logger)
    {
        _ivrRepository = ivrRepository;
        _sessionRepository = sessionRepository;
        _twimlService = twimlService;
        _voiceRecognitionService = voiceRecognitionService;
        _cache = cache;
        _logger = logger;
    }

    public async Task<IVRSystem> CreateIVRSystemAsync(
        string name,
        string description,
        string welcomeMessage,
        string defaultLanguage,
        Guid createdByUserId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating IVR system {Name} by user {UserId}", name, createdByUserId);

            // Check if name already exists
            if (await _ivrRepository.ExistsAsync(name, cancellationToken))
            {
                throw new InvalidOperationException($"IVR system with name '{name}' already exists");
            }

            var ivrSystem = IVRSystem.Create(name, description, welcomeMessage, defaultLanguage, createdByUserId);
            var createdSystem = await _ivrRepository.AddAsync(ivrSystem, cancellationToken);

            _logger.LogInformation("Successfully created IVR system {SystemId} with name {Name}",
                createdSystem.Id, createdSystem.Name);

            return createdSystem;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating IVR system {Name}", name);
            throw;
        }
    }

    public async Task<IVRSystem?> GetIVRSystemAsync(
        Guid ivrSystemId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting IVR system {SystemId}", ivrSystemId);

            var cacheKey = $"ivr_system_{ivrSystemId}";
            var cachedSystem = await GetFromCacheAsync<IVRSystem>(cacheKey, cancellationToken);
            if (cachedSystem != null)
            {
                return cachedSystem;
            }

            var system = await _ivrRepository.GetByIdAsync(ivrSystemId, cancellationToken);
            if (system != null)
            {
                await SetCacheAsync(cacheKey, system, cancellationToken);
            }

            return system;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting IVR system {SystemId}", ivrSystemId);
            throw;
        }
    }

    public async Task<IVRSystem> UpdateIVRSystemAsync(
        Guid ivrSystemId,
        IVRSystem updatedSystem,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Updating IVR system {SystemId}", ivrSystemId);

            var system = await _ivrRepository.UpdateAsync(updatedSystem, cancellationToken);
            await InvalidateSystemCacheAsync(ivrSystemId, cancellationToken);

            _logger.LogInformation("Successfully updated IVR system {SystemId}", ivrSystemId);
            return system;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating IVR system {SystemId}", ivrSystemId);
            throw;
        }
    }

    public async Task DeleteIVRSystemAsync(
        Guid ivrSystemId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Deleting IVR system {SystemId}", ivrSystemId);

            await _ivrRepository.DeleteAsync(ivrSystemId, cancellationToken);
            await InvalidateSystemCacheAsync(ivrSystemId, cancellationToken);

            _logger.LogInformation("Successfully deleted IVR system {SystemId}", ivrSystemId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting IVR system {SystemId}", ivrSystemId);
            throw;
        }
    }

    public async Task<List<IVRSystem>> GetAllIVRSystemsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting all IVR systems");

            var cacheKey = "all_ivr_systems";
            var cachedSystems = await GetFromCacheAsync<List<IVRSystem>>(cacheKey, cancellationToken);
            if (cachedSystems != null)
            {
                return cachedSystems;
            }

            var systems = await _ivrRepository.GetAllAsync(cancellationToken);
            await SetCacheAsync(cacheKey, systems, cancellationToken);

            _logger.LogDebug("Retrieved {Count} IVR systems", systems.Count);
            return systems;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all IVR systems");
            throw;
        }
    }

    public async Task<IVRSystem> ActivateIVRSystemAsync(
        Guid ivrSystemId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Activating IVR system {SystemId}", ivrSystemId);

            var system = await _ivrRepository.GetByIdAsync(ivrSystemId, cancellationToken);
            if (system == null)
            {
                throw new ArgumentException($"IVR system with ID {ivrSystemId} not found");
            }

            system.Activate();
            var updatedSystem = await _ivrRepository.UpdateAsync(system, cancellationToken);
            await InvalidateSystemCacheAsync(ivrSystemId, cancellationToken);

            _logger.LogInformation("Successfully activated IVR system {SystemId}", ivrSystemId);
            return updatedSystem;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error activating IVR system {SystemId}", ivrSystemId);
            throw;
        }
    }

    public async Task<IVRSystem> DeactivateIVRSystemAsync(
        Guid ivrSystemId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Deactivating IVR system {SystemId}", ivrSystemId);

            var system = await _ivrRepository.GetByIdAsync(ivrSystemId, cancellationToken);
            if (system == null)
            {
                throw new ArgumentException($"IVR system with ID {ivrSystemId} not found");
            }

            system.Deactivate();
            var updatedSystem = await _ivrRepository.UpdateAsync(system, cancellationToken);
            await InvalidateSystemCacheAsync(ivrSystemId, cancellationToken);

            _logger.LogInformation("Successfully deactivated IVR system {SystemId}", ivrSystemId);
            return updatedSystem;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deactivating IVR system {SystemId}", ivrSystemId);
            throw;
        }
    }

    public async Task<IVRSession> StartIVRSessionAsync(
        string callId,
        string phoneNumber,
        Guid ivrSystemId,
        string language = "en-US",
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting IVR session for call {CallId} with system {SystemId}",
                callId, ivrSystemId);

            var ivrSystem = await GetIVRSystemAsync(ivrSystemId, cancellationToken);
            if (ivrSystem == null)
            {
                throw new ArgumentException($"IVR system with ID {ivrSystemId} not found");
            }

            if (ivrSystem.Status != IVRSystemStatus.Active)
            {
                throw new InvalidOperationException($"IVR system {ivrSystemId} is not active");
            }

            var session = IVRSession.Create(callId, phoneNumber, ivrSystemId, language);

            // Navigate to main menu
            var mainMenu = ivrSystem.GetMainMenu();
            if (mainMenu != null)
            {
                session.NavigateToMenu(mainMenu.Id);
            }

            var createdSession = await _sessionRepository.AddAsync(session, cancellationToken);

            _logger.LogInformation("Successfully started IVR session {SessionId} for call {CallId}",
                createdSession.SessionId, callId);

            return createdSession;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting IVR session for call {CallId}", callId);
            throw;
        }
    }

    public async Task<IVRProcessingResult> ProcessInputAsync(
        Guid sessionId,
        string input,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Processing input '{Input}' for session {SessionId}", input, sessionId);

            var session = await _sessionRepository.GetByIdAsync(sessionId, cancellationToken);
            if (session == null)
            {
                return IVRProcessingResult.Failure($"Session {sessionId} not found");
            }

            if (session.Status != IVRSessionStatus.Active)
            {
                return IVRProcessingResult.Failure($"Session {sessionId} is not active");
            }

            var ivrSystem = await GetIVRSystemAsync(session.IVRSystemId, cancellationToken);
            if (ivrSystem == null)
            {
                return IVRProcessingResult.Failure($"IVR system {session.IVRSystemId} not found");
            }

            // Record the input
            session.RecordInput(input);

            // Get current menu
            var currentMenu = session.CurrentMenuId.HasValue
                ? ivrSystem.GetMenuById(session.CurrentMenuId.Value)
                : ivrSystem.GetMainMenu();

            if (currentMenu == null)
            {
                return IVRProcessingResult.Failure("No current menu found");
            }

            // Process the input based on menu type and options
            var result = await ProcessMenuInputAsync(ivrSystem, currentMenu, session, input, cancellationToken);

            // Update session
            await _sessionRepository.UpdateAsync(session, cancellationToken);

            _logger.LogDebug("Successfully processed input for session {SessionId}", sessionId);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing input for session {SessionId}", sessionId);
            return IVRProcessingResult.Failure($"Error processing input: {ex.Message}");
        }
    }

    public async Task EndIVRSessionAsync(
        Guid sessionId,
        IVRSessionStatus endStatus,
        string? reason = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Ending IVR session {SessionId} with status {Status}", sessionId, endStatus);

            var session = await _sessionRepository.GetByIdAsync(sessionId, cancellationToken);
            if (session == null)
            {
                _logger.LogWarning("Session {SessionId} not found when trying to end", sessionId);
                return;
            }

            switch (endStatus)
            {
                case IVRSessionStatus.Completed:
                    session.Complete();
                    break;
                case IVRSessionStatus.Abandoned:
                    session.Abandon();
                    break;
                case IVRSessionStatus.Failed:
                    session.Fail(reason ?? "Unknown error");
                    break;
            }

            await _sessionRepository.UpdateAsync(session, cancellationToken);

            _logger.LogInformation("Successfully ended IVR session {SessionId}", sessionId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error ending IVR session {SessionId}", sessionId);
            throw;
        }
    }

    public async Task<IVRSession?> GetIVRSessionAsync(
        Guid sessionId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            return await _sessionRepository.GetByIdAsync(sessionId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting IVR session {SessionId}", sessionId);
            throw;
        }
    }

    public async Task<List<IVRSession>> GetActiveSessionsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return await _sessionRepository.GetActiveSessionsAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active IVR sessions");
            throw;
        }
    }

    public async Task<IVRAnalytics> GetIVRAnalyticsAsync(
        Guid ivrSystemId,
        DateTime? startDate = null,
        DateTime? endDate = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Getting IVR analytics for system {SystemId}", ivrSystemId);

            var start = startDate ?? DateTime.UtcNow.AddDays(-30);
            var end = endDate ?? DateTime.UtcNow;

            var sessions = await _sessionRepository.GetByIVRSystemAsync(ivrSystemId, start, end, cancellationToken);

            var totalCalls = sessions.Count;
            var completedCalls = sessions.Count(s => s.Status == IVRSessionStatus.Completed);
            var abandonedCalls = sessions.Count(s => s.Status == IVRSessionStatus.Abandoned);
            var averageCallDuration = sessions.Any()
                ? (decimal)sessions.Average(s => s.GetDuration().TotalSeconds)
                : 0;

            // Calculate menu usage
            var menuUsage = new Dictionary<string, int>();
            var optionSelections = new Dictionary<string, int>();
            var menuDurations = new Dictionary<string, decimal>();

            foreach (var session in sessions)
            {
                var menuSteps = session.GetStepsByType(IVRStepType.MenuNavigation);
                foreach (var step in menuSteps)
                {
                    var menuId = step.Data;
                    menuUsage[menuId] = menuUsage.GetValueOrDefault(menuId, 0) + 1;
                }

                var inputSteps = session.GetStepsByType(IVRStepType.UserInput);
                foreach (var step in inputSteps)
                {
                    var input = step.Data;
                    optionSelections[input] = optionSelections.GetValueOrDefault(input, 0) + 1;
                }
            }

            var analytics = new IVRAnalytics(
                totalCalls, completedCalls, abandonedCalls, averageCallDuration,
                menuUsage, optionSelections, menuDurations);

            _logger.LogInformation("Generated IVR analytics for system {SystemId}: {TotalCalls} calls, {CompletionRate}% completion rate",
                ivrSystemId, totalCalls, analytics.CompletionRate);

            return analytics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting IVR analytics for system {SystemId}", ivrSystemId);
            throw;
        }
    }

    // Private helper methods
    private async Task<IVRProcessingResult> ProcessMenuInputAsync(
        IVRSystem ivrSystem,
        IVRMenu menu,
        IVRSession session,
        string input,
        CancellationToken cancellationToken)
    {
        // Check if input matches any menu option
        var option = menu.GetOption(input);
        if (option == null || !option.IsEnabled)
        {
            // Handle invalid input
            session.RecordError($"Invalid input: {input}");

            if (session.RetryCount >= menu.MaxRetries)
            {
                // Max retries reached, end call or transfer to fallback
                return IVRProcessingResult.Failure("Maximum retries exceeded");
            }

            // Generate retry TwiML
            var retryTwiML = await _twimlService.GenerateMenuTwiMLAsync(menu, session, cancellationToken);
            return IVRProcessingResult.Success(retryTwiML);
        }

        // Process the selected option
        return await ProcessMenuOptionAsync(ivrSystem, option, session, cancellationToken);
    }

    private async Task<IVRProcessingResult> ProcessMenuOptionAsync(
        IVRSystem ivrSystem,
        IVRMenuOption option,
        IVRSession session,
        CancellationToken cancellationToken)
    {
        switch (option.ActionType)
        {
            case IVRActionType.NavigateToMenu:
                return await ProcessNavigateToMenuAsync(ivrSystem, option, session, cancellationToken);

            case IVRActionType.TransferCall:
                return ProcessTransferCall(option);

            case IVRActionType.PlayMessage:
                return await ProcessPlayMessageAsync(option, session, cancellationToken);

            case IVRActionType.CollectInput:
                return await ProcessCollectInputAsync(option, session, cancellationToken);

            case IVRActionType.HangUp:
                return IVRProcessingResult.EndCall();

            case IVRActionType.SetVariable:
                return ProcessSetVariable(option, session);

            case IVRActionType.ExecuteWebhook:
                return await ProcessExecuteWebhookAsync(option, session, cancellationToken);

            default:
                return IVRProcessingResult.Failure($"Unsupported action type: {option.ActionType}");
        }
    }

    private async Task<IVRProcessingResult> ProcessNavigateToMenuAsync(
        IVRSystem ivrSystem,
        IVRMenuOption option,
        IVRSession session,
        CancellationToken cancellationToken)
    {
        if (!Guid.TryParse(option.ActionValue, out var menuId))
        {
            return IVRProcessingResult.Failure($"Invalid menu ID: {option.ActionValue}");
        }

        var targetMenu = ivrSystem.GetMenuById(menuId);
        if (targetMenu == null)
        {
            return IVRProcessingResult.Failure($"Menu not found: {menuId}");
        }

        // Check menu conditions
        if (!targetMenu.EvaluateConditions(session.Variables))
        {
            return IVRProcessingResult.Failure("Menu conditions not met");
        }

        session.NavigateToMenu(menuId);
        var twiML = await _twimlService.GenerateMenuTwiMLAsync(targetMenu, session, cancellationToken);

        return IVRProcessingResult.Success(twiML, IVRActionType.NavigateToMenu, menuId.ToString());
    }

    private IVRProcessingResult ProcessTransferCall(IVRMenuOption option)
    {
        var transferNumber = option.ActionValue;
        var transferMessage = option.Parameters.GetValueOrDefault("message");

        return IVRProcessingResult.Transfer(transferNumber, transferMessage);
    }

    private async Task<IVRProcessingResult> ProcessPlayMessageAsync(
        IVRMenuOption option,
        IVRSession session,
        CancellationToken cancellationToken)
    {
        var message = option.ActionValue;
        var twiML = await _twimlService.GenerateMessageTwiMLAsync(message, session.Language, cancellationToken: cancellationToken);

        return IVRProcessingResult.Success(twiML, IVRActionType.PlayMessage, message);
    }

    private async Task<IVRProcessingResult> ProcessCollectInputAsync(
        IVRMenuOption option,
        IVRSession session,
        CancellationToken cancellationToken)
    {
        var prompt = option.ActionValue;
        var maxDigits = int.Parse(option.Parameters.GetValueOrDefault("maxDigits", "1"));
        var timeoutSeconds = int.Parse(option.Parameters.GetValueOrDefault("timeout", "10"));
        var finishOnKey = option.Parameters.GetValueOrDefault("finishOnKey", "#");

        var twiML = await _twimlService.GenerateInputCollectionTwiMLAsync(
            prompt, maxDigits, timeoutSeconds, finishOnKey, cancellationToken);

        return IVRProcessingResult.Success(twiML, IVRActionType.CollectInput, prompt);
    }

    private IVRProcessingResult ProcessSetVariable(IVRMenuOption option, IVRSession session)
    {
        var parts = option.ActionValue.Split('=', 2);
        if (parts.Length == 2)
        {
            session.SetVariable(parts[0], parts[1]);
            return IVRProcessingResult.Success("<Response><Say>Variable set</Say></Response>");
        }

        return IVRProcessingResult.Failure("Invalid variable format");
    }

    private async Task<IVRProcessingResult> ProcessExecuteWebhookAsync(
        IVRMenuOption option,
        IVRSession session,
        CancellationToken cancellationToken)
    {
        // This would integrate with a webhook service
        // For now, return a simple success response
        var webhookUrl = option.ActionValue;
        _logger.LogInformation("Executing webhook {WebhookUrl} for session {SessionId}", webhookUrl, session.SessionId);

        return IVRProcessingResult.Success("<Response><Say>Webhook executed</Say></Response>");
    }

    // Cache helper methods
    private async Task<T?> GetFromCacheAsync<T>(string key, CancellationToken cancellationToken) where T : class
    {
        try
        {
            var cachedData = await _cache.GetStringAsync(key, cancellationToken);
            if (!string.IsNullOrEmpty(cachedData))
            {
                return JsonSerializer.Deserialize<T>(cachedData);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error retrieving data from cache for key {Key}", key);
        }

        return null;
    }

    private async Task SetCacheAsync<T>(string key, T data, CancellationToken cancellationToken)
    {
        try
        {
            var serializedData = JsonSerializer.Serialize(data);
            await _cache.SetStringAsync(key, serializedData, new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = _cacheExpiration
            }, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error setting cache for key {Key}", key);
        }
    }

    private async Task InvalidateSystemCacheAsync(Guid ivrSystemId, CancellationToken cancellationToken)
    {
        var keysToInvalidate = new[]
        {
            $"ivr_system_{ivrSystemId}",
            "all_ivr_systems"
        };

        foreach (var key in keysToInvalidate)
        {
            try
            {
                await _cache.RemoveAsync(key, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error invalidating cache key {Key}", key);
            }
        }
    }
}
