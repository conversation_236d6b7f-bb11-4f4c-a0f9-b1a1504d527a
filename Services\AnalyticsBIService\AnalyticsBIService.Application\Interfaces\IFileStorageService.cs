namespace AnalyticsBIService.Application.Interfaces;

/// <summary>
/// File storage service interface
/// </summary>
public interface IFileStorageService
{
    /// <summary>
    /// Save file to storage
    /// </summary>
    Task<string> SaveFileAsync(
        string fileName,
        byte[] content,
        string contentType,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Save file from stream
    /// </summary>
    Task<string> SaveFileAsync(
        string fileName,
        Stream content,
        string contentType,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get file content as bytes
    /// </summary>
    Task<byte[]> GetFileAsync(
        string filePath,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get file content as stream
    /// </summary>
    Task<Stream> GetFileStreamAsync(
        string filePath,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete file from storage
    /// </summary>
    Task<bool> DeleteFileAsync(
        string filePath,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if file exists
    /// </summary>
    Task<bool> FileExistsAsync(
        string filePath,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get file information
    /// </summary>
    Task<FileInfo?> GetFileInfoAsync(
        string filePath,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// List files in directory
    /// </summary>
    Task<List<string>> ListFilesAsync(
        string directoryPath,
        string searchPattern = "*",
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Create directory
    /// </summary>
    Task<bool> CreateDirectoryAsync(
        string directoryPath,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete directory
    /// </summary>
    Task<bool> DeleteDirectoryAsync(
        string directoryPath,
        bool recursive = false,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get file size
    /// </summary>
    Task<long> GetFileSizeAsync(
        string filePath,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Copy file
    /// </summary>
    Task<bool> CopyFileAsync(
        string sourceFilePath,
        string destinationFilePath,
        bool overwrite = false,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Move file
    /// </summary>
    Task<bool> MoveFileAsync(
        string sourceFilePath,
        string destinationFilePath,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Store file and return file path
    /// </summary>
    Task<string> StoreFileAsync(
        byte[] content,
        string fileName,
        string contentType,
        CancellationToken cancellationToken = default);
}
