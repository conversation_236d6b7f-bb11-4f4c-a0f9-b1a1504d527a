using MediatR;
using TripManagement.Application.DTOs;
using TripManagement.Domain.Enums;

namespace TripManagement.Application.Commands.UpdateTripLocation;

public record UpdateTripLocationCommand : IRequest<Guid>
{
    public Guid TripId { get; init; }
    public double Latitude { get; init; }
    public double Longitude { get; init; }
    public double? Altitude { get; init; }
    public double? Accuracy { get; init; }
    public double? Speed { get; init; }
    public double? Heading { get; init; }
    public LocationUpdateSource Source { get; init; } = LocationUpdateSource.GPS;
    public DateTime Timestamp { get; init; } = DateTime.UtcNow;

    // For backward compatibility
    public LocationDto Location => new LocationDto
    {
        Latitude = Latitude,
        Longitude = Longitude
    };
}
