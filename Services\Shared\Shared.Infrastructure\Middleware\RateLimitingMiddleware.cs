using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using System.Net;

namespace Shared.Infrastructure.Middleware
{
    /// <summary>
    /// Rate limiting middleware for export endpoints
    /// </summary>
    public class RateLimitingMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly IMemoryCache _cache;
        private readonly ILogger<RateLimitingMiddleware> _logger;
        private readonly RateLimitOptions _options;

        public RateLimitingMiddleware(
            RequestDelegate next,
            IMemoryCache cache,
            ILogger<RateLimitingMiddleware> logger,
            RateLimitOptions options)
        {
            _next = next;
            _cache = cache;
            _logger = logger;
            _options = options;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            // Only apply rate limiting to export endpoints
            if (!IsExportEndpoint(context.Request.Path))
            {
                await _next(context);
                return;
            }

            var clientId = GetClientIdentifier(context);
            var rateLimitKey = $"rate_limit:{clientId}";
            
            var requestCount = _cache.Get<int>(rateLimitKey);
            
            if (requestCount >= _options.MaxRequests)
            {
                _logger.LogWarning("Rate limit exceeded for client {ClientId}. Current count: {RequestCount}", 
                    clientId, requestCount);
                
                context.Response.StatusCode = (int)HttpStatusCode.TooManyRequests;
                context.Response.Headers.Add("Retry-After", _options.WindowSizeInMinutes.ToString());
                
                await context.Response.WriteAsync("Rate limit exceeded. Please try again later.");
                return;
            }

            // Increment request count
            _cache.Set(rateLimitKey, requestCount + 1, TimeSpan.FromMinutes(_options.WindowSizeInMinutes));
            
            // Add rate limit headers
            context.Response.Headers.Add("X-RateLimit-Limit", _options.MaxRequests.ToString());
            context.Response.Headers.Add("X-RateLimit-Remaining", (_options.MaxRequests - requestCount - 1).ToString());
            context.Response.Headers.Add("X-RateLimit-Reset", DateTimeOffset.UtcNow.AddMinutes(_options.WindowSizeInMinutes).ToUnixTimeSeconds().ToString());

            await _next(context);
        }

        private bool IsExportEndpoint(PathString path)
        {
            var pathValue = path.Value?.ToLowerInvariant() ?? "";
            return pathValue.Contains("/export") || 
                   pathValue.Contains("/download") ||
                   pathValue.EndsWith("/csv") ||
                   pathValue.EndsWith("/excel") ||
                   pathValue.EndsWith("/pdf");
        }

        private string GetClientIdentifier(HttpContext context)
        {
            // Try to get user ID first
            var userId = context.User?.FindFirst("sub")?.Value ?? 
                        context.User?.FindFirst("id")?.Value;
            
            if (!string.IsNullOrEmpty(userId))
            {
                return $"user:{userId}";
            }

            // Fall back to IP address
            var ipAddress = context.Connection.RemoteIpAddress?.ToString() ?? "unknown";
            return $"ip:{ipAddress}";
        }
    }

    /// <summary>
    /// Rate limiting configuration options
    /// </summary>
    public class RateLimitOptions
    {
        public int MaxRequests { get; set; } = 10; // Max requests per window
        public int WindowSizeInMinutes { get; set; } = 60; // Time window in minutes
    }
}
