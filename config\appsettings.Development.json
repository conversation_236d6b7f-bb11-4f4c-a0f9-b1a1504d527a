{"Logging": {"LogLevel": {"Default": "Debug", "Microsoft": "Information", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.EntityFrameworkCore": "Information", "System.Net.Http.HttpClient": "Information"}}, "ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=TLI_Development;Username=********;Password=********;", "Redis": "localhost:6379", "RabbitMQ": "amqp://rabbitmq:rabbitmq123@localhost:5672/"}, "Authentication": {"Jwt": {"SecretKey": "TLI-Development-Secret-Key-For-JWT-Token-Generation-2024-DEV", "ExpiryInMinutes": 120}}, "Cors": {"AllowedOrigins": ["http://localhost:3000", "http://localhost:3001", "http://localhost:4200", "https://localhost:3000", "https://localhost:3001", "https://localhost:4200", "http://localhost:8080", "https://localhost:8080"]}, "RateLimiting": {"EnableRateLimiting": false, "GeneralRules": {"PermitLimit": 1000, "Window": "00:01:00"}}, "Caching": {"Redis": {"Enabled": true, "DefaultExpirationMinutes": 5, "Database": 1}, "Memory": {"DefaultExpirationMinutes": 5}}, "HealthChecks": {"DetailedErrors": true, "Timeout": "00:01:00"}, "Monitoring": {"ApplicationInsights": {"Enabled": false}, "Prometheus": {"Enabled": true}, "Jaeger": {"Enabled": true, "ServiceName": "TLI-Development"}, "Seq": {"Enabled": true, "ServerUrl": "http://localhost:5341"}}, "Security": {"EnableHttpsRedirection": false, "EnableHsts": false}, "Swagger": {"Enabled": true, "Title": "TLI Microservices API - Development", "Description": "Development environment for TLI Microservices API"}, "FileStorage": {"Provider": "Local", "Local": {"BasePath": "./uploads/dev", "MaxFileSize": 52428800}}, "Email": {"Provider": "SMTP", "SMTP": {"Host": "localhost", "Port": 1025, "EnableSsl": false, "Username": "", "Password": "", "FromEmail": "<EMAIL>", "FromName": "TLI Development"}}, "Features": {"EnableUserRegistration": true, "EnableEmailVerification": false, "EnablePhoneVerification": false, "EnableTwoFactorAuth": false, "EnableSocialLogin": false, "MaintenanceMode": false}, "BusinessRules": {"MaxOrderValue": 10000000, "MinOrderValue": 1, "SessionTimeoutMinutes": 120, "PasswordPolicy": {"MinLength": 6, "RequireUppercase": false, "RequireLowercase": false, "RequireDigit": false, "RequireSpecialChar": false}}, "DeveloperSettings": {"EnableDetailedErrors": true, "EnableSensitiveDataLogging": true, "EnableDeveloperExceptionPage": true, "SeedTestData": true, "BypassAuthentication": false, "MockExternalServices": true, "EnableSqlLogging": true, "EnablePerformanceCounters": true}}