using Shared.Domain.Common;
using MobileWorkflow.Domain.Events;

namespace MobileWorkflow.Domain.Entities;

public class BiometricProfile : AggregateRoot
{
    public Guid UserId { get; private set; }
    public string DeviceId { get; private set; }
    public string Platform { get; private set; } // iOS, Android, Windows
    public List<string> SupportedBiometrics { get; private set; }
    public List<string> EnabledBiometrics { get; private set; }
    public bool IsActive { get; private set; }
    public DateTime EnrolledAt { get; private set; }
    public DateTime? LastUsedAt { get; private set; }
    public Dictionary<string, object> BiometricData { get; private set; }
    public Dictionary<string, object> SecuritySettings { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    public virtual ICollection<BiometricAuthenticationAttempt> AuthenticationAttempts { get; private set; } = new List<BiometricAuthenticationAttempt>();

    private BiometricProfile() { } // EF Core

    public BiometricProfile(
        Guid userId,
        string deviceId,
        string platform,
        List<string> supportedBiometrics)
    {
        UserId = userId;
        DeviceId = deviceId ?? throw new ArgumentNullException(nameof(deviceId));
        Platform = platform ?? throw new ArgumentNullException(nameof(platform));
        SupportedBiometrics = supportedBiometrics ?? throw new ArgumentNullException(nameof(supportedBiometrics));

        EnabledBiometrics = new List<string>();
        IsActive = true;
        EnrolledAt = DateTime.UtcNow;
        BiometricData = new Dictionary<string, object>();
        SecuritySettings = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();

        SetDefaultSecuritySettings();

        AddDomainEvent(new BiometricProfileCreatedEvent(Id, UserId, DeviceId, Platform, SupportedBiometrics));
    }

    public void EnableBiometric(string biometricType, Dictionary<string, object> biometricData)
    {
        if (!SupportedBiometrics.Contains(biometricType, StringComparer.OrdinalIgnoreCase))
        {
            throw new InvalidOperationException($"Biometric type {biometricType} is not supported on this device");
        }

        if (!EnabledBiometrics.Contains(biometricType, StringComparer.OrdinalIgnoreCase))
        {
            EnabledBiometrics.Add(biometricType);
        }

        BiometricData[biometricType] = biometricData ?? throw new ArgumentNullException(nameof(biometricData));

        AddDomainEvent(new BiometricProfileBiometricEnabledEvent(Id, UserId, DeviceId, biometricType));
    }

    public void DisableBiometric(string biometricType)
    {
        if (EnabledBiometrics.Remove(biometricType))
        {
            BiometricData.Remove(biometricType);

            AddDomainEvent(new BiometricProfileBiometricDisabledEvent(Id, UserId, DeviceId, biometricType));
        }
    }

    public void UpdateLastUsed()
    {
        LastUsedAt = DateTime.UtcNow;
    }

    public void Deactivate()
    {
        IsActive = false;

        AddDomainEvent(new BiometricProfileDeactivatedEvent(Id, UserId, DeviceId));
    }

    public void Reactivate()
    {
        IsActive = true;

        AddDomainEvent(new BiometricProfileReactivatedEvent(Id, UserId, DeviceId));
    }

    public bool SupportsBiometric(string biometricType)
    {
        return SupportedBiometrics.Contains(biometricType, StringComparer.OrdinalIgnoreCase);
    }

    public bool IsBiometricEnabled(string biometricType)
    {
        return EnabledBiometrics.Contains(biometricType, StringComparer.OrdinalIgnoreCase);
    }

    public bool HasAnyBiometricEnabled()
    {
        return EnabledBiometrics.Count > 0;
    }

    public void UpdateSecuritySettings(Dictionary<string, object> settings)
    {
        SecuritySettings = settings ?? throw new ArgumentNullException(nameof(settings));

        AddDomainEvent(new BiometricProfileSecuritySettingsUpdatedEvent(Id, UserId, DeviceId));
    }

    private void SetDefaultSecuritySettings()
    {
        SecuritySettings["max_failed_attempts"] = 5;
        SecuritySettings["lockout_duration_minutes"] = 30;
        SecuritySettings["require_device_lock"] = true;
        SecuritySettings["allow_fallback_to_passcode"] = true;
        SecuritySettings["biometric_timeout_seconds"] = 30;
        SecuritySettings["require_user_presence"] = true;
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetSecuritySetting<T>(string key, T? defaultValue = default)
    {
        if (SecuritySettings.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetBiometricData<T>(string biometricType, string key, T? defaultValue = default)
    {
        if (BiometricData.TryGetValue(biometricType, out var biometricObj) &&
            biometricObj is Dictionary<string, object> biometricDict &&
            biometricDict.TryGetValue(key, out var value) &&
            value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }
}

public class BiometricAuthenticationAttempt : AggregateRoot
{
    public Guid BiometricProfileId { get; private set; }
    public Guid UserId { get; private set; }
    public string DeviceId { get; private set; }
    public string BiometricType { get; private set; } // Fingerprint, FaceID, TouchID, VoiceID
    public string Result { get; private set; } // Success, Failed, Cancelled, NotAvailable, NotEnrolled
    public DateTime AttemptedAt { get; private set; }
    public string? ErrorCode { get; private set; }
    public string? ErrorMessage { get; private set; }
    public Dictionary<string, object> AttemptContext { get; private set; }
    public Dictionary<string, object> SecurityMetrics { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    public virtual BiometricProfile BiometricProfile { get; private set; } = null!;

    private BiometricAuthenticationAttempt() { } // EF Core

    public BiometricAuthenticationAttempt(
        Guid biometricProfileId,
        Guid userId,
        string deviceId,
        string biometricType,
        string result,
        Dictionary<string, object> attemptContext)
    {
        BiometricProfileId = biometricProfileId;
        UserId = userId;
        DeviceId = deviceId ?? throw new ArgumentNullException(nameof(deviceId));
        BiometricType = biometricType ?? throw new ArgumentNullException(nameof(biometricType));
        Result = result ?? throw new ArgumentNullException(nameof(result));
        AttemptContext = attemptContext ?? throw new ArgumentNullException(nameof(attemptContext));

        AttemptedAt = DateTime.UtcNow;
        SecurityMetrics = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();

        AddDomainEvent(new BiometricAuthenticationAttemptCreatedEvent(Id, UserId, DeviceId, BiometricType, Result));
    }

    public void SetError(string errorCode, string errorMessage)
    {
        ErrorCode = errorCode;
        ErrorMessage = errorMessage;
    }

    public void AddSecurityMetric(string key, object value)
    {
        SecurityMetrics[key] = value;
    }

    public bool IsSuccessful()
    {
        return Result.Equals("Success", StringComparison.OrdinalIgnoreCase);
    }

    public bool IsFailed()
    {
        return Result.Equals("Failed", StringComparison.OrdinalIgnoreCase);
    }

    public bool WasCancelled()
    {
        return Result.Equals("Cancelled", StringComparison.OrdinalIgnoreCase);
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetAttemptContext<T>(string key, T? defaultValue = default)
    {
        if (AttemptContext.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetSecurityMetric<T>(string key, T? defaultValue = default)
    {
        if (SecurityMetrics.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }
}

public class BiometricPolicy : AggregateRoot
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public string Platform { get; private set; } // iOS, Android, Windows, All
    public bool IsActive { get; private set; }
    public bool IsDefault { get; private set; }
    public Dictionary<string, object> PolicyRules { get; private set; }
    public Dictionary<string, object> SecurityRequirements { get; private set; }
    public Dictionary<string, object> FallbackOptions { get; private set; }
    public List<string> AllowedBiometrics { get; private set; }
    public List<string> RequiredBiometrics { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? UpdatedAt { get; private set; }
    public string CreatedBy { get; private set; }
    public string? UpdatedBy { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    private BiometricPolicy() { } // EF Core

    public BiometricPolicy(
        string name,
        string description,
        string platform,
        Dictionary<string, object> policyRules,
        string createdBy)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        Platform = platform ?? throw new ArgumentNullException(nameof(platform));
        PolicyRules = policyRules ?? throw new ArgumentNullException(nameof(policyRules));
        CreatedBy = createdBy ?? throw new ArgumentNullException(nameof(createdBy));

        IsActive = true;
        IsDefault = false;
        CreatedAt = DateTime.UtcNow;
        SecurityRequirements = new Dictionary<string, object>();
        FallbackOptions = new Dictionary<string, object>();
        AllowedBiometrics = new List<string>();
        RequiredBiometrics = new List<string>();
        Metadata = new Dictionary<string, object>();

        SetDefaultPolicyRules();
        SetDefaultSecurityRequirements();
        SetDefaultFallbackOptions();

        AddDomainEvent(new BiometricPolicyCreatedEvent(Id, Name, Platform, CreatedBy));
    }

    public void UpdatePolicyRules(Dictionary<string, object> policyRules, string updatedBy)
    {
        PolicyRules = policyRules ?? throw new ArgumentNullException(nameof(policyRules));
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new BiometricPolicyPolicyRulesUpdatedEvent(Id, Name, UpdatedBy));
    }

    public void UpdateSecurityRequirements(Dictionary<string, object> securityRequirements, string updatedBy)
    {
        SecurityRequirements = securityRequirements ?? throw new ArgumentNullException(nameof(securityRequirements));
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new BiometricPolicySecurityRequirementsUpdatedEvent(Id, Name, UpdatedBy));
    }

    public void UpdateFallbackOptions(Dictionary<string, object> fallbackOptions, string updatedBy)
    {
        FallbackOptions = fallbackOptions ?? throw new ArgumentNullException(nameof(fallbackOptions));
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new BiometricPolicyFallbackOptionsUpdatedEvent(Id, Name, UpdatedBy));
    }

    public void SetAsDefault(string updatedBy)
    {
        IsDefault = true;
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new BiometricPolicySetAsDefaultEvent(Id, Name, UpdatedBy));
    }

    public void RemoveAsDefault(string updatedBy)
    {
        IsDefault = false;
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new BiometricPolicyRemovedAsDefaultEvent(Id, Name, UpdatedBy));
    }

    public void Activate(string updatedBy)
    {
        IsActive = true;
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new BiometricPolicyActivatedEvent(Id, Name, UpdatedBy));
    }

    public void Deactivate(string updatedBy)
    {
        IsActive = false;
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new BiometricPolicyDeactivatedEvent(Id, Name, UpdatedBy));
    }

    public bool AppliesToPlatform(string platform)
    {
        return Platform.Equals("All", StringComparison.OrdinalIgnoreCase) ||
               Platform.Equals(platform, StringComparison.OrdinalIgnoreCase);
    }

    public bool IsBiometricAllowed(string biometricType)
    {
        return AllowedBiometrics.Count == 0 || // Empty list means all are allowed
               AllowedBiometrics.Contains(biometricType, StringComparer.OrdinalIgnoreCase);
    }

    public bool IsBiometricRequired(string biometricType)
    {
        return RequiredBiometrics.Contains(biometricType, StringComparer.OrdinalIgnoreCase);
    }

    private void SetDefaultPolicyRules()
    {
        PolicyRules["max_failed_attempts"] = 5;
        PolicyRules["lockout_duration_minutes"] = 30;
        PolicyRules["session_timeout_minutes"] = 15;
        PolicyRules["require_reauthentication_minutes"] = 60;
        PolicyRules["allow_multiple_biometrics"] = true;
        PolicyRules["require_device_lock"] = true;
    }

    private void SetDefaultSecurityRequirements()
    {
        SecurityRequirements["minimum_security_level"] = "Medium";
        SecurityRequirements["require_hardware_security"] = true;
        SecurityRequirements["allow_rooted_devices"] = false;
        SecurityRequirements["require_screen_lock"] = true;
        SecurityRequirements["minimum_os_version"] = new Dictionary<string, string>
        {
            ["iOS"] = "12.0",
            ["Android"] = "8.0"
        };
    }

    private void SetDefaultFallbackOptions()
    {
        FallbackOptions["allow_passcode_fallback"] = true;
        FallbackOptions["allow_pattern_fallback"] = true;
        FallbackOptions["allow_pin_fallback"] = true;
        FallbackOptions["require_fallback_after_failures"] = 3;
        FallbackOptions["fallback_timeout_seconds"] = 300;
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
        UpdatedAt = DateTime.UtcNow;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetPolicyRule<T>(string key, T? defaultValue = default)
    {
        if (PolicyRules.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetSecurityRequirement<T>(string key, T? defaultValue = default)
    {
        if (SecurityRequirements.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetFallbackOption<T>(string key, T? defaultValue = default)
    {
        if (FallbackOptions.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }
}


