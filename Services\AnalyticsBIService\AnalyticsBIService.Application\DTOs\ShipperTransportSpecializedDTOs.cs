using System;
using System.Collections.Generic;

namespace AnalyticsBIService.Application.DTOs
{
    /// <summary>
    /// Shipper competitive benchmarking data transfer object
    /// </summary>
    public class ShipperCompetitiveBenchmarkingDto
    {
        public List<BenchmarkMetricDto> BenchmarkMetrics { get; set; } = new();
        public List<CompetitorComparisonDto> CompetitorComparisons { get; set; } = new();
        public List<MarketPositionDto> MarketPositions { get; set; } = new();
        public List<ImprovementOpportunityDto> ImprovementOpportunities { get; set; } = new();
    }

    /// <summary>
    /// Improvement opportunity data transfer object
    /// </summary>
    public class ImprovementOpportunityDto
    {
        public string OpportunityType { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public decimal PotentialImpact { get; set; }
        public string Priority { get; set; } = string.Empty;
        public string ActionPlan { get; set; } = string.Empty;
        public DateTime TargetDate { get; set; }
    }

    /// <summary>
    /// Shipper supply chain analytics data transfer object
    /// </summary>
    public class ShipperSupplyChainAnalyticsDto
    {
        public List<SupplyChainMetricDto> SupplyChainMetrics { get; set; } = new();
        public List<SupplierPerformanceDto> SupplierPerformance { get; set; } = new();
        public List<InventoryAnalyticsDto> InventoryAnalytics { get; set; } = new();
        public List<LogisticsEfficiencyDto> LogisticsEfficiency { get; set; } = new();
    }

    /// <summary>
    /// Supply chain metric data transfer object
    /// </summary>
    public class SupplyChainMetricDto
    {
        public string MetricName { get; set; } = string.Empty;
        public decimal CurrentValue { get; set; }
        public decimal TargetValue { get; set; }
        public decimal Variance { get; set; }
        public string Status { get; set; } = string.Empty;
        public string Unit { get; set; } = string.Empty;
    }

    /// <summary>
    /// Supplier performance data transfer object
    /// </summary>
    public class SupplierPerformanceDto
    {
        public Guid SupplierId { get; set; }
        public string SupplierName { get; set; } = string.Empty;
        public decimal OnTimeDeliveryRate { get; set; }
        public decimal QualityScore { get; set; }
        public decimal CostEfficiency { get; set; }
        public decimal OverallRating { get; set; }
        public List<PerformanceIssueDto> Issues { get; set; } = new();
    }

    /// <summary>
    /// Performance issue data transfer object
    /// </summary>
    public class PerformanceIssueDto
    {
        public string IssueType { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public DateTime IssueDate { get; set; }
        public string Status { get; set; } = string.Empty;
        public decimal ImpactScore { get; set; }
    }

    /// <summary>
    /// Inventory analytics data transfer object
    /// </summary>
    public class InventoryAnalyticsDto
    {
        public string ProductCategory { get; set; } = string.Empty;
        public decimal CurrentStock { get; set; }
        public decimal OptimalStock { get; set; }
        public decimal TurnoverRate { get; set; }
        public decimal CarryingCost { get; set; }
        public string StockStatus { get; set; } = string.Empty;
    }

    /// <summary>
    /// Logistics efficiency data transfer object
    /// </summary>
    public class LogisticsEfficiencyDto
    {
        public string ProcessName { get; set; } = string.Empty;
        public decimal EfficiencyScore { get; set; }
        public decimal CostPerUnit { get; set; }
        public decimal TimePerUnit { get; set; }
        public List<EfficiencyTrendDto> Trends { get; set; } = new();
    }

    /// <summary>
    /// SLA trend data transfer object
    /// </summary>
    public class SLATrendDto
    {
        public DateTime Period { get; set; }
        public decimal SLACompliance { get; set; }
        public int TotalSLAs { get; set; }
        public int MetSLAs { get; set; }
        public int MissedSLAs { get; set; }
        public decimal AverageResponseTime { get; set; }
    }

    /// <summary>
    /// Shipper cost trend data transfer object
    /// </summary>
    public class ShipperCostTrendDto
    {
        public DateTime Period { get; set; }
        public decimal TotalCost { get; set; }
        public decimal TransportationCost { get; set; }
        public decimal WarehouseCost { get; set; }
        public decimal InventoryCost { get; set; }
        public decimal CostPerUnit { get; set; }
    }

    /// <summary>
    /// Shipper monthly trend data transfer object
    /// </summary>
    public class ShipperMonthlyTrendDto
    {
        public DateTime Month { get; set; }
        public decimal TotalShipments { get; set; }
        public decimal TotalCost { get; set; }
        public decimal AverageCostPerShipment { get; set; }
        public decimal OnTimeDeliveryRate { get; set; }
        public decimal CustomerSatisfactionScore { get; set; }
    }

    /// <summary>
    /// Transport company predictive insights data transfer object
    /// </summary>
    public class TransportCompanyPredictiveInsightsDto
    {
        public List<PredictiveMetricDto> PredictiveMetrics { get; set; } = new();
        public List<ForecastDto> Forecasts { get; set; } = new();
        public List<RiskPredictionDto> RiskPredictions { get; set; } = new();
        public List<OpportunityPredictionDto> OpportunityPredictions { get; set; } = new();

        // Additional properties required by query handlers
        public Guid TransportCompanyId { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public int ForecastDays { get; set; }
    }

    /// <summary>
    /// Predictive metric data transfer object
    /// </summary>
    public class PredictiveMetricDto
    {
        public string MetricName { get; set; } = string.Empty;
        public decimal CurrentValue { get; set; }
        public decimal PredictedValue { get; set; }
        public decimal Confidence { get; set; }
        public DateTime PredictionDate { get; set; }
        public string Trend { get; set; } = string.Empty;
    }

    /// <summary>
    /// Forecast data transfer object
    /// </summary>
    public class ForecastDto
    {
        public string ForecastType { get; set; } = string.Empty;
        public DateTime Period { get; set; }
        public decimal ForecastedValue { get; set; }
        public decimal ConfidenceInterval { get; set; }
        public string Methodology { get; set; } = string.Empty;
    }

    /// <summary>
    /// Risk prediction data transfer object
    /// </summary>
    public class RiskPredictionDto
    {
        public string RiskType { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public decimal Probability { get; set; }
        public decimal Impact { get; set; }
        public string MitigationStrategy { get; set; } = string.Empty;
        public DateTime PredictedDate { get; set; }
    }

    /// <summary>
    /// Opportunity prediction data transfer object
    /// </summary>
    public class OpportunityPredictionDto
    {
        public string OpportunityType { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public decimal PotentialValue { get; set; }
        public decimal Probability { get; set; }
        public string ActionRequired { get; set; } = string.Empty;
        public DateTime OpportunityDate { get; set; }
    }

    /// <summary>
    /// Customer specific analytics data transfer object
    /// </summary>
    public class CustomerSpecificAnalyticsDto
    {
        public Guid CustomerId { get; set; }
        public string CustomerName { get; set; } = string.Empty;
        public List<CustomerMetricDto> CustomerMetrics { get; set; } = new();
        public List<CustomerTrendDto> CustomerTrends { get; set; } = new();
        public List<CustomerInsightDto> CustomerInsights { get; set; } = new();

        // Additional properties required by query handlers
        public Guid TransportCompanyId { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
    }

    /// <summary>
    /// Customer metric data transfer object
    /// </summary>
    public class CustomerMetricDto
    {
        public string MetricName { get; set; } = string.Empty;
        public decimal Value { get; set; }
        public string Unit { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
    }

    /// <summary>
    /// Customer trend data transfer object
    /// </summary>
    public class CustomerTrendDto
    {
        public DateTime Period { get; set; }
        public string MetricName { get; set; } = string.Empty;
        public decimal Value { get; set; }
        public string Trend { get; set; } = string.Empty;
    }

    /// <summary>
    /// Customer insight data transfer object
    /// </summary>
    public class CustomerInsightDto
    {
        public string InsightType { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public decimal Impact { get; set; }
        public string Recommendation { get; set; } = string.Empty;
        public string Priority { get; set; } = string.Empty;
    }

    /// <summary>
    /// Competitive analysis data transfer object
    /// </summary>
    public class CompetitiveAnalysisDto
    {
        public List<CompetitorAnalysisDto> CompetitorAnalyses { get; set; } = new();
        public List<MarketTrendDto> MarketTrends { get; set; } = new();
        public List<CompetitivePositionDto> CompetitivePositions { get; set; } = new();
        public List<StrategicRecommendationDto> StrategicRecommendations { get; set; } = new();

        // Additional properties required by GetTransportCompanyCostAnalyticsQueryHandler
        public string CompetitorName { get; set; } = string.Empty;
        public decimal MarketShare { get; set; }
        public decimal GrowthRate { get; set; }
        public List<string> Strengths { get; set; } = new();
        public List<string> Weaknesses { get; set; } = new();
    }



    /// <summary>
    /// Competitor strength data transfer object
    /// </summary>
    public class CompetitorStrengthDto
    {
        public string Strength { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public decimal Impact { get; set; }
    }

    /// <summary>
    /// Competitor weakness data transfer object
    /// </summary>
    public class CompetitorWeaknessDto
    {
        public string Weakness { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public decimal Opportunity { get; set; }
    }

    /// <summary>
    /// Competitive position data transfer object
    /// </summary>
    public class CompetitivePositionDto
    {
        public string Market { get; set; } = string.Empty;
        public decimal MarketShare { get; set; }
        public int Ranking { get; set; }
        public string Position { get; set; } = string.Empty;
        public string Trend { get; set; } = string.Empty;
    }

    /// <summary>
    /// Strategic recommendation data transfer object
    /// </summary>
    public class StrategicRecommendationDto
    {
        public string RecommendationType { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public decimal PotentialImpact { get; set; }
        public string Implementation { get; set; } = string.Empty;
        public string Priority { get; set; } = string.Empty;
        public DateTime TargetDate { get; set; }
    }

    /// <summary>
    /// Trend data transfer object
    /// </summary>
    public class TrendDto
    {
        public DateTime Period { get; set; }
        public string TrendName { get; set; } = string.Empty;
        public decimal Value { get; set; }
        public string Direction { get; set; } = string.Empty;
        public decimal ChangePercentage { get; set; }
        public string Significance { get; set; } = string.Empty;
    }
}
