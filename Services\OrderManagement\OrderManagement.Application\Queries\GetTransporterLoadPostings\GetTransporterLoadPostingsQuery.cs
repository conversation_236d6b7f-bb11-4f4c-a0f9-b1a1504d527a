using MediatR;
using OrderManagement.Application.DTOs;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Application.Queries.GetTransporterLoadPostings;

public class GetTransporterLoadPostingsQuery : IRequest<TransporterLoadPostingAnalyticsDto>
{
    public Guid TransporterId { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public List<string>? StatusFilter { get; set; } // open, quoted, expired, converted
    public List<Guid>? BrokerIds { get; set; } // Filter by specific brokers
    public List<LoadType>? LoadTypes { get; set; } // Filter by load types
    public List<string>? Routes { get; set; } // Filter by specific routes
    public bool IncludeDailyBreakdown { get; set; } = true;
    public bool IncludeBrokerBreakdown { get; set; } = true;
    public bool IncludeStatusBreakdown { get; set; } = true;
    public bool IncludeRouteBreakdown { get; set; } = true;
    public bool IncludeConversionAnalytics { get; set; } = true;
    
    public GetTransporterLoadPostingsQuery()
    {
        // Default to last 30 days if no dates specified
        ToDate = DateTime.UtcNow.Date.AddDays(1).AddTicks(-1); // End of today
        FromDate = ToDate.Value.AddDays(-30); // 30 days ago
    }
    
    public GetTransporterLoadPostingsQuery(Guid transporterId, DateTime? fromDate = null, DateTime? toDate = null)
    {
        TransporterId = transporterId;
        FromDate = fromDate ?? DateTime.UtcNow.Date.AddDays(-30);
        ToDate = toDate ?? DateTime.UtcNow.Date.AddDays(1).AddTicks(-1);
    }
}
