﻿using System.Diagnostics;
using System.Diagnostics.Metrics;

namespace Shared.Infrastructure.Monitoring;

public interface IPerformanceMetrics
{
    void RecordRequest(string endpoint, double duration, string status);
    void RecordDatabaseQuery(string queryType, double duration);
    void RecordCacheOperation(string operation, bool hit);
    void RecordMemoryUsage(long bytes);
    IDisposable StartTimer(string operationName);
}

public class PerformanceMetrics : IPerformanceMetrics
{
    private readonly Meter _meter;
    private readonly Counter<long> _requestCounter;
    private readonly Histogram<double> _requestDuration;
    private readonly Counter<long> _databaseQueryCounter;
    private readonly Histogram<double> _databaseQueryDuration;
    private readonly Counter<long> _cacheHitCounter;
    private readonly Counter<long> _cacheMissCounter;
    private readonly Histogram<long> _memoryUsage;

    public PerformanceMetrics(IMeterFactory meterFactory)
    {
        _meter = meterFactory.Create("TLI.Performance");

        _requestCounter = _meter.CreateCounter<long>(
            "http_requests_total",
            description: "Total number of HTTP requests");

        _requestDuration = _meter.CreateHistogram<double>(
            "http_request_duration_seconds",
            description: "Duration of HTTP requests in seconds");

        _databaseQueryCounter = _meter.CreateCounter<long>(
            "database_queries_total",
            description: "Total number of database queries");

        _databaseQueryDuration = _meter.CreateHistogram<double>(
            "database_query_duration_seconds",
            description: "Duration of database queries in seconds");

        _cacheHitCounter = _meter.CreateCounter<long>(
            "cache_hits_total",
            description: "Total number of cache hits");

        _cacheMissCounter = _meter.CreateCounter<long>(
            "cache_misses_total",
            description: "Total number of cache misses");

        _memoryUsage = _meter.CreateHistogram<long>(
            "memory_usage_bytes",
            description: "Memory usage in bytes");
    }

    public void RecordRequest(string endpoint, double duration, string status)
    {
        var tags = new TagList
        {
            { "endpoint", endpoint },
            { "status", status }
        };

        _requestCounter.Add(1, tags);
        _requestDuration.Record(duration, tags);
    }

    public void RecordDatabaseQuery(string queryType, double duration)
    {
        var tags = new TagList
        {
            { "query_type", queryType }
        };

        _databaseQueryCounter.Add(1, tags);
        _databaseQueryDuration.Record(duration, tags);
    }

    public void RecordCacheOperation(string operation, bool hit)
    {
        var tags = new TagList
        {
            { "operation", operation }
        };

        if (hit)
        {
            _cacheHitCounter.Add(1, tags);
        }
        else
        {
            _cacheMissCounter.Add(1, tags);
        }
    }

    public void RecordMemoryUsage(long bytes)
    {
        _memoryUsage.Record(bytes);
    }

    public IDisposable StartTimer(string operationName)
    {
        return new OperationTimer(operationName, this);
    }

    private class OperationTimer : IDisposable
    {
        private readonly string _operationName;
        private readonly PerformanceMetrics _metrics;
        private readonly Stopwatch _stopwatch;

        public OperationTimer(string operationName, PerformanceMetrics metrics)
        {
            _operationName = operationName;
            _metrics = metrics;
            _stopwatch = Stopwatch.StartNew();
        }

        public void Dispose()
        {
            _stopwatch.Stop();
            var duration = _stopwatch.Elapsed.TotalSeconds;

            // Record the operation duration
            var tags = new TagList
            {
                { "operation", _operationName }
            };

            _metrics._requestDuration.Record(duration, tags);
        }
    }
}

public static class PerformanceMetricsExtensions
{
    public static async Task<T> MeasureAsync<T>(this IPerformanceMetrics metrics, string operationName, Func<Task<T>> operation)
    {
        using var timer = metrics.StartTimer(operationName);
        return await operation();
    }

    public static async Task MeasureAsync(this IPerformanceMetrics metrics, string operationName, Func<Task> operation)
    {
        using var timer = metrics.StartTimer(operationName);
        await operation();
    }
}


