using UserManagement.Application.DTOs;
using UserManagement.Domain.Entities;

namespace UserManagement.Application.Interfaces;

public interface IFeedbackRepository
{
    Task<List<OrderFeedbackDto>> GetFeedbacksByUserIdAsync(Guid userId);
    Task<FeedbackAnalyticsResult> GetAnalyticsAsync(FeedbackAnalyticsQuery query);
    Task<List<OrderFeedbackDto>> GetRecentFeedbacksAsync(int count);

    // Missing methods needed by EnhancedFeedbackService
    Task<List<Feedback>> GetRecentFeedbackAsync(Guid? userId, DateTime fromDate, CancellationToken cancellationToken = default);
    Task<List<Feedback>> GetFeedbackForTrendAnalysisAsync(FeedbackTrendQuery query, CancellationToken cancellationToken = default);
    Task<List<OrderFeedbackDto>> GetOrderFeedbackAsync(Guid orderId, CancellationToken cancellationToken = default);
    Task<List<OrderFeedbackDto>> GetUserFeedbackAsync(Guid userId, CancellationToken cancellationToken = default);
    Task SaveRedFlagAsync(RedFlagAlert redFlag, CancellationToken cancellationToken = default);
    Task UpdateUserAverageRatingAsync(Guid userId, decimal averageRating, CancellationToken cancellationToken = default);
    Task<List<Feedback>> GetFeedbackForAnalyticsAsync(FeedbackAnalyticsQuery query, CancellationToken cancellationToken = default);
    Task AddAsync(Feedback feedback, CancellationToken cancellationToken = default);
}
