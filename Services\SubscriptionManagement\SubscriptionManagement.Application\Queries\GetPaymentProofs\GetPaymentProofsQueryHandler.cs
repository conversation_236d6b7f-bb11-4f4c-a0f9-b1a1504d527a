using MediatR;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.DTOs;
using SubscriptionManagement.Domain.Interfaces;

namespace SubscriptionManagement.Application.Queries.GetPaymentProofs
{
    public class GetPaymentProofsQueryHandler : IRequestHandler<GetPaymentProofsQuery, PaymentProofListDto>
    {
        private readonly ISubscriptionPaymentProofRepository _paymentProofRepository;
        private readonly ILogger<GetPaymentProofsQueryHandler> _logger;

        public GetPaymentProofsQueryHandler(
            ISubscriptionPaymentProofRepository paymentProofRepository,
            ILogger<GetPaymentProofsQueryHandler> logger)
        {
            _paymentProofRepository = paymentProofRepository;
            _logger = logger;
        }

        public async Task<PaymentProofListDto> Handle(GetPaymentProofsQuery request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Getting payment proofs with filters: Status={Status}, UserId={UserId}, SubscriptionId={SubscriptionId}, Page={PageNumber}", 
                request.Status, request.UserId, request.SubscriptionId, request.PageNumber);

            var (items, totalCount) = await _paymentProofRepository.GetPagedAsync(
                request.PageNumber,
                request.PageSize,
                request.Status,
                request.UserId,
                request.SubscriptionId,
                request.FromDate,
                request.ToDate,
                cancellationToken);

            var paymentProofDtos = items.Select(p => new SubscriptionPaymentProofDto
            {
                Id = p.Id,
                SubscriptionId = p.SubscriptionId,
                UserId = p.UserId,
                Amount = p.Amount.Amount,
                Currency = p.Amount.Currency,
                PaymentDate = p.PaymentDate,
                ProofImageUrl = p.ProofImageUrl,
                Notes = p.Notes,
                Status = p.Status,
                PaymentMethod = p.PaymentMethod,
                TransactionReference = p.TransactionReference,
                VerifiedByUserId = p.VerifiedByUserId,
                VerifiedAt = p.VerifiedAt,
                VerificationNotes = p.VerificationNotes,
                RejectionReason = p.RejectionReason,
                CreatedAt = p.CreatedAt,
                UpdatedAt = p.UpdatedAt
            }).ToList();

            var totalPages = (int)Math.Ceiling((double)totalCount / request.PageSize);

            return new PaymentProofListDto
            {
                Items = paymentProofDtos,
                TotalCount = totalCount,
                PageNumber = request.PageNumber,
                PageSize = request.PageSize,
                TotalPages = totalPages
            };
        }
    }
}
