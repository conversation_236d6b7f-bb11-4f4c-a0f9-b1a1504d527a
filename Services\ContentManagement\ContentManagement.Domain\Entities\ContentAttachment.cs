using ContentManagement.Domain.Enums;
using Shared.Domain.Common;
using Shared.Domain.Common;

namespace ContentManagement.Domain.Entities;

/// <summary>
/// Represents an attachment linked to content (references DataStorage service documents)
/// </summary>
public class ContentAttachment : BaseEntity
{
    public Guid ContentId { get; private set; }
    public Guid DocumentId { get; private set; } // Reference to DataStorage.Document
    public AttachmentType Type { get; private set; }
    public string DisplayName { get; private set; }
    public string? Description { get; private set; }
    public int SortOrder { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public Guid CreatedBy { get; private set; }
    public string CreatedByName { get; private set; }

    private ContentAttachment()
    {
        DisplayName = string.Empty;
        CreatedByName = string.Empty;
    }

    public ContentAttachment(
        Guid contentId,
        Guid documentId,
        AttachmentType type,
        string displayName,
        Guid createdBy,
        string createdByName,
        string? description = null,
        int sortOrder = 0)
    {
        if (string.IsNullOrWhiteSpace(displayName))
            throw new ArgumentException("Display name cannot be empty", nameof(displayName));
        if (string.IsNullOrWhiteSpace(createdByName))
            throw new ArgumentException("Created by name cannot be empty", nameof(createdByName));

        Id = Guid.NewGuid();
        ContentId = contentId;
        DocumentId = documentId;
        Type = type;
        DisplayName = displayName;
        Description = description;
        SortOrder = sortOrder;
        CreatedBy = createdBy;
        CreatedByName = createdByName;
        CreatedAt = DateTime.UtcNow;
    }

    public void UpdateDisplayName(string displayName)
    {
        if (string.IsNullOrWhiteSpace(displayName))
            throw new ArgumentException("Display name cannot be empty", nameof(displayName));

        DisplayName = displayName;
    }

    public void UpdateDescription(string? description)
    {
        Description = description;
    }

    public void UpdateSortOrder(int sortOrder)
    {
        SortOrder = sortOrder;
    }
}



