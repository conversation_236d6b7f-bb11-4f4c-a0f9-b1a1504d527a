using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using AuditCompliance.Application.Interfaces;
using AuditCompliance.Application.DTOs.Risk;
using AuditCompliance.Infrastructure.MultiTenant;

namespace AuditCompliance.API.Controllers;

/// <summary>
/// Controller for risk assessment and management
/// </summary>
[ApiController]
[Route("api/v{version:apiVersion}/[controller]")]
[Route("api/[controller]")] // Backward compatibility
[ApiVersion("1.0")]
[ApiVersion("2.0")]
[Authorize]
[RequireTenant]
public class RiskAssessmentController : ControllerBase
{
    private readonly IRiskAssessmentService _riskAssessmentService;
    private readonly IRiskScoringEngine _scoringEngine;
    private readonly IRiskMitigationEngine _mitigationEngine;
    private readonly ILogger<RiskAssessmentController> _logger;

    public RiskAssessmentController(
        IRiskAssessmentService riskAssessmentService,
        IRiskScoringEngine scoringEngine,
        IRiskMitigationEngine mitigationEngine,
        ILogger<RiskAssessmentController> logger)
    {
        _riskAssessmentService = riskAssessmentService;
        _scoringEngine = scoringEngine;
        _mitigationEngine = mitigationEngine;
        _logger = logger;
    }

    /// <summary>
    /// Calculate risk score for an entity
    /// </summary>
    [HttpPost("calculate")]
    [Authorize(Roles = "Admin,ComplianceOfficer,Auditor,RiskManager")]
    public async Task<ActionResult<RiskScoreDto>> CalculateRiskScore([FromBody] RiskAssessmentRequestDto request)
    {
        try
        {
            var riskScore = await _riskAssessmentService.CalculateRiskScoreAsync(request);
            return Ok(riskScore);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating risk score for entity {EntityId}", request.EntityId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get risk assessment for an entity
    /// </summary>
    [HttpGet("{entityId}")]
    [Authorize(Roles = "Admin,ComplianceOfficer,Auditor,RiskManager")]
    public async Task<ActionResult<RiskAssessmentDto>> GetRiskAssessment(
        Guid entityId,
        [FromQuery] string entityType = "Organization")
    {
        try
        {
            var assessment = await _riskAssessmentService.GetRiskAssessmentAsync(entityId, entityType);
            if (assessment == null)
            {
                return NotFound($"Risk assessment not found for entity {entityId}");
            }

            return Ok(assessment);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting risk assessment for entity {EntityId}", entityId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Create or update risk assessment
    /// </summary>
    [HttpPost("assessments")]
    [Authorize(Roles = "Admin,ComplianceOfficer,RiskManager")]
    public async Task<ActionResult<object>> CreateRiskAssessment([FromBody] CreateRiskAssessmentDto assessmentDto)
    {
        try
        {
            var assessmentId = await _riskAssessmentService.CreateOrUpdateRiskAssessmentAsync(assessmentDto);
            
            var response = new
            {
                AssessmentId = assessmentId,
                Message = "Risk assessment created successfully",
                Links = new
                {
                    Self = $"/api/risk-assessment/{assessmentDto.EntityId}?entityType={assessmentDto.EntityType}",
                    Mitigations = $"/api/risk-assessment/{assessmentDto.EntityId}/mitigations"
                }
            };

            return CreatedAtAction(nameof(GetRiskAssessment), 
                new { entityId = assessmentDto.EntityId, entityType = assessmentDto.EntityType }, response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating risk assessment for entity {EntityId}", assessmentDto.EntityId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get risk factors configuration
    /// </summary>
    [HttpGet("factors")]
    [Authorize(Roles = "Admin,ComplianceOfficer,Auditor,RiskManager")]
    public async Task<ActionResult<List<RiskFactorDto>>> GetRiskFactors([FromQuery] string? category = null)
    {
        try
        {
            var factors = await _riskAssessmentService.GetRiskFactorsAsync(category);
            return Ok(factors);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting risk factors");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Create or update risk factor
    /// </summary>
    [HttpPost("factors")]
    [Authorize(Roles = "Admin,RiskManager")]
    public async Task<ActionResult<object>> CreateRiskFactor([FromBody] CreateRiskFactorDto factorDto)
    {
        try
        {
            var factorId = await _riskAssessmentService.CreateOrUpdateRiskFactorAsync(factorDto);
            
            var response = new
            {
                FactorId = factorId,
                Message = "Risk factor created successfully"
            };

            return CreatedAtAction(nameof(GetRiskFactors), response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating risk factor");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Delete risk factor
    /// </summary>
    [HttpDelete("factors/{factorId}")]
    [Authorize(Roles = "Admin,RiskManager")]
    public async Task<ActionResult<object>> DeleteRiskFactor(Guid factorId)
    {
        try
        {
            var success = await _riskAssessmentService.DeleteRiskFactorAsync(factorId);
            if (!success)
            {
                return NotFound($"Risk factor with ID {factorId} not found");
            }

            return Ok(new { Message = "Risk factor deleted successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting risk factor {FactorId}", factorId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get risk thresholds configuration
    /// </summary>
    [HttpGet("thresholds")]
    [Authorize(Roles = "Admin,ComplianceOfficer,Auditor,RiskManager")]
    public async Task<ActionResult<List<RiskThresholdDto>>> GetRiskThresholds()
    {
        try
        {
            var thresholds = await _riskAssessmentService.GetRiskThresholdsAsync();
            return Ok(thresholds);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting risk thresholds");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Update risk thresholds
    /// </summary>
    [HttpPut("thresholds")]
    [Authorize(Roles = "Admin,RiskManager")]
    public async Task<ActionResult<object>> UpdateRiskThresholds([FromBody] List<RiskThresholdDto> thresholds)
    {
        try
        {
            var success = await _riskAssessmentService.UpdateRiskThresholdsAsync(thresholds);
            if (!success)
            {
                return BadRequest("Failed to update risk thresholds");
            }

            return Ok(new { Message = "Risk thresholds updated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating risk thresholds");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get risk mitigation recommendations
    /// </summary>
    [HttpGet("{entityId}/mitigations")]
    [Authorize(Roles = "Admin,ComplianceOfficer,Auditor,RiskManager")]
    public async Task<ActionResult<List<RiskMitigationDto>>> GetRiskMitigationRecommendations(
        Guid entityId,
        [FromQuery] string entityType = "Organization")
    {
        try
        {
            var recommendations = await _riskAssessmentService.GetRiskMitigationRecommendationsAsync(entityId, entityType);
            return Ok(recommendations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting risk mitigation recommendations for entity {EntityId}", entityId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Create risk mitigation plan
    /// </summary>
    [HttpPost("mitigation-plans")]
    [Authorize(Roles = "Admin,ComplianceOfficer,RiskManager")]
    public async Task<ActionResult<object>> CreateRiskMitigationPlan([FromBody] CreateRiskMitigationPlanDto planDto)
    {
        try
        {
            var planId = await _riskAssessmentService.CreateRiskMitigationPlanAsync(planDto);
            
            var response = new
            {
                PlanId = planId,
                Message = "Risk mitigation plan created successfully",
                Links = new
                {
                    Self = $"/api/risk-assessment/mitigation-plans/{planId}"
                }
            };

            return CreatedAtAction(nameof(GetRiskMitigationPlan), new { planId }, response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating risk mitigation plan");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get risk mitigation plan
    /// </summary>
    [HttpGet("mitigation-plans/{planId}")]
    [Authorize(Roles = "Admin,ComplianceOfficer,Auditor,RiskManager")]
    public async Task<ActionResult<RiskMitigationPlanDto>> GetRiskMitigationPlan(Guid planId)
    {
        try
        {
            var plan = await _riskAssessmentService.GetRiskMitigationPlanAsync(planId);
            if (plan == null)
            {
                return NotFound($"Risk mitigation plan with ID {planId} not found");
            }

            return Ok(plan);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting risk mitigation plan {PlanId}", planId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Update risk mitigation plan
    /// </summary>
    [HttpPut("mitigation-plans/{planId}")]
    [Authorize(Roles = "Admin,ComplianceOfficer,RiskManager")]
    public async Task<ActionResult<object>> UpdateRiskMitigationPlan(
        Guid planId,
        [FromBody] UpdateRiskMitigationPlanDto planDto)
    {
        try
        {
            var success = await _riskAssessmentService.UpdateRiskMitigationPlanAsync(planId, planDto);
            if (!success)
            {
                return NotFound($"Risk mitigation plan with ID {planId} not found");
            }

            return Ok(new { Message = "Risk mitigation plan updated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating risk mitigation plan {PlanId}", planId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get risk trends for an entity
    /// </summary>
    [HttpGet("{entityId}/trends")]
    [Authorize(Roles = "Admin,ComplianceOfficer,Auditor,RiskManager")]
    public async Task<ActionResult<List<RiskTrendDto>>> GetRiskTrends(
        Guid entityId,
        [FromQuery] string entityType = "Organization",
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        try
        {
            var from = fromDate ?? DateTime.UtcNow.AddMonths(-6);
            var to = toDate ?? DateTime.UtcNow;

            var trends = await _riskAssessmentService.GetRiskTrendsAsync(entityId, entityType, from, to);
            return Ok(trends);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting risk trends for entity {EntityId}", entityId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get risk heat map
    /// </summary>
    [HttpPost("heat-map")]
    [Authorize(Roles = "Admin,ComplianceOfficer,Auditor,RiskManager")]
    public async Task<ActionResult<RiskHeatMapDto>> GetRiskHeatMap([FromBody] RiskHeatMapRequestDto request)
    {
        try
        {
            var heatMap = await _riskAssessmentService.GetRiskHeatMapAsync(request);
            return Ok(heatMap);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting risk heat map");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Perform bulk risk assessment
    /// </summary>
    [HttpPost("bulk-assessment")]
    [Authorize(Roles = "Admin,RiskManager")]
    public async Task<ActionResult<BulkRiskAssessmentResultDto>> PerformBulkRiskAssessment(
        [FromBody] BulkRiskAssessmentRequestDto request)
    {
        try
        {
            var result = await _riskAssessmentService.PerformBulkRiskAssessmentAsync(request);
            return Accepted(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing bulk risk assessment");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get risk assessment summary
    /// </summary>
    [HttpGet("summary")]
    [Authorize(Roles = "Admin,ComplianceOfficer,Auditor,RiskManager")]
    public async Task<ActionResult<RiskAssessmentSummaryDto>> GetRiskAssessmentSummary(
        [FromQuery] Guid? organizationId = null)
    {
        try
        {
            var summary = await _riskAssessmentService.GetRiskAssessmentSummaryAsync(organizationId);
            return Ok(summary);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting risk assessment summary");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Validate risk configuration
    /// </summary>
    [HttpGet("validate-configuration")]
    [Authorize(Roles = "Admin,RiskManager")]
    public async Task<ActionResult<RiskConfigurationValidationResult>> ValidateRiskConfiguration()
    {
        try
        {
            var result = await _riskAssessmentService.ValidateRiskConfigurationAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating risk configuration");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get risk assessment history
    /// </summary>
    [HttpGet("{entityId}/history")]
    [Authorize(Roles = "Admin,ComplianceOfficer,Auditor,RiskManager")]
    public async Task<ActionResult<List<RiskAssessmentHistoryDto>>> GetRiskAssessmentHistory(
        Guid entityId,
        [FromQuery] string entityType = "Organization",
        [FromQuery] int pageSize = 50,
        [FromQuery] int pageNumber = 1)
    {
        try
        {
            var history = await _riskAssessmentService.GetRiskAssessmentHistoryAsync(
                entityId, entityType, pageSize, pageNumber);
            return Ok(history);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting risk assessment history for entity {EntityId}", entityId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Export risk assessment data
    /// </summary>
    [HttpPost("export")]
    [Authorize(Roles = "Admin,ComplianceOfficer,Auditor,RiskManager")]
    public async Task<ActionResult<object>> ExportRiskAssessmentData([FromBody] ExportRiskDataRequestDto request)
    {
        try
        {
            var exportId = await _riskAssessmentService.ExportRiskAssessmentDataAsync(request);
            
            var response = new
            {
                ExportId = exportId,
                Message = "Risk assessment data export started",
                Status = "Processing",
                Format = request.Format,
                Links = new
                {
                    Status = $"/api/risk-assessment/exports/{exportId}/status",
                    Download = $"/api/risk-assessment/exports/{exportId}/download"
                }
            };

            return Accepted(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting risk assessment data");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Schedule automated risk assessment
    /// </summary>
    [HttpPost("schedule")]
    [Authorize(Roles = "Admin,RiskManager")]
    public async Task<ActionResult<object>> ScheduleAutomatedRiskAssessment([FromBody] ScheduleRiskAssessmentDto scheduleDto)
    {
        try
        {
            var scheduleId = await _riskAssessmentService.ScheduleAutomatedRiskAssessmentAsync(scheduleDto);
            
            var response = new
            {
                ScheduleId = scheduleId,
                Message = "Automated risk assessment scheduled successfully",
                Schedule = scheduleDto.Schedule,
                IsActive = scheduleDto.IsActive
            };

            return CreatedAtAction(nameof(GetRiskAssessmentSummary), response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error scheduling automated risk assessment");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get mitigation templates
    /// </summary>
    [HttpGet("mitigation-templates")]
    [Authorize(Roles = "Admin,ComplianceOfficer,RiskManager")]
    public async Task<ActionResult<List<MitigationTemplateDto>>> GetMitigationTemplates([FromQuery] string category = "General")
    {
        try
        {
            var templates = await _mitigationEngine.GetMitigationTemplatesAsync(category);
            return Ok(templates);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting mitigation templates for category {Category}", category);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Calculate mitigation impact
    /// </summary>
    [HttpPost("mitigation-impact")]
    [Authorize(Roles = "Admin,ComplianceOfficer,RiskManager")]
    public async Task<ActionResult<MitigationImpactDto>> CalculateMitigationImpact([FromBody] CalculateMitigationImpactRequestDto request)
    {
        try
        {
            var impact = await _mitigationEngine.CalculateMitigationImpactAsync(request.Mitigation, request.Assessment);
            return Ok(impact);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating mitigation impact");
            return StatusCode(500, "Internal server error");
        }
    }
}

/// <summary>
/// Request DTO for calculating mitigation impact
/// </summary>
public class CalculateMitigationImpactRequestDto
{
    public RiskMitigationDto Mitigation { get; set; } = new();
    public RiskAssessmentDto Assessment { get; set; } = new();
}
