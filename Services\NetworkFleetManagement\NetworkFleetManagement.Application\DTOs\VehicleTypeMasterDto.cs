using Shared.Domain.Common;

namespace NetworkFleetManagement.Application.DTOs;

/// <summary>
/// DTO for VehicleTypeMaster entity
/// </summary>
public class VehicleTypeMasterDto
{
    public Guid Id { get; set; }
    public string Code { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public decimal? MinLoadCapacityKg { get; set; }
    public decimal? MaxLoadCapacityKg { get; set; }
    public decimal? MinVolumeCapacityM3 { get; set; }
    public decimal? MaxVolumeCapacityM3 { get; set; }
    public string? SpecialRequirements { get; set; }
    public bool IsActive { get; set; }
    public int SortOrder { get; set; }
    public string? IconUrl { get; set; }
    public Dictionary<string, object> AdditionalProperties { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

/// <summary>
/// Summary DTO for VehicleTypeMaster entity (for lists and dropdowns)
/// </summary>
public class VehicleTypeMasterSummaryDto
{
    public Guid Id { get; set; }
    public string Code { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public int SortOrder { get; set; }
    public string? IconUrl { get; set; }
    public string CapacityRange { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for creating VehicleTypeMaster
/// </summary>
public class CreateVehicleTypeMasterDto
{
    public string Code { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public decimal? MinLoadCapacityKg { get; set; }
    public decimal? MaxLoadCapacityKg { get; set; }
    public decimal? MinVolumeCapacityM3 { get; set; }
    public decimal? MaxVolumeCapacityM3 { get; set; }
    public string? SpecialRequirements { get; set; }
    public int SortOrder { get; set; }
    public string? IconUrl { get; set; }
    public Dictionary<string, object>? AdditionalProperties { get; set; }
}

/// <summary>
/// Request DTO for updating VehicleTypeMaster
/// </summary>
public class UpdateVehicleTypeMasterDto
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public decimal? MinLoadCapacityKg { get; set; }
    public decimal? MaxLoadCapacityKg { get; set; }
    public decimal? MinVolumeCapacityM3 { get; set; }
    public decimal? MaxVolumeCapacityM3 { get; set; }
    public string? SpecialRequirements { get; set; }
    public int SortOrder { get; set; }
    public string? IconUrl { get; set; }
    public Dictionary<string, object>? AdditionalProperties { get; set; }
}

/// <summary>
/// Request DTO for vehicle type master search and filtering
/// </summary>
public class VehicleTypeMasterSearchDto
{
    public string? SearchTerm { get; set; }
    public string? Category { get; set; }
    public bool? IsActive { get; set; }
    public decimal? MinLoadCapacity { get; set; }
    public decimal? MaxLoadCapacity { get; set; }
    public decimal? MinVolumeCapacity { get; set; }
    public decimal? MaxVolumeCapacity { get; set; }
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 10;
}

/// <summary>
/// Response DTO for paginated vehicle type master results
/// </summary>
public class VehicleTypeMasterPagedResultDto
{
    public IEnumerable<VehicleTypeMasterSummaryDto> Items { get; set; } = new List<VehicleTypeMasterSummaryDto>();
    public int TotalCount { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
    public bool HasPreviousPage => PageNumber > 1;
    public bool HasNextPage => PageNumber < TotalPages;
}

/// <summary>
/// DTO for vehicle type master statistics
/// </summary>
public class VehicleTypeMasterStatsDto
{
    public int TotalCount { get; set; }
    public int ActiveCount { get; set; }
    public int InactiveCount { get; set; }
    public Dictionary<string, int> CategoryCounts { get; set; } = new();
    public Dictionary<string, int> UsageStatistics { get; set; } = new();
}

