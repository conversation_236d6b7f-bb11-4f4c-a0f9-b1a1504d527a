using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Application.Interfaces;

public interface ICommissionService
{
    Task<Commission> CalculateCommissionAsync(
        Guid orderId,
        Guid brokerId,
        Guid transportCompanyId,
        Guid carrierId,
        Money orderAmount,
        CommissionStructure commissionStructure);

    Task<bool> ApproveCommissionAsync(Guid commissionId, string approvedBy);
    Task<bool> PayCommissionAsync(Guid commissionId, string paymentMethodId);
    Task<bool> DisputeCommissionAsync(Guid commissionId, string reason);
    Task<bool> ResolveCommissionDisputeAsync(Guid commissionId, Money resolvedAmount, string resolution);
    
    Task<Commission?> GetCommissionByIdAsync(Guid commissionId);
    Task<Commission?> GetCommissionByOrderIdAsync(Guid orderId);
    Task<List<Commission>> GetCommissionsByBrokerAsync(Guid brokerId);
    Task<List<Commission>> GetCommissionsByTransportCompanyAsync(Guid transportCompanyId);
    Task<List<Commission>> GetCommissionsByCarrierAsync(Guid carrierId);
    Task<List<Commission>> GetCommissionsByStatusAsync(CommissionStatus status);
}

public class CommissionCalculationRequest
{
    public Guid OrderId { get; set; }
    public Guid BrokerId { get; set; }
    public Guid TransportCompanyId { get; set; }
    public Guid CarrierId { get; set; }
    public Money OrderAmount { get; set; } = null!;
    public CommissionStructure CommissionStructure { get; set; } = null!;
    public string? Notes { get; set; }
}

public class CommissionPaymentResult
{
    public bool IsSuccess { get; set; }
    public string? TransactionId { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime ProcessedAt { get; set; }
}
