using MediatR;
using TripManagement.Application.DTOs;
using TripManagement.Domain.Enums;

namespace TripManagement.Application.Queries.GetCarrierTripsWithMilestones;

public record GetCarrierTripsWithMilestonesQuery(
    Guid CarrierId,
    int PageNumber = 1,
    int PageSize = 10,
    TripStatus? Status = null,
    DateTime? FromDate = null,
    DateTime? ToDate = null,
    bool? IsDelayed = null,
    bool? HasExceptions = null,
    bool? IsCompleted = null,
    string? SearchTerm = null) : IRequest<PagedResult<TripDashboardDto>>;

public class PagedResult<T>
{
    public IEnumerable<T> Items { get; set; } = new List<T>();
    public int TotalCount { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
    public bool HasPreviousPage => PageNumber > 1;
    public bool HasNextPage => PageNumber < TotalPages;
}
