using Xunit;
using FluentAssertions;
using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Enums;
using AnalyticsBIService.Tests.Infrastructure;

namespace AnalyticsBIService.Tests.Unit.Domain;

/// <summary>
/// Unit tests for Dashboard domain entity
/// </summary>
public class DashboardTests
{
    [Fact]
    public void Dashboard_Constructor_Should_Create_Valid_Dashboard()
    {
        // Arrange
        var name = "Test Dashboard";
        var description = "Test Description";
        var dashboardType = DashboardType.AdminPlatformPerformance;
        var userId = Guid.NewGuid();
        var userType = UserType.Admin;

        // Act
        var dashboard = new Dashboard(name, description, dashboardType, userId, userType);

        // Assert
        dashboard.Should().NotBeNull();
        dashboard.Id.Should().NotBeEmpty();
        dashboard.Name.Should().Be(name);
        dashboard.Description.Should().Be(description);
        dashboard.Type.Should().Be(dashboardType);
        dashboard.UserId.Should().Be(userId);
        dashboard.UserType.Should().Be(userType);
        dashboard.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        dashboard.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        dashboard.IsDefault.Should().BeFalse();
        dashboard.IsPublic.Should().BeFalse();
    }

    [Fact]
    public void Dashboard_Constructor_Should_Throw_When_Name_Is_Null()
    {
        // Act & Assert
        var action = () => new Dashboard(null!, "Description", DashboardType.AdminPlatformPerformance,
            Guid.NewGuid(), UserType.Admin);

        action.Should().Throw<ArgumentNullException>()
            .WithParameterName("name");
    }

    [Fact]
    public void Dashboard_Constructor_Should_Throw_When_Description_Is_Null()
    {
        // Act & Assert
        var action = () => new Dashboard("Test Dashboard", null!, DashboardType.AdminPlatformPerformance,
            Guid.NewGuid(), UserType.Admin);

        action.Should().Throw<ArgumentNullException>()
            .WithParameterName("description");
    }

    [Theory]
    [InlineData(DashboardType.AdminPlatformPerformance)]
    [InlineData(DashboardType.TransportCompanyPerformance)]
    [InlineData(DashboardType.BrokerOperational)]
    [InlineData(DashboardType.CarrierPerformance)]
    public void Dashboard_Should_Accept_Valid_DashboardTypes(DashboardType dashboardType)
    {
        // Act
        var dashboard = new Dashboard("Test", "Description", dashboardType, Guid.NewGuid(), UserType.Admin);

        // Assert
        dashboard.Type.Should().Be(dashboardType);
    }

    [Theory]
    [InlineData(UserType.Admin)]
    [InlineData(UserType.TransportCompany)]
    [InlineData(UserType.Broker)]
    [InlineData(UserType.Carrier)]
    public void Dashboard_Should_Accept_Valid_UserTypes(UserType userType)
    {
        // Act
        var dashboard = new Dashboard("Test", "Description", DashboardType.AdminPlatformPerformance,
            Guid.NewGuid(), userType);

        // Assert
        dashboard.UserType.Should().Be(userType);
    }

    [Fact]
    public void Dashboard_Constructor_Should_Handle_Optional_Parameters()
    {
        // Arrange
        var configuration = new Dictionary<string, object> { { "theme", "dark" } };

        // Act
        var dashboard = new Dashboard("Test Dashboard", "Description", DashboardType.AdminPlatformPerformance,
            Guid.NewGuid(), UserType.Admin, true, true, configuration);

        // Assert
        dashboard.IsDefault.Should().BeTrue();
        dashboard.IsPublic.Should().BeTrue();
        dashboard.Configuration.Should().BeEquivalentTo(configuration);
    }

    [Fact]
    public void Dashboard_UpdateConfiguration_Should_Update_Configuration_And_Timestamp()
    {
        // Arrange
        var dashboard = TestDataFactory.CreateTestDashboard();
        var originalUpdatedAt = dashboard.UpdatedAt;
        var newConfiguration = new Dictionary<string, object> { { "theme", "dark" }, { "layout", "grid" } };

        // Wait a small amount to ensure timestamp difference
        Thread.Sleep(10);

        // Act
        dashboard.UpdateConfiguration(newConfiguration);

        // Assert
        dashboard.Configuration.Should().BeEquivalentTo(newConfiguration);
        dashboard.UpdatedAt.Should().BeAfter(originalUpdatedAt.Value);
    }

    [Fact]
    public void Dashboard_UpdateConfiguration_Should_Throw_When_Configuration_Is_Null()
    {
        // Arrange
        var dashboard = TestDataFactory.CreateTestDashboard();

        // Act & Assert
        var action = () => dashboard.UpdateConfiguration(null!);
        action.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void Dashboard_UpdateLayout_Should_Update_Layout_And_Timestamp()
    {
        // Arrange
        var dashboard = TestDataFactory.CreateTestDashboard();
        var originalUpdatedAt = dashboard.UpdatedAt;
        var newLayout = "updated-layout-json";

        // Wait a small amount to ensure timestamp difference
        Thread.Sleep(10);

        // Act
        dashboard.UpdateLayout(newLayout);

        // Assert
        dashboard.Layout.Should().Be(newLayout);
        dashboard.UpdatedAt.Should().BeAfter(originalUpdatedAt.Value);
    }

    [Fact]
    public void Dashboard_UpdateTags_Should_Update_Tags_And_Timestamp()
    {
        // Arrange
        var dashboard = TestDataFactory.CreateTestDashboard();
        var originalUpdatedAt = dashboard.UpdatedAt;
        var newTags = new List<string> { "analytics", "performance", "monitoring" };

        // Wait a small amount to ensure timestamp difference
        Thread.Sleep(10);

        // Act
        dashboard.UpdateTags(newTags);

        // Assert
        dashboard.Tags.Should().BeEquivalentTo(newTags);
        dashboard.UpdatedAt.Should().BeAfter(originalUpdatedAt.Value);
    }

    [Fact]
    public void Dashboard_SetPublic_Should_Update_IsPublic_And_Timestamp()
    {
        // Arrange
        var dashboard = TestDataFactory.CreateTestDashboard();
        var originalUpdatedAt = dashboard.UpdatedAt;

        // Wait a small amount to ensure timestamp difference
        Thread.Sleep(10);

        // Act
        dashboard.SetPublic(true);

        // Assert
        dashboard.IsPublic.Should().BeTrue();
        dashboard.UpdatedAt.Should().BeAfter(originalUpdatedAt.Value);
    }

    [Fact]
    public void Dashboard_SetAsDefault_Should_Update_IsDefault_And_Timestamp()
    {
        // Arrange
        var dashboard = TestDataFactory.CreateTestDashboard();
        var originalUpdatedAt = dashboard.UpdatedAt;

        // Wait a small amount to ensure timestamp difference
        Thread.Sleep(10);

        // Act
        dashboard.SetAsDefault();

        // Assert
        dashboard.IsDefault.Should().BeTrue();
        dashboard.UpdatedAt.Should().BeAfter(originalUpdatedAt.Value);
    }

    [Fact]
    public void Dashboard_RecordAccess_Should_Update_LastAccessedAt_And_AccessCount()
    {
        // Arrange
        var dashboard = TestDataFactory.CreateTestDashboard();
        var originalAccessCount = dashboard.AccessCount;

        // Act
        dashboard.RecordAccess(Guid.NewGuid(), UserType.Admin);

        // Assert
        dashboard.LastAccessedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        dashboard.AccessCount.Should().Be(originalAccessCount + 1);
    }

    [Fact]
    public void Dashboard_Multiple_RecordAccess_Should_Increment_AccessCount()
    {
        // Arrange
        var dashboard = TestDataFactory.CreateTestDashboard();
        var originalAccessCount = dashboard.AccessCount;

        // Act
        dashboard.RecordAccess(Guid.NewGuid(), UserType.Admin);
        dashboard.RecordAccess(Guid.NewGuid(), UserType.Broker);
        dashboard.RecordAccess(Guid.NewGuid(), UserType.Carrier);

        // Assert
        dashboard.AccessCount.Should().Be(originalAccessCount + 3);
    }

    [Fact]
    public void Dashboard_Should_Have_Unique_Ids()
    {
        // Arrange & Act
        var dashboard1 = TestDataFactory.CreateTestDashboard();
        var dashboard2 = TestDataFactory.CreateTestDashboard();

        // Assert
        dashboard1.Id.Should().NotBe(dashboard2.Id);
    }

    [Fact]
    public void Dashboard_Factory_Methods_Should_Create_Specific_Dashboard_Types()
    {
        // Arrange
        var userId = Guid.NewGuid();

        // Act
        var adminDashboard = TestDataFactory.CreateAdminDashboard(userId);

        // Assert
        adminDashboard.Should().NotBeNull();
        adminDashboard.Type.Should().Be(DashboardType.AdminPlatformPerformance);
        adminDashboard.UserId.Should().Be(userId);
        adminDashboard.UserType.Should().Be(UserType.Admin);
        adminDashboard.IsDefault.Should().BeTrue();
        adminDashboard.IsPublic.Should().BeFalse();
        adminDashboard.Name.Should().Contain("Admin");
    }

    [Fact]
    public void Dashboard_Should_Handle_Null_Optional_Parameters()
    {
        // Act
        var dashboard = new Dashboard("Test Dashboard", "Description", DashboardType.AdminPlatformPerformance);

        // Assert
        dashboard.UserId.Should().BeNull();
        dashboard.UserType.Should().BeNull();
        dashboard.IsDefault.Should().BeFalse();
        dashboard.IsPublic.Should().BeFalse();
        dashboard.Configuration.Should().NotBeNull();
        dashboard.Configuration.Should().BeEmpty();
    }

    [Fact]
    public void Dashboard_Should_Initialize_Collections_Properly()
    {
        // Act
        var dashboard = TestDataFactory.CreateTestDashboard();

        // Assert
        dashboard.Configuration.Should().NotBeNull();
        dashboard.Widgets.Should().NotBeNull();
        dashboard.Widgets.Should().BeEmpty();
    }

    [Fact]
    public void Dashboard_Should_Support_Configuration_Updates()
    {
        // Arrange
        var dashboard = TestDataFactory.CreateTestDashboard();
        var originalUpdatedAt = dashboard.UpdatedAt;
        var newConfiguration = new Dictionary<string, object>
        {
            { "theme", "light" },
            { "autoRefresh", true },
            { "refreshInterval", 30 }
        };

        // Wait a small amount to ensure timestamp difference
        Thread.Sleep(10);

        // Act
        dashboard.UpdateConfiguration(newConfiguration);

        // Assert
        dashboard.Configuration.Should().BeEquivalentTo(newConfiguration);
        dashboard.UpdatedAt.Should().BeAfter(originalUpdatedAt.Value);
    }

    [Fact]
    public void Dashboard_Should_Track_Creation_And_Update_Times()
    {
        // Arrange
        var dashboard = TestDataFactory.CreateTestDashboard();
        var createdAt = dashboard.CreatedAt;
        var originalUpdatedAt = dashboard.UpdatedAt;

        // Wait a small amount to ensure timestamp difference
        Thread.Sleep(10);

        // Act
        dashboard.UpdateConfiguration(new Dictionary<string, object> { { "updated", true } });

        // Assert
        dashboard.CreatedAt.Should().Be(createdAt); // Should not change
        dashboard.UpdatedAt.Should().BeAfter(originalUpdatedAt.Value); // Should be updated
    }

    [Fact]
    public void Dashboard_Should_Support_Different_Dashboard_Types_For_Different_User_Types()
    {
        // Arrange & Act
        var adminDashboard = TestDataFactory.CreateTestDashboard(
            dashboardType: DashboardType.AdminPlatformPerformance,
            userType: UserType.Admin);

        var transportCompanyDashboard = TestDataFactory.CreateTestDashboard(
            dashboardType: DashboardType.TransportCompanyPerformance,
            userType: UserType.TransportCompany);

        var brokerDashboard = TestDataFactory.CreateTestDashboard(
            dashboardType: DashboardType.BrokerOperational,
            userType: UserType.Broker);

        var carrierDashboard = TestDataFactory.CreateTestDashboard(
            dashboardType: DashboardType.CarrierPerformance,
            userType: UserType.Carrier);

        // Assert
        adminDashboard.Type.Should().Be(DashboardType.AdminPlatformPerformance);
        adminDashboard.UserType.Should().Be(UserType.Admin);

        transportCompanyDashboard.Type.Should().Be(DashboardType.TransportCompanyPerformance);
        transportCompanyDashboard.UserType.Should().Be(UserType.TransportCompany);

        brokerDashboard.Type.Should().Be(DashboardType.BrokerOperational);
        brokerDashboard.UserType.Should().Be(UserType.Broker);

        carrierDashboard.Type.Should().Be(DashboardType.CarrierPerformance);
        carrierDashboard.UserType.Should().Be(UserType.Carrier);
    }
}
