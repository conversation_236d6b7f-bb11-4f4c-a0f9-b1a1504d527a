using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Caching.Memory;
using MobileWorkflow.Domain.Entities;
using MobileWorkflow.Domain.Repositories;
using Newtonsoft.Json;

namespace MobileWorkflow.Infrastructure.Services;

public interface IProgressiveWebAppService
{
    Task<PWAManifest> GenerateManifestAsync(string platform, string version, CancellationToken cancellationToken = default);
    Task<ServiceWorkerConfig> GenerateServiceWorkerConfigAsync(Guid userId, string platform, CancellationToken cancellationToken = default);
    Task<bool> RegisterPWAInstallationAsync(Guid userId, string deviceId, PWAInstallInfo installInfo, CancellationToken cancellationToken = default);
    Task<List<PWAFeature>> GetAvailableFeaturesAsync(string platform, CancellationToken cancellationToken = default);
    Task<bool> UpdatePWACacheAsync(Guid userId, List<string> resources, CancellationToken cancellationToken = default);
    Task<PWACapabilities> GetPWACapabilitiesAsync(string userAgent, CancellationToken cancellationToken = default);
}

public class ProgressiveWebAppService : IProgressiveWebAppService
{
    private readonly IMobileAppRepository _mobileAppRepository;
    private readonly IMobileSessionRepository _sessionRepository;
    private readonly IMemoryCache _cache;
    private readonly ILogger<ProgressiveWebAppService> _logger;

    public ProgressiveWebAppService(
        IMobileAppRepository mobileAppRepository,
        IMobileSessionRepository sessionRepository,
        IMemoryCache cache,
        ILogger<ProgressiveWebAppService> logger)
    {
        _mobileAppRepository = mobileAppRepository;
        _sessionRepository = sessionRepository;
        _cache = cache;
        _logger = logger;
    }

    public async Task<PWAManifest> GenerateManifestAsync(string platform, string version, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Generating PWA manifest for platform {Platform}, version {Version}", platform, version);

        var cacheKey = $"pwa_manifest_{platform}_{version}";
        
        if (_cache.TryGetValue(cacheKey, out PWAManifest? cachedManifest) && cachedManifest != null)
        {
            return cachedManifest;
        }

        var mobileApp = await _mobileAppRepository.GetLatestVersionAsync(platform, cancellationToken);
        
        var manifest = new PWAManifest
        {
            Name = "TLI Logistics Mobile",
            ShortName = "TLI Mobile",
            Description = "TLI Logistics mobile application for drivers, carriers, and logistics management",
            StartUrl = "/",
            Display = "standalone",
            BackgroundColor = "#ffffff",
            ThemeColor = "#2196F3",
            Orientation = "portrait-primary",
            Scope = "/",
            Icons = GenerateIconSet(),
            Categories = new List<string> { "logistics", "transportation", "business" },
            Screenshots = GenerateScreenshots(),
            Shortcuts = GenerateShortcuts(),
            RelatedApplications = GenerateRelatedApplications(platform),
            PreferRelatedApplications = false
        };

        if (mobileApp != null)
        {
            manifest.Version = mobileApp.Version;
            manifest.Features = mobileApp.Features.Keys.ToList();
        }

        _cache.Set(cacheKey, manifest, TimeSpan.FromHours(6));
        return manifest;
    }

    public async Task<ServiceWorkerConfig> GenerateServiceWorkerConfigAsync(Guid userId, string platform, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Generating service worker config for user {UserId}, platform {Platform}", userId, platform);

        var config = new ServiceWorkerConfig
        {
            CacheName = $"tli-mobile-v{DateTime.UtcNow:yyyyMMdd}",
            CacheStrategy = "CacheFirst",
            OfflinePageUrl = "/offline.html",
            CacheResources = GetCacheResources(),
            SyncTags = GetSyncTags(),
            BackgroundSyncEnabled = true,
            PushNotificationsEnabled = true,
            UpdateCheckInterval = TimeSpan.FromHours(6),
            MaxCacheSize = 50 * 1024 * 1024, // 50MB
            MaxCacheAge = TimeSpan.FromDays(7)
        };

        // Customize based on user preferences
        var activeSession = await _sessionRepository.GetActiveSessionByUserIdAsync(userId, cancellationToken);
        if (activeSession != null && activeSession.IsOfflineCapable)
        {
            config.OfflineCapabilities = GetOfflineCapabilities();
            config.SyncConfiguration = await GetUserSyncConfigurationAsync(userId, cancellationToken);
        }

        return config;
    }

    public async Task<bool> RegisterPWAInstallationAsync(Guid userId, string deviceId, PWAInstallInfo installInfo, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Registering PWA installation for user {UserId}, device {DeviceId}", userId, deviceId);

        try
        {
            var activeSession = await _sessionRepository.GetActiveSessionByDeviceIdAsync(deviceId, cancellationToken);
            if (activeSession != null)
            {
                activeSession.UpdateSessionData("pwa_installed", true);
                activeSession.UpdateSessionData("pwa_install_date", installInfo.InstallDate);
                activeSession.UpdateSessionData("pwa_install_source", installInfo.InstallSource);
                
                await _sessionRepository.UpdateAsync(activeSession, cancellationToken);
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error registering PWA installation for user {UserId}", userId);
            return false;
        }
    }

    public async Task<List<PWAFeature>> GetAvailableFeaturesAsync(string platform, CancellationToken cancellationToken = default)
    {
        var features = new List<PWAFeature>
        {
            new PWAFeature { Name = "Offline Mode", Supported = true, Description = "Work without internet connection" },
            new PWAFeature { Name = "Push Notifications", Supported = true, Description = "Receive real-time notifications" },
            new PWAFeature { Name = "Background Sync", Supported = true, Description = "Sync data in background" },
            new PWAFeature { Name = "Camera Access", Supported = true, Description = "Take photos for POD and documents" },
            new PWAFeature { Name = "Location Services", Supported = true, Description = "GPS tracking and navigation" },
            new PWAFeature { Name = "File Upload", Supported = true, Description = "Upload documents and photos" },
            new PWAFeature { Name = "Biometric Auth", Supported = IsBiometricSupported(platform), Description = "Fingerprint/Face ID authentication" },
            new PWAFeature { Name = "Voice Recording", Supported = true, Description = "Record voice notes" },
            new PWAFeature { Name = "QR Code Scanner", Supported = true, Description = "Scan QR codes for quick actions" },
            new PWAFeature { Name = "Installable", Supported = true, Description = "Install as native app" }
        };

        return features;
    }

    public async Task<bool> UpdatePWACacheAsync(Guid userId, List<string> resources, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Updating PWA cache for user {UserId} with {Count} resources", userId, resources.Count);

        try
        {
            var activeSession = await _sessionRepository.GetActiveSessionByUserIdAsync(userId, cancellationToken);
            if (activeSession != null)
            {
                activeSession.UpdateSessionData("cached_resources", resources);
                activeSession.UpdateSessionData("cache_updated", DateTime.UtcNow);
                
                await _sessionRepository.UpdateAsync(activeSession, cancellationToken);
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating PWA cache for user {UserId}", userId);
            return false;
        }
    }

    public async Task<PWACapabilities> GetPWACapabilitiesAsync(string userAgent, CancellationToken cancellationToken = default)
    {
        var capabilities = new PWACapabilities
        {
            SupportsServiceWorker = true,
            SupportsWebAppManifest = true,
            SupportsInstallPrompt = DetectInstallPromptSupport(userAgent),
            SupportsPushNotifications = true,
            SupportsBackgroundSync = true,
            SupportsOfflineMode = true,
            SupportsCameraAccess = true,
            SupportsLocationServices = true,
            SupportsFileUpload = true,
            SupportsBiometrics = DetectBiometricSupport(userAgent),
            SupportsVoiceRecording = true,
            SupportsQRScanner = true,
            Platform = DetectPlatform(userAgent),
            Browser = DetectBrowser(userAgent),
            Version = DetectVersion(userAgent)
        };

        return capabilities;
    }

    private List<PWAIcon> GenerateIconSet()
    {
        return new List<PWAIcon>
        {
            new PWAIcon { Src = "/icons/icon-72x72.png", Sizes = "72x72", Type = "image/png" },
            new PWAIcon { Src = "/icons/icon-96x96.png", Sizes = "96x96", Type = "image/png" },
            new PWAIcon { Src = "/icons/icon-128x128.png", Sizes = "128x128", Type = "image/png" },
            new PWAIcon { Src = "/icons/icon-144x144.png", Sizes = "144x144", Type = "image/png" },
            new PWAIcon { Src = "/icons/icon-152x152.png", Sizes = "152x152", Type = "image/png" },
            new PWAIcon { Src = "/icons/icon-192x192.png", Sizes = "192x192", Type = "image/png" },
            new PWAIcon { Src = "/icons/icon-384x384.png", Sizes = "384x384", Type = "image/png" },
            new PWAIcon { Src = "/icons/icon-512x512.png", Sizes = "512x512", Type = "image/png" }
        };
    }

    private List<PWAScreenshot> GenerateScreenshots()
    {
        return new List<PWAScreenshot>
        {
            new PWAScreenshot { Src = "/screenshots/mobile-1.png", Sizes = "390x844", Type = "image/png" },
            new PWAScreenshot { Src = "/screenshots/mobile-2.png", Sizes = "390x844", Type = "image/png" },
            new PWAScreenshot { Src = "/screenshots/tablet-1.png", Sizes = "768x1024", Type = "image/png" }
        };
    }

    private List<PWAShortcut> GenerateShortcuts()
    {
        return new List<PWAShortcut>
        {
            new PWAShortcut { Name = "New Trip", ShortName = "Trip", Url = "/trips/new", Icons = new List<PWAIcon> { new PWAIcon { Src = "/icons/trip-icon.png", Sizes = "96x96" } } },
            new PWAShortcut { Name = "Upload POD", ShortName = "POD", Url = "/pod/upload", Icons = new List<PWAIcon> { new PWAIcon { Src = "/icons/pod-icon.png", Sizes = "96x96" } } },
            new PWAShortcut { Name = "My Profile", ShortName = "Profile", Url = "/profile", Icons = new List<PWAIcon> { new PWAIcon { Src = "/icons/profile-icon.png", Sizes = "96x96" } } }
        };
    }

    private List<PWARelatedApplication> GenerateRelatedApplications(string platform)
    {
        var apps = new List<PWARelatedApplication>();

        if (platform.ToLower().Contains("android"))
        {
            apps.Add(new PWARelatedApplication
            {
                Platform = "play",
                Url = "https://play.google.com/store/apps/details?id=com.tli.logistics",
                Id = "com.tli.logistics"
            });
        }

        if (platform.ToLower().Contains("ios"))
        {
            apps.Add(new PWARelatedApplication
            {
                Platform = "itunes",
                Url = "https://apps.apple.com/app/tli-logistics/id123456789",
                Id = "123456789"
            });
        }

        return apps;
    }

    private List<string> GetCacheResources()
    {
        return new List<string>
        {
            "/",
            "/offline.html",
            "/css/app.css",
            "/js/app.js",
            "/js/offline.js",
            "/icons/icon-192x192.png",
            "/fonts/roboto.woff2"
        };
    }

    private List<string> GetSyncTags()
    {
        return new List<string>
        {
            "trip-updates",
            "pod-uploads",
            "document-uploads",
            "location-updates",
            "user-preferences"
        };
    }

    private Dictionary<string, object> GetOfflineCapabilities()
    {
        return new Dictionary<string, object>
        {
            { "maxOfflineStorage", "100MB" },
            { "offlinePages", new[] { "/trips", "/profile", "/documents", "/pod" } },
            { "syncOnReconnect", true },
            { "backgroundSync", true }
        };
    }

    private async Task<Dictionary<string, object>> GetUserSyncConfigurationAsync(Guid userId, CancellationToken cancellationToken)
    {
        return new Dictionary<string, object>
        {
            { "autoSync", true },
            { "syncInterval", 300000 }, // 5 minutes in milliseconds
            { "wifiOnly", false },
            { "backgroundSyncEnabled", true }
        };
    }

    private bool IsBiometricSupported(string platform)
    {
        return platform.ToLower().Contains("android") || platform.ToLower().Contains("ios");
    }

    private bool DetectInstallPromptSupport(string userAgent)
    {
        return userAgent.Contains("Chrome") || userAgent.Contains("Edge") || userAgent.Contains("Samsung");
    }

    private bool DetectBiometricSupport(string userAgent)
    {
        return userAgent.Contains("Mobile") && (userAgent.Contains("Chrome") || userAgent.Contains("Safari"));
    }

    private string DetectPlatform(string userAgent)
    {
        if (userAgent.Contains("Android")) return "Android";
        if (userAgent.Contains("iPhone") || userAgent.Contains("iPad")) return "iOS";
        if (userAgent.Contains("Windows")) return "Windows";
        if (userAgent.Contains("Mac")) return "macOS";
        return "Unknown";
    }

    private string DetectBrowser(string userAgent)
    {
        if (userAgent.Contains("Chrome")) return "Chrome";
        if (userAgent.Contains("Safari")) return "Safari";
        if (userAgent.Contains("Firefox")) return "Firefox";
        if (userAgent.Contains("Edge")) return "Edge";
        return "Unknown";
    }

    private string DetectVersion(string userAgent)
    {
        // Simple version detection - in real implementation, use proper regex
        return "1.0.0";
    }
}

// PWA Data Models
public class PWAManifest
{
    public string Name { get; set; } = string.Empty;
    public string ShortName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string StartUrl { get; set; } = string.Empty;
    public string Display { get; set; } = string.Empty;
    public string BackgroundColor { get; set; } = string.Empty;
    public string ThemeColor { get; set; } = string.Empty;
    public string Orientation { get; set; } = string.Empty;
    public string Scope { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public List<PWAIcon> Icons { get; set; } = new();
    public List<string> Categories { get; set; } = new();
    public List<PWAScreenshot> Screenshots { get; set; } = new();
    public List<PWAShortcut> Shortcuts { get; set; } = new();
    public List<PWARelatedApplication> RelatedApplications { get; set; } = new();
    public bool PreferRelatedApplications { get; set; }
    public List<string> Features { get; set; } = new();
}

public class PWAIcon
{
    public string Src { get; set; } = string.Empty;
    public string Sizes { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Purpose { get; set; } = "any";
}

public class PWAScreenshot
{
    public string Src { get; set; } = string.Empty;
    public string Sizes { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
}

public class PWAShortcut
{
    public string Name { get; set; } = string.Empty;
    public string ShortName { get; set; } = string.Empty;
    public string Url { get; set; } = string.Empty;
    public List<PWAIcon> Icons { get; set; } = new();
}

public class PWARelatedApplication
{
    public string Platform { get; set; } = string.Empty;
    public string Url { get; set; } = string.Empty;
    public string Id { get; set; } = string.Empty;
}

public class ServiceWorkerConfig
{
    public string CacheName { get; set; } = string.Empty;
    public string CacheStrategy { get; set; } = string.Empty;
    public string OfflinePageUrl { get; set; } = string.Empty;
    public List<string> CacheResources { get; set; } = new();
    public List<string> SyncTags { get; set; } = new();
    public bool BackgroundSyncEnabled { get; set; }
    public bool PushNotificationsEnabled { get; set; }
    public TimeSpan UpdateCheckInterval { get; set; }
    public long MaxCacheSize { get; set; }
    public TimeSpan MaxCacheAge { get; set; }
    public Dictionary<string, object> OfflineCapabilities { get; set; } = new();
    public Dictionary<string, object> SyncConfiguration { get; set; } = new();
}

public class PWAInstallInfo
{
    public DateTime InstallDate { get; set; }
    public string InstallSource { get; set; } = string.Empty;
    public string Platform { get; set; } = string.Empty;
    public string Browser { get; set; } = string.Empty;
}

public class PWAFeature
{
    public string Name { get; set; } = string.Empty;
    public bool Supported { get; set; }
    public string Description { get; set; } = string.Empty;
}

public class PWACapabilities
{
    public bool SupportsServiceWorker { get; set; }
    public bool SupportsWebAppManifest { get; set; }
    public bool SupportsInstallPrompt { get; set; }
    public bool SupportsPushNotifications { get; set; }
    public bool SupportsBackgroundSync { get; set; }
    public bool SupportsOfflineMode { get; set; }
    public bool SupportsCameraAccess { get; set; }
    public bool SupportsLocationServices { get; set; }
    public bool SupportsFileUpload { get; set; }
    public bool SupportsBiometrics { get; set; }
    public bool SupportsVoiceRecording { get; set; }
    public bool SupportsQRScanner { get; set; }
    public string Platform { get; set; } = string.Empty;
    public string Browser { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
}
