using CommunicationNotification.Application.Commands.Alerts;
using CommunicationNotification.Domain.Enums;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Events;

namespace CommunicationNotification.Application.EventHandlers;

/// <summary>
/// Event handler for carrier user feature events
/// </summary>
public class CarrierUserFeatureEventHandler :
    INotificationHandler<CarrierPerformanceUpdatedEvent>,
    INotificationHandler<CarrierRatingChangedEvent>,
    INotificationHandler<CarrierMilestoneAchievedEvent>,
    INotificationHandler<CarrierDocumentExpiringEvent>,
    INotificationHandler<CarrierTripStatusChangedEvent>,
    INotificationHandler<CarrierVehicleStatusChangedEvent>,
    INotificationHandler<CarrierDriverStatusChangedEvent>,
    INotificationHandler<CarrierEarningsUpdatedEvent>,
    INotificationHandler<CarrierAnalyticsUpdatedEvent>,
    INotificationHandler<CarrierFeedbackReceivedEvent>
{
    private readonly IMediator _mediator;
    private readonly ILogger<CarrierUserFeatureEventHandler> _logger;

    public CarrierUserFeatureEventHandler(
        IMediator mediator,
        ILogger<CarrierUserFeatureEventHandler> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    public async Task Handle(CarrierPerformanceUpdatedEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing performance updated event for carrier {CarrierId}", notification.CarrierId);

        try
        {
            // Check if performance change is significant enough to warrant notification
            var performanceThresholds = await GetPerformanceThresholds(notification.CarrierId);
            
            if (ShouldNotifyPerformanceChange(notification, performanceThresholds))
            {
                var alertCommand = new SendPerformanceAlertCommand
                {
                    CarrierId = notification.CarrierId,
                    PerformanceMetric = "OverallPerformance",
                    AlertType = DetermineAlertType(notification),
                    CurrentValue = notification.OnTimeDeliveryPercentage,
                    TimePeriod = "Monthly",
                    MeasurementDate = notification.Timestamp,
                    PerformanceData = new Dictionary<string, object>
                    {
                        ["OnTimeDeliveryPercentage"] = notification.OnTimeDeliveryPercentage,
                        ["CustomerSatisfactionScore"] = notification.CustomerSatisfactionScore,
                        ["AverageRating"] = notification.AverageRating,
                        ["TotalTripsCompleted"] = notification.TotalTripsCompleted,
                        ["TotalEarnings"] = notification.TotalEarnings,
                        ["CategoryRatings"] = notification.CategoryRatings,
                        ["PerformanceHighlights"] = notification.PerformanceHighlights,
                        ["ImprovementAreas"] = notification.ImprovementAreas
                    },
                    Priority = Priority.Normal,
                    Tags = new List<string> { "performance", "monthly-update" }
                };

                await _mediator.Send(alertCommand, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing performance updated event for carrier {CarrierId}", notification.CarrierId);
        }
    }

    public async Task Handle(CarrierRatingChangedEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing rating changed event for carrier {CarrierId}", notification.CarrierId);

        try
        {
            if (notification.RequiresNotification)
            {
                var alertCommand = new SendRatingChangeAlertCommand
                {
                    CarrierId = notification.CarrierId,
                    CurrentRating = notification.CurrentRating,
                    PreviousRating = notification.PreviousRating,
                    RatingChange = notification.RatingChange,
                    RatingCategory = notification.RatingCategory,
                    ChangeType = notification.ChangeType,
                    ReviewCount = notification.ReviewCount,
                    RatingDate = notification.RatingDate,
                    RecentFeedback = notification.RecentFeedback,
                    Priority = GetRatingChangePriority(notification.RatingChange),
                    Tags = new List<string> { "rating-change", notification.RatingCategory.ToLower() }
                };

                await _mediator.Send(alertCommand, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing rating changed event for carrier {CarrierId}", notification.CarrierId);
        }
    }

    public async Task Handle(CarrierMilestoneAchievedEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing milestone achieved event for carrier {CarrierId}", notification.CarrierId);

        try
        {
            var alertCommand = new SendPerformanceMilestoneAlertCommand
            {
                CarrierId = notification.CarrierId,
                MilestoneType = notification.MilestoneType,
                MilestoneName = notification.MilestoneName,
                MilestoneValue = notification.MilestoneValue,
                MilestoneUnit = notification.MilestoneUnit,
                AchievedDate = notification.AchievedDate,
                AchievementLevel = notification.AchievementLevel,
                MilestoneData = notification.Metadata,
                Rewards = notification.Rewards,
                NextMilestone = notification.NextMilestone,
                NextMilestoneTarget = notification.NextMilestoneTarget,
                Priority = Priority.High, // Milestones are always high priority
                Tags = new List<string> { "milestone", notification.MilestoneType.ToLower(), notification.AchievementLevel.ToLower() }
            };

            await _mediator.Send(alertCommand, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing milestone achieved event for carrier {CarrierId}", notification.CarrierId);
        }
    }

    public async Task Handle(CarrierDocumentExpiringEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing document expiring event for carrier {CarrierId}", notification.CarrierId);

        try
        {
            var alertCommand = new SendDocumentExpiryAlertCommand
            {
                CarrierId = notification.CarrierId,
                DocumentType = notification.DocumentType,
                EntityType = notification.EntityType,
                EntityId = notification.EntityId,
                EntityName = notification.EntityName,
                ExpiryDate = notification.ExpiryDate,
                DaysUntilExpiry = notification.DaysUntilExpiry,
                AlertType = notification.AlertType,
                ThresholdDays = notification.ThresholdDays,
                DocumentDetails = notification.DocumentDetails,
                Priority = GetDocumentExpiryPriority(notification.DaysUntilExpiry),
                RequireAcknowledgment = notification.RequiresImmediateAction,
                Tags = new List<string> { "document-expiry", notification.DocumentType.ToLower(), notification.EntityType.ToLower() }
            };

            await _mediator.Send(alertCommand, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing document expiring event for carrier {CarrierId}", notification.CarrierId);
        }
    }

    public async Task Handle(CarrierTripStatusChangedEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing trip status changed event for carrier {CarrierId}", notification.CarrierId);

        try
        {
            // Only send notifications for significant status changes
            if (IsSignificantTripStatusChange(notification.TripStatus, notification.PreviousStatus))
            {
                var alertCommand = new SendPerformanceAlertCommand
                {
                    CarrierId = notification.CarrierId,
                    PerformanceMetric = "TripStatus",
                    AlertType = "StatusUpdate",
                    CurrentValue = GetTripStatusValue(notification.TripStatus),
                    TimePeriod = "Immediate",
                    MeasurementDate = notification.StatusChangeDate,
                    PerformanceData = new Dictionary<string, object>
                    {
                        ["TripId"] = notification.TripId,
                        ["TripStatus"] = notification.TripStatus,
                        ["PreviousStatus"] = notification.PreviousStatus,
                        ["ChangeReason"] = notification.ChangeReason,
                        ["TripDetails"] = notification.TripDetails,
                        ["MilestonesCompleted"] = notification.MilestonesCompleted,
                        ["IsOnTime"] = notification.IsOnTime
                    },
                    Priority = GetTripStatusPriority(notification.TripStatus),
                    Tags = new List<string> { "trip-status", notification.TripStatus.ToLower() }
                };

                await _mediator.Send(alertCommand, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing trip status changed event for carrier {CarrierId}", notification.CarrierId);
        }
    }

    public async Task Handle(CarrierVehicleStatusChangedEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing vehicle status changed event for carrier {CarrierId}", notification.CarrierId);

        try
        {
            if (notification.RequiresAttention)
            {
                var alertCommand = new SendPerformanceAlertCommand
                {
                    CarrierId = notification.CarrierId,
                    PerformanceMetric = "VehicleStatus",
                    AlertType = "StatusUpdate",
                    CurrentValue = GetVehicleStatusValue(notification.VehicleStatus),
                    TimePeriod = "Immediate",
                    MeasurementDate = notification.StatusChangeDate,
                    PerformanceData = new Dictionary<string, object>
                    {
                        ["VehicleId"] = notification.VehicleId,
                        ["VehicleRegistration"] = notification.VehicleRegistration,
                        ["VehicleStatus"] = notification.VehicleStatus,
                        ["PreviousStatus"] = notification.PreviousStatus,
                        ["ChangeReason"] = notification.ChangeReason,
                        ["VehicleDetails"] = notification.VehicleDetails,
                        ["DocumentsAffected"] = notification.DocumentsAffected
                    },
                    Priority = GetVehicleStatusPriority(notification.VehicleStatus),
                    Tags = new List<string> { "vehicle-status", notification.VehicleStatus.ToLower() }
                };

                await _mediator.Send(alertCommand, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing vehicle status changed event for carrier {CarrierId}", notification.CarrierId);
        }
    }

    public async Task Handle(CarrierDriverStatusChangedEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing driver status changed event for carrier {CarrierId}", notification.CarrierId);

        try
        {
            if (notification.RequiresAttention)
            {
                var alertCommand = new SendPerformanceAlertCommand
                {
                    CarrierId = notification.CarrierId,
                    PerformanceMetric = "DriverStatus",
                    AlertType = "StatusUpdate",
                    CurrentValue = GetDriverStatusValue(notification.DriverStatus),
                    TimePeriod = "Immediate",
                    MeasurementDate = notification.StatusChangeDate,
                    PerformanceData = new Dictionary<string, object>
                    {
                        ["DriverId"] = notification.DriverId,
                        ["DriverName"] = notification.DriverName,
                        ["DriverStatus"] = notification.DriverStatus,
                        ["PreviousStatus"] = notification.PreviousStatus,
                        ["ChangeReason"] = notification.ChangeReason,
                        ["DriverDetails"] = notification.DriverDetails,
                        ["LicensesAffected"] = notification.LicensesAffected
                    },
                    Priority = GetDriverStatusPriority(notification.DriverStatus),
                    Tags = new List<string> { "driver-status", notification.DriverStatus.ToLower() }
                };

                await _mediator.Send(alertCommand, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing driver status changed event for carrier {CarrierId}", notification.CarrierId);
        }
    }

    public async Task Handle(CarrierEarningsUpdatedEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing earnings updated event for carrier {CarrierId}", notification.CarrierId);

        try
        {
            if (notification.MilestoneReached)
            {
                var alertCommand = new SendPerformanceAlertCommand
                {
                    CarrierId = notification.CarrierId,
                    PerformanceMetric = "Earnings",
                    AlertType = "Milestone",
                    CurrentValue = notification.TotalEarnings,
                    TimePeriod = notification.EarningsPeriod,
                    MeasurementDate = notification.EarningsDate,
                    PerformanceData = new Dictionary<string, object>
                    {
                        ["TotalEarnings"] = notification.TotalEarnings,
                        ["PeriodEarnings"] = notification.PeriodEarnings,
                        ["EarningsChange"] = notification.EarningsChange,
                        ["EarningsBreakdown"] = notification.EarningsBreakdown,
                        ["EarningsSources"] = notification.EarningsSources,
                        ["NextEarningsMilestone"] = notification.NextEarningsMilestone
                    },
                    Priority = Priority.High,
                    Tags = new List<string> { "earnings", "milestone" }
                };

                await _mediator.Send(alertCommand, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing earnings updated event for carrier {CarrierId}", notification.CarrierId);
        }
    }

    public async Task Handle(CarrierAnalyticsUpdatedEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing analytics updated event for carrier {CarrierId}", notification.CarrierId);

        try
        {
            if (notification.RequiresAction)
            {
                var alertCommand = new SendPerformanceAlertCommand
                {
                    CarrierId = notification.CarrierId,
                    PerformanceMetric = notification.AnalyticsType,
                    AlertType = "AnalyticsInsight",
                    CurrentValue = 0, // Analytics don't have a single value
                    TimePeriod = "Analytics",
                    MeasurementDate = notification.Timestamp,
                    PerformanceData = new Dictionary<string, object>
                    {
                        ["AnalyticsType"] = notification.AnalyticsType,
                        ["AnalyticsData"] = notification.AnalyticsData,
                        ["KeyInsights"] = notification.KeyInsights,
                        ["Recommendations"] = notification.Recommendations,
                        ["ActionPriority"] = notification.ActionPriority
                    },
                    Priority = GetAnalyticsPriority(notification.ActionPriority),
                    Tags = new List<string> { "analytics", notification.AnalyticsType.ToLower() }
                };

                await _mediator.Send(alertCommand, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing analytics updated event for carrier {CarrierId}", notification.CarrierId);
        }
    }

    public async Task Handle(CarrierFeedbackReceivedEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing feedback received event for carrier {CarrierId}", notification.CarrierId);

        try
        {
            if (notification.RequiresResponse || notification.ImpactsRating)
            {
                var alertCommand = new SendPerformanceAlertCommand
                {
                    CarrierId = notification.CarrierId,
                    PerformanceMetric = "CustomerFeedback",
                    AlertType = "FeedbackReceived",
                    CurrentValue = notification.Rating ?? 0,
                    TimePeriod = "Immediate",
                    MeasurementDate = notification.FeedbackDate,
                    PerformanceData = new Dictionary<string, object>
                    {
                        ["FeedbackId"] = notification.FeedbackId,
                        ["FeedbackSource"] = notification.FeedbackSource,
                        ["FeedbackType"] = notification.FeedbackType,
                        ["Rating"] = notification.Rating,
                        ["Comment"] = notification.Comment,
                        ["Sentiment"] = notification.Sentiment,
                        ["Category"] = notification.Category,
                        ["FeedbackDetails"] = notification.FeedbackDetails,
                        ["RequiresResponse"] = notification.RequiresResponse,
                        ["ImpactsRating"] = notification.ImpactsRating
                    },
                    Priority = GetFeedbackPriority(notification.Sentiment, notification.RequiresResponse),
                    Tags = new List<string> { "feedback", notification.FeedbackType.ToLower(), notification.Sentiment.ToLower() }
                };

                await _mediator.Send(alertCommand, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing feedback received event for carrier {CarrierId}", notification.CarrierId);
        }
    }

    // Helper methods
    private async Task<Dictionary<string, decimal>> GetPerformanceThresholds(Guid carrierId)
    {
        // In a real implementation, this would fetch from database
        return new Dictionary<string, decimal>
        {
            ["OnTimeDeliveryThreshold"] = 85.0m,
            ["CustomerSatisfactionThreshold"] = 4.0m,
            ["RatingThreshold"] = 4.0m
        };
    }

    private bool ShouldNotifyPerformanceChange(CarrierPerformanceUpdatedEvent notification, Dictionary<string, decimal> thresholds)
    {
        return notification.OnTimeDeliveryPercentage < thresholds["OnTimeDeliveryThreshold"] ||
               notification.CustomerSatisfactionScore < thresholds["CustomerSatisfactionThreshold"] ||
               notification.AverageRating < thresholds["RatingThreshold"];
    }

    private string DetermineAlertType(CarrierPerformanceUpdatedEvent notification)
    {
        if (notification.ImprovementAreas.Any())
            return "Improvement";
        if (notification.PerformanceHighlights.Any())
            return "Achievement";
        return "Update";
    }

    private Priority GetRatingChangePriority(decimal ratingChange)
    {
        return Math.Abs(ratingChange) switch
        {
            >= 0.5m => Priority.High,
            >= 0.2m => Priority.Normal,
            _ => Priority.Low
        };
    }

    private Priority GetDocumentExpiryPriority(int daysUntilExpiry)
    {
        return daysUntilExpiry switch
        {
            <= 0 => Priority.Critical,
            <= 7 => Priority.High,
            <= 15 => Priority.Normal,
            _ => Priority.Low
        };
    }

    private bool IsSignificantTripStatusChange(string newStatus, string previousStatus)
    {
        var significantStatuses = new[] { "Completed", "Cancelled", "Delayed", "InTransit" };
        return significantStatuses.Contains(newStatus) || significantStatuses.Contains(previousStatus);
    }

    private decimal GetTripStatusValue(string status)
    {
        return status.ToLower() switch
        {
            "completed" => 100,
            "intransit" => 75,
            "delayed" => 50,
            "cancelled" => 0,
            _ => 25
        };
    }

    private Priority GetTripStatusPriority(string status)
    {
        return status.ToLower() switch
        {
            "cancelled" => Priority.High,
            "delayed" => Priority.Normal,
            "completed" => Priority.Low,
            _ => Priority.Normal
        };
    }

    private decimal GetVehicleStatusValue(string status)
    {
        return status.ToLower() switch
        {
            "active" => 100,
            "maintenance" => 50,
            "inactive" => 25,
            "breakdown" => 0,
            _ => 50
        };
    }

    private Priority GetVehicleStatusPriority(string status)
    {
        return status.ToLower() switch
        {
            "breakdown" => Priority.Critical,
            "maintenance" => Priority.High,
            "inactive" => Priority.Normal,
            _ => Priority.Low
        };
    }

    private decimal GetDriverStatusValue(string status)
    {
        return status.ToLower() switch
        {
            "active" => 100,
            "onleave" => 50,
            "suspended" => 25,
            "inactive" => 0,
            _ => 50
        };
    }

    private Priority GetDriverStatusPriority(string status)
    {
        return status.ToLower() switch
        {
            "suspended" => Priority.High,
            "inactive" => Priority.Normal,
            "onleave" => Priority.Low,
            _ => Priority.Low
        };
    }

    private Priority GetAnalyticsPriority(string actionPriority)
    {
        return actionPriority.ToLower() switch
        {
            "high" => Priority.High,
            "medium" => Priority.Normal,
            "low" => Priority.Low,
            _ => Priority.Normal
        };
    }

    private Priority GetFeedbackPriority(string sentiment, bool requiresResponse)
    {
        if (requiresResponse)
            return Priority.High;
        
        return sentiment.ToLower() switch
        {
            "negative" => Priority.High,
            "positive" => Priority.Low,
            _ => Priority.Normal
        };
    }
}
