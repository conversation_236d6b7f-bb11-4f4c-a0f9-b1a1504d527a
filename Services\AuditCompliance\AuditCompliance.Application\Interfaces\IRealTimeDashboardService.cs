using AuditCompliance.Application.DTOs.Analytics;
using Shared.Domain.Common;
using AuditCompliance.Application.DTOs;
using Shared.Domain.Common;

namespace AuditCompliance.Application.Interfaces;

/// <summary>
/// Service for real-time dashboard updates and notifications
/// </summary>
public interface IRealTimeDashboardService
{
    /// <summary>
    /// Broadcast compliance alert to relevant users
    /// </summary>
    Task BroadcastComplianceAlertAsync(
        ComplianceAlertDto alert,
        Guid? organizationId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Broadcast anomaly detection to dashboard users
    /// </summary>
    Task BroadcastAnomalyDetectionAsync(
        ComplianceAnomalyDto anomaly,
        Guid? organizationId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Broadcast risk score update
    /// </summary>
    Task BroadcastRiskScoreUpdateAsync(
        RiskScoreUpdateDto riskUpdate,
        Guid? organizationId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Broadcast dashboard metrics update
    /// </summary>
    Task BroadcastDashboardMetricsUpdateAsync(
        DashboardMetricsDto metrics,
        Guid? organizationId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Broadcast compliance status update
    /// </summary>
    Task BroadcastComplianceStatusUpdateAsync(
        ComplianceStatusUpdateDto statusUpdate,
        Guid? organizationId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Broadcast compliance report status update
    /// </summary>
    Task BroadcastComplianceReportUpdateAsync(
        ComplianceReportUpdateDto reportUpdate,
        Guid? organizationId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Broadcast audit log notification
    /// </summary>
    Task BroadcastAuditLogNotificationAsync(
        AuditLogNotificationDto auditNotification,
        Guid? organizationId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Broadcast model training status update
    /// </summary>
    Task BroadcastModelTrainingUpdateAsync(
        ModelTrainingUpdateDto trainingUpdate,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Send personalized dashboard update to specific user
    /// </summary>
    Task SendPersonalizedDashboardUpdateAsync(
        string userId,
        PersonalizedDashboardDto dashboard,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Broadcast entity-specific compliance update
    /// </summary>
    Task BroadcastEntityComplianceUpdateAsync(
        Guid entityId,
        string entityType,
        EntityComplianceUpdateDto update,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get current dashboard metrics for real-time display
    /// </summary>
    Task<DashboardMetricsDto> GetCurrentDashboardMetricsAsync(
        Guid? organizationId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get real-time compliance status
    /// </summary>
    Task<ComplianceStatusUpdateDto> GetRealTimeComplianceStatusAsync(
        Guid? organizationId = null,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Compliance alert DTO for real-time notifications
/// </summary>
public class ComplianceAlertDto
{
    public Guid Id { get; set; }
    public string AlertType { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public Guid? EntityId { get; set; }
    public string? EntityType { get; set; }
    public DateTime Timestamp { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
    public List<string> RequiredActions { get; set; } = new();
}

/// <summary>
/// Risk score update DTO
/// </summary>
public class RiskScoreUpdateDto
{
    public Guid EntityId { get; set; }
    public string EntityType { get; set; } = string.Empty;
    public float PreviousScore { get; set; }
    public float CurrentScore { get; set; }
    public string RiskLevel { get; set; } = string.Empty;
    public string ChangeReason { get; set; } = string.Empty;
    public DateTime UpdatedAt { get; set; }
    public List<string> ImpactedAreas { get; set; } = new();
}

/// <summary>
/// Dashboard metrics DTO
/// </summary>
public class DashboardMetricsDto
{
    public DateTime Timestamp { get; set; }
    public Guid? OrganizationId { get; set; }
    public ComplianceOverviewDto Overview { get; set; } = new();
    public List<ComplianceMetricDto> RealTimeMetrics { get; set; } = new();
    public List<ComplianceAnomalyDto> RecentAnomalies { get; set; } = new();
    public List<ComplianceAlertDto> ActiveAlerts { get; set; } = new();
    public Dictionary<string, float> TrendIndicators { get; set; } = new();
}

/// <summary>
/// Compliance status update DTO
/// </summary>
public class ComplianceStatusUpdateDto
{
    public DateTime Timestamp { get; set; }
    public Guid? OrganizationId { get; set; }
    public float OverallComplianceScore { get; set; }
    public string ComplianceStatus { get; set; } = string.Empty; // Compliant, At Risk, Non-Compliant
    public int ActiveViolations { get; set; }
    public int PendingReviews { get; set; }
    public int CompletedAudits { get; set; }
    public Dictionary<string, float> StandardCompliance { get; set; } = new();
    public List<string> RecentChanges { get; set; } = new();
}

/// <summary>
/// Compliance report update DTO
/// </summary>
public class ComplianceReportUpdateDto
{
    public Guid ReportId { get; set; }
    public string ReportType { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public DateTime UpdatedAt { get; set; }
    public string UpdatedBy { get; set; } = string.Empty;
    public string UpdateReason { get; set; } = string.Empty;
    public int ProgressPercentage { get; set; }
}

/// <summary>
/// Audit log notification DTO
/// </summary>
public class AuditLogNotificationDto
{
    public Guid AuditLogId { get; set; }
    public string EventType { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public string EntityType { get; set; } = string.Empty;
    public Guid? EntityId { get; set; }
    public string Action { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public bool RequiresAttention { get; set; }
}

/// <summary>
/// Model training update DTO
/// </summary>
public class ModelTrainingUpdateDto
{
    public string ModelType { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty; // Training, Completed, Failed
    public int ProgressPercentage { get; set; }
    public DateTime StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, float> Metrics { get; set; } = new();
}

/// <summary>
/// Personalized dashboard DTO
/// </summary>
public class PersonalizedDashboardDto
{
    public string UserId { get; set; } = string.Empty;
    public DateTime GeneratedAt { get; set; }
    public List<ComplianceAlertDto> PersonalAlerts { get; set; } = new();
    public List<ComplianceTaskDto> AssignedTasks { get; set; } = new();
    public List<ComplianceMetricDto> RelevantMetrics { get; set; } = new();
    public List<ComplianceRecommendationDto> PersonalRecommendations { get; set; } = new();
}

/// <summary>
/// Compliance task DTO
/// </summary>
public class ComplianceTaskDto
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Priority { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime DueDate { get; set; }
    public string AssignedTo { get; set; } = string.Empty;
    public Guid? RelatedEntityId { get; set; }
    public string? RelatedEntityType { get; set; }
}

/// <summary>
/// Entity compliance update DTO
/// </summary>
public class EntityComplianceUpdateDto
{
    public Guid EntityId { get; set; }
    public string EntityType { get; set; } = string.Empty;
    public float ComplianceScore { get; set; }
    public string ComplianceStatus { get; set; } = string.Empty;
    public DateTime LastUpdated { get; set; }
    public List<string> ChangedStandards { get; set; } = new();
    public List<ComplianceIssueDto> NewIssues { get; set; } = new();
    public List<ComplianceIssueDto> ResolvedIssues { get; set; } = new();
}

/// <summary>
/// Compliance issue DTO
/// </summary>
public class ComplianceIssueDto
{
    public Guid Id { get; set; }
    public string IssueType { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Standard { get; set; } = string.Empty;
    public DateTime DetectedAt { get; set; }
    public DateTime? ResolvedAt { get; set; }
    public string Status { get; set; } = string.Empty;
}

