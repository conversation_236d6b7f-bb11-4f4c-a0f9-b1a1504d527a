using MediatR;
using FinancialPayment.Application.Interfaces.Repositories;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.ValueObjects;
using Shared.Domain.Common;
using Shared.Infrastructure.Interfaces;
using FluentValidation;

namespace FinancialPayment.Application.Commands.CreateGstConfiguration;

public class CreateGstConfigurationCommandHandler : IRequestHandler<CreateGstConfigurationCommand, Guid>
{
    private readonly IGstConfigurationRepository _gstConfigurationRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IValidator<CreateGstConfigurationCommand> _validator;

    public CreateGstConfigurationCommandHandler(
        IGstConfigurationRepository gstConfigurationRepository,
        IUnitOfWork unitOfWork,
        IValidator<CreateGstConfigurationCommand> validator)
    {
        _gstConfigurationRepository = gstConfigurationRepository;
        _unitOfWork = unitOfWork;
        _validator = validator;
    }

    public async Task<Guid> Handle(CreateGstConfigurationCommand request, CancellationToken cancellationToken)
    {
        // Validate the request
        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            throw new ValidationException(validationResult.Errors);
        }

        // Check if configuration with same name already exists
        var existingConfig = await _gstConfigurationRepository.ExistsByNameAsync(request.Name);
        if (existingConfig)
        {
            throw new InvalidOperationException($"GST configuration with name '{request.Name}' already exists");
        }

        // Create value objects
        var jurisdiction = new TaxJurisdiction(
            request.Jurisdiction.Country,
            request.Jurisdiction.State,
            request.Jurisdiction.City,
            request.Jurisdiction.Type);

        var taxRate = new TaxRate(
            request.TaxRate.Rate,
            request.TaxRate.CalculationMethod,
            request.TaxRate.EffectiveFrom,
            request.TaxRate.EffectiveTo);

        var minimumAmount = new Money(request.MinimumAmount.Amount, request.MinimumAmount.Currency);
        var maximumAmount = new Money(request.MaximumAmount.Amount, request.MaximumAmount.Currency);

        // Create the GST configuration entity
        var gstConfiguration = new GstConfiguration(
            request.Name,
            request.Description,
            request.ServiceCategory,
            jurisdiction,
            request.GstRate,
            taxRate,
            minimumAmount,
            maximumAmount,
            request.CreatedBy,
            request.HsnCode,
            request.IsReverseChargeApplicable,
            request.ReverseChargeConditions);

        // Add to repository
        await _gstConfigurationRepository.AddAsync(gstConfiguration);

        // Save changes
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return gstConfiguration.Id;
    }
}

