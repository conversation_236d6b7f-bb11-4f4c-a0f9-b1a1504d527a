using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using NetworkFleetManagement.Infrastructure.Persistence;

namespace NetworkFleetManagement.Infrastructure.Monitoring.HealthChecks;

public class DatabaseHealthCheck : IHealthCheck
{
    private readonly NetworkFleetDbContext _context;
    private readonly ILogger<DatabaseHealthCheck> _logger;

    public DatabaseHealthCheck(NetworkFleetDbContext context, ILogger<DatabaseHealthCheck> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            // Test database connectivity
            var canConnect = await _context.Database.CanConnectAsync(cancellationToken);
            
            if (!canConnect)
            {
                return HealthCheckResult.Unhealthy("Cannot connect to database");
            }

            // Test a simple query
            var carrierCount = await _context.Carriers.CountAsync(cancellationToken);
            
            var data = new Dictionary<string, object>
            {
                ["database"] = "PostgreSQL",
                ["carriers_count"] = carrierCount,
                ["connection_state"] = _context.Database.GetConnectionString(),
                ["checked_at"] = DateTime.UtcNow
            };

            _logger.LogDebug("Database health check passed. Carriers count: {CarrierCount}", carrierCount);
            
            return HealthCheckResult.Healthy("Database is healthy", data);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Database health check failed");
            return HealthCheckResult.Unhealthy("Database health check failed", ex);
        }
    }
}
