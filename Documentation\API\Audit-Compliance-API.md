# 📋 Audit & Compliance Service API Documentation

## 📋 Overview

The Audit & Compliance Service provides comprehensive audit logging, compliance reporting, data retention management, and regulatory compliance features. This service is 70% complete with core audit features production-ready and advanced compliance capabilities in development.

**Base URL**: `/api/v1/audit` (via API Gateway)  
**Direct URL**: `http://localhost:5012`  
**Authentication**: Required for all endpoints  
**Authorization**: Role-based access control

## 🔐 Authentication & Permissions

### Required Permissions

| Operation                   | Permission                  | Roles                             |
| --------------------------- | --------------------------- | --------------------------------- |
| Create audit logs           | `audit.logs.create`         | Admin, System                     |
| View audit logs             | `audit.logs.read`           | Admin, Auditor, ComplianceOfficer |
| Search audit trail          | `audit.trail.search`        | Admin, Auditor, ComplianceOfficer |
| Generate compliance reports | `compliance.reports.create` | Admin, ComplianceOfficer          |
| View compliance reports     | `compliance.reports.read`   | Admin, ComplianceOfficer, Auditor |
| Manage retention policies   | `audit.retention.manage`    | Admin                             |
| Export audit data           | `audit.export`              | Admin, Auditor                    |
| Manage modules              | `audit.modules.manage`      | Admin                             |

## 📊 Core Endpoints

### 1. Audit Logging

#### Create Audit Log Entry

```http
POST /api/v1/audit/logs
Authorization: Bearer <token>
Content-Type: application/json

{
  "action": "UserLogin",
  "entityType": "User",
  "entityId": "user-uuid",
  "userId": "user-uuid",
  "userName": "<EMAIL>",
  "serviceName": "UserManagement",
  "description": "User successfully logged in",
  "ipAddress": "*************",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
  "sessionId": "session-uuid",
  "correlationId": "correlation-uuid",
  "additionalData": {
    "loginMethod": "OAuth2",
    "deviceType": "Desktop",
    "location": "Mumbai, India"
  },
  "complianceFlags": ["GDPR", "SOX"],
  "riskLevel": "Low",
  "severity": "Information"
}
```

**Response:**

```json
{
  "auditLogId": "audit-uuid",
  "status": "Created",
  "message": "Audit log created successfully",
  "timestamp": "2024-01-15T10:30:00Z",
  "retentionDate": "2031-01-15T10:30:00Z",
  "complianceScore": 95.5,
  "links": {
    "self": "/api/v1/audit/logs/audit-uuid",
    "trail": "/api/v1/audit/trail"
  }
}
```

#### Get Audit Log by ID

```http
GET /api/v1/audit/logs/{auditLogId}
Authorization: Bearer <token>
```

**Response:**

```json
{
  "id": "audit-uuid",
  "action": "UserLogin",
  "entityType": "User",
  "entityId": "user-uuid",
  "userId": "user-uuid",
  "userName": "<EMAIL>",
  "serviceName": "UserManagement",
  "description": "User successfully logged in",
  "timestamp": "2024-01-15T10:30:00Z",
  "ipAddress": "*************",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
  "sessionId": "session-uuid",
  "correlationId": "correlation-uuid",
  "additionalData": {
    "loginMethod": "OAuth2",
    "deviceType": "Desktop",
    "location": "Mumbai, India"
  },
  "complianceFlags": ["GDPR", "SOX"],
  "riskLevel": "Low",
  "severity": "Information",
  "retentionDate": "2031-01-15T10:30:00Z",
  "complianceScore": 95.5,
  "isArchived": false,
  "archiveDate": null
}
```

#### Search Audit Trail

```http
POST /api/v1/audit/trail
Authorization: Bearer <token>
Content-Type: application/json

{
  "fromDate": "2024-01-01T00:00:00Z",
  "toDate": "2024-01-31T23:59:59Z",
  "actions": ["UserLogin", "UserLogout", "OrderCreated"],
  "entityTypes": ["User", "Order"],
  "userIds": ["user-uuid-1", "user-uuid-2"],
  "serviceNames": ["UserManagement", "OrderManagement"],
  "riskLevels": ["Medium", "High"],
  "severities": ["Warning", "Error"],
  "complianceFlags": ["GDPR", "SOX"],
  "ipAddresses": ["*************"],
  "searchText": "login failed",
  "includeSensitiveData": false,
  "page": 1,
  "pageSize": 50,
  "sortBy": "timestamp",
  "sortDirection": "desc"
}
```

**Response:**

```json
{
  "data": [
    {
      "id": "audit-uuid",
      "action": "UserLogin",
      "entityType": "User",
      "entityId": "user-uuid",
      "userName": "<EMAIL>",
      "serviceName": "UserManagement",
      "description": "User successfully logged in",
      "timestamp": "2024-01-15T10:30:00Z",
      "ipAddress": "*************",
      "riskLevel": "Low",
      "severity": "Information",
      "complianceFlags": ["GDPR"],
      "complianceScore": 95.5
    }
  ],
  "pagination": {
    "currentPage": 1,
    "pageSize": 50,
    "totalItems": 1250,
    "totalPages": 25,
    "hasNextPage": true,
    "hasPreviousPage": false
  },
  "summary": {
    "totalRecords": 1250,
    "dateRange": {
      "fromDate": "2024-01-01T00:00:00Z",
      "toDate": "2024-01-31T23:59:59Z"
    },
    "actionBreakdown": {
      "UserLogin": 450,
      "UserLogout": 420,
      "OrderCreated": 380
    },
    "riskLevelBreakdown": {
      "Low": 1100,
      "Medium": 120,
      "High": 25,
      "Critical": 5
    },
    "complianceScore": 94.2
  }
}
```

### 2. Compliance Reporting

#### Create Compliance Report

```http
POST /api/v1/audit/compliance/reports
Authorization: Bearer <token>
Content-Type: application/json

{
  "reportType": "GDPR_Compliance",
  "title": "GDPR Compliance Report - January 2024",
  "description": "Monthly GDPR compliance assessment",
  "period": {
    "fromDate": "2024-01-01T00:00:00Z",
    "toDate": "2024-01-31T23:59:59Z"
  },
  "scope": {
    "services": ["UserManagement", "OrderManagement", "DataStorage"],
    "entityTypes": ["User", "Order", "Document"],
    "complianceFrameworks": ["GDPR", "SOX"]
  },
  "includeFlags": {
    "includeSensitiveData": false,
    "includePersonalData": true,
    "includeFinancialData": true,
    "includeSecurityEvents": true
  },
  "customFilters": {
    "riskLevel": ["Medium", "High", "Critical"],
    "dataProcessingActivities": ["Collection", "Processing", "Storage", "Transfer"]
  },
  "outputFormat": "PDF",
  "deliveryMethod": "Email",
  "recipients": ["<EMAIL>", "<EMAIL>"]
}
```

**Response:**

```json
{
  "reportId": "report-uuid",
  "status": "Processing",
  "estimatedCompletionTime": "2024-01-15T10:45:00Z",
  "createdAt": "2024-01-15T10:30:00Z",
  "reportType": "GDPR_Compliance",
  "title": "GDPR Compliance Report - January 2024",
  "generatedBy": "<EMAIL>",
  "links": {
    "status": "/api/v1/audit/compliance/reports/report-uuid/status",
    "download": "/api/v1/audit/compliance/reports/report-uuid/download"
  }
}
```

#### Get Compliance Report

```http
GET /api/v1/audit/compliance/reports/{reportId}
Authorization: Bearer <token>
```

**Response:**

```json
{
  "id": "report-uuid",
  "reportType": "GDPR_Compliance",
  "title": "GDPR Compliance Report - January 2024",
  "description": "Monthly GDPR compliance assessment",
  "status": "Completed",
  "generatedAt": "2024-01-15T10:42:00Z",
  "generatedBy": "<EMAIL>",
  "period": {
    "fromDate": "2024-01-01T00:00:00Z",
    "toDate": "2024-01-31T23:59:59Z"
  },
  "summary": {
    "overallComplianceScore": 92.5,
    "totalDataSubjects": 15000,
    "dataProcessingActivities": 45,
    "complianceViolations": 3,
    "riskAssessment": "Medium",
    "recommendationsCount": 8
  },
  "sections": [
    {
      "title": "Data Processing Activities",
      "content": "Analysis of data processing activities during the reporting period",
      "metrics": {
        "totalActivities": 45,
        "compliantActivities": 42,
        "nonCompliantActivities": 3,
        "complianceRate": 93.3
      },
      "status": "Completed",
      "recommendations": [
        "Implement additional consent mechanisms for data collection",
        "Review data retention policies for customer data"
      ]
    },
    {
      "title": "Data Subject Rights",
      "content": "Assessment of data subject rights fulfillment",
      "metrics": {
        "accessRequests": 125,
        "deletionRequests": 45,
        "rectificationRequests": 28,
        "averageResponseTime": "2.5 days"
      },
      "status": "Completed",
      "recommendations": [
        "Automate data subject request processing",
        "Improve response time for deletion requests"
      ]
    }
  ],
  "violations": [
    {
      "id": "violation-uuid",
      "type": "Data Retention",
      "severity": "Medium",
      "description": "Customer data retained beyond policy period",
      "affectedRecords": 150,
      "detectedAt": "2024-01-20T09:15:00Z",
      "status": "Remediated"
    }
  ],
  "recommendations": [
    {
      "priority": "High",
      "category": "Data Protection",
      "description": "Implement automated data retention policy enforcement",
      "estimatedEffort": "2 weeks",
      "expectedImpact": "Reduce retention violations by 80%"
    }
  ],
  "fileSize": 2048576,
  "fileSizeFormatted": "2.00 MB",
  "downloadUrl": "/api/v1/audit/compliance/reports/report-uuid/download",
  "expiresAt": "2024-02-15T10:42:00Z"
}
```

#### Search Compliance Reports

```http
POST /api/v1/audit/compliance/reports/search
Authorization: Bearer <token>
Content-Type: application/json

{
  "reportTypes": ["GDPR_Compliance", "SOX_Compliance"],
  "status": ["Completed", "Processing"],
  "fromDate": "2024-01-01T00:00:00Z",
  "toDate": "2024-01-31T23:59:59Z",
  "generatedBy": "<EMAIL>",
  "complianceScoreRange": {
    "min": 80.0,
    "max": 100.0
  },
  "page": 1,
  "pageSize": 20,
  "sortBy": "generatedAt",
  "sortDirection": "desc"
}
```

### 3. Data Retention Management

#### Create Retention Policy

```http
POST /api/v1/audit/retention-policies
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "User Data Retention Policy",
  "description": "Retention policy for user personal data under GDPR",
  "entityType": "User",
  "retentionPeriod": "P7Y",
  "isActive": true,
  "complianceFramework": "GDPR",
  "criteria": {
    "dataTypes": ["PersonalData", "ContactInformation"],
    "conditions": [
      {
        "field": "lastLoginDate",
        "operator": "OlderThan",
        "value": "P2Y"
      }
    ]
  },
  "actions": [
    {
      "type": "Archive",
      "priority": 1,
      "configuration": {
        "storageClass": "ColdStorage",
        "encryptionEnabled": true
      }
    },
    {
      "type": "Delete",
      "priority": 2,
      "configuration": {
        "hardDelete": true,
        "confirmationRequired": true
      }
    }
  ],
  "approvalRequired": true
}
```

### 4. Enhanced Export Functionality

#### Create Enhanced Export

```http
POST /api/v1/audit/export
Authorization: Bearer <token>
Content-Type: application/json

{
  "exportType": "AuditTrail",
  "title": "Q1 2024 Audit Export",
  "format": "Excel",
  "filters": {
    "fromDate": "2024-01-01T00:00:00Z",
    "toDate": "2024-03-31T23:59:59Z",
    "services": ["UserManagement", "OrderManagement"],
    "riskLevels": ["Medium", "High", "Critical"]
  },
  "options": {
    "includePersonalData": false,
    "compressionEnabled": true,
    "encryptionEnabled": true,
    "digitalSignatureRequired": true
  },
  "deliveryMethod": "Download",
  "retentionPeriod": "P30D"
}
```

## 🔄 Real-time Updates

### SignalR Hub: `/api/v1/audit/hub`

**Events:**

- `AuditLogCreated` - New audit log entry created
- `ComplianceReportGenerated` - Compliance report completed
- `RetentionPolicyExecuted` - Retention policy executed
- `ExportJobCompleted` - Export job completed
- `ComplianceViolationDetected` - Compliance violation detected
- `ModuleHealthChanged` - Module health status changed

## ❌ Error Responses

### Common Error Codes

| Code | Message                     | Description                                   |
| ---- | --------------------------- | --------------------------------------------- |
| 400  | `INVALID_DATE_RANGE`        | Invalid date range specified                  |
| 400  | `INVALID_RETENTION_PERIOD`  | Invalid retention period format               |
| 401  | `UNAUTHORIZED`              | Authentication required                       |
| 403  | `INSUFFICIENT_PERMISSIONS`  | Insufficient permissions for audit operations |
| 404  | `AUDIT_LOG_NOT_FOUND`       | Audit log not found                           |
| 404  | `REPORT_NOT_FOUND`          | Compliance report not found                   |
| 409  | `RETENTION_POLICY_CONFLICT` | Conflicting retention policy exists           |
| 422  | `VALIDATION_FAILED`         | Input validation failed                       |
| 429  | `RATE_LIMIT_EXCEEDED`       | Too many requests                             |

### Error Response Format

```json
{
  "error": {
    "code": "INVALID_DATE_RANGE",
    "message": "Invalid date range specified",
    "details": "End date must be after start date",
    "timestamp": "2024-01-01T00:00:00Z",
    "traceId": "trace-uuid"
  }
}
```
