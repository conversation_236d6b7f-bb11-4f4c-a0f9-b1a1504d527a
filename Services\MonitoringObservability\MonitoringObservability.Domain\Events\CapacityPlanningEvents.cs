using MonitoringObservability.Domain.Enums;
using Shared.Domain.Common;

namespace MonitoringObservability.Domain.Events;

// Capacity Plan Events
public record CapacityPlanUpdatedEvent(Guid PlanId, string PlanName) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ResourceForecastAddedEvent(Guid PlanId, Guid ForecastId, ResourceType ResourceType) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record CapacityAlertCreatedEvent(Guid PlanId, Guid AlertId, CapacityAlertSeverity Severity) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// Cost Optimization Events
public record CostOptimizationAnalysisUpdatedEvent(Guid AnalysisId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record CostRecommendationCreatedEvent(Guid AnalysisId, Guid RecommendationId, string Priority) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record CostAlertCreatedEvent(Guid AnalysisId, Guid AlertId, string Severity) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record CostDataUpdatedEvent(Guid AnalysisId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record CostOptimizationAnalysisExecutedEvent(Guid AnalysisId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record CostOptimizationAnalysisActivatedEvent(Guid AnalysisId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record CostOptimizationAnalysisDeactivatedEvent(Guid AnalysisId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// SLA Events
public record SLAUpdatedEvent(Guid SLAId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record SLAObjectiveAddedEvent(Guid SLAId, Guid ObjectiveId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record SLAObjectiveRemovedEvent(Guid SLAId, Guid ObjectiveId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record SLABreachDetectedEvent(Guid SLAId, string ObjectiveName, string Severity) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record SLAComplianceUpdatedEvent(Guid SLAId, decimal CompliancePercentage) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record SLAActivatedEvent(Guid SLAId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record SLADeactivatedEvent(Guid SLAId, string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record SLASuspendedEvent(Guid SLAId, string Name, string Reason) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ScalingRecommendationCreatedEvent(Guid PlanId, Guid RecommendationId, ScalingAction Action) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record CapacityPlanExecutedEvent(Guid PlanId, string PlanName) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record CapacityPlanActivatedEvent(Guid PlanId, string PlanName) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record CapacityPlanDeactivatedEvent(Guid PlanId, string PlanName) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}
