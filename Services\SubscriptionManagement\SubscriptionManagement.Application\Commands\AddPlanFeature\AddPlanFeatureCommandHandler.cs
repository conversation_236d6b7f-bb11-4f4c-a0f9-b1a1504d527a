using MediatR;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Exceptions;
using Shared.Messaging;

namespace SubscriptionManagement.Application.Commands.AddPlanFeature;

public class AddPlanFeatureCommandHandler : IRequestHandler<AddPlanFeatureCommand, bool>
{
    private readonly IPlanRepository _planRepository;
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<AddPlanFeatureCommandHandler> _logger;

    public AddPlanFeatureCommandHandler(
        IPlanRepository planRepository,
        IMessageBroker messageBroker,
        ILogger<AddPlanFeatureCommandHandler> logger)
    {
        _planRepository = planRepository;
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task<bool> Handle(AddPlanFeatureCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Adding feature {FeatureType} to plan {PlanId}", 
            request.FeatureType, request.PlanId);

        var plan = await _planRepository.GetByIdAsync(request.PlanId);
        if (plan == null)
        {
            throw new SubscriptionDomainException("Plan not found");
        }

        // Add the feature to the plan
        plan.AddFeature(request.FeatureType, request.AccessType, request.LimitValue, request.Description);

        // Save changes
        await _planRepository.UpdateAsync(plan);

        // Publish event
        await _messageBroker.PublishAsync("plan.feature.added", new
        {
            PlanId = plan.Id,
            PlanName = plan.Name,
            FeatureType = request.FeatureType.ToString(),
            AccessType = request.AccessType.ToString(),
            LimitValue = request.LimitValue,
            Description = request.Description,
            AddedAt = DateTime.UtcNow
        });

        _logger.LogInformation("Successfully added feature {FeatureType} to plan {PlanId}", 
            request.FeatureType, request.PlanId);

        return true;
    }
}
