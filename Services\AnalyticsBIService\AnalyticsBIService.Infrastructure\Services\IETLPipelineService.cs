using AnalyticsBIService.Domain.Enums;

namespace AnalyticsBIService.Infrastructure.Services;

/// <summary>
/// Interface for ETL pipeline service
/// </summary>
public interface IETLPipelineService
{
    /// <summary>
    /// Create a new ETL pipeline
    /// </summary>
    Task<ETLPipelineDto> CreatePipelineAsync(CreateETLPipelineRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get ETL pipeline by ID
    /// </summary>
    Task<ETLPipelineDto?> GetPipelineAsync(Guid pipelineId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all pipelines for a user
    /// </summary>
    Task<List<ETLPipelineDto>> GetUserPipelinesAsync(Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Execute ETL pipeline
    /// </summary>
    Task<ETLExecutionResultDto> ExecutePipelineAsync(Guid pipelineId, ETLExecutionOptionsDto? options = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Schedule ETL pipeline execution
    /// </summary>
    Task<ETLScheduleDto> SchedulePipelineAsync(ScheduleETLPipelineRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get pipeline execution history
    /// </summary>
    Task<List<ETLExecutionHistoryDto>> GetExecutionHistoryAsync(Guid pipelineId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get pipeline execution status
    /// </summary>
    Task<ETLExecutionStatusDto> GetExecutionStatusAsync(Guid executionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Cancel pipeline execution
    /// </summary>
    Task<bool> CancelExecutionAsync(Guid executionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update pipeline configuration
    /// </summary>
    Task<bool> UpdatePipelineAsync(Guid pipelineId, UpdateETLPipelineRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete ETL pipeline
    /// </summary>
    Task<bool> DeletePipelineAsync(Guid pipelineId, Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate pipeline configuration
    /// </summary>
    Task<ETLValidationResult> ValidatePipelineAsync(ETLPipelineConfigurationDto configuration, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get available data sources
    /// </summary>
    Task<List<ETLDataSourceDto>> GetAvailableDataSourcesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get available transformations
    /// </summary>
    Task<List<ETLTransformationDto>> GetAvailableTransformationsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Test data source connection
    /// </summary>
    Task<ETLConnectionTestResult> TestDataSourceConnectionAsync(ETLDataSourceConfigDto dataSource, CancellationToken cancellationToken = default);

    /// <summary>
    /// Preview pipeline data
    /// </summary>
    Task<ETLDataPreviewDto> PreviewPipelineDataAsync(Guid pipelineId, int maxRows = 100, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get pipeline metrics
    /// </summary>
    Task<ETLPipelineMetricsDto> GetPipelineMetricsAsync(Guid pipelineId, CancellationToken cancellationToken = default);
}

/// <summary>
/// ETL pipeline data transfer object
/// </summary>
public class ETLPipelineDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public ETLPipelineConfigurationDto Configuration { get; set; } = new();
    public string Status { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public Guid CreatedBy { get; set; }
    public bool IsActive { get; set; }
    public DateTime? LastExecuted { get; set; }
    public int ExecutionCount { get; set; }
    public string? LastExecutionStatus { get; set; }
}

/// <summary>
/// ETL pipeline configuration data transfer object
/// </summary>
public class ETLPipelineConfigurationDto
{
    public List<ETLDataSourceConfigDto> DataSources { get; set; } = new();
    public List<ETLTransformationConfigDto> Transformations { get; set; } = new();
    public List<ETLDestinationConfigDto> Destinations { get; set; } = new();
    public ETLExecutionSettingsDto ExecutionSettings { get; set; } = new();
    public Dictionary<string, object> Variables { get; set; } = new();
    public List<ETLValidationRuleDto> ValidationRules { get; set; } = new();
}

/// <summary>
/// ETL data source configuration data transfer object
/// </summary>
public class ETLDataSourceConfigDto
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string ConnectionString { get; set; } = string.Empty;
    public Dictionary<string, object> Parameters { get; set; } = new();
    public string Query { get; set; } = string.Empty;
    public List<ETLFieldMappingDto> FieldMappings { get; set; } = new();
    public ETLDataSourceOptionsDto Options { get; set; } = new();
}

/// <summary>
/// ETL data source options data transfer object
/// </summary>
public class ETLDataSourceOptionsDto
{
    public int BatchSize { get; set; } = 1000;
    public int TimeoutSeconds { get; set; } = 300;
    public bool UseIncrementalLoad { get; set; }
    public string? IncrementalField { get; set; }
    public object? LastIncrementalValue { get; set; }
    public Dictionary<string, object> CustomOptions { get; set; } = new();
}

/// <summary>
/// ETL field mapping data transfer object
/// </summary>
public class ETLFieldMappingDto
{
    public string SourceField { get; set; } = string.Empty;
    public string TargetField { get; set; } = string.Empty;
    public string DataType { get; set; } = string.Empty;
    public bool IsRequired { get; set; }
    public object? DefaultValue { get; set; }
    public List<ETLFieldTransformationDto> Transformations { get; set; } = new();
}

/// <summary>
/// ETL field transformation data transfer object
/// </summary>
public class ETLFieldTransformationDto
{
    public string Type { get; set; } = string.Empty;
    public Dictionary<string, object> Parameters { get; set; } = new();
    public int Order { get; set; }
}

/// <summary>
/// ETL transformation configuration data transfer object
/// </summary>
public class ETLTransformationConfigDto
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public Dictionary<string, object> Parameters { get; set; } = new();
    public List<string> InputSources { get; set; } = new();
    public string OutputName { get; set; } = string.Empty;
    public int Order { get; set; }
    public bool IsEnabled { get; set; } = true;
}

/// <summary>
/// ETL destination configuration data transfer object
/// </summary>
public class ETLDestinationConfigDto
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string ConnectionString { get; set; } = string.Empty;
    public string TargetTable { get; set; } = string.Empty;
    public string WriteMode { get; set; } = "Insert";
    public Dictionary<string, object> Parameters { get; set; } = new();
    public ETLDestinationOptionsDto Options { get; set; } = new();
}

/// <summary>
/// ETL destination options data transfer object
/// </summary>
public class ETLDestinationOptionsDto
{
    public int BatchSize { get; set; } = 1000;
    public bool CreateTableIfNotExists { get; set; }
    public bool TruncateBeforeLoad { get; set; }
    public List<string> UniqueKeys { get; set; } = new();
    public Dictionary<string, object> CustomOptions { get; set; } = new();
}

/// <summary>
/// ETL execution settings data transfer object
/// </summary>
public class ETLExecutionSettingsDto
{
    public int MaxParallelism { get; set; } = 1;
    public int TimeoutMinutes { get; set; } = 60;
    public bool ContinueOnError { get; set; }
    public int MaxRetries { get; set; } = 3;
    public int RetryDelaySeconds { get; set; } = 30;
    public bool EnableLogging { get; set; } = true;
    public string LogLevel { get; set; } = "Information";
    public Dictionary<string, object> CustomSettings { get; set; } = new();
}

/// <summary>
/// ETL validation rule data transfer object
/// </summary>
public class ETLValidationRuleDto
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Field { get; set; } = string.Empty;
    public Dictionary<string, object> Parameters { get; set; } = new();
    public string Severity { get; set; } = "Error";
    public bool IsEnabled { get; set; } = true;
}

/// <summary>
/// ETL execution result data transfer object
/// </summary>
public class ETLExecutionResultDto
{
    public Guid ExecutionId { get; set; }
    public Guid PipelineId { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public TimeSpan? Duration { get; set; }
    public int RecordsProcessed { get; set; }
    public int RecordsSuccessful { get; set; }
    public int RecordsFailed { get; set; }
    public List<ETLStepResultDto> StepResults { get; set; } = new();
    public List<ETLExecutionLogDto> Logs { get; set; } = new();
    public string? ErrorMessage { get; set; }
    public ETLExecutionMetricsDto Metrics { get; set; } = new();
}

/// <summary>
/// ETL step result data transfer object
/// </summary>
public class ETLStepResultDto
{
    public string StepId { get; set; } = string.Empty;
    public string StepName { get; set; } = string.Empty;
    public string StepType { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public TimeSpan? Duration { get; set; }
    public int RecordsProcessed { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object> Metrics { get; set; } = new();
}

/// <summary>
/// ETL execution log data transfer object
/// </summary>
public class ETLExecutionLogDto
{
    public DateTime Timestamp { get; set; }
    public string Level { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string? StepId { get; set; }
    public Dictionary<string, object> Properties { get; set; } = new();
}

/// <summary>
/// ETL execution metrics data transfer object
/// </summary>
public class ETLExecutionMetricsDto
{
    public decimal ThroughputRecordsPerSecond { get; set; }
    public long MemoryUsageBytes { get; set; }
    public decimal CpuUsagePercent { get; set; }
    public int ActiveConnections { get; set; }
    public Dictionary<string, object> CustomMetrics { get; set; } = new();
}

/// <summary>
/// ETL execution options data transfer object
/// </summary>
public class ETLExecutionOptionsDto
{
    public bool FullRefresh { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public Dictionary<string, object> Variables { get; set; } = new();
    public List<string> StepsToExecute { get; set; } = new();
    public bool DryRun { get; set; }
}

/// <summary>
/// ETL schedule data transfer object
/// </summary>
public class ETLScheduleDto
{
    public Guid Id { get; set; }
    public Guid PipelineId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string ScheduleType { get; set; } = string.Empty;
    public string CronExpression { get; set; } = string.Empty;
    public DateTime NextRunDate { get; set; }
    public DateTime? LastRunDate { get; set; }
    public bool IsActive { get; set; }
    public ETLExecutionOptionsDto? ExecutionOptions { get; set; }
    public DateTime CreatedAt { get; set; }
}

/// <summary>
/// ETL execution history data transfer object
/// </summary>
public class ETLExecutionHistoryDto
{
    public Guid ExecutionId { get; set; }
    public DateTime StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string Status { get; set; } = string.Empty;
    public TimeSpan? Duration { get; set; }
    public int RecordsProcessed { get; set; }
    public string? ErrorMessage { get; set; }
    public string TriggerType { get; set; } = string.Empty;
    public Guid? TriggeredBy { get; set; }
}

/// <summary>
/// ETL execution status data transfer object
/// </summary>
public class ETLExecutionStatusDto
{
    public Guid ExecutionId { get; set; }
    public string Status { get; set; } = string.Empty;
    public decimal ProgressPercentage { get; set; }
    public string CurrentStep { get; set; } = string.Empty;
    public int ProcessedRecords { get; set; }
    public int TotalRecords { get; set; }
    public DateTime StartedAt { get; set; }
    public TimeSpan ElapsedTime { get; set; }
    public TimeSpan? EstimatedTimeRemaining { get; set; }
    public List<ETLStepStatusDto> StepStatuses { get; set; } = new();
}

/// <summary>
/// ETL step status data transfer object
/// </summary>
public class ETLStepStatusDto
{
    public string StepId { get; set; } = string.Empty;
    public string StepName { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public decimal ProgressPercentage { get; set; }
    public int ProcessedRecords { get; set; }
    public string? CurrentOperation { get; set; }
}

/// <summary>
/// ETL validation result data transfer object
/// </summary>
public class ETLValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public List<ETLValidationIssueDto> Issues { get; set; } = new();
    public ETLValidationSummaryDto Summary { get; set; } = new();
}

/// <summary>
/// ETL validation issue data transfer object
/// </summary>
public class ETLValidationIssueDto
{
    public string Type { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string? StepId { get; set; }
    public string? Field { get; set; }
    public Dictionary<string, object> Context { get; set; } = new();
}

/// <summary>
/// ETL validation summary data transfer object
/// </summary>
public class ETLValidationSummaryDto
{
    public int TotalIssues { get; set; }
    public int ErrorCount { get; set; }
    public int WarningCount { get; set; }
    public int InfoCount { get; set; }
    public List<string> RecommendedActions { get; set; } = new();
}

/// <summary>
/// ETL data source data transfer object
/// </summary>
public class ETLDataSourceDto
{
    public string Type { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<string> SupportedOperations { get; set; } = new();
    public List<ETLParameterDto> RequiredParameters { get; set; } = new();
    public List<ETLParameterDto> OptionalParameters { get; set; } = new();
    public string Category { get; set; } = string.Empty;
}

/// <summary>
/// ETL transformation data transfer object
/// </summary>
public class ETLTransformationDto
{
    public string Type { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<ETLParameterDto> Parameters { get; set; } = new();
    public string Category { get; set; } = string.Empty;
    public List<string> SupportedDataTypes { get; set; } = new();
}

/// <summary>
/// ETL parameter data transfer object
/// </summary>
public class ETLParameterDto
{
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public bool IsRequired { get; set; }
    public object? DefaultValue { get; set; }
    public List<object> AllowedValues { get; set; } = new();
}

/// <summary>
/// ETL connection test result data transfer object
/// </summary>
public class ETLConnectionTestResult
{
    public bool IsSuccessful { get; set; }
    public string? ErrorMessage { get; set; }
    public TimeSpan ResponseTime { get; set; }
    public Dictionary<string, object> ConnectionInfo { get; set; } = new();
    public List<string> AvailableTables { get; set; } = new();
}

/// <summary>
/// ETL data preview data transfer object
/// </summary>
public class ETLDataPreviewDto
{
    public List<Dictionary<string, object>> SampleData { get; set; } = new();
    public List<ETLFieldInfoDto> Fields { get; set; } = new();
    public int TotalRecords { get; set; }
    public DateTime GeneratedAt { get; set; }
}

/// <summary>
/// ETL field info data transfer object
/// </summary>
public class ETLFieldInfoDto
{
    public string Name { get; set; } = string.Empty;
    public string DataType { get; set; } = string.Empty;
    public bool IsNullable { get; set; }
    public int? MaxLength { get; set; }
    public object? SampleValue { get; set; }
}

/// <summary>
/// ETL pipeline metrics data transfer object
/// </summary>
public class ETLPipelineMetricsDto
{
    public Guid PipelineId { get; set; }
    public int TotalExecutions { get; set; }
    public int SuccessfulExecutions { get; set; }
    public int FailedExecutions { get; set; }
    public decimal SuccessRate { get; set; }
    public TimeSpan AverageExecutionTime { get; set; }
    public long TotalRecordsProcessed { get; set; }
    public decimal AverageThroughput { get; set; }
    public DateTime? LastSuccessfulExecution { get; set; }
    public DateTime? LastFailedExecution { get; set; }
    public List<ETLPerformanceTrendDto> PerformanceTrends { get; set; } = new();
}

/// <summary>
/// ETL performance trend data transfer object
/// </summary>
public class ETLPerformanceTrendDto
{
    public DateTime Date { get; set; }
    public TimeSpan ExecutionTime { get; set; }
    public int RecordsProcessed { get; set; }
    public decimal Throughput { get; set; }
    public string Status { get; set; } = string.Empty;
}

// Request DTOs
/// <summary>
/// Create ETL pipeline request
/// </summary>
public class CreateETLPipelineRequest
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public ETLPipelineConfigurationDto Configuration { get; set; } = new();
    public Guid UserId { get; set; }
}

/// <summary>
/// Update ETL pipeline request
/// </summary>
public class UpdateETLPipelineRequest
{
    public string? Name { get; set; }
    public string? Description { get; set; }
    public ETLPipelineConfigurationDto? Configuration { get; set; }
    public bool? IsActive { get; set; }
}

/// <summary>
/// Schedule ETL pipeline request
/// </summary>
public class ScheduleETLPipelineRequest
{
    public Guid PipelineId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string ScheduleType { get; set; } = string.Empty;
    public string CronExpression { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public ETLExecutionOptionsDto? ExecutionOptions { get; set; }
    public bool IsActive { get; set; } = true;
}
