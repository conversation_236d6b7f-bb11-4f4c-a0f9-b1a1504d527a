using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using MediatR;
using MonitoringObservability.Application.Commands;
using MonitoringObservability.Application.Queries;
using MonitoringObservability.Application.DTOs;
using MonitoringObservability.Domain.Enums;

namespace MonitoringObservability.API.Controllers;

[ApiController]
[Route("api/v{version:apiVersion}/[controller]")]
[ApiVersion("1.0")]
[Authorize]
public class HealthChecksController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<HealthChecksController> _logger;

    public HealthChecksController(IMediator mediator, ILogger<HealthChecksController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get all health checks with optional filtering
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<HealthCheckDto>>> GetHealthChecks([FromQuery] GetHealthChecksRequest request)
    {
        try
        {
            var query = new GetHealthChecksQuery
            {
                ServiceName = request.ServiceName,
                Status = request.Status,
                IsEnabled = request.IsEnabled
            };

            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get health checks");
            return StatusCode(500, "Failed to retrieve health checks");
        }
    }

    /// <summary>
    /// Get health check by ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<HealthCheckDto>> GetHealthCheck(Guid id)
    {
        try
        {
            var query = new GetHealthCheckByIdQuery { HealthCheckId = id };
            var result = await _mediator.Send(query);
            
            if (result == null)
                return NotFound();

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get health check {HealthCheckId}", id);
            return StatusCode(500, "Failed to retrieve health check");
        }
    }

    /// <summary>
    /// Get health checks for a specific service
    /// </summary>
    [HttpGet("services/{serviceName}")]
    public async Task<ActionResult<IEnumerable<HealthCheckDto>>> GetHealthChecksByService(string serviceName, [FromQuery] bool enabledOnly = true)
    {
        try
        {
            var query = new GetHealthChecksByServiceQuery { ServiceName = serviceName, EnabledOnly = enabledOnly };
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get health checks for service {ServiceName}", serviceName);
            return StatusCode(500, "Failed to retrieve service health checks");
        }
    }

    /// <summary>
    /// Create a new health check
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Admin,Monitor")]
    public async Task<ActionResult<HealthCheckDto>> CreateHealthCheck([FromBody] CreateHealthCheckRequest request)
    {
        try
        {
            var command = new CreateHealthCheckCommand
            {
                Name = request.Name,
                ServiceName = request.ServiceName,
                Description = request.Description,
                Endpoint = request.Endpoint,
                Interval = request.Interval,
                Timeout = request.Timeout,
                MaxRetries = request.MaxRetries,
                Headers = request.Headers,
                Tags = request.Tags
            };

            var result = await _mediator.Send(command);
            return CreatedAtAction(nameof(GetHealthCheck), new { id = result.Id }, result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create health check");
            return StatusCode(500, "Failed to create health check");
        }
    }

    /// <summary>
    /// Update a health check
    /// </summary>
    [HttpPut("{id}")]
    [Authorize(Roles = "Admin,Monitor")]
    public async Task<ActionResult<HealthCheckDto>> UpdateHealthCheck(Guid id, [FromBody] UpdateHealthCheckRequest request)
    {
        try
        {
            var command = new UpdateHealthCheckCommand
            {
                HealthCheckId = id,
                Name = request.Name,
                Description = request.Description,
                Endpoint = request.Endpoint,
                Interval = request.Interval,
                Timeout = request.Timeout,
                MaxRetries = request.MaxRetries,
                Headers = request.Headers,
                Tags = request.Tags
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update health check {HealthCheckId}", id);
            return StatusCode(500, "Failed to update health check");
        }
    }

    /// <summary>
    /// Execute a health check manually
    /// </summary>
    [HttpPost("{id}/execute")]
    public async Task<ActionResult<HealthCheckDto>> ExecuteHealthCheck(Guid id, [FromQuery] bool force = false)
    {
        try
        {
            var command = new ExecuteHealthCheckCommand { HealthCheckId = id, ForceExecution = force };
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to execute health check {HealthCheckId}", id);
            return StatusCode(500, "Failed to execute health check");
        }
    }

    /// <summary>
    /// Execute all health checks for a service
    /// </summary>
    [HttpPost("services/{serviceName}/execute")]
    [Authorize(Roles = "Admin,Monitor")]
    public async Task<ActionResult<IEnumerable<HealthCheckDto>>> ExecuteServiceHealthChecks(string serviceName)
    {
        try
        {
            var command = new ExecuteAllHealthChecksCommand { ServiceName = serviceName };
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to execute health checks for service {ServiceName}", serviceName);
            return StatusCode(500, "Failed to execute service health checks");
        }
    }

    /// <summary>
    /// Enable a health check
    /// </summary>
    [HttpPost("{id}/enable")]
    [Authorize(Roles = "Admin,Monitor")]
    public async Task<ActionResult<HealthCheckDto>> EnableHealthCheck(Guid id)
    {
        try
        {
            var command = new EnableHealthCheckCommand { HealthCheckId = id };
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to enable health check {HealthCheckId}", id);
            return StatusCode(500, "Failed to enable health check");
        }
    }

    /// <summary>
    /// Disable a health check
    /// </summary>
    [HttpPost("{id}/disable")]
    [Authorize(Roles = "Admin,Monitor")]
    public async Task<ActionResult<HealthCheckDto>> DisableHealthCheck(Guid id)
    {
        try
        {
            var command = new DisableHealthCheckCommand { HealthCheckId = id };
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to disable health check {HealthCheckId}", id);
            return StatusCode(500, "Failed to disable health check");
        }
    }

    /// <summary>
    /// Delete a health check
    /// </summary>
    [HttpDelete("{id}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> DeleteHealthCheck(Guid id)
    {
        try
        {
            var command = new DeleteHealthCheckCommand { HealthCheckId = id };
            await _mediator.Send(command);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete health check {HealthCheckId}", id);
            return StatusCode(500, "Failed to delete health check");
        }
    }

    /// <summary>
    /// Get service health summary
    /// </summary>
    [HttpGet("services/{serviceName}/summary")]
    public async Task<ActionResult<ServiceHealthSummaryDto>> GetServiceHealthSummary(
        string serviceName, 
        [FromQuery] bool includeMetrics = true,
        [FromQuery] bool includeAlerts = true,
        [FromQuery] bool includeIncidents = true)
    {
        try
        {
            var query = new GetServiceHealthSummaryQuery
            {
                ServiceName = serviceName,
                IncludeMetrics = includeMetrics,
                IncludeAlerts = includeAlerts,
                IncludeIncidents = includeIncidents
            };

            var result = await _mediator.Send(query);
            
            if (result == null)
                return NotFound();

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get health summary for service {ServiceName}", serviceName);
            return StatusCode(500, "Failed to retrieve service health summary");
        }
    }

    /// <summary>
    /// Get all services health summary
    /// </summary>
    [HttpGet("services/summary")]
    public async Task<ActionResult<IEnumerable<ServiceHealthSummaryDto>>> GetAllServicesHealthSummary(
        [FromQuery] bool includeMetrics = false,
        [FromQuery] bool includeAlerts = true,
        [FromQuery] bool includeIncidents = true)
    {
        try
        {
            var query = new GetAllServicesHealthSummaryQuery
            {
                IncludeMetrics = includeMetrics,
                IncludeAlerts = includeAlerts,
                IncludeIncidents = includeIncidents
            };

            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get all services health summary");
            return StatusCode(500, "Failed to retrieve services health summary");
        }
    }

    /// <summary>
    /// Get health status statistics
    /// </summary>
    [HttpGet("statistics")]
    public async Task<ActionResult<HealthStatusStatisticsDto>> GetHealthStatusStatistics([FromQuery] string? serviceName = null)
    {
        try
        {
            var query = new GetHealthStatusSummaryQuery { ServiceName = serviceName };
            var result = await _mediator.Send(query);

            var statistics = new HealthStatusStatisticsDto
            {
                StatusCounts = result,
                ServiceName = serviceName,
                GeneratedAt = DateTime.UtcNow
            };

            return Ok(statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get health status statistics");
            return StatusCode(500, "Failed to retrieve health status statistics");
        }
    }

    /// <summary>
    /// Get service uptime
    /// </summary>
    [HttpGet("services/{serviceName}/uptime")]
    public async Task<ActionResult<ServiceUptimeDto>> GetServiceUptime(string serviceName, [FromQuery] TimeSpan? period = null)
    {
        try
        {
            var query = new GetServiceUptimeQuery
            {
                ServiceName = serviceName,
                Period = period ?? TimeSpan.FromDays(30)
            };

            var uptime = await _mediator.Send(query);

            var result = new ServiceUptimeDto
            {
                ServiceName = serviceName,
                UptimePercentage = uptime,
                Period = query.Period,
                CalculatedAt = DateTime.UtcNow
            };

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get uptime for service {ServiceName}", serviceName);
            return StatusCode(500, "Failed to retrieve service uptime");
        }
    }
}

// Request DTOs
public class GetHealthChecksRequest
{
    public string? ServiceName { get; set; }
    public HealthStatus? Status { get; set; }
    public bool? IsEnabled { get; set; }
}

public class CreateHealthCheckRequest
{
    public string Name { get; set; } = string.Empty;
    public string ServiceName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Endpoint { get; set; } = string.Empty;
    public TimeSpan Interval { get; set; }
    public TimeSpan Timeout { get; set; }
    public int MaxRetries { get; set; } = 3;
    public Dictionary<string, string>? Headers { get; set; }
    public Dictionary<string, string>? Tags { get; set; }
}

public class UpdateHealthCheckRequest
{
    public string? Name { get; set; }
    public string? Description { get; set; }
    public string? Endpoint { get; set; }
    public TimeSpan? Interval { get; set; }
    public TimeSpan? Timeout { get; set; }
    public int? MaxRetries { get; set; }
    public Dictionary<string, string>? Headers { get; set; }
    public Dictionary<string, string>? Tags { get; set; }
}

public class HealthStatusStatisticsDto
{
    public Dictionary<HealthStatus, int> StatusCounts { get; set; } = new();
    public string? ServiceName { get; set; }
    public DateTime GeneratedAt { get; set; }
}

public class ServiceUptimeDto
{
    public string ServiceName { get; set; } = string.Empty;
    public double UptimePercentage { get; set; }
    public TimeSpan Period { get; set; }
    public DateTime CalculatedAt { get; set; }
}
