apiVersion: v1
kind: Namespace
metadata:
  name: tli-analytics
  labels:
    name: tli-analytics
    environment: production
    service: analytics-bi
    version: v1
  annotations:
    description: "Analytics & Business Intelligence Service namespace"
    contact: "<EMAIL>"
---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: analytics-resource-quota
  namespace: tli-analytics
spec:
  hard:
    requests.cpu: "4"
    requests.memory: 8Gi
    limits.cpu: "8"
    limits.memory: 16Gi
    persistentvolumeclaims: "10"
    services: "20"
    secrets: "20"
    configmaps: "20"
---
apiVersion: v1
kind: LimitRange
metadata:
  name: analytics-limit-range
  namespace: tli-analytics
spec:
  limits:
  - default:
      cpu: "1"
      memory: "1Gi"
    defaultRequest:
      cpu: "100m"
      memory: "128Mi"
    type: Container
  - default:
      storage: "10Gi"
    type: PersistentVolumeClaim
