namespace AuditCompliance.Domain.Enums;

/// <summary>
/// Status of compliance checks and reports
/// </summary>
public enum ComplianceStatus
{
    /// <summary>
    /// Compliance check is pending
    /// </summary>
    Pending = 1,
    
    /// <summary>
    /// Compliance check is in progress
    /// </summary>
    InProgress = 2,
    
    /// <summary>
    /// Fully compliant with all requirements
    /// </summary>
    Compliant = 3,
    
    /// <summary>
    /// Partially compliant - some issues found
    /// </summary>
    PartiallyCompliant = 4,
    
    /// <summary>
    /// Non-compliant - significant issues found
    /// </summary>
    NonCompliant = 5,
    
    /// <summary>
    /// Compliance check failed due to technical issues
    /// </summary>
    Failed = 6,
    
    /// <summary>
    /// Requires manual review
    /// </summary>
    RequiresReview = 7
}
