using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Infrastructure.ExternalServices;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CommunicationNotification.Infrastructure.Controllers;

/// <summary>
/// External services management and monitoring API controller
/// </summary>
[ApiController]
[Route("api/external-services")]
[Authorize(Roles = "Admin,SystemOperator")]
public class ExternalServicesController : ControllerBase
{
    private readonly IExternalServiceFactory _serviceFactory;
    private readonly ExternalServiceHealthCheckService _healthCheckService;

    public ExternalServicesController(
        IExternalServiceFactory serviceFactory,
        ExternalServiceHealthCheckService healthCheckService)
    {
        _serviceFactory = serviceFactory;
        _healthCheckService = healthCheckService;
    }

    /// <summary>
    /// Get health status of all external service providers
    /// </summary>
    [HttpGet("health")]
    public async Task<IActionResult> GetHealthStatus(CancellationToken cancellationToken = default)
    {
        var healthStatuses = await _serviceFactory.CheckAllProvidersHealthAsync(cancellationToken);
        var summary = _healthCheckService.GetHealthSummary();

        return Ok(new
        {
            Summary = new
            {
                summary.TotalProviders,
                summary.HealthyProviders,
                summary.UnhealthyProviders,
                summary.OverallHealthPercentage,
                summary.LastChecked
            },
            Providers = healthStatuses.Select(h => new
            {
                h.Channel,
                h.ProviderType,
                h.IsHealthy,
                h.ErrorMessage,
                h.CheckedAt,
                ResponseTimeMs = h.ResponseTime?.TotalMilliseconds,
                h.Metadata
            })
        });
    }

    /// <summary>
    /// Get health status for a specific channel
    /// </summary>
    [HttpGet("health/{channel}")]
    public async Task<IActionResult> GetChannelHealth(
        NotificationChannel channel,
        CancellationToken cancellationToken = default)
    {
        var healthStatus = await _serviceFactory.CheckProviderHealthAsync(channel, cancellationToken);

        if (healthStatus == null)
        {
            return NotFound(new { Message = $"No provider found for channel {channel}" });
        }

        return Ok(new
        {
            healthStatus.Channel,
            healthStatus.ProviderType,
            healthStatus.IsHealthy,
            healthStatus.ErrorMessage,
            healthStatus.CheckedAt,
            ResponseTimeMs = healthStatus.ResponseTime?.TotalMilliseconds,
            healthStatus.Metadata
        });
    }

    /// <summary>
    /// Get configuration for all external service providers
    /// </summary>
    [HttpGet("configuration")]
    [Authorize(Roles = "Admin")]
    public IActionResult GetProviderConfigurations()
    {
        var channels = Enum.GetValues<NotificationChannel>();
        var configurations = new Dictionary<string, object>();

        foreach (var channel in channels)
        {
            try
            {
                var config = _serviceFactory.GetProviderConfiguration(channel);
                configurations[channel.ToString()] = new
                {
                    config.ProviderType,
                    config.IsEnabled,
                    // Don't expose sensitive configuration data
                    HasConfiguration = config.Configuration.Any()
                };
            }
            catch (Exception ex)
            {
                configurations[channel.ToString()] = new
                {
                    Error = ex.Message,
                    IsEnabled = false
                };
            }
        }

        return Ok(configurations);
    }

    /// <summary>
    /// Test SMS provider functionality
    /// </summary>
    [HttpPost("test/sms")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> TestSmsProvider(
        [FromBody] TestSmsRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var provider = _serviceFactory.GetSmsProvider();
            
            var smsRequest = new SmsRequest
            {
                PhoneNumber = request.PhoneNumber,
                Message = request.Message ?? "Test message from TLI Communication Service"
            };

            var result = await provider.SendSmsAsync(smsRequest, cancellationToken);

            return Ok(new
            {
                result.IsSuccess,
                result.MessageId,
                result.Status,
                result.SentAt,
                result.ErrorMessage,
                result.Metadata
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { Error = ex.Message });
        }
    }

    /// <summary>
    /// Test email provider functionality
    /// </summary>
    [HttpPost("test/email")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> TestEmailProvider(
        [FromBody] TestEmailRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var provider = _serviceFactory.GetEmailProvider();
            
            var emailRequest = new EmailRequest
            {
                ToEmail = request.ToEmail,
                Subject = request.Subject ?? "Test email from TLI Communication Service",
                PlainTextContent = request.Message ?? "This is a test email from TLI Communication Service.",
                HtmlContent = $"<p>{request.Message ?? "This is a test email from TLI Communication Service."}</p>"
            };

            var result = await provider.SendEmailAsync(emailRequest, cancellationToken);

            return Ok(new
            {
                result.IsSuccess,
                result.MessageId,
                result.Status,
                result.SentAt,
                result.ErrorMessage,
                result.Metadata
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { Error = ex.Message });
        }
    }

    /// <summary>
    /// Test push notification provider functionality
    /// </summary>
    [HttpPost("test/push")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> TestPushProvider(
        [FromBody] TestPushRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var provider = _serviceFactory.GetPushProvider();
            
            var pushRequest = new PushNotificationRequest
            {
                DeviceToken = request.DeviceToken,
                Title = request.Title ?? "Test Notification",
                Body = request.Message ?? "This is a test push notification from TLI Communication Service.",
                Platform = request.Platform,
                Data = new Dictionary<string, string>
                {
                    ["test"] = "true",
                    ["timestamp"] = DateTime.UtcNow.ToString("O")
                }
            };

            var result = await provider.SendPushNotificationAsync(pushRequest, cancellationToken);

            return Ok(new
            {
                result.IsSuccess,
                result.MessageId,
                result.Status,
                result.SentAt,
                result.ErrorMessage,
                result.Metadata
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { Error = ex.Message });
        }
    }

    /// <summary>
    /// Test WhatsApp provider functionality
    /// </summary>
    [HttpPost("test/whatsapp")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> TestWhatsAppProvider(
        [FromBody] TestWhatsAppRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var provider = _serviceFactory.GetWhatsAppProvider();
            
            var message = request.Message ?? "Test message from TLI Communication Service";
            var result = await provider.SendTextMessageAsync(request.PhoneNumber, message, cancellationToken);

            return Ok(new
            {
                result.IsSuccess,
                result.MessageId,
                result.Status,
                result.SentAt,
                result.ErrorMessage,
                result.Metadata
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { Error = ex.Message });
        }
    }

    /// <summary>
    /// Validate phone number using SMS provider
    /// </summary>
    [HttpPost("validate/phone")]
    public async Task<IActionResult> ValidatePhoneNumber(
        [FromBody] ValidatePhoneRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var provider = _serviceFactory.GetSmsProvider();
            var isValid = await provider.ValidatePhoneNumberAsync(request.PhoneNumber, cancellationToken);

            return Ok(new
            {
                PhoneNumber = request.PhoneNumber,
                IsValid = isValid,
                ValidatedAt = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { Error = ex.Message });
        }
    }

    /// <summary>
    /// Validate email address using email provider
    /// </summary>
    [HttpPost("validate/email")]
    public async Task<IActionResult> ValidateEmailAddress(
        [FromBody] ValidateEmailRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var provider = _serviceFactory.GetEmailProvider();
            var isValid = await provider.ValidateEmailAddressAsync(request.EmailAddress, cancellationToken);

            return Ok(new
            {
                EmailAddress = request.EmailAddress,
                IsValid = isValid,
                ValidatedAt = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { Error = ex.Message });
        }
    }

    /// <summary>
    /// Get provider metrics and statistics
    /// </summary>
    [HttpGet("metrics")]
    public IActionResult GetProviderMetrics()
    {
        var healthStatuses = _healthCheckService.GetCurrentHealthStatuses();
        var unhealthyProviders = _healthCheckService.GetUnhealthyProviders();

        var metrics = new
        {
            TotalProviders = healthStatuses.Count,
            HealthyProviders = healthStatuses.Values.Count(s => s.IsHealthy),
            UnhealthyProviders = unhealthyProviders.Count,
            AverageResponseTime = healthStatuses.Values
                .Where(s => s.ResponseTime.HasValue)
                .Average(s => s.ResponseTime!.Value.TotalMilliseconds),
            ProviderBreakdown = healthStatuses.Values
                .GroupBy(s => s.Channel)
                .ToDictionary(
                    g => g.Key.ToString(),
                    g => new
                    {
                        Total = g.Count(),
                        Healthy = g.Count(s => s.IsHealthy),
                        AverageResponseTime = g.Where(s => s.ResponseTime.HasValue)
                            .Average(s => s.ResponseTime!.Value.TotalMilliseconds)
                    }
                ),
            LastUpdated = healthStatuses.Values.Any() 
                ? healthStatuses.Values.Max(s => s.CheckedAt) 
                : DateTime.MinValue
        };

        return Ok(metrics);
    }
}

// Request models
public class TestSmsRequest
{
    public string PhoneNumber { get; set; } = string.Empty;
    public string? Message { get; set; }
}

public class TestEmailRequest
{
    public string ToEmail { get; set; } = string.Empty;
    public string? Subject { get; set; }
    public string? Message { get; set; }
}

public class TestPushRequest
{
    public string DeviceToken { get; set; } = string.Empty;
    public string? Title { get; set; }
    public string? Message { get; set; }
    public DevicePlatform Platform { get; set; } = DevicePlatform.Android;
}

public class TestWhatsAppRequest
{
    public string PhoneNumber { get; set; } = string.Empty;
    public string? Message { get; set; }
}

public class ValidatePhoneRequest
{
    public string PhoneNumber { get; set; } = string.Empty;
}

public class ValidateEmailRequest
{
    public string EmailAddress { get; set; } = string.Empty;
}
