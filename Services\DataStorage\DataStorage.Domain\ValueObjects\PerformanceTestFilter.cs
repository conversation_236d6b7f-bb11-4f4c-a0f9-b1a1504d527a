using Shared.Domain.ValueObjects;

namespace DataStorage.Domain.ValueObjects;

/// <summary>
/// Filter for performance test queries
/// </summary>
public class PerformanceTestFilter : ValueObject
{
    public string? TestName { get; init; }
    public string? TestType { get; init; }
    public DateTime? FromDate { get; init; }
    public DateTime? ToDate { get; init; }
    public string? Status { get; init; }
    public double? MinDuration { get; init; }
    public double? MaxDuration { get; init; }
    public int? Skip { get; init; }
    public int? Take { get; init; }

    public PerformanceTestFilter()
    {
    }

    public PerformanceTestFilter(
        string? testName = null,
        string? testType = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        string? status = null,
        double? minDuration = null,
        double? maxDuration = null,
        int? skip = null,
        int? take = null)
    {
        TestName = testName;
        TestType = testType;
        FromDate = fromDate;
        ToDate = toDate;
        Status = status;
        MinDuration = minDuration;
        MaxDuration = maxDuration;
        Skip = skip;
        Take = take;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return TestName ?? string.Empty;
        yield return TestType ?? string.Empty;
        yield return FromDate ?? DateTime.MinValue;
        yield return ToDate ?? DateTime.MinValue;
        yield return Status ?? string.Empty;
        yield return MinDuration ?? 0.0;
        yield return MaxDuration ?? 0.0;
        yield return Skip ?? 0;
        yield return Take ?? 0;
    }
}
