using Shared.Domain.Common;
using SubscriptionManagement.Domain.Events;
using SubscriptionManagement.Domain.Exceptions;
using SubscriptionManagement.Domain.ValueObjects;

namespace SubscriptionManagement.Domain.Entities
{
    public class GlobalTaxConfiguration : AggregateRoot
    {
        public TaxConfiguration TaxConfiguration { get; private set; }
        public bool IsActive { get; private set; }
        public int Priority { get; private set; }
        public string? Description { get; private set; }
        public Guid CreatedByUserId { get; private set; }
        public Guid? ModifiedByUserId { get; private set; }

        private GlobalTaxConfiguration()
        {
            TaxConfiguration = null!;
        }

        public GlobalTaxConfiguration(TaxConfiguration taxConfiguration, int priority, 
            Guid createdByUserId, string? description = null)
        {
            if (taxConfiguration == null)
                throw new SubscriptionDomainException("Tax configuration cannot be null");

            if (createdByUserId == Guid.Empty)
                throw new SubscriptionDomainException("Created by user ID cannot be empty");

            if (priority < 0)
                throw new SubscriptionDomainException("Priority cannot be negative");

            TaxConfiguration = taxConfiguration;
            IsActive = true;
            Priority = priority;
            Description = description;
            CreatedByUserId = createdByUserId;

            AddDomainEvent(new GlobalTaxConfigurationCreatedEvent(taxConfiguration.TaxType, 
                taxConfiguration.Rate, taxConfiguration.ApplicableRegions, taxConfiguration.IsIncluded));
        }

        public void UpdateTaxConfiguration(TaxConfiguration newTaxConfiguration, Guid modifiedByUserId, string? description = null)
        {
            if (newTaxConfiguration == null)
                throw new SubscriptionDomainException("Tax configuration cannot be null");

            if (modifiedByUserId == Guid.Empty)
                throw new SubscriptionDomainException("Modified by user ID cannot be empty");

            var oldRate = TaxConfiguration.Rate;
            TaxConfiguration = newTaxConfiguration;
            Description = description;
            ModifiedByUserId = modifiedByUserId;
            UpdatedAt = DateTime.UtcNow;

            AddDomainEvent(new GlobalTaxConfigurationChangedEvent(TaxConfiguration.TaxType, 
                oldRate, newTaxConfiguration.Rate, newTaxConfiguration.ApplicableRegions));
        }

        public void UpdatePriority(int newPriority, Guid modifiedByUserId)
        {
            if (newPriority < 0)
                throw new SubscriptionDomainException("Priority cannot be negative");

            if (modifiedByUserId == Guid.Empty)
                throw new SubscriptionDomainException("Modified by user ID cannot be empty");

            Priority = newPriority;
            ModifiedByUserId = modifiedByUserId;
            UpdatedAt = DateTime.UtcNow;
        }

        public void Activate(Guid modifiedByUserId)
        {
            if (IsActive)
                return;

            if (modifiedByUserId == Guid.Empty)
                throw new SubscriptionDomainException("Modified by user ID cannot be empty");

            IsActive = true;
            ModifiedByUserId = modifiedByUserId;
            UpdatedAt = DateTime.UtcNow;
        }

        public void Deactivate(Guid modifiedByUserId)
        {
            if (!IsActive)
                return;

            if (modifiedByUserId == Guid.Empty)
                throw new SubscriptionDomainException("Modified by user ID cannot be empty");

            IsActive = false;
            ModifiedByUserId = modifiedByUserId;
            UpdatedAt = DateTime.UtcNow;

            AddDomainEvent(new GlobalTaxConfigurationDeactivatedEvent(TaxConfiguration.TaxType, 
                TaxConfiguration.ApplicableRegions));
        }

        public void UpdateDescription(string? description, Guid modifiedByUserId)
        {
            if (modifiedByUserId == Guid.Empty)
                throw new SubscriptionDomainException("Modified by user ID cannot be empty");

            Description = description;
            ModifiedByUserId = modifiedByUserId;
            UpdatedAt = DateTime.UtcNow;
        }

        public bool IsEffectiveOn(DateTime date)
        {
            return IsActive && TaxConfiguration.IsActiveOn(date);
        }

        public bool IsApplicableToRegion(string region)
        {
            return TaxConfiguration.IsApplicableToRegion(region);
        }

        public Money CalculateTax(Money baseAmount, string region, DateTime? date = null)
        {
            var checkDate = date ?? DateTime.UtcNow;
            
            if (!IsEffectiveOn(checkDate) || !IsApplicableToRegion(region))
                return Money.Zero(baseAmount.Currency);

            return TaxConfiguration.CalculateTax(baseAmount, region);
        }
    }
}
