using CommunicationNotification.Domain.Enums;
using Shared.Domain.Common;

namespace CommunicationNotification.Domain.Entities;

public class ConversationParticipant : BaseEntity
{
    public Guid ConversationThreadId { get; private set; }
    public Guid UserId { get; private set; }
    public ConversationRole Role { get; private set; }
    public bool CanWrite { get; private set; }
    public DateTime JoinedAt { get; private set; }
    public DateTime? LeftAt { get; private set; }
    public DateTime? LastReadAt { get; private set; }
    public bool IsActive { get; private set; }

    // Navigation properties
    public ConversationThread ConversationThread { get; private set; } = null!;

    private ConversationParticipant() { }

    public ConversationParticipant(
        Guid conversationThreadId,
        Guid userId,
        ConversationRole role,
        bool canWrite = true)
    {
        if (conversationThreadId == Guid.Empty)
            throw new ArgumentException("Conversation thread ID cannot be empty", nameof(conversationThreadId));

        if (userId == Guid.Empty)
            throw new ArgumentException("User ID cannot be empty", nameof(userId));

        ConversationThreadId = conversationThreadId;
        UserId = userId;
        Role = role;
        CanWrite = canWrite;
        JoinedAt = DateTime.UtcNow;
        IsActive = true;
    }

    public static ConversationParticipant Create(
        Guid conversationThreadId,
        Guid userId,
        ConversationRole role,
        bool canWrite = true)
    {
        return new ConversationParticipant(conversationThreadId, userId, role, canWrite);
    }

    public void UpdatePermissions(bool canWrite)
    {
        if (!IsActive)
            throw new InvalidOperationException("Cannot update permissions for inactive participant");

        CanWrite = canWrite;
    }

    public void MarkAsRead()
    {
        if (!IsActive)
            throw new InvalidOperationException("Cannot mark as read for inactive participant");

        LastReadAt = DateTime.UtcNow;
    }

    public void Leave()
    {
        if (!IsActive)
            throw new InvalidOperationException("Participant has already left the conversation");

        IsActive = false;
        LeftAt = DateTime.UtcNow;
    }

    public void Rejoin()
    {
        if (IsActive)
            throw new InvalidOperationException("Participant is already active in the conversation");

        IsActive = true;
        LeftAt = null;
        JoinedAt = DateTime.UtcNow;
    }

    public bool HasUnreadMessages(DateTime? lastMessageTime)
    {
        if (!IsActive || lastMessageTime == null)
            return false;

        return LastReadAt == null || LastReadAt < lastMessageTime;
    }

    public TimeSpan GetParticipationDuration()
    {
        var endTime = LeftAt ?? DateTime.UtcNow;
        return endTime - JoinedAt;
    }
}
