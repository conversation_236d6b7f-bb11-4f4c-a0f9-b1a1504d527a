using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using MobileWorkflow.Domain.Entities;

namespace MobileWorkflow.Infrastructure.Data;

public class MilestoneDataSeeder
{
    private readonly MobileWorkflowDbContext _context;
    private readonly ILogger<MilestoneDataSeeder> _logger;

    public MilestoneDataSeeder(MobileWorkflowDbContext context, ILogger<MilestoneDataSeeder> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task SeedAsync()
    {
        try
        {
            _logger.LogInformation("Starting milestone data seeding...");

            // Check if data already exists
            if (await _context.MilestoneTemplates.AnyAsync())
            {
                _logger.LogInformation("Milestone data already exists, skipping seeding");
                return;
            }

            await SeedMilestoneTemplatesAsync();
            await SeedRoleTemplateMappingsAsync();

            await _context.SaveChangesAsync();
            _logger.LogInformation("Milestone data seeding completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred during milestone data seeding");
            throw;
        }
    }

    private async Task SeedMilestoneTemplatesAsync()
    {
        var templates = new List<MilestoneTemplate>();

        // 1. Standard Trip Template
        var standardTripTemplate = new MilestoneTemplate(
            "Standard Trip Milestones",
            "Standard milestone template for regular trip completion with driver payments",
            "Trip",
            "Logistics",
            "system");

        var tripStep1 = standardTripTemplate.AddStep("Trip Started", "Driver has started the trip and is en route", 1, true);
        tripStep1.SetTriggerCondition("status=started");
        tripStep1.AddPayoutRule(20.0m, "status=started", "Initial payment for trip start");
        tripStep1.UpdateConfiguration(new Dictionary<string, object>
        {
            { "require_gps_verification", true },
            { "auto_advance", true }
        });

        var tripStep2 = standardTripTemplate.AddStep("Goods Loaded", "Goods have been loaded and verified", 2, true);
        tripStep2.SetTriggerCondition("goods_loaded=true");
        tripStep2.AddPayoutRule(15.0m, "goods_loaded=true", "Payment for loading verification");

        var tripStep3 = standardTripTemplate.AddStep("Trip Completed", "Trip has been completed successfully", 3, true);
        tripStep3.SetTriggerCondition("status=completed");
        tripStep3.AddPayoutRule(50.0m, "status=completed", "Main payment for trip completion");

        var tripStep4 = standardTripTemplate.AddStep("POD Submitted", "Proof of delivery has been submitted", 4, true);
        tripStep4.SetTriggerCondition("pod_submitted=true");
        tripStep4.AddPayoutRule(15.0m, "pod_submitted=true", "Payment for POD submission");
        tripStep4.UpdateConfiguration(new Dictionary<string, object>
        {
            { "require_signature", true },
            { "require_photo", true },
            { "timeout_minutes", 30 }
        });

        standardTripTemplate.SetAsDefault();
        standardTripTemplate.UpdateConfiguration(new Dictionary<string, object>
        {
            { "auto_advance_enabled", true },
            { "payment_auto_trigger", true },
            { "sla_hours", 24 }
        });
        standardTripTemplate.AddMetadata("department", "logistics");
        standardTripTemplate.AddMetadata("version", "1.0");
        standardTripTemplate.AddMetadata("created_for", "standard_operations");

        templates.Add(standardTripTemplate);

        // 2. Express Delivery Template
        var expressDeliveryTemplate = new MilestoneTemplate(
            "Express Delivery Milestones",
            "Fast-track milestone template for express delivery services",
            "Delivery",
            "Express",
            "system");

        var deliveryStep1 = expressDeliveryTemplate.AddStep("Package Picked", "Package picked up from sender", 1, true);
        deliveryStep1.SetTriggerCondition("status=picked");
        deliveryStep1.AddPayoutRule(30.0m, "status=picked", "Payment for package pickup");
        deliveryStep1.UpdateConfiguration(new Dictionary<string, object>
        {
            { "require_photo", true },
            { "gps_verification", true },
            { "barcode_scan", true }
        });

        var deliveryStep2 = expressDeliveryTemplate.AddStep("In Transit", "Package is in transit to destination", 2, false);
        deliveryStep2.SetTriggerCondition("status=in_transit");
        deliveryStep2.AddPayoutRule(10.0m, "status=in_transit", "Transit milestone payment");

        var deliveryStep3 = expressDeliveryTemplate.AddStep("Package Delivered", "Package delivered to recipient", 3, true);
        deliveryStep3.SetTriggerCondition("status=delivered");
        deliveryStep3.AddPayoutRule(60.0m, "status=delivered", "Final payment for delivery");
        deliveryStep3.UpdateConfiguration(new Dictionary<string, object>
        {
            { "require_recipient_signature", true },
            { "require_delivery_photo", true },
            { "otp_verification", true }
        });

        expressDeliveryTemplate.UpdateConfiguration(new Dictionary<string, object>
        {
            { "sla_hours", 4 },
            { "priority", "high" },
            { "real_time_tracking", true }
        });
        expressDeliveryTemplate.AddMetadata("service_type", "express");
        expressDeliveryTemplate.AddMetadata("max_delivery_time", "4_hours");

        templates.Add(expressDeliveryTemplate);

        // 3. Long Distance Trip Template
        var longDistanceTemplate = new MilestoneTemplate(
            "Long Distance Trip Milestones",
            "Milestone template for long distance trips with multiple checkpoints",
            "Trip",
            "Transportation",
            "system");

        var longStep1 = longDistanceTemplate.AddStep("Trip Initiated", "Long distance trip has been initiated", 1, true);
        longStep1.AddPayoutRule(15.0m, "status=initiated", "Initial payment for trip start");

        var longStep2 = longDistanceTemplate.AddStep("First Checkpoint", "Reached first checkpoint", 2, true);
        longStep2.SetTriggerCondition("checkpoint_1=reached");
        longStep2.AddPayoutRule(20.0m, "checkpoint_1=reached", "Payment for first checkpoint");

        var longStep3 = longDistanceTemplate.AddStep("Midway Checkpoint", "Reached midway checkpoint", 3, false);
        longStep3.SetTriggerCondition("checkpoint_mid=reached");
        longStep3.AddPayoutRule(15.0m, "checkpoint_mid=reached", "Midway milestone payment");

        var longStep4 = longDistanceTemplate.AddStep("Final Checkpoint", "Reached final checkpoint", 4, true);
        longStep4.SetTriggerCondition("checkpoint_final=reached");
        longStep4.AddPayoutRule(25.0m, "checkpoint_final=reached", "Final checkpoint payment");

        var longStep5 = longDistanceTemplate.AddStep("Trip Completed", "Long distance trip completed", 5, true);
        longStep5.AddPayoutRule(25.0m, "status=completed", "Trip completion payment");

        longDistanceTemplate.AddMetadata("trip_type", "long_distance");
        longDistanceTemplate.AddMetadata("min_distance_km", "500");

        templates.Add(longDistanceTemplate);

        // 4. Pickup and Delivery Template
        var pickupDeliveryTemplate = new MilestoneTemplate(
            "Pickup & Delivery Milestones",
            "Template for pickup and delivery operations",
            "Order",
            "Pickup",
            "system");

        var pickupStep1 = pickupDeliveryTemplate.AddStep("Order Assigned", "Order has been assigned to driver", 1, true);
        pickupStep1.AddPayoutRule(5.0m, "status=assigned", "Assignment confirmation payment");

        var pickupStep2 = pickupDeliveryTemplate.AddStep("Pickup Completed", "Items picked up from source", 2, true);
        pickupStep2.SetTriggerCondition("pickup_completed=true");
        pickupStep2.AddPayoutRule(35.0m, "pickup_completed=true", "Pickup completion payment");

        var pickupStep3 = pickupDeliveryTemplate.AddStep("Delivery Completed", "Items delivered to destination", 3, true);
        pickupStep3.SetTriggerCondition("delivery_completed=true");
        pickupStep3.AddPayoutRule(60.0m, "delivery_completed=true", "Delivery completion payment");

        pickupDeliveryTemplate.AddMetadata("operation_type", "pickup_delivery");
        pickupDeliveryTemplate.AddMetadata("suitable_for", "local_operations");

        templates.Add(pickupDeliveryTemplate);

        // 5. Quality Compliance Template
        var qualityTemplate = new MilestoneTemplate(
            "Quality Compliance Milestones",
            "Template with quality checkpoints and compliance requirements",
            "Trip",
            "Quality",
            "system");

        var qualityStep1 = qualityTemplate.AddStep("Pre-Trip Inspection", "Vehicle and documentation inspection", 1, true);
        qualityStep1.AddPayoutRule(10.0m, "inspection_passed=true", "Pre-trip inspection bonus");

        var qualityStep2 = qualityTemplate.AddStep("Trip Execution", "Trip executed according to standards", 2, true);
        qualityStep2.AddPayoutRule(60.0m, "status=completed", "Standard trip payment");

        var qualityStep3 = qualityTemplate.AddStep("Quality Check", "Post-trip quality verification", 3, false);
        qualityStep3.SetTriggerCondition("quality_check=passed");
        qualityStep3.AddPayoutRule(15.0m, "quality_check=passed", "Quality compliance bonus");

        var qualityStep4 = qualityTemplate.AddStep("Customer Feedback", "Positive customer feedback received", 4, false);
        qualityStep4.SetTriggerCondition("feedback_rating>=4");
        qualityStep4.AddPayoutRule(15.0m, "feedback_rating>=4", "Customer satisfaction bonus");

        qualityTemplate.AddMetadata("focus", "quality_compliance");
        qualityTemplate.AddMetadata("bonus_eligible", "true");

        templates.Add(qualityTemplate);

        // 6. Emergency Response Template
        var emergencyTemplate = new MilestoneTemplate(
            "Emergency Response Milestones",
            "Template for emergency and urgent delivery scenarios",
            "Delivery",
            "Emergency",
            "system");

        var emergencyStep1 = emergencyTemplate.AddStep("Emergency Assigned", "Emergency delivery assigned", 1, true);
        emergencyStep1.AddPayoutRule(25.0m, "status=assigned", "Emergency assignment payment");

        var emergencyStep2 = emergencyTemplate.AddStep("Rapid Pickup", "Emergency pickup completed", 2, true);
        emergencyStep2.SetTriggerCondition("pickup_time<=15");
        emergencyStep2.AddPayoutRule(35.0m, "pickup_time<=15", "Rapid pickup bonus");

        var emergencyStep3 = emergencyTemplate.AddStep("Priority Delivery", "Emergency delivery completed", 3, true);
        emergencyStep3.SetTriggerCondition("delivery_time<=60");
        emergencyStep3.AddPayoutRule(40.0m, "delivery_time<=60", "Priority delivery payment");

        emergencyTemplate.UpdateConfiguration(new Dictionary<string, object>
        {
            { "priority", "emergency" },
            { "sla_minutes", 60 },
            { "auto_escalate", true }
        });
        emergencyTemplate.AddMetadata("urgency", "critical");
        emergencyTemplate.AddMetadata("max_response_time", "15_minutes");

        templates.Add(emergencyTemplate);

        _context.MilestoneTemplates.AddRange(templates);
        _logger.LogInformation($"Added {templates.Count} milestone templates");
    }

    private async Task SeedRoleTemplateMappingsAsync()
    {
        var mappings = new List<RoleTemplateMappings>();

        // Get templates for mapping
        var standardTripTemplate = await _context.MilestoneTemplates
            .FirstAsync(t => t.Name == "Standard Trip Milestones");
        var expressDeliveryTemplate = await _context.MilestoneTemplates
            .FirstAsync(t => t.Name == "Express Delivery Milestones");
        var longDistanceTemplate = await _context.MilestoneTemplates
            .FirstAsync(t => t.Name == "Long Distance Trip Milestones");
        var pickupDeliveryTemplate = await _context.MilestoneTemplates
            .FirstAsync(t => t.Name == "Pickup & Delivery Milestones");
        var qualityTemplate = await _context.MilestoneTemplates
            .FirstAsync(t => t.Name == "Quality Compliance Milestones");
        var emergencyTemplate = await _context.MilestoneTemplates
            .FirstAsync(t => t.Name == "Emergency Response Milestones");

        // Driver role mappings
        mappings.Add(new RoleTemplateMappings("Driver", standardTripTemplate.Id, "system", true, 100));
        mappings.Add(new RoleTemplateMappings("Driver", longDistanceTemplate.Id, "system", false, 80)
        {
            Conditions = "{\"trip_distance_km\": \">500\"}"
        });
        mappings.Add(new RoleTemplateMappings("Driver", qualityTemplate.Id, "system", false, 90)
        {
            Conditions = "{\"driver_rating\": \">4.5\", \"experience_years\": \">2\"}"
        });

        // Senior Driver role mappings
        mappings.Add(new RoleTemplateMappings("SeniorDriver", qualityTemplate.Id, "system", true, 100));
        mappings.Add(new RoleTemplateMappings("SeniorDriver", longDistanceTemplate.Id, "system", false, 90));
        mappings.Add(new RoleTemplateMappings("SeniorDriver", emergencyTemplate.Id, "system", false, 95)
        {
            Conditions = "{\"emergency_certified\": \"true\", \"experience_years\": \">5\"}"
        });

        // Delivery Agent role mappings
        mappings.Add(new RoleTemplateMappings("DeliveryAgent", expressDeliveryTemplate.Id, "system", true, 100));
        mappings.Add(new RoleTemplateMappings("DeliveryAgent", pickupDeliveryTemplate.Id, "system", false, 80));
        mappings.Add(new RoleTemplateMappings("DeliveryAgent", emergencyTemplate.Id, "system", false, 70)
        {
            Conditions = "{\"emergency_trained\": \"true\"}"
        });

        // Logistics Coordinator role mappings
        mappings.Add(new RoleTemplateMappings("LogisticsCoordinator", standardTripTemplate.Id, "system", true, 100));
        mappings.Add(new RoleTemplateMappings("LogisticsCoordinator", longDistanceTemplate.Id, "system", false, 90));

        // Express Courier role mappings
        mappings.Add(new RoleTemplateMappings("ExpressCourier", expressDeliveryTemplate.Id, "system", true, 100));
        mappings.Add(new RoleTemplateMappings("ExpressCourier", emergencyTemplate.Id, "system", false, 95));

        // Emergency Responder role mappings
        mappings.Add(new RoleTemplateMappings("EmergencyResponder", emergencyTemplate.Id, "system", true, 100));
        mappings.Add(new RoleTemplateMappings("EmergencyResponder", expressDeliveryTemplate.Id, "system", false, 80));

        // Quality Inspector role mappings
        mappings.Add(new RoleTemplateMappings("QualityInspector", qualityTemplate.Id, "system", true, 100));
        mappings.Add(new RoleTemplateMappings("QualityInspector", standardTripTemplate.Id, "system", false, 70));

        // Fleet Manager role mappings
        mappings.Add(new RoleTemplateMappings("FleetManager", standardTripTemplate.Id, "system", true, 100));
        mappings.Add(new RoleTemplateMappings("FleetManager", longDistanceTemplate.Id, "system", false, 90));
        mappings.Add(new RoleTemplateMappings("FleetManager", qualityTemplate.Id, "system", false, 85));

        // Add configuration and metadata to mappings
        foreach (var mapping in mappings)
        {
            mapping.UpdateConfiguration(new Dictionary<string, object>
            {
                { "auto_assign", true },
                { "notification_enabled", true },
                { "escalation_enabled", false }
            }, "system");

            mapping.AddMetadata("created_by_seeder", "true");
            mapping.AddMetadata("environment", "production");
        }

        _context.RoleTemplateMappings.AddRange(mappings);
        _logger.LogInformation($"Added {mappings.Count} role template mappings");
    }

    public async Task SeedDevelopmentDataAsync()
    {
        _logger.LogInformation("Seeding additional development data...");

        // Add test templates for development
        if (!await _context.MilestoneTemplates.AnyAsync(t => t.Name.Contains("Test")))
        {
            var testTemplate = new MilestoneTemplate(
                "Test Development Template",
                "Template for development and testing purposes",
                "Test",
                "Development",
                "developer");

            var testStep = testTemplate.AddStep("Test Step", "A test milestone step", 1, true);
            testStep.AddPayoutRule(100.0m, "status=test", "Test payment rule");

            testTemplate.AddMetadata("environment", "development");
            testTemplate.AddMetadata("purpose", "testing");

            _context.MilestoneTemplates.Add(testTemplate);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Added development test data");
        }
    }
}
