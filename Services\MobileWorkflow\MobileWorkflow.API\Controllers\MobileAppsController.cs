using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MobileWorkflow.Application.Commands.MobileApp;
using MobileWorkflow.Application.DTOs;
using MobileWorkflow.Application.Queries.MobileApp;

namespace MobileWorkflow.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class MobileAppsController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<MobileAppsController> _logger;

    public MobileAppsController(IMediator mediator, ILogger<MobileAppsController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get all mobile apps with optional filtering
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<MobileAppDto>>> GetMobileApps(
        [FromQuery] string? platform = null,
        [FromQuery] bool? isActive = null,
        [FromQuery] bool? supportsOffline = null)
    {
        var query = new GetMobileAppsQuery
        {
            Platform = platform,
            IsActive = isActive,
            SupportsOffline = supportsOffline
        };

        var result = await _mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Get mobile app by ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<MobileAppDto>> GetMobileApp(Guid id)
    {
        var query = new GetMobileAppByIdQuery(id);
        var result = await _mediator.Send(query);

        if (result == null)
            return NotFound($"Mobile app with ID {id} not found");

        return Ok(result);
    }

    /// <summary>
    /// Get mobile app by package ID
    /// </summary>
    [HttpGet("package/{packageId}")]
    public async Task<ActionResult<MobileAppDto>> GetMobileAppByPackageId(string packageId)
    {
        var query = new GetMobileAppByPackageIdQuery(packageId);
        var result = await _mediator.Send(query);

        if (result == null)
            return NotFound($"Mobile app with package ID {packageId} not found");

        return Ok(result);
    }

    /// <summary>
    /// Create a new mobile app
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Admin,Developer")]
    public async Task<ActionResult<MobileAppDto>> CreateMobileApp([FromBody] CreateMobileAppCommand command)
    {
        try
        {
            var result = await _mediator.Send(command);
            return CreatedAtAction(nameof(GetMobileApp), new { id = result.Id }, result);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Update mobile app configuration
    /// </summary>
    [HttpPut("{id}")]
    [Authorize(Roles = "Admin,Developer")]
    public async Task<ActionResult> UpdateMobileApp(Guid id, [FromBody] UpdateMobileAppDto updateDto)
    {
        // Implementation would go here - create UpdateMobileAppCommand
        return NoContent();
    }

    /// <summary>
    /// Delete mobile app
    /// </summary>
    [HttpDelete("{id}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> DeleteMobileApp(Guid id)
    {
        // Implementation would go here - create DeleteMobileAppCommand
        return NoContent();
    }

    /// <summary>
    /// Get mobile app manifest for PWA
    /// </summary>
    [HttpGet("{id}/manifest")]
    [AllowAnonymous]
    public async Task<ActionResult> GetManifest(Guid id, [FromQuery] string platform = "web")
    {
        // This would integrate with the PWA service
        var manifest = new
        {
            name = "TLI Logistics Mobile",
            short_name = "TLI Mobile",
            description = "TLI Logistics mobile application",
            start_url = "/",
            display = "standalone",
            background_color = "#ffffff",
            theme_color = "#2196F3",
            icons = new[]
            {
                new { src = "/icons/icon-192x192.png", sizes = "192x192", type = "image/png" },
                new { src = "/icons/icon-512x512.png", sizes = "512x512", type = "image/png" }
            }
        };

        return Ok(manifest);
    }

    /// <summary>
    /// Get service worker configuration
    /// </summary>
    [HttpGet("{id}/service-worker-config")]
    [Authorize]
    public async Task<ActionResult> GetServiceWorkerConfig(Guid id)
    {
        // This would integrate with the PWA service
        var config = new
        {
            cacheName = "tli-mobile-v1",
            cacheStrategy = "CacheFirst",
            offlinePageUrl = "/offline.html",
            syncTags = new[] { "trip-updates", "pod-uploads", "document-uploads" }
        };

        return Ok(config);
    }
}

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class MobileSessionsController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<MobileSessionsController> _logger;

    public MobileSessionsController(IMediator mediator, ILogger<MobileSessionsController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Start a new mobile session
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<MobileSessionDto>> StartSession([FromBody] CreateMobileSessionDto sessionDto)
    {
        // Implementation would go here - create StartMobileSessionCommand
        return Ok();
    }

    /// <summary>
    /// Get active session for current user
    /// </summary>
    [HttpGet("active")]
    public async Task<ActionResult<MobileSessionDto>> GetActiveSession()
    {
        // Implementation would go here - create GetActiveSessionQuery
        return Ok();
    }

    /// <summary>
    /// Update session data
    /// </summary>
    [HttpPut("{sessionId}/data")]
    public async Task<ActionResult> UpdateSessionData(Guid sessionId, [FromBody] Dictionary<string, object> data)
    {
        // Implementation would go here - create UpdateSessionDataCommand
        return NoContent();
    }

    /// <summary>
    /// End mobile session
    /// </summary>
    [HttpPost("{sessionId}/end")]
    public async Task<ActionResult> EndSession(Guid sessionId)
    {
        // Implementation would go here - create EndMobileSessionCommand
        return NoContent();
    }

    /// <summary>
    /// Sync offline data
    /// </summary>
    [HttpPost("{sessionId}/sync")]
    public async Task<ActionResult> SyncOfflineData(Guid sessionId, [FromBody] List<CreateOfflineDataDto> offlineData)
    {
        // Implementation would go here - integrate with CrossPlatformSyncService
        return Ok(new { syncedItems = offlineData.Count, status = "success" });
    }
}
