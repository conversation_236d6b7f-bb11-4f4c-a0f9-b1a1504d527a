using Microsoft.EntityFrameworkCore;
using MobileWorkflow.Domain.Entities;
using MobileWorkflow.Domain.Enums;
using MobileWorkflow.Domain.Repositories;
using MobileWorkflow.Infrastructure.Data;

namespace MobileWorkflow.Infrastructure.Repositories;

public class SyncOperationRepository : ISyncOperationRepository
{
    private readonly MobileWorkflowDbContext _context;

    public SyncOperationRepository(MobileWorkflowDbContext context)
    {
        _context = context;
    }

    public void Add(SyncOperation syncOperation)
    {
        _context.SyncOperations.Add(syncOperation);
    }

    public async Task<SyncOperation?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.SyncOperations
            .Include(so => so.SyncItems)
            .Include(so => so.Conflicts)
            .FirstOrDefaultAsync(so => so.Id == id, cancellationToken);
    }

    public async Task<SyncOperation?> GetActiveSyncOperationAsync(Guid userId, Guid deviceId, CancellationToken cancellationToken = default)
    {
        return await _context.SyncOperations
            .Where(so => so.UserId == userId && 
                        so.DeviceId == deviceId && 
                        so.Status == SyncStatus.InProgress)
            .OrderByDescending(so => so.StartTime)
            .FirstOrDefaultAsync(cancellationToken);
    }

    public async Task<List<SyncOperation>> GetByUserIdAsync(Guid userId, DateTime? fromDate = null, CancellationToken cancellationToken = default)
    {
        var query = _context.SyncOperations
            .Where(so => so.UserId == userId);

        if (fromDate.HasValue)
        {
            query = query.Where(so => so.StartTime >= fromDate.Value);
        }

        return await query
            .OrderByDescending(so => so.StartTime)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<SyncOperation>> GetByStatusAsync(SyncStatus status, CancellationToken cancellationToken = default)
    {
        return await _context.SyncOperations
            .Where(so => so.Status == status)
            .OrderByDescending(so => so.StartTime)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<SyncOperation>> GetPendingRetriesAsync(CancellationToken cancellationToken = default)
    {
        return await _context.SyncOperations
            .Where(so => so.Status == SyncStatus.Failed && 
                        so.NextRetryTime.HasValue && 
                        so.NextRetryTime <= DateTime.UtcNow)
            .OrderBy(so => so.NextRetryTime)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<SyncOperation>> GetByDeviceIdAsync(Guid deviceId, CancellationToken cancellationToken = default)
    {
        return await _context.SyncOperations
            .Where(so => so.DeviceId == deviceId)
            .OrderByDescending(so => so.StartTime)
            .ToListAsync(cancellationToken);
    }

    public async Task<int> GetActiveOperationsCountAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await _context.SyncOperations
            .CountAsync(so => so.UserId == userId && so.Status == SyncStatus.InProgress, cancellationToken);
    }

    public async Task<SyncOperation?> GetLatestByUserIdAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await _context.SyncOperations
            .Where(so => so.UserId == userId)
            .OrderByDescending(so => so.StartTime)
            .FirstOrDefaultAsync(cancellationToken);
    }

    public void Update(SyncOperation syncOperation)
    {
        _context.SyncOperations.Update(syncOperation);
    }

    public void Remove(SyncOperation syncOperation)
    {
        _context.SyncOperations.Remove(syncOperation);
    }

    public async Task SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        await _context.SaveChangesAsync(cancellationToken);
    }
}

public class SyncItemRepository : ISyncItemRepository
{
    private readonly MobileWorkflowDbContext _context;

    public SyncItemRepository(MobileWorkflowDbContext context)
    {
        _context = context;
    }

    public void Add(SyncItem syncItem)
    {
        _context.SyncItems.Add(syncItem);
    }

    public async Task<SyncItem?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.SyncItems
            .Include(si => si.SyncOperation)
            .FirstOrDefaultAsync(si => si.Id == id, cancellationToken);
    }

    public async Task<List<SyncItem>> GetBySyncOperationIdAsync(Guid syncOperationId, CancellationToken cancellationToken = default)
    {
        return await _context.SyncItems
            .Where(si => si.SyncOperationId == syncOperationId)
            .OrderBy(si => si.Priority)
            .ThenBy(si => si.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<SyncItem>> GetPendingByUserIdAsync(Guid userId, int priority = 0, CancellationToken cancellationToken = default)
    {
        var query = _context.SyncItems
            .Include(si => si.SyncOperation)
            .Where(si => si.SyncOperation.UserId == userId && 
                        si.Status == SyncStatus.Pending);

        if (priority > 0)
        {
            query = query.Where(si => si.Priority == priority);
        }

        return await query
            .OrderBy(si => si.Priority)
            .ThenBy(si => si.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<SyncItem>> GetByStatusAsync(SyncStatus status, CancellationToken cancellationToken = default)
    {
        return await _context.SyncItems
            .Where(si => si.Status == status)
            .OrderBy(si => si.Priority)
            .ThenBy(si => si.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<SyncItem>> GetConflictItemsAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await _context.SyncItems
            .Include(si => si.SyncOperation)
            .Where(si => si.SyncOperation.UserId == userId && si.Status == SyncStatus.Conflict)
            .OrderByDescending(si => si.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<SyncItem>> GetFailedItemsAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await _context.SyncItems
            .Include(si => si.SyncOperation)
            .Where(si => si.SyncOperation.UserId == userId && si.Status == SyncStatus.Failed)
            .OrderByDescending(si => si.ProcessedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<SyncItem>> GetReadyForRetryAsync(CancellationToken cancellationToken = default)
    {
        return await _context.SyncItems
            .Where(si => si.Status == SyncStatus.Failed && 
                        si.NextRetryTime.HasValue && 
                        si.NextRetryTime <= DateTime.UtcNow)
            .OrderBy(si => si.Priority)
            .ThenBy(si => si.NextRetryTime)
            .ToListAsync(cancellationToken);
    }

    public async Task<SyncItem?> GetByEntityAsync(string entityType, string entityId, CancellationToken cancellationToken = default)
    {
        return await _context.SyncItems
            .Where(si => si.EntityType == entityType && si.EntityId == entityId)
            .OrderByDescending(si => si.CreatedAt)
            .FirstOrDefaultAsync(cancellationToken);
    }

    public async Task<List<SyncItem>> GetByEntityTypeAsync(string entityType, CancellationToken cancellationToken = default)
    {
        return await _context.SyncItems
            .Where(si => si.EntityType == entityType)
            .OrderByDescending(si => si.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<int> GetPendingCountByUserIdAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await _context.SyncItems
            .Include(si => si.SyncOperation)
            .CountAsync(si => si.SyncOperation.UserId == userId && si.Status == SyncStatus.Pending, cancellationToken);
    }

    public async Task<long> GetPendingDataSizeByUserIdAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await _context.SyncItems
            .Include(si => si.SyncOperation)
            .Where(si => si.SyncOperation.UserId == userId && si.Status == SyncStatus.Pending)
            .SumAsync(si => si.DataSizeBytes, cancellationToken);
    }

    public void Update(SyncItem syncItem)
    {
        _context.SyncItems.Update(syncItem);
    }

    public void Remove(SyncItem syncItem)
    {
        _context.SyncItems.Remove(syncItem);
    }

    public async Task SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        await _context.SaveChangesAsync(cancellationToken);
    }
}

public class ConflictResolutionRepository : IConflictResolutionRepository
{
    private readonly MobileWorkflowDbContext _context;

    public ConflictResolutionRepository(MobileWorkflowDbContext context)
    {
        _context = context;
    }

    public void Add(ConflictResolution conflictResolution)
    {
        _context.ConflictResolutions.Add(conflictResolution);
    }

    public async Task<ConflictResolution?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.ConflictResolutions
            .Include(cr => cr.SyncOperation)
            .FirstOrDefaultAsync(cr => cr.Id == id, cancellationToken);
    }

    public async Task<List<ConflictResolution>> GetBySyncOperationIdAsync(Guid syncOperationId, CancellationToken cancellationToken = default)
    {
        return await _context.ConflictResolutions
            .Where(cr => cr.SyncOperationId == syncOperationId)
            .OrderByDescending(cr => cr.DetectedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<ConflictResolution>> GetPendingConflictsByUserIdAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await _context.ConflictResolutions
            .Include(cr => cr.SyncOperation)
            .Where(cr => cr.SyncOperation.UserId == userId && cr.Status == "Pending")
            .OrderByDescending(cr => cr.DetectedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<ConflictResolution>> GetByEntityAsync(string entityType, string entityId, CancellationToken cancellationToken = default)
    {
        return await _context.ConflictResolutions
            .Where(cr => cr.EntityType == entityType && cr.EntityId == entityId)
            .OrderByDescending(cr => cr.DetectedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<ConflictResolution>> GetByStatusAsync(string status, CancellationToken cancellationToken = default)
    {
        return await _context.ConflictResolutions
            .Where(cr => cr.Status == status)
            .OrderByDescending(cr => cr.DetectedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<ConflictResolution>> GetAutoResolvableConflictsAsync(CancellationToken cancellationToken = default)
    {
        return await _context.ConflictResolutions
            .Where(cr => cr.Status == "Pending")
            .ToListAsync(cancellationToken);
    }

    public async Task<List<ConflictResolution>> GetByConflictTypeAsync(string conflictType, CancellationToken cancellationToken = default)
    {
        return await _context.ConflictResolutions
            .Where(cr => cr.ConflictType == conflictType)
            .OrderByDescending(cr => cr.DetectedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<int> GetPendingCountByUserIdAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await _context.ConflictResolutions
            .Include(cr => cr.SyncOperation)
            .CountAsync(cr => cr.SyncOperation.UserId == userId && cr.Status == "Pending", cancellationToken);
    }

    public async Task<ConflictResolution?> GetLatestByEntityAsync(string entityType, string entityId, CancellationToken cancellationToken = default)
    {
        return await _context.ConflictResolutions
            .Where(cr => cr.EntityType == entityType && cr.EntityId == entityId)
            .OrderByDescending(cr => cr.DetectedAt)
            .FirstOrDefaultAsync(cancellationToken);
    }

    public void Update(ConflictResolution conflictResolution)
    {
        _context.ConflictResolutions.Update(conflictResolution);
    }

    public void Remove(ConflictResolution conflictResolution)
    {
        _context.ConflictResolutions.Remove(conflictResolution);
    }

    public async Task SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        await _context.SaveChangesAsync(cancellationToken);
    }
}
