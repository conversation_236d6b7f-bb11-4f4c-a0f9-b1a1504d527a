namespace MobileWorkflow.Application.DTOs;

public class ScanSessionDto
{
    public Guid Id { get; set; }
    public Guid UserId { get; set; }
    public string SessionName { get; set; } = string.Empty;
    public string SessionType { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string? DeviceId { get; set; }
    public Dictionary<string, object> SessionConfiguration { get; set; } = new();
    public Dictionary<string, object> ValidationRules { get; set; } = new();
    public int TotalScans { get; set; }
    public int SuccessfulScans { get; set; }
    public int FailedScans { get; set; }
    public int DuplicateScans { get; set; }
    public Dictionary<string, object> SessionData { get; set; } = new();
    public bool IsActive { get; set; }
    public bool IsCompleted { get; set; }
    public TimeSpan? Duration { get; set; }
    public double SuccessRate { get; set; }
    public double FailureRate { get; set; }
    public double DuplicateRate { get; set; }
}

public class ScanResultDto
{
    public Guid Id { get; set; }
    public Guid SessionId { get; set; }
    public Guid UserId { get; set; }
    public string BarcodeData { get; set; } = string.Empty;
    public string BarcodeFormat { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime ScannedAt { get; set; }
    public double? Latitude { get; set; }
    public double? Longitude { get; set; }
    public string? DeviceId { get; set; }
    public Dictionary<string, object> ScanContext { get; set; } = new();
    public Dictionary<string, object> ValidationResult { get; set; } = new();
    public Dictionary<string, object> ProcessingResult { get; set; } = new();
    public string? ErrorMessage { get; set; }
    public string? ErrorCode { get; set; }
    public bool IsProcessed { get; set; }
    public DateTime? ProcessedAt { get; set; }
    public bool IsSuccessful { get; set; }
    public bool IsFailed { get; set; }
    public bool IsDuplicate { get; set; }
    public bool IsInvalid { get; set; }
}

public class BarcodeTemplateDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string BarcodeFormat { get; set; } = string.Empty;
    public string Pattern { get; set; } = string.Empty;
    public Dictionary<string, object> ValidationRules { get; set; } = new();
    public Dictionary<string, object> ProcessingRules { get; set; } = new();
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public string? UpdatedBy { get; set; }
}

public class ScanAnalyticsDto
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public int TotalScans { get; set; }
    public int SuccessfulScans { get; set; }
    public int FailedScans { get; set; }
    public int DuplicateScans { get; set; }
    public int InvalidScans { get; set; }
    public double SuccessRate { get; set; }
    public int TotalSessions { get; set; }
    public int CompletedSessions { get; set; }
    public int ActiveSessions { get; set; }
    public Dictionary<string, int> ScansByFormat { get; set; } = new();
    public Dictionary<int, int> ScansByHour { get; set; } = new();
    public Dictionary<DateTime, int> ScansByDay { get; set; } = new();
    public double AverageScansPerSession { get; set; }
    public Dictionary<Guid, int> MostActiveUsers { get; set; } = new();
}

// Request DTOs
public class CreateScanSessionRequest
{
    public Guid UserId { get; set; }
    public string SessionName { get; set; } = string.Empty;
    public string SessionType { get; set; } = string.Empty; // Single, Batch, Inventory, Verification
    public Dictionary<string, object> SessionConfiguration { get; set; } = new();
    public Dictionary<string, object>? ValidationRules { get; set; }
    public string? DeviceId { get; set; }
}

public class ProcessScanRequest
{
    public Guid SessionId { get; set; }
    public Guid UserId { get; set; }
    public string BarcodeData { get; set; } = string.Empty;
    public string BarcodeFormat { get; set; } = string.Empty;
    public Dictionary<string, object> ScanContext { get; set; } = new();
    public double? Latitude { get; set; }
    public double? Longitude { get; set; }
    public string? DeviceId { get; set; }
}

public class ProcessBatchScanRequest
{
    public Guid SessionId { get; set; }
    public Guid UserId { get; set; }
    public List<BatchScanItem> ScanItems { get; set; } = new();
    public string? DeviceId { get; set; }
}

public class BatchScanItem
{
    public string BarcodeData { get; set; } = string.Empty;
    public string BarcodeFormat { get; set; } = string.Empty;
    public Dictionary<string, object> ScanContext { get; set; } = new();
    public double? Latitude { get; set; }
    public double? Longitude { get; set; }
}

public class CreateBarcodeTemplateRequest
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string BarcodeFormat { get; set; } = string.Empty;
    public string Pattern { get; set; } = string.Empty;
    public Dictionary<string, object>? ValidationRules { get; set; }
    public Dictionary<string, object>? ProcessingRules { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

public class UpdateBarcodeTemplateRequest
{
    public Dictionary<string, object>? ValidationRules { get; set; }
    public Dictionary<string, object>? ProcessingRules { get; set; }
    public bool? IsActive { get; set; }
    public string UpdatedBy { get; set; } = string.Empty;
}

public class ValidateBarcodeRequest
{
    public string BarcodeData { get; set; } = string.Empty;
    public string BarcodeFormat { get; set; } = string.Empty;
    public Dictionary<string, object>? ValidationRules { get; set; }
}

public class BarcodeFormatDto
{
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<string> SupportedPlatforms { get; set; } = new();
    public Dictionary<string, object> FormatSpecifications { get; set; } = new();
    public bool SupportsChecksum { get; set; }
    public int MinLength { get; set; }
    public int MaxLength { get; set; }
    public string CharacterSet { get; set; } = string.Empty;
}

public class ScanConfigurationDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<string> AllowedFormats { get; set; } = new();
    public Dictionary<string, object> ScanSettings { get; set; } = new();
    public Dictionary<string, object> ValidationSettings { get; set; } = new();
    public Dictionary<string, object> ProcessingSettings { get; set; } = new();
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

public class ScanHistoryDto
{
    public Guid UserId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public List<ScanSessionDto> Sessions { get; set; } = new();
    public List<ScanResultDto> RecentScans { get; set; } = new();
    public Dictionary<string, int> FormatBreakdown { get; set; } = new();
    public Dictionary<string, int> StatusBreakdown { get; set; } = new();
    public int TotalScans { get; set; }
    public double AverageSessionDuration { get; set; }
}

public class BarcodeGenerationDto
{
    public string BarcodeFormat { get; set; } = string.Empty;
    public string Data { get; set; } = string.Empty;
    public Dictionary<string, object> GenerationOptions { get; set; } = new();
    public string OutputFormat { get; set; } = "PNG"; // PNG, SVG, PDF
    public int Width { get; set; } = 200;
    public int Height { get; set; } = 100;
    public bool IncludeText { get; set; } = true;
}

public class BarcodeImageDto
{
    public string BarcodeFormat { get; set; } = string.Empty;
    public string Data { get; set; } = string.Empty;
    public string ImageFormat { get; set; } = string.Empty;
    public byte[] ImageData { get; set; } = Array.Empty<byte>();
    public string Base64Image { get; set; } = string.Empty;
    public int Width { get; set; }
    public int Height { get; set; }
    public DateTime GeneratedAt { get; set; }
}

public class ScanPerformanceDto
{
    public string BarcodeFormat { get; set; } = string.Empty;
    public string DeviceType { get; set; } = string.Empty;
    public double AverageScanTime { get; set; }
    public double SuccessRate { get; set; }
    public int TotalScans { get; set; }
    public int SuccessfulScans { get; set; }
    public int FailedScans { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public Dictionary<string, object> PerformanceMetrics { get; set; } = new();
}

public class ScanQualityDto
{
    public Guid ScanResultId { get; set; }
    public double QualityScore { get; set; }
    public Dictionary<string, double> QualityMetrics { get; set; } = new();
    public List<string> QualityIssues { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
    public DateTime AssessedAt { get; set; }
}

public class BarcodeInventoryDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<BarcodeInventoryItem> Items { get; set; } = new();
    public string Status { get; set; } = string.Empty; // InProgress, Completed, Cancelled
    public DateTime StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public Guid CreatedBy { get; set; }
    public Dictionary<string, object> InventorySettings { get; set; } = new();
}

public class BarcodeInventoryItem
{
    public Guid Id { get; set; }
    public string BarcodeData { get; set; } = string.Empty;
    public string BarcodeFormat { get; set; } = string.Empty;
    public string ItemName { get; set; } = string.Empty;
    public string ItemDescription { get; set; } = string.Empty;
    public int ExpectedQuantity { get; set; }
    public int ScannedQuantity { get; set; }
    public string Status { get; set; } = string.Empty; // NotScanned, Scanned, Discrepancy
    public DateTime? LastScannedAt { get; set; }
    public Dictionary<string, object> ItemData { get; set; } = new();
}

public class ScanWorkflowDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<ScanWorkflowStep> Steps { get; set; } = new();
    public string Status { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public bool IsActive { get; set; }
}

public class ScanWorkflowStep
{
    public int Order { get; set; }
    public string Name { get; set; } = string.Empty;
    public string StepType { get; set; } = string.Empty; // Scan, Validate, Process, Notify
    public Dictionary<string, object> StepConfiguration { get; set; } = new();
    public Dictionary<string, object> ValidationRules { get; set; } = new();
    public bool IsRequired { get; set; }
    public string? NextStepCondition { get; set; }
}
