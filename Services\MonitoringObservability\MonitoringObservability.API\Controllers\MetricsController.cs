using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using MediatR;
using MonitoringObservability.Application.Commands;
using MonitoringObservability.Application.Queries;
using MonitoringObservability.Application.DTOs;
using MonitoringObservability.Domain.Enums;

namespace MonitoringObservability.API.Controllers;

[ApiController]
[Route("api/v{version:apiVersion}/[controller]")]
[ApiVersion("1.0")]
[Authorize]
public class MetricsController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<MetricsController> _logger;

    public MetricsController(IMediator mediator, ILogger<MetricsController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get all metrics with optional filtering
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<MetricDto>>> GetMetrics([FromQuery] GetMetricsRequest request)
    {
        try
        {
            var query = new GetMetricsQuery
            {
                ServiceName = request.ServiceName,
                Type = request.Type,
                Category = request.Category,
                IsActive = request.IsActive,
                AlertingEnabled = request.AlertingEnabled
            };

            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get metrics");
            return StatusCode(500, "Failed to retrieve metrics");
        }
    }

    /// <summary>
    /// Get metric by ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<MetricDto>> GetMetric(Guid id)
    {
        try
        {
            var query = new GetMetricByIdQuery { MetricId = id };
            var result = await _mediator.Send(query);
            
            if (result == null)
                return NotFound();

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get metric {MetricId}", id);
            return StatusCode(500, "Failed to retrieve metric");
        }
    }

    /// <summary>
    /// Get metric by name and service
    /// </summary>
    [HttpGet("by-name")]
    public async Task<ActionResult<MetricDto>> GetMetricByName([FromQuery] string metricName, [FromQuery] string serviceName)
    {
        try
        {
            var query = new GetMetricByNameQuery { MetricName = metricName, ServiceName = serviceName };
            var result = await _mediator.Send(query);
            
            if (result == null)
                return NotFound();

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get metric {MetricName} for service {ServiceName}", metricName, serviceName);
            return StatusCode(500, "Failed to retrieve metric");
        }
    }

    /// <summary>
    /// Get metrics for a specific service
    /// </summary>
    [HttpGet("services/{serviceName}")]
    public async Task<ActionResult<IEnumerable<MetricDto>>> GetMetricsByService(string serviceName, [FromQuery] bool activeOnly = true)
    {
        try
        {
            var query = new GetMetricsByServiceQuery { ServiceName = serviceName, ActiveOnly = activeOnly };
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get metrics for service {ServiceName}", serviceName);
            return StatusCode(500, "Failed to retrieve service metrics");
        }
    }

    /// <summary>
    /// Create a new metric
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Admin,Monitor")]
    public async Task<ActionResult<MetricDto>> CreateMetric([FromBody] CreateMetricRequest request)
    {
        try
        {
            var command = new CreateMetricCommand
            {
                Name = request.Name,
                ServiceName = request.ServiceName,
                Description = request.Description,
                Type = request.Type,
                Unit = request.Unit,
                Category = request.Category,
                RetentionPeriod = request.RetentionPeriod,
                AggregationInterval = request.AggregationInterval,
                Tags = request.Tags,
                Metadata = request.Metadata
            };

            var result = await _mediator.Send(command);
            return CreatedAtAction(nameof(GetMetric), new { id = result.Id }, result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create metric");
            return StatusCode(500, "Failed to create metric");
        }
    }

    /// <summary>
    /// Record a metric value
    /// </summary>
    [HttpPost("{id}/values")]
    public async Task<ActionResult> RecordMetricValue(Guid id, [FromBody] RecordMetricValueRequest request)
    {
        try
        {
            var command = new RecordMetricValueCommand
            {
                MetricId = id,
                Value = request.Value,
                Timestamp = request.Timestamp,
                AdditionalTags = request.AdditionalTags
            };

            await _mediator.Send(command);
            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to record metric value for {MetricId}", id);
            return StatusCode(500, "Failed to record metric value");
        }
    }

    /// <summary>
    /// Record metric by name (auto-create if not exists)
    /// </summary>
    [HttpPost("record")]
    public async Task<ActionResult> RecordMetric([FromBody] RecordMetricRequest request)
    {
        try
        {
            var command = new RecordMetricByNameCommand
            {
                ServiceName = request.ServiceName,
                MetricName = request.MetricName,
                Value = request.Value,
                Unit = request.Unit,
                Type = request.Type,
                Timestamp = request.Timestamp,
                Tags = request.Tags
            };

            await _mediator.Send(command);
            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to record metric {MetricName} for service {ServiceName}", 
                request.MetricName, request.ServiceName);
            return StatusCode(500, "Failed to record metric");
        }
    }

    /// <summary>
    /// Record multiple metrics in batch
    /// </summary>
    [HttpPost("batch")]
    public async Task<ActionResult> RecordMetricsBatch([FromBody] RecordMetricsBatchRequest request)
    {
        try
        {
            var command = new RecordMetricsBatchCommand { Metrics = request.Metrics };
            await _mediator.Send(command);
            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to record metrics batch");
            return StatusCode(500, "Failed to record metrics batch");
        }
    }

    /// <summary>
    /// Get metric data points
    /// </summary>
    [HttpGet("{id}/data")]
    public async Task<ActionResult<IEnumerable<MetricDataPointDto>>> GetMetricData(
        Guid id, 
        [FromQuery] DateTime fromDate, 
        [FromQuery] DateTime toDate,
        [FromQuery] TimeSpan? aggregationInterval = null)
    {
        try
        {
            var query = new GetMetricDataPointsQuery
            {
                MetricId = id,
                FromDate = fromDate,
                ToDate = toDate,
                AggregationInterval = aggregationInterval
            };

            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get metric data for {MetricId}", id);
            return StatusCode(500, "Failed to retrieve metric data");
        }
    }

    /// <summary>
    /// Get metric data by name
    /// </summary>
    [HttpGet("data/by-name")]
    public async Task<ActionResult<IEnumerable<MetricDataPointDto>>> GetMetricDataByName(
        [FromQuery] string serviceName,
        [FromQuery] string metricName,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimeSpan? aggregationInterval = null)
    {
        try
        {
            var query = new GetMetricDataByNameQuery
            {
                ServiceName = serviceName,
                MetricName = metricName,
                FromDate = fromDate,
                ToDate = toDate,
                AggregationInterval = aggregationInterval
            };

            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get metric data for {MetricName} in service {ServiceName}", 
                metricName, serviceName);
            return StatusCode(500, "Failed to retrieve metric data");
        }
    }

    /// <summary>
    /// Get latest metric values for a service
    /// </summary>
    [HttpGet("services/{serviceName}/latest")]
    public async Task<ActionResult<IEnumerable<MetricDataPointDto>>> GetLatestMetricValues(
        string serviceName, 
        [FromQuery] TimeSpan? period = null)
    {
        try
        {
            var query = new GetLatestMetricValuesQuery
            {
                ServiceName = serviceName,
                Period = period ?? TimeSpan.FromHours(1)
            };

            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get latest metric values for service {ServiceName}", serviceName);
            return StatusCode(500, "Failed to retrieve latest metric values");
        }
    }

    /// <summary>
    /// Set alert threshold for a metric
    /// </summary>
    [HttpPost("{id}/threshold")]
    [Authorize(Roles = "Admin,Monitor")]
    public async Task<ActionResult<MetricDto>> SetThreshold(Guid id, [FromBody] SetThresholdRequest request)
    {
        try
        {
            var command = new SetMetricThresholdCommand
            {
                MetricId = id,
                Threshold = request.Threshold
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to set threshold for metric {MetricId}", id);
            return StatusCode(500, "Failed to set metric threshold");
        }
    }

    /// <summary>
    /// Enable alerting for a metric
    /// </summary>
    [HttpPost("{id}/alerting/enable")]
    [Authorize(Roles = "Admin,Monitor")]
    public async Task<ActionResult<MetricDto>> EnableAlerting(Guid id)
    {
        try
        {
            var command = new EnableMetricAlertingCommand { MetricId = id };
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to enable alerting for metric {MetricId}", id);
            return StatusCode(500, "Failed to enable metric alerting");
        }
    }

    /// <summary>
    /// Disable alerting for a metric
    /// </summary>
    [HttpPost("{id}/alerting/disable")]
    [Authorize(Roles = "Admin,Monitor")]
    public async Task<ActionResult<MetricDto>> DisableAlerting(Guid id)
    {
        try
        {
            var command = new DisableMetricAlertingCommand { MetricId = id };
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to disable alerting for metric {MetricId}", id);
            return StatusCode(500, "Failed to disable metric alerting");
        }
    }

    /// <summary>
    /// Record performance metrics for a service
    /// </summary>
    [HttpPost("performance")]
    public async Task<ActionResult> RecordPerformanceMetrics([FromBody] RecordPerformanceMetricsRequest request)
    {
        try
        {
            var command = new RecordPerformanceMetricsCommand
            {
                ServiceName = request.ServiceName,
                Metrics = request.Metrics
            };

            await _mediator.Send(command);
            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to record performance metrics for service {ServiceName}", request.ServiceName);
            return StatusCode(500, "Failed to record performance metrics");
        }
    }
}

// Request DTOs
public class GetMetricsRequest
{
    public string? ServiceName { get; set; }
    public MetricType? Type { get; set; }
    public MonitoringCategory? Category { get; set; }
    public bool? IsActive { get; set; }
    public bool? AlertingEnabled { get; set; }
}

public class CreateMetricRequest
{
    public string Name { get; set; } = string.Empty;
    public string ServiceName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public MetricType Type { get; set; }
    public string Unit { get; set; } = string.Empty;
    public MonitoringCategory Category { get; set; }
    public TimeSpan? RetentionPeriod { get; set; }
    public TimeSpan? AggregationInterval { get; set; }
    public Dictionary<string, string>? Tags { get; set; }
    public Dictionary<string, string>? Metadata { get; set; }
}

public class RecordMetricValueRequest
{
    public double Value { get; set; }
    public DateTime? Timestamp { get; set; }
    public Dictionary<string, string>? AdditionalTags { get; set; }
}

public class RecordMetricRequest
{
    public string ServiceName { get; set; } = string.Empty;
    public string MetricName { get; set; } = string.Empty;
    public double Value { get; set; }
    public string Unit { get; set; } = string.Empty;
    public MetricType Type { get; set; }
    public DateTime? Timestamp { get; set; }
    public Dictionary<string, string>? Tags { get; set; }
}

public class RecordMetricsBatchRequest
{
    public IEnumerable<MetricRecordingDto> Metrics { get; set; } = new List<MetricRecordingDto>();
}

public class SetThresholdRequest
{
    public AlertThresholdDto Threshold { get; set; } = null!;
}

public class RecordPerformanceMetricsRequest
{
    public string ServiceName { get; set; } = string.Empty;
    public PerformanceMetricsDto Metrics { get; set; } = null!;
}
