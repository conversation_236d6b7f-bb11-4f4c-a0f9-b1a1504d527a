using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NetworkFleetManagement.Application.Commands.Vehicles;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Application.Queries.Vehicles;
using NetworkFleetManagement.Domain.Enums;

namespace NetworkFleetManagement.API.Controllers;

[ApiController]
[Route("api/v1/[controller]")]
[Authorize]
public class VehiclesController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<VehiclesController> _logger;

    public VehiclesController(IMediator mediator, ILogger<VehiclesController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get all vehicles with pagination and filtering
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<PagedResult<VehicleSummaryDto>>> GetVehicles(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] Guid? carrierId = null,
        [FromQuery] VehicleStatus? status = null,
        [FromQuery] VehicleType? vehicleType = null,
        [FromQuery] string? searchTerm = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetVehiclesQuery(pageNumber, pageSize, carrierId, status, vehicleType, searchTerm);
            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving vehicles");
            return StatusCode(500, "An error occurred while retrieving vehicles");
        }
    }

    /// <summary>
    /// Get vehicle by ID
    /// </summary>
    [HttpGet("{id:guid}")]
    public async Task<ActionResult<VehicleDto>> GetVehicle(Guid id, CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetVehicleByIdQuery(id);
            var result = await _mediator.Send(query, cancellationToken);

            if (result == null)
                return NotFound($"Vehicle with ID {id} not found");

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving vehicle {VehicleId}", id);
            return StatusCode(500, "An error occurred while retrieving the vehicle");
        }
    }

    /// <summary>
    /// Create a new vehicle
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<VehicleDto>> CreateVehicle(
        [FromBody] CreateVehicleDto createVehicleDto,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var command = new CreateVehicleCommand(createVehicleDto);
            var result = await _mediator.Send(command, cancellationToken);

            return CreatedAtAction(
                nameof(GetVehicle),
                new { id = result.Id },
                result);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while creating vehicle");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating vehicle");
            return StatusCode(500, "An error occurred while creating the vehicle");
        }
    }

    /// <summary>
    /// Update vehicle information
    /// </summary>
    [HttpPut("{id:guid}")]
    public async Task<ActionResult<VehicleDto>> UpdateVehicle(
        Guid id,
        [FromBody] UpdateVehicleDto updateVehicleDto,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var command = new UpdateVehicleCommand(id, updateVehicleDto);
            var result = await _mediator.Send(command, cancellationToken);

            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while updating vehicle");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating vehicle {VehicleId}", id);
            return StatusCode(500, "An error occurred while updating the vehicle");
        }
    }

    /// <summary>
    /// Update vehicle status
    /// </summary>
    [HttpPut("{id:guid}/status")]
    public async Task<ActionResult<VehicleDto>> UpdateVehicleStatus(
        Guid id,
        [FromBody] UpdateVehicleStatusDto statusDto,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var command = new UpdateVehicleStatusCommand(id, statusDto);
            var result = await _mediator.Send(command, cancellationToken);

            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while updating vehicle status");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating vehicle status for {VehicleId}", id);
            return StatusCode(500, "An error occurred while updating the vehicle status");
        }
    }

    /// <summary>
    /// Update vehicle location
    /// </summary>
    [HttpPut("{id:guid}/location")]
    public async Task<ActionResult<VehicleDto>> UpdateVehicleLocation(
        Guid id,
        [FromBody] UpdateVehicleLocationDto locationDto,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var command = new UpdateVehicleLocationCommand(id, locationDto);
            var result = await _mediator.Send(command, cancellationToken);

            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while updating vehicle location");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating vehicle location for {VehicleId}", id);
            return StatusCode(500, "An error occurred while updating the vehicle location");
        }
    }

    /// <summary>
    /// Schedule vehicle maintenance
    /// </summary>
    [HttpPost("{id:guid}/maintenance/schedule")]
    public async Task<ActionResult<VehicleDto>> ScheduleMaintenance(
        Guid id,
        [FromBody] ScheduleMaintenanceDto maintenanceDto,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var command = new ScheduleMaintenanceCommand(id, maintenanceDto);
            var result = await _mediator.Send(command, cancellationToken);

            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while scheduling maintenance");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error scheduling maintenance for vehicle {VehicleId}", id);
            return StatusCode(500, "An error occurred while scheduling maintenance");
        }
    }

    /// <summary>
    /// Complete vehicle maintenance
    /// </summary>
    [HttpPost("{id:guid}/maintenance/complete")]
    public async Task<ActionResult<VehicleDto>> CompleteMaintenance(
        Guid id,
        [FromBody] CompleteMaintenanceDto maintenanceDto,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var command = new CompleteMaintenanceCommand(id, maintenanceDto);
            var result = await _mediator.Send(command, cancellationToken);

            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while completing maintenance");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error completing maintenance for vehicle {VehicleId}", id);
            return StatusCode(500, "An error occurred while completing maintenance");
        }
    }

    /// <summary>
    /// Get vehicles by carrier
    /// </summary>
    [HttpGet("by-carrier/{carrierId:guid}")]
    public async Task<ActionResult<IEnumerable<VehicleSummaryDto>>> GetVehiclesByCarrier(
        Guid carrierId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetVehiclesByCarrierQuery(carrierId);
            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving vehicles for carrier {CarrierId}", carrierId);
            return StatusCode(500, "An error occurred while retrieving vehicles");
        }
    }

    /// <summary>
    /// Get vehicles dashboard for carrier with detailed information
    /// </summary>
    [HttpGet("dashboard/carrier/{carrierId:guid}")]
    [Authorize(Roles = "Admin,Carrier")]
    public async Task<ActionResult<PagedResult<VehicleDashboardDto>>> GetVehiclesDashboardByCarrier(
        Guid carrierId,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] VehicleStatus? status = null,
        [FromQuery] VehicleType? vehicleType = null,
        [FromQuery] bool? hasExpiredDocuments = null,
        [FromQuery] bool? requiresMaintenance = null,
        [FromQuery] bool? isAssigned = null,
        [FromQuery] string? searchTerm = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetVehiclesByCarrierWithDetailsQuery(
                carrierId, pageNumber, pageSize, status, vehicleType,
                hasExpiredDocuments, requiresMaintenance, isAssigned, searchTerm);
            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving vehicles dashboard for carrier {CarrierId}", carrierId);
            return StatusCode(500, "An error occurred while retrieving vehicles dashboard");
        }
    }

    /// <summary>
    /// Get available vehicles
    /// </summary>
    [HttpGet("available")]
    public async Task<ActionResult<IEnumerable<VehicleSummaryDto>>> GetAvailableVehicles(
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetAvailableVehiclesQuery();
            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving available vehicles");
            return StatusCode(500, "An error occurred while retrieving available vehicles");
        }
    }

    /// <summary>
    /// Get vehicles requiring maintenance
    /// </summary>
    [HttpGet("maintenance-required")]
    public async Task<ActionResult<IEnumerable<VehicleSummaryDto>>> GetVehiclesRequiringMaintenance(
        [FromQuery] int daysThreshold = 7,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetVehiclesRequiringMaintenanceQuery(daysThreshold);
            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving vehicles requiring maintenance");
            return StatusCode(500, "An error occurred while retrieving vehicles requiring maintenance");
        }
    }

    /// <summary>
    /// Get vehicle maintenance history
    /// </summary>
    [HttpGet("{id:guid}/maintenance-history")]
    public async Task<ActionResult<IEnumerable<VehicleMaintenanceRecordDto>>> GetMaintenanceHistory(
        Guid id,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetVehicleMaintenanceHistoryQuery(id);
            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving maintenance history for vehicle {VehicleId}", id);
            return StatusCode(500, "An error occurred while retrieving maintenance history");
        }
    }
}
