using Microsoft.AspNetCore.Http;
using UserManagement.Application.Interfaces;
using UserManagement.Application.Services;

namespace UserManagement.Infrastructure.Services
{
    public class AzureOcrServiceAdapter : IOCRService
    {
        private readonly IOcrService _ocrService;

        public AzureOcrServiceAdapter(IOcrService ocrService)
        {
            _ocrService = ocrService;
        }

        public async Task<OCRResult> ProcessDocumentAsync(IFormFile document)
        {
            // Convert IFormFile to stream and process
            using var stream = document.OpenReadStream();
            using var memoryStream = new MemoryStream();
            await stream.CopyToAsync(memoryStream);
            
            return await ProcessImageAsync(memoryStream.ToArray());
        }

        public async Task<OCRResult> ProcessImageAsync(byte[] imageData)
        {
            // Create a temporary file to work with the existing service
            var tempFile = Path.GetTempFileName();
            try
            {
                await File.WriteAllBytesAsync(tempFile, imageData);
                
                // Use the existing OCR service
                var result = await _ocrService.ExtractDataAsync(tempFile, "general");
                
                return new OCRResult
                {
                    Text = string.Join("\n", result.ExtractedText),
                    ExtractedFields = result.Fields,
                    Confidence = (float)result.Confidence,
                    ExtractedData = result.Fields.ToDictionary(kvp => kvp.Key, kvp => (object)kvp.Value)
                };
            }
            finally
            {
                if (File.Exists(tempFile))
                {
                    File.Delete(tempFile);
                }
            }
        }

        public async Task<OCRResult> ExtractDataAsync(string filePath, string documentType, CancellationToken cancellationToken = default)
        {
            var result = await _ocrService.ExtractDataAsync(filePath, documentType, cancellationToken);
            
            return new OCRResult
            {
                Text = string.Join("\n", result.ExtractedText),
                ExtractedFields = result.Fields,
                Confidence = (float)result.Confidence,
                ExtractedData = result.Fields.ToDictionary(kvp => kvp.Key, kvp => (object)kvp.Value)
            };
        }
    }
}
