using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Infrastructure.Services;

public class SubscriptionBillingService : ISubscriptionBillingService
{
    private readonly ISubscriptionRepository _subscriptionRepository;
    private readonly ISubscriptionPlanRepository _planRepository;
    private readonly IBillingProcessor _billingProcessor;
    private readonly ISubscriptionNotificationService _notificationService;
    private readonly ILogger<SubscriptionBillingService> _logger;
    private readonly SubscriptionConfiguration _configuration;

    public SubscriptionBillingService(
        ISubscriptionRepository subscriptionRepository,
        ISubscriptionPlanRepository planRepository,
        IBillingProcessor billingProcessor,
        ISubscriptionNotificationService notificationService,
        ILogger<SubscriptionBillingService> logger,
        IOptions<SubscriptionConfiguration> configuration)
    {
        _subscriptionRepository = subscriptionRepository;
        _planRepository = planRepository;
        _billingProcessor = billingProcessor;
        _notificationService = notificationService;
        _logger = logger;
        _configuration = configuration.Value;
    }

    public async Task<Subscription> CreateSubscriptionAsync(CreateSubscriptionRequest request)
    {
        _logger.LogInformation("Creating subscription for user {UserId} with plan {PlanId}",
            request.UserId, request.PlanId);

        try
        {
            var plan = await _planRepository.GetByIdAsync(request.PlanId);
            if (plan == null)
            {
                throw new InvalidOperationException($"Subscription plan {request.PlanId} not found");
            }

            if (!plan.IsActive)
            {
                throw new InvalidOperationException($"Subscription plan {request.PlanId} is not active");
            }

            var startDate = request.StartDate ?? DateTime.UtcNow;
            var nextBillingDate = plan.CalculateNextBillingDate(startDate);
            
            DateTime? trialEndDate = null;
            if (request.StartTrial && plan.TrialPeriodDays.HasValue)
            {
                trialEndDate = startDate.AddDays(plan.TrialPeriodDays.Value);
                nextBillingDate = trialEndDate.Value;
            }

            // Create or get customer ID
            var customerId = request.CustomerId;
            if (string.IsNullOrEmpty(customerId) && !string.IsNullOrEmpty(request.PaymentMethodId))
            {
                customerId = await _billingProcessor.CreateCustomerAsync(
                    request.UserId, 
                    $"user_{request.UserId}@example.com", // In real implementation, get actual email
                    request.PaymentMethodId);
            }

            var subscription = new Subscription(
                request.UserId,
                request.PlanId,
                plan.Price,
                startDate,
                nextBillingDate,
                request.PaymentMethodId,
                customerId,
                trialEndDate);

            // Add metadata
            foreach (var metadata in request.Metadata)
            {
                subscription.Metadata[metadata.Key] = metadata.Value;
            }

            await _subscriptionRepository.AddAsync(subscription);

            _logger.LogInformation("Subscription {SubscriptionId} created successfully for user {UserId}",
                subscription.Id, request.UserId);

            return subscription;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating subscription for user {UserId}", request.UserId);
            throw;
        }
    }

    public async Task<Subscription> UpdateSubscriptionAsync(Guid subscriptionId, UpdateSubscriptionRequest request)
    {
        _logger.LogInformation("Updating subscription {SubscriptionId}", subscriptionId);

        try
        {
            var subscription = await _subscriptionRepository.GetByIdAsync(subscriptionId);
            if (subscription == null)
            {
                throw new InvalidOperationException($"Subscription {subscriptionId} not found");
            }

            // Update plan if requested
            if (request.NewPlanId.HasValue)
            {
                var newPlan = await _planRepository.GetByIdAsync(request.NewPlanId.Value);
                if (newPlan == null)
                {
                    throw new InvalidOperationException($"New plan {request.NewPlanId} not found");
                }

                // Handle plan change logic (simplified)
                subscription.UpdatePrice(newPlan.Price, "Plan changed");
                var nextBillingDate = newPlan.CalculateNextBillingDate(DateTime.UtcNow);
                subscription.UpdateNextBillingDate(nextBillingDate);
            }

            // Update payment method if requested
            if (!string.IsNullOrEmpty(request.PaymentMethodId))
            {
                subscription.UpdatePaymentMethod(request.PaymentMethodId);
            }

            // Update metadata if provided
            if (request.Metadata != null)
            {
                foreach (var metadata in request.Metadata)
                {
                    subscription.Metadata[metadata.Key] = metadata.Value;
                }
            }

            await _subscriptionRepository.UpdateAsync(subscription);

            _logger.LogInformation("Subscription {SubscriptionId} updated successfully", subscriptionId);
            return subscription;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating subscription {SubscriptionId}", subscriptionId);
            throw;
        }
    }

    public async Task<Subscription> CancelSubscriptionAsync(Guid subscriptionId, string reason)
    {
        _logger.LogInformation("Cancelling subscription {SubscriptionId}", subscriptionId);

        try
        {
            var subscription = await _subscriptionRepository.GetByIdAsync(subscriptionId);
            if (subscription == null)
            {
                throw new InvalidOperationException($"Subscription {subscriptionId} not found");
            }

            subscription.Cancel(reason);
            await _subscriptionRepository.UpdateAsync(subscription);

            // Send notification
            await _notificationService.SendSubscriptionCancelledNotificationAsync(subscription, reason);

            _logger.LogInformation("Subscription {SubscriptionId} cancelled successfully", subscriptionId);
            return subscription;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling subscription {SubscriptionId}", subscriptionId);
            throw;
        }
    }

    public async Task<Subscription> PauseSubscriptionAsync(Guid subscriptionId, string reason)
    {
        _logger.LogInformation("Pausing subscription {SubscriptionId}", subscriptionId);

        try
        {
            var subscription = await _subscriptionRepository.GetByIdAsync(subscriptionId);
            if (subscription == null)
            {
                throw new InvalidOperationException($"Subscription {subscriptionId} not found");
            }

            subscription.Pause(reason);
            await _subscriptionRepository.UpdateAsync(subscription);

            _logger.LogInformation("Subscription {SubscriptionId} paused successfully", subscriptionId);
            return subscription;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error pausing subscription {SubscriptionId}", subscriptionId);
            throw;
        }
    }

    public async Task<Subscription> ResumeSubscriptionAsync(Guid subscriptionId)
    {
        _logger.LogInformation("Resuming subscription {SubscriptionId}", subscriptionId);

        try
        {
            var subscription = await _subscriptionRepository.GetByIdAsync(subscriptionId);
            if (subscription == null)
            {
                throw new InvalidOperationException($"Subscription {subscriptionId} not found");
            }

            subscription.Resume();
            await _subscriptionRepository.UpdateAsync(subscription);

            _logger.LogInformation("Subscription {SubscriptionId} resumed successfully", subscriptionId);
            return subscription;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resuming subscription {SubscriptionId}", subscriptionId);
            throw;
        }
    }

    public async Task<List<Subscription>> GetUserSubscriptionsAsync(Guid userId)
    {
        try
        {
            return await _subscriptionRepository.GetByUserIdAsync(userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting subscriptions for user {UserId}", userId);
            throw;
        }
    }

    public async Task<Subscription?> GetSubscriptionAsync(Guid subscriptionId)
    {
        try
        {
            return await _subscriptionRepository.GetByIdAsync(subscriptionId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting subscription {SubscriptionId}", subscriptionId);
            throw;
        }
    }

    public async Task<List<Subscription>> GetSubscriptionsDueForBillingAsync()
    {
        try
        {
            return await _subscriptionRepository.GetDueForBillingAsync(DateTime.UtcNow);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting subscriptions due for billing");
            throw;
        }
    }

    public async Task<BillingResult> ProcessBillingAsync(Guid subscriptionId)
    {
        _logger.LogInformation("Processing billing for subscription {SubscriptionId}", subscriptionId);

        try
        {
            var subscription = await _subscriptionRepository.GetByIdAsync(subscriptionId);
            if (subscription == null)
            {
                return new BillingResult
                {
                    SubscriptionId = subscriptionId,
                    IsSuccess = false,
                    ErrorMessage = "Subscription not found",
                    ProcessedAt = DateTime.UtcNow
                };
            }

            var plan = await _planRepository.GetByIdAsync(subscription.SubscriptionPlanId);
            if (plan == null)
            {
                return new BillingResult
                {
                    SubscriptionId = subscriptionId,
                    IsSuccess = false,
                    ErrorMessage = "Subscription plan not found",
                    ProcessedAt = DateTime.UtcNow
                };
            }

            return await _billingProcessor.ProcessSubscriptionBillingAsync(subscription, plan);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing billing for subscription {SubscriptionId}", subscriptionId);
            
            return new BillingResult
            {
                SubscriptionId = subscriptionId,
                IsSuccess = false,
                ErrorMessage = ex.Message,
                ProcessedAt = DateTime.UtcNow
            };
        }
    }

    public async Task<List<BillingResult>> ProcessBatchBillingAsync(List<Guid> subscriptionIds)
    {
        _logger.LogInformation("Processing batch billing for {Count} subscriptions", subscriptionIds.Count);

        var results = new List<BillingResult>();

        foreach (var subscriptionId in subscriptionIds)
        {
            try
            {
                var result = await ProcessBillingAsync(subscriptionId);
                results.Add(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in batch billing for subscription {SubscriptionId}", subscriptionId);
                
                results.Add(new BillingResult
                {
                    SubscriptionId = subscriptionId,
                    IsSuccess = false,
                    ErrorMessage = ex.Message,
                    ProcessedAt = DateTime.UtcNow
                });
            }
        }

        _logger.LogInformation("Batch billing completed. Success: {Success}, Failed: {Failed}",
            results.Count(r => r.IsSuccess), results.Count(r => !r.IsSuccess));

        return results;
    }

    public async Task<SubscriptionPlan> CreatePlanAsync(CreatePlanRequest request)
    {
        _logger.LogInformation("Creating subscription plan {PlanName}", request.PlanName);

        try
        {
            var plan = new SubscriptionPlan(
                request.PlanName,
                request.Description,
                new Money(request.Price, request.Currency),
                request.BillingInterval,
                request.BillingIntervalCount,
                request.TrialPeriodDays,
                request.PlanCode);

            // Add features
            foreach (var feature in request.Features)
            {
                plan.Features[feature.Key] = feature.Value;
            }

            // Add tiers
            foreach (var tierRequest in request.Tiers)
            {
                plan.AddTier(
                    tierRequest.TierName,
                    new Money(tierRequest.TierPrice, tierRequest.Currency),
                    tierRequest.UserLimit,
                    tierRequest.Features);
            }

            await _planRepository.AddAsync(plan);

            _logger.LogInformation("Subscription plan {PlanName} created successfully with ID {PlanId}",
                request.PlanName, plan.Id);

            return plan;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating subscription plan {PlanName}", request.PlanName);
            throw;
        }
    }

    public async Task<List<SubscriptionPlan>> GetActivePlansAsync()
    {
        try
        {
            return await _planRepository.GetActivePlansAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active subscription plans");
            throw;
        }
    }

    public async Task<SubscriptionPlan?> GetPlanAsync(Guid planId)
    {
        try
        {
            return await _planRepository.GetByIdAsync(planId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting subscription plan {PlanId}", planId);
            throw;
        }
    }

    public async Task<SubscriptionAnalytics> GetSubscriptionAnalyticsAsync(DateTime? fromDate = null, DateTime? toDate = null)
    {
        _logger.LogInformation("Getting subscription analytics");

        try
        {
            var from = fromDate ?? DateTime.UtcNow.AddDays(-30);
            var to = toDate ?? DateTime.UtcNow;

            // Get all subscriptions for analytics
            var activeSubscriptions = await _subscriptionRepository.GetByStatusAsync(SubscriptionStatus.Active);
            var cancelledSubscriptions = await _subscriptionRepository.GetByStatusAsync(SubscriptionStatus.Cancelled);
            var pausedSubscriptions = await _subscriptionRepository.GetByStatusAsync(SubscriptionStatus.Paused);
            var trialSubscriptions = await _subscriptionRepository.GetByStatusAsync(SubscriptionStatus.Trialing);

            var totalSubscriptions = activeSubscriptions.Count + cancelledSubscriptions.Count + 
                                   pausedSubscriptions.Count + trialSubscriptions.Count;

            // Calculate MRR (simplified)
            var mrr = activeSubscriptions.Aggregate(Money.Zero("INR"), 
                (sum, sub) => sum + sub.CurrentPrice);

            // Calculate ARR
            var arr = new Money(mrr.Amount * 12, mrr.Currency);

            // Calculate churn rate (simplified)
            var churnRate = totalSubscriptions > 0 ? 
                (decimal)cancelledSubscriptions.Count / totalSubscriptions * 100 : 0;

            return new SubscriptionAnalytics
            {
                PeriodStart = from,
                PeriodEnd = to,
                TotalSubscriptions = totalSubscriptions,
                ActiveSubscriptions = activeSubscriptions.Count,
                CancelledSubscriptions = cancelledSubscriptions.Count,
                PausedSubscriptions = pausedSubscriptions.Count,
                TrialSubscriptions = trialSubscriptions.Count,
                MonthlyRecurringRevenue = mrr,
                AnnualRecurringRevenue = arr,
                ChurnRate = churnRate,
                GrowthRate = 0, // Would need historical data to calculate
                AverageRevenuePerUser = activeSubscriptions.Any() ? 
                    new Money(mrr.Amount / activeSubscriptions.Count, mrr.Currency) : Money.Zero("INR")
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting subscription analytics");
            throw;
        }
    }
}
