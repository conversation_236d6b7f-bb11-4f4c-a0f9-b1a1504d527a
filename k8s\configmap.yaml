apiVersion: v1
kind: ConfigMap
metadata:
  name: tli-config
  namespace: tli-microservices
data:
  ASPNETCORE_ENVIRONMENT: "Production"
  ASPNETCORE_URLS: "http://+:80"
  
  # Logging Configuration
  Logging__LogLevel__Default: "Information"
  Logging__LogLevel__Microsoft: "Warning"
  Logging__LogLevel__Microsoft.Hosting.Lifetime: "Information"
  
  # Health Checks
  HealthChecks__Enabled: "true"
  HealthChecks__DetailedErrors: "false"
  HealthChecks__Timeout: "00:00:30"
  
  # Monitoring
  Monitoring__Prometheus__Enabled: "true"
  Monitoring__Prometheus__Endpoint: "/metrics"
  Monitoring__Jaeger__Enabled: "true"
  Monitoring__Jaeger__AgentHost: "jaeger-agent"
  Monitoring__Jaeger__AgentPort: "6831"
  
  # Caching
  Caching__Redis__Enabled: "true"
  Caching__Redis__DefaultExpirationMinutes: "60"
  Caching__Redis__Database: "0"
  Caching__Redis__KeyPrefix: "TLI:"
  
  # Message Broker
  MessageBroker__RabbitMQ__Enabled: "true"
  MessageBroker__RabbitMQ__ExchangeName: "tli.events"
  MessageBroker__RabbitMQ__QueuePrefix: "tli."
  MessageBroker__RabbitMQ__RetryCount: "5"
  MessageBroker__RabbitMQ__RetryDelay: "00:00:10"
  
  # Security
  Security__EnableHttpsRedirection: "false"
  Security__EnableHsts: "false"
  
  # CORS
  Cors__AllowedMethods__0: "GET"
  Cors__AllowedMethods__1: "POST"
  Cors__AllowedMethods__2: "PUT"
  Cors__AllowedMethods__3: "DELETE"
  Cors__AllowedMethods__4: "PATCH"
  Cors__AllowedMethods__5: "OPTIONS"
  Cors__AllowedHeaders__0: "*"
  Cors__AllowCredentials: "true"
  
  # Rate Limiting
  RateLimiting__EnableRateLimiting: "true"
  RateLimiting__GeneralRules__PermitLimit: "50"
  RateLimiting__GeneralRules__Window: "00:01:00"
  RateLimiting__GeneralRules__ReplenishmentPeriod: "00:00:10"
  RateLimiting__GeneralRules__TokensPerPeriod: "5"
  RateLimiting__GeneralRules__QueueLimit: "5"
  
  # Features
  Features__EnableUserRegistration: "true"
  Features__EnableEmailVerification: "true"
  Features__EnablePhoneVerification: "true"
  Features__EnableTwoFactorAuth: "true"
  Features__EnableSocialLogin: "true"
  Features__EnableFileUpload: "true"
  Features__EnableRealTimeNotifications: "true"
  Features__EnableAnalytics: "true"
  Features__EnableAuditLogging: "true"
  Features__MaintenanceMode: "false"
  
  # Business Rules
  BusinessRules__MaxOrderValue: "1000000"
  BusinessRules__MinOrderValue: "100"
  BusinessRules__DefaultCurrency: "INR"
  BusinessRules__MaxFileUploadSize: "10485760"
  BusinessRules__SessionTimeoutMinutes: "30"
  
  # Localization
  Localization__DefaultCulture: "en-IN"
  Localization__SupportedCultures__0: "en-IN"
  Localization__SupportedCultures__1: "hi-IN"
  Localization__SupportedCultures__2: "en-US"
  Localization__ResourcePath: "Resources"

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: tli-database-config
  namespace: tli-microservices
data:
  # Database Configuration
  DB_HOST: "postgres-service"
  DB_PORT: "5432"
  REDIS_HOST: "redis-service"
  REDIS_PORT: "6379"
  RABBITMQ_HOST: "rabbitmq-service"
  RABBITMQ_PORT: "5672"
  
  # Service Discovery
  IDENTITY_SERVICE_URL: "http://identity-service"
  USER_MANAGEMENT_SERVICE_URL: "http://user-management-service"
  ORDER_MANAGEMENT_SERVICE_URL: "http://order-management-service"
  SUBSCRIPTION_MANAGEMENT_SERVICE_URL: "http://subscription-management-service"
  TRIP_MANAGEMENT_SERVICE_URL: "http://trip-management-service"
  NETWORK_FLEET_MANAGEMENT_SERVICE_URL: "http://network-fleet-management-service"
  FINANCIAL_PAYMENT_SERVICE_URL: "http://financial-payment-service"
  COMMUNICATION_NOTIFICATION_SERVICE_URL: "http://communication-notification-service"
  ANALYTICS_BI_SERVICE_URL: "http://analytics-bi-service"
  DATA_STORAGE_SERVICE_URL: "http://data-storage-service"
  MONITORING_OBSERVABILITY_SERVICE_URL: "http://monitoring-observability-service"
  AUDIT_COMPLIANCE_SERVICE_URL: "http://audit-compliance-service"
  MOBILE_WORKFLOW_SERVICE_URL: "http://mobile-workflow-service"
