using System;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.Extensions.Logging;
using UserManagement.Domain.Repositories;
using UserManagement.Domain.Exceptions;

namespace UserManagement.Application.UserProfiles.Commands.UpdatePersonalDetails
{
    public class UpdatePersonalDetailsCommandHandler : IRequestHandler<UpdatePersonalDetailsCommand, bool>
    {
        private readonly IUserProfileRepository _userProfileRepository;
        private readonly ILogger<UpdatePersonalDetailsCommandHandler> _logger;

        public UpdatePersonalDetailsCommandHandler(
            IUserProfileRepository userProfileRepository,
            ILogger<UpdatePersonalDetailsCommandHandler> logger)
        {
            _userProfileRepository = userProfileRepository;
            _logger = logger;
        }

        public async Task<bool> Handle(UpdatePersonalDetailsCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Updating personal details for profile {ProfileId}", request.Id);

                var userProfile = await _userProfileRepository.GetByIdAsync(request.Id);
                if (userProfile == null)
                {
                    throw new UserManagementDomainException("User profile not found");
                }

                // Update personal details using domain method
                userProfile.UpdatePersonalDetails(request.FirstName, request.LastName, request.PhoneNumber);

                // Save changes
                await _userProfileRepository.UpdateAsync(userProfile);

                _logger.LogInformation("Successfully updated personal details for profile {ProfileId}", request.Id);

                return true;
            }
            catch (UserManagementDomainException)
            {
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while updating personal details for profile {ProfileId}", request.Id);
                throw;
            }
        }
    }
}
