using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NetworkFleetManagement.Domain.Entities;
using NetworkFleetManagement.Infrastructure.Persistence;
using System.Diagnostics;

namespace NetworkFleetManagement.Infrastructure.Performance;

public interface IQueryOptimizationService
{
    Task<IEnumerable<PreferredPartner>> GetOptimizedPreferredPartnersAsync(
        Guid userId, 
        PreferredPartnerType? partnerType = null,
        bool activeOnly = true,
        int? limit = null,
        CancellationToken cancellationToken = default);

    Task<IEnumerable<PreferredPartner>> GetOptimizedAutoAssignPartnersAsync(
        Guid userId,
        PreferredPartnerType partnerType,
        string? route = null,
        string? loadType = null,
        decimal? minRating = null,
        CancellationToken cancellationToken = default);

    Task WarmupCacheAsync(CancellationToken cancellationToken = default);
    Task<QueryPerformanceMetrics> AnalyzeQueryPerformanceAsync(string queryName, Func<Task> queryAction);
}

public class QueryOptimizationService : IQueryOptimizationService
{
    private readonly NetworkFleetDbContext _context;
    private readonly ILogger<QueryOptimizationService> _logger;

    public QueryOptimizationService(NetworkFleetDbContext context, ILogger<QueryOptimizationService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<IEnumerable<PreferredPartner>> GetOptimizedPreferredPartnersAsync(
        Guid userId, 
        PreferredPartnerType? partnerType = null,
        bool activeOnly = true,
        int? limit = null,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            var query = _context.PreferredPartners
                .AsNoTracking()
                .Where(p => p.UserId == userId);

            if (partnerType.HasValue)
            {
                query = query.Where(p => p.PartnerType == partnerType.Value);
            }

            if (activeOnly)
            {
                query = query.Where(p => p.Status == PreferenceStatus.Active);
            }

            // Optimized ordering using composite index
            query = query.OrderBy(p => p.PreferenceLevel)
                         .ThenBy(p => p.Priority)
                         .ThenByDescending(p => p.PerformanceMetrics.OverallRating);

            if (limit.HasValue)
            {
                query = query.Take(limit.Value);
            }

            var results = await query.ToListAsync(cancellationToken);

            stopwatch.Stop();
            _logger.LogInformation("Optimized preferred partners query completed in {ElapsedMs}ms for user {UserId}, returned {Count} results",
                stopwatch.ElapsedMilliseconds, userId, results.Count);

            return results;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error in optimized preferred partners query for user {UserId} after {ElapsedMs}ms",
                userId, stopwatch.ElapsedMilliseconds);
            throw;
        }
    }

    public async Task<IEnumerable<PreferredPartner>> GetOptimizedAutoAssignPartnersAsync(
        Guid userId,
        PreferredPartnerType partnerType,
        string? route = null,
        string? loadType = null,
        decimal? minRating = null,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            // Use raw SQL for complex filtering to leverage database optimizations
            var sql = @"
                SELECT * FROM network_fleet.preferred_partners p
                WHERE p.""UserId"" = @userId 
                  AND p.""PartnerType"" = @partnerType
                  AND p.""Status"" = 'Active'
                  AND p.""AutoAssignEnabled"" = true
                  AND (@minRating IS NULL OR p.overall_rating >= @minRating)
                  AND (@route IS NULL OR 
                       p.""PreferredRoutes""::jsonb ? @route OR 
                       NOT (p.""ExcludedRoutes""::jsonb ? @route))
                  AND (@loadType IS NULL OR 
                       p.""PreferredLoadTypes""::jsonb ? @loadType OR 
                       NOT (p.""ExcludedLoadTypes""::jsonb ? @loadType))
                ORDER BY p.""PreferenceLevel"", p.""Priority"", p.overall_rating DESC
                LIMIT 10";

            var parameters = new[]
            {
                new Npgsql.NpgsqlParameter("@userId", userId),
                new Npgsql.NpgsqlParameter("@partnerType", partnerType.ToString()),
                new Npgsql.NpgsqlParameter("@route", route ?? (object)DBNull.Value),
                new Npgsql.NpgsqlParameter("@loadType", loadType ?? (object)DBNull.Value),
                new Npgsql.NpgsqlParameter("@minRating", minRating ?? (object)DBNull.Value)
            };

            var results = await _context.PreferredPartners
                .FromSqlRaw(sql, parameters)
                .AsNoTracking()
                .ToListAsync(cancellationToken);

            stopwatch.Stop();
            _logger.LogInformation("Optimized auto-assign query completed in {ElapsedMs}ms for user {UserId}, returned {Count} results",
                stopwatch.ElapsedMilliseconds, userId, results.Count);

            return results;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error in optimized auto-assign query for user {UserId} after {ElapsedMs}ms",
                userId, stopwatch.ElapsedMilliseconds);
            throw;
        }
    }

    public async Task WarmupCacheAsync(CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            _logger.LogInformation("Starting cache warmup...");

            // Warmup common queries
            var warmupTasks = new[]
            {
                WarmupPreferredPartnersCache(cancellationToken),
                WarmupPerformanceMetricsCache(cancellationToken),
                WarmupAutoAssignCache(cancellationToken)
            };

            await Task.WhenAll(warmupTasks);

            stopwatch.Stop();
            _logger.LogInformation("Cache warmup completed in {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Cache warmup failed after {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
            throw;
        }
    }

    public async Task<QueryPerformanceMetrics> AnalyzeQueryPerformanceAsync(string queryName, Func<Task> queryAction)
    {
        var stopwatch = Stopwatch.StartNew();
        var startMemory = GC.GetTotalMemory(false);

        try
        {
            await queryAction();
            
            stopwatch.Stop();
            var endMemory = GC.GetTotalMemory(false);

            var metrics = new QueryPerformanceMetrics
            {
                QueryName = queryName,
                ExecutionTime = stopwatch.Elapsed,
                MemoryUsed = endMemory - startMemory,
                Timestamp = DateTime.UtcNow,
                Success = true
            };

            _logger.LogInformation("Query {QueryName} performance: {ElapsedMs}ms, {MemoryKB}KB memory",
                queryName, stopwatch.ElapsedMilliseconds, metrics.MemoryUsed / 1024);

            return metrics;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            var endMemory = GC.GetTotalMemory(false);

            var metrics = new QueryPerformanceMetrics
            {
                QueryName = queryName,
                ExecutionTime = stopwatch.Elapsed,
                MemoryUsed = endMemory - startMemory,
                Timestamp = DateTime.UtcNow,
                Success = false,
                ErrorMessage = ex.Message
            };

            _logger.LogError(ex, "Query {QueryName} failed after {ElapsedMs}ms", queryName, stopwatch.ElapsedMilliseconds);

            return metrics;
        }
    }

    private async Task WarmupPreferredPartnersCache(CancellationToken cancellationToken)
    {
        // Load most common partner types to warm up the cache
        var commonQueries = new[]
        {
            _context.PreferredPartners.Where(p => p.PartnerType == PreferredPartnerType.Carrier && p.Status == PreferenceStatus.Active).Take(100).LoadAsync(cancellationToken),
            _context.PreferredPartners.Where(p => p.PartnerType == PreferredPartnerType.Broker && p.Status == PreferenceStatus.Active).Take(100).LoadAsync(cancellationToken),
            _context.PreferredPartners.Where(p => p.PartnerType == PreferredPartnerType.Transporter && p.Status == PreferenceStatus.Active).Take(100).LoadAsync(cancellationToken)
        };

        await Task.WhenAll(commonQueries);
    }

    private async Task WarmupPerformanceMetricsCache(CancellationToken cancellationToken)
    {
        // Load high-performing partners
        await _context.PreferredPartners
            .Where(p => p.PerformanceMetrics.OverallRating >= 4.0m && p.Status == PreferenceStatus.Active)
            .Take(200)
            .LoadAsync(cancellationToken);
    }

    private async Task WarmupAutoAssignCache(CancellationToken cancellationToken)
    {
        // Load auto-assign enabled partners
        await _context.PreferredPartners
            .Where(p => p.AutoAssignEnabled && p.Status == PreferenceStatus.Active)
            .Take(150)
            .LoadAsync(cancellationToken);
    }
}

public class QueryPerformanceMetrics
{
    public string QueryName { get; set; } = string.Empty;
    public TimeSpan ExecutionTime { get; set; }
    public long MemoryUsed { get; set; }
    public DateTime Timestamp { get; set; }
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
}
