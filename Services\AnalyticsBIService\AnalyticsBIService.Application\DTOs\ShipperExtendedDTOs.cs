using System.ComponentModel.DataAnnotations;
using AnalyticsBIService.Domain.Enums;
using MediatR;

namespace AnalyticsBIService.Application.DTOs;

/// <summary>
/// Shipper shipment tracking data transfer object
/// </summary>
public class ShipperShipmentTrackingDto
{
    public string ShipperId { get; set; } = string.Empty;
    public List<ShipmentTrackingDto> Shipments { get; set; } = new();
    public decimal OnTimeDeliveryRate { get; set; }
    public decimal AverageTransitTime { get; set; }
    public int TotalShipments { get; set; }
    public int DelayedShipments { get; set; }
    public List<TrackingEventDto> RecentEvents { get; set; } = new();
    public DateTime LastUpdated { get; set; }
}

/// <summary>
/// Shipment tracking data transfer object
/// </summary>
public class ShipmentTrackingDto
{
    public string ShipmentId { get; set; } = string.Empty;
    public string TrackingNumber { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string Origin { get; set; } = string.Empty;
    public string Destination { get; set; } = string.Empty;
    public DateTime ShipDate { get; set; }
    public DateTime? DeliveryDate { get; set; }
    public DateTime EstimatedDelivery { get; set; }
    public List<TrackingEventDto> TrackingEvents { get; set; } = new();
    public string CarrierId { get; set; } = string.Empty;
    public string CarrierName { get; set; } = string.Empty;
}

/// <summary>
/// Tracking event data transfer object
/// </summary>
public class TrackingEventDto
{
    public string EventId { get; set; } = string.Empty;
    public string EventType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime EventTime { get; set; }
    public string Location { get; set; } = string.Empty;
    public Dictionary<string, object> EventData { get; set; } = new();
}

/// <summary>
/// Shipper freight optimization data transfer object
/// </summary>
public class ShipperFreightOptimizationDto
{
    public Guid ShipperId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;
    public List<FreightOptimizationDto> Optimizations { get; set; } = new();
    public decimal TotalCostSavings { get; set; }
    public decimal EfficiencyImprovement { get; set; }
    public List<OptimizationRecommendationDto> Recommendations { get; set; } = new();
    public DateTime LastOptimized { get; set; }
}

/// <summary>
/// Freight optimization data transfer object
/// </summary>
public class FreightOptimizationDto
{
    public string OptimizationId { get; set; } = string.Empty;
    public string OptimizationType { get; set; } = string.Empty; // "Route", "Carrier", "Mode", "Consolidation"
    public string Description { get; set; } = string.Empty;
    public decimal CurrentCost { get; set; }
    public decimal OptimizedCost { get; set; }
    public decimal CostSavings { get; set; }
    public decimal EfficiencyGain { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime ImplementationDate { get; set; }

    // Additional properties for analytics
    public decimal LoadOptimizationScore { get; set; }
    public decimal CarrierOptimizationScore { get; set; }
    public decimal RouteOptimizationScore { get; set; }
    public decimal OverallOptimizationScore { get; set; }
    public List<FreightOptimizationOpportunityDto> OptimizationOpportunities { get; set; } = new();
    public LoadOptimizationDto LoadOptimization { get; set; } = new();
    public CarrierOptimizationDto CarrierOptimization { get; set; } = new();
    public RouteOptimizationDto RouteOptimization { get; set; } = new();
}

/// <summary>
/// Shipper compliance analytics data transfer object
/// </summary>
public class ShipperComplianceAnalyticsDto
{
    public string ShipperId { get; set; } = string.Empty;
    public decimal ComplianceScore { get; set; }
    public List<ComplianceRequirementDto> ComplianceRequirements { get; set; } = new();
    public List<ComplianceViolationDto> Violations { get; set; } = new();
    public List<ComplianceAuditDto> Audits { get; set; } = new();
    public List<ComplianceTrendDto> ComplianceTrends { get; set; } = new();
    public DateTime LastAssessment { get; set; }
}

/// <summary>
/// Compliance requirement data transfer object
/// </summary>
public class ComplianceRequirementDto
{
    public string RequirementId { get; set; } = string.Empty;
    public string RequirementName { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty; // "Compliant", "Non-Compliant", "Pending"
    public DateTime LastChecked { get; set; }
    public DateTime? DueDate { get; set; }
    public List<string> RequiredActions { get; set; } = new();
}

/// <summary>
/// Compliance audit data transfer object
/// </summary>
public class ComplianceAuditDto
{
    public string AuditId { get; set; } = string.Empty;
    public string AuditType { get; set; } = string.Empty;
    public DateTime AuditDate { get; set; }
    public string AuditorName { get; set; } = string.Empty;
    public decimal ComplianceScore { get; set; }
    public List<AuditFindingDto> Findings { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
    public string Status { get; set; } = string.Empty;
}



/// <summary>
/// Real-time metrics data transfer object
/// </summary>
public class RealTimeMetricsDto
{
    public string MetricId { get; set; } = string.Empty;
    public string MetricName { get; set; } = string.Empty;
    public decimal CurrentValue { get; set; }
    public decimal PreviousValue { get; set; }
    public decimal ChangePercentage { get; set; }
    public string Trend { get; set; } = string.Empty; // "Increasing", "Decreasing", "Stable"
    public DateTime LastUpdated { get; set; }
    public List<MetricDataPointDto> DataPoints { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();

    // Properties required by RealTimeAnalyticsService
    public Guid UserId { get; set; }
    public string UserRole { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public List<RealTimeMetricDto> Metrics { get; set; } = new();
}

// MetricDataPointDto is already defined in AnalyticsDto.cs - using that instead

/// <summary>
/// Carrier performance alert data transfer object
/// </summary>
public class CarrierPerformanceAlertDto
{
    public string AlertId { get; set; } = string.Empty;
    public string CarrierId { get; set; } = string.Empty;
    public string CarrierName { get; set; } = string.Empty;
    public string AlertType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public bool IsAcknowledged { get; set; } = false;
    public DateTime? AcknowledgedAt { get; set; }
    public string? AcknowledgedBy { get; set; }
    public Dictionary<string, object> AlertData { get; set; } = new();

    // Additional properties required by GetCarrierPerformanceSummaryQueryHandler
    public string Message { get; set; } = string.Empty;
    public bool RequiresAction { get; set; } = false;
}

/// <summary>
/// Time-based earnings data transfer object
/// </summary>
public class TimeBasedEarningsDto
{
    public string DriverId { get; set; } = string.Empty;
    public string DriverName { get; set; } = string.Empty;
    public DateTime Date { get; set; }
    public decimal HourlyRate { get; set; }
    public decimal HoursWorked { get; set; }
    public decimal RegularEarnings { get; set; }
    public decimal OvertimeEarnings { get; set; }
    public decimal BonusEarnings { get; set; }
    public decimal TotalEarnings { get; set; }
    public decimal Amount { get; set; } // Alias for TotalEarnings
    public int TripsCount { get; set; } // Number of trips completed
    public List<EarningsBreakdownDto> EarningsBreakdown { get; set; } = new();
}

// EarningsBreakdownDto is already defined in DriverAnalyticsDTOs.cs - using that instead

// DriverPerformanceDashboardDto is already defined in DriverIncentiveDTOs.cs - using that instead

/// <summary>
/// Performance KPI data transfer object
/// </summary>
public class PerformanceKPIDto
{
    public string KPIName { get; set; } = string.Empty;
    public decimal CurrentValue { get; set; }
    public decimal TargetValue { get; set; }
    public decimal Achievement { get; set; }
    public string Status { get; set; } = string.Empty;
    public string Unit { get; set; } = string.Empty;
    public string Trend { get; set; } = string.Empty;
}

// PerformanceTrendDto is already defined in AnalyticsDto.cs - using that instead

/// <summary>
/// Achievement data transfer object
/// </summary>
public class AchievementDto
{
    public string AchievementId { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public DateTime AchievedDate { get; set; }
    public decimal Points { get; set; }
    public string BadgeUrl { get; set; } = string.Empty;
}

/// <summary>
/// Detailed recommendation data transfer object
/// </summary>
public class DetailedRecommendationDto
{
    public string RecommendationId { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string Priority { get; set; } = string.Empty;
    public decimal ImpactScore { get; set; }
    public decimal ImplementationDifficulty { get; set; }
    public List<string> Benefits { get; set; } = new();
    public List<ActionStepDto> ActionSteps { get; set; } = new();
    public List<string> Resources { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public string Status { get; set; } = "Pending";
}

/// <summary>
/// Action step data transfer object
/// </summary>
public class ActionStepDto
{
    public string StepId { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int Order { get; set; }
    public bool IsCompleted { get; set; } = false;
    public DateTime? CompletedDate { get; set; }
    public string? CompletedBy { get; set; }
}

/// <summary>
/// Driver action plan data transfer object
/// </summary>
public class DriverActionPlanDto
{
    public string PlanId { get; set; } = string.Empty;
    public string DriverId { get; set; } = string.Empty;
    public string DriverName { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<ActionItemDto> ActionItems { get; set; } = new();
    public List<GoalDto> Goals { get; set; } = new();
    public DateTime CreatedDate { get; set; }
    public DateTime? TargetCompletionDate { get; set; }
    public string Status { get; set; } = "Active";
    public decimal ProgressPercentage { get; set; }
}

/// <summary>
/// Action item data transfer object
/// </summary>


/// <summary>
/// Goal data transfer object
/// </summary>
public class GoalDto
{
    public string GoalId { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string MetricName { get; set; } = string.Empty;
    public decimal TargetValue { get; set; }
    public decimal CurrentValue { get; set; }
    public decimal Progress { get; set; }
    public DateTime TargetDate { get; set; }
    public string Status { get; set; } = "Active";
}

/// <summary>
/// Industry benchmark data transfer object
/// </summary>
public class IndustryBenchmarkDto
{
    public string BenchmarkId { get; set; } = string.Empty;
    public string MetricName { get; set; } = string.Empty;
    public string Industry { get; set; } = string.Empty;
    public string Region { get; set; } = string.Empty;
    public decimal AverageValue { get; set; }
    public decimal MedianValue { get; set; }
    public decimal TopQuartileValue { get; set; }
    public decimal BottomQuartileValue { get; set; }
    public DateTime LastUpdated { get; set; }
    public string DataSource { get; set; } = string.Empty;
    public int SampleSize { get; set; }

    // Additional properties required by GetCarrierPerformanceSummaryQueryHandler
    public decimal OnTimeDeliveryRate { get; set; }
    public decimal CustomerSatisfactionScore { get; set; }
    public decimal AverageEarningsPerTrip { get; set; }
    public decimal VehicleUtilizationRate { get; set; }
}

/// <summary>
/// Create custom dashboard request data transfer object
/// </summary>
public class CreateCustomDashboardRequest
{
    [Required]
    public string DashboardName { get; set; } = string.Empty;

    [Required]
    public string Name { get; set; } = string.Empty;

    public string Description { get; set; } = string.Empty;

    [Required]
    public string UserId { get; set; } = string.Empty;

    [Required]
    public UserType UserType { get; set; }

    public List<DashboardWidgetRequest> Widgets { get; set; } = new();
    public Dictionary<string, object> Layout { get; set; } = new();
    public Dictionary<string, object> Settings { get; set; } = new();
    public Dictionary<string, object> GlobalFilters { get; set; } = new();
    public TimeSpan RefreshInterval { get; set; } = TimeSpan.FromMinutes(5);
    public string Theme { get; set; } = "Default";
    public List<string> Tags { get; set; } = new();
    public bool IsPublic { get; set; } = false;
    public List<string> SharedWith { get; set; } = new();
}
