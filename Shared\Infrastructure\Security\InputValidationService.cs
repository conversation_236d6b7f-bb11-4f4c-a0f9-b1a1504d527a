using Microsoft.Extensions.Logging;
using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;

namespace Shared.Infrastructure.Security;

/// <summary>
/// Comprehensive input validation service with XSS protection, SQL injection prevention, and data sanitization
/// Provides enterprise-grade input validation for all user inputs across microservices
/// </summary>
public interface IInputValidationService
{
    ValidationResult ValidateInput<T>(T input) where T : class;
    ValidationResult ValidateString(string input, StringValidationOptions options);
    ValidationResult ValidateEmail(string email);
    ValidationResult ValidatePhoneNumber(string phoneNumber);
    ValidationResult ValidateUrl(string url);
    ValidationResult ValidateGuid(string guid);
    ValidationResult ValidateNumeric(string input, NumericValidationOptions options);
    ValidationResult ValidateDate(string date, DateValidationOptions options);
    string SanitizeHtml(string input);
    string SanitizeString(string input, StringSanitizationOptions options);
    bool ContainsSqlInjection(string input);
    bool ContainsXssAttempt(string input);
    bool ContainsMaliciousContent(string input);
    ValidationResult ValidateFileUpload(FileUploadInfo fileInfo);
    ValidationResult ValidateJsonInput(string json, JsonValidationOptions options);
}

public class InputValidationService : IInputValidationService
{
    private readonly ILogger<InputValidationService> _logger;
    private readonly ValidationConfiguration _config;

    // Common regex patterns
    private static readonly Regex EmailRegex = new(@"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$", RegexOptions.Compiled);
    private static readonly Regex PhoneRegex = new(@"^\+?[1-9]\d{1,14}$", RegexOptions.Compiled);
    private static readonly Regex UrlRegex = new(@"^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$", RegexOptions.Compiled);
    private static readonly Regex GuidRegex = new(@"^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$", RegexOptions.Compiled);
    
    // Security patterns
    private static readonly Regex SqlInjectionRegex = new(@"(\b(ALTER|CREATE|DELETE|DROP|EXEC(UTE)?|INSERT( +INTO)?|MERGE|SELECT|UPDATE|UNION( +ALL)?)\b)|('(''|[^'])*')|(;)|(\b(AND|OR)\b.*[=<>])", RegexOptions.IgnoreCase | RegexOptions.Compiled);
    private static readonly Regex XssRegex = new(@"<script[^>]*>.*?</script>|javascript:|vbscript:|onload=|onerror=|onclick=|onmouseover=", RegexOptions.IgnoreCase | RegexOptions.Compiled);
    private static readonly Regex HtmlTagRegex = new(@"<[^>]+>", RegexOptions.Compiled);

    public InputValidationService(ILogger<InputValidationService> logger, ValidationConfiguration config)
    {
        _logger = logger;
        _config = config;
    }

    public ValidationResult ValidateInput<T>(T input) where T : class
    {
        if (input == null)
        {
            return ValidationResult.Failed("Input cannot be null");
        }

        var validationResults = new List<System.ComponentModel.DataAnnotations.ValidationResult>();
        var validationContext = new ValidationContext(input);

        var isValid = Validator.TryValidateObject(input, validationContext, validationResults, true);

        if (!isValid)
        {
            var errors = validationResults.Select(vr => vr.ErrorMessage ?? "Validation error").ToList();
            return ValidationResult.Failed(errors);
        }

        // Additional security validation
        var securityValidation = ValidateObjectForSecurity(input);
        if (!securityValidation.IsValid)
        {
            return securityValidation;
        }

        return ValidationResult.Success();
    }

    public ValidationResult ValidateString(string input, StringValidationOptions options)
    {
        if (input == null && options.Required)
        {
            return ValidationResult.Failed("String is required");
        }

        if (input == null)
        {
            return ValidationResult.Success();
        }

        var errors = new List<string>();

        // Length validation
        if (options.MinLength.HasValue && input.Length < options.MinLength.Value)
        {
            errors.Add($"String must be at least {options.MinLength.Value} characters long");
        }

        if (options.MaxLength.HasValue && input.Length > options.MaxLength.Value)
        {
            errors.Add($"String must not exceed {options.MaxLength.Value} characters");
        }

        // Pattern validation
        if (!string.IsNullOrEmpty(options.Pattern) && !Regex.IsMatch(input, options.Pattern))
        {
            errors.Add($"String does not match required pattern");
        }

        // Security validation
        if (options.CheckForSqlInjection && ContainsSqlInjection(input))
        {
            errors.Add("Input contains potential SQL injection attempt");
            _logger.LogWarning("SQL injection attempt detected: {Input}", input);
        }

        if (options.CheckForXss && ContainsXssAttempt(input))
        {
            errors.Add("Input contains potential XSS attempt");
            _logger.LogWarning("XSS attempt detected: {Input}", input);
        }

        if (options.CheckForMaliciousContent && ContainsMaliciousContent(input))
        {
            errors.Add("Input contains potentially malicious content");
            _logger.LogWarning("Malicious content detected: {Input}", input);
        }

        // Allowed characters validation
        if (options.AllowedCharacters?.Any() == true)
        {
            var disallowedChars = input.Where(c => !options.AllowedCharacters.Contains(c)).ToList();
            if (disallowedChars.Any())
            {
                errors.Add($"String contains disallowed characters: {string.Join(", ", disallowedChars.Distinct())}");
            }
        }

        return errors.Any() ? ValidationResult.Failed(errors) : ValidationResult.Success();
    }

    public ValidationResult ValidateEmail(string email)
    {
        if (string.IsNullOrWhiteSpace(email))
        {
            return ValidationResult.Failed("Email is required");
        }

        if (!EmailRegex.IsMatch(email))
        {
            return ValidationResult.Failed("Invalid email format");
        }

        if (email.Length > _config.MaxEmailLength)
        {
            return ValidationResult.Failed($"Email must not exceed {_config.MaxEmailLength} characters");
        }

        // Check for suspicious patterns
        if (ContainsSqlInjection(email) || ContainsXssAttempt(email))
        {
            _logger.LogWarning("Suspicious email detected: {Email}", email);
            return ValidationResult.Failed("Invalid email format");
        }

        return ValidationResult.Success();
    }

    public ValidationResult ValidatePhoneNumber(string phoneNumber)
    {
        if (string.IsNullOrWhiteSpace(phoneNumber))
        {
            return ValidationResult.Failed("Phone number is required");
        }

        // Remove common formatting characters
        var cleanedNumber = Regex.Replace(phoneNumber, @"[\s\-\(\)\.]+", "");

        if (!PhoneRegex.IsMatch(cleanedNumber))
        {
            return ValidationResult.Failed("Invalid phone number format");
        }

        return ValidationResult.Success();
    }

    public ValidationResult ValidateUrl(string url)
    {
        if (string.IsNullOrWhiteSpace(url))
        {
            return ValidationResult.Failed("URL is required");
        }

        if (!UrlRegex.IsMatch(url))
        {
            return ValidationResult.Failed("Invalid URL format");
        }

        if (url.Length > _config.MaxUrlLength)
        {
            return ValidationResult.Failed($"URL must not exceed {_config.MaxUrlLength} characters");
        }

        // Check for suspicious patterns
        if (ContainsXssAttempt(url) || ContainsMaliciousContent(url))
        {
            _logger.LogWarning("Suspicious URL detected: {Url}", url);
            return ValidationResult.Failed("Invalid URL");
        }

        return ValidationResult.Success();
    }

    public ValidationResult ValidateGuid(string guid)
    {
        if (string.IsNullOrWhiteSpace(guid))
        {
            return ValidationResult.Failed("GUID is required");
        }

        if (!GuidRegex.IsMatch(guid))
        {
            return ValidationResult.Failed("Invalid GUID format");
        }

        return ValidationResult.Success();
    }

    public ValidationResult ValidateNumeric(string input, NumericValidationOptions options)
    {
        if (string.IsNullOrWhiteSpace(input) && options.Required)
        {
            return ValidationResult.Failed("Numeric value is required");
        }

        if (string.IsNullOrWhiteSpace(input))
        {
            return ValidationResult.Success();
        }

        if (options.IsInteger)
        {
            if (!long.TryParse(input, out var intValue))
            {
                return ValidationResult.Failed("Invalid integer format");
            }

            if (options.MinValue.HasValue && intValue < options.MinValue.Value)
            {
                return ValidationResult.Failed($"Value must be at least {options.MinValue.Value}");
            }

            if (options.MaxValue.HasValue && intValue > options.MaxValue.Value)
            {
                return ValidationResult.Failed($"Value must not exceed {options.MaxValue.Value}");
            }
        }
        else
        {
            if (!decimal.TryParse(input, out var decimalValue))
            {
                return ValidationResult.Failed("Invalid decimal format");
            }

            if (options.MinValue.HasValue && decimalValue < options.MinValue.Value)
            {
                return ValidationResult.Failed($"Value must be at least {options.MinValue.Value}");
            }

            if (options.MaxValue.HasValue && decimalValue > options.MaxValue.Value)
            {
                return ValidationResult.Failed($"Value must not exceed {options.MaxValue.Value}");
            }
        }

        return ValidationResult.Success();
    }

    public ValidationResult ValidateDate(string date, DateValidationOptions options)
    {
        if (string.IsNullOrWhiteSpace(date) && options.Required)
        {
            return ValidationResult.Failed("Date is required");
        }

        if (string.IsNullOrWhiteSpace(date))
        {
            return ValidationResult.Success();
        }

        if (!DateTime.TryParse(date, out var dateValue))
        {
            return ValidationResult.Failed("Invalid date format");
        }

        if (options.MinDate.HasValue && dateValue < options.MinDate.Value)
        {
            return ValidationResult.Failed($"Date must be after {options.MinDate.Value:yyyy-MM-dd}");
        }

        if (options.MaxDate.HasValue && dateValue > options.MaxDate.Value)
        {
            return ValidationResult.Failed($"Date must be before {options.MaxDate.Value:yyyy-MM-dd}");
        }

        if (options.FutureOnly && dateValue <= DateTime.UtcNow)
        {
            return ValidationResult.Failed("Date must be in the future");
        }

        if (options.PastOnly && dateValue >= DateTime.UtcNow)
        {
            return ValidationResult.Failed("Date must be in the past");
        }

        return ValidationResult.Success();
    }

    public string SanitizeHtml(string input)
    {
        if (string.IsNullOrEmpty(input))
        {
            return input;
        }

        // Remove all HTML tags
        var sanitized = HtmlTagRegex.Replace(input, string.Empty);

        // Decode HTML entities
        sanitized = System.Net.WebUtility.HtmlDecode(sanitized);

        // Remove potentially dangerous characters
        sanitized = sanitized.Replace("<", "&lt;").Replace(">", "&gt;");

        return sanitized;
    }

    public string SanitizeString(string input, StringSanitizationOptions options)
    {
        if (string.IsNullOrEmpty(input))
        {
            return input;
        }

        var sanitized = input;

        if (options.RemoveHtml)
        {
            sanitized = SanitizeHtml(sanitized);
        }

        if (options.TrimWhitespace)
        {
            sanitized = sanitized.Trim();
        }

        if (options.RemoveControlCharacters)
        {
            sanitized = Regex.Replace(sanitized, @"[\x00-\x1F\x7F]", string.Empty);
        }

        if (options.NormalizeWhitespace)
        {
            sanitized = Regex.Replace(sanitized, @"\s+", " ");
        }

        if (options.MaxLength.HasValue && sanitized.Length > options.MaxLength.Value)
        {
            sanitized = sanitized.Substring(0, options.MaxLength.Value);
        }

        return sanitized;
    }

    public bool ContainsSqlInjection(string input)
    {
        if (string.IsNullOrEmpty(input))
        {
            return false;
        }

        return SqlInjectionRegex.IsMatch(input);
    }

    public bool ContainsXssAttempt(string input)
    {
        if (string.IsNullOrEmpty(input))
        {
            return false;
        }

        return XssRegex.IsMatch(input);
    }

    public bool ContainsMaliciousContent(string input)
    {
        if (string.IsNullOrEmpty(input))
        {
            return false;
        }

        var maliciousPatterns = new[]
        {
            @"\.\.\/",  // Directory traversal
            @"\/etc\/passwd",  // Unix password file
            @"cmd\.exe",  // Windows command execution
            @"powershell",  // PowerShell execution
            @"eval\s*\(",  // Code evaluation
            @"exec\s*\(",  // Code execution
            @"system\s*\(",  // System calls
        };

        return maliciousPatterns.Any(pattern => Regex.IsMatch(input, pattern, RegexOptions.IgnoreCase));
    }

    public ValidationResult ValidateFileUpload(FileUploadInfo fileInfo)
    {
        var errors = new List<string>();

        if (fileInfo == null)
        {
            return ValidationResult.Failed("File information is required");
        }

        // File size validation
        if (fileInfo.Size > _config.MaxFileSize)
        {
            errors.Add($"File size exceeds maximum allowed size of {_config.MaxFileSize / (1024 * 1024)} MB");
        }

        // File type validation
        if (_config.AllowedFileTypes?.Any() == true)
        {
            var extension = Path.GetExtension(fileInfo.FileName)?.ToLowerInvariant();
            if (string.IsNullOrEmpty(extension) || !_config.AllowedFileTypes.Contains(extension))
            {
                errors.Add($"File type '{extension}' is not allowed");
            }
        }

        // Filename validation
        var filenameValidation = ValidateString(fileInfo.FileName, new StringValidationOptions
        {
            Required = true,
            MaxLength = _config.MaxFilenameLength,
            CheckForMaliciousContent = true
        });

        if (!filenameValidation.IsValid)
        {
            errors.AddRange(filenameValidation.Errors);
        }

        // Content type validation
        if (!string.IsNullOrEmpty(fileInfo.ContentType))
        {
            if (_config.AllowedContentTypes?.Any() == true && !_config.AllowedContentTypes.Contains(fileInfo.ContentType))
            {
                errors.Add($"Content type '{fileInfo.ContentType}' is not allowed");
            }
        }

        return errors.Any() ? ValidationResult.Failed(errors) : ValidationResult.Success();
    }

    public ValidationResult ValidateJsonInput(string json, JsonValidationOptions options)
    {
        if (string.IsNullOrWhiteSpace(json) && options.Required)
        {
            return ValidationResult.Failed("JSON input is required");
        }

        if (string.IsNullOrWhiteSpace(json))
        {
            return ValidationResult.Success();
        }

        try
        {
            var jsonDocument = System.Text.Json.JsonDocument.Parse(json);
            
            if (options.MaxDepth.HasValue)
            {
                var depth = CalculateJsonDepth(jsonDocument.RootElement);
                if (depth > options.MaxDepth.Value)
                {
                    return ValidationResult.Failed($"JSON depth exceeds maximum allowed depth of {options.MaxDepth.Value}");
                }
            }

            if (options.MaxSize.HasValue && json.Length > options.MaxSize.Value)
            {
                return ValidationResult.Failed($"JSON size exceeds maximum allowed size of {options.MaxSize.Value} characters");
            }

            return ValidationResult.Success();
        }
        catch (System.Text.Json.JsonException ex)
        {
            _logger.LogWarning(ex, "Invalid JSON input received");
            return ValidationResult.Failed("Invalid JSON format");
        }
    }

    private ValidationResult ValidateObjectForSecurity(object input)
    {
        var errors = new List<string>();
        var properties = input.GetType().GetProperties();

        foreach (var property in properties)
        {
            if (property.PropertyType == typeof(string))
            {
                var value = property.GetValue(input) as string;
                if (!string.IsNullOrEmpty(value))
                {
                    if (ContainsSqlInjection(value))
                    {
                        errors.Add($"Property '{property.Name}' contains potential SQL injection");
                        _logger.LogWarning("SQL injection attempt in property {PropertyName}: {Value}", property.Name, value);
                    }

                    if (ContainsXssAttempt(value))
                    {
                        errors.Add($"Property '{property.Name}' contains potential XSS attempt");
                        _logger.LogWarning("XSS attempt in property {PropertyName}: {Value}", property.Name, value);
                    }
                }
            }
        }

        return errors.Any() ? ValidationResult.Failed(errors) : ValidationResult.Success();
    }

    private int CalculateJsonDepth(System.Text.Json.JsonElement element, int currentDepth = 1)
    {
        var maxDepth = currentDepth;

        switch (element.ValueKind)
        {
            case System.Text.Json.JsonValueKind.Object:
                foreach (var property in element.EnumerateObject())
                {
                    var depth = CalculateJsonDepth(property.Value, currentDepth + 1);
                    maxDepth = Math.Max(maxDepth, depth);
                }
                break;

            case System.Text.Json.JsonValueKind.Array:
                foreach (var item in element.EnumerateArray())
                {
                    var depth = CalculateJsonDepth(item, currentDepth + 1);
                    maxDepth = Math.Max(maxDepth, depth);
                }
                break;
        }

        return maxDepth;
    }
}

// Supporting classes and options
public class ValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();

    public static ValidationResult Success()
    {
        return new ValidationResult { IsValid = true };
    }

    public static ValidationResult Failed(string error)
    {
        return new ValidationResult { IsValid = false, Errors = new List<string> { error } };
    }

    public static ValidationResult Failed(List<string> errors)
    {
        return new ValidationResult { IsValid = false, Errors = errors };
    }
}

public class StringValidationOptions
{
    public bool Required { get; set; } = false;
    public int? MinLength { get; set; }
    public int? MaxLength { get; set; }
    public string? Pattern { get; set; }
    public bool CheckForSqlInjection { get; set; } = true;
    public bool CheckForXss { get; set; } = true;
    public bool CheckForMaliciousContent { get; set; } = true;
    public HashSet<char>? AllowedCharacters { get; set; }
}

public class NumericValidationOptions
{
    public bool Required { get; set; } = false;
    public bool IsInteger { get; set; } = false;
    public decimal? MinValue { get; set; }
    public decimal? MaxValue { get; set; }
}

public class DateValidationOptions
{
    public bool Required { get; set; } = false;
    public DateTime? MinDate { get; set; }
    public DateTime? MaxDate { get; set; }
    public bool FutureOnly { get; set; } = false;
    public bool PastOnly { get; set; } = false;
}

public class StringSanitizationOptions
{
    public bool RemoveHtml { get; set; } = true;
    public bool TrimWhitespace { get; set; } = true;
    public bool RemoveControlCharacters { get; set; } = true;
    public bool NormalizeWhitespace { get; set; } = true;
    public int? MaxLength { get; set; }
}

public class JsonValidationOptions
{
    public bool Required { get; set; } = false;
    public int? MaxDepth { get; set; } = 10;
    public int? MaxSize { get; set; } = 1024 * 1024; // 1MB
}

public class FileUploadInfo
{
    public string FileName { get; set; } = string.Empty;
    public long Size { get; set; }
    public string? ContentType { get; set; }
    public byte[]? Content { get; set; }
}

public class ValidationConfiguration
{
    public int MaxEmailLength { get; set; } = 254;
    public int MaxUrlLength { get; set; } = 2048;
    public long MaxFileSize { get; set; } = 10 * 1024 * 1024; // 10MB
    public int MaxFilenameLength { get; set; } = 255;
    public List<string> AllowedFileTypes { get; set; } = new() { ".pdf", ".doc", ".docx", ".jpg", ".jpeg", ".png", ".gif" };
    public List<string> AllowedContentTypes { get; set; } = new() { "application/pdf", "application/msword", "image/jpeg", "image/png", "image/gif" };
}
