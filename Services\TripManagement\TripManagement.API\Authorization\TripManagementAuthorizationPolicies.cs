using Microsoft.AspNetCore.Authorization;

namespace TripManagement.API.Authorization;

public static class TripManagementAuthorizationPolicies
{
    public const string AdminOnly = "AdminOnly";
    public const string CarrierOrAdmin = "CarrierOrAdmin";
    public const string DriverOrCarrierOrAdmin = "DriverOrCarrierOrAdmin";
    public const string BrokerOrCarrierOrAdmin = "BrokerOrCarrierOrAdmin";
    public const string AllRoles = "AllRoles";
    public const string TripAccess = "TripAccess";
    public const string VehicleManagement = "VehicleManagement";
    public const string DriverManagement = "DriverManagement";
    public const string ExceptionManagement = "ExceptionManagement";
    public const string MonitoringAccess = "MonitoringAccess";

    public static void ConfigurePolicies(AuthorizationOptions options)
    {
        // Basic role-based policies
        options.AddPolicy(AdminOnly, policy =>
            policy.RequireRole("Admin"));

        options.AddPolicy(CarrierOrAdmin, policy =>
            policy.RequireRole("Admin", "Carrier"));

        options.AddPolicy(DriverOrCarrierOrAdmin, policy =>
            policy.RequireRole("Admin", "Carrier", "Driver"));

        options.AddPolicy(BrokerOrCarrierOrAdmin, policy =>
            policy.RequireRole("Admin", "Carrier", "Broker"));

        options.AddPolicy(AllRoles, policy =>
            policy.RequireRole("Admin", "Carrier", "Driver", "Broker"));

        // Feature-specific policies
        options.AddPolicy(TripAccess, policy =>
        {
            policy.RequireRole("Admin", "Carrier", "Driver", "Broker");
            policy.RequireClaim("permissions", "trip:read");
        });

        options.AddPolicy(VehicleManagement, policy =>
        {
            policy.RequireRole("Admin", "Carrier");
            policy.RequireClaim("permissions", "vehicle:manage");
        });

        options.AddPolicy(DriverManagement, policy =>
        {
            policy.RequireRole("Admin", "Carrier");
            policy.RequireClaim("permissions", "driver:manage");
        });

        options.AddPolicy(ExceptionManagement, policy =>
        {
            policy.RequireRole("Admin", "Carrier", "Driver");
            policy.RequireClaim("permissions", "exception:manage");
        });

        options.AddPolicy(MonitoringAccess, policy =>
        {
            policy.RequireRole("Admin", "Carrier");
            policy.RequireClaim("permissions", "monitoring:read");
        });
    }
}

public static class TripManagementClaims
{
    public const string TripRead = "trip:read";
    public const string TripWrite = "trip:write";
    public const string TripDelete = "trip:delete";
    
    public const string VehicleRead = "vehicle:read";
    public const string VehicleWrite = "vehicle:write";
    public const string VehicleManage = "vehicle:manage";
    
    public const string DriverRead = "driver:read";
    public const string DriverWrite = "driver:write";
    public const string DriverManage = "driver:manage";
    
    public const string ExceptionRead = "exception:read";
    public const string ExceptionWrite = "exception:write";
    public const string ExceptionManage = "exception:manage";
    
    public const string MonitoringRead = "monitoring:read";
    public const string MonitoringWrite = "monitoring:write";
    
    public const string AdminAccess = "admin:access";
}

public static class TripManagementRoles
{
    public const string Admin = "Admin";
    public const string Carrier = "Carrier";
    public const string Driver = "Driver";
    public const string Broker = "Broker";
    public const string System = "System";
    
    public static readonly string[] AllRoles = { Admin, Carrier, Driver, Broker, System };
    
    public static readonly Dictionary<string, string[]> RolePermissions = new()
    {
        [Admin] = new[]
        {
            TripManagementClaims.TripRead,
            TripManagementClaims.TripWrite,
            TripManagementClaims.TripDelete,
            TripManagementClaims.VehicleRead,
            TripManagementClaims.VehicleWrite,
            TripManagementClaims.VehicleManage,
            TripManagementClaims.DriverRead,
            TripManagementClaims.DriverWrite,
            TripManagementClaims.DriverManage,
            TripManagementClaims.ExceptionRead,
            TripManagementClaims.ExceptionWrite,
            TripManagementClaims.ExceptionManage,
            TripManagementClaims.MonitoringRead,
            TripManagementClaims.MonitoringWrite,
            TripManagementClaims.AdminAccess
        },
        [Carrier] = new[]
        {
            TripManagementClaims.TripRead,
            TripManagementClaims.TripWrite,
            TripManagementClaims.VehicleRead,
            TripManagementClaims.VehicleWrite,
            TripManagementClaims.VehicleManage,
            TripManagementClaims.DriverRead,
            TripManagementClaims.DriverWrite,
            TripManagementClaims.DriverManage,
            TripManagementClaims.ExceptionRead,
            TripManagementClaims.ExceptionWrite,
            TripManagementClaims.ExceptionManage,
            TripManagementClaims.MonitoringRead
        },
        [Driver] = new[]
        {
            TripManagementClaims.TripRead,
            TripManagementClaims.TripWrite,
            TripManagementClaims.VehicleRead,
            TripManagementClaims.DriverRead,
            TripManagementClaims.ExceptionRead,
            TripManagementClaims.ExceptionWrite
        },
        [Broker] = new[]
        {
            TripManagementClaims.TripRead,
            TripManagementClaims.VehicleRead,
            TripManagementClaims.DriverRead,
            TripManagementClaims.ExceptionRead
        },
        [System] = new[]
        {
            TripManagementClaims.TripRead,
            TripManagementClaims.TripWrite,
            TripManagementClaims.VehicleRead,
            TripManagementClaims.DriverRead,
            TripManagementClaims.ExceptionRead,
            TripManagementClaims.ExceptionWrite,
            TripManagementClaims.MonitoringRead,
            TripManagementClaims.MonitoringWrite
        }
    };
}
