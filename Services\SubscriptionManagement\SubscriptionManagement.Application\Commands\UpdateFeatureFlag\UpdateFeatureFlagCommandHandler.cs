using MediatR;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Domain.Interfaces;
using SubscriptionManagement.Domain.Exceptions;
using SubscriptionManagement.Domain.Enums;
using Shared.Messaging;

namespace SubscriptionManagement.Application.Commands.UpdateFeatureFlag
{
    public class UpdateFeatureFlagCommandHandler : IRequestHandler<UpdateFeatureFlagCommand, bool>
    {
        private readonly IFeatureFlagRepository _featureFlagRepository;
        private readonly IMessageBroker _messageBroker;
        private readonly ILogger<UpdateFeatureFlagCommandHandler> _logger;

        public UpdateFeatureFlagCommandHandler(
            IFeatureFlagRepository featureFlagRepository,
            IMessageBroker messageBroker,
            ILogger<UpdateFeatureFlagCommandHandler> logger)
        {
            _featureFlagRepository = featureFlagRepository;
            _messageBroker = messageBroker;
            _logger = logger;
        }

        public async Task<bool> Handle(UpdateFeatureFlagCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Updating feature flag {FeatureFlagId}", request.FeatureFlagId);

            // Get feature flag
            var featureFlag = await _featureFlagRepository.GetByIdAsync(request.FeatureFlagId);
            if (featureFlag == null)
            {
                throw new SubscriptionDomainException("Feature flag not found");
            }

            var changes = new List<string>();

            // Update name
            if (!string.IsNullOrEmpty(request.Name) && request.Name != featureFlag.Name)
            {
                featureFlag.UpdateName(request.Name);
                changes.Add($"Name: {featureFlag.Name} -> {request.Name}");
            }

            // Update description
            if (!string.IsNullOrEmpty(request.Description) && request.Description != featureFlag.Description)
            {
                featureFlag.UpdateDescription(request.Description);
                changes.Add($"Description updated");
            }

            // Update enabled status
            if (request.IsEnabled.HasValue && request.IsEnabled.Value != featureFlag.IsEnabled)
            {
                if (request.IsEnabled.Value)
                {
                    featureFlag.Enable();
                }
                else
                {
                    featureFlag.Disable();
                }
                changes.Add($"Enabled: {featureFlag.IsEnabled} -> {request.IsEnabled.Value}");
            }

            // Update status
            if (request.Status.HasValue && request.Status.Value != featureFlag.Status)
            {
                featureFlag.SetStatus(request.Status.Value);
                changes.Add($"Status: {featureFlag.Status} -> {request.Status.Value}");
            }

            // Update rollout percentage
            if (request.RolloutPercentage.HasValue)
            {
                featureFlag.SetRolloutPercentage(request.RolloutPercentage.Value);
                changes.Add($"Rollout percentage: {request.RolloutPercentage.Value}%");
            }

            // Update default value
            if (request.DefaultValue != null)
            {
                featureFlag.SetDefaultValue(request.DefaultValue);
                changes.Add($"Default value updated");
            }

            // Update A/B test configuration
            if (featureFlag.Type == FeatureFlagType.ABTest && 
                !string.IsNullOrEmpty(request.ABTestConfiguration))
            {
                featureFlag.ConfigureABTest(request.ABTestConfiguration, request.Variants ?? "[]");
                changes.Add("A/B test configuration updated");
            }

            // Update activation period
            if (request.StartDate.HasValue || request.EndDate.HasValue)
            {
                featureFlag.SetActivationPeriod(request.StartDate, request.EndDate);
                changes.Add("Activation period updated");
            }

            // Update configuration with target user types
            if (!string.IsNullOrEmpty(request.TargetUserTypes))
            {
                try
                {
                    var userTypes = System.Text.Json.JsonSerializer.Deserialize<string[]>(request.TargetUserTypes);
                    featureFlag.UpdateConfiguration(new Dictionary<string, object>
                    {
                        ["target_user_types"] = userTypes
                    });
                    changes.Add("Target user types updated");
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to parse target user types: {TargetUserTypes}", request.TargetUserTypes);
                }
            }

            // Save changes
            await _featureFlagRepository.UpdateAsync(featureFlag);

            // Publish update event
            await _messageBroker.PublishAsync("feature_flag.updated", new
            {
                FeatureFlagId = featureFlag.Id,
                Key = featureFlag.Key,
                Name = featureFlag.Name,
                Changes = changes,
                UpdateReason = request.UpdateReason,
                UpdatedAt = DateTime.UtcNow
            });

            _logger.LogInformation("Successfully updated feature flag {FeatureFlagId}. Changes: {Changes}", 
                request.FeatureFlagId, string.Join(", ", changes));

            return true;
        }
    }
}
