using Shared.Domain.Common;
using Microsoft.Extensions.Logging;
using NetworkFleetManagement.Domain.Interfaces;
using Shared.Domain.Common;
using Shared.Messaging;
using NetworkFleetManagement.Domain.Repositories;

namespace NetworkFleetManagement.Application.Commands.VehicleTypeMaster;

/// <summary>
/// Handler for deleting a vehicle type master
/// </summary>
public class DeleteVehicleTypeMasterCommandHandler : IRequestHandler<DeleteVehicleTypeMasterCommand, bool>
{
    private readonly IVehicleTypeMasterRepository _repository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<DeleteVehicleTypeMasterCommandHandler> _logger;

    public DeleteVehicleTypeMasterCommandHandler(
        IVehicleTypeMasterRepository repository,
        IUnitOfWork unitOfWork,
        IMessageBroker messageBroker,
        ILogger<DeleteVehicleTypeMasterCommandHandler> logger)
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task<bool> Handle(DeleteVehicleTypeMasterCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Deleting vehicle type master with ID: {Id}", request.Id);

            // Get existing entity
            var vehicleTypeMaster = await _repository.GetByIdAsync(request.Id, cancellationToken);
            if (vehicleTypeMaster == null)
            {
                _logger.LogWarning("Vehicle type master with ID: {Id} not found", request.Id);
                return false;
            }

            // Check if it's being used (this would require additional logic to check references)
            // For now, we'll allow deletion but in production you might want to check for references

            // Delete from repository
            _repository.Delete(vehicleTypeMaster);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Publish integration event
            await _messageBroker.PublishAsync("vehicletypemaster.deleted", new
            {
                Id = vehicleTypeMaster.Id,
                Code = vehicleTypeMaster.Code,
                Name = vehicleTypeMaster.Name,
                Category = vehicleTypeMaster.Category,
                DeletedAt = DateTime.UtcNow
            }, cancellationToken);

            _logger.LogInformation("Successfully deleted vehicle type master with ID: {Id}", request.Id);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting vehicle type master with ID: {Id}", request.Id);
            throw;
        }
    }
}

