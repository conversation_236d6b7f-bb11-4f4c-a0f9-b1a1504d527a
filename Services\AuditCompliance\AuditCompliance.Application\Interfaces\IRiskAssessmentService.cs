using AuditCompliance.Application.DTOs.Risk;
using Shared.Domain.Common;

namespace AuditCompliance.Application.Interfaces;

/// <summary>
/// Service for automated risk assessment and scoring
/// </summary>
public interface IRiskAssessmentService
{
    /// <summary>
    /// Calculate risk score for an entity
    /// </summary>
    Task<RiskScoreDto> CalculateRiskScoreAsync(
        RiskAssessmentRequestDto request,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get risk assessment for an entity
    /// </summary>
    Task<RiskAssessmentDto?> GetRiskAssessmentAsync(
        Guid entityId,
        string entityType,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Create or update risk assessment
    /// </summary>
    Task<Guid> CreateOrUpdateRiskAssessmentAsync(
        CreateRiskAssessmentDto assessmentDto,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get risk factors configuration
    /// </summary>
    Task<List<RiskFactorDto>> GetRiskFactorsAsync(
        string? category = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Create or update risk factor
    /// </summary>
    Task<Guid> CreateOrUpdateRiskFactorAsync(
        CreateRiskFactorDto factorDto,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete risk factor
    /// </summary>
    Task<bool> DeleteRiskFactorAsync(
        Guid factorId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get risk thresholds configuration
    /// </summary>
    Task<List<RiskThresholdDto>> GetRiskThresholdsAsync(
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Update risk thresholds
    /// </summary>
    Task<bool> UpdateRiskThresholdsAsync(
        List<RiskThresholdDto> thresholds,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get risk mitigation recommendations
    /// </summary>
    Task<List<RiskMitigationDto>> GetRiskMitigationRecommendationsAsync(
        Guid entityId,
        string entityType,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Create risk mitigation plan
    /// </summary>
    Task<Guid> CreateRiskMitigationPlanAsync(
        CreateRiskMitigationPlanDto planDto,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Update risk mitigation plan
    /// </summary>
    Task<bool> UpdateRiskMitigationPlanAsync(
        Guid planId,
        UpdateRiskMitigationPlanDto planDto,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get risk mitigation plan
    /// </summary>
    Task<RiskMitigationPlanDto?> GetRiskMitigationPlanAsync(
        Guid planId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get risk trends for an entity
    /// </summary>
    Task<List<RiskTrendDto>> GetRiskTrendsAsync(
        Guid entityId,
        string entityType,
        DateTime fromDate,
        DateTime toDate,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get risk heat map data
    /// </summary>
    Task<RiskHeatMapDto> GetRiskHeatMapAsync(
        RiskHeatMapRequestDto request,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Perform bulk risk assessment
    /// </summary>
    Task<BulkRiskAssessmentResultDto> PerformBulkRiskAssessmentAsync(
        BulkRiskAssessmentRequestDto request,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get risk assessment summary
    /// </summary>
    Task<RiskAssessmentSummaryDto> GetRiskAssessmentSummaryAsync(
        Guid? organizationId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate risk assessment configuration
    /// </summary>
    Task<RiskConfigurationValidationResult> ValidateRiskConfigurationAsync(
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get risk assessment history
    /// </summary>
    Task<List<RiskAssessmentHistoryDto>> GetRiskAssessmentHistoryAsync(
        Guid entityId,
        string entityType,
        int pageSize = 50,
        int pageNumber = 1,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Export risk assessment data
    /// </summary>
    Task<Guid> ExportRiskAssessmentDataAsync(
        ExportRiskDataRequestDto request,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Schedule automated risk assessment
    /// </summary>
    Task<Guid> ScheduleAutomatedRiskAssessmentAsync(
        ScheduleRiskAssessmentDto scheduleDto,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Process scheduled risk assessments
    /// </summary>
    Task ProcessScheduledRiskAssessmentsAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface for risk scoring engine
/// </summary>
public interface IRiskScoringEngine
{
    /// <summary>
    /// Calculate risk score using Shared.Domain.Common;

    /// <summary>
    /// Evaluate individual risk factor
    /// </summary>
    Task<RiskFactorEvaluationResult> EvaluateRiskFactorAsync(
        RiskFactorDto factor,
        Dictionary<string, object> entityData,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get risk level from score
    /// </summary>
    string GetRiskLevel(float riskScore, List<RiskThresholdDto> thresholds);

    /// <summary>
    /// Calculate weighted risk score
    /// </summary>
    float CalculateWeightedScore(List<RiskFactorEvaluationResult> evaluations);
}

/// <summary>
/// Interface for risk mitigation engine
/// </summary>
public interface IRiskMitigationEngine
{
    /// <summary>
    /// Generate mitigation recommendations
    /// </summary>
    Task<List<RiskMitigationDto>> GenerateMitigationRecommendationsAsync(
        RiskAssessmentDto assessment,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Prioritize mitigation actions
    /// </summary>
    Task<List<RiskMitigationDto>> PrioritizeMitigationActionsAsync(
        List<RiskMitigationDto> mitigations,
        RiskAssessmentDto assessment,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Calculate mitigation impact
    /// </summary>
    Task<MitigationImpactDto> CalculateMitigationImpactAsync(
        RiskMitigationDto mitigation,
        RiskAssessmentDto assessment,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get mitigation templates
    /// </summary>
    Task<List<MitigationTemplateDto>> GetMitigationTemplatesAsync(
        string riskCategory,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Risk factor evaluation result
/// </summary>
public class RiskFactorEvaluationResult
{
    public Guid FactorId { get; set; }
    public string FactorName { get; set; } = string.Empty;
    public float Score { get; set; }
    public float Weight { get; set; }
    public float WeightedScore { get; set; }
    public string Evaluation { get; set; } = string.Empty;
    public Dictionary<string, object> Details { get; set; } = new();
}

/// <summary>
/// Risk configuration validation result
/// </summary>
public class RiskConfigurationValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
}

/// <summary>
/// Mitigation impact DTO
/// </summary>
public class MitigationImpactDto
{
    public float RiskReduction { get; set; }
    public float CostEstimate { get; set; }
    public int ImplementationTimeWeeks { get; set; }
    public string EffectivenessLevel { get; set; } = string.Empty;
    public List<string> Dependencies { get; set; } = new();
    public Dictionary<string, object> ImpactMetrics { get; set; } = new();
}

/// <summary>
/// Mitigation template DTO
/// </summary>
public class MitigationTemplateDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string RiskType { get; set; } = string.Empty;
    public List<string> Actions { get; set; } = new();
    public int EstimatedTimeWeeks { get; set; }
    public float EstimatedCost { get; set; }
    public float EffectivenessRating { get; set; }
    public List<string> Prerequisites { get; set; } = new();
    public Dictionary<string, object> Parameters { get; set; } = new();
}

