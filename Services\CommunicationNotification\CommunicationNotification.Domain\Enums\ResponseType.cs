namespace CommunicationNotification.Domain.Enums;

/// <summary>
/// Represents the type of chatbot response
/// </summary>
public enum ResponseType
{
    /// <summary>
    /// Plain text response
    /// </summary>
    Text = 0,
    
    /// <summary>
    /// Rich text with formatting
    /// </summary>
    RichText = 1,
    
    /// <summary>
    /// Quick reply buttons
    /// </summary>
    QuickReply = 2,
    
    /// <summary>
    /// Card with image and buttons
    /// </summary>
    Card = 3,
    
    /// <summary>
    /// Carousel of cards
    /// </summary>
    Carousel = 4,
    
    /// <summary>
    /// Image response
    /// </summary>
    Image = 5,
    
    /// <summary>
    /// Audio response
    /// </summary>
    Audio = 6,
    
    /// <summary>
    /// Video response
    /// </summary>
    Video = 7,
    
    /// <summary>
    /// File attachment
    /// </summary>
    File = 8,
    
    /// <summary>
    /// Location sharing
    /// </summary>
    Location = 9,
    
    /// <summary>
    /// Custom response type
    /// </summary>
    Custom = 10
}
