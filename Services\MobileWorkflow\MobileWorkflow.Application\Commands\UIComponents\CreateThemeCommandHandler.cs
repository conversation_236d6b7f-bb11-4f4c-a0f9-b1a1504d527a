using System;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.Extensions.Logging;
using MobileWorkflow.Application.DTOs;
using MobileWorkflow.Application.Services;


namespace MobileWorkflow.Application.Commands.UIComponents
{
    public class CreateThemeCommandHandler : IRequestHandler<CreateThemeCommand, ThemeDto>
    {
        private readonly ICrossPlatformUIService _uiService;
        private readonly ILogger<CreateThemeCommandHandler> _logger;

        public CreateThemeCommandHandler(
            ICrossPlatformUIService uiService,
            ILogger<CreateThemeCommandHandler> logger)
        {
            _uiService = uiService;
            _logger = logger;
        }

        public async Task<ThemeDto> Handle(CreateThemeCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Creating theme {Name} version {Version}",
                    request.Name, request.Version);

                var createRequest = new CreateThemeRequest
                {
                    Name = request.Name,
                    DisplayName = request.DisplayName,
                    Description = request.Description,
                    Version = request.Version,
                    SupportedPlatforms = request.SupportedPlatforms,
                    ColorPalette = request.ColorPalette,
                    Typography = request.Typography,
                    Spacing = request.Spacing,
                    ComponentStyles = request.ComponentStyles,
                    PlatformSpecific = request.PlatformSpecific,
                    DarkModeOverrides = request.DarkModeOverrides,
                    Tags = request.Tags,
                    CreatedBy = request.CreatedBy,
                    IsDefault = request.IsDefault,
                    IsPublic = request.IsPublic
                };

                var result = await _uiService.CreateThemeAsync(createRequest, cancellationToken);

                _logger.LogInformation("Successfully created theme {ThemeId}", result.Id);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating theme {Name}", request.Name);
                throw;
            }
        }
    }
}
