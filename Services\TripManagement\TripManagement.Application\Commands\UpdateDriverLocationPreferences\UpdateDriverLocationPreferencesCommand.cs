using MediatR;

namespace TripManagement.Application.Commands.UpdateDriverLocationPreferences;

public class UpdateDriverLocationPreferencesCommand : IRequest<UpdateDriverLocationPreferencesResponse>
{
    public Guid DriverId { get; set; }
    public bool LocationSharingEnabled { get; set; }
    public bool ManualToggleAllowed { get; set; } = true;
    public int LocationUpdateIntervalSeconds { get; set; } = 30;
    public bool ShowGpsTimestamp { get; set; } = true;
    public bool HighAccuracyMode { get; set; } = false;
    public bool BackgroundLocationEnabled { get; set; } = true;
    public bool GeofenceAlertsEnabled { get; set; } = true;
    public double LocationAccuracyThresholdMeters { get; set; } = 10.0;
    public Dictionary<string, object> AdditionalSettings { get; set; } = new();
}

public class UpdateDriverLocationPreferencesResponse
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DriverLocationPreferences? UpdatedPreferences { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class DriverLocationPreferences
{
    public Guid DriverId { get; set; }
    public bool LocationSharingEnabled { get; set; }
    public bool ManualToggleAllowed { get; set; }
    public int LocationUpdateIntervalSeconds { get; set; }
    public bool ShowGpsTimestamp { get; set; }
    public bool HighAccuracyMode { get; set; }
    public bool BackgroundLocationEnabled { get; set; }
    public bool GeofenceAlertsEnabled { get; set; }
    public double LocationAccuracyThresholdMeters { get; set; }
    public DateTime LastLocationUpdate { get; set; }
    public string? LastKnownLocation { get; set; }
    public double? LastLatitude { get; set; }
    public double? LastLongitude { get; set; }
    public double? LastAccuracy { get; set; }
    public DateTime? LastGpsTimestamp { get; set; }
    public Dictionary<string, object> AdditionalSettings { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}
