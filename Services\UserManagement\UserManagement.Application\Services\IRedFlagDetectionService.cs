using UserManagement.Domain.Entities;

namespace UserManagement.Application.Services;

/// <summary>
/// Service for detecting red flags in user feedback
/// </summary>
public interface IRedFlagDetectionService
{
    /// <summary>
    /// Analyzes feedback for potential red flags
    /// </summary>
    /// <param name="feedback">The feedback to analyze</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of detected red flags</returns>
    Task<IEnumerable<FeedbackRedFlag>> AnalyzeFeedbackAsync(Feedback feedback, CancellationToken cancellationToken = default);

    /// <summary>
    /// Analyzes multiple feedbacks for red flags
    /// </summary>
    /// <param name="feedbacks">The feedbacks to analyze</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of detected red flags</returns>
    Task<IEnumerable<FeedbackRedFlag>> AnalyzeFeedbacksAsync(IEnumerable<Feedback> feedbacks, CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates red flag detection rules
    /// </summary>
    /// <param name="rules">New detection rules</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task UpdateDetectionRulesAsync(IEnumerable<RedFlagRule> rules, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets current red flag detection rules
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Current detection rules</returns>
    Task<IEnumerable<RedFlagRule>> GetDetectionRulesAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Represents a detected red flag in feedback
/// </summary>
public class FeedbackRedFlag
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public Guid FeedbackId { get; set; }
    public string FlagType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int Severity { get; set; } // 1-10 scale
    public string DetectedText { get; set; } = string.Empty;
    public DateTime DetectedAt { get; set; } = DateTime.UtcNow;
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Represents a red flag detection rule
/// </summary>
public class RedFlagRule
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Pattern { get; set; } = string.Empty; // Regex pattern or keyword
    public string FlagType { get; set; } = string.Empty;
    public int Severity { get; set; }
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime? UpdatedAt { get; set; }
}
