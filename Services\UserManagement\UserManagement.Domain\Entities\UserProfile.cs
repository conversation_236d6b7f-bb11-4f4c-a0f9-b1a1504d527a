using Shared.Domain.Common;
using UserManagement.Domain.Exceptions;

namespace UserManagement.Domain.Entities
{
    public enum UserType
    {
        Admin,
        TransportCompany,
        Broker,
        Carrier,
        Driver,
        Shipper
    }

    public enum ProfileStatus
    {
        Incomplete,
        Complete,
        UnderReview,
        Approved,
        Rejected
    }

    public class UserProfile : AggregateRoot
    {
        public Guid UserId { get; private set; }
        public UserType UserType { get; private set; }
        public ProfileStatus Status { get; private set; }
        public string FirstName { get; private set; }
        public string LastName { get; private set; }
        public string Email { get; private set; }
        public string PhoneNumber { get; private set; }
        public string CompanyName { get; private set; }
        public string GstNumber { get; private set; }
        public string PanNumber { get; private set; }
        public string AadharNumber { get; private set; }
        public string LicenseNumber { get; private set; }
        public string Address { get; private set; }
        public string City { get; private set; }
        public string State { get; private set; }
        public string Country { get; private set; }
        public string PostalCode { get; private set; }
        public DateTime? ApprovedAt { get; private set; }
        public Guid? ApprovedBy { get; private set; }
        public string RejectionReason { get; private set; }
        public DateTime? RejectedAt { get; private set; }
        public bool IsComplete => IsProfileComplete();
        public bool IsActive { get; private set; } = true;

        // Extended properties for advanced features
        public string Region { get; private set; }
        public DateTime? LastLoginAt { get; private set; }
        public string LastLoginIp { get; private set; }
        public string KycStatus { get; private set; }
        public DateTime? KycSubmittedAt { get; private set; }
        public DateTime? KycApprovedAt { get; private set; }
        public DateTime? LastKycReminderSent { get; private set; }
        public int? KycReminderCount { get; private set; }
        public bool? KycRequiresResubmission { get; private set; }

        // Subscription properties
        public string SubscriptionPlan { get; private set; }
        public DateTime? SubscriptionStartDate { get; private set; }
        public DateTime? SubscriptionExpiryDate { get; private set; }
        public bool? AutoRenewalEnabled { get; private set; }
        public decimal? SubscriptionMonthlyFee { get; private set; }
        public int? SubscriptionFeatureLimits { get; private set; }

        // Activity properties
        public int? RfqCount { get; private set; }
        public int? OrderCount { get; private set; }
        public int? TripCount { get; private set; }
        public decimal? TotalBusinessValue { get; private set; }

        // Language and display properties
        public string PreferredLanguage { get; private set; } = "en";
        public string FullName => $"{FirstName} {LastName}".Trim();

        // Document properties
        public int? TotalDocuments { get; private set; }
        public int? ApprovedDocuments { get; private set; }
        public int? PendingDocuments { get; private set; }
        public int? RejectedDocuments { get; private set; }
        public DateTime? LastDocumentUpload { get; private set; }

        // Account management properties
        public DateTime? LastPasswordChangeAt { get; private set; }
        public bool RequirePasswordChange { get; private set; }
        public bool IsDeactivated { get; private set; }
        public string DeactivationReason { get; private set; }
        public DateTime? DeactivatedAt { get; private set; }

        private UserProfile() { }

        public UserProfile(Guid userId, UserType userType, string email)
        {
            if (userId == Guid.Empty)
                throw new UserManagementDomainException("User ID cannot be empty");

            if (string.IsNullOrWhiteSpace(email))
                throw new UserManagementDomainException("Email cannot be empty");

            UserId = userId;
            UserType = userType;
            Email = email;
            Status = ProfileStatus.Incomplete;
            Country = "India"; // Default country
            CreatedAt = DateTime.UtcNow;
        }

        public void UpdatePersonalDetails(string firstName, string lastName, string phoneNumber)
        {
            FirstName = firstName;
            LastName = lastName;
            PhoneNumber = phoneNumber;
            UpdatedAt = DateTime.UtcNow;
            UpdateStatus();
        }

        public void UpdateCompanyDetails(string companyName, string gstNumber, string panNumber)
        {
            if (UserType != UserType.TransportCompany && UserType != UserType.Broker)
                throw new UserManagementDomainException("Company details can only be set for transport companies and brokers");

            CompanyName = companyName;
            GstNumber = gstNumber;
            PanNumber = panNumber;
            UpdatedAt = DateTime.UtcNow;
            UpdateStatus();
        }

        public void UpdateAddress(string address, string city, string state, string postalCode, string country = "India")
        {
            Address = address;
            City = city;
            State = state;
            PostalCode = postalCode;
            Country = country;
            UpdatedAt = DateTime.UtcNow;
            UpdateStatus();
        }

        public void SetAadharNumber(string aadharNumber)
        {
            AadharNumber = aadharNumber;
            UpdatedAt = DateTime.UtcNow;
            UpdateStatus();
        }

        public void SetLicenseNumber(string licenseNumber)
        {
            if (UserType != UserType.Driver)
                throw new UserManagementDomainException("License number can only be set for drivers");

            LicenseNumber = licenseNumber;
            UpdatedAt = DateTime.UtcNow;
            UpdateStatus();
        }

        public void SubmitForReview()
        {
            if (Status != ProfileStatus.Complete)
                throw new UserManagementDomainException("Profile must be complete to submit for review");

            Status = ProfileStatus.UnderReview;
            UpdatedAt = DateTime.UtcNow;
        }

        public void Approve(Guid approvedBy)
        {
            if (Status != ProfileStatus.UnderReview)
                throw new UserManagementDomainException("Profile must be under review to approve");

            Status = ProfileStatus.Approved;
            ApprovedAt = DateTime.UtcNow;
            ApprovedBy = approvedBy;
            RejectionReason = null;
            UpdatedAt = DateTime.UtcNow;
        }

        public void Reject(string reason)
        {
            if (Status != ProfileStatus.UnderReview)
                throw new UserManagementDomainException("Profile must be under review to reject");

            if (string.IsNullOrWhiteSpace(reason))
                throw new UserManagementDomainException("Rejection reason is required");

            Status = ProfileStatus.Rejected;
            RejectionReason = reason;
            RejectedAt = DateTime.UtcNow;
            UpdatedAt = DateTime.UtcNow;
        }

        private void UpdateStatus()
        {
            if (Status == ProfileStatus.Approved || Status == ProfileStatus.UnderReview)
                return;

            bool isComplete = IsProfileComplete();
            Status = isComplete ? ProfileStatus.Complete : ProfileStatus.Incomplete;
        }

        private bool IsProfileComplete()
        {
            // Basic required fields for all user types
            if (string.IsNullOrWhiteSpace(FirstName) ||
                string.IsNullOrWhiteSpace(LastName) ||
                string.IsNullOrWhiteSpace(PhoneNumber) ||
                string.IsNullOrWhiteSpace(Address) ||
                string.IsNullOrWhiteSpace(City) ||
                string.IsNullOrWhiteSpace(State) ||
                string.IsNullOrWhiteSpace(PostalCode))
            {
                return false;
            }

            // User type specific requirements
            switch (UserType)
            {
                case UserType.TransportCompany:
                case UserType.Broker:
                    return !string.IsNullOrWhiteSpace(CompanyName) &&
                           !string.IsNullOrWhiteSpace(GstNumber) &&
                           !string.IsNullOrWhiteSpace(PanNumber);

                case UserType.Driver:
                    return !string.IsNullOrWhiteSpace(LicenseNumber) &&
                           !string.IsNullOrWhiteSpace(AadharNumber);

                case UserType.Carrier:
                    return !string.IsNullOrWhiteSpace(AadharNumber);

                case UserType.Admin:
                case UserType.Shipper:
                    return true; // Basic fields are sufficient

                default:
                    return false;
            }
        }

        public string GetDisplayName()
        {
            if (!string.IsNullOrWhiteSpace(CompanyName))
                return CompanyName;

            if (!string.IsNullOrWhiteSpace(FirstName) && !string.IsNullOrWhiteSpace(LastName))
                return $"{FirstName} {LastName}";

            return Email;
        }

        // Account Management Methods
        public void Activate()
        {
            IsDeactivated = false;
            DeactivationReason = null;
            DeactivatedAt = null;
            UpdatedAt = DateTime.UtcNow;
        }

        public void Deactivate(string reason)
        {
            IsDeactivated = true;
            DeactivationReason = reason;
            DeactivatedAt = DateTime.UtcNow;
            UpdatedAt = DateTime.UtcNow;
        }

        public void Suspend(string reason)
        {
            Status = ProfileStatus.Rejected; // Using Rejected as suspended state
            RejectionReason = reason;
            UpdatedAt = DateTime.UtcNow;
        }

        public void Unsuspend()
        {
            if (Status == ProfileStatus.Rejected)
            {
                Status = ProfileStatus.Approved;
                RejectionReason = null;
                UpdatedAt = DateTime.UtcNow;
            }
        }

        public void RecordPasswordReset(Guid resetBy, bool requireChange = true)
        {
            LastPasswordChangeAt = DateTime.UtcNow;
            RequirePasswordChange = requireChange;
            UpdatedAt = DateTime.UtcNow;
        }

        // Subscription Management Methods
        public void UpdateSubscriptionPlan(string planName, DateTime startDate, DateTime expiryDate, decimal monthlyFee, int featureLimits)
        {
            SubscriptionPlan = planName;
            SubscriptionStartDate = startDate;
            SubscriptionExpiryDate = expiryDate;
            SubscriptionMonthlyFee = monthlyFee;
            SubscriptionFeatureLimits = featureLimits;
            UpdatedAt = DateTime.UtcNow;
        }

        public void EnableAutoRenewal()
        {
            AutoRenewalEnabled = true;
            UpdatedAt = DateTime.UtcNow;
        }

        public void DisableAutoRenewal()
        {
            AutoRenewalEnabled = false;
            UpdatedAt = DateTime.UtcNow;
        }

        public void ExtendSubscription(DateTime newExpiryDate)
        {
            SubscriptionExpiryDate = newExpiryDate;
            UpdatedAt = DateTime.UtcNow;
        }

        public void CancelSubscription(string reason)
        {
            SubscriptionPlan = null;
            SubscriptionExpiryDate = DateTime.UtcNow;
            AutoRenewalEnabled = false;
            UpdatedAt = DateTime.UtcNow;
        }

        // KYC Management Methods
        public void RecordKycReminderSent()
        {
            LastKycReminderSent = DateTime.UtcNow;
            KycReminderCount = (KycReminderCount ?? 0) + 1;
            UpdatedAt = DateTime.UtcNow;
        }

        public void RecordKycUploadPromptSent()
        {
            LastKycReminderSent = DateTime.UtcNow;
            UpdatedAt = DateTime.UtcNow;
        }

        public void ForceKycResubmission(string reason)
        {
            KycRequiresResubmission = true;
            KycStatus = "RequiresResubmission";
            RejectionReason = reason;
            UpdatedAt = DateTime.UtcNow;
        }

        public void UpdateKycStatus(string status)
        {
            KycStatus = status;
            if (status == "Submitted")
            {
                KycSubmittedAt = DateTime.UtcNow;
            }
            else if (status == "Approved")
            {
                KycApprovedAt = DateTime.UtcNow;
                KycRequiresResubmission = false;
            }
            UpdatedAt = DateTime.UtcNow;
        }

        // Activity Tracking Methods
        public void UpdateActivity(int? rfqCount = null, int? orderCount = null, int? tripCount = null, decimal? businessValue = null)
        {
            if (rfqCount.HasValue) RfqCount = rfqCount.Value;
            if (orderCount.HasValue) OrderCount = orderCount.Value;
            if (tripCount.HasValue) TripCount = tripCount.Value;
            if (businessValue.HasValue) TotalBusinessValue = businessValue.Value;
            UpdatedAt = DateTime.UtcNow;
        }

        public void UpdateDocumentCounts(int total, int approved, int pending, int rejected)
        {
            TotalDocuments = total;
            ApprovedDocuments = approved;
            PendingDocuments = pending;
            RejectedDocuments = rejected;
            LastDocumentUpload = DateTime.UtcNow;
            UpdatedAt = DateTime.UtcNow;
        }

        public void RecordLogin(string ipAddress)
        {
            LastLoginAt = DateTime.UtcNow;
            LastLoginIp = ipAddress;
            UpdatedAt = DateTime.UtcNow;
        }

        public void SetRegion(string region)
        {
            Region = region;
            UpdatedAt = DateTime.UtcNow;
        }

        public void UpdateLanguagePreference(string language)
        {
            if (string.IsNullOrWhiteSpace(language))
                throw new UserManagementDomainException("Language cannot be empty");

            PreferredLanguage = language;
            UpdatedAt = DateTime.UtcNow;
        }
    }
}
