using MediatR;
using Microsoft.Extensions.Logging;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Application.Interfaces.Repositories;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;
using Shared.Infrastructure.Interfaces;

namespace FinancialPayment.Application.Commands.ReleaseMilestonePayment;

public class ReleaseMilestonePaymentCommandHandler : IRequestHandler<ReleaseMilestonePaymentCommand, bool>
{
    private readonly IPaymentMilestoneRepository _milestoneRepository;
    private readonly IEscrowAccountRepository _escrowRepository;
    private readonly ISettlementRepository _settlementRepository;
    private readonly IPaymentGatewayService _paymentGatewayService;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<ReleaseMilestonePaymentCommandHandler> _logger;

    public ReleaseMilestonePaymentCommandHandler(
        IPaymentMilestoneRepository milestoneRepository,
        IEscrowAccountRepository escrowRepository,
        ISettlementRepository settlementRepository,
        IPaymentGatewayService paymentGatewayService,
        IUnitOfWork unitOfWork,
        IMessageBroker messageBroker,
        ILogger<ReleaseMilestonePaymentCommandHandler> logger)
    {
        _milestoneRepository = milestoneRepository;
        _escrowRepository = escrowRepository;
        _settlementRepository = settlementRepository;
        _paymentGatewayService = paymentGatewayService;
        _unitOfWork = unitOfWork;
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task<bool> Handle(ReleaseMilestonePaymentCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Releasing milestone payment for milestone {MilestoneId} by user {UserId}",
            request.MilestoneId, request.RequestingUserId);

        try
        {
            // Get the milestone
            var milestone = await _milestoneRepository.GetByIdAsync(request.MilestoneId, cancellationToken);
            if (milestone == null)
            {
                _logger.LogWarning("Payment milestone {MilestoneId} not found", request.MilestoneId);
                throw new ArgumentException($"Payment milestone {request.MilestoneId} not found");
            }

            // Get the escrow account
            var escrowAccount = await _escrowRepository.GetByIdAsync(request.EscrowAccountId, cancellationToken);
            if (escrowAccount == null)
            {
                _logger.LogWarning("Escrow account {EscrowAccountId} not found", request.EscrowAccountId);
                throw new ArgumentException($"Escrow account {request.EscrowAccountId} not found");
            }

            // Validate user has permission to release milestone payment
            if (escrowAccount.TransportCompanyId != request.RequestingUserId)
            {
                _logger.LogWarning("User {UserId} does not have permission to release payment for milestone {MilestoneId}",
                    request.RequestingUserId, request.MilestoneId);
                throw new UnauthorizedAccessException("You do not have permission to release payment for this milestone");
            }

            // Validate milestone can be completed
            if (!milestone.CanComplete())
            {
                _logger.LogWarning("Milestone {MilestoneId} cannot be completed", request.MilestoneId);
                throw new InvalidOperationException("Milestone cannot be completed - check completion criteria and approval status");
            }

            // Validate escrow account has sufficient funds
            if (escrowAccount.AvailableAmount.Amount < milestone.Amount.Amount)
            {
                _logger.LogWarning("Insufficient funds in escrow account {EscrowAccountId} for milestone {MilestoneId}",
                    request.EscrowAccountId, request.MilestoneId);
                throw new InvalidOperationException("Insufficient funds in escrow account");
            }

            // Complete the milestone
            milestone.Complete(request.CompletionNotes, request.RequestingUserId);

            // Create escrow transaction for the release
            var releaseTransaction = new EscrowTransaction(
                request.EscrowAccountId,
                EscrowTransactionType.Release,
                milestone.Amount,
                request.RequestingUserId,
                request.PaymentMethodId,
                $"Milestone payment release: {milestone.Name}");

            // Release funds from escrow
            escrowAccount.Release(milestone.Amount, request.RequestingUserId, $"Milestone completed: {milestone.Name}", request.PaymentMethodId);

            // Create settlement if requested
            Settlement? settlement = null;
            if (request.AutoCreateSettlement && request.Recipients.Any())
            {
                settlement = await CreateSettlement(escrowAccount, milestone, request, cancellationToken);
            }

            // Process payments to recipients
            if (request.Recipients.Any())
            {
                var convertedRecipients = request.Recipients.Select(r => new Application.DTOs.RecipientPaymentDto
                {
                    RecipientId = r.RecipientId,
                    RecipientRole = r.RecipientType,
                    Amount = r.Amount.Amount,
                    Currency = r.Amount.Currency,
                    Metadata = r.Metadata ?? new Dictionary<string, object>()
                }).ToList();
                await ProcessRecipientPayments(convertedRecipients, milestone, request, cancellationToken);
            }

            // Complete the release transaction
            releaseTransaction.MarkAsProcessed(request.PaymentMethodId);

            // Save all changes
            _milestoneRepository.Update(milestone);
            _escrowRepository.Update(escrowAccount);

            if (settlement != null)
            {
                await _settlementRepository.AddAsync(settlement);
            }

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Publish integration events
            await PublishMilestonePaymentEvents(milestone, escrowAccount, settlement, request, cancellationToken);

            _logger.LogInformation("Successfully released milestone payment for milestone {MilestoneId}",
                request.MilestoneId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error releasing milestone payment for milestone {MilestoneId}",
                request.MilestoneId);
            throw;
        }
    }

    private async Task<Settlement> CreateSettlement(
        EscrowAccount escrowAccount,
        PaymentMilestone milestone,
        ReleaseMilestonePaymentCommand request,
        CancellationToken cancellationToken)
    {
        var settlement = new Settlement(
            escrowAccount.OrderId,
            Guid.Empty, // TripId would need to be provided or retrieved
            escrowAccount.Id,
            milestone.Amount,
            $"Milestone payment settlement: {milestone.Name}");

        // Add distributions for each recipient
        foreach (var recipient in request.Recipients)
        {
            var amount = new Money(recipient.Amount.Amount, recipient.Amount.Currency);
            var participantRole = MapRecipientTypeToParticipantRole(recipient.RecipientType);
            var distributionType = MapRecipientTypeToDistributionType(recipient.RecipientType);
            settlement.AddDistribution(recipient.RecipientId, participantRole, amount, distributionType, recipient.PaymentReason);
        }

        return settlement;
    }

    private async Task ProcessRecipientPayments(
        List<Application.DTOs.RecipientPaymentDto> recipients,
        PaymentMilestone milestone,
        ReleaseMilestonePaymentCommand request,
        CancellationToken cancellationToken)
    {
        foreach (var recipient in recipients)
        {
            try
            {
                var amount = new Money(recipient.Amount, recipient.Currency);

                var paymentResult = await _paymentGatewayService.ProcessPaymentAsync(
                    recipient.RecipientId,
                    amount,
                    request.PaymentMethodId,
                    $"Milestone payment: {milestone.Name}",
                    recipient.Metadata,
                    cancellationToken);

                if (!paymentResult.IsSuccess)
                {
                    _logger.LogWarning("Payment failed for recipient {RecipientId} in milestone {MilestoneId}: {Reason}",
                        recipient.RecipientId, milestone.Id, paymentResult.ErrorMessage);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing payment for recipient {RecipientId} in milestone {MilestoneId}",
                    recipient.RecipientId, milestone.Id);
                // Continue processing other recipients
            }
        }
    }

    private async Task PublishMilestonePaymentEvents(
        PaymentMilestone milestone,
        EscrowAccount escrowAccount,
        Settlement? settlement,
        ReleaseMilestonePaymentCommand request,
        CancellationToken cancellationToken)
    {
        try
        {
            // Publish milestone completed event
            await _messageBroker.PublishAsync("milestone.payment.released", new
            {
                MilestoneId = milestone.Id,
                EscrowAccountId = escrowAccount.Id,
                OrderId = escrowAccount.OrderId,
                MilestoneName = milestone.Name,
                Amount = new
                {
                    Amount = milestone.Amount.Amount,
                    Currency = milestone.Amount.Currency
                },
                CompletedAt = milestone.CompletedAt,
                CompletedBy = request.RequestingUserId,
                CompletionNotes = request.CompletionNotes,
                SettlementId = settlement?.Id,
                Recipients = request.Recipients.Select(r => new
                {
                    RecipientId = r.RecipientId,
                    RecipientType = r.RecipientType,
                    Amount = new
                    {
                        Amount = r.Amount.Amount,
                        Currency = r.Amount.Currency
                    }
                }).ToList()
            }, cancellationToken);

            _logger.LogInformation("Successfully published milestone payment events for milestone {MilestoneId}",
                milestone.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing milestone payment events for milestone {MilestoneId}",
                milestone.Id);
            // Don't throw here as the main operation succeeded
        }
    }

    private static ParticipantRole MapRecipientTypeToParticipantRole(string recipientType)
    {
        return recipientType.ToLowerInvariant() switch
        {
            "carrier" => ParticipantRole.Carrier,
            "broker" => ParticipantRole.Broker,
            "transportcompany" => ParticipantRole.TransportCompany,
            "shipper" => ParticipantRole.Shipper,
            "admin" => ParticipantRole.Admin,
            "platform" => ParticipantRole.Platform,
            _ => ParticipantRole.Carrier // Default to Carrier
        };
    }

    private static DistributionType MapRecipientTypeToDistributionType(string recipientType)
    {
        return recipientType.ToLowerInvariant() switch
        {
            "carrier" => DistributionType.CarrierPayment,
            "broker" => DistributionType.BrokerCommission,
            "transportcompany" => DistributionType.CarrierPayment,
            "shipper" => DistributionType.Refund,
            "admin" => DistributionType.PlatformFee,
            "platform" => DistributionType.PlatformFee,
            _ => DistributionType.CarrierPayment // Default to CarrierPayment
        };
    }
}
