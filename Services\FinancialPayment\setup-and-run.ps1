# Financial & Payment Service Setup and Run Script
# This script sets up and runs the Financial & Payment Service

param(
    [string]$Environment = "Development",
    [switch]$SkipBuild = $false,
    [switch]$SkipDatabase = $false,
    [switch]$RunTests = $false
)

Write-Host "🏦 Setting up Financial & Payment Service..." -ForegroundColor Green
Write-Host "Environment: $Environment" -ForegroundColor Yellow

# Set error action preference
$ErrorActionPreference = "Stop"

try {
    # Check if Docker is running
    Write-Host "🐳 Checking Docker..." -ForegroundColor Blue
    docker version | Out-Null
    if ($LASTEXITCODE -ne 0) {
        throw "Docker is not running. Please start Docker Desktop."
    }

    # Create network if it doesn't exist
    Write-Host "🌐 Creating Docker network..." -ForegroundColor Blue
    docker network create tli-network 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Docker network 'tli-network' created" -ForegroundColor Green
    } else {
        Write-Host "ℹ️ Docker network 'tli-network' already exists" -ForegroundColor Yellow
    }

    # Start infrastructure services
    if (-not $SkipDatabase) {
        Write-Host "🗄️ Starting infrastructure services..." -ForegroundColor Blue
        docker-compose up -d timescaledb rabbitmq redis
        
        Write-Host "⏳ Waiting for database to be ready..." -ForegroundColor Yellow
        Start-Sleep -Seconds 10
        
        # Test database connection
        $maxRetries = 30
        $retryCount = 0
        do {
            $retryCount++
            Write-Host "🔍 Testing database connection (attempt $retryCount/$maxRetries)..." -ForegroundColor Blue
            
            $result = docker exec tli-financial-payment-db pg_isready -h localhost -p 5432 -U timescale 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ Database is ready!" -ForegroundColor Green
                break
            }
            
            if ($retryCount -eq $maxRetries) {
                throw "Database failed to start after $maxRetries attempts"
            }
            
            Start-Sleep -Seconds 2
        } while ($true)
    }

    # Build the application
    if (-not $SkipBuild) {
        Write-Host "🔨 Building Financial & Payment Service..." -ForegroundColor Blue
        dotnet build FinancialPayment.API/FinancialPayment.API.csproj -c Release
        if ($LASTEXITCODE -ne 0) {
            throw "Build failed"
        }
        Write-Host "✅ Build completed successfully" -ForegroundColor Green
    }

    # Run tests
    if ($RunTests) {
        Write-Host "🧪 Running tests..." -ForegroundColor Blue
        dotnet test --configuration Release --logger "console;verbosity=minimal"
        if ($LASTEXITCODE -ne 0) {
            Write-Host "⚠️ Some tests failed, but continuing..." -ForegroundColor Yellow
        } else {
            Write-Host "✅ All tests passed" -ForegroundColor Green
        }
    }

    # Apply database migrations
    if (-not $SkipDatabase) {
        Write-Host "📊 Applying database migrations..." -ForegroundColor Blue
        $env:ASPNETCORE_ENVIRONMENT = $Environment
        dotnet ef database update --project FinancialPayment.Infrastructure --startup-project FinancialPayment.API --context FinancialPaymentDbContext
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Database migrations applied successfully" -ForegroundColor Green
        } else {
            Write-Host "⚠️ Database migrations failed, but continuing..." -ForegroundColor Yellow
        }
    }

    # Start the API service
    Write-Host "🚀 Starting Financial & Payment Service API..." -ForegroundColor Blue
    
    if ($Environment -eq "Docker") {
        # Run with Docker
        docker-compose up -d financial-payment-api
        Write-Host "✅ Financial & Payment Service started with Docker" -ForegroundColor Green
        Write-Host "🌐 API URL: http://localhost:5007" -ForegroundColor Cyan
        Write-Host "📚 Swagger UI: http://localhost:5007/swagger" -ForegroundColor Cyan
        Write-Host "❤️ Health Check: http://localhost:5007/health" -ForegroundColor Cyan
    } else {
        # Run with dotnet
        Write-Host "🌐 API will be available at: http://localhost:5007" -ForegroundColor Cyan
        Write-Host "📚 Swagger UI will be available at: http://localhost:5007/swagger" -ForegroundColor Cyan
        Write-Host "❤️ Health Check will be available at: http://localhost:5007/health" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "Press Ctrl+C to stop the service" -ForegroundColor Yellow
        Write-Host ""
        
        $env:ASPNETCORE_ENVIRONMENT = $Environment
        $env:ASPNETCORE_URLS = "http://localhost:5007"
        dotnet run --project FinancialPayment.API/FinancialPayment.API.csproj
    }

} catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "🔍 Check the logs above for more details" -ForegroundColor Yellow
    exit 1
}

Write-Host ""
Write-Host "🎉 Financial & Payment Service setup completed!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Service Information:" -ForegroundColor Cyan
Write-Host "  • Environment: $Environment" -ForegroundColor White
Write-Host "  • API URL: http://localhost:5007" -ForegroundColor White
Write-Host "  • Swagger UI: http://localhost:5007/swagger" -ForegroundColor White
Write-Host "  • Health Check: http://localhost:5007/health" -ForegroundColor White
Write-Host "  • Database: TimescaleDB on port 5434" -ForegroundColor White
Write-Host "  • Message Queue: RabbitMQ on port 5674 (Management: 15674)" -ForegroundColor White
Write-Host "  • Cache: Redis on port 6381" -ForegroundColor White
Write-Host ""
Write-Host "🔧 Management URLs:" -ForegroundColor Cyan
Write-Host "  • RabbitMQ Management: http://localhost:15674 (guest/guest)" -ForegroundColor White
Write-Host ""
Write-Host "📊 Key Features Available:" -ForegroundColor Cyan
Write-Host "  • ✅ Escrow Account Management" -ForegroundColor White
Write-Host "  • ✅ Multi-party Settlement Processing" -ForegroundColor White
Write-Host "  • ✅ Commission Calculation Engine" -ForegroundColor White
Write-Host "  • ✅ Payment Dispute Resolution" -ForegroundColor White
Write-Host "  • ✅ Enhanced Payment Processing" -ForegroundColor White
Write-Host "  • ✅ Role-based Financial Operations" -ForegroundColor White
Write-Host ""
Write-Host "🚀 Ready to process financial transactions!" -ForegroundColor Green
