using MediatR;
using Microsoft.Extensions.Logging;
using UserManagement.Domain.Repositories;
using UserManagement.Domain.Exceptions;
using Shared.Messaging;

namespace UserManagement.Application.Admin.Commands.ApproveDocuments
{
    public class ApproveDocumentsCommandHandler : IRequestHandler<ApproveDocumentsCommand, ApproveDocumentsResponse>
    {
        private readonly IDocumentSubmissionRepository _documentSubmissionRepository;
        private readonly IMessageBroker _messageBroker;
        private readonly ILogger<ApproveDocumentsCommandHandler> _logger;

        public ApproveDocumentsCommandHandler(
            IDocumentSubmissionRepository documentSubmissionRepository,
            IMessageBroker messageBroker,
            ILogger<ApproveDocumentsCommandHandler> logger)
        {
            _documentSubmissionRepository = documentSubmissionRepository;
            _messageBroker = messageBroker;
            _logger = logger;
        }

        public async Task<ApproveDocumentsResponse> Handle(ApproveDocumentsCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Processing bulk document approval for SubmissionId: {SubmissionId} by {ApprovedBy}", 
                request.SubmissionId, request.ApprovedBy);

            try
            {
                // Get the document submission
                var submission = await _documentSubmissionRepository.GetByIdAsync(request.SubmissionId);
                if (submission == null)
                {
                    _logger.LogWarning("Document submission not found for SubmissionId: {SubmissionId}", request.SubmissionId);
                    return new ApproveDocumentsResponse
                    {
                        Success = false,
                        Message = "Document submission not found"
                    };
                }

                var results = new List<DocumentApprovalResult>();

                // Process each document approval
                foreach (var docRequest in request.Documents)
                {
                    try
                    {
                        submission.ApproveDocument(docRequest.DocumentType, docRequest.ReviewNotes);
                        results.Add(new DocumentApprovalResult
                        {
                            DocumentType = docRequest.DocumentType,
                            Success = true,
                            Message = "Document approved successfully"
                        });

                        _logger.LogInformation("Approved document {DocumentType} for submission {SubmissionId}", 
                            docRequest.DocumentType, request.SubmissionId);
                    }
                    catch (UserManagementDomainException ex)
                    {
                        results.Add(new DocumentApprovalResult
                        {
                            DocumentType = docRequest.DocumentType,
                            Success = false,
                            Message = ex.Message
                        });

                        _logger.LogWarning("Failed to approve document {DocumentType}: {Message}", 
                            docRequest.DocumentType, ex.Message);
                    }
                }

                // Update the submission
                await _documentSubmissionRepository.UpdateAsync(submission);

                // Publish integration event
                await _messageBroker.PublishAsync("documents.bulk.approved", new
                {
                    SubmissionId = request.SubmissionId,
                    UserId = submission.UserId,
                    UserType = submission.UserType.ToString(),
                    ApprovedBy = request.ApprovedBy,
                    ApprovedDocuments = results.Where(r => r.Success).Select(r => r.DocumentType.ToString()).ToList(),
                    ProcessedAt = DateTime.UtcNow,
                    Notes = request.Notes
                });

                var successCount = results.Count(r => r.Success);
                var totalCount = results.Count;

                _logger.LogInformation("Bulk document approval completed: {SuccessCount}/{TotalCount} documents approved", 
                    successCount, totalCount);

                return new ApproveDocumentsResponse
                {
                    Success = successCount > 0,
                    Message = $"Processed {totalCount} documents. {successCount} approved successfully.",
                    Results = results,
                    ProcessedAt = DateTime.UtcNow
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing bulk document approval for submission {SubmissionId}", request.SubmissionId);
                return new ApproveDocumentsResponse
                {
                    Success = false,
                    Message = "An error occurred while processing document approvals"
                };
            }
        }
    }
}
