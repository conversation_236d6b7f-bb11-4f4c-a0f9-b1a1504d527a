using System;
using System.Threading;
using System.Threading.Tasks;
using Identity.Application.Common.Interfaces;
using Identity.Application.Common.Models;
using Identity.Domain.Entities;
using Identity.Domain.Repositories;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Identity.Application.Auth.Commands.Login
{
    public class LoginCommandHandler : IRequestHandler<LoginCommand, TokenResponse>
    {
        private readonly IUserRepository _userRepository;
        private readonly IPasswordHasher _passwordHasher;
        private readonly ITokenService _tokenService;
        private readonly IUserSessionRepository _sessionRepository;
        private readonly ILogger<LoginCommandHandler> _logger;

        public LoginCommandHandler(
            IUserRepository userRepository,
            IPasswordHasher passwordHasher,
            ITokenService tokenService,
            IUserSessionRepository sessionRepository,
            ILogger<LoginCommandHandler> logger)
        {
            _userRepository = userRepository;
            _passwordHasher = passwordHasher;
            _tokenService = tokenService;
            _sessionRepository = sessionRepository;
            _logger = logger;
        }

        public async Task<TokenResponse> Handle(LoginCommand request, CancellationToken cancellationToken)
        {
            // Get the user by username
            var user = await _userRepository.GetByUsernameAsync(request.Username);
            if (user == null)
            {
                throw new ApplicationException("Invalid username or password");
            }

            // Verify the password
            if (!_passwordHasher.VerifyPassword(user.PasswordHash, request.Password))
            {
                // Increment failed login attempts
                user.IncrementAccessFailedCount();
                await _userRepository.UpdateAsync(user);

                // Check if account should be locked
                if (user.AccessFailedCount >= 5 && user.LockoutEnabled)
                {
                    user.LockoutUser(TimeSpan.FromMinutes(15));
                    await _userRepository.UpdateAsync(user);
                    _logger.LogWarning("User {UserId} account locked due to multiple failed login attempts", user.Id);
                    throw new ApplicationException("Account locked due to multiple failed login attempts");
                }

                throw new ApplicationException("Invalid username or password");
            }

            // Check if account is locked
            if (user.LockoutEnd.HasValue && user.LockoutEnd > DateTime.UtcNow)
            {
                throw new ApplicationException("Account is locked. Please try again later");
            }

            // Check if account is suspended
            if (user.Status == UserStatus.Suspended)
            {
                throw new ApplicationException("Account is suspended. Please contact support");
            }

            // Reset failed login attempts and update user
            user.ResetAccessFailedCount();
            await _userRepository.UpdateAsync(user);

            // Detach the user entity to avoid tracking conflicts
            await _userRepository.DetachAsync(user);

            // Generate tokens (this will save the refresh token separately)
            var tokenResponse = await _tokenService.GenerateTokensAsync(user.Id);

            // Create user session
            var session = new UserSession(
                user.Id,
                tokenResponse.AccessToken,
                request.DeviceInfo,
                request.IpAddress,
                tokenResponse.ExpiresAt);

            await _sessionRepository.AddAsync(session);

            _logger.LogInformation("User {UserId} logged in successfully", user.Id);

            return tokenResponse;
        }
    }
}
