using NetworkFleetManagement.Domain.Enums;

namespace NetworkFleetManagement.Application.DTOs;

public class DriverDto
{
    public Guid Id { get; set; }
    public Guid CarrierId { get; set; }
    public Guid UserId { get; set; }
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string FullName { get; set; } = string.Empty;
    public string PhoneNumber { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string LicenseNumber { get; set; } = string.Empty;
    public DateTime LicenseExpiryDate { get; set; }
    public string? AadharNumber { get; set; }
    public string? PANNumber { get; set; }
    public DriverStatus Status { get; set; }
    public OnboardingStatus OnboardingStatus { get; set; }
    public LocationDto? CurrentLocation { get; set; }
    public PerformanceMetricsDto PerformanceMetrics { get; set; } = new();
    public string? ProfilePhotoUrl { get; set; }
    public DateTime? LastActiveAt { get; set; }
    public string? Notes { get; set; }
    public bool IsVerified { get; set; }
    public DateTime? VerifiedAt { get; set; }
    public bool IsLicenseValid { get; set; }
    public bool IsAvailable { get; set; }
    public List<string> PreferredRoutes { get; set; } = new();
    public List<string> OperationalAreas { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }

    // Related data
    public string CarrierCompanyName { get; set; } = string.Empty;
    public int DocumentCount { get; set; }
    public int VehicleAssignmentCount { get; set; }
}

public class DriverSummaryDto
{
    public Guid Id { get; set; }
    public Guid CarrierId { get; set; }
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string FullName { get; set; } = string.Empty;
    public string PhoneNumber { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string LicenseNumber { get; set; } = string.Empty;
    public DriverStatus Status { get; set; }
    public OnboardingStatus OnboardingStatus { get; set; }
    public decimal Rating { get; set; }
    public int TotalTrips { get; set; }
    public bool IsVerified { get; set; }
    public bool IsAvailable { get; set; }
    public string CarrierCompanyName { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
}

public class CreateDriverDto
{
    public Guid CarrierId { get; set; }
    public Guid UserId { get; set; }
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string PhoneNumber { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string LicenseNumber { get; set; } = string.Empty;
    public DateTime LicenseExpiryDate { get; set; }
    public string? AadharNumber { get; set; }
    public string? PANNumber { get; set; }
    public string? ProfilePhotoUrl { get; set; }
    public string? Notes { get; set; }
    public List<string> PreferredRoutes { get; set; } = new();
    public List<string> OperationalAreas { get; set; } = new();
}

public class UpdateDriverDto
{
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string PhoneNumber { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string LicenseNumber { get; set; } = string.Empty;
    public DateTime LicenseExpiryDate { get; set; }
    public string? AadharNumber { get; set; }
    public string? PANNumber { get; set; }
    public string? ProfilePhotoUrl { get; set; }
    public string? Notes { get; set; }
    public List<string> PreferredRoutes { get; set; } = new();
    public List<string> OperationalAreas { get; set; } = new();
}

public class UpdateDriverStatusDto
{
    public DriverStatus Status { get; set; }
}

public class DriverDashboardDto
{
    public Guid Id { get; set; }
    public Guid CarrierId { get; set; }
    public Guid UserId { get; set; }
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string FullName { get; set; } = string.Empty;
    public string PhoneNumber { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string LicenseNumber { get; set; } = string.Empty;
    public DateTime LicenseExpiryDate { get; set; }
    public DriverStatus Status { get; set; }
    public OnboardingStatus OnboardingStatus { get; set; }
    public string CarrierCompanyName { get; set; } = string.Empty;

    // License Status Information
    public bool IsLicenseValid { get; set; }
    public int DaysUntilLicenseExpiry { get; set; }
    public string LicenseStatus { get; set; } = string.Empty; // Valid, Expired, Expiring
    public string LicenseStatusColor { get; set; } = string.Empty; // Green, Red, Orange

    // Performance Metrics
    public PerformanceMetricsDto PerformanceMetrics { get; set; } = new();
    public decimal Rating { get; set; }
    public int TotalTrips { get; set; }
    public int CompletedTrips { get; set; }
    public decimal CompletionRate { get; set; }
    public decimal OnTimeDeliveryRate { get; set; }

    // Current Assignment Information
    public Guid? CurrentVehicleId { get; set; }
    public string? CurrentVehicleRegistration { get; set; }
    public string? CurrentVehicleDisplayName { get; set; }
    public bool IsAssignedToVehicle { get; set; }
    public DateTime? AssignmentStartDate { get; set; }

    // Current Trip Information
    public Guid? CurrentTripId { get; set; }
    public string? CurrentTripNumber { get; set; }
    public string? CurrentTripStatus { get; set; }
    public bool IsOnTrip { get; set; }

    // Location and Activity
    public LocationDto? CurrentLocation { get; set; }
    public DateTime? LastActiveAt { get; set; }
    public bool IsActive { get; set; } // Active in last 24 hours

    // Verification and Compliance
    public bool IsVerified { get; set; }
    public DateTime? VerifiedAt { get; set; }
    public bool IsAvailable { get; set; }
    public int DocumentCount { get; set; }
    public int ExpiredDocumentsCount { get; set; }
    public int ExpiringDocumentsCount { get; set; } // Expiring within 30 days

    // Profile Information
    public string? ProfilePhotoUrl { get; set; }
    public List<string> PreferredRoutes { get; set; } = new();
    public List<string> OperationalAreas { get; set; } = new();

    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

public class DriverAssignmentSummaryDto
{
    public Guid DriverId { get; set; }
    public string DriverName { get; set; } = string.Empty;
    public Guid? VehicleId { get; set; }
    public string? VehicleRegistration { get; set; }
    public DateTime? AssignmentStartDate { get; set; }
    public bool IsActive { get; set; }
    public Guid? CurrentTripId { get; set; }
    public string? CurrentTripNumber { get; set; }
}

public class UpdateDriverOnboardingStatusDto
{
    public OnboardingStatus Status { get; set; }
}

public class UpdateDriverLocationDto
{
    public LocationDto Location { get; set; } = new();
}
