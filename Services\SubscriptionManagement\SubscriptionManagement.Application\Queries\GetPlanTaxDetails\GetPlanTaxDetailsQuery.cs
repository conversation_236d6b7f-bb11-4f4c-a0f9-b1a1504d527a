using MediatR;
using SubscriptionManagement.Application.DTOs;

namespace SubscriptionManagement.Application.Queries.GetPlanTaxDetails
{
    public class GetPlanTaxDetailsQuery : IRequest<PlanTaxDetailsDto?>
    {
        public Guid PlanId { get; set; }
        public string CustomerRegion { get; set; } = string.Empty;
        public Guid? CustomerId { get; set; }
        public DateTime? CalculationDate { get; set; }
    }
}
