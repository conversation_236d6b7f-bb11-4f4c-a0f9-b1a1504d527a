using CommunicationNotification.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace CommunicationNotification.Infrastructure.Persistence.Configurations;

/// <summary>
/// Entity configuration for ConversationParticipant
/// </summary>
public class ConversationParticipantConfiguration : IEntityTypeConfiguration<ConversationParticipant>
{
    public void Configure(EntityTypeBuilder<ConversationParticipant> builder)
    {
        builder.ToTable("conversation_participants");

        // Primary key
        builder.HasKey(cp => cp.Id);
        builder.Property(cp => cp.Id)
            .ValueGeneratedNever();

        // Properties
        builder.Property(cp => cp.ConversationThreadId)
            .IsRequired();

        builder.Property(cp => cp.UserId)
            .IsRequired();

        builder.Property(cp => cp.Role)
            .IsRequired()
            .HasConversion<string>()
            .HasMaxLength(50);

        builder.Property(cp => cp.JoinedAt)
            .IsRequired();

        builder.Property(cp => cp.LeftAt)
            .IsRequired(false);

        builder.Property(cp => cp.CanWrite)
            .IsRequired()
            .HasDefaultValue(true);

        builder.Property(cp => cp.CanRead)
            .IsRequired()
            .HasDefaultValue(true);

        builder.Property(cp => cp.IsActive)
            .IsRequired()
            .HasDefaultValue(true);

        builder.Property(cp => cp.LastReadAt)
            .IsRequired(false);

        builder.Property(cp => cp.NotificationSettings)
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new())
            .HasColumnType("jsonb");

        // Base entity properties
        builder.Property(cp => cp.CreatedAt)
            .IsRequired();

        builder.Property(cp => cp.UpdatedAt)
            .IsRequired();

        // Unique constraint on ConversationThreadId + UserId
        builder.HasIndex(cp => new { cp.ConversationThreadId, cp.UserId })
            .IsUnique();

        // Relationships
        builder.HasOne<ConversationThread>()
            .WithMany(ct => ct.Participants)
            .HasForeignKey(cp => cp.ConversationThreadId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes are configured in DbContext
    }
}

/// <summary>
/// Entity configuration for Template
/// </summary>
public class TemplateConfiguration : IEntityTypeConfiguration<Template>
{
    public void Configure(EntityTypeBuilder<Template> builder)
    {
        builder.ToTable("templates");

        // Primary key
        builder.HasKey(t => t.Id);
        builder.Property(t => t.Id)
            .ValueGeneratedNever();

        // Properties
        builder.Property(t => t.Name)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(t => t.Description)
            .HasMaxLength(500)
            .IsRequired(false);

        builder.Property(t => t.MessageType)
            .IsRequired()
            .HasConversion<string>()
            .HasMaxLength(50);

        builder.Property(t => t.Channel)
            .IsRequired()
            .HasConversion<string>()
            .HasMaxLength(20);

        builder.Property(t => t.Language)
            .IsRequired()
            .HasConversion<string>()
            .HasMaxLength(20);

        builder.Property(t => t.Subject)
            .HasMaxLength(500)
            .IsRequired(false);

        builder.Property(t => t.Body)
            .IsRequired()
            .HasMaxLength(4000);

        builder.Property(t => t.IsActive)
            .IsRequired()
            .HasDefaultValue(true);

        // Variables as JSON
        builder.Property(t => t.Variables)
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new())
            .HasColumnType("jsonb");

        // Metadata as JSON
        builder.Property(t => t.Metadata)
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new())
            .HasColumnType("jsonb");

        // Base entity properties
        builder.Property(t => t.CreatedAt)
            .IsRequired();

        builder.Property(t => t.UpdatedAt)
            .IsRequired();

        // Unique constraint on Name
        builder.HasIndex(t => t.Name)
            .IsUnique();

        // Relationships
        builder.HasMany(t => t.Versions)
            .WithOne()
            .HasForeignKey(tv => tv.TemplateId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes are configured in DbContext
    }
}

/// <summary>
/// Entity configuration for TemplateVersion
/// </summary>
public class TemplateVersionConfiguration : IEntityTypeConfiguration<TemplateVersion>
{
    public void Configure(EntityTypeBuilder<TemplateVersion> builder)
    {
        builder.ToTable("template_versions");

        // Primary key
        builder.HasKey(tv => tv.Id);
        builder.Property(tv => tv.Id)
            .ValueGeneratedNever();

        // Properties
        builder.Property(tv => tv.TemplateId)
            .IsRequired();

        builder.Property(tv => tv.Version)
            .IsRequired();

        builder.Property(tv => tv.Subject)
            .HasMaxLength(500)
            .IsRequired(false);

        builder.Property(tv => tv.Body)
            .IsRequired()
            .HasMaxLength(4000);

        builder.Property(tv => tv.Variables)
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new())
            .HasColumnType("jsonb");

        builder.Property(tv => tv.IsActive)
            .IsRequired()
            .HasDefaultValue(false);

        builder.Property(tv => tv.CreatedBy)
            .IsRequired();

        builder.Property(tv => tv.ChangeNotes)
            .HasMaxLength(1000)
            .IsRequired(false);

        // Base entity properties
        builder.Property(tv => tv.CreatedAt)
            .IsRequired();

        builder.Property(tv => tv.UpdatedAt)
            .IsRequired();

        // Unique constraint on TemplateId + Version
        builder.HasIndex(tv => new { tv.TemplateId, tv.Version })
            .IsUnique();

        // Relationships
        builder.HasOne<Template>()
            .WithMany(t => t.Versions)
            .HasForeignKey(tv => tv.TemplateId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}

/// <summary>
/// Entity configuration for DeliveryAttempt
/// </summary>
public class DeliveryAttemptConfiguration : IEntityTypeConfiguration<DeliveryAttempt>
{
    public void Configure(EntityTypeBuilder<DeliveryAttempt> builder)
    {
        builder.ToTable("delivery_attempts");

        // Primary key
        builder.HasKey(da => da.Id);
        builder.Property(da => da.Id)
            .ValueGeneratedNever();

        // Properties
        builder.Property(da => da.NotificationId)
            .IsRequired();

        builder.Property(da => da.AttemptNumber)
            .IsRequired();

        builder.Property(da => da.Channel)
            .IsRequired()
            .HasConversion<string>()
            .HasMaxLength(20);

        builder.Property(da => da.AttemptedAt)
            .IsRequired();

        builder.Property(da => da.IsSuccess)
            .IsRequired();

        builder.Property(da => da.ErrorMessage)
            .HasMaxLength(1000)
            .IsRequired(false);

        builder.Property(da => da.ExternalId)
            .HasMaxLength(255)
            .IsRequired(false);

        builder.Property(da => da.ResponseTime)
            .IsRequired(false);

        builder.Property(da => da.ProviderResponse)
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new())
            .HasColumnType("jsonb");

        // Base entity properties
        builder.Property(da => da.CreatedAt)
            .IsRequired();

        builder.Property(da => da.UpdatedAt)
            .IsRequired();

        // Relationships
        builder.HasOne<Notification>()
            .WithMany()
            .HasForeignKey(da => da.NotificationId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes are configured in DbContext
    }
}

/// <summary>
/// Entity configuration for MessageArchive
/// </summary>
public class MessageArchiveConfiguration : IEntityTypeConfiguration<MessageArchive>
{
    public void Configure(EntityTypeBuilder<MessageArchive> builder)
    {
        builder.ToTable("message_archives");

        // Primary key
        builder.HasKey(ma => ma.Id);
        builder.Property(ma => ma.Id)
            .ValueGeneratedNever();

        // Properties
        builder.Property(ma => ma.OriginalMessageId)
            .IsRequired();

        builder.Property(ma => ma.UserId)
            .IsRequired();

        builder.Property(ma => ma.ArchivedAt)
            .IsRequired();

        builder.Property(ma => ma.ArchivedBy)
            .IsRequired();

        builder.Property(ma => ma.Reason)
            .HasMaxLength(500)
            .IsRequired(false);

        builder.Property(ma => ma.MessageData)
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new())
            .HasColumnType("jsonb");

        // Base entity properties
        builder.Property(ma => ma.CreatedAt)
            .IsRequired();

        builder.Property(ma => ma.UpdatedAt)
            .IsRequired();

        // Indexes
        builder.HasIndex(ma => ma.OriginalMessageId);
        builder.HasIndex(ma => ma.UserId);
        builder.HasIndex(ma => ma.ArchivedAt);
    }
}
