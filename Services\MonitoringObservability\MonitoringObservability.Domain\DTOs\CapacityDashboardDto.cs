namespace MonitoringObservability.Domain.DTOs;

/// <summary>
/// DTO for capacity dashboard
/// </summary>
public class CapacityDashboardDto
{
    public DateTime GeneratedAt { get; set; }
    public CapacityOverviewMetrics Overview { get; set; } = new();
    public List<CapacityServiceSummary> ServiceSummaries { get; set; } = new();
    public List<CapacityAlert> Alerts { get; set; } = new();
    public List<CapacityTrendData> Trends { get; set; } = new();
    public CapacityResourceBreakdown ResourceBreakdown { get; set; } = new();
    public List<CapacityRecommendation> Recommendations { get; set; } = new();
}

public class CapacityOverviewMetrics
{
    public double OverallCpuUtilization { get; set; }
    public double OverallMemoryUtilization { get; set; }
    public double OverallDiskUtilization { get; set; }
    public double OverallNetworkUtilization { get; set; }
    public int TotalServices { get; set; }
    public int ServicesAtCapacity { get; set; }
    public int ServicesNearCapacity { get; set; }
    public DateTime? NextCapacityExhaustionDate { get; set; }
}

public class CapacityServiceSummary
{
    public string ServiceName { get; set; } = string.Empty;
    public double CpuUtilization { get; set; }
    public double MemoryUtilization { get; set; }
    public double DiskUtilization { get; set; }
    public double NetworkUtilization { get; set; }
    public string CapacityStatus { get; set; } = string.Empty;
    public DateTime? EstimatedCapacityExhaustionDate { get; set; }
    public double GrowthRate { get; set; }
    public int CurrentInstances { get; set; }
}

public class CapacityAlert
{
    public Guid AlertId { get; set; }
    public string ServiceName { get; set; } = string.Empty;
    public string ResourceType { get; set; } = string.Empty;
    public string AlertType { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public double CurrentUtilization { get; set; }
    public double ThresholdValue { get; set; }
    public string Message { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public string Status { get; set; } = string.Empty;
}

public class CapacityTrendData
{
    public DateTime Date { get; set; }
    public string ServiceName { get; set; } = string.Empty;
    public string ResourceType { get; set; } = string.Empty;
    public double UtilizationPercentage { get; set; }
    public double ThroughputValue { get; set; }
    public int InstanceCount { get; set; }
}

public class CapacityResourceBreakdown
{
    public Dictionary<string, double> CpuByService { get; set; } = new();
    public Dictionary<string, double> MemoryByService { get; set; } = new();
    public Dictionary<string, double> DiskByService { get; set; } = new();
    public Dictionary<string, double> NetworkByService { get; set; } = new();
    public Dictionary<string, int> InstancesByService { get; set; } = new();
}
