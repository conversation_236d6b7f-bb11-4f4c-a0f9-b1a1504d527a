using AnalyticsBIService.Domain.Enums;

namespace AnalyticsBIService.Infrastructure.Services;

/// <summary>
/// Interface for advanced data visualization service
/// </summary>
public interface IDataVisualizationService
{
    /// <summary>
    /// Create interactive chart visualization
    /// </summary>
    Task<ChartVisualizationDto> CreateInteractiveChartAsync(CreateChartRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Create heatmap visualization
    /// </summary>
    Task<HeatmapVisualizationDto> CreateHeatmapAsync(CreateHeatmapRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Create funnel chart visualization
    /// </summary>
    Task<FunnelVisualizationDto> CreateFunnelChartAsync(CreateFunnelRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Create treemap visualization
    /// </summary>
    Task<TreemapVisualizationDto> CreateTreemapAsync(CreateTreemapRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Create gauge chart visualization
    /// </summary>
    Task<GaugeVisualizationDto> CreateGaugeChartAsync(CreateGaugeRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Create scatter plot visualization
    /// </summary>
    Task<ScatterPlotVisualizationDto> CreateScatterPlotAsync(CreateScatterPlotRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Create geographic map visualization
    /// </summary>
    Task<MapVisualizationDto> CreateGeographicMapAsync(CreateMapRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Create custom dashboard with multiple visualizations
    /// </summary>
    Task<DashboardVisualizationDto> CreateCustomDashboardAsync(CreateDashboardRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get available visualization templates
    /// </summary>
    Task<List<VisualizationTemplateDto>> GetVisualizationTemplatesAsync(string category = "", CancellationToken cancellationToken = default);

    /// <summary>
    /// Export visualization as image
    /// </summary>
    Task<byte[]> ExportVisualizationAsImageAsync(Guid visualizationId, string format = "PNG", CancellationToken cancellationToken = default);

    /// <summary>
    /// Get visualization data in real-time
    /// </summary>
    Task<RealTimeVisualizationDto> GetRealTimeVisualizationAsync(Guid visualizationId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update visualization configuration
    /// </summary>
    Task<bool> UpdateVisualizationConfigAsync(Guid visualizationId, UpdateVisualizationRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get visualization performance metrics
    /// </summary>
    Task<VisualizationPerformanceDto> GetVisualizationPerformanceAsync(Guid visualizationId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Base visualization data transfer object
/// </summary>
public abstract class BaseVisualizationDto
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public Dictionary<string, object> Data { get; set; } = new();
    public Dictionary<string, object> Options { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public Guid CreatedBy { get; set; }
    public bool IsInteractive { get; set; }
    public bool IsRealTime { get; set; }
}

/// <summary>
/// Chart visualization data transfer object
/// </summary>
public class ChartVisualizationDto : BaseVisualizationDto
{
    public List<ChartDatasetDto> Datasets { get; set; } = new();
    public List<string> Labels { get; set; } = new();
    public ChartAxesDto Axes { get; set; } = new();
    public ChartLegendDto Legend { get; set; } = new();
    public List<ChartAnnotationDto> Annotations { get; set; } = new();
}

/// <summary>
/// Heatmap visualization data transfer object
/// </summary>
public class HeatmapVisualizationDto : BaseVisualizationDto
{
    public List<HeatmapDataPointDto> DataPoints { get; set; } = new();
    public HeatmapColorScaleDto ColorScale { get; set; } = new();
    public List<string> XAxisLabels { get; set; } = new();
    public List<string> YAxisLabels { get; set; } = new();
}

/// <summary>
/// Funnel visualization data transfer object
/// </summary>
public class FunnelVisualizationDto : BaseVisualizationDto
{
    public List<FunnelStageDto> Stages { get; set; } = new();
    public FunnelMetricsDto Metrics { get; set; } = new();
}

/// <summary>
/// Treemap visualization data transfer object
/// </summary>
public class TreemapVisualizationDto : BaseVisualizationDto
{
    public List<TreemapNodeDto> Nodes { get; set; } = new();
    public TreemapColorSchemeDto ColorScheme { get; set; } = new();
}

/// <summary>
/// Gauge visualization data transfer object
/// </summary>
public class GaugeVisualizationDto : BaseVisualizationDto
{
    public decimal Value { get; set; }
    public decimal MinValue { get; set; }
    public decimal MaxValue { get; set; }
    public List<GaugeThresholdDto> Thresholds { get; set; } = new();
    public string Unit { get; set; } = string.Empty;
}

/// <summary>
/// Scatter plot visualization data transfer object
/// </summary>
public class ScatterPlotVisualizationDto : BaseVisualizationDto
{
    public List<ScatterPlotDatasetDto> Datasets { get; set; } = new();
    public ScatterPlotAxesDto Axes { get; set; } = new();
    public List<ScatterPlotTrendlineDto> Trendlines { get; set; } = new();
}

/// <summary>
/// Map visualization data transfer object
/// </summary>
public class MapVisualizationDto : BaseVisualizationDto
{
    public List<MapDataPointDto> DataPoints { get; set; } = new();
    public MapConfigurationDto Configuration { get; set; } = new();
    public List<MapLayerDto> Layers { get; set; } = new();
}

/// <summary>
/// Dashboard visualization data transfer object
/// </summary>
public class DashboardVisualizationDto : BaseVisualizationDto
{
    public List<DashboardWidgetDto> Widgets { get; set; } = new();
    public DashboardLayoutDto Layout { get; set; } = new();
    public DashboardFiltersDto Filters { get; set; } = new();
}

/// <summary>
/// Real-time visualization data transfer object
/// </summary>
public class RealTimeVisualizationDto
{
    public Guid VisualizationId { get; set; }
    public Dictionary<string, object> Data { get; set; } = new();
    public DateTime LastUpdated { get; set; }
    public int UpdateInterval { get; set; }
    public bool IsConnected { get; set; }
}

/// <summary>
/// Visualization template data transfer object
/// </summary>
public class VisualizationTemplateDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public Dictionary<string, object> DefaultConfiguration { get; set; } = new();
    public List<string> RequiredFields { get; set; } = new();
    public string PreviewImage { get; set; } = string.Empty;
    public bool IsCustomizable { get; set; }

    // Properties required by DataVisualizationService
    public string[] ChartTypes { get; set; } = Array.Empty<string>();
    public string[] DataRequirements { get; set; } = Array.Empty<string>();
    public string UseCase { get; set; } = string.Empty;
}

/// <summary>
/// Visualization performance data transfer object
/// </summary>
public class VisualizationPerformanceDto
{
    public Guid VisualizationId { get; set; }
    public TimeSpan AverageRenderTime { get; set; }
    public int DataPointCount { get; set; }
    public long MemoryUsage { get; set; }
    public int ViewCount { get; set; }
    public DateTime LastAccessed { get; set; }
    public List<PerformanceMetricDto> Metrics { get; set; } = new();
}

// Supporting DTOs
public class ChartDatasetDto
{
    public string Label { get; set; } = string.Empty;
    public List<object> Data { get; set; } = new();
    public string BackgroundColor { get; set; } = string.Empty;
    public string BorderColor { get; set; } = string.Empty;
    public int BorderWidth { get; set; } = 1;
    public bool Fill { get; set; }
    public string Type { get; set; } = string.Empty;
}

public class ChartAxesDto
{
    public ChartAxisDto XAxis { get; set; } = new();
    public ChartAxisDto YAxis { get; set; } = new();
}

public class ChartAxisDto
{
    public string Label { get; set; } = string.Empty;
    public string Type { get; set; } = "linear";
    public decimal? Min { get; set; }
    public decimal? Max { get; set; }
    public bool Display { get; set; } = true;
}

public class ChartLegendDto
{
    public bool Display { get; set; } = true;
    public string Position { get; set; } = "top";
    public Dictionary<string, object> Style { get; set; } = new();
}

public class ChartAnnotationDto
{
    public string Type { get; set; } = string.Empty;
    public string Label { get; set; } = string.Empty;
    public object Value { get; set; } = new();
    public string Color { get; set; } = string.Empty;
}

public class HeatmapDataPointDto
{
    public int X { get; set; }
    public int Y { get; set; }
    public decimal Value { get; set; }
    public string Label { get; set; } = string.Empty;
}

public class HeatmapColorScaleDto
{
    public string Type { get; set; } = "linear";
    public List<string> Colors { get; set; } = new();
    public decimal MinValue { get; set; }
    public decimal MaxValue { get; set; }
}

public class FunnelStageDto
{
    public string Name { get; set; } = string.Empty;
    public decimal Value { get; set; }
    public decimal ConversionRate { get; set; }
    public string Color { get; set; } = string.Empty;
    public int Order { get; set; }
}



public class TreemapNodeDto
{
    public string Name { get; set; } = string.Empty;
    public decimal Value { get; set; }
    public string Color { get; set; } = string.Empty;
    public List<TreemapNodeDto> Children { get; set; } = new();
    public string Category { get; set; } = string.Empty;
}

public class TreemapColorSchemeDto
{
    public string Type { get; set; } = "categorical";
    public List<string> Colors { get; set; } = new();
}

public class GaugeThresholdDto
{
    public decimal Value { get; set; }
    public string Color { get; set; } = string.Empty;
    public string Label { get; set; } = string.Empty;
}

public class ScatterPlotDatasetDto
{
    public string Label { get; set; } = string.Empty;
    public List<ScatterPlotPointDto> Data { get; set; } = new();
    public string BackgroundColor { get; set; } = string.Empty;
    public string BorderColor { get; set; } = string.Empty;
}

public class ScatterPlotPointDto
{
    public decimal X { get; set; }
    public decimal Y { get; set; }
    public string Label { get; set; } = string.Empty;
}

public class ScatterPlotAxesDto
{
    public string XAxisLabel { get; set; } = string.Empty;
    public string YAxisLabel { get; set; } = string.Empty;
    public decimal? XMin { get; set; }
    public decimal? XMax { get; set; }
    public decimal? YMin { get; set; }
    public decimal? YMax { get; set; }
}

public class ScatterPlotTrendlineDto
{
    public string Type { get; set; } = "linear";
    public string Color { get; set; } = string.Empty;
    public List<ScatterPlotPointDto> Points { get; set; } = new();
}

public class MapDataPointDto
{
    public decimal Latitude { get; set; }
    public decimal Longitude { get; set; }
    public decimal Value { get; set; }
    public string Label { get; set; } = string.Empty;
    public string Color { get; set; } = string.Empty;
    public Dictionary<string, object> Properties { get; set; } = new();
}

public class MapConfigurationDto
{
    public decimal CenterLatitude { get; set; }
    public decimal CenterLongitude { get; set; }
    public int ZoomLevel { get; set; } = 10;
    public string MapStyle { get; set; } = "standard";
    public bool ShowControls { get; set; } = true;
}

public class MapLayerDto
{
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public bool IsVisible { get; set; } = true;
    public Dictionary<string, object> Configuration { get; set; } = new();
}

public class DashboardWidgetDto
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public int X { get; set; }
    public int Y { get; set; }
    public int Width { get; set; }
    public int Height { get; set; }
    public Dictionary<string, object> Configuration { get; set; } = new();
}

public class DashboardLayoutDto
{
    public int Columns { get; set; } = 12;
    public int RowHeight { get; set; } = 100;
    public bool IsDraggable { get; set; } = true;
    public bool IsResizable { get; set; } = true;
}

public class DashboardFiltersDto
{
    public List<DashboardFilterDto> Filters { get; set; } = new();
    public bool IsGlobal { get; set; }
}

public class DashboardFilterDto
{
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public object Value { get; set; } = new();
    public List<object> Options { get; set; } = new();
}

public class PerformanceMetricDto
{
    public string Name { get; set; } = string.Empty;
    public decimal Value { get; set; }
    public string Unit { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
}

// Request DTOs
public class CreateChartRequest
{
    public string Title { get; set; } = string.Empty;
    public string ChartType { get; set; } = string.Empty;
    public List<ChartDatasetDto> Datasets { get; set; } = new();
    public List<string> Labels { get; set; } = new();
    public Dictionary<string, object> Options { get; set; } = new();
    public Guid UserId { get; set; }
    public bool IsInteractive { get; set; } = true;
}

public class CreateHeatmapRequest
{
    public string Title { get; set; } = string.Empty;
    public List<HeatmapDataPointDto> DataPoints { get; set; } = new();
    public List<string> XAxisLabels { get; set; } = new();
    public List<string> YAxisLabels { get; set; } = new();
    public HeatmapColorScaleDto ColorScale { get; set; } = new();
    public Guid UserId { get; set; }
}

public class CreateFunnelRequest
{
    public string Title { get; set; } = string.Empty;
    public List<FunnelStageDto> Stages { get; set; } = new();
    public Dictionary<string, object> Options { get; set; } = new();
    public Guid UserId { get; set; }
}

public class CreateTreemapRequest
{
    public string Title { get; set; } = string.Empty;
    public List<TreemapNodeDto> Nodes { get; set; } = new();
    public TreemapColorSchemeDto ColorScheme { get; set; } = new();
    public Guid UserId { get; set; }
}

public class CreateGaugeRequest
{
    public string Title { get; set; } = string.Empty;
    public decimal Value { get; set; }
    public decimal MinValue { get; set; }
    public decimal MaxValue { get; set; }
    public List<GaugeThresholdDto> Thresholds { get; set; } = new();
    public string Unit { get; set; } = string.Empty;
    public Guid UserId { get; set; }
}

public class CreateScatterPlotRequest
{
    public string Title { get; set; } = string.Empty;
    public List<ScatterPlotDatasetDto> Datasets { get; set; } = new();
    public ScatterPlotAxesDto Axes { get; set; } = new();
    public bool ShowTrendlines { get; set; }
    public Guid UserId { get; set; }
}

public class CreateMapRequest
{
    public string Title { get; set; } = string.Empty;
    public List<MapDataPointDto> DataPoints { get; set; } = new();
    public MapConfigurationDto Configuration { get; set; } = new();
    public List<MapLayerDto> Layers { get; set; } = new();
    public Guid UserId { get; set; }
}

public class CreateDashboardRequest
{
    public string Title { get; set; } = string.Empty;
    public List<DashboardWidgetDto> Widgets { get; set; } = new();
    public DashboardLayoutDto Layout { get; set; } = new();
    public DashboardFiltersDto Filters { get; set; } = new();
    public Guid UserId { get; set; }
}

public class UpdateVisualizationRequest
{
    public string? Title { get; set; }
    public string? Description { get; set; }
    public Dictionary<string, object>? Options { get; set; }
    public Dictionary<string, object>? Data { get; set; }
    public bool? IsInteractive { get; set; }
    public bool? IsRealTime { get; set; }
}
