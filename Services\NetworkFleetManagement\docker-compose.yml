version: '3.8'

services:
  networkfleet-api:
    build:
      context: ../../
      dockerfile: Services/NetworkFleetManagement/Dockerfile
    container_name: networkfleet-api
    ports:
      - "5005:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Host=postgres;Port=5432;Database=TLI_NetworkFleetManagement;Username=timescale;Password=timescale
      - ConnectionStrings__RabbitMQ=rabbitmq
      - ConnectionStrings__Redis=redis:6379
      - Authentication__Authority=http://identity-api:80
      - Authentication__Audience=networkfleet-api
    depends_on:
      - postgres
      - rabbitmq
      - redis
    networks:
      - tli-network
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped

  postgres:
    image: timescale/timescaledb:latest-pg15
    container_name: networkfleet-postgres
    environment:
      - POSTGRES_DB=TLI_NetworkFleetManagement
      - POSTGRES_USER=timescale
      - POSTGRES_PASSWORD=timescale
    ports:
      - "5434:5432"
    volumes:
      - networkfleet_postgres_data:/var/lib/postgresql/data
      - ./database-setup.sql:/docker-entrypoint-initdb.d/01-setup.sql
    networks:
      - tli-network
    restart: unless-stopped

  rabbitmq:
    image: rabbitmq:3-management
    container_name: networkfleet-rabbitmq
    environment:
      - RABBITMQ_DEFAULT_USER=admin
      - RABBITMQ_DEFAULT_PASS=admin123
    ports:
      - "5674:5672"
      - "15674:15672"
    volumes:
      - networkfleet_rabbitmq_data:/var/lib/rabbitmq
    networks:
      - tli-network
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: networkfleet-redis
    ports:
      - "6381:6379"
    volumes:
      - networkfleet_redis_data:/data
    networks:
      - tli-network
    restart: unless-stopped
    command: redis-server --appendonly yes

volumes:
  networkfleet_postgres_data:
    driver: local
  networkfleet_rabbitmq_data:
    driver: local
  networkfleet_redis_data:
    driver: local

networks:
  tli-network:
    external: true
