using MediatR;
using NetworkFleetManagement.Application.DTOs;

namespace NetworkFleetManagement.Application.Queries.PreferredPartners;

public class GetPartnerCollaborationHistoryQuery : IRequest<List<PartnerCollaborationSummaryDto>>
{
    public Guid PreferredPartnerId { get; set; }
    public Guid UserId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public int? Limit { get; set; }
}
