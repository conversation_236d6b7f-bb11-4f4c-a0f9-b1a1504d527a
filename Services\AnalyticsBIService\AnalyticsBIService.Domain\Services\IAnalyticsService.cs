using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Enums;
using AnalyticsBIService.Domain.ValueObjects;

namespace AnalyticsBIService.Domain.Services;

/// <summary>
/// Core analytics domain service interface
/// </summary>
public interface IAnalyticsService
{
    // Event tracking
    Task TrackEventAsync(AnalyticsEvent analyticsEvent, CancellationToken cancellationToken = default);
    Task TrackUserActivityAsync(string eventName, Guid userId, UserType userType, Dictionary<string, object>? properties = null, CancellationToken cancellationToken = default);
    Task TrackBusinessTransactionAsync(string eventName, DataSourceType dataSource, Guid entityId, string entityType, Dictionary<string, object>? properties = null, Guid? userId = null, CancellationToken cancellationToken = default);

    // Metric calculation
    Task<Metric> CalculateMetricAsync(string metricName, MetricType type, KPICategory category, TimePeriodValue period, DataSourceType dataSource, Guid? userId = null, UserType? userType = null, CancellationToken cancellationToken = default);
    Task<List<Metric>> CalculateMetricsAsync(List<string> metricNames, TimePeriodValue period, Guid? userId = null, UserType? userType = null, CancellationToken cancellationToken = default);
    Task<MetricValue> GetRealTimeMetricAsync(string metricName, Guid? userId = null, CancellationToken cancellationToken = default);

    // KPI management
    Task<List<Metric>> GetKPIsAsync(KPICategory category, Guid? userId = null, UserType? userType = null, CancellationToken cancellationToken = default);
    Task<PerformanceStatus> EvaluateKPIPerformanceAsync(string metricName, Guid? userId = null, CancellationToken cancellationToken = default);
    Task<List<Metric>> GetUnderperformingKPIsAsync(Guid? userId = null, UserType? userType = null, CancellationToken cancellationToken = default);

    // Trend analysis
    Task<TrendDirection> AnalyzeTrendAsync(string metricName, TimePeriodValue period, Guid? userId = null, CancellationToken cancellationToken = default);
    Task<Dictionary<string, TrendDirection>> AnalyzeMultipleTrendsAsync(List<string> metricNames, TimePeriodValue period, Guid? userId = null, CancellationToken cancellationToken = default);
    Task<decimal> CalculateGrowthRateAsync(string metricName, TimePeriodValue currentPeriod, TimePeriodValue previousPeriod, Guid? userId = null, CancellationToken cancellationToken = default);

    // Comparative analysis
    Task<Dictionary<string, decimal>> ComparePerformanceAsync(List<Guid> userIds, string metricName, TimePeriodValue period, CancellationToken cancellationToken = default);
    Task<Dictionary<string, object>> BenchmarkPerformanceAsync(Guid userId, UserType userType, List<string> metricNames, TimePeriodValue period, CancellationToken cancellationToken = default);
}

/// <summary>
/// Dashboard management domain service interface
/// </summary>
public interface IDashboardService
{
    // Dashboard management
    Task<Dashboard> CreateDashboardAsync(string name, string description, DashboardType type, Guid? userId = null, UserType? userType = null, CancellationToken cancellationToken = default);
    Task<Dashboard> GetDashboardAsync(Guid dashboardId, CancellationToken cancellationToken = default);
    Task<List<Dashboard>> GetUserDashboardsAsync(Guid userId, UserType userType, CancellationToken cancellationToken = default);
    Task<Dashboard> GetDefaultDashboardAsync(UserType userType, CancellationToken cancellationToken = default);

    // Dashboard data
    Task<Dictionary<string, object>> GetDashboardDataAsync(Guid dashboardId, TimePeriodValue? period = null, CancellationToken cancellationToken = default);
    Task<Dictionary<string, object>> GetWidgetDataAsync(Guid dashboardId, Guid widgetId, TimePeriodValue? period = null, CancellationToken cancellationToken = default);
    Task RefreshDashboardDataAsync(Guid dashboardId, CancellationToken cancellationToken = default);

    // Widget management
    Task AddWidgetToDashboardAsync(Guid dashboardId, DashboardWidget widget, CancellationToken cancellationToken = default);
    Task UpdateWidgetAsync(Guid dashboardId, Guid widgetId, string? title = null, Dictionary<string, object>? configuration = null, CancellationToken cancellationToken = default);
    Task RemoveWidgetFromDashboardAsync(Guid dashboardId, Guid widgetId, CancellationToken cancellationToken = default);

    // Dashboard access tracking
    Task RecordDashboardAccessAsync(Guid dashboardId, Guid userId, UserType userType, CancellationToken cancellationToken = default);
}

/// <summary>
/// Report generation domain service interface
/// </summary>
public interface IReportService
{
    // Report generation
    Task<Report> GenerateReportAsync(string name, string description, ReportType type, Guid generatedBy, UserType userType, TimePeriodValue period, Dictionary<string, object>? parameters = null, CancellationToken cancellationToken = default);
    Task<Report> GenerateCustomReportAsync(string name, string description, Guid generatedBy, UserType userType, TimePeriodValue period, Dictionary<string, object> configuration, CancellationToken cancellationToken = default);
    Task<List<Report>> GetUserReportsAsync(Guid userId, UserType userType, CancellationToken cancellationToken = default);

    // Report sections
    Task AddReportSectionAsync(Guid reportId, ReportSection section, CancellationToken cancellationToken = default);
    Task<Dictionary<string, object>> GenerateSectionDataAsync(string sectionType, Dictionary<string, object> configuration, TimePeriodValue period, CancellationToken cancellationToken = default);

    // Report export
    Task<ReportExport> ExportReportAsync(Guid reportId, ExportFormat format, Guid exportedBy, CancellationToken cancellationToken = default);
    Task<byte[]> GetExportDataAsync(Guid exportId, CancellationToken cancellationToken = default);
    Task<List<ReportExport>> GetReportExportsAsync(Guid reportId, CancellationToken cancellationToken = default);

    // Scheduled reports
    Task ScheduleReportAsync(Guid reportId, string cronExpression, CancellationToken cancellationToken = default);
    Task<List<Report>> GetScheduledReportsAsync(CancellationToken cancellationToken = default);
    Task ProcessScheduledReportsAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Alert management domain service interface
/// </summary>
public interface IAlertService
{
    // Alert management
    Task<Alert> CreateAlertAsync(string name, string description, AlertSeverity severity, string message, Guid? metricId = null, Dictionary<string, object>? context = null, CancellationToken cancellationToken = default);
    Task<List<Alert>> GetActiveAlertsAsync(Guid? userId = null, UserType? userType = null, CancellationToken cancellationToken = default);
    Task<List<Alert>> GetAlertsAsync(AlertSeverity? severity = null, bool? isActive = null, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default);

    // Alert actions
    Task AcknowledgeAlertAsync(Guid alertId, Guid acknowledgedBy, CancellationToken cancellationToken = default);
    Task ResolveAlertAsync(Guid alertId, Guid? resolvedBy = null, string? resolution = null, CancellationToken cancellationToken = default);

    // Alert rules
    Task<AlertRule> CreateAlertRuleAsync(string name, string description, string metricName, KPICategory category, AlertSeverity severity, string condition, decimal thresholdValue, TimeSpan evaluationWindow, Guid createdBy, UserType userType, CancellationToken cancellationToken = default);
    Task<List<AlertRule>> GetAlertRulesAsync(Guid? userId = null, UserType? userType = null, bool? isEnabled = null, CancellationToken cancellationToken = default);
    Task EvaluateAlertRulesAsync(CancellationToken cancellationToken = default);

    // Alert notifications
    Task NotifyAlertAsync(Guid alertId, List<Guid> userIds, CancellationToken cancellationToken = default);
    Task ProcessAlertEscalationAsync(CancellationToken cancellationToken = default);
}


