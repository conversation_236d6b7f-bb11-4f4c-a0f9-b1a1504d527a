using Shared.Domain.Common;
using AuditCompliance.Domain.Entities;

namespace AuditCompliance.Domain.Events;

/// <summary>
/// Domain event raised when a service provider rating is created
/// </summary>
public class ServiceProviderRatingCreatedEvent : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
    public ServiceProviderRating Rating { get; }

    public ServiceProviderRatingCreatedEvent(ServiceProviderRating rating)
    {
        Rating = rating ?? throw new ArgumentNullException(nameof(rating));
    }
}

/// <summary>
/// Domain event raised when a service provider rating is submitted
/// </summary>
public class ServiceProviderRatingSubmittedEvent : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
    public ServiceProviderRating Rating { get; }

    public ServiceProviderRatingSubmittedEvent(ServiceProviderRating rating)
    {
        Rating = rating ?? throw new ArgumentNullException(nameof(rating));
    }
}

/// <summary>
/// Domain event raised when a service provider rating is flagged for review
/// </summary>
public class ServiceProviderRatingFlaggedEvent : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
    public ServiceProviderRating Rating { get; }
    public string Reason { get; }

    public ServiceProviderRatingFlaggedEvent(ServiceProviderRating rating, string reason)
    {
        Rating = rating ?? throw new ArgumentNullException(nameof(rating));
        Reason = reason ?? throw new ArgumentNullException(nameof(reason));
    }
}

/// <summary>
/// Domain event raised when a service provider rating is removed
/// </summary>
public class ServiceProviderRatingRemovedEvent : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
    public ServiceProviderRating Rating { get; }
    public string Reason { get; }

    public ServiceProviderRatingRemovedEvent(ServiceProviderRating rating, string reason)
    {
        Rating = rating ?? throw new ArgumentNullException(nameof(rating));
        Reason = reason ?? throw new ArgumentNullException(nameof(reason));
    }
}

/// <summary>
/// Domain event raised when a service issue is reported
/// </summary>
public class ServiceIssueReportedEvent : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
    public ServiceProviderRating Rating { get; }
    public ServiceIssue Issue { get; }

    public ServiceIssueReportedEvent(ServiceProviderRating rating, ServiceIssue issue)
    {
        Rating = rating ?? throw new ArgumentNullException(nameof(rating));
        Issue = issue ?? throw new ArgumentNullException(nameof(issue));
    }
}

/// <summary>
/// Domain event raised when a preferred provider is added
/// </summary>
public class PreferredProviderAddedEvent : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
    public PreferredProviderNetwork PreferredProvider { get; }

    public PreferredProviderAddedEvent(PreferredProviderNetwork preferredProvider)
    {
        PreferredProvider = preferredProvider ?? throw new ArgumentNullException(nameof(preferredProvider));
    }
}

/// <summary>
/// Domain event raised when a preferred provider is removed
/// </summary>
public class PreferredProviderRemovedEvent : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
    public PreferredProviderNetwork PreferredProvider { get; }
    public string Reason { get; }

    public PreferredProviderRemovedEvent(PreferredProviderNetwork preferredProvider, string reason)
    {
        PreferredProvider = preferredProvider ?? throw new ArgumentNullException(nameof(preferredProvider));
        Reason = reason ?? throw new ArgumentNullException(nameof(reason));
    }
}
