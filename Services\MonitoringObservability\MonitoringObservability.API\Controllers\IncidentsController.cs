using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using MediatR;
using MonitoringObservability.Application.Commands;
using MonitoringObservability.Application.Queries;
using MonitoringObservability.Application.DTOs;
using MonitoringObservability.Domain.Enums;

namespace MonitoringObservability.API.Controllers;

[ApiController]
[Route("api/v{version:apiVersion}/[controller]")]
[ApiVersion("1.0")]
[Authorize]
public class IncidentsController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<IncidentsController> _logger;

    public IncidentsController(IMediator mediator, ILogger<IncidentsController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get all incidents with optional filtering
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<IncidentDto>>> GetIncidents([FromQuery] GetIncidentsRequest request)
    {
        try
        {
            var query = new GetIncidentsQuery
            {
                ServiceName = request.ServiceName,
                Severity = request.Severity,
                Status = request.Status,
                CreatedAfter = request.CreatedAfter,
                CreatedBefore = request.CreatedBefore,
                AssignedToUserId = request.AssignedToUserId,
                Skip = request.Skip,
                Take = request.Take,
                SortBy = request.SortBy,
                SortDescending = request.SortDescending
            };

            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get incidents");
            return StatusCode(500, "Failed to retrieve incidents");
        }
    }

    /// <summary>
    /// Get incident by ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<IncidentDto>> GetIncident(Guid id, [FromQuery] bool includeDetails = false)
    {
        try
        {
            var query = new GetIncidentByIdQuery { IncidentId = id, IncludeDetails = includeDetails };
            var result = await _mediator.Send(query);
            
            if (result == null)
                return NotFound();

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get incident {IncidentId}", id);
            return StatusCode(500, "Failed to retrieve incident");
        }
    }

    /// <summary>
    /// Create a new incident
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Admin,Monitor,Manager")]
    public async Task<ActionResult<IncidentDto>> CreateIncident([FromBody] CreateIncidentRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();

            var command = new CreateIncidentCommand
            {
                Title = request.Title,
                Description = request.Description,
                Severity = request.Severity,
                ServiceName = request.ServiceName,
                CreatedByUserId = userId,
                Component = request.Component,
                ImpactLevel = request.ImpactLevel,
                UrgencyLevel = request.UrgencyLevel,
                ImpactDescription = request.ImpactDescription,
                Tags = request.Tags
            };

            var result = await _mediator.Send(command);
            return CreatedAtAction(nameof(GetIncident), new { id = result.Id }, result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create incident");
            return StatusCode(500, "Failed to create incident");
        }
    }

    /// <summary>
    /// Update incident status
    /// </summary>
    [HttpPost("{id}/status")]
    public async Task<ActionResult<IncidentDto>> UpdateIncidentStatus(Guid id, [FromBody] UpdateIncidentStatusRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();

            var command = new UpdateIncidentStatusCommand
            {
                IncidentId = id,
                NewStatus = request.NewStatus,
                UpdateMessage = request.UpdateMessage,
                UpdatedByUserId = userId
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update incident status {IncidentId}", id);
            return StatusCode(500, "Failed to update incident status");
        }
    }

    /// <summary>
    /// Assign incident to a user
    /// </summary>
    [HttpPost("{id}/assign")]
    public async Task<ActionResult<IncidentDto>> AssignIncident(Guid id, [FromBody] AssignIncidentRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();

            var command = new AssignIncidentCommand
            {
                IncidentId = id,
                AssignedToUserId = request.AssignedToUserId,
                AssignedToUserName = request.AssignedToUserName,
                AssignedByUserId = userId
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to assign incident {IncidentId}", id);
            return StatusCode(500, "Failed to assign incident");
        }
    }

    /// <summary>
    /// Update incident severity
    /// </summary>
    [HttpPost("{id}/severity")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<IncidentDto>> UpdateIncidentSeverity(Guid id, [FromBody] UpdateIncidentSeverityRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();

            var command = new UpdateIncidentSeverityCommand
            {
                IncidentId = id,
                NewSeverity = request.NewSeverity,
                Reason = request.Reason,
                UpdatedByUserId = userId
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update incident severity {IncidentId}", id);
            return StatusCode(500, "Failed to update incident severity");
        }
    }

    /// <summary>
    /// Update incident impact assessment
    /// </summary>
    [HttpPost("{id}/impact")]
    public async Task<ActionResult<IncidentDto>> UpdateIncidentImpact(Guid id, [FromBody] UpdateIncidentImpactRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();

            var command = new UpdateIncidentImpactCommand
            {
                IncidentId = id,
                ImpactLevel = request.ImpactLevel,
                UrgencyLevel = request.UrgencyLevel,
                ImpactDescription = request.ImpactDescription,
                UpdatedByUserId = userId
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update incident impact {IncidentId}", id);
            return StatusCode(500, "Failed to update incident impact");
        }
    }

    /// <summary>
    /// Resolve an incident
    /// </summary>
    [HttpPost("{id}/resolve")]
    public async Task<ActionResult<IncidentDto>> ResolveIncident(Guid id, [FromBody] ResolveIncidentRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();

            var command = new ResolveIncidentCommand
            {
                IncidentId = id,
                ResolutionSummary = request.ResolutionSummary,
                RootCause = request.RootCause,
                PreventiveMeasures = request.PreventiveMeasures,
                ResolvedByUserId = userId
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to resolve incident {IncidentId}", id);
            return StatusCode(500, "Failed to resolve incident");
        }
    }

    /// <summary>
    /// Add comment to an incident
    /// </summary>
    [HttpPost("{id}/comments")]
    public async Task<ActionResult<IncidentCommentDto>> AddComment(Guid id, [FromBody] AddIncidentCommentRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            var userName = GetCurrentUserName();

            var command = new AddIncidentCommentCommand
            {
                IncidentId = id,
                UserId = userId,
                UserName = userName,
                Comment = request.Comment,
                IsInternal = request.IsInternal
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to add comment to incident {IncidentId}", id);
            return StatusCode(500, "Failed to add comment");
        }
    }

    /// <summary>
    /// Link alert to incident
    /// </summary>
    [HttpPost("{id}/alerts/{alertId}")]
    public async Task<ActionResult<IncidentDto>> LinkAlert(Guid id, Guid alertId, [FromBody] LinkAlertRequest request)
    {
        try
        {
            var command = new LinkAlertToIncidentCommand
            {
                IncidentId = id,
                AlertId = alertId,
                AlertTitle = request.AlertTitle
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to link alert {AlertId} to incident {IncidentId}", alertId, id);
            return StatusCode(500, "Failed to link alert to incident");
        }
    }

    /// <summary>
    /// Unlink alert from incident
    /// </summary>
    [HttpDelete("{id}/alerts/{alertId}")]
    public async Task<ActionResult<IncidentDto>> UnlinkAlert(Guid id, Guid alertId)
    {
        try
        {
            var command = new UnlinkAlertFromIncidentCommand
            {
                IncidentId = id,
                AlertId = alertId
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to unlink alert {AlertId} from incident {IncidentId}", alertId, id);
            return StatusCode(500, "Failed to unlink alert from incident");
        }
    }

    /// <summary>
    /// Create incident from alert
    /// </summary>
    [HttpPost("from-alert/{alertId}")]
    [Authorize(Roles = "Admin,Monitor,Manager")]
    public async Task<ActionResult<IncidentDto>> CreateIncidentFromAlert(Guid alertId, [FromBody] CreateIncidentFromAlertRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();

            var command = new CreateIncidentFromAlertCommand
            {
                AlertId = alertId,
                CreatedByUserId = userId,
                AdditionalDescription = request.AdditionalDescription,
                OverrideSeverity = request.OverrideSeverity
            };

            var result = await _mediator.Send(command);
            return CreatedAtAction(nameof(GetIncident), new { id = result.Id }, result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create incident from alert {AlertId}", alertId);
            return StatusCode(500, "Failed to create incident from alert");
        }
    }

    /// <summary>
    /// Get open incidents
    /// </summary>
    [HttpGet("open")]
    public async Task<ActionResult<IEnumerable<IncidentDto>>> GetOpenIncidents([FromQuery] string? serviceName = null, [FromQuery] IncidentSeverity? minimumSeverity = null)
    {
        try
        {
            var query = new GetOpenIncidentsQuery
            {
                ServiceName = serviceName,
                MinimumSeverity = minimumSeverity
            };

            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get open incidents");
            return StatusCode(500, "Failed to retrieve open incidents");
        }
    }

    /// <summary>
    /// Get critical incidents
    /// </summary>
    [HttpGet("critical")]
    public async Task<ActionResult<IEnumerable<IncidentDto>>> GetCriticalIncidents([FromQuery] string? serviceName = null)
    {
        try
        {
            var query = new GetCriticalIncidentsQuery { ServiceName = serviceName };
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get critical incidents");
            return StatusCode(500, "Failed to retrieve critical incidents");
        }
    }

    /// <summary>
    /// Get incidents assigned to current user
    /// </summary>
    [HttpGet("assigned-to-me")]
    public async Task<ActionResult<IEnumerable<IncidentDto>>> GetMyIncidents([FromQuery] IncidentStatus? status = null)
    {
        try
        {
            var userId = GetCurrentUserId();
            var query = new GetIncidentsAssignedToUserQuery { UserId = userId, Status = status };
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get user incidents");
            return StatusCode(500, "Failed to retrieve user incidents");
        }
    }

    /// <summary>
    /// Get incident statistics
    /// </summary>
    [HttpGet("statistics")]
    public async Task<ActionResult<IncidentStatisticsDto>> GetIncidentStatistics([FromQuery] DateTime? fromDate = null, [FromQuery] string? serviceName = null)
    {
        try
        {
            var severityCountQuery = new GetIncidentCountBySeverityQuery { FromDate = fromDate, ServiceName = serviceName };
            var serviceCountQuery = new GetIncidentCountByServiceQuery { FromDate = fromDate };
            var avgResolutionQuery = new GetAverageResolutionTimeQuery { FromDate = fromDate, ServiceName = serviceName };

            var severityCount = await _mediator.Send(severityCountQuery);
            var serviceCount = await _mediator.Send(serviceCountQuery);
            var avgResolutionTime = await _mediator.Send(avgResolutionQuery);

            var statistics = new IncidentStatisticsDto
            {
                CountBySeverity = severityCount,
                CountByService = serviceCount,
                AverageResolutionTimeMinutes = avgResolutionTime,
                TotalIncidents = severityCount.Values.Sum(),
                GeneratedAt = DateTime.UtcNow
            };

            return Ok(statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get incident statistics");
            return StatusCode(500, "Failed to retrieve incident statistics");
        }
    }

    /// <summary>
    /// Add custom field to incident
    /// </summary>
    [HttpPost("{id}/custom-fields")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<IncidentDto>> AddCustomField(Guid id, [FromBody] AddCustomFieldRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();

            var command = new AddIncidentCustomFieldCommand
            {
                IncidentId = id,
                FieldName = request.FieldName,
                FieldValue = request.FieldValue,
                UpdatedByUserId = userId
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to add custom field to incident {IncidentId}", id);
            return StatusCode(500, "Failed to add custom field");
        }
    }

    /// <summary>
    /// Remove custom field from incident
    /// </summary>
    [HttpDelete("{id}/custom-fields/{fieldName}")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<IncidentDto>> RemoveCustomField(Guid id, string fieldName)
    {
        try
        {
            var userId = GetCurrentUserId();

            var command = new RemoveIncidentCustomFieldCommand
            {
                IncidentId = id,
                FieldName = fieldName,
                UpdatedByUserId = userId
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to remove custom field from incident {IncidentId}", id);
            return StatusCode(500, "Failed to remove custom field");
        }
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst("sub") ?? User.FindFirst("user_id");
        if (userIdClaim != null && Guid.TryParse(userIdClaim.Value, out var userId))
        {
            return userId;
        }
        throw new UnauthorizedAccessException("User ID not found in token");
    }

    private string GetCurrentUserName()
    {
        var userNameClaim = User.FindFirst("name") ?? User.FindFirst("username");
        return userNameClaim?.Value ?? "Unknown User";
    }
}

// Request DTOs
public class GetIncidentsRequest
{
    public string? ServiceName { get; set; }
    public IncidentSeverity? Severity { get; set; }
    public IncidentStatus? Status { get; set; }
    public DateTime? CreatedAfter { get; set; }
    public DateTime? CreatedBefore { get; set; }
    public Guid? AssignedToUserId { get; set; }
    public int Skip { get; set; } = 0;
    public int Take { get; set; } = 50;
    public string? SortBy { get; set; } = "CreatedAt";
    public bool SortDescending { get; set; } = true;
}

public class CreateIncidentRequest
{
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public IncidentSeverity Severity { get; set; }
    public string ServiceName { get; set; } = string.Empty;
    public string? Component { get; set; }
    public int ImpactLevel { get; set; } = 3;
    public int UrgencyLevel { get; set; } = 3;
    public string? ImpactDescription { get; set; }
    public Dictionary<string, string>? Tags { get; set; }
}

public class UpdateIncidentStatusRequest
{
    public IncidentStatus NewStatus { get; set; }
    public string UpdateMessage { get; set; } = string.Empty;
}

public class AssignIncidentRequest
{
    public Guid AssignedToUserId { get; set; }
    public string AssignedToUserName { get; set; } = string.Empty;
}

public class UpdateIncidentSeverityRequest
{
    public IncidentSeverity NewSeverity { get; set; }
    public string Reason { get; set; } = string.Empty;
}

public class UpdateIncidentImpactRequest
{
    public int ImpactLevel { get; set; }
    public int UrgencyLevel { get; set; }
    public string? ImpactDescription { get; set; }
}

public class ResolveIncidentRequest
{
    public string ResolutionSummary { get; set; } = string.Empty;
    public string? RootCause { get; set; }
    public string? PreventiveMeasures { get; set; }
}

public class AddIncidentCommentRequest
{
    public string Comment { get; set; } = string.Empty;
    public bool IsInternal { get; set; } = false;
}

public class LinkAlertRequest
{
    public string AlertTitle { get; set; } = string.Empty;
}

public class CreateIncidentFromAlertRequest
{
    public string? AdditionalDescription { get; set; }
    public IncidentSeverity? OverrideSeverity { get; set; }
}

public class AddCustomFieldRequest
{
    public string FieldName { get; set; } = string.Empty;
    public object FieldValue { get; set; } = null!;
}
