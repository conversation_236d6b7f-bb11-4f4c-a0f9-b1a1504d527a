﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FinancialPayment.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.EnsureSchema(
                name: "financial_payment");

            migrationBuilder.CreateTable(
                name: "commissions",
                schema: "financial_payment",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    order_id = table.Column<Guid>(type: "uuid", nullable: false),
                    broker_id = table.Column<Guid>(type: "uuid", nullable: false),
                    transport_company_id = table.Column<Guid>(type: "uuid", nullable: false),
                    carrier_id = table.Column<Guid>(type: "uuid", nullable: false),
                    order_amount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    order_currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    commission_type = table.Column<int>(type: "integer", nullable: false),
                    commission_rate = table.Column<decimal>(type: "numeric(18,4)", nullable: false),
                    commission_minimum_amount = table.Column<decimal>(type: "numeric(18,2)", nullable: true),
                    commission_maximum_amount = table.Column<decimal>(type: "numeric(18,2)", nullable: true),
                    commission_description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    calculated_amount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    calculated_currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    status = table.Column<int>(type: "integer", nullable: false),
                    processed_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    notes = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_commissions", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "escrow_accounts",
                schema: "financial_payment",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    order_id = table.Column<Guid>(type: "uuid", nullable: false),
                    transport_company_id = table.Column<Guid>(type: "uuid", nullable: false),
                    broker_id = table.Column<Guid>(type: "uuid", nullable: false),
                    carrier_id = table.Column<Guid>(type: "uuid", nullable: false),
                    escrow_account_number = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    total_amount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    total_currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    available_amount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    available_currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    reserved_amount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    reserved_currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    status = table.Column<int>(type: "integer", nullable: false),
                    funded_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    released_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    release_reason = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    notes = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_escrow_accounts", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "commission_adjustments",
                schema: "financial_payment",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    commission_id = table.Column<Guid>(type: "uuid", nullable: false),
                    amount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    type = table.Column<int>(type: "integer", nullable: false),
                    reason = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    created_by = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_commission_adjustments", x => x.id);
                    table.ForeignKey(
                        name: "FK_commission_adjustments_commissions_commission_id",
                        column: x => x.commission_id,
                        principalSchema: "financial_payment",
                        principalTable: "commissions",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "escrow_milestones",
                schema: "financial_payment",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    escrow_account_id = table.Column<Guid>(type: "uuid", nullable: false),
                    description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    amount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    due_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    status = table.Column<int>(type: "integer", nullable: false),
                    completed_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    completion_notes = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_escrow_milestones", x => x.id);
                    table.ForeignKey(
                        name: "FK_escrow_milestones_escrow_accounts_escrow_account_id",
                        column: x => x.escrow_account_id,
                        principalSchema: "financial_payment",
                        principalTable: "escrow_accounts",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "escrow_transactions",
                schema: "financial_payment",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    escrow_account_id = table.Column<Guid>(type: "uuid", nullable: false),
                    type = table.Column<int>(type: "integer", nullable: false),
                    amount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    participant_id = table.Column<Guid>(type: "uuid", nullable: true),
                    payment_gateway_transaction_id = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    notes = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    processed_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    status = table.Column<int>(type: "integer", nullable: false),
                    failure_reason = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_escrow_transactions", x => x.id);
                    table.ForeignKey(
                        name: "FK_escrow_transactions_escrow_accounts_escrow_account_id",
                        column: x => x.escrow_account_id,
                        principalSchema: "financial_payment",
                        principalTable: "escrow_accounts",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "settlements",
                schema: "financial_payment",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    order_id = table.Column<Guid>(type: "uuid", nullable: false),
                    trip_id = table.Column<Guid>(type: "uuid", nullable: false),
                    escrow_account_id = table.Column<Guid>(type: "uuid", nullable: false),
                    settlement_number = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    total_amount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    total_currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    status = table.Column<int>(type: "integer", nullable: false),
                    processed_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    notes = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_settlements", x => x.id);
                    table.ForeignKey(
                        name: "FK_settlements_escrow_accounts_escrow_account_id",
                        column: x => x.escrow_account_id,
                        principalSchema: "financial_payment",
                        principalTable: "escrow_accounts",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "payment_disputes",
                schema: "financial_payment",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    order_id = table.Column<Guid>(type: "uuid", nullable: false),
                    escrow_account_id = table.Column<Guid>(type: "uuid", nullable: true),
                    settlement_id = table.Column<Guid>(type: "uuid", nullable: true),
                    initiated_by = table.Column<Guid>(type: "uuid", nullable: false),
                    initiator_role = table.Column<int>(type: "integer", nullable: false),
                    dispute_number = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    title = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    description = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: false),
                    disputed_amount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    disputed_currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    category = table.Column<int>(type: "integer", nullable: false),
                    status = table.Column<int>(type: "integer", nullable: false),
                    priority = table.Column<int>(type: "integer", nullable: false),
                    resolved_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    resolution = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    resolved_amount = table.Column<decimal>(type: "numeric(18,2)", nullable: true),
                    resolved_currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    resolved_by = table.Column<Guid>(type: "uuid", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_payment_disputes", x => x.id);
                    table.ForeignKey(
                        name: "FK_payment_disputes_escrow_accounts_escrow_account_id",
                        column: x => x.escrow_account_id,
                        principalSchema: "financial_payment",
                        principalTable: "escrow_accounts",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_payment_disputes_settlements_settlement_id",
                        column: x => x.settlement_id,
                        principalSchema: "financial_payment",
                        principalTable: "settlements",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "settlement_distributions",
                schema: "financial_payment",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    settlement_id = table.Column<Guid>(type: "uuid", nullable: false),
                    recipient_id = table.Column<Guid>(type: "uuid", nullable: false),
                    recipient_role = table.Column<int>(type: "integer", nullable: false),
                    amount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    type = table.Column<int>(type: "integer", nullable: false),
                    description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    status = table.Column<int>(type: "integer", nullable: false),
                    processed_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    payment_gateway_transaction_id = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    failure_reason = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_settlement_distributions", x => x.id);
                    table.ForeignKey(
                        name: "FK_settlement_distributions_settlements_settlement_id",
                        column: x => x.settlement_id,
                        principalSchema: "financial_payment",
                        principalTable: "settlements",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "dispute_comments",
                schema: "financial_payment",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    dispute_id = table.Column<Guid>(type: "uuid", nullable: false),
                    author_id = table.Column<Guid>(type: "uuid", nullable: false),
                    author_role = table.Column<int>(type: "integer", nullable: false),
                    content = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: false),
                    is_internal = table.Column<bool>(type: "boolean", nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_dispute_comments", x => x.id);
                    table.ForeignKey(
                        name: "FK_dispute_comments_payment_disputes_dispute_id",
                        column: x => x.dispute_id,
                        principalSchema: "financial_payment",
                        principalTable: "payment_disputes",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "dispute_documents",
                schema: "financial_payment",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    dispute_id = table.Column<Guid>(type: "uuid", nullable: false),
                    file_name = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    file_url = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    uploaded_by = table.Column<Guid>(type: "uuid", nullable: false),
                    uploaded_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_dispute_documents", x => x.id);
                    table.ForeignKey(
                        name: "FK_dispute_documents_payment_disputes_dispute_id",
                        column: x => x.dispute_id,
                        principalSchema: "financial_payment",
                        principalTable: "payment_disputes",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "ix_commission_adjustments_commission_id",
                schema: "financial_payment",
                table: "commission_adjustments",
                column: "commission_id");

            migrationBuilder.CreateIndex(
                name: "ix_commission_adjustments_created_at",
                schema: "financial_payment",
                table: "commission_adjustments",
                column: "created_at");

            migrationBuilder.CreateIndex(
                name: "ix_commission_adjustments_type",
                schema: "financial_payment",
                table: "commission_adjustments",
                column: "type");

            migrationBuilder.CreateIndex(
                name: "ix_commissions_broker_id",
                schema: "financial_payment",
                table: "commissions",
                column: "broker_id");

            migrationBuilder.CreateIndex(
                name: "ix_commissions_carrier_id",
                schema: "financial_payment",
                table: "commissions",
                column: "carrier_id");

            migrationBuilder.CreateIndex(
                name: "ix_commissions_created_at",
                schema: "financial_payment",
                table: "commissions",
                column: "created_at");

            migrationBuilder.CreateIndex(
                name: "ix_commissions_order_id",
                schema: "financial_payment",
                table: "commissions",
                column: "order_id",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_commissions_status",
                schema: "financial_payment",
                table: "commissions",
                column: "status");

            migrationBuilder.CreateIndex(
                name: "ix_commissions_transport_company_id",
                schema: "financial_payment",
                table: "commissions",
                column: "transport_company_id");

            migrationBuilder.CreateIndex(
                name: "ix_dispute_comments_author_id",
                schema: "financial_payment",
                table: "dispute_comments",
                column: "author_id");

            migrationBuilder.CreateIndex(
                name: "ix_dispute_comments_author_role",
                schema: "financial_payment",
                table: "dispute_comments",
                column: "author_role");

            migrationBuilder.CreateIndex(
                name: "ix_dispute_comments_created_at",
                schema: "financial_payment",
                table: "dispute_comments",
                column: "created_at");

            migrationBuilder.CreateIndex(
                name: "ix_dispute_comments_dispute_id",
                schema: "financial_payment",
                table: "dispute_comments",
                column: "dispute_id");

            migrationBuilder.CreateIndex(
                name: "ix_dispute_comments_is_internal",
                schema: "financial_payment",
                table: "dispute_comments",
                column: "is_internal");

            migrationBuilder.CreateIndex(
                name: "ix_dispute_documents_dispute_id",
                schema: "financial_payment",
                table: "dispute_documents",
                column: "dispute_id");

            migrationBuilder.CreateIndex(
                name: "ix_dispute_documents_uploaded_at",
                schema: "financial_payment",
                table: "dispute_documents",
                column: "uploaded_at");

            migrationBuilder.CreateIndex(
                name: "ix_dispute_documents_uploaded_by",
                schema: "financial_payment",
                table: "dispute_documents",
                column: "uploaded_by");

            migrationBuilder.CreateIndex(
                name: "ix_escrow_accounts_account_number",
                schema: "financial_payment",
                table: "escrow_accounts",
                column: "escrow_account_number",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_escrow_accounts_broker_id",
                schema: "financial_payment",
                table: "escrow_accounts",
                column: "broker_id");

            migrationBuilder.CreateIndex(
                name: "ix_escrow_accounts_carrier_id",
                schema: "financial_payment",
                table: "escrow_accounts",
                column: "carrier_id");

            migrationBuilder.CreateIndex(
                name: "ix_escrow_accounts_created_at",
                schema: "financial_payment",
                table: "escrow_accounts",
                column: "created_at");

            migrationBuilder.CreateIndex(
                name: "ix_escrow_accounts_order_id",
                schema: "financial_payment",
                table: "escrow_accounts",
                column: "order_id",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_escrow_accounts_status",
                schema: "financial_payment",
                table: "escrow_accounts",
                column: "status");

            migrationBuilder.CreateIndex(
                name: "ix_escrow_accounts_transport_company_id",
                schema: "financial_payment",
                table: "escrow_accounts",
                column: "transport_company_id");

            migrationBuilder.CreateIndex(
                name: "ix_escrow_milestones_created_at",
                schema: "financial_payment",
                table: "escrow_milestones",
                column: "created_at");

            migrationBuilder.CreateIndex(
                name: "ix_escrow_milestones_due_date",
                schema: "financial_payment",
                table: "escrow_milestones",
                column: "due_date");

            migrationBuilder.CreateIndex(
                name: "ix_escrow_milestones_escrow_account_id",
                schema: "financial_payment",
                table: "escrow_milestones",
                column: "escrow_account_id");

            migrationBuilder.CreateIndex(
                name: "ix_escrow_milestones_status",
                schema: "financial_payment",
                table: "escrow_milestones",
                column: "status");

            migrationBuilder.CreateIndex(
                name: "ix_escrow_transactions_created_at",
                schema: "financial_payment",
                table: "escrow_transactions",
                column: "created_at");

            migrationBuilder.CreateIndex(
                name: "ix_escrow_transactions_escrow_account_id",
                schema: "financial_payment",
                table: "escrow_transactions",
                column: "escrow_account_id");

            migrationBuilder.CreateIndex(
                name: "ix_escrow_transactions_gateway_transaction_id",
                schema: "financial_payment",
                table: "escrow_transactions",
                column: "payment_gateway_transaction_id");

            migrationBuilder.CreateIndex(
                name: "ix_escrow_transactions_participant_id",
                schema: "financial_payment",
                table: "escrow_transactions",
                column: "participant_id");

            migrationBuilder.CreateIndex(
                name: "ix_escrow_transactions_status",
                schema: "financial_payment",
                table: "escrow_transactions",
                column: "status");

            migrationBuilder.CreateIndex(
                name: "ix_escrow_transactions_type",
                schema: "financial_payment",
                table: "escrow_transactions",
                column: "type");

            migrationBuilder.CreateIndex(
                name: "ix_payment_disputes_category",
                schema: "financial_payment",
                table: "payment_disputes",
                column: "category");

            migrationBuilder.CreateIndex(
                name: "ix_payment_disputes_created_at",
                schema: "financial_payment",
                table: "payment_disputes",
                column: "created_at");

            migrationBuilder.CreateIndex(
                name: "ix_payment_disputes_dispute_number",
                schema: "financial_payment",
                table: "payment_disputes",
                column: "dispute_number",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_payment_disputes_escrow_account_id",
                schema: "financial_payment",
                table: "payment_disputes",
                column: "escrow_account_id");

            migrationBuilder.CreateIndex(
                name: "ix_payment_disputes_initiated_by",
                schema: "financial_payment",
                table: "payment_disputes",
                column: "initiated_by");

            migrationBuilder.CreateIndex(
                name: "ix_payment_disputes_initiator_role",
                schema: "financial_payment",
                table: "payment_disputes",
                column: "initiator_role");

            migrationBuilder.CreateIndex(
                name: "ix_payment_disputes_order_id",
                schema: "financial_payment",
                table: "payment_disputes",
                column: "order_id");

            migrationBuilder.CreateIndex(
                name: "ix_payment_disputes_priority",
                schema: "financial_payment",
                table: "payment_disputes",
                column: "priority");

            migrationBuilder.CreateIndex(
                name: "ix_payment_disputes_settlement_id",
                schema: "financial_payment",
                table: "payment_disputes",
                column: "settlement_id");

            migrationBuilder.CreateIndex(
                name: "ix_payment_disputes_status",
                schema: "financial_payment",
                table: "payment_disputes",
                column: "status");

            migrationBuilder.CreateIndex(
                name: "ix_settlement_distributions_created_at",
                schema: "financial_payment",
                table: "settlement_distributions",
                column: "created_at");

            migrationBuilder.CreateIndex(
                name: "ix_settlement_distributions_gateway_transaction_id",
                schema: "financial_payment",
                table: "settlement_distributions",
                column: "payment_gateway_transaction_id");

            migrationBuilder.CreateIndex(
                name: "ix_settlement_distributions_recipient_id",
                schema: "financial_payment",
                table: "settlement_distributions",
                column: "recipient_id");

            migrationBuilder.CreateIndex(
                name: "ix_settlement_distributions_recipient_role",
                schema: "financial_payment",
                table: "settlement_distributions",
                column: "recipient_role");

            migrationBuilder.CreateIndex(
                name: "ix_settlement_distributions_settlement_id",
                schema: "financial_payment",
                table: "settlement_distributions",
                column: "settlement_id");

            migrationBuilder.CreateIndex(
                name: "ix_settlement_distributions_status",
                schema: "financial_payment",
                table: "settlement_distributions",
                column: "status");

            migrationBuilder.CreateIndex(
                name: "ix_settlement_distributions_type",
                schema: "financial_payment",
                table: "settlement_distributions",
                column: "type");

            migrationBuilder.CreateIndex(
                name: "ix_settlements_created_at",
                schema: "financial_payment",
                table: "settlements",
                column: "created_at");

            migrationBuilder.CreateIndex(
                name: "ix_settlements_escrow_account_id",
                schema: "financial_payment",
                table: "settlements",
                column: "escrow_account_id");

            migrationBuilder.CreateIndex(
                name: "ix_settlements_order_id",
                schema: "financial_payment",
                table: "settlements",
                column: "order_id",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_settlements_settlement_number",
                schema: "financial_payment",
                table: "settlements",
                column: "settlement_number",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_settlements_status",
                schema: "financial_payment",
                table: "settlements",
                column: "status");

            migrationBuilder.CreateIndex(
                name: "ix_settlements_trip_id",
                schema: "financial_payment",
                table: "settlements",
                column: "trip_id",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "commission_adjustments",
                schema: "financial_payment");

            migrationBuilder.DropTable(
                name: "dispute_comments",
                schema: "financial_payment");

            migrationBuilder.DropTable(
                name: "dispute_documents",
                schema: "financial_payment");

            migrationBuilder.DropTable(
                name: "escrow_milestones",
                schema: "financial_payment");

            migrationBuilder.DropTable(
                name: "escrow_transactions",
                schema: "financial_payment");

            migrationBuilder.DropTable(
                name: "settlement_distributions",
                schema: "financial_payment");

            migrationBuilder.DropTable(
                name: "commissions",
                schema: "financial_payment");

            migrationBuilder.DropTable(
                name: "payment_disputes",
                schema: "financial_payment");

            migrationBuilder.DropTable(
                name: "settlements",
                schema: "financial_payment");

            migrationBuilder.DropTable(
                name: "escrow_accounts",
                schema: "financial_payment");
        }
    }
}
