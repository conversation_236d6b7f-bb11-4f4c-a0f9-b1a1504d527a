using MediatR;
using Microsoft.Extensions.Logging;
using TripManagement.Application.Interfaces;
using TripManagement.Domain.ValueObjects;

namespace TripManagement.Application.Commands.ReportException;

public class ReportExceptionCommandHandler : IRequestHandler<ReportExceptionCommand, Guid>
{
    private readonly ITripRepository _tripRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<ReportExceptionCommandHandler> _logger;
    private readonly IMessageBroker _messageBroker;
    private readonly INotificationService _notificationService;
    private readonly IExceptionAnalysisService _exceptionAnalysisService;

    public ReportExceptionCommandHandler(
        ITripRepository tripRepository,
        IUnitOfWork unitOfWork,
        ILogger<ReportExceptionCommandHandler> logger,
        IMessageBroker messageBroker,
        INotificationService notificationService,
        IExceptionAnalysisService exceptionAnalysisService)
    {
        _tripRepository = tripRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
        _messageBroker = messageBroker;
        _notificationService = notificationService;
        _exceptionAnalysisService = exceptionAnalysisService;
    }

    public async Task<Guid> Handle(ReportExceptionCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Reporting exception for trip {TripId} by user {ReportedBy}", 
                request.TripId, request.ReportedBy);

            // Get the trip
            var trip = await _tripRepository.GetByIdAsync(request.TripId, cancellationToken);
            if (trip == null)
            {
                throw new ArgumentException($"Trip {request.TripId} not found");
            }

            // Convert location if provided
            Location? exceptionLocation = null;
            if (request.Location != null)
            {
                exceptionLocation = new Location(
                    request.Location.Latitude,
                    request.Location.Longitude,
                    request.Location.Altitude,
                    request.Location.Accuracy,
                    request.Location.Address,
                    request.Location.City,
                    request.Location.State,
                    request.Location.Country,
                    request.Location.PostalCode);
            }

            // Add exception to trip
            trip.AddException(request.ExceptionType, request.Description, exceptionLocation);

            // Get the newly added exception
            var addedException = trip.Exceptions.OrderByDescending(e => e.ReportedAt).First();

            // Save changes
            _tripRepository.Update(trip);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Analyze exception for automated resolution suggestions
            var analysisResult = await _exceptionAnalysisService.AnalyzeExceptionAsync(
                request.ExceptionType, 
                request.Description, 
                trip, 
                cancellationToken);

            // Send notifications based on severity
            await SendExceptionNotificationsAsync(trip, addedException, request, analysisResult, cancellationToken);

            // Publish integration event
            await _messageBroker.PublishAsync("trip.exception_reported", new
            {
                TripId = request.TripId,
                ExceptionId = addedException.Id,
                ExceptionType = request.ExceptionType.ToString(),
                Description = request.Description,
                Severity = request.Severity.ToString(),
                ReportedBy = request.ReportedBy,
                ReportedAt = addedException.ReportedAt,
                Location = request.Location,
                RequiresImmediateAttention = request.RequiresImmediateAttention,
                SuggestedResolution = analysisResult?.SuggestedResolution
            }, cancellationToken);

            _logger.LogInformation("Exception {ExceptionId} reported successfully for trip {TripId}", 
                addedException.Id, request.TripId);

            return addedException.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reporting exception for trip {TripId}", request.TripId);
            throw;
        }
    }

    private async Task SendExceptionNotificationsAsync(
        Domain.Entities.Trip trip, 
        Domain.Entities.TripException exception,
        ReportExceptionCommand request,
        ExceptionAnalysisResult? analysisResult,
        CancellationToken cancellationToken)
    {
        var notificationTitle = $"Trip Exception: {request.ExceptionType}";
        var notificationMessage = $"Exception reported for trip {trip.TripNumber}: {request.Description}";

        if (analysisResult?.SuggestedResolution != null)
        {
            notificationMessage += $"\n\nSuggested Resolution: {analysisResult.SuggestedResolution}";
        }

        // Always notify carrier
        await _notificationService.SendNotificationAsync(
            trip.CarrierId,
            notificationTitle,
            notificationMessage,
            cancellationToken);

        // Notify driver if different from reporter
        if (trip.DriverId.HasValue && trip.DriverId != request.ReportedBy)
        {
            await _notificationService.SendNotificationAsync(
                trip.DriverId.Value,
                notificationTitle,
                $"Exception reported for your trip {trip.TripNumber}: {request.Description}",
                cancellationToken);
        }

        // For critical exceptions, send immediate alerts
        if (request.Severity == ExceptionSeverity.Critical || request.RequiresImmediateAttention)
        {
            await _messageBroker.PublishAsync("alert.critical_exception", new
            {
                TripId = request.TripId,
                ExceptionId = exception.Id,
                ExceptionType = request.ExceptionType.ToString(),
                Description = request.Description,
                Location = request.Location,
                ReportedAt = exception.ReportedAt
            }, cancellationToken);
        }
    }
}

public record ExceptionAnalysisResult
{
    public string? SuggestedResolution { get; init; }
    public List<string> RecommendedActions { get; init; } = new();
    public bool RequiresHumanIntervention { get; init; }
    public TimeSpan EstimatedResolutionTime { get; init; }
}
