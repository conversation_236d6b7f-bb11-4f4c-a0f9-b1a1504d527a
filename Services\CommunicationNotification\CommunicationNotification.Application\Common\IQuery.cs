using MediatR;

namespace CommunicationNotification.Application.Common;

/// <summary>
/// Base interface for queries
/// </summary>
/// <typeparam name="TResponse">The response type</typeparam>
public interface IQuery<out TResponse> : IRequest<TResponse>
{
}

/// <summary>
/// Base interface for query handlers
/// </summary>
/// <typeparam name="TQuery">The query type</typeparam>
/// <typeparam name="TResponse">The response type</typeparam>
public interface IQueryHandler<in TQuery, TResponse> : IRequestHandler<TQuery, TResponse>
    where TQuery : IQuery<TResponse>
{
}

/// <summary>
/// Base abstract class for queries
/// </summary>
/// <typeparam name="TResponse">The response type</typeparam>
public abstract class QueryBase<TResponse> : IQuery<TResponse>
{
    /// <summary>
    /// Unique identifier for the query
    /// </summary>
    public Guid QueryId { get; } = Guid.NewGuid();

    /// <summary>
    /// Timestamp when the query was created
    /// </summary>
    public DateTime CreatedAt { get; } = DateTime.UtcNow;

    /// <summary>
    /// User who initiated the query
    /// </summary>
    public Guid? RequestedBy { get; set; }

    /// <summary>
    /// Correlation ID for tracking across services
    /// </summary>
    public string? CorrelationId { get; set; }

    /// <summary>
    /// Additional metadata for the query
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Query result wrapper
/// </summary>
/// <typeparam name="T">The result data type</typeparam>
public class QueryResult<T>
{
    public bool IsSuccess { get; set; }
    public T? Data { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime ExecutedAt { get; set; } = DateTime.UtcNow;
    public Guid QueryId { get; set; }
    public QueryMetadata Metadata { get; set; } = new();

    public static QueryResult<T> Success(T data, Guid queryId)
    {
        return new QueryResult<T>
        {
            IsSuccess = true,
            Data = data,
            QueryId = queryId
        };
    }

    public static QueryResult<T> Failure(string errorMessage, Guid queryId)
    {
        return new QueryResult<T>
        {
            IsSuccess = false,
            ErrorMessage = errorMessage,
            QueryId = queryId
        };
    }
}

/// <summary>
/// Paginated query result
/// </summary>
/// <typeparam name="T">The item type</typeparam>
public class PagedQueryResult<T>
{
    public bool IsSuccess { get; set; }
    public List<T> Items { get; set; } = new();
    public int TotalCount { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
    public bool HasNextPage => PageNumber < TotalPages;
    public bool HasPreviousPage => PageNumber > 1;
    public string? ErrorMessage { get; set; }
    public DateTime ExecutedAt { get; set; } = DateTime.UtcNow;
    public Guid QueryId { get; set; }
    public QueryMetadata Metadata { get; set; } = new();

    public static PagedQueryResult<T> Success(List<T> items, int totalCount, int pageNumber, int pageSize, Guid queryId)
    {
        return new PagedQueryResult<T>
        {
            IsSuccess = true,
            Items = items,
            TotalCount = totalCount,
            PageNumber = pageNumber,
            PageSize = pageSize,
            QueryId = queryId
        };
    }

    public static PagedQueryResult<T> Failure(string errorMessage, Guid queryId)
    {
        return new PagedQueryResult<T>
        {
            IsSuccess = false,
            ErrorMessage = errorMessage,
            QueryId = queryId
        };
    }
}

/// <summary>
/// Query metadata for additional information
/// </summary>
public class QueryMetadata
{
    public TimeSpan ExecutionTime { get; set; }
    public string? CacheStatus { get; set; }
    public Dictionary<string, object> AdditionalInfo { get; set; } = new();
}

/// <summary>
/// Base pagination parameters
/// </summary>
public class PaginationParameters
{
    private int _pageSize = 20;
    private int _pageNumber = 1;

    public int PageNumber
    {
        get => _pageNumber;
        set => _pageNumber = value < 1 ? 1 : value;
    }

    public int PageSize
    {
        get => _pageSize;
        set => _pageSize = value switch
        {
            < 1 => 1,
            > 1000 => 1000,
            _ => value
        };
    }

    public int Skip => (PageNumber - 1) * PageSize;
    public int Take => PageSize;
}

/// <summary>
/// Base sorting parameters
/// </summary>
public class SortingParameters
{
    public string? SortBy { get; set; }
    public SortDirection SortDirection { get; set; } = SortDirection.Ascending;
}

/// <summary>
/// Sort direction enumeration
/// </summary>
public enum SortDirection
{
    Ascending,
    Descending
}

/// <summary>
/// Base filtering parameters
/// </summary>
public class FilteringParameters
{
    public string? SearchTerm { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public Dictionary<string, object> Filters { get; set; } = new();
}

/// <summary>
/// Combined query parameters for common scenarios
/// </summary>
public class QueryParameters : PaginationParameters
{
    public SortingParameters Sorting { get; set; } = new();
    public FilteringParameters Filtering { get; set; } = new();
}

/// <summary>
/// Query performance metrics
/// </summary>
public class QueryPerformanceMetrics
{
    public TimeSpan ExecutionTime { get; set; }
    public int RecordsScanned { get; set; }
    public int RecordsReturned { get; set; }
    public bool UsedCache { get; set; }
    public string? QueryPlan { get; set; }
    public Dictionary<string, object> AdditionalMetrics { get; set; } = new();
}

/// <summary>
/// Query validation result
/// </summary>
public class QueryValidationResult
{
    public bool IsValid { get; set; }
    public List<string> ValidationErrors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();

    public static QueryValidationResult Valid()
    {
        return new QueryValidationResult { IsValid = true };
    }

    public static QueryValidationResult Invalid(List<string> errors)
    {
        return new QueryValidationResult
        {
            IsValid = false,
            ValidationErrors = errors
        };
    }

    public static QueryValidationResult Invalid(string error)
    {
        return new QueryValidationResult
        {
            IsValid = false,
            ValidationErrors = new List<string> { error }
        };
    }
}
