{
  "Routes": [
    // Identity Service
    {
      "DownstreamPathTemplate": "/api/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7000
        }
      ],
      "UpstreamPathTemplate": "/api/identity/{everything}",
      "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"],
      "Key": "identity"
    },
    {
      "DownstreamPathTemplate": "/api/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7000
        }
      ],
      "UpstreamPathTemplate": "/api/auth/{everything}",
      "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"],
      "Key": "auth"
    },

    // User Management Service
    {
      "DownstreamPathTemplate": "/api/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7001
        }
      ],
      "UpstreamPathTemplate": "/api/users/{everything}",
      "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"],
      "Key": "users"
    },
    {
      "DownstreamPathTemplate": "/api/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7001
        }
      ],
      "UpstreamPathTemplate": "/api/profiles/{everything}",
      "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"],
      "Key": "profiles"
    },

    // Subscription Management Service
    {
      "DownstreamPathTemplate": "/api/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7003
        }
      ],
      "UpstreamPathTemplate": "/api/subscriptions/{everything}",
      "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"],
      "Key": "subscriptions"
    },
    {
      "DownstreamPathTemplate": "/api/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7003
        }
      ],
      "UpstreamPathTemplate": "/api/features/{everything}",
      "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"],
      "Key": "features"
    },
    {
      "DownstreamPathTemplate": "/api/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7003
        }
      ],
      "UpstreamPathTemplate": "/api/billing/{everything}",
      "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"],
      "Key": "billing"
    },

    // Order Management Service
    {
      "DownstreamPathTemplate": "/api/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7002
        }
      ],
      "UpstreamPathTemplate": "/api/orders/{everything}",
      "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"],
      "Key": "orders"
    },
    {
      "DownstreamPathTemplate": "/api/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7002
        }
      ],
      "UpstreamPathTemplate": "/api/rfq/{everything}",
      "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"],
      "Key": "rfq"
    },
    {
      "DownstreamPathTemplate": "/api/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7002
        }
      ],
      "UpstreamPathTemplate": "/api/quotes/{everything}",
      "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"],
      "Key": "quotes"
    },

    // Trip Management Service
    {
      "DownstreamPathTemplate": "/api/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7004
        }
      ],
      "UpstreamPathTemplate": "/api/trips/{everything}",
      "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"],
      "Key": "trips"
    },
    {
      "DownstreamPathTemplate": "/api/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7004
        }
      ],
      "UpstreamPathTemplate": "/api/tracking/{everything}",
      "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"],
      "Key": "tracking"
    },

    // Network Fleet Management Service
    {
      "DownstreamPathTemplate": "/api/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7005
        }
      ],
      "UpstreamPathTemplate": "/api/fleet/{everything}",
      "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"],
      "Key": "fleet"
    },
    {
      "DownstreamPathTemplate": "/api/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7005
        }
      ],
      "UpstreamPathTemplate": "/api/vehicles/{everything}",
      "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"],
      "Key": "vehicles"
    },
    {
      "DownstreamPathTemplate": "/api/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7005
        }
      ],
      "UpstreamPathTemplate": "/api/drivers/{everything}",
      "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"],
      "Key": "drivers"
    },

    // Financial Payment Service
    {
      "DownstreamPathTemplate": "/api/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7006
        }
      ],
      "UpstreamPathTemplate": "/api/payments/{everything}",
      "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"],
      "Key": "payments"
    },
    {
      "DownstreamPathTemplate": "/api/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7006
        }
      ],
      "UpstreamPathTemplate": "/api/invoices/{everything}",
      "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"],
      "Key": "invoices"
    },

    // Communication Notification Service
    {
      "DownstreamPathTemplate": "/api/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7007
        }
      ],
      "UpstreamPathTemplate": "/api/notifications/{everything}",
      "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"],
      "Key": "notifications"
    },
    {
      "DownstreamPathTemplate": "/api/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7007
        }
      ],
      "UpstreamPathTemplate": "/api/messages/{everything}",
      "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"],
      "Key": "messages"
    },

    // Analytics BI Service
    {
      "DownstreamPathTemplate": "/api/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7008
        }
      ],
      "UpstreamPathTemplate": "/api/analytics/{everything}",
      "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"],
      "Key": "analytics"
    },
    {
      "DownstreamPathTemplate": "/api/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7008
        }
      ],
      "UpstreamPathTemplate": "/api/reports/{everything}",
      "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"],
      "Key": "reports"
    },

    // Data & Storage Service
    {
      "DownstreamPathTemplate": "/api/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7009
        }
      ],
      "UpstreamPathTemplate": "/api/documents/{everything}",
      "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"],
      "Key": "documents"
    },
    {
      "DownstreamPathTemplate": "/api/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7009
        }
      ],
      "UpstreamPathTemplate": "/api/storage/{everything}",
      "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"],
      "Key": "storage"
    },
    {
      "DownstreamPathTemplate": "/api/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7009
        }
      ],
      "UpstreamPathTemplate": "/api/files/{everything}",
      "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"],
      "Key": "files"
    },

    // Monitoring & Observability Service
    {
      "DownstreamPathTemplate": "/api/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7010
        }
      ],
      "UpstreamPathTemplate": "/api/monitoring/alerts/{everything}",
      "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"],
      "Key": "monitoring-alerts"
    },
    {
      "DownstreamPathTemplate": "/api/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7010
        }
      ],
      "UpstreamPathTemplate": "/api/monitoring/metrics/{everything}",
      "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"],
      "Key": "monitoring-metrics"
    },
    {
      "DownstreamPathTemplate": "/api/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7010
        }
      ],
      "UpstreamPathTemplate": "/api/monitoring/health-checks/{everything}",
      "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"],
      "Key": "monitoring-health-checks"
    },
    {
      "DownstreamPathTemplate": "/api/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7010
        }
      ],
      "UpstreamPathTemplate": "/api/monitoring/dashboard/{everything}",
      "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"],
      "Key": "monitoring-dashboard"
    },
    {
      "DownstreamPathTemplate": "/api/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7010
        }
      ],
      "UpstreamPathTemplate": "/api/monitoring/admin/{everything}",
      "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"],
      "Key": "monitoring-admin",
      "AuthenticationOptions": {
        "AuthenticationProviderKey": "Bearer",
        "AllowedScopes": ["admin"]
      }
    },

    // Audit & Compliance Service
    {
      "DownstreamPathTemplate": "/api/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7011
        }
      ],
      "UpstreamPathTemplate": "/api/audit/{everything}",
      "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"],
      "Key": "audit",
      "AuthenticationOptions": {
        "AuthenticationProviderKey": "Bearer",
        "AllowedScopes": ["admin", "auditor", "compliance"]
      }
    },
    {
      "DownstreamPathTemplate": "/api/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7011
        }
      ],
      "UpstreamPathTemplate": "/api/compliance/{everything}",
      "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"],
      "Key": "compliance",
      "AuthenticationOptions": {
        "AuthenticationProviderKey": "Bearer",
        "AllowedScopes": ["admin", "auditor", "compliance"]
      }
    },
    {
      "DownstreamPathTemplate": "/api/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7011
        }
      ],
      "UpstreamPathTemplate": "/api/rating/{everything}",
      "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"],
      "Key": "rating",
      "AuthenticationOptions": {
        "AuthenticationProviderKey": "Bearer",
        "AllowedScopes": ["shipper", "transport", "admin"]
      }
    },

    // Mobile Workflow Service
    {
      "DownstreamPathTemplate": "/api/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7012
        }
      ],
      "UpstreamPathTemplate": "/api/mobile/{everything}",
      "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"],
      "Key": "mobile"
    },
    {
      "DownstreamPathTemplate": "/api/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7012
        }
      ],
      "UpstreamPathTemplate": "/api/workflow/{everything}",
      "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"],
      "Key": "workflow"
    },

    // Health Check Routes
    {
      "DownstreamPathTemplate": "/health",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7000
        }
      ],
      "UpstreamPathTemplate": "/health/identity",
      "UpstreamHttpMethod": ["GET"],
      "Key": "health-identity"
    },
    {
      "DownstreamPathTemplate": "/health",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7001
        }
      ],
      "UpstreamPathTemplate": "/health/users",
      "UpstreamHttpMethod": ["GET"],
      "Key": "health-users"
    },
    {
      "DownstreamPathTemplate": "/health",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7002
        }
      ],
      "UpstreamPathTemplate": "/health/orders",
      "UpstreamHttpMethod": ["GET"],
      "Key": "health-orders"
    },
    {
      "DownstreamPathTemplate": "/health",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7003
        }
      ],
      "UpstreamPathTemplate": "/health/subscriptions",
      "UpstreamHttpMethod": ["GET"],
      "Key": "health-subscriptions"
    },
    {
      "DownstreamPathTemplate": "/health",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7004
        }
      ],
      "UpstreamPathTemplate": "/health/trips",
      "UpstreamHttpMethod": ["GET"],
      "Key": "health-trips"
    },
    {
      "DownstreamPathTemplate": "/health",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7005
        }
      ],
      "UpstreamPathTemplate": "/health/fleet",
      "UpstreamHttpMethod": ["GET"],
      "Key": "health-fleet"
    },
    {
      "DownstreamPathTemplate": "/health",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7006
        }
      ],
      "UpstreamPathTemplate": "/health/payments",
      "UpstreamHttpMethod": ["GET"],
      "Key": "health-payments"
    },
    {
      "DownstreamPathTemplate": "/health",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7007
        }
      ],
      "UpstreamPathTemplate": "/health/notifications",
      "UpstreamHttpMethod": ["GET"],
      "Key": "health-notifications"
    },
    {
      "DownstreamPathTemplate": "/health",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7008
        }
      ],
      "UpstreamPathTemplate": "/health/analytics",
      "UpstreamHttpMethod": ["GET"],
      "Key": "health-analytics"
    },
    {
      "DownstreamPathTemplate": "/health",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7009
        }
      ],
      "UpstreamPathTemplate": "/health/datastorage",
      "UpstreamHttpMethod": ["GET"],
      "Key": "health-datastorage"
    },
    {
      "DownstreamPathTemplate": "/health",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7010
        }
      ],
      "UpstreamPathTemplate": "/health/monitoring",
      "UpstreamHttpMethod": ["GET"],
      "Key": "health-monitoring"
    },
    {
      "DownstreamPathTemplate": "/health",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7011
        }
      ],
      "UpstreamPathTemplate": "/health/auditcompliance",
      "UpstreamHttpMethod": ["GET"],
      "Key": "health-auditcompliance"
    },
    {
      "DownstreamPathTemplate": "/health",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 7012
        }
      ],
      "UpstreamPathTemplate": "/health/mobile",
      "UpstreamHttpMethod": ["GET"],
      "Key": "health-mobile"
    }
  ],
  "GlobalConfiguration": {
    "BaseUrl": "http://localhost:5000",
    "RequestIdKey": "X-Request-ID"
  },
  "AuthenticationProviders": [
    {
      "AuthenticationProviderKey": "Bearer",
      "Type": "Bearer"
    }
  ]
}
