using Shared.Domain.Common;
using TripManagement.Domain.Enums;
using TripManagement.Domain.Events;
using TripManagement.Domain.ValueObjects;
using TripManagement.Domain.Exceptions;

namespace TripManagement.Domain.Entities;

/// <summary>
/// Represents a delay alert for Transport Company Portal
/// </summary>
public class DelayAlert : AggregateRoot
{
    public Guid TripId { get; private set; }
    public Guid? TripStopId { get; private set; }
    public Guid TransportCompanyId { get; private set; }
    public Guid? ShipperId { get; private set; }
    public DelayAlertType AlertType { get; private set; }
    public DelayAlertSeverity Severity { get; private set; }
    public DelayAlertStatus Status { get; private set; }
    public string Title { get; private set; } = string.Empty;
    public string Description { get; private set; } = string.Empty;
    public TimeSpan DelayDuration { get; private set; }
    public DateTime ScheduledTime { get; private set; }
    public DateTime? EstimatedTime { get; private set; }
    public DateTime? ActualTime { get; private set; }
    public Location? CurrentLocation { get; private set; }
    public Location? TargetLocation { get; private set; }
    public double? DistanceRemainingKm { get; private set; }
    public DelayReason DelayReason { get; private set; }
    public string? DelayReasonDescription { get; private set; }
    public DelayAlertConfiguration Configuration { get; private set; }

    // Escalation tracking
    public int EscalationLevel { get; private set; }
    public DateTime? LastEscalatedAt { get; private set; }
    public DateTime? AcknowledgedAt { get; private set; }
    public Guid? AcknowledgedByUserId { get; private set; }
    public string? AcknowledgmentNotes { get; private set; }
    public DateTime? ResolvedAt { get; private set; }
    public string? ResolutionNotes { get; private set; }

    // Notification tracking
    private readonly List<DelayAlertNotification> _notifications = new();
    public IReadOnlyList<DelayAlertNotification> Notifications => _notifications.AsReadOnly();

    // Impact assessment
    public DelayImpactAssessment? ImpactAssessment { get; private set; }

    // Additional metadata
    public Dictionary<string, object> Metadata { get; private set; } = new();

    private DelayAlert() { }

    public DelayAlert(
        Guid tripId,
        Guid transportCompanyId,
        DelayAlertType alertType,
        DelayAlertSeverity severity,
        string title,
        string description,
        TimeSpan delayDuration,
        DateTime scheduledTime,
        DelayReason delayReason,
        DelayAlertConfiguration configuration,
        Guid? tripStopId = null,
        Guid? shipperId = null,
        DateTime? estimatedTime = null,
        Location? currentLocation = null,
        Location? targetLocation = null,
        double? distanceRemainingKm = null,
        string? delayReasonDescription = null)
    {
        if (delayDuration <= TimeSpan.Zero)
            throw new InvalidDelayThresholdException("Delay duration must be positive");

        if (string.IsNullOrWhiteSpace(title))
            throw new DelayAlertException("Alert title is required");

        TripId = tripId;
        TripStopId = tripStopId;
        TransportCompanyId = transportCompanyId;
        ShipperId = shipperId;
        AlertType = alertType;
        Severity = severity;
        Status = DelayAlertStatus.Active;
        Title = title;
        Description = description;
        DelayDuration = delayDuration;
        ScheduledTime = scheduledTime;
        EstimatedTime = estimatedTime;
        CurrentLocation = currentLocation;
        TargetLocation = targetLocation;
        DistanceRemainingKm = distanceRemainingKm;
        DelayReason = delayReason;
        DelayReasonDescription = delayReasonDescription;
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        EscalationLevel = 0;

        AddDomainEvent(new DelayAlertCreatedEvent(
            Id, TripId, TransportCompanyId, AlertType, Severity, DelayDuration, ScheduledTime));
    }

    public void UpdateDelayDuration(TimeSpan newDelayDuration, DateTime? newEstimatedTime = null)
    {
        if (Status == DelayAlertStatus.Resolved)
            throw new DelayAlertException("Cannot update resolved delay alert");

        var previousDuration = DelayDuration;
        DelayDuration = newDelayDuration;

        if (newEstimatedTime.HasValue)
            EstimatedTime = newEstimatedTime;

        // Check if severity should be updated based on new delay duration
        var newSeverity = DetermineSeverity(newDelayDuration);
        if (newSeverity != Severity)
        {
            var previousSeverity = Severity;
            Severity = newSeverity;

            AddDomainEvent(new DelayAlertSeverityChangedEvent(
                Id, TripId, previousSeverity, newSeverity, previousDuration, newDelayDuration));
        }

        AddDomainEvent(new DelayAlertUpdatedEvent(
            Id, TripId, previousDuration, newDelayDuration, EstimatedTime));
    }

    public void Escalate(string reason)
    {
        if (Status != DelayAlertStatus.Active)
            throw new DelayAlertException("Can only escalate active delay alerts");

        if (EscalationLevel >= Configuration.MaxEscalationLevel)
            throw new DelayAlertException("Maximum escalation level reached");

        EscalationLevel++;
        LastEscalatedAt = DateTime.UtcNow;

        AddDomainEvent(new DelayAlertEscalatedEvent(
            Id, TripId, EscalationLevel, reason, LastEscalatedAt.Value));
    }

    public void Acknowledge(Guid acknowledgedByUserId, string? notes = null)
    {
        if (Status != DelayAlertStatus.Active)
            throw new DelayAlertException("Can only acknowledge active delay alerts");

        Status = DelayAlertStatus.Acknowledged;
        AcknowledgedAt = DateTime.UtcNow;
        AcknowledgedByUserId = acknowledgedByUserId;
        AcknowledgmentNotes = notes;

        AddDomainEvent(new DelayAlertAcknowledgedEvent(
            Id, TripId, acknowledgedByUserId, AcknowledgedAt.Value, notes));
    }

    public void Resolve(string resolutionNotes, DateTime? actualTime = null)
    {
        if (Status == DelayAlertStatus.Resolved)
            throw new DelayAlertException("Delay alert is already resolved");

        Status = DelayAlertStatus.Resolved;
        ResolvedAt = DateTime.UtcNow;
        ResolutionNotes = resolutionNotes;

        if (actualTime.HasValue)
            ActualTime = actualTime;

        AddDomainEvent(new DelayAlertResolvedEvent(
            Id, TripId, ResolvedAt.Value, resolutionNotes, ActualTime));
    }

    public void Cancel(string reason)
    {
        if (Status == DelayAlertStatus.Resolved)
            throw new DelayAlertException("Cannot cancel resolved delay alert");

        Status = DelayAlertStatus.Cancelled;
        ResolutionNotes = $"Cancelled: {reason}";
        ResolvedAt = DateTime.UtcNow;

        AddDomainEvent(new DelayAlertCancelledEvent(Id, TripId, reason, ResolvedAt.Value));
    }

    public void AddNotification(DelayAlertNotification notification)
    {
        _notifications.Add(notification);

        AddDomainEvent(new DelayAlertNotificationSentEvent(
            Id, TripId, notification.Channel, notification.Recipients, notification.SentAt));
    }

    public void UpdateImpactAssessment(DelayImpactAssessment impactAssessment)
    {
        ImpactAssessment = impactAssessment;

        AddDomainEvent(new DelayAlertImpactAssessedEvent(
            Id, TripId, impactAssessment.FinancialImpact, impactAssessment.CustomerImpact, impactAssessment.OperationalImpact));
    }

    public void UpdateLocation(Location currentLocation, double? distanceRemainingKm = null)
    {
        CurrentLocation = currentLocation;
        if (distanceRemainingKm.HasValue)
            DistanceRemainingKm = distanceRemainingKm;
    }

    public bool ShouldEscalate()
    {
        if (Status != DelayAlertStatus.Active)
            return false;

        if (EscalationLevel >= Configuration.MaxEscalationLevel)
            return false;

        var timeSinceCreated = DateTime.UtcNow - CreatedAt;
        var timeSinceLastEscalation = LastEscalatedAt.HasValue
            ? DateTime.UtcNow - LastEscalatedAt.Value
            : timeSinceCreated;

        var escalationThreshold = Configuration.GetEscalationThreshold(EscalationLevel);
        return timeSinceLastEscalation >= escalationThreshold;
    }

    public bool IsExpired()
    {
        return Configuration.AlertExpiryHours.HasValue &&
               DateTime.UtcNow > CreatedAt.AddHours(Configuration.AlertExpiryHours.Value);
    }

    public DelayAlertSummary GetSummary()
    {
        return new DelayAlertSummary
        {
            Id = Id,
            TripId = TripId,
            AlertType = AlertType,
            Severity = Severity,
            Status = Status,
            DelayDuration = DelayDuration,
            EscalationLevel = EscalationLevel,
            CreatedAt = CreatedAt,
            AcknowledgedAt = AcknowledgedAt,
            ResolvedAt = ResolvedAt,
            NotificationCount = _notifications.Count,
            IsExpired = IsExpired()
        };
    }

    private DelayAlertSeverity DetermineSeverity(TimeSpan delayDuration)
    {
        return delayDuration.TotalMinutes switch
        {
            <= 15 => DelayAlertSeverity.Low,
            <= 30 => DelayAlertSeverity.Medium,
            <= 60 => DelayAlertSeverity.High,
            _ => DelayAlertSeverity.Critical
        };
    }
}

/// <summary>
/// Represents a notification sent for a delay alert
/// </summary>
public class DelayAlertNotification
{
    public Guid Id { get; private set; }
    public Guid DelayAlertId { get; private set; }
    public string Channel { get; private set; } = string.Empty; // Email, SMS, Push, etc.
    public List<string> Recipients { get; private set; } = new();
    public string Subject { get; private set; } = string.Empty;
    public string Content { get; private set; } = string.Empty;
    public DateTime SentAt { get; private set; }
    public bool IsSuccessful { get; private set; }
    public string? ErrorMessage { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; } = new();

    private DelayAlertNotification() { }

    public DelayAlertNotification(
        Guid delayAlertId,
        string channel,
        List<string> recipients,
        string subject,
        string content,
        bool isSuccessful = true,
        string? errorMessage = null,
        Dictionary<string, object>? metadata = null)
    {
        Id = Guid.NewGuid();
        DelayAlertId = delayAlertId;
        Channel = channel;
        Recipients = recipients;
        Subject = subject;
        Content = content;
        SentAt = DateTime.UtcNow;
        IsSuccessful = isSuccessful;
        ErrorMessage = errorMessage;
        Metadata = metadata ?? new Dictionary<string, object>();
    }
}


