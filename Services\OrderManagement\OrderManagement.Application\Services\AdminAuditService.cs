using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Entities;
using OrderManagement.Domain.Enums;
using OrderManagement.Application.Commands.ForceOrderClosure;

namespace OrderManagement.Application.Services;

public interface IAdminAuditService
{
    Task LogAdminActionAsync(AdminAuditEntry entry, CancellationToken cancellationToken = default);
    Task<List<AdminAuditLog>> GetAuditLogsAsync(AdminAuditQuery query, CancellationToken cancellationToken = default);
    Task<AdminAuditSummary> GetAuditSummaryAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default);
    Task<List<AdminAuditLog>> GetOrderAuditTrailAsync(Guid orderId, CancellationToken cancellationToken = default);
    Task<List<AdminAuditLog>> GetAdminUserActionsAsync(Guid adminUserId, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default);
}

public class AdminAuditService : IAdminAuditService
{
    private readonly IAdminAuditRepository _auditRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<AdminAuditService> _logger;

    public AdminAuditService(
        IAdminAuditRepository auditRepository,
        IUnitOfWork unitOfWork,
        ILogger<AdminAuditService> logger)
    {
        _auditRepository = auditRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task LogAdminActionAsync(AdminAuditEntry entry, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Logging admin action: {Action} by {AdminUserId} on order {OrderId}",
                entry.Action, entry.AdminUserId, entry.OrderId);

            var auditLog = new AdminAuditLog
            {
                Id = Guid.NewGuid(),
                OrderId = entry.OrderId,
                Action = entry.Action,
                PreviousValue = entry.PreviousValue,
                NewValue = entry.NewValue,
                AdminUserId = entry.AdminUserId,
                AdminUserName = entry.AdminUserName,
                Reason = entry.Reason,
                AdminNotes = entry.AdminNotes,
                IpAddress = entry.IpAddress,
                UserAgent = entry.UserAgent,
                Severity = entry.Severity,
                Timestamp = entry.Timestamp,
                SessionId = GenerateSessionId(),
                CorrelationId = Guid.NewGuid(),
                Metadata = CreateMetadata(entry)
            };

            await _auditRepository.AddAsync(auditLog, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Admin action logged successfully: {AuditLogId}", auditLog.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error logging admin action: {Action} by {AdminUserId}",
                entry.Action, entry.AdminUserId);
            throw;
        }
    }

    public async Task<List<AdminAuditLog>> GetAuditLogsAsync(AdminAuditQuery query, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Retrieving audit logs with query parameters");

            return await _auditRepository.GetAuditLogsAsync(query, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving audit logs");
            throw;
        }
    }

    public async Task<AdminAuditSummary> GetAuditSummaryAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Generating audit summary from {FromDate} to {ToDate}", fromDate, toDate);

            var logs = await _auditRepository.GetAuditLogsByDateRangeAsync(fromDate, toDate, cancellationToken);

            var summary = new AdminAuditSummary
            {
                FromDate = fromDate,
                ToDate = toDate,
                TotalActions = logs.Count,
                ActionsByType = logs.GroupBy(l => l.Action)
                    .ToDictionary(g => g.Key, g => g.Count()),
                ActionsBySeverity = logs.GroupBy(l => l.Severity)
                    .ToDictionary(g => g.Key.ToString(), g => g.Count()),
                ActionsByAdmin = logs.GroupBy(l => l.AdminUserId)
                    .ToDictionary(g => g.Key, g => new AdminActionSummary
                    {
                        AdminUserId = g.Key,
                        AdminUserName = g.First().AdminUserName ?? "Unknown",
                        ActionCount = g.Count(),
                        LastAction = g.Max(a => a.Timestamp),
                        HighSeverityActions = g.Count(a => a.Severity >= AuditSeverity.High)
                    }),
                CriticalActions = logs.Where(l => l.Severity == AuditSeverity.Critical).Count(),
                HighSeverityActions = logs.Where(l => l.Severity == AuditSeverity.High).Count(),
                GeneratedAt = DateTime.UtcNow
            };

            _logger.LogInformation("Audit summary generated: {TotalActions} actions", summary.TotalActions);
            return summary;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating audit summary");
            throw;
        }
    }

    public async Task<List<AdminAuditLog>> GetOrderAuditTrailAsync(Guid orderId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Retrieving audit trail for order {OrderId}", orderId);

            return await _auditRepository.GetOrderAuditTrailAsync(orderId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving audit trail for order {OrderId}", orderId);
            throw;
        }
    }

    public async Task<List<AdminAuditLog>> GetAdminUserActionsAsync(Guid adminUserId, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Retrieving actions for admin user {AdminUserId}", adminUserId);

            return await _auditRepository.GetAdminUserActionsAsync(adminUserId, fromDate, toDate, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving actions for admin user {AdminUserId}", adminUserId);
            throw;
        }
    }

    private string GenerateSessionId()
    {
        return Guid.NewGuid().ToString("N")[..16];
    }

    private Dictionary<string, object> CreateMetadata(AdminAuditEntry entry)
    {
        var metadata = new Dictionary<string, object>
        {
            ["Source"] = "AdminAuditService",
            ["Version"] = "1.0",
            ["Environment"] = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown"
        };

        if (!string.IsNullOrEmpty(entry.IpAddress))
            metadata["IpAddress"] = entry.IpAddress;

        if (!string.IsNullOrEmpty(entry.UserAgent))
            metadata["UserAgent"] = entry.UserAgent;

        return metadata;
    }
}

// Supporting entities and DTOs
public class AdminAuditLog
{
    public Guid Id { get; set; }
    public Guid? OrderId { get; set; }
    public string Action { get; set; } = string.Empty;
    public string? PreviousValue { get; set; }
    public string? NewValue { get; set; }
    public Guid AdminUserId { get; set; }
    public string? AdminUserName { get; set; }
    public string? Reason { get; set; }
    public string? AdminNotes { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
    public AuditSeverity Severity { get; set; }
    public DateTime Timestamp { get; set; }
    public string? SessionId { get; set; }
    public Guid? CorrelationId { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}

public class AdminAuditQuery
{
    public Guid? OrderId { get; set; }
    public Guid? AdminUserId { get; set; }
    public string? Action { get; set; }
    public AuditSeverity? MinSeverity { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 50;
    public string? SortBy { get; set; } = "Timestamp";
    public string? SortDirection { get; set; } = "desc";
}

public class AdminAuditSummary
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public int TotalActions { get; set; }
    public Dictionary<string, int> ActionsByType { get; set; } = new();
    public Dictionary<string, int> ActionsBySeverity { get; set; } = new();
    public Dictionary<Guid, AdminActionSummary> ActionsByAdmin { get; set; } = new();
    public int CriticalActions { get; set; }
    public int HighSeverityActions { get; set; }
    public DateTime GeneratedAt { get; set; }
}

public class AdminActionSummary
{
    public Guid AdminUserId { get; set; }
    public string AdminUserName { get; set; } = string.Empty;
    public int ActionCount { get; set; }
    public DateTime LastAction { get; set; }
    public int HighSeverityActions { get; set; }
}

public interface IAdminAuditRepository
{
    Task AddAsync(AdminAuditLog auditLog, CancellationToken cancellationToken = default);
    Task<List<AdminAuditLog>> GetAuditLogsAsync(AdminAuditQuery query, CancellationToken cancellationToken = default);
    Task<List<AdminAuditLog>> GetAuditLogsByDateRangeAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default);
    Task<List<AdminAuditLog>> GetOrderAuditTrailAsync(Guid orderId, CancellationToken cancellationToken = default);
    Task<List<AdminAuditLog>> GetAdminUserActionsAsync(Guid adminUserId, DateTime? fromDate, DateTime? toDate, CancellationToken cancellationToken = default);
}
