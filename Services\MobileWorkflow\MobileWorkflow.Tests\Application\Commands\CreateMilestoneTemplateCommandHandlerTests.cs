using AutoMapper;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using MobileWorkflow.Application.Commands.Milestone;
using MobileWorkflow.Application.DTOs;
using MobileWorkflow.Domain.Entities;
using MobileWorkflow.Domain.Repositories;
using Moq;
using Xunit;

namespace MobileWorkflow.Tests.Application.Commands;

public class CreateMilestoneTemplateCommandHandlerTests
{
    private readonly Mock<IMilestoneTemplateRepository> _mockRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly Mock<ILogger<CreateMilestoneTemplateCommandHandler>> _mockLogger;
    private readonly CreateMilestoneTemplateCommandHandler _handler;

    public CreateMilestoneTemplateCommandHandlerTests()
    {
        _mockRepository = new Mock<IMilestoneTemplateRepository>();
        _mockMapper = new Mock<IMapper>();
        _mockLogger = new Mock<ILogger<CreateMilestoneTemplateCommandHandler>>();
        _handler = new CreateMilestoneTemplateCommandHandler(_mockRepository.Object, _mockMapper.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task Handle_WithValidCommand_ShouldCreateTemplateSuccessfully()
    {
        // Arrange
        var command = new CreateMilestoneTemplateCommand
        {
            Name = "Test Template",
            Description = "Test Description",
            Type = "Trip",
            Category = "Logistics",
            CreatedBy = "<EMAIL>",
            Configuration = new Dictionary<string, object> { { "auto_advance", true } },
            Metadata = new Dictionary<string, object> { { "version", "1.0" } },
            Steps = new List<CreateMilestoneStepRequest>
            {
                new CreateMilestoneStepRequest
                {
                    Name = "Step 1",
                    Description = "First step",
                    SequenceNumber = 1,
                    IsRequired = true,
                    PayoutRules = new List<CreateMilestonePayoutRuleRequest>
                    {
                        new CreateMilestonePayoutRuleRequest { PayoutPercentage = 100.0m }
                    }
                }
            }
        };

        var expectedTemplate = new MilestoneTemplate(command.Name, command.Description, command.Type, command.Category, command.CreatedBy);
        var expectedDto = new MilestoneTemplateDto
        {
            Id = expectedTemplate.Id,
            Name = command.Name,
            Description = command.Description,
            Type = command.Type,
            Category = command.Category,
            CreatedBy = command.CreatedBy
        };

        _mockRepository.Setup(r => r.GetByNameAsync(command.Name, It.IsAny<CancellationToken>()))
                      .ReturnsAsync((MilestoneTemplate?)null);

        _mockRepository.Setup(r => r.AddAsync(It.IsAny<MilestoneTemplate>(), It.IsAny<CancellationToken>()))
                      .ReturnsAsync(expectedTemplate);

        _mockMapper.Setup(m => m.Map<MilestoneTemplateDto>(It.IsAny<MilestoneTemplate>()))
                   .Returns(expectedDto);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Name.Should().Be(command.Name);
        result.Description.Should().Be(command.Description);
        result.Type.Should().Be(command.Type);
        result.Category.Should().Be(command.Category);
        result.CreatedBy.Should().Be(command.CreatedBy);

        _mockRepository.Verify(r => r.GetByNameAsync(command.Name, It.IsAny<CancellationToken>()), Times.Once);
        _mockRepository.Verify(r => r.AddAsync(It.IsAny<MilestoneTemplate>(), It.IsAny<CancellationToken>()), Times.Once);
        _mockMapper.Verify(m => m.Map<MilestoneTemplateDto>(It.IsAny<MilestoneTemplate>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithExistingTemplateName_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var command = new CreateMilestoneTemplateCommand
        {
            Name = "Existing Template",
            Description = "Test Description",
            Type = "Trip",
            Category = "Logistics",
            CreatedBy = "<EMAIL>"
        };

        var existingTemplate = new MilestoneTemplate("Existing Template", "Description", "Trip", "Logistics", "admin");

        _mockRepository.Setup(r => r.GetByNameAsync(command.Name, It.IsAny<CancellationToken>()))
                      .ReturnsAsync(existingTemplate);

        // Act & Assert
        var act = async () => await _handler.Handle(command, CancellationToken.None);
        await act.Should().ThrowAsync<InvalidOperationException>()
                 .WithMessage("Milestone template with name 'Existing Template' already exists");

        _mockRepository.Verify(r => r.GetByNameAsync(command.Name, It.IsAny<CancellationToken>()), Times.Once);
        _mockRepository.Verify(r => r.AddAsync(It.IsAny<MilestoneTemplate>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task Handle_WithInvalidPayoutPercentages_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var command = new CreateMilestoneTemplateCommand
        {
            Name = "Test Template",
            Description = "Test Description",
            Type = "Trip",
            Category = "Logistics",
            CreatedBy = "<EMAIL>",
            Steps = new List<CreateMilestoneStepRequest>
            {
                new CreateMilestoneStepRequest
                {
                    Name = "Step 1",
                    Description = "First step",
                    SequenceNumber = 1,
                    IsRequired = true,
                    PayoutRules = new List<CreateMilestonePayoutRuleRequest>
                    {
                        new CreateMilestonePayoutRuleRequest { PayoutPercentage = 60.0m }
                    }
                },
                new CreateMilestoneStepRequest
                {
                    Name = "Step 2",
                    Description = "Second step",
                    SequenceNumber = 2,
                    IsRequired = true,
                    PayoutRules = new List<CreateMilestonePayoutRuleRequest>
                    {
                        new CreateMilestonePayoutRuleRequest { PayoutPercentage = 50.0m } // Total = 110%
                    }
                }
            }
        };

        _mockRepository.Setup(r => r.GetByNameAsync(command.Name, It.IsAny<CancellationToken>()))
                      .ReturnsAsync((MilestoneTemplate?)null);

        // Act & Assert
        var act = async () => await _handler.Handle(command, CancellationToken.None);
        await act.Should().ThrowAsync<InvalidOperationException>()
                 .WithMessage("Template validation failed:*");

        _mockRepository.Verify(r => r.GetByNameAsync(command.Name, It.IsAny<CancellationToken>()), Times.Once);
        _mockRepository.Verify(r => r.AddAsync(It.IsAny<MilestoneTemplate>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task Handle_WithStepsHavingConfiguration_ShouldSetConfigurationCorrectly()
    {
        // Arrange
        var stepConfiguration = new Dictionary<string, object> { { "require_photo", true } };
        var stepMetadata = new Dictionary<string, object> { { "importance", "high" } };

        var command = new CreateMilestoneTemplateCommand
        {
            Name = "Test Template",
            Description = "Test Description",
            Type = "Trip",
            Category = "Logistics",
            CreatedBy = "<EMAIL>",
            Steps = new List<CreateMilestoneStepRequest>
            {
                new CreateMilestoneStepRequest
                {
                    Name = "Step 1",
                    Description = "First step",
                    SequenceNumber = 1,
                    IsRequired = true,
                    Configuration = stepConfiguration,
                    Metadata = stepMetadata,
                    TriggerCondition = "status=ready",
                    PayoutRules = new List<CreateMilestonePayoutRuleRequest>
                    {
                        new CreateMilestonePayoutRuleRequest 
                        { 
                            PayoutPercentage = 100.0m,
                            Description = "Full payment",
                            TriggerCondition = "status=completed"
                        }
                    }
                }
            }
        };

        var expectedTemplate = new MilestoneTemplate(command.Name, command.Description, command.Type, command.Category, command.CreatedBy);
        var expectedDto = new MilestoneTemplateDto { Id = expectedTemplate.Id, Name = command.Name };

        _mockRepository.Setup(r => r.GetByNameAsync(command.Name, It.IsAny<CancellationToken>()))
                      .ReturnsAsync((MilestoneTemplate?)null);

        _mockRepository.Setup(r => r.AddAsync(It.IsAny<MilestoneTemplate>(), It.IsAny<CancellationToken>()))
                      .ReturnsAsync(expectedTemplate);

        _mockMapper.Setup(m => m.Map<MilestoneTemplateDto>(It.IsAny<MilestoneTemplate>()))
                   .Returns(expectedDto);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();

        _mockRepository.Verify(r => r.AddAsync(It.Is<MilestoneTemplate>(t => 
            t.Steps.Any(s => 
                s.Name == "Step 1" && 
                s.TriggerCondition == "status=ready" &&
                s.PayoutRules.Any(pr => pr.Description == "Full payment"))), 
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithEmptySteps_ShouldCreateTemplateWithoutSteps()
    {
        // Arrange
        var command = new CreateMilestoneTemplateCommand
        {
            Name = "Empty Template",
            Description = "Template without steps",
            Type = "Trip",
            Category = "Logistics",
            CreatedBy = "<EMAIL>",
            Steps = new List<CreateMilestoneStepRequest>()
        };

        var expectedTemplate = new MilestoneTemplate(command.Name, command.Description, command.Type, command.Category, command.CreatedBy);
        var expectedDto = new MilestoneTemplateDto { Id = expectedTemplate.Id, Name = command.Name };

        _mockRepository.Setup(r => r.GetByNameAsync(command.Name, It.IsAny<CancellationToken>()))
                      .ReturnsAsync((MilestoneTemplate?)null);

        _mockRepository.Setup(r => r.AddAsync(It.IsAny<MilestoneTemplate>(), It.IsAny<CancellationToken>()))
                      .ReturnsAsync(expectedTemplate);

        _mockMapper.Setup(m => m.Map<MilestoneTemplateDto>(It.IsAny<MilestoneTemplate>()))
                   .Returns(expectedDto);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Name.Should().Be(command.Name);

        _mockRepository.Verify(r => r.AddAsync(It.Is<MilestoneTemplate>(t => t.Steps.Count == 0), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WhenRepositoryThrows_ShouldPropagateException()
    {
        // Arrange
        var command = new CreateMilestoneTemplateCommand
        {
            Name = "Test Template",
            Description = "Test Description",
            Type = "Trip",
            Category = "Logistics",
            CreatedBy = "<EMAIL>"
        };

        _mockRepository.Setup(r => r.GetByNameAsync(command.Name, It.IsAny<CancellationToken>()))
                      .ThrowsAsync(new Exception("Database error"));

        // Act & Assert
        var act = async () => await _handler.Handle(command, CancellationToken.None);
        await act.Should().ThrowAsync<Exception>()
                 .WithMessage("Database error");

        _mockRepository.Verify(r => r.GetByNameAsync(command.Name, It.IsAny<CancellationToken>()), Times.Once);
        _mockRepository.Verify(r => r.AddAsync(It.IsAny<MilestoneTemplate>(), It.IsAny<CancellationToken>()), Times.Never);
    }
}
