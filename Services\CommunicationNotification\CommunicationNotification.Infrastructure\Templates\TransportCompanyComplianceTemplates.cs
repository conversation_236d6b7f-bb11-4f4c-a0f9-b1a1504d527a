using CommunicationNotification.Domain.Enums;

namespace CommunicationNotification.Infrastructure.Templates;

/// <summary>
/// Templates for Transport Company compliance reminders
/// </summary>
public static class TransportCompanyComplianceTemplates
{
    public static readonly Dictionary<string, NotificationTemplate> Templates = new()
    {
        // GST Certificate Templates
        ["transport_company_gst_expiry_30"] = new NotificationTemplate
        {
            Id = "transport_company_gst_expiry_30",
            Name = "GST Certificate Expiry - 30 Days Notice",
            Subject = "GST Certificate Expiring Soon - Action Required",
            EmailBody = @"
Dear {{CompanyName}},

This is a reminder that your GST Certificate is expiring in {{DaysUntilExpiry}} days.

Certificate Details:
- GST Number: {{GstNumber}}
- Expiry Date: {{ExpiryDate}}
- Days Until Expiry: {{DaysUntilExpiry}}

To ensure uninterrupted business operations, please:
1. Renew your GST certificate before the expiry date
2. Upload the renewed certificate to your profile
3. Update any relevant business documents

If you have already renewed your certificate, please upload the new document to avoid further reminders.

For assistance, contact our support team.

Best regards,
TLI Logistics Team",
            SmsBody = "GST Certificate expiring in {{DaysUntilExpiry}} days ({{ExpiryDate}}). Please renew and update your profile. - TLI Logistics",
            PushNotificationBody = "Your GST Certificate expires in {{DaysUntilExpiry}} days. Renew now to avoid business disruption.",
            Language = Language.English,
            MessageType = MessageType.DocumentExpiry,
            Priority = Priority.Normal,
            Tags = new List<string> { "transport-company", "gst", "compliance", "30-day-notice" }
        },

        ["transport_company_gst_expiry_7"] = new NotificationTemplate
        {
            Id = "transport_company_gst_expiry_7",
            Name = "GST Certificate Expiry - 7 Days Critical Notice",
            Subject = "URGENT: GST Certificate Expiring in 7 Days",
            EmailBody = @"
Dear {{CompanyName}},

URGENT NOTICE: Your GST Certificate is expiring in just {{DaysUntilExpiry}} days.

Certificate Details:
- GST Number: {{GstNumber}}
- Expiry Date: {{ExpiryDate}}
- Status: CRITICAL - Immediate Action Required

IMMEDIATE ACTIONS REQUIRED:
1. Renew your GST certificate TODAY
2. Upload the renewed certificate immediately
3. Verify all business operations compliance

Failure to renew may result in:
- Business operation disruptions
- Compliance violations
- Potential penalties

If you need assistance with the renewal process, please contact our support team immediately.

Urgent regards,
TLI Logistics Compliance Team",
            SmsBody = "URGENT: GST Certificate expires in {{DaysUntilExpiry}} days! Renew immediately to avoid business disruption. Contact support if needed. - TLI",
            PushNotificationBody = "URGENT: GST Certificate expires in {{DaysUntilExpiry}} days! Renew now.",
            Language = Language.English,
            MessageType = MessageType.DocumentExpiry,
            Priority = Priority.High,
            Tags = new List<string> { "transport-company", "gst", "compliance", "critical", "7-day-notice" }
        },

        ["transport_company_gst_expired"] = new NotificationTemplate
        {
            Id = "transport_company_gst_expired",
            Name = "GST Certificate Expired - Immediate Action Required",
            Subject = "CRITICAL: GST Certificate Has Expired - Immediate Action Required",
            EmailBody = @"
Dear {{CompanyName}},

CRITICAL ALERT: Your GST Certificate has EXPIRED as of {{ExpiryDate}}.

Certificate Details:
- GST Number: {{GstNumber}}
- Expired On: {{ExpiryDate}}
- Days Overdue: {{DaysOverdue}}
- Status: EXPIRED - CRITICAL

IMMEDIATE ACTIONS REQUIRED:
1. Renew your GST certificate IMMEDIATELY
2. Upload the renewed certificate to restore compliance
3. Contact our compliance team for guidance

Your account may be subject to:
- Service restrictions
- Compliance penalties
- Business operation limitations

Please treat this as a PRIORITY and take immediate action.

For urgent assistance, contact our compliance team immediately.

Critical regards,
TLI Logistics Compliance Team",
            SmsBody = "CRITICAL: Your GST Certificate EXPIRED on {{ExpiryDate}}. Renew IMMEDIATELY to restore compliance. Contact support urgently. - TLI",
            PushNotificationBody = "CRITICAL: GST Certificate EXPIRED! Renew immediately to restore compliance.",
            Language = Language.English,
            MessageType = MessageType.DocumentExpiry,
            Priority = Priority.Critical,
            Tags = new List<string> { "transport-company", "gst", "compliance", "expired", "critical" }
        },

        // PAN Certificate Templates
        ["transport_company_pan_expiry_30"] = new NotificationTemplate
        {
            Id = "transport_company_pan_expiry_30",
            Name = "PAN Certificate Expiry - 30 Days Notice",
            Subject = "PAN Certificate Expiring Soon",
            EmailBody = @"
Dear {{CompanyName}},

Your PAN Certificate is expiring in {{DaysUntilExpiry}} days.

Certificate Details:
- PAN Number: {{PanNumber}}
- Expiry Date: {{ExpiryDate}}
- Days Until Expiry: {{DaysUntilExpiry}}

Please ensure to renew your PAN certificate and update your profile to maintain compliance.

Best regards,
TLI Logistics Team",
            SmsBody = "PAN Certificate expiring in {{DaysUntilExpiry}} days. Please renew and update your profile. - TLI Logistics",
            PushNotificationBody = "PAN Certificate expires in {{DaysUntilExpiry}} days. Renew to maintain compliance.",
            Language = Language.English,
            MessageType = MessageType.DocumentExpiry,
            Priority = Priority.Normal,
            Tags = new List<string> { "transport-company", "pan", "compliance", "30-day-notice" }
        },

        // Vehicle Insurance Templates
        ["transport_company_vehicle_insurance_expiry_15"] = new NotificationTemplate
        {
            Id = "transport_company_vehicle_insurance_expiry_15",
            Name = "Vehicle Insurance Expiry - 15 Days Notice",
            Subject = "Vehicle Insurance Expiring Soon - {{VehicleNumber}}",
            EmailBody = @"
Dear {{CompanyName}},

The insurance for your vehicle {{VehicleNumber}} is expiring in {{DaysUntilExpiry}} days.

Vehicle Details:
- Vehicle Number: {{VehicleNumber}}
- Insurance Policy: {{PolicyNumber}}
- Expiry Date: {{ExpiryDate}}
- Days Until Expiry: {{DaysUntilExpiry}}

Please renew the insurance policy to ensure:
- Legal compliance for vehicle operations
- Protection against accidents and damages
- Uninterrupted service delivery

Upload the renewed insurance certificate to your vehicle profile once renewed.

Best regards,
TLI Logistics Team",
            SmsBody = "Vehicle {{VehicleNumber}} insurance expires in {{DaysUntilExpiry}} days. Renew to maintain operations. - TLI",
            PushNotificationBody = "Vehicle {{VehicleNumber}} insurance expires in {{DaysUntilExpiry}} days. Renew now.",
            Language = Language.English,
            MessageType = MessageType.DocumentExpiry,
            Priority = Priority.High,
            Tags = new List<string> { "transport-company", "vehicle", "insurance", "compliance", "15-day-notice" }
        },

        // Driver License Templates
        ["transport_company_driver_license_expiry_7"] = new NotificationTemplate
        {
            Id = "transport_company_driver_license_expiry_7",
            Name = "Driver License Expiry - 7 Days Critical Notice",
            Subject = "URGENT: Driver License Expiring Soon - {{DriverName}}",
            EmailBody = @"
Dear {{CompanyName}},

URGENT: The driving license for {{DriverName}} is expiring in {{DaysUntilExpiry}} days.

Driver Details:
- Driver Name: {{DriverName}}
- License Number: {{LicenseNumber}}
- Expiry Date: {{ExpiryDate}}
- Days Until Expiry: {{DaysUntilExpiry}}

IMMEDIATE ACTIONS REQUIRED:
1. Ensure the driver renews their license immediately
2. Upload the renewed license to the driver profile
3. Temporarily suspend the driver from operations if license expires

An expired license will result in:
- Driver being unable to operate vehicles legally
- Potential legal and compliance issues
- Service disruptions

Please treat this as urgent and take immediate action.

Urgent regards,
TLI Logistics Team",
            SmsBody = "URGENT: {{DriverName}}'s license expires in {{DaysUntilExpiry}} days. Ensure immediate renewal. - TLI",
            PushNotificationBody = "URGENT: Driver {{DriverName}}'s license expires in {{DaysUntilExpiry}} days!",
            Language = Language.English,
            MessageType = MessageType.DocumentExpiry,
            Priority = Priority.High,
            Tags = new List<string> { "transport-company", "driver", "license", "compliance", "critical", "7-day-notice" }
        },

        // Grouped Reminders Template
        ["transport_company_grouped_expiry_notice"] = new NotificationTemplate
        {
            Id = "transport_company_grouped_expiry_notice",
            Name = "Multiple Documents Expiring - Grouped Notice",
            Subject = "Multiple {{EntityType}} Documents Expiring Soon",
            EmailBody = @"
Dear {{CompanyName}},

You have {{DocumentCount}} {{EntityType}} documents expiring soon that require your attention.

Expiring Documents:
{{#each Documents}}
- {{Type}}: Expires on {{ExpiryDate}} ({{DaysUntilExpiry}} days)
{{/each}}

Please take the following actions:
1. Review each document's expiry date
2. Renew documents before they expire
3. Upload renewed documents to maintain compliance
4. Contact support if you need assistance

Maintaining up-to-date documentation ensures:
- Continuous compliance with regulations
- Uninterrupted business operations
- Avoidance of penalties and restrictions

Best regards,
TLI Logistics Compliance Team",
            SmsBody = "{{DocumentCount}} {{EntityType}} documents expiring soon. Check your dashboard for details. - TLI",
            PushNotificationBody = "{{DocumentCount}} {{EntityType}} documents expiring soon. Review and renew.",
            Language = Language.English,
            MessageType = MessageType.DocumentExpiry,
            Priority = Priority.Normal,
            Tags = new List<string> { "transport-company", "grouped", "compliance", "multiple-documents" }
        }
    };

    public static NotificationTemplate? GetTemplate(string templateId)
    {
        return Templates.TryGetValue(templateId, out var template) ? template : null;
    }

    public static List<NotificationTemplate> GetTemplatesByDocumentType(string documentType)
    {
        return Templates.Values
            .Where(t => t.Tags.Contains(documentType.ToLower()))
            .ToList();
    }

    public static List<NotificationTemplate> GetTemplatesByPriority(Priority priority)
    {
        return Templates.Values
            .Where(t => t.Priority == priority)
            .ToList();
    }
}

/// <summary>
/// Notification template structure
/// </summary>
public class NotificationTemplate
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Subject { get; set; } = string.Empty;
    public string EmailBody { get; set; } = string.Empty;
    public string SmsBody { get; set; } = string.Empty;
    public string PushNotificationBody { get; set; } = string.Empty;
    public Language Language { get; set; } = Language.English;
    public MessageType MessageType { get; set; }
    public Priority Priority { get; set; }
    public List<string> Tags { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}
