using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Domain.Entities;
using System.Collections.Concurrent;

namespace FinancialPayment.Infrastructure.Services;

public class MonitoringService : IMonitoringService
{
    private readonly ILogger<MonitoringService> _logger;
    private readonly MonitoringConfiguration _configuration;
    
    // In-memory storage for demo purposes
    private readonly ConcurrentBag<SystemMetric> _metrics;
    private readonly ConcurrentBag<PerformanceMetric> _performanceMetrics;
    private readonly ConcurrentBag<Alert> _alerts;
    private readonly ConcurrentBag<MonitoringDashboard> _dashboards;

    public MonitoringService(
        ILogger<MonitoringService> logger,
        IOptions<MonitoringConfiguration> configuration)
    {
        _logger = logger;
        _configuration = configuration.Value;
        _metrics = new ConcurrentBag<SystemMetric>();
        _performanceMetrics = new ConcurrentBag<PerformanceMetric>();
        _alerts = new ConcurrentBag<Alert>();
        _dashboards = new ConcurrentBag<MonitoringDashboard>();
    }

    public async Task RecordMetricAsync(string metricName, decimal value, string unit, string source, Dictionary<string, string>? tags = null)
    {
        try
        {
            if (!_configuration.EnableMetricsCollection) return;

            var metric = new SystemMetric(metricName, "Gauge", value, unit, source, tags);
            _metrics.Add(metric);

            _logger.LogDebug("Recorded metric {MetricName}: {Value} {Unit} from {Source}", 
                metricName, value, unit, source);

            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording metric {MetricName}", metricName);
        }
    }

    public async Task RecordPerformanceAsync(string operationName, string serviceName, TimeSpan duration, bool isSuccess, string? errorMessage = null)
    {
        try
        {
            if (!_configuration.EnableMetricsCollection) return;

            var performanceMetric = new PerformanceMetric(
                operationName,
                serviceName,
                DateTime.UtcNow.Subtract(duration),
                DateTime.UtcNow,
                isSuccess,
                errorMessage);

            _performanceMetrics.Add(performanceMetric);

            _logger.LogDebug("Recorded performance metric for {OperationName} in {ServiceName}: {Duration}ms, Success: {IsSuccess}",
                operationName, serviceName, duration.TotalMilliseconds, isSuccess);

            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording performance metric for {OperationName}", operationName);
        }
    }

    public async Task<List<SystemMetric>> GetMetricsAsync(string? metricName = null, DateTime? fromDate = null, DateTime? toDate = null)
    {
        try
        {
            var from = fromDate ?? DateTime.UtcNow.AddHours(-24);
            var to = toDate ?? DateTime.UtcNow;

            var filteredMetrics = _metrics
                .Where(m => m.Timestamp >= from && m.Timestamp <= to)
                .Where(m => string.IsNullOrEmpty(metricName) || m.MetricName == metricName)
                .OrderByDescending(m => m.Timestamp)
                .ToList();

            return await Task.FromResult(filteredMetrics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting metrics");
            return new List<SystemMetric>();
        }
    }

    public async Task<List<PerformanceMetric>> GetPerformanceMetricsAsync(string? operationName = null, DateTime? fromDate = null, DateTime? toDate = null)
    {
        try
        {
            var from = fromDate ?? DateTime.UtcNow.AddHours(-24);
            var to = toDate ?? DateTime.UtcNow;

            var filteredMetrics = _performanceMetrics
                .Where(m => m.StartTime >= from && m.EndTime <= to)
                .Where(m => string.IsNullOrEmpty(operationName) || m.OperationName == operationName)
                .OrderByDescending(m => m.StartTime)
                .ToList();

            return await Task.FromResult(filteredMetrics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting performance metrics");
            return new List<PerformanceMetric>();
        }
    }

    public async Task<SystemHealthSummary> GetSystemHealthAsync()
    {
        try
        {
            // Simulate system health based on recent performance metrics
            var recentMetrics = await GetPerformanceMetricsAsync(fromDate: DateTime.UtcNow.AddMinutes(-5));
            
            var services = recentMetrics
                .GroupBy(m => m.ServiceName)
                .Select(g => new ServiceHealthStatus
                {
                    ServiceName = g.Key,
                    Status = CalculateServiceHealth(g.ToList()),
                    ResponseTime = TimeSpan.FromMilliseconds(g.Average(m => m.Duration.TotalMilliseconds)),
                    LastChecked = g.Max(m => m.EndTime),
                    ErrorMessage = g.Where(m => !m.IsSuccess).FirstOrDefault()?.ErrorMessage
                })
                .ToList();

            var healthyCount = services.Count(s => s.Status == HealthStatus.Healthy);
            var degradedCount = services.Count(s => s.Status == HealthStatus.Degraded);
            var unhealthyCount = services.Count(s => s.Status == HealthStatus.Unhealthy);

            var overallStatus = unhealthyCount > 0 ? HealthStatus.Unhealthy :
                               degradedCount > 0 ? HealthStatus.Degraded :
                               HealthStatus.Healthy;

            return new SystemHealthSummary
            {
                OverallStatus = overallStatus,
                HealthyServices = healthyCount,
                DegradedServices = degradedCount,
                UnhealthyServices = unhealthyCount,
                ServiceStatuses = services,
                LastUpdated = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting system health");
            return new SystemHealthSummary
            {
                OverallStatus = HealthStatus.Unknown,
                LastUpdated = DateTime.UtcNow
            };
        }
    }

    public async Task<List<Alert>> GetActiveAlertsAsync()
    {
        try
        {
            var activeAlerts = _alerts
                .Where(a => a.Status == AlertStatus.Active)
                .OrderByDescending(a => a.TriggeredAt)
                .ToList();

            return await Task.FromResult(activeAlerts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active alerts");
            return new List<Alert>();
        }
    }

    public async Task<Alert> CreateAlertAsync(string alertName, AlertSeverity severity, string message, string source, Dictionary<string, object>? alertData = null)
    {
        try
        {
            var alert = new Alert(alertName, severity, message, source, alertData);
            _alerts.Add(alert);

            _logger.LogWarning("Alert created: {AlertName} - {Severity} - {Message}", alertName, severity, message);

            // In a real implementation, you would send notifications here
            await SendAlertNotificationAsync(alert);

            return alert;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating alert {AlertName}", alertName);
            throw;
        }
    }

    public async Task<bool> AcknowledgeAlertAsync(Guid alertId, Guid acknowledgedBy, string? notes = null)
    {
        try
        {
            var alert = _alerts.FirstOrDefault(a => a.Id == alertId);
            if (alert == null) return false;

            alert.Acknowledge(acknowledgedBy, notes);
            
            _logger.LogInformation("Alert {AlertId} acknowledged by {UserId}", alertId, acknowledgedBy);
            return await Task.FromResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error acknowledging alert {AlertId}", alertId);
            return false;
        }
    }

    public async Task<bool> ResolveAlertAsync(Guid alertId, string? resolutionNotes = null)
    {
        try
        {
            var alert = _alerts.FirstOrDefault(a => a.Id == alertId);
            if (alert == null) return false;

            alert.Resolve(resolutionNotes);
            
            _logger.LogInformation("Alert {AlertId} resolved", alertId);
            return await Task.FromResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resolving alert {AlertId}", alertId);
            return false;
        }
    }

    public async Task<MonitoringDashboard> CreateDashboardAsync(CreateMonitoringDashboardRequest request)
    {
        try
        {
            var dashboard = new MonitoringDashboard(
                request.DashboardName,
                request.Description,
                request.CreatedBy,
                request.IsPublic);

            foreach (var widgetRequest in request.Widgets)
            {
                dashboard.AddWidget(
                    widgetRequest.WidgetName,
                    widgetRequest.WidgetType,
                    widgetRequest.Configuration,
                    widgetRequest.Order);
            }

            _dashboards.Add(dashboard);

            _logger.LogInformation("Monitoring dashboard created: {DashboardName}", request.DashboardName);
            return await Task.FromResult(dashboard);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating monitoring dashboard {DashboardName}", request.DashboardName);
            throw;
        }
    }

    public async Task<List<MonitoringDashboard>> GetDashboardsAsync(Guid? userId = null)
    {
        try
        {
            var dashboards = _dashboards
                .Where(d => userId == null || d.IsPublic || d.CreatedBy == userId)
                .OrderBy(d => d.DashboardName)
                .ToList();

            return await Task.FromResult(dashboards);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting monitoring dashboards");
            return new List<MonitoringDashboard>();
        }
    }

    public async Task<MonitoringDashboard?> GetDashboardAsync(Guid dashboardId)
    {
        try
        {
            var dashboard = _dashboards.FirstOrDefault(d => d.Id == dashboardId);
            return await Task.FromResult(dashboard);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting monitoring dashboard {DashboardId}", dashboardId);
            return null;
        }
    }

    private HealthStatus CalculateServiceHealth(List<PerformanceMetric> metrics)
    {
        if (!metrics.Any()) return HealthStatus.Unknown;

        var successRate = (decimal)metrics.Count(m => m.IsSuccess) / metrics.Count;
        var averageResponseTime = metrics.Average(m => m.Duration.TotalMilliseconds);

        if (successRate < 0.9 || averageResponseTime > 5000) // 90% success rate, 5 second response time
            return HealthStatus.Unhealthy;
        
        if (successRate < 0.95 || averageResponseTime > 2000) // 95% success rate, 2 second response time
            return HealthStatus.Degraded;

        return HealthStatus.Healthy;
    }

    private async Task SendAlertNotificationAsync(Alert alert)
    {
        try
        {
            // In a real implementation, this would send notifications via email, Slack, etc.
            _logger.LogInformation("Sending alert notification for {AlertName} - {Severity}", 
                alert.AlertName, alert.Severity);

            await Task.Delay(100); // Simulate notification sending
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending alert notification for {AlertName}", alert.AlertName);
        }
    }
}
