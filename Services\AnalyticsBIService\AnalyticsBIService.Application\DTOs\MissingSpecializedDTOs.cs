using System;
using System.Collections.Generic;

namespace AnalyticsBIService.Application.DTOs
{
    /// <summary>
    /// Real-time data point data transfer object
    /// </summary>
    public class RealTimeDataPointDto
    {
        public DateTime Timestamp { get; set; }
        public decimal Value { get; set; }
        public string Label { get; set; } = string.Empty;
    }

    /// <summary>
    /// Carrier network growth analytics data transfer object
    /// </summary>
    public class CarrierNetworkGrowthAnalyticsDto
    {
        public Guid BrokerId { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public string Period { get; set; } = string.Empty;
        public int TotalCarriers { get; set; }
        public int NewCarriersThisMonth { get; set; }
        public int ActiveCarriers { get; set; }
        public decimal GrowthRate { get; set; }
        public List<CarrierGrowthTrendDto> GrowthTrends { get; set; } = new();
        public List<CarrierSegmentDto> CarrierSegments { get; set; } = new();
        public List<GeographicGrowthDto> GeographicGrowth { get; set; } = new();
    }

    /// <summary>
    /// Carrier growth trend data transfer object
    /// </summary>
    public class CarrierGrowthTrendDto
    {
        public DateTime Period { get; set; }
        public int NewCarriers { get; set; }
        public int TotalCarriers { get; set; }
        public decimal GrowthRate { get; set; }
    }

    /// <summary>
    /// Carrier segment data transfer object
    /// </summary>
    public class CarrierSegmentDto
    {
        public string SegmentName { get; set; } = string.Empty;
        public int CarrierCount { get; set; }
        public decimal Percentage { get; set; }
        public decimal GrowthRate { get; set; }
    }

    /// <summary>
    /// Geographic growth data transfer object
    /// </summary>
    public class GeographicGrowthDto
    {
        public string Region { get; set; } = string.Empty;
        public string State { get; set; } = string.Empty;
        public string City { get; set; } = string.Empty;
        public int CarrierCount { get; set; }
        public decimal GrowthRate { get; set; }
    }

    /// <summary>
    /// Broker efficiency metrics data transfer object
    /// </summary>
    public class BrokerEfficiencyMetricsDto
    {
        public Guid BrokerId { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public string Period { get; set; } = string.Empty;
        public decimal QuoteResponseTime { get; set; }
        public decimal LoadMatchingAccuracy { get; set; }
        public decimal CustomerSatisfactionScore { get; set; }
        public decimal RevenuePerLoad { get; set; }
        public decimal OperationalEfficiency { get; set; }
        public List<EfficiencyTrendDto> EfficiencyTrends { get; set; } = new();
        public List<BenchmarkComparisonDto> BenchmarkComparisons { get; set; } = new();
    }



    /// <summary>
    /// Broker customer satisfaction data transfer object
    /// </summary>
    public class BrokerCustomerSatisfactionDto
    {
        public Guid BrokerId { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public string Period { get; set; } = string.Empty;
        public decimal OverallSatisfactionScore { get; set; }
        public int TotalResponses { get; set; }
        public List<SatisfactionCategoryDto> CategoryScores { get; set; } = new();
        public List<CustomerFeedbackDto> RecentFeedback { get; set; } = new();
        public List<SatisfactionTrendDto> SatisfactionTrends { get; set; } = new();
    }

    /// <summary>
    /// Satisfaction category data transfer object
    /// </summary>
    public class SatisfactionCategoryDto
    {
        public string Category { get; set; } = string.Empty;
        public decimal Score { get; set; }
        public int ResponseCount { get; set; }
    }

    /// <summary>
    /// Customer feedback data transfer object
    /// </summary>


    /// <summary>
    /// Satisfaction trend data transfer object
    /// </summary>
    public class SatisfactionTrendDto
    {
        public DateTime Period { get; set; }
        public decimal Score { get; set; }
        public int ResponseCount { get; set; }
    }

    /// <summary>
    /// Broker competitive analysis data transfer object
    /// </summary>
    public class BrokerCompetitiveAnalysisDto
    {
        public Guid BrokerId { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public string Period { get; set; } = string.Empty;
        public List<CompetitorMetricDto> CompetitorMetrics { get; set; } = new();
        public List<CompetitiveAdvantageDto> CompetitiveAdvantages { get; set; } = new();
    }

    /// <summary>
    /// Competitor metric data transfer object
    /// </summary>
    public class CompetitorMetricDto
    {
        public string CompetitorName { get; set; } = string.Empty;
        public string MetricName { get; set; } = string.Empty;
        public decimal CompetitorValue { get; set; }
        public decimal OurValue { get; set; }
        public decimal Difference { get; set; }
        public string Performance { get; set; } = string.Empty;
    }







    /// <summary>
    /// Broker route optimization data transfer object
    /// </summary>
    public class BrokerRouteOptimizationDto
    {
        public Guid BrokerId { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public string Period { get; set; } = string.Empty;
        public List<RouteOptimizationDto> OptimizedRoutes { get; set; } = new();
        public decimal TotalSavings { get; set; }
        public decimal EfficiencyGain { get; set; }
        public List<OptimizationRecommendationDto> Recommendations { get; set; } = new();
    }

    /// <summary>
    /// Route optimization data transfer object
    /// </summary>
    public class RouteOptimizationDto
    {
        public string RouteId { get; set; } = string.Empty;
        public string Origin { get; set; } = string.Empty;
        public string Destination { get; set; } = string.Empty;
        public decimal CurrentCost { get; set; }
        public decimal OptimizedCost { get; set; }
        public decimal Savings { get; set; }
        public string OptimizationType { get; set; } = string.Empty;

        // Additional properties for analytics
        public decimal RouteEfficiencyScore { get; set; }
        public decimal RouteOptimizationPotential { get; set; }
        public List<RouteRecommendationDto> RouteRecommendations { get; set; } = new();
    }

    /// <summary>
    /// Optimization recommendation data transfer object
    /// </summary>
    public class OptimizationRecommendationDto
    {
        public string RecommendationType { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public decimal PotentialSavings { get; set; }
        public string Implementation { get; set; } = string.Empty;
        public string Priority { get; set; } = string.Empty;
    }

    /// <summary>
    /// Broker capacity management data transfer object
    /// </summary>
    public class BrokerCapacityManagementDto
    {
        public Guid BrokerId { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public string Period { get; set; } = string.Empty;
        public decimal TotalCapacity { get; set; }
        public decimal UtilizedCapacity { get; set; }
        public decimal AvailableCapacity { get; set; }
        public decimal UtilizationRate { get; set; }
        public List<CapacityTrendDto> CapacityTrends { get; set; } = new();
        public List<CapacityForecastDto> CapacityForecasts { get; set; } = new();
    }

    /// <summary>
    /// Capacity trend data transfer object
    /// </summary>
    public class CapacityTrendDto
    {
        public DateTime Period { get; set; }
        public decimal TotalCapacity { get; set; }
        public decimal UtilizedCapacity { get; set; }
        public decimal UtilizationRate { get; set; }
    }


}
