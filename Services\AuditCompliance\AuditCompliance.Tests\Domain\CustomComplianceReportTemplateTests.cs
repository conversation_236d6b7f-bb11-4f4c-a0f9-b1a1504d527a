using Shared.Domain.Common;
using AuditCompliance.Domain.Entities;
using AuditCompliance.Domain.Enums;
using AuditCompliance.Domain.Events;
using AuditCompliance.Domain.ValueObjects;
using FluentAssertions;
using Xunit;

namespace AuditCompliance.Tests.Domain
{
    /// <summary>
    /// Unit tests for CustomComplianceReportTemplate domain entity
    /// </summary>
    public class CustomComplianceReportTemplateTests
    {
        [Fact]
        public void Constructor_WithValidParameters_ShouldCreateTemplate()
        {
            // Arrange
            var templateName = "Test Template";
            var description = "Test template description";
            var complianceStandard = ComplianceStandard.SOX;
            var templateType = ReportTemplateType.Custom;
            var templateContent = "Template content";
            var sections = new List<ReportSection>();
            var parameters = new List<ReportParameter>();
            var createdBy = Guid.NewGuid();
            var createdByName = "Test User";
            var tags = new List<string> { "test", "compliance" };

            // Act
            var template = new CustomComplianceReportTemplate(
                templateName,
                description,
                complianceStandard,
                templateType,
                templateContent,
                sections,
                parameters,
                createdBy,
                createdByName,
                tags);

            // Assert
            template.TemplateName.Should().Be(templateName);
            template.Description.Should().Be(description);
            template.ComplianceStandard.Should().Be(complianceStandard);
            template.TemplateType.Should().Be(templateType);
            template.TemplateContent.Should().Be(templateContent);
            template.Sections.Should().BeEquivalentTo(sections);
            template.Parameters.Should().BeEquivalentTo(parameters);
            template.CreatedBy.Should().Be(createdBy);
            template.CreatedByName.Should().Be(createdByName);
            template.Tags.Should().BeEquivalentTo(tags);
            template.IsActive.Should().BeTrue();
            template.IsPublic.Should().BeFalse();
            template.UsageCount.Should().Be(0);
            template.Version.Should().Be("1.0");
        }

        [Theory]
        [InlineData("")]
        [InlineData(null)]
        [InlineData("   ")]
        public void Constructor_WithInvalidTemplateName_ShouldThrowArgumentException(string invalidTemplateName)
        {
            // Arrange & Act & Assert
            var action = () => new CustomComplianceReportTemplate(
                invalidTemplateName,
                "Description",
                ComplianceStandard.SOX,
                ReportTemplateType.Custom,
                "Content",
                new List<ReportSection>(),
                new List<ReportParameter>(),
                Guid.NewGuid(),
                "User",
                new List<string>());

            action.Should().Throw<ArgumentException>()
                .WithMessage("Template name cannot be empty*");
        }

        [Theory]
        [InlineData("")]
        [InlineData(null)]
        [InlineData("   ")]
        public void Constructor_WithInvalidTemplateContent_ShouldThrowArgumentException(string invalidContent)
        {
            // Arrange & Act & Assert
            var action = () => new CustomComplianceReportTemplate(
                "Template Name",
                "Description",
                ComplianceStandard.SOX,
                ReportTemplateType.Custom,
                invalidContent,
                new List<ReportSection>(),
                new List<ReportParameter>(),
                Guid.NewGuid(),
                "User",
                new List<string>());

            action.Should().Throw<ArgumentException>()
                .WithMessage("Template content cannot be empty*");
        }

        [Fact]
        public void Constructor_ShouldRaiseCustomReportTemplateCreatedEvent()
        {
            // Arrange & Act
            var template = CreateTestTemplate();

            // Assert
            var domainEvents = template.GetDomainEvents();
            domainEvents.Should().HaveCount(1);
            domainEvents.First().Should().BeOfType<CustomReportTemplateCreatedEvent>();
        }

        [Fact]
        public void UpdateTemplate_WithValidParameters_ShouldUpdateProperties()
        {
            // Arrange
            var template = CreateTestTemplate();
            var newTemplateName = "Updated Template";
            var newDescription = "Updated description";
            var newTemplateContent = "Updated content";
            var newSections = new List<ReportSection>();
            var newParameters = new List<ReportParameter>();
            var newTags = new List<string> { "updated", "test" };

            template.ClearDomainEvents();

            // Act
            template.UpdateTemplate(
                newTemplateName,
                newDescription,
                newTemplateContent,
                newSections,
                newParameters,
                newTags);

            // Assert
            template.TemplateName.Should().Be(newTemplateName);
            template.Description.Should().Be(newDescription);
            template.TemplateContent.Should().Be(newTemplateContent);
            template.Sections.Should().BeEquivalentTo(newSections);
            template.Parameters.Should().BeEquivalentTo(newParameters);
            template.Tags.Should().BeEquivalentTo(newTags);
            template.Version.Should().Be("1.1"); // Version should increment
        }

        [Fact]
        public void UpdateTemplate_ShouldRaiseCustomReportTemplateUpdatedEvent()
        {
            // Arrange
            var template = CreateTestTemplate();
            template.ClearDomainEvents();

            // Act
            template.UpdateTemplate(
                "Updated Name",
                "Updated Description",
                "Updated Content",
                new List<ReportSection>(),
                new List<ReportParameter>(),
                new List<string>());

            // Assert
            var domainEvents = template.GetDomainEvents();
            domainEvents.Should().HaveCount(1);
            domainEvents.First().Should().BeOfType<CustomReportTemplateUpdatedEvent>();
        }

        [Fact]
        public void Activate_WhenInactive_ShouldActivateTemplate()
        {
            // Arrange
            var template = CreateTestTemplate();
            template.Deactivate();
            template.ClearDomainEvents();

            // Act
            template.Activate();

            // Assert
            template.IsActive.Should().BeTrue();
            var domainEvents = template.GetDomainEvents();
            domainEvents.Should().HaveCount(1);
            domainEvents.First().Should().BeOfType<CustomReportTemplateActivatedEvent>();
        }

        [Fact]
        public void Deactivate_WhenActive_ShouldDeactivateTemplate()
        {
            // Arrange
            var template = CreateTestTemplate();
            template.ClearDomainEvents();

            // Act
            template.Deactivate();

            // Assert
            template.IsActive.Should().BeFalse();
            var domainEvents = template.GetDomainEvents();
            domainEvents.Should().HaveCount(1);
            domainEvents.First().Should().BeOfType<CustomReportTemplateDeactivatedEvent>();
        }

        [Fact]
        public void MakePublic_WhenPrivate_ShouldMakeTemplatePublic()
        {
            // Arrange
            var template = CreateTestTemplate();
            template.ClearDomainEvents();

            // Act
            template.MakePublic();

            // Assert
            template.IsPublic.Should().BeTrue();
            var domainEvents = template.GetDomainEvents();
            domainEvents.Should().HaveCount(1);
            domainEvents.First().Should().BeOfType<CustomReportTemplateMadePublicEvent>();
        }

        [Fact]
        public void MakePrivate_WhenPublic_ShouldMakeTemplatePrivate()
        {
            // Arrange
            var template = CreateTestTemplate();
            template.MakePublic();
            template.ClearDomainEvents();

            // Act
            template.MakePrivate();

            // Assert
            template.IsPublic.Should().BeFalse();
            var domainEvents = template.GetDomainEvents();
            domainEvents.Should().HaveCount(1);
            domainEvents.First().Should().BeOfType<CustomReportTemplateMadePrivateEvent>();
        }

        [Fact]
        public void RecordUsage_ShouldIncrementUsageCountAndUpdateLastUsed()
        {
            // Arrange
            var template = CreateTestTemplate();
            var initialUsageCount = template.UsageCount;
            template.ClearDomainEvents();

            // Act
            template.RecordUsage();

            // Assert
            template.UsageCount.Should().Be(initialUsageCount + 1);
            template.LastUsed.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
            var domainEvents = template.GetDomainEvents();
            domainEvents.Should().HaveCount(1);
            domainEvents.First().Should().BeOfType<CustomReportTemplateUsedEvent>();
        }

        [Fact]
        public void GrantPermission_WithNewUser_ShouldAddPermission()
        {
            // Arrange
            var template = CreateTestTemplate();
            var userId = Guid.NewGuid();
            var userName = "Test User";
            var permissionType = TemplatePermissionType.Edit;
            var grantedBy = Guid.NewGuid();
            template.ClearDomainEvents();

            // Act
            template.GrantPermission(userId, userName, permissionType, grantedBy);

            // Assert
            template.Permissions.Should().HaveCount(1);
            var permission = template.Permissions.First();
            permission.UserId.Should().Be(userId);
            permission.UserName.Should().Be(userName);
            permission.PermissionType.Should().Be(permissionType);
            permission.GrantedBy.Should().Be(grantedBy);
            permission.IsActive.Should().BeTrue();

            var domainEvents = template.GetDomainEvents();
            domainEvents.Should().HaveCount(1);
            domainEvents.First().Should().BeOfType<TemplatePermissionGrantedEvent>();
        }

        [Fact]
        public void GrantPermission_WithExistingUser_ShouldUpdatePermission()
        {
            // Arrange
            var template = CreateTestTemplate();
            var userId = Guid.NewGuid();
            var userName = "Test User";
            var grantedBy = Guid.NewGuid();

            // Grant initial permission
            template.GrantPermission(userId, userName, TemplatePermissionType.View, grantedBy);
            template.ClearDomainEvents();

            // Act - Grant higher permission
            template.GrantPermission(userId, userName, TemplatePermissionType.Edit, grantedBy);

            // Assert
            template.Permissions.Should().HaveCount(1);
            var permission = template.Permissions.First();
            permission.PermissionType.Should().Be(TemplatePermissionType.Edit);
        }

        [Fact]
        public void RevokePermission_WithExistingUser_ShouldRevokePermission()
        {
            // Arrange
            var template = CreateTestTemplate();
            var userId = Guid.NewGuid();
            var userName = "Test User";
            var grantedBy = Guid.NewGuid();
            var revokedBy = Guid.NewGuid();

            template.GrantPermission(userId, userName, TemplatePermissionType.Edit, grantedBy);
            template.ClearDomainEvents();

            // Act
            template.RevokePermission(userId, revokedBy);

            // Assert
            var permission = template.Permissions.First();
            permission.IsActive.Should().BeFalse();
            permission.RevokedBy.Should().Be(revokedBy);
            permission.RevokedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));

            var domainEvents = template.GetDomainEvents();
            domainEvents.Should().HaveCount(1);
            domainEvents.First().Should().BeOfType<TemplatePermissionRevokedEvent>();
        }

        [Fact]
        public void HasPermission_AsCreator_ShouldReturnTrue()
        {
            // Arrange
            var createdBy = Guid.NewGuid();
            var template = new CustomComplianceReportTemplate(
                "Test Template",
                "Description",
                ComplianceStandard.SOX,
                ReportTemplateType.Custom,
                "Content",
                new List<ReportSection>(),
                new List<ReportParameter>(),
                createdBy,
                "Creator",
                new List<string>());

            // Act & Assert
            template.HasPermission(createdBy, TemplatePermissionType.Admin).Should().BeTrue();
            template.HasPermission(createdBy, TemplatePermissionType.Edit).Should().BeTrue();
            template.HasPermission(createdBy, TemplatePermissionType.View).Should().BeTrue();
        }

        [Fact]
        public void HasPermission_ForPublicTemplate_ShouldAllowView()
        {
            // Arrange
            var template = CreateTestTemplate();
            template.MakePublic();
            var randomUserId = Guid.NewGuid();

            // Act & Assert
            template.HasPermission(randomUserId, TemplatePermissionType.View).Should().BeTrue();
            template.HasPermission(randomUserId, TemplatePermissionType.Edit).Should().BeFalse();
        }

        [Fact]
        public void HasPermission_WithGrantedPermission_ShouldReturnTrue()
        {
            // Arrange
            var template = CreateTestTemplate();
            var userId = Guid.NewGuid();
            var grantedBy = Guid.NewGuid();

            template.GrantPermission(userId, "User", TemplatePermissionType.Edit, grantedBy);

            // Act & Assert
            template.HasPermission(userId, TemplatePermissionType.View).Should().BeTrue();
            template.HasPermission(userId, TemplatePermissionType.Edit).Should().BeTrue();
            template.HasPermission(userId, TemplatePermissionType.Admin).Should().BeFalse();
        }

        [Fact]
        public void ValidateTemplate_WithValidTemplate_ShouldReturnTrue()
        {
            // Arrange
            var template = CreateTestTemplate();

            // Act
            var isValid = template.ValidateTemplate();

            // Assert
            isValid.Should().BeTrue();
        }

        [Fact]
        public void SetMetadata_ShouldUpdateMetadata()
        {
            // Arrange
            var template = CreateTestTemplate();
            var key = "testKey";
            var value = "testValue";

            // Act
            template.SetMetadata(key, value);

            // Assert
            template.Metadata.Should().ContainKey(key);
            template.Metadata[key].Should().Be(value);
        }

        [Fact]
        public void GetMetadata_WithExistingKey_ShouldReturnValue()
        {
            // Arrange
            var template = CreateTestTemplate();
            var key = "testKey";
            var value = "testValue";
            template.SetMetadata(key, value);

            // Act
            var retrievedValue = template.GetMetadata<string>(key);

            // Assert
            retrievedValue.Should().Be(value);
        }

        [Fact]
        public void GetMetadata_WithNonExistentKey_ShouldReturnDefault()
        {
            // Arrange
            var template = CreateTestTemplate();

            // Act
            var retrievedValue = template.GetMetadata<string>("nonExistentKey");

            // Assert
            retrievedValue.Should().BeNull();
        }

        private static CustomComplianceReportTemplate CreateTestTemplate()
        {
            return new CustomComplianceReportTemplate(
                "Test Template",
                "Test template description",
                ComplianceStandard.SOX,
                ReportTemplateType.Custom,
                "Template content",
                new List<ReportSection>(),
                new List<ReportParameter>(),
                Guid.NewGuid(),
                "Test User",
                new List<string> { "test", "compliance" });
        }
    }
}

