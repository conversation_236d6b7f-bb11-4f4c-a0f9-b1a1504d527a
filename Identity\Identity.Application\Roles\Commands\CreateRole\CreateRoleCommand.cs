using System;
using System.Collections.Generic;
using MediatR;

namespace Identity.Application.Roles.Commands.CreateRole
{
    public class CreateRoleCommand : IRequest<Guid>
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public string Department { get; set; }
        public bool IsTemplate { get; set; }
        public Guid? ParentRoleId { get; set; }
        public int Priority { get; set; }
        public List<Guid> PermissionIds { get; set; } = new();
        public Guid CreatedBy { get; set; }
    }
}
