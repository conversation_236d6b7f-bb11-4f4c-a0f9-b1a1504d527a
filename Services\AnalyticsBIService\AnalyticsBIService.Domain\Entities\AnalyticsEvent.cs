using AnalyticsBIService.Domain.Enums;
using AnalyticsBIService.Domain.Events;
using AnalyticsBIService.Domain.ValueObjects;
using Shared.Domain.Common;

namespace AnalyticsBIService.Domain.Entities;

/// <summary>
/// Core analytics event entity for tracking all platform activities
/// </summary>
public class AnalyticsEvent : BaseEntity
{
    public string EventName { get; private set; }
    public AnalyticsEventType EventType { get; private set; }
    public DataSourceType DataSource { get; private set; }
    public Guid? UserId { get; private set; }
    public UserType? UserType { get; private set; }
    public Guid? EntityId { get; private set; }
    public string? EntityType { get; private set; }
    public DateTime Timestamp { get; private set; }
    public Dictionary<string, object> Properties { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }
    public string? SessionId { get; private set; }
    public string? IpAddress { get; private set; }
    public string? UserAgent { get; private set; }
    public bool IsActive { get; private set; } = true;
    public bool IsProcessed { get; private set; } = false;
    public DateTime? ProcessedAt { get; private set; }
    public bool ProcessingFailed { get; private set; } = false;

    private AnalyticsEvent()
    {
        EventName = string.Empty;
        Properties = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();
    }

    public AnalyticsEvent(
        string eventName,
        AnalyticsEventType eventType,
        DataSourceType dataSource,
        Guid? userId = null,
        UserType? userType = null,
        Guid? entityId = null,
        string? entityType = null,
        Dictionary<string, object>? properties = null,
        Dictionary<string, object>? metadata = null,
        string? sessionId = null,
        string? ipAddress = null,
        string? userAgent = null)
    {
        EventName = eventName ?? throw new ArgumentNullException(nameof(eventName));
        EventType = eventType;
        DataSource = dataSource;
        UserId = userId;
        UserType = userType;
        EntityId = entityId;
        EntityType = entityType;
        Timestamp = DateTime.UtcNow;
        Properties = properties ?? new Dictionary<string, object>();
        Metadata = metadata ?? new Dictionary<string, object>();
        SessionId = sessionId;
        IpAddress = ipAddress;
        UserAgent = userAgent;

        // Add domain event
        AddDomainEvent(new AnalyticsEventCreatedEvent(Id, EventName, EventType, DataSource, UserId, Timestamp));
    }

    public static AnalyticsEvent CreateUserActivity(
        string eventName,
        Guid userId,
        UserType userType,
        Dictionary<string, object>? properties = null,
        string? sessionId = null)
    {
        return new AnalyticsEvent(
            eventName,
            AnalyticsEventType.UserActivity,
            DataSourceType.External,
            userId,
            userType,
            properties: properties,
            sessionId: sessionId);
    }

    public static AnalyticsEvent CreateBusinessTransaction(
        string eventName,
        DataSourceType dataSource,
        Guid entityId,
        string entityType,
        Dictionary<string, object>? properties = null,
        Guid? userId = null)
    {
        return new AnalyticsEvent(
            eventName,
            AnalyticsEventType.BusinessTransaction,
            dataSource,
            userId,
            entityId: entityId,
            entityType: entityType,
            properties: properties);
    }

    public static AnalyticsEvent CreateSystemPerformance(
        string eventName,
        DataSourceType dataSource,
        Dictionary<string, object>? properties = null)
    {
        return new AnalyticsEvent(
            eventName,
            AnalyticsEventType.SystemPerformance,
            dataSource,
            properties: properties);
    }

    public void AddProperty(string key, object value)
    {
        Properties[key] = value;
        SetUpdatedAt();
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
        SetUpdatedAt();
    }

    public T? GetProperty<T>(string key)
    {
        if (Properties.TryGetValue(key, out var value) && value is T typedValue)
            return typedValue;
        return default;
    }

    public T? GetMetadata<T>(string key)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
            return typedValue;
        return default;
    }

    public bool HasProperty(string key) => Properties.ContainsKey(key);
    public bool HasMetadata(string key) => Metadata.ContainsKey(key);

    public void MarkAsProcessed()
    {
        IsProcessed = true;
        ProcessedAt = DateTime.UtcNow;
        ProcessingFailed = false;
        SetUpdatedAt();
    }

    public void MarkProcessingFailed()
    {
        ProcessingFailed = true;
        ProcessedAt = DateTime.UtcNow;
        SetUpdatedAt();
    }

    private readonly List<IDomainEvent> _domainEvents = new();
    public IReadOnlyCollection<IDomainEvent> DomainEvents => _domainEvents.AsReadOnly();

    protected void AddDomainEvent(IDomainEvent domainEvent)
    {
        _domainEvents.Add(domainEvent);
    }

    public void ClearDomainEvents()
    {
        _domainEvents.Clear();
    }
}

/// <summary>
/// Aggregated metrics entity for performance tracking
/// </summary>
public class Metric : BaseEntity
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public MetricType Type { get; private set; }
    public KPICategory Category { get; private set; }
    public MetricValue Value { get; private set; }
    public KPITarget? Target { get; private set; }
    public TimePeriodValue Period { get; private set; }
    public Guid? UserId { get; private set; }
    public UserType? UserType { get; private set; }
    public DataSourceType DataSource { get; private set; }
    public Dictionary<string, string> Tags { get; private set; }
    public bool IsActive { get; private set; } = true;

    // Additional properties for compatibility
    public Guid? EntityId => UserId;
    public string? EntityType { get; private set; }
    public DateTime Timestamp => CreatedAt;
    public Guid? CreatedBy => UserId;

    private Metric()
    {
        Name = string.Empty;
        Description = string.Empty;
        Value = new MetricValue(0, MetricType.Counter, "count", DateTime.UtcNow);
        Period = new TimePeriodValue(DateTime.UtcNow, DateTime.UtcNow.AddDays(1), TimePeriod.Daily);
        Tags = new Dictionary<string, string>();
    }

    public Metric(
        string name,
        string description,
        MetricType type,
        KPICategory category,
        MetricValue value,
        TimePeriodValue period,
        DataSourceType dataSource,
        Guid? userId = null,
        UserType? userType = null,
        KPITarget? target = null,
        Dictionary<string, string>? tags = null)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        Type = type;
        Category = category;
        Value = value ?? throw new ArgumentNullException(nameof(value));
        Period = period ?? throw new ArgumentNullException(nameof(period));
        DataSource = dataSource;
        UserId = userId;
        UserType = userType;
        Target = target;
        Tags = tags ?? new Dictionary<string, string>();

        // Add domain event
        AddDomainEvent(new MetricCalculatedEvent(Id, Name, Value.Value, Type, (MetricCategory)Category));
    }

    public PerformanceStatus GetPerformanceStatus()
    {
        return Target?.EvaluatePerformance(Value.Value) ?? PerformanceStatus.Average;
    }

    public AlertSeverity GetAlertSeverity()
    {
        return Target?.GetAlertSeverity(Value.Value) ?? AlertSeverity.Info;
    }

    public decimal? GetTargetAchievementPercentage()
    {
        return Target?.GetTargetAchievementPercentage(Value.Value);
    }

    public void UpdateValue(MetricValue newValue)
    {
        var oldValue = Value.Value;
        Value = newValue ?? throw new ArgumentNullException(nameof(newValue));
        SetUpdatedAt();

        // Add domain event
        AddDomainEvent(new MetricUpdatedEvent(Id, Name, oldValue, newValue.Value, Type, (MetricCategory)Category));
    }

    public void SetTarget(KPITarget target)
    {
        Target = target;
        SetUpdatedAt();
    }

    public void AddTag(string key, string value)
    {
        Tags[key] = value;
        SetUpdatedAt();
    }

    public void RemoveTag(string key)
    {
        Tags.Remove(key);
        SetUpdatedAt();
    }

    private readonly List<IDomainEvent> _domainEvents = new();
    public IReadOnlyCollection<IDomainEvent> DomainEvents => _domainEvents.AsReadOnly();

    protected void AddDomainEvent(IDomainEvent domainEvent)
    {
        _domainEvents.Add(domainEvent);
    }

    public void ClearDomainEvents()
    {
        _domainEvents.Clear();
    }
}
