using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OrderManagement.Application.DTOs;
using OrderManagement.Application.Queries.GetBrokerQuoteAnalytics;
using OrderManagement.Application.Queries.GetTransporterLoadPostings;
using OrderManagement.Application.Queries.GetShipperOrderLifecycle;
using OrderManagement.Domain.Enums;
using System.Security.Claims;

namespace OrderManagement.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class AnalyticsController : BaseController
{
    /// <summary>
    /// Get quote analytics dashboard for brokers
    /// </summary>
    /// <param name="brokerId">Broker ID (optional - defaults to current user)</param>
    /// <param name="fromDate">Start date for analysis</param>
    /// <param name="toDate">End date for analysis</param>
    /// <param name="carrierIds">Filter by specific carriers</param>
    /// <param name="loadTypes">Filter by load types</param>
    /// <param name="routes">Filter by specific routes</param>
    /// <param name="includeDaily">Include daily breakdown</param>
    /// <param name="includeCarriers">Include carrier breakdown</param>
    /// <param name="includeLoadTypes">Include load type breakdown</param>
    /// <param name="includeRoutes">Include route breakdown</param>
    /// <param name="topRoutesLimit">Limit for top performing routes</param>
    /// <returns>Broker quote analytics</returns>
    [HttpGet("broker/quotes")]
    [Authorize(Roles = "Admin,Broker")]
    public async Task<ActionResult<BrokerQuoteAnalyticsDto>> GetBrokerQuoteAnalytics(
        [FromQuery] Guid? brokerId = null,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] List<Guid>? carrierIds = null,
        [FromQuery] List<LoadType>? loadTypes = null,
        [FromQuery] List<string>? routes = null,
        [FromQuery] bool includeDaily = true,
        [FromQuery] bool includeCarriers = true,
        [FromQuery] bool includeLoadTypes = true,
        [FromQuery] bool includeRoutes = true,
        [FromQuery] int topRoutesLimit = 10)
    {
        try
        {
            // Use provided brokerId or current user's ID
            var targetBrokerId = brokerId ?? GetCurrentUserId();
            
            // Ensure broker can only access their own data (unless admin)
            if (!IsAdmin() && targetBrokerId != GetCurrentUserId())
            {
                return Forbid("Brokers can only access their own analytics");
            }

            var query = new GetBrokerQuoteAnalyticsQuery(targetBrokerId, fromDate, toDate)
            {
                CarrierIds = carrierIds,
                LoadTypes = loadTypes,
                Routes = routes,
                IncludeDailyBreakdown = includeDaily,
                IncludeCarrierBreakdown = includeCarriers,
                IncludeLoadTypeBreakdown = includeLoadTypes,
                IncludeRouteBreakdown = includeRoutes,
                IncludeTopPerformingRoutes = true,
                TopRoutesLimit = topRoutesLimit
            };

            var result = await Mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "An error occurred while retrieving broker quote analytics", error = ex.Message });
        }
    }

    /// <summary>
    /// Get load posting analytics for transporters/carriers
    /// </summary>
    /// <param name="transporterId">Transporter ID (optional - defaults to current user)</param>
    /// <param name="fromDate">Start date for analysis</param>
    /// <param name="toDate">End date for analysis</param>
    /// <param name="statusFilter">Filter by posting status</param>
    /// <param name="brokerIds">Filter by specific brokers</param>
    /// <param name="loadTypes">Filter by load types</param>
    /// <param name="routes">Filter by specific routes</param>
    /// <param name="includeDaily">Include daily breakdown</param>
    /// <param name="includeBrokers">Include broker breakdown</param>
    /// <param name="includeStatus">Include status breakdown</param>
    /// <param name="includeRoutes">Include route breakdown</param>
    /// <returns>Transporter load posting analytics</returns>
    [HttpGet("transporter/load-postings")]
    [Authorize(Roles = "Admin,Carrier,TransportCompany")]
    public async Task<ActionResult<TransporterLoadPostingAnalyticsDto>> GetTransporterLoadPostingAnalytics(
        [FromQuery] Guid? transporterId = null,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] List<string>? statusFilter = null,
        [FromQuery] List<Guid>? brokerIds = null,
        [FromQuery] List<LoadType>? loadTypes = null,
        [FromQuery] List<string>? routes = null,
        [FromQuery] bool includeDaily = true,
        [FromQuery] bool includeBrokers = true,
        [FromQuery] bool includeStatus = true,
        [FromQuery] bool includeRoutes = true)
    {
        try
        {
            // Use provided transporterId or current user's ID
            var targetTransporterId = transporterId ?? GetCurrentUserId();
            
            // Ensure transporter can only access their own data (unless admin)
            if (!IsAdmin() && targetTransporterId != GetCurrentUserId())
            {
                return Forbid("Transporters can only access their own analytics");
            }

            var query = new GetTransporterLoadPostingsQuery(targetTransporterId, fromDate, toDate)
            {
                StatusFilter = statusFilter,
                BrokerIds = brokerIds,
                LoadTypes = loadTypes,
                Routes = routes,
                IncludeDailyBreakdown = includeDaily,
                IncludeBrokerBreakdown = includeBrokers,
                IncludeStatusBreakdown = includeStatus,
                IncludeRouteBreakdown = includeRoutes,
                IncludeConversionAnalytics = true
            };

            var result = await Mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "An error occurred while retrieving transporter load posting analytics", error = ex.Message });
        }
    }

    /// <summary>
    /// Get order lifecycle analytics for shippers
    /// </summary>
    /// <param name="shipperId">Shipper ID (optional - defaults to current user)</param>
    /// <param name="fromDate">Start date for analysis</param>
    /// <param name="toDate">End date for analysis</param>
    /// <param name="statusFilter">Filter by order status</param>
    /// <param name="serviceProviderIds">Filter by specific service providers</param>
    /// <param name="loadTypes">Filter by load types</param>
    /// <param name="routes">Filter by specific routes</param>
    /// <param name="includeDaily">Include daily breakdown</param>
    /// <param name="includeStatus">Include status breakdown</param>
    /// <param name="includeProviders">Include service provider breakdown</param>
    /// <param name="includeRoutes">Include route breakdown</param>
    /// <param name="includePerformance">Include performance metrics</param>
    /// <returns>Shipper order lifecycle analytics</returns>
    [HttpGet("shipper/order-lifecycle")]
    [Authorize(Roles = "Admin,Shipper,TransportCompany")]
    public async Task<ActionResult<ShipperOrderLifecycleAnalyticsDto>> GetShipperOrderLifecycleAnalytics(
        [FromQuery] Guid? shipperId = null,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] List<OrderStatus>? statusFilter = null,
        [FromQuery] List<Guid>? serviceProviderIds = null,
        [FromQuery] List<LoadType>? loadTypes = null,
        [FromQuery] List<string>? routes = null,
        [FromQuery] bool includeDaily = true,
        [FromQuery] bool includeStatus = true,
        [FromQuery] bool includeProviders = true,
        [FromQuery] bool includeRoutes = true,
        [FromQuery] bool includePerformance = true)
    {
        try
        {
            // Use provided shipperId or current user's ID
            var targetShipperId = shipperId ?? GetCurrentUserId();
            
            // Ensure shipper can only access their own data (unless admin)
            if (!IsAdmin() && targetShipperId != GetCurrentUserId())
            {
                return Forbid("Shippers can only access their own analytics");
            }

            var query = new GetShipperOrderLifecycleQuery(targetShipperId, fromDate, toDate)
            {
                StatusFilter = statusFilter,
                ServiceProviderIds = serviceProviderIds,
                LoadTypes = loadTypes,
                Routes = routes,
                IncludeDailyBreakdown = includeDaily,
                IncludeStatusBreakdown = includeStatus,
                IncludeProviderBreakdown = includeProviders,
                IncludeRouteBreakdown = includeRoutes,
                IncludePerformanceMetrics = includePerformance,
                IncludeServiceProviderComparison = true
            };

            var result = await Mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "An error occurred while retrieving shipper order lifecycle analytics", error = ex.Message });
        }
    }

    /// <summary>
    /// Get analytics summary for current user based on their role
    /// </summary>
    /// <param name="fromDate">Start date for analysis</param>
    /// <param name="toDate">End date for analysis</param>
    /// <returns>Role-specific analytics summary</returns>
    [HttpGet("summary")]
    public async Task<ActionResult<object>> GetAnalyticsSummary(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        try
        {
            var currentUserId = GetCurrentUserId();
            var userRole = GetCurrentUserRole();

            switch (userRole.ToLower())
            {
                case "broker":
                    var brokerQuery = new GetBrokerQuoteAnalyticsQuery(currentUserId, fromDate, toDate)
                    {
                        IncludeDailyBreakdown = false,
                        IncludeCarrierBreakdown = false,
                        IncludeLoadTypeBreakdown = false,
                        IncludeRouteBreakdown = false,
                        IncludeTopPerformingRoutes = false
                    };
                    var brokerResult = await Mediator.Send(brokerQuery);
                    return Ok(new
                    {
                        Role = "Broker",
                        Summary = new
                        {
                            brokerResult.TotalQuotesSubmitted,
                            brokerResult.QuotesAccepted,
                            brokerResult.AcceptanceRate,
                            brokerResult.TotalQuotedValue,
                            brokerResult.ConversionRate
                        }
                    });

                case "carrier":
                case "transportcompany":
                    var transporterQuery = new GetTransporterLoadPostingsQuery(currentUserId, fromDate, toDate)
                    {
                        IncludeDailyBreakdown = false,
                        IncludeBrokerBreakdown = false,
                        IncludeStatusBreakdown = false,
                        IncludeRouteBreakdown = false,
                        IncludeConversionAnalytics = false
                    };
                    var transporterResult = await Mediator.Send(transporterQuery);
                    return Ok(new
                    {
                        Role = "Transporter",
                        Summary = new
                        {
                            transporterResult.TotalLoadPostings,
                            transporterResult.ConvertedPostings,
                            transporterResult.ConversionRate,
                            transporterResult.TotalPostedValue,
                            transporterResult.QuoteRate
                        }
                    });

                case "shipper":
                    var shipperQuery = new GetShipperOrderLifecycleQuery(currentUserId, fromDate, toDate)
                    {
                        IncludeDailyBreakdown = false,
                        IncludeStatusBreakdown = false,
                        IncludeProviderBreakdown = false,
                        IncludeRouteBreakdown = false,
                        IncludePerformanceMetrics = false,
                        IncludeServiceProviderComparison = false
                    };
                    var shipperResult = await Mediator.Send(shipperQuery);
                    return Ok(new
                    {
                        Role = "Shipper",
                        Summary = new
                        {
                            shipperResult.TotalOrders,
                            shipperResult.CompletedOrders,
                            shipperResult.CompletionRate,
                            shipperResult.TotalOrderValue,
                            shipperResult.AverageOrderValue
                        }
                    });

                default:
                    return BadRequest(new { message = "Analytics not available for this user role" });
            }
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "An error occurred while retrieving analytics summary", error = ex.Message });
        }
    }
}
