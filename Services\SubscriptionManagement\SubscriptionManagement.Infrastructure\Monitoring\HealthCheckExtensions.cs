using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using SubscriptionManagement.Infrastructure.Persistence;
using SubscriptionManagement.Application.Interfaces;

namespace SubscriptionManagement.Infrastructure.Monitoring
{
    public static class HealthCheckExtensions
    {
        public static IServiceCollection AddSubscriptionHealthChecks(this IServiceCollection services)
        {
            services.AddHealthChecks()
                .AddDbContextCheck<SubscriptionDbContext>("database")
                .AddCheck<CommunicationServiceHealthCheck>("communication_service")
                .AddCheck<NotificationQueueHealthCheck>("notification_queue")
                .AddCheck<PaymentProofStorageHealthCheck>("payment_proof_storage")
                .AddCheck<CacheHealthCheck>("cache");

            return services;
        }
    }

    public class CommunicationServiceHealthCheck : IHealthCheck
    {
        private readonly ICommunicationService _communicationService;

        public CommunicationServiceHealthCheck(ICommunicationService communicationService)
        {
            _communicationService = communicationService;
        }

        public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
        {
            try
            {
                // Try to get status of a dummy notification to test connectivity
                var testResult = await _communicationService.GetNotificationStatusAsync("health-check-test", cancellationToken);
                
                return HealthCheckResult.Healthy("Communication service is responsive");
            }
            catch (Exception ex)
            {
                return HealthCheckResult.Unhealthy("Communication service is not responsive", ex);
            }
        }
    }

    public class NotificationQueueHealthCheck : IHealthCheck
    {
        private readonly INotificationTrackingService _trackingService;

        public NotificationQueueHealthCheck(INotificationTrackingService trackingService)
        {
            _trackingService = trackingService;
        }

        public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
        {
            try
            {
                // Check for stuck notifications
                var failedNotifications = await _trackingService.GetNotificationsForRetryAsync(cancellationToken);
                var overdueNotifications = await _trackingService.GetScheduledNotificationsReadyToSendAsync(cancellationToken);

                var data = new Dictionary<string, object>
                {
                    ["failed_notifications"] = failedNotifications.Count,
                    ["overdue_notifications"] = overdueNotifications.Count
                };

                if (failedNotifications.Count > 100 || overdueNotifications.Count > 50)
                {
                    return HealthCheckResult.Degraded("High number of failed or overdue notifications", data: data);
                }

                return HealthCheckResult.Healthy("Notification queue is healthy", data);
            }
            catch (Exception ex)
            {
                return HealthCheckResult.Unhealthy("Unable to check notification queue health", ex);
            }
        }
    }

    public class PaymentProofStorageHealthCheck : IHealthCheck
    {
        private readonly IPaymentProofFileService _fileService;

        public PaymentProofStorageHealthCheck(IPaymentProofFileService fileService)
        {
            _fileService = fileService;
        }

        public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
        {
            try
            {
                // Test file storage connectivity by checking if we can access the storage
                // This is a simplified check - in reality you might want to test actual file operations
                await Task.Delay(10, cancellationToken); // Simulate storage check
                
                return HealthCheckResult.Healthy("Payment proof storage is accessible");
            }
            catch (Exception ex)
            {
                return HealthCheckResult.Unhealthy("Payment proof storage is not accessible", ex);
            }
        }
    }

    public class CacheHealthCheck : IHealthCheck
    {
        public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
        {
            try
            {
                // Test cache connectivity
                // This would typically test Redis or other cache providers
                await Task.Delay(10, cancellationToken); // Simulate cache check
                
                return HealthCheckResult.Healthy("Cache is responsive");
            }
            catch (Exception ex)
            {
                return HealthCheckResult.Unhealthy("Cache is not responsive", ex);
            }
        }
    }
}
