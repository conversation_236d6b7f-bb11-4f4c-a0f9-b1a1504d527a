using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Enums;
using AnalyticsBIService.Domain.Repositories;
using AnalyticsBIService.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Shared.Infrastructure.Repositories;
using Shared.Infrastructure.Caching;
using Shared.Infrastructure.Monitoring;

namespace AnalyticsBIService.Infrastructure.Repositories;

/// <summary>
/// Repository implementation for Threshold entity
/// </summary>
public class ThresholdRepository : OptimizedRepositoryBase<Threshold, Guid>, IThresholdRepository
{
    public ThresholdRepository(
        AnalyticsBIDbContext context,
        ICachingService cachingService,
        IPerformanceMetrics performanceMetrics)
        : base(context, cachingService, performanceMetrics)
    {
    }

    /// <summary>
    /// Get thresholds by metric type
    /// </summary>
    public async Task<IEnumerable<Threshold>> GetByMetricTypeAsync(string metricType, CancellationToken cancellationToken = default)
    {
        return await DbSet
            .Where(t => t.MetricType == metricType)
            .OrderBy(t => t.Name)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get thresholds by entity type
    /// </summary>
    public async Task<IEnumerable<Threshold>> GetByEntityTypeAsync(string entityType, CancellationToken cancellationToken = default)
    {
        return await DbSet
            .Where(t => t.EntityType == entityType)
            .OrderBy(t => t.Name)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get thresholds by user role
    /// </summary>
    public async Task<IEnumerable<Threshold>> GetByUserRoleAsync(UserRole userRole, CancellationToken cancellationToken = default)
    {
        return await DbSet
            .Where(t => t.UserRole == userRole)
            .OrderBy(t => t.Name)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get enabled thresholds
    /// </summary>
    public async Task<IEnumerable<Threshold>> GetEnabledAsync(CancellationToken cancellationToken = default)
    {
        return await DbSet
            .Where(t => t.IsEnabled)
            .OrderBy(t => t.Name)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get thresholds for monitoring
    /// </summary>
    public async Task<IEnumerable<Threshold>> GetForMonitoringAsync(string metricType, string entityType, CancellationToken cancellationToken = default)
    {
        return await DbSet
            .Where(t => t.IsEnabled && t.MetricType == metricType && t.EntityType == entityType)
            .OrderBy(t => t.Name)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get thresholds by notification recipient
    /// </summary>
    public async Task<IEnumerable<Threshold>> GetByNotificationRecipientAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await DbSet
            .Where(t => t.NotificationRecipients.Contains(userId))
            .OrderBy(t => t.Name)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get thresholds by user and metric name
    /// </summary>
    public async Task<List<Threshold>> GetByUserAndMetricAsync(Guid userId, string metricName, CancellationToken cancellationToken = default)
    {
        return await DbSet
            .Where(t => t.UserId == userId && t.MetricName == metricName)
            .OrderBy(t => t.Name)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get thresholds by user
    /// </summary>
    public async Task<List<Threshold>> GetByUserAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await DbSet
            .Where(t => t.UserId == userId)
            .OrderBy(t => t.Name)
            .ToListAsync(cancellationToken);
    }
}
