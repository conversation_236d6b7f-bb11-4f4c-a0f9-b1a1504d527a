using NBomber.CSharp;
using NBomber.Http.CSharp;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Text.Json;

namespace CommunicationNotification.Tests.Performance;

/// <summary>
/// Comprehensive performance testing framework for the Communication & Notification Service
/// </summary>
public class PerformanceTestFramework
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<PerformanceTestFramework> _logger;
    private readonly HttpClient _httpClient;
    private readonly ConcurrentDictionary<string, PerformanceMetrics> _metrics;
    private readonly PerformanceTestConfiguration _testConfig;

    public PerformanceTestFramework(
        IConfiguration configuration,
        ILogger<PerformanceTestFramework> logger,
        HttpClient httpClient)
    {
        _configuration = configuration;
        _logger = logger;
        _httpClient = httpClient;
        _metrics = new ConcurrentDictionary<string, PerformanceMetrics>();
        _testConfig = configuration.GetSection("PerformanceTest").Get<PerformanceTestConfiguration>()
                     ?? new PerformanceTestConfiguration();
    }

    /// <summary>
    /// Run comprehensive performance test suite
    /// </summary>
    public async Task<PerformanceTestResults> RunPerformanceTestSuiteAsync()
    {
        _logger.LogInformation("Starting comprehensive performance test suite");

        var results = new PerformanceTestResults
        {
            StartTime = DateTime.UtcNow,
            TestConfiguration = _testConfig
        };

        try
        {
            // Run load tests
            results.LoadTestResults = await RunLoadTestsAsync();

            // Run stress tests
            results.StressTestResults = await RunStressTestsAsync();

            // Run endurance tests
            results.EnduranceTestResults = await RunEnduranceTestsAsync();

            // Run spike tests
            results.SpikeTestResults = await RunSpikeTestsAsync();

            // Run volume tests
            results.VolumeTestResults = await RunVolumeTestsAsync();

            // Run concurrent user tests
            results.ConcurrentUserTestResults = await RunConcurrentUserTestsAsync();

            // Generate performance report
            results.PerformanceReport = GeneratePerformanceReport(results);

            results.EndTime = DateTime.UtcNow;
            results.TotalDuration = results.EndTime - results.StartTime;
            results.Success = true;

            _logger.LogInformation("Performance test suite completed successfully in {Duration}", results.TotalDuration);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Performance test suite failed");
            results.Success = false;
            results.ErrorMessage = ex.Message;
            results.EndTime = DateTime.UtcNow;
            results.TotalDuration = results.EndTime - results.StartTime;
        }

        return results;
    }

    /// <summary>
    /// Run load tests to verify system performance under expected load
    /// </summary>
    public async Task<LoadTestResults> RunLoadTestsAsync()
    {
        _logger.LogInformation("Running load tests");

        var scenarios = new[]
        {
            CreateNotificationSendScenario("load_test_send", _testConfig.LoadTest.RequestsPerSecond, _testConfig.LoadTest.Duration),
            CreateNotificationStatusScenario("load_test_status", _testConfig.LoadTest.RequestsPerSecond / 2, _testConfig.LoadTest.Duration),
            CreateBulkNotificationScenario("load_test_bulk", _testConfig.LoadTest.RequestsPerSecond / 4, _testConfig.LoadTest.Duration)
        };

        var stats = NBomberRunner
            .RegisterScenarios(scenarios)
            .WithReportFolder("performance-reports/load-tests")
            .WithReportFormats(ReportFormat.Html, ReportFormat.Csv)
            .Run();

        return new LoadTestResults
        {
            TestName = "Load Test",
            Duration = _testConfig.LoadTest.Duration,
            RequestsPerSecond = _testConfig.LoadTest.RequestsPerSecond,
            TotalRequests = stats.AllOkCount + stats.AllFailCount,
            SuccessfulRequests = stats.AllOkCount,
            FailedRequests = stats.AllFailCount,
            AverageResponseTime = stats.ScenarioStats.Average(s => s.Ok.Mean),
            MaxResponseTime = stats.ScenarioStats.Max(s => s.Ok.Max),
            MinResponseTime = stats.ScenarioStats.Min(s => s.Ok.Min),
            ThroughputPerSecond = stats.AllOkCount / _testConfig.LoadTest.Duration.TotalSeconds,
            ErrorRate = (double)stats.AllFailCount / (stats.AllOkCount + stats.AllFailCount) * 100,
            Success = stats.AllFailCount == 0 || (double)stats.AllFailCount / (stats.AllOkCount + stats.AllFailCount) < 0.01 // Less than 1% error rate
        };
    }

    /// <summary>
    /// Run stress tests to find system breaking point
    /// </summary>
    public async Task<StressTestResults> RunStressTestsAsync()
    {
        _logger.LogInformation("Running stress tests");

        var scenario = CreateNotificationSendScenario(
            "stress_test",
            _testConfig.StressTest.MaxRequestsPerSecond,
            _testConfig.StressTest.Duration);

        var stats = NBomberRunner
            .RegisterScenarios(scenario)
            .WithReportFolder("performance-reports/stress-tests")
            .WithReportFormats(ReportFormat.Html, ReportFormat.Csv)
            .Run();

        return new StressTestResults
        {
            TestName = "Stress Test",
            Duration = _testConfig.StressTest.Duration,
            MaxRequestsPerSecond = _testConfig.StressTest.MaxRequestsPerSecond,
            TotalRequests = stats.AllOkCount + stats.AllFailCount,
            SuccessfulRequests = stats.AllOkCount,
            FailedRequests = stats.AllFailCount,
            AverageResponseTime = stats.ScenarioStats.Average(s => s.Ok.Mean),
            MaxResponseTime = stats.ScenarioStats.Max(s => s.Ok.Max),
            BreakingPoint = CalculateBreakingPoint(stats),
            ResourceUtilization = await MeasureResourceUtilizationAsync(),
            Success = stats.AllFailCount < stats.AllOkCount * 0.1 // Less than 10% error rate under stress
        };
    }

    /// <summary>
    /// Run endurance tests to verify system stability over time
    /// </summary>
    public async Task<EnduranceTestResults> RunEnduranceTestsAsync()
    {
        _logger.LogInformation("Running endurance tests");

        var scenario = CreateNotificationSendScenario(
            "endurance_test",
            _testConfig.EnduranceTest.RequestsPerSecond,
            _testConfig.EnduranceTest.Duration);

        var memoryBefore = GC.GetTotalMemory(true);
        var startTime = DateTime.UtcNow;

        var stats = NBomberRunner
            .RegisterScenarios(scenario)
            .WithReportFolder("performance-reports/endurance-tests")
            .WithReportFormats(ReportFormat.Html, ReportFormat.Csv)
            .Run();

        var memoryAfter = GC.GetTotalMemory(true);
        var endTime = DateTime.UtcNow;

        return new EnduranceTestResults
        {
            TestName = "Endurance Test",
            Duration = _testConfig.EnduranceTest.Duration,
            RequestsPerSecond = _testConfig.EnduranceTest.RequestsPerSecond,
            TotalRequests = stats.AllOkCount + stats.AllFailCount,
            SuccessfulRequests = stats.AllOkCount,
            FailedRequests = stats.AllFailCount,
            AverageResponseTime = stats.ScenarioStats.Average(s => s.Ok.Mean),
            MemoryLeakDetected = (memoryAfter - memoryBefore) > (memoryBefore * 0.1), // More than 10% memory increase
            MemoryUsageBefore = memoryBefore,
            MemoryUsageAfter = memoryAfter,
            PerformanceDegradation = CalculatePerformanceDegradation(stats),
            Success = stats.AllFailCount == 0 && (memoryAfter - memoryBefore) <= (memoryBefore * 0.1)
        };
    }

    /// <summary>
    /// Run spike tests to verify system behavior under sudden load increases
    /// </summary>
    public async Task<SpikeTestResults> RunSpikeTestsAsync()
    {
        _logger.LogInformation("Running spike tests");

        var scenario = Scenario.Create("spike_test", async context =>
        {
            var request = new
            {
                UserId = Guid.NewGuid(),
                Content = $"Spike test notification {context.InvocationNumber}",
                MessageType = "SystemNotification",
                Priority = "Normal",
                PreferredChannel = "Push"
            };

            var response = await _httpClient.PostAsJsonAsync("/api/notifications/send", request);
            return response.IsSuccessStatusCode ? Response.Ok() : Response.Fail();
        })
        .WithLoadSimulations(
            Simulation.InjectPerSec(rate: 10, during: TimeSpan.FromSeconds(30)),  // Normal load
            Simulation.InjectPerSec(rate: 100, during: TimeSpan.FromSeconds(10)), // Spike
            Simulation.InjectPerSec(rate: 10, during: TimeSpan.FromSeconds(30))   // Back to normal
        );

        var stats = NBomberRunner
            .RegisterScenarios(scenario)
            .WithReportFolder("performance-reports/spike-tests")
            .WithReportFormats(ReportFormat.Html, ReportFormat.Csv)
            .Run();

        return new SpikeTestResults
        {
            TestName = "Spike Test",
            NormalLoad = 10,
            SpikeLoad = 100,
            SpikeDuration = TimeSpan.FromSeconds(10),
            TotalRequests = stats.AllOkCount + stats.AllFailCount,
            SuccessfulRequests = stats.AllOkCount,
            FailedRequests = stats.AllFailCount,
            RecoveryTime = CalculateRecoveryTime(stats),
            SystemStability = CalculateSystemStability(stats),
            Success = stats.AllFailCount < stats.AllOkCount * 0.05 // Less than 5% error rate during spike
        };
    }

    /// <summary>
    /// Run volume tests to verify system behavior with large data sets
    /// </summary>
    public async Task<VolumeTestResults> RunVolumeTestsAsync()
    {
        _logger.LogInformation("Running volume tests");

        var largePayloadScenario = Scenario.Create("volume_test_large_payload", async context =>
        {
            var request = new
            {
                UserId = Guid.NewGuid(),
                Content = GenerateLargeContent(5000), // 5KB content
                MessageType = "SystemNotification",
                Priority = "Normal",
                PreferredChannel = "Email",
                Attachments = GenerateAttachments(3) // 3 attachments
            };

            var response = await _httpClient.PostAsJsonAsync("/api/notifications/send", request);
            return response.IsSuccessStatusCode ? Response.Ok() : Response.Fail();
        })
        .WithLoadSimulations(
            Simulation.InjectPerSec(rate: 5, during: TimeSpan.FromMinutes(2))
        );

        var bulkNotificationScenario = Scenario.Create("volume_test_bulk", async context =>
        {
            var request = new
            {
                UserIds = Enumerable.Range(0, 1000).Select(_ => Guid.NewGuid()).ToArray(), // 1000 users
                Content = "Bulk notification test",
                MessageType = "SystemNotification",
                Priority = "Normal"
            };

            var response = await _httpClient.PostAsJsonAsync("/api/notifications/send-bulk", request);
            return response.IsSuccessStatusCode ? Response.Ok() : Response.Fail();
        })
        .WithLoadSimulations(
            Simulation.InjectPerSec(rate: 1, during: TimeSpan.FromMinutes(1))
        );

        var stats = NBomberRunner
            .RegisterScenarios(largePayloadScenario, bulkNotificationScenario)
            .WithReportFolder("performance-reports/volume-tests")
            .WithReportFormats(ReportFormat.Html, ReportFormat.Csv)
            .Run();

        return new VolumeTestResults
        {
            TestName = "Volume Test",
            LargePayloadSize = 5000,
            BulkNotificationSize = 1000,
            TotalRequests = stats.AllOkCount + stats.AllFailCount,
            SuccessfulRequests = stats.AllOkCount,
            FailedRequests = stats.AllFailCount,
            AverageResponseTime = stats.ScenarioStats.Average(s => s.Ok.Mean),
            DataThroughput = CalculateDataThroughput(stats),
            Success = stats.AllFailCount == 0
        };
    }

    /// <summary>
    /// Run concurrent user tests to simulate realistic user behavior
    /// </summary>
    public async Task<ConcurrentUserTestResults> RunConcurrentUserTestsAsync()
    {
        _logger.LogInformation("Running concurrent user tests");

        var userJourneyScenario = Scenario.Create("concurrent_user_journey", async context =>
        {
            var userId = Guid.NewGuid();

            // Step 1: Send notification
            var sendRequest = new
            {
                UserId = userId,
                Content = $"User journey test {context.InvocationNumber}",
                MessageType = "SystemNotification",
                Priority = "Normal"
            };

            var sendResponse = await _httpClient.PostAsJsonAsync("/api/notifications/send", sendRequest);
            if (!sendResponse.IsSuccessStatusCode)
                return Response.Fail();

            // Step 2: Check status
            await Task.Delay(100); // Simulate user wait time
            var statusResponse = await _httpClient.GetAsync($"/api/notifications/{userId}/status");
            if (!statusResponse.IsSuccessStatusCode)
                return Response.Fail();

            // Step 3: Get notification history
            await Task.Delay(50);
            var historyResponse = await _httpClient.GetAsync($"/api/notifications/{userId}/history");
            if (!historyResponse.IsSuccessStatusCode)
                return Response.Fail();

            return Response.Ok();
        })
        .WithLoadSimulations(
            Simulation.InjectPerSec(rate: _testConfig.ConcurrentUserTest.UsersPerSecond,
                                  during: _testConfig.ConcurrentUserTest.Duration)
        );

        var stats = NBomberRunner
            .RegisterScenarios(userJourneyScenario)
            .WithReportFolder("performance-reports/concurrent-user-tests")
            .WithReportFormats(ReportFormat.Html, ReportFormat.Csv)
            .Run();

        return new ConcurrentUserTestResults
        {
            TestName = "Concurrent User Test",
            ConcurrentUsers = _testConfig.ConcurrentUserTest.UsersPerSecond,
            Duration = _testConfig.ConcurrentUserTest.Duration,
            TotalUserJourneys = stats.AllOkCount + stats.AllFailCount,
            SuccessfulJourneys = stats.AllOkCount,
            FailedJourneys = stats.AllFailCount,
            AverageJourneyTime = stats.ScenarioStats.Average(s => s.Ok.Mean),
            UserExperienceScore = CalculateUserExperienceScore(stats),
            Success = stats.AllFailCount < stats.AllOkCount * 0.02 // Less than 2% failure rate
        };
    }

    // Helper methods for creating scenarios
    private Scenario CreateNotificationSendScenario(string name, int requestsPerSecond, TimeSpan duration)
    {
        return Scenario.Create(name, async context =>
        {
            var request = new
            {
                UserId = Guid.NewGuid(),
                Content = $"Performance test notification {context.InvocationNumber}",
                MessageType = "SystemNotification",
                Priority = "Normal",
                PreferredChannel = "Push"
            };

            var response = await _httpClient.PostAsJsonAsync("/api/notifications/send", request);
            return response.IsSuccessStatusCode ? Response.Ok() : Response.Fail();
        })
        .WithLoadSimulations(
            Simulation.InjectPerSec(rate: requestsPerSecond, during: duration)
        );
    }

    private Scenario CreateNotificationStatusScenario(string name, int requestsPerSecond, TimeSpan duration)
    {
        return Scenario.Create(name, async context =>
        {
            var userId = Guid.NewGuid();
            var response = await _httpClient.GetAsync($"/api/notifications/{userId}/status");
            return response.IsSuccessStatusCode ? Response.Ok() : Response.Fail();
        })
        .WithLoadSimulations(
            Simulation.InjectPerSec(rate: requestsPerSecond, during: duration)
        );
    }

    private Scenario CreateBulkNotificationScenario(string name, int requestsPerSecond, TimeSpan duration)
    {
        return Scenario.Create(name, async context =>
        {
            var request = new
            {
                UserIds = Enumerable.Range(0, 10).Select(_ => Guid.NewGuid()).ToArray(),
                Content = $"Bulk notification test {context.InvocationNumber}",
                MessageType = "SystemNotification",
                Priority = "Normal"
            };

            var response = await _httpClient.PostAsJsonAsync("/api/notifications/send-bulk", request);
            return response.IsSuccessStatusCode ? Response.Ok() : Response.Fail();
        })
        .WithLoadSimulations(
            Simulation.InjectPerSec(rate: requestsPerSecond, during: duration)
        );
    }

    // Helper methods for calculations and measurements
    private string GenerateLargeContent(int sizeInBytes)
    {
        var random = new Random();
        var chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789 ";
        return new string(Enumerable.Repeat(chars, sizeInBytes)
            .Select(s => s[random.Next(s.Length)]).ToArray());
    }

    private object[] GenerateAttachments(int count)
    {
        return Enumerable.Range(0, count)
            .Select(i => new { Name = $"attachment_{i}.txt", Size = 1024, Type = "text/plain" })
            .ToArray();
    }

    private double CalculateBreakingPoint(NBomberStats stats)
    {
        // Calculate the point where error rate exceeds 5%
        var errorRate = (double)stats.AllFailCount / (stats.AllOkCount + stats.AllFailCount);
        return errorRate > 0.05 ? stats.AllRequestCount / stats.ScenarioStats.First().Duration.TotalSeconds : 0;
    }

    private async Task<ResourceUtilization> MeasureResourceUtilizationAsync()
    {
        var process = Process.GetCurrentProcess();
        return new ResourceUtilization
        {
            CpuUsagePercent = await GetCpuUsageAsync(),
            MemoryUsageMB = process.WorkingSet64 / 1024 / 1024,
            ThreadCount = process.Threads.Count,
            HandleCount = process.HandleCount
        };
    }

    private async Task<double> GetCpuUsageAsync()
    {
        var startTime = DateTime.UtcNow;
        var startCpuUsage = Process.GetCurrentProcess().TotalProcessorTime;
        await Task.Delay(500);
        var endTime = DateTime.UtcNow;
        var endCpuUsage = Process.GetCurrentProcess().TotalProcessorTime;

        var cpuUsedMs = (endCpuUsage - startCpuUsage).TotalMilliseconds;
        var totalMsPassed = (endTime - startTime).TotalMilliseconds;
        var cpuUsageTotal = cpuUsedMs / (Environment.ProcessorCount * totalMsPassed);

        return cpuUsageTotal * 100;
    }

    private double CalculatePerformanceDegradation(NBomberStats stats)
    {
        // Calculate performance degradation over time
        // This would require time-series data in a real implementation
        return 0; // Placeholder
    }

    private TimeSpan CalculateRecoveryTime(NBomberStats stats)
    {
        // Calculate time to recover from spike
        // This would require detailed timing analysis in a real implementation
        return TimeSpan.FromSeconds(5); // Placeholder
    }

    private double CalculateSystemStability(NBomberStats stats)
    {
        // Calculate system stability score (0-100)
        var errorRate = (double)stats.AllFailCount / (stats.AllOkCount + stats.AllFailCount);
        return Math.Max(0, 100 - (errorRate * 100));
    }

    private double CalculateDataThroughput(NBomberStats stats)
    {
        // Calculate data throughput in MB/s
        var totalDataMB = (stats.AllOkCount * 5) / 1024.0; // Assuming 5KB average payload
        var durationSeconds = stats.ScenarioStats.First().Duration.TotalSeconds;
        return totalDataMB / durationSeconds;
    }

    private double CalculateUserExperienceScore(NBomberStats stats)
    {
        // Calculate user experience score based on response times and success rate
        var successRate = (double)stats.AllOkCount / (stats.AllOkCount + stats.AllFailCount);
        var avgResponseTime = stats.ScenarioStats.Average(s => s.Ok.Mean);

        var responseTimeScore = Math.Max(0, 100 - (avgResponseTime / 10)); // Penalty for slow responses
        var reliabilityScore = successRate * 100;

        return (responseTimeScore + reliabilityScore) / 2;
    }

    private PerformanceReport GeneratePerformanceReport(PerformanceTestResults results)
    {
        return new PerformanceReport
        {
            GeneratedAt = DateTime.UtcNow,
            TestDuration = results.TotalDuration,
            OverallScore = CalculateOverallScore(results),
            Summary = GenerateTestSummary(results),
            Recommendations = GenerateRecommendations(results),
            Benchmarks = GenerateBenchmarks(results),
            TrendAnalysis = GenerateTrendAnalysis(results)
        };
    }

    private double CalculateOverallScore(PerformanceTestResults results)
    {
        var scores = new List<double>();

        if (results.LoadTestResults?.Success == true)
            scores.Add(90);
        else if (results.LoadTestResults != null)
            scores.Add(Math.Max(0, 90 - results.LoadTestResults.ErrorRate));

        if (results.StressTestResults?.Success == true)
            scores.Add(85);
        else if (results.StressTestResults != null)
            scores.Add(Math.Max(0, 85 - (results.StressTestResults.FailedRequests / (double)results.StressTestResults.TotalRequests * 100)));

        if (results.EnduranceTestResults?.Success == true)
            scores.Add(80);

        if (results.SpikeTestResults?.Success == true)
            scores.Add(75);

        if (results.VolumeTestResults?.Success == true)
            scores.Add(70);

        if (results.ConcurrentUserTestResults?.Success == true)
            scores.Add(results.ConcurrentUserTestResults.UserExperienceScore);

        return scores.Any() ? scores.Average() : 0;
    }

    private string GenerateTestSummary(PerformanceTestResults results)
    {
        var summary = $"Performance test suite completed in {results.TotalDuration:hh\\:mm\\:ss}.\n";

        if (results.LoadTestResults != null)
            summary += $"Load Test: {results.LoadTestResults.SuccessfulRequests}/{results.LoadTestResults.TotalRequests} requests successful.\n";

        if (results.StressTestResults != null)
            summary += $"Stress Test: System handled {results.StressTestResults.MaxRequestsPerSecond} req/s with {results.StressTestResults.FailedRequests} failures.\n";

        if (results.EnduranceTestResults != null)
            summary += $"Endurance Test: {(results.EnduranceTestResults.MemoryLeakDetected ? "Memory leak detected" : "No memory leaks")}.\n";

        return summary;
    }

    private List<string> GenerateRecommendations(PerformanceTestResults results)
    {
        var recommendations = new List<string>();

        if (results.LoadTestResults?.ErrorRate > 1)
            recommendations.Add("Consider optimizing database queries and adding connection pooling to reduce error rate under load.");

        if (results.StressTestResults?.AverageResponseTime > 1000)
            recommendations.Add("Response times are high under stress. Consider implementing caching and load balancing.");

        if (results.EnduranceTestResults?.MemoryLeakDetected == true)
            recommendations.Add("Memory leak detected during endurance test. Review object disposal and garbage collection.");

        if (results.VolumeTestResults?.AverageResponseTime > 2000)
            recommendations.Add("Large payload processing is slow. Consider implementing streaming or chunking for large data.");

        if (results.ConcurrentUserTestResults?.UserExperienceScore < 80)
            recommendations.Add("User experience score is below optimal. Focus on reducing response times and improving reliability.");

        return recommendations;
    }

    private Dictionary<string, double> GenerateBenchmarks(PerformanceTestResults results)
    {
        var benchmarks = new Dictionary<string, double>();

        if (results.LoadTestResults != null)
        {
            benchmarks["Load Test Throughput (req/s)"] = results.LoadTestResults.ThroughputPerSecond;
            benchmarks["Load Test Error Rate (%)"] = results.LoadTestResults.ErrorRate;
            benchmarks["Load Test Avg Response Time (ms)"] = results.LoadTestResults.AverageResponseTime;
        }

        if (results.StressTestResults != null)
        {
            benchmarks["Stress Test Max Throughput (req/s)"] = results.StressTestResults.MaxRequestsPerSecond;
            benchmarks["Stress Test Breaking Point (req/s)"] = results.StressTestResults.BreakingPoint;
        }

        if (results.ConcurrentUserTestResults != null)
        {
            benchmarks["User Experience Score"] = results.ConcurrentUserTestResults.UserExperienceScore;
            benchmarks["Concurrent Users Supported"] = results.ConcurrentUserTestResults.ConcurrentUsers;
        }

        return benchmarks;
    }

    private Dictionary<string, object> GenerateTrendAnalysis(PerformanceTestResults results)
    {
        // This would compare with historical test results in a real implementation
        return new Dictionary<string, object>
        {
            ["trend"] = "stable",
            ["comparison_period"] = "last_30_days",
            ["performance_change"] = "+2.5%",
            ["reliability_change"] = "+1.2%"
        };
    }
}
