using DataStorage.Domain.Enums;

namespace DataStorage.Domain.ValueObjects;

public class FileProcessingResult
{
    public bool IsSuccessful { get; private set; }
    public string? ErrorMessage { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }
    public List<string> ExtractedText { get; private set; }
    public List<string> ExtractedImages { get; private set; }
    public FileComplexity Complexity { get; private set; }
    public TimeSpan ProcessingTime { get; private set; }
    public long ProcessedBytes { get; private set; }

    private FileProcessingResult()
    {
        Metadata = new Dictionary<string, object>();
        ExtractedText = new List<string>();
        ExtractedImages = new List<string>();
    }

    public static FileProcessingResult Success(
        Dictionary<string, object> metadata,
        List<string> extractedText,
        List<string> extractedImages,
        FileComplexity complexity,
        TimeSpan processingTime,
        long processedBytes)
    {
        return new FileProcessingResult
        {
            IsSuccessful = true,
            Metadata = metadata ?? new Dictionary<string, object>(),
            ExtractedText = extractedText ?? new List<string>(),
            ExtractedImages = extractedImages ?? new List<string>(),
            Complexity = complexity,
            ProcessingTime = processingTime,
            ProcessedBytes = processedBytes
        };
    }

    public static FileProcessingResult Failure(string errorMessage)
    {
        return new FileProcessingResult
        {
            IsSuccessful = false,
            ErrorMessage = errorMessage,
            Metadata = new Dictionary<string, object>(),
            ExtractedText = new List<string>(),
            ExtractedImages = new List<string>(),
            Complexity = FileComplexity.Unknown,
            ProcessingTime = TimeSpan.Zero,
            ProcessedBytes = 0
        };
    }
}

public class DocumentStructure
{
    public string Title { get; private set; }
    public List<DocumentSection> Sections { get; private set; }
    public List<DocumentTable> Tables { get; private set; }
    public List<DocumentImage> Images { get; private set; }
    public List<DocumentChart> Charts { get; private set; }
    public DocumentMetadata Metadata { get; private set; }

    public DocumentStructure(
        string title,
        List<DocumentSection> sections,
        List<DocumentTable> tables,
        List<DocumentImage> images,
        List<DocumentChart> charts,
        DocumentMetadata metadata)
    {
        Title = title ?? string.Empty;
        Sections = sections ?? new List<DocumentSection>();
        Tables = tables ?? new List<DocumentTable>();
        Images = images ?? new List<DocumentImage>();
        Charts = charts ?? new List<DocumentChart>();
        Metadata = metadata ?? new DocumentMetadata();
    }
}

public class DocumentSection
{
    public string Title { get; private set; }
    public int Level { get; private set; }
    public string Content { get; private set; }
    public int PageNumber { get; private set; }
    public List<DocumentSection> SubSections { get; private set; }

    public DocumentSection(string title, int level, string content, int pageNumber)
    {
        Title = title ?? string.Empty;
        Level = level;
        Content = content ?? string.Empty;
        PageNumber = pageNumber;
        SubSections = new List<DocumentSection>();
    }

    public void AddSubSection(DocumentSection subSection)
    {
        SubSections.Add(subSection);
    }
}

public class DocumentTable
{
    public string Title { get; private set; }
    public int RowCount { get; private set; }
    public int ColumnCount { get; private set; }
    public List<string> Headers { get; private set; }
    public List<List<string>> Rows { get; private set; }
    public int PageNumber { get; private set; }

    public DocumentTable(string title, List<string> headers, List<List<string>> rows, int pageNumber)
    {
        Title = title ?? string.Empty;
        Headers = headers ?? new List<string>();
        Rows = rows ?? new List<List<string>>();
        RowCount = Rows.Count;
        ColumnCount = Headers.Count;
        PageNumber = pageNumber;
    }
}

public class DocumentImage
{
    public string Title { get; private set; }
    public string Description { get; private set; }
    public int Width { get; private set; }
    public int Height { get; private set; }
    public string Format { get; private set; }
    public long SizeBytes { get; private set; }
    public int PageNumber { get; private set; }
    public string? ExtractedPath { get; private set; }

    public DocumentImage(
        string title,
        string description,
        int width,
        int height,
        string format,
        long sizeBytes,
        int pageNumber,
        string? extractedPath = null)
    {
        Title = title ?? string.Empty;
        Description = description ?? string.Empty;
        Width = width;
        Height = height;
        Format = format ?? string.Empty;
        SizeBytes = sizeBytes;
        PageNumber = pageNumber;
        ExtractedPath = extractedPath;
    }
}

public class DocumentChart
{
    public string Title { get; private set; }
    public string ChartType { get; private set; }
    public Dictionary<string, object> Data { get; private set; }
    public int PageNumber { get; private set; }

    public DocumentChart(string title, string chartType, Dictionary<string, object> data, int pageNumber)
    {
        Title = title ?? string.Empty;
        ChartType = chartType ?? string.Empty;
        Data = data ?? new Dictionary<string, object>();
        PageNumber = pageNumber;
    }
}

public class DocumentMetadata
{
    public string Author { get; private set; }
    public DateTime CreatedDate { get; private set; }
    public DateTime ModifiedDate { get; private set; }
    public string Subject { get; private set; }
    public string Keywords { get; private set; }
    public string Application { get; private set; }
    public string Version { get; private set; }
    public int PageCount { get; private set; }
    public int WordCount { get; private set; }
    public Dictionary<string, string> CustomProperties { get; private set; }

    public DocumentMetadata(
        string author = "",
        DateTime createdDate = default,
        DateTime modifiedDate = default,
        string subject = "",
        string keywords = "",
        string application = "",
        string version = "",
        int pageCount = 0,
        int wordCount = 0,
        Dictionary<string, string>? customProperties = null)
    {
        Author = author;
        CreatedDate = createdDate;
        ModifiedDate = modifiedDate;
        Subject = subject;
        Keywords = keywords;
        Application = application;
        Version = version;
        PageCount = pageCount;
        WordCount = wordCount;
        CustomProperties = customProperties ?? new Dictionary<string, string>();
    }
}
