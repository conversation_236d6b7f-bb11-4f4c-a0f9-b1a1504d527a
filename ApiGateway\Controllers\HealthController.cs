using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using System.Net;
using System.Diagnostics;

namespace ApiGateway.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class HealthController : ControllerBase
    {
        private readonly HealthCheckService _healthCheckService;
        private readonly ILogger<HealthController> _logger;
        private readonly IConfiguration _configuration;

        public HealthController(
            HealthCheckService healthCheckService,
            ILogger<HealthController> logger,
            IConfiguration configuration)
        {
            _healthCheckService = healthCheckService;
            _logger = logger;
            _configuration = configuration;
        }

        /// <summary>
        /// Get overall health status of the API Gateway
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> Get()
        {
            try
            {
                var healthReport = await _healthCheckService.CheckHealthAsync();

                var response = new
                {
                    Status = healthReport.Status.ToString(),
                    TotalDuration = healthReport.TotalDuration.TotalMilliseconds,
                    Checks = healthReport.Entries.Select(entry => new
                    {
                        Name = entry.Key,
                        Status = entry.Value.Status.ToString(),
                        Duration = entry.Value.Duration.TotalMilliseconds,
                        Description = entry.Value.Description,
                        Data = entry.Value.Data
                    }),
                    Timestamp = DateTime.UtcNow
                };

                var statusCode = healthReport.Status == HealthStatus.Healthy
                    ? HttpStatusCode.OK
                    : HttpStatusCode.ServiceUnavailable;

                return StatusCode((int)statusCode, response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking health status");
                return StatusCode(500, new { Status = "Error", Message = "Health check failed" });
            }
        }

        /// <summary>
        /// Get health status of all downstream services
        /// </summary>
        [HttpGet("services")]
        public async Task<IActionResult> GetServicesHealth()
        {
            try
            {
                var services = _configuration.GetSection("ServiceDiscovery:Services").Get<List<ServiceConfig>>() ?? new List<ServiceConfig>();
                var serviceHealthTasks = services.Select(CheckServiceHealth);
                var serviceHealthResults = await Task.WhenAll(serviceHealthTasks);

                var response = new
                {
                    Status = serviceHealthResults.All(r => r.IsHealthy) ? "Healthy" : "Unhealthy",
                    Services = serviceHealthResults,
                    Timestamp = DateTime.UtcNow
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking services health");
                return StatusCode(500, new { Status = "Error", Message = "Services health check failed" });
            }
        }

        /// <summary>
        /// Get configuration information (non-sensitive)
        /// </summary>
        [HttpGet("info")]
        public IActionResult GetInfo()
        {
            try
            {
                var response = new
                {
                    Application = "TLI Microservices API Gateway",
                    Version = "1.0.0",
                    Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown",
                    MachineName = Environment.MachineName,
                    ProcessId = Environment.ProcessId,
                    Uptime = DateTime.UtcNow - Process.GetCurrentProcess().StartTime.ToUniversalTime(),
                    Timestamp = DateTime.UtcNow,
                    ConfiguredServices = _configuration.GetSection("ServiceDiscovery:Services").Get<List<ServiceConfig>>()?.Select(s => new
                    {
                        s.Name,
                        s.Host,
                        s.Port
                    }).Cast<object>().ToList() ?? new List<object>()
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting application info");
                return StatusCode(500, new { Status = "Error", Message = "Failed to get application info" });
            }
        }

        private async Task<ServiceHealthResult> CheckServiceHealth(ServiceConfig service)
        {
            try
            {
                using var httpClient = new HttpClient();
                httpClient.Timeout = TimeSpan.FromSeconds(5);

                var healthUrl = $"http://{service.Host}:{service.Port}{service.HealthCheckPath}";
                var response = await httpClient.GetAsync(healthUrl);

                return new ServiceHealthResult
                {
                    Name = service.Name,
                    Host = service.Host,
                    Port = service.Port,
                    IsHealthy = response.IsSuccessStatusCode,
                    StatusCode = (int)response.StatusCode,
                    ResponseTime = 0, // Could be measured if needed
                    LastChecked = DateTime.UtcNow
                };
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Health check failed for service {ServiceName}", service.Name);
                return new ServiceHealthResult
                {
                    Name = service.Name,
                    Host = service.Host,
                    Port = service.Port,
                    IsHealthy = false,
                    StatusCode = 0,
                    Error = ex.Message,
                    LastChecked = DateTime.UtcNow
                };
            }
        }
    }

    public class ServiceConfig
    {
        public string Name { get; set; } = string.Empty;
        public string Host { get; set; } = string.Empty;
        public int Port { get; set; }
        public string HealthCheckPath { get; set; } = "/health";
    }

    public class ServiceHealthResult
    {
        public string Name { get; set; } = string.Empty;
        public string Host { get; set; } = string.Empty;
        public int Port { get; set; }
        public bool IsHealthy { get; set; }
        public int StatusCode { get; set; }
        public double ResponseTime { get; set; }
        public string? Error { get; set; }
        public DateTime LastChecked { get; set; }
    }
}
