using System;
using System.Security.Cryptography;
using System.Threading.Tasks;
using Identity.Application.Common.Interfaces;
using Identity.Domain.Entities;
using Identity.Domain.Repositories;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Shared.Messaging;

namespace Identity.Infrastructure.Services
{
    public class OtpService : IOtpService
    {
        private readonly IOtpTokenRepository _otpTokenRepository;
        private readonly IUserRepository _userRepository;
        private readonly IMessageBroker _messageBroker;
        private readonly IConfiguration _configuration;
        private readonly ILogger<OtpService> _logger;

        public OtpService(
            IOtpTokenRepository otpTokenRepository,
            IUserRepository userRepository,
            IMessageBroker messageBroker,
            IConfiguration configuration,
            ILogger<OtpService> logger)
        {
            _otpTokenRepository = otpTokenRepository;
            _userRepository = userRepository;
            _messageBroker = messageBroker;
            _configuration = configuration;
            _logger = logger;
        }

        public async Task<string> GenerateOtpAsync(
            Guid userId, 
            OtpPurpose purpose, 
            string deliveryMethod, 
            string deliveryAddress,
            int expirationMinutes = 5,
            int maxAttempts = 3)
        {
            // Invalidate existing active tokens for this purpose
            await InvalidateUserOtpsAsync(userId, purpose);

            // Generate 6-digit OTP
            var token = GenerateSecureOtp();
            var expiresAt = DateTime.UtcNow.AddMinutes(expirationMinutes);

            // Create OTP token entity
            var otpToken = new OtpToken(
                userId,
                token,
                purpose,
                deliveryMethod,
                deliveryAddress,
                expiresAt,
                maxAttempts);

            await _otpTokenRepository.AddAsync(otpToken);

            _logger.LogInformation("Generated OTP for user {UserId} with purpose {Purpose}", userId, purpose);

            return token;
        }

        public async Task<bool> ValidateOtpAsync(Guid userId, string token, OtpPurpose purpose)
        {
            var otpToken = await _otpTokenRepository.GetValidTokenAsync(userId, token, purpose);
            
            if (otpToken == null)
            {
                _logger.LogWarning("Invalid OTP attempt for user {UserId} with purpose {Purpose}", userId, purpose);
                return false;
            }

            try
            {
                otpToken.MarkAsUsed();
                await _otpTokenRepository.UpdateAsync(otpToken);
                
                _logger.LogInformation("OTP validated successfully for user {UserId} with purpose {Purpose}", userId, purpose);
                return true;
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning("OTP validation failed for user {UserId}: {Error}", userId, ex.Message);
                
                // Increment attempt count even if validation fails
                otpToken.IncrementAttemptCount();
                await _otpTokenRepository.UpdateAsync(otpToken);
                
                return false;
            }
        }

        public async Task<bool> SendOtpAsync(
            string deliveryMethod, 
            string deliveryAddress, 
            string token, 
            OtpPurpose purpose)
        {
            try
            {
                var eventName = deliveryMethod.ToLower() switch
                {
                    "sms" => "otp.sms.send",
                    "email" => "otp.email.send",
                    _ => throw new ArgumentException($"Unsupported delivery method: {deliveryMethod}")
                };

                var message = GetOtpMessage(token, purpose);

                await _messageBroker.PublishAsync(eventName, new
                {
                    DeliveryAddress = deliveryAddress,
                    Message = message,
                    Token = token,
                    Purpose = purpose.ToString(),
                    Timestamp = DateTime.UtcNow
                });

                _logger.LogInformation("OTP sent via {DeliveryMethod} to {DeliveryAddress}", deliveryMethod, MaskAddress(deliveryAddress));
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send OTP via {DeliveryMethod} to {DeliveryAddress}", deliveryMethod, MaskAddress(deliveryAddress));
                return false;
            }
        }

        public async Task InvalidateUserOtpsAsync(Guid userId, OtpPurpose purpose)
        {
            await _otpTokenRepository.InvalidateUserTokensAsync(userId, purpose);
            _logger.LogInformation("Invalidated existing OTPs for user {UserId} with purpose {Purpose}", userId, purpose);
        }

        public async Task CleanupExpiredOtpsAsync()
        {
            await _otpTokenRepository.CleanupExpiredTokensAsync();
            _logger.LogInformation("Cleaned up expired OTP tokens");
        }

        public async Task<bool> CanRequestOtpAsync(Guid userId, OtpPurpose purpose)
        {
            var activeTokens = await _otpTokenRepository.GetActiveTokensAsync(userId, purpose);
            
            // Check rate limiting - allow new OTP only if no active tokens or last token is older than 1 minute
            if (activeTokens.Count > 0)
            {
                var latestToken = activeTokens[0];
                var timeSinceLastRequest = DateTime.UtcNow - latestToken.CreatedAt;
                
                if (timeSinceLastRequest.TotalMinutes < 1)
                {
                    _logger.LogWarning("Rate limit exceeded for user {UserId} requesting OTP with purpose {Purpose}", userId, purpose);
                    return false;
                }
            }

            return true;
        }

        public async Task<int> GetRemainingAttemptsAsync(Guid userId, string token, OtpPurpose purpose)
        {
            var otpToken = await _otpTokenRepository.GetValidTokenAsync(userId, token, purpose);
            return otpToken?.MaxAttempts - otpToken?.AttemptCount ?? 0;
        }

        private string GenerateSecureOtp()
        {
            using var rng = RandomNumberGenerator.Create();
            var bytes = new byte[4];
            rng.GetBytes(bytes);
            var randomNumber = BitConverter.ToUInt32(bytes, 0);
            return (randomNumber % 900000 + 100000).ToString(); // 6-digit number
        }

        private string GetOtpMessage(string token, OtpPurpose purpose)
        {
            var appName = _configuration["App:Name"] ?? "TLI Logistics";
            
            return purpose switch
            {
                OtpPurpose.Login => $"Your {appName} login OTP is {token}. Valid for 5 minutes. Do not share this code.",
                OtpPurpose.Registration => $"Your {appName} registration OTP is {token}. Valid for 5 minutes.",
                OtpPurpose.PasswordReset => $"Your {appName} password reset OTP is {token}. Valid for 5 minutes.",
                OtpPurpose.PhoneVerification => $"Your {appName} phone verification OTP is {token}. Valid for 5 minutes.",
                OtpPurpose.EmailVerification => $"Your {appName} email verification OTP is {token}. Valid for 5 minutes.",
                _ => $"Your {appName} verification code is {token}. Valid for 5 minutes."
            };
        }

        private string MaskAddress(string address)
        {
            if (string.IsNullOrEmpty(address))
                return address;

            if (address.Contains("@"))
            {
                // Email masking
                var parts = address.Split('@');
                if (parts[0].Length <= 2)
                    return $"**@{parts[1]}";
                return $"{parts[0][..2]}***@{parts[1]}";
            }
            else
            {
                // Phone number masking
                if (address.Length <= 4)
                    return "****";
                return $"****{address[^4..]}";
            }
        }
    }
}
