using MediatR;
using OrderManagement.Application.DTOs;

namespace OrderManagement.Application.Queries.GetPublishedRfqs;

public class GetPublishedRfqsQuery : IRequest<PagedResult<RfqSummaryDto>>
{
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public string? PickupCity { get; set; }
    public string? DeliveryCity { get; set; }
    public string? LoadType { get; set; }
    public decimal? MaxBudget { get; set; }
    public bool ExcludeExpired { get; set; } = true;
}
