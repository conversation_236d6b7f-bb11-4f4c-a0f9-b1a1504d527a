using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Messaging;
using TripManagement.Domain.Entities;
using TripManagement.Domain.Enums;
using TripManagement.Domain.Repositories;

namespace TripManagement.Application.Commands.CancelTrip;

public class CancelTripCommandHandler : IRequestHandler<CancelTripCommand, bool>
{
    private readonly ITripRepository _tripRepository;
    private readonly IDriverRepository _driverRepository;
    private readonly IVehicleRepository _vehicleRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<CancelTripCommandHandler> _logger;

    public CancelTripCommandHandler(
        ITripRepository tripRepository,
        IDriverRepository driverRepository,
        IVehicleRepository vehicleRepository,
        IUnitOfWork unitOfWork,
        IMessageBroker messageBroker,
        ILogger<CancelTripCommandHandler> logger)
    {
        _tripRepository = tripRepository;
        _driverRepository = driverRepository;
        _vehicleRepository = vehicleRepository;
        _unitOfWork = unitOfWork;
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task<bool> Handle(CancelTripCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Cancelling trip {TripId} with reason: {Reason}", 
            request.TripId, request.Reason);

        try
        {
            // Get trip
            var trip = await _tripRepository.GetByIdAsync(request.TripId, cancellationToken);
            if (trip == null)
            {
                _logger.LogWarning("Trip {TripId} not found", request.TripId);
                throw new ArgumentException($"Trip {request.TripId} not found");
            }

            // Validate trip can be cancelled
            if (trip.Status == TripStatus.Completed)
            {
                _logger.LogWarning("Cannot cancel completed trip {TripId}", request.TripId);
                throw new InvalidOperationException("Cannot cancel completed trips");
            }

            if (trip.Status == TripStatus.Cancelled)
            {
                _logger.LogWarning("Trip {TripId} is already cancelled", request.TripId);
                return true; // Already cancelled, consider it successful
            }

            // Store original status for event publishing
            var originalStatus = trip.Status;

            // Cancel the trip
            trip.Cancel(request.Reason);

            // Update driver status if assigned
            Driver? driver = null;
            if (trip.DriverId.HasValue)
            {
                driver = await _driverRepository.GetByIdAsync(trip.DriverId.Value, cancellationToken);
                if (driver != null)
                {
                    driver.UpdateStatus(DriverStatus.Available);
                    _driverRepository.Update(driver);
                }
            }

            // Update vehicle status if assigned
            Vehicle? vehicle = null;
            if (trip.VehicleId.HasValue)
            {
                vehicle = await _vehicleRepository.GetByIdAsync(trip.VehicleId.Value, cancellationToken);
                if (vehicle != null)
                {
                    vehicle.UpdateStatus(VehicleStatus.Available);
                    _vehicleRepository.Update(vehicle);
                }
            }

            // Save changes
            _tripRepository.Update(trip);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Publish integration events
            await _messageBroker.PublishAsync("trip.cancelled", new
            {
                TripId = trip.Id,
                TripNumber = trip.TripNumber,
                OrderId = trip.OrderId,
                CarrierId = trip.CarrierId,
                DriverId = trip.DriverId,
                VehicleId = trip.VehicleId,
                OriginalStatus = originalStatus.ToString(),
                CancellationReason = request.Reason,
                AdditionalNotes = request.AdditionalNotes,
                CancelledBy = request.CancelledBy,
                CancelledAt = trip.CancelledAt,
                NotifyStakeholders = request.NotifyStakeholders
            }, cancellationToken);

            // Send notifications if requested
            if (request.NotifyStakeholders)
            {
                await _messageBroker.PublishAsync("communication.send.notification", new
                {
                    MessageType = "TripCancelled",
                    Subject = $"Trip Cancelled: {trip.TripNumber}",
                    Content = $"Trip {trip.TripNumber} has been cancelled. Reason: {request.Reason}",
                    Priority = "High",
                    RelatedEntityId = trip.Id,
                    RelatedEntityType = "Trip",
                    Recipients = new[]
                    {
                        new { UserId = trip.CarrierId, UserType = "Carrier" },
                        trip.DriverId.HasValue ? new { UserId = trip.DriverId.Value, UserType = "Driver" } : null
                    }.Where(r => r != null),
                    Tags = new[] { "trip", "cancellation", "urgent" },
                    Metadata = new
                    {
                        TripId = trip.Id,
                        TripNumber = trip.TripNumber,
                        CancellationReason = request.Reason,
                        CancelledBy = request.CancelledBy
                    }
                }, cancellationToken);
            }

            _logger.LogInformation("Successfully cancelled trip {TripId}", request.TripId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling trip {TripId}", request.TripId);
            throw;
        }
    }
}
