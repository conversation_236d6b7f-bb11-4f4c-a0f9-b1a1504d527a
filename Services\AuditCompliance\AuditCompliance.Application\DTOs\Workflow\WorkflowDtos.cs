namespace AuditCompliance.Application.DTOs.Workflow;

/// <summary>
/// Workflow definition DTO
/// </summary>
public class WorkflowDefinitionDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string Version { get; set; } = "1.0";
    public bool IsActive { get; set; }
    public List<WorkflowStepDto> Steps { get; set; } = new();
    public List<WorkflowTransitionDto> Transitions { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime? UpdatedAt { get; set; }
    public string? UpdatedBy { get; set; }
    public Guid? TenantId { get; set; }
}

/// <summary>
/// Create workflow definition DTO
/// </summary>
public class CreateWorkflowDefinitionDto
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string Version { get; set; } = "1.0";
    public List<WorkflowStepDto> Steps { get; set; } = new();
    public List<WorkflowTransitionDto> Transitions { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
}

/// <summary>
/// Update workflow definition DTO
/// </summary>
public class UpdateWorkflowDefinitionDto
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public List<WorkflowStepDto> Steps { get; set; } = new();
    public List<WorkflowTransitionDto> Transitions { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
}

/// <summary>
/// Workflow step DTO
/// </summary>
public class WorkflowStepDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty; // Task, Decision, Action, Timer, Parallel, Sequential
    public int Order { get; set; }
    public bool IsStartStep { get; set; }
    public bool IsEndStep { get; set; }
    public List<WorkflowActionDto> Actions { get; set; } = new();
    public WorkflowAssignmentDto? Assignment { get; set; }
    public WorkflowTimerDto? Timer { get; set; }
    public string? Condition { get; set; }
    public Dictionary<string, object> Properties { get; set; } = new();
}

/// <summary>
/// Workflow transition DTO
/// </summary>
public class WorkflowTransitionDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public Guid FromStepId { get; set; }
    public Guid ToStepId { get; set; }
    public string? Condition { get; set; }
    public int Priority { get; set; }
    public Dictionary<string, object> Properties { get; set; } = new();
}

/// <summary>
/// Workflow action DTO
/// </summary>
public class WorkflowActionDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty; // Email, API, Database, Script, Approval
    public Dictionary<string, object> Parameters { get; set; } = new();
    public bool IsRequired { get; set; }
    public int Order { get; set; }
}

/// <summary>
/// Workflow assignment DTO
/// </summary>
public class WorkflowAssignmentDto
{
    public string Type { get; set; } = string.Empty; // User, Role, Group, Auto
    public List<string> Assignees { get; set; } = new();
    public string AssignmentRule { get; set; } = string.Empty; // RoundRobin, LoadBalance, All, First
    public bool AllowReassignment { get; set; } = true;
    public WorkflowEscalationDto? Escalation { get; set; }
}

/// <summary>
/// Workflow escalation DTO
/// </summary>
public class WorkflowEscalationDto
{
    public int TimeoutMinutes { get; set; }
    public List<string> EscalateTo { get; set; } = new();
    public string EscalationType { get; set; } = string.Empty; // Manager, Role, User
    public bool SendNotification { get; set; } = true;
    public string? NotificationTemplate { get; set; }
}

/// <summary>
/// Workflow timer DTO
/// </summary>
public class WorkflowTimerDto
{
    public string Type { get; set; } = string.Empty; // Delay, Deadline, Recurring
    public int DurationMinutes { get; set; }
    public DateTime? SpecificTime { get; set; }
    public string? CronExpression { get; set; }
    public string Action { get; set; } = string.Empty; // Continue, Escalate, Cancel
}

/// <summary>
/// Workflow instance DTO
/// </summary>
public class WorkflowInstanceDto
{
    public Guid Id { get; set; }
    public Guid DefinitionId { get; set; }
    public string DefinitionName { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty; // Running, Completed, Cancelled, Suspended, Failed
    public Guid? CurrentStepId { get; set; }
    public string? CurrentStepName { get; set; }
    public string InitiatedBy { get; set; } = string.Empty;
    public DateTime StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string? CompletionReason { get; set; }
    public Dictionary<string, object> Context { get; set; } = new();
    public List<WorkflowTaskDto> Tasks { get; set; } = new();
    public string? EntityType { get; set; }
    public Guid? EntityId { get; set; }
    public string? EntityName { get; set; }
    public int Priority { get; set; } = 5; // 1-10 scale
    public Guid? TenantId { get; set; }
}

/// <summary>
/// Start workflow DTO
/// </summary>
public class StartWorkflowDto
{
    public Guid DefinitionId { get; set; }
    public string InitiatedBy { get; set; } = string.Empty;
    public Dictionary<string, object> InitialContext { get; set; } = new();
    public string? EntityType { get; set; }
    public Guid? EntityId { get; set; }
    public string? EntityName { get; set; }
    public int Priority { get; set; } = 5;
    public string? Description { get; set; }
}

/// <summary>
/// Workflow task DTO
/// </summary>
public class WorkflowTaskDto
{
    public Guid Id { get; set; }
    public Guid InstanceId { get; set; }
    public Guid StepId { get; set; }
    public string StepName { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty; // Pending, Assigned, InProgress, Completed, Cancelled
    public string? AssignedTo { get; set; }
    public string? AssignedBy { get; set; }
    public DateTime? AssignedAt { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? DueDate { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string? CompletedBy { get; set; }
    public Dictionary<string, object> Data { get; set; } = new();
    public Dictionary<string, object> Result { get; set; } = new();
    public List<WorkflowTaskActionDto> Actions { get; set; } = new();
    public int Priority { get; set; } = 5;
    public string? Comments { get; set; }
}

/// <summary>
/// Complete task DTO
/// </summary>
public class CompleteTaskDto
{
    public string CompletedBy { get; set; } = string.Empty;
    public Dictionary<string, object> Result { get; set; } = new();
    public string? Comments { get; set; }
    public string Action { get; set; } = "Complete"; // Complete, Approve, Reject, Delegate
}

/// <summary>
/// Workflow task action DTO
/// </summary>
public class WorkflowTaskActionDto
{
    public string Name { get; set; } = string.Empty;
    public string Label { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty; // Button, Link, Form
    public Dictionary<string, object> Parameters { get; set; } = new();
    public bool IsDefault { get; set; }
    public string? ConfirmationMessage { get; set; }
}

/// <summary>
/// Workflow instance filter DTO
/// </summary>
public class WorkflowInstanceFilterDto
{
    public Guid? DefinitionId { get; set; }
    public string? Status { get; set; }
    public string? InitiatedBy { get; set; }
    public DateTime? StartedAfter { get; set; }
    public DateTime? StartedBefore { get; set; }
    public string? EntityType { get; set; }
    public Guid? EntityId { get; set; }
    public int? Priority { get; set; }
    public int PageSize { get; set; } = 50;
    public int PageNumber { get; set; } = 1;
}

/// <summary>
/// Task filter DTO
/// </summary>
public class TaskFilterDto
{
    public string? Status { get; set; }
    public string? Type { get; set; }
    public DateTime? DueBefore { get; set; }
    public DateTime? DueAfter { get; set; }
    public int? Priority { get; set; }
    public bool OverdueOnly { get; set; } = false;
    public int PageSize { get; set; } = 50;
    public int PageNumber { get; set; } = 1;
}

/// <summary>
/// Workflow history DTO
/// </summary>
public class WorkflowHistoryDto
{
    public Guid Id { get; set; }
    public Guid InstanceId { get; set; }
    public string EventType { get; set; } = string.Empty; // Started, StepCompleted, TaskAssigned, Completed, etc.
    public string Description { get; set; } = string.Empty;
    public string? UserId { get; set; }
    public string? UserName { get; set; }
    public DateTime Timestamp { get; set; }
    public Dictionary<string, object> Data { get; set; } = new();
    public Guid? StepId { get; set; }
    public string? StepName { get; set; }
    public Guid? TaskId { get; set; }
}

/// <summary>
/// Workflow analytics DTO
/// </summary>
public class WorkflowAnalyticsDto
{
    public DateTime GeneratedAt { get; set; }
    public WorkflowAnalyticsRequestDto Request { get; set; } = new();
    public WorkflowAnalyticsSummaryDto Summary { get; set; } = new();
    public List<WorkflowPerformanceDto> Performance { get; set; } = new();
    public List<WorkflowTrendDto> Trends { get; set; } = new();
    public Dictionary<string, int> StatusDistribution { get; set; } = new();
    public Dictionary<string, float> AverageCompletionTimes { get; set; } = new();
}

/// <summary>
/// Workflow analytics request DTO
/// </summary>
public class WorkflowAnalyticsRequestDto
{
    public Guid? DefinitionId { get; set; }
    public string? Category { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string? GroupBy { get; set; } = "Day"; // Day, Week, Month
    public bool IncludeDetails { get; set; } = false;
}

/// <summary>
/// Workflow analytics summary DTO
/// </summary>
public class WorkflowAnalyticsSummaryDto
{
    public int TotalInstances { get; set; }
    public int CompletedInstances { get; set; }
    public int RunningInstances { get; set; }
    public int CancelledInstances { get; set; }
    public int FailedInstances { get; set; }
    public float CompletionRate { get; set; }
    public float AverageCompletionTimeHours { get; set; }
    public int TotalTasks { get; set; }
    public int CompletedTasks { get; set; }
    public int OverdueTasks { get; set; }
    public float TaskCompletionRate { get; set; }
}

/// <summary>
/// Workflow performance DTO
/// </summary>
public class WorkflowPerformanceDto
{
    public Guid DefinitionId { get; set; }
    public string DefinitionName { get; set; } = string.Empty;
    public int InstanceCount { get; set; }
    public float AverageCompletionTimeHours { get; set; }
    public float CompletionRate { get; set; }
    public int BottleneckStepCount { get; set; }
    public string? BottleneckStepName { get; set; }
    public Dictionary<string, object> Metrics { get; set; } = new();
}

/// <summary>
/// Workflow trend DTO
/// </summary>
public class WorkflowTrendDto
{
    public DateTime Date { get; set; }
    public int StartedCount { get; set; }
    public int CompletedCount { get; set; }
    public int CancelledCount { get; set; }
    public float AverageCompletionTimeHours { get; set; }
    public Dictionary<string, int> CategoryCounts { get; set; } = new();
}

/// <summary>
/// Workflow template DTO
/// </summary>
public class WorkflowTemplateDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string Industry { get; set; } = string.Empty;
    public List<string> Tags { get; set; } = new();
    public WorkflowDefinitionDto Definition { get; set; } = new();
    public List<WorkflowTemplateParameterDto> Parameters { get; set; } = new();
    public bool IsPublic { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public int UsageCount { get; set; }
    public float Rating { get; set; }
}

/// <summary>
/// Workflow template parameter DTO
/// </summary>
public class WorkflowTemplateParameterDto
{
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty; // String, Number, Boolean, List, User, Role
    public bool IsRequired { get; set; }
    public object? DefaultValue { get; set; }
    public List<string>? AllowedValues { get; set; }
    public string? Description { get; set; }
    public string? ValidationPattern { get; set; }
}

/// <summary>
/// Create from template DTO
/// </summary>
public class CreateFromTemplateDto
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public Dictionary<string, object> Parameters { get; set; } = new();
    public bool StartImmediately { get; set; } = false;
    public Dictionary<string, object> InitialContext { get; set; } = new();
}
