using CommunicationNotification.Application.Common;
using CommunicationNotification.Domain.Enums;

namespace CommunicationNotification.Application.Commands.Notifications;

/// <summary>
/// Command to send a notification
/// </summary>
public class SendNotificationCommand : CommandBase<SendNotificationResult>
{
    public Guid UserId { get; set; }
    public string Content { get; set; } = string.Empty;
    public MessageType MessageType { get; set; }
    public Priority Priority { get; set; } = Priority.Normal;
    public NotificationChannel? PreferredChannel { get; set; }
    public Language? PreferredLanguage { get; set; }
    public string? Subject { get; set; }
    public Dictionary<string, object> Parameters { get; set; } = new();
    public string? TemplateId { get; set; }
    public bool RequireDeliveryConfirmation { get; set; }
    public DateTime? ScheduledAt { get; set; }
    public List<string> Tags { get; set; } = new();
    public Dictionary<string, string> CustomHeaders { get; set; } = new();
}

/// <summary>
/// Command to send bulk notifications
/// </summary>
public class SendBulkNotificationCommand : CommandBase<SendBulkNotificationResult>
{
    public List<Guid> UserIds { get; set; } = new();
    public string Content { get; set; } = string.Empty;
    public MessageType MessageType { get; set; }
    public Priority Priority { get; set; } = Priority.Normal;
    public NotificationChannel? PreferredChannel { get; set; }
    public Language? PreferredLanguage { get; set; }
    public string? Subject { get; set; }
    public Dictionary<string, object> Parameters { get; set; } = new();
    public string? TemplateId { get; set; }
    public bool RequireDeliveryConfirmation { get; set; }
    public DateTime? ScheduledAt { get; set; }
    public List<string> Tags { get; set; } = new();
    public int BatchSize { get; set; } = 100;
    public TimeSpan BatchDelay { get; set; } = TimeSpan.FromSeconds(1);
}

/// <summary>
/// Command to send template-based notification
/// </summary>
public class SendTemplateNotificationCommand : CommandBase<SendNotificationResult>
{
    public Guid UserId { get; set; }
    public string TemplateId { get; set; } = string.Empty;
    public Dictionary<string, object> TemplateParameters { get; set; } = new();
    public MessageType MessageType { get; set; }
    public Priority Priority { get; set; } = Priority.Normal;
    public NotificationChannel? PreferredChannel { get; set; }
    public Language? PreferredLanguage { get; set; }
    public bool RequireDeliveryConfirmation { get; set; }
    public DateTime? ScheduledAt { get; set; }
    public List<string> Tags { get; set; } = new();
}

/// <summary>
/// Command to send emergency notification
/// </summary>
public class SendEmergencyNotificationCommand : CommandBase<SendNotificationResult>
{
    public Guid UserId { get; set; }
    public string Content { get; set; } = string.Empty;
    public EmergencyType EmergencyType { get; set; }
    public string? Location { get; set; }
    public Dictionary<string, object> EmergencyData { get; set; } = new();
    public bool RequireAcknowledgment { get; set; } = true;
    public TimeSpan AcknowledgmentTimeout { get; set; } = TimeSpan.FromMinutes(5);
    public List<NotificationChannel> EscalationChannels { get; set; } = new();
    public List<Guid> EscalationUserIds { get; set; } = new();
}

/// <summary>
/// Command to schedule a notification
/// </summary>
public class ScheduleNotificationCommand : CommandBase<ScheduleNotificationResult>
{
    public Guid UserId { get; set; }
    public string Content { get; set; } = string.Empty;
    public MessageType MessageType { get; set; }
    public Priority Priority { get; set; } = Priority.Normal;
    public NotificationChannel? PreferredChannel { get; set; }
    public Language? PreferredLanguage { get; set; }
    public DateTime ScheduledAt { get; set; }
    public string? Subject { get; set; }
    public Dictionary<string, object> Parameters { get; set; } = new();
    public string? TemplateId { get; set; }
    public bool RequireDeliveryConfirmation { get; set; }
    public List<string> Tags { get; set; } = new();
    public RecurrencePattern? RecurrencePattern { get; set; }
}

/// <summary>
/// Command to cancel a scheduled notification
/// </summary>
public class CancelScheduledNotificationCommand : CommandBase<CancelNotificationResult>
{
    public Guid ScheduledNotificationId { get; set; }
    public string? CancellationReason { get; set; }
}

/// <summary>
/// Command to retry a failed notification
/// </summary>
public class RetryNotificationCommand : CommandBase<SendNotificationResult>
{
    public Guid OriginalNotificationId { get; set; }
    public NotificationChannel? AlternativeChannel { get; set; }
    public Dictionary<string, object> UpdatedParameters { get; set; } = new();
    public string? RetryReason { get; set; }
}

// Result classes
public class SendNotificationResult
{
    public Guid NotificationId { get; set; }
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public NotificationChannel ChannelUsed { get; set; }
    public DateTime SentAt { get; set; }
    public string? ExternalId { get; set; }
    public Dictionary<string, object> DeliveryMetadata { get; set; } = new();
}

public class SendBulkNotificationResult
{
    public Guid BulkOperationId { get; set; }
    public bool IsSuccess { get; set; }
    public int TotalNotifications { get; set; }
    public int SuccessfulNotifications { get; set; }
    public int FailedNotifications { get; set; }
    public List<SendNotificationResult> Results { get; set; } = new();
    public List<string> Errors { get; set; } = new();
    public DateTime StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
}

public class ScheduleNotificationResult
{
    public Guid ScheduledNotificationId { get; set; }
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime ScheduledAt { get; set; }
    public DateTime CreatedAt { get; set; }
    public ScheduleStatus Status { get; set; }
}

public class CancelNotificationResult
{
    public Guid ScheduledNotificationId { get; set; }
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime CancelledAt { get; set; }
    public ScheduleStatus PreviousStatus { get; set; }
}

// Supporting enums and classes
public enum EmergencyType
{
    Accident,
    Breakdown,
    Medical,
    Security,
    Weather,
    Traffic,
    SystemFailure,
    Other
}

public enum ScheduleStatus
{
    Scheduled,
    Processing,
    Sent,
    Failed,
    Cancelled,
    Expired
}

public class RecurrencePattern
{
    public RecurrenceType Type { get; set; }
    public int Interval { get; set; } = 1;
    public List<DayOfWeek> DaysOfWeek { get; set; } = new();
    public int? DayOfMonth { get; set; }
    public DateTime? EndDate { get; set; }
    public int? MaxOccurrences { get; set; }
}

public enum RecurrenceType
{
    None,
    Daily,
    Weekly,
    Monthly,
    Yearly
}
