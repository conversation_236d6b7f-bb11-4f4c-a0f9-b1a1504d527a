using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Identity.Domain.Entities;
using Identity.Domain.Repositories;
using Microsoft.EntityFrameworkCore;

namespace Identity.Infrastructure.Repositories
{
    public class OtpTokenRepository : IOtpTokenRepository
    {
        private readonly Identity.Infrastructure.Persistence.IdentityDbContext _context;

        public OtpTokenRepository(Identity.Infrastructure.Persistence.IdentityDbContext context)
        {
            _context = context;
        }

        public async Task<OtpToken> GetByIdAsync(Guid id)
        {
            return await _context.OtpTokens
                .Include(o => o.User)
                .FirstOrDefaultAsync(o => o.Id == id);
        }

        public async Task<OtpToken> GetValidTokenAsync(Guid userId, string token, OtpPurpose purpose)
        {
            return await _context.OtpTokens
                .Include(o => o.User)
                .FirstOrDefaultAsync(o => 
                    o.UserId == userId && 
                    o.Token == token && 
                    o.Purpose == purpose &&
                    !o.IsUsed &&
                    o.ExpiresAt > DateTime.UtcNow &&
                    o.AttemptCount < o.MaxAttempts);
        }

        public async Task<List<OtpToken>> GetActiveTokensAsync(Guid userId, OtpPurpose purpose)
        {
            return await _context.OtpTokens
                .Where(o => 
                    o.UserId == userId && 
                    o.Purpose == purpose &&
                    !o.IsUsed &&
                    o.ExpiresAt > DateTime.UtcNow)
                .OrderByDescending(o => o.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<OtpToken>> GetExpiredTokensAsync()
        {
            return await _context.OtpTokens
                .Where(o => o.ExpiresAt <= DateTime.UtcNow && !o.IsUsed)
                .ToListAsync();
        }

        public async Task AddAsync(OtpToken otpToken)
        {
            await _context.OtpTokens.AddAsync(otpToken);
            await _context.SaveChangesAsync();
        }

        public async Task UpdateAsync(OtpToken otpToken)
        {
            _context.OtpTokens.Update(otpToken);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteAsync(OtpToken otpToken)
        {
            _context.OtpTokens.Remove(otpToken);
            await _context.SaveChangesAsync();
        }

        public async Task InvalidateUserTokensAsync(Guid userId, OtpPurpose purpose)
        {
            var tokens = await _context.OtpTokens
                .Where(o => o.UserId == userId && o.Purpose == purpose && !o.IsUsed)
                .ToListAsync();

            foreach (var token in tokens)
            {
                token.Invalidate();
            }

            await _context.SaveChangesAsync();
        }

        public async Task CleanupExpiredTokensAsync()
        {
            var expiredTokens = await GetExpiredTokensAsync();
            _context.OtpTokens.RemoveRange(expiredTokens);
            await _context.SaveChangesAsync();
        }
    }
}
