using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;
using Shared.Domain.Common;

namespace CommunicationNotification.Domain.Entities;

/// <summary>
/// A/B Test entity for message testing and optimization
/// </summary>
public class ABTest : BaseEntity
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public ABTestStatus Status { get; private set; }
    public ABTestType Type { get; private set; }
    public DateTime StartDate { get; private set; }
    public DateTime? EndDate { get; private set; }
    public int TargetSampleSize { get; private set; }
    public int CurrentSampleSize { get; private set; }
    public decimal TrafficAllocation { get; private set; } // Percentage of traffic to include in test
    public string HypothesisStatement { get; private set; }
    public string SuccessMetric { get; private set; } // DeliveryRate, ReadRate, ClickThroughRate, etc.
    public decimal SignificanceLevel { get; private set; } // Default 0.05 (95% confidence)
    public decimal MinimumDetectableEffect { get; private set; } // Minimum effect size to detect
    public Guid CreatedByUserId { get; private set; }
    public List<ABTestVariant> Variants { get; private set; }
    public Dictionary<string, string> TargetingCriteria { get; private set; }
    public Dictionary<string, object> Configuration { get; private set; }
    public ABTestResults? Results { get; private set; }

    private ABTest()
    {
        Variants = new List<ABTestVariant>();
        TargetingCriteria = new Dictionary<string, string>();
        Configuration = new Dictionary<string, object>();
        Name = string.Empty;
        Description = string.Empty;
        HypothesisStatement = string.Empty;
        SuccessMetric = string.Empty;
    }

    public ABTest(
        string name,
        string description,
        ABTestType type,
        DateTime startDate,
        int targetSampleSize,
        decimal trafficAllocation,
        string hypothesisStatement,
        string successMetric,
        Guid createdByUserId,
        decimal significanceLevel = 0.05m,
        decimal minimumDetectableEffect = 0.05m,
        Dictionary<string, string>? targetingCriteria = null,
        Dictionary<string, object>? configuration = null)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Test name cannot be empty", nameof(name));
        if (targetSampleSize <= 0)
            throw new ArgumentException("Target sample size must be positive", nameof(targetSampleSize));
        if (trafficAllocation <= 0 || trafficAllocation > 100)
            throw new ArgumentException("Traffic allocation must be between 0 and 100", nameof(trafficAllocation));
        if (createdByUserId == Guid.Empty)
            throw new ArgumentException("Created by user ID cannot be empty", nameof(createdByUserId));

        Name = name;
        Description = description;
        Type = type;
        Status = ABTestStatus.Draft;
        StartDate = startDate;
        TargetSampleSize = targetSampleSize;
        TrafficAllocation = trafficAllocation;
        HypothesisStatement = hypothesisStatement;
        SuccessMetric = successMetric;
        CreatedByUserId = createdByUserId;
        SignificanceLevel = significanceLevel;
        MinimumDetectableEffect = minimumDetectableEffect;
        Variants = new List<ABTestVariant>();
        TargetingCriteria = targetingCriteria ?? new Dictionary<string, string>();
        Configuration = configuration ?? new Dictionary<string, object>();
    }

    public static ABTest Create(
        string name,
        string description,
        ABTestType type,
        DateTime startDate,
        int targetSampleSize,
        decimal trafficAllocation,
        string hypothesisStatement,
        string successMetric,
        Guid createdByUserId,
        decimal significanceLevel = 0.05m,
        decimal minimumDetectableEffect = 0.05m,
        Dictionary<string, string>? targetingCriteria = null,
        Dictionary<string, object>? configuration = null)
    {
        return new ABTest(name, description, type, startDate, targetSampleSize, trafficAllocation,
            hypothesisStatement, successMetric, createdByUserId, significanceLevel, minimumDetectableEffect,
            targetingCriteria, configuration);
    }

    public void AddVariant(ABTestVariant variant)
    {
        if (variant == null)
            throw new ArgumentNullException(nameof(variant));

        if (Status != ABTestStatus.Draft)
            throw new InvalidOperationException("Cannot add variants to a test that is not in draft status");

        // Validate traffic allocation doesn't exceed 100%
        var totalAllocation = Variants.Sum(v => v.TrafficAllocation) + variant.TrafficAllocation;
        if (totalAllocation > 100)
            throw new InvalidOperationException("Total traffic allocation cannot exceed 100%");

        Variants.Add(variant);
    }

    public void RemoveVariant(Guid variantId)
    {
        if (Status != ABTestStatus.Draft)
            throw new InvalidOperationException("Cannot remove variants from a test that is not in draft status");

        var variant = Variants.FirstOrDefault(v => v.Id == variantId);
        if (variant != null)
        {
            Variants.Remove(variant);
        }
    }

    public void Start()
    {
        if (Status != ABTestStatus.Draft)
            throw new InvalidOperationException($"Cannot start test in {Status} status");

        if (!Variants.Any())
            throw new InvalidOperationException("Cannot start test without variants");

        if (Variants.Count < 2)
            throw new InvalidOperationException("Test must have at least 2 variants");

        // Validate traffic allocation
        var totalAllocation = Variants.Sum(v => v.TrafficAllocation);
        if (Math.Abs(totalAllocation - 100) > 0.01m)
            throw new InvalidOperationException("Total traffic allocation must equal 100%");

        Status = ABTestStatus.Running;
        StartDate = DateTime.UtcNow;
    }

    public void Pause()
    {
        if (Status != ABTestStatus.Running)
            throw new InvalidOperationException($"Cannot pause test in {Status} status");

        Status = ABTestStatus.Paused;
    }

    public void Resume()
    {
        if (Status != ABTestStatus.Paused)
            throw new InvalidOperationException($"Cannot resume test in {Status} status");

        Status = ABTestStatus.Running;
    }

    public void Stop()
    {
        if (Status != ABTestStatus.Running && Status != ABTestStatus.Paused)
            throw new InvalidOperationException($"Cannot stop test in {Status} status");

        Status = ABTestStatus.Stopped;
        EndDate = DateTime.UtcNow;
    }

    public void Complete(ABTestResults results)
    {
        if (Status != ABTestStatus.Running && Status != ABTestStatus.Stopped)
            throw new InvalidOperationException($"Cannot complete test in {Status} status");

        Status = ABTestStatus.Completed;
        EndDate = DateTime.UtcNow;
        Results = results ?? throw new ArgumentNullException(nameof(results));
    }

    public void UpdateSampleSize(int newSampleSize)
    {
        CurrentSampleSize = newSampleSize;
    }

    public ABTestVariant? GetVariantForUser(Guid userId)
    {
        if (Status != ABTestStatus.Running)
            return null;

        if (!ShouldIncludeUser(userId))
            return null;

        // Use consistent hashing to assign user to variant
        var hash = GetUserHash(userId);
        var cumulativeAllocation = 0m;

        foreach (var variant in Variants.OrderBy(v => v.Name))
        {
            cumulativeAllocation += variant.TrafficAllocation;
            if (hash <= cumulativeAllocation)
            {
                return variant;
            }
        }

        return Variants.LastOrDefault();
    }

    public bool IsStatisticallySignificant()
    {
        return Results?.IsStatisticallySignificant == true;
    }

    public bool HasReachedTargetSampleSize()
    {
        return CurrentSampleSize >= TargetSampleSize;
    }

    public bool ShouldIncludeUser(Guid userId)
    {
        // Check if user meets targeting criteria
        foreach (var criteria in TargetingCriteria)
        {
            // This would typically check against user properties
            // For now, we'll include all users
        }

        // Check traffic allocation
        var userHash = GetUserHash(userId);
        return userHash <= TrafficAllocation;
    }

    private decimal GetUserHash(Guid userId)
    {
        // Create a consistent hash for the user for this test
        var combined = $"{userId}:{Id}";
        var hash = Math.Abs(combined.GetHashCode()) % 10000;
        return hash / 100m; // Convert to percentage (0-100)
    }

    public void AddTargetingCriteria(string key, string value)
    {
        TargetingCriteria[key] = value;
    }

    public void UpdateConfiguration(string key, object value)
    {
        Configuration[key] = value;
    }
}

/// <summary>
/// A/B Test variant representing different versions being tested
/// </summary>
public class ABTestVariant
{
    public Guid Id { get; private set; }
    public string Name { get; private set; }
    public string Description { get; private set; }
    public decimal TrafficAllocation { get; private set; } // Percentage of test traffic
    public bool IsControl { get; private set; }
    public Dictionary<string, object> Configuration { get; private set; }
    public int SampleSize { get; private set; }
    public MessagePerformanceMetrics? Performance { get; private set; }

    private ABTestVariant()
    {
        Configuration = new Dictionary<string, object>();
        Name = string.Empty;
        Description = string.Empty;
    }

    public ABTestVariant(
        string name,
        string description,
        decimal trafficAllocation,
        bool isControl = false,
        Dictionary<string, object>? configuration = null)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Variant name cannot be empty", nameof(name));
        if (trafficAllocation <= 0 || trafficAllocation > 100)
            throw new ArgumentException("Traffic allocation must be between 0 and 100", nameof(trafficAllocation));

        Id = Guid.NewGuid();
        Name = name;
        Description = description;
        TrafficAllocation = trafficAllocation;
        IsControl = isControl;
        Configuration = configuration ?? new Dictionary<string, object>();
    }

    public void UpdatePerformance(MessagePerformanceMetrics performance)
    {
        Performance = performance ?? throw new ArgumentNullException(nameof(performance));
    }

    public void UpdateSampleSize(int sampleSize)
    {
        SampleSize = sampleSize;
    }

    public void UpdateConfiguration(string key, object value)
    {
        Configuration[key] = value;
    }
}

/// <summary>
/// A/B Test status enumeration
/// </summary>
public enum ABTestStatus
{
    Draft = 1,
    Running = 2,
    Paused = 3,
    Stopped = 4,
    Completed = 5,
    Archived = 6
}

/// <summary>
/// A/B Test type enumeration
/// </summary>
public enum ABTestType
{
    MessageContent = 1,
    MessageTemplate = 2,
    SendTime = 3,
    Channel = 4,
    Subject = 5,
    CallToAction = 6,
    Personalization = 7,
    Frequency = 8
}


