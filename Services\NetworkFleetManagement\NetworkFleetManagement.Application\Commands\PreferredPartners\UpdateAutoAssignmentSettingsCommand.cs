using MediatR;

namespace NetworkFleetManagement.Application.Commands.PreferredPartners;

public class UpdateAutoAssignmentSettingsCommand : IRequest<bool>
{
    public Guid PreferredPartnerId { get; set; }
    public Guid UserId { get; set; }
    public bool AutoAssignEnabled { get; set; }
    public decimal? AutoAssignThreshold { get; set; }
    public List<string>? PreferredRoutes { get; set; }
    public List<string>? PreferredLoadTypes { get; set; }
    public List<string>? ExcludedRoutes { get; set; }
    public List<string>? ExcludedLoadTypes { get; set; }
}
