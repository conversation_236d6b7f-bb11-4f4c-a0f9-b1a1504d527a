using System;
using System.Collections.Generic;
using MediatR;
using MobileWorkflow.Application.DTOs;

namespace MobileWorkflow.Application.Commands.OfflineSync
{
    public class StartAdvancedSyncCommand : IRequest<SyncOperationResult>
    {
        public Guid UserId { get; set; }
        public Guid DeviceId { get; set; }
        public SyncOptions Options { get; set; } = new();
        public List<string> PriorityDataTypes { get; set; } = new();
        public bool EnableBandwidthOptimization { get; set; } = true;
        public bool EnableProgressiveSync { get; set; } = true;
        public int? MaxBandwidthKbps { get; set; }
        public bool SyncOnWiFiOnly { get; set; } = false;
        public bool SyncOnLowBattery { get; set; } = false;
    }

    public class SyncOptions
    {
        public string ClientVersion { get; set; } = string.Empty;
        public bool IncludeMetadata { get; set; } = true;
        public bool EnableCompression { get; set; } = true;
        public int BatchSize { get; set; } = 50;
        public int MaxRetryAttempts { get; set; } = 3;
        public TimeSpan RetryDelay { get; set; } = TimeSpan.FromMinutes(1);
        public Dictionary<string, string> ConflictResolutionStrategies { get; set; } = new();
        public List<string> ExcludedDataTypes { get; set; } = new();
        public DateTime? SyncFromDate { get; set; }
        public bool ValidateDataIntegrity { get; set; } = true;
    }
}
