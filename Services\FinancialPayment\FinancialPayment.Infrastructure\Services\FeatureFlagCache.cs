using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Domain.Entities;

namespace FinancialPayment.Infrastructure.Services;

public class FeatureFlagCache : IFeatureFlagCache
{
    private readonly IMemoryCache _cache;
    private readonly ILogger<FeatureFlagCache> _logger;
    private const string CACHE_KEY_PREFIX = "feature_flag:";
    private const string ALL_FLAGS_KEY = "feature_flags:all";

    public FeatureFlagCache(IMemoryCache cache, ILogger<FeatureFlagCache> logger)
    {
        _cache = cache;
        _logger = logger;
    }

    public async Task<FeatureFlag?> GetAsync(string flagKey)
    {
        try
        {
            var cacheKey = GetCacheKey(flagKey);
            var flag = _cache.Get<FeatureFlag>(cacheKey);
            
            if (flag != null)
            {
                _logger.LogDebug("Feature flag {FlagKey} found in cache", flagKey);
            }

            return await Task.FromResult(flag);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting feature flag {FlagKey} from cache", flagKey);
            return null;
        }
    }

    public async Task SetAsync(string flagKey, FeatureFlag flag, TimeSpan? expiry = null)
    {
        try
        {
            var cacheKey = GetCacheKey(flagKey);
            var options = new MemoryCacheEntryOptions();
            
            if (expiry.HasValue)
            {
                options.AbsoluteExpirationRelativeToNow = expiry.Value;
            }
            else
            {
                options.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(15); // Default 15 minutes
            }

            // Set sliding expiration to refresh cache on access
            options.SlidingExpiration = TimeSpan.FromMinutes(5);

            _cache.Set(cacheKey, flag, options);
            
            // Update the all flags cache
            await UpdateAllFlagsCache(flagKey, flag);

            _logger.LogDebug("Feature flag {FlagKey} cached successfully", flagKey);
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error caching feature flag {FlagKey}", flagKey);
        }
    }

    public async Task RemoveAsync(string flagKey)
    {
        try
        {
            var cacheKey = GetCacheKey(flagKey);
            _cache.Remove(cacheKey);
            
            // Remove from all flags cache
            await RemoveFromAllFlagsCache(flagKey);

            _logger.LogDebug("Feature flag {FlagKey} removed from cache", flagKey);
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing feature flag {FlagKey} from cache", flagKey);
        }
    }

    public async Task ClearAsync()
    {
        try
        {
            // Since IMemoryCache doesn't have a clear all method, we'll remove the all flags cache
            // Individual flag caches will expire naturally
            _cache.Remove(ALL_FLAGS_KEY);

            _logger.LogInformation("Feature flag cache cleared");
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing feature flag cache");
        }
    }

    public async Task<Dictionary<string, FeatureFlag>> GetAllAsync()
    {
        try
        {
            var allFlags = _cache.Get<Dictionary<string, FeatureFlag>>(ALL_FLAGS_KEY);
            return await Task.FromResult(allFlags ?? new Dictionary<string, FeatureFlag>());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all feature flags from cache");
            return new Dictionary<string, FeatureFlag>();
        }
    }

    private string GetCacheKey(string flagKey)
    {
        return $"{CACHE_KEY_PREFIX}{flagKey}";
    }

    private async Task UpdateAllFlagsCache(string flagKey, FeatureFlag flag)
    {
        try
        {
            var allFlags = await GetAllAsync();
            allFlags[flagKey] = flag;

            var options = new MemoryCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(30),
                SlidingExpiration = TimeSpan.FromMinutes(10)
            };

            _cache.Set(ALL_FLAGS_KEY, allFlags, options);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating all flags cache");
        }
    }

    private async Task RemoveFromAllFlagsCache(string flagKey)
    {
        try
        {
            var allFlags = await GetAllAsync();
            if (allFlags.ContainsKey(flagKey))
            {
                allFlags.Remove(flagKey);

                var options = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(30),
                    SlidingExpiration = TimeSpan.FromMinutes(10)
                };

                _cache.Set(ALL_FLAGS_KEY, allFlags, options);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing flag from all flags cache");
        }
    }
}
