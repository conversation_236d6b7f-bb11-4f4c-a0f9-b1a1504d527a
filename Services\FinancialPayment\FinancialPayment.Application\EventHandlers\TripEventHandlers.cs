using MediatR;
using Microsoft.Extensions.Logging;
using FinancialPayment.Application.Commands.ProcessMilestonePayment;
using FinancialPayment.Application.IntegrationEvents;

namespace FinancialPayment.Application.EventHandlers;

public class TripStartedEventHandler : INotificationHandler<TripStartedIntegrationEvent>
{
    private readonly IMediator _mediator;
    private readonly ILogger<TripStartedEventHandler> _logger;

    public TripStartedEventHandler(IMediator mediator, ILogger<TripStartedEventHandler> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    public async Task Handle(TripStartedIntegrationEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing trip started event for milestone payments. TripId: {TripId}", notification.TripId);

        try
        {
            var command = new ProcessMilestonePaymentCommand
            {
                TripId = notification.TripId,
                OrderId = notification.OrderId,
                MilestoneType = "Pickup",
                TriggerEvent = "TripStarted",
                EventData = new Dictionary<string, object>
                {
                    ["TripId"] = notification.TripId,
                    ["OrderId"] = notification.OrderId,
                    ["StartedAt"] = notification.StartedAt,
                    ["DriverId"] = notification.DriverId,
                    ["VehicleId"] = notification.VehicleId
                },
                TriggeredBy = notification.DriverId,
                EventTimestamp = notification.StartedAt
            };

            var result = await _mediator.Send(command, cancellationToken);
            
            if (result.Success)
            {
                _logger.LogInformation("Successfully processed pickup milestone payments for trip {TripId}. Amount released: {Amount}",
                    notification.TripId, result.TotalAmountReleased);
            }
            else
            {
                _logger.LogWarning("Failed to process pickup milestone payments for trip {TripId}: {Message}",
                    notification.TripId, result.Message);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing trip started event for milestone payments. TripId: {TripId}", notification.TripId);
        }
    }
}

public class TripCompletedEventHandler : INotificationHandler<TripCompletedIntegrationEvent>
{
    private readonly IMediator _mediator;
    private readonly ILogger<TripCompletedEventHandler> _logger;

    public TripCompletedEventHandler(IMediator mediator, ILogger<TripCompletedEventHandler> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    public async Task Handle(TripCompletedIntegrationEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing trip completed event for milestone payments. TripId: {TripId}", notification.TripId);

        try
        {
            var command = new ProcessMilestonePaymentCommand
            {
                TripId = notification.TripId,
                OrderId = notification.OrderId,
                MilestoneType = "Delivery",
                TriggerEvent = "TripCompleted",
                EventData = new Dictionary<string, object>
                {
                    ["TripId"] = notification.TripId,
                    ["OrderId"] = notification.OrderId,
                    ["CompletedAt"] = notification.CompletedAt,
                    ["DriverId"] = notification.DriverId,
                    ["VehicleId"] = notification.VehicleId,
                    ["ActualDistanceKm"] = notification.ActualDistanceKm,
                    ["ProofOfDeliveryUploaded"] = notification.ProofOfDeliveryUploaded
                },
                TriggeredBy = notification.DriverId,
                EventTimestamp = notification.CompletedAt
            };

            var result = await _mediator.Send(command, cancellationToken);
            
            if (result.Success)
            {
                _logger.LogInformation("Successfully processed delivery milestone payments for trip {TripId}. Amount released: {Amount}",
                    notification.TripId, result.TotalAmountReleased);
            }
            else
            {
                _logger.LogWarning("Failed to process delivery milestone payments for trip {TripId}: {Message}",
                    notification.TripId, result.Message);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing trip completed event for milestone payments. TripId: {TripId}", notification.TripId);
        }
    }
}

public class DocumentUploadedEventHandler : INotificationHandler<DocumentUploadedIntegrationEvent>
{
    private readonly IMediator _mediator;
    private readonly ILogger<DocumentUploadedEventHandler> _logger;

    public DocumentUploadedEventHandler(IMediator mediator, ILogger<DocumentUploadedEventHandler> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    public async Task Handle(DocumentUploadedIntegrationEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing document uploaded event for milestone payments. DocumentId: {DocumentId}", notification.DocumentId);

        try
        {
            // Only process if it's a trip-related document
            if (notification.RelatedEntityType != "Trip")
                return;

            var command = new ProcessMilestonePaymentCommand
            {
                TripId = notification.RelatedEntityId,
                OrderId = notification.OrderId,
                MilestoneType = "Documentation",
                TriggerEvent = "DocumentUploaded",
                EventData = new Dictionary<string, object>
                {
                    ["DocumentId"] = notification.DocumentId,
                    ["DocumentType"] = notification.DocumentType,
                    ["RelatedEntityId"] = notification.RelatedEntityId,
                    ["RelatedEntityType"] = notification.RelatedEntityType,
                    ["UploadedAt"] = notification.UploadedAt
                },
                TriggeredBy = notification.UploadedBy,
                EventTimestamp = notification.UploadedAt
            };

            var result = await _mediator.Send(command, cancellationToken);
            
            if (result.Success)
            {
                _logger.LogInformation("Successfully processed documentation milestone payments for trip {TripId}. Amount released: {Amount}",
                    notification.RelatedEntityId, result.TotalAmountReleased);
            }
            else
            {
                _logger.LogWarning("Failed to process documentation milestone payments for trip {TripId}: {Message}",
                    notification.RelatedEntityId, result.Message);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing document uploaded event for milestone payments. DocumentId: {DocumentId}", notification.DocumentId);
        }
    }
}
