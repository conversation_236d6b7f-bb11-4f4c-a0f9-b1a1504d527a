﻿namespace OrderManagement.Domain.Enums;

public enum RfqStatus
{
    Draft = 0,
    Published = 1,
    Closed = 2,
    Expired = 3,
    Awarded = 4,
    Cancelled = 5
}

public enum BidStatus
{
    Submitted = 0,
    Accepted = 1,
    Rejected = 2,
    Withdrawn = 3,
    Expired = 4,
    Awarded = 5,
    Pending = 6
}

public enum OrderStatus
{
    Created = 0,
    Confirmed = 1,
    InProgress = 2,
    Completed = 3,
    Cancelled = 4,
    OnHold = 5
}

public enum PaymentStatus
{
    Pending = 0,
    Paid = 1,
    PartiallyPaid = 2,
    Overdue = 3,
    Failed = 4,
    Refunded = 5
}

public enum InvoiceStatus
{
    Draft = 0,
    Sent = 1,
    Paid = 2,
    Overdue = 3,
    Cancelled = 4,
    PartiallyPaid = 5
}

public enum InvoiceType
{
    Standard = 0,
    Proforma = 1,
    CreditNote = 2,
    DebitNote = 3,
    Recurring = 4,
    Interim = 5,
    Final = 6
}

public enum PartnerType
{
    Carrier = 0,
    Broker = 1,
    Shipper = 2,
    Preferred = 3,
    Blacklisted = 4,
    Transporter = 5
}

public enum DocumentStatus
{
    Pending = 0,
    Approved = 1,
    Rejected = 2,
    UnderReview = 3,
    Expired = 4,
    Archived = 5
}

public enum CounterOfferType
{
    Price = 0,
    Timeline = 1,
    Terms = 2,
    Route = 3,
    Equipment = 4,
    Combined = 5,
    Broker = 6,
    Shipper = 7
}

public enum OrderType
{
    Standard = 0,
    Express = 1,
    Scheduled = 2,
    Recurring = 3,
    Emergency = 4,
    Bulk = 5,
    Specialized = 6
}

public enum DocumentType
{
    RfqSpecification = 0,
    LoadPhoto = 1,
    BillOfLading = 2,
    ProofOfDelivery = 3,
    Invoice = 4,
    Contract = 5,
    Insurance = 6,
    Permit = 7,
    Other = 8
}

public enum TimeframeUnit
{
    Minutes = 0,
    Hours = 1,
    Days = 2
}

public enum VehicleType
{
    Truck = 0,
    Van = 1,
    Trailer = 2,
    Container = 3,
    Flatbed = 4,
    Refrigerated = 5,
    Tanker = 6,
    Other = 7
}

public enum LoadType
{
    General = 0,
    Fragile = 1,
    Hazardous = 2,
    Perishable = 3,
    Oversized = 4,
    Liquid = 5,
    Bulk = 6,
    Automotive = 7,
    Other = 8
}

public enum UrgencyLevel
{
    Normal = 0,
    High = 1,
    Critical = 2,
    Emergency = 3
}

public enum WeightUnit
{
    Kg = 0,
    Ton = 1,
    Pound = 2
}

public enum VolumeUnit
{
    CubicMeter = 0,
    CubicFeet = 1,
    Liter = 2
}

public enum DistanceUnit
{
    Kilometer = 0,
    Mile = 1
}

public enum RfqTimelineEventType
{
    Created = 0,
    Published = 1,
    BidReceived = 2,
    BidAccepted = 3,
    BidRejected = 4,
    TimeframeExtended = 5,
    Closed = 6,
    Expired = 7,
    Cancelled = 8,
    RouteAssigned = 9,
    RouteReassigned = 10,
    DocumentAdded = 11,
    DocumentRemoved = 12,
    StatusChanged = 13,
    PriceExpectationSet = 14,
    ReverseAuctionEnabled = 15,
    BrokerCommentAdded = 16,
    MilestoneTemplateAttached = 17,
    MilestoneTemplateDetached = 18,
    ForceAwarded = 19,
    AwardReset = 20,
    RoutingFailed = 21,
    RoutingTagAdded = 22,
    Other = 99
}

public enum RfqRoutingAction
{
    RouteToBroker = 0,
    RouteToCarrier = 1,
    Reassign = 2,
    Recall = 3,
    Forward = 4
}

public enum RfqRoutingEntityType
{
    TransportCompany = 0,
    Broker = 1,
    Carrier = 2,
    System = 3
}

public enum RfqRoutingStatus
{
    Pending = 0,
    Responded = 1,
    Completed = 2,
    Failed = 3,
    Expired = 4,
    Cancelled = 5
}

public enum RfqTagType
{
    RoutingFailure = 0,
    NoResponse = 1,
    LowBidCount = 2,
    HighValue = 3,
    UrgentLoad = 4,
    SpecialHandling = 5,
    GeographicChallenge = 6,
    SeasonalDemand = 7,
    Custom = 99
}

public enum MilestoneTemplateType
{
    Standard = 0,
    Express = 1,
    LongHaul = 2,
    LocalDelivery = 3,
    Specialized = 4,
    Custom = 99
}

public enum MilestoneStepType
{
    Pickup = 0,
    InTransit = 1,
    Delivery = 2,
    Documentation = 3,
    Inspection = 4,
    Payment = 5,
    Custom = 99
}

public enum MilestoneProgressStatus
{
    NotStarted = 0,
    InProgress = 1,
    PendingApproval = 2,
    Completed = 3,
    Cancelled = 4
}

public enum PriceExpectationType
{
    Fixed = 0,
    Range = 1,
    Target = 2,
    Maximum = 3,
    Negotiable = 4
}

public enum BrokerCommentType
{
    General = 0,
    Routing = 1,
    Pricing = 2,
    Capacity = 3,
    Requirements = 4,
    Timeline = 5,
    Documentation = 6,
    Issue = 7
}

public enum AdministrativeAction
{
    ForceAwardRfq = 0,
    ResetRfqAward = 1,
    SecurityViolation = 2,
    DataExport = 3,
    BulkUpdate = 4,
    SystemConfiguration = 5,
    UserManagement = 6,
    ReportGeneration = 7,
    DataView = 8,
    Other = 99
}

public enum AuditSeverity
{
    Low = 0,
    Medium = 1,
    High = 2,
    Critical = 3
}

public enum OrderTimelineEventType
{
    OrderCreated = 0,
    OrderConfirmed = 1,
    OrderStarted = 2,
    OrderCompleted = 3,
    OrderCancelled = 4,
    OrderOnHold = 5,
    StatusChanged = 6,
    PaymentUpdated = 7,
    DocumentUploaded = 8,
    DocumentApproved = 9,
    DocumentRejected = 10,
    TripAssigned = 11,
    TripStarted = 12,
    TripCompleted = 13,
    LocationUpdated = 14,
    MilestoneReached = 15,
    MilestoneDelayed = 16,
    ExceptionReported = 17,
    DisputeCreated = 18,
    DisputeResolved = 19,
    AdminAction = 20,
    SystemAction = 21,
    UserAction = 22,
    IntegrationEvent = 23,
    NotificationSent = 24,
    FeedbackReceived = 25,
    Other = 99
}

public enum DisputeType
{
    PaymentDispute = 0,
    ServiceQuality = 1,
    DeliveryIssue = 2,
    DamageOrLoss = 3,
    DelayCompensation = 4,
    ContractualDispute = 5,
    BillingError = 6,
    DocumentationIssue = 7,
    CommunicationIssue = 8,
    Other = 99
}

public enum DisputeStatus
{
    Open = 0,
    InProgress = 1,
    UnderReview = 2,
    Resolved = 3,
    Closed = 4,
    Escalated = 5,
    Cancelled = 6
}

public enum DisputePriority
{
    Low = 0,
    Medium = 1,
    High = 2,
    Critical = 3
}

