using MediatR;

namespace Identity.Application.Auth.Commands.RequestOtpLogin
{
    public class RequestOtpLoginCommand : IRequest<RequestOtpLoginResponse>
    {
        public string Username { get; set; }
        public string DeliveryMethod { get; set; } // "SMS" or "Email"
        public string IpAddress { get; set; }
        public string UserAgent { get; set; }
    }

    public class RequestOtpLoginResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public string DeliveryAddress { get; set; } // Masked phone/email
        public int ExpirationMinutes { get; set; }
        public int MaxAttempts { get; set; }
    }
}
