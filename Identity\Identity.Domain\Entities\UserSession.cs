using System;
using Identity.Domain.Common;
using Identity.Domain.Exceptions;

namespace Identity.Domain.Entities
{
    public class UserSession : Entity
    {
        public Guid UserId { get; private set; }
        public string Token { get; private set; }
        public string DeviceInfo { get; private set; }
        public string IpAddress { get; private set; }
        public DateTime LastActivity { get; private set; }
        public DateTime ExpiresAt { get; private set; }
        public bool IsActive { get; private set; }

        private UserSession() { }

        public UserSession(Guid userId, string token, string deviceInfo, string ipAddress, DateTime expiresAt)
        {
            if (string.IsNullOrWhiteSpace(token))
                throw new DomainException("Token cannot be empty");

            UserId = userId;
            Token = token;
            DeviceInfo = deviceInfo;
            IpAddress = ipAddress;
            LastActivity = DateTime.UtcNow;
            ExpiresAt = expiresAt;
            IsActive = true;
            CreatedAt = DateTime.UtcNow;
        }

        public void UpdateLastActivity()
        {
            LastActivity = DateTime.UtcNow;
            UpdatedAt = DateTime.UtcNow;
        }

        public void Terminate()
        {
            IsActive = false;
            UpdatedAt = DateTime.UtcNow;
        }

        public bool IsExpired()
        {
            return DateTime.UtcNow > ExpiresAt;
        }
    }
}
