-- Migration: Add VehicleTypeMaster table
-- Date: 2024-12-26
-- Description: Create vehicle_type_masters table for master data management

-- Create the vehicle_type_masters table
CREATE TABLE IF NOT EXISTS networkfleet.vehicle_type_masters (
    "Id" UUID PRIMARY KEY,
    "Code" VARCHAR(20) NOT NULL,
    "Name" VARCHAR(100) NOT NULL,
    "Description" VARCHAR(500),
    "Category" VARCHAR(50) NOT NULL,
    "MinLoadCapacityKg" DECIMAL(10,2),
    "MaxLoadCapacityKg" DECIMAL(10,2),
    "MinVolumeCapacityM3" DECIMAL(10,2),
    "MaxVolumeCapacityM3" DECIMAL(10,2),
    "SpecialRequirements" VARCHAR(1000),
    "IsActive" BOOLEAN NOT NULL DEFAULT TRUE,
    "SortOrder" INTEGER NOT NULL DEFAULT 0,
    "IconUrl" VARCHAR(500),
    "AdditionalProperties" JSONB NOT NULL DEFAULT '{}',
    "CreatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    "UpdatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create unique index on Code
CREATE UNIQUE INDEX IF NOT EXISTS "IX_VehicleTypeMasters_Code" 
ON networkfleet.vehicle_type_masters ("Code");

-- Create index on Name
CREATE INDEX IF NOT EXISTS "IX_VehicleTypeMasters_Name" 
ON networkfleet.vehicle_type_masters ("Name");

-- Create index on Category
CREATE INDEX IF NOT EXISTS "IX_VehicleTypeMasters_Category" 
ON networkfleet.vehicle_type_masters ("Category");

-- Create index on IsActive
CREATE INDEX IF NOT EXISTS "IX_VehicleTypeMasters_IsActive" 
ON networkfleet.vehicle_type_masters ("IsActive");

-- Create index on SortOrder
CREATE INDEX IF NOT EXISTS "IX_VehicleTypeMasters_SortOrder" 
ON networkfleet.vehicle_type_masters ("SortOrder");

-- Create composite index for common queries
CREATE INDEX IF NOT EXISTS "IX_VehicleTypeMasters_Category_IsActive_SortOrder" 
ON networkfleet.vehicle_type_masters ("Category", "IsActive", "SortOrder");

-- Create index for load capacity queries
CREATE INDEX IF NOT EXISTS "IX_VehicleTypeMasters_LoadCapacity" 
ON networkfleet.vehicle_type_masters ("MinLoadCapacityKg", "MaxLoadCapacityKg");

-- Create index for volume capacity queries
CREATE INDEX IF NOT EXISTS "IX_VehicleTypeMasters_VolumeCapacity" 
ON networkfleet.vehicle_type_masters ("MinVolumeCapacityM3", "MaxVolumeCapacityM3");

-- Add check constraints
ALTER TABLE networkfleet.vehicle_type_masters 
ADD CONSTRAINT IF NOT EXISTS "CK_VehicleTypeMasters_LoadCapacity" 
CHECK ("MinLoadCapacityKg" IS NULL OR "MaxLoadCapacityKg" IS NULL OR "MinLoadCapacityKg" <= "MaxLoadCapacityKg");

ALTER TABLE networkfleet.vehicle_type_masters 
ADD CONSTRAINT IF NOT EXISTS "CK_VehicleTypeMasters_VolumeCapacity" 
CHECK ("MinVolumeCapacityM3" IS NULL OR "MaxVolumeCapacityM3" IS NULL OR "MinVolumeCapacityM3" <= "MaxVolumeCapacityM3");

ALTER TABLE networkfleet.vehicle_type_masters 
ADD CONSTRAINT IF NOT EXISTS "CK_VehicleTypeMasters_PositiveCapacities" 
CHECK (
    ("MinLoadCapacityKg" IS NULL OR "MinLoadCapacityKg" >= 0) AND 
    ("MaxLoadCapacityKg" IS NULL OR "MaxLoadCapacityKg" >= 0) AND 
    ("MinVolumeCapacityM3" IS NULL OR "MinVolumeCapacityM3" >= 0) AND 
    ("MaxVolumeCapacityM3" IS NULL OR "MaxVolumeCapacityM3" >= 0)
);

-- Insert default vehicle type masters
INSERT INTO networkfleet.vehicle_type_masters 
("Id", "Code", "Name", "Description", "Category", "MinLoadCapacityKg", "MaxLoadCapacityKg", "MinVolumeCapacityM3", "MaxVolumeCapacityM3", "SortOrder", "CreatedAt", "UpdatedAt")
VALUES 
(gen_random_uuid(), 'TRUCK', 'Truck', 'Standard cargo truck for general freight', 'Commercial', 1000, 25000, 10, 100, 1, NOW(), NOW()),
(gen_random_uuid(), 'VAN', 'Van', 'Light commercial vehicle for small deliveries', 'Commercial', 100, 3000, 2, 20, 2, NOW(), NOW()),
(gen_random_uuid(), 'TRAILER', 'Trailer', 'Heavy-duty trailer for large cargo', 'Commercial', 5000, 40000, 20, 150, 3, NOW(), NOW()),
(gen_random_uuid(), 'CONTAINER', 'Container Truck', 'Specialized truck for container transport', 'Commercial', 10000, 35000, 30, 120, 4, NOW(), NOW()),
(gen_random_uuid(), 'PICKUP', 'Pickup Truck', 'Light pickup truck for small loads', 'Light', 200, 2000, 1, 10, 5, NOW(), NOW()),
(gen_random_uuid(), 'MOTORCYCLE', 'Motorcycle', 'Two-wheeler for express deliveries', 'Light', 5, 100, 0.1, 1, 6, NOW(), NOW()),
(gen_random_uuid(), 'BICYCLE', 'Bicycle', 'Eco-friendly option for local deliveries', 'Light', 1, 50, 0.05, 0.5, 7, NOW(), NOW()),
(gen_random_uuid(), 'FLATBED', 'Flatbed Truck', 'Open truck for oversized cargo', 'Specialized', 2000, 30000, NULL, NULL, 8, NOW(), NOW()),
(gen_random_uuid(), 'REFRIGERATED', 'Refrigerated Truck', 'Temperature-controlled transport', 'Specialized', 1000, 20000, 5, 80, 9, NOW(), NOW()),
(gen_random_uuid(), 'TANKER', 'Tanker Truck', 'Liquid cargo transport', 'Specialized', 5000, 35000, 10, 100, 10, NOW(), NOW()),
(gen_random_uuid(), 'OTHER', 'Other', 'Other vehicle types not categorized', 'General', NULL, NULL, NULL, NULL, 99, NOW(), NOW())
ON CONFLICT ("Code") DO NOTHING;

-- Add comment to table
COMMENT ON TABLE networkfleet.vehicle_type_masters IS 'Master data table for vehicle types used in fleet management';

-- Add comments to columns
COMMENT ON COLUMN networkfleet.vehicle_type_masters."Id" IS 'Unique identifier for the vehicle type master';
COMMENT ON COLUMN networkfleet.vehicle_type_masters."Code" IS 'Unique code for the vehicle type (uppercase)';
COMMENT ON COLUMN networkfleet.vehicle_type_masters."Name" IS 'Display name of the vehicle type';
COMMENT ON COLUMN networkfleet.vehicle_type_masters."Description" IS 'Detailed description of the vehicle type';
COMMENT ON COLUMN networkfleet.vehicle_type_masters."Category" IS 'Category classification (Commercial, Light, Specialized, etc.)';
COMMENT ON COLUMN networkfleet.vehicle_type_masters."MinLoadCapacityKg" IS 'Minimum load capacity in kilograms';
COMMENT ON COLUMN networkfleet.vehicle_type_masters."MaxLoadCapacityKg" IS 'Maximum load capacity in kilograms';
COMMENT ON COLUMN networkfleet.vehicle_type_masters."MinVolumeCapacityM3" IS 'Minimum volume capacity in cubic meters';
COMMENT ON COLUMN networkfleet.vehicle_type_masters."MaxVolumeCapacityM3" IS 'Maximum volume capacity in cubic meters';
COMMENT ON COLUMN networkfleet.vehicle_type_masters."SpecialRequirements" IS 'Special requirements or restrictions for this vehicle type';
COMMENT ON COLUMN networkfleet.vehicle_type_masters."IsActive" IS 'Whether this vehicle type is active and available for use';
COMMENT ON COLUMN networkfleet.vehicle_type_masters."SortOrder" IS 'Display order for sorting in lists';
COMMENT ON COLUMN networkfleet.vehicle_type_masters."IconUrl" IS 'URL to icon image for this vehicle type';
COMMENT ON COLUMN networkfleet.vehicle_type_masters."AdditionalProperties" IS 'Additional properties stored as JSON';
COMMENT ON COLUMN networkfleet.vehicle_type_masters."CreatedAt" IS 'Timestamp when the record was created';
COMMENT ON COLUMN networkfleet.vehicle_type_masters."UpdatedAt" IS 'Timestamp when the record was last updated';

-- Grant permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON networkfleet.vehicle_type_masters TO timescale;
GRANT USAGE ON SCHEMA networkfleet TO timescale;
