using FluentAssertions;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using SubscriptionManagement.API;
using SubscriptionManagement.Application.DTOs;
using SubscriptionManagement.Domain.Enums;
using System.Net;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using Xunit;

namespace SubscriptionManagement.Tests.Integration
{
    public class PaymentProofWorkflowTests : IClassFixture<WebApplicationFactory<Program>>
    {
        private readonly WebApplicationFactory<Program> _factory;
        private readonly HttpClient _client;

        public PaymentProofWorkflowTests(WebApplicationFactory<Program> factory)
        {
            _factory = factory.WithWebHostBuilder(builder =>
            {
                builder.UseEnvironment("Testing");
                builder.ConfigureServices(services =>
                {
                    // Override services for testing
                    // Add in-memory database, mock external services, etc.
                });
            });

            _client = _factory.CreateClient();
            
            // Set up authentication headers for testing
            _client.DefaultRequestHeaders.Add("Authorization", "Bearer test-admin-token");
        }

        [Fact]
        public async Task PaymentProofWorkflow_CompleteFlow_ShouldWorkCorrectly()
        {
            // Arrange
            var subscriptionId = Guid.NewGuid();
            var userId = Guid.NewGuid();

            // Step 1: Upload payment proof
            var uploadRequest = new UploadPaymentProofRequest
            {
                Amount = 1000,
                Currency = "INR",
                PaymentDate = DateTime.UtcNow.AddDays(-1),
                Notes = "Bank transfer payment",
                PaymentMethod = "Bank Transfer",
                TransactionReference = "TXN123456"
            };

            // Create a mock file for upload
            var fileContent = Encoding.UTF8.GetBytes("Mock image content");
            using var formContent = new MultipartFormDataContent();
            formContent.Add(new StringContent(uploadRequest.Amount.ToString()), "Amount");
            formContent.Add(new StringContent(uploadRequest.Currency), "Currency");
            formContent.Add(new StringContent(uploadRequest.PaymentDate.ToString("O")), "PaymentDate");
            formContent.Add(new StringContent(uploadRequest.Notes ?? ""), "Notes");
            formContent.Add(new StringContent(uploadRequest.PaymentMethod ?? ""), "PaymentMethod");
            formContent.Add(new StringContent(uploadRequest.TransactionReference ?? ""), "TransactionReference");
            formContent.Add(new ByteArrayContent(fileContent), "ProofImage", "proof.jpg");

            // Act & Assert - Step 1: Upload payment proof
            var uploadResponse = await _client.PostAsync($"/api/subscriptions/{subscriptionId}/payment-proof", formContent);
            uploadResponse.StatusCode.Should().Be(HttpStatusCode.Created);

            var uploadResult = await uploadResponse.Content.ReadFromJsonAsync<UploadPaymentProofResponse>();
            uploadResult.Should().NotBeNull();
            uploadResult!.Success.Should().BeTrue();
            uploadResult.Status.Should().Be(PaymentProofStatus.Pending);

            var paymentProofId = uploadResult.PaymentProofId;

            // Step 2: Get pending payment proofs (admin view)
            var pendingResponse = await _client.GetAsync("/api/admin/payment-proofs/pending");
            pendingResponse.StatusCode.Should().Be(HttpStatusCode.OK);

            var pendingProofs = await pendingResponse.Content.ReadFromJsonAsync<PaymentProofListDto>();
            pendingProofs.Should().NotBeNull();
            pendingProofs!.Items.Should().Contain(p => p.Id == paymentProofId);

            // Step 3: Verify payment proof (admin action)
            var verifyRequest = new VerifyPaymentProofRequest
            {
                VerificationNotes = "Payment verified successfully"
            };

            var verifyResponse = await _client.PutAsJsonAsync($"/api/admin/payment-proofs/{paymentProofId}/verify", verifyRequest);
            verifyResponse.StatusCode.Should().Be(HttpStatusCode.OK);

            // Step 4: Verify the payment proof status has changed
            var statusResponse = await _client.GetAsync($"/api/admin/payment-proofs?subscriptionId={subscriptionId}");
            statusResponse.StatusCode.Should().Be(HttpStatusCode.OK);

            var statusResult = await statusResponse.Content.ReadFromJsonAsync<PaymentProofListDto>();
            statusResult.Should().NotBeNull();
            
            var verifiedProof = statusResult!.Items.FirstOrDefault(p => p.Id == paymentProofId);
            verifiedProof.Should().NotBeNull();
            verifiedProof!.Status.Should().Be(PaymentProofStatus.Verified);
            verifiedProof.VerificationNotes.Should().Be("Payment verified successfully");
        }

        [Fact]
        public async Task PaymentProofWorkflow_RejectFlow_ShouldWorkCorrectly()
        {
            // Arrange
            var subscriptionId = Guid.NewGuid();
            var paymentProofId = await CreateTestPaymentProof(subscriptionId);

            // Act - Reject payment proof
            var rejectRequest = new RejectPaymentProofRequest
            {
                RejectionReason = "Invalid payment proof - unclear image",
                VerificationNotes = "Image quality is too poor to verify"
            };

            var rejectResponse = await _client.PutAsJsonAsync($"/api/admin/payment-proofs/{paymentProofId}/reject", rejectRequest);

            // Assert
            rejectResponse.StatusCode.Should().Be(HttpStatusCode.OK);

            // Verify the payment proof status
            var statusResponse = await _client.GetAsync($"/api/admin/payment-proofs?subscriptionId={subscriptionId}");
            var statusResult = await statusResponse.Content.ReadFromJsonAsync<PaymentProofListDto>();
            
            var rejectedProof = statusResult!.Items.FirstOrDefault(p => p.Id == paymentProofId);
            rejectedProof.Should().NotBeNull();
            rejectedProof!.Status.Should().Be(PaymentProofStatus.Rejected);
            rejectedProof.RejectionReason.Should().Be("Invalid payment proof - unclear image");
        }

        [Fact]
        public async Task PaymentProofUpload_WithInvalidFile_ShouldReturnBadRequest()
        {
            // Arrange
            var subscriptionId = Guid.NewGuid();
            
            // Create invalid file content (too large or wrong type)
            var invalidFileContent = new byte[15 * 1024 * 1024]; // 15MB file (exceeds limit)
            
            using var formContent = new MultipartFormDataContent();
            formContent.Add(new StringContent("1000"), "Amount");
            formContent.Add(new StringContent("INR"), "Currency");
            formContent.Add(new StringContent(DateTime.UtcNow.AddDays(-1).ToString("O")), "PaymentDate");
            formContent.Add(new ByteArrayContent(invalidFileContent), "ProofImage", "large-file.jpg");

            // Act
            var response = await _client.PostAsync($"/api/subscriptions/{subscriptionId}/payment-proof", formContent);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        }

        [Fact]
        public async Task PaymentProofVerification_WithoutAdminRole_ShouldReturnForbidden()
        {
            // Arrange
            var paymentProofId = Guid.NewGuid();
            var clientWithoutAdminRole = _factory.CreateClient();
            clientWithoutAdminRole.DefaultRequestHeaders.Add("Authorization", "Bearer test-user-token");

            var verifyRequest = new VerifyPaymentProofRequest
            {
                VerificationNotes = "Attempting to verify without admin role"
            };

            // Act
            var response = await clientWithoutAdminRole.PutAsJsonAsync($"/api/admin/payment-proofs/{paymentProofId}/verify", verifyRequest);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.Forbidden);
        }

        [Fact]
        public async Task PaymentProofList_WithFiltering_ShouldReturnFilteredResults()
        {
            // Arrange
            var subscriptionId1 = Guid.NewGuid();
            var subscriptionId2 = Guid.NewGuid();
            
            await CreateTestPaymentProof(subscriptionId1);
            await CreateTestPaymentProof(subscriptionId2);

            // Act - Get payment proofs for specific subscription
            var response = await _client.GetAsync($"/api/admin/payment-proofs?subscriptionId={subscriptionId1}&pageSize=10");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var result = await response.Content.ReadFromJsonAsync<PaymentProofListDto>();
            result.Should().NotBeNull();
            result!.Items.Should().OnlyContain(p => p.SubscriptionId == subscriptionId1);
        }

        [Fact]
        public async Task PaymentProofAnalytics_ShouldReturnCorrectStatistics()
        {
            // Arrange - Create some test payment proofs with different statuses
            var subscriptionId = Guid.NewGuid();
            var paymentProofId1 = await CreateTestPaymentProof(subscriptionId);
            var paymentProofId2 = await CreateTestPaymentProof(subscriptionId);

            // Verify one and reject another
            await _client.PutAsJsonAsync($"/api/admin/payment-proofs/{paymentProofId1}/verify", 
                new VerifyPaymentProofRequest { VerificationNotes = "Verified" });
            
            await _client.PutAsJsonAsync($"/api/admin/payment-proofs/{paymentProofId2}/reject", 
                new RejectPaymentProofRequest { RejectionReason = "Rejected" });

            // Act
            var response = await _client.GetAsync("/api/admin/dashboard/overview");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var dashboard = await response.Content.ReadFromJsonAsync<DashboardOverviewDto>();
            dashboard.Should().NotBeNull();
            dashboard!.PaymentProofs.Verified.Should().BeGreaterThan(0);
            dashboard.PaymentProofs.Rejected.Should().BeGreaterThan(0);
        }

        private async Task<Guid> CreateTestPaymentProof(Guid subscriptionId)
        {
            var uploadRequest = new UploadPaymentProofRequest
            {
                Amount = 1000,
                Currency = "INR",
                PaymentDate = DateTime.UtcNow.AddDays(-1),
                Notes = "Test payment proof",
                PaymentMethod = "Bank Transfer"
            };

            var fileContent = Encoding.UTF8.GetBytes("Mock image content");
            using var formContent = new MultipartFormDataContent();
            formContent.Add(new StringContent(uploadRequest.Amount.ToString()), "Amount");
            formContent.Add(new StringContent(uploadRequest.Currency), "Currency");
            formContent.Add(new StringContent(uploadRequest.PaymentDate.ToString("O")), "PaymentDate");
            formContent.Add(new StringContent(uploadRequest.Notes ?? ""), "Notes");
            formContent.Add(new StringContent(uploadRequest.PaymentMethod ?? ""), "PaymentMethod");
            formContent.Add(new ByteArrayContent(fileContent), "ProofImage", "test-proof.jpg");

            var response = await _client.PostAsync($"/api/subscriptions/{subscriptionId}/payment-proof", formContent);
            var result = await response.Content.ReadFromJsonAsync<UploadPaymentProofResponse>();
            
            return result!.PaymentProofId;
        }
    }
}
