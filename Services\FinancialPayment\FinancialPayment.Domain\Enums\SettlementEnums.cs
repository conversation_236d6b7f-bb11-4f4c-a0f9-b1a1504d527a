namespace FinancialPayment.Domain.Enums;

public enum SettlementStatus
{
    Created = 1,
    Processing = 2,
    Completed = 3,
    Failed = 4,
    Cancelled = 5
}

public enum DistributionType
{
    CarrierPayment = 1,
    BrokerCommission = 2,
    PlatformFee = 3,
    TaxWithholding = 4,
    Refund = 5,
    Adjustment = 6
}

public enum DistributionStatus
{
    Pending = 1,
    Processing = 2,
    Completed = 3,
    Failed = 4,
    Cancelled = 5
}

public enum ParticipantRole
{
    TransportCompany = 1,
    Broker = 2,
    Carrier = 3,
    Shipper = 4,
    Admin = 5,
    Platform = 6
}
