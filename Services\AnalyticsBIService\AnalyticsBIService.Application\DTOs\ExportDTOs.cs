using AnalyticsBIService.Domain.Enums;

namespace AnalyticsBIService.Application.DTOs
{
    /// <summary>
    /// Export record for tracking export operations
    /// </summary>
    public class ExportRecord
    {
        public Guid Id { get; set; }
        public ExportType ExportType { get; set; }
        public ExportFormat Format { get; set; }
        public Guid RequestedBy { get; set; }
        public DateTime RequestedAt { get; set; }
        public AnalyticsBIService.Domain.Enums.ExportStatus Status { get; set; }
        public DateTime? CompletedAt { get; set; }
        public string? FilePath { get; set; }
        public string? FileName { get; set; }
        public int RecordCount { get; set; }
        public long FileSizeBytes { get; set; }
        public string? ErrorMessage { get; set; }
        public Dictionary<string, object> Parameters { get; set; } = new();
    }

    /// <summary>
    /// Filter for order data export
    /// </summary>
    public class OrderDataFilter
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string? Status { get; set; }
        public Guid? TransportCompanyId { get; set; }
        public Guid? BrokerId { get; set; }
        public Guid? CarrierId { get; set; }
        public bool IncludeTimeline { get; set; } = false;
        public bool IncludeDocuments { get; set; } = false;
        public bool IncludeFinancials { get; set; } = false;
    }

    /// <summary>
    /// Filter for trip data export
    /// </summary>
    public class TripDataFilter
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string? Status { get; set; }
        public Guid? CarrierId { get; set; }
        public Guid? DriverId { get; set; }
        public Guid? VehicleId { get; set; }
        public bool IncludeLocationHistory { get; set; } = false;
        public bool IncludePerformanceMetrics { get; set; } = false;
    }



    /// <summary>
    /// Data export request DTO
    /// </summary>
    public class DataExportRequestDto
    {
        public Guid UserId { get; set; }
        public string DataType { get; set; } = string.Empty;
        public ExportFormat Format { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public Dictionary<string, object> Filters { get; set; } = new();
        public List<string> SelectedFields { get; set; } = new();
        public List<string> SelectedColumns { get; set; } = new();
        public string FileName { get; set; } = string.Empty;
        public string? GroupBy { get; set; }
        public string? Sorting { get; set; }
        public int? MaxRecords { get; set; }
        public bool IncludeHeaders { get; set; } = true;
        public bool IncludeMetadata { get; set; } = false;
        public string? CustomQuery { get; set; }
        public Dictionary<string, object> Parameters { get; set; } = new();
        public List<string> UserTypes { get; set; } = new();
        public bool IncludeDetails { get; set; } = false;
    }

    /// <summary>
    /// Export request DTO for usage logs
    /// </summary>
    public class ExportRequestDto
    {
        public Guid Id { get; set; }
        public Guid UserId { get; set; }
        public string DataSource { get; set; } = string.Empty;
        public string ExportType { get; set; } = string.Empty;
        public ExportFormat Format { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public Dictionary<string, object> Filters { get; set; } = new();
        public Dictionary<string, object> Parameters { get; set; } = new();
        public string Status { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public string? FilePath { get; set; }
        public DateTime? CompletedAt { get; set; }
        public DateTime RequestedAt { get; set; }
        public string? ErrorMessage { get; set; }
    }
}
