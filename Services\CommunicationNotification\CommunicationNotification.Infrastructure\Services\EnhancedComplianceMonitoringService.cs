using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Shared.Messaging;
using CommunicationNotification.Application.Commands.Alerts;

namespace CommunicationNotification.Infrastructure.Services;

public class EnhancedComplianceMonitoringService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<EnhancedComplianceMonitoringService> _logger;
    private readonly ComplianceMonitoringSettings _settings;
    private readonly TimeSpan _monitoringInterval = TimeSpan.FromHours(6); // Run every 6 hours

    public EnhancedComplianceMonitoringService(
        IServiceProvider serviceProvider,
        ILogger<EnhancedComplianceMonitoringService> logger,
        IOptions<ComplianceMonitoringSettings> settings)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _settings = settings.Value;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Enhanced Compliance Monitoring Service started");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await RunEnhancedComplianceChecksAsync(stoppingToken);
                await Task.Delay(_monitoringInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in enhanced compliance monitoring cycle");
                await Task.Delay(TimeSpan.FromMinutes(30), stoppingToken); // Wait 30 minutes on error
            }
        }

        _logger.LogInformation("Enhanced Compliance Monitoring Service stopped");
    }

    private async Task RunEnhancedComplianceChecksAsync(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var messageBroker = scope.ServiceProvider.GetRequiredService<IMessageBroker>();

        _logger.LogDebug("Starting enhanced compliance monitoring cycle");

        // Monitor document expiry across all services
        await MonitorDocumentExpiryAsync(messageBroker, cancellationToken);

        // Monitor subscription compliance
        await MonitorSubscriptionComplianceAsync(messageBroker, cancellationToken);

        // Monitor KYC compliance
        await MonitorKycComplianceAsync(messageBroker, cancellationToken);

        // Monitor vehicle and driver compliance
        await MonitorFleetComplianceAsync(messageBroker, cancellationToken);

        // Generate compliance reports
        await GenerateComplianceReportsAsync(messageBroker, cancellationToken);

        _logger.LogDebug("Enhanced compliance monitoring cycle completed");
    }

    private async Task MonitorDocumentExpiryAsync(IMessageBroker messageBroker, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Monitoring document expiry across all services");

            // Request document expiry data from all services
            var services = new[] { "UserManagement", "NetworkFleetManagement", "TripManagement" };
            var alertThresholds = new[] { 30, 15, 7, 1, 0 }; // Days before expiry

            foreach (var service in services)
            {
                foreach (var threshold in alertThresholds)
                {
                    await messageBroker.PublishAsync($"{service.ToLower()}.documents.check_expiry", new
                    {
                        ThresholdDays = threshold,
                        RequestId = Guid.NewGuid(),
                        RequestedAt = DateTime.UtcNow,
                        AlertLevel = GetAlertLevel(threshold)
                    }, cancellationToken);
                }
            }

            _logger.LogDebug("Document expiry monitoring requests sent to all services");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error monitoring document expiry");
        }
    }

    private async Task MonitorSubscriptionComplianceAsync(IMessageBroker messageBroker, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Monitoring subscription compliance");

            await messageBroker.PublishAsync("subscriptionmanagement.compliance.check", new
            {
                CheckType = "ExpiryAndPayment",
                RequestId = Guid.NewGuid(),
                RequestedAt = DateTime.UtcNow,
                IncludeGracePeriod = true,
                AlertThresholds = new[] { 30, 15, 7, 3, 1, 0 }
            }, cancellationToken);

            _logger.LogDebug("Subscription compliance monitoring request sent");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error monitoring subscription compliance");
        }
    }

    private async Task MonitorKycComplianceAsync(IMessageBroker messageBroker, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Monitoring KYC compliance");

            await messageBroker.PublishAsync("usermanagement.kyc.compliance_check", new
            {
                CheckType = "CompletionAndExpiry",
                RequestId = Guid.NewGuid(),
                RequestedAt = DateTime.UtcNow,
                IncludeIncompleteKyc = true,
                IncludeExpiredDocuments = true,
                AlertThresholds = new[] { 30, 15, 7, 0 }
            }, cancellationToken);

            _logger.LogDebug("KYC compliance monitoring request sent");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error monitoring KYC compliance");
        }
    }

    private async Task MonitorFleetComplianceAsync(IMessageBroker messageBroker, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Monitoring fleet compliance");

            await messageBroker.PublishAsync("networkfleetmanagement.compliance.check", new
            {
                CheckType = "VehicleAndDriverCompliance",
                RequestId = Guid.NewGuid(),
                RequestedAt = DateTime.UtcNow,
                IncludeVehicleDocuments = true,
                IncludeDriverDocuments = true,
                IncludeInsurance = true,
                IncludeFitnessCertificates = true,
                AlertThresholds = new[] { 30, 15, 7, 3, 1, 0 }
            }, cancellationToken);

            _logger.LogDebug("Fleet compliance monitoring request sent");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error monitoring fleet compliance");
        }
    }

    private async Task GenerateComplianceReportsAsync(IMessageBroker messageBroker, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Generating compliance reports");

            // Generate daily compliance summary
            await messageBroker.PublishAsync("compliance.report.generate", new
            {
                ReportType = "DailyComplianceSummary",
                RequestId = Guid.NewGuid(),
                RequestedAt = DateTime.UtcNow,
                IncludeMetrics = true,
                IncludeAlerts = true,
                IncludeTrends = true,
                Recipients = _settings.ComplianceReportRecipients
            }, cancellationToken);

            _logger.LogDebug("Compliance report generation request sent");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating compliance reports");
        }
    }

    private string GetAlertLevel(int daysUntilExpiry)
    {
        return daysUntilExpiry switch
        {
            0 => "Critical",
            <= 1 => "High",
            <= 7 => "Medium",
            <= 15 => "Low",
            _ => "Info"
        };
    }
}

public class ComplianceMonitoringSettings
{
    public bool EnableDocumentExpiryMonitoring { get; set; } = true;
    public bool EnableSubscriptionMonitoring { get; set; } = true;
    public bool EnableKycMonitoring { get; set; } = true;
    public bool EnableFleetMonitoring { get; set; } = true;
    public bool EnableAutomaticReports { get; set; } = true;
    public List<string> ComplianceReportRecipients { get; set; } = new();
    public int MonitoringIntervalHours { get; set; } = 6;
    public List<int> DefaultAlertThresholds { get; set; } = new() { 30, 15, 7, 1, 0 };
}
