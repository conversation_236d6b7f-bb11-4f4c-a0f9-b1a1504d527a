using MediatR;
using Microsoft.Extensions.Logging;
using NetworkFleetManagement.Domain.Repositories;

namespace NetworkFleetManagement.Application.Commands.PreferredPartners;

public class ActivatePreferredPartnerCommandHandler : IRequestHandler<ActivatePreferredPartnerCommand, bool>
{
    private readonly IPreferredPartnerRepository _preferredPartnerRepository;
    private readonly ILogger<ActivatePreferredPartnerCommandHandler> _logger;

    public ActivatePreferredPartnerCommandHandler(
        IPreferredPartnerRepository preferredPartnerRepository,
        ILogger<ActivatePreferredPartnerCommandHandler> logger)
    {
        _preferredPartnerRepository = preferredPartnerRepository;
        _logger = logger;
    }

    public async Task<bool> Handle(ActivatePreferredPartnerCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var partner = await _preferredPartnerRepository.GetByIdAsync(request.Id, cancellationToken);
            
            if (partner == null || partner.UserId != request.UserId)
            {
                _logger.LogWarning("Preferred partner {Id} not found or user {UserId} doesn't have permission to activate it", 
                    request.Id, request.UserId);
                return false;
            }

            partner.Activate();
            await _preferredPartnerRepository.UpdateAsync(partner, cancellationToken);

            _logger.LogInformation("Preferred partner {Id} activated by user {UserId}", 
                request.Id, request.UserId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error activating preferred partner {Id}", request.Id);
            throw;
        }
    }
}
