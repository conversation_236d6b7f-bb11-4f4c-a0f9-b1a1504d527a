using NetworkFleetManagement.Domain.Entities;
using NetworkFleetManagement.Domain.Enums;

namespace NetworkFleetManagement.Domain.Repositories;

public interface IDocumentRepository
{
    // Carrier Documents
    Task<CarrierDocument?> GetCarrierDocumentByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<IEnumerable<CarrierDocument>> GetCarrierDocumentsByCarrierIdAsync(Guid carrierId, CancellationToken cancellationToken = default);
    Task<IEnumerable<CarrierDocument>> GetExpiringCarrierDocumentsAsync(Guid carrierId, int daysThreshold = 30, CancellationToken cancellationToken = default);
    
    // Vehicle Documents
    Task<VehicleDocument?> GetVehicleDocumentByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<IEnumerable<VehicleDocument>> GetVehicleDocumentsByVehicleIdAsync(Guid vehicleId, CancellationToken cancellationToken = default);
    Task<IEnumerable<VehicleDocument>> GetExpiringVehicleDocumentsAsync(Guid carrierId, int daysThreshold = 30, CancellationToken cancellationToken = default);
    
    // Driver Documents
    Task<DriverDocument?> GetDriverDocumentByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<IEnumerable<DriverDocument>> GetDriverDocumentsByDriverIdAsync(Guid driverId, CancellationToken cancellationToken = default);
    Task<IEnumerable<DriverDocument>> GetExpiringDriverDocumentsAsync(Guid carrierId, int daysThreshold = 30, CancellationToken cancellationToken = default);
    
    // Document Status Summary
    Task<DocumentStatusSummary> GetDocumentStatusSummaryAsync(Guid carrierId, int expiryThresholdDays = 30, CancellationToken cancellationToken = default);
    
    // Expiring Documents Query
    Task<(IEnumerable<ExpiringDocumentInfo> Documents, int TotalCount)> GetExpiringDocumentsPagedAsync(
        Guid carrierId,
        int pageNumber,
        int pageSize,
        int expiryThresholdDays = 30,
        string? entityType = null,
        string? documentType = null,
        bool? isExpired = null,
        CancellationToken cancellationToken = default);
    
    // CRUD Operations
    Task AddCarrierDocumentAsync(CarrierDocument document, CancellationToken cancellationToken = default);
    Task AddVehicleDocumentAsync(VehicleDocument document, CancellationToken cancellationToken = default);
    Task AddDriverDocumentAsync(DriverDocument document, CancellationToken cancellationToken = default);
    
    void UpdateCarrierDocument(CarrierDocument document);
    void UpdateVehicleDocument(VehicleDocument document);
    void UpdateDriverDocument(DriverDocument document);
    
    void RemoveCarrierDocument(CarrierDocument document);
    void RemoveVehicleDocument(VehicleDocument document);
    void RemoveDriverDocument(DriverDocument document);
}

// Supporting DTOs for the repository
public class DocumentStatusSummary
{
    public int TotalDocuments { get; set; }
    public int VerifiedDocuments { get; set; }
    public int PendingDocuments { get; set; }
    public int ExpiredDocuments { get; set; }
    public int ExpiringSoonDocuments { get; set; }
    public int RejectedDocuments { get; set; }
    
    public DocumentTypeSummary CarrierDocuments { get; set; } = new();
    public DocumentTypeSummary VehicleDocuments { get; set; } = new();
    public DocumentTypeSummary DriverDocuments { get; set; } = new();
}

public class DocumentTypeSummary
{
    public int Total { get; set; }
    public int Verified { get; set; }
    public int Pending { get; set; }
    public int Expired { get; set; }
    public int ExpiringSoon { get; set; }
    public int Rejected { get; set; }
}

public class ExpiringDocumentInfo
{
    public Guid DocumentId { get; set; }
    public string EntityType { get; set; } = string.Empty; // "Carrier", "Vehicle", "Driver"
    public Guid EntityId { get; set; }
    public string EntityName { get; set; } = string.Empty;
    public DocumentType DocumentType { get; set; }
    public string DocumentName { get; set; } = string.Empty;
    public DateTime? ExpiryDate { get; set; }
    public int DaysUntilExpiry { get; set; }
    public bool IsExpired { get; set; }
    public bool IsVerified { get; set; }
    public string? RejectionReason { get; set; }
}
