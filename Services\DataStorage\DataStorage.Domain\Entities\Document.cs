using Shared.Domain.Common;
using DataStorage.Domain.Enums;
using DataStorage.Domain.ValueObjects;
using DataStorage.Domain.Events;

namespace DataStorage.Domain.Entities;

public class Document : AggregateRoot
{
    public string Title { get; private set; }
    public string? Description { get; private set; }
    public DocumentType DocumentType { get; private set; }
    public DocumentCategory Category { get; private set; }
    public DocumentStatus Status { get; private set; }
    public AccessLevel AccessLevel { get; private set; }
    
    // File Information
    public FileMetadata FileMetadata { get; private set; }
    public StorageLocation StorageLocation { get; private set; }
    
    // Ownership and Access
    public Guid OwnerId { get; private set; }
    public string OwnerType { get; private set; } // User type: Admin, Shipper, Carrier, etc.
    
    // Versioning
    public int Version { get; private set; }
    public Guid? ParentDocumentId { get; private set; }
    public bool IsLatestVersion { get; private set; }
    
    // Timestamps
    public DateTime CreatedAt { get; private set; }
    public DateTime? UpdatedAt { get; private set; }
    public DateTime? ExpiresAt { get; private set; }
    
    // Metadata
    public Dictionary<string, string> Tags { get; private set; }
    public string? ExternalReference { get; private set; }
    
    // Collections
    private readonly List<DocumentVersion> _versions = new();
    private readonly List<DocumentAccess> _accessGrants = new();
    
    public IReadOnlyCollection<DocumentVersion> Versions => _versions.AsReadOnly();
    public IReadOnlyCollection<DocumentAccess> AccessGrants => _accessGrants.AsReadOnly();

    private Document()
    {
        Title = string.Empty;
        OwnerType = string.Empty;
        FileMetadata = null!;
        StorageLocation = null!;
        Tags = new Dictionary<string, string>();
    }

    public Document(
        string title,
        string? description,
        DocumentType documentType,
        DocumentCategory category,
        FileMetadata fileMetadata,
        StorageLocation storageLocation,
        Guid ownerId,
        string ownerType,
        AccessLevel accessLevel = AccessLevel.Private,
        DateTime? expiresAt = null,
        Dictionary<string, string>? tags = null,
        string? externalReference = null)
    {
        if (string.IsNullOrWhiteSpace(title))
            throw new ArgumentException("Title cannot be empty", nameof(title));

        if (string.IsNullOrWhiteSpace(ownerType))
            throw new ArgumentException("Owner type cannot be empty", nameof(ownerType));

        Id = Guid.NewGuid();
        Title = title;
        Description = description;
        DocumentType = documentType;
        Category = category;
        Status = DocumentStatus.Uploaded;
        AccessLevel = accessLevel;
        FileMetadata = fileMetadata ?? throw new ArgumentNullException(nameof(fileMetadata));
        StorageLocation = storageLocation ?? throw new ArgumentNullException(nameof(storageLocation));
        OwnerId = ownerId;
        OwnerType = ownerType;
        Version = 1;
        IsLatestVersion = true;
        CreatedAt = DateTime.UtcNow;
        ExpiresAt = expiresAt;
        Tags = tags ?? new Dictionary<string, string>();
        ExternalReference = externalReference;

        // Add initial version
        var initialVersion = new DocumentVersion(Id, 1, fileMetadata, storageLocation, "Initial upload");
        _versions.Add(initialVersion);

        // Raise domain event
        AddDomainEvent(new DocumentUploadedEvent(Id, Title, DocumentType, OwnerId, OwnerType));
    }

    public void UpdateMetadata(string title, string? description, Dictionary<string, string>? tags = null)
    {
        if (string.IsNullOrWhiteSpace(title))
            throw new ArgumentException("Title cannot be empty", nameof(title));

        Title = title;
        Description = description;
        
        if (tags != null)
        {
            Tags = new Dictionary<string, string>(tags);
        }
        
        UpdatedAt = DateTime.UtcNow;
        
        AddDomainEvent(new DocumentUpdatedEvent(Id, Title, OwnerId));
    }

    public void ChangeStatus(DocumentStatus newStatus, string? reason = null)
    {
        if (Status == newStatus)
            return;

        var previousStatus = Status;
        Status = newStatus;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new DocumentStatusChangedEvent(Id, previousStatus, newStatus, reason));
    }

    public void GrantAccess(Guid userId, string userType, AccessLevel accessLevel, DateTime? expiresAt = null)
    {
        var existingAccess = _accessGrants.FirstOrDefault(a => a.UserId == userId);
        if (existingAccess != null)
        {
            existingAccess.UpdateAccess(accessLevel, expiresAt);
        }
        else
        {
            var access = new DocumentAccess(Id, userId, userType, accessLevel, expiresAt);
            _accessGrants.Add(access);
        }

        AddDomainEvent(new DocumentAccessGrantedEvent(Id, userId, userType, accessLevel));
    }

    public void RevokeAccess(Guid userId)
    {
        var access = _accessGrants.FirstOrDefault(a => a.UserId == userId);
        if (access != null)
        {
            _accessGrants.Remove(access);
            AddDomainEvent(new DocumentAccessRevokedEvent(Id, userId));
        }
    }

    public bool HasAccess(Guid userId, AccessLevel requiredLevel)
    {
        if (OwnerId == userId)
            return true;

        var access = _accessGrants.FirstOrDefault(a => a.UserId == userId && a.IsActive);
        return access != null && access.AccessLevel >= requiredLevel;
    }

    public void Archive()
    {
        if (Status == DocumentStatus.Archived)
            return;

        Status = DocumentStatus.Archived;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new DocumentArchivedEvent(Id, Title, OwnerId));
    }

    public void Delete()
    {
        Status = DocumentStatus.Deleted;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new DocumentDeletedEvent(Id, Title, OwnerId));
    }
}
