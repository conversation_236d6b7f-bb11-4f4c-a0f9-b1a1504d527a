
using Shared.Domain.Common;
using MobileWorkflow.Domain.Events;

namespace MobileWorkflow.Domain.Entities;

public class MilestoneStep : AggregateRoot
{
    public Guid MilestoneTemplateId { get; private set; }
    public string Name { get; private set; }
    public string Description { get; private set; }
    public int SequenceNumber { get; private set; }
    public bool IsRequired { get; private set; }
    public bool IsActive { get; private set; }
    public string? TriggerCondition { get; private set; }
    public Dictionary<string, object> Configuration { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    public virtual MilestoneTemplate MilestoneTemplate { get; private set; } = null!;
    public virtual ICollection<MilestonePayoutRule> PayoutRules { get; private set; } = new List<MilestonePayoutRule>();

    private MilestoneStep() { } // EF Core

    public MilestoneStep(
        Guid milestoneTemplateId,
        string name,
        string description,
        int sequenceNumber,
        bool isRequired = true)
    {
        MilestoneTemplateId = milestoneTemplateId;
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        SequenceNumber = sequenceNumber;
        IsRequired = isRequired;

        IsActive = true;
        Configuration = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();

        AddDomainEvent(new MilestoneStepCreatedEvent(Id, MilestoneTemplateId, Name, SequenceNumber));
    }

    public void UpdateDetails(string name, string description)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));

        AddDomainEvent(new MilestoneStepUpdatedEvent(Id, MilestoneTemplateId, Name));
    }

    public void UpdateSequenceNumber(int sequenceNumber)
    {
        var oldSequence = SequenceNumber;
        SequenceNumber = sequenceNumber;

        AddDomainEvent(new MilestoneStepSequenceUpdatedEvent(Id, MilestoneTemplateId, Name, oldSequence, sequenceNumber));
    }

    public void SetRequired(bool isRequired)
    {
        IsRequired = isRequired;

        AddDomainEvent(new MilestoneStepRequiredStatusChangedEvent(Id, MilestoneTemplateId, Name, IsRequired));
    }

    public void Activate()
    {
        IsActive = true;

        AddDomainEvent(new MilestoneStepActivatedEvent(Id, MilestoneTemplateId, Name));
    }

    public void Deactivate()
    {
        IsActive = false;

        AddDomainEvent(new MilestoneStepDeactivatedEvent(Id, MilestoneTemplateId, Name));
    }

    public void SetTriggerCondition(string? triggerCondition)
    {
        TriggerCondition = triggerCondition;

        AddDomainEvent(new MilestoneStepTriggerConditionSetEvent(Id, MilestoneTemplateId, Name, TriggerCondition));
    }

    public MilestonePayoutRule AddPayoutRule(decimal payoutPercentage, string? condition = null)
    {
        if (payoutPercentage < 0 || payoutPercentage > 100)
            throw new ArgumentException("Payout percentage must be between 0 and 100", nameof(payoutPercentage));

        var payoutRule = new MilestonePayoutRule(Id, payoutPercentage, condition);
        PayoutRules.Add(payoutRule);

        AddDomainEvent(new MilestoneStepPayoutRuleAddedEvent(Id, MilestoneTemplateId, Name, payoutPercentage));

        return payoutRule;
    }

    public void RemovePayoutRule(Guid payoutRuleId)
    {
        var rule = PayoutRules.FirstOrDefault(r => r.Id == payoutRuleId);
        if (rule == null)
            throw new InvalidOperationException($"Payout rule with ID {payoutRuleId} not found");

        PayoutRules.Remove(rule);

        AddDomainEvent(new MilestoneStepPayoutRuleRemovedEvent(Id, MilestoneTemplateId, Name, rule.PayoutPercentage));
    }

    public void UpdateConfiguration(Dictionary<string, object> configuration)
    {
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));

        AddDomainEvent(new MilestoneStepConfigurationUpdatedEvent(Id, MilestoneTemplateId, Name));
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public decimal GetTotalPayoutPercentage()
    {
        return PayoutRules.Sum(r => r.PayoutPercentage);
    }

    public bool ValidatePayoutRules()
    {
        var totalPercentage = GetTotalPayoutPercentage();
        return totalPercentage >= 0 && totalPercentage <= 100;
    }

    public List<string> GetValidationErrors()
    {
        var errors = new List<string>();

        if (string.IsNullOrWhiteSpace(Name))
        {
            errors.Add("Step name is required");
        }

        if (SequenceNumber <= 0)
        {
            errors.Add("Sequence number must be greater than 0");
        }

        if (!ValidatePayoutRules())
        {
            errors.Add("Total payout percentage for step cannot exceed 100%");
        }

        return errors;
    }

    public bool CanBeDeleted()
    {
        // Can be deleted if it's not required or if the template is not in use
        return !IsRequired;
    }
}



