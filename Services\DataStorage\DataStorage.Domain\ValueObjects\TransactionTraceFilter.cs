using Shared.Domain.ValueObjects;

namespace DataStorage.Domain.ValueObjects;

/// <summary>
/// Filter for transaction trace queries
/// </summary>
public class TransactionTraceFilter : ValueObject
{
    public string? TransactionId { get; init; }
    public string? TransactionName { get; init; }
    public DateTime? FromDate { get; init; }
    public DateTime? ToDate { get; init; }
    public string? UserId { get; init; }
    public string? Status { get; init; }
    public double? MinDuration { get; init; }
    public double? MaxDuration { get; init; }
    public int? Skip { get; init; }
    public int? Take { get; init; }

    public TransactionTraceFilter()
    {
    }

    public TransactionTraceFilter(
        string? transactionId = null,
        string? transactionName = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        string? userId = null,
        string? status = null,
        double? minDuration = null,
        double? maxDuration = null,
        int? skip = null,
        int? take = null)
    {
        TransactionId = transactionId;
        TransactionName = transactionName;
        FromDate = fromDate;
        ToDate = toDate;
        UserId = userId;
        Status = status;
        MinDuration = minDuration;
        MaxDuration = maxDuration;
        Skip = skip;
        Take = take;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return TransactionId ?? string.Empty;
        yield return TransactionName ?? string.Empty;
        yield return FromDate ?? DateTime.MinValue;
        yield return ToDate ?? DateTime.MinValue;
        yield return UserId ?? string.Empty;
        yield return Status ?? string.Empty;
        yield return MinDuration ?? 0.0;
        yield return MaxDuration ?? 0.0;
        yield return Skip ?? 0;
        yield return Take ?? 0;
    }
}
