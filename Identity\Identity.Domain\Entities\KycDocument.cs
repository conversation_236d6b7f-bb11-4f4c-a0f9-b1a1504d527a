using System;
using Identity.Domain.Common;
using Identity.Domain.Exceptions;

namespace Identity.Domain.Entities
{
    public enum DocumentType
    {
        // Company Documents
        GstCertificate,
        TradeLicense,
        PanCard,
        
        // Individual Documents
        AadharCard,
        DrivingLicense,
        
        // Vehicle Documents
        VehicleRegistration,
        VehicleInsurance,
        
        // Profile Photo
        ProfilePhoto
    }

    public enum DocumentStatus
    {
        Uploaded,
        UnderReview,
        Approved,
        Rejected,
        RequiresResubmission
    }

    public enum VerificationMethod
    {
        Manual,
        AI_OCR,
        Hybrid
    }

    public class KycDocument : Entity
    {
        public Guid UserId { get; private set; }
        public DocumentType DocumentType { get; private set; }
        public string FileName { get; private set; }
        public string FilePath { get; private set; }
        public string FileSize { get; private set; }
        public string MimeType { get; private set; }
        public DocumentStatus Status { get; private set; }
        public VerificationMethod VerificationMethod { get; private set; }
        public string ExtractedData { get; private set; }
        public decimal ConfidenceScore { get; private set; }
        public string ReviewNotes { get; private set; }
        public string RejectionReason { get; private set; }
        public Guid? ReviewedBy { get; private set; }
        public DateTime? ReviewedAt { get; private set; }
        public DateTime UploadedAt { get; private set; }

        private KycDocument() { }

        public KycDocument(
            Guid userId,
            DocumentType documentType,
            string fileName,
            string filePath,
            string fileSize,
            string mimeType)
        {
            if (userId == Guid.Empty)
                throw new DomainException("User ID cannot be empty");

            if (string.IsNullOrWhiteSpace(fileName))
                throw new DomainException("File name cannot be empty");

            if (string.IsNullOrWhiteSpace(filePath))
                throw new DomainException("File path cannot be empty");

            UserId = userId;
            DocumentType = documentType;
            FileName = fileName;
            FilePath = filePath;
            FileSize = fileSize;
            MimeType = mimeType;
            Status = DocumentStatus.Uploaded;
            VerificationMethod = VerificationMethod.Manual;
            UploadedAt = DateTime.UtcNow;
            CreatedAt = DateTime.UtcNow;
        }

        public void StartReview(Guid reviewerId, VerificationMethod method = VerificationMethod.Manual)
        {
            if (Status != DocumentStatus.Uploaded && Status != DocumentStatus.RequiresResubmission)
                throw new DomainException("Document must be uploaded or require resubmission to start review");

            Status = DocumentStatus.UnderReview;
            ReviewedBy = reviewerId;
            VerificationMethod = method;
            UpdatedAt = DateTime.UtcNow;
        }

        public void SetAIExtractionResults(string extractedData, decimal confidenceScore)
        {
            if (VerificationMethod != VerificationMethod.AI_OCR && VerificationMethod != VerificationMethod.Hybrid)
                throw new DomainException("AI extraction results can only be set for AI/OCR or Hybrid verification");

            ExtractedData = extractedData;
            ConfidenceScore = confidenceScore;
            UpdatedAt = DateTime.UtcNow;
        }

        public void Approve(string reviewNotes = null)
        {
            if (Status != DocumentStatus.UnderReview)
                throw new DomainException("Document must be under review to approve");

            Status = DocumentStatus.Approved;
            ReviewNotes = reviewNotes;
            ReviewedAt = DateTime.UtcNow;
            RejectionReason = null;
            UpdatedAt = DateTime.UtcNow;
        }

        public void Reject(string rejectionReason, string reviewNotes = null)
        {
            if (Status != DocumentStatus.UnderReview)
                throw new DomainException("Document must be under review to reject");

            if (string.IsNullOrWhiteSpace(rejectionReason))
                throw new DomainException("Rejection reason is required");

            Status = DocumentStatus.Rejected;
            RejectionReason = rejectionReason;
            ReviewNotes = reviewNotes;
            ReviewedAt = DateTime.UtcNow;
            UpdatedAt = DateTime.UtcNow;
        }

        public void RequestResubmission(string rejectionReason, string reviewNotes = null)
        {
            if (Status != DocumentStatus.UnderReview)
                throw new DomainException("Document must be under review to request resubmission");

            if (string.IsNullOrWhiteSpace(rejectionReason))
                throw new DomainException("Rejection reason is required");

            Status = DocumentStatus.RequiresResubmission;
            RejectionReason = rejectionReason;
            ReviewNotes = reviewNotes;
            ReviewedAt = DateTime.UtcNow;
            UpdatedAt = DateTime.UtcNow;
        }

        public void UpdateFile(string fileName, string filePath, string fileSize, string mimeType)
        {
            if (Status != DocumentStatus.RequiresResubmission)
                throw new DomainException("File can only be updated when resubmission is required");

            FileName = fileName;
            FilePath = filePath;
            FileSize = fileSize;
            MimeType = mimeType;
            Status = DocumentStatus.Uploaded;
            RejectionReason = null;
            ReviewNotes = null;
            ReviewedBy = null;
            ReviewedAt = null;
            UploadedAt = DateTime.UtcNow;
            UpdatedAt = DateTime.UtcNow;
        }
    }
}
