using Shared.Domain.ValueObjects;

namespace DataStorage.Domain.ValueObjects;

/// <summary>
/// Resource forecast result
/// </summary>
public class ResourceForecast : ValueObject
{
    public string ResourceType { get; init; }
    public DateTime ForecastGeneratedAt { get; init; }
    public string ForecastModel { get; init; }
    public List<ForecastDataPoint> ForecastData { get; init; }
    public ForecastAccuracy Accuracy { get; init; }
    public List<string> Assumptions { get; init; }
    public Dictionary<string, object> ModelMetadata { get; init; }

    public ResourceForecast(
        string resourceType,
        DateTime forecastGeneratedAt,
        string forecastModel,
        List<ForecastDataPoint> forecastData,
        ForecastAccuracy accuracy,
        List<string> assumptions,
        Dictionary<string, object>? modelMetadata = null)
    {
        ResourceType = resourceType;
        ForecastGeneratedAt = forecastGeneratedAt;
        ForecastModel = forecastModel;
        ForecastData = forecastData;
        Accuracy = accuracy;
        Assumptions = assumptions;
        ModelMetadata = modelMetadata ?? new Dictionary<string, object>();
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return ResourceType;
        yield return ForecastGeneratedAt;
        yield return ForecastModel;
        yield return Accuracy;
        
        foreach (var dataPoint in ForecastData.OrderBy(x => x.Date))
        {
            yield return dataPoint;
        }
        
        foreach (var assumption in Assumptions.OrderBy(x => x))
        {
            yield return assumption;
        }
        
        foreach (var meta in ModelMetadata.OrderBy(x => x.Key))
        {
            yield return meta.Key;
            yield return meta.Value?.ToString() ?? string.Empty;
        }
    }
}

public class ForecastDataPoint : ValueObject
{
    public DateTime Date { get; init; }
    public double PredictedValue { get; init; }
    public double ConfidenceIntervalLower { get; init; }
    public double ConfidenceIntervalUpper { get; init; }
    public string MetricName { get; init; }
    public string Unit { get; init; }

    public ForecastDataPoint(
        DateTime date,
        double predictedValue,
        double confidenceIntervalLower,
        double confidenceIntervalUpper,
        string metricName,
        string unit)
    {
        Date = date;
        PredictedValue = predictedValue;
        ConfidenceIntervalLower = confidenceIntervalLower;
        ConfidenceIntervalUpper = confidenceIntervalUpper;
        MetricName = metricName;
        Unit = unit;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Date;
        yield return PredictedValue;
        yield return ConfidenceIntervalLower;
        yield return ConfidenceIntervalUpper;
        yield return MetricName;
        yield return Unit;
    }
}

public class ForecastAccuracy : ValueObject
{
    public double MeanAbsoluteError { get; init; }
    public double MeanSquaredError { get; init; }
    public double RSquared { get; init; }
    public double ConfidenceLevel { get; init; }
    public string AccuracyGrade { get; init; }

    public ForecastAccuracy(
        double meanAbsoluteError,
        double meanSquaredError,
        double rSquared,
        double confidenceLevel,
        string accuracyGrade)
    {
        MeanAbsoluteError = meanAbsoluteError;
        MeanSquaredError = meanSquaredError;
        RSquared = rSquared;
        ConfidenceLevel = confidenceLevel;
        AccuracyGrade = accuracyGrade;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return MeanAbsoluteError;
        yield return MeanSquaredError;
        yield return RSquared;
        yield return ConfidenceLevel;
        yield return AccuracyGrade;
    }
}
