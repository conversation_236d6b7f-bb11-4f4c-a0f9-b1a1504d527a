using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using NetworkFleetManagement.Infrastructure.Persistence;
using Shared.Messaging;
using Testcontainers.PostgreSql;

namespace NetworkFleetManagement.IntegrationTests.Infrastructure;

public class TestWebApplicationFactory : WebApplicationFactory<Program>, IAsyncLifetime
{
    private readonly PostgreSqlContainer _dbContainer = new PostgreSqlBuilder()
        .WithImage("postgres:15")
        .WithDatabase("networkfleet_test")
        .WithUsername("test")
        .WithPassword("test")
        .Build();

    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        builder.ConfigureServices(services =>
        {
            // Remove the existing DbContext registration
            services.RemoveAll(typeof(DbContextOptions<NetworkFleetDbContext>));
            services.RemoveAll(typeof(NetworkFleetDbContext));

            // Add test database
            services.AddDbContext<NetworkFleetDbContext>(options =>
            {
                options.UseNpgsql(_dbContainer.GetConnectionString());
            });

            // Replace message broker with test implementation
            services.RemoveAll(typeof(IMessageBroker));
            services.AddSingleton<IMessageBroker, TestMessageBroker>();

            // Ensure database is created
            var serviceProvider = services.BuildServiceProvider();
            using var scope = serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<NetworkFleetDbContext>();
            context.Database.EnsureCreated();
        });

        builder.UseEnvironment("Testing");
    }

    public async Task InitializeAsync()
    {
        await _dbContainer.StartAsync();
    }

    public new async Task DisposeAsync()
    {
        await _dbContainer.StopAsync();
        await base.DisposeAsync();
    }
}

public class TestMessageBroker : IMessageBroker
{
    private readonly List<(string Topic, object Message)> _publishedMessages = new();

    public Task PublishAsync<T>(string topic, T message, CancellationToken cancellationToken = default)
    {
        _publishedMessages.Add((topic, message!));
        return Task.CompletedTask;
    }

    public IReadOnlyList<(string Topic, object Message)> PublishedMessages => _publishedMessages.AsReadOnly();

    public void ClearMessages() => _publishedMessages.Clear();
}
