using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Repositories;
using AnalyticsBIService.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Shared.Infrastructure.Repositories;
using Shared.Infrastructure.Caching;
using Shared.Infrastructure.Monitoring;

namespace AnalyticsBIService.Infrastructure.Repositories;

/// <summary>
/// Repository implementation for data pipeline operations
/// </summary>
public class DataPipelineRepository : OptimizedRepositoryBase<DataPipeline, Guid>, IDataPipelineRepository
{
    public DataPipelineRepository(
        AnalyticsBIDbContext context,
        ICachingService cachingService,
        IPerformanceMetrics performanceMetrics) : base(context, cachingService, performanceMetrics)
    {
    }

    /// <summary>
    /// Get data pipelines by user ID
    /// </summary>
    public async Task<IEnumerable<DataPipeline>> GetByUserIdAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await Context.Set<DataPipeline>()
            .Where(dp => dp.UserId == userId)
            .OrderByDescending(dp => dp.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get data pipelines by status
    /// </summary>
    public async Task<IEnumerable<DataPipeline>> GetByStatusAsync(string status, CancellationToken cancellationToken = default)
    {
        return await Context.Set<DataPipeline>()
            .Where(dp => dp.Status == status)
            .OrderByDescending(dp => dp.LastRun)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get active data pipelines
    /// </summary>
    public async Task<IEnumerable<DataPipeline>> GetActiveAsync(CancellationToken cancellationToken = default)
    {
        return await Context.Set<DataPipeline>()
            .Where(dp => dp.IsActive && dp.Status == "Active")
            .OrderByDescending(dp => dp.LastRun)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get data pipelines scheduled to run
    /// </summary>
    public async Task<IEnumerable<DataPipeline>> GetScheduledToRunAsync(DateTime beforeTime, CancellationToken cancellationToken = default)
    {
        return await Context.Set<DataPipeline>()
            .Where(dp => dp.IsActive &&
                        dp.Status == "Active" &&
                        dp.NextRun.HasValue &&
                        dp.NextRun.Value <= beforeTime)
            .OrderBy(dp => dp.NextRun)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get data pipeline by name and user ID
    /// </summary>
    public async Task<DataPipeline?> GetByNameAndUserIdAsync(string name, Guid userId, CancellationToken cancellationToken = default)
    {
        return await Context.Set<DataPipeline>()
            .FirstOrDefaultAsync(dp => dp.Name == name && dp.UserId == userId, cancellationToken);
    }

    /// <summary>
    /// Get recent pipeline executions
    /// </summary>
    public async Task<IEnumerable<DataPipeline>> GetRecentExecutionsAsync(int count = 10, CancellationToken cancellationToken = default)
    {
        return await Context.Set<DataPipeline>()
            .Where(dp => dp.LastRun.HasValue)
            .OrderByDescending(dp => dp.LastRun)
            .Take(count)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get pipeline execution statistics
    /// </summary>
    public async Task<Dictionary<string, object>> GetExecutionStatisticsAsync(Guid pipelineId, CancellationToken cancellationToken = default)
    {
        var pipeline = await Context.Set<DataPipeline>()
            .FirstOrDefaultAsync(dp => dp.Id == pipelineId, cancellationToken);

        if (pipeline == null)
        {
            return new Dictionary<string, object>();
        }

        return new Dictionary<string, object>
        {
            ["TotalRuns"] = pipeline.TotalRuns,
            ["SuccessfulRuns"] = pipeline.SuccessfulRuns,
            ["FailedRuns"] = pipeline.FailedRuns,
            ["SuccessRate"] = pipeline.SuccessRate,
            ["AverageExecutionTime"] = pipeline.AverageExecutionTime,
            ["TotalRecordsProcessed"] = pipeline.TotalRecordsProcessed,
            ["LastRun"] = pipeline.LastRun,
            ["LastSuccessfulRun"] = pipeline.LastSuccessfulRun,
            ["LastFailedRun"] = pipeline.LastFailedRun,
            ["LastError"] = pipeline.LastError
        };
    }
}
