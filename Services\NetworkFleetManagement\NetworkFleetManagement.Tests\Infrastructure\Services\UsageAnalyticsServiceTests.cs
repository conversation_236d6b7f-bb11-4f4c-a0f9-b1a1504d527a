using Microsoft.Extensions.Logging;
using Moq;
using NetworkFleetManagement.Domain.Entities;
using NetworkFleetManagement.Domain.Repositories;
using NetworkFleetManagement.Domain.Services;
using NetworkFleetManagement.Infrastructure.Services;
using Shared.Infrastructure.Caching;
using Xunit;

namespace NetworkFleetManagement.Tests.Infrastructure.Services;

public class UsageAnalyticsServiceTests
{
    private readonly Mock<IUsageAnalyticsRepository> _mockRepository;
    private readonly Mock<ICacheService> _mockCacheService;
    private readonly Mock<ILogger<UsageAnalyticsService>> _mockLogger;
    private readonly UsageAnalyticsService _service;

    public UsageAnalyticsServiceTests()
    {
        _mockRepository = new Mock<IUsageAnalyticsRepository>();
        _mockCacheService = new Mock<ICacheService>();
        _mockLogger = new Mock<ILogger<UsageAnalyticsService>>();
        _service = new UsageAnalyticsService(_mockRepository.Object, _mockCacheService.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task GetFleetUtilizationAnalyticsAsync_WithValidData_ReturnsAnalytics()
    {
        // Arrange
        var carrierId = Guid.NewGuid();
        var from = DateTime.UtcNow.AddDays(-30);
        var to = DateTime.UtcNow;
        var expectedMetrics = new Dictionary<string, object>
        {
            ["totalVehicles"] = 50,
            ["activeVehicles"] = 45,
            ["utilizationRate"] = 0.9
        };

        _mockCacheService.Setup(x => x.GetAsync<Dictionary<string, object>>(It.IsAny<string>()))
            .ReturnsAsync((Dictionary<string, object>?)null);
        _mockRepository.Setup(x => x.GetFleetUtilizationMetricsAsync(carrierId, from, to))
            .ReturnsAsync(expectedMetrics);

        // Act
        var result = await _service.GetFleetUtilizationAnalyticsAsync(carrierId, from, to);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(carrierId, result["carrierId"]);
        Assert.Contains("totalVehicles", result.Keys);
        Assert.Contains("activeVehicles", result.Keys);
        Assert.Contains("utilizationRate", result.Keys);
    }

    [Fact]
    public async Task GetVehiclePerformanceAnalyticsAsync_WithValidData_ReturnsAnalytics()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var from = DateTime.UtcNow.AddDays(-7);
        var to = DateTime.UtcNow;
        var expectedMetrics = new Dictionary<string, object>
        {
            ["totalTrips"] = 25,
            ["totalDistance"] = 1500.5,
            ["averageSpeed"] = 65.2,
            ["fuelEfficiency"] = 8.5
        };

        _mockCacheService.Setup(x => x.GetAsync<Dictionary<string, object>>(It.IsAny<string>()))
            .ReturnsAsync((Dictionary<string, object>?)null);
        _mockRepository.Setup(x => x.GetVehiclePerformanceMetricsAsync(vehicleId, from, to))
            .ReturnsAsync(expectedMetrics);

        // Act
        var result = await _service.GetVehiclePerformanceAnalyticsAsync(vehicleId, from, to);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(vehicleId, result["vehicleId"]);
        Assert.Contains("totalTrips", result.Keys);
        Assert.Contains("totalDistance", result.Keys);
    }

    [Fact]
    public async Task GetDriverPerformanceAnalyticsAsync_WithValidData_ReturnsAnalytics()
    {
        // Arrange
        var driverId = Guid.NewGuid();
        var from = DateTime.UtcNow.AddDays(-30);
        var to = DateTime.UtcNow;
        var expectedMetrics = new Dictionary<string, object>
        {
            ["totalTrips"] = 100,
            ["totalHours"] = 160,
            ["safetyScore"] = 95.5,
            ["onTimePercentage"] = 98.2
        };

        _mockCacheService.Setup(x => x.GetAsync<Dictionary<string, object>>(It.IsAny<string>()))
            .ReturnsAsync((Dictionary<string, object>?)null);
        _mockRepository.Setup(x => x.GetDriverPerformanceMetricsAsync(driverId, from, to))
            .ReturnsAsync(expectedMetrics);

        // Act
        var result = await _service.GetDriverPerformanceAnalyticsAsync(driverId, from, to);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(driverId, result["driverId"]);
        Assert.Contains("totalTrips", result.Keys);
        Assert.Contains("safetyScore", result.Keys);
    }

    [Fact]
    public async Task GetTopPerformingVehiclesAsync_WithValidData_ReturnsTopVehicles()
    {
        // Arrange
        var carrierId = Guid.NewGuid();
        var count = 5;
        var from = DateTime.UtcNow.AddDays(-30);
        var to = DateTime.UtcNow;
        var expectedVehicles = new List<Dictionary<string, object>>
        {
            new() { ["vehicleId"] = Guid.NewGuid(), ["score"] = 95.5 },
            new() { ["vehicleId"] = Guid.NewGuid(), ["score"] = 94.2 },
            new() { ["vehicleId"] = Guid.NewGuid(), ["score"] = 93.8 }
        };

        _mockCacheService.Setup(x => x.GetAsync<List<Dictionary<string, object>>>(It.IsAny<string>()))
            .ReturnsAsync((List<Dictionary<string, object>>?)null);
        _mockRepository.Setup(x => x.GetTopPerformingEntitiesAsync("Vehicle", "performance", count, from, to))
            .ReturnsAsync(expectedVehicles);

        // Act
        var result = await _service.GetTopPerformingVehiclesAsync(carrierId, count, from, to);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, vehicle => Assert.Contains("vehicleId", vehicle.Keys));
    }

    [Fact]
    public async Task TrackVehicleUsageAsync_WithValidData_CreatesRecord()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var usageType = VehicleUsageType.TripStarted;
        var metadata = new Dictionary<string, object> { ["location"] = "Test Location" };

        _mockRepository.Setup(x => x.CreateAsync(It.IsAny<UsageAnalyticsRecord>()))
            .ReturnsAsync((UsageAnalyticsRecord record) => record);

        // Act
        await _service.TrackVehicleUsageAsync(vehicleId, usageType, metadata);

        // Assert
        _mockRepository.Verify(x => x.CreateAsync(It.Is<UsageAnalyticsRecord>(r => 
            r.EntityId == vehicleId && 
            r.EventType == usageType.ToString() &&
            r.Category == "Vehicle")), Times.Once);
    }

    [Fact]
    public async Task TrackDriverActivityAsync_WithValidData_CreatesRecord()
    {
        // Arrange
        var driverId = Guid.NewGuid();
        var activityType = DriverActivityType.TripActivity;
        var metadata = new Dictionary<string, object> { ["duration"] = 120 };

        _mockRepository.Setup(x => x.CreateAsync(It.IsAny<UsageAnalyticsRecord>()))
            .ReturnsAsync((UsageAnalyticsRecord record) => record);

        // Act
        await _service.TrackDriverActivityAsync(driverId, activityType, metadata);

        // Assert
        _mockRepository.Verify(x => x.CreateAsync(It.Is<UsageAnalyticsRecord>(r => 
            r.EntityId == driverId && 
            r.EventType == activityType.ToString() &&
            r.Category == "Driver")), Times.Once);
    }

    [Fact]
    public async Task GetDriverComplianceAnalyticsAsync_WithValidData_ReturnsComplianceMetrics()
    {
        // Arrange
        var driverId = Guid.NewGuid();
        var from = DateTime.UtcNow.AddDays(-30);
        var to = DateTime.UtcNow;
        var complianceRecords = new List<UsageAnalyticsRecord>
        {
            UsageAnalyticsRecord.CreateDriverActivityRecord(driverId, DriverActivityType.ComplianceCheck, 
                new Dictionary<string, object> { ["status"] = "passed" }),
            UsageAnalyticsRecord.CreateDriverActivityRecord(driverId, DriverActivityType.ComplianceCheck, 
                new Dictionary<string, object> { ["status"] = "passed" }),
            UsageAnalyticsRecord.CreateDriverActivityRecord(driverId, DriverActivityType.ComplianceCheck, 
                new Dictionary<string, object> { ["status"] = "failed" })
        };

        // Set the event type to include "Compliance"
        foreach (var record in complianceRecords)
        {
            record.GetType().GetProperty("EventType")?.SetValue(record, "ComplianceCheck");
            record.GetType().GetProperty("Status")?.SetValue(record, 
                record.Metadata.ContainsKey("status") ? record.Metadata["status"].ToString() : "unknown");
        }

        _mockCacheService.Setup(x => x.GetAsync<Dictionary<string, object>>(It.IsAny<string>()))
            .ReturnsAsync((Dictionary<string, object>?)null);
        _mockRepository.Setup(x => x.GetByEntityAsync(driverId, "Driver", from, to))
            .ReturnsAsync(complianceRecords);

        // Act
        var result = await _service.GetDriverComplianceAnalyticsAsync(driverId, from, to);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(driverId, result["driverId"]);
        Assert.Contains("complianceRate", result.Keys);
    }

    [Fact]
    public async Task GetAggregatedMetricsAsync_WithValidData_ReturnsAggregatedData()
    {
        // Arrange
        var metricType = "vehicle_utilization";
        var from = DateTime.UtcNow.AddDays(-7);
        var to = DateTime.UtcNow;
        var aggregationType = "daily";
        var expectedData = new Dictionary<string, object>
        {
            ["aggregationType"] = aggregationType,
            ["data"] = new Dictionary<string, object>()
        };

        _mockCacheService.Setup(x => x.GetAsync<Dictionary<string, object>>(It.IsAny<string>()))
            .ReturnsAsync((Dictionary<string, object>?)null);
        _mockRepository.Setup(x => x.GetAggregatedMetricsAsync(metricType, from, to, aggregationType))
            .ReturnsAsync(expectedData);

        // Act
        var result = await _service.GetAggregatedMetricsAsync(metricType, from, to, aggregationType);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(expectedData, result);
    }

    [Fact]
    public async Task TrackNetworkEventAsync_WithValidData_CreatesRecord()
    {
        // Arrange
        var networkId = Guid.NewGuid();
        var eventType = NetworkEventType.PerformanceUpdate;
        var metadata = new Dictionary<string, object> { ["responseTime"] = 150 };

        _mockRepository.Setup(x => x.CreateAsync(It.IsAny<UsageAnalyticsRecord>()))
            .ReturnsAsync((UsageAnalyticsRecord record) => record);

        // Act
        await _service.TrackNetworkEventAsync(networkId, eventType, metadata);

        // Assert
        _mockRepository.Verify(x => x.CreateAsync(It.Is<UsageAnalyticsRecord>(r => 
            r.EntityId == networkId && 
            r.EventType == eventType.ToString() &&
            r.Category == "Network")), Times.Once);
    }

    [Fact]
    public async Task TrackSystemEventAsync_WithValidData_CreatesRecord()
    {
        // Arrange
        var eventType = SystemEventType.PerformanceAlert;
        var metadata = new Dictionary<string, object> { ["severity"] = "high" };

        _mockRepository.Setup(x => x.CreateAsync(It.IsAny<UsageAnalyticsRecord>()))
            .ReturnsAsync((UsageAnalyticsRecord record) => record);

        // Act
        await _service.TrackSystemEventAsync(eventType, metadata);

        // Assert
        _mockRepository.Verify(x => x.CreateAsync(It.Is<UsageAnalyticsRecord>(r => 
            r.EventType == eventType.ToString() &&
            r.Category == "System")), Times.Once);
    }
}
