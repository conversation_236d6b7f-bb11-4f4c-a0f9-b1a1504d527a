using Shared.Domain.ValueObjects;

namespace DataStorage.Domain.ValueObjects;

/// <summary>
/// Dependency map for services and components
/// </summary>
public class DependencyMap : ValueObject
{
    public string ServiceName { get; init; }
    public DateTime GeneratedAt { get; init; }
    public List<ServiceDependency> Dependencies { get; init; }
    public List<ServiceDependent> Dependents { get; init; }
    public DependencyMetrics Metrics { get; init; }
    public Dictionary<string, object> Metadata { get; init; }

    public DependencyMap(
        string serviceName,
        DateTime generatedAt,
        List<ServiceDependency> dependencies,
        List<ServiceDependent> dependents,
        DependencyMetrics metrics,
        Dictionary<string, object>? metadata = null)
    {
        ServiceName = serviceName;
        GeneratedAt = generatedAt;
        Dependencies = dependencies;
        Dependents = dependents;
        Metrics = metrics;
        Metadata = metadata ?? new Dictionary<string, object>();
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return ServiceName;
        yield return GeneratedAt;
        yield return Metrics;
        
        foreach (var dependency in Dependencies.OrderBy(x => x.ServiceName))
        {
            yield return dependency;
        }
        
        foreach (var dependent in Dependents.OrderBy(x => x.ServiceName))
        {
            yield return dependent;
        }
        
        foreach (var meta in Metadata.OrderBy(x => x.Key))
        {
            yield return meta.Key;
            yield return meta.Value?.ToString() ?? string.Empty;
        }
    }
}

public class ServiceDependency : ValueObject
{
    public string ServiceName { get; init; }
    public string DependencyType { get; init; }
    public string ConnectionType { get; init; }
    public bool IsCritical { get; init; }
    public double HealthScore { get; init; }
    public DateTime LastChecked { get; init; }
    public string Status { get; init; }

    public ServiceDependency(
        string serviceName,
        string dependencyType,
        string connectionType,
        bool isCritical,
        double healthScore,
        DateTime lastChecked,
        string status)
    {
        ServiceName = serviceName;
        DependencyType = dependencyType;
        ConnectionType = connectionType;
        IsCritical = isCritical;
        HealthScore = healthScore;
        LastChecked = lastChecked;
        Status = status;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return ServiceName;
        yield return DependencyType;
        yield return ConnectionType;
        yield return IsCritical;
        yield return HealthScore;
        yield return LastChecked;
        yield return Status;
    }
}

public class ServiceDependent : ValueObject
{
    public string ServiceName { get; init; }
    public string DependencyType { get; init; }
    public double ImpactScore { get; init; }
    public DateTime LastInteraction { get; init; }
    public string Status { get; init; }

    public ServiceDependent(
        string serviceName,
        string dependencyType,
        double impactScore,
        DateTime lastInteraction,
        string status)
    {
        ServiceName = serviceName;
        DependencyType = dependencyType;
        ImpactScore = impactScore;
        LastInteraction = lastInteraction;
        Status = status;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return ServiceName;
        yield return DependencyType;
        yield return ImpactScore;
        yield return LastInteraction;
        yield return Status;
    }
}

public class DependencyMetrics : ValueObject
{
    public int TotalDependencies { get; init; }
    public int CriticalDependencies { get; init; }
    public int HealthyDependencies { get; init; }
    public int UnhealthyDependencies { get; init; }
    public double OverallHealthScore { get; init; }
    public int TotalDependents { get; init; }

    public DependencyMetrics(
        int totalDependencies,
        int criticalDependencies,
        int healthyDependencies,
        int unhealthyDependencies,
        double overallHealthScore,
        int totalDependents)
    {
        TotalDependencies = totalDependencies;
        CriticalDependencies = criticalDependencies;
        HealthyDependencies = healthyDependencies;
        UnhealthyDependencies = unhealthyDependencies;
        OverallHealthScore = overallHealthScore;
        TotalDependents = totalDependents;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return TotalDependencies;
        yield return CriticalDependencies;
        yield return HealthyDependencies;
        yield return UnhealthyDependencies;
        yield return OverallHealthScore;
        yield return TotalDependents;
    }
}
