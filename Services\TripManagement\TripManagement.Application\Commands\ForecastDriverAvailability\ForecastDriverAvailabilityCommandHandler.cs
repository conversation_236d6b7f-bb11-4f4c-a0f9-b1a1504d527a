using MediatR;
using Microsoft.Extensions.Logging;
using TripManagement.Application.Interfaces;
using TripManagement.Domain.Enums;

namespace TripManagement.Application.Commands.ForecastDriverAvailability;

public class ForecastDriverAvailabilityCommandHandler : IRequestHandler<ForecastDriverAvailabilityCommand, DriverAvailabilityForecast>
{
    private readonly IDriverRepository _driverRepository;
    private readonly ITripRepository _tripRepository;
    private readonly ILogger<ForecastDriverAvailabilityCommandHandler> _logger;

    public ForecastDriverAvailabilityCommandHandler(
        IDriverRepository driverRepository,
        ITripRepository tripRepository,
        ILogger<ForecastDriverAvailabilityCommandHandler> logger)
    {
        _driverRepository = driverRepository;
        _tripRepository = tripRepository;
        _logger = logger;
    }

    public async Task<DriverAvailabilityForecast> Handle(ForecastDriverAvailabilityCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Forecasting driver availability from {StartDate} to {EndDate}", 
                request.StartDate, request.EndDate);

            // Get drivers to analyze
            var drivers = request.DriverId.HasValue
                ? new List<Domain.Entities.Driver> { await _driverRepository.GetByIdAsync(request.DriverId.Value, cancellationToken) }.Where(d => d != null).ToList()!
                : request.CarrierId.HasValue
                    ? await _driverRepository.GetByCarrierIdAsync(request.CarrierId.Value, cancellationToken)
                    : await GetAllDriversAsync(cancellationToken);

            var driverAvailabilities = new List<DriverAvailabilityDto>();

            foreach (var driver in drivers)
            {
                var availability = await AnalyzeDriverAvailabilityAsync(driver, request, cancellationToken);
                driverAvailabilities.Add(availability);
            }

            var summary = CalculateDriverAvailabilitySummary(driverAvailabilities);

            var forecast = new DriverAvailabilityForecast
            {
                ForecastDate = DateTime.UtcNow,
                DriverAvailabilities = driverAvailabilities,
                Summary = summary
            };

            _logger.LogInformation("Driver availability forecast completed for {DriverCount} drivers", 
                driverAvailabilities.Count);

            return forecast;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error forecasting driver availability");
            throw;
        }
    }

    private async Task<List<Domain.Entities.Driver>> GetAllDriversAsync(CancellationToken cancellationToken)
    {
        // This would need to be implemented in the repository
        // For now, return empty list
        return new List<Domain.Entities.Driver>();
    }

    private async Task<DriverAvailabilityDto> AnalyzeDriverAvailabilityAsync(
        Domain.Entities.Driver driver, 
        ForecastDriverAvailabilityCommand request, 
        CancellationToken cancellationToken)
    {
        var availabilityPeriods = new List<DriverAvailabilityPeriod>();
        var unavailabilityPeriods = new List<DriverUnavailabilityPeriod>();

        // Check current availability
        var isCurrentlyAvailable = driver.IsAvailable;
        DateTime? nextAvailableDate = null;

        // Analyze trips if requested
        if (request.IncludeTrips)
        {
            var driverTrips = await _tripRepository.GetByDriverIdAsync(driver.Id, cancellationToken);
            var activeTrips = driverTrips.Where(t => 
                t.Status == TripStatus.Assigned || 
                t.Status == TripStatus.InProgress ||
                (t.EstimatedStartTime >= request.StartDate && t.EstimatedStartTime <= request.EndDate))
                .ToList();

            foreach (var trip in activeTrips)
            {
                var startDate = trip.StartedAt ?? trip.EstimatedStartTime;
                var endDate = trip.CompletedAt ?? trip.EstimatedEndTime;

                if (startDate <= request.EndDate && endDate >= request.StartDate)
                {
                    unavailabilityPeriods.Add(new DriverUnavailabilityPeriod
                    {
                        StartDate = startDate > request.StartDate ? startDate : request.StartDate,
                        EndDate = endDate < request.EndDate ? endDate : request.EndDate,
                        Reason = DriverUnavailabilityReason.OnTrip,
                        Description = $"Trip {trip.TripNumber}",
                        RelatedEntityId = trip.Id
                    });
                }
            }
        }

        // Check license expiry if requested
        if (request.IncludeLicenseExpiry && !driver.IsLicenseValid)
        {
            unavailabilityPeriods.Add(new DriverUnavailabilityPeriod
            {
                StartDate = driver.LicenseExpiryDate,
                EndDate = request.EndDate,
                Reason = DriverUnavailabilityReason.ExpiredLicense,
                Description = "Expired driving license"
            });
        }

        // Check driver status
        if (driver.Status != DriverStatus.Available)
        {
            var reason = driver.Status switch
            {
                DriverStatus.OffDuty => DriverUnavailabilityReason.OffDuty,
                DriverStatus.Suspended => DriverUnavailabilityReason.Suspended,
                DriverStatus.Unavailable => DriverUnavailabilityReason.Other,
                _ => DriverUnavailabilityReason.Other
            };

            unavailabilityPeriods.Add(new DriverUnavailabilityPeriod
            {
                StartDate = request.StartDate,
                EndDate = request.EndDate,
                Reason = reason,
                Description = $"Driver status: {driver.Status}"
            });
        }

        // Calculate availability periods (gaps between unavailability periods)
        availabilityPeriods = CalculateDriverAvailabilityPeriods(request.StartDate, request.EndDate, unavailabilityPeriods, driver);

        // Calculate utilization
        var totalPeriod = request.EndDate - request.StartDate;
        var unavailableTime = unavailabilityPeriods.Sum(u => (u.EndDate - u.StartDate).TotalHours);
        var utilizationPercentage = totalPeriod.TotalHours > 0 ? (unavailableTime / totalPeriod.TotalHours) * 100 : 0;

        // Find next available date
        if (!isCurrentlyAvailable)
        {
            var currentUnavailability = unavailabilityPeriods
                .Where(u => u.StartDate <= DateTime.UtcNow && u.EndDate > DateTime.UtcNow)
                .OrderBy(u => u.EndDate)
                .FirstOrDefault();

            nextAvailableDate = currentUnavailability?.EndDate;
        }

        // Calculate reliability score
        var reliabilityScore = CalculateReliabilityScore(driver);

        return new DriverAvailabilityDto
        {
            DriverId = driver.Id,
            FullName = driver.FullName,
            LicenseNumber = driver.LicenseNumber,
            AvailabilityPeriods = availabilityPeriods,
            UnavailabilityPeriods = unavailabilityPeriods,
            UtilizationPercentage = utilizationPercentage,
            IsCurrentlyAvailable = isCurrentlyAvailable,
            NextAvailableDate = nextAvailableDate,
            PerformanceRating = driver.Rating,
            ReliabilityScore = reliabilityScore
        };
    }

    private List<DriverAvailabilityPeriod> CalculateDriverAvailabilityPeriods(
        DateTime startDate, 
        DateTime endDate, 
        List<DriverUnavailabilityPeriod> unavailabilityPeriods,
        Domain.Entities.Driver driver)
    {
        var availabilityPeriods = new List<DriverAvailabilityPeriod>();
        
        // Sort unavailability periods by start date
        var sortedUnavailability = unavailabilityPeriods.OrderBy(u => u.StartDate).ToList();
        
        var currentDate = startDate;
        
        foreach (var unavailability in sortedUnavailability)
        {
            if (unavailability.StartDate > currentDate)
            {
                // There's an availability gap
                var availabilityType = DetermineAvailabilityType(driver);
                var confidenceLevel = CalculateConfidenceLevel(driver, currentDate, unavailability.StartDate);
                
                availabilityPeriods.Add(new DriverAvailabilityPeriod
                {
                    StartDate = currentDate,
                    EndDate = unavailability.StartDate,
                    Type = availabilityType,
                    ConfidenceLevel = confidenceLevel
                });
            }
            
            currentDate = unavailability.EndDate > currentDate ? unavailability.EndDate : currentDate;
        }
        
        // Check if there's availability after the last unavailability period
        if (currentDate < endDate)
        {
            var availabilityType = DetermineAvailabilityType(driver);
            var confidenceLevel = CalculateConfidenceLevel(driver, currentDate, endDate);
            
            availabilityPeriods.Add(new DriverAvailabilityPeriod
            {
                StartDate = currentDate,
                EndDate = endDate,
                Type = availabilityType,
                ConfidenceLevel = confidenceLevel
            });
        }
        
        return availabilityPeriods;
    }

    private DriverAvailabilityType DetermineAvailabilityType(Domain.Entities.Driver driver)
    {
        // Determine availability type based on driver performance and rating
        if (driver.Rating >= 4.5m)
            return DriverAvailabilityType.PreferredDriver;
        
        if (driver.Rating >= 3.5m)
            return DriverAvailabilityType.Available;
        
        return DriverAvailabilityType.PartiallyAvailable;
    }

    private decimal CalculateConfidenceLevel(Domain.Entities.Driver driver, DateTime startDate, DateTime endDate)
    {
        // Calculate confidence based on driver reliability and historical patterns
        var baseConfidence = 80m; // Base confidence level
        
        // Adjust based on driver rating
        var ratingAdjustment = (driver.Rating - 3m) * 5m; // +/- 10% based on rating
        
        // Adjust based on completion rate
        var completionRate = driver.CompletedTrips > 0 
            ? (decimal)driver.CompletedTrips / driver.TotalTrips * 100
            : 50m;
        var completionAdjustment = (completionRate - 80m) * 0.2m;
        
        var confidence = baseConfidence + ratingAdjustment + completionAdjustment;
        
        return Math.Max(0, Math.Min(100, confidence));
    }

    private DriverReliabilityScore CalculateReliabilityScore(Domain.Entities.Driver driver)
    {
        var onTimePercentage = 85m; // This would be calculated from performance records
        var completionRate = driver.TotalTrips > 0 
            ? (decimal)driver.CompletedTrips / driver.TotalTrips * 100 
            : 0m;
        var safetyScore = 90m; // This would be calculated from performance records
        
        var overallReliability = (onTimePercentage * 0.4m + completionRate * 0.3m + safetyScore * 0.3m);
        
        var reliabilityFactors = new List<string>();
        if (onTimePercentage >= 90) reliabilityFactors.Add("Excellent punctuality");
        if (completionRate >= 95) reliabilityFactors.Add("High completion rate");
        if (safetyScore >= 95) reliabilityFactors.Add("Outstanding safety record");
        if (driver.Rating >= 4.5m) reliabilityFactors.Add("High customer satisfaction");
        
        return new DriverReliabilityScore
        {
            OnTimePercentage = onTimePercentage,
            CompletionRate = completionRate,
            SafetyScore = safetyScore,
            OverallReliability = overallReliability,
            ReliabilityFactors = reliabilityFactors
        };
    }

    private DriverAvailabilitySummary CalculateDriverAvailabilitySummary(List<DriverAvailabilityDto> driverAvailabilities)
    {
        var totalDrivers = driverAvailabilities.Count;
        var availableDrivers = driverAvailabilities.Count(d => d.IsCurrentlyAvailable);
        var unavailableDrivers = totalDrivers - availableDrivers;
        
        var driversOnTrips = driverAvailabilities.Count(d => 
            d.UnavailabilityPeriods.Any(u => u.Reason == DriverUnavailabilityReason.OnTrip && 
                u.StartDate <= DateTime.UtcNow && u.EndDate > DateTime.UtcNow));
        
        var driversWithExpiredLicenses = driverAvailabilities.Count(d => 
            d.UnavailabilityPeriods.Any(u => u.Reason == DriverUnavailabilityReason.ExpiredLicense));
        
        var highPerformanceDrivers = driverAvailabilities.Count(d => d.PerformanceRating >= 4.0m);
        
        var averageUtilization = totalDrivers > 0 
            ? driverAvailabilities.Average(d => d.UtilizationPercentage) 
            : 0;
        
        var averagePerformanceRating = totalDrivers > 0 
            ? driverAvailabilities.Average(d => d.PerformanceRating) 
            : 0;

        return new DriverAvailabilitySummary
        {
            TotalDrivers = totalDrivers,
            AvailableDrivers = availableDrivers,
            UnavailableDrivers = unavailableDrivers,
            DriversOnTrips = driversOnTrips,
            DriversWithExpiredLicenses = driversWithExpiredLicenses,
            HighPerformanceDrivers = highPerformanceDrivers,
            AverageUtilization = averageUtilization,
            AveragePerformanceRating = averagePerformanceRating
        };
    }
}
