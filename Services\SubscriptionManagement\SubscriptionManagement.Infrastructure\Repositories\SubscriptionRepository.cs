using Microsoft.EntityFrameworkCore;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Infrastructure.Persistence;

namespace SubscriptionManagement.Infrastructure.Repositories
{
    public class SubscriptionRepository : ISubscriptionRepository
    {
        private readonly SubscriptionDbContext _context;

        public SubscriptionRepository(SubscriptionDbContext context)
        {
            _context = context;
        }

        public async Task<Subscription?> GetByIdAsync(Guid id)
        {
            return await _context.Subscriptions
                .Include(s => s.Plan)
                .ThenInclude(p => p.Features)
                .Include(s => s.Payments)
                .Include(s => s.Changes)
                .FirstOrDefaultAsync(s => s.Id == id);
        }

        public async Task<Subscription?> GetByUserIdAsync(Guid userId)
        {
            return await _context.Subscriptions
                .Include(s => s.Plan)
                .ThenInclude(p => p.Features)
                .Include(s => s.Payments)
                .Include(s => s.Changes)
                .FirstOrDefaultAsync(s => s.UserId == userId && s.Status == SubscriptionStatus.Active);
        }

        public async Task<List<Subscription>> GetByUserIdAndStatusAsync(Guid userId, SubscriptionStatus status)
        {
            return await _context.Subscriptions
                .Include(s => s.Plan)
                .Where(s => s.UserId == userId && s.Status == status)
                .OrderByDescending(s => s.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<Subscription>> GetActiveSubscriptionsAsync()
        {
            return await _context.Subscriptions
                .Include(s => s.Plan)
                .Where(s => s.Status == SubscriptionStatus.Active)
                .ToListAsync();
        }

        public async Task<List<Subscription>> GetExpiredSubscriptionsAsync()
        {
            return await _context.Subscriptions
                .Include(s => s.Plan)
                .Where(s => s.Status == SubscriptionStatus.Expired ||
                           (s.EndDate.HasValue && s.EndDate.Value <= DateTime.UtcNow))
                .ToListAsync();
        }

        public async Task<List<Subscription>> GetSubscriptionsDueForRenewalAsync()
        {
            return await _context.Subscriptions
                .Include(s => s.Plan)
                .Where(s => s.Status == SubscriptionStatus.Active &&
                           s.NextBillingDate <= DateTime.UtcNow &&
                           s.AutoRenew)
                .ToListAsync();
        }

        public async Task<List<Subscription>> GetSubscriptionsDueForBillingAsync(DateTime billingDate)
        {
            return await _context.Subscriptions
                .Include(s => s.Plan)
                .Where(s => s.Status == SubscriptionStatus.Active &&
                           s.NextBillingDate <= billingDate)
                .ToListAsync();
        }

        public async Task<List<Subscription>> GetSubscriptionsByPlanIdAsync(Guid planId)
        {
            return await _context.Subscriptions
                .Include(s => s.Plan)
                .Where(s => s.PlanId == planId)
                .ToListAsync();
        }

        public async Task<List<Subscription>> GetSubscriptionsAsync(int page, int pageSize)
        {
            return await _context.Subscriptions
                .Include(s => s.Plan)
                .OrderByDescending(s => s.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
        }

        public async Task<int> GetSubscriptionsCountAsync()
        {
            return await _context.Subscriptions.CountAsync();
        }

        public async Task<int> GetActiveSubscriptionsCountAsync()
        {
            return await _context.Subscriptions
                .CountAsync(s => s.Status == SubscriptionStatus.Active);
        }

        public async Task<int> GetSubscriptionsCountByStatusAsync(SubscriptionStatus status)
        {
            return await _context.Subscriptions
                .CountAsync(s => s.Status == status);
        }

        public async Task<decimal> GetTotalRevenueAsync()
        {
            return await _context.Payments
                .Where(p => p.Status == PaymentStatus.Completed)
                .SumAsync(p => p.Amount.Amount);
        }

        public async Task<decimal> GetMonthlyRecurringRevenueAsync()
        {
            return await _context.Subscriptions
                .Where(s => s.Status == SubscriptionStatus.Active)
                .SumAsync(s => s.CurrentPrice.Amount);
        }

        public async Task AddAsync(Subscription subscription)
        {
            await _context.Subscriptions.AddAsync(subscription);
            await _context.SaveChangesAsync();
        }

        public async Task UpdateAsync(Subscription subscription)
        {
            _context.Subscriptions.Update(subscription);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteAsync(Subscription subscription)
        {
            _context.Subscriptions.Remove(subscription);
            await _context.SaveChangesAsync();
        }

        public async Task<bool> ExistsAsync(Guid id)
        {
            return await _context.Subscriptions.AnyAsync(s => s.Id == id);
        }

        public async Task<bool> HasActiveSubscriptionAsync(Guid userId)
        {
            return await _context.Subscriptions
                .AnyAsync(s => s.UserId == userId && s.Status == SubscriptionStatus.Active);
        }
    }
}
