using MediatR;
using OrderManagement.Application.DTOs;

namespace OrderManagement.Application.Commands.StartNegotiation;

public class StartNegotiationCommand : IRequest<Guid>
{
    public Guid RfqId { get; set; }
    public Guid BrokerId { get; set; }
    public Guid OriginalBidId { get; set; }
    public MoneyDto OriginalPrice { get; set; } = new();
    public Guid RequestingUserId { get; set; }
    public string? Notes { get; set; }
}
