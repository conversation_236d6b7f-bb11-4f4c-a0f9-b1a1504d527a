# Fix critical build issues
Write-Host "Fixing critical build issues..." -ForegroundColor Green

# 1. Fix package version conflicts
Write-Host "Fixing package version conflicts..." -ForegroundColor Yellow

# Fix Microsoft.Extensions.Caching.Memory version conflicts
$cachingMemoryProjects = @(
    "Services\AnalyticsBIService\AnalyticsBIService.API\AnalyticsBIService.API.csproj",
    "Services\MobileWorkflow\MobileWorkflow.Infrastructure\MobileWorkflow.Infrastructure.csproj",
    "Services\MobileWorkflow\MobileWorkflow.Tests\MobileWorkflow.Tests.csproj",
    "Services\DataStorage\DataStorage.Infrastructure\DataStorage.Infrastructure.csproj"
)

foreach ($project in $cachingMemoryProjects) {
    if (Test-Path $project) {
        $content = Get-Content $project -Raw
        # Update to consistent version
        $content = $content -replace 'Microsoft\.Extensions\.Caching\.Memory.*Version="8\.0\.0"', 'Microsoft.Extensions.Caching.Memory" Version="8.0.1"'
        Set-Content -Path $project -Value $content -Encoding UTF8
        Write-Host "  Fixed caching memory version in $(Split-Path $project -Leaf)" -ForegroundColor Green
    }
}

# Fix StackExchange.Redis version conflicts
$redisProjects = @(
    "Services\AuditCompliance\AuditCompliance.Infrastructure\AuditCompliance.Infrastructure.csproj",
    "Services\MobileWorkflow\MobileWorkflow.API\MobileWorkflow.API.csproj"
)

foreach ($project in $redisProjects) {
    if (Test-Path $project) {
        $content = Get-Content $project -Raw
        # Update to consistent version
        $content = $content -replace 'StackExchange\.Redis.*Version="2\.7\.[0-9]+"', 'StackExchange.Redis" Version="2.8.16"'
        Set-Content -Path $project -Value $content -Encoding UTF8
        Write-Host "  Fixed Redis version in $(Split-Path $project -Leaf)" -ForegroundColor Green
    }
}

# 2. Fix missing Microsoft.ML.Transforms package
Write-Host "Fixing missing ML package..." -ForegroundColor Yellow
$auditApiProject = "Services\AuditCompliance\AuditCompliance.API\AuditCompliance.API.csproj"
if (Test-Path $auditApiProject) {
    $content = Get-Content $auditApiProject -Raw
    # Replace with correct package name
    $content = $content -replace 'Microsoft\.ML\.Transforms', 'Microsoft.ML'
    Set-Content -Path $auditApiProject -Value $content -Encoding UTF8
    Write-Host "  Fixed ML package reference" -ForegroundColor Green
}

# 3. Fix ContentManagement ValueObject references
Write-Host "Fixing ContentManagement ValueObject references..." -ForegroundColor Yellow
$contentValueObjects = @(
    "Services\ContentManagement\ContentManagement.Domain\ValueObjects\ContentVisibility.cs",
    "Services\ContentManagement\ContentManagement.Domain\ValueObjects\PublishSchedule.cs"
)

foreach ($voFile in $contentValueObjects) {
    if (Test-Path $voFile) {
        $content = Get-Content $voFile -Raw
        # Add using statement if not present
        if ($content -notmatch "using Shared\.Domain\.Common;") {
            $content = $content -replace "(using System[^;]*;)", "`$1`nusing Shared.Domain.Common;"
        }
        Set-Content -Path $voFile -Value $content -Encoding UTF8
        Write-Host "  Fixed ValueObject reference in $(Split-Path $voFile -Leaf)" -ForegroundColor Green
    }
}

# 4. Fix OrderManagement Entity references
Write-Host "Fixing OrderManagement Entity references..." -ForegroundColor Yellow
$orderEntities = Get-ChildItem "Services\OrderManagement\OrderManagement.Domain\Entities\*.cs" -ErrorAction SilentlyContinue

foreach ($entityFile in $orderEntities) {
    $content = Get-Content $entityFile.FullName -Raw
    $modified = $false
    
    # Add using statement if Entity is used but not imported
    if ($content -match ": Entity" -and $content -notmatch "using Shared\.Domain\.Common;") {
        $content = $content -replace "(using System[^;]*;)", "`$1`nusing Shared.Domain.Common;"
        $modified = $true
    }
    
    if ($modified) {
        Set-Content -Path $entityFile.FullName -Value $content -Encoding UTF8
        Write-Host "  Fixed Entity reference in $($entityFile.Name)" -ForegroundColor Green
    }
}

# 5. Fix TripManagement Driver syntax error
Write-Host "Fixing TripManagement Driver syntax error..." -ForegroundColor Yellow
$driverFile = "Services\TripManagement\TripManagement.Domain\Entities\Driver.cs"
if (Test-Path $driverFile) {
    $content = Get-Content $driverFile -Raw
    # Fix syntax error around line 266
    $content = $content -replace "([^)]+)\s*\)\s*\)\s*;", "`$1);"
    Set-Content -Path $driverFile -Value $content -Encoding UTF8
    Write-Host "  Fixed Driver syntax error" -ForegroundColor Green
}

# 6. Fix Shared.Infrastructure PerformanceMetrics
Write-Host "Fixing PerformanceMetrics..." -ForegroundColor Yellow
$perfFile = "Shared\Shared.Infrastructure\Monitoring\PerformanceMetrics.cs"
if (Test-Path $perfFile) {
    $content = Get-Content $perfFile -Raw
    # Fix string to int conversion issues
    $content = $content -replace 'Interlocked\.Increment\(([^)]+)\)', 'Interlocked.Increment(ref $1)'
    Set-Content -Path $perfFile -Value $content -Encoding UTF8
    Write-Host "  Fixed PerformanceMetrics" -ForegroundColor Green
}

# 7. Fix NetworkFleetManagement IPreferredPartnerRepository syntax errors
Write-Host "Fixing IPreferredPartnerRepository syntax errors..." -ForegroundColor Yellow
$prefPartnerRepo = "Services\NetworkFleetManagement\NetworkFleetManagement.Domain\Repositories\IPreferredPartnerRepository.cs"
if (Test-Path $prefPartnerRepo) {
    # Recreate the file with correct syntax
    $correctContent = @"
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using NetworkFleetManagement.Domain.Entities;

namespace NetworkFleetManagement.Domain.Repositories
{
    public interface IPreferredPartnerRepository
    {
        Task<PreferredPartner?> GetByIdAsync(Guid id);
        Task<IEnumerable<PreferredPartner>> GetAllAsync();
        Task<IEnumerable<PreferredPartner>> GetByUserIdAsync(Guid userId);
        Task<PreferredPartner> AddAsync(PreferredPartner preferredPartner);
        Task UpdateAsync(PreferredPartner preferredPartner);
        Task DeleteAsync(Guid id);
        Task<bool> ExistsAsync(Guid userId, Guid partnerId);
        Task<IEnumerable<PreferredPartner>> GetActivePartnersAsync(Guid userId);
        Task<int> GetPartnerCountAsync(Guid userId);
    }
}
"@
    Set-Content -Path $prefPartnerRepo -Value $correctContent -Encoding UTF8
    Write-Host "  Fixed IPreferredPartnerRepository syntax" -ForegroundColor Green
}

# 8. Fix Identity Infrastructure ambiguous references
Write-Host "Fixing Identity Infrastructure ambiguous references..." -ForegroundColor Yellow
$identityFiles = @(
    "Identity\Identity.Infrastructure\Data\SeedData.cs",
    "Identity\Identity.Infrastructure\Services\TokenService.cs"
)

foreach ($idFile in $identityFiles) {
    if (Test-Path $idFile) {
        $content = Get-Content $idFile -Raw
        # Use fully qualified names to resolve ambiguity
        $content = $content -replace "IRoleRepository", "Identity.Domain.Repositories.IRoleRepository"
        $content = $content -replace "IPermissionRepository", "Identity.Domain.Repositories.IPermissionRepository"
        Set-Content -Path $idFile -Value $content -Encoding UTF8
        Write-Host "  Fixed ambiguous references in $(Split-Path $idFile -Leaf)" -ForegroundColor Green
    }
}

# 9. Fix CommunicationNotification missing references
Write-Host "Fixing CommunicationNotification missing references..." -ForegroundColor Yellow
$commFiles = Get-ChildItem "Services\CommunicationNotification\CommunicationNotification.Domain" -Recurse -Filter "*.cs" -ErrorAction SilentlyContinue

foreach ($commFile in $commFiles) {
    $content = Get-Content $commFile.FullName -Raw
    $modified = $false
    
    # Fix SharedKernel references
    if ($content -match "using SharedKernel" -and $content -notmatch "using Shared\.Domain\.Common") {
        $content = $content -replace "using SharedKernel[^;]*;", "using Shared.Domain.Common;"
        $modified = $true
    }
    
    # Fix ValueObject references
    if ($content -match ": ValueObject" -and $content -notmatch "using Shared\.Domain\.Common") {
        $content = $content -replace "(using System[^;]*;)", "`$1`nusing Shared.Domain.Common;"
        $modified = $true
    }
    
    if ($modified) {
        Set-Content -Path $commFile.FullName -Value $content -Encoding UTF8
        Write-Host "  Fixed references in $($commFile.Name)" -ForegroundColor Green
    }
}

Write-Host "Critical build issues fix completed!" -ForegroundColor Green
