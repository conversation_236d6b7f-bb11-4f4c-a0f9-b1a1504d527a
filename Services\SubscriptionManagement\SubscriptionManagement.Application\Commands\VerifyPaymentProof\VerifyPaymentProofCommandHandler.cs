using MediatR;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.Commands.ActivateSubscriptionFromPaymentProof;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Interfaces;

namespace SubscriptionManagement.Application.Commands.VerifyPaymentProof
{
    public class VerifyPaymentProofCommandHandler : IRequestHandler<VerifyPaymentProofCommand, bool>
    {
        private readonly ISubscriptionPaymentProofRepository _paymentProofRepository;
        private readonly ISubscriptionRepository _subscriptionRepository;
        private readonly IMediator _mediator;
        private readonly ILogger<VerifyPaymentProofCommandHandler> _logger;

        public VerifyPaymentProofCommandHandler(
            ISubscriptionPaymentProofRepository paymentProofRepository,
            ISubscriptionRepository subscriptionRepository,
            IMediator mediator,
            ILogger<VerifyPaymentProofCommandHandler> logger)
        {
            _paymentProofRepository = paymentProofRepository;
            _subscriptionRepository = subscriptionRepository;
            _mediator = mediator;
            _logger = logger;
        }

        public async Task<bool> Handle(VerifyPaymentProofCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Verifying payment proof {PaymentProofId} by admin {AdminUserId}",
                request.PaymentProofId, request.VerifiedByUserId);

            // Get payment proof
            var paymentProof = await _paymentProofRepository.GetByIdWithSubscriptionAsync(request.PaymentProofId, cancellationToken);
            if (paymentProof == null)
            {
                throw new ArgumentException($"Payment proof with ID {request.PaymentProofId} not found");
            }

            if (!paymentProof.CanBeVerified())
            {
                throw new InvalidOperationException($"Payment proof cannot be verified in its current status: {paymentProof.Status}");
            }

            try
            {
                // Verify the payment proof
                paymentProof.Verify(request.VerifiedByUserId, request.VerificationNotes);

                // Save changes
                await _paymentProofRepository.UpdateAsync(paymentProof, cancellationToken);

                // Activate/extend subscription based on verified payment
                var activationCommand = new ActivateSubscriptionFromPaymentProofCommand
                {
                    PaymentProofId = request.PaymentProofId,
                    SubscriptionId = paymentProof.SubscriptionId,
                    PaymentAmount = paymentProof.Amount.Amount,
                    Currency = paymentProof.Amount.Currency,
                    VerifiedByUserId = request.VerifiedByUserId,
                    VerifiedAt = paymentProof.VerifiedAt!.Value
                };

                var activationResult = await _mediator.Send(activationCommand, cancellationToken);
                if (!activationResult)
                {
                    _logger.LogWarning("Payment proof {PaymentProofId} was verified but subscription activation failed",
                        request.PaymentProofId);
                }

                _logger.LogInformation("Payment proof {PaymentProofId} verified successfully by admin {AdminUserId}",
                    request.PaymentProofId, request.VerifiedByUserId);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to verify payment proof {PaymentProofId}", request.PaymentProofId);
                throw;
            }
        }
    }
}
