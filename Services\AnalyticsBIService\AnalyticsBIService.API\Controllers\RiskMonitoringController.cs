using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Application.Queries.Risk;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace AnalyticsBIService.API.Controllers;

/// <summary>
/// Controller for risk monitoring and feedback analysis
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class RiskMonitoringController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<RiskMonitoringController> _logger;

    public RiskMonitoringController(IMediator mediator, ILogger<RiskMonitoringController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get comprehensive risk monitoring dashboard
    /// </summary>
    /// <returns>Risk monitoring dashboard data</returns>
    [HttpGet("dashboard")]
    public async Task<ActionResult<RiskMonitoringDto>> GetRiskMonitoringDashboard()
    {
        try
        {
            var userId = GetCurrentUserId();
            
            _logger.LogInformation("Getting risk monitoring dashboard for user {UserId}", userId);

            var query = new GetRiskMonitoringDashboardQuery(userId);
            var result = await _mediator.Send(query);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting risk monitoring dashboard");
            return StatusCode(500, "An error occurred while retrieving the risk monitoring dashboard");
        }
    }

    /// <summary>
    /// Get count of issue-flagged feedback
    /// </summary>
    /// <param name="entityId">Entity ID (optional)</param>
    /// <param name="entityType">Entity type (optional)</param>
    /// <param name="fromDate">Start date (optional)</param>
    /// <param name="toDate">End date (optional)</param>
    /// <returns>Count of issue-flagged feedback</returns>
    [HttpGet("issue-feedback-count")]
    public async Task<ActionResult<int>> GetIssueFlaggedFeedbackCount(
        [FromQuery] Guid? entityId = null,
        [FromQuery] string? entityType = null,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        try
        {
            _logger.LogInformation("Getting issue-flagged feedback count for entity {EntityId} of type {EntityType}", 
                entityId, entityType);

            var query = new GetIssueFlaggedFeedbackCountQuery(entityId, entityType, fromDate, toDate);
            var result = await _mediator.Send(query);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting issue-flagged feedback count");
            return StatusCode(500, "An error occurred while counting issue-flagged feedback");
        }
    }

    /// <summary>
    /// Detect red flag trends
    /// </summary>
    /// <param name="entityId">Entity ID (optional)</param>
    /// <param name="entityType">Entity type (optional)</param>
    /// <param name="lookbackDays">Number of days to look back (default: 30)</param>
    /// <returns>List of red flag trends</returns>
    [HttpGet("red-flag-trends")]
    public async Task<ActionResult<List<RedFlagTrendDto>>> GetRedFlagTrends(
        [FromQuery] Guid? entityId = null,
        [FromQuery] string? entityType = null,
        [FromQuery] int lookbackDays = 30)
    {
        try
        {
            _logger.LogInformation("Detecting red flag trends for entity {EntityId} of type {EntityType}", 
                entityId, entityType);

            var query = new GetRedFlagTrendsQuery(entityId, entityType, lookbackDays);
            var result = await _mediator.Send(query);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error detecting red flag trends");
            return StatusCode(500, "An error occurred while detecting red flag trends");
        }
    }

    /// <summary>
    /// Get entity performance rankings
    /// </summary>
    /// <param name="entityType">Entity type (Carrier, Broker, Shipper)</param>
    /// <param name="topCount">Number of top entities to return (default: 10)</param>
    /// <returns>List of entity performance rankings</returns>
    [HttpGet("entity-rankings/{entityType}")]
    public async Task<ActionResult<List<EntityPerformanceRankingDto>>> GetEntityPerformanceRankings(
        string entityType,
        [FromQuery] int topCount = 10)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(entityType))
            {
                return BadRequest("Entity type is required");
            }

            _logger.LogInformation("Getting entity performance rankings for type {EntityType}", entityType);

            var query = new GetEntityPerformanceRankingsQuery(entityType, topCount);
            var result = await _mediator.Send(query);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting entity performance rankings for type {EntityType}", entityType);
            return StatusCode(500, "An error occurred while retrieving entity performance rankings");
        }
    }

    /// <summary>
    /// Assess risk for a specific entity
    /// </summary>
    /// <param name="entityId">Entity ID</param>
    /// <param name="entityType">Entity type</param>
    /// <returns>Risk assessment for the entity</returns>
    [HttpGet("risk-assessment/{entityId}")]
    public async Task<ActionResult<RiskAssessmentDto>> GetEntityRiskAssessment(
        Guid entityId,
        [FromQuery] string entityType)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(entityType))
            {
                return BadRequest("Entity type is required");
            }

            _logger.LogInformation("Assessing risk for entity {EntityId} of type {EntityType}", 
                entityId, entityType);

            var query = new GetEntityRiskAssessmentQuery(entityId, entityType);
            var result = await _mediator.Send(query);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assessing risk for entity {EntityId}", entityId);
            return StatusCode(500, "An error occurred while assessing entity risk");
        }
    }

    /// <summary>
    /// Get active risk alerts
    /// </summary>
    /// <returns>List of active risk alerts</returns>
    [HttpGet("active-alerts")]
    public async Task<ActionResult<List<RiskAlertDto>>> GetActiveRiskAlerts()
    {
        try
        {
            var userId = GetCurrentUserId();
            
            _logger.LogInformation("Getting active risk alerts for user {UserId}", userId);

            var query = new GetActiveRiskAlertsQuery(userId);
            var result = await _mediator.Send(query);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active risk alerts");
            return StatusCode(500, "An error occurred while retrieving active risk alerts");
        }
    }

    /// <summary>
    /// Get active risk alerts for a specific user (admin only)
    /// </summary>
    /// <param name="userId">Target user ID</param>
    /// <returns>List of active risk alerts for the user</returns>
    [HttpGet("active-alerts/user/{userId}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<List<RiskAlertDto>>> GetUserActiveRiskAlerts(Guid userId)
    {
        try
        {
            _logger.LogInformation("Admin getting active risk alerts for user {UserId}", userId);

            var query = new GetActiveRiskAlertsQuery(userId);
            var result = await _mediator.Send(query);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active risk alerts for user {UserId}", userId);
            return StatusCode(500, "An error occurred while retrieving active risk alerts");
        }
    }

    /// <summary>
    /// Get comprehensive risk analysis for all entity types (admin only)
    /// </summary>
    /// <returns>Comprehensive risk analysis data</returns>
    [HttpGet("comprehensive-analysis")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<object>> GetComprehensiveRiskAnalysis()
    {
        try
        {
            _logger.LogInformation("Getting comprehensive risk analysis");

            // Get data for all entity types
            var carrierRankings = await _mediator.Send(new GetEntityPerformanceRankingsQuery("Carrier", 20));
            var brokerRankings = await _mediator.Send(new GetEntityPerformanceRankingsQuery("Broker", 20));
            var shipperRankings = await _mediator.Send(new GetEntityPerformanceRankingsQuery("Shipper", 20));
            var redFlagTrends = await _mediator.Send(new GetRedFlagTrendsQuery());
            var activeAlerts = await _mediator.Send(new GetActiveRiskAlertsQuery());

            var analysis = new
            {
                GeneratedAt = DateTime.UtcNow,
                CarrierRankings = carrierRankings,
                BrokerRankings = brokerRankings,
                ShipperRankings = shipperRankings,
                RedFlagTrends = redFlagTrends,
                ActiveAlerts = activeAlerts,
                Summary = new
                {
                    TotalHighRiskEntities = carrierRankings.Count(r => r.RiskLevel == "High") +
                                          brokerRankings.Count(r => r.RiskLevel == "High") +
                                          shipperRankings.Count(r => r.RiskLevel == "High"),
                    TotalRedFlagTrends = redFlagTrends.Count,
                    TotalCriticalAlerts = activeAlerts.Count(a => a.Severity == "Critical")
                }
            };

            return Ok(analysis);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting comprehensive risk analysis");
            return StatusCode(500, "An error occurred while retrieving comprehensive risk analysis");
        }
    }

    // Helper methods
    private Guid? GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return Guid.TryParse(userIdClaim, out var userId) ? userId : null;
    }
}
