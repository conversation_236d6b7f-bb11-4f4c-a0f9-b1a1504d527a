# Analytics & BI Service Environment Configuration
# Copy this file to .env and update the values for your environment

#==============================================================================
# ENVIRONMENT CONFIGURATION
#==============================================================================
ENVIRONMENT=development
VERSION=latest
SERVICE_NAME=analytics-bi-service
NAMESPACE=tli-analytics

#==============================================================================
# DATABASE CONFIGURATION
#==============================================================================
# TimescaleDB/PostgreSQL Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=tli_analytics_dev
POSTGRES_USER=timescale
POSTGRES_PASSWORD=your_secure_postgres_password_here
POSTGRES_SSL_MODE=disable
POSTGRES_MAX_CONNECTIONS=100
POSTGRES_COMMAND_TIMEOUT=30

# Database Connection String (auto-generated from above values)
DATABASE_CONNECTION_STRING=Host=${POSTGRES_HOST};Port=${POSTGRES_PORT};Database=${POSTGRES_DB};User Id=${POSTGRES_USER};Password=${POSTGRES_PASSWORD};Include Error Detail=false;Pooling=true;MinPoolSize=5;MaxPoolSize=${POSTGRES_MAX_CONNECTIONS};CommandTimeout=${POSTGRES_COMMAND_TIMEOUT};SslMode=${POSTGRES_SSL_MODE}

#==============================================================================
# REDIS CONFIGURATION
#==============================================================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_secure_redis_password_here
REDIS_DATABASE=0
REDIS_ABORT_CONNECT=false

# Redis Connection String (auto-generated from above values)
REDIS_CONNECTION_STRING=${REDIS_HOST}:${REDIS_PORT},password=${REDIS_PASSWORD},abortConnect=${REDIS_ABORT_CONNECT}

#==============================================================================
# JWT AUTHENTICATION CONFIGURATION
#==============================================================================
JWT_SECRET_KEY=your_jwt_secret_key_minimum_256_bits_long_replace_this_with_actual_secret
JWT_ISSUER=TLI.Analytics
JWT_AUDIENCE=TLI.Users
JWT_EXPIRY_MINUTES=60
JWT_VALIDATE_ISSUER=true
JWT_VALIDATE_AUDIENCE=true
JWT_VALIDATE_LIFETIME=true
JWT_VALIDATE_ISSUER_SIGNING_KEY=true

#==============================================================================
# API CONFIGURATION
#==============================================================================
API_PORT=5004
API_HOST=0.0.0.0
API_BASE_URL=https://analytics-api.tli-platform.com
ASPNETCORE_ENVIRONMENT=${ENVIRONMENT}
ASPNETCORE_URLS=http://+:${API_PORT}
ASPNETCORE_FORWARDEDHEADERS_ENABLED=true

#==============================================================================
# CORS CONFIGURATION
#==============================================================================
CORS_ALLOWED_ORIGINS=https://app.tli-platform.com,https://admin.tli-platform.com,http://localhost:3000,http://localhost:3001
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOW_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOW_HEADERS=Authorization,Content-Type,Accept,Origin,X-Requested-With

#==============================================================================
# LOGGING CONFIGURATION
#==============================================================================
LOG_LEVEL=Information
LOG_LEVEL_MICROSOFT=Warning
LOG_LEVEL_MICROSOFT_HOSTING_LIFETIME=Information
LOG_LEVEL_ANALYTICS_SERVICE=Information
LOG_FILE_PATH=/app/logs/analytics-bi-service.log
LOG_RETENTION_DAYS=30

#==============================================================================
# ANALYTICS CONFIGURATION
#==============================================================================
ANALYTICS_REALTIME_PROCESSING=true
ANALYTICS_CACHE_EXPIRATION_MINUTES=30
ANALYTICS_BATCH_PROCESSING_INTERVAL=00:05:00
ANALYTICS_MAX_CONCURRENT_QUERIES=50
ANALYTICS_QUERY_TIMEOUT_SECONDS=30
ANALYTICS_ENABLE_METRICS_COLLECTION=true

#==============================================================================
# RATE LIMITING CONFIGURATION
#==============================================================================
RATE_LIMIT_ENABLE=true
RATE_LIMIT_REQUESTS_PER_MINUTE=1000
RATE_LIMIT_REQUESTS_PER_HOUR=10000
RATE_LIMIT_REQUESTS_PER_DAY=100000

#==============================================================================
# HEALTH CHECKS CONFIGURATION
#==============================================================================
HEALTHCHECKS_ENABLED=true
HEALTHCHECKS_DATABASE_TIMEOUT_SECONDS=10
HEALTHCHECKS_REDIS_TIMEOUT_SECONDS=5
HEALTHCHECKS_ENDPOINT=/api/analytics/health

#==============================================================================
# FILE STORAGE CONFIGURATION
#==============================================================================
FILE_STORAGE_PROVIDER=S3
FILE_STORAGE_LOCAL_PATH=/app/data/exports

# AWS S3 Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_aws_access_key_id_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key_here
S3_BUCKET_NAME=tli-analytics-exports
S3_ENDPOINT_URL=https://s3.amazonaws.com

#==============================================================================
# MONITORING CONFIGURATION
#==============================================================================
# Application Insights
APPINSIGHTS_INSTRUMENTATIONKEY=your_application_insights_instrumentation_key_here
APPINSIGHTS_CONNECTION_STRING=InstrumentationKey=${APPINSIGHTS_INSTRUMENTATIONKEY}

# Prometheus
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=9090
PROMETHEUS_ENDPOINT=/metrics
PROMETHEUS_SCRAPE_INTERVAL=15s

# Grafana
GRAFANA_PORT=3000
GRAFANA_ADMIN_USER=admin
GRAFANA_ADMIN_PASSWORD=your_secure_grafana_password_here
GRAFANA_ROOT_URL=http://localhost:${GRAFANA_PORT}

#==============================================================================
# DOCKER CONFIGURATION
#==============================================================================
DOCKER_REGISTRY=registry.tli-platform.com
DOCKER_IMAGE_NAME=tli/analytics-bi-service
DOCKER_IMAGE_TAG=${VERSION}
DOCKER_BUILD_ARGS=--no-cache

# TimescaleDB Docker Configuration
TIMESCALEDB_PORT=5432
TIMESCALEDB_IMAGE=timescale/timescaledb:latest-pg15

# Redis Docker Configuration
REDIS_IMAGE=redis:7-alpine

# Nginx Configuration
NGINX_HTTP_PORT=80
NGINX_HTTPS_PORT=443

#==============================================================================
# KUBERNETES CONFIGURATION
#==============================================================================
K8S_NAMESPACE=${NAMESPACE}
K8S_CLUSTER_NAME=tli-analytics-cluster
K8S_CONTEXT=tli-analytics-context
KUBECONFIG_PATH=~/.kube/config

# Resource Limits
K8S_CPU_REQUEST=200m
K8S_CPU_LIMIT=1000m
K8S_MEMORY_REQUEST=512Mi
K8S_MEMORY_LIMIT=1Gi
K8S_STORAGE_REQUEST=20Gi
K8S_STORAGE_CLASS=fast-ssd

#==============================================================================
# EXTERNAL SERVICES CONFIGURATION
#==============================================================================
# Email Service
EMAIL_SERVICE_PROVIDER=SendGrid
EMAIL_SERVICE_API_KEY=your_email_service_api_key_here
EMAIL_FROM_ADDRESS=<EMAIL>
EMAIL_FROM_NAME=TLI Analytics Service

# SMS Service
SMS_SERVICE_PROVIDER=Twilio
SMS_SERVICE_API_KEY=your_sms_service_api_key_here
SMS_SERVICE_ACCOUNT_SID=your_sms_account_sid_here
SMS_FROM_NUMBER=+**********

# Push Notification Service
PUSH_NOTIFICATION_PROVIDER=Firebase
PUSH_NOTIFICATION_SERVER_KEY=your_firebase_server_key_here
PUSH_NOTIFICATION_PROJECT_ID=your_firebase_project_id_here

#==============================================================================
# SECURITY CONFIGURATION
#==============================================================================
# Data Protection
DATA_PROTECTION_KEY=your_data_protection_key_here
DATA_PROTECTION_KEY_RING_PATH=/app/keys

# SSL/TLS Configuration
SSL_CERTIFICATE_PATH=/etc/ssl/certs/tli-platform.crt
SSL_CERTIFICATE_KEY_PATH=/etc/ssl/private/tli-platform.key
SSL_CERTIFICATE_PASSWORD=your_ssl_certificate_password_here

# Security Headers
SECURITY_REQUIRE_HTTPS=true
SECURITY_HSTS_MAX_AGE=********
SECURITY_HSTS_INCLUDE_SUBDOMAINS=true
SECURITY_CONTENT_TYPE_OPTIONS=nosniff
SECURITY_FRAME_OPTIONS=DENY
SECURITY_XSS_PROTECTION=1; mode=block

#==============================================================================
# FEATURE FLAGS
#==============================================================================
FEATURE_REAL_TIME_ANALYTICS=true
FEATURE_DATA_EXPORT=true
FEATURE_CUSTOM_REPORTS=true
FEATURE_BI_INTEGRATIONS=true
FEATURE_ADVANCED_ANALYTICS=true
FEATURE_PREDICTIVE_INSIGHTS=true
FEATURE_MACHINE_LEARNING=false
FEATURE_AUDIT_LOGGING=true
FEATURE_COMPLIANCE_REPORTING=true

#==============================================================================
# PERFORMANCE TUNING
#==============================================================================
# .NET Configuration
DOTNET_GC_SERVER=true
DOTNET_GC_CONCURRENT=true
DOTNET_GC_RETAIN_VM=true
DOTNET_THREAD_POOL_MIN_WORKER_THREADS=50
DOTNET_THREAD_POOL_MIN_COMPLETION_PORT_THREADS=50

# Cache Configuration
CACHE_DEFAULT_EXPIRATION=00:30:00
CACHE_SLIDING_EXPIRATION=00:15:00
CACHE_ABSOLUTE_EXPIRATION=01:00:00
CACHE_SIZE_LIMIT=1000

#==============================================================================
# BACKUP AND DISASTER RECOVERY
#==============================================================================
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=tli-analytics-backups
BACKUP_ENCRYPTION_KEY=your_backup_encryption_key_here

#==============================================================================
# COMPLIANCE AND AUDIT
#==============================================================================
AUDIT_ENABLED=true
AUDIT_INCLUDE_REQUEST_BODY=false
AUDIT_INCLUDE_RESPONSE_BODY=false
AUDIT_RETENTION_DAYS=90

# GDPR Compliance
GDPR_ENABLED=true
GDPR_DATA_RETENTION_DAYS=2555  # 7 years
GDPR_ANONYMIZATION_ENABLED=true

#==============================================================================
# DEVELOPMENT CONFIGURATION
#==============================================================================
# Development-specific settings (only used in development environment)
DEV_ENABLE_SWAGGER=true
DEV_ENABLE_DETAILED_ERRORS=true
DEV_ENABLE_DEVELOPER_EXCEPTION_PAGE=true
DEV_ENABLE_BROWSER_LINK=false
DEV_HOT_RELOAD_ENABLED=true

#==============================================================================
# TESTING CONFIGURATION
#==============================================================================
# Test-specific settings
TEST_DATABASE_NAME=tli_analytics_test
TEST_REDIS_DATABASE=1
TEST_ENABLE_INTEGRATION_TESTS=true
TEST_ENABLE_PERFORMANCE_TESTS=false
TEST_PARALLEL_EXECUTION=true

#==============================================================================
# THIRD-PARTY INTEGRATIONS
#==============================================================================
# BI Tool Integrations
TABLEAU_SERVER_URL=https://tableau.tli-platform.com
TABLEAU_USERNAME=tableau_user
TABLEAU_PASSWORD=your_tableau_password_here

POWERBI_TENANT_ID=your_powerbi_tenant_id_here
POWERBI_CLIENT_ID=your_powerbi_client_id_here
POWERBI_CLIENT_SECRET=your_powerbi_client_secret_here

LOOKER_BASE_URL=https://looker.tli-platform.com
LOOKER_CLIENT_ID=your_looker_client_id_here
LOOKER_CLIENT_SECRET=your_looker_client_secret_here

#==============================================================================
# WEBHOOK CONFIGURATION
#==============================================================================
WEBHOOK_ENABLED=true
WEBHOOK_SECRET=your_webhook_secret_here
WEBHOOK_TIMEOUT_SECONDS=30
WEBHOOK_RETRY_ATTEMPTS=3
WEBHOOK_RETRY_DELAY_SECONDS=5

#==============================================================================
# LICENSING
#==============================================================================
LICENSE_KEY=your_license_key_here
LICENSE_VALIDATION_URL=https://license.tli-platform.com/validate
LICENSE_CHECK_INTERVAL=24:00:00  # 24 hours
