using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using TripManagement.Application.Commands.ReportException;
using TripManagement.Application.Commands.ResolveException;
using TripManagement.Application.Commands.DetectExceptions;
using TripManagement.Application.DTOs;
using TripManagement.Application.Interfaces;
using TripManagement.Domain.Enums;
using TripManagement.Infrastructure.Persistence;
using TripManagement.Infrastructure.Repositories;
using Xunit;

namespace TripManagement.Tests.Integration;

public class ExceptionHandlingIntegrationTests : IDisposable
{
    private readonly TripManagementDbContext _context;
    private readonly ITripRepository _tripRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly Mock<IMessageBroker> _messageBrokerMock;
    private readonly Mock<INotificationService> _notificationServiceMock;
    private readonly Mock<IExceptionAnalysisService> _exceptionAnalysisServiceMock;

    public ExceptionHandlingIntegrationTests()
    {
        var options = new DbContextOptionsBuilder<TripManagementDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new TripManagementDbContext(options);
        _tripRepository = new TripRepository(_context);
        _unitOfWork = new UnitOfWork(_context);
        _messageBrokerMock = new Mock<IMessageBroker>();
        _notificationServiceMock = new Mock<INotificationService>();
        _exceptionAnalysisServiceMock = new Mock<IExceptionAnalysisService>();
    }

    [Fact]
    public async Task ReportException_EndToEnd_Should_Work_Successfully()
    {
        // Arrange
        var trip = await CreateTestTrip();
        var reportedBy = Guid.NewGuid();

        var analysisResult = new ExceptionAnalysisResult
        {
            SuggestedResolution = "Contact customer to reschedule delivery",
            RecommendedActions = new List<string> { "Call customer", "Update ETA" },
            RequiresHumanIntervention = true,
            EstimatedResolutionTime = TimeSpan.FromHours(2)
        };

        _exceptionAnalysisServiceMock
            .Setup(e => e.AnalyzeExceptionAsync(It.IsAny<ExceptionType>(), It.IsAny<string>(), It.IsAny<TripManagement.Domain.Entities.Trip>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(analysisResult);

        var loggerMock = new Mock<ILogger<ReportExceptionCommandHandler>>();
        var handler = new ReportExceptionCommandHandler(
            _tripRepository,
            _unitOfWork,
            loggerMock.Object,
            _messageBrokerMock.Object,
            _notificationServiceMock.Object,
            _exceptionAnalysisServiceMock.Object);

        var command = new ReportExceptionCommand
        {
            TripId = trip.Id,
            ReportedBy = reportedBy,
            ExceptionType = ExceptionType.Delay,
            Description = "Traffic jam causing significant delay",
            Location = new LocationDto
            {
                Latitude = 39.0,
                Longitude = -76.0,
                Address = "Highway I-95",
                Timestamp = DateTime.UtcNow
            },
            Severity = ExceptionSeverity.High,
            RequiresImmediateAttention = true,
            SuggestedResolution = "Find alternate route"
        };

        // Act
        var exceptionId = await handler.Handle(command, CancellationToken.None);

        // Assert
        exceptionId.Should().NotBeEmpty();

        var updatedTrip = await _tripRepository.GetByIdAsync(trip.Id);
        updatedTrip!.Exceptions.Should().HaveCount(1);
        
        var exception = updatedTrip.Exceptions.First();
        exception.ExceptionType.Should().Be(ExceptionType.Delay);
        exception.Description.Should().Be("Traffic jam causing significant delay");
        exception.IsResolved.Should().BeFalse();

        // Verify exception analysis service was called
        _exceptionAnalysisServiceMock.Verify(e => e.AnalyzeExceptionAsync(
            ExceptionType.Delay,
            "Traffic jam causing significant delay",
            It.IsAny<TripManagement.Domain.Entities.Trip>(),
            It.IsAny<CancellationToken>()), Times.Once);

        // Verify notifications were sent
        _notificationServiceMock.Verify(n => n.SendNotificationAsync(
            trip.CarrierId,
            It.IsAny<string>(),
            It.IsAny<string>(),
            It.IsAny<CancellationToken>()), Times.Once);

        // Verify message broker was called
        _messageBrokerMock.Verify(m => m.PublishAsync(
            "trip.exception_reported",
            It.IsAny<object>(),
            It.IsAny<CancellationToken>()), Times.Once);

        // Verify critical alert was published for high severity
        _messageBrokerMock.Verify(m => m.PublishAsync(
            "alert.critical_exception",
            It.IsAny<object>(),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task ResolveException_EndToEnd_Should_Work_Successfully()
    {
        // Arrange
        var trip = await CreateTestTripWithException();
        var exception = trip.Exceptions.First();

        var loggerMock = new Mock<ILogger<ResolveExceptionCommandHandler>>();
        var handler = new ResolveExceptionCommandHandler(
            _tripRepository,
            _unitOfWork,
            loggerMock.Object,
            _messageBrokerMock.Object,
            _notificationServiceMock.Object);

        var command = new ResolveExceptionCommand
        {
            TripId = trip.Id,
            ExceptionId = exception.Id,
            Resolution = "Alternate route found, delay reduced to 15 minutes",
            ResolvedBy = "<EMAIL>",
            ResolvedAt = DateTime.UtcNow,
            Notes = "Customer notified of new ETA"
        };

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeTrue();

        var updatedTrip = await _tripRepository.GetByIdAsync(trip.Id);
        var updatedException = updatedTrip!.Exceptions.First(e => e.Id == exception.Id);
        
        updatedException.IsResolved.Should().BeTrue();
        updatedException.Resolution.Should().Be("Alternate route found, delay reduced to 15 minutes");

        // Verify notifications were sent to carrier and driver
        _notificationServiceMock.Verify(n => n.SendNotificationAsync(
            trip.CarrierId,
            "Exception Resolved",
            It.IsAny<string>(),
            It.IsAny<CancellationToken>()), Times.Once);

        // Verify message broker was called
        _messageBrokerMock.Verify(m => m.PublishAsync(
            "trip.exception_resolved",
            It.IsAny<object>(),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task DetectExceptions_DelayDetection_Should_Work_Successfully()
    {
        // Arrange
        var delayedTrip = await CreateDelayedTrip();
        var onTimeTrip = await CreateTestTrip();

        var loggerMock = new Mock<ILogger<DetectExceptionsCommandHandler>>();
        var handler = new DetectExceptionsCommandHandler(
            _tripRepository,
            loggerMock.Object,
            _messageBrokerMock.Object);

        var rules = new List<ExceptionDetectionRule>
        {
            new()
            {
                Type = ExceptionDetectionType.DelayDetection,
                Parameters = new Dictionary<string, object> { ["delayThresholdMinutes"] = 30 },
                IsEnabled = true
            }
        };

        var command = new DetectExceptionsCommand
        {
            TripId = null, // Check all trips
            Rules = rules
        };

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().HaveCount(1); // Only the delayed trip should be detected
        
        var detectedDelay = result.First();
        detectedDelay.TripId.Should().Be(delayedTrip.Id);
        detectedDelay.DetectionType.Should().Be(ExceptionDetectionType.DelayDetection);
        detectedDelay.Severity.Should().Be(ExceptionSeverity.Medium);
        detectedDelay.Description.Should().Contain("delayed");

        // Verify message broker was called
        _messageBrokerMock.Verify(m => m.PublishAsync(
            "trip.exceptions_detected",
            It.IsAny<object>(),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task DetectExceptions_ExtendedStop_Should_Work_Successfully()
    {
        // Arrange
        var trip = await CreateTestTripWithExtendedStop();

        var loggerMock = new Mock<ILogger<DetectExceptionsCommandHandler>>();
        var handler = new DetectExceptionsCommandHandler(
            _tripRepository,
            loggerMock.Object,
            _messageBrokerMock.Object);

        var rules = new List<ExceptionDetectionRule>
        {
            new()
            {
                Type = ExceptionDetectionType.ExtendedStop,
                Parameters = new Dictionary<string, object> { ["maxStopDurationMinutes"] = 60 },
                IsEnabled = true
            }
        };

        var command = new DetectExceptionsCommand
        {
            TripId = trip.Id,
            Rules = rules
        };

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().HaveCount(1);
        
        var detectedExtendedStop = result.First();
        detectedExtendedStop.TripId.Should().Be(trip.Id);
        detectedExtendedStop.DetectionType.Should().Be(ExceptionDetectionType.ExtendedStop);
        detectedExtendedStop.Description.Should().Contain("extended period");
    }

    [Fact]
    public async Task DetectExceptions_CommunicationLoss_Should_Work_Successfully()
    {
        // Arrange
        var trip = await CreateTestTripWithOldLocationUpdate();

        var loggerMock = new Mock<ILogger<DetectExceptionsCommandHandler>>();
        var handler = new DetectExceptionsCommandHandler(
            _tripRepository,
            loggerMock.Object,
            _messageBrokerMock.Object);

        var rules = new List<ExceptionDetectionRule>
        {
            new()
            {
                Type = ExceptionDetectionType.CommunicationLoss,
                Parameters = new Dictionary<string, object> { ["maxSilenceMinutes"] = 30 },
                IsEnabled = true
            }
        };

        var command = new DetectExceptionsCommand
        {
            TripId = trip.Id,
            Rules = rules
        };

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().HaveCount(1);
        
        var detectedCommunicationLoss = result.First();
        detectedCommunicationLoss.TripId.Should().Be(trip.Id);
        detectedCommunicationLoss.DetectionType.Should().Be(ExceptionDetectionType.CommunicationLoss);
        detectedCommunicationLoss.Description.Should().Contain("No location updates");
    }

    private async Task<TripManagement.Domain.Entities.Trip> CreateTestTrip()
    {
        var startLocation = new TripManagement.Domain.ValueObjects.Location(40.7128, -74.0060, DateTime.UtcNow, address: "New York, NY");
        var endLocation = new TripManagement.Domain.ValueObjects.Location(34.0522, -118.2437, DateTime.UtcNow, address: "Los Angeles, CA");
        var route = new TripManagement.Domain.ValueObjects.Route(startLocation, endLocation, estimatedDistanceKm: 3944);

        var trip = new TripManagement.Domain.Entities.Trip(
            Guid.NewGuid(),
            Guid.NewGuid(),
            route,
            DateTime.UtcNow.AddHours(1),
            DateTime.UtcNow.AddHours(41),
            "Test trip for exceptions",
            false,
            3944m);

        await _tripRepository.AddAsync(trip);
        await _unitOfWork.SaveChangesAsync();

        return trip;
    }

    private async Task<TripManagement.Domain.Entities.Trip> CreateTestTripWithException()
    {
        var trip = await CreateTestTrip();
        
        var location = new TripManagement.Domain.ValueObjects.Location(39.0, -76.0, DateTime.UtcNow, address: "Highway I-95");
        trip.AddException(ExceptionType.Delay, "Test exception for resolution", location);
        
        _tripRepository.Update(trip);
        await _unitOfWork.SaveChangesAsync();

        return trip;
    }

    private async Task<TripManagement.Domain.Entities.Trip> CreateDelayedTrip()
    {
        var trip = await CreateTestTrip();
        
        // Set estimated end time to past to simulate delay
        var pastEndTime = DateTime.UtcNow.AddHours(-2);
        trip.UpdateEstimatedEndTime(pastEndTime);
        
        _tripRepository.Update(trip);
        await _unitOfWork.SaveChangesAsync();

        return trip;
    }

    private async Task<TripManagement.Domain.Entities.Trip> CreateTestTripWithExtendedStop()
    {
        var trip = await CreateTestTrip();
        
        var startLocation = new TripManagement.Domain.ValueObjects.Location(40.7128, -74.0060, DateTime.UtcNow, address: "New York, NY");
        var stop = new TripManagement.Domain.Entities.TripStop(
            trip.Id,
            TripStopType.Pickup,
            1,
            startLocation,
            DateTime.UtcNow.AddHours(-3)); // Arrived 3 hours ago

        stop.MarkArrived();
        stop.UpdateActualArrival(DateTime.UtcNow.AddHours(-3));
        trip.AddStop(stop);
        
        _tripRepository.Update(trip);
        await _unitOfWork.SaveChangesAsync();

        return trip;
    }

    private async Task<TripManagement.Domain.Entities.Trip> CreateTestTripWithOldLocationUpdate()
    {
        var trip = await CreateTestTrip();
        
        // Add old location update (2 hours ago)
        var oldLocation = new TripManagement.Domain.ValueObjects.Location(39.0, -76.0, DateTime.UtcNow.AddHours(-2), address: "Old location");
        trip.UpdateLocation(oldLocation);
        
        _tripRepository.Update(trip);
        await _unitOfWork.SaveChangesAsync();

        return trip;
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
