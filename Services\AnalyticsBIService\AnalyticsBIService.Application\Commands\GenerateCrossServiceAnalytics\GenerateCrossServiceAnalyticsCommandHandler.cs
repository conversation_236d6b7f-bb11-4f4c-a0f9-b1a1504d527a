using MediatR;
using Microsoft.Extensions.Logging;
using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Application.Interfaces;

namespace AnalyticsBIService.Application.Commands.GenerateCrossServiceAnalytics;

public class GenerateCrossServiceAnalyticsCommandHandler : IRequestHandler<GenerateCrossServiceAnalyticsCommand, CrossServiceAnalyticsDto>
{
    private readonly ICrossServiceDataCollector _dataCollector;
    private readonly ICorrelationAnalysisService _correlationService;
    private readonly IInsightGenerationService _insightService;
    private readonly ICrossServiceAnalyticsRepository _analyticsRepository;
    private readonly ILogger<GenerateCrossServiceAnalyticsCommandHandler> _logger;

    public GenerateCrossServiceAnalyticsCommandHandler(
        ICrossServiceDataCollector dataCollector,
        ICorrelationAnalysisService correlationService,
        IInsightGenerationService insightService,
        ICrossServiceAnalyticsRepository analyticsRepository,
        ILogger<GenerateCrossServiceAnalyticsCommandHandler> logger)
    {
        _dataCollector = dataCollector;
        _correlationService = correlationService;
        _insightService = insightService;
        _analyticsRepository = analyticsRepository;
        _logger = logger;
    }

    public async Task<CrossServiceAnalyticsDto> Handle(GenerateCrossServiceAnalyticsCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Generating cross-service analytics for services: {Services} by user {UserId}",
            string.Join(", ", request.Services), request.RequestedBy);

        try
        {
            // Validate services
            await ValidateServices(request.Services, cancellationToken);

            // Collect data from all services
            var serviceData = await CollectServiceData(request, cancellationToken);

            // Generate correlations if requested
            var correlations = new List<ServiceCorrelationDto>();
            if (request.IncludeCorrelations)
            {
                correlations = await GenerateCorrelations(serviceData, request.CorrelationThreshold, cancellationToken);
            }

            // Generate insights if requested
            var insights = new List<CrossServiceInsightDto>();
            if (request.IncludeInsights)
            {
                insights = await GenerateInsights(serviceData, correlations, request, cancellationToken);
            }

            // Create analytics result
            var analytics = new CrossServiceAnalyticsDto
            {
                Id = Guid.NewGuid(),
                GeneratedAt = DateTime.UtcNow,
                DateRange = request.DateRange,
                Services = request.Services,
                ServiceData = serviceData,
                Correlations = correlations,
                Insights = insights,
                Summary = GenerateSummary(serviceData, correlations, insights)
            };

            // Save results if requested
            if (request.SaveResults)
            {
                await SaveAnalyticsResults(analytics, request.ResultsName, request.RequestedBy, cancellationToken);
            }

            _logger.LogInformation("Successfully generated cross-service analytics for {ServiceCount} services with {CorrelationCount} correlations and {InsightCount} insights",
                request.Services.Count, correlations.Count, insights.Count);

            return analytics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating cross-service analytics for services: {Services}",
                string.Join(", ", request.Services));
            throw;
        }
    }

    private async Task ValidateServices(List<string> services, CancellationToken cancellationToken)
    {
        var validServices = new List<string>
        {
            "UserManagement", "OrderManagement", "TripManagement", "NetworkFleetManagement",
            "FinancialPayment", "CommunicationNotification", "AnalyticsBIService",
            "DataStorage", "MobileWorkflow", "AuditCompliance"
        };

        var invalidServices = services.Where(s => !validServices.Contains(s)).ToList();
        if (invalidServices.Any())
        {
            throw new ArgumentException($"Invalid services specified: {string.Join(", ", invalidServices)}");
        }
    }

    private async Task<Dictionary<string, ServiceAnalyticsDataDto>> CollectServiceData(
        GenerateCrossServiceAnalyticsCommand request,
        CancellationToken cancellationToken)
    {
        var serviceData = new Dictionary<string, ServiceAnalyticsDataDto>();

        foreach (var service in request.Services)
        {
            try
            {
                _logger.LogDebug("Collecting data from service: {Service}", service);

                var data = await _dataCollector.CollectServiceAnalyticsAsync(
                    service,
                    request.DateRange,
                    request.Metrics,
                    (request.ServiceSpecificParameters?.GetValueOrDefault(service) as Dictionary<string, object>) ?? new Dictionary<string, object>(),
                    request.Depth,
                    cancellationToken);

                serviceData[service] = data;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to collect data from service {Service}", service);

                // Create placeholder data for failed service
                serviceData[service] = new ServiceAnalyticsDataDto
                {
                    ServiceName = service,
                    HealthStatus = "Error",
                    LastUpdated = DateTime.UtcNow,
                    DataPointCount = 0,
                    Metrics = new Dictionary<string, decimal> { { "Error", 1 } }
                };
            }
        }

        return serviceData;
    }

    private async Task<List<ServiceCorrelationDto>> GenerateCorrelations(
        Dictionary<string, ServiceAnalyticsDataDto> serviceData,
        decimal threshold,
        CancellationToken cancellationToken)
    {
        var correlations = new List<ServiceCorrelationDto>();

        // Generate correlations between all service pairs
        var services = serviceData.Keys.ToList();
        for (int i = 0; i < services.Count; i++)
        {
            for (int j = i + 1; j < services.Count; j++)
            {
                var service1 = services[i];
                var service2 = services[j];

                var serviceCorrelations = await _correlationService.AnalyzeServiceCorrelationsAsync(
                    serviceData[service1],
                    serviceData[service2],
                    threshold,
                    cancellationToken);

                correlations.AddRange(serviceCorrelations);
            }
        }

        return correlations.Where(c => Math.Abs(c.CorrelationCoefficient) >= threshold)
                          .OrderByDescending(c => Math.Abs(c.CorrelationCoefficient))
                          .ToList();
    }

    private async Task<List<CrossServiceInsightDto>> GenerateInsights(
        Dictionary<string, ServiceAnalyticsDataDto> serviceData,
        List<ServiceCorrelationDto> correlations,
        GenerateCrossServiceAnalyticsCommand request,
        CancellationToken cancellationToken)
    {
        var insights = new List<CrossServiceInsightDto>();

        // Generate service health insights
        insights.AddRange(await GenerateServiceHealthInsights(serviceData, cancellationToken));

        // Generate correlation insights
        if (correlations.Any())
        {
            insights.AddRange(await GenerateCorrelationInsights(correlations, cancellationToken));
        }

        // Generate performance insights
        insights.AddRange(await GeneratePerformanceInsights(serviceData, request.Metrics, cancellationToken));

        // Generate trend insights
        insights.AddRange(await GenerateTrendInsights(serviceData, request.DateRange, cancellationToken));

        // Generate predictive insights if requested
        if (request.IncludePredictiveAnalytics)
        {
            insights.AddRange(await GeneratePredictiveInsights(serviceData, correlations, cancellationToken));
        }

        // Generate focus area insights if specified
        if (request.FocusAreas.Any())
        {
            insights.AddRange(await GenerateFocusAreaInsights(serviceData, request.FocusAreas, cancellationToken));
        }

        return insights.OrderByDescending(i => i.Importance).ToList();
    }

    private CrossServiceSummaryDto GenerateSummary(
        Dictionary<string, ServiceAnalyticsDataDto> serviceData,
        List<ServiceCorrelationDto> correlations,
        List<CrossServiceInsightDto> insights)
    {
        var healthyServices = serviceData.Values.Count(s => s.HealthStatus == "Healthy");
        var totalDataPoints = serviceData.Values.Sum(s => s.DataPointCount);
        var strongCorrelations = correlations.Count(c => Math.Abs(c.CorrelationCoefficient) > 0.7m);
        var keyInsights = insights.Count(i => i.Importance == "High");

        var overallHealthScore = healthyServices > 0 ?
            $"{(decimal)healthyServices / serviceData.Count * 100:F1}%" : "0%";

        var criticalIssues = insights.Where(i => i.Importance == "High" && i.Title.Contains("Issue"))
                                   .Select(i => i.Title)
                                   .ToList();

        var opportunities = insights.Where(i => i.Importance == "High" && i.Title.Contains("Opportunity"))
                                  .Select(i => i.Title)
                                  .ToList();

        return new CrossServiceSummaryDto
        {
            TotalServices = serviceData.Count,
            TotalDataPoints = totalDataPoints,
            StrongCorrelations = strongCorrelations,
            KeyInsights = keyInsights,
            OverallHealthScore = overallHealthScore,
            CriticalIssues = criticalIssues,
            Opportunities = opportunities
        };
    }

    private async Task SaveAnalyticsResults(
        CrossServiceAnalyticsDto analytics,
        string? resultsName,
        Guid requestedBy,
        CancellationToken cancellationToken)
    {
        var name = resultsName ?? $"Cross-Service Analytics - {DateTime.UtcNow:yyyy-MM-dd HH:mm}";
        await _analyticsRepository.SaveAnalyticsResultsAsync(analytics, name, requestedBy, cancellationToken);
    }

    // Insight generation methods
    private async Task<List<CrossServiceInsightDto>> GenerateServiceHealthInsights(
        Dictionary<string, ServiceAnalyticsDataDto> serviceData,
        CancellationToken cancellationToken)
    {
        var insights = new List<CrossServiceInsightDto>();

        var unhealthyServices = serviceData.Where(s => s.Value.HealthStatus != "Healthy").ToList();
        if (unhealthyServices.Any())
        {
            insights.Add(new CrossServiceInsightDto
            {
                InsightType = "Health",
                Title = "Service Health Issues Detected",
                Description = $"{unhealthyServices.Count} services are experiencing health issues",
                Importance = "High",
                AffectedServices = unhealthyServices.Select(s => s.Key).ToList(),
                Recommendations = new List<string>
                {
                    "Investigate service health issues immediately",
                    "Check service logs and monitoring dashboards",
                    "Consider scaling or restarting affected services"
                }
            });
        }

        return insights;
    }

    private async Task<List<CrossServiceInsightDto>> GenerateCorrelationInsights(
        List<ServiceCorrelationDto> correlations,
        CancellationToken cancellationToken)
    {
        var insights = new List<CrossServiceInsightDto>();

        var strongPositiveCorrelations = correlations.Where(c => c.CorrelationCoefficient > 0.8m).ToList();
        if (strongPositiveCorrelations.Any())
        {
            insights.Add(new CrossServiceInsightDto
            {
                InsightType = "Correlation",
                Title = "Strong Positive Correlations Found",
                Description = $"Found {strongPositiveCorrelations.Count} strong positive correlations between services",
                Importance = "Medium",
                AffectedServices = strongPositiveCorrelations.SelectMany(c => new[] { c.Service1, c.Service2 }).Distinct().ToList(),
                Data = new Dictionary<string, object> { { "correlations", strongPositiveCorrelations } },
                Recommendations = new List<string>
                {
                    "Leverage these correlations for predictive analytics",
                    "Consider optimizing these service interactions"
                }
            });
        }

        return insights;
    }

    private async Task<List<CrossServiceInsightDto>> GeneratePerformanceInsights(
        Dictionary<string, ServiceAnalyticsDataDto> serviceData,
        List<string> metrics,
        CancellationToken cancellationToken)
    {
        var insights = new List<CrossServiceInsightDto>();

        // Analyze performance metrics across services
        foreach (var metric in metrics)
        {
            var servicesWithMetric = serviceData.Where(s => s.Value.Metrics.ContainsKey(metric)).ToList();
            if (servicesWithMetric.Count >= 2)
            {
                var values = servicesWithMetric.Select(s => s.Value.Metrics[metric]).ToList();
                var average = values.Average();
                var outliers = servicesWithMetric.Where(s => Math.Abs(s.Value.Metrics[metric] - average) > average * 0.5m).ToList();

                if (outliers.Any())
                {
                    insights.Add(new CrossServiceInsightDto
                    {
                        InsightType = "Performance",
                        Title = $"Performance Outliers in {metric}",
                        Description = $"{outliers.Count} services show significant deviation in {metric}",
                        Importance = "Medium",
                        AffectedServices = outliers.Select(o => o.Key).ToList(),
                        Data = new Dictionary<string, object>
                        {
                            { "metric", metric },
                            { "average", average },
                            { "outliers", outliers.ToDictionary(o => o.Key, o => o.Value.Metrics[metric]) }
                        },
                        Recommendations = new List<string>
                        {
                            $"Investigate {metric} performance in outlier services",
                            "Consider performance optimization strategies"
                        }
                    });
                }
            }
        }

        return insights;
    }

    private async Task<List<CrossServiceInsightDto>> GenerateTrendInsights(
        Dictionary<string, ServiceAnalyticsDataDto> serviceData,
        DateRangeDto dateRange,
        CancellationToken cancellationToken)
    {
        var insights = new List<CrossServiceInsightDto>();

        // Analyze trends across services
        var servicesWithTrends = serviceData.Where(s => s.Value.Trends.Any()).ToList();
        if (servicesWithTrends.Any())
        {
            insights.Add(new CrossServiceInsightDto
            {
                InsightType = "Trend",
                Title = "Cross-Service Trend Analysis",
                Description = $"Analyzed trends across {servicesWithTrends.Count} services over {dateRange.TotalDays} days",
                Importance = "Medium",
                AffectedServices = servicesWithTrends.Select(s => s.Key).ToList(),
                Data = new Dictionary<string, object> { { "trendCount", servicesWithTrends.Sum(s => s.Value.Trends.Count) } },
                Recommendations = new List<string>
                {
                    "Monitor trend patterns for early warning indicators",
                    "Use trend data for capacity planning"
                }
            });
        }

        return insights;
    }

    private async Task<List<CrossServiceInsightDto>> GeneratePredictiveInsights(
        Dictionary<string, ServiceAnalyticsDataDto> serviceData,
        List<ServiceCorrelationDto> correlations,
        CancellationToken cancellationToken)
    {
        var insights = new List<CrossServiceInsightDto>();

        // Generate predictive insights based on correlations and trends
        if (correlations.Any() && serviceData.Values.Any(s => s.Trends.Any()))
        {
            insights.Add(new CrossServiceInsightDto
            {
                InsightType = "Prediction",
                Title = "Predictive Analytics Opportunity",
                Description = "Strong correlations and trend data enable predictive analytics capabilities",
                Importance = "High",
                AffectedServices = serviceData.Keys.ToList(),
                Recommendations = new List<string>
                {
                    "Implement predictive models based on identified correlations",
                    "Set up automated alerts for predicted issues",
                    "Use predictions for proactive capacity planning"
                }
            });
        }

        return insights;
    }

    private async Task<List<CrossServiceInsightDto>> GenerateFocusAreaInsights(
        Dictionary<string, ServiceAnalyticsDataDto> serviceData,
        List<string> focusAreas,
        CancellationToken cancellationToken)
    {
        var insights = new List<CrossServiceInsightDto>();

        foreach (var focusArea in focusAreas)
        {
            // Generate insights specific to each focus area
            insights.Add(new CrossServiceInsightDto
            {
                InsightType = "FocusArea",
                Title = $"{focusArea} Analysis",
                Description = $"Focused analysis of {focusArea} across all services",
                Importance = "Medium",
                AffectedServices = serviceData.Keys.ToList(),
                Data = new Dictionary<string, object> { { "focusArea", focusArea } },
                Recommendations = new List<string>
                {
                    $"Deep dive into {focusArea} metrics",
                    $"Optimize {focusArea} performance across services"
                }
            });
        }

        return insights;
    }
}
