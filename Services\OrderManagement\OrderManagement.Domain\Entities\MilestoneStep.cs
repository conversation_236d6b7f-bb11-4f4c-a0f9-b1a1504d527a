using Shared.Domain.Common;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Domain.Entities;

/// <summary>
/// Entity representing a step within a milestone template
/// </summary>
public class MilestoneStep : BaseEntity
{
    public Guid MilestoneTemplateId { get; private set; }
    public string Name { get; private set; } = string.Empty;
    public string Description { get; private set; } = string.Empty;
    public int StepOrder { get; private set; }
    public decimal PayoutPercentage { get; private set; }
    public MilestoneStepType StepType { get; private set; }
    public bool IsRequired { get; private set; }
    public bool RequiresDocumentation { get; private set; }
    public bool RequiresApproval { get; private set; }
    public string? DocumentationRequirements { get; private set; }
    public string? ApprovalCriteria { get; private set; }
    public int? EstimatedDurationHours { get; private set; }
    public Dictionary<string, object>? StepMetadata { get; private set; }

    // Navigation property
    public MilestoneTemplate MilestoneTemplate { get; private set; } = null!;

    private MilestoneStep() { } // EF Constructor

    public MilestoneStep(
        Guid milestoneTemplateId,
        string name,
        string description,
        int stepOrder,
        decimal payoutPercentage,
        MilestoneStepType stepType,
        bool isRequired = true,
        bool requiresDocumentation = false,
        bool requiresApproval = false,
        string? documentationRequirements = null,
        string? approvalCriteria = null,
        int? estimatedDurationHours = null,
        Dictionary<string, object>? stepMetadata = null)
    {
        if (payoutPercentage < 0 || payoutPercentage > 100)
            throw new ArgumentException("Payout percentage must be between 0 and 100", nameof(payoutPercentage));

        if (stepOrder < 1)
            throw new ArgumentException("Step order must be greater than 0", nameof(stepOrder));

        MilestoneTemplateId = milestoneTemplateId;
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        StepOrder = stepOrder;
        PayoutPercentage = payoutPercentage;
        StepType = stepType;
        IsRequired = isRequired;
        RequiresDocumentation = requiresDocumentation;
        RequiresApproval = requiresApproval;
        DocumentationRequirements = documentationRequirements;
        ApprovalCriteria = approvalCriteria;
        EstimatedDurationHours = estimatedDurationHours;
        StepMetadata = stepMetadata;
    }

    public void UpdateDetails(
        string name,
        string description,
        decimal payoutPercentage,
        bool isRequired = true,
        bool requiresDocumentation = false,
        bool requiresApproval = false,
        string? documentationRequirements = null,
        string? approvalCriteria = null,
        int? estimatedDurationHours = null,
        Dictionary<string, object>? stepMetadata = null)
    {
        if (payoutPercentage < 0 || payoutPercentage > 100)
            throw new ArgumentException("Payout percentage must be between 0 and 100", nameof(payoutPercentage));

        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        PayoutPercentage = payoutPercentage;
        IsRequired = isRequired;
        RequiresDocumentation = requiresDocumentation;
        RequiresApproval = requiresApproval;
        DocumentationRequirements = documentationRequirements;
        ApprovalCriteria = approvalCriteria;
        EstimatedDurationHours = estimatedDurationHours;
        StepMetadata = stepMetadata;
    }

    public void UpdateOrder(int newOrder)
    {
        if (newOrder < 1)
            throw new ArgumentException("Step order must be greater than 0", nameof(newOrder));

        StepOrder = newOrder;
    }

    public void UpdatePayoutPercentage(decimal newPercentage)
    {
        if (newPercentage < 0 || newPercentage > 100)
            throw new ArgumentException("Payout percentage must be between 0 and 100", nameof(newPercentage));

        PayoutPercentage = newPercentage;
    }

    // Factory methods for common step types
    public static MilestoneStep CreatePickupStep(
        Guid milestoneTemplateId,
        decimal payoutPercentage = 20m,
        bool requiresDocumentation = true)
    {
        return new MilestoneStep(
            milestoneTemplateId,
            "Pickup Completed",
            "Load has been picked up from origin location",
            1,
            payoutPercentage,
            MilestoneStepType.Pickup,
            true,
            requiresDocumentation,
            false,
            "Pickup receipt, load photos, BOL signature",
            null,
            2);
    }

    public static MilestoneStep CreateInTransitStep(
        Guid milestoneTemplateId,
        decimal payoutPercentage = 60m,
        int estimatedHours = 24)
    {
        return new MilestoneStep(
            milestoneTemplateId,
            "In Transit",
            "Load is in transit to destination",
            2,
            payoutPercentage,
            MilestoneStepType.InTransit,
            true,
            false,
            false,
            null,
            null,
            estimatedHours);
    }

    public static MilestoneStep CreateDeliveryStep(
        Guid milestoneTemplateId,
        decimal payoutPercentage = 20m,
        bool requiresApproval = true)
    {
        return new MilestoneStep(
            milestoneTemplateId,
            "Delivery Completed",
            "Load has been delivered to destination",
            3,
            payoutPercentage,
            MilestoneStepType.Delivery,
            true,
            true,
            requiresApproval,
            "Delivery receipt, POD, customer signature",
            "Customer confirmation of delivery completion",
            2);
    }
}
