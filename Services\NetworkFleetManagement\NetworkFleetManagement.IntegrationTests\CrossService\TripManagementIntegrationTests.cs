using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Entities;
using NetworkFleetManagement.Domain.Repositories;
using NetworkFleetManagement.IntegrationTests.TestFixtures;
using System.Net.Http.Json;
using Xunit;

namespace NetworkFleetManagement.IntegrationTests.CrossService;

[Collection("NetworkFleet")]
public class TripManagementIntegrationTests : IClassFixture<NetworkFleetTestFixture>
{
    private readonly NetworkFleetTestFixture _fixture;
    private readonly HttpClient _client;
    private readonly Guid _testUserId = Guid.NewGuid();

    public TripManagementIntegrationTests(NetworkFleetTestFixture fixture)
    {
        _fixture = fixture;
        _client = _fixture.CreateClient();
        
        _client.DefaultRequestHeaders.Add("X-User-Id", _testUserId.ToString());
        _client.DefaultRequestHeaders.Add("X-User-Role", "Broker");
    }

    [Fact]
    public async Task TripCompletion_UpdatesPartnerPerformanceMetrics()
    {
        // Arrange
        await _fixture.ResetDatabaseAsync();
        var partnerId = await CreateTestPartnerAsync();

        // Simulate trip completion event from Trip Management service
        var tripCompletionData = new
        {
            TripId = Guid.NewGuid(),
            PartnerId = partnerId,
            CompletedAt = DateTime.UtcNow,
            OnTimeDelivery = true,
            CustomerRating = 4.8m,
            DeliveryTime = TimeSpan.FromHours(8.5),
            Issues = new string[0]
        };

        // Act - Simulate processing trip completion event
        using var scope = _fixture.Services.CreateScope();
        var repository = scope.ServiceProvider.GetRequiredService<IPreferredPartnerRepository>();
        
        var partner = await repository.GetByIdAsync(partnerId);
        partner.Should().NotBeNull();

        // Update performance metrics based on trip completion
        partner!.UpdatePerformanceMetrics(
            tripCompletionData.CustomerRating,
            tripCompletionData.OnTimeDelivery ? 1.0m : 0.0m,
            tripCompletionData.CustomerRating,
            4.5m, // Communication rating
            (decimal)tripCompletionData.DeliveryTime.TotalHours);

        await repository.UpdateAsync(partner);

        // Assert
        var updatedPartner = await repository.GetByIdAsync(partnerId);
        updatedPartner.Should().NotBeNull();
        updatedPartner!.PerformanceMetrics.OverallRating.Should().BeGreaterThan(4.0m);
        updatedPartner.PerformanceMetrics.TotalOrders.Should().BeGreaterThan(0);
        updatedPartner.LastCollaborationDate.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromMinutes(1));
    }

    [Fact]
    public async Task TripAssignment_UsesPreferredPartnerPriority()
    {
        // Arrange
        await _fixture.ResetDatabaseAsync();
        await SeedPreferredPartnersWithPrioritiesAsync();

        var tripAssignmentRequest = new
        {
            Route = "Route1",
            LoadType = "Dry Van",
            PickupDate = DateTime.UtcNow.AddDays(1),
            RequiredRating = 4.0m
        };

        // Act - Test auto-assignment for trip
        var response = await _client.PostAsJsonAsync("/api/v1/preferredpartners/test-auto-assignment", new TestAutoAssignmentRequest
        {
            PartnerType = "Carrier",
            Route = tripAssignmentRequest.Route,
            LoadType = tripAssignmentRequest.LoadType,
            MinRating = tripAssignmentRequest.RequiredRating,
            MaxResults = 3
        });

        // Assert
        response.Should().BeSuccessful();
        
        var eligiblePartners = await response.Content.ReadFromJsonAsync<List<PreferredPartnerSummaryDto>>();
        eligiblePartners.Should().NotBeNull();
        eligiblePartners!.Should().NotBeEmpty();

        // Verify partners are returned in priority order
        for (int i = 0; i < eligiblePartners.Count - 1; i++)
        {
            var current = eligiblePartners[i];
            var next = eligiblePartners[i + 1];
            
            // Higher preference level or lower priority number should come first
            if (current.PreferenceLevel == next.PreferenceLevel)
            {
                current.Priority.Should().BeLessOrEqualTo(next.Priority);
            }
        }
    }

    [Fact]
    public async Task TripCancellation_UpdatesPartnerCancellationMetrics()
    {
        // Arrange
        await _fixture.ResetDatabaseAsync();
        var partnerId = await CreateTestPartnerAsync();

        // Simulate trip cancellation event
        var cancellationData = new
        {
            TripId = Guid.NewGuid(),
            PartnerId = partnerId,
            CancelledAt = DateTime.UtcNow,
            CancellationReason = "Vehicle breakdown",
            CancelledBy = "Carrier"
        };

        // Act - Process cancellation
        using var scope = _fixture.Services.CreateScope();
        var repository = scope.ServiceProvider.GetRequiredService<IPreferredPartnerRepository>();
        
        var partner = await repository.GetByIdAsync(partnerId);
        partner.Should().NotBeNull();

        // Update cancellation metrics
        var currentMetrics = partner!.PerformanceMetrics;
        partner.UpdatePerformanceMetrics(
            currentMetrics.OverallRating * 0.95m, // Slight penalty for cancellation
            currentMetrics.OnTimePerformance,
            currentMetrics.QualityScore,
            currentMetrics.CommunicationRating,
            currentMetrics.AverageResponseTime);

        // Increment cancelled orders
        var performanceRecord = new PartnerPerformanceRecord
        {
            PreferredPartnerId = partnerId,
            Rating = currentMetrics.OverallRating,
            OnTimeDelivery = false,
            QualityScore = currentMetrics.QualityScore,
            CommunicationRating = currentMetrics.CommunicationRating,
            ResponseTime = currentMetrics.AverageResponseTime,
            RecordedAt = DateTime.UtcNow,
            IsCancellation = true
        };

        await repository.AddPerformanceRecordAsync(performanceRecord);

        // Assert
        var updatedPartner = await repository.GetByIdAsync(partnerId);
        updatedPartner.Should().NotBeNull();
        updatedPartner!.PerformanceMetrics.CancelledOrders.Should().BeGreaterThan(0);
    }

    [Fact]
    public async Task TripRouteOptimization_ConsidersPreferredRoutes()
    {
        // Arrange
        await _fixture.ResetDatabaseAsync();
        await SeedPartnersWithRoutePreferencesAsync();

        // Act - Get partners for specific route
        var response = await _client.GetAsync("/api/v1/preferredpartners/carriers?route=Route1&activeOnly=true");

        // Assert
        response.Should().BeSuccessful();
        
        var carriers = await response.Content.ReadFromJsonAsync<List<PreferredPartnerSummaryDto>>();
        carriers.Should().NotBeNull();
        carriers!.Should().NotBeEmpty();

        // All returned carriers should have Route1 in their preferred routes or not in excluded routes
        carriers.Should().AllSatisfy(c => 
        {
            c.PartnerType.Should().Be("Carrier");
            c.Status.Should().Be("Active");
        });
    }

    [Fact]
    public async Task TripPerformanceTracking_GeneratesAnalytics()
    {
        // Arrange
        await _fixture.ResetDatabaseAsync();
        var partnerId = await CreateTestPartnerAsync();

        // Simulate multiple trip completions over time
        await SimulateMultipleTripCompletionsAsync(partnerId);

        // Act - Get partner analytics
        var response = await _client.GetAsync($"/api/v1/preferredpartners/{partnerId}/analytics?fromDate=2024-01-01&toDate=2024-12-31");

        // Assert
        response.Should().BeSuccessful();
        
        var analytics = await response.Content.ReadFromJsonAsync<PartnerAnalyticsDto>();
        analytics.Should().NotBeNull();
        analytics!.PreferredPartnerId.Should().Be(partnerId);
        analytics.TotalCollaborations.Should().BeGreaterThan(0);
        analytics.PerformanceMetrics.Should().NotBeNull();
        analytics.PerformanceMetrics.TotalOrders.Should().BeGreaterThan(0);
    }

    private async Task<Guid> CreateTestPartnerAsync()
    {
        var partner = PreferredPartner.Create(
            _testUserId,
            Guid.NewGuid(),
            PreferredPartnerType.Carrier,
            PreferenceLevel.High,
            1);

        partner.Activate();
        partner.UpdateAutoAssignSettings(true, 4.0m,
            new List<string> { "Route1", "Route2" },
            new List<string> { "Dry Van", "Refrigerated" },
            new List<string>(),
            new List<string>());

        using var context = _fixture.GetDbContext();
        context.PreferredPartners.Add(partner);
        await context.SaveChangesAsync();

        return partner.Id;
    }

    private async Task SeedPreferredPartnersWithPrioritiesAsync()
    {
        using var context = _fixture.GetDbContext();

        var partners = new[]
        {
            CreatePartnerWithPriority(_testUserId, PreferredPartnerType.Carrier, PreferenceLevel.High, 1),
            CreatePartnerWithPriority(_testUserId, PreferredPartnerType.Carrier, PreferenceLevel.High, 2),
            CreatePartnerWithPriority(_testUserId, PreferredPartnerType.Carrier, PreferenceLevel.Medium, 1),
            CreatePartnerWithPriority(_testUserId, PreferredPartnerType.Carrier, PreferenceLevel.Medium, 2)
        };

        context.PreferredPartners.AddRange(partners);
        await context.SaveChangesAsync();
    }

    private async Task SeedPartnersWithRoutePreferencesAsync()
    {
        using var context = _fixture.GetDbContext();

        var partners = new[]
        {
            CreatePartnerWithRoutes(_testUserId, new[] { "Route1", "Route2" }, new string[0]),
            CreatePartnerWithRoutes(_testUserId, new[] { "Route1", "Route3" }, new string[0]),
            CreatePartnerWithRoutes(_testUserId, new[] { "Route2", "Route3" }, new[] { "Route1" })
        };

        context.PreferredPartners.AddRange(partners);
        await context.SaveChangesAsync();
    }

    private async Task SimulateMultipleTripCompletionsAsync(Guid partnerId)
    {
        using var scope = _fixture.Services.CreateScope();
        var repository = scope.ServiceProvider.GetRequiredService<IPreferredPartnerRepository>();

        var partner = await repository.GetByIdAsync(partnerId);
        if (partner == null) return;

        // Simulate 5 successful trips
        for (int i = 0; i < 5; i++)
        {
            partner.UpdatePerformanceMetrics(
                4.0m + (decimal)(new Random().NextDouble() * 1.0), // 4.0 to 5.0 rating
                0.9m, // 90% on-time
                4.5m,
                4.3m,
                8.0m); // 8 hours average
        }

        await repository.UpdateAsync(partner);
    }

    private PreferredPartner CreatePartnerWithPriority(Guid userId, PreferredPartnerType type, PreferenceLevel level, int priority)
    {
        var partner = PreferredPartner.Create(userId, Guid.NewGuid(), type, level, priority);
        partner.Activate();
        partner.UpdateAutoAssignSettings(true, 4.0m,
            new List<string> { "Route1", "Route2" },
            new List<string> { "Dry Van" },
            new List<string>(),
            new List<string>());
        return partner;
    }

    private PreferredPartner CreatePartnerWithRoutes(Guid userId, string[] preferredRoutes, string[] excludedRoutes)
    {
        var partner = PreferredPartner.Create(userId, Guid.NewGuid(), PreferredPartnerType.Carrier, PreferenceLevel.High, 1);
        partner.Activate();
        partner.UpdateAutoAssignSettings(true, 4.0m,
            preferredRoutes.ToList(),
            new List<string> { "Dry Van" },
            excludedRoutes.ToList(),
            new List<string>());
        return partner;
    }
}
