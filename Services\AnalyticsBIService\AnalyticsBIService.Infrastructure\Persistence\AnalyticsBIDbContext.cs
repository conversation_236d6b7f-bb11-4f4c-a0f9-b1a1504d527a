using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Infrastructure.Services;
using AnalyticsBIService.Infrastructure.Persistence.Configurations;
using Microsoft.EntityFrameworkCore;
using Shared.Domain.Common;

namespace AnalyticsBIService.Infrastructure.Persistence;

public class AnalyticsBIDbContext : DbContext
{
    public AnalyticsBIDbContext(DbContextOptions<AnalyticsBIDbContext> options) : base(options)
    {
    }

    // Entity sets
    public DbSet<AnalyticsEvent> AnalyticsEvents => Set<AnalyticsEvent>();
    public DbSet<Metric> Metrics => Set<Metric>();
    public DbSet<Dashboard> Dashboards => Set<Dashboard>();
    public DbSet<DashboardWidget> DashboardWidgets => Set<DashboardWidget>();
    public DbSet<Report> Reports => Set<Report>();
    public DbSet<ReportSection> ReportSections => Set<ReportSection>();
    public DbSet<ReportExport> ReportExports => Set<ReportExport>();
    public DbSet<Alert> Alerts => Set<Alert>();
    public DbSet<AlertRule> AlertRules => Set<AlertRule>();
    public DbSet<Threshold> Thresholds => Set<Threshold>();
    public DbSet<ReportTemplate> ReportTemplates => Set<ReportTemplate>();
    public DbSet<AnalyticsBIService.Domain.Entities.ReportExecution> ReportExecutions => Set<AnalyticsBIService.Domain.Entities.ReportExecution>();
    public DbSet<OrderData> OrderData => Set<OrderData>();
    public DbSet<TripData> TripData => Set<TripData>();
    public DbSet<ExportData> ExportData => Set<ExportData>();
    public DbSet<DataPipeline> DataPipelines => Set<DataPipeline>();

    // Additional entity sets referenced in the codebase
    public DbSet<AnalyticsBIService.Domain.Entities.ScheduledReport> ScheduledReports => Set<AnalyticsBIService.Domain.Entities.ScheduledReport>();
    public DbSet<AnalyticsBIService.Domain.Entities.CohortAnalysis> CohortAnalyses => Set<AnalyticsBIService.Domain.Entities.CohortAnalysis>();
    public DbSet<AnalyticsBIService.Domain.Entities.KPIMonitor> KPIMonitors => Set<AnalyticsBIService.Domain.Entities.KPIMonitor>();
    public DbSet<AnalyticsBIService.Domain.Entities.ThresholdRule> ThresholdRules => Set<AnalyticsBIService.Domain.Entities.ThresholdRule>();
    public DbSet<AnalyticsBIService.Domain.Entities.AlertConfiguration> AlertConfigurations => Set<AnalyticsBIService.Domain.Entities.AlertConfiguration>();
    public DbSet<AutomatedResponse> AutomatedResponses => Set<AutomatedResponse>();
    public DbSet<AnalyticsBIService.Domain.Entities.MobileAnalyticsConfig> MobileAnalyticsConfigs => Set<AnalyticsBIService.Domain.Entities.MobileAnalyticsConfig>();
    public DbSet<AnalyticsBIService.Domain.Entities.CustomReportTemplate> CustomReportTemplates => Set<AnalyticsBIService.Domain.Entities.CustomReportTemplate>();
    public DbSet<AnalyticsBIService.Domain.Entities.DataWarehouseConnection> DataWarehouseConnections => Set<AnalyticsBIService.Domain.Entities.DataWarehouseConnection>();
    public DbSet<AnalyticsBIService.Domain.Entities.ETLPipeline> ETLPipelines => Set<AnalyticsBIService.Domain.Entities.ETLPipeline>();
    public DbSet<AnalyticsBIService.Domain.Entities.MobileCrash> MobileCrashes => Set<AnalyticsBIService.Domain.Entities.MobileCrash>();
    public DbSet<AnalyticsBIService.Domain.Entities.MobilePerformanceMetric> MobilePerformanceMetrics => Set<AnalyticsBIService.Domain.Entities.MobilePerformanceMetric>();
    public DbSet<AnalyticsBIService.Domain.Entities.MobileEvent> MobileEvents => Set<AnalyticsBIService.Domain.Entities.MobileEvent>();
    public DbSet<AnalyticsBIService.Domain.Entities.MobileSession> MobileSessions => Set<AnalyticsBIService.Domain.Entities.MobileSession>();
    public DbSet<DataExport> DataExports => Set<DataExport>();
    public DbSet<AnalyticsBIService.Domain.Entities.DataPipelineExecution> DataPipelineExecutions => Set<AnalyticsBIService.Domain.Entities.DataPipelineExecution>();
    public DbSet<AnalyticsBIService.Domain.Entities.SchemaMapping> SchemaMappings => Set<AnalyticsBIService.Domain.Entities.SchemaMapping>();
    public DbSet<AnalyticsBIService.Domain.Entities.DataSyncSchedule> DataSyncSchedules => Set<AnalyticsBIService.Domain.Entities.DataSyncSchedule>();
    public DbSet<AnalyticsBIService.Domain.Entities.DataSyncRecord> DataSyncRecords => Set<AnalyticsBIService.Domain.Entities.DataSyncRecord>();
    public DbSet<AnalyticsBIService.Domain.Entities.ETLSchedule> ETLSchedules => Set<AnalyticsBIService.Domain.Entities.ETLSchedule>();
    public DbSet<AnalyticsBIService.Domain.Entities.FunnelAnalysis> FunnelAnalyses => Set<AnalyticsBIService.Domain.Entities.FunnelAnalysis>();
    public DbSet<AnalyticsBIService.Domain.Entities.FunnelEvent> FunnelEvents => Set<AnalyticsBIService.Domain.Entities.FunnelEvent>();
    public DbSet<ETLExecution> ETLExecutions => Set<ETLExecution>();
    public DbSet<ScheduledExport> ScheduledExports => Set<ScheduledExport>();

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Apply configurations
        modelBuilder.ApplyConfiguration(new AnalyticsEventConfiguration());
        modelBuilder.ApplyConfiguration(new MetricConfiguration());
        modelBuilder.ApplyConfiguration(new DashboardConfiguration());
        modelBuilder.ApplyConfiguration(new DashboardWidgetConfiguration());
        modelBuilder.ApplyConfiguration(new ReportConfiguration());
        modelBuilder.ApplyConfiguration(new ReportSectionConfiguration());
        modelBuilder.ApplyConfiguration(new ReportExportConfiguration());
        modelBuilder.ApplyConfiguration(new AlertConfiguration());
        modelBuilder.ApplyConfiguration(new AlertRuleConfiguration());
        modelBuilder.ApplyConfiguration(new ThresholdConfiguration());
        modelBuilder.ApplyConfiguration(new ReportTemplateConfiguration());
        modelBuilder.ApplyConfiguration(new ReportExecutionConfiguration());
        modelBuilder.ApplyConfiguration(new OrderDataConfiguration());
        modelBuilder.ApplyConfiguration(new TripDataConfiguration());
        modelBuilder.ApplyConfiguration(new ExportDataConfiguration());
        modelBuilder.ApplyConfiguration(new DataPipelineConfiguration());

        // Configure schema
        modelBuilder.HasDefaultSchema("analytics");

        // Configure value objects
        ConfigureValueObjects(modelBuilder);

        // Configure indexes for performance
        ConfigureIndexes(modelBuilder);

        // Configure TimescaleDB hypertables
        ConfigureTimescaleDB(modelBuilder);
    }

    private void ConfigureValueObjects(ModelBuilder modelBuilder)
    {
        // Configure MetricValue value object
        modelBuilder.Entity<Metric>()
            .OwnsOne(m => m.Value, mv =>
            {
                mv.Property(v => v.Value).HasColumnName("metric_value").HasPrecision(18, 6);
                mv.Property(v => v.Type).HasColumnName("metric_type").HasConversion<string>();
                mv.Property(v => v.Unit).HasColumnName("metric_unit").HasMaxLength(50);
                mv.Property(v => v.Timestamp).HasColumnName("metric_timestamp");
                mv.Property(v => v.Metadata).HasColumnName("metric_metadata")
                    .HasConversion(
                        v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                        v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new Dictionary<string, object>());
            });

        // Configure TimePeriodValue value object
        modelBuilder.Entity<Metric>()
            .OwnsOne(m => m.Period, tp =>
            {
                tp.Property(p => p.StartDate).HasColumnName("period_start");
                tp.Property(p => p.EndDate).HasColumnName("period_end");
                tp.Property(p => p.Period).HasColumnName("period_type").HasConversion<string>();
            });

        // Configure KPITarget value object
        modelBuilder.Entity<Metric>()
            .OwnsOne(m => m.Target, kt =>
            {
                kt.Property(t => t.TargetValue).HasColumnName("target_value").HasPrecision(18, 6);
                kt.Property(t => t.MinThreshold).HasColumnName("min_threshold").HasPrecision(18, 6);
                kt.Property(t => t.MaxThreshold).HasColumnName("max_threshold").HasPrecision(18, 6);
                kt.Property(t => t.WarningThreshold).HasColumnName("warning_threshold").HasPrecision(18, 6);
                kt.Property(t => t.CriticalThreshold).HasColumnName("critical_threshold").HasPrecision(18, 6);
                kt.Property(t => t.Unit).HasColumnName("target_unit").HasMaxLength(50);
                kt.Property(t => t.IsHigherBetter).HasColumnName("is_higher_better");
            });

        // Configure TimePeriodValue for Reports
        modelBuilder.Entity<Report>()
            .OwnsOne(r => r.Period, tp =>
            {
                tp.Property(p => p.StartDate).HasColumnName("period_start");
                tp.Property(p => p.EndDate).HasColumnName("period_end");
                tp.Property(p => p.Period).HasColumnName("period_type").HasConversion<string>();
            });

        // Configure TimePeriodValue for DashboardWidget
        modelBuilder.Entity<DashboardWidget>()
            .OwnsOne(w => w.DefaultTimePeriod, tp =>
            {
                tp.Property(p => p.StartDate).HasColumnName("default_period_start");
                tp.Property(p => p.EndDate).HasColumnName("default_period_end");
                tp.Property(p => p.Period).HasColumnName("default_period_type").HasConversion<string>();
            });
    }

    private void ConfigureIndexes(ModelBuilder modelBuilder)
    {
        // AnalyticsEvent indexes
        modelBuilder.Entity<AnalyticsEvent>()
            .HasIndex(e => e.Timestamp)
            .HasDatabaseName("IX_AnalyticsEvents_Timestamp");

        modelBuilder.Entity<AnalyticsEvent>()
            .HasIndex(e => new { e.EventType, e.Timestamp })
            .HasDatabaseName("IX_AnalyticsEvents_EventType_Timestamp");

        modelBuilder.Entity<AnalyticsEvent>()
            .HasIndex(e => new { e.UserId, e.Timestamp })
            .HasDatabaseName("IX_AnalyticsEvents_UserId_Timestamp");

        modelBuilder.Entity<AnalyticsEvent>()
            .HasIndex(e => new { e.DataSource, e.Timestamp })
            .HasDatabaseName("IX_AnalyticsEvents_DataSource_Timestamp");

        // Metric indexes
        modelBuilder.Entity<Metric>()
            .HasIndex(m => new { m.Name, m.UserId })
            .HasDatabaseName("IX_Metrics_Name_UserId");

        modelBuilder.Entity<Metric>()
            .HasIndex(m => new { m.Category, m.Type })
            .HasDatabaseName("IX_Metrics_Category_Type");

        // Dashboard indexes
        modelBuilder.Entity<Dashboard>()
            .HasIndex(d => new { d.UserId, d.Type })
            .HasDatabaseName("IX_Dashboards_UserId_Type");

        // Report indexes
        modelBuilder.Entity<Report>()
            .HasIndex(r => new { r.GeneratedBy, r.Type })
            .HasDatabaseName("IX_Reports_GeneratedBy_Type");

        // Alert indexes
        modelBuilder.Entity<Alert>()
            .HasIndex(a => new { a.IsActive, a.Severity })
            .HasDatabaseName("IX_Alerts_IsActive_Severity");

        modelBuilder.Entity<Alert>()
            .HasIndex(a => a.TriggeredAt)
            .HasDatabaseName("IX_Alerts_TriggeredAt");
    }

    private void ConfigureTimescaleDB(ModelBuilder modelBuilder)
    {
        // Configure hypertables for time-series data
        // These will be created in migrations as raw SQL

        // AnalyticsEvents hypertable (partitioned by Timestamp)
        modelBuilder.Entity<AnalyticsEvent>()
            .ToTable("analytics_events")
            .HasComment("Hypertable partitioned by timestamp for time-series optimization");

        // Metrics hypertable (partitioned by CreatedAt)
        modelBuilder.Entity<Metric>()
            .ToTable("metrics")
            .HasComment("Hypertable partitioned by created_at for time-series optimization");

        // Alerts hypertable (partitioned by TriggeredAt)
        modelBuilder.Entity<Alert>()
            .ToTable("alerts")
            .HasComment("Hypertable partitioned by triggered_at for time-series optimization");
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        UpdateTimestamps();
        return await base.SaveChangesAsync(cancellationToken);
    }

    public override int SaveChanges()
    {
        UpdateTimestamps();
        return base.SaveChanges();
    }

    private void UpdateTimestamps()
    {
        var entries = ChangeTracker
            .Entries()
            .Where(e => e.Entity is BaseEntity && (
                e.State == EntityState.Added ||
                e.State == EntityState.Modified));

        foreach (var entityEntry in entries)
        {
            var entity = (BaseEntity)entityEntry.Entity;

            if (entityEntry.State == EntityState.Added)
            {
                // CreatedAt is set in the constructor, no need to update
            }

            if (entityEntry.State == EntityState.Modified)
            {
                // Use reflection to call protected SetUpdatedAt method
                var method = typeof(BaseEntity).GetMethod("SetUpdatedAt",
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                method?.Invoke(entity, null);
            }
        }
    }
}
