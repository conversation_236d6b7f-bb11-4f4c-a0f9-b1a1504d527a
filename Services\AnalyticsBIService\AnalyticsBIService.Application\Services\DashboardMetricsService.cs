using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Enums;
using AnalyticsBIService.Domain.ValueObjects;
using AnalyticsBIService.Domain.Repositories;
using AnalyticsBIService.Application.Interfaces;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace AnalyticsBIService.Application.Services;

/// <summary>
/// Service for calculating and caching dashboard metrics with data aggregation
/// </summary>
public interface IDashboardMetricsService
{
    Task<DashboardMetricsDto> GetDashboardMetricsAsync(Guid? userId, UserType? userType, CancellationToken cancellationToken = default);
    Task<decimal> CalculateAverageOrderFulfillmentTimeAsync(Guid? userId = null, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default);
    Task<List<MetricDataPointDto>> GetOrderFulfillmentTrendsAsync(Guid? userId = null, TimePeriod period = TimePeriod.Daily, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default);
    Task RefreshMetricsCacheAsync(CancellationToken cancellationToken = default);
    Task<Dictionary<string, decimal>> GetRealTimeMetricsAsync(Guid? userId = null, CancellationToken cancellationToken = default);
    Task ScheduleMetricsCalculationAsync(string metricName, TimeSpan interval, CancellationToken cancellationToken = default);
}

public class DashboardMetricsService : IDashboardMetricsService
{
    private readonly IMetricRepository _metricRepository;
    private readonly IAnalyticsEventRepository _analyticsEventRepository;
    private readonly ICacheService _cacheService;
    private readonly ILogger<DashboardMetricsService> _logger;

    // Cache keys
    private const string CACHE_KEY_PREFIX = "dashboard_metrics";
    private const string ORDER_FULFILLMENT_TIME_KEY = "avg_order_fulfillment_time";
    private const string REAL_TIME_METRICS_KEY = "real_time_metrics";

    // Cache expiration times
    private static readonly TimeSpan DefaultCacheExpiration = TimeSpan.FromMinutes(15);
    private static readonly TimeSpan RealTimeCacheExpiration = TimeSpan.FromMinutes(5);
    private static readonly TimeSpan IntensiveMetricsCacheExpiration = TimeSpan.FromHours(1);

    // In-memory cache for frequently accessed metrics
    private readonly ConcurrentDictionary<string, CachedMetric> _memoryCache;

    public DashboardMetricsService(
        IMetricRepository metricRepository,
        IAnalyticsEventRepository analyticsEventRepository,
        ICacheService cacheService,
        ILogger<DashboardMetricsService> logger)
    {
        _metricRepository = metricRepository;
        _analyticsEventRepository = analyticsEventRepository;
        _cacheService = cacheService;
        _logger = logger;
        _memoryCache = new ConcurrentDictionary<string, CachedMetric>();
    }

    public async Task<DashboardMetricsDto> GetDashboardMetricsAsync(Guid? userId, UserType? userType, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting dashboard metrics for user {UserId} of type {UserType}", userId, userType);

            var cacheKey = GenerateCacheKey("dashboard_metrics", userId, userType);

            // Try to get from cache first
            var cachedMetrics = await _cacheService.GetAsync<DashboardMetricsDto>(cacheKey, cancellationToken);
            if (cachedMetrics != null)
            {
                _logger.LogDebug("Dashboard metrics retrieved from cache for user {UserId}", userId);
                return cachedMetrics;
            }

            // Calculate metrics
            var metrics = new DashboardMetricsDto
            {
                UserId = userId,
                UserType = userType?.ToString(),
                GeneratedAt = DateTime.UtcNow,
                AverageOrderFulfillmentTime = await CalculateAverageOrderFulfillmentTimeAsync(userId, cancellationToken: cancellationToken),
                RealTimeMetrics = await GetRealTimeMetricsAsync(userId, cancellationToken),
                OrderFulfillmentTrends = await GetOrderFulfillmentTrendsAsync(userId, cancellationToken: cancellationToken)
            };

            // Add additional role-specific metrics
            await AddRoleSpecificMetricsAsync(metrics, userId, userType, cancellationToken);

            // Cache the results
            await _cacheService.SetAsync(cacheKey, metrics, DefaultCacheExpiration, cancellationToken);

            _logger.LogDebug("Dashboard metrics calculated and cached for user {UserId}", userId);
            return metrics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting dashboard metrics for user {UserId}", userId);
            throw;
        }
    }

    public async Task<decimal> CalculateAverageOrderFulfillmentTimeAsync(Guid? userId = null, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = GenerateCacheKey(ORDER_FULFILLMENT_TIME_KEY, userId, fromDate, toDate);

            // Check memory cache first for frequently accessed data
            if (_memoryCache.TryGetValue(cacheKey, out var cachedMetric) &&
                cachedMetric.ExpiresAt > DateTime.UtcNow)
            {
                return cachedMetric.Value;
            }

            // Check distributed cache
            var cachedValue = await _cacheService.GetAsync<decimal?>(cacheKey, cancellationToken);
            if (cachedValue.HasValue)
            {
                // Update memory cache
                _memoryCache.TryAdd(cacheKey, new CachedMetric
                {
                    Value = cachedValue.Value,
                    ExpiresAt = DateTime.UtcNow.Add(RealTimeCacheExpiration)
                });
                return cachedValue.Value;
            }

            _logger.LogDebug("Calculating average order fulfillment time for user {UserId}", userId);

            var startDate = fromDate ?? DateTime.UtcNow.AddDays(-30);
            var endDate = toDate ?? DateTime.UtcNow;

            // Get order fulfillment data from analytics events
            var fulfillmentEvents = await GetOrderFulfillmentEventsAsync(userId, startDate, endDate, cancellationToken);

            if (!fulfillmentEvents.Any())
            {
                _logger.LogDebug("No order fulfillment events found for user {UserId}", userId);
                return 0;
            }

            // Calculate average fulfillment time
            var averageFulfillmentTime = CalculateAverageFulfillmentTime(fulfillmentEvents);

            // Cache the result
            await _cacheService.SetAsync(cacheKey, averageFulfillmentTime, IntensiveMetricsCacheExpiration, cancellationToken);

            // Update memory cache
            _memoryCache.TryAdd(cacheKey, new CachedMetric
            {
                Value = averageFulfillmentTime,
                ExpiresAt = DateTime.UtcNow.Add(RealTimeCacheExpiration)
            });

            _logger.LogDebug("Average order fulfillment time calculated: {Time} hours for user {UserId}",
                averageFulfillmentTime, userId);

            return averageFulfillmentTime;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating average order fulfillment time for user {UserId}", userId);
            return 0;
        }
    }

    public async Task<List<MetricDataPointDto>> GetOrderFulfillmentTrendsAsync(Guid? userId = null, TimePeriod period = TimePeriod.Daily, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = GenerateCacheKey("fulfillment_trends", userId, period, fromDate, toDate);

            // Try cache first
            var cachedTrends = await _cacheService.GetAsync<List<MetricDataPointDto>>(cacheKey, cancellationToken);
            if (cachedTrends != null)
            {
                return cachedTrends;
            }

            _logger.LogDebug("Calculating order fulfillment trends for user {UserId}", userId);

            var startDate = fromDate ?? DateTime.UtcNow.AddDays(-30);
            var endDate = toDate ?? DateTime.UtcNow;

            var trends = new List<MetricDataPointDto>();
            var fulfillmentEvents = await GetOrderFulfillmentEventsAsync(userId, startDate, endDate, cancellationToken);

            if (fulfillmentEvents.Any())
            {
                trends = CalculateFulfillmentTrends(fulfillmentEvents, period, startDate, endDate);
            }

            // Cache the results
            await _cacheService.SetAsync(cacheKey, trends, DefaultCacheExpiration, cancellationToken);

            return trends;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating order fulfillment trends for user {UserId}", userId);
            return new List<MetricDataPointDto>();
        }
    }

    public async Task<Dictionary<string, decimal>> GetRealTimeMetricsAsync(Guid? userId = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = GenerateCacheKey(REAL_TIME_METRICS_KEY, userId);

            // Try cache first
            var cachedMetrics = await _cacheService.GetAsync<Dictionary<string, decimal>>(cacheKey, cancellationToken);
            if (cachedMetrics != null)
            {
                return cachedMetrics;
            }

            _logger.LogDebug("Calculating real-time metrics for user {UserId}", userId);

            var metrics = new Dictionary<string, decimal>();

            // Calculate various real-time metrics
            var now = DateTime.UtcNow;
            var last24Hours = now.AddHours(-24);
            var lastHour = now.AddHours(-1);

            // Active orders in last 24 hours
            var activeOrdersCount = await GetActiveOrdersCountAsync(userId, last24Hours, cancellationToken);
            metrics["active_orders_24h"] = activeOrdersCount;

            // Orders completed in last hour
            var completedOrdersLastHour = await GetCompletedOrdersCountAsync(userId, lastHour, cancellationToken);
            metrics["completed_orders_1h"] = completedOrdersLastHour;

            // Average response time
            var avgResponseTime = await CalculateAverageResponseTimeAsync(userId, last24Hours, cancellationToken);
            metrics["avg_response_time_hours"] = avgResponseTime;

            // Cache the results with shorter expiration for real-time data
            await _cacheService.SetAsync(cacheKey, metrics, RealTimeCacheExpiration, cancellationToken);

            return metrics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating real-time metrics for user {UserId}", userId);
            return new Dictionary<string, decimal>();
        }
    }

    public async Task RefreshMetricsCacheAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Refreshing metrics cache");

            // Clear memory cache
            _memoryCache.Clear();

            // Remove cached metrics by pattern
            await _cacheService.RemoveByPatternAsync($"{CACHE_KEY_PREFIX}*", cancellationToken);

            _logger.LogInformation("Metrics cache refreshed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error refreshing metrics cache");
            throw;
        }
    }

    public async Task ScheduleMetricsCalculationAsync(string metricName, TimeSpan interval, CancellationToken cancellationToken = default)
    {
        // This would integrate with a background job scheduler
        // For now, we'll log the scheduling request
        _logger.LogInformation("Scheduling metric calculation for {MetricName} with interval {Interval}",
            metricName, interval);

        // Implementation would depend on the background job system being used
        // Could use Hangfire, Quartz.NET, or similar
        await Task.CompletedTask;
    }

    // Private helper methods
    private async Task<List<OrderFulfillmentEvent>> GetOrderFulfillmentEventsAsync(Guid? userId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken)
    {
        var eventsResult = await _analyticsEventRepository.GetByDateRangeAsync(
            startDate, endDate, 1, int.MaxValue, cancellationToken);

        var events = eventsResult.Items
            .Where(e => e.EventName == "OrderCreated" || e.EventName == "OrderCompleted" || e.EventName == "TripCompleted");

        if (userId.HasValue)
        {
            events = events.Where(e => e.UserId == userId.Value);
        }

        var eventsList = events
            .OrderBy(e => e.Timestamp)
            .ToList();

        return ProcessOrderFulfillmentEvents(eventsList);
    }

    private List<OrderFulfillmentEvent> ProcessOrderFulfillmentEvents(List<AnalyticsEvent> events)
    {
        var fulfillmentEvents = new List<OrderFulfillmentEvent>();
        var orderEvents = new Dictionary<Guid, OrderFulfillmentEvent>();

        foreach (var analyticsEvent in events)
        {
            if (!analyticsEvent.EntityId.HasValue) continue;

            var orderId = analyticsEvent.EntityId.Value;

            if (analyticsEvent.EventName == "OrderCreated")
            {
                orderEvents[orderId] = new OrderFulfillmentEvent
                {
                    OrderId = orderId,
                    UserId = analyticsEvent.UserId,
                    CreatedAt = analyticsEvent.Timestamp
                };
            }
            else if ((analyticsEvent.EventName == "OrderCompleted" || analyticsEvent.EventName == "TripCompleted") &&
                     orderEvents.ContainsKey(orderId))
            {
                var orderEvent = orderEvents[orderId];
                orderEvent.CompletedAt = analyticsEvent.Timestamp;
                orderEvent.FulfillmentTimeHours = (decimal)(analyticsEvent.Timestamp - orderEvent.CreatedAt).TotalHours;
                fulfillmentEvents.Add(orderEvent);
                orderEvents.Remove(orderId);
            }
        }

        return fulfillmentEvents;
    }

    private decimal CalculateAverageFulfillmentTime(List<OrderFulfillmentEvent> fulfillmentEvents)
    {
        if (!fulfillmentEvents.Any()) return 0;

        var validEvents = fulfillmentEvents.Where(e => e.FulfillmentTimeHours > 0).ToList();
        if (!validEvents.Any()) return 0;

        return validEvents.Average(e => e.FulfillmentTimeHours);
    }

    private List<MetricDataPointDto> CalculateFulfillmentTrends(List<OrderFulfillmentEvent> fulfillmentEvents, TimePeriod period, DateTime startDate, DateTime endDate)
    {
        var trends = new List<MetricDataPointDto>();
        var groupedEvents = GroupEventsByPeriod(fulfillmentEvents, period, startDate, endDate);

        foreach (var group in groupedEvents.OrderBy(g => g.Key))
        {
            var avgTime = group.Value.Any() ? group.Value.Average(e => e.FulfillmentTimeHours) : 0;
            trends.Add(new MetricDataPointDto
            {
                Timestamp = group.Key,
                Value = avgTime,
                Label = FormatPeriodLabel(group.Key, period),
                MetricName = "Average Fulfillment Time",
                Unit = "hours"
            });
        }

        return trends;
    }

    private Dictionary<DateTime, List<OrderFulfillmentEvent>> GroupEventsByPeriod(List<OrderFulfillmentEvent> events, TimePeriod period, DateTime startDate, DateTime endDate)
    {
        var grouped = new Dictionary<DateTime, List<OrderFulfillmentEvent>>();

        var current = startDate;
        while (current <= endDate)
        {
            var periodStart = current;
            var periodEnd = period switch
            {
                TimePeriod.Hourly => current.AddHours(1),
                TimePeriod.Daily => current.AddDays(1),
                TimePeriod.Weekly => current.AddDays(7),
                TimePeriod.Monthly => current.AddMonths(1),
                _ => current.AddDays(1)
            };

            var eventsInPeriod = events
                .Where(e => e.CompletedAt >= periodStart && e.CompletedAt < periodEnd)
                .ToList();

            grouped[periodStart] = eventsInPeriod;

            current = periodEnd;
        }

        return grouped;
    }

    private async Task<decimal> GetActiveOrdersCountAsync(Guid? userId, DateTime since, CancellationToken cancellationToken)
    {
        var eventsResult = await _analyticsEventRepository.GetByDateRangeAsync(
            since, DateTime.UtcNow, 1, int.MaxValue, cancellationToken);

        var events = eventsResult.Items
            .Where(e => e.EventName == "OrderCreated" &&
                       e.EventType == AnalyticsEventType.BusinessTransaction);

        if (userId.HasValue)
        {
            events = events.Where(e => e.UserId == userId.Value);
        }

        return events.Count();
    }

    private async Task<decimal> GetCompletedOrdersCountAsync(Guid? userId, DateTime since, CancellationToken cancellationToken)
    {
        var eventsResult = await _analyticsEventRepository.GetByDateRangeAsync(
            since, DateTime.UtcNow, 1, int.MaxValue, cancellationToken);

        var events = eventsResult.Items
            .Where(e => (e.EventName == "OrderCompleted" || e.EventName == "TripCompleted") &&
                       e.EventType == AnalyticsEventType.BusinessTransaction);

        if (userId.HasValue)
        {
            events = events.Where(e => e.UserId == userId.Value);
        }

        return events.Count();
    }

    private async Task<decimal> CalculateAverageResponseTimeAsync(Guid? userId, DateTime since, CancellationToken cancellationToken)
    {
        // This would calculate average response time based on quote/response events
        // For now, return a placeholder calculation
        var eventsResult = await _analyticsEventRepository.GetByDateRangeAsync(
            since, DateTime.UtcNow, 1, int.MaxValue, cancellationToken);

        var responseEvents = eventsResult.Items
            .Where(e => e.EventName.Contains("Response") &&
                       e.Properties.ContainsKey("responseTime"))
            .ToList();

        if (!responseEvents.Any()) return 0;

        var responseTimes = responseEvents
            .Select(e => e.GetProperty<decimal>("responseTime"))
            .Where(rt => rt > 0)
            .ToList();

        return responseTimes.Any() ? responseTimes.Average() : 0;
    }

    private async Task AddRoleSpecificMetricsAsync(DashboardMetricsDto metrics, Guid? userId, UserType? userType, CancellationToken cancellationToken)
    {
        if (!userType.HasValue) return;

        switch (userType.Value)
        {
            case UserType.TransportCompany:
                await AddTransportCompanyMetricsAsync(metrics, userId, cancellationToken);
                break;
            case UserType.Broker:
                await AddBrokerMetricsAsync(metrics, userId, cancellationToken);
                break;
            case UserType.Carrier:
                await AddCarrierMetricsAsync(metrics, userId, cancellationToken);
                break;
            case UserType.Shipper:
                await AddShipperMetricsAsync(metrics, userId, cancellationToken);
                break;
        }
    }

    private async Task AddTransportCompanyMetricsAsync(DashboardMetricsDto metrics, Guid? userId, CancellationToken cancellationToken)
    {
        // Add transport company specific metrics
        var last30Days = DateTime.UtcNow.AddDays(-30);

        var eventsResult = await _analyticsEventRepository.GetByDateRangeAsync(
            last30Days, DateTime.UtcNow, 1, int.MaxValue, cancellationToken);

        var rfqCount = eventsResult.Items
            .Where(e => e.UserId == userId && e.EventName == "RFQCreated")
            .Count();

        metrics.RoleSpecificMetrics = new Dictionary<string, object>
        {
            ["active_rfqs"] = rfqCount,
            ["conversion_rate"] = await CalculateConversionRateAsync(userId, last30Days, cancellationToken)
        };
    }

    private async Task AddBrokerMetricsAsync(DashboardMetricsDto metrics, Guid? userId, CancellationToken cancellationToken)
    {
        var last30Days = DateTime.UtcNow.AddDays(-30);

        var eventsResult = await _analyticsEventRepository.GetByDateRangeAsync(
            last30Days, DateTime.UtcNow, 1, int.MaxValue, cancellationToken);

        var quoteCount = eventsResult.Items
            .Where(e => e.UserId == userId && e.EventName == "QuoteSubmitted")
            .Count();

        metrics.RoleSpecificMetrics = new Dictionary<string, object>
        {
            ["active_quotes"] = quoteCount,
            ["quote_success_rate"] = await CalculateQuoteSuccessRateAsync(userId, last30Days, cancellationToken)
        };
    }

    private async Task AddCarrierMetricsAsync(DashboardMetricsDto metrics, Guid? userId, CancellationToken cancellationToken)
    {
        var last30Days = DateTime.UtcNow.AddDays(-30);

        var eventsResult = await _analyticsEventRepository.GetByDateRangeAsync(
            last30Days, DateTime.UtcNow, 1, int.MaxValue, cancellationToken);

        var activeTrips = eventsResult.Items
            .Where(e => e.UserId == userId && e.EventName == "TripStarted")
            .Count();

        metrics.RoleSpecificMetrics = new Dictionary<string, object>
        {
            ["active_trips"] = activeTrips,
            ["on_time_delivery_rate"] = await CalculateOnTimeDeliveryRateAsync(userId, last30Days, cancellationToken)
        };
    }

    private async Task AddShipperMetricsAsync(DashboardMetricsDto metrics, Guid? userId, CancellationToken cancellationToken)
    {
        var last30Days = DateTime.UtcNow.AddDays(-30);

        var eventsResult = await _analyticsEventRepository.GetByDateRangeAsync(
            last30Days, DateTime.UtcNow, 1, int.MaxValue, cancellationToken);

        var activeShipments = eventsResult.Items
            .Where(e => e.UserId == userId && e.EventName == "ShipmentCreated")
            .Count();

        metrics.RoleSpecificMetrics = new Dictionary<string, object>
        {
            ["active_shipments"] = activeShipments,
            ["sla_compliance_rate"] = await CalculateSLAComplianceRateAsync(userId, last30Days, cancellationToken)
        };
    }

    private async Task<decimal> CalculateConversionRateAsync(Guid? userId, DateTime since, CancellationToken cancellationToken)
    {
        var eventsResult = await _analyticsEventRepository.GetByDateRangeAsync(
            since, DateTime.UtcNow, 1, int.MaxValue, cancellationToken);

        var rfqCount = eventsResult.Items
            .Where(e => e.UserId == userId && e.EventName == "RFQCreated")
            .Count();

        var convertedCount = eventsResult.Items
            .Where(e => e.UserId == userId && e.EventName == "OrderCreated")
            .Count();

        return rfqCount > 0 ? (decimal)convertedCount / rfqCount * 100 : 0;
    }

    private async Task<decimal> CalculateQuoteSuccessRateAsync(Guid? userId, DateTime since, CancellationToken cancellationToken)
    {
        var eventsResult = await _analyticsEventRepository.GetByDateRangeAsync(
            since, DateTime.UtcNow, 1, int.MaxValue, cancellationToken);

        var quoteCount = eventsResult.Items
            .Where(e => e.UserId == userId && e.EventName == "QuoteSubmitted")
            .Count();

        var acceptedCount = eventsResult.Items
            .Where(e => e.UserId == userId && e.EventName == "QuoteAccepted")
            .Count();

        return quoteCount > 0 ? (decimal)acceptedCount / quoteCount * 100 : 0;
    }

    private async Task<decimal> CalculateOnTimeDeliveryRateAsync(Guid? userId, DateTime since, CancellationToken cancellationToken)
    {
        var eventsResult = await _analyticsEventRepository.GetByDateRangeAsync(
            since, DateTime.UtcNow, 1, int.MaxValue, cancellationToken);

        var completedTrips = eventsResult.Items
            .Where(e => e.UserId == userId && e.EventName == "TripCompleted")
            .Count();

        var onTimeTrips = eventsResult.Items
            .Where(e => e.UserId == userId &&
                       e.EventName == "TripCompleted" &&
                       e.Properties.ContainsKey("onTime") &&
                       e.GetProperty<bool>("onTime"))
            .Count();

        return completedTrips > 0 ? (decimal)onTimeTrips / completedTrips * 100 : 0;
    }

    private async Task<decimal> CalculateSLAComplianceRateAsync(Guid? userId, DateTime since, CancellationToken cancellationToken)
    {
        var eventsResult = await _analyticsEventRepository.GetByDateRangeAsync(
            since, DateTime.UtcNow, 1, int.MaxValue, cancellationToken);

        var completedShipments = eventsResult.Items
            .Where(e => e.UserId == userId && e.EventName == "ShipmentCompleted")
            .Count();

        var slaCompliantShipments = eventsResult.Items
            .Where(e => e.UserId == userId &&
                       e.EventName == "ShipmentCompleted" &&
                       e.Properties.ContainsKey("slaCompliant") &&
                       e.GetProperty<bool>("slaCompliant"))
            .Count();

        return completedShipments > 0 ? (decimal)slaCompliantShipments / completedShipments * 100 : 0;
    }

    private string GenerateCacheKey(string prefix, params object?[] parameters)
    {
        var keyParts = new List<string> { CACHE_KEY_PREFIX, prefix };

        foreach (var param in parameters)
        {
            if (param != null)
            {
                keyParts.Add(param.ToString()!);
            }
        }

        return string.Join(":", keyParts);
    }

    private string FormatPeriodLabel(DateTime timestamp, TimePeriod period)
    {
        return period switch
        {
            TimePeriod.Hourly => timestamp.ToString("yyyy-MM-dd HH:00"),
            TimePeriod.Daily => timestamp.ToString("yyyy-MM-dd"),
            TimePeriod.Weekly => $"Week of {timestamp:yyyy-MM-dd}",
            TimePeriod.Monthly => timestamp.ToString("yyyy-MM"),
            _ => timestamp.ToString("yyyy-MM-dd")
        };
    }
}

// Supporting classes
public class OrderFulfillmentEvent
{
    public Guid OrderId { get; set; }
    public Guid? UserId { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime CompletedAt { get; set; }
    public decimal FulfillmentTimeHours { get; set; }
}

public class CachedMetric
{
    public decimal Value { get; set; }
    public DateTime ExpiresAt { get; set; }
}
