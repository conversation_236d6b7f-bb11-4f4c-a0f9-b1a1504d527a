using MediatR;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Exceptions;
using Shared.Messaging;

namespace SubscriptionManagement.Application.Commands.CreateSubscription
{
    public class CreateSubscriptionCommandHandler : IRequestHandler<CreateSubscriptionCommand, Guid>
    {
        private readonly ISubscriptionRepository _subscriptionRepository;
        private readonly IPlanRepository _planRepository;
        private readonly IPaymentService _paymentService;
        private readonly IMessageBroker _messageBroker;
        private readonly ISubscriptionMetricsService _metricsService;
        private readonly ILogger<CreateSubscriptionCommandHandler> _logger;

        public CreateSubscriptionCommandHandler(
            ISubscriptionRepository subscriptionRepository,
            IPlanRepository planRepository,
            IPaymentService paymentService,
            IMessageBroker messageBroker,
            ISubscriptionMetricsService metricsService,
            ILogger<CreateSubscriptionCommandHandler> logger)
        {
            _subscriptionRepository = subscriptionRepository;
            _planRepository = planRepository;
            _paymentService = paymentService;
            _messageBroker = messageBroker;
            _metricsService = metricsService;
            _logger = logger;
        }

        public async Task<Guid> Handle(CreateSubscriptionCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Creating subscription for user {UserId} with plan {PlanId}",
                request.UserId, request.PlanId);

            // Check if user already has an active subscription
            var existingSubscription = await _subscriptionRepository.GetByUserIdAsync(request.UserId);
            if (existingSubscription != null && existingSubscription.Status == Domain.Enums.SubscriptionStatus.Active)
            {
                throw new SubscriptionDomainException("User already has an active subscription");
            }

            // Get the plan
            var plan = await _planRepository.GetByIdAsync(request.PlanId);
            if (plan == null)
            {
                throw new SubscriptionDomainException("Plan not found");
            }

            if (!plan.IsActive)
            {
                throw new SubscriptionDomainException("Cannot create subscription for inactive plan");
            }

            // Validate payment method if provided
            if (!string.IsNullOrEmpty(request.PaymentMethodId))
            {
                var isValidPaymentMethod = await _paymentService.ValidatePaymentMethodAsync(
                    request.PaymentMethodId, request.UserId);

                if (!isValidPaymentMethod)
                {
                    throw new SubscriptionDomainException("Invalid payment method");
                }
            }

            // Create the subscription
            var subscription = new Subscription(
                request.UserId,
                plan,
                request.StartDate,
                request.TrialEndDate,
                request.AutoRenew,
                request.ProrationMode);

            // Save the subscription
            await _subscriptionRepository.AddAsync(subscription);

            // If not in trial and has payment method, process initial payment
            if (!subscription.IsInTrial() && !string.IsNullOrEmpty(request.PaymentMethodId))
            {
                try
                {
                    var paymentResult = await _paymentService.ProcessSubscriptionPaymentAsync(
                        subscription, request.PaymentMethodId);

                    if (paymentResult.IsSuccess)
                    {
                        subscription.Activate();
                        await _subscriptionRepository.UpdateAsync(subscription);

                        _logger.LogInformation("Subscription {SubscriptionId} activated after successful payment",
                            subscription.Id);
                    }
                    else
                    {
                        _logger.LogWarning("Payment failed for subscription {SubscriptionId}: {Error}",
                            subscription.Id, paymentResult.ErrorMessage);

                        // Subscription remains in pending status
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing payment for subscription {SubscriptionId}",
                        subscription.Id);

                    // Subscription remains in pending status
                }
            }
            else if (subscription.IsInTrial())
            {
                // Activate trial subscriptions immediately
                subscription.Activate();
                await _subscriptionRepository.UpdateAsync(subscription);

                _logger.LogInformation("Trial subscription {SubscriptionId} activated", subscription.Id);
            }

            // Record metrics
            _metricsService.RecordSubscriptionCreated(plan.Name, subscription.CurrentPrice.Amount, subscription.CurrentPrice.Currency);

            // Publish integration events
            await _messageBroker.PublishAsync("subscription.created", new
            {
                SubscriptionId = subscription.Id,
                UserId = subscription.UserId,
                PlanId = subscription.PlanId,
                PlanName = plan.Name,
                Status = subscription.Status.ToString(),
                StartDate = subscription.StartDate,
                NextBillingDate = subscription.NextBillingDate,
                IsInTrial = subscription.IsInTrial(),
                Amount = subscription.CurrentPrice.Amount,
                Currency = subscription.CurrentPrice.Currency,
                CreatedAt = DateTime.UtcNow
            });

            _logger.LogInformation("Successfully created subscription {SubscriptionId} for user {UserId}",
                subscription.Id, request.UserId);

            return subscription.Id;
        }
    }
}
