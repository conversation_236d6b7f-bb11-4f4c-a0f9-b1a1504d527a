using Shared.Domain.Common;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Domain.Events;

// Escrow Account Events
public record EscrowAccountCreatedEvent(Guid EscrowAccountId, Guid OrderId, Money TotalAmount) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record EscrowAccountFundedEvent(Guid EscrowAccountId, Guid OrderId, Money TotalAmount) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record EscrowAccountReleasedEvent(Guid EscrowAccountId, Guid OrderId, Money TotalAmount) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// Escrow Transaction Events
public record EscrowTransactionCreatedEvent(Guid TransactionId, Guid EscrowAccountId, Money Amount, EscrowTransactionType Type) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// Escrow Milestone Events
public record EscrowMilestoneCompletedEvent(Guid MilestoneId, Guid EscrowAccountId, Money Amount) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}
