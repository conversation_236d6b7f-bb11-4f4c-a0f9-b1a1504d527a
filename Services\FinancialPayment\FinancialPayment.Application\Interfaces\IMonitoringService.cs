using FinancialPayment.Domain.Entities;

namespace FinancialPayment.Application.Interfaces;

public interface IMonitoringService
{
    Task RecordMetricAsync(string metricName, decimal value, string unit, string source, Dictionary<string, string>? tags = null);
    Task RecordPerformanceAsync(string operationName, string serviceName, TimeSpan duration, bool isSuccess, string? errorMessage = null);
    Task<List<SystemMetric>> GetMetricsAsync(string? metricName = null, DateTime? fromDate = null, DateTime? toDate = null);
    Task<List<PerformanceMetric>> GetPerformanceMetricsAsync(string? operationName = null, DateTime? fromDate = null, DateTime? toDate = null);
    Task<SystemHealthSummary> GetSystemHealthAsync();
    Task<List<Alert>> GetActiveAlertsAsync();
    Task<Alert> CreateAlertAsync(string alertName, AlertSeverity severity, string message, string source, Dictionary<string, object>? alertData = null);
    Task<bool> AcknowledgeAlertAsync(Guid alertId, Guid acknowledgedBy, string? notes = null);
    Task<bool> ResolveAlertAsync(Guid alertId, string? resolutionNotes = null);
    Task<MonitoringDashboard> CreateDashboardAsync(CreateMonitoringDashboardRequest request);
    Task<List<MonitoringDashboard>> GetDashboardsAsync(Guid? userId = null);
    Task<MonitoringDashboard?> GetDashboardAsync(Guid dashboardId);
}

public interface IUsageAnalyticsService
{
    Task RecordUsageAsync(string resourceType, string resourceName, string action, bool isSuccess, Guid? userId = null, TimeSpan? duration = null, Dictionary<string, object>? properties = null);
    Task<UsageAnalyticsSummary> GetUsageAnalyticsAsync(DateTime? fromDate = null, DateTime? toDate = null, string? resourceType = null);
    Task<List<UsageAnalytics>> GetUsageHistoryAsync(string? resourceName = null, Guid? userId = null, DateTime? fromDate = null, DateTime? toDate = null);
    Task<List<TopUsedResource>> GetTopUsedResourcesAsync(string resourceType, DateTime? fromDate = null, DateTime? toDate = null, int limit = 10);
    Task<List<UserActivitySummary>> GetUserActivityAsync(DateTime? fromDate = null, DateTime? toDate = null, int limit = 100);
    Task<ApiUsageMetrics> GetApiUsageMetricsAsync(DateTime? fromDate = null, DateTime? toDate = null);
}

public interface IHealthCheckService
{
    Task<HealthCheck> PerformHealthCheckAsync(string serviceName, string checkName);
    Task<List<HealthCheck>> GetHealthChecksAsync(string? serviceName = null);
    Task<SystemHealthSummary> GetSystemHealthSummaryAsync();
    Task RegisterHealthCheckAsync(string serviceName, string checkName, Func<Task<HealthCheckResult>> healthCheckFunc);
    Task<bool> IsServiceHealthyAsync(string serviceName);
}

public interface IAlertingService
{
    Task<Alert> TriggerAlertAsync(string alertName, AlertSeverity severity, string message, string source, Dictionary<string, object>? alertData = null);
    Task<bool> SuppressAlertAsync(Guid alertId, TimeSpan suppressionDuration);
    Task<List<Alert>> GetAlertsAsync(AlertStatus? status = null, AlertSeverity? severity = null, DateTime? fromDate = null, DateTime? toDate = null);
    Task<AlertStatistics> GetAlertStatisticsAsync(DateTime? fromDate = null, DateTime? toDate = null);
    Task ConfigureAlertRuleAsync(AlertRule rule);
    Task<List<AlertRule>> GetAlertRulesAsync();
    Task EvaluateAlertRulesAsync();
}

public interface IMetricsCollector
{
    Task CollectSystemMetricsAsync();
    Task CollectApplicationMetricsAsync();
    Task CollectBusinessMetricsAsync();
    Task<Dictionary<string, decimal>> GetCurrentMetricsAsync();
}

// Request/Response DTOs
public class CreateMonitoringDashboardRequest
{
    public string DashboardName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public Guid? CreatedBy { get; set; }
    public bool IsPublic { get; set; } = false;
    public List<CreateDashboardWidgetRequest> Widgets { get; set; } = new();
}

public class CreateDashboardWidgetRequest
{
    public string WidgetName { get; set; } = string.Empty;
    public string WidgetType { get; set; } = string.Empty;
    public Dictionary<string, object> Configuration { get; set; } = new();
    public int Order { get; set; } = 0;
}

public class SystemHealthSummary
{
    public HealthStatus OverallStatus { get; set; }
    public int HealthyServices { get; set; }
    public int DegradedServices { get; set; }
    public int UnhealthyServices { get; set; }
    public List<ServiceHealthStatus> ServiceStatuses { get; set; } = new();
    public DateTime LastUpdated { get; set; }
}

public class ServiceHealthStatus
{
    public string ServiceName { get; set; } = string.Empty;
    public HealthStatus Status { get; set; }
    public TimeSpan ResponseTime { get; set; }
    public DateTime LastChecked { get; set; }
    public string? ErrorMessage { get; set; }
}

public class UsageAnalyticsSummary
{
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
    public int TotalRequests { get; set; }
    public int SuccessfulRequests { get; set; }
    public int FailedRequests { get; set; }
    public decimal SuccessRate { get; set; }
    public TimeSpan AverageResponseTime { get; set; }
    public int UniqueUsers { get; set; }
    public Dictionary<string, int> RequestsByResourceType { get; set; } = new();
    public Dictionary<string, int> RequestsByHour { get; set; } = new();
    public List<ErrorSummary> TopErrors { get; set; } = new();
}

public class TopUsedResource
{
    public string ResourceName { get; set; } = string.Empty;
    public string ResourceType { get; set; } = string.Empty;
    public int UsageCount { get; set; }
    public int UniqueUsers { get; set; }
    public TimeSpan AverageResponseTime { get; set; }
    public decimal SuccessRate { get; set; }
}

public class UserActivitySummary
{
    public Guid UserId { get; set; }
    public int TotalRequests { get; set; }
    public int SuccessfulRequests { get; set; }
    public DateTime FirstActivity { get; set; }
    public DateTime LastActivity { get; set; }
    public List<string> ResourcesUsed { get; set; } = new();
    public TimeSpan TotalActiveTime { get; set; }
}

public class ApiUsageMetrics
{
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
    public int TotalApiCalls { get; set; }
    public Dictionary<string, int> CallsByEndpoint { get; set; } = new();
    public Dictionary<string, int> CallsByStatusCode { get; set; } = new();
    public Dictionary<string, TimeSpan> AverageResponseTimeByEndpoint { get; set; } = new();
    public Dictionary<string, int> CallsByUserAgent { get; set; } = new();
    public List<ApiRateLimitViolation> RateLimitViolations { get; set; } = new();
}

public class ApiRateLimitViolation
{
    public Guid? UserId { get; set; }
    public string IpAddress { get; set; } = string.Empty;
    public string Endpoint { get; set; } = string.Empty;
    public DateTime ViolationTime { get; set; }
    public int RequestCount { get; set; }
    public int RateLimit { get; set; }
}

public class ErrorSummary
{
    public string ErrorCode { get; set; } = string.Empty;
    public string ErrorMessage { get; set; } = string.Empty;
    public int Count { get; set; }
    public DateTime FirstOccurrence { get; set; }
    public DateTime LastOccurrence { get; set; }
    public List<string> AffectedResources { get; set; } = new();
}

public class AlertStatistics
{
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
    public int TotalAlerts { get; set; }
    public int ActiveAlerts { get; set; }
    public int ResolvedAlerts { get; set; }
    public Dictionary<AlertSeverity, int> AlertsBySeverity { get; set; } = new();
    public Dictionary<string, int> AlertsBySource { get; set; } = new();
    public TimeSpan AverageResolutionTime { get; set; }
    public List<string> TopAlertSources { get; set; } = new();
}

public class AlertRule
{
    public Guid Id { get; set; }
    public string RuleName { get; set; } = string.Empty;
    public string MetricName { get; set; } = string.Empty;
    public MetricThreshold Threshold { get; set; } = null!;
    public bool IsEnabled { get; set; }
    public List<string> NotificationChannels { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
}

public class HealthCheckResult
{
    public HealthStatus Status { get; set; }
    public string? Description { get; set; }
    public Dictionary<string, object> Data { get; set; } = new();
    public string? ErrorMessage { get; set; }
}

public class MonitoringConfiguration
{
    public bool EnableMetricsCollection { get; set; } = true;
    public bool EnableUsageAnalytics { get; set; } = true;
    public bool EnableHealthChecks { get; set; } = true;
    public bool EnableAlerting { get; set; } = true;
    public int MetricsRetentionDays { get; set; } = 90;
    public int UsageAnalyticsRetentionDays { get; set; } = 365;
    public int HealthCheckIntervalSeconds { get; set; } = 30;
    public int AlertEvaluationIntervalSeconds { get; set; } = 60;
    public List<string> NotificationChannels { get; set; } = new() { "email", "slack", "webhook" };
    public Dictionary<string, string> NotificationSettings { get; set; } = new();
}
