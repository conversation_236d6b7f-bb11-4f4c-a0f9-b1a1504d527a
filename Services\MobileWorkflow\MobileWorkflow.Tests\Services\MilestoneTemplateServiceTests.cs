using FluentAssertions;
using Microsoft.Extensions.Logging;
using MobileWorkflow.Application.Services;
using MobileWorkflow.Domain.Entities;
using MobileWorkflow.Domain.Repositories;
using Moq;
using Xunit;

namespace MobileWorkflow.Tests.Services;

public class MilestoneTemplateServiceTests
{
    private readonly Mock<IMilestoneTemplateRepository> _mockTemplateRepository;
    private readonly Mock<IMilestoneStepRepository> _mockStepRepository;
    private readonly Mock<IMilestonePayoutRuleRepository> _mockPayoutRuleRepository;
    private readonly Mock<IRoleTemplateMappingsRepository> _mockRoleMappingsRepository;
    private readonly Mock<ILogger<MilestoneTemplateService>> _mockLogger;
    private readonly MilestoneTemplateService _service;

    public MilestoneTemplateServiceTests()
    {
        _mockTemplateRepository = new Mock<IMilestoneTemplateRepository>();
        _mockStepRepository = new Mock<IMilestoneStepRepository>();
        _mockPayoutRuleRepository = new Mock<IMilestonePayoutRuleRepository>();
        _mockRoleMappingsRepository = new Mock<IRoleTemplateMappingsRepository>();
        _mockLogger = new Mock<ILogger<MilestoneTemplateService>>();
        
        _service = new MilestoneTemplateService(
            _mockTemplateRepository.Object,
            _mockStepRepository.Object,
            _mockPayoutRuleRepository.Object,
            _mockRoleMappingsRepository.Object,
            _mockLogger.Object);
    }

    [Fact]
    public async Task CloneTemplateAsync_WithValidTemplate_ShouldCreateCloneSuccessfully()
    {
        // Arrange
        var originalTemplateId = Guid.NewGuid();
        var newName = "Cloned Template";
        var clonedBy = "<EMAIL>";

        var originalTemplate = new MilestoneTemplate("Original Template", "Description", "Trip", "Logistics", "creator");
        var step1 = originalTemplate.AddStep("Step 1", "Description 1", 1);
        var step2 = originalTemplate.AddStep("Step 2", "Description 2", 2);
        step1.AddPayoutRule(60.0m);
        step2.AddPayoutRule(40.0m);

        _mockTemplateRepository.Setup(r => r.GetByIdAsync(originalTemplateId, It.IsAny<CancellationToken>()))
                              .ReturnsAsync(originalTemplate);

        _mockTemplateRepository.Setup(r => r.GetByNameAsync(newName, It.IsAny<CancellationToken>()))
                              .ReturnsAsync((MilestoneTemplate?)null);

        _mockTemplateRepository.Setup(r => r.AddAsync(It.IsAny<MilestoneTemplate>(), It.IsAny<CancellationToken>()))
                              .ReturnsAsync((MilestoneTemplate template, CancellationToken _) => template);

        // Act
        var result = await _service.CloneTemplateAsync(originalTemplateId, newName, clonedBy);

        // Assert
        result.Should().NotBeNull();
        result.Name.Should().Be(newName);
        result.Description.Should().Be(originalTemplate.Description);
        result.Type.Should().Be(originalTemplate.Type);
        result.Category.Should().Be(originalTemplate.Category);
        result.CreatedBy.Should().Be(clonedBy);
        result.Steps.Should().HaveCount(2);
        result.Steps.Should().OnlyContain(s => s.MilestoneTemplateId == result.Id);

        _mockTemplateRepository.Verify(r => r.GetByIdAsync(originalTemplateId, It.IsAny<CancellationToken>()), Times.Once);
        _mockTemplateRepository.Verify(r => r.GetByNameAsync(newName, It.IsAny<CancellationToken>()), Times.Once);
        _mockTemplateRepository.Verify(r => r.AddAsync(It.IsAny<MilestoneTemplate>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task CloneTemplateAsync_WithNonExistentTemplate_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var templateId = Guid.NewGuid();
        var newName = "Cloned Template";
        var clonedBy = "<EMAIL>";

        _mockTemplateRepository.Setup(r => r.GetByIdAsync(templateId, It.IsAny<CancellationToken>()))
                              .ReturnsAsync((MilestoneTemplate?)null);

        // Act & Assert
        var act = async () => await _service.CloneTemplateAsync(templateId, newName, clonedBy);
        await act.Should().ThrowAsync<InvalidOperationException>()
                 .WithMessage($"Template with ID {templateId} not found");

        _mockTemplateRepository.Verify(r => r.GetByIdAsync(templateId, It.IsAny<CancellationToken>()), Times.Once);
        _mockTemplateRepository.Verify(r => r.AddAsync(It.IsAny<MilestoneTemplate>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task CloneTemplateAsync_WithExistingName_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var originalTemplateId = Guid.NewGuid();
        var existingName = "Existing Template";
        var clonedBy = "<EMAIL>";

        var originalTemplate = new MilestoneTemplate("Original Template", "Description", "Trip", "Logistics", "creator");
        var existingTemplate = new MilestoneTemplate(existingName, "Description", "Trip", "Logistics", "creator");

        _mockTemplateRepository.Setup(r => r.GetByIdAsync(originalTemplateId, It.IsAny<CancellationToken>()))
                              .ReturnsAsync(originalTemplate);

        _mockTemplateRepository.Setup(r => r.GetByNameAsync(existingName, It.IsAny<CancellationToken>()))
                              .ReturnsAsync(existingTemplate);

        // Act & Assert
        var act = async () => await _service.CloneTemplateAsync(originalTemplateId, existingName, clonedBy);
        await act.Should().ThrowAsync<InvalidOperationException>()
                 .WithMessage($"Template with name '{existingName}' already exists");

        _mockTemplateRepository.Verify(r => r.GetByIdAsync(originalTemplateId, It.IsAny<CancellationToken>()), Times.Once);
        _mockTemplateRepository.Verify(r => r.GetByNameAsync(existingName, It.IsAny<CancellationToken>()), Times.Once);
        _mockTemplateRepository.Verify(r => r.AddAsync(It.IsAny<MilestoneTemplate>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task GetTemplateUsageAnalyticsAsync_WithValidTemplate_ShouldReturnAnalytics()
    {
        // Arrange
        var templateId = Guid.NewGuid();
        var template = new MilestoneTemplate("Test Template", "Description", "Trip", "Logistics", "creator");
        template.IncrementUsage();
        template.IncrementUsage();

        var roleMappings = new List<RoleTemplateMappings>
        {
            new RoleTemplateMappings("Driver", templateId, "admin", true, 100),
            new RoleTemplateMappings("Manager", templateId, "admin", false, 50)
        };

        _mockTemplateRepository.Setup(r => r.GetByIdAsync(templateId, It.IsAny<CancellationToken>()))
                              .ReturnsAsync(template);

        _mockRoleMappingsRepository.Setup(r => r.GetByTemplateIdAsync(templateId, It.IsAny<CancellationToken>()))
                                  .ReturnsAsync(roleMappings);

        // Act
        var result = await _service.GetTemplateUsageAnalyticsAsync(templateId);

        // Assert
        result.Should().NotBeNull();
        result.Should().ContainKey("TemplateId");
        result.Should().ContainKey("UsageCount");
        result.Should().ContainKey("RoleMappingCount");
        result.Should().ContainKey("IsActive");
        result.Should().ContainKey("IsDefault");
        result.Should().ContainKey("StepCount");

        result["TemplateId"].Should().Be(templateId);
        result["UsageCount"].Should().Be(2);
        result["RoleMappingCount"].Should().Be(2);

        _mockTemplateRepository.Verify(r => r.GetByIdAsync(templateId, It.IsAny<CancellationToken>()), Times.Once);
        _mockRoleMappingsRepository.Verify(r => r.GetByTemplateIdAsync(templateId, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetTemplateUsageAnalyticsAsync_WithNonExistentTemplate_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var templateId = Guid.NewGuid();

        _mockTemplateRepository.Setup(r => r.GetByIdAsync(templateId, It.IsAny<CancellationToken>()))
                              .ReturnsAsync((MilestoneTemplate?)null);

        // Act & Assert
        var act = async () => await _service.GetTemplateUsageAnalyticsAsync(templateId);
        await act.Should().ThrowAsync<InvalidOperationException>()
                 .WithMessage($"Template with ID {templateId} not found");

        _mockTemplateRepository.Verify(r => r.GetByIdAsync(templateId, It.IsAny<CancellationToken>()), Times.Once);
        _mockRoleMappingsRepository.Verify(r => r.GetByTemplateIdAsync(It.IsAny<Guid>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task GetTemplateComparisonAsync_WithValidTemplates_ShouldReturnComparison()
    {
        // Arrange
        var template1Id = Guid.NewGuid();
        var template2Id = Guid.NewGuid();

        var template1 = new MilestoneTemplate("Template 1", "Description 1", "Trip", "Logistics", "creator");
        var step1 = template1.AddStep("Step 1", "Description 1", 1);
        step1.AddPayoutRule(100.0m);

        var template2 = new MilestoneTemplate("Template 2", "Description 2", "Order", "Delivery", "creator");
        var step2a = template2.AddStep("Step 1", "Description 1", 1);
        var step2b = template2.AddStep("Step 2", "Description 2", 2);
        step2a.AddPayoutRule(60.0m);
        step2b.AddPayoutRule(40.0m);

        _mockTemplateRepository.Setup(r => r.GetByIdAsync(template1Id, It.IsAny<CancellationToken>()))
                              .ReturnsAsync(template1);

        _mockTemplateRepository.Setup(r => r.GetByIdAsync(template2Id, It.IsAny<CancellationToken>()))
                              .ReturnsAsync(template2);

        // Act
        var result = await _service.GetTemplateComparisonAsync(template1Id, template2Id);

        // Assert
        result.Should().NotBeNull();
        result.Should().ContainKey("Template1");
        result.Should().ContainKey("Template2");
        result.Should().ContainKey("Differences");

        var differences = result["Differences"] as Dictionary<string, object>;
        differences.Should().NotBeNull();
        differences.Should().ContainKey("Type");
        differences.Should().ContainKey("Category");
        differences.Should().ContainKey("StepCount");

        _mockTemplateRepository.Verify(r => r.GetByIdAsync(template1Id, It.IsAny<CancellationToken>()), Times.Once);
        _mockTemplateRepository.Verify(r => r.GetByIdAsync(template2Id, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task OptimizeTemplateAsync_WithValidTemplate_ShouldReturnOptimizations()
    {
        // Arrange
        var templateId = Guid.NewGuid();
        var template = new MilestoneTemplate("Test Template", "Description", "Trip", "Logistics", "creator");
        
        // Create a template with optimization opportunities
        var step1 = template.AddStep("Step 1", "Description 1", 1);
        var step2 = template.AddStep("Step 2", "Description 2", 2);
        var step3 = template.AddStep("Step 3", "Description 3", 3);
        
        step1.AddPayoutRule(30.0m);
        step2.AddPayoutRule(30.0m);
        step3.AddPayoutRule(30.0m); // Total = 90%, optimization opportunity

        _mockTemplateRepository.Setup(r => r.GetByIdAsync(templateId, It.IsAny<CancellationToken>()))
                              .ReturnsAsync(template);

        // Act
        var result = await _service.OptimizeTemplateAsync(templateId);

        // Assert
        result.Should().NotBeNull();
        result.Should().ContainKey("TemplateId");
        result.Should().ContainKey("OptimizationSuggestions");
        result.Should().ContainKey("CurrentIssues");
        result.Should().ContainKey("RecommendedChanges");

        var suggestions = result["OptimizationSuggestions"] as List<string>;
        suggestions.Should().NotBeNull();
        suggestions.Should().NotBeEmpty();

        _mockTemplateRepository.Verify(r => r.GetByIdAsync(templateId, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetTemplatePerformanceMetricsAsync_WithValidTemplate_ShouldReturnMetrics()
    {
        // Arrange
        var templateId = Guid.NewGuid();
        var template = new MilestoneTemplate("Test Template", "Description", "Trip", "Logistics", "creator");
        template.IncrementUsage();
        template.IncrementUsage();
        template.IncrementUsage();

        _mockTemplateRepository.Setup(r => r.GetByIdAsync(templateId, It.IsAny<CancellationToken>()))
                              .ReturnsAsync(template);

        // Act
        var result = await _service.GetTemplatePerformanceMetricsAsync(templateId);

        // Assert
        result.Should().NotBeNull();
        result.Should().ContainKey("TemplateId");
        result.Should().ContainKey("UsageCount");
        result.Should().ContainKey("PerformanceScore");
        result.Should().ContainKey("EfficiencyRating");
        result.Should().ContainKey("RecommendationLevel");

        result["TemplateId"].Should().Be(templateId);
        result["UsageCount"].Should().Be(3);

        _mockTemplateRepository.Verify(r => r.GetByIdAsync(templateId, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task ExportTemplateAsync_WithValidTemplate_ShouldReturnExportData()
    {
        // Arrange
        var templateId = Guid.NewGuid();
        var template = new MilestoneTemplate("Test Template", "Description", "Trip", "Logistics", "creator");
        var step = template.AddStep("Step 1", "Description 1", 1);
        step.AddPayoutRule(100.0m);

        var roleMappings = new List<RoleTemplateMappings>
        {
            new RoleTemplateMappings("Driver", templateId, "admin", true, 100)
        };

        _mockTemplateRepository.Setup(r => r.GetByIdAsync(templateId, It.IsAny<CancellationToken>()))
                              .ReturnsAsync(template);

        _mockRoleMappingsRepository.Setup(r => r.GetByTemplateIdAsync(templateId, It.IsAny<CancellationToken>()))
                                  .ReturnsAsync(roleMappings);

        // Act
        var result = await _service.ExportTemplateAsync(templateId);

        // Assert
        result.Should().NotBeNull();
        result.Should().ContainKey("Template");
        result.Should().ContainKey("Steps");
        result.Should().ContainKey("RoleMappings");
        result.Should().ContainKey("ExportMetadata");

        var exportMetadata = result["ExportMetadata"] as Dictionary<string, object>;
        exportMetadata.Should().NotBeNull();
        exportMetadata.Should().ContainKey("ExportedAt");
        exportMetadata.Should().ContainKey("Version");

        _mockTemplateRepository.Verify(r => r.GetByIdAsync(templateId, It.IsAny<CancellationToken>()), Times.Once);
        _mockRoleMappingsRepository.Verify(r => r.GetByTemplateIdAsync(templateId, It.IsAny<CancellationToken>()), Times.Once);
    }
}
