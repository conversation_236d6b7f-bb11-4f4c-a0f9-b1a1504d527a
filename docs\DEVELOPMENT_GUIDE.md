# TLI Logistics Microservices Development Guide

## Table of Contents

- [Getting Started](#getting-started)
- [Architecture Overview](#architecture-overview)
- [Development Workflow](#development-workflow)
- [Adding New Microservices](#adding-new-microservices)
- [Testing Strategy](#testing-strategy)
- [Deployment](#deployment)
- [Troubleshooting](#troubleshooting)

## Getting Started

### Prerequisites

- .NET 8 SDK
- Docker Desktop
- Visual Studio 2022 or VS Code
- PostgreSQL (for local development without Docker)
- RabbitMQ (for local development without Docker)

### Quick Setup

1. Clone the repository
2. Run the setup script:
   ```powershell
   .\scripts\setup-dev-environment.ps1
   ```
3. Start the services:
   ```powershell
   .\scripts\start-services.ps1
   ```

### Manual Setup

1. **Start Infrastructure Services**

   ```powershell
   .\scripts\start-infrastructure.ps1
   ```

2. **Build the Solution**

   ```bash
   dotnet build TLIMicroservices.sln
   ```

3. **Run Tests**

   ```bash
   dotnet test TLIMicroservices.sln
   ```

4. **Start Services**
   - Identity Service: `cd Identity/Identity.API && dotnet run`
   - API Gateway: `cd ApiGateway && dotnet run`

## Architecture Overview

### Clean Architecture

Each microservice follows Clean Architecture principles:

- **Domain**: Business entities and rules
- **Application**: Use cases and business logic
- **Infrastructure**: Data access and external services
- **API**: Controllers and presentation layer

### Communication Patterns

- **Synchronous**: HTTP/REST for real-time operations
- **Asynchronous**: RabbitMQ for event-driven communication
- **API Gateway**: Single entry point using Ocelot

### Data Management

- **Database per Service**: Each service has its own PostgreSQL database
- **Event Sourcing**: For audit trails and complex business processes
- **CQRS**: Command Query Responsibility Segregation where appropriate

## Development Workflow

### 1. Feature Development

1. Create a feature branch from `main`
2. Implement the feature following TDD principles
3. Write unit and integration tests
4. Update documentation
5. Create a pull request

### 2. Code Standards

- Follow C# coding conventions
- Use meaningful names for classes and methods
- Write XML documentation for public APIs
- Implement proper error handling
- Use dependency injection

### 3. Testing Requirements

- Unit tests for business logic (minimum 80% coverage)
- Integration tests for API endpoints
- Contract tests for inter-service communication
- End-to-end tests for critical user journeys

## Adding New Microservices

### 1. Using the Template

```powershell
.\templates\create-new-service.ps1 -ServiceName "YourService"
```

### 2. Manual Creation

1. **Create Directory Structure**

   ```
   Services/YourService/
   ├── YourService.API/
   ├── YourService.Application/
   ├── YourService.Domain/
   ├── YourService.Infrastructure/
   └── YourService.Tests/
   ```

2. **Add Projects to Solution**

   ```bash
   dotnet sln add Services/YourService/YourService.Domain/YourService.Domain.csproj
   dotnet sln add Services/YourService/YourService.Application/YourService.Application.csproj
   # ... add other projects
   ```

3. **Update API Gateway**
   - Add route configuration in `ApiGateway/ocelot.json`
   - Update Docker Compose if needed

### 3. Service Template Structure

#### Domain Layer

```csharp
// Logistics Entities Examples
public class Shipment : AggregateRoot
{
    public string TrackingNumber { get; private set; }
    public Address Origin { get; private set; }
    public Address Destination { get; private set; }
    public ShipmentStatus Status { get; private set; }
    // Business logic for shipment management
}

// Value Objects
public class Address : ValueObject
{
    public string Street { get; private set; }
    public string City { get; private set; }
    public string PostalCode { get; private set; }
    // Immutable address implementation
}

// Domain Events
public class ShipmentCreatedEvent : DomainEvent
{
    public Guid ShipmentId { get; set; }
    public string TrackingNumber { get; set; }
}
```

#### Application Layer

```csharp
// Commands
public class CreateShipmentCommand : IRequest<Guid>
{
    public string TrackingNumber { get; set; }
    public Address Origin { get; set; }
    public Address Destination { get; set; }
}

// Queries
public class GetShipmentQuery : IRequest<ShipmentDto>
{
    public Guid ShipmentId { get; set; }
}

// Handlers
public class CreateShipmentHandler : IRequestHandler<CreateShipmentCommand, Guid>
{
    // Handler implementation for creating shipments
}
```

## Testing Strategy

### Unit Tests

- Test business logic in isolation
- Mock external dependencies
- Use AAA pattern (Arrange, Act, Assert)

```csharp
[Test]
public void Should_CreateShipment_When_ValidDataProvided()
{
    // Arrange
    var trackingNumber = "TRK123456";
    var origin = new Address("123 Main St", "City A", "12345");
    var destination = new Address("456 Oak Ave", "City B", "67890");

    // Act
    var shipment = new Shipment(trackingNumber, origin, destination);

    // Assert
    Assert.That(shipment.TrackingNumber, Is.EqualTo(trackingNumber));
    Assert.That(shipment.Status, Is.EqualTo(ShipmentStatus.Created));
}
```

### Integration Tests

- Test API endpoints end-to-end
- Use TestContainers for database testing
- Test inter-service communication

```csharp
[Test]
public async Task Should_CreateShipment_When_PostToApi()
{
    // Arrange
    var request = new CreateShipmentRequest
    {
        TrackingNumber = "TRK123456",
        Origin = new AddressDto { Street = "123 Main St", City = "City A" },
        Destination = new AddressDto { Street = "456 Oak Ave", City = "City B" }
    };

    // Act
    var response = await _client.PostAsJsonAsync("/api/shipments", request);

    // Assert
    response.EnsureSuccessStatusCode();
}
```

### Running Tests

```powershell
# All tests
.\scripts\run-tests.ps1

# Specific project
dotnet test Services/Shipment/Shipment.Tests/

# With coverage
dotnet test --collect:"XPlat Code Coverage"
```

## Deployment

### Local Development

```powershell
# Start all services with Docker
docker-compose up --build

# Start individual services
.\scripts\start-services.ps1
```

### Production Deployment

1. **Build Docker Images**

   ```bash
   docker build -f Identity/Identity.API/Dockerfile -t tli-identity:latest .
   docker build -f ApiGateway/Dockerfile -t tli-gateway:latest .
   ```

2. **Deploy to Kubernetes**

   ```bash
   kubectl apply -f k8s/
   ```

3. **Environment Variables**
   - Database connection strings
   - RabbitMQ configuration
   - JWT secrets
   - External service URLs

## Troubleshooting

### Common Issues

#### Build Errors

- Ensure .NET 8 SDK is installed
- Check project references
- Clean and rebuild solution

#### Database Connection Issues

- Verify PostgreSQL is running
- Check connection strings
- Ensure database exists

#### RabbitMQ Connection Issues

- Verify RabbitMQ is running
- Check host configuration
- Verify credentials

#### Docker Issues

- Ensure Docker Desktop is running
- Check port conflicts
- Verify Docker Compose configuration

### Debugging Tips

1. **Enable Detailed Logging**

   ```json
   {
     "Logging": {
       "LogLevel": {
         "Default": "Debug"
       }
     }
   }
   ```

2. **Use Health Checks**

   - Check `/health` endpoints
   - Monitor service dependencies

3. **Database Debugging**

   ```bash
   # Connect to PostgreSQL
   docker exec -it tli-postgres psql -U postgres -d tli_microservices
   ```

4. **Message Queue Debugging**
   - Access RabbitMQ Management UI: http://localhost:15672
   - Monitor message queues and exchanges

### Performance Monitoring

- Use Application Insights or similar
- Monitor database query performance
- Track API response times
- Monitor memory and CPU usage

### Logging Best Practices

- Use structured logging with Serilog
- Include correlation IDs for request tracing
- Log at appropriate levels (Debug, Info, Warning, Error)
- Avoid logging sensitive information

## Additional Resources

- [Clean Architecture Guide](https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html)
- [Microservices Patterns](https://microservices.io/patterns/)
- [.NET Microservices Guide](https://docs.microsoft.com/en-us/dotnet/architecture/microservices/)
- [Docker Best Practices](https://docs.docker.com/develop/dev-best-practices/)
