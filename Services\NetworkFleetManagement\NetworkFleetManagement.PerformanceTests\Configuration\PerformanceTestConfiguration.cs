namespace NetworkFleetManagement.PerformanceTests.Configuration;

public class PerformanceTestConfiguration
{
    public string BaseUrl { get; set; } = "http://localhost:5000";
    public int MaxConcurrentUsers { get; set; } = 100;
    public TimeSpan TestDuration { get; set; } = TimeSpan.FromMinutes(5);
    public TimeSpan WarmupDuration { get; set; } = TimeSpan.FromSeconds(30);
    public PerformanceThresholds Thresholds { get; set; } = new();
    public DatabaseConfiguration Database { get; set; } = new();
    public CacheConfiguration Cache { get; set; } = new();
}

public class PerformanceThresholds
{
    public TimeSpan MaxApiResponseTime { get; set; } = TimeSpan.FromMilliseconds(500);
    public TimeSpan MaxQueryResponseTime { get; set; } = TimeSpan.FromMilliseconds(300);
    public TimeSpan MaxAutoAssignResponseTime { get; set; } = TimeSpan.FromMilliseconds(200);
    public double MaxFailureRate { get; set; } = 0.01; // 1%
    public double MinCacheHitRate { get; set; } = 0.80; // 80%
    public int MaxMemoryUsageMB { get; set; } = 512;
    public int MaxCpuUsagePercent { get; set; } = 80;
}

public class DatabaseConfiguration
{
    public string ConnectionString { get; set; } = string.Empty;
    public int MaxConnections { get; set; } = 100;
    public TimeSpan CommandTimeout { get; set; } = TimeSpan.FromSeconds(30);
    public bool EnableQueryLogging { get; set; } = false;
}

public class CacheConfiguration
{
    public string RedisConnectionString { get; set; } = string.Empty;
    public TimeSpan DefaultExpiration { get; set; } = TimeSpan.FromMinutes(15);
    public bool EnableDistributedCache { get; set; } = true;
}

public static class PerformanceTestScenarios
{
    public static readonly Dictionary<string, ScenarioConfiguration> Scenarios = new()
    {
        ["ReadHeavy"] = new ScenarioConfiguration
        {
            Name = "Read Heavy Workload",
            ReadPercentage = 90,
            WritePercentage = 10,
            ConcurrentUsers = 80,
            Duration = TimeSpan.FromMinutes(10)
        },
        ["WriteHeavy"] = new ScenarioConfiguration
        {
            Name = "Write Heavy Workload", 
            ReadPercentage = 30,
            WritePercentage = 70,
            ConcurrentUsers = 40,
            Duration = TimeSpan.FromMinutes(5)
        },
        ["Balanced"] = new ScenarioConfiguration
        {
            Name = "Balanced Workload",
            ReadPercentage = 70,
            WritePercentage = 30,
            ConcurrentUsers = 60,
            Duration = TimeSpan.FromMinutes(15)
        },
        ["AutoAssignStress"] = new ScenarioConfiguration
        {
            Name = "Auto Assignment Stress Test",
            ReadPercentage = 100,
            WritePercentage = 0,
            ConcurrentUsers = 200,
            Duration = TimeSpan.FromMinutes(3),
            FocusEndpoint = "/api/v1/preferredpartners/test-auto-assignment"
        },
        ["DashboardLoad"] = new ScenarioConfiguration
        {
            Name = "Dashboard Load Test",
            ReadPercentage = 100,
            WritePercentage = 0,
            ConcurrentUsers = 150,
            Duration = TimeSpan.FromMinutes(5),
            FocusEndpoint = "/api/v1/preferredpartners/dashboard"
        }
    };
}

public class ScenarioConfiguration
{
    public string Name { get; set; } = string.Empty;
    public int ReadPercentage { get; set; }
    public int WritePercentage { get; set; }
    public int ConcurrentUsers { get; set; }
    public TimeSpan Duration { get; set; }
    public string? FocusEndpoint { get; set; }
}

public class PerformanceTestResult
{
    public string ScenarioName { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public TimeSpan Duration => EndTime - StartTime;
    public long TotalRequests { get; set; }
    public long SuccessfulRequests { get; set; }
    public long FailedRequests { get; set; }
    public double FailureRate => TotalRequests > 0 ? (double)FailedRequests / TotalRequests : 0;
    public TimeSpan AverageResponseTime { get; set; }
    public TimeSpan P95ResponseTime { get; set; }
    public TimeSpan P99ResponseTime { get; set; }
    public double RequestsPerSecond { get; set; }
    public double CacheHitRate { get; set; }
    public long MemoryUsageMB { get; set; }
    public double CpuUsagePercent { get; set; }
    public List<string> Errors { get; set; } = new();
    public Dictionary<string, object> AdditionalMetrics { get; set; } = new();
    public bool PassedThresholds { get; set; }
    public List<string> ThresholdViolations { get; set; } = new();
}

public static class PerformanceTestDataGenerator
{
    private static readonly Random Random = new();

    public static List<TestUser> GenerateTestUsers(int count)
    {
        return Enumerable.Range(1, count)
            .Select(i => new TestUser
            {
                Id = Guid.NewGuid(),
                Role = GetRandomRole(),
                Name = $"TestUser{i}",
                Email = $"testuser{i}@example.com"
            })
            .ToList();
    }

    public static List<TestPartner> GenerateTestPartners(int count)
    {
        return Enumerable.Range(1, count)
            .Select(i => new TestPartner
            {
                Id = Guid.NewGuid(),
                Type = GetRandomPartnerType(),
                Name = $"TestPartner{i}",
                Rating = (decimal)(Random.NextDouble() * 2 + 3), // 3.0 to 5.0
                Routes = GenerateRandomRoutes(),
                LoadTypes = GenerateRandomLoadTypes()
            })
            .ToList();
    }

    private static string GetRandomRole()
    {
        var roles = new[] { "Broker", "Transporter", "Shipper", "Carrier" };
        return roles[Random.Next(roles.Length)];
    }

    private static string GetRandomPartnerType()
    {
        var types = new[] { "Carrier", "Broker", "Transporter", "Shipper" };
        return types[Random.Next(types.Length)];
    }

    private static List<string> GenerateRandomRoutes()
    {
        var allRoutes = new[] { "Route1", "Route2", "Route3", "Route4", "Route5", "Route6", "Route7", "Route8", "Route9", "Route10" };
        var count = Random.Next(1, 4); // 1 to 3 routes
        return allRoutes.OrderBy(x => Random.Next()).Take(count).ToList();
    }

    private static List<string> GenerateRandomLoadTypes()
    {
        var allLoadTypes = new[] { "Dry Van", "Refrigerated", "Flatbed", "Tanker", "Container", "Oversized" };
        var count = Random.Next(1, 3); // 1 to 2 load types
        return allLoadTypes.OrderBy(x => Random.Next()).Take(count).ToList();
    }
}

public class TestUser
{
    public Guid Id { get; set; }
    public string Role { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
}

public class TestPartner
{
    public Guid Id { get; set; }
    public string Type { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public decimal Rating { get; set; }
    public List<string> Routes { get; set; } = new();
    public List<string> LoadTypes { get; set; } = new();
}
