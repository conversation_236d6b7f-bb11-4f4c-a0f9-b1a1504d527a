using CommunicationNotification.Domain.Entities;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;

namespace CommunicationNotification.Application.Interfaces;

/// <summary>
/// Service interface for feature flag management and evaluation
/// </summary>
public interface IFeatureFlagService
{
    /// <summary>
    /// Create a new feature flag
    /// </summary>
    Task<FeatureFlag> CreateFeatureFlagAsync(
        string key,
        string name,
        string description,
        FeatureFlagType type,
        string environment,
        Guid createdByUserId,
        DateTime? startDate = null,
        DateTime? endDate = null,
        RolloutConfiguration? rolloutConfiguration = null,
        TargetingRules? targetingRules = null,
        Dictionary<string, object>? defaultValue = null,
        Dictionary<string, object>? configuration = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get feature flag by key
    /// </summary>
    Task<FeatureFlag?> GetFeatureFlagAsync(
        string key,
        string? environment = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get feature flag by ID
    /// </summary>
    Task<FeatureFlag?> GetFeatureFlagByIdAsync(
        Guid id,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all feature flags
    /// </summary>
    Task<List<FeatureFlag>> GetAllFeatureFlagsAsync(
        string? environment = null,
        FeatureFlagStatus? status = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Update feature flag
    /// </summary>
    Task<FeatureFlag> UpdateFeatureFlagAsync(
        Guid id,
        string? name = null,
        string? description = null,
        RolloutConfiguration? rolloutConfiguration = null,
        TargetingRules? targetingRules = null,
        Dictionary<string, object>? defaultValue = null,
        Dictionary<string, object>? configuration = null,
        Guid? modifiedByUserId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete feature flag
    /// </summary>
    Task DeleteFeatureFlagAsync(
        Guid id,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Add variant to feature flag
    /// </summary>
    Task<FeatureFlag> AddVariantAsync(
        Guid featureFlagId,
        string variantName,
        string description,
        decimal trafficAllocation,
        object? value = null,
        Dictionary<string, object>? configuration = null,
        bool isControl = false,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Remove variant from feature flag
    /// </summary>
    Task<FeatureFlag> RemoveVariantAsync(
        Guid featureFlagId,
        string variantName,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Activate feature flag
    /// </summary>
    Task<FeatureFlag> ActivateFeatureFlagAsync(
        Guid id,
        Guid modifiedByUserId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Pause feature flag
    /// </summary>
    Task<FeatureFlag> PauseFeatureFlagAsync(
        Guid id,
        Guid modifiedByUserId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Complete feature flag
    /// </summary>
    Task<FeatureFlag> CompleteFeatureFlagAsync(
        Guid id,
        Guid modifiedByUserId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Archive feature flag
    /// </summary>
    Task<FeatureFlag> ArchiveFeatureFlagAsync(
        Guid id,
        Guid modifiedByUserId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if feature flag is enabled for user
    /// </summary>
    Task<bool> IsEnabledAsync(
        string key,
        Guid userId,
        Dictionary<string, object>? context = null,
        string? environment = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get feature flag value for user
    /// </summary>
    Task<T?> GetValueAsync<T>(
        string key,
        Guid userId,
        T? defaultValue = default,
        Dictionary<string, object>? context = null,
        string? environment = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get variant assignment for user in A/B test
    /// </summary>
    Task<string?> GetVariantAsync(
        string key,
        Guid userId,
        Dictionary<string, object>? context = null,
        string? environment = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get multiple feature flag values for user
    /// </summary>
    Task<Dictionary<string, object?>> GetMultipleValuesAsync(
        List<string> keys,
        Guid userId,
        Dictionary<string, object>? context = null,
        string? environment = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Record feature flag usage
    /// </summary>
    Task RecordUsageAsync(
        string key,
        Guid userId,
        string? variant = null,
        Dictionary<string, object>? context = null,
        string? environment = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get feature flag metrics
    /// </summary>
    Task<FeatureFlagMetrics> GetMetricsAsync(
        Guid featureFlagId,
        DateTime? startDate = null,
        DateTime? endDate = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get feature flag usage history
    /// </summary>
    Task<List<FeatureFlagUsage>> GetUsageHistoryAsync(
        Guid featureFlagId,
        DateTime? startDate = null,
        DateTime? endDate = null,
        int? limit = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Bulk update rollout percentage
    /// </summary>
    Task BulkUpdateRolloutAsync(
        List<Guid> featureFlagIds,
        decimal percentage,
        Guid modifiedByUserId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Clone feature flag to different environment
    /// </summary>
    Task<FeatureFlag> CloneFeatureFlagAsync(
        Guid sourceId,
        string targetEnvironment,
        string? newKey = null,
        Guid? createdByUserId = null,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for feature flags
/// </summary>
public interface IFeatureFlagRepository
{
    /// <summary>
    /// Add feature flag
    /// </summary>
    Task<FeatureFlag> AddAsync(
        FeatureFlag featureFlag,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Update feature flag
    /// </summary>
    Task<FeatureFlag> UpdateAsync(
        FeatureFlag featureFlag,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get feature flag by ID
    /// </summary>
    Task<FeatureFlag?> GetByIdAsync(
        Guid id,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get feature flag by key
    /// </summary>
    Task<FeatureFlag?> GetByKeyAsync(
        string key,
        string? environment = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all feature flags
    /// </summary>
    Task<List<FeatureFlag>> GetAllAsync(
        string? environment = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get feature flags by status
    /// </summary>
    Task<List<FeatureFlag>> GetByStatusAsync(
        FeatureFlagStatus status,
        string? environment = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get active feature flags
    /// </summary>
    Task<List<FeatureFlag>> GetActiveAsync(
        string? environment = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get feature flags by type
    /// </summary>
    Task<List<FeatureFlag>> GetByTypeAsync(
        FeatureFlagType type,
        string? environment = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if feature flag key exists
    /// </summary>
    Task<bool> ExistsAsync(
        string key,
        string? environment = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete feature flag
    /// </summary>
    Task DeleteAsync(
        Guid id,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get usage history for feature flag
    /// </summary>
    Task<List<FeatureFlagUsage>> GetUsageHistoryAsync(
        Guid featureFlagId,
        DateTime? startDate = null,
        DateTime? endDate = null,
        int? limit = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get feature flags for user based on targeting
    /// </summary>
    Task<List<FeatureFlag>> GetForUserAsync(
        Guid userId,
        Dictionary<string, object>? context = null,
        string? environment = null,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Service interface for feature flag analytics
/// </summary>
public interface IFeatureFlagAnalyticsService
{
    /// <summary>
    /// Calculate feature flag metrics
    /// </summary>
    Task<FeatureFlagMetrics> CalculateMetricsAsync(
        Guid featureFlagId,
        DateTime? startDate = null,
        DateTime? endDate = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get variant performance comparison
    /// </summary>
    Task<Dictionary<string, VariantPerformance>> GetVariantPerformanceAsync(
        Guid featureFlagId,
        DateTime? startDate = null,
        DateTime? endDate = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get feature flag adoption metrics
    /// </summary>
    Task<AdoptionMetrics> GetAdoptionMetricsAsync(
        Guid featureFlagId,
        DateTime? startDate = null,
        DateTime? endDate = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get rollout impact analysis
    /// </summary>
    Task<RolloutImpactAnalysis> GetRolloutImpactAsync(
        Guid featureFlagId,
        DateTime? startDate = null,
        DateTime? endDate = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Generate feature flag insights
    /// </summary>
    Task<List<FeatureFlagInsight>> GenerateInsightsAsync(
        Guid featureFlagId,
        CancellationToken cancellationToken = default);
}

// Supporting DTOs and classes
public class VariantPerformance
{
    public string VariantName { get; set; } = string.Empty;
    public int Users { get; set; }
    public int Conversions { get; set; }
    public decimal ConversionRate { get; set; }
    public decimal Revenue { get; set; }
    public Dictionary<string, object> Metrics { get; set; } = new();
}

public class AdoptionMetrics
{
    public int TotalUsers { get; set; }
    public int ActiveUsers { get; set; }
    public decimal AdoptionRate { get; set; }
    public Dictionary<string, int> UsersByVariant { get; set; } = new();
    public List<DailyAdoption> DailyAdoption { get; set; } = new();
}

public class DailyAdoption
{
    public DateTime Date { get; set; }
    public int NewUsers { get; set; }
    public int ActiveUsers { get; set; }
    public decimal AdoptionRate { get; set; }
}

public class RolloutImpactAnalysis
{
    public decimal PerformanceImpact { get; set; }
    public decimal ErrorRateChange { get; set; }
    public decimal UserSatisfactionChange { get; set; }
    public Dictionary<string, decimal> MetricChanges { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
}

public class FeatureFlagInsight
{
    public string Type { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal Impact { get; set; }
    public decimal Confidence { get; set; }
    public Dictionary<string, object> Data { get; set; } = new();
    public DateTime GeneratedAt { get; set; }
}
