using Microsoft.Extensions.Logging;
using AuditCompliance.Application.Interfaces;
using AuditCompliance.Application.DTOs.Reporting;
using System.Text.RegularExpressions;
using System.Text.Json;

namespace AuditCompliance.Infrastructure.Services;

/// <summary>
/// Report template engine for rendering reports
/// </summary>
public class ReportTemplateEngine : IReportTemplateEngine
{
    private readonly ILogger<ReportTemplateEngine> _logger;
    private readonly Dictionary<string, Func<object, string>> _formatters;

    public ReportTemplateEngine(ILogger<ReportTemplateEngine> logger)
    {
        _logger = logger;
        _formatters = InitializeFormatters();
    }

    public async Task<string> RenderReportAsync(
        ReportTemplateDto template,
        Dictionary<string, object> data,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Rendering report template {TemplateId}", template.Id);

            var content = template.Content;

            // Replace variables in template
            content = await ReplaceVariablesAsync(content, data, cancellationToken);

            // Process conditional blocks
            content = ProcessConditionalBlocks(content, data);

            // Process loops
            content = ProcessLoops(content, data);

            // Apply formatters
            content = ApplyFormatters(content, data);

            // Process includes (sub-templates)
            content = await ProcessIncludesAsync(content, data, cancellationToken);

            _logger.LogInformation("Successfully rendered report template {TemplateId}", template.Id);
            return content;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error rendering report template {TemplateId}", template.Id);
            throw;
        }
    }

    public async Task<TemplateValidationResult> ValidateTemplateAsync(
        string templateContent,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var result = new TemplateValidationResult { IsValid = true };

            // Validate variable syntax
            var variableMatches = Regex.Matches(templateContent, @"\{\{([^}]+)\}\}");
            foreach (Match match in variableMatches)
            {
                var variable = match.Groups[1].Value.Trim();
                if (string.IsNullOrEmpty(variable))
                {
                    result.Errors.Add($"Empty variable at position {match.Index}");
                    result.IsValid = false;
                }
                else
                {
                    result.RequiredVariables.Add(variable);
                }
            }

            // Validate conditional blocks
            var conditionalMatches = Regex.Matches(templateContent, @"\{\%\s*if\s+([^%]+)\s*\%\}");
            var endIfMatches = Regex.Matches(templateContent, @"\{\%\s*endif\s*\%\}");
            
            if (conditionalMatches.Count != endIfMatches.Count)
            {
                result.Errors.Add("Mismatched if/endif blocks");
                result.IsValid = false;
            }

            // Validate loop blocks
            var loopMatches = Regex.Matches(templateContent, @"\{\%\s*for\s+([^%]+)\s*\%\}");
            var endForMatches = Regex.Matches(templateContent, @"\{\%\s*endfor\s*\%\}");
            
            if (loopMatches.Count != endForMatches.Count)
            {
                result.Errors.Add("Mismatched for/endfor blocks");
                result.IsValid = false;
            }

            // Validate HTML structure (if HTML template)
            if (templateContent.Contains("<html") || templateContent.Contains("<!DOCTYPE"))
            {
                await ValidateHtmlStructureAsync(templateContent, result, cancellationToken);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating template");
            return new TemplateValidationResult
            {
                IsValid = false,
                Errors = new List<string> { $"Validation error: {ex.Message}" }
            };
        }
    }

    public async Task<List<TemplateVariableDto>> GetTemplateVariablesAsync(
        string templateType,
        CancellationToken cancellationToken = default)
    {
        try
        {
            return templateType.ToLower() switch
            {
                "compliance" => GetComplianceTemplateVariables(),
                "audit" => GetAuditTemplateVariables(),
                "rating" => GetRatingTemplateVariables(),
                "regulatory" => GetRegulatoryTemplateVariables(),
                _ => GetCommonTemplateVariables()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting template variables for type {TemplateType}", templateType);
            throw;
        }
    }

    // Private helper methods
    private async Task<string> ReplaceVariablesAsync(
        string content,
        Dictionary<string, object> data,
        CancellationToken cancellationToken)
    {
        var variablePattern = @"\{\{([^}]+)\}\}";
        
        return await Task.Run(() =>
        {
            return Regex.Replace(content, variablePattern, match =>
            {
                var variable = match.Groups[1].Value.Trim();
                return GetVariableValue(variable, data);
            });
        }, cancellationToken);
    }

    private string ProcessConditionalBlocks(string content, Dictionary<string, object> data)
    {
        var conditionalPattern = @"\{\%\s*if\s+([^%]+)\s*\%\}(.*?)\{\%\s*endif\s*\%\}";
        
        return Regex.Replace(content, conditionalPattern, match =>
        {
            var condition = match.Groups[1].Value.Trim();
            var blockContent = match.Groups[2].Value;
            
            if (EvaluateCondition(condition, data))
            {
                return blockContent;
            }
            
            return string.Empty;
        }, RegexOptions.Singleline);
    }

    private string ProcessLoops(string content, Dictionary<string, object> data)
    {
        var loopPattern = @"\{\%\s*for\s+(\w+)\s+in\s+([^%]+)\s*\%\}(.*?)\{\%\s*endfor\s*\%\}";
        
        return Regex.Replace(content, loopPattern, match =>
        {
            var itemVariable = match.Groups[1].Value.Trim();
            var collectionVariable = match.Groups[2].Value.Trim();
            var loopContent = match.Groups[3].Value;
            
            return ProcessLoop(itemVariable, collectionVariable, loopContent, data);
        }, RegexOptions.Singleline);
    }

    private string ApplyFormatters(string content, Dictionary<string, object> data)
    {
        var formatterPattern = @"\{\{([^}]+)\|([^}]+)\}\}";
        
        return Regex.Replace(content, formatterPattern, match =>
        {
            var variable = match.Groups[1].Value.Trim();
            var formatter = match.Groups[2].Value.Trim();
            
            var value = GetVariableValue(variable, data);
            return ApplyFormatter(value, formatter);
        });
    }

    private async Task<string> ProcessIncludesAsync(
        string content,
        Dictionary<string, object> data,
        CancellationToken cancellationToken)
    {
        var includePattern = @"\{\%\s*include\s+['""]([^'""]+)['""]\s*\%\}";
        
        var matches = Regex.Matches(content, includePattern);
        foreach (Match match in matches)
        {
            var templateName = match.Groups[1].Value;
            var includeContent = await LoadIncludeTemplateAsync(templateName, cancellationToken);
            content = content.Replace(match.Value, includeContent);
        }
        
        return content;
    }

    private string GetVariableValue(string variable, Dictionary<string, object> data)
    {
        try
        {
            // Support nested properties (e.g., "user.name")
            var parts = variable.Split('.');
            object current = data;
            
            foreach (var part in parts)
            {
                if (current is Dictionary<string, object> dict && dict.ContainsKey(part))
                {
                    current = dict[part];
                }
                else if (current != null)
                {
                    var property = current.GetType().GetProperty(part);
                    if (property != null)
                    {
                        current = property.GetValue(current);
                    }
                    else
                    {
                        return $"{{{{ {variable} }}}}"; // Return original if not found
                    }
                }
                else
                {
                    return $"{{{{ {variable} }}}}"; // Return original if not found
                }
            }
            
            return current?.ToString() ?? string.Empty;
        }
        catch
        {
            return $"{{{{ {variable} }}}}"; // Return original if error
        }
    }

    private bool EvaluateCondition(string condition, Dictionary<string, object> data)
    {
        try
        {
            // Simple condition evaluation (can be enhanced)
            if (condition.Contains("=="))
            {
                var parts = condition.Split("==", 2);
                var left = GetVariableValue(parts[0].Trim(), data);
                var right = parts[1].Trim().Trim('"', '\'');
                return left == right;
            }
            
            if (condition.Contains("!="))
            {
                var parts = condition.Split("!=", 2);
                var left = GetVariableValue(parts[0].Trim(), data);
                var right = parts[1].Trim().Trim('"', '\'');
                return left != right;
            }
            
            // Check if variable exists and is truthy
            var value = GetVariableValue(condition, data);
            return !string.IsNullOrEmpty(value) && value != "False" && value != "0";
        }
        catch
        {
            return false;
        }
    }

    private string ProcessLoop(string itemVariable, string collectionVariable, string loopContent, Dictionary<string, object> data)
    {
        try
        {
            var collection = GetVariableValue(collectionVariable, data);
            if (collection is IEnumerable<object> enumerable)
            {
                var result = new List<string>();
                var index = 0;
                
                foreach (var item in enumerable)
                {
                    var loopData = new Dictionary<string, object>(data)
                    {
                        [itemVariable] = item,
                        ["loop"] = new Dictionary<string, object>
                        {
                            ["index"] = index,
                            ["index0"] = index,
                            ["index1"] = index + 1,
                            ["first"] = index == 0,
                            ["last"] = index == enumerable.Count() - 1
                        }
                    };
                    
                    var processedContent = ReplaceVariablesAsync(loopContent, loopData, CancellationToken.None).Result;
                    result.Add(processedContent);
                    index++;
                }
                
                return string.Join("", result);
            }
            
            return string.Empty;
        }
        catch
        {
            return string.Empty;
        }
    }

    private string ApplyFormatter(string value, string formatter)
    {
        try
        {
            if (_formatters.ContainsKey(formatter))
            {
                return _formatters[formatter](value);
            }
            
            return value;
        }
        catch
        {
            return value;
        }
    }

    private async Task<string> LoadIncludeTemplateAsync(string templateName, CancellationToken cancellationToken)
    {
        try
        {
            // This would load from database or file system
            // For now, return empty string
            return string.Empty;
        }
        catch
        {
            return string.Empty;
        }
    }

    private async Task ValidateHtmlStructureAsync(
        string templateContent,
        TemplateValidationResult result,
        CancellationToken cancellationToken)
    {
        try
        {
            // Basic HTML validation
            var openTags = Regex.Matches(templateContent, @"<(\w+)[^>]*>");
            var closeTags = Regex.Matches(templateContent, @"</(\w+)>");
            
            var openTagNames = openTags.Cast<Match>().Select(m => m.Groups[1].Value.ToLower()).ToList();
            var closeTagNames = closeTags.Cast<Match>().Select(m => m.Groups[1].Value.ToLower()).ToList();
            
            // Check for self-closing tags
            var selfClosingTags = new[] { "br", "hr", "img", "input", "meta", "link" };
            openTagNames = openTagNames.Where(tag => !selfClosingTags.Contains(tag)).ToList();
            
            foreach (var tag in openTagNames)
            {
                if (!closeTagNames.Contains(tag))
                {
                    result.Warnings.Add($"Unclosed HTML tag: {tag}");
                }
            }
        }
        catch (Exception ex)
        {
            result.Warnings.Add($"HTML validation error: {ex.Message}");
        }
    }

    private Dictionary<string, Func<object, string>> InitializeFormatters()
    {
        return new Dictionary<string, Func<object, string>>
        {
            ["upper"] = value => value?.ToString()?.ToUpper() ?? string.Empty,
            ["lower"] = value => value?.ToString()?.ToLower() ?? string.Empty,
            ["title"] = value => System.Globalization.CultureInfo.CurrentCulture.TextInfo.ToTitleCase(value?.ToString()?.ToLower() ?? string.Empty),
            ["date"] = value => DateTime.TryParse(value?.ToString(), out var date) ? date.ToString("yyyy-MM-dd") : value?.ToString() ?? string.Empty,
            ["datetime"] = value => DateTime.TryParse(value?.ToString(), out var date) ? date.ToString("yyyy-MM-dd HH:mm:ss") : value?.ToString() ?? string.Empty,
            ["currency"] = value => decimal.TryParse(value?.ToString(), out var amount) ? amount.ToString("C") : value?.ToString() ?? string.Empty,
            ["percent"] = value => float.TryParse(value?.ToString(), out var percent) ? (percent * 100).ToString("F1") + "%" : value?.ToString() ?? string.Empty,
            ["json"] = value => JsonSerializer.Serialize(value),
            ["escape"] = value => System.Web.HttpUtility.HtmlEncode(value?.ToString() ?? string.Empty)
        };
    }

    private List<TemplateVariableDto> GetComplianceTemplateVariables()
    {
        return new List<TemplateVariableDto>
        {
            new() { Name = "organization.name", Type = "string", Description = "Organization name", IsRequired = true },
            new() { Name = "report.title", Type = "string", Description = "Report title", IsRequired = true },
            new() { Name = "report.period.start", Type = "date", Description = "Report period start date", IsRequired = true },
            new() { Name = "report.period.end", Type = "date", Description = "Report period end date", IsRequired = true },
            new() { Name = "compliance.score", Type = "number", Description = "Overall compliance score", IsRequired = false },
            new() { Name = "compliance.standards", Type = "array", Description = "List of compliance standards", IsRequired = false },
            new() { Name = "violations.total", Type = "number", Description = "Total number of violations", IsRequired = false },
            new() { Name = "violations.critical", Type = "number", Description = "Number of critical violations", IsRequired = false }
        };
    }

    private List<TemplateVariableDto> GetAuditTemplateVariables()
    {
        return new List<TemplateVariableDto>
        {
            new() { Name = "audit.id", Type = "string", Description = "Audit ID", IsRequired = true },
            new() { Name = "audit.date", Type = "date", Description = "Audit date", IsRequired = true },
            new() { Name = "auditor.name", Type = "string", Description = "Auditor name", IsRequired = true },
            new() { Name = "audit.findings", Type = "array", Description = "List of audit findings", IsRequired = false },
            new() { Name = "audit.recommendations", Type = "array", Description = "List of recommendations", IsRequired = false }
        };
    }

    private List<TemplateVariableDto> GetRatingTemplateVariables()
    {
        return new List<TemplateVariableDto>
        {
            new() { Name = "provider.name", Type = "string", Description = "Service provider name", IsRequired = true },
            new() { Name = "rating.overall", Type = "number", Description = "Overall rating", IsRequired = true },
            new() { Name = "rating.breakdown", Type = "object", Description = "Rating breakdown by category", IsRequired = false },
            new() { Name = "reviews.count", Type = "number", Description = "Number of reviews", IsRequired = false }
        };
    }

    private List<TemplateVariableDto> GetRegulatoryTemplateVariables()
    {
        return new List<TemplateVariableDto>
        {
            new() { Name = "regulation.name", Type = "string", Description = "Regulation name", IsRequired = true },
            new() { Name = "regulation.version", Type = "string", Description = "Regulation version", IsRequired = false },
            new() { Name = "compliance.status", Type = "string", Description = "Compliance status", IsRequired = true },
            new() { Name = "evidence.documents", Type = "array", Description = "List of evidence documents", IsRequired = false }
        };
    }

    private List<TemplateVariableDto> GetCommonTemplateVariables()
    {
        return new List<TemplateVariableDto>
        {
            new() { Name = "current.date", Type = "date", Description = "Current date", IsRequired = false, DefaultValue = DateTime.UtcNow },
            new() { Name = "current.time", Type = "datetime", Description = "Current date and time", IsRequired = false, DefaultValue = DateTime.UtcNow },
            new() { Name = "user.name", Type = "string", Description = "Current user name", IsRequired = false },
            new() { Name = "tenant.name", Type = "string", Description = "Tenant name", IsRequired = false }
        };
    }
}
