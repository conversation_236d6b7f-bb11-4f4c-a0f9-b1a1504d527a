using Microsoft.EntityFrameworkCore;
using MonitoringObservability.Domain.Entities;
using MonitoringObservability.Domain.Enums;
using MonitoringObservability.Domain.Interfaces;
using MonitoringObservability.Infrastructure.Data;

namespace MonitoringObservability.Infrastructure.Repositories;

/// <summary>
/// Repository implementation for distributed traces
/// </summary>
public class TraceRepository : ITraceRepository
{
    private readonly MonitoringDbContext _context;

    public TraceRepository(MonitoringDbContext context)
    {
        _context = context ?? throw new ArgumentNullException(nameof(context));
    }

    public async Task<Trace?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.Traces
            .Include(t => t.Spans)
                .ThenInclude(s => s.Events)
            .Include(t => t.Spans)
                .ThenInclude(s => s.Logs)
            .Include(t => t.Events)
            .FirstOrDefaultAsync(t => t.Id == id, cancellationToken);
    }

    public async Task<Trace?> GetByTraceIdAsync(string traceId, CancellationToken cancellationToken = default)
    {
        return await _context.Traces
            .Include(t => t.Spans)
                .ThenInclude(s => s.Events)
            .Include(t => t.Spans)
                .ThenInclude(s => s.Logs)
            .Include(t => t.Events)
            .FirstOrDefaultAsync(t => t.TraceId == traceId, cancellationToken);
    }

    public async Task<IEnumerable<Trace>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        return await _context.Traces
            .Include(t => t.Spans)
            .Include(t => t.Events)
            .ToListAsync(cancellationToken);
    }

    public async Task AddAsync(Trace trace, CancellationToken cancellationToken = default)
    {
        await _context.Traces.AddAsync(trace, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task UpdateAsync(Trace trace, CancellationToken cancellationToken = default)
    {
        _context.Traces.Update(trace);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var trace = await _context.Traces.FindAsync(new object[] { id }, cancellationToken);
        if (trace != null)
        {
            _context.Traces.Remove(trace);
            await _context.SaveChangesAsync(cancellationToken);
        }
    }

    public async Task<IEnumerable<Trace>> SearchTracesAsync(
        string? serviceName = null,
        string? operationName = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        TimeSpan? minDuration = null,
        TimeSpan? maxDuration = null,
        TraceStatus? status = null,
        bool? hasErrors = null,
        Dictionary<string, string>? tags = null,
        int skip = 0,
        int take = 100,
        CancellationToken cancellationToken = default)
    {
        var query = _context.Traces
            .Include(t => t.Spans)
            .AsQueryable();

        if (!string.IsNullOrEmpty(serviceName))
        {
            query = query.Where(t => t.RootServiceName == serviceName || 
                                   t.Spans.Any(s => s.ServiceName == serviceName));
        }

        if (!string.IsNullOrEmpty(operationName))
        {
            query = query.Where(t => t.RootOperationName == operationName ||
                                   t.Spans.Any(s => s.OperationName == operationName));
        }

        if (fromDate.HasValue)
        {
            query = query.Where(t => t.StartTime >= fromDate.Value);
        }

        if (toDate.HasValue)
        {
            query = query.Where(t => t.StartTime <= toDate.Value);
        }

        if (minDuration.HasValue)
        {
            query = query.Where(t => t.Duration >= minDuration.Value);
        }

        if (maxDuration.HasValue)
        {
            query = query.Where(t => t.Duration <= maxDuration.Value);
        }

        if (status.HasValue)
        {
            query = query.Where(t => t.Status == status.Value);
        }

        if (hasErrors.HasValue)
        {
            if (hasErrors.Value)
            {
                query = query.Where(t => t.Status == TraceStatus.Error || 
                                       t.Spans.Any(s => s.HasError));
            }
            else
            {
                query = query.Where(t => t.Status != TraceStatus.Error && 
                                       !t.Spans.Any(s => s.HasError));
            }
        }

        // Apply tags filter if provided
        if (tags != null && tags.Any())
        {
            foreach (var tag in tags)
            {
                var key = tag.Key;
                var value = tag.Value;
                query = query.Where(t => t.Tags.Any(kvp => kvp.Key == key && kvp.Value == value));
            }
        }

        return await query
            .OrderByDescending(t => t.StartTime)
            .Skip(skip)
            .Take(take)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Trace>> GetSlowTracesAsync(
        string? serviceName,
        TimeSpan threshold,
        TimeSpan period,
        int limit = 100,
        CancellationToken cancellationToken = default)
    {
        var fromDate = DateTime.UtcNow.Subtract(period);
        
        var query = _context.Traces
            .Include(t => t.Spans)
            .Where(t => t.StartTime >= fromDate && 
                       t.Duration >= threshold);

        if (!string.IsNullOrEmpty(serviceName))
        {
            query = query.Where(t => t.RootServiceName == serviceName ||
                                   t.Spans.Any(s => s.ServiceName == serviceName));
        }

        return await query
            .OrderByDescending(t => t.Duration)
            .Take(limit)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Trace>> GetErrorTracesAsync(
        string? serviceName,
        TimeSpan period,
        int limit = 100,
        CancellationToken cancellationToken = default)
    {
        var fromDate = DateTime.UtcNow.Subtract(period);
        
        var query = _context.Traces
            .Include(t => t.Spans)
            .Where(t => t.StartTime >= fromDate && 
                       (t.Status == TraceStatus.Error || t.Spans.Any(s => s.HasError)));

        if (!string.IsNullOrEmpty(serviceName))
        {
            query = query.Where(t => t.RootServiceName == serviceName ||
                                   t.Spans.Any(s => s.ServiceName == serviceName));
        }

        return await query
            .OrderByDescending(t => t.StartTime)
            .Take(limit)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Trace>> GetActiveTracesAsync(CancellationToken cancellationToken = default)
    {
        return await _context.Traces
            .Include(t => t.Spans)
            .Where(t => t.IsActive)
            .OrderByDescending(t => t.StartTime)
            .ToListAsync(cancellationToken);
    }

    public async Task<double> GetAverageTraceTimeAsync(
        string serviceName,
        string? operationName = null,
        TimeSpan? period = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.Traces.AsQueryable();

        if (period.HasValue)
        {
            var fromDate = DateTime.UtcNow.Subtract(period.Value);
            query = query.Where(t => t.StartTime >= fromDate);
        }

        query = query.Where(t => t.RootServiceName == serviceName && t.Duration.HasValue);

        if (!string.IsNullOrEmpty(operationName))
        {
            query = query.Where(t => t.RootOperationName == operationName);
        }

        var durations = await query
            .Select(t => t.Duration!.Value.TotalMilliseconds)
            .ToListAsync(cancellationToken);

        return durations.Any() ? durations.Average() : 0;
    }

    public async Task<IEnumerable<(string operationName, double averageTime, int count)>> GetSlowestOperationsAsync(
        string serviceName,
        TimeSpan period,
        int topCount = 10,
        CancellationToken cancellationToken = default)
    {
        var fromDate = DateTime.UtcNow.Subtract(period);
        
        var results = await _context.Traces
            .Where(t => t.RootServiceName == serviceName && 
                       t.StartTime >= fromDate && 
                       t.Duration.HasValue)
            .GroupBy(t => t.RootOperationName)
            .Select(g => new
            {
                OperationName = g.Key,
                AverageTime = g.Average(t => t.Duration!.Value.TotalMilliseconds),
                Count = g.Count()
            })
            .OrderByDescending(x => x.AverageTime)
            .Take(topCount)
            .ToListAsync(cancellationToken);

        return results.Select(r => (r.OperationName, r.AverageTime, r.Count));
    }

    public async Task<Dictionary<string, int>> GetTraceCountByServiceAsync(
        TimeSpan period,
        CancellationToken cancellationToken = default)
    {
        var fromDate = DateTime.UtcNow.Subtract(period);
        
        var results = await _context.Traces
            .Where(t => t.StartTime >= fromDate)
            .GroupBy(t => t.RootServiceName)
            .Select(g => new { ServiceName = g.Key, Count = g.Count() })
            .ToListAsync(cancellationToken);

        return results.ToDictionary(r => r.ServiceName, r => r.Count);
    }

    public async Task<Dictionary<TraceStatus, int>> GetTraceCountByStatusAsync(
        TimeSpan period,
        string? serviceName = null,
        CancellationToken cancellationToken = default)
    {
        var fromDate = DateTime.UtcNow.Subtract(period);
        
        var query = _context.Traces
            .Where(t => t.StartTime >= fromDate);

        if (!string.IsNullOrEmpty(serviceName))
        {
            query = query.Where(t => t.RootServiceName == serviceName);
        }

        var results = await query
            .GroupBy(t => t.Status)
            .Select(g => new { Status = g.Key, Count = g.Count() })
            .ToListAsync(cancellationToken);

        return results.ToDictionary(r => r.Status, r => r.Count);
    }

    public async Task<int> DeleteOldTracesAsync(TimeSpan retentionPeriod, CancellationToken cancellationToken = default)
    {
        var cutoffDate = DateTime.UtcNow.Subtract(retentionPeriod);
        
        var oldTraces = await _context.Traces
            .Where(t => t.StartTime < cutoffDate)
            .ToListAsync(cancellationToken);

        if (oldTraces.Any())
        {
            _context.Traces.RemoveRange(oldTraces);
            await _context.SaveChangesAsync(cancellationToken);
        }

        return oldTraces.Count;
    }
}
