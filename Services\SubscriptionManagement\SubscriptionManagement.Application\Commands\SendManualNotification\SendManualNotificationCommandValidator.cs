using FluentValidation;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Application.Commands.SendManualNotification
{
    public class SendManualNotificationCommandValidator : AbstractValidator<SendManualNotificationCommand>
    {
        public SendManualNotificationCommandValidator()
        {
            RuleFor(x => x.TriggeredByUserId)
                .NotEmpty()
                .WithMessage("TriggeredByUserId is required");

            RuleFor(x => x.NotificationType)
                .IsInEnum()
                .WithMessage("Valid notification type is required");

            RuleFor(x => x.Channels)
                .NotEmpty()
                .WithMessage("At least one notification channel must be specified")
                .Must(channels => channels.All(c => IsValidChannel(c)))
                .WithMessage("All channels must be valid (Email, SMS, WhatsApp, InApp)");

            RuleFor(x => x.Language)
                .NotEmpty()
                .WithMessage("Language is required")
                .Length(2, 10)
                .WithMessage("Language must be between 2 and 10 characters");

            RuleFor(x => x.CustomMessage)
                .MaximumLength(2000)
                .WithMessage("Custom message cannot exceed 2000 characters");

            RuleFor(x => x.ScheduledAt)
                .GreaterThan(DateTime.UtcNow)
                .When(x => x.ScheduledAt.HasValue)
                .WithMessage("Scheduled time must be in the future");

            RuleFor(x => x)
                .Must(x => x.SubscriptionId.HasValue || x.UserId.HasValue)
                .WithMessage("Either SubscriptionId or UserId must be provided");

            RuleForEach(x => x.AdditionalVariables.Keys)
                .NotEmpty()
                .WithMessage("Variable names cannot be empty")
                .MaximumLength(100)
                .WithMessage("Variable names cannot exceed 100 characters");

            RuleForEach(x => x.AdditionalVariables.Values)
                .MaximumLength(1000)
                .WithMessage("Variable values cannot exceed 1000 characters");
        }

        private bool IsValidChannel(string channel)
        {
            var validChannels = new[] { "Email", "SMS", "WhatsApp", "InApp" };
            return validChannels.Contains(channel, StringComparer.OrdinalIgnoreCase);
        }
    }
}
