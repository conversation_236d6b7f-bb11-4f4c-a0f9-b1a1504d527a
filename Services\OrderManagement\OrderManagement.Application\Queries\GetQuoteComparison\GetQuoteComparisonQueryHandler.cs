using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.DTOs;
using OrderManagement.Application.Interfaces;

namespace OrderManagement.Application.Queries.GetQuoteComparison;

public class GetQuoteComparisonQueryHandler : IRequestHandler<GetQuoteComparisonQuery, QuoteComparisonDto?>
{
    private readonly IRfqRepository _rfqRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetQuoteComparisonQueryHandler> _logger;

    public GetQuoteComparisonQueryHandler(
        IRfqRepository rfqRepository,
        IMapper mapper,
        ILogger<GetQuoteComparisonQueryHandler> logger)
    {
        _rfqRepository = rfqRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<QuoteComparisonDto?> Handle(GetQuoteComparisonQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting quote comparison for RFQ {RfqId} by user {UserId}", 
            request.RfqId, request.RequestingUserId);

        try
        {
            // Get the RFQ with bids
            var rfq = await _rfqRepository.GetByIdWithBidsAsync(request.RfqId, cancellationToken);
            if (rfq == null)
            {
                _logger.LogWarning("RFQ {RfqId} not found", request.RfqId);
                return null;
            }

            // Validate user has permission to view this RFQ
            if (rfq.TransportCompanyId != request.RequestingUserId)
            {
                _logger.LogWarning("User {UserId} does not have permission to view RFQ {RfqId}", 
                    request.RequestingUserId, request.RfqId);
                throw new UnauthorizedAccessException("You do not have permission to view this RFQ");
            }

            // Get bids for comparison
            var bidsForComparison = rfq.Bids.Where(b => 
                b.Status == Domain.Enums.BidStatus.Submitted || 
                b.Status == Domain.Enums.BidStatus.Accepted).ToList();

            if (request.IncludeCounterOffers)
            {
                // Include counter offers in comparison
                bidsForComparison = bidsForComparison.Where(b => 
                    b.Status == Domain.Enums.BidStatus.Submitted || 
                    b.Status == Domain.Enums.BidStatus.Accepted ||
                    b.IsCounterOffer).ToList();
            }

            if (!bidsForComparison.Any())
            {
                _logger.LogInformation("No bids available for comparison for RFQ {RfqId}", request.RfqId);
                return null;
            }

            // Check if we have an existing comparison or need to create a new one
            var existingComparison = rfq.GetLatestQuoteComparison();
            
            Domain.Entities.QuoteComparison comparison;
            if (existingComparison == null || ShouldRegenerateComparison(existingComparison, bidsForComparison))
            {
                // Create new comparison
                var comparisonCriteria = request.ComparisonCriteria.Any() 
                    ? request.ComparisonCriteria 
                    : GetDefaultComparisonCriteria();

                comparison = rfq.CreateQuoteComparison(
                    request.RequestingUserId, 
                    bidsForComparison, 
                    comparisonCriteria);

                // Save the comparison
                _rfqRepository.Update(rfq);
                await _rfqRepository.SaveChangesAsync(cancellationToken);
            }
            else
            {
                comparison = existingComparison;
            }

            // Map to DTO
            var comparisonDto = MapToQuoteComparisonDto(comparison, rfq);

            _logger.LogInformation("Successfully generated quote comparison for RFQ {RfqId} with {Count} quotes", 
                request.RfqId, comparison.TotalQuotes);

            return comparisonDto;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting quote comparison for RFQ {RfqId}", request.RfqId);
            throw;
        }
    }

    private bool ShouldRegenerateComparison(Domain.Entities.QuoteComparison existingComparison, List<Domain.Entities.RfqBid> currentBids)
    {
        // Regenerate if the number of bids has changed or if the comparison is older than 1 hour
        return existingComparison.TotalQuotes != currentBids.Count ||
               DateTime.UtcNow - existingComparison.GeneratedAt > TimeSpan.FromHours(1);
    }

    private List<string> GetDefaultComparisonCriteria()
    {
        return new List<string>
        {
            "Price",
            "Pickup Date",
            "Delivery Date",
            "Broker Rating",
            "Vehicle Details",
            "Additional Services"
        };
    }

    private QuoteComparisonDto MapToQuoteComparisonDto(Domain.Entities.QuoteComparison comparison, Domain.Entities.RFQ rfq)
    {
        return new QuoteComparisonDto
        {
            RfqId = comparison.RfqId,
            RfqTitle = rfq.Title,
            GeneratedAt = comparison.GeneratedAt,
            RecommendedQuoteId = comparison.RecommendedBidId,
            RecommendationReason = comparison.RecommendationReason,
            ComparisonCriteria = comparison.ComparisonCriteria.ToList(),
            Quotes = comparison.ComparisonItems.Select(MapToQuoteComparisonItemDto).ToList(),
            Summary = new QuoteComparisonSummaryDto
            {
                LowestPrice = _mapper.Map<MoneyDto>(comparison.LowestPrice),
                HighestPrice = _mapper.Map<MoneyDto>(comparison.HighestPrice),
                AveragePrice = _mapper.Map<MoneyDto>(comparison.AveragePrice),
                EarliestPickup = comparison.EarliestPickup,
                LatestPickup = comparison.LatestPickup,
                EarliestDelivery = comparison.EarliestDelivery,
                LatestDelivery = comparison.LatestDelivery,
                HighestRatedBroker = comparison.HighestRatedBroker,
                LowestRatedBroker = comparison.LowestRatedBroker,
                AverageRating = comparison.AverageRating,
                TotalQuotes = comparison.TotalQuotes,
                CounterOffers = comparison.CounterOffers,
                PriceDifference = _mapper.Map<MoneyDto>(comparison.GetPriceDifference()),
                DeliveryTimeRange = comparison.GetDeliveryTimeRange()
            }
        };
    }

    private QuoteComparisonItemDto MapToQuoteComparisonItemDto(Domain.Entities.QuoteComparisonItem item)
    {
        return new QuoteComparisonItemDto
        {
            BidId = item.BidId,
            BrokerId = item.BrokerId,
            BrokerName = "Broker Name", // Would need to fetch from external service
            BidNumber = "BID-" + item.BidId.ToString()[..8],
            QuotedPrice = _mapper.Map<MoneyDto>(item.QuotedPrice),
            EstimatedPickupDate = item.EstimatedPickupDate,
            EstimatedDeliveryDate = item.EstimatedDeliveryDate,
            EstimatedDuration = item.GetEstimatedDuration(),
            BrokerRating = 4.0m, // Would need to fetch from external service
            VehicleDetails = item.VehicleDetails,
            DriverDetails = item.DriverDetails,
            AdditionalServices = item.AdditionalServices,
            Advantages = item.Advantages.ToList(),
            Disadvantages = item.Disadvantages.ToList(),
            IsRecommended = item.IsRecommended,
            RecommendationScore = item.RecommendationScore?.ToString("F2"),
            IsCounterOffer = item.IsCounterOffer,
            OriginalBidId = item.OriginalBidId
        };
    }
}
