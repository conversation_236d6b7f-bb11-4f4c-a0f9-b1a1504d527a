using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Hosting;
using Microsoft.AspNetCore.Builder;
using AnalyticsBIService.Domain.Exceptions;
using System.Net;
using System.Text.Json;
using FluentValidation;

namespace AnalyticsBIService.Infrastructure.Middleware;

/// <summary>
/// Global error handling middleware for AnalyticsBIService
/// </summary>
public class ErrorHandlingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<ErrorHandlingMiddleware> _logger;
    private readonly IHostEnvironment _environment;

    public ErrorHandlingMiddleware(
        RequestDelegate next,
        ILogger<ErrorHandlingMiddleware> logger,
        IHostEnvironment environment)
    {
        _next = next;
        _logger = logger;
        _environment = environment;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An unhandled exception occurred while processing the request. Path: {Path}, Method: {Method}, TraceId: {TraceId}",
                context.Request.Path, context.Request.Method, context.TraceIdentifier);
            await HandleExceptionAsync(context, ex);
        }
    }

    private async Task HandleExceptionAsync(HttpContext context, Exception exception)
    {
        var response = context.Response;
        response.ContentType = "application/json";

        var errorResponse = new ErrorResponse();

        switch (exception)
        {
            case ValidationException validationEx:
                response.StatusCode = (int)HttpStatusCode.BadRequest;
                errorResponse.Message = "Validation failed";
                errorResponse.Code = "VALIDATION_ERROR";
                errorResponse.Details = validationEx.Errors.Select(e => new ErrorDetail
                {
                    Field = e.PropertyName,
                    Message = e.ErrorMessage,
                    Code = e.ErrorCode
                }).ToList();
                break;

            case AnalyticsDomainException domainEx:
                response.StatusCode = (int)HttpStatusCode.BadRequest;
                errorResponse.Message = domainEx.Message;
                errorResponse.Code = "DOMAIN_ERROR";
                break;

            case UnauthorizedAccessException:
                response.StatusCode = (int)HttpStatusCode.Forbidden;
                errorResponse.Message = "Access denied";
                errorResponse.Code = "ACCESS_DENIED";
                break;

            case ArgumentNullException nullEx:
                response.StatusCode = (int)HttpStatusCode.BadRequest;
                errorResponse.Message = "Required parameter is missing";
                errorResponse.Code = "MISSING_PARAMETER";
                if (_environment.IsDevelopment())
                {
                    errorResponse.Details = new List<ErrorDetail>
                    {
                        new ErrorDetail { Message = nullEx.Message, Field = nullEx.ParamName }
                    };
                }
                break;

            case ArgumentException argEx:
                response.StatusCode = (int)HttpStatusCode.BadRequest;
                errorResponse.Message = "Invalid argument provided";
                errorResponse.Code = "INVALID_ARGUMENT";
                if (_environment.IsDevelopment())
                {
                    errorResponse.Details = new List<ErrorDetail>
                    {
                        new ErrorDetail { Message = argEx.Message, Field = argEx.ParamName }
                    };
                }
                break;

            case KeyNotFoundException:
                response.StatusCode = (int)HttpStatusCode.NotFound;
                errorResponse.Message = "Requested resource not found";
                errorResponse.Code = "NOT_FOUND";
                break;

            case InvalidOperationException invalidOpEx:
                response.StatusCode = (int)HttpStatusCode.BadRequest;
                errorResponse.Message = "Invalid operation";
                errorResponse.Code = "INVALID_OPERATION";
                if (_environment.IsDevelopment())
                {
                    errorResponse.Details = new List<ErrorDetail>
                    {
                        new ErrorDetail { Message = invalidOpEx.Message }
                    };
                }
                break;

            case TimeoutException:
                response.StatusCode = (int)HttpStatusCode.RequestTimeout;
                errorResponse.Message = "Request timeout";
                errorResponse.Code = "TIMEOUT";
                break;

            case HttpRequestException httpEx:
                response.StatusCode = (int)HttpStatusCode.BadGateway;
                errorResponse.Message = "External service error";
                errorResponse.Code = "EXTERNAL_SERVICE_ERROR";
                errorResponse.Details = new List<ErrorDetail>
                {
                    new ErrorDetail { Message = httpEx.Message }
                };
                break;

            case TaskCanceledException:
                response.StatusCode = (int)HttpStatusCode.RequestTimeout;
                errorResponse.Message = "Request was cancelled or timed out";
                errorResponse.Code = "REQUEST_CANCELLED";
                break;

            case OperationCanceledException:
                response.StatusCode = (int)HttpStatusCode.RequestTimeout;
                errorResponse.Message = "Operation was cancelled";
                errorResponse.Code = "OPERATION_CANCELLED";
                break;

            default:
                response.StatusCode = (int)HttpStatusCode.InternalServerError;
                errorResponse.Message = "An internal server error occurred";
                errorResponse.Code = "INTERNAL_ERROR";

                // Only include exception details in development
                if (_environment.IsDevelopment())
                {
                    errorResponse.Details = new List<ErrorDetail>
                    {
                        new ErrorDetail
                        {
                            Message = exception.Message,
                            Field = exception.GetType().Name
                        }
                    };
                }
                break;
        }

        // Add correlation ID if available
        if (context.Request.Headers.TryGetValue("X-Correlation-ID", out var correlationId))
        {
            errorResponse.CorrelationId = correlationId.FirstOrDefault();
            response.Headers.Add("X-Correlation-ID", correlationId);
        }

        // Add trace ID
        errorResponse.TraceId = context.TraceIdentifier;

        // Add timestamp
        errorResponse.Timestamp = DateTime.UtcNow;

        // Add request path for debugging
        errorResponse.Path = context.Request.Path;

        var jsonResponse = JsonSerializer.Serialize(errorResponse, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = _environment.IsDevelopment()
        });

        await response.WriteAsync(jsonResponse);
    }
}

/// <summary>
/// Standard error response model
/// </summary>
public class ErrorResponse
{
    public string Message { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public string? TraceId { get; set; }
    public string? CorrelationId { get; set; }
    public DateTime Timestamp { get; set; }
    public string? Path { get; set; }
    public List<ErrorDetail>? Details { get; set; }
}

/// <summary>
/// Error detail for validation and specific errors
/// </summary>
public class ErrorDetail
{
    public string? Field { get; set; }
    public string Message { get; set; } = string.Empty;
    public string? Code { get; set; }
}

/// <summary>
/// Extension methods for registering error handling middleware
/// </summary>
public static class ErrorHandlingMiddlewareExtensions
{
    public static IApplicationBuilder UseErrorHandling(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<ErrorHandlingMiddleware>();
    }
}
