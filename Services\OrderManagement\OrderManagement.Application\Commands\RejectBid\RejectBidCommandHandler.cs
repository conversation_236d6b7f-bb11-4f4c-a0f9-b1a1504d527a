using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Exceptions;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Application.Commands.RejectBid;

public class RejectBidCommandHandler : IRequestHandler<RejectBidCommand, bool>
{
    private readonly IRfqBidRepository _bidRepository;
    private readonly IRfqRepository _rfqRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<RejectBidCommandHandler> _logger;

    public RejectBidCommandHandler(
        IRfqBidRepository bidRepository,
        IRfqRepository rfqRepository,
        IUnitOfWork unitOfWork,
        ILogger<RejectBidCommandHandler> logger)
    {
        _bidRepository = bidRepository;
        _rfqRepository = rfqRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<bool> Handle(RejectBidCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Rejecting bid {BidId} by transport company {TransportCompanyId}", 
            request.BidId, request.TransportCompanyId);

        var bid = await _bidRepository.GetByIdAsync(request.BidId, cancellationToken);
        
        if (bid == null)
        {
            _logger.LogWarning("Bid {BidId} not found", request.BidId);
            throw new OrderManagementException("Bid not found");
        }

        // Load the RFQ to check authorization
        var rfq = await _rfqRepository.GetByIdAsync(bid.RfqId, cancellationToken);
        
        if (rfq == null)
        {
            _logger.LogWarning("RFQ {RfqId} not found for bid {BidId}", bid.RfqId, request.BidId);
            throw new OrderManagementException("Associated RFQ not found");
        }

        // Authorization check
        if (rfq.TransportCompanyId != request.TransportCompanyId)
        {
            _logger.LogWarning("Transport company {TransportCompanyId} is not authorized to reject bid {BidId}", 
                request.TransportCompanyId, request.BidId);
            throw new OrderManagementException("You are not authorized to reject this bid");
        }

        // Business rule checks
        if (bid.Status != BidStatus.Submitted)
        {
            _logger.LogWarning("Cannot reject bid {BidId} with status {Status}", request.BidId, bid.Status);
            throw new OrderManagementException($"Cannot reject bid with status {bid.Status}");
        }

        // Reject the bid
        bid.Reject(request.RejectionReason ?? "Bid rejected by transport company");

        _bidRepository.Update(bid);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Successfully rejected bid {BidId}", request.BidId);
        
        return true;
    }
}
