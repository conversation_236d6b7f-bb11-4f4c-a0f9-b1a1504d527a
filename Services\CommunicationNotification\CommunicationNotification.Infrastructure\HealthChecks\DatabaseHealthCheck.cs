using CommunicationNotification.Infrastructure.Persistence;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.EntityFrameworkCore;

namespace CommunicationNotification.Infrastructure.HealthChecks;

/// <summary>
/// Database health check implementation
/// </summary>
public class DatabaseHealthCheck : IHealthCheck
{
    private readonly CommunicationDbContext _dbContext;
    private readonly ILogger<DatabaseHealthCheck> _logger;

    public DatabaseHealthCheck(CommunicationDbContext dbContext, ILogger<DatabaseHealthCheck> logger)
    {
        _dbContext = dbContext;
        _logger = logger;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Starting database health check");

            // Check database connectivity
            var canConnect = await _dbContext.Database.CanConnectAsync(cancellationToken);
            if (!canConnect)
            {
                return HealthCheckResult.Unhealthy("Cannot connect to database");
            }

            // Check if database is responsive
            var startTime = DateTime.UtcNow;
            var messageCount = await _dbContext.Messages.CountAsync(cancellationToken);
            var responseTime = DateTime.UtcNow - startTime;

            var data = new Dictionary<string, object>
            {
                ["database_type"] = "TimescaleDB",
                ["connection_state"] = _dbContext.Database.GetDbConnection().State.ToString(),
                ["message_count"] = messageCount,
                ["response_time_ms"] = responseTime.TotalMilliseconds,
                ["last_checked"] = DateTime.UtcNow
            };

            // Check response time threshold
            if (responseTime.TotalSeconds > 5)
            {
                _logger.LogWarning("Database response time is slow: {ResponseTime}ms", responseTime.TotalMilliseconds);
                return HealthCheckResult.Degraded("Database response time is slow", data: data);
            }

            _logger.LogDebug("Database health check completed successfully");
            return HealthCheckResult.Healthy("Database is healthy", data);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Database health check failed");
            return HealthCheckResult.Unhealthy("Database health check failed", ex);
        }
    }
}

/// <summary>
/// External services health check implementation
/// </summary>
public class ExternalServicesHealthCheck : IHealthCheck
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<ExternalServicesHealthCheck> _logger;

    public ExternalServicesHealthCheck(IServiceProvider serviceProvider, ILogger<ExternalServicesHealthCheck> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Starting external services health check");

            var healthData = new Dictionary<string, object>();
            var unhealthyServices = new List<string>();
            var degradedServices = new List<string>();

            // Check external service factory if available
            using var scope = _serviceProvider.CreateScope();
            var serviceFactory = scope.ServiceProvider.GetService<Application.Interfaces.IExternalServiceFactory>();
            
            if (serviceFactory != null)
            {
                var healthStatuses = await serviceFactory.CheckAllProvidersHealthAsync(cancellationToken);
                
                foreach (var status in healthStatuses)
                {
                    var key = $"{status.Channel}_{status.ProviderType}";
                    healthData[key] = new
                    {
                        status.IsHealthy,
                        status.ErrorMessage,
                        ResponseTimeMs = status.ResponseTime?.TotalMilliseconds,
                        status.CheckedAt
                    };

                    if (!status.IsHealthy)
                    {
                        unhealthyServices.Add(key);
                    }
                    else if (status.ResponseTime?.TotalMilliseconds > 3000)
                    {
                        degradedServices.Add(key);
                    }
                }

                healthData["total_services"] = healthStatuses.Count;
                healthData["healthy_services"] = healthStatuses.Count(s => s.IsHealthy);
                healthData["unhealthy_services"] = unhealthyServices.Count;
                healthData["degraded_services"] = degradedServices.Count;
            }
            else
            {
                healthData["external_service_factory"] = "Not available";
            }

            healthData["last_checked"] = DateTime.UtcNow;

            if (unhealthyServices.Any())
            {
                var message = $"External services unhealthy: {string.Join(", ", unhealthyServices)}";
                _logger.LogWarning(message);
                return HealthCheckResult.Unhealthy(message, data: healthData);
            }

            if (degradedServices.Any())
            {
                var message = $"External services degraded: {string.Join(", ", degradedServices)}";
                _logger.LogWarning(message);
                return HealthCheckResult.Degraded(message, data: healthData);
            }

            _logger.LogDebug("External services health check completed successfully");
            return HealthCheckResult.Healthy("All external services are healthy", healthData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "External services health check failed");
            return HealthCheckResult.Unhealthy("External services health check failed", ex);
        }
    }
}

/// <summary>
/// SignalR health check implementation
/// </summary>
public class SignalRHealthCheck : IHealthCheck
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<SignalRHealthCheck> _logger;

    public SignalRHealthCheck(IServiceProvider serviceProvider, ILogger<SignalRHealthCheck> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Starting SignalR health check");

            var healthData = new Dictionary<string, object>();

            // Check if SignalR hub context is available
            using var scope = _serviceProvider.CreateScope();
            var hubContext = scope.ServiceProvider.GetService<Microsoft.AspNetCore.SignalR.IHubContext<Hubs.ChatHub>>();
            
            if (hubContext != null)
            {
                healthData["signalr_hub"] = "Available";
                healthData["hub_type"] = "ChatHub";
                
                // Try to get connected clients count (this is a basic connectivity test)
                try
                {
                    // This is a simple test to ensure the hub context is working
                    var clients = hubContext.Clients.All;
                    healthData["clients_accessible"] = true;
                }
                catch (Exception ex)
                {
                    healthData["clients_accessible"] = false;
                    healthData["clients_error"] = ex.Message;
                    _logger.LogWarning(ex, "SignalR clients not accessible");
                    return HealthCheckResult.Degraded("SignalR clients not accessible", data: healthData);
                }
            }
            else
            {
                healthData["signalr_hub"] = "Not available";
                _logger.LogWarning("SignalR hub context not available");
                return HealthCheckResult.Degraded("SignalR hub context not available", data: healthData);
            }

            // Check Redis connection if using Redis backplane
            var distributedCache = scope.ServiceProvider.GetService<Microsoft.Extensions.Caching.Distributed.IDistributedCache>();
            if (distributedCache != null)
            {
                try
                {
                    var testKey = $"health_check_{Guid.NewGuid()}";
                    var testValue = "test";
                    
                    await distributedCache.SetStringAsync(testKey, testValue, cancellationToken);
                    var retrievedValue = await distributedCache.GetStringAsync(testKey, cancellationToken);
                    await distributedCache.RemoveAsync(testKey, cancellationToken);
                    
                    healthData["distributed_cache"] = retrievedValue == testValue ? "Healthy" : "Unhealthy";
                }
                catch (Exception ex)
                {
                    healthData["distributed_cache"] = "Unhealthy";
                    healthData["cache_error"] = ex.Message;
                    _logger.LogWarning(ex, "Distributed cache health check failed");
                    return HealthCheckResult.Degraded("Distributed cache unhealthy", data: healthData);
                }
            }
            else
            {
                healthData["distributed_cache"] = "Not configured";
            }

            healthData["last_checked"] = DateTime.UtcNow;

            _logger.LogDebug("SignalR health check completed successfully");
            return HealthCheckResult.Healthy("SignalR is healthy", healthData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "SignalR health check failed");
            return HealthCheckResult.Unhealthy("SignalR health check failed", ex);
        }
    }
}

/// <summary>
/// Memory health check implementation
/// </summary>
public class MemoryHealthCheck : IHealthCheck
{
    private readonly ILogger<MemoryHealthCheck> _logger;

    public MemoryHealthCheck(ILogger<MemoryHealthCheck> logger)
    {
        _logger = logger;
    }

    public Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Starting memory health check");

            var gc = GC.GetTotalMemory(false);
            var workingSet = Environment.WorkingSet;
            
            // Convert to MB for readability
            var gcMemoryMB = gc / 1024 / 1024;
            var workingSetMB = workingSet / 1024 / 1024;

            var healthData = new Dictionary<string, object>
            {
                ["gc_memory_mb"] = gcMemoryMB,
                ["working_set_mb"] = workingSetMB,
                ["gen0_collections"] = GC.CollectionCount(0),
                ["gen1_collections"] = GC.CollectionCount(1),
                ["gen2_collections"] = GC.CollectionCount(2),
                ["last_checked"] = DateTime.UtcNow
            };

            // Check memory thresholds
            const long maxMemoryMB = 1024; // 1GB threshold
            
            if (workingSetMB > maxMemoryMB)
            {
                _logger.LogWarning("High memory usage detected: {WorkingSetMB}MB", workingSetMB);
                return Task.FromResult(HealthCheckResult.Degraded("High memory usage", data: healthData));
            }

            _logger.LogDebug("Memory health check completed successfully");
            return Task.FromResult(HealthCheckResult.Healthy("Memory usage is normal", healthData));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Memory health check failed");
            return Task.FromResult(HealthCheckResult.Unhealthy("Memory health check failed", ex));
        }
    }
}

/// <summary>
/// Application health check implementation
/// </summary>
public class ApplicationHealthCheck : IHealthCheck
{
    private readonly ILogger<ApplicationHealthCheck> _logger;
    private readonly IServiceProvider _serviceProvider;

    public ApplicationHealthCheck(ILogger<ApplicationHealthCheck> logger, IServiceProvider serviceProvider)
    {
        _logger = logger;
        _serviceProvider = serviceProvider;
    }

    public Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Starting application health check");

            var healthData = new Dictionary<string, object>
            {
                ["application_name"] = "TLI Communication & Notification Service",
                ["version"] = Assembly.GetExecutingAssembly().GetName().Version?.ToString() ?? "Unknown",
                ["environment"] = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown",
                ["machine_name"] = Environment.MachineName,
                ["process_id"] = Environment.ProcessId,
                ["uptime"] = DateTime.UtcNow - Process.GetCurrentProcess().StartTime.ToUniversalTime(),
                ["thread_count"] = Process.GetCurrentProcess().Threads.Count,
                ["last_checked"] = DateTime.UtcNow
            };

            // Check critical services
            var criticalServices = new[]
            {
                typeof(MediatR.IMediator),
                typeof(CommunicationDbContext),
                typeof(Microsoft.Extensions.Caching.Memory.IMemoryCache)
            };

            var missingServices = new List<string>();
            
            foreach (var serviceType in criticalServices)
            {
                var service = _serviceProvider.GetService(serviceType);
                if (service == null)
                {
                    missingServices.Add(serviceType.Name);
                }
            }

            if (missingServices.Any())
            {
                healthData["missing_services"] = missingServices;
                var message = $"Critical services missing: {string.Join(", ", missingServices)}";
                _logger.LogError(message);
                return Task.FromResult(HealthCheckResult.Unhealthy(message, data: healthData));
            }

            healthData["critical_services"] = "All available";

            _logger.LogDebug("Application health check completed successfully");
            return Task.FromResult(HealthCheckResult.Healthy("Application is healthy", healthData));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Application health check failed");
            return Task.FromResult(HealthCheckResult.Unhealthy("Application health check failed", ex));
        }
    }
}
