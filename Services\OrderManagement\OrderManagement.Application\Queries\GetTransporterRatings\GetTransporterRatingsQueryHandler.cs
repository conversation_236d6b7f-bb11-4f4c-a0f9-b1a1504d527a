using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.DTOs;
using OrderManagement.Application.Interfaces;

namespace OrderManagement.Application.Queries.GetTransporterRatings;

public class GetTransporterRatingsQueryHandler : IRequestHandler<GetTransporterRatingsQuery, List<TransporterRatingDto>>
{
    private readonly IRfqRepository _rfqRepository;
    private readonly INetworkFleetService _networkFleetService;
    private readonly ILogger<GetTransporterRatingsQueryHandler> _logger;

    public GetTransporterRatingsQueryHandler(
        IRfqRepository rfqRepository,
        INetworkFleetService networkFleetService,
        ILogger<GetTransporterRatingsQueryHandler> logger)
    {
        _rfqRepository = rfqRepository;
        _networkFleetService = networkFleetService;
        _logger = logger;
    }

    public async Task<List<TransporterRatingDto>> Handle(GetTransporterRatingsQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting transporter ratings for RFQ {RfqId} by user {UserId}",
            request.RfqId, request.RequestingUserId);

        try
        {
            // Get the RFQ to validate access and get preferred partners
            var rfq = await _rfqRepository.GetByIdAsync(request.RfqId, cancellationToken);
            if (rfq == null)
            {
                _logger.LogWarning("RFQ {RfqId} not found", request.RfqId);
                throw new ArgumentException($"RFQ {request.RfqId} not found");
            }

            // Validate user has permission to view this RFQ
            if (rfq.TransportCompanyId != request.RequestingUserId)
            {
                _logger.LogWarning("User {UserId} does not have permission to view RFQ {RfqId}",
                    request.RequestingUserId, request.RfqId);
                throw new UnauthorizedAccessException("You do not have permission to view this RFQ");
            }

            // Get transporter ratings from NetworkFleetService
            var transporterRatings = await _networkFleetService.GetTransporterRatingsAsync(
                request.ServiceAreas,
                request.VehicleTypes,
                request.MinRating,
                request.MaxResults,
                cancellationToken);

            // If only preferred partners requested, filter the results
            if (request.OnlyPreferredPartners)
            {
                var preferredPartnerIds = rfq.GetActivePreferredPartners()
                    .Where(pp => pp.PartnerType == OrderManagement.Domain.Entities.PartnerType.Transporter)
                    .Select(pp => pp.PartnerId)
                    .ToHashSet();

                transporterRatings = transporterRatings
                    .Where(tr => preferredPartnerIds.Contains(tr.TransporterId))
                    .ToList();
            }

            // Mark preferred partners
            var preferredTransporterIds = rfq.GetActivePreferredPartners()
                .Where(pp => pp.PartnerType == OrderManagement.Domain.Entities.PartnerType.Transporter)
                .Select(pp => pp.PartnerId)
                .ToHashSet();

            foreach (var rating in transporterRatings)
            {
                rating.IsPreferredPartner = preferredTransporterIds.Contains(rating.TransporterId);
            }

            // Apply sorting
            transporterRatings = ApplySorting(transporterRatings, request.SortBy, request.SortDescending);

            _logger.LogInformation("Successfully retrieved {Count} transporter ratings for RFQ {RfqId}",
                transporterRatings.Count, request.RfqId);

            return transporterRatings;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting transporter ratings for RFQ {RfqId}", request.RfqId);
            throw;
        }
    }

    private List<TransporterRatingDto> ApplySorting(List<TransporterRatingDto> ratings, string? sortBy, bool descending)
    {
        return sortBy?.ToLower() switch
        {
            "overallrating" => descending
                ? ratings.OrderByDescending(r => r.OverallRating).ToList()
                : ratings.OrderBy(r => r.OverallRating).ToList(),
            "ontimedeliveryrate" => descending
                ? ratings.OrderByDescending(r => r.OnTimeDeliveryRate).ToList()
                : ratings.OrderBy(r => r.OnTimeDeliveryRate).ToList(),
            "totaltrips" => descending
                ? ratings.OrderByDescending(r => r.TotalTrips).ToList()
                : ratings.OrderBy(r => r.TotalTrips).ToList(),
            "lasttripdate" => descending
                ? ratings.OrderByDescending(r => r.LastTripDate).ToList()
                : ratings.OrderBy(r => r.LastTripDate).ToList(),
            "customersatisfactionscore" => descending
                ? ratings.OrderByDescending(r => r.CustomerSatisfactionScore).ToList()
                : ratings.OrderBy(r => r.CustomerSatisfactionScore).ToList(),
            "safetyrating" => descending
                ? ratings.OrderByDescending(r => r.SafetyRating).ToList()
                : ratings.OrderBy(r => r.SafetyRating).ToList(),
            _ => descending
                ? ratings.OrderByDescending(r => r.OverallRating).ToList()
                : ratings.OrderBy(r => r.OverallRating).ToList()
        };
    }
}
