using MediatR;
using FluentValidation;

namespace UserManagement.Application.Admin.Commands.RejectUser
{
    public class RejectUserCommand : IRequest<RejectUserResponse>
    {
        public Guid UserId { get; set; }
        public string Reason { get; set; } = string.Empty;
        public string Notes { get; set; } = string.Empty;
        public Guid RejectedBy { get; set; }
    }

    public class RejectUserResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public DateTime RejectedAt { get; set; }
    }

    public class RejectUserCommandValidator : AbstractValidator<RejectUserCommand>
    {
        public RejectUserCommandValidator()
        {
            RuleFor(x => x.UserId)
                .NotEmpty()
                .WithMessage("User ID is required");

            RuleFor(x => x.Reason)
                .NotEmpty()
                .WithMessage("Rejection reason is required")
                .MaximumLength(500)
                .WithMessage("Rejection reason cannot exceed 500 characters");

            RuleFor(x => x.RejectedBy)
                .NotEmpty()
                .WithMessage("Rejector ID is required");
        }
    }
}
