using Microsoft.Extensions.Logging;
using SubscriptionManagement.Infrastructure.Services;
using System.Diagnostics;

namespace SubscriptionManagement.API.Middleware
{
    public class MetricsMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ISubscriptionMetricsService _metricsService;
        private readonly ILogger<MetricsMiddleware> _logger;

        public MetricsMiddleware(
            RequestDelegate next,
            ISubscriptionMetricsService metricsService,
            ILogger<MetricsMiddleware> logger)
        {
            _next = next;
            _metricsService = metricsService;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            var stopwatch = Stopwatch.StartNew();
            var endpoint = GetEndpointName(context);
            var success = true;

            try
            {
                await _next(context);
                
                // Consider 4xx and 5xx as failures
                success = context.Response.StatusCode < 400;
            }
            catch (Exception ex)
            {
                success = false;
                _logger.LogError(ex, "Unhandled exception in request to {Endpoint}", endpoint);
                throw;
            }
            finally
            {
                stopwatch.Stop();
                _metricsService.RecordApiCall(endpoint, stopwatch.Elapsed, success);
            }
        }

        private string GetEndpointName(HttpContext context)
        {
            var endpoint = context.GetEndpoint();
            if (endpoint != null)
            {
                var routePattern = endpoint.Metadata.GetMetadata<Microsoft.AspNetCore.Routing.RouteAttribute>()?.Template;
                if (!string.IsNullOrEmpty(routePattern))
                {
                    return $"{context.Request.Method} {routePattern}";
                }
            }

            return $"{context.Request.Method} {context.Request.Path}";
        }
    }

    public static class MetricsMiddlewareExtensions
    {
        public static IApplicationBuilder UseMetrics(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<MetricsMiddleware>();
        }
    }
}
