using CommunicationNotification.Domain.Enums;
using Shared.Domain.Common;

namespace CommunicationNotification.Domain.Entities;

/// <summary>
/// Template version entity for managing template versioning
/// </summary>
public class TemplateVersion : BaseEntity
{
    public Guid TemplateId { get; private set; }
    public int VersionNumber { get; private set; }
    public string Content { get; private set; }
    public string? Subject { get; private set; }
    public MessageType MessageType { get; private set; }
    public string? Variables { get; private set; } // JSON string of template variables
    public bool IsActive { get; private set; }
    public bool IsPublished { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public Guid CreatedBy { get; private set; }
    public DateTime? PublishedAt { get; private set; }
    public Guid? PublishedBy { get; private set; }
    public string? ChangeLog { get; private set; }

    private TemplateVersion()
    {
        Content = string.Empty;
    }

    public TemplateVersion(
        Guid templateId,
        int versionNumber,
        string content,
        MessageType messageType,
        Guid createdBy,
        string? subject = null,
        string? variables = null,
        string? changeLog = null)
    {
        TemplateId = templateId;
        VersionNumber = versionNumber;
        Content = content;
        Subject = subject;
        MessageType = messageType;
        Variables = variables;
        CreatedBy = createdBy;
        CreatedAt = DateTime.UtcNow;
        ChangeLog = changeLog;
        IsActive = false;
        IsPublished = false;
    }

    public void Activate()
    {
        IsActive = true;
    }

    public void Deactivate()
    {
        IsActive = false;
    }

    public void Publish(Guid publishedBy)
    {
        IsPublished = true;
        PublishedAt = DateTime.UtcNow;
        PublishedBy = publishedBy;
        IsActive = true;
    }

    public void UpdateContent(string content, string? subject = null, string? variables = null, string? changeLog = null)
    {
        Content = content;
        Subject = subject;
        Variables = variables;
        ChangeLog = changeLog;
    }
}
