using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Application.Interfaces
{
    public interface IPlanRepository
    {
        Task<Plan?> GetByIdAsync(Guid id);
        Task<Plan?> GetByNameAsync(string name);
        Task<List<Plan>> GetAllAsync();
        Task<List<Plan>> GetActiveAsync();
        Task<List<Plan>> GetPublicAsync();
        Task<List<Plan>> GetByUserTypeAsync(UserType userType);
        Task<List<Plan>> GetByTypeAsync(PlanType type);
        Task<List<Plan>> GetByUserTypeAndTypeAsync(UserType userType, PlanType type);
        Task<List<Plan>> GetPlansAsync(int page, int pageSize);
        Task<int> GetPlansCountAsync();
        Task AddAsync(Plan plan);
        Task UpdateAsync(Plan plan);
        Task DeleteAsync(Plan plan);
        Task<bool> ExistsAsync(Guid id);
        Task<bool> ExistsByNameAsync(string name);
    }
}
