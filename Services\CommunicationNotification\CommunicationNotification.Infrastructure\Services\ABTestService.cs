using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Domain.Entities;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace CommunicationNotification.Infrastructure.Services;

/// <summary>
/// Service for A/B testing functionality
/// </summary>
public class ABTestService : IABTestService
{
    private readonly IABTestRepository _testRepository;
    private readonly IABTestEventService _eventService;
    private readonly IStatisticalAnalysisService _statisticalService;
    private readonly IMessageAnalyticsRepository _analyticsRepository;
    private readonly IDistributedCache _cache;
    private readonly ILogger<ABTestService> _logger;
    private readonly TimeSpan _cacheExpiration = TimeSpan.FromMinutes(10);

    public ABTestService(
        IABTestRepository testRepository,
        IABTestEventService eventService,
        IStatisticalAnalysisService statisticalService,
        IMessageAnalyticsRepository analyticsRepository,
        IDistributedCache cache,
        ILogger<ABTestService> logger)
    {
        _testRepository = testRepository;
        _eventService = eventService;
        _statisticalService = statisticalService;
        _analyticsRepository = analyticsRepository;
        _cache = cache;
        _logger = logger;
    }

    public async Task<ABTest> CreateTestAsync(
        string name,
        string description,
        ABTestType type,
        DateTime startDate,
        int targetSampleSize,
        decimal trafficAllocation,
        string hypothesisStatement,
        string successMetric,
        Guid createdByUserId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating A/B test {TestName} by user {UserId}", name, createdByUserId);

            // Check if test name already exists
            if (await _testRepository.ExistsAsync(name, cancellationToken))
            {
                throw new InvalidOperationException($"A/B test with name '{name}' already exists");
            }

            var test = ABTest.Create(
                name, description, type, startDate, targetSampleSize, trafficAllocation,
                hypothesisStatement, successMetric, createdByUserId);

            var createdTest = await _testRepository.AddAsync(test, cancellationToken);

            _logger.LogInformation("Successfully created A/B test {TestId} with name {TestName}",
                createdTest.Id, createdTest.Name);

            return createdTest;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating A/B test {TestName}", name);
            throw;
        }
    }

    public async Task<ABTest> AddVariantAsync(
        Guid testId,
        string variantName,
        string description,
        decimal trafficAllocation,
        bool isControl,
        Dictionary<string, object> configuration,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Adding variant {VariantName} to A/B test {TestId}", variantName, testId);

            var test = await _testRepository.GetByIdAsync(testId, cancellationToken);
            if (test == null)
            {
                throw new ArgumentException($"A/B test with ID {testId} not found");
            }

            var variant = new ABTestVariant(variantName, description, trafficAllocation, isControl, configuration);
            test.AddVariant(variant);

            var updatedTest = await _testRepository.UpdateAsync(test, cancellationToken);

            // Invalidate cache
            await InvalidateTestCacheAsync(testId, cancellationToken);

            _logger.LogInformation("Successfully added variant {VariantName} to A/B test {TestId}",
                variantName, testId);

            return updatedTest;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding variant {VariantName} to A/B test {TestId}", variantName, testId);
            throw;
        }
    }

    public async Task<ABTest> StartTestAsync(
        Guid testId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting A/B test {TestId}", testId);

            var test = await _testRepository.GetByIdAsync(testId, cancellationToken);
            if (test == null)
            {
                throw new ArgumentException($"A/B test with ID {testId} not found");
            }

            test.Start();
            var updatedTest = await _testRepository.UpdateAsync(test, cancellationToken);

            // Invalidate cache
            await InvalidateTestCacheAsync(testId, cancellationToken);

            _logger.LogInformation("Successfully started A/B test {TestId}", testId);

            return updatedTest;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting A/B test {TestId}", testId);
            throw;
        }
    }

    public async Task<ABTest> StopTestAsync(
        Guid testId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Stopping A/B test {TestId}", testId);

            var test = await _testRepository.GetByIdAsync(testId, cancellationToken);
            if (test == null)
            {
                throw new ArgumentException($"A/B test with ID {testId} not found");
            }

            // Calculate final results before stopping
            var results = await CalculateTestResultsAsync(testId, cancellationToken);
            test.Complete(results);

            var updatedTest = await _testRepository.UpdateAsync(test, cancellationToken);

            // Invalidate cache
            await InvalidateTestCacheAsync(testId, cancellationToken);

            _logger.LogInformation("Successfully stopped A/B test {TestId}", testId);

            return updatedTest;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping A/B test {TestId}", testId);
            throw;
        }
    }

    public async Task<ABTestVariant?> GetVariantForUserAsync(
        Guid testId,
        Guid userId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting variant assignment for user {UserId} in test {TestId}", userId, testId);

            // Check cache first
            var cacheKey = $"ab_test_assignment_{testId}_{userId}";
            var cachedVariant = await GetFromCacheAsync<ABTestVariant>(cacheKey, cancellationToken);
            if (cachedVariant != null)
            {
                return cachedVariant;
            }

            var test = await _testRepository.GetByIdAsync(testId, cancellationToken);
            if (test == null)
            {
                _logger.LogWarning("A/B test {TestId} not found", testId);
                return null;
            }

            var variant = test.GetVariantForUser(userId);
            if (variant != null)
            {
                // Track assignment
                await _eventService.TrackAssignmentAsync(testId, variant.Name, userId, DateTime.UtcNow, cancellationToken);

                // Cache the assignment
                await SetCacheAsync(cacheKey, variant, cancellationToken);

                _logger.LogDebug("Assigned user {UserId} to variant {VariantName} in test {TestId}",
                    userId, variant.Name, testId);
            }

            return variant;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting variant assignment for user {UserId} in test {TestId}", userId, testId);
            return null;
        }
    }

    public async Task RecordTestEventAsync(
        Guid testId,
        string variantName,
        Guid userId,
        string eventType,
        Dictionary<string, object>? eventData = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Recording A/B test event {EventType} for user {UserId} in test {TestId} variant {VariantName}",
                eventType, userId, testId, variantName);

            await _eventService.TrackConversionAsync(testId, variantName, userId, eventType, eventData, cancellationToken);

            _logger.LogDebug("Successfully recorded A/B test event {EventType} for user {UserId}", eventType, userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording A/B test event {EventType} for user {UserId} in test {TestId}",
                eventType, userId, testId);
            throw;
        }
    }

    public async Task<ABTestResults> CalculateTestResultsAsync(
        Guid testId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Calculating A/B test results for test {TestId}", testId);

            var test = await _testRepository.GetByIdAsync(testId, cancellationToken);
            if (test == null)
            {
                throw new ArgumentException($"A/B test with ID {testId} not found");
            }

            // Get test events for analysis
            var events = await _eventService.GetTestEventsAsync(testId, cancellationToken: cancellationToken);

            // Calculate variant statistics
            var variantStats = new Dictionary<string, VariantStatistics>();

            foreach (var variant in test.Variants)
            {
                var variantEvents = events.Where(e => e.VariantName == variant.Name).ToList();
                var assignments = variantEvents.Where(e => e.EventType == "assignment").Count();
                var conversions = variantEvents.Where(e => e.EventType == test.SuccessMetric).Count();

                var conversionRate = assignments > 0 ? (decimal)conversions / assignments : 0;
                var standardError = CalculateStandardError(conversionRate, assignments);
                var (lowerCI, upperCI) = await _statisticalService.CalculateConfidenceIntervalAsync(
                    conversionRate, assignments, 0.95m, cancellationToken);

                var stats = new VariantStatistics(
                    variant.Name, assignments, conversionRate, standardError, lowerCI, upperCI, 0);

                variantStats[variant.Name] = stats;
            }

            // Perform statistical analysis
            var config = StatisticalTestConfig.ForConversionRate(test.SignificanceLevel);
            var results = await _statisticalService.PerformZTestAsync(variantStats, config, cancellationToken);

            _logger.LogInformation("Calculated A/B test results for test {TestId}: Winner = {Winner}, Significant = {IsSignificant}",
                testId, results.WinningVariant, results.IsStatisticallySignificant);

            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating A/B test results for test {TestId}", testId);
            throw;
        }
    }

    public async Task<Dictionary<string, MessagePerformanceMetrics>> GetTestPerformanceAsync(
        Guid testId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Getting A/B test performance for test {TestId}", testId);

            var test = await _testRepository.GetByIdAsync(testId, cancellationToken);
            if (test == null)
            {
                throw new ArgumentException($"A/B test with ID {testId} not found");
            }

            var performance = new Dictionary<string, MessagePerformanceMetrics>();

            foreach (var variant in test.Variants)
            {
                // Get analytics data for this variant
                var analytics = await _analyticsRepository.GetByDateRangeAsync(
                    test.StartDate, test.EndDate ?? DateTime.UtcNow,
                    null, null, null, cancellationToken);

                var variantAnalytics = analytics
                    .Where(a => a.ABTestVariant == variant.Name &&
                               a.Tags.ContainsKey("testId") &&
                               a.Tags["testId"] == testId.ToString())
                    .ToList();

                if (variantAnalytics.Any())
                {
                    var metrics = CalculatePerformanceMetrics(variantAnalytics);
                    performance[variant.Name] = metrics;
                }
                else
                {
                    performance[variant.Name] = MessagePerformanceMetrics.Empty();
                }
            }

            _logger.LogInformation("Retrieved A/B test performance for {VariantCount} variants in test {TestId}",
                performance.Count, testId);

            return performance;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting A/B test performance for test {TestId}", testId);
            throw;
        }
    }

    public async Task<SampleSizeCalculation> CalculateSampleSizeAsync(
        decimal baselineConversionRate,
        decimal minimumDetectableEffect,
        decimal significanceLevel = 0.05m,
        decimal power = 0.8m,
        int numberOfVariants = 2,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Calculating sample size for baseline rate {BaselineRate}, MDE {MDE}",
                baselineConversionRate, minimumDetectableEffect);

            var config = new StatisticalTestConfig("ZTest", significanceLevel, power, minimumDetectableEffect);
            var calculation = await _statisticalService.CalculateSampleSizeAsync(
                baselineConversionRate, minimumDetectableEffect, config, numberOfVariants, cancellationToken);

            _logger.LogInformation("Calculated sample size: {SampleSize} per variant, {TotalSample} total",
                calculation.RequiredSampleSizePerVariant, calculation.TotalRequiredSampleSize);

            return calculation;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating sample size");
            throw;
        }
    }

    public async Task<List<ABTest>> GetActiveTestsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting active A/B tests");

            var cacheKey = "active_ab_tests";
            var cachedTests = await GetFromCacheAsync<List<ABTest>>(cacheKey, cancellationToken);
            if (cachedTests != null)
            {
                return cachedTests;
            }

            var activeTests = await _testRepository.GetActiveTestsAsync(cancellationToken);
            await SetCacheAsync(cacheKey, activeTests, cancellationToken);

            _logger.LogDebug("Retrieved {Count} active A/B tests", activeTests.Count);
            return activeTests;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active A/B tests");
            throw;
        }
    }

    public async Task<ABTest?> GetTestAsync(Guid testId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting A/B test {TestId}", testId);

            var cacheKey = $"ab_test_{testId}";
            var cachedTest = await GetFromCacheAsync<ABTest>(cacheKey, cancellationToken);
            if (cachedTest != null)
            {
                return cachedTest;
            }

            var test = await _testRepository.GetByIdAsync(testId, cancellationToken);
            if (test != null)
            {
                await SetCacheAsync(cacheKey, test, cancellationToken);
            }

            return test;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting A/B test {TestId}", testId);
            throw;
        }
    }

    public async Task<ABTest> UpdateTestAsync(
        Guid testId,
        ABTest updatedTest,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Updating A/B test {TestId}", testId);

            var test = await _testRepository.UpdateAsync(updatedTest, cancellationToken);
            await InvalidateTestCacheAsync(testId, cancellationToken);

            _logger.LogInformation("Successfully updated A/B test {TestId}", testId);
            return test;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating A/B test {TestId}", testId);
            throw;
        }
    }

    public async Task DeleteTestAsync(Guid testId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Deleting A/B test {TestId}", testId);

            await _testRepository.DeleteAsync(testId, cancellationToken);
            await InvalidateTestCacheAsync(testId, cancellationToken);

            _logger.LogInformation("Successfully deleted A/B test {TestId}", testId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting A/B test {TestId}", testId);
            throw;
        }
    }

    public async Task<List<ABTest>> GetUserTestHistoryAsync(
        Guid userId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting A/B test history for user {UserId}", userId);

            var assignments = await _eventService.GetUserAssignmentsAsync(userId, cancellationToken);
            var testIds = assignments.Select(a => a.TestId).Distinct().ToList();

            var tests = new List<ABTest>();
            foreach (var testId in testIds)
            {
                var test = await GetTestAsync(testId, cancellationToken);
                if (test != null)
                {
                    tests.Add(test);
                }
            }

            _logger.LogDebug("Retrieved {Count} A/B tests for user {UserId}", tests.Count, userId);
            return tests;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting A/B test history for user {UserId}", userId);
            throw;
        }
    }

    public async Task<bool> IsUserEligibleAsync(
        Guid testId,
        Guid userId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var test = await GetTestAsync(testId, cancellationToken);
            if (test == null || test.Status != ABTestStatus.Running)
            {
                return false;
            }

            return test.ShouldIncludeUser(userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking user eligibility for test {TestId} and user {UserId}", testId, userId);
            return false;
        }
    }

    public async Task<Dictionary<string, object>> GetTestInsightsAsync(
        Guid testId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Getting insights for A/B test {TestId}", testId);

            var test = await GetTestAsync(testId, cancellationToken);
            if (test == null)
            {
                throw new ArgumentException($"A/B test with ID {testId} not found");
            }

            var insights = new Dictionary<string, object>();

            // Basic test info
            insights["testName"] = test.Name;
            insights["status"] = test.Status.ToString();
            insights["duration"] = test.EndDate?.Subtract(test.StartDate).TotalDays ??
                                  DateTime.UtcNow.Subtract(test.StartDate).TotalDays;

            // Sample size progress
            insights["currentSampleSize"] = test.CurrentSampleSize;
            insights["targetSampleSize"] = test.TargetSampleSize;
            insights["sampleSizeProgress"] = test.TargetSampleSize > 0 ?
                (decimal)test.CurrentSampleSize / test.TargetSampleSize * 100 : 0;

            // Statistical significance
            if (test.Results != null)
            {
                insights["isStatisticallySignificant"] = test.Results.IsStatisticallySignificant;
                insights["confidenceLevel"] = test.Results.ConfidenceLevel;
                insights["winningVariant"] = test.Results.WinningVariant;
                insights["effectSize"] = test.Results.EffectSize;
            }

            // Performance comparison
            var performance = await GetTestPerformanceAsync(testId, cancellationToken);
            insights["variantPerformance"] = performance;

            // Recommendations
            var recommendations = GenerateRecommendations(test, performance);
            insights["recommendations"] = recommendations;

            return insights;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting insights for A/B test {TestId}", testId);
            throw;
        }
    }

    // Helper methods
    private async Task<T?> GetFromCacheAsync<T>(string key, CancellationToken cancellationToken) where T : class
    {
        try
        {
            var cachedData = await _cache.GetStringAsync(key, cancellationToken);
            if (!string.IsNullOrEmpty(cachedData))
            {
                return JsonSerializer.Deserialize<T>(cachedData);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error retrieving data from cache for key {Key}", key);
        }

        return null;
    }

    private async Task SetCacheAsync<T>(string key, T data, CancellationToken cancellationToken)
    {
        try
        {
            var serializedData = JsonSerializer.Serialize(data);
            await _cache.SetStringAsync(key, serializedData, new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = _cacheExpiration
            }, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error setting cache for key {Key}", key);
        }
    }

    private async Task InvalidateTestCacheAsync(Guid testId, CancellationToken cancellationToken)
    {
        var keysToInvalidate = new[]
        {
            $"ab_test_{testId}",
            "active_ab_tests"
        };

        foreach (var key in keysToInvalidate)
        {
            try
            {
                await _cache.RemoveAsync(key, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error invalidating cache key {Key}", key);
            }
        }
    }

    private MessagePerformanceMetrics CalculatePerformanceMetrics(List<MessageAnalytics> analytics)
    {
        if (!analytics.Any())
            return MessagePerformanceMetrics.Empty();

        var totalMessages = analytics.Count;
        var deliveredMessages = analytics.Count(a => a.IsDelivered);
        var readMessages = analytics.Count(a => a.IsRead);
        var clickedMessages = analytics.Count(a => a.IsClicked);
        var failedMessages = analytics.Count(a => a.IsFailed);

        var deliveryTimes = analytics
            .Where(a => a.DeliveryTime.HasValue)
            .Select(a => a.DeliveryTime!.Value)
            .ToList();

        var readTimes = analytics
            .Where(a => a.ReadTime.HasValue)
            .Select(a => a.ReadTime!.Value)
            .ToList();

        var averageDeliveryTime = deliveryTimes.Any()
            ? TimeSpan.FromTicks((long)deliveryTimes.Average(dt => dt.Ticks))
            : TimeSpan.Zero;

        var averageReadTime = readTimes.Any()
            ? TimeSpan.FromTicks((long)readTimes.Average(rt => rt.Ticks))
            : TimeSpan.Zero;

        var totalCost = analytics.Sum(a => a.Cost);
        var engagementScore = analytics.Average(a => a.GetEngagementScore());

        return new MessagePerformanceMetrics(
            totalMessages, deliveredMessages, readMessages, clickedMessages, failedMessages,
            averageDeliveryTime, averageReadTime, totalCost, engagementScore);
    }

    private List<string> GenerateRecommendations(ABTest test, Dictionary<string, MessagePerformanceMetrics> performance)
    {
        var recommendations = new List<string>();

        if (test.Status == ABTestStatus.Running)
        {
            if (!test.HasReachedTargetSampleSize())
            {
                recommendations.Add("Continue running the test to reach target sample size for statistical significance.");
            }

            if (test.IsStatisticallySignificant())
            {
                recommendations.Add($"Test shows statistical significance. Consider implementing the winning variant: {test.Results?.WinningVariant}");
            }
        }

        // Performance-based recommendations
        var bestPerformingVariant = performance
            .OrderByDescending(kvp => kvp.Value.EngagementScore)
            .FirstOrDefault();

        if (!string.IsNullOrEmpty(bestPerformingVariant.Key))
        {
            recommendations.Add($"Variant '{bestPerformingVariant.Key}' shows the highest engagement score of {bestPerformingVariant.Value.EngagementScore:F2}");
        }

        return recommendations;
    }
}
