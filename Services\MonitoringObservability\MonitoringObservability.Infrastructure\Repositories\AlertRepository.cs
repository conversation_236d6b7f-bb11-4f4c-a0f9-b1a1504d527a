using Microsoft.EntityFrameworkCore;
using MonitoringObservability.Domain.Entities;
using MonitoringObservability.Domain.Interfaces;
using MonitoringObservability.Domain.Enums;
using MonitoringObservability.Infrastructure.Data;

namespace MonitoringObservability.Infrastructure.Repositories;

public class AlertRepository : IAlertRepository
{
    private readonly MonitoringDbContext _context;

    public AlertRepository(MonitoringDbContext context)
    {
        _context = context;
    }

    public async Task<Alert?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.Alerts
            .FirstOrDefaultAsync(a => a.Id == id, cancellationToken);
    }

    public async Task<Alert?> GetByIdWithDetailsAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.Alerts
            .Include(a => a.Notifications)
            .Include(a => a.Comments)
            .FirstOrDefaultAsync(a => a.Id == id, cancellationToken);
    }

    public async Task<IEnumerable<Alert>> GetByServiceAsync(string serviceName, CancellationToken cancellationToken = default)
    {
        return await _context.Alerts
            .Where(a => a.ServiceName == serviceName)
            .OrderByDescending(a => a.TriggeredAt)
            .ToListAsync(cancellationToken);
    }

    public async Task AddAsync(Alert alert, CancellationToken cancellationToken = default)
    {
        await _context.Alerts.AddAsync(alert, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task UpdateAsync(Alert alert, CancellationToken cancellationToken = default)
    {
        _context.Alerts.Update(alert);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var alert = await GetByIdAsync(id, cancellationToken);
        if (alert != null)
        {
            _context.Alerts.Remove(alert);
            await _context.SaveChangesAsync(cancellationToken);
        }
    }

    public async Task<IEnumerable<Alert>> SearchAsync(
        string? serviceName = null,
        AlertSeverity? severity = null,
        AlertStatus? status = null,
        DateTime? triggeredAfter = null,
        DateTime? triggeredBefore = null,
        Guid? assignedToUserId = null,
        int skip = 0,
        int take = 50,
        CancellationToken cancellationToken = default)
    {
        var query = _context.Alerts.AsQueryable();

        if (!string.IsNullOrWhiteSpace(serviceName))
            query = query.Where(a => a.ServiceName == serviceName);

        if (severity.HasValue)
            query = query.Where(a => a.Severity == severity.Value);

        if (status.HasValue)
            query = query.Where(a => a.Status == status.Value);

        if (triggeredAfter.HasValue)
            query = query.Where(a => a.TriggeredAt >= triggeredAfter.Value);

        if (triggeredBefore.HasValue)
            query = query.Where(a => a.TriggeredAt <= triggeredBefore.Value);

        if (assignedToUserId.HasValue)
            query = query.Where(a => a.AssignedToUserId == assignedToUserId.Value);

        return await query
            .OrderByDescending(a => a.TriggeredAt)
            .Skip(skip)
            .Take(take)
            .ToListAsync(cancellationToken);
    }

    public async Task<int> CountAsync(
        string? serviceName = null,
        AlertSeverity? severity = null,
        AlertStatus? status = null,
        DateTime? triggeredAfter = null,
        DateTime? triggeredBefore = null,
        Guid? assignedToUserId = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.Alerts.AsQueryable();

        if (!string.IsNullOrWhiteSpace(serviceName))
            query = query.Where(a => a.ServiceName == serviceName);

        if (severity.HasValue)
            query = query.Where(a => a.Severity == severity.Value);

        if (status.HasValue)
            query = query.Where(a => a.Status == status.Value);

        if (triggeredAfter.HasValue)
            query = query.Where(a => a.TriggeredAt >= triggeredAfter.Value);

        if (triggeredBefore.HasValue)
            query = query.Where(a => a.TriggeredAt <= triggeredBefore.Value);

        if (assignedToUserId.HasValue)
            query = query.Where(a => a.AssignedToUserId == assignedToUserId.Value);

        return await query.CountAsync(cancellationToken);
    }

    public async Task<IEnumerable<Alert>> GetOpenAlertsAsync(CancellationToken cancellationToken = default)
    {
        return await _context.Alerts
            .Where(a => a.Status == AlertStatus.Open || a.Status == AlertStatus.Acknowledged || a.Status == AlertStatus.InProgress)
            .OrderByDescending(a => a.Severity)
            .ThenByDescending(a => a.TriggeredAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Alert>> GetCriticalAlertsAsync(CancellationToken cancellationToken = default)
    {
        return await _context.Alerts
            .Where(a => a.Severity == AlertSeverity.Critical && 
                       (a.Status == AlertStatus.Open || a.Status == AlertStatus.Acknowledged || a.Status == AlertStatus.InProgress))
            .OrderByDescending(a => a.TriggeredAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Alert>> GetUnassignedAlertsAsync(CancellationToken cancellationToken = default)
    {
        return await _context.Alerts
            .Where(a => a.AssignedToUserId == null && 
                       (a.Status == AlertStatus.Open || a.Status == AlertStatus.Acknowledged))
            .OrderByDescending(a => a.Severity)
            .ThenByDescending(a => a.TriggeredAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Alert>> GetAlertsAssignedToUserAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await _context.Alerts
            .Where(a => a.AssignedToUserId == userId && 
                       (a.Status == AlertStatus.Acknowledged || a.Status == AlertStatus.InProgress))
            .OrderByDescending(a => a.Severity)
            .ThenByDescending(a => a.TriggeredAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<Dictionary<AlertSeverity, int>> GetAlertCountBySeverityAsync(DateTime? fromDate = null, CancellationToken cancellationToken = default)
    {
        var query = _context.Alerts.AsQueryable();

        if (fromDate.HasValue)
            query = query.Where(a => a.TriggeredAt >= fromDate.Value);

        return await query
            .GroupBy(a => a.Severity)
            .ToDictionaryAsync(g => g.Key, g => g.Count(), cancellationToken);
    }

    public async Task<Dictionary<string, int>> GetAlertCountByServiceAsync(DateTime? fromDate = null, CancellationToken cancellationToken = default)
    {
        var query = _context.Alerts.AsQueryable();

        if (fromDate.HasValue)
            query = query.Where(a => a.TriggeredAt >= fromDate.Value);

        return await query
            .GroupBy(a => a.ServiceName)
            .ToDictionaryAsync(g => g.Key, g => g.Count(), cancellationToken);
    }

    public async Task<IEnumerable<Alert>> GetExpiredAlertsAsync(TimeSpan maxAge, CancellationToken cancellationToken = default)
    {
        var cutoffDate = DateTime.UtcNow - maxAge;
        
        return await _context.Alerts
            .Where(a => a.TriggeredAt <= cutoffDate && 
                       (a.Status == AlertStatus.Open || a.Status == AlertStatus.Acknowledged))
            .ToListAsync(cancellationToken);
    }
}
