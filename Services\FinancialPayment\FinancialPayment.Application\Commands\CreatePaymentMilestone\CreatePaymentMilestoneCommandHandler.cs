using MediatR;
using Microsoft.Extensions.Logging;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Application.Interfaces.Repositories;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.ValueObjects;
using Shared.Infrastructure.Interfaces;

namespace FinancialPayment.Application.Commands.CreatePaymentMilestone;

public class CreatePaymentMilestoneCommandHandler : IRequestHandler<CreatePaymentMilestoneCommand, Guid>
{
    private readonly IEscrowAccountRepository _escrowRepository;
    private readonly IPaymentMilestoneRepository _milestoneRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<CreatePaymentMilestoneCommandHandler> _logger;

    public CreatePaymentMilestoneCommandHandler(
        IEscrowAccountRepository escrowRepository,
        IPaymentMilestoneRepository milestoneRepository,
        IUnitOfWork unitOfWork,
        ILogger<CreatePaymentMilestoneCommandHandler> logger)
    {
        _escrowRepository = escrowRepository;
        _milestoneRepository = milestoneRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<Guid> Handle(CreatePaymentMilestoneCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Creating payment milestone for escrow account {EscrowAccountId} by user {UserId}",
            request.EscrowAccountId, request.RequestingUserId);

        try
        {
            // Get the escrow account
            var escrowAccount = await _escrowRepository.GetByIdAsync(request.EscrowAccountId, cancellationToken);
            if (escrowAccount == null)
            {
                _logger.LogWarning("Escrow account {EscrowAccountId} not found", request.EscrowAccountId);
                throw new ArgumentException($"Escrow account {request.EscrowAccountId} not found");
            }

            // Validate user has permission to create milestones for this escrow account
            if (escrowAccount.TransportCompanyId != request.RequestingUserId)
            {
                _logger.LogWarning("User {UserId} does not have permission to create milestones for escrow account {EscrowAccountId}",
                    request.RequestingUserId, request.EscrowAccountId);
                throw new UnauthorizedAccessException("You do not have permission to create milestones for this escrow account");
            }

            // Validate escrow account is in a state that allows milestone creation
            if (escrowAccount.Status != Domain.Enums.EscrowStatus.Created &&
                escrowAccount.Status != Domain.Enums.EscrowStatus.PartiallyFunded)
            {
                _logger.LogWarning("Cannot create milestone for escrow account {EscrowAccountId} with status {Status}",
                    request.EscrowAccountId, escrowAccount.Status);
                throw new InvalidOperationException($"Cannot create milestone for escrow account with status {escrowAccount.Status}");
            }

            // Validate sequence number is unique
            var existingMilestones = await _milestoneRepository.GetByEscrowAccountIdAsync(request.EscrowAccountId, cancellationToken);
            if (existingMilestones.Any(m => m.SequenceNumber == request.SequenceNumber))
            {
                throw new InvalidOperationException($"Milestone with sequence number {request.SequenceNumber} already exists");
            }

            // Convert amount DTO to domain value object
            var amount = new Money(request.Amount.Amount, request.Amount.Currency);

            // Create the payment milestone
            var milestone = new PaymentMilestone(
                request.EscrowAccountId,
                request.Name,
                request.Description,
                amount,
                request.PayoutPercentage,
                request.SequenceNumber,
                request.DueDate,
                request.IsRequired,
                request.RequiresApproval,
                request.TripLegReference,
                request.OrderReference);

            // Add completion criteria
            foreach (var criterion in request.CompletionCriteria)
            {
                milestone.AddCompletionCriterion(criterion);
            }

            // Add payout rules
            foreach (var ruleRequest in request.PayoutRules)
            {
                var rule = new MilestonePayoutRule(
                    milestone.Id,
                    ruleRequest.RuleName,
                    ruleRequest.RuleType,
                    ruleRequest.RuleValue,
                    ruleRequest.Priority,
                    ruleRequest.Condition,
                    ruleRequest.Description);

                milestone.AddPayoutRule(rule);
            }

            // Save the milestone
            await _milestoneRepository.AddAsync(milestone, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Successfully created payment milestone {MilestoneId} for escrow account {EscrowAccountId}",
                milestone.Id, request.EscrowAccountId);

            return milestone.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating payment milestone for escrow account {EscrowAccountId}",
                request.EscrowAccountId);
            throw;
        }
    }
}
