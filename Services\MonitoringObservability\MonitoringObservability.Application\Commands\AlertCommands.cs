using MediatR;
using MonitoringObservability.Application.DTOs;
using MonitoringObservability.Domain.Enums;

namespace MonitoringObservability.Application.Commands;

// Alert management commands
public class CreateAlertCommand : IRequest<AlertDto>
{
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public AlertSeverity Severity { get; set; }
    public string Source { get; set; } = string.Empty;
    public string ServiceName { get; set; } = string.Empty;
    public string MetricName { get; set; } = string.Empty;
    public double CurrentValue { get; set; }
    public AlertThresholdDto Threshold { get; set; } = null!;
    public Dictionary<string, string>? Tags { get; set; }
}

public class AcknowledgeAlertCommand : IRequest<AlertDto>
{
    public Guid AlertId { get; set; }
    public Guid UserId { get; set; }
    public string? UserName { get; set; }
}

public class AssignAlertCommand : IRequest<AlertDto>
{
    public Guid AlertId { get; set; }
    public Guid AssignedToUserId { get; set; }
    public string AssignedToUserName { get; set; } = string.Empty;
    public Guid AssignedByUserId { get; set; }
}

public class ResolveAlertCommand : IRequest<AlertDto>
{
    public Guid AlertId { get; set; }
    public Guid UserId { get; set; }
    public string? ResolutionNotes { get; set; }
}

public class CloseAlertCommand : IRequest<AlertDto>
{
    public Guid AlertId { get; set; }
    public Guid UserId { get; set; }
    public string? ClosureNotes { get; set; }
}

public class EscalateAlertCommand : IRequest<AlertDto>
{
    public Guid AlertId { get; set; }
    public int EscalationLevel { get; set; }
    public string Reason { get; set; } = string.Empty;
    public Guid EscalatedByUserId { get; set; }
}

public class SuppressAlertCommand : IRequest<AlertDto>
{
    public Guid AlertId { get; set; }
    public TimeSpan Duration { get; set; }
    public Guid UserId { get; set; }
    public string Reason { get; set; } = string.Empty;
}

public class AddAlertCommentCommand : IRequest<AlertCommentDto>
{
    public Guid AlertId { get; set; }
    public Guid UserId { get; set; }
    public string UserName { get; set; } = string.Empty;
    public string Comment { get; set; } = string.Empty;
}

public class SendAlertNotificationCommand : IRequest<Unit>
{
    public Guid AlertId { get; set; }
    public NotificationChannel Channel { get; set; }
    public string Recipient { get; set; } = string.Empty;
}

public class BulkUpdateAlertsCommand : IRequest<IEnumerable<AlertDto>>
{
    public IEnumerable<Guid> AlertIds { get; set; } = new List<Guid>();
    public AlertStatus? NewStatus { get; set; }
    public Guid? AssignToUserId { get; set; }
    public string? AssignToUserName { get; set; }
    public Guid UpdatedByUserId { get; set; }
    public string? UpdateReason { get; set; }
}

// Metric management commands
public class CreateMetricCommand : IRequest<MetricDto>
{
    public string Name { get; set; } = string.Empty;
    public string ServiceName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public MetricType Type { get; set; }
    public string Unit { get; set; } = string.Empty;
    public MonitoringCategory Category { get; set; }
    public TimeSpan? RetentionPeriod { get; set; }
    public TimeSpan? AggregationInterval { get; set; }
    public Dictionary<string, string>? Tags { get; set; }
    public Dictionary<string, string>? Metadata { get; set; }
}

public class RecordMetricValueCommand : IRequest<Unit>
{
    public Guid MetricId { get; set; }
    public double Value { get; set; }
    public DateTime? Timestamp { get; set; }
    public Dictionary<string, string>? AdditionalTags { get; set; }
}

public class RecordMetricByNameCommand : IRequest<Unit>
{
    public string ServiceName { get; set; } = string.Empty;
    public string MetricName { get; set; } = string.Empty;
    public double Value { get; set; }
    public string Unit { get; set; } = string.Empty;
    public MetricType Type { get; set; }
    public DateTime? Timestamp { get; set; }
    public Dictionary<string, string>? Tags { get; set; }
}

public class SetMetricThresholdCommand : IRequest<MetricDto>
{
    public Guid MetricId { get; set; }
    public AlertThresholdDto Threshold { get; set; } = null!;
}

public class EnableMetricAlertingCommand : IRequest<MetricDto>
{
    public Guid MetricId { get; set; }
}

public class DisableMetricAlertingCommand : IRequest<MetricDto>
{
    public Guid MetricId { get; set; }
}

public class UpdateMetricMetadataCommand : IRequest<MetricDto>
{
    public Guid MetricId { get; set; }
    public Dictionary<string, string> Metadata { get; set; } = new();
}

public class DeactivateMetricCommand : IRequest<MetricDto>
{
    public Guid MetricId { get; set; }
}

// Batch metric recording
public class RecordMetricsBatchCommand : IRequest<Unit>
{
    public IEnumerable<MetricRecordingDto> Metrics { get; set; } = new List<MetricRecordingDto>();
}

public class MetricRecordingDto
{
    public string ServiceName { get; set; } = string.Empty;
    public string MetricName { get; set; } = string.Empty;
    public double Value { get; set; }
    public string Unit { get; set; } = string.Empty;
    public MetricType Type { get; set; }
    public DateTime? Timestamp { get; set; }
    public Dictionary<string, string>? Tags { get; set; }
}

// Performance metrics commands
public class RecordPerformanceMetricsCommand : IRequest<Unit>
{
    public string ServiceName { get; set; } = string.Empty;
    public PerformanceMetricsDto Metrics { get; set; } = null!;
}

public class RecordResponseTimeCommand : IRequest<Unit>
{
    public string ServiceName { get; set; } = string.Empty;
    public string Endpoint { get; set; } = string.Empty;
    public TimeSpan ResponseTime { get; set; }
    public int StatusCode { get; set; }
    public Dictionary<string, string>? Tags { get; set; }
}

public class RecordThroughputCommand : IRequest<Unit>
{
    public string ServiceName { get; set; } = string.Empty;
    public string Endpoint { get; set; } = string.Empty;
    public int RequestCount { get; set; }
    public TimeSpan Period { get; set; }
    public Dictionary<string, string>? Tags { get; set; }
}

public class RecordErrorRateCommand : IRequest<Unit>
{
    public string ServiceName { get; set; } = string.Empty;
    public string Endpoint { get; set; } = string.Empty;
    public double ErrorRate { get; set; }
    public Dictionary<string, string>? Tags { get; set; }
}
