using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;
using Shared.Domain.Common;

namespace CommunicationNotification.Domain.Entities;

/// <summary>
/// Advanced message performance report entity with comprehensive analytics
/// </summary>
public class MessagePerformanceReport : BaseEntity
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public ReportType Type { get; private set; }
    public ReportStatus Status { get; private set; }
    public DateTime StartDate { get; private set; }
    public DateTime EndDate { get; private set; }
    public List<ReportFilter> Filters { get; private set; }
    public PerformanceMetrics Metrics { get; private set; }
    public ConversionAnalytics ConversionAnalytics { get; private set; }
    public ROIAnalysis ROIAnalysis { get; private set; }
    public List<SegmentPerformance> SegmentPerformance { get; private set; }
    public List<CohortAnalysis> CohortAnalysis { get; private set; }
    public List<FunnelAnalysis> FunnelSteps { get; private set; }
    public AttributionAnalysis AttributionAnalysis { get; private set; }
    public List<Benchmark> Benchmarks { get; private set; }
    public List<Insight> Insights { get; private set; }
    public List<Recommendation> Recommendations { get; private set; }
    public ReportConfiguration Configuration { get; private set; }
    public Guid GeneratedByUserId { get; private set; }
    public DateTime? GeneratedAt { get; private set; }
    public DateTime? ExpiresAt { get; private set; }

    private MessagePerformanceReport()
    {
        Name = string.Empty;
        Description = string.Empty;
        Filters = new List<ReportFilter>();
        Metrics = PerformanceMetrics.Empty();
        ConversionAnalytics = ConversionAnalytics.Empty();
        ROIAnalysis = ROIAnalysis.Empty();
        SegmentPerformance = new List<SegmentPerformance>();
        CohortAnalysis = new List<CohortAnalysis>();
        FunnelSteps = new List<FunnelAnalysis>();
        AttributionAnalysis = AttributionAnalysis.Empty();
        Benchmarks = new List<Benchmark>();
        Insights = new List<Insight>();
        Recommendations = new List<Recommendation>();
        Configuration = ReportConfiguration.Default();
    }

    public MessagePerformanceReport(
        string name,
        string description,
        ReportType type,
        DateTime startDate,
        DateTime endDate,
        List<ReportFilter>? filters,
        ReportConfiguration? configuration,
        Guid generatedByUserId,
        DateTime? expiresAt = null)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Report name cannot be empty", nameof(name));
        if (startDate >= endDate)
            throw new ArgumentException("Start date must be before end date");
        if (generatedByUserId == Guid.Empty)
            throw new ArgumentException("Generated by user ID cannot be empty", nameof(generatedByUserId));

        Name = name;
        Description = description;
        Type = type;
        Status = ReportStatus.Pending;
        StartDate = startDate;
        EndDate = endDate;
        Filters = filters ?? new List<ReportFilter>();
        Configuration = configuration ?? ReportConfiguration.Default();
        GeneratedByUserId = generatedByUserId;
        ExpiresAt = expiresAt ?? DateTime.UtcNow.AddDays(30);

        // Initialize empty collections
        Metrics = PerformanceMetrics.Empty();
        ConversionAnalytics = ConversionAnalytics.Empty();
        ROIAnalysis = ROIAnalysis.Empty();
        SegmentPerformance = new List<SegmentPerformance>();
        CohortAnalysis = new List<CohortAnalysis>();
        FunnelSteps = new List<FunnelAnalysis>();
        AttributionAnalysis = AttributionAnalysis.Empty();
        Benchmarks = new List<Benchmark>();
        Insights = new List<Insight>();
        Recommendations = new List<Recommendation>();
    }

    public static MessagePerformanceReport Create(
        string name,
        string description,
        ReportType type,
        DateTime startDate,
        DateTime endDate,
        List<ReportFilter>? filters,
        ReportConfiguration? configuration,
        Guid generatedByUserId,
        DateTime? expiresAt = null)
    {
        return new MessagePerformanceReport(name, description, type, startDate, endDate,
            filters, configuration, generatedByUserId, expiresAt);
    }

    public void StartGeneration()
    {
        if (Status != ReportStatus.Pending)
            throw new InvalidOperationException($"Cannot start generation when status is {Status}");

        Status = ReportStatus.Generating;
        GeneratedAt = DateTime.UtcNow;
    }

    public void CompleteGeneration(
        PerformanceMetrics metrics,
        ConversionAnalytics conversionAnalytics,
        ROIAnalysis roiAnalysis,
        List<SegmentPerformance> segmentPerformance,
        List<CohortAnalysis> cohortAnalysis,
        List<FunnelAnalysis> funnelSteps,
        AttributionAnalysis attributionAnalysis,
        List<Benchmark> benchmarks,
        List<Insight> insights,
        List<Recommendation> recommendations)
    {
        if (Status != ReportStatus.Generating)
            throw new InvalidOperationException($"Cannot complete generation when status is {Status}");

        Status = ReportStatus.Completed;
        Metrics = metrics ?? throw new ArgumentNullException(nameof(metrics));
        ConversionAnalytics = conversionAnalytics ?? throw new ArgumentNullException(nameof(conversionAnalytics));
        ROIAnalysis = roiAnalysis ?? throw new ArgumentNullException(nameof(roiAnalysis));
        SegmentPerformance = segmentPerformance ?? new List<SegmentPerformance>();
        CohortAnalysis = cohortAnalysis ?? new List<CohortAnalysis>();
        FunnelSteps = funnelSteps ?? new List<FunnelAnalysis>();
        AttributionAnalysis = attributionAnalysis ?? throw new ArgumentNullException(nameof(attributionAnalysis));
        Benchmarks = benchmarks ?? new List<Benchmark>();
        Insights = insights ?? new List<Insight>();
        Recommendations = recommendations ?? new List<Recommendation>();
    }

    public void MarkAsFailed(string errorMessage)
    {
        if (Status != ReportStatus.Generating)
            throw new InvalidOperationException($"Cannot mark as failed when status is {Status}");

        Status = ReportStatus.Failed;
        // Store error message in metadata or separate field
    }

    public void AddFilter(ReportFilter filter)
    {
        if (filter == null)
            throw new ArgumentNullException(nameof(filter));

        if (Status != ReportStatus.Pending)
            throw new InvalidOperationException("Cannot modify filters after generation has started");

        Filters.Add(filter);
    }

    public void RemoveFilter(string filterName)
    {
        if (Status != ReportStatus.Pending)
            throw new InvalidOperationException("Cannot modify filters after generation has started");

        var filter = Filters.FirstOrDefault(f => f.Name.Equals(filterName, StringComparison.OrdinalIgnoreCase));
        if (filter != null)
        {
            Filters.Remove(filter);
        }
    }

    public void AddInsight(Insight insight)
    {
        if (insight == null)
            throw new ArgumentNullException(nameof(insight));

        Insights.Add(insight);
    }

    public void AddRecommendation(Recommendation recommendation)
    {
        if (recommendation == null)
            throw new ArgumentNullException(nameof(recommendation));

        Recommendations.Add(recommendation);
    }

    public bool IsExpired()
    {
        return ExpiresAt.HasValue && DateTime.UtcNow > ExpiresAt.Value;
    }

    public TimeSpan GetReportPeriod()
    {
        return EndDate - StartDate;
    }

    public List<Insight> GetInsightsByCategory(string category)
    {
        return Insights.Where(i => i.Category.Equals(category, StringComparison.OrdinalIgnoreCase)).ToList();
    }

    public List<Recommendation> GetRecommendationsByPriority(RecommendationPriority priority)
    {
        return Recommendations.Where(r => r.Priority == priority).ToList();
    }
}

/// <summary>
/// Report filter for data filtering
/// </summary>
public class ReportFilter
{
    public string Name { get; private set; }
    public FilterType Type { get; private set; }
    public string Value { get; private set; }
    public FilterOperator Operator { get; private set; }
    public bool IsEnabled { get; private set; }

    private ReportFilter()
    {
        Name = string.Empty;
        Value = string.Empty;
    }

    public ReportFilter(string name, FilterType type, string value, FilterOperator @operator = FilterOperator.Equals, bool isEnabled = true)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Filter name cannot be empty", nameof(name));

        Name = name;
        Type = type;
        Value = value;
        Operator = @operator;
        IsEnabled = isEnabled;
    }

    public void Enable()
    {
        IsEnabled = true;
    }

    public void Disable()
    {
        IsEnabled = false;
    }
}

/// <summary>
/// Insight generated from analytics
/// </summary>
public class Insight
{
    public Guid Id { get; private set; }
    public string Title { get; private set; }
    public string Description { get; private set; }
    public string Category { get; private set; }
    public InsightType Type { get; private set; }
    public decimal Impact { get; private set; }
    public decimal Confidence { get; private set; }
    public Dictionary<string, object> Data { get; private set; }
    public DateTime GeneratedAt { get; private set; }

    private Insight()
    {
        Title = string.Empty;
        Description = string.Empty;
        Category = string.Empty;
        Data = new Dictionary<string, object>();
    }

    public Insight(string title, string description, string category, InsightType type, decimal impact, decimal confidence, Dictionary<string, object>? data = null)
    {
        if (string.IsNullOrWhiteSpace(title))
            throw new ArgumentException("Insight title cannot be empty", nameof(title));

        Id = Guid.NewGuid();
        Title = title;
        Description = description;
        Category = category;
        Type = type;
        Impact = impact;
        Confidence = confidence;
        Data = data ?? new Dictionary<string, object>();
        GeneratedAt = DateTime.UtcNow;
    }
}

/// <summary>
/// Recommendation for performance improvement
/// </summary>
public class Recommendation
{
    public Guid Id { get; private set; }
    public string Title { get; private set; }
    public string Description { get; private set; }
    public string Category { get; private set; }
    public RecommendationType Type { get; private set; }
    public RecommendationPriority Priority { get; private set; }
    public decimal EstimatedImpact { get; private set; }
    public string ActionPlan { get; private set; }
    public Dictionary<string, object> Parameters { get; private set; }
    public DateTime GeneratedAt { get; private set; }

    private Recommendation()
    {
        Title = string.Empty;
        Description = string.Empty;
        Category = string.Empty;
        ActionPlan = string.Empty;
        Parameters = new Dictionary<string, object>();
    }

    public Recommendation(string title, string description, string category, RecommendationType type,
        RecommendationPriority priority, decimal estimatedImpact, string actionPlan, Dictionary<string, object>? parameters = null)
    {
        if (string.IsNullOrWhiteSpace(title))
            throw new ArgumentException("Recommendation title cannot be empty", nameof(title));

        Id = Guid.NewGuid();
        Title = title;
        Description = description;
        Category = category;
        Type = type;
        Priority = priority;
        EstimatedImpact = estimatedImpact;
        ActionPlan = actionPlan;
        Parameters = parameters ?? new Dictionary<string, object>();
        GeneratedAt = DateTime.UtcNow;
    }
}

/// <summary>
/// Report status enumeration
/// </summary>
public enum ReportStatus
{
    Pending = 1,
    Generating = 2,
    Completed = 3,
    Failed = 4,
    Expired = 5
}

/// <summary>
/// Report type enumeration
/// </summary>
public enum ReportType
{
    Performance = 1,
    Conversion = 2,
    ROI = 3,
    Engagement = 4,
    Segmentation = 5,
    Cohort = 6,
    Funnel = 7,
    Attribution = 8,
    Comprehensive = 9
}

/// <summary>
/// Filter type enumeration
/// </summary>
public enum FilterType
{
    MessageType = 1,
    Channel = 2,
    UserSegment = 3,
    Campaign = 4,
    ABTestVariant = 5,
    DateRange = 6,
    UserRole = 7,
    Geography = 8
}

/// <summary>
/// Filter operator enumeration
/// </summary>
public enum FilterOperator
{
    Equals = 1,
    NotEquals = 2,
    Contains = 3,
    NotContains = 4,
    GreaterThan = 5,
    LessThan = 6,
    In = 7,
    NotIn = 8
}

/// <summary>
/// Insight type enumeration
/// </summary>
public enum InsightType
{
    Trend = 1,
    Anomaly = 2,
    Opportunity = 3,
    Risk = 4,
    Correlation = 5,
    Prediction = 6
}

/// <summary>
/// Recommendation type enumeration
/// </summary>
public enum RecommendationType
{
    Optimization = 1,
    Strategy = 2,
    Tactical = 3,
    Technical = 4,
    Content = 5,
    Timing = 6,
    Channel = 7,
    Segmentation = 8
}

/// <summary>
/// Recommendation priority enumeration
/// </summary>
public enum RecommendationPriority
{
    Low = 1,
    Medium = 2,
    High = 3,
    Critical = 4
}


