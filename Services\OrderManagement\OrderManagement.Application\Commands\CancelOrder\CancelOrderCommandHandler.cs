using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Exceptions;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Application.Commands.CancelOrder;

public class CancelOrderCommandHandler : IRequestHandler<CancelOrderCommand, bool>
{
    private readonly IOrderRepository _orderRepository;
    private readonly IMessageBroker _messageBroker;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<CancelOrderCommandHandler> _logger;

    public CancelOrderCommandHandler(
        IOrderRepository orderRepository,
        IMessageBroker messageBroker,
        IUnitOfWork unitOfWork,
        ILogger<CancelOrderCommandHandler> logger)
    {
        _orderRepository = orderRepository;
        _messageBroker = messageBroker;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<bool> Handle(CancelOrderCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Cancelling order {OrderId} by user {UserId} with reason: {Reason}", 
            request.OrderId, request.UserId, request.CancellationReason);

        var order = await _orderRepository.GetByIdAsync(request.OrderId, cancellationToken);
        
        if (order == null)
        {
            _logger.LogWarning("Order {OrderId} not found", request.OrderId);
            throw new OrderManagementException("Order not found");
        }

        // Authorization check - only the transport company or broker involved can cancel
        if (order.TransportCompanyId != request.UserId && order.BrokerId != request.UserId)
        {
            _logger.LogWarning("User {UserId} is not authorized to cancel order {OrderId}", 
                request.UserId, request.OrderId);
            throw new OrderManagementException("Not authorized to cancel this order");
        }

        // Business rule validation
        if (order.Status == OrderStatus.Completed || order.Status == OrderStatus.Cancelled)
        {
            _logger.LogWarning("Cannot cancel order {OrderId} with status {Status}", 
                request.OrderId, order.Status);
            throw new OrderManagementException($"Cannot cancel order with status {order.Status}");
        }

        if (string.IsNullOrWhiteSpace(request.CancellationReason))
        {
            _logger.LogWarning("Cancellation reason is required for order {OrderId}", request.OrderId);
            throw new OrderManagementException("Cancellation reason is required");
        }

        try
        {
            // Cancel the order
            order.Cancel(request.CancellationReason);

            // Update repository
            await _orderRepository.UpdateAsync(order, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Publish integration event
            await _messageBroker.PublishAsync("order.cancelled", new
            {
                OrderId = order.Id,
                OrderNumber = order.OrderNumber,
                TransportCompanyId = order.TransportCompanyId,
                BrokerId = order.BrokerId,
                CarrierId = order.CarrierId,
                CancelledAt = order.CancelledAt,
                CancellationReason = order.CancellationReason,
                CancelledBy = request.UserId
            }, cancellationToken);

            _logger.LogInformation("Successfully cancelled order {OrderId} with reason: {Reason}",
                request.OrderId, request.CancellationReason);

            return true;
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Failed to cancel order {OrderId}: {Message}", 
                request.OrderId, ex.Message);
            throw new OrderManagementException(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling order {OrderId}", request.OrderId);
            throw;
        }
    }
}
