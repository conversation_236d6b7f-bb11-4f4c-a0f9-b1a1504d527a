using System;
using System.Threading;
using System.Threading.Tasks;
using Identity.Application.Common.Interfaces;
using Identity.Application.Common.Models;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Identity.Application.Auth.Commands.RefreshToken
{
    public class RefreshTokenCommandHandler : IRequestHandler<RefreshTokenCommand, TokenResponse>
    {
        private readonly ITokenService _tokenService;
        private readonly ILogger<RefreshTokenCommandHandler> _logger;

        public RefreshTokenCommandHandler(
            ITokenService tokenService,
            ILogger<RefreshTokenCommandHandler> logger)
        {
            _tokenService = tokenService;
            _logger = logger;
        }

        public async Task<TokenResponse> Handle(RefreshTokenCommand request, CancellationToken cancellationToken)
        {
            try
            {
                var tokenResponse = await _tokenService.RefreshTokenAsync(request.RefreshToken);
                return tokenResponse;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error refreshing token");
                throw new ApplicationException("Invalid or expired refresh token");
            }
        }
    }
}
