using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using TripManagement.Application.DTOs;
using TripManagement.Application.Interfaces;

namespace TripManagement.Application.Queries.GetTripsByCarrier;

public class GetTripsByCarrierQueryHandler : IRequestHandler<GetTripsByCarrierQuery, PagedResult<TripSummaryDto>>
{
    private readonly ITripRepository _tripRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetTripsByCarrierQueryHandler> _logger;

    public GetTripsByCarrierQueryHandler(
        ITripRepository tripRepository,
        IMapper mapper,
        ILogger<GetTripsByCarrierQueryHandler> logger)
    {
        _tripRepository = tripRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<PagedResult<TripSummaryDto>> Handle(GetTripsByCarrierQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting trips for carrier {CarrierId}, Page: {Page}, PageSize: {PageSize}",
            request.CarrierId, request.Page, request.PageSize);

        try
        {
            // Get all trips for carrier first
            var allTrips = await _tripRepository.GetByCarrierIdAsync(request.CarrierId, cancellationToken);

            // Apply filters
            var filteredTrips = allTrips.AsQueryable();

            if (request.Status.HasValue)
            {
                filteredTrips = filteredTrips.Where(t => t.Status == request.Status.Value);
            }

            if (request.FromDate.HasValue)
            {
                filteredTrips = filteredTrips.Where(t => t.CreatedAt >= request.FromDate.Value);
            }

            if (request.ToDate.HasValue)
            {
                filteredTrips = filteredTrips.Where(t => t.CreatedAt <= request.ToDate.Value);
            }

            if (!string.IsNullOrEmpty(request.SearchTerm))
            {
                filteredTrips = filteredTrips.Where(t =>
                    t.TripNumber.Contains(request.SearchTerm, StringComparison.OrdinalIgnoreCase) ||
                    (t.SpecialInstructions != null && t.SpecialInstructions.Contains(request.SearchTerm, StringComparison.OrdinalIgnoreCase)));
            }

            // Get total count
            var totalCount = filteredTrips.Count();

            // Apply pagination
            var trips = filteredTrips
                .Skip((request.Page - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToList();

            // Map to DTOs
            var tripDtos = _mapper.Map<List<TripSummaryDto>>(trips);

            var result = new PagedResult<TripSummaryDto>
            {
                Items = tripDtos,
                TotalCount = totalCount,
                Page = request.Page,
                PageSize = request.PageSize
            };

            _logger.LogInformation("Successfully retrieved {Count} trips for carrier {CarrierId}",
                tripDtos.Count, request.CarrierId);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting trips for carrier {CarrierId}", request.CarrierId);
            throw;
        }
    }
}
