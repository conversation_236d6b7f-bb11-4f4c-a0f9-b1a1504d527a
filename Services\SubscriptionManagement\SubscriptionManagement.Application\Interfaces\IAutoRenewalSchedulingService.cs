using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SubscriptionManagement.Application.Interfaces
{
    public interface IAutoRenewalSchedulingService
    {
        Task ScheduleRenewalAsync(Guid subscriptionId, DateTime renewalDate);
        Task CancelScheduledRenewalAsync(Guid subscriptionId);
        Task<bool> IsRenewalScheduledAsync(Guid subscriptionId);
        Task<DateTime?> GetScheduledRenewalDateAsync(Guid subscriptionId);
        Task<List<Guid>> GetSubscriptionsDueForRenewalAsync(DateTime date);
        Task<List<Guid>> GetSubscriptionsDueForRenewalAsync(DateTime fromDate, DateTime toDate);
        Task ProcessScheduledRenewalsAsync(DateTime date);
        Task RescheduleRenewalAsync(Guid subscriptionId, DateTime newRenewalDate);
        Task<Dictionary<string, object>> GetRenewalScheduleStatsAsync();
        Task<bool> UpdateRenewalPreferencesAsync(Guid subscriptionId, bool autoRenew, int reminderDays = 7);
        Task<List<Guid>> GetSubscriptionsForReminderAsync(DateTime date, int reminderDays = 7);
        Task SendRenewalRemindersAsync(DateTime date);
        Task<bool> CanScheduleRenewalAsync(Guid subscriptionId);
        Task<TimeSpan> GetOptimalRenewalTimeAsync(Guid subscriptionId);
    }
}
