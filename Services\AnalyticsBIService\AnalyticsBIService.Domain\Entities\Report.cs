using AnalyticsBIService.Domain.Enums;
using AnalyticsBIService.Domain.Events;
using AnalyticsBIService.Domain.ValueObjects;
using Shared.Domain.Common;

namespace AnalyticsBIService.Domain.Entities;

/// <summary>
/// Report entity for business intelligence and analytics reporting
/// </summary>
public class Report : BaseEntity
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public ReportType Type { get; private set; }
    public Guid GeneratedBy { get; private set; }
    public UserType UserType { get; private set; }
    public TimePeriodValue Period { get; private set; }
    public Dictionary<string, object> Parameters { get; private set; }
    public Dictionary<string, object> Configuration { get; private set; }
    public DateTime GeneratedAt { get; private set; }
    public DateTime? ExpiresAt { get; private set; }
    public bool IsScheduled { get; private set; }
    public string? ScheduleCron { get; private set; }
    public int AccessCount { get; private set; }
    public DateTime? LastAccessedAt { get; private set; }
    public bool IsActive { get; private set; } = true;
    public ReportStatus Status { get; private set; } = ReportStatus.Generated;
    public Dictionary<string, string> Properties { get; private set; } = new();
    public Guid CreatedBy => GeneratedBy; // Alias for GeneratedBy
    public Dictionary<string, object> Metadata { get; private set; } = new();

    // Additional properties required by services
    public Guid? UserId => GeneratedBy; // Alias for GeneratedBy
    public Dictionary<string, object> Filters => Parameters; // Alias for Parameters
    public List<string> Columns { get; private set; } = new();
    public string ReportName => Name; // Alias for Name

    private readonly List<ReportSection> _sections = new();
    public IReadOnlyCollection<ReportSection> Sections => _sections.AsReadOnly();

    private readonly List<ReportExport> _exports = new();
    public IReadOnlyCollection<ReportExport> Exports => _exports.AsReadOnly();

    private Report()
    {
        Name = string.Empty;
        Description = string.Empty;
        Period = new TimePeriodValue(DateTime.UtcNow, DateTime.UtcNow.AddDays(1), TimePeriod.Daily);
        Parameters = new Dictionary<string, object>();
        Configuration = new Dictionary<string, object>();
    }

    public Report(
        string name,
        string description,
        ReportType type,
        Guid generatedBy,
        UserType userType,
        TimePeriodValue period,
        Dictionary<string, object>? parameters = null,
        Dictionary<string, object>? configuration = null,
        DateTime? expiresAt = null,
        bool isScheduled = false,
        string? scheduleCron = null)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        Type = type;
        GeneratedBy = generatedBy;
        UserType = userType;
        Period = period ?? throw new ArgumentNullException(nameof(period));
        Parameters = parameters ?? new Dictionary<string, object>();
        Configuration = configuration ?? new Dictionary<string, object>();
        GeneratedAt = DateTime.UtcNow;
        ExpiresAt = expiresAt;
        IsScheduled = isScheduled;
        ScheduleCron = scheduleCron;
        AccessCount = 0;

        // Set initial UpdatedAt timestamp
        SetUpdatedAt();

        // Add domain event
        AddDomainEvent(new ReportGeneratedEvent(Id, Name, Type, GeneratedBy, UserType, GeneratedAt));
    }

    public static Report CreatePlatformUsageReport(Guid generatedBy, TimePeriodValue period)
    {
        return new Report(
            "Platform Usage Report",
            "Comprehensive platform usage and performance metrics",
            ReportType.PlatformUsage,
            generatedBy,
            UserType.Admin,
            period);
    }

    public static Report CreateRevenueAnalysisReport(Guid generatedBy, TimePeriodValue period)
    {
        return new Report(
            "Revenue Analysis Report",
            "Financial performance and revenue analytics",
            ReportType.RevenueAnalysis,
            generatedBy,
            UserType.Admin,
            period);
    }

    public static Report CreateCustomReport(
        string name,
        string description,
        Guid generatedBy,
        UserType userType,
        TimePeriodValue period,
        Dictionary<string, object> configuration)
    {
        return new Report(
            name,
            description,
            ReportType.CustomReport,
            generatedBy,
            userType,
            period,
            configuration: configuration);
    }

    public void AddSection(ReportSection section)
    {
        if (section == null) throw new ArgumentNullException(nameof(section));

        _sections.Add(section);
        SetUpdatedAt();
    }

    public void RemoveSection(Guid sectionId)
    {
        var section = _sections.FirstOrDefault(s => s.Id == sectionId);
        if (section != null)
        {
            _sections.Remove(section);
            SetUpdatedAt();
        }
    }

    public ReportExport CreateExport(ExportFormat format, Guid exportedBy, byte[] data, string fileName)
    {
        var export = new ReportExport(Id, format, exportedBy, data, fileName);
        _exports.Add(export);
        SetUpdatedAt();

        // Add domain event
        AddDomainEvent(new ReportExportedEvent(Id, format, exportedBy, DateTime.UtcNow));

        return export;
    }

    public void RecordAccess()
    {
        AccessCount++;
        LastAccessedAt = DateTime.UtcNow;
        SetUpdatedAt();
    }

    public void SetSchedule(string cronExpression)
    {
        ScheduleCron = cronExpression;
        IsScheduled = !string.IsNullOrWhiteSpace(cronExpression);
        SetUpdatedAt();
    }

    public void RemoveSchedule()
    {
        ScheduleCron = null;
        IsScheduled = false;
        SetUpdatedAt();
    }

    public bool IsExpired => ExpiresAt.HasValue && ExpiresAt.Value < DateTime.UtcNow;

    /// <summary>
    /// Update report name
    /// </summary>
    public void UpdateName(string name)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        SetUpdatedAt();
    }

    /// <summary>
    /// Update report description
    /// </summary>
    public void UpdateDescription(string description)
    {
        Description = description ?? throw new ArgumentNullException(nameof(description));
        SetUpdatedAt();
    }

    /// <summary>
    /// Update report schedule
    /// </summary>
    public void UpdateSchedule(string scheduleExpression)
    {
        SetSchedule(scheduleExpression);
    }

    /// <summary>
    /// Update report parameters
    /// </summary>
    public void UpdateParameters(Dictionary<string, object> parameters)
    {
        Parameters = parameters ?? throw new ArgumentNullException(nameof(parameters));
        SetUpdatedAt();
    }

    /// <summary>
    /// Update report configuration
    /// </summary>
    public void UpdateConfiguration(Dictionary<string, object> configuration)
    {
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        SetUpdatedAt();
    }

    /// <summary>
    /// Update report status
    /// </summary>
    public void UpdateStatus(ReportStatus status)
    {
        Status = status;
        SetUpdatedAt();
    }

    private readonly List<IDomainEvent> _domainEvents = new();
    public IReadOnlyCollection<IDomainEvent> DomainEvents => _domainEvents.AsReadOnly();

    protected void AddDomainEvent(IDomainEvent domainEvent)
    {
        _domainEvents.Add(domainEvent);
    }

    public void ClearDomainEvents()
    {
        _domainEvents.Clear();
    }
}

/// <summary>
/// Report section entity for organizing report content
/// </summary>
public class ReportSection : BaseEntity
{
    public Guid ReportId { get; private set; }
    public string Title { get; private set; }
    public string Description { get; private set; }
    public int Order { get; private set; }
    public string SectionType { get; private set; }
    public string Query { get; private set; } = string.Empty;
    public Dictionary<string, object> Data { get; private set; }
    public Dictionary<string, object> Configuration { get; private set; }
    public bool IsActive { get; private set; } = true;
    public Guid? UserId { get; private set; }

    // Alias properties for compatibility
    public string Name => Title;
    public string Type => SectionType; // Alias for SectionType
    public Dictionary<string, object> Content => Data; // Alias for Data

    private ReportSection()
    {
        Title = string.Empty;
        Description = string.Empty;
        SectionType = string.Empty;
        Data = new Dictionary<string, object>();
        Configuration = new Dictionary<string, object>();
    }

    public ReportSection(
        Guid reportId,
        string title,
        string description,
        int order,
        string sectionType,
        string query = "",
        Dictionary<string, object>? data = null,
        Dictionary<string, object>? configuration = null)
    {
        ReportId = reportId;
        Title = title ?? throw new ArgumentNullException(nameof(title));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        Order = order;
        SectionType = sectionType ?? throw new ArgumentNullException(nameof(sectionType));
        Query = query ?? string.Empty;
        Data = data ?? new Dictionary<string, object>();
        Configuration = configuration ?? new Dictionary<string, object>();
    }

    public static ReportSection CreateSummarySection(Guid reportId, string title, Dictionary<string, object> summaryData, int order = 1)
    {
        return new ReportSection(reportId, title, "Executive summary section", order, "Summary", "", summaryData);
    }

    public static ReportSection CreateChartSection(Guid reportId, string title, string chartType, Dictionary<string, object> chartData, int order)
    {
        var config = new Dictionary<string, object>
        {
            ["chartType"] = chartType
        };

        return new ReportSection(reportId, title, "Chart visualization section", order, "Chart", "", chartData, config);
    }

    public static ReportSection CreateTableSection(Guid reportId, string title, Dictionary<string, object> tableData, int order)
    {
        return new ReportSection(reportId, title, "Data table section", order, "Table", "", tableData);
    }

    public void UpdateData(Dictionary<string, object> data)
    {
        Data = data ?? throw new ArgumentNullException(nameof(data));
        SetUpdatedAt();
    }

    public void UpdateOrder(int order)
    {
        Order = order;
        SetUpdatedAt();
    }
}

/// <summary>
/// Report export entity for tracking exported reports
/// </summary>
public class ReportExport : BaseEntity
{
    public Guid ReportId { get; private set; }
    public ExportFormat Format { get; private set; }
    public Guid ExportedBy { get; private set; }
    public DateTime ExportedAt { get; private set; }
    public string FileName { get; private set; }
    public long FileSizeBytes { get; private set; }
    public byte[] Data { get; private set; }
    public string? ContentType { get; private set; }
    public DateTime? ExpiresAt { get; private set; }
    public int DownloadCount { get; private set; }
    public bool IsActive { get; private set; } = true;
    public ReportStatus Status { get; private set; } = ReportStatus.Generated;

    // Additional properties required by configurations and services
    public Guid? UserId => ExportedBy; // Alias for compatibility
    public DateTime RequestedAt { get; private set; }
    public Guid RequestedBy => ExportedBy; // Alias for ExportedBy
    public string? FilePath { get; private set; }
    public long? FileSize => FileSizeBytes; // Alias for FileSizeBytes
    public DateTime? CompletedAt => ExportedAt; // Alias for ExportedAt
    public string? ErrorMessage { get; private set; }
    public Dictionary<string, object> Parameters { get; private set; } = new();

    private ReportExport()
    {
        FileName = string.Empty;
        Data = Array.Empty<byte>();
    }

    public ReportExport(
        Guid reportId,
        ExportFormat format,
        Guid exportedBy,
        byte[] data,
        string fileName,
        string? contentType = null,
        DateTime? expiresAt = null)
    {
        ReportId = reportId;
        Format = format;
        ExportedBy = exportedBy;
        Data = data ?? throw new ArgumentNullException(nameof(data));
        FileName = fileName ?? throw new ArgumentNullException(nameof(fileName));
        FileSizeBytes = data.Length;
        ContentType = contentType ?? GetDefaultContentType(format);
        ExportedAt = DateTime.UtcNow;
        RequestedAt = DateTime.UtcNow;
        ExpiresAt = expiresAt ?? DateTime.UtcNow.AddDays(30); // Default 30 days expiration
        DownloadCount = 0;
    }

    private static string GetDefaultContentType(ExportFormat format)
    {
        return format switch
        {
            ExportFormat.PDF => "application/pdf",
            ExportFormat.Excel => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            ExportFormat.CSV => "text/csv",
            ExportFormat.JSON => "application/json",
            ExportFormat.XML => "application/xml",
            _ => "application/octet-stream"
        };
    }

    public void RecordDownload()
    {
        DownloadCount++;
        SetUpdatedAt();
    }

    public void UpdateStatus(ReportStatus status)
    {
        Status = status;
        SetUpdatedAt();
    }

    public bool IsExpired => ExpiresAt.HasValue && ExpiresAt.Value < DateTime.UtcNow;
}
