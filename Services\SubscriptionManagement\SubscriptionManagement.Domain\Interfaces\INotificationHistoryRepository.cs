using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Domain.Interfaces
{
    public interface INotificationHistoryRepository
    {
        Task<NotificationHistory?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
        Task<NotificationHistory?> GetByExternalIdAsync(string externalNotificationId, CancellationToken cancellationToken = default);
        Task<List<NotificationHistory>> GetBySubscriptionIdAsync(Guid subscriptionId, CancellationToken cancellationToken = default);
        Task<List<NotificationHistory>> GetByUserIdAsync(Guid userId, CancellationToken cancellationToken = default);
        Task<List<NotificationHistory>> GetByStatusAsync(string status, CancellationToken cancellationToken = default);
        Task<List<NotificationHistory>> GetByChannelAsync(string channel, CancellationToken cancellationToken = default);
        Task<List<NotificationHistory>> GetByTypeAsync(NotificationType type, CancellationToken cancellationToken = default);
        
        Task<List<NotificationHistory>> GetPendingNotificationsAsync(CancellationToken cancellationToken = default);
        Task<List<NotificationHistory>> GetScheduledNotificationsAsync(DateTime? upToDate = null, CancellationToken cancellationToken = default);
        Task<List<NotificationHistory>> GetFailedNotificationsForRetryAsync(CancellationToken cancellationToken = default);
        
        Task<(List<NotificationHistory> Items, int TotalCount)> GetPagedAsync(
            int pageNumber,
            int pageSize,
            Guid? subscriptionId = null,
            Guid? userId = null,
            NotificationType? type = null,
            string? channel = null,
            string? status = null,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            Guid? triggeredByUserId = null,
            CancellationToken cancellationToken = default);

        Task<NotificationHistory> AddAsync(NotificationHistory notification, CancellationToken cancellationToken = default);
        Task UpdateAsync(NotificationHistory notification, CancellationToken cancellationToken = default);
        Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);
        
        Task<bool> ExistsAsync(Guid id, CancellationToken cancellationToken = default);
        Task<int> GetCountAsync(CancellationToken cancellationToken = default);
        Task<int> GetCountByStatusAsync(string status, CancellationToken cancellationToken = default);
        Task<int> GetCountByChannelAsync(string channel, CancellationToken cancellationToken = default);
        Task<int> GetCountByTypeAsync(NotificationType type, CancellationToken cancellationToken = default);
        
        // Analytics methods
        Task<Dictionary<string, int>> GetNotificationCountsByStatusAsync(
            DateTime? fromDate = null, 
            DateTime? toDate = null, 
            CancellationToken cancellationToken = default);
            
        Task<Dictionary<string, int>> GetNotificationCountsByChannelAsync(
            DateTime? fromDate = null, 
            DateTime? toDate = null, 
            CancellationToken cancellationToken = default);
            
        Task<Dictionary<NotificationType, int>> GetNotificationCountsByTypeAsync(
            DateTime? fromDate = null, 
            DateTime? toDate = null, 
            CancellationToken cancellationToken = default);
            
        Task<double> GetDeliveryRateAsync(
            string? channel = null, 
            DateTime? fromDate = null, 
            DateTime? toDate = null, 
            CancellationToken cancellationToken = default);
            
        Task<double> GetReadRateAsync(
            string? channel = null, 
            DateTime? fromDate = null, 
            DateTime? toDate = null, 
            CancellationToken cancellationToken = default);
            
        Task<double> GetClickRateAsync(
            string? channel = null, 
            DateTime? fromDate = null, 
            DateTime? toDate = null, 
            CancellationToken cancellationToken = default);
            
        Task<TimeSpan> GetAverageDeliveryTimeAsync(
            string? channel = null, 
            DateTime? fromDate = null, 
            DateTime? toDate = null, 
            CancellationToken cancellationToken = default);
    }
}
