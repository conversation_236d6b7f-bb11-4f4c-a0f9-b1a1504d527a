using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.EntityFrameworkCore;
using TripManagement.Infrastructure.Persistence;

namespace TripManagement.Infrastructure.HealthChecks;

public class DatabaseHealthCheck : IHealthCheck
{
    private readonly TripManagementDbContext _context;

    public DatabaseHealthCheck(TripManagementDbContext context)
    {
        _context = context;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            // Test database connectivity
            await _context.Database.CanConnectAsync(cancellationToken);
            
            // Test a simple query
            var tripCount = await _context.Trips.CountAsync(cancellationToken);
            
            var data = new Dictionary<string, object>
            {
                ["database"] = "Connected",
                ["tripCount"] = tripCount,
                ["connectionString"] = _context.Database.GetConnectionString()?.Substring(0, 50) + "..."
            };

            return HealthCheckResult.Healthy("Database is healthy", data);
        }
        catch (Exception ex)
        {
            var data = new Dictionary<string, object>
            {
                ["database"] = "Disconnected",
                ["error"] = ex.Message
            };

            return HealthCheckResult.Unhealthy("Database is unhealthy", ex, data);
        }
    }
}
