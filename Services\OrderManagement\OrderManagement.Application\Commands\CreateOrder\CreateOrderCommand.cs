using MediatR;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Application.Commands.CreateOrder;

public class CreateOrderCommand : IRequest<Guid>
{
    public Guid TransportCompanyId { get; set; }
    public Guid BrokerId { get; set; }
    public Guid? RfqId { get; set; }
    public Guid? AcceptedBidId { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public CreateOrderLoadDetailsDto LoadDetails { get; set; } = new();
    public CreateOrderRouteDetailsDto RouteDetails { get; set; } = new();
    public CreateOrderMoneyDto AgreedPrice { get; set; } = new();
    public CreateOrderCustomerDetailsDto CustomerDetails { get; set; } = new();
    public CreateOrderBillingDetailsDto BillingDetails { get; set; } = new();
    public string? SpecialInstructions { get; set; }
    public bool IsUrgent { get; set; }
}

public class CreateOrderLoadDetailsDto
{
    public string Description { get; set; } = string.Empty;
    public LoadType LoadType { get; set; }
    public CreateOrderWeightDto Weight { get; set; } = new();
    public CreateOrderVolumeDto? Volume { get; set; }
    public CreateOrderDimensionsDto? Dimensions { get; set; }
    public int Quantity { get; set; }
    public string? PackagingType { get; set; }
    public VehicleType RequiredVehicleType { get; set; }
    public bool RequiresSpecialHandling { get; set; }
    public string? SpecialHandlingInstructions { get; set; }
    public bool IsHazardous { get; set; }
    public string? HazardousClassification { get; set; }
    public bool RequiresTemperatureControl { get; set; }
    public CreateOrderTemperatureRangeDto? TemperatureRange { get; set; }
    public List<string> Photos { get; set; } = new();
}

public class CreateOrderRouteDetailsDto
{
    public CreateOrderAddressDto PickupAddress { get; set; } = new();
    public CreateOrderAddressDto DeliveryAddress { get; set; } = new();
    public DateTime PreferredPickupDate { get; set; }
    public DateTime PreferredDeliveryDate { get; set; }
    public CreateOrderTimeWindowDto? PickupTimeWindow { get; set; }
    public CreateOrderTimeWindowDto? DeliveryTimeWindow { get; set; }
    public CreateOrderDistanceDto? EstimatedDistance { get; set; }
    public TimeSpan? EstimatedDuration { get; set; }
    public List<CreateOrderAddressDto> IntermediateStops { get; set; } = new();
    public string? RouteNotes { get; set; }
    public bool IsFlexiblePickup { get; set; }
    public bool IsFlexibleDelivery { get; set; }
}

public class CreateOrderCustomerDetailsDto
{
    public string CompanyName { get; set; } = string.Empty;
    public string ContactPerson { get; set; } = string.Empty;
    public string ContactEmail { get; set; } = string.Empty;
    public string ContactPhone { get; set; } = string.Empty;
    public CreateOrderAddressDto BillingAddress { get; set; } = new();
    public string? TaxId { get; set; }
    public string? CustomerReference { get; set; }
    public string? PurchaseOrderNumber { get; set; }
}

public class CreateOrderBillingDetailsDto
{
    public string BillingMethod { get; set; } = string.Empty;
    public int PaymentTermsDays { get; set; }
    public string? PaymentInstructions { get; set; }
    public bool RequiresPurchaseOrder { get; set; }
    public string? PreferredInvoiceFormat { get; set; }
    public string? BillingContact { get; set; }
    public string? BillingEmail { get; set; }
}

public class CreateOrderAddressDto
{
    public string Street { get; set; } = string.Empty;
    public string City { get; set; } = string.Empty;
    public string State { get; set; } = string.Empty;
    public string PostalCode { get; set; } = string.Empty;
    public string Country { get; set; } = string.Empty;
    public decimal? Latitude { get; set; }
    public decimal? Longitude { get; set; }
    public string? ContactPerson { get; set; }
    public string? ContactPhone { get; set; }
    public string? SpecialInstructions { get; set; }
}

public class CreateOrderMoneyDto
{
    public decimal Amount { get; set; }
    public string Currency { get; set; } = string.Empty;
}

public class CreateOrderWeightDto
{
    public decimal Value { get; set; }
    public WeightUnit Unit { get; set; }
}

public class CreateOrderVolumeDto
{
    public decimal Value { get; set; }
    public VolumeUnit Unit { get; set; }
}

public class CreateOrderDimensionsDto
{
    public decimal Length { get; set; }
    public decimal Width { get; set; }
    public decimal Height { get; set; }
    public string Unit { get; set; } = string.Empty;
}

public class CreateOrderTemperatureRangeDto
{
    public decimal MinTemperature { get; set; }
    public decimal MaxTemperature { get; set; }
    public string Unit { get; set; } = string.Empty;
}

public class CreateOrderTimeWindowDto
{
    public TimeSpan StartTime { get; set; }
    public TimeSpan EndTime { get; set; }
}

public class CreateOrderDistanceDto
{
    public decimal Value { get; set; }
    public DistanceUnit Unit { get; set; }
}
