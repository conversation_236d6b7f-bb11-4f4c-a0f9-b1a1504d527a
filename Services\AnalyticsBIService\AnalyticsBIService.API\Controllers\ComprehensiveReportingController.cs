using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using MediatR;
using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Application.Commands.GenerateComprehensiveReport;
using AnalyticsBIService.Application.Commands.GenerateCrossServiceAnalytics;
using AnalyticsBIService.Application.Commands.CreateCustomReportTemplate;
using System.Security.Claims;

namespace AnalyticsBIService.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class ComprehensiveReportingController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<ComprehensiveReportingController> _logger;

    public ComprehensiveReportingController(
        IMediator mediator,
        ILogger<ComprehensiveReportingController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Generate comprehensive report with cross-service data aggregation
    /// </summary>
    [HttpPost("generate")]
    [ProducesResponseType(typeof(ComprehensiveReportDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<ComprehensiveReportDto>> GenerateComprehensiveReport(
        [FromBody] GenerateComprehensiveReportRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var userId = GetCurrentUserId();
            var command = new GenerateComprehensiveReportCommand
            {
                ReportType = request.ReportType,
                DateRange = request.DateRange,
                DataSources = request.DataSources,
                Metrics = request.Metrics,
                Parameters = request.Parameters,
                IncludeVisualizations = request.IncludeVisualizations,
                IncludeInsights = request.IncludeInsights,
                IncludePredictiveAnalytics = request.IncludePredictiveAnalytics,
                IncludeBenchmarking = request.IncludeBenchmarking,
                OutputFormat = request.OutputFormat,
                RequestedBy = userId,
                TemplateId = request.TemplateId,
                Priority = request.Priority,
                Recipients = request.Recipients,
                SaveAsTemplate = request.SaveAsTemplate,
                TemplateName = request.TemplateName
            };

            var result = await _mediator.Send(command, cancellationToken);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (UnauthorizedAccessException)
        {
            return Forbid("Access denied to generate reports");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating comprehensive report for user {UserId}", GetCurrentUserId());
            return StatusCode(500, "An error occurred while generating the report");
        }
    }

    /// <summary>
    /// Generate cross-service analytics with correlation analysis
    /// </summary>
    [HttpPost("cross-service-analytics")]
    [ProducesResponseType(typeof(CrossServiceAnalyticsDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<CrossServiceAnalyticsDto>> GenerateCrossServiceAnalytics(
        [FromBody] GenerateCrossServiceAnalyticsRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var userId = GetCurrentUserId();
            var command = new GenerateCrossServiceAnalyticsCommand
            {
                Services = request.Services,
                DateRange = request.DateRange,
                Metrics = request.Metrics,
                IncludeCorrelations = request.IncludeCorrelations,
                IncludeInsights = request.IncludeInsights,
                IncludePredictiveAnalytics = request.IncludePredictiveAnalytics,
                CorrelationThreshold = request.CorrelationThreshold,
                Depth = request.Depth,
                FocusAreas = request.FocusAreas,
                ServiceSpecificParameters = request.ServiceSpecificParameters,
                RequestedBy = userId,
                SaveResults = request.SaveResults,
                ResultsName = request.ResultsName
            };

            var result = await _mediator.Send(command, cancellationToken);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (UnauthorizedAccessException)
        {
            return Forbid("Access denied to generate cross-service analytics");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating cross-service analytics for user {UserId}", GetCurrentUserId());
            return StatusCode(500, "An error occurred while generating cross-service analytics");
        }
    }

    /// <summary>
    /// Create custom report template for reusable reporting
    /// </summary>
    [HttpPost("templates")]
    [ProducesResponseType(typeof(CustomReportTemplateDto), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<CustomReportTemplateDto>> CreateCustomReportTemplate(
        [FromBody] CreateCustomReportTemplateRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var userId = GetCurrentUserId();
            var command = new CreateCustomReportTemplateCommand
            {
                Name = request.Name,
                Description = request.Description,
                Category = request.Category,
                IsPublic = request.IsPublic,
                Tags = request.Tags,
                Configuration = request.Configuration,
                Parameters = request.Parameters,
                CreatedBy = userId,
                ValidateConfiguration = request.ValidateConfiguration,
                SaveAsDraft = request.SaveAsDraft,
                BasedOnTemplateId = request.BasedOnTemplateId,
                CustomSettings = request.CustomSettings
            };

            var result = await _mediator.Send(command, cancellationToken);
            return CreatedAtAction(nameof(GetReportTemplate), new { id = result.TemplateId }, result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return Conflict(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating custom report template for user {UserId}", GetCurrentUserId());
            return StatusCode(500, "An error occurred while creating the report template");
        }
    }

    /// <summary>
    /// Generate report from existing template
    /// </summary>
    [HttpPost("templates/{templateId}/generate")]
    [ProducesResponseType(typeof(ComprehensiveReportDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ComprehensiveReportDto>> GenerateReportFromTemplate(
        Guid templateId,
        [FromBody] GenerateFromTemplateRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var userId = GetCurrentUserId();
            var command = new GenerateReportFromTemplateCommand
            {
                TemplateId = templateId,
                ParameterValues = request.ParameterValues,
                OutputFormat = request.OutputFormat,
                IncludeVisualizations = request.IncludeVisualizations,
                IncludeInsights = request.IncludeInsights,
                RequestedBy = userId,
                SaveResults = request.SaveResults,
                ResultsName = request.ResultsName,
                Recipients = request.Recipients,
                ScheduleReport = request.ScheduleReport,
                ScheduleExpression = request.ScheduleExpression
            };

            var result = await _mediator.Send(command, cancellationToken);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating report from template {TemplateId} for user {UserId}", 
                templateId, GetCurrentUserId());
            return StatusCode(500, "An error occurred while generating the report");
        }
    }

    /// <summary>
    /// Get report template details (placeholder for future implementation)
    /// </summary>
    [HttpGet("templates/{id}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult> GetReportTemplate(Guid id)
    {
        // This would be implemented with a proper query handler
        return Ok(new { id, message = "Report template details endpoint - to be implemented" });
    }

    /// <summary>
    /// Export report in specified format
    /// </summary>
    [HttpPost("export")]
    [ProducesResponseType(typeof(ExportResultDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<ExportResultDto>> ExportReport(
        [FromBody] ExportReportRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // This would be implemented with a proper export service
            var result = new ExportResultDto
            {
                ExportId = Guid.NewGuid(),
                FileName = $"report_{DateTime.UtcNow:yyyyMMdd_HHmmss}.{request.Format.ToLower()}",
                Format = request.Format,
                FileSizeBytes = 1024000, // Placeholder
                DownloadUrl = $"/api/downloads/{Guid.NewGuid()}",
                ExportedAt = DateTime.UtcNow,
                ExpiresAt = DateTime.UtcNow.AddDays(7),
                Status = "Completed",
                Metadata = new ExportMetadataDto
                {
                    TotalRecords = 1000,
                    TotalPages = 10,
                    IncludesVisualizations = request.IncludeVisualizations,
                    IncludesRawData = request.IncludeRawData,
                    ProcessingTime = TimeSpan.FromSeconds(5)
                }
            };

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting report for user {UserId}", GetCurrentUserId());
            return StatusCode(500, "An error occurred while exporting the report");
        }
    }

    /// <summary>
    /// Get available data sources for reporting
    /// </summary>
    [HttpGet("data-sources")]
    [ProducesResponseType(typeof(List<DataSourceInfoDto>), StatusCodes.Status200OK)]
    public async Task<ActionResult<List<DataSourceInfoDto>>> GetAvailableDataSources()
    {
        try
        {
            var dataSources = new List<DataSourceInfoDto>
            {
                new() { Name = "UserManagement", DisplayName = "User Management", Description = "User accounts, roles, and permissions data" },
                new() { Name = "OrderManagement", DisplayName = "Order Management", Description = "Orders, RFQs, and quotes data" },
                new() { Name = "TripManagement", DisplayName = "Trip Management", Description = "Trips, routes, and delivery data" },
                new() { Name = "NetworkFleetManagement", DisplayName = "Network & Fleet", Description = "Carriers, drivers, and vehicles data" },
                new() { Name = "FinancialPayment", DisplayName = "Financial & Payment", Description = "Payments, settlements, and financial data" },
                new() { Name = "CommunicationNotification", DisplayName = "Communication", Description = "Messages, notifications, and communication data" },
                new() { Name = "AnalyticsBIService", DisplayName = "Analytics & BI", Description = "Analytics, reports, and business intelligence data" },
                new() { Name = "DataStorage", DisplayName = "Data & Storage", Description = "Documents, files, and storage data" },
                new() { Name = "MobileWorkflow", DisplayName = "Mobile & Workflow", Description = "Mobile app usage and workflow data" },
                new() { Name = "AuditCompliance", DisplayName = "Audit & Compliance", Description = "Audit trails and compliance data" }
            };

            return Ok(dataSources);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting available data sources");
            return StatusCode(500, "An error occurred while retrieving data sources");
        }
    }

    /// <summary>
    /// Get available metrics for a specific data source
    /// </summary>
    [HttpGet("data-sources/{dataSource}/metrics")]
    [ProducesResponseType(typeof(List<MetricInfoDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<List<MetricInfoDto>>> GetDataSourceMetrics(string dataSource)
    {
        try
        {
            // This would be implemented with a proper service to get actual metrics
            var metrics = new List<MetricInfoDto>
            {
                new() { Name = "TotalRecords", DisplayName = "Total Records", DataType = "int", Description = "Total number of records" },
                new() { Name = "ActiveRecords", DisplayName = "Active Records", DataType = "int", Description = "Number of active records" },
                new() { Name = "AverageValue", DisplayName = "Average Value", DataType = "decimal", Description = "Average value across records" },
                new() { Name = "SuccessRate", DisplayName = "Success Rate", DataType = "decimal", Description = "Success rate percentage" }
            };

            return Ok(metrics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting metrics for data source {DataSource}", dataSource);
            return StatusCode(500, "An error occurred while retrieving metrics");
        }
    }

    // Helper methods
    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst("sub") ?? User.FindFirst("userId");
        return userIdClaim != null ? Guid.Parse(userIdClaim.Value) : Guid.Empty;
    }

    private string GetCurrentUserRole()
    {
        var roleClaim = User.FindFirst("role") ?? User.FindFirst(ClaimTypes.Role);
        return roleClaim?.Value ?? "User";
    }
}

// ===== REQUEST DTOs =====

public class GenerateComprehensiveReportRequest
{
    public string ReportType { get; set; } = string.Empty;
    public DateRangeDto DateRange { get; set; } = new();
    public List<string> DataSources { get; set; } = new();
    public List<string> Metrics { get; set; } = new();
    public Dictionary<string, object> Parameters { get; set; } = new();
    public bool IncludeVisualizations { get; set; } = true;
    public bool IncludeInsights { get; set; } = true;
    public bool IncludePredictiveAnalytics { get; set; } = false;
    public bool IncludeBenchmarking { get; set; } = false;
    public string OutputFormat { get; set; } = "PDF";
    public string? TemplateId { get; set; }
    public ReportPriority Priority { get; set; } = ReportPriority.Normal;
    public List<string> Recipients { get; set; } = new();
    public bool SaveAsTemplate { get; set; } = false;
    public string? TemplateName { get; set; }
}

public class GenerateCrossServiceAnalyticsRequest
{
    public List<string> Services { get; set; } = new();
    public DateRangeDto DateRange { get; set; } = new();
    public List<string> Metrics { get; set; } = new();
    public bool IncludeCorrelations { get; set; } = true;
    public bool IncludeInsights { get; set; } = true;
    public bool IncludePredictiveAnalytics { get; set; } = false;
    public decimal CorrelationThreshold { get; set; } = 0.5m;
    public AnalyticsDepth Depth { get; set; } = AnalyticsDepth.Standard;
    public List<string> FocusAreas { get; set; } = new();
    public Dictionary<string, object> ServiceSpecificParameters { get; set; } = new();
    public bool SaveResults { get; set; } = true;
    public string? ResultsName { get; set; }
}

public class CreateCustomReportTemplateRequest
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public bool IsPublic { get; set; } = false;
    public List<string> Tags { get; set; } = new();
    public ReportConfigurationDto Configuration { get; set; } = new();
    public List<ReportParameterDto> Parameters { get; set; } = new();
    public bool ValidateConfiguration { get; set; } = true;
    public bool SaveAsDraft { get; set; } = false;
    public string? BasedOnTemplateId { get; set; }
    public Dictionary<string, object> CustomSettings { get; set; } = new();
}

public class GenerateFromTemplateRequest
{
    public Dictionary<string, object> ParameterValues { get; set; } = new();
    public string OutputFormat { get; set; } = "PDF";
    public bool IncludeVisualizations { get; set; } = true;
    public bool IncludeInsights { get; set; } = true;
    public bool SaveResults { get; set; } = true;
    public string? ResultsName { get; set; }
    public List<string> Recipients { get; set; } = new();
    public bool ScheduleReport { get; set; } = false;
    public string? ScheduleExpression { get; set; }
}

public class ExportReportRequest
{
    public Guid ReportId { get; set; }
    public string Format { get; set; } = "PDF";
    public bool IncludeVisualizations { get; set; } = true;
    public bool IncludeRawData { get; set; } = false;
    public bool IncludeInsights { get; set; } = true;
    public Dictionary<string, object> FormatOptions { get; set; } = new();
}

public class DataSourceInfoDto
{
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public bool IsAvailable { get; set; } = true;
    public DateTime? LastUpdated { get; set; }
    public List<string> SupportedMetrics { get; set; } = new();
}

public class MetricInfoDto
{
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string DataType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public bool IsAggregatable { get; set; } = true;
    public List<string> SupportedAggregations { get; set; } = new();
}
