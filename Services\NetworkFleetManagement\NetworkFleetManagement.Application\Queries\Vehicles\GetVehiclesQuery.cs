using MediatR;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Enums;

namespace NetworkFleetManagement.Application.Queries.Vehicles;

public record GetVehiclesQuery(
    int PageNumber = 1,
    int PageSize = 10,
    Guid? CarrierId = null,
    VehicleStatus? Status = null,
    VehicleType? VehicleType = null,
    string? SearchTerm = null) : IRequest<PagedResult<VehicleSummaryDto>>;
