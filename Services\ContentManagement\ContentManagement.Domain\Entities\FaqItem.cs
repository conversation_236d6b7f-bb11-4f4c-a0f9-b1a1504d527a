using ContentManagement.Domain.Enums;
using Shared.Domain.Common;
using ContentManagement.Domain.Events;
using ContentManagement.Domain.ValueObjects;

namespace ContentManagement.Domain.Entities;

/// <summary>
/// FAQ item entity with question/answer structure and role-based visibility
/// </summary>
public class FaqItem : Content
{
    public string Question { get; private set; }
    public string Answer { get; private set; }
    public FaqCategory Category { get; private set; }
    public List<string> RoleVisibility { get; private set; }
    public bool IsHighlighted { get; private set; }
    public int ViewCount { get; private set; }
    public decimal HelpfulnessRating { get; private set; }
    public int TotalRatings { get; private set; }

    private FaqItem()
    {
        Question = string.Empty;
        Answer = string.Empty;
        RoleVisibility = new List<string>();
    }

    public FaqItem(
        string question,
        string answer,
        FaqCategory category,
        Guid createdBy,
        string createdByName,
        List<string>? roleVisibility = null,
        bool isHighlighted = false,
        ContentVisibility? visibility = null,
        List<string>? tags = null,
        int sortOrder = 0)
        : base(
            title: question.Length > 100 ? question.Substring(0, 100) + "..." : question,
            slug: GenerateSlug(question),
            type: ContentType.Faq,
            contentBody: answer,
            createdBy: createdBy,
            createdByName: createdByName,
            description: $"FAQ: {question}",
            visibility: visibility,
            tags: tags,
            sortOrder: sortOrder)
    {
        if (string.IsNullOrWhiteSpace(question))
            throw new ArgumentException("Question cannot be empty", nameof(question));
        if (string.IsNullOrWhiteSpace(answer))
            throw new ArgumentException("Answer cannot be empty", nameof(answer));

        Question = question;
        Answer = answer;
        Category = category;
        RoleVisibility = roleVisibility ?? new List<string>();
        IsHighlighted = isHighlighted;
        ViewCount = 0;
        HelpfulnessRating = 0;
        TotalRatings = 0;

        // Add FAQ-specific domain event
        AddDomainEvent(new FaqItemCreatedEvent(this));
    }

    public void UpdateQuestionAndAnswer(
        string question,
        string answer,
        Guid updatedBy,
        string updatedByName,
        string? changeDescription = null)
    {
        if (string.IsNullOrWhiteSpace(question))
            throw new ArgumentException("Question cannot be empty", nameof(question));
        if (string.IsNullOrWhiteSpace(answer))
            throw new ArgumentException("Answer cannot be empty", nameof(answer));

        var oldQuestion = Question;
        var oldAnswer = Answer;

        Question = question;
        Answer = answer;

        // Update the base content
        var newTitle = question.Length > 100 ? question.Substring(0, 100) + "..." : question;
        UpdateContent(
            title: newTitle,
            description: $"FAQ: {question}",
            contentBody: answer,
            updatedBy: updatedBy,
            updatedByName: updatedByName,
            changeDescription: changeDescription);

        AddDomainEvent(new FaqItemUpdatedEvent(this, oldQuestion, oldAnswer, updatedBy, updatedByName));
    }

    public void UpdateCategory(FaqCategory category, Guid updatedBy, string updatedByName)
    {
        var oldCategory = Category;
        Category = category;

        AddDomainEvent(new FaqCategoryChangedEvent(this, oldCategory, category, updatedBy, updatedByName));
    }

    public void UpdateRoleVisibility(List<string> roleVisibility, Guid updatedBy, string updatedByName)
    {
        var oldRoleVisibility = new List<string>(RoleVisibility);
        RoleVisibility = roleVisibility ?? new List<string>();

        AddDomainEvent(new FaqRoleVisibilityChangedEvent(this, oldRoleVisibility, RoleVisibility, updatedBy, updatedByName));
    }

    public void SetHighlighted(bool isHighlighted, Guid updatedBy, string updatedByName)
    {
        var oldValue = IsHighlighted;
        IsHighlighted = isHighlighted;

        AddDomainEvent(new FaqHighlightChangedEvent(this, oldValue, isHighlighted, updatedBy, updatedByName));
    }

    public void IncrementViewCount()
    {
        ViewCount++;
        AddDomainEvent(new FaqViewedEvent(this));
    }

    public void AddHelpfulnessRating(int rating, Guid ratedBy, string ratedByName)
    {
        if (rating < 1 || rating > 5)
            throw new ArgumentException("Rating must be between 1 and 5", nameof(rating));

        // Calculate new average rating
        var totalScore = HelpfulnessRating * TotalRatings + rating;
        TotalRatings++;
        HelpfulnessRating = totalScore / TotalRatings;

        AddDomainEvent(new FaqRatedEvent(this, rating, ratedBy, ratedByName));
    }

    public bool IsVisibleToRole(string role)
    {
        return !RoleVisibility.Any() || RoleVisibility.Contains(role);
    }

    public bool IsVisibleToRoles(List<string> roles)
    {
        return !RoleVisibility.Any() || RoleVisibility.Any(rv => roles.Contains(rv));
    }

    private static string GenerateSlug(string question)
    {
        // Simple slug generation - remove special characters and replace spaces with hyphens
        var slug = question.ToLowerInvariant()
            .Replace(" ", "-")
            .Replace("?", "")
            .Replace("!", "")
            .Replace(".", "")
            .Replace(",", "")
            .Replace("'", "")
            .Replace("\"", "");

        // Limit length and ensure uniqueness will be handled at the service level
        return slug.Length > 50 ? slug.Substring(0, 50).TrimEnd('-') : slug;
    }
}


