using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using UserManagement.Domain.Entities;
using UserManagement.Domain.Repositories;
using UserManagement.Infrastructure.Data;

namespace UserManagement.Infrastructure.Repositories
{
    public class UserProfileRepository : IUserProfileRepository
    {
        private readonly UserManagementDbContext _context;

        public UserProfileRepository(UserManagementDbContext context)
        {
            _context = context;
        }

        public async Task<UserProfile> GetByIdAsync(Guid id)
        {
            return await _context.UserProfiles.FindAsync(id);
        }

        public async Task<UserProfile> GetByUserIdAsync(Guid userId)
        {
            return await _context.UserProfiles
                .FirstOrDefaultAsync(up => up.UserId == userId);
        }

        public async Task<IEnumerable<UserProfile>> GetByUserTypeAsync(UserType userType)
        {
            return await _context.UserProfiles
                .Where(up => up.UserType == userType)
                .ToListAsync();
        }

        public async Task<IEnumerable<UserProfile>> GetByStatusAsync(ProfileStatus status)
        {
            return await _context.UserProfiles
                .Where(up => up.Status == status)
                .ToListAsync();
        }

        public async Task<IEnumerable<UserProfile>> GetPendingApprovalAsync()
        {
            return await _context.UserProfiles
                .Where(up => up.Status == ProfileStatus.UnderReview)
                .OrderBy(up => up.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<UserProfile>> SearchAsync(string searchTerm, UserType? userType = null, ProfileStatus? status = null)
        {
            var query = _context.UserProfiles.AsQueryable();

            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                query = query.Where(up =>
                    up.FirstName.Contains(searchTerm) ||
                    up.LastName.Contains(searchTerm) ||
                    up.Email.Contains(searchTerm) ||
                    up.CompanyName.Contains(searchTerm));
            }

            if (userType.HasValue)
            {
                query = query.Where(up => up.UserType == userType.Value);
            }

            if (status.HasValue)
            {
                query = query.Where(up => up.Status == status.Value);
            }

            return await query.ToListAsync();
        }

        public async Task<bool> ExistsAsync(Guid userId)
        {
            return await _context.UserProfiles
                .AnyAsync(up => up.UserId == userId);
        }

        public async Task AddAsync(UserProfile userProfile)
        {
            await _context.UserProfiles.AddAsync(userProfile);
            await _context.SaveChangesAsync();
        }

        public async Task UpdateAsync(UserProfile userProfile)
        {
            _context.UserProfiles.Update(userProfile);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteAsync(Guid id)
        {
            var userProfile = await GetByIdAsync(id);
            if (userProfile != null)
            {
                _context.UserProfiles.Remove(userProfile);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<int> GetCountByStatusAsync(ProfileStatus status)
        {
            return await _context.UserProfiles
                .CountAsync(up => up.Status == status);
        }

        public async Task<int> GetCountByUserTypeAsync(UserType userType)
        {
            return await _context.UserProfiles
                .CountAsync(up => up.UserType == userType);
        }

        public async Task<int> GetTotalUsersCountAsync()
        {
            return await _context.UserProfiles.CountAsync();
        }

        public async Task<int> GetApprovedTodayCountAsync()
        {
            var today = DateTime.UtcNow.Date;
            var tomorrow = today.AddDays(1);

            return await _context.UserProfiles
                .CountAsync(up => up.Status == ProfileStatus.Approved &&
                                 up.ApprovedAt >= today &&
                                 up.ApprovedAt < tomorrow);
        }

        public async Task<int> GetRejectedTodayCountAsync()
        {
            var today = DateTime.UtcNow.Date;
            var tomorrow = today.AddDays(1);

            return await _context.UserProfiles
                .CountAsync(up => up.Status == ProfileStatus.Rejected &&
                                 up.UpdatedAt >= today &&
                                 up.UpdatedAt < tomorrow);
        }

        public async Task<IEnumerable<UserProfile>> GetUsersAdvancedAsync(Dictionary<string, object> filters, int pageNumber, int pageSize)
        {
            var query = _context.UserProfiles.AsQueryable();

            // Apply filters
            if (filters.ContainsKey("FromDate") && filters["FromDate"] is DateTime fromDate)
            {
                query = query.Where(u => u.CreatedAt >= fromDate);
            }

            if (filters.ContainsKey("ToDate") && filters["ToDate"] is DateTime toDate)
            {
                query = query.Where(u => u.CreatedAt <= toDate);
            }

            if (filters.ContainsKey("UserType") && filters["UserType"] is UserType userType)
            {
                query = query.Where(u => u.UserType == userType);
            }

            if (filters.ContainsKey("Status") && filters["Status"] is ProfileStatus status)
            {
                query = query.Where(u => u.Status == status);
            }

            if (filters.ContainsKey("Location") && filters["Location"] is string location && !string.IsNullOrWhiteSpace(location))
            {
                query = query.Where(u => u.City.Contains(location) || u.State.Contains(location) || u.Region.Contains(location));
            }

            if (filters.ContainsKey("Region") && filters["Region"] is string region && !string.IsNullOrWhiteSpace(region))
            {
                query = query.Where(u => u.Region == region);
            }

            // Apply pagination
            query = query.Skip((pageNumber - 1) * pageSize);

            if (pageSize != int.MaxValue)
            {
                query = query.Take(pageSize);
            }

            return await query.OrderBy(u => u.CreatedAt).ToListAsync();
        }

        public async Task<IEnumerable<UserProfile>> GetByTransportCompanyIdAsync(Guid transportCompanyId)
        {
            return await _context.UserProfiles
                .Where(up => up.Id == transportCompanyId && up.UserType == UserType.TransportCompany)
                .ToListAsync();
        }

        // Advanced search implementation
        public async Task<(IEnumerable<UserProfile> Items, int TotalCount)> GetUsersAdvancedAsync(
            int pageNumber, int pageSize, string searchTerm = null, string email = null,
            string phoneNumber = null, string companyName = null, string userName = null,
            string subscriptionPlan = null, DateTime? planExpiryFrom = null, DateTime? planExpiryTo = null,
            DateTime? registrationDateFrom = null, DateTime? registrationDateTo = null,
            ProfileStatus? status = null, UserType? userType = null, bool? isActive = null,
            string kycStatus = null, bool? kycCompleted = null, DateTime? lastLoginFrom = null,
            DateTime? lastLoginTo = null, int? minRfqVolume = null, int? maxRfqVolume = null,
            string sortBy = "CreatedAt", string sortDirection = "desc",
            bool? hasSubscription = null, bool? autoRenewalEnabled = null,
            string region = null, string state = null, string city = null)
        {
            var query = _context.UserProfiles.AsQueryable();

            // Apply filters
            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                query = query.Where(up =>
                    up.FirstName.Contains(searchTerm) ||
                    up.LastName.Contains(searchTerm) ||
                    up.Email.Contains(searchTerm) ||
                    up.CompanyName.Contains(searchTerm));
            }

            if (!string.IsNullOrWhiteSpace(email))
            {
                query = query.Where(up => up.Email.Contains(email));
            }

            if (!string.IsNullOrWhiteSpace(phoneNumber))
            {
                query = query.Where(up => up.PhoneNumber.Contains(phoneNumber));
            }

            if (!string.IsNullOrWhiteSpace(companyName))
            {
                query = query.Where(up => up.CompanyName.Contains(companyName));
            }

            if (!string.IsNullOrWhiteSpace(userName))
            {
                query = query.Where(up => (up.FirstName + " " + up.LastName).Contains(userName));
            }

            if (registrationDateFrom.HasValue)
            {
                query = query.Where(up => up.CreatedAt >= registrationDateFrom.Value);
            }

            if (registrationDateTo.HasValue)
            {
                query = query.Where(up => up.CreatedAt <= registrationDateTo.Value);
            }

            if (status.HasValue)
            {
                query = query.Where(up => up.Status == status.Value);
            }

            if (userType.HasValue)
            {
                query = query.Where(up => up.UserType == userType.Value);
            }

            if (isActive.HasValue)
            {
                query = query.Where(up => up.IsActive == isActive.Value);
            }

            // Apply sorting
            query = sortBy?.ToLowerInvariant() switch
            {
                "firstname" => sortDirection?.ToLowerInvariant() == "desc"
                    ? query.OrderByDescending(up => up.FirstName)
                    : query.OrderBy(up => up.FirstName),
                "lastname" => sortDirection?.ToLowerInvariant() == "desc"
                    ? query.OrderByDescending(up => up.LastName)
                    : query.OrderBy(up => up.LastName),
                "email" => sortDirection?.ToLowerInvariant() == "desc"
                    ? query.OrderByDescending(up => up.Email)
                    : query.OrderBy(up => up.Email),
                "status" => sortDirection?.ToLowerInvariant() == "desc"
                    ? query.OrderByDescending(up => up.Status)
                    : query.OrderBy(up => up.Status),
                "usertype" => sortDirection?.ToLowerInvariant() == "desc"
                    ? query.OrderByDescending(up => up.UserType)
                    : query.OrderBy(up => up.UserType),
                _ => sortDirection?.ToLowerInvariant() == "desc"
                    ? query.OrderByDescending(up => up.CreatedAt)
                    : query.OrderBy(up => up.CreatedAt)
            };

            var totalCount = await query.CountAsync();
            var items = await query
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return (items, totalCount);
        }

        public async Task<UserStatisticsData> GetUserStatisticsAsync()
        {
            return new UserStatisticsData();
        }

        public async Task<IEnumerable<UserProfile>> GetUsersWithExpiringSubscriptionsAsync(int days = 30)
        {
            return await _context.UserProfiles.Take(0).ToListAsync();
        }

        public async Task<IEnumerable<UserProfile>> GetInactiveUsersAsync(int days = 90)
        {
            return await _context.UserProfiles.Take(0).ToListAsync();
        }

        public async Task<IEnumerable<UserProfile>> GetUsersByRegionAsync(string region)
        {
            return await _context.UserProfiles.Where(up => up.Region == region).ToListAsync();
        }

        public async Task<IEnumerable<UserProfile>> GetUsersRequiringKycAsync()
        {
            return await _context.UserProfiles.Take(0).ToListAsync();
        }

        public async Task<Dictionary<string, int>> GetUserCountByRegionAsync()
        {
            return new Dictionary<string, int>();
        }

        public async Task<Dictionary<UserType, int>> GetUserCountByTypeAsync()
        {
            return new Dictionary<UserType, int>();
        }

        public async Task<Dictionary<ProfileStatus, int>> GetUserCountByStatusAsync()
        {
            return new Dictionary<ProfileStatus, int>();
        }
    }
}
