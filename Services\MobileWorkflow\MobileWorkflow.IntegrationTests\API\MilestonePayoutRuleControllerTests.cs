using FluentAssertions;
using Microsoft.AspNetCore.Mvc.Testing;
using MobileWorkflow.Application.DTOs;
using MobileWorkflow.IntegrationTests.Infrastructure;
using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using Xunit;

namespace MobileWorkflow.IntegrationTests.API;

public class MilestonePayoutRuleControllerTests : IClassFixture<TestWebApplicationFactory<Program>>, IAsyncLifetime
{
    private readonly TestWebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;
    private readonly JsonSerializerOptions _jsonOptions;
    private Guid _stepId;
    private Guid _templateId;

    public MilestonePayoutRuleControllerTests(TestWebApplicationFactory<Program> factory)
    {
        _factory = factory;
        _client = _factory.CreateClient();
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
    }

    public async Task InitializeAsync()
    {
        // Create test data
        var ids = await _factory.ExecuteDbContextAsync(async context =>
        {
            // Clear existing data
            context.MilestonePayoutRules.RemoveRange(context.MilestonePayoutRules);
            context.MilestoneSteps.RemoveRange(context.MilestoneSteps);
            context.MilestoneTemplates.RemoveRange(context.MilestoneTemplates);
            await context.SaveChangesAsync();

            // Create test template and step
            var template = new Domain.Entities.MilestoneTemplate(
                "Test Template for Payout Rules",
                "Template for testing payout rules",
                "Trip",
                "Testing",
                "<EMAIL>");

            var step = template.AddStep("Test Step", "A test step for payout rules", 1, true);
            
            // Add some payout rules
            step.AddPayoutRule(60.0m, "status=completed", "Main payment");
            step.AddPayoutRule(40.0m, "quality_check=passed", "Quality bonus");

            context.MilestoneTemplates.Add(template);
            await context.SaveChangesAsync();

            return new { TemplateId = template.Id, StepId = step.Id };
        });

        _templateId = ids.TemplateId;
        _stepId = ids.StepId;
    }

    public Task DisposeAsync() => Task.CompletedTask;

    [Fact]
    public async Task GetMilestonePayoutRules_WithValidStepId_ShouldReturnRules()
    {
        // Act
        var response = await _client.GetAsync($"/api/milestone-steps/{_stepId}/payout-rules");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var rules = JsonSerializer.Deserialize<List<MilestonePayoutRuleDto>>(content, _jsonOptions);
        
        rules.Should().NotBeNull();
        rules.Should().HaveCount(2);
        rules.Should().Contain(r => r.Description == "Main payment");
        rules.Should().Contain(r => r.Description == "Quality bonus");
    }

    [Fact]
    public async Task GetMilestonePayoutRule_WithValidId_ShouldReturnRule()
    {
        // Arrange - Get existing rule ID
        var ruleId = await _factory.ExecuteDbContextAsync(async context =>
        {
            var step = await context.MilestoneSteps.FindAsync(_stepId);
            return step!.PayoutRules.First().Id;
        });

        // Act
        var response = await _client.GetAsync($"/api/milestone-steps/{_stepId}/payout-rules/{ruleId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var rule = JsonSerializer.Deserialize<MilestonePayoutRuleDto>(content, _jsonOptions);
        
        rule.Should().NotBeNull();
        rule.Id.Should().Be(ruleId);
        rule.MilestoneStepId.Should().Be(_stepId);
    }

    [Fact]
    public async Task CreateMilestonePayoutRule_WithValidData_ShouldCreateSuccessfully()
    {
        // Arrange - First clear existing rules to avoid percentage conflicts
        await _factory.ExecuteDbContextAsync(async context =>
        {
            var step = await context.MilestoneSteps.FindAsync(_stepId);
            step!.PayoutRules.Clear();
            await context.SaveChangesAsync();
        });

        var request = new CreateMilestonePayoutRuleRequest
        {
            PayoutPercentage = 75.0m,
            TriggerCondition = "status=verified",
            Description = "Verification payment",
            Configuration = new Dictionary<string, object> { { "auto_trigger", true } },
            Metadata = new Dictionary<string, object> { { "rule_type", "verification" } }
        };

        // Act
        var response = await _client.PostAsJsonAsync($"/api/milestone-steps/{_stepId}/payout-rules", request, _jsonOptions);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Created);
        
        var content = await response.Content.ReadAsStringAsync();
        var rule = JsonSerializer.Deserialize<MilestonePayoutRuleDto>(content, _jsonOptions);
        
        rule.Should().NotBeNull();
        rule.PayoutPercentage.Should().Be(request.PayoutPercentage);
        rule.TriggerCondition.Should().Be(request.TriggerCondition);
        rule.Description.Should().Be(request.Description);
        rule.MilestoneStepId.Should().Be(_stepId);

        // Verify in database
        var dbRule = await _factory.ExecuteDbContextAsync(async context =>
        {
            return context.MilestonePayoutRules
                .FirstOrDefault(r => r.Description == request.Description);
        });

        dbRule.Should().NotBeNull();
        dbRule!.PayoutPercentage.Should().Be(request.PayoutPercentage);
        dbRule.TriggerCondition.Should().Be(request.TriggerCondition);
    }

    [Fact]
    public async Task UpdateMilestonePayoutRule_WithValidData_ShouldUpdateSuccessfully()
    {
        // Arrange - Get existing rule
        var ruleId = await _factory.ExecuteDbContextAsync(async context =>
        {
            var step = await context.MilestoneSteps.FindAsync(_stepId);
            return step!.PayoutRules.First().Id;
        });

        var request = new UpdateMilestonePayoutRuleRequest
        {
            PayoutPercentage = 80.0m,
            TriggerCondition = "status=updated",
            Description = "Updated payment description",
            Configuration = new Dictionary<string, object> { { "updated", true } },
            Metadata = new Dictionary<string, object> { { "version", "2.0" } }
        };

        // Act
        var response = await _client.PutAsJsonAsync($"/api/milestone-steps/{_stepId}/payout-rules/{ruleId}", request, _jsonOptions);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var rule = JsonSerializer.Deserialize<MilestonePayoutRuleDto>(content, _jsonOptions);
        
        rule.Should().NotBeNull();
        rule.PayoutPercentage.Should().Be(request.PayoutPercentage);
        rule.TriggerCondition.Should().Be(request.TriggerCondition);
        rule.Description.Should().Be(request.Description);

        // Verify in database
        var dbRule = await _factory.ExecuteDbContextAsync(async context =>
        {
            return context.MilestonePayoutRules.FirstOrDefault(r => r.Id == ruleId);
        });

        dbRule.Should().NotBeNull();
        dbRule!.PayoutPercentage.Should().Be(request.PayoutPercentage);
        dbRule.TriggerCondition.Should().Be(request.TriggerCondition);
        dbRule.Description.Should().Be(request.Description);
    }

    [Fact]
    public async Task DeleteMilestonePayoutRule_WithValidId_ShouldDeleteSuccessfully()
    {
        // Arrange - Create a rule to delete
        var ruleId = await _factory.ExecuteDbContextAsync(async context =>
        {
            var step = await context.MilestoneSteps.FindAsync(_stepId);
            var rule = step!.AddPayoutRule(25.0m, "status=temp", "Temporary rule");
            await context.SaveChangesAsync();
            return rule.Id;
        });

        // Act
        var response = await _client.DeleteAsync($"/api/milestone-steps/{_stepId}/payout-rules/{ruleId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NoContent);

        // Verify deletion in database
        var dbRule = await _factory.ExecuteDbContextAsync(async context =>
        {
            return context.MilestonePayoutRules.FirstOrDefault(r => r.Id == ruleId);
        });

        dbRule.Should().BeNull();
    }

    [Fact]
    public async Task GetTotalPayoutPercentage_WithValidTemplateId_ShouldReturnTotal()
    {
        // Act
        var response = await _client.GetAsync($"/api/milestone-templates/{_templateId}/total-payout");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var total = JsonSerializer.Deserialize<decimal>(content, _jsonOptions);
        
        total.Should().Be(100.0m); // 60% + 40% from initialization
    }

    [Fact]
    public async Task CreateMilestonePayoutRule_WithInvalidPercentage_ShouldReturnBadRequest()
    {
        // Arrange
        var request = new CreateMilestonePayoutRuleRequest
        {
            PayoutPercentage = 150.0m, // Invalid percentage > 100
            TriggerCondition = "status=invalid",
            Description = "Invalid rule"
        };

        // Act
        var response = await _client.PostAsJsonAsync($"/api/milestone-steps/{_stepId}/payout-rules", request, _jsonOptions);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task GetMilestonePayoutRule_WithInvalidId_ShouldReturnNotFound()
    {
        // Arrange
        var invalidRuleId = Guid.NewGuid();

        // Act
        var response = await _client.GetAsync($"/api/milestone-steps/{_stepId}/payout-rules/{invalidRuleId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task CreateMilestonePayoutRule_WithInvalidStepId_ShouldReturnBadRequest()
    {
        // Arrange
        var invalidStepId = Guid.NewGuid();
        var request = new CreateMilestonePayoutRuleRequest
        {
            PayoutPercentage = 50.0m,
            Description = "Test rule"
        };

        // Act
        var response = await _client.PostAsJsonAsync($"/api/milestone-steps/{invalidStepId}/payout-rules", request, _jsonOptions);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task GetMilestonePayoutRules_WithInvalidStepId_ShouldReturnEmptyList()
    {
        // Arrange
        var invalidStepId = Guid.NewGuid();

        // Act
        var response = await _client.GetAsync($"/api/milestone-steps/{invalidStepId}/payout-rules");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var rules = JsonSerializer.Deserialize<List<MilestonePayoutRuleDto>>(content, _jsonOptions);
        
        rules.Should().NotBeNull();
        rules.Should().BeEmpty();
    }
}
