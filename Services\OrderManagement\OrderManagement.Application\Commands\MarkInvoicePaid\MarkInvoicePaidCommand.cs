using MediatR;

namespace OrderManagement.Application.Commands.MarkInvoicePaid;

public class MarkInvoicePaidCommand : IRequest<bool>
{
    public Guid InvoiceId { get; set; }
    public string? PaymentReference { get; set; }
    public DateTime? PaidAt { get; set; }
    public decimal? PaidAmount { get; set; } // For partial payments
    public string? PaymentNotes { get; set; }
    public Guid ProcessedBy { get; set; } // For authorization and audit
}
