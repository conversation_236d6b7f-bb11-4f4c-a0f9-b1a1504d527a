using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Domain.Interfaces
{
    public interface ITaxCategoryRepository
    {
        Task<TaxCategory?> GetByIdAsync(Guid id);
        Task<TaxCategory?> GetByCodeAsync(string code);
        Task<List<TaxCategory>> GetAllAsync();
        Task<List<TaxCategory>> GetActiveAsync();
        Task<List<TaxCategory>> GetByRegionAsync(string region);
        Task<TaxCategory> AddAsync(TaxCategory taxCategory);
        Task<TaxCategory> UpdateAsync(TaxCategory taxCategory);
        Task DeleteAsync(Guid id);
        Task<bool> ExistsAsync(Guid id);
        Task<bool> ExistsByCodeAsync(string code);
        Task<List<TaxCategory>> GetByTaxTypeAsync(TaxType taxType);
        Task<List<TaxCategory>> SearchAsync(string searchTerm, int page = 1, int pageSize = 10);
        Task<int> GetCountAsync();
    }
}
