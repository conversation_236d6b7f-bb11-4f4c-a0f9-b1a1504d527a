using Shared.Domain.Common;
using MonitoringObservability.Domain.Enums;
using MonitoringObservability.Domain.ValueObjects;
using MonitoringObservability.Domain.Events;

namespace MonitoringObservability.Domain.Entities;

public class Alert : AggregateRoot
{
    public string Title { get; private set; }
    public string Description { get; private set; }
    public AlertSeverity Severity { get; private set; }
    public AlertStatus Status { get; private set; }
    public string Source { get; private set; }
    public string ServiceName { get; private set; }
    public string MetricName { get; private set; }
    public double CurrentValue { get; private set; }
    public double ThresholdValue { get; private set; }
    public AlertThreshold Threshold { get; private set; }
    
    // Timestamps
    public DateTime TriggeredAt { get; private set; }
    public DateTime? AcknowledgedAt { get; private set; }
    public DateTime? ResolvedAt { get; private set; }
    public DateTime? ClosedAt { get; private set; }
    
    // Assignment and handling
    public Guid? AssignedToUserId { get; private set; }
    public string? AssignedToUserName { get; private set; }
    public Guid? AcknowledgedByUserId { get; private set; }
    public Guid? ResolvedByUserId { get; private set; }
    
    // Additional context
    public Dictionary<string, string> Tags { get; private set; }
    public string? ResolutionNotes { get; private set; }
    public int EscalationLevel { get; private set; }
    
    // Collections
    private readonly List<AlertNotification> _notifications = new();
    private readonly List<AlertComment> _comments = new();
    
    public IReadOnlyCollection<AlertNotification> Notifications => _notifications.AsReadOnly();
    public IReadOnlyCollection<AlertComment> Comments => _comments.AsReadOnly();

    private Alert()
    {
        Title = string.Empty;
        Description = string.Empty;
        Source = string.Empty;
        ServiceName = string.Empty;
        MetricName = string.Empty;
        Threshold = null!;
        Tags = new Dictionary<string, string>();
    }

    public Alert(
        string title,
        string description,
        AlertSeverity severity,
        string source,
        string serviceName,
        string metricName,
        double currentValue,
        AlertThreshold threshold,
        Dictionary<string, string>? tags = null)
    {
        if (string.IsNullOrWhiteSpace(title))
            throw new ArgumentException("Title cannot be empty", nameof(title));

        if (string.IsNullOrWhiteSpace(serviceName))
            throw new ArgumentException("Service name cannot be empty", nameof(serviceName));

        Id = Guid.NewGuid();
        Title = title;
        Description = description;
        Severity = severity;
        Status = AlertStatus.Open;
        Source = source;
        ServiceName = serviceName;
        MetricName = metricName;
        CurrentValue = currentValue;
        ThresholdValue = threshold.CriticalThreshold;
        Threshold = threshold;
        TriggeredAt = DateTime.UtcNow;
        Tags = tags ?? new Dictionary<string, string>();
        EscalationLevel = 0;

        // Raise domain event
        AddDomainEvent(new AlertTriggeredEvent(Id, Title, Severity, ServiceName, MetricName, CurrentValue));
    }

    public void Acknowledge(Guid userId, string? userName = null)
    {
        if (Status != AlertStatus.Open)
            throw new InvalidOperationException($"Cannot acknowledge alert in {Status} status");

        Status = AlertStatus.Acknowledged;
        AcknowledgedAt = DateTime.UtcNow;
        AcknowledgedByUserId = userId;

        AddDomainEvent(new AlertAcknowledgedEvent(Id, userId, userName));
    }

    public void Assign(Guid userId, string userName)
    {
        if (Status == AlertStatus.Closed || Status == AlertStatus.Resolved)
            throw new InvalidOperationException($"Cannot assign alert in {Status} status");

        AssignedToUserId = userId;
        AssignedToUserName = userName;

        if (Status == AlertStatus.Open)
        {
            Status = AlertStatus.Acknowledged;
            AcknowledgedAt = DateTime.UtcNow;
            AcknowledgedByUserId = userId;
        }

        AddDomainEvent(new AlertAssignedEvent(Id, userId, userName));
    }

    public void StartInvestigation(Guid userId)
    {
        if (Status != AlertStatus.Acknowledged)
            throw new InvalidOperationException($"Cannot start investigation for alert in {Status} status");

        Status = AlertStatus.InProgress;

        AddDomainEvent(new AlertInvestigationStartedEvent(Id, userId));
    }

    public void Resolve(Guid userId, string? resolutionNotes = null)
    {
        if (Status == AlertStatus.Closed || Status == AlertStatus.Resolved)
            throw new InvalidOperationException($"Alert is already {Status}");

        Status = AlertStatus.Resolved;
        ResolvedAt = DateTime.UtcNow;
        ResolvedByUserId = userId;
        ResolutionNotes = resolutionNotes;

        AddDomainEvent(new AlertResolvedEvent(Id, userId, resolutionNotes));
    }

    public void Close(Guid userId, string? closureNotes = null)
    {
        if (Status == AlertStatus.Closed)
            throw new InvalidOperationException("Alert is already closed");

        Status = AlertStatus.Closed;
        ClosedAt = DateTime.UtcNow;

        if (!string.IsNullOrWhiteSpace(closureNotes))
        {
            ResolutionNotes = string.IsNullOrWhiteSpace(ResolutionNotes) 
                ? closureNotes 
                : $"{ResolutionNotes}\n\nClosure Notes: {closureNotes}";
        }

        AddDomainEvent(new AlertClosedEvent(Id, userId));
    }

    public void Suppress(TimeSpan duration, Guid userId, string reason)
    {
        Status = AlertStatus.Suppressed;
        
        var suppressionTag = $"suppressed_until:{DateTime.UtcNow.Add(duration):O}";
        Tags["suppression_reason"] = reason;
        Tags["suppressed_by"] = userId.ToString();
        Tags["suppressed_until"] = DateTime.UtcNow.Add(duration).ToString("O");

        AddDomainEvent(new AlertSuppressedEvent(Id, duration, userId, reason));
    }

    public void Escalate(int newLevel, string reason)
    {
        EscalationLevel = newLevel;
        
        // Increase severity if escalating
        if (newLevel > 0 && Severity != AlertSeverity.Critical)
        {
            Severity = newLevel switch
            {
                1 => AlertSeverity.Warning,
                2 => AlertSeverity.Error,
                _ => AlertSeverity.Critical
            };
        }

        AddDomainEvent(new AlertEscalatedEvent(Id, newLevel, reason));
    }

    public void AddComment(Guid userId, string userName, string comment)
    {
        if (string.IsNullOrWhiteSpace(comment))
            throw new ArgumentException("Comment cannot be empty", nameof(comment));

        var alertComment = new AlertComment(Id, userId, userName, comment);
        _comments.Add(alertComment);

        AddDomainEvent(new AlertCommentAddedEvent(Id, userId, userName, comment));
    }

    public void AddNotification(NotificationChannel channel, string recipient, bool sent = false)
    {
        var notification = new AlertNotification(Id, channel, recipient, sent);
        _notifications.Add(notification);

        if (sent)
        {
            AddDomainEvent(new AlertNotificationSentEvent(Id, channel, recipient));
        }
    }

    public void UpdateCurrentValue(double newValue)
    {
        CurrentValue = newValue;
        
        // Re-evaluate severity based on new value
        var newSeverity = Threshold.EvaluateThreshold(newValue);
        if (newSeverity != Severity)
        {
            var oldSeverity = Severity;
            Severity = newSeverity;
            AddDomainEvent(new AlertSeverityChangedEvent(Id, oldSeverity, newSeverity, newValue));
        }
    }

    public bool IsExpired(TimeSpan maxAge)
    {
        return DateTime.UtcNow - TriggeredAt > maxAge;
    }

    public bool IsSuppressed()
    {
        if (Status != AlertStatus.Suppressed)
            return false;

        if (Tags.TryGetValue("suppressed_until", out var suppressedUntilStr) &&
            DateTime.TryParse(suppressedUntilStr, out var suppressedUntil))
        {
            return DateTime.UtcNow < suppressedUntil;
        }

        return false;
    }
}

public class AlertNotification : BaseEntity
{
    public Guid AlertId { get; private set; }
    public NotificationChannel Channel { get; private set; }
    public string Recipient { get; private set; }
    public bool Sent { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? SentAt { get; private set; }
    public string? ErrorMessage { get; private set; }

    private AlertNotification()
    {
        Recipient = string.Empty;
    }

    public AlertNotification(Guid alertId, NotificationChannel channel, string recipient, bool sent = false)
    {
        Id = Guid.NewGuid();
        AlertId = alertId;
        Channel = channel;
        Recipient = recipient;
        Sent = sent;
        CreatedAt = DateTime.UtcNow;
        
        if (sent)
        {
            SentAt = DateTime.UtcNow;
        }
    }

    public void MarkAsSent()
    {
        Sent = true;
        SentAt = DateTime.UtcNow;
        ErrorMessage = null;
    }

    public void MarkAsFailed(string errorMessage)
    {
        Sent = false;
        ErrorMessage = errorMessage;
    }
}

public class AlertComment : BaseEntity
{
    public Guid AlertId { get; private set; }
    public Guid UserId { get; private set; }
    public string UserName { get; private set; }
    public string Comment { get; private set; }
    public DateTime CreatedAt { get; private set; }

    private AlertComment()
    {
        UserName = string.Empty;
        Comment = string.Empty;
    }

    public AlertComment(Guid alertId, Guid userId, string userName, string comment)
    {
        Id = Guid.NewGuid();
        AlertId = alertId;
        UserId = userId;
        UserName = userName;
        Comment = comment;
        CreatedAt = DateTime.UtcNow;
    }
}
