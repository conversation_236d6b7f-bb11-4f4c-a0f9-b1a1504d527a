using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.DTOs;
using OrderManagement.Application.Interfaces;

namespace OrderManagement.Application.Queries.GetNegotiationDetails;

public class GetNegotiationDetailsQueryHandler : IRequestHandler<GetNegotiationDetailsQuery, RfqNegotiationDetailsDto?>
{
    private readonly IRfqRepository _rfqRepository;
    private readonly IBrokerCommentRepository _commentRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetNegotiationDetailsQueryHandler> _logger;

    public GetNegotiationDetailsQueryHandler(
        IRfqRepository rfqRepository,
        IBrokerCommentRepository commentRepository,
        IMapper mapper,
        ILogger<GetNegotiationDetailsQueryHandler> logger)
    {
        _rfqRepository = rfqRepository;
        _commentRepository = commentRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<RfqNegotiationDetailsDto?> Handle(GetNegotiationDetailsQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting negotiation details for RFQ {RfqId} by user {UserId}", 
            request.RfqId, request.RequestingUserId);

        try
        {
            // Verify RFQ exists and user has access
            var rfq = await _rfqRepository.GetByIdAsync(request.RfqId, cancellationToken);
            if (rfq == null)
            {
                _logger.LogWarning("RFQ {RfqId} not found", request.RfqId);
                return null;
            }

            // TODO: Add authorization logic here
            // Check if the requesting user has permission to view this RFQ negotiation details

            // Create negotiation details DTO
            var negotiationDetails = new RfqNegotiationDetailsDto
            {
                RfqId = request.RfqId,
                HasPriceExpectations = rfq.HasPriceExpectations(),
                PriceExpectations = _mapper.Map<PriceExpectationsDto>(rfq.PriceExpectations),
                HasReverseAuction = rfq.HasReverseAuction(),
                ReverseAuctionSettings = _mapper.Map<ReverseAuctionSettingsDto>(rfq.ReverseAuctionSettings),
                IsReverseAuctionActive = rfq.IsReverseAuctionActive()
            };

            // Get broker comments if requested
            if (request.IncludeBrokerComments)
            {
                var comments = await _commentRepository.GetByRfqIdAsync(
                    request.RfqId, 
                    true, // visible only
                    request.IncludeInternalComments, 
                    cancellationToken);

                negotiationDetails.BrokerComments = _mapper.Map<List<BrokerCommentDto>>(comments);
            }

            // Calculate negotiation metrics
            negotiationDetails.TotalBrokerComments = rfq.BrokerComments.Count(c => c.IsVisible);
            negotiationDetails.HasActiveNegotiation = rfq.HasBrokerComments() || rfq.HasReverseAuction();

            if (rfq.PriceExpectations != null)
            {
                negotiationDetails.IsPriceExpectationExpired = rfq.PriceExpectations.IsExpired();
            }

            if (rfq.ReverseAuctionSettings != null)
            {
                negotiationDetails.ReverseAuctionTimeRemaining = rfq.ReverseAuctionSettings.GetRemainingTime();
                negotiationDetails.CanExtendReverseAuction = rfq.ReverseAuctionSettings.CanExtend();
            }

            _logger.LogInformation("Retrieved negotiation details for RFQ {RfqId} with {CommentCount} comments",
                request.RfqId, negotiationDetails.TotalBrokerComments);

            return negotiationDetails;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving negotiation details for RFQ {RfqId}", request.RfqId);
            throw;
        }
    }
}
