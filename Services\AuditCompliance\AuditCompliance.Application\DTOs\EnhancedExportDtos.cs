using AuditCompliance.Domain.Enums;

namespace AuditCompliance.Application.DTOs
{
    /// <summary>
    /// Enhanced request DTO for exporting audit trail with comprehensive filtering
    /// </summary>
    public class EnhancedAuditExportRequestDto
    {
        public List<Guid> UserIds { get; set; } = new();
        public List<string> ActionTypes { get; set; } = new();
        public List<string> EntityTypes { get; set; } = new();
        public List<string> ModuleNames { get; set; } = new(); // New: Module filtering
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public List<string> SeverityLevels { get; set; } = new();
        public List<string> IpAddresses { get; set; } = new();
        public List<string> ComplianceFlags { get; set; } = new(); // New: Compliance filtering
        public bool IncludeSystemActions { get; set; } = true;
        public bool IncludeSensitiveData { get; set; } = false;
        public ExportFormat Format { get; set; } = ExportFormat.CSV;
        public List<string> SelectedColumns { get; set; } = new();
        public int? MaxRecords { get; set; }
        public int PageSize { get; set; } = 10000; // New: Pagination support
        public bool EnableBackgroundProcessing { get; set; } = false; // New: Background processing
        public bool RequireDigitalSignature { get; set; } = false; // New: Digital signature
        public string? NotificationEmail { get; set; } // New: Notification email
        public bool EnableCompression { get; set; } = false; // New: Compression option
        public string? ExportTitle { get; set; } // New: Custom export title
        public Dictionary<string, object> CustomFilters { get; set; } = new(); // New: Custom filters
        
        // Audit fields
        public Guid RequestedBy { get; set; }
        public string RequestedByRole { get; set; } = string.Empty;
        public string IpAddress { get; set; } = string.Empty;
        public string UserAgent { get; set; } = string.Empty;
    }

    /// <summary>
    /// Enhanced response DTO for audit export with comprehensive metadata
    /// </summary>
    public class EnhancedAuditExportResponseDto
    {
        public string ExportId { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public string? FileUrl { get; set; }
        public ExportFormat Format { get; set; }
        public int RecordCount { get; set; }
        public long FileSizeBytes { get; set; }
        public DateTime GeneratedAt { get; set; }
        public DateTime ExpiresAt { get; set; }
        public ExportStatus Status { get; set; }
        public string? StatusMessage { get; set; }
        public bool IsBackgroundProcessing { get; set; }
        public bool HasDigitalSignature { get; set; }
        public string? DigitalSignature { get; set; }
        public bool IsCompressed { get; set; }
        public ExportMetadataDto Metadata { get; set; } = new();
        public List<ExportValidationDto> ValidationResults { get; set; } = new();
        public Dictionary<string, string> Links { get; set; } = new();
    }

    /// <summary>
    /// Export metadata with detailed information
    /// </summary>
    public class ExportMetadataDto
    {
        public int TotalRecordsAvailable { get; set; }
        public int RecordsExported { get; set; }
        public int PagesProcessed { get; set; }
        public TimeSpan ProcessingTime { get; set; }
        public DateTime RequestedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        public string RequestedBy { get; set; } = string.Empty;
        public string RequestedByRole { get; set; } = string.Empty;
        public List<string> AppliedFilters { get; set; } = new();
        public List<string> IncludedColumns { get; set; } = new();
        public Dictionary<string, int> RecordsByModule { get; set; } = new();
        public Dictionary<string, int> RecordsBySeverity { get; set; } = new();
        public List<string> ComplianceFlags { get; set; } = new();
    }

    /// <summary>
    /// Export validation result
    /// </summary>
    public class ExportValidationDto
    {
        public string Type { get; set; } = string.Empty; // Warning, Error, Info
        public string Message { get; set; } = string.Empty;
        public string? Field { get; set; }
        public string? Code { get; set; }
    }

    /// <summary>
    /// Bulk export request for multiple audit trails
    /// </summary>
    public class BulkAuditExportRequestDto
    {
        public List<EnhancedAuditExportRequestDto> ExportRequests { get; set; } = new();
        public bool ProcessInParallel { get; set; } = false;
        public string? BatchName { get; set; }
        public bool CreateZipArchive { get; set; } = true;
        public string? NotificationEmail { get; set; }
        public Dictionary<string, object> GlobalSettings { get; set; } = new();
        
        // Audit fields
        public Guid RequestedBy { get; set; }
        public string RequestedByRole { get; set; } = string.Empty;
        public string IpAddress { get; set; } = string.Empty;
        public string UserAgent { get; set; } = string.Empty;
    }

    /// <summary>
    /// Bulk export response
    /// </summary>
    public class BulkAuditExportResponseDto
    {
        public string BatchId { get; set; } = string.Empty;
        public string BatchName { get; set; } = string.Empty;
        public int TotalExports { get; set; }
        public int CompletedExports { get; set; }
        public int FailedExports { get; set; }
        public ExportStatus Status { get; set; }
        public DateTime RequestedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        public string? ZipFileUrl { get; set; }
        public long TotalFileSizeBytes { get; set; }
        public List<EnhancedAuditExportResponseDto> ExportResults { get; set; } = new();
        public Dictionary<string, string> Links { get; set; } = new();
    }

    /// <summary>
    /// Export job status for tracking
    /// </summary>
    public class ExportJobStatusDto
    {
        public string JobId { get; set; } = string.Empty;
        public ExportStatus Status { get; set; }
        public string? StatusMessage { get; set; }
        public int ProgressPercentage { get; set; }
        public DateTime QueuedAt { get; set; }
        public DateTime? StartedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        public DateTime? EstimatedCompletionTime { get; set; }
        public int RecordsProcessed { get; set; }
        public int TotalRecords { get; set; }
        public string? ErrorMessage { get; set; }
        public Dictionary<string, object> AdditionalInfo { get; set; } = new();
    }

    /// <summary>
    /// Export format configuration
    /// </summary>
    public class ExportFormatConfigDto
    {
        public ExportFormat Format { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string MimeType { get; set; } = string.Empty;
        public string FileExtension { get; set; } = string.Empty;
        public int MaxRecords { get; set; }
        public bool SupportsCompression { get; set; }
        public bool SupportsDigitalSignature { get; set; }
        public List<string> SupportedColumns { get; set; } = new();
        public Dictionary<string, object> FormatSpecificOptions { get; set; } = new();
    }

    /// <summary>
    /// Export status enumeration
    /// </summary>
    public enum ExportStatus
    {
        Queued = 0,
        Processing = 1,
        Completed = 2,
        Failed = 3,
        Cancelled = 4,
        PartiallyCompleted = 5
    }

    /// <summary>
    /// Export format enumeration (enhanced)
    /// </summary>
    public enum ExportFormat
    {
        CSV = 0,
        Excel = 1,
        PDF = 2,
        JSON = 3,
        XML = 4,
        XLSX = 5,
        ZIP = 6
    }
}
