using System.Collections.Generic;
using MediatR;
using UserManagement.Application.Admin.Queries.GetPendingApprovals;

namespace UserManagement.Application.Admin.Queries.GetPendingApprovals
{
    public class GetPendingApprovalsQuery : IRequest<PendingApprovalsResponse>
    {
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 20;
    }

    public class PendingApprovalsResponse
    {
        public List<PendingUserProfileDto> PendingProfiles { get; set; } = new();
        public List<PendingDocumentSubmissionDto> PendingDocuments { get; set; } = new();
        public int TotalPendingProfiles { get; set; }
        public int TotalPendingDocuments { get; set; }
    }

    public class PendingUserProfileDto
    {
        public Guid Id { get; set; }
        public Guid UserId { get; set; }
        public string UserType { get; set; }
        public string DisplayName { get; set; }
        public string Email { get; set; }
        public string Status { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? SubmittedAt { get; set; }
    }

    public class PendingDocumentSubmissionDto
    {
        public Guid Id { get; set; }
        public Guid UserId { get; set; }
        public string UserType { get; set; }
        public string UserDisplayName { get; set; }
        public string Status { get; set; }
        public DateTime? SubmittedAt { get; set; }
        public int CompletionPercentage { get; set; }
        public List<DocumentDto> Documents { get; set; } = new();
    }

    public class DocumentDto
    {
        public string DocumentType { get; set; }
        public string Status { get; set; }
        public string FileName { get; set; }
        public DateTime? UploadedAt { get; set; }
        public string RejectionReason { get; set; }
    }
}
