namespace FinancialPayment.Domain.Exceptions;

public abstract class FinancialPaymentDomainException : Exception
{
    protected FinancialPaymentDomainException(string message) : base(message) { }
    protected FinancialPaymentDomainException(string message, Exception innerException) : base(message, innerException) { }
}

public class PaymentLinkNotFoundException : FinancialPaymentDomainException
{
    public PaymentLinkNotFoundException(Guid paymentLinkId)
        : base($"Payment link with ID '{paymentLinkId}' was not found.")
    {
    }
}

public class PaymentLinkValidationException : FinancialPaymentDomainException
{
    public PaymentLinkValidationException(string message) : base(message)
    {
    }
}

public class PaymentLinkInvalidStateException : FinancialPaymentDomainException
{
    public PaymentLinkInvalidStateException(Guid paymentLinkId, string currentState, string attemptedAction)
        : base($"Cannot perform action '{attemptedAction}' on payment link '{paymentLinkId}' in state '{currentState}'.")
    {
    }
}

public class CommissionNotFoundException : FinancialPaymentDomainException
{
    public CommissionNotFoundException(Guid commissionId)
        : base($"Commission with ID '{commissionId}' was not found.")
    {
    }
}

public class CommissionInvalidStateException : FinancialPaymentDomainException
{
    public CommissionInvalidStateException(Guid commissionId, string currentState, string attemptedAction)
        : base($"Cannot perform action '{attemptedAction}' on commission '{commissionId}' in state '{currentState}'.")
    {
    }
}

public class EscrowAccountNotFoundException : FinancialPaymentDomainException
{
    public EscrowAccountNotFoundException(Guid escrowAccountId)
        : base($"Escrow account with ID '{escrowAccountId}' was not found.")
    {
    }
}

public class InsufficientEscrowFundsException : FinancialPaymentDomainException
{
    public InsufficientEscrowFundsException(Guid escrowAccountId, decimal requestedAmount, decimal availableAmount)
        : base($"Insufficient funds in escrow account '{escrowAccountId}'. Requested: {requestedAmount}, Available: {availableAmount}")
    {
    }
}

public class PaymentMilestoneNotFoundException : FinancialPaymentDomainException
{
    public PaymentMilestoneNotFoundException(Guid milestoneId)
        : base($"Payment milestone with ID '{milestoneId}' was not found.")
    {
    }
}

public class TripLedgerNotFoundException : FinancialPaymentDomainException
{
    public TripLedgerNotFoundException(Guid tripLedgerId)
        : base($"Trip ledger with ID '{tripLedgerId}' was not found.")
    {
    }
}

public class InvalidTaxCalculationException : FinancialPaymentDomainException
{
    public InvalidTaxCalculationException(string reason)
        : base($"Invalid tax calculation: {reason}")
    {
    }
}

public class PaymentProcessingException : FinancialPaymentDomainException
{
    public PaymentProcessingException(string operation, string reason)
        : base($"Payment processing failed during '{operation}': {reason}")
    {
    }

    public PaymentProcessingException(string operation, Exception innerException)
        : base($"Payment processing failed during '{operation}'", innerException)
    {
    }
}

public class InvalidPaymentAmountException : FinancialPaymentDomainException
{
    public InvalidPaymentAmountException(string reason)
        : base($"Invalid payment amount: {reason}")
    {
    }
}

public class PaymentLinkExpiredException : FinancialPaymentDomainException
{
    public PaymentLinkExpiredException(Guid paymentLinkId, DateTime expiryDate)
        : base($"Payment link '{paymentLinkId}' expired on {expiryDate:yyyy-MM-dd HH:mm:ss}")
    {
    }
}

public class DuplicatePaymentException : FinancialPaymentDomainException
{
    public DuplicatePaymentException(string transactionId)
        : base($"Duplicate payment detected for transaction ID '{transactionId}'")
    {
    }
}

public class SettlementNotFoundException : FinancialPaymentDomainException
{
    public SettlementNotFoundException(Guid settlementId)
        : base($"Settlement with ID '{settlementId}' was not found.")
    {
    }
}

public class InvalidSettlementStateException : FinancialPaymentDomainException
{
    public InvalidSettlementStateException(Guid settlementId, string currentState, string attemptedAction)
        : base($"Cannot perform action '{attemptedAction}' on settlement '{settlementId}' in state '{currentState}'.")
    {
    }
}
