using Microsoft.Extensions.Logging;
using MonitoringObservability.Domain.Entities;
using MonitoringObservability.Domain.Enums;
using MonitoringObservability.Domain.Interfaces;
using System.Collections.Concurrent;

namespace MonitoringObservability.Infrastructure.Services;

/// <summary>
/// Implementation of distributed tracing service
/// </summary>
public class DistributedTracingService : IDistributedTracingService
{
    private readonly ITraceRepository _traceRepository;
    private readonly ISpanRepository _spanRepository;
    private readonly IServiceDependencyRepository _dependencyRepository;
    private readonly ILogger<DistributedTracingService> _logger;
    
    // In-memory cache for active traces and spans
    private readonly ConcurrentDictionary<string, Trace> _activeTraces = new();
    private readonly ConcurrentDictionary<string, Span> _activeSpans = new();

    public DistributedTracingService(
        ITraceRepository traceRepository,
        ISpanRepository spanRepository,
        IServiceDependencyRepository dependencyRepository,
        ILogger<DistributedTracingService> logger)
    {
        _traceRepository = traceRepository ?? throw new ArgumentNullException(nameof(traceRepository));
        _spanRepository = spanRepository ?? throw new ArgumentNullException(nameof(spanRepository));
        _dependencyRepository = dependencyRepository ?? throw new ArgumentNullException(nameof(dependencyRepository));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task StartTraceAsync(string traceId, string operationName, string serviceName, 
        Dictionary<string, string>? tags = null, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Starting trace {TraceId} for operation {OperationName} in service {ServiceName}", 
                traceId, operationName, serviceName);

            var trace = new Trace(traceId, operationName, serviceName, tags);
            
            // Add to active traces cache
            _activeTraces.TryAdd(traceId, trace);
            
            // Persist to repository
            await _traceRepository.AddAsync(trace, cancellationToken);
            
            _logger.LogInformation("Started trace {TraceId} for {ServiceName}.{OperationName}", 
                traceId, serviceName, operationName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start trace {TraceId}", traceId);
            throw;
        }
    }

    public async Task EndTraceAsync(string traceId, TraceStatus status, TimeSpan duration, 
        string? errorMessage = null, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Ending trace {TraceId} with status {Status}", traceId, status);

            // Get trace from cache or repository
            var trace = _activeTraces.GetValueOrDefault(traceId) ?? 
                       await _traceRepository.GetByTraceIdAsync(traceId, cancellationToken);

            if (trace == null)
            {
                _logger.LogWarning("Trace {TraceId} not found when trying to end it", traceId);
                return;
            }

            // End the trace
            trace.EndTrace(status, errorMessage);
            
            // Remove from active traces cache
            _activeTraces.TryRemove(traceId, out _);
            
            // Update in repository
            await _traceRepository.UpdateAsync(trace, cancellationToken);
            
            // Update service dependencies based on trace spans
            await UpdateServiceDependenciesAsync(trace, cancellationToken);
            
            _logger.LogInformation("Ended trace {TraceId} with status {Status}, duration {Duration}ms", 
                traceId, status, duration.TotalMilliseconds);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to end trace {TraceId}", traceId);
            throw;
        }
    }

    public async Task RecordSpanAsync(string traceId, string spanId, string operationName, string serviceName, 
        TimeSpan duration, Dictionary<string, string>? tags = null, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Recording span {SpanId} for trace {TraceId}", spanId, traceId);

            // Get or create trace
            var trace = _activeTraces.GetValueOrDefault(traceId) ?? 
                       await _traceRepository.GetByTraceIdAsync(traceId, cancellationToken);

            if (trace == null)
            {
                _logger.LogWarning("Trace {TraceId} not found when recording span {SpanId}", traceId, spanId);
                return;
            }

            // Create span
            var span = new Span(traceId, spanId, operationName, serviceName, tags: tags);
            span.EndSpan(); // Mark as completed with the provided duration
            
            // Add span to trace
            trace.AddSpan(span);
            
            // Add to active spans cache temporarily
            _activeSpans.TryAdd(spanId, span);
            
            // Persist span
            await _spanRepository.AddAsync(span, cancellationToken);
            
            // Update trace
            await _traceRepository.UpdateAsync(trace, cancellationToken);
            
            _logger.LogDebug("Recorded span {SpanId} for {ServiceName}.{OperationName}, duration {Duration}ms", 
                spanId, serviceName, operationName, duration.TotalMilliseconds);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to record span {SpanId} for trace {TraceId}", spanId, traceId);
            throw;
        }
    }

    public async Task<object?> GetTraceAsync(string traceId, CancellationToken cancellationToken = default)
    {
        try
        {
            var trace = await _traceRepository.GetByTraceIdAsync(traceId, cancellationToken);
            return trace;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get trace {TraceId}", traceId);
            throw;
        }
    }

    public async Task<IEnumerable<object>> SearchTracesAsync(string? serviceName = null, string? operationName = null, 
        DateTime? fromDate = null, DateTime? toDate = null, TimeSpan? minDuration = null, TraceStatus? status = null, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var traces = await _traceRepository.SearchTracesAsync(
                serviceName, operationName, fromDate, toDate, minDuration, null, status, 
                cancellationToken: cancellationToken);
            
            return traces.Cast<object>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to search traces");
            throw;
        }
    }

    public async Task<IEnumerable<object>> GetSlowTracesAsync(string serviceName, TimeSpan threshold, TimeSpan period, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var traces = await _traceRepository.GetSlowTracesAsync(serviceName, threshold, period, 
                cancellationToken: cancellationToken);
            
            return traces.Cast<object>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get slow traces for service {ServiceName}", serviceName);
            throw;
        }
    }

    public async Task<double> GetAverageTraceTimeAsync(string serviceName, string operationName, TimeSpan period, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            return await _traceRepository.GetAverageTraceTimeAsync(serviceName, operationName, period, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get average trace time for {ServiceName}.{OperationName}", 
                serviceName, operationName);
            throw;
        }
    }

    public async Task<IEnumerable<(string operationName, double averageTime)>> GetSlowestOperationsAsync(
        string serviceName, TimeSpan period, int topCount = 10, CancellationToken cancellationToken = default)
    {
        try
        {
            var operations = await _traceRepository.GetSlowestOperationsAsync(serviceName, period, topCount, cancellationToken);
            return operations.Select(op => (op.operationName, op.averageTime));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get slowest operations for service {ServiceName}", serviceName);
            throw;
        }
    }

    private async Task UpdateServiceDependenciesAsync(Trace trace, CancellationToken cancellationToken)
    {
        try
        {
            // Analyze spans to identify service dependencies
            var spans = trace.Spans.ToList();
            
            foreach (var span in spans)
            {
                // Look for child spans that represent calls to other services
                var childSpans = spans.Where(s => s.ParentSpanId == span.SpanId).ToList();
                
                foreach (var childSpan in childSpans)
                {
                    if (childSpan.ServiceName != span.ServiceName)
                    {
                        // This represents a service-to-service call
                        await UpdateServiceDependencyAsync(
                            span.ServiceName, 
                            childSpan.ServiceName, 
                            childSpan.OperationName,
                            childSpan.Duration ?? TimeSpan.Zero,
                            childSpan.HasError,
                            cancellationToken);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update service dependencies for trace {TraceId}", trace.TraceId);
            // Don't rethrow - this is a background operation
        }
    }

    private async Task UpdateServiceDependencyAsync(string fromService, string toService, string operationType,
        TimeSpan latency, bool hasError, CancellationToken cancellationToken)
    {
        try
        {
            var dependency = await _dependencyRepository.GetDependencyAsync(fromService, toService, operationType, cancellationToken);
            
            if (dependency == null)
            {
                dependency = new ServiceDependency(fromService, toService, operationType, 1, latency);
                await _dependencyRepository.AddAsync(dependency, cancellationToken);
            }
            else
            {
                dependency.UpdateStatistics(latency, hasError);
                await _dependencyRepository.UpdateAsync(dependency, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update service dependency {FromService} -> {ToService}", 
                fromService, toService);
        }
    }
}
