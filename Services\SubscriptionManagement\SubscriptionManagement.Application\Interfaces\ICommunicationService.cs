using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Application.Interfaces
{
    public interface ICommunicationService
    {
        /// <summary>
        /// Send notification through the Communication/Notification service
        /// </summary>
        Task<NotificationResult> SendNotificationAsync(
            CommunicationNotificationRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Send bulk notifications
        /// </summary>
        Task<BulkNotificationResult> SendBulkNotificationsAsync(
            List<CommunicationNotificationRequest> requests,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Schedule a notification for later delivery
        /// </summary>
        Task<NotificationResult> ScheduleNotificationAsync(
            CommunicationNotificationRequest request,
            DateTime scheduledAt,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get notification delivery status
        /// </summary>
        Task<NotificationStatus> GetNotificationStatusAsync(
            string notificationId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Cancel a scheduled notification
        /// </summary>
        Task<bool> CancelScheduledNotificationAsync(
            string notificationId,
            CancellationToken cancellationToken = default);
    }

    public class CommunicationNotificationRequest
    {
        public Guid? UserId { get; set; }
        public string? Email { get; set; }
        public string? PhoneNumber { get; set; }
        public string Channel { get; set; } = string.Empty; // Email, SMS, WhatsApp, InApp
        public string Subject { get; set; } = string.Empty;
        public string Body { get; set; } = string.Empty;
        public NotificationType Type { get; set; }
        public Dictionary<string, string> Metadata { get; set; } = new();
        public int Priority { get; set; } = 1; // 1 = High, 2 = Medium, 3 = Low
        public string? TemplateId { get; set; }
        public Dictionary<string, string> TemplateVariables { get; set; } = new();
    }

    public class NotificationResult
    {
        public bool Success { get; set; }
        public string NotificationId { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string Channel { get; set; } = string.Empty;
        public DateTime SentAt { get; set; }
        public string? ErrorCode { get; set; }
        public string? ErrorMessage { get; set; }
    }

    public class BulkNotificationResult
    {
        public bool Success { get; set; }
        public string BatchId { get; set; } = string.Empty;
        public int TotalRequests { get; set; }
        public int SuccessfulSends { get; set; }
        public int FailedSends { get; set; }
        public List<NotificationResult> Results { get; set; } = new();
        public DateTime ProcessedAt { get; set; }
        public TimeSpan ProcessingTime { get; set; }
    }

    public class NotificationStatus
    {
        public string NotificationId { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty; // Pending, Sent, Delivered, Failed, Cancelled
        public string Channel { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime? SentAt { get; set; }
        public DateTime? DeliveredAt { get; set; }
        public string? ErrorMessage { get; set; }
        public Dictionary<string, string> Metadata { get; set; } = new();
    }
}
