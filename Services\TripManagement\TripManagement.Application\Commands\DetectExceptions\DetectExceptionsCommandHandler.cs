using MediatR;
using Microsoft.Extensions.Logging;
using TripManagement.Application.Interfaces;
using TripManagement.Application.DTOs;
using TripManagement.Domain.Enums;

namespace TripManagement.Application.Commands.DetectExceptions;

public class DetectExceptionsCommandHandler : IRequestHandler<DetectExceptionsCommand, List<DetectedExceptionDto>>
{
    private readonly ITripRepository _tripRepository;
    private readonly ILogger<DetectExceptionsCommandHandler> _logger;
    private readonly IMessageBroker _messageBroker;

    public DetectExceptionsCommandHandler(
        ITripRepository tripRepository,
        ILogger<DetectExceptionsCommandHandler> logger,
        IMessageBroker messageBroker)
    {
        _tripRepository = tripRepository;
        _logger = logger;
        _messageBroker = messageBroker;
    }

    public async Task<List<DetectedExceptionDto>> Handle(DetectExceptionsCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Detecting exceptions for trip {TripId}", request.TripId?.ToString() ?? "all active trips");

            var detectedExceptions = new List<DetectedExceptionDto>();

            // Get trips to analyze
            var trips = request.TripId.HasValue 
                ? new List<Domain.Entities.Trip> { await _tripRepository.GetByIdAsync(request.TripId.Value, cancellationToken) }.Where(t => t != null).ToList()!
                : await _tripRepository.GetActiveTripsAsync(cancellationToken);

            foreach (var trip in trips)
            {
                var tripExceptions = await DetectTripExceptionsAsync(trip, request.Rules, cancellationToken);
                detectedExceptions.AddRange(tripExceptions);
            }

            // Publish detected exceptions
            if (detectedExceptions.Any())
            {
                await _messageBroker.PublishAsync("trip.exceptions_detected", new
                {
                    DetectedAt = DateTime.UtcNow,
                    ExceptionCount = detectedExceptions.Count,
                    Exceptions = detectedExceptions
                }, cancellationToken);
            }

            _logger.LogInformation("Exception detection completed. Found {ExceptionCount} exceptions", 
                detectedExceptions.Count);

            return detectedExceptions;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error detecting exceptions");
            throw;
        }
    }

    private async Task<List<DetectedExceptionDto>> DetectTripExceptionsAsync(
        Domain.Entities.Trip trip, 
        List<ExceptionDetectionRule> rules, 
        CancellationToken cancellationToken)
    {
        var detectedExceptions = new List<DetectedExceptionDto>();

        foreach (var rule in rules.Where(r => r.IsEnabled))
        {
            var exceptions = rule.Type switch
            {
                ExceptionDetectionType.DelayDetection => DetectDelays(trip, rule),
                ExceptionDetectionType.RouteDeviation => DetectRouteDeviations(trip, rule),
                ExceptionDetectionType.ExtendedStop => DetectExtendedStops(trip, rule),
                ExceptionDetectionType.UnexpectedStop => DetectUnexpectedStops(trip, rule),
                ExceptionDetectionType.CommunicationLoss => DetectCommunicationLoss(trip, rule),
                _ => new List<DetectedExceptionDto>()
            };

            detectedExceptions.AddRange(exceptions);
        }

        return detectedExceptions;
    }

    private List<DetectedExceptionDto> DetectDelays(Domain.Entities.Trip trip, ExceptionDetectionRule rule)
    {
        var exceptions = new List<DetectedExceptionDto>();
        var delayThresholdMinutes = rule.Parameters.GetValueOrDefault("delayThresholdMinutes", 30);

        // Check overall trip delay
        if (trip.IsDelayed())
        {
            var delay = DateTime.UtcNow - trip.EstimatedEndTime;
            if (delay.TotalMinutes > Convert.ToDouble(delayThresholdMinutes))
            {
                exceptions.Add(new DetectedExceptionDto
                {
                    TripId = trip.Id,
                    TripNumber = trip.TripNumber,
                    DetectionType = ExceptionDetectionType.DelayDetection,
                    Description = $"Trip is delayed by {delay.TotalMinutes:F0} minutes",
                    DetectedAt = DateTime.UtcNow,
                    Severity = delay.TotalHours > 2 ? ExceptionSeverity.High : ExceptionSeverity.Medium,
                    SuggestedAction = "Contact driver and update customer with new ETA",
                    Metadata = new Dictionary<string, object>
                    {
                        ["delayMinutes"] = delay.TotalMinutes,
                        ["originalETA"] = trip.EstimatedEndTime,
                        ["currentTime"] = DateTime.UtcNow
                    }
                });
            }
        }

        // Check individual stop delays
        foreach (var stop in trip.Stops.Where(s => s.IsDelayed()))
        {
            var stopDelay = stop.ActualArrival.HasValue 
                ? stop.ActualArrival.Value - stop.ScheduledArrival!.Value
                : DateTime.UtcNow - stop.ScheduledArrival!.Value;

            if (stopDelay.TotalMinutes > Convert.ToDouble(delayThresholdMinutes))
            {
                exceptions.Add(new DetectedExceptionDto
                {
                    TripId = trip.Id,
                    TripNumber = trip.TripNumber,
                    DetectionType = ExceptionDetectionType.DelayDetection,
                    Description = $"Stop {stop.SequenceNumber} is delayed by {stopDelay.TotalMinutes:F0} minutes",
                    Location = new LocationDto
                    {
                        Latitude = stop.Location.Latitude,
                        Longitude = stop.Location.Longitude,
                        Address = stop.Location.Address
                    },
                    DetectedAt = DateTime.UtcNow,
                    Severity = stopDelay.TotalHours > 1 ? ExceptionSeverity.High : ExceptionSeverity.Medium,
                    SuggestedAction = "Contact customer and reschedule if necessary",
                    Metadata = new Dictionary<string, object>
                    {
                        ["stopId"] = stop.Id,
                        ["delayMinutes"] = stopDelay.TotalMinutes,
                        ["scheduledArrival"] = stop.ScheduledArrival!.Value
                    }
                });
            }
        }

        return exceptions;
    }

    private List<DetectedExceptionDto> DetectRouteDeviations(Domain.Entities.Trip trip, ExceptionDetectionRule rule)
    {
        var exceptions = new List<DetectedExceptionDto>();
        var deviationThresholdKm = rule.Parameters.GetValueOrDefault("deviationThresholdKm", 5.0);

        var currentLocation = trip.GetCurrentLocation();
        if (currentLocation == null) return exceptions;

        // This would require more sophisticated route analysis
        // For now, we'll implement a basic check
        var expectedRoute = trip.Route;
        // Implementation would check if current location is significantly off the expected route

        return exceptions;
    }

    private List<DetectedExceptionDto> DetectExtendedStops(Domain.Entities.Trip trip, ExceptionDetectionRule rule)
    {
        var exceptions = new List<DetectedExceptionDto>();
        var maxStopDurationMinutes = rule.Parameters.GetValueOrDefault("maxStopDurationMinutes", 120);

        foreach (var stop in trip.Stops.Where(s => s.Status == TripStopStatus.InProgress))
        {
            if (stop.ActualArrival.HasValue)
            {
                var stopDuration = DateTime.UtcNow - stop.ActualArrival.Value;
                if (stopDuration.TotalMinutes > Convert.ToDouble(maxStopDurationMinutes))
                {
                    exceptions.Add(new DetectedExceptionDto
                    {
                        TripId = trip.Id,
                        TripNumber = trip.TripNumber,
                        DetectionType = ExceptionDetectionType.ExtendedStop,
                        Description = $"Driver has been at stop {stop.SequenceNumber} for {stopDuration.TotalMinutes:F0} minutes",
                        Location = new LocationDto
                        {
                            Latitude = stop.Location.Latitude,
                            Longitude = stop.Location.Longitude,
                            Address = stop.Location.Address
                        },
                        DetectedAt = DateTime.UtcNow,
                        Severity = stopDuration.TotalHours > 3 ? ExceptionSeverity.High : ExceptionSeverity.Medium,
                        SuggestedAction = "Contact driver to check status",
                        Metadata = new Dictionary<string, object>
                        {
                            ["stopId"] = stop.Id,
                            ["stopDurationMinutes"] = stopDuration.TotalMinutes,
                            ["arrivedAt"] = stop.ActualArrival.Value
                        }
                    });
                }
            }
        }

        return exceptions;
    }

    private List<DetectedExceptionDto> DetectUnexpectedStops(Domain.Entities.Trip trip, ExceptionDetectionRule rule)
    {
        var exceptions = new List<DetectedExceptionDto>();
        // Implementation would analyze location updates to detect unexpected stops
        return exceptions;
    }

    private List<DetectedExceptionDto> DetectCommunicationLoss(Domain.Entities.Trip trip, ExceptionDetectionRule rule)
    {
        var exceptions = new List<DetectedExceptionDto>();
        var maxSilenceMinutes = rule.Parameters.GetValueOrDefault("maxSilenceMinutes", 60);

        var lastLocationUpdate = trip.LocationUpdates.OrderByDescending(l => l.Timestamp).FirstOrDefault();
        if (lastLocationUpdate != null)
        {
            var silenceDuration = DateTime.UtcNow - lastLocationUpdate.Timestamp;
            if (silenceDuration.TotalMinutes > Convert.ToDouble(maxSilenceMinutes))
            {
                exceptions.Add(new DetectedExceptionDto
                {
                    TripId = trip.Id,
                    TripNumber = trip.TripNumber,
                    DetectionType = ExceptionDetectionType.CommunicationLoss,
                    Description = $"No location updates received for {silenceDuration.TotalMinutes:F0} minutes",
                    DetectedAt = DateTime.UtcNow,
                    Severity = silenceDuration.TotalHours > 2 ? ExceptionSeverity.High : ExceptionSeverity.Medium,
                    SuggestedAction = "Contact driver immediately",
                    Metadata = new Dictionary<string, object>
                    {
                        ["lastUpdateTime"] = lastLocationUpdate.Timestamp,
                        ["silenceMinutes"] = silenceDuration.TotalMinutes
                    }
                });
            }
        }

        return exceptions;
    }
}
