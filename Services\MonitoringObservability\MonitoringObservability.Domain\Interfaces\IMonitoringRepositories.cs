using MonitoringObservability.Domain.Entities;
using MonitoringObservability.Domain.Enums;

namespace MonitoringObservability.Domain.Interfaces;

public interface IAlertRepository
{
    // Basic CRUD operations
    Task<Alert?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<Alert?> GetByIdWithDetailsAsync(Guid id, CancellationToken cancellationToken = default);
    Task<IEnumerable<Alert>> GetByServiceAsync(string serviceName, CancellationToken cancellationToken = default);
    Task AddAsync(Alert alert, CancellationToken cancellationToken = default);
    Task UpdateAsync(Alert alert, CancellationToken cancellationToken = default);
    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);

    // Query operations
    Task<IEnumerable<Alert>> SearchAsync(
        string? serviceName = null,
        AlertSeverity? severity = null,
        AlertStatus? status = null,
        DateTime? triggeredAfter = null,
        DateTime? triggeredBefore = null,
        Guid? assignedToUserId = null,
        int skip = 0,
        int take = 50,
        CancellationToken cancellationToken = default);

    Task<int> CountAsync(
        string? serviceName = null,
        AlertSeverity? severity = null,
        AlertStatus? status = null,
        DateTime? triggeredAfter = null,
        DateTime? triggeredBefore = null,
        Guid? assignedToUserId = null,
        CancellationToken cancellationToken = default);

    // Status-based queries
    Task<IEnumerable<Alert>> GetOpenAlertsAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<Alert>> GetCriticalAlertsAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<Alert>> GetUnassignedAlertsAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<Alert>> GetAlertsAssignedToUserAsync(Guid userId, CancellationToken cancellationToken = default);

    // Analytics
    Task<Dictionary<AlertSeverity, int>> GetAlertCountBySeverityAsync(DateTime? fromDate = null, CancellationToken cancellationToken = default);
    Task<Dictionary<string, int>> GetAlertCountByServiceAsync(DateTime? fromDate = null, CancellationToken cancellationToken = default);
    Task<IEnumerable<Alert>> GetExpiredAlertsAsync(TimeSpan maxAge, CancellationToken cancellationToken = default);
}

public interface IMetricRepository
{
    // Basic CRUD operations
    Task<Metric?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<Metric?> GetByNameAndServiceAsync(string name, string serviceName, CancellationToken cancellationToken = default);
    Task<IEnumerable<Metric>> GetByServiceAsync(string serviceName, CancellationToken cancellationToken = default);
    Task AddAsync(Metric metric, CancellationToken cancellationToken = default);
    Task UpdateAsync(Metric metric, CancellationToken cancellationToken = default);
    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);

    // Query operations
    Task<IEnumerable<Metric>> SearchAsync(
        string? serviceName = null,
        MetricType? type = null,
        MonitoringCategory? category = null,
        bool? isActive = null,
        bool? alertingEnabled = null,
        CancellationToken cancellationToken = default);

    // Data point operations
    Task<IEnumerable<MetricDataPoint>> GetDataPointsAsync(
        Guid metricId,
        DateTime fromDate,
        DateTime toDate,
        CancellationToken cancellationToken = default);

    Task<IEnumerable<MetricDataPoint>> GetLatestDataPointsAsync(
        string serviceName,
        TimeSpan period,
        CancellationToken cancellationToken = default);

    // Aggregation operations
    Task<double> GetAverageValueAsync(Guid metricId, TimeSpan period, CancellationToken cancellationToken = default);
    Task<double> GetMaxValueAsync(Guid metricId, TimeSpan period, CancellationToken cancellationToken = default);
    Task<double> GetMinValueAsync(Guid metricId, TimeSpan period, CancellationToken cancellationToken = default);

    // Cleanup operations
    Task CleanupOldDataPointsAsync(TimeSpan retentionPeriod, CancellationToken cancellationToken = default);
}

public interface IHealthCheckRepository
{
    // Basic CRUD operations
    Task<HealthCheck?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<HealthCheck?> GetByNameAndServiceAsync(string name, string serviceName, CancellationToken cancellationToken = default);
    Task<IEnumerable<HealthCheck>> GetByServiceAsync(string serviceName, CancellationToken cancellationToken = default);
    Task AddAsync(HealthCheck healthCheck, CancellationToken cancellationToken = default);
    Task UpdateAsync(HealthCheck healthCheck, CancellationToken cancellationToken = default);
    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);

    // Query operations
    Task<IEnumerable<HealthCheck>> GetAllActiveAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<HealthCheck>> GetByStatusAsync(HealthStatus status, CancellationToken cancellationToken = default);
    Task<IEnumerable<HealthCheck>> GetDueForCheckAsync(CancellationToken cancellationToken = default);

    // Analytics
    Task<Dictionary<HealthStatus, int>> GetHealthStatusCountAsync(CancellationToken cancellationToken = default);
    Task<Dictionary<string, HealthStatus>> GetServiceHealthSummaryAsync(CancellationToken cancellationToken = default);
}

public interface IIncidentRepository
{
    // Basic CRUD operations
    Task<Incident?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<Incident?> GetByIdWithDetailsAsync(Guid id, CancellationToken cancellationToken = default);
    Task<IEnumerable<Incident>> GetByServiceAsync(string serviceName, CancellationToken cancellationToken = default);
    Task AddAsync(Incident incident, CancellationToken cancellationToken = default);
    Task UpdateAsync(Incident incident, CancellationToken cancellationToken = default);
    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);

    // Query operations
    Task<IEnumerable<Incident>> SearchAsync(
        string? serviceName = null,
        IncidentSeverity? severity = null,
        IncidentStatus? status = null,
        DateTime? createdAfter = null,
        DateTime? createdBefore = null,
        Guid? assignedToUserId = null,
        int skip = 0,
        int take = 50,
        CancellationToken cancellationToken = default);

    Task<int> CountAsync(
        string? serviceName = null,
        IncidentSeverity? severity = null,
        IncidentStatus? status = null,
        DateTime? createdAfter = null,
        DateTime? createdBefore = null,
        Guid? assignedToUserId = null,
        CancellationToken cancellationToken = default);

    // Status-based queries
    Task<IEnumerable<Incident>> GetOpenIncidentsAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<Incident>> GetCriticalIncidentsAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<Incident>> GetIncidentsAssignedToUserAsync(Guid userId, CancellationToken cancellationToken = default);

    // Analytics
    Task<Dictionary<IncidentSeverity, int>> GetIncidentCountBySeverityAsync(DateTime? fromDate = null, CancellationToken cancellationToken = default);
    Task<Dictionary<string, int>> GetIncidentCountByServiceAsync(DateTime? fromDate = null, CancellationToken cancellationToken = default);
    Task<double> GetAverageResolutionTimeAsync(DateTime? fromDate = null, CancellationToken cancellationToken = default);
}
