using UserManagement.Domain.Entities;

namespace UserManagement.Application.Models;

/// <summary>
/// Search criteria for audit log queries
/// </summary>
public class AuditLogSearchCriteria
{
    /// <summary>
    /// Start date for the search range
    /// </summary>
    public DateTime? FromDate { get; set; }

    /// <summary>
    /// End date for the search range
    /// </summary>
    public DateTime? ToDate { get; set; }

    /// <summary>
    /// User ID to filter by
    /// </summary>
    public Guid? UserId { get; set; }

    /// <summary>
    /// Action type to filter by
    /// </summary>
    public string? Action { get; set; }

    /// <summary>
    /// Entity type to filter by
    /// </summary>
    public string? EntityType { get; set; }

    /// <summary>
    /// Entity ID to filter by
    /// </summary>
    public string? EntityId { get; set; }

    /// <summary>
    /// Severity level to filter by
    /// </summary>
    public AuditSeverity? Severity { get; set; }

    /// <summary>
    /// IP address to filter by
    /// </summary>
    public string? IpAddress { get; set; }

    /// <summary>
    /// User agent to filter by
    /// </summary>
    public string? UserAgent { get; set; }

    /// <summary>
    /// Session ID to filter by
    /// </summary>
    public string? SessionId { get; set; }

    /// <summary>
    /// Correlation ID to filter by
    /// </summary>
    public string? CorrelationId { get; set; }

    /// <summary>
    /// Free text search in description
    /// </summary>
    public string? SearchText { get; set; }

    /// <summary>
    /// Page number for pagination
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Page size for pagination
    /// </summary>
    public int PageSize { get; set; } = 50;

    /// <summary>
    /// Sort field
    /// </summary>
    public string SortBy { get; set; } = "Timestamp";

    /// <summary>
    /// Sort direction (asc/desc)
    /// </summary>
    public string SortDirection { get; set; } = "desc";

    /// <summary>
    /// Include only critical events
    /// </summary>
    public bool CriticalOnly { get; set; } = false;

    /// <summary>
    /// Include only failed operations
    /// </summary>
    public bool FailedOnly { get; set; } = false;

    /// <summary>
    /// Additional filters as key-value pairs
    /// </summary>
    public Dictionary<string, object> AdditionalFilters { get; set; } = new();
}
