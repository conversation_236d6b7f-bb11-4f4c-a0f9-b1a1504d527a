-- TLI Microservices Database Setup Script
-- This script creates the databases for all microservices
-- Run this script as a PostgreSQL superuser

-- Create databases for all microservices
CREATE DATABASE "TLI_Identity" WITH ENCODING 'UTF8';
CREATE DATABASE "TLI_UserManagement" WITH ENCODING 'UTF8';
CREATE DATABASE "TLI_SubscriptionManagement" WITH ENCODING 'UTF8';
CREATE DATABASE "TLI_OrderManagement" WITH ENCODING 'UTF8';
CREATE DATABASE "TLI_TripManagement" WITH ENCODING 'UTF8';
CREATE DATABASE "TLI_NetworkFleetManagement" WITH ENCODING 'UTF8';
CREATE DATABASE "TLI_FinancialPayment" WITH ENCODING 'UTF8';
CREATE DATABASE "TLI_CommunicationNotification" WITH ENCODING 'UTF8';
CREATE DATABASE "TLI_AnalyticsBI" WITH ENCODING 'UTF8';
CREATE DATABASE "TLI_DataStorage" WITH ENCODING 'UTF8';
CREATE DATABASE "TLI_MonitoringObservability" WITH ENCODING 'UTF8';
CREATE DATABASE "TLI_AuditCompliance" WITH ENCODING 'UTF8';
CREATE DATABASE "TLI_MobileWorkflow" WITH ENCODING 'UTF8';

-- Create a dedicated user for the microservices
CREATE USER timescale WITH PASSWORD 'timescale';

-- Grant privileges to the timescale user on all databases
GRANT ALL PRIVILEGES ON DATABASE "TLI_Identity" TO timescale;
GRANT ALL PRIVILEGES ON DATABASE "TLI_UserManagement" TO timescale;
GRANT ALL PRIVILEGES ON DATABASE "TLI_SubscriptionManagement" TO timescale;
GRANT ALL PRIVILEGES ON DATABASE "TLI_OrderManagement" TO timescale;
GRANT ALL PRIVILEGES ON DATABASE "TLI_TripManagement" TO timescale;
GRANT ALL PRIVILEGES ON DATABASE "TLI_NetworkFleetManagement" TO timescale;
GRANT ALL PRIVILEGES ON DATABASE "TLI_FinancialPayment" TO timescale;
GRANT ALL PRIVILEGES ON DATABASE "TLI_CommunicationNotification" TO timescale;
GRANT ALL PRIVILEGES ON DATABASE "TLI_AnalyticsBI" TO timescale;
GRANT ALL PRIVILEGES ON DATABASE "TLI_DataStorage" TO timescale;
GRANT ALL PRIVILEGES ON DATABASE "TLI_MonitoringObservability" TO timescale;
GRANT ALL PRIVILEGES ON DATABASE "TLI_AuditCompliance" TO timescale;
GRANT ALL PRIVILEGES ON DATABASE "TLI_MobileWorkflow" TO timescale;

-- Connect to each database and set up extensions and permissions

-- Data Storage Database Setup
\c "TLI_DataStorage";
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";
GRANT ALL ON SCHEMA public TO timescale;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO timescale;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO timescale;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO timescale;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO timescale;

-- Monitoring & Observability Database Setup
\c "TLI_MonitoringObservability";
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";
-- Enable TimescaleDB extension for time-series data (if available)
-- CREATE EXTENSION IF NOT EXISTS timescaledb;
GRANT ALL ON SCHEMA public TO timescale;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO timescale;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO timescale;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO timescale;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO timescale;

-- Identity Database Setup
\c "TLI_Identity";
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
GRANT ALL ON SCHEMA public TO timescale;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO timescale;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO timescale;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO timescale;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO timescale;

-- User Management Database Setup
\c "TLI_UserManagement";
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
GRANT ALL ON SCHEMA public TO timescale;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO timescale;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO timescale;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO timescale;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO timescale;

-- Subscription Management Database Setup
\c "TLI_SubscriptionManagement";
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
GRANT ALL ON SCHEMA public TO timescale;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO timescale;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO timescale;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO timescale;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO timescale;

-- Order Management Database Setup
\c "TLI_OrderManagement";
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
GRANT ALL ON SCHEMA public TO timescale;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO timescale;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO timescale;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO timescale;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO timescale;

-- Trip Management Database Setup
\c "TLI_TripManagement";
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
GRANT ALL ON SCHEMA public TO timescale;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO timescale;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO timescale;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO timescale;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO timescale;

-- Network Fleet Management Database Setup
\c "TLI_NetworkFleetManagement";
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
GRANT ALL ON SCHEMA public TO timescale;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO timescale;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO timescale;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO timescale;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO timescale;

-- Financial Payment Database Setup
\c "TLI_FinancialPayment";
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
GRANT ALL ON SCHEMA public TO timescale;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO timescale;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO timescale;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO timescale;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO timescale;

-- Communication Notification Database Setup
\c "TLI_CommunicationNotification";
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
GRANT ALL ON SCHEMA public TO timescale;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO timescale;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO timescale;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO timescale;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO timescale;

-- Analytics BI Database Setup
\c "TLI_AnalyticsBI";
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
GRANT ALL ON SCHEMA public TO timescale;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO timescale;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO timescale;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO timescale;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO timescale;

-- Audit Compliance Database Setup
\c "TLI_AuditCompliance";
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
GRANT ALL ON SCHEMA public TO timescale;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO timescale;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO timescale;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO timescale;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO timescale;

-- Mobile Workflow Database Setup
\c "TLI_MobileWorkflow";
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
GRANT ALL ON SCHEMA public TO timescale;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO timescale;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO timescale;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO timescale;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO timescale;

-- Create indexes for better performance on commonly queried columns
-- These will be created by Entity Framework migrations, but listed here for reference

-- Data Storage specific indexes
\c "TLI_DataStorage";
-- Indexes will be created by EF migrations

-- Monitoring specific indexes and optimizations
\c "TLI_MonitoringObservability";
-- Indexes will be created by EF migrations
-- If TimescaleDB is available, convert MetricDataPoints to hypertable
-- SELECT create_hypertable('MetricDataPoints', 'Timestamp');

-- Print completion message
\echo 'Database setup completed successfully!'
\echo 'Created databases for all TLI microservices'
\echo 'User: timescale'
\echo 'Password: timescale'
\echo ''
\echo 'Next steps:'
\echo '1. Run Entity Framework migrations for each service'
\echo '2. Run seed data scripts'
\echo '3. Start the microservices'
