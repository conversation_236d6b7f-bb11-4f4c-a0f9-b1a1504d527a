using Microsoft.EntityFrameworkCore;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.ValueObjects;
using SubscriptionManagement.Infrastructure.Persistence;

namespace SubscriptionManagement.Infrastructure.Data;

public static class SeedData
{
    public static async Task SeedAsync(SubscriptionDbContext context)
    {
        if (await context.Plans.AnyAsync())
        {
            return; // Database has been seeded
        }

        // Seed Plans
        var plans = CreateSamplePlans();
        await context.Plans.AddRangeAsync(plans);

        // Seed Plan Features
        var planFeatures = CreatePlanFeatures(plans);
        await context.PlanFeatures.AddRangeAsync(planFeatures);

        // Seed Feature Flags
        var featureFlags = CreateFeatureFlags();
        await context.FeatureFlags.AddRangeAsync(featureFlags);

        // Seed Subscriptions
        var subscriptions = CreateSampleSubscriptions(plans);
        await context.Subscriptions.AddRangeAsync(subscriptions);

        await context.SaveChangesAsync();
    }

    private static List<Plan> CreateSamplePlans()
    {
        var plans = new List<Plan>();

        // Basic Plan for Transport Companies
        var basicPlan = new Plan(
            "Basic Transport",
            "Basic plan for small transport companies",
            new Money(2999, "INR"),
            BillingCycle.Monthly,
            UserType.TransportCompany,
            new Dictionary<string, object>
            {
                { "max_rfqs_per_month", 10 },
                { "max_vehicles", 5 },
                { "basic_analytics", true },
                { "email_support", true },
                { "phone_support", false },
                { "api_access", false }
            }
        );

        // Pro Plan for Transport Companies
        var proPlan = new Plan(
            "Pro Transport",
            "Professional plan for growing transport companies",
            new Money(7999, "INR"),
            BillingCycle.Monthly,
            UserType.TransportCompany,
            new Dictionary<string, object>
            {
                { "max_rfqs_per_month", 50 },
                { "max_vehicles", 25 },
                { "advanced_analytics", true },
                { "email_support", true },
                { "phone_support", true },
                { "api_access", true },
                { "priority_support", true }
            }
        );

        // Enterprise Plan for Transport Companies
        var enterprisePlan = new Plan(
            "Enterprise Transport",
            "Enterprise plan for large transport companies",
            new Money(19999, "INR"),
            BillingCycle.Monthly,
            UserType.TransportCompany,
            new Dictionary<string, object>
            {
                { "max_rfqs_per_month", -1 }, // Unlimited
                { "max_vehicles", -1 }, // Unlimited
                { "advanced_analytics", true },
                { "custom_reports", true },
                { "email_support", true },
                { "phone_support", true },
                { "api_access", true },
                { "priority_support", true },
                { "dedicated_account_manager", true }
            }
        );

        // Basic Plan for Brokers
        var brokerBasicPlan = new Plan(
            "Basic Broker",
            "Basic plan for freight brokers",
            new Money(1999, "INR"),
            BillingCycle.Monthly,
            UserType.Broker,
            new Dictionary<string, object>
            {
                { "max_active_rfqs", 20 },
                { "max_carrier_network", 50 },
                { "basic_matching", true },
                { "email_support", true },
                { "commission_tracking", true }
            }
        );

        // Pro Plan for Brokers
        var brokerProPlan = new Plan(
            "Pro Broker",
            "Professional plan for freight brokers",
            new Money(4999, "INR"),
            BillingCycle.Monthly,
            UserType.Broker,
            new Dictionary<string, object>
            {
                { "max_active_rfqs", 100 },
                { "max_carrier_network", 200 },
                { "advanced_matching", true },
                { "automated_bidding", true },
                { "email_support", true },
                { "phone_support", true },
                { "commission_tracking", true },
                { "performance_analytics", true }
            }
        );

        // Basic Plan for Carriers
        var carrierBasicPlan = new Plan(
            "Basic Carrier",
            "Basic plan for carriers",
            new Money(999, "INR"),
            BillingCycle.Monthly,
            UserType.Carrier,
            new Dictionary<string, object>
            {
                { "max_vehicles", 3 },
                { "max_drivers", 5 },
                { "basic_trip_tracking", true },
                { "email_support", true },
                { "mobile_app_access", true }
            }
        );

        // Pro Plan for Carriers
        var carrierProPlan = new Plan(
            "Pro Carrier",
            "Professional plan for carriers",
            new Money(2999, "INR"),
            BillingCycle.Monthly,
            UserType.Carrier,
            new Dictionary<string, object>
            {
                { "max_vehicles", 15 },
                { "max_drivers", 25 },
                { "advanced_trip_tracking", true },
                { "route_optimization", true },
                { "fuel_management", true },
                { "email_support", true },
                { "phone_support", true },
                { "mobile_app_access", true },
                { "driver_performance_tracking", true }
            }
        );

        plans.AddRange(new[] { basicPlan, proPlan, enterprisePlan, brokerBasicPlan, brokerProPlan, carrierBasicPlan, carrierProPlan });
        return plans;
    }

    private static List<PlanFeature> CreatePlanFeatures(List<Plan> plans)
    {
        var planFeatures = new List<PlanFeature>();

        foreach (var plan in plans)
        {
            foreach (var feature in plan.Features)
            {
                var planFeature = new PlanFeature(
                    plan.Id,
                    feature.Key,
                    feature.Value?.ToString() ?? "true",
                    feature.Key.Contains("max_") ? "limit" : "boolean"
                );
                planFeatures.Add(planFeature);
            }
        }

        return planFeatures;
    }

    private static List<FeatureFlag> CreateFeatureFlags()
    {
        var featureFlags = new List<FeatureFlag>();

        // Feature flag for new dashboard
        var newDashboardFlag = new FeatureFlag(
            "new_dashboard_v2",
            "New Dashboard V2",
            "Enable the new dashboard interface",
            false, // Not enabled by default
            new Dictionary<string, object>
            {
                { "rollout_percentage", 10 },
                { "target_user_types", new[] { "TransportCompany", "Broker" } }
            }
        );

        // Feature flag for AI-powered matching
        var aiMatchingFlag = new FeatureFlag(
            "ai_powered_matching",
            "AI Powered Matching",
            "Enable AI-powered load matching for brokers",
            false,
            new Dictionary<string, object>
            {
                { "rollout_percentage", 5 },
                { "target_user_types", new[] { "Broker" } },
                { "required_plan_tier", "Pro" }
            }
        );

        // Feature flag for real-time tracking
        var realTimeTrackingFlag = new FeatureFlag(
            "real_time_tracking",
            "Real-time Tracking",
            "Enable real-time GPS tracking for trips",
            true, // Enabled by default
            new Dictionary<string, object>
            {
                { "rollout_percentage", 100 },
                { "target_user_types", new[] { "TransportCompany", "Broker", "Carrier" } }
            }
        );

        // Feature flag for mobile app v2
        var mobileAppV2Flag = new FeatureFlag(
            "mobile_app_v2",
            "Mobile App V2",
            "Enable the new mobile app interface",
            false,
            new Dictionary<string, object>
            {
                { "rollout_percentage", 25 },
                { "target_user_types", new[] { "Carrier" } }
            }
        );

        featureFlags.AddRange(new[] { newDashboardFlag, aiMatchingFlag, realTimeTrackingFlag, mobileAppV2Flag });
        return featureFlags;
    }

    private static List<Subscription> CreateSampleSubscriptions(List<Plan> plans)
    {
        var subscriptions = new List<Subscription>();

        // Sample User IDs (these would come from Identity service)
        var transportCompanyUserId1 = Guid.Parse("11111111-1111-1111-1111-111111111111");
        var transportCompanyUserId2 = Guid.Parse("*************-2222-2222-************");
        var brokerUserId1 = Guid.Parse("*************-3333-3333-************");
        var brokerUserId2 = Guid.Parse("*************-4444-4444-************");
        var carrierUserId1 = Guid.Parse("*************-5555-5555-************");
        var carrierUserId2 = Guid.Parse("*************-6666-6666-************");

        // Transport Company subscriptions
        var basicTransportPlan = plans.First(p => p.Name == "Basic Transport");
        var proTransportPlan = plans.First(p => p.Name == "Pro Transport");

        var subscription1 = new Subscription(
            transportCompanyUserId1,
            basicTransportPlan,
            DateTime.UtcNow.AddDays(-30), // Started 30 days ago
            null, // No trial
            true, // Auto-renew
            ProrationMode.Immediate
        );

        var subscription2 = new Subscription(
            transportCompanyUserId2,
            proTransportPlan,
            DateTime.UtcNow.AddDays(-15), // Started 15 days ago
            DateTime.UtcNow.AddDays(-8), // Trial ended 8 days ago
            true, // Auto-renew
            ProrationMode.NextBillingCycle
        );

        // Broker subscriptions
        var basicBrokerPlan = plans.First(p => p.Name == "Basic Broker");
        var proBrokerPlan = plans.First(p => p.Name == "Pro Broker");

        var subscription3 = new Subscription(
            brokerUserId1,
            basicBrokerPlan,
            DateTime.UtcNow.AddDays(-20), // Started 20 days ago
            null, // No trial
            true, // Auto-renew
            ProrationMode.Immediate
        );

        var subscription4 = new Subscription(
            brokerUserId2,
            proBrokerPlan,
            DateTime.UtcNow.AddDays(-5), // Started 5 days ago
            DateTime.UtcNow.AddDays(2), // Trial ends in 2 days
            false, // No auto-renew yet
            ProrationMode.NextBillingCycle
        );

        // Carrier subscriptions
        var basicCarrierPlan = plans.First(p => p.Name == "Basic Carrier");
        var proCarrierPlan = plans.First(p => p.Name == "Pro Carrier");

        var subscription5 = new Subscription(
            carrierUserId1,
            basicCarrierPlan,
            DateTime.UtcNow.AddDays(-10), // Started 10 days ago
            null, // No trial
            true, // Auto-renew
            ProrationMode.Immediate
        );

        var subscription6 = new Subscription(
            carrierUserId2,
            proCarrierPlan,
            DateTime.UtcNow.AddDays(-3), // Started 3 days ago
            DateTime.UtcNow.AddDays(4), // Trial ends in 4 days
            false, // No auto-renew yet
            ProrationMode.NextBillingCycle
        );

        subscriptions.AddRange(new[] { subscription1, subscription2, subscription3, subscription4, subscription5, subscription6 });
        return subscriptions;
    }
}
