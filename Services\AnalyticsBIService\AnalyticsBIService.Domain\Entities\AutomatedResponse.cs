using AnalyticsBIService.Domain.Enums;
using Shared.Domain.Common;

namespace AnalyticsBIService.Domain.Entities;

/// <summary>
/// Automated response entity for alert handling
/// </summary>
public class AutomatedResponse : BaseEntity
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public string TriggerCondition { get; private set; }
    public string ResponseAction { get; private set; }
    public string Configuration { get; private set; }
    public bool IsEnabled { get; private set; }
    public Guid CreatedBy { get; private set; }
    public UserType UserType { get; private set; }
    public int ExecutionCount { get; private set; }
    public DateTime? LastExecuted { get; private set; }
    public string? LastExecutionResult { get; private set; }

    private AutomatedResponse()
    {
        Name = string.Empty;
        Description = string.Empty;
        TriggerCondition = string.Empty;
        ResponseAction = string.Empty;
        Configuration = string.Empty;
    }

    public AutomatedResponse(
        string name,
        string description,
        string triggerCondition,
        string responseAction,
        string configuration,
        Guid createdBy,
        UserType userType)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        TriggerCondition = triggerCondition ?? throw new ArgumentNullException(nameof(triggerCondition));
        ResponseAction = responseAction ?? throw new ArgumentNullException(nameof(responseAction));
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        CreatedBy = createdBy;
        UserType = userType;
        IsEnabled = true;
        ExecutionCount = 0;
    }

    public void UpdateConfiguration(string configuration)
    {
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        SetUpdatedAt();
    }

    public void UpdateTriggerCondition(string triggerCondition)
    {
        TriggerCondition = triggerCondition ?? throw new ArgumentNullException(nameof(triggerCondition));
        SetUpdatedAt();
    }

    public void UpdateResponseAction(string responseAction)
    {
        ResponseAction = responseAction ?? throw new ArgumentNullException(nameof(responseAction));
        SetUpdatedAt();
    }

    public void RecordExecution(string result)
    {
        ExecutionCount++;
        LastExecuted = DateTime.UtcNow;
        LastExecutionResult = result;
        SetUpdatedAt();
    }

    public void Enable()
    {
        IsEnabled = true;
        SetUpdatedAt();
    }

    public void Disable()
    {
        IsEnabled = false;
        SetUpdatedAt();
    }
}
