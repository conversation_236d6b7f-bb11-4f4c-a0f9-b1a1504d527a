# Broker App Features - API Documentation

## Overview

This document provides comprehensive API documentation for the new Broker App features, including request/response schemas, authentication requirements, and usage examples.

## Authentication

All endpoints require JWT Bearer token authentication with appropriate role-based permissions:

```http
Authorization: Bearer <jwt_token>
```

**Required Roles by Feature**:
- KYC Operations: `Broker`, `Admin`
- Subscription Management: `Broker`, `Admin`
- Markup Editing: `Broker`
- Invoice Generation: `System`, `Admin`

## 1. KYC Auto-check Functionality

### Start KYC Auto-Verification

**Endpoint**: `POST /api/kyc/verify/{documentId}`

**Description**: Initiates automated KYC verification for a specific document.

**Request**:
```json
{
  "documentType": "PanCard",
  "forceVerification": false
}
```

**Response**:
```json
{
  "success": true,
  "message": "KYC verification initiated successfully",
  "verificationId": "123e4567-e89b-12d3-a456-426614174000",
  "estimatedCompletionTime": "2024-01-15T10:35:00Z"
}
```

**Status Codes**:
- `200 OK`: Verification initiated successfully
- `400 Bad Request`: Invalid document or already verified
- `401 Unauthorized`: Invalid or missing authentication
- `404 Not Found`: Document not found

### Get KYC Verification Status

**Endpoint**: `GET /api/kyc/status`

**Query Parameters**:
- `documentType` (optional): Filter by specific document type

**Response**:
```json
{
  "userId": "123e4567-e89b-12d3-a456-426614174000",
  "overallCompletionPercentage": 75.0,
  "overallStatus": "InProgress",
  "lastVerificationAttempt": "2024-01-15T10:30:00Z",
  "documents": [
    {
      "documentId": "123e4567-e89b-12d3-a456-426614174001",
      "documentType": "PanCard",
      "status": "Verified",
      "confidenceScore": 0.95,
      "verifiedAt": "2024-01-15T10:32:00Z",
      "extractedData": {
        "panNumber": "**********",
        "name": "John Doe",
        "dateOfBirth": "1990-01-15"
      }
    }
  ],
  "pendingDocuments": ["GstCertificate"]
}
```

### Get KYC Verification History

**Endpoint**: `GET /api/kyc/history/{documentId}`

**Response**:
```json
{
  "documentId": "123e4567-e89b-12d3-a456-426614174001",
  "verificationHistory": [
    {
      "attemptId": "123e4567-e89b-12d3-a456-426614174002",
      "attemptedAt": "2024-01-15T10:30:00Z",
      "status": "Verified",
      "confidenceScore": 0.95,
      "processingTime": 120,
      "provider": "PAN Verification API",
      "errors": []
    }
  ]
}
```

## 2. Auto-renew Toggle Implementation

### Update Auto-Renewal Preferences

**Endpoint**: `PUT /api/subscriptions/{subscriptionId}/auto-renewal`

**Request**:
```json
{
  "isEnabled": true,
  "daysBeforeExpiry": 7,
  "maxRetryAttempts": 3,
  "notifyBeforeRenewal": true,
  "notifyAfterRenewal": true,
  "notifyOnFailure": true,
  "notificationChannels": ["email", "sms"]
}
```

**Response**:
```json
{
  "success": true,
  "message": "Auto-renewal preferences updated successfully",
  "subscriptionId": "123e4567-e89b-12d3-a456-426614174000",
  "nextRenewalAttemptAt": "2024-02-08T00:00:00Z",
  "preferences": {
    "isEnabled": true,
    "daysBeforeExpiry": 7,
    "maxRetryAttempts": 3,
    "notifyBeforeRenewal": true,
    "notifyAfterRenewal": true,
    "notifyOnFailure": true,
    "notificationChannels": ["email", "sms"]
  }
}
```

### Get Auto-Renewal Status

**Endpoint**: `GET /api/subscriptions/{subscriptionId}/auto-renewal`

**Response**:
```json
{
  "subscriptionId": "123e4567-e89b-12d3-a456-426614174000",
  "isAutoRenewalEnabled": true,
  "nextBillingDate": "2024-02-15T00:00:00Z",
  "nextRenewalAttemptAt": "2024-02-08T00:00:00Z",
  "renewalFailureCount": 0,
  "lastRenewalAttempt": null,
  "preferences": {
    "isEnabled": true,
    "daysBeforeExpiry": 7,
    "maxRetryAttempts": 3,
    "notifyBeforeRenewal": true,
    "notifyAfterRenewal": true,
    "notifyOnFailure": true,
    "notificationChannels": ["email", "sms"]
  },
  "recentRenewalAttempts": []
}
```

### Get Renewal History

**Endpoint**: `GET /api/subscriptions/{subscriptionId}/renewal-history`

**Query Parameters**:
- `fromDate` (optional): Start date for history
- `toDate` (optional): End date for history
- `limit` (optional): Maximum number of records (default: 50)

**Response**:
```json
{
  "subscriptionId": "123e4567-e89b-12d3-a456-426614174000",
  "renewalHistory": [
    {
      "attemptId": "123e4567-e89b-12d3-a456-426614174001",
      "attemptedAt": "2024-01-15T00:00:00Z",
      "status": "Successful",
      "attemptedAmount": 999.00,
      "paymentReference": "PAY_123456789",
      "nextRetryAt": null,
      "failureReason": null
    }
  ],
  "totalAttempts": 1,
  "successfulRenewals": 1,
  "failedRenewals": 0
}
```

## 3. Broker Markup Editing System

### Update Broker Markup

**Endpoint**: `PUT /api/bids/{bidId}/markup`

**Request**:
```json
{
  "markupPercentage": 15.5,
  "calculationMethod": "Percentage",
  "justification": "Premium service for urgent delivery",
  "previewOnly": false
}
```

**Response**:
```json
{
  "success": true,
  "message": "Markup updated successfully",
  "originalAmount": 10000.00,
  "markupAmount": 1550.00,
  "finalAmount": 11550.00,
  "markupPercentage": 15.5,
  "subscriptionTierLimit": 20.0,
  "isWithinLimit": true,
  "validationWarnings": []
}
```

### Preview Markup Calculation

**Endpoint**: `POST /api/bids/{bidId}/markup/preview`

**Request**:
```json
{
  "markupPercentage": 25.0,
  "calculationMethod": "Percentage"
}
```

**Response**:
```json
{
  "success": false,
  "message": "Markup percentage 25.0% exceeds subscription tier limit of 20.0%",
  "originalAmount": 10000.00,
  "markupAmount": 2500.00,
  "finalAmount": 12500.00,
  "markupPercentage": 25.0,
  "subscriptionTierLimit": 20.0,
  "isWithinLimit": false,
  "validationWarnings": [
    "Markup exceeds subscription tier limit",
    "Consider upgrading subscription for higher markup limits"
  ]
}
```

### Get Markup History

**Endpoint**: `GET /api/bids/{bidId}/markup/history`

**Response**:
```json
{
  "bidId": "123e4567-e89b-12d3-a456-426614174000",
  "markupHistory": [
    {
      "modifiedAt": "2024-01-15T10:30:00Z",
      "modifiedBy": "123e4567-e89b-12d3-a456-426614174001",
      "previousMarkup": 10.0,
      "newMarkup": 15.5,
      "justification": "Premium service for urgent delivery",
      "originalAmount": 10000.00,
      "finalAmount": 11550.00
    }
  ]
}
```

### Get Subscription Tier Markup Limits

**Endpoint**: `GET /api/subscriptions/markup-limits`

**Response**:
```json
{
  "currentTier": "Premium",
  "markupLimits": {
    "basic": 10.0,
    "standard": 15.0,
    "premium": 20.0,
    "enterprise": 30.0
  },
  "currentLimit": 20.0,
  "upgradeOptions": [
    {
      "tier": "Enterprise",
      "markupLimit": 30.0,
      "monthlyPrice": 2999.00,
      "benefits": ["Higher markup limits", "Priority support", "Advanced analytics"]
    }
  ]
}
```

## 4. Auto-generate Invoice at Order Confirmation

### Get Auto-Generated Invoice

**Endpoint**: `GET /api/invoices/order/{orderId}`

**Response**:
```json
{
  "invoiceId": "123e4567-e89b-12d3-a456-426614174000",
  "invoiceNumber": "INV-2024-001234",
  "orderId": "123e4567-e89b-12d3-a456-426614174001",
  "orderNumber": "ORD-2024-005678",
  "status": "Draft",
  "totalAmount": {
    "amount": 11550.00,
    "currency": "INR"
  },
  "taxDetails": {
    "baseAmount": 10000.00,
    "gstAmount": 1800.00,
    "tdsAmount": 200.00,
    "netPayable": 11600.00
  },
  "generatedAt": "2024-01-15T10:35:00Z",
  "dueDate": "2024-01-30T23:59:59Z",
  "isAutoGenerated": true,
  "lineItems": [
    {
      "description": "Transportation Service - Mumbai to Delhi",
      "quantity": 1,
      "unitPrice": 10000.00,
      "totalPrice": 10000.00,
      "hsnCode": "996511"
    }
  ]
}
```

### Trigger Manual Invoice Generation

**Endpoint**: `POST /api/invoices/generate/order/{orderId}`

**Request**:
```json
{
  "forceGeneration": false,
  "customNotes": "Manual invoice generation requested",
  "dueDate": "2024-01-30T23:59:59Z"
}
```

**Response**:
```json
{
  "success": true,
  "message": "Invoice generated successfully",
  "invoiceId": "123e4567-e89b-12d3-a456-426614174000",
  "invoiceNumber": "INV-2024-001234",
  "totalAmount": {
    "amount": 11550.00,
    "currency": "INR"
  },
  "generatedAt": "2024-01-15T10:35:00Z",
  "dueDate": "2024-01-30T23:59:59Z"
}
```

### Get Invoice Generation Status

**Endpoint**: `GET /api/invoices/generation-status/{orderId}`

**Response**:
```json
{
  "orderId": "123e4567-e89b-12d3-a456-426614174001",
  "generationStatus": "Completed",
  "invoiceId": "123e4567-e89b-12d3-a456-426614174000",
  "generatedAt": "2024-01-15T10:35:00Z",
  "processingTime": 2.5,
  "errors": [],
  "isAutoGenerated": true
}
```

## Error Handling

### Standard Error Response Format

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "One or more validation errors occurred",
    "details": [
      {
        "field": "markupPercentage",
        "message": "Markup percentage must be between 0 and 100"
      }
    ],
    "timestamp": "2024-01-15T10:30:00Z",
    "traceId": "123e4567-e89b-12d3-a456-426614174000"
  }
}
```

### Common Error Codes

- `VALIDATION_ERROR`: Request validation failed
- `UNAUTHORIZED`: Authentication required or insufficient permissions
- `NOT_FOUND`: Requested resource not found
- `BUSINESS_RULE_VIOLATION`: Business logic constraint violated
- `EXTERNAL_SERVICE_ERROR`: External API call failed
- `RATE_LIMIT_EXCEEDED`: Too many requests
- `SYSTEM_ERROR`: Internal server error

## Rate Limiting

All endpoints are subject to rate limiting:

- **KYC Verification**: 10 requests per minute per user
- **Subscription Management**: 30 requests per minute per user
- **Markup Operations**: 60 requests per minute per user
- **Invoice Generation**: 20 requests per minute per user

Rate limit headers are included in responses:

```http
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 45
X-RateLimit-Reset: 1642248000
```

## Webhooks

### KYC Verification Completed

```json
{
  "event": "kyc.verification.completed",
  "timestamp": "2024-01-15T10:35:00Z",
  "data": {
    "userId": "123e4567-e89b-12d3-a456-426614174000",
    "documentId": "123e4567-e89b-12d3-a456-426614174001",
    "documentType": "PanCard",
    "status": "Verified",
    "confidenceScore": 0.95
  }
}
```

### Subscription Auto-Renewed

```json
{
  "event": "subscription.auto.renewed",
  "timestamp": "2024-01-15T00:00:00Z",
  "data": {
    "subscriptionId": "123e4567-e89b-12d3-a456-426614174000",
    "userId": "123e4567-e89b-12d3-a456-426614174001",
    "renewedAmount": 999.00,
    "nextBillingDate": "2024-02-15T00:00:00Z"
  }
}
```

### Invoice Auto-Generated

```json
{
  "event": "invoice.auto.generated",
  "timestamp": "2024-01-15T10:35:00Z",
  "data": {
    "invoiceId": "123e4567-e89b-12d3-a456-426614174000",
    "orderId": "123e4567-e89b-12d3-a456-426614174001",
    "invoiceNumber": "INV-2024-001234",
    "totalAmount": 11550.00
  }
}
```
