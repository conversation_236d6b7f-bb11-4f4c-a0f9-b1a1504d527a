using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using AuditCompliance.Application.Interfaces;
using AuditCompliance.Application.DTOs.Analytics;
using AuditCompliance.Infrastructure.Hubs;
using AuditCompliance.Domain.Interfaces;

namespace AuditCompliance.Infrastructure.Services;

/// <summary>
/// Service for real-time dashboard updates and notifications using SignalR
/// </summary>
public class RealTimeDashboardService : IRealTimeDashboardService
{
    private readonly IHubContext<ComplianceMonitoringHub, IComplianceMonitoringClient> _hubContext;
    private readonly IAdvancedAnalyticsService _analyticsService;
    private readonly IAuditLogRepository _auditLogRepository;
    private readonly IComplianceReportRepository _complianceReportRepository;
    private readonly ILogger<RealTimeDashboardService> _logger;

    public RealTimeDashboardService(
        IHubContext<ComplianceMonitoringHub, IComplianceMonitoringClient> hubContext,
        IAdvancedAnalyticsService analyticsService,
        IAuditLogRepository auditLogRepository,
        IComplianceReportRepository complianceReportRepository,
        ILogger<RealTimeDashboardService> logger)
    {
        _hubContext = hubContext;
        _analyticsService = analyticsService;
        _auditLogRepository = auditLogRepository;
        _complianceReportRepository = complianceReportRepository;
        _logger = logger;
    }

    public async Task BroadcastComplianceAlertAsync(
        ComplianceAlertDto alert,
        Guid? organizationId = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Broadcasting compliance alert {AlertId} of type {AlertType}", 
                alert.Id, alert.AlertType);

            if (organizationId.HasValue)
            {
                await _hubContext.Clients.Group($"org_{organizationId}")
                    .ComplianceAlert(alert);
            }
            else
            {
                await _hubContext.Clients.Group("dashboard_monitoring")
                    .ComplianceAlert(alert);
            }

            // Also send to entity-specific subscribers if applicable
            if (alert.EntityId.HasValue && !string.IsNullOrEmpty(alert.EntityType))
            {
                await _hubContext.Clients.Group($"entity_{alert.EntityType}_{alert.EntityId}")
                    .ComplianceAlert(alert);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error broadcasting compliance alert {AlertId}", alert.Id);
        }
    }

    public async Task BroadcastAnomalyDetectionAsync(
        ComplianceAnomalyDto anomaly,
        Guid? organizationId = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Broadcasting anomaly detection {AnomalyId} of type {AnomalyType}", 
                anomaly.Id, anomaly.AnomalyType);

            if (organizationId.HasValue)
            {
                await _hubContext.Clients.Group($"org_{organizationId}")
                    .AnomalyDetected(anomaly);
            }
            else
            {
                await _hubContext.Clients.Group("dashboard_monitoring")
                    .AnomalyDetected(anomaly);
            }

            // Send to entity-specific subscribers if applicable
            if (anomaly.EntityId.HasValue && !string.IsNullOrEmpty(anomaly.EntityType))
            {
                await _hubContext.Clients.Group($"entity_{anomaly.EntityType}_{anomaly.EntityId}")
                    .AnomalyDetected(anomaly);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error broadcasting anomaly detection {AnomalyId}", anomaly.Id);
        }
    }

    public async Task BroadcastRiskScoreUpdateAsync(
        RiskScoreUpdateDto riskUpdate,
        Guid? organizationId = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Broadcasting risk score update for entity {EntityId}", 
                riskUpdate.EntityId);

            if (organizationId.HasValue)
            {
                await _hubContext.Clients.Group($"org_{organizationId}")
                    .RiskScoreUpdate(riskUpdate);
            }
            else
            {
                await _hubContext.Clients.Group("dashboard_monitoring")
                    .RiskScoreUpdate(riskUpdate);
            }

            // Send to entity-specific subscribers
            await _hubContext.Clients.Group($"entity_{riskUpdate.EntityType}_{riskUpdate.EntityId}")
                .RiskScoreUpdate(riskUpdate);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error broadcasting risk score update for entity {EntityId}", 
                riskUpdate.EntityId);
        }
    }

    public async Task BroadcastDashboardMetricsUpdateAsync(
        DashboardMetricsDto metrics,
        Guid? organizationId = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Broadcasting dashboard metrics update for organization {OrganizationId}", 
                organizationId);

            if (organizationId.HasValue)
            {
                await _hubContext.Clients.Group($"org_{organizationId}")
                    .DashboardMetricsUpdate(metrics);
            }
            else
            {
                await _hubContext.Clients.Group("dashboard_monitoring")
                    .DashboardMetricsUpdate(metrics);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error broadcasting dashboard metrics update");
        }
    }

    public async Task BroadcastComplianceStatusUpdateAsync(
        ComplianceStatusUpdateDto statusUpdate,
        Guid? organizationId = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Broadcasting compliance status update for organization {OrganizationId}", 
                organizationId);

            if (organizationId.HasValue)
            {
                await _hubContext.Clients.Group($"org_{organizationId}")
                    .ComplianceStatusUpdate(statusUpdate);
            }
            else
            {
                await _hubContext.Clients.Group("dashboard_monitoring")
                    .ComplianceStatusUpdate(statusUpdate);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error broadcasting compliance status update");
        }
    }

    public async Task BroadcastComplianceReportUpdateAsync(
        ComplianceReportUpdateDto reportUpdate,
        Guid? organizationId = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Broadcasting compliance report update for report {ReportId}", 
                reportUpdate.ReportId);

            if (organizationId.HasValue)
            {
                await _hubContext.Clients.Group($"org_{organizationId}")
                    .ComplianceReportUpdate(reportUpdate);
            }
            else
            {
                await _hubContext.Clients.Group("dashboard_monitoring")
                    .ComplianceReportUpdate(reportUpdate);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error broadcasting compliance report update for report {ReportId}", 
                reportUpdate.ReportId);
        }
    }

    public async Task BroadcastAuditLogNotificationAsync(
        AuditLogNotificationDto auditNotification,
        Guid? organizationId = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Broadcasting audit log notification {AuditLogId}", 
                auditNotification.AuditLogId);

            if (organizationId.HasValue)
            {
                await _hubContext.Clients.Group($"org_{organizationId}")
                    .AuditLogNotification(auditNotification);
            }
            else
            {
                await _hubContext.Clients.Group("dashboard_monitoring")
                    .AuditLogNotification(auditNotification);
            }

            // Send to entity-specific subscribers if applicable
            if (auditNotification.EntityId.HasValue && !string.IsNullOrEmpty(auditNotification.EntityType))
            {
                await _hubContext.Clients.Group($"entity_{auditNotification.EntityType}_{auditNotification.EntityId}")
                    .AuditLogNotification(auditNotification);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error broadcasting audit log notification {AuditLogId}", 
                auditNotification.AuditLogId);
        }
    }

    public async Task BroadcastModelTrainingUpdateAsync(
        ModelTrainingUpdateDto trainingUpdate,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Broadcasting model training update for model {ModelType}", 
                trainingUpdate.ModelType);

            await _hubContext.Clients.Group("dashboard_monitoring")
                .ModelTrainingUpdate(trainingUpdate);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error broadcasting model training update for model {ModelType}", 
                trainingUpdate.ModelType);
        }
    }

    public async Task SendPersonalizedDashboardUpdateAsync(
        string userId,
        PersonalizedDashboardDto dashboard,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Sending personalized dashboard update to user {UserId}", userId);

            await _hubContext.Clients.User(userId)
                .DashboardMetricsUpdate(dashboard);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending personalized dashboard update to user {UserId}", userId);
        }
    }

    public async Task BroadcastEntityComplianceUpdateAsync(
        Guid entityId,
        string entityType,
        EntityComplianceUpdateDto update,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Broadcasting entity compliance update for {EntityType} {EntityId}", 
                entityType, entityId);

            await _hubContext.Clients.Group($"entity_{entityType}_{entityId}")
                .ComplianceStatusUpdate(update);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error broadcasting entity compliance update for {EntityType} {EntityId}", 
                entityType, entityId);
        }
    }

    public async Task<DashboardMetricsDto> GetCurrentDashboardMetricsAsync(
        Guid? organizationId = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Getting current dashboard metrics for organization {OrganizationId}", 
                organizationId);

            // Get comprehensive compliance insights
            var insights = await _analyticsService.GenerateComplianceInsightsAsync(organizationId, cancellationToken);

            // Get recent anomalies
            var fromDate = DateTime.UtcNow.AddDays(-7);
            var toDate = DateTime.UtcNow;
            var anomalies = await _analyticsService.DetectComplianceAnomaliesAsync(fromDate, toDate, cancellationToken: cancellationToken);

            // Create dashboard metrics
            var metrics = new DashboardMetricsDto
            {
                Timestamp = DateTime.UtcNow,
                OrganizationId = organizationId,
                Overview = insights.Overview,
                RealTimeMetrics = insights.KeyMetrics,
                RecentAnomalies = anomalies.Take(10).ToList(),
                ActiveAlerts = await GetActiveAlertsAsync(organizationId, cancellationToken),
                TrendIndicators = await GetTrendIndicatorsAsync(organizationId, cancellationToken)
            };

            return metrics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting current dashboard metrics");
            throw;
        }
    }

    public async Task<ComplianceStatusUpdateDto> GetRealTimeComplianceStatusAsync(
        Guid? organizationId = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Getting real-time compliance status for organization {OrganizationId}", 
                organizationId);

            var insights = await _analyticsService.GenerateComplianceInsightsAsync(organizationId, cancellationToken);

            var statusUpdate = new ComplianceStatusUpdateDto
            {
                Timestamp = DateTime.UtcNow,
                OrganizationId = organizationId,
                OverallComplianceScore = insights.Overview.OverallScore,
                ComplianceStatus = GetComplianceStatusFromScore(insights.Overview.OverallScore),
                ActiveViolations = insights.Overview.NonCompliantEntities,
                PendingReviews = insights.Overview.PendingReviews,
                CompletedAudits = await GetCompletedAuditsCountAsync(organizationId, cancellationToken),
                StandardCompliance = insights.Overview.StandardScores,
                RecentChanges = await GetRecentChangesAsync(organizationId, cancellationToken)
            };

            return statusUpdate;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting real-time compliance status");
            throw;
        }
    }

    // Private helper methods
    private async Task<List<ComplianceAlertDto>> GetActiveAlertsAsync(
        Guid? organizationId, CancellationToken cancellationToken)
    {
        // This would typically query active alerts from the database
        // For now, return empty list as placeholder
        return new List<ComplianceAlertDto>();
    }

    private async Task<Dictionary<string, float>> GetTrendIndicatorsAsync(
        Guid? organizationId, CancellationToken cancellationToken)
    {
        // This would calculate trend indicators based on historical data
        // For now, return placeholder data
        return new Dictionary<string, float>
        {
            ["ComplianceScoreTrend"] = 0.05f, // 5% improvement
            ["ViolationTrend"] = -0.10f, // 10% reduction in violations
            ["AuditFrequencyTrend"] = 0.15f // 15% increase in audit frequency
        };
    }

    private string GetComplianceStatusFromScore(float score)
    {
        return score switch
        {
            >= 0.9f => "Compliant",
            >= 0.7f => "At Risk",
            _ => "Non-Compliant"
        };
    }

    private async Task<int> GetCompletedAuditsCountAsync(
        Guid? organizationId, CancellationToken cancellationToken)
    {
        // This would query completed audits from the last 30 days
        // For now, return placeholder value
        return 25;
    }

    private async Task<List<string>> GetRecentChangesAsync(
        Guid? organizationId, CancellationToken cancellationToken)
    {
        // This would query recent changes in compliance status
        // For now, return placeholder data
        return new List<string>
        {
            "GDPR compliance score improved by 5%",
            "2 new compliance violations detected",
            "Data retention policy updated",
            "3 audit reports completed"
        };
    }
}
