using FluentAssertions;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using NetworkFleetManagement.Domain.Entities;
using NetworkFleetManagement.Infrastructure.MessageBus;
using NetworkFleetManagement.IntegrationTests.TestFixtures;
using RabbitMQ.Client;
using RabbitMQ.Client.Events;
using System.Text;
using System.Text.Json;
using Xunit;

namespace NetworkFleetManagement.IntegrationTests.Events;

[Collection("NetworkFleet")]
public class PreferredPartnerEventTests : IClassFixture<NetworkFleetTestFixture>
{
    private readonly NetworkFleetTestFixture _fixture;
    private readonly Guid _testUserId = Guid.NewGuid();

    public PreferredPartnerEventTests(NetworkFleetTestFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task PreferredPartnerActivated_PublishesIntegrationEvent()
    {
        // Arrange
        await _fixture.ResetDatabaseAsync();
        var receivedEvents = new List<string>();
        
        // Setup message consumer
        using var connection = CreateRabbitMQConnection();
        using var channel = connection.CreateModel();
        
        var queueName = SetupEventConsumer(channel, "network-fleet.preferredpartneractivatedevent", receivedEvents);

        // Create and activate partner
        var partner = PreferredPartner.Create(
            _testUserId,
            Guid.NewGuid(),
            PreferredPartnerType.Carrier,
            PreferenceLevel.High,
            1);

        // Act
        using var scope = _fixture.Services.CreateScope();
        var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();
        var context = scope.ServiceProvider.GetRequiredService<NetworkFleetDbContext>();

        context.PreferredPartners.Add(partner);
        partner.Activate(); // This should trigger the domain event
        await context.SaveChangesAsync();

        // Publish domain events
        await mediator.Publish(new PreferredPartnerActivatedEvent(
            partner.UserId, 
            partner.PartnerId, 
            partner.Id, 
            partner.PartnerType));

        // Assert
        await Task.Delay(1000); // Wait for message processing
        receivedEvents.Should().HaveCount(1);
        
        var eventData = JsonSerializer.Deserialize<JsonElement>(receivedEvents[0]);
        eventData.GetProperty("EventType").GetString().Should().Be("PreferredPartnerActivatedEvent");
        eventData.GetProperty("Source").GetString().Should().Be("NetworkFleetManagement");
        
        var data = eventData.GetProperty("Data");
        data.GetProperty("UserId").GetString().Should().Be(_testUserId.ToString());
        data.GetProperty("PartnerType").GetString().Should().Be("Carrier");

        // Cleanup
        channel.QueueDelete(queueName);
    }

    [Fact]
    public async Task PreferredPartnerDeactivated_PublishesIntegrationEvent()
    {
        // Arrange
        await _fixture.ResetDatabaseAsync();
        var receivedEvents = new List<string>();
        
        using var connection = CreateRabbitMQConnection();
        using var channel = connection.CreateModel();
        
        var queueName = SetupEventConsumer(channel, "network-fleet.preferredpartnerdeactivatedevent", receivedEvents);

        var partner = PreferredPartner.Create(
            _testUserId,
            Guid.NewGuid(),
            PreferredPartnerType.Carrier,
            PreferenceLevel.High,
            1);

        // Act
        using var scope = _fixture.Services.CreateScope();
        var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

        partner.Activate();
        partner.Deactivate("Test deactivation");

        await mediator.Publish(new PreferredPartnerDeactivatedEvent(
            partner.UserId,
            partner.PartnerId,
            partner.Id,
            partner.PartnerType,
            "Test deactivation"));

        // Assert
        await Task.Delay(1000);
        receivedEvents.Should().HaveCount(1);
        
        var eventData = JsonSerializer.Deserialize<JsonElement>(receivedEvents[0]);
        eventData.GetProperty("EventType").GetString().Should().Be("PreferredPartnerDeactivatedEvent");
        
        var data = eventData.GetProperty("Data");
        data.GetProperty("Reason").GetString().Should().Be("Test deactivation");
    }

    [Fact]
    public async Task PreferredPartnerPreferenceLevelUpdated_PublishesIntegrationEvent()
    {
        // Arrange
        await _fixture.ResetDatabaseAsync();
        var receivedEvents = new List<string>();
        
        using var connection = CreateRabbitMQConnection();
        using var channel = connection.CreateModel();
        
        var queueName = SetupEventConsumer(channel, "network-fleet.preferredpartnerpreferencelevelupdatedevent", receivedEvents);

        var partner = PreferredPartner.Create(
            _testUserId,
            Guid.NewGuid(),
            PreferredPartnerType.Carrier,
            PreferenceLevel.Medium,
            2);

        // Act
        using var scope = _fixture.Services.CreateScope();
        var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

        partner.UpdatePreferenceLevel(PreferenceLevel.High, 1);

        await mediator.Publish(new PreferredPartnerPreferenceLevelUpdatedEvent(
            partner.UserId,
            partner.PartnerId,
            partner.Id,
            PreferenceLevel.High,
            1));

        // Assert
        await Task.Delay(1000);
        receivedEvents.Should().HaveCount(1);
        
        var eventData = JsonSerializer.Deserialize<JsonElement>(receivedEvents[0]);
        var data = eventData.GetProperty("Data");
        data.GetProperty("NewLevel").GetString().Should().Be("High");
        data.GetProperty("NewPriority").GetInt32().Should().Be(1);
    }

    [Fact]
    public async Task MessageBusPublisher_PublishesCorrectFormat()
    {
        // Arrange
        using var scope = _fixture.Services.CreateScope();
        var publisher = scope.ServiceProvider.GetRequiredService<IMessageBusPublisher>();
        
        var receivedEvents = new List<string>();
        using var connection = CreateRabbitMQConnection();
        using var channel = connection.CreateModel();
        
        var queueName = SetupEventConsumer(channel, "test.event", receivedEvents);

        var testEvent = new
        {
            Id = Guid.NewGuid(),
            Name = "Test Event",
            Timestamp = DateTime.UtcNow
        };

        // Act
        await publisher.PublishIntegrationEventAsync(testEvent);

        // Assert
        await Task.Delay(1000);
        receivedEvents.Should().HaveCount(1);
        
        var eventData = JsonSerializer.Deserialize<JsonElement>(receivedEvents[0]);
        eventData.GetProperty("Source").GetString().Should().Be("NetworkFleetManagement");
        eventData.GetProperty("Version").GetString().Should().Be("1.0");
        eventData.GetProperty("Data").GetProperty("Name").GetString().Should().Be("Test Event");

        // Cleanup
        channel.QueueDelete(queueName);
    }

    private IConnection CreateRabbitMQConnection()
    {
        var factory = new ConnectionFactory
        {
            HostName = "localhost", // This would be configured from test settings
            Port = 5672,
            UserName = "test",
            Password = "test",
            VirtualHost = "/"
        };

        return factory.CreateConnection();
    }

    private string SetupEventConsumer(IModel channel, string routingKey, List<string> receivedEvents)
    {
        var exchangeName = "test.events";
        var queueName = $"test-queue-{Guid.NewGuid()}";

        channel.ExchangeDeclare(exchange: exchangeName, type: ExchangeType.Topic, durable: true);
        channel.QueueDeclare(queue: queueName, durable: false, exclusive: true, autoDelete: true);
        channel.QueueBind(queue: queueName, exchange: exchangeName, routingKey: routingKey);

        var consumer = new EventingBasicConsumer(channel);
        consumer.Received += (model, ea) =>
        {
            var body = ea.Body.ToArray();
            var message = Encoding.UTF8.GetString(body);
            receivedEvents.Add(message);
        };

        channel.BasicConsume(queue: queueName, autoAck: true, consumer: consumer);
        
        return queueName;
    }
}
