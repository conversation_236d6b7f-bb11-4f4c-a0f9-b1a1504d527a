using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Infrastructure.Data;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Infrastructure.Services;

public class PlatformTransactionProvider : IPlatformTransactionProvider
{
    private readonly FinancialPaymentDbContext _context;
    private readonly ILogger<PlatformTransactionProvider> _logger;

    public PlatformTransactionProvider(
        FinancialPaymentDbContext context,
        ILogger<PlatformTransactionProvider> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<List<PlatformTransaction>> GetTransactionsAsync(DateTime fromDate, DateTime toDate, string? paymentGateway = null)
    {
        _logger.LogDebug("Getting platform transactions from {FromDate} to {ToDate} for gateway {Gateway}",
            fromDate, toDate, paymentGateway);

        try
        {
            var transactions = new List<PlatformTransaction>();

            // Get escrow transactions
            var escrowTransactions = await GetEscrowTransactionsAsync(fromDate, toDate, paymentGateway);
            transactions.AddRange(escrowTransactions);

            // Get settlement transactions
            var settlementTransactions = await GetSettlementTransactionsAsync(fromDate, toDate, paymentGateway);
            transactions.AddRange(settlementTransactions);

            // Get commission transactions
            var commissionTransactions = await GetCommissionTransactionsAsync(fromDate, toDate, paymentGateway);
            transactions.AddRange(commissionTransactions);

            _logger.LogDebug("Found {Count} platform transactions", transactions.Count);
            return transactions.OrderBy(t => t.TransactionDate).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting platform transactions");
            throw;
        }
    }

    public async Task<PlatformTransaction?> GetTransactionAsync(string transactionId)
    {
        try
        {
            // Try to find in escrow transactions first
            var escrowTx = await _context.EscrowTransactions
                .Where(et => et.TransactionId == transactionId)
                .Select(et => new PlatformTransaction
                {
                    TransactionId = et.TransactionId,
                    GatewayTransactionId = et.PaymentReference,
                    Amount = et.Amount,
                    Status = et.Status.ToString(),
                    TransactionDate = et.CreatedAt,
                    PaymentGateway = "razorpay", // Default gateway
                    UserId = null,
                    OrderId = null,
                    Metadata = new Dictionary<string, object>
                    {
                        { "type", "escrow" },
                        { "escrow_account_id", et.EscrowAccountId.ToString() }
                    }
                })
                .FirstOrDefaultAsync();

            if (escrowTx != null) return escrowTx;

            // Try settlements
            var settlementTx = await _context.SettlementDistributions
                .Where(sd => sd.Id.ToString() == transactionId)
                .Select(sd => new PlatformTransaction
                {
                    TransactionId = sd.Id.ToString(),
                    GatewayTransactionId = null,
                    Amount = sd.Amount,
                    Status = sd.Status.ToString(),
                    TransactionDate = sd.CreatedAt,
                    PaymentGateway = "razorpay", // Default gateway
                    UserId = sd.RecipientId,
                    OrderId = null,
                    Metadata = new Dictionary<string, object>
                    {
                        { "type", "settlement" },
                        { "settlement_id", sd.SettlementId.ToString() }
                    }
                })
                .FirstOrDefaultAsync();

            if (settlementTx != null) return settlementTx;

            // Try commissions
            var commissionTx = await _context.Commissions
                .Where(c => c.Id.ToString() == transactionId)
                .Select(c => new PlatformTransaction
                {
                    TransactionId = c.Id.ToString(),
                    GatewayTransactionId = null,
                    Amount = c.Amount,
                    Status = c.Status.ToString(),
                    TransactionDate = c.CreatedAt,
                    PaymentGateway = "razorpay", // Default gateway
                    UserId = c.BrokerId,
                    OrderId = c.OrderId,
                    Metadata = new Dictionary<string, object>
                    {
                        { "type", "commission" },
                        { "broker_id", c.BrokerId.ToString() }
                    }
                })
                .FirstOrDefaultAsync();

            return commissionTx;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting platform transaction {TransactionId}", transactionId);
            throw;
        }
    }

    private async Task<List<PlatformTransaction>> GetEscrowTransactionsAsync(DateTime fromDate, DateTime toDate, string? paymentGateway)
    {
        var query = _context.EscrowTransactions
            .Where(et => et.CreatedAt >= fromDate && et.CreatedAt <= toDate);

        return await query
            .Select(et => new PlatformTransaction
            {
                TransactionId = et.TransactionId,
                GatewayTransactionId = et.PaymentReference,
                Amount = et.Amount,
                Status = et.Status.ToString(),
                TransactionDate = et.CreatedAt,
                PaymentGateway = paymentGateway ?? "razorpay",
                UserId = null,
                OrderId = null,
                Metadata = new Dictionary<string, object>
                {
                    { "type", "escrow" },
                    { "escrow_account_id", et.EscrowAccountId.ToString() }
                }
            })
            .ToListAsync();
    }

    private async Task<List<PlatformTransaction>> GetSettlementTransactionsAsync(DateTime fromDate, DateTime toDate, string? paymentGateway)
    {
        var query = _context.SettlementDistributions
            .Include(sd => sd.Settlement)
            .Where(sd => sd.CreatedAt >= fromDate && sd.CreatedAt <= toDate);

        return await query
            .Select(sd => new PlatformTransaction
            {
                TransactionId = sd.Id.ToString(),
                GatewayTransactionId = null,
                Amount = sd.Amount,
                Status = sd.Status.ToString(),
                TransactionDate = sd.CreatedAt,
                PaymentGateway = paymentGateway ?? "razorpay",
                UserId = sd.RecipientId,
                OrderId = sd.Settlement.OrderId,
                Metadata = new Dictionary<string, object>
                {
                    { "type", "settlement" },
                    { "settlement_id", sd.SettlementId.ToString() },
                    { "recipient_role", sd.RecipientRole.ToString() }
                }
            })
            .ToListAsync();
    }

    private async Task<List<PlatformTransaction>> GetCommissionTransactionsAsync(DateTime fromDate, DateTime toDate, string? paymentGateway)
    {
        var query = _context.Commissions
            .Where(c => c.CreatedAt >= fromDate && c.CreatedAt <= toDate);

        return await query
            .Select(c => new PlatformTransaction
            {
                TransactionId = c.Id.ToString(),
                GatewayTransactionId = null,
                Amount = c.Amount,
                Status = c.Status.ToString(),
                TransactionDate = c.CreatedAt,
                PaymentGateway = paymentGateway ?? "razorpay",
                UserId = c.BrokerId,
                OrderId = c.OrderId,
                Metadata = new Dictionary<string, object>
                {
                    { "type", "commission" },
                    { "broker_id", c.BrokerId.ToString() },
                    { "transport_company_id", c.TransportCompanyId.ToString() }
                }
            })
            .ToListAsync();
    }
}
