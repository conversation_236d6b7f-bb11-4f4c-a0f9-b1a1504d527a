using Shared.Domain.Common;
using MobileWorkflow.Domain.Events;

namespace MobileWorkflow.Domain.Entities;

public class SyncFile : AggregateRoot
{
    public string FileName { get; private set; }
    public string FilePath { get; private set; }
    public string FileHash { get; private set; }
    public long FileSize { get; private set; }
    public string MimeType { get; private set; }
    public Guid UserId { get; private set; }
    public string DeviceId { get; private set; }
    public int Version { get; private set; }
    public string Status { get; private set; } // Pending, Syncing, Synced, Failed, Conflict
    public DateTime CreatedAt { get; private set; }
    public DateTime ModifiedAt { get; private set; }
    public DateTime? LastSyncedAt { get; private set; }
    public string? ConflictReason { get; private set; }
    public Dictionary<string, object> FileMetadata { get; private set; }
    public Dictionary<string, object> SyncMetadata { get; private set; }
    public bool IsDeleted { get; private set; }
    public DateTime? DeletedAt { get; private set; }

    // Navigation properties
    public virtual ICollection<FileVersion> Versions { get; private set; } = new List<FileVersion>();
    public virtual ICollection<FileSyncOperation> SyncOperations { get; private set; } = new List<FileSyncOperation>();

    private SyncFile() { } // EF Core

    public SyncFile(
        string fileName,
        string filePath,
        string fileHash,
        long fileSize,
        string mimeType,
        Guid userId,
        string deviceId)
    {
        FileName = fileName ?? throw new ArgumentNullException(nameof(fileName));
        FilePath = filePath ?? throw new ArgumentNullException(nameof(filePath));
        FileHash = fileHash ?? throw new ArgumentNullException(nameof(fileHash));
        FileSize = fileSize;
        MimeType = mimeType ?? throw new ArgumentNullException(nameof(mimeType));
        UserId = userId;
        DeviceId = deviceId ?? throw new ArgumentNullException(nameof(deviceId));

        Version = 1;
        Status = "Pending";
        CreatedAt = DateTime.UtcNow;
        ModifiedAt = DateTime.UtcNow;
        IsDeleted = false;
        FileMetadata = new Dictionary<string, object>();
        SyncMetadata = new Dictionary<string, object>();

        AddDomainEvent(new SyncFileCreatedEvent(Id, FileName, FilePath, UserId, DeviceId, FileSize));
    }

    public void UpdateFile(string fileHash, long fileSize, Dictionary<string, object>? fileMetadata = null)
    {
        if (FileHash != fileHash)
        {
            // Create new version
            var newVersion = new FileVersion(
                Id,
                Version + 1,
                fileHash,
                fileSize,
                DeviceId,
                fileMetadata ?? new Dictionary<string, object>());

            Versions.Add(newVersion);

            FileHash = fileHash;
            FileSize = fileSize;
            Version++;
            ModifiedAt = DateTime.UtcNow;
            Status = "Pending";

            if (fileMetadata != null)
            {
                FileMetadata = fileMetadata;
            }

            AddDomainEvent(new SyncFileUpdatedEvent(Id, FileName, Version, FileSize, DeviceId));
        }
    }

    public void MarkAsSyncing()
    {
        if (Status != "Pending")
            throw new InvalidOperationException($"Cannot start syncing file with status: {Status}");

        Status = "Syncing";

        AddDomainEvent(new SyncFileSyncStartedEvent(Id, FileName, Version));
    }

    public void MarkAsSynced()
    {
        if (Status != "Syncing")
            throw new InvalidOperationException($"Cannot mark file as synced with status: {Status}");

        Status = "Synced";
        LastSyncedAt = DateTime.UtcNow;
        ConflictReason = null;

        AddDomainEvent(new SyncFileSyncedEvent(Id, FileName, Version, LastSyncedAt));
    }

    public void MarkAsFailed(string reason)
    {
        Status = "Failed";
        ConflictReason = reason;

        AddDomainEvent(new SyncFileSyncFailedEvent(Id, FileName, Version, reason));
    }

    public void MarkAsConflicted(string conflictReason)
    {
        Status = "Conflict";
        ConflictReason = conflictReason ?? throw new ArgumentNullException(nameof(conflictReason));

        AddDomainEvent(new SyncFileConflictEvent(Id, FileName, Version, conflictReason));
    }

    public void ResolveConflict(string resolution, string resolvedBy)
    {
        if (Status != "Conflict")
            throw new InvalidOperationException("File is not in conflict state");

        Status = "Pending";
        ConflictReason = null;

        AddSyncMetadata("conflict_resolved_at", DateTime.UtcNow);
        AddSyncMetadata("conflict_resolved_by", resolvedBy);
        AddSyncMetadata("conflict_resolution", resolution);

        AddDomainEvent(new SyncFileConflictResolvedEvent(Id, FileName, resolution, resolvedBy));
    }

    public void MarkAsDeleted()
    {
        IsDeleted = true;
        DeletedAt = DateTime.UtcNow;
        Status = "Pending"; // Will sync the deletion

        AddDomainEvent(new SyncFileDeletedEvent(Id, FileName, DeletedAt));
    }

    public void RestoreFromDeletion()
    {
        if (!IsDeleted)
            throw new InvalidOperationException("File is not deleted");

        IsDeleted = false;
        DeletedAt = null;
        Status = "Pending";

        AddDomainEvent(new SyncFileRestoredEvent(Id, FileName));
    }

    public bool NeedsSync()
    {
        return Status == "Pending" || Status == "Failed";
    }

    public bool IsInConflict()
    {
        return Status == "Conflict";
    }

    public bool IsSynced()
    {
        return Status == "Synced";
    }

    public bool IsSyncing()
    {
        return Status == "Syncing";
    }

    public TimeSpan? GetTimeSinceLastSync()
    {
        return LastSyncedAt.HasValue ? DateTime.UtcNow - LastSyncedAt.Value : null;
    }

    public FileVersion? GetLatestVersion()
    {
        return Versions.OrderByDescending(v => v.VersionNumber).FirstOrDefault();
    }

    public FileVersion? GetVersion(int versionNumber)
    {
        return Versions.FirstOrDefault(v => v.VersionNumber == versionNumber);
    }

    public void AddFileMetadata(string key, object value)
    {
        FileMetadata[key] = value;
        ModifiedAt = DateTime.UtcNow;
    }

    public void AddSyncMetadata(string key, object value)
    {
        SyncMetadata[key] = value;
    }

    public T? GetFileMetadata<T>(string key, T? defaultValue = default)
    {
        if (FileMetadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetSyncMetadata<T>(string key, T? defaultValue = default)
    {
        if (SyncMetadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }
}

public class FileVersion : AggregateRoot
{
    public Guid SyncFileId { get; private set; }
    public int VersionNumber { get; private set; }
    public string FileHash { get; private set; }
    public long FileSize { get; private set; }
    public string CreatedByDevice { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public Dictionary<string, object> VersionMetadata { get; private set; }
    public string? StorageLocation { get; private set; }
    public bool IsActive { get; private set; }

    // Navigation properties
    public virtual SyncFile SyncFile { get; private set; } = null!;

    private FileVersion() { } // EF Core

    public FileVersion(
        Guid syncFileId,
        int versionNumber,
        string fileHash,
        long fileSize,
        string createdByDevice,
        Dictionary<string, object> versionMetadata)
    {
        SyncFileId = syncFileId;
        VersionNumber = versionNumber;
        FileHash = fileHash ?? throw new ArgumentNullException(nameof(fileHash));
        FileSize = fileSize;
        CreatedByDevice = createdByDevice ?? throw new ArgumentNullException(nameof(createdByDevice));
        VersionMetadata = versionMetadata ?? throw new ArgumentNullException(nameof(versionMetadata));

        CreatedAt = DateTime.UtcNow;
        IsActive = true;

        AddDomainEvent(new FileVersionCreatedEvent(Id, SyncFileId, VersionNumber, FileHash, FileSize));
    }

    public void SetStorageLocation(string storageLocation)
    {
        StorageLocation = storageLocation;
    }

    public void Deactivate()
    {
        IsActive = false;

        AddDomainEvent(new FileVersionDeactivatedEvent(Id, SyncFileId, VersionNumber));
    }

    public void Activate()
    {
        IsActive = true;

        AddDomainEvent(new FileVersionActivatedEvent(Id, SyncFileId, VersionNumber));
    }

    public void AddMetadata(string key, object value)
    {
        VersionMetadata[key] = value;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (VersionMetadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }
}

public class FileSyncOperation : AggregateRoot
{
    public Guid SyncFileId { get; private set; }
    public string OperationType { get; private set; } // Upload, Download, Delete, Conflict
    public string Status { get; private set; } // Pending, InProgress, Completed, Failed, Cancelled
    public DateTime StartedAt { get; private set; }
    public DateTime? CompletedAt { get; private set; }
    public string SourceDevice { get; private set; }
    public string? TargetDevice { get; private set; }
    public long BytesTransferred { get; private set; }
    public long TotalBytes { get; private set; }
    public double? TransferSpeed { get; private set; } // Bytes per second
    public string? ErrorMessage { get; private set; }
    public string? ErrorCode { get; private set; }
    public Dictionary<string, object> OperationMetadata { get; private set; }
    public Dictionary<string, object> PerformanceMetrics { get; private set; }

    // Navigation properties
    public virtual SyncFile SyncFile { get; private set; } = null!;

    private FileSyncOperation() { } // EF Core

    public FileSyncOperation(
        Guid syncFileId,
        string operationType,
        string sourceDevice,
        long totalBytes,
        string? targetDevice = null)
    {
        SyncFileId = syncFileId;
        OperationType = operationType ?? throw new ArgumentNullException(nameof(operationType));
        SourceDevice = sourceDevice ?? throw new ArgumentNullException(nameof(sourceDevice));
        TargetDevice = targetDevice;
        TotalBytes = totalBytes;

        Status = "Pending";
        StartedAt = DateTime.UtcNow;
        BytesTransferred = 0;
        OperationMetadata = new Dictionary<string, object>();
        PerformanceMetrics = new Dictionary<string, object>();

        AddDomainEvent(new FileSyncOperationCreatedEvent(Id, SyncFileId, OperationType, SourceDevice, TotalBytes));
    }

    public void Start()
    {
        if (Status != "Pending")
            throw new InvalidOperationException($"Cannot start operation with status: {Status}");

        Status = "InProgress";
        StartedAt = DateTime.UtcNow;

        AddDomainEvent(new FileSyncOperationStartedEvent(Id, SyncFileId, OperationType));
    }

    public void UpdateProgress(long bytesTransferred, double? transferSpeed = null)
    {
        if (Status != "InProgress")
            throw new InvalidOperationException($"Cannot update progress for operation with status: {Status}");

        BytesTransferred = Math.Min(bytesTransferred, TotalBytes);
        TransferSpeed = transferSpeed;

        AddDomainEvent(new FileSyncOperationProgressUpdatedEvent(Id, SyncFileId, BytesTransferred, TotalBytes, GetProgressPercentage()));
    }

    public void Complete()
    {
        if (Status != "InProgress")
            throw new InvalidOperationException($"Cannot complete operation with status: {Status}");

        Status = "Completed";
        CompletedAt = DateTime.UtcNow;
        BytesTransferred = TotalBytes;

        AddDomainEvent(new FileSyncOperationCompletedEvent(Id, SyncFileId, OperationType, GetDuration()));
    }

    public void Fail(string errorMessage, string? errorCode = null)
    {
        Status = "Failed";
        CompletedAt = DateTime.UtcNow;
        ErrorMessage = errorMessage;
        ErrorCode = errorCode;

        AddDomainEvent(new FileSyncOperationFailedEvent(Id, SyncFileId, OperationType, errorMessage, errorCode));
    }

    public void Cancel()
    {
        if (Status == "Completed")
            throw new InvalidOperationException("Cannot cancel completed operation");

        Status = "Cancelled";
        CompletedAt = DateTime.UtcNow;

        AddDomainEvent(new FileSyncOperationCancelledEvent(Id, SyncFileId, OperationType));
    }

    public double GetProgressPercentage()
    {
        return TotalBytes > 0 ? (double)BytesTransferred / TotalBytes * 100 : 0;
    }

    public TimeSpan? GetDuration()
    {
        var endTime = CompletedAt ?? DateTime.UtcNow;
        return endTime - StartedAt;
    }

    public double? GetAverageTransferSpeed()
    {
        var duration = GetDuration();
        return duration?.TotalSeconds > 0 ? BytesTransferred / duration.Value.TotalSeconds : null;
    }

    public bool IsCompleted()
    {
        return Status == "Completed";
    }

    public bool IsFailed()
    {
        return Status == "Failed";
    }

    public bool IsInProgress()
    {
        return Status == "InProgress";
    }

    public bool IsCancelled()
    {
        return Status == "Cancelled";
    }

    public void AddMetadata(string key, object value)
    {
        OperationMetadata[key] = value;
    }

    public void AddPerformanceMetric(string key, object value)
    {
        PerformanceMetrics[key] = value;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (OperationMetadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetPerformanceMetric<T>(string key, T? defaultValue = default)
    {
        if (PerformanceMetrics.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }
}


