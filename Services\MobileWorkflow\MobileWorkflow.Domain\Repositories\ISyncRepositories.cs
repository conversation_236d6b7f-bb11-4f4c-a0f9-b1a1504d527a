using MobileWorkflow.Domain.Entities;
using Shared.Domain.Common;
using MobileWorkflow.Domain.Enums;
using MobileWorkflow.Domain.Events;

namespace MobileWorkflow.Domain.Repositories;

public interface ISyncOperationRepository
{
    void Add(SyncOperation syncOperation);
    Task<SyncOperation?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<SyncOperation?> GetActiveSyncOperationAsync(Guid userId, Guid deviceId, CancellationToken cancellationToken = default);
    Task<List<SyncOperation>> GetByUserIdAsync(Guid userId, DateTime? fromDate = null, CancellationToken cancellationToken = default);
    Task<List<SyncOperation>> GetByStatusAsync(SyncStatus status, CancellationToken cancellationToken = default);
    Task<List<SyncOperation>> GetPendingRetriesAsync(CancellationToken cancellationToken = default);
    Task<List<SyncOperation>> GetByDeviceIdAsync(Guid deviceId, CancellationToken cancellationToken = default);
    Task<int> GetActiveOperationsCountAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<SyncOperation?> GetLatestByUserIdAsync(Guid userId, CancellationToken cancellationToken = default);
    void Update(SyncOperation syncOperation);
    void Remove(SyncOperation syncOperation);
    Task SaveChangesAsync(CancellationToken cancellationToken = default);
}

public interface ISyncItemRepository
{
    void Add(SyncItem syncItem);
    Task<SyncItem?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<List<SyncItem>> GetBySyncOperationIdAsync(Guid syncOperationId, CancellationToken cancellationToken = default);
    Task<List<SyncItem>> GetPendingByUserIdAsync(Guid userId, int priority = 0, CancellationToken cancellationToken = default);
    Task<List<SyncItem>> GetByStatusAsync(SyncStatus status, CancellationToken cancellationToken = default);
    Task<List<SyncItem>> GetConflictItemsAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<List<SyncItem>> GetFailedItemsAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<List<SyncItem>> GetReadyForRetryAsync(CancellationToken cancellationToken = default);
    Task<SyncItem?> GetByEntityAsync(string entityType, string entityId, CancellationToken cancellationToken = default);
    Task<List<SyncItem>> GetByEntityTypeAsync(string entityType, CancellationToken cancellationToken = default);
    Task<int> GetPendingCountByUserIdAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<long> GetPendingDataSizeByUserIdAsync(Guid userId, CancellationToken cancellationToken = default);
    void Update(SyncItem syncItem);
    void Remove(SyncItem syncItem);
    Task SaveChangesAsync(CancellationToken cancellationToken = default);
}

public interface IConflictResolutionRepository
{
    void Add(ConflictResolution conflictResolution);
    Task<ConflictResolution?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<List<ConflictResolution>> GetBySyncOperationIdAsync(Guid syncOperationId, CancellationToken cancellationToken = default);
    Task<List<ConflictResolution>> GetPendingConflictsByUserIdAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<List<ConflictResolution>> GetByEntityAsync(string entityType, string entityId, CancellationToken cancellationToken = default);
    Task<List<ConflictResolution>> GetByStatusAsync(string status, CancellationToken cancellationToken = default);
    Task<List<ConflictResolution>> GetAutoResolvableConflictsAsync(CancellationToken cancellationToken = default);
    Task<List<ConflictResolution>> GetByConflictTypeAsync(string conflictType, CancellationToken cancellationToken = default);
    Task<int> GetPendingCountByUserIdAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<ConflictResolution?> GetLatestByEntityAsync(string entityType, string entityId, CancellationToken cancellationToken = default);
    void Update(ConflictResolution conflictResolution);
    void Remove(ConflictResolution conflictResolution);
    Task SaveChangesAsync(CancellationToken cancellationToken = default);
}

public interface IOfflineDataRepository
{
    void Add(OfflineData offlineData);
    Task<OfflineData?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<List<OfflineData>> GetByUserIdAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<List<OfflineData>> GetUnsyncedDataAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<List<OfflineData>> GetByDataTypeAsync(string dataType, CancellationToken cancellationToken = default);
    Task<List<OfflineData>> GetByPriorityAsync(int priority, CancellationToken cancellationToken = default);
    Task<List<OfflineData>> GetFailedSyncDataAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<List<OfflineData>> GetReadyForRetryAsync(CancellationToken cancellationToken = default);
    Task<int> GetUnsyncedCountByUserIdAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<long> GetUnsyncedDataSizeByUserIdAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<List<OfflineData>> GetByMobileSessionIdAsync(Guid mobileSessionId, CancellationToken cancellationToken = default);
    Task<List<OfflineData>> GetOldUnsyncedDataAsync(TimeSpan maxAge, CancellationToken cancellationToken = default);
    void Update(OfflineData offlineData);
    void Remove(OfflineData offlineData);
    Task SaveChangesAsync(CancellationToken cancellationToken = default);
}

public interface ISyncPolicyRepository
{
    void Add(SyncPolicy syncPolicy);
    Task<SyncPolicy?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<List<SyncPolicy>> GetActiveAsync(CancellationToken cancellationToken = default);
    Task<SyncPolicy?> GetByNameAsync(string name, CancellationToken cancellationToken = default);
    Task<List<SyncPolicy>> GetByEntityTypeAsync(string entityType, CancellationToken cancellationToken = default);
    Task<SyncPolicy?> GetDefaultPolicyAsync(CancellationToken cancellationToken = default);
    void Update(SyncPolicy syncPolicy);
    void Remove(SyncPolicy syncPolicy);
    Task SaveChangesAsync(CancellationToken cancellationToken = default);
}

// Additional entity for sync policies
public class SyncPolicy : AggregateRoot
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public Dictionary<string, object> Rules { get; private set; }
    public bool IsActive { get; private set; }
    public bool IsDefault { get; private set; }
    public string? EntityType { get; private set; }
    public int Priority { get; private set; }
    public Dictionary<string, object> ConflictResolutionRules { get; private set; }
    public Dictionary<string, object> RetryPolicies { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    private SyncPolicy() { } // EF Core

    public SyncPolicy(
        string name,
        string description,
        Dictionary<string, object> rules,
        string? entityType = null,
        int priority = 1)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        Rules = rules ?? throw new ArgumentNullException(nameof(rules));
        EntityType = entityType;
        Priority = priority;
        IsActive = true;
        IsDefault = false;
        ConflictResolutionRules = new Dictionary<string, object>();
        RetryPolicies = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();

        AddDomainEvent(new SyncPolicyCreatedEvent(Id, Name, EntityType, Priority));
    }

    public void Activate()
    {
        IsActive = true;
        AddDomainEvent(new SyncPolicyActivatedEvent(Id, Name));
    }

    public void Deactivate()
    {
        IsActive = false;
        AddDomainEvent(new SyncPolicyDeactivatedEvent(Id, Name));
    }

    public void SetAsDefault()
    {
        IsDefault = true;
        AddDomainEvent(new SyncPolicySetAsDefaultEvent(Id, Name));
    }

    public void UpdateRules(Dictionary<string, object> newRules)
    {
        Rules = newRules ?? throw new ArgumentNullException(nameof(newRules));
        AddDomainEvent(new SyncPolicyRulesUpdatedEvent(Id, Name));
    }

    public void UpdateConflictResolutionRules(Dictionary<string, object> conflictRules)
    {
        ConflictResolutionRules = conflictRules ?? throw new ArgumentNullException(nameof(conflictRules));
        AddDomainEvent(new SyncPolicyConflictRulesUpdatedEvent(Id, Name));
    }

    public void UpdateRetryPolicies(Dictionary<string, object> retryPolicies)
    {
        RetryPolicies = retryPolicies ?? throw new ArgumentNullException(nameof(retryPolicies));
        AddDomainEvent(new SyncPolicyRetryPoliciesUpdatedEvent(Id, Name));
    }

    public bool AppliesTo(string entityType)
    {
        return EntityType == null || EntityType.Equals(entityType, StringComparison.OrdinalIgnoreCase);
    }

    public T GetRule<T>(string ruleName, T defaultValue = default!)
    {
        if (Rules.TryGetValue(ruleName, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }
}

// UI Component Repository Interfaces
public interface IUIComponentRepository
{
    void Add(UIComponent component);
    Task<UIComponent?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<UIComponent?> GetByNameAsync(string name, CancellationToken cancellationToken = default);
    Task<List<UIComponent>> GetActiveComponentsAsync(string? platform = null, string? category = null, CancellationToken cancellationToken = default);
    Task<List<UIComponent>> GetByCategoryAsync(string category, CancellationToken cancellationToken = default);
    Task<List<UIComponent>> GetByTypeAsync(string componentType, CancellationToken cancellationToken = default);
    Task<List<UIComponent>> GetByPlatformAsync(string platform, CancellationToken cancellationToken = default);
    Task<List<string>> GetSupportedPlatformsAsync(CancellationToken cancellationToken = default);
    Task<List<string>> GetCategoriesAsync(CancellationToken cancellationToken = default);
    Task<List<UIComponent>> SearchComponentsAsync(string searchTerm, string? platform = null, CancellationToken cancellationToken = default);
    void Update(UIComponent component);
    void Remove(UIComponent component);
    Task SaveChangesAsync(CancellationToken cancellationToken = default);
}

public interface IComponentThemeRepository
{
    void Add(ComponentTheme theme);
    Task<ComponentTheme?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<ComponentTheme?> GetByNameAsync(string name, CancellationToken cancellationToken = default);
    Task<List<ComponentTheme>> GetActiveThemesAsync(string? platform = null, CancellationToken cancellationToken = default);
    Task<List<ComponentTheme>> GetDefaultThemesAsync(CancellationToken cancellationToken = default);
    Task<ComponentTheme?> GetDefaultThemeAsync(string? platform = null, CancellationToken cancellationToken = default);
    Task<List<ComponentTheme>> GetByPlatformAsync(string platform, CancellationToken cancellationToken = default);
    void Update(ComponentTheme theme);
    void Remove(ComponentTheme theme);
    Task SaveChangesAsync(CancellationToken cancellationToken = default);
}

public interface IComponentLibraryRepository
{
    void Add(ComponentLibrary library);
    Task<ComponentLibrary?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<ComponentLibrary?> GetByNameAsync(string name, CancellationToken cancellationToken = default);
    Task<List<ComponentLibrary>> GetActiveLibrariesAsync(CancellationToken cancellationToken = default);
    Task<List<ComponentLibrary>> GetByPlatformAsync(string platform, CancellationToken cancellationToken = default);
    void Update(ComponentLibrary library);
    void Remove(ComponentLibrary library);
    Task SaveChangesAsync(CancellationToken cancellationToken = default);
}

public interface IComponentUsageRepository
{
    void Add(ComponentUsage usage);
    Task<ComponentUsage?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<List<ComponentUsage>> GetByComponentIdAsync(Guid componentId, DateTime? fromDate = null, CancellationToken cancellationToken = default);
    Task<List<ComponentUsage>> GetByUserIdAsync(Guid userId, DateTime? fromDate = null, CancellationToken cancellationToken = default);
    Task<List<ComponentUsage>> GetByPlatformAsync(string platform, DateTime? fromDate = null, CancellationToken cancellationToken = default);
    Task<List<ComponentUsage>> GetFailedUsagesAsync(DateTime? fromDate = null, CancellationToken cancellationToken = default);
    void Update(ComponentUsage usage);
    void Remove(ComponentUsage usage);
    Task SaveChangesAsync(CancellationToken cancellationToken = default);
}


