﻿using CommunicationNotification.Domain.Enums;
using Shared.Domain.Common;
using Shared.Domain.ValueObjects;

namespace CommunicationNotification.Domain.ValueObjects;

/// <summary>
/// Rollout configuration value object
/// </summary>
public class RolloutConfiguration : ValueObject
{
    public decimal Percentage { get; private set; }
    public RolloutStrategy Strategy { get; private set; }
    public List<Guid> WhitelistedUsers { get; private set; }
    public List<Guid> BlacklistedUsers { get; private set; }
    public List<string> WhitelistedGroups { get; private set; }
    public List<string> BlacklistedGroups { get; private set; }
    public Dictionary<string, object> CustomRules { get; private set; }

    private RolloutConfiguration()
    {
        WhitelistedUsers = new List<Guid>();
        BlacklistedUsers = new List<Guid>();
        WhitelistedGroups = new List<string>();
        BlacklistedGroups = new List<string>();
        CustomRules = new Dictionary<string, object>();
    }

    public RolloutConfiguration(
        decimal percentage,
        RolloutStrategy strategy = RolloutStrategy.Percentage,
        List<Guid>? whitelistedUsers = null,
        List<Guid>? blacklistedUsers = null,
        List<string>? whitelistedGroups = null,
        List<string>? blacklistedGroups = null,
        Dictionary<string, object>? customRules = null)
    {
        if (percentage < 0 || percentage > 100)
            throw new ArgumentException("Percentage must be between 0 and 100", nameof(percentage));

        Percentage = percentage;
        Strategy = strategy;
        WhitelistedUsers = whitelistedUsers ?? new List<Guid>();
        BlacklistedUsers = blacklistedUsers ?? new List<Guid>();
        WhitelistedGroups = whitelistedGroups ?? new List<string>();
        BlacklistedGroups = blacklistedGroups ?? new List<string>();
        CustomRules = customRules ?? new Dictionary<string, object>();
    }

    public static RolloutConfiguration Default()
    {
        return new RolloutConfiguration(0);
    }

    public static RolloutConfiguration FullRollout()
    {
        return new RolloutConfiguration(100);
    }

    public static RolloutConfiguration WhitelistOnly(List<Guid> whitelistedUsers)
    {
        return new RolloutConfiguration(0, RolloutStrategy.Whitelist, whitelistedUsers);
    }

    public bool IsUserIncluded(Guid userId)
    {
        // Check blacklist first
        if (BlacklistedUsers.Contains(userId))
            return false;

        // Check whitelist
        if (WhitelistedUsers.Contains(userId))
            return true;

        // Apply strategy
        return Strategy switch
        {
            RolloutStrategy.Percentage => IsUserInPercentage(userId),
            RolloutStrategy.Whitelist => WhitelistedUsers.Contains(userId),
            RolloutStrategy.All => true,
            RolloutStrategy.None => false,
            _ => false
        };
    }

    private bool IsUserInPercentage(Guid userId)
    {
        if (Percentage == 0) return false;
        if (Percentage == 100) return true;

        var hash = Math.Abs(userId.GetHashCode()) % 10000;
        var userPercentage = hash / 100m;
        return userPercentage < Percentage;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Percentage;
        yield return Strategy;

        foreach (var user in WhitelistedUsers.OrderBy(x => x))
            yield return user;

        foreach (var user in BlacklistedUsers.OrderBy(x => x))
            yield return user;

        foreach (var group in WhitelistedGroups.OrderBy(x => x))
            yield return group;

        foreach (var group in BlacklistedGroups.OrderBy(x => x))
            yield return group;

        foreach (var rule in CustomRules.OrderBy(x => x.Key))
        {
            yield return rule.Key;
            yield return rule.Value;
        }
    }
}

/// <summary>
/// Targeting rules value object
/// </summary>
public class TargetingRules : ValueObject
{
    public List<TargetingRule> Rules { get; private set; }
    public TargetingLogic Logic { get; private set; }

    private TargetingRules()
    {
        Rules = new List<TargetingRule>();
    }

    public TargetingRules(List<TargetingRule> rules, TargetingLogic logic = TargetingLogic.And)
    {
        Rules = rules ?? new List<TargetingRule>();
        Logic = logic;
    }

    public static TargetingRules Empty()
    {
        return new TargetingRules();
    }

    public bool IsUserTargeted(Guid userId, Dictionary<string, object>? context = null)
    {
        if (!Rules.Any())
            return true; // No rules means everyone is targeted

        var results = Rules.Select(rule => rule.Evaluate(userId, context)).ToList();

        return Logic switch
        {
            TargetingLogic.And => results.All(r => r),
            TargetingLogic.Or => results.Any(r => r),
            _ => true
        };
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Logic;

        foreach (var rule in Rules)
            yield return rule;
    }
}

/// <summary>
/// Individual targeting rule
/// </summary>
public class TargetingRule : ValueObject
{
    public string Property { get; private set; }
    public TargetingOperator Operator { get; private set; }
    public object Value { get; private set; }

    private TargetingRule()
    {
        Property = string.Empty;
        Value = new object();
    }

    public TargetingRule(string property, TargetingOperator @operator, object value)
    {
        if (string.IsNullOrWhiteSpace(property))
            throw new ArgumentException("Property cannot be empty", nameof(property));

        Property = property;
        Operator = @operator;
        Value = value ?? throw new ArgumentNullException(nameof(value));
    }

    public bool Evaluate(Guid userId, Dictionary<string, object>? context = null)
    {
        var actualValue = GetActualValue(userId, context);
        if (actualValue == null)
            return false;

        return Operator switch
        {
            TargetingOperator.Equals => actualValue.Equals(Value),
            TargetingOperator.NotEquals => !actualValue.Equals(Value),
            TargetingOperator.Contains => actualValue.ToString()?.Contains(Value.ToString() ?? string.Empty) == true,
            TargetingOperator.NotContains => actualValue.ToString()?.Contains(Value.ToString() ?? string.Empty) == false,
            TargetingOperator.GreaterThan => CompareValues(actualValue, Value) > 0,
            TargetingOperator.LessThan => CompareValues(actualValue, Value) < 0,
            TargetingOperator.GreaterThanOrEqual => CompareValues(actualValue, Value) >= 0,
            TargetingOperator.LessThanOrEqual => CompareValues(actualValue, Value) <= 0,
            TargetingOperator.In => IsValueInList(actualValue, Value),
            TargetingOperator.NotIn => !IsValueInList(actualValue, Value),
            _ => false
        };
    }

    private object? GetActualValue(Guid userId, Dictionary<string, object>? context)
    {
        // Handle special properties
        if (Property.Equals("userId", StringComparison.OrdinalIgnoreCase))
            return userId;

        if (Property.Equals("userIdString", StringComparison.OrdinalIgnoreCase))
            return userId.ToString();

        // Get from context
        if (context != null && context.TryGetValue(Property, out var contextValue))
            return contextValue;

        return null;
    }

    private int CompareValues(object actual, object expected)
    {
        if (actual is IComparable actualComparable && expected is IComparable expectedComparable)
        {
            return actualComparable.CompareTo(expectedComparable);
        }

        return string.Compare(actual.ToString(), expected.ToString(), StringComparison.OrdinalIgnoreCase);
    }

    private bool IsValueInList(object actual, object expected)
    {
        if (expected is IEnumerable<object> list)
        {
            return list.Any(item => item.Equals(actual));
        }

        if (expected is string stringList)
        {
            var items = stringList.Split(',', StringSplitOptions.RemoveEmptyEntries);
            return items.Any(item => item.Trim().Equals(actual.ToString(), StringComparison.OrdinalIgnoreCase));
        }

        return false;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Property;
        yield return Operator;
        yield return Value;
    }
}

/// <summary>
/// Feature flag metrics value object
/// </summary>
public class FeatureFlagMetrics : ValueObject
{
    public int TotalUsages { get; private set; }
    public int UniqueUsers { get; private set; }
    public Dictionary<string, int> VariantUsages { get; private set; }
    public Dictionary<string, int> VariantUniqueUsers { get; private set; }
    public DateTime LastUsed { get; private set; }
    public Dictionary<string, decimal> ConversionRates { get; private set; }

    private FeatureFlagMetrics()
    {
        VariantUsages = new Dictionary<string, int>();
        VariantUniqueUsers = new Dictionary<string, int>();
        ConversionRates = new Dictionary<string, decimal>();
    }

    public FeatureFlagMetrics(
        int totalUsages,
        int uniqueUsers,
        Dictionary<string, int>? variantUsages = null,
        Dictionary<string, int>? variantUniqueUsers = null,
        DateTime? lastUsed = null,
        Dictionary<string, decimal>? conversionRates = null)
    {
        TotalUsages = totalUsages;
        UniqueUsers = uniqueUsers;
        VariantUsages = variantUsages ?? new Dictionary<string, int>();
        VariantUniqueUsers = variantUniqueUsers ?? new Dictionary<string, int>();
        LastUsed = lastUsed ?? DateTime.MinValue;
        ConversionRates = conversionRates ?? new Dictionary<string, decimal>();
    }

    public static FeatureFlagMetrics Empty()
    {
        return new FeatureFlagMetrics(0, 0);
    }

    public void RecordUsage(string? variant = null)
    {
        TotalUsages++;
        LastUsed = DateTime.UtcNow;

        if (!string.IsNullOrEmpty(variant))
        {
            VariantUsages[variant] = VariantUsages.GetValueOrDefault(variant, 0) + 1;
        }
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return TotalUsages;
        yield return UniqueUsers;
        yield return LastUsed;

        foreach (var usage in VariantUsages.OrderBy(x => x.Key))
        {
            yield return usage.Key;
            yield return usage.Value;
        }

        foreach (var user in VariantUniqueUsers.OrderBy(x => x.Key))
        {
            yield return user.Key;
            yield return user.Value;
        }

        foreach (var rate in ConversionRates.OrderBy(x => x.Key))
        {
            yield return rate.Key;
            yield return rate.Value;
        }
    }
}

/// <summary>
/// Rollout strategy enumeration
/// </summary>
public enum RolloutStrategy
{
    Percentage = 1,
    Whitelist = 2,
    All = 3,
    None = 4
}

/// <summary>
/// Targeting logic enumeration
/// </summary>
public enum TargetingLogic
{
    And = 1,
    Or = 2
}

/// <summary>
/// Targeting operator enumeration
/// </summary>
public enum TargetingOperator
{
    Equals = 1,
    NotEquals = 2,
    Contains = 3,
    NotContains = 4,
    GreaterThan = 5,
    LessThan = 6,
    GreaterThanOrEqual = 7,
    LessThanOrEqual = 8,
    In = 9,
    NotIn = 10
}


