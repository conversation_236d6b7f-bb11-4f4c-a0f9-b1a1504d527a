using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using FinancialPayment.Domain.Entities;

namespace FinancialPayment.Infrastructure.Data.Configurations;

public class EscrowMilestoneConfiguration : IEntityTypeConfiguration<EscrowMilestone>
{
    public void Configure(EntityTypeBuilder<EscrowMilestone> builder)
    {
        builder.ToTable("escrow_milestones");

        builder.<PERSON><PERSON>ey(e => e.Id);

        builder.Property(e => e.Id)
            .HasColumnName("id")
            .ValueGeneratedNever();

        builder.Property(e => e.EscrowAccountId)
            .HasColumnName("escrow_account_id")
            .IsRequired();

        builder.Property(e => e.Description)
            .HasColumnName("description")
            .HasMaxLength(500)
            .IsRequired();

        // Money value object
        builder.OwnsOne(e => e.Amount, money =>
        {
            money.Property(m => m.Amount)
                .HasColumnName("amount")
                .HasColumnType("decimal(18,2)")
                .IsRequired();

            money.Property(m => m.Currency)
                .HasColumnName("currency")
                .HasMaxLength(3)
                .IsRequired();
        });

        builder.Property(e => e.DueDate)
            .HasColumnName("due_date");

        builder.Property(e => e.Status)
            .HasColumnName("status")
            .HasConversion<int>()
            .IsRequired();

        builder.Property(e => e.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(e => e.CompletedAt)
            .HasColumnName("completed_at");

        builder.Property(e => e.CompletionNotes)
            .HasColumnName("completion_notes")
            .HasMaxLength(1000);

        // Indexes
        builder.HasIndex(e => e.EscrowAccountId)
            .HasDatabaseName("ix_escrow_milestones_escrow_account_id");

        builder.HasIndex(e => e.Status)
            .HasDatabaseName("ix_escrow_milestones_status");

        builder.HasIndex(e => e.DueDate)
            .HasDatabaseName("ix_escrow_milestones_due_date");

        builder.HasIndex(e => e.CreatedAt)
            .HasDatabaseName("ix_escrow_milestones_created_at");
    }
}
