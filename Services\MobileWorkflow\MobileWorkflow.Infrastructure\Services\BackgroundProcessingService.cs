using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using MobileWorkflow.Domain.Entities;
using MobileWorkflow.Domain.Repositories;
using MobileWorkflow.Application.DTOs;
using System.Text.Json;

namespace MobileWorkflow.Infrastructure.Services;

public interface IBackgroundProcessingService
{
    Task<BackgroundJobDto> EnqueueJobAsync(EnqueueJobRequest request, CancellationToken cancellationToken = default);
    Task<BackgroundJobDto> ScheduleJobAsync(ScheduleJobRequest request, CancellationToken cancellationToken = default);
    Task<BackgroundJobDto?> GetJobAsync(Guid jobId, CancellationToken cancellationToken = default);
    Task<List<BackgroundJobDto>> GetJobsAsync(string? queue = null, string? status = null, int? limit = null, CancellationToken cancellationToken = default);
    Task<bool> CancelJobAsync(Guid jobId, CancellationToken cancellationToken = default);
    Task<bool> RetryJobAsync(Guid jobId, CancellationToken cancellationToken = default);
    Task<JobQueueDto> CreateQueueAsync(CreateJobQueueRequest request, CancellationToken cancellationToken = default);
    Task<List<JobQueueDto>> GetQueuesAsync(CancellationToken cancellationToken = default);
    Task<bool> PauseQueueAsync(string queueName, CancellationToken cancellationToken = default);
    Task<bool> ResumeQueueAsync(string queueName, CancellationToken cancellationToken = default);
    Task<JobWorkerDto> RegisterWorkerAsync(RegisterWorkerRequest request, CancellationToken cancellationToken = default);
    Task<bool> UpdateWorkerHeartbeatAsync(string workerId, CancellationToken cancellationToken = default);
    Task<List<JobWorkerDto>> GetWorkersAsync(CancellationToken cancellationToken = default);
    Task<JobProcessingStatsDto> GetProcessingStatsAsync(CancellationToken cancellationToken = default);
    Task<BackgroundJobDto?> DequeueJobAsync(string workerId, List<string> queues, CancellationToken cancellationToken = default);
    Task<bool> UpdateJobProgressAsync(Guid jobId, UpdateJobProgressRequest request, CancellationToken cancellationToken = default);
    Task<bool> CompleteJobAsync(Guid jobId, CompleteJobRequest request, CancellationToken cancellationToken = default);
    Task<bool> FailJobAsync(Guid jobId, FailJobRequest request, CancellationToken cancellationToken = default);
    Task ProcessScheduledJobsAsync(CancellationToken cancellationToken = default);
    Task ProcessRetryJobsAsync(CancellationToken cancellationToken = default);
    Task CleanupCompletedJobsAsync(CancellationToken cancellationToken = default);
}

public class BackgroundProcessingService : IBackgroundProcessingService
{
    private readonly IBackgroundJobRepository _jobRepository;
    private readonly IJobQueueRepository _queueRepository;
    private readonly IJobWorkerRepository _workerRepository;
    private readonly IJobExecutionRepository _executionRepository;
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<BackgroundProcessingService> _logger;
    private readonly IMemoryCache _cache;
    private readonly IConfiguration _configuration;

    public BackgroundProcessingService(
        IBackgroundJobRepository jobRepository,
        IJobQueueRepository queueRepository,
        IJobWorkerRepository workerRepository,
        IJobExecutionRepository executionRepository,
        IServiceProvider serviceProvider,
        ILogger<BackgroundProcessingService> logger,
        IMemoryCache cache,
        IConfiguration configuration)
    {
        _jobRepository = jobRepository;
        _queueRepository = queueRepository;
        _workerRepository = workerRepository;
        _executionRepository = executionRepository;
        _serviceProvider = serviceProvider;
        _logger = logger;
        _cache = cache;
        _configuration = configuration;
    }

    public async Task<BackgroundJobDto> EnqueueJobAsync(EnqueueJobRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Enqueuing job {JobType} in queue {Queue}", request.JobType, request.Queue);

            var job = new BackgroundJob(
                request.JobType,
                request.JobName,
                request.Queue,
                request.Parameters,
                request.Priority,
                null,
                request.CreatedBy);

            if (request.ParentJobId.HasValue)
            {
                job.SetParentJob(request.ParentJobId.Value.ToString());
            }

            _jobRepository.Add(job);
            await _jobRepository.SaveChangesAsync(cancellationToken);

            // Update queue statistics
            await UpdateQueueStatisticsAsync(request.Queue, "total_jobs", 1, cancellationToken);
            await UpdateQueueStatisticsAsync(request.Queue, "pending_jobs", 1, cancellationToken);

            _logger.LogInformation("Job {JobId} enqueued successfully", job.Id);

            return MapToBackgroundJobDto(job);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error enqueuing job {JobType}", request.JobType);
            throw;
        }
    }

    public async Task<BackgroundJobDto> ScheduleJobAsync(ScheduleJobRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Scheduling job {JobType} for {ScheduledAt}", request.JobType, request.ScheduledAt);

            var job = new BackgroundJob(
                request.JobType,
                request.JobName,
                request.Queue,
                request.Parameters,
                request.Priority,
                request.ScheduledAt,
                request.CreatedBy);

            if (request.ParentJobId.HasValue)
            {
                job.SetParentJob(request.ParentJobId.Value.ToString());
            }

            _jobRepository.Add(job);
            await _jobRepository.SaveChangesAsync(cancellationToken);

            // Update queue statistics
            await UpdateQueueStatisticsAsync(request.Queue, "total_jobs", 1, cancellationToken);

            _logger.LogInformation("Job {JobId} scheduled successfully for {ScheduledAt}", job.Id, request.ScheduledAt);

            return MapToBackgroundJobDto(job);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error scheduling job {JobType}", request.JobType);
            throw;
        }
    }

    public async Task<BackgroundJobDto?> GetJobAsync(Guid jobId, CancellationToken cancellationToken = default)
    {
        try
        {
            var job = await _jobRepository.GetByIdAsync(jobId, cancellationToken);
            if (job == null)
            {
                return null;
            }

            return MapToBackgroundJobDto(job);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting job {JobId}", jobId);
            return null;
        }
    }

    public async Task<List<BackgroundJobDto>> GetJobsAsync(string? queue = null, string? status = null, int? limit = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var jobs = await _jobRepository.GetJobsAsync(queue, status, limit, cancellationToken);
            return jobs.Select(MapToBackgroundJobDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting jobs");
            return new List<BackgroundJobDto>();
        }
    }

    public async Task<bool> CancelJobAsync(Guid jobId, CancellationToken cancellationToken = default)
    {
        try
        {
            var job = await _jobRepository.GetByIdAsync(jobId, cancellationToken);
            if (job == null)
            {
                return false;
            }

            job.Cancel();
            await _jobRepository.SaveChangesAsync(cancellationToken);

            // Update queue statistics
            await UpdateQueueStatisticsAsync(job.Queue, "cancelled_jobs", 1, cancellationToken);
            await UpdateQueueStatisticsAsync(job.Queue, "pending_jobs", -1, cancellationToken);

            _logger.LogInformation("Job {JobId} cancelled successfully", jobId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling job {JobId}", jobId);
            return false;
        }
    }

    public async Task<bool> RetryJobAsync(Guid jobId, CancellationToken cancellationToken = default)
    {
        try
        {
            var job = await _jobRepository.GetByIdAsync(jobId, cancellationToken);
            if (job == null || !job.CanRetry())
            {
                return false;
            }

            var retryDelay = CalculateRetryDelay(job.RetryCount);
            job.Retry(retryDelay);
            await _jobRepository.SaveChangesAsync(cancellationToken);

            // Update queue statistics
            await UpdateQueueStatisticsAsync(job.Queue, "pending_jobs", 1, cancellationToken);

            _logger.LogInformation("Job {JobId} scheduled for retry", jobId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrying job {JobId}", jobId);
            return false;
        }
    }

    public async Task<JobQueueDto> CreateQueueAsync(CreateJobQueueRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating job queue {Name}", request.Name);

            var queue = new JobQueue(
                request.Name,
                request.Description,
                request.MaxConcurrency,
                request.Priority,
                request.CreatedBy);

            if (request.Configuration != null)
            {
                queue.UpdateConfiguration(request.Configuration);
            }

            _queueRepository.Add(queue);
            await _queueRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Job queue {QueueId} created successfully", queue.Id);

            return MapToJobQueueDto(queue);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating job queue {Name}", request.Name);
            throw;
        }
    }

    public async Task<List<JobQueueDto>> GetQueuesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var queues = await _queueRepository.GetAllAsync(cancellationToken);
            return queues.Select(MapToJobQueueDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting job queues");
            return new List<JobQueueDto>();
        }
    }

    public async Task<bool> PauseQueueAsync(string queueName, CancellationToken cancellationToken = default)
    {
        try
        {
            var queue = await _queueRepository.GetByNameAsync(queueName, cancellationToken);
            if (queue == null)
            {
                return false;
            }

            queue.Pause();
            await _queueRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Queue {QueueName} paused", queueName);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error pausing queue {QueueName}", queueName);
            return false;
        }
    }

    public async Task<bool> ResumeQueueAsync(string queueName, CancellationToken cancellationToken = default)
    {
        try
        {
            var queue = await _queueRepository.GetByNameAsync(queueName, cancellationToken);
            if (queue == null)
            {
                return false;
            }

            queue.Resume();
            await _queueRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Queue {QueueName} resumed", queueName);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resuming queue {QueueName}", queueName);
            return false;
        }
    }

    public async Task<JobWorkerDto> RegisterWorkerAsync(RegisterWorkerRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Registering worker {WorkerId}", request.WorkerId);

            // Check if worker already exists
            var existingWorker = await _workerRepository.GetByWorkerIdAsync(request.WorkerId, cancellationToken);
            if (existingWorker != null)
            {
                existingWorker.UpdateHeartbeat();
                await _workerRepository.SaveChangesAsync(cancellationToken);
                return MapToJobWorkerDto(existingWorker);
            }

            var worker = new JobWorker(
                request.WorkerId,
                request.WorkerName,
                request.MachineName,
                request.ProcessId,
                request.SupportedQueues,
                request.SupportedJobTypes,
                request.MaxConcurrentJobs);

            _workerRepository.Add(worker);
            await _workerRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Worker {WorkerId} registered successfully", request.WorkerId);

            return MapToJobWorkerDto(worker);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error registering worker {WorkerId}", request.WorkerId);
            throw;
        }
    }

    public async Task<bool> UpdateWorkerHeartbeatAsync(string workerId, CancellationToken cancellationToken = default)
    {
        try
        {
            var worker = await _workerRepository.GetByWorkerIdAsync(workerId, cancellationToken);
            if (worker == null)
            {
                return false;
            }

            worker.UpdateHeartbeat();
            await _workerRepository.SaveChangesAsync(cancellationToken);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating worker heartbeat {WorkerId}", workerId);
            return false;
        }
    }

    public async Task<List<JobWorkerDto>> GetWorkersAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var workers = await _workerRepository.GetAllAsync(cancellationToken);
            return workers.Select(MapToJobWorkerDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting workers");
            return new List<JobWorkerDto>();
        }
    }

    public async Task<JobProcessingStatsDto> GetProcessingStatsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var jobs = await _jobRepository.GetAllAsync(cancellationToken);
            var workers = await _workerRepository.GetAllAsync(cancellationToken);
            var queues = await _queueRepository.GetAllAsync(cancellationToken);

            var stats = new JobProcessingStatsDto
            {
                TotalJobs = jobs.Count,
                PendingJobs = jobs.Count(j => j.Status == "Pending"),
                RunningJobs = jobs.Count(j => j.Status == "Running"),
                CompletedJobs = jobs.Count(j => j.Status == "Completed"),
                FailedJobs = jobs.Count(j => j.Status == "Failed"),
                CancelledJobs = jobs.Count(j => j.Status == "Cancelled"),
                ScheduledJobs = jobs.Count(j => j.Status == "Scheduled"),
                TotalWorkers = workers.Count,
                ActiveWorkers = workers.Count(w => w.IsOnline() && w.IsAvailable()),
                BusyWorkers = workers.Count(w => w.Status == "Busy"),
                OfflineWorkers = workers.Count(w => !w.IsOnline()),
                TotalQueues = queues.Count,
                ActiveQueues = queues.Count(q => q.IsActive && !q.IsPaused),
                PausedQueues = queues.Count(q => q.IsPaused),
                AverageExecutionTime = jobs.Where(j => j.Duration.HasValue).DefaultIfEmpty().Average(j => j?.Duration?.TotalMilliseconds ?? 0),
                JobsByQueue = jobs.GroupBy(j => j.Queue).ToDictionary(g => g.Key, g => g.Count()),
                JobsByStatus = jobs.GroupBy(j => j.Status).ToDictionary(g => g.Key, g => g.Count()),
                GeneratedAt = DateTime.UtcNow
            };

            return stats;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting processing stats");
            return new JobProcessingStatsDto { GeneratedAt = DateTime.UtcNow };
        }
    }

    public async Task<BackgroundJobDto?> DequeueJobAsync(string workerId, List<string> queues, CancellationToken cancellationToken = default)
    {
        try
        {
            var worker = await _workerRepository.GetByWorkerIdAsync(workerId, cancellationToken);
            if (worker == null || !worker.IsAvailable())
            {
                return null;
            }

            // Get next available job from supported queues
            var job = await _jobRepository.GetNextJobAsync(queues, worker.SupportedJobTypes, cancellationToken);
            if (job == null)
            {
                return null;
            }

            // Start the job
            job.Start(workerId);
            worker.StartJob();

            // Create execution record
            var execution = new JobExecution(
                job.Id,
                job.RetryCount + 1,
                workerId,
                new Dictionary<string, object>
                {
                    ["worker_name"] = worker.WorkerName,
                    ["machine_name"] = worker.MachineName,
                    ["process_id"] = worker.ProcessId,
                    ["dequeued_at"] = DateTime.UtcNow
                });

            _executionRepository.Add(execution);

            await _jobRepository.SaveChangesAsync(cancellationToken);
            await _workerRepository.SaveChangesAsync(cancellationToken);
            await _executionRepository.SaveChangesAsync(cancellationToken);

            // Update queue statistics
            await UpdateQueueStatisticsAsync(job.Queue, "pending_jobs", -1, cancellationToken);
            await UpdateQueueStatisticsAsync(job.Queue, "running_jobs", 1, cancellationToken);

            _logger.LogInformation("Job {JobId} dequeued by worker {WorkerId}", job.Id, workerId);

            return MapToBackgroundJobDto(job);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error dequeuing job for worker {WorkerId}", workerId);
            return null;
        }
    }

    public async Task<bool> UpdateJobProgressAsync(Guid jobId, UpdateJobProgressRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var job = await _jobRepository.GetByIdAsync(jobId, cancellationToken);
            if (job == null || !job.IsRunning())
            {
                return false;
            }

            job.UpdateProgress(request.Progress);
            await _jobRepository.SaveChangesAsync(cancellationToken);

            _logger.LogDebug("Job {JobId} progress updated", jobId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating job progress {JobId}", jobId);
            return false;
        }
    }

    public async Task<bool> CompleteJobAsync(Guid jobId, CompleteJobRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var job = await _jobRepository.GetByIdAsync(jobId, cancellationToken);
            if (job == null || !job.IsRunning())
            {
                return false;
            }

            job.Complete(request.Result);

            // Update worker
            if (!string.IsNullOrEmpty(job.WorkerId))
            {
                var worker = await _workerRepository.GetByWorkerIdAsync(job.WorkerId, cancellationToken);
                if (worker != null)
                {
                    worker.CompleteJob(true);
                    await _workerRepository.SaveChangesAsync(cancellationToken);
                }
            }

            // Update execution
            var execution = await _executionRepository.GetLatestByJobIdAsync(jobId, cancellationToken);
            if (execution != null)
            {
                execution.Complete(request.Result, request.PerformanceMetrics);
                await _executionRepository.SaveChangesAsync(cancellationToken);
            }

            await _jobRepository.SaveChangesAsync(cancellationToken);

            // Update queue statistics
            await UpdateQueueStatisticsAsync(job.Queue, "running_jobs", -1, cancellationToken);
            await UpdateQueueStatisticsAsync(job.Queue, "completed_jobs", 1, cancellationToken);

            _logger.LogInformation("Job {JobId} completed successfully", jobId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error completing job {JobId}", jobId);
            return false;
        }
    }

    public async Task<bool> FailJobAsync(Guid jobId, FailJobRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var job = await _jobRepository.GetByIdAsync(jobId, cancellationToken);
            if (job == null || !job.IsRunning())
            {
                return false;
            }

            job.Fail(request.ErrorMessage, request.ErrorStackTrace);

            // Update worker
            if (!string.IsNullOrEmpty(job.WorkerId))
            {
                var worker = await _workerRepository.GetByWorkerIdAsync(job.WorkerId, cancellationToken);
                if (worker != null)
                {
                    worker.CompleteJob(false);
                    await _workerRepository.SaveChangesAsync(cancellationToken);
                }
            }

            // Update execution
            var execution = await _executionRepository.GetLatestByJobIdAsync(jobId, cancellationToken);
            if (execution != null)
            {
                execution.Fail(request.ErrorMessage, request.ErrorStackTrace, request.PerformanceMetrics);
                await _executionRepository.SaveChangesAsync(cancellationToken);
            }

            await _jobRepository.SaveChangesAsync(cancellationToken);

            // Update queue statistics
            await UpdateQueueStatisticsAsync(job.Queue, "running_jobs", -1, cancellationToken);
            await UpdateQueueStatisticsAsync(job.Queue, "failed_jobs", 1, cancellationToken);

            // Auto-retry if possible
            if (job.CanRetry())
            {
                var retryDelay = CalculateRetryDelay(job.RetryCount);
                job.Retry(retryDelay);
                await _jobRepository.SaveChangesAsync(cancellationToken);
                await UpdateQueueStatisticsAsync(job.Queue, "pending_jobs", 1, cancellationToken);
                _logger.LogInformation("Job {JobId} scheduled for automatic retry", jobId);
            }

            _logger.LogInformation("Job {JobId} failed: {ErrorMessage}", jobId, request.ErrorMessage);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error failing job {JobId}", jobId);
            return false;
        }
    }

    public async Task ProcessScheduledJobsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var scheduledJobs = await _jobRepository.GetScheduledJobsAsync(cancellationToken);

            foreach (var job in scheduledJobs.Where(j => j.ScheduledAt <= DateTime.UtcNow))
            {
                job.Status = "Pending";
                _logger.LogDebug("Scheduled job {JobId} moved to pending", job.Id);
            }

            if (scheduledJobs.Any())
            {
                await _jobRepository.SaveChangesAsync(cancellationToken);
                _logger.LogInformation("Processed {Count} scheduled jobs", scheduledJobs.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing scheduled jobs");
        }
    }

    public async Task ProcessRetryJobsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var retryJobs = await _jobRepository.GetRetryJobsAsync(cancellationToken);

            foreach (var job in retryJobs.Where(j => j.NextRetryAt <= DateTime.UtcNow))
            {
                job.Status = "Pending";
                job.NextRetryAt = null;
                _logger.LogDebug("Retry job {JobId} moved to pending", job.Id);
            }

            if (retryJobs.Any())
            {
                await _jobRepository.SaveChangesAsync(cancellationToken);
                _logger.LogInformation("Processed {Count} retry jobs", retryJobs.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing retry jobs");
        }
    }

    public async Task CleanupCompletedJobsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var completedJobsRetentionDays = _configuration.GetValue<int>("BackgroundJobs:CompletedJobsRetentionDays", 7);
            var failedJobsRetentionDays = _configuration.GetValue<int>("BackgroundJobs:FailedJobsRetentionDays", 30);

            var completedCutoff = DateTime.UtcNow.AddDays(-completedJobsRetentionDays);
            var failedCutoff = DateTime.UtcNow.AddDays(-failedJobsRetentionDays);

            var completedJobsToDelete = await _jobRepository.GetCompletedJobsOlderThanAsync(completedCutoff, cancellationToken);
            var failedJobsToDelete = await _jobRepository.GetFailedJobsOlderThanAsync(failedCutoff, cancellationToken);

            foreach (var job in completedJobsToDelete.Concat(failedJobsToDelete))
            {
                _jobRepository.Remove(job);
            }

            var totalDeleted = completedJobsToDelete.Count + failedJobsToDelete.Count;
            if (totalDeleted > 0)
            {
                await _jobRepository.SaveChangesAsync(cancellationToken);
                _logger.LogInformation("Cleaned up {Count} old jobs", totalDeleted);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up completed jobs");
        }
    }

    // Helper methods
    private async Task UpdateQueueStatisticsAsync(string queueName, string statistic, int increment, CancellationToken cancellationToken)
    {
        try
        {
            var queue = await _queueRepository.GetByNameAsync(queueName, cancellationToken);
            if (queue != null)
            {
                queue.IncrementStatistic(statistic, increment);
                await _queueRepository.SaveChangesAsync(cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating queue statistics for {QueueName}", queueName);
        }
    }

    private TimeSpan CalculateRetryDelay(int retryCount)
    {
        // Exponential backoff with jitter
        var baseDelay = _configuration.GetValue<int>("BackgroundJobs:BaseRetryDelaySeconds", 60);
        var maxDelay = _configuration.GetValue<int>("BackgroundJobs:MaxRetryDelaySeconds", 3600);

        var delay = Math.Min(baseDelay * Math.Pow(2, retryCount), maxDelay);
        var jitter = new Random().NextDouble() * 0.1 * delay; // 10% jitter

        return TimeSpan.FromSeconds(delay + jitter);
    }

    // Mapping methods
    private BackgroundJobDto MapToBackgroundJobDto(BackgroundJob job)
    {
        return new BackgroundJobDto
        {
            Id = job.Id,
            JobType = job.JobType,
            JobName = job.JobName,
            Queue = job.Queue,
            Status = job.Status,
            Priority = job.Priority,
            Parameters = job.Parameters,
            Result = job.Result,
            CreatedAt = job.CreatedAt,
            ScheduledAt = job.ScheduledAt,
            StartedAt = job.StartedAt,
            CompletedAt = job.CompletedAt,
            Duration = job.Duration?.TotalMilliseconds,
            ErrorMessage = job.ErrorMessage,
            ErrorStackTrace = job.ErrorStackTrace,
            RetryCount = job.RetryCount,
            MaxRetries = job.MaxRetries,
            NextRetryAt = job.NextRetryAt,
            ParentJobId = job.ParentJobId,
            ChildJobIds = job.ChildJobIds,
            CreatedBy = job.CreatedBy,
            WorkerId = job.WorkerId,
            Progress = job.Progress,
            ProgressPercentage = job.GetProgressPercentage(),
            EstimatedTimeRemaining = job.GetEstimatedTimeRemaining(),
            Age = job.GetAge().TotalMinutes,
            ExecutionTime = job.GetExecutionTime()?.TotalMilliseconds,
            IsReadyToRun = job.IsReadyToRun(),
            CanRetry = job.CanRetry(),
            IsCompleted = job.IsCompleted(),
            IsRunning = job.IsRunning()
        };
    }

    private JobQueueDto MapToJobQueueDto(JobQueue queue)
    {
        return new JobQueueDto
        {
            Id = queue.Id,
            Name = queue.Name,
            Description = queue.Description,
            MaxConcurrency = queue.MaxConcurrency,
            Priority = queue.Priority,
            IsActive = queue.IsActive,
            IsPaused = queue.IsPaused,
            Configuration = queue.Configuration,
            Statistics = queue.Statistics,
            CreatedAt = queue.CreatedAt,
            UpdatedAt = queue.UpdatedAt,
            CreatedBy = queue.CreatedBy,
            CanProcessJobs = queue.CanProcessJobs()
        };
    }

    private JobWorkerDto MapToJobWorkerDto(JobWorker worker)
    {
        return new JobWorkerDto
        {
            Id = worker.Id,
            WorkerId = worker.WorkerId,
            WorkerName = worker.WorkerName,
            MachineName = worker.MachineName,
            ProcessId = worker.ProcessId,
            SupportedQueues = worker.SupportedQueues,
            SupportedJobTypes = worker.SupportedJobTypes,
            Status = worker.Status,
            StartedAt = worker.StartedAt,
            LastHeartbeat = worker.LastHeartbeat,
            MaxConcurrentJobs = worker.MaxConcurrentJobs,
            CurrentJobCount = worker.CurrentJobCount,
            Configuration = worker.Configuration,
            Statistics = worker.Statistics,
            IsAvailable = worker.IsAvailable(),
            IsOnline = worker.IsOnline()
        };
    }
}

// Supporting interfaces for job execution
public interface IJobHandler
{
    Task<Dictionary<string, object>> ExecuteAsync(Dictionary<string, object> parameters, IProgress<Dictionary<string, object>>? progress = null, CancellationToken cancellationToken = default);
    string JobType { get; }
}

public interface IJobExecutor
{
    Task<JobExecutionResult> ExecuteJobAsync(BackgroundJob job, CancellationToken cancellationToken = default);
}

public class JobExecutionResult
{
    public bool IsSuccess { get; set; }
    public Dictionary<string, object> Result { get; set; } = new();
    public string? ErrorMessage { get; set; }
    public string? ErrorStackTrace { get; set; }
    public Dictionary<string, object> PerformanceMetrics { get; set; } = new();
}
