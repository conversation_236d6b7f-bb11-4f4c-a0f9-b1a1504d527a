using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SubscriptionManagement.Domain.Entities;

namespace SubscriptionManagement.Infrastructure.Persistence.Configurations;

public class FeatureFlagRuleConfiguration : IEntityTypeConfiguration<FeatureFlagRule>
{
    public void Configure(EntityTypeBuilder<FeatureFlagRule> builder)
    {
        builder.ToTable("FeatureFlagRules");

        builder.<PERSON>Key(r => r.Id);

        builder.Property(r => r.Name)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(r => r.Description)
            .HasMaxLength(500);

        builder.Property(r => r.Type)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(r => r.Condition)
            .HasMaxLength(2000);

        builder.Property(r => r.Priority)
            .HasDefaultValue(0);

        builder.Property(r => r.IsActive)
            .HasDefaultValue(true);

        builder.Property(r => r.CreatedAt)
            .IsRequired();

        builder.Property(r => r.UpdatedAt);

        // Indexes
        builder.HasIndex(r => r.Type);
        builder.HasIndex(r => r.IsActive);
        builder.HasIndex(r => new { r.FeatureFlagId, r.IsActive });
        builder.HasIndex(r => r.Priority);
    }
}
