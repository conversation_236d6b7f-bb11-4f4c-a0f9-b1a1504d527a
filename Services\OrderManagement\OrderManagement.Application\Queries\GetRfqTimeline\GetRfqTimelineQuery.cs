using MediatR;
using OrderManagement.Application.DTOs;

namespace OrderManagement.Application.Queries.GetRfqTimeline;

public class GetRfqTimelineQuery : IRequest<List<RfqTimelineEventDto>>
{
    public Guid RfqId { get; set; }
    public Guid RequestingUserId { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public List<string>? EventTypes { get; set; }
}
