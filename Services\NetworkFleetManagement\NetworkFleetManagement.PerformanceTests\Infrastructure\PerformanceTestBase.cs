using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using NetworkFleetManagement.API;
using NetworkFleetManagement.Infrastructure.Persistence;
using Testcontainers.PostgreSql;
using Testcontainers.Redis;
using Bogus;

namespace NetworkFleetManagement.PerformanceTests.Infrastructure;

public class PerformanceTestBase : IAsyncLifetime
{
    protected WebApplicationFactory<Program> Factory { get; private set; } = null!;
    protected HttpClient Client { get; private set; } = null!;
    protected PostgreSqlContainer PostgreSqlContainer { get; private set; } = null!;
    protected RedisContainer RedisContainer { get; private set; } = null!;
    protected Faker Faker { get; private set; } = null!;

    public async Task InitializeAsync()
    {
        // Initialize test containers
        PostgreSqlContainer = new PostgreSqlBuilder()
            .WithImage("postgres:15")
            .WithDatabase("networkfleet_test")
            .WithUsername("test")
            .WithPassword("test123")
            .Build();

        RedisContainer = new RedisBuilder()
            .WithImage("redis:7")
            .Build();

        await PostgreSqlContainer.StartAsync();
        await RedisContainer.StartAsync();

        // Initialize Faker
        Faker = new Faker();

        // Create test application factory
        Factory = new WebApplicationFactory<Program>()
            .WithWebHostBuilder(builder =>
            {
                builder.UseEnvironment("Testing");
                builder.ConfigureServices(services =>
                {
                    // Replace database connection
                    var descriptor = services.SingleOrDefault(d => d.ServiceType == typeof(NetworkFleetDbContext));
                    if (descriptor != null)
                        services.Remove(descriptor);

                    services.AddDbContext<NetworkFleetDbContext>(options =>
                        options.UseNpgsql(PostgreSqlContainer.GetConnectionString()));

                    // Replace Redis connection
                    services.Configure<RedisOptions>(options =>
                    {
                        options.ConnectionString = RedisContainer.GetConnectionString();
                    });

                    // Configure logging for performance tests
                    services.AddLogging(logging =>
                    {
                        logging.ClearProviders();
                        logging.AddConsole();
                        logging.SetMinimumLevel(LogLevel.Warning);
                    });
                });
            });

        Client = Factory.CreateClient();

        // Initialize database
        using var scope = Factory.Services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<NetworkFleetDbContext>();
        await context.Database.EnsureCreatedAsync();
    }

    public async Task DisposeAsync()
    {
        Client?.Dispose();
        Factory?.Dispose();
        
        if (PostgreSqlContainer != null)
            await PostgreSqlContainer.DisposeAsync();
        
        if (RedisContainer != null)
            await RedisContainer.DisposeAsync();
    }

    protected async Task SeedTestDataAsync(int carrierCount = 10, int vehiclesPerCarrier = 50, int driversPerCarrier = 30)
    {
        using var scope = Factory.Services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<NetworkFleetDbContext>();

        var carriers = GenerateCarriers(carrierCount);
        await context.Carriers.AddRangeAsync(carriers);
        await context.SaveChangesAsync();

        foreach (var carrier in carriers)
        {
            var vehicles = GenerateVehicles(carrier.Id, vehiclesPerCarrier);
            var drivers = GenerateDrivers(carrier.Id, driversPerCarrier);

            await context.Vehicles.AddRangeAsync(vehicles);
            await context.Drivers.AddRangeAsync(drivers);
        }

        await context.SaveChangesAsync();
    }

    private List<Carrier> GenerateCarriers(int count)
    {
        var carriers = new List<Carrier>();
        
        for (int i = 0; i < count; i++)
        {
            var carrier = new Carrier(
                Faker.Company.CompanyName(),
                Faker.Internet.Email(),
                Faker.Phone.PhoneNumber(),
                new Address(
                    Faker.Address.StreetAddress(),
                    Faker.Address.City(),
                    Faker.Address.StateAbbr(),
                    Faker.Address.ZipCode(),
                    Faker.Address.Country()
                )
            );
            
            carriers.Add(carrier);
        }

        return carriers;
    }

    private List<Vehicle> GenerateVehicles(Guid carrierId, int count)
    {
        var vehicles = new List<Vehicle>();
        var vehicleTypes = new[] { "Truck", "Van", "Trailer", "Container" };
        
        for (int i = 0; i < count; i++)
        {
            var vehicle = new Vehicle(
                Faker.Vehicle.Vin(),
                Faker.Random.String2(8).ToUpper(),
                Faker.PickRandom(vehicleTypes),
                carrierId,
                new VehicleSpecifications(
                    Faker.Random.Double(5000, 40000),
                    Faker.Random.Double(10, 50),
                    Faker.Random.Double(2, 4),
                    Faker.Random.Double(6, 20)
                )
            );
            
            vehicles.Add(vehicle);
        }

        return vehicles;
    }

    private List<Driver> GenerateDrivers(Guid carrierId, int count)
    {
        var drivers = new List<Driver>();
        
        for (int i = 0; i < count; i++)
        {
            var driver = new Driver(
                Faker.Name.FirstName(),
                Faker.Name.LastName(),
                Faker.Internet.Email(),
                Faker.Phone.PhoneNumber(),
                Faker.Random.String2(10).ToUpper(),
                DateTime.UtcNow.AddYears(2),
                carrierId
            );
            
            drivers.Add(driver);
        }

        return drivers;
    }

    protected Dictionary<string, object> GenerateVehicleLocationUpdate()
    {
        return new Dictionary<string, object>
        {
            ["latitude"] = Faker.Address.Latitude(),
            ["longitude"] = Faker.Address.Longitude(),
            ["speed"] = Faker.Random.Double(0, 120),
            ["heading"] = Faker.Random.Double(0, 360),
            ["timestamp"] = DateTime.UtcNow
        };
    }

    protected Dictionary<string, object> GenerateDriverStatusUpdate()
    {
        var statuses = new[] { "available", "on_trip", "off_duty", "break" };
        return new Dictionary<string, object>
        {
            ["status"] = Faker.PickRandom(statuses),
            ["timestamp"] = DateTime.UtcNow,
            ["location"] = new
            {
                latitude = Faker.Address.Latitude(),
                longitude = Faker.Address.Longitude()
            }
        };
    }

    protected Dictionary<string, object> GenerateMaintenanceRecord()
    {
        var maintenanceTypes = new[] { "oil_change", "tire_replacement", "brake_service", "engine_repair" };
        return new Dictionary<string, object>
        {
            ["type"] = Faker.PickRandom(maintenanceTypes),
            ["cost"] = Faker.Random.Double(100, 5000),
            ["description"] = Faker.Lorem.Sentence(),
            ["scheduledDate"] = Faker.Date.Future(),
            ["estimatedDuration"] = Faker.Random.Int(1, 8)
        };
    }
}
