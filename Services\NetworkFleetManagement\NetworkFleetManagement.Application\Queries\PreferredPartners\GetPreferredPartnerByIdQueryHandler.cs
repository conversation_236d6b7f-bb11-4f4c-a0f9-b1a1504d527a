using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Repositories;

namespace NetworkFleetManagement.Application.Queries.PreferredPartners;

public class GetPreferredPartnerByIdQueryHandler : IRequestHandler<GetPreferredPartnerByIdQuery, PreferredPartnerDto?>
{
    private readonly IPreferredPartnerRepository _preferredPartnerRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetPreferredPartnerByIdQueryHandler> _logger;

    public GetPreferredPartnerByIdQueryHandler(
        IPreferredPartnerRepository preferredPartnerRepository,
        IMapper mapper,
        ILogger<GetPreferredPartnerByIdQueryHandler> logger)
    {
        _preferredPartnerRepository = preferredPartnerRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<PreferredPartnerDto?> Handle(GetPreferredPartnerByIdQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var partner = await _preferredPartnerRepository.GetByIdAsync(request.Id, cancellationToken);
            
            if (partner == null)
            {
                return null;
            }

            return _mapper.Map<PreferredPartnerDto>(partner);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving preferred partner by ID {Id}", request.Id);
            throw;
        }
    }
}
