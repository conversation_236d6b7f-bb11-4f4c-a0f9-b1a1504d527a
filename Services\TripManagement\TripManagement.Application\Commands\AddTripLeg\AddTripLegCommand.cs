using MediatR;
using TripManagement.Application.DTOs;

namespace TripManagement.Application.Commands.AddTripLeg;

public class AddTripLegCommand : IRequest<Guid>
{
    public Guid TripId { get; set; }
    public int LegNumber { get; set; }
    public string LegName { get; set; } = string.Empty;
    public string? Description { get; set; }
    public RouteDto Route { get; set; } = null!;
    public DateTime EstimatedStartTime { get; set; }
    public DateTime EstimatedEndTime { get; set; }
    public string? SpecialInstructions { get; set; }
    public bool IsRequired { get; set; } = true;
    public int Priority { get; set; } = 1;
    public List<TripLegStopRequest> Stops { get; set; } = new();
    public Guid RequestingUserId { get; set; }
}

public class TripLegStopRequest
{
    public TripStopType StopType { get; set; }
    public int SequenceNumber { get; set; }
    public LocationDto Location { get; set; } = null!;
    public string? ContactName { get; set; }
    public string? ContactPhone { get; set; }
    public DateTime? ScheduledArrival { get; set; }
    public DateTime? ScheduledDeparture { get; set; }
    public string? Instructions { get; set; }
    public bool IsRequired { get; set; } = true;
}
