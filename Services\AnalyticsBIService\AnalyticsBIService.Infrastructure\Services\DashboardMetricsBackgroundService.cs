using AnalyticsBIService.Application.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace AnalyticsBIService.Infrastructure.Services;

/// <summary>
/// Background service for scheduled dashboard metrics calculation
/// </summary>
public class DashboardMetricsBackgroundService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<DashboardMetricsBackgroundService> _logger;
    
    // Scheduling intervals
    private static readonly TimeSpan IntensiveMetricsInterval = TimeSpan.FromHours(1);
    private static readonly TimeSpan RealTimeMetricsInterval = TimeSpan.FromMinutes(5);
    private static readonly TimeSpan CacheCleanupInterval = TimeSpan.FromHours(6);

    public DashboardMetricsBackgroundService(
        IServiceProvider serviceProvider,
        ILogger<DashboardMetricsBackgroundService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Dashboard Metrics Background Service started");

        // Start multiple tasks for different metric types
        var tasks = new[]
        {
            Task.Run(() => ProcessIntensiveMetricsAsync(stoppingToken), stoppingToken),
            Task.Run(() => ProcessRealTimeMetricsAsync(stoppingToken), stoppingToken),
            Task.Run(() => ProcessCacheCleanupAsync(stoppingToken), stoppingToken)
        };

        try
        {
            await Task.WhenAll(tasks);
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("Dashboard Metrics Background Service is stopping");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Dashboard Metrics Background Service encountered an error");
        }
    }

    private async Task ProcessIntensiveMetricsAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Starting intensive metrics processing loop");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var dashboardMetricsService = scope.ServiceProvider.GetRequiredService<IDashboardMetricsService>();

                _logger.LogDebug("Processing intensive metrics calculation");

                // Pre-calculate expensive metrics for common scenarios
                await PreCalculateCommonMetricsAsync(dashboardMetricsService, stoppingToken);

                _logger.LogDebug("Intensive metrics calculation completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during intensive metrics processing");
            }

            try
            {
                await Task.Delay(IntensiveMetricsInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
        }

        _logger.LogInformation("Intensive metrics processing loop stopped");
    }

    private async Task ProcessRealTimeMetricsAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Starting real-time metrics processing loop");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var dashboardMetricsService = scope.ServiceProvider.GetRequiredService<IDashboardMetricsService>();

                _logger.LogDebug("Processing real-time metrics update");

                // Update real-time metrics for active users
                await UpdateRealTimeMetricsAsync(dashboardMetricsService, stoppingToken);

                _logger.LogDebug("Real-time metrics update completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during real-time metrics processing");
            }

            try
            {
                await Task.Delay(RealTimeMetricsInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
        }

        _logger.LogInformation("Real-time metrics processing loop stopped");
    }

    private async Task ProcessCacheCleanupAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Starting cache cleanup processing loop");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var dashboardMetricsService = scope.ServiceProvider.GetRequiredService<IDashboardMetricsService>();

                _logger.LogDebug("Processing cache cleanup");

                // Perform cache cleanup and optimization
                await PerformCacheCleanupAsync(dashboardMetricsService, stoppingToken);

                _logger.LogDebug("Cache cleanup completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during cache cleanup processing");
            }

            try
            {
                await Task.Delay(CacheCleanupInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
        }

        _logger.LogInformation("Cache cleanup processing loop stopped");
    }

    private async Task PreCalculateCommonMetricsAsync(IDashboardMetricsService dashboardMetricsService, CancellationToken stoppingToken)
    {
        try
        {
            // Pre-calculate platform-wide metrics (for admin dashboard)
            _logger.LogDebug("Pre-calculating platform-wide metrics");
            await dashboardMetricsService.CalculateAverageOrderFulfillmentTimeAsync(cancellationToken: stoppingToken);

            // Pre-calculate metrics for different time periods
            var now = DateTime.UtcNow;
            var timeRanges = new[]
            {
                (now.AddDays(-7), now),   // Last 7 days
                (now.AddDays(-30), now),  // Last 30 days
                (now.AddDays(-90), now)   // Last 90 days
            };

            foreach (var (fromDate, toDate) in timeRanges)
            {
                await dashboardMetricsService.CalculateAverageOrderFulfillmentTimeAsync(
                    fromDate: fromDate, 
                    toDate: toDate, 
                    cancellationToken: stoppingToken);
            }

            _logger.LogDebug("Pre-calculation of common metrics completed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error pre-calculating common metrics");
        }
    }

    private async Task UpdateRealTimeMetricsAsync(IDashboardMetricsService dashboardMetricsService, CancellationToken stoppingToken)
    {
        try
        {
            // Update real-time metrics for platform overview
            _logger.LogDebug("Updating real-time platform metrics");
            await dashboardMetricsService.GetRealTimeMetricsAsync(cancellationToken: stoppingToken);

            // Here you could also update metrics for specific active users
            // This would require getting a list of currently active users
            // and updating their metrics individually

            _logger.LogDebug("Real-time metrics update completed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating real-time metrics");
        }
    }

    private async Task PerformCacheCleanupAsync(IDashboardMetricsService dashboardMetricsService, CancellationToken stoppingToken)
    {
        try
        {
            _logger.LogDebug("Performing cache cleanup and optimization");

            // This could include:
            // 1. Removing expired cache entries
            // 2. Compacting cache data
            // 3. Pre-warming frequently accessed metrics
            // 4. Analyzing cache hit rates and optimizing

            // For now, we'll just refresh the cache to remove stale data
            await dashboardMetricsService.RefreshMetricsCacheAsync(stoppingToken);

            _logger.LogDebug("Cache cleanup and optimization completed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during cache cleanup");
        }
    }

    public override async Task StopAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Dashboard Metrics Background Service is stopping");
        await base.StopAsync(stoppingToken);
    }
}
