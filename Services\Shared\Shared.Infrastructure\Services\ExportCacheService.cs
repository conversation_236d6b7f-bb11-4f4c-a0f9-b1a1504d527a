using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace Shared.Infrastructure.Services
{
    /// <summary>
    /// Caching service for export operations to improve performance
    /// </summary>
    public interface IExportCacheService
    {
        Task<T?> GetAsync<T>(string key) where T : class;
        Task SetAsync<T>(string key, T value, TimeSpan? expiration = null) where T : class;
        Task RemoveAsync(string key);
        Task<bool> ExistsAsync(string key);
        string GenerateExportCacheKey(string exportType, string parameters);
        Task InvalidateExportCacheAsync(string pattern);
    }

    public class ExportCacheService : IExportCacheService
    {
        private readonly IDistributedCache _distributedCache;
        private readonly ILogger<ExportCacheService> _logger;
        private readonly TimeSpan _defaultExpiration = TimeSpan.FromMinutes(30);

        public ExportCacheService(
            IDistributedCache distributedCache,
            ILogger<ExportCacheService> logger)
        {
            _distributedCache = distributedCache;
            _logger = logger;
        }

        public async Task<T?> GetAsync<T>(string key) where T : class
        {
            try
            {
                var cachedValue = await _distributedCache.GetStringAsync(key);
                
                if (string.IsNullOrEmpty(cachedValue))
                {
                    return null;
                }

                var result = JsonSerializer.Deserialize<T>(cachedValue);
                _logger.LogDebug("Cache hit for key: {Key}", key);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving cached value for key: {Key}", key);
                return null;
            }
        }

        public async Task SetAsync<T>(string key, T value, TimeSpan? expiration = null) where T : class
        {
            try
            {
                var serializedValue = JsonSerializer.Serialize(value);
                var options = new DistributedCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = expiration ?? _defaultExpiration
                };

                await _distributedCache.SetStringAsync(key, serializedValue, options);
                _logger.LogDebug("Cached value for key: {Key} with expiration: {Expiration}", 
                    key, expiration ?? _defaultExpiration);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error caching value for key: {Key}", key);
            }
        }

        public async Task RemoveAsync(string key)
        {
            try
            {
                await _distributedCache.RemoveAsync(key);
                _logger.LogDebug("Removed cached value for key: {Key}", key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing cached value for key: {Key}", key);
            }
        }

        public async Task<bool> ExistsAsync(string key)
        {
            try
            {
                var cachedValue = await _distributedCache.GetStringAsync(key);
                return !string.IsNullOrEmpty(cachedValue);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking cache existence for key: {Key}", key);
                return false;
            }
        }

        public string GenerateExportCacheKey(string exportType, string parameters)
        {
            var hash = parameters.GetHashCode();
            return $"export:{exportType}:{hash}";
        }

        public async Task InvalidateExportCacheAsync(string pattern)
        {
            // Note: This is a simplified implementation
            // In a real Redis implementation, you would use SCAN with pattern matching
            _logger.LogInformation("Cache invalidation requested for pattern: {Pattern}", pattern);
            
            // For now, we'll just log the invalidation request
            // In a production environment, you would implement proper pattern-based cache invalidation
        }
    }

    /// <summary>
    /// Export cache configuration
    /// </summary>
    public class ExportCacheOptions
    {
        public TimeSpan DefaultExpiration { get; set; } = TimeSpan.FromMinutes(30);
        public TimeSpan UserSummaryExpiration { get; set; } = TimeSpan.FromMinutes(15);
        public TimeSpan KycDocumentsExpiration { get; set; } = TimeSpan.FromMinutes(60);
        public TimeSpan UsageLogsExpiration { get; set; } = TimeSpan.FromMinutes(10);
        public TimeSpan FeedbackHistoryExpiration { get; set; } = TimeSpan.FromMinutes(20);
        public TimeSpan AdminAuditTrailExpiration { get; set; } = TimeSpan.FromMinutes(5);
        public bool EnableCaching { get; set; } = true;
    }

    /// <summary>
    /// Cached export metadata
    /// </summary>
    public class CachedExportMetadata
    {
        public string ExportType { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public string FileUrl { get; set; } = string.Empty;
        public int RecordCount { get; set; }
        public long FileSizeBytes { get; set; }
        public DateTime GeneratedAt { get; set; }
        public DateTime ExpiresAt { get; set; }
        public string ExportId { get; set; } = string.Empty;
        public Dictionary<string, object> Summary { get; set; } = new();
        public bool IsValid => DateTime.UtcNow < ExpiresAt;
    }

    /// <summary>
    /// Export performance metrics for caching decisions
    /// </summary>
    public class ExportPerformanceMetrics
    {
        public string ExportType { get; set; } = string.Empty;
        public TimeSpan AverageProcessingTime { get; set; }
        public int AverageRecordCount { get; set; }
        public long AverageFileSizeBytes { get; set; }
        public int RequestFrequency { get; set; } // Requests per hour
        public DateTime LastUpdated { get; set; }
        public bool ShouldCache => RequestFrequency > 5 && AverageProcessingTime > TimeSpan.FromSeconds(10);
    }
}
