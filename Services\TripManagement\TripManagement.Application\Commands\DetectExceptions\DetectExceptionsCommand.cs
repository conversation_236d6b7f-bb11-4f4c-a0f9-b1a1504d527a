using MediatR;
using TripManagement.Application.DTOs;

namespace TripManagement.Application.Commands.DetectExceptions;

public record DetectExceptionsCommand : IRequest<List<DetectedExceptionDto>>
{
    public Guid? TripId { get; init; } // If null, check all active trips
    public List<ExceptionDetectionRule> Rules { get; init; } = new();
}

public record DetectedExceptionDto
{
    public Guid TripId { get; init; }
    public string TripNumber { get; init; } = string.Empty;
    public ExceptionDetectionType DetectionType { get; init; }
    public string Description { get; init; } = string.Empty;
    public LocationDto? Location { get; init; }
    public DateTime DetectedAt { get; init; }
    public ExceptionSeverity Severity { get; init; }
    public string? SuggestedAction { get; init; }
    public Dictionary<string, object> Metadata { get; init; } = new();
}

public record ExceptionDetectionRule
{
    public ExceptionDetectionType Type { get; init; }
    public Dictionary<string, object> Parameters { get; init; } = new();
    public bool IsEnabled { get; init; } = true;
}

public enum ExceptionDetectionType
{
    DelayDetection = 0,
    RouteDeviation = 1,
    SpeedViolation = 2,
    GeofenceViolation = 3,
    ExtendedStop = 4,
    UnexpectedStop = 5,
    CommunicationLoss = 6,
    FuelLow = 7,
    MaintenanceRequired = 8,
    WeatherImpact = 9
}

public enum ExceptionSeverity
{
    Low = 0,
    Medium = 1,
    High = 2,
    Critical = 3
}
