using Shared.Domain.Common;
using MobileWorkflow.Domain.Enums;
using MobileWorkflow.Domain.Events;

namespace MobileWorkflow.Domain.Entities;

public class UIComponent : AggregateRoot
{
    public string Name { get; private set; }
    public string DisplayName { get; private set; }
    public string Description { get; private set; }
    public string Category { get; private set; } // Form, Navigation, Display, Input, Layout, etc.
    public string ComponentType { get; private set; } // Button, Input, Card, Modal, etc.
    public string Version { get; private set; }
    public bool IsActive { get; private set; }
    public Dictionary<string, object> Properties { get; private set; }
    public Dictionary<string, object> DefaultValues { get; private set; }
    public Dictionary<string, object> Styling { get; private set; }
    public Dictionary<string, object> Behavior { get; private set; }
    public List<string> SupportedPlatforms { get; private set; }
    public Dictionary<string, object> PlatformSpecific { get; private set; }
    public Dictionary<string, object> Dependencies { get; private set; }
    public string? ParentComponentId { get; private set; }
    public bool IsComposite { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? UpdatedAt { get; private set; }
    public string CreatedBy { get; private set; }
    public string? UpdatedBy { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    public virtual ICollection<UIComponent> ChildComponents { get; private set; } = new List<UIComponent>();
    public virtual ICollection<ComponentTheme> Themes { get; private set; } = new List<ComponentTheme>();
    public virtual ICollection<ComponentUsage> Usages { get; private set; } = new List<ComponentUsage>();

    private UIComponent() { } // EF Core

    public UIComponent(
        string name,
        string displayName,
        string description,
        string category,
        string componentType,
        string version,
        Dictionary<string, object> properties,
        List<string> supportedPlatforms,
        string createdBy)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        DisplayName = displayName ?? throw new ArgumentNullException(nameof(displayName));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        Category = category ?? throw new ArgumentNullException(nameof(category));
        ComponentType = componentType ?? throw new ArgumentNullException(nameof(componentType));
        Version = version ?? throw new ArgumentNullException(nameof(version));
        Properties = properties ?? throw new ArgumentNullException(nameof(properties));
        SupportedPlatforms = supportedPlatforms ?? throw new ArgumentNullException(nameof(supportedPlatforms));
        CreatedBy = createdBy ?? throw new ArgumentNullException(nameof(createdBy));

        IsActive = true;
        IsComposite = false;
        CreatedAt = DateTime.UtcNow;
        DefaultValues = new Dictionary<string, object>();
        Styling = new Dictionary<string, object>();
        Behavior = new Dictionary<string, object>();
        PlatformSpecific = new Dictionary<string, object>();
        Dependencies = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();

        AddDomainEvent(new UIComponentCreatedEvent(Id, Name, Category, ComponentType, CreatedBy));
    }

    public void UpdateProperties(Dictionary<string, object> newProperties, string updatedBy)
    {
        Properties = newProperties ?? throw new ArgumentNullException(nameof(newProperties));
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new UIComponentPropertiesUpdatedEvent(Id, Name, UpdatedBy));
    }

    public void UpdateStyling(Dictionary<string, object> newStyling, string updatedBy)
    {
        Styling = newStyling ?? throw new ArgumentNullException(nameof(newStyling));
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new UIComponentStylingUpdatedEvent(Id, Name, UpdatedBy));
    }

    public void UpdateBehavior(Dictionary<string, object> newBehavior, string updatedBy)
    {
        Behavior = newBehavior ?? throw new ArgumentNullException(nameof(newBehavior));
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new UIComponentBehaviorUpdatedEvent(Id, Name, UpdatedBy));
    }

    public void SetPlatformSpecific(string platform, Dictionary<string, object> platformConfig)
    {
        if (!SupportedPlatforms.Contains(platform, StringComparer.OrdinalIgnoreCase))
        {
            throw new InvalidOperationException($"Platform {platform} is not supported by this component");
        }

        PlatformSpecific[platform] = platformConfig ?? throw new ArgumentNullException(nameof(platformConfig));
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new UIComponentPlatformConfigUpdatedEvent(Id, Name, platform));
    }

    public void AddDependency(string dependencyName, string version, string type = "component")
    {
        Dependencies[dependencyName] = new { Version = version, Type = type, AddedAt = DateTime.UtcNow };
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new UIComponentDependencyAddedEvent(Id, Name, dependencyName, version));
    }

    public void RemoveDependency(string dependencyName)
    {
        if (Dependencies.Remove(dependencyName))
        {
            UpdatedAt = DateTime.UtcNow;
            AddDomainEvent(new UIComponentDependencyRemovedEvent(Id, Name, dependencyName));
        }
    }

    public void SetAsComposite(string parentComponentId)
    {
        IsComposite = true;
        ParentComponentId = parentComponentId;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new UIComponentSetAsCompositeEvent(Id, Name, parentComponentId));
    }

    public void Activate()
    {
        IsActive = true;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new UIComponentActivatedEvent(Id, Name));
    }

    public void Deactivate()
    {
        IsActive = false;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new UIComponentDeactivatedEvent(Id, Name));
    }

    public void UpdateVersion(string newVersion, string updatedBy)
    {
        var oldVersion = Version;
        Version = newVersion ?? throw new ArgumentNullException(nameof(newVersion));
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new UIComponentVersionUpdatedEvent(Id, Name, oldVersion, newVersion, UpdatedBy));
    }

    public bool SupportsPlatform(string platform)
    {
        return SupportedPlatforms.Contains(platform, StringComparer.OrdinalIgnoreCase);
    }

    public Dictionary<string, object> GetPlatformConfig(string platform)
    {
        if (PlatformSpecific.TryGetValue(platform, out var config) && config is Dictionary<string, object> platformConfig)
        {
            return platformConfig;
        }
        return new Dictionary<string, object>();
    }

    public Dictionary<string, object> GetMergedProperties(string? platform = null)
    {
        var merged = new Dictionary<string, object>(Properties);

        // Merge default values
        foreach (var kvp in DefaultValues)
        {
            if (!merged.ContainsKey(kvp.Key))
            {
                merged[kvp.Key] = kvp.Value;
            }
        }

        // Merge platform-specific properties
        if (!string.IsNullOrEmpty(platform))
        {
            var platformConfig = GetPlatformConfig(platform);
            foreach (var kvp in platformConfig)
            {
                merged[kvp.Key] = kvp.Value;
            }
        }

        return merged;
    }

    public bool HasDependency(string dependencyName)
    {
        return Dependencies.ContainsKey(dependencyName);
    }

    public List<string> GetDependencyNames()
    {
        return Dependencies.Keys.ToList();
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
        UpdatedAt = DateTime.UtcNow;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public void SetDefaultValue(string propertyName, object value)
    {
        DefaultValues[propertyName] = value;
        UpdatedAt = DateTime.UtcNow;
    }

    public bool IsCompatibleWith(string platform, string version)
    {
        if (!SupportsPlatform(platform))
        {
            return false;
        }

        // Add version compatibility logic here
        // For now, assume all versions are compatible
        return true;
    }
}


