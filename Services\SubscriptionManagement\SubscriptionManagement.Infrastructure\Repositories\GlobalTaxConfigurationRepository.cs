using Microsoft.EntityFrameworkCore;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Interfaces;
using SubscriptionManagement.Infrastructure.Persistence;

namespace SubscriptionManagement.Infrastructure.Repositories
{
    public class GlobalTaxConfigurationRepository : IGlobalTaxConfigurationRepository
    {
        private readonly SubscriptionDbContext _context;

        public GlobalTaxConfigurationRepository(SubscriptionDbContext context)
        {
            _context = context;
        }

        public async Task<GlobalTaxConfiguration?> GetByIdAsync(Guid id)
        {
            return await _context.GlobalTaxConfigurations
                .FirstOrDefaultAsync(gtc => gtc.Id == id);
        }

        public async Task<List<GlobalTaxConfiguration>> GetAllAsync()
        {
            return await _context.GlobalTaxConfigurations
                .OrderBy(gtc => gtc.Priority)
                .ThenBy(gtc => gtc.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<GlobalTaxConfiguration>> GetActiveAsync()
        {
            return await _context.GlobalTaxConfigurations
                .Where(gtc => gtc.IsActive)
                .OrderBy(gtc => gtc.Priority)
                .ThenBy(gtc => gtc.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<GlobalTaxConfiguration>> GetByRegionAsync(string region)
        {
            var upperRegion = region.ToUpperInvariant();
            return await _context.GlobalTaxConfigurations
                .Where(gtc => gtc.IsActive && 
                             gtc.TaxConfiguration.ApplicableRegions.Contains(upperRegion))
                .OrderBy(gtc => gtc.Priority)
                .ToListAsync();
        }

        public async Task<List<GlobalTaxConfiguration>> GetByTaxTypeAsync(TaxType taxType)
        {
            return await _context.GlobalTaxConfigurations
                .Where(gtc => gtc.IsActive && gtc.TaxConfiguration.TaxType == taxType)
                .OrderBy(gtc => gtc.Priority)
                .ToListAsync();
        }

        public async Task<GlobalTaxConfiguration?> GetByTaxTypeAndRegionAsync(TaxType taxType, string region)
        {
            var upperRegion = region.ToUpperInvariant();
            return await _context.GlobalTaxConfigurations
                .Where(gtc => gtc.IsActive && 
                             gtc.TaxConfiguration.TaxType == taxType &&
                             gtc.TaxConfiguration.ApplicableRegions.Contains(upperRegion))
                .OrderBy(gtc => gtc.Priority)
                .FirstOrDefaultAsync();
        }

        public async Task<List<GlobalTaxConfiguration>> GetEffectiveConfigurationsAsync(string region, DateTime? date = null)
        {
            var checkDate = date ?? DateTime.UtcNow;
            var upperRegion = region.ToUpperInvariant();
            
            return await _context.GlobalTaxConfigurations
                .Where(gtc => gtc.IsActive && 
                             gtc.TaxConfiguration.ApplicableRegions.Contains(upperRegion) &&
                             gtc.TaxConfiguration.EffectiveDate <= checkDate &&
                             (gtc.TaxConfiguration.ExpirationDate == null || 
                              gtc.TaxConfiguration.ExpirationDate >= checkDate))
                .OrderBy(gtc => gtc.Priority)
                .ToListAsync();
        }

        public async Task<GlobalTaxConfiguration> AddAsync(GlobalTaxConfiguration globalTaxConfiguration)
        {
            _context.GlobalTaxConfigurations.Add(globalTaxConfiguration);
            await _context.SaveChangesAsync();
            return globalTaxConfiguration;
        }

        public async Task<GlobalTaxConfiguration> UpdateAsync(GlobalTaxConfiguration globalTaxConfiguration)
        {
            _context.GlobalTaxConfigurations.Update(globalTaxConfiguration);
            await _context.SaveChangesAsync();
            return globalTaxConfiguration;
        }

        public async Task DeleteAsync(Guid id)
        {
            var globalTaxConfiguration = await GetByIdAsync(id);
            if (globalTaxConfiguration != null)
            {
                _context.GlobalTaxConfigurations.Remove(globalTaxConfiguration);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<bool> ExistsAsync(Guid id)
        {
            return await _context.GlobalTaxConfigurations
                .AnyAsync(gtc => gtc.Id == id);
        }

        public async Task<bool> ExistsByTaxTypeAndRegionAsync(TaxType taxType, string region)
        {
            var upperRegion = region.ToUpperInvariant();
            return await _context.GlobalTaxConfigurations
                .AnyAsync(gtc => gtc.IsActive && 
                                gtc.TaxConfiguration.TaxType == taxType &&
                                gtc.TaxConfiguration.ApplicableRegions.Contains(upperRegion));
        }

        public async Task<List<GlobalTaxConfiguration>> GetByPriorityRangeAsync(int minPriority, int maxPriority)
        {
            return await _context.GlobalTaxConfigurations
                .Where(gtc => gtc.IsActive && 
                             gtc.Priority >= minPriority && 
                             gtc.Priority <= maxPriority)
                .OrderBy(gtc => gtc.Priority)
                .ToListAsync();
        }

        public async Task<int> GetMaxPriorityAsync()
        {
            var maxPriority = await _context.GlobalTaxConfigurations
                .Where(gtc => gtc.IsActive)
                .MaxAsync(gtc => (int?)gtc.Priority);
            
            return maxPriority ?? 0;
        }

        public async Task<List<GlobalTaxConfiguration>> SearchAsync(string searchTerm, int page = 1, int pageSize = 10)
        {
            var query = _context.GlobalTaxConfigurations.AsQueryable();

            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                var lowerSearchTerm = searchTerm.ToLowerInvariant();
                query = query.Where(gtc => 
                    (gtc.Description != null && gtc.Description.ToLower().Contains(lowerSearchTerm)) ||
                    gtc.TaxConfiguration.TaxType.ToString().ToLower().Contains(lowerSearchTerm));
            }

            return await query
                .OrderBy(gtc => gtc.Priority)
                .ThenByDescending(gtc => gtc.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
        }

        public async Task<int> GetCountAsync()
        {
            return await _context.GlobalTaxConfigurations.CountAsync();
        }
    }
}
