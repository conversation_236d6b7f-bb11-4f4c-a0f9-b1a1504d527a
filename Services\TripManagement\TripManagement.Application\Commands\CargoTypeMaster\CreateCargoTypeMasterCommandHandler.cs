using Shared.Domain.Common;
using Shared.Domain.Common;
using Microsoft.Extensions.Logging;
using Shared.Domain.Common;
using TripManagement.Application.DTOs;
using Shared.Domain.Common;
using TripManagement.Domain.Entities;
using TripManagement.Domain.Interfaces;
using Shared.Domain.Common;
using Shared.Messaging;

namespace TripManagement.Application.Commands.CargoTypeMaster;

/// <summary>
/// Handler for creating a new cargo type master
/// </summary>
public class CreateCargoTypeMasterCommandHandler : IRequestHandler<CreateCargoTypeMasterCommand, CargoTypeMasterDto>
{
    private readonly ICargoTypeMasterRepository _repository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<CreateCargoTypeMasterCommandHandler> _logger;

    public CreateCargoTypeMasterCommandHandler(
        ICargoTypeMasterRepository repository,
        IUnitOfWork unitOfWork,
        IMapper mapper,
        IMessageBroker messageBroker,
        ILogger<CreateCargoTypeMasterCommandHandler> logger)
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task<CargoTypeMasterDto> Handle(CreateCargoTypeMasterCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Creating cargo type master with code: {Code}", request.Code);

            // Check if code already exists
            var existingByCode = await _repository.GetByCodeAsync(request.Code, cancellationToken);
            if (existingByCode != null)
            {
                throw new InvalidOperationException($"Cargo type master with code '{request.Code}' already exists");
            }

            // Get next sort order if not specified
            var sortOrder = request.SortOrder;
            if (sortOrder == 0)
            {
                sortOrder = await _repository.GetMaxSortOrderAsync(cancellationToken) + 1;
            }

            // Create entity
            var cargoTypeMaster = new Domain.Entities.CargoTypeMaster(
                request.Code,
                request.Name,
                request.Description,
                request.Category,
                request.RequiresSpecialHandling,
                request.IsHazardous,
                request.RequiresTemperatureControl,
                request.MinTemperatureCelsius,
                request.MaxTemperatureCelsius,
                request.HandlingInstructions,
                request.SafetyRequirements,
                sortOrder,
                request.IconUrl,
                request.AdditionalProperties);

            // Add to repository
            _repository.Add(cargoTypeMaster);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Publish integration event
            await _messageBroker.PublishAsync("cargotypemaster.created", new
            {
                Id = cargoTypeMaster.Id,
                Code = cargoTypeMaster.Code,
                Name = cargoTypeMaster.Name,
                Category = cargoTypeMaster.Category,
                RequiresSpecialHandling = cargoTypeMaster.RequiresSpecialHandling,
                IsHazardous = cargoTypeMaster.IsHazardous,
                RequiresTemperatureControl = cargoTypeMaster.RequiresTemperatureControl,
                IsActive = cargoTypeMaster.IsActive,
                CreatedAt = cargoTypeMaster.CreatedAt
            }, cancellationToken);

            // Map to DTO and return
            var result = _mapper.Map<CargoTypeMasterDto>(cargoTypeMaster);

            _logger.LogInformation("Successfully created cargo type master with ID: {Id}", cargoTypeMaster.Id);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating cargo type master with code: {Code}", request.Code);
            throw;
        }
    }
}

