using Shared.Domain.Common;

namespace OrderManagement.Domain.Events;

public class RfqAwardResetDomainEvent : DomainEvent
{
    public Guid RfqId { get; }
    public Guid PreviousAwardedBidId { get; }
    public Guid PreviousAwardedBidderId { get; }
    public string PreviousAwardedBidderName { get; }
    public Guid AdminUserId { get; }
    public string ResetReason { get; }

    public RfqAwardResetDomainEvent(
        Guid rfqId,
        Guid previousAwardedBidId,
        Guid previousAwardedBidderId,
        string previousAwardedBidderName,
        Guid adminUserId,
        string resetReason)
    {
        RfqId = rfqId;
        PreviousAwardedBidId = previousAwardedBidId;
        PreviousAwardedBidderId = previousAwardedBidderId;
        PreviousAwardedBidderName = previousAwardedBidderName;
        AdminUserId = adminUserId;
        ResetReason = resetReason;
    }
}
