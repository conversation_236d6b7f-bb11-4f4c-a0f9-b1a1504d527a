using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.ValueObjects;
using FinancialPayment.Infrastructure.Data;

namespace FinancialPayment.Infrastructure.Services;

public class PaymentDataAggregator : IPaymentDataAggregator
{
    private readonly FinancialPaymentDbContext _context;
    private readonly ILogger<PaymentDataAggregator> _logger;

    public PaymentDataAggregator(FinancialPaymentDbContext context, ILogger<PaymentDataAggregator> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<PaymentAnalyticsSummary> AggregatePaymentDataAsync(PaymentAnalyticsFilter filter)
    {
        _logger.LogDebug("Aggregating payment data for period {Start} to {End}",
            filter.FromDate, filter.ToDate);

        try
        {
            var fromDate = filter.FromDate ?? DateTime.UtcNow.AddDays(-30);
            var toDate = filter.ToDate ?? DateTime.UtcNow;

            // Get escrow transactions as primary payment data
            var escrowQuery = _context.EscrowTransactions
                .Where(et => et.CreatedAt >= fromDate && et.CreatedAt <= toDate);

            // Apply filters
            if (filter.MinAmount.HasValue)
            {
                escrowQuery = escrowQuery.Where(et => et.Amount.Amount >= filter.MinAmount.Value);
            }

            if (filter.MaxAmount.HasValue)
            {
                escrowQuery = escrowQuery.Where(et => et.Amount.Amount <= filter.MaxAmount.Value);
            }

            if (!string.IsNullOrEmpty(filter.Currency))
            {
                escrowQuery = escrowQuery.Where(et => et.Amount.Currency == filter.Currency);
            }

            var escrowTransactions = await escrowQuery.ToListAsync();

            // Calculate basic metrics
            var totalTransactions = escrowTransactions.Count;
            var totalVolume = escrowTransactions.Aggregate(Money.Zero("INR"), 
                (sum, et) => sum + et.Amount);

            var successfulTransactions = escrowTransactions.Count(et => 
                et.Status == EscrowTransactionStatus.Completed);
            var failedTransactions = escrowTransactions.Count(et => 
                et.Status == EscrowTransactionStatus.Failed);

            var successRate = totalTransactions > 0 ? (decimal)successfulTransactions / totalTransactions * 100 : 0;
            var failureRate = totalTransactions > 0 ? (decimal)failedTransactions / totalTransactions * 100 : 0;

            // Calculate refund data (simplified)
            var refundedTransactions = escrowTransactions.Count(et => 
                et.Status == EscrowTransactionStatus.Refunded);
            var refundRate = totalTransactions > 0 ? (decimal)refundedTransactions / totalTransactions * 100 : 0;
            var totalRefunds = escrowTransactions
                .Where(et => et.Status == EscrowTransactionStatus.Refunded)
                .Aggregate(Money.Zero("INR"), (sum, et) => sum + et.Amount);

            var averageTransactionValue = totalTransactions > 0 
                ? new Money(totalVolume.Amount / totalTransactions, totalVolume.Currency)
                : Money.Zero("INR");

            // Get payment method distribution (simplified)
            var paymentMethodDistribution = new Dictionary<string, decimal>
            {
                { "escrow", 100 } // Since we're only looking at escrow transactions
            };

            // Get gateway distribution (simplified)
            var gatewayDistribution = new Dictionary<string, decimal>
            {
                { "razorpay", 70 },
                { "stripe", 20 },
                { "paypal", 10 }
            };

            // Get volume trends
            var volumeTrends = await AggregateVolumeDataAsync(filter);
            var transactionTrends = await AggregateTransactionCountAsync(filter);

            return new PaymentAnalyticsSummary
            {
                PeriodStart = fromDate,
                PeriodEnd = toDate,
                TotalVolume = totalVolume,
                TotalTransactions = totalTransactions,
                AverageTransactionValue = averageTransactionValue,
                SuccessRate = successRate,
                FailureRate = failureRate,
                RefundRate = refundRate,
                TotalRefunds = totalRefunds,
                PaymentMethodDistribution = paymentMethodDistribution,
                GatewayDistribution = gatewayDistribution,
                Volumetrends = volumeTrends,
                TransactionTrends = transactionTrends
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error aggregating payment data");
            throw;
        }
    }

    public async Task<List<PaymentTrend>> AggregateVolumeDataAsync(PaymentAnalyticsFilter filter)
    {
        try
        {
            var fromDate = filter.FromDate ?? DateTime.UtcNow.AddDays(-30);
            var toDate = filter.ToDate ?? DateTime.UtcNow;

            var trends = new List<PaymentTrend>();

            // Group by the specified period
            var groupBy = filter.GroupBy?.ToLowerInvariant() ?? "day";

            var escrowTransactions = await _context.EscrowTransactions
                .Where(et => et.CreatedAt >= fromDate && et.CreatedAt <= toDate)
                .ToListAsync();

            switch (groupBy)
            {
                case "hour":
                    trends = escrowTransactions
                        .GroupBy(et => new { et.CreatedAt.Date, et.CreatedAt.Hour })
                        .Select(g => new PaymentTrend
                        {
                            Date = g.Key.Date.AddHours(g.Key.Hour),
                            Value = g.Sum(et => et.Amount.Amount),
                            Label = g.Key.Date.AddHours(g.Key.Hour).ToString("MMM dd HH:mm")
                        })
                        .OrderBy(t => t.Date)
                        .ToList();
                    break;

                case "week":
                    trends = escrowTransactions
                        .GroupBy(et => GetWeekOfYear(et.CreatedAt))
                        .Select(g => new PaymentTrend
                        {
                            Date = GetDateFromWeek(g.Key.Year, g.Key.Week),
                            Value = g.Sum(et => et.Amount.Amount),
                            Label = $"Week {g.Key.Week}, {g.Key.Year}"
                        })
                        .OrderBy(t => t.Date)
                        .ToList();
                    break;

                case "month":
                    trends = escrowTransactions
                        .GroupBy(et => new { et.CreatedAt.Year, et.CreatedAt.Month })
                        .Select(g => new PaymentTrend
                        {
                            Date = new DateTime(g.Key.Year, g.Key.Month, 1),
                            Value = g.Sum(et => et.Amount.Amount),
                            Label = new DateTime(g.Key.Year, g.Key.Month, 1).ToString("MMM yyyy")
                        })
                        .OrderBy(t => t.Date)
                        .ToList();
                    break;

                default: // day
                    trends = escrowTransactions
                        .GroupBy(et => et.CreatedAt.Date)
                        .Select(g => new PaymentTrend
                        {
                            Date = g.Key,
                            Value = g.Sum(et => et.Amount.Amount),
                            Label = g.Key.ToString("MMM dd")
                        })
                        .OrderBy(t => t.Date)
                        .ToList();
                    break;
            }

            return trends;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error aggregating volume data");
            throw;
        }
    }

    public async Task<List<PaymentTrend>> AggregateTransactionCountAsync(PaymentAnalyticsFilter filter)
    {
        try
        {
            var fromDate = filter.FromDate ?? DateTime.UtcNow.AddDays(-30);
            var toDate = filter.ToDate ?? DateTime.UtcNow;

            var groupBy = filter.GroupBy?.ToLowerInvariant() ?? "day";

            var escrowTransactions = await _context.EscrowTransactions
                .Where(et => et.CreatedAt >= fromDate && et.CreatedAt <= toDate)
                .ToListAsync();

            var trends = new List<PaymentTrend>();

            switch (groupBy)
            {
                case "hour":
                    trends = escrowTransactions
                        .GroupBy(et => new { et.CreatedAt.Date, et.CreatedAt.Hour })
                        .Select(g => new PaymentTrend
                        {
                            Date = g.Key.Date.AddHours(g.Key.Hour),
                            Value = g.Count(),
                            Label = g.Key.Date.AddHours(g.Key.Hour).ToString("MMM dd HH:mm")
                        })
                        .OrderBy(t => t.Date)
                        .ToList();
                    break;

                default: // day
                    trends = escrowTransactions
                        .GroupBy(et => et.CreatedAt.Date)
                        .Select(g => new PaymentTrend
                        {
                            Date = g.Key,
                            Value = g.Count(),
                            Label = g.Key.ToString("MMM dd")
                        })
                        .OrderBy(t => t.Date)
                        .ToList();
                    break;
            }

            return trends;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error aggregating transaction count");
            throw;
        }
    }

    public async Task<Dictionary<string, decimal>> AggregatePaymentMethodDataAsync(PaymentAnalyticsFilter filter)
    {
        try
        {
            // For demo purposes, return mock data
            // In a real implementation, you would query actual payment method data
            await Task.CompletedTask;

            return new Dictionary<string, decimal>
            {
                { "Credit Card", 45 },
                { "Debit Card", 25 },
                { "Net Banking", 15 },
                { "UPI", 10 },
                { "Wallet", 5 }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error aggregating payment method data");
            throw;
        }
    }

    public async Task<Dictionary<string, decimal>> AggregateGatewayDataAsync(PaymentAnalyticsFilter filter)
    {
        try
        {
            // For demo purposes, return mock data
            // In a real implementation, you would query actual gateway data
            await Task.CompletedTask;

            return new Dictionary<string, decimal>
            {
                { "Razorpay", 60 },
                { "Stripe", 25 },
                { "PayPal", 10 },
                { "Square", 5 }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error aggregating gateway data");
            throw;
        }
    }

    public async Task<List<PaymentTrend>> AggregateSuccessRateAsync(PaymentAnalyticsFilter filter)
    {
        try
        {
            var fromDate = filter.FromDate ?? DateTime.UtcNow.AddDays(-30);
            var toDate = filter.ToDate ?? DateTime.UtcNow;

            var escrowTransactions = await _context.EscrowTransactions
                .Where(et => et.CreatedAt >= fromDate && et.CreatedAt <= toDate)
                .ToListAsync();

            var trends = escrowTransactions
                .GroupBy(et => et.CreatedAt.Date)
                .Select(g => new PaymentTrend
                {
                    Date = g.Key,
                    Value = g.Count() > 0 ? 
                        (decimal)g.Count(et => et.Status == EscrowTransactionStatus.Completed) / g.Count() * 100 : 0,
                    Label = g.Key.ToString("MMM dd")
                })
                .OrderBy(t => t.Date)
                .ToList();

            return trends;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error aggregating success rate");
            throw;
        }
    }

    private static (int Year, int Week) GetWeekOfYear(DateTime date)
    {
        var culture = System.Globalization.CultureInfo.CurrentCulture;
        var calendar = culture.Calendar;
        var weekOfYear = calendar.GetWeekOfYear(date, 
            culture.DateTimeFormat.CalendarWeekRule, 
            culture.DateTimeFormat.FirstDayOfWeek);
        
        return (date.Year, weekOfYear);
    }

    private static DateTime GetDateFromWeek(int year, int week)
    {
        var jan1 = new DateTime(year, 1, 1);
        var daysOffset = (int)System.Globalization.CultureInfo.CurrentCulture.DateTimeFormat.FirstDayOfWeek - (int)jan1.DayOfWeek;
        var firstWeek = jan1.AddDays(daysOffset);
        return firstWeek.AddDays((week - 1) * 7);
    }
}
