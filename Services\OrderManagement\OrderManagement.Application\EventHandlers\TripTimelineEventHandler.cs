using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Domain.Entities;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Application.EventHandlers;

// Integration events from Trip Management Service
public record TripAssignedIntegrationEvent : INotification
{
    public Guid TripId { get; init; }
    public Guid OrderId { get; init; }
    public string TripNumber { get; init; } = string.Empty;
    public Guid DriverId { get; init; }
    public Guid VehicleId { get; init; }
    public Guid CarrierId { get; init; }
    public DateTime AssignedAt { get; init; }
}

public record TripStartedIntegrationEvent : INotification
{
    public Guid TripId { get; init; }
    public Guid OrderId { get; init; }
    public string TripNumber { get; init; } = string.Empty;
    public Guid CarrierId { get; init; }
    public Guid DriverId { get; init; }
    public Guid VehicleId { get; init; }
    public DateTime StartedAt { get; init; }
    public string StartLocation { get; init; } = string.Empty;
}

public record TripLocationUpdatedIntegrationEvent : INotification
{
    public Guid TripId { get; init; }
    public Guid OrderId { get; init; }
    public string TripNumber { get; init; } = string.Empty;
    public string Location { get; init; } = string.Empty;
    public double? Speed { get; init; }
    public double? Heading { get; init; }
    public string Source { get; init; } = string.Empty;
    public DateTime UpdatedAt { get; init; }
    public bool IsSignificantUpdate { get; init; }
}

public record TripExceptionIntegrationEvent : INotification
{
    public Guid TripId { get; init; }
    public Guid OrderId { get; init; }
    public string TripNumber { get; init; } = string.Empty;
    public string ExceptionType { get; init; } = string.Empty;
    public string Severity { get; init; } = string.Empty;
    public string Description { get; init; } = string.Empty;
    public DateTime ReportedAt { get; init; }
    public Guid ReportedBy { get; init; }
}

public class TripTimelineEventHandler :
    INotificationHandler<TripAssignedIntegrationEvent>,
    INotificationHandler<TripStartedIntegrationEvent>,
    INotificationHandler<TripLocationUpdatedIntegrationEvent>,
    INotificationHandler<TripCompletedIntegrationEvent>,
    INotificationHandler<TripExceptionIntegrationEvent>
{
    private readonly IOrderRepository _orderRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<TripTimelineEventHandler> _logger;

    public TripTimelineEventHandler(
        IOrderRepository orderRepository,
        IUnitOfWork unitOfWork,
        ILogger<TripTimelineEventHandler> logger)
    {
        _orderRepository = orderRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task Handle(TripAssignedIntegrationEvent notification, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Processing trip assigned event for order {OrderId}, trip {TripId}",
                notification.OrderId, notification.TripId);

            var order = await _orderRepository.GetByIdAsync(notification.OrderId, cancellationToken);
            if (order == null)
            {
                _logger.LogWarning("Order {OrderId} not found for trip assigned event", notification.OrderId);
                return;
            }

            order.AddTimelineEvent(
                OrderTimelineEventType.TripAssigned,
                $"Trip {notification.TripNumber} assigned to driver {notification.DriverId} and vehicle {notification.VehicleId}",
                additionalData: $"TripId: {notification.TripId}, CarrierId: {notification.CarrierId}",
                notes: $"Trip assigned at {notification.AssignedAt}",
                metadata: new Dictionary<string, object>
                {
                    ["TripId"] = notification.TripId,
                    ["TripNumber"] = notification.TripNumber,
                    ["DriverId"] = notification.DriverId,
                    ["VehicleId"] = notification.VehicleId,
                    ["CarrierId"] = notification.CarrierId,
                    ["AssignedAt"] = notification.AssignedAt
                });

            await _orderRepository.UpdateAsync(order, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Trip assigned timeline event added for order {OrderId}", notification.OrderId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing trip assigned event for order {OrderId}", notification.OrderId);
        }
    }

    public async Task Handle(TripStartedIntegrationEvent notification, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Processing trip started event for order {OrderId}, trip {TripId}",
                notification.OrderId, notification.TripId);

            var order = await _orderRepository.GetByIdAsync(notification.OrderId, cancellationToken);
            if (order == null)
            {
                _logger.LogWarning("Order {OrderId} not found for trip started event", notification.OrderId);
                return;
            }

            order.AddTimelineEvent(
                OrderTimelineEventType.TripStarted,
                $"Trip {notification.TripNumber} started",
                additionalData: $"TripId: {notification.TripId}, StartLocation: {notification.StartLocation}",
                notes: $"Trip started at {notification.StartedAt}",
                metadata: new Dictionary<string, object>
                {
                    ["TripId"] = notification.TripId,
                    ["TripNumber"] = notification.TripNumber,
                    ["StartedAt"] = notification.StartedAt,
                    ["StartLocation"] = notification.StartLocation
                });

            await _orderRepository.UpdateAsync(order, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Trip started timeline event added for order {OrderId}", notification.OrderId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing trip started event for order {OrderId}", notification.OrderId);
        }
    }

    public async Task Handle(TripLocationUpdatedIntegrationEvent notification, CancellationToken cancellationToken)
    {
        try
        {
            // Only log significant location updates to avoid timeline spam
            if (notification.IsSignificantUpdate)
            {
                _logger.LogInformation("Processing significant location update for order {OrderId}, trip {TripId}",
                    notification.OrderId, notification.TripId);

                var order = await _orderRepository.GetByIdAsync(notification.OrderId, cancellationToken);
                if (order == null)
                {
                    _logger.LogWarning("Order {OrderId} not found for location update event", notification.OrderId);
                    return;
                }

                order.AddTimelineEvent(
                    OrderTimelineEventType.LocationUpdated,
                    $"Trip location updated: {notification.Location}",
                    additionalData: $"TripId: {notification.TripId}, Speed: {notification.Speed}, Source: {notification.Source}",
                    notes: $"Location updated at {notification.UpdatedAt}",
                    metadata: new Dictionary<string, object>
                    {
                        ["TripId"] = notification.TripId,
                        ["TripNumber"] = notification.TripNumber,
                        ["Location"] = notification.Location,
                        ["Speed"] = notification.Speed ?? 0,
                        ["Heading"] = notification.Heading ?? 0,
                        ["Source"] = notification.Source,
                        ["UpdatedAt"] = notification.UpdatedAt,
                        ["IsSignificantUpdate"] = notification.IsSignificantUpdate
                    });

                await _orderRepository.UpdateAsync(order, cancellationToken);
                await _unitOfWork.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("Location update timeline event added for order {OrderId}", notification.OrderId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing location update event for order {OrderId}", notification.OrderId);
        }
    }

    public async Task Handle(TripCompletedIntegrationEvent notification, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Processing trip completed event for order {OrderId}, trip {TripId}",
                notification.OrderId, notification.TripId);

            var order = await _orderRepository.GetByIdAsync(notification.OrderId, cancellationToken);
            if (order == null)
            {
                _logger.LogWarning("Order {OrderId} not found for trip completed event", notification.OrderId);
                return;
            }

            order.AddTimelineEvent(
                OrderTimelineEventType.TripCompleted,
                $"Trip {notification.TripNumber} completed successfully",
                additionalData: $"TripId: {notification.TripId}, ActualDistance: {notification.ActualDistanceKm}km",
                notes: $"Trip completed at {notification.CompletedAt}",
                metadata: new Dictionary<string, object>
                {
                    ["TripId"] = notification.TripId,
                    ["TripNumber"] = notification.TripNumber,
                    ["CompletedAt"] = notification.CompletedAt,
                    ["ActualDistanceKm"] = notification.ActualDistanceKm ?? 0
                });

            await _orderRepository.UpdateAsync(order, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Trip completed timeline event added for order {OrderId}", notification.OrderId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing trip completed event for order {OrderId}", notification.OrderId);
        }
    }

    public async Task Handle(TripExceptionIntegrationEvent notification, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Processing trip exception event for order {OrderId}, trip {TripId}",
                notification.OrderId, notification.TripId);

            var order = await _orderRepository.GetByIdAsync(notification.OrderId, cancellationToken);
            if (order == null)
            {
                _logger.LogWarning("Order {OrderId} not found for trip exception event", notification.OrderId);
                return;
            }

            order.AddTimelineEvent(
                OrderTimelineEventType.ExceptionReported,
                $"Trip exception reported: {notification.ExceptionType} - {notification.Description}",
                additionalData: $"TripId: {notification.TripId}, Severity: {notification.Severity}",
                notes: $"Exception reported at {notification.ReportedAt} by {notification.ReportedBy}",
                metadata: new Dictionary<string, object>
                {
                    ["TripId"] = notification.TripId,
                    ["TripNumber"] = notification.TripNumber,
                    ["ExceptionType"] = notification.ExceptionType,
                    ["Severity"] = notification.Severity,
                    ["Description"] = notification.Description,
                    ["ReportedAt"] = notification.ReportedAt,
                    ["ReportedBy"] = notification.ReportedBy
                });

            await _orderRepository.UpdateAsync(order, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Trip exception timeline event added for order {OrderId}", notification.OrderId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing trip exception event for order {OrderId}", notification.OrderId);
        }
    }
}
