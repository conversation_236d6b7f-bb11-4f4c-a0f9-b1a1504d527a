using AuditCompliance.Domain.Enums;

namespace AuditCompliance.Application.DTOs.V2;

/// <summary>
/// Enhanced audit log response for API V2
/// </summary>
public class AuditLogResponseV2Dto
{
    public Guid AuditLogId { get; set; }
    public string Status { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public string Version { get; set; } = "2.0";
    public Dictionary<string, string> Links { get; set; } = new();
}

/// <summary>
/// Enhanced audit log detail for API V2
/// </summary>
public class AuditLogDetailV2Dto
{
    public Guid Id { get; set; }
    public string EventType { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public string EntityType { get; set; } = string.Empty;
    public string Action { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public Guid UserId { get; set; }
    public string UserName { get; set; } = string.Empty;
    public string UserRole { get; set; } = string.Empty;
    public Guid? EntityId { get; set; }
    public string? OldValues { get; set; }
    public string? NewValues { get; set; }
    public DateTime Timestamp { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
    public string? SessionId { get; set; }
    public string? CorrelationId { get; set; }
    public List<string>? ComplianceFlags { get; set; }
    public string Version { get; set; } = "2.0";
    public AuditLogMetadataV2Dto Metadata { get; set; } = new();
    public Dictionary<string, string> Links { get; set; } = new();
}

/// <summary>
/// Enhanced audit log metadata for API V2
/// </summary>
public class AuditLogMetadataV2Dto
{
    public DateTime CreatedAt { get; set; }
    public DateTime RetentionDate { get; set; }
    public bool IsArchived { get; set; }
    public DateTime? ArchiveDate { get; set; }
    public float ComplianceScore { get; set; }
    public string RiskLevel { get; set; } = string.Empty;
}

/// <summary>
/// Enhanced audit trail response for API V2
/// </summary>
public class AuditTrailResponseV2Dto
{
    public List<AuditLogSummaryV2Dto> Data { get; set; } = new();
    public PaginationV2Dto Pagination { get; set; } = new();
    public AuditTrailFiltersV2Dto Filters { get; set; } = new();
    public AuditTrailSummaryV2Dto Summary { get; set; } = new();
    public string Version { get; set; } = "2.0";
    public DateTime Timestamp { get; set; }
    public Dictionary<string, string> Links { get; set; } = new();
}

/// <summary>
/// Enhanced audit log summary for API V2
/// </summary>
public class AuditLogSummaryV2Dto
{
    public Guid Id { get; set; }
    public string EventType { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public string EntityType { get; set; } = string.Empty;
    public string Action { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public string UserRole { get; set; } = string.Empty;
    public Guid? EntityId { get; set; }
    public DateTime Timestamp { get; set; }
    public float ComplianceScore { get; set; }
    public string RiskLevel { get; set; } = string.Empty;
    public Dictionary<string, string> Links { get; set; } = new();
}

/// <summary>
/// Enhanced pagination for API V2
/// </summary>
public class PaginationV2Dto
{
    public int CurrentPage { get; set; }
    public int PageSize { get; set; }
    public int TotalCount { get; set; }
    public int TotalPages { get; set; }
    public bool HasPreviousPage { get; set; }
    public bool HasNextPage { get; set; }
}

/// <summary>
/// Enhanced audit trail filters for API V2
/// </summary>
public class AuditTrailFiltersV2Dto
{
    public Dictionary<string, object> Applied { get; set; } = new();
    public Dictionary<string, object> Available { get; set; } = new();
}

/// <summary>
/// Enhanced audit trail summary for API V2
/// </summary>
public class AuditTrailSummaryV2Dto
{
    public int TotalEvents { get; set; }
    public int CriticalEvents { get; set; }
    public int HighSeverityEvents { get; set; }
    public int UniqueUsers { get; set; }
    public int UniqueEntities { get; set; }
    public object DateRange { get; set; } = new();
}

/// <summary>
/// Enhanced security events response for API V2
/// </summary>
public class SecurityEventsResponseV2Dto
{
    public List<SecurityEventV2Dto> Events { get; set; } = new();
    public SecurityEventsSummaryV2Dto Summary { get; set; } = new();
    public string Version { get; set; } = "2.0";
    public DateTime Timestamp { get; set; }
}

/// <summary>
/// Enhanced security event for API V2
/// </summary>
public class SecurityEventV2Dto
{
    public Guid Id { get; set; }
    public string EventType { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public Guid UserId { get; set; }
    public string UserName { get; set; } = string.Empty;
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
    public DateTime Timestamp { get; set; }
    public string ThreatLevel { get; set; } = string.Empty;
    public bool RequiresAction { get; set; }
    public Dictionary<string, string> Links { get; set; } = new();
}

/// <summary>
/// Enhanced security events summary for API V2
/// </summary>
public class SecurityEventsSummaryV2Dto
{
    public int TotalEvents { get; set; }
    public int CriticalEvents { get; set; }
    public int HighThreatEvents { get; set; }
    public int UniqueUsers { get; set; }
    public int UniqueIpAddresses { get; set; }
    public object TimeRange { get; set; } = new();
}

/// <summary>
/// Enhanced export response for API V2
/// </summary>
public class ExportResponseV2Dto
{
    public Guid ExportId { get; set; }
    public string Status { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public DateTime EstimatedCompletionTime { get; set; }
    public string Format { get; set; } = string.Empty;
    public string Version { get; set; } = "2.0";
    public Dictionary<string, string> Links { get; set; } = new();
}

/// <summary>
/// Enhanced error response for API V2
/// </summary>
public class ErrorResponseV2Dto
{
    public string Error { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public string Version { get; set; } = "2.0";
    public Dictionary<string, object>? Details { get; set; }
    public string? TraceId { get; set; }
}

/// <summary>
/// Enhanced create audit log command for API V2
/// </summary>
public class CreateAuditLogV2Command
{
    public string EventType { get; set; } = string.Empty;
    public AuditSeverity Severity { get; set; }
    public string EntityType { get; set; } = string.Empty;
    public string Action { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public Guid UserId { get; set; }
    public string UserName { get; set; } = string.Empty;
    public string UserRole { get; set; } = string.Empty;
    public Guid? EntityId { get; set; }
    public string? OldValues { get; set; }
    public string? NewValues { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
    public string? SessionId { get; set; }
    public string? CorrelationId { get; set; }
    public List<string>? ComplianceFlags { get; set; }
    public Dictionary<string, object>? AdditionalMetadata { get; set; }
}

/// <summary>
/// Enhanced audit trail query for API V2
/// </summary>
public class GetAuditTrailV2Query
{
    public Guid? UserId { get; set; }
    public string? EntityType { get; set; }
    public Guid? EntityId { get; set; }
    public string? EventType { get; set; }
    public AuditSeverity? MinSeverity { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public string? SearchTerm { get; set; }
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 50;
    public string? SortBy { get; set; } = "Timestamp";
    public string? SortOrder { get; set; } = "desc";
    public List<string>? ComplianceFlags { get; set; }
    public string? RiskLevel { get; set; }
    public bool IncludeArchived { get; set; } = false;
}

/// <summary>
/// Enhanced security events query for API V2
/// </summary>
public class GetSecurityEventsV2Query
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string? Severity { get; set; }
    public int PageSize { get; set; } = 50;
    public int PageNumber { get; set; } = 1;
    public string? ThreatLevel { get; set; }
    public bool RequiresActionOnly { get; set; } = false;
}

/// <summary>
/// Enhanced export audit trail command for API V2
/// </summary>
public class ExportAuditTrailV2Command
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Format { get; set; } = "CSV"; // CSV, JSON, PDF, Excel
    public List<string>? Columns { get; set; }
    public GetAuditTrailV2Query? Filters { get; set; }
    public bool IncludeMetadata { get; set; } = true;
    public bool CompressOutput { get; set; } = false;
    public string? Password { get; set; } // For password-protected exports
}

/// <summary>
/// API version information DTO
/// </summary>
public class ApiVersionInfoV2Dto
{
    public string Version { get; set; } = "2.0";
    public string Name { get; set; } = "Audit & Compliance API V2";
    public DateTime ReleaseDate { get; set; }
    public string Status { get; set; } = "Stable";
    public List<string> Features { get; set; } = new();
    public List<string> BreakingChanges { get; set; } = new();
    public Dictionary<string, string> Links { get; set; } = new();
    public DeprecationInfoV2Dto? Deprecation { get; set; }
}

/// <summary>
/// API deprecation information DTO
/// </summary>
public class DeprecationInfoV2Dto
{
    public bool IsDeprecated { get; set; }
    public DateTime? DeprecationDate { get; set; }
    public DateTime? SunsetDate { get; set; }
    public string? ReplacementVersion { get; set; }
    public string? MigrationGuide { get; set; }
}
