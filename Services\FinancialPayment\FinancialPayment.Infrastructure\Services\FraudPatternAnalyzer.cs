using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Infrastructure.Services;

public class FraudPatternAnalyzer : IFraudPatternAnalyzer
{
    private readonly IFraudAssessmentRepository _assessmentRepository;
    private readonly ILogger<FraudPatternAnalyzer> _logger;
    private readonly FraudDetectionConfiguration _configuration;

    public FraudPatternAnalyzer(
        IFraudAssessmentRepository assessmentRepository,
        ILogger<FraudPatternAnalyzer> logger,
        IOptions<FraudDetectionConfiguration> configuration)
    {
        _assessmentRepository = assessmentRepository;
        _logger = logger;
        _configuration = configuration.Value;
    }

    public async Task<List<FraudPattern>> AnalyzeUserPatternsAsync(Guid userId)
    {
        _logger.LogDebug("Analyzing fraud patterns for user {UserId}", userId);

        try
        {
            var patterns = new List<FraudPattern>();
            var fromDate = DateTime.UtcNow.AddDays(-30);
            var userAssessments = await _assessmentRepository.GetByUserIdAsync(userId, fromDate);

            if (!userAssessments.Any())
            {
                return patterns;
            }

            // Analyze transaction frequency patterns
            patterns.AddRange(AnalyzeFrequencyPatterns(userAssessments));

            // Analyze amount patterns
            patterns.AddRange(AnalyzeAmountPatterns(userAssessments));

            // Analyze time-based patterns
            patterns.AddRange(AnalyzeTimePatterns(userAssessments));

            // Analyze location patterns
            patterns.AddRange(AnalyzeLocationPatterns(userAssessments));

            // Analyze payment method patterns
            patterns.AddRange(AnalyzePaymentMethodPatterns(userAssessments));

            _logger.LogDebug("Found {Count} patterns for user {UserId}", patterns.Count, userId);
            return patterns;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing patterns for user {UserId}", userId);
            return new List<FraudPattern>();
        }
    }

    public async Task<List<FraudPattern>> AnalyzeTransactionPatternsAsync(DateTime fromDate, DateTime toDate)
    {
        _logger.LogDebug("Analyzing transaction patterns from {FromDate} to {ToDate}", fromDate, toDate);

        try
        {
            var patterns = new List<FraudPattern>();

            // Get high-risk assessments in the period
            var highRiskAssessments = await _assessmentRepository.GetByRiskLevelAsync(
                Domain.Entities.FraudRiskLevel.High, fromDate, toDate);
            var criticalRiskAssessments = await _assessmentRepository.GetByRiskLevelAsync(
                Domain.Entities.FraudRiskLevel.Critical, fromDate, toDate);

            var allHighRisk = highRiskAssessments.Concat(criticalRiskAssessments).ToList();

            if (!allHighRisk.Any())
            {
                return patterns;
            }

            // Analyze common characteristics in high-risk transactions
            patterns.AddRange(AnalyzeHighRiskPatterns(allHighRisk));

            _logger.LogDebug("Found {Count} transaction patterns", patterns.Count);
            return patterns;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing transaction patterns");
            return new List<FraudPattern>();
        }
    }

    public async Task<bool> IsVelocityExceededAsync(Guid userId, Money amount, TimeSpan timeWindow)
    {
        try
        {
            var fromDate = DateTime.UtcNow.Subtract(timeWindow);
            var userAssessments = await _assessmentRepository.GetByUserIdAsync(userId, fromDate);

            var transactionCount = userAssessments.Count;
            var totalAmount = userAssessments.Aggregate(Money.Zero(amount.Currency), 
                (sum, assessment) => sum + assessment.TransactionAmount);

            var isCountExceeded = transactionCount > _configuration.MaxTransactionsPerWindow;
            var isAmountExceeded = totalAmount.Amount > _configuration.MaxAmountPerWindow;

            if (isCountExceeded || isAmountExceeded)
            {
                _logger.LogWarning("Velocity exceeded for user {UserId}: {Count} transactions, {Amount} total",
                    userId, transactionCount, totalAmount);
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking velocity for user {UserId}", userId);
            return false;
        }
    }

    public async Task<bool> IsLocationSuspiciousAsync(string ipAddress, Guid? userId = null)
    {
        try
        {
            // Check if IP is from high-risk country
            var location = await GetLocationFromIpAsync(ipAddress);
            if (_configuration.HighRiskCountries.Contains(location.Country))
            {
                _logger.LogWarning("Suspicious location detected: {Country} for IP {IpAddress}", 
                    location.Country, ipAddress);
                return true;
            }

            // If user provided, check for unusual location
            if (userId.HasValue)
            {
                var isUnusual = await IsUnusualLocationForUserAsync(userId.Value, location);
                if (isUnusual)
                {
                    _logger.LogWarning("Unusual location for user {UserId}: {Country}", userId, location.Country);
                    return true;
                }
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking location for IP {IpAddress}", ipAddress);
            return false;
        }
    }

    public async Task<bool> IsDeviceSuspiciousAsync(string deviceFingerprint, Guid? userId = null)
    {
        try
        {
            if (userId.HasValue)
            {
                var fromDate = DateTime.UtcNow.AddDays(-30);
                var userAssessments = await _assessmentRepository.GetByUserIdAsync(userId.Value, fromDate);

                var knownDevices = userAssessments
                    .Where(a => !string.IsNullOrEmpty(a.DeviceFingerprint))
                    .Select(a => a.DeviceFingerprint)
                    .Distinct()
                    .ToList();

                if (knownDevices.Any() && !knownDevices.Contains(deviceFingerprint))
                {
                    _logger.LogWarning("Unknown device detected for user {UserId}: {DeviceFingerprint}", 
                        userId, deviceFingerprint);
                    return true;
                }
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking device {DeviceFingerprint}", deviceFingerprint);
            return false;
        }
    }

    private List<FraudPattern> AnalyzeFrequencyPatterns(List<Domain.Entities.FraudAssessment> assessments)
    {
        var patterns = new List<FraudPattern>();

        // Check for rapid-fire transactions
        var rapidTransactions = assessments
            .OrderBy(a => a.AssessedAt)
            .Zip(assessments.Skip(1).OrderBy(a => a.AssessedAt), (current, next) => new
            {
                TimeDiff = next.AssessedAt - current.AssessedAt,
                Current = current,
                Next = next
            })
            .Where(pair => pair.TimeDiff.TotalMinutes < 5)
            .ToList();

        if (rapidTransactions.Count > 3)
        {
            patterns.Add(new FraudPattern
            {
                PatternType = "RapidTransactions",
                Description = $"Multiple transactions within short time periods ({rapidTransactions.Count} pairs)",
                Frequency = rapidTransactions.Count,
                RiskScore = Math.Min(90, rapidTransactions.Count * 15),
                FirstOccurrence = rapidTransactions.First().Current.AssessedAt,
                LastOccurrence = rapidTransactions.Last().Next.AssessedAt
            });
        }

        return patterns;
    }

    private List<FraudPattern> AnalyzeAmountPatterns(List<Domain.Entities.FraudAssessment> assessments)
    {
        var patterns = new List<FraudPattern>();

        if (!assessments.Any()) return patterns;

        var amounts = assessments.Select(a => a.TransactionAmount.Amount).ToList();
        var avgAmount = amounts.Average();
        var maxAmount = amounts.Max();

        // Check for unusually large transactions
        var largeTransactions = assessments.Where(a => a.TransactionAmount.Amount > avgAmount * 3).ToList();
        if (largeTransactions.Any())
        {
            patterns.Add(new FraudPattern
            {
                PatternType = "UnusuallyLargeAmounts",
                Description = $"Transactions significantly larger than user's average ({largeTransactions.Count} transactions)",
                Frequency = largeTransactions.Count,
                RiskScore = Math.Min(80, largeTransactions.Count * 20),
                FirstOccurrence = largeTransactions.Min(t => t.AssessedAt),
                LastOccurrence = largeTransactions.Max(t => t.AssessedAt)
            });
        }

        // Check for round number patterns (potential testing)
        var roundAmounts = assessments.Where(a => a.TransactionAmount.Amount % 100 == 0).ToList();
        if (roundAmounts.Count > assessments.Count * 0.7)
        {
            patterns.Add(new FraudPattern
            {
                PatternType = "RoundAmountPattern",
                Description = "High frequency of round number transactions (potential testing)",
                Frequency = roundAmounts.Count,
                RiskScore = 60,
                FirstOccurrence = roundAmounts.Min(t => t.AssessedAt),
                LastOccurrence = roundAmounts.Max(t => t.AssessedAt)
            });
        }

        return patterns;
    }

    private List<FraudPattern> AnalyzeTimePatterns(List<Domain.Entities.FraudAssessment> assessments)
    {
        var patterns = new List<FraudPattern>();

        // Check for unusual time patterns (e.g., many transactions at odd hours)
        var oddHourTransactions = assessments
            .Where(a => a.AssessedAt.Hour < 6 || a.AssessedAt.Hour > 23)
            .ToList();

        if (oddHourTransactions.Count > assessments.Count * 0.5)
        {
            patterns.Add(new FraudPattern
            {
                PatternType = "OddHourActivity",
                Description = "High frequency of transactions during unusual hours",
                Frequency = oddHourTransactions.Count,
                RiskScore = 50,
                FirstOccurrence = oddHourTransactions.Min(t => t.AssessedAt),
                LastOccurrence = oddHourTransactions.Max(t => t.AssessedAt)
            });
        }

        return patterns;
    }

    private List<FraudPattern> AnalyzeLocationPatterns(List<Domain.Entities.FraudAssessment> assessments)
    {
        var patterns = new List<FraudPattern>();

        // Check for multiple IP addresses
        var uniqueIps = assessments.Select(a => a.IpAddress).Distinct().ToList();
        if (uniqueIps.Count > 5)
        {
            patterns.Add(new FraudPattern
            {
                PatternType = "MultipleLocations",
                Description = $"Transactions from multiple IP addresses ({uniqueIps.Count} unique IPs)",
                Frequency = uniqueIps.Count,
                RiskScore = Math.Min(70, uniqueIps.Count * 10),
                FirstOccurrence = assessments.Min(a => a.AssessedAt),
                LastOccurrence = assessments.Max(a => a.AssessedAt)
            });
        }

        return patterns;
    }

    private List<FraudPattern> AnalyzePaymentMethodPatterns(List<Domain.Entities.FraudAssessment> assessments)
    {
        var patterns = new List<FraudPattern>();

        // Check for multiple payment methods
        var uniquePaymentMethods = assessments.Select(a => a.PaymentMethod).Distinct().ToList();
        if (uniquePaymentMethods.Count > 3)
        {
            patterns.Add(new FraudPattern
            {
                PatternType = "MultiplePaymentMethods",
                Description = $"Use of multiple payment methods ({string.Join(", ", uniquePaymentMethods)})",
                Frequency = uniquePaymentMethods.Count,
                RiskScore = uniquePaymentMethods.Count * 15,
                FirstOccurrence = assessments.Min(a => a.AssessedAt),
                LastOccurrence = assessments.Max(a => a.AssessedAt)
            });
        }

        return patterns;
    }

    private List<FraudPattern> AnalyzeHighRiskPatterns(List<Domain.Entities.FraudAssessment> assessments)
    {
        var patterns = new List<FraudPattern>();

        // Analyze common payment methods in high-risk transactions
        var paymentMethodGroups = assessments.GroupBy(a => a.PaymentMethod).ToList();
        foreach (var group in paymentMethodGroups.Where(g => g.Count() > 5))
        {
            patterns.Add(new FraudPattern
            {
                PatternType = "HighRiskPaymentMethod",
                Description = $"High frequency of high-risk transactions using {group.Key}",
                Frequency = group.Count(),
                RiskScore = Math.Min(80, group.Count() * 5),
                FirstOccurrence = group.Min(a => a.AssessedAt),
                LastOccurrence = group.Max(a => a.AssessedAt),
                PatternData = new Dictionary<string, object> { { "payment_method", group.Key } }
            });
        }

        return patterns;
    }

    private async Task<(string Country, string City)> GetLocationFromIpAsync(string ipAddress)
    {
        // In a real implementation, you would use a geolocation service
        // For demo purposes, return mock data
        await Task.Delay(50);
        
        // Simple mock logic based on IP ranges
        if (ipAddress.StartsWith("192.168.") || ipAddress.StartsWith("10.") || ipAddress.StartsWith("172."))
        {
            return ("Local", "Local");
        }

        // Mock some countries based on IP patterns
        var mockCountries = new[] { "US", "IN", "GB", "CA", "AU", "DE", "FR", "JP" };
        var hash = ipAddress.GetHashCode();
        var countryIndex = Math.Abs(hash) % mockCountries.Length;
        
        return (mockCountries[countryIndex], "Unknown");
    }

    private async Task<bool> IsUnusualLocationForUserAsync(Guid userId, (string Country, string City) location)
    {
        try
        {
            var fromDate = DateTime.UtcNow.AddDays(-30);
            var userAssessments = await _assessmentRepository.GetByUserIdAsync(userId, fromDate);

            if (!userAssessments.Any()) return false;

            // Get user's typical locations
            var userLocations = new List<string>();
            foreach (var assessment in userAssessments)
            {
                var assessmentLocation = await GetLocationFromIpAsync(assessment.IpAddress);
                userLocations.Add(assessmentLocation.Country);
            }

            var typicalCountries = userLocations.GroupBy(c => c)
                .Where(g => g.Count() > 1)
                .Select(g => g.Key)
                .ToList();

            return typicalCountries.Any() && !typicalCountries.Contains(location.Country);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking unusual location for user {UserId}", userId);
            return false;
        }
    }
}
