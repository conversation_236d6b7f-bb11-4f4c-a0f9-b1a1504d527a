using AuditCompliance.Application.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Text.Json;

namespace AuditCompliance.Infrastructure.Services
{
    /// <summary>
    /// Digital signature service implementation for compliance and security
    /// </summary>
    public class DigitalSignatureService : IDigitalSignatureService
    {
        private readonly ILogger<DigitalSignatureService> _logger;
        private readonly IConfiguration _configuration;
        private readonly X509Certificate2? _signingCertificate;

        public DigitalSignatureService(ILogger<DigitalSignatureService> logger, IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
            _signingCertificate = LoadSigningCertificate();
        }

        public async Task<string> SignContentAsync(string content)
        {
            try
            {
                var contentBytes = Encoding.UTF8.GetBytes(content);
                return await SignContentAsync(contentBytes);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error signing string content");
                throw;
            }
        }

        public async Task<string> SignContentAsync(byte[] content)
        {
            try
            {
                if (_signingCertificate == null)
                {
                    // Fallback to HMAC signing if no certificate is available
                    return await SignWithHmacAsync(content);
                }

                using var rsa = _signingCertificate.GetRSAPrivateKey();
                if (rsa == null)
                {
                    throw new InvalidOperationException("Certificate does not contain a valid RSA private key");
                }

                // Create hash of content
                using var sha256 = SHA256.Create();
                var hash = sha256.ComputeHash(content);

                // Sign the hash
                var signature = rsa.SignHash(hash, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);

                // Create signature metadata
                var signatureMetadata = new DigitalSignatureMetadataDto
                {
                    SignatureId = Guid.NewGuid().ToString("N")[..12],
                    Algorithm = "RSA-SHA256",
                    SignedAt = DateTime.UtcNow,
                    SignedBy = "AuditCompliance Service",
                    CertificateThumbprint = _signingCertificate.Thumbprint,
                    IsValid = true,
                    ExpiresAt = _signingCertificate.NotAfter,
                    AdditionalInfo = new Dictionary<string, object>
                    {
                        ["ContentLength"] = content.Length,
                        ["ContentHash"] = Convert.ToBase64String(hash),
                        ["CertificateSubject"] = _signingCertificate.Subject,
                        ["CertificateIssuer"] = _signingCertificate.Issuer
                    }
                };

                // Combine signature and metadata
                var signatureData = new
                {
                    Signature = Convert.ToBase64String(signature),
                    Metadata = signatureMetadata
                };

                var signatureJson = JsonSerializer.Serialize(signatureData);
                var signatureBase64 = Convert.ToBase64String(Encoding.UTF8.GetBytes(signatureJson));

                _logger.LogInformation("Content signed successfully with certificate {Thumbprint}", _signingCertificate.Thumbprint);
                
                return signatureBase64;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error signing content with certificate");
                throw;
            }
        }

        public async Task<bool> VerifySignatureAsync(string content, string signature)
        {
            try
            {
                var contentBytes = Encoding.UTF8.GetBytes(content);
                return await VerifySignatureAsync(contentBytes, signature);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error verifying string content signature");
                return false;
            }
        }

        public async Task<bool> VerifySignatureAsync(byte[] content, string signature)
        {
            try
            {
                // Decode signature
                var signatureBytes = Convert.FromBase64String(signature);
                var signatureJson = Encoding.UTF8.GetString(signatureBytes);
                var signatureData = JsonSerializer.Deserialize<JsonElement>(signatureJson);

                if (!signatureData.TryGetProperty("Signature", out var signatureElement) ||
                    !signatureData.TryGetProperty("Metadata", out var metadataElement))
                {
                    _logger.LogWarning("Invalid signature format");
                    return false;
                }

                var signatureValue = Convert.FromBase64String(signatureElement.GetString() ?? "");
                var metadata = JsonSerializer.Deserialize<DigitalSignatureMetadataDto>(metadataElement.GetRawText());

                if (metadata == null)
                {
                    _logger.LogWarning("Invalid signature metadata");
                    return false;
                }

                // Check if signature has expired
                if (metadata.ExpiresAt.HasValue && metadata.ExpiresAt.Value < DateTime.UtcNow)
                {
                    _logger.LogWarning("Signature has expired");
                    return false;
                }

                // Verify based on algorithm
                return metadata.Algorithm switch
                {
                    "RSA-SHA256" => await VerifyRsaSignatureAsync(content, signatureValue, metadata),
                    "HMAC-SHA256" => await VerifyHmacSignatureAsync(content, signatureValue, metadata),
                    _ => false
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error verifying signature");
                return false;
            }
        }

        public async Task<DigitalSignatureMetadataDto> GetSignatureMetadataAsync(string signature)
        {
            try
            {
                var signatureBytes = Convert.FromBase64String(signature);
                var signatureJson = Encoding.UTF8.GetString(signatureBytes);
                var signatureData = JsonSerializer.Deserialize<JsonElement>(signatureJson);

                if (signatureData.TryGetProperty("Metadata", out var metadataElement))
                {
                    var metadata = JsonSerializer.Deserialize<DigitalSignatureMetadataDto>(metadataElement.GetRawText());
                    return metadata ?? throw new InvalidOperationException("Failed to deserialize signature metadata");
                }

                throw new InvalidOperationException("Signature does not contain metadata");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error extracting signature metadata");
                throw;
            }
        }

        private async Task<string> SignWithHmacAsync(byte[] content)
        {
            try
            {
                var secretKey = _configuration.GetValue<string>("DigitalSignature:SecretKey") ?? "default-hmac-key";
                
                using var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(secretKey));
                var signature = hmac.ComputeHash(content);

                var signatureMetadata = new DigitalSignatureMetadataDto
                {
                    SignatureId = Guid.NewGuid().ToString("N")[..12],
                    Algorithm = "HMAC-SHA256",
                    SignedAt = DateTime.UtcNow,
                    SignedBy = "AuditCompliance Service",
                    CertificateThumbprint = "HMAC",
                    IsValid = true,
                    AdditionalInfo = new Dictionary<string, object>
                    {
                        ["ContentLength"] = content.Length,
                        ["KeyLength"] = secretKey.Length
                    }
                };

                var signatureData = new
                {
                    Signature = Convert.ToBase64String(signature),
                    Metadata = signatureMetadata
                };

                var signatureJson = JsonSerializer.Serialize(signatureData);
                var signatureBase64 = Convert.ToBase64String(Encoding.UTF8.GetBytes(signatureJson));

                _logger.LogInformation("Content signed successfully with HMAC");
                
                return signatureBase64;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error signing content with HMAC");
                throw;
            }
        }

        private async Task<bool> VerifyRsaSignatureAsync(byte[] content, byte[] signature, DigitalSignatureMetadataDto metadata)
        {
            try
            {
                if (_signingCertificate == null)
                {
                    _logger.LogWarning("No signing certificate available for RSA verification");
                    return false;
                }

                using var rsa = _signingCertificate.GetRSAPublicKey();
                if (rsa == null)
                {
                    _logger.LogWarning("Certificate does not contain a valid RSA public key");
                    return false;
                }

                // Create hash of content
                using var sha256 = SHA256.Create();
                var hash = sha256.ComputeHash(content);

                // Verify signature
                var isValid = rsa.VerifyHash(hash, signature, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);

                _logger.LogDebug("RSA signature verification result: {IsValid}", isValid);
                
                return isValid;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error verifying RSA signature");
                return false;
            }
        }

        private async Task<bool> VerifyHmacSignatureAsync(byte[] content, byte[] signature, DigitalSignatureMetadataDto metadata)
        {
            try
            {
                var secretKey = _configuration.GetValue<string>("DigitalSignature:SecretKey") ?? "default-hmac-key";
                
                using var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(secretKey));
                var expectedSignature = hmac.ComputeHash(content);

                var isValid = signature.SequenceEqual(expectedSignature);

                _logger.LogDebug("HMAC signature verification result: {IsValid}", isValid);
                
                return isValid;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error verifying HMAC signature");
                return false;
            }
        }

        private X509Certificate2? LoadSigningCertificate()
        {
            try
            {
                var certificatePath = _configuration.GetValue<string>("DigitalSignature:CertificatePath");
                var certificatePassword = _configuration.GetValue<string>("DigitalSignature:CertificatePassword");
                var certificateThumbprint = _configuration.GetValue<string>("DigitalSignature:CertificateThumbprint");

                // Try to load from file path
                if (!string.IsNullOrEmpty(certificatePath) && File.Exists(certificatePath))
                {
                    var certificate = new X509Certificate2(certificatePath, certificatePassword);
                    _logger.LogInformation("Signing certificate loaded from file: {Subject}", certificate.Subject);
                    return certificate;
                }

                // Try to load from certificate store
                if (!string.IsNullOrEmpty(certificateThumbprint))
                {
                    using var store = new X509Store(StoreName.My, StoreLocation.LocalMachine);
                    store.Open(OpenFlags.ReadOnly);
                    
                    var certificates = store.Certificates.Find(X509FindType.FindByThumbprint, certificateThumbprint, false);
                    if (certificates.Count > 0)
                    {
                        var certificate = certificates[0];
                        _logger.LogInformation("Signing certificate loaded from store: {Subject}", certificate.Subject);
                        return certificate;
                    }
                }

                _logger.LogWarning("No signing certificate configured. Digital signatures will use HMAC fallback.");
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading signing certificate");
                return null;
            }
        }

        public void Dispose()
        {
            _signingCertificate?.Dispose();
        }
    }
}
