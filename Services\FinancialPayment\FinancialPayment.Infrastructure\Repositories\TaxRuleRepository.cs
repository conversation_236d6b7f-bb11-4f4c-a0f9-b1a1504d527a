using Microsoft.EntityFrameworkCore;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Infrastructure.Data;

namespace FinancialPayment.Infrastructure.Repositories;

public class TaxRuleRepository : ITaxRuleRepository
{
    private readonly FinancialPaymentDbContext _context;

    public TaxRuleRepository(FinancialPaymentDbContext context)
    {
        _context = context;
    }

    public async Task<TaxRule?> GetByIdAsync(Guid id)
    {
        return await _context.TaxRules
            .Include(tr => tr.Conditions)
            .FirstOrDefaultAsync(tr => tr.Id == id);
    }

    public async Task<List<TaxRule>> GetByJurisdictionAsync(string jurisdiction)
    {
        return await _context.TaxRules
            .Include(tr => tr.Conditions)
            .Where(tr => tr.Jurisdiction == jurisdiction)
            .OrderBy(tr => tr.Priority)
            .ToListAsync();
    }

    public async Task<List<TaxRule>> GetApplicableRulesAsync(
        string jurisdiction, 
        DateTime date, 
        string? productCategory = null, 
        string? serviceType = null)
    {
        var query = _context.TaxRules
            .Include(tr => tr.Conditions)
            .Where(tr => tr.IsActive && 
                        tr.Jurisdiction == jurisdiction &&
                        tr.EffectiveFrom <= date &&
                        (tr.EffectiveTo == null || tr.EffectiveTo >= date));

        if (!string.IsNullOrEmpty(productCategory))
        {
            query = query.Where(tr => tr.ProductCategory == null || tr.ProductCategory == productCategory);
        }

        if (!string.IsNullOrEmpty(serviceType))
        {
            query = query.Where(tr => tr.ServiceType == null || tr.ServiceType == serviceType);
        }

        return await query
            .OrderBy(tr => tr.Priority)
            .ToListAsync();
    }

    public async Task AddAsync(TaxRule taxRule)
    {
        await _context.TaxRules.AddAsync(taxRule);
        await _context.SaveChangesAsync();
    }

    public async Task UpdateAsync(TaxRule taxRule)
    {
        _context.TaxRules.Update(taxRule);
        await _context.SaveChangesAsync();
    }

    public async Task DeleteAsync(Guid id)
    {
        var taxRule = await _context.TaxRules.FindAsync(id);
        if (taxRule != null)
        {
            _context.TaxRules.Remove(taxRule);
            await _context.SaveChangesAsync();
        }
    }

    public async Task<List<TaxRule>> GetAllActiveAsync()
    {
        return await _context.TaxRules
            .Include(tr => tr.Conditions)
            .Where(tr => tr.IsActive)
            .OrderBy(tr => tr.Jurisdiction)
            .ThenBy(tr => tr.Priority)
            .ToListAsync();
    }
}
