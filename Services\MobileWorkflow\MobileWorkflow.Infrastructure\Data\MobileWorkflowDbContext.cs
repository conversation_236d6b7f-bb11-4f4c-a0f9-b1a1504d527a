using Microsoft.EntityFrameworkCore;
using MobileWorkflow.Domain.Entities;
using MobileWorkflow.Infrastructure.Data.Configurations;
using Newtonsoft.Json;

namespace MobileWorkflow.Infrastructure.Data;

public class MobileWorkflowDbContext : DbContext
{
    public MobileWorkflowDbContext(DbContextOptions<MobileWorkflowDbContext> options) : base(options)
    {
    }

    public DbSet<MobileApp> MobileApps { get; set; }
    public DbSet<MobileSession> MobileSessions { get; set; }
    public DbSet<OfflineData> OfflineData { get; set; }
    public DbSet<Workflow> Workflows { get; set; }
    public DbSet<WorkflowExecution> WorkflowExecutions { get; set; }
    public DbSet<WorkflowTask> WorkflowTasks { get; set; }

    // New sync entities
    public DbSet<SyncOperation> SyncOperations { get; set; }
    public DbSet<SyncItem> SyncItems { get; set; }
    public DbSet<ConflictResolution> ConflictResolutions { get; set; }

    // Milestone entities
    public DbSet<MilestoneTemplate> MilestoneTemplates { get; set; }
    public DbSet<MilestoneStep> MilestoneSteps { get; set; }
    public DbSet<MilestonePayoutRule> MilestonePayoutRules { get; set; }
    public DbSet<RoleTemplateMappings> RoleTemplateMappings { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Apply configurations
        modelBuilder.ApplyConfiguration(new MobileAppConfiguration());
        modelBuilder.ApplyConfiguration(new MobileSessionConfiguration());
        modelBuilder.ApplyConfiguration(new OfflineDataConfiguration());
        modelBuilder.ApplyConfiguration(new WorkflowConfiguration());
        modelBuilder.ApplyConfiguration(new WorkflowExecutionConfiguration());
        modelBuilder.ApplyConfiguration(new WorkflowTaskConfiguration());
        modelBuilder.ApplyConfiguration(new SyncOperationConfiguration());
        modelBuilder.ApplyConfiguration(new SyncItemConfiguration());
        modelBuilder.ApplyConfiguration(new ConflictResolutionConfiguration());
        modelBuilder.ApplyConfiguration(new MilestoneTemplateConfiguration());
        modelBuilder.ApplyConfiguration(new MilestoneStepConfiguration());
        modelBuilder.ApplyConfiguration(new MilestonePayoutRuleConfiguration());
        modelBuilder.ApplyConfiguration(new RoleTemplateMappingsConfiguration());

        // Configure JSON columns for PostgreSQL
        ConfigureJsonColumns(modelBuilder);

        // Configure indexes
        ConfigureIndexes(modelBuilder);
    }

    private static void ConfigureJsonColumns(ModelBuilder modelBuilder)
    {
        // MobileApp JSON columns
        modelBuilder.Entity<MobileApp>()
            .Property(e => e.Features)
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<Dictionary<string, object>>(v) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        modelBuilder.Entity<MobileApp>()
            .Property(e => e.Configuration)
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<Dictionary<string, object>>(v) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        // MobileSession JSON columns
        modelBuilder.Entity<MobileSession>()
            .Property(e => e.SessionData)
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<Dictionary<string, object>>(v) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        // OfflineData JSON columns
        modelBuilder.Entity<OfflineData>()
            .Property(e => e.Data)
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<Dictionary<string, object>>(v) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        modelBuilder.Entity<OfflineData>()
            .Property(e => e.Metadata)
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<Dictionary<string, object>>(v) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        // Workflow JSON columns
        modelBuilder.Entity<Workflow>()
            .Property(e => e.Definition)
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<Dictionary<string, object>>(v) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        modelBuilder.Entity<Workflow>()
            .Property(e => e.Configuration)
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<Dictionary<string, object>>(v) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        modelBuilder.Entity<Workflow>()
            .Property(e => e.TriggerConfiguration)
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<Dictionary<string, object>>(v) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        modelBuilder.Entity<Workflow>()
            .Property(e => e.Metadata)
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<Dictionary<string, object>>(v) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        // WorkflowExecution JSON columns
        modelBuilder.Entity<WorkflowExecution>()
            .Property(e => e.InputData)
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<Dictionary<string, object>>(v) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        modelBuilder.Entity<WorkflowExecution>()
            .Property(e => e.OutputData)
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<Dictionary<string, object>>(v) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        modelBuilder.Entity<WorkflowExecution>()
            .Property(e => e.Context)
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<Dictionary<string, object>>(v) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        modelBuilder.Entity<WorkflowExecution>()
            .Property(e => e.ExecutionLog)
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<Dictionary<string, object>>(v) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        // WorkflowTask JSON columns
        modelBuilder.Entity<WorkflowTask>()
            .Property(e => e.Parameters)
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<Dictionary<string, object>>(v) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        modelBuilder.Entity<WorkflowTask>()
            .Property(e => e.Result)
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<Dictionary<string, object>>(v) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        modelBuilder.Entity<WorkflowTask>()
            .Property(e => e.Metadata)
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<Dictionary<string, object>>(v) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        // MilestoneTemplate JSON columns
        modelBuilder.Entity<MilestoneTemplate>()
            .Property(e => e.Configuration)
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<Dictionary<string, object>>(v) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        modelBuilder.Entity<MilestoneTemplate>()
            .Property(e => e.Metadata)
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<Dictionary<string, object>>(v) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        // MilestoneStep JSON columns
        modelBuilder.Entity<MilestoneStep>()
            .Property(e => e.Configuration)
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<Dictionary<string, object>>(v) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        modelBuilder.Entity<MilestoneStep>()
            .Property(e => e.Metadata)
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<Dictionary<string, object>>(v) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        // MilestonePayoutRule JSON columns
        modelBuilder.Entity<MilestonePayoutRule>()
            .Property(e => e.Configuration)
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<Dictionary<string, object>>(v) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        modelBuilder.Entity<MilestonePayoutRule>()
            .Property(e => e.Metadata)
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<Dictionary<string, object>>(v) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        // RoleTemplateMappings JSON columns
        modelBuilder.Entity<RoleTemplateMappings>()
            .Property(e => e.Configuration)
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<Dictionary<string, object>>(v) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        modelBuilder.Entity<RoleTemplateMappings>()
            .Property(e => e.Metadata)
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<Dictionary<string, object>>(v) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");
    }

    private static void ConfigureIndexes(ModelBuilder modelBuilder)
    {
        // MobileApp indexes
        modelBuilder.Entity<MobileApp>()
            .HasIndex(e => e.PackageId)
            .IsUnique();

        modelBuilder.Entity<MobileApp>()
            .HasIndex(e => e.Platform);

        modelBuilder.Entity<MobileApp>()
            .HasIndex(e => e.IsActive);

        // MobileSession indexes
        modelBuilder.Entity<MobileSession>()
            .HasIndex(e => e.UserId);

        modelBuilder.Entity<MobileSession>()
            .HasIndex(e => e.DeviceId);

        modelBuilder.Entity<MobileSession>()
            .HasIndex(e => e.IsActive);

        modelBuilder.Entity<MobileSession>()
            .HasIndex(e => e.Platform);

        // OfflineData indexes
        modelBuilder.Entity<OfflineData>()
            .HasIndex(e => e.UserId);

        modelBuilder.Entity<OfflineData>()
            .HasIndex(e => e.IsSynced);

        modelBuilder.Entity<OfflineData>()
            .HasIndex(e => e.DataType);

        modelBuilder.Entity<OfflineData>()
            .HasIndex(e => e.Priority);

        modelBuilder.Entity<OfflineData>()
            .HasIndex(e => e.CreatedOffline);

        // Workflow indexes
        modelBuilder.Entity<Workflow>()
            .HasIndex(e => e.Name)
            .IsUnique();

        modelBuilder.Entity<Workflow>()
            .HasIndex(e => e.Category);

        modelBuilder.Entity<Workflow>()
            .HasIndex(e => e.IsActive);

        modelBuilder.Entity<Workflow>()
            .HasIndex(e => e.TriggerType);

        // WorkflowExecution indexes
        modelBuilder.Entity<WorkflowExecution>()
            .HasIndex(e => e.WorkflowId);

        modelBuilder.Entity<WorkflowExecution>()
            .HasIndex(e => e.TriggeredBy);

        modelBuilder.Entity<WorkflowExecution>()
            .HasIndex(e => e.Status);

        modelBuilder.Entity<WorkflowExecution>()
            .HasIndex(e => e.StartTime);

        // WorkflowTask indexes
        modelBuilder.Entity<WorkflowTask>()
            .HasIndex(e => e.AssignedTo);

        modelBuilder.Entity<WorkflowTask>()
            .HasIndex(e => e.AssignedToRole);

        modelBuilder.Entity<WorkflowTask>()
            .HasIndex(e => e.Status);

        modelBuilder.Entity<WorkflowTask>()
            .HasIndex(e => e.Priority);

        modelBuilder.Entity<WorkflowTask>()
            .HasIndex(e => e.DueDate);

        modelBuilder.Entity<WorkflowTask>()
            .HasIndex(e => e.IsOverdue);

        // MilestoneTemplate indexes
        modelBuilder.Entity<MilestoneTemplate>()
            .HasIndex(e => e.Name)
            .IsUnique();

        modelBuilder.Entity<MilestoneTemplate>()
            .HasIndex(e => e.Type);

        modelBuilder.Entity<MilestoneTemplate>()
            .HasIndex(e => e.Category);

        modelBuilder.Entity<MilestoneTemplate>()
            .HasIndex(e => e.IsActive);

        modelBuilder.Entity<MilestoneTemplate>()
            .HasIndex(e => e.IsDefault);

        modelBuilder.Entity<MilestoneTemplate>()
            .HasIndex(e => e.CreatedBy);

        // MilestoneStep indexes
        modelBuilder.Entity<MilestoneStep>()
            .HasIndex(e => e.MilestoneTemplateId);

        modelBuilder.Entity<MilestoneStep>()
            .HasIndex(e => new { e.MilestoneTemplateId, e.SequenceNumber })
            .IsUnique();

        modelBuilder.Entity<MilestoneStep>()
            .HasIndex(e => e.IsActive);

        modelBuilder.Entity<MilestoneStep>()
            .HasIndex(e => e.IsRequired);

        // MilestonePayoutRule indexes
        modelBuilder.Entity<MilestonePayoutRule>()
            .HasIndex(e => e.MilestoneStepId);

        modelBuilder.Entity<MilestonePayoutRule>()
            .HasIndex(e => e.IsActive);

        // RoleTemplateMappings indexes
        modelBuilder.Entity<RoleTemplateMappings>()
            .HasIndex(e => e.RoleName);

        modelBuilder.Entity<RoleTemplateMappings>()
            .HasIndex(e => e.MilestoneTemplateId);

        modelBuilder.Entity<RoleTemplateMappings>()
            .HasIndex(e => new { e.RoleName, e.MilestoneTemplateId })
            .IsUnique();

        modelBuilder.Entity<RoleTemplateMappings>()
            .HasIndex(e => e.IsDefault);

        modelBuilder.Entity<RoleTemplateMappings>()
            .HasIndex(e => e.IsActive);

        modelBuilder.Entity<RoleTemplateMappings>()
            .HasIndex(e => e.Priority);

        modelBuilder.Entity<RoleTemplateMappings>()
            .HasIndex(e => e.CreatedBy);
    }
}
