using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Enums;
using OrderManagement.Domain.Exceptions;

namespace OrderManagement.Application.EventHandlers;

// Integration event from Financial Payment Service
public record PaymentProcessedIntegrationEvent : INotification
{
    public Guid PaymentId { get; init; }
    public Guid OrderId { get; init; }
    public Guid InvoiceId { get; init; }
    public decimal Amount { get; init; }
    public string Currency { get; init; } = string.Empty;
    public string PaymentStatus { get; init; } = string.Empty; // Success, Failed, Pending
    public string? PaymentReference { get; init; }
    public string? PaymentMethod { get; init; }
    public DateTime ProcessedAt { get; init; }
    public string? FailureReason { get; init; }
}

public class PaymentProcessedEventHandler : INotificationHandler<PaymentProcessedIntegrationEvent>
{
    private readonly IOrderRepository _orderRepository;
    private readonly IInvoiceRepository _invoiceRepository;
    private readonly IMessageBroker _messageBroker;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<PaymentProcessedEventHandler> _logger;

    public PaymentProcessedEventHandler(
        IOrderRepository orderRepository,
        IInvoiceRepository invoiceRepository,
        IMessageBroker messageBroker,
        IUnitOfWork unitOfWork,
        ILogger<PaymentProcessedEventHandler> logger)
    {
        _orderRepository = orderRepository;
        _invoiceRepository = invoiceRepository;
        _messageBroker = messageBroker;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task Handle(PaymentProcessedIntegrationEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing payment processed event for order {OrderId}, invoice {InvoiceId}, payment {PaymentId}", 
            notification.OrderId, notification.InvoiceId, notification.PaymentId);

        try
        {
            // Get the order and invoice
            var order = await _orderRepository.GetByIdAsync(notification.OrderId, cancellationToken);
            if (order == null)
            {
                _logger.LogWarning("Order {OrderId} not found for payment event", notification.OrderId);
                throw new OrderManagementException("Order not found");
            }

            var invoice = await _invoiceRepository.GetByIdAsync(notification.InvoiceId, cancellationToken);
            if (invoice == null)
            {
                _logger.LogWarning("Invoice {InvoiceId} not found for payment event", notification.InvoiceId);
                throw new OrderManagementException("Invoice not found");
            }

            // Update based on payment status
            switch (notification.PaymentStatus.ToLower())
            {
                case "success":
                case "completed":
                    await HandleSuccessfulPayment(order, invoice, notification, cancellationToken);
                    break;

                case "failed":
                case "declined":
                    await HandleFailedPayment(order, invoice, notification, cancellationToken);
                    break;

                case "pending":
                case "processing":
                    await HandlePendingPayment(order, invoice, notification, cancellationToken);
                    break;

                default:
                    _logger.LogWarning("Unknown payment status {PaymentStatus} for payment {PaymentId}", 
                        notification.PaymentStatus, notification.PaymentId);
                    break;
            }

            _logger.LogInformation("Successfully processed payment event for order {OrderId}", notification.OrderId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing payment event for order {OrderId}", notification.OrderId);
            throw;
        }
    }

    private async Task HandleSuccessfulPayment(
        Domain.Entities.Order order,
        Domain.Entities.Invoice invoice,
        PaymentProcessedIntegrationEvent notification,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Handling successful payment for order {OrderId}, invoice {InvoiceId}", 
            order.Id, invoice.Id);

        // Mark invoice as paid
        invoice.MarkAsPaid(notification.PaymentReference ?? notification.PaymentId.ToString());

        // Update order payment status
        order.UpdatePaymentStatus(PaymentStatus.Paid);

        // Save changes
        await _invoiceRepository.UpdateAsync(invoice, cancellationToken);
        await _orderRepository.UpdateAsync(order, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        // Publish integration event
        await _messageBroker.PublishAsync("order.payment.completed", new
        {
            OrderId = order.Id,
            OrderNumber = order.OrderNumber,
            InvoiceId = invoice.Id,
            InvoiceNumber = invoice.InvoiceNumber,
            PaymentId = notification.PaymentId,
            PaymentReference = notification.PaymentReference,
            Amount = notification.Amount,
            Currency = notification.Currency,
            PaidAt = notification.ProcessedAt
        }, cancellationToken);
    }

    private async Task HandleFailedPayment(
        Domain.Entities.Order order,
        Domain.Entities.Invoice invoice,
        PaymentProcessedIntegrationEvent notification,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Handling failed payment for order {OrderId}, invoice {InvoiceId}", 
            order.Id, invoice.Id);

        // Update order payment status
        order.UpdatePaymentStatus(PaymentStatus.Failed);

        // Save changes
        await _orderRepository.UpdateAsync(order, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        // Publish integration event
        await _messageBroker.PublishAsync("order.payment.failed", new
        {
            OrderId = order.Id,
            OrderNumber = order.OrderNumber,
            InvoiceId = invoice.Id,
            InvoiceNumber = invoice.InvoiceNumber,
            PaymentId = notification.PaymentId,
            Amount = notification.Amount,
            Currency = notification.Currency,
            FailureReason = notification.FailureReason,
            FailedAt = notification.ProcessedAt
        }, cancellationToken);
    }

    private async Task HandlePendingPayment(
        Domain.Entities.Order order,
        Domain.Entities.Invoice invoice,
        PaymentProcessedIntegrationEvent notification,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Handling pending payment for order {OrderId}, invoice {InvoiceId}", 
            order.Id, invoice.Id);

        // Update order payment status if not already pending
        if (order.PaymentStatus != PaymentStatus.Pending)
        {
            order.UpdatePaymentStatus(PaymentStatus.Pending);
            await _orderRepository.UpdateAsync(order, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);
        }

        // Publish integration event
        await _messageBroker.PublishAsync("order.payment.pending", new
        {
            OrderId = order.Id,
            OrderNumber = order.OrderNumber,
            InvoiceId = invoice.Id,
            InvoiceNumber = invoice.InvoiceNumber,
            PaymentId = notification.PaymentId,
            Amount = notification.Amount,
            Currency = notification.Currency,
            PendingAt = notification.ProcessedAt
        }, cancellationToken);
    }
}
