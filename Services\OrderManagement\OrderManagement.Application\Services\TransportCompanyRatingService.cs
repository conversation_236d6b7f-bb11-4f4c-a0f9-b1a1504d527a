using OrderManagement.Application.DTOs;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Entities;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;

namespace OrderManagement.Application.Services;

/// <summary>
/// Service for managing Transport Company rating aggregation and display
/// </summary>
public interface ITransportCompanyRatingService
{
    Task<TransportCompanyRatingDisplayDto> GetTransportCompanyRatingDisplayAsync(
        Guid transportCompanyId,
        CancellationToken cancellationToken = default);

    Task<List<TransportCompanyRatingDisplayDto>> GetMultipleTransportCompanyRatingsAsync(
        List<Guid> transportCompanyIds,
        CancellationToken cancellationToken = default);

    Task<TransportCompanyRatingTrendsDto> GetRatingTrendsAsync(
        Guid transportCompanyId,
        DateTime fromDate,
        DateTime toDate,
        CancellationToken cancellationToken = default);

    Task<TransportCompanyRatingComparisonDto> GetRatingComparisonAsync(
        List<Guid> transportCompanyIds,
        CancellationToken cancellationToken = default);

    Task RefreshRatingCacheAsync(Guid transportCompanyId, CancellationToken cancellationToken = default);

    Task<TransportCompanyPerformanceMetricsDto> GetPerformanceMetricsAsync(
        Guid transportCompanyId,
        CancellationToken cancellationToken = default);
}

public class TransportCompanyRatingService : ITransportCompanyRatingService
{
    private readonly IRatingAggregationRepository _ratingRepository;
    private readonly ITripFeedbackRepository _tripFeedbackRepository;
    private readonly IOrderRepository _orderRepository;
    private readonly IMemoryCache _cache;
    private readonly ILogger<TransportCompanyRatingService> _logger;

    private const int CacheExpirationMinutes = 30;
    private const string CacheKeyPrefix = "transport_company_rating_";

    public TransportCompanyRatingService(
        IRatingAggregationRepository ratingRepository,
        ITripFeedbackRepository tripFeedbackRepository,
        IOrderRepository orderRepository,
        IMemoryCache cache,
        ILogger<TransportCompanyRatingService> logger)
    {
        _ratingRepository = ratingRepository;
        _tripFeedbackRepository = tripFeedbackRepository;
        _orderRepository = orderRepository;
        _cache = cache;
        _logger = logger;
    }

    public async Task<TransportCompanyRatingDisplayDto> GetTransportCompanyRatingDisplayAsync(
        Guid transportCompanyId,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting rating display for Transport Company {TransportCompanyId}", transportCompanyId);

        var cacheKey = $"{CacheKeyPrefix}{transportCompanyId}";

        if (_cache.TryGetValue(cacheKey, out TransportCompanyRatingDisplayDto? cachedRating))
        {
            _logger.LogDebug("Returning cached rating for Transport Company {TransportCompanyId}", transportCompanyId);
            return cachedRating!;
        }

        try
        {
            var ratingDisplay = await BuildRatingDisplayAsync(transportCompanyId, cancellationToken);

            // Cache the result
            _cache.Set(cacheKey, ratingDisplay, TimeSpan.FromMinutes(CacheExpirationMinutes));

            _logger.LogInformation("Successfully built rating display for Transport Company {TransportCompanyId}. Overall Rating: {OverallRating}",
                transportCompanyId, ratingDisplay.OverallRating);

            return ratingDisplay;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting rating display for Transport Company {TransportCompanyId}", transportCompanyId);
            throw;
        }
    }

    public async Task<List<TransportCompanyRatingDisplayDto>> GetMultipleTransportCompanyRatingsAsync(
        List<Guid> transportCompanyIds,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting ratings for {Count} transport companies", transportCompanyIds.Count);

        var results = new List<TransportCompanyRatingDisplayDto>();
        var uncachedIds = new List<Guid>();

        // Check cache first
        foreach (var id in transportCompanyIds)
        {
            var cacheKey = $"{CacheKeyPrefix}{id}";
            if (_cache.TryGetValue(cacheKey, out TransportCompanyRatingDisplayDto? cachedRating))
            {
                results.Add(cachedRating!);
            }
            else
            {
                uncachedIds.Add(id);
            }
        }

        // Build ratings for uncached companies
        if (uncachedIds.Any())
        {
            var tasks = uncachedIds.Select(id => BuildRatingDisplayAsync(id, cancellationToken));
            var uncachedResults = await Task.WhenAll(tasks);

            foreach (var result in uncachedResults)
            {
                var cacheKey = $"{CacheKeyPrefix}{result.TransportCompanyId}";
                _cache.Set(cacheKey, result, TimeSpan.FromMinutes(CacheExpirationMinutes));
                results.Add(result);
            }
        }

        return results.OrderByDescending(r => r.OverallRating).ToList();
    }

    public async Task<TransportCompanyRatingTrendsDto> GetRatingTrendsAsync(
        Guid transportCompanyId,
        DateTime fromDate,
        DateTime toDate,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting rating trends for Transport Company {TransportCompanyId} from {FromDate} to {ToDate}",
            transportCompanyId, fromDate, toDate);

        try
        {
            var feedbacks = await _tripFeedbackRepository.GetFeedbackByTransportCompanyAsync(
                transportCompanyId, fromDate, toDate, cancellationToken);

            var trends = new TransportCompanyRatingTrendsDto
            {
                TransportCompanyId = transportCompanyId,
                FromDate = fromDate,
                ToDate = toDate,
                GeneratedAt = DateTime.UtcNow
            };

            // Group by month for trends
            var monthlyGroups = feedbacks
                .GroupBy(f => new { f.SubmittedAt.Year, f.SubmittedAt.Month })
                .OrderBy(g => g.Key.Year)
                .ThenBy(g => g.Key.Month);

            foreach (var group in monthlyGroups)
            {
                var monthData = new RatingTrendDataPoint
                {
                    Date = new DateTime(group.Key.Year, group.Key.Month, 1),
                    OverallRating = group.Average(f => f.OverallRating),
                    ServiceQualityRating = group.Average(f => f.ServiceQualityRating),
                    TimelinessRating = group.Average(f => f.TimelinessRating),
                    CommunicationRating = group.Average(f => f.CommunicationRating),
                    ProfessionalismRating = group.Average(f => f.ProfessionalismRating),
                    TotalFeedbacks = group.Count(),
                    PositiveFeedbacks = group.Count(f => f.OverallRating >= 4.0m),
                    NegativeFeedbacks = group.Count(f => f.OverallRating <= 2.0m)
                };

                trends.MonthlyTrends.Add(monthData);
            }

            // Calculate improvement/decline indicators
            if (trends.MonthlyTrends.Count >= 2)
            {
                var latest = trends.MonthlyTrends.Last();
                var previous = trends.MonthlyTrends[trends.MonthlyTrends.Count - 2];

                trends.RatingImprovement = latest.OverallRating - previous.OverallRating;
                trends.IsImproving = trends.RatingImprovement > 0;
            }

            return trends;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting rating trends for Transport Company {TransportCompanyId}", transportCompanyId);
            throw;
        }
    }

    public async Task<TransportCompanyRatingComparisonDto> GetRatingComparisonAsync(
        List<Guid> transportCompanyIds,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting rating comparison for {Count} transport companies", transportCompanyIds.Count);

        try
        {
            var ratings = await GetMultipleTransportCompanyRatingsAsync(transportCompanyIds, cancellationToken);

            var comparison = new TransportCompanyRatingComparisonDto
            {
                ComparedAt = DateTime.UtcNow,
                TotalCompanies = ratings.Count
            };

            foreach (var rating in ratings)
            {
                var comparisonItem = new TransportCompanyComparisonItem
                {
                    TransportCompanyId = rating.TransportCompanyId,
                    CompanyName = rating.CompanyName,
                    OverallRating = rating.OverallRating,
                    TotalTrips = rating.TotalTrips,
                    OnTimeDeliveryRate = rating.OnTimeDeliveryRate,
                    CustomerSatisfactionScore = rating.CustomerSatisfactionScore,
                    RankByRating = 0, // Will be calculated below
                    RankByTrips = 0,
                    RankByOnTimeDelivery = 0,
                    PerformanceIndicators = rating.PerformanceIndicators
                };

                comparison.Companies.Add(comparisonItem);
            }

            // Calculate rankings
            CalculateRankings(comparison.Companies);

            // Calculate statistics
            comparison.HighestRating = comparison.Companies.Max(c => c.OverallRating);
            comparison.LowestRating = comparison.Companies.Min(c => c.OverallRating);
            comparison.AverageRating = comparison.Companies.Average(c => c.OverallRating);
            comparison.MedianRating = CalculateMedian(comparison.Companies.Select(c => c.OverallRating));

            return comparison;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting rating comparison for transport companies");
            throw;
        }
    }

    public async Task RefreshRatingCacheAsync(Guid transportCompanyId, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Refreshing rating cache for Transport Company {TransportCompanyId}", transportCompanyId);

        var cacheKey = $"{CacheKeyPrefix}{transportCompanyId}";
        _cache.Remove(cacheKey);

        // Rebuild and cache
        await GetTransportCompanyRatingDisplayAsync(transportCompanyId, cancellationToken);
    }

    public async Task<TransportCompanyPerformanceMetricsDto> GetPerformanceMetricsAsync(
        Guid transportCompanyId,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting performance metrics for Transport Company {TransportCompanyId}", transportCompanyId);

        try
        {
            var feedbacks = await _tripFeedbackRepository.GetFeedbackByTransportCompanyAsync(
                transportCompanyId, DateTime.UtcNow.AddMonths(-12), DateTime.UtcNow, cancellationToken);

            var orders = await _orderRepository.GetOrdersByTransportCompanyAsync(
                transportCompanyId, DateTime.UtcNow.AddMonths(-12), DateTime.UtcNow, cancellationToken);

            var metrics = new TransportCompanyPerformanceMetricsDto
            {
                TransportCompanyId = transportCompanyId,
                CalculatedAt = DateTime.UtcNow,
                PeriodStart = DateTime.UtcNow.AddMonths(-12),
                PeriodEnd = DateTime.UtcNow
            };

            // Calculate metrics
            if (feedbacks.Any())
            {
                metrics.TotalFeedbacks = feedbacks.Count();
                metrics.AverageOverallRating = feedbacks.Average(f => f.OverallRating);
                metrics.HighRatingPercentage = (decimal)feedbacks.Count(f => f.OverallRating >= 4.0m) / feedbacks.Count() * 100;
                metrics.LowRatingPercentage = (decimal)feedbacks.Count(f => f.OverallRating <= 2.0m) / feedbacks.Count() * 100;

                // Category averages
                metrics.ServiceQualityAverage = feedbacks.Average(f => f.ServiceQualityRating);
                metrics.TimelinessAverage = feedbacks.Average(f => f.TimelinessRating);
                metrics.CommunicationAverage = feedbacks.Average(f => f.CommunicationRating);
                metrics.ProfessionalismAverage = feedbacks.Average(f => f.ProfessionalismRating);
                metrics.VehicleConditionAverage = feedbacks.Average(f => f.VehicleConditionRating);
                metrics.CargoHandlingAverage = feedbacks.Average(f => f.CargoHandlingRating);
            }

            if (orders.Any())
            {
                metrics.TotalOrders = orders.Count();
                metrics.CompletedOrders = orders.Count(o => o.Status == Domain.Enums.OrderStatus.Completed);
                // Note: WasDelayed, ActualDeliveryDate, ActualPickupDate properties don't exist in Order entity
                // Using CompletedAt as a proxy for on-time delivery tracking
                metrics.OnTimeDeliveries = orders.Count(o => o.Status == Domain.Enums.OrderStatus.Completed && o.CompletedAt.HasValue);
                metrics.OnTimeDeliveryRate = metrics.TotalOrders > 0 ?
                    (decimal)metrics.OnTimeDeliveries / metrics.TotalOrders * 100 : 0;

                metrics.AverageDeliveryTime = orders.Where(o => o.Status == Domain.Enums.OrderStatus.Completed && o.CompletedAt.HasValue)
                    .Any() ? orders.Where(o => o.Status == Domain.Enums.OrderStatus.Completed && o.CompletedAt.HasValue)
                    .Average(o => (o.CompletedAt!.Value - o.CreatedAt).TotalHours) : 0;
            }

            return metrics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting performance metrics for Transport Company {TransportCompanyId}", transportCompanyId);
            throw;
        }
    }

    private async Task<TransportCompanyRatingDisplayDto> BuildRatingDisplayAsync(
        Guid transportCompanyId,
        CancellationToken cancellationToken)
    {
        // Get recent feedbacks (last 12 months)
        var recentFeedbacks = await _tripFeedbackRepository.GetFeedbackByTransportCompanyAsync(
            transportCompanyId, DateTime.UtcNow.AddMonths(-12), DateTime.UtcNow, cancellationToken);

        // Get order statistics
        var orders = await _orderRepository.GetOrdersByTransportCompanyAsync(
            transportCompanyId, DateTime.UtcNow.AddMonths(-12), DateTime.UtcNow, cancellationToken);

        var ratingDisplay = new TransportCompanyRatingDisplayDto
        {
            TransportCompanyId = transportCompanyId,
            CompanyName = await GetCompanyNameAsync(transportCompanyId, cancellationToken),
            LastUpdated = DateTime.UtcNow
        };

        if (recentFeedbacks.Any())
        {
            // Calculate overall ratings
            ratingDisplay.OverallRating = Math.Round(recentFeedbacks.Average(f => f.OverallRating), 1);
            ratingDisplay.TotalReviews = recentFeedbacks.Count();
            ratingDisplay.TotalTrips = orders.Count();

            // Calculate category ratings
            ratingDisplay.ServiceQualityRating = Math.Round(recentFeedbacks.Average(f => f.ServiceQualityRating), 1);
            ratingDisplay.TimelinessRating = Math.Round(recentFeedbacks.Average(f => f.TimelinessRating), 1);
            ratingDisplay.CommunicationRating = Math.Round(recentFeedbacks.Average(f => f.CommunicationRating), 1);
            ratingDisplay.ProfessionalismRating = Math.Round(recentFeedbacks.Average(f => f.ProfessionalismRating), 1);

            // Calculate performance metrics
            var completedOrders = orders.Where(o => o.Status == Domain.Enums.OrderStatus.Completed).ToList();
            if (completedOrders.Any())
            {
                // Note: WasDelayed property doesn't exist in Order entity, using CompletedAt as proxy
                ratingDisplay.OnTimeDeliveryRate = Math.Round(
                    (decimal)completedOrders.Count(o => o.CompletedAt.HasValue) / completedOrders.Count() * 100, 1);

                ratingDisplay.CustomerSatisfactionScore = Math.Round(
                    recentFeedbacks.Count(f => f.OverallRating >= 4.0m) / (decimal)recentFeedbacks.Count() * 100, 1);
            }

            // Generate performance indicators
            ratingDisplay.PerformanceIndicators = GeneratePerformanceIndicators(ratingDisplay);

            // Calculate badges
            ratingDisplay.Badges = CalculateBadges(ratingDisplay, recentFeedbacks, completedOrders);
        }
        else
        {
            // No ratings yet
            ratingDisplay.OverallRating = 0;
            ratingDisplay.TotalReviews = 0;
            ratingDisplay.TotalTrips = orders.Count();
            ratingDisplay.PerformanceIndicators = new List<PerformanceIndicator>();
            ratingDisplay.Badges = new List<string>();
        }

        return ratingDisplay;
    }

    private List<PerformanceIndicator> GeneratePerformanceIndicators(TransportCompanyRatingDisplayDto rating)
    {
        var indicators = new List<PerformanceIndicator>();

        // Overall rating indicator
        indicators.Add(new PerformanceIndicator
        {
            Name = "Overall Rating",
            Value = rating.OverallRating,
            MaxValue = 5.0m,
            Status = rating.OverallRating >= 4.0m ? "Excellent" :
                    rating.OverallRating >= 3.0m ? "Good" :
                    rating.OverallRating >= 2.0m ? "Fair" : "Poor",
            Color = rating.OverallRating >= 4.0m ? "green" :
                   rating.OverallRating >= 3.0m ? "yellow" : "red",
            Icon = "star"
        });

        // On-time delivery indicator
        indicators.Add(new PerformanceIndicator
        {
            Name = "On-Time Delivery",
            Value = rating.OnTimeDeliveryRate,
            MaxValue = 100m,
            Status = rating.OnTimeDeliveryRate >= 90m ? "Excellent" :
                    rating.OnTimeDeliveryRate >= 80m ? "Good" :
                    rating.OnTimeDeliveryRate >= 70m ? "Fair" : "Poor",
            Color = rating.OnTimeDeliveryRate >= 90m ? "green" :
                   rating.OnTimeDeliveryRate >= 80m ? "yellow" : "red",
            Icon = "clock",
            Unit = "%"
        });

        return indicators;
    }

    private List<string> CalculateBadges(
        TransportCompanyRatingDisplayDto rating,
        List<TripFeedback> feedbacks,
        List<Order> completedOrders)
    {
        var badges = new List<string>();

        // Rating-based badges
        if (rating.OverallRating >= 4.5m)
            badges.Add("Top Rated");
        else if (rating.OverallRating >= 4.0m)
            badges.Add("Highly Rated");

        // Performance-based badges
        if (rating.OnTimeDeliveryRate >= 95m)
            badges.Add("Always On Time");
        else if (rating.OnTimeDeliveryRate >= 90m)
            badges.Add("Reliable");

        // Volume-based badges
        if (rating.TotalTrips >= 1000)
            badges.Add("Experienced");
        else if (rating.TotalTrips >= 500)
            badges.Add("Established");

        return badges;
    }

    private async Task<string> GetCompanyNameAsync(Guid transportCompanyId, CancellationToken cancellationToken)
    {
        // This would typically fetch from a company service or repository
        return $"Transport Company {transportCompanyId.ToString()[..8]}";
    }

    private void CalculateRankings(List<TransportCompanyComparisonItem> companies)
    {
        var sortedCompanies = companies.OrderByDescending(c => c.OverallRating).ToList();
        for (int i = 0; i < sortedCompanies.Count; i++)
        {
            sortedCompanies[i].RankByRating = i + 1;
        }
    }

    private decimal CalculateMedian(IEnumerable<decimal> values)
    {
        var sortedValues = values.OrderBy(x => x).ToList();
        int count = sortedValues.Count;

        if (count == 0) return 0;
        if (count % 2 == 0)
        {
            return (sortedValues[count / 2 - 1] + sortedValues[count / 2]) / 2;
        }
        else
        {
            return sortedValues[count / 2];
        }
    }
}
