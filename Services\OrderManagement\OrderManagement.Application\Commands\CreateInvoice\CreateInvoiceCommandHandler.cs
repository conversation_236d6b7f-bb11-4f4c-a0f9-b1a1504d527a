using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Entities;
using OrderManagement.Domain.ValueObjects;
using OrderManagement.Domain.Exceptions;
using OrderManagement.Domain.Enums;
using Shared.Domain.Common;
using MediatR;

namespace OrderManagement.Application.Commands.CreateInvoice;

public class CreateInvoiceCommandHandler : IRequestHandler<CreateInvoiceCommand, Guid>
{
    private readonly IInvoiceRepository _invoiceRepository;
    private readonly IOrderRepository _orderRepository;
    private readonly IMessageBroker _messageBroker;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<CreateInvoiceCommandHandler> _logger;

    public CreateInvoiceCommandHandler(
        IInvoiceRepository invoiceRepository,
        IOrderRepository orderRepository,
        IMessageBroker messageBroker,
        IUnitOfWork unitOfWork,
        ILogger<CreateInvoiceCommandHandler> logger)
    {
        _invoiceRepository = invoiceRepository;
        _orderRepository = orderRepository;
        _messageBroker = messageBroker;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<Guid> Handle(CreateInvoiceCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Creating invoice for order {OrderId} by user {CreatedBy}",
            request.OrderId, request.CreatedBy);

        // Validate order exists and user has permission
        var order = await _orderRepository.GetByIdAsync(request.OrderId, cancellationToken);
        if (order == null)
        {
            _logger.LogWarning("Order {OrderId} not found", request.OrderId);
            throw new OrderManagementException("Order not found");
        }

        // Authorization check - only transport company or broker can create invoices
        if (order.TransportCompanyId != request.CreatedBy && order.BrokerId != request.CreatedBy)
        {
            _logger.LogWarning("User {CreatedBy} is not authorized to create invoice for order {OrderId}",
                request.CreatedBy, request.OrderId);
            throw new OrderManagementException("Not authorized to create invoice for this order");
        }

        // Business rule validation - order should be completed or confirmed to create invoice
        if (order.Status != OrderStatus.Completed && order.Status != OrderStatus.Confirmed)
        {
            _logger.LogWarning("Cannot create invoice for order {OrderId} with status {Status}",
                request.OrderId, order.Status);
            throw new OrderManagementException($"Cannot create invoice for order with status {order.Status}");
        }

        // Check if invoice already exists for this order
        var existingInvoices = await _invoiceRepository.GetByOrderIdAsync(request.OrderId, cancellationToken);
        if (existingInvoices.Any(i => i.Status != InvoiceStatus.Cancelled))
        {
            _logger.LogWarning("Active invoice already exists for order {OrderId}", request.OrderId);
            throw new OrderManagementException("An active invoice already exists for this order");
        }

        try
        {
            // Calculate totals from line items
            var subtotal = request.LineItems.Sum(item => item.UnitPrice * item.Quantity);
            var totalTaxAmount = request.LineItems
                .Where(item => item.TaxRate.HasValue)
                .Sum(item => (item.UnitPrice * item.Quantity) * (decimal)(item.TaxRate.Value / 100));

            var amount = new Money(subtotal, request.LineItems.FirstOrDefault()?.Currency ?? "USD");
            var taxAmount = totalTaxAmount > 0 ? new Money(totalTaxAmount, request.LineItems.FirstOrDefault()?.Currency ?? "USD") : null;

            // Create Invoice entity
            // Note: CustomerDetails and BillingDetails don't exist as Order properties
            var invoice = new Invoice(
                request.OrderId,
                amount,
                request.Description,
                taxAmount,
                request.Notes);

            // Add line items
            foreach (var lineItemDto in request.LineItems)
            {
                var unitPrice = new Money(lineItemDto.UnitPrice, lineItemDto.Currency);
                var lineItem = new InvoiceLineItem(
                    lineItemDto.Description,
                    lineItemDto.Quantity,
                    unitPrice);

                invoice.AddLineItem(lineItem);
            }

            // Save to repository
            await _invoiceRepository.AddAsync(invoice, cancellationToken);

            // Add invoice to order
            order.AddInvoice(invoice);
            await _orderRepository.UpdateAsync(order, cancellationToken);

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Publish integration event
            await _messageBroker.PublishAsync("invoice.created", new
            {
                InvoiceId = invoice.Id,
                InvoiceNumber = invoice.InvoiceNumber,
                OrderId = invoice.OrderId,
                OrderNumber = order.OrderNumber,
                Amount = new { Amount = invoice.Amount.Amount, Currency = invoice.Amount.Currency },
                TotalAmount = new { Amount = invoice.TotalAmount.Amount, Currency = invoice.TotalAmount.Currency },
                DueDate = invoice.DueDate,
                CustomerEmail = "<EMAIL>", // Note: CustomerDetails property doesn't exist on Invoice
                CreatedBy = request.CreatedBy,
                CreatedAt = invoice.CreatedAt
            }, cancellationToken);

            _logger.LogInformation("Successfully created invoice {InvoiceId} with number {InvoiceNumber} for order {OrderId}",
                invoice.Id, invoice.InvoiceNumber, request.OrderId);

            return invoice.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating invoice for order {OrderId}", request.OrderId);
            throw;
        }
    }
}

