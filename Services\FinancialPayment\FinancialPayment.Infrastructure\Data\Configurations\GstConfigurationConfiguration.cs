using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Infrastructure.Data.Configurations;

public class GstConfigurationConfiguration : IEntityTypeConfiguration<GstConfiguration>
{
    public void Configure(EntityTypeBuilder<GstConfiguration> builder)
    {
        builder.ToTable("GstConfigurations");

        builder.HasKey(gc => gc.Id);

        builder.Property(gc => gc.Name)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(gc => gc.Description)
            .IsRequired()
            .HasMaxLength(1000);

        builder.Property(gc => gc.ServiceCategory)
            .IsRequired()
            .HasConversion<int>();

        // Configure TaxJurisdiction value object
        builder.OwnsOne(gc => gc.Jurisdiction, jurisdiction =>
        {
            jurisdiction.Property(j => j.Country)
                .HasColumnName("JurisdictionCountry")
                .IsRequired()
                .HasMaxLength(100);
            jurisdiction.Property(j => j.State)
                .HasColumnName("JurisdictionState")
                .HasMaxLength(100);
            jurisdiction.Property(j => j.City)
                .HasColumnName("JurisdictionCity")
                .HasMaxLength(100);
            jurisdiction.Property(j => j.Type)
                .HasColumnName("JurisdictionType")
                .HasConversion<int>();
        });

        builder.Property(gc => gc.GstRate)
            .IsRequired()
            .HasConversion<int>();

        // Configure TaxRate value object
        builder.OwnsOne(gc => gc.TaxRate, taxRate =>
        {
            taxRate.Property(tr => tr.Rate)
                .HasColumnName("TaxRate")
                .HasPrecision(5, 4);
            taxRate.Property(tr => tr.CalculationMethod)
                .HasColumnName("TaxCalculationMethod")
                .HasConversion<int>();
            taxRate.Property(tr => tr.EffectiveFrom)
                .HasColumnName("TaxEffectiveFrom");
            taxRate.Property(tr => tr.EffectiveTo)
                .HasColumnName("TaxEffectiveTo");
        });

        builder.Property(gc => gc.Status)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(gc => gc.HsnCode)
            .HasMaxLength(20);

        // Configure Money value objects
        builder.OwnsOne(gc => gc.MinimumAmount, money =>
        {
            money.Property(m => m.Amount)
                .HasColumnName("MinimumAmount")
                .HasPrecision(18, 2);
            money.Property(m => m.Currency)
                .HasColumnName("MinimumAmountCurrency")
                .HasMaxLength(3);
        });

        builder.OwnsOne(gc => gc.MaximumAmount, money =>
        {
            money.Property(m => m.Amount)
                .HasColumnName("MaximumAmount")
                .HasPrecision(18, 2);
            money.Property(m => m.Currency)
                .HasColumnName("MaximumAmountCurrency")
                .HasMaxLength(3);
        });

        builder.Property(gc => gc.IsReverseChargeApplicable)
            .IsRequired();

        builder.Property(gc => gc.ReverseChargeConditions)
            .HasMaxLength(1000);

        builder.Property(gc => gc.CreatedBy)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(gc => gc.ModifiedBy)
            .HasMaxLength(100);

        builder.Property(gc => gc.ModifiedAt);

        // Configure relationships
        builder.HasMany(gc => gc.History)
            .WithOne()
            .HasForeignKey(h => h.GstConfigurationId)
            .OnDelete(DeleteBehavior.Cascade);

        // Configure indexes
        builder.HasIndex(gc => gc.Name)
            .IsUnique();
        builder.HasIndex(gc => gc.ServiceCategory);
        builder.HasIndex(gc => gc.GstRate);
        builder.HasIndex(gc => gc.Status);
        builder.HasIndex(gc => gc.HsnCode);
        builder.HasIndex(gc => gc.IsReverseChargeApplicable);
        builder.HasIndex(gc => new { gc.ServiceCategory, gc.Status });
    }
}

public class GstConfigurationHistoryConfiguration : IEntityTypeConfiguration<GstConfigurationHistory>
{
    public void Configure(EntityTypeBuilder<GstConfigurationHistory> builder)
    {
        builder.ToTable("GstConfigurationHistories");

        builder.HasKey(gch => gch.Id);

        builder.Property(gch => gch.GstConfigurationId)
            .IsRequired();

        builder.Property(gch => gch.Action)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(gch => gch.Details)
            .IsRequired()
            .HasMaxLength(2000);

        builder.Property(gch => gch.ModifiedBy)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(gch => gch.ModifiedAt)
            .IsRequired();

        // Configure indexes
        builder.HasIndex(gch => gch.GstConfigurationId);
        builder.HasIndex(gch => gch.ModifiedAt);
        builder.HasIndex(gch => gch.ModifiedBy);
    }
}
