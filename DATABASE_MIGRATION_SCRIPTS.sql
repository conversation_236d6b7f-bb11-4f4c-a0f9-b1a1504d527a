-- =====================================================
-- TLI Broker App Features - Database Migration Scripts
-- =====================================================

-- Migration Version: 2024.01.15.001
-- Description: Add support for KYC auto-verification, auto-renewal preferences, 
--              broker markup editing, and auto-invoice generation

-- =====================================================
-- 1. UserManagement Service Schema Changes
-- =====================================================

-- Add KYC verification tracking to user_profiles
ALTER TABLE user_profiles 
ADD COLUMN kyc_verification_status VARCHAR(50) DEFAULT 'Pending',
ADD COLUMN kyc_verified_at TIMESTAMP NULL,
ADD COLUMN kyc_verification_details JSONB DEFAULT '{}',
ADD COLUMN preferred_language VARCHAR(10) DEFAULT 'en',
ADD COLUMN language_preferences JSONB DEFAULT '{"notifications": "en", "ui": "en"}';

-- Add indexes for performance
CREATE INDEX idx_user_profiles_kyc_status ON user_profiles(kyc_verification_status);
CREATE INDEX idx_user_profiles_language ON user_profiles(preferred_language);

-- Add KYC verification result tracking to documents
ALTER TABLE documents
ADD COLUMN kyc_verification_result JSONB DEFAULT '{}',
ADD COLUMN is_auto_verification_enabled BOOLEAN DEFAULT false,
ADD COLUMN verification_attempts INTEGER DEFAULT 0,
ADD COLUMN last_verification_attempt TIMESTAMP NULL,
ADD COLUMN verification_provider VARCHAR(100) NULL,
ADD COLUMN verification_reference_id VARCHAR(255) NULL;

-- Create KYC verification history table
CREATE TABLE kyc_verification_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    user_id UUID NOT NULL,
    verification_id VARCHAR(255) NOT NULL,
    provider_name VARCHAR(100) NOT NULL,
    status VARCHAR(50) NOT NULL,
    confidence_score DECIMAL(5,4) DEFAULT 0,
    extracted_data JSONB DEFAULT '{}',
    validation_errors JSONB DEFAULT '[]',
    processing_time_ms INTEGER DEFAULT 0,
    attempted_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for KYC history
CREATE INDEX idx_kyc_history_document_id ON kyc_verification_history(document_id);
CREATE INDEX idx_kyc_history_user_id ON kyc_verification_history(user_id);
CREATE INDEX idx_kyc_history_status ON kyc_verification_history(status);
CREATE INDEX idx_kyc_history_attempted_at ON kyc_verification_history(attempted_at);

-- =====================================================
-- 2. SubscriptionManagement Service Schema Changes
-- =====================================================

-- Add auto-renewal preferences to subscriptions
ALTER TABLE subscriptions
ADD COLUMN auto_renewal_preferences JSONB DEFAULT '{"isEnabled": true, "daysBeforeExpiry": 7, "maxRetryAttempts": 3, "notifyBeforeRenewal": true, "notifyAfterRenewal": true, "notifyOnFailure": true, "notificationChannels": ["email"]}',
ADD COLUMN next_renewal_attempt_at TIMESTAMP NULL,
ADD COLUMN renewal_failure_count INTEGER DEFAULT 0,
ADD COLUMN last_renewal_attempt_at TIMESTAMP NULL,
ADD COLUMN renewal_failure_reason TEXT NULL;

-- Create renewal attempts tracking table
CREATE TABLE subscription_renewal_attempts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    subscription_id UUID NOT NULL REFERENCES subscriptions(id) ON DELETE CASCADE,
    attempted_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(50) NOT NULL, -- Pending, Processing, Successful, Failed, Cancelled
    attempted_amount DECIMAL(15,2) NOT NULL,
    payment_reference VARCHAR(255) NULL,
    failure_reason TEXT NULL,
    next_retry_at TIMESTAMP NULL,
    processing_time_ms INTEGER DEFAULT 0,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for renewal attempts
CREATE INDEX idx_renewal_attempts_subscription_id ON subscription_renewal_attempts(subscription_id);
CREATE INDEX idx_renewal_attempts_status ON subscription_renewal_attempts(status);
CREATE INDEX idx_renewal_attempts_attempted_at ON subscription_renewal_attempts(attempted_at);
CREATE INDEX idx_renewal_attempts_next_retry ON subscription_renewal_attempts(next_retry_at) WHERE next_retry_at IS NOT NULL;

-- Add index for next renewal attempt scheduling
CREATE INDEX idx_subscriptions_next_renewal_attempt ON subscriptions(next_renewal_attempt_at) WHERE next_renewal_attempt_at IS NOT NULL;

-- =====================================================
-- 3. OrderManagement Service Schema Changes
-- =====================================================

-- Add broker markup fields to rfq_bids
ALTER TABLE rfq_bids
ADD COLUMN broker_markup_percentage DECIMAL(5,2) DEFAULT 0,
ADD COLUMN broker_markup_amount DECIMAL(15,2) DEFAULT 0,
ADD COLUMN markup_calculation_method VARCHAR(50) DEFAULT 'Percentage',
ADD COLUMN markup_justification TEXT NULL,
ADD COLUMN original_quote_amount DECIMAL(15,2) NULL,
ADD COLUMN final_quote_amount DECIMAL(15,2) NULL,
ADD COLUMN is_markup_editable BOOLEAN DEFAULT true,
ADD COLUMN markup_last_modified TIMESTAMP NULL,
ADD COLUMN markup_modified_by UUID NULL,
ADD COLUMN markup_metadata JSONB DEFAULT '{}';

-- Create broker markup history table
CREATE TABLE broker_markup_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bid_id UUID NOT NULL REFERENCES rfq_bids(id) ON DELETE CASCADE,
    rfq_id UUID NOT NULL,
    broker_id UUID NOT NULL,
    previous_markup_percentage DECIMAL(5,2) DEFAULT 0,
    new_markup_percentage DECIMAL(5,2) NOT NULL,
    previous_amount DECIMAL(15,2) DEFAULT 0,
    new_amount DECIMAL(15,2) NOT NULL,
    calculation_method VARCHAR(50) NOT NULL,
    justification TEXT NULL,
    subscription_tier_limit DECIMAL(5,2) NOT NULL,
    modified_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    modified_by UUID NOT NULL,
    metadata JSONB DEFAULT '{}'
);

-- Add indexes for markup history
CREATE INDEX idx_markup_history_bid_id ON broker_markup_history(bid_id);
CREATE INDEX idx_markup_history_broker_id ON broker_markup_history(broker_id);
CREATE INDEX idx_markup_history_modified_at ON broker_markup_history(modified_at);

-- Add negotiation tracking enhancements
ALTER TABLE rfq_negotiations
ADD COLUMN negotiation_type VARCHAR(50) DEFAULT 'Standard', -- Standard, Markup, CounterOffer
ADD COLUMN markup_related BOOLEAN DEFAULT false,
ADD COLUMN original_quote_reference UUID NULL,
ADD COLUMN negotiation_metadata JSONB DEFAULT '{}';

-- =====================================================
-- 4. TripManagement Service Schema Changes
-- =====================================================

-- Add per-leg broker assignment to trip_legs
ALTER TABLE trip_legs
ADD COLUMN assigned_broker_id UUID NULL,
ADD COLUMN broker_assignment_date TIMESTAMP NULL,
ADD COLUMN broker_assignment_notes TEXT NULL,
ADD COLUMN broker_assignment_status VARCHAR(50) DEFAULT 'Unassigned',
ADD COLUMN broker_performance_rating DECIMAL(3,2) NULL,
ADD COLUMN broker_assignment_metadata JSONB DEFAULT '{}';

-- Create broker leg assignments tracking table
CREATE TABLE trip_leg_broker_assignments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    trip_id UUID NOT NULL,
    leg_id UUID NOT NULL REFERENCES trip_legs(id) ON DELETE CASCADE,
    broker_id UUID NOT NULL,
    assigned_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    assigned_by UUID NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'Active', -- Active, Completed, Cancelled, Reassigned
    performance_rating DECIMAL(3,2) NULL,
    completion_notes TEXT NULL,
    completed_at TIMESTAMP NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for broker assignments
CREATE INDEX idx_leg_broker_assignments_trip_id ON trip_leg_broker_assignments(trip_id);
CREATE INDEX idx_leg_broker_assignments_broker_id ON trip_leg_broker_assignments(broker_id);
CREATE INDEX idx_leg_broker_assignments_status ON trip_leg_broker_assignments(status);

-- Enhance feedback system for mandatory comments on low ratings
ALTER TABLE trip_feedback
ADD COLUMN requires_mandatory_comment BOOLEAN DEFAULT false,
ADD COLUMN mandatory_comment_provided BOOLEAN DEFAULT false,
ADD COLUMN low_rating_threshold DECIMAL(3,2) DEFAULT 3.0,
ADD COLUMN feedback_validation_status VARCHAR(50) DEFAULT 'Valid',
ADD COLUMN admin_review_required BOOLEAN DEFAULT false;

-- Add index for feedback requiring review
CREATE INDEX idx_trip_feedback_admin_review ON trip_feedback(admin_review_required) WHERE admin_review_required = true;

-- =====================================================
-- 5. FinancialPayment Service Schema Changes
-- =====================================================

-- Add auto-generation tracking to invoices
ALTER TABLE invoices
ADD COLUMN is_auto_generated BOOLEAN DEFAULT false,
ADD COLUMN auto_generation_trigger VARCHAR(100) NULL, -- order_confirmation, milestone_completion, etc.
ADD COLUMN generation_metadata JSONB DEFAULT '{}',
ADD COLUMN auto_generation_timestamp TIMESTAMP NULL,
ADD COLUMN manual_override_reason TEXT NULL;

-- Create invoice auto-generation log table
CREATE TABLE invoice_auto_generation_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id UUID NOT NULL,
    invoice_id UUID NULL,
    trigger_event VARCHAR(100) NOT NULL,
    generation_status VARCHAR(50) NOT NULL, -- Pending, Processing, Completed, Failed
    processing_time_ms INTEGER DEFAULT 0,
    error_message TEXT NULL,
    retry_count INTEGER DEFAULT 0,
    next_retry_at TIMESTAMP NULL,
    triggered_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    metadata JSONB DEFAULT '{}'
);

-- Add indexes for auto-generation log
CREATE INDEX idx_invoice_auto_gen_log_order_id ON invoice_auto_generation_log(order_id);
CREATE INDEX idx_invoice_auto_gen_log_status ON invoice_auto_generation_log(generation_status);
CREATE INDEX idx_invoice_auto_gen_log_triggered_at ON invoice_auto_generation_log(triggered_at);
CREATE INDEX idx_invoice_auto_gen_log_retry ON invoice_auto_generation_log(next_retry_at) WHERE next_retry_at IS NOT NULL;

-- =====================================================
-- 6. AnalyticsBIService Schema Changes
-- =====================================================

-- Create funnel tracking events table for RFQ-to-Quote-to-Order analytics
CREATE TABLE funnel_tracking_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    session_id UUID NULL,
    funnel_type VARCHAR(50) NOT NULL, -- rfq_to_order, quote_to_order, etc.
    event_name VARCHAR(100) NOT NULL,
    step_order INTEGER NOT NULL,
    entity_id UUID NOT NULL, -- RFQ ID, Quote ID, Order ID
    entity_type VARCHAR(50) NOT NULL,
    event_properties JSONB DEFAULT '{}',
    conversion_value DECIMAL(15,2) NULL,
    timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for funnel analytics
CREATE INDEX idx_funnel_events_user_id ON funnel_tracking_events(user_id);
CREATE INDEX idx_funnel_events_funnel_type ON funnel_tracking_events(funnel_type);
CREATE INDEX idx_funnel_events_timestamp ON funnel_tracking_events(timestamp);
CREATE INDEX idx_funnel_events_entity ON funnel_tracking_events(entity_id, entity_type);

-- Create monthly performance snapshots table
CREATE TABLE monthly_performance_snapshots (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    user_type VARCHAR(50) NOT NULL,
    snapshot_month DATE NOT NULL, -- First day of the month
    performance_metrics JSONB NOT NULL DEFAULT '{}',
    comparison_metrics JSONB DEFAULT '{}', -- Comparison with previous month
    generated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    notification_sent BOOLEAN DEFAULT false,
    notification_sent_at TIMESTAMP NULL
);

-- Add indexes for performance snapshots
CREATE INDEX idx_performance_snapshots_user_id ON monthly_performance_snapshots(user_id);
CREATE INDEX idx_performance_snapshots_month ON monthly_performance_snapshots(snapshot_month);
CREATE INDEX idx_performance_snapshots_notification ON monthly_performance_snapshots(notification_sent) WHERE notification_sent = false;

-- =====================================================
-- 7. CommunicationNotification Service Schema Changes
-- =====================================================

-- Add broker-specific alert types and preferences
ALTER TABLE user_notification_preferences
ADD COLUMN broker_alert_preferences JSONB DEFAULT '{"kyc_verification": true, "subscription_renewal": true, "markup_limits": true, "invoice_generation": true, "performance_alerts": true}',
ADD COLUMN alert_delivery_preferences JSONB DEFAULT '{"immediate": ["email"], "daily_digest": ["email"], "weekly_summary": ["email"]}',
ADD COLUMN custom_alert_rules JSONB DEFAULT '[]';

-- Create broker-specific notification templates
CREATE TABLE broker_notification_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    template_name VARCHAR(100) NOT NULL UNIQUE,
    template_type VARCHAR(50) NOT NULL, -- kyc_verification, subscription_renewal, etc.
    subject_template TEXT NOT NULL,
    body_template TEXT NOT NULL,
    template_variables JSONB DEFAULT '[]',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for notification templates
CREATE INDEX idx_broker_templates_type ON broker_notification_templates(template_type);
CREATE INDEX idx_broker_templates_active ON broker_notification_templates(is_active) WHERE is_active = true;

-- =====================================================
-- 8. Data Migration and Cleanup
-- =====================================================

-- Update existing subscriptions with default auto-renewal preferences
UPDATE subscriptions 
SET auto_renewal_preferences = '{"isEnabled": true, "daysBeforeExpiry": 7, "maxRetryAttempts": 3, "notifyBeforeRenewal": true, "notifyAfterRenewal": true, "notifyOnFailure": true, "notificationChannels": ["email"]}'
WHERE auto_renewal_preferences = '{}' OR auto_renewal_preferences IS NULL;

-- Set next renewal attempt date for active subscriptions
UPDATE subscriptions 
SET next_renewal_attempt_at = next_billing_date - INTERVAL '7 days'
WHERE status = 'Active' 
  AND auto_renew = true 
  AND next_renewal_attempt_at IS NULL
  AND next_billing_date > CURRENT_TIMESTAMP;

-- Mark existing invoices as manually generated
UPDATE invoices 
SET is_auto_generated = false, 
    generation_metadata = '{"migration": true, "original_generation": "manual"}'
WHERE is_auto_generated IS NULL;

-- Set default markup editability for existing bids
UPDATE rfq_bids 
SET is_markup_editable = true,
    original_quote_amount = quoted_amount
WHERE is_markup_editable IS NULL;

-- =====================================================
-- 9. Create Views for Analytics and Reporting
-- =====================================================

-- View for KYC verification analytics
CREATE VIEW v_kyc_verification_analytics AS
SELECT 
    up.user_type,
    up.kyc_verification_status,
    COUNT(*) as user_count,
    AVG(CASE WHEN kvh.confidence_score IS NOT NULL THEN kvh.confidence_score ELSE 0 END) as avg_confidence_score,
    AVG(kvh.processing_time_ms) as avg_processing_time_ms
FROM user_profiles up
LEFT JOIN documents d ON d.user_id = up.user_id
LEFT JOIN kyc_verification_history kvh ON kvh.document_id = d.id
GROUP BY up.user_type, up.kyc_verification_status;

-- View for subscription renewal analytics
CREATE VIEW v_subscription_renewal_analytics AS
SELECT 
    DATE_TRUNC('month', sra.attempted_at) as month,
    sra.status,
    COUNT(*) as attempt_count,
    SUM(sra.attempted_amount) as total_amount,
    AVG(sra.processing_time_ms) as avg_processing_time
FROM subscription_renewal_attempts sra
GROUP BY DATE_TRUNC('month', sra.attempted_at), sra.status;

-- View for broker markup analytics
CREATE VIEW v_broker_markup_analytics AS
SELECT 
    bmh.broker_id,
    DATE_TRUNC('month', bmh.modified_at) as month,
    COUNT(*) as markup_changes,
    AVG(bmh.new_markup_percentage) as avg_markup_percentage,
    MAX(bmh.new_markup_percentage) as max_markup_percentage,
    SUM(bmh.new_amount - bmh.previous_amount) as total_markup_increase
FROM broker_markup_history bmh
GROUP BY bmh.broker_id, DATE_TRUNC('month', bmh.modified_at);

-- =====================================================
-- 10. Add Constraints and Validation
-- =====================================================

-- Add check constraints for data validation
ALTER TABLE user_profiles 
ADD CONSTRAINT chk_kyc_verification_status 
CHECK (kyc_verification_status IN ('Pending', 'InProgress', 'Verified', 'Failed', 'Expired', 'RequiresManualReview'));

ALTER TABLE rfq_bids 
ADD CONSTRAINT chk_broker_markup_percentage 
CHECK (broker_markup_percentage >= 0 AND broker_markup_percentage <= 100);

ALTER TABLE subscription_renewal_attempts 
ADD CONSTRAINT chk_renewal_attempt_status 
CHECK (status IN ('Pending', 'Processing', 'Successful', 'Failed', 'Cancelled'));

ALTER TABLE trip_feedback 
ADD CONSTRAINT chk_low_rating_threshold 
CHECK (low_rating_threshold >= 1.0 AND low_rating_threshold <= 5.0);

-- =====================================================
-- Migration Complete
-- =====================================================

-- Insert migration record
INSERT INTO schema_migrations (version, description, applied_at) 
VALUES ('2024.01.15.001', 'Add Broker App Features Support', CURRENT_TIMESTAMP);
