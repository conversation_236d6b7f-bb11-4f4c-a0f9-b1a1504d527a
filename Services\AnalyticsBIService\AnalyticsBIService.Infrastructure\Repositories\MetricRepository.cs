using Microsoft.EntityFrameworkCore;
using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Repositories;
using AnalyticsBIService.Infrastructure.Persistence;
using Shared.Infrastructure.Repositories;
using Shared.Infrastructure.Caching;
using Shared.Infrastructure.Monitoring;
using AnalyticsBIService.Domain.Enums;
using AnalyticsBIService.Domain.ValueObjects;

namespace AnalyticsBIService.Infrastructure.Repositories;

public class MetricRepository : OptimizedRepositoryBase<Metric, Guid>, IMetricRepository
{
    public MetricRepository(
        AnalyticsBIDbContext context,
        ICachingService cachingService,
        IPerformanceMetrics performanceMetrics)
        : base(context, cachingService, performanceMetrics)
    {
    }

    public async Task<Metric?> GetByNameAsync(string name, Guid? userId = null, CancellationToken cancellationToken = default)
    {
        var query = Context.Set<Metric>()
            .Where(m => m.Name == name);

        if (userId.HasValue)
        {
            query = query.Where(m => m.UserId == userId.Value);
        }

        return await query
            .OrderByDescending(m => m.CreatedAt)
            .FirstOrDefaultAsync(cancellationToken);
    }

    public async Task<PagedResult<Metric>> GetByCategoryAsync(string category, int page, int pageSize, CancellationToken cancellationToken = default)
    {
        var query = Context.Set<Metric>()
            .Where(m => m.Category.ToString() == category)
            .OrderByDescending(m => m.CreatedAt);

        var totalCount = await query.CountAsync(cancellationToken);
        var items = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return new PagedResult<Metric>
        {
            Items = items,
            TotalCount = totalCount,
            PageNumber = page,
            PageSize = pageSize
        };
    }

    public async Task<PagedResult<Metric>> GetByTypeAsync(MetricType type, int page, int pageSize, CancellationToken cancellationToken = default)
    {
        var query = Context.Set<Metric>()
            .Where(m => m.Type == type)
            .OrderByDescending(m => m.CreatedAt);

        var totalCount = await query.CountAsync(cancellationToken);
        var items = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return new PagedResult<Metric>
        {
            Items = items,
            TotalCount = totalCount,
            PageNumber = page,
            PageSize = pageSize
        };
    }

    public async Task<PagedResult<Metric>> GetByUserIdAsync(Guid userId, int page, int pageSize, CancellationToken cancellationToken = default)
    {
        var query = Context.Set<Metric>()
            .Where(m => m.UserId == userId)
            .OrderByDescending(m => m.CreatedAt);

        var totalCount = await query.CountAsync(cancellationToken);
        var items = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return new PagedResult<Metric>
        {
            Items = items,
            TotalCount = totalCount,
            PageNumber = page,
            PageSize = pageSize
        };
    }

    public async Task<IEnumerable<Metric>> GetByDataSourceAsync(DataSourceType dataSource, CancellationToken cancellationToken = default)
    {
        return await Context.Set<Metric>()
            .Where(m => m.DataSource == dataSource)
            .OrderByDescending(m => m.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<PagedResult<Metric>> GetByDateRangeAsync(DateTime startDate, DateTime endDate, int page, int pageSize, CancellationToken cancellationToken = default)
    {
        var query = Context.Set<Metric>()
            .Where(m => m.CreatedAt >= startDate && m.CreatedAt <= endDate)
            .OrderByDescending(m => m.CreatedAt);

        var totalCount = await query.CountAsync(cancellationToken);
        var items = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return new PagedResult<Metric>
        {
            Items = items,
            TotalCount = totalCount,
            PageNumber = page,
            PageSize = pageSize
        };
    }

    public async Task<Metric?> GetLatestByNameAsync(string name, CancellationToken cancellationToken = default)
    {
        return await Context.Set<Metric>()
            .Where(m => m.Name == name)
            .OrderByDescending(m => m.CreatedAt)
            .FirstOrDefaultAsync(cancellationToken);
    }

    public async Task<IEnumerable<Metric>> GetLatestByCategoryAsync(KPICategory category, int count = 10, CancellationToken cancellationToken = default)
    {
        return await Context.Set<Metric>()
            .Where(m => m.Category == category)
            .OrderByDescending(m => m.CreatedAt)
            .Take(count)
            .ToListAsync(cancellationToken);
    }

    public async Task<Dictionary<string, decimal>> GetAverageValuesByNameAsync(IEnumerable<string> metricNames, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        return await Context.Set<Metric>()
            .Where(m => metricNames.Contains(m.Name) && m.CreatedAt >= startDate && m.CreatedAt <= endDate)
            .GroupBy(m => m.Name)
            .Select(g => new { Name = g.Key, Average = g.Average(m => m.Value.Value) })
            .ToDictionaryAsync(x => x.Name, x => x.Average, cancellationToken);
    }

    public async Task<Dictionary<KPICategory, int>> GetCountsByCategoryAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        return await Context.Set<Metric>()
            .Where(m => m.CreatedAt >= startDate && m.CreatedAt <= endDate)
            .GroupBy(m => m.Category)
            .Select(g => new { Category = g.Key, Count = g.Count() })
            .ToDictionaryAsync(x => x.Category, x => x.Count, cancellationToken);
    }

    public async Task<IEnumerable<Metric>> GetTrendingMetricsAsync(int days = 7, int count = 10, CancellationToken cancellationToken = default)
    {
        var startDate = DateTime.UtcNow.AddDays(-days);

        return await Context.Set<Metric>()
            .Where(m => m.CreatedAt >= startDate)
            .GroupBy(m => m.Name)
            .OrderByDescending(g => g.Count())
            .Take(count)
            .SelectMany(g => g.OrderByDescending(m => m.CreatedAt).Take(1))
            .ToListAsync(cancellationToken);
    }

    public async Task BulkInsertAsync(IEnumerable<Metric> metrics, CancellationToken cancellationToken = default)
    {
        await Context.Set<Metric>().AddRangeAsync(metrics, cancellationToken);
        await Context.SaveChangesAsync(cancellationToken);
    }

    public async Task<IEnumerable<Metric>> SearchAsync(string searchTerm, CancellationToken cancellationToken = default)
    {
        return await Context.Set<Metric>()
            .Where(m => m.Name.Contains(searchTerm) ||
                       m.Description.Contains(searchTerm))
            .OrderByDescending(m => m.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<PagedResult<Metric>> GetAllAsync(int page, int pageSize, CancellationToken cancellationToken = default)
    {
        var query = Context.Set<Metric>()
            .OrderByDescending(m => m.CreatedAt);

        var totalCount = await query.CountAsync(cancellationToken);
        var items = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return new PagedResult<Metric>
        {
            Items = items,
            TotalCount = totalCount,
            PageNumber = page,
            PageSize = pageSize
        };
    }

    public async Task<List<Metric>> GetTimeSeriesAsync(string metricName, DateTime startDate, DateTime endDate, Guid? userId = null, CancellationToken cancellationToken = default)
    {
        var query = Context.Set<Metric>()
            .Where(m => m.Name == metricName && m.CreatedAt >= startDate && m.CreatedAt <= endDate);

        if (userId.HasValue)
        {
            query = query.Where(m => m.UserId == userId.Value);
        }

        return await query
            .OrderBy(m => m.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<Metric>> GetKPIPerformanceAsync(List<string> metricNames, Guid? userId = null, CancellationToken cancellationToken = default)
    {
        var query = Context.Set<Metric>()
            .Where(m => metricNames.Contains(m.Name));

        if (userId.HasValue)
        {
            query = query.Where(m => m.UserId == userId.Value);
        }

        return await query
            .OrderByDescending(m => m.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<Dictionary<string, Metric>> GetRealTimeMetricsAsync(List<string> metricNames, Guid? userId = null, CancellationToken cancellationToken = default)
    {
        var query = Context.Set<Metric>()
            .Where(m => metricNames.Contains(m.Name));

        if (userId.HasValue)
        {
            query = query.Where(m => m.UserId == userId.Value);
        }

        var metrics = await query
            .GroupBy(m => m.Name)
            .Select(g => g.OrderByDescending(m => m.CreatedAt).First())
            .ToListAsync(cancellationToken);

        return metrics.ToDictionary(m => m.Name, m => m);
    }

    public async Task<Dictionary<string, List<Metric>>> GetMetricTrendsAsync(List<string> metricNames, DateTime startDate, DateTime endDate, TimePeriod aggregationPeriod, Guid? userId = null, CancellationToken cancellationToken = default)
    {
        var query = Context.Set<Metric>()
            .Where(m => metricNames.Contains(m.Name) && m.CreatedAt >= startDate && m.CreatedAt <= endDate);

        if (userId.HasValue)
        {
            query = query.Where(m => m.UserId == userId.Value);
        }

        var metrics = await query
            .OrderBy(m => m.CreatedAt)
            .ToListAsync(cancellationToken);

        return metrics
            .GroupBy(m => m.Name)
            .ToDictionary(g => g.Key, g => g.ToList());
    }

    public async Task<List<Metric>> CompareMetricsAsync(List<string> metricNames, DateTime startDate, DateTime endDate, Guid? userId = null, CancellationToken cancellationToken = default)
    {
        var query = Context.Set<Metric>()
            .Where(m => metricNames.Contains(m.Name) && m.CreatedAt >= startDate && m.CreatedAt <= endDate);

        if (userId.HasValue)
        {
            query = query.Where(m => m.UserId == userId.Value);
        }

        return await query
            .OrderBy(m => m.Name)
            .ThenBy(m => m.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<Metric>> GetUnderperformingKPIsAsync(Guid? userId = null, CancellationToken cancellationToken = default)
    {
        var query = Context.Set<Metric>()
            .Where(m => m.Target != null && m.Value.Value < m.Target.TargetValue);

        if (userId.HasValue)
        {
            query = query.Where(m => m.UserId == userId.Value);
        }

        return await query
            .OrderByDescending(m => m.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task UpdateMetricValueAsync(Guid metricId, MetricValue newValue, CancellationToken cancellationToken = default)
    {
        var metric = await Context.Set<Metric>()
            .FirstOrDefaultAsync(m => m.Id == metricId, cancellationToken);

        if (metric != null)
        {
            metric.UpdateValue(newValue);
            await Context.SaveChangesAsync(cancellationToken);
        }
    }

    public async Task<List<Metric>> GetMetricsByCategoryAsync(MetricCategory category, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        return await Context.Set<Metric>()
            .Where(m => m.Category == (KPICategory)category && m.CreatedAt >= startDate && m.CreatedAt <= endDate)
            .OrderBy(m => m.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<Metric>> GetMetricsByTypeAsync(MetricType metricType, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        return await Context.Set<Metric>()
            .Where(m => m.Type == metricType && m.CreatedAt >= startDate && m.CreatedAt <= endDate)
            .OrderBy(m => m.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task SetMetricTargetAsync(Guid metricId, KPITarget target, CancellationToken cancellationToken = default)
    {
        var metric = await Context.Set<Metric>()
            .FirstOrDefaultAsync(m => m.Id == metricId, cancellationToken);

        if (metric != null)
        {
            metric.SetTarget(target);
            await Context.SaveChangesAsync(cancellationToken);
        }
    }

    public async Task BulkUpdateAsync(IEnumerable<Metric> metrics, CancellationToken cancellationToken = default)
    {
        Context.Set<Metric>().UpdateRange(metrics);
        await Context.SaveChangesAsync(cancellationToken);
    }

    public async Task DeleteOldMetricsAsync(DateTime cutoffDate, CancellationToken cancellationToken = default)
    {
        var oldMetrics = await Context.Set<Metric>()
            .Where(m => m.CreatedAt < cutoffDate)
            .ToListAsync(cancellationToken);

        Context.Set<Metric>().RemoveRange(oldMetrics);
        await Context.SaveChangesAsync(cancellationToken);
    }

    public async Task<List<Metric>> GetMetricsByDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        return await Context.Set<Metric>()
            .Where(m => m.CreatedAt >= startDate && m.CreatedAt <= endDate)
            .OrderBy(m => m.CreatedAt)
            .ToListAsync(cancellationToken);
    }
}
