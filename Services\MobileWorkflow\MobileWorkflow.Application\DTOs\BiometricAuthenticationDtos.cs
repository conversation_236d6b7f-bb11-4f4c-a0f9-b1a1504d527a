namespace MobileWorkflow.Application.DTOs;

public class BiometricProfileDto
{
    public Guid Id { get; set; }
    public Guid UserId { get; set; }
    public string DeviceId { get; set; } = string.Empty;
    public string Platform { get; set; } = string.Empty;
    public List<string> SupportedBiometrics { get; set; } = new();
    public List<string> EnabledBiometrics { get; set; } = new();
    public bool IsActive { get; set; }
    public DateTime EnrolledAt { get; set; }
    public DateTime? LastUsedAt { get; set; }
    public Dictionary<string, object> SecuritySettings { get; set; } = new();
    public bool HasAnyBiometricEnabled { get; set; }
}

public class BiometricPolicyDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Platform { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public bool IsDefault { get; set; }
    public Dictionary<string, object> PolicyRules { get; set; } = new();
    public Dictionary<string, object> SecurityRequirements { get; set; } = new();
    public Dictionary<string, object> FallbackOptions { get; set; } = new();
    public List<string> AllowedBiometrics { get; set; } = new();
    public List<string> RequiredBiometrics { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public string? UpdatedBy { get; set; }
}

public class BiometricAuthenticationAttemptDto
{
    public Guid Id { get; set; }
    public Guid UserId { get; set; }
    public string DeviceId { get; set; } = string.Empty;
    public string BiometricType { get; set; } = string.Empty;
    public string Result { get; set; } = string.Empty;
    public DateTime AttemptedAt { get; set; }
    public string? ErrorCode { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object> AttemptContext { get; set; } = new();
    public Dictionary<string, object> SecurityMetrics { get; set; } = new();
    public bool IsSuccessful { get; set; }
    public bool IsFailed { get; set; }
    public bool WasCancelled { get; set; }
}

public class BiometricCapabilitiesDto
{
    public string Platform { get; set; } = string.Empty;
    public string DeviceModel { get; set; } = string.Empty;
    public List<string> SupportedBiometrics { get; set; } = new();
    public bool HasHardwareSupport { get; set; }
    public bool HasSecureEnclave { get; set; }
    public List<string> SupportedSecurityLevels { get; set; } = new();
    public int MaxEnrollments { get; set; }
    public bool RequiresUserPresence { get; set; }
    public bool SupportsLivenessDetection { get; set; }
}

public class BiometricSecurityReportDto
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public int TotalAttempts { get; set; }
    public int SuccessfulAttempts { get; set; }
    public int FailedAttempts { get; set; }
    public int CancelledAttempts { get; set; }
    public double SuccessRate { get; set; }
    public int ActiveProfiles { get; set; }
    public int TotalProfiles { get; set; }
    public Dictionary<string, int> BiometricBreakdown { get; set; } = new();
    public Dictionary<string, int> PlatformBreakdown { get; set; } = new();
    public Dictionary<int, int> HourlyAttempts { get; set; } = new();
    public List<string> SecurityAlerts { get; set; } = new();
}

// Request DTOs
public class CreateBiometricProfileRequest
{
    public Guid UserId { get; set; }
    public string DeviceId { get; set; } = string.Empty;
    public string Platform { get; set; } = string.Empty;
    public string DeviceModel { get; set; } = string.Empty;
    public Dictionary<string, object>? SecuritySettings { get; set; }
}

public class EnableBiometricRequest
{
    public Guid UserId { get; set; }
    public string DeviceId { get; set; } = string.Empty;
    public string BiometricType { get; set; } = string.Empty;
    public Dictionary<string, object> BiometricData { get; set; } = new();
}

public class BiometricAuthenticationRequest
{
    public Guid UserId { get; set; }
    public string DeviceId { get; set; } = string.Empty;
    public string BiometricType { get; set; } = string.Empty;
    public Dictionary<string, object> BiometricData { get; set; } = new();
    public string? Challenge { get; set; }
    public string? UserAgent { get; set; }
    public string? IpAddress { get; set; }
}

public class CreateBiometricPolicyRequest
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Platform { get; set; } = string.Empty;
    public Dictionary<string, object> PolicyRules { get; set; } = new();
    public Dictionary<string, object>? SecurityRequirements { get; set; }
    public Dictionary<string, object>? FallbackOptions { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

public class UpdateBiometricPolicyRequest
{
    public Dictionary<string, object>? PolicyRules { get; set; }
    public Dictionary<string, object>? SecurityRequirements { get; set; }
    public Dictionary<string, object>? FallbackOptions { get; set; }
    public string UpdatedBy { get; set; } = string.Empty;
}

public class BiometricEnrollmentDto
{
    public Guid Id { get; set; }
    public Guid UserId { get; set; }
    public string DeviceId { get; set; } = string.Empty;
    public string BiometricType { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty; // Enrolled, Pending, Failed, Removed
    public DateTime EnrolledAt { get; set; }
    public DateTime? LastUsedAt { get; set; }
    public Dictionary<string, object> EnrollmentData { get; set; } = new();
    public int UsageCount { get; set; }
    public double QualityScore { get; set; }
}

public class BiometricTemplateDto
{
    public Guid Id { get; set; }
    public Guid ProfileId { get; set; }
    public string BiometricType { get; set; } = string.Empty;
    public string TemplateData { get; set; } = string.Empty; // Encrypted/hashed template
    public string Algorithm { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class BiometricMatchResultDto
{
    public bool IsMatch { get; set; }
    public double ConfidenceScore { get; set; }
    public string MatchAlgorithm { get; set; } = string.Empty;
    public TimeSpan ProcessingTime { get; set; }
    public Dictionary<string, object> MatchDetails { get; set; } = new();
}

public class BiometricFallbackDto
{
    public string FallbackType { get; set; } = string.Empty; // PIN, Pattern, Password, SecurityQuestions
    public bool IsEnabled { get; set; }
    public Dictionary<string, object> Configuration { get; set; } = new();
    public int MaxAttempts { get; set; }
    public TimeSpan Timeout { get; set; }
}

public class BiometricAuditLogDto
{
    public Guid Id { get; set; }
    public Guid UserId { get; set; }
    public string DeviceId { get; set; } = string.Empty;
    public string Action { get; set; } = string.Empty; // Enroll, Authenticate, Remove, PolicyChange
    public string BiometricType { get; set; } = string.Empty;
    public string Result { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
    public Dictionary<string, object> Details { get; set; } = new();
}

public class BiometricComplianceDto
{
    public Guid ProfileId { get; set; }
    public bool IsCompliant { get; set; }
    public List<string> Violations { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
    public DateTime LastChecked { get; set; }
    public string ComplianceLevel { get; set; } = string.Empty; // Low, Medium, High
    public Dictionary<string, object> ComplianceDetails { get; set; } = new();
}

public class BiometricPerformanceDto
{
    public string BiometricType { get; set; } = string.Empty;
    public string Platform { get; set; } = string.Empty;
    public double AverageAuthenticationTime { get; set; }
    public double SuccessRate { get; set; }
    public double FalseAcceptanceRate { get; set; }
    public double FalseRejectionRate { get; set; }
    public int TotalAttempts { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
}

public class BiometricHealthCheckDto
{
    public string Component { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty; // Healthy, Warning, Critical
    public string? Message { get; set; }
    public Dictionary<string, object> Metrics { get; set; } = new();
    public DateTime CheckedAt { get; set; }
    public TimeSpan ResponseTime { get; set; }
}

public class BiometricConfigurationDto
{
    public string Platform { get; set; } = string.Empty;
    public Dictionary<string, object> GlobalSettings { get; set; } = new();
    public Dictionary<string, object> BiometricSettings { get; set; } = new();
    public Dictionary<string, object> SecuritySettings { get; set; } = new();
    public Dictionary<string, object> FallbackSettings { get; set; } = new();
    public bool IsActive { get; set; }
    public DateTime LastUpdated { get; set; }
    public string UpdatedBy { get; set; } = string.Empty;
}

public class BiometricMigrationDto
{
    public Guid Id { get; set; }
    public string FromVersion { get; set; } = string.Empty;
    public string ToVersion { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty; // Pending, InProgress, Completed, Failed
    public int TotalProfiles { get; set; }
    public int MigratedProfiles { get; set; }
    public int FailedProfiles { get; set; }
    public DateTime StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public List<string> Errors { get; set; } = new();
    public Dictionary<string, object> MigrationDetails { get; set; } = new();
}
