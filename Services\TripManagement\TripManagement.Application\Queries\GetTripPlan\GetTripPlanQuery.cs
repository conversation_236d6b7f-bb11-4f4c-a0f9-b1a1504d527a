using MediatR;
using TripManagement.Application.DTOs;

namespace TripManagement.Application.Queries.GetTripPlan;

public class GetTripPlanQuery : IRequest<TripPlanDto?>
{
    public Guid TripId { get; set; }
    public Guid RequestingUserId { get; set; }
    public bool IncludeResourceDetails { get; set; } = true;
    public bool IncludeRouteDetails { get; set; } = true;
    public bool IncludeStopDetails { get; set; } = true;
    public bool GeneratePrintableVersion { get; set; } = false;
}
