using CommunicationNotification.Domain.Enums;
using Shared.Domain.Common;
using CommunicationNotification.Domain.ValueObjects;
using Shared.Domain.Common;

namespace CommunicationNotification.Domain.Entities;

/// <summary>
/// Feature flag entity for gradual rollout and experimentation
/// </summary>
public class FeatureFlag : BaseEntity
{
    public string Key { get; private set; }
    public string Name { get; private set; }
    public string Description { get; private set; }
    public FeatureFlagType Type { get; private set; }
    public FeatureFlagStatus Status { get; private set; }
    public string Environment { get; private set; }
    public DateTime? StartDate { get; private set; }
    public DateTime? EndDate { get; private set; }
    public RolloutConfiguration RolloutConfiguration { get; private set; }
    public TargetingRules TargetingRules { get; private set; }
    public List<FeatureFlagVariant> Variants { get; private set; }
    public FeatureFlagMetrics Metrics { get; private set; }
    public Dictionary<string, object> DefaultValue { get; private set; }
    public Dictionary<string, object> Configuration { get; private set; }
    public List<FeatureFlagUsage> UsageHistory { get; private set; }
    public Guid CreatedByUserId { get; private set; }
    public Guid? ModifiedByUserId { get; private set; }
    public DateTime? LastModifiedAt { get; private set; }

    private FeatureFlag()
    {
        Key = string.Empty;
        Name = string.Empty;
        Description = string.Empty;
        Environment = string.Empty;
        RolloutConfiguration = RolloutConfiguration.Default();
        TargetingRules = TargetingRules.Empty();
        Variants = new List<FeatureFlagVariant>();
        Metrics = FeatureFlagMetrics.Empty();
        DefaultValue = new Dictionary<string, object>();
        Configuration = new Dictionary<string, object>();
        UsageHistory = new List<FeatureFlagUsage>();
    }

    public FeatureFlag(
        string key,
        string name,
        string description,
        FeatureFlagType type,
        string environment,
        Guid createdByUserId,
        DateTime? startDate = null,
        DateTime? endDate = null,
        RolloutConfiguration? rolloutConfiguration = null,
        TargetingRules? targetingRules = null,
        Dictionary<string, object>? defaultValue = null,
        Dictionary<string, object>? configuration = null)
    {
        if (string.IsNullOrWhiteSpace(key))
            throw new ArgumentException("Feature flag key cannot be empty", nameof(key));
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Feature flag name cannot be empty", nameof(name));
        if (createdByUserId == Guid.Empty)
            throw new ArgumentException("Created by user ID cannot be empty", nameof(createdByUserId));

        Key = key;
        Name = name;
        Description = description;
        Type = type;
        Status = FeatureFlagStatus.Draft;
        Environment = environment;
        StartDate = startDate;
        EndDate = endDate;
        RolloutConfiguration = rolloutConfiguration ?? RolloutConfiguration.Default();
        TargetingRules = targetingRules ?? TargetingRules.Empty();
        Variants = new List<FeatureFlagVariant>();
        Metrics = FeatureFlagMetrics.Empty();
        DefaultValue = defaultValue ?? new Dictionary<string, object>();
        Configuration = configuration ?? new Dictionary<string, object>();
        UsageHistory = new List<FeatureFlagUsage>();
        CreatedByUserId = createdByUserId;
    }

    public static FeatureFlag Create(
        string key,
        string name,
        string description,
        FeatureFlagType type,
        string environment,
        Guid createdByUserId,
        DateTime? startDate = null,
        DateTime? endDate = null,
        RolloutConfiguration? rolloutConfiguration = null,
        TargetingRules? targetingRules = null,
        Dictionary<string, object>? defaultValue = null,
        Dictionary<string, object>? configuration = null)
    {
        return new FeatureFlag(key, name, description, type, environment, createdByUserId,
            startDate, endDate, rolloutConfiguration, targetingRules, defaultValue, configuration);
    }

    public void AddVariant(FeatureFlagVariant variant)
    {
        if (variant == null)
            throw new ArgumentNullException(nameof(variant));

        if (Status != FeatureFlagStatus.Draft)
            throw new InvalidOperationException("Cannot modify variants when feature flag is not in draft status");

        if (Variants.Any(v => v.Name.Equals(variant.Name, StringComparison.OrdinalIgnoreCase)))
            throw new InvalidOperationException($"Variant with name '{variant.Name}' already exists");

        Variants.Add(variant);
        ValidateVariantAllocations();
    }

    public void RemoveVariant(string variantName)
    {
        if (Status != FeatureFlagStatus.Draft)
            throw new InvalidOperationException("Cannot modify variants when feature flag is not in draft status");

        var variant = Variants.FirstOrDefault(v => v.Name.Equals(variantName, StringComparison.OrdinalIgnoreCase));
        if (variant != null)
        {
            Variants.Remove(variant);
        }
    }

    public void UpdateRolloutConfiguration(RolloutConfiguration rolloutConfiguration)
    {
        RolloutConfiguration = rolloutConfiguration ?? throw new ArgumentNullException(nameof(rolloutConfiguration));
        LastModifiedAt = DateTime.UtcNow;
    }

    public void UpdateTargetingRules(TargetingRules targetingRules)
    {
        TargetingRules = targetingRules ?? throw new ArgumentNullException(nameof(targetingRules));
        LastModifiedAt = DateTime.UtcNow;
    }

    public void Activate(Guid modifiedByUserId)
    {
        if (Status != FeatureFlagStatus.Draft && Status != FeatureFlagStatus.Paused)
            throw new InvalidOperationException($"Cannot activate feature flag in {Status} status");

        if (Type == FeatureFlagType.ABTest && !Variants.Any())
            throw new InvalidOperationException("Cannot activate A/B test without variants");

        Status = FeatureFlagStatus.Active;
        ModifiedByUserId = modifiedByUserId;
        LastModifiedAt = DateTime.UtcNow;
    }

    public void Pause(Guid modifiedByUserId)
    {
        if (Status != FeatureFlagStatus.Active)
            throw new InvalidOperationException($"Cannot pause feature flag in {Status} status");

        Status = FeatureFlagStatus.Paused;
        ModifiedByUserId = modifiedByUserId;
        LastModifiedAt = DateTime.UtcNow;
    }

    public void Complete(Guid modifiedByUserId)
    {
        Status = FeatureFlagStatus.Completed;
        ModifiedByUserId = modifiedByUserId;
        LastModifiedAt = DateTime.UtcNow;
    }

    public void Archive(Guid modifiedByUserId)
    {
        Status = FeatureFlagStatus.Archived;
        ModifiedByUserId = modifiedByUserId;
        LastModifiedAt = DateTime.UtcNow;
    }

    public bool IsEnabledForUser(Guid userId, Dictionary<string, object>? context = null)
    {
        if (Status != FeatureFlagStatus.Active)
            return false;

        if (StartDate.HasValue && DateTime.UtcNow < StartDate.Value)
            return false;

        if (EndDate.HasValue && DateTime.UtcNow > EndDate.Value)
            return false;

        // Check targeting rules
        if (!TargetingRules.IsUserTargeted(userId, context))
            return false;

        // Check rollout percentage
        return RolloutConfiguration.IsUserIncluded(userId);
    }

    public FeatureFlagVariant? GetVariantForUser(Guid userId, Dictionary<string, object>? context = null)
    {
        if (!IsEnabledForUser(userId, context))
            return null;

        if (Type != FeatureFlagType.ABTest || !Variants.Any())
            return null;

        // Use consistent hashing to assign user to variant
        var hash = GetUserHash(userId);
        var cumulativeAllocation = 0m;

        foreach (var variant in Variants.OrderBy(v => v.Name))
        {
            cumulativeAllocation += variant.TrafficAllocation;
            if (hash <= cumulativeAllocation)
            {
                return variant;
            }
        }

        return Variants.LastOrDefault();
    }

    public object? GetValueForUser(Guid userId, Dictionary<string, object>? context = null)
    {
        if (!IsEnabledForUser(userId, context))
        {
            return GetDefaultValue();
        }

        if (Type == FeatureFlagType.ABTest)
        {
            var variant = GetVariantForUser(userId, context);
            return variant?.Value ?? GetDefaultValue();
        }

        return GetDefaultValue();
    }

    public void RecordUsage(Guid userId, string? variant = null, Dictionary<string, object>? context = null)
    {
        var usage = new FeatureFlagUsage(userId, variant, context);
        UsageHistory.Add(usage);

        // Update metrics
        Metrics.RecordUsage(variant);
    }

    public void UpdateMetrics(FeatureFlagMetrics metrics)
    {
        Metrics = metrics ?? throw new ArgumentNullException(nameof(metrics));
        LastModifiedAt = DateTime.UtcNow;
    }

    private object? GetDefaultValue()
    {
        if (DefaultValue.TryGetValue("value", out var value))
        {
            return value;
        }

        return Type switch
        {
            FeatureFlagType.Boolean => false,
            FeatureFlagType.String => string.Empty,
            FeatureFlagType.Number => 0,
            FeatureFlagType.JSON => new Dictionary<string, object>(),
            _ => null
        };
    }

    private decimal GetUserHash(Guid userId)
    {
        // Create a consistent hash for the user for this feature flag
        var combined = $"{userId}:{Key}";
        var hash = Math.Abs(combined.GetHashCode()) % 10000;
        return hash / 100m; // Convert to percentage (0-100)
    }

    private void ValidateVariantAllocations()
    {
        var totalAllocation = Variants.Sum(v => v.TrafficAllocation);
        if (totalAllocation > 100)
        {
            throw new InvalidOperationException("Total variant traffic allocation cannot exceed 100%");
        }
    }
}

/// <summary>
/// Feature flag variant for A/B testing
/// </summary>
public class FeatureFlagVariant
{
    public Guid Id { get; private set; }
    public string Name { get; private set; }
    public string Description { get; private set; }
    public decimal TrafficAllocation { get; private set; }
    public object? Value { get; private set; }
    public Dictionary<string, object> Configuration { get; private set; }
    public bool IsControl { get; private set; }

    private FeatureFlagVariant()
    {
        Name = string.Empty;
        Description = string.Empty;
        Configuration = new Dictionary<string, object>();
    }

    public FeatureFlagVariant(
        string name,
        string description,
        decimal trafficAllocation,
        object? value = null,
        Dictionary<string, object>? configuration = null,
        bool isControl = false)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Variant name cannot be empty", nameof(name));
        if (trafficAllocation < 0 || trafficAllocation > 100)
            throw new ArgumentException("Traffic allocation must be between 0 and 100", nameof(trafficAllocation));

        Id = Guid.NewGuid();
        Name = name;
        Description = description;
        TrafficAllocation = trafficAllocation;
        Value = value;
        Configuration = configuration ?? new Dictionary<string, object>();
        IsControl = isControl;
    }

    public void UpdateTrafficAllocation(decimal trafficAllocation)
    {
        if (trafficAllocation < 0 || trafficAllocation > 100)
            throw new ArgumentException("Traffic allocation must be between 0 and 100", nameof(trafficAllocation));

        TrafficAllocation = trafficAllocation;
    }

    public void UpdateValue(object? value)
    {
        Value = value;
    }
}

/// <summary>
/// Feature flag usage tracking
/// </summary>
public class FeatureFlagUsage
{
    public Guid Id { get; private set; }
    public Guid UserId { get; private set; }
    public string? Variant { get; private set; }
    public Dictionary<string, object> Context { get; private set; }
    public DateTime Timestamp { get; private set; }

    private FeatureFlagUsage()
    {
        Context = new Dictionary<string, object>();
    }

    public FeatureFlagUsage(Guid userId, string? variant = null, Dictionary<string, object>? context = null)
    {
        Id = Guid.NewGuid();
        UserId = userId;
        Variant = variant;
        Context = context ?? new Dictionary<string, object>();
        Timestamp = DateTime.UtcNow;
    }
}

/// <summary>
/// Feature flag type enumeration
/// </summary>
public enum FeatureFlagType
{
    Boolean = 1,
    String = 2,
    Number = 3,
    JSON = 4,
    ABTest = 5,
    Rollout = 6
}

/// <summary>
/// Feature flag status enumeration
/// </summary>
public enum FeatureFlagStatus
{
    Draft = 1,
    Active = 2,
    Paused = 3,
    Completed = 4,
    Archived = 5
}


