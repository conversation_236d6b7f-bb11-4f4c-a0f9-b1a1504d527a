using AutoMapper;
using FinancialPayment.Application.DTOs;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Application.Common.Mappings;

public class MappingProfile : Profile
{
    public MappingProfile()
    {
        // Entity to DTO mappings
        CreateMap<Commission, CommissionDto>()
            .ForMember(dest => dest.OrderAmount, opt => opt.MapFrom(src => src.OrderAmount.Amount))
            .ForMember(dest => dest.OrderCurrency, opt => opt.MapFrom(src => src.OrderAmount.Currency))
            .ForMember(dest => dest.CalculatedAmount, opt => opt.MapFrom(src => src.CalculatedAmount.Amount))
            .ForMember(dest => dest.CalculatedCurrency, opt => opt.MapFrom(src => src.CalculatedAmount.Currency))
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status.ToString()));

        CreateMap<EscrowAccount, EscrowAccountDto>()
            .ForMember(dest => dest.TotalAmount, opt => opt.MapFrom(src => src.TotalAmount.Amount))
            .ForMember(dest => dest.TotalCurrency, opt => opt.MapFrom(src => src.TotalAmount.Currency))
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status.ToString()));

        CreateMap<Settlement, SettlementDto>()
            .ForMember(dest => dest.TotalAmount, opt => opt.MapFrom(src => src.TotalAmount.Amount))
            .ForMember(dest => dest.TotalCurrency, opt => opt.MapFrom(src => src.TotalAmount.Currency))
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status.ToString()));

        CreateMap<PaymentDispute, PaymentDisputeDto>()
            .ForMember(dest => dest.DisputedAmount, opt => opt.MapFrom(src => src.DisputedAmount.Amount))
            .ForMember(dest => dest.DisputedCurrency, opt => opt.MapFrom(src => src.DisputedAmount.Currency))
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status.ToString()));

        // Value Object to DTO mappings
        CreateMap<Money, MoneyDto>()
            .ForMember(dest => dest.Amount, opt => opt.MapFrom(src => src.Amount))
            .ForMember(dest => dest.Currency, opt => opt.MapFrom(src => src.Currency));

        // DTO to Value Object mappings
        CreateMap<CreateMoneyDto, Money>()
            .ConstructUsing(src => new Money(src.Amount, src.Currency));

        CreateMap<MoneyDto, Money>()
            .ConstructUsing(src => new Money(src.Amount, src.Currency));

        // Reverse mappings for updates
        CreateMap<CommissionDto, Commission>().ReverseMap();
        CreateMap<EscrowAccountDto, EscrowAccount>().ReverseMap();
        CreateMap<SettlementDto, Settlement>().ReverseMap();
        CreateMap<PaymentDisputeDto, PaymentDispute>().ReverseMap();
    }
}
