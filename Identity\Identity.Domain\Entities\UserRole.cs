using System;
using Identity.Domain.Common;

namespace Identity.Domain.Entities
{
    public class UserRole : Entity
    {
        public Guid UserId { get; private set; }
        public Guid RoleId { get; private set; }

        private UserRole() { }

        public UserRole(Guid userId, Guid roleId)
        {
            UserId = userId;
            RoleId = roleId;
            CreatedAt = DateTime.UtcNow;
        }
    }
}
