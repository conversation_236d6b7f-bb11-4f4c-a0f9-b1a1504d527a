using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using UserManagement.Application.Services;

namespace UserManagement.Infrastructure.Services
{
    public class FileStorageService : IFileStorageService
    {
        private readonly ILogger<FileStorageService> _logger;
        private readonly IConfiguration _configuration;
        private readonly string _exportPath;
        private readonly string _baseUrl;

        public FileStorageService(
            ILogger<FileStorageService> logger,
            IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
            
            _exportPath = _configuration.GetValue<string>("FileStorage:ExportPath") ?? "exports";
            _baseUrl = _configuration.GetValue<string>("FileStorage:BaseUrl") ?? "https://localhost:7001/files/exports";
            
            // Ensure export directory exists
            Directory.CreateDirectory(_exportPath);
        }

        public async Task<string> SaveExportFileAsync(string fileName, string content, string contentType)
        {
            var bytes = Encoding.UTF8.GetBytes(content);
            return await SaveExportFileAsync(fileName, bytes, contentType);
        }

        public async Task<string> SaveExportFileAsync(string fileName, byte[] content, string contentType)
        {
            try
            {
                var filePath = Path.Combine(_exportPath, fileName);
                
                await File.WriteAllBytesAsync(filePath, content);
                
                var fileUrl = $"{_baseUrl.TrimEnd('/')}/{fileName}";
                
                _logger.LogInformation("Export file saved successfully: {FileName} ({Size} bytes)", 
                    fileName, content.Length);
                
                return fileUrl;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving export file: {FileName}", fileName);
                throw;
            }
        }

        public async Task<string> GetFileUrlAsync(string fileName)
        {
            var filePath = Path.Combine(_exportPath, fileName);
            
            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException($"Export file not found: {fileName}");
            }
            
            return $"{_baseUrl.TrimEnd('/')}/{fileName}";
        }

        public async Task<bool> DeleteFileAsync(string fileName)
        {
            try
            {
                var filePath = Path.Combine(_exportPath, fileName);
                
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                    _logger.LogInformation("Export file deleted: {FileName}", fileName);
                    return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting export file: {FileName}", fileName);
                return false;
            }
        }

        public async Task<bool> FileExistsAsync(string fileName)
        {
            var filePath = Path.Combine(_exportPath, fileName);
            return File.Exists(filePath);
        }

        public async Task<long> GetFileSizeAsync(string fileName)
        {
            var filePath = Path.Combine(_exportPath, fileName);
            
            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException($"Export file not found: {fileName}");
            }
            
            var fileInfo = new FileInfo(filePath);
            return fileInfo.Length;
        }

        public async Task CleanupExpiredFilesAsync(DateTime cutoffDate)
        {
            try
            {
                var files = Directory.GetFiles(_exportPath);
                var deletedCount = 0;
                
                foreach (var file in files)
                {
                    var fileInfo = new FileInfo(file);
                    
                    if (fileInfo.CreationTime < cutoffDate)
                    {
                        try
                        {
                            File.Delete(file);
                            deletedCount++;
                            _logger.LogDebug("Deleted expired export file: {FileName}", fileInfo.Name);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "Failed to delete expired export file: {FileName}", fileInfo.Name);
                        }
                    }
                }
                
                _logger.LogInformation("Cleanup completed: {DeletedCount} expired export files deleted", deletedCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during export file cleanup");
                throw;
            }
        }
    }
}
