using FinancialPayment.Domain.ValueObjects;
using Shared.Domain.Common;

namespace FinancialPayment.Domain.Entities;

public class PaymentReconciliation : AggregateRoot
{
    public string ReconciliationNumber { get; private set; }
    public DateTime ReconciliationDate { get; private set; }
    public DateTime PeriodStart { get; private set; }
    public DateTime PeriodEnd { get; private set; }
    public string PaymentGateway { get; private set; }
    public ReconciliationStatus Status { get; private set; }
    public Money TotalPlatformAmount { get; private set; }
    public Money TotalGatewayAmount { get; private set; }
    public Money VarianceAmount { get; private set; }
    public int TotalPlatformTransactions { get; private set; }
    public int TotalGatewayTransactions { get; private set; }
    public int MatchedTransactions { get; private set; }
    public int UnmatchedPlatformTransactions { get; private set; }
    public int UnmatchedGatewayTransactions { get; private set; }
    public string? Notes { get; private set; }
    public DateTime? CompletedAt { get; private set; }
    public Guid? CompletedBy { get; private set; }

    // Navigation properties
    private readonly List<ReconciliationItem> _reconciliationItems = new();
    public IReadOnlyList<ReconciliationItem> ReconciliationItems => _reconciliationItems.AsReadOnly();

    private readonly List<ReconciliationDiscrepancy> _discrepancies = new();
    public IReadOnlyList<ReconciliationDiscrepancy> Discrepancies => _discrepancies.AsReadOnly();

    private PaymentReconciliation() { }

    public PaymentReconciliation(
        DateTime periodStart,
        DateTime periodEnd,
        string paymentGateway,
        string currency = "INR")
    {
        if (periodEnd <= periodStart)
            throw new ArgumentException("Period end must be after period start");

        if (string.IsNullOrWhiteSpace(paymentGateway))
            throw new ArgumentException("Payment gateway cannot be empty", nameof(paymentGateway));

        ReconciliationNumber = GenerateReconciliationNumber();
        ReconciliationDate = DateTime.UtcNow;
        PeriodStart = periodStart;
        PeriodEnd = periodEnd;
        PaymentGateway = paymentGateway;
        Status = ReconciliationStatus.Pending;
        TotalPlatformAmount = Money.Zero(currency);
        TotalGatewayAmount = Money.Zero(currency);
        VarianceAmount = Money.Zero(currency);
        TotalPlatformTransactions = 0;
        TotalGatewayTransactions = 0;
        MatchedTransactions = 0;
        UnmatchedPlatformTransactions = 0;
        UnmatchedGatewayTransactions = 0;
    }

    public void AddReconciliationItem(
        string platformTransactionId,
        string? gatewayTransactionId,
        Money platformAmount,
        Money? gatewayAmount,
        DateTime transactionDate,
        ReconciliationItemStatus status,
        string? notes = null)
    {
        var item = new ReconciliationItem(
            Id,
            platformTransactionId,
            gatewayTransactionId,
            platformAmount,
            gatewayAmount,
            transactionDate,
            status,
            notes);

        _reconciliationItems.Add(item);
        RecalculateTotals();
    }

    public void AddDiscrepancy(
        string description,
        DiscrepancyType type,
        Money amount,
        string? platformTransactionId = null,
        string? gatewayTransactionId = null,
        string? resolution = null)
    {
        var discrepancy = new ReconciliationDiscrepancy(
            Id,
            description,
            type,
            amount,
            platformTransactionId,
            gatewayTransactionId,
            resolution);

        _discrepancies.Add(discrepancy);
    }

    public void StartReconciliation()
    {
        if (Status != ReconciliationStatus.Pending)
            throw new InvalidOperationException("Only pending reconciliations can be started");

        Status = ReconciliationStatus.InProgress;
        SetUpdatedAt();
    }

    public void CompleteReconciliation(Guid completedBy, string? notes = null)
    {
        if (Status != ReconciliationStatus.InProgress)
            throw new InvalidOperationException("Only in-progress reconciliations can be completed");

        Status = ReconciliationStatus.Completed;
        CompletedAt = DateTime.UtcNow;
        CompletedBy = completedBy;
        Notes = notes;
        SetUpdatedAt();
    }

    public void FailReconciliation(string errorMessage)
    {
        Status = ReconciliationStatus.Failed;
        Notes = errorMessage;
        SetUpdatedAt();
    }

    public void RequireReview(string reason)
    {
        Status = ReconciliationStatus.RequiresReview;
        Notes = reason;
        SetUpdatedAt();
    }

    private void RecalculateTotals()
    {
        TotalPlatformTransactions = _reconciliationItems.Count(ri => !string.IsNullOrEmpty(ri.PlatformTransactionId));
        TotalGatewayTransactions = _reconciliationItems.Count(ri => !string.IsNullOrEmpty(ri.GatewayTransactionId));
        MatchedTransactions = _reconciliationItems.Count(ri => ri.Status == ReconciliationItemStatus.Matched);
        UnmatchedPlatformTransactions = _reconciliationItems.Count(ri => ri.Status == ReconciliationItemStatus.UnmatchedPlatform);
        UnmatchedGatewayTransactions = _reconciliationItems.Count(ri => ri.Status == ReconciliationItemStatus.UnmatchedGateway);

        var currency = TotalPlatformAmount.Currency;
        TotalPlatformAmount = _reconciliationItems
            .Where(ri => ri.PlatformAmount != null)
            .Aggregate(Money.Zero(currency), (sum, item) => sum + item.PlatformAmount!);

        TotalGatewayAmount = _reconciliationItems
            .Where(ri => ri.GatewayAmount != null)
            .Aggregate(Money.Zero(currency), (sum, item) => sum + item.GatewayAmount!);

        VarianceAmount = TotalPlatformAmount - TotalGatewayAmount;
        SetUpdatedAt();
    }

    public ReconciliationSummary GetSummary()
    {
        return new ReconciliationSummary
        {
            ReconciliationId = Id,
            ReconciliationNumber = ReconciliationNumber,
            PaymentGateway = PaymentGateway,
            PeriodStart = PeriodStart,
            PeriodEnd = PeriodEnd,
            Status = Status,
            TotalPlatformAmount = TotalPlatformAmount,
            TotalGatewayAmount = TotalGatewayAmount,
            VarianceAmount = VarianceAmount,
            TotalPlatformTransactions = TotalPlatformTransactions,
            TotalGatewayTransactions = TotalGatewayTransactions,
            MatchedTransactions = MatchedTransactions,
            UnmatchedPlatformTransactions = UnmatchedPlatformTransactions,
            UnmatchedGatewayTransactions = UnmatchedGatewayTransactions,
            DiscrepancyCount = _discrepancies.Count,
            ReconciliationDate = ReconciliationDate,
            CompletedAt = CompletedAt
        };
    }

    private static string GenerateReconciliationNumber()
    {
        return $"REC-{DateTime.UtcNow:yyyyMMdd}-{Guid.NewGuid():N}";
    }
}

public class ReconciliationItem : BaseEntity
{
    public Guid ReconciliationId { get; private set; }
    public string PlatformTransactionId { get; private set; }
    public string? GatewayTransactionId { get; private set; }
    public Money? PlatformAmount { get; private set; }
    public Money? GatewayAmount { get; private set; }
    public DateTime TransactionDate { get; private set; }
    public ReconciliationItemStatus Status { get; private set; }
    public string? Notes { get; private set; }

    private ReconciliationItem() { }

    public ReconciliationItem(
        Guid reconciliationId,
        string platformTransactionId,
        string? gatewayTransactionId,
        Money? platformAmount,
        Money? gatewayAmount,
        DateTime transactionDate,
        ReconciliationItemStatus status,
        string? notes = null)
    {
        ReconciliationId = reconciliationId;
        PlatformTransactionId = platformTransactionId;
        GatewayTransactionId = gatewayTransactionId;
        PlatformAmount = platformAmount;
        GatewayAmount = gatewayAmount;
        TransactionDate = transactionDate;
        Status = status;
        Notes = notes;
    }

    public void UpdateStatus(ReconciliationItemStatus newStatus, string? notes = null)
    {
        Status = newStatus;
        Notes = notes;
    }
}

public class ReconciliationDiscrepancy : BaseEntity
{
    public Guid ReconciliationId { get; private set; }
    public string Description { get; private set; }
    public DiscrepancyType Type { get; private set; }
    public Money Amount { get; private set; }
    public string? PlatformTransactionId { get; private set; }
    public string? GatewayTransactionId { get; private set; }
    public string? Resolution { get; private set; }
    public bool IsResolved { get; private set; }
    public DateTime? ResolvedAt { get; private set; }
    public Guid? ResolvedBy { get; private set; }

    private ReconciliationDiscrepancy() { }

    public ReconciliationDiscrepancy(
        Guid reconciliationId,
        string description,
        DiscrepancyType type,
        Money amount,
        string? platformTransactionId = null,
        string? gatewayTransactionId = null,
        string? resolution = null)
    {
        ReconciliationId = reconciliationId;
        Description = description;
        Type = type;
        Amount = amount;
        PlatformTransactionId = platformTransactionId;
        GatewayTransactionId = gatewayTransactionId;
        Resolution = resolution;
        IsResolved = false;
    }

    public void Resolve(string resolution, Guid resolvedBy)
    {
        Resolution = resolution;
        IsResolved = true;
        ResolvedAt = DateTime.UtcNow;
        ResolvedBy = resolvedBy;
    }
}

public enum ReconciliationStatus
{
    Pending = 1,
    InProgress = 2,
    Completed = 3,
    Failed = 4,
    RequiresReview = 5
}

public enum ReconciliationItemStatus
{
    Matched = 1,
    UnmatchedPlatform = 2,
    UnmatchedGateway = 3,
    AmountMismatch = 4,
    DateMismatch = 5
}

public enum DiscrepancyType
{
    AmountMismatch = 1,
    MissingTransaction = 2,
    DuplicateTransaction = 3,
    StatusMismatch = 4,
    DateMismatch = 5,
    Other = 6
}

public class ReconciliationSummary
{
    public Guid ReconciliationId { get; set; }
    public string ReconciliationNumber { get; set; } = string.Empty;
    public string PaymentGateway { get; set; } = string.Empty;
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
    public ReconciliationStatus Status { get; set; }
    public Money TotalPlatformAmount { get; set; } = Money.Zero("INR");
    public Money TotalGatewayAmount { get; set; } = Money.Zero("INR");
    public Money VarianceAmount { get; set; } = Money.Zero("INR");
    public int TotalPlatformTransactions { get; set; }
    public int TotalGatewayTransactions { get; set; }
    public int MatchedTransactions { get; set; }
    public int UnmatchedPlatformTransactions { get; set; }
    public int UnmatchedGatewayTransactions { get; set; }
    public int DiscrepancyCount { get; set; }
    public DateTime ReconciliationDate { get; set; }
    public DateTime? CompletedAt { get; set; }
}


