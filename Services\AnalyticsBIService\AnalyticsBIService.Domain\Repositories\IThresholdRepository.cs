using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Enums;
using Shared.Infrastructure.Repositories;

namespace AnalyticsBIService.Domain.Repositories;

/// <summary>
/// Repository interface for Threshold entity
/// </summary>
public interface IThresholdRepository : IRepositoryBase<Threshold, Guid>
{
    /// <summary>
    /// Get thresholds by metric type
    /// </summary>
    Task<IEnumerable<Threshold>> GetByMetricTypeAsync(string metricType, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get thresholds by entity type
    /// </summary>
    Task<IEnumerable<Threshold>> GetByEntityTypeAsync(string entityType, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get thresholds by user role
    /// </summary>
    Task<IEnumerable<Threshold>> GetByUserRoleAsync(UserRole userRole, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get enabled thresholds
    /// </summary>
    Task<IEnumerable<Threshold>> GetEnabledAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get thresholds for monitoring
    /// </summary>
    Task<IEnumerable<Threshold>> GetForMonitoringAsync(string metricType, string entityType, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get thresholds by notification recipient
    /// </summary>
    Task<IEnumerable<Threshold>> GetByNotificationRecipientAsync(Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get thresholds by user and metric name
    /// </summary>
    Task<List<Threshold>> GetByUserAndMetricAsync(Guid userId, string metricName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get thresholds by user
    /// </summary>
    Task<List<Threshold>> GetByUserAsync(Guid userId, CancellationToken cancellationToken = default);
}
