﻿using AnalyticsBIService.Domain.Enums;
using Shared.Domain.Common;
using Shared.Domain.ValueObjects;

namespace AnalyticsBIService.Domain.ValueObjects;

/// <summary>
/// Value object representing a KPI target with thresholds
/// </summary>
public class KPITarget : ValueObject
{
    public decimal TargetValue { get; private set; }
    public decimal? MinThreshold { get; private set; }
    public decimal? MaxThreshold { get; private set; }
    public decimal? WarningThreshold { get; private set; }
    public decimal? CriticalThreshold { get; private set; }
    public string Unit { get; private set; }
    public bool IsHigherBetter { get; private set; }

    private KPITarget()
    {
        Unit = string.Empty;
    }

    public KPITarget(
        decimal targetValue,
        string unit,
        bool isHigherBetter = true,
        decimal? minThreshold = null,
        decimal? maxThreshold = null,
        decimal? warningThreshold = null,
        decimal? criticalThreshold = null)
    {
        TargetValue = targetValue;
        Unit = unit ?? throw new ArgumentNullException(nameof(unit));
        IsHigherBetter = isHigherBetter;
        MinThreshold = minThreshold;
        MaxThreshold = maxThreshold;
        WarningThreshold = warningThreshold;
        CriticalThreshold = criticalThreshold;

        ValidateThresholds();
    }

    private void ValidateThresholds()
    {
        if (MinThreshold.HasValue && MaxThreshold.HasValue && MinThreshold >= MaxThreshold)
            throw new ArgumentException("Min threshold must be less than max threshold");

        if (IsHigherBetter)
        {
            if (WarningThreshold.HasValue && WarningThreshold > TargetValue)
                throw new ArgumentException("Warning threshold should be below target for higher-is-better metrics");

            if (CriticalThreshold.HasValue && CriticalThreshold > TargetValue)
                throw new ArgumentException("Critical threshold should be below target for higher-is-better metrics");
        }
        else
        {
            if (WarningThreshold.HasValue && WarningThreshold < TargetValue)
                throw new ArgumentException("Warning threshold should be above target for lower-is-better metrics");

            if (CriticalThreshold.HasValue && CriticalThreshold < TargetValue)
                throw new ArgumentException("Critical threshold should be above target for lower-is-better metrics");
        }
    }

    public static KPITarget Create(decimal targetValue, string unit, bool isHigherBetter = true)
    {
        return new KPITarget(targetValue, unit, isHigherBetter);
    }

    public static KPITarget CreatePercentage(decimal targetPercentage, bool isHigherBetter = true)
    {
        if (targetPercentage < 0 || targetPercentage > 100)
            throw new ArgumentException("Percentage must be between 0 and 100");

        return new KPITarget(targetPercentage, "%", isHigherBetter);
    }

    public static KPITarget CreateWithThresholds(
        decimal targetValue,
        string unit,
        decimal warningThreshold,
        decimal criticalThreshold,
        bool isHigherBetter = true)
    {
        return new KPITarget(targetValue, unit, isHigherBetter, null, null, warningThreshold, criticalThreshold);
    }

    public PerformanceStatus EvaluatePerformance(decimal actualValue)
    {
        if (CriticalThreshold.HasValue)
        {
            if (IsHigherBetter && actualValue <= CriticalThreshold)
                return PerformanceStatus.Poor;
            if (!IsHigherBetter && actualValue >= CriticalThreshold)
                return PerformanceStatus.Poor;
        }

        if (WarningThreshold.HasValue)
        {
            if (IsHigherBetter && actualValue <= WarningThreshold)
                return PerformanceStatus.BelowAverage;
            if (!IsHigherBetter && actualValue >= WarningThreshold)
                return PerformanceStatus.BelowAverage;
        }

        var targetAchievement = actualValue / TargetValue;

        return targetAchievement switch
        {
            >= 1.1m when IsHigherBetter => PerformanceStatus.Excellent,
            >= 0.95m when IsHigherBetter => PerformanceStatus.Good,
            >= 0.8m when IsHigherBetter => PerformanceStatus.Average,
            < 0.8m when IsHigherBetter => PerformanceStatus.BelowAverage,

            <= 0.9m when !IsHigherBetter => PerformanceStatus.Excellent,
            <= 1.05m when !IsHigherBetter => PerformanceStatus.Good,
            <= 1.2m when !IsHigherBetter => PerformanceStatus.Average,
            > 1.2m when !IsHigherBetter => PerformanceStatus.BelowAverage,

            _ => PerformanceStatus.Average
        };
    }

    public AlertSeverity GetAlertSeverity(decimal actualValue)
    {
        if (CriticalThreshold.HasValue)
        {
            if (IsHigherBetter && actualValue <= CriticalThreshold)
                return AlertSeverity.Critical;
            if (!IsHigherBetter && actualValue >= CriticalThreshold)
                return AlertSeverity.Critical;
        }

        if (WarningThreshold.HasValue)
        {
            if (IsHigherBetter && actualValue <= WarningThreshold)
                return AlertSeverity.Warning;
            if (!IsHigherBetter && actualValue >= WarningThreshold)
                return AlertSeverity.Warning;
        }

        return AlertSeverity.Info;
    }

    public decimal GetTargetAchievementPercentage(decimal actualValue)
    {
        if (TargetValue == 0) return 0;

        return Math.Round((actualValue / TargetValue) * 100, 2);
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return TargetValue;
        yield return MinThreshold ?? 0;
        yield return MaxThreshold ?? 0;
        yield return WarningThreshold ?? 0;
        yield return CriticalThreshold ?? 0;
        yield return Unit;
        yield return IsHigherBetter;
    }

    public override string ToString()
    {
        return $"Target: {TargetValue} {Unit} ({(IsHigherBetter ? "Higher is better" : "Lower is better")})";
    }
}


