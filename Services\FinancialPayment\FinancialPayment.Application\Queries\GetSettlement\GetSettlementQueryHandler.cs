using MediatR;
using Microsoft.Extensions.Logging;
using FinancialPayment.Application.DTOs;
using FinancialPayment.Application.Interfaces.Repositories;

namespace FinancialPayment.Application.Queries.GetSettlement;

public class GetSettlementQueryHandler : IRequestHandler<GetSettlementQuery, SettlementDto?>
{
    private readonly ISettlementRepository _settlementRepository;
    private readonly ILogger<GetSettlementQueryHandler> _logger;

    public GetSettlementQueryHandler(
        ISettlementRepository settlementRepository,
        ILogger<GetSettlementQueryHandler> logger)
    {
        _settlementRepository = settlementRepository;
        _logger = logger;
    }

    public async Task<SettlementDto?> Handle(GetSettlementQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Getting settlement {SettlementId}", request.SettlementId);

            var settlement = await _settlementRepository.GetByIdAsync(request.SettlementId);
            if (settlement == null)
            {
                _logger.LogWarning("Settlement {SettlementId} not found", request.SettlementId);
                return null;
            }

            return new SettlementDto
            {
                Id = settlement.Id,
                OrderId = settlement.OrderId,
                TripId = settlement.TripId,
                EscrowAccountId = settlement.EscrowAccountId,
                SettlementNumber = settlement.SettlementNumber,
                TotalAmount = settlement.TotalAmount.Amount,
                TotalCurrency = settlement.TotalAmount.Currency,
                Status = settlement.Status.ToString(),
                ProcessedAt = settlement.ProcessedAt,
                Notes = settlement.Notes,
                CreatedAt = settlement.CreatedAt,
                UpdatedAt = settlement.UpdatedAt,
                Distributions = settlement.Distributions.Select(d => new SettlementDistributionDto
                {
                    Id = d.Id,
                    SettlementId = d.SettlementId,
                    RecipientId = d.RecipientId,
                    RecipientRole = d.RecipientRole.ToString(),
                    Amount = d.Amount.Amount,
                    Currency = d.Amount.Currency,
                    Type = d.Type.ToString(),
                    Description = d.Description,
                    Status = d.Status.ToString(),
                    ProcessedAt = d.ProcessedAt,
                    PaymentGatewayTransactionId = d.PaymentGatewayTransactionId,
                    FailureReason = d.FailureReason,
                    CreatedAt = d.CreatedAt,
                    UpdatedAt = d.UpdatedAt
                }).ToList()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting settlement {SettlementId}", request.SettlementId);
            throw;
        }
    }
}
