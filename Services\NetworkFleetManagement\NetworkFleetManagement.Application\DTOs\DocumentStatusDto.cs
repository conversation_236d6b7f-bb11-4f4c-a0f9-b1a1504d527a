using NetworkFleetManagement.Domain.Enums;

namespace NetworkFleetManagement.Application.DTOs;

public class DocumentStatusSummaryDto
{
    public Guid CarrierId { get; set; }
    public string CarrierCompanyName { get; set; } = string.Empty;
    public int TotalDocuments { get; set; }
    public int ValidDocuments { get; set; }
    public int ExpiredDocuments { get; set; }
    public int ExpiringDocuments { get; set; } // Expiring within 30 days
    public decimal ComplianceRate { get; set; }
    public List<DocumentCategoryStatusDto> CategoryBreakdown { get; set; } = new();
    public List<ExpiringDocumentDto> UpcomingExpirations { get; set; } = new();
    public DateTime GeneratedAt { get; set; }
}

public class DocumentCategoryStatusDto
{
    public string Category { get; set; } = string.Empty; // Vehicle, Driver, Carrier
    public int TotalDocuments { get; set; }
    public int ValidDocuments { get; set; }
    public int ExpiredDocuments { get; set; }
    public int ExpiringDocuments { get; set; }
    public decimal ComplianceRate { get; set; }
}

public class ExpiringDocumentDto
{
    public Guid DocumentId { get; set; }
    public string DocumentType { get; set; } = string.Empty;
    public string EntityType { get; set; } = string.Empty; // Vehicle, Driver, Carrier
    public Guid EntityId { get; set; }
    public string EntityName { get; set; } = string.Empty; // Vehicle registration, driver name, etc.
    public DateTime ExpiryDate { get; set; }
    public int DaysUntilExpiry { get; set; }
    public string Status { get; set; } = string.Empty; // Valid, Expiring, Expired
    public string StatusColor { get; set; } = string.Empty; // Green, Orange, Red
    public string Priority { get; set; } = string.Empty; // High, Medium, Low
}

public class VehicleDocumentStatusDto
{
    public Guid VehicleId { get; set; }
    public string RegistrationNumber { get; set; } = string.Empty;
    public string VehicleDisplayName { get; set; } = string.Empty;
    public VehicleStatus VehicleStatus { get; set; }
    
    // Insurance
    public bool HasInsurance { get; set; }
    public DateTime? InsuranceExpiryDate { get; set; }
    public bool IsInsuranceValid { get; set; }
    public int InsuranceDaysUntilExpiry { get; set; }
    public string InsuranceStatus { get; set; } = string.Empty;
    public string InsuranceStatusColor { get; set; } = string.Empty;
    
    // Fitness Certificate
    public bool HasFitnessCertificate { get; set; }
    public DateTime? FitnessExpiryDate { get; set; }
    public bool IsFitnessValid { get; set; }
    public int FitnessDaysUntilExpiry { get; set; }
    public string FitnessStatus { get; set; } = string.Empty;
    public string FitnessStatusColor { get; set; } = string.Empty;
    
    // Permit
    public bool HasPermit { get; set; }
    public DateTime? PermitExpiryDate { get; set; }
    public bool IsPermitValid { get; set; }
    public int PermitDaysUntilExpiry { get; set; }
    public string PermitStatus { get; set; } = string.Empty;
    public string PermitStatusColor { get; set; } = string.Empty;
    
    // Overall Status
    public bool IsCompliant { get; set; }
    public int ExpiredDocumentsCount { get; set; }
    public int ExpiringDocumentsCount { get; set; }
    public string OverallStatus { get; set; } = string.Empty;
    public string OverallStatusColor { get; set; } = string.Empty;
}

public class DriverDocumentStatusDto
{
    public Guid DriverId { get; set; }
    public string DriverName { get; set; } = string.Empty;
    public DriverStatus DriverStatus { get; set; }
    
    // License
    public string LicenseNumber { get; set; } = string.Empty;
    public DateTime LicenseExpiryDate { get; set; }
    public bool IsLicenseValid { get; set; }
    public int LicenseDaysUntilExpiry { get; set; }
    public string LicenseStatus { get; set; } = string.Empty;
    public string LicenseStatusColor { get; set; } = string.Empty;
    
    // Additional Documents (from Documents collection)
    public int TotalDocuments { get; set; }
    public int ValidDocuments { get; set; }
    public int ExpiredDocuments { get; set; }
    public int ExpiringDocuments { get; set; }
    
    // Overall Status
    public bool IsCompliant { get; set; }
    public string OverallStatus { get; set; } = string.Empty;
    public string OverallStatusColor { get; set; } = string.Empty;
}

public class CarrierDocumentStatusDto
{
    public Guid CarrierId { get; set; }
    public string CompanyName { get; set; } = string.Empty;
    public CarrierStatus CarrierStatus { get; set; }
    
    // Document Summary
    public int TotalDocuments { get; set; }
    public int ValidDocuments { get; set; }
    public int ExpiredDocuments { get; set; }
    public int ExpiringDocuments { get; set; }
    
    // Vehicle Documents Summary
    public int TotalVehicles { get; set; }
    public int CompliantVehicles { get; set; }
    public int NonCompliantVehicles { get; set; }
    
    // Driver Documents Summary
    public int TotalDrivers { get; set; }
    public int CompliantDrivers { get; set; }
    public int NonCompliantDrivers { get; set; }
    
    // Overall Compliance
    public decimal OverallComplianceRate { get; set; }
    public bool IsCompliant { get; set; }
    public string OverallStatus { get; set; } = string.Empty;
    public string OverallStatusColor { get; set; } = string.Empty;
}
