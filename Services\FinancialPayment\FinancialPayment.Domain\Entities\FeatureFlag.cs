using Shared.Domain.Common;

namespace FinancialPayment.Domain.Entities;

public class FeatureFlag : AggregateRoot
{
    public string FlagKey { get; private set; }
    public string Name { get; private set; }
    public string Description { get; private set; }
    public FeatureFlagType Type { get; private set; }
    public bool IsEnabled { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? LastModified { get; private set; }
    public Guid? CreatedBy { get; private set; }
    public Guid? ModifiedBy { get; private set; }
    public string Environment { get; private set; } // dev, staging, production
    public Dictionary<string, object> DefaultValue { get; private set; }
    public Dictionary<string, object> Configuration { get; private set; }

    // Navigation properties
    private readonly List<FeatureFlagRule> _rules = new();
    public IReadOnlyList<FeatureFlagRule> Rules => _rules.AsReadOnly();

    private readonly List<FeatureFlagVariant> _variants = new();
    public IReadOnlyList<FeatureFlagVariant> Variants => _variants.AsReadOnly();

    private readonly List<FeatureFlagAuditLog> _auditLogs = new();
    public IReadOnlyList<FeatureFlagAuditLog> AuditLogs => _auditLogs.AsReadOnly();

    private FeatureFlag() { }

    public FeatureFlag(
        string flagKey,
        string name,
        string description,
        FeatureFlagType type,
        string environment = "production",
        Guid? createdBy = null)
    {
        if (string.IsNullOrWhiteSpace(flagKey))
            throw new ArgumentException("Flag key cannot be empty", nameof(flagKey));

        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Flag name cannot be empty", nameof(name));

        FlagKey = flagKey;
        Name = name;
        Description = description;
        Type = type;
        Environment = environment;
        IsEnabled = false;
        CreatedAt = DateTime.UtcNow;
        CreatedBy = createdBy;
        DefaultValue = new Dictionary<string, object>();
        Configuration = new Dictionary<string, object>();

        AddAuditLog(FeatureFlagAction.Created, "Feature flag created", createdBy);
    }

    public void Enable(Guid? modifiedBy = null)
    {
        if (IsEnabled) return;

        IsEnabled = true;
        LastModified = DateTime.UtcNow;
        ModifiedBy = modifiedBy;
        SetUpdatedAt();

        AddAuditLog(FeatureFlagAction.Enabled, "Feature flag enabled", modifiedBy);
    }

    public void Disable(Guid? modifiedBy = null)
    {
        if (!IsEnabled) return;

        IsEnabled = false;
        LastModified = DateTime.UtcNow;
        ModifiedBy = modifiedBy;
        SetUpdatedAt();

        AddAuditLog(FeatureFlagAction.Disabled, "Feature flag disabled", modifiedBy);
    }

    public void UpdateConfiguration(Dictionary<string, object> newConfiguration, Guid? modifiedBy = null)
    {
        Configuration = newConfiguration;
        LastModified = DateTime.UtcNow;
        ModifiedBy = modifiedBy;
        SetUpdatedAt();

        AddAuditLog(FeatureFlagAction.ConfigurationUpdated, "Configuration updated", modifiedBy);
    }

    public void AddRule(string ruleName, string condition, Dictionary<string, object> parameters, int priority = 0)
    {
        var rule = new FeatureFlagRule(Id, ruleName, condition, parameters, priority);
        _rules.Add(rule);
        SetUpdatedAt();
    }

    public void AddVariant(string variantKey, string name, int weight, Dictionary<string, object> value)
    {
        var variant = new FeatureFlagVariant(Id, variantKey, name, weight, value);
        _variants.Add(variant);
        SetUpdatedAt();
    }

    public bool EvaluateFlag(FeatureFlagContext context)
    {
        if (!IsEnabled) return false;

        // If no rules, return enabled state
        if (!_rules.Any()) return true;

        // Evaluate rules in priority order
        var applicableRules = _rules.Where(r => r.IsActive).OrderBy(r => r.Priority);

        foreach (var rule in applicableRules)
        {
            if (rule.Evaluate(context))
            {
                return true;
            }
        }

        return false;
    }

    public FeatureFlagVariant? GetVariant(FeatureFlagContext context)
    {
        if (!IsEnabled || Type != FeatureFlagType.Experiment) return null;

        var activeVariants = _variants.Where(v => v.IsActive).ToList();
        if (!activeVariants.Any()) return null;

        // Use user ID for consistent variant assignment
        var hash = context.UserId?.GetHashCode() ?? context.SessionId?.GetHashCode() ?? 0;
        var totalWeight = activeVariants.Sum(v => v.Weight);
        var targetWeight = Math.Abs(hash) % totalWeight;

        var currentWeight = 0;
        foreach (var variant in activeVariants)
        {
            currentWeight += variant.Weight;
            if (targetWeight < currentWeight)
            {
                return variant;
            }
        }

        return activeVariants.First(); // Fallback
    }

    private void AddAuditLog(FeatureFlagAction action, string description, Guid? performedBy)
    {
        var auditLog = new FeatureFlagAuditLog(Id, action, description, performedBy);
        _auditLogs.Add(auditLog);
    }
}

public class FeatureFlagRule : BaseEntity
{
    public Guid FeatureFlagId { get; private set; }
    public string RuleName { get; private set; }
    public string Condition { get; private set; } // JSON expression or simple condition
    public Dictionary<string, object> Parameters { get; private set; }
    public int Priority { get; private set; }
    public bool IsActive { get; private set; }
    public DateTime CreatedAt { get; private set; }

    private FeatureFlagRule() { }

    public FeatureFlagRule(
        Guid featureFlagId,
        string ruleName,
        string condition,
        Dictionary<string, object> parameters,
        int priority = 0)
    {
        FeatureFlagId = featureFlagId;
        RuleName = ruleName;
        Condition = condition;
        Parameters = parameters;
        Priority = priority;
        IsActive = true;
        CreatedAt = DateTime.UtcNow;
    }

    public bool Evaluate(FeatureFlagContext context)
    {
        if (!IsActive) return false;

        try
        {
            // Simple condition evaluation
            return Condition.ToLowerInvariant() switch
            {
                "user_id_in_list" => EvaluateUserIdInList(context),
                "percentage_rollout" => EvaluatePercentageRollout(context),
                "user_attribute_equals" => EvaluateUserAttributeEquals(context),
                "date_range" => EvaluateDateRange(context),
                "always_true" => true,
                "always_false" => false,
                _ => false
            };
        }
        catch
        {
            return false;
        }
    }

    private bool EvaluateUserIdInList(FeatureFlagContext context)
    {
        if (!context.UserId.HasValue || !Parameters.ContainsKey("user_ids"))
            return false;

        var userIds = Parameters["user_ids"] as List<string> ?? new List<string>();
        return userIds.Contains(context.UserId.Value.ToString());
    }

    private bool EvaluatePercentageRollout(FeatureFlagContext context)
    {
        if (!Parameters.ContainsKey("percentage"))
            return false;

        var percentage = Convert.ToDecimal(Parameters["percentage"]);
        var hash = context.UserId?.GetHashCode() ?? context.SessionId?.GetHashCode() ?? 0;
        var userPercentage = (Math.Abs(hash) % 100) + 1;

        return userPercentage <= percentage;
    }

    private bool EvaluateUserAttributeEquals(FeatureFlagContext context)
    {
        if (!Parameters.ContainsKey("attribute") || !Parameters.ContainsKey("value"))
            return false;

        var attribute = Parameters["attribute"].ToString();
        var expectedValue = Parameters["value"].ToString();

        return context.UserAttributes.TryGetValue(attribute!, out var actualValue) &&
               actualValue?.ToString() == expectedValue;
    }

    private bool EvaluateDateRange(FeatureFlagContext context)
    {
        if (!Parameters.ContainsKey("start_date") || !Parameters.ContainsKey("end_date"))
            return false;

        var startDate = DateTime.Parse(Parameters["start_date"].ToString()!);
        var endDate = DateTime.Parse(Parameters["end_date"].ToString()!);
        var now = DateTime.UtcNow;

        return now >= startDate && now <= endDate;
    }

    public void Activate() => IsActive = true;
    public void Deactivate() => IsActive = false;
}

public class FeatureFlagVariant : BaseEntity
{
    public Guid FeatureFlagId { get; private set; }
    public string VariantKey { get; private set; }
    public string Name { get; private set; }
    public int Weight { get; private set; } // For percentage distribution
    public Dictionary<string, object> Value { get; private set; }
    public bool IsActive { get; private set; }
    public DateTime CreatedAt { get; private set; }

    private FeatureFlagVariant() { }

    public FeatureFlagVariant(
        Guid featureFlagId,
        string variantKey,
        string name,
        int weight,
        Dictionary<string, object> value)
    {
        FeatureFlagId = featureFlagId;
        VariantKey = variantKey;
        Name = name;
        Weight = weight;
        Value = value;
        IsActive = true;
        CreatedAt = DateTime.UtcNow;
    }

    public void UpdateWeight(int newWeight)
    {
        Weight = newWeight;
    }

    public void Activate() => IsActive = true;
    public void Deactivate() => IsActive = false;
}

public class FeatureFlagAuditLog : BaseEntity
{
    public Guid FeatureFlagId { get; private set; }
    public FeatureFlagAction Action { get; private set; }
    public string Description { get; private set; }
    public Guid? PerformedBy { get; private set; }
    public DateTime PerformedAt { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    private FeatureFlagAuditLog() { }

    public FeatureFlagAuditLog(
        Guid featureFlagId,
        FeatureFlagAction action,
        string description,
        Guid? performedBy = null)
    {
        FeatureFlagId = featureFlagId;
        Action = action;
        Description = description;
        PerformedBy = performedBy;
        PerformedAt = DateTime.UtcNow;
        Metadata = new Dictionary<string, object>();
    }
}

// Enums
public enum FeatureFlagType
{
    Boolean = 1,
    String = 2,
    Number = 3,
    Json = 4,
    Experiment = 5
}

public enum FeatureFlagAction
{
    Created = 1,
    Enabled = 2,
    Disabled = 3,
    ConfigurationUpdated = 4,
    RuleAdded = 5,
    RuleUpdated = 6,
    RuleRemoved = 7,
    VariantAdded = 8,
    VariantUpdated = 9,
    VariantRemoved = 10,
    Deleted = 11
}

// Value Objects
public class FeatureFlagContext
{
    public Guid? UserId { get; set; }
    public string? SessionId { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
    public Dictionary<string, object> UserAttributes { get; set; } = new();
    public Dictionary<string, object> RequestAttributes { get; set; } = new();
    public DateTime RequestTime { get; set; } = DateTime.UtcNow;
}

public class FeatureFlagEvaluation
{
    public string FlagKey { get; set; } = string.Empty;
    public bool IsEnabled { get; set; }
    public Dictionary<string, object> Value { get; set; } = new();
    public string? VariantKey { get; set; }
    public string? RuleName { get; set; }
    public DateTime EvaluatedAt { get; set; }
    public string Reason { get; set; } = string.Empty;
}

