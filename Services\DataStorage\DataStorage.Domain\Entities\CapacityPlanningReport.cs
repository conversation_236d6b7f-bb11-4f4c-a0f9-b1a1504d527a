using System;
using System.Collections.Generic;
using Shared.Domain.Common;

namespace DataStorage.Domain.Entities
{
    public class CapacityPlanningReport : BaseEntity
    {
        public string Name { get; private set; }
        public string Description { get; private set; }
        public DateTime PeriodStart { get; private set; }
        public DateTime PeriodEnd { get; private set; }
        public DateTime ProjectionDate { get; private set; }
        public string ResourceType { get; private set; } // Storage, CPU, Memory, Network
        public double CurrentUsage { get; private set; }
        public double ProjectedUsage { get; private set; }
        public double CapacityLimit { get; private set; }
        public double UtilizationPercentage { get; private set; }
        public string RecommendedActions { get; private set; } // JSON array
        public string TrendAnalysis { get; private set; } // JSON data
        public Dictionary<string, double> Metrics { get; private set; }
        public Guid GeneratedBy { get; private set; }
        public ReportStatus Status { get; private set; }
        public string FilePath { get; private set; }

        private CapacityPlanningReport()
        {
            Metrics = new Dictionary<string, double>();
        }

        public CapacityPlanningReport(
            string name,
            string description,
            DateTime periodStart,
            DateTime periodEnd,
            DateTime projectionDate,
            string resourceType,
            Guid generatedBy)
        {
            Name = name ?? throw new ArgumentNullException(nameof(name));
            Description = description;
            PeriodStart = periodStart;
            PeriodEnd = periodEnd;
            ProjectionDate = projectionDate;
            ResourceType = resourceType ?? throw new ArgumentNullException(nameof(resourceType));
            GeneratedBy = generatedBy;
            Status = ReportStatus.Pending;
            Metrics = new Dictionary<string, double>();
            CreatedAt = DateTime.UtcNow;
        }

        public void UpdateCapacityData(
            double currentUsage,
            double projectedUsage,
            double capacityLimit)
        {
            CurrentUsage = currentUsage;
            ProjectedUsage = projectedUsage;
            CapacityLimit = capacityLimit;
            UtilizationPercentage = capacityLimit > 0 ? (projectedUsage / capacityLimit) * 100 : 0;
            UpdatedAt = DateTime.UtcNow;
        }

        public void SetRecommendations(string recommendedActions)
        {
            RecommendedActions = recommendedActions;
            UpdatedAt = DateTime.UtcNow;
        }

        public void SetTrendAnalysis(string trendAnalysis)
        {
            TrendAnalysis = trendAnalysis;
            UpdatedAt = DateTime.UtcNow;
        }

        public void CompleteReport(string filePath)
        {
            Status = ReportStatus.Completed;
            FilePath = filePath;
            UpdatedAt = DateTime.UtcNow;
        }

        public void FailReport(string errorMessage)
        {
            Status = ReportStatus.Failed;
            UpdatedAt = DateTime.UtcNow;
        }

        public void AddMetric(string key, double value)
        {
            Metrics[key] = value;
            UpdatedAt = DateTime.UtcNow;
        }

        public bool IsCapacityAtRisk(double thresholdPercentage = 80.0)
        {
            return UtilizationPercentage >= thresholdPercentage;
        }

        public bool RequiresImmediateAction(double criticalThreshold = 95.0)
        {
            return UtilizationPercentage >= criticalThreshold;
        }
    }
}
