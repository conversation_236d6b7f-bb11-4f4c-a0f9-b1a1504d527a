using CommunicationNotification.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Testing;
using Xunit.Abstractions;

namespace CommunicationNotification.Tests.Infrastructure;

/// <summary>
/// Base class for all tests with common setup and utilities
/// </summary>
public abstract class TestBase : IDisposable
{
    protected readonly ITestOutputHelper Output;
    protected readonly IServiceProvider ServiceProvider;
    protected readonly IConfiguration Configuration;
    protected readonly FakeLogger Logger;
    protected readonly CommunicationDbContext DbContext;

    private readonly ServiceCollection _services;
    private bool _disposed = false;

    protected TestBase(ITestOutputHelper output)
    {
        Output = output;
        
        // Setup configuration
        Configuration = new ConfigurationBuilder()
            .AddJsonFile("appsettings.test.json")
            .AddEnvironmentVariables()
            .Build();

        // Setup services
        _services = new ServiceCollection();
        ConfigureServices(_services);
        
        ServiceProvider = _services.BuildServiceProvider();
        
        // Setup logging
        Logger = new FakeLogger();
        
        // Setup database context
        DbContext = ServiceProvider.GetRequiredService<CommunicationDbContext>();
        
        // Initialize database
        InitializeDatabase().GetAwaiter().GetResult();
    }

    /// <summary>
    /// Configure services for testing
    /// </summary>
    protected virtual void ConfigureServices(IServiceCollection services)
    {
        // Add configuration
        services.AddSingleton(Configuration);
        
        // Add logging
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.AddDebug();
            builder.SetMinimumLevel(LogLevel.Debug);
        });

        // Add database context with SQLite for testing
        services.AddDbContext<CommunicationDbContext>(options =>
        {
            if (Configuration.GetValue<bool>("Database:UseInMemoryDatabase"))
            {
                options.UseInMemoryDatabase($"TestDb_{Guid.NewGuid()}");
            }
            else
            {
                options.UseSqlite($"Data Source=test_{Guid.NewGuid()}.db");
            }
            
            options.EnableSensitiveDataLogging();
            options.EnableDetailedErrors();
        });

        // Add application services
        services.AddCommunicationInfrastructure(Configuration);
        services.AddCommunicationApplication();
    }

    /// <summary>
    /// Initialize database for testing
    /// </summary>
    protected virtual async Task InitializeDatabase()
    {
        await DbContext.Database.EnsureCreatedAsync();
        
        if (Configuration.GetValue<bool>("Testing:EnableTestData"))
        {
            await SeedTestData();
        }
    }

    /// <summary>
    /// Seed test data
    /// </summary>
    protected virtual async Task SeedTestData()
    {
        // Override in derived classes to add specific test data
        await Task.CompletedTask;
    }

    /// <summary>
    /// Clean up database after test
    /// </summary>
    protected virtual async Task CleanupDatabase()
    {
        if (Configuration.GetValue<bool>("Database:UseInMemoryDatabase"))
        {
            // In-memory database is automatically cleaned up
            return;
        }

        try
        {
            await DbContext.Database.EnsureDeletedAsync();
        }
        catch (Exception ex)
        {
            Output.WriteLine($"Error cleaning up database: {ex.Message}");
        }
    }

    /// <summary>
    /// Get service from DI container
    /// </summary>
    protected T GetService<T>() where T : notnull
    {
        return ServiceProvider.GetRequiredService<T>();
    }

    /// <summary>
    /// Get optional service from DI container
    /// </summary>
    protected T? GetOptionalService<T>() where T : class
    {
        return ServiceProvider.GetService<T>();
    }

    /// <summary>
    /// Create a new scope for testing
    /// </summary>
    protected IServiceScope CreateScope()
    {
        return ServiceProvider.CreateScope();
    }

    /// <summary>
    /// Write output to test console
    /// </summary>
    protected void WriteOutput(string message)
    {
        Output.WriteLine($"[{DateTime.UtcNow:HH:mm:ss.fff}] {message}");
    }

    /// <summary>
    /// Write formatted output to test console
    /// </summary>
    protected void WriteOutput(string format, params object[] args)
    {
        WriteOutput(string.Format(format, args));
    }

    /// <summary>
    /// Dispose resources
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// Dispose resources
    /// </summary>
    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            CleanupDatabase().GetAwaiter().GetResult();
            DbContext?.Dispose();
            ServiceProvider?.Dispose();
            _disposed = true;
        }
    }
}

/// <summary>
/// Base class for integration tests with web application factory
/// </summary>
public abstract class IntegrationTestBase : TestBase
{
    protected readonly TestWebApplicationFactory WebApplicationFactory;
    protected readonly HttpClient HttpClient;

    protected IntegrationTestBase(ITestOutputHelper output) : base(output)
    {
        WebApplicationFactory = new TestWebApplicationFactory();
        HttpClient = WebApplicationFactory.CreateClient();
    }

    /// <summary>
    /// Configure additional services for integration testing
    /// </summary>
    protected override void ConfigureServices(IServiceCollection services)
    {
        base.ConfigureServices(services);
        
        // Add HTTP client for external service testing
        services.AddHttpClient();
        
        // Add mock external services
        services.AddMockExternalServices(Configuration);
    }

    /// <summary>
    /// Dispose integration test resources
    /// </summary>
    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            HttpClient?.Dispose();
            WebApplicationFactory?.Dispose();
        }
        
        base.Dispose(disposing);
    }
}

/// <summary>
/// Base class for unit tests with mocking support
/// </summary>
public abstract class UnitTestBase : TestBase
{
    protected UnitTestBase(ITestOutputHelper output) : base(output)
    {
    }

    /// <summary>
    /// Configure services for unit testing with mocks
    /// </summary>
    protected override void ConfigureServices(IServiceCollection services)
    {
        base.ConfigureServices(services);
        
        // Use in-memory database for unit tests
        services.Configure<DatabaseOptions>(options =>
        {
            options.UseInMemoryDatabase = true;
        });
    }
}

/// <summary>
/// Database options for testing
/// </summary>
public class DatabaseOptions
{
    public bool UseInMemoryDatabase { get; set; } = true;
    public bool UseSqliteDatabase { get; set; } = false;
    public bool EnableSensitiveDataLogging { get; set; } = true;
    public bool EnableDetailedErrors { get; set; } = true;
}

/// <summary>
/// Extension methods for test service configuration
/// </summary>
public static class TestServiceExtensions
{
    /// <summary>
    /// Add mock external services for testing
    /// </summary>
    public static IServiceCollection AddMockExternalServices(
        this IServiceCollection services, 
        IConfiguration configuration)
    {
        if (configuration.GetValue<bool>("Testing:EnableMockProviders"))
        {
            // Add mock providers instead of real ones
            services.AddMockNotificationProviders();
            services.AddMockTranslationProviders();
            services.AddMockExternalApiClients();
        }

        return services;
    }

    /// <summary>
    /// Add mock notification providers
    /// </summary>
    private static IServiceCollection AddMockNotificationProviders(this IServiceCollection services)
    {
        // Mock providers will be implemented in separate files
        return services;
    }

    /// <summary>
    /// Add mock translation providers
    /// </summary>
    private static IServiceCollection AddMockTranslationProviders(this IServiceCollection services)
    {
        // Mock translation providers will be implemented in separate files
        return services;
    }

    /// <summary>
    /// Add mock external API clients
    /// </summary>
    private static IServiceCollection AddMockExternalApiClients(this IServiceCollection services)
    {
        // Mock API clients will be implemented in separate files
        return services;
    }
}

/// <summary>
/// Test data factory for creating test entities
/// </summary>
public static class TestDataFactory
{
    private static readonly Random Random = new();

    /// <summary>
    /// Generate random GUID
    /// </summary>
    public static Guid RandomGuid() => Guid.NewGuid();

    /// <summary>
    /// Generate random string
    /// </summary>
    public static string RandomString(int length = 10)
    {
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        return new string(Enumerable.Repeat(chars, length)
            .Select(s => s[Random.Next(s.Length)]).ToArray());
    }

    /// <summary>
    /// Generate random email
    /// </summary>
    public static string RandomEmail() => $"test.{RandomString(8)}@example.com";

    /// <summary>
    /// Generate random phone number
    /// </summary>
    public static string RandomPhoneNumber() => $"+91{Random.Next(7000000000, 9999999999)}";

    /// <summary>
    /// Generate random date in the past
    /// </summary>
    public static DateTime RandomPastDate(int maxDaysAgo = 30)
    {
        return DateTime.UtcNow.AddDays(-Random.Next(1, maxDaysAgo));
    }

    /// <summary>
    /// Generate random date in the future
    /// </summary>
    public static DateTime RandomFutureDate(int maxDaysAhead = 30)
    {
        return DateTime.UtcNow.AddDays(Random.Next(1, maxDaysAhead));
    }
}

/// <summary>
/// Test constants
/// </summary>
public static class TestConstants
{
    public const string TestUserId = "11111111-1111-1111-1111-111111111111";
    public const string TestDriverId = "22222222-2222-2222-2222-222222222222";
    public const string TestCustomerId = "33333333-3333-3333-3333-333333333333";
    public const string TestAdminId = "44444444-4444-4444-4444-444444444444";
    
    public const string TestPhoneNumber = "+919876543210";
    public const string TestEmail = "<EMAIL>";
    public const string TestWhatsAppNumber = "+919876543210";
    
    public const string TestJwtSecret = "test-secret-key-for-jwt-token-generation-in-tests-only";
    public const string TestJwtIssuer = "TLI.CommunicationService.Test";
    public const string TestJwtAudience = "TLI.CommunicationService.Test";
    
    public const int DefaultTestTimeout = 30000; // 30 seconds
    public const int ShortTestTimeout = 5000;    // 5 seconds
    public const int LongTestTimeout = 60000;    // 60 seconds
}
