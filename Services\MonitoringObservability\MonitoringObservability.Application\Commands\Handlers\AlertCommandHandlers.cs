using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using MonitoringObservability.Application.DTOs;
using MonitoringObservability.Domain.Entities;
using MonitoringObservability.Domain.Enums;
using MonitoringObservability.Domain.Interfaces;
using MonitoringObservability.Domain.ValueObjects;
using MonitoringObservability.Domain.Exceptions;
using Shared.Domain.Common;

namespace MonitoringObservability.Application.Commands.Handlers;

public class CreateAlertCommandHandler : IRequestHandler<CreateAlertCommand, AlertDto>
{
    private readonly IAlertRepository _alertRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<CreateAlertCommandHandler> _logger;

    public CreateAlertCommandHandler(
        IAlertRepository alertRepository,
        IMapper mapper,
        ILogger<CreateAlertCommandHandler> logger)
    {
        _alertRepository = alertRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<AlertDto> Handle(CreateAlertCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Creating alert for service {ServiceName}, metric {MetricName}",
                request.ServiceName, request.MetricName);

            // Map threshold DTO to value object
            var threshold = new AlertThreshold(
                request.Threshold.MetricName,
                request.Threshold.WarningThreshold,
                request.Threshold.CriticalThreshold,
                request.Threshold.Operator,
                request.Threshold.EvaluationWindow);

            // Create alert entity
            var alert = new Alert(
                request.Title,
                request.Description,
                request.Severity,
                request.Source,
                request.ServiceName,
                request.MetricName,
                request.CurrentValue,
                threshold,
                request.Tags);

            // Save to repository
            await _alertRepository.AddAsync(alert, cancellationToken);

            _logger.LogInformation("Alert created successfully with ID {AlertId}", alert.Id);

            return _mapper.Map<AlertDto>(alert);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create alert for service {ServiceName}", request.ServiceName);
            throw;
        }
    }
}

public class AcknowledgeAlertCommandHandler : IRequestHandler<AcknowledgeAlertCommand, AlertDto>
{
    private readonly IAlertRepository _alertRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<AcknowledgeAlertCommandHandler> _logger;

    public AcknowledgeAlertCommandHandler(
        IAlertRepository alertRepository,
        IMapper mapper,
        ILogger<AcknowledgeAlertCommandHandler> logger)
    {
        _alertRepository = alertRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<AlertDto> Handle(AcknowledgeAlertCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Acknowledging alert {AlertId} by user {UserId}",
                request.AlertId, request.UserId);

            var alert = await _alertRepository.GetByIdAsync(request.AlertId, cancellationToken);
            if (alert == null)
                throw new AlertNotFoundException(request.AlertId);

            alert.Acknowledge(request.UserId, request.UserName);

            await _alertRepository.UpdateAsync(alert, cancellationToken);

            _logger.LogInformation("Alert {AlertId} acknowledged successfully", request.AlertId);

            return _mapper.Map<AlertDto>(alert);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to acknowledge alert {AlertId}", request.AlertId);
            throw;
        }
    }
}

public class AssignAlertCommandHandler : IRequestHandler<AssignAlertCommand, AlertDto>
{
    private readonly IAlertRepository _alertRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<AssignAlertCommandHandler> _logger;

    public AssignAlertCommandHandler(
        IAlertRepository alertRepository,
        IMapper mapper,
        ILogger<AssignAlertCommandHandler> logger)
    {
        _alertRepository = alertRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<AlertDto> Handle(AssignAlertCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Assigning alert {AlertId} to user {AssignedToUserId}",
                request.AlertId, request.AssignedToUserId);

            var alert = await _alertRepository.GetByIdAsync(request.AlertId, cancellationToken);
            if (alert == null)
                throw new AlertNotFoundException(request.AlertId);

            alert.Assign(request.AssignedToUserId, request.AssignedToUserName);

            await _alertRepository.UpdateAsync(alert, cancellationToken);

            _logger.LogInformation("Alert {AlertId} assigned successfully to {AssignedToUserName}",
                request.AlertId, request.AssignedToUserName);

            return _mapper.Map<AlertDto>(alert);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to assign alert {AlertId}", request.AlertId);
            throw;
        }
    }
}

public class ResolveAlertCommandHandler : IRequestHandler<ResolveAlertCommand, AlertDto>
{
    private readonly IAlertRepository _alertRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<ResolveAlertCommandHandler> _logger;

    public ResolveAlertCommandHandler(
        IAlertRepository alertRepository,
        IMapper mapper,
        ILogger<ResolveAlertCommandHandler> logger)
    {
        _alertRepository = alertRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<AlertDto> Handle(ResolveAlertCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Resolving alert {AlertId} by user {UserId}",
                request.AlertId, request.UserId);

            var alert = await _alertRepository.GetByIdAsync(request.AlertId, cancellationToken);
            if (alert == null)
                throw new AlertNotFoundException(request.AlertId);

            alert.Resolve(request.UserId, request.ResolutionNotes);

            await _alertRepository.UpdateAsync(alert, cancellationToken);

            _logger.LogInformation("Alert {AlertId} resolved successfully", request.AlertId);

            return _mapper.Map<AlertDto>(alert);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to resolve alert {AlertId}", request.AlertId);
            throw;
        }
    }
}

public class EscalateAlertCommandHandler : IRequestHandler<EscalateAlertCommand, AlertDto>
{
    private readonly IAlertRepository _alertRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<EscalateAlertCommandHandler> _logger;

    public EscalateAlertCommandHandler(
        IAlertRepository alertRepository,
        IMapper mapper,
        ILogger<EscalateAlertCommandHandler> logger)
    {
        _alertRepository = alertRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<AlertDto> Handle(EscalateAlertCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Escalating alert {AlertId} to level {EscalationLevel}",
                request.AlertId, request.EscalationLevel);

            var alert = await _alertRepository.GetByIdAsync(request.AlertId, cancellationToken);
            if (alert == null)
                throw new AlertNotFoundException(request.AlertId);

            alert.Escalate(request.EscalationLevel, request.Reason);

            await _alertRepository.UpdateAsync(alert, cancellationToken);

            _logger.LogInformation("Alert {AlertId} escalated successfully to level {EscalationLevel}",
                request.AlertId, request.EscalationLevel);

            return _mapper.Map<AlertDto>(alert);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to escalate alert {AlertId}", request.AlertId);
            throw;
        }
    }
}

public class AddAlertCommentCommandHandler : IRequestHandler<AddAlertCommentCommand, AlertCommentDto>
{
    private readonly IAlertRepository _alertRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<AddAlertCommentCommandHandler> _logger;

    public AddAlertCommentCommandHandler(
        IAlertRepository alertRepository,
        IMapper mapper,
        ILogger<AddAlertCommentCommandHandler> logger)
    {
        _alertRepository = alertRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<AlertCommentDto> Handle(AddAlertCommentCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Adding comment to alert {AlertId} by user {UserId}",
                request.AlertId, request.UserId);

            var alert = await _alertRepository.GetByIdAsync(request.AlertId, cancellationToken);
            if (alert == null)
                throw new AlertNotFoundException(request.AlertId);

            alert.AddComment(request.UserId, request.UserName, request.Comment);

            await _alertRepository.UpdateAsync(alert, cancellationToken);

            // Get the latest comment
            var latestComment = alert.Comments.OrderByDescending(c => c.CreatedAt).First();

            _logger.LogInformation("Comment added successfully to alert {AlertId}", request.AlertId);

            return _mapper.Map<AlertCommentDto>(latestComment);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to add comment to alert {AlertId}", request.AlertId);
            throw;
        }
    }
}

public class RecordMetricByNameCommandHandler : IRequestHandler<RecordMetricByNameCommand, Unit>
{
    private readonly IMetricRepository _metricRepository;
    private readonly ILogger<RecordMetricByNameCommandHandler> _logger;

    public RecordMetricByNameCommandHandler(
        IMetricRepository metricRepository,
        ILogger<RecordMetricByNameCommandHandler> logger)
    {
        _metricRepository = metricRepository;
        _logger = logger;
    }

    public async Task<Unit> Handle(RecordMetricByNameCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Recording metric {MetricName} for service {ServiceName} with value {Value}",
                request.MetricName, request.ServiceName, request.Value);

            // Get or create metric
            var metric = await _metricRepository.GetByNameAndServiceAsync(request.MetricName, request.ServiceName, cancellationToken);

            if (metric == null)
            {
                // Create new metric
                metric = new Metric(
                    request.MetricName,
                    request.ServiceName,
                    $"Auto-created metric for {request.MetricName}",
                    request.Type,
                    request.Unit,
                    MonitoringCategory.Performance, // Default category
                    tags: request.Tags);

                await _metricRepository.AddAsync(metric, cancellationToken);
            }

            // Record the value
            metric.RecordValue(request.Value, request.Timestamp, request.Tags);

            await _metricRepository.UpdateAsync(metric, cancellationToken);

            return Unit.Value;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to record metric {MetricName} for service {ServiceName}",
                request.MetricName, request.ServiceName);
            throw;
        }
    }
}

