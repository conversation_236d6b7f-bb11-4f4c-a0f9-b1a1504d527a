using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Application.Interfaces.Repositories;
using SharedKernel.Application.Interfaces;
using Microsoft.Extensions.Logging;

namespace FinancialPayment.Infrastructure.Services;

/// <summary>
/// Enhanced commission service that includes comprehensive tax calculations
/// </summary>
public class EnhancedCommissionService : ICommissionService
{
    private readonly ICommissionRepository _commissionRepository;
    private readonly ITaxConfigurationService _taxConfigurationService;
    private readonly IPaymentService _paymentService;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<EnhancedCommissionService> _logger;

    public EnhancedCommissionService(
        ICommissionRepository commissionRepository,
        ITaxConfigurationService taxConfigurationService,
        IPaymentService paymentService,
        IUnitOfWork unitOfWork,
        ILogger<EnhancedCommissionService> logger)
    {
        _commissionRepository = commissionRepository;
        _taxConfigurationService = taxConfigurationService;
        _paymentService = paymentService;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<Commission> CalculateCommissionAsync(
        Guid orderId,
        Guid brokerId,
        Guid transportCompanyId,
        Guid carrierId,
        Money orderAmount,
        CommissionStructure commissionStructure)
    {
        _logger.LogInformation("Calculating commission with tax for order {OrderId} and broker {BrokerId}", orderId, brokerId);

        try
        {
            // Check if commission already exists
            var existingCommission = await _commissionRepository.GetByOrderIdAsync(orderId);
            if (existingCommission != null)
            {
                throw new InvalidOperationException($"Commission already exists for order {orderId}");
            }

            // Create base commission
            var commission = new Commission(
                orderId,
                brokerId,
                transportCompanyId,
                carrierId,
                orderAmount,
                commissionStructure);

            // Calculate taxes on commission amount
            var decimal = await CalculateCommissionTaxesAsync(
                commission.CalculatedAmount,
                brokerId,
                transportCompanyId);

            // Create enhanced commission with tax details
            var enhancedCommission = new EnhancedCommission(
                commission,
                decimal);

            await _commissionRepository.AddAsync(enhancedCommission);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Successfully calculated commission {CommissionId} with taxes for order {OrderId}. " +
                                 "Base: {BaseAmount}, GST: {GstAmount}, TDS: {TdsAmount}, Net: {NetAmount}",
                enhancedCommission.Id, orderId,
                enhancedCommission.CalculatedAmount.Amount,
                enhancedCommission.TaxDetails.GstAmount.Amount,
                enhancedCommission.TaxDetails.TdsAmount.Amount,
                enhancedCommission.TaxDetails.NetPayableAmount.Amount);

            return enhancedCommission;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating commission with tax for order {OrderId}", orderId);
            throw;
        }
    }

    public async Task<Commission> CalculateCommissionWithTaxAsync(
        Guid orderId,
        Guid brokerId,
        Guid transportCompanyId,
        Guid carrierId,
        Money orderAmount,
        CommissionStructure commissionStructure,
        TaxJurisdiction jurisdiction,
        string? hsnCode = null,
        EntityType entityType = EntityType.Company)
    {
        _logger.LogInformation("Calculating commission with comprehensive tax for order {OrderId}", orderId);

        try
        {
            // Check if commission already exists
            var existingCommission = await _commissionRepository.GetByOrderIdAsync(orderId);
            if (existingCommission != null)
            {
                throw new InvalidOperationException($"Commission already exists for order {orderId}");
            }

            // Create base commission
            var commission = new Commission(
                orderId,
                brokerId,
                transportCompanyId,
                carrierId,
                orderAmount,
                commissionStructure);

            // Calculate comprehensive taxes
            var decimal = await _taxConfigurationService.CalculateComprehensiveTaxAsync(
                commission.CalculatedAmount,
                ServiceCategory.Brokerage, // Commission is typically brokerage service
                jurisdiction,
                entityType,
                TdsSection.Section194H, // Commission/brokerage TDS section
                hsnCode,
                true); // Assume PAN is available

            // Create enhanced commission with comprehensive tax details
            var enhancedCommission = new EnhancedCommission(
                commission,
                decimal);

            await _commissionRepository.AddAsync(enhancedCommission);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Successfully calculated commission {CommissionId} with comprehensive taxes for order {OrderId}",
                enhancedCommission.Id, orderId);

            return enhancedCommission;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating commission with comprehensive tax for order {OrderId}", orderId);
            throw;
        }
    }

    public async Task<bool> ApproveCommissionAsync(Guid commissionId, string approvedBy)
    {
        _logger.LogInformation("Approving commission {CommissionId}", commissionId);

        try
        {
            var commission = await _commissionRepository.GetByIdAsync(commissionId);
            if (commission == null)
            {
                throw new InvalidOperationException($"Commission {commissionId} not found");
            }

            commission.Approve(approvedBy);
            await _commissionRepository.UpdateAsync(commission);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Successfully approved commission {CommissionId}", commissionId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error approving commission {CommissionId}", commissionId);
            throw;
        }
    }

    public async Task<bool> PayCommissionAsync(Guid commissionId, string paymentMethodId)
    {
        _logger.LogInformation("Paying commission {CommissionId}", commissionId);

        try
        {
            var commission = await _commissionRepository.GetByIdAsync(commissionId);
            if (commission == null)
            {
                throw new InvalidOperationException($"Commission {commissionId} not found");
            }

            // Determine payment amount (net amount after taxes for enhanced commissions)
            var paymentAmount = commission is EnhancedCommission enhancedCommission
                ? enhancedCommission.TaxDetails.NetPayableAmount
                : commission.CalculatedAmount;

            // Process payment
            var paymentResult = await _paymentService.ProcessCommissionPaymentAsync(
                commissionId,
                paymentAmount,
                commission.BrokerId,
                paymentMethodId);

            if (!paymentResult.IsSuccess)
            {
                _logger.LogWarning("Payment failed for commission {CommissionId}: {Error}",
                    commissionId, paymentResult.ErrorMessage);
                return false;
            }

            commission.Pay(paymentResult.TransactionId!);
            await _commissionRepository.UpdateAsync(commission);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Successfully paid commission {CommissionId} with transaction {TransactionId}",
                commissionId, paymentResult.TransactionId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error paying commission {CommissionId}", commissionId);
            throw;
        }
    }

    public async Task<Commission?> GetCommissionByIdAsync(Guid commissionId)
    {
        return await _commissionRepository.GetByIdAsync(commissionId);
    }

    public async Task<Commission?> GetCommissionByOrderIdAsync(Guid orderId)
    {
        return await _commissionRepository.GetByOrderIdAsync(orderId);
    }

    public async Task<List<Commission>> GetCommissionsByBrokerIdAsync(Guid brokerId)
    {
        return await _commissionRepository.GetByBrokerIdAsync(brokerId);
    }

    public async Task<List<Commission>> GetCommissionsByCarrierIdAsync(Guid carrierId)
    {
        return await _commissionRepository.GetByCarrierIdAsync(carrierId);
    }

    public async Task<List<Commission>> GetCommissionsByStatusAsync(CommissionStatus status)
    {
        return await _commissionRepository.GetByStatusAsync(status);
    }

    public async Task<bool> DisputeCommissionAsync(Guid commissionId, string reason)
    {
        _logger.LogInformation("Creating dispute for commission {CommissionId}", commissionId);

        try
        {
            var commission = await _commissionRepository.GetByIdAsync(commissionId);
            if (commission == null)
            {
                throw new InvalidOperationException($"Commission {commissionId} not found");
            }

            commission.Dispute(reason);
            await _commissionRepository.UpdateAsync(commission);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Successfully created dispute for commission {CommissionId}", commissionId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating dispute for commission {CommissionId}", commissionId);
            throw;
        }
    }

    public async Task<bool> ResolveCommissionDisputeAsync(Guid commissionId, Money resolvedAmount, string resolution)
    {
        _logger.LogInformation("Resolving dispute for commission {CommissionId}", commissionId);

        try
        {
            var commission = await _commissionRepository.GetByIdAsync(commissionId);
            if (commission == null)
            {
                throw new InvalidOperationException($"Commission {commissionId} not found");
            }

            commission.ResolveDispute(resolvedAmount, resolution);
            await _commissionRepository.UpdateAsync(commission);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Successfully resolved dispute for commission {CommissionId}", commissionId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resolving dispute for commission {CommissionId}", commissionId);
            throw;
        }
    }

    private async Task<decimal> CalculateCommissionTaxesAsync(
        Money commissionAmount,
        Guid brokerId,
        Guid transportCompanyId)
    {
        try
        {
            // Default jurisdiction (can be enhanced to get from broker/transport company details)
            var jurisdiction = new TaxJurisdiction("India", "Maharashtra", "Mumbai");

            // Calculate taxes for commission (brokerage service)
            var taxResult = await _taxConfigurationService.CalculateComprehensiveTaxAsync(
                commissionAmount,
                ServiceCategory.Brokerage,
                jurisdiction,
                EntityType.Company, // Assume company entity
                TdsSection.Section194H, // Commission/brokerage TDS
                null, // HSN code for brokerage services
                true); // Assume PAN available

            return taxResult;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error calculating taxes for commission, using zero tax amounts");
            
            // Return zero tax calculation if tax calculation fails
            return new decimal
            {
                BaseAmount = commissionAmount,
                GstAmount = Money.Zero(commissionAmount.Currency),
                TdsAmount = Money.Zero(commissionAmount.Currency),
                TotalTaxAmount = Money.Zero(commissionAmount.Currency),
                NetAmount = commissionAmount,
                EffectiveGstRate = 0,
                EffectiveTdsRate = 0,
                AppliedRules = new List<string> { "Tax calculation failed, using zero rates" },
                Warnings = new List<string> { "Tax calculation service unavailable" },
                CalculatedAt = DateTime.UtcNow
            };
        }
    }
}
