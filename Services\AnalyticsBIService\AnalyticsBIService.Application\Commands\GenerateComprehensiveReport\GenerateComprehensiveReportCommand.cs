using MediatR;
using AnalyticsBIService.Application.DTOs;

namespace AnalyticsBIService.Application.Commands.GenerateComprehensiveReport;

public class GenerateComprehensiveReportCommand : IRequest<ComprehensiveReportDto>
{
    public string ReportType { get; set; } = string.Empty;
    public DateRangeDto DateRange { get; set; } = new();
    public List<string> DataSources { get; set; } = new();
    public List<string> Metrics { get; set; } = new();
    public Dictionary<string, object> Parameters { get; set; } = new();
    public bool IncludeVisualizations { get; set; } = true;
    public bool IncludeInsights { get; set; } = true;
    public bool IncludePredictiveAnalytics { get; set; } = false;
    public bool IncludeBenchmarking { get; set; } = false;
    public string OutputFormat { get; set; } = "PDF";
    public Guid RequestedBy { get; set; }
    public string? TemplateId { get; set; }
    public ReportPriority Priority { get; set; } = ReportPriority.Normal;
    public List<string> Recipients { get; set; } = new();
    public bool SaveAsTemplate { get; set; } = false;
    public string? TemplateName { get; set; }
}

public enum ReportPriority
{
    Low = 1,
    Normal = 2,
    High = 3,
    Critical = 4
}
