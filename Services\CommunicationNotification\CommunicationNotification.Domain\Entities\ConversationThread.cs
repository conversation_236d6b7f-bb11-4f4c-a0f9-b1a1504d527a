using CommunicationNotification.Domain.Enums;
using Shared.Domain.Common;

namespace CommunicationNotification.Domain.Entities;

public class ConversationThread : BaseEntity
{
    public string? Title { get; private set; }
    public string? Description { get; private set; }
    public ConversationType Type { get; private set; }
    public Guid? RelatedEntityId { get; private set; }
    public string? RelatedEntityType { get; private set; }
    public bool IsActive { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public Guid CreatedBy { get; private set; }
    public DateTime LastActivity { get; private set; }
    public DateTime? ClosedAt { get; private set; }
    public Guid? ClosedByUserId { get; private set; }
    public string? CloseReason { get; private set; }
    public Dictionary<string, string> Metadata { get; private set; }

    // Navigation properties
    public ICollection<Message> Messages { get; private set; }
    public ICollection<ConversationParticipant> Participants { get; private set; }

    private ConversationThread()
    {
        Messages = new List<Message>();
        Participants = new List<ConversationParticipant>();
        Metadata = new Dictionary<string, string>();
    }

    public ConversationThread(
        string? title,
        ConversationType type,
        Guid createdBy,
        string? description = null,
        Guid? relatedEntityId = null,
        string? relatedEntityType = null,
        Dictionary<string, string>? metadata = null)
    {
        Title = title;
        Description = description;
        Type = type;
        CreatedBy = createdBy;
        RelatedEntityId = relatedEntityId;
        RelatedEntityType = relatedEntityType;
        IsActive = true;
        CreatedAt = DateTime.UtcNow;
        LastActivity = DateTime.UtcNow;
        Messages = new List<Message>();
        Participants = new List<ConversationParticipant>();
        Metadata = metadata ?? new Dictionary<string, string>();
    }

    public static ConversationThread Create(
        string? title,
        ConversationType type,
        Guid createdBy,
        Guid? relatedEntityId = null,
        string? relatedEntityType = null,
        Dictionary<string, string>? metadata = null)
    {
        return new ConversationThread(title, type, createdBy, null, relatedEntityId, relatedEntityType, metadata);
    }

    public void AddParticipant(Guid userId, ConversationRole role, bool canWrite = true)
    {
        if (userId == Guid.Empty)
            throw new ArgumentException("User ID cannot be empty", nameof(userId));

        if (Participants.Any(p => p.UserId == userId))
            return; // Don't throw, just ignore if already exists

        var participant = new ConversationParticipant(Id, userId, role, canWrite);
        Participants.Add(participant);
        UpdateLastActivity();
    }

    public void RemoveParticipant(Guid userId)
    {
        var participant = Participants.FirstOrDefault(p => p.UserId == userId);
        if (participant == null)
            return; // Don't throw, just ignore if not exists

        Participants.Remove(participant);
        UpdateLastActivity();
    }

    public void UpdateParticipantPermissions(Guid userId, bool canWrite)
    {
        var participant = Participants.FirstOrDefault(p => p.UserId == userId);
        if (participant == null)
            throw new InvalidOperationException("User is not a participant in this conversation");

        participant.UpdatePermissions(canWrite);
    }

    public void Close(Guid closedByUserId, string? reason = null)
    {
        if (!IsActive)
            throw new InvalidOperationException("Conversation is already closed");

        if (closedByUserId == Guid.Empty)
            throw new ArgumentException("Closed by user ID cannot be empty", nameof(closedByUserId));

        if (!Participants.Any(p => p.UserId == closedByUserId))
            throw new InvalidOperationException("Only participants can close the conversation");

        IsActive = false;
        ClosedAt = DateTime.UtcNow;
        ClosedByUserId = closedByUserId;
        CloseReason = reason;
    }

    public void Reopen()
    {
        if (IsActive)
            throw new InvalidOperationException("Conversation is already active");

        IsActive = true;
        ClosedAt = null;
        ClosedByUserId = null;
        CloseReason = null;
    }

    public void UpdateTitle(string title)
    {
        if (string.IsNullOrWhiteSpace(title))
            throw new ArgumentException("Title cannot be empty", nameof(title));

        Title = title;
    }

    public void UpdateDescription(string? description)
    {
        Description = description;
    }

    public void AddMetadata(string key, string value)
    {
        if (string.IsNullOrWhiteSpace(key))
            throw new ArgumentException("Metadata key cannot be empty", nameof(key));

        Metadata[key] = value ?? string.Empty;
    }

    public bool CanUserWrite(Guid userId)
    {
        if (!IsActive) return false;

        var participant = Participants.FirstOrDefault(p => p.UserId == userId);
        return participant?.CanWrite == true;
    }

    public bool IsUserParticipant(Guid userId)
    {
        return Participants.Any(p => p.UserId == userId);
    }

    public bool IsParticipant(Guid userId)
    {
        return Participants.Any(p => p.UserId == userId && p.IsActive);
    }

    public void UpdateLastActivity()
    {
        LastActivity = DateTime.UtcNow;
    }

    public int GetMessageCount()
    {
        return Messages.Count;
    }

    public DateTime? GetLastMessageTime()
    {
        return Messages.OrderByDescending(m => m.SentAt).FirstOrDefault()?.SentAt;
    }

    public IEnumerable<Guid> GetParticipantIds()
    {
        return Participants.Select(p => p.UserId);
    }
}
