using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Application.DTOs;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class TaxCalculationController : ControllerBase
{
    private readonly ITaxConfigurationService _taxConfigurationService;
    private readonly ILogger<TaxCalculationController> _logger;

    public TaxCalculationController(
        ITaxConfigurationService taxConfigurationService,
        ILogger<TaxCalculationController> logger)
    {
        _taxConfigurationService = taxConfigurationService;
        _logger = logger;
    }

    /// <summary>
    /// Calculate comprehensive tax (GST + TDS) for given parameters
    /// </summary>
    [HttpPost("comprehensive")]
    [Authorize(Roles = "Admin,Finance,Broker,TransportCompany")]
    public async Task<ActionResult<TaxCalculationResultDto>> CalculateComprehensiveTax([FromBody] ComprehensiveTaxCalculationRequestDto request)
    {
        try
        {
            var baseAmount = new Money(request.BaseAmount.Amount, request.BaseAmount.Currency);
            var jurisdiction = new TaxJurisdiction(
                request.Jurisdiction.Country,
                request.Jurisdiction.State,
                request.Jurisdiction.City,
                request.Jurisdiction.Type);

            var result = await _taxConfigurationService.CalculateComprehensiveTaxAsync(
                baseAmount,
                request.ServiceCategory,
                jurisdiction,
                request.EntityType,
                request.TdsSection,
                request.HsnCode,
                request.HasPan,
                request.CalculationDate);

            var dto = new TaxCalculationResultDto
            {
                BaseAmount = new MoneyDto
                {
                    Amount = result.BaseAmount.Amount,
                    Currency = result.BaseAmount.Currency
                },
                GstAmount = new MoneyDto
                {
                    Amount = result.GstAmount.Amount,
                    Currency = result.GstAmount.Currency
                },
                TdsAmount = new MoneyDto
                {
                    Amount = result.TdsAmount.Amount,
                    Currency = result.TdsAmount.Currency
                },
                TotalTaxAmount = new MoneyDto
                {
                    Amount = result.TotalTaxAmount.Amount,
                    Currency = result.TotalTaxAmount.Currency
                },
                NetAmount = new MoneyDto
                {
                    Amount = result.NetAmount.Amount,
                    Currency = result.NetAmount.Currency
                },
                EffectiveGstRate = result.EffectiveGstRate,
                EffectiveTdsRate = result.EffectiveTdsRate,
                AppliedHsnCode = result.AppliedHsnCode,
                IsReverseChargeApplicable = result.IsReverseChargeApplicable,
                AppliedRules = result.AppliedRules,
                Warnings = result.Warnings,
                CalculatedAt = result.CalculatedAt
            };

            return Ok(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating comprehensive tax");
            return StatusCode(500, "An error occurred while calculating tax");
        }
    }

    /// <summary>
    /// Calculate GST only
    /// </summary>
    [HttpPost("gst")]
    [Authorize(Roles = "Admin,Finance,Broker,TransportCompany")]
    public async Task<ActionResult<MoneyDto>> CalculateGst([FromBody] CalculateGstOnlyRequest request)
    {
        try
        {
            var baseAmount = new Money(request.BaseAmount.Amount, request.BaseAmount.Currency);
            var jurisdiction = new TaxJurisdiction(
                request.Jurisdiction.Country,
                request.Jurisdiction.State,
                request.Jurisdiction.City,
                request.Jurisdiction.Type);

            var gstAmount = await _taxConfigurationService.CalculateGstAsync(
                baseAmount, request.ServiceCategory, jurisdiction);

            var dto = new MoneyDto
            {
                Amount = gstAmount.Amount,
                Currency = gstAmount.Currency
            };

            return Ok(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating GST");
            return StatusCode(500, "An error occurred while calculating GST");
        }
    }

    /// <summary>
    /// Calculate TDS only
    /// </summary>
    [HttpPost("tds")]
    [Authorize(Roles = "Admin,Finance,Broker,TransportCompany")]
    public async Task<ActionResult<MoneyDto>> CalculateTds([FromBody] CalculateTdsOnlyRequest request)
    {
        try
        {
            var baseAmount = new Money(request.BaseAmount.Amount, request.BaseAmount.Currency);

            var tdsAmount = await _taxConfigurationService.CalculateTdsAsync(
                baseAmount, request.TdsSection, request.EntityType, request.HasPan);

            var dto = new MoneyDto
            {
                Amount = tdsAmount.Amount,
                Currency = tdsAmount.Currency
            };

            return Ok(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating TDS");
            return StatusCode(500, "An error occurred while calculating TDS");
        }
    }

    /// <summary>
    /// Check if HSN code is required for given service category and jurisdiction
    /// </summary>
    [HttpGet("hsn-required")]
    [Authorize(Roles = "Admin,Finance,Broker,TransportCompany")]
    public async Task<ActionResult<bool>> IsHsnCodeRequired(
        [FromQuery] ServiceCategory serviceCategory,
        [FromQuery] string country,
        [FromQuery] string? state = null,
        [FromQuery] string? city = null)
    {
        try
        {
            var jurisdiction = new TaxJurisdiction(country, state ?? "", city ?? "");
            var isRequired = await _taxConfigurationService.IsHsnCodeRequiredAsync(serviceCategory, jurisdiction);
            return Ok(isRequired);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking HSN code requirement");
            return StatusCode(500, "An error occurred while checking HSN code requirement");
        }
    }

    /// <summary>
    /// Get tax compliance report for jurisdiction
    /// </summary>
    [HttpGet("compliance-report")]
    [Authorize(Roles = "Admin,Finance")]
    public async Task<ActionResult<TaxComplianceReportDto>> GetTaxComplianceReport(
        [FromQuery] string country,
        [FromQuery] string? state = null,
        [FromQuery] string? city = null)
    {
        try
        {
            var jurisdiction = new TaxJurisdiction(country, state ?? "", city ?? "");
            var issues = await _taxConfigurationService.GetTaxComplianceIssuesAsync(jurisdiction);

            var report = new TaxComplianceReportDto
            {
                Jurisdiction = new TaxJurisdictionDto
                {
                    Country = jurisdiction.Country,
                    State = jurisdiction.State,
                    City = jurisdiction.City,
                    Type = jurisdiction.Type
                },
                ComplianceIssues = issues,
                IsCompliant = !issues.Any(),
                GeneratedAt = DateTime.UtcNow,
                GeneratedBy = User.Identity?.Name ?? "System"
            };

            // Add recommendations based on issues
            if (issues.Any(i => i.Contains("default tax configuration")))
            {
                report.Recommendations.Add("Create a default tax configuration for the jurisdiction");
            }
            if (issues.Any(i => i.Contains("Multiple active tax configurations")))
            {
                report.Recommendations.Add("Review and consolidate overlapping tax configurations");
            }
            if (issues.Any(i => i.Contains("GST configurations")))
            {
                report.Recommendations.Add("Set up GST configurations for all required service categories");
            }
            if (issues.Any(i => i.Contains("TDS configurations")))
            {
                report.Recommendations.Add("Configure TDS settings for applicable entity types and sections");
            }

            return Ok(report);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating tax compliance report");
            return StatusCode(500, "An error occurred while generating compliance report");
        }
    }

    /// <summary>
    /// Validate tax configuration
    /// </summary>
    [HttpPost("validate-configuration/{id}")]
    [Authorize(Roles = "Admin,Finance")]
    public async Task<ActionResult<bool>> ValidateTaxConfiguration(Guid id)
    {
        try
        {
            var isValid = await _taxConfigurationService.ValidateTaxConfigurationAsync(id);
            return Ok(isValid);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating tax configuration {Id}", id);
            return StatusCode(500, "An error occurred while validating tax configuration");
        }
    }

    /// <summary>
    /// Get tax configuration summary statistics
    /// </summary>
    [HttpGet("summary")]
    [Authorize(Roles = "Admin,Finance")]
    public async Task<ActionResult<TaxConfigurationSummaryDto>> GetTaxConfigurationSummary()
    {
        try
        {
            var activeTaxConfigs = await _taxConfigurationService.GetActiveTaxConfigurationsAsync();
            var activeGstConfigs = await _taxConfigurationService.GetActiveGstConfigurationsAsync();
            var activeTdsConfigs = await _taxConfigurationService.GetActiveTdsConfigurationsAsync();
            var activeHsnCodes = await _taxConfigurationService.GetActiveHsnCodesAsync();

            var summary = new TaxConfigurationSummaryDto
            {
                TotalConfigurations = activeTaxConfigs.Count,
                ActiveConfigurations = activeTaxConfigs.Count(c => c.Status == TaxConfigurationStatus.Active),
                InactiveConfigurations = activeTaxConfigs.Count(c => c.Status == TaxConfigurationStatus.Inactive),
                ArchivedConfigurations = activeTaxConfigs.Count(c => c.Status == TaxConfigurationStatus.Archived),
                GstConfigurations = activeGstConfigs.Count,
                TdsConfigurations = activeTdsConfigs.Count,
                HsnCodes = activeHsnCodes.Count,
                LastUpdated = DateTime.UtcNow
            };

            return Ok(summary);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving tax configuration summary");
            return StatusCode(500, "An error occurred while retrieving summary");
        }
    }
}

public class CalculateGstOnlyRequest
{
    public CreateMoneyDto BaseAmount { get; set; } = new();
    public ServiceCategory ServiceCategory { get; set; }
    public TaxJurisdictionDto Jurisdiction { get; set; } = new();
}

public class CalculateTdsOnlyRequest
{
    public CreateMoneyDto BaseAmount { get; set; } = new();
    public TdsSection TdsSection { get; set; }
    public EntityType EntityType { get; set; }
    public bool HasPan { get; set; } = true;
}
