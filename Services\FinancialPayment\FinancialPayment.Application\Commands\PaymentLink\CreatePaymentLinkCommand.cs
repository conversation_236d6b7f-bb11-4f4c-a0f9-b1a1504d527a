using FinancialPayment.Application.Common;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;
using MediatR;

namespace FinancialPayment.Application.Commands.PaymentLink;

/// <summary>
/// Command to create a payment link for Transport Company Portal
/// </summary>
public class CreatePaymentLinkCommand : IRequest<CreatePaymentLinkResult>
{
    public Guid TransportCompanyId { get; set; }
    public Guid ShipperId { get; set; }
    public Guid? OrderId { get; set; }
    public Guid? InvoiceId { get; set; }
    public decimal Amount { get; set; }
    public string Currency { get; set; } = "INR";
    public string Description { get; set; } = string.Empty;
    public PaymentLinkType LinkType { get; set; } = PaymentLinkType.Invoice;
    
    // Shipper contact details
    public string ShipperName { get; set; } = string.Empty;
    public string ShipperEmail { get; set; } = string.Empty;
    public string? ShipperPhone { get; set; }
    public string? ShipperCompanyName { get; set; }
    
    // Transport company contact details
    public string TransportCompanyName { get; set; } = string.Empty;
    public string TransportCompanyEmail { get; set; } = string.Empty;
    public string? TransportCompanyPhone { get; set; }
    
    // Address details (optional)
    public string? ShipperStreet { get; set; }
    public string? ShipperCity { get; set; }
    public string? ShipperState { get; set; }
    public string? ShipperPostalCode { get; set; }
    public string? ShipperCountry { get; set; } = "India";
    
    // Payment link settings
    public int? ExpiryDays { get; set; }
    public DateTime? CustomExpiryDate { get; set; }
    public bool RequiresAuthentication { get; set; } = false;
    public bool SendEmailNotification { get; set; } = true;
    public bool SendSmsNotification { get; set; } = false;
    public bool EnablePaymentReminders { get; set; } = true;
    public int ReminderIntervalHours { get; set; } = 24;
    public int MaxReminders { get; set; } = 3;
    public List<string> AllowedIpAddresses { get; set; } = new();
    public List<string> NotificationEmails { get; set; } = new();
    public List<string> NotificationPhones { get; set; } = new();
    
    // Additional settings
    public Dictionary<string, object> CustomSettings { get; set; } = new();
    public List<string> Tags { get; set; } = new();
    public string? Notes { get; set; }
    public Guid CreatedByUserId { get; set; }
}

/// <summary>
/// Result of creating a payment link
/// </summary>
public class CreatePaymentLinkResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public Guid? PaymentLinkId { get; set; }
    public string? LinkToken { get; set; }
    public string? PublicUrl { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public DateTime CreatedAt { get; set; }
    public PaymentLinkSummary? Summary { get; set; }
    public List<string> ValidationErrors { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Command to share a payment link with shipper
/// </summary>
public class SharePaymentLinkCommand : IRequest<SharePaymentLinkResult>
{
    public Guid PaymentLinkId { get; set; }
    public List<string> ShareVia { get; set; } = new(); // Email, SMS, WhatsApp
    public List<string> Recipients { get; set; } = new();
    public string? CustomMessage { get; set; }
    public bool IncludePaymentInstructions { get; set; } = true;
    public bool IncludeCompanyDetails { get; set; } = true;
    public Guid SharedByUserId { get; set; }
    public Dictionary<string, object> ShareSettings { get; set; } = new();
}

/// <summary>
/// Result of sharing a payment link
/// </summary>
public class SharePaymentLinkResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public int TotalRecipients { get; set; }
    public int SuccessfulShares { get; set; }
    public int FailedShares { get; set; }
    public List<ShareAttemptResult> ShareAttempts { get; set; } = new();
    public DateTime SharedAt { get; set; }
    public Dictionary<string, object> ShareMetadata { get; set; } = new();
}

/// <summary>
/// Result of individual share attempt
/// </summary>
public class ShareAttemptResult
{
    public string Recipient { get; set; } = string.Empty;
    public string ShareMethod { get; set; } = string.Empty;
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public string? ExternalId { get; set; }
    public DateTime AttemptedAt { get; set; }
}

/// <summary>
/// Command to cancel a payment link
/// </summary>
public class CancelPaymentLinkCommand : IRequest<CancelPaymentLinkResult>
{
    public Guid PaymentLinkId { get; set; }
    public string Reason { get; set; } = string.Empty;
    public bool NotifyShipper { get; set; } = true;
    public string? CustomNotificationMessage { get; set; }
    public Guid CancelledByUserId { get; set; }
}

/// <summary>
/// Result of cancelling a payment link
/// </summary>
public class CancelPaymentLinkResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime CancelledAt { get; set; }
    public bool NotificationSent { get; set; }
    public string? NotificationError { get; set; }
}

/// <summary>
/// Command to extend payment link expiry
/// </summary>
public class ExtendPaymentLinkCommand : IRequest<ExtendPaymentLinkResult>
{
    public Guid PaymentLinkId { get; set; }
    public DateTime NewExpiryDate { get; set; }
    public string Reason { get; set; } = string.Empty;
    public bool NotifyShipper { get; set; } = true;
    public string? CustomNotificationMessage { get; set; }
    public Guid ExtendedByUserId { get; set; }
}

/// <summary>
/// Result of extending a payment link
/// </summary>
public class ExtendPaymentLinkResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime? OldExpiryDate { get; set; }
    public DateTime? NewExpiryDate { get; set; }
    public DateTime ExtendedAt { get; set; }
    public bool NotificationSent { get; set; }
    public string? NotificationError { get; set; }
}

/// <summary>
/// Command to process payment through a payment link
/// </summary>
public class ProcessPaymentLinkPaymentCommand : IRequest<ProcessPaymentLinkPaymentResult>
{
    public string LinkToken { get; set; } = string.Empty;
    public string PaymentMethodId { get; set; } = string.Empty;
    public string? GatewayName { get; set; } = "razorpay";
    public string? CustomerIpAddress { get; set; }
    public string? UserAgent { get; set; }
    public Dictionary<string, object> PaymentMetadata { get; set; } = new();
}

/// <summary>
/// Result of processing payment through a payment link
/// </summary>
public class ProcessPaymentLinkPaymentResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public string? PaymentTransactionId { get; set; }
    public string? GatewayTransactionId { get; set; }
    public DateTime? ProcessedAt { get; set; }
    public PaymentStatus PaymentStatus { get; set; }
    public string? GatewayResponse { get; set; }
    public Dictionary<string, object> PaymentMetadata { get; set; } = new();
}

/// <summary>
/// Command to send payment reminder
/// </summary>
public class SendPaymentReminderCommand : IRequest<SendPaymentReminderResult>
{
    public Guid PaymentLinkId { get; set; }
    public List<string> ReminderChannels { get; set; } = new(); // Email, SMS
    public string? CustomMessage { get; set; }
    public bool IsUrgent { get; set; } = false;
    public Guid SentByUserId { get; set; }
}

/// <summary>
/// Result of sending payment reminder
/// </summary>
public class SendPaymentReminderResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public int RemindersSent { get; set; }
    public int RemindersFailed { get; set; }
    public List<string> SuccessfulChannels { get; set; } = new();
    public List<string> FailedChannels { get; set; } = new();
    public DateTime SentAt { get; set; }
    public int TotalRemindersForLink { get; set; }
}
