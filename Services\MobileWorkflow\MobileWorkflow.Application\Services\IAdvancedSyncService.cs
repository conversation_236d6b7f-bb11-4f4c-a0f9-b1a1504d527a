using MobileWorkflow.Application.DTOs;

namespace MobileWorkflow.Application.Services;

public interface IAdvancedSyncService
{
    Task<SyncOperationResult> StartSyncOperationAsync(Guid userId, Guid deviceId, SyncOptions options, CancellationToken cancellationToken = default);
    Task<SyncOperationResult> GetSyncStatusAsync(Guid syncOperationId, CancellationToken cancellationToken = default);
    Task<List<ConflictResolutionDto>> GetPendingConflictsAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<bool> ResolveConflictAsync(Guid conflictId, ConflictResolutionRequest request, CancellationToken cancellationToken = default);
    Task<SyncStatistics> GetSyncStatisticsAsync(Guid userId, DateTime? fromDate = null, CancellationToken cancellationToken = default);
    Task<bool> OptimizeBandwidthAsync(Guid syncOperationId, BandwidthOptimizationOptions options, CancellationToken cancellationToken = default);
    Task<List<SyncItem>> GetPendingSyncItemsAsync(Guid userId, int priority = 0, CancellationToken cancellationToken = default);
    Task<bool> CancelSyncOperationAsync(Guid syncOperationId, CancellationToken cancellationToken = default);
    Task<bool> EnableProgressiveSyncAsync(Guid syncOperationId, CancellationToken cancellationToken = default);
    Task<bool> ApplyResolutionToSimilarConflictsAsync(Guid conflictId, ConflictResolutionRequest request, CancellationToken cancellationToken = default);
}

public class ConflictResolutionRequest
{
    public Guid ConflictId { get; set; }
    public string ResolutionType { get; set; } = string.Empty; // "UseLocal", "UseRemote", "Merge", "Custom"
    public string ResolutionStrategy { get; set; } = string.Empty;
    public Dictionary<string, object> ResolutionData { get; set; } = new();
    public Dictionary<string, object> CustomData { get; set; } = new();
    public string ResolvedBy { get; set; } = string.Empty;
    public string Comments { get; set; } = string.Empty;
    public string Notes { get; set; } = string.Empty;
    public bool ApplyToSimilar { get; set; } = false;
}


