using Shared.Domain.Common;

namespace UserManagement.Domain.Events;

/// <summary>
/// Domain event raised when a user flag is resolved
/// </summary>
public class UserFlagResolvedEvent : DomainEvent
{
    public Guid UserId { get; }
    public Guid FlagId { get; }
    public string Resolution { get; }
    public Guid ResolvedBy { get; }
    public DateTime ResolvedAt { get; }

    public UserFlagResolvedEvent(
        Guid userId,
        Guid flagId,
        string resolution,
        Guid resolvedBy,
        DateTime resolvedAt)
    {
        UserId = userId;
        FlagId = flagId;
        Resolution = resolution;
        ResolvedBy = resolvedBy;
        ResolvedAt = resolvedAt;
    }
}
