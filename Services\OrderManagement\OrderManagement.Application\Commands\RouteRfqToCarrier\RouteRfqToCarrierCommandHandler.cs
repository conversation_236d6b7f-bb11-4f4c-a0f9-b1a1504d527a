using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Entities;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Application.Commands.RouteRfqToCarrier;

public class RouteRfqToCarrierCommandHandler : IRequestHandler<RouteRfqToCarrierCommand, bool>
{
    private readonly IRfqRepository _rfqRepository;
    private readonly IRfqRoutingHistoryRepository _routingHistoryRepository;
    private readonly IRfqTimelineRepository _timelineRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<RouteRfqToCarrierCommandHandler> _logger;

    public RouteRfqToCarrierCommandHandler(
        IRfqRepository rfqRepository,
        IRfqRoutingHistoryRepository routingHistoryRepository,
        IRfqTimelineRepository timelineRepository,
        IUnitOfWork unitOfWork,
        ILogger<RouteRfqToCarrierCommandHandler> logger)
    {
        _rfqRepository = rfqRepository;
        _routingHistoryRepository = routingHistoryRepository;
        _timelineRepository = timelineRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<bool> Handle(RouteRfqToCarrierCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Routing RFQ {RfqId} to carrier {CarrierId} by user {RoutedBy}",
            request.RfqId, request.CarrierId, request.RoutedBy);

        try
        {
            // Get the RFQ
            var rfq = await _rfqRepository.GetByIdAsync(request.RfqId, cancellationToken);
            if (rfq == null)
            {
                _logger.LogWarning("RFQ {RfqId} not found", request.RfqId);
                return false;
            }

            // Validate RFQ can be routed
            if (rfq.Status != RfqStatus.Published)
            {
                _logger.LogWarning("RFQ {RfqId} cannot be routed - status is {Status}", request.RfqId, rfq.Status);
                return false;
            }

            // Get next sequence number
            var existingRoutingHistory = await _routingHistoryRepository.GetByRfqIdAsync(request.RfqId, cancellationToken);
            var nextSequenceNumber = existingRoutingHistory.Count + 1;

            // Create routing history entry
            var routingHistory = RfqRoutingHistory.CreateCarrierRouting(
                request.RfqId,
                request.CarrierId,
                request.CarrierName,
                request.FromBrokerId,
                request.FromBrokerName,
                request.RoutedBy,
                request.RoutedByName,
                request.RoutingReason,
                request.RoutingNotes,
                request.ResponseDeadline,
                nextSequenceNumber);

            // Add routing history to RFQ
            rfq.AddRoutingHistory(routingHistory);

            // Create timeline event if requested
            if (request.CreateTimelineEvent)
            {
                var timelineEvent = RfqTimeline.CreateEvent(
                    request.RfqId,
                    RfqTimelineEventType.RouteAssigned,
                    $"RFQ routed to carrier '{request.CarrierName}' for execution" + 
                    (request.FromBrokerId.HasValue ? $" via broker '{request.FromBrokerName}'" : ""),
                    request.RoutedBy,
                    request.RoutedByName,
                    request.FromBrokerId.HasValue ? "Broker" : "Transport Company",
                    $"{{\"carrierId\": \"{request.CarrierId}\", \"carrierName\": \"{request.CarrierName}\", \"fromBrokerId\": \"{request.FromBrokerId}\", \"fromBrokerName\": \"{request.FromBrokerName}\", \"reason\": \"{request.RoutingReason}\", \"responseDeadline\": \"{request.ResponseDeadline:yyyy-MM-ddTHH:mm:ssZ}\"}}");

                await _timelineRepository.AddAsync(timelineEvent, cancellationToken);
            }

            // Save changes
            _rfqRepository.Update(rfq);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Successfully routed RFQ {RfqId} to carrier {CarrierId}", request.RfqId, request.CarrierId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error routing RFQ {RfqId} to carrier {CarrierId}", request.RfqId, request.CarrierId);
            throw;
        }
    }
}
