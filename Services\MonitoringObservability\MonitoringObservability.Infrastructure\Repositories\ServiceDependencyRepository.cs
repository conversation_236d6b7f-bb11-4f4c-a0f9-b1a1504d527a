using Microsoft.EntityFrameworkCore;
using MonitoringObservability.Domain.Entities;
using MonitoringObservability.Domain.Interfaces;
using MonitoringObservability.Infrastructure.Data;

namespace MonitoringObservability.Infrastructure.Repositories;

/// <summary>
/// Repository implementation for service dependencies
/// </summary>
public class ServiceDependencyRepository : IServiceDependencyRepository
{
    private readonly MonitoringDbContext _context;

    public ServiceDependencyRepository(MonitoringDbContext context)
    {
        _context = context ?? throw new ArgumentNullException(nameof(context));
    }

    public async Task<ServiceDependency?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.ServiceDependencies
            .FirstOrDefaultAsync(d => d.Id == id, cancellationToken);
    }

    public async Task<IEnumerable<ServiceDependency>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        return await _context.ServiceDependencies
            .OrderBy(d => d.FromService)
            .ThenBy(d => d.ToService)
            .ToListAsync(cancellationToken);
    }

    public async Task AddAsync(ServiceDependency dependency, CancellationToken cancellationToken = default)
    {
        await _context.ServiceDependencies.AddAsync(dependency, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task UpdateAsync(ServiceDependency dependency, CancellationToken cancellationToken = default)
    {
        _context.ServiceDependencies.Update(dependency);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var dependency = await _context.ServiceDependencies.FindAsync(new object[] { id }, cancellationToken);
        if (dependency != null)
        {
            _context.ServiceDependencies.Remove(dependency);
            await _context.SaveChangesAsync(cancellationToken);
        }
    }

    public async Task<IEnumerable<ServiceDependency>> GetDependenciesForServiceAsync(
        string serviceName,
        CancellationToken cancellationToken = default)
    {
        return await _context.ServiceDependencies
            .Where(d => d.FromService == serviceName)
            .OrderBy(d => d.ToService)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<ServiceDependency>> GetDependentsOfServiceAsync(
        string serviceName,
        CancellationToken cancellationToken = default)
    {
        return await _context.ServiceDependencies
            .Where(d => d.ToService == serviceName)
            .OrderBy(d => d.FromService)
            .ToListAsync(cancellationToken);
    }

    public async Task<ServiceDependency?> GetDependencyAsync(
        string fromService,
        string toService,
        string operationType,
        CancellationToken cancellationToken = default)
    {
        return await _context.ServiceDependencies
            .FirstOrDefaultAsync(d => d.FromService == fromService && 
                                    d.ToService == toService && 
                                    d.OperationType == operationType, 
                                cancellationToken);
    }

    public async Task<IEnumerable<ServiceDependency>> GetHighErrorRateDependenciesAsync(
        double errorRateThreshold = 0.05,
        CancellationToken cancellationToken = default)
    {
        return await _context.ServiceDependencies
            .Where(d => d.ErrorRate >= errorRateThreshold)
            .OrderByDescending(d => d.ErrorRate)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<ServiceDependency>> GetSlowDependenciesAsync(
        TimeSpan latencyThreshold,
        CancellationToken cancellationToken = default)
    {
        return await _context.ServiceDependencies
            .Where(d => d.AverageLatency >= latencyThreshold)
            .OrderByDescending(d => d.AverageLatency)
            .ToListAsync(cancellationToken);
    }

    public async Task<Dictionary<string, int>> GetServiceCallCountsAsync(
        TimeSpan period,
        CancellationToken cancellationToken = default)
    {
        var fromDate = DateTime.UtcNow.Subtract(period);
        
        var results = await _context.ServiceDependencies
            .Where(d => d.LastSeen >= fromDate)
            .GroupBy(d => d.FromService)
            .Select(g => new { ServiceName = g.Key, TotalCalls = g.Sum(d => d.CallCount) })
            .ToListAsync(cancellationToken);

        return results.ToDictionary(r => r.ServiceName, r => r.TotalCalls);
    }

    public async Task<IEnumerable<(string fromService, string toService, double errorRate)>> GetTopErrorRateDependenciesAsync(
        int topCount = 10,
        TimeSpan? period = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.ServiceDependencies.AsQueryable();

        if (period.HasValue)
        {
            var fromDate = DateTime.UtcNow.Subtract(period.Value);
            query = query.Where(d => d.LastSeen >= fromDate);
        }

        var results = await query
            .Where(d => d.ErrorRate > 0)
            .OrderByDescending(d => d.ErrorRate)
            .Take(topCount)
            .Select(d => new { d.FromService, d.ToService, d.ErrorRate })
            .ToListAsync(cancellationToken);

        return results.Select(r => (r.FromService, r.ToService, r.ErrorRate));
    }

    public async Task<int> DeleteOldDependenciesAsync(TimeSpan retentionPeriod, CancellationToken cancellationToken = default)
    {
        var cutoffDate = DateTime.UtcNow.Subtract(retentionPeriod);
        
        var oldDependencies = await _context.ServiceDependencies
            .Where(d => d.LastSeen < cutoffDate)
            .ToListAsync(cancellationToken);

        if (oldDependencies.Any())
        {
            _context.ServiceDependencies.RemoveRange(oldDependencies);
            await _context.SaveChangesAsync(cancellationToken);
        }

        return oldDependencies.Count;
    }

    public async Task<IEnumerable<ServiceDependency>> GetServiceDependencyGraphAsync(CancellationToken cancellationToken = default)
    {
        return await _context.ServiceDependencies
            .Where(d => d.CallCount > 0) // Only include dependencies with actual calls
            .OrderBy(d => d.FromService)
            .ThenBy(d => d.ToService)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<string>> GetAllServiceNamesAsync(CancellationToken cancellationToken = default)
    {
        var fromServices = await _context.ServiceDependencies
            .Select(d => d.FromService)
            .Distinct()
            .ToListAsync(cancellationToken);

        var toServices = await _context.ServiceDependencies
            .Select(d => d.ToService)
            .Distinct()
            .ToListAsync(cancellationToken);

        return fromServices.Union(toServices).Distinct().OrderBy(s => s);
    }

    public async Task<Dictionary<string, ServiceDependencyStats>> GetServiceDependencyStatsAsync(
        TimeSpan period,
        CancellationToken cancellationToken = default)
    {
        var fromDate = DateTime.UtcNow.Subtract(period);
        
        var stats = await _context.ServiceDependencies
            .Where(d => d.LastSeen >= fromDate)
            .GroupBy(d => d.FromService)
            .Select(g => new
            {
                ServiceName = g.Key,
                TotalDependencies = g.Count(),
                TotalCalls = g.Sum(d => d.CallCount),
                AverageErrorRate = g.Average(d => d.ErrorRate),
                AverageLatency = g.Where(d => d.AverageLatency.HasValue).Average(d => d.AverageLatency!.Value.TotalMilliseconds)
            })
            .ToListAsync(cancellationToken);

        return stats.ToDictionary(
            s => s.ServiceName,
            s => new ServiceDependencyStats
            {
                TotalDependencies = s.TotalDependencies,
                TotalCalls = s.TotalCalls,
                AverageErrorRate = s.AverageErrorRate,
                AverageLatencyMs = s.AverageLatency
            });
    }
}

/// <summary>
/// Statistics for service dependencies
/// </summary>
public class ServiceDependencyStats
{
    public int TotalDependencies { get; set; }
    public int TotalCalls { get; set; }
    public double AverageErrorRate { get; set; }
    public double AverageLatencyMs { get; set; }
}
