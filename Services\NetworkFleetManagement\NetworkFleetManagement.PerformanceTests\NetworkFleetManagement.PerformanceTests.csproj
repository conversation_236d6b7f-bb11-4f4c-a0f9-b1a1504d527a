<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageReference Include="xunit" Version="2.6.1" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.5.3">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="coverlet.collector" Version="6.0.0">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="NBomber" Version="5.0.0" />
    <PackageReference Include="NBomber.Http" Version="5.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="8.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="8.0.11" />
    <PackageReference Include="Testcontainers.PostgreSql" Version="3.6.0" />
    <PackageReference Include="Testcontainers.Redis" Version="3.6.0" />
    <PackageReference Include="Bogus" Version="35.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\NetworkFleetManagement.API\NetworkFleetManagement.API.csproj" />
    <ProjectReference Include="..\NetworkFleetManagement.Application\NetworkFleetManagement.Application.csproj" />
    <ProjectReference Include="..\NetworkFleetManagement.Infrastructure\NetworkFleetManagement.Infrastructure.csproj" />
    <ProjectReference Include="..\NetworkFleetManagement.Domain\NetworkFleetManagement.Domain.csproj" />
  </ItemGroup>

</Project>
