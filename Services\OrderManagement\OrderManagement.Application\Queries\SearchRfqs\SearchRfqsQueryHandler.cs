using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.DTOs;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Application.Queries.SearchRfqs;

public class SearchRfqsQueryHandler : IRequestHandler<SearchRfqsQuery, PagedResult<RfqSummaryDto>>
{
    private readonly IRfqRepository _rfqRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<SearchRfqsQueryHandler> _logger;

    public SearchRfqsQueryHandler(
        IRfqRepository rfqRepository,
        IMapper mapper,
        ILogger<SearchRfqsQueryHandler> logger)
    {
        _rfqRepository = rfqRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<PagedResult<RfqSummaryDto>> Handle(SearchRfqsQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Searching RFQs with term '{SearchTerm}' for user {UserId}, Page: {Page}, PageSize: {PageSize}",
            request.SearchTerm, request.RequestingUserId, request.Page, request.PageSize);

        // Validate pagination parameters
        if (request.Page < 1) request.Page = 1;
        if (request.PageSize < 1 || request.PageSize > 100) request.PageSize = 20;

        // Validate search term
        if (string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            _logger.LogWarning("Empty search term provided for user {UserId}", request.RequestingUserId);
            return new PagedResult<RfqSummaryDto>
            {
                Items = new List<RfqSummaryDto>(),
                TotalCount = 0,
                Page = request.Page,
                PageSize = request.PageSize
            };
        }

        // Parse status filters
        List<RfqStatus>? statuses = null;
        if (request.Statuses?.Any() == true)
        {
            statuses = new List<RfqStatus>();
            foreach (var statusStr in request.Statuses)
            {
                if (Enum.TryParse<RfqStatus>(statusStr, true, out var status))
                {
                    statuses.Add(status);
                }
            }
        }

        try
        {
            // Perform search
            var (rfqs, totalCount) = await _rfqRepository.SearchRfqsAsync(
                request.SearchTerm,
                request.Page,
                request.PageSize,
                request.FromDate,
                request.ToDate,
                statuses,
                request.TransportCompanyId,
                request.ExcludeExpired,
                cancellationToken);

            // Map to DTOs
            var rfqDtos = _mapper.Map<List<RfqSummaryDto>>(rfqs);

            var result = new PagedResult<RfqSummaryDto>
            {
                Items = rfqDtos,
                TotalCount = totalCount,
                Page = request.Page,
                PageSize = request.PageSize
            };

            _logger.LogInformation("Successfully found {Count} RFQs out of {TotalCount} matching search term '{SearchTerm}' for user {UserId}",
                rfqDtos.Count, totalCount, request.SearchTerm, request.RequestingUserId);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching RFQs with term '{SearchTerm}' for user {UserId}", 
                request.SearchTerm, request.RequestingUserId);
            throw;
        }
    }
}
