# Server 1 - Infrastructure Setup Guide

## Overview

Server 1 hosts all infrastructure services including PostgreSQL, Redis, RabbitMQ, and monitoring tools. This server acts as the data and messaging backbone for the TLI microservices.

## Server Specifications

### Minimum Requirements

- **CPU**: 4 cores
- **RAM**: 8GB
- **Storage**: 200GB SSD
- **OS**: Ubuntu 24.04 LTS

### Recommended Requirements

- **CPU**: 8 cores
- **RAM**: 16GB
- **Storage**: 500GB SSD
- **Network**: 1Gbps connection

## Step 1: Initial Server Setup

### 1.1 Update System

```bash
# Update package lists and upgrade system
sudo apt update && sudo apt upgrade -y

# Install essential packages
sudo apt install -y curl wget git unzip software-properties-common apt-transport-https ca-certificates gnupg lsb-release

# Set timezone (adjust as needed)
sudo timedatectl set-timezone Asia/Kolkata

# Configure hostname
sudo hostnamectl set-hostname tli-infrastructure
```

### 1.2 Create TLI User

```bash
# Create dedicated user for TLI services
sudo useradd -m -s /bin/bash tli
sudo usermod -aG sudo tli

# Set password for tli user
sudo passwd tli

# Create directories
sudo mkdir -p /opt/tli/{data,logs,backups,config}
sudo chown -R tli:tli /opt/tli
```

### 1.3 Configure Firewall

```bash
# Enable UFW firewall
sudo ufw enable

# Allow SSH
sudo ufw allow ssh

# Allow PostgreSQL (from Server 2 only - replace with actual IP)
sudo ufw allow from SERVER2_IP to any port 5432

# Allow Redis (from Server 2 only)
sudo ufw allow from SERVER2_IP to any port 6379

# Allow RabbitMQ (from Server 2 only)
sudo ufw allow from SERVER2_IP to any port 5672

# Allow RabbitMQ Management UI (from admin networks only)
sudo ufw allow from ADMIN_NETWORK to any port 15672

# Allow Prometheus (from monitoring networks)
sudo ufw allow from MONITORING_NETWORK to any port 9090

# Allow Grafana (from admin networks)
sudo ufw allow from ADMIN_NETWORK to any port 3000

# Check firewall status
sudo ufw status verbose
```

## Step 2: Install Docker and Docker Compose

### 2.1 Install Docker

```bash
# Add Docker's official GPG key
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

# Add Docker repository
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# Update package lists
sudo apt update

# Install Docker
sudo apt install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin

# Add tli user to docker group
sudo usermod -aG docker tli

# Enable and start Docker
sudo systemctl enable docker
sudo systemctl start docker

# Verify installation
docker --version
docker compose version
```

### 2.2 Configure Docker

```bash
# Create Docker daemon configuration
sudo tee /etc/docker/daemon.json > /dev/null <<EOF
{
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  },
  "storage-driver": "overlay2",
  "data-root": "/opt/tli/data/docker"
}
EOF

# Restart Docker
sudo systemctl restart docker
```

## Step 3: PostgreSQL Setup

### 3.1 Create PostgreSQL Configuration

```bash
# Switch to tli user
sudo su - tli

# Create PostgreSQL directory structure
mkdir -p /opt/tli/data/postgres
mkdir -p /opt/tli/config/postgres
mkdir -p /opt/tli/logs/postgres

# Create PostgreSQL configuration file
cat > /opt/tli/config/postgres/postgresql.conf << 'EOF'
# PostgreSQL Configuration for TLI
listen_addresses = '*'
port = 5432
max_connections = 200
shared_buffers = 256MB
effective_cache_size = 1GB
maintenance_work_mem = 64MB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
random_page_cost = 1.1
effective_io_concurrency = 200
work_mem = 4MB
min_wal_size = 1GB
max_wal_size = 4GB
max_worker_processes = 8
max_parallel_workers_per_gather = 2
max_parallel_workers = 8
max_parallel_maintenance_workers = 2

# Logging
log_destination = 'stderr'
logging_collector = on
log_directory = '/var/log/postgresql'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_rotation_age = 1d
log_rotation_size = 100MB
log_min_duration_statement = 1000
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
log_checkpoints = on
log_connections = on
log_disconnections = on
log_lock_waits = on
log_temp_files = 10MB

# Security
ssl = on
ssl_cert_file = '/var/lib/postgresql/server.crt'
ssl_key_file = '/var/lib/postgresql/server.key'
EOF
```

### 3.2 Create PostgreSQL Host-based Authentication

```bash
cat > /opt/tli/config/postgres/pg_hba.conf << 'EOF'
# PostgreSQL Client Authentication Configuration File
# TYPE  DATABASE        USER            ADDRESS                 METHOD

# "local" is for Unix domain socket connections only
local   all             all                                     trust

# IPv4 local connections:
host    all             all             127.0.0.1/32            md5

# IPv4 connections from Server 2 (replace with actual IP)
host    all             all             SERVER2_IP/32           md5

# IPv4 connections from admin networks
host    all             all             ADMIN_NETWORK/24        md5

# Deny all other connections
host    all             all             0.0.0.0/0               reject
EOF
```

### 3.3 Deploy PostgreSQL with Docker

```bash
# Create PostgreSQL Docker Compose file
cat > /opt/tli/config/postgres/docker-compose.yml << 'EOF'
version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: tli-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      PGDATA: /var/lib/postgresql/data/pgdata
    ports:
      - "5432:5432"
    volumes:
      - /opt/tli/data/postgres:/var/lib/postgresql/data
      - /opt/tli/config/postgres/postgresql.conf:/etc/postgresql/postgresql.conf
      - /opt/tli/config/postgres/pg_hba.conf:/etc/postgresql/pg_hba.conf
      - /opt/tli/logs/postgres:/var/log/postgresql
    command: postgres -c config_file=/etc/postgresql/postgresql.conf
    networks:
      - tli-infrastructure
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  tli-infrastructure:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
EOF

# Create environment file
cat > /opt/tli/config/postgres/.env << 'EOF'
POSTGRES_PASSWORD=TLI_Postgres_2024!@#
EOF

# Start PostgreSQL
cd /opt/tli/config/postgres
docker compose up -d

# Wait for PostgreSQL to start
sleep 30

# Verify PostgreSQL is running
docker compose ps
docker compose logs postgres
```

## Step 4: Create TLI Databases

### 4.1 Database Creation Script

```bash
# Create database setup script
cat > /opt/tli/config/postgres/setup-databases.sql << 'EOF'
-- Create TLI databases
CREATE DATABASE "TLI_Identity";
CREATE DATABASE "TLI_UserManagement";
CREATE DATABASE "TLI_SubscriptionManagement";
CREATE DATABASE "TLI_OrderManagement";
CREATE DATABASE "TLI_TripManagement";
CREATE DATABASE "TLI_NetworkFleetManagement";
CREATE DATABASE "TLI_FinancialPayment";
CREATE DATABASE "TLI_CommunicationNotification";
CREATE DATABASE "TLI_AnalyticsBIService";
CREATE DATABASE "TLI_DataStorage";
CREATE DATABASE "TLI_AuditCompliance";
CREATE DATABASE "TLI_MonitoringObservability";
CREATE DATABASE "TLI_MobileWorkflow";

-- Create application user
CREATE USER tli_admin WITH PASSWORD 'TLI_Admin_2024!@#';

-- Grant privileges to all databases
GRANT ALL PRIVILEGES ON DATABASE "TLI_Identity" TO tli_admin;
GRANT ALL PRIVILEGES ON DATABASE "TLI_UserManagement" TO tli_admin;
GRANT ALL PRIVILEGES ON DATABASE "TLI_SubscriptionManagement" TO tli_admin;
GRANT ALL PRIVILEGES ON DATABASE "TLI_OrderManagement" TO tli_admin;
GRANT ALL PRIVILEGES ON DATABASE "TLI_TripManagement" TO tli_admin;
GRANT ALL PRIVILEGES ON DATABASE "TLI_NetworkFleetManagement" TO tli_admin;
GRANT ALL PRIVILEGES ON DATABASE "TLI_FinancialPayment" TO tli_admin;
GRANT ALL PRIVILEGES ON DATABASE "TLI_CommunicationNotification" TO tli_admin;
GRANT ALL PRIVILEGES ON DATABASE "TLI_AnalyticsBIService" TO tli_admin;
GRANT ALL PRIVILEGES ON DATABASE "TLI_DataStorage" TO tli_admin;
GRANT ALL PRIVILEGES ON DATABASE "TLI_AuditCompliance" TO tli_admin;
GRANT ALL PRIVILEGES ON DATABASE "TLI_MonitoringObservability" TO tli_admin;
GRANT ALL PRIVILEGES ON DATABASE "TLI_MobileWorkflow" TO tli_admin;

-- Install TimescaleDB extension for time-series data
\c "TLI_AnalyticsBIService";
CREATE EXTENSION IF NOT EXISTS timescaledb;

\c "TLI_TripManagement";
CREATE EXTENSION IF NOT EXISTS timescaledb;

\c "TLI_MonitoringObservability";
CREATE EXTENSION IF NOT EXISTS timescaledb;
EOF

# Execute database setup
docker exec -i tli-postgres psql -U postgres < /opt/tli/config/postgres/setup-databases.sql

# Verify databases
docker exec tli-postgres psql -U postgres -c "\l"
```

### 4.2 Database Backup Configuration

```bash
# Create backup script
cat > /opt/tli/config/postgres/backup-databases.sh << 'EOF'
#!/bin/bash

BACKUP_DIR="/opt/tli/backups/postgres"
DATE=$(date +%Y%m%d_%H%M%S)
DATABASES=(
  "TLI_Identity"
  "TLI_UserManagement"
  "TLI_SubscriptionManagement"
  "TLI_OrderManagement"
  "TLI_TripManagement"
  "TLI_NetworkFleetManagement"
  "TLI_FinancialPayment"
  "TLI_CommunicationNotification"
  "TLI_AnalyticsBIService"
  "TLI_DataStorage"
  "TLI_AuditCompliance"
  "TLI_MonitoringObservability"
  "TLI_MobileWorkflow"
)

mkdir -p $BACKUP_DIR

for db in "${DATABASES[@]}"; do
  echo "Backing up $db..."
  docker exec tli-postgres pg_dump -U postgres "$db" | gzip > "$BACKUP_DIR/${db}_${DATE}.sql.gz"
done

# Keep only last 7 days of backups
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete

echo "Backup completed: $DATE"
EOF

chmod +x /opt/tli/config/postgres/backup-databases.sh

# Create cron job for daily backups
(crontab -l 2>/dev/null; echo "0 2 * * * /opt/tli/config/postgres/backup-databases.sh >> /opt/tli/logs/postgres/backup.log 2>&1") | crontab -
```

## Step 5: Redis Setup

### 5.1 Create Redis Configuration

```bash
# Create Redis directory structure
mkdir -p /opt/tli/data/redis
mkdir -p /opt/tli/config/redis
mkdir -p /opt/tli/logs/redis

# Create Redis configuration file
cat > /opt/tli/config/redis/redis.conf << 'EOF'
# Redis Configuration for TLI
bind 0.0.0.0
port 6379
protected-mode yes
requirepass TLI_Redis_2024!@#

# Memory management
maxmemory 2gb
maxmemory-policy allkeys-lru

# Persistence
save 900 1
save 300 10
save 60 10000
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# Append only file
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# Logging
loglevel notice
logfile /var/log/redis/redis-server.log

# Security
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command DEBUG ""
rename-command CONFIG "CONFIG_TLI_2024"

# Performance
tcp-keepalive 300
timeout 0
tcp-backlog 511
databases 16
EOF

# Create Redis Docker Compose file
cat > /opt/tli/config/redis/docker-compose.yml << 'EOF'
version: '3.8'

services:
  redis:
    image: redis:7-alpine
    container_name: tli-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - /opt/tli/data/redis:/data
      - /opt/tli/config/redis/redis.conf:/usr/local/etc/redis/redis.conf
      - /opt/tli/logs/redis:/var/log/redis
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - tli-infrastructure
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  tli-infrastructure:
    external: true
EOF

# Start Redis
cd /opt/tli/config/redis
docker compose up -d

# Verify Redis is running
docker compose ps
docker compose logs redis

# Test Redis connection
docker exec tli-redis redis-cli -a "TLI_Redis_2024!@#" ping
```

## Step 6: RabbitMQ Setup

### 6.1 Create RabbitMQ Configuration

```bash
# Create RabbitMQ directory structure
mkdir -p /opt/tli/data/rabbitmq
mkdir -p /opt/tli/config/rabbitmq
mkdir -p /opt/tli/logs/rabbitmq

# Create RabbitMQ configuration file
cat > /opt/tli/config/rabbitmq/rabbitmq.conf << 'EOF'
# RabbitMQ Configuration for TLI
listeners.tcp.default = 5672
management.tcp.port = 15672

# Default user
default_user = tli_admin
default_pass = TLI_RabbitMQ_2024!@#

# Logging
log.file = /var/log/rabbitmq/rabbit.log
log.file.level = info
log.file.rotation.date = $D0
log.file.rotation.size = 100MB

# Memory and disk limits
vm_memory_high_watermark.relative = 0.6
disk_free_limit.relative = 2.0

# Clustering (for future expansion)
cluster_formation.peer_discovery_backend = classic_config

# Security
ssl_options.verify = verify_peer
ssl_options.fail_if_no_peer_cert = true
EOF

# Create RabbitMQ definitions (exchanges, queues, users)
cat > /opt/tli/config/rabbitmq/definitions.json << 'EOF'
{
  "users": [
    {
      "name": "tli_admin",
      "password_hash": "TLI_RabbitMQ_2024!@#",
      "hashing_algorithm": "rabbit_password_hashing_sha256",
      "tags": "administrator"
    },
    {
      "name": "tli_service",
      "password_hash": "TLI_Service_2024!@#",
      "hashing_algorithm": "rabbit_password_hashing_sha256",
      "tags": ""
    }
  ],
  "vhosts": [
    {
      "name": "/"
    },
    {
      "name": "tli"
    }
  ],
  "permissions": [
    {
      "user": "tli_admin",
      "vhost": "/",
      "configure": ".*",
      "write": ".*",
      "read": ".*"
    },
    {
      "user": "tli_admin",
      "vhost": "tli",
      "configure": ".*",
      "write": ".*",
      "read": ".*"
    },
    {
      "user": "tli_service",
      "vhost": "tli",
      "configure": ".*",
      "write": ".*",
      "read": ".*"
    }
  ],
  "exchanges": [
    {
      "name": "tli.events",
      "vhost": "tli",
      "type": "topic",
      "durable": true,
      "auto_delete": false,
      "internal": false,
      "arguments": {}
    },
    {
      "name": "tli.commands",
      "vhost": "tli",
      "type": "direct",
      "durable": true,
      "auto_delete": false,
      "internal": false,
      "arguments": {}
    }
  ]
}
EOF

# Create RabbitMQ Docker Compose file
cat > /opt/tli/config/rabbitmq/docker-compose.yml << 'EOF'
version: '3.8'

services:
  rabbitmq:
    image: rabbitmq:3.12-management
    container_name: tli-rabbitmq
    restart: unless-stopped
    environment:
      RABBITMQ_DEFAULT_USER: tli_admin
      RABBITMQ_DEFAULT_PASS: TLI_RabbitMQ_2024!@#
      RABBITMQ_CONFIG_FILE: /etc/rabbitmq/rabbitmq
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - /opt/tli/data/rabbitmq:/var/lib/rabbitmq
      - /opt/tli/config/rabbitmq/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf
      - /opt/tli/config/rabbitmq/definitions.json:/etc/rabbitmq/definitions.json
      - /opt/tli/logs/rabbitmq:/var/log/rabbitmq
    networks:
      - tli-infrastructure
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  tli-infrastructure:
    external: true
EOF

# Start RabbitMQ
cd /opt/tli/config/rabbitmq
docker compose up -d

# Wait for RabbitMQ to start
sleep 60

# Verify RabbitMQ is running
docker compose ps
docker compose logs rabbitmq

# Test RabbitMQ connection
docker exec tli-rabbitmq rabbitmq-diagnostics ping
```

## Step 7: Monitoring Stack Setup

### 7.1 Prometheus Configuration

```bash
# Create monitoring directory structure
mkdir -p /opt/tli/data/prometheus
mkdir -p /opt/tli/config/prometheus
mkdir -p /opt/tli/logs/prometheus

# Create Prometheus configuration
cat > /opt/tli/config/prometheus/prometheus.yml << 'EOF'
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "tli_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']

  - job_name: 'rabbitmq'
    static_configs:
      - targets: ['rabbitmq:15692']

  - job_name: 'tli-services'
    static_configs:
      - targets:
        - 'SERVER2_IP:5001'  # Identity Service
        - 'SERVER2_IP:5002'  # User Management
        - 'SERVER2_IP:5003'  # Subscription Management
        - 'SERVER2_IP:5004'  # Order Management
        - 'SERVER2_IP:5005'  # Trip Management
        - 'SERVER2_IP:5006'  # Network & Fleet
        - 'SERVER2_IP:5007'  # Financial & Payment
        - 'SERVER2_IP:5008'  # Communication
        - 'SERVER2_IP:5009'  # Analytics & BI
        - 'SERVER2_IP:5010'  # Data & Storage
        - 'SERVER2_IP:5011'  # Audit & Compliance
        - 'SERVER2_IP:5012'  # Monitoring
        - 'SERVER2_IP:5013'  # Mobile Workflow
    scrape_interval: 30s
    metrics_path: '/metrics'
EOF

# Create alerting rules
cat > /opt/tli/config/prometheus/tli_rules.yml << 'EOF'
groups:
  - name: tli_infrastructure
    rules:
      - alert: PostgreSQLDown
        expr: up{job="postgres"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "PostgreSQL is down"
          description: "PostgreSQL has been down for more than 1 minute."

      - alert: RedisDown
        expr: up{job="redis"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Redis is down"
          description: "Redis has been down for more than 1 minute."

      - alert: RabbitMQDown
        expr: up{job="rabbitmq"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "RabbitMQ is down"
          description: "RabbitMQ has been down for more than 1 minute."

  - name: tli_services
    rules:
      - alert: TLIServiceDown
        expr: up{job="tli-services"} == 0
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "TLI Service {{ $labels.instance }} is down"
          description: "TLI Service {{ $labels.instance }} has been down for more than 2 minutes."
EOF
```

### 7.2 Grafana Configuration

```bash
# Create Grafana directory structure
mkdir -p /opt/tli/data/grafana
mkdir -p /opt/tli/config/grafana
mkdir -p /opt/tli/logs/grafana

# Create Grafana configuration
cat > /opt/tli/config/grafana/grafana.ini << 'EOF'
[server]
http_port = 3000
domain = localhost
root_url = http://localhost:3000

[database]
type = sqlite3
path = /var/lib/grafana/grafana.db

[security]
admin_user = admin
admin_password = TLI_Grafana_2024!@#
secret_key = TLI_Secret_Key_2024

[users]
allow_sign_up = false
allow_org_create = false
auto_assign_org = true
auto_assign_org_role = Viewer

[auth.anonymous]
enabled = false

[log]
mode = file
level = info
EOF

# Create datasources configuration
cat > /opt/tli/config/grafana/datasources.yml << 'EOF'
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
EOF

# Create monitoring stack Docker Compose file
cat > /opt/tli/config/monitoring/docker-compose.yml << 'EOF'
version: '3.8'

services:
  prometheus:
    image: prom/prometheus:latest
    container_name: tli-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - /opt/tli/data/prometheus:/prometheus
      - /opt/tli/config/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - /opt/tli/config/prometheus/tli_rules.yml:/etc/prometheus/tli_rules.yml
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
    networks:
      - tli-infrastructure

  grafana:
    image: grafana/grafana:latest
    container_name: tli-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    volumes:
      - /opt/tli/data/grafana:/var/lib/grafana
      - /opt/tli/config/grafana/grafana.ini:/etc/grafana/grafana.ini
      - /opt/tli/config/grafana/datasources.yml:/etc/grafana/provisioning/datasources/datasources.yml
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=TLI_Grafana_2024!@#
    networks:
      - tli-infrastructure
    depends_on:
      - prometheus

  postgres-exporter:
    image: prometheuscommunity/postgres-exporter:latest
    container_name: tli-postgres-exporter
    restart: unless-stopped
    environment:
      DATA_SOURCE_NAME: "postgresql://postgres:TLI_Postgres_2024!@#@postgres:5432/postgres?sslmode=disable"
    ports:
      - "9187:9187"
    networks:
      - tli-infrastructure
    depends_on:
      - prometheus

  redis-exporter:
    image: oliver006/redis_exporter:latest
    container_name: tli-redis-exporter
    restart: unless-stopped
    environment:
      REDIS_ADDR: "redis://redis:6379"
      REDIS_PASSWORD: "TLI_Redis_2024!@#"
    ports:
      - "9121:9121"
    networks:
      - tli-infrastructure
    depends_on:
      - prometheus

networks:
  tli-infrastructure:
    external: true
EOF

# Start monitoring stack
cd /opt/tli/config/monitoring
docker compose up -d

# Wait for services to start
sleep 60

# Verify monitoring stack
docker compose ps
```

## Step 8: Complete Infrastructure Setup

### 8.1 Create Master Docker Compose

```bash
# Create master infrastructure compose file
cat > /opt/tli/infrastructure-stack.yml << 'EOF'
version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    container_name: tli-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: TLI_Postgres_2024!@#
      PGDATA: /var/lib/postgresql/data/pgdata
    ports:
      - "5432:5432"
    volumes:
      - /opt/tli/data/postgres:/var/lib/postgresql/data
      - /opt/tli/config/postgres/postgresql.conf:/etc/postgresql/postgresql.conf
      - /opt/tli/config/postgres/pg_hba.conf:/etc/postgresql/pg_hba.conf
      - /opt/tli/logs/postgres:/var/log/postgresql
    command: postgres -c config_file=/etc/postgresql/postgresql.conf
    networks:
      - tli-infrastructure
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: tli-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - /opt/tli/data/redis:/data
      - /opt/tli/config/redis/redis.conf:/usr/local/etc/redis/redis.conf
      - /opt/tli/logs/redis:/var/log/redis
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - tli-infrastructure
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # RabbitMQ Message Broker
  rabbitmq:
    image: rabbitmq:3.12-management
    container_name: tli-rabbitmq
    restart: unless-stopped
    environment:
      RABBITMQ_DEFAULT_USER: tli_admin
      RABBITMQ_DEFAULT_PASS: TLI_RabbitMQ_2024!@#
      RABBITMQ_CONFIG_FILE: /etc/rabbitmq/rabbitmq
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - /opt/tli/data/rabbitmq:/var/lib/rabbitmq
      - /opt/tli/config/rabbitmq/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf
      - /opt/tli/config/rabbitmq/definitions.json:/etc/rabbitmq/definitions.json
      - /opt/tli/logs/rabbitmq:/var/log/rabbitmq
    networks:
      - tli-infrastructure
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: tli-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - /opt/tli/data/prometheus:/prometheus
      - /opt/tli/config/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - /opt/tli/config/prometheus/tli_rules.yml:/etc/prometheus/tli_rules.yml
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
    networks:
      - tli-infrastructure

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: tli-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    volumes:
      - /opt/tli/data/grafana:/var/lib/grafana
      - /opt/tli/config/grafana/grafana.ini:/etc/grafana/grafana.ini
      - /opt/tli/config/grafana/datasources.yml:/etc/grafana/provisioning/datasources/datasources.yml
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=TLI_Grafana_2024!@#
    networks:
      - tli-infrastructure
    depends_on:
      - prometheus

  # PostgreSQL Exporter
  postgres-exporter:
    image: prometheuscommunity/postgres-exporter:latest
    container_name: tli-postgres-exporter
    restart: unless-stopped
    environment:
      DATA_SOURCE_NAME: "postgresql://postgres:TLI_Postgres_2024!@#@postgres:5432/postgres?sslmode=disable"
    ports:
      - "9187:9187"
    networks:
      - tli-infrastructure
    depends_on:
      - postgres

  # Redis Exporter
  redis-exporter:
    image: oliver006/redis_exporter:latest
    container_name: tli-redis-exporter
    restart: unless-stopped
    environment:
      REDIS_ADDR: "redis://redis:6379"
      REDIS_PASSWORD: "TLI_Redis_2024!@#"
    ports:
      - "9121:9121"
    networks:
      - tli-infrastructure
    depends_on:
      - redis

networks:
  tli-infrastructure:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  postgres_data:
  redis_data:
  rabbitmq_data:
  prometheus_data:
  grafana_data:
EOF
```

### 8.2 Infrastructure Management Scripts

```bash
# Create infrastructure management script
cat > /opt/tli/manage-infrastructure.sh << 'EOF'
#!/bin/bash

SCRIPT_DIR="/opt/tli"
COMPOSE_FILE="$SCRIPT_DIR/infrastructure-stack.yml"

case "$1" in
  start)
    echo "Starting TLI Infrastructure Stack..."
    cd $SCRIPT_DIR
    docker compose -f $COMPOSE_FILE up -d
    echo "Infrastructure stack started successfully!"
    ;;
  stop)
    echo "Stopping TLI Infrastructure Stack..."
    cd $SCRIPT_DIR
    docker compose -f $COMPOSE_FILE down
    echo "Infrastructure stack stopped successfully!"
    ;;
  restart)
    echo "Restarting TLI Infrastructure Stack..."
    cd $SCRIPT_DIR
    docker compose -f $COMPOSE_FILE down
    sleep 10
    docker compose -f $COMPOSE_FILE up -d
    echo "Infrastructure stack restarted successfully!"
    ;;
  status)
    echo "TLI Infrastructure Stack Status:"
    cd $SCRIPT_DIR
    docker compose -f $COMPOSE_FILE ps
    ;;
  logs)
    echo "TLI Infrastructure Stack Logs:"
    cd $SCRIPT_DIR
    docker compose -f $COMPOSE_FILE logs -f
    ;;
  backup)
    echo "Creating backup of TLI databases..."
    /opt/tli/config/postgres/backup-databases.sh
    ;;
  *)
    echo "Usage: $0 {start|stop|restart|status|logs|backup}"
    exit 1
    ;;
esac
EOF

chmod +x /opt/tli/manage-infrastructure.sh

# Create systemd service for infrastructure
sudo tee /etc/systemd/system/tli-infrastructure.service > /dev/null << 'EOF'
[Unit]
Description=TLI Infrastructure Stack
Requires=docker.service
After=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
User=tli
Group=tli
WorkingDirectory=/opt/tli
ExecStart=/opt/tli/manage-infrastructure.sh start
ExecStop=/opt/tli/manage-infrastructure.sh stop
TimeoutStartSec=0

[Install]
WantedBy=multi-user.target
EOF

# Enable and start the service
sudo systemctl daemon-reload
sudo systemctl enable tli-infrastructure.service
sudo systemctl start tli-infrastructure.service
```

## Step 9: PostgreSQL Troubleshooting and Fixes

### 9.1 Common PostgreSQL Issues and Solutions

#### Issue 1: PostgreSQL Container Won't Start

**Symptoms:**

- Container fails to start
- No postgres container visible in `docker ps`

**Diagnosis and Solutions:**

```bash
# Check if PostgreSQL container exists
docker ps -a | grep postgres

# Check container logs for errors
docker logs tli-postgres

# Common fixes:

# 1. Port already in use
sudo netstat -tlnp | grep 5432
sudo lsof -i :5432
# Kill conflicting process or change port in docker-compose.yml

# 2. Data directory permissions issue
sudo chown -R 999:999 /opt/tli/data/postgres
sudo chmod 700 /opt/tli/data/postgres

# 3. Configuration file errors
sudo nano /opt/tli/config/postgres/postgresql.conf
# Check for syntax errors, especially around quotes and semicolons

# 4. Insufficient disk space
df -h /opt/tli/data/postgres
# Clean up or expand storage if needed

# 5. Remove corrupted container and restart
docker rm -f tli-postgres
cd /opt/tli/config/postgres
docker compose up -d
```

#### Issue 2: Cannot Connect to PostgreSQL from Server 2

**Symptoms:**

- `psql: could not connect to server: Connection refused`
- Connection timeouts from microservices

**Diagnosis and Solutions:**

```bash
# Test connectivity from Server 2
nc -z SERVER1_IP 5432
telnet SERVER1_IP 5432

# From Server 1, check PostgreSQL is responding
docker exec tli-postgres pg_isready -U postgres

# Solutions:

# 1. Check and fix firewall rules
sudo ufw status
sudo ufw allow from SERVER2_IP to any port 5432
sudo ufw reload

# 2. Verify PostgreSQL listen configuration
docker exec tli-postgres cat /etc/postgresql/postgresql.conf | grep listen_addresses
# Should show: listen_addresses = '*'

# 3. Check host-based authentication
docker exec tli-postgres cat /etc/postgresql/pg_hba.conf
# Should include: host all all SERVER2_IP/32 md5

# 4. Fix pg_hba.conf if needed
cat > /opt/tli/config/postgres/pg_hba.conf << 'EOF'
# TYPE  DATABASE        USER            ADDRESS                 METHOD
local   all             all                                     trust
host    all             all             127.0.0.1/32            md5
host    all             all             SERVER2_IP/32           md5
host    all             all             ADMIN_NETWORK/24        md5
host    all             all             0.0.0.0/0               reject
EOF

# 5. Restart PostgreSQL to apply changes
/opt/tli/manage-infrastructure.sh restart
```

#### Issue 3: Database Connection Pool Exhausted

**Symptoms:**

- `FATAL: remaining connection slots are reserved for non-replication superuser connections`
- Services unable to connect to database

**Diagnosis and Solutions:**

```bash
# Check current connections
docker exec tli-postgres psql -U postgres -c "SELECT count(*) FROM pg_stat_activity;"
docker exec tli-postgres psql -U postgres -c "SELECT * FROM pg_stat_activity WHERE state = 'active';"

# Solutions:

# 1. Increase max_connections in postgresql.conf
sudo nano /opt/tli/config/postgres/postgresql.conf
# Change: max_connections = 300  (from 200)

# 2. Kill idle connections
docker exec tli-postgres psql -U postgres -c "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE state = 'idle' AND state_change < now() - interval '5 minutes';"

# 3. Restart PostgreSQL
/opt/tli/manage-infrastructure.sh restart
```

#### Issue 4: TimescaleDB Extension Installation Fails

**Symptoms:**

- `ERROR: extension "timescaledb" is not available`
- Analytics services fail to start

**Solutions:**

```bash
# Use TimescaleDB image instead of standard PostgreSQL
# Update docker-compose.yml to use timescale/timescaledb:latest-pg15

# Or install TimescaleDB in existing container
docker exec -it tli-postgres bash
apt-get update
apt-get install -y postgresql-15-timescaledb-2.11
echo "shared_preload_libraries = 'timescaledb'" >> /etc/postgresql/postgresql.conf
exit

# Restart container
docker restart tli-postgres

# Re-run database setup
docker exec -i tli-postgres psql -U postgres < /opt/tli/config/postgres/setup-databases.sql
```

### 9.2 Enhanced Database Setup Script

```bash
# Create enhanced database setup with error handling
cat > /opt/tli/config/postgres/setup-databases-enhanced.sql << 'EOF'
-- Enhanced TLI Database Setup with Error Handling
\set ON_ERROR_STOP on

-- Create databases with proper encoding and collation
CREATE DATABASE "TLI_Identity" WITH ENCODING 'UTF8' LC_COLLATE='en_US.UTF-8' LC_CTYPE='en_US.UTF-8';
CREATE DATABASE "TLI_UserManagement" WITH ENCODING 'UTF8' LC_COLLATE='en_US.UTF-8' LC_CTYPE='en_US.UTF-8';
CREATE DATABASE "TLI_SubscriptionManagement" WITH ENCODING 'UTF8' LC_COLLATE='en_US.UTF-8' LC_CTYPE='en_US.UTF-8';
CREATE DATABASE "TLI_OrderManagement" WITH ENCODING 'UTF8' LC_COLLATE='en_US.UTF-8' LC_CTYPE='en_US.UTF-8';
CREATE DATABASE "TLI_TripManagement" WITH ENCODING 'UTF8' LC_COLLATE='en_US.UTF-8' LC_CTYPE='en_US.UTF-8';
CREATE DATABASE "TLI_NetworkFleetManagement" WITH ENCODING 'UTF8' LC_COLLATE='en_US.UTF-8' LC_CTYPE='en_US.UTF-8';
CREATE DATABASE "TLI_FinancialPayment" WITH ENCODING 'UTF8' LC_COLLATE='en_US.UTF-8' LC_CTYPE='en_US.UTF-8';
CREATE DATABASE "TLI_CommunicationNotification" WITH ENCODING 'UTF8' LC_COLLATE='en_US.UTF-8' LC_CTYPE='en_US.UTF-8';
CREATE DATABASE "TLI_AnalyticsBIService" WITH ENCODING 'UTF8' LC_COLLATE='en_US.UTF-8' LC_CTYPE='en_US.UTF-8';
CREATE DATABASE "TLI_DataStorage" WITH ENCODING 'UTF8' LC_COLLATE='en_US.UTF-8' LC_CTYPE='en_US.UTF-8';
CREATE DATABASE "TLI_AuditCompliance" WITH ENCODING 'UTF8' LC_COLLATE='en_US.UTF-8' LC_CTYPE='en_US.UTF-8';
CREATE DATABASE "TLI_MonitoringObservability" WITH ENCODING 'UTF8' LC_COLLATE='en_US.UTF-8' LC_CTYPE='en_US.UTF-8';
CREATE DATABASE "TLI_MobileWorkflow" WITH ENCODING 'UTF8' LC_COLLATE='en_US.UTF-8' LC_CTYPE='en_US.UTF-8';

-- Create application user with proper permissions
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'tli_admin') THEN
        CREATE USER tli_admin WITH PASSWORD 'TLI_Admin_2024!@#';
    END IF;
END
$$;

-- Grant privileges to all databases
GRANT ALL PRIVILEGES ON DATABASE "TLI_Identity" TO tli_admin;
GRANT ALL PRIVILEGES ON DATABASE "TLI_UserManagement" TO tli_admin;
GRANT ALL PRIVILEGES ON DATABASE "TLI_SubscriptionManagement" TO tli_admin;
GRANT ALL PRIVILEGES ON DATABASE "TLI_OrderManagement" TO tli_admin;
GRANT ALL PRIVILEGES ON DATABASE "TLI_TripManagement" TO tli_admin;
GRANT ALL PRIVILEGES ON DATABASE "TLI_NetworkFleetManagement" TO tli_admin;
GRANT ALL PRIVILEGES ON DATABASE "TLI_FinancialPayment" TO tli_admin;
GRANT ALL PRIVILEGES ON DATABASE "TLI_CommunicationNotification" TO tli_admin;
GRANT ALL PRIVILEGES ON DATABASE "TLI_AnalyticsBIService" TO tli_admin;
GRANT ALL PRIVILEGES ON DATABASE "TLI_DataStorage" TO tli_admin;
GRANT ALL PRIVILEGES ON DATABASE "TLI_AuditCompliance" TO tli_admin;
GRANT ALL PRIVILEGES ON DATABASE "TLI_MonitoringObservability" TO tli_admin;
GRANT ALL PRIVILEGES ON DATABASE "TLI_MobileWorkflow" TO tli_admin;

-- Install extensions with error handling
\c "TLI_AnalyticsBIService";
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
DO $$
BEGIN
    CREATE EXTENSION IF NOT EXISTS timescaledb;
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'TimescaleDB extension not available, skipping...';
END
$$;

\c "TLI_TripManagement";
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
DO $$
BEGIN
    CREATE EXTENSION IF NOT EXISTS timescaledb;
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'TimescaleDB extension not available, skipping...';
END
$$;

\c "TLI_MonitoringObservability";
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
DO $$
BEGIN
    CREATE EXTENSION IF NOT EXISTS timescaledb;
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'TimescaleDB extension not available, skipping...';
END
$$;

-- Grant schema permissions for each database
\c "TLI_Identity";
GRANT ALL ON SCHEMA public TO tli_admin;
\c "TLI_UserManagement";
GRANT ALL ON SCHEMA public TO tli_admin;
\c "TLI_SubscriptionManagement";
GRANT ALL ON SCHEMA public TO tli_admin;
\c "TLI_OrderManagement";
GRANT ALL ON SCHEMA public TO tli_admin;
\c "TLI_TripManagement";
GRANT ALL ON SCHEMA public TO tli_admin;
\c "TLI_NetworkFleetManagement";
GRANT ALL ON SCHEMA public TO tli_admin;
\c "TLI_FinancialPayment";
GRANT ALL ON SCHEMA public TO tli_admin;
\c "TLI_CommunicationNotification";
GRANT ALL ON SCHEMA public TO tli_admin;
\c "TLI_AnalyticsBIService";
GRANT ALL ON SCHEMA public TO tli_admin;
\c "TLI_DataStorage";
GRANT ALL ON SCHEMA public TO tli_admin;
\c "TLI_AuditCompliance";
GRANT ALL ON SCHEMA public TO tli_admin;
\c "TLI_MonitoringObservability";
GRANT ALL ON SCHEMA public TO tli_admin;
\c "TLI_MobileWorkflow";
GRANT ALL ON SCHEMA public TO tli_admin;

\echo 'Database setup completed successfully!'
EOF

# Execute enhanced database setup
docker exec -i tli-postgres psql -U postgres < /opt/tli/config/postgres/setup-databases-enhanced.sql

# Verify databases and users
docker exec tli-postgres psql -U postgres -c "\l"
docker exec tli-postgres psql -U postgres -c "\du"
```

### 9.3 Service Health Checks

```bash
# Create comprehensive health check script
cat > /opt/tli/health-check.sh << 'EOF'
#!/bin/bash

echo "=== TLI Infrastructure Health Check ==="
echo "Date: $(date)"
echo

# Check Docker
echo "1. Docker Status:"
systemctl is-active docker
echo

# Check PostgreSQL with detailed diagnostics
echo "2. PostgreSQL Status:"
docker exec tli-postgres pg_isready -U postgres
if [ $? -eq 0 ]; then
  echo "✓ PostgreSQL is healthy"

  # Check database connections
  echo "  - Active connections:"
  docker exec tli-postgres psql -U postgres -c "SELECT count(*) as active_connections FROM pg_stat_activity WHERE state = 'active';" -t

  # Check database sizes
  echo "  - Database sizes:"
  docker exec tli-postgres psql -U postgres -c "SELECT datname, pg_size_pretty(pg_database_size(datname)) as size FROM pg_database WHERE datname LIKE 'TLI_%' ORDER BY pg_database_size(datname) DESC;" -t

  # Test tli_admin user
  docker exec tli-postgres psql -U tli_admin -d TLI_Identity -c "SELECT 1;" > /dev/null 2>&1
  if [ $? -eq 0 ]; then
    echo "  ✓ tli_admin user can connect"
  else
    echo "  ✗ tli_admin user connection failed"
  fi
else
  echo "✗ PostgreSQL is not responding"
  echo "  - Container status:"
  docker ps | grep postgres
  echo "  - Recent logs:"
  docker logs tli-postgres --tail 10
fi
echo

# Check Redis
echo "3. Redis Status:"
docker exec tli-redis redis-cli -a "TLI_Redis_2024!@#" ping 2>/dev/null
if [ $? -eq 0 ]; then
  echo "✓ Redis is healthy"
  echo "  - Memory usage:"
  docker exec tli-redis redis-cli -a "TLI_Redis_2024!@#" info memory | grep used_memory_human
else
  echo "✗ Redis is not responding"
fi
echo

# Check RabbitMQ
echo "4. RabbitMQ Status:"
docker exec tli-rabbitmq rabbitmq-diagnostics ping 2>/dev/null
if [ $? -eq 0 ]; then
  echo "✓ RabbitMQ is healthy"
  echo "  - Queue status:"
  docker exec tli-rabbitmq rabbitmqctl list_queues 2>/dev/null | head -5
else
  echo "✗ RabbitMQ is not responding"
fi
echo

# Check Prometheus
echo "5. Prometheus Status:"
curl -s http://localhost:9090/-/healthy > /dev/null
if [ $? -eq 0 ]; then
  echo "✓ Prometheus is healthy"
else
  echo "✗ Prometheus is not responding"
fi
echo

# Check Grafana
echo "6. Grafana Status:"
curl -s http://localhost:3000/api/health > /dev/null
if [ $? -eq 0 ]; then
  echo "✓ Grafana is healthy"
else
  echo "✗ Grafana is not responding"
fi
echo

# Check disk space
echo "7. Disk Space:"
df -h /opt/tli/data | grep -v Filesystem
echo

echo "=== Health Check Complete ==="
EOF

chmod +x /opt/tli/health-check.sh

# Run initial health check
/opt/tli/health-check.sh
```

### 9.4 PostgreSQL Performance Optimization

```bash
# Create PostgreSQL performance monitoring script
cat > /opt/tli/postgres-performance.sh << 'EOF'
#!/bin/bash

echo "=== PostgreSQL Performance Analysis ==="
echo "Date: $(date)"
echo

# Check slow queries
echo "1. Slow Queries (>1 second):"
docker exec tli-postgres psql -U postgres -c "
SELECT query, calls, total_time, mean_time, rows
FROM pg_stat_statements
WHERE mean_time > 1000
ORDER BY mean_time DESC
LIMIT 10;" 2>/dev/null || echo "pg_stat_statements extension not enabled"
echo

# Check database sizes and growth
echo "2. Database Sizes:"
docker exec tli-postgres psql -U postgres -c "
SELECT
    datname as database_name,
    pg_size_pretty(pg_database_size(datname)) as size,
    pg_database_size(datname) as size_bytes
FROM pg_database
WHERE datname LIKE 'TLI_%'
ORDER BY pg_database_size(datname) DESC;"
echo

# Check connection statistics
echo "3. Connection Statistics:"
docker exec tli-postgres psql -U postgres -c "
SELECT
    datname,
    numbackends as active_connections,
    xact_commit as transactions_committed,
    xact_rollback as transactions_rolled_back,
    blks_read as blocks_read,
    blks_hit as blocks_hit,
    round((blks_hit::float/(blks_read + blks_hit + 1)) * 100, 2) as cache_hit_ratio
FROM pg_stat_database
WHERE datname LIKE 'TLI_%';"
echo

# Check table statistics for largest tables
echo "4. Largest Tables:"
docker exec tli-postgres psql -U postgres -c "
SELECT
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
    pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
FROM pg_tables
WHERE schemaname NOT IN ('information_schema', 'pg_catalog')
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
LIMIT 10;" 2>/dev/null || echo "No user tables found yet"
echo

# Check locks
echo "5. Current Locks:"
docker exec tli-postgres psql -U postgres -c "
SELECT
    datname,
    locktype,
    mode,
    granted,
    count(*)
FROM pg_locks l
JOIN pg_database d ON l.database = d.oid
WHERE datname LIKE 'TLI_%'
GROUP BY datname, locktype, mode, granted
ORDER BY count(*) DESC;"
echo

echo "=== Performance Analysis Complete ==="
EOF

chmod +x /opt/tli/postgres-performance.sh

# Create PostgreSQL optimization script
cat > /opt/tli/optimize-postgres.sh << 'EOF'
#!/bin/bash

echo "=== PostgreSQL Optimization Script ==="
echo "This script will apply performance optimizations to PostgreSQL"
echo "Make sure to backup your data before proceeding!"
echo

read -p "Do you want to continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Optimization cancelled."
    exit 1
fi

# Enable pg_stat_statements for query analysis
echo "1. Enabling pg_stat_statements extension..."
docker exec tli-postgres psql -U postgres -c "CREATE EXTENSION IF NOT EXISTS pg_stat_statements;" 2>/dev/null

# Update postgresql.conf with optimized settings
echo "2. Updating PostgreSQL configuration..."
cat > /opt/tli/config/postgres/postgresql-optimized.conf << 'PGCONF'
# PostgreSQL Optimized Configuration for TLI
listen_addresses = '*'
port = 5432

# Connection settings
max_connections = 300
superuser_reserved_connections = 3

# Memory settings (adjust based on available RAM)
shared_buffers = 512MB                    # 25% of RAM for 2GB system
effective_cache_size = 1536MB             # 75% of RAM for 2GB system
work_mem = 8MB                            # Increased for complex queries
maintenance_work_mem = 128MB              # For maintenance operations
wal_buffers = 32MB                        # Increased for better write performance

# Checkpoint settings
checkpoint_completion_target = 0.9
checkpoint_timeout = 10min
max_wal_size = 8GB                        # Increased for better performance
min_wal_size = 2GB

# Query planner settings
default_statistics_target = 100
random_page_cost = 1.1                    # For SSD storage
effective_io_concurrency = 200            # For SSD storage
seq_page_cost = 1.0

# Parallel query settings
max_worker_processes = 8
max_parallel_workers_per_gather = 4
max_parallel_workers = 8
max_parallel_maintenance_workers = 4

# Logging settings
log_destination = 'stderr'
logging_collector = on
log_directory = '/var/log/postgresql'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_rotation_age = 1d
log_rotation_size = 100MB
log_min_duration_statement = 1000         # Log slow queries
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
log_checkpoints = on
log_connections = on
log_disconnections = on
log_lock_waits = on
log_temp_files = 10MB

# Performance monitoring
shared_preload_libraries = 'pg_stat_statements'
pg_stat_statements.max = 10000
pg_stat_statements.track = all

# Security settings
ssl = on
ssl_cert_file = '/var/lib/postgresql/server.crt'
ssl_key_file = '/var/lib/postgresql/server.key'

# Autovacuum settings (important for performance)
autovacuum = on
autovacuum_max_workers = 3
autovacuum_naptime = 1min
autovacuum_vacuum_threshold = 50
autovacuum_analyze_threshold = 50
autovacuum_vacuum_scale_factor = 0.1
autovacuum_analyze_scale_factor = 0.05
autovacuum_vacuum_cost_delay = 10ms
autovacuum_vacuum_cost_limit = 1000

# Background writer settings
bgwriter_delay = 200ms
bgwriter_lru_maxpages = 100
bgwriter_lru_multiplier = 2.0
bgwriter_flush_after = 512kB

# WAL settings for better performance
wal_level = replica
wal_compression = on
wal_log_hints = on
archive_mode = off                        # Enable if you need point-in-time recovery

# Connection pooling preparation
tcp_keepalives_idle = 600
tcp_keepalives_interval = 30
tcp_keepalives_count = 3
PGCONF

# Backup current configuration
echo "3. Backing up current configuration..."
docker exec tli-postgres cp /etc/postgresql/postgresql.conf /etc/postgresql/postgresql.conf.backup

# Apply new configuration
echo "4. Applying optimized configuration..."
docker cp /opt/tli/config/postgres/postgresql-optimized.conf tli-postgres:/etc/postgresql/postgresql.conf

# Restart PostgreSQL to apply changes
echo "5. Restarting PostgreSQL..."
/opt/tli/manage-infrastructure.sh restart

# Wait for PostgreSQL to start
echo "6. Waiting for PostgreSQL to start..."
sleep 30

# Verify configuration
echo "7. Verifying configuration..."
docker exec tli-postgres psql -U postgres -c "SHOW shared_buffers;"
docker exec tli-postgres psql -U postgres -c "SHOW effective_cache_size;"
docker exec tli-postgres psql -U postgres -c "SHOW max_connections;"

echo "=== PostgreSQL Optimization Complete ==="
echo "Run /opt/tli/postgres-performance.sh to monitor performance"
EOF

chmod +x /opt/tli/optimize-postgres.sh
```

### 9.5 PostgreSQL Backup and Recovery Enhancements

```bash
# Create enhanced backup script with compression and retention
cat > /opt/tli/config/postgres/backup-databases-enhanced.sh << 'EOF'
#!/bin/bash

# Enhanced PostgreSQL Backup Script for TLI
BACKUP_DIR="/opt/tli/backups/postgres"
DATE=$(date +%Y%m%d_%H%M%S)
RETENTION_DAYS=30
LOG_FILE="/opt/tli/logs/postgres/backup.log"

# Database list
DATABASES=(
  "TLI_Identity"
  "TLI_UserManagement"
  "TLI_SubscriptionManagement"
  "TLI_OrderManagement"
  "TLI_TripManagement"
  "TLI_NetworkFleetManagement"
  "TLI_FinancialPayment"
  "TLI_CommunicationNotification"
  "TLI_AnalyticsBIService"
  "TLI_DataStorage"
  "TLI_AuditCompliance"
  "TLI_MonitoringObservability"
  "TLI_MobileWorkflow"
)

# Logging function
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# Create backup directory
mkdir -p "$BACKUP_DIR"
mkdir -p "$(dirname "$LOG_FILE")"

log "Starting PostgreSQL backup process"

# Check if PostgreSQL is running
if ! docker exec tli-postgres pg_isready -U postgres > /dev/null 2>&1; then
    log "ERROR: PostgreSQL is not running or not accessible"
    exit 1
fi

# Create full cluster backup
log "Creating full cluster backup..."
docker exec tli-postgres pg_dumpall -U postgres | gzip > "$BACKUP_DIR/full_cluster_${DATE}.sql.gz"
if [ $? -eq 0 ]; then
    log "Full cluster backup completed successfully"
else
    log "ERROR: Full cluster backup failed"
fi

# Create individual database backups
for db in "${DATABASES[@]}"; do
    log "Backing up database: $db"

    # Check if database exists
    if docker exec tli-postgres psql -U postgres -lqt | cut -d \| -f 1 | grep -qw "$db"; then
        # Create backup with custom format for better compression and faster restore
        docker exec tli-postgres pg_dump -U postgres -Fc "$db" > "$BACKUP_DIR/${db}_${DATE}.dump"

        if [ $? -eq 0 ]; then
            # Compress the backup
            gzip "$BACKUP_DIR/${db}_${DATE}.dump"
            log "Database $db backup completed successfully"
        else
            log "ERROR: Database $db backup failed"
        fi
    else
        log "WARNING: Database $db does not exist, skipping"
    fi
done

# Create backup metadata
cat > "$BACKUP_DIR/backup_${DATE}.info" << METADATA
Backup Date: $(date)
PostgreSQL Version: $(docker exec tli-postgres psql -U postgres -t -c "SELECT version();")
Backup Type: Full + Individual Databases
Databases: ${DATABASES[*]}
Backup Location: $BACKUP_DIR
METADATA

# Clean up old backups
log "Cleaning up backups older than $RETENTION_DAYS days"
find "$BACKUP_DIR" -name "*.gz" -mtime +$RETENTION_DAYS -delete
find "$BACKUP_DIR" -name "*.dump.gz" -mtime +$RETENTION_DAYS -delete
find "$BACKUP_DIR" -name "*.info" -mtime +$RETENTION_DAYS -delete

# Calculate backup sizes
TOTAL_SIZE=$(du -sh "$BACKUP_DIR" | cut -f1)
log "Backup process completed. Total backup size: $TOTAL_SIZE"

# Send notification (if notification service is available)
# curl -X POST "http://localhost:5008/api/notifications/send" \
#      -H "Content-Type: application/json" \
#      -d "{\"message\":\"PostgreSQL backup completed successfully. Size: $TOTAL_SIZE\",\"type\":\"info\"}" \
#      2>/dev/null || true

log "Backup process finished"
EOF

chmod +x /opt/tli/config/postgres/backup-databases-enhanced.sh

# Create database restore script
cat > /opt/tli/config/postgres/restore-database.sh << 'EOF'
#!/bin/bash

# PostgreSQL Database Restore Script
BACKUP_DIR="/opt/tli/backups/postgres"
LOG_FILE="/opt/tli/logs/postgres/restore.log"

# Function to display usage
usage() {
    echo "Usage: $0 <database_name> <backup_file>"
    echo "Example: $0 TLI_Identity TLI_Identity_20241201_120000.dump.gz"
    echo ""
    echo "Available databases:"
    echo "  TLI_Identity"
    echo "  TLI_UserManagement"
    echo "  TLI_SubscriptionManagement"
    echo "  TLI_OrderManagement"
    echo "  TLI_TripManagement"
    echo "  TLI_NetworkFleetManagement"
    echo "  TLI_FinancialPayment"
    echo "  TLI_CommunicationNotification"
    echo "  TLI_AnalyticsBIService"
    echo "  TLI_DataStorage"
    echo "  TLI_AuditCompliance"
    echo "  TLI_MonitoringObservability"
    echo "  TLI_MobileWorkflow"
    echo ""
    echo "Available backups:"
    ls -la "$BACKUP_DIR"/*.gz 2>/dev/null | tail -10
    exit 1
}

# Logging function
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# Check parameters
if [ $# -ne 2 ]; then
    usage
fi

DATABASE_NAME="$1"
BACKUP_FILE="$2"
BACKUP_PATH="$BACKUP_DIR/$BACKUP_FILE"

# Validate inputs
if [ ! -f "$BACKUP_PATH" ]; then
    echo "ERROR: Backup file not found: $BACKUP_PATH"
    exit 1
fi

# Create log directory
mkdir -p "$(dirname "$LOG_FILE")"

log "Starting database restore process"
log "Database: $DATABASE_NAME"
log "Backup file: $BACKUP_FILE"

# Confirmation prompt
echo "WARNING: This will drop and recreate the database '$DATABASE_NAME'"
echo "All existing data in this database will be lost!"
read -p "Are you sure you want to continue? (type 'yes' to confirm): " -r
if [ "$REPLY" != "yes" ]; then
    log "Restore cancelled by user"
    exit 1
fi

# Check if PostgreSQL is running
if ! docker exec tli-postgres pg_isready -U postgres > /dev/null 2>&1; then
    log "ERROR: PostgreSQL is not running or not accessible"
    exit 1
fi

# Drop existing database
log "Dropping existing database: $DATABASE_NAME"
docker exec tli-postgres psql -U postgres -c "DROP DATABASE IF EXISTS \"$DATABASE_NAME\";"

# Create new database
log "Creating new database: $DATABASE_NAME"
docker exec tli-postgres psql -U postgres -c "CREATE DATABASE \"$DATABASE_NAME\" WITH ENCODING 'UTF8' LC_COLLATE='en_US.UTF-8' LC_CTYPE='en_US.UTF-8';"

# Grant permissions
log "Granting permissions to tli_admin"
docker exec tli-postgres psql -U postgres -c "GRANT ALL PRIVILEGES ON DATABASE \"$DATABASE_NAME\" TO tli_admin;"

# Restore database
log "Restoring database from backup..."
if [[ "$BACKUP_FILE" == *.sql.gz ]]; then
    # SQL format backup
    gunzip -c "$BACKUP_PATH" | docker exec -i tli-postgres psql -U postgres -d "$DATABASE_NAME"
elif [[ "$BACKUP_FILE" == *.dump.gz ]]; then
    # Custom format backup
    gunzip -c "$BACKUP_PATH" | docker exec -i tli-postgres pg_restore -U postgres -d "$DATABASE_NAME" --verbose
else
    log "ERROR: Unsupported backup file format"
    exit 1
fi

if [ $? -eq 0 ]; then
    log "Database restore completed successfully"
else
    log "ERROR: Database restore failed"
    exit 1
fi

# Verify restore
log "Verifying database restore..."
TABLE_COUNT=$(docker exec tli-postgres psql -U postgres -d "$DATABASE_NAME" -t -c "SELECT count(*) FROM information_schema.tables WHERE table_schema = 'public';")
log "Restored database contains $TABLE_COUNT tables"

log "Database restore process completed"
EOF

chmod +x /opt/tli/config/postgres/restore-database.sh

# Update cron job for enhanced backup
(crontab -l 2>/dev/null | grep -v backup-databases.sh; echo "0 2 * * * /opt/tli/config/postgres/backup-databases-enhanced.sh") | crontab -
```

## Next Steps

1. **Configure IP Addresses**: Replace `SERVER2_IP`, `ADMIN_NETWORK`, and `MONITORING_NETWORK` with actual values
2. **Setup SSL Certificates**: Configure SSL for secure communication
3. **Test Connectivity**: Ensure Server 2 can connect to all infrastructure services
4. **Setup Monitoring**: Access Grafana at `http://SERVER1_IP:3000` (admin/TLI_Grafana_2024!@#)
5. **Proceed to Server 2 Setup**: Follow the Server 2 deployment guide

## Important Notes

### Security and Configuration

- All passwords are examples and should be changed in production
- Firewall rules need to be adjusted based on your network configuration
- Replace `SERVER2_IP`, `ADMIN_NETWORK`, and `MONITORING_NETWORK` with actual values
- Keep Docker images updated for security patches

### PostgreSQL Specific Notes

- **Data Directory Permissions**: Ensure `/opt/tli/data/postgres` has correct ownership (999:999) and permissions (700)
- **Connection Limits**: Default max_connections is set to 200, increase to 300 if needed using the optimization script
- **TimescaleDB**: If analytics services fail, ensure TimescaleDB extension is properly installed
- **Backup Strategy**: Enhanced backups are scheduled daily at 2 AM with 30-day retention
- **Performance Monitoring**: Use `/opt/tli/postgres-performance.sh` to monitor database performance
- **Configuration**: Use `/opt/tli/optimize-postgres.sh` to apply performance optimizations

### Monitoring and Maintenance

- Regular backups are scheduled daily at 2 AM using enhanced backup script
- Monitor disk space usage regularly, especially `/opt/tli/data/postgres`
- Use `/opt/tli/health-check.sh` for comprehensive infrastructure health monitoring
- Check PostgreSQL logs at `/opt/tli/logs/postgres/` for any issues
- Monitor connection pool usage to prevent exhaustion

### Troubleshooting Quick Reference

- **Container won't start**: Check permissions, port conflicts, disk space
- **Connection refused**: Verify firewall rules, pg_hba.conf, and listen_addresses
- **Connection pool exhausted**: Increase max_connections or kill idle connections
- **TimescaleDB issues**: Use TimescaleDB Docker image or install extension manually
- **Performance issues**: Run performance analysis and apply optimizations

### Recovery Procedures

- **Database corruption**: Use `/opt/tli/config/postgres/restore-database.sh` to restore from backup
- **Full system recovery**: Restore from full cluster backup in `/opt/tli/backups/postgres/`
- **Configuration rollback**: Restore from `/etc/postgresql/postgresql.conf.backup`
