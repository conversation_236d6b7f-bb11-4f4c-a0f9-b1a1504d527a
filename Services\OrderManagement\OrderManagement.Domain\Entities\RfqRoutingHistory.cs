using Shared.Domain.Common;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Domain.Entities;

/// <summary>
/// Entity to track detailed routing history for RFQs
/// </summary>
public class RfqRoutingHistory : BaseEntity
{
    public Guid RfqId { get; private set; }
    public RfqRoutingAction Action { get; private set; }
    public Guid? FromEntityId { get; private set; }
    public string? FromEntityName { get; private set; }
    public RfqRoutingEntityType? FromEntityType { get; private set; }
    public Guid? ToEntityId { get; private set; }
    public string? ToEntityName { get; private set; }
    public RfqRoutingEntityType? ToEntityType { get; private set; }
    public DateTime RoutedAt { get; private set; }
    public Guid? RoutedBy { get; private set; }
    public string? RoutedByName { get; private set; }
    public string? RoutingReason { get; private set; }
    public string? RoutingNotes { get; private set; }
    public RfqRoutingStatus Status { get; private set; }
    public DateTime? ResponseDeadline { get; private set; }
    public DateTime? RespondedAt { get; private set; }
    public string? ResponseNotes { get; private set; }
    public bool IsSuccessful { get; private set; }
    public string? FailureReason { get; private set; }
    public int SequenceNumber { get; private set; }

    // Navigation property
    public RFQ RFQ { get; private set; } = null!;

    private RfqRoutingHistory() { } // EF Constructor

    public RfqRoutingHistory(
        Guid rfqId,
        RfqRoutingAction action,
        Guid? toEntityId,
        string? toEntityName,
        RfqRoutingEntityType? toEntityType,
        Guid? routedBy = null,
        string? routedByName = null,
        string? routingReason = null,
        string? routingNotes = null,
        DateTime? responseDeadline = null,
        Guid? fromEntityId = null,
        string? fromEntityName = null,
        RfqRoutingEntityType? fromEntityType = null,
        int sequenceNumber = 1)
    {
        RfqId = rfqId;
        Action = action;
        FromEntityId = fromEntityId;
        FromEntityName = fromEntityName;
        FromEntityType = fromEntityType;
        ToEntityId = toEntityId;
        ToEntityName = toEntityName;
        ToEntityType = toEntityType;
        RoutedAt = DateTime.UtcNow;
        RoutedBy = routedBy;
        RoutedByName = routedByName;
        RoutingReason = routingReason;
        RoutingNotes = routingNotes;
        Status = RfqRoutingStatus.Pending;
        ResponseDeadline = responseDeadline;
        IsSuccessful = false;
        SequenceNumber = sequenceNumber;
    }

    public void MarkAsResponded(string? responseNotes = null)
    {
        RespondedAt = DateTime.UtcNow;
        ResponseNotes = responseNotes;
        Status = RfqRoutingStatus.Responded;
    }

    public void MarkAsSuccessful(string? responseNotes = null)
    {
        RespondedAt = DateTime.UtcNow;
        ResponseNotes = responseNotes;
        Status = RfqRoutingStatus.Completed;
        IsSuccessful = true;
    }

    public void MarkAsFailed(string failureReason, string? responseNotes = null)
    {
        RespondedAt = DateTime.UtcNow;
        ResponseNotes = responseNotes;
        Status = RfqRoutingStatus.Failed;
        IsSuccessful = false;
        FailureReason = failureReason;
    }

    public void MarkAsExpired()
    {
        Status = RfqRoutingStatus.Expired;
        IsSuccessful = false;
        FailureReason = "Response deadline exceeded";
    }

    public bool IsExpired => ResponseDeadline.HasValue && DateTime.UtcNow > ResponseDeadline.Value;

    public static RfqRoutingHistory CreateBrokerRouting(
        Guid rfqId,
        Guid brokerId,
        string brokerName,
        Guid? routedBy = null,
        string? routedByName = null,
        string? routingReason = null,
        string? routingNotes = null,
        DateTime? responseDeadline = null,
        int sequenceNumber = 1)
    {
        return new RfqRoutingHistory(
            rfqId,
            RfqRoutingAction.RouteToBroker,
            brokerId,
            brokerName,
            RfqRoutingEntityType.Broker,
            routedBy,
            routedByName,
            routingReason,
            routingNotes,
            responseDeadline,
            sequenceNumber: sequenceNumber);
    }

    public static RfqRoutingHistory CreateCarrierRouting(
        Guid rfqId,
        Guid carrierId,
        string carrierName,
        Guid? fromBrokerId = null,
        string? fromBrokerName = null,
        Guid? routedBy = null,
        string? routedByName = null,
        string? routingReason = null,
        string? routingNotes = null,
        DateTime? responseDeadline = null,
        int sequenceNumber = 1)
    {
        return new RfqRoutingHistory(
            rfqId,
            RfqRoutingAction.RouteToCarrier,
            carrierId,
            carrierName,
            RfqRoutingEntityType.Carrier,
            routedBy,
            routedByName,
            routingReason,
            routingNotes,
            responseDeadline,
            fromBrokerId,
            fromBrokerName,
            RfqRoutingEntityType.Broker,
            sequenceNumber);
    }
}
