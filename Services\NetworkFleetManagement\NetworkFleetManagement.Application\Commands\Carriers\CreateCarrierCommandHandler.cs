using AutoMapper;
using MediatR;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Entities;
using NetworkFleetManagement.Domain.Repositories;
using NetworkFleetManagement.Domain.ValueObjects;
using Shared.Domain.Common;
using Shared.Messaging;

namespace NetworkFleetManagement.Application.Commands.Carriers;

public class CreateCarrierCommandHandler : IRequestHandler<CreateCarrierCommand, CarrierDto>
{
    private readonly ICarrierRepository _carrierRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly IMessageBroker _messageBroker;

    public CreateCarrierCommandHandler(
        ICarrierRepository carrierRepository,
        IUnitOfWork unitOfWork,
        IMapper mapper,
        IMessageBroker messageBroker)
    {
        _carrierRepository = carrierRepository;
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _messageBroker = messageBroker;
    }

    public async Task<CarrierDto> Handle(CreateCarrierCommand request, CancellationToken cancellationToken)
    {
        var dto = request.CarrierDto;

        // Check if carrier already exists for this user
        var existingCarrier = await _carrierRepository.GetByUserIdAsync(dto.UserId, cancellationToken);
        if (existingCarrier != null)
        {
            throw new InvalidOperationException($"Carrier already exists for user {dto.UserId}");
        }

        // Create business address if provided
        Location? businessAddress = null;
        if (dto.BusinessAddress != null)
        {
            businessAddress = new Location(
                dto.BusinessAddress.Latitude,
                dto.BusinessAddress.Longitude,
                dto.BusinessAddress.Address,
                dto.BusinessAddress.City,
                dto.BusinessAddress.State,
                dto.BusinessAddress.Country,
                dto.BusinessAddress.PostalCode);
        }

        // Create carrier entity
        var carrier = new Carrier(
            dto.UserId,
            dto.CompanyName,
            dto.ContactPersonName,
            dto.Email,
            dto.PhoneNumber,
            dto.BusinessLicenseNumber,
            dto.TaxIdentificationNumber,
            businessAddress,
            dto.Notes);

        // Add to repository
        _carrierRepository.Add(carrier);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        // Publish integration event
        await _messageBroker.PublishAsync("carrier.created", new
        {
            CarrierId = carrier.Id,
            UserId = carrier.UserId,
            CompanyName = carrier.CompanyName,
            ContactPersonName = carrier.ContactPersonName,
            Email = carrier.Email,
            PhoneNumber = carrier.PhoneNumber,
            Status = carrier.Status.ToString(),
            OnboardingStatus = carrier.OnboardingStatus.ToString(),
            CreatedAt = carrier.CreatedAt
        }, cancellationToken);

        // Map to DTO and return
        var result = _mapper.Map<CarrierDto>(carrier);
        return result;
    }
}

