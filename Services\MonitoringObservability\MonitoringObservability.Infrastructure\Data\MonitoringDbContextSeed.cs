using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using MonitoringObservability.Domain.Entities;
using MonitoringObservability.Domain.Enums;

namespace MonitoringObservability.Infrastructure.Data;

public static class MonitoringDbContextSeed
{
    public static async Task SeedAsync(MonitoringDbContext context, ILogger logger)
    {
        try
        {
            if (!await context.Metrics.AnyAsync())
            {
                logger.LogInformation("Seeding Monitoring & Observability database...");

                // Sample user IDs (these would come from Identity service in real scenario)
                var adminUserId = Guid.Parse("11111111-1111-1111-1111-111111111111");
                var monitorUserId = Guid.Parse("*************-8888-8888-************");

                var services = new[] { "Identity", "UserManagement", "SubscriptionManagement", "OrderManagement", "TripManagement", "DataStorage" };

                var metrics = new List<Metric>();
                var healthChecks = new List<HealthCheck>();

                foreach (var serviceName in services)
                {
                    // Create standard metrics for each service
                    var serviceMetrics = new[]
                    {
                        Metric.Create(
                            name: "ResponseTime",
                            serviceName: serviceName,
                            description: $"Average response time for {serviceName} service",
                            type: MetricType.Gauge,
                            unit: "ms",
                            category: MonitoringCategory.Performance,
                            warningThreshold: 1000,
                            criticalThreshold: 5000,
                            thresholdOperator: ">",
                            evaluationWindow: TimeSpan.FromMinutes(5),
                            tags: new Dictionary<string, string>
                            {
                                { "service", serviceName.ToLower() },
                                { "metric_type", "performance" },
                                { "priority", "high" }
                            }
                        ),

                        Metric.Create(
                            name: "ErrorRate",
                            serviceName: serviceName,
                            description: $"Error rate percentage for {serviceName} service",
                            type: MetricType.Gauge,
                            unit: "%",
                            category: MonitoringCategory.Reliability,
                            warningThreshold: 5,
                            criticalThreshold: 20,
                            thresholdOperator: ">",
                            evaluationWindow: TimeSpan.FromMinutes(5),
                            tags: new Dictionary<string, string>
                            {
                                { "service", serviceName.ToLower() },
                                { "metric_type", "reliability" },
                                { "priority", "critical" }
                            }
                        ),

                        Metric.Create(
                            name: "Throughput",
                            serviceName: serviceName,
                            description: $"Requests per second for {serviceName} service",
                            type: MetricType.Counter,
                            unit: "req/s",
                            category: MonitoringCategory.Performance,
                            warningThreshold: 10,
                            criticalThreshold: 5,
                            thresholdOperator: "<",
                            evaluationWindow: TimeSpan.FromMinutes(5),
                            tags: new Dictionary<string, string>
                            {
                                { "service", serviceName.ToLower() },
                                { "metric_type", "throughput" },
                                { "priority", "medium" }
                            }
                        ),

                        Metric.Create(
                            name: "CpuUsage",
                            serviceName: serviceName,
                            description: $"CPU usage percentage for {serviceName} service",
                            type: MetricType.Gauge,
                            unit: "%",
                            category: MonitoringCategory.Infrastructure,
                            warningThreshold: 80,
                            criticalThreshold: 95,
                            thresholdOperator: ">",
                            evaluationWindow: TimeSpan.FromMinutes(5),
                            tags: new Dictionary<string, string>
                            {
                                { "service", serviceName.ToLower() },
                                { "metric_type", "resource" },
                                { "resource", "cpu" }
                            }
                        ),

                        Metric.Create(
                            name: "MemoryUsage",
                            serviceName: serviceName,
                            description: $"Memory usage percentage for {serviceName} service",
                            type: MetricType.Gauge,
                            unit: "%",
                            category: MonitoringCategory.Infrastructure,
                            warningThreshold: 85,
                            criticalThreshold: 95,
                            thresholdOperator: ">",
                            evaluationWindow: TimeSpan.FromMinutes(5),
                            tags: new Dictionary<string, string>
                            {
                                { "service", serviceName.ToLower() },
                                { "metric_type", "resource" },
                                { "resource", "memory" }
                            }
                        )
                    };

                    metrics.AddRange(serviceMetrics);

                    // Create health checks for each service
                    var servicePort = serviceName switch
                    {
                        "Identity" => 5001,
                        "UserManagement" => 5002,
                        "SubscriptionManagement" => 5003,
                        "OrderManagement" => 5004,
                        "TripManagement" => 5005,
                        "DataStorage" => 5010,
                        _ => 5000
                    };

                    var healthCheck = HealthCheck.Create(
                        name: $"{serviceName}HealthCheck",
                        serviceName: serviceName,
                        description: $"Health check for {serviceName} service",
                        endpoint: $"http://localhost:{servicePort}/health",
                        interval: TimeSpan.FromSeconds(30),
                        timeout: TimeSpan.FromSeconds(10),
                        maxRetries: 3,
                        headers: new Dictionary<string, string>
                        {
                            { "User-Agent", "TLI-HealthChecker/1.0" },
                            { "Accept", "application/json" }
                        },
                        tags: new Dictionary<string, string>
                        {
                            { "service", serviceName.ToLower() },
                            { "check_type", "http" },
                            { "environment", "development" }
                        }
                    );

                    healthChecks.Add(healthCheck);
                }

                // Add some sample incidents
                var incidents = new List<Incident>
                {
                    Incident.Create(
                        title: "High Response Time in Order Management",
                        description: "Order Management service is experiencing high response times above 5 seconds",
                        severity: IncidentSeverity.High,
                        serviceName: "OrderManagement",
                        createdByUserId: monitorUserId,
                        createdByUserName: "Monitor System",
                        component: "API Gateway",
                        impactLevel: 4,
                        urgencyLevel: 4,
                        impactDescription: "Users experiencing slow order processing",
                        tags: new Dictionary<string, string>
                        {
                            { "category", "performance" },
                            { "auto_created", "true" },
                            { "source", "metric_threshold" }
                        }
                    ),

                    Incident.Create(
                        title: "Database Connection Issues",
                        description: "Intermittent database connection failures affecting multiple services",
                        severity: IncidentSeverity.Critical,
                        serviceName: "DataStorage",
                        createdByUserId: adminUserId,
                        createdByUserName: "System Admin",
                        component: "Database",
                        impactLevel: 5,
                        urgencyLevel: 5,
                        impactDescription: "Multiple services unable to access database",
                        tags: new Dictionary<string, string>
                        {
                            { "category", "infrastructure" },
                            { "affected_services", "multiple" },
                            { "priority", "p1" }
                        }
                    )
                };

                // Resolve the second incident
                incidents[1].Resolve(
                    resolutionSummary: "Database connection pool was exhausted. Increased pool size and restarted services.",
                    rootCause: "Database connection pool configuration was insufficient for current load",
                    preventiveMeasures: "Implemented connection pool monitoring and auto-scaling",
                    resolvedByUserId: adminUserId,
                    resolvedByUserName: "System Admin"
                );

                // Add some sample alerts
                var alerts = new List<Alert>
                {
                    Alert.Create(
                        title: "High CPU Usage - Identity Service",
                        description: "CPU usage has exceeded 85% for the past 10 minutes",
                        severity: AlertSeverity.Warning,
                        source: "MetricThreshold",
                        serviceName: "Identity",
                        metricName: "CpuUsage",
                        currentValue: 87.5,
                        warningThreshold: 80,
                        criticalThreshold: 95,
                        thresholdOperator: ">",
                        tags: new Dictionary<string, string>
                        {
                            { "resource", "cpu" },
                            { "threshold_type", "warning" },
                            { "auto_generated", "true" }
                        }
                    ),

                    Alert.Create(
                        title: "Service Unavailable - UserManagement",
                        description: "UserManagement service health check is failing",
                        severity: AlertSeverity.Critical,
                        source: "HealthCheck",
                        serviceName: "UserManagement",
                        tags: new Dictionary<string, string>
                        {
                            { "check_type", "availability" },
                            { "consecutive_failures", "3" },
                            { "escalation_required", "true" }
                        }
                    )
                };

                // Acknowledge the first alert
                alerts[0].Acknowledge(
                    acknowledgedByUserId: monitorUserId,
                    acknowledgedByUserName: "Monitor User"
                );

                // Resolve the second alert
                alerts[1].Resolve(
                    resolvedByUserId: adminUserId,
                    resolvedByUserName: "System Admin",
                    resolutionNotes: "Service was restarted and is now responding normally"
                );

                // Add data to context
                await context.Metrics.AddRangeAsync(metrics);
                await context.HealthChecks.AddRangeAsync(healthChecks);
                await context.Incidents.AddRangeAsync(incidents);
                await context.Alerts.AddRangeAsync(alerts);

                // Add some sample metric data points for the last hour
                var dataPoints = new List<MetricDataPoint>();
                var now = DateTime.UtcNow;

                foreach (var metric in metrics.Take(10)) // Only for first 10 metrics to avoid too much data
                {
                    for (int i = 60; i >= 0; i -= 5) // Every 5 minutes for the last hour
                    {
                        var timestamp = now.AddMinutes(-i);
                        var value = metric.Name switch
                        {
                            "ResponseTime" => Random.Shared.NextDouble() * 2000 + 200, // 200-2200ms
                            "ErrorRate" => Random.Shared.NextDouble() * 10, // 0-10%
                            "Throughput" => Random.Shared.NextDouble() * 50 + 10, // 10-60 req/s
                            "CpuUsage" => Random.Shared.NextDouble() * 40 + 30, // 30-70%
                            "MemoryUsage" => Random.Shared.NextDouble() * 30 + 40, // 40-70%
                            _ => Random.Shared.NextDouble() * 100
                        };

                        var dataPoint = MetricDataPoint.Create(
                            metricId: metric.Id,
                            value: value,
                            timestamp: timestamp,
                            tags: new Dictionary<string, string>
                            {
                                { "source", "sample_data" },
                                { "environment", "development" }
                            }
                        );

                        dataPoints.Add(dataPoint);
                    }
                }

                await context.MetricDataPoints.AddRangeAsync(dataPoints);

                await context.SaveChangesAsync();

                logger.LogInformation("Monitoring & Observability database seeded successfully with {MetricCount} metrics, {HealthCheckCount} health checks, {IncidentCount} incidents, {AlertCount} alerts, and {DataPointCount} data points", 
                    metrics.Count, healthChecks.Count, incidents.Count, alerts.Count, dataPoints.Count);
            }
            else
            {
                logger.LogInformation("Monitoring & Observability database already contains data, skipping seed");
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while seeding the Monitoring & Observability database");
            throw;
        }
    }
}
