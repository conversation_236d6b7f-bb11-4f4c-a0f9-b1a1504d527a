namespace MonitoringObservability.Domain.DTOs;

/// <summary>
/// DTO for SLA compliance information
/// </summary>
public class SLAComplianceDto
{
    public Guid SlaId { get; set; }
    public string SlaName { get; set; } = string.Empty;
    public string ServiceName { get; set; } = string.Empty;
    public SLAComplianceStatus Status { get; set; } = new();
    public List<SLAMetricComplianceDto> Metrics { get; set; } = new();
    public SLAViolationSummary Violations { get; set; } = new();
    public DateTime CompliancePeriodStart { get; set; }
    public DateTime CompliancePeriodEnd { get; set; }
    public DateTime LastChecked { get; set; }
}

public class SLAComplianceStatus
{
    public bool IsCompliant { get; set; }
    public double CompliancePercentage { get; set; }
    public string Status { get; set; } = string.Empty;
    public int DaysInCompliance { get; set; }
    public int TotalDays { get; set; }
    public string? NonComplianceReason { get; set; }
}

public class SLAMetricComplianceDto
{
    public string MetricName { get; set; } = string.Empty;
    public string MetricType { get; set; } = string.Empty;
    public double TargetValue { get; set; }
    public double ActualValue { get; set; }
    public string Unit { get; set; } = string.Empty;
    public bool IsCompliant { get; set; }
    public double CompliancePercentage { get; set; }
    public List<SLAViolationDto> Violations { get; set; } = new();
}

public class SLAViolationSummary
{
    public int TotalViolations { get; set; }
    public int CriticalViolations { get; set; }
    public int WarningViolations { get; set; }
    public TimeSpan TotalDowntime { get; set; }
    public DateTime? LastViolation { get; set; }
    public string? MostFrequentViolationType { get; set; }
}

public class SLAViolationDto
{
    public Guid ViolationId { get; set; }
    public string ViolationType { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public TimeSpan Duration { get; set; }
    public string Description { get; set; } = string.Empty;
    public Dictionary<string, object> Context { get; set; } = new();
}
