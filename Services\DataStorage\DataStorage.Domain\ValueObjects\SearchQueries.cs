using DataStorage.Domain.Enums;

namespace DataStorage.Domain.ValueObjects;

public class FacetedSearchQuery : SearchQuery
{
    public List<FacetRequest> FacetRequests { get; private set; }
    public int MaxFacetValues { get; private set; }

    public FacetedSearchQuery(
        string query,
        List<FacetRequest> facetRequests,
        List<SearchField>? fields = null,
        List<SearchFilter>? filters = null,
        SearchSort? sort = null,
        int pageNumber = 1,
        int pageSize = 20,
        int maxFacetValues = 10,
        Guid? requestingUserId = null)
        : base(query, fields, filters, sort, pageNumber, pageSize, false, true, requestingUserId)
    {
        FacetRequests = facetRequests ?? throw new ArgumentNullException(nameof(facetRequests));
        MaxFacetValues = Math.Max(1, Math.Min(100, maxFacetValues));
    }
}

public class BooleanSearchQuery
{
    public List<BooleanClause> Clauses { get; private set; }
    public List<SearchFilter> Filters { get; private set; }
    public SearchSort Sort { get; private set; }
    public int PageNumber { get; private set; }
    public int PageSize { get; private set; }
    public Guid? RequestingUserId { get; private set; }

    public BooleanSearchQuery(
        List<BooleanClause> clauses,
        List<SearchFilter>? filters = null,
        SearchSort? sort = null,
        int pageNumber = 1,
        int pageSize = 20,
        Guid? requestingUserId = null)
    {
        Clauses = clauses ?? throw new ArgumentNullException(nameof(clauses));
        Filters = filters ?? new List<SearchFilter>();
        Sort = sort ?? SearchSort.Relevance;
        PageNumber = Math.Max(1, pageNumber);
        PageSize = Math.Max(1, Math.Min(100, pageSize));
        RequestingUserId = requestingUserId;
    }
}

public class FuzzySearchQuery : SearchQuery
{
    public int MaxEdits { get; private set; }
    public int PrefixLength { get; private set; }
    public bool Transpositions { get; private set; }

    public FuzzySearchQuery(
        string query,
        int maxEdits = 2,
        int prefixLength = 0,
        bool transpositions = true,
        List<SearchField>? fields = null,
        List<SearchFilter>? filters = null,
        SearchSort? sort = null,
        int pageNumber = 1,
        int pageSize = 20,
        Guid? requestingUserId = null)
        : base(query, fields, filters, sort, pageNumber, pageSize, false, false, requestingUserId)
    {
        MaxEdits = Math.Max(0, Math.Min(2, maxEdits));
        PrefixLength = Math.Max(0, prefixLength);
        Transpositions = transpositions;
    }
}

public class RangeSearchQuery
{
    public string Field { get; private set; }
    public object? From { get; private set; }
    public object? To { get; private set; }
    public bool IncludeFrom { get; private set; }
    public bool IncludeTo { get; private set; }
    public List<SearchFilter> Filters { get; private set; }
    public SearchSort Sort { get; private set; }
    public int PageNumber { get; private set; }
    public int PageSize { get; private set; }
    public Guid? RequestingUserId { get; private set; }

    public RangeSearchQuery(
        string field,
        object? from = null,
        object? to = null,
        bool includeFrom = true,
        bool includeTo = true,
        List<SearchFilter>? filters = null,
        SearchSort? sort = null,
        int pageNumber = 1,
        int pageSize = 20,
        Guid? requestingUserId = null)
    {
        Field = field ?? throw new ArgumentNullException(nameof(field));
        From = from;
        To = to;
        IncludeFrom = includeFrom;
        IncludeTo = includeTo;
        Filters = filters ?? new List<SearchFilter>();
        Sort = sort ?? SearchSort.Relevance;
        PageNumber = Math.Max(1, pageNumber);
        PageSize = Math.Max(1, Math.Min(100, pageSize));
        RequestingUserId = requestingUserId;
    }
}

public class GeoSearchQuery
{
    public GeoLocation CenterPoint { get; private set; }
    public double RadiusKm { get; private set; }
    public string? Query { get; private set; }
    public List<SearchFilter> Filters { get; private set; }
    public GeoSortMode SortMode { get; private set; }
    public int PageNumber { get; private set; }
    public int PageSize { get; private set; }
    public Guid? RequestingUserId { get; private set; }

    public GeoSearchQuery(
        GeoLocation centerPoint,
        double radiusKm,
        string? query = null,
        List<SearchFilter>? filters = null,
        GeoSortMode sortMode = GeoSortMode.Distance,
        int pageNumber = 1,
        int pageSize = 20,
        Guid? requestingUserId = null)
    {
        CenterPoint = centerPoint ?? throw new ArgumentNullException(nameof(centerPoint));
        RadiusKm = Math.Max(0.1, radiusKm);
        Query = query;
        Filters = filters ?? new List<SearchFilter>();
        SortMode = sortMode;
        PageNumber = Math.Max(1, pageNumber);
        PageSize = Math.Max(1, Math.Min(100, pageSize));
        RequestingUserId = requestingUserId;
    }
}

public class BooleanClause
{
    public string Query { get; private set; }
    public List<SearchField> Fields { get; private set; }
    public BooleanOperator Operator { get; private set; }
    public double Boost { get; private set; }

    public BooleanClause(
        string query,
        BooleanOperator @operator,
        List<SearchField>? fields = null,
        double boost = 1.0)
    {
        Query = query ?? throw new ArgumentNullException(nameof(query));
        Operator = @operator;
        Fields = fields ?? new List<SearchField> { SearchField.All };
        Boost = Math.Max(0.1, boost);
    }
}

public class SearchFilter
{
    public string Field { get; private set; }
    public FilterOperator Operator { get; private set; }
    public object Value { get; private set; }
    public List<object>? Values { get; private set; }

    public SearchFilter(string field, FilterOperator @operator, object value)
    {
        Field = field ?? throw new ArgumentNullException(nameof(field));
        Operator = @operator;
        Value = value ?? throw new ArgumentNullException(nameof(value));
    }

    public SearchFilter(string field, FilterOperator @operator, List<object> values)
    {
        Field = field ?? throw new ArgumentNullException(nameof(field));
        Operator = @operator;
        Value = values?.FirstOrDefault() ?? throw new ArgumentNullException(nameof(values));
        Values = values;
    }
}

public class FacetRequest
{
    public string Field { get; private set; }
    public string? DisplayName { get; private set; }
    public FacetType Type { get; private set; }
    public int MaxValues { get; private set; }
    public string? Filter { get; private set; }

    public FacetRequest(
        string field,
        FacetType type,
        string? displayName = null,
        int maxValues = 10,
        string? filter = null)
    {
        Field = field ?? throw new ArgumentNullException(nameof(field));
        Type = type;
        DisplayName = displayName ?? field;
        MaxValues = Math.Max(1, Math.Min(100, maxValues));
        Filter = filter;
    }
}

public class FacetedSearchResult : SearchResult
{
    public List<SearchFacet> Facets { get; private set; }

    public FacetedSearchResult(
        List<SearchHit> hits,
        long totalHits,
        double maxScore,
        TimeSpan searchTime,
        int pageNumber,
        int pageSize,
        List<SearchFacet> facets,
        SearchAggregations? aggregations = null)
        : base(hits, totalHits, maxScore, searchTime, pageNumber, pageSize, facets, aggregations)
    {
        Facets = facets ?? new List<SearchFacet>();
    }
}

public class SearchFacet
{
    public string Field { get; private set; }
    public string DisplayName { get; private set; }
    public FacetType Type { get; private set; }
    public List<FacetValue> Values { get; private set; }
    public long TotalValues { get; private set; }

    public SearchFacet(
        string field,
        string displayName,
        FacetType type,
        List<FacetValue> values,
        long totalValues)
    {
        Field = field ?? throw new ArgumentNullException(nameof(field));
        DisplayName = displayName ?? throw new ArgumentNullException(nameof(displayName));
        Type = type;
        Values = values ?? new List<FacetValue>();
        TotalValues = totalValues;
    }
}

public class FacetValue
{
    public object Value { get; private set; }
    public string DisplayValue { get; private set; }
    public long Count { get; private set; }
    public bool IsSelected { get; private set; }

    public FacetValue(object value, long count, bool isSelected = false, string? displayValue = null)
    {
        Value = value ?? throw new ArgumentNullException(nameof(value));
        DisplayValue = displayValue ?? value.ToString() ?? string.Empty;
        Count = count;
        IsSelected = isSelected;
    }
}

public class FacetValues
{
    public string FacetName { get; private set; }
    public List<FacetValue> Values { get; private set; }
    public long TotalValues { get; private set; }

    public FacetValues(string facetName, List<FacetValue> values, long totalValues)
    {
        FacetName = facetName ?? throw new ArgumentNullException(nameof(facetName));
        Values = values ?? new List<FacetValue>();
        TotalValues = totalValues;
    }
}

public class HighlightOptions
{
    public List<string> Fields { get; private set; }
    public string PreTag { get; private set; }
    public string PostTag { get; private set; }
    public int FragmentSize { get; private set; }
    public int MaxFragments { get; private set; }

    public HighlightOptions(
        List<string>? fields = null,
        string preTag = "<mark>",
        string postTag = "</mark>",
        int fragmentSize = 150,
        int maxFragments = 3)
    {
        Fields = fields ?? new List<string> { "title", "content", "summary" };
        PreTag = preTag ?? throw new ArgumentNullException(nameof(preTag));
        PostTag = postTag ?? throw new ArgumentNullException(nameof(postTag));
        FragmentSize = Math.Max(50, Math.Min(500, fragmentSize));
        MaxFragments = Math.Max(1, Math.Min(10, maxFragments));
    }
}

public class SearchAggregations
{
    public Dictionary<string, object> Aggregations { get; private set; }

    public SearchAggregations(Dictionary<string, object>? aggregations = null)
    {
        Aggregations = aggregations ?? new Dictionary<string, object>();
    }

    public T? GetAggregation<T>(string name) where T : class
    {
        return Aggregations.TryGetValue(name, out var value) ? value as T : null;
    }
}
