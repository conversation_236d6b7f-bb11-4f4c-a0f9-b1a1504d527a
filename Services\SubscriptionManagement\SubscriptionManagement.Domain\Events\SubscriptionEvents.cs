using Shared.Domain.Common;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Domain.Events
{
    public class SubscriptionCreatedEvent : IDomainEvent
    {
        public Guid Id { get; }
        public Guid SubscriptionId { get; }
        public Guid UserId { get; }
        public Guid PlanId { get; }
        public DateTime StartDate { get; }
        public DateTime NextBillingDate { get; }
        public decimal Amount { get; }
        public string Currency { get; }
        public DateTime OccurredOn { get; }

        public SubscriptionCreatedEvent(Guid subscriptionId, Guid userId, Guid planId,
            DateTime startDate, DateTime nextBillingDate, decimal amount, string currency)
        {
            Id = Guid.NewGuid();
            SubscriptionId = subscriptionId;
            UserId = userId;
            PlanId = planId;
            StartDate = startDate;
            NextBillingDate = nextBillingDate;
            Amount = amount;
            Currency = currency;
            OccurredOn = DateTime.UtcNow;
        }
    }

    public class SubscriptionActivatedEvent : IDomainEvent
    {
        public Guid Id { get; }
        public Guid SubscriptionId { get; }
        public Guid UserId { get; }
        public DateTime ActivatedAt { get; }
        public DateTime OccurredOn { get; }

        public SubscriptionActivatedEvent(Guid subscriptionId, Guid userId, DateTime activatedAt)
        {
            Id = Guid.NewGuid();
            SubscriptionId = subscriptionId;
            UserId = userId;
            ActivatedAt = activatedAt;
            OccurredOn = DateTime.UtcNow;
        }
    }

    public class SubscriptionCancelledEvent : IDomainEvent
    {
        public Guid Id { get; }
        public Guid SubscriptionId { get; }
        public Guid UserId { get; }
        public DateTime CancelledAt { get; }
        public string? CancellationReason { get; }
        public DateTime OccurredOn { get; }

        public SubscriptionCancelledEvent(Guid subscriptionId, Guid userId, DateTime cancelledAt, string? cancellationReason = null)
        {
            Id = Guid.NewGuid();
            SubscriptionId = subscriptionId;
            UserId = userId;
            CancelledAt = cancelledAt;
            CancellationReason = cancellationReason;
            OccurredOn = DateTime.UtcNow;
        }
    }

    public class SubscriptionUpgradedEvent : IDomainEvent
    {
        public Guid Id { get; }
        public Guid SubscriptionId { get; }
        public Guid UserId { get; }
        public Guid OldPlanId { get; }
        public Guid NewPlanId { get; }
        public DateTime UpgradedAt { get; }
        public decimal ProrationAmount { get; }
        public string Currency { get; }
        public DateTime OccurredOn { get; }

        public SubscriptionUpgradedEvent(Guid subscriptionId, Guid userId, Guid oldPlanId, Guid newPlanId,
            DateTime upgradedAt, decimal prorationAmount, string currency)
        {
            Id = Guid.NewGuid();
            SubscriptionId = subscriptionId;
            UserId = userId;
            OldPlanId = oldPlanId;
            NewPlanId = newPlanId;
            UpgradedAt = upgradedAt;
            ProrationAmount = prorationAmount;
            Currency = currency;
            OccurredOn = DateTime.UtcNow;
        }
    }

    public class SubscriptionDowngradedEvent : IDomainEvent
    {
        public Guid Id { get; }
        public Guid SubscriptionId { get; }
        public Guid UserId { get; }
        public Guid OldPlanId { get; }
        public Guid NewPlanId { get; }
        public DateTime DowngradedAt { get; }
        public decimal RefundAmount { get; }
        public string Currency { get; }
        public DateTime OccurredOn { get; }

        public SubscriptionDowngradedEvent(Guid subscriptionId, Guid userId, Guid oldPlanId, Guid newPlanId,
            DateTime downgradedAt, decimal refundAmount, string currency)
        {
            Id = Guid.NewGuid();
            SubscriptionId = subscriptionId;
            UserId = userId;
            OldPlanId = oldPlanId;
            NewPlanId = newPlanId;
            DowngradedAt = downgradedAt;
            RefundAmount = refundAmount;
            Currency = currency;
            OccurredOn = DateTime.UtcNow;
        }
    }

    public class SubscriptionRenewedEvent : IDomainEvent
    {
        public Guid Id { get; }
        public Guid SubscriptionId { get; }
        public Guid UserId { get; }
        public DateTime RenewedAt { get; }
        public DateTime NextBillingDate { get; }
        public decimal Amount { get; }
        public string Currency { get; }
        public DateTime OccurredOn { get; }

        public SubscriptionRenewedEvent(Guid subscriptionId, Guid userId, DateTime renewedAt,
            DateTime nextBillingDate, decimal amount, string currency)
        {
            Id = Guid.NewGuid();
            SubscriptionId = subscriptionId;
            UserId = userId;
            RenewedAt = renewedAt;
            NextBillingDate = nextBillingDate;
            Amount = amount;
            Currency = currency;
            OccurredOn = DateTime.UtcNow;
        }
    }

    public class PaymentProcessedEvent : IDomainEvent
    {
        public Guid Id { get; }
        public Guid PaymentId { get; }
        public Guid SubscriptionId { get; }
        public Guid UserId { get; }
        public decimal Amount { get; }
        public string Currency { get; }
        public PaymentStatus Status { get; }
        public string? PaymentGatewayTransactionId { get; }
        public DateTime ProcessedAt { get; }
        public DateTime OccurredOn { get; }

        public PaymentProcessedEvent(Guid paymentId, Guid subscriptionId, Guid userId,
            decimal amount, string currency, PaymentStatus status,
            string? paymentGatewayTransactionId, DateTime processedAt)
        {
            Id = Guid.NewGuid();
            PaymentId = paymentId;
            SubscriptionId = subscriptionId;
            UserId = userId;
            Amount = amount;
            Currency = currency;
            Status = status;
            PaymentGatewayTransactionId = paymentGatewayTransactionId;
            ProcessedAt = processedAt;
            OccurredOn = DateTime.UtcNow;
        }
    }

    public class SubscriptionExtendedEvent : IDomainEvent
    {
        public Guid Id { get; }
        public Guid SubscriptionId { get; }
        public Guid UserId { get; }
        public int ExtensionDays { get; }
        public string? Reason { get; }
        public Guid ExtendedByUserId { get; }
        public bool ApplyAsGracePeriod { get; }
        public DateTime? OriginalEndDate { get; }
        public DateTime OriginalNextBillingDate { get; }
        public DateTime? OriginalGracePeriodEndDate { get; }
        public DateTime? NewEndDate { get; }
        public DateTime NewNextBillingDate { get; }
        public DateTime? NewGracePeriodEndDate { get; }
        public DateTime OccurredOn { get; }

        public SubscriptionExtendedEvent(
            Guid subscriptionId,
            Guid userId,
            int extensionDays,
            string? reason,
            Guid extendedByUserId,
            bool applyAsGracePeriod,
            DateTime? originalEndDate,
            DateTime originalNextBillingDate,
            DateTime? originalGracePeriodEndDate,
            DateTime? newEndDate,
            DateTime newNextBillingDate,
            DateTime? newGracePeriodEndDate,
            DateTime occurredOn)
        {
            Id = Guid.NewGuid();
            SubscriptionId = subscriptionId;
            UserId = userId;
            ExtensionDays = extensionDays;
            Reason = reason;
            ExtendedByUserId = extendedByUserId;
            ApplyAsGracePeriod = applyAsGracePeriod;
            OriginalEndDate = originalEndDate;
            OriginalNextBillingDate = originalNextBillingDate;
            OriginalGracePeriodEndDate = originalGracePeriodEndDate;
            NewEndDate = newEndDate;
            NewNextBillingDate = newNextBillingDate;
            NewGracePeriodEndDate = newGracePeriodEndDate;
            OccurredOn = occurredOn;
        }
    }

    public class PlanCreatedEvent : IDomainEvent
    {
        public Guid Id { get; }
        public Guid PlanId { get; }
        public string Name { get; }
        public PlanType Type { get; }
        public UserType UserType { get; }
        public decimal Price { get; }
        public string Currency { get; }
        public DateTime OccurredOn { get; }

        public PlanCreatedEvent(Guid planId, string name, PlanType type, UserType userType, decimal price, string currency)
        {
            Id = Guid.NewGuid();
            PlanId = planId;
            Name = name;
            Type = type;
            UserType = userType;
            Price = price;
            Currency = currency;
            OccurredOn = DateTime.UtcNow;
        }
    }

    public class PaymentProofSubmittedEvent : IDomainEvent
    {
        public Guid Id { get; }
        public Guid PaymentProofId { get; }
        public Guid SubscriptionId { get; }
        public Guid UserId { get; }
        public decimal Amount { get; }
        public string Currency { get; }
        public DateTime PaymentDate { get; }
        public DateTime OccurredOn { get; }

        public PaymentProofSubmittedEvent(Guid paymentProofId, Guid subscriptionId, Guid userId,
            decimal amount, string currency, DateTime paymentDate)
        {
            Id = Guid.NewGuid();
            PaymentProofId = paymentProofId;
            SubscriptionId = subscriptionId;
            UserId = userId;
            Amount = amount;
            Currency = currency;
            PaymentDate = paymentDate;
            OccurredOn = DateTime.UtcNow;
        }
    }





    public class PaymentProofAdditionalInfoRequestedEvent : IDomainEvent
    {
        public Guid Id { get; }
        public Guid PaymentProofId { get; }
        public Guid SubscriptionId { get; }
        public Guid UserId { get; }
        public Guid RequestedByUserId { get; }
        public string RequestReason { get; }
        public DateTime RequestedAt { get; }
        public DateTime OccurredOn { get; }

        public PaymentProofAdditionalInfoRequestedEvent(Guid paymentProofId, Guid subscriptionId, Guid userId,
            Guid requestedByUserId, string requestReason, DateTime requestedAt)
        {
            Id = Guid.NewGuid();
            PaymentProofId = paymentProofId;
            SubscriptionId = subscriptionId;
            UserId = userId;
            RequestedByUserId = requestedByUserId;
            RequestReason = requestReason;
            RequestedAt = requestedAt;
            OccurredOn = DateTime.UtcNow;
        }
    }

    public class ManualNotificationTriggeredEvent : IDomainEvent
    {
        public Guid Id { get; }
        public Guid? SubscriptionId { get; }
        public Guid? UserId { get; }
        public NotificationType NotificationType { get; }
        public string? CustomMessage { get; }
        public List<string> Channels { get; }
        public Guid TriggeredByUserId { get; }
        public DateTime? ScheduledAt { get; }
        public DateTime OccurredOn { get; }

        public ManualNotificationTriggeredEvent(Guid? subscriptionId, Guid? userId, NotificationType notificationType,
            string? customMessage, List<string> channels, Guid triggeredByUserId, DateTime? scheduledAt = null)
        {
            Id = Guid.NewGuid();
            SubscriptionId = subscriptionId;
            UserId = userId;
            NotificationType = notificationType;
            CustomMessage = customMessage;
            Channels = channels;
            TriggeredByUserId = triggeredByUserId;
            ScheduledAt = scheduledAt;
            OccurredOn = DateTime.UtcNow;
        }
    }
}


