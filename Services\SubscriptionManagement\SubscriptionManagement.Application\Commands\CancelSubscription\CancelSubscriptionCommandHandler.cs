using MediatR;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Exceptions;
using SubscriptionManagement.Domain.Enums;
using Shared.Messaging;

namespace SubscriptionManagement.Application.Commands.CancelSubscription;

public class CancelSubscriptionCommandHandler : IRequestHandler<CancelSubscriptionCommand, bool>
{
    private readonly ISubscriptionRepository _subscriptionRepository;
    private readonly IPaymentService _paymentService;
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<CancelSubscriptionCommandHandler> _logger;

    public CancelSubscriptionCommandHandler(
        ISubscriptionRepository subscriptionRepository,
        IPaymentService paymentService,
        IMessageBroker messageBroker,
        ILogger<CancelSubscriptionCommandHandler> logger)
    {
        _subscriptionRepository = subscriptionRepository;
        _paymentService = paymentService;
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task<bool> Handle(CancelSubscriptionCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Cancelling subscription {SubscriptionId} for user {UserId}", 
            request.SubscriptionId, request.UserId);

        var subscription = await _subscriptionRepository.GetByIdAsync(request.SubscriptionId);
        if (subscription == null)
        {
            throw new SubscriptionDomainException("Subscription not found");
        }

        // Authorization check
        if (subscription.UserId != request.UserId)
        {
            throw new SubscriptionDomainException("You can only cancel your own subscription");
        }

        // Validate subscription can be cancelled
        if (subscription.Status == SubscriptionStatus.Cancelled)
        {
            throw new SubscriptionDomainException("Subscription is already cancelled");
        }

        if (subscription.Status == SubscriptionStatus.Expired)
        {
            throw new SubscriptionDomainException("Cannot cancel an expired subscription");
        }

        // Cancel the subscription
        subscription.Cancel(request.CancellationReason, request.ImmediateCancellation);

        // Handle refund if requested and applicable
        if (request.RefundProration && subscription.Status == SubscriptionStatus.Active)
        {
            try
            {
                var refundAmount = subscription.CalculateProrationRefund();
                if (refundAmount.Amount > 0)
                {
                    // Find the last successful payment
                    var lastPayment = subscription.Payments
                        .Where(p => p.Status == PaymentStatus.Completed)
                        .OrderByDescending(p => p.ProcessedAt)
                        .FirstOrDefault();

                    if (lastPayment != null)
                    {
                        var refundResult = await _paymentService.ProcessRefundAsync(lastPayment, refundAmount);
                        if (refundResult.IsSuccess)
                        {
                            _logger.LogInformation("Processed refund of {Amount} {Currency} for cancelled subscription {SubscriptionId}",
                                refundAmount.Amount, refundAmount.Currency, subscription.Id);
                        }
                        else
                        {
                            _logger.LogWarning("Failed to process refund for cancelled subscription {SubscriptionId}: {Error}",
                                subscription.Id, refundResult.ErrorMessage);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing refund for cancelled subscription {SubscriptionId}", subscription.Id);
                // Don't fail the cancellation if refund fails
            }
        }

        // Save changes
        await _subscriptionRepository.UpdateAsync(subscription);

        // Publish cancellation event
        await _messageBroker.PublishAsync("subscription.cancelled", new
        {
            SubscriptionId = subscription.Id,
            UserId = subscription.UserId,
            PlanId = subscription.PlanId,
            CancellationReason = request.CancellationReason,
            CancelledAt = subscription.CancelledAt,
            ImmediateCancellation = request.ImmediateCancellation,
            RefundProcessed = request.RefundProration,
            FinalBillingDate = subscription.EndDate
        });

        _logger.LogInformation("Successfully cancelled subscription {SubscriptionId} for user {UserId}", 
            request.SubscriptionId, request.UserId);

        return true;
    }
}
