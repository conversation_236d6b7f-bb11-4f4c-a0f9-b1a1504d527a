using CommunicationNotification.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace CommunicationNotification.Infrastructure.Persistence.Configurations;

/// <summary>
/// Entity configuration for AuditLog
/// </summary>
public class AuditLogConfiguration : IEntityTypeConfiguration<AuditLog>
{
    public void Configure(EntityTypeBuilder<AuditLog> builder)
    {
        builder.ToTable("audit_logs");

        // Primary key
        builder.HasKey(al => al.Id);
        builder.Property(al => al.Id)
            .ValueGeneratedNever();

        // Properties
        builder.Property(al => al.UserId)
            .IsRequired();

        builder.Property(al => al.Action)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(al => al.EntityType)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(al => al.EntityId)
            .IsRequired();

        builder.Property(al => al.Timestamp)
            .IsRequired();

        builder.Property(al => al.IpAddress)
            .HasMaxLength(45) // IPv6 max length
            .IsRequired(false);

        builder.Property(al => al.UserAgent)
            .HasMaxLength(500)
            .IsRequired(false);

        builder.Property(al => al.SessionId)
            .HasMaxLength(100)
            .IsRequired(false);

        builder.Property(al => al.CorrelationId)
            .HasMaxLength(100)
            .IsRequired(false);

        // Changes as JSON
        builder.Property(al => al.Changes)
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new())
            .HasColumnType("jsonb");

        // Additional data as JSON
        builder.Property(al => al.AdditionalData)
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new())
            .HasColumnType("jsonb");

        // Base entity properties
        builder.Property(al => al.CreatedAt)
            .IsRequired();

        builder.Property(al => al.UpdatedAt)
            .IsRequired();

        // Indexes are configured in DbContext
    }
}

/// <summary>
/// Entity configuration for ComplianceRecord
/// </summary>
public class ComplianceRecordConfiguration : IEntityTypeConfiguration<ComplianceRecord>
{
    public void Configure(EntityTypeBuilder<ComplianceRecord> builder)
    {
        builder.ToTable("compliance_records");

        // Primary key
        builder.HasKey(cr => cr.Id);
        builder.Property(cr => cr.Id)
            .ValueGeneratedNever();

        // Properties
        builder.Property(cr => cr.UserId)
            .IsRequired();

        builder.Property(cr => cr.ComplianceType)
            .IsRequired()
            .HasConversion<string>()
            .HasMaxLength(50);

        builder.Property(cr => cr.Action)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(cr => cr.Timestamp)
            .IsRequired();

        builder.Property(cr => cr.ConsentGiven)
            .IsRequired(false);

        builder.Property(cr => cr.ConsentTimestamp)
            .IsRequired(false);

        builder.Property(cr => cr.ConsentMethod)
            .HasMaxLength(50)
            .IsRequired(false);

        builder.Property(cr => cr.ConsentVersion)
            .HasMaxLength(20)
            .IsRequired(false);

        builder.Property(cr => cr.DataProcessed)
            .HasMaxLength(1000)
            .IsRequired(false);

        builder.Property(cr => cr.LegalBasis)
            .HasMaxLength(200)
            .IsRequired(false);

        builder.Property(cr => cr.RetentionPeriod)
            .IsRequired(false);

        builder.Property(cr => cr.ExpiresAt)
            .IsRequired(false);

        builder.Property(cr => cr.IsActive)
            .IsRequired()
            .HasDefaultValue(true);

        // Compliance data as JSON
        builder.Property(cr => cr.ComplianceData)
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new())
            .HasColumnType("jsonb");

        // Base entity properties
        builder.Property(cr => cr.CreatedAt)
            .IsRequired();

        builder.Property(cr => cr.UpdatedAt)
            .IsRequired();

        // Indexes
        builder.HasIndex(cr => cr.UserId);
        builder.HasIndex(cr => cr.ComplianceType);
        builder.HasIndex(cr => cr.Timestamp);
        builder.HasIndex(cr => cr.ExpiresAt);
        builder.HasIndex(cr => new { cr.UserId, cr.ComplianceType, cr.IsActive });
    }
}

/// <summary>
/// Entity configuration for DataRetentionPolicy
/// </summary>
public class DataRetentionPolicyConfiguration : IEntityTypeConfiguration<DataRetentionPolicy>
{
    public void Configure(EntityTypeBuilder<DataRetentionPolicy> builder)
    {
        builder.ToTable("data_retention_policies");

        // Primary key
        builder.HasKey(drp => drp.Id);
        builder.Property(drp => drp.Id)
            .ValueGeneratedNever();

        // Properties
        builder.Property(drp => drp.EntityType)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(drp => drp.DataCategory)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(drp => drp.RetentionPeriodDays)
            .IsRequired();

        builder.Property(drp => drp.PurgeAfterDays)
            .IsRequired(false);

        builder.Property(drp => drp.ArchiveAfterDays)
            .IsRequired(false);

        builder.Property(drp => drp.IsActive)
            .IsRequired()
            .HasDefaultValue(true);

        builder.Property(drp => drp.LegalBasis)
            .HasMaxLength(200)
            .IsRequired(false);

        builder.Property(drp => drp.Description)
            .HasMaxLength(500)
            .IsRequired(false);

        // Policy rules as JSON
        builder.Property(drp => drp.PolicyRules)
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new())
            .HasColumnType("jsonb");

        // Base entity properties
        builder.Property(drp => drp.CreatedAt)
            .IsRequired();

        builder.Property(drp => drp.UpdatedAt)
            .IsRequired();

        // Unique constraint on EntityType + DataCategory
        builder.HasIndex(drp => new { drp.EntityType, drp.DataCategory })
            .IsUnique();

        // Indexes
        builder.HasIndex(drp => drp.EntityType);
        builder.HasIndex(drp => drp.IsActive);
    }
}

/// <summary>
/// Entity configuration for ConsentRecord
/// </summary>
public class ConsentRecordConfiguration : IEntityTypeConfiguration<ConsentRecord>
{
    public void Configure(EntityTypeBuilder<ConsentRecord> builder)
    {
        builder.ToTable("consent_records");

        // Primary key
        builder.HasKey(cr => cr.Id);
        builder.Property(cr => cr.Id)
            .ValueGeneratedNever();

        // Properties
        builder.Property(cr => cr.UserId)
            .IsRequired();

        builder.Property(cr => cr.ConsentType)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(cr => cr.ConsentGiven)
            .IsRequired();

        builder.Property(cr => cr.ConsentTimestamp)
            .IsRequired();

        builder.Property(cr => cr.ConsentMethod)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(cr => cr.ConsentVersion)
            .IsRequired()
            .HasMaxLength(20);

        builder.Property(cr => cr.Purpose)
            .IsRequired()
            .HasMaxLength(500);

        builder.Property(cr => cr.LegalBasis)
            .HasMaxLength(200)
            .IsRequired(false);

        builder.Property(cr => cr.ExpiresAt)
            .IsRequired(false);

        builder.Property(cr => cr.WithdrawnAt)
            .IsRequired(false);

        builder.Property(cr => cr.WithdrawalMethod)
            .HasMaxLength(50)
            .IsRequired(false);

        builder.Property(cr => cr.IsActive)
            .IsRequired()
            .HasDefaultValue(true);

        builder.Property(cr => cr.IpAddress)
            .HasMaxLength(45)
            .IsRequired(false);

        builder.Property(cr => cr.UserAgent)
            .HasMaxLength(500)
            .IsRequired(false);

        // Consent data as JSON
        builder.Property(cr => cr.ConsentData)
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new())
            .HasColumnType("jsonb");

        // Base entity properties
        builder.Property(cr => cr.CreatedAt)
            .IsRequired();

        builder.Property(cr => cr.UpdatedAt)
            .IsRequired();

        // Indexes
        builder.HasIndex(cr => cr.UserId);
        builder.HasIndex(cr => cr.ConsentType);
        builder.HasIndex(cr => cr.ConsentTimestamp);
        builder.HasIndex(cr => cr.ExpiresAt);
        builder.HasIndex(cr => new { cr.UserId, cr.ConsentType, cr.IsActive });
    }
}
