using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;

namespace OrderManagement.Application.EventHandlers;

/// <summary>
/// Integration event for RFQ expired notifications
/// </summary>
public record RfqExpiredIntegrationEvent : INotification
{
    public Guid RfqId { get; init; }
    public Guid TransportCompanyId { get; init; }
    public string Title { get; init; } = string.Empty;
    public DateTime? ExpiresAt { get; init; }
    public DateTime ExpiredAt { get; init; }
    public string EventType { get; init; } = string.Empty;
    public bool NotificationRequired { get; init; }
    public string[] NotificationChannels { get; init; } = Array.Empty<string>();
    public string NotificationTemplate { get; init; } = string.Empty;
    public object NotificationData { get; init; } = new();
}

/// <summary>
/// Integration event for RFQ timeframe extended notifications
/// </summary>
public record RfqTimeframeExtendedIntegrationEvent : INotification
{
    public Guid RfqId { get; init; }
    public Guid TransportCompanyId { get; init; }
    public object Extension { get; init; } = new();
    public DateTime NewExpiresAt { get; init; }
    public string EventType { get; init; } = string.Empty;
    public bool NotificationRequired { get; init; }
    public string[] NotificationChannels { get; init; } = Array.Empty<string>();
    public string NotificationTemplate { get; init; } = string.Empty;
    public object NotificationData { get; init; } = new();
}

/// <summary>
/// Integration event for RFQ approaching expiration notifications
/// </summary>
public record RfqApproachingExpirationIntegrationEvent : INotification
{
    public Guid RfqId { get; init; }
    public Guid TransportCompanyId { get; init; }
    public string Title { get; init; } = string.Empty;
    public DateTime ExpiresAt { get; init; }
    public object RemainingTime { get; init; } = new();
    public string UrgencyLevel { get; init; } = string.Empty;
    public string EventType { get; init; } = string.Empty;
    public bool NotificationRequired { get; init; }
    public string[] NotificationChannels { get; init; } = Array.Empty<string>();
    public string NotificationTemplate { get; init; } = string.Empty;
    public object NotificationData { get; init; } = new();
}

/// <summary>
/// Handler for RFQ expired integration events - sends notifications
/// </summary>
public class RfqExpiredNotificationHandler : INotificationHandler<RfqExpiredIntegrationEvent>
{
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<RfqExpiredNotificationHandler> _logger;

    public RfqExpiredNotificationHandler(
        IMessageBroker messageBroker,
        ILogger<RfqExpiredNotificationHandler> logger)
    {
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task Handle(RfqExpiredIntegrationEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Sending expiration notification for RFQ {RfqId}", notification.RfqId);

        try
        {
            // Send notification to CommunicationNotification service
            await _messageBroker.PublishAsync("communication.send.notification", new
            {
                UserId = notification.TransportCompanyId,
                MessageType = "RfqExpired",
                Subject = $"RFQ Expired: {notification.Title}",
                Content = $"Your RFQ '{notification.Title}' has expired and is no longer accepting bids.",
                Priority = "High",
                PreferredChannels = notification.NotificationChannels,
                TemplateId = notification.NotificationTemplate,
                TemplateParameters = notification.NotificationData,
                RelatedEntityId = notification.RfqId,
                RelatedEntityType = "RFQ",
                Tags = new[] { "rfq", "expiration", "urgent" },
                Metadata = new
                {
                    RfqId = notification.RfqId,
                    EventType = notification.EventType,
                    ExpiredAt = notification.ExpiredAt
                }
            }, cancellationToken);

            _logger.LogInformation("Successfully sent expiration notification for RFQ {RfqId}", notification.RfqId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending expiration notification for RFQ {RfqId}", notification.RfqId);
            throw;
        }
    }
}

/// <summary>
/// Handler for RFQ timeframe extended integration events - sends notifications
/// </summary>
public class RfqTimeframeExtendedNotificationHandler : INotificationHandler<RfqTimeframeExtendedIntegrationEvent>
{
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<RfqTimeframeExtendedNotificationHandler> _logger;

    public RfqTimeframeExtendedNotificationHandler(
        IMessageBroker messageBroker,
        ILogger<RfqTimeframeExtendedNotificationHandler> logger)
    {
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task Handle(RfqTimeframeExtendedIntegrationEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Sending timeframe extension notification for RFQ {RfqId}", notification.RfqId);

        try
        {
            // Send notification to CommunicationNotification service
            await _messageBroker.PublishAsync("communication.send.notification", new
            {
                UserId = notification.TransportCompanyId,
                MessageType = "RfqTimeframeExtended",
                Subject = "RFQ Timeframe Extended",
                Content = $"The timeframe for your RFQ has been extended. New expiration: {notification.NewExpiresAt:yyyy-MM-dd HH:mm:ss UTC}",
                Priority = "Normal",
                PreferredChannels = notification.NotificationChannels,
                TemplateId = notification.NotificationTemplate,
                TemplateParameters = notification.NotificationData,
                RelatedEntityId = notification.RfqId,
                RelatedEntityType = "RFQ",
                Tags = new[] { "rfq", "extension", "timeframe" },
                Metadata = new
                {
                    RfqId = notification.RfqId,
                    EventType = notification.EventType,
                    Extension = notification.Extension,
                    NewExpiresAt = notification.NewExpiresAt
                }
            }, cancellationToken);

            _logger.LogInformation("Successfully sent timeframe extension notification for RFQ {RfqId}", notification.RfqId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending timeframe extension notification for RFQ {RfqId}", notification.RfqId);
            throw;
        }
    }
}

/// <summary>
/// Handler for RFQ approaching expiration integration events - sends notifications
/// </summary>
public class RfqApproachingExpirationNotificationHandler : INotificationHandler<RfqApproachingExpirationIntegrationEvent>
{
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<RfqApproachingExpirationNotificationHandler> _logger;

    public RfqApproachingExpirationNotificationHandler(
        IMessageBroker messageBroker,
        ILogger<RfqApproachingExpirationNotificationHandler> logger)
    {
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task Handle(RfqApproachingExpirationIntegrationEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Sending approaching expiration notification for RFQ {RfqId} with urgency {UrgencyLevel}", 
            notification.RfqId, notification.UrgencyLevel);

        try
        {
            var priority = notification.UrgencyLevel switch
            {
                "Critical" => "Critical",
                "High" => "High",
                "Medium" => "Normal",
                _ => "Low"
            };

            var subject = notification.UrgencyLevel switch
            {
                "Critical" => $"URGENT: RFQ '{notification.Title}' expires in less than 1 hour!",
                "High" => $"RFQ '{notification.Title}' expires soon",
                _ => $"RFQ '{notification.Title}' expiration reminder"
            };

            // Send notification to CommunicationNotification service
            await _messageBroker.PublishAsync("communication.send.notification", new
            {
                UserId = notification.TransportCompanyId,
                MessageType = "RfqApproachingExpiration",
                Subject = subject,
                Content = GetNotificationContent(notification),
                Priority = priority,
                PreferredChannels = notification.NotificationChannels,
                TemplateId = notification.NotificationTemplate,
                TemplateParameters = notification.NotificationData,
                RelatedEntityId = notification.RfqId,
                RelatedEntityType = "RFQ",
                Tags = new[] { "rfq", "expiration", "reminder", notification.UrgencyLevel.ToLower() },
                Metadata = new
                {
                    RfqId = notification.RfqId,
                    EventType = notification.EventType,
                    UrgencyLevel = notification.UrgencyLevel,
                    ExpiresAt = notification.ExpiresAt,
                    RemainingTime = notification.RemainingTime
                }
            }, cancellationToken);

            _logger.LogInformation("Successfully sent approaching expiration notification for RFQ {RfqId}", notification.RfqId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending approaching expiration notification for RFQ {RfqId}", notification.RfqId);
            throw;
        }
    }

    private static string GetNotificationContent(RfqApproachingExpirationIntegrationEvent notification)
    {
        return notification.UrgencyLevel switch
        {
            "Critical" => $"Your RFQ '{notification.Title}' will expire in less than 1 hour! Take immediate action to extend the timeframe or close the RFQ if you have received sufficient bids.",
            "High" => $"Your RFQ '{notification.Title}' will expire soon. Consider extending the timeframe if you need more time to receive bids.",
            "Medium" => $"Your RFQ '{notification.Title}' will expire today. You may want to review the current bids or extend the timeframe.",
            _ => $"Your RFQ '{notification.Title}' will expire within 24 hours. Please review and take appropriate action."
        };
    }
}
