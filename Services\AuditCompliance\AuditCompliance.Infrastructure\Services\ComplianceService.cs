using AuditCompliance.Application.Interfaces;
using AuditCompliance.Application.DTOs;
using AuditCompliance.Domain.Entities;
using AuditCompliance.Domain.Enums;
using Microsoft.Extensions.Logging;

namespace AuditCompliance.Infrastructure.Services;

public class ComplianceService : IComplianceService
{
    private readonly IComplianceReportRepository _complianceReportRepository;
    private readonly IAuditLogRepository _auditLogRepository;
    private readonly ILogger<ComplianceService> _logger;

    public ComplianceService(
        IComplianceReportRepository complianceReportRepository,
        IAuditLogRepository auditLogRepository,
        ILogger<ComplianceService> logger)
    {
        _complianceReportRepository = complianceReportRepository;
        _auditLogRepository = auditLogRepository;
        _logger = logger;
    }

    public async Task<Guid> CreateReportAsync(CreateComplianceReportDto reportDto, Guid generatedBy, string generatedByName, CancellationToken cancellationToken = default)
    {
        try
        {
            var report = new ComplianceReport(
                reportDto.Title,
                reportDto.Description,
                reportDto.Standard,
                reportDto.ReportPeriodStart,
                reportDto.ReportPeriodEnd,
                generatedBy,
                generatedByName,
                reportDto.IsAutomated);

            var savedReport = await _complianceReportRepository.AddAsync(report, cancellationToken);

            _logger.LogInformation("Compliance report created: {ReportId} for standard: {Standard}", 
                savedReport.Id, reportDto.Standard);

            return savedReport.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating compliance report for standard: {Standard}", reportDto.Standard);
            throw;
        }
    }

    public async Task<ComplianceReportDto?> GetReportAsync(Guid reportId, CancellationToken cancellationToken = default)
    {
        try
        {
            var report = await _complianceReportRepository.GetByIdAsync(reportId, cancellationToken);
            if (report == null)
                return null;

            return new ComplianceReportDto
            {
                Id = report.Id,
                ReportNumber = report.ReportNumber,
                Title = report.Title,
                Description = report.Description,
                Standard = report.Standard,
                Status = report.Status,
                ReportPeriodStart = report.ReportPeriodStart,
                ReportPeriodEnd = report.ReportPeriodEnd,
                GeneratedBy = report.GeneratedBy,
                GeneratedByName = report.GeneratedByName,
                CreatedAt = report.CreatedAt,
                CompletedAt = report.CompletedAt,
                FilePath = report.FilePath,
                IsAutomated = report.IsAutomated,
                Findings = report.Findings,
                Recommendations = report.Recommendations,
                TotalViolations = report.TotalViolations,
                CriticalViolations = report.CriticalViolations,
                HighViolations = report.HighViolations,
                MediumViolations = report.MediumViolations,
                LowViolations = report.LowViolations,
                Items = report.Items.Select(item => new ComplianceReportItemDto
                {
                    Id = item.Id,
                    CheckName = item.CheckName,
                    Description = item.Description,
                    IsViolation = item.IsViolation,
                    Severity = item.Severity,
                    Details = item.Details,
                    Recommendation = item.Recommendation,
                    Evidence = item.Evidence,
                    CheckedAt = item.CheckedAt
                }).ToList()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving compliance report: {ReportId}", reportId);
            throw;
        }
    }

    public async Task<ComplianceReportResultDto> GetReportsAsync(ComplianceReportQueryDto query, CancellationToken cancellationToken = default)
    {
        try
        {
            var reports = await _complianceReportRepository.GetReportsAsync(
                query.Standard,
                query.Status,
                query.FromDate,
                query.ToDate,
                query.GeneratedBy,
                query.IsAutomated,
                query.PageNumber,
                query.PageSize,
                cancellationToken);

            var totalCount = await _complianceReportRepository.GetReportsCountAsync(
                query.Standard,
                query.Status,
                query.FromDate,
                query.ToDate,
                query.GeneratedBy,
                query.IsAutomated,
                cancellationToken);

            var reportDtos = reports.Select(r => new ComplianceReportDto
            {
                Id = r.Id,
                ReportNumber = r.ReportNumber,
                Title = r.Title,
                Description = r.Description,
                Standard = r.Standard,
                Status = r.Status,
                ReportPeriodStart = r.ReportPeriodStart,
                ReportPeriodEnd = r.ReportPeriodEnd,
                GeneratedBy = r.GeneratedBy,
                GeneratedByName = r.GeneratedByName,
                CreatedAt = r.CreatedAt,
                CompletedAt = r.CompletedAt,
                FilePath = r.FilePath,
                IsAutomated = r.IsAutomated,
                Findings = r.Findings,
                Recommendations = r.Recommendations,
                TotalViolations = r.TotalViolations,
                CriticalViolations = r.CriticalViolations,
                HighViolations = r.HighViolations,
                MediumViolations = r.MediumViolations,
                LowViolations = r.LowViolations
            }).ToList();

            var totalPages = (int)Math.Ceiling((double)totalCount / query.PageSize);

            return new ComplianceReportResultDto
            {
                Reports = reportDtos,
                TotalCount = totalCount,
                PageNumber = query.PageNumber,
                PageSize = query.PageSize,
                TotalPages = totalPages,
                HasNextPage = query.PageNumber < totalPages,
                HasPreviousPage = query.PageNumber > 1
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving compliance reports");
            throw;
        }
    }

    public async Task<List<ComplianceSummaryDto>> GetComplianceSummaryAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var summaries = new List<ComplianceSummaryDto>();
            var standards = Enum.GetValues<ComplianceStandard>();

            foreach (var standard in standards)
            {
                var reports = await _complianceReportRepository.GetReportsAsync(
                    standard: standard,
                    fromDate: DateTime.UtcNow.AddMonths(-12),
                    cancellationToken: cancellationToken);

                var summary = new ComplianceSummaryDto
                {
                    Standard = standard,
                    TotalReports = reports.Count,
                    CompliantReports = reports.Count(r => r.Status == ComplianceStatus.Compliant),
                    NonCompliantReports = reports.Count(r => r.Status == ComplianceStatus.NonCompliant),
                    PendingReports = reports.Count(r => r.Status == ComplianceStatus.Pending || r.Status == ComplianceStatus.InProgress),
                    LastReportDate = reports.Any() ? reports.Max(r => r.CreatedAt) : DateTime.MinValue,
                    OverallStatus = DetermineOverallStatus(reports)
                };

                // Calculate violation summary
                var allViolations = reports.SelectMany(r => r.Items).Where(i => i.IsViolation).ToList();
                if (allViolations.Any())
                {
                    var totalViolations = allViolations.Count;
                    summary.ViolationSummary = Enum.GetValues<AuditSeverity>()
                        .Select(severity => new ComplianceViolationSummaryDto
                        {
                            Severity = severity,
                            Count = allViolations.Count(v => v.Severity == severity),
                            Percentage = totalViolations > 0 ? (decimal)allViolations.Count(v => v.Severity == severity) / totalViolations * 100 : 0
                        }).ToList();
                }

                summaries.Add(summary);
            }

            return summaries.Where(s => s.TotalReports > 0).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving compliance summary");
            throw;
        }
    }

    public async Task StartReportProcessingAsync(Guid reportId, CancellationToken cancellationToken = default)
    {
        try
        {
            var report = await _complianceReportRepository.GetByIdAsync(reportId, cancellationToken);
            if (report == null)
                throw new InvalidOperationException($"Compliance report not found: {reportId}");

            report.SetInProgress();
            await _complianceReportRepository.UpdateAsync(report, cancellationToken);

            _logger.LogInformation("Started processing compliance report: {ReportId}", reportId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting compliance report processing: {ReportId}", reportId);
            throw;
        }
    }

    public async Task CompleteReportAsync(Guid reportId, string? findings = null, string? recommendations = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var report = await _complianceReportRepository.GetByIdAsync(reportId, cancellationToken);
            if (report == null)
                throw new InvalidOperationException($"Compliance report not found: {reportId}");

            report.Complete(findings, recommendations);
            await _complianceReportRepository.UpdateAsync(report, cancellationToken);

            _logger.LogInformation("Completed compliance report: {ReportId}", reportId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error completing compliance report: {ReportId}", reportId);
            throw;
        }
    }

    public async Task<ComplianceReportDto> GenerateAutomatedReportAsync(ComplianceStandard standard, DateTime periodStart, DateTime periodEnd, CancellationToken cancellationToken = default)
    {
        try
        {
            // Create automated report
            var report = new ComplianceReport(
                $"Automated {standard} Compliance Report",
                $"Automated compliance report for {standard} covering period {periodStart:yyyy-MM-dd} to {periodEnd:yyyy-MM-dd}",
                standard,
                periodStart,
                periodEnd,
                Guid.Empty, // System generated
                "System",
                isAutomated: true);

            // Add automated checks based on standard
            await AddAutomatedChecks(report, standard, periodStart, periodEnd, cancellationToken);

            var savedReport = await _complianceReportRepository.AddAsync(report, cancellationToken);
            savedReport.Complete("Automated compliance check completed", "Review findings and take corrective actions as needed");
            await _complianceReportRepository.UpdateAsync(savedReport, cancellationToken);

            return await GetReportAsync(savedReport.Id, cancellationToken) ?? throw new InvalidOperationException("Failed to retrieve generated report");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating automated compliance report for standard: {Standard}", standard);
            throw;
        }
    }

    private ComplianceStatus DetermineOverallStatus(List<ComplianceReport> reports)
    {
        if (!reports.Any())
            return ComplianceStatus.Pending;

        var recentReports = reports.Where(r => r.CreatedAt >= DateTime.UtcNow.AddDays(-30)).ToList();
        if (!recentReports.Any())
            return ComplianceStatus.RequiresReview;

        if (recentReports.Any(r => r.Status == ComplianceStatus.NonCompliant))
            return ComplianceStatus.NonCompliant;

        if (recentReports.Any(r => r.Status == ComplianceStatus.PartiallyCompliant))
            return ComplianceStatus.PartiallyCompliant;

        return recentReports.All(r => r.Status == ComplianceStatus.Compliant) 
            ? ComplianceStatus.Compliant 
            : ComplianceStatus.RequiresReview;
    }

    private async Task AddAutomatedChecks(ComplianceReport report, ComplianceStandard standard, DateTime periodStart, DateTime periodEnd, CancellationToken cancellationToken)
    {
        // Get audit logs for the period
        var auditLogs = await _auditLogRepository.GetAuditTrailAsync(
            fromDate: periodStart,
            toDate: periodEnd,
            cancellationToken: cancellationToken);

        switch (standard)
        {
            case ComplianceStandard.GDPR:
                await AddGDPRChecks(report, auditLogs);
                break;
            case ComplianceStandard.DataRetention:
                await AddDataRetentionChecks(report, auditLogs);
                break;
            case ComplianceStandard.AccessControl:
                await AddAccessControlChecks(report, auditLogs);
                break;
            default:
                await AddGenericChecks(report, auditLogs);
                break;
        }
    }

    private async Task AddGDPRChecks(ComplianceReport report, List<AuditLog> auditLogs)
    {
        // Check for data export requests
        var dataExports = auditLogs.Where(al => al.EventType == AuditEventType.DataExport).ToList();
        report.AddItem(ComplianceReportItem.CreateCompliant(
            report.Id,
            "Data Export Tracking",
            "Verify all data export requests are properly logged",
            $"Found {dataExports.Count} data export events"));

        // Check for data deletion requests
        var dataDeletions = auditLogs.Where(al => al.EventType == AuditEventType.DataDeletion).ToList();
        if (dataDeletions.Any(dd => !dd.ComplianceFlags.Contains(ComplianceStandard.GDPR)))
        {
            report.AddItem(ComplianceReportItem.CreateViolation(
                report.Id,
                "GDPR Data Deletion",
                "All data deletions must be GDPR compliant",
                AuditSeverity.High,
                "Some data deletions are not marked as GDPR compliant",
                "Review and update data deletion procedures to ensure GDPR compliance"));
        }
        else
        {
            report.AddItem(ComplianceReportItem.CreateCompliant(
                report.Id,
                "GDPR Data Deletion",
                "All data deletions are GDPR compliant",
                $"Found {dataDeletions.Count} compliant data deletion events"));
        }
    }

    private async Task AddDataRetentionChecks(ComplianceReport report, List<AuditLog> auditLogs)
    {
        // Check for expired data
        var expiredLogs = auditLogs.Where(al => al.IsRetentionExpired()).ToList();
        if (expiredLogs.Any())
        {
            report.AddItem(ComplianceReportItem.CreateViolation(
                report.Id,
                "Data Retention Policy",
                "Data should be deleted according to retention policy",
                AuditSeverity.Medium,
                $"Found {expiredLogs.Count} audit logs that have exceeded their retention period",
                "Implement automated data cleanup process"));
        }
        else
        {
            report.AddItem(ComplianceReportItem.CreateCompliant(
                report.Id,
                "Data Retention Policy",
                "All data is within retention policy limits",
                "No expired data found"));
        }
    }

    private async Task AddAccessControlChecks(ComplianceReport report, List<AuditLog> auditLogs)
    {
        // Check for unauthorized access attempts
        var unauthorizedAccess = auditLogs.Where(al => al.EventType == AuditEventType.UnauthorizedAccess).ToList();
        if (unauthorizedAccess.Any())
        {
            report.AddItem(ComplianceReportItem.CreateViolation(
                report.Id,
                "Unauthorized Access",
                "Monitor and prevent unauthorized access attempts",
                AuditSeverity.High,
                $"Found {unauthorizedAccess.Count} unauthorized access attempts",
                "Review access controls and implement additional security measures"));
        }
        else
        {
            report.AddItem(ComplianceReportItem.CreateCompliant(
                report.Id,
                "Unauthorized Access",
                "No unauthorized access attempts detected",
                "Access control systems are functioning properly"));
        }
    }

    private async Task AddGenericChecks(ComplianceReport report, List<AuditLog> auditLogs)
    {
        // Generic audit log analysis
        var highSeverityEvents = auditLogs.Where(al => al.Severity >= AuditSeverity.High).ToList();
        if (highSeverityEvents.Any())
        {
            report.AddItem(ComplianceReportItem.CreateViolation(
                report.Id,
                "High Severity Events",
                "Monitor and address high severity audit events",
                AuditSeverity.Medium,
                $"Found {highSeverityEvents.Count} high severity events",
                "Review high severity events and implement corrective actions"));
        }
        else
        {
            report.AddItem(ComplianceReportItem.CreateCompliant(
                report.Id,
                "High Severity Events",
                "No high severity events detected",
                "System is operating within normal parameters"));
        }
    }
}
