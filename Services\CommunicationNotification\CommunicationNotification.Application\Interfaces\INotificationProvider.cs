using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;

namespace CommunicationNotification.Application.Interfaces;

/// <summary>
/// Base interface for all notification providers
/// </summary>
public interface INotificationProvider
{
    /// <summary>
    /// The notification channel this provider supports
    /// </summary>
    NotificationChannel Channel { get; }

    /// <summary>
    /// Check if the provider is available and configured
    /// </summary>
    Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Send a notification through this provider
    /// </summary>
    /// <param name="recipient">Recipient contact information</param>
    /// <param name="content">Message content</param>
    /// <param name="metadata">Additional metadata for the message</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result containing external ID and delivery status</returns>
    Task<NotificationResult> SendAsync(
        ContactInfo recipient,
        MessageContent content,
        Dictionary<string, string>? metadata = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get delivery status for a previously sent notification
    /// </summary>
    /// <param name="externalId">External ID returned from SendAsync</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Current delivery status</returns>
    Task<DeliveryStatus> GetDeliveryStatusAsync(
        string externalId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate if the recipient contact info is valid for this provider
    /// </summary>
    /// <param name="recipient">Recipient contact information</param>
    /// <returns>True if valid, false otherwise</returns>
    bool ValidateRecipient(ContactInfo recipient);
}

/// <summary>
/// Result of a notification send operation
/// </summary>
public class NotificationResult
{
    public bool IsSuccess { get; set; }
    public string? ExternalId { get; set; }
    public string? ErrorMessage { get; set; }
    public DeliveryStatus Status { get; set; }
    public Dictionary<string, string> Metadata { get; set; } = new();

    public static NotificationResult Success(string externalId, DeliveryStatus status = DeliveryStatus.Sent)
    {
        return new NotificationResult
        {
            IsSuccess = true,
            ExternalId = externalId,
            Status = status
        };
    }

    public static NotificationResult Failure(string errorMessage)
    {
        return new NotificationResult
        {
            IsSuccess = false,
            ErrorMessage = errorMessage,
            Status = DeliveryStatus.Failed
        };
    }
}

/// <summary>
/// Delivery status for notifications
/// </summary>
public enum DeliveryStatus
{
    Pending = 1,
    Sent = 2,
    Delivered = 3,
    Read = 4,
    Failed = 5,
    Expired = 6
}
