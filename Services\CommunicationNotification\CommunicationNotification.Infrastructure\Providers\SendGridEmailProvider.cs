using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;
using CommunicationNotification.Infrastructure.Configuration;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SendGrid;
using SendGrid.Helpers.Mail;

namespace CommunicationNotification.Infrastructure.Providers;

/// <summary>
/// SendGrid email provider implementation
/// </summary>
public class SendGridEmailProvider : IEmailProvider
{
    private readonly ISendGridClient _sendGridClient;
    private readonly ILogger<SendGridEmailProvider> _logger;
    private readonly SendGridConfiguration _configuration;

    public NotificationChannel Channel => NotificationChannel.Email;

    public SendGridEmailProvider(
        IConfiguration configuration,
        ILogger<SendGridEmailProvider> logger)
    {
        _logger = logger;
        _configuration = configuration.GetSection("SendGrid").Get<SendGridConfiguration>()
            ?? throw new InvalidOperationException("SendGrid configuration is missing");

        _sendGridClient = new SendGridClient(_configuration.ApiKey);
    }

    public async Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(_configuration.ApiKey))
            {
                return false;
            }

            // Test API connectivity by making a simple API call
            var response = await _sendGridClient.RequestAsync(
                method: SendGridClient.Method.GET,
                urlPath: "user/profile",
                cancellationToken: cancellationToken);

            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check SendGrid availability");
            return false;
        }
    }

    public async Task<NotificationResult> SendAsync(
        ContactInfo recipient,
        MessageContent content,
        Dictionary<string, string>? metadata = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (!ValidateRecipient(recipient))
            {
                return NotificationResult.Failure("Invalid email address");
            }

            var emailRequest = new EmailRequest
            {
                ToEmail = recipient.Email,
                Subject = content.Subject,
                HtmlContent = content.Body,
                TextContent = content.Body,
                FromEmail = _configuration.FromEmail,
                FromName = _configuration.FromName
            };

            var result = await SendEmailAsync(emailRequest, cancellationToken);

            return result.IsSuccess
                ? NotificationResult.Success(result.MessageId!, DeliveryStatus.Sent)
                : NotificationResult.Failure(result.ErrorMessage!);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send email to {Email}", recipient.Email);
            return NotificationResult.Failure($"Failed to send email: {ex.Message}");
        }
    }

    public async Task<DeliveryStatus> GetDeliveryStatusAsync(
        string externalId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // SendGrid doesn't provide a direct API to get message status by ID
            // This would typically be handled through webhooks
            // For now, return sent status as a placeholder
            _logger.LogDebug("Getting email delivery status for {MessageId}", externalId);
            return DeliveryStatus.Sent;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get email delivery status for {MessageId}", externalId);
            return DeliveryStatus.Failed;
        }
    }

    public bool ValidateRecipient(ContactInfo recipient)
    {
        if (string.IsNullOrWhiteSpace(recipient.Email))
            return false;

        try
        {
            var emailAddress = new System.Net.Mail.MailAddress(recipient.Email);
            return emailAddress.Address == recipient.Email;
        }
        catch
        {
            return false;
        }
    }

    public async Task<EmailResult> SendEmailAsync(
        EmailRequest emailRequest,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var from = new EmailAddress(
                emailRequest.FromEmail ?? _configuration.FromEmail,
                emailRequest.FromName ?? _configuration.FromName);

            var to = new EmailAddress(emailRequest.ToEmail, emailRequest.ToName);

            var msg = MailHelper.CreateSingleEmail(
                from,
                to,
                emailRequest.Subject,
                emailRequest.TextContent,
                emailRequest.HtmlContent);

            // Add CC recipients
            if (emailRequest.CcEmails?.Any() == true)
            {
                var ccList = emailRequest.CcEmails.Select(email => new EmailAddress(email)).ToList();
                msg.AddCcs(ccList);
            }

            // Add BCC recipients
            if (emailRequest.BccEmails?.Any() == true)
            {
                var bccList = emailRequest.BccEmails.Select(email => new EmailAddress(email)).ToList();
                msg.AddBccs(bccList);
            }

            // Add attachments
            if (emailRequest.Attachments?.Any() == true)
            {
                foreach (var attachment in emailRequest.Attachments)
                {
                    var sendGridAttachment = new Attachment
                    {
                        Content = Convert.ToBase64String(attachment.Content),
                        Filename = attachment.FileName,
                        Type = attachment.ContentType
                    };

                    if (!string.IsNullOrEmpty(attachment.ContentId))
                    {
                        sendGridAttachment.ContentId = attachment.ContentId;
                        sendGridAttachment.Disposition = "inline";
                    }

                    msg.AddAttachment(sendGridAttachment);
                }
            }

            // Add custom headers
            if (emailRequest.Headers?.Any() == true)
            {
                msg.AddHeaders(emailRequest.Headers);
            }

            // Configure tracking
            msg.SetClickTracking(emailRequest.TrackClicks, emailRequest.TrackClicks);
            msg.SetOpenTracking(emailRequest.TrackOpens);

            var response = await _sendGridClient.SendEmailAsync(msg, cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var messageId = response.Headers?.GetValues("X-Message-Id")?.FirstOrDefault() 
                    ?? Guid.NewGuid().ToString();

                _logger.LogInformation("Email sent successfully to {Email}, MessageId: {MessageId}",
                    emailRequest.ToEmail, messageId);

                return EmailResult.Success(messageId);
            }
            else
            {
                var errorBody = await response.Body.ReadAsStringAsync();
                _logger.LogError("Failed to send email to {Email}: {StatusCode} - {Error}",
                    emailRequest.ToEmail, response.StatusCode, errorBody);

                return EmailResult.Failure($"SendGrid API error: {response.StatusCode}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception occurred while sending email to {Email}", emailRequest.ToEmail);
            return EmailResult.Failure($"Exception: {ex.Message}");
        }
    }

    public async Task<BulkEmailResult> SendBulkEmailAsync(
        BulkEmailRequest bulkEmailRequest,
        CancellationToken cancellationToken = default)
    {
        var result = new BulkEmailResult
        {
            TotalEmails = bulkEmailRequest.Recipients.Count
        };

        try
        {
            var from = new EmailAddress(
                bulkEmailRequest.FromEmail ?? _configuration.FromEmail,
                bulkEmailRequest.FromName ?? _configuration.FromName);

            var msg = new SendGridMessage
            {
                From = from,
                Subject = bulkEmailRequest.Subject,
                PlainTextContent = bulkEmailRequest.TextContent,
                HtmlContent = bulkEmailRequest.HtmlContent
            };

            // Add recipients with personalization data
            foreach (var recipient in bulkEmailRequest.Recipients)
            {
                var personalization = new Personalization();
                personalization.AddTo(new EmailAddress(recipient.Email, recipient.Name));

                if (recipient.PersonalizationData?.Any() == true)
                {
                    foreach (var data in recipient.PersonalizationData)
                    {
                        personalization.AddSubstitution($"-{data.Key}-", data.Value?.ToString() ?? "");
                    }
                }

                msg.AddPersonalization(personalization);
            }

            // Add attachments
            if (bulkEmailRequest.Attachments?.Any() == true)
            {
                foreach (var attachment in bulkEmailRequest.Attachments)
                {
                    var sendGridAttachment = new Attachment
                    {
                        Content = Convert.ToBase64String(attachment.Content),
                        Filename = attachment.FileName,
                        Type = attachment.ContentType
                    };

                    msg.AddAttachment(sendGridAttachment);
                }
            }

            // Configure tracking
            msg.SetClickTracking(bulkEmailRequest.TrackClicks, bulkEmailRequest.TrackClicks);
            msg.SetOpenTracking(bulkEmailRequest.TrackOpens);

            var response = await _sendGridClient.SendEmailAsync(msg, cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                result.SuccessfulEmails = result.TotalEmails;
                result.FailedEmails = 0;

                // Create individual results for each recipient
                foreach (var recipient in bulkEmailRequest.Recipients)
                {
                    result.Results.Add(EmailResult.Success(Guid.NewGuid().ToString()));
                }

                _logger.LogInformation("Bulk email sent successfully to {Count} recipients", result.TotalEmails);
            }
            else
            {
                result.SuccessfulEmails = 0;
                result.FailedEmails = result.TotalEmails;

                var errorBody = await response.Body.ReadAsStringAsync();
                var errorMessage = $"SendGrid API error: {response.StatusCode}";

                // Create failed results for each recipient
                foreach (var recipient in bulkEmailRequest.Recipients)
                {
                    result.Results.Add(EmailResult.Failure(errorMessage));
                }

                _logger.LogError("Failed to send bulk email: {StatusCode} - {Error}",
                    response.StatusCode, errorBody);
            }
        }
        catch (Exception ex)
        {
            result.SuccessfulEmails = 0;
            result.FailedEmails = result.TotalEmails;

            var errorMessage = $"Exception: {ex.Message}";

            // Create failed results for each recipient
            foreach (var recipient in bulkEmailRequest.Recipients)
            {
                result.Results.Add(EmailResult.Failure(errorMessage));
            }

            _logger.LogError(ex, "Exception occurred while sending bulk email");
        }

        return result;
    }

    public async Task<EmailResult> SendTemplatedEmailAsync(
        TemplatedEmailRequest templateRequest,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var from = new EmailAddress(
                templateRequest.FromEmail ?? _configuration.FromEmail,
                templateRequest.FromName ?? _configuration.FromName);

            var to = new EmailAddress(templateRequest.ToEmail, templateRequest.ToName);

            var msg = new SendGridMessage
            {
                From = from,
                TemplateId = templateRequest.TemplateId
            };

            var personalization = new Personalization();
            personalization.AddTo(to);

            // Add template data
            if (templateRequest.TemplateData?.Any() == true)
            {
                foreach (var data in templateRequest.TemplateData)
                {
                    personalization.AddSubstitution($"-{data.Key}-", data.Value?.ToString() ?? "");
                }
            }

            msg.AddPersonalization(personalization);

            // Configure tracking
            msg.SetClickTracking(templateRequest.TrackClicks, templateRequest.TrackClicks);
            msg.SetOpenTracking(templateRequest.TrackOpens);

            var response = await _sendGridClient.SendEmailAsync(msg, cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var messageId = response.Headers?.GetValues("X-Message-Id")?.FirstOrDefault() 
                    ?? Guid.NewGuid().ToString();

                _logger.LogInformation("Templated email sent successfully to {Email}, Template: {TemplateId}, MessageId: {MessageId}",
                    templateRequest.ToEmail, templateRequest.TemplateId, messageId);

                return EmailResult.Success(messageId);
            }
            else
            {
                var errorBody = await response.Body.ReadAsStringAsync();
                _logger.LogError("Failed to send templated email to {Email}: {StatusCode} - {Error}",
                    templateRequest.ToEmail, response.StatusCode, errorBody);

                return EmailResult.Failure($"SendGrid API error: {response.StatusCode}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception occurred while sending templated email to {Email}", templateRequest.ToEmail);
            return EmailResult.Failure($"Exception: {ex.Message}");
        }
    }

    public async Task<EmailDeliveryStatus> GetEmailStatusAsync(
        string messageId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // SendGrid doesn't provide a direct API to get message status by ID
            // This would typically be handled through webhooks and stored in database
            // For now, return sent status as a placeholder
            _logger.LogDebug("Getting email status for {MessageId}", messageId);
            return EmailDeliveryStatus.Sent;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get email status for {MessageId}", messageId);
            return EmailDeliveryStatus.Failed;
        }
    }
}
