using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Enums;
using AnalyticsBIService.Domain.Repositories;
using AnalyticsBIService.Application.Interfaces;
using Microsoft.Extensions.Logging;

namespace AnalyticsBIService.Application.Services;

/// <summary>
/// Service for monitoring carrier pool health, regional activity, and availability trends
/// </summary>
public interface ICarrierPoolHealthService
{
    Task<CarrierPoolHealthDto> GetCarrierPoolHealthAsync(string? region = null, CancellationToken cancellationToken = default);
    Task<List<RegionalCarrierActivityDto>> GetRegionalCarrierActivityAsync(CancellationToken cancellationToken = default);
    Task<CarrierAvailabilityTrendsDto> GetCarrierAvailabilityTrendsAsync(string? region = null, TimePeriod period = TimePeriod.Daily, CancellationToken cancellationToken = default);
    Task<List<CarrierHealthScoreDto>> CalculateCarrierHealthScoresAsync(string? region = null, CancellationToken cancellationToken = default);
    Task<CarrierCapacityAnalysisDto> GetCarrierCapacityAnalysisAsync(string? region = null, CancellationToken cancellationToken = default);
    Task<List<CarrierPerformanceMetricDto>> GetCarrierPerformanceMetricsAsync(string? region = null, CancellationToken cancellationToken = default);
    Task UpdateCarrierActivityStatusAsync(Guid carrierId, string region, bool isActive, Dictionary<string, object> metadata, CancellationToken cancellationToken = default);
}

public class CarrierPoolHealthService : ICarrierPoolHealthService
{
    private readonly IMetricRepository _metricRepository;
    private readonly IAnalyticsEventRepository _analyticsEventRepository;
    private readonly ICacheService _cacheService;
    private readonly ILogger<CarrierPoolHealthService> _logger;

    private const string CACHE_KEY_PREFIX = "carrier_pool_health";
    private static readonly TimeSpan DefaultCacheExpiration = TimeSpan.FromMinutes(15);
    private static readonly TimeSpan RealTimeCacheExpiration = TimeSpan.FromMinutes(5);

    // Health score thresholds
    private const decimal EXCELLENT_HEALTH_THRESHOLD = 90m;
    private const decimal GOOD_HEALTH_THRESHOLD = 75m;
    private const decimal FAIR_HEALTH_THRESHOLD = 60m;

    // Activity thresholds
    private const int MIN_ACTIVE_CARRIERS_PER_REGION = 5;
    private const decimal MIN_ACTIVITY_PERCENTAGE = 70m;

    public CarrierPoolHealthService(
        IMetricRepository metricRepository,
        IAnalyticsEventRepository analyticsEventRepository,
        ICacheService cacheService,
        ILogger<CarrierPoolHealthService> logger)
    {
        _metricRepository = metricRepository;
        _analyticsEventRepository = analyticsEventRepository;
        _cacheService = cacheService;
        _logger = logger;
    }

    public async Task<CarrierPoolHealthDto> GetCarrierPoolHealthAsync(string? region = null, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting carrier pool health for region {Region}", region);

            var cacheKey = GenerateCacheKey("pool_health", region);
            var cachedHealth = await _cacheService.GetAsync<CarrierPoolHealthDto>(cacheKey, cancellationToken);

            if (cachedHealth != null)
            {
                return cachedHealth;
            }

            var health = new CarrierPoolHealthDto
            {
                Region = region,
                GeneratedAt = DateTime.UtcNow
            };

            // Get carrier activity data
            var last30Days = DateTime.UtcNow.AddDays(-30);
            var carrierActivityData = await GetCarrierActivityDataAsync(region, last30Days, cancellationToken);

            // Calculate basic metrics
            health.TotalCarriers = carrierActivityData.Count;
            health.ActiveCarriers = carrierActivityData.Count(c => c.IsActive);
            health.InactiveCarriers = health.TotalCarriers - health.ActiveCarriers;
            health.ActivityPercentage = health.TotalCarriers > 0 ?
                (decimal)health.ActiveCarriers / health.TotalCarriers * 100 : 0;

            // Calculate capacity metrics
            var capacityAnalysis = await GetCarrierCapacityAnalysisAsync(region, cancellationToken);
            health.TotalCapacity = capacityAnalysis.TotalCapacity;
            health.UtilizedCapacity = capacityAnalysis.UtilizedCapacity;
            health.AvailableCapacity = capacityAnalysis.AvailableCapacity;
            health.CapacityUtilizationPercentage = capacityAnalysis.UtilizationPercentage;

            // Calculate health scores
            var healthScores = await CalculateCarrierHealthScoresAsync(region, cancellationToken);
            health.AverageHealthScore = healthScores.Any() ? healthScores.Average(h => h.HealthScore) : 0;
            health.HealthyCarriers = healthScores.Count(h => h.HealthScore >= GOOD_HEALTH_THRESHOLD);
            health.AtRiskCarriers = healthScores.Count(h => h.HealthScore < FAIR_HEALTH_THRESHOLD);

            // Calculate performance metrics
            var performanceMetrics = await GetCarrierPerformanceMetricsAsync(region, cancellationToken);
            health.AverageOnTimePerformance = performanceMetrics.Any() ?
                performanceMetrics.Average(p => p.OnTimeDeliveryRate) : 0;
            health.AverageCustomerRating = performanceMetrics.Any() ?
                performanceMetrics.Average(p => p.AverageRating) : 0;

            // Determine overall health status
            health.OverallHealthStatus = DetermineOverallHealthStatus(health);
            health.HealthAlerts = GenerateHealthAlerts(health, carrierActivityData);

            // Get regional breakdown if no specific region requested
            if (string.IsNullOrEmpty(region))
            {
                health.RegionalBreakdown = await GetRegionalCarrierActivityAsync(cancellationToken);
            }

            // Cache the results
            await _cacheService.SetAsync(cacheKey, health, DefaultCacheExpiration, cancellationToken);

            return health;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting carrier pool health for region {Region}", region);
            throw;
        }
    }

    public async Task<List<RegionalCarrierActivityDto>> GetRegionalCarrierActivityAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = GenerateCacheKey("regional_activity");
            var cachedActivity = await _cacheService.GetAsync<List<RegionalCarrierActivityDto>>(cacheKey, cancellationToken);

            if (cachedActivity != null)
            {
                return cachedActivity;
            }

            _logger.LogDebug("Getting regional carrier activity");

            var last30Days = DateTime.UtcNow.AddDays(-30);

            // Get carrier activity by region
            var eventsResult = await _analyticsEventRepository.GetByDateRangeAsync(
                last30Days, DateTime.UtcNow, 1, int.MaxValue, cancellationToken);
            var regionalData = eventsResult.Items
                .Where(e => e.EventName == "CarrierActivityUpdate" &&
                           e.Properties.ContainsKey("region") &&
                           e.Properties.ContainsKey("isActive"))
                .GroupBy(e => e.GetProperty<string>("region"))
                .Select(g => new
                {
                    Region = g.Key,
                    Events = g.ToList()
                })
                .ToList();

            var regionalActivity = new List<RegionalCarrierActivityDto>();

            foreach (var regionGroup in regionalData)
            {
                var latestCarrierStates = regionGroup.Events
                    .GroupBy(e => e.UserId)
                    .Select(carrierGroup => carrierGroup.OrderByDescending(e => e.Timestamp).First())
                    .ToList();

                var totalCarriers = latestCarrierStates.Count;
                var activeCarriers = latestCarrierStates.Count(e => e.GetProperty<bool>("isActive"));
                var activityPercentage = totalCarriers > 0 ? (decimal)activeCarriers / totalCarriers * 100 : 0;

                regionalActivity.Add(new RegionalCarrierActivityDto
                {
                    Region = regionGroup.Region,
                    TotalCarriers = totalCarriers,
                    ActiveCarriers = activeCarriers,
                    InactiveCarriers = totalCarriers - activeCarriers,
                    ActivityPercentage = activityPercentage,
                    HealthStatus = DetermineRegionalHealthStatus(activityPercentage, totalCarriers),
                    LastUpdated = latestCarrierStates.Max(e => e.Timestamp)
                });
            }

            // Sort by activity percentage descending
            regionalActivity = regionalActivity.OrderByDescending(r => r.ActivityPercentage).ToList();

            await _cacheService.SetAsync(cacheKey, regionalActivity, DefaultCacheExpiration, cancellationToken);
            return regionalActivity;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting regional carrier activity");
            return new List<RegionalCarrierActivityDto>();
        }
    }

    public async Task<CarrierAvailabilityTrendsDto> GetCarrierAvailabilityTrendsAsync(string? region = null, TimePeriod period = TimePeriod.Daily, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = GenerateCacheKey("availability_trends", region, period);
            var cachedTrends = await _cacheService.GetAsync<CarrierAvailabilityTrendsDto>(cacheKey, cancellationToken);

            if (cachedTrends != null)
            {
                return cachedTrends;
            }

            _logger.LogDebug("Getting carrier availability trends for region {Region} with period {Period}", region, period);

            var trends = new CarrierAvailabilityTrendsDto
            {
                Region = region,
                Period = period.ToString(),
                GeneratedAt = DateTime.UtcNow
            };

            var endDate = DateTime.UtcNow;
            var startDate = period switch
            {
                TimePeriod.Hourly => endDate.AddHours(-24),
                TimePeriod.Daily => endDate.AddDays(-30),
                TimePeriod.Weekly => endDate.AddDays(-84),
                TimePeriod.Monthly => endDate.AddMonths(-12),
                _ => endDate.AddDays(-30)
            };

            // Get availability events for the period
            var availabilityEvents = await GetCarrierAvailabilityEventsAsync(region, startDate, endDate, cancellationToken);

            // Calculate trend data points
            trends.TrendData = await CalculateAvailabilityTrendDataAsync(availabilityEvents, period, startDate, endDate);

            // Calculate trend analysis
            if (trends.TrendData.Count >= 2)
            {
                var firstPoint = trends.TrendData.First();
                var lastPoint = trends.TrendData.Last();

                trends.TrendDirection = lastPoint.ActivityPercentage > firstPoint.ActivityPercentage ? "Increasing" :
                                       lastPoint.ActivityPercentage < firstPoint.ActivityPercentage ? "Decreasing" : "Stable";

                trends.TrendPercentageChange = firstPoint.ActivityPercentage > 0 ?
                    (lastPoint.ActivityPercentage - firstPoint.ActivityPercentage) / firstPoint.ActivityPercentage * 100 : 0;
            }

            await _cacheService.SetAsync(cacheKey, trends, DefaultCacheExpiration, cancellationToken);
            return trends;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting carrier availability trends");
            throw;
        }
    }

    public async Task<List<CarrierHealthScoreDto>> CalculateCarrierHealthScoresAsync(string? region = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = GenerateCacheKey("health_scores", region);
            var cachedScores = await _cacheService.GetAsync<List<CarrierHealthScoreDto>>(cacheKey, cancellationToken);

            if (cachedScores != null)
            {
                return cachedScores;
            }

            _logger.LogDebug("Calculating carrier health scores for region {Region}", region);

            var last30Days = DateTime.UtcNow.AddDays(-30);
            var healthScores = new List<CarrierHealthScoreDto>();

            // Get carrier performance data
            var carrierData = await GetCarrierPerformanceDataAsync(region, last30Days, cancellationToken);

            foreach (var carrier in carrierData)
            {
                var healthScore = CalculateIndividualHealthScore(carrier);

                healthScores.Add(new CarrierHealthScoreDto
                {
                    CarrierId = carrier.CarrierId,
                    Region = carrier.Region,
                    HealthScore = healthScore,
                    HealthStatus = DetermineHealthStatus(healthScore),
                    OnTimeDeliveryRate = carrier.OnTimeDeliveryRate,
                    AverageRating = carrier.AverageRating,
                    ActiveDays = carrier.ActiveDays,
                    TotalTrips = carrier.TotalTrips,
                    LastActiveDate = carrier.LastActiveDate,
                    RiskFactors = IdentifyRiskFactors(carrier)
                });
            }

            // Sort by health score descending
            healthScores = healthScores.OrderByDescending(h => h.HealthScore).ToList();

            await _cacheService.SetAsync(cacheKey, healthScores, DefaultCacheExpiration, cancellationToken);
            return healthScores;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating carrier health scores");
            return new List<CarrierHealthScoreDto>();
        }
    }

    public async Task<CarrierCapacityAnalysisDto> GetCarrierCapacityAnalysisAsync(string? region = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = GenerateCacheKey("capacity_analysis", region);
            var cachedAnalysis = await _cacheService.GetAsync<CarrierCapacityAnalysisDto>(cacheKey, cancellationToken);

            if (cachedAnalysis != null)
            {
                return cachedAnalysis;
            }

            _logger.LogDebug("Getting carrier capacity analysis for region {Region}", region);

            var analysis = new CarrierCapacityAnalysisDto
            {
                Region = region,
                GeneratedAt = DateTime.UtcNow
            };

            var last7Days = DateTime.UtcNow.AddDays(-7);

            // Get capacity events
            var capacityEvents = await GetCarrierCapacityEventsAsync(region, last7Days, cancellationToken);

            // Calculate capacity metrics
            analysis.TotalCapacity = capacityEvents.Sum(e => e.GetProperty<decimal>("totalCapacity"));
            analysis.UtilizedCapacity = capacityEvents.Sum(e => e.GetProperty<decimal>("utilizedCapacity"));
            analysis.AvailableCapacity = analysis.TotalCapacity - analysis.UtilizedCapacity;
            analysis.UtilizationPercentage = analysis.TotalCapacity > 0 ?
                analysis.UtilizedCapacity / analysis.TotalCapacity * 100 : 0;

            // Calculate peak utilization times
            analysis.PeakUtilizationHours = await CalculatePeakUtilizationHoursAsync(capacityEvents);

            // Get capacity by vehicle type
            analysis.CapacityByVehicleType = await GetCapacityByVehicleTypeAsync(capacityEvents);

            // Calculate capacity trends
            analysis.CapacityTrend = await CalculateCapacityTrendAsync(region, cancellationToken);

            await _cacheService.SetAsync(cacheKey, analysis, DefaultCacheExpiration, cancellationToken);
            return analysis;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting carrier capacity analysis");
            throw;
        }
    }

    public async Task<List<CarrierPerformanceMetricDto>> GetCarrierPerformanceMetricsAsync(string? region = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = GenerateCacheKey("performance_metrics", region);
            var cachedMetrics = await _cacheService.GetAsync<List<CarrierPerformanceMetricDto>>(cacheKey, cancellationToken);

            if (cachedMetrics != null)
            {
                return cachedMetrics;
            }

            _logger.LogDebug("Getting carrier performance metrics for region {Region}", region);

            var last30Days = DateTime.UtcNow.AddDays(-30);
            var performanceData = await GetCarrierPerformanceDataAsync(region, last30Days, cancellationToken);

            var metrics = performanceData.Select(data => new CarrierPerformanceMetricDto
            {
                CarrierId = data.CarrierId,
                Region = data.Region,
                OnTimeDeliveryRate = data.OnTimeDeliveryRate,
                AverageRating = data.AverageRating,
                TotalTrips = data.TotalTrips,
                CompletedTrips = data.CompletedTrips,
                CancelledTrips = data.CancelledTrips,
                AverageResponseTime = data.AverageResponseTime,
                CustomerSatisfactionScore = data.CustomerSatisfactionScore,
                ReliabilityScore = CalculateReliabilityScore(data),
                EfficiencyScore = CalculateEfficiencyScore(data)
            }).OrderByDescending(m => m.OnTimeDeliveryRate).ToList();

            await _cacheService.SetAsync(cacheKey, metrics, DefaultCacheExpiration, cancellationToken);
            return metrics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting carrier performance metrics");
            return new List<CarrierPerformanceMetricDto>();
        }
    }

    public async Task UpdateCarrierActivityStatusAsync(Guid carrierId, string region, bool isActive, Dictionary<string, object> metadata, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Updating carrier activity status for carrier {CarrierId} in region {Region}: {IsActive}",
                carrierId, region, isActive);

            var analyticsEvent = new AnalyticsEvent(
                eventName: "CarrierActivityUpdate",
                eventType: AnalyticsEventType.SystemPerformance,
                dataSource: DataSourceType.NetworkFleetManagement,
                userId: carrierId,
                entityId: carrierId,
                entityType: "Carrier",
                properties: new Dictionary<string, object>(metadata)
                {
                    ["region"] = region,
                    ["isActive"] = isActive,
                    ["updatedAt"] = DateTime.UtcNow
                });

            await _analyticsEventRepository.AddAsync(analyticsEvent, cancellationToken);

            // Invalidate related caches
            await InvalidateCarrierPoolCachesAsync(region, cancellationToken);

            _logger.LogDebug("Successfully updated carrier activity status for carrier {CarrierId}", carrierId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating carrier activity status for carrier {CarrierId}", carrierId);
            throw;
        }
    }

    // Private helper methods
    private async Task<List<CarrierActivityData>> GetCarrierActivityDataAsync(string? region, DateTime since, CancellationToken cancellationToken)
    {
        var eventsResult = await _analyticsEventRepository.GetByDateRangeAsync(
            since, DateTime.UtcNow, 1, int.MaxValue, cancellationToken);

        var events = eventsResult.Items
            .Where(e => e.EventName == "CarrierActivityUpdate" &&
                       e.Properties.ContainsKey("isActive"));

        if (!string.IsNullOrEmpty(region))
        {
            events = events.Where(e => e.Properties.ContainsKey("region") &&
                               e.GetProperty<string>("region") == region);
        }

        var eventsList = events.ToList();

        return eventsList.GroupBy(e => e.UserId)
                    .Select(g => g.OrderByDescending(e => e.Timestamp).First())
                    .Select(e => new CarrierActivityData
                    {
                        CarrierId = e.UserId ?? Guid.Empty,
                        Region = e.GetProperty<string>("region") ?? "Unknown",
                        IsActive = e.GetProperty<bool>("isActive"),
                        LastUpdated = e.Timestamp
                    })
                    .ToList();
    }

    private async Task<List<CarrierPerformanceData>> GetCarrierPerformanceDataAsync(string? region, DateTime since, CancellationToken cancellationToken)
    {
        // This would typically query multiple event types to build comprehensive performance data
        var eventsResult = await _analyticsEventRepository.GetByDateRangeAsync(
            since, DateTime.UtcNow, 1, int.MaxValue, cancellationToken);
        var performanceEvents = eventsResult.Items
            .Where(e => e.EntityType == "Carrier" &&
                       (e.EventName == "TripCompleted" || e.EventName == "TripRated" || e.EventName == "CarrierActivityUpdate"))
            .ToList();

        var carrierGroups = performanceEvents.GroupBy(e => e.UserId ?? e.EntityId ?? Guid.Empty);

        var performanceData = new List<CarrierPerformanceData>();

        foreach (var carrierGroup in carrierGroups)
        {
            var carrierId = carrierGroup.Key;
            var events = carrierGroup.ToList();

            var tripEvents = events.Where(e => e.EventName == "TripCompleted").ToList();
            var ratingEvents = events.Where(e => e.EventName == "TripRated").ToList();
            var activityEvents = events.Where(e => e.EventName == "CarrierActivityUpdate").ToList();

            var carrierRegion = activityEvents.LastOrDefault()?.GetProperty<string>("region") ?? "Unknown";

            if (!string.IsNullOrEmpty(region) && carrierRegion != region)
                continue;

            var onTimeTrips = tripEvents.Count(e => e.Properties.ContainsKey("onTime") && e.GetProperty<bool>("onTime"));
            var totalTrips = tripEvents.Count;
            var completedTrips = tripEvents.Count(e => e.Properties.ContainsKey("status") && e.GetProperty<string>("status") == "Completed");
            var cancelledTrips = tripEvents.Count(e => e.Properties.ContainsKey("status") && e.GetProperty<string>("status") == "Cancelled");

            var ratings = ratingEvents.Where(e => e.Properties.ContainsKey("rating"))
                                    .Select(e => e.GetProperty<decimal>("rating"))
                                    .ToList();

            var activeDays = activityEvents.Where(e => e.GetProperty<bool>("isActive"))
                                         .Select(e => e.Timestamp.Date)
                                         .Distinct()
                                         .Count();

            performanceData.Add(new CarrierPerformanceData
            {
                CarrierId = carrierId,
                Region = carrierRegion,
                TotalTrips = totalTrips,
                CompletedTrips = completedTrips,
                CancelledTrips = cancelledTrips,
                OnTimeDeliveryRate = totalTrips > 0 ? (decimal)onTimeTrips / totalTrips * 100 : 0,
                AverageRating = ratings.Any() ? ratings.Average() : 0,
                ActiveDays = activeDays,
                LastActiveDate = activityEvents.Where(e => e.GetProperty<bool>("isActive"))
                                              .Max(e => e.Timestamp),
                AverageResponseTime = CalculateAverageResponseTime(events),
                CustomerSatisfactionScore = ratings.Any() ? ratings.Average() * 20 : 0 // Convert 5-star to 100-point scale
            });
        }

        return performanceData;
    }

    private async Task<List<AnalyticsEvent>> GetCarrierAvailabilityEventsAsync(string? region, DateTime startDate, DateTime endDate, CancellationToken cancellationToken)
    {
        var eventsResult = await _analyticsEventRepository.GetByDateRangeAsync(
            startDate, endDate, 1, int.MaxValue, cancellationToken);

        var events = eventsResult.Items
            .Where(e => e.EventName == "CarrierActivityUpdate" &&
                       e.Properties.ContainsKey("isActive"));

        if (!string.IsNullOrEmpty(region))
        {
            events = events.Where(e => e.Properties.ContainsKey("region") &&
                               e.GetProperty<string>("region") == region);
        }

        return events.OrderBy(e => e.Timestamp).ToList();
    }

    private async Task<List<AnalyticsEvent>> GetCarrierCapacityEventsAsync(string? region, DateTime since, CancellationToken cancellationToken)
    {
        var eventsResult = await _analyticsEventRepository.GetByDateRangeAsync(
            since, DateTime.UtcNow, 1, int.MaxValue, cancellationToken);

        var events = eventsResult.Items
            .Where(e => e.EventName == "CarrierCapacityUpdate" &&
                       e.Properties.ContainsKey("totalCapacity") &&
                       e.Properties.ContainsKey("utilizedCapacity"));

        if (!string.IsNullOrEmpty(region))
        {
            events = events.Where(e => e.Properties.ContainsKey("region") &&
                               e.GetProperty<string>("region") == region);
        }

        return events.ToList();
    }

    private async Task<List<CarrierAvailabilityTrendDataDto>> CalculateAvailabilityTrendDataAsync(List<AnalyticsEvent> events, TimePeriod period, DateTime startDate, DateTime endDate)
    {
        var trendData = new List<CarrierAvailabilityTrendDataDto>();
        var current = startDate;

        while (current <= endDate)
        {
            var periodEnd = period switch
            {
                TimePeriod.Hourly => current.AddHours(1),
                TimePeriod.Daily => current.AddDays(1),
                TimePeriod.Weekly => current.AddDays(7),
                TimePeriod.Monthly => current.AddMonths(1),
                _ => current.AddDays(1)
            };

            var periodEvents = events.Where(e => e.Timestamp >= current && e.Timestamp < periodEnd).ToList();

            // Get latest status for each carrier in this period
            var latestCarrierStates = periodEvents.GroupBy(e => e.UserId)
                                                 .Select(g => g.OrderByDescending(e => e.Timestamp).First())
                                                 .ToList();

            var totalCarriers = latestCarrierStates.Count;
            var activeCarriers = latestCarrierStates.Count(e => e.GetProperty<bool>("isActive"));
            var activityPercentage = totalCarriers > 0 ? (decimal)activeCarriers / totalCarriers * 100 : 0;

            trendData.Add(new CarrierAvailabilityTrendDataDto
            {
                Timestamp = current,
                TotalCarriers = totalCarriers,
                ActiveCarriers = activeCarriers,
                ActivityPercentage = activityPercentage
            });

            current = periodEnd;
        }

        return trendData;
    }

    private decimal CalculateIndividualHealthScore(CarrierPerformanceData carrier)
    {
        var score = 0m;

        // On-time delivery (40% weight)
        score += carrier.OnTimeDeliveryRate * 0.4m;

        // Customer rating (30% weight)
        score += (carrier.AverageRating / 5 * 100) * 0.3m;

        // Activity level (20% weight)
        var activityScore = Math.Min(100, carrier.ActiveDays * 3.33m); // 30 days = 100%
        score += activityScore * 0.2m;

        // Reliability (10% weight)
        var reliabilityScore = carrier.TotalTrips > 0 ?
            (decimal)carrier.CompletedTrips / carrier.TotalTrips * 100 : 0;
        score += reliabilityScore * 0.1m;

        return Math.Min(100, Math.Max(0, score));
    }

    private string DetermineHealthStatus(decimal healthScore)
    {
        return healthScore switch
        {
            >= EXCELLENT_HEALTH_THRESHOLD => "Excellent",
            >= GOOD_HEALTH_THRESHOLD => "Good",
            >= FAIR_HEALTH_THRESHOLD => "Fair",
            _ => "Poor"
        };
    }

    private string DetermineOverallHealthStatus(CarrierPoolHealthDto health)
    {
        if (health.ActivityPercentage < MIN_ACTIVITY_PERCENTAGE || health.TotalCarriers < MIN_ACTIVE_CARRIERS_PER_REGION)
            return "Critical";

        if (health.AverageHealthScore >= EXCELLENT_HEALTH_THRESHOLD && health.ActivityPercentage >= 90)
            return "Excellent";

        if (health.AverageHealthScore >= GOOD_HEALTH_THRESHOLD && health.ActivityPercentage >= 80)
            return "Good";

        if (health.AverageHealthScore >= FAIR_HEALTH_THRESHOLD && health.ActivityPercentage >= 70)
            return "Fair";

        return "Poor";
    }

    private string DetermineRegionalHealthStatus(decimal activityPercentage, int totalCarriers)
    {
        if (totalCarriers < MIN_ACTIVE_CARRIERS_PER_REGION)
            return "Insufficient Coverage";

        return activityPercentage switch
        {
            >= 90 => "Excellent",
            >= 80 => "Good",
            >= 70 => "Fair",
            _ => "Poor"
        };
    }

    private List<string> GenerateHealthAlerts(CarrierPoolHealthDto health, List<CarrierActivityData> carrierData)
    {
        var alerts = new List<string>();

        if (health.ActivityPercentage < MIN_ACTIVITY_PERCENTAGE)
            alerts.Add($"Low carrier activity: {health.ActivityPercentage:F1}% (target: {MIN_ACTIVITY_PERCENTAGE}%)");

        if (health.TotalCarriers < MIN_ACTIVE_CARRIERS_PER_REGION)
            alerts.Add($"Insufficient carrier coverage: {health.TotalCarriers} carriers (minimum: {MIN_ACTIVE_CARRIERS_PER_REGION})");

        if (health.CapacityUtilizationPercentage > 90)
            alerts.Add($"High capacity utilization: {health.CapacityUtilizationPercentage:F1}% - consider expanding carrier pool");

        if (health.AtRiskCarriers > health.TotalCarriers * 0.2m)
            alerts.Add($"High number of at-risk carriers: {health.AtRiskCarriers} ({(decimal)health.AtRiskCarriers / health.TotalCarriers * 100:F1}%)");

        return alerts;
    }

    private List<string> IdentifyRiskFactors(CarrierPerformanceData carrier)
    {
        var riskFactors = new List<string>();

        if (carrier.OnTimeDeliveryRate < 80)
            riskFactors.Add($"Low on-time delivery rate: {carrier.OnTimeDeliveryRate:F1}%");

        if (carrier.AverageRating < 3.5m)
            riskFactors.Add($"Low customer rating: {carrier.AverageRating:F1}/5");

        if (carrier.ActiveDays < 20)
            riskFactors.Add($"Low activity: {carrier.ActiveDays} active days in last 30 days");

        if (carrier.CancelledTrips > carrier.TotalTrips * 0.1m)
            riskFactors.Add($"High cancellation rate: {(decimal)carrier.CancelledTrips / carrier.TotalTrips * 100:F1}%");

        return riskFactors;
    }

    private decimal CalculateReliabilityScore(CarrierPerformanceData data)
    {
        if (data.TotalTrips == 0) return 0;

        var completionRate = (decimal)data.CompletedTrips / data.TotalTrips * 100;
        var onTimeWeight = data.OnTimeDeliveryRate * 0.6m;
        var completionWeight = completionRate * 0.4m;

        return onTimeWeight + completionWeight;
    }

    private decimal CalculateEfficiencyScore(CarrierPerformanceData data)
    {
        var baseScore = 50m;

        // Adjust based on response time
        if (data.AverageResponseTime <= 2) baseScore += 30;
        else if (data.AverageResponseTime <= 6) baseScore += 20;
        else if (data.AverageResponseTime <= 24) baseScore += 10;

        // Adjust based on activity level
        var activityBonus = Math.Min(20, data.ActiveDays * 0.67m);
        baseScore += activityBonus;

        return Math.Min(100, baseScore);
    }

    private decimal CalculateAverageResponseTime(List<AnalyticsEvent> events)
    {
        var responseTimes = events.Where(e => e.Properties.ContainsKey("responseTimeHours"))
                                 .Select(e => e.GetProperty<decimal>("responseTimeHours"))
                                 .ToList();

        return responseTimes.Any() ? responseTimes.Average() : 24; // Default to 24 hours if no data
    }

    // Placeholder implementations for complex calculations
    private async Task<List<int>> CalculatePeakUtilizationHoursAsync(List<AnalyticsEvent> capacityEvents)
    {
        var hourlyUtilization = capacityEvents.GroupBy(e => e.Timestamp.Hour)
                                            .Select(g => new { Hour = g.Key, AvgUtilization = g.Average(e => e.GetProperty<decimal>("utilizedCapacity")) })
                                            .OrderByDescending(x => x.AvgUtilization)
                                            .Take(3)
                                            .Select(x => x.Hour)
                                            .ToList();

        return hourlyUtilization;
    }

    private async Task<Dictionary<string, decimal>> GetCapacityByVehicleTypeAsync(List<AnalyticsEvent> capacityEvents)
    {
        return capacityEvents.Where(e => e.Properties.ContainsKey("vehicleType"))
                           .GroupBy(e => e.GetProperty<string>("vehicleType"))
                           .ToDictionary(g => g.Key, g => g.Sum(e => e.GetProperty<decimal>("totalCapacity")));
    }

    private async Task<string> CalculateCapacityTrendAsync(string? region, CancellationToken cancellationToken)
    {
        // Simplified trend calculation - would be more sophisticated in real implementation
        var last7Days = DateTime.UtcNow.AddDays(-7);
        var last14Days = DateTime.UtcNow.AddDays(-14);

        var recentCapacity = await GetAverageCapacityForPeriodAsync(region, last7Days, DateTime.UtcNow, cancellationToken);
        var previousCapacity = await GetAverageCapacityForPeriodAsync(region, last14Days, last7Days, cancellationToken);

        if (recentCapacity > previousCapacity * 1.05m) return "Increasing";
        if (recentCapacity < previousCapacity * 0.95m) return "Decreasing";
        return "Stable";
    }

    private async Task<decimal> GetAverageCapacityForPeriodAsync(string? region, DateTime start, DateTime end, CancellationToken cancellationToken)
    {
        var events = await GetCarrierCapacityEventsAsync(region, start, cancellationToken);
        var periodEvents = events.Where(e => e.Timestamp >= start && e.Timestamp <= end).ToList();

        return periodEvents.Any() ? periodEvents.Average(e => e.GetProperty<decimal>("totalCapacity")) : 0;
    }

    private async Task InvalidateCarrierPoolCachesAsync(string? region, CancellationToken cancellationToken)
    {
        var patterns = new[]
        {
            $"{CACHE_KEY_PREFIX}:pool_health:*",
            $"{CACHE_KEY_PREFIX}:regional_activity:*",
            $"{CACHE_KEY_PREFIX}:availability_trends:*",
            $"{CACHE_KEY_PREFIX}:health_scores:*",
            $"{CACHE_KEY_PREFIX}:capacity_analysis:*",
            $"{CACHE_KEY_PREFIX}:performance_metrics:*"
        };

        foreach (var pattern in patterns)
        {
            await _cacheService.RemoveByPatternAsync(pattern, cancellationToken);
        }
    }

    private string GenerateCacheKey(string prefix, params object?[] parameters)
    {
        var keyParts = new List<string> { CACHE_KEY_PREFIX, prefix };

        foreach (var param in parameters)
        {
            if (param != null)
            {
                keyParts.Add(param.ToString()!);
            }
        }

        return string.Join(":", keyParts);
    }
}

// Supporting classes
public class CarrierActivityData
{
    public Guid CarrierId { get; set; }
    public string Region { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public DateTime LastUpdated { get; set; }
}

public class CarrierPerformanceData
{
    public Guid CarrierId { get; set; }
    public string Region { get; set; } = string.Empty;
    public int TotalTrips { get; set; }
    public int CompletedTrips { get; set; }
    public int CancelledTrips { get; set; }
    public decimal OnTimeDeliveryRate { get; set; }
    public decimal AverageRating { get; set; }
    public int ActiveDays { get; set; }
    public DateTime LastActiveDate { get; set; }
    public decimal AverageResponseTime { get; set; }
    public decimal CustomerSatisfactionScore { get; set; }
}
