-- Tax Support Database Setup Script
-- This script creates the necessary tables and indexes for tax functionality

-- Create TaxCategories table
CREATE TABLE subscription."TaxCategories" (
    "Id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "Name" VARCHAR(100) NOT NULL,
    "Description" VARCHAR(500) NOT NULL,
    "Code" VARCHAR(10) NOT NULL UNIQUE,
    "IsActive" BOOLEAN NOT NULL DEFAULT true,
    "CreatedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP NULL
);

-- Create TaxCategoryConfigurations table
CREATE TABLE subscription."TaxCategoryConfigurations" (
    "Id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "TaxCategoryId" UUID NOT NULL REFERENCES subscription."TaxCategories"("Id") ON DELETE CASCADE,
    "TaxType" VARCHAR(50) NOT NULL,
    "Rate" DECIMAL(5,4) NOT NULL,
    "IsIncluded" BOOLEAN NOT NULL,
    "EffectiveDate" TIMESTAMP NOT NULL,
    "ExpirationDate" TIMESTAMP NULL,
    "ApplicableRegions" VARCHAR(1000) NOT NULL
);

-- Create TaxExemptions table
CREATE TABLE subscription."TaxExemptions" (
    "Id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "UserId" UUID NOT NULL,
    "ExemptionType" VARCHAR(100) NOT NULL,
    "ExemptionNumber" VARCHAR(50) NOT NULL,
    "IssuingAuthority" VARCHAR(200) NOT NULL,
    "ValidFrom" TIMESTAMP NOT NULL,
    "ValidTo" TIMESTAMP NOT NULL,
    "ExemptTaxTypes" VARCHAR(500) NOT NULL,
    "ApplicableRegions" VARCHAR(1000) NOT NULL,
    "IsActive" BOOLEAN NOT NULL DEFAULT true,
    "DocumentPath" VARCHAR(500) NULL,
    "VerifiedAt" TIMESTAMP NULL,
    "VerifiedByUserId" UUID NULL,
    "VerificationNotes" VARCHAR(1000) NULL,
    "CreatedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP NULL
);

-- Create GlobalTaxConfigurations table
CREATE TABLE subscription."GlobalTaxConfigurations" (
    "Id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "TaxType" VARCHAR(50) NOT NULL,
    "Rate" DECIMAL(5,4) NOT NULL,
    "IsIncluded" BOOLEAN NOT NULL,
    "EffectiveDate" TIMESTAMP NOT NULL,
    "ExpirationDate" TIMESTAMP NULL,
    "ApplicableRegions" VARCHAR(1000) NOT NULL,
    "IsActive" BOOLEAN NOT NULL DEFAULT true,
    "Priority" INTEGER NOT NULL DEFAULT 0,
    "Description" VARCHAR(500) NULL,
    "CreatedByUserId" UUID NOT NULL,
    "ModifiedByUserId" UUID NULL,
    "CreatedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP NULL
);

-- Create PlanTaxConfigurations table
CREATE TABLE subscription."PlanTaxConfigurations" (
    "Id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "PlanId" UUID NOT NULL REFERENCES subscription."Plans"("Id") ON DELETE CASCADE,
    "TaxType" VARCHAR(50) NOT NULL,
    "Rate" DECIMAL(5,4) NOT NULL,
    "IsIncluded" BOOLEAN NOT NULL,
    "TaxEffectiveDate" TIMESTAMP NOT NULL,
    "TaxExpirationDate" TIMESTAMP NULL,
    "ApplicableRegions" VARCHAR(1000) NOT NULL,
    "IsActive" BOOLEAN NOT NULL DEFAULT true,
    "EffectiveFrom" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "EffectiveTo" TIMESTAMP NULL,
    "Notes" VARCHAR(1000) NULL,
    "CreatedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP NULL
);

-- Add TaxCategoryId column to Plans table
ALTER TABLE subscription."Plans" ADD COLUMN "TaxCategoryId" UUID NULL;
ALTER TABLE subscription."Plans" ADD CONSTRAINT "FK_Plans_TaxCategories_TaxCategoryId" 
    FOREIGN KEY ("TaxCategoryId") REFERENCES subscription."TaxCategories"("Id") ON DELETE SET NULL;

-- Create indexes for TaxCategories
CREATE INDEX "IX_TaxCategories_Name" ON subscription."TaxCategories"("Name");
CREATE INDEX "IX_TaxCategories_IsActive" ON subscription."TaxCategories"("IsActive");
CREATE INDEX "IX_TaxCategories_CreatedAt" ON subscription."TaxCategories"("CreatedAt");

-- Create indexes for TaxCategoryConfigurations
CREATE INDEX "IX_TaxCategoryConfigurations_TaxCategoryId" ON subscription."TaxCategoryConfigurations"("TaxCategoryId");
CREATE INDEX "IX_TaxCategoryConfigurations_TaxType" ON subscription."TaxCategoryConfigurations"("TaxType");
CREATE INDEX "IX_TaxCategoryConfigurations_EffectiveDate" ON subscription."TaxCategoryConfigurations"("EffectiveDate");

-- Create indexes for TaxExemptions
CREATE INDEX "IX_TaxExemptions_UserId" ON subscription."TaxExemptions"("UserId");
CREATE INDEX "IX_TaxExemptions_ExemptionNumber" ON subscription."TaxExemptions"("ExemptionNumber");
CREATE INDEX "IX_TaxExemptions_IsActive" ON subscription."TaxExemptions"("IsActive");
CREATE INDEX "IX_TaxExemptions_ValidFrom" ON subscription."TaxExemptions"("ValidFrom");
CREATE INDEX "IX_TaxExemptions_ValidTo" ON subscription."TaxExemptions"("ValidTo");
CREATE INDEX "IX_TaxExemptions_VerifiedAt" ON subscription."TaxExemptions"("VerifiedAt");
CREATE INDEX "IX_TaxExemptions_CreatedAt" ON subscription."TaxExemptions"("CreatedAt");
CREATE INDEX "IX_TaxExemptions_UserId_IsActive" ON subscription."TaxExemptions"("UserId", "IsActive");
CREATE UNIQUE INDEX "IX_TaxExemptions_ExemptionNumber_UserId" ON subscription."TaxExemptions"("ExemptionNumber", "UserId");

-- Create indexes for GlobalTaxConfigurations
CREATE INDEX "IX_GlobalTaxConfigurations_IsActive" ON subscription."GlobalTaxConfigurations"("IsActive");
CREATE INDEX "IX_GlobalTaxConfigurations_Priority" ON subscription."GlobalTaxConfigurations"("Priority");
CREATE INDEX "IX_GlobalTaxConfigurations_CreatedByUserId" ON subscription."GlobalTaxConfigurations"("CreatedByUserId");
CREATE INDEX "IX_GlobalTaxConfigurations_CreatedAt" ON subscription."GlobalTaxConfigurations"("CreatedAt");
CREATE INDEX "IX_GlobalTaxConfigurations_TaxType" ON subscription."GlobalTaxConfigurations"("TaxType");
CREATE INDEX "IX_GlobalTaxConfigurations_EffectiveDate" ON subscription."GlobalTaxConfigurations"("EffectiveDate");
CREATE INDEX "IX_GlobalTaxConfigurations_ExpirationDate" ON subscription."GlobalTaxConfigurations"("ExpirationDate");
CREATE INDEX "IX_GlobalTaxConfigurations_IsActive_Priority" ON subscription."GlobalTaxConfigurations"("IsActive", "Priority");
CREATE INDEX "IX_GlobalTaxConfigurations_TaxType_IsActive" ON subscription."GlobalTaxConfigurations"("TaxType", "IsActive");

-- Create indexes for PlanTaxConfigurations
CREATE INDEX "IX_PlanTaxConfigurations_PlanId" ON subscription."PlanTaxConfigurations"("PlanId");
CREATE INDEX "IX_PlanTaxConfigurations_IsActive" ON subscription."PlanTaxConfigurations"("IsActive");
CREATE INDEX "IX_PlanTaxConfigurations_EffectiveFrom" ON subscription."PlanTaxConfigurations"("EffectiveFrom");
CREATE INDEX "IX_PlanTaxConfigurations_EffectiveTo" ON subscription."PlanTaxConfigurations"("EffectiveTo");
CREATE INDEX "IX_PlanTaxConfigurations_CreatedAt" ON subscription."PlanTaxConfigurations"("CreatedAt");
CREATE INDEX "IX_PlanTaxConfigurations_TaxType" ON subscription."PlanTaxConfigurations"("TaxType");
CREATE INDEX "IX_PlanTaxConfigurations_PlanId_IsActive" ON subscription."PlanTaxConfigurations"("PlanId", "IsActive");
CREATE INDEX "IX_PlanTaxConfigurations_PlanId_EffectiveFrom_EffectiveTo" ON subscription."PlanTaxConfigurations"("PlanId", "EffectiveFrom", "EffectiveTo");
CREATE UNIQUE INDEX "IX_PlanTaxConfigurations_PlanId_TaxType_Active" ON subscription."PlanTaxConfigurations"("PlanId", "TaxType") WHERE "IsActive" = true;

-- Create index for Plans.TaxCategoryId
CREATE INDEX "IX_Plans_TaxCategoryId" ON subscription."Plans"("TaxCategoryId");

-- Insert default tax categories for India
INSERT INTO subscription."TaxCategories" ("Name", "Description", "Code", "IsActive") VALUES
('Standard GST', 'Standard Goods and Services Tax for India', 'GST_STD', true),
('Service Tax', 'Service Tax for digital services', 'SVC_TAX', true),
('TDS', 'Tax Deducted at Source', 'TDS', true);

-- Insert default global tax configurations for India
INSERT INTO subscription."GlobalTaxConfigurations" 
("TaxType", "Rate", "IsIncluded", "EffectiveDate", "ApplicableRegions", "IsActive", "Priority", "Description", "CreatedByUserId") VALUES
('GST', 18.0000, false, CURRENT_TIMESTAMP, 'IN', true, 1, 'Standard GST rate for digital services in India', '00000000-0000-0000-0000-000000000001'),
('TDS', 2.0000, false, CURRENT_TIMESTAMP, 'IN', true, 2, 'TDS for digital services in India', '00000000-0000-0000-0000-000000000001');

-- Create a function to calculate effective tax rate for a region
CREATE OR REPLACE FUNCTION subscription.calculate_effective_tax_rate(
    p_region VARCHAR(10),
    p_calculation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) RETURNS DECIMAL(5,4) AS $$
DECLARE
    total_rate DECIMAL(5,4) := 0;
BEGIN
    SELECT COALESCE(SUM("Rate"), 0)
    INTO total_rate
    FROM subscription."GlobalTaxConfigurations"
    WHERE "IsActive" = true
      AND "ApplicableRegions" LIKE '%' || p_region || '%'
      AND "EffectiveDate" <= p_calculation_date
      AND ("ExpirationDate" IS NULL OR "ExpirationDate" >= p_calculation_date);
    
    RETURN total_rate;
END;
$$ LANGUAGE plpgsql;

-- Create a function to check if user has tax exemption
CREATE OR REPLACE FUNCTION subscription.check_tax_exemption(
    p_user_id UUID,
    p_tax_type VARCHAR(50),
    p_region VARCHAR(10),
    p_check_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) RETURNS BOOLEAN AS $$
DECLARE
    exemption_exists BOOLEAN := false;
BEGIN
    SELECT EXISTS(
        SELECT 1
        FROM subscription."TaxExemptions"
        WHERE "UserId" = p_user_id
          AND "IsActive" = true
          AND "ValidFrom" <= p_check_date
          AND "ValidTo" >= p_check_date
          AND "ExemptTaxTypes" LIKE '%' || p_tax_type || '%'
          AND "ApplicableRegions" LIKE '%' || p_region || '%'
          AND "VerifiedAt" IS NOT NULL
    ) INTO exemption_exists;
    
    RETURN exemption_exists;
END;
$$ LANGUAGE plpgsql;

-- Grant permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA subscription TO subscription_user;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA subscription TO subscription_user;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA subscription TO subscription_user;
