using MediatR;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Entities;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.DTOs;
using OrderManagement.Domain.Enums;


namespace OrderManagement.Application.Queries.GetTransporterLoadPostings;

public class GetTransporterLoadPostingsQueryHandler : IRequestHandler<GetTransporterLoadPostingsQuery, TransporterLoadPostingAnalyticsDto>
{
    private readonly IRfqRepository _rfqRepository;
    private readonly IOrderRepository _orderRepository;
    private readonly ILogger<GetTransporterLoadPostingsQueryHandler> _logger;

    public GetTransporterLoadPostingsQueryHandler(
        IRfqRepository rfqRepository,
        IOrderRepository orderRepository,
        ILogger<GetTransporterLoadPostingsQueryHandler> logger)
    {
        _rfqRepository = rfqRepository;
        _orderRepository = orderRepository;
        _logger = logger;
    }

    public async Task<TransporterLoadPostingAnalyticsDto> Handle(GetTransporterLoadPostingsQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting load posting analytics for transporter {TransporterId} from {FromDate} to {ToDate}",
            request.TransporterId, request.FromDate, request.ToDate);

        // Base query for transporter's RFQs (load postings) within date range
        var rfqs = await _rfqRepository.GetRfqsByTransportCompanyAsync(request.TransporterId, cancellationToken);

        // Apply filters to the list
        var filteredRfqs = rfqs.AsEnumerable();

        if (request.FromDate.HasValue)
            filteredRfqs = filteredRfqs.Where(r => r.CreatedAt >= request.FromDate.Value);

        if (request.ToDate.HasValue)
            filteredRfqs = filteredRfqs.Where(r => r.CreatedAt <= request.ToDate.Value);

        if (request.BrokerIds?.Any() == true)
        {
            filteredRfqs = filteredRfqs.Where(r => r.Bids.Any(b => request.BrokerIds.Contains(b.BrokerId)));
        }

        if (request.LoadTypes?.Any() == true)
        {
            filteredRfqs = filteredRfqs.Where(r => request.LoadTypes.Contains(r.LoadDetails.LoadType));
        }

        if (request.Routes?.Any() == true)
        {
            filteredRfqs = filteredRfqs.Where(r => request.Routes.Contains(
                r.RouteDetails.PickupAddress.City + "-" + r.RouteDetails.DeliveryAddress.City));
        }

        var finalRfqs = filteredRfqs.ToList();

        // Get related orders to determine conversion status
        var rfqIds = finalRfqs.Select(r => r.Id).ToList();
        var allOrders = await _orderRepository.GetAllAsync(cancellationToken);
        var orders = allOrders.Where((Order o) => o.RfqId.HasValue && rfqIds.Contains(o.RfqId.Value)).ToList();

        // Calculate main metrics
        var totalPostings = finalRfqs.Count;
        var openPostings = finalRfqs.Count(r => r.Status == RfqStatus.Published && (!r.ExpiresAt.HasValue || r.ExpiresAt > DateTime.UtcNow));
        var quotedPostings = finalRfqs.Count(r => r.Bids.Any());
        var expiredPostings = finalRfqs.Count(r => r.Status == RfqStatus.Expired || (r.ExpiresAt.HasValue && r.ExpiresAt <= DateTime.UtcNow));
        var convertedPostings = finalRfqs.Count(r => orders.Any(o => o.RfqId == r.Id));

        var totalPostedValue = finalRfqs.Where(r => r.BudgetRange != null).Sum(r => r.BudgetRange.Amount);
        var convertedValue = finalRfqs.Where(r => orders.Any(o => o.RfqId == r.Id) && r.BudgetRange != null).Sum(r => r.BudgetRange.Amount);

        var analytics = new TransporterLoadPostingAnalyticsDto
        {
            TransporterId = request.TransporterId,
            AnalysisPeriodStart = request.FromDate ?? DateTime.MinValue,
            AnalysisPeriodEnd = request.ToDate ?? DateTime.MaxValue,
            TotalLoadPostings = totalPostings,
            OpenPostings = openPostings,
            QuotedPostings = quotedPostings,
            ExpiredPostings = expiredPostings,
            ConvertedPostings = convertedPostings,
            ConversionRate = totalPostings > 0 ? (decimal)convertedPostings / totalPostings * 100 : 0,
            QuoteRate = totalPostings > 0 ? (decimal)quotedPostings / totalPostings * 100 : 0,
            TotalPostedValue = new MoneyDto { Amount = totalPostedValue, Currency = "INR" },
            ConvertedValue = new MoneyDto { Amount = convertedValue, Currency = "INR" },
            AveragePostingValue = new MoneyDto { Amount = totalPostings > 0 ? totalPostedValue / totalPostings : 0, Currency = "INR" }
        };

        // Calculate average time to first quote
        var timeToFirstQuotes = finalRfqs.Where(r => r.Bids.Any())
            .Select(r => r.Bids.OrderBy(b => b.SubmittedAt).First().SubmittedAt - r.CreatedAt)
            .ToList();

        if (timeToFirstQuotes.Any())
        {
            analytics.AverageTimeToFirstQuote = TimeSpan.FromTicks((long)timeToFirstQuotes.Average(t => t.Ticks));
        }

        // Calculate average time to conversion
        var timeToConversions = orders.Where(o => o.RfqId.HasValue)
            .Join(finalRfqs, o => o.RfqId.Value, r => r.Id, (o, r) => o.CreatedAt - r.CreatedAt)
            .ToList();

        if (timeToConversions.Any())
        {
            analytics.AverageTimeToConversion = TimeSpan.FromTicks((long)timeToConversions.Average(t => t.Ticks));
        }

        // Status breakdown (if requested)
        if (request.IncludeStatusBreakdown)
        {
            analytics.PostingsByStatus = GetPostingsByStatus(finalRfqs, orders);
        }

        // Broker breakdown (if requested)
        if (request.IncludeBrokerBreakdown)
        {
            analytics.PostingsByBroker = await GetPostingsByBroker(finalRfqs, orders, cancellationToken);
        }

        // Daily breakdown (if requested)
        if (request.IncludeDailyBreakdown)
        {
            analytics.DailyPostings = GetDailyPostings(finalRfqs, orders);
        }

        // Route breakdown (if requested)
        if (request.IncludeRouteBreakdown)
        {
            analytics.PostingsByRoute = GetPostingsByRoute(finalRfqs, orders);
        }

        _logger.LogInformation("Successfully calculated load posting analytics for transporter {TransporterId}. Total postings: {TotalPostings}, Conversion rate: {ConversionRate}%",
            request.TransporterId, totalPostings, analytics.ConversionRate);

        return analytics;
    }

    private List<LoadPostingByStatusDto> GetPostingsByStatus(List<Domain.Entities.RFQ> rfqs, List<Domain.Entities.Order> orders)
    {
        var totalPostings = rfqs.Count;
        var statusGroups = new List<LoadPostingByStatusDto>();

        // Open postings
        var openCount = rfqs.Count(r => r.Status == RfqStatus.Published && (!r.ExpiresAt.HasValue || r.ExpiresAt > DateTime.UtcNow));
        var openValue = rfqs.Where(r => r.Status == RfqStatus.Published && (!r.ExpiresAt.HasValue || r.ExpiresAt > DateTime.UtcNow) && r.BudgetRange != null)
            .Sum(r => r.BudgetRange.Amount);

        statusGroups.Add(new LoadPostingByStatusDto
        {
            Status = "Open",
            Count = openCount,
            Percentage = totalPostings > 0 ? (decimal)openCount / totalPostings * 100 : 0,
            TotalValue = new MoneyDto { Amount = openValue, Currency = "INR" }
        });

        // Quoted postings
        var quotedCount = rfqs.Count(r => r.Bids.Any());
        var quotedValue = rfqs.Where(r => r.Bids.Any() && r.BudgetRange != null).Sum(r => r.BudgetRange.Amount);

        statusGroups.Add(new LoadPostingByStatusDto
        {
            Status = "Quoted",
            Count = quotedCount,
            Percentage = totalPostings > 0 ? (decimal)quotedCount / totalPostings * 100 : 0,
            TotalValue = new MoneyDto { Amount = quotedValue, Currency = "INR" }
        });

        // Expired postings
        var expiredCount = rfqs.Count(r => r.Status == RfqStatus.Expired || (r.ExpiresAt.HasValue && r.ExpiresAt <= DateTime.UtcNow));
        var expiredValue = rfqs.Where(r => (r.Status == RfqStatus.Expired || (r.ExpiresAt.HasValue && r.ExpiresAt <= DateTime.UtcNow)) && r.BudgetRange != null)
            .Sum(r => r.BudgetRange.Amount);

        statusGroups.Add(new LoadPostingByStatusDto
        {
            Status = "Expired",
            Count = expiredCount,
            Percentage = totalPostings > 0 ? (decimal)expiredCount / totalPostings * 100 : 0,
            TotalValue = new MoneyDto { Amount = expiredValue, Currency = "INR" }
        });

        // Converted postings
        var convertedRfqIds = orders.Where(o => o.RfqId.HasValue).Select(o => o.RfqId.Value).ToList();
        var convertedCount = rfqs.Count(r => convertedRfqIds.Contains(r.Id));
        var convertedValue = rfqs.Where(r => convertedRfqIds.Contains(r.Id) && r.BudgetRange != null).Sum(r => r.BudgetRange.Amount);

        statusGroups.Add(new LoadPostingByStatusDto
        {
            Status = "Converted",
            Count = convertedCount,
            Percentage = totalPostings > 0 ? (decimal)convertedCount / totalPostings * 100 : 0,
            TotalValue = new MoneyDto { Amount = convertedValue, Currency = "INR" }
        });

        return statusGroups;
    }

    private async Task<List<LoadPostingByBrokerDto>> GetPostingsByBroker(List<Domain.Entities.RFQ> rfqs, List<Domain.Entities.Order> orders, CancellationToken cancellationToken)
    {
        var brokerAnalytics = new List<LoadPostingByBrokerDto>();

        // Get all brokers who have submitted bids
        var brokerIds = rfqs.SelectMany(r => r.Bids).Select(b => b.BrokerId).Distinct().ToList();

        foreach (var brokerId in brokerIds)
        {
            var brokerRfqs = rfqs.Where(r => r.Bids.Any(b => b.BrokerId == brokerId)).ToList();
            var brokerOrders = orders.Where(o => o.BrokerId == brokerId).ToList();

            var totalAssignments = brokerRfqs.Count;
            var convertedAssignments = brokerRfqs.Count(r => brokerOrders.Any(o => o.RfqId == r.Id));
            var totalValue = brokerRfqs.Where(r => r.BudgetRange != null).Sum(r => r.BudgetRange.Amount);

            // Calculate average response time for this broker
            var responseTimes = brokerRfqs.SelectMany(r => r.Bids.Where(b => b.BrokerId == brokerId))
                .Select(b => b.SubmittedAt - b.Rfq.CreatedAt)
                .ToList();

            brokerAnalytics.Add(new LoadPostingByBrokerDto
            {
                BrokerId = brokerId,
                BrokerName = $"Broker-{brokerId}", // TODO: Get actual broker name from user service
                TotalAssignments = totalAssignments,
                ConvertedAssignments = convertedAssignments,
                ConversionRate = totalAssignments > 0 ? (decimal)convertedAssignments / totalAssignments * 100 : 0,
                TotalValue = new MoneyDto { Amount = totalValue, Currency = "INR" },
                AverageResponseTime = responseTimes.Any() ? TimeSpan.FromTicks((long)responseTimes.Average(t => t.Ticks)) : TimeSpan.Zero,
                PerformanceRating = CalculateBrokerPerformanceRating(totalAssignments, convertedAssignments, responseTimes)
            });
        }

        return brokerAnalytics.OrderByDescending(b => b.ConversionRate).ToList();
    }

    private List<LoadPostingByDateDto> GetDailyPostings(List<Domain.Entities.RFQ> rfqs, List<Domain.Entities.Order> orders)
    {
        var convertedRfqIds = orders.Where(o => o.RfqId.HasValue).Select(o => o.RfqId.Value).ToList();

        return rfqs.GroupBy(r => r.CreatedAt.Date)
            .Select(g => new LoadPostingByDateDto
            {
                Date = g.Key,
                PostingsCreated = g.Count(),
                PostingsConverted = g.Count(r => convertedRfqIds.Contains(r.Id)),
                ConversionRate = g.Count() > 0 ? (decimal)g.Count(r => convertedRfqIds.Contains(r.Id)) / g.Count() * 100 : 0,
                TotalValue = new MoneyDto { Amount = g.Where(r => r.BudgetRange != null).Sum(r => r.BudgetRange.Amount), Currency = "INR" }
            })
            .OrderBy(d => d.Date)
            .ToList();
    }

    private List<LoadPostingByRouteDto> GetPostingsByRoute(List<Domain.Entities.RFQ> rfqs, List<Domain.Entities.Order> orders)
    {
        var convertedRfqIds = orders.Where(o => o.RfqId.HasValue).Select(o => o.RfqId.Value).ToList();

        return rfqs.GroupBy(r => new
        {
            PickupCity = r.RouteDetails.PickupAddress.City,
            PickupState = r.RouteDetails.PickupAddress.State,
            DeliveryCity = r.RouteDetails.DeliveryAddress.City,
            DeliveryState = r.RouteDetails.DeliveryAddress.State
        })
            .Select(g => new LoadPostingByRouteDto
            {
                Route = $"{g.Key.PickupCity}, {g.Key.PickupState} → {g.Key.DeliveryCity}, {g.Key.DeliveryState}",
                PickupCity = g.Key.PickupCity,
                PickupState = g.Key.PickupState,
                DeliveryCity = g.Key.DeliveryCity,
                DeliveryState = g.Key.DeliveryState,
                TotalPostings = g.Count(),
                ConvertedPostings = g.Count(r => convertedRfqIds.Contains(r.Id)),
                ConversionRate = g.Count() > 0 ? (decimal)g.Count(r => convertedRfqIds.Contains(r.Id)) / g.Count() * 100 : 0,
                AverageValue = new MoneyDto { Amount = g.Count() > 0 && g.Any(r => r.BudgetRange != null) ? g.Where(r => r.BudgetRange != null).Sum(r => r.BudgetRange.Amount) / g.Count() : 0, Currency = "INR" },
                AverageConversionTime = CalculateAverageConversionTime(g.ToList(), orders)
            })
            .OrderByDescending(r => r.ConversionRate)
            .ToList();
    }

    private decimal CalculateBrokerPerformanceRating(int totalAssignments, int convertedAssignments, List<TimeSpan> responseTimes)
    {
        if (totalAssignments == 0) return 0;

        var conversionScore = (decimal)convertedAssignments / totalAssignments * 50; // 50% weight for conversion
        var responseScore = responseTimes.Any() ?
            Math.Max(0, 50 - (decimal)responseTimes.Average(t => t.TotalHours)) : 0; // 50% weight for response time

        return Math.Min(100, conversionScore + responseScore);
    }

    private TimeSpan CalculateAverageConversionTime(List<Domain.Entities.RFQ> rfqs, List<Domain.Entities.Order> orders)
    {
        var conversionTimes = rfqs.Where(r => orders.Any(o => o.RfqId == r.Id))
            .Select(r => orders.First(o => o.RfqId == r.Id).CreatedAt - r.CreatedAt)
            .ToList();

        return conversionTimes.Any() ? TimeSpan.FromTicks((long)conversionTimes.Average(t => t.Ticks)) : TimeSpan.Zero;
    }
}
