using MediatR;

namespace SubscriptionManagement.Application.Commands.ProcessBilling
{
    public class ProcessBillingCommand : IRequest<bool>
    {
        public Guid? SubscriptionId { get; set; } // If null, process all due subscriptions
        public DateTime? BillingDate { get; set; } // If null, use current date
        public bool ForceProcessing { get; set; } = false; // Process even if not due
        public int MaxRetries { get; set; } = 3;
    }
}
