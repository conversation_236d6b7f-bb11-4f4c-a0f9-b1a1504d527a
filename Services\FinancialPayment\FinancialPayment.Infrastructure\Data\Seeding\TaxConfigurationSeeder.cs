using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Infrastructure.Data.Seeding;

/// <summary>
/// Seeds initial tax configuration data for Indian tax system
/// </summary>
public class TaxConfigurationSeeder
{
    private readonly FinancialPaymentDbContext _context;
    private readonly ILogger<TaxConfigurationSeeder> _logger;

    public TaxConfigurationSeeder(FinancialPaymentDbContext context, ILogger<TaxConfigurationSeeder> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task SeedAsync()
    {
        _logger.LogInformation("Starting tax configuration seeding...");

        try
        {
            await SeedTaxConfigurationsAsync();
            await SeedGstConfigurationsAsync();
            await SeedTdsConfigurationsAsync();
            await SeedHsnCodesAsync();

            await _context.SaveChangesAsync();
            _logger.LogInformation("Tax configuration seeding completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred during tax configuration seeding");
            throw;
        }
    }

    private async Task SeedTaxConfigurationsAsync()
    {
        if (await _context.TaxConfigurations.AnyAsync())
        {
            _logger.LogInformation("Tax configurations already exist, skipping seeding");
            return;
        }

        _logger.LogInformation("Seeding tax configurations...");

        var indiaJurisdiction = new TaxJurisdiction("India", "", "", LocationType.Country);

        var defaultTaxConfig = new TaxConfiguration(
            "India Default Tax Configuration",
            "Default tax configuration for India with GST and TDS enabled",
            indiaJurisdiction,
            DateTime.UtcNow,
            "System",
            null,
            true,
            1);

        defaultTaxConfig.UpdateTaxSettings(
            enableGstCalculation: true,
            enableTdsCalculation: true,
            enableReverseCharge: true,
            requireHsnCode: true,
            defaultGstRate: 18.0m,
            defaultTdsRate: 2.0m,
            defaultCurrency: "INR",
            "System");

        _context.TaxConfigurations.Add(defaultTaxConfig);

        // Add state-specific configurations
        var maharashtraJurisdiction = new TaxJurisdiction("India", "Maharashtra", "", LocationType.State);
        var maharashtraTaxConfig = new TaxConfiguration(
            "Maharashtra Tax Configuration",
            "Tax configuration for Maharashtra state",
            maharashtraJurisdiction,
            DateTime.UtcNow,
            "System",
            null,
            false,
            2);

        maharashtraTaxConfig.UpdateTaxSettings(
            enableGstCalculation: true,
            enableTdsCalculation: true,
            enableReverseCharge: true,
            requireHsnCode: true,
            defaultGstRate: 18.0m,
            defaultTdsRate: 2.0m,
            defaultCurrency: "INR",
            "System");

        _context.TaxConfigurations.Add(maharashtraTaxConfig);
    }

    private async Task SeedGstConfigurationsAsync()
    {
        if (await _context.GstConfigurations.AnyAsync())
        {
            _logger.LogInformation("GST configurations already exist, skipping seeding");
            return;
        }

        _logger.LogInformation("Seeding GST configurations...");

        var indiaJurisdiction = new TaxJurisdiction("India", "", "", LocationType.Country);

        // Transportation services - 5% GST
        var transportationGst = new GstConfiguration(
            "Transportation Services GST",
            "GST configuration for transportation and logistics services",
            ServiceCategory.Transportation,
            indiaJurisdiction,
            GstRate.Rate5,
            new TaxRate(5.0m, TaxCalculationMethod.Percentage, DateTime.UtcNow, null),
            Money.Zero("INR"),
            new Money(10000000, "INR"), // 1 crore max
            "System",
            "9967", // HSN for transport services
            false,
            null);

        _context.GstConfigurations.Add(transportationGst);

        // Brokerage services - 18% GST
        var brokerageGst = new GstConfiguration(
            "Brokerage Services GST",
            "GST configuration for brokerage and commission services",
            ServiceCategory.Brokerage,
            indiaJurisdiction,
            GstRate.Rate18,
            new TaxRate(18.0m, TaxCalculationMethod.Percentage, DateTime.UtcNow, null),
            Money.Zero("INR"),
            new Money(10000000, "INR"),
            "System",
            "9972", // HSN for brokerage services
            false,
            null);

        _context.GstConfigurations.Add(brokerageGst);

        // Insurance services - 18% GST
        var insuranceGst = new GstConfiguration(
            "Insurance Services GST",
            "GST configuration for insurance services",
            ServiceCategory.Insurance,
            indiaJurisdiction,
            GstRate.Rate18,
            new TaxRate(18.0m, TaxCalculationMethod.Percentage, DateTime.UtcNow, null),
            Money.Zero("INR"),
            new Money(10000000, "INR"),
            "System",
            "9971", // HSN for insurance services
            false,
            null);

        _context.GstConfigurations.Add(insuranceGst);

        // Warehousing services - 18% GST
        var warehousingGst = new GstConfiguration(
            "Warehousing Services GST",
            "GST configuration for warehousing and storage services",
            ServiceCategory.Warehousing,
            indiaJurisdiction,
            GstRate.Rate18,
            new TaxRate(18.0m, TaxCalculationMethod.Percentage, DateTime.UtcNow, null),
            Money.Zero("INR"),
            new Money(10000000, "INR"),
            "System",
            "9963", // HSN for warehousing services
            false,
            null);

        _context.GstConfigurations.Add(warehousingGst);
    }

    private async Task SeedTdsConfigurationsAsync()
    {
        if (await _context.TdsConfigurations.AnyAsync())
        {
            _logger.LogInformation("TDS configurations already exist, skipping seeding");
            return;
        }

        _logger.LogInformation("Seeding TDS configurations...");

        // Section 194C - Payments to contractors
        var section194C = new TdsConfiguration(
            "Section 194C - Contractors",
            "TDS on payments to contractors and sub-contractors",
            TdsSection.Section194C,
            EntityType.Company,
            new TaxRate(2.0m, TaxCalculationMethod.Percentage, DateTime.UtcNow, null),
            new TdsThreshold(
                new Money(30000, "INR"), // Single payment threshold
                new Money(100000, "INR"), // Annual threshold
                EntityType.Company,
                true),
            "System",
            true,
            20.0m, // Higher rate without PAN
            "Applicable for payments to contractors for carrying out any work");

        _context.TdsConfigurations.Add(section194C);

        // Section 194H - Commission or brokerage
        var section194H = new TdsConfiguration(
            "Section 194H - Commission/Brokerage",
            "TDS on commission or brokerage payments",
            TdsSection.Section194H,
            EntityType.Company,
            new TaxRate(5.0m, TaxCalculationMethod.Percentage, DateTime.UtcNow, null),
            new TdsThreshold(
                new Money(15000, "INR"), // Single payment threshold
                new Money(15000, "INR"), // Annual threshold
                EntityType.Company,
                true),
            "System",
            true,
            20.0m, // Higher rate without PAN
            "Applicable for commission or brokerage payments");

        _context.TdsConfigurations.Add(section194H);

        // Section 194I - Rent
        var section194I = new TdsConfiguration(
            "Section 194I - Rent",
            "TDS on rent payments",
            TdsSection.Section194I,
            EntityType.Company,
            new TaxRate(10.0m, TaxCalculationMethod.Percentage, DateTime.UtcNow, null),
            new TdsThreshold(
                new Money(240000, "INR"), // Annual threshold
                new Money(240000, "INR"), // Annual threshold
                EntityType.Company,
                true),
            "System",
            true,
            20.0m, // Higher rate without PAN
            "Applicable for rent payments for land, building, or furniture");

        _context.TdsConfigurations.Add(section194I);

        // Section 194J - Professional/technical services
        var section194J = new TdsConfiguration(
            "Section 194J - Professional Services",
            "TDS on fees for professional or technical services",
            TdsSection.Section194J,
            EntityType.Company,
            new TaxRate(10.0m, TaxCalculationMethod.Percentage, DateTime.UtcNow, null),
            new TdsThreshold(
                new Money(30000, "INR"), // Single payment threshold
                new Money(30000, "INR"), // Annual threshold
                EntityType.Company,
                true),
            "System",
            true,
            20.0m, // Higher rate without PAN
            "Applicable for professional or technical services");

        _context.TdsConfigurations.Add(section194J);
    }

    private async Task SeedHsnCodesAsync()
    {
        if (await _context.HsnCodes.AnyAsync())
        {
            _logger.LogInformation("HSN codes already exist, skipping seeding");
            return;
        }

        _logger.LogInformation("Seeding HSN codes...");

        // Transportation services
        var transportHsn = new HsnCode(
            new HsnCodeDetails("9967", "Supporting and auxiliary transport services", "Transportation", GstRate.Rate5),
            "99", // Chapter 99 - Services
            "Services",
            DateTime.UtcNow,
            "System",
            null,
            "Includes freight transport agency services, cargo handling, storage and warehousing services");

        _context.HsnCodes.Add(transportHsn);

        // Brokerage services
        var brokerageHsn = new HsnCode(
            new HsnCodeDetails("9972", "Commission agent services", "Brokerage", GstRate.Rate18),
            "99",
            "Services",
            DateTime.UtcNow,
            "System",
            null,
            "Includes commission agent services and brokerage services");

        _context.HsnCodes.Add(brokerageHsn);

        // Insurance services
        var insuranceHsn = new HsnCode(
            new HsnCodeDetails("9971", "Insurance and pension services", "Insurance", GstRate.Rate18),
            "99",
            "Services",
            DateTime.UtcNow,
            "System",
            null,
            "Includes life insurance, general insurance, and reinsurance services");

        _context.HsnCodes.Add(insuranceHsn);

        // Warehousing services
        var warehousingHsn = new HsnCode(
            new HsnCodeDetails("9963", "Storage and warehousing services", "Warehousing", GstRate.Rate18),
            "99",
            "Services",
            DateTime.UtcNow,
            "System",
            null,
            "Includes storage and warehousing services for goods");

        _context.HsnCodes.Add(warehousingHsn);

        // Consulting services
        var consultingHsn = new HsnCode(
            new HsnCodeDetails("9983", "Business and management consultancy and public relations services", "Consulting", GstRate.Rate18),
            "99",
            "Services",
            DateTime.UtcNow,
            "System",
            null,
            "Includes management consulting, business consulting, and advisory services");

        _context.HsnCodes.Add(consultingHsn);
    }
}
