# PowerShell script to fix domain events across all services
Write-Host "Fixing domain events to implement IDomainEvent interface..." -ForegroundColor Green

# Get the solution root directory
$solutionRoot = Split-Path -Parent $PSScriptRoot

# Function to fix domain events in a file
function Fix-DomainEvents {
    param(
        [string]$FilePath
    )
    
    if (Test-Path $FilePath) {
        $content = Get-Content $FilePath -Raw
        $modified = $false
        
        # Pattern to match record declarations that implement IDomainEvent but don't have Id and OccurredOn
        $pattern = 'public record (\w+)\([^)]*\) : IDomainEvent;'
        
        if ($content -match $pattern) {
            # Replace simple record declarations with full implementations
            $content = $content -replace 'public record (\w+)\(([^)]*)\) : IDomainEvent;', @'
public record $1($2) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}
'@
            $modified = $true
        }
        
        if ($modified) {
            Set-Content $FilePath $content -NoNewline
            Write-Host "  Fixed domain events in $(Split-Path $FilePath -Leaf)" -ForegroundColor Green
        }
    }
}

# Find all C# files in Domain projects
$domainFiles = Get-ChildItem -Path $solutionRoot -Recurse -Filter "*.cs" | Where-Object { 
    $_.FullName -like "*Domain*" -and $_.FullName -notlike "*bin*" -and $_.FullName -notlike "*obj*" 
}

Write-Host "Found $($domainFiles.Count) domain files to process" -ForegroundColor Cyan

foreach ($file in $domainFiles) {
    $content = Get-Content $file.FullName -Raw -ErrorAction SilentlyContinue
    if ($content -and $content -match ": IDomainEvent") {
        Write-Host "Processing $($file.Name)..." -ForegroundColor White
        Fix-DomainEvents -FilePath $file.FullName
    }
}

Write-Host "Domain event fixes completed!" -ForegroundColor Green
