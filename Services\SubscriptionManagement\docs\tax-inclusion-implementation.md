# Tax Inclusion Implementation Guide

## Overview

The Tax Inclusion system provides comprehensive tax management capabilities for subscription plans, supporting multiple regions, tax types, exemptions, and compliance requirements. This implementation follows Clean Architecture principles with CQRS patterns using MediatR.

## Architecture

### Domain Layer

#### Value Objects
- **TaxConfiguration**: Core tax configuration with rate, type, regions, and date ranges
- **Money**: Enhanced to support tax calculations

#### Entities
- **TaxCategory**: Groups related tax configurations
- **TaxExemption**: Customer tax exemptions with verification workflow
- **GlobalTaxConfiguration**: System-wide tax settings with priority
- **PlanTaxConfiguration**: Plan-specific tax overrides
- **Plan**: Extended with tax calculation methods

#### Domain Services
- **TaxDomainService**: Business rules validation and compound tax calculations
- **ITaxCalculationService**: Tax calculation interface

### Application Layer

#### Commands
- **SetGlobalTaxConfigurationCommand**: Manage global tax settings
- **SetPlanTaxConfigurationCommand**: Configure plan-specific taxes
- **CreateTaxCategoryCommand**: Create tax categories
- **CreateTaxExemptionCommand**: Handle tax exemptions

#### Queries
- **GetPlanTaxDetailsQuery**: Retrieve detailed tax information
- **CalculateTaxForSubscriptionQuery**: Calculate taxes for subscriptions
- **GetTaxCategoriesQuery**: Retrieve tax categories
- **GetGlobalTaxConfigurationsQuery**: Manage global configurations

### Infrastructure Layer

#### Repositories
- **TaxCategoryRepository**: CRUD operations for tax categories
- **TaxExemptionRepository**: Manage customer exemptions
- **GlobalTaxConfigurationRepository**: System-wide tax configurations
- **PlanTaxConfigurationRepository**: Plan-specific configurations

#### Services
- **TaxCalculationService**: Core tax calculation engine
- **TaxAwareInvoiceService**: Invoice generation with tax details
- **TaxAwarePaymentService**: Payment processing with tax handling
- **TaxReportingService**: Compliance and reporting

### API Layer

#### Admin Endpoints (`/api/admin/tax`)
- `POST /global-configurations` - Set global tax configuration
- `GET /global-configurations` - List global configurations
- `POST /plans/{planId}/configurations` - Set plan tax configuration
- `GET /plans/{planId}/configurations` - Get plan configurations
- `POST /categories` - Create tax category
- `GET /categories` - List tax categories
- `POST /exemptions` - Create tax exemption
- `GET /exemptions` - List tax exemptions

#### Public Endpoints (`/api/tax`)
- `GET /plans/{planId}/details` - Get plan tax details
- `POST /calculate` - Calculate tax for subscription
- `GET /rates` - Get effective tax rates
- `GET /exemptions/check` - Check customer exemptions
- `GET /regions` - Get supported regions

## Features

### Multi-Region Tax Support
- Different tax rules for different regions (India, US, UK, Canada)
- Region-specific tax rates and types
- Automatic region detection and validation

### Tax Types Supported
- **GST** (Goods and Services Tax)
- **VAT** (Value Added Tax)
- **Sales Tax**
- **Service Tax**
- **TDS** (Tax Deducted at Source)
- **TCS** (Tax Collected at Source)
- **IGST/CGST/SGST/UTGST** (Indian GST components)
- **Cess** (Additional tax)
- **Custom Duty**
- **Excise Duty**

### Compound Taxation
- Support for cascading taxes (tax on tax)
- Configurable tax priority and calculation order
- Cess calculated on GST + base amount

### Tax Exemptions
- Customer-specific exemptions
- Document verification workflow
- Expiration date management
- Admin verification required

### Business Rules
- **India-specific**: GST rates ≤ 28%, TDS rates ≤ 30%
- **CGST + SGST = IGST** validation
- **Equal CGST and SGST rates** requirement
- **No overlapping tax configurations**

### Tax-Inclusive/Exclusive Pricing
- Support for both pricing models
- Automatic base amount calculation
- Clear price display with tax breakdown

## Configuration

### Database Setup

Run the migration script:
```sql
-- Execute the migration
dotnet ef database update --project SubscriptionManagement.Infrastructure

-- Or run the SQL script directly
psql -d subscription_db -f tax-database-setup.sql
```

### Dependency Injection

Services are automatically registered in `DependencyInjection.cs`:
```csharp
// Tax-related repositories
services.AddScoped<ITaxCategoryRepository, TaxCategoryRepository>();
services.AddScoped<ITaxExemptionRepository, TaxExemptionRepository>();
services.AddScoped<IGlobalTaxConfigurationRepository, GlobalTaxConfigurationRepository>();
services.AddScoped<IPlanTaxConfigurationRepository, PlanTaxConfigurationRepository>();

// Tax-related services
services.AddScoped<ITaxCalculationService, TaxCalculationService>();
```

### Default Tax Configurations

The system includes default configurations for India:
- **GST**: 18% for digital services
- **TDS**: 2% for digital services

## Usage Examples

### Setting Global Tax Configuration

```csharp
var command = new SetGlobalTaxConfigurationCommand
{
    TaxType = TaxType.GST,
    Rate = 18.0m,
    IsIncluded = false,
    ApplicableRegions = new List<string> { "IN" },
    EffectiveDate = DateTime.UtcNow,
    Priority = 1,
    Description = "Standard GST for digital services",
    CreatedByUserId = adminUserId
};

var configurationId = await mediator.Send(command);
```

### Calculating Tax for a Plan

```csharp
var query = new GetPlanTaxDetailsQuery
{
    PlanId = planId,
    CustomerRegion = "IN",
    CustomerId = customerId
};

var taxDetails = await mediator.Send(query);
// Returns: BasePrice, TaxAmount, DisplayPrice, TaxBreakdown, etc.
```

### Creating Tax Exemption

```csharp
var command = new CreateTaxExemptionCommand
{
    UserId = customerId,
    ExemptionType = "Educational Institution",
    ExemptionNumber = "EDU123456",
    IssuingAuthority = "Ministry of Education",
    ValidFrom = DateTime.UtcNow,
    ValidTo = DateTime.UtcNow.AddYears(1),
    ExemptTaxTypes = new List<TaxType> { TaxType.GST },
    ApplicableRegions = new List<string> { "IN" },
    CreatedByUserId = adminUserId
};

var exemptionId = await mediator.Send(command);
```

## API Examples

### Get Plan Tax Details
```http
GET /api/tax/plans/123e4567-e89b-12d3-a456-426614174000/details?region=IN&customerId=456e7890-e89b-12d3-a456-426614174000
```

Response:
```json
{
  "planId": "123e4567-e89b-12d3-a456-426614174000",
  "planName": "Premium Plan",
  "basePrice": 100.00,
  "currency": "INR",
  "customerRegion": "IN",
  "taxBreakdown": [
    {
      "taxType": "GST",
      "taxName": "GST",
      "rate": 18.00,
      "taxableAmount": 100.00,
      "taxAmount": 18.00,
      "isExempt": false
    }
  ],
  "totalTaxAmount": 18.00,
  "displayPrice": 118.00,
  "isTaxInclusive": false,
  "hasExemptions": false
}
```

### Calculate Tax
```http
POST /api/tax/calculate
Content-Type: application/json

{
  "planId": "123e4567-e89b-12d3-a456-426614174000",
  "customerRegion": "IN",
  "customerId": "456e7890-e89b-12d3-a456-426614174000",
  "includeTaxBreakdown": true
}
```

### Set Global Tax Configuration (Admin)
```http
POST /api/admin/tax/global-configurations
Authorization: Bearer {admin-token}
Content-Type: application/json

{
  "taxType": "GST",
  "rate": 18.00,
  "isIncluded": false,
  "applicableRegions": ["IN"],
  "effectiveDate": "2024-01-01T00:00:00Z",
  "priority": 1,
  "description": "Standard GST for digital services"
}
```

## Testing

### Unit Tests
- **Domain**: TaxConfiguration, TaxDomainService
- **Application**: Command/Query handlers
- **API**: Controller endpoints

### Integration Tests
- Database operations
- End-to-end tax calculations
- API endpoint testing

### Test Coverage
- Domain Logic: 95%+
- Application Layer: 90%+
- API Layer: 85%+

## Monitoring and Observability

### Metrics
- Tax calculation requests per second
- Tax exemption usage rates
- Regional tax distribution
- Compliance validation results

### Logging
- Tax configuration changes
- Exemption verifications
- Calculation errors
- Compliance violations

### Events
- `tax.global_configuration_changed`
- `tax.plan_configuration_changed`
- `tax.exemption_created`
- `tax.payment_processed`
- `tax.refund_processed`

## Compliance and Reporting

### Tax Reports
- Regional tax collection summaries
- Exemption usage reports
- Compliance status reports
- Filing requirement tracking

### Audit Trail
- All tax configuration changes logged
- Exemption verification history
- Payment tax calculations recorded
- Admin action tracking

## Security

### Authorization
- Admin-only tax configuration endpoints
- Customer-specific exemption access
- Role-based permissions

### Data Protection
- Sensitive tax data encryption
- Audit log retention policies
- GDPR compliance for EU customers

## Performance

### Caching
- Tax calculations cached for 5 minutes
- Regional configurations cached for 10 minutes
- Exemption checks cached for 2 minutes

### Optimization
- Database indexes on frequently queried fields
- Efficient compound tax calculations
- Minimal API response payloads

## Deployment

### Database Migration
```bash
# Apply tax-related migrations
dotnet ef database update --project SubscriptionManagement.Infrastructure

# Verify tables created
psql -d subscription_db -c "\dt subscription.*tax*"
```

### Configuration Validation
```bash
# Validate tax configurations
curl -X GET "https://api.example.com/api/tax/regions" \
  -H "Authorization: Bearer {token}"
```

### Health Checks
- Tax calculation service availability
- Database connectivity
- Cache performance
- External tax service integration

This implementation provides a robust, scalable, and compliant tax management system that can handle complex multi-regional tax requirements while maintaining performance and security standards.
