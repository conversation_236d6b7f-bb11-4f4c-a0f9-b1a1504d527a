using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;
using MediatR;

namespace CommunicationNotification.Application.Commands.TransportCompanyNotification;

/// <summary>
/// Command to configure Transport Company notifications
/// </summary>
public class ConfigureTransportCompanyNotificationsCommand : IRequest<ConfigureTransportCompanyNotificationsResult>
{
    public Guid TransportCompanyId { get; set; }
    public bool IsEnabled { get; set; } = true;
    
    // Channel preferences
    public bool EmailEnabled { get; set; } = true;
    public bool SmsEnabled { get; set; } = true;
    public bool PushEnabled { get; set; } = true;
    public bool InAppEnabled { get; set; } = true;
    public bool WhatsAppEnabled { get; set; } = false;
    public List<NotificationChannel> PriorityChannels { get; set; } = new();
    public List<NotificationChannel> EmergencyChannels { get; set; } = new();
    
    // Quiet hours
    public bool QuietHoursEnabled { get; set; } = false;
    public TimeSpan QuietHoursStart { get; set; } = new(22, 0, 0);
    public TimeSpan QuietHoursEnd { get; set; } = new(6, 0, 0);
    public string TimeZone { get; set; } = "Asia/Kolkata";
    public List<DayOfWeek> QuietHoursApplicableDays { get; set; } = new();
    public bool EmergencyOverride { get; set; } = true;
    public List<MessageType> ExemptMessageTypes { get; set; } = new();
    
    // Frequency settings
    public int MaxNotificationsPerHour { get; set; } = 50;
    public int MaxNotificationsPerDay { get; set; } = 200;
    public Dictionary<MessageType, int> MessageTypeHourlyLimits { get; set; } = new();
    public Dictionary<MessageType, int> MessageTypeDailyLimits { get; set; } = new();
    public TimeSpan CooldownPeriod { get; set; } = TimeSpan.FromMinutes(5);
    public Dictionary<MessageType, TimeSpan> MessageTypeCooldowns { get; set; } = new();
    public bool BurstModeEnabled { get; set; } = true;
    public int BurstModeThreshold { get; set; } = 10;
    
    // Grouping settings
    public bool GroupingEnabled { get; set; } = true;
    public TimeSpan GroupingWindow { get; set; } = TimeSpan.FromMinutes(15);
    public int MaxGroupSize { get; set; } = 10;
    public List<MessageType> GroupableMessageTypes { get; set; } = new();
    public Dictionary<MessageType, TimeSpan> MessageTypeGroupingWindows { get; set; } = new();
    public bool GroupBySender { get; set; } = true;
    public bool GroupByPriority { get; set; } = true;
    public bool GroupByRelatedEntity { get; set; } = true;
    
    // Escalation settings
    public bool EscalationEnabled { get; set; } = false;
    public TimeSpan DefaultEscalationInterval { get; set; } = TimeSpan.FromHours(2);
    public int MaxEscalationLevel { get; set; } = 3;
    public List<EscalationLevelDto> EscalationLevels { get; set; } = new();
    
    // Digest settings
    public bool DigestEnabled { get; set; } = false;
    public List<DigestScheduleDto> DigestSchedules { get; set; } = new();
    public List<MessageType> DigestableMessageTypes { get; set; } = new();
    public int MaxItemsPerDigest { get; set; } = 50;
    public string DigestTemplate { get; set; } = "default";
    
    // Notification types
    public List<NotificationTypeConfigurationDto> NotificationTypes { get; set; } = new();
    
    // Recipients
    public List<NotificationRecipientConfigurationDto> Recipients { get; set; } = new();
    
    // Custom settings
    public Dictionary<string, object> CustomSettings { get; set; } = new();
    
    public Guid ConfiguredByUserId { get; set; }
}

/// <summary>
/// DTO for escalation level configuration
/// </summary>
public class EscalationLevelDto
{
    public int Level { get; set; }
    public TimeSpan Interval { get; set; }
    public List<NotificationRecipientRole> RecipientRoles { get; set; } = new();
}

/// <summary>
/// DTO for digest schedule configuration
/// </summary>
public class DigestScheduleDto
{
    public string Name { get; set; } = string.Empty;
    public TimeSpan DeliveryTime { get; set; }
    public DigestFrequency Frequency { get; set; }
    public List<DayOfWeek> DeliveryDays { get; set; } = new();
}

/// <summary>
/// DTO for notification type configuration
/// </summary>
public class NotificationTypeConfigurationDto
{
    public MessageType MessageType { get; set; }
    public bool IsEnabled { get; set; } = true;
    public List<NotificationChannel> PreferredChannels { get; set; } = new();
    public List<NotificationRecipientRole> EligibleRecipientRoles { get; set; } = new();
    public Priority MinimumPriority { get; set; } = Priority.Low;
    public string? CustomTemplate { get; set; }
    public Dictionary<string, object> Settings { get; set; } = new();
}

/// <summary>
/// DTO for notification recipient configuration
/// </summary>
public class NotificationRecipientConfigurationDto
{
    public Guid UserId { get; set; }
    public NotificationRecipientRole Role { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string? Phone { get; set; }
    public bool IsEnabled { get; set; } = true;
    public List<MessageType> SubscribedMessageTypes { get; set; } = new();
    public List<NotificationChannel> PreferredChannels { get; set; } = new();
    public Dictionary<string, object> Settings { get; set; } = new();
}

/// <summary>
/// Result of configuring Transport Company notifications
/// </summary>
public class ConfigureTransportCompanyNotificationsResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public Guid? ConfigurationId { get; set; }
    public DateTime ConfiguredAt { get; set; }
    public List<string> ValidationErrors { get; set; } = new();
    public ConfigurationSummaryDto Summary { get; set; } = new();
}

/// <summary>
/// Summary of notification configuration
/// </summary>
public class ConfigurationSummaryDto
{
    public int TotalNotificationTypes { get; set; }
    public int EnabledNotificationTypes { get; set; }
    public int TotalRecipients { get; set; }
    public int EnabledRecipients { get; set; }
    public List<string> EnabledChannels { get; set; } = new();
    public bool QuietHoursConfigured { get; set; }
    public bool FrequencyLimitsConfigured { get; set; }
    public bool GroupingConfigured { get; set; }
    public bool EscalationConfigured { get; set; }
    public bool DigestConfigured { get; set; }
}

/// <summary>
/// Command to update notification channel preferences
/// </summary>
public class UpdateNotificationChannelPreferencesCommand : IRequest<UpdateNotificationChannelPreferencesResult>
{
    public Guid TransportCompanyId { get; set; }
    public bool EmailEnabled { get; set; } = true;
    public bool SmsEnabled { get; set; } = true;
    public bool PushEnabled { get; set; } = true;
    public bool InAppEnabled { get; set; } = true;
    public bool WhatsAppEnabled { get; set; } = false;
    public List<NotificationChannel> PriorityChannels { get; set; } = new();
    public List<NotificationChannel> EmergencyChannels { get; set; } = new();
    public Dictionary<Priority, List<NotificationChannel>> ChannelsByPriority { get; set; } = new();
    public Guid UpdatedByUserId { get; set; }
}

/// <summary>
/// Result of updating notification channel preferences
/// </summary>
public class UpdateNotificationChannelPreferencesResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime UpdatedAt { get; set; }
    public List<string> ValidationErrors { get; set; } = new();
}

/// <summary>
/// Command to update quiet hours configuration
/// </summary>
public class UpdateQuietHoursConfigurationCommand : IRequest<UpdateQuietHoursConfigurationResult>
{
    public Guid TransportCompanyId { get; set; }
    public bool IsEnabled { get; set; } = false;
    public TimeSpan StartTime { get; set; } = new(22, 0, 0);
    public TimeSpan EndTime { get; set; } = new(6, 0, 0);
    public string TimeZone { get; set; } = "Asia/Kolkata";
    public List<DayOfWeek> ApplicableDays { get; set; } = new();
    public bool EmergencyOverride { get; set; } = true;
    public List<MessageType> ExemptMessageTypes { get; set; } = new();
    public Guid UpdatedByUserId { get; set; }
}

/// <summary>
/// Result of updating quiet hours configuration
/// </summary>
public class UpdateQuietHoursConfigurationResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime UpdatedAt { get; set; }
    public List<string> ValidationErrors { get; set; } = new();
}

/// <summary>
/// Command to update notification frequency settings
/// </summary>
public class UpdateNotificationFrequencySettingsCommand : IRequest<UpdateNotificationFrequencySettingsResult>
{
    public Guid TransportCompanyId { get; set; }
    public int MaxNotificationsPerHour { get; set; } = 50;
    public int MaxNotificationsPerDay { get; set; } = 200;
    public Dictionary<MessageType, int> MessageTypeHourlyLimits { get; set; } = new();
    public Dictionary<MessageType, int> MessageTypeDailyLimits { get; set; } = new();
    public TimeSpan CooldownPeriod { get; set; } = TimeSpan.FromMinutes(5);
    public Dictionary<MessageType, TimeSpan> MessageTypeCooldowns { get; set; } = new();
    public bool BurstModeEnabled { get; set; } = true;
    public int BurstModeThreshold { get; set; } = 10;
    public Guid UpdatedByUserId { get; set; }
}

/// <summary>
/// Result of updating notification frequency settings
/// </summary>
public class UpdateNotificationFrequencySettingsResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime UpdatedAt { get; set; }
    public List<string> ValidationErrors { get; set; } = new();
}

/// <summary>
/// Command to add notification recipient
/// </summary>
public class AddNotificationRecipientCommand : IRequest<AddNotificationRecipientResult>
{
    public Guid TransportCompanyId { get; set; }
    public Guid UserId { get; set; }
    public NotificationRecipientRole Role { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string? Phone { get; set; }
    public bool IsEnabled { get; set; } = true;
    public List<MessageType> SubscribedMessageTypes { get; set; } = new();
    public List<NotificationChannel> PreferredChannels { get; set; } = new();
    public Dictionary<string, object> Settings { get; set; } = new();
    public Guid AddedByUserId { get; set; }
}

/// <summary>
/// Result of adding notification recipient
/// </summary>
public class AddNotificationRecipientResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime AddedAt { get; set; }
    public List<string> ValidationErrors { get; set; } = new();
}
