using FluentValidation;
using MobileWorkflow.Application.Commands.FormBuilder;

namespace MobileWorkflow.Application.Validators
{
    public class CreateFormDefinitionCommandValidator : AbstractValidator<CreateFormDefinitionCommand>
    {
        public CreateFormDefinitionCommandValidator()
        {
            RuleFor(x => x.Name)
                .NotEmpty()
                .WithMessage("Form name is required")
                .MaximumLength(100)
                .WithMessage("Form name cannot exceed 100 characters");

            RuleFor(x => x.DisplayName)
                .NotEmpty()
                .WithMessage("Display name is required")
                .MaximumLength(200)
                .WithMessage("Display name cannot exceed 200 characters");

            RuleFor(x => x.Category)
                .NotEmpty()
                .WithMessage("Category is required")
                .MaximumLength(50)
                .WithMessage("Category cannot exceed 50 characters");

            RuleFor(x => x.CreatedBy)
                .NotEmpty()
                .WithMessage("CreatedBy is required");

            RuleFor(x => x.Version)
                .NotEmpty()
                .WithMessage("Version is required")
                .Matches(@"^\d+\.\d+(\.\d+)?$")
                .WithMessage("Version must be in format x.y or x.y.z");
        }
    }

    public class AddFormFieldCommandValidator : AbstractValidator<AddFormFieldCommand>
    {
        public AddFormFieldCommandValidator()
        {
            RuleFor(x => x.FormDefinitionId)
                .NotEmpty()
                .WithMessage("Form definition ID is required");

            RuleFor(x => x.Name)
                .NotEmpty()
                .WithMessage("Field name is required")
                .MaximumLength(100)
                .WithMessage("Field name cannot exceed 100 characters");

            RuleFor(x => x.DisplayName)
                .NotEmpty()
                .WithMessage("Display name is required")
                .MaximumLength(200)
                .WithMessage("Display name cannot exceed 200 characters");

            RuleFor(x => x.FieldType)
                .NotEmpty()
                .WithMessage("Field type is required")
                .Must(BeValidFieldType)
                .WithMessage("Invalid field type");

            RuleFor(x => x.Order)
                .GreaterThan(0)
                .WithMessage("Order must be greater than 0");
        }

        private bool BeValidFieldType(string fieldType)
        {
            var validTypes = new[] { "text", "email", "number", "select", "checkbox", "radio", "file", "date", "datetime", "textarea", "password", "url", "tel" };
            return validTypes.Contains(fieldType.ToLowerInvariant());
        }
    }

    public class SubmitFormCommandValidator : AbstractValidator<SubmitFormCommand>
    {
        public SubmitFormCommandValidator()
        {
            RuleFor(x => x.FormDefinitionId)
                .NotEmpty()
                .WithMessage("Form definition ID is required");

            RuleFor(x => x.UserId)
                .NotEmpty()
                .WithMessage("User ID is required");

            RuleFor(x => x.FormData)
                .NotNull()
                .WithMessage("Form data is required");
        }
    }
}
