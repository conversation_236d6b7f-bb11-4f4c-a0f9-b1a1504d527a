using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Domain.Entities;
using CommunicationNotification.Domain.ValueObjects;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace CommunicationNotification.Infrastructure.Services;

/// <summary>
/// Service for Natural Language Processing using OpenAI GPT and other providers
/// </summary>
public class NLPService : INLPService
{
    private readonly ILogger<NLPService> _logger;
    private readonly HttpClient _httpClient;
    private readonly string _openAIApiKey;
    private readonly string _openAIModel;

    public NLPService(
        IConfiguration configuration,
        ILogger<NLPService> logger,
        HttpClient httpClient)
    {
        _logger = logger;
        _httpClient = httpClient;
        _openAIApiKey = configuration["OpenAI:ApiKey"] ?? "";
        _openAIModel = configuration["OpenAI:Model"] ?? "gpt-3.5-turbo";

        if (!string.IsNullOrEmpty(_openAIApiKey))
        {
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_openAIApiKey}");
        }
    }

    public async Task<NLPResult> ProcessTextAsync(
        string text,
        string language = "en-US",
        List<ChatbotIntent>? intents = null,
        List<ChatbotEntity>? entities = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Processing text with NLP: {Text}", text);

            // For demo purposes, simulate NLP processing
            // In production, this would integrate with OpenAI, Azure Cognitive Services, or Google Cloud NLP
            var simulatedResult = await SimulateNLPProcessingAsync(text, language, intents, entities, cancellationToken);

            _logger.LogDebug("NLP processing completed with intent {Intent} and confidence {Confidence}", 
                simulatedResult.Intent, simulatedResult.Confidence);

            return simulatedResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing text with NLP");
            return NLPResult.Failure(text, $"NLP processing failed: {ex.Message}");
        }
    }

    public async Task<Dictionary<string, object>> ExtractEntitiesAsync(
        string text,
        List<ChatbotEntity> entities,
        string language = "en-US",
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Extracting entities from text: {Text}", text);

            var extractedEntities = new Dictionary<string, object>();

            foreach (var entity in entities)
            {
                var value = ExtractEntityValue(text, entity);
                if (value != null)
                {
                    extractedEntities[entity.Name] = value;
                }
            }

            _logger.LogDebug("Extracted {Count} entities from text", extractedEntities.Count);
            return extractedEntities;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting entities from text");
            return new Dictionary<string, object>();
        }
    }

    public async Task<(string Sentiment, decimal Score)> AnalyzeSentimentAsync(
        string text,
        string language = "en-US",
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Analyzing sentiment for text: {Text}", text);

            // Simulate sentiment analysis
            var sentiment = SimulateSentimentAnalysis(text);
            
            _logger.LogDebug("Sentiment analysis completed: {Sentiment} with score {Score}", 
                sentiment.Sentiment, sentiment.Score);

            return sentiment;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing sentiment");
            return ("neutral", 0.5m);
        }
    }

    public async Task<string> DetectLanguageAsync(
        string text,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Detecting language for text: {Text}", text);

            // Simple language detection based on character patterns
            var detectedLanguage = DetectLanguageFromText(text);
            
            _logger.LogDebug("Detected language: {Language}", detectedLanguage);
            return detectedLanguage;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error detecting language");
            return "en-US";
        }
    }

    public async Task<string> GenerateResponseAsync(
        string userMessage,
        string context,
        string language = "en-US",
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Generating AI response for message: {Message}", userMessage);

            if (string.IsNullOrEmpty(_openAIApiKey))
            {
                // Fallback to rule-based response generation
                return GenerateRuleBasedResponse(userMessage, context, language);
            }

            // Call OpenAI API for response generation
            var response = await CallOpenAIAsync(userMessage, context, language, cancellationToken);
            
            _logger.LogDebug("Generated AI response: {Response}", response);
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating AI response");
            return GenerateRuleBasedResponse(userMessage, context, language);
        }
    }

    public async Task<bool> TrainModelAsync(
        List<ChatbotIntent> intents,
        List<ChatbotEntity> entities,
        string modelId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Training NLP model {ModelId} with {IntentCount} intents and {EntityCount} entities",
                modelId, intents.Count, entities.Count);

            // Simulate model training
            await Task.Delay(2000, cancellationToken); // Simulate training time

            _logger.LogInformation("Successfully trained NLP model {ModelId}", modelId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error training NLP model {ModelId}", modelId);
            return false;
        }
    }

    public async Task<List<string>> GetSupportedLanguagesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var supportedLanguages = new List<string>
            {
                "en-US", "en-GB", "en-AU",
                "hi-IN", "kn-IN", "ta-IN", "te-IN", "mr-IN", "gu-IN", "bn-IN",
                "es-ES", "fr-FR", "de-DE", "it-IT", "pt-BR",
                "ja-JP", "ko-KR", "zh-CN", "ar-SA"
            };

            _logger.LogDebug("Retrieved {Count} supported languages for NLP", supportedLanguages.Count);
            return supportedLanguages;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting supported languages");
            throw;
        }
    }

    // Private helper methods
    private async Task<NLPResult> SimulateNLPProcessingAsync(
        string text,
        string language,
        List<ChatbotIntent>? intents,
        List<ChatbotEntity>? entities,
        CancellationToken cancellationToken)
    {
        // Simulate processing time
        await Task.Delay(200, cancellationToken);

        var normalizedText = text.ToLowerInvariant();
        string? matchedIntent = null;
        decimal confidence = 0.5m;

        // Simple intent matching based on keywords
        if (intents != null)
        {
            foreach (var intent in intents.Where(i => i.IsEnabled))
            {
                foreach (var phrase in intent.TrainingPhrases)
                {
                    if (normalizedText.Contains(phrase.ToLowerInvariant()))
                    {
                        matchedIntent = intent.Name;
                        confidence = 0.85m + (decimal)(new Random().NextDouble() * 0.14); // 85-99%
                        break;
                    }
                }
                if (matchedIntent != null) break;
            }
        }

        // Fallback intent matching based on common patterns
        if (matchedIntent == null)
        {
            matchedIntent = MatchCommonIntents(normalizedText);
            confidence = 0.7m;
        }

        // Extract entities
        var extractedEntities = new Dictionary<string, object>();
        if (entities != null)
        {
            extractedEntities = await ExtractEntitiesAsync(text, entities, language, cancellationToken);
        }

        // Analyze sentiment
        var (sentiment, sentimentScore) = await AnalyzeSentimentAsync(text, language, cancellationToken);

        return NLPResult.Success(text, matchedIntent, confidence, extractedEntities, sentiment, sentimentScore, language);
    }

    private string? MatchCommonIntents(string text)
    {
        var intentPatterns = new Dictionary<string, string[]>
        {
            ["greeting"] = new[] { "hello", "hi", "hey", "good morning", "good afternoon", "good evening" },
            ["goodbye"] = new[] { "bye", "goodbye", "see you", "farewell", "take care" },
            ["help"] = new[] { "help", "assist", "support", "problem", "issue" },
            ["booking"] = new[] { "book", "reserve", "schedule", "appointment", "trip" },
            ["status"] = new[] { "status", "track", "where", "location", "update" },
            ["cancel"] = new[] { "cancel", "stop", "abort", "terminate" },
            ["complaint"] = new[] { "complain", "problem", "issue", "wrong", "bad", "terrible" },
            ["thanks"] = new[] { "thank", "thanks", "appreciate", "grateful" }
        };

        foreach (var pattern in intentPatterns)
        {
            if (pattern.Value.Any(keyword => text.Contains(keyword)))
            {
                return pattern.Key;
            }
        }

        return null;
    }

    private object? ExtractEntityValue(string text, ChatbotEntity entity)
    {
        switch (entity.Type)
        {
            case EntityType.PhoneNumber:
                return ExtractPhoneNumber(text);
            case EntityType.Email:
                return ExtractEmail(text);
            case EntityType.Number:
                return ExtractNumber(text);
            case EntityType.Date:
                return ExtractDate(text);
            case EntityType.Location:
                return ExtractLocation(text);
            case EntityType.Custom:
                return ExtractCustomEntity(text, entity);
            default:
                return ExtractTextEntity(text, entity);
        }
    }

    private string? ExtractPhoneNumber(string text)
    {
        var phonePattern = @"\b\d{10}\b|\b\d{3}[-.\s]?\d{3}[-.\s]?\d{4}\b";
        var match = Regex.Match(text, phonePattern);
        return match.Success ? match.Value : null;
    }

    private string? ExtractEmail(string text)
    {
        var emailPattern = @"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b";
        var match = Regex.Match(text, emailPattern);
        return match.Success ? match.Value : null;
    }

    private decimal? ExtractNumber(string text)
    {
        var numberPattern = @"\b\d+(?:\.\d+)?\b";
        var match = Regex.Match(text, numberPattern);
        return match.Success && decimal.TryParse(match.Value, out var number) ? number : null;
    }

    private DateTime? ExtractDate(string text)
    {
        // Simple date extraction - in production, use more sophisticated NLP
        var datePatterns = new[]
        {
            @"\b\d{1,2}/\d{1,2}/\d{4}\b",
            @"\b\d{4}-\d{2}-\d{2}\b",
            @"\b(today|tomorrow|yesterday)\b"
        };

        foreach (var pattern in datePatterns)
        {
            var match = Regex.Match(text, pattern, RegexOptions.IgnoreCase);
            if (match.Success)
            {
                if (DateTime.TryParse(match.Value, out var date))
                    return date;
                
                // Handle relative dates
                if (match.Value.ToLower() == "today")
                    return DateTime.Today;
                if (match.Value.ToLower() == "tomorrow")
                    return DateTime.Today.AddDays(1);
                if (match.Value.ToLower() == "yesterday")
                    return DateTime.Today.AddDays(-1);
            }
        }

        return null;
    }

    private string? ExtractLocation(string text)
    {
        // Simple location extraction - in production, use geolocation APIs
        var locationKeywords = new[] { "bangalore", "mumbai", "delhi", "chennai", "hyderabad", "pune", "kolkata" };
        var normalizedText = text.ToLowerInvariant();
        
        return locationKeywords.FirstOrDefault(location => normalizedText.Contains(location));
    }

    private object? ExtractCustomEntity(string text, ChatbotEntity entity)
    {
        if (!string.IsNullOrEmpty(entity.RegexPattern))
        {
            var match = Regex.Match(text, entity.RegexPattern, RegexOptions.IgnoreCase);
            return match.Success ? match.Value : null;
        }

        // Check synonyms
        var normalizedText = text.ToLowerInvariant();
        return entity.Synonyms.FirstOrDefault(synonym => normalizedText.Contains(synonym.ToLowerInvariant()));
    }

    private string? ExtractTextEntity(string text, ChatbotEntity entity)
    {
        var normalizedText = text.ToLowerInvariant();
        return entity.Synonyms.FirstOrDefault(synonym => normalizedText.Contains(synonym.ToLowerInvariant()));
    }

    private (string Sentiment, decimal Score) SimulateSentimentAnalysis(string text)
    {
        var positiveWords = new[] { "good", "great", "excellent", "amazing", "wonderful", "fantastic", "love", "like", "happy", "satisfied" };
        var negativeWords = new[] { "bad", "terrible", "awful", "hate", "dislike", "angry", "frustrated", "disappointed", "sad", "upset" };

        var normalizedText = text.ToLowerInvariant();
        var positiveCount = positiveWords.Count(word => normalizedText.Contains(word));
        var negativeCount = negativeWords.Count(word => normalizedText.Contains(word));

        if (positiveCount > negativeCount)
            return ("positive", 0.7m + (decimal)(new Random().NextDouble() * 0.3));
        else if (negativeCount > positiveCount)
            return ("negative", 0.1m + (decimal)(new Random().NextDouble() * 0.3));
        else
            return ("neutral", 0.4m + (decimal)(new Random().NextDouble() * 0.2));
    }

    private string DetectLanguageFromText(string text)
    {
        // Simple language detection based on character patterns
        if (Regex.IsMatch(text, @"[\u0900-\u097F]")) return "hi-IN"; // Devanagari (Hindi)
        if (Regex.IsMatch(text, @"[\u0C80-\u0CFF]")) return "kn-IN"; // Kannada
        if (Regex.IsMatch(text, @"[\u0B80-\u0BFF]")) return "ta-IN"; // Tamil
        if (Regex.IsMatch(text, @"[\u0C00-\u0C7F]")) return "te-IN"; // Telugu
        if (Regex.IsMatch(text, @"[\u4e00-\u9fff]")) return "zh-CN"; // Chinese
        if (Regex.IsMatch(text, @"[\u3040-\u309f\u30a0-\u30ff]")) return "ja-JP"; // Japanese
        if (Regex.IsMatch(text, @"[\uac00-\ud7af]")) return "ko-KR"; // Korean
        if (Regex.IsMatch(text, @"[\u0600-\u06ff]")) return "ar-SA"; // Arabic

        return "en-US"; // Default to English
    }

    private async Task<string> CallOpenAIAsync(string userMessage, string context, string language, CancellationToken cancellationToken)
    {
        try
        {
            var requestBody = new
            {
                model = _openAIModel,
                messages = new[]
                {
                    new { role = "system", content = $"You are a helpful assistant for a transportation and logistics platform. Context: {context}. Respond in {language}." },
                    new { role = "user", content = userMessage }
                },
                max_tokens = 150,
                temperature = 0.7
            };

            var json = JsonSerializer.Serialize(requestBody);
            var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("https://api.openai.com/v1/chat/completions", content, cancellationToken);
            
            if (response.IsSuccessStatusCode)
            {
                var responseJson = await response.Content.ReadAsStringAsync(cancellationToken);
                var responseData = JsonSerializer.Deserialize<JsonElement>(responseJson);
                
                if (responseData.TryGetProperty("choices", out var choices) && choices.GetArrayLength() > 0)
                {
                    var firstChoice = choices[0];
                    if (firstChoice.TryGetProperty("message", out var message) &&
                        message.TryGetProperty("content", out var messageContent))
                    {
                        return messageContent.GetString() ?? GenerateRuleBasedResponse(userMessage, context, language);
                    }
                }
            }

            return GenerateRuleBasedResponse(userMessage, context, language);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calling OpenAI API");
            return GenerateRuleBasedResponse(userMessage, context, language);
        }
    }

    private string GenerateRuleBasedResponse(string userMessage, string context, string language)
    {
        var normalizedMessage = userMessage.ToLowerInvariant();

        // Rule-based responses based on common patterns
        if (normalizedMessage.Contains("hello") || normalizedMessage.Contains("hi"))
            return "Hello! How can I help you today?";
        
        if (normalizedMessage.Contains("book") || normalizedMessage.Contains("reserve"))
            return "I'd be happy to help you with booking. Could you please provide more details about your trip?";
        
        if (normalizedMessage.Contains("track") || normalizedMessage.Contains("status"))
            return "I can help you track your shipment. Please provide your tracking number.";
        
        if (normalizedMessage.Contains("cancel"))
            return "I understand you want to cancel. Let me help you with that. Could you provide your booking reference?";
        
        if (normalizedMessage.Contains("help"))
            return "I'm here to help! You can ask me about bookings, tracking, cancellations, or any other questions about our services.";
        
        if (normalizedMessage.Contains("thank"))
            return "You're welcome! Is there anything else I can help you with?";
        
        if (normalizedMessage.Contains("bye") || normalizedMessage.Contains("goodbye"))
            return "Goodbye! Have a great day and thank you for using our services.";

        return "I understand. Could you please provide more details so I can assist you better?";
    }
}
