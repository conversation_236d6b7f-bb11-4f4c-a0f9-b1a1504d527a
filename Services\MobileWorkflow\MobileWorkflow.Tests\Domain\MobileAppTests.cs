using FluentAssertions;
using MobileWorkflow.Domain.Entities;
using Xunit;

namespace MobileWorkflow.Tests.Domain;

public class MobileAppTests
{
    [Fact]
    public void CreateMobileApp_WithValidData_ShouldCreateSuccessfully()
    {
        // Arrange
        var name = "TLI Driver App";
        var version = "1.0.0";
        var platform = "Android";
        var packageId = "com.tli.driver.android";
        var releaseDate = DateTime.UtcNow;
        var minimumOSVersion = "8.0";
        var features = new Dictionary<string, object> { { "offline_mode", true } };
        var configuration = new Dictionary<string, object> { { "sync_interval", 300 } };
        var supportsOffline = true;
        var downloadUrl = "https://play.google.com/store/apps/details?id=com.tli.driver";
        var fileSize = 25000000L;
        var checksum = "abc123def456";

        // Act
        var mobileApp = new MobileApp(
            name, version, platform, packageId, releaseDate, minimumOSVersion,
            features, configuration, supportsOffline, downloadUrl, fileSize, checksum);

        // Assert
        mobileApp.Should().NotBeNull();
        mobileApp.Name.Should().Be(name);
        mobileApp.Version.Should().Be(version);
        mobileApp.Platform.Should().Be(platform);
        mobileApp.PackageId.Should().Be(packageId);
        mobileApp.IsActive.Should().BeTrue();
        mobileApp.SupportsOffline.Should().Be(supportsOffline);
        mobileApp.Features.Should().ContainKey("offline_mode");
        mobileApp.Configuration.Should().ContainKey("sync_interval");
    }

    [Fact]
    public void UpdateVersion_WithValidData_ShouldUpdateSuccessfully()
    {
        // Arrange
        var mobileApp = CreateTestMobileApp();
        var newVersion = "1.1.0";
        var newDownloadUrl = "https://play.google.com/store/apps/details?id=com.tli.driver.v2";
        var newFileSize = 26000000L;
        var newChecksum = "def456ghi789";
        var newFeatures = new Dictionary<string, object> { { "biometric_auth", true } };

        // Act
        mobileApp.UpdateVersion(newVersion, newDownloadUrl, newFileSize, newChecksum, newFeatures);

        // Assert
        mobileApp.Version.Should().Be(newVersion);
        mobileApp.DownloadUrl.Should().Be(newDownloadUrl);
        mobileApp.FileSize.Should().Be(newFileSize);
        mobileApp.Checksum.Should().Be(newChecksum);
        mobileApp.Features.Should().ContainKey("biometric_auth");
    }

    [Fact]
    public void Activate_WhenCalled_ShouldSetIsActiveToTrue()
    {
        // Arrange
        var mobileApp = CreateTestMobileApp();
        mobileApp.Deactivate();

        // Act
        mobileApp.Activate();

        // Assert
        mobileApp.IsActive.Should().BeTrue();
    }

    [Fact]
    public void Deactivate_WhenCalled_ShouldSetIsActiveToFalse()
    {
        // Arrange
        var mobileApp = CreateTestMobileApp();

        // Act
        mobileApp.Deactivate();

        // Assert
        mobileApp.IsActive.Should().BeFalse();
    }

    [Theory]
    [InlineData(null)]
    [InlineData("")]
    public void CreateMobileApp_WithInvalidName_ShouldThrowArgumentNullException(string invalidName)
    {
        // Act & Assert
        var act = () => new MobileApp(
            invalidName, "1.0.0", "Android", "com.test.app", DateTime.UtcNow, "8.0",
            new Dictionary<string, object>(), new Dictionary<string, object>(),
            true, "https://test.com", 1000000L, "checksum123");

        act.Should().Throw<ArgumentNullException>();
    }

    [Theory]
    [InlineData(null)]
    [InlineData("")]
    public void CreateMobileApp_WithInvalidPackageId_ShouldThrowArgumentNullException(string invalidPackageId)
    {
        // Act & Assert
        var act = () => new MobileApp(
            "Test App", "1.0.0", "Android", invalidPackageId, DateTime.UtcNow, "8.0",
            new Dictionary<string, object>(), new Dictionary<string, object>(),
            true, "https://test.com", 1000000L, "checksum123");

        act.Should().Throw<ArgumentNullException>();
    }

    private static MobileApp CreateTestMobileApp()
    {
        return new MobileApp(
            "Test App",
            "1.0.0",
            "Android",
            "com.test.app",
            DateTime.UtcNow,
            "8.0",
            new Dictionary<string, object> { { "offline_mode", true } },
            new Dictionary<string, object> { { "sync_interval", 300 } },
            true,
            "https://test.com",
            1000000L,
            "checksum123");
    }
}

public class MobileSessionTests
{
    [Fact]
    public void CreateMobileSession_WithValidData_ShouldCreateSuccessfully()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var mobileAppId = Guid.NewGuid();
        var deviceId = "device123";
        var deviceInfo = "Samsung Galaxy S21";
        var appVersion = "1.0.0";
        var platform = "Android";
        var osVersion = "11.0";
        var isOfflineCapable = true;

        // Act
        var session = new MobileSession(
            userId, mobileAppId, deviceId, deviceInfo, appVersion, platform, osVersion, isOfflineCapable);

        // Assert
        session.Should().NotBeNull();
        session.UserId.Should().Be(userId);
        session.MobileAppId.Should().Be(mobileAppId);
        session.DeviceId.Should().Be(deviceId);
        session.IsActive.Should().BeTrue();
        session.IsOfflineCapable.Should().Be(isOfflineCapable);
        session.OfflineActionsCount.Should().Be(0);
    }

    [Fact]
    public void UpdateLocation_WithValidLocation_ShouldUpdateSuccessfully()
    {
        // Arrange
        var session = CreateTestMobileSession();
        var location = "12.9716,77.5946";

        // Act
        session.UpdateLocation(location);

        // Assert
        session.LastKnownLocation.Should().Be(location);
        session.SessionData.Should().ContainKey("last_location_update");
    }

    [Fact]
    public void RecordOfflineAction_WhenCalled_ShouldIncrementCount()
    {
        // Arrange
        var session = CreateTestMobileSession();
        var initialCount = session.OfflineActionsCount;

        // Act
        session.RecordOfflineAction();

        // Assert
        session.OfflineActionsCount.Should().Be(initialCount + 1);
        session.SessionData.Should().ContainKey("offline_actions_count");
    }

    [Fact]
    public void SyncCompleted_WhenCalled_ShouldResetOfflineActionsCount()
    {
        // Arrange
        var session = CreateTestMobileSession();
        session.RecordOfflineAction();
        session.RecordOfflineAction();

        // Act
        session.SyncCompleted();

        // Assert
        session.OfflineActionsCount.Should().Be(0);
        session.SessionData.Should().ContainKey("last_sync");
    }

    [Fact]
    public void EndSession_WhenActive_ShouldEndSuccessfully()
    {
        // Arrange
        var session = CreateTestMobileSession();

        // Act
        session.EndSession();

        // Assert
        session.IsActive.Should().BeFalse();
        session.EndTime.Should().NotBeNull();
        session.EndTime.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void EndSession_WhenAlreadyEnded_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var session = CreateTestMobileSession();
        session.EndSession();

        // Act & Assert
        var act = () => session.EndSession();
        act.Should().Throw<InvalidOperationException>()
           .WithMessage("Session is already ended");
    }

    [Fact]
    public void GetSessionDuration_WhenActive_ShouldReturnCurrentDuration()
    {
        // Arrange
        var session = CreateTestMobileSession();
        Thread.Sleep(100); // Small delay to ensure duration > 0

        // Act
        var duration = session.GetSessionDuration();

        // Assert
        duration.Should().BeGreaterThan(TimeSpan.Zero);
    }

    [Fact]
    public void GetSessionDuration_WhenEnded_ShouldReturnActualDuration()
    {
        // Arrange
        var session = CreateTestMobileSession();
        Thread.Sleep(100);
        session.EndSession();

        // Act
        var duration = session.GetSessionDuration();

        // Assert
        duration.Should().BeGreaterThan(TimeSpan.Zero);
        duration.Should().BeLessThan(TimeSpan.FromSeconds(1));
    }

    private static MobileSession CreateTestMobileSession()
    {
        return new MobileSession(
            Guid.NewGuid(),
            Guid.NewGuid(),
            "device123",
            "Test Device",
            "1.0.0",
            "Android",
            "11.0",
            true);
    }
}
