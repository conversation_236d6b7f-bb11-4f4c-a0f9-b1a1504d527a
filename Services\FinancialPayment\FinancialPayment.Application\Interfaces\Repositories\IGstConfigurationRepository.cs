using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Application.Interfaces.Repositories;

public interface IGstConfigurationRepository
{
    Task<GstConfiguration?> GetByIdAsync(Guid id);
    Task<List<GstConfiguration>> GetAllAsync();
    Task<List<GstConfiguration>> GetActiveAsync();
    Task<List<GstConfiguration>> GetByStatusAsync(TaxConfigurationStatus status);
    Task<List<GstConfiguration>> GetByServiceCategoryAsync(ServiceCategory serviceCategory);
    Task<List<GstConfiguration>> GetByJurisdictionAsync(TaxJurisdiction jurisdiction);
    Task<List<GstConfiguration>> GetByGstRateAsync(GstRate gstRate);
    Task<List<GstConfiguration>> GetEffectiveOnDateAsync(DateTime date);
    Task<List<GstConfiguration>> GetByServiceCategoryAndJurisdictionAsync(ServiceCategory serviceCategory, TaxJurisdiction jurisdiction);
    Task<GstConfiguration?> GetApplicableConfigurationAsync(ServiceCategory serviceCategory, TaxJurisdiction jurisdiction, DateTime date);
    Task<GstConfiguration?> GetDefaultConfigurationAsync(TaxJurisdiction jurisdiction);
    Task<List<GstConfiguration>> GetByHsnCodeAsync(string hsnCode);
    Task<List<GstConfiguration>> GetWithReverseChargeAsync();
    Task<List<GstConfiguration>> GetByCreatedByAsync(string createdBy);
    Task<List<GstConfiguration>> GetByDateRangeAsync(DateTime from, DateTime to);
    Task<GstConfiguration> AddAsync(GstConfiguration gstConfiguration);
    Task UpdateAsync(GstConfiguration gstConfiguration);
    Task DeleteAsync(Guid id);
    Task<bool> ExistsAsync(Guid id);
    Task<bool> ExistsByNameAsync(string name, Guid? excludeId = null);
    Task<int> GetCountByStatusAsync(TaxConfigurationStatus status);
    Task<List<GstConfiguration>> SearchAsync(string searchTerm, int skip = 0, int take = 50);
    Task<List<GstConfigurationHistory>> GetHistoryAsync(Guid gstConfigurationId);
}
