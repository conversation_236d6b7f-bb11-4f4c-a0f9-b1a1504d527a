using Microsoft.AspNetCore.Http;

namespace UserManagement.Application.Interfaces;

public interface IFileStorageService
{
    Task<string> UploadFileAsync(IFormFile file, string containerName);
    Task<byte[]> DownloadFileAsync(string fileName, string containerName);
    Task DeleteFileAsync(string fileName, string containerName);
    Task<bool> FileExistsAsync(string fileName, string containerName);
}
