# Workflow Configuration Guide

## Overview

This guide provides comprehensive instructions for configuring and managing business process workflows in the TLI Mobile & Workflow Service. Learn how to create, customize, and optimize workflows for various business scenarios.

## Workflow Concepts

### Core Components

1. **Workflow Definition**: The blueprint that defines the process steps and flow
2. **Workflow Execution**: A runtime instance of a workflow processing specific data
3. **Workflow Tasks**: Individual work items assigned to users or systems
4. **Triggers**: Events or conditions that start workflow execution
5. **Steps**: Individual actions within a workflow (Manual, Automated, Approval, etc.)

### Workflow Types

- **Manual Workflows**: Require human intervention at each step
- **Automated Workflows**: Execute without human intervention
- **Hybrid Workflows**: Combine manual and automated steps
- **Approval Workflows**: Include approval gates and decision points

## Creating Workflows

### Basic Workflow Structure

```json
{
  "name": "Driver Onboarding",
  "description": "Complete workflow for onboarding new drivers",
  "category": "UserOnboarding",
  "version": "1.0",
  "definition": {
    "steps": [
      {
        "id": "collect_documents",
        "name": "Collect Documents",
        "type": "Manual",
        "parameters": {
          "required_docs": ["license", "aadhar", "pan"],
          "assigned_to_role": "hr_admin",
          "sla": "24:00:00",
          "instructions": "Collect and verify all required documents"
        }
      },
      {
        "id": "verify_identity",
        "name": "Verify Identity",
        "type": "Automated",
        "parameters": {
          "verification_service": "aadhar_api",
          "retry_attempts": 3,
          "timeout": "00:05:00"
        }
      },
      {
        "id": "background_check",
        "name": "Background Check",
        "type": "Automated",
        "parameters": {
          "check_type": "criminal_record",
          "external_service": "background_check_api"
        }
      },
      {
        "id": "final_approval",
        "name": "Final Approval",
        "type": "Approval",
        "parameters": {
          "approvers": ["hr_manager", "operations_manager"],
          "approval_type": "any", // "any" or "all"
          "sla": "48:00:00"
        }
      }
    ],
    "transitions": [
      {
        "from": "collect_documents",
        "to": "verify_identity",
        "condition": "documents_complete"
      },
      {
        "from": "verify_identity",
        "to": "background_check",
        "condition": "identity_verified"
      },
      {
        "from": "background_check",
        "to": "final_approval",
        "condition": "background_clear"
      }
    ]
  },
  "configuration": {
    "timeout": "168:00:00", // 7 days
    "auto_retry_failed_steps": true,
    "max_retry_attempts": 3,
    "notification_settings": {
      "notify_on_start": true,
      "notify_on_completion": true,
      "notify_on_failure": true,
      "escalation_enabled": true,
      "escalation_delay": "24:00:00"
    }
  },
  "triggerType": "Manual",
  "triggerConfiguration": {
    "allowed_roles": ["hr_admin", "admin"],
    "required_data": ["user_id", "employee_type"]
  }
}
```

### Step Types and Parameters

#### 1. Manual Steps

```json
{
  "id": "review_application",
  "name": "Review Application",
  "type": "Manual",
  "parameters": {
    "assigned_to": "uuid-of-specific-user", // Optional: specific user
    "assigned_to_role": "reviewer", // Optional: role-based assignment
    "sla": "24:00:00",
    "priority": 2, // 1=High, 2=Medium, 3=Low
    "instructions": "Review the application thoroughly",
    "required_fields": ["decision", "comments"],
    "form_schema": {
      "decision": {
        "type": "select",
        "options": ["approve", "reject", "request_more_info"],
        "required": true
      },
      "comments": {
        "type": "textarea",
        "required": true,
        "min_length": 10
      }
    }
  }
}
```

#### 2. Automated Steps

```json
{
  "id": "send_notification",
  "name": "Send Welcome Email",
  "type": "Automated",
  "parameters": {
    "action": "send_email",
    "email_template": "driver_welcome",
    "recipients": ["${user.email}"],
    "retry_attempts": 3,
    "timeout": "00:02:00",
    "on_failure": "continue" // "continue", "stop", "retry"
  }
}
```

#### 3. Approval Steps

```json
{
  "id": "manager_approval",
  "name": "Manager Approval",
  "type": "Approval",
  "parameters": {
    "approvers": ["manager1_uuid", "manager2_uuid"],
    "approval_type": "any", // "any" or "all"
    "sla": "48:00:00",
    "escalation": {
      "enabled": true,
      "delay": "24:00:00",
      "escalate_to": ["senior_manager_uuid"]
    },
    "auto_approve_conditions": {
      "amount_less_than": 1000,
      "user_level": "senior"
    }
  }
}
```

#### 4. Condition Steps

```json
{
  "id": "check_amount",
  "name": "Check Amount",
  "type": "Condition",
  "parameters": {
    "condition": "${amount} > 5000",
    "true_path": "high_value_approval",
    "false_path": "auto_approve"
  }
}
```

#### 5. Delay Steps

```json
{
  "id": "wait_period",
  "name": "Waiting Period",
  "type": "Delay",
  "parameters": {
    "delay_minutes": 1440, // 24 hours
    "reason": "Mandatory waiting period for verification"
  }
}
```

## Workflow Templates

### Pre-built Templates

#### 1. Driver Onboarding Template

```http
GET /api/businessprocess/templates?category=UserOnboarding
```

```json
{
  "id": "driver-onboarding-workflow",
  "name": "Driver Onboarding Workflow",
  "description": "Complete workflow for onboarding new drivers",
  "category": "UserOnboarding",
  "steps": [
    {
      "name": "Collect Documents",
      "type": "Manual",
      "description": "Collect required documents from driver"
    },
    {
      "name": "Verify Identity",
      "type": "Automated",
      "description": "Verify driver identity using Aadhar/PAN"
    },
    {
      "name": "Background Check",
      "type": "Automated",
      "description": "Perform background verification"
    },
    {
      "name": "Training Assignment",
      "type": "Automated",
      "description": "Assign mandatory training modules"
    },
    {
      "name": "Final Approval",
      "type": "Approval",
      "description": "Final approval for driver activation"
    }
  ]
}
```

#### 2. Trip Completion Template

```json
{
  "id": "trip-completion-workflow",
  "name": "Trip Completion Workflow",
  "description": "Automated workflow for trip completion and POD processing",
  "category": "TripManagement",
  "steps": [
    {
      "name": "Verify POD",
      "type": "Automated",
      "description": "Verify proof of delivery documents"
    },
    {
      "name": "Update Trip Status",
      "type": "Automated",
      "description": "Update trip status to completed"
    },
    {
      "name": "Process Payment",
      "type": "Automated",
      "description": "Process payment to carrier"
    },
    {
      "name": "Send Completion Notification",
      "type": "Notification",
      "description": "Notify all parties of trip completion"
    }
  ]
}
```

### Creating Custom Templates

```http
POST /api/businessprocess
Content-Type: application/json

{
  "name": "Custom RFQ Processing",
  "description": "Custom workflow for RFQ processing with special requirements",
  "category": "OrderProcessing",
  "version": "1.0",
  "steps": [
    {
      "name": "Validate RFQ Data",
      "type": "Automated",
      "parameters": {
        "validation_rules": [
          "required_fields_present",
          "valid_pickup_location",
          "valid_delivery_location",
          "valid_dates"
        ]
      }
    },
    {
      "name": "Check Carrier Availability",
      "type": "Automated",
      "parameters": {
        "search_radius": 50,
        "vehicle_type_match": true,
        "capacity_check": true
      }
    },
    {
      "name": "Manual Review",
      "type": "Manual",
      "parameters": {
        "assigned_to_role": "rfq_reviewer",
        "sla": "02:00:00",
        "condition": "${rfq.value} > 10000"
      }
    },
    {
      "name": "Send to Carriers",
      "type": "Automated",
      "parameters": {
        "max_carriers": 5,
        "notification_method": "push_and_sms"
      }
    }
  ],
  "triggerType": "Event",
  "triggerConfiguration": {
    "event": "rfq_created",
    "conditions": {
      "status": "pending_processing"
    }
  }
}
```

## Workflow Execution

### Starting a Workflow

```http
POST /api/workflow/{workflowId}/execute
Content-Type: application/json

{
  "inputData": {
    "userId": "driver-uuid",
    "employeeType": "driver",
    "priority": "high",
    "requestedBy": "hr-admin-uuid"
  },
  "triggerSource": "API"
}
```

### Monitoring Execution

```http
GET /api/workflow/executions/{executionId}
```

Response:
```json
{
  "id": "execution-uuid",
  "workflowId": "workflow-uuid",
  "status": "Running",
  "currentStepIndex": 2,
  "currentStepId": "background_check",
  "startTime": "2024-01-15T10:00:00Z",
  "inputData": {
    "userId": "driver-uuid",
    "employeeType": "driver"
  },
  "context": {
    "documents_collected": true,
    "identity_verified": true,
    "verification_score": 95
  },
  "tasks": [
    {
      "id": "task-uuid",
      "name": "Collect Documents",
      "status": "Completed",
      "assignedTo": "hr-admin-uuid",
      "completedAt": "2024-01-15T11:30:00Z"
    },
    {
      "id": "task-uuid-2",
      "name": "Background Check",
      "status": "Running",
      "startedAt": "2024-01-15T11:35:00Z"
    }
  ]
}
```

## Task Management

### Getting Assigned Tasks

```http
GET /api/workflowtasks/assigned
```

Response:
```json
[
  {
    "id": "task-uuid",
    "workflowExecutionId": "execution-uuid",
    "name": "Review Driver Application",
    "description": "Review and approve driver onboarding application",
    "type": "Manual",
    "status": "Pending",
    "priority": 2,
    "dueDate": "2024-01-16T10:00:00Z",
    "parameters": {
      "instructions": "Review all documents and verify information",
      "required_fields": ["decision", "comments"]
    },
    "sla": "24:00:00",
    "isOverdue": false
  }
]
```

### Completing a Task

```http
POST /api/workflowtasks/{taskId}/complete
Content-Type: application/json

{
  "result": {
    "decision": "approve",
    "comments": "All documents verified and in order",
    "verification_score": 95
  },
  "notes": "Driver meets all requirements for onboarding"
}
```

### Task Reassignment

```http
POST /api/workflowtasks/{taskId}/reassign
Content-Type: application/json

{
  "newAssignedTo": "new-user-uuid",
  "reason": "Original assignee is unavailable"
}
```

## Advanced Configuration

### Conditional Workflows

```json
{
  "steps": [
    {
      "id": "amount_check",
      "name": "Check Amount",
      "type": "Condition",
      "parameters": {
        "condition": "${order.amount} > 5000",
        "true_path": "high_value_process",
        "false_path": "standard_process"
      }
    },
    {
      "id": "high_value_process",
      "name": "High Value Processing",
      "type": "Manual",
      "parameters": {
        "assigned_to_role": "senior_manager",
        "sla": "04:00:00"
      }
    },
    {
      "id": "standard_process",
      "name": "Standard Processing",
      "type": "Automated",
      "parameters": {
        "auto_approve": true
      }
    }
  ]
}
```

### Parallel Execution

```json
{
  "steps": [
    {
      "id": "parallel_start",
      "name": "Start Parallel Tasks",
      "type": "Parallel",
      "parameters": {
        "parallel_steps": [
          "background_check",
          "reference_check",
          "skill_assessment"
        ],
        "wait_for": "all" // "all" or "any"
      }
    },
    {
      "id": "background_check",
      "name": "Background Check",
      "type": "Automated",
      "parameters": {
        "service": "background_api"
      }
    },
    {
      "id": "reference_check",
      "name": "Reference Check",
      "type": "Manual",
      "parameters": {
        "assigned_to_role": "hr_specialist"
      }
    }
  ]
}
```

### Error Handling

```json
{
  "steps": [
    {
      "id": "api_call",
      "name": "External API Call",
      "type": "Automated",
      "parameters": {
        "action": "call_external_api",
        "retry_attempts": 3,
        "retry_delay": "00:01:00",
        "on_failure": "escalate",
        "escalation_step": "manual_review"
      }
    },
    {
      "id": "manual_review",
      "name": "Manual Review",
      "type": "Manual",
      "parameters": {
        "assigned_to_role": "technical_support",
        "instructions": "API call failed, please review manually"
      }
    }
  ]
}
```

## Workflow Analytics

### Getting Workflow Metrics

```http
GET /api/businessprocess/metrics?startDate=2024-01-01&endDate=2024-01-31
```

Response:
```json
{
  "startDate": "2024-01-01T00:00:00Z",
  "endDate": "2024-01-31T23:59:59Z",
  "totalExecutions": 150,
  "successfulExecutions": 142,
  "failedExecutions": 8,
  "successRate": 94.67,
  "averageExecutionTime": "02:15:30",
  "processBreakdown": {
    "UserOnboarding": 45,
    "TripManagement": 75,
    "OrderProcessing": 30
  },
  "dailyExecutionCounts": {
    "2024-01-01": 5,
    "2024-01-02": 8,
    "2024-01-03": 12
  }
}
```

### Performance Optimization

#### 1. Step Optimization

```json
{
  "parameters": {
    "cache_results": true,
    "cache_duration": "01:00:00",
    "batch_processing": true,
    "batch_size": 10
  }
}
```

#### 2. Resource Management

```json
{
  "configuration": {
    "max_concurrent_executions": 50,
    "resource_pool": "high_priority",
    "memory_limit": "512MB",
    "timeout": "01:00:00"
  }
}
```

## Best Practices

### 1. Workflow Design

- **Keep steps atomic**: Each step should perform a single, well-defined action
- **Use meaningful names**: Step and workflow names should be descriptive
- **Plan for failures**: Include error handling and retry logic
- **Set appropriate SLAs**: Balance urgency with realistic completion times

### 2. Task Assignment

- **Use role-based assignment**: Prefer roles over specific users for flexibility
- **Implement escalation**: Define escalation paths for overdue tasks
- **Balance workload**: Distribute tasks evenly across team members

### 3. Performance

- **Optimize step execution**: Minimize external API calls and database queries
- **Use caching**: Cache frequently accessed data
- **Monitor execution times**: Track and optimize slow-running workflows

### 4. Security

- **Validate inputs**: Always validate workflow input data
- **Control access**: Implement proper role-based access control
- **Audit trails**: Maintain comprehensive audit logs

## Troubleshooting

### Common Issues

1. **Workflow Stuck in Running State**
   - Check for failed automated steps
   - Verify external service availability
   - Review task assignments

2. **Tasks Not Being Assigned**
   - Verify role configurations
   - Check user permissions
   - Review assignment logic

3. **Performance Issues**
   - Monitor execution times
   - Check resource utilization
   - Optimize step configurations

### Debug Mode

Enable workflow debugging:

```json
{
  "configuration": {
    "debug_mode": true,
    "log_level": "verbose",
    "trace_execution": true
  }
}
```

This will provide detailed execution logs for troubleshooting workflow issues.
