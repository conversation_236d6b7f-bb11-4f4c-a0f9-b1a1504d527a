using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Domain.Repositories;
using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace AnalyticsBIService.Application.Queries.Carrier;

/// <summary>
/// Handler for carrier broker ratings queries
/// </summary>
public class GetCarrierBrokerRatingsQueryHandler : IRequestHandler<GetCarrierBrokerRatingsQuery, CarrierBrokerRatingsDto>
{
    private readonly IAnalyticsEventRepository _analyticsEventRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetCarrierBrokerRatingsQueryHandler> _logger;

    public GetCarrierBrokerRatingsQueryHandler(
        IAnalyticsEventRepository analyticsEventRepository,
        IMapper mapper,
        ILogger<GetCarrierBrokerRatingsQueryHandler> logger)
    {
        _analyticsEventRepository = analyticsEventRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<CarrierBrokerRatingsDto> Handle(GetCarrierBrokerRatingsQuery request, CancellationToken cancellationToken)
    {
        _logger.LogDebug("Getting broker ratings for carrier {CarrierId}", request.CarrierId);

        // Get broker rating events for this carrier
        var allEvents = await _analyticsEventRepository.GetByDateRangeAsync(request.FromDate, request.ToDate, 1, 1000, cancellationToken);
        var brokerRatingEvents = allEvents.Items
            .Where(e => e.UserId == request.CarrierId &&
                       e.EventName.Contains("BrokerRating"))
            .ToList();

        // Calculate basic metrics (using mock data for demonstration)
        var averageBrokerRating = 4.3m;
        var totalBrokerRatings = 89;
        var uniqueBrokers = 15;
        var brokerSatisfactionScore = 86.5m;

        // Generate top rated brokers
        var topRatedBrokers = new List<BrokerRatingDto>
        {
            new()
            {
                BrokerId = Guid.NewGuid(),
                BrokerName = "ABC Logistics",
                Rating = 4.7m,
                RatingCount = 12,
                LastRatingDate = DateTime.UtcNow.AddDays(-5),
                RelationshipStrength = "Strong",
                PositiveComments = new List<string> { "Excellent communication", "Always on time", "Professional service" },
                ImprovementAreas = new List<string> { "Could improve documentation speed" }
            },
            new()
            {
                BrokerId = Guid.NewGuid(),
                BrokerName = "XYZ Transport",
                Rating = 4.5m,
                RatingCount = 8,
                LastRatingDate = DateTime.UtcNow.AddDays(-3),
                RelationshipStrength = "Strong",
                PositiveComments = new List<string> { "Reliable partner", "Good pricing", "Quick responses" },
                ImprovementAreas = new List<string> { "Better route planning" }
            },
            new()
            {
                BrokerId = Guid.NewGuid(),
                BrokerName = "DEF Freight",
                Rating = 4.2m,
                RatingCount = 6,
                LastRatingDate = DateTime.UtcNow.AddDays(-7),
                RelationshipStrength = "Medium",
                PositiveComments = new List<string> { "Fair pricing", "Consistent work" },
                ImprovementAreas = new List<string> { "Communication could be better", "Payment delays" }
            }
        };

        // Generate broker rating trends
        var brokerRatingTrends = GenerateBrokerRatingTrends(request.FromDate, request.ToDate, averageBrokerRating);

        // Generate broker feedback themes
        var brokerFeedbackThemes = new List<FeedbackThemeDto>
        {
            new() { Theme = "Communication", MentionCount = 45, Percentage = 50.6m, Sentiment = "Positive", ImpactScore = 8.5m, SampleComments = new List<string> { "Great communication throughout", "Always kept us informed" } },
            new() { Theme = "Reliability", MentionCount = 38, Percentage = 42.7m, Sentiment = "Positive", ImpactScore = 9.2m, SampleComments = new List<string> { "Never missed a deadline", "Consistent performance" } },
            new() { Theme = "Pricing", MentionCount = 28, Percentage = 31.5m, Sentiment = "Neutral", ImpactScore = 7.1m, SampleComments = new List<string> { "Fair pricing", "Competitive rates" } },
            new() { Theme = "Documentation", MentionCount = 15, Percentage = 16.9m, Sentiment = "Negative", ImpactScore = 6.8m, SampleComments = new List<string> { "Slow with paperwork", "Missing documents" } }
        };

        return new CarrierBrokerRatingsDto
        {
            AverageBrokerRating = averageBrokerRating,
            TotalBrokerRatings = totalBrokerRatings,
            UniqueBrokers = uniqueBrokers,
            BrokerSatisfactionScore = brokerSatisfactionScore,
            TopRatedBrokers = topRatedBrokers,
            BrokerRatingTrends = brokerRatingTrends,
            BrokerFeedbackThemes = brokerFeedbackThemes
        };
    }

    private List<BrokerRatingTrendDto> GenerateBrokerRatingTrends(DateTime fromDate, DateTime toDate, decimal baseRating)
    {
        var days = (toDate - fromDate).Days;
        var trends = new List<BrokerRatingTrendDto>();

        for (var date = fromDate; date <= toDate; date = date.AddDays(7)) // Weekly trends
        {
            var variance = (decimal)(new Random().NextDouble() * 0.4 - 0.2); // ±0.2 rating variance
            var rating = Math.Max(1, Math.Min(5, baseRating + variance));
            var ratingCount = new Random().Next(2, 8);
            var ratingChange = variance;

            trends.Add(new BrokerRatingTrendDto
            {
                Date = date,
                AverageRating = rating,
                RatingCount = ratingCount,
                RatingChange = ratingChange
            });
        }

        return trends;
    }
}
