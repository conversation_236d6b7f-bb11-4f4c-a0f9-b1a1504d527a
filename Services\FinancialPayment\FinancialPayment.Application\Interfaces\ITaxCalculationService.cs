using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Application.Interfaces;

public interface ITaxCalculationService
{
    Task<TaxCalculation> CalculateTaxAsync(TaxCalculationRequest request);
    Task<List<TaxRule>> GetApplicableTaxRulesAsync(string jurisdiction, DateTime date, string? productCategory = null, string? serviceType = null);
    Task<TaxCalculation> RecalculateTaxAsync(Guid taxCalculationId);
    Task<bool> ValidateTaxCalculationAsync(Guid taxCalculationId);
    Task<TaxCalculationSummary> GetTaxSummaryAsync(Guid orderId);
    Task<List<TaxCalculation>> GetTaxCalculationHistoryAsync(Guid orderId);
}

public interface ITaxRuleRepository
{
    Task<TaxRule?> GetByIdAsync(Guid id);
    Task<List<TaxRule>> GetByJurisdictionAsync(string jurisdiction);
    Task<List<TaxRule>> GetApplicableRulesAsync(string jurisdiction, DateTime date, string? productCategory = null, string? serviceType = null);
    Task AddAsync(TaxRule taxRule);
    Task UpdateAsync(TaxRule taxRule);
    Task DeleteAsync(Guid id);
    Task<List<TaxRule>> GetAllActiveAsync();
}

public interface ITaxCalculationRepository
{
    Task<TaxCalculation?> GetByIdAsync(Guid id);
    Task<TaxCalculation?> GetByOrderIdAsync(Guid orderId);
    Task<List<TaxCalculation>> GetByOrderIdsAsync(List<Guid> orderIds);
    Task<List<TaxCalculation>> GetHistoryByOrderIdAsync(Guid orderId);
    Task AddAsync(TaxCalculation taxCalculation);
    Task UpdateAsync(TaxCalculation taxCalculation);
    Task DeleteAsync(Guid id);
}

public class TaxCalculationRequest
{
    public Guid OrderId { get; set; }
    public Guid? UserId { get; set; }
    public Money Amount { get; set; } = Money.Zero("INR");
    public string Jurisdiction { get; set; } = string.Empty;
    public string? ProductCategory { get; set; }
    public string? ServiceType { get; set; }
    public DateTime? CalculationDate { get; set; }
    public Dictionary<string, object> Context { get; set; } = new();
    public bool ForceRecalculation { get; set; } = false;
}

public class TaxCalculationResult
{
    public bool IsSuccess { get; set; }
    public TaxCalculation? TaxCalculation { get; set; }
    public string? ErrorMessage { get; set; }
    public List<string> Warnings { get; set; } = new();
    public List<TaxRule> AppliedRules { get; set; } = new();
}

public class TaxRuleValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
}

public class TaxJurisdiction
{
    public string Code { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty; // Country, State, City
    public string? ParentJurisdiction { get; set; }
    public bool IsActive { get; set; }
    public List<string> SupportedTaxTypes { get; set; } = new();
}

public class TaxConfiguration
{
    public string DefaultJurisdiction { get; set; } = string.Empty;
    public string DefaultCurrency { get; set; } = "INR";
    public bool EnableTaxCalculation { get; set; } = true;
    public bool EnableAutomaticTaxApplication { get; set; } = true;
    public int TaxCalculationTimeoutSeconds { get; set; } = 30;
    public List<TaxJurisdiction> SupportedJurisdictions { get; set; } = new();
}
