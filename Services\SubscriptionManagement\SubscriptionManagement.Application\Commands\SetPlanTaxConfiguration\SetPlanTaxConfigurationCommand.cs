using MediatR;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Application.Commands.SetPlanTaxConfiguration
{
    public class SetPlanTaxConfigurationCommand : IRequest<Guid>
    {
        public Guid PlanId { get; set; }
        public TaxType TaxType { get; set; }
        public decimal Rate { get; set; }
        public bool IsIncluded { get; set; }
        public List<string> ApplicableRegions { get; set; } = new();
        public DateTime EffectiveDate { get; set; }
        public DateTime? ExpirationDate { get; set; }
        public string? Notes { get; set; }
        public Guid CreatedByUserId { get; set; }
    }
}
