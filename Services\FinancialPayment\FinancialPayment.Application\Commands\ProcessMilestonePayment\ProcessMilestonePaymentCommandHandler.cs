using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Messaging;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Application.Interfaces.Repositories;
using FinancialPayment.Domain.Repositories;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Application.Services;
using Shared.Infrastructure.Interfaces;

namespace FinancialPayment.Application.Commands.ProcessMilestonePayment;

public class ProcessMilestonePaymentCommandHandler : IRequestHandler<ProcessMilestonePaymentCommand, ProcessMilestonePaymentResponse>
{
    private readonly IEscrowAccountRepository _escrowAccountRepository;
    private readonly ISettlementRepository _settlementRepository;
    private readonly IEscrowService _escrowService;
    private readonly IUnitOfWork _unitOfWork;
    private readonly Shared.Messaging.IMessageBroker _messageBroker;
    private readonly ILogger<ProcessMilestonePaymentCommandHandler> _logger;

    public ProcessMilestonePaymentCommandHandler(
        IEscrowAccountRepository escrowAccountRepository,
        ISettlementRepository settlementRepository,
        IEscrowService escrowService,
        IUnitOfWork unitOfWork,
        Shared.Messaging.IMessageBroker messageBroker,
        ILogger<ProcessMilestonePaymentCommandHandler> logger)
    {
        _escrowAccountRepository = escrowAccountRepository;
        _settlementRepository = settlementRepository;
        _escrowService = escrowService;
        _unitOfWork = unitOfWork;
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task<ProcessMilestonePaymentResponse> Handle(ProcessMilestonePaymentCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing milestone payment for trip {TripId}, order {OrderId}, milestone type {MilestoneType}",
            request.TripId, request.OrderId, request.MilestoneType);

        try
        {
            var response = new ProcessMilestonePaymentResponse
            {
                ProcessedMilestones = new List<MilestonePaymentResult>()
            };

            // Get escrow account for the order
            var escrowAccount = await _escrowAccountRepository.GetByOrderIdAsync(request.OrderId);
            if (escrowAccount == null)
            {
                _logger.LogWarning("No escrow account found for order {OrderId}", request.OrderId);
                return new ProcessMilestonePaymentResponse
                {
                    Success = false,
                    Message = $"No escrow account found for order {request.OrderId}"
                };
            }

            // Find eligible milestones based on trigger event and milestone type
            var eligibleMilestones = GetEligibleMilestones(escrowAccount, request.MilestoneType, request.TriggerEvent);

            if (!eligibleMilestones.Any())
            {
                _logger.LogInformation("No eligible milestones found for processing");
                return new ProcessMilestonePaymentResponse
                {
                    Success = true,
                    Message = "No eligible milestones found for processing",
                    TotalAmountReleased = 0,
                    Currency = "USD"
                };
            }

            decimal totalReleased = 0;
            var processedMilestones = new List<MilestonePaymentResult>();

            foreach (var milestone in eligibleMilestones)
            {
                try
                {
                    // Validate milestone completion criteria
                    var isEligible = await ValidateMilestoneCompletionCriteriaAsync(
                        milestone, request.TriggerEvent, request.EventData, request.ForceProcess);

                    if (!isEligible && !request.ForceProcess)
                    {
                        processedMilestones.Add(new MilestonePaymentResult
                        {
                            MilestoneId = milestone.Id,
                            Description = milestone.Description,
                            Amount = milestone.Amount.Amount,
                            Currency = milestone.Amount.Currency,
                            PaymentReleased = false,
                            Status = "NotEligible",
                            ErrorMessage = "Milestone completion criteria not met",
                            ProcessedAt = DateTime.UtcNow
                        });
                        continue;
                    }

                    // Complete the milestone
                    escrowAccount.CompleteMilestone(milestone.Id,
                        $"Auto-completed by {request.TriggerEvent} event for trip {request.TripId}");

                    // Release funds
                    var releaseSuccess = await _escrowService.ReleaseEscrowFundsAsync(
                        escrowAccount.Id,
                        milestone.Amount,
                        request.TriggeredBy, // This should be mapped to the actual recipient
                        $"Milestone payment release: {milestone.Description}");

                    if (releaseSuccess)
                    {
                        totalReleased += milestone.Amount.Amount;

                        processedMilestones.Add(new MilestonePaymentResult
                        {
                            MilestoneId = milestone.Id,
                            Description = milestone.Description,
                            Amount = milestone.Amount.Amount,
                            Currency = milestone.Amount.Currency,
                            PaymentReleased = true,
                            Status = "Released",
                            ProcessedAt = DateTime.UtcNow
                        });

                        _logger.LogInformation("Successfully released milestone payment {MilestoneId} for amount {Amount}",
                            milestone.Id, milestone.Amount.Amount);
                    }
                    else
                    {
                        processedMilestones.Add(new MilestonePaymentResult
                        {
                            MilestoneId = milestone.Id,
                            Description = milestone.Description,
                            Amount = milestone.Amount.Amount,
                            Currency = milestone.Amount.Currency,
                            PaymentReleased = false,
                            Status = "ReleaseFailed",
                            ErrorMessage = "Failed to release escrow funds",
                            ProcessedAt = DateTime.UtcNow
                        });
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing milestone {MilestoneId}", milestone.Id);

                    processedMilestones.Add(new MilestonePaymentResult
                    {
                        MilestoneId = milestone.Id,
                        Description = milestone.Description,
                        Amount = milestone.Amount.Amount,
                        Currency = milestone.Amount.Currency,
                        PaymentReleased = false,
                        Status = "Error",
                        ErrorMessage = ex.Message,
                        ProcessedAt = DateTime.UtcNow
                    });
                }
            }

            // Save changes
            await _escrowAccountRepository.UpdateAsync(escrowAccount);
            await _unitOfWork.SaveChangesAsync();

            // Publish integration events
            await _messageBroker.PublishAsync("milestone.payment.processed", new
            {
                TripId = request.TripId,
                OrderId = request.OrderId,
                EscrowAccountId = escrowAccount.Id,
                MilestoneType = request.MilestoneType,
                TriggerEvent = request.TriggerEvent,
                TotalAmountReleased = totalReleased,
                ProcessedMilestones = processedMilestones,
                ProcessedBy = request.TriggeredBy,
                ProcessedAt = DateTime.UtcNow
            });

            var finalResponse = new ProcessMilestonePaymentResponse
            {
                Success = true,
                Message = $"Processed {processedMilestones.Count} milestones, released {totalReleased}",
                ProcessedMilestones = processedMilestones,
                TotalAmountReleased = totalReleased,
                Currency = processedMilestones.FirstOrDefault()?.Currency ?? "USD"
            };

            _logger.LogInformation("Successfully processed milestone payments for trip {TripId}. Total released: {TotalReleased}",
                request.TripId, totalReleased);

            return finalResponse;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing milestone payment for trip {TripId}", request.TripId);
            return new ProcessMilestonePaymentResponse
            {
                Success = false,
                Message = $"Error processing milestone payment: {ex.Message}"
            };
        }
    }

    private List<Domain.Entities.EscrowMilestone> GetEligibleMilestones(
        Domain.Entities.EscrowAccount escrowAccount,
        string milestoneType,
        string triggerEvent)
    {
        return escrowAccount.Milestones
            .Where(m => m.Status == EscrowMilestoneStatus.Pending)
            .Where(m => IsMilestoneEligibleForTrigger(m, milestoneType, triggerEvent))
            .ToList();
    }

    private bool IsMilestoneEligibleForTrigger(
        Domain.Entities.EscrowMilestone milestone,
        string milestoneType,
        string triggerEvent)
    {
        // This logic should be configurable based on business rules
        return triggerEvent.ToLower() switch
        {
            "tripstarted" => milestoneType.ToLower() == "pickup" || milestone.Description.ToLower().Contains("pickup"),
            "tripcompleted" => milestoneType.ToLower() == "delivery" || milestone.Description.ToLower().Contains("delivery"),
            "documentuploaded" => milestoneType.ToLower() == "documentation" || milestone.Description.ToLower().Contains("document"),
            _ => false
        };
    }

    private async Task<bool> ValidateMilestoneCompletionCriteriaAsync(
        Domain.Entities.EscrowMilestone milestone,
        string triggerEvent,
        Dictionary<string, object> eventData,
        bool forceProcess)
    {
        if (forceProcess)
            return true;

        // Add validation logic based on trigger event and milestone requirements
        return triggerEvent.ToLower() switch
        {
            "tripstarted" => eventData.ContainsKey("TripId") && eventData.ContainsKey("StartedAt"),
            "tripcompleted" => eventData.ContainsKey("TripId") && eventData.ContainsKey("CompletedAt"),
            "documentuploaded" => eventData.ContainsKey("DocumentType") && eventData.ContainsKey("DocumentId"),
            _ => false
        };
    }
}
