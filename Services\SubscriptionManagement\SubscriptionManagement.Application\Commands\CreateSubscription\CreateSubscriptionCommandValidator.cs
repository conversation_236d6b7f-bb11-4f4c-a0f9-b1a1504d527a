using FluentValidation;

namespace SubscriptionManagement.Application.Commands.CreateSubscription
{
    public class CreateSubscriptionCommandValidator : AbstractValidator<CreateSubscriptionCommand>
    {
        public CreateSubscriptionCommandValidator()
        {
            RuleFor(x => x.UserId)
                .NotEmpty()
                .WithMessage("User ID is required");

            RuleFor(x => x.PlanId)
                .NotEmpty()
                .WithMessage("Plan ID is required");

            RuleFor(x => x.StartDate)
                .GreaterThanOrEqualTo(DateTime.UtcNow.Date)
                .When(x => x.StartDate.HasValue)
                .WithMessage("Start date cannot be in the past");

            RuleFor(x => x.TrialEndDate)
                .GreaterThan(x => x.StartDate ?? DateTime.UtcNow)
                .When(x => x.TrialEndDate.HasValue)
                .WithMessage("Trial end date must be after start date");

            RuleFor(x => x.ProrationMode)
                .IsInEnum()
                .WithMessage("Invalid proration mode");
        }
    }
}
