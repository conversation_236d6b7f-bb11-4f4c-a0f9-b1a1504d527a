using CommunicationNotification.Application.Commands.Alerts;
using CommunicationNotification.Application.DTOs;
using CommunicationNotification.Application.Services;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CommunicationNotification.API.Controllers;

/// <summary>
/// Controller for Transport Company compliance reminder management
/// </summary>
[ApiController]
[Route("api/transport-company/compliance")]
[Authorize(Roles = "TransportCompany,Admin")]
public class TransportCompanyComplianceController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ITransportCompanyComplianceReminderService _complianceReminderService;
    private readonly ILogger<TransportCompanyComplianceController> _logger;

    public TransportCompanyComplianceController(
        IMediator mediator,
        ITransportCompanyComplianceReminderService complianceReminderService,
        ILogger<TransportCompanyComplianceController> logger)
    {
        _mediator = mediator;
        _complianceReminderService = complianceReminderService;
        _logger = logger;
    }

    /// <summary>
    /// Configure compliance reminders for a transport company
    /// </summary>
    [HttpPost("{transportCompanyId}/reminders/configure")]
    [ProducesResponseType(typeof(ConfigureTransportCompanyComplianceRemindersResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<ConfigureTransportCompanyComplianceRemindersResult>> ConfigureComplianceReminders(
        Guid transportCompanyId,
        [FromBody] ConfigureTransportCompanyComplianceRemindersCommand request)
    {
        try
        {
            // Validate that the requesting user can configure reminders for this transport company
            if (!await CanManageComplianceReminders(transportCompanyId))
            {
                return Forbid("You don't have permission to configure compliance reminders for this transport company");
            }

            request.TransportCompanyId = transportCompanyId;
            var result = await _mediator.Send(request);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Compliance reminders configured successfully for Transport Company {TransportCompanyId}",
                    transportCompanyId);
                return Ok(result);
            }

            _logger.LogWarning("Failed to configure compliance reminders for Transport Company {TransportCompanyId}: {Error}",
                transportCompanyId, result.ErrorMessage);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error configuring compliance reminders for Transport Company {TransportCompanyId}",
                transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get compliance reminder status for a transport company
    /// </summary>
    [HttpGet("{transportCompanyId}/reminders/status")]
    [ProducesResponseType(typeof(ComplianceReminderStatusResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<ComplianceReminderStatusResponse>> GetComplianceReminderStatus(
        Guid transportCompanyId,
        [FromQuery] GetComplianceReminderStatusRequest request)
    {
        try
        {
            if (!await CanViewComplianceReminders(transportCompanyId))
            {
                return Forbid("You don't have permission to view compliance reminders for this transport company");
            }

            request.TransportCompanyId = transportCompanyId;
            
            // This would be implemented as a query handler
            var response = new ComplianceReminderStatusResponse
            {
                TransportCompanyId = transportCompanyId,
                GeneratedAt = DateTime.UtcNow,
                Summary = new ComplianceReminderSummary
                {
                    TotalReminders = 0,
                    PendingAcknowledgments = 0,
                    AcknowledgedReminders = 0,
                    CriticalExpirations = 0,
                    WarningExpirations = 0,
                    NoticeExpirations = 0,
                    ExpiredDocuments = 0
                },
                Reminders = new List<ComplianceReminderInfo>(),
                UpcomingExpirations = new List<ExpiringDocumentInfo>(),
                Pagination = new PaginationInfo
                {
                    PageNumber = request.PageNumber,
                    PageSize = request.PageSize,
                    TotalItems = 0,
                    TotalPages = 0
                }
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting compliance reminder status for Transport Company {TransportCompanyId}",
                transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Process daily compliance reminders for a transport company
    /// </summary>
    [HttpPost("{transportCompanyId}/reminders/process-daily")]
    [ProducesResponseType(typeof(ComplianceReminderProcessingResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [Authorize(Roles = "Admin")] // Only admins can trigger manual processing
    public async Task<ActionResult<ComplianceReminderProcessingResult>> ProcessDailyComplianceReminders(
        Guid transportCompanyId)
    {
        try
        {
            _logger.LogInformation("Processing daily compliance reminders for Transport Company {TransportCompanyId}",
                transportCompanyId);

            var result = await _complianceReminderService.ProcessDailyComplianceRemindersAsync(
                transportCompanyId, HttpContext.RequestAborted);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Successfully processed {Count} compliance reminders for Transport Company {TransportCompanyId}",
                    result.TotalRemindersProcessed, transportCompanyId);
                return Ok(result);
            }

            _logger.LogWarning("Failed to process compliance reminders for Transport Company {TransportCompanyId}: {Error}",
                transportCompanyId, result.ErrorMessage);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing daily compliance reminders for Transport Company {TransportCompanyId}",
                transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Acknowledge compliance reminders
    /// </summary>
    [HttpPost("{transportCompanyId}/reminders/acknowledge")]
    [ProducesResponseType(typeof(AcknowledgeComplianceRemindersResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<AcknowledgeComplianceRemindersResponse>> AcknowledgeComplianceReminders(
        Guid transportCompanyId,
        [FromBody] AcknowledgeComplianceRemindersRequest request)
    {
        try
        {
            if (!await CanManageComplianceReminders(transportCompanyId))
            {
                return Forbid("You don't have permission to acknowledge compliance reminders for this transport company");
            }

            request.TransportCompanyId = transportCompanyId;
            request.AcknowledgedByUserId = GetCurrentUserId();

            // This would be implemented as a command handler
            var response = new AcknowledgeComplianceRemindersResponse
            {
                IsSuccess = true,
                AcknowledgedCount = request.AlertIds.Count,
                FailedCount = 0,
                ProcessedAt = DateTime.UtcNow
            };

            _logger.LogInformation("Acknowledged {Count} compliance reminders for Transport Company {TransportCompanyId}",
                response.AcknowledgedCount, transportCompanyId);

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error acknowledging compliance reminders for Transport Company {TransportCompanyId}",
                transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Update compliance reminder configuration
    /// </summary>
    [HttpPut("{transportCompanyId}/reminders/configuration")]
    [ProducesResponseType(typeof(UpdateComplianceReminderConfigurationResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<UpdateComplianceReminderConfigurationResponse>> UpdateComplianceReminderConfiguration(
        Guid transportCompanyId,
        [FromBody] UpdateComplianceReminderConfigurationRequest request)
    {
        try
        {
            if (!await CanManageComplianceReminders(transportCompanyId))
            {
                return Forbid("You don't have permission to update compliance reminder configuration for this transport company");
            }

            request.TransportCompanyId = transportCompanyId;

            // This would be implemented as a command handler
            var response = new UpdateComplianceReminderConfigurationResponse
            {
                IsSuccess = true,
                ConfigurationId = Guid.NewGuid(),
                UpdatedAt = DateTime.UtcNow,
                UpdatedJobIds = new List<Guid>(),
                ConfigurationSummary = new Dictionary<string, object>
                {
                    ["TotalConfigurations"] = request.ReminderConfigurations.Count,
                    ["ActiveConfigurations"] = request.ReminderConfigurations.Count(c => c.IsEnabled),
                    ["PreferredChannels"] = request.PreferredChannels,
                    ["MaxRemindersPerDay"] = request.MaxRemindersPerDay
                }
            };

            _logger.LogInformation("Updated compliance reminder configuration for Transport Company {TransportCompanyId}",
                transportCompanyId);

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating compliance reminder configuration for Transport Company {TransportCompanyId}",
                transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get expiring documents for a transport company
    /// </summary>
    [HttpGet("{transportCompanyId}/expiring-documents")]
    [ProducesResponseType(typeof(List<ExpiringDocumentInfo>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<List<ExpiringDocumentInfo>>> GetExpiringDocuments(
        Guid transportCompanyId,
        [FromQuery] List<int> reminderDays)
    {
        try
        {
            if (!await CanViewComplianceReminders(transportCompanyId))
            {
                return Forbid("You don't have permission to view expiring documents for this transport company");
            }

            if (!reminderDays.Any())
            {
                reminderDays = new List<int> { 30, 15, 7, 3, 1, 0 };
            }

            var expiringDocuments = await _complianceReminderService.GetExpiringDocumentsAsync(
                transportCompanyId, reminderDays, HttpContext.RequestAborted);

            return Ok(expiringDocuments);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting expiring documents for Transport Company {TransportCompanyId}",
                transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    private async Task<bool> CanManageComplianceReminders(Guid transportCompanyId)
    {
        // Implement authorization logic
        // Check if current user is admin or belongs to the transport company
        var currentUserId = GetCurrentUserId();
        var userRoles = GetCurrentUserRoles();

        return userRoles.Contains("Admin") || 
               (userRoles.Contains("TransportCompany") && await IsUserFromTransportCompany(currentUserId, transportCompanyId));
    }

    private async Task<bool> CanViewComplianceReminders(Guid transportCompanyId)
    {
        return await CanManageComplianceReminders(transportCompanyId);
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst("sub") ?? User.FindFirst("userId");
        return userIdClaim != null && Guid.TryParse(userIdClaim.Value, out var userId) ? userId : Guid.Empty;
    }

    private List<string> GetCurrentUserRoles()
    {
        return User.FindAll("role").Select(c => c.Value).ToList();
    }

    private async Task<bool> IsUserFromTransportCompany(Guid userId, Guid transportCompanyId)
    {
        // This would typically check the user's company association
        // For now, return true as a placeholder
        return await Task.FromResult(true);
    }
}
