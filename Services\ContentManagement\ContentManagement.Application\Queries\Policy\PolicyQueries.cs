using ContentManagement.Application.DTOs;
using ContentManagement.Domain.Enums;
using MediatR;

namespace ContentManagement.Application.Queries.Policy;

/// <summary>
/// Query to get policy document by ID
/// </summary>
public class GetPolicyByIdQuery : IRequest<PolicyDocumentDto?>
{
    public Guid Id { get; set; }
    public bool IncludeAcceptances { get; set; } = false;
    
    // User context for visibility checks
    public List<string> UserRoles { get; set; } = new();
    public bool IsAuthenticated { get; set; } = false;
}

/// <summary>
/// Query to get policy document by type
/// </summary>
public class GetPolicyByTypeQuery : IRequest<PolicyDocumentDto?>
{
    public PolicyType PolicyType { get; set; }
    public bool CurrentVersionOnly { get; set; } = true;
    public bool IncludeAcceptances { get; set; } = false;
    
    // User context for visibility checks
    public List<string> UserRoles { get; set; } = new();
    public bool IsAuthenticated { get; set; } = false;
}

/// <summary>
/// Query to search policy documents with filters
/// </summary>
public class SearchPoliciesQuery : IRequest<PolicySearchResultDto>
{
    public string? SearchTerm { get; set; }
    public PolicyType? PolicyType { get; set; }
    public ContentStatus? Status { get; set; }
    public bool? RequiresAcceptance { get; set; }
    public bool? IsCurrentVersion { get; set; }
    public DateTime? EffectiveFrom { get; set; }
    public DateTime? EffectiveTo { get; set; }
    public string? SortBy { get; set; } = "EffectiveDate";
    public bool SortDescending { get; set; } = true;
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 20;
    
    // User context for visibility checks
    public List<string> UserRoles { get; set; } = new();
    public bool IsAuthenticated { get; set; } = false;
}

/// <summary>
/// Query to get policies applicable to user roles
/// </summary>
public class GetApplicablePoliciesQuery : IRequest<List<PolicySummaryDto>>
{
    public List<string> UserRoles { get; set; } = new();
    public bool RequiresAcceptanceOnly { get; set; } = false;
    public bool CurrentVersionOnly { get; set; } = true;
    public bool EffectiveOnly { get; set; } = true;
}

/// <summary>
/// Query to get user policy status
/// </summary>
public class GetUserPolicyStatusQuery : IRequest<UserPolicyStatusDto>
{
    public Guid UserId { get; set; }
    public string UserName { get; set; } = string.Empty;
    public List<string> UserRoles { get; set; } = new();
    public bool RequiresAcceptanceOnly { get; set; } = false;
}

/// <summary>
/// Query to get policy compliance report
/// </summary>
public class GetPolicyComplianceReportQuery : IRequest<PolicyComplianceReportDto>
{
    public Guid PolicyId { get; set; }
    public List<string>? FilterByRoles { get; set; }
    public bool IncludeRecentAcceptances { get; set; } = true;
    public int RecentAcceptancesLimit { get; set; } = 10;
}

/// <summary>
/// Query to get policies requiring acceptance
/// </summary>
public class GetPoliciesRequiringAcceptanceQuery : IRequest<List<PolicySummaryDto>>
{
    public List<string> UserRoles { get; set; } = new();
    public bool CurrentVersionOnly { get; set; } = true;
    public bool EffectiveOnly { get; set; } = true;
}

/// <summary>
/// Query to get policy versions
/// </summary>
public class GetPolicyVersionsQuery : IRequest<List<PolicySummaryDto>>
{
    public PolicyType PolicyType { get; set; }
    public bool IncludeObsolete { get; set; } = false;
}

/// <summary>
/// Query to get policy acceptance history
/// </summary>
public class GetPolicyAcceptanceHistoryQuery : IRequest<List<PolicyAcceptanceDto>>
{
    public Guid? PolicyId { get; set; }
    public Guid? UserId { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public string? SortBy { get; set; } = "AcceptedAt";
    public bool SortDescending { get; set; } = true;
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 50;
}

/// <summary>
/// Query to get policy analytics
/// </summary>
public class GetPolicyAnalyticsQuery : IRequest<PolicyAnalyticsDto>
{
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public PolicyType? PolicyType { get; set; }
}

/// <summary>
/// Policy analytics DTO
/// </summary>
public class PolicyAnalyticsDto
{
    public int TotalPolicies { get; set; }
    public int CurrentVersionPolicies { get; set; }
    public int PoliciesRequiringAcceptance { get; set; }
    public int TotalAcceptances { get; set; }
    public decimal OverallComplianceRate { get; set; }
    public Dictionary<PolicyType, int> PoliciesByType { get; set; } = new();
    public Dictionary<PolicyType, decimal> ComplianceByType { get; set; } = new();
    public List<PolicyComplianceReportDto> LowCompliancePolicies { get; set; } = new();
    public DateTime? LastUpdated { get; set; }
}
