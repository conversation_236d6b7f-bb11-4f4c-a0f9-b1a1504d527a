using AnalyticsBIService.Domain.Enums;
using MediatR;

namespace AnalyticsBIService.Application.Commands.TrackEvent;

/// <summary>
/// Command to track an analytics event
/// </summary>
public record TrackEventCommand(
    string EventName,
    AnalyticsEventType EventType,
    DataSourceType DataSource,
    Guid? UserId = null,
    UserType? UserType = null,
    Guid? EntityId = null,
    string? EntityType = null,
    Dictionary<string, object>? Properties = null,
    Dictionary<string, object>? Metadata = null,
    string? SessionId = null,
    string? IpAddress = null,
    string? UserAgent = null
) : IRequest<Guid>;

/// <summary>
/// Command to track user activity
/// </summary>
public record TrackUserActivityCommand(
    string EventName,
    Guid UserId,
    UserType UserType,
    Dictionary<string, object>? Properties = null,
    string? SessionId = null,
    string? IpAddress = null,
    string? UserAgent = null
) : IRequest<Guid>;

/// <summary>
/// Command to track business transaction
/// </summary>
public record TrackBusinessTransactionCommand(
    string EventName,
    DataSourceType DataSource,
    Guid EntityId,
    string EntityType,
    Dictionary<string, object>? Properties = null,
    Guid? UserId = null,
    UserType? UserType = null
) : IRequest<Guid>;

/// <summary>
/// Command to track system performance event
/// </summary>
public record TrackSystemPerformanceCommand(
    string EventName,
    DataSourceType DataSource,
    Dictionary<string, object>? Properties = null,
    Dictionary<string, object>? Metadata = null
) : IRequest<Guid>;

/// <summary>
/// Batch command to track multiple events
/// </summary>
public record TrackEventsBatchCommand(
    List<TrackEventCommand> Events
) : IRequest<List<Guid>>;

/// <summary>
/// Command to calculate and store a metric
/// </summary>
public record CalculateMetricCommand(
    string MetricName,
    string Description,
    MetricType Type,
    KPICategory Category,
    decimal Value,
    string Unit,
    DateTime? Timestamp = null,
    DateTime? PeriodStart = null,
    DateTime? PeriodEnd = null,
    TimePeriod Period = TimePeriod.Daily,
    DataSourceType DataSource = DataSourceType.External,
    Guid? UserId = null,
    UserType? UserType = null,
    Dictionary<string, object>? Metadata = null,
    Dictionary<string, string>? Tags = null,
    decimal? TargetValue = null,
    string? TargetUnit = null,
    bool? IsHigherBetter = null,
    decimal? WarningThreshold = null,
    decimal? CriticalThreshold = null
) : IRequest<Guid>;

/// <summary>
/// Command to update metric value
/// </summary>
public record UpdateMetricCommand(
    Guid MetricId,
    decimal NewValue,
    DateTime? Timestamp = null,
    Dictionary<string, object>? Metadata = null
) : IRequest<bool>;

/// <summary>
/// Command to set metric target
/// </summary>
public record SetMetricTargetCommand(
    Guid MetricId,
    decimal TargetValue,
    string Unit,
    bool IsHigherBetter = true,
    decimal? WarningThreshold = null,
    decimal? CriticalThreshold = null
) : IRequest<bool>;

/// <summary>
/// Command to process real-time analytics
/// </summary>
public record ProcessRealTimeAnalyticsCommand(
    List<string> MetricNames,
    DateTime? FromTime = null,
    DateTime? ToTime = null
) : IRequest<Dictionary<string, object>>;

/// <summary>
/// Command to aggregate data for a specific period
/// </summary>
public record AggregateDataCommand(
    TimePeriod Period,
    DateTime PeriodStart,
    DateTime PeriodEnd,
    DataSourceType? DataSource = null,
    List<string>? MetricNames = null
) : IRequest<bool>;
