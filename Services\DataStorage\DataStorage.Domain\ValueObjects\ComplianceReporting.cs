using DataStorage.Domain.Enums;

namespace DataStorage.Domain.ValueObjects;

public class ComplianceAlert
{
    public Guid AlertId { get; private set; }
    public ComplianceAlertType Type { get; private set; }
    public ComplianceAlertSeverity Severity { get; private set; }
    public string Title { get; private set; }
    public string Description { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? AcknowledgedAt { get; private set; }
    public Guid? AcknowledgedBy { get; private set; }
    public string? AcknowledgmentNotes { get; private set; }
    public ComplianceAlertStatus Status { get; private set; }
    public List<Guid> AffectedDocuments { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    public ComplianceAlert(
        Guid alertId,
        ComplianceAlertType type,
        ComplianceAlertSeverity severity,
        string title,
        string description,
        DateTime createdAt,
        ComplianceAlertStatus status,
        List<Guid>? affectedDocuments = null,
        DateTime? acknowledgedAt = null,
        Guid? acknowledgedBy = null,
        string? acknowledgmentNotes = null,
        Dictionary<string, object>? metadata = null)
    {
        AlertId = alertId;
        Type = type;
        Severity = severity;
        Title = title ?? throw new ArgumentNullException(nameof(title));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        CreatedAt = createdAt;
        AcknowledgedAt = acknowledgedAt;
        AcknowledgedBy = acknowledgedBy;
        AcknowledgmentNotes = acknowledgmentNotes;
        Status = status;
        AffectedDocuments = affectedDocuments ?? new List<Guid>();
        Metadata = metadata ?? new Dictionary<string, object>();
    }
}

public class AutomatedComplianceResult
{
    public bool IsSuccessful { get; private set; }
    public string? ErrorMessage { get; private set; }
    public DateTime ExecutedAt { get; private set; }
    public TimeSpan ExecutionTime { get; private set; }
    public int DocumentsProcessed { get; private set; }
    public int ViolationsDetected { get; private set; }
    public int ActionsExecuted { get; private set; }
    public List<ComplianceAction> Actions { get; private set; }
    public List<ComplianceAlert> GeneratedAlerts { get; private set; }

    private AutomatedComplianceResult()
    {
        Actions = new List<ComplianceAction>();
        GeneratedAlerts = new List<ComplianceAlert>();
    }

    public static AutomatedComplianceResult Success(
        TimeSpan executionTime,
        int documentsProcessed,
        int violationsDetected,
        int actionsExecuted,
        List<ComplianceAction> actions,
        List<ComplianceAlert> generatedAlerts)
    {
        return new AutomatedComplianceResult
        {
            IsSuccessful = true,
            ExecutedAt = DateTime.UtcNow,
            ExecutionTime = executionTime,
            DocumentsProcessed = documentsProcessed,
            ViolationsDetected = violationsDetected,
            ActionsExecuted = actionsExecuted,
            Actions = actions ?? new List<ComplianceAction>(),
            GeneratedAlerts = generatedAlerts ?? new List<ComplianceAlert>()
        };
    }

    public static AutomatedComplianceResult Failure(string errorMessage)
    {
        return new AutomatedComplianceResult
        {
            IsSuccessful = false,
            ErrorMessage = errorMessage,
            ExecutedAt = DateTime.UtcNow,
            Actions = new List<ComplianceAction>(),
            GeneratedAlerts = new List<ComplianceAlert>()
        };
    }
}

public class ComplianceAction
{
    public Guid ActionId { get; private set; }
    public ComplianceActionType Type { get; private set; }
    public Guid DocumentId { get; private set; }
    public string Description { get; private set; }
    public DateTime ExecutedAt { get; private set; }
    public Guid ExecutedBy { get; private set; }
    public ComplianceActionStatus Status { get; private set; }
    public string? Result { get; private set; }
    public Dictionary<string, object> Parameters { get; private set; }

    public ComplianceAction(
        Guid actionId,
        ComplianceActionType type,
        Guid documentId,
        string description,
        DateTime executedAt,
        Guid executedBy,
        ComplianceActionStatus status,
        string? result = null,
        Dictionary<string, object>? parameters = null)
    {
        ActionId = actionId;
        Type = type;
        DocumentId = documentId;
        Description = description ?? throw new ArgumentNullException(nameof(description));
        ExecutedAt = executedAt;
        ExecutedBy = executedBy;
        Status = status;
        Result = result;
        Parameters = parameters ?? new Dictionary<string, object>();
    }
}

public class ComplianceSchedule
{
    public Guid Id { get; private set; }
    public string Name { get; private set; }
    public ComplianceCheckType CheckType { get; private set; }
    public string CronExpression { get; private set; }
    public DateTime NextRunTime { get; private set; }
    public DateTime? LastRunTime { get; private set; }
    public bool IsEnabled { get; private set; }
    public Guid CreatedBy { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public ComplianceScheduleStatistics Statistics { get; private set; }

    public ComplianceSchedule(
        Guid id,
        string name,
        ComplianceCheckType checkType,
        string cronExpression,
        DateTime nextRunTime,
        bool isEnabled,
        Guid createdBy,
        DateTime createdAt,
        DateTime? lastRunTime = null,
        ComplianceScheduleStatistics? statistics = null)
    {
        Id = id;
        Name = name ?? throw new ArgumentNullException(nameof(name));
        CheckType = checkType;
        CronExpression = cronExpression ?? throw new ArgumentNullException(nameof(cronExpression));
        NextRunTime = nextRunTime;
        LastRunTime = lastRunTime;
        IsEnabled = isEnabled;
        CreatedBy = createdBy;
        CreatedAt = createdAt;
        Statistics = statistics ?? new ComplianceScheduleStatistics();
    }
}

public class ComplianceScheduleRequest
{
    public string Name { get; private set; }
    public ComplianceCheckType CheckType { get; private set; }
    public string CronExpression { get; private set; }
    public DateTime StartTime { get; private set; }
    public bool IsEnabled { get; private set; }
    public Guid CreatedBy { get; private set; }
    public List<ComplianceStandard> Standards { get; private set; }

    public ComplianceScheduleRequest(
        string name,
        ComplianceCheckType checkType,
        string cronExpression,
        DateTime startTime,
        Guid createdBy,
        bool isEnabled = true,
        List<ComplianceStandard>? standards = null)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        CheckType = checkType;
        CronExpression = cronExpression ?? throw new ArgumentNullException(nameof(cronExpression));
        StartTime = startTime;
        CreatedBy = createdBy;
        IsEnabled = isEnabled;
        Standards = standards ?? new List<ComplianceStandard>();
    }
}

public class ComplianceScheduleStatistics
{
    public int TotalRuns { get; private set; }
    public int SuccessfulRuns { get; private set; }
    public int FailedRuns { get; private set; }
    public TimeSpan AverageRunTime { get; private set; }
    public int TotalViolationsDetected { get; private set; }
    public int TotalActionsExecuted { get; private set; }

    public ComplianceScheduleStatistics(
        int totalRuns = 0,
        int successfulRuns = 0,
        int failedRuns = 0,
        TimeSpan averageRunTime = default,
        int totalViolationsDetected = 0,
        int totalActionsExecuted = 0)
    {
        TotalRuns = totalRuns;
        SuccessfulRuns = successfulRuns;
        FailedRuns = failedRuns;
        AverageRunTime = averageRunTime;
        TotalViolationsDetected = totalViolationsDetected;
        TotalActionsExecuted = totalActionsExecuted;
    }
}

public class ComplianceJob
{
    public Guid JobId { get; private set; }
    public ComplianceJobType Type { get; private set; }
    public ComplianceJobStatus Status { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? StartedAt { get; private set; }
    public DateTime? CompletedAt { get; private set; }
    public double ProgressPercentage { get; private set; }
    public string? ErrorMessage { get; private set; }
    public Guid CreatedBy { get; private set; }
    public Dictionary<string, object> Parameters { get; private set; }
    public ComplianceJobResult? Result { get; private set; }

    public ComplianceJob(
        Guid jobId,
        ComplianceJobType type,
        ComplianceJobStatus status,
        DateTime createdAt,
        Guid createdBy,
        Dictionary<string, object> parameters,
        DateTime? startedAt = null,
        DateTime? completedAt = null,
        double progressPercentage = 0,
        string? errorMessage = null,
        ComplianceJobResult? result = null)
    {
        JobId = jobId;
        Type = type;
        Status = status;
        CreatedAt = createdAt;
        StartedAt = startedAt;
        CompletedAt = completedAt;
        ProgressPercentage = Math.Max(0, Math.Min(100, progressPercentage));
        ErrorMessage = errorMessage;
        CreatedBy = createdBy;
        Parameters = parameters ?? new Dictionary<string, object>();
        Result = result;
    }
}

public class ComplianceJobResult
{
    public int DocumentsProcessed { get; private set; }
    public int ViolationsFound { get; private set; }
    public int ActionsExecuted { get; private set; }
    public List<ComplianceAlert> AlertsGenerated { get; private set; }
    public Dictionary<string, object> Summary { get; private set; }

    public ComplianceJobResult(
        int documentsProcessed,
        int violationsFound,
        int actionsExecuted,
        List<ComplianceAlert> alertsGenerated,
        Dictionary<string, object> summary)
    {
        DocumentsProcessed = documentsProcessed;
        ViolationsFound = violationsFound;
        ActionsExecuted = actionsExecuted;
        AlertsGenerated = alertsGenerated ?? new List<ComplianceAlert>();
        Summary = summary ?? new Dictionary<string, object>();
    }
}

public class RetentionViolationFilter
{
    public List<RetentionViolationType> Types { get; private set; }
    public List<ViolationSeverity> Severities { get; private set; }
    public List<ViolationStatus> Statuses { get; private set; }
    public DateTime? StartDate { get; private set; }
    public DateTime? EndDate { get; private set; }
    public List<DocumentCategory> Categories { get; private set; }

    public RetentionViolationFilter(
        List<RetentionViolationType>? types = null,
        List<ViolationSeverity>? severities = null,
        List<ViolationStatus>? statuses = null,
        DateTime? startDate = null,
        DateTime? endDate = null,
        List<DocumentCategory>? categories = null)
    {
        Types = types ?? new List<RetentionViolationType>();
        Severities = severities ?? new List<ViolationSeverity>();
        Statuses = statuses ?? new List<ViolationStatus>();
        StartDate = startDate;
        EndDate = endDate;
        Categories = categories ?? new List<DocumentCategory>();
    }
}

public class RetentionComplianceReport
{
    public DateTime GeneratedAt { get; private set; }
    public RetentionReportRequest Request { get; private set; }
    public RetentionComplianceSummary Summary { get; private set; }
    public List<RetentionViolation> Violations { get; private set; }
    public List<RetentionPolicyEffectiveness> PolicyEffectiveness { get; private set; }
    public List<RetentionTrend> Trends { get; private set; }

    public RetentionComplianceReport(
        RetentionReportRequest request,
        RetentionComplianceSummary summary,
        List<RetentionViolation> violations,
        List<RetentionPolicyEffectiveness> policyEffectiveness,
        List<RetentionTrend> trends)
    {
        GeneratedAt = DateTime.UtcNow;
        Request = request ?? throw new ArgumentNullException(nameof(request));
        Summary = summary ?? throw new ArgumentNullException(nameof(summary));
        Violations = violations ?? new List<RetentionViolation>();
        PolicyEffectiveness = policyEffectiveness ?? new List<RetentionPolicyEffectiveness>();
        Trends = trends ?? new List<RetentionTrend>();
    }
}

public class RetentionReportRequest
{
    public DateTime StartDate { get; private set; }
    public DateTime EndDate { get; private set; }
    public List<ComplianceStandard> Standards { get; private set; }
    public List<DocumentCategory> Categories { get; private set; }
    public Guid RequestedBy { get; private set; }
    public bool IncludeTrends { get; private set; }
    public bool IncludePolicyEffectiveness { get; private set; }

    public RetentionReportRequest(
        DateTime startDate,
        DateTime endDate,
        Guid requestedBy,
        List<ComplianceStandard>? standards = null,
        List<DocumentCategory>? categories = null,
        bool includeTrends = true,
        bool includePolicyEffectiveness = true)
    {
        StartDate = startDate;
        EndDate = endDate;
        RequestedBy = requestedBy;
        Standards = standards ?? new List<ComplianceStandard>();
        Categories = categories ?? new List<DocumentCategory>();
        IncludeTrends = includeTrends;
        IncludePolicyEffectiveness = includePolicyEffectiveness;
    }
}

public class RetentionComplianceSummary
{
    public int TotalDocuments { get; private set; }
    public int DocumentsWithPolicies { get; private set; }
    public int DocumentsUnderLegalHold { get; private set; }
    public int TotalViolations { get; private set; }
    public double CompliancePercentage { get; private set; }
    public Dictionary<ComplianceStandard, double> ComplianceByStandard { get; private set; }

    public RetentionComplianceSummary(
        int totalDocuments,
        int documentsWithPolicies,
        int documentsUnderLegalHold,
        int totalViolations,
        double compliancePercentage,
        Dictionary<ComplianceStandard, double> complianceByStandard)
    {
        TotalDocuments = totalDocuments;
        DocumentsWithPolicies = documentsWithPolicies;
        DocumentsUnderLegalHold = documentsUnderLegalHold;
        TotalViolations = totalViolations;
        CompliancePercentage = Math.Max(0, Math.Min(100, compliancePercentage));
        ComplianceByStandard = complianceByStandard ?? new Dictionary<ComplianceStandard, double>();
    }
}

public class RetentionPolicyEffectiveness
{
    public Guid PolicyId { get; private set; }
    public string PolicyName { get; private set; }
    public int DocumentsAssigned { get; private set; }
    public int DocumentsCompliant { get; private set; }
    public int ViolationsDetected { get; private set; }
    public double EffectivenessScore { get; private set; }
    public List<string> IssuesIdentified { get; private set; }

    public RetentionPolicyEffectiveness(
        Guid policyId,
        string policyName,
        int documentsAssigned,
        int documentsCompliant,
        int violationsDetected,
        double effectivenessScore,
        List<string> issuesIdentified)
    {
        PolicyId = policyId;
        PolicyName = policyName ?? throw new ArgumentNullException(nameof(policyName));
        DocumentsAssigned = documentsAssigned;
        DocumentsCompliant = documentsCompliant;
        ViolationsDetected = violationsDetected;
        EffectivenessScore = Math.Max(0, Math.Min(100, effectivenessScore));
        IssuesIdentified = issuesIdentified ?? new List<string>();
    }
}

public class RetentionTrend
{
    public DateTime Date { get; private set; }
    public int DocumentsRetained { get; private set; }
    public int DocumentsDeleted { get; private set; }
    public int ViolationsDetected { get; private set; }
    public double ComplianceScore { get; private set; }

    public RetentionTrend(
        DateTime date,
        int documentsRetained,
        int documentsDeleted,
        int violationsDetected,
        double complianceScore)
    {
        Date = date;
        DocumentsRetained = documentsRetained;
        DocumentsDeleted = documentsDeleted;
        ViolationsDetected = violationsDetected;
        ComplianceScore = Math.Max(0, Math.Min(100, complianceScore));
    }
}
