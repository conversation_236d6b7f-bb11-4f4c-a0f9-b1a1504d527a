using UserManagement.Application.Commands.TransportCompanyOCR;
using UserManagement.Application.DTOs;
using UserManagement.Domain.Entities;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace UserManagement.API.Controllers;

/// <summary>
/// Controller for Transport Company OCR document processing
/// </summary>
[ApiController]
[Route("api/transport-company/ocr")]
[Authorize(Roles = "TransportCompany,Admin")]
public class TransportCompanyOCRController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<TransportCompanyOCRController> _logger;

    public TransportCompanyOCRController(
        IMediator mediator,
        ILogger<TransportCompanyOCRController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Process document with OCR preview and validation
    /// </summary>
    [HttpPost("{transportCompanyId}/process-document")]
    [ProducesResponseType(typeof(ProcessTransportCompanyDocumentWithOCRResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [RequestSizeLimit(10 * 1024 * 1024)] // 10MB limit
    public async Task<ActionResult<ProcessTransportCompanyDocumentWithOCRResult>> ProcessDocumentWithOCR(
        Guid transportCompanyId,
        [FromForm] ProcessDocumentWithOCRRequest request)
    {
        try
        {
            if (!await CanManageDocuments(transportCompanyId))
            {
                return Forbid("You don't have permission to process documents for this transport company");
            }

            if (request.File == null || request.File.Length == 0)
            {
                return BadRequest("File is required");
            }

            // Read file content
            byte[] fileContent;
            using (var memoryStream = new MemoryStream())
            {
                await request.File.CopyToAsync(memoryStream);
                fileContent = memoryStream.ToArray();
            }

            var command = new ProcessTransportCompanyDocumentWithOCRCommand
            {
                TransportCompanyId = transportCompanyId,
                DocumentType = request.DocumentType,
                FileName = request.File.FileName,
                FileContent = fileContent,
                ContentType = request.File.ContentType,
                FileSize = request.File.Length,
                IsPreviewMode = request.IsPreviewMode,
                AutoValidate = request.AutoValidate,
                AutoCorrect = request.AutoCorrect,
                MinimumConfidenceThreshold = request.MinimumConfidenceThreshold,
                ExistingData = request.ExistingData,
                OverwriteExistingData = request.OverwriteExistingData,
                RequiredFields = request.RequiredFields,
                ValidationRules = request.ValidationRules,
                ProcessedByUserId = GetCurrentUserId()
            };

            var result = await _mediator.Send(command);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Document processed successfully with OCR for Transport Company {TransportCompanyId}. Confidence: {Confidence}%",
                    transportCompanyId, result.ExtractionResult.OverallConfidence * 100);
                return Ok(result);
            }

            _logger.LogWarning("Failed to process document with OCR for Transport Company {TransportCompanyId}: {Error}",
                transportCompanyId, result.ErrorMessage);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing document with OCR for Transport Company {TransportCompanyId}",
                transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Confirm OCR processed document
    /// </summary>
    [HttpPost("{transportCompanyId}/confirm-document")]
    [ProducesResponseType(typeof(ConfirmOCRProcessedDocumentResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<ConfirmOCRProcessedDocumentResult>> ConfirmOCRProcessedDocument(
        Guid transportCompanyId,
        [FromBody] ConfirmOCRProcessedDocumentCommand command)
    {
        try
        {
            if (!await CanManageDocuments(transportCompanyId))
            {
                return Forbid("You don't have permission to confirm documents for this transport company");
            }

            command.TransportCompanyId = transportCompanyId;
            command.ConfirmedByUserId = GetCurrentUserId();

            var result = await _mediator.Send(command);

            if (result.IsSuccess)
            {
                _logger.LogInformation("OCR processed document confirmed successfully for Transport Company {TransportCompanyId}. DocumentId: {DocumentId}",
                    transportCompanyId, result.DocumentId);
                return Ok(result);
            }

            _logger.LogWarning("Failed to confirm OCR processed document for Transport Company {TransportCompanyId}: {Error}",
                transportCompanyId, result.ErrorMessage);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error confirming OCR processed document for Transport Company {TransportCompanyId}",
                transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get OCR processing status
    /// </summary>
    [HttpGet("{transportCompanyId}/processing-status/{previewToken}")]
    [ProducesResponseType(typeof(OCRProcessingStatusDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<OCRProcessingStatusDto>> GetOCRProcessingStatus(
        Guid transportCompanyId,
        string previewToken)
    {
        try
        {
            if (!await CanViewDocuments(transportCompanyId))
            {
                return Forbid("You don't have permission to view processing status for this transport company");
            }

            // This would retrieve the processing status from cache/storage
            var status = new OCRProcessingStatusDto
            {
                PreviewToken = previewToken,
                Status = "Completed",
                ProcessedAt = DateTime.UtcNow,
                ExpiresAt = DateTime.UtcNow.AddHours(24),
                IsExpired = false,
                CanConfirm = true
            };

            return Ok(status);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting OCR processing status for Transport Company {TransportCompanyId}",
                transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get supported document types for OCR processing
    /// </summary>
    [HttpGet("supported-document-types")]
    [ProducesResponseType(typeof(List<SupportedDocumentTypeDto>), StatusCodes.Status200OK)]
    public async Task<ActionResult<List<SupportedDocumentTypeDto>>> GetSupportedDocumentTypes()
    {
        try
        {
            var supportedTypes = new List<SupportedDocumentTypeDto>
            {
                new SupportedDocumentTypeDto
                {
                    DocumentType = DocumentType.GstCertificate,
                    DisplayName = "GST Certificate",
                    Description = "Goods and Services Tax Registration Certificate",
                    SupportedFormats = new List<string> { "PDF", "JPEG", "PNG", "TIFF" },
                    MaxFileSize = 10 * 1024 * 1024, // 10MB
                    ExpectedFields = new List<string> { "GST Number", "Legal Name", "Trade Name", "Address", "State Code", "Registration Date" },
                    ValidationRules = new List<string> { "GST Number must be 15 characters", "Must be a valid GST format" }
                },
                new SupportedDocumentTypeDto
                {
                    DocumentType = DocumentType.PanCard,
                    DisplayName = "PAN Card",
                    Description = "Permanent Account Number Card",
                    SupportedFormats = new List<string> { "JPEG", "PNG", "TIFF" },
                    MaxFileSize = 5 * 1024 * 1024, // 5MB
                    ExpectedFields = new List<string> { "PAN Number", "Name", "Father's Name", "Date of Birth" },
                    ValidationRules = new List<string> { "PAN Number must be 10 characters", "Must follow ********** format" }
                },
                new SupportedDocumentTypeDto
                {
                    DocumentType = DocumentType.DrivingLicense,
                    DisplayName = "Driving License",
                    Description = "Driving License Document",
                    SupportedFormats = new List<string> { "JPEG", "PNG", "TIFF" },
                    MaxFileSize = 5 * 1024 * 1024, // 5MB
                    ExpectedFields = new List<string> { "License Number", "Name", "Address", "Date of Birth", "Issue Date", "Expiry Date", "Vehicle Class" },
                    ValidationRules = new List<string> { "License must not be expired", "Must be a valid state format" }
                },
                new SupportedDocumentTypeDto
                {
                    DocumentType = DocumentType.VehicleRegistration,
                    DisplayName = "Vehicle Registration",
                    Description = "Vehicle Registration Certificate",
                    SupportedFormats = new List<string> { "PDF", "JPEG", "PNG", "TIFF" },
                    MaxFileSize = 10 * 1024 * 1024, // 10MB
                    ExpectedFields = new List<string> { "Registration Number", "Owner Name", "Vehicle Class", "Engine Number", "Chassis Number", "Registration Date" },
                    ValidationRules = new List<string> { "Registration number must be valid", "Must not be expired" }
                },
                new SupportedDocumentTypeDto
                {
                    DocumentType = DocumentType.VehicleInsurance,
                    DisplayName = "Vehicle Insurance",
                    Description = "Vehicle Insurance Policy",
                    SupportedFormats = new List<string> { "PDF", "JPEG", "PNG", "TIFF" },
                    MaxFileSize = 10 * 1024 * 1024, // 10MB
                    ExpectedFields = new List<string> { "Policy Number", "Vehicle Number", "Insured Name", "Policy Start Date", "Policy End Date", "Insurance Company" },
                    ValidationRules = new List<string> { "Policy must be active", "Vehicle number must match registration" }
                }
            };

            return Ok(supportedTypes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting supported document types");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get OCR processing analytics for a transport company
    /// </summary>
    [HttpGet("{transportCompanyId}/analytics")]
    [ProducesResponseType(typeof(OCRAnalyticsDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<OCRAnalyticsDto>> GetOCRAnalytics(
        Guid transportCompanyId,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        try
        {
            if (!await CanViewDocuments(transportCompanyId))
            {
                return Forbid("You don't have permission to view OCR analytics for this transport company");
            }

            var from = fromDate ?? DateTime.UtcNow.AddDays(-30);
            var to = toDate ?? DateTime.UtcNow;

            // This would be implemented as a query handler
            var analytics = new OCRAnalyticsDto
            {
                TransportCompanyId = transportCompanyId,
                FromDate = from,
                ToDate = to,
                GeneratedAt = DateTime.UtcNow,
                TotalDocumentsProcessed = 150,
                SuccessfulExtractions = 142,
                FailedExtractions = 8,
                AverageConfidence = 0.87m,
                AverageProcessingTime = TimeSpan.FromSeconds(3.2),
                DocumentTypeBreakdown = new Dictionary<string, int>
                {
                    ["GstCertificate"] = 45,
                    ["PanCard"] = 38,
                    ["DrivingLicense"] = 32,
                    ["VehicleRegistration"] = 25,
                    ["VehicleInsurance"] = 10
                },
                QualityMetrics = new OCRQualityMetricsDto
                {
                    AverageImageQuality = 0.82m,
                    BlurryDocuments = 12,
                    SkewedDocuments = 8,
                    LowQualityDocuments = 15,
                    QualityImprovementSuggestions = new List<string>
                    {
                        "Ensure good lighting when taking photos",
                        "Keep documents flat and avoid shadows",
                        "Use higher resolution camera settings"
                    }
                }
            };

            return Ok(analytics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting OCR analytics for Transport Company {TransportCompanyId}",
                transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    private async Task<bool> CanManageDocuments(Guid transportCompanyId)
    {
        var currentUserId = GetCurrentUserId();
        var userRoles = GetCurrentUserRoles();

        return userRoles.Contains("Admin") ||
               (userRoles.Contains("TransportCompany") && await IsUserFromTransportCompany(currentUserId, transportCompanyId));
    }

    private async Task<bool> CanViewDocuments(Guid transportCompanyId)
    {
        return await CanManageDocuments(transportCompanyId);
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst("sub") ?? User.FindFirst("userId");
        return userIdClaim != null && Guid.TryParse(userIdClaim.Value, out var userId) ? userId : Guid.Empty;
    }

    private List<string> GetCurrentUserRoles()
    {
        return User.FindAll("role").Select(c => c.Value).ToList();
    }

    private async Task<bool> IsUserFromTransportCompany(Guid userId, Guid transportCompanyId)
    {
        // This would typically check the user's company association
        return await Task.FromResult(true);
    }
}
