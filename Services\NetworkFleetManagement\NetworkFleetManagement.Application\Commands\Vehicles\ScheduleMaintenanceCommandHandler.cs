using AutoMapper;
using MediatR;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Repositories;
using Shared.Messaging;

namespace NetworkFleetManagement.Application.Commands.Vehicles;

public class ScheduleMaintenanceCommandHandler : IRequestHandler<ScheduleMaintenanceCommand, VehicleDto>
{
    private readonly IVehicleRepository _vehicleRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly IMessageBroker _messageBroker;

    public ScheduleMaintenanceCommandHandler(
        IVehicleRepository vehicleRepository,
        IUnitOfWork unitOfWork,
        IMapper mapper,
        IMessageBroker messageBroker)
    {
        _vehicleRepository = vehicleRepository;
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _messageBroker = messageBroker;
    }

    public async Task<VehicleDto> Handle(ScheduleMaintenanceCommand request, CancellationToken cancellationToken)
    {
        var vehicle = await _vehicleRepository.GetByIdAsync(request.VehicleId, cancellationToken);
        if (vehicle == null)
        {
            throw new InvalidOperationException($"Vehicle with ID {request.VehicleId} not found");
        }

        var maintenanceDto = request.MaintenanceDto;
        vehicle.ScheduleMaintenance(maintenanceDto.ScheduledDate, maintenanceDto.Notes);

        _vehicleRepository.Update(vehicle);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        // Publish integration event
        await _messageBroker.PublishAsync("maintenance.scheduled", new
        {
            VehicleId = vehicle.Id,
            CarrierId = vehicle.CarrierId,
            RegistrationNumber = vehicle.RegistrationNumber,
            ScheduledDate = maintenanceDto.ScheduledDate,
            Notes = maintenanceDto.Notes,
            ScheduledAt = DateTime.UtcNow
        }, cancellationToken);

        var result = _mapper.Map<VehicleDto>(vehicle);
        return result;
    }
}
