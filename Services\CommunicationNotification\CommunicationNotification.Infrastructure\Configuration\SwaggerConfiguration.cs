using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using System.ComponentModel;
using System.Reflection;

namespace CommunicationNotification.Infrastructure.Configuration;

/// <summary>
/// Swagger default values operation filter
/// </summary>
public class SwaggerDefaultValues : IOperationFilter
{
    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
        var apiDescription = context.ApiDescription;

        operation.Deprecated |= apiDescription.IsDeprecated();

        foreach (var responseType in context.ApiDescription.SupportedResponseTypes)
        {
            var responseKey = responseType.IsDefaultResponse ? "default" : responseType.StatusCode.ToString();
            var response = operation.Responses[responseKey];

            foreach (var contentType in response.Content.Keys)
            {
                if (responseType.ApiResponseFormats.All(x => x.MediaType != contentType))
                {
                    response.Content.Remove(contentType);
                }
            }
        }

        if (operation.Parameters == null)
            return;

        foreach (var parameter in operation.Parameters)
        {
            var description = apiDescription.ParameterDescriptions.First(p => p.Name == parameter.Name);

            parameter.Description ??= description.ModelMetadata?.Description;

            if (parameter.Schema.Default == null && description.DefaultValue != null)
            {
                parameter.Schema.Default = Microsoft.OpenApi.Any.OpenApiAnyFactory.CreateFromJson(
                    System.Text.Json.JsonSerializer.Serialize(description.DefaultValue));
            }

            parameter.Required |= description.IsRequired;
        }
    }
}

/// <summary>
/// Swagger enum descriptions document filter
/// </summary>
public class SwaggerEnumDescriptions : IDocumentFilter
{
    public void Apply(OpenApiDocument swaggerDoc, DocumentFilterContext context)
    {
        // Add enum descriptions to the document
        foreach (var schema in swaggerDoc.Components.Schemas.Values)
        {
            if (schema.Enum?.Any() == true)
            {
                var enumType = context.SchemaRepository.Schemas
                    .FirstOrDefault(x => x.Value == schema).Key;

                if (!string.IsNullOrEmpty(enumType))
                {
                    var type = Type.GetType(enumType);
                    if (type?.IsEnum == true)
                    {
                        schema.Description = GetEnumDescription(type);
                    }
                }
            }
        }
    }

    private string GetEnumDescription(Type enumType)
    {
        var descriptions = new List<string>();
        
        foreach (var value in Enum.GetValues(enumType))
        {
            var field = enumType.GetField(value.ToString()!);
            var description = field?.GetCustomAttribute<DescriptionAttribute>()?.Description ?? value.ToString();
            descriptions.Add($"{(int)value} = {value} ({description})");
        }

        return $"Possible values:\n{string.Join("\n", descriptions)}";
    }
}

/// <summary>
/// API documentation examples
/// </summary>
public static class ApiDocumentationExamples
{
    /// <summary>
    /// Notification API examples
    /// </summary>
    public static class NotificationExamples
    {
        public static readonly object SendNotificationRequest = new
        {
            userId = "123e4567-e89b-12d3-a456-426614174000",
            content = "Your trip has been confirmed! Driver: Rajesh Kumar, Vehicle: KA01AB1234. Pickup time: 9:00 AM from Bangalore Airport.",
            messageType = "TripConfirmation",
            priority = "High",
            preferredChannel = "WhatsApp",
            preferredLanguage = "Kannada",
            subject = "Trip Confirmation - Booking #12345",
            parameters = new
            {
                customer_name = "Suresh Kumar",
                trip_date = "2024-01-15",
                pickup_time = "09:00",
                pickup_location = "Bangalore Airport",
                driver_name = "Rajesh Kumar",
                vehicle_number = "KA01AB1234"
            },
            templateId = "trip_confirmation_template",
            requireDeliveryConfirmation = true,
            tags = new[] { "trip", "confirmation", "high-priority" },
            correlationId = "trip-12345-confirmation"
        };

        public static readonly object SendBulkNotificationRequest = new
        {
            userIds = new[]
            {
                "123e4567-e89b-12d3-a456-426614174000",
                "123e4567-e89b-12d3-a456-426614174001",
                "123e4567-e89b-12d3-a456-426614174002"
            },
            content = "New trip opportunities available in your area. Check the app for details.",
            messageType = "TripOpportunity",
            priority = "Normal",
            preferredChannel = "Push",
            preferredLanguage = "English",
            subject = "New Trip Opportunities",
            batchSize = 100,
            batchDelaySeconds = 5,
            tags = new[] { "bulk", "opportunity", "drivers" }
        };

        public static readonly object SendTemplateNotificationRequest = new
        {
            userId = "123e4567-e89b-12d3-a456-426614174000",
            templateId = "trip_confirmation",
            templateParameters = new
            {
                customer_name = "Suresh Kumar",
                trip_date = "January 15, 2024",
                pickup_time = "9:00 AM",
                pickup_location = "Bangalore Airport",
                driver_name = "Rajesh Kumar",
                vehicle_number = "KA01AB1234",
                estimated_fare = "₹450"
            },
            messageType = "TripConfirmation",
            priority = "High",
            preferredChannel = "WhatsApp",
            preferredLanguage = "Kannada",
            requireDeliveryConfirmation = true,
            tags = new[] { "template", "trip", "confirmation" }
        };

        public static readonly object NotificationSendResponse = new
        {
            notificationId = "123e4567-e89b-12d3-a456-426614174000",
            status = "Sent",
            channelUsed = "WhatsApp",
            sentAt = "2024-01-15T09:00:00Z",
            externalId = "wamid.HBgNOTE1234567890",
            estimatedDeliveryTime = "00:00:15",
            commandId = "123e4567-e89b-12d3-a456-426614174001",
            templateId = "trip_confirmation"
        };
    }

    /// <summary>
    /// Message API examples
    /// </summary>
    public static class MessageExamples
    {
        public static readonly object MessageHistoryResponse = new
        {
            messages = new[]
            {
                new
                {
                    messageId = "123e4567-e89b-12d3-a456-426614174000",
                    content = "Your trip has been confirmed!",
                    subject = "Trip Confirmation",
                    messageType = "TripConfirmation",
                    channel = "WhatsApp",
                    status = "Delivered",
                    priority = "High",
                    language = "Kannada",
                    sentAt = "2024-01-15T09:00:00Z",
                    deliveredAt = "2024-01-15T09:00:15Z",
                    readAt = "2024-01-15T09:05:30Z",
                    externalId = "wamid.HBgNOTE1234567890",
                    tags = new[] { "trip", "confirmation" },
                    templateId = "trip_confirmation"
                }
            },
            totalCount = 150,
            pageNumber = 1,
            pageSize = 20,
            totalPages = 8,
            hasNextPage = true,
            hasPreviousPage = false
        };

        public static readonly object MessageSearchRequest = new
        {
            searchTerm = "trip confirmation",
            userIds = new[] { "123e4567-e89b-12d3-a456-426614174000" },
            channels = new[] { "WhatsApp", "SMS" },
            messageTypes = new[] { "TripConfirmation", "TripUpdate" },
            statuses = new[] { "Delivered", "Read" },
            fromDate = "2024-01-01T00:00:00Z",
            toDate = "2024-01-31T23:59:59Z",
            tags = new[] { "trip", "high-priority" },
            pageNumber = 1,
            pageSize = 20,
            sortBy = "SentAt",
            sortDirection = "DESC"
        };
    }

    /// <summary>
    /// User Preferences API examples
    /// </summary>
    public static class UserPreferencesExamples
    {
        public static readonly object UpdateUserPreferencesRequest = new
        {
            preferredLanguage = "Kannada",
            timeZone = "Asia/Kolkata",
            quietHoursStart = "22:00:00",
            quietHoursEnd = "07:00:00",
            enableQuietHours = true,
            channelPreferences = new
            {
                WhatsApp = new
                {
                    isEnabled = true,
                    priority = 9,
                    contactInfo = "+919876543210",
                    settings = new
                    {
                        enableReadReceipts = true,
                        enableGroupMessages = false
                    }
                },
                SMS = new
                {
                    isEnabled = true,
                    priority = 7,
                    contactInfo = "+919876543210",
                    settings = new
                    {
                        enableDeliveryReports = true
                    }
                },
                Email = new
                {
                    isEnabled = true,
                    priority = 5,
                    contactInfo = "<EMAIL>",
                    settings = new
                    {
                        enableHtmlEmails = true,
                        enableClickTracking = false
                    }
                },
                Push = new
                {
                    isEnabled = true,
                    priority = 8,
                    contactInfo = "device_token_here",
                    settings = new
                    {
                        enableSound = true,
                        enableVibration = true,
                        enableBadge = true
                    }
                }
            },
            messageTypePreferences = new
            {
                TripConfirmation = new
                {
                    isEnabled = true,
                    preferredChannels = new[] { "WhatsApp", "SMS" },
                    requireConfirmation = false,
                    settings = new
                    {
                        sendImmediately = true
                    }
                },
                TripUpdate = new
                {
                    isEnabled = true,
                    preferredChannels = new[] { "Push", "WhatsApp" },
                    requireConfirmation = false,
                    settings = new
                    {
                        enableRealTimeUpdates = true
                    }
                },
                Marketing = new
                {
                    isEnabled = false,
                    preferredChannels = new[] { "Email" },
                    requireConfirmation = true,
                    settings = new
                    {
                        maxFrequency = "weekly"
                    }
                }
            },
            globalOptOut = false,
            marketingOptOut = true
        };

        public static readonly object UserPreferencesResponse = new
        {
            userId = "123e4567-e89b-12d3-a456-426614174000",
            preferredLanguage = "Kannada",
            timeZone = "Asia/Kolkata",
            quietHoursStart = "22:00:00",
            quietHoursEnd = "07:00:00",
            enableQuietHours = true,
            channelPreferences = new
            {
                WhatsApp = new
                {
                    isEnabled = true,
                    priority = 9,
                    contactInfo = "+919876543210",
                    settings = new
                    {
                        enableReadReceipts = true,
                        enableGroupMessages = false
                    }
                }
            },
            messageTypePreferences = new
            {
                TripConfirmation = new
                {
                    isEnabled = true,
                    preferredChannels = new[] { "WhatsApp", "SMS" },
                    requireConfirmation = false,
                    settings = new
                    {
                        sendImmediately = true
                    }
                }
            },
            globalOptOut = false,
            marketingOptOut = true,
            lastUpdated = "2024-01-15T10:30:00Z",
            createdAt = "2024-01-01T00:00:00Z"
        };
    }

    /// <summary>
    /// Chat API examples
    /// </summary>
    public static class ChatExamples
    {
        public static readonly object CreateChatConversationRequest = new
        {
            title = "Trip #12345 - Bangalore to Mysore",
            description = "Communication channel for trip coordination",
            conversationType = "TripCoordination",
            participantIds = new[]
            {
                "123e4567-e89b-12d3-a456-426614174000", // Customer
                "123e4567-e89b-12d3-a456-426614174001", // Driver
                "123e4567-e89b-12d3-a456-426614174002"  // Support
            },
            tripId = "123e4567-e89b-12d3-a456-426614174003",
            isPrivate = false,
            allowFileSharing = true,
            maxParticipants = 10,
            tags = new[] { "trip", "coordination", "bangalore-mysore" }
        };

        public static readonly object SendChatMessageRequest = new
        {
            content = "I'm 5 minutes away from the pickup location. Please be ready.",
            messageType = "Text",
            replyToMessageId = "123e4567-e89b-12d3-a456-426614174004",
            attachments = new[]
            {
                new
                {
                    fileName = "location.jpg",
                    fileSize = 245760,
                    contentType = "image/jpeg",
                    fileData = "base64_encoded_image_data_here"
                }
            },
            metadata = new
            {
                location = new
                {
                    latitude = 12.9716,
                    longitude = 77.5946
                },
                urgency = "high"
            }
        };

        public static readonly object ChatMessageResponse = new
        {
            messageId = "123e4567-e89b-12d3-a456-426614174000",
            conversationId = "123e4567-e89b-12d3-a456-426614174001",
            content = "I'm 5 minutes away from the pickup location. Please be ready.",
            messageType = "Text",
            senderId = "123e4567-e89b-12d3-a456-426614174002",
            sentAt = "2024-01-15T08:55:00Z",
            isEdited = false,
            editedAt = null,
            replyToMessageId = "123e4567-e89b-12d3-a456-426614174004",
            attachments = new[]
            {
                new
                {
                    fileName = "location.jpg",
                    fileSize = 245760,
                    contentType = "image/jpeg",
                    downloadUrl = "https://storage.tli.com/chat/attachments/location.jpg"
                }
            }
        };
    }
}
