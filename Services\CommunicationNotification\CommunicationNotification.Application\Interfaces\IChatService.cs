using CommunicationNotification.Domain.Entities;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;

namespace CommunicationNotification.Application.Interfaces;

/// <summary>
/// Interface for real-time chat functionality
/// </summary>
public interface IChatService
{
    /// <summary>
    /// Send a chat message
    /// </summary>
    /// <param name="request">Chat message request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Chat message result</returns>
    Task<ChatMessageResult> SendMessageAsync(
        ChatMessageRequest request,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get conversation history
    /// </summary>
    /// <param name="conversationId">Conversation thread ID</param>
    /// <param name="pageSize">Number of messages per page</param>
    /// <param name="pageToken">Page token for pagination</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Conversation history</returns>
    Task<ConversationHistory> GetConversationHistoryAsync(
        Guid conversationId,
        int pageSize = 50,
        string? pageToken = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Create a new conversation thread
    /// </summary>
    /// <param name="request">Conversation creation request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Created conversation thread</returns>
    Task<ConversationThread> CreateConversationAsync(
        CreateConversationRequest request,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Add participant to conversation
    /// </summary>
    /// <param name="conversationId">Conversation ID</param>
    /// <param name="userId">User ID to add</param>
    /// <param name="role">Participant role</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if added successfully</returns>
    Task<bool> AddParticipantAsync(
        Guid conversationId,
        Guid userId,
        ConversationRole role = ConversationRole.Participant,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Remove participant from conversation
    /// </summary>
    /// <param name="conversationId">Conversation ID</param>
    /// <param name="userId">User ID to remove</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if removed successfully</returns>
    Task<bool> RemoveParticipantAsync(
        Guid conversationId,
        Guid userId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Mark message as read
    /// </summary>
    /// <param name="messageId">Message ID</param>
    /// <param name="userId">User ID who read the message</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if marked successfully</returns>
    Task<bool> MarkMessageAsReadAsync(
        Guid messageId,
        Guid userId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get user's active conversations
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of active conversations</returns>
    Task<List<ConversationSummary>> GetUserConversationsAsync(
        Guid userId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Search messages in conversation
    /// </summary>
    /// <param name="conversationId">Conversation ID</param>
    /// <param name="searchQuery">Search query</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Search results</returns>
    Task<List<Message>> SearchMessagesAsync(
        Guid conversationId,
        string searchQuery,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Chat message request
/// </summary>
public class ChatMessageRequest
{
    public Guid SenderId { get; set; }
    public Guid? ConversationId { get; set; }
    public List<Guid>? RecipientIds { get; set; }
    public MessageContent Content { get; set; } = MessageContent.Create("", "", Language.English);
    public MessageType MessageType { get; set; } = MessageType.Text;
    public Priority Priority { get; set; } = Priority.Normal;
    public Guid? RelatedEntityId { get; set; }
    public string? RelatedEntityType { get; set; }
    public Dictionary<string, string>? Metadata { get; set; }
}

/// <summary>
/// Chat message result
/// </summary>
public class ChatMessageResult
{
    public bool IsSuccess { get; set; }
    public Guid? MessageId { get; set; }
    public Guid? ConversationId { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime SentAt { get; set; } = DateTime.UtcNow;

    public static ChatMessageResult Success(Guid messageId, Guid conversationId)
    {
        return new ChatMessageResult
        {
            IsSuccess = true,
            MessageId = messageId,
            ConversationId = conversationId
        };
    }

    public static ChatMessageResult Failure(string errorMessage)
    {
        return new ChatMessageResult
        {
            IsSuccess = false,
            ErrorMessage = errorMessage
        };
    }
}

/// <summary>
/// Conversation history
/// </summary>
public class ConversationHistory
{
    public Guid ConversationId { get; set; }
    public List<Message> Messages { get; set; } = new();
    public string? NextPageToken { get; set; }
    public bool HasMoreMessages { get; set; }
    public int TotalMessages { get; set; }
}

/// <summary>
/// Conversation thread
/// </summary>
public class ConversationThread
{
    public Guid Id { get; set; }
    public string? Title { get; set; }
    public ConversationType Type { get; set; }
    public List<ConversationParticipant> Participants { get; set; } = new();
    public Guid? RelatedEntityId { get; set; }
    public string? RelatedEntityType { get; set; }
    public DateTime CreatedAt { get; set; }
    public Guid CreatedBy { get; set; }
    public bool IsActive { get; set; } = true;
    public Dictionary<string, string> Metadata { get; set; } = new();
}

/// <summary>
/// Create conversation request
/// </summary>
public class CreateConversationRequest
{
    public string? Title { get; set; }
    public ConversationType Type { get; set; } = ConversationType.Direct;
    public List<Guid> ParticipantIds { get; set; } = new();
    public Guid CreatedBy { get; set; }
    public Guid? RelatedEntityId { get; set; }
    public string? RelatedEntityType { get; set; }
    public Dictionary<string, string>? Metadata { get; set; }
}

/// <summary>
/// Conversation participant
/// </summary>
public class ConversationParticipant
{
    public Guid UserId { get; set; }
    public ConversationRole Role { get; set; }
    public DateTime JoinedAt { get; set; }
    public DateTime? LastReadAt { get; set; }
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// Conversation summary
/// </summary>
public class ConversationSummary
{
    public Guid Id { get; set; }
    public string? Title { get; set; }
    public ConversationType Type { get; set; }
    public int ParticipantCount { get; set; }
    public Message? LastMessage { get; set; }
    public int UnreadCount { get; set; }
    public DateTime LastActivity { get; set; }
    public Guid? RelatedEntityId { get; set; }
    public string? RelatedEntityType { get; set; }
}

/// <summary>
/// Conversation type
/// </summary>
public enum ConversationType
{
    Direct = 1,
    Group = 2,
    Trip = 3,
    Order = 4,
    Support = 5
}

/// <summary>
/// Conversation role
/// </summary>
public enum ConversationRole
{
    Participant = 1,
    Moderator = 2,
    Admin = 3
}
