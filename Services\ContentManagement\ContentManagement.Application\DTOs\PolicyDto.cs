using ContentManagement.Domain.Enums;

namespace ContentManagement.Application.DTOs;

/// <summary>
/// Data transfer object for policy documents
/// </summary>
public class PolicyDocumentDto : ContentDto
{
    public PolicyType PolicyType { get; set; }
    public string Version { get; set; } = string.Empty;
    public DateTime EffectiveDate { get; set; }
    public DateTime? ExpiryDate { get; set; }
    public bool RequiresAcceptance { get; set; }
    public List<string> ApplicableRoles { get; set; } = new();
    public string? LegalReferences { get; set; }
    public bool IsCurrentVersion { get; set; }
    public List<PolicyAcceptanceDto> Acceptances { get; set; } = new();
}

/// <summary>
/// Data transfer object for policy acceptance
/// </summary>
public class PolicyAcceptanceDto
{
    public Guid Id { get; set; }
    public Guid PolicyId { get; set; }
    public Guid UserId { get; set; }
    public string UserName { get; set; } = string.Empty;
    public string UserRole { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public DateTime AcceptedAt { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
}

/// <summary>
/// Simplified policy DTO for lists
/// </summary>
public class PolicySummaryDto
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public PolicyType PolicyType { get; set; }
    public string Version { get; set; } = string.Empty;
    public ContentStatus Status { get; set; }
    public DateTime EffectiveDate { get; set; }
    public DateTime? ExpiryDate { get; set; }
    public bool RequiresAcceptance { get; set; }
    public bool IsCurrentVersion { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public int AcceptanceCount { get; set; }
}

/// <summary>
/// Policy search result DTO
/// </summary>
public class PolicySearchResultDto
{
    public List<PolicySummaryDto> Items { get; set; } = new();
    public int TotalCount { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public int TotalPages { get; set; }
    public bool HasNextPage { get; set; }
    public bool HasPreviousPage { get; set; }
    public Dictionary<PolicyType, int> TypeCounts { get; set; } = new();
}

/// <summary>
/// Policy compliance report DTO
/// </summary>
public class PolicyComplianceReportDto
{
    public Guid PolicyId { get; set; }
    public string Title { get; set; } = string.Empty;
    public PolicyType PolicyType { get; set; }
    public string Version { get; set; } = string.Empty;
    public int TotalApplicableUsers { get; set; }
    public int AcceptedCount { get; set; }
    public int PendingCount { get; set; }
    public decimal CompliancePercentage { get; set; }
    public DateTime ReportGeneratedAt { get; set; }
    public List<PolicyAcceptanceDto> RecentAcceptances { get; set; } = new();
}

/// <summary>
/// User policy status DTO
/// </summary>
public class UserPolicyStatusDto
{
    public Guid UserId { get; set; }
    public string UserName { get; set; } = string.Empty;
    public string UserRole { get; set; } = string.Empty;
    public List<PolicyStatusItemDto> PolicyStatuses { get; set; } = new();
}

/// <summary>
/// Individual policy status for a user
/// </summary>
public class PolicyStatusItemDto
{
    public Guid PolicyId { get; set; }
    public string Title { get; set; } = string.Empty;
    public PolicyType PolicyType { get; set; }
    public string Version { get; set; } = string.Empty;
    public bool RequiresAcceptance { get; set; }
    public bool IsAccepted { get; set; }
    public DateTime? AcceptedAt { get; set; }
    public bool IsApplicable { get; set; }
    public DateTime EffectiveDate { get; set; }
    public DateTime? ExpiryDate { get; set; }
}
