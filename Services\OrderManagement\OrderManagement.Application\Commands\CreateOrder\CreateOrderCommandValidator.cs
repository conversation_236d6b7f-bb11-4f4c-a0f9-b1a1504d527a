using FluentValidation;

namespace OrderManagement.Application.Commands.CreateOrder;

public class CreateOrderCommandValidator : AbstractValidator<CreateOrderCommand>
{
    public CreateOrderCommandValidator()
    {
        RuleFor(x => x.TransportCompanyId)
            .NotEmpty()
            .WithMessage("Transport company ID is required");

        RuleFor(x => x.BrokerId)
            .NotEmpty()
            .WithMessage("Broker ID is required");

        RuleFor(x => x.Title)
            .NotEmpty()
            .WithMessage("Order title is required")
            .MaximumLength(200)
            .WithMessage("Order title cannot exceed 200 characters");

        RuleFor(x => x.Description)
            .NotEmpty()
            .WithMessage("Order description is required")
            .MaximumLength(2000)
            .WithMessage("Order description cannot exceed 2000 characters");

        RuleFor(x => x.LoadDetails)
            .NotNull()
            .WithMessage("Load details are required")
            .SetValidator(new CreateOrderLoadDetailsValidator());

        RuleFor(x => x.RouteDetails)
            .NotNull()
            .WithMessage("Route details are required")
            .SetValidator(new CreateOrderRouteDetailsValidator());

        RuleFor(x => x.AgreedPrice)
            .NotNull()
            .WithMessage("Agreed price is required")
            .SetValidator(new CreateOrderMoneyValidator());

        RuleFor(x => x.CustomerDetails)
            .NotNull()
            .WithMessage("Customer details are required")
            .SetValidator(new CreateOrderCustomerDetailsValidator());

        RuleFor(x => x.BillingDetails)
            .NotNull()
            .WithMessage("Billing details are required")
            .SetValidator(new CreateOrderBillingDetailsValidator());

        RuleFor(x => x.SpecialInstructions)
            .MaximumLength(1000)
            .WithMessage("Special instructions cannot exceed 1000 characters")
            .When(x => !string.IsNullOrEmpty(x.SpecialInstructions));
    }
}

public class CreateOrderLoadDetailsValidator : AbstractValidator<CreateOrderLoadDetailsDto>
{
    public CreateOrderLoadDetailsValidator()
    {
        RuleFor(x => x.Description)
            .NotEmpty()
            .WithMessage("Load description is required")
            .MaximumLength(500)
            .WithMessage("Load description cannot exceed 500 characters");

        RuleFor(x => x.LoadType)
            .IsInEnum()
            .WithMessage("Valid load type is required");

        RuleFor(x => x.Weight)
            .NotNull()
            .WithMessage("Weight is required")
            .SetValidator(new CreateOrderWeightValidator());

        RuleFor(x => x.Quantity)
            .GreaterThan(0)
            .WithMessage("Quantity must be greater than 0");

        RuleFor(x => x.RequiredVehicleType)
            .IsInEnum()
            .WithMessage("Valid vehicle type is required");

        RuleFor(x => x.SpecialHandlingInstructions)
            .MaximumLength(500)
            .WithMessage("Special handling instructions cannot exceed 500 characters")
            .When(x => x.RequiresSpecialHandling);

        RuleFor(x => x.HazardousClassification)
            .NotEmpty()
            .WithMessage("Hazardous classification is required when load is hazardous")
            .When(x => x.IsHazardous);

        RuleFor(x => x.TemperatureRange)
            .NotNull()
            .WithMessage("Temperature range is required when temperature control is needed")
            .When(x => x.RequiresTemperatureControl);
    }
}

public class CreateOrderRouteDetailsValidator : AbstractValidator<CreateOrderRouteDetailsDto>
{
    public CreateOrderRouteDetailsValidator()
    {
        RuleFor(x => x.PickupAddress)
            .NotNull()
            .WithMessage("Pickup address is required")
            .SetValidator(new CreateOrderAddressValidator());

        RuleFor(x => x.DeliveryAddress)
            .NotNull()
            .WithMessage("Delivery address is required")
            .SetValidator(new CreateOrderAddressValidator());

        RuleFor(x => x.PreferredPickupDate)
            .GreaterThan(DateTime.UtcNow)
            .WithMessage("Preferred pickup date must be in the future");

        RuleFor(x => x.PreferredDeliveryDate)
            .GreaterThan(x => x.PreferredPickupDate)
            .WithMessage("Preferred delivery date must be after pickup date");
    }
}

public class CreateOrderCustomerDetailsValidator : AbstractValidator<CreateOrderCustomerDetailsDto>
{
    public CreateOrderCustomerDetailsValidator()
    {
        RuleFor(x => x.CompanyName)
            .NotEmpty()
            .WithMessage("Company name is required")
            .MaximumLength(200)
            .WithMessage("Company name cannot exceed 200 characters");

        RuleFor(x => x.ContactPerson)
            .NotEmpty()
            .WithMessage("Contact person is required")
            .MaximumLength(100)
            .WithMessage("Contact person cannot exceed 100 characters");

        RuleFor(x => x.ContactEmail)
            .NotEmpty()
            .WithMessage("Contact email is required")
            .EmailAddress()
            .WithMessage("Valid email address is required");

        RuleFor(x => x.ContactPhone)
            .NotEmpty()
            .WithMessage("Contact phone is required")
            .MaximumLength(20)
            .WithMessage("Contact phone cannot exceed 20 characters");

        RuleFor(x => x.BillingAddress)
            .NotNull()
            .WithMessage("Billing address is required")
            .SetValidator(new CreateOrderAddressValidator());
    }
}

public class CreateOrderBillingDetailsValidator : AbstractValidator<CreateOrderBillingDetailsDto>
{
    public CreateOrderBillingDetailsValidator()
    {
        RuleFor(x => x.BillingMethod)
            .NotEmpty()
            .WithMessage("Billing method is required")
            .MaximumLength(50)
            .WithMessage("Billing method cannot exceed 50 characters");

        RuleFor(x => x.PaymentTermsDays)
            .GreaterThanOrEqualTo(0)
            .WithMessage("Payment terms days cannot be negative");
    }
}

public class CreateOrderAddressValidator : AbstractValidator<CreateOrderAddressDto>
{
    public CreateOrderAddressValidator()
    {
        RuleFor(x => x.Street)
            .NotEmpty()
            .WithMessage("Street address is required")
            .MaximumLength(200)
            .WithMessage("Street address cannot exceed 200 characters");

        RuleFor(x => x.City)
            .NotEmpty()
            .WithMessage("City is required")
            .MaximumLength(100)
            .WithMessage("City cannot exceed 100 characters");

        RuleFor(x => x.State)
            .NotEmpty()
            .WithMessage("State is required")
            .MaximumLength(50)
            .WithMessage("State cannot exceed 50 characters");

        RuleFor(x => x.PostalCode)
            .NotEmpty()
            .WithMessage("Postal code is required")
            .MaximumLength(20)
            .WithMessage("Postal code cannot exceed 20 characters");

        RuleFor(x => x.Country)
            .NotEmpty()
            .WithMessage("Country is required")
            .MaximumLength(50)
            .WithMessage("Country cannot exceed 50 characters");
    }
}

public class CreateOrderMoneyValidator : AbstractValidator<CreateOrderMoneyDto>
{
    public CreateOrderMoneyValidator()
    {
        RuleFor(x => x.Amount)
            .GreaterThan(0)
            .WithMessage("Amount must be greater than 0");

        RuleFor(x => x.Currency)
            .NotEmpty()
            .WithMessage("Currency is required")
            .Length(3)
            .WithMessage("Currency must be a 3-letter code (e.g., USD)");
    }
}

public class CreateOrderWeightValidator : AbstractValidator<CreateOrderWeightDto>
{
    public CreateOrderWeightValidator()
    {
        RuleFor(x => x.Value)
            .GreaterThan(0)
            .WithMessage("Weight value must be greater than 0");

        RuleFor(x => x.Unit)
            .IsInEnum()
            .WithMessage("Valid weight unit is required");
    }
}
