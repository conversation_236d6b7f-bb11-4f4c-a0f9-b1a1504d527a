using AnalyticsBIService.Domain.Entities;
using Shared.Infrastructure.Repositories;

namespace AnalyticsBIService.Domain.Repositories;

/// <summary>
/// Repository interface for data pipeline operations
/// </summary>
public interface IDataPipelineRepository : IRepositoryBase<DataPipeline, Guid>
{
    /// <summary>
    /// Get data pipelines by user ID
    /// </summary>
    Task<IEnumerable<DataPipeline>> GetByUserIdAsync(Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get data pipelines by status
    /// </summary>
    Task<IEnumerable<DataPipeline>> GetByStatusAsync(string status, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get active data pipelines
    /// </summary>
    Task<IEnumerable<DataPipeline>> GetActiveAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get data pipelines scheduled to run
    /// </summary>
    Task<IEnumerable<DataPipeline>> GetScheduledToRunAsync(DateTime beforeTime, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get data pipeline by name and user ID
    /// </summary>
    Task<DataPipeline?> GetByNameAndUserIdAsync(string name, Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get recent pipeline executions
    /// </summary>
    Task<IEnumerable<DataPipeline>> GetRecentExecutionsAsync(int count = 10, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get pipeline execution statistics
    /// </summary>
    Task<Dictionary<string, object>> GetExecutionStatisticsAsync(Guid pipelineId, CancellationToken cancellationToken = default);
}
