using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Identity.Domain.Entities;

namespace Identity.Application.Common.Interfaces
{
    public interface IPermissionRepository
    {
        Task<Permission> GetByIdAsync(Guid id);
        Task<Permission> GetByNameAsync(string name);
        Task<IEnumerable<Permission>> GetAllAsync();
        Task<IEnumerable<Permission>> GetByModuleAsync(string module);
        Task<IEnumerable<Permission>> GetByCategoryAsync(string category);
        Task<IEnumerable<Permission>> GetByTypeAsync(PermissionType type);
        Task<IEnumerable<Permission>> GetSystemPermissionsAsync();
        Task<IEnumerable<Permission>> GetUserPermissionsAsync(Guid userId);
        Task<IEnumerable<Permission>> GetRolePermissionsAsync(Guid roleId);
        Task<(IEnumerable<Permission> Items, int TotalCount)> GetPagedAsync(
            int pageNumber, 
            int pageSize, 
            string searchTerm = null,
            string module = null,
            string category = null,
            PermissionType? type = null);
        Task AddAsync(Permission permission);
        Task UpdateAsync(Permission permission);
        Task DeleteAsync(Guid id);
        Task<bool> ExistsAsync(Guid id);
        Task<bool> ExistsByNameAsync(string name);
        Task<Dictionary<string, List<Permission>>> GetPermissionsByModuleGroupedAsync();
        Task<IEnumerable<string>> GetAllModulesAsync();
        Task<IEnumerable<string>> GetAllCategoriesAsync();
        Task InitializeSystemPermissionsAsync();
    }
}
