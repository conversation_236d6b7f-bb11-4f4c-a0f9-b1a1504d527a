using AnalyticsBIService.Domain.Enums;
using Shared.Domain.Common;

namespace AnalyticsBIService.Domain.Entities;

/// <summary>
/// KPI monitor entity for performance tracking
/// </summary>
public class KPIMonitor : BaseEntity
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public string KPIType { get; private set; }
    public string DataSource { get; private set; }
    public string Query { get; private set; }
    public string AggregationMethod { get; private set; }
    public int MonitoringIntervalMinutes { get; private set; }
    public string Configuration { get; private set; }
    public bool IsActive { get; private set; }
    public Guid CreatedBy { get; private set; }
    public UserType UserType { get; private set; }
    public DateTime? LastCalculated { get; private set; }
    public decimal? LastValue { get; private set; }
    public string? LastStatus { get; private set; }

    private KPIMonitor()
    {
        Name = string.Empty;
        Description = string.Empty;
        KPIType = string.Empty;
        DataSource = string.Empty;
        Query = string.Empty;
        AggregationMethod = string.Empty;
        Configuration = string.Empty;
    }

    public KPIMonitor(
        string name,
        string description,
        string kpiType,
        string dataSource,
        string query,
        string aggregationMethod,
        int monitoringIntervalMinutes,
        string configuration,
        Guid createdBy,
        UserType userType)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        KPIType = kpiType ?? throw new ArgumentNullException(nameof(kpiType));
        DataSource = dataSource ?? throw new ArgumentNullException(nameof(dataSource));
        Query = query ?? throw new ArgumentNullException(nameof(query));
        AggregationMethod = aggregationMethod ?? throw new ArgumentNullException(nameof(aggregationMethod));
        MonitoringIntervalMinutes = monitoringIntervalMinutes;
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        CreatedBy = createdBy;
        UserType = userType;
        IsActive = true;
    }

    public void UpdateName(string name)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        SetUpdatedAt();
    }

    public void UpdateDescription(string description)
    {
        Description = description ?? throw new ArgumentNullException(nameof(description));
        SetUpdatedAt();
    }

    public void UpdateAggregationMethod(string aggregationMethod)
    {
        AggregationMethod = aggregationMethod ?? throw new ArgumentNullException(nameof(aggregationMethod));
        SetUpdatedAt();
    }

    public void UpdateConfiguration(string configuration)
    {
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        SetUpdatedAt();
    }

    public void UpdateQuery(string query)
    {
        Query = query ?? throw new ArgumentNullException(nameof(query));
        SetUpdatedAt();
    }

    public void UpdateMonitoringInterval(int intervalMinutes)
    {
        MonitoringIntervalMinutes = intervalMinutes;
        SetUpdatedAt();
    }

    public void RecordCalculation()
    {
        LastCalculated = DateTime.UtcNow;
        SetUpdatedAt();
    }

    public void UpdateLastValue(decimal? value, string? status)
    {
        LastValue = value;
        LastStatus = status;
        LastCalculated = DateTime.UtcNow;
        SetUpdatedAt();
    }

    public void Activate()
    {
        IsActive = true;
        SetUpdatedAt();
    }

    public void Deactivate()
    {
        IsActive = false;
        SetUpdatedAt();
    }
}
