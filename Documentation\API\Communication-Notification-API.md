# 📢 Communication & Notification Service API Documentation

## 📋 Overview

The Communication & Notification Service provides comprehensive multi-channel communication capabilities including WhatsApp Business API, SMS, Email, Push Notifications, Voice calls, and real-time chat. This service is 80% complete and production-ready for core features.

**Base URL**: `/api/v1/notifications` (via API Gateway)  
**Direct URL**: `http://localhost:5008`  
**Authentication**: Required for all endpoints  
**Authorization**: Role-based access control

## 🔐 Authentication & Permissions

### Required Permissions

| Operation             | Permission                         | Roles                   |
| --------------------- | ---------------------------------- | ----------------------- |
| Send notifications    | `notifications.send`               | All authenticated users |
| View notifications    | `notifications.read`               | All authenticated users |
| Manage templates      | `notifications.templates.manage`   | Admin, Marketing        |
| Configure preferences | `notifications.preferences.manage` | All authenticated users |
| View analytics        | `notifications.analytics.read`     | Admin, Marketing        |
| Manage campaigns      | `notifications.campaigns.manage`   | Admin, Marketing        |
| Access chat           | `notifications.chat.access`        | All authenticated users |
| Moderate chat         | `notifications.chat.moderate`      | Admin, Support          |

## 📊 Core Endpoints

### 1. Notification Management

#### Send Notification

```http
POST /api/v1/notifications/send
Authorization: Bearer <token>
Content-Type: application/json

{
  "userId": "user-uuid",
  "content": "Your trip from Mumbai to Delhi has been confirmed for January 15, 2024",
  "messageType": "TripConfirmation",
  "priority": "High",
  "preferredChannel": "WhatsApp",
  "preferredLanguage": "English",
  "subject": "Trip Confirmation - TLI Transport",
  "templateId": "trip_confirmation_template",
  "templateParameters": {
    "customerName": "Rajesh Kumar",
    "tripDate": "January 15, 2024",
    "pickupLocation": "Mumbai Airport",
    "deliveryLocation": "Delhi Railway Station",
    "tripId": "TRP-2024-001234"
  },
  "requireDeliveryConfirmation": true,
  "scheduledAt": "2024-01-14T18:00:00Z",
  "tags": ["trip", "confirmation", "high-priority"],
  "metadata": {
    "orderId": "order-uuid",
    "tripId": "trip-uuid",
    "source": "TripManagement"
  }
}
```

**Response:**

```json
{
  "id": "notification-uuid",
  "userId": "user-uuid",
  "messageType": "TripConfirmation",
  "status": "Sent",
  "channel": "WhatsApp",
  "priority": "High",
  "sentAt": "2024-01-14T18:00:00Z",
  "externalId": "whatsapp_msg_123456",
  "deliveryStatus": "Delivered",
  "deliveredAt": "2024-01-14T18:00:15Z",
  "readAt": null,
  "estimatedCost": {
    "amount": 0.05,
    "currency": "USD"
  }
}
```

#### Send Bulk Notifications

```http
POST /api/v1/notifications/bulk
Authorization: Bearer <token>
Content-Type: application/json

{
  "recipients": [
    {
      "userId": "user-uuid-1",
      "personalizedContent": {
        "customerName": "Rajesh Kumar",
        "tripDate": "January 15, 2024"
      }
    },
    {
      "userId": "user-uuid-2",
      "personalizedContent": {
        "customerName": "Priya Sharma",
        "tripDate": "January 16, 2024"
      }
    }
  ],
  "templateId": "trip_confirmation_template",
  "messageType": "TripConfirmation",
  "priority": "Normal",
  "preferredChannel": "SMS",
  "scheduledAt": "2024-01-14T20:00:00Z",
  "batchSize": 100,
  "delayBetweenBatches": 60
}
```

**Response:**

```json
{
  "batchId": "batch-uuid",
  "totalRecipients": 2,
  "status": "Processing",
  "estimatedCompletionTime": "2024-01-14T20:05:00Z",
  "createdAt": "2024-01-14T19:30:00Z",
  "progress": {
    "processed": 0,
    "successful": 0,
    "failed": 0,
    "pending": 2
  }
}
```

#### Get Notification Status

```http
GET /api/v1/notifications/{notificationId}
Authorization: Bearer <token>
```

**Response:**

```json
{
  "id": "notification-uuid",
  "userId": "user-uuid",
  "messageType": "TripConfirmation",
  "content": {
    "text": "Your trip from Mumbai to Delhi has been confirmed",
    "subject": "Trip Confirmation - TLI Transport",
    "language": "English"
  },
  "status": "Delivered",
  "channel": "WhatsApp",
  "priority": "High",
  "createdAt": "2024-01-14T17:30:00Z",
  "scheduledAt": "2024-01-14T18:00:00Z",
  "sentAt": "2024-01-14T18:00:00Z",
  "deliveredAt": "2024-01-14T18:00:15Z",
  "readAt": "2024-01-14T18:05:30Z",
  "externalId": "whatsapp_msg_123456",
  "deliveryAttempts": 1,
  "lastAttemptAt": "2024-01-14T18:00:00Z",
  "failureReason": null,
  "estimatedCost": {
    "amount": 0.05,
    "currency": "USD"
  },
  "metadata": {
    "orderId": "order-uuid",
    "tripId": "trip-uuid"
  }
}
```

#### Get User Notifications

```http
GET /api/v1/notifications/user/{userId}
Authorization: Bearer <token>
```

**Query Parameters:**

- `messageType`: Filter by message type
- `status`: Filter by notification status
- `channel`: Filter by communication channel
- `fromDate`: Filter from date
- `toDate`: Filter to date
- `page`: Page number (default: 1)
- `pageSize`: Items per page (default: 20)
- `includeRead`: Include read notifications (default: true)

**Response:**

```json
{
  "data": [
    {
      "id": "notification-uuid",
      "messageType": "TripConfirmation",
      "content": {
        "text": "Your trip has been confirmed",
        "subject": "Trip Confirmation"
      },
      "status": "Delivered",
      "channel": "WhatsApp",
      "priority": "High",
      "sentAt": "2024-01-14T18:00:00Z",
      "readAt": "2024-01-14T18:05:30Z",
      "isRead": true
    }
  ],
  "pagination": {
    "currentPage": 1,
    "pageSize": 20,
    "totalItems": 45,
    "totalPages": 3,
    "hasNextPage": true,
    "hasPreviousPage": false
  },
  "summary": {
    "totalNotifications": 45,
    "unreadCount": 8,
    "readCount": 37,
    "byChannel": {
      "WhatsApp": 25,
      "SMS": 15,
      "Email": 5
    }
  }
}
```

### 2. Channel-Specific Messaging

#### Send WhatsApp Message

```http
POST /api/v1/notifications/whatsapp/send
Authorization: Bearer <token>
Content-Type: application/json

{
  "phoneNumber": "+91-9876543210",
  "messageType": "text",
  "content": {
    "text": "Your OTP for TLI login is: 123456. Valid for 5 minutes."
  },
  "templateName": "otp_verification",
  "templateLanguage": "en",
  "templateParameters": [
    {
      "type": "text",
      "value": "123456"
    },
    {
      "type": "text",
      "value": "5 minutes"
    }
  ]
}
```

#### Send WhatsApp Media Message

```http
POST /api/v1/notifications/whatsapp/media
Authorization: Bearer <token>
Content-Type: application/json

{
  "phoneNumber": "+91-9876543210",
  "mediaType": "image",
  "mediaUrl": "https://storage.tli.com/trip-photos/photo-uuid.jpg",
  "caption": "Your cargo has been loaded successfully",
  "filename": "cargo_loading_proof.jpg"
}
```

#### Send SMS

```http
POST /api/v1/notifications/sms/send
Authorization: Bearer <token>
Content-Type: application/json

{
  "phoneNumber": "+91-9876543210",
  "message": "Your trip TRP-2024-001234 is starting now. Driver: Rajesh Kumar, Vehicle: MH-01-AB-1234",
  "enableDeliveryReceipt": true,
  "validityPeriod": 1440,
  "metadata": {
    "tripId": "trip-uuid",
    "messageType": "TripStart"
  }
}
```

#### Send Email

```http
POST /api/v1/notifications/email/send
Authorization: Bearer <token>
Content-Type: application/json

{
  "toEmail": "<EMAIL>",
  "subject": "Trip Completion Confirmation - TLI Transport",
  "htmlContent": "<h1>Trip Completed Successfully</h1><p>Your cargo has been delivered.</p>",
  "textContent": "Trip Completed Successfully. Your cargo has been delivered.",
  "templateId": "trip_completion_email",
  "templateData": {
    "customerName": "Rajesh Kumar",
    "tripId": "TRP-2024-001234",
    "deliveryDate": "2024-01-15T16:30:00Z"
  },
  "attachments": [
    {
      "filename": "delivery_receipt.pdf",
      "content": "base64-encoded-content",
      "contentType": "application/pdf"
    }
  ],
  "ccEmails": ["<EMAIL>"],
  "bccEmails": ["<EMAIL>"],
  "replyTo": "<EMAIL>"
}
```

#### Send Push Notification

```http
POST /api/v1/notifications/push/send
Authorization: Bearer <token>
Content-Type: application/json

{
  "userId": "user-uuid",
  "title": "Trip Update",
  "body": "Your driver is 10 minutes away from pickup location",
  "data": {
    "tripId": "trip-uuid",
    "driverLocation": {
      "latitude": 19.0760,
      "longitude": 72.8777
    },
    "eta": "2024-01-15T10:10:00Z"
  },
  "imageUrl": "https://storage.tli.com/notifications/trip-update.png",
  "clickAction": "OPEN_TRIP_TRACKING",
  "sound": "default",
  "badge": 1,
  "priority": "high",
  "timeToLive": 3600
}
```

### 3. User Preferences

#### Get User Preferences

```http
GET /api/v1/notifications/preferences/{userId}
Authorization: Bearer <token>
```

**Response:**

```json
{
  "userId": "user-uuid",
  "preferences": {
    "channels": {
      "WhatsApp": {
        "enabled": true,
        "phoneNumber": "+91-9876543210",
        "messageTypes": ["TripUpdate", "PaymentNotification", "Emergency"]
      },
      "SMS": {
        "enabled": true,
        "phoneNumber": "+91-9876543210",
        "messageTypes": ["Emergency", "OTP"]
      },
      "Email": {
        "enabled": true,
        "emailAddress": "<EMAIL>",
        "messageTypes": ["TripConfirmation", "Invoice", "Newsletter"]
      },
      "Push": {
        "enabled": true,
        "deviceTokens": ["fcm-token-1", "fcm-token-2"],
        "messageTypes": ["TripUpdate", "Emergency"]
      }
    },
    "language": "English",
    "timezone": "Asia/Kolkata",
    "quietHours": {
      "enabled": true,
      "startTime": "22:00",
      "endTime": "07:00",
      "allowEmergency": true
    },
    "frequency": {
      "marketing": "Weekly",
      "updates": "Immediate",
      "reminders": "Daily"
    }
  },
  "lastUpdated": "2024-01-10T10:00:00Z"
}
```

#### Update User Preferences

```http
PUT /api/v1/notifications/preferences/{userId}
Authorization: Bearer <token>
Content-Type: application/json

{
  "channels": {
    "WhatsApp": {
      "enabled": true,
      "phoneNumber": "+91-9876543210",
      "messageTypes": ["TripUpdate", "PaymentNotification", "Emergency"]
    },
    "Email": {
      "enabled": false
    }
  },
  "language": "Hindi",
  "quietHours": {
    "enabled": true,
    "startTime": "23:00",
    "endTime": "06:00",
    "allowEmergency": true
  }
}
```

### 4. Template Management

#### Get Message Templates

```http
GET /api/v1/notifications/templates
Authorization: Bearer <token>
```

**Query Parameters:**

- `templateType`: Filter by template type
- `language`: Filter by language
- `channel`: Filter by communication channel
- `isActive`: Filter active templates
- `page`: Page number
- `pageSize`: Items per page

**Response:**

```json
{
  "data": [
    {
      "id": "template-uuid",
      "name": "trip_confirmation",
      "displayName": "Trip Confirmation",
      "description": "Template for trip confirmation notifications",
      "templateType": "TripConfirmation",
      "channel": "WhatsApp",
      "language": "English",
      "isActive": true,
      "content": {
        "subject": "Trip Confirmation - {{tripId}}",
        "body": "Hello {{customerName}}, your trip from {{pickupLocation}} to {{deliveryLocation}} on {{tripDate}} has been confirmed.",
        "footer": "Thank you for choosing TLI Transport"
      },
      "parameters": [
        {
          "name": "customerName",
          "type": "text",
          "required": true,
          "description": "Customer's full name"
        },
        {
          "name": "tripId",
          "type": "text",
          "required": true,
          "description": "Trip identifier"
        },
        {
          "name": "pickupLocation",
          "type": "text",
          "required": true,
          "description": "Pickup location"
        },
        {
          "name": "deliveryLocation",
          "type": "text",
          "required": true,
          "description": "Delivery location"
        },
        {
          "name": "tripDate",
          "type": "datetime",
          "required": true,
          "description": "Trip date and time"
        }
      ],
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-10T00:00:00Z"
    }
  ]
}
```

#### Create Message Template

```http
POST /api/v1/notifications/templates
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "payment_reminder",
  "displayName": "Payment Reminder",
  "description": "Template for payment reminder notifications",
  "templateType": "PaymentReminder",
  "channel": "Email",
  "language": "English",
  "content": {
    "subject": "Payment Reminder - Invoice {{invoiceNumber}}",
    "body": "Dear {{customerName}}, this is a reminder that your payment of {{amount}} for invoice {{invoiceNumber}} is due on {{dueDate}}.",
    "footer": "Please contact support if you have any questions."
  },
  "parameters": [
    {
      "name": "customerName",
      "type": "text",
      "required": true,
      "description": "Customer's full name"
    },
    {
      "name": "invoiceNumber",
      "type": "text",
      "required": true,
      "description": "Invoice number"
    },
    {
      "name": "amount",
      "type": "currency",
      "required": true,
      "description": "Payment amount"
    },
    {
      "name": "dueDate",
      "type": "date",
      "required": true,
      "description": "Payment due date"
    }
  ],
  "isActive": true
}
```

#### Update Template

```http
PUT /api/v1/notifications/templates/{templateId}
Authorization: Bearer <token>
Content-Type: application/json

{
  "displayName": "Updated Payment Reminder",
  "content": {
    "subject": "Urgent: Payment Reminder - Invoice {{invoiceNumber}}",
    "body": "Dear {{customerName}}, your payment of {{amount}} for invoice {{invoiceNumber}} is overdue. Please pay by {{dueDate}} to avoid late fees."
  },
  "isActive": true
}
```

### 5. Real-time Chat

#### Get Conversations

```http
GET /api/v1/notifications/chat/conversations
Authorization: Bearer <token>
```

**Query Parameters:**

- `participantId`: Filter by participant
- `status`: Filter by conversation status
- `hasUnreadMessages`: Filter conversations with unread messages
- `page`: Page number
- `pageSize`: Items per page

**Response:**

```json
{
  "data": [
    {
      "id": "conversation-uuid",
      "title": "Trip Support - TRP-2024-001234",
      "type": "Support",
      "status": "Active",
      "participants": [
        {
          "userId": "user-uuid-1",
          "role": "Customer",
          "joinedAt": "2024-01-15T10:00:00Z",
          "isOnline": true,
          "lastSeenAt": "2024-01-15T14:30:00Z"
        },
        {
          "userId": "user-uuid-2",
          "role": "Support",
          "joinedAt": "2024-01-15T10:05:00Z",
          "isOnline": true,
          "lastSeenAt": "2024-01-15T14:35:00Z"
        }
      ],
      "lastMessage": {
        "id": "message-uuid",
        "senderId": "user-uuid-2",
        "content": "I'll check the status of your trip and get back to you shortly.",
        "timestamp": "2024-01-15T14:35:00Z",
        "messageType": "Text"
      },
      "unreadCount": 0,
      "createdAt": "2024-01-15T10:00:00Z",
      "updatedAt": "2024-01-15T14:35:00Z",
      "metadata": {
        "tripId": "trip-uuid",
        "priority": "Normal"
      }
    }
  ]
}
```

#### Get Conversation Messages

```http
GET /api/v1/notifications/chat/conversations/{conversationId}/messages
Authorization: Bearer <token>
```

**Query Parameters:**

- `fromDate`: Filter messages from date
- `toDate`: Filter messages to date
- `messageType`: Filter by message type
- `page`: Page number
- `pageSize`: Items per page

**Response:**

```json
{
  "data": [
    {
      "id": "message-uuid",
      "conversationId": "conversation-uuid",
      "senderId": "user-uuid-1",
      "senderName": "Rajesh Kumar",
      "content": "Hello, I need help with my trip TRP-2024-001234",
      "messageType": "Text",
      "timestamp": "2024-01-15T14:30:00Z",
      "editedAt": null,
      "replyToMessageId": null,
      "attachments": [],
      "reactions": [],
      "isRead": true,
      "readBy": [
        {
          "userId": "user-uuid-2",
          "readAt": "2024-01-15T14:31:00Z"
        }
      ],
      "metadata": {
        "platform": "Web",
        "ipAddress": "***********"
      }
    }
  ]
}
```

#### Send Chat Message

```http
POST /api/v1/notifications/chat/conversations/{conversationId}/messages
Authorization: Bearer <token>
Content-Type: application/json

{
  "content": "I'll check the status of your trip and get back to you shortly.",
  "messageType": "Text",
  "replyToMessageId": "message-uuid",
  "attachments": [
    {
      "fileName": "trip_status.pdf",
      "fileUrl": "https://storage.tli.com/chat/attachment-uuid.pdf",
      "fileSize": 1024000,
      "mimeType": "application/pdf"
    }
  ],
  "metadata": {
    "priority": "Normal"
  }
}
```

### 6. Analytics & Reporting

#### Get Notification Analytics

```http
GET /api/v1/notifications/analytics/summary
Authorization: Bearer <token>
```

**Query Parameters:**

- `fromDate`: Start date for analytics
- `toDate`: End date for analytics
- `channel`: Filter by communication channel
- `messageType`: Filter by message type
- `granularity`: `hourly|daily|weekly|monthly`

**Response:**

```json
{
  "period": {
    "fromDate": "2024-01-01T00:00:00Z",
    "toDate": "2024-01-31T23:59:59Z"
  },
  "totalNotifications": 15000,
  "deliveryRate": 98.5,
  "readRate": 75.2,
  "clickRate": 12.8,
  "byChannel": {
    "WhatsApp": {
      "sent": 8000,
      "delivered": 7920,
      "read": 6400,
      "deliveryRate": 99.0,
      "readRate": 80.0,
      "avgCost": 0.05
    },
    "SMS": {
      "sent": 4000,
      "delivered": 3960,
      "read": 2970,
      "deliveryRate": 99.0,
      "readRate": 75.0,
      "avgCost": 0.03
    },
    "Email": {
      "sent": 2000,
      "delivered": 1940,
      "opened": 1164,
      "clicked": 194,
      "deliveryRate": 97.0,
      "openRate": 60.0,
      "clickRate": 10.0,
      "avgCost": 0.01
    },
    "Push": {
      "sent": 1000,
      "delivered": 980,
      "opened": 490,
      "deliveryRate": 98.0,
      "openRate": 50.0,
      "avgCost": 0.001
    }
  },
  "byMessageType": {
    "TripUpdate": 5000,
    "PaymentNotification": 3000,
    "TripConfirmation": 2500,
    "Emergency": 100,
    "Marketing": 4400
  },
  "trends": [
    {
      "date": "2024-01-01",
      "sent": 500,
      "delivered": 492,
      "read": 369
    }
  ],
  "topPerformingTemplates": [
    {
      "templateId": "template-uuid",
      "templateName": "trip_confirmation",
      "sent": 2500,
      "deliveryRate": 99.2,
      "readRate": 85.0
    }
  ]
}
```

#### Get Channel Performance

```http
GET /api/v1/notifications/analytics/channels
Authorization: Bearer <token>
```

#### Get Cost Analysis

```http
GET /api/v1/notifications/analytics/costs
Authorization: Bearer <token>
```

**Response:**

```json
{
  "period": {
    "fromDate": "2024-01-01T00:00:00Z",
    "toDate": "2024-01-31T23:59:59Z"
  },
  "totalCost": {
    "amount": 520.0,
    "currency": "USD"
  },
  "costByChannel": {
    "WhatsApp": {
      "amount": 400.0,
      "currency": "USD",
      "messageCount": 8000,
      "avgCostPerMessage": 0.05
    },
    "SMS": {
      "amount": 120.0,
      "currency": "USD",
      "messageCount": 4000,
      "avgCostPerMessage": 0.03
    },
    "Email": {
      "amount": 20.0,
      "currency": "USD",
      "messageCount": 2000,
      "avgCostPerMessage": 0.01
    },
    "Push": {
      "amount": 1.0,
      "currency": "USD",
      "messageCount": 1000,
      "avgCostPerMessage": 0.001
    }
  },
  "costTrends": [
    {
      "date": "2024-01-01",
      "cost": 16.8,
      "messageCount": 500
    }
  ],
  "projectedMonthlyCost": {
    "amount": 550.0,
    "currency": "USD"
  }
}
```

## 🔄 Real-time Updates

### SignalR Hub: `/api/v1/notifications/hub`

**Events:**

- `NotificationSent` - Notification sent successfully
- `NotificationDelivered` - Notification delivered to recipient
- `NotificationRead` - Notification read by recipient
- `NotificationFailed` - Notification delivery failed
- `MessageReceived` - New chat message received
- `UserOnline` - User came online
- `UserOffline` - User went offline
- `TypingStarted` - User started typing
- `TypingStopped` - User stopped typing
- `ConversationCreated` - New conversation created
- `ConversationUpdated` - Conversation updated

**Example Usage:**

```typescript
const connection = new signalR.HubConnectionBuilder()
  .withUrl('/api/v1/notifications/hub')
  .build()

// Listen for real-time notifications
connection.on('NotificationDelivered', (data) => {
  console.log('Notification delivered:', data)
  // Update notification status in UI
})

// Listen for chat messages
connection.on('MessageReceived', (data) => {
  console.log('New message:', data)
  // Add message to chat UI
})

// Join conversation for real-time chat
await connection.invoke('JoinConversation', conversationId)

// Send typing indicator
await connection.invoke('StartTyping', conversationId)
```

### Chat Hub Events

```typescript
// Join conversation
await connection.invoke('JoinConversation', 'conversation-uuid')

// Leave conversation
await connection.invoke('LeaveConversation', 'conversation-uuid')

// Send message
await connection.invoke('SendMessage', {
  conversationId: 'conversation-uuid',
  content: 'Hello, how can I help you?',
  messageType: 'Text',
})

// Start typing indicator
await connection.invoke('StartTyping', 'conversation-uuid')

// Stop typing indicator
await connection.invoke('StopTyping', 'conversation-uuid')

// Mark messages as read
await connection.invoke('MarkAsRead', 'conversation-uuid', [
  'message-uuid-1',
  'message-uuid-2',
])
```

## ❌ Error Responses

### Common Error Codes

| Code | Message                       | Description                          |
| ---- | ----------------------------- | ------------------------------------ |
| 400  | `INVALID_PHONE_NUMBER`        | Invalid phone number format          |
| 400  | `INVALID_EMAIL_ADDRESS`       | Invalid email address format         |
| 400  | `TEMPLATE_NOT_FOUND`          | Message template not found           |
| 400  | `INVALID_TEMPLATE_PARAMETERS` | Invalid template parameters          |
| 400  | `CHANNEL_NOT_SUPPORTED`       | Communication channel not supported  |
| 400  | `MESSAGE_TOO_LONG`            | Message content exceeds length limit |
| 401  | `UNAUTHORIZED`                | Authentication required              |
| 403  | `INSUFFICIENT_PERMISSIONS`    | Insufficient permissions             |
| 404  | `NOTIFICATION_NOT_FOUND`      | Notification not found               |
| 404  | `CONVERSATION_NOT_FOUND`      | Conversation not found               |
| 404  | `USER_PREFERENCES_NOT_FOUND`  | User preferences not found           |
| 409  | `CONVERSATION_ALREADY_EXISTS` | Conversation already exists          |
| 422  | `DELIVERY_FAILED`             | Message delivery failed              |
| 422  | `VALIDATION_FAILED`           | Input validation failed              |
| 429  | `RATE_LIMIT_EXCEEDED`         | Too many requests                    |
| 503  | `PROVIDER_UNAVAILABLE`        | Communication provider unavailable   |

### Error Response Format

```json
{
  "error": {
    "code": "DELIVERY_FAILED",
    "message": "Message delivery failed",
    "details": "WhatsApp provider returned error: Invalid phone number",
    "timestamp": "2024-01-01T00:00:00Z",
    "traceId": "trace-uuid",
    "retryable": true,
    "suggestedAction": "Verify phone number format and try again"
  }
}
```

## 📋 Integration Examples

### Frontend Integration Example (React)

```typescript
// Communication Service Client
class CommunicationClient {
  private apiClient = ApiClient.getInstance()

  async sendNotification(
    request: SendNotificationRequest
  ): Promise<Notification> {
    const response = await this.apiClient.axios.post(
      '/notifications/send',
      request
    )
    return response.data
  }

  async getUserNotifications(
    userId: string,
    filters: NotificationFilters
  ): Promise<PagedResult<Notification>> {
    const response = await this.apiClient.axios.get(
      `/notifications/user/${userId}`,
      { params: filters }
    )
    return response.data
  }

  async updateUserPreferences(
    userId: string,
    preferences: UserPreferences
  ): Promise<void> {
    await this.apiClient.axios.put(
      `/notifications/preferences/${userId}`,
      preferences
    )
  }

  async sendChatMessage(
    conversationId: string,
    message: ChatMessage
  ): Promise<ChatMessage> {
    const response = await this.apiClient.axios.post(
      `/notifications/chat/conversations/${conversationId}/messages`,
      message
    )
    return response.data
  }
}

// React Component Example
function NotificationCenter() {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [unreadCount, setUnreadCount] = useState(0)
  const communicationClient = new CommunicationClient()

  // SignalR integration for real-time updates
  const { on } = useSignalR({
    hubName: 'Communication',
    hubUrl: '/api/v1/notifications/hub',
  })

  useEffect(() => {
    const unsubscribe = on(
      'NotificationDelivered',
      (data: NotificationDeliveredEvent) => {
        // Update notification status
        setNotifications((prev) =>
          prev.map((notification) =>
            notification.id === data.notificationId
              ? {
                  ...notification,
                  status: 'Delivered',
                  deliveredAt: data.deliveredAt,
                }
              : notification
          )
        )
      }
    )

    return unsubscribe
  }, [on])

  const handleSendNotification = async (request: SendNotificationRequest) => {
    try {
      const notification = await communicationClient.sendNotification(request)
      setNotifications((prev) => [notification, ...prev])
    } catch (error) {
      console.error('Failed to send notification:', error)
    }
  }

  return (
    <div>
      <h2>Notifications ({unreadCount} unread)</h2>
      {notifications.map((notification) => (
        <NotificationCard key={notification.id} notification={notification} />
      ))}
    </div>
  )
}

// Chat Component Example
function ChatWindow({ conversationId }: { conversationId: string }) {
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [newMessage, setNewMessage] = useState('')
  const [isTyping, setIsTyping] = useState(false)

  const { connection, invoke } = useSignalR({
    hubName: 'Communication',
    hubUrl: '/api/v1/notifications/hub',
  })

  useEffect(() => {
    if (connection) {
      // Join conversation
      invoke('JoinConversation', conversationId)

      // Listen for new messages
      const unsubscribe = connection.on(
        'MessageReceived',
        (message: ChatMessage) => {
          if (message.conversationId === conversationId) {
            setMessages((prev) => [...prev, message])
          }
        }
      )

      // Listen for typing indicators
      connection.on(
        'TypingStarted',
        (data: { userId: string; conversationId: string }) => {
          if (data.conversationId === conversationId) {
            setIsTyping(true)
          }
        }
      )

      connection.on(
        'TypingStopped',
        (data: { userId: string; conversationId: string }) => {
          if (data.conversationId === conversationId) {
            setIsTyping(false)
          }
        }
      )

      return () => {
        unsubscribe()
        invoke('LeaveConversation', conversationId)
      }
    }
  }, [connection, conversationId, invoke])

  const handleSendMessage = async () => {
    if (newMessage.trim()) {
      await invoke('SendMessage', {
        conversationId,
        content: newMessage,
        messageType: 'Text',
      })
      setNewMessage('')
    }
  }

  const handleTyping = () => {
    invoke('StartTyping', conversationId)
    // Stop typing after 3 seconds of inactivity
    setTimeout(() => invoke('StopTyping', conversationId), 3000)
  }

  return (
    <div className="chat-window">
      <div className="messages">
        {messages.map((message) => (
          <MessageBubble key={message.id} message={message} />
        ))}
        {isTyping && <TypingIndicator />}
      </div>
      <div className="message-input">
        <input
          value={newMessage}
          onChange={(e) => {
            setNewMessage(e.target.value)
            handleTyping()
          }}
          onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
          placeholder="Type a message..."
        />
        <button onClick={handleSendMessage}>Send</button>
      </div>
    </div>
  )
}
```
