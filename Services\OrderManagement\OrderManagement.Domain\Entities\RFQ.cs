using OrderManagement.Domain.Primitives;
using Shared.Domain.Common;
using OrderManagement.Domain.Enums;
using OrderManagement.Domain.ValueObjects;
using MediatR;

namespace OrderManagement.Domain.Entities;

public class RFQ : Entity
{
    public Guid TransportCompanyId { get; private set; }
    public string RfqNumber { get; private set; }
    public string Title { get; private set; }
    public string Description { get; private set; }
    public LoadDetails LoadDetails { get; private set; }
    public RouteDetails RouteDetails { get; private set; }
    public RfqRequirements Requirements { get; private set; }
    public RfqStatus Status { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? ExpiresAt { get; private set; }
    public DateTime? ClosedAt { get; private set; }
    public string? ClosureReason { get; private set; }
    public Guid? AwardedBidId { get; private set; }
    public DateTime? AwardedAt { get; private set; }
    public RfqTimeframe? Timeframe { get; private set; }
    public Money? BudgetRange { get; private set; }
    public string? SpecialInstructions { get; private set; }
    public bool IsUrgent { get; private set; }
    public string? ContactPerson { get; private set; }
    public string? ContactPhone { get; private set; }
    public string? ContactEmail { get; private set; }

    // Navigation properties
    private readonly List<RfqBid> _bids = new();
    public IReadOnlyCollection<RfqBid> Bids => _bids.AsReadOnly();

    private readonly List<RfqDocument> _documents = new();
    public IReadOnlyCollection<RfqDocument> Documents => _documents.AsReadOnly();

    private readonly List<TimeframeExtension> _timeframeExtensions = new();
    public IReadOnlyCollection<TimeframeExtension> TimeframeExtensions => _timeframeExtensions.AsReadOnly();

    private readonly List<RfqTimeline> _timelineEvents = new();
    public IReadOnlyCollection<RfqTimeline> TimelineEvents => _timelineEvents.AsReadOnly();

    private readonly List<RfqRoutingHistory> _routingHistory = new();
    public IReadOnlyCollection<RfqRoutingHistory> RoutingHistory => _routingHistory.AsReadOnly();

    private readonly List<RfqTag> _tags = new();
    public IReadOnlyCollection<RfqTag> Tags => _tags.AsReadOnly();

    private readonly List<RfqMilestoneAssignment> _milestoneAssignments = new();
    public IReadOnlyCollection<RfqMilestoneAssignment> MilestoneAssignments => _milestoneAssignments.AsReadOnly();

    private readonly List<BrokerComment> _brokerComments = new();
    public IReadOnlyCollection<BrokerComment> BrokerComments => _brokerComments.AsReadOnly();

    private readonly List<PreferredPartner> _preferredPartners = new();
    public IReadOnlyCollection<PreferredPartner> PreferredPartners => _preferredPartners.AsReadOnly();

    private readonly List<Negotiation> _negotiations = new();
    public IReadOnlyCollection<Negotiation> Negotiations => _negotiations.AsReadOnly();

    private readonly List<QuoteComparison> _quoteComparisons = new();
    public IReadOnlyCollection<QuoteComparison> QuoteComparisons => _quoteComparisons.AsReadOnly();

    // Price expectations and reverse auction
    public PriceExpectations? PriceExpectations { get; private set; }
    public ReverseAuctionSettings? ReverseAuctionSettings { get; private set; }

    private RFQ() { } // EF Constructor

    public RFQ(
        Guid transportCompanyId,
        string title,
        string description,
        LoadDetails loadDetails,
        RouteDetails routeDetails,
        RfqRequirements requirements,
        DateTime? expiresAt = null,
        Money? budgetRange = null,
        string? specialInstructions = null,
        bool isUrgent = false,
        string? contactPerson = null,
        string? contactPhone = null,
        string? contactEmail = null,
        RfqTimeframe? timeframe = null)
    {
        TransportCompanyId = transportCompanyId;
        RfqNumber = GenerateRfqNumber();
        Title = title ?? throw new ArgumentNullException(nameof(title));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        LoadDetails = loadDetails ?? throw new ArgumentNullException(nameof(loadDetails));
        RouteDetails = routeDetails ?? throw new ArgumentNullException(nameof(routeDetails));
        Requirements = requirements ?? throw new ArgumentNullException(nameof(requirements));
        Status = RfqStatus.Draft;
        CreatedAt = DateTime.UtcNow;

        // Set timeframe and expiration
        Timeframe = timeframe;
        ExpiresAt = timeframe?.ExpiresAt ?? expiresAt;

        BudgetRange = budgetRange;
        SpecialInstructions = specialInstructions;
        IsUrgent = isUrgent;
        ContactPerson = contactPerson;
        ContactPhone = contactPhone;
        ContactEmail = contactEmail;
    }

    public void Publish()
    {
        if (Status != RfqStatus.Draft)
            throw new InvalidOperationException("Only draft RFQs can be published");

        Status = RfqStatus.Published;

        // Domain event for RFQ published
        AddDomainEvent(new RfqPublishedDomainEvent(Id, TransportCompanyId, Title));
    }

    public void Close(string reason)
    {
        if (Status == RfqStatus.Closed)
            throw new InvalidOperationException("RFQ is already closed");

        Status = RfqStatus.Closed;
        ClosedAt = DateTime.UtcNow;
        ClosureReason = reason;

        // Domain event for RFQ closed
        AddDomainEvent(new RfqClosedDomainEvent(Id, reason));
    }

    public void AddBid(RfqBid bid)
    {
        if (Status != RfqStatus.Published)
            throw new InvalidOperationException("Can only add bids to published RFQs");

        if (IsExpired())
            throw new InvalidOperationException("Cannot add bid to expired RFQ");

        _bids.Add(bid);
    }

    public void AddDocument(RfqDocument document)
    {
        _documents.Add(document);
    }

    public void AddTimelineEvent(RfqTimeline timelineEvent)
    {
        _timelineEvents.Add(timelineEvent);
    }

    public void AddRoutingHistory(RfqRoutingHistory routingHistory)
    {
        _routingHistory.Add(routingHistory);
    }

    public void AddTag(RfqTag tag)
    {
        _tags.Add(tag);
    }

    public void RemoveTag(Guid tagId, Guid? removedBy = null, string? removalReason = null)
    {
        var tag = _tags.FirstOrDefault(t => t.Id == tagId && t.IsActive);
        tag?.Remove(removedBy, removalReason);
    }

    public bool HasActiveTag(RfqTagType tagType)
    {
        return _tags.Any(t => t.TagType == tagType && t.IsActive);
    }

    public IEnumerable<RfqTag> GetActiveTagsByType(RfqTagType tagType)
    {
        return _tags.Where(t => t.TagType == tagType && t.IsActive);
    }

    // Milestone management methods
    public void AttachMilestoneTemplate(RfqMilestoneAssignment assignment)
    {
        // Deactivate any existing active assignments
        foreach (var existingAssignment in _milestoneAssignments.Where(ma => ma.IsActive))
        {
            existingAssignment.Deactivate(assignment.AssignedBy, "Replaced by new milestone template");
        }

        _milestoneAssignments.Add(assignment);
    }

    public void DetachMilestoneTemplate(Guid assignmentId, Guid deactivatedBy, string reason)
    {
        var assignment = _milestoneAssignments.FirstOrDefault(ma => ma.Id == assignmentId && ma.IsActive);
        assignment?.Deactivate(deactivatedBy, reason);
    }

    public RfqMilestoneAssignment? GetActiveMilestoneAssignment()
    {
        return _milestoneAssignments.FirstOrDefault(ma => ma.IsActive);
    }

    public bool HasActiveMilestoneTemplate()
    {
        return _milestoneAssignments.Any(ma => ma.IsActive);
    }

    // Price expectations methods
    public void SetPriceExpectations(PriceExpectations priceExpectations)
    {
        PriceExpectations = priceExpectations;
    }

    public void ClearPriceExpectations()
    {
        PriceExpectations = null;
    }

    public bool HasPriceExpectations()
    {
        return PriceExpectations != null;
    }

    // Reverse auction methods
    public void EnableReverseAuction(ReverseAuctionSettings settings)
    {
        if (Status != RfqStatus.Published)
            throw new InvalidOperationException("Can only enable reverse auction for published RFQs");

        ReverseAuctionSettings = settings;
    }

    public void DisableReverseAuction()
    {
        ReverseAuctionSettings = null;
    }

    public bool HasReverseAuction()
    {
        return ReverseAuctionSettings?.IsEnabled == true;
    }

    public bool IsReverseAuctionActive()
    {
        return ReverseAuctionSettings?.IsActive() == true;
    }

    // Broker comment methods
    public void AddBrokerComment(BrokerComment comment)
    {
        _brokerComments.Add(comment);
    }

    public IEnumerable<BrokerComment> GetBrokerComments(bool includeInternal = true, bool visibleOnly = true)
    {
        return _brokerComments.Where(c =>
            (!visibleOnly || c.IsVisible) &&
            (includeInternal || !c.IsInternal));
    }

    public IEnumerable<BrokerComment> GetBrokerCommentsByType(BrokerCommentType commentType)
    {
        return _brokerComments.Where(c => c.CommentType == commentType && c.IsVisible);
    }

    public bool HasBrokerComments()
    {
        return _brokerComments.Any(c => c.IsVisible);
    }

    // Administrative override methods
    public void ForceAward(RfqBid bid, Guid adminUserId, string reason)
    {
        if (bid.RfqId != Id)
            throw new InvalidOperationException("Bid does not belong to this RFQ");

        // Store previous state
        var previousStatus = Status;
        var previousAwardedBidId = AwardedBidId;

        // Force award the bid
        Status = RfqStatus.Awarded;
        AwardedBidId = bid.Id;
        AwardedAt = DateTime.UtcNow;

        // Mark the bid as awarded
        bid.Award();

        // Reject all other bids
        foreach (var otherBid in _bids.Where(b => b.Id != bid.Id && b.Status == BidStatus.Submitted))
        {
            otherBid.Reject("RFQ awarded to another bidder (administrative override)");
        }

        // Add domain event
        AddDomainEvent(new Events.RfqForceAwardedDomainEvent(
            Id,
            bid.Id,
            bid.BidderId,
            bid.BidderName,
            adminUserId,
            reason,
            previousStatus,
            previousAwardedBidId));
    }

    public void ResetAward(Guid adminUserId, string reason)
    {
        if (Status != RfqStatus.Awarded)
            throw new InvalidOperationException($"Cannot reset award for RFQ in {Status} status");

        if (!AwardedBidId.HasValue)
            throw new InvalidOperationException("No awarded bid to reset");

        // Store previous state
        var previousAwardedBidId = AwardedBidId;
        var previousAwardedBid = _bids.FirstOrDefault(b => b.Id == AwardedBidId.Value);

        // Reset the award
        Status = RfqStatus.Published;
        AwardedBidId = null;
        AwardedAt = null;

        // Reset the previously awarded bid status
        if (previousAwardedBid != null)
        {
            previousAwardedBid.ResetFromAwarded("Award reset by administrator");
        }

        // Reactivate other submitted bids
        foreach (var bid in _bids.Where(b => b.Status == BidStatus.Rejected))
        {
            bid.Reactivate("RFQ award reset - bidding reopened");
        }

        // Add domain event
        AddDomainEvent(new Events.RfqAwardResetDomainEvent(
            Id,
            previousAwardedBidId.Value,
            previousAwardedBid?.BidderId ?? Guid.Empty,
            previousAwardedBid?.BidderName ?? "Unknown",
            adminUserId,
            reason));
    }

    public void Republish(Guid republishedBy)
    {
        if (Status != RfqStatus.Published)
        {
            Status = RfqStatus.Published;
            // Could add a republished timestamp if needed
        }

        // Add domain event
        AddDomainEvent(new Events.RfqRepublishedDomainEvent(Id, Title, republishedBy));
    }

    /// <summary>
    /// Check if the RFQ has expired based on timeframe or explicit expiration date
    /// </summary>
    public bool IsExpired()
    {
        if (Timeframe != null)
            return Timeframe.IsExpired;

        return ExpiresAt.HasValue && DateTime.UtcNow > ExpiresAt.Value;
    }

    /// <summary>
    /// Check if the RFQ is approaching expiration
    /// </summary>
    public bool IsApproachingExpiration()
    {
        if (Timeframe != null)
            return Timeframe.IsApproachingExpiration;

        return ExpiresAt.HasValue && DateTime.UtcNow > ExpiresAt.Value.AddHours(-24);
    }

    /// <summary>
    /// Check if the RFQ is expiring soon within specified hours
    /// </summary>
    public bool IsExpiringSoon(int hoursBeforeExpiration)
    {
        if (Timeframe != null)
            return Timeframe.IsExpiringSoon(hoursBeforeExpiration);

        return ExpiresAt.HasValue && DateTime.UtcNow > ExpiresAt.Value.AddHours(-hoursBeforeExpiration);
    }

    /// <summary>
    /// Expire the RFQ automatically
    /// </summary>
    public void Expire()
    {
        if (Status == RfqStatus.Expired)
            throw new InvalidOperationException("RFQ is already expired");

        if (Status == RfqStatus.Closed)
            throw new InvalidOperationException("Cannot expire a closed RFQ");

        Status = RfqStatus.Expired;
        ClosedAt = DateTime.UtcNow;
        ClosureReason = "Automatically expired due to timeframe";

        // Domain event for RFQ expired
        AddDomainEvent(new RfqExpiredDomainEvent(Id, TransportCompanyId, Title, ExpiresAt));
    }

    /// <summary>
    /// Extend the RFQ timeframe
    /// </summary>
    public void ExtendTimeframe(int duration, TimeframeUnit unit, string reason, Guid extendedBy)
    {
        if (Status == RfqStatus.Closed || Status == RfqStatus.Expired)
            throw new InvalidOperationException("Cannot extend timeframe of closed or expired RFQ");

        if (Timeframe == null)
            throw new InvalidOperationException("Cannot extend RFQ without timeframe configuration");

        if (!Timeframe.CanExtend)
            throw new InvalidOperationException("Cannot extend RFQ timeframe - maximum extensions reached or extensions not allowed");

        var extension = new TimeframeExtension(duration, unit, reason, extendedBy, Timeframe.ExpiresAt.AddMinutes(
            unit switch
            {
                TimeframeUnit.Minutes => duration,
                TimeframeUnit.Hours => duration * 60,
                TimeframeUnit.Days => duration * 24 * 60,
                _ => throw new ArgumentOutOfRangeException(nameof(unit))
            }));

        _timeframeExtensions.Add(extension);
        Timeframe = Timeframe.Extend(duration, unit);
        ExpiresAt = Timeframe.ExpiresAt;

        // Domain event for RFQ timeframe extended
        AddDomainEvent(new RfqTimeframeExtendedDomainEvent(Id, TransportCompanyId, extension, Timeframe.ExpiresAt));
    }

    /// <summary>
    /// Trigger approaching expiration notification
    /// </summary>
    public void TriggerApproachingExpirationNotification()
    {
        if (Status != RfqStatus.Published)
            return;

        var remainingTime = Timeframe?.RemainingTime ?? (ExpiresAt - DateTime.UtcNow) ?? TimeSpan.Zero;

        // Domain event for RFQ approaching expiration
        AddDomainEvent(new RfqApproachingExpirationDomainEvent(Id, TransportCompanyId, Title, ExpiresAt ?? DateTime.UtcNow, remainingTime));
    }

    // Preferred Partner Management
    public void AddPreferredPartner(PreferredPartner preferredPartner)
    {
        if (preferredPartner == null)
            throw new ArgumentNullException(nameof(preferredPartner));

        if (_preferredPartners.Any(pp => pp.PartnerId == preferredPartner.PartnerId))
            throw new InvalidOperationException("Partner is already in the preferred list");

        _preferredPartners.Add(preferredPartner);
    }

    public void RemovePreferredPartner(Guid partnerId)
    {
        var partner = _preferredPartners.FirstOrDefault(pp => pp.PartnerId == partnerId);
        if (partner != null)
        {
            partner.Deactivate();
        }
    }

    public void UpdatePreferredPartnerRating(Guid partnerId, decimal newRating)
    {
        var partner = _preferredPartners.FirstOrDefault(pp => pp.PartnerId == partnerId && pp.IsActive);
        if (partner == null)
            throw new ArgumentException("Preferred partner not found", nameof(partnerId));

        partner.UpdateRating(newRating);
    }

    public bool HasPreferredPartner(Guid partnerId)
    {
        return _preferredPartners.Any(pp => pp.PartnerId == partnerId && pp.IsActive);
    }

    public List<PreferredPartner> GetActivePreferredPartners()
    {
        return _preferredPartners.Where(pp => pp.IsActive).OrderBy(pp => pp.Priority).ToList();
    }

    // Negotiation Management
    public Negotiation StartNegotiation(Guid brokerId, Guid originalBidId, Money originalPrice, Guid startedBy, string? notes = null)
    {
        var existingNegotiation = _negotiations.FirstOrDefault(n => n.BrokerId == brokerId && n.IsActive);
        if (existingNegotiation != null)
            throw new InvalidOperationException("Active negotiation already exists with this broker");

        var negotiation = new Negotiation(Id, brokerId, originalBidId, originalPrice, startedBy, notes);
        _negotiations.Add(negotiation);

        return negotiation;
    }

    public Negotiation? GetActiveNegotiation(Guid brokerId)
    {
        return _negotiations.FirstOrDefault(n => n.BrokerId == brokerId && n.IsActive);
    }

    public List<Negotiation> GetAllNegotiations()
    {
        return _negotiations.ToList();
    }

    // Quote Comparison Management
    public QuoteComparison CreateQuoteComparison(Guid generatedBy, List<RfqBid> bids, List<string> comparisonCriteria, string? notes = null)
    {
        var comparison = new QuoteComparison(Id, generatedBy, bids, comparisonCriteria, notes);
        _quoteComparisons.Add(comparison);

        return comparison;
    }

    public QuoteComparison? GetLatestQuoteComparison()
    {
        return _quoteComparisons.OrderByDescending(qc => qc.GeneratedAt).FirstOrDefault();
    }

    public List<QuoteComparison> GetAllQuoteComparisons()
    {
        return _quoteComparisons.ToList();
    }

    private static string GenerateRfqNumber()
    {
        return $"RFQ-{DateTime.UtcNow:yyyyMMdd}-{Guid.NewGuid().ToString("N")[..8].ToUpper()}";
    }
}

// Domain Events
public record RfqPublishedDomainEvent(Guid RfqId, Guid TransportCompanyId, string Title) : IDomainEvent, INotification
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}
public record RfqClosedDomainEvent(Guid RfqId, string Reason) : IDomainEvent, INotification
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}
public record RfqExpiredDomainEvent(Guid RfqId, Guid TransportCompanyId, string Title, DateTime? ExpiresAt) : IDomainEvent, INotification
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}
public record RfqTimeframeExtendedDomainEvent(Guid RfqId, Guid TransportCompanyId, TimeframeExtension Extension, DateTime NewExpiresAt) : IDomainEvent, INotification
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}
public record RfqApproachingExpirationDomainEvent(Guid RfqId, Guid TransportCompanyId, string Title, DateTime ExpiresAt, TimeSpan RemainingTime) : IDomainEvent, INotification
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}
public record RfqForceAwardedDomainEvent(Guid RfqId, Guid AwardedBidId, Guid AwardedBidderId, string AwardedBidderName, Guid AdminUserId, string ForceAwardReason, RfqStatus PreviousStatus, Guid? PreviousAwardedBidId) : IDomainEvent, INotification
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}


