using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Enums;
using System.Diagnostics.Metrics;

namespace SubscriptionManagement.Infrastructure.Monitoring
{
    public class MetricsService : IMetricsService
    {
        private readonly ILogger<MetricsService> _logger;
        private readonly Meter _meter;
        
        // Counters
        private readonly Counter<long> _paymentProofUploadsCounter;
        private readonly Counter<long> _paymentProofVerificationsCounter;
        private readonly Counter<long> _notificationsSentCounter;
        private readonly Counter<long> _notificationFailuresCounter;
        private readonly Counter<long> _bulkNotificationProcessedCounter;
        
        // Histograms
        private readonly Histogram<double> _paymentVerificationDuration;
        private readonly Histogram<double> _notificationDeliveryDuration;
        private readonly Histogram<double> _bulkNotificationProcessingDuration;
        
        // Gauges (using UpDownCounter as .NET doesn't have native Gauge)
        private readonly UpDownCounter<long> _pendingPaymentProofsGauge;
        private readonly UpDownCounter<long> _pendingNotificationsGauge;

        public MetricsService(ILogger<MetricsService> logger)
        {
            _logger = logger;
            _meter = new Meter("SubscriptionManagement", "1.0.0");
            
            // Initialize counters
            _paymentProofUploadsCounter = _meter.CreateCounter<long>(
                "subscription_payment_proof_uploads_total",
                description: "Total number of payment proof uploads");
                
            _paymentProofVerificationsCounter = _meter.CreateCounter<long>(
                "subscription_payment_proof_verifications_total",
                description: "Total number of payment proof verifications");
                
            _notificationsSentCounter = _meter.CreateCounter<long>(
                "subscription_notifications_sent_total",
                description: "Total number of notifications sent");
                
            _notificationFailuresCounter = _meter.CreateCounter<long>(
                "subscription_notification_failures_total",
                description: "Total number of notification failures");
                
            _bulkNotificationProcessedCounter = _meter.CreateCounter<long>(
                "subscription_bulk_notifications_processed_total",
                description: "Total number of bulk notifications processed");
            
            // Initialize histograms
            _paymentVerificationDuration = _meter.CreateHistogram<double>(
                "subscription_payment_verification_duration_seconds",
                description: "Duration of payment verification process in seconds");
                
            _notificationDeliveryDuration = _meter.CreateHistogram<double>(
                "subscription_notification_delivery_duration_seconds",
                description: "Duration of notification delivery in seconds");
                
            _bulkNotificationProcessingDuration = _meter.CreateHistogram<double>(
                "subscription_bulk_notification_processing_duration_seconds",
                description: "Duration of bulk notification processing in seconds");
            
            // Initialize gauges
            _pendingPaymentProofsGauge = _meter.CreateUpDownCounter<long>(
                "subscription_pending_payment_proofs",
                description: "Number of pending payment proofs");
                
            _pendingNotificationsGauge = _meter.CreateUpDownCounter<long>(
                "subscription_pending_notifications",
                description: "Number of pending notifications");
        }

        public void RecordPaymentProofUpload(PaymentProofStatus status, string? paymentMethod = null)
        {
            var tags = new TagList
            {
                ["status"] = status.ToString(),
                ["payment_method"] = paymentMethod ?? "unknown"
            };
            
            _paymentProofUploadsCounter.Add(1, tags);
            _logger.LogInformation("Payment proof upload recorded: Status={Status}, PaymentMethod={PaymentMethod}", 
                status, paymentMethod);
        }

        public void RecordPaymentProofVerification(PaymentProofStatus fromStatus, PaymentProofStatus toStatus, double durationSeconds)
        {
            var tags = new TagList
            {
                ["from_status"] = fromStatus.ToString(),
                ["to_status"] = toStatus.ToString()
            };
            
            _paymentProofVerificationsCounter.Add(1, tags);
            _paymentVerificationDuration.Record(durationSeconds, tags);
            
            _logger.LogInformation("Payment proof verification recorded: {FromStatus} -> {ToStatus}, Duration={Duration}s", 
                fromStatus, toStatus, durationSeconds);
        }

        public void RecordNotificationSent(NotificationType type, string channel, bool success, double? durationSeconds = null)
        {
            var tags = new TagList
            {
                ["type"] = type.ToString(),
                ["channel"] = channel,
                ["success"] = success.ToString()
            };
            
            if (success)
            {
                _notificationsSentCounter.Add(1, tags);
                if (durationSeconds.HasValue)
                {
                    _notificationDeliveryDuration.Record(durationSeconds.Value, tags);
                }
            }
            else
            {
                _notificationFailuresCounter.Add(1, tags);
            }
            
            _logger.LogInformation("Notification recorded: Type={Type}, Channel={Channel}, Success={Success}, Duration={Duration}s", 
                type, channel, success, durationSeconds);
        }

        public void RecordBulkNotificationProcessing(int totalNotifications, int successCount, int failureCount, double durationSeconds)
        {
            var tags = new TagList
            {
                ["total"] = totalNotifications.ToString(),
                ["success_count"] = successCount.ToString(),
                ["failure_count"] = failureCount.ToString()
            };
            
            _bulkNotificationProcessedCounter.Add(totalNotifications, tags);
            _bulkNotificationProcessingDuration.Record(durationSeconds, tags);
            
            _logger.LogInformation("Bulk notification processing recorded: Total={Total}, Success={Success}, Failures={Failures}, Duration={Duration}s", 
                totalNotifications, successCount, failureCount, durationSeconds);
        }

        public void UpdatePendingPaymentProofs(int count)
        {
            // Reset and set the new value
            _pendingPaymentProofsGauge.Add(count);
            _logger.LogDebug("Updated pending payment proofs gauge: {Count}", count);
        }

        public void UpdatePendingNotifications(int count)
        {
            // Reset and set the new value
            _pendingNotificationsGauge.Add(count);
            _logger.LogDebug("Updated pending notifications gauge: {Count}", count);
        }

        public void RecordConversionEvent(NotificationType notificationType, string channel, bool converted, TimeSpan timeSinceNotification)
        {
            var tags = new TagList
            {
                ["notification_type"] = notificationType.ToString(),
                ["channel"] = channel,
                ["converted"] = converted.ToString()
            };
            
            var conversionCounter = _meter.CreateCounter<long>(
                "subscription_conversion_events_total",
                description: "Total number of conversion events tracked");
                
            var conversionTimeHistogram = _meter.CreateHistogram<double>(
                "subscription_conversion_time_seconds",
                description: "Time from notification to conversion in seconds");
            
            conversionCounter.Add(1, tags);
            
            if (converted)
            {
                conversionTimeHistogram.Record(timeSinceNotification.TotalSeconds, tags);
            }
            
            _logger.LogInformation("Conversion event recorded: Type={Type}, Channel={Channel}, Converted={Converted}, Time={Time}s", 
                notificationType, channel, converted, timeSinceNotification.TotalSeconds);
        }

        public void RecordApiRequest(string endpoint, string method, int statusCode, double durationMs)
        {
            var tags = new TagList
            {
                ["endpoint"] = endpoint,
                ["method"] = method,
                ["status_code"] = statusCode.ToString()
            };
            
            var apiRequestCounter = _meter.CreateCounter<long>(
                "subscription_api_requests_total",
                description: "Total number of API requests");
                
            var apiRequestDuration = _meter.CreateHistogram<double>(
                "subscription_api_request_duration_milliseconds",
                description: "Duration of API requests in milliseconds");
            
            apiRequestCounter.Add(1, tags);
            apiRequestDuration.Record(durationMs, tags);
        }

        public void RecordCacheHit(string cacheKey, bool hit)
        {
            var tags = new TagList
            {
                ["cache_key"] = cacheKey,
                ["hit"] = hit.ToString()
            };
            
            var cacheCounter = _meter.CreateCounter<long>(
                "subscription_cache_operations_total",
                description: "Total number of cache operations");
            
            cacheCounter.Add(1, tags);
        }

        public void RecordDatabaseQuery(string operation, string table, double durationMs, bool success)
        {
            var tags = new TagList
            {
                ["operation"] = operation,
                ["table"] = table,
                ["success"] = success.ToString()
            };
            
            var dbQueryCounter = _meter.CreateCounter<long>(
                "subscription_database_queries_total",
                description: "Total number of database queries");
                
            var dbQueryDuration = _meter.CreateHistogram<double>(
                "subscription_database_query_duration_milliseconds",
                description: "Duration of database queries in milliseconds");
            
            dbQueryCounter.Add(1, tags);
            dbQueryDuration.Record(durationMs, tags);
        }

        public void Dispose()
        {
            _meter?.Dispose();
        }
    }
}
