using Shared.Domain.Common;

namespace NetworkFleetManagement.Application.Queries.Drivers.GetEnhancedDriverProfile;

public class GetEnhancedDriverProfileQuery : IRequest<GetEnhancedDriverProfileResponse>
{
    public Guid DriverId { get; set; }
    public bool IncludeEntityDetails { get; set; } = true;
    public bool IncludePerformanceMetrics { get; set; } = true;
    public bool IncludeDocumentStatus { get; set; } = true;
    public bool IncludeVehicleAssignments { get; set; } = true;
    public bool IncludeLocationHistory { get; set; } = false;
    public bool IncludeTripHistory { get; set; } = false;
    public int HistoryDays { get; set; } = 30;
}

public class GetEnhancedDriverProfileResponse
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public EnhancedDriverProfileDto? Profile { get; set; }
}

public class EnhancedDriverProfileDto
{
    // Basic Information
    public Guid Id { get; set; }
    public Guid UserId { get; set; }
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string FullName { get; set; } = string.Empty;
    public string PhoneNumber { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string? ProfilePhotoUrl { get; set; }
    
    // License Information
    public string LicenseNumber { get; set; } = string.Empty;
    public DateTime LicenseExpiryDate { get; set; }
    public bool IsLicenseValid { get; set; }
    public int LicenseExpiryDays { get; set; }
    
    // Identity Documents
    public string? AadharNumber { get; set; }
    public string? PANNumber { get; set; }
    
    // Status Information
    public string Status { get; set; } = string.Empty;
    public string OnboardingStatus { get; set; } = string.Empty;
    public bool IsVerified { get; set; }
    public DateTime? VerifiedAt { get; set; }
    public bool IsAvailable { get; set; }
    public DateTime? LastActiveAt { get; set; }
    
    // Entity Assignment Information
    public EntityAssignmentInfo EntityAssignment { get; set; } = new();
    
    // Performance Metrics
    public DriverPerformanceMetricsDto PerformanceMetrics { get; set; } = new();
    
    // Document Status
    public DocumentStatusSummary DocumentStatus { get; set; } = new();
    
    // Vehicle Assignments
    public List<VehicleAssignmentDto> VehicleAssignments { get; set; } = new();
    
    // Location Information
    public CurrentLocationInfo? CurrentLocation { get; set; }
    public List<LocationHistoryDto> LocationHistory { get; set; } = new();
    
    // Trip Information
    public CurrentTripInfo? CurrentTrip { get; set; }
    public List<RecentTripDto> RecentTrips { get; set; } = new();
    
    // Preferences and Settings
    public DriverPreferencesDto Preferences { get; set; } = new();
    
    // Operational Information
    public List<string> PreferredRoutes { get; set; } = new();
    public List<string> OperationalAreas { get; set; } = new();
    public string? Notes { get; set; }
    
    // Timestamps
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

public class EntityAssignmentInfo
{
    public Guid CarrierId { get; set; }
    public string CarrierName { get; set; } = string.Empty;
    public string CarrierCompanyName { get; set; } = string.Empty;
    public string CarrierType { get; set; } = string.Empty;
    public string CarrierStatus { get; set; } = string.Empty;
    public ContactInfo CarrierContact { get; set; } = new();
    public DateTime AssignedAt { get; set; }
    public bool IsActiveAssignment { get; set; }
    public string? AssignmentNotes { get; set; }
    
    // Fleet Information (if applicable)
    public Guid? FleetId { get; set; }
    public string? FleetName { get; set; }
    public string? FleetManager { get; set; }
    public ContactInfo? FleetContact { get; set; }
    
    // Hierarchy Information
    public List<EntityHierarchyLevel> EntityHierarchy { get; set; } = new();
}

public class ContactInfo
{
    public string? PrimaryPhone { get; set; }
    public string? SecondaryPhone { get; set; }
    public string? Email { get; set; }
    public string? Address { get; set; }
    public string? City { get; set; }
    public string? State { get; set; }
    public string? Country { get; set; }
    public string? PostalCode { get; set; }
}

public class EntityHierarchyLevel
{
    public string Level { get; set; } = string.Empty; // Company, Division, Fleet, Team
    public string Name { get; set; } = string.Empty;
    public string? Manager { get; set; }
    public string? Contact { get; set; }
}

public class DriverPerformanceMetricsDto
{
    public double AverageRating { get; set; }
    public int TotalTrips { get; set; }
    public int CompletedTrips { get; set; }
    public int CancelledTrips { get; set; }
    public double OnTimePerformance { get; set; }
    public double SafetyScore { get; set; }
    public double FuelEfficiency { get; set; }
    public decimal TotalEarnings { get; set; }
    public DateTime LastUpdated { get; set; }
    public string PerformanceGrade { get; set; } = string.Empty; // A, B, C, D, F
    public List<PerformanceAlert> Alerts { get; set; } = new();
}

public class PerformanceAlert
{
    public string Type { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
}

public class DocumentStatusSummary
{
    public int TotalDocuments { get; set; }
    public int ValidDocuments { get; set; }
    public int ExpiredDocuments { get; set; }
    public int ExpiringDocuments { get; set; } // Within 30 days
    public List<DocumentStatusDto> Documents { get; set; } = new();
    public DateTime LastUpdated { get; set; }
}

public class DocumentStatusDto
{
    public Guid Id { get; set; }
    public string DocumentType { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime? ExpiryDate { get; set; }
    public int? DaysUntilExpiry { get; set; }
    public bool IsExpired { get; set; }
    public bool IsExpiringSoon { get; set; }
    public string? DocumentUrl { get; set; }
}

public class VehicleAssignmentDto
{
    public Guid VehicleId { get; set; }
    public string VehicleNumber { get; set; } = string.Empty;
    public string Make { get; set; } = string.Empty;
    public string Model { get; set; } = string.Empty;
    public string VehicleType { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public DateTime AssignedAt { get; set; }
    public DateTime? UnassignedAt { get; set; }
    public string? AssignmentNotes { get; set; }
}

public class CurrentLocationInfo
{
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public string? Address { get; set; }
    public DateTime Timestamp { get; set; }
    public double? Accuracy { get; set; }
    public bool IsLocationSharingEnabled { get; set; }
    public TimeSpan TimeSinceLastUpdate { get; set; }
}

public class LocationHistoryDto
{
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public string? Address { get; set; }
    public DateTime Timestamp { get; set; }
    public double? Speed { get; set; }
    public string Source { get; set; } = string.Empty;
}

public class CurrentTripInfo
{
    public Guid TripId { get; set; }
    public string TripNumber { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string Origin { get; set; } = string.Empty;
    public string Destination { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime? EstimatedEndTime { get; set; }
    public double? ProgressPercentage { get; set; }
}

public class RecentTripDto
{
    public Guid TripId { get; set; }
    public string TripNumber { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public decimal? Earnings { get; set; }
    public double? Rating { get; set; }
}

public class DriverPreferencesDto
{
    public string PreferredLanguage { get; set; } = "en";
    public string TimeZone { get; set; } = "UTC";
    public bool LocationSharingEnabled { get; set; } = true;
    public bool NotificationsEnabled { get; set; } = true;
    public Dictionary<string, object> CustomSettings { get; set; } = new();
}

