using MediatR;
using OrderManagement.Application.Interfaces;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.DTOs;
using OrderManagement.Domain.Enums;


namespace OrderManagement.Application.Queries.GetBrokerQuoteAnalytics;

public class GetBrokerQuoteAnalyticsQueryHandler : IRequestHandler<GetBrokerQuoteAnalyticsQuery, BrokerQuoteAnalyticsDto>
{
    private readonly IRfqBidRepository _rfqBidRepository;
    private readonly IOrderRepository _orderRepository;
    private readonly ILogger<GetBrokerQuoteAnalyticsQueryHandler> _logger;

    public GetBrokerQuoteAnalyticsQueryHandler(
        IRfqBidRepository rfqBidRepository,
        IOrderRepository orderRepository,
        ILogger<GetBrokerQuoteAnalyticsQueryHandler> logger)
    {
        _rfqBidRepository = rfqBidRepository;
        _orderRepository = orderRepository;
        _logger = logger;
    }

    public async Task<BrokerQuoteAnalyticsDto> Handle(GetBrokerQuoteAnalyticsQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting quote analytics for broker {BrokerId} from {FromDate} to {ToDate}",
            request.BrokerId, request.FromDate, request.ToDate);

        // Get filtered bids using repository
        var bids = await _rfqBidRepository.GetBrokerBidsWithFiltersAsync(
            request.BrokerId,
            request.FromDate,
            request.ToDate,
            request.CarrierIds,
            request.LoadTypes,
            request.Routes,
            cancellationToken);

        // Calculate main metrics
        var totalQuotes = bids.Count;
        var acceptedQuotes = bids.Count(b => b.Status == BidStatus.Accepted);
        var rejectedQuotes = bids.Count(b => b.Status == BidStatus.Rejected);
        var pendingQuotes = bids.Count(b => b.Status == BidStatus.Pending);

        var totalQuotedValue = bids.Sum(b => b.QuotedPrice.Amount);
        var acceptedQuotesValue = bids.Where(b => b.Status == BidStatus.Accepted).Sum(b => b.QuotedPrice.Amount);

        var analytics = new BrokerQuoteAnalyticsDto
        {
            BrokerId = request.BrokerId,
            AnalysisPeriodStart = request.FromDate ?? DateTime.MinValue,
            AnalysisPeriodEnd = request.ToDate ?? DateTime.MaxValue,
            TotalQuotesSubmitted = totalQuotes,
            QuotesAccepted = acceptedQuotes,
            QuotesRejected = rejectedQuotes,
            QuotesPending = pendingQuotes,
            AcceptanceRate = totalQuotes > 0 ? (decimal)acceptedQuotes / totalQuotes * 100 : 0,
            RejectionRate = totalQuotes > 0 ? (decimal)rejectedQuotes / totalQuotes * 100 : 0,
            TotalQuotedValue = new MoneyDto { Amount = totalQuotedValue, Currency = "INR" },
            AcceptedQuotesValue = new MoneyDto { Amount = acceptedQuotesValue, Currency = "INR" },
            AverageQuoteValue = new MoneyDto { Amount = totalQuotes > 0 ? totalQuotedValue / totalQuotes : 0, Currency = "INR" },
            AverageAcceptedQuoteValue = new MoneyDto { Amount = acceptedQuotes > 0 ? acceptedQuotesValue / acceptedQuotes : 0, Currency = "INR" },
            ConversionRate = totalQuotes > 0 ? (decimal)acceptedQuotes / totalQuotes * 100 : 0
        };

        // Calculate average response time
        var responseTimes = bids.Where(b => b.Rfq.CreatedAt < b.SubmittedAt)
            .Select(b => b.SubmittedAt - b.Rfq.CreatedAt)
            .ToList();

        if (responseTimes.Any())
        {
            analytics.AverageResponseTime = TimeSpan.FromTicks((long)responseTimes.Average(t => t.Ticks));
        }

        // Analytics by carrier (if requested)
        if (request.IncludeCarrierBreakdown)
        {
            analytics.AnalyticsByCarrier = await GetAnalyticsByCarrier(bids, cancellationToken);
        }

        // Daily analytics (if requested)
        if (request.IncludeDailyBreakdown)
        {
            analytics.DailyAnalytics = GetDailyAnalytics(bids);
        }

        // Analytics by load type (if requested)
        if (request.IncludeLoadTypeBreakdown)
        {
            analytics.AnalyticsByLoadType = GetAnalyticsByLoadType(bids);
        }

        // Analytics by route (if requested)
        if (request.IncludeRouteBreakdown)
        {
            analytics.AnalyticsByRoute = GetAnalyticsByRoute(bids);
        }

        // Top performing routes (if requested)
        if (request.IncludeTopPerformingRoutes)
        {
            analytics.TopPerformingRoutes = await GetTopPerformingRoutes(request.BrokerId, request.TopRoutesLimit, cancellationToken);
        }

        _logger.LogInformation("Successfully calculated quote analytics for broker {BrokerId}. Total quotes: {TotalQuotes}, Acceptance rate: {AcceptanceRate}%",
            request.BrokerId, totalQuotes, analytics.AcceptanceRate);

        return analytics;
    }

    private async Task<List<QuoteAnalyticsByCarrierDto>> GetAnalyticsByCarrier(List<Domain.Entities.RfqBid> bids, CancellationToken cancellationToken)
    {
        // Get carrier information for accepted bids
        var acceptedBidIds = bids.Where(b => b.Status == BidStatus.Accepted).Select(b => b.Id).ToList();
        var allOrders = await _orderRepository.GetAllAsync(cancellationToken);
        var orders = allOrders.Where(o => o.AcceptedBidId.HasValue && acceptedBidIds.Contains(o.AcceptedBidId.Value)).ToList();

        var carrierAnalytics = new List<QuoteAnalyticsByCarrierDto>();

        foreach (var carrierGroup in orders.GroupBy(o => o.CarrierId))
        {
            if (!carrierGroup.Key.HasValue) continue;

            var carrierId = carrierGroup.Key.Value;
            var carrierBids = bids.Where(b => orders.Any(o => o.AcceptedBidId == b.Id && o.CarrierId == carrierId)).ToList();
            var totalCarrierQuotes = carrierBids.Count;
            var acceptedCarrierQuotes = carrierBids.Count(b => b.Status == BidStatus.Accepted);

            var responseTimes = carrierBids.Where(b => b.Rfq.CreatedAt < b.SubmittedAt)
                .Select(b => b.SubmittedAt - b.Rfq.CreatedAt)
                .ToList();

            carrierAnalytics.Add(new QuoteAnalyticsByCarrierDto
            {
                CarrierId = carrierId,
                CarrierName = $"Carrier-{carrierId}", // TODO: Get actual carrier name from carrier service
                TotalQuotes = totalCarrierQuotes,
                AcceptedQuotes = acceptedCarrierQuotes,
                AcceptanceRate = totalCarrierQuotes > 0 ? (decimal)acceptedCarrierQuotes / totalCarrierQuotes * 100 : 0,
                TotalValue = new MoneyDto { Amount = carrierBids.Sum(b => b.QuotedPrice.Amount), Currency = "INR" },
                AverageQuoteValue = new MoneyDto { Amount = totalCarrierQuotes > 0 ? carrierBids.Sum(b => b.QuotedPrice.Amount) / totalCarrierQuotes : 0, Currency = "INR" },
                AverageResponseTime = responseTimes.Any() ? TimeSpan.FromTicks((long)responseTimes.Average(t => t.Ticks)) : TimeSpan.Zero
            });
        }

        return carrierAnalytics.OrderByDescending(c => c.AcceptanceRate).ToList();
    }

    private List<QuoteAnalyticsByDateDto> GetDailyAnalytics(List<Domain.Entities.RfqBid> bids)
    {
        return bids.GroupBy(b => b.SubmittedAt.Date)
            .Select(g => new QuoteAnalyticsByDateDto
            {
                Date = g.Key,
                QuotesSubmitted = g.Count(),
                QuotesAccepted = g.Count(b => b.Status == BidStatus.Accepted),
                AcceptanceRate = g.Count() > 0 ? (decimal)g.Count(b => b.Status == BidStatus.Accepted) / g.Count() * 100 : 0,
                TotalValue = new MoneyDto { Amount = g.Sum(b => b.QuotedPrice.Amount), Currency = "INR" }
            })
            .OrderBy(d => d.Date)
            .ToList();
    }

    private List<QuoteAnalyticsByLoadTypeDto> GetAnalyticsByLoadType(List<Domain.Entities.RfqBid> bids)
    {
        return bids.GroupBy(b => b.Rfq.LoadDetails.LoadType)
            .Select(g => new QuoteAnalyticsByLoadTypeDto
            {
                LoadType = g.Key,
                TotalQuotes = g.Count(),
                AcceptedQuotes = g.Count(b => b.Status == BidStatus.Accepted),
                AcceptanceRate = g.Count() > 0 ? (decimal)g.Count(b => b.Status == BidStatus.Accepted) / g.Count() * 100 : 0,
                AverageQuoteValue = new MoneyDto { Amount = g.Count() > 0 ? g.Sum(b => b.QuotedPrice.Amount) / g.Count() : 0, Currency = "INR" }
            })
            .OrderByDescending(l => l.AcceptanceRate)
            .ToList();
    }

    private List<QuoteAnalyticsByRouteDto> GetAnalyticsByRoute(List<Domain.Entities.RfqBid> bids)
    {
        return bids.GroupBy(b => new
        {
            PickupCity = b.Rfq.RouteDetails.PickupAddress.City,
            PickupState = b.Rfq.RouteDetails.PickupAddress.State,
            DeliveryCity = b.Rfq.RouteDetails.DeliveryAddress.City,
            DeliveryState = b.Rfq.RouteDetails.DeliveryAddress.State
        })
            .Select(g => new QuoteAnalyticsByRouteDto
            {
                Route = $"{g.Key.PickupCity}, {g.Key.PickupState} → {g.Key.DeliveryCity}, {g.Key.DeliveryState}",
                PickupCity = g.Key.PickupCity,
                PickupState = g.Key.PickupState,
                DeliveryCity = g.Key.DeliveryCity,
                DeliveryState = g.Key.DeliveryState,
                TotalQuotes = g.Count(),
                AcceptedQuotes = g.Count(b => b.Status == BidStatus.Accepted),
                AcceptanceRate = g.Count() > 0 ? (decimal)g.Count(b => b.Status == BidStatus.Accepted) / g.Count() * 100 : 0,
                AverageQuoteValue = new MoneyDto { Amount = g.Count() > 0 ? g.Sum(b => b.QuotedPrice.Amount) / g.Count() : 0, Currency = "INR" }
            })
            .OrderByDescending(r => r.AcceptanceRate)
            .ToList();
    }

    private async Task<List<TopPerformingRouteDto>> GetTopPerformingRoutes(Guid brokerId, int limit, CancellationToken cancellationToken)
    {
        // Get completed orders for this broker to calculate route performance
        var allOrders = await _orderRepository.GetAllAsync(cancellationToken);
        var completedOrders = allOrders.Where(o => o.BrokerId == brokerId && o.Status == OrderStatus.Completed).ToList();

        return completedOrders
            .GroupBy(o => new
            {
                PickupCity = o.RouteDetails.PickupAddress.City,
                PickupState = o.RouteDetails.PickupAddress.State,
                DeliveryCity = o.RouteDetails.DeliveryAddress.City,
                DeliveryState = o.RouteDetails.DeliveryAddress.State
            })
            .Select(g => new TopPerformingRouteDto
            {
                Route = $"{g.Key.PickupCity}, {g.Key.PickupState} → {g.Key.DeliveryCity}, {g.Key.DeliveryState}",
                TotalOrders = g.Count(),
                TotalRevenue = new MoneyDto { Amount = g.Sum(o => o.AgreedPrice.Amount), Currency = "INR" },
                AverageMargin = 15.0m, // TODO: Calculate actual margin based on costs
                ProfitabilityScore = g.Count() * g.Sum(o => o.AgreedPrice.Amount) / 1000000 // Simple scoring algorithm
            })
            .OrderByDescending(r => r.ProfitabilityScore)
            .Take(limit)
            .ToList();
    }
}
