using AutoMapper;
using ContentManagement.Application.Commands.Content;
using ContentManagement.Application.DTOs;
using ContentManagement.Application.Handlers.Commands.Content;
using ContentManagement.Application.Interfaces;
using ContentManagement.Domain.Entities;
using ContentManagement.Domain.Enums;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace ContentManagement.Tests.Application;

public class ContentCommandHandlerTests
{
    private readonly Mock<IContentRepository> _mockRepository;
    private readonly Mock<IContentAuditService> _mockAuditService;
    private readonly Mock<IMapper> _mockMapper;
    private readonly Mock<ILogger<CreateContentCommandHandler>> _mockLogger;
    private readonly CreateContentCommandHandler _handler;

    public ContentCommandHandlerTests()
    {
        _mockRepository = new Mock<IContentRepository>();
        _mockAuditService = new Mock<IContentAuditService>();
        _mockMapper = new Mock<IMapper>();
        _mockLogger = new Mock<ILogger<CreateContentCommandHandler>>();
        
        _handler = new CreateContentCommandHandler(
            _mockRepository.Object,
            _mockAuditService.Object,
            _mockMapper.Object,
            _mockLogger.Object);
    }

    [Fact]
    public async Task Handle_CreateContentCommand_Should_Create_Content_Successfully()
    {
        // Arrange
        var command = new CreateContentCommand
        {
            Title = "Test Content",
            Slug = "test-content",
            Type = ContentType.General,
            ContentBody = "This is test content",
            CreatedBy = Guid.NewGuid(),
            CreatedByName = "Test User"
        };

        var expectedContent = new Content(
            command.Title,
            command.Slug,
            command.Type,
            command.ContentBody,
            command.CreatedBy,
            command.CreatedByName);

        var expectedDto = new ContentDto
        {
            Id = expectedContent.Id,
            Title = command.Title,
            Slug = command.Slug,
            Type = command.Type,
            ContentBody = command.ContentBody
        };

        _mockRepository.Setup(r => r.IsSlugUniqueAsync(command.Slug, null, It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        _mockRepository.Setup(r => r.AddAsync(It.IsAny<Content>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        _mockAuditService.Setup(a => a.LogContentCreatedAsync(
            It.IsAny<Content>(),
            command.CreatedBy,
            command.CreatedByName,
            It.IsAny<string>(),
            null,
            null,
            It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        _mockMapper.Setup(m => m.Map<ContentDto>(It.IsAny<Content>()))
            .Returns(expectedDto);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Title.Should().Be(command.Title);
        result.Slug.Should().Be(command.Slug);
        result.Type.Should().Be(command.Type);

        _mockRepository.Verify(r => r.AddAsync(It.IsAny<Content>(), It.IsAny<CancellationToken>()), Times.Once);
        _mockAuditService.Verify(a => a.LogContentCreatedAsync(
            It.IsAny<Content>(),
            command.CreatedBy,
            command.CreatedByName,
            It.IsAny<string>(),
            null,
            null,
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateContentCommand_With_Duplicate_Slug_Should_Generate_Unique_Slug()
    {
        // Arrange
        var command = new CreateContentCommand
        {
            Title = "Test Content",
            Slug = "test-content",
            Type = ContentType.General,
            ContentBody = "This is test content",
            CreatedBy = Guid.NewGuid(),
            CreatedByName = "Test User"
        };

        var expectedDto = new ContentDto
        {
            Title = command.Title,
            Slug = "test-content-1", // Expected unique slug
            Type = command.Type
        };

        _mockRepository.Setup(r => r.IsSlugUniqueAsync("test-content", null, It.IsAny<CancellationToken>()))
            .ReturnsAsync(false);

        _mockRepository.Setup(r => r.GenerateUniqueSlugAsync("test-content", It.IsAny<CancellationToken>()))
            .ReturnsAsync("test-content-1");

        _mockRepository.Setup(r => r.AddAsync(It.IsAny<Content>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        _mockAuditService.Setup(a => a.LogContentCreatedAsync(
            It.IsAny<Content>(),
            It.IsAny<Guid>(),
            It.IsAny<string>(),
            It.IsAny<string>(),
            null,
            null,
            It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        _mockMapper.Setup(m => m.Map<ContentDto>(It.IsAny<Content>()))
            .Returns(expectedDto);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Slug.Should().Be("test-content-1");

        _mockRepository.Verify(r => r.GenerateUniqueSlugAsync("test-content", It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateContentCommand_Should_Throw_Exception_When_Repository_Fails()
    {
        // Arrange
        var command = new CreateContentCommand
        {
            Title = "Test Content",
            Slug = "test-content",
            Type = ContentType.General,
            ContentBody = "This is test content",
            CreatedBy = Guid.NewGuid(),
            CreatedByName = "Test User"
        };

        _mockRepository.Setup(r => r.IsSlugUniqueAsync(command.Slug, null, It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        _mockRepository.Setup(r => r.AddAsync(It.IsAny<Content>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Database error"));

        // Act & Assert
        var act = async () => await _handler.Handle(command, CancellationToken.None);
        await act.Should().ThrowAsync<InvalidOperationException>().WithMessage("Database error");
    }
}
