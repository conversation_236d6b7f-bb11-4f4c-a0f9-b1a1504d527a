using FluentAssertions;
using MobileWorkflow.Domain.Entities;
using Xunit;

namespace MobileWorkflow.Tests.Domain;

public class MilestoneStepTests
{
    [Fact]
    public void CreateMilestoneStep_WithValidData_ShouldCreateSuccessfully()
    {
        // Arrange
        var templateId = Guid.NewGuid();
        var name = "Trip Started";
        var description = "Driver has started the trip";
        var sequenceNumber = 1;
        var isRequired = true;

        // Act
        var step = new MilestoneStep(templateId, name, description, sequenceNumber, isRequired);

        // Assert
        step.Should().NotBeNull();
        step.MilestoneTemplateId.Should().Be(templateId);
        step.Name.Should().Be(name);
        step.Description.Should().Be(description);
        step.SequenceNumber.Should().Be(sequenceNumber);
        step.IsRequired.Should().Be(isRequired);
        step.IsActive.Should().BeTrue();
        step.TriggerCondition.Should().BeNull();
        step.PayoutRules.Should().BeEmpty();
        step.Configuration.Should().NotBeNull();
        step.Metadata.Should().NotBeNull();
    }

    [Theory]
    [InlineData(null)]
    [InlineData("")]
    public void CreateMilestoneStep_WithInvalidName_ShouldThrowArgumentNullException(string invalidName)
    {
        // Act & Assert
        var act = () => new MilestoneStep(Guid.NewGuid(), invalidName, "Description", 1, true);
        act.Should().Throw<ArgumentNullException>();
    }

    [Theory]
    [InlineData(null)]
    [InlineData("")]
    public void CreateMilestoneStep_WithInvalidDescription_ShouldThrowArgumentNullException(string invalidDescription)
    {
        // Act & Assert
        var act = () => new MilestoneStep(Guid.NewGuid(), "Name", invalidDescription, 1, true);
        act.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void UpdateDetails_WithValidData_ShouldUpdateSuccessfully()
    {
        // Arrange
        var step = CreateTestMilestoneStep();
        var newName = "Updated Step Name";
        var newDescription = "Updated description";

        // Act
        step.UpdateDetails(newName, newDescription);

        // Assert
        step.Name.Should().Be(newName);
        step.Description.Should().Be(newDescription);
    }

    [Fact]
    public void UpdateSequenceNumber_WithValidNumber_ShouldUpdateSuccessfully()
    {
        // Arrange
        var step = CreateTestMilestoneStep();
        var newSequenceNumber = 5;

        // Act
        step.UpdateSequenceNumber(newSequenceNumber);

        // Assert
        step.SequenceNumber.Should().Be(newSequenceNumber);
    }

    [Fact]
    public void SetRequired_WithTrue_ShouldSetIsRequiredToTrue()
    {
        // Arrange
        var step = CreateTestMilestoneStep();
        step.SetRequired(false);

        // Act
        step.SetRequired(true);

        // Assert
        step.IsRequired.Should().BeTrue();
    }

    [Fact]
    public void SetRequired_WithFalse_ShouldSetIsRequiredToFalse()
    {
        // Arrange
        var step = CreateTestMilestoneStep();

        // Act
        step.SetRequired(false);

        // Assert
        step.IsRequired.Should().BeFalse();
    }

    [Fact]
    public void Activate_WhenCalled_ShouldSetIsActiveToTrue()
    {
        // Arrange
        var step = CreateTestMilestoneStep();
        step.Deactivate();

        // Act
        step.Activate();

        // Assert
        step.IsActive.Should().BeTrue();
    }

    [Fact]
    public void Deactivate_WhenCalled_ShouldSetIsActiveToFalse()
    {
        // Arrange
        var step = CreateTestMilestoneStep();

        // Act
        step.Deactivate();

        // Assert
        step.IsActive.Should().BeFalse();
    }

    [Fact]
    public void SetTriggerCondition_WithValidCondition_ShouldSetSuccessfully()
    {
        // Arrange
        var step = CreateTestMilestoneStep();
        var condition = "status=completed";

        // Act
        step.SetTriggerCondition(condition);

        // Assert
        step.TriggerCondition.Should().Be(condition);
    }

    [Fact]
    public void SetTriggerCondition_WithNull_ShouldSetToNull()
    {
        // Arrange
        var step = CreateTestMilestoneStep();
        step.SetTriggerCondition("status=completed");

        // Act
        step.SetTriggerCondition(null);

        // Assert
        step.TriggerCondition.Should().BeNull();
    }

    [Fact]
    public void AddPayoutRule_WithValidPercentage_ShouldAddRuleSuccessfully()
    {
        // Arrange
        var step = CreateTestMilestoneStep();
        var payoutPercentage = 50.0m;
        var condition = "status=completed";

        // Act
        var rule = step.AddPayoutRule(payoutPercentage, condition);

        // Assert
        rule.Should().NotBeNull();
        rule.MilestoneStepId.Should().Be(step.Id);
        rule.PayoutPercentage.Should().Be(payoutPercentage);
        rule.TriggerCondition.Should().Be(condition);
        step.PayoutRules.Should().Contain(rule);
        step.PayoutRules.Should().HaveCount(1);
    }

    [Theory]
    [InlineData(-1)]
    [InlineData(101)]
    public void AddPayoutRule_WithInvalidPercentage_ShouldThrowArgumentException(decimal invalidPercentage)
    {
        // Arrange
        var step = CreateTestMilestoneStep();

        // Act & Assert
        var act = () => step.AddPayoutRule(invalidPercentage);
        act.Should().Throw<ArgumentException>()
           .WithMessage("Payout percentage must be between 0 and 100*");
    }

    [Fact]
    public void RemovePayoutRule_WithValidRuleId_ShouldRemoveRuleSuccessfully()
    {
        // Arrange
        var step = CreateTestMilestoneStep();
        var rule = step.AddPayoutRule(50.0m);

        // Act
        step.RemovePayoutRule(rule.Id);

        // Assert
        step.PayoutRules.Should().NotContain(rule);
        step.PayoutRules.Should().BeEmpty();
    }

    [Fact]
    public void RemovePayoutRule_WithInvalidRuleId_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var step = CreateTestMilestoneStep();
        var invalidRuleId = Guid.NewGuid();

        // Act & Assert
        var act = () => step.RemovePayoutRule(invalidRuleId);
        act.Should().Throw<InvalidOperationException>()
           .WithMessage($"Payout rule with ID {invalidRuleId} not found");
    }

    [Fact]
    public void GetTotalPayoutPercentage_WithMultipleRules_ShouldReturnCorrectTotal()
    {
        // Arrange
        var step = CreateTestMilestoneStep();
        step.AddPayoutRule(30.0m);
        step.AddPayoutRule(20.0m);
        step.AddPayoutRule(25.0m);

        // Act
        var total = step.GetTotalPayoutPercentage();

        // Assert
        total.Should().Be(75.0m);
    }

    [Fact]
    public void GetTotalPayoutPercentage_WithNoRules_ShouldReturnZero()
    {
        // Arrange
        var step = CreateTestMilestoneStep();

        // Act
        var total = step.GetTotalPayoutPercentage();

        // Assert
        total.Should().Be(0.0m);
    }

    [Fact]
    public void ValidatePayoutRules_WithValidPercentages_ShouldReturnTrue()
    {
        // Arrange
        var step = CreateTestMilestoneStep();
        step.AddPayoutRule(50.0m);
        step.AddPayoutRule(30.0m);

        // Act
        var isValid = step.ValidatePayoutRules();

        // Assert
        isValid.Should().BeTrue();
    }

    [Fact]
    public void ValidatePayoutRules_WithExcessivePercentages_ShouldReturnFalse()
    {
        // Arrange
        var step = CreateTestMilestoneStep();
        step.AddPayoutRule(60.0m);
        step.AddPayoutRule(50.0m); // Total = 110%

        // Act
        var isValid = step.ValidatePayoutRules();

        // Assert
        isValid.Should().BeFalse();
    }

    [Fact]
    public void GetValidationErrors_WithValidStep_ShouldReturnEmptyList()
    {
        // Arrange
        var step = CreateTestMilestoneStep();
        step.AddPayoutRule(50.0m);

        // Act
        var errors = step.GetValidationErrors();

        // Assert
        errors.Should().BeEmpty();
    }

    [Fact]
    public void GetValidationErrors_WithInvalidSequenceNumber_ShouldReturnError()
    {
        // Arrange
        var step = new MilestoneStep(Guid.NewGuid(), "Name", "Description", 0, true);

        // Act
        var errors = step.GetValidationErrors();

        // Assert
        errors.Should().Contain("Sequence number must be greater than 0");
    }

    [Fact]
    public void CanBeDeleted_WhenNotRequired_ShouldReturnTrue()
    {
        // Arrange
        var step = CreateTestMilestoneStep();
        step.SetRequired(false);

        // Act
        var canBeDeleted = step.CanBeDeleted();

        // Assert
        canBeDeleted.Should().BeTrue();
    }

    [Fact]
    public void CanBeDeleted_WhenRequired_ShouldReturnFalse()
    {
        // Arrange
        var step = CreateTestMilestoneStep();

        // Act
        var canBeDeleted = step.CanBeDeleted();

        // Assert
        canBeDeleted.Should().BeFalse();
    }

    [Fact]
    public void UpdateConfiguration_WithValidData_ShouldUpdateSuccessfully()
    {
        // Arrange
        var step = CreateTestMilestoneStep();
        var configuration = new Dictionary<string, object>
        {
            { "require_photo", true },
            { "timeout_minutes", 30 }
        };

        // Act
        step.UpdateConfiguration(configuration);

        // Assert
        step.Configuration.Should().BeEquivalentTo(configuration);
    }

    [Fact]
    public void AddMetadata_WithValidData_ShouldAddSuccessfully()
    {
        // Arrange
        var step = CreateTestMilestoneStep();
        var key = "test_key";
        var value = "test_value";

        // Act
        step.AddMetadata(key, value);

        // Assert
        step.Metadata.Should().ContainKey(key);
        step.Metadata[key].Should().Be(value);
    }

    [Fact]
    public void GetMetadata_WithExistingKey_ShouldReturnValue()
    {
        // Arrange
        var step = CreateTestMilestoneStep();
        var key = "test_key";
        var value = 42;
        step.AddMetadata(key, value);

        // Act
        var result = step.GetMetadata<int>(key);

        // Assert
        result.Should().Be(value);
    }

    [Fact]
    public void GetMetadata_WithNonExistingKey_ShouldReturnDefault()
    {
        // Arrange
        var step = CreateTestMilestoneStep();
        var defaultValue = 100;

        // Act
        var result = step.GetMetadata("non_existing_key", defaultValue);

        // Assert
        result.Should().Be(defaultValue);
    }

    private static MilestoneStep CreateTestMilestoneStep()
    {
        return new MilestoneStep(
            Guid.NewGuid(),
            "Test Step",
            "Test Description",
            1,
            true);
    }
}
