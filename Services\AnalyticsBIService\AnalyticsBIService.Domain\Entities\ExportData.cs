using AnalyticsBIService.Domain.Enums;
using Shared.Domain.Common;

namespace AnalyticsBIService.Domain.Entities;

/// <summary>
/// Export data entity for tracking data exports
/// </summary>
public class ExportData : BaseEntity
{
    public Guid UserId { get; private set; }
    public string ExportType { get; private set; }
    public string FileName { get; private set; }
    public string FilePath { get; private set; }
    public ExportFormat Format { get; private set; }
    public string Status { get; private set; }
    public DateTime RequestedAt { get; private set; }
    public DateTime? CompletedAt { get; private set; }
    public long? FileSizeBytes { get; private set; }
    public int? RecordCount { get; private set; }
    public string? ErrorMessage { get; private set; }
    public string Parameters { get; private set; }
    public DateTime? ExpiresAt { get; private set; }
    public bool IsDownloaded { get; private set; }
    public DateTime? DownloadedAt { get; private set; }
    public int DownloadCount { get; private set; }

    // Private constructor for EF Core
    private ExportData() 
    {
        ExportType = string.Empty;
        FileName = string.Empty;
        FilePath = string.Empty;
        Status = string.Empty;
        Parameters = string.Empty;
    }

    /// <summary>
    /// Create export data request
    /// </summary>
    public static ExportData Create(
        Guid userId,
        string exportType,
        string fileName,
        ExportFormat format,
        string parameters)
    {
        return new ExportData
        {
            UserId = userId,
            ExportType = exportType,
            FileName = fileName,
            FilePath = string.Empty,
            Format = format,
            Status = "Requested",
            RequestedAt = DateTime.UtcNow,
            Parameters = parameters,
            ExpiresAt = DateTime.UtcNow.AddDays(7), // Default 7 days expiry
            IsDownloaded = false,
            DownloadCount = 0
        };
    }

    /// <summary>
    /// Mark export as processing
    /// </summary>
    public void MarkProcessing()
    {
        Status = "Processing";
    }

    /// <summary>
    /// Mark export as completed
    /// </summary>
    public void MarkCompleted(string filePath, long fileSizeBytes, int recordCount)
    {
        Status = "Completed";
        FilePath = filePath;
        FileSizeBytes = fileSizeBytes;
        RecordCount = recordCount;
        CompletedAt = DateTime.UtcNow;
        ErrorMessage = null;
    }

    /// <summary>
    /// Mark export as failed
    /// </summary>
    public void MarkFailed(string errorMessage)
    {
        Status = "Failed";
        ErrorMessage = errorMessage;
        CompletedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Record download
    /// </summary>
    public void RecordDownload()
    {
        IsDownloaded = true;
        DownloadedAt = DateTime.UtcNow;
        DownloadCount++;
    }

    /// <summary>
    /// Set expiry date
    /// </summary>
    public void SetExpiryDate(DateTime expiresAt)
    {
        ExpiresAt = expiresAt;
    }

    /// <summary>
    /// Check if export is expired
    /// </summary>
    public bool IsExpired()
    {
        return ExpiresAt.HasValue && DateTime.UtcNow > ExpiresAt.Value;
    }

    /// <summary>
    /// Check if export is ready for download
    /// </summary>
    public bool IsReadyForDownload()
    {
        return Status == "Completed" && !IsExpired();
    }

    /// <summary>
    /// Get file size in MB
    /// </summary>
    public decimal GetFileSizeMB()
    {
        return FileSizeBytes.HasValue ? (decimal)FileSizeBytes.Value / (1024 * 1024) : 0;
    }

    /// <summary>
    /// Get processing duration
    /// </summary>
    public TimeSpan? GetProcessingDuration()
    {
        if (CompletedAt.HasValue)
            return CompletedAt.Value - RequestedAt;
        
        if (Status == "Processing")
            return DateTime.UtcNow - RequestedAt;
            
        return null;
    }

    /// <summary>
    /// Get days until expiry
    /// </summary>
    public int? GetDaysUntilExpiry()
    {
        if (!ExpiresAt.HasValue) return null;
        
        var daysLeft = (ExpiresAt.Value - DateTime.UtcNow).Days;
        return Math.Max(0, daysLeft);
    }
}
