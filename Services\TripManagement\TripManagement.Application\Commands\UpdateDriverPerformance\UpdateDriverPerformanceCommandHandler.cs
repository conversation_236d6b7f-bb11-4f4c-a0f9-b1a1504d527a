using MediatR;
using Microsoft.Extensions.Logging;
using TripManagement.Application.Interfaces;
using TripManagement.Domain.Entities;

namespace TripManagement.Application.Commands.UpdateDriverPerformance;

public class UpdateDriverPerformanceCommandHandler : IRequestHandler<UpdateDriverPerformanceCommand, bool>
{
    private readonly IDriverRepository _driverRepository;
    private readonly ITripRepository _tripRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<UpdateDriverPerformanceCommandHandler> _logger;
    private readonly IMessageBroker _messageBroker;

    public UpdateDriverPerformanceCommandHandler(
        IDriverRepository driverRepository,
        ITripRepository tripRepository,
        IUnitOfWork unitOfWork,
        ILogger<UpdateDriverPerformanceCommandHandler> logger,
        IMessageBroker messageBroker)
    {
        _driverRepository = driverRepository;
        _tripRepository = tripRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
        _messageBroker = messageBroker;
    }

    public async Task<bool> Handle(UpdateDriverPerformanceCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Updating performance for driver {DriverId}", request.DriverId);

            // Get the driver
            var driver = await _driverRepository.GetByIdAsync(request.DriverId, cancellationToken);
            if (driver == null)
            {
                _logger.LogWarning("Driver {DriverId} not found", request.DriverId);
                return false;
            }

            // Get the trip for context
            var trip = await _tripRepository.GetByIdAsync(request.TripId, cancellationToken);
            if (trip == null)
            {
                _logger.LogWarning("Trip {TripId} not found", request.TripId);
                return false;
            }

            // Create performance record
            var performanceRecord = new DriverPerformance(
                driverId: request.DriverId,
                tripId: request.TripId,
                metrics: request.Metrics,
                evaluationDate: request.EvaluationDate,
                evaluatedBy: request.EvaluatedBy,
                notes: request.Notes);

            // Add performance record to driver
            driver.AddPerformanceRecord(performanceRecord);

            // Update driver's overall rating based on new performance data
            var updatedRating = CalculateUpdatedRating(driver, request.Metrics);
            driver.UpdateRating(updatedRating);

            // Save changes
            _driverRepository.Update(driver);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Publish integration event
            await _messageBroker.PublishAsync("driver.performance_updated", new
            {
                DriverId = request.DriverId,
                TripId = request.TripId,
                PerformanceRecordId = performanceRecord.Id,
                UpdatedRating = updatedRating,
                OnTimeDeliveryRate = request.Metrics.OnTimeDeliveryRate,
                SafetyScore = request.Metrics.SafetyScore,
                CustomerRating = request.Metrics.CustomerRating,
                EvaluationDate = request.EvaluationDate,
                EvaluatedBy = request.EvaluatedBy
            }, cancellationToken);

            _logger.LogInformation("Performance updated successfully for driver {DriverId}. New rating: {Rating}", 
                request.DriverId, updatedRating);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating performance for driver {DriverId}", request.DriverId);
            throw;
        }
    }

    private decimal CalculateUpdatedRating(Driver driver, PerformanceMetrics newMetrics)
    {
        // Get all performance records for the driver
        var allRecords = driver.PerformanceRecords.ToList();
        
        if (!allRecords.Any())
        {
            // If no previous records, use the new metrics to calculate initial rating
            return CalculateRatingFromMetrics(newMetrics);
        }

        // Calculate weighted average based on recent performance
        var recentRecords = allRecords.OrderByDescending(r => r.EvaluationDate).Take(10).ToList();
        
        var totalWeight = 0m;
        var weightedSum = 0m;
        
        for (int i = 0; i < recentRecords.Count; i++)
        {
            var weight = 1m / (i + 1); // More recent records have higher weight
            var recordRating = CalculateRatingFromMetrics(recentRecords[i].Metrics);
            
            weightedSum += recordRating * weight;
            totalWeight += weight;
        }
        
        return totalWeight > 0 ? Math.Round(weightedSum / totalWeight, 2) : 0;
    }

    private decimal CalculateRatingFromMetrics(PerformanceMetrics metrics)
    {
        // Weighted calculation of overall rating (0-5 scale)
        var rating = 0m;
        
        // On-time delivery (30% weight)
        rating += (metrics.OnTimeDeliveryRate / 100) * 5 * 0.3m;
        
        // Safety score (25% weight)
        rating += (metrics.SafetyScore / 100) * 5 * 0.25m;
        
        // Customer rating (20% weight)
        rating += metrics.CustomerRating * 0.2m;
        
        // Fuel efficiency (15% weight)
        rating += (metrics.FuelEfficiencyScore / 100) * 5 * 0.15m;
        
        // Communication score (10% weight)
        rating += (metrics.CommunicationScore / 100) * 5 * 0.1m;
        
        return Math.Round(Math.Max(0, Math.Min(5, rating)), 2);
    }
}
