using Microsoft.Extensions.Logging;
using Moq;
using NetworkFleetManagement.Domain.Entities;
using NetworkFleetManagement.Domain.Repositories;
using NetworkFleetManagement.Domain.Services;
using NetworkFleetManagement.Infrastructure.Services;
using Shared.Infrastructure.Caching;
using Xunit;

namespace NetworkFleetManagement.Tests.Infrastructure.Services;

public class FeatureFlagServiceTests
{
    private readonly Mock<IFeatureFlagRepository> _mockRepository;
    private readonly Mock<ICacheService> _mockCacheService;
    private readonly Mock<ILogger<FeatureFlagService>> _mockLogger;
    private readonly FeatureFlagService _service;

    public FeatureFlagServiceTests()
    {
        _mockRepository = new Mock<IFeatureFlagRepository>();
        _mockCacheService = new Mock<ICacheService>();
        _mockLogger = new Mock<ILogger<FeatureFlagService>>();
        _service = new FeatureFlagService(_mockRepository.Object, _mockCacheService.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task IsFeatureEnabledAsync_WithValidFeatureFlag_ReturnsTrue()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var featureKey = "test_feature";
        var featureFlag = new FeatureFlag(featureKey, "Test Feature", "Test Description", true);
        
        _mockCacheService.Setup(x => x.GetAsync<FeatureFlag>(It.IsAny<string>()))
            .ReturnsAsync((FeatureFlag?)null);
        _mockRepository.Setup(x => x.GetByKeyAsync(featureKey))
            .ReturnsAsync(featureFlag);

        // Act
        var result = await _service.IsFeatureEnabledAsync(featureKey, userId);

        // Assert
        Assert.True(result);
        _mockRepository.Verify(x => x.UpdateAsync(It.IsAny<FeatureFlag>()), Times.Once);
    }

    [Fact]
    public async Task IsFeatureEnabledAsync_WithNonExistentFeatureFlag_ReturnsFalse()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var featureKey = "non_existent_feature";
        
        _mockCacheService.Setup(x => x.GetAsync<FeatureFlag>(It.IsAny<string>()))
            .ReturnsAsync((FeatureFlag?)null);
        _mockRepository.Setup(x => x.GetByKeyAsync(featureKey))
            .ReturnsAsync((FeatureFlag?)null);

        // Act
        var result = await _service.IsFeatureEnabledAsync(featureKey, userId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task CreateFeatureFlagAsync_WithValidData_ReturnsTrue()
    {
        // Arrange
        var key = "new_feature";
        var name = "New Feature";
        var description = "New Feature Description";
        
        _mockRepository.Setup(x => x.GetByKeyAsync(key))
            .ReturnsAsync((FeatureFlag?)null);
        _mockRepository.Setup(x => x.CreateAsync(It.IsAny<FeatureFlag>()))
            .ReturnsAsync((FeatureFlag flag) => flag);

        // Act
        var result = await _service.CreateFeatureFlagAsync(key, name, description, true);

        // Assert
        Assert.True(result);
        _mockRepository.Verify(x => x.CreateAsync(It.IsAny<FeatureFlag>()), Times.Once);
    }

    [Fact]
    public async Task CreateFeatureFlagAsync_WithExistingKey_ReturnsFalse()
    {
        // Arrange
        var key = "existing_feature";
        var name = "Existing Feature";
        var description = "Existing Feature Description";
        var existingFlag = new FeatureFlag(key, name, description);
        
        _mockRepository.Setup(x => x.GetByKeyAsync(key))
            .ReturnsAsync(existingFlag);

        // Act
        var result = await _service.CreateFeatureFlagAsync(key, name, description, true);

        // Assert
        Assert.False(result);
        _mockRepository.Verify(x => x.CreateAsync(It.IsAny<FeatureFlag>()), Times.Never);
    }

    [Fact]
    public async Task GetFeatureVariantAsync_WithValidFeatureFlag_ReturnsVariant()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var featureKey = "test_feature";
        var featureFlag = new FeatureFlag(featureKey, "Test Feature", "Test Description", true);
        
        var variantWeights = new Dictionary<string, double>
        {
            ["variant_a"] = 50.0,
            ["variant_b"] = 50.0
        };
        featureFlag.SetVariantWeights(variantWeights);
        
        _mockCacheService.Setup(x => x.GetAsync<FeatureFlag>(It.IsAny<string>()))
            .ReturnsAsync((FeatureFlag?)null);
        _mockRepository.Setup(x => x.GetByKeyAsync(featureKey))
            .ReturnsAsync(featureFlag);

        // Act
        var result = await _service.GetFeatureVariantAsync(featureKey, userId);

        // Assert
        Assert.NotNull(result);
        Assert.Contains(result, new[] { "variant_a", "variant_b" });
    }

    [Fact]
    public async Task SetRolloutPercentageAsync_WithValidFeatureFlag_ReturnsTrue()
    {
        // Arrange
        var featureKey = "test_feature";
        var percentage = 75.0;
        var featureFlag = new FeatureFlag(featureKey, "Test Feature", "Test Description", true);
        
        _mockRepository.Setup(x => x.GetByKeyAsync(featureKey))
            .ReturnsAsync(featureFlag);
        _mockRepository.Setup(x => x.UpdateAsync(It.IsAny<FeatureFlag>()))
            .ReturnsAsync((FeatureFlag flag) => flag);

        // Act
        var result = await _service.SetRolloutPercentageAsync(featureKey, percentage);

        // Assert
        Assert.True(result);
        _mockRepository.Verify(x => x.UpdateAsync(It.IsAny<FeatureFlag>()), Times.Once);
    }

    [Fact]
    public async Task IsFleetFeatureEnabledAsync_WithValidFeatureType_CallsIsFeatureEnabledAsync()
    {
        // Arrange
        var carrierId = Guid.NewGuid();
        var featureType = FleetFeatureType.RealTimeTracking;
        var expectedKey = "fleet_realtimetracking";
        
        _mockCacheService.Setup(x => x.GetAsync<FeatureFlag>(It.IsAny<string>()))
            .ReturnsAsync((FeatureFlag?)null);
        _mockRepository.Setup(x => x.GetByKeyAsync(expectedKey))
            .ReturnsAsync((FeatureFlag?)null);

        // Act
        var result = await _service.IsFleetFeatureEnabledAsync(featureType, carrierId);

        // Assert
        Assert.False(result); // Should be false since feature flag doesn't exist
        _mockRepository.Verify(x => x.GetByKeyAsync(expectedKey), Times.Once);
    }

    [Fact]
    public async Task GetFeatureFlagAnalyticsAsync_WithValidFeatureFlag_ReturnsAnalytics()
    {
        // Arrange
        var featureKey = "test_feature";
        var from = DateTime.UtcNow.AddDays(-30);
        var to = DateTime.UtcNow;
        var featureFlag = new FeatureFlag(featureKey, "Test Feature", "Test Description", true);
        var expectedAnalytics = new Dictionary<string, object>
        {
            ["totalUsage"] = 100,
            ["uniqueUsers"] = 50
        };
        
        _mockRepository.Setup(x => x.GetByKeyAsync(featureKey))
            .ReturnsAsync(featureFlag);
        _mockRepository.Setup(x => x.GetAnalyticsAsync(featureFlag.Id, from, to))
            .ReturnsAsync(expectedAnalytics);

        // Act
        var result = await _service.GetFeatureFlagAnalyticsAsync(featureKey, from, to);

        // Assert
        Assert.Equal(expectedAnalytics, result);
    }

    [Fact]
    public async Task RecordConversionAsync_WithValidData_CallsRepository()
    {
        // Arrange
        var featureKey = "test_feature";
        var userId = Guid.NewGuid();
        var variant = "variant_a";
        var conversionData = new Dictionary<string, object> { ["action"] = "purchase" };

        // Act
        await _service.RecordConversionAsync(featureKey, userId, variant, conversionData);

        // Assert
        _mockRepository.Verify(x => x.RecordConversionAsync(featureKey, userId, variant, conversionData), Times.Once);
    }

    [Fact]
    public async Task AddUserToWhitelistAsync_WithValidFeatureFlag_ReturnsTrue()
    {
        // Arrange
        var featureKey = "test_feature";
        var userId = Guid.NewGuid();
        var featureFlag = new FeatureFlag(featureKey, "Test Feature", "Test Description", true);
        
        _mockRepository.Setup(x => x.GetByKeyAsync(featureKey))
            .ReturnsAsync(featureFlag);
        _mockRepository.Setup(x => x.UpdateAsync(It.IsAny<FeatureFlag>()))
            .ReturnsAsync((FeatureFlag flag) => flag);

        // Act
        var result = await _service.AddUserToWhitelistAsync(featureKey, userId);

        // Assert
        Assert.True(result);
        _mockRepository.Verify(x => x.UpdateAsync(It.IsAny<FeatureFlag>()), Times.Once);
    }

    [Fact]
    public async Task RemoveUserFromWhitelistAsync_WithValidFeatureFlag_ReturnsTrue()
    {
        // Arrange
        var featureKey = "test_feature";
        var userId = Guid.NewGuid();
        var featureFlag = new FeatureFlag(featureKey, "Test Feature", "Test Description", true);
        featureFlag.AddToWhitelist(userId); // Add user first
        
        _mockRepository.Setup(x => x.GetByKeyAsync(featureKey))
            .ReturnsAsync(featureFlag);
        _mockRepository.Setup(x => x.UpdateAsync(It.IsAny<FeatureFlag>()))
            .ReturnsAsync((FeatureFlag flag) => flag);

        // Act
        var result = await _service.RemoveUserFromWhitelistAsync(featureKey, userId);

        // Assert
        Assert.True(result);
        _mockRepository.Verify(x => x.UpdateAsync(It.IsAny<FeatureFlag>()), Times.Once);
    }
}
