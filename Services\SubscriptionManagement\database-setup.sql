-- Subscription Management Service Database Setup
-- Run this script to create the database schema for the Subscription Management Service

-- Create database (run this as superuser)
-- CREATE DATABASE "TLI_SubscriptionManagement";

-- Connect to the database and create schema
CREATE SCHEMA IF NOT EXISTS subscription;

-- Set search path
SET search_path TO subscription;

-- Create Plans table
CREATE TABLE subscription."Plans" (
    "Id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "Name" VARCHAR(100) NOT NULL UNIQUE,
    "Description" VARCHAR(500) NOT NULL,
    "Type" VARCHAR(50) NOT NULL,
    "UserType" VARCHAR(50) NOT NULL,
    "Price" DECIMAL(18,2) NOT NULL,
    "Currency" VARCHAR(3) NOT NULL,
    "BillingInterval" VARCHAR(50) NOT NULL,
    "BillingIntervalCount" INTEGER NOT NULL,
    "CustomBillingDays" INTEGER NULL,
    "RfqLimit" INTEGER NULL,
    "VehicleLimit" INTEGER NULL,
    "CarrierLimit" INTEGER NULL,
    "IsUnlimited" BOOLEAN NOT NULL DEFAULT FALSE,
    "IsActive" BOOLEAN NOT NULL DEFAULT TRUE,
    "IsPublic" BOOLEAN NOT NULL DEFAULT TRUE,
    "TrialPeriodDays" TIMESTAMP NULL,
    "SetupFeeAmount" INTEGER NULL,
    "SetupFeeCurrency" VARCHAR(3) NULL,
    "CreatedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP NULL
);

-- Create PlanFeatures table
CREATE TABLE subscription."PlanFeatures" (
    "Id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "PlanId" UUID NOT NULL REFERENCES subscription."Plans"("Id") ON DELETE CASCADE,
    "FeatureType" VARCHAR(50) NOT NULL,
    "AccessType" VARCHAR(50) NOT NULL,
    "LimitValue" INTEGER NULL,
    "Description" VARCHAR(500) NULL,
    "IsEnabled" BOOLEAN NOT NULL DEFAULT TRUE,
    "CreatedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP NULL,
    UNIQUE("PlanId", "FeatureType")
);

-- Create Subscriptions table
CREATE TABLE subscription."Subscriptions" (
    "Id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "UserId" UUID NOT NULL,
    "PlanId" UUID NOT NULL REFERENCES subscription."Plans"("Id") ON DELETE RESTRICT,
    "Status" VARCHAR(50) NOT NULL,
    "StartDate" TIMESTAMP NOT NULL,
    "EndDate" TIMESTAMP NULL,
    "NextBillingDate" TIMESTAMP NOT NULL,
    "TrialEndDate" TIMESTAMP NULL,
    "CancelledAt" TIMESTAMP NULL,
    "CancellationReason" VARCHAR(500) NULL,
    "AutoRenew" BOOLEAN NOT NULL DEFAULT TRUE,
    "CurrentPrice" DECIMAL(18,2) NOT NULL,
    "CurrentCurrency" VARCHAR(3) NOT NULL,
    "BillingInterval" VARCHAR(50) NOT NULL,
    "BillingIntervalCount" INTEGER NOT NULL,
    "CustomBillingDays" INTEGER NULL,
    "ProrationMode" VARCHAR(50) NOT NULL,
    "GracePeriodEndDate" TIMESTAMP NULL,
    "LastExtendedAt" TIMESTAMP NULL,
    "ExtensionReason" VARCHAR(500) NULL,
    "ExtendedByUserId" UUID NULL,
    "CreatedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP NULL
);

-- Create Payments table
CREATE TABLE subscription."Payments" (
    "Id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "SubscriptionId" UUID NOT NULL REFERENCES subscription."Subscriptions"("Id") ON DELETE CASCADE,
    "UserId" UUID NOT NULL,
    "Amount" DECIMAL(18,2) NOT NULL,
    "Currency" VARCHAR(3) NOT NULL,
    "Status" VARCHAR(50) NOT NULL,
    "PaymentGatewayTransactionId" VARCHAR(100) NULL,
    "PaymentMethod" VARCHAR(50) NULL,
    "ProcessedAt" TIMESTAMP NULL,
    "FailedAt" TIMESTAMP NULL,
    "FailureReason" VARCHAR(500) NULL,
    "PaymentGatewayResponse" VARCHAR(2000) NULL,
    "RetryCount" INTEGER NOT NULL DEFAULT 0,
    "NextRetryAt" TIMESTAMP NULL,
    "CreatedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP NULL
);

-- Create SubscriptionChanges table
CREATE TABLE subscription."SubscriptionChanges" (
    "Id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "SubscriptionId" UUID NOT NULL REFERENCES subscription."Subscriptions"("Id") ON DELETE CASCADE,
    "ChangeType" VARCHAR(50) NOT NULL,
    "Description" VARCHAR(500) NULL,
    "ChangedAt" TIMESTAMP NOT NULL,
    "CreatedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP NULL
);

-- Create indexes for better performance
CREATE INDEX "IX_Plans_UserType_Type" ON subscription."Plans"("UserType", "Type");
CREATE INDEX "IX_Plans_IsActive" ON subscription."Plans"("IsActive");
CREATE INDEX "IX_Plans_IsPublic" ON subscription."Plans"("IsPublic");

CREATE INDEX "IX_Subscriptions_UserId" ON subscription."Subscriptions"("UserId");
CREATE INDEX "IX_Subscriptions_PlanId" ON subscription."Subscriptions"("PlanId");
CREATE INDEX "IX_Subscriptions_Status" ON subscription."Subscriptions"("Status");
CREATE INDEX "IX_Subscriptions_NextBillingDate" ON subscription."Subscriptions"("NextBillingDate");
CREATE INDEX "IX_Subscriptions_UserId_Status" ON subscription."Subscriptions"("UserId", "Status");
CREATE INDEX "IX_Subscriptions_GracePeriodEndDate" ON subscription."Subscriptions"("GracePeriodEndDate");
CREATE INDEX "IX_Subscriptions_LastExtendedAt" ON subscription."Subscriptions"("LastExtendedAt");
CREATE INDEX "IX_Subscriptions_ExtendedByUserId" ON subscription."Subscriptions"("ExtendedByUserId");

CREATE INDEX "IX_Payments_SubscriptionId" ON subscription."Payments"("SubscriptionId");
CREATE INDEX "IX_Payments_UserId" ON subscription."Payments"("UserId");
CREATE INDEX "IX_Payments_Status" ON subscription."Payments"("Status");
CREATE INDEX "IX_Payments_PaymentGatewayTransactionId" ON subscription."Payments"("PaymentGatewayTransactionId");
CREATE INDEX "IX_Payments_ProcessedAt" ON subscription."Payments"("ProcessedAt");

CREATE INDEX "IX_SubscriptionChanges_SubscriptionId" ON subscription."SubscriptionChanges"("SubscriptionId");
CREATE INDEX "IX_SubscriptionChanges_ChangedAt" ON subscription."SubscriptionChanges"("ChangedAt");

-- Insert sample plans for testing
INSERT INTO subscription."Plans" (
    "Name", "Description", "Type", "UserType", "Price", "Currency",
    "BillingInterval", "BillingIntervalCount", "RfqLimit", "IsUnlimited"
) VALUES 
-- Transport Company Plans
('Transport Basic', 'Basic plan for transport companies', 'Basic', 'TransportCompany', 999.00, 'INR', 'Monthly', 1, 10, FALSE),
('Transport Pro', 'Professional plan for transport companies', 'Pro', 'TransportCompany', 2999.00, 'INR', 'Monthly', 1, NULL, TRUE),
('Transport Enterprise', 'Enterprise plan for transport companies', 'Enterprise', 'TransportCompany', 9999.00, 'INR', 'Monthly', 1, NULL, TRUE),

-- Broker Plans
('Broker Basic', 'Basic plan for brokers', 'Basic', 'Broker', 1499.00, 'INR', 'Monthly', 1, 20, FALSE),
('Broker Pro', 'Professional plan for brokers', 'Pro', 'Broker', 4999.00, 'INR', 'Monthly', 1, NULL, TRUE),
('Broker Enterprise', 'Enterprise plan for brokers', 'Enterprise', 'Broker', 14999.00, 'INR', 'Monthly', 1, NULL, TRUE),

-- Carrier Plans
('Carrier Basic', 'Basic plan for carriers', 'Basic', 'Carrier', 799.00, 'INR', 'Monthly', 1, 15, FALSE),
('Carrier Pro', 'Professional plan for carriers', 'Pro', 'Carrier', 2499.00, 'INR', 'Monthly', 1, NULL, TRUE),
('Carrier Enterprise', 'Enterprise plan for carriers', 'Enterprise', 'Carrier', 7999.00, 'INR', 'Monthly', 1, NULL, TRUE);

COMMIT;
