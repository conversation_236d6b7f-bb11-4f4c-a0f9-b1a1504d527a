using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using MediatR;
using AuditCompliance.Application.Commands;
using AuditCompliance.Application.Queries;
using AuditCompliance.Application.DTOs;

namespace AuditCompliance.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize(Roles = "Shipper")]
public class PreferredProviderController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<PreferredProviderController> _logger;

    public PreferredProviderController(IMediator mediator, ILogger<PreferredProviderController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get preferred provider network for current shipper
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<List<PreferredProviderNetworkDto>>> GetPreferredProviders()
    {
        try
        {
            var query = new GetPreferredProviderNetworkQuery { ShipperId = GetCurrentUserId() };
            var providers = await _mediator.Send(query);
            return Ok(providers);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving preferred providers for shipper: {ShipperId}", GetCurrentUserId());
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get preferred provider by ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<PreferredProviderNetworkDto>> GetPreferredProvider(Guid id)
    {
        try
        {
            var query = new GetPreferredProviderByIdQuery { Id = id };
            var provider = await _mediator.Send(query);
            
            if (provider == null)
                return NotFound();

            // Verify ownership
            if (provider.ShipperId != GetCurrentUserId())
                return Forbid();

            return Ok(provider);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving preferred provider: {ProviderId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Add a transport company to preferred provider network
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<Guid>> AddPreferredProvider([FromBody] AddPreferredProviderCommand command)
    {
        try
        {
            // Set shipper information from current user
            command.ShipperId = GetCurrentUserId();
            command.ShipperName = GetCurrentUserName();

            var providerId = await _mediator.Send(command);
            return Ok(new { ProviderId = providerId });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding preferred provider");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Remove a transport company from preferred provider network
    /// </summary>
    [HttpDelete("{transportCompanyId}")]
    public async Task<ActionResult> RemovePreferredProvider(Guid transportCompanyId, [FromBody] RemovePreferredProviderRequest request)
    {
        try
        {
            var command = new RemovePreferredProviderCommand 
            { 
                ShipperId = GetCurrentUserId(),
                TransportCompanyId = transportCompanyId,
                Reason = request.Reason
            };

            var result = await _mediator.Send(command);
            
            if (!result)
                return NotFound();

            return Ok(new { Message = "Preferred provider removed" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing preferred provider: {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Update preferred provider statistics
    /// </summary>
    [HttpPut("{transportCompanyId}/statistics")]
    public async Task<ActionResult> UpdatePreferredProviderStatistics(Guid transportCompanyId, [FromBody] UpdatePreferredProviderStatisticsCommand command)
    {
        try
        {
            command.ShipperId = GetCurrentUserId();
            command.TransportCompanyId = transportCompanyId;

            var result = await _mediator.Send(command);
            
            if (!result)
                return NotFound();

            return Ok(new { Message = "Preferred provider statistics updated" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating preferred provider statistics: {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Reorder preferred providers
    /// </summary>
    [HttpPut("reorder")]
    public async Task<ActionResult> ReorderPreferredProviders([FromBody] ReorderPreferredProvidersCommand command)
    {
        try
        {
            command.ShipperId = GetCurrentUserId();

            var result = await _mediator.Send(command);
            
            if (!result)
                return BadRequest("Failed to reorder preferred providers");

            return Ok(new { Message = "Preferred providers reordered" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reordering preferred providers for shipper: {ShipperId}", GetCurrentUserId());
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get preferred provider by shipper and transport company
    /// </summary>
    [HttpGet("transport-company/{transportCompanyId}")]
    public async Task<ActionResult<PreferredProviderNetworkDto>> GetPreferredProviderByTransportCompany(Guid transportCompanyId)
    {
        try
        {
            var query = new GetPreferredProviderByShipperAndProviderQuery 
            { 
                ShipperId = GetCurrentUserId(),
                TransportCompanyId = transportCompanyId
            };
            
            var provider = await _mediator.Send(query);
            
            if (provider == null)
                return NotFound();

            return Ok(provider);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving preferred provider by transport company: {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst("sub") ?? User.FindFirst("id");
        return userIdClaim != null && Guid.TryParse(userIdClaim.Value, out var userId) 
            ? userId 
            : Guid.Empty;
    }

    private string GetCurrentUserName()
    {
        return User.FindFirst("name")?.Value ?? 
               User.FindFirst("preferred_username")?.Value ?? 
               User.Identity?.Name ?? 
               "Unknown";
    }
}

public class RemovePreferredProviderRequest
{
    public string Reason { get; set; } = string.Empty;
}
