namespace AuditCompliance.Domain.Enums;

/// <summary>
/// Severity levels for audit events
/// </summary>
public enum AuditSeverity
{
    /// <summary>
    /// Informational events - normal system operations
    /// </summary>
    Info = 1,
    
    /// <summary>
    /// Low severity - minor issues or warnings
    /// </summary>
    Low = 2,
    
    /// <summary>
    /// Medium severity - important events that require attention
    /// </summary>
    Medium = 3,
    
    /// <summary>
    /// High severity - significant events that may impact operations
    /// </summary>
    High = 4,
    
    /// <summary>
    /// Critical severity - events that require immediate attention
    /// </summary>
    Critical = 5
}
