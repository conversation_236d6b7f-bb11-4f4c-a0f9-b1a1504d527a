namespace MonitoringObservability.Domain.DTOs;

/// <summary>
/// DTO for capacity analysis
/// </summary>
public class CapacityAnalysisDto
{
    public string ServiceName { get; set; } = string.Empty;
    public DateTime AnalysisDate { get; set; }
    public CapacityCurrentState CurrentState { get; set; } = new();
    public CapacityProjection Projection { get; set; } = new();
    public List<CapacityRecommendation> Recommendations { get; set; } = new();
    public CapacityRiskAssessment RiskAssessment { get; set; } = new();
    public List<CapacityMetricAnalysis> MetricAnalysis { get; set; } = new();
}

public class CapacityCurrentState
{
    public double CpuUtilizationPercentage { get; set; }
    public double MemoryUtilizationPercentage { get; set; }
    public double DiskUtilizationPercentage { get; set; }
    public double NetworkUtilizationPercentage { get; set; }
    public int CurrentInstances { get; set; }
    public double CurrentThroughput { get; set; }
    public double AverageResponseTime { get; set; }
}

public class CapacityProjection
{
    public int ProjectionDays { get; set; }
    public double ProjectedCpuUtilization { get; set; }
    public double ProjectedMemoryUtilization { get; set; }
    public double ProjectedDiskUtilization { get; set; }
    public double ProjectedThroughput { get; set; }
    public DateTime EstimatedCapacityExhaustionDate { get; set; }
    public double GrowthRate { get; set; }
}

public class CapacityRecommendation
{
    public string RecommendationType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Priority { get; set; } = string.Empty;
    public DateTime RecommendedImplementationDate { get; set; }
    public Dictionary<string, object> Parameters { get; set; } = new();
    public double EstimatedImpact { get; set; }
    public string ImpactDescription { get; set; } = string.Empty;
}

public class CapacityRiskAssessment
{
    public string RiskLevel { get; set; } = string.Empty;
    public double RiskScore { get; set; }
    public List<string> RiskFactors { get; set; } = new();
    public DateTime? EstimatedRiskMaterializationDate { get; set; }
    public List<string> MitigationStrategies { get; set; } = new();
}

public class CapacityMetricAnalysis
{
    public string MetricName { get; set; } = string.Empty;
    public double CurrentValue { get; set; }
    public double ThresholdValue { get; set; }
    public double UtilizationPercentage { get; set; }
    public string Trend { get; set; } = string.Empty;
    public DateTime? EstimatedThresholdBreachDate { get; set; }
    public string Status { get; set; } = string.Empty;
}
