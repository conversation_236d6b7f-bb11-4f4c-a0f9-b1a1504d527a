using AuditCompliance.Application.DTOs;
using AuditCompliance.Domain.Enums;

namespace AuditCompliance.Application.Interfaces;

public interface IAuditService
{
    Task<Guid> LogEventAsync(CreateAuditLogDto auditLogDto, CancellationToken cancellationToken = default);
    Task<AuditTrailResultDto> GetAuditTrailAsync(AuditTrailQueryDto query, CancellationToken cancellationToken = default);
    Task<List<AuditLogDto>> GetSecurityEventsAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default);
    Task<int> CleanupExpiredAuditLogsAsync(CancellationToken cancellationToken = default);
}

public interface IComplianceService
{
    Task<Guid> CreateReportAsync(CreateComplianceReportDto reportDto, Guid generatedBy, string generatedByName, CancellationToken cancellationToken = default);
    Task<ComplianceReportDto?> GetReportAsync(Guid reportId, CancellationToken cancellationToken = default);
    Task<ComplianceReportResultDto> GetReportsAsync(ComplianceReportQueryDto query, CancellationToken cancellationToken = default);
    Task<List<ComplianceSummaryDto>> GetComplianceSummaryAsync(CancellationToken cancellationToken = default);
    Task StartReportProcessingAsync(Guid reportId, CancellationToken cancellationToken = default);
    Task CompleteReportAsync(Guid reportId, string? findings = null, string? recommendations = null, CancellationToken cancellationToken = default);
    Task<ComplianceReportDto> GenerateAutomatedReportAsync(ComplianceStandard standard, DateTime periodStart, DateTime periodEnd, CancellationToken cancellationToken = default);
}

public interface IRatingService
{
    Task<Guid> CreateRatingAsync(CreateServiceProviderRatingDto ratingDto, Guid shipperId, string shipperName, CancellationToken cancellationToken = default);
    Task<ServiceProviderRatingDto?> GetRatingAsync(Guid ratingId, CancellationToken cancellationToken = default);
    Task<RatingResultDto> GetRatingsAsync(RatingQueryDto query, CancellationToken cancellationToken = default);
    Task<TransportCompanyRatingSummaryDto> GetTransportCompanyRatingSummaryAsync(Guid transportCompanyId, CancellationToken cancellationToken = default);
    Task SubmitRatingAsync(Guid ratingId, CancellationToken cancellationToken = default);
    Task FlagRatingAsync(Guid ratingId, string reason, CancellationToken cancellationToken = default);
    Task RemoveRatingAsync(Guid ratingId, string reason, CancellationToken cancellationToken = default);
    Task<Guid> ReportServiceIssueAsync(ReportServiceIssueDto issueDto, CancellationToken cancellationToken = default);
    Task ResolveServiceIssueAsync(Guid issueId, string resolution, Guid resolvedBy, string resolvedByName, CancellationToken cancellationToken = default);
}

public interface IPreferredProviderService
{
    Task<List<PreferredProviderNetworkDto>> GetPreferredProvidersAsync(Guid shipperId, CancellationToken cancellationToken = default);
    Task<Guid> AddPreferredProviderAsync(Guid shipperId, string shipperName, Guid transportCompanyId, string transportCompanyName, CancellationToken cancellationToken = default);
    Task RemovePreferredProviderAsync(Guid shipperId, Guid transportCompanyId, string reason, CancellationToken cancellationToken = default);
    Task UpdatePreferredProviderStatisticsAsync(Guid shipperId, Guid transportCompanyId, decimal averageRating, int totalOrders, int completedOrders, DateTime lastOrderDate, CancellationToken cancellationToken = default);
    Task ReorderPreferredProvidersAsync(Guid shipperId, List<Guid> transportCompanyIds, CancellationToken cancellationToken = default);
}
