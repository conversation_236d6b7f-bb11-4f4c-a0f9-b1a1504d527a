using Microsoft.Extensions.Logging;
using AnalyticsBIService.Application.Interfaces;
using AnalyticsBIService.Domain.Repositories;
using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Enums;
using AnalyticsBIService.Application.DTOs;
using System.Text;
using System.Text.Json;

namespace AnalyticsBIService.Infrastructure.Services;

/// <summary>
/// Report generation service implementation
/// </summary>
public class ReportGenerationService : IReportGenerationService
{
    private readonly IReportRepository _reportRepository;
    private readonly AnalyticsBIService.Domain.Repositories.IReportTemplateRepository _reportTemplateRepository;
    private readonly IDataAggregationService _dataAggregationService;
    private readonly IFileStorageService _fileStorageService;
    private readonly ILogger<ReportGenerationService> _logger;

    public ReportGenerationService(
        IReportRepository reportRepository,
        AnalyticsBIService.Domain.Repositories.IReportTemplateRepository reportTemplateRepository,
        IDataAggregationService dataAggregationService,
        IFileStorageService fileStorageService,
        ILogger<ReportGenerationService> logger)
    {
        _reportRepository = reportRepository;
        _reportTemplateRepository = reportTemplateRepository;
        _dataAggregationService = dataAggregationService;
        _fileStorageService = fileStorageService;
        _logger = logger;
    }

    /// <summary>
    /// Generate report based on template
    /// </summary>
    public async Task<string> GenerateReportAsync(
        Guid reportId,
        ExportFormat format,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var report = await _reportRepository.GetByIdAsync(reportId, cancellationToken);
            if (report == null)
                throw new ArgumentException($"Report with ID {reportId} not found");

            var reportData = await CollectReportDataAsync(report, cancellationToken);

            return format switch
            {
                ExportFormat.PDF => await GeneratePdfReportAsync(report, reportData, cancellationToken),
                ExportFormat.Excel => await GenerateExcelReportAsync(report, reportData, cancellationToken),
                ExportFormat.CSV => await GenerateCsvReportAsync(report, reportData, cancellationToken),
                ExportFormat.JSON => await GenerateJsonReportAsync(report, reportData, cancellationToken),
                ExportFormat.XML => await GenerateXmlReportAsync(report, reportData, cancellationToken),
                _ => throw new ArgumentException($"Unsupported export format: {format}")
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating report {ReportId} in format {Format}", reportId, format);
            throw;
        }
    }

    /// <summary>
    /// Generate scheduled report
    /// </summary>
    public async Task<string> GenerateScheduledReportAsync(
        Guid reportId,
        Dictionary<string, object> parameters,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var report = await _reportRepository.GetByIdAsync(reportId, cancellationToken);
            if (report == null)
                throw new ArgumentException($"Report with ID {reportId} not found");

            // Apply parameters to report configuration
            var reportData = await CollectReportDataWithParametersAsync(report, parameters, cancellationToken);

            // Generate in default format (PDF)
            var filePath = await GeneratePdfReportAsync(report, reportData, cancellationToken);

            // Update report execution status
            await UpdateReportExecutionAsync(reportId, ReportStatus.Completed, filePath, cancellationToken);

            return filePath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating scheduled report {ReportId}", reportId);
            await UpdateReportExecutionAsync(reportId, ReportStatus.Failed, null, cancellationToken);
            throw;
        }
    }

    /// <summary>
    /// Validate report template
    /// </summary>
    public async Task<bool> ValidateReportTemplateAsync(
        Guid reportId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var report = await _reportRepository.GetByIdAsync(reportId, cancellationToken);
            if (report == null)
                return false;

            // Validate report structure
            if (string.IsNullOrEmpty(report.Name) || string.IsNullOrEmpty(report.Description))
                return false;

            // Validate report sections
            if (report.Sections == null || !report.Sections.Any())
                return false;

            // Validate each section
            foreach (var section in report.Sections)
            {
                if (string.IsNullOrEmpty(section.Name) || string.IsNullOrEmpty(section.Query))
                    return false;
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating report template {ReportId}", reportId);
            return false;
        }
    }

    #region Private Methods

    private async Task<Dictionary<string, object>> CollectReportDataAsync(
        Report report,
        CancellationToken cancellationToken)
    {
        var reportData = new Dictionary<string, object>();

        foreach (var section in report.Sections)
        {
            try
            {
                var sectionData = await ExecuteReportSectionAsync(section, cancellationToken);
                reportData[section.Name] = sectionData;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error executing report section {SectionName}", section.Name);
                reportData[section.Name] = new { Error = ex.Message };
            }
        }

        return reportData;
    }

    private async Task<Dictionary<string, object>> CollectReportDataWithParametersAsync(
        Report report,
        Dictionary<string, object> parameters,
        CancellationToken cancellationToken)
    {
        var reportData = new Dictionary<string, object>();

        foreach (var section in report.Sections)
        {
            try
            {
                var sectionData = await ExecuteReportSectionWithParametersAsync(section, parameters, cancellationToken);
                reportData[section.Name] = sectionData;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error executing report section {SectionName} with parameters", section.Name);
                reportData[section.Name] = new { Error = ex.Message };
            }
        }

        return reportData;
    }

    private async Task<object> ExecuteReportSectionAsync(
        ReportSection section,
        CancellationToken cancellationToken)
    {
        // This is a simplified implementation
        // In a real scenario, you would parse the query and execute it against the appropriate data source

        return new
        {
            SectionName = section.Name,
            Query = section.Query,
            ExecutedAt = DateTime.UtcNow,
            Data = "Sample data - implement actual query execution"
        };
    }

    private async Task<object> ExecuteReportSectionWithParametersAsync(
        ReportSection section,
        Dictionary<string, object> parameters,
        CancellationToken cancellationToken)
    {
        // Apply parameters to the query
        var parameterizedQuery = ApplyParametersToQuery(section.Query, parameters);

        return new
        {
            SectionName = section.Name,
            Query = parameterizedQuery,
            Parameters = parameters,
            ExecutedAt = DateTime.UtcNow,
            Data = "Sample data with parameters - implement actual query execution"
        };
    }

    private string ApplyParametersToQuery(string query, Dictionary<string, object> parameters)
    {
        var result = query;

        foreach (var parameter in parameters)
        {
            result = result.Replace($"@{parameter.Key}", parameter.Value?.ToString() ?? "NULL");
        }

        return result;
    }

    private async Task<string> GeneratePdfReportAsync(
        Report report,
        Dictionary<string, object> reportData,
        CancellationToken cancellationToken)
    {
        // Simplified PDF generation - implement with actual PDF library
        var content = GenerateReportContent(report, reportData);
        var fileName = $"report_{report.Id}_{DateTime.UtcNow:yyyyMMddHHmmss}.pdf";

        return await _fileStorageService.SaveFileAsync(fileName, Encoding.UTF8.GetBytes(content), "application/pdf", cancellationToken);
    }

    private async Task<string> GenerateExcelReportAsync(
        Report report,
        Dictionary<string, object> reportData,
        CancellationToken cancellationToken)
    {
        // Simplified Excel generation - implement with actual Excel library
        var content = GenerateReportContent(report, reportData);
        var fileName = $"report_{report.Id}_{DateTime.UtcNow:yyyyMMddHHmmss}.xlsx";

        return await _fileStorageService.SaveFileAsync(fileName, Encoding.UTF8.GetBytes(content), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", cancellationToken);
    }

    private async Task<string> GenerateCsvReportAsync(
        Report report,
        Dictionary<string, object> reportData,
        CancellationToken cancellationToken)
    {
        var content = GenerateCsvContent(reportData);
        var fileName = $"report_{report.Id}_{DateTime.UtcNow:yyyyMMddHHmmss}.csv";

        return await _fileStorageService.SaveFileAsync(fileName, Encoding.UTF8.GetBytes(content), "text/csv", cancellationToken);
    }

    private async Task<string> GenerateJsonReportAsync(
        Report report,
        Dictionary<string, object> reportData,
        CancellationToken cancellationToken)
    {
        var content = JsonSerializer.Serialize(reportData, new JsonSerializerOptions { WriteIndented = true });
        var fileName = $"report_{report.Id}_{DateTime.UtcNow:yyyyMMddHHmmss}.json";

        return await _fileStorageService.SaveFileAsync(fileName, Encoding.UTF8.GetBytes(content), "application/json", cancellationToken);
    }

    private async Task<string> GenerateXmlReportAsync(
        Report report,
        Dictionary<string, object> reportData,
        CancellationToken cancellationToken)
    {
        var content = GenerateXmlContent(reportData);
        var fileName = $"report_{report.Id}_{DateTime.UtcNow:yyyyMMddHHmmss}.xml";

        return await _fileStorageService.SaveFileAsync(fileName, Encoding.UTF8.GetBytes(content), "application/xml", cancellationToken);
    }

    private string GenerateReportContent(Report report, Dictionary<string, object> reportData)
    {
        var sb = new StringBuilder();
        sb.AppendLine($"Report: {report.Name}");
        sb.AppendLine($"Description: {report.Description}");
        sb.AppendLine($"Generated: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC");
        sb.AppendLine();

        foreach (var section in reportData)
        {
            sb.AppendLine($"Section: {section.Key}");
            sb.AppendLine($"Data: {JsonSerializer.Serialize(section.Value)}");
            sb.AppendLine();
        }

        return sb.ToString();
    }

    private string GenerateCsvContent(Dictionary<string, object> reportData)
    {
        var sb = new StringBuilder();
        sb.AppendLine("Section,Data");

        foreach (var section in reportData)
        {
            sb.AppendLine($"{section.Key},\"{JsonSerializer.Serialize(section.Value).Replace("\"", "\"\"")}\"");
        }

        return sb.ToString();
    }

    private string GenerateXmlContent(Dictionary<string, object> reportData)
    {
        var sb = new StringBuilder();
        sb.AppendLine("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
        sb.AppendLine("<Report>");

        foreach (var section in reportData)
        {
            sb.AppendLine($"  <Section name=\"{section.Key}\">");
            sb.AppendLine($"    <Data>{JsonSerializer.Serialize(section.Value)}</Data>");
            sb.AppendLine("  </Section>");
        }

        sb.AppendLine("</Report>");
        return sb.ToString();
    }

    private async Task UpdateReportExecutionAsync(
        Guid reportId,
        ReportStatus status,
        string? filePath,
        CancellationToken cancellationToken)
    {
        try
        {
            var report = await _reportRepository.GetByIdAsync(reportId, cancellationToken);
            if (report != null)
            {
                report.UpdateStatus(status);
                if (!string.IsNullOrEmpty(filePath))
                {
                    // Update report with file path - this would typically be in a ReportExecution entity
                }
                await _reportRepository.UpdateAsync(report, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating report execution status for report {ReportId}", reportId);
        }
    }

    #endregion

    #region IReportGenerationService Implementation

    /// <summary>
    /// Generate order report from data
    /// </summary>
    public async Task<string> GenerateOrderReportAsync(List<OrderDataDto> orders, Guid templateId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Generating order report for {OrderCount} orders using template {TemplateId}", orders.Count, templateId);

            var template = await _reportTemplateRepository.GetByIdAsync(templateId, cancellationToken);
            if (template == null)
            {
                throw new ArgumentException($"Report template {templateId} not found");
            }

            var reportContent = new StringBuilder();
            reportContent.AppendLine($"# Order Report - {template.Name}");
            reportContent.AppendLine($"Generated: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC");
            reportContent.AppendLine($"Total Orders: {orders.Count}");
            reportContent.AppendLine();

            foreach (var order in orders)
            {
                reportContent.AppendLine($"Order: {order.OrderNumber}");
                reportContent.AppendLine($"  Customer: {order.CustomerName}");
                reportContent.AppendLine($"  Date: {order.OrderDate:yyyy-MM-dd}");
                reportContent.AppendLine($"  Value: ${order.OrderValue:N2}");
                reportContent.AppendLine($"  Status: {order.Status}");
                reportContent.AppendLine($"  Route: {order.Origin} → {order.Destination}");
                reportContent.AppendLine();
            }

            var result = reportContent.ToString();
            _logger.LogInformation("Order report generated successfully with {Length} characters", result.Length);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating order report for template {TemplateId}", templateId);
            throw;
        }
    }

    /// <summary>
    /// Generate trip report from data
    /// </summary>
    public async Task<string> GenerateTripReportAsync(List<TripDataDto> trips, Guid templateId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Generating trip report for {TripCount} trips using template {TemplateId}", trips.Count, templateId);

            var template = await _reportTemplateRepository.GetByIdAsync(templateId, cancellationToken);
            if (template == null)
            {
                throw new ArgumentException($"Report template {templateId} not found");
            }

            var reportContent = new StringBuilder();
            reportContent.AppendLine($"# Trip Report - {template.Name}");
            reportContent.AppendLine($"Generated: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC");
            reportContent.AppendLine($"Total Trips: {trips.Count}");
            reportContent.AppendLine();

            foreach (var trip in trips)
            {
                reportContent.AppendLine($"Trip: {trip.TripNumber}");
                reportContent.AppendLine($"  Driver: {trip.DriverName}");
                reportContent.AppendLine($"  Start: {trip.StartTime:yyyy-MM-dd HH:mm}");
                reportContent.AppendLine($"  End: {trip.EndTime?.ToString("yyyy-MM-dd HH:mm") ?? "In Progress"}");
                reportContent.AppendLine($"  Status: {trip.Status}");
                reportContent.AppendLine($"  Route: {trip.Origin} → {trip.Destination}");
                reportContent.AppendLine($"  Distance: {trip.Distance:N2} km");
                reportContent.AppendLine($"  Fuel: {trip.FuelConsumed:N2} L");
                reportContent.AppendLine($"  Cost: ${trip.Cost:N2}");
                reportContent.AppendLine();
            }

            var result = reportContent.ToString();
            _logger.LogInformation("Trip report generated successfully with {Length} characters", result.Length);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating trip report for template {TemplateId}", templateId);
            throw;
        }
    }

    /// <summary>
    /// Generate timeline report from data
    /// </summary>
    public async Task<string> GenerateTimelineReportAsync(List<OrderTimelineDto> timeline, Guid templateId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Generating timeline report for {TimelineCount} events using template {TemplateId}", timeline.Count, templateId);

            var template = await _reportTemplateRepository.GetByIdAsync(templateId, cancellationToken);
            if (template == null)
            {
                throw new ArgumentException($"Report template {templateId} not found");
            }

            var reportContent = new StringBuilder();
            reportContent.AppendLine($"# Timeline Report - {template.Name}");
            reportContent.AppendLine($"Generated: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC");
            reportContent.AppendLine($"Total Events: {timeline.Count}");
            reportContent.AppendLine();

            var sortedTimeline = timeline.OrderBy(t => t.EventTimestamp).ToList();
            foreach (var timelineEvent in sortedTimeline)
            {
                reportContent.AppendLine($"{timelineEvent.EventTimestamp:yyyy-MM-dd HH:mm:ss} - {timelineEvent.EventType}");
                reportContent.AppendLine($"  Order ID: {timelineEvent.OrderId}");
                reportContent.AppendLine($"  Description: {timelineEvent.Description}");
                reportContent.AppendLine($"  Event By: {timelineEvent.EventBy}");
                if (timelineEvent.EventData.Any())
                {
                    reportContent.AppendLine($"  Event Data: {string.Join(", ", timelineEvent.EventData.Select(kv => $"{kv.Key}: {kv.Value}"))}");
                }
                reportContent.AppendLine();
            }

            var result = reportContent.ToString();
            _logger.LogInformation("Timeline report generated successfully with {Length} characters", result.Length);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating timeline report for template {TemplateId}", templateId);
            throw;
        }
    }

    /// <summary>
    /// Generate performance report from data
    /// </summary>
    public async Task<string> GeneratePerformanceReportAsync(List<PerformanceDataDto> performance, Guid templateId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Generating performance report for {MetricCount} metrics using template {TemplateId}", performance.Count, templateId);

            var template = await _reportTemplateRepository.GetByIdAsync(templateId, cancellationToken);
            if (template == null)
            {
                throw new ArgumentException($"Report template {templateId} not found");
            }

            var reportContent = new StringBuilder();
            reportContent.AppendLine($"# Performance Report - {template.Name}");
            reportContent.AppendLine($"Generated: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC");
            reportContent.AppendLine($"Total Metrics: {performance.Count}");
            reportContent.AppendLine();

            var groupedMetrics = performance.GroupBy(p => p.MetricType);
            foreach (var group in groupedMetrics)
            {
                reportContent.AppendLine($"## {group.Key} Metrics");
                foreach (var metric in group.OrderBy(m => m.MeasuredAt))
                {
                    reportContent.AppendLine($"  {metric.MetricName}: {metric.Value:N2}");
                    reportContent.AppendLine($"    Measured: {metric.MeasuredAt:yyyy-MM-dd HH:mm}");
                    reportContent.AppendLine($"    Entity: {metric.EntityType}");
                }
                reportContent.AppendLine();
            }

            var result = reportContent.ToString();
            _logger.LogInformation("Performance report generated successfully with {Length} characters", result.Length);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating performance report for template {TemplateId}", templateId);
            throw;
        }
    }

    /// <summary>
    /// Generate CSV format from data
    /// </summary>
    public async Task<byte[]> GenerateCSVAsync(object data, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Generating CSV from data of type {DataType}", data.GetType().Name);

            var csvContent = new StringBuilder();

            if (data is IEnumerable<object> enumerable)
            {
                var items = enumerable.ToList();
                if (items.Any())
                {
                    // Get headers from first item properties
                    var firstItem = items.First();
                    var properties = firstItem.GetType().GetProperties();
                    csvContent.AppendLine(string.Join(",", properties.Select(p => $"\"{p.Name}\"")));

                    // Add data rows
                    foreach (var item in items)
                    {
                        var values = properties.Select(p =>
                        {
                            var value = p.GetValue(item);
                            return value?.ToString()?.Replace("\"", "\"\"") ?? "";
                        });
                        csvContent.AppendLine(string.Join(",", values.Select(v => $"\"{v}\"")));
                    }
                }
            }
            else
            {
                // Single object - convert to single row CSV
                var properties = data.GetType().GetProperties();
                csvContent.AppendLine(string.Join(",", properties.Select(p => $"\"{p.Name}\"")));
                var values = properties.Select(p =>
                {
                    var value = p.GetValue(data);
                    return value?.ToString()?.Replace("\"", "\"\"") ?? "";
                });
                csvContent.AppendLine(string.Join(",", values.Select(v => $"\"{v}\"")));
            }

            var result = Encoding.UTF8.GetBytes(csvContent.ToString());
            _logger.LogInformation("CSV generated successfully with {Size} bytes", result.Length);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating CSV from data");
            throw;
        }
    }

    /// <summary>
    /// Generate Excel format from data
    /// </summary>
    public async Task<byte[]> GenerateExcelAsync(object data, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Generating Excel from data of type {DataType}", data.GetType().Name);

            // For now, generate CSV format as Excel is not implemented
            // In a real implementation, you would use a library like EPPlus or ClosedXML
            var csvData = await GenerateCSVAsync(data, cancellationToken);

            _logger.LogInformation("Excel generated successfully with {Size} bytes (CSV format)", csvData.Length);
            return csvData;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating Excel from data");
            throw;
        }
    }

    /// <summary>
    /// Generate PDF format from data
    /// </summary>
    public async Task<byte[]> GeneratePDFAsync(object data, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Generating PDF from data of type {DataType}", data.GetType().Name);

            // For now, generate text content as PDF is not implemented
            // In a real implementation, you would use a library like iTextSharp or PdfSharp
            var textContent = JsonSerializer.Serialize(data, new JsonSerializerOptions { WriteIndented = true });
            var pdfContent = $"PDF Report\n\nGenerated: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC\n\nData:\n{textContent}";

            var result = Encoding.UTF8.GetBytes(pdfContent);
            _logger.LogInformation("PDF generated successfully with {Size} bytes (text format)", result.Length);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating PDF from data");
            throw;
        }
    }

    /// <summary>
    /// Generate JSON format from data
    /// </summary>
    public async Task<byte[]> GenerateJSONAsync(object data, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Generating JSON from data of type {DataType}", data.GetType().Name);

            var jsonContent = JsonSerializer.Serialize(data, new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            var result = Encoding.UTF8.GetBytes(jsonContent);
            _logger.LogInformation("JSON generated successfully with {Size} bytes", result.Length);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating JSON from data");
            throw;
        }
    }

    /// <summary>
    /// Generate preview content for report template
    /// </summary>
    public async Task<string> GeneratePreviewContentAsync(AnalyticsBIService.Application.DTOs.ReportTemplateDto template, object sampleData, Dictionary<string, object> parameters, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Generating preview content for template {TemplateName}", template.Name);

            var previewContent = new StringBuilder();
            previewContent.AppendLine($"# Preview: {template.Name}");
            previewContent.AppendLine($"Description: {template.Description}");
            previewContent.AppendLine($"Category: {template.Category}");
            previewContent.AppendLine($"Generated: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC");
            previewContent.AppendLine();

            if (parameters.Any())
            {
                previewContent.AppendLine("## Parameters:");
                foreach (var param in parameters)
                {
                    previewContent.AppendLine($"  {param.Key}: {param.Value}");
                }
                previewContent.AppendLine();
            }

            previewContent.AppendLine("## Sample Data:");
            var sampleJson = JsonSerializer.Serialize(sampleData, new JsonSerializerOptions { WriteIndented = true });
            previewContent.AppendLine(sampleJson);

            var result = previewContent.ToString();
            _logger.LogInformation("Preview content generated successfully with {Length} characters", result.Length);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating preview content for template {TemplateName}", template.Name);
            throw;
        }
    }

    /// <summary>
    /// Generate report from template with parameters
    /// </summary>
    public async Task<string> GenerateReportFromTemplateAsync(Guid templateId, Dictionary<string, object> parameters, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Generating report from template {TemplateId} with {ParameterCount} parameters", templateId, parameters.Count);

            var template = await _reportTemplateRepository.GetByIdAsync(templateId, cancellationToken);
            if (template == null)
            {
                throw new ArgumentException($"Report template {templateId} not found");
            }

            var reportContent = new StringBuilder();
            reportContent.AppendLine($"# {template.Name}");
            reportContent.AppendLine($"Description: {template.Description}");
            reportContent.AppendLine($"Generated: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC");
            reportContent.AppendLine();

            if (parameters.Any())
            {
                reportContent.AppendLine("## Parameters:");
                foreach (var param in parameters)
                {
                    reportContent.AppendLine($"  {param.Key}: {param.Value}");
                }
                reportContent.AppendLine();
            }

            reportContent.AppendLine("## Report Content:");
            reportContent.AppendLine(template.Content ?? "No content available");

            var result = reportContent.ToString();
            _logger.LogInformation("Report generated successfully from template with {Length} characters", result.Length);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating report from template {TemplateId}", templateId);
            throw;
        }
    }

    /// <summary>
    /// Generate report content from template and data
    /// </summary>
    public async Task<string> GenerateReportContentAsync(AnalyticsBIService.Application.DTOs.ReportTemplateDto template, Dictionary<string, object> data, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Generating report content for template {TemplateName} with {DataCount} data items", template.Name, data.Count);

            var reportContent = new StringBuilder();
            reportContent.AppendLine($"# {template.Name}");
            reportContent.AppendLine($"Description: {template.Description}");
            reportContent.AppendLine($"Category: {template.Category}");
            reportContent.AppendLine($"Generated: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC");
            reportContent.AppendLine();

            if (data.Any())
            {
                reportContent.AppendLine("## Data:");
                foreach (var item in data)
                {
                    reportContent.AppendLine($"  {item.Key}: {item.Value}");
                }
                reportContent.AppendLine();
            }

            if (template.Parameters.Any())
            {
                reportContent.AppendLine("## Template Parameters:");
                foreach (var param in template.Parameters)
                {
                    reportContent.AppendLine($"  {param.Name}: {param.Description} (Type: {param.Type})");
                }
                reportContent.AppendLine();
            }

            reportContent.AppendLine("## Report Content:");
            // In a real implementation, you would process the template content with the data
            reportContent.AppendLine("Report content would be generated here based on template and data.");

            var result = reportContent.ToString();
            _logger.LogInformation("Report content generated successfully with {Length} characters", result.Length);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating report content for template {TemplateName}", template.Name);
            throw;
        }
    }

    #endregion
}
