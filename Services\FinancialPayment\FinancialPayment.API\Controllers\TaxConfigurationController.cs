using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Application.DTOs;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class TaxConfigurationController : ControllerBase
{
    private readonly ITaxConfigurationService _taxConfigurationService;
    private readonly ILogger<TaxConfigurationController> _logger;

    public TaxConfigurationController(
        ITaxConfigurationService taxConfigurationService,
        ILogger<TaxConfigurationController> logger)
    {
        _taxConfigurationService = taxConfigurationService;
        _logger = logger;
    }

    /// <summary>
    /// Create a new tax configuration
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<Guid>> CreateTaxConfiguration([FromBody] CreateTaxConfigurationDto request)
    {
        try
        {
            var jurisdiction = new TaxJurisdiction(
                request.Jurisdiction.Country,
                request.Jurisdiction.State,
                request.Jurisdiction.City,
                request.Jurisdiction.Type);

            var taxConfiguration = await _taxConfigurationService.CreateTaxConfigurationAsync(
                request.Name,
                request.Description,
                jurisdiction,
                request.EffectiveFrom,
                request.CreatedBy,
                request.EffectiveTo,
                request.IsDefault,
                request.Priority);

            return CreatedAtAction(nameof(GetTaxConfiguration), new { id = taxConfiguration.Id }, taxConfiguration.Id);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while creating tax configuration");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating tax configuration");
            return StatusCode(500, "An error occurred while creating tax configuration");
        }
    }

    /// <summary>
    /// Get tax configuration by ID
    /// </summary>
    [HttpGet("{id}")]
    [Authorize(Roles = "Admin,Finance")]
    public async Task<ActionResult<TaxConfigurationDto>> GetTaxConfiguration(Guid id)
    {
        try
        {
            var taxConfiguration = await _taxConfigurationService.GetTaxConfigurationAsync(id);
            if (taxConfiguration == null)
                return NotFound($"Tax configuration {id} not found");

            var dto = MapToDto(taxConfiguration);
            return Ok(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving tax configuration {Id}", id);
            return StatusCode(500, "An error occurred while retrieving tax configuration");
        }
    }

    /// <summary>
    /// Get all active tax configurations
    /// </summary>
    [HttpGet("active")]
    [Authorize(Roles = "Admin,Finance")]
    public async Task<ActionResult<List<TaxConfigurationDto>>> GetActiveTaxConfigurations()
    {
        try
        {
            var configurations = await _taxConfigurationService.GetActiveTaxConfigurationsAsync();
            var dtos = configurations.Select(MapToDto).ToList();
            return Ok(dtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving active tax configurations");
            return StatusCode(500, "An error occurred while retrieving tax configurations");
        }
    }

    /// <summary>
    /// Get default tax configuration
    /// </summary>
    [HttpGet("default")]
    [Authorize(Roles = "Admin,Finance")]
    public async Task<ActionResult<TaxConfigurationDto>> GetDefaultTaxConfiguration()
    {
        try
        {
            var configuration = await _taxConfigurationService.GetDefaultTaxConfigurationAsync();
            if (configuration == null)
                return NotFound("No default tax configuration found");

            var dto = MapToDto(configuration);
            return Ok(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving default tax configuration");
            return StatusCode(500, "An error occurred while retrieving default tax configuration");
        }
    }

    /// <summary>
    /// Update tax configuration basic details
    /// </summary>
    [HttpPut("{id}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<TaxConfigurationDto>> UpdateTaxConfiguration(Guid id, [FromBody] UpdateTaxConfigurationDto request)
    {
        try
        {
            var jurisdiction = new TaxJurisdiction(
                request.Jurisdiction.Country,
                request.Jurisdiction.State,
                request.Jurisdiction.City,
                request.Jurisdiction.Type);

            var updatedConfiguration = await _taxConfigurationService.UpdateTaxConfigurationAsync(
                id,
                request.Name,
                request.Description,
                jurisdiction,
                request.EffectiveFrom,
                request.EffectiveTo,
                request.ModifiedBy,
                request.Priority);

            var dto = MapToDto(updatedConfiguration);
            return Ok(dto);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while updating tax configuration {Id}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating tax configuration {Id}", id);
            return StatusCode(500, "An error occurred while updating tax configuration");
        }
    }

    /// <summary>
    /// Update tax configuration settings
    /// </summary>
    [HttpPut("{id}/settings")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<TaxConfigurationDto>> UpdateTaxSettings(Guid id, [FromBody] UpdateTaxSettingsDto request)
    {
        try
        {
            var updatedConfiguration = await _taxConfigurationService.UpdateTaxSettingsAsync(
                id,
                request.EnableGstCalculation,
                request.EnableTdsCalculation,
                request.EnableReverseCharge,
                request.RequireHsnCode,
                request.DefaultGstRate,
                request.DefaultTdsRate,
                request.DefaultCurrency,
                request.ModifiedBy);

            var dto = MapToDto(updatedConfiguration);
            return Ok(dto);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while updating tax settings {Id}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating tax settings {Id}", id);
            return StatusCode(500, "An error occurred while updating tax settings");
        }
    }

    /// <summary>
    /// Set tax configuration as default
    /// </summary>
    [HttpPost("{id}/set-default")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> SetAsDefault(Guid id, [FromBody] string modifiedBy)
    {
        try
        {
            await _taxConfigurationService.SetAsDefaultTaxConfigurationAsync(id, modifiedBy);
            return Ok();
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while setting tax configuration as default {Id}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting tax configuration as default {Id}", id);
            return StatusCode(500, "An error occurred while setting tax configuration as default");
        }
    }

    /// <summary>
    /// Remove tax configuration as default
    /// </summary>
    [HttpPost("{id}/remove-default")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> RemoveAsDefault(Guid id, [FromBody] string modifiedBy)
    {
        try
        {
            await _taxConfigurationService.RemoveAsDefaultTaxConfigurationAsync(id, modifiedBy);
            return Ok();
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while removing tax configuration as default {Id}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing tax configuration as default {Id}", id);
            return StatusCode(500, "An error occurred while removing tax configuration as default");
        }
    }

    /// <summary>
    /// Activate tax configuration
    /// </summary>
    [HttpPost("{id}/activate")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> ActivateTaxConfiguration(Guid id, [FromBody] string activatedBy)
    {
        try
        {
            await _taxConfigurationService.ActivateTaxConfigurationAsync(id, activatedBy);
            return Ok();
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while activating tax configuration {Id}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error activating tax configuration {Id}", id);
            return StatusCode(500, "An error occurred while activating tax configuration");
        }
    }

    /// <summary>
    /// Deactivate tax configuration
    /// </summary>
    [HttpPost("{id}/deactivate")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> DeactivateTaxConfiguration(Guid id, [FromBody] DeactivateRequest request)
    {
        try
        {
            await _taxConfigurationService.DeactivateTaxConfigurationAsync(id, request.DeactivatedBy, request.Reason);
            return Ok();
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while deactivating tax configuration {Id}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deactivating tax configuration {Id}", id);
            return StatusCode(500, "An error occurred while deactivating tax configuration");
        }
    }

    /// <summary>
    /// Archive tax configuration
    /// </summary>
    [HttpPost("{id}/archive")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> ArchiveTaxConfiguration(Guid id, [FromBody] ArchiveRequest request)
    {
        try
        {
            await _taxConfigurationService.ArchiveTaxConfigurationAsync(id, request.ArchivedBy, request.Reason);
            return Ok();
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while archiving tax configuration {Id}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error archiving tax configuration {Id}", id);
            return StatusCode(500, "An error occurred while archiving tax configuration");
        }
    }

    /// <summary>
    /// Validate tax configuration
    /// </summary>
    [HttpPost("{id}/validate")]
    [Authorize(Roles = "Admin,Finance")]
    public async Task<ActionResult<bool>> ValidateTaxConfiguration(Guid id)
    {
        try
        {
            var isValid = await _taxConfigurationService.ValidateTaxConfigurationAsync(id);
            return Ok(isValid);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating tax configuration {Id}", id);
            return StatusCode(500, "An error occurred while validating tax configuration");
        }
    }

    private static TaxConfigurationDto MapToDto(Domain.Entities.TaxConfiguration taxConfiguration)
    {
        return new TaxConfigurationDto
        {
            Id = taxConfiguration.Id,
            Name = taxConfiguration.Name,
            Description = taxConfiguration.Description,
            Jurisdiction = new TaxJurisdictionDto
            {
                Country = taxConfiguration.Jurisdiction.Country,
                State = taxConfiguration.Jurisdiction.State,
                City = taxConfiguration.Jurisdiction.City,
                Type = taxConfiguration.Jurisdiction.Type
            },
            Status = taxConfiguration.Status,
            EffectiveFrom = taxConfiguration.EffectiveFrom,
            EffectiveTo = taxConfiguration.EffectiveTo,
            IsDefault = taxConfiguration.IsDefault,
            Priority = taxConfiguration.Priority,
            CreatedBy = taxConfiguration.CreatedBy,
            ModifiedBy = taxConfiguration.ModifiedBy,
            ModifiedAt = taxConfiguration.ModifiedAt,
            CreatedAt = taxConfiguration.CreatedAt,
            UpdatedAt = taxConfiguration.UpdatedAt,
            EnableGstCalculation = taxConfiguration.EnableGstCalculation,
            EnableTdsCalculation = taxConfiguration.EnableTdsCalculation,
            EnableReverseCharge = taxConfiguration.EnableReverseCharge,
            RequireHsnCode = taxConfiguration.RequireHsnCode,
            DefaultGstRate = taxConfiguration.DefaultGstRate,
            DefaultTdsRate = taxConfiguration.DefaultTdsRate,
            DefaultCurrency = taxConfiguration.DefaultCurrency,
            History = taxConfiguration.History.Select(h => new TaxConfigurationHistoryDto
            {
                Id = h.Id,
                TaxConfigurationId = h.TaxConfigurationId,
                Action = h.Action,
                Details = h.Details,
                ModifiedBy = h.ModifiedBy,
                ModifiedAt = h.ModifiedAt
            }).ToList(),
            Rules = taxConfiguration.Rules.Select(r => new TaxConfigurationRuleDto
            {
                Id = r.Id,
                TaxConfigurationId = r.TaxConfigurationId,
                Name = r.Name,
                Condition = r.Condition,
                Action = r.Action,
                IsActive = r.IsActive,
                CreatedBy = r.CreatedBy,
                CreatedAt = r.CreatedAt
            }).ToList()
        };
    }
}

public class DeactivateRequest
{
    public string DeactivatedBy { get; set; } = string.Empty;
    public string Reason { get; set; } = string.Empty;
}

public class ArchiveRequest
{
    public string ArchivedBy { get; set; } = string.Empty;
    public string Reason { get; set; } = string.Empty;
}
