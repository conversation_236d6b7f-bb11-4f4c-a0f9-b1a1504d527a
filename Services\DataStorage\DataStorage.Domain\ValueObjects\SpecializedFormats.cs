namespace DataStorage.Domain.ValueObjects;

public class ArchiveContents
{
    public string ArchiveType { get; private set; }
    public long TotalSize { get; private set; }
    public long CompressedSize { get; private set; }
    public double CompressionRatio { get; private set; }
    public int FileCount { get; private set; }
    public int DirectoryCount { get; private set; }
    public List<ArchiveEntry> Entries { get; private set; }
    public bool IsEncrypted { get; private set; }
    public DateTime CreatedDate { get; private set; }

    public ArchiveContents(
        string archiveType,
        long totalSize,
        long compressedSize,
        int fileCount,
        int directoryCount,
        List<ArchiveEntry> entries,
        bool isEncrypted,
        DateTime createdDate)
    {
        ArchiveType = archiveType ?? string.Empty;
        TotalSize = totalSize;
        CompressedSize = compressedSize;
        CompressionRatio = totalSize > 0 ? (double)compressedSize / totalSize : 0;
        FileCount = fileCount;
        DirectoryCount = directoryCount;
        Entries = entries ?? new List<ArchiveEntry>();
        IsEncrypted = isEncrypted;
        CreatedDate = createdDate;
    }
}

public class ArchiveEntry
{
    public string Name { get; private set; }
    public string FullPath { get; private set; }
    public bool IsDirectory { get; private set; }
    public long Size { get; private set; }
    public long CompressedSize { get; private set; }
    public DateTime ModifiedDate { get; private set; }
    public string? Checksum { get; private set; }

    public ArchiveEntry(
        string name,
        string fullPath,
        bool isDirectory,
        long size,
        long compressedSize,
        DateTime modifiedDate,
        string? checksum = null)
    {
        Name = name ?? string.Empty;
        FullPath = fullPath ?? string.Empty;
        IsDirectory = isDirectory;
        Size = size;
        CompressedSize = compressedSize;
        ModifiedDate = modifiedDate;
        Checksum = checksum;
    }
}

public class EmailContent
{
    public string Subject { get; private set; }
    public string From { get; private set; }
    public List<string> To { get; private set; }
    public List<string> Cc { get; private set; }
    public List<string> Bcc { get; private set; }
    public DateTime SentDate { get; private set; }
    public DateTime ReceivedDate { get; private set; }
    public string Body { get; private set; }
    public string HtmlBody { get; private set; }
    public List<EmailAttachment> Attachments { get; private set; }
    public EmailHeaders Headers { get; private set; }
    public bool IsRead { get; private set; }
    public bool HasAttachments { get; private set; }

    public EmailContent(
        string subject,
        string from,
        List<string> to,
        List<string> cc,
        List<string> bcc,
        DateTime sentDate,
        DateTime receivedDate,
        string body,
        string htmlBody,
        List<EmailAttachment> attachments,
        EmailHeaders headers,
        bool isRead)
    {
        Subject = subject ?? string.Empty;
        From = from ?? string.Empty;
        To = to ?? new List<string>();
        Cc = cc ?? new List<string>();
        Bcc = bcc ?? new List<string>();
        SentDate = sentDate;
        ReceivedDate = receivedDate;
        Body = body ?? string.Empty;
        HtmlBody = htmlBody ?? string.Empty;
        Attachments = attachments ?? new List<EmailAttachment>();
        Headers = headers ?? new EmailHeaders();
        IsRead = isRead;
        HasAttachments = Attachments.Any();
    }
}

public class EmailAttachment
{
    public string Name { get; private set; }
    public string ContentType { get; private set; }
    public long Size { get; private set; }
    public string? ContentId { get; private set; }
    public bool IsInline { get; private set; }

    public EmailAttachment(string name, string contentType, long size, string? contentId = null, bool isInline = false)
    {
        Name = name ?? string.Empty;
        ContentType = contentType ?? string.Empty;
        Size = size;
        ContentId = contentId;
        IsInline = isInline;
    }
}

public class EmailHeaders
{
    public string MessageId { get; private set; }
    public string InReplyTo { get; private set; }
    public string References { get; private set; }
    public string ReturnPath { get; private set; }
    public Dictionary<string, string> CustomHeaders { get; private set; }

    public EmailHeaders(
        string messageId = "",
        string inReplyTo = "",
        string references = "",
        string returnPath = "",
        Dictionary<string, string>? customHeaders = null)
    {
        MessageId = messageId;
        InReplyTo = inReplyTo;
        References = references;
        ReturnPath = returnPath;
        CustomHeaders = customHeaders ?? new Dictionary<string, string>();
    }
}

public class DatabaseSchema
{
    public string DatabaseType { get; private set; }
    public string Version { get; private set; }
    public string Name { get; private set; }
    public List<DatabaseTable> Tables { get; private set; }
    public List<DatabaseView> Views { get; private set; }
    public List<DatabaseIndex> Indexes { get; private set; }
    public List<DatabaseConstraint> Constraints { get; private set; }
    public DatabaseStatistics Statistics { get; private set; }

    public DatabaseSchema(
        string databaseType,
        string version,
        string name,
        List<DatabaseTable> tables,
        List<DatabaseView> views,
        List<DatabaseIndex> indexes,
        List<DatabaseConstraint> constraints,
        DatabaseStatistics statistics)
    {
        DatabaseType = databaseType ?? string.Empty;
        Version = version ?? string.Empty;
        Name = name ?? string.Empty;
        Tables = tables ?? new List<DatabaseTable>();
        Views = views ?? new List<DatabaseView>();
        Indexes = indexes ?? new List<DatabaseIndex>();
        Constraints = constraints ?? new List<DatabaseConstraint>();
        Statistics = statistics ?? new DatabaseStatistics();
    }
}

public class DatabaseTable
{
    public string Name { get; private set; }
    public string Schema { get; private set; }
    public List<DatabaseColumn> Columns { get; private set; }
    public long RowCount { get; private set; }
    public long SizeBytes { get; private set; }

    public DatabaseTable(string name, string schema, List<DatabaseColumn> columns, long rowCount, long sizeBytes)
    {
        Name = name ?? string.Empty;
        Schema = schema ?? string.Empty;
        Columns = columns ?? new List<DatabaseColumn>();
        RowCount = rowCount;
        SizeBytes = sizeBytes;
    }
}

public class DatabaseColumn
{
    public string Name { get; private set; }
    public string DataType { get; private set; }
    public bool IsNullable { get; private set; }
    public bool IsPrimaryKey { get; private set; }
    public bool IsForeignKey { get; private set; }
    public string? DefaultValue { get; private set; }

    public DatabaseColumn(
        string name,
        string dataType,
        bool isNullable,
        bool isPrimaryKey,
        bool isForeignKey,
        string? defaultValue = null)
    {
        Name = name ?? string.Empty;
        DataType = dataType ?? string.Empty;
        IsNullable = isNullable;
        IsPrimaryKey = isPrimaryKey;
        IsForeignKey = isForeignKey;
        DefaultValue = defaultValue;
    }
}

public class DatabaseView
{
    public string Name { get; private set; }
    public string Schema { get; private set; }
    public string Definition { get; private set; }

    public DatabaseView(string name, string schema, string definition)
    {
        Name = name ?? string.Empty;
        Schema = schema ?? string.Empty;
        Definition = definition ?? string.Empty;
    }
}

public class DatabaseIndex
{
    public string Name { get; private set; }
    public string TableName { get; private set; }
    public List<string> Columns { get; private set; }
    public bool IsUnique { get; private set; }
    public bool IsClustered { get; private set; }

    public DatabaseIndex(string name, string tableName, List<string> columns, bool isUnique, bool isClustered)
    {
        Name = name ?? string.Empty;
        TableName = tableName ?? string.Empty;
        Columns = columns ?? new List<string>();
        IsUnique = isUnique;
        IsClustered = isClustered;
    }
}

public class DatabaseConstraint
{
    public string Name { get; private set; }
    public string Type { get; private set; }
    public string TableName { get; private set; }
    public List<string> Columns { get; private set; }
    public string? ReferencedTable { get; private set; }
    public List<string> ReferencedColumns { get; private set; }

    public DatabaseConstraint(
        string name,
        string type,
        string tableName,
        List<string> columns,
        string? referencedTable = null,
        List<string>? referencedColumns = null)
    {
        Name = name ?? string.Empty;
        Type = type ?? string.Empty;
        TableName = tableName ?? string.Empty;
        Columns = columns ?? new List<string>();
        ReferencedTable = referencedTable;
        ReferencedColumns = referencedColumns ?? new List<string>();
    }
}

public class DatabaseStatistics
{
    public int TableCount { get; private set; }
    public int ViewCount { get; private set; }
    public int IndexCount { get; private set; }
    public int ConstraintCount { get; private set; }
    public long TotalSizeBytes { get; private set; }

    public DatabaseStatistics(int tableCount = 0, int viewCount = 0, int indexCount = 0, int constraintCount = 0, long totalSizeBytes = 0)
    {
        TableCount = tableCount;
        ViewCount = viewCount;
        IndexCount = indexCount;
        ConstraintCount = constraintCount;
        TotalSizeBytes = totalSizeBytes;
    }
}
