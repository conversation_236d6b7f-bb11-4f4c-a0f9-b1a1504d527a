using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using FinancialPayment.Domain.Entities;

namespace FinancialPayment.Infrastructure.Data.Configurations;

public class SettlementConfiguration : IEntityTypeConfiguration<Settlement>
{
    public void Configure(EntityTypeBuilder<Settlement> builder)
    {
        builder.ToTable("settlements");

        builder.HasKey(s => s.Id);

        builder.Property(s => s.Id)
            .HasColumnName("id")
            .ValueGeneratedNever();

        builder.Property(s => s.OrderId)
            .HasColumnName("order_id")
            .IsRequired();

        builder.Property(s => s.TripId)
            .HasColumnName("trip_id")
            .IsRequired();

        builder.Property(s => s.EscrowAccountId)
            .HasColumnName("escrow_account_id")
            .IsRequired();

        builder.Property(s => s.SettlementNumber)
            .HasColumnName("settlement_number")
            .HasMaxLength(50)
            .IsRequired();

        // Money value object
        builder.OwnsOne(s => s.TotalAmount, money =>
        {
            money.Property(m => m.Amount)
                .HasColumnName("total_amount")
                .HasColumnType("decimal(18,2)")
                .IsRequired();

            money.Property(m => m.Currency)
                .HasColumnName("total_currency")
                .HasMaxLength(3)
                .IsRequired();
        });

        builder.Property(s => s.Status)
            .HasColumnName("status")
            .HasConversion<int>()
            .IsRequired();

        builder.Property(s => s.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(s => s.ProcessedAt)
            .HasColumnName("processed_at");

        builder.Property(s => s.Notes)
            .HasColumnName("notes")
            .HasMaxLength(1000);

        // Relationships
        builder.HasMany(s => s.Distributions)
            .WithOne(d => d.Settlement)
            .HasForeignKey(d => d.SettlementId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(s => s.EscrowAccount)
            .WithMany()
            .HasForeignKey(s => s.EscrowAccountId)
            .OnDelete(DeleteBehavior.Restrict);

        // Indexes
        builder.HasIndex(s => s.OrderId)
            .IsUnique()
            .HasDatabaseName("ix_settlements_order_id");

        builder.HasIndex(s => s.TripId)
            .IsUnique()
            .HasDatabaseName("ix_settlements_trip_id");

        builder.HasIndex(s => s.EscrowAccountId)
            .HasDatabaseName("ix_settlements_escrow_account_id");

        builder.HasIndex(s => s.SettlementNumber)
            .IsUnique()
            .HasDatabaseName("ix_settlements_settlement_number");

        builder.HasIndex(s => s.Status)
            .HasDatabaseName("ix_settlements_status");

        builder.HasIndex(s => s.CreatedAt)
            .HasDatabaseName("ix_settlements_created_at");
    }
}
