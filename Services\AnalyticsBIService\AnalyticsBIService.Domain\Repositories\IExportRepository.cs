using AnalyticsBIService.Domain.Entities;
using Shared.Infrastructure.Repositories;

namespace AnalyticsBIService.Domain.Repositories;

/// <summary>
/// Repository interface for export operations
/// </summary>
public interface IExportRepository : IRepositoryBase<ExportData, Guid>
{
    /// <summary>
    /// Get export data by user ID
    /// </summary>
    Task<IEnumerable<ExportData>> GetByUserIdAsync(Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get export data by status
    /// </summary>
    Task<IEnumerable<ExportData>> GetByStatusAsync(string status, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get export data by date range
    /// </summary>
    Task<IEnumerable<ExportData>> GetByDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get recent exports
    /// </summary>
    Task<IEnumerable<ExportData>> GetRecentExportsAsync(int count = 10, CancellationToken cancellationToken = default);

    /// <summary>
    /// Clean up old export files
    /// </summary>
    Task CleanupOldExportsAsync(DateTime cutoffDate, CancellationToken cancellationToken = default);
}
