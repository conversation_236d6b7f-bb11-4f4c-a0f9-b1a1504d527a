using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using MobileWorkflow.Application.DTOs;
using MobileWorkflow.Domain.Entities;
using MobileWorkflow.Domain.Repositories;
using Shared.Domain.Common;

namespace MobileWorkflow.Application.Commands.MobileApp;

public class CreateMobileAppCommandHandler : IRequestHandler<CreateMobileAppCommand, MobileAppDto>
{
    private readonly IMobileAppRepository _mobileAppRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<CreateMobileAppCommandHandler> _logger;

    public CreateMobileAppCommandHandler(
        IMobileAppRepository mobileAppRepository,
        IMapper mapper,
        ILogger<CreateMobileAppCommandHandler> logger)
    {
        _mobileAppRepository = mobileAppRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<MobileAppDto> Handle(CreateMobileAppCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Creating mobile app: {Name} for platform: {Platform}", request.Name, request.Platform);

        // Check if package ID already exists
        var existingApp = await _mobileAppRepository.GetByPackageIdAsync(request.PackageId, cancellationToken);
        if (existingApp != null)
        {
            throw new InvalidOperationException($"Mobile app with package ID {request.PackageId} already exists");
        }

        // Create mobile app entity
        var mobileApp = new Domain.Entities.MobileApp(
            request.Name,
            request.Version,
            request.Platform,
            request.PackageId,
            request.ReleaseDate,
            request.MinimumOSVersion,
            request.Features,
            request.Configuration,
            request.SupportsOffline,
            request.DownloadUrl,
            request.FileSize,
            request.Checksum);

        // Save to repository
        var savedApp = await _mobileAppRepository.AddAsync(mobileApp, cancellationToken);

        _logger.LogInformation("Mobile app created successfully with ID: {Id}", savedApp.Id);

        return _mapper.Map<MobileAppDto>(savedApp);
    }
}

