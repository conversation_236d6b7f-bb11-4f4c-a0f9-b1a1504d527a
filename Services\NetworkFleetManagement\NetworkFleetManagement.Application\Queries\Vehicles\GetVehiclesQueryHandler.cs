using AutoMapper;
using MediatR;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Repositories;

namespace NetworkFleetManagement.Application.Queries.Vehicles;

public class GetVehiclesQueryHandler : IRequestHandler<GetVehiclesQuery, PagedResult<VehicleSummaryDto>>
{
    private readonly IVehicleRepository _vehicleRepository;
    private readonly IMapper _mapper;

    public GetVehiclesQueryHandler(IVehicleRepository vehicleRepository, IMapper mapper)
    {
        _vehicleRepository = vehicleRepository;
        _mapper = mapper;
    }

    public async Task<PagedResult<VehicleSummaryDto>> Handle(GetVehiclesQuery request, CancellationToken cancellationToken)
    {
        var (vehicles, totalCount) = await _vehicleRepository.GetPagedAsync(
            request.PageNumber,
            request.PageSize,
            request.CarrierId,
            request.Status,
            request.VehicleType,
            request.SearchTerm,
            cancellationToken);

        var vehicleDtos = _mapper.Map<IEnumerable<VehicleSummaryDto>>(vehicles);

        return new PagedResult<VehicleSummaryDto>
        {
            Items = vehicleDtos,
            TotalCount = totalCount,
            PageNumber = request.PageNumber,
            PageSize = request.PageSize
        };
    }
}
