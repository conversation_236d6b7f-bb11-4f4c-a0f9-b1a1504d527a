# Network Configuration Guide

## Overview

This guide covers the network configuration required for secure communication between Server 1 (Infrastructure) and Server 2 (Applications) in the TLI microservices deployment.

## Network Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                        INTERNET                                 │
└─────────────────────┬───────────────────────────────────────────┘
                      │
                      │ HTTPS (443) / HTTP (80)
                      │
┌─────────────────────▼───────────────────────────────────────────┐
│                 Load Balancer / Reverse Proxy                  │
│                    (nginx/HAProxy)                              │
└─────────────────────┬───────────────────────────────────────────┘
                      │
                      │ Internal Network
                      │
┌─────────────────────▼───────────────────────────────────────────┐
│                 SERVER 2 (Applications)                        │
│                    IP: **********                              │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │              API Gateway (5000)                         │   │
│  │              TLI Microservices (5001-5013)              │   │
│  └─────────────────────────────────────────────────────────┘   │
└─────────────────────┬───────────────────────────────────────────┘
                      │
                      │ Private Network (********/24)
                      │
┌─────────────────────▼───────────────────────────────────────────┐
│                SERVER 1 (Infrastructure)                       │
│                    IP: **********                              │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │  PostgreSQL (5432), <PERSON><PERSON> (6379), <PERSON><PERSON><PERSON> (5672)      │   │
│  │  Prometheus (9090), <PERSON><PERSON> (3000)                     │   │
│  └─────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
```

## Step 1: Network Planning

### 1.1 IP Address Assignment
```bash
# Example IP configuration
SERVER1_IP=**********    # Infrastructure Server
SERVER2_IP=**********    # Applications Server
GATEWAY_IP=********      # Network Gateway
SUBNET=********/24       # Private Subnet
```

### 1.2 Port Mapping
```bash
# Server 1 (Infrastructure) - Exposed Ports
POSTGRES_PORT=5432
REDIS_PORT=6379
RABBITMQ_PORT=5672
RABBITMQ_MGMT_PORT=15672
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000

# Server 2 (Applications) - Exposed Ports
API_GATEWAY_PORT=5000
IDENTITY_PORT=5001
USER_MGMT_PORT=5002
SUBSCRIPTION_PORT=5003
ORDER_PORT=5004
TRIP_PORT=5005
FLEET_PORT=5006
PAYMENT_PORT=5007
COMMUNICATION_PORT=5008
ANALYTICS_PORT=5009
DATASTORAGE_PORT=5010
AUDIT_PORT=5011
MONITORING_PORT=5012
MOBILE_PORT=5013
```

## Step 2: Server 1 Network Configuration

### 2.1 Configure Static IP (Server 1)
```bash
# Edit netplan configuration
sudo nano /etc/netplan/01-network-manager-all.yaml

# Add the following configuration:
network:
  version: 2
  renderer: networkd
  ethernets:
    eth0:  # Replace with your interface name
      dhcp4: false
      addresses:
        - **********/24
      gateway4: ********
      nameservers:
        addresses:
          - *******
          - *******

# Apply the configuration
sudo netplan apply

# Verify the configuration
ip addr show
```

### 2.2 Configure Firewall (Server 1)
```bash
# Reset UFW to default
sudo ufw --force reset

# Set default policies
sudo ufw default deny incoming
sudo ufw default allow outgoing

# Allow SSH from anywhere (be careful in production)
sudo ufw allow ssh

# Allow from Server 2 to infrastructure services
sudo ufw allow from ********** to any port 5432 comment 'PostgreSQL from Server 2'
sudo ufw allow from ********** to any port 6379 comment 'Redis from Server 2'
sudo ufw allow from ********** to any port 5672 comment 'RabbitMQ from Server 2'

# Allow monitoring access from admin networks
sudo ufw allow from ********/24 to any port 9090 comment 'Prometheus'
sudo ufw allow from ********/24 to any port 3000 comment 'Grafana'
sudo ufw allow from ********/24 to any port 15672 comment 'RabbitMQ Management'

# Allow ping for connectivity testing
sudo ufw allow from ********/24 to any port 22

# Enable firewall
sudo ufw enable

# Check status
sudo ufw status verbose
```

### 2.3 Configure Host-based Access (Server 1)
```bash
# Update PostgreSQL pg_hba.conf
sudo nano /opt/tli/config/postgres/pg_hba.conf

# Add Server 2 access
echo "host    all             all             **********/32          md5" >> /opt/tli/config/postgres/pg_hba.conf

# Update Redis configuration to allow Server 2
sudo nano /opt/tli/config/redis/redis.conf

# Update bind directive
sed -i 's/bind 0.0.0.0/bind 0.0.0.0 **********/' /opt/tli/config/redis/redis.conf

# Restart services
/opt/tli/manage-infrastructure.sh restart
```

## Step 3: Server 2 Network Configuration

### 3.1 Configure Static IP (Server 2)
```bash
# Edit netplan configuration
sudo nano /etc/netplan/01-network-manager-all.yaml

# Add the following configuration:
network:
  version: 2
  renderer: networkd
  ethernets:
    eth0:  # Replace with your interface name
      dhcp4: false
      addresses:
        - **********/24
      gateway4: ********
      nameservers:
        addresses:
          - *******
          - *******

# Apply the configuration
sudo netplan apply

# Verify the configuration
ip addr show
```

### 3.2 Configure Firewall (Server 2)
```bash
# Reset UFW to default
sudo ufw --force reset

# Set default policies
sudo ufw default deny incoming
sudo ufw default allow outgoing

# Allow SSH
sudo ufw allow ssh

# Allow HTTP and HTTPS
sudo ufw allow 80
sudo ufw allow 443

# Allow API Gateway
sudo ufw allow 5000

# Allow microservices (restrict to admin networks in production)
sudo ufw allow from ********/24 to any port 5001:5013

# Allow from load balancer/reverse proxy if using one
# sudo ufw allow from LOAD_BALANCER_IP to any port 5000:5013

# Enable firewall
sudo ufw enable

# Check status
sudo ufw status verbose
```

### 3.3 Update Environment Configuration (Server 2)
```bash
# Update environment file with correct Server 1 IP
sudo nano /opt/tli/config/tli-environment.env

# Update the following variables:
SERVER1_IP=**********
POSTGRES_HOST=**********
REDIS_HOST=**********
RABBITMQ_HOST=**********
```

## Step 4: Network Connectivity Testing

### 4.1 Test Connectivity from Server 2 to Server 1
```bash
# Create connectivity test script
cat > /opt/tli/test-connectivity.sh << 'EOF'
#!/bin/bash

SERVER1_IP="**********"

echo "=== Network Connectivity Test ==="
echo "Testing connectivity from Server 2 to Server 1"
echo

# Test basic connectivity
echo "1. Basic Connectivity:"
if ping -c 3 $SERVER1_IP > /dev/null 2>&1; then
  echo "  ✓ Server 1 is reachable"
else
  echo "  ✗ Server 1 is not reachable"
  exit 1
fi

# Test PostgreSQL
echo "2. PostgreSQL (Port 5432):"
if nc -z $SERVER1_IP 5432; then
  echo "  ✓ PostgreSQL port is open"
else
  echo "  ✗ PostgreSQL port is not accessible"
fi

# Test Redis
echo "3. Redis (Port 6379):"
if nc -z $SERVER1_IP 6379; then
  echo "  ✓ Redis port is open"
else
  echo "  ✗ Redis port is not accessible"
fi

# Test RabbitMQ
echo "4. RabbitMQ (Port 5672):"
if nc -z $SERVER1_IP 5672; then
  echo "  ✓ RabbitMQ port is open"
else
  echo "  ✗ RabbitMQ port is not accessible"
fi

# Test Prometheus
echo "5. Prometheus (Port 9090):"
if nc -z $SERVER1_IP 9090; then
  echo "  ✓ Prometheus port is open"
else
  echo "  ✗ Prometheus port is not accessible"
fi

# Test Grafana
echo "6. Grafana (Port 3000):"
if nc -z $SERVER1_IP 3000; then
  echo "  ✓ Grafana port is open"
else
  echo "  ✗ Grafana port is not accessible"
fi

echo
echo "=== Connectivity Test Complete ==="
EOF

chmod +x /opt/tli/test-connectivity.sh

# Run the test
/opt/tli/test-connectivity.sh
```

### 4.2 Test Database Connectivity
```bash
# Test PostgreSQL connection
docker run --rm postgres:15 psql -h ********** -U tli_admin -d TLI_Identity -c "SELECT 1;"

# Test Redis connection
docker run --rm redis:7-alpine redis-cli -h ********** -a "TLI_Redis_2024!@#" ping
```

## Step 5: Load Balancer Configuration (Optional)

### 5.1 Install and Configure Nginx
```bash
# Install nginx on Server 2 or separate load balancer
sudo apt update
sudo apt install -y nginx

# Create TLI configuration
sudo tee /etc/nginx/sites-available/tli << 'EOF'
upstream tli_api_gateway {
    server 127.0.0.1:5000;
}

upstream tli_identity {
    server 127.0.0.1:5001;
}

upstream tli_usermanagement {
    server 127.0.0.1:5002;
}

# Add more upstreams for other services as needed

server {
    listen 80;
    server_name tli.yourdomain.com;  # Replace with your domain
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name tli.yourdomain.com;  # Replace with your domain
    
    # SSL Configuration
    ssl_certificate /opt/tli/config/ssl/tli.crt;
    ssl_certificate_key /opt/tli/config/ssl/tli.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";
    
    # API Gateway (main entry point)
    location / {
        proxy_pass http://tli_api_gateway;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # Direct service access (for admin/debugging)
    location /identity/ {
        proxy_pass http://tli_identity/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location /users/ {
        proxy_pass http://tli_usermanagement/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Health check endpoint
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
EOF

# Enable the site
sudo ln -s /etc/nginx/sites-available/tli /etc/nginx/sites-enabled/
sudo rm /etc/nginx/sites-enabled/default

# Test configuration
sudo nginx -t

# Start and enable nginx
sudo systemctl enable nginx
sudo systemctl start nginx
```

## Step 6: DNS Configuration

### 6.1 Configure Local DNS (Development)
```bash
# Add entries to /etc/hosts on both servers
echo "********** tli-infrastructure" | sudo tee -a /etc/hosts
echo "********** tli-applications" | sudo tee -a /etc/hosts
echo "********** tli.local" | sudo tee -a /etc/hosts
```

### 6.2 Configure Production DNS
```bash
# For production, configure your DNS provider to point:
# tli.yourdomain.com -> Server 2 IP (or Load Balancer IP)
# api.tli.yourdomain.com -> Server 2 IP
# monitoring.tli.yourdomain.com -> Server 1 IP (for Grafana access)
```

## Step 7: Network Security

### 7.1 Configure VPN (Optional but Recommended)
```bash
# Install WireGuard for secure communication
sudo apt install -y wireguard

# Generate keys
wg genkey | tee privatekey | wg pubkey > publickey

# Configure WireGuard (example configuration)
sudo tee /etc/wireguard/wg0.conf << 'EOF'
[Interface]
PrivateKey = YOUR_PRIVATE_KEY
Address = ********/24
ListenPort = 51820

[Peer]
PublicKey = PEER_PUBLIC_KEY
AllowedIPs = ********/32
Endpoint = OTHER_SERVER_IP:51820
EOF

# Enable and start WireGuard
sudo systemctl enable wg-quick@wg0
sudo systemctl start wg-quick@wg0
```

### 7.2 Configure Fail2Ban
```bash
# Install Fail2Ban
sudo apt install -y fail2ban

# Configure for SSH protection
sudo tee /etc/fail2ban/jail.local << 'EOF'
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3

[sshd]
enabled = true
port = ssh
filter = sshd
logpath = /var/log/auth.log
maxretry = 3
EOF

# Start and enable Fail2Ban
sudo systemctl enable fail2ban
sudo systemctl start fail2ban
```

## Step 8: Network Monitoring

### 8.1 Install Network Monitoring Tools
```bash
# Install monitoring tools
sudo apt install -y iftop nethogs nload tcpdump

# Create network monitoring script
cat > /opt/tli/monitor-network.sh << 'EOF'
#!/bin/bash

echo "=== Network Monitoring ==="
echo "Date: $(date)"
echo

# Check network interfaces
echo "1. Network Interfaces:"
ip addr show | grep -E "inet|state"
echo

# Check routing table
echo "2. Routing Table:"
ip route show
echo

# Check active connections
echo "3. Active Connections:"
ss -tuln | grep -E ":5432|:6379|:5672|:5000|:5001|:5002|:5003|:5004|:5005|:5006|:5007|:5008|:5009|:5010|:5011|:5012|:5013"
echo

# Check bandwidth usage
echo "4. Network Statistics:"
cat /proc/net/dev | grep -E "eth0|ens"
echo

echo "=== Network Monitoring Complete ==="
EOF

chmod +x /opt/tli/monitor-network.sh
```

## Troubleshooting

### Common Network Issues

1. **Connection Refused**
   ```bash
   # Check if service is running
   sudo systemctl status service-name
   
   # Check if port is listening
   sudo netstat -tlnp | grep PORT
   
   # Check firewall rules
   sudo ufw status verbose
   ```

2. **DNS Resolution Issues**
   ```bash
   # Test DNS resolution
   nslookup tli.yourdomain.com
   
   # Check /etc/hosts
   cat /etc/hosts
   
   # Test with IP directly
   curl http://IP_ADDRESS:PORT/health
   ```

3. **SSL Certificate Issues**
   ```bash
   # Check certificate validity
   openssl x509 -in /opt/tli/config/ssl/tli.crt -text -noout
   
   # Test SSL connection
   openssl s_client -connect tli.yourdomain.com:443
   ```

4. **Performance Issues**
   ```bash
   # Monitor network traffic
   sudo iftop -i eth0
   
   # Check for packet loss
   ping -c 100 SERVER_IP
   
   # Monitor bandwidth
   nload eth0
   ```

## Next Steps

1. **Security Configuration**: Follow the security configuration guide
2. **SSL Setup**: Configure proper SSL certificates for production
3. **Monitoring**: Setup network monitoring and alerting
4. **Load Testing**: Test network performance under load
5. **Backup Network**: Configure backup network paths if available
