using Shared.Domain.Common;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Exceptions;

namespace SubscriptionManagement.Domain.Entities
{
    public class PlanFeature : BaseEntity
    {
        public FeatureType FeatureType { get; private set; }
        public FeatureAccessType AccessType { get; private set; }
        public int? LimitValue { get; private set; }
        public string? Description { get; private set; }
        public bool IsEnabled { get; private set; }

        private PlanFeature() { }

        public PlanFeature(FeatureType featureType, FeatureAccessType accessType, int? limitValue = null, string? description = null)
        {
            if (accessType == FeatureAccessType.Limit && !limitValue.HasValue)
                throw new SubscriptionDomainException("Limit value must be specified for limit-based features");

            if (accessType != FeatureAccessType.Limit && limitValue.HasValue)
                throw new SubscriptionDomainException("Limit value should only be specified for limit-based features");

            if (limitValue.HasValue && limitValue.Value < 0)
                throw new SubscriptionDomainException("Limit value cannot be negative");

            FeatureType = featureType;
            AccessType = accessType;
            LimitValue = limitValue;
            Description = description;
            IsEnabled = true;
        }

        public void Update(FeatureAccessType accessType, int? limitValue = null, string? description = null)
        {
            if (accessType == FeatureAccessType.Limit && !limitValue.HasValue)
                throw new SubscriptionDomainException("Limit value must be specified for limit-based features");

            if (accessType != FeatureAccessType.Limit && limitValue.HasValue)
                throw new SubscriptionDomainException("Limit value should only be specified for limit-based features");

            if (limitValue.HasValue && limitValue.Value < 0)
                throw new SubscriptionDomainException("Limit value cannot be negative");

            AccessType = accessType;
            LimitValue = limitValue;
            Description = description;
            SetUpdatedAt();
        }

        public void Enable()
        {
            IsEnabled = true;
            SetUpdatedAt();
        }

        public void Disable()
        {
            IsEnabled = false;
            SetUpdatedAt();
        }

        public bool IsAccessAllowed(int currentUsage = 0)
        {
            if (!IsEnabled)
                return false;

            return AccessType switch
            {
                FeatureAccessType.Boolean => true,
                FeatureAccessType.Unlimited => true,
                FeatureAccessType.Limit => currentUsage < (LimitValue ?? 0),
                _ => false
            };
        }

        public int GetRemainingUsage(int currentUsage)
        {
            if (!IsEnabled)
                return 0;

            return AccessType switch
            {
                FeatureAccessType.Boolean => 1,
                FeatureAccessType.Unlimited => int.MaxValue,
                FeatureAccessType.Limit => Math.Max(0, (LimitValue ?? 0) - currentUsage),
                _ => 0
            };
        }

        public string GetDisplayName()
        {
            return FeatureType switch
            {
                FeatureType.RfqLimit => "RFQ Creation",
                FeatureType.VehicleLimit => "Vehicle Management",
                FeatureType.CarrierLimit => "Carrier Network",
                FeatureType.Analytics => "Advanced Analytics",
                FeatureType.PrioritySupport => "Priority Support",
                FeatureType.ApiAccess => "API Access",
                FeatureType.CustomBranding => "Custom Branding",
                FeatureType.WhiteLabel => "White Label",
                FeatureType.RouteOptimization => "Route Optimization",
                FeatureType.MarginAnalysis => "Margin Analysis",
                FeatureType.CustomPricing => "Custom Pricing",
                FeatureType.DedicatedSupport => "Dedicated Support",
                FeatureType.Integrations => "Third-party Integrations",
                FeatureType.VolumeDiscounts => "Volume Discounts",
                _ => FeatureType.ToString()
            };
        }

        public string GetAccessDescription()
        {
            return AccessType switch
            {
                FeatureAccessType.Boolean when IsEnabled => "Enabled",
                FeatureAccessType.Boolean when !IsEnabled => "Disabled",
                FeatureAccessType.Unlimited => "Unlimited",
                FeatureAccessType.Limit => $"Up to {LimitValue}",
                _ => "Unknown"
            };
        }

        public override string ToString()
        {
            var accessDesc = GetAccessDescription();
            var description = !string.IsNullOrWhiteSpace(Description) ? $" - {Description}" : "";
            return $"{GetDisplayName()}: {accessDesc}{description}";
        }
    }
}
