using Shared.Domain.Common;
using TripManagement.Domain.Events;
using TripManagement.Domain.ValueObjects;

namespace TripManagement.Domain.Entities;

/// <summary>
/// Master data entity for service zones
/// </summary>
public class ServiceZoneMaster : AggregateRoot
{
    public string Code { get; private set; } = string.Empty;
    public string Name { get; private set; } = string.Empty;
    public string Description { get; private set; } = string.Empty;
    public string ZoneType { get; private set; } = string.Empty; // City, State, Region, Custom
    public string Country { get; private set; } = string.Empty;
    public string State { get; private set; } = string.Empty;
    public string City { get; private set; } = string.Empty;
    public string PostalCode { get; private set; } = string.Empty;
    public Location? CenterPoint { get; private set; }
    public decimal? RadiusKm { get; private set; }
    public string? BoundaryCoordinates { get; private set; } // GeoJSON polygon
    public decimal BasePriceMultiplier { get; private set; } = 1.0m;
    public decimal DistancePriceMultiplier { get; private set; } = 1.0m;
    public decimal TimePriceMultiplier { get; private set; } = 1.0m;
    public bool IsActive { get; private set; }
    public int Priority { get; private set; } // Higher priority zones override lower ones
    public string? ServiceRestrictions { get; private set; }
    public TimeSpan? ServiceStartTime { get; private set; }
    public TimeSpan? ServiceEndTime { get; private set; }
    public Dictionary<string, object> AdditionalProperties { get; private set; } = new();

    // Private constructor for EF Core
    private ServiceZoneMaster() { }

    public ServiceZoneMaster(
        string code,
        string name,
        string description,
        string zoneType,
        string country,
        string state = "",
        string city = "",
        string postalCode = "",
        Location? centerPoint = null,
        decimal? radiusKm = null,
        string? boundaryCoordinates = null,
        decimal basePriceMultiplier = 1.0m,
        decimal distancePriceMultiplier = 1.0m,
        decimal timePriceMultiplier = 1.0m,
        int priority = 0,
        string? serviceRestrictions = null,
        TimeSpan? serviceStartTime = null,
        TimeSpan? serviceEndTime = null,
        Dictionary<string, object>? additionalProperties = null)
    {
        if (string.IsNullOrWhiteSpace(code))
            throw new ArgumentException("Service zone code cannot be empty", nameof(code));

        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Service zone name cannot be empty", nameof(name));

        if (string.IsNullOrWhiteSpace(zoneType))
            throw new ArgumentException("Zone type cannot be empty", nameof(zoneType));

        if (string.IsNullOrWhiteSpace(country))
            throw new ArgumentException("Country cannot be empty", nameof(country));

        Code = code.Trim().ToUpperInvariant();
        Name = name.Trim();
        Description = description?.Trim() ?? string.Empty;
        ZoneType = zoneType.Trim();
        Country = country.Trim();
        State = state?.Trim() ?? string.Empty;
        City = city?.Trim() ?? string.Empty;
        PostalCode = postalCode?.Trim() ?? string.Empty;
        CenterPoint = centerPoint;
        RadiusKm = radiusKm;
        BoundaryCoordinates = boundaryCoordinates?.Trim();
        BasePriceMultiplier = basePriceMultiplier;
        DistancePriceMultiplier = distancePriceMultiplier;
        TimePriceMultiplier = timePriceMultiplier;
        IsActive = true;
        Priority = priority;
        ServiceRestrictions = serviceRestrictions?.Trim();
        ServiceStartTime = serviceStartTime;
        ServiceEndTime = serviceEndTime;
        AdditionalProperties = additionalProperties ?? new Dictionary<string, object>();

        // Validate pricing multipliers
        ValidatePriceMultipliers();

        // Validate service times
        ValidateServiceTimes();

        // Add domain event
        AddDomainEvent(new ServiceZoneMasterCreatedEvent(Id, Code, Name, ZoneType, Country));
    }

    public void UpdateDetails(
        string name,
        string description,
        string zoneType,
        string country,
        string state = "",
        string city = "",
        string postalCode = "",
        Location? centerPoint = null,
        decimal? radiusKm = null,
        string? boundaryCoordinates = null,
        decimal basePriceMultiplier = 1.0m,
        decimal distancePriceMultiplier = 1.0m,
        decimal timePriceMultiplier = 1.0m,
        int priority = 0,
        string? serviceRestrictions = null,
        TimeSpan? serviceStartTime = null,
        TimeSpan? serviceEndTime = null,
        Dictionary<string, object>? additionalProperties = null)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Service zone name cannot be empty", nameof(name));

        if (string.IsNullOrWhiteSpace(zoneType))
            throw new ArgumentException("Zone type cannot be empty", nameof(zoneType));

        if (string.IsNullOrWhiteSpace(country))
            throw new ArgumentException("Country cannot be empty", nameof(country));

        var oldName = Name;
        var oldZoneType = ZoneType;

        Name = name.Trim();
        Description = description?.Trim() ?? string.Empty;
        ZoneType = zoneType.Trim();
        Country = country.Trim();
        State = state?.Trim() ?? string.Empty;
        City = city?.Trim() ?? string.Empty;
        PostalCode = postalCode?.Trim() ?? string.Empty;
        CenterPoint = centerPoint;
        RadiusKm = radiusKm;
        BoundaryCoordinates = boundaryCoordinates?.Trim();
        BasePriceMultiplier = basePriceMultiplier;
        DistancePriceMultiplier = distancePriceMultiplier;
        TimePriceMultiplier = timePriceMultiplier;
        Priority = priority;
        ServiceRestrictions = serviceRestrictions?.Trim();
        ServiceStartTime = serviceStartTime;
        ServiceEndTime = serviceEndTime;

        if (additionalProperties != null)
        {
            AdditionalProperties = additionalProperties;
        }

        // Validate pricing multipliers
        ValidatePriceMultipliers();

        // Validate service times
        ValidateServiceTimes();

        SetUpdatedAt();

        // Add domain event if significant changes
        if (oldName != Name || oldZoneType != ZoneType)
        {
            AddDomainEvent(new ServiceZoneMasterUpdatedEvent(Id, Code, Name, ZoneType, oldName, oldZoneType));
        }
    }

    public void Activate()
    {
        if (IsActive) return;

        IsActive = true;
        SetUpdatedAt();

        AddDomainEvent(new ServiceZoneMasterActivatedEvent(Id, Code, Name));
    }

    public void Deactivate()
    {
        if (!IsActive) return;

        IsActive = false;
        SetUpdatedAt();

        AddDomainEvent(new ServiceZoneMasterDeactivatedEvent(Id, Code, Name));
    }

    public void UpdatePricing(decimal basePriceMultiplier, decimal distancePriceMultiplier, decimal timePriceMultiplier)
    {
        BasePriceMultiplier = basePriceMultiplier;
        DistancePriceMultiplier = distancePriceMultiplier;
        TimePriceMultiplier = timePriceMultiplier;

        ValidatePriceMultipliers();
        SetUpdatedAt();
    }

    public bool IsLocationInZone(Location location)
    {
        if (!IsActive) return false;

        // If radius-based zone
        if (CenterPoint != null && RadiusKm.HasValue)
        {
            var distance = CenterPoint.CalculateDistanceKm(location);
            return (decimal)distance <= RadiusKm.Value;
        }

        // If boundary-based zone (would need GeoJSON parsing implementation)
        if (!string.IsNullOrWhiteSpace(BoundaryCoordinates))
        {
            // TODO: Implement GeoJSON polygon contains point logic
            return false;
        }

        return false;
    }

    public bool IsServiceAvailable(DateTime requestTime)
    {
        if (!IsActive) return false;

        if (ServiceStartTime.HasValue && ServiceEndTime.HasValue)
        {
            var timeOfDay = requestTime.TimeOfDay;

            // Handle overnight service (e.g., 22:00 to 06:00)
            if (ServiceStartTime.Value > ServiceEndTime.Value)
            {
                return timeOfDay >= ServiceStartTime.Value || timeOfDay <= ServiceEndTime.Value;
            }

            // Normal service hours
            return timeOfDay >= ServiceStartTime.Value && timeOfDay <= ServiceEndTime.Value;
        }

        return true; // 24/7 service if no time restrictions
    }

    public decimal CalculatePriceMultiplier(decimal basePrice, decimal distance, TimeSpan estimatedTime)
    {
        var basePriceAdjusted = basePrice * BasePriceMultiplier;
        var distancePriceAdjusted = distance * DistancePriceMultiplier;
        var timePriceAdjusted = (decimal)estimatedTime.TotalHours * TimePriceMultiplier;

        return basePriceAdjusted + distancePriceAdjusted + timePriceAdjusted;
    }

    private void ValidatePriceMultipliers()
    {
        if (BasePriceMultiplier < 0)
            throw new ArgumentException("Base price multiplier cannot be negative");

        if (DistancePriceMultiplier < 0)
            throw new ArgumentException("Distance price multiplier cannot be negative");

        if (TimePriceMultiplier < 0)
            throw new ArgumentException("Time price multiplier cannot be negative");
    }

    private void ValidateServiceTimes()
    {
        // Service times are optional, so no validation needed if both are null
        // If one is set, both should be set for clarity, but this is not enforced
    }
}


