using AutoMapper;
using MediatR;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Repositories;
using Shared.Messaging;

namespace NetworkFleetManagement.Application.Commands.Vehicles;

public class CompleteMaintenanceCommandHandler : IRequestHandler<CompleteMaintenanceCommand, VehicleDto>
{
    private readonly IVehicleRepository _vehicleRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly IMessageBroker _messageBroker;

    public CompleteMaintenanceCommandHandler(
        IVehicleRepository vehicleRepository,
        IUnitOfWork unitOfWork,
        IMapper mapper,
        IMessageBroker messageBroker)
    {
        _vehicleRepository = vehicleRepository;
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _messageBroker = messageBroker;
    }

    public async Task<VehicleDto> Handle(CompleteMaintenanceCommand request, CancellationToken cancellationToken)
    {
        var vehicle = await _vehicleRepository.GetByIdAsync(request.VehicleId, cancellationToken);
        if (vehicle == null)
        {
            throw new InvalidOperationException($"Vehicle with ID {request.VehicleId} not found");
        }

        var maintenanceDto = request.MaintenanceDto;
        vehicle.CompleteMaintenance(maintenanceDto.CompletedDate, maintenanceDto.Notes, maintenanceDto.Cost);

        _vehicleRepository.Update(vehicle);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        // Publish integration event
        await _messageBroker.PublishAsync("maintenance.completed", new
        {
            VehicleId = vehicle.Id,
            CarrierId = vehicle.CarrierId,
            RegistrationNumber = vehicle.RegistrationNumber,
            CompletedDate = maintenanceDto.CompletedDate,
            Notes = maintenanceDto.Notes,
            Cost = maintenanceDto.Cost,
            CompletedAt = DateTime.UtcNow
        }, cancellationToken);

        var result = _mapper.Map<VehicleDto>(vehicle);
        return result;
    }
}
