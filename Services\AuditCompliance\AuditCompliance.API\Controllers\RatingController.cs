using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using MediatR;
using AuditCompliance.Application.Commands;
using AuditCompliance.Application.Queries;
using AuditCompliance.Application.DTOs;

namespace AuditCompliance.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class RatingController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<RatingController> _logger;

    public RatingController(IMediator mediator, ILogger<RatingController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Create a new service provider rating
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Shipper")]
    public async Task<ActionResult<Guid>> CreateServiceProviderRating([FromBody] CreateServiceProviderRatingCommand command)
    {
        try
        {
            // Set shipper information from current user
            command.ShipperId = GetCurrentUserId();
            command.ShipperName = GetCurrentUserName();

            var ratingId = await _mediator.Send(command);
            return Ok(new { RatingId = ratingId });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating service provider rating");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get service provider rating by ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<ServiceProviderRatingDto>> GetServiceProviderRating(Guid id)
    {
        try
        {
            var query = new GetServiceProviderRatingByIdQuery { Id = id };
            var rating = await _mediator.Send(query);

            if (rating == null)
                return NotFound();

            // Check authorization - shippers can only see their own ratings, others need admin/transport company role
            if (!CanAccessRating(rating))
                return Forbid();

            return Ok(rating);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving service provider rating: {RatingId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get service provider ratings with filtering and pagination
    /// </summary>
    [HttpPost("search")]
    public async Task<ActionResult<RatingResultDto>> GetServiceProviderRatings([FromBody] GetServiceProviderRatingsQuery query)
    {
        try
        {
            // Apply authorization filters
            ApplyAuthorizationFilters(query);

            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving service provider ratings");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get transport company rating summary
    /// </summary>
    [HttpGet("transport-company/{transportCompanyId}/summary")]
    public async Task<ActionResult<TransportCompanyRatingSummaryDto>> GetTransportCompanyRatingSummary(Guid transportCompanyId)
    {
        try
        {
            var query = new GetTransportCompanyRatingSummaryQuery { TransportCompanyId = transportCompanyId };
            var summary = await _mediator.Send(query);
            return Ok(summary);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving transport company rating summary: {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Submit a service provider rating
    /// </summary>
    [HttpPost("{id}/submit")]
    [Authorize(Roles = "Shipper")]
    public async Task<ActionResult> SubmitServiceProviderRating(Guid id)
    {
        try
        {
            // Verify ownership
            var rating = await GetRatingIfOwned(id);
            if (rating == null)
                return NotFound();

            var command = new SubmitServiceProviderRatingCommand { RatingId = id };
            var result = await _mediator.Send(command);

            if (!result)
                return NotFound();

            return Ok(new { Message = "Service provider rating submitted" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error submitting service provider rating: {RatingId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Flag a service provider rating for review
    /// </summary>
    [HttpPost("{id}/flag")]
    [Authorize(Roles = "Admin,TransportCompany")]
    public async Task<ActionResult> FlagServiceProviderRating(Guid id, [FromBody] FlagServiceProviderRatingCommand command)
    {
        try
        {
            command.RatingId = id;
            var result = await _mediator.Send(command);

            if (!result)
                return NotFound();

            return Ok(new { Message = "Service provider rating flagged for review" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error flagging service provider rating: {RatingId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Remove a service provider rating
    /// </summary>
    [HttpPost("{id}/remove")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> RemoveServiceProviderRating(Guid id, [FromBody] RemoveServiceProviderRatingCommand command)
    {
        try
        {
            command.RatingId = id;
            var result = await _mediator.Send(command);

            if (!result)
                return NotFound();

            return Ok(new { Message = "Service provider rating removed" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing service provider rating: {RatingId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Report a service issue
    /// </summary>
    [HttpPost("issues")]
    [Authorize(Roles = "Shipper")]
    public async Task<ActionResult<Guid>> ReportServiceIssue([FromBody] ReportServiceIssueCommand command)
    {
        try
        {
            // Verify the rating belongs to the current shipper
            var rating = await GetRatingIfOwned(command.ServiceProviderRatingId);
            if (rating == null)
                return NotFound();

            var issueId = await _mediator.Send(command);
            return Ok(new { IssueId = issueId });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reporting service issue");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Resolve a service issue
    /// </summary>
    [HttpPost("issues/{issueId}/resolve")]
    [Authorize(Roles = "Admin,TransportCompany")]
    public async Task<ActionResult> ResolveServiceIssue(Guid issueId, [FromBody] ResolveServiceIssueCommand command)
    {
        try
        {
            command.IssueId = issueId;
            command.ResolvedBy = GetCurrentUserId();
            command.ResolvedByName = GetCurrentUserName();

            var result = await _mediator.Send(command);

            if (!result)
                return NotFound();

            return Ok(new { Message = "Service issue resolved" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resolving service issue: {IssueId}", issueId);
            return StatusCode(500, "Internal server error");
        }
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst("sub") ?? User.FindFirst("id");
        return userIdClaim != null && Guid.TryParse(userIdClaim.Value, out var userId)
            ? userId
            : Guid.Empty;
    }

    private string GetCurrentUserName()
    {
        return User.FindFirst("name")?.Value ??
               User.FindFirst("preferred_username")?.Value ??
               User.Identity?.Name ??
               "Unknown";
    }

    private bool CanAccessRating(ServiceProviderRatingDto rating)
    {
        var currentUserId = GetCurrentUserId();

        // Admins can access all ratings
        if (User.IsInRole("Admin"))
            return true;

        // Shippers can access their own ratings
        if (User.IsInRole("Shipper") && rating.ShipperId == currentUserId)
            return true;

        // Transport companies can access ratings for their company
        if (User.IsInRole("TransportCompany") && rating.TransportCompanyId == currentUserId)
            return true;

        return false;
    }

    private void ApplyAuthorizationFilters(GetServiceProviderRatingsQuery query)
    {
        var currentUserId = GetCurrentUserId();

        // Non-admins can only see specific data
        if (!User.IsInRole("Admin"))
        {
            if (User.IsInRole("Shipper"))
            {
                // Shippers can only see their own ratings
                query.ShipperId = currentUserId;
            }
            else if (User.IsInRole("TransportCompany"))
            {
                // Transport companies can only see ratings for their company
                query.TransportCompanyId = currentUserId;
            }
        }
    }

    private async Task<ServiceProviderRatingDto?> GetRatingIfOwned(Guid ratingId)
    {
        var query = new GetServiceProviderRatingByIdQuery { Id = ratingId };
        var rating = await _mediator.Send(query);

        if (rating == null || !CanAccessRating(rating))
            return null;

        return rating;
    }

    /// <summary>
    /// Export feedback history with filtering and anonymization options
    /// </summary>
    [HttpPost("export")]
    [Authorize(Roles = "Admin,ComplianceOfficer,Auditor")]
    public async Task<ActionResult<FeedbackExportResponseDto>> ExportFeedbackHistory([FromBody] FeedbackExportRequestDto request)
    {
        try
        {
            request.RequestedBy = GetCurrentUserId();
            request.RequestedByRole = GetCurrentUserRole();
            request.IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
            request.UserAgent = HttpContext.Request.Headers["User-Agent"].ToString();

            var command = new ExportFeedbackHistoryCommand
            {
                UserIds = request.UserIds,
                TransportCompanyIds = request.TransportCompanyIds,
                FromDate = request.FromDate,
                ToDate = request.ToDate,
                MinRating = request.MinRating,
                MaxRating = request.MaxRating,
                Status = request.Status,
                IncludeAnonymous = request.IncludeAnonymous,
                AnonymizeData = request.AnonymizeData,
                Format = request.Format,
                SelectedColumns = request.SelectedColumns,
                MaxRecords = request.MaxRecords,
                RequestedBy = request.RequestedBy,
                RequestedByRole = request.RequestedByRole,
                IpAddress = request.IpAddress,
                UserAgent = request.UserAgent
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting feedback history");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get available columns for feedback export
    /// </summary>
    [HttpGet("export/columns")]
    [Authorize(Roles = "Admin,ComplianceOfficer,Auditor")]
    public ActionResult<object> GetFeedbackExportColumns()
    {
        try
        {
            var columns = new
            {
                BasicInfo = new[]
                {
                    new { Key = "Id", Name = "Rating ID", Description = "Unique rating identifier" },
                    new { Key = "ShipperId", Name = "Shipper ID", Description = "Shipper identifier" },
                    new { Key = "ShipperName", Name = "Shipper Name", Description = "Shipper name (anonymizable)" },
                    new { Key = "TransportCompanyId", Name = "Transport Company ID", Description = "Transport company identifier" },
                    new { Key = "TransportCompanyName", Name = "Transport Company Name", Description = "Transport company name" },
                    new { Key = "OrderId", Name = "Order ID", Description = "Related order identifier" },
                    new { Key = "TripId", Name = "Trip ID", Description = "Related trip identifier" }
                },
                RatingInfo = new[]
                {
                    new { Key = "OverallRating", Name = "Overall Rating", Description = "Overall rating score" },
                    new { Key = "Status", Name = "Status", Description = "Rating status" },
                    new { Key = "ReviewTitle", Name = "Review Title", Description = "Review title (anonymizable)" },
                    new { Key = "ReviewComment", Name = "Review Comment", Description = "Review comment (anonymizable)" },
                    new { Key = "IsAnonymous", Name = "Is Anonymous", Description = "Whether rating is anonymous" }
                },
                CategoryRatings = new[]
                {
                    new { Key = "TimelinessRating", Name = "Timeliness Rating", Description = "Timeliness category rating" },
                    new { Key = "CommunicationRating", Name = "Communication Rating", Description = "Communication category rating" },
                    new { Key = "QualityRating", Name = "Quality Rating", Description = "Quality category rating" },
                    new { Key = "ProfessionalismRating", Name = "Professionalism Rating", Description = "Professionalism category rating" }
                },
                Timestamps = new[]
                {
                    new { Key = "ServiceCompletedAt", Name = "Service Completed", Description = "Service completion date" },
                    new { Key = "ReviewSubmittedAt", Name = "Review Submitted", Description = "Review submission date" },
                    new { Key = "CreatedAt", Name = "Created Date", Description = "Rating creation date" }
                },
                Issues = new[]
                {
                    new { Key = "ReportedIssuesCount", Name = "Issues Count", Description = "Number of reported issues" },
                    new { Key = "IssueTypes", Name = "Issue Types", Description = "Types of reported issues" }
                }
            };

            return Ok(columns);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting feedback export columns");
            return StatusCode(500, "Internal server error");
        }
    }

    private string GetCurrentUserRole()
    {
        return User.FindFirst(ClaimTypes.Role)?.Value ?? string.Empty;
    }
}
