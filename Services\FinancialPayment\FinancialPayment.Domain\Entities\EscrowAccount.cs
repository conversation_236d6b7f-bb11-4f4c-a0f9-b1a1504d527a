using Shared.Domain.Common;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;
using FinancialPayment.Domain.Events;

namespace FinancialPayment.Domain.Entities;

public class EscrowAccount : AggregateRoot
{
    public Guid OrderId { get; private set; }
    public Guid TransportCompanyId { get; private set; }
    public Guid BrokerId { get; private set; }
    public Guid CarrierId { get; private set; }
    public string EscrowAccountNumber { get; private set; }
    public Money TotalAmount { get; private set; }
    public Money AvailableAmount { get; private set; }
    public Money ReservedAmount { get; private set; }
    public EscrowStatus Status { get; private set; }

    public DateTime? FundedAt { get; private set; }
    public DateTime? ReleasedAt { get; private set; }
    public string? ReleaseReason { get; private set; }
    public string? Notes { get; private set; }

    // Navigation properties
    private readonly List<EscrowTransaction> _transactions = new();
    public IReadOnlyList<EscrowTransaction> Transactions => _transactions.AsReadOnly();

    private readonly List<EscrowMilestone> _milestones = new();
    public IReadOnlyList<EscrowMilestone> Milestones => _milestones.AsReadOnly();

    private EscrowAccount() { }

    public EscrowAccount(
        Guid orderId,
        Guid transportCompanyId,
        Guid brokerId,
        Guid carrierId,
        Money totalAmount,
        string? notes = null)
    {
        if (totalAmount == null || totalAmount.Amount <= 0)
            throw new ArgumentException("Total amount must be greater than zero");

        Id = Guid.NewGuid();
        OrderId = orderId;
        TransportCompanyId = transportCompanyId;
        BrokerId = brokerId;
        CarrierId = carrierId;
        EscrowAccountNumber = GenerateEscrowAccountNumber();
        TotalAmount = totalAmount;
        AvailableAmount = Money.Zero(totalAmount.Currency);
        ReservedAmount = Money.Zero(totalAmount.Currency);
        Status = EscrowStatus.Created;
        CreatedAt = DateTime.UtcNow;
        Notes = notes;

        AddDomainEvent(new EscrowAccountCreatedEvent(Id, OrderId, TotalAmount));
    }

    public void Fund(Money amount, string paymentGatewayTransactionId, string? notes = null)
    {
        if (Status != EscrowStatus.Created && Status != EscrowStatus.PartiallyFunded)
            throw new InvalidOperationException("Can only fund created or partially funded escrow accounts");

        if (amount.Currency != TotalAmount.Currency)
            throw new ArgumentException("Funding currency must match escrow currency");

        var newAvailableAmount = AvailableAmount + amount;
        if (newAvailableAmount > TotalAmount)
            throw new ArgumentException("Funding amount exceeds total escrow amount");

        var transaction = new EscrowTransaction(
            Id,
            EscrowTransactionType.Fund,
            amount,
            TransportCompanyId,
            paymentGatewayTransactionId,
            notes);

        _transactions.Add(transaction);
        AvailableAmount = newAvailableAmount;

        if (AvailableAmount == TotalAmount)
        {
            Status = EscrowStatus.Funded;
            FundedAt = DateTime.UtcNow;
            AddDomainEvent(new EscrowAccountFundedEvent(Id, OrderId, TotalAmount));
        }
        else
        {
            Status = EscrowStatus.PartiallyFunded;
        }

        AddDomainEvent(new EscrowTransactionCreatedEvent(transaction.Id, Id, amount, EscrowTransactionType.Fund));
    }

    public void Reserve(Money amount, string reason)
    {
        if (Status != EscrowStatus.Funded && Status != EscrowStatus.PartiallyFunded)
            throw new InvalidOperationException("Can only reserve from funded or partially funded escrow accounts");

        if (amount > AvailableAmount)
            throw new ArgumentException("Cannot reserve more than available amount");

        AvailableAmount -= amount;
        ReservedAmount += amount;

        var transaction = new EscrowTransaction(
            Id,
            EscrowTransactionType.Reserve,
            amount,
            null,
            null,
            reason);

        _transactions.Add(transaction);
        AddDomainEvent(new EscrowTransactionCreatedEvent(transaction.Id, Id, amount, EscrowTransactionType.Reserve));
    }

    public void Release(Money amount, Guid recipientId, string reason, string? paymentGatewayTransactionId = null)
    {
        if (Status != EscrowStatus.Funded && Status != EscrowStatus.PartiallyReleased)
            throw new InvalidOperationException("Can only release from funded or partially released escrow accounts");

        if (amount > ReservedAmount && amount > AvailableAmount)
            throw new ArgumentException("Cannot release more than available or reserved amount");

        // Prioritize releasing from reserved amount first
        if (amount <= ReservedAmount)
        {
            ReservedAmount -= amount;
        }
        else
        {
            var fromReserved = ReservedAmount;
            var fromAvailable = amount - ReservedAmount;
            ReservedAmount = Money.Zero(TotalAmount.Currency);
            AvailableAmount -= fromAvailable;
        }

        var transaction = new EscrowTransaction(
            Id,
            EscrowTransactionType.Release,
            amount,
            recipientId,
            paymentGatewayTransactionId,
            reason);

        _transactions.Add(transaction);

        var totalReleased = _transactions
            .Where(t => t.Type == EscrowTransactionType.Release)
            .Sum(t => t.Amount.Amount);

        if (totalReleased >= TotalAmount.Amount)
        {
            Status = EscrowStatus.Released;
            ReleasedAt = DateTime.UtcNow;
            ReleaseReason = reason;
            AddDomainEvent(new EscrowAccountReleasedEvent(Id, OrderId, TotalAmount));
        }
        else
        {
            Status = EscrowStatus.PartiallyReleased;
        }

        AddDomainEvent(new EscrowTransactionCreatedEvent(transaction.Id, Id, amount, EscrowTransactionType.Release));
    }

    public void AddMilestone(string description, Money amount, DateTime? dueDate = null)
    {
        if (Status != EscrowStatus.Created && Status != EscrowStatus.PartiallyFunded)
            throw new InvalidOperationException("Can only add milestones to created or partially funded escrow accounts");

        var milestone = new EscrowMilestone(Id, description, amount, dueDate);
        _milestones.Add(milestone);
    }

    public void CompleteMilestone(Guid milestoneId, string completionNotes)
    {
        var milestone = _milestones.FirstOrDefault(m => m.Id == milestoneId);
        if (milestone == null)
            throw new ArgumentException("Milestone not found");

        milestone.Complete(completionNotes);
        AddDomainEvent(new EscrowMilestoneCompletedEvent(milestoneId, Id, milestone.Amount));
    }

    public void Refund(Money amount, string reason, string? paymentGatewayTransactionId = null)
    {
        if (Status == EscrowStatus.Released)
            throw new InvalidOperationException("Cannot refund from released escrow account");

        if (amount > (AvailableAmount + ReservedAmount))
            throw new ArgumentException("Cannot refund more than available amount");

        var transaction = new EscrowTransaction(
            Id,
            EscrowTransactionType.Refund,
            amount,
            TransportCompanyId,
            paymentGatewayTransactionId,
            reason);

        _transactions.Add(transaction);

        // Reduce from available first, then reserved
        if (amount <= AvailableAmount)
        {
            AvailableAmount -= amount;
        }
        else
        {
            var fromAvailable = AvailableAmount;
            var fromReserved = amount - AvailableAmount;
            AvailableAmount = Money.Zero(TotalAmount.Currency);
            ReservedAmount -= fromReserved;
        }

        Status = EscrowStatus.Refunded;
        AddDomainEvent(new EscrowTransactionCreatedEvent(transaction.Id, Id, amount, EscrowTransactionType.Refund));
    }

    private static string GenerateEscrowAccountNumber()
    {
        return $"ESC{DateTime.UtcNow:yyyyMMdd}{Random.Shared.Next(1000, 9999)}";
    }
}
