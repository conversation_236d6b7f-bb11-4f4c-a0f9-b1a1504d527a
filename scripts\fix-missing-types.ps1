#!/usr/bin/env pwsh

Write-Host "Creating missing types and fixing remaining issues..." -ForegroundColor Green

# Create BarcodeScanning class for MobileWorkflow
$barcodeScanningPath = "Services/MobileWorkflow/MobileWorkflow.Domain/ValueObjects/BarcodeScanning.cs"
if (-not (Test-Path $barcodeScanningPath)) {
    $barcodeScanningContent = @"
using Shared.Domain.ValueObjects;

namespace MobileWorkflow.Domain.ValueObjects;

public class BarcodeScanning : ValueObject
{
    public string Format { get; private set; }
    public string Data { get; private set; }
    public DateTime ScannedAt { get; private set; }
    public bool IsValid { get; private set; }

    private BarcodeScanning() 
    { 
        Format = string.Empty;
        Data = string.Empty;
    }

    public BarcodeScanning(string format, string data, bool isValid = true)
    {
        Format = format ?? throw new ArgumentNullException(nameof(format));
        Data = data ?? throw new ArgumentNullException(nameof(data));
        IsValid = isValid;
        ScannedAt = DateTime.UtcNow;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Format;
        yield return Data;
        yield return ScannedAt;
        yield return IsValid;
    }
}
"@
    Set-Content $barcodeScanningPath $barcodeScanningContent -NoNewline
    Write-Host "Created BarcodeScanning class" -ForegroundColor Green
}

# Create missing DTOs for various services
$missingDtos = @{
    "Services/UserManagement/UserManagement.Application/DTOs/TransportCompanyProfileDto.cs" = @"
namespace UserManagement.Application.DTOs;

public class TransportCompanyProfileDto
{
    public Guid Id { get; set; }
    public string CompanyName { get; set; } = string.Empty;
    public string RegistrationNumber { get; set; } = string.Empty;
    public string ContactEmail { get; set; } = string.Empty;
    public string ContactPhone { get; set; } = string.Empty;
    public string Address { get; set; } = string.Empty;
    public string City { get; set; } = string.Empty;
    public string State { get; set; } = string.Empty;
    public string Country { get; set; } = string.Empty;
    public string PostalCode { get; set; } = string.Empty;
    public string? LogoUrl { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}
"@
    "Services/UserManagement/UserManagement.Application/DTOs/ShipperProfileDto.cs" = @"
namespace UserManagement.Application.DTOs;

public class ShipperProfileDto
{
    public Guid Id { get; set; }
    public string CompanyName { get; set; } = string.Empty;
    public string ContactPersonName { get; set; } = string.Empty;
    public string ContactEmail { get; set; } = string.Empty;
    public string ContactPhone { get; set; } = string.Empty;
    public string Address { get; set; } = string.Empty;
    public string City { get; set; } = string.Empty;
    public string State { get; set; } = string.Empty;
    public string Country { get; set; } = string.Empty;
    public string PostalCode { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}
"@
    "Services/UserManagement/UserManagement.Application/DTOs/BrokerProfileDto.cs" = @"
namespace UserManagement.Application.DTOs;

public class BrokerProfileDto
{
    public Guid Id { get; set; }
    public string CompanyName { get; set; } = string.Empty;
    public string ContactPersonName { get; set; } = string.Empty;
    public string ContactEmail { get; set; } = string.Empty;
    public string ContactPhone { get; set; } = string.Empty;
    public string LicenseNumber { get; set; } = string.Empty;
    public string Address { get; set; } = string.Empty;
    public string City { get; set; } = string.Empty;
    public string State { get; set; } = string.Empty;
    public string Country { get; set; } = string.Empty;
    public string PostalCode { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}
"@
}

foreach ($filePath in $missingDtos.Keys) {
    $directory = Split-Path -Parent $filePath
    if (-not (Test-Path $directory)) {
        New-Item -ItemType Directory -Path $directory -Force | Out-Null
    }
    
    if (-not (Test-Path $filePath)) {
        Set-Content $filePath $missingDtos[$filePath] -NoNewline
        Write-Host "Created DTO: $filePath" -ForegroundColor Green
    }
}

Write-Host "Missing types creation completed!" -ForegroundColor Green
