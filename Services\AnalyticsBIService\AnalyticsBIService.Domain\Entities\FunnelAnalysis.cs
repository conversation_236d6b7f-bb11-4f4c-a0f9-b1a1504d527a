using AnalyticsBIService.Domain.Enums;
using Shared.Domain.Common;
using System.Text.Json;

namespace AnalyticsBIService.Domain.Entities;

/// <summary>
/// Funnel analysis domain entity
/// </summary>
public class FunnelAnalysis : BaseEntity
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public string FunnelType { get; private set; }
    public string Definition { get; private set; }
    public string Metrics { get; private set; }
    public Guid CreatedBy { get; private set; }
    public UserType UserType { get; private set; }
    public bool IsActive { get; private set; }

    // Required for EF Core
    private FunnelAnalysis()
    {
        Name = string.Empty;
        Description = string.Empty;
        FunnelType = string.Empty;
        Definition = string.Empty;
        Metrics = string.Empty;
    }

    public FunnelAnalysis(
        string name,
        string description,
        string funnelType,
        string definition,
        Guid createdBy,
        UserType userType)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Name cannot be null or empty", nameof(name));
        if (string.IsNullOrWhiteSpace(funnelType))
            throw new ArgumentException("Funnel type cannot be null or empty", nameof(funnelType));
        if (string.IsNullOrWhiteSpace(definition))
            throw new ArgumentException("Definition cannot be null or empty", nameof(definition));

        Id = Guid.NewGuid();
        Name = name;
        Description = description ?? string.Empty;
        FunnelType = funnelType;
        Definition = definition;
        CreatedBy = createdBy;
        UserType = userType;
        IsActive = true;
        Metrics = "{}";
        CreatedAt = DateTime.UtcNow;
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Update funnel analysis properties
    /// </summary>
    public void UpdateProperties(string name, string description, string definition)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Name cannot be null or empty", nameof(name));
        if (string.IsNullOrWhiteSpace(definition))
            throw new ArgumentException("Definition cannot be null or empty", nameof(definition));

        Name = name;
        Description = description ?? string.Empty;
        Definition = definition;
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Update funnel metrics
    /// </summary>
    public void UpdateMetrics(object metrics)
    {
        if (metrics == null)
            throw new ArgumentNullException(nameof(metrics));

        Metrics = JsonSerializer.Serialize(metrics);
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Activate the funnel analysis
    /// </summary>
    public void Activate()
    {
        IsActive = true;
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Deactivate the funnel analysis
    /// </summary>
    public void Deactivate()
    {
        IsActive = false;
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Update funnel type
    /// </summary>
    public void UpdateFunnelType(string funnelType)
    {
        if (string.IsNullOrWhiteSpace(funnelType))
            throw new ArgumentException("Funnel type cannot be null or empty", nameof(funnelType));

        FunnelType = funnelType;
        UpdatedAt = DateTime.UtcNow;
    }
}
