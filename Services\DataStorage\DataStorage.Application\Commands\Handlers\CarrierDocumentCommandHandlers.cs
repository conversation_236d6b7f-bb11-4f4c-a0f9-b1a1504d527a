using AutoMapper;
using DataStorage.Application.DTOs;
using DataStorage.Domain.Entities;
using DataStorage.Domain.Enums;
using DataStorage.Domain.Exceptions;
using DataStorage.Domain.Interfaces;
using DataStorage.Domain.ValueObjects;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Domain.Common;

namespace DataStorage.Application.Commands.Handlers;

public class UploadPickupConfirmationPhotoCommandHandler : IRequestHandler<UploadPickupConfirmationPhotoCommand, DocumentDto>
{
    private readonly IDocumentRepository _documentRepository;
    private readonly IFileStorageService _fileStorageService;
    private readonly IDocumentProcessingService _documentProcessingService;
    private readonly IMapper _mapper;
    private readonly ILogger<UploadPickupConfirmationPhotoCommandHandler> _logger;

    public UploadPickupConfirmationPhotoCommandHandler(
        IDocumentRepository documentRepository,
        IFileStorageService fileStorageService,
        IDocumentProcessingService documentProcessingService,
        IMapper mapper,
        ILogger<UploadPickupConfirmationPhotoCommandHandler> logger)
    {
        _documentRepository = documentRepository;
        _fileStorageService = fileStorageService;
        _documentProcessingService = documentProcessingService;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<DocumentDto> Handle(UploadPickupConfirmationPhotoCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Uploading pickup confirmation photo for carrier {CarrierId}, trip {TripId}",
                request.CarrierId, request.TripId);

            // Validate that it's an image
            if (!request.ContentType.StartsWith("image/", StringComparison.OrdinalIgnoreCase))
            {
                throw new InvalidFileFormatException(request.FileName, "Image", request.ContentType);
            }

            // Upload photo to storage
            var storageLocation = await _fileStorageService.UploadFileAsync(
                request.PhotoStream,
                request.FileName,
                "image/jpeg", // Standardize to JPEG for photos
                cancellationToken: cancellationToken);

            // Get file metadata
            var fileMetadata = await _fileStorageService.GetFileMetadataAsync(storageLocation, cancellationToken);

            // Calculate checksum
            var checksum = await _documentProcessingService.CalculateChecksumAsync(storageLocation, cancellationToken);
            fileMetadata = fileMetadata.WithChecksum(checksum);

            // Prepare tags with pickup specific information
            var tags = new Dictionary<string, string>(request.AdditionalMetadata ?? new Dictionary<string, string>())
            {
                ["trip_id"] = request.TripId.ToString(),
                ["document_subtype"] = "pickup_confirmation_photo",
                ["captured_at"] = (request.CapturedAt ?? DateTime.UtcNow).ToString("O")
            };

            if (request.OrderId.HasValue)
                tags["order_id"] = request.OrderId.Value.ToString();

            if (request.Latitude.HasValue && request.Longitude.HasValue)
            {
                tags["latitude"] = request.Latitude.Value.ToString("F6");
                tags["longitude"] = request.Longitude.Value.ToString("F6");
            }

            if (!string.IsNullOrWhiteSpace(request.LocationAddress))
                tags["location_address"] = request.LocationAddress;

            // Create document entity
            var document = new Document(
                request.Title,
                request.Description,
                DocumentType.PickupConfirmationPhoto,
                DocumentCategory.Operational,
                fileMetadata,
                storageLocation,
                request.CarrierId,
                "Carrier",
                AccessLevel.Internal,
                expiresAt: null,
                tags);

            // Save to repository
            await _documentRepository.AddAsync(document, cancellationToken);

            // Generate thumbnail
            var thumbnailBytes = await _documentProcessingService.GenerateThumbnailAsync(
                storageLocation, 200, 200, cancellationToken);

            _logger.LogInformation("Pickup confirmation photo uploaded successfully. DocumentId: {DocumentId}", document.Id);

            var documentDto = _mapper.Map<DocumentDto>(document);
            documentDto.AccessUrl = storageLocation.GetAccessUrl();

            if (thumbnailBytes?.Length > 0)
            {
                try
                {
                    var thumbnailLocation = await _fileStorageService.UploadFileAsync(
                        thumbnailBytes,
                        $"thumb_{request.FileName}",
                        "image/jpeg",
                        cancellationToken: cancellationToken);

                    documentDto.ThumbnailUrl = thumbnailLocation.GetAccessUrl();
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to upload thumbnail for document {DocumentId}", document.Id);
                }
            }

            return documentDto;
        }
        catch (Exception ex) when (!(ex is DataStorageDomainException))
        {
            _logger.LogError(ex, "Failed to upload pickup confirmation photo for carrier {CarrierId}", request.CarrierId);
            throw new FileUploadException(request.FileName, ex);
        }
    }
}

public class SubmitDigitalProofOfDeliveryCommandHandler : IRequestHandler<SubmitDigitalProofOfDeliveryCommand, DocumentDto>
{
    private readonly IDocumentRepository _documentRepository;
    private readonly IFileStorageService _fileStorageService;
    private readonly IDocumentProcessingService _documentProcessingService;
    private readonly IMapper _mapper;
    private readonly ILogger<SubmitDigitalProofOfDeliveryCommandHandler> _logger;

    public SubmitDigitalProofOfDeliveryCommandHandler(
        IDocumentRepository documentRepository,
        IFileStorageService fileStorageService,
        IDocumentProcessingService documentProcessingService,
        IMapper mapper,
        ILogger<SubmitDigitalProofOfDeliveryCommandHandler> logger)
    {
        _documentRepository = documentRepository;
        _fileStorageService = fileStorageService;
        _documentProcessingService = documentProcessingService;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<DocumentDto> Handle(SubmitDigitalProofOfDeliveryCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Submitting digital proof of delivery for carrier {CarrierId}, trip {TripId}",
                request.CarrierId, request.TripId);

            // Generate POD document content
            var podContent = await GeneratePODContentAsync(request, cancellationToken);
            var fileName = $"POD_{request.TripId}_{DateTime.UtcNow:yyyyMMddHHmmss}.pdf";

            // Upload POD document
            var storageLocation = await _fileStorageService.UploadFileAsync(
                podContent,
                fileName,
                "application/pdf",
                cancellationToken: cancellationToken);

            // Get file metadata
            var fileMetadata = await _fileStorageService.GetFileMetadataAsync(storageLocation, cancellationToken);

            // Prepare tags with POD specific information
            var tags = new Dictionary<string, string>(request.AdditionalMetadata ?? new Dictionary<string, string>())
            {
                ["trip_id"] = request.TripId.ToString(),
                ["document_subtype"] = "digital_proof_of_delivery",
                ["recipient_name"] = request.RecipientName,
                ["recipient_contact"] = request.RecipientContact,
                ["delivered_at"] = request.DeliveredAt.ToString("O"),
                ["delivery_condition"] = request.DeliveryCondition.ToString()
            };

            if (request.OrderId.HasValue)
                tags["order_id"] = request.OrderId.Value.ToString();

            if (request.Latitude.HasValue && request.Longitude.HasValue)
            {
                tags["latitude"] = request.Latitude.Value.ToString("F6");
                tags["longitude"] = request.Longitude.Value.ToString("F6");
            }

            if (!string.IsNullOrWhiteSpace(request.LocationAddress))
                tags["location_address"] = request.LocationAddress;

            if (!string.IsNullOrWhiteSpace(request.RecipientIdType))
                tags["recipient_id_type"] = request.RecipientIdType;

            if (!string.IsNullOrWhiteSpace(request.RecipientIdNumber))
                tags["recipient_id_number"] = request.RecipientIdNumber;

            if (!string.IsNullOrWhiteSpace(request.DeliveryNotes))
                tags["delivery_notes"] = request.DeliveryNotes;

            // Create document entity
            var document = new Document(
                request.Title,
                request.Description,
                DocumentType.ProofOfDelivery,
                DocumentCategory.Operational,
                fileMetadata,
                storageLocation,
                request.CarrierId,
                "Carrier",
                AccessLevel.Shared, // POD should be accessible to multiple parties
                expiresAt: null,
                tags);

            // Save to repository
            await _documentRepository.AddAsync(document, cancellationToken);

            // Upload supporting documents if provided
            if (request.SupportingDocuments?.Any() == true && request.SupportingDocumentNames?.Any() == true)
            {
                await UploadSupportingDocumentsAsync(document.Id, request.SupportingDocuments,
                    request.SupportingDocumentNames, request.CarrierId, cancellationToken);
            }

            _logger.LogInformation("Digital proof of delivery submitted successfully. DocumentId: {DocumentId}", document.Id);

            var documentDto = _mapper.Map<DocumentDto>(document);
            documentDto.AccessUrl = storageLocation.GetAccessUrl();

            return documentDto;
        }
        catch (Exception ex) when (!(ex is DataStorageDomainException))
        {
            _logger.LogError(ex, "Failed to submit digital proof of delivery for carrier {CarrierId}", request.CarrierId);
            throw new DocumentProcessingException("submit digital proof of delivery", request.TripId.ToString(), ex);
        }
    }

    private async Task<byte[]> GeneratePODContentAsync(SubmitDigitalProofOfDeliveryCommand request, CancellationToken cancellationToken)
    {
        // This is a placeholder implementation
        // In a real implementation, you would use a PDF generation library

        var podText = $@"
DIGITAL PROOF OF DELIVERY
========================

Trip ID: {request.TripId}
{(request.OrderId.HasValue ? $"Order ID: {request.OrderId}" : "")}

DELIVERY DETAILS
---------------
Delivered At: {request.DeliveredAt:yyyy-MM-dd HH:mm:ss}
Location: {request.LocationAddress ?? "Not specified"}
{(request.Latitude.HasValue && request.Longitude.HasValue ? $"Coordinates: {request.Latitude:F6}, {request.Longitude:F6}" : "")}

RECIPIENT INFORMATION
--------------------
Name: {request.RecipientName}
Contact: {request.RecipientContact}
{(!string.IsNullOrWhiteSpace(request.RecipientIdType) ? $"ID Type: {request.RecipientIdType}" : "")}
{(!string.IsNullOrWhiteSpace(request.RecipientIdNumber) ? $"ID Number: {request.RecipientIdNumber}" : "")}

DELIVERY STATUS
--------------
Condition: {request.DeliveryCondition}
{(!string.IsNullOrWhiteSpace(request.DeliveryNotes) ? $"Notes: {request.DeliveryNotes}" : "")}

DIGITAL SIGNATURE
----------------
{(request.DigitalSignature?.Length > 0 ? "Digital signature captured" : "No digital signature")}

Generated on: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC
";

        return System.Text.Encoding.UTF8.GetBytes(podText);
    }

    private async Task UploadSupportingDocumentsAsync(
        Guid parentDocumentId,
        List<Stream> supportingDocuments,
        List<string> supportingDocumentNames,
        Guid carrierId,
        CancellationToken cancellationToken)
    {
        for (int i = 0; i < Math.Min(supportingDocuments.Count, supportingDocumentNames.Count); i++)
        {
            try
            {
                var stream = supportingDocuments[i];
                var fileName = supportingDocumentNames[i];
                var contentType = GetContentTypeFromFileName(fileName);

                var storageLocation = await _fileStorageService.UploadFileAsync(
                    stream, fileName, contentType, cancellationToken: cancellationToken);

                var fileMetadata = await _fileStorageService.GetFileMetadataAsync(storageLocation, cancellationToken);

                var tags = new Dictionary<string, string>
                {
                    ["parent_document_id"] = parentDocumentId.ToString(),
                    ["document_subtype"] = "pod_supporting_document",
                    ["document_index"] = i.ToString()
                };

                var supportingDocument = new Document(
                    $"POD Supporting Document - {fileName}",
                    $"Supporting document for POD {parentDocumentId}",
                    DocumentType.DeliveryReceipt,
                    DocumentCategory.Operational,
                    fileMetadata,
                    storageLocation,
                    carrierId,
                    "Carrier",
                    AccessLevel.Internal,
                    expiresAt: null,
                    tags);

                await _documentRepository.AddAsync(supportingDocument, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to upload supporting document {Index} for POD {ParentDocumentId}",
                    i, parentDocumentId);
            }
        }
    }

    private static string GetContentTypeFromFileName(string fileName)
    {
        var extension = Path.GetExtension(fileName).ToLowerInvariant();
        return extension switch
        {
            ".jpg" or ".jpeg" => "image/jpeg",
            ".png" => "image/png",
            ".pdf" => "application/pdf",
            ".doc" => "application/msword",
            ".docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            _ => "application/octet-stream"
        };
    }
}

