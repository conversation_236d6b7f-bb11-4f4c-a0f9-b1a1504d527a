namespace Shared.Messaging.Events;

/// <summary>
/// Event published when an audit log entry is created
/// </summary>
public class AuditLogCreatedEvent
{
    public Guid AuditLogId { get; set; }
    public string EventType { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public Guid? UserId { get; set; }
    public string? UserName { get; set; }
    public string EntityType { get; set; } = string.Empty;
    public Guid? EntityId { get; set; }
    public string Action { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime EventTimestamp { get; set; }
    public List<string> ComplianceFlags { get; set; } = new();
    public string? IpAddress { get; set; }
    public string? SessionId { get; set; }
}

/// <summary>
/// Event published when a compliance report is generated
/// </summary>
public class ComplianceReportGeneratedEvent
{
    public Guid ReportId { get; set; }
    public string ReportNumber { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Standard { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime ReportPeriodStart { get; set; }
    public DateTime ReportPeriodEnd { get; set; }
    public Guid GeneratedBy { get; set; }
    public string GeneratedByName { get; set; } = string.Empty;
    public bool IsAutomated { get; set; }
    public DateTime CreatedAt { get; set; }
}

/// <summary>
/// Event published when a compliance report is completed
/// </summary>
public class ComplianceReportCompletedEvent
{
    public Guid ReportId { get; set; }
    public string ReportNumber { get; set; } = string.Empty;
    public string Standard { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public int TotalViolations { get; set; }
    public int CriticalViolations { get; set; }
    public int HighViolations { get; set; }
    public string? Findings { get; set; }
    public string? Recommendations { get; set; }
    public DateTime CompletedAt { get; set; }
}

/// <summary>
/// Event published when a compliance violation is detected
/// </summary>
public class ComplianceViolationDetectedEvent
{
    public Guid ViolationId { get; set; }
    public string Standard { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public string ViolationType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public Guid? EntityId { get; set; }
    public string? EntityType { get; set; }
    public string? Evidence { get; set; }
    public string? Recommendation { get; set; }
    public DateTime DetectedAt { get; set; }
    public bool RequiresImmediateAction { get; set; }
}

/// <summary>
/// Event published when audit data cleanup is performed
/// </summary>
public class AuditDataCleanupPerformedEvent
{
    public int DeletedRecordsCount { get; set; }
    public DateTime CleanupDate { get; set; }
    public string RetentionPolicy { get; set; } = string.Empty;
    public List<string> DeletedEventTypes { get; set; } = new();
    public DateTime OldestDeletedRecord { get; set; }
    public DateTime NewestDeletedRecord { get; set; }
}
