using MediatR;
using AuditCompliance.Application.DTOs;
using AuditCompliance.Domain.Enums;

namespace AuditCompliance.Application.Commands
{
    public class ExportFeedbackHistoryCommand : IRequest<FeedbackExportResponseDto>
    {
        public List<Guid> UserIds { get; set; } = new();
        public List<Guid> TransportCompanyIds { get; set; } = new();
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public decimal? MinRating { get; set; }
        public decimal? MaxRating { get; set; }
        public RatingStatus? Status { get; set; }
        public bool IncludeAnonymous { get; set; } = true;
        public bool AnonymizeData { get; set; } = false;
        public ExportFormat Format { get; set; } = ExportFormat.CSV;
        public List<string> SelectedColumns { get; set; } = new();
        public int? MaxRecords { get; set; }
        
        // Audit fields
        public Guid RequestedBy { get; set; }
        public string RequestedByRole { get; set; }
        public string IpAddress { get; set; }
        public string UserAgent { get; set; }
    }
}
