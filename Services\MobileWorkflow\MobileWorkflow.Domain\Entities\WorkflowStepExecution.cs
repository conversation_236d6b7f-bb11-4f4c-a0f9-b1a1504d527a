using Shared.Domain.Common;
using MobileWorkflow.Domain.Enums;
using MobileWorkflow.Domain.Events;

namespace MobileWorkflow.Domain.Entities;

public class WorkflowStepExecution : AggregateRoot
{
    public Guid WorkflowExecutionId { get; private set; }
    public Guid WorkflowStepId { get; private set; }
    public string StepName { get; private set; }
    public string StepType { get; private set; }
    public string Status { get; private set; } // Pending, Running, Completed, Failed, Skipped, Cancelled
    public DateTime StartTime { get; private set; }
    public DateTime? EndTime { get; private set; }
    public Dictionary<string, object> InputData { get; private set; }
    public Dictionary<string, object> OutputData { get; private set; }
    public Dictionary<string, object> Context { get; private set; }
    public string? ErrorMessage { get; private set; }
    public string? ErrorDetails { get; private set; }
    public int AttemptNumber { get; private set; }
    public int MaxAttempts { get; private set; }
    public DateTime? NextRetryTime { get; private set; }
    public Dictionary<string, object> ExecutionLog { get; private set; }
    public Dictionary<string, object> PerformanceMetrics { get; private set; }
    public Guid? AssignedTo { get; private set; }
    public string? AssignedToRole { get; private set; }
    public DateTime? AssignedAt { get; private set; }
    public DateTime? CompletedAt { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    public virtual WorkflowExecution WorkflowExecution { get; private set; } = null!;
    public virtual WorkflowStep WorkflowStep { get; private set; } = null!;

    private WorkflowStepExecution() { } // EF Core

    public WorkflowStepExecution(
        Guid workflowExecutionId,
        Guid workflowStepId,
        string stepName,
        string stepType,
        Dictionary<string, object> inputData,
        int maxAttempts = 3)
    {
        WorkflowExecutionId = workflowExecutionId;
        WorkflowStepId = workflowStepId;
        StepName = stepName ?? throw new ArgumentNullException(nameof(stepName));
        StepType = stepType ?? throw new ArgumentNullException(nameof(stepType));
        InputData = inputData ?? throw new ArgumentNullException(nameof(inputData));
        MaxAttempts = maxAttempts;

        Status = "Pending";
        StartTime = DateTime.UtcNow;
        AttemptNumber = 1;
        OutputData = new Dictionary<string, object>();
        Context = new Dictionary<string, object>();
        ExecutionLog = new Dictionary<string, object>();
        PerformanceMetrics = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();

        AddDomainEvent(new WorkflowStepExecutionCreatedEvent(Id, WorkflowExecutionId, StepName));
    }

    public void Start()
    {
        if (Status != "Pending")
            throw new InvalidOperationException($"Cannot start step execution with status: {Status}");

        Status = "Running";
        StartTime = DateTime.UtcNow;

        LogExecution($"Step execution started (Attempt {AttemptNumber}/{MaxAttempts})");

        AddDomainEvent(new WorkflowStepExecutionStartedEvent(Id, WorkflowExecutionId, StepName, AttemptNumber));
    }

    public void Complete(Dictionary<string, object> outputData)
    {
        if (Status != "Running")
            throw new InvalidOperationException($"Cannot complete step execution with status: {Status}");

        Status = "Completed";
        EndTime = DateTime.UtcNow;
        CompletedAt = DateTime.UtcNow;
        OutputData = outputData ?? throw new ArgumentNullException(nameof(outputData));

        var duration = EndTime.Value - StartTime;
        PerformanceMetrics["execution_duration_ms"] = duration.TotalMilliseconds;

        LogExecution($"Step execution completed successfully in {duration.TotalSeconds:F2} seconds");

        AddDomainEvent(new WorkflowStepExecutionCompletedEvent(Id, WorkflowExecutionId, StepName, true));
    }

    public void Fail(string errorMessage, string? errorDetails = null)
    {
        if (Status != "Running")
            throw new InvalidOperationException($"Cannot fail step execution with status: {Status}");

        Status = "Failed";
        EndTime = DateTime.UtcNow;
        ErrorMessage = errorMessage;
        ErrorDetails = errorDetails;

        var duration = EndTime.Value - StartTime;
        PerformanceMetrics["execution_duration_ms"] = duration.TotalMilliseconds;

        LogExecution($"Step execution failed: {errorMessage}");

        AddDomainEvent(new WorkflowStepExecutionFailedEvent(Id, WorkflowExecutionId, StepName, errorMessage, AttemptNumber));
    }

    public void Skip(string reason)
    {
        if (Status != "Pending" && Status != "Running")
            throw new InvalidOperationException($"Cannot skip step execution with status: {Status}");

        Status = "Skipped";
        EndTime = DateTime.UtcNow;

        LogExecution($"Step execution skipped: {reason}");

        AddDomainEvent(new WorkflowStepExecutionSkippedEvent(Id, WorkflowExecutionId, StepName, reason));
    }

    public void Cancel()
    {
        if (Status == "Completed" || Status == "Failed" || Status == "Cancelled")
            throw new InvalidOperationException($"Cannot cancel step execution with status: {Status}");

        Status = "Cancelled";
        EndTime = DateTime.UtcNow;

        LogExecution("Step execution cancelled");

        AddDomainEvent(new WorkflowStepExecutionCancelledEvent(Id, WorkflowExecutionId, StepName));
    }

    public void ScheduleRetry(TimeSpan delay)
    {
        if (Status != "Failed")
            throw new InvalidOperationException($"Cannot schedule retry for step execution with status: {Status}");

        if (AttemptNumber >= MaxAttempts)
            throw new InvalidOperationException($"Maximum retry attempts ({MaxAttempts}) exceeded");

        AttemptNumber++;
        NextRetryTime = DateTime.UtcNow.Add(delay);
        Status = "Pending";
        ErrorMessage = null;
        ErrorDetails = null;

        LogExecution($"Retry scheduled for {NextRetryTime} (Attempt {AttemptNumber}/{MaxAttempts})");

        AddDomainEvent(new WorkflowStepExecutionRetryScheduledEvent(Id, WorkflowExecutionId, StepName, AttemptNumber, NextRetryTime));
    }

    public void AssignTo(Guid userId, string? role = null)
    {
        AssignedTo = userId;
        AssignedToRole = role;
        AssignedAt = DateTime.UtcNow;

        LogExecution($"Step assigned to user {userId}" + (role != null ? $" with role {role}" : ""));

        AddDomainEvent(new WorkflowStepExecutionAssignedEvent(Id, WorkflowExecutionId, StepName, userId.ToString()));
    }

    public void UpdateContext(string key, object value)
    {
        Context[key] = value;
        LogExecution($"Context updated: {key}");
    }

    public void UpdateInputData(Dictionary<string, object> newInputData)
    {
        InputData = newInputData ?? throw new ArgumentNullException(nameof(newInputData));
        LogExecution("Input data updated");
    }

    public void AddPerformanceMetric(string metric, object value)
    {
        PerformanceMetrics[metric] = value;
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    public bool CanRetry()
    {
        return Status == "Failed" && AttemptNumber < MaxAttempts &&
               (NextRetryTime == null || DateTime.UtcNow >= NextRetryTime);
    }

    public bool IsCompleted()
    {
        return Status == "Completed" || Status == "Skipped";
    }

    public bool IsFailed()
    {
        return Status == "Failed" && !CanRetry();
    }

    public bool IsRunning()
    {
        return Status == "Running";
    }

    public bool IsPending()
    {
        return Status == "Pending";
    }

    public TimeSpan GetExecutionDuration()
    {
        var endTime = EndTime ?? DateTime.UtcNow;
        return endTime - StartTime;
    }

    public TimeSpan? GetRetryDelay()
    {
        if (NextRetryTime.HasValue)
        {
            var delay = NextRetryTime.Value - DateTime.UtcNow;
            return delay > TimeSpan.Zero ? delay : TimeSpan.Zero;
        }
        return null;
    }

    public T? GetInputData<T>(string key, T? defaultValue = default)
    {
        if (InputData.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetOutputData<T>(string key, T? defaultValue = default)
    {
        if (OutputData.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetContext<T>(string key, T? defaultValue = default)
    {
        if (Context.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetPerformanceMetric<T>(string metric, T? defaultValue = default)
    {
        if (PerformanceMetrics.TryGetValue(metric, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    private void LogExecution(string message)
    {
        var timestamp = DateTime.UtcNow;
        var logKey = $"log_{timestamp:yyyyMMddHHmmss}_{Guid.NewGuid():N}";
        ExecutionLog[logKey] = new { Timestamp = timestamp, Message = message, Status, AttemptNumber };
    }

    public List<Dictionary<string, object>> GetExecutionLogs()
    {
        return ExecutionLog.Values
            .Cast<Dictionary<string, object>>()
            .OrderBy(log => log.TryGetValue("Timestamp", out var ts) ? ts : DateTime.MinValue)
            .ToList();
    }
}


