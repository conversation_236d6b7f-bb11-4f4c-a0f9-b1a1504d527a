# Content Management Service Test Runner
# This script runs all tests for the Content Management Service

param(
    [string]$Configuration = "Debug",
    [string]$TestFilter = "",
    [switch]$Coverage = $false,
    [switch]$Performance = $false,
    [switch]$Integration = $false,
    [switch]$EndToEnd = $false,
    [switch]$Verbose = $false,
    [switch]$NoBuild = $false
)

Write-Host "Content Management Service Test Runner" -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Green

# Set error action preference
$ErrorActionPreference = "Stop"

# Get the script directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = $ScriptDir

# Test project path
$TestProject = Join-Path $ProjectRoot "ContentManagement.Tests"

# Check if test project exists
if (-not (Test-Path $TestProject)) {
    Write-Error "Test project not found at: $TestProject"
    exit 1
}

# Build arguments
$BuildArgs = @()
if ($NoBuild) {
    $BuildArgs += "--no-build"
}

$BuildArgs += "--configuration", $Configuration

# Test arguments
$TestArgs = @()
$TestArgs += $BuildArgs
$TestArgs += "--logger", "console;verbosity=normal"

if ($Verbose) {
    $TestArgs += "--verbosity", "detailed"
}

# Coverage arguments
if ($Coverage) {
    $TestArgs += "--collect:XPlat Code Coverage"
    $TestArgs += "--results-directory", "TestResults"
}

# Test filter
$FilterArgs = @()

if ($TestFilter) {
    $FilterArgs += "--filter", $TestFilter
} elseif ($Performance) {
    $FilterArgs += "--filter", "Category=Performance"
} elseif ($Integration) {
    $FilterArgs += "--filter", "Category=Integration"
} elseif ($EndToEnd) {
    $FilterArgs += "--filter", "Category=EndToEnd"
}

# Function to run tests with specific filter
function Run-Tests {
    param(
        [string]$TestCategory,
        [string[]]$AdditionalArgs = @()
    )
    
    Write-Host "`nRunning $TestCategory tests..." -ForegroundColor Yellow
    Write-Host "Command: dotnet test $TestProject $($TestArgs -join ' ') $($AdditionalArgs -join ' ')" -ForegroundColor Gray
    
    $AllArgs = $TestArgs + $AdditionalArgs
    
    try {
        & dotnet test $TestProject @AllArgs
        if ($LASTEXITCODE -ne 0) {
            Write-Error "$TestCategory tests failed with exit code $LASTEXITCODE"
            return $false
        }
        Write-Host "$TestCategory tests completed successfully!" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Error "Error running $TestCategory tests: $_"
        return $false
    }
}

# Main execution
try {
    # Change to project directory
    Push-Location $ProjectRoot
    
    Write-Host "Project Directory: $ProjectRoot" -ForegroundColor Cyan
    Write-Host "Test Project: $TestProject" -ForegroundColor Cyan
    Write-Host "Configuration: $Configuration" -ForegroundColor Cyan
    
    # Build the solution first (unless --no-build is specified)
    if (-not $NoBuild) {
        Write-Host "`nBuilding solution..." -ForegroundColor Yellow
        & dotnet build --configuration $Configuration
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Build failed with exit code $LASTEXITCODE"
            exit 1
        }
        Write-Host "Build completed successfully!" -ForegroundColor Green
    }
    
    $TestResults = @()
    
    # Run specific test categories or all tests
    if ($Performance -or $Integration -or $EndToEnd -or $TestFilter) {
        # Run specific category
        $CategoryName = if ($Performance) { "Performance" } 
                       elseif ($Integration) { "Integration" }
                       elseif ($EndToEnd) { "End-to-End" }
                       else { "Filtered" }
        
        $Success = Run-Tests -TestCategory $CategoryName -AdditionalArgs $FilterArgs
        $TestResults += @{ Category = $CategoryName; Success = $Success }
    }
    else {
        # Run all test categories
        Write-Host "`nRunning all test categories..." -ForegroundColor Cyan
        
        # Unit Tests (default - no specific filter)
        $Success = Run-Tests -TestCategory "Unit" -AdditionalArgs @("--filter", "Category!=Performance&Category!=Integration&Category!=EndToEnd")
        $TestResults += @{ Category = "Unit"; Success = $Success }
        
        # Integration Tests
        $Success = Run-Tests -TestCategory "Integration" -AdditionalArgs @("--filter", "Category=Integration")
        $TestResults += @{ Category = "Integration"; Success = $Success }
        
        # Performance Tests
        $Success = Run-Tests -TestCategory "Performance" -AdditionalArgs @("--filter", "Category=Performance")
        $TestResults += @{ Category = "Performance"; Success = $Success }
        
        # End-to-End Tests
        $Success = Run-Tests -TestCategory "End-to-End" -AdditionalArgs @("--filter", "Category=EndToEnd")
        $TestResults += @{ Category = "End-to-End"; Success = $Success }
    }
    
    # Generate coverage report if requested
    if ($Coverage) {
        Write-Host "`nGenerating coverage report..." -ForegroundColor Yellow
        
        $CoverageFiles = Get-ChildItem -Path "TestResults" -Filter "coverage.cobertura.xml" -Recurse
        if ($CoverageFiles.Count -gt 0) {
            Write-Host "Coverage files found:" -ForegroundColor Cyan
            foreach ($file in $CoverageFiles) {
                Write-Host "  $($file.FullName)" -ForegroundColor Gray
            }
            
            # Try to generate HTML report using ReportGenerator if available
            try {
                $ReportGeneratorPath = (Get-Command reportgenerator -ErrorAction SilentlyContinue).Source
                if ($ReportGeneratorPath) {
                    $CoverageFile = $CoverageFiles[0].FullName
                    $ReportDir = Join-Path "TestResults" "CoverageReport"
                    
                    & reportgenerator "-reports:$CoverageFile" "-targetdir:$ReportDir" "-reporttypes:Html"
                    Write-Host "HTML coverage report generated at: $ReportDir" -ForegroundColor Green
                }
                else {
                    Write-Host "ReportGenerator not found. Install with: dotnet tool install -g dotnet-reportgenerator-globaltool" -ForegroundColor Yellow
                }
            }
            catch {
                Write-Host "Could not generate HTML coverage report: $_" -ForegroundColor Yellow
            }
        }
        else {
            Write-Host "No coverage files found in TestResults directory" -ForegroundColor Yellow
        }
    }
    
    # Summary
    Write-Host "`n" + "="*50 -ForegroundColor Green
    Write-Host "TEST EXECUTION SUMMARY" -ForegroundColor Green
    Write-Host "="*50 -ForegroundColor Green
    
    $AllPassed = $true
    foreach ($result in $TestResults) {
        $Status = if ($result.Success) { "PASSED" } else { "FAILED" }
        $Color = if ($result.Success) { "Green" } else { "Red" }
        Write-Host "$($result.Category) Tests: $Status" -ForegroundColor $Color
        
        if (-not $result.Success) {
            $AllPassed = $false
        }
    }
    
    Write-Host "`nOverall Result: $(if ($AllPassed) { 'ALL TESTS PASSED' } else { 'SOME TESTS FAILED' })" -ForegroundColor $(if ($AllPassed) { 'Green' } else { 'Red' })
    
    if ($Coverage) {
        Write-Host "`nCoverage reports available in TestResults directory" -ForegroundColor Cyan
    }
    
    # Exit with appropriate code
    exit $(if ($AllPassed) { 0 } else { 1 })
}
catch {
    Write-Error "Test execution failed: $_"
    exit 1
}
finally {
    Pop-Location
}

# Usage examples:
# .\run-tests.ps1                          # Run all tests
# .\run-tests.ps1 -Coverage                # Run all tests with coverage
# .\run-tests.ps1 -Performance             # Run only performance tests
# .\run-tests.ps1 -Integration             # Run only integration tests
# .\run-tests.ps1 -EndToEnd                # Run only end-to-end tests
# .\run-tests.ps1 -TestFilter "ContentTests" # Run tests matching filter
# .\run-tests.ps1 -Verbose                 # Run with detailed output
# .\run-tests.ps1 -Configuration Release   # Run in Release configuration
