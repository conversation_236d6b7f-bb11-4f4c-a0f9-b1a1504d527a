using Microsoft.Extensions.Logging;
using AuditCompliance.Application.Interfaces;
using AuditCompliance.Application.DTOs.Risk;
using System.Text.RegularExpressions;
using System.Text.Json;

namespace AuditCompliance.Infrastructure.Services;

/// <summary>
/// Risk scoring engine for calculating risk scores using configurable factors
/// </summary>
public class RiskScoringEngine : IRiskScoringEngine
{
    private readonly ILogger<RiskScoringEngine> _logger;
    private readonly Dictionary<string, Func<Dictionary<string, object>, RiskFactorDto, float>> _evaluators;

    public RiskScoringEngine(ILogger<RiskScoringEngine> logger)
    {
        _logger = logger;
        _evaluators = InitializeEvaluators();
    }

    public async Task<float> CalculateRiskScoreAsync(
        Dictionary<string, object> entityData,
        List<RiskFactorDto> riskFactors,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Calculating risk score using {FactorCount} factors", riskFactors.Count);

            if (!riskFactors.Any())
            {
                _logger.LogWarning("No risk factors provided for calculation");
                return 0f;
            }

            var evaluations = new List<RiskFactorEvaluationResult>();

            foreach (var factor in riskFactors)
            {
                var evaluation = await EvaluateRiskFactorAsync(factor, entityData, cancellationToken);
                evaluations.Add(evaluation);
            }

            var weightedScore = CalculateWeightedScore(evaluations);

            _logger.LogInformation("Calculated weighted risk score: {Score}", weightedScore);
            return weightedScore;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating risk score");
            throw;
        }
    }

    public async Task<RiskFactorEvaluationResult> EvaluateRiskFactorAsync(
        RiskFactorDto factor,
        Dictionary<string, object> entityData,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Evaluating risk factor: {FactorName}", factor.Name);

            var score = factor.EvaluationMethod.ToLower() switch
            {
                "formula" => EvaluateFormula(entityData, factor),
                "lookup" => EvaluateLookup(entityData, factor),
                "ml" => await EvaluateMLAsync(entityData, factor, cancellationToken),
                "manual" => EvaluateManual(entityData, factor),
                "threshold" => EvaluateThreshold(entityData, factor),
                "count" => EvaluateCount(entityData, factor),
                "percentage" => EvaluatePercentage(entityData, factor),
                "date" => EvaluateDate(entityData, factor),
                _ => EvaluateDefault(entityData, factor)
            };

            // Ensure score is within bounds
            score = Math.Max(factor.MinScore, Math.Min(factor.MaxScore, score));

            var weightedScore = score * factor.Weight / 100f;

            var result = new RiskFactorEvaluationResult
            {
                FactorId = factor.Id,
                FactorName = factor.Name,
                Score = score,
                Weight = factor.Weight,
                WeightedScore = weightedScore,
                Evaluation = GetEvaluationDescription(score, factor),
                Details = new Dictionary<string, object>
                {
                    ["EvaluationMethod"] = factor.EvaluationMethod,
                    ["DataSource"] = factor.DataSource,
                    ["MinScore"] = factor.MinScore,
                    ["MaxScore"] = factor.MaxScore,
                    ["RawScore"] = score
                }
            };

            _logger.LogDebug("Factor {FactorName} evaluated: Score={Score}, Weighted={WeightedScore}", 
                factor.Name, score, weightedScore);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error evaluating risk factor {FactorName}", factor.Name);
            
            // Return neutral score on error
            return new RiskFactorEvaluationResult
            {
                FactorId = factor.Id,
                FactorName = factor.Name,
                Score = (factor.MinScore + factor.MaxScore) / 2,
                Weight = factor.Weight,
                WeightedScore = ((factor.MinScore + factor.MaxScore) / 2) * factor.Weight / 100f,
                Evaluation = "Error in evaluation",
                Details = new Dictionary<string, object> { ["Error"] = ex.Message }
            };
        }
    }

    public string GetRiskLevel(float riskScore, List<RiskThresholdDto> thresholds)
    {
        try
        {
            var threshold = thresholds
                .OrderBy(t => t.MinScore)
                .FirstOrDefault(t => riskScore >= t.MinScore && riskScore <= t.MaxScore);

            return threshold?.RiskLevel ?? "Unknown";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error determining risk level for score {Score}", riskScore);
            return "Unknown";
        }
    }

    public float CalculateWeightedScore(List<RiskFactorEvaluationResult> evaluations)
    {
        try
        {
            if (!evaluations.Any())
                return 0f;

            var totalWeight = evaluations.Sum(e => e.Weight);
            if (totalWeight == 0)
                return evaluations.Average(e => e.Score);

            var weightedSum = evaluations.Sum(e => e.WeightedScore);
            return (weightedSum / totalWeight) * 100f;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating weighted score");
            return 0f;
        }
    }

    // Private evaluation methods
    private float EvaluateFormula(Dictionary<string, object> entityData, RiskFactorDto factor)
    {
        try
        {
            var formula = factor.EvaluationCriteria;
            
            // Replace variables in formula with actual values
            foreach (var kvp in entityData)
            {
                var value = kvp.Value?.ToString() ?? "0";
                formula = formula.Replace($"{{{kvp.Key}}}", value);
            }

            // Simple formula evaluation (in production, use a proper expression evaluator)
            if (float.TryParse(formula, out var result))
            {
                return result;
            }

            // Basic arithmetic operations
            if (formula.Contains("+") || formula.Contains("-") || formula.Contains("*") || formula.Contains("/"))
            {
                return EvaluateSimpleFormula(formula);
            }

            return factor.MinScore;
        }
        catch
        {
            return factor.MinScore;
        }
    }

    private float EvaluateLookup(Dictionary<string, object> entityData, RiskFactorDto factor)
    {
        try
        {
            var lookupTable = factor.Configuration.ContainsKey("LookupTable") 
                ? JsonSerializer.Deserialize<Dictionary<string, float>>(factor.Configuration["LookupTable"].ToString() ?? "{}")
                : new Dictionary<string, float>();

            var lookupKey = GetDataValue(entityData, factor.DataSource)?.ToString() ?? "";
            
            return lookupTable.ContainsKey(lookupKey) ? lookupTable[lookupKey] : factor.MinScore;
        }
        catch
        {
            return factor.MinScore;
        }
    }

    private async Task<float> EvaluateMLAsync(Dictionary<string, object> entityData, RiskFactorDto factor, CancellationToken cancellationToken)
    {
        try
        {
            // This would integrate with ML.NET or external ML service
            // For now, return a simulated ML score
            var features = ExtractFeatures(entityData, factor);
            var mlScore = SimulateMLPrediction(features);
            
            return mlScore;
        }
        catch
        {
            return factor.MinScore;
        }
    }

    private float EvaluateManual(Dictionary<string, object> entityData, RiskFactorDto factor)
    {
        // Manual evaluation would be set by user input
        var manualScore = GetDataValue(entityData, $"manual_{factor.Name}");
        return manualScore != null && float.TryParse(manualScore.ToString(), out var score) ? score : factor.MinScore;
    }

    private float EvaluateThreshold(Dictionary<string, object> entityData, RiskFactorDto factor)
    {
        try
        {
            var value = GetDataValue(entityData, factor.DataSource);
            if (value == null || !float.TryParse(value.ToString(), out var numericValue))
                return factor.MinScore;

            var thresholds = factor.Configuration.ContainsKey("Thresholds")
                ? JsonSerializer.Deserialize<List<ThresholdConfig>>(factor.Configuration["Thresholds"].ToString() ?? "[]")
                : new List<ThresholdConfig>();

            var threshold = thresholds
                .OrderByDescending(t => t.Value)
                .FirstOrDefault(t => numericValue >= t.Value);

            return threshold?.Score ?? factor.MinScore;
        }
        catch
        {
            return factor.MinScore;
        }
    }

    private float EvaluateCount(Dictionary<string, object> entityData, RiskFactorDto factor)
    {
        try
        {
            var value = GetDataValue(entityData, factor.DataSource);
            if (value == null)
                return factor.MinScore;

            var count = 0;
            if (value is IEnumerable<object> enumerable)
            {
                count = enumerable.Count();
            }
            else if (int.TryParse(value.ToString(), out var intValue))
            {
                count = intValue;
            }

            // Scale count to score range
            var maxCount = factor.Configuration.ContainsKey("MaxCount") 
                ? Convert.ToInt32(factor.Configuration["MaxCount"]) 
                : 100;

            var scoreRange = factor.MaxScore - factor.MinScore;
            var scaledScore = (float)count / maxCount * scoreRange + factor.MinScore;

            return Math.Min(factor.MaxScore, scaledScore);
        }
        catch
        {
            return factor.MinScore;
        }
    }

    private float EvaluatePercentage(Dictionary<string, object> entityData, RiskFactorDto factor)
    {
        try
        {
            var value = GetDataValue(entityData, factor.DataSource);
            if (value == null || !float.TryParse(value.ToString(), out var percentage))
                return factor.MinScore;

            // Convert percentage to score range
            var scoreRange = factor.MaxScore - factor.MinScore;
            return (percentage / 100f) * scoreRange + factor.MinScore;
        }
        catch
        {
            return factor.MinScore;
        }
    }

    private float EvaluateDate(Dictionary<string, object> entityData, RiskFactorDto factor)
    {
        try
        {
            var value = GetDataValue(entityData, factor.DataSource);
            if (value == null || !DateTime.TryParse(value.ToString(), out var date))
                return factor.MinScore;

            var daysDifference = (DateTime.UtcNow - date).TotalDays;
            var maxDays = factor.Configuration.ContainsKey("MaxDays") 
                ? Convert.ToDouble(factor.Configuration["MaxDays"]) 
                : 365;

            // Scale days to score range (older = higher risk)
            var scoreRange = factor.MaxScore - factor.MinScore;
            var scaledScore = (float)(daysDifference / maxDays) * scoreRange + factor.MinScore;

            return Math.Min(factor.MaxScore, scaledScore);
        }
        catch
        {
            return factor.MinScore;
        }
    }

    private float EvaluateDefault(Dictionary<string, object> entityData, RiskFactorDto factor)
    {
        // Default evaluation returns middle score
        return (factor.MinScore + factor.MaxScore) / 2;
    }

    private float EvaluateSimpleFormula(string formula)
    {
        try
        {
            // Very basic arithmetic evaluation
            // In production, use a proper expression evaluator like NCalc
            
            if (formula.Contains("+"))
            {
                var parts = formula.Split('+');
                return parts.Sum(p => float.TryParse(p.Trim(), out var val) ? val : 0);
            }
            
            if (formula.Contains("*"))
            {
                var parts = formula.Split('*');
                var result = 1f;
                foreach (var part in parts)
                {
                    if (float.TryParse(part.Trim(), out var val))
                        result *= val;
                }
                return result;
            }

            return 0f;
        }
        catch
        {
            return 0f;
        }
    }

    private object? GetDataValue(Dictionary<string, object> entityData, string dataSource)
    {
        if (string.IsNullOrEmpty(dataSource))
            return null;

        // Support nested properties (e.g., "compliance.score")
        var parts = dataSource.Split('.');
        object? current = entityData;

        foreach (var part in parts)
        {
            if (current is Dictionary<string, object> dict && dict.ContainsKey(part))
            {
                current = dict[part];
            }
            else
            {
                return null;
            }
        }

        return current;
    }

    private Dictionary<string, float> ExtractFeatures(Dictionary<string, object> entityData, RiskFactorDto factor)
    {
        var features = new Dictionary<string, float>();
        
        foreach (var kvp in entityData)
        {
            if (float.TryParse(kvp.Value?.ToString(), out var value))
            {
                features[kvp.Key] = value;
            }
        }

        return features;
    }

    private float SimulateMLPrediction(Dictionary<string, float> features)
    {
        // Simulate ML prediction
        // In production, this would call actual ML model
        var random = new Random();
        return random.Next(0, 100);
    }

    private string GetEvaluationDescription(float score, RiskFactorDto factor)
    {
        var percentage = (score - factor.MinScore) / (factor.MaxScore - factor.MinScore) * 100;
        
        return percentage switch
        {
            >= 75 => "High risk - immediate attention required",
            >= 50 => "Medium risk - monitor closely",
            >= 25 => "Low risk - routine monitoring",
            _ => "Minimal risk - no immediate action needed"
        };
    }

    private Dictionary<string, Func<Dictionary<string, object>, RiskFactorDto, float>> InitializeEvaluators()
    {
        return new Dictionary<string, Func<Dictionary<string, object>, RiskFactorDto, float>>
        {
            ["formula"] = EvaluateFormula,
            ["lookup"] = EvaluateLookup,
            ["manual"] = EvaluateManual,
            ["threshold"] = EvaluateThreshold,
            ["count"] = EvaluateCount,
            ["percentage"] = EvaluatePercentage,
            ["date"] = EvaluateDate,
            ["default"] = EvaluateDefault
        };
    }
}

/// <summary>
/// Threshold configuration for risk evaluation
/// </summary>
public class ThresholdConfig
{
    public float Value { get; set; }
    public float Score { get; set; }
    public string Description { get; set; } = string.Empty;
}
