using Microsoft.EntityFrameworkCore;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Infrastructure.Persistence;

namespace SubscriptionManagement.Infrastructure.Repositories
{
    public class PlanRepository : IPlanRepository
    {
        private readonly SubscriptionDbContext _context;

        public PlanRepository(SubscriptionDbContext context)
        {
            _context = context;
        }

        public async Task<Plan?> GetByIdAsync(Guid id)
        {
            return await _context.Plans
                .Include(p => p.Features)
                .FirstOrDefaultAsync(p => p.Id == id);
        }

        public async Task<Plan?> GetByNameAsync(string name)
        {
            return await _context.Plans
                .Include(p => p.Features)
                .FirstOrDefaultAsync(p => p.Name == name);
        }

        public async Task<List<Plan>> GetAllAsync()
        {
            return await _context.Plans
                .Include(p => p.Features)
                .OrderBy(p => p.UserType)
                .ThenBy(p => p.Type)
                .ToListAsync();
        }

        public async Task<List<Plan>> GetActiveAsync()
        {
            return await _context.Plans
                .Include(p => p.Features)
                .Where(p => p.IsActive)
                .OrderBy(p => p.UserType)
                .ThenBy(p => p.Type)
                .ToListAsync();
        }

        public async Task<List<Plan>> GetPublicAsync()
        {
            return await _context.Plans
                .Include(p => p.Features)
                .Where(p => p.IsActive && p.IsPublic)
                .OrderBy(p => p.UserType)
                .ThenBy(p => p.Type)
                .ToListAsync();
        }

        public async Task<List<Plan>> GetByUserTypeAsync(UserType userType)
        {
            return await _context.Plans
                .Include(p => p.Features)
                .Where(p => p.UserType == userType && p.IsActive)
                .OrderBy(p => p.Type)
                .ToListAsync();
        }

        public async Task<List<Plan>> GetByTypeAsync(PlanType type)
        {
            return await _context.Plans
                .Include(p => p.Features)
                .Where(p => p.Type == type && p.IsActive)
                .OrderBy(p => p.UserType)
                .ToListAsync();
        }

        public async Task<List<Plan>> GetByUserTypeAndTypeAsync(UserType userType, PlanType type)
        {
            return await _context.Plans
                .Include(p => p.Features)
                .Where(p => p.UserType == userType && p.Type == type && p.IsActive)
                .ToListAsync();
        }

        public async Task<List<Plan>> GetPlansAsync(int page, int pageSize)
        {
            return await _context.Plans
                .Include(p => p.Features)
                .OrderBy(p => p.UserType)
                .ThenBy(p => p.Type)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
        }

        public async Task<int> GetPlansCountAsync()
        {
            return await _context.Plans.CountAsync();
        }

        public async Task AddAsync(Plan plan)
        {
            await _context.Plans.AddAsync(plan);
            await _context.SaveChangesAsync();
        }

        public async Task UpdateAsync(Plan plan)
        {
            _context.Plans.Update(plan);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteAsync(Plan plan)
        {
            _context.Plans.Remove(plan);
            await _context.SaveChangesAsync();
        }

        public async Task<bool> ExistsAsync(Guid id)
        {
            return await _context.Plans.AnyAsync(p => p.Id == id);
        }

        public async Task<bool> ExistsByNameAsync(string name)
        {
            return await _context.Plans.AnyAsync(p => p.Name == name);
        }
    }
}
