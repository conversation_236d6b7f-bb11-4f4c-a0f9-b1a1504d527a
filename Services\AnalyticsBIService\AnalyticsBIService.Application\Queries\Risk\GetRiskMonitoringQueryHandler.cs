using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Application.Services;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Domain.Common;

namespace AnalyticsBIService.Application.Queries.Risk;

/// <summary>
/// Handler for risk monitoring queries
/// </summary>
public class GetRiskMonitoringQueryHandler :
    IRequestHandler<GetRiskMonitoringDashboardQuery, RiskMonitoringDto>,
    IRequestHandler<GetIssueFlaggedFeedbackCountQuery, int>,
    IRequestHandler<GetRedFlagTrendsQuery, List<RedFlagTrendDto>>,
    IRequestHandler<GetEntityPerformanceRankingsQuery, List<EntityPerformanceRankingDto>>,
    IRequestHandler<GetEntityRiskAssessmentQuery, RiskAssessmentDto>,
    IRequestHandler<GetActiveRiskAlertsQuery, List<RiskAlertDto>>
{
    private readonly IRiskMonitoringService _riskMonitoringService;
    private readonly ILogger<GetRiskMonitoringQueryHandler> _logger;

    public GetRiskMonitoringQueryHandler(
        IRiskMonitoringService riskMonitoringService,
        ILogger<GetRiskMonitoringQueryHandler> logger)
    {
        _riskMonitoringService = riskMonitoringService;
        _logger = logger;
    }

    public async Task<RiskMonitoringDto> Handle(GetRiskMonitoringDashboardQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Handling GetRiskMonitoringDashboardQuery for user {UserId}", request.UserId);

            var dashboard = await _riskMonitoringService.GetRiskMonitoringDashboardAsync(
                request.UserId,
                cancellationToken);

            _logger.LogDebug("Successfully retrieved risk monitoring dashboard for user {UserId}", request.UserId);
            return dashboard;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling GetRiskMonitoringDashboardQuery for user {UserId}", request.UserId);
            throw;
        }
    }

    public async Task<int> Handle(GetIssueFlaggedFeedbackCountQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Handling GetIssueFlaggedFeedbackCountQuery for entity {EntityId} of type {EntityType}",
                request.EntityId, request.EntityType);

            var count = await _riskMonitoringService.CountIssueFlaggedFeedbackAsync(
                request.EntityId,
                request.EntityType,
                request.FromDate,
                request.ToDate,
                cancellationToken);

            _logger.LogDebug("Successfully counted {Count} issue-flagged feedback items", count);
            return count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling GetIssueFlaggedFeedbackCountQuery");
            throw;
        }
    }

    public async Task<List<RedFlagTrendDto>> Handle(GetRedFlagTrendsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Handling GetRedFlagTrendsQuery for entity {EntityId} of type {EntityType}",
                request.EntityId, request.EntityType);

            var trends = await _riskMonitoringService.DetectRedFlagTrendsAsync(
                request.EntityId,
                request.EntityType,
                request.LookbackDays,
                cancellationToken);

            _logger.LogDebug("Successfully detected {Count} red flag trends", trends.Count);
            return trends;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling GetRedFlagTrendsQuery");
            throw;
        }
    }

    public async Task<List<EntityPerformanceRankingDto>> Handle(GetEntityPerformanceRankingsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Handling GetEntityPerformanceRankingsQuery for entity type {EntityType}",
                request.EntityType);

            var rankings = await _riskMonitoringService.GetEntityPerformanceRankingsAsync(
                request.EntityType,
                request.TopCount,
                cancellationToken);

            _logger.LogDebug("Successfully retrieved {Count} entity performance rankings", rankings.Count);
            return rankings;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling GetEntityPerformanceRankingsQuery for type {EntityType}",
                request.EntityType);
            throw;
        }
    }

    public async Task<RiskAssessmentDto> Handle(GetEntityRiskAssessmentQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Handling GetEntityRiskAssessmentQuery for entity {EntityId} of type {EntityType}",
                request.EntityId, request.EntityType);

            var assessment = await _riskMonitoringService.AssessEntityRiskAsync(
                request.EntityId,
                request.EntityType,
                cancellationToken);

            _logger.LogDebug("Successfully assessed risk for entity {EntityId}: {RiskLevel}",
                request.EntityId, assessment.RiskLevel);
            return assessment;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling GetEntityRiskAssessmentQuery for entity {EntityId}",
                request.EntityId);
            throw;
        }
    }

    public async Task<List<RiskAlertDto>> Handle(GetActiveRiskAlertsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Handling GetActiveRiskAlertsQuery for user {UserId}", request.UserId);

            var alerts = await _riskMonitoringService.GetActiveRiskAlertsAsync(
                request.UserId,
                cancellationToken);

            _logger.LogDebug("Successfully retrieved {Count} active risk alerts", alerts.Count);
            return alerts;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling GetActiveRiskAlertsQuery for user {UserId}", request.UserId);
            throw;
        }
    }
}

