using MobileWorkflow.Domain.Entities;
using Shared.Domain.Common;

namespace MobileWorkflow.Domain.Repositories;

public interface IDriverSessionRepository
{
    void Add(DriverSession driverSession);
    Task<DriverSession?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<DriverSession?> GetActiveSessionAsync(Guid driverId, CancellationToken cancellationToken = default);
    Task<DriverSession?> GetActiveSessionByUserIdAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<List<DriverSession>> GetByDriverIdAsync(Guid driverId, DateTime? fromDate = null, CancellationToken cancellationToken = default);
    Task<List<DriverSession>> GetActiveSessionsAsync(CancellationToken cancellationToken = default);
    Task<List<DriverSession>> GetByStatusAsync(string status, CancellationToken cancellationToken = default);
    Task<List<DriverSession>> GetByDateRangeAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default);
    Task<DriverSession?> GetLatestByDriverIdAsync(Guid driverId, CancellationToken cancellationToken = default);
    Task<int> GetActiveSessionsCountAsync(CancellationToken cancellationToken = default);
    Task<TimeSpan> GetTotalDrivingTimeAsync(Guid driverId, DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default);
    void Update(DriverSession driverSession);
    Task UpdateAsync(DriverSession driverSession, CancellationToken cancellationToken = default);
    void Remove(DriverSession driverSession);
    Task SaveChangesAsync(CancellationToken cancellationToken = default);
}

public interface ILocalizationConfigurationRepository
{
    void Add(LocalizationConfiguration configuration);
    Task<LocalizationConfiguration> AddAsync(LocalizationConfiguration configuration, CancellationToken cancellationToken = default);
    Task<LocalizationConfiguration?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<LocalizationConfiguration?> GetByDriverIdAsync(Guid driverId, CancellationToken cancellationToken = default);
    Task<LocalizationConfiguration?> GetByLanguageCodeAsync(string languageCode, CancellationToken cancellationToken = default);
    Task<List<LocalizationConfiguration>> GetActiveConfigurationsAsync(CancellationToken cancellationToken = default);
    Task<List<LocalizationConfiguration>> GetByRegionAsync(string region, CancellationToken cancellationToken = default);
    Task<LocalizationConfiguration?> GetDefaultConfigurationAsync(CancellationToken cancellationToken = default);
    Task<List<string>> GetSupportedLanguagesAsync(CancellationToken cancellationToken = default);
    Task<List<string>> GetSupportedRegionsAsync(CancellationToken cancellationToken = default);
    Task<Dictionary<string, string>> GetTranslationsAsync(string languageCode, string? category = null, CancellationToken cancellationToken = default);
    Task<string?> GetTranslationAsync(string languageCode, string key, CancellationToken cancellationToken = default);
    void Update(LocalizationConfiguration configuration);
    void Remove(LocalizationConfiguration configuration);
    Task SaveChangesAsync(CancellationToken cancellationToken = default);
}

// Domain entities for the new repositories
public class DriverSession : AggregateRoot
{
    public Guid DriverId { get; private set; }
    public string DriverName { get; private set; }
    public string VehicleId { get; private set; }
    public string Status { get; private set; } // Active, Paused, Completed, Cancelled
    public DateTime StartTime { get; private set; }
    public DateTime? EndTime { get; private set; }
    public TimeSpan? Duration { get; private set; }
    public decimal? StartMileage { get; private set; }
    public decimal? EndMileage { get; private set; }
    public decimal? TotalDistance { get; private set; }
    public string? StartLocation { get; private set; }
    public string? EndLocation { get; private set; }
    public Dictionary<string, object> SessionData { get; private set; }
    public List<string> Violations { get; private set; }
    public Dictionary<string, object> PerformanceMetrics { get; private set; }
    public string? Notes { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    private DriverSession()
    {
        SessionData = new Dictionary<string, object>();
        Violations = new List<string>();
        PerformanceMetrics = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();
    }

    public DriverSession(
        Guid driverId,
        string driverName,
        string vehicleId,
        DateTime startTime,
        string? startLocation = null)
    {
        DriverId = driverId;
        DriverName = driverName ?? throw new ArgumentNullException(nameof(driverName));
        VehicleId = vehicleId ?? throw new ArgumentNullException(nameof(vehicleId));
        StartTime = startTime;
        StartLocation = startLocation;
        Status = "Active";
        SessionData = new Dictionary<string, object>();
        Violations = new List<string>();
        PerformanceMetrics = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();
    }

    public void EndSession(DateTime endTime, string? endLocation = null, decimal? endMileage = null)
    {
        EndTime = endTime;
        EndLocation = endLocation;
        EndMileage = endMileage;
        Duration = endTime - StartTime;
        Status = "Completed";

        if (StartMileage.HasValue && EndMileage.HasValue)
        {
            TotalDistance = EndMileage.Value - StartMileage.Value;
        }
    }

    public void PauseSession()
    {
        Status = "Paused";
    }

    public void ResumeSession()
    {
        Status = "Active";
    }

    public void CancelSession()
    {
        Status = "Cancelled";
        EndTime = DateTime.UtcNow;
    }

    public void AddViolation(string violation)
    {
        Violations.Add(violation);
    }

    public void UpdatePerformanceMetric(string metric, object value)
    {
        PerformanceMetrics[metric] = value;
    }
}

public class LocalizationConfiguration : AggregateRoot
{
    public string LanguageCode { get; private set; }
    public string LanguageName { get; private set; }
    public string Region { get; private set; }
    public string CultureCode { get; private set; }
    public bool IsActive { get; private set; }
    public bool IsDefault { get; private set; }
    public Dictionary<string, string> Translations { get; private set; }
    public Dictionary<string, object> FormatSettings { get; private set; }
    public Dictionary<string, object> CurrencySettings { get; private set; }
    public Dictionary<string, object> DateTimeSettings { get; private set; }
    public Dictionary<string, object> NumberSettings { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }
    public DateTime LastUpdated { get; private set; }
    public string? UpdatedBy { get; private set; }

    private LocalizationConfiguration()
    {
        Translations = new Dictionary<string, string>();
        FormatSettings = new Dictionary<string, object>();
        CurrencySettings = new Dictionary<string, object>();
        DateTimeSettings = new Dictionary<string, object>();
        NumberSettings = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();
    }

    public LocalizationConfiguration(
        string languageCode,
        string languageName,
        string region,
        string cultureCode)
    {
        LanguageCode = languageCode ?? throw new ArgumentNullException(nameof(languageCode));
        LanguageName = languageName ?? throw new ArgumentNullException(nameof(languageName));
        Region = region ?? throw new ArgumentNullException(nameof(region));
        CultureCode = cultureCode ?? throw new ArgumentNullException(nameof(cultureCode));
        IsActive = true;
        IsDefault = false;
        Translations = new Dictionary<string, string>();
        FormatSettings = new Dictionary<string, object>();
        CurrencySettings = new Dictionary<string, object>();
        DateTimeSettings = new Dictionary<string, object>();
        NumberSettings = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();
        LastUpdated = DateTime.UtcNow;
    }

    public void AddTranslation(string key, string value)
    {
        Translations[key] = value;
        LastUpdated = DateTime.UtcNow;
    }

    public void AddTranslations(Dictionary<string, string> translations)
    {
        foreach (var translation in translations)
        {
            Translations[translation.Key] = translation.Value;
        }
        LastUpdated = DateTime.UtcNow;
    }

    public void SetAsDefault()
    {
        IsDefault = true;
    }

    public void Activate()
    {
        IsActive = true;
    }

    public void Deactivate()
    {
        IsActive = false;
    }

    public string? GetTranslation(string key)
    {
        return Translations.TryGetValue(key, out var value) ? value : null;
    }
}

// Barcode Scanning Repository Interfaces
public interface IScanSessionRepository
{
    void Add(ScanSession scanSession);
    Task<ScanSession> AddAsync(ScanSession scanSession, CancellationToken cancellationToken = default);
    Task<ScanSession?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<List<ScanSession>> GetByUserIdAsync(Guid userId, bool? isActive = null, CancellationToken cancellationToken = default);
    Task<List<ScanSession>> GetByStatusAsync(string status, CancellationToken cancellationToken = default);
    Task<List<ScanSession>> GetBySessionTypeAsync(string sessionType, CancellationToken cancellationToken = default);
    Task<List<ScanSession>> GetActiveSessionsAsync(CancellationToken cancellationToken = default);
    Task<List<ScanSession>> GetSessionsAsync(DateTime? fromDate = null, CancellationToken cancellationToken = default);
    Task<List<ScanSession>> GetByDateRangeAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default);
    Task<ScanSession?> GetActiveSessionByUserIdAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<int> GetActiveSessionsCountAsync(CancellationToken cancellationToken = default);
    Task<int> GetSessionsCountByUserIdAsync(Guid userId, CancellationToken cancellationToken = default);
    void Update(ScanSession scanSession);
    Task UpdateAsync(ScanSession scanSession, CancellationToken cancellationToken = default);
    void Remove(ScanSession scanSession);
    Task SaveChangesAsync(CancellationToken cancellationToken = default);
}

public interface IScanResultRepository
{
    void Add(ScanResult scanResult);
    Task<ScanResult> AddAsync(ScanResult scanResult, CancellationToken cancellationToken = default);
    Task<ScanResult?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<List<ScanResult>> GetBySessionIdAsync(Guid sessionId, CancellationToken cancellationToken = default);
    Task<List<ScanResult>> GetByUserIdAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<List<ScanResult>> GetByStatusAsync(string status, CancellationToken cancellationToken = default);
    Task<List<ScanResult>> GetByBarcodeFormatAsync(string barcodeFormat, CancellationToken cancellationToken = default);
    Task<List<ScanResult>> GetByDateRangeAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default);
    Task<List<ScanResult>> GetScanResultsAsync(Guid? sessionId = null, Guid? userId = null, DateTime? fromDate = null, CancellationToken cancellationToken = default);
    Task<List<ScanResult>> GetUnprocessedAsync(CancellationToken cancellationToken = default);
    Task<List<ScanResult>> GetFailedScansAsync(Guid? sessionId = null, CancellationToken cancellationToken = default);
    Task<List<ScanResult>> GetDuplicateScansAsync(Guid sessionId, CancellationToken cancellationToken = default);
    Task<ScanResult?> GetByBarcodeDataAsync(Guid sessionId, string barcodeData, CancellationToken cancellationToken = default);
    Task<bool> ExistsByBarcodeDataAsync(Guid sessionId, string barcodeData, CancellationToken cancellationToken = default);
    Task<int> GetScanCountBySessionIdAsync(Guid sessionId, CancellationToken cancellationToken = default);
    void Update(ScanResult scanResult);
    Task UpdateAsync(ScanResult scanResult, CancellationToken cancellationToken = default);
    void Remove(ScanResult scanResult);
    Task SaveChangesAsync(CancellationToken cancellationToken = default);
}

public interface IBarcodeTemplateRepository
{
    void Add(BarcodeTemplate template);
    Task<BarcodeTemplate> AddAsync(BarcodeTemplate template, CancellationToken cancellationToken = default);
    Task<BarcodeTemplate?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<BarcodeTemplate?> GetByNameAsync(string name, CancellationToken cancellationToken = default);
    Task<List<BarcodeTemplate>> GetAllAsync(CancellationToken cancellationToken = default);
    Task<List<BarcodeTemplate>> GetActiveAsync(CancellationToken cancellationToken = default);
    Task<List<BarcodeTemplate>> GetByBarcodeFormatAsync(string barcodeFormat, CancellationToken cancellationToken = default);
    Task<List<BarcodeTemplate>> GetByCreatedByAsync(string createdBy, CancellationToken cancellationToken = default);
    Task<List<BarcodeTemplate>> GetByDateRangeAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default);
    Task<List<BarcodeTemplate>> SearchAsync(string searchTerm, CancellationToken cancellationToken = default);
    Task<bool> ExistsByNameAsync(string name, CancellationToken cancellationToken = default);
    Task<int> GetCountAsync(CancellationToken cancellationToken = default);
    Task<int> GetActiveCountAsync(CancellationToken cancellationToken = default);
    void Update(BarcodeTemplate template);
    Task UpdateAsync(BarcodeTemplate template, CancellationToken cancellationToken = default);
    void Remove(BarcodeTemplate template);
    Task SaveChangesAsync(CancellationToken cancellationToken = default);
}
