using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using TripManagement.Application.DTOs;
using TripManagement.Application.Interfaces;

namespace TripManagement.Application.Queries.GetTripPlan;

public class GetTripPlanQueryHandler : IRequestHandler<GetTripPlanQuery, TripPlanDto?>
{
    private readonly ITripRepository _tripRepository;
    private readonly INetworkFleetService _networkFleetService;
    private readonly IMapper _mapper;
    private readonly ILogger<GetTripPlanQueryHandler> _logger;

    public GetTripPlanQueryHandler(
        ITripRepository tripRepository,
        INetworkFleetService networkFleetService,
        IMapper mapper,
        ILogger<GetTripPlanQueryHandler> logger)
    {
        _tripRepository = tripRepository;
        _networkFleetService = networkFleetService;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<TripPlanDto?> Handle(GetTripPlanQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting trip plan for trip {TripId} by user {UserId}", 
            request.TripId, request.RequestingUserId);

        try
        {
            // Get the trip with legs
            var trip = await _tripRepository.GetByIdWithLegsAsync(request.TripId, cancellationToken);
            if (trip == null)
            {
                _logger.LogWarning("Trip {TripId} not found", request.TripId);
                return null;
            }

            // Validate user has permission to view this trip
            if (trip.CarrierId != request.RequestingUserId)
            {
                _logger.LogWarning("User {UserId} does not have permission to view trip {TripId}", 
                    request.RequestingUserId, request.TripId);
                throw new UnauthorizedAccessException("You do not have permission to view this trip");
            }

            // Build the trip plan
            var tripPlan = await BuildTripPlan(trip, request, cancellationToken);

            _logger.LogInformation("Successfully generated trip plan for trip {TripId}", request.TripId);

            return tripPlan;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting trip plan for trip {TripId}", request.TripId);
            throw;
        }
    }

    private async Task<TripPlanDto> BuildTripPlan(
        Domain.Entities.Trip trip, 
        GetTripPlanQuery request, 
        CancellationToken cancellationToken)
    {
        var legPlans = new List<TripLegPlanDto>();
        var resourceAssignments = new List<ResourceAssignmentDto>();

        // Process each leg
        foreach (var leg in trip.GetLegsInOrder())
        {
            var legPlan = await BuildLegPlan(leg, request, cancellationToken);
            legPlans.Add(legPlan);

            // Build resource assignment
            var resourceAssignment = await BuildResourceAssignment(leg, request, cancellationToken);
            resourceAssignments.Add(resourceAssignment);
        }

        return new TripPlanDto
        {
            TripId = trip.Id,
            TripNumber = trip.TripNumber,
            GeneratedAt = DateTime.UtcNow,
            GeneratedBy = request.RequestingUserId,
            GeneratedByName = "User Name", // Would need to fetch from external service
            IsMultiLegTrip = trip.IsMultiLegTrip(),
            TotalLegs = trip.Legs.Count,
            EstimatedStartTime = trip.EstimatedStartTime,
            EstimatedEndTime = trip.GetEstimatedCompletionTime() ?? trip.EstimatedEndTime,
            TotalEstimatedDuration = (trip.GetEstimatedCompletionTime() ?? trip.EstimatedEndTime) - trip.EstimatedStartTime,
            TotalEstimatedDistance = trip.GetTotalEstimatedDistance(),
            LegPlans = legPlans,
            ResourceAssignments = resourceAssignments,
            SpecialInstructions = trip.SpecialInstructions,
            Notes = trip.Notes,
            IsPrintable = request.GeneratePrintableVersion
        };
    }

    private async Task<TripLegPlanDto> BuildLegPlan(
        Domain.Entities.TripLeg leg, 
        GetTripPlanQuery request, 
        CancellationToken cancellationToken)
    {
        var stops = new List<TripLegStopPlanDto>();

        if (request.IncludeStopDetails)
        {
            foreach (var stop in leg.Stops.OrderBy(s => s.SequenceNumber))
            {
                stops.Add(new TripLegStopPlanDto
                {
                    SequenceNumber = stop.SequenceNumber,
                    StopType = stop.StopType,
                    StopTypeName = stop.StopType.ToString(),
                    Location = _mapper.Map<LocationDto>(stop.Location),
                    LocationName = stop.Location.Address ?? $"{stop.Location.Latitude}, {stop.Location.Longitude}",
                    ContactName = stop.ContactName,
                    ContactPhone = stop.ContactPhone,
                    ScheduledArrival = stop.ScheduledArrival,
                    ScheduledDeparture = stop.ScheduledDeparture,
                    Instructions = stop.Instructions,
                    IsRequired = stop.IsRequired
                });
            }
        }

        // Get resource details if requested
        string? driverName = null;
        string? vehicleRegistration = null;

        if (request.IncludeResourceDetails)
        {
            if (leg.DriverId.HasValue)
            {
                var driver = await _networkFleetService.GetDriverDetailsAsync(leg.DriverId.Value, cancellationToken);
                driverName = driver?.FullName;
            }

            if (leg.VehicleId.HasValue)
            {
                var vehicle = await _networkFleetService.GetVehicleDetailsAsync(leg.VehicleId.Value, cancellationToken);
                vehicleRegistration = vehicle?.RegistrationNumber;
            }
        }

        return new TripLegPlanDto
        {
            LegNumber = leg.LegNumber,
            LegName = leg.LegName,
            Description = leg.Description,
            Route = request.IncludeRouteDetails ? _mapper.Map<RouteDto>(leg.Route) : new RouteDto(),
            EstimatedStartTime = leg.EstimatedStartTime,
            EstimatedEndTime = leg.EstimatedEndTime,
            EstimatedDuration = leg.GetEstimatedDuration(),
            EstimatedDistanceKm = leg.EstimatedDistanceKm,
            AssignedDriverId = leg.DriverId,
            AssignedDriverName = driverName,
            AssignedVehicleId = leg.VehicleId,
            AssignedVehicleRegistration = vehicleRegistration,
            IsRequired = leg.IsRequired,
            Priority = leg.Priority,
            SpecialInstructions = leg.SpecialInstructions,
            Stops = stops
        };
    }

    private async Task<ResourceAssignmentDto> BuildResourceAssignment(
        Domain.Entities.TripLeg leg, 
        GetTripPlanQuery request, 
        CancellationToken cancellationToken)
    {
        string? driverName = null;
        string? driverPhone = null;
        string? vehicleRegistration = null;
        string? vehicleDisplayName = null;
        Domain.Enums.VehicleType? vehicleType = null;

        if (request.IncludeResourceDetails)
        {
            if (leg.DriverId.HasValue)
            {
                var driver = await _networkFleetService.GetDriverDetailsAsync(leg.DriverId.Value, cancellationToken);
                if (driver != null)
                {
                    driverName = driver.FullName;
                    driverPhone = driver.PhoneNumber;
                }
            }

            if (leg.VehicleId.HasValue)
            {
                var vehicle = await _networkFleetService.GetVehicleDetailsAsync(leg.VehicleId.Value, cancellationToken);
                if (vehicle != null)
                {
                    vehicleRegistration = vehicle.RegistrationNumber;
                    vehicleDisplayName = vehicle.DisplayName;
                    vehicleType = vehicle.VehicleType;
                }
            }
        }

        return new ResourceAssignmentDto
        {
            LegNumber = leg.LegNumber,
            LegName = leg.LegName,
            DriverId = leg.DriverId,
            DriverName = driverName,
            DriverPhone = driverPhone,
            VehicleId = leg.VehicleId,
            VehicleRegistration = vehicleRegistration,
            VehicleDisplayName = vehicleDisplayName,
            VehicleType = vehicleType,
            IsAssigned = leg.DriverId.HasValue && leg.VehicleId.HasValue,
            AssignedAt = leg.Status == Domain.Enums.TripLegStatus.Assigned ? DateTime.UtcNow : null // This would need to be tracked properly
        };
    }
}
