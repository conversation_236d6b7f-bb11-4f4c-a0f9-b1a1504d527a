using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Identity.Application.Common.Interfaces;
using Identity.Domain.Entities;
using Identity.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;

namespace Identity.Infrastructure.Repositories
{
    public class SubAdminRepository : ISubAdminRepository
    {
        private readonly Identity.Infrastructure.Persistence.IdentityDbContext _context;

        public SubAdminRepository(Identity.Infrastructure.Persistence.IdentityDbContext context)
        {
            _context = context;
        }

        public async Task<SubAdmin> GetByIdAsync(Guid id)
        {
            return await _context.SubAdmins
                .Include(s => s.Roles)
                .Include(s => s.Sessions)
                .FirstOrDefaultAsync(s => s.Id == id);
        }

        public async Task<SubAdmin> GetByUserIdAsync(Guid userId)
        {
            return await _context.SubAdmins
                .Include(s => s.Roles)
                .Include(s => s.Sessions)
                .FirstOrDefaultAsync(s => s.UserId == userId);
        }

        public async Task<SubAdmin> GetByEmailAsync(string email)
        {
            return await _context.SubAdmins
                .Include(s => s.Roles)
                .Include(s => s.Sessions)
                .FirstOrDefaultAsync(s => s.Email.Value == email);
        }

        public async Task<IEnumerable<SubAdmin>> GetAllAsync()
        {
            return await _context.SubAdmins
                .Include(s => s.Roles)
                .OrderBy(s => s.Name)
                .ToListAsync();
        }

        public async Task<IEnumerable<SubAdmin>> GetActiveAsync()
        {
            return await _context.SubAdmins
                .Include(s => s.Roles)
                .Where(s => s.Status == SubAdminStatus.Active)
                .OrderBy(s => s.Name)
                .ToListAsync();
        }

        public async Task<IEnumerable<SubAdmin>> GetByDepartmentAsync(string department)
        {
            return await _context.SubAdmins
                .Include(s => s.Roles)
                .Where(s => s.Department == department)
                .OrderBy(s => s.Name)
                .ToListAsync();
        }

        public async Task<IEnumerable<SubAdmin>> GetBySupervisorAsync(Guid supervisorId)
        {
            return await _context.SubAdmins
                .Include(s => s.Roles)
                .Where(s => s.SupervisorId == supervisorId)
                .OrderBy(s => s.Name)
                .ToListAsync();
        }

        public async Task<IEnumerable<SubAdmin>> GetByStatusAsync(SubAdminStatus status)
        {
            return await _context.SubAdmins
                .Include(s => s.Roles)
                .Where(s => s.Status == status)
                .OrderBy(s => s.Name)
                .ToListAsync();
        }

        public async Task<(IEnumerable<SubAdmin> Items, int TotalCount)> GetPagedAsync(
            int pageNumber,
            int pageSize,
            string? searchTerm = null,
            string? department = null,
            SubAdminStatus? status = null)
        {
            var query = _context.SubAdmins
                .Include(s => s.Roles)
                .AsQueryable();

            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                query = query.Where(s => s.Name.Contains(searchTerm) || 
                                        s.Email.Value.Contains(searchTerm));
            }

            if (!string.IsNullOrWhiteSpace(department))
            {
                query = query.Where(s => s.Department == department);
            }

            if (status.HasValue)
            {
                query = query.Where(s => s.Status == status.Value);
            }

            var totalCount = await query.CountAsync();

            var items = await query
                .OrderBy(s => s.Name)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return (items, totalCount);
        }

        public async Task AddAsync(SubAdmin subAdmin)
        {
            await _context.SubAdmins.AddAsync(subAdmin);
            await _context.SaveChangesAsync();
        }

        public async Task UpdateAsync(SubAdmin subAdmin)
        {
            _context.SubAdmins.Update(subAdmin);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteAsync(Guid id)
        {
            var subAdmin = await _context.SubAdmins.FindAsync(id);
            if (subAdmin != null)
            {
                _context.SubAdmins.Remove(subAdmin);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<bool> ExistsAsync(Guid id)
        {
            return await _context.SubAdmins.AnyAsync(s => s.Id == id);
        }

        public async Task<bool> ExistsByEmailAsync(string email)
        {
            return await _context.SubAdmins.AnyAsync(s => s.Email.Value == email);
        }

        public async Task<int> GetCountByDepartmentAsync(string department)
        {
            return await _context.SubAdmins
                .Where(s => s.Department == department)
                .CountAsync();
        }

        public async Task<IEnumerable<SubAdmin>> GetRecentlyCreatedAsync(int days = 30)
        {
            var cutoffDate = DateTime.UtcNow.AddDays(-days);
            return await _context.SubAdmins
                .Include(s => s.Roles)
                .Where(s => s.CreatedAt >= cutoffDate)
                .OrderByDescending(s => s.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<SubAdmin>> GetInactiveAsync(int days = 90)
        {
            var cutoffDate = DateTime.UtcNow.AddDays(-days);
            return await _context.SubAdmins
                .Include(s => s.Roles)
                .Where(s => s.LastLoginAt == null || s.LastLoginAt < cutoffDate)
                .OrderBy(s => s.Name)
                .ToListAsync();
        }
    }
}
