using ContentManagement.Domain.Entities;
using Shared.Domain.Common;
using ContentManagement.Domain.ValueObjects;
using Shared.Domain.Common;

namespace ContentManagement.Domain.Events;

/// <summary>
/// Domain event fired when content is created
/// </summary>
public record ContentCreatedEvent(Content Content) : IDomainEvent
{
    public Guid Id { get; init; } = Guid.NewGuid();
    public DateTime OccurredOn { get; init; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when content is updated
/// </summary>
public record ContentUpdatedEvent(
    Content Content,
    object OldValues,
    Guid UpdatedBy,
    string UpdatedByName) : IDomainEvent
{
    public Guid Id { get; init; } = Guid.NewGuid();
    public DateTime OccurredOn { get; init; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when content visibility changes
/// </summary>
public record ContentVisibilityChangedEvent(
    Content Content,
    ContentVisibility OldVisibility,
    ContentVisibility NewVisibility,
    Guid UpdatedBy,
    string UpdatedByName) : IDomainEvent
{
    public Guid Id { get; init; } = Guid.NewGuid();
    public DateTime OccurredOn { get; init; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when content sort order changes
/// </summary>
public record ContentSortOrderChangedEvent(
    Content Content,
    int OldSortOrder,
    int NewSortOrder,
    Guid UpdatedBy,
    string UpdatedByName) : IDomainEvent
{
    public Guid Id { get; init; } = Guid.NewGuid();
    public DateTime OccurredOn { get; init; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when content is submitted for review
/// </summary>
public record ContentSubmittedForReviewEvent(
    Content Content,
    Guid SubmittedBy,
    string SubmittedByName) : IDomainEvent
{
    public Guid Id { get; init; } = Guid.NewGuid();
    public DateTime OccurredOn { get; init; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when content is approved
/// </summary>
public record ContentApprovedEvent(
    Content Content,
    Guid ApprovedBy,
    string ApprovedByName) : IDomainEvent
{
    public Guid Id { get; init; } = Guid.NewGuid();
    public DateTime OccurredOn { get; init; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when content is rejected
/// </summary>
public record ContentRejectedEvent(
    Content Content,
    Guid RejectedBy,
    string RejectedByName,
    string Reason) : IDomainEvent
{
    public Guid Id { get; init; } = Guid.NewGuid();
    public DateTime OccurredOn { get; init; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when content is published
/// </summary>
public record ContentPublishedEvent(
    Content Content,
    Guid PublishedBy,
    string PublishedByName) : IDomainEvent
{
    public Guid Id { get; init; } = Guid.NewGuid();
    public DateTime OccurredOn { get; init; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when content is unpublished
/// </summary>
public record ContentUnpublishedEvent(
    Content Content,
    Guid UnpublishedBy,
    string UnpublishedByName) : IDomainEvent
{
    public Guid Id { get; init; } = Guid.NewGuid();
    public DateTime OccurredOn { get; init; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when content is archived
/// </summary>
public record ContentArchivedEvent(
    Content Content,
    Guid ArchivedBy,
    string ArchivedByName) : IDomainEvent
{
    public Guid Id { get; init; } = Guid.NewGuid();
    public DateTime OccurredOn { get; init; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when an attachment is added to content
/// </summary>
public record ContentAttachmentAddedEvent(
    Content Content,
    ContentAttachment Attachment) : IDomainEvent
{
    public Guid Id { get; init; } = Guid.NewGuid();
    public DateTime OccurredOn { get; init; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when an attachment is removed from content
/// </summary>
public record ContentAttachmentRemovedEvent(
    Content Content,
    ContentAttachment Attachment,
    Guid RemovedBy,
    string RemovedByName) : IDomainEvent
{
    public Guid Id { get; init; } = Guid.NewGuid();
    public DateTime OccurredOn { get; init; } = DateTime.UtcNow;
}







