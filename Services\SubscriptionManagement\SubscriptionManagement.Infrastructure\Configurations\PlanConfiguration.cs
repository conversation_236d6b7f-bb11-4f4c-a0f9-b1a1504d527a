using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.ValueObjects;

namespace SubscriptionManagement.Infrastructure.Configurations
{
    public class PlanConfiguration : IEntityTypeConfiguration<Plan>
    {
        public void Configure(EntityTypeBuilder<Plan> builder)
        {
            builder.ToTable("Plans");

            builder.HasKey(p => p.Id);

            builder.Property(p => p.Name)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(p => p.Description)
                .IsRequired()
                .HasMaxLength(500);

            builder.Property(p => p.Type)
                .IsRequired()
                .HasConversion<string>();

            builder.Property(p => p.UserType)
                .IsRequired()
                .HasConversion<string>();

            // Configure Money value object for Price
            builder.OwnsOne(p => p.Price, price =>
            {
                price.Property(m => m.Amount)
                    .HasColumnName("Price")
                    .HasPrecision(18, 2)
                    .IsRequired();

                price.Property(m => m.Currency)
                    .HasColumnName("Currency")
                    .HasMaxLength(3)
                    .IsRequired();
            });

            // Configure BillingCycle value object
            builder.OwnsOne(p => p.BillingCycle, cycle =>
            {
                cycle.Property(c => c.Interval)
                    .HasColumnName("BillingInterval")
                    .HasConversion<string>()
                    .IsRequired();

                cycle.Property(c => c.IntervalCount)
                    .HasColumnName("BillingIntervalCount")
                    .IsRequired();

                cycle.Property(c => c.CustomDays)
                    .HasColumnName("CustomBillingDays");
            });

            // Configure PlanLimits value object
            builder.OwnsOne(p => p.Limits, limits =>
            {
                limits.Property(l => l.RfqLimit)
                    .HasColumnName("RfqLimit");

                limits.Property(l => l.VehicleLimit)
                    .HasColumnName("VehicleLimit");

                limits.Property(l => l.CarrierLimit)
                    .HasColumnName("CarrierLimit");

                limits.Property(l => l.IsUnlimited)
                    .HasColumnName("IsUnlimited")
                    .IsRequired();
            });

            builder.Property(p => p.IsActive)
                .IsRequired();

            builder.Property(p => p.IsPublic)
                .IsRequired();

            builder.Property(p => p.TrialPeriodDays);

            builder.Property(p => p.SetupFeeAmount);

            builder.Property(p => p.SetupFeeCurrency)
                .HasMaxLength(3);

            builder.Property(p => p.CreatedAt)
                .IsRequired();

            builder.Property(p => p.UpdatedAt);

            // Configure relationships
            builder.HasMany(p => p.Features)
                .WithOne()
                .HasForeignKey("PlanId")
                .OnDelete(DeleteBehavior.Cascade);

            // Indexes
            builder.HasIndex(p => p.Name)
                .IsUnique();

            builder.HasIndex(p => new { p.UserType, p.Type });
            builder.HasIndex(p => p.IsActive);
            builder.HasIndex(p => p.IsPublic);
        }
    }
}
