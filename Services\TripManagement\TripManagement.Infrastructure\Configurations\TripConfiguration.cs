using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TripManagement.Domain.Entities;
using TripManagement.Domain.ValueObjects;

namespace TripManagement.Infrastructure.Configurations;

public class TripConfiguration : IEntityTypeConfiguration<Trip>
{
    public void Configure(EntityTypeBuilder<Trip> builder)
    {
        builder.ToTable("trips");

        builder.HasKey(t => t.Id);

        builder.Property(t => t.Id)
            .ValueGeneratedNever();

        builder.Property(t => t.OrderId)
            .IsRequired();

        builder.Property(t => t.TripNumber)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(t => t.CarrierId)
            .IsRequired();

        builder.Property(t => t.DriverId);

        builder.Property(t => t.VehicleId);

        builder.Property(t => t.Status)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(t => t.CreatedAt)
            .IsRequired();

        builder.Property(t => t.AssignedAt);

        builder.Property(t => t.StartedAt);

        builder.Property(t => t.CompletedAt);

        builder.Property(t => t.CancelledAt);

        builder.Property(t => t.CancellationReason)
            .HasMaxLength(500);

        builder.Property(t => t.EstimatedStartTime)
            .IsRequired();

        builder.Property(t => t.EstimatedEndTime)
            .IsRequired();

        builder.Property(t => t.SpecialInstructions)
            .HasMaxLength(1000);

        builder.Property(t => t.IsUrgent)
            .IsRequired();

        builder.Property(t => t.EstimatedDistanceKm)
            .HasPrecision(10, 2);

        builder.Property(t => t.ActualDistanceKm)
            .HasPrecision(10, 2);

        builder.Property(t => t.Notes)
            .HasMaxLength(2000);

        // Configure Route as owned entity
        builder.OwnsOne(t => t.Route, route =>
        {
            route.OwnsOne(r => r.StartLocation, startLocation =>
            {
                startLocation.Property(l => l.Latitude)
                    .IsRequired()
                    .HasPrecision(10, 7);

                startLocation.Property(l => l.Longitude)
                    .IsRequired()
                    .HasPrecision(10, 7);

                startLocation.Property(l => l.Altitude)
                    .HasPrecision(10, 2);

                startLocation.Property(l => l.Accuracy)
                    .HasPrecision(10, 2);

                startLocation.Property(l => l.Timestamp)
                    .IsRequired();

                startLocation.Property(l => l.Address)
                    .HasMaxLength(500);

                startLocation.Property(l => l.City)
                    .HasMaxLength(100);

                startLocation.Property(l => l.State)
                    .HasMaxLength(100);

                startLocation.Property(l => l.Country)
                    .HasMaxLength(100);

                startLocation.Property(l => l.PostalCode)
                    .HasMaxLength(20);
            });

            route.OwnsOne(r => r.EndLocation, endLocation =>
            {
                endLocation.Property(l => l.Latitude)
                    .IsRequired()
                    .HasPrecision(10, 7);

                endLocation.Property(l => l.Longitude)
                    .IsRequired()
                    .HasPrecision(10, 7);

                endLocation.Property(l => l.Altitude)
                    .HasPrecision(10, 2);

                endLocation.Property(l => l.Accuracy)
                    .HasPrecision(10, 2);

                endLocation.Property(l => l.Timestamp)
                    .IsRequired();

                endLocation.Property(l => l.Address)
                    .HasMaxLength(500);

                endLocation.Property(l => l.City)
                    .HasMaxLength(100);

                endLocation.Property(l => l.State)
                    .HasMaxLength(100);

                endLocation.Property(l => l.Country)
                    .HasMaxLength(100);

                endLocation.Property(l => l.PostalCode)
                    .HasMaxLength(20);
            });

            route.Property(r => r.EstimatedDistanceKm)
                .HasPrecision(10, 2);

            route.Property(r => r.RouteInstructions)
                .HasMaxLength(2000);

            // Configure waypoints as JSON
            route.Property(r => r.Waypoints)
                .HasConversion(
                    v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                    v => System.Text.Json.JsonSerializer.Deserialize<List<Location>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new List<Location>())
                .HasColumnType("jsonb");
        });

        // Configure relationships
        builder.HasOne(t => t.Driver)
            .WithMany(d => d.Trips)
            .HasForeignKey(t => t.DriverId)
            .OnDelete(DeleteBehavior.SetNull);

        builder.HasOne(t => t.Vehicle)
            .WithMany(v => v.Trips)
            .HasForeignKey(t => t.VehicleId)
            .OnDelete(DeleteBehavior.SetNull);

        builder.HasMany(t => t.Stops)
            .WithOne(s => s.Trip)
            .HasForeignKey(s => s.TripId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(t => t.LocationUpdates)
            .WithOne(l => l.Trip)
            .HasForeignKey(l => l.TripId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(t => t.Exceptions)
            .WithOne(e => e.Trip)
            .HasForeignKey(e => e.TripId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(t => t.Documents)
            .WithOne(d => d.Trip)
            .HasForeignKey(d => d.TripId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(t => t.OrderId)
            .IsUnique();

        builder.HasIndex(t => t.TripNumber)
            .IsUnique();

        builder.HasIndex(t => t.CarrierId);

        builder.HasIndex(t => t.DriverId);

        builder.HasIndex(t => t.VehicleId);

        builder.HasIndex(t => t.Status);

        builder.HasIndex(t => t.CreatedAt);

        builder.HasIndex(t => t.EstimatedStartTime);
    }
}

public class TripStopConfiguration : IEntityTypeConfiguration<TripStop>
{
    public void Configure(EntityTypeBuilder<TripStop> builder)
    {
        builder.ToTable("trip_stops");

        builder.HasKey(ts => ts.Id);

        builder.Property(ts => ts.Id)
            .ValueGeneratedNever();

        builder.Property(ts => ts.TripId)
            .IsRequired();

        builder.Property(ts => ts.StopType)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(ts => ts.SequenceNumber)
            .IsRequired();

        builder.Property(ts => ts.ContactName)
            .HasMaxLength(100);

        builder.Property(ts => ts.ContactPhone)
            .HasMaxLength(20);

        builder.Property(ts => ts.Status)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(ts => ts.Instructions)
            .HasMaxLength(1000);

        builder.Property(ts => ts.Notes)
            .HasMaxLength(1000);

        // Configure Location as owned entity
        builder.OwnsOne(ts => ts.Location, location =>
        {
            location.Property(l => l.Latitude)
                .IsRequired()
                .HasPrecision(10, 7);

            location.Property(l => l.Longitude)
                .IsRequired()
                .HasPrecision(10, 7);

            location.Property(l => l.Altitude)
                .HasPrecision(10, 2);

            location.Property(l => l.Accuracy)
                .HasPrecision(10, 2);

            location.Property(l => l.Timestamp)
                .IsRequired();

            location.Property(l => l.Address)
                .HasMaxLength(500);

            location.Property(l => l.City)
                .HasMaxLength(100);

            location.Property(l => l.State)
                .HasMaxLength(100);

            location.Property(l => l.Country)
                .HasMaxLength(100);

            location.Property(l => l.PostalCode)
                .HasMaxLength(20);
        });

        // Configure relationships
        builder.HasMany(ts => ts.ProofOfDeliveries)
            .WithOne(pod => pod.TripStop)
            .HasForeignKey(pod => pod.TripStopId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(ts => ts.TripId);
        builder.HasIndex(ts => ts.SequenceNumber);
        builder.HasIndex(ts => ts.Status);
    }
}
