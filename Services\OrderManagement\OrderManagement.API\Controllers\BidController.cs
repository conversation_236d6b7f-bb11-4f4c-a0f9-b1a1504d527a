using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OrderManagement.Application.Commands.SubmitBid;
using OrderManagement.Application.Commands.AcceptBid;
using OrderManagement.Application.Commands.RejectBid;
using OrderManagement.Application.Commands.WithdrawBid;
using OrderManagement.Application.Queries.GetBid;
using OrderManagement.Application.Queries.GetMyBids;

namespace OrderManagement.API.Controllers;

[Authorize]
public class BidController : BaseController
{
    /// <summary>
    /// Submit a bid for an RFQ
    /// </summary>
    [HttpPost]
    [ProducesResponseType(typeof(Guid), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> SubmitBid([FromBody] SubmitBidCommand command)
    {
        try
        {
            command.BrokerId = GetCurrentUserId();
            var bidId = await Mediator.Send(command);
            return CreatedAtAction(nameof(GetBid), new { id = bidId }, bidId);
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Get bid by ID
    /// </summary>
    [HttpGet("{id}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetBid(Guid id)
    {
        try
        {
            var userId = GetCurrentUserId();
            var query = new GetBidQuery
            {
                BidId = id,
                RequestingUserId = userId
            };

            var result = await Mediator.Send(query);
            if (result == null)
            {
                return NotFound(new { message = "Bid not found" });
            }

            return Ok(result);
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Get my bids (for brokers)
    /// </summary>
    [HttpGet("my")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetMyBids([FromQuery] int page = 1, [FromQuery] int pageSize = 10)
    {
        try
        {
            var brokerId = GetCurrentUserId();
            var query = new GetMyBidsQuery
            {
                BrokerId = brokerId,
                Page = page,
                PageSize = pageSize
            };

            var result = await Mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Accept a bid (for transport companies)
    /// </summary>
    [HttpPost("{id}/accept")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> AcceptBid(Guid id)
    {
        try
        {
            var transportCompanyId = GetCurrentUserId();
            var command = new AcceptBidCommand
            {
                BidId = id,
                TransportCompanyId = transportCompanyId
            };

            var result = await Mediator.Send(command);
            return Ok(new { success = result, message = "Bid accepted successfully" });
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Reject a bid (for transport companies)
    /// </summary>
    [HttpPost("{id}/reject")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> RejectBid(Guid id, [FromBody] RejectBidRequest request)
    {
        try
        {
            var transportCompanyId = GetCurrentUserId();
            var command = new RejectBidCommand
            {
                BidId = id,
                TransportCompanyId = transportCompanyId,
                RejectionReason = request.Reason
            };

            var result = await Mediator.Send(command);
            return Ok(new { success = result, message = "Bid rejected successfully" });
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Withdraw a bid (for brokers)
    /// </summary>
    [HttpPost("{id}/withdraw")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> WithdrawBid(Guid id)
    {
        try
        {
            var brokerId = GetCurrentUserId();
            var command = new WithdrawBidCommand
            {
                BidId = id,
                BrokerId = brokerId
            };

            var result = await Mediator.Send(command);
            return Ok(new { success = result, message = "Bid withdrawn successfully" });
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }
}

public class RejectBidRequest
{
    public string Reason { get; set; } = string.Empty;
}
