using FluentValidation;

namespace SubscriptionManagement.Application.Commands.UploadPaymentProof
{
    public class UploadPaymentProofCommandValidator : AbstractValidator<UploadPaymentProofCommand>
    {
        public UploadPaymentProofCommandValidator()
        {
            RuleFor(x => x.SubscriptionId)
                .NotEmpty()
                .WithMessage("Subscription ID is required");

            RuleFor(x => x.UserId)
                .NotEmpty()
                .WithMessage("User ID is required");

            RuleFor(x => x.Amount)
                .GreaterThan(0)
                .WithMessage("Amount must be greater than zero")
                .LessThanOrEqualTo(1000000)
                .WithMessage("Amount cannot exceed 1,000,000");

            RuleFor(x => x.Currency)
                .NotEmpty()
                .WithMessage("Currency is required")
                .Length(3)
                .WithMessage("Currency must be a 3-character code")
                .Must(BeValidCurrency)
                .WithMessage("Currency must be a valid currency code (INR, USD, EUR)");

            RuleFor(x => x.PaymentDate)
                .NotEmpty()
                .WithMessage("Payment date is required")
                .LessThanOrEqualTo(DateTime.UtcNow)
                .WithMessage("Payment date cannot be in the future")
                .GreaterThan(DateTime.UtcNow.AddYears(-1))
                .WithMessage("Payment date cannot be more than 1 year ago");

            RuleFor(x => x.ProofImage)
                .NotNull()
                .WithMessage("Proof image is required");

            RuleFor(x => x.Notes)
                .MaximumLength(2000)
                .WithMessage("Notes cannot exceed 2000 characters");

            RuleFor(x => x.PaymentMethod)
                .MaximumLength(100)
                .WithMessage("Payment method cannot exceed 100 characters");

            RuleFor(x => x.TransactionReference)
                .MaximumLength(200)
                .WithMessage("Transaction reference cannot exceed 200 characters");
        }

        private bool BeValidCurrency(string currency)
        {
            var validCurrencies = new[] { "INR", "USD", "EUR" };
            return validCurrencies.Contains(currency?.ToUpperInvariant());
        }
    }
}
