using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using MobileWorkflow.Domain.Entities;
using MobileWorkflow.Domain.Enums;
using Newtonsoft.Json;

namespace MobileWorkflow.Infrastructure.Data.Configurations;

public class SyncOperationConfiguration : IEntityTypeConfiguration<SyncOperation>
{
    public void Configure(EntityTypeBuilder<SyncOperation> builder)
    {
        builder.ToTable("sync_operations");

        builder.HasKey(e => e.Id);

        builder.Property(e => e.UserId)
            .IsRequired();

        builder.Property(e => e.DeviceId)
            .IsRequired();

        builder.Property(e => e.OperationType)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(e => e.Status)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(e => e.StartTime)
            .IsRequired();

        builder.Property(e => e.EndTime);

        builder.Property(e => e.TotalItems)
            .IsRequired();

        builder.Property(e => e.ProcessedItems)
            .IsRequired();

        builder.Property(e => e.FailedItems)
            .IsRequired();

        builder.Property(e => e.ConflictItems)
            .IsRequired();

        builder.Property(e => e.DataSizeBytes)
            .IsRequired();

        builder.Property(e => e.TransferredBytes)
            .IsRequired();

        builder.Property(e => e.ErrorMessage)
            .HasMaxLength(2000);

        builder.Property(e => e.Metadata)
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<Dictionary<string, object>>(v) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        builder.Property(e => e.ConflictResolutions)
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<Dictionary<string, object>>(v) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        builder.Property(e => e.RetryCount)
            .IsRequired();

        builder.Property(e => e.NextRetryTime);

        builder.Property(e => e.NetworkType)
            .HasMaxLength(50);

        builder.Property(e => e.BandwidthKbps);

        builder.Property(e => e.EstimatedTimeRemaining);

        // Relationships
        builder.HasMany(e => e.SyncItems)
            .WithOne(si => si.SyncOperation)
            .HasForeignKey(si => si.SyncOperationId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.Conflicts)
            .WithOne(cr => cr.SyncOperation)
            .HasForeignKey(cr => cr.SyncOperationId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(e => e.UserId);
        builder.HasIndex(e => e.DeviceId);
        builder.HasIndex(e => e.Status);
        builder.HasIndex(e => e.StartTime);
        builder.HasIndex(e => new { e.UserId, e.Status });
        builder.HasIndex(e => new { e.Status, e.NextRetryTime });
    }
}

public class SyncItemConfiguration : IEntityTypeConfiguration<SyncItem>
{
    public void Configure(EntityTypeBuilder<SyncItem> builder)
    {
        builder.ToTable("sync_items");

        builder.HasKey(e => e.Id);

        builder.Property(e => e.SyncOperationId)
            .IsRequired();

        builder.Property(e => e.EntityType)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(e => e.EntityId)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(e => e.Action)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(e => e.Status)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(e => e.Priority)
            .IsRequired();

        builder.Property(e => e.CreatedAt)
            .IsRequired();

        builder.Property(e => e.ProcessedAt);

        builder.Property(e => e.ErrorMessage)
            .HasMaxLength(2000);

        builder.Property(e => e.Data)
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<Dictionary<string, object>>(v) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        builder.Property(e => e.OriginalData)
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<Dictionary<string, object>>(v) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        builder.Property(e => e.DataHash)
            .HasMaxLength(100);

        builder.Property(e => e.DataSizeBytes)
            .IsRequired();

        builder.Property(e => e.Version)
            .IsRequired();

        builder.Property(e => e.LastModified)
            .IsRequired();

        builder.Property(e => e.ConflictType)
            .HasMaxLength(100);

        builder.Property(e => e.ConflictData)
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<Dictionary<string, object>>(v) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        builder.Property(e => e.ConflictResolution)
            .HasMaxLength(100);

        builder.Property(e => e.RetryCount)
            .IsRequired();

        builder.Property(e => e.NextRetryTime);

        builder.Property(e => e.Metadata)
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<Dictionary<string, object>>(v) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        // Indexes
        builder.HasIndex(e => e.SyncOperationId);
        builder.HasIndex(e => e.Status);
        builder.HasIndex(e => e.Priority);
        builder.HasIndex(e => e.CreatedAt);
        builder.HasIndex(e => new { e.EntityType, e.EntityId });
        builder.HasIndex(e => new { e.Status, e.Priority });
        builder.HasIndex(e => new { e.Status, e.NextRetryTime });
    }
}

public class ConflictResolutionConfiguration : IEntityTypeConfiguration<ConflictResolution>
{
    public void Configure(EntityTypeBuilder<ConflictResolution> builder)
    {
        builder.ToTable("conflict_resolutions");

        builder.HasKey(e => e.Id);

        builder.Property(e => e.SyncOperationId)
            .IsRequired();

        builder.Property(e => e.EntityType)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(e => e.EntityId)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(e => e.ConflictType)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(e => e.LocalData)
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<Dictionary<string, object>>(v) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        builder.Property(e => e.RemoteData)
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<Dictionary<string, object>>(v) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        builder.Property(e => e.ConflictDetails)
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<Dictionary<string, object>>(v) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        builder.Property(e => e.ResolutionStrategy)
            .HasMaxLength(50);

        builder.Property(e => e.ResolvedData)
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<Dictionary<string, object>>(v) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        builder.Property(e => e.Status)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(e => e.DetectedAt)
            .IsRequired();

        builder.Property(e => e.ResolvedAt);

        builder.Property(e => e.ResolvedBy);

        builder.Property(e => e.ResolutionNotes)
            .HasMaxLength(2000);

        builder.Property(e => e.LocalVersion)
            .IsRequired();

        builder.Property(e => e.RemoteVersion)
            .IsRequired();

        builder.Property(e => e.ResolvedVersion)
            .IsRequired();

        builder.Property(e => e.Metadata)
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<Dictionary<string, object>>(v) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        // Indexes
        builder.HasIndex(e => e.SyncOperationId);
        builder.HasIndex(e => e.Status);
        builder.HasIndex(e => e.ConflictType);
        builder.HasIndex(e => e.DetectedAt);
        builder.HasIndex(e => new { e.EntityType, e.EntityId });
        builder.HasIndex(e => new { e.Status, e.DetectedAt });
    }
}
