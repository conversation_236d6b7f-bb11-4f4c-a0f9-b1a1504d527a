using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Infrastructure.Services;

public class FinancialReportingService : IFinancialReportingService
{
    private readonly IFinancialReportRepository _reportRepository;
    private readonly IReportTemplateRepository _templateRepository;
    private readonly IFinancialDataAggregator _dataAggregator;
    private readonly IFinancialReportGenerator _reportGenerator;
    private readonly ILogger<FinancialReportingService> _logger;
    private readonly FinancialReportConfiguration _configuration;

    public FinancialReportingService(
        IFinancialReportRepository reportRepository,
        IReportTemplateRepository templateRepository,
        IFinancialDataAggregator dataAggregator,
        IFinancialReportGenerator reportGenerator,
        ILogger<FinancialReportingService> logger,
        IOptions<FinancialReportConfiguration> configuration)
    {
        _reportRepository = reportRepository;
        _templateRepository = templateRepository;
        _dataAggregator = dataAggregator;
        _reportGenerator = reportGenerator;
        _logger = logger;
        _configuration = configuration.Value;
    }

    public async Task<FinancialReport> GenerateReportAsync(GenerateFinancialReportRequest request)
    {
        _logger.LogInformation("Generating financial report {ReportName} of type {ReportType}",
            request.ReportName, request.ReportType);

        try
        {
            var report = new FinancialReport(
                request.ReportName,
                request.ReportType,
                request.Description,
                request.PeriodStart,
                request.PeriodEnd,
                request.GeneratedBy,
                request.Parameters);

            await _reportRepository.AddAsync(report);

            // Start report generation in background
            _ = Task.Run(async () => await GenerateReportFileAsync(report, request));

            return report;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error initiating financial report generation");
            throw;
        }
    }

    public async Task<List<FinancialReport>> GetReportsAsync(Guid? userId = null, string? reportType = null)
    {
        try
        {
            if (userId.HasValue)
            {
                return await _reportRepository.GetByUserAsync(userId.Value);
            }
            else if (!string.IsNullOrEmpty(reportType))
            {
                return await _reportRepository.GetByTypeAsync(reportType);
            }
            else
            {
                return await _reportRepository.GetByStatusAsync(FinancialReportStatus.Completed);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting financial reports");
            throw;
        }
    }

    public async Task<FinancialReport?> GetReportAsync(Guid reportId)
    {
        try
        {
            return await _reportRepository.GetByIdAsync(reportId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting financial report {ReportId}", reportId);
            throw;
        }
    }

    public async Task<FinancialMetrics> GetFinancialMetricsAsync(DateTime fromDate, DateTime toDate, FinancialReportFilter? filter = null)
    {
        try
        {
            return await _dataAggregator.AggregateFinancialMetricsAsync(fromDate, toDate, filter);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting financial metrics");
            throw;
        }
    }

    public async Task<RevenueBreakdown> GetRevenueBreakdownAsync(DateTime fromDate, DateTime toDate, FinancialReportFilter? filter = null)
    {
        try
        {
            return await _dataAggregator.AggregateRevenueDataAsync(fromDate, toDate, filter);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting revenue breakdown");
            throw;
        }
    }

    public async Task<ExpenseBreakdown> GetExpenseBreakdownAsync(DateTime fromDate, DateTime toDate, FinancialReportFilter? filter = null)
    {
        try
        {
            return await _dataAggregator.AggregateExpenseDataAsync(fromDate, toDate, filter);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting expense breakdown");
            throw;
        }
    }

    public async Task<ProfitLossStatement> GenerateProfitLossStatementAsync(DateTime fromDate, DateTime toDate, FinancialReportFilter? filter = null)
    {
        try
        {
            var metrics = await _dataAggregator.AggregateFinancialMetricsAsync(fromDate, toDate, filter);
            var expenses = await _dataAggregator.AggregateExpenseDataAsync(fromDate, toDate, filter);

            var statement = new ProfitLossStatement
            {
                PeriodStart = fromDate,
                PeriodEnd = toDate,
                Revenue = metrics.TotalRevenue,
                CostOfGoodsSold = expenses.PaymentProcessingFees + expenses.GatewayFees,
                GrossProfit = metrics.GrossProfit,
                OperatingExpenses = metrics.TotalExpenses,
                OperatingIncome = metrics.TotalRevenue - metrics.TotalExpenses,
                NetIncome = metrics.NetIncome,
                GrossProfitMargin = metrics.GrossProfitMargin,
                NetProfitMargin = metrics.NetProfitMargin
            };

            // Calculate operating margin
            statement.OperatingMargin = statement.Revenue.Amount > 0 
                ? (statement.OperatingIncome.Amount / statement.Revenue.Amount) * 100 
                : 0;

            return statement;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating profit & loss statement");
            throw;
        }
    }

    public async Task<CashFlowStatement> GenerateCashFlowStatementAsync(DateTime fromDate, DateTime toDate, FinancialReportFilter? filter = null)
    {
        try
        {
            var cashFlowItems = await _dataAggregator.AggregateCashFlowDataAsync(fromDate, toDate, filter);

            var statement = new CashFlowStatement
            {
                PeriodStart = fromDate,
                PeriodEnd = toDate,
                OperatingActivities = cashFlowItems.Where(cf => cf.Category == "Operating").ToList(),
                InvestingActivities = cashFlowItems.Where(cf => cf.Category == "Investing").ToList(),
                FinancingActivities = cashFlowItems.Where(cf => cf.Category == "Financing").ToList()
            };

            // Calculate net cash flow
            var totalInflows = cashFlowItems.Where(cf => cf.Amount.Amount > 0).Sum(cf => cf.Amount.Amount);
            var totalOutflows = cashFlowItems.Where(cf => cf.Amount.Amount < 0).Sum(cf => cf.Amount.Amount);
            statement.NetCashFlow = new Money(totalInflows + totalOutflows, "INR");

            // For demo purposes, set opening and closing balances
            statement.OpeningBalance = Money.Zero("INR");
            statement.ClosingBalance = statement.NetCashFlow;

            return statement;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating cash flow statement");
            throw;
        }
    }

    public async Task<ReportTemplate> CreateTemplateAsync(CreateReportTemplateRequest request)
    {
        _logger.LogInformation("Creating report template {TemplateName}", request.TemplateName);

        try
        {
            var template = new ReportTemplate(
                request.TemplateName,
                request.Description,
                request.ReportType,
                request.CreatedBy,
                request.IsDefault);

            // Add configuration
            foreach (var config in request.Configuration)
            {
                template.Configuration[config.Key] = config.Value;
            }

            // Add sections
            foreach (var sectionRequest in request.Sections)
            {
                template.AddSection(
                    sectionRequest.SectionName,
                    sectionRequest.SectionType,
                    sectionRequest.Configuration,
                    sectionRequest.Order);
            }

            await _templateRepository.AddAsync(template);

            _logger.LogInformation("Report template {TemplateName} created successfully", request.TemplateName);
            return template;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating report template {TemplateName}", request.TemplateName);
            throw;
        }
    }

    public async Task<List<ReportTemplate>> GetTemplatesAsync(string? reportType = null)
    {
        try
        {
            if (!string.IsNullOrEmpty(reportType))
            {
                return await _templateRepository.GetByTypeAsync(reportType);
            }
            else
            {
                return await _templateRepository.GetActiveTemplatesAsync();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting report templates");
            throw;
        }
    }

    public async Task<ReportTemplate?> GetTemplateAsync(Guid templateId)
    {
        try
        {
            return await _templateRepository.GetByIdAsync(templateId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting report template {TemplateId}", templateId);
            throw;
        }
    }

    public async Task<byte[]> ExportReportAsync(Guid reportId, string format)
    {
        _logger.LogInformation("Exporting report {ReportId} in {Format} format", reportId, format);

        try
        {
            var report = await _reportRepository.GetByIdAsync(reportId);
            if (report == null)
            {
                throw new InvalidOperationException($"Report {reportId} not found");
            }

            if (report.Status != FinancialReportStatus.Completed)
            {
                throw new InvalidOperationException($"Report {reportId} is not completed");
            }

            // Get report data
            var metrics = await _dataAggregator.AggregateFinancialMetricsAsync(
                report.PeriodStart, report.PeriodEnd);
            var revenue = await _dataAggregator.AggregateRevenueDataAsync(
                report.PeriodStart, report.PeriodEnd);
            var expenses = await _dataAggregator.AggregateExpenseDataAsync(
                report.PeriodStart, report.PeriodEnd);

            return format.ToLowerInvariant() switch
            {
                "excel" => await _reportGenerator.GenerateExcelBytesAsync(report, metrics, revenue, expenses),
                "pdf" => await _reportGenerator.GeneratePdfBytesAsync(report, metrics, revenue, expenses),
                _ => throw new ArgumentException($"Unsupported export format: {format}")
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting report {ReportId}", reportId);
            throw;
        }
    }

    public async Task<List<FinancialTrend>> GetFinancialTrendsAsync(DateTime fromDate, DateTime toDate, string metricType, FinancialReportFilter? filter = null)
    {
        try
        {
            return await _dataAggregator.AggregateTrendDataAsync(fromDate, toDate, metricType, filter);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting financial trends for metric {MetricType}", metricType);
            throw;
        }
    }

    private async Task GenerateReportFileAsync(FinancialReport report, GenerateFinancialReportRequest request)
    {
        try
        {
            report.MarkAsGenerating();
            await _reportRepository.UpdateAsync(report);

            // Get financial data
            var metrics = await _dataAggregator.AggregateFinancialMetricsAsync(
                request.PeriodStart, request.PeriodEnd, request.Filter);
            var revenue = await _dataAggregator.AggregateRevenueDataAsync(
                request.PeriodStart, request.PeriodEnd, request.Filter);
            var expenses = await _dataAggregator.AggregateExpenseDataAsync(
                request.PeriodStart, request.PeriodEnd, request.Filter);

            // Add report sections
            report.AddSection("Financial Summary", "Summary", new Dictionary<string, object>
            {
                { "total_revenue", metrics.TotalRevenue.Amount },
                { "total_expenses", metrics.TotalExpenses.Amount },
                { "net_income", metrics.NetIncome.Amount },
                { "transaction_count", metrics.TotalTransactionCount }
            }, 1);

            report.AddSection("Revenue Breakdown", "Table", new Dictionary<string, object>
            {
                { "by_payment_method", revenue.ByPaymentMethod },
                { "by_gateway", revenue.ByGateway },
                { "daily_breakdown", revenue.DailyBreakdown }
            }, 2);

            report.AddSection("Expense Analysis", "Table", new Dictionary<string, object>
            {
                { "payment_processing_fees", expenses.PaymentProcessingFees.Amount },
                { "gateway_fees", expenses.GatewayFees.Amount },
                { "operational_expenses", expenses.OperationalExpenses.Amount }
            }, 3);

            string filePath;
            long fileSize;

            // Generate report file based on format
            switch (request.Format.ToLowerInvariant())
            {
                case "excel":
                    filePath = await _reportGenerator.GenerateExcelReportAsync(report, metrics, revenue, expenses);
                    break;
                case "pdf":
                    filePath = await _reportGenerator.GeneratePdfReportAsync(report, metrics, revenue, expenses);
                    break;
                case "csv":
                    var trends = await _dataAggregator.AggregateTrendDataAsync(
                        request.PeriodStart, request.PeriodEnd, "revenue", request.Filter);
                    filePath = await _reportGenerator.GenerateCsvReportAsync(report, trends);
                    break;
                default:
                    throw new ArgumentException($"Unsupported report format: {request.Format}");
            }

            // Get file size
            var fileInfo = new FileInfo(filePath);
            fileSize = fileInfo.Length;

            // Create summary
            var reportSummary = new Dictionary<string, object>
            {
                { "total_revenue", metrics.TotalRevenue.Amount },
                { "total_expenses", metrics.TotalExpenses.Amount },
                { "net_income", metrics.NetIncome.Amount },
                { "transaction_count", metrics.TotalTransactionCount },
                { "generated_at", DateTime.UtcNow }
            };

            report.MarkAsCompleted(filePath, request.Format, fileSize, reportSummary);
            await _reportRepository.UpdateAsync(report);

            _logger.LogInformation("Financial report {ReportId} generated successfully at {FilePath}",
                report.Id, filePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating financial report file for {ReportId}", report.Id);
            
            report.MarkAsFailed(ex.Message);
            await _reportRepository.UpdateAsync(report);
        }
    }
}
