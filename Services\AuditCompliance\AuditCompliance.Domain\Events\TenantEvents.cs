using AuditCompliance.Domain.Entities;
using Shared.Domain.Common;

namespace AuditCompliance.Domain.Events;

/// <summary>
/// Event raised when a tenant is created
/// </summary>
public class TenantCreatedEvent : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public Guid TenantId { get; }
    public string TenantName { get; }
    public string TenantCode { get; }
    public TenantPlan Plan { get; }
    public DateTime OccurredOn { get; }

    public TenantCreatedEvent(Guid tenantId, string tenantName, string tenantCode, TenantPlan plan)
    {
        TenantId = tenantId;
        TenantName = tenantName;
        TenantCode = tenantCode;
        Plan = plan;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Event raised when a tenant is updated
/// </summary>
public class TenantUpdatedEvent : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public Guid TenantId { get; }
    public string NewName { get; }
    public string OldName { get; }
    public DateTime OccurredOn { get; }

    public TenantUpdatedEvent(Guid tenantId, string newName, string oldName)
    {
        TenantId = tenantId;
        NewName = newName;
        OldName = oldName;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Event raised when a tenant plan is changed
/// </summary>
public class TenantPlanChangedEvent : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public Guid TenantId { get; }
    public TenantPlan NewPlan { get; }
    public TenantPlan OldPlan { get; }
    public DateTime OccurredOn { get; }

    public TenantPlanChangedEvent(Guid tenantId, TenantPlan newPlan, TenantPlan oldPlan)
    {
        TenantId = tenantId;
        NewPlan = newPlan;
        OldPlan = oldPlan;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Event raised when tenant configuration is updated
/// </summary>
public class TenantConfigurationUpdatedEvent : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public Guid TenantId { get; }
    public TenantConfiguration Configuration { get; }
    public DateTime OccurredOn { get; }

    public TenantConfigurationUpdatedEvent(Guid tenantId, TenantConfiguration configuration)
    {
        TenantId = tenantId;
        Configuration = configuration;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Event raised when a compliance standard is enabled for a tenant
/// </summary>
public class TenantComplianceStandardEnabledEvent : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public Guid TenantId { get; }
    public string Standard { get; }
    public DateTime OccurredOn { get; }

    public TenantComplianceStandardEnabledEvent(Guid tenantId, string standard)
    {
        TenantId = tenantId;
        Standard = standard;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Event raised when a compliance standard is disabled for a tenant
/// </summary>
public class TenantComplianceStandardDisabledEvent : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public Guid TenantId { get; }
    public string Standard { get; }
    public DateTime OccurredOn { get; }

    public TenantComplianceStandardDisabledEvent(Guid tenantId, string standard)
    {
        TenantId = tenantId;
        Standard = standard;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Event raised when tenant compliance settings are updated
/// </summary>
public class TenantComplianceSettingsUpdatedEvent : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public Guid TenantId { get; }
    public Dictionary<string, object> Settings { get; }
    public DateTime OccurredOn { get; }

    public TenantComplianceSettingsUpdatedEvent(Guid tenantId, Dictionary<string, object> settings)
    {
        TenantId = tenantId;
        Settings = settings;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Event raised when a tenant is deactivated
/// </summary>
public class TenantDeactivatedEvent : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public Guid TenantId { get; }
    public string Reason { get; }
    public DateTime OccurredOn { get; }

    public TenantDeactivatedEvent(Guid tenantId, string reason)
    {
        TenantId = tenantId;
        Reason = reason;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Event raised when a tenant is reactivated
/// </summary>
public class TenantReactivatedEvent : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public Guid TenantId { get; }
    public DateTime OccurredOn { get; }

    public TenantReactivatedEvent(Guid tenantId)
    {
        TenantId = tenantId;
        OccurredOn = DateTime.UtcNow;
    }
}

/// <summary>
/// Event raised when a tenant is suspended
/// </summary>
public class TenantSuspendedEvent : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public Guid TenantId { get; }
    public string Reason { get; }
    public DateTime OccurredOn { get; }

    public TenantSuspendedEvent(Guid tenantId, string reason)
    {
        TenantId = tenantId;
        Reason = reason;
        OccurredOn = DateTime.UtcNow;
    }
}


