using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MobileWorkflow.Infrastructure.Services;

namespace MobileWorkflow.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize(Roles = "Driver,Carrier")]
public class DriverMobileController : ControllerBase
{
    private readonly IDriverMobileAppService _driverMobileService;
    private readonly ILogger<DriverMobileController> _logger;

    public DriverMobileController(
        IDriverMobileAppService driverMobileService,
        ILogger<DriverMobileController> logger)
    {
        _driverMobileService = driverMobileService;
        _logger = logger;
    }

    /// <summary>
    /// Get driver dashboard with current status and pending items
    /// </summary>
    [HttpGet("dashboard")]
    public async Task<ActionResult<DriverDashboard>> GetDashboard()
    {
        var driverId = GetCurrentUserId();
        var dashboard = await _driverMobileService.GetDriverDashboardAsync(driverId);
        return Ok(dashboard);
    }

    /// <summary>
    /// Get pending trip assignments for driver
    /// </summary>
    [HttpGet("trip-assignments")]
    public async Task<ActionResult<List<TripAssignment>>> GetPendingTripAssignments()
    {
        var driverId = GetCurrentUserId();
        var assignments = await _driverMobileService.GetPendingTripAssignmentsAsync(driverId);
        return Ok(assignments);
    }

    /// <summary>
    /// Accept a trip assignment
    /// </summary>
    [HttpPost("trip-assignments/{tripId}/accept")]
    public async Task<ActionResult<TripAssignmentResult>> AcceptTripAssignment(Guid tripId)
    {
        var driverId = GetCurrentUserId();
        var result = await _driverMobileService.AcceptTripAssignmentAsync(driverId, tripId);

        if (!result.IsSuccess)
            return BadRequest(result.ErrorMessage);

        return Ok(result);
    }

    /// <summary>
    /// Reject a trip assignment
    /// </summary>
    [HttpPost("trip-assignments/{tripId}/reject")]
    public async Task<ActionResult<TripAssignmentResult>> RejectTripAssignment(Guid tripId, [FromBody] RejectTripRequest request)
    {
        var driverId = GetCurrentUserId();
        var result = await _driverMobileService.RejectTripAssignmentAsync(driverId, tripId, request.Reason);

        if (!result.IsSuccess)
            return BadRequest(result.ErrorMessage);

        return Ok(result);
    }

    /// <summary>
    /// Update trip status
    /// </summary>
    [HttpPut("trips/{tripId}/status")]
    public async Task<ActionResult> UpdateTripStatus(Guid tripId, [FromBody] UpdateTripStatusRequest request)
    {
        var driverId = GetCurrentUserId();
        var success = await _driverMobileService.UpdateTripStatusAsync(driverId, tripId, request.Status, request.StatusData);

        if (!success)
            return BadRequest("Failed to update trip status");

        return NoContent();
    }

    /// <summary>
    /// Update driver location
    /// </summary>
    [HttpPost("location")]
    public async Task<ActionResult> UpdateLocation([FromBody] LocationUpdate locationUpdate)
    {
        var driverId = GetCurrentUserId();
        var success = await _driverMobileService.UpdateDriverLocationAsync(driverId, locationUpdate);

        if (!success)
            return BadRequest("Failed to update location");

        return NoContent();
    }

    /// <summary>
    /// Upload Proof of Delivery (POD)
    /// </summary>
    [HttpPost("trips/{tripId}/pod")]
    public async Task<ActionResult<PODUploadResult>> UploadPOD(Guid tripId, [FromBody] PODData podData)
    {
        var driverId = GetCurrentUserId();
        var result = await _driverMobileService.UploadPODAsync(driverId, tripId, podData);

        if (!result.IsSuccess)
            return BadRequest(result.ErrorMessage);

        return Ok(result);
    }

    /// <summary>
    /// Upload document (license, insurance, etc.)
    /// </summary>
    [HttpPost("documents")]
    public async Task<ActionResult<DocumentUploadResult>> UploadDocument([FromBody] DocumentUploadData documentData)
    {
        var driverId = GetCurrentUserId();
        var result = await _driverMobileService.UploadDocumentAsync(driverId, documentData);

        if (!result.IsSuccess)
            return BadRequest(result.ErrorMessage);

        return Ok(result);
    }

    /// <summary>
    /// Get enhanced features based on subscription tier
    /// </summary>
    [HttpGet("enhanced-features")]
    public async Task<ActionResult<List<EnhancedFeature>>> GetEnhancedFeatures([FromQuery] string subscriptionTier = "Basic")
    {
        var driverId = GetCurrentUserId();
        var features = await _driverMobileService.GetEnhancedFeaturesAsync(driverId, subscriptionTier);
        return Ok(features);
    }

    /// <summary>
    /// Record offline action for later sync
    /// </summary>
    [HttpPost("offline-actions")]
    public async Task<ActionResult> RecordOfflineAction([FromBody] OfflineAction action)
    {
        var driverId = GetCurrentUserId();
        var success = await _driverMobileService.RecordOfflineActionAsync(driverId, action);

        if (!success)
            return BadRequest("Failed to record offline action");

        return NoContent();
    }

    /// <summary>
    /// Get driver performance metrics
    /// </summary>
    [HttpGet("performance")]
    public async Task<ActionResult<DriverPerformanceMetrics>> GetPerformanceMetrics([FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
    {
        var driverId = GetCurrentUserId();

        // Mock performance metrics - in real implementation, this would come from analytics service
        var metrics = new DriverPerformanceMetrics
        {
            DriverId = driverId,
            Period = new DateRange
            {
                StartDate = startDate ?? DateTime.UtcNow.AddDays(-30),
                EndDate = endDate ?? DateTime.UtcNow
            },
            TotalTrips = 45,
            CompletedTrips = 43,
            CancelledTrips = 2,
            TotalEarnings = 12500.00m,
            AverageRating = 4.7,
            OnTimeDeliveryRate = 95.5,
            FuelEfficiency = 12.5,
            SafetyScore = 98.2
        };

        return Ok(metrics);
    }

    /// <summary>
    /// Get driver mobile app configuration including session timeout and security settings
    /// </summary>
    [HttpGet("config")]
    public async Task<ActionResult<DriverMobileConfig>> GetDriverMobileConfig()
    {
        var driverId = GetCurrentUserId();
        var config = await _driverMobileService.GetDriverMobileConfigAsync(driverId);
        return Ok(config);
    }

    /// <summary>
    /// Update driver location sharing preferences
    /// </summary>
    [HttpPut("location-preferences")]
    public async Task<ActionResult> UpdateLocationPreferences([FromBody] LocationPreferencesRequest request)
    {
        var driverId = GetCurrentUserId();
        var success = await _driverMobileService.UpdateLocationPreferencesAsync(driverId, request);

        if (!success)
            return BadRequest("Failed to update location preferences");

        return NoContent();
    }

    /// <summary>
    /// Send emergency alert/SOS
    /// </summary>
    [HttpPost("emergency-alert")]
    public async Task<ActionResult<EmergencyAlertResult>> SendEmergencyAlert([FromBody] EmergencyAlertRequest request)
    {
        var driverId = GetCurrentUserId();
        var result = await _driverMobileService.SendEmergencyAlertAsync(driverId, request);

        if (!result.IsSuccess)
            return BadRequest(result.ErrorMessage);

        return Ok(result);
    }

    /// <summary>
    /// Get driver's completed trips with pagination
    /// </summary>
    [HttpGet("completed-trips")]
    public async Task<ActionResult<PagedResult<CompletedTripDto>>> GetCompletedTrips(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null)
    {
        var driverId = GetCurrentUserId();
        var result = await _driverMobileService.GetCompletedTripsAsync(driverId, pageNumber, pageSize, startDate, endDate);
        return Ok(result);
    }

    /// <summary>
    /// Get all feedback for driver
    /// </summary>
    [HttpGet("feedback")]
    public async Task<ActionResult<PagedResult<DriverFeedbackDto>>> GetDriverFeedback(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10)
    {
        var driverId = GetCurrentUserId();
        var result = await _driverMobileService.GetDriverFeedbackAsync(driverId, pageNumber, pageSize);
        return Ok(result);
    }

    /// <summary>
    /// Get driver's average rating and statistics
    /// </summary>
    [HttpGet("rating-stats")]
    public async Task<ActionResult<DriverRatingStats>> GetRatingStats()
    {
        var driverId = GetCurrentUserId();
        var stats = await _driverMobileService.GetDriverRatingStatsAsync(driverId);
        return Ok(stats);
    }

    /// <summary>
    /// Update driver language preference
    /// </summary>
    [HttpPut("language")]
    public async Task<ActionResult> UpdateLanguagePreference([FromBody] LanguagePreferenceRequest request)
    {
        var driverId = GetCurrentUserId();
        var success = await _driverMobileService.UpdateLanguagePreferenceAsync(driverId, request.LanguageCode);

        if (!success)
            return BadRequest("Failed to update language preference");

        return NoContent();
    }

    /// <summary>
    /// Get localized strings for driver app
    /// </summary>
    [HttpGet("localization/{languageCode}")]
    public async Task<ActionResult<Dictionary<string, string>>> GetLocalizationStrings(string languageCode)
    {
        var strings = await _driverMobileService.GetLocalizationStringsAsync(languageCode);
        return Ok(strings);
    }

    /// <summary>
    /// Get enhanced driver profile with entity assignment information
    /// </summary>
    [HttpGet("profile/enhanced")]
    public async Task<ActionResult<EnhancedDriverProfileResponse>> GetEnhancedProfile(
        [FromQuery] bool includeEntityDetails = true,
        [FromQuery] bool includePerformanceMetrics = true,
        [FromQuery] bool includeDocumentStatus = true,
        [FromQuery] bool includeVehicleAssignments = true,
        [FromQuery] bool includeLocationHistory = false,
        [FromQuery] bool includeTripHistory = false,
        [FromQuery] int historyDays = 30)
    {
        var driverId = GetCurrentUserId();
        var profile = await _driverMobileService.GetEnhancedDriverProfileAsync(driverId, new EnhancedProfileRequest
        {
            IncludeEntityDetails = includeEntityDetails,
            IncludePerformanceMetrics = includePerformanceMetrics,
            IncludeDocumentStatus = includeDocumentStatus,
            IncludeVehicleAssignments = includeVehicleAssignments,
            IncludeLocationHistory = includeLocationHistory,
            IncludeTripHistory = includeTripHistory,
            HistoryDays = historyDays
        });

        return Ok(profile);
    }

    /// <summary>
    /// Update driver profile information
    /// </summary>
    [HttpPut("profile")]
    public async Task<ActionResult> UpdateDriverProfile([FromBody] UpdateDriverProfileRequest request)
    {
        var driverId = GetCurrentUserId();
        var success = await _driverMobileService.UpdateDriverProfileAsync(driverId, request);

        if (!success)
            return BadRequest("Failed to update driver profile");

        return NoContent();
    }

    /// <summary>
    /// Get driver entity assignment details
    /// </summary>
    [HttpGet("entity-assignment")]
    public async Task<ActionResult<EntityAssignmentDetails>> GetEntityAssignmentDetails()
    {
        var driverId = GetCurrentUserId();
        var assignment = await _driverMobileService.GetEntityAssignmentDetailsAsync(driverId);
        return Ok(assignment);
    }

    /// <summary>
    /// Get driver document status summary
    /// </summary>
    [HttpGet("documents/status")]
    public async Task<ActionResult<DocumentStatusSummary>> GetDocumentStatusSummary()
    {
        var driverId = GetCurrentUserId();
        var status = await _driverMobileService.GetDocumentStatusSummaryAsync(driverId);
        return Ok(status);
    }

    /// <summary>
    /// Get enhanced driver dashboard with ratings, trips, and navigation shortcuts
    /// </summary>
    [HttpGet("dashboard/enhanced")]
    public async Task<ActionResult<EnhancedDashboardResponse>> GetEnhancedDashboard(
        [FromQuery] bool includeRatingStats = true,
        [FromQuery] bool includeCompletedTrips = true,
        [FromQuery] bool includeFeedback = true,
        [FromQuery] bool includeMapShortcuts = true,
        [FromQuery] bool includePerformanceMetrics = true,
        [FromQuery] int recentTripsCount = 5,
        [FromQuery] int recentFeedbackCount = 3,
        [FromQuery] int performanceDays = 30)
    {
        var driverId = GetCurrentUserId();
        var dashboard = await _driverMobileService.GetEnhancedDashboardAsync(driverId, new EnhancedDashboardRequest
        {
            IncludeRatingStats = includeRatingStats,
            IncludeCompletedTrips = includeCompletedTrips,
            IncludeFeedback = includeFeedback,
            IncludeMapShortcuts = includeMapShortcuts,
            IncludePerformanceMetrics = includePerformanceMetrics,
            RecentTripsCount = recentTripsCount,
            RecentFeedbackCount = recentFeedbackCount,
            PerformanceDays = performanceDays
        });

        return Ok(dashboard);
    }

    /// <summary>
    /// Get map shortcuts for navigation
    /// </summary>
    [HttpGet("map-shortcuts")]
    public async Task<ActionResult<List<MapShortcutDto>>> GetMapShortcuts()
    {
        var driverId = GetCurrentUserId();
        var shortcuts = await _driverMobileService.GetMapShortcutsAsync(driverId);
        return Ok(shortcuts);
    }

    /// <summary>
    /// Add or update a map shortcut
    /// </summary>
    [HttpPost("map-shortcuts")]
    public async Task<ActionResult> AddMapShortcut([FromBody] AddMapShortcutRequest request)
    {
        var driverId = GetCurrentUserId();
        var success = await _driverMobileService.AddMapShortcutAsync(driverId, request);

        if (!success)
            return BadRequest("Failed to add map shortcut");

        return Ok();
    }

    /// <summary>
    /// Delete a map shortcut
    /// </summary>
    [HttpDelete("map-shortcuts/{shortcutId}")]
    public async Task<ActionResult> DeleteMapShortcut(Guid shortcutId)
    {
        var driverId = GetCurrentUserId();
        var success = await _driverMobileService.DeleteMapShortcutAsync(driverId, shortcutId);

        if (!success)
            return BadRequest("Failed to delete map shortcut");

        return NoContent();
    }

    /// <summary>
    /// Upload POD with preview functionality
    /// </summary>
    [HttpPost("pod/upload")]
    public async Task<ActionResult<PODUploadResponse>> UploadPODWithPreview([FromBody] PODUploadRequest request)
    {
        var driverId = GetCurrentUserId();
        var result = await _driverMobileService.UploadPODWithPreviewAsync(driverId, request);
        return Ok(result);
    }

    /// <summary>
    /// Get POD submission status and tracking information
    /// </summary>
    [HttpGet("pod/{podId}/status")]
    public async Task<ActionResult<PODStatusResponse>> GetPODSubmissionStatus(
        Guid podId,
        [FromQuery] bool includeDocuments = true,
        [FromQuery] bool includeProcessingHistory = true)
    {
        var driverId = GetCurrentUserId();
        var status = await _driverMobileService.GetPODSubmissionStatusAsync(driverId, podId, includeDocuments, includeProcessingHistory);
        return Ok(status);
    }

    /// <summary>
    /// Get POD preview before final submission
    /// </summary>
    [HttpPost("pod/preview")]
    public async Task<ActionResult<PODPreviewResponse>> GetPODPreview([FromBody] PODPreviewRequest request)
    {
        var driverId = GetCurrentUserId();
        var preview = await _driverMobileService.GetPODPreviewAsync(driverId, request);
        return Ok(preview);
    }

    /// <summary>
    /// Confirm and submit POD after preview
    /// </summary>
    [HttpPost("pod/confirm")]
    public async Task<ActionResult<PODConfirmationResponse>> ConfirmPODSubmission([FromBody] PODConfirmationRequest request)
    {
        var driverId = GetCurrentUserId();
        var result = await _driverMobileService.ConfirmPODSubmissionAsync(driverId, request);

        if (!result.IsSuccess)
            return BadRequest(result.ErrorMessage);

        return Ok(result);
    }

    /// <summary>
    /// Get list of PODs for driver with status tracking
    /// </summary>
    [HttpGet("pods")]
    public async Task<ActionResult<PagedResult<PODSummaryDto>>> GetDriverPODs(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] string? status = null,
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null)
    {
        var driverId = GetCurrentUserId();
        var pods = await _driverMobileService.GetDriverPODsAsync(driverId, pageNumber, pageSize, status, startDate, endDate);
        return Ok(pods);
    }

    /// <summary>
    /// Get comprehensive document center with viewer and expiry alerts
    /// </summary>
    [HttpGet("documents/center")]
    public async Task<ActionResult<DocumentCenterResponse>> GetDocumentCenter(
        [FromQuery] bool includeExpiredDocuments = true,
        [FromQuery] bool includeExpiringDocuments = true,
        [FromQuery] bool includeDocumentHistory = false,
        [FromQuery] bool includePreviewUrls = true,
        [FromQuery] string? documentTypeFilter = null,
        [FromQuery] string? statusFilter = null,
        [FromQuery] int expiryWarningDays = 30)
    {
        var driverId = GetCurrentUserId();
        var documentCenter = await _driverMobileService.GetDocumentCenterAsync(driverId, new DocumentCenterRequest
        {
            IncludeExpiredDocuments = includeExpiredDocuments,
            IncludeExpiringDocuments = includeExpiringDocuments,
            IncludeDocumentHistory = includeDocumentHistory,
            IncludePreviewUrls = includePreviewUrls,
            DocumentTypeFilter = documentTypeFilter,
            StatusFilter = statusFilter,
            ExpiryWarningDays = expiryWarningDays
        });

        return Ok(documentCenter);
    }

    /// <summary>
    /// Setup document expiry alerts and notifications
    /// </summary>
    [HttpPost("documents/expiry-alerts/setup")]
    public async Task<ActionResult<DocumentExpiryAlertsSetupResponse>> SetupDocumentExpiryAlerts([FromBody] DocumentExpiryAlertsSetupRequest request)
    {
        var driverId = GetCurrentUserId();
        var result = await _driverMobileService.SetupDocumentExpiryAlertsAsync(driverId, request);

        if (!result.IsSuccess)
            return BadRequest(result.ErrorMessage);

        return Ok(result);
    }

    /// <summary>
    /// Get document expiry alerts for driver
    /// </summary>
    [HttpGet("documents/expiry-alerts")]
    public async Task<ActionResult<DocumentExpiryAlertsResponse>> GetDocumentExpiryAlerts(
        [FromQuery] string? alertType = null,
        [FromQuery] bool includeAcknowledged = false)
    {
        var driverId = GetCurrentUserId();
        var alerts = await _driverMobileService.GetDocumentExpiryAlertsAsync(driverId, alertType, includeAcknowledged);
        return Ok(alerts);
    }

    /// <summary>
    /// Acknowledge document expiry alert
    /// </summary>
    [HttpPost("documents/expiry-alerts/{alertId}/acknowledge")]
    public async Task<ActionResult> AcknowledgeExpiryAlert(Guid alertId)
    {
        var driverId = GetCurrentUserId();
        var success = await _driverMobileService.AcknowledgeExpiryAlertAsync(driverId, alertId);

        if (!success)
            return BadRequest("Failed to acknowledge alert");

        return NoContent();
    }

    /// <summary>
    /// Get document viewer with preview and download capabilities
    /// </summary>
    [HttpGet("documents/{documentId}/viewer")]
    public async Task<ActionResult<DocumentViewerResponse>> GetDocumentViewer(Guid documentId)
    {
        var driverId = GetCurrentUserId();
        var viewer = await _driverMobileService.GetDocumentViewerAsync(driverId, documentId);

        if (!viewer.IsSuccess)
            return BadRequest(viewer.ErrorMessage);

        return Ok(viewer);
    }

    /// <summary>
    /// Update driver localization settings
    /// </summary>
    [HttpPut("localization/settings")]
    public async Task<ActionResult<LocalizationSettingsResponse>> UpdateLocalizationSettings([FromBody] LocalizationSettingsRequest request)
    {
        var driverId = GetCurrentUserId();
        var result = await _driverMobileService.UpdateLocalizationSettingsAsync(driverId, request);

        if (!result.IsSuccess)
            return BadRequest(result.ErrorMessage);

        return Ok(result);
    }

    /// <summary>
    /// Get localized content and strings for the app
    /// </summary>
    [HttpGet("localization/content")]
    public async Task<ActionResult<LocalizedContentResponse>> GetLocalizedContent(
        [FromQuery] string languageCode = "en",
        [FromQuery] string? countryCode = null,
        [FromQuery] string? categories = null,
        [FromQuery] bool includeFormatting = true,
        [FromQuery] bool includeRegionalSettings = true,
        [FromQuery] bool includeVoiceContent = false,
        [FromQuery] string? appVersion = null)
    {
        var driverId = GetCurrentUserId();
        var contentCategories = string.IsNullOrWhiteSpace(categories)
            ? new List<string>()
            : categories.Split(',').ToList();

        var content = await _driverMobileService.GetLocalizedContentAsync(driverId, new LocalizedContentRequest
        {
            LanguageCode = languageCode,
            CountryCode = countryCode,
            ContentCategories = contentCategories,
            IncludeFormatting = includeFormatting,
            IncludeRegionalSettings = includeRegionalSettings,
            IncludeVoiceContent = includeVoiceContent,
            AppVersion = appVersion
        });

        return Ok(content);
    }

    /// <summary>
    /// Get available languages and regions
    /// </summary>
    [HttpGet("localization/options")]
    public async Task<ActionResult<LocalizationOptionsResponse>> GetLocalizationOptions()
    {
        var options = await _driverMobileService.GetLocalizationOptionsAsync();
        return Ok(options);
    }

    /// <summary>
    /// Get current driver's localization configuration
    /// </summary>
    [HttpGet("localization/configuration")]
    public async Task<ActionResult<LocalizationConfigurationResponse>> GetLocalizationConfiguration()
    {
        var driverId = GetCurrentUserId();
        var configuration = await _driverMobileService.GetLocalizationConfigurationAsync(driverId);
        return Ok(configuration);
    }

    private Guid GetCurrentUserId()
    {
        // In real implementation, extract from JWT token
        var userIdClaim = User.FindFirst("sub")?.Value ?? User.FindFirst("userId")?.Value;
        return Guid.TryParse(userIdClaim, out var userId) ? userId : Guid.Empty;
    }
}

// Request/Response models
public class RejectTripRequest
{
    public string Reason { get; set; } = string.Empty;
}

public class UpdateTripStatusRequest
{
    public string Status { get; set; } = string.Empty;
    public Dictionary<string, object> StatusData { get; set; } = new();
}

public class DriverPerformanceMetrics
{
    public Guid DriverId { get; set; }
    public DateRange Period { get; set; } = new();
    public int TotalTrips { get; set; }
    public int CompletedTrips { get; set; }
    public int CancelledTrips { get; set; }
    public decimal TotalEarnings { get; set; }
    public double AverageRating { get; set; }
    public double OnTimeDeliveryRate { get; set; }
    public double FuelEfficiency { get; set; }
    public double SafetyScore { get; set; }
}

public class DateRange
{
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
}

// New request/response models for driver app features
public class DriverMobileConfig
{
    public Guid DriverId { get; set; }
    public SessionSettings SessionSettings { get; set; } = new();
    public SecuritySettings SecuritySettings { get; set; } = new();
    public LocationSettings LocationSettings { get; set; } = new();
    public NotificationSettings NotificationSettings { get; set; } = new();
    public LocalizationSettings LocalizationSettings { get; set; } = new();
}

public class SessionSettings
{
    public int InactivityTimeoutMinutes { get; set; } = 30;
    public bool AutoLockEnabled { get; set; } = true;
    public bool RememberDevice { get; set; } = false;
    public int MaxConcurrentSessions { get; set; } = 1;
}

public class SecuritySettings
{
    public bool BiometricAuthEnabled { get; set; } = false;
    public bool RequireOtpLogin { get; set; } = true;
    public int OtpExpiryMinutes { get; set; } = 5;
    public int MaxLoginAttempts { get; set; } = 3;
}

public class LocationSettings
{
    public bool LocationSharingEnabled { get; set; } = true;
    public bool ManualToggleAllowed { get; set; } = true;
    public int LocationUpdateIntervalSeconds { get; set; } = 30;
    public bool ShowGpsTimestamp { get; set; } = true;
}

public class LocalizationSettings
{
    public string LanguageCode { get; set; } = "en";
    public string CountryCode { get; set; } = "US";
    public string TimeZone { get; set; } = "UTC";
    public string DateFormat { get; set; } = "MM/dd/yyyy";
    public string TimeFormat { get; set; } = "HH:mm";
}

public class LocationPreferencesRequest
{
    public bool LocationSharingEnabled { get; set; }
    public int UpdateIntervalSeconds { get; set; } = 30;
}

public class EmergencyAlertRequest
{
    public string AlertType { get; set; } = "SOS"; // SOS, Accident, Breakdown, Medical
    public string? Message { get; set; }
    public double? Latitude { get; set; }
    public double? Longitude { get; set; }
    public string? TripId { get; set; }
    public Dictionary<string, object> AdditionalData { get; set; } = new();
}

public class EmergencyAlertResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public string AlertId { get; set; } = string.Empty;
    public DateTime AlertTime { get; set; }
    public List<string> NotifiedContacts { get; set; } = new();
}

public class CompletedTripDto
{
    public Guid TripId { get; set; }
    public string TripNumber { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public string OriginAddress { get; set; } = string.Empty;
    public string DestinationAddress { get; set; } = string.Empty;
    public decimal Distance { get; set; }
    public decimal Earnings { get; set; }
    public double Rating { get; set; }
    public string? CustomerFeedback { get; set; }
    public string Status { get; set; } = string.Empty;
}

public class DriverFeedbackDto
{
    public Guid FeedbackId { get; set; }
    public Guid TripId { get; set; }
    public string TripNumber { get; set; } = string.Empty;
    public double Rating { get; set; }
    public string? Comments { get; set; }
    public DateTime SubmittedAt { get; set; }
    public string CustomerName { get; set; } = string.Empty;
    public string FeedbackType { get; set; } = string.Empty; // Positive, Negative, Neutral
}

public class DriverRatingStats
{
    public Guid DriverId { get; set; }
    public double AverageRating { get; set; }
    public int TotalRatings { get; set; }
    public Dictionary<int, int> RatingDistribution { get; set; } = new(); // Star -> Count
    public double RecentRating { get; set; } // Last 30 days
    public int RecentRatingCount { get; set; }
    public DateTime LastUpdated { get; set; }
}

public class LanguagePreferenceRequest
{
    public string LanguageCode { get; set; } = string.Empty;
}

public class PagedResult<T>
{
    public List<T> Items { get; set; } = new();
    public int TotalCount { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
    public bool HasNextPage => PageNumber < TotalPages;
    public bool HasPreviousPage => PageNumber > 1;
}

// Enhanced driver profile models
public class EnhancedProfileRequest
{
    public bool IncludeEntityDetails { get; set; } = true;
    public bool IncludePerformanceMetrics { get; set; } = true;
    public bool IncludeDocumentStatus { get; set; } = true;
    public bool IncludeVehicleAssignments { get; set; } = true;
    public bool IncludeLocationHistory { get; set; } = false;
    public bool IncludeTripHistory { get; set; } = false;
    public int HistoryDays { get; set; } = 30;
}

public class EnhancedDriverProfileResponse
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public EnhancedDriverProfile? Profile { get; set; }
}

public class EnhancedDriverProfile
{
    public Guid Id { get; set; }
    public string FullName { get; set; } = string.Empty;
    public string PhoneNumber { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string? ProfilePhotoUrl { get; set; }
    public string Status { get; set; } = string.Empty;
    public bool IsVerified { get; set; }
    public EntityAssignmentDetails EntityAssignment { get; set; } = new();
    public DriverPerformanceSummary Performance { get; set; } = new();
    public DocumentStatusSummary DocumentStatus { get; set; } = new();
    public List<VehicleAssignmentInfo> VehicleAssignments { get; set; } = new();
    public CurrentLocationInfo? CurrentLocation { get; set; }
    public CurrentTripInfo? CurrentTrip { get; set; }
    public DateTime LastActiveAt { get; set; }
}

public class UpdateDriverProfileRequest
{
    public string? PhoneNumber { get; set; }
    public string? Email { get; set; }
    public string? ProfilePhotoUrl { get; set; }
    public List<string> PreferredRoutes { get; set; } = new();
    public List<string> OperationalAreas { get; set; } = new();
    public DriverPreferences Preferences { get; set; } = new();
}

public class EntityAssignmentDetails
{
    public Guid CarrierId { get; set; }
    public string CarrierName { get; set; } = string.Empty;
    public string CarrierType { get; set; } = string.Empty;
    public string CarrierStatus { get; set; } = string.Empty;
    public ContactDetails CarrierContact { get; set; } = new();
    public string? FleetName { get; set; }
    public string? FleetManager { get; set; }
    public List<EntityHierarchy> Hierarchy { get; set; } = new();
    public DateTime AssignedAt { get; set; }
}

public class ContactDetails
{
    public string? Phone { get; set; }
    public string? Email { get; set; }
    public string? Address { get; set; }
}

public class EntityHierarchy
{
    public string Level { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? Manager { get; set; }
    public string? Contact { get; set; }
}

public class DriverPerformanceSummary
{
    public double AverageRating { get; set; }
    public int TotalTrips { get; set; }
    public double OnTimePerformance { get; set; }
    public double SafetyScore { get; set; }
    public string PerformanceGrade { get; set; } = string.Empty;
    public List<string> Alerts { get; set; } = new();
}

public class VehicleAssignmentInfo
{
    public Guid VehicleId { get; set; }
    public string VehicleNumber { get; set; } = string.Empty;
    public string VehicleType { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public DateTime AssignedAt { get; set; }
}

public class DriverPreferences
{
    public string PreferredLanguage { get; set; } = "en";
    public bool LocationSharingEnabled { get; set; } = true;
    public bool NotificationsEnabled { get; set; } = true;
    public Dictionary<string, object> CustomSettings { get; set; } = new();
}

// Enhanced dashboard models
public class EnhancedDashboardRequest
{
    public bool IncludeRatingStats { get; set; } = true;
    public bool IncludeCompletedTrips { get; set; } = true;
    public bool IncludeFeedback { get; set; } = true;
    public bool IncludeMapShortcuts { get; set; } = true;
    public bool IncludePerformanceMetrics { get; set; } = true;
    public int RecentTripsCount { get; set; } = 5;
    public int RecentFeedbackCount { get; set; } = 3;
    public int PerformanceDays { get; set; } = 30;
}

public class EnhancedDashboardResponse
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public EnhancedDashboard? Dashboard { get; set; }
}

public class EnhancedDashboard
{
    public Guid DriverId { get; set; }
    public string DriverName { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public CurrentTripInfo? CurrentTrip { get; set; }
    public RatingStatistics RatingStats { get; set; } = new();
    public List<CompletedTripSummary> RecentCompletedTrips { get; set; } = new();
    public List<FeedbackSummary> RecentFeedback { get; set; } = new();
    public List<MapShortcutDto> MapShortcuts { get; set; } = new();
    public PerformanceMetrics PerformanceMetrics { get; set; } = new();
    public QuickStats QuickStats { get; set; } = new();
    public EarningsSummary Earnings { get; set; } = new();
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
}

public class RatingStatistics
{
    public double AverageRating { get; set; }
    public int TotalRatings { get; set; }
    public Dictionary<int, int> RatingDistribution { get; set; } = new();
    public double RecentRating { get; set; }
    public int RecentRatingCount { get; set; }
    public string RatingGrade { get; set; } = string.Empty;
    public List<string> RatingHighlights { get; set; } = new();
}

public class PerformanceMetrics
{
    public double OnTimePerformance { get; set; }
    public double SafetyScore { get; set; }
    public double FuelEfficiency { get; set; }
    public double CustomerSatisfaction { get; set; }
    public int TotalTripsCompleted { get; set; }
    public string PerformanceGrade { get; set; } = string.Empty;
    public List<string> Alerts { get; set; } = new();
}

public class QuickStats
{
    public int TripsToday { get; set; }
    public int TripsThisWeek { get; set; }
    public int TripsThisMonth { get; set; }
    public decimal EarningsToday { get; set; }
    public decimal EarningsThisWeek { get; set; }
    public decimal EarningsThisMonth { get; set; }
    public int PendingTrips { get; set; }
    public int DocumentsExpiringSoon { get; set; }
}

public class MapShortcutDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public string Address { get; set; } = string.Empty;
    public string ShortcutType { get; set; } = string.Empty;
    public string NavigationUrl { get; set; } = string.Empty;
    public double? DistanceFromCurrentKm { get; set; }
    public int? EstimatedTimeMinutes { get; set; }
    public bool IsFavorite { get; set; }
}

public class AddMapShortcutRequest
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public string Address { get; set; } = string.Empty;
    public string ShortcutType { get; set; } = string.Empty;
    public bool IsFavorite { get; set; } = false;
}

// POD-related models
public class PODUploadRequest
{
    public Guid TripId { get; set; }
    public bool IsPreviewMode { get; set; } = true;
    public bool RequireConfirmation { get; set; } = true;
    public string? ConfirmationToken { get; set; }
    public PODDataDto PODData { get; set; } = new();
}

public class PODDataDto
{
    public List<PODDocumentDto> Documents { get; set; } = new();
    public PODSignatureDto? Signature { get; set; }
    public string? RecipientName { get; set; }
    public string? RecipientTitle { get; set; }
    public string? RecipientPhone { get; set; }
    public string? RecipientEmail { get; set; }
    public DateTime DeliveryDateTime { get; set; } = DateTime.UtcNow;
    public double? DeliveryLatitude { get; set; }
    public double? DeliveryLongitude { get; set; }
    public string? DeliveryAddress { get; set; }
    public string? DeliveryNotes { get; set; }
    public List<string> DeliveryConditions { get; set; } = new();
    public bool RequireRecipientVerification { get; set; } = true;
    public string? VerificationMethod { get; set; }
}

public class PODDocumentDto
{
    public string DocumentType { get; set; } = string.Empty;
    public string FileName { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    public string Base64Content { get; set; } = string.Empty; // Base64 encoded content
    public string? Description { get; set; }
    public bool IsRequired { get; set; }
}

public class PODSignatureDto
{
    public string SignatureData { get; set; } = string.Empty; // Base64 encoded
    public string SignatureFormat { get; set; } = "SVG";
    public DateTime SignedAt { get; set; } = DateTime.UtcNow;
    public string? SignerName { get; set; }
    public string? SignerTitle { get; set; }
    public bool IsDigitalSignature { get; set; } = false;
}

public class PODUploadResponse
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public PODUploadResultDto? Result { get; set; }
    public PODPreviewDataDto? PreviewData { get; set; }
    public string? ConfirmationToken { get; set; }
    public List<ValidationIssueDto> ValidationIssues { get; set; } = new();
    public DateTime ProcessedAt { get; set; } = DateTime.UtcNow;
}

public class PODUploadResultDto
{
    public Guid PODId { get; set; }
    public string PODNumber { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime UploadedAt { get; set; }
    public List<string> UploadedDocuments { get; set; } = new();
    public PODProcessingInfoDto ProcessingInfo { get; set; } = new();
}

public class PODPreviewDataDto
{
    public List<DocumentPreviewDto> DocumentPreviews { get; set; } = new();
    public SignaturePreviewDto? SignaturePreview { get; set; }
    public DeliveryInfoPreviewDto DeliveryInfo { get; set; } = new();
    public List<string> RequiredActions { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public bool IsReadyForSubmission { get; set; }
    public DateTime PreviewGeneratedAt { get; set; } = DateTime.UtcNow;
}

public class DocumentPreviewDto
{
    public string DocumentType { get; set; } = string.Empty;
    public string FileName { get; set; } = string.Empty;
    public string PreviewUrl { get; set; } = string.Empty;
    public string ThumbnailUrl { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public bool IsValid { get; set; }
    public List<string> ValidationMessages { get; set; } = new();
}

public class SignaturePreviewDto
{
    public string PreviewUrl { get; set; } = string.Empty;
    public string SignerName { get; set; } = string.Empty;
    public DateTime SignedAt { get; set; }
    public bool IsValid { get; set; }
    public List<string> ValidationMessages { get; set; } = new();
}

public class DeliveryInfoPreviewDto
{
    public string RecipientName { get; set; } = string.Empty;
    public string RecipientTitle { get; set; } = string.Empty;
    public DateTime DeliveryDateTime { get; set; }
    public string DeliveryAddress { get; set; } = string.Empty;
    public List<string> DeliveryConditions { get; set; } = new();
    public string? DeliveryNotes { get; set; }
    public bool LocationVerified { get; set; }
    public bool RecipientVerified { get; set; }
}

public class PODProcessingInfoDto
{
    public string ProcessingStatus { get; set; } = string.Empty;
    public double ProcessingProgress { get; set; }
    public List<ProcessingStepDto> ProcessingSteps { get; set; } = new();
    public DateTime? CompletedAt { get; set; }
    public string? ErrorMessage { get; set; }
}

public class ProcessingStepDto
{
    public string StepName { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime? StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string? ErrorMessage { get; set; }
}

public class ValidationIssueDto
{
    public string Code { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public string? Field { get; set; }
    public bool IsBlocking { get; set; }
    public string? SuggestedAction { get; set; }
}

public class PODStatusResponse
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public PODStatusDto? Status { get; set; }
}

public class PODStatusDto
{
    public Guid PODId { get; set; }
    public string PODNumber { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string StatusDescription { get; set; } = string.Empty;
    public DateTime SubmittedAt { get; set; }
    public DateTime? ProcessedAt { get; set; }
    public PODProcessingInfoDto ProcessingInfo { get; set; } = new();
    public List<string> AvailableActions { get; set; } = new();
    public DateTime LastUpdated { get; set; }
}

public class PODPreviewRequest
{
    public Guid TripId { get; set; }
    public PODDataDto PODData { get; set; } = new();
}

public class PODPreviewResponse
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public PODPreviewDataDto? PreviewData { get; set; }
    public string? ConfirmationToken { get; set; }
    public List<ValidationIssueDto> ValidationIssues { get; set; } = new();
}

public class PODConfirmationRequest
{
    public Guid TripId { get; set; }
    public string ConfirmationToken { get; set; } = string.Empty;
    public PODDataDto PODData { get; set; } = new();
}

public class PODConfirmationResponse
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public PODUploadResultDto? Result { get; set; }
}

public class PODSummaryDto
{
    public Guid PODId { get; set; }
    public string PODNumber { get; set; } = string.Empty;
    public Guid TripId { get; set; }
    public string TripNumber { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime SubmittedAt { get; set; }
    public DateTime? ProcessedAt { get; set; }
    public string RecipientName { get; set; } = string.Empty;
    public int DocumentCount { get; set; }
    public bool HasSignature { get; set; }
}

// Document Center models
public class DocumentCenterRequest
{
    public bool IncludeExpiredDocuments { get; set; } = true;
    public bool IncludeExpiringDocuments { get; set; } = true;
    public bool IncludeDocumentHistory { get; set; } = false;
    public bool IncludePreviewUrls { get; set; } = true;
    public string? DocumentTypeFilter { get; set; }
    public string? StatusFilter { get; set; }
    public int ExpiryWarningDays { get; set; } = 30;
}

public class DocumentCenterResponse
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DocumentCenterDto? DocumentCenter { get; set; }
}

public class DocumentCenterDto
{
    public Guid DriverId { get; set; }
    public string DriverName { get; set; } = string.Empty;
    public List<DocumentCategoryDto> DocumentCategories { get; set; } = new();
    public DocumentSummaryDto Summary { get; set; } = new();
    public DocumentExpiryAlertsDto ExpiryAlerts { get; set; } = new();
    public List<DocumentActivityDto> RecentActivity { get; set; } = new();
    public List<DocumentRequirementDto> UploadRequirements { get; set; } = new();
    public List<string> AvailableActions { get; set; } = new();
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
}

public class DocumentCategoryDto
{
    public string CategoryName { get; set; } = string.Empty;
    public string CategoryDescription { get; set; } = string.Empty;
    public List<DriverDocumentDto> Documents { get; set; } = new();
    public int TotalDocuments { get; set; }
    public int ValidDocuments { get; set; }
    public int ExpiredDocuments { get; set; }
    public int ExpiringDocuments { get; set; }
    public bool IsRequired { get; set; }
    public string? CategoryIcon { get; set; }
}

public class DriverDocumentDto
{
    public Guid DocumentId { get; set; }
    public string DocumentType { get; set; } = string.Empty;
    public string DocumentName { get; set; } = string.Empty;
    public string FileName { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime UploadedAt { get; set; }
    public DateTime? ExpiryDate { get; set; }
    public string? PreviewUrl { get; set; }
    public string? DownloadUrl { get; set; }
    public string? ThumbnailUrl { get; set; }
    public bool IsExpired { get; set; }
    public bool IsExpiringSoon { get; set; }
    public bool IsValid { get; set; }
    public bool RequiresRenewal { get; set; }
    public int? DaysUntilExpiry { get; set; }
    public List<string> AvailableActions { get; set; } = new();
    public string FileSizeFormatted { get; set; } = string.Empty;
}

public class DocumentExpiryAlertsDto
{
    public List<ExpiryAlertDto> CriticalAlerts { get; set; } = new();
    public List<ExpiryAlertDto> WarningAlerts { get; set; } = new();
    public List<ExpiryAlertDto> InfoAlerts { get; set; } = new();
    public DateTime NextScheduledAlert { get; set; }
    public bool HasCriticalAlerts => CriticalAlerts.Any();
    public bool HasWarningAlerts => WarningAlerts.Any();
}

public class ExpiryAlertDto
{
    public Guid DocumentId { get; set; }
    public string DocumentType { get; set; } = string.Empty;
    public string DocumentName { get; set; } = string.Empty;
    public DateTime? ExpiryDate { get; set; }
    public int? DaysUntilExpiry { get; set; }
    public string AlertType { get; set; } = string.Empty;
    public string AlertMessage { get; set; } = string.Empty;
    public string? ActionRequired { get; set; }
    public DateTime AlertCreatedAt { get; set; }
    public bool IsAcknowledged { get; set; }
}

public class DocumentActivityDto
{
    public Guid ActivityId { get; set; }
    public string ActivityType { get; set; } = string.Empty;
    public string ActivityDescription { get; set; } = string.Empty;
    public DateTime ActivityDate { get; set; }
    public string? PerformedBy { get; set; }
    public string? DocumentName { get; set; }
}

public class DocumentRequirementDto
{
    public string DocumentType { get; set; } = string.Empty;
    public string RequirementName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public bool IsRequired { get; set; }
    public bool IsMissing { get; set; }
    public string Priority { get; set; } = string.Empty;
    public List<string> AcceptedFormats { get; set; } = new();
    public string? UploadInstructions { get; set; }
}

public class DocumentExpiryAlertsSetupRequest
{
    public bool EmailNotificationsEnabled { get; set; } = true;
    public bool SMSNotificationsEnabled { get; set; } = false;
    public bool PushNotificationsEnabled { get; set; } = true;
    public List<int> AlertDays { get; set; } = new() { 30, 15, 7, 1 };
    public string AlertTime { get; set; } = "09:00";
    public List<string> EmailRecipients { get; set; } = new();
    public List<string> SMSRecipients { get; set; } = new();
    public string PreferredLanguage { get; set; } = "en";
}

public class DocumentExpiryAlertsSetupResponse
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public int AlertsScheduled { get; set; }
    public DateTime ConfiguredAt { get; set; }
}

public class DocumentExpiryAlertsResponse
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public List<ExpiryAlertDto> Alerts { get; set; } = new();
}

public class DocumentViewerResponse
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DocumentViewerDto? Viewer { get; set; }
}

public class DocumentViewerDto
{
    public Guid DocumentId { get; set; }
    public string DocumentName { get; set; } = string.Empty;
    public string DocumentType { get; set; } = string.Empty;
    public string FileName { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    public string PreviewUrl { get; set; } = string.Empty;
    public string DownloadUrl { get; set; } = string.Empty;
    public string? ThumbnailUrl { get; set; }
    public long FileSize { get; set; }
    public string FileSizeFormatted { get; set; } = string.Empty;
    public DateTime UploadedAt { get; set; }
    public DateTime? ExpiryDate { get; set; }
    public string Status { get; set; } = string.Empty;
    public bool CanDownload { get; set; } = true;
    public bool CanShare { get; set; } = false;
    public bool CanDelete { get; set; } = false;
    public List<string> AvailableActions { get; set; } = new();
}

// Localization models
public class LocalizationSettingsRequest
{
    public string LanguageCode { get; set; } = "en";
    public string CountryCode { get; set; } = "US";
    public string TimeZone { get; set; } = "UTC";
    public string DateFormat { get; set; } = "MM/dd/yyyy";
    public string TimeFormat { get; set; } = "HH:mm";
    public bool Use24HourFormat { get; set; } = false;
    public string CurrencyCode { get; set; } = "USD";
    public string DistanceUnit { get; set; } = "Miles";
    public string WeightUnit { get; set; } = "Pounds";
    public string TemperatureUnit { get; set; } = "Fahrenheit";
    public bool RightToLeftLayout { get; set; } = false;
    public string FontSize { get; set; } = "Medium";
    public bool HighContrastMode { get; set; } = false;
    public string VoiceLanguage { get; set; } = "en-US";
    public string VoiceGender { get; set; } = "Female";
    public double VoiceSpeed { get; set; } = 1.0;
    public bool EnableVoiceInstructions { get; set; } = true;
    public Dictionary<string, object> CustomSettings { get; set; } = new();
}

public class LocalizationSettingsResponse
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public LocalizationConfigurationDto? Configuration { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class LocalizationConfigurationDto
{
    public Guid ConfigurationId { get; set; }
    public Guid DriverId { get; set; }
    public LocalizationSettingsRequest Settings { get; set; } = new();
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class LocalizedContentRequest
{
    public string LanguageCode { get; set; } = "en";
    public string? CountryCode { get; set; }
    public List<string> ContentCategories { get; set; } = new();
    public bool IncludeFormatting { get; set; } = true;
    public bool IncludeRegionalSettings { get; set; } = true;
    public bool IncludeVoiceContent { get; set; } = false;
    public string? AppVersion { get; set; }
}

public class LocalizedContentResponse
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public LocalizedContentDto? Content { get; set; }
}

public class LocalizedContentDto
{
    public string LanguageCode { get; set; } = string.Empty;
    public string CountryCode { get; set; } = string.Empty;
    public string CultureCode { get; set; } = string.Empty;
    public string LanguageName { get; set; } = string.Empty;
    public string NativeName { get; set; } = string.Empty;
    public bool IsRightToLeft { get; set; }
    public Dictionary<string, Dictionary<string, string>> LocalizedStrings { get; set; } = new();
    public LocalizedFormattingDto Formatting { get; set; } = new();
    public RegionalSettingsDto RegionalSettings { get; set; } = new();
    public VoiceContentDto? VoiceContent { get; set; }
    public DateTime LastUpdated { get; set; }
    public string Version { get; set; } = string.Empty;
    public List<string> AvailableLanguages { get; set; } = new();
    public List<string> SupportedRegions { get; set; } = new();
}

public class LocalizedFormattingDto
{
    public string DateFormat { get; set; } = string.Empty;
    public string TimeFormat { get; set; } = string.Empty;
    public string DateTimeFormat { get; set; } = string.Empty;
    public bool Use24HourFormat { get; set; }
    public string FirstDayOfWeek { get; set; } = string.Empty;
    public List<string> DayNames { get; set; } = new();
    public List<string> ShortDayNames { get; set; } = new();
    public List<string> MonthNames { get; set; } = new();
    public List<string> ShortMonthNames { get; set; } = new();
    public string CurrencyFormat { get; set; } = string.Empty;
    public string CurrencySymbol { get; set; } = string.Empty;
    public string DecimalSeparator { get; set; } = string.Empty;
    public string ThousandsSeparator { get; set; } = string.Empty;
    public string SampleDate { get; set; } = string.Empty;
    public string SampleTime { get; set; } = string.Empty;
    public string SampleCurrency { get; set; } = string.Empty;
}

public class RegionalSettingsDto
{
    public string DistanceUnit { get; set; } = string.Empty;
    public string DistanceUnitShort { get; set; } = string.Empty;
    public string WeightUnit { get; set; } = string.Empty;
    public string WeightUnitShort { get; set; } = string.Empty;
    public string TemperatureUnit { get; set; } = string.Empty;
    public string TemperatureUnitShort { get; set; } = string.Empty;
    public string AddressFormat { get; set; } = string.Empty;
    public string PostalCodeLabel { get; set; } = string.Empty;
    public string PhoneNumberFormat { get; set; } = string.Empty;
    public string DefaultTimeZone { get; set; } = string.Empty;
    public string DrivingSide { get; set; } = string.Empty;
    public List<string> EmergencyNumbers { get; set; } = new();
}

public class VoiceContentDto
{
    public string VoiceLanguage { get; set; } = string.Empty;
    public List<VoiceOptionDto> AvailableVoices { get; set; } = new();
    public Dictionary<string, string> VoiceInstructions { get; set; } = new();
    public Dictionary<string, string> VoiceAlerts { get; set; } = new();
    public VoiceSettingsDto DefaultSettings { get; set; } = new();
}

public class VoiceOptionDto
{
    public string VoiceId { get; set; } = string.Empty;
    public string VoiceName { get; set; } = string.Empty;
    public string Gender { get; set; } = string.Empty;
    public string Accent { get; set; } = string.Empty;
    public bool IsDefault { get; set; }
}

public class VoiceSettingsDto
{
    public double Speed { get; set; } = 1.0;
    public double Pitch { get; set; } = 1.0;
    public double Volume { get; set; } = 1.0;
}

public class LocalizationOptionsResponse
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public LocalizationOptionsDto? Options { get; set; }
}

public class LocalizationOptionsDto
{
    public List<LanguageOptionDto> AvailableLanguages { get; set; } = new();
    public List<CountryOptionDto> SupportedCountries { get; set; } = new();
    public List<string> SupportedTimeZones { get; set; } = new();
    public List<string> SupportedCurrencies { get; set; } = new();
    public List<string> DistanceUnits { get; set; } = new();
    public List<string> WeightUnits { get; set; } = new();
    public List<string> TemperatureUnits { get; set; } = new();
}

public class LanguageOptionDto
{
    public string LanguageCode { get; set; } = string.Empty;
    public string LanguageName { get; set; } = string.Empty;
    public string NativeName { get; set; } = string.Empty;
    public bool IsRightToLeft { get; set; }
    public bool IsSupported { get; set; }
}

public class CountryOptionDto
{
    public string CountryCode { get; set; } = string.Empty;
    public string CountryName { get; set; } = string.Empty;
    public string NativeName { get; set; } = string.Empty;
    public string CurrencyCode { get; set; } = string.Empty;
    public string CurrencySymbol { get; set; } = string.Empty;
    public bool IsSupported { get; set; }
}

public class LocalizationConfigurationResponse
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public LocalizationConfigurationDto? Configuration { get; set; }
}
