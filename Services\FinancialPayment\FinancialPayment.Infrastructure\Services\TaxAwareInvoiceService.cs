using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Application.Interfaces.Repositories;
using SharedKernel.Application.Interfaces;
using Microsoft.Extensions.Logging;

namespace FinancialPayment.Infrastructure.Services;

/// <summary>
/// Tax-aware invoice service that generates invoices with comprehensive tax calculations
/// </summary>
public class TaxAwareInvoiceService
{
    private readonly ITaxConfigurationService _taxConfigurationService;
    private readonly ILogger<TaxAwareInvoiceService> _logger;

    public TaxAwareInvoiceService(
        ITaxConfigurationService taxConfigurationService,
        ILogger<TaxAwareInvoiceService> logger)
    {
        _taxConfigurationService = taxConfigurationService;
        _logger = logger;
    }

    public async Task<TaxAwareInvoice> GenerateInvoiceWithTaxAsync(
        Guid orderId,
        Money baseAmount,
        string string,
        string string,
        ServiceCategory serviceCategory,
        TaxJurisdiction jurisdiction,
        EntityType entityType,
        List<InvoiceLineItem> lineItems,
        TdsSection? tdsSection = null,
        string? hsnCode = null,
        bool hasPan = true,
        string? notes = null)
    {
        _logger.LogInformation("Generating tax-aware invoice for order {OrderId}", orderId);

        try
        {
            // Calculate comprehensive tax for the invoice
            var decimal = await _taxConfigurationService.CalculateComprehensiveTaxAsync(
                baseAmount,
                serviceCategory,
                jurisdiction,
                entityType,
                tdsSection,
                hsnCode,
                hasPan);

            // Create tax-aware invoice
            var invoice = new TaxAwareInvoice(
                orderId,
                baseAmount,
                string,
                string,
                decimal,
                serviceCategory,
                jurisdiction,
                entityType,
                lineItems,
                tdsSection,
                hsnCode,
                hasPan,
                notes);

            _logger.LogInformation("Successfully generated tax-aware invoice {InvoiceId} for order {OrderId}. " +
                                 "Base: {BaseAmount}, GST: {GstAmount}, TDS: {TdsAmount}, Total: {TotalAmount}",
                invoice.Id, orderId,
                baseAmount.Amount,
                decimal.GstAmount.Amount,
                decimal.TdsAmount.Amount,
                invoice.TotalAmount.Amount);

            return invoice;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating tax-aware invoice for order {OrderId}", orderId);
            throw;
        }
    }

    public async Task<TaxAwareInvoice> GenerateCommissionInvoiceAsync(
        EnhancedCommission commission,
        string string,
        string string,
        string? notes = null)
    {
        _logger.LogInformation("Generating commission invoice for commission {CommissionId}", commission.Id);

        try
        {
            // Create line items for commission
            var lineItems = new List<InvoiceLineItem>
            {
                new InvoiceLineItem(
                    "Commission",
                    "Brokerage commission for order services",
                    1,
                    commission.GetGrossCommissionAmount(),
                    commission.TaxDetails.EffectiveGstRate,
                    commission.TaxDetails.AppliedHsnCode)
            };

            // Create tax calculation result from commission tax details
            var decimal = new decimal
            {
                BaseAmount = commission.GetGrossCommissionAmount(),
                GstAmount = commission.TaxDetails.GstAmount,
                TdsAmount = commission.TaxDetails.TdsAmount,
                TotalTaxAmount = commission.TaxDetails.TotalTaxAmount,
                NetAmount = commission.TaxDetails.NetPayableAmount,
                EffectiveGstRate = commission.TaxDetails.EffectiveGstRate,
                EffectiveTdsRate = commission.TaxDetails.EffectiveTdsRate,
                IsReverseChargeApplicable = commission.TaxDetails.IsReverseChargeApplicable,
                AppliedHsnCode = commission.TaxDetails.AppliedHsnCode,
                AppliedRules = commission.TaxDetails.AppliedTaxRules.ToList(),
                Warnings = commission.TaxDetails.TaxWarnings.ToList(),
                CalculatedAt = commission.TaxCalculatedAt ?? DateTime.UtcNow
            };

            // Create commission invoice
            var invoice = new TaxAwareInvoice(
                commission.OrderId,
                commission.GetGrossCommissionAmount(),
                string,
                string,
                decimal,
                ServiceCategory.Brokerage,
                new TaxJurisdiction("India", "Maharashtra", "Mumbai"), // Default jurisdiction
                EntityType.Company,
                lineItems,
                TdsSection.Section194H, // Commission TDS section
                commission.TaxDetails.AppliedHsnCode,
                true,
                notes ?? $"Commission invoice for order {commission.OrderId}");

            // Set commission reference
            invoice.SetCommissionReference(commission.Id);

            _logger.LogInformation("Successfully generated commission invoice {InvoiceId} for commission {CommissionId}",
                invoice.Id, commission.Id);

            return invoice;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating commission invoice for commission {CommissionId}", commission.Id);
            throw;
        }
    }

    public async Task<List<TaxAwareInvoice>> GenerateBulkInvoicesAsync(
        List<BulkInvoiceRequest> requests,
        TaxJurisdiction defaultJurisdiction,
        EntityType defaultEntityType = EntityType.Company)
    {
        _logger.LogInformation("Generating {Count} bulk invoices", requests.Count);

        var invoices = new List<TaxAwareInvoice>();
        var errors = new List<string>();

        foreach (var request in requests)
        {
            try
            {
                var invoice = await GenerateInvoiceWithTaxAsync(
                    request.OrderId,
                    request.BaseAmount,
                    request.string,
                    request.string,
                    request.ServiceCategory,
                    request.Jurisdiction ?? defaultJurisdiction,
                    request.EntityType ?? defaultEntityType,
                    request.LineItems,
                    request.TdsSection,
                    request.HsnCode,
                    request.HasPan,
                    request.Notes);

                invoices.Add(invoice);
            }
            catch (Exception ex)
            {
                errors.Add($"Error generating invoice for order {request.OrderId}: {ex.Message}");
                _logger.LogWarning(ex, "Failed to generate invoice for order {OrderId}", request.OrderId);
            }
        }

        _logger.LogInformation("Successfully generated {SuccessCount} out of {TotalCount} bulk invoices. Errors: {ErrorCount}",
            invoices.Count, requests.Count, errors.Count);

        if (errors.Any())
        {
            _logger.LogWarning("Bulk invoice generation errors: {Errors}", string.Join("; ", errors));
        }

        return invoices;
    }

    public async Task<InvoiceTaxSummary> GetInvoiceTaxSummaryAsync(
        List<TaxAwareInvoice> invoices,
        DateTime fromDate,
        DateTime toDate)
    {
        _logger.LogInformation("Generating tax summary for {Count} invoices from {FromDate} to {ToDate}",
            invoices.Count, fromDate, toDate);

        var filteredInvoices = invoices
            .Where(i => i.InvoiceDate >= fromDate && i.InvoiceDate <= toDate)
            .ToList();

        var summary = new InvoiceTaxSummary
        {
            FromDate = fromDate,
            ToDate = toDate,
            TotalInvoices = filteredInvoices.Count,
            TotalBaseAmount = filteredInvoices.Aggregate(Money.Zero("INR"), (sum, inv) => sum + inv.BaseAmount),
            TotalGstAmount = filteredInvoices.Aggregate(Money.Zero("INR"), (sum, inv) => sum + inv.TaxDetails.GstAmount),
            TotalTdsAmount = filteredInvoices.Aggregate(Money.Zero("INR"), (sum, inv) => sum + inv.TaxDetails.TdsAmount),
            TotalTaxAmount = filteredInvoices.Aggregate(Money.Zero("INR"), (sum, inv) => sum + inv.TaxDetails.TotalTaxAmount),
            TotalAmount = filteredInvoices.Aggregate(Money.Zero("INR"), (sum, inv) => sum + inv.TotalAmount),
            AverageGstRate = filteredInvoices.Any() ? filteredInvoices.Average(i => i.TaxDetails.EffectiveGstRate) : 0,
            AverageTdsRate = filteredInvoices.Any() ? filteredInvoices.Average(i => i.TaxDetails.EffectiveTdsRate) : 0,
            InvoicesByServiceCategory = filteredInvoices
                .GroupBy(i => i.ServiceCategory)
                .ToDictionary(g => g.Key, g => g.Count()),
            InvoicesByJurisdiction = filteredInvoices
                .GroupBy(i => i.Jurisdiction.GetFullJurisdiction())
                .ToDictionary(g => g.Key, g => g.Count()),
            GeneratedAt = DateTime.UtcNow
        };

        return summary;
    }
}

/// <summary>
/// Request object for bulk invoice generation
/// </summary>
public class BulkInvoiceRequest
{
    public Guid OrderId { get; set; }
    public Money BaseAmount { get; set; } = Money.Zero("INR");
    public string string { get; set; } = null!;
    public string string { get; set; } = null!;
    public ServiceCategory ServiceCategory { get; set; }
    public TaxJurisdiction? Jurisdiction { get; set; }
    public EntityType? EntityType { get; set; }
    public List<InvoiceLineItem> LineItems { get; set; } = new();
    public TdsSection? TdsSection { get; set; }
    public string? HsnCode { get; set; }
    public bool HasPan { get; set; } = true;
    public string? Notes { get; set; }
}

/// <summary>
/// Tax summary for a collection of invoices
/// </summary>
public class InvoiceTaxSummary
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public int TotalInvoices { get; set; }
    public Money TotalBaseAmount { get; set; } = Money.Zero("INR");
    public Money TotalGstAmount { get; set; } = Money.Zero("INR");
    public Money TotalTdsAmount { get; set; } = Money.Zero("INR");
    public Money TotalTaxAmount { get; set; } = Money.Zero("INR");
    public Money TotalAmount { get; set; } = Money.Zero("INR");
    public decimal AverageGstRate { get; set; }
    public decimal AverageTdsRate { get; set; }
    public Dictionary<ServiceCategory, int> InvoicesByServiceCategory { get; set; } = new();
    public Dictionary<string, int> InvoicesByJurisdiction { get; set; } = new();
    public DateTime GeneratedAt { get; set; }
}
