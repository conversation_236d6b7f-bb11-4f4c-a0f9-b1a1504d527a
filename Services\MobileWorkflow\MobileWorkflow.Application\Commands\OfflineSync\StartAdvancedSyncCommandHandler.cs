using System;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.Extensions.Logging;
using MobileWorkflow.Application.DTOs;
using MobileWorkflow.Application.Services;

namespace MobileWorkflow.Application.Commands.OfflineSync
{
    public class StartAdvancedSyncCommandHandler : IRequestHandler<StartAdvancedSyncCommand, SyncOperationResult>
    {
        private readonly IAdvancedSyncService _advancedSyncService;
        private readonly ILogger<StartAdvancedSyncCommandHandler> _logger;

        public StartAdvancedSyncCommandHandler(
            IAdvancedSyncService advancedSyncService,
            ILogger<StartAdvancedSyncCommandHandler> logger)
        {
            _advancedSyncService = advancedSyncService;
            _logger = logger;
        }

        public async Task<SyncOperationResult> Handle(StartAdvancedSyncCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Starting advanced sync for user {UserId} on device {DeviceId}",
                    request.UserId, request.DeviceId);

                // Configure sync options based on request
                var syncOptions = new MobileWorkflow.Application.DTOs.SyncOptions
                {
                    ClientVersion = request.Options.ClientVersion,
                    EnableCompression = request.Options.EnableCompression,
                    MaxBandwidthKbps = request.MaxBandwidthKbps,
                    MaxConcurrentItems = 5,
                    Timeout = TimeSpan.FromMinutes(30),
                    CustomOptions = new Dictionary<string, object>()
                };

                // Set priority data types
                if (request.PriorityDataTypes.Count > 0)
                {
                    syncOptions.CustomOptions["PriorityDataTypes"] = string.Join(",", request.PriorityDataTypes);
                }

                // Configure bandwidth optimization
                if (request.EnableBandwidthOptimization)
                {
                    syncOptions.EnableCompression = true;
                    if (request.MaxBandwidthKbps.HasValue)
                    {
                        syncOptions.MaxBandwidthKbps = request.MaxBandwidthKbps.Value;
                    }
                }

                // Configure network constraints
                if (request.SyncOnWiFiOnly)
                {
                    syncOptions.CustomOptions["WiFiOnly"] = "true";
                }

                if (!request.SyncOnLowBattery)
                {
                    syncOptions.CustomOptions["SkipOnLowBattery"] = "true";
                }

                var result = await _advancedSyncService.StartSyncOperationAsync(
                    request.UserId,
                    request.DeviceId,
                    syncOptions,
                    cancellationToken);

                // Enable progressive sync if requested
                if (request.EnableProgressiveSync && result.Success)
                {
                    await _advancedSyncService.EnableProgressiveSyncAsync(
                        result.SyncOperationId ?? Guid.NewGuid(),
                        cancellationToken);
                }

                _logger.LogInformation("Successfully started advanced sync operation {SyncId} for user {UserId}",
                    result.SyncOperationId, request.UserId);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error starting advanced sync for user {UserId}", request.UserId);
                throw;
            }
        }
    }
}
