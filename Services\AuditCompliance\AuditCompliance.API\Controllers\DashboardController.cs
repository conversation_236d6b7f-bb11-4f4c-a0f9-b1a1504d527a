using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using AuditCompliance.Application.Interfaces;

namespace AuditCompliance.API.Controllers;

/// <summary>
/// Controller for real-time compliance dashboard functionality
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class DashboardController : ControllerBase
{
    private readonly IRealTimeDashboardService _dashboardService;
    private readonly IAdvancedAnalyticsService _analyticsService;
    private readonly ILogger<DashboardController> _logger;

    public DashboardController(
        IRealTimeDashboardService dashboardService,
        IAdvancedAnalyticsService analyticsService,
        ILogger<DashboardController> logger)
    {
        _dashboardService = dashboardService;
        _analyticsService = analyticsService;
        _logger = logger;
    }

    /// <summary>
    /// Get current dashboard metrics
    /// </summary>
    [HttpGet("metrics")]
    [Authorize(Roles = "Admin,ComplianceOfficer,Auditor")]
    public async Task<ActionResult<object>> GetDashboardMetrics(
        [FromQuery] Guid? organizationId = null)
    {
        try
        {
            var metrics = await _dashboardService.GetCurrentDashboardMetricsAsync(organizationId);
            return Ok(metrics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting dashboard metrics");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get real-time compliance status
    /// </summary>
    [HttpGet("compliance-status")]
    [Authorize(Roles = "Admin,ComplianceOfficer,Auditor")]
    public async Task<ActionResult<object>> GetComplianceStatus(
        [FromQuery] Guid? organizationId = null)
    {
        try
        {
            var status = await _dashboardService.GetRealTimeComplianceStatusAsync(organizationId);
            return Ok(status);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting compliance status");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get comprehensive dashboard data
    /// </summary>
    [HttpGet("comprehensive")]
    [Authorize(Roles = "Admin,ComplianceOfficer,Auditor")]
    public async Task<ActionResult<object>> GetComprehensiveDashboard(
        [FromQuery] Guid? organizationId = null,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        try
        {
            var from = fromDate ?? DateTime.UtcNow.AddDays(-30);
            var to = toDate ?? DateTime.UtcNow;

            // Get all dashboard components
            var metrics = await _dashboardService.GetCurrentDashboardMetricsAsync(organizationId);
            var status = await _dashboardService.GetRealTimeComplianceStatusAsync(organizationId);
            var insights = await _analyticsService.GenerateComplianceInsightsAsync(organizationId);
            var anomalies = await _analyticsService.DetectComplianceAnomaliesAsync(from, to);
            var modelPerformance = await _analyticsService.GetModelPerformanceAsync();

            var dashboard = new
            {
                Timestamp = DateTime.UtcNow,
                OrganizationId = organizationId,
                Metrics = metrics,
                Status = status,
                Insights = new
                {
                    insights.Overview,
                    TopInsights = insights.Insights.Take(5),
                    TopRecommendations = insights.Recommendations.Take(5),
                    KeyMetrics = insights.KeyMetrics
                },
                Anomalies = anomalies.Take(10),
                ModelStatus = modelPerformance.Select(m => new
                {
                    m.ModelType,
                    m.Accuracy,
                    m.LastTrained,
                    m.IsActive,
                    Status = m.IsActive ? "Active" : "Inactive"
                }),
                Charts = new
                {
                    ComplianceScoreTrend = await GetComplianceScoreTrendAsync(organizationId, from, to),
                    ViolationsByStandard = await GetViolationsByStandardAsync(organizationId, from, to),
                    RiskDistribution = await GetRiskDistributionAsync(organizationId),
                    AuditActivity = await GetAuditActivityAsync(organizationId, from, to)
                }
            };

            return Ok(dashboard);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting comprehensive dashboard");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get dashboard configuration for user
    /// </summary>
    [HttpGet("config")]
    [Authorize]
    public async Task<ActionResult<object>> GetDashboardConfig()
    {
        try
        {
            var userId = User.Identity?.Name ?? "Unknown";
            var userRoles = User.Claims
                .Where(c => c.Type == "role" || c.Type == "http://schemas.microsoft.com/ws/2008/06/identity/claims/role")
                .Select(c => c.Value)
                .ToList();

            var config = new
            {
                UserId = userId,
                Roles = userRoles,
                Permissions = GetUserPermissions(userRoles),
                DefaultRefreshInterval = 30000, // 30 seconds
                AvailableWidgets = GetAvailableWidgets(userRoles),
                NotificationSettings = new
                {
                    EnableRealTimeAlerts = true,
                    EnableAnomalyNotifications = true,
                    EnableRiskScoreUpdates = true,
                    EnableReportUpdates = true
                }
            };

            return Ok(config);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting dashboard config");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Trigger manual dashboard refresh
    /// </summary>
    [HttpPost("refresh")]
    [Authorize(Roles = "Admin,ComplianceOfficer,Auditor")]
    public async Task<ActionResult<object>> RefreshDashboard(
        [FromQuery] Guid? organizationId = null)
    {
        try
        {
            // Get fresh metrics and broadcast to connected clients
            var metrics = await _dashboardService.GetCurrentDashboardMetricsAsync(organizationId);
            await _dashboardService.BroadcastDashboardMetricsUpdateAsync(metrics, organizationId);

            var status = await _dashboardService.GetRealTimeComplianceStatusAsync(organizationId);
            await _dashboardService.BroadcastComplianceStatusUpdateAsync(status, organizationId);

            return Ok(new
            {
                Message = "Dashboard refreshed successfully",
                Timestamp = DateTime.UtcNow,
                OrganizationId = organizationId
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error refreshing dashboard");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get dashboard health status
    /// </summary>
    [HttpGet("health")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<object>> GetDashboardHealth()
    {
        try
        {
            var health = new
            {
                Status = "Healthy",
                Timestamp = DateTime.UtcNow,
                Components = new
                {
                    SignalRHub = "Connected",
                    AnalyticsService = "Running",
                    Database = "Connected",
                    ModelTraining = "Active"
                },
                Metrics = new
                {
                    ConnectedClients = 0, // This would come from SignalR hub
                    ActiveSubscriptions = 0,
                    LastDataUpdate = DateTime.UtcNow.AddMinutes(-2),
                    AverageResponseTime = "150ms"
                }
            };

            return Ok(health);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting dashboard health");
            return StatusCode(500, "Internal server error");
        }
    }

    // Private helper methods
    private async Task<object> GetComplianceScoreTrendAsync(
        Guid? organizationId, DateTime fromDate, DateTime toDate)
    {
        // This would generate compliance score trend data
        // For now, return placeholder data
        var days = (toDate - fromDate).Days;
        var trendData = new List<object>();

        for (int i = 0; i <= days; i++)
        {
            var date = fromDate.AddDays(i);
            trendData.Add(new
            {
                Date = date.ToString("yyyy-MM-dd"),
                Score = 0.75f + (float)(Math.Sin(i * 0.1) * 0.1) // Simulated trend
            });
        }

        return trendData;
    }

    private async Task<object> GetViolationsByStandardAsync(
        Guid? organizationId, DateTime fromDate, DateTime toDate)
    {
        // This would get violations grouped by compliance standard
        return new[]
        {
            new { Standard = "GDPR", Count = 5, Severity = "High" },
            new { Standard = "SOX", Count = 2, Severity = "Medium" },
            new { Standard = "DataRetention", Count = 8, Severity = "Low" },
            new { Standard = "AccessControl", Count = 3, Severity = "High" }
        };
    }

    private async Task<object> GetRiskDistributionAsync(Guid? organizationId)
    {
        // This would get risk distribution across entities
        return new
        {
            Critical = 5,
            High = 15,
            Medium = 30,
            Low = 50
        };
    }

    private async Task<object> GetAuditActivityAsync(
        Guid? organizationId, DateTime fromDate, DateTime toDate)
    {
        // This would get audit activity over time
        return new[]
        {
            new { Date = "2024-01-01", Audits = 12, Violations = 3 },
            new { Date = "2024-01-02", Audits = 8, Violations = 1 },
            new { Date = "2024-01-03", Audits = 15, Violations = 4 },
            new { Date = "2024-01-04", Audits = 10, Violations = 2 }
        };
    }

    private List<string> GetUserPermissions(List<string> roles)
    {
        var permissions = new List<string>();

        if (roles.Contains("Admin"))
        {
            permissions.AddRange(new[]
            {
                "ViewAllDashboards", "ManageAlerts", "ConfigureWidgets",
                "ViewAnalytics", "ManageModels", "ViewSystemHealth"
            });
        }
        else if (roles.Contains("ComplianceOfficer"))
        {
            permissions.AddRange(new[]
            {
                "ViewComplianceDashboard", "ViewAnalytics", "ManageReports", "ViewAlerts"
            });
        }
        else if (roles.Contains("Auditor"))
        {
            permissions.AddRange(new[]
            {
                "ViewAuditDashboard", "ViewAnalytics", "ViewReports"
            });
        }

        return permissions;
    }

    private List<string> GetAvailableWidgets(List<string> roles)
    {
        var widgets = new List<string>
        {
            "ComplianceOverview", "RecentAlerts", "KeyMetrics"
        };

        if (roles.Contains("Admin") || roles.Contains("ComplianceOfficer"))
        {
            widgets.AddRange(new[]
            {
                "RiskHeatmap", "TrendAnalysis", "AnomalyDetection", "ModelPerformance"
            });
        }

        if (roles.Contains("Admin"))
        {
            widgets.AddRange(new[]
            {
                "SystemHealth", "UserActivity", "ModelTraining"
            });
        }

        return widgets;
    }
}
