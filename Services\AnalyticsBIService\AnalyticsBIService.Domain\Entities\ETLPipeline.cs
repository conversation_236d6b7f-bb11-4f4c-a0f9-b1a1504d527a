using Shared.Domain.Common;
using System;

namespace AnalyticsBIService.Domain.Entities;

/// <summary>
/// ETL Pipeline entity for data extraction, transformation, and loading operations
/// </summary>
public class ETLPipeline : BaseEntity
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public string Configuration { get; private set; }
    public string Status { get; private set; }
    public Guid CreatedBy { get; private set; }
    public bool IsActive { get; private set; }
    public DateTime? LastExecuted { get; private set; }
    public int ExecutionCount { get; private set; }
    public string? LastExecutionStatus { get; private set; }

    private ETLPipeline()
    {
        Name = string.Empty;
        Description = string.Empty;
        Configuration = string.Empty;
        Status = string.Empty;
    }

    public ETLPipeline(
        string name,
        string description,
        string configuration,
        Guid createdBy)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        CreatedBy = createdBy;
        Status = "Created";
        IsActive = true;
        ExecutionCount = 0;
    }

    public void UpdateName(string name)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        SetUpdatedAt();
    }

    public void UpdateDescription(string description)
    {
        Description = description ?? throw new ArgumentNullException(nameof(description));
        SetUpdatedAt();
    }

    public void UpdateConfiguration(string configuration)
    {
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        SetUpdatedAt();
    }

    public void UpdateStatus(string status)
    {
        Status = status ?? throw new ArgumentNullException(nameof(status));
        SetUpdatedAt();
    }

    public void RecordExecution(string executionStatus)
    {
        ExecutionCount++;
        LastExecuted = DateTime.UtcNow;
        LastExecutionStatus = executionStatus;
        SetUpdatedAt();
    }

    public void Activate()
    {
        IsActive = true;
        SetUpdatedAt();
    }

    public void Deactivate()
    {
        IsActive = false;
        SetUpdatedAt();
    }
}
