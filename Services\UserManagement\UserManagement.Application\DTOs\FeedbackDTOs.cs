namespace UserManagement.Application.DTOs;

public class OrderFeedbackRequest
{
    public Guid OrderId { get; set; }
    public Guid UserId { get; set; }
    public Guid ReviewerId { get; set; }
    public string ReviewerRole { get; set; } = string.Empty;
    public Guid RevieweeId { get; set; }
    public string RevieweeRole { get; set; } = string.Empty;
    public int Rating { get; set; }
    public decimal OverallRating { get; set; }
    public decimal ServiceQualityRating { get; set; }
    public decimal CommunicationRating { get; set; }
    public decimal TimelinessRating { get; set; }
    public decimal ProfessionalismRating { get; set; }
    public string Comments { get; set; } = string.Empty;
    public bool IsAnonymous { get; set; }
    public string FeedbackType { get; set; } = "Order";
    public List<string> Categories { get; set; } = new();
    public List<string> Tags { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
    public bool NotifyReviewee { get; set; }
}

public class FeedbackAnalyticsQuery
{
    public Guid? UserId { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
}

public class FeedbackAnalyticsResult
{
    public double AverageRating { get; set; }
    public int TotalFeedbacks { get; set; }
    public Dictionary<int, int> RatingDistribution { get; set; } = new();
    public decimal AverageOverallRating { get; set; }
    public decimal AverageServiceQualityRating { get; set; }
    public decimal AverageCommunicationRating { get; set; }
    public decimal AverageTimelinessRating { get; set; }
    public decimal AverageProfessionalismRating { get; set; }
    public List<OrderFeedbackDto> RecentFeedbacks { get; set; } = new();
    public Dictionary<string, int> CategoryBreakdown { get; set; } = new();
    public List<TrendPoint> TrendData { get; set; } = new();
    public List<string> TopIssues { get; set; } = new();
    public List<string> ImprovementSuggestions { get; set; } = new();
    public DateTime GeneratedAt { get; set; }
}

public class RedFlagAlert
{
    public Guid UserId { get; set; }
    public string AlertType { get; set; } = string.Empty;
    public string FlagType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime DetectedAt { get; set; }
    public int Severity { get; set; }
    public int Count { get; set; }
    public DateTime FirstOccurrence { get; set; }
    public DateTime LastOccurrence { get; set; }
    public List<Guid> RelatedFeedbackIds { get; set; } = new();
}

public class FeedbackTrendQuery
{
    public Guid? UserId { get; set; }
    public int Days { get; set; } = 30;
}

public class FeedbackTrendAnalysis
{
    public List<TrendPoint> TrendPoints { get; set; } = new();
    public TrendDirection Direction { get; set; }
}

public class TrendPoint
{
    public DateTime Date { get; set; }
    public double AverageRating { get; set; }
    public int FeedbackCount { get; set; }
}

public enum TrendDirection
{
    Improving,
    Declining,
    Stable
}

public enum RedFlagSeverity
{
    Low = 1,
    Medium = 2,
    High = 3,
    Critical = 4
}

public class OrderFeedbackDto
{
    public Guid Id { get; set; }
    public Guid OrderId { get; set; }
    public Guid UserId { get; set; }
    public Guid? ReviewerId { get; set; }
    public string ReviewerRole { get; set; } = string.Empty;
    public Guid RevieweeId { get; set; }
    public string RevieweeRole { get; set; } = string.Empty;
    public int Rating { get; set; }
    public decimal OverallRating { get; set; }
    public decimal ServiceQualityRating { get; set; }
    public decimal CommunicationRating { get; set; }
    public decimal TimelinessRating { get; set; }
    public decimal ProfessionalismRating { get; set; }
    public string Comments { get; set; } = string.Empty;
    public bool IsAnonymous { get; set; }
    public string FeedbackType { get; set; } = string.Empty;
    public List<string> Categories { get; set; } = new();
    public List<string> Tags { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime SubmittedAt { get; set; }
}

public class UserFeedbackSummary
{
    public Guid UserId { get; set; }
    public double AverageRating { get; set; }
    public int TotalFeedbacks { get; set; }
    public DateTime LastFeedbackDate { get; set; }
    public Dictionary<int, int> RatingDistribution { get; set; } = new();
    public List<OrderFeedbackDto> RecentFeedbacks { get; set; } = new();
    public TrendDirection TrendDirection { get; set; }
    public DateTime? GeneratedAt { get; set; }
}

public class FeedbackInsightQuery
{
    public Guid? UserId { get; set; }
    public string? Category { get; set; }
}

public class FeedbackInsight
{
    public Guid Id { get; set; }
    public string Type { get; set; } = string.Empty; // "Strength", "Weakness", "Opportunity", "Threat"
    public string Category { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal Impact { get; set; } // 1-10 scale
    public string Recommendation { get; set; } = string.Empty;
    public List<string> SupportingData { get; set; } = new();
    public DateTime GeneratedAt { get; set; }
}




