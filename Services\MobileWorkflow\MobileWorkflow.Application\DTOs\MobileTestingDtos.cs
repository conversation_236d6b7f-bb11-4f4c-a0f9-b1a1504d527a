namespace MobileWorkflow.Application.DTOs;

public class TestSuiteDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string TestType { get; set; } = string.Empty;
    public string Platform { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public Dictionary<string, object> Configuration { get; set; } = new();
    public Dictionary<string, object> TestEnvironment { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public string? UpdatedBy { get; set; }
}

public class TestCaseDto
{
    public Guid Id { get; set; }
    public Guid TestSuiteId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string TestSteps { get; set; } = string.Empty;
    public string ExpectedResult { get; set; } = string.Empty;
    public int Priority { get; set; }
    public string Category { get; set; } = string.Empty;
    public bool IsAutomated { get; set; }
    public bool IsActive { get; set; }
    public Dictionary<string, object> TestData { get; set; } = new();
    public Dictionary<string, object> Preconditions { get; set; } = new();
    public Dictionary<string, object> Postconditions { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public string? UpdatedBy { get; set; }
}

public class TestExecutionDto
{
    public Guid Id { get; set; }
    public Guid TestSuiteId { get; set; }
    public string ExecutionName { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public TimeSpan? Duration { get; set; }
    public string ExecutedBy { get; set; } = string.Empty;
    public string? ExecutionEnvironment { get; set; }
    public Dictionary<string, object> ExecutionConfiguration { get; set; } = new();
    public Dictionary<string, object> ExecutionResults { get; set; } = new();
    public Dictionary<string, object> PerformanceMetrics { get; set; } = new();
    public string? ErrorMessage { get; set; }
    public bool IsCompleted { get; set; }
    public bool IsRunning { get; set; }
    public bool IsSuccessful { get; set; }
}

public class TestCaseExecutionDto
{
    public Guid Id { get; set; }
    public Guid TestExecutionId { get; set; }
    public Guid TestCaseId { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public TimeSpan? Duration { get; set; }
    public string? ActualResult { get; set; }
    public string? ErrorMessage { get; set; }
    public string? ErrorStackTrace { get; set; }
    public Dictionary<string, object> TestOutput { get; set; } = new();
    public Dictionary<string, object> Screenshots { get; set; } = new();
    public Dictionary<string, object> Logs { get; set; } = new();
    public Dictionary<string, object> PerformanceData { get; set; } = new();
    public bool IsCompleted { get; set; }
    public bool IsSuccessful { get; set; }
    public bool IsFailed { get; set; }
    public bool IsSkipped { get; set; }
}

public class DeviceSimulationDto
{
    public Guid Id { get; set; }
    public string DeviceName { get; set; } = string.Empty;
    public string Platform { get; set; } = string.Empty;
    public string OSVersion { get; set; } = string.Empty;
    public string DeviceModel { get; set; } = string.Empty;
    public string ScreenResolution { get; set; } = string.Empty;
    public Dictionary<string, object> DeviceCapabilities { get; set; } = new();
    public Dictionary<string, object> SimulationSettings { get; set; } = new();
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

public class TestAnalyticsDto
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public int TotalTestSuites { get; set; }
    public int ActiveTestSuites { get; set; }
    public int TotalTestCases { get; set; }
    public int AutomatedTestCases { get; set; }
    public int TotalExecutions { get; set; }
    public int SuccessfulExecutions { get; set; }
    public int FailedExecutions { get; set; }
    public double SuccessRate { get; set; }
    public double AverageExecutionTime { get; set; }
    public Dictionary<string, int> ExecutionsByPlatform { get; set; } = new();
    public Dictionary<string, int> ExecutionsByTestType { get; set; } = new();
    public Dictionary<DateTime, int> DailyExecutions { get; set; } = new();
    public Dictionary<string, double> PerformanceMetrics { get; set; } = new();
}

public class TestReportDto
{
    public Guid Id { get; set; }
    public string ReportName { get; set; } = string.Empty;
    public string ReportType { get; set; } = string.Empty;
    public DateTime GeneratedAt { get; set; }
    public string GeneratedBy { get; set; } = string.Empty;
    public Dictionary<string, object> ReportData { get; set; } = new();
    public string ReportFormat { get; set; } = string.Empty; // HTML, PDF, JSON, XML
    public byte[] ReportContent { get; set; } = Array.Empty<byte>();
    public string DownloadUrl { get; set; } = string.Empty;
}

// Request DTOs
public class CreateTestSuiteRequest
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string TestType { get; set; } = string.Empty;
    public string Platform { get; set; } = string.Empty;
    public Dictionary<string, object> Configuration { get; set; } = new();
    public Dictionary<string, object>? TestEnvironment { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

public class UpdateTestSuiteRequest
{
    public Dictionary<string, object>? Configuration { get; set; }
    public Dictionary<string, object>? TestEnvironment { get; set; }
    public bool? IsActive { get; set; }
    public string UpdatedBy { get; set; } = string.Empty;
}

public class CreateTestCaseRequest
{
    public Guid TestSuiteId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string TestSteps { get; set; } = string.Empty;
    public string ExpectedResult { get; set; } = string.Empty;
    public int Priority { get; set; } = 2; // Default to Medium
    public string Category { get; set; } = string.Empty;
    public bool IsAutomated { get; set; }
    public Dictionary<string, object>? TestData { get; set; }
    public Dictionary<string, object>? Preconditions { get; set; }
    public Dictionary<string, object>? Postconditions { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

public class UpdateTestCaseRequest
{
    public string? Name { get; set; }
    public string? Description { get; set; }
    public string? TestSteps { get; set; }
    public string? ExpectedResult { get; set; }
    public int? Priority { get; set; }
    public string? Category { get; set; }
    public bool? IsAutomated { get; set; }
    public Dictionary<string, object>? TestData { get; set; }
    public bool? IsActive { get; set; }
    public string UpdatedBy { get; set; } = string.Empty;
}

public class StartTestExecutionRequest
{
    public Guid TestSuiteId { get; set; }
    public string ExecutionName { get; set; } = string.Empty;
    public string ExecutedBy { get; set; } = string.Empty;
    public Dictionary<string, object> ExecutionConfiguration { get; set; } = new();
    public string? ExecutionEnvironment { get; set; }
}

public class ExecuteTestCaseRequest
{
    public Guid TestExecutionId { get; set; }
    public Guid TestCaseId { get; set; }
    public Dictionary<string, object> ExecutionContext { get; set; } = new();
}

public class CreateDeviceSimulationRequest
{
    public string DeviceName { get; set; } = string.Empty;
    public string Platform { get; set; } = string.Empty;
    public string OSVersion { get; set; } = string.Empty;
    public string DeviceModel { get; set; } = string.Empty;
    public string ScreenResolution { get; set; } = string.Empty;
    public Dictionary<string, object> DeviceCapabilities { get; set; } = new();
    public Dictionary<string, object>? SimulationSettings { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

public class GenerateTestReportRequest
{
    public string ReportName { get; set; } = string.Empty;
    public string ReportType { get; set; } = string.Empty; // Summary, Detailed, Performance
    public string ReportFormat { get; set; } = "HTML";
    public Guid? TestSuiteId { get; set; }
    public Guid? TestExecutionId { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public List<string>? IncludeSections { get; set; }
    public Dictionary<string, object>? ReportOptions { get; set; }
    public string GeneratedBy { get; set; } = string.Empty;
}

public class TestEnvironmentDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string EnvironmentType { get; set; } = string.Empty; // Development, Testing, Staging, Production
    public Dictionary<string, object> Configuration { get; set; } = new();
    public Dictionary<string, object> Capabilities { get; set; } = new();
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

public class TestDataSetDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string DataType { get; set; } = string.Empty; // JSON, CSV, XML, Database
    public Dictionary<string, object> DataContent { get; set; } = new();
    public List<string> Tags { get; set; } = new();
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

public class TestStepDto
{
    public int Order { get; set; }
    public string Action { get; set; } = string.Empty;
    public string Target { get; set; } = string.Empty;
    public string Value { get; set; } = string.Empty;
    public string ExpectedResult { get; set; } = string.Empty;
    public Dictionary<string, object> Parameters { get; set; } = new();
    public bool IsOptional { get; set; }
}

public class TestResultSummaryDto
{
    public Guid TestExecutionId { get; set; }
    public int TotalTestCases { get; set; }
    public int PassedTestCases { get; set; }
    public int FailedTestCases { get; set; }
    public int SkippedTestCases { get; set; }
    public double PassRate { get; set; }
    public TimeSpan TotalExecutionTime { get; set; }
    public DateTime ExecutionDate { get; set; }
    public string ExecutedBy { get; set; } = string.Empty;
    public Dictionary<string, int> ResultsByCategory { get; set; } = new();
}

public class TestCoverageDto
{
    public Guid TestSuiteId { get; set; }
    public double OverallCoverage { get; set; }
    public Dictionary<string, double> CoverageByFeature { get; set; } = new();
    public Dictionary<string, double> CoverageByPlatform { get; set; } = new();
    public List<string> UncoveredAreas { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
    public DateTime CalculatedAt { get; set; }
}

public class TestPerformanceDto
{
    public Guid TestCaseId { get; set; }
    public string TestCaseName { get; set; } = string.Empty;
    public double AverageExecutionTime { get; set; }
    public double MinExecutionTime { get; set; }
    public double MaxExecutionTime { get; set; }
    public int ExecutionCount { get; set; }
    public double SuccessRate { get; set; }
    public Dictionary<string, double> PerformanceMetrics { get; set; } = new();
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
}
