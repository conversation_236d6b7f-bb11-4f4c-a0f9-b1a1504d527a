namespace OrderManagement.Application.DTOs;

/// <summary>
/// DTO for Transport Company rating display
/// </summary>
public class TransportCompanyRatingDisplayDto
{
    public Guid TransportCompanyId { get; set; }
    public string CompanyName { get; set; } = string.Empty;
    public decimal OverallRating { get; set; }
    public int TotalReviews { get; set; }
    public int TotalTrips { get; set; }
    public DateTime LastUpdated { get; set; }
    
    // Category ratings
    public decimal ServiceQualityRating { get; set; }
    public decimal TimelinessRating { get; set; }
    public decimal CommunicationRating { get; set; }
    public decimal ProfessionalismRating { get; set; }
    public decimal VehicleConditionRating { get; set; }
    public decimal CargoHandlingRating { get; set; }
    
    // Performance metrics
    public decimal OnTimeDeliveryRate { get; set; }
    public decimal CustomerSatisfactionScore { get; set; }
    public string RatingTrend { get; set; } = string.Empty; // Improving, Declining, Stable
    
    // Visual indicators
    public List<PerformanceIndicator> PerformanceIndicators { get; set; } = new();
    public List<string> Badges { get; set; } = new();
    public Dictionary<int, int> RatingDistribution { get; set; } = new(); // Star rating -> count
    
    // Additional info
    public string? CompanyLogo { get; set; }
    public bool IsVerified { get; set; }
    public bool IsActive { get; set; } = true;
    public DateTime? LastTripDate { get; set; }
}

/// <summary>
/// Performance indicator for visual display
/// </summary>
public class PerformanceIndicator
{
    public string Name { get; set; } = string.Empty;
    public decimal Value { get; set; }
    public decimal MaxValue { get; set; }
    public string Status { get; set; } = string.Empty; // Excellent, Good, Fair, Poor
    public string Color { get; set; } = string.Empty; // green, yellow, red
    public string Icon { get; set; } = string.Empty;
    public string? Unit { get; set; }
    public string? Description { get; set; }
    public decimal Percentage => MaxValue > 0 ? (Value / MaxValue) * 100 : 0;
}

/// <summary>
/// DTO for rating trends over time
/// </summary>
public class TransportCompanyRatingTrendsDto
{
    public Guid TransportCompanyId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public DateTime GeneratedAt { get; set; }
    
    // Trend data
    public List<RatingTrendDataPoint> MonthlyTrends { get; set; } = new();
    public List<RatingTrendDataPoint> WeeklyTrends { get; set; } = new();
    
    // Trend analysis
    public decimal RatingImprovement { get; set; } // Positive = improving, negative = declining
    public bool IsImproving { get; set; }
    public string TrendDescription { get; set; } = string.Empty;
    
    // Peak performance
    public RatingTrendDataPoint? BestMonth { get; set; }
    public RatingTrendDataPoint? WorstMonth { get; set; }
    
    // Consistency metrics
    public decimal RatingConsistency { get; set; } // Lower variance = more consistent
    public decimal AverageRatingOverPeriod { get; set; }
}

/// <summary>
/// Data point for rating trends
/// </summary>
public class RatingTrendDataPoint
{
    public DateTime Date { get; set; }
    public decimal OverallRating { get; set; }
    public decimal ServiceQualityRating { get; set; }
    public decimal TimelinessRating { get; set; }
    public decimal CommunicationRating { get; set; }
    public decimal ProfessionalismRating { get; set; }
    public int TotalFeedbacks { get; set; }
    public int PositiveFeedbacks { get; set; }
    public int NegativeFeedbacks { get; set; }
    public decimal PositivePercentage => TotalFeedbacks > 0 ? (decimal)PositiveFeedbacks / TotalFeedbacks * 100 : 0;
}

/// <summary>
/// DTO for comparing multiple transport companies
/// </summary>
public class TransportCompanyRatingComparisonDto
{
    public DateTime ComparedAt { get; set; }
    public int TotalCompanies { get; set; }
    public List<TransportCompanyComparisonItem> Companies { get; set; } = new();
    
    // Comparison statistics
    public decimal HighestRating { get; set; }
    public decimal LowestRating { get; set; }
    public decimal AverageRating { get; set; }
    public decimal MedianRating { get; set; }
    
    // Ranking categories
    public List<RankingCategory> RankingCategories { get; set; } = new()
    {
        new RankingCategory { Name = "Overall Rating", Field = "OverallRating" },
        new RankingCategory { Name = "On-Time Delivery", Field = "OnTimeDeliveryRate" },
        new RankingCategory { Name = "Customer Satisfaction", Field = "CustomerSatisfactionScore" },
        new RankingCategory { Name = "Total Trips", Field = "TotalTrips" }
    };
}

/// <summary>
/// Individual company in comparison
/// </summary>
public class TransportCompanyComparisonItem
{
    public Guid TransportCompanyId { get; set; }
    public string CompanyName { get; set; } = string.Empty;
    public decimal OverallRating { get; set; }
    public int TotalTrips { get; set; }
    public decimal OnTimeDeliveryRate { get; set; }
    public decimal CustomerSatisfactionScore { get; set; }
    
    // Rankings
    public int RankByRating { get; set; }
    public int RankByTrips { get; set; }
    public int RankByOnTimeDelivery { get; set; }
    public int RankByCustomerSatisfaction { get; set; }
    
    // Visual indicators
    public List<PerformanceIndicator> PerformanceIndicators { get; set; } = new();
    public List<string> Badges { get; set; } = new();
    
    // Comparison metrics
    public decimal RatingDifferenceFromAverage { get; set; }
    public string PerformanceCategory { get; set; } = string.Empty; // Top Performer, Above Average, Below Average
}

/// <summary>
/// Ranking category for comparison
/// </summary>
public class RankingCategory
{
    public string Name { get; set; } = string.Empty;
    public string Field { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public bool IsDescending { get; set; } = true; // Higher values = better rank
}

/// <summary>
/// DTO for performance metrics
/// </summary>
public class TransportCompanyPerformanceMetricsDto
{
    public Guid TransportCompanyId { get; set; }
    public DateTime CalculatedAt { get; set; }
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
    
    // Feedback metrics
    public int TotalFeedbacks { get; set; }
    public decimal AverageOverallRating { get; set; }
    public decimal HighRatingPercentage { get; set; } // 4+ stars
    public decimal LowRatingPercentage { get; set; } // 2- stars
    
    // Category averages
    public decimal ServiceQualityAverage { get; set; }
    public decimal TimelinessAverage { get; set; }
    public decimal CommunicationAverage { get; set; }
    public decimal ProfessionalismAverage { get; set; }
    public decimal VehicleConditionAverage { get; set; }
    public decimal CargoHandlingAverage { get; set; }
    
    // Operational metrics
    public int TotalOrders { get; set; }
    public int CompletedOrders { get; set; }
    public int OnTimeDeliveries { get; set; }
    public decimal OnTimeDeliveryRate { get; set; }
    public double AverageDeliveryTime { get; set; } // in hours
    
    // Performance trends
    public List<PerformanceMetricTrend> Trends { get; set; } = new();
    
    // Benchmarking
    public PerformanceBenchmark? IndustryBenchmark { get; set; }
    public PerformanceBenchmark? RegionalBenchmark { get; set; }
}

/// <summary>
/// Performance metric trend
/// </summary>
public class PerformanceMetricTrend
{
    public string MetricName { get; set; } = string.Empty;
    public decimal CurrentValue { get; set; }
    public decimal PreviousValue { get; set; }
    public decimal Change { get; set; }
    public decimal ChangePercentage { get; set; }
    public string TrendDirection { get; set; } = string.Empty; // Up, Down, Stable
    public string TrendDescription { get; set; } = string.Empty;
}

/// <summary>
/// Performance benchmark
/// </summary>
public class PerformanceBenchmark
{
    public string BenchmarkType { get; set; } = string.Empty; // Industry, Regional, Size-based
    public decimal AverageRating { get; set; }
    public decimal AverageOnTimeRate { get; set; }
    public decimal AverageCustomerSatisfaction { get; set; }
    public string ComparisonResult { get; set; } = string.Empty; // Above Average, Below Average, Average
    public List<string> ImprovementAreas { get; set; } = new();
}

/// <summary>
/// Request for rating display
/// </summary>
public class GetRatingDisplayRequest
{
    public Guid TransportCompanyId { get; set; }
    public bool IncludeTrends { get; set; } = false;
    public bool IncludePerformanceMetrics { get; set; } = false;
    public bool IncludeBenchmarks { get; set; } = false;
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
}

/// <summary>
/// Request for rating comparison
/// </summary>
public class GetRatingComparisonRequest
{
    public List<Guid> TransportCompanyIds { get; set; } = new();
    public List<string> ComparisonMetrics { get; set; } = new();
    public string SortBy { get; set; } = "OverallRating";
    public bool SortDescending { get; set; } = true;
    public bool IncludeRankings { get; set; } = true;
    public bool IncludeBenchmarks { get; set; } = false;
}

/// <summary>
/// Rating summary for dashboard
/// </summary>
public class RatingSummaryDto
{
    public Guid TransportCompanyId { get; set; }
    public decimal OverallRating { get; set; }
    public int TotalReviews { get; set; }
    public string RatingTrend { get; set; } = string.Empty;
    public List<string> TopBadges { get; set; } = new();
    public DateTime LastUpdated { get; set; }
    
    // Quick metrics
    public decimal OnTimeRate { get; set; }
    public decimal SatisfactionScore { get; set; }
    public int RecentTrips { get; set; }
    
    // Visual elements
    public string RatingColor { get; set; } = string.Empty;
    public string TrendIcon { get; set; } = string.Empty;
    public bool ShowAlert { get; set; }
    public string? AlertMessage { get; set; }
}
