using MediatR;
using AutoMapper;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;
using OrderManagement.Application.DTOs;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Application.Queries.GetMyRfqs;

public class GetMyRfqsQueryHandler : IRequestHandler<GetMyRfqsQuery, PagedResult<RfqSummaryDto>>
{
    private readonly IRfqRepository _rfqRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetMyRfqsQueryHandler> _logger;

    public GetMyRfqsQueryHandler(
        IRfqRepository rfqRepository,
        IMapper mapper,
        ILogger<GetMyRfqsQueryHandler> logger)
    {
        _rfqRepository = rfqRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<PagedResult<RfqSummaryDto>> Handle(GetMyRfqsQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting RFQs for transport company {TransportCompanyId}, Page: {Page}, PageSize: {PageSize}",
            request.TransportCompanyId, request.Page, request.PageSize);

        // Parse status filter if provided
        RfqStatus? statusFilter = null;
        if (!string.IsNullOrEmpty(request.Status) && Enum.TryParse<RfqStatus>(request.Status, true, out var parsedStatus))
        {
            statusFilter = parsedStatus;
        }

        // Get paginated RFQs
        var rfqs = await _rfqRepository.GetByTransportCompanyAsync(
            request.TransportCompanyId,
            request.Page,
            request.PageSize,
            statusFilter,
            request.FromDate,
            request.ToDate,
            cancellationToken);

        // Get total count for pagination
        var totalCount = await _rfqRepository.GetCountByTransportCompanyAsync(
            request.TransportCompanyId,
            statusFilter,
            request.FromDate,
            request.ToDate,
            cancellationToken);

        var rfqDtos = _mapper.Map<List<RfqSummaryDto>>(rfqs);

        _logger.LogInformation("Retrieved {Count} RFQs out of {TotalCount} for transport company {TransportCompanyId}",
            rfqs.Count, totalCount, request.TransportCompanyId);

        return new PagedResult<RfqSummaryDto>
        {
            Items = rfqDtos,
            TotalCount = totalCount,
            Page = request.Page,
            PageSize = request.PageSize
        };
    }
}
