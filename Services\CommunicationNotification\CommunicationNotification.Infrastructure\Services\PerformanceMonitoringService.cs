using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Diagnostics.Metrics;

namespace CommunicationNotification.Infrastructure.Services;

/// <summary>
/// Performance monitoring service with metrics collection and analysis
/// </summary>
public interface IPerformanceMonitoringService
{
    Task<IDisposable> StartOperationAsync(string operationName, Dictionary<string, object>? tags = null);
    Task RecordMetricAsync(string metricName, double value, Dictionary<string, object>? tags = null);
    Task RecordCounterAsync(string counterName, long value = 1, Dictionary<string, object>? tags = null);
    Task<PerformanceReport> GetPerformanceReportAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<List<PerformanceAlert>> GetActiveAlertsAsync();
    Task<Dictionary<string, object>> GetRealTimeMetricsAsync();
    void RecordCacheHit(string cacheType, string operation);
    void RecordCacheMiss(string cacheType, string operation);
    void RecordDatabaseQuery(string queryType, TimeSpan duration, bool isSuccess);
    void RecordApiCall(string endpoint, string method, int statusCode, TimeSpan duration);
    void RecordMessageProcessing(string messageType, string channel, TimeSpan duration, bool isSuccess);
}

public class PerformanceMonitoringService : IPerformanceMonitoringService
{
    private readonly ILogger<PerformanceMonitoringService> _logger;
    private readonly PerformanceConfiguration _configuration;
    private readonly Meter _meter;
    private readonly ConcurrentDictionary<string, PerformanceMetric> _metrics;
    private readonly ConcurrentQueue<PerformanceEvent> _events;
    private readonly Timer _alertCheckTimer;

    // Metrics instruments
    private readonly Counter<long> _operationCounter;
    private readonly Histogram<double> _operationDuration;
    private readonly Counter<long> _cacheHitCounter;
    private readonly Counter<long> _cacheMissCounter;
    private readonly Histogram<double> _databaseQueryDuration;
    private readonly Counter<long> _databaseQueryCounter;
    private readonly Histogram<double> _apiCallDuration;
    private readonly Counter<long> _apiCallCounter;
    private readonly Histogram<double> _messageProcessingDuration;
    private readonly Counter<long> _messageProcessingCounter;

    public PerformanceMonitoringService(
        ILogger<PerformanceMonitoringService> logger,
        IOptions<PerformanceConfiguration> configuration)
    {
        _logger = logger;
        _configuration = configuration.Value;
        _meter = new Meter("TLI.Communication.Performance", "1.0.0");
        _metrics = new ConcurrentDictionary<string, PerformanceMetric>();
        _events = new ConcurrentQueue<PerformanceEvent>();

        // Initialize metrics instruments
        _operationCounter = _meter.CreateCounter<long>("operation_total", "count", "Total number of operations");
        _operationDuration = _meter.CreateHistogram<double>("operation_duration", "ms", "Operation duration in milliseconds");
        _cacheHitCounter = _meter.CreateCounter<long>("cache_hits_total", "count", "Total cache hits");
        _cacheMissCounter = _meter.CreateCounter<long>("cache_misses_total", "count", "Total cache misses");
        _databaseQueryDuration = _meter.CreateHistogram<double>("database_query_duration", "ms", "Database query duration");
        _databaseQueryCounter = _meter.CreateCounter<long>("database_queries_total", "count", "Total database queries");
        _apiCallDuration = _meter.CreateHistogram<double>("api_call_duration", "ms", "API call duration");
        _apiCallCounter = _meter.CreateCounter<long>("api_calls_total", "count", "Total API calls");
        _messageProcessingDuration = _meter.CreateHistogram<double>("message_processing_duration", "ms", "Message processing duration");
        _messageProcessingCounter = _meter.CreateCounter<long>("messages_processed_total", "count", "Total messages processed");

        // Start alert checking timer
        if (_configuration.EnableAlerting)
        {
            _alertCheckTimer = new Timer(CheckAlerts, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
        }
    }

    public async Task<IDisposable> StartOperationAsync(string operationName, Dictionary<string, object>? tags = null)
    {
        var operation = new PerformanceOperation(operationName, tags, this);
        await Task.CompletedTask;
        return operation;
    }

    public async Task RecordMetricAsync(string metricName, double value, Dictionary<string, object>? tags = null)
    {
        try
        {
            var metric = _metrics.GetOrAdd(metricName, _ => new PerformanceMetric(metricName));
            metric.RecordValue(value, tags);

            var performanceEvent = new PerformanceEvent
            {
                Type = "Metric",
                Name = metricName,
                Value = value,
                Tags = tags ?? new Dictionary<string, object>(),
                Timestamp = DateTime.UtcNow
            };

            _events.Enqueue(performanceEvent);

            // Trim events queue if it gets too large
            if (_events.Count > _configuration.MaxEventHistory)
            {
                for (int i = 0; i < _configuration.MaxEventHistory / 4; i++)
                {
                    _events.TryDequeue(out _);
                }
            }

            _logger.LogDebug("Recorded metric {MetricName}: {Value}", metricName, value);
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording metric {MetricName}", metricName);
        }
    }

    public async Task RecordCounterAsync(string counterName, long value = 1, Dictionary<string, object>? tags = null)
    {
        try
        {
            _operationCounter.Add(value, CreateTagArray(tags));
            await RecordMetricAsync(counterName, value, tags);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording counter {CounterName}", counterName);
        }
    }

    public async Task<PerformanceReport> GetPerformanceReportAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            var start = startDate ?? DateTime.UtcNow.AddHours(-24);
            var end = endDate ?? DateTime.UtcNow;

            var relevantEvents = _events
                .Where(e => e.Timestamp >= start && e.Timestamp <= end)
                .ToList();

            var report = new PerformanceReport
            {
                StartDate = start,
                EndDate = end,
                TotalEvents = relevantEvents.Count,
                Metrics = _metrics.Values.ToList(),
                TopOperations = GetTopOperations(relevantEvents),
                PerformanceTrends = CalculatePerformanceTrends(relevantEvents),
                CacheStatistics = CalculateCacheStatistics(relevantEvents),
                DatabaseStatistics = CalculateDatabaseStatistics(relevantEvents),
                ApiStatistics = CalculateApiStatistics(relevantEvents),
                Recommendations = GenerateRecommendations(relevantEvents)
            };

            return report;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating performance report");
            return new PerformanceReport();
        }
    }

    public async Task<List<PerformanceAlert>> GetActiveAlertsAsync()
    {
        try
        {
            var alerts = new List<PerformanceAlert>();
            var now = DateTime.UtcNow;
            var lookbackPeriod = TimeSpan.FromMinutes(_configuration.AlertLookbackMinutes);

            var recentEvents = _events
                .Where(e => e.Timestamp >= now.Subtract(lookbackPeriod))
                .ToList();

            // Check for high error rates
            var errorRate = CalculateErrorRate(recentEvents);
            if (errorRate > _configuration.ErrorRateThreshold)
            {
                alerts.Add(new PerformanceAlert
                {
                    Type = "HighErrorRate",
                    Severity = AlertSeverity.High,
                    Message = $"Error rate is {errorRate:P2}, exceeding threshold of {_configuration.ErrorRateThreshold:P2}",
                    Timestamp = now,
                    Value = errorRate
                });
            }

            // Check for slow operations
            var slowOperations = GetSlowOperations(recentEvents);
            foreach (var operation in slowOperations)
            {
                alerts.Add(new PerformanceAlert
                {
                    Type = "SlowOperation",
                    Severity = AlertSeverity.Medium,
                    Message = $"Operation '{operation.Name}' is running slowly (avg: {operation.AverageDuration:F2}ms)",
                    Timestamp = now,
                    Value = operation.AverageDuration,
                    Tags = new Dictionary<string, object> { ["operation"] = operation.Name }
                });
            }

            // Check cache hit rate
            var cacheHitRate = CalculateCacheHitRate(recentEvents);
            if (cacheHitRate < _configuration.CacheHitRateThreshold)
            {
                alerts.Add(new PerformanceAlert
                {
                    Type = "LowCacheHitRate",
                    Severity = AlertSeverity.Medium,
                    Message = $"Cache hit rate is {cacheHitRate:P2}, below threshold of {_configuration.CacheHitRateThreshold:P2}",
                    Timestamp = now,
                    Value = cacheHitRate
                });
            }

            return alerts;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active alerts");
            return new List<PerformanceAlert>();
        }
    }

    public async Task<Dictionary<string, object>> GetRealTimeMetricsAsync()
    {
        try
        {
            var now = DateTime.UtcNow;
            var last5Minutes = now.AddMinutes(-5);

            var recentEvents = _events
                .Where(e => e.Timestamp >= last5Minutes)
                .ToList();

            var metrics = new Dictionary<string, object>
            {
                ["timestamp"] = now,
                ["totalEvents"] = recentEvents.Count,
                ["eventsPerMinute"] = recentEvents.Count / 5.0,
                ["errorRate"] = CalculateErrorRate(recentEvents),
                ["cacheHitRate"] = CalculateCacheHitRate(recentEvents),
                ["averageResponseTime"] = CalculateAverageResponseTime(recentEvents),
                ["activeOperations"] = GetActiveOperations(),
                ["memoryUsage"] = GC.GetTotalMemory(false),
                ["threadCount"] = Process.GetCurrentProcess().Threads.Count
            };

            return metrics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting real-time metrics");
            return new Dictionary<string, object>();
        }
    }

    public void RecordCacheHit(string cacheType, string operation)
    {
        var tags = new Dictionary<string, object>
        {
            ["cache_type"] = cacheType,
            ["operation"] = operation
        };

        _cacheHitCounter.Add(1, CreateTagArray(tags));
        
        var _ = RecordMetricAsync("cache_hit", 1, tags);
    }

    public void RecordCacheMiss(string cacheType, string operation)
    {
        var tags = new Dictionary<string, object>
        {
            ["cache_type"] = cacheType,
            ["operation"] = operation
        };

        _cacheMissCounter.Add(1, CreateTagArray(tags));
        
        var _ = RecordMetricAsync("cache_miss", 1, tags);
    }

    public void RecordDatabaseQuery(string queryType, TimeSpan duration, bool isSuccess)
    {
        var tags = new Dictionary<string, object>
        {
            ["query_type"] = queryType,
            ["success"] = isSuccess
        };

        _databaseQueryDuration.Record(duration.TotalMilliseconds, CreateTagArray(tags));
        _databaseQueryCounter.Add(1, CreateTagArray(tags));
        
        var _ = RecordMetricAsync("database_query", duration.TotalMilliseconds, tags);
    }

    public void RecordApiCall(string endpoint, string method, int statusCode, TimeSpan duration)
    {
        var tags = new Dictionary<string, object>
        {
            ["endpoint"] = endpoint,
            ["method"] = method,
            ["status_code"] = statusCode,
            ["success"] = statusCode < 400
        };

        _apiCallDuration.Record(duration.TotalMilliseconds, CreateTagArray(tags));
        _apiCallCounter.Add(1, CreateTagArray(tags));
        
        var _ = RecordMetricAsync("api_call", duration.TotalMilliseconds, tags);
    }

    public void RecordMessageProcessing(string messageType, string channel, TimeSpan duration, bool isSuccess)
    {
        var tags = new Dictionary<string, object>
        {
            ["message_type"] = messageType,
            ["channel"] = channel,
            ["success"] = isSuccess
        };

        _messageProcessingDuration.Record(duration.TotalMilliseconds, CreateTagArray(tags));
        _messageProcessingCounter.Add(1, CreateTagArray(tags));
        
        var _ = RecordMetricAsync("message_processing", duration.TotalMilliseconds, tags);
    }

    internal void RecordOperationCompletion(string operationName, TimeSpan duration, Dictionary<string, object>? tags, bool isSuccess)
    {
        var allTags = new Dictionary<string, object>(tags ?? new Dictionary<string, object>())
        {
            ["success"] = isSuccess
        };

        _operationDuration.Record(duration.TotalMilliseconds, CreateTagArray(allTags));
        _operationCounter.Add(1, CreateTagArray(allTags));
        
        var _ = RecordMetricAsync($"operation_{operationName}", duration.TotalMilliseconds, allTags);
    }

    private KeyValuePair<string, object?>[] CreateTagArray(Dictionary<string, object>? tags)
    {
        if (tags == null || !tags.Any())
            return Array.Empty<KeyValuePair<string, object?>>();

        return tags.Select(kvp => new KeyValuePair<string, object?>(kvp.Key, kvp.Value)).ToArray();
    }

    private void CheckAlerts(object? state)
    {
        try
        {
            var _ = Task.Run(async () =>
            {
                var alerts = await GetActiveAlertsAsync();
                foreach (var alert in alerts.Where(a => a.Severity >= AlertSeverity.High))
                {
                    _logger.LogWarning("Performance Alert: {Message}", alert.Message);
                    // Here you could send notifications, trigger webhooks, etc.
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking alerts");
        }
    }

    private double CalculateErrorRate(List<PerformanceEvent> events)
    {
        if (!events.Any()) return 0;

        var errorEvents = events.Count(e => 
            e.Tags.ContainsKey("success") && 
            e.Tags["success"] is bool success && 
            !success);

        return (double)errorEvents / events.Count;
    }

    private double CalculateCacheHitRate(List<PerformanceEvent> events)
    {
        var cacheEvents = events.Where(e => e.Name == "cache_hit" || e.Name == "cache_miss").ToList();
        if (!cacheEvents.Any()) return 1.0;

        var hits = cacheEvents.Count(e => e.Name == "cache_hit");
        return (double)hits / cacheEvents.Count;
    }

    private double CalculateAverageResponseTime(List<PerformanceEvent> events)
    {
        var responseTimeEvents = events.Where(e => e.Name.Contains("duration") || e.Name.Contains("response")).ToList();
        if (!responseTimeEvents.Any()) return 0;

        return responseTimeEvents.Average(e => e.Value);
    }

    private List<string> GetActiveOperations()
    {
        // This would track currently running operations
        // For simplicity, returning recent operation types
        var recentEvents = _events
            .Where(e => e.Timestamp >= DateTime.UtcNow.AddMinutes(-1))
            .Where(e => e.Tags.ContainsKey("operation"))
            .Select(e => e.Tags["operation"].ToString())
            .Distinct()
            .ToList();

        return recentEvents!;
    }

    private List<TopOperation> GetTopOperations(List<PerformanceEvent> events)
    {
        return events
            .Where(e => e.Tags.ContainsKey("operation"))
            .GroupBy(e => e.Tags["operation"].ToString())
            .Select(g => new TopOperation
            {
                Name = g.Key!,
                Count = g.Count(),
                AverageDuration = g.Average(e => e.Value),
                TotalDuration = g.Sum(e => e.Value)
            })
            .OrderByDescending(o => o.Count)
            .Take(10)
            .ToList();
    }

    private Dictionary<string, double> CalculatePerformanceTrends(List<PerformanceEvent> events)
    {
        // Calculate trends over time periods
        var trends = new Dictionary<string, double>();
        
        var hourlyGroups = events
            .GroupBy(e => e.Timestamp.Hour)
            .ToDictionary(g => g.Key, g => g.Average(e => e.Value));

        if (hourlyGroups.Count > 1)
        {
            var values = hourlyGroups.Values.ToList();
            var trend = CalculateLinearTrend(values);
            trends["hourly_trend"] = trend;
        }

        return trends;
    }

    private double CalculateLinearTrend(List<double> values)
    {
        if (values.Count < 2) return 0;

        var n = values.Count;
        var sumX = Enumerable.Range(0, n).Sum();
        var sumY = values.Sum();
        var sumXY = values.Select((y, x) => x * y).Sum();
        var sumX2 = Enumerable.Range(0, n).Sum(x => x * x);

        var slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
        return slope;
    }

    private CacheStatistics CalculateCacheStatistics(List<PerformanceEvent> events)
    {
        var cacheEvents = events.Where(e => e.Name.StartsWith("cache_")).ToList();
        
        return new CacheStatistics
        {
            TotalRequests = cacheEvents.Count,
            HitRate = CalculateCacheHitRate(cacheEvents),
            MissRate = 1 - CalculateCacheHitRate(cacheEvents),
            AverageResponseTime = cacheEvents.Any() ? cacheEvents.Average(e => e.Value) : 0
        };
    }

    private DatabaseStatistics CalculateDatabaseStatistics(List<PerformanceEvent> events)
    {
        var dbEvents = events.Where(e => e.Name == "database_query").ToList();
        
        return new DatabaseStatistics
        {
            TotalQueries = dbEvents.Count,
            AverageQueryTime = dbEvents.Any() ? dbEvents.Average(e => e.Value) : 0,
            SlowQueries = dbEvents.Count(e => e.Value > _configuration.SlowQueryThresholdMs),
            ErrorRate = CalculateErrorRate(dbEvents)
        };
    }

    private ApiStatistics CalculateApiStatistics(List<PerformanceEvent> events)
    {
        var apiEvents = events.Where(e => e.Name == "api_call").ToList();
        
        return new ApiStatistics
        {
            TotalCalls = apiEvents.Count,
            AverageResponseTime = apiEvents.Any() ? apiEvents.Average(e => e.Value) : 0,
            ErrorRate = CalculateErrorRate(apiEvents),
            ThroughputPerMinute = apiEvents.Count / Math.Max(1, (DateTime.UtcNow - events.Min(e => e.Timestamp)).TotalMinutes)
        };
    }

    private List<SlowOperation> GetSlowOperations(List<PerformanceEvent> events)
    {
        return events
            .Where(e => e.Value > _configuration.SlowOperationThresholdMs)
            .Where(e => e.Tags.ContainsKey("operation"))
            .GroupBy(e => e.Tags["operation"].ToString())
            .Select(g => new SlowOperation
            {
                Name = g.Key!,
                Count = g.Count(),
                AverageDuration = g.Average(e => e.Value)
            })
            .ToList();
    }

    private List<string> GenerateRecommendations(List<PerformanceEvent> events)
    {
        var recommendations = new List<string>();

        var cacheHitRate = CalculateCacheHitRate(events);
        if (cacheHitRate < 0.8)
        {
            recommendations.Add("Consider increasing cache TTL or reviewing cache key strategies to improve hit rate");
        }

        var errorRate = CalculateErrorRate(events);
        if (errorRate > 0.05)
        {
            recommendations.Add("High error rate detected. Review error logs and implement better error handling");
        }

        var slowQueries = events.Count(e => e.Name == "database_query" && e.Value > _configuration.SlowQueryThresholdMs);
        if (slowQueries > events.Count * 0.1)
        {
            recommendations.Add("Consider optimizing database queries or adding indexes for better performance");
        }

        return recommendations;
    }

    public void Dispose()
    {
        _alertCheckTimer?.Dispose();
        _meter?.Dispose();
    }
}
