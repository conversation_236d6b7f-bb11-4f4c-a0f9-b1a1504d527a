using UserManagement.Application.Interfaces;
using UserManagement.Application.Services;

namespace UserManagement.Infrastructure.Services
{
    public class StubNotificationServiceAdapter : UserManagement.Application.Interfaces.INotificationService
    {
        private readonly UserManagement.Application.Services.INotificationService _notificationService;

        public StubNotificationServiceAdapter(UserManagement.Application.Services.INotificationService notificationService)
        {
            _notificationService = notificationService;
        }

        public Task SendNotificationAsync(Guid userId, string title, string message)
        {
            return _notificationService.SendSystemNotificationAsync(title, message, "info", null, userId);
        }

        public Task SendEmailAsync(string email, string subject, string body)
        {
            // Stub implementation - log the email
            Console.WriteLine($"Email to {email}: {subject} - {body}");
            return Task.CompletedTask;
        }

        public Task SendSmsAsync(string phoneNumber, string message)
        {
            // Stub implementation - log the SMS
            Console.WriteLine($"SMS to {phoneNumber}: {message}");
            return Task.CompletedTask;
        }

        public Task SendKycReminderAsync(Guid userId, string userType, string displayName, string email)
        {
            // Stub implementation - log the KYC reminder
            Console.WriteLine($"KYC Reminder to {email} ({displayName}): Please complete your KYC verification");
            return Task.CompletedTask;
        }

        public Task SendKycUploadPromptAsync(Guid userId, string userType, string displayName, string email)
        {
            // Stub implementation - log the KYC upload prompt
            Console.WriteLine($"KYC Upload Prompt to {email} ({displayName}): Please upload your documents");
            return Task.CompletedTask;
        }

        public Task SendLanguageChangeNotificationAsync(Guid userId, string email, string userName, string newLanguage, string oldLanguage)
        {
            // Stub implementation - log the language change notification
            Console.WriteLine($"Language Change Notification to {email} ({userName}): Changed from {oldLanguage} to {newLanguage}");
            return Task.CompletedTask;
        }

        public Task SendAccountActionNotificationAsync(Guid userId, string userType, string displayName, string email, string notificationType, string actionResult, string reason, DateTime actionDate)
        {
            // Stub implementation - log the account action notification
            Console.WriteLine($"Account Action Notification to {email} ({displayName}): {notificationType} - {actionResult} on {actionDate}");
            return Task.CompletedTask;
        }

        public Task SendKycStatusNotificationAsync(Guid userId, string userType, string displayName, string email, string notificationType, string decisionMessage, DateTime processedDate)
        {
            // Stub implementation - log the KYC status notification
            Console.WriteLine($"KYC Status Notification to {email} ({displayName}): {notificationType} - {decisionMessage} on {processedDate}");
            return Task.CompletedTask;
        }
    }
}
