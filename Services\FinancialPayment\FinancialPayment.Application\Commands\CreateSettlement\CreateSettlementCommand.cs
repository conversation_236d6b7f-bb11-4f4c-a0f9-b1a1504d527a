using MediatR;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Application.Commands.CreateSettlement;

public class CreateSettlementCommand : IRequest<Guid>
{
    public Guid OrderId { get; set; }
    public Guid TripId { get; set; }
    public Guid EscrowAccountId { get; set; }
    public CreateMoneyDto TotalAmount { get; set; } = new();
    public List<SettlementDistributionDto> Distributions { get; set; } = new();
    public string? Notes { get; set; }
}

public class SettlementDistributionDto
{
    public Guid ParticipantId { get; set; }
    public string ParticipantRole { get; set; } = string.Empty;
    public CreateMoneyDto Amount { get; set; } = new();
    public string? Description { get; set; }
    public string? PaymentMethodId { get; set; }
}

public class CreateMoneyDto
{
    public decimal Amount { get; set; }
    public string Currency { get; set; } = "INR";

    public Money ToMoney() => new(Amount, Currency);
}
