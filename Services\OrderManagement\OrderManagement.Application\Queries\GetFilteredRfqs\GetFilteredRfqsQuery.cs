using Shared.Domain.Common;
using OrderManagement.Application.DTOs;
using OrderManagement.Domain.Enums;
using MediatR;

namespace OrderManagement.Application.Queries.GetFilteredRfqs;

public class GetFilteredRfqsQuery : IRequest<PagedResult<RfqSummaryDto>>
{
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 20;
    public Guid RequestingUserId { get; set; }

    // Date filters
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }

    // Type filters
    public List<string>? VehicleTypes { get; set; }
    public List<string>? LoadTypes { get; set; }
    public List<string>? Statuses { get; set; }

    // Entity filters
    public Guid? TransportCompanyId { get; set; }
    public Guid? BrokerId { get; set; }
    public Guid? CarrierId { get; set; }

    // Location filters
    public string? PickupCity { get; set; }
    public string? DeliveryCity { get; set; }
    public string? PickupState { get; set; }
    public string? DeliveryState { get; set; }

    // Budget filters
    public decimal? MinBudget { get; set; }
    public decimal? MaxBudget { get; set; }

    // Boolean filters
    public bool? IsUrgent { get; set; }
    public bool? HasDocuments { get; set; }
    public bool? HasBids { get; set; }
    public bool ExcludeExpired { get; set; } = true;
}

