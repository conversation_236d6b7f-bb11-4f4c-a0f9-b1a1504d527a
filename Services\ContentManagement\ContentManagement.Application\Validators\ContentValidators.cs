using ContentManagement.Application.Commands.Content;
using ContentManagement.Application.Commands.Faq;
using ContentManagement.Application.Commands.Policy;
using ContentManagement.Domain.Enums;
using FluentValidation;

namespace ContentManagement.Application.Validators;

/// <summary>
/// Validator for CreateContentCommand
/// </summary>
public class CreateContentCommandValidator : AbstractValidator<CreateContentCommand>
{
    public CreateContentCommandValidator()
    {
        RuleFor(x => x.Title)
            .NotEmpty().WithMessage("Title is required")
            .MaximumLength(500).WithMessage("Title cannot exceed 500 characters");

        RuleFor(x => x.Slug)
            .NotEmpty().WithMessage("Slug is required")
            .MaximumLength(200).WithMessage("Slug cannot exceed 200 characters")
            .Matches(@"^[a-z0-9-]+$").WithMessage("Slug can only contain lowercase letters, numbers, and hyphens");

        RuleFor(x => x.ContentBody)
            .NotEmpty().WithMessage("Content body is required");

        RuleFor(x => x.Type)
            .IsInEnum().WithMessage("Invalid content type");

        RuleFor(x => x.CreatedBy)
            .NotEmpty().WithMessage("Created by is required");

        RuleFor(x => x.CreatedByName)
            .NotEmpty().WithMessage("Created by name is required")
            .MaximumLength(200).WithMessage("Created by name cannot exceed 200 characters");

        RuleFor(x => x.MetaTitle)
            .MaximumLength(200).WithMessage("Meta title cannot exceed 200 characters")
            .When(x => !string.IsNullOrEmpty(x.MetaTitle));

        RuleFor(x => x.MetaDescription)
            .MaximumLength(500).WithMessage("Meta description cannot exceed 500 characters")
            .When(x => !string.IsNullOrEmpty(x.MetaDescription));

        RuleFor(x => x.SortOrder)
            .GreaterThanOrEqualTo(0).WithMessage("Sort order must be non-negative");

        RuleFor(x => x.Tags)
            .Must(tags => tags == null || tags.Count <= 20)
            .WithMessage("Cannot have more than 20 tags")
            .Must(tags => tags == null || tags.All(tag => !string.IsNullOrWhiteSpace(tag) && tag.Length <= 50))
            .WithMessage("Each tag must be non-empty and not exceed 50 characters");
    }
}

/// <summary>
/// Validator for UpdateContentCommand
/// </summary>
public class UpdateContentCommandValidator : AbstractValidator<UpdateContentCommand>
{
    public UpdateContentCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty().WithMessage("Content ID is required");

        RuleFor(x => x.Title)
            .NotEmpty().WithMessage("Title is required")
            .MaximumLength(500).WithMessage("Title cannot exceed 500 characters");

        RuleFor(x => x.ContentBody)
            .NotEmpty().WithMessage("Content body is required");

        RuleFor(x => x.UpdatedBy)
            .NotEmpty().WithMessage("Updated by is required");

        RuleFor(x => x.UpdatedByName)
            .NotEmpty().WithMessage("Updated by name is required")
            .MaximumLength(200).WithMessage("Updated by name cannot exceed 200 characters");

        RuleFor(x => x.MetaTitle)
            .MaximumLength(200).WithMessage("Meta title cannot exceed 200 characters")
            .When(x => !string.IsNullOrEmpty(x.MetaTitle));

        RuleFor(x => x.MetaDescription)
            .MaximumLength(500).WithMessage("Meta description cannot exceed 500 characters")
            .When(x => !string.IsNullOrEmpty(x.MetaDescription));

        RuleFor(x => x.ChangeDescription)
            .MaximumLength(1000).WithMessage("Change description cannot exceed 1000 characters")
            .When(x => !string.IsNullOrEmpty(x.ChangeDescription));

        RuleFor(x => x.Tags)
            .Must(tags => tags == null || tags.Count <= 20)
            .WithMessage("Cannot have more than 20 tags")
            .Must(tags => tags == null || tags.All(tag => !string.IsNullOrWhiteSpace(tag) && tag.Length <= 50))
            .WithMessage("Each tag must be non-empty and not exceed 50 characters");
    }
}

/// <summary>
/// Validator for CreateFaqItemCommand
/// </summary>
public class CreateFaqItemCommandValidator : AbstractValidator<CreateFaqItemCommand>
{
    public CreateFaqItemCommandValidator()
    {
        RuleFor(x => x.Question)
            .NotEmpty().WithMessage("Question is required")
            .MaximumLength(1000).WithMessage("Question cannot exceed 1000 characters");

        RuleFor(x => x.Answer)
            .NotEmpty().WithMessage("Answer is required");

        RuleFor(x => x.Category)
            .IsInEnum().WithMessage("Invalid FAQ category");

        RuleFor(x => x.CreatedBy)
            .NotEmpty().WithMessage("Created by is required");

        RuleFor(x => x.CreatedByName)
            .NotEmpty().WithMessage("Created by name is required")
            .MaximumLength(200).WithMessage("Created by name cannot exceed 200 characters");

        RuleFor(x => x.SortOrder)
            .GreaterThanOrEqualTo(0).WithMessage("Sort order must be non-negative");

        RuleFor(x => x.RoleVisibility)
            .Must(roles => roles == null || roles.All(role => !string.IsNullOrWhiteSpace(role)))
            .WithMessage("Role visibility cannot contain empty roles");

        RuleFor(x => x.Tags)
            .Must(tags => tags == null || tags.Count <= 20)
            .WithMessage("Cannot have more than 20 tags")
            .Must(tags => tags == null || tags.All(tag => !string.IsNullOrWhiteSpace(tag) && tag.Length <= 50))
            .WithMessage("Each tag must be non-empty and not exceed 50 characters");
    }
}

/// <summary>
/// Validator for UpdateFaqItemCommand
/// </summary>
public class UpdateFaqItemCommandValidator : AbstractValidator<UpdateFaqItemCommand>
{
    public UpdateFaqItemCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty().WithMessage("FAQ ID is required");

        RuleFor(x => x.Question)
            .NotEmpty().WithMessage("Question is required")
            .MaximumLength(1000).WithMessage("Question cannot exceed 1000 characters");

        RuleFor(x => x.Answer)
            .NotEmpty().WithMessage("Answer is required");

        RuleFor(x => x.UpdatedBy)
            .NotEmpty().WithMessage("Updated by is required");

        RuleFor(x => x.UpdatedByName)
            .NotEmpty().WithMessage("Updated by name is required")
            .MaximumLength(200).WithMessage("Updated by name cannot exceed 200 characters");

        RuleFor(x => x.ChangeDescription)
            .MaximumLength(1000).WithMessage("Change description cannot exceed 1000 characters")
            .When(x => !string.IsNullOrEmpty(x.ChangeDescription));
    }
}

/// <summary>
/// Validator for RateFaqHelpfulnessCommand
/// </summary>
public class RateFaqHelpfulnessCommandValidator : AbstractValidator<RateFaqHelpfulnessCommand>
{
    public RateFaqHelpfulnessCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty().WithMessage("FAQ ID is required");

        RuleFor(x => x.Rating)
            .InclusiveBetween(1, 5).WithMessage("Rating must be between 1 and 5");

        RuleFor(x => x.RatedBy)
            .NotEmpty().WithMessage("Rated by is required");

        RuleFor(x => x.RatedByName)
            .NotEmpty().WithMessage("Rated by name is required")
            .MaximumLength(200).WithMessage("Rated by name cannot exceed 200 characters");
    }
}

/// <summary>
/// Validator for CreatePolicyDocumentCommand
/// </summary>
public class CreatePolicyDocumentCommandValidator : AbstractValidator<CreatePolicyDocumentCommand>
{
    public CreatePolicyDocumentCommandValidator()
    {
        RuleFor(x => x.PolicyType)
            .IsInEnum().WithMessage("Invalid policy type");

        RuleFor(x => x.Title)
            .NotEmpty().WithMessage("Title is required")
            .MaximumLength(500).WithMessage("Title cannot exceed 500 characters");

        RuleFor(x => x.Version)
            .NotEmpty().WithMessage("Version is required")
            .MaximumLength(50).WithMessage("Version cannot exceed 50 characters")
            .Matches(@"^[0-9]+(\.[0-9]+)*$").WithMessage("Version must be in format like 1.0, 1.2.3, etc.");

        RuleFor(x => x.ContentBody)
            .NotEmpty().WithMessage("Content body is required");

        RuleFor(x => x.EffectiveDate)
            .NotEmpty().WithMessage("Effective date is required");

        RuleFor(x => x.ExpiryDate)
            .GreaterThan(x => x.EffectiveDate)
            .WithMessage("Expiry date must be after effective date")
            .When(x => x.ExpiryDate.HasValue);

        RuleFor(x => x.CreatedBy)
            .NotEmpty().WithMessage("Created by is required");

        RuleFor(x => x.CreatedByName)
            .NotEmpty().WithMessage("Created by name is required")
            .MaximumLength(200).WithMessage("Created by name cannot exceed 200 characters");

        RuleFor(x => x.ApplicableRoles)
            .Must(roles => roles == null || roles.All(role => !string.IsNullOrWhiteSpace(role)))
            .WithMessage("Applicable roles cannot contain empty roles");

        RuleFor(x => x.LegalReferences)
            .MaximumLength(2000).WithMessage("Legal references cannot exceed 2000 characters")
            .When(x => !string.IsNullOrEmpty(x.LegalReferences));
    }
}

/// <summary>
/// Validator for AcceptPolicyCommand
/// </summary>
public class AcceptPolicyCommandValidator : AbstractValidator<AcceptPolicyCommand>
{
    public AcceptPolicyCommandValidator()
    {
        RuleFor(x => x.PolicyId)
            .NotEmpty().WithMessage("Policy ID is required");

        RuleFor(x => x.UserId)
            .NotEmpty().WithMessage("User ID is required");

        RuleFor(x => x.UserName)
            .NotEmpty().WithMessage("User name is required")
            .MaximumLength(200).WithMessage("User name cannot exceed 200 characters");

        RuleFor(x => x.UserRole)
            .NotEmpty().WithMessage("User role is required")
            .MaximumLength(100).WithMessage("User role cannot exceed 100 characters");
    }
}
