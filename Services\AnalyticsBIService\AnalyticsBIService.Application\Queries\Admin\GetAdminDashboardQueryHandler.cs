using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Domain.Enums;
using AnalyticsBIService.Domain.Repositories;
using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Infrastructure.Repositories;

namespace AnalyticsBIService.Application.Queries.Admin;

/// <summary>
/// Handler for admin dashboard and analytics queries
/// </summary>
public class GetAdminDashboardQueryHandler :
    IRequestHandler<GetAdminPlatformDashboardQuery, AdminPlatformDashboardDto>,
    IRequestHandler<GetPlatformPerformanceMetricsQuery, PlatformPerformanceMetricsDto>,
    IRequestHandler<GetRealTimeKPIMonitoringQuery, RealTimeKPIMonitoringDto>,
    IRequestHandler<GetRevenueAnalyticsQuery, RevenueAnalyticsDto>,
    IRequestHandler<GetUserGrowthTrackingQuery, UserGrowthTrackingDto>
{
    private readonly IAnalyticsEventRepository _analyticsEventRepository;
    private readonly IMetricRepository _metricRepository;
    private readonly IAlertRepository _alertRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetAdminDashboardQueryHandler> _logger;

    public GetAdminDashboardQueryHandler(
        IAnalyticsEventRepository analyticsEventRepository,
        IMetricRepository metricRepository,
        IAlertRepository alertRepository,
        IMapper mapper,
        ILogger<GetAdminDashboardQueryHandler> logger)
    {
        _analyticsEventRepository = analyticsEventRepository;
        _metricRepository = metricRepository;
        _alertRepository = alertRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<AdminPlatformDashboardDto> Handle(GetAdminPlatformDashboardQuery request, CancellationToken cancellationToken)
    {
        _logger.LogDebug("Getting admin platform dashboard");

        var fromDate = request.FromDate ?? DateTime.UtcNow.AddDays(-30);
        var toDate = request.ToDate ?? DateTime.UtcNow;

        // Get platform performance metrics
        var platformPerformance = await GetPlatformPerformanceMetricsAsync(fromDate, toDate, request.Period, cancellationToken);

        // Get revenue analytics
        var revenueAnalytics = await GetRevenueAnalyticsAsync(fromDate, toDate, request.Period, cancellationToken);

        // Get user growth tracking
        var userGrowth = await GetUserGrowthTrackingAsync(fromDate, toDate, request.Period, cancellationToken);

        // Get real-time KPIs
        var realTimeKPIs = await GetRealTimeKPIMonitoringAsync(cancellationToken);

        // Get geographic performance
        var geographicPerformance = await GetGeographicPerformanceAsync(fromDate, toDate, cancellationToken);

        // Get subscription analytics
        var subscriptionAnalytics = await GetSubscriptionAnalyticsAsync(fromDate, toDate, request.Period, cancellationToken);

        // Get top events
        var topEvents = await GetTopEventsAsync(fromDate, toDate, cancellationToken);

        // Get critical alerts
        var criticalAlerts = await GetCriticalAlertsAsync(cancellationToken);

        // Get key KPIs
        var keyKPIs = await GetKeyKPIsAsync(cancellationToken);

        return new AdminPlatformDashboardDto
        {
            GeneratedAt = DateTime.UtcNow,
            FromDate = fromDate,
            ToDate = toDate,
            Period = request.Period.ToString(),
            PlatformPerformance = platformPerformance,
            RevenueAnalytics = revenueAnalytics,
            UserGrowth = userGrowth,
            RealTimeKPIs = realTimeKPIs,
            GeographicPerformance = geographicPerformance,
            SubscriptionAnalytics = subscriptionAnalytics,
            TopEvents = topEvents,
            CriticalAlerts = criticalAlerts,
            KeyKPIs = keyKPIs
        };
    }

    public async Task<PlatformPerformanceMetricsDto> Handle(GetPlatformPerformanceMetricsQuery request, CancellationToken cancellationToken)
    {
        return await GetPlatformPerformanceMetricsAsync(request.FromDate, request.ToDate, request.Period, cancellationToken);
    }

    public async Task<RealTimeKPIMonitoringDto> Handle(GetRealTimeKPIMonitoringQuery request, CancellationToken cancellationToken)
    {
        return await GetRealTimeKPIMonitoringAsync(cancellationToken);
    }

    public async Task<RevenueAnalyticsDto> Handle(GetRevenueAnalyticsQuery request, CancellationToken cancellationToken)
    {
        return await GetRevenueAnalyticsAsync(request.FromDate, request.ToDate, request.Period, cancellationToken);
    }

    public async Task<UserGrowthTrackingDto> Handle(GetUserGrowthTrackingQuery request, CancellationToken cancellationToken)
    {
        return await GetUserGrowthTrackingAsync(request.FromDate, request.ToDate, request.Period, cancellationToken);
    }

    private async Task<PlatformPerformanceMetricsDto> GetPlatformPerformanceMetricsAsync(
        DateTime fromDate,
        DateTime toDate,
        TimePeriod period,
        CancellationToken cancellationToken)
    {
        // Get platform metrics from analytics events and metrics tables
        var allEvents = await _analyticsEventRepository.GetByDateRangeAsync(fromDate, toDate, 1, 10000, cancellationToken);
        var totalTransactions = allEvents.Items
            .Count(e => e.EventType == AnalyticsEventType.BusinessTransaction);

        var allMetrics = await _metricRepository.GetByDateRangeAsync(fromDate, toDate, 1, 10000, cancellationToken);
        var totalRevenue = allMetrics.Items
            .Where(m => m.Name == "TotalRevenue")
            .Sum(m => m.Value.Value);

        // Calculate performance trends
        var performanceTrends = await GetPerformanceTrendsAsync(fromDate, toDate, period, cancellationToken);

        // Get service performance breakdown
        var servicePerformance = await GetServicePerformanceAsync(fromDate, toDate, cancellationToken);

        // Calculate overall performance score (simplified calculation)
        var overallPerformanceScore = CalculateOverallPerformanceScore(servicePerformance);

        return new PlatformPerformanceMetricsDto
        {
            FromDate = fromDate,
            ToDate = toDate,
            Period = period.ToString(),
            OverallPerformanceScore = overallPerformanceScore,
            TotalTransactions = totalTransactions,
            AverageResponseTime = 150, // This would come from system metrics
            SystemUptime = 99.9m,
            ErrorRate = 0.1m,
            TotalRevenue = (long)totalRevenue,
            RevenueGrowthRate = 15.5m, // This would be calculated from historical data
            ServicePerformance = servicePerformance,
            PerformanceTrends = performanceTrends
        };
    }

    private async Task<RealTimeKPIMonitoringDto> GetRealTimeKPIMonitoringAsync(CancellationToken cancellationToken)
    {
        // Get latest KPI values from metrics table
        var operationalMetrics = await _metricRepository.GetByCategoryAsync(KPICategory.Operational.ToString(), 1, 1000, cancellationToken);
        var financialMetrics = await _metricRepository.GetByCategoryAsync(KPICategory.Financial.ToString(), 1, 1000, cancellationToken);

        var allMetrics = operationalMetrics.Items.Concat(financialMetrics.Items);
        var latestKPIs = allMetrics
            .GroupBy(m => m.Name)
            .Select(g => g.OrderByDescending(m => m.CreatedAt).First())
            .ToList();

        var yesterday = DateTime.UtcNow.AddHours(-24);
        var recentEvents = await _analyticsEventRepository.GetByDateRangeAsync(yesterday, DateTime.UtcNow, 1, 1000, cancellationToken);

        var totalActiveUsers = recentEvents.Items
            .Where(e => e.UserId.HasValue)
            .Select(e => e.UserId)
            .Distinct()
            .Count();

        var totalTransactions = recentEvents.Items
            .Count(e => e.EventType == AnalyticsEventType.BusinessTransaction);

        var customKPIs = latestKPIs.Select(kpi => new RealTimeMetricDto
        {
            MetricName = kpi.Name,
            CurrentValue = kpi.Value.Value,
            Unit = kpi.Value.Unit,
            LastUpdated = kpi.CreatedAt,
            PerformanceStatus = kpi.GetPerformanceStatus().ToString()
        }).ToList();

        return new RealTimeKPIMonitoringDto
        {
            LastUpdated = DateTime.UtcNow,
            RFQConversionRate = GetKPIValue(latestKPIs, "RFQConversionRate", 75.5m),
            QuoteAccuracyRate = GetKPIValue(latestKPIs, "QuoteAccuracyRate", 92.3m),
            TripCompletionPercentage = GetKPIValue(latestKPIs, "TripCompletionPercentage", 98.7m),
            TotalActiveUsers = totalActiveUsers,
            TotalTransactions = totalTransactions,
            AverageResponseTime = 145.2m,
            SystemUptime = 99.95m,
            ErrorRate = 0.05m,
            TotalRevenue = (long)GetKPIValue(latestKPIs, "TotalRevenue", 1250000),
            CustomerSatisfactionScore = GetKPIValue(latestKPIs, "CustomerSatisfactionScore", 4.6m),
            CustomKPIs = customKPIs
        };
    }

    private async Task<RevenueAnalyticsDto> GetRevenueAnalyticsAsync(
        DateTime fromDate,
        DateTime toDate,
        TimePeriod period,
        CancellationToken cancellationToken)
    {
        // Get revenue metrics
        var allMetrics = await _metricRepository.GetByDateRangeAsync(fromDate, toDate, 1, 1000, cancellationToken);
        var revenueMetrics = allMetrics.Items
            .Where(m => m.Category == KPICategory.Financial)
            .ToList();

        var totalRevenue = revenueMetrics
            .Where(m => m.Name == "TotalRevenue")
            .Sum(m => m.Value.Value);

        // Calculate revenue trends
        var revenueTrends = await GetRevenueTrendsAsync(fromDate, toDate, period, cancellationToken);

        // Get subscription breakdown (placeholder data)
        var subscriptionBreakdown = new List<SubscriptionTierRevenueDto>
        {
            new() { TierName = "Basic", Revenue = totalRevenue * 0.3m, SubscriberCount = 1500, AverageRevenuePerSubscriber = 50, GrowthRate = 12.5m, MarketShare = 30 },
            new() { TierName = "Pro", Revenue = totalRevenue * 0.5m, SubscriberCount = 800, AverageRevenuePerSubscriber = 150, GrowthRate = 18.2m, MarketShare = 50 },
            new() { TierName = "Enterprise", Revenue = totalRevenue * 0.2m, SubscriberCount = 200, AverageRevenuePerSubscriber = 500, GrowthRate = 25.1m, MarketShare = 20 }
        };

        // Get user type breakdown (placeholder data)
        var userTypeBreakdown = new List<UserTypeRevenueDto>
        {
            new() { UserType = "TransportCompany", Revenue = totalRevenue * 0.4m, UserCount = 500, AverageRevenuePerUser = 200, GrowthRate = 15.3m },
            new() { UserType = "Broker", Revenue = totalRevenue * 0.35m, UserCount = 300, AverageRevenuePerUser = 300, GrowthRate = 20.1m },
            new() { UserType = "Carrier", Revenue = totalRevenue * 0.2m, UserCount = 800, AverageRevenuePerUser = 100, GrowthRate = 10.5m },
            new() { UserType = "Shipper", Revenue = totalRevenue * 0.05m, UserCount = 200, AverageRevenuePerUser = 50, GrowthRate = 8.2m }
        };

        // Get churn analysis
        var churnAnalysis = await GetChurnAnalysisAsync(fromDate, toDate, cancellationToken);

        return new RevenueAnalyticsDto
        {
            FromDate = fromDate,
            ToDate = toDate,
            Period = period.ToString(),
            TotalRevenue = totalRevenue,
            RevenueGrowthRate = 15.7m, // This would be calculated from historical data
            AverageRevenuePerUser = totalRevenue / Math.Max(2500, 1), // Total users placeholder
            MonthlyRecurringRevenue = totalRevenue / ((toDate - fromDate).Days / 30.0m),
            AnnualRecurringRevenue = totalRevenue * 12,
            ChurnRate = 5.2m,
            CustomerLifetimeValue = 2500m,
            RevenueTrends = revenueTrends,
            SubscriptionBreakdown = subscriptionBreakdown,
            UserTypeBreakdown = userTypeBreakdown,
            ChurnAnalysis = churnAnalysis
        };
    }

    private async Task<UserGrowthTrackingDto> GetUserGrowthTrackingAsync(
        DateTime fromDate,
        DateTime toDate,
        TimePeriod period,
        CancellationToken cancellationToken)
    {
        // Get user activity events
        var allEvents = await _analyticsEventRepository.GetByDateRangeAsync(fromDate, toDate, 1, 1000, cancellationToken);
        var userEvents = allEvents.Items
            .Where(e => e.EventType == AnalyticsEventType.UserActivity &&
                       e.UserId.HasValue)
            .ToList();

        var totalUsers = userEvents.Select(e => e.UserId).Distinct().Count();
        var activeUsers = userEvents
            .Where(e => e.Timestamp >= DateTime.UtcNow.AddDays(-30))
            .Select(e => e.UserId)
            .Distinct()
            .Count();

        // Calculate growth trends
        var growthTrends = await GetUserGrowthTrendsAsync(fromDate, toDate, period, cancellationToken);

        // Get user type breakdown
        var userTypeBreakdown = await GetUserTypeGrowthAsync(fromDate, toDate, cancellationToken);

        // Get cohort analysis (simplified)
        var cohortAnalysis = await GetCohortAnalysisAsync(fromDate, toDate, cancellationToken);

        // Get acquisition analysis
        var acquisitionAnalysis = await GetAcquisitionAnalysisAsync(fromDate, toDate, cancellationToken);

        return new UserGrowthTrackingDto
        {
            FromDate = fromDate,
            ToDate = toDate,
            Period = period.ToString(),
            TotalUsers = totalUsers,
            NewUsers = totalUsers / 10, // Simplified calculation
            ActiveUsers = activeUsers,
            UserGrowthRate = 12.5m,
            UserRetentionRate = 85.3m,
            UserActivationRate = 78.9m,
            GrowthTrends = growthTrends,
            UserTypeBreakdown = userTypeBreakdown,
            CohortAnalysis = cohortAnalysis,
            AcquisitionAnalysis = acquisitionAnalysis
        };
    }

    // Helper methods for data calculation
    private static decimal GetKPIValue(List<Domain.Entities.Metric> metrics, string metricName, decimal defaultValue)
    {
        return metrics.FirstOrDefault(m => m.Name == metricName)?.Value.Value ?? defaultValue;
    }

    private static decimal CalculateOverallPerformanceScore(List<ServicePerformanceDto> servicePerformance)
    {
        return servicePerformance.Any() ? servicePerformance.Average(s => s.PerformanceScore) : 85.5m;
    }

    // Placeholder implementations for complex calculations
    private async Task<List<MetricDataPointDto>> GetPerformanceTrendsAsync(DateTime fromDate, DateTime toDate, TimePeriod period, CancellationToken cancellationToken)
    {
        // This would implement actual trend calculation logic
        return new List<MetricDataPointDto>();
    }

    private async Task<List<ServicePerformanceDto>> GetServicePerformanceAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken)
    {
        // This would get actual service performance data
        return new List<ServicePerformanceDto>
        {
            new() { ServiceName = "Order Management", PerformanceScore = 92.5m, AverageResponseTime = 120, ErrorRate = 0.1m, Uptime = 99.9m, RequestCount = 15000 },
            new() { ServiceName = "Trip Management", PerformanceScore = 88.3m, AverageResponseTime = 180, ErrorRate = 0.2m, Uptime = 99.8m, RequestCount = 12000 },
            new() { ServiceName = "User Management", PerformanceScore = 95.1m, AverageResponseTime = 80, ErrorRate = 0.05m, Uptime = 99.95m, RequestCount = 8000 },
            new() { ServiceName = "Subscription Management", PerformanceScore = 90.7m, AverageResponseTime = 100, ErrorRate = 0.1m, Uptime = 99.9m, RequestCount = 5000 }
        };
    }

    private async Task<List<RevenueDataPointDto>> GetRevenueTrendsAsync(DateTime fromDate, DateTime toDate, TimePeriod period, CancellationToken cancellationToken)
    {
        // This would implement actual revenue trend calculation
        return new List<RevenueDataPointDto>();
    }

    private async Task<ChurnAnalysisDto> GetChurnAnalysisAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken)
    {
        // This would implement actual churn analysis
        return new ChurnAnalysisDto
        {
            OverallChurnRate = 5.2m,
            MonthlyChurnRate = 4.8m,
            AnnualChurnRate = 15.6m,
            ChurnPredictionAccuracy = 78.5m
        };
    }

    private async Task<List<UserGrowthDataPointDto>> GetUserGrowthTrendsAsync(DateTime fromDate, DateTime toDate, TimePeriod period, CancellationToken cancellationToken)
    {
        // This would implement actual user growth trend calculation
        return new List<UserGrowthDataPointDto>();
    }

    private async Task<List<UserTypeGrowthDto>> GetUserTypeGrowthAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken)
    {
        // This would implement actual user type growth calculation
        return new List<UserTypeGrowthDto>();
    }

    private async Task<List<UserCohortAnalysisDto>> GetCohortAnalysisAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken)
    {
        // This would implement actual cohort analysis
        return new List<UserCohortAnalysisDto>();
    }

    private async Task<UserAcquisitionAnalysisDto> GetAcquisitionAnalysisAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken)
    {
        // This would implement actual acquisition analysis
        return new UserAcquisitionAnalysisDto();
    }

    private async Task<GeographicPerformanceDto> GetGeographicPerformanceAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken)
    {
        // This would implement actual geographic performance analysis
        return new GeographicPerformanceDto
        {
            FromDate = fromDate,
            ToDate = toDate
        };
    }

    private async Task<SubscriptionAnalyticsDto> GetSubscriptionAnalyticsAsync(DateTime fromDate, DateTime toDate, TimePeriod period, CancellationToken cancellationToken)
    {
        // This would implement actual subscription analytics
        return new SubscriptionAnalyticsDto
        {
            FromDate = fromDate,
            ToDate = toDate,
            Period = period.ToString()
        };
    }

    private async Task<List<TopEventDto>> GetTopEventsAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken)
    {
        var allEvents = await _analyticsEventRepository.GetByDateRangeAsync(fromDate, toDate, 1, int.MaxValue, cancellationToken);
        return allEvents.Items
            .GroupBy(e => new { e.EventName, e.EventType, e.DataSource })
            .Select(g => new TopEventDto
            {
                EventName = g.Key.EventName,
                EventType = g.Key.EventType.ToString(),
                DataSource = g.Key.DataSource.ToString(),
                EventCount = g.Count(),
                UniqueUsers = g.Where(e => e.UserId.HasValue).Select(e => e.UserId).Distinct().Count(),
                FirstOccurrence = g.Min(e => e.Timestamp),
                LastOccurrence = g.Max(e => e.Timestamp)
            })
            .OrderByDescending(e => e.EventCount)
            .Take(10)
            .ToList();
    }

    private async Task<List<AlertDto>> GetCriticalAlertsAsync(CancellationToken cancellationToken)
    {
        var allAlerts = await _alertRepository.GetAllAsync(1, 100, cancellationToken);
        var criticalAlerts = allAlerts.Items
            .Where(a => a.IsActive && a.Severity == AlertSeverity.Critical)
            .OrderByDescending(a => a.TriggeredAt)
            .Take(10)
            .ToList();

        return _mapper.Map<List<AlertDto>>(criticalAlerts);
    }

    private async Task<List<KPIPerformanceDto>> GetKeyKPIsAsync(CancellationToken cancellationToken)
    {
        var allMetrics = await _metricRepository.GetAllAsync(1, 100, cancellationToken);
        var keyMetrics = allMetrics.Items
            .Where(m => m.Target != null)
            .GroupBy(m => new { m.Name, m.UserId })
            .Select(g => g.OrderByDescending(m => m.CreatedAt).First())
            .Take(10)
            .ToList();

        return keyMetrics.Select(m => new KPIPerformanceDto
        {
            MetricName = m.Name,
            Category = m.Category.ToString(),
            UserType = m.UserType?.ToString(),
            CurrentValue = m.Value.Value,
            TargetValue = m.Target?.TargetValue,
            Unit = m.Value.Unit,
            IsHigherBetter = m.Target?.IsHigherBetter,
            PerformanceStatus = m.GetPerformanceStatus().ToString(),
            TargetAchievementPercentage = m.GetTargetAchievementPercentage(),
            AlertSeverity = m.GetAlertSeverity().ToString(),
            LastUpdated = m.CreatedAt
        }).ToList();
    }
}
