using AutoMapper;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Entities;
using NetworkFleetManagement.Domain.ValueObjects;

namespace NetworkFleetManagement.Application.Mappings;

public class NetworkFleetMappingProfile : Profile
{
    public NetworkFleetMappingProfile()
    {
        // Carrier mappings
        CreateMap<Carrier, CarrierDto>()
            .ForMember(dest => dest.IsActive, opt => opt.MapFrom(src => src.IsActive))
            .ForMember(dest => dest.VehicleCount, opt => opt.MapFrom(src => src.Vehicles.Count))
            .ForMember(dest => dest.DriverCount, opt => opt.MapFrom(src => src.Drivers.Count))
            .ForMember(dest => dest.AvailableVehicleCount, opt => opt.MapFrom(src => src.GetAvailableVehicleCount()))
            .ForMember(dest => dest.AvailableDriverCount, opt => opt.MapFrom(src => src.GetAvailableDriverCount()));

        CreateMap<Carrier, CarrierSummaryDto>()
            .ForMember(dest => dest.IsActive, opt => opt.MapFrom(src => src.IsActive))
            .ForMember(dest => dest.Rating, opt => opt.MapFrom(src => src.PerformanceMetrics.Rating))
            .ForMember(dest => dest.TotalTrips, opt => opt.MapFrom(src => src.PerformanceMetrics.TotalTrips))
            .ForMember(dest => dest.VehicleCount, opt => opt.MapFrom(src => src.Vehicles.Count))
            .ForMember(dest => dest.DriverCount, opt => opt.MapFrom(src => src.Drivers.Count));

        // Vehicle mappings
        CreateMap<Vehicle, VehicleDto>()
            .ForMember(dest => dest.DisplayName, opt => opt.MapFrom(src => src.DisplayName))
            .ForMember(dest => dest.IsInsuranceValid, opt => opt.MapFrom(src => src.IsInsuranceValid))
            .ForMember(dest => dest.IsFitnessValid, opt => opt.MapFrom(src => src.IsFitnessValid))
            .ForMember(dest => dest.IsPermitValid, opt => opt.MapFrom(src => src.IsPermitValid))
            .ForMember(dest => dest.IsAvailable, opt => opt.MapFrom(src => src.IsAvailable))
            .ForMember(dest => dest.CarrierCompanyName, opt => opt.MapFrom(src => src.Carrier.CompanyName))
            .ForMember(dest => dest.DocumentCount, opt => opt.MapFrom(src => src.Documents.Count))
            .ForMember(dest => dest.MaintenanceRecordCount, opt => opt.MapFrom(src => src.MaintenanceRecords.Count));

        CreateMap<Vehicle, VehicleSummaryDto>()
            .ForMember(dest => dest.DisplayName, opt => opt.MapFrom(src => src.DisplayName))
            .ForMember(dest => dest.IsAvailable, opt => opt.MapFrom(src => src.IsAvailable))
            .ForMember(dest => dest.CarrierCompanyName, opt => opt.MapFrom(src => src.Carrier.CompanyName));

        // Driver mappings
        CreateMap<Driver, DriverDto>()
            .ForMember(dest => dest.FullName, opt => opt.MapFrom(src => src.FullName))
            .ForMember(dest => dest.IsLicenseValid, opt => opt.MapFrom(src => src.IsLicenseValid))
            .ForMember(dest => dest.IsAvailable, opt => opt.MapFrom(src => src.IsAvailable))
            .ForMember(dest => dest.CarrierCompanyName, opt => opt.MapFrom(src => src.Carrier.CompanyName))
            .ForMember(dest => dest.DocumentCount, opt => opt.MapFrom(src => src.Documents.Count))
            .ForMember(dest => dest.VehicleAssignmentCount, opt => opt.MapFrom(src => src.VehicleAssignments.Count))
            .ForMember(dest => dest.PreferredRoutes, opt => opt.MapFrom(src => src.PreferredRoutes.ToList()))
            .ForMember(dest => dest.OperationalAreas, opt => opt.MapFrom(src => src.OperationalAreas.ToList()));

        CreateMap<Driver, DriverSummaryDto>()
            .ForMember(dest => dest.FullName, opt => opt.MapFrom(src => src.FullName))
            .ForMember(dest => dest.Rating, opt => opt.MapFrom(src => src.PerformanceMetrics.Rating))
            .ForMember(dest => dest.TotalTrips, opt => opt.MapFrom(src => src.PerformanceMetrics.TotalTrips))
            .ForMember(dest => dest.IsAvailable, opt => opt.MapFrom(src => src.IsAvailable))
            .ForMember(dest => dest.CarrierCompanyName, opt => opt.MapFrom(src => src.Carrier.CompanyName));

        // Value object mappings
        CreateMap<Location, LocationDto>().ReverseMap();
        CreateMap<VehicleSpecifications, VehicleSpecificationsDto>().ReverseMap();
        CreateMap<PerformanceMetrics, PerformanceMetricsDto>()
            .ForMember(dest => dest.CompletionRate, opt => opt.MapFrom(src => src.CompletionRate))
            .ForMember(dest => dest.CancellationRate, opt => opt.MapFrom(src => src.CancellationRate));

        // Network mappings
        CreateMap<BrokerCarrierNetwork, BrokerCarrierNetworkDto>()
            .ForMember(dest => dest.IsActive, opt => opt.MapFrom(src => src.IsActive))
            .ForMember(dest => dest.IsContractValid, opt => opt.MapFrom(src => src.IsContractValid))
            .ForMember(dest => dest.IsExclusivityActive, opt => opt.MapFrom(src => src.IsExclusivityActive))
            .ForMember(dest => dest.CarrierCompanyName, opt => opt.MapFrom(src => src.Carrier.CompanyName));

        // VehicleTypeMaster mappings
        CreateMap<VehicleTypeMaster, VehicleTypeMasterDto>();

        CreateMap<VehicleTypeMaster, VehicleTypeMasterSummaryDto>()
            .ForMember(dest => dest.CapacityRange, opt => opt.MapFrom(src =>
                GetCapacityRangeString(src.MinLoadCapacityKg, src.MaxLoadCapacityKg, src.MinVolumeCapacityM3, src.MaxVolumeCapacityM3)));
    }

    private static string GetCapacityRangeString(decimal? minLoad, decimal? maxLoad, decimal? minVolume, decimal? maxVolume)
    {
        var parts = new List<string>();

        if (minLoad.HasValue || maxLoad.HasValue)
        {
            var loadRange = minLoad.HasValue && maxLoad.HasValue
                ? $"{minLoad:F0}-{maxLoad:F0} kg"
                : minLoad.HasValue
                    ? $"≥{minLoad:F0} kg"
                    : $"≤{maxLoad:F0} kg";
            parts.Add($"Load: {loadRange}");
        }

        if (minVolume.HasValue || maxVolume.HasValue)
        {
            var volumeRange = minVolume.HasValue && maxVolume.HasValue
                ? $"{minVolume:F1}-{maxVolume:F1} m³"
                : minVolume.HasValue
                    ? $"≥{minVolume:F1} m³"
                    : $"≤{maxVolume:F1} m³";
            parts.Add($"Volume: {volumeRange}");
        }

        return string.Join(", ", parts);
    }
}
