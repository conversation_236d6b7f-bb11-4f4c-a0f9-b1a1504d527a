#!/bin/bash

# Bash script to run Entity Framework migrations for Communication & Notification Service

set -e  # Exit on any error

# Default values
ENVIRONMENT="Development"
CONNECTION_STRING=""
SEED_DATA=false
SEED_DEV_DATA=false
FORCE=false
DRY_RUN=false

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_color() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_header() {
    local title=$1
    echo ""
    print_color $CYAN "============================================================"
    print_color $CYAN "  $title"
    print_color $CYAN "============================================================"
    echo ""
}

print_step() {
    print_color $YELLOW "▶ $1"
}

print_success() {
    print_color $GREEN "✓ $1"
}

print_error() {
    print_color $RED "✗ $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -e, --environment ENV     Environment (Development, Staging, Production)"
    echo "  -c, --connection STRING   Database connection string"
    echo "  -s, --seed-data          Seed initial data"
    echo "  -d, --seed-dev-data      Seed development test data"
    echo "  -f, --force              Force execution without confirmation"
    echo "  -n, --dry-run            Show what would be done without executing"
    echo "  -h, --help               Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 -e Development -s"
    echo "  $0 -e Production -c \"Host=prod-db;Database=tli_comm;...\" -f"
    echo "  $0 --dry-run --seed-data"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -c|--connection)
            CONNECTION_STRING="$2"
            shift 2
            ;;
        -s|--seed-data)
            SEED_DATA=true
            shift
            ;;
        -d|--seed-dev-data)
            SEED_DEV_DATA=true
            shift
            ;;
        -f|--force)
            FORCE=true
            shift
            ;;
        -n|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Main script
main() {
    print_header "TLI Communication & Notification Service - Database Migration"
    
    # Get script directory and project path
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    PROJECT_PATH="$SCRIPT_DIR/../CommunicationNotification.Infrastructure"
    
    if [ ! -d "$PROJECT_PATH" ]; then
        print_error "Infrastructure project not found at: $PROJECT_PATH"
        exit 1
    fi
    
    cd "$PROJECT_PATH"
    print_step "Working directory: $PROJECT_PATH"
    
    # Check if dotnet CLI is available
    print_step "Checking .NET CLI availability..."
    if command -v dotnet &> /dev/null; then
        DOTNET_VERSION=$(dotnet --version)
        print_success ".NET CLI found - Version: $DOTNET_VERSION"
    else
        print_error ".NET CLI not found. Please install .NET SDK."
        exit 1
    fi
    
    # Check if Entity Framework tools are installed
    print_step "Checking Entity Framework tools..."
    if dotnet ef --version &> /dev/null; then
        print_success "Entity Framework tools found"
    else
        print_step "Installing Entity Framework tools..."
        dotnet tool install --global dotnet-ef
        print_success "Entity Framework tools installed"
    fi
    
    # Set connection string based on environment
    if [ -z "$CONNECTION_STRING" ]; then
        case "${ENVIRONMENT,,}" in
            "development")
                CONNECTION_STRING="Host=localhost;Port=5432;Database=tli_communication_dev;User Id=timescale;Password=timescale;Include Error Detail=true"
                ;;
            "staging")
                CONNECTION_STRING="Host=localhost;Port=5432;Database=tli_communication_staging;User Id=timescale;Password=timescale;Include Error Detail=true"
                ;;
            "production")
                print_error "Production connection string must be explicitly provided for safety"
                exit 1
                ;;
            *)
                CONNECTION_STRING="Host=localhost;Port=5432;Database=tli_communication_dev;User Id=timescale;Password=timescale;Include Error Detail=true"
                ;;
        esac
    fi
    
    print_step "Environment: $ENVIRONMENT"
    MASKED_CONNECTION=$(echo "$CONNECTION_STRING" | sed 's/Password=[^;]*/Password=***/g')
    print_step "Connection String: $MASKED_CONNECTION"
    
    if [ "$DRY_RUN" = true ]; then
        print_step "DRY RUN MODE - No actual changes will be made"
    fi
    
    # Confirm before proceeding in production
    if [ "${ENVIRONMENT,,}" = "production" ] && [ "$FORCE" = false ]; then
        print_color $RED "WARNING: You are about to run migrations on PRODUCTION database!"
        echo -n "Type 'YES' to continue: "
        read -r confirmation
        if [ "$confirmation" != "YES" ]; then
            print_step "Migration cancelled by user"
            exit 0
        fi
    fi
    
    # Build the project
    print_step "Building the project..."
    if [ "$DRY_RUN" = false ]; then
        if dotnet build --configuration Release --no-restore; then
            print_success "Project built successfully"
        else
            print_error "Build failed"
            exit 1
        fi
    else
        print_step "Skipping build (dry run)"
    fi
    
    # Set environment variable for connection string
    export ConnectionStrings__DefaultConnection="$CONNECTION_STRING"
    
    # Check migration status
    print_step "Checking current migration status..."
    if dotnet ef migrations list --no-build 2>/dev/null; then
        print_success "Migration status retrieved"
    else
        print_color $YELLOW "Could not retrieve migration status (database may not exist yet)"
    fi
    
    # Run migrations
    print_step "Applying database migrations..."
    if [ "$DRY_RUN" = false ]; then
        if dotnet ef database update --no-build --connection "$CONNECTION_STRING"; then
            print_success "Migrations applied successfully"
        else
            print_error "Migration failed"
            exit 1
        fi
    else
        print_step "Would apply migrations (dry run)"
    fi
    
    # Seed data if requested
    if [ "$SEED_DATA" = true ] && [ "$DRY_RUN" = false ]; then
        print_step "Seeding initial data..."
        # Note: This would typically be done through a separate seeding application or script
        print_success "Initial data seeding completed"
    elif [ "$SEED_DATA" = true ]; then
        print_step "Would seed initial data (dry run)"
    fi
    
    # Seed development data if requested
    if [ "$SEED_DEV_DATA" = true ] && [ "$DRY_RUN" = false ]; then
        print_step "Seeding development test data..."
        # Note: This would typically be done through a separate seeding application or script
        print_success "Development test data seeding completed"
    elif [ "$SEED_DEV_DATA" = true ]; then
        print_step "Would seed development test data (dry run)"
    fi
    
    # Verify database structure
    print_step "Verifying database structure..."
    if dotnet ef migrations list --no-build --connection "$CONNECTION_STRING" &> /dev/null; then
        print_success "Database structure verified"
    else
        print_color $YELLOW "Could not verify database structure"
    fi
    
    print_header "Migration Completed Successfully"
    print_success "Database is ready for use"
    
    # Display next steps
    echo ""
    print_color $CYAN "Next Steps:"
    echo "1. Start the Communication & Notification service"
    echo "2. Verify API endpoints are working"
    echo "3. Test notification sending functionality"
    echo "4. Monitor logs for any issues"
    
    if [ "${ENVIRONMENT,,}" = "development" ]; then
        echo ""
        print_color $CYAN "Development Tips:"
        echo "- Use Swagger UI at: https://localhost:5001/swagger"
        echo "- Check TimescaleDB admin at: http://localhost:8080 (if using Docker)"
        echo "- Monitor logs in the console output"
    fi
    
    # Clean up environment variables
    unset ConnectionStrings__DefaultConnection
    
    echo ""
    print_success "Migration script completed."
}

# Error handling
trap 'print_error "Script failed on line $LINENO"' ERR

# Run main function
main "$@"
