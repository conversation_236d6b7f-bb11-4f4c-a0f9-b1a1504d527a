using CommunicationNotification.Application.Common;
using CommunicationNotification.Domain.Enums;

namespace CommunicationNotification.Application.Commands.Alerts;

/// <summary>
/// Command to send performance alert notifications
/// </summary>
public class SendPerformanceAlertCommand : CommandBase<SendPerformanceAlertResult>
{
    public Guid CarrierId { get; set; }
    public string PerformanceMetric { get; set; } = string.Empty; // Rating, OnTimeDelivery, CustomerSatisfaction, etc.
    public string AlertType { get; set; } = string.Empty; // Improvement, Decline, Milestone, Threshold
    public decimal CurrentValue { get; set; }
    public decimal? PreviousValue { get; set; }
    public decimal? TargetValue { get; set; }
    public decimal? ThresholdValue { get; set; }
    public string ChangeDirection { get; set; } = string.Empty; // Increase, Decrease, Stable
    public decimal ChangePercentage { get; set; }
    public string TimePeriod { get; set; } = string.Empty; // Daily, Weekly, Monthly
    public DateTime MeasurementDate { get; set; }
    public Dictionary<string, object> PerformanceData { get; set; } = new();
    public List<NotificationChannel> PreferredChannels { get; set; } = new();
    public Priority Priority { get; set; } = Priority.Normal;
    public bool RequireAcknowledgment { get; set; } = false;
    public List<string> Tags { get; set; } = new();
}

/// <summary>
/// Command to send rating change alert
/// </summary>
public class SendRatingChangeAlertCommand : CommandBase<SendPerformanceAlertResult>
{
    public Guid CarrierId { get; set; }
    public decimal CurrentRating { get; set; }
    public decimal PreviousRating { get; set; }
    public decimal RatingChange { get; set; }
    public string RatingCategory { get; set; } = string.Empty; // Overall, Service, Communication, etc.
    public string ChangeType { get; set; } = string.Empty; // Improvement, Decline, Significant
    public int ReviewCount { get; set; }
    public DateTime RatingDate { get; set; }
    public List<string> RecentFeedback { get; set; } = new();
    public Dictionary<string, object> RatingDetails { get; set; } = new();
    public List<NotificationChannel> PreferredChannels { get; set; } = new();
    public Priority Priority { get; set; } = Priority.Normal;
    public bool RequireAcknowledgment { get; set; } = false;
    public List<string> Tags { get; set; } = new();
}

/// <summary>
/// Command to send performance milestone alert
/// </summary>
public class SendPerformanceMilestoneAlertCommand : CommandBase<SendPerformanceAlertResult>
{
    public Guid CarrierId { get; set; }
    public string MilestoneType { get; set; } = string.Empty; // TripsCompleted, EarningsReached, RatingAchieved
    public string MilestoneName { get; set; } = string.Empty;
    public decimal MilestoneValue { get; set; }
    public string MilestoneUnit { get; set; } = string.Empty; // trips, rupees, rating points
    public DateTime AchievedDate { get; set; }
    public string AchievementLevel { get; set; } = string.Empty; // Bronze, Silver, Gold, Platinum
    public Dictionary<string, object> MilestoneData { get; set; } = new();
    public List<string> Rewards { get; set; } = new(); // Badge, Bonus, Recognition
    public string NextMilestone { get; set; } = string.Empty;
    public decimal? NextMilestoneTarget { get; set; }
    public List<NotificationChannel> PreferredChannels { get; set; } = new();
    public Priority Priority { get; set; } = Priority.Normal;
    public bool RequireAcknowledgment { get; set; } = false;
    public List<string> Tags { get; set; } = new();
}

/// <summary>
/// Command to send bulk performance alerts for multiple carriers
/// </summary>
public class SendBulkPerformanceAlertsCommand : CommandBase<SendBulkPerformanceAlertsResult>
{
    public List<PerformanceAlertInfo> PerformanceAlerts { get; set; } = new();
    public string AlertCategory { get; set; } = string.Empty; // Rating, Performance, Milestone
    public List<NotificationChannel> PreferredChannels { get; set; } = new();
    public Priority Priority { get; set; } = Priority.Normal;
    public bool RequireAcknowledgment { get; set; } = false;
    public bool GroupByAlertType { get; set; } = false;
    public List<string> Tags { get; set; } = new();
}

/// <summary>
/// Command to configure performance alert thresholds
/// </summary>
public class ConfigurePerformanceAlertThresholdsCommand : CommandBase<ConfigurePerformanceAlertThresholdsResult>
{
    public Guid CarrierId { get; set; }
    public List<PerformanceThreshold> Thresholds { get; set; } = new();
    public bool IsActive { get; set; } = true;
    public List<NotificationChannel> PreferredChannels { get; set; } = new();
    public Dictionary<string, object> AlertSettings { get; set; } = new();
}

/// <summary>
/// Information about a performance alert
/// </summary>
public class PerformanceAlertInfo
{
    public Guid CarrierId { get; set; }
    public string PerformanceMetric { get; set; } = string.Empty;
    public string AlertType { get; set; } = string.Empty;
    public decimal CurrentValue { get; set; }
    public decimal? PreviousValue { get; set; }
    public decimal? ThresholdValue { get; set; }
    public string ChangeDirection { get; set; } = string.Empty;
    public decimal ChangePercentage { get; set; }
    public DateTime MeasurementDate { get; set; }
    public Dictionary<string, object> PerformanceData { get; set; } = new();
}

/// <summary>
/// Performance threshold configuration
/// </summary>
public class PerformanceThreshold
{
    public string MetricName { get; set; } = string.Empty;
    public decimal WarningThreshold { get; set; }
    public decimal CriticalThreshold { get; set; }
    public string ThresholdType { get; set; } = string.Empty; // Above, Below, Range
    public bool IsEnabled { get; set; } = true;
    public string AlertFrequency { get; set; } = string.Empty; // Immediate, Daily, Weekly
}

/// <summary>
/// Result of sending performance alert
/// </summary>
public class SendPerformanceAlertResult
{
    public Guid AlertId { get; set; }
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public List<NotificationChannel> ChannelsUsed { get; set; } = new();
    public DateTime SentAt { get; set; }
    public string? ExternalId { get; set; }
    public Dictionary<string, object> DeliveryMetadata { get; set; } = new();
    public bool RequiresAcknowledgment { get; set; }
    public DateTime? AcknowledgmentDeadline { get; set; }
}

/// <summary>
/// Result of sending bulk performance alerts
/// </summary>
public class SendBulkPerformanceAlertsResult
{
    public List<SendPerformanceAlertResult> AlertResults { get; set; } = new();
    public int TotalAlerts { get; set; }
    public int SuccessfulAlerts { get; set; }
    public int FailedAlerts { get; set; }
    public List<string> Errors { get; set; } = new();
    public DateTime ProcessedAt { get; set; }
    public TimeSpan ProcessingDuration { get; set; }
}

/// <summary>
/// Result of configuring performance alert thresholds
/// </summary>
public class ConfigurePerformanceAlertThresholdsResult
{
    public Guid ConfigurationId { get; set; }
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime ConfiguredAt { get; set; }
    public int ThresholdsConfigured { get; set; }
    public Dictionary<string, object> ConfigurationMetadata { get; set; } = new();
}
