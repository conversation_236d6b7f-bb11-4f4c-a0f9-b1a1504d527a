using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Application.DTOs
{
    public class TaxConfigurationDto
    {
        public TaxType TaxType { get; set; }
        public decimal Rate { get; set; }
        public bool IsIncluded { get; set; }
        public List<string> ApplicableRegions { get; set; } = new();
        public DateTime EffectiveDate { get; set; }
        public DateTime? ExpirationDate { get; set; }
    }

    public class TaxCategoryDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public bool IsActive { get; set; }
        public List<TaxConfigurationDto> TaxConfigurations { get; set; } = new();
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class TaxExemptionDto
    {
        public Guid Id { get; set; }
        public Guid UserId { get; set; }
        public string ExemptionType { get; set; } = string.Empty;
        public string ExemptionNumber { get; set; } = string.Empty;
        public string IssuingAuthority { get; set; } = string.Empty;
        public DateTime ValidFrom { get; set; }
        public DateTime ValidTo { get; set; }
        public List<TaxType> ExemptTaxTypes { get; set; } = new();
        public List<string> ApplicableRegions { get; set; } = new();
        public bool IsActive { get; set; }
        public string? DocumentPath { get; set; }
        public DateTime? VerifiedAt { get; set; }
        public Guid? VerifiedByUserId { get; set; }
        public string? VerificationNotes { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class GlobalTaxConfigurationDto
    {
        public Guid Id { get; set; }
        public TaxConfigurationDto TaxConfiguration { get; set; } = null!;
        public bool IsActive { get; set; }
        public int Priority { get; set; }
        public string? Description { get; set; }
        public Guid CreatedByUserId { get; set; }
        public Guid? ModifiedByUserId { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class PlanTaxConfigurationDto
    {
        public Guid Id { get; set; }
        public Guid PlanId { get; set; }
        public string PlanName { get; set; } = string.Empty;
        public TaxConfigurationDto TaxConfiguration { get; set; } = null!;
        public bool IsActive { get; set; }
        public DateTime EffectiveFrom { get; set; }
        public DateTime? EffectiveTo { get; set; }
        public string? Notes { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class PlanTaxDetailsDto
    {
        public Guid PlanId { get; set; }
        public string PlanName { get; set; } = string.Empty;
        public decimal BasePrice { get; set; }
        public string Currency { get; set; } = string.Empty;
        public string CustomerRegion { get; set; } = string.Empty;
        public List<TaxBreakdownItemDto> TaxBreakdown { get; set; } = new();
        public decimal TotalTaxAmount { get; set; }
        public decimal DisplayPrice { get; set; }
        public bool IsTaxInclusive { get; set; }
        public string TaxDisplayText { get; set; } = string.Empty;
        public List<string> TaxLabels { get; set; } = new();
        public bool HasExemptions { get; set; }
        public DateTime CalculatedAt { get; set; }
    }

    public class TaxBreakdownItemDto
    {
        public TaxType TaxType { get; set; }
        public string TaxName { get; set; } = string.Empty;
        public decimal Rate { get; set; }
        public decimal TaxableAmount { get; set; }
        public decimal TaxAmount { get; set; }
        public bool IsExempt { get; set; }
        public string? ExemptionReason { get; set; }
    }

    public class TaxCalculationRequestDto
    {
        public Guid PlanId { get; set; }
        public string CustomerRegion { get; set; } = string.Empty;
        public Guid? CustomerId { get; set; }
        public DateTime? CalculationDate { get; set; }
        public bool IncludeTaxBreakdown { get; set; } = true;
    }

    public class TaxCalculationResponseDto
    {
        public decimal BaseAmount { get; set; }
        public string Currency { get; set; } = string.Empty;
        public List<TaxBreakdownItemDto> TaxItems { get; set; } = new();
        public decimal TotalTaxAmount { get; set; }
        public decimal TotalAmount { get; set; }
        public bool IsTaxInclusive { get; set; }
        public string CustomerRegion { get; set; } = string.Empty;
        public DateTime CalculatedAt { get; set; }
        public bool HasExemptions { get; set; }
    }

    public class CreateTaxCategoryDto
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
    }

    public class UpdateTaxCategoryDto
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
    }

    public class CreateTaxExemptionDto
    {
        public Guid UserId { get; set; }
        public string ExemptionType { get; set; } = string.Empty;
        public string ExemptionNumber { get; set; } = string.Empty;
        public string IssuingAuthority { get; set; } = string.Empty;
        public DateTime ValidFrom { get; set; }
        public DateTime ValidTo { get; set; }
        public List<TaxType> ExemptTaxTypes { get; set; } = new();
        public List<string> ApplicableRegions { get; set; } = new();
        public string? DocumentPath { get; set; }
    }

    public class SetGlobalTaxConfigurationDto
    {
        public TaxType TaxType { get; set; }
        public decimal Rate { get; set; }
        public bool IsIncluded { get; set; }
        public List<string> ApplicableRegions { get; set; } = new();
        public DateTime EffectiveDate { get; set; }
        public DateTime? ExpirationDate { get; set; }
        public int Priority { get; set; }
        public string? Description { get; set; }
    }

    public class SetPlanTaxConfigurationDto
    {
        public TaxType TaxType { get; set; }
        public decimal Rate { get; set; }
        public bool IsIncluded { get; set; }
        public List<string> ApplicableRegions { get; set; } = new();
        public DateTime EffectiveDate { get; set; }
        public DateTime? ExpirationDate { get; set; }
        public string? Notes { get; set; }
    }
}
