using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using System.Text.Json;

namespace SubscriptionManagement.Infrastructure.Configurations
{
    public class NotificationTemplateConfiguration : IEntityTypeConfiguration<NotificationTemplate>
    {
        public void Configure(EntityTypeBuilder<NotificationTemplate> builder)
        {
            builder.ToTable("NotificationTemplates");

            builder.HasKey(t => t.Id);

            builder.Property(t => t.Id)
                .IsRequired()
                .ValueGeneratedNever();

            builder.Property(t => t.Name)
                .IsRequired()
                .HasMaxLength(200);

            builder.Property(t => t.Description)
                .HasMaxLength(1000);

            builder.Property(t => t.Type)
                .IsRequired()
                .HasConversion<int>();

            builder.Property(t => t.Channel)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(t => t.Language)
                .IsRequired()
                .HasMaxLength(10)
                .HasDefaultValue("en");

            builder.Property(t => t.Subject)
                .HasMaxLength(500);

            builder.Property(t => t.Body)
                .IsRequired()
                .HasColumnType("text");

            builder.Property(t => t.IsActive)
                .IsRequired()
                .HasDefaultValue(true);

            // Configure Variables as JSON
            builder.Property(t => t.Variables)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                    v => JsonSerializer.Deserialize<Dictionary<string, string>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, string>())
                .HasColumnType("jsonb");

            // Configure Metadata as JSON
            builder.Property(t => t.Metadata)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                    v => JsonSerializer.Deserialize<Dictionary<string, string>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, string>())
                .HasColumnType("jsonb");

            builder.Property(t => t.CreatedAt)
                .IsRequired()
                .HasColumnType("datetime2");

            builder.Property(t => t.UpdatedAt)
                .HasColumnType("datetime2");

            // Configure indexes
            builder.HasIndex(t => t.Name)
                .IsUnique()
                .HasDatabaseName("IX_NotificationTemplates_Name");

            builder.HasIndex(t => t.Type)
                .HasDatabaseName("IX_NotificationTemplates_Type");

            builder.HasIndex(t => t.Channel)
                .HasDatabaseName("IX_NotificationTemplates_Channel");

            builder.HasIndex(t => t.Language)
                .HasDatabaseName("IX_NotificationTemplates_Language");

            builder.HasIndex(t => t.IsActive)
                .HasDatabaseName("IX_NotificationTemplates_IsActive");

            // Composite indexes for common queries
            builder.HasIndex(t => new { t.Type, t.Channel, t.Language, t.IsActive })
                .HasDatabaseName("IX_NotificationTemplates_Type_Channel_Language_IsActive");

            builder.HasIndex(t => new { t.Channel, t.IsActive })
                .HasDatabaseName("IX_NotificationTemplates_Channel_IsActive");

            builder.HasIndex(t => new { t.Type, t.IsActive })
                .HasDatabaseName("IX_NotificationTemplates_Type_IsActive");
        }
    }
}
