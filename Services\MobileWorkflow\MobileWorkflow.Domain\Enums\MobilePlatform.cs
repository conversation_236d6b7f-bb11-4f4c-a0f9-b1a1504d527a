namespace MobileWorkflow.Domain.Enums;

public enum MobilePlatform
{
    iOS = 1,
    Android = 2,
    Web = 3,
    PWA = 4, // Progressive Web App
    Windows = 5,
    MacOS = 6,
    Linux = 7
}

public enum DeviceType
{
    Phone = 1,
    Tablet = 2,
    Desktop = 3,
    Laptop = 4,
    Watch = 5,
    TV = 6,
    Unknown = 99
}

public enum AppFeature
{
    OfflineMode = 1,
    PushNotifications = 2,
    LocationTracking = 3,
    CameraAccess = 4,
    FileUpload = 5,
    VoiceRecording = 6,
    Biometrics = 7,
    NFC = 8,
    Bluetooth = 9,
    QRCodeScanner = 10,
    Maps = 11,
    Calendar = 12,
    Contacts = 13,
    RealTimeSync = 14,
    AdvancedAnalytics = 15
}

public enum SyncStatus
{
    Pending = 1,
    InProgress = 2,
    Completed = 3,
    Failed = 4,
    Conflict = 5,
    Cancelled = 6
}

public enum OfflineDataPriority
{
    Critical = 1,    // Emergency alerts, safety issues
    High = 2,        // Trip updates, POD uploads
    Medium = 3,      // Status updates, location updates
    Low = 4          // Analytics, preferences
}
