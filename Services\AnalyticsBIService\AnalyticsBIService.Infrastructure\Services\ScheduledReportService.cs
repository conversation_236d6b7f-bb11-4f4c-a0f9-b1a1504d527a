using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Enums;
using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Diagnostics;

namespace AnalyticsBIService.Infrastructure.Services;

/// <summary>
/// Implementation of scheduled report service
/// </summary>
public class ScheduledReportService : IScheduledReportService
{
    private readonly AnalyticsBIDbContext _context;
    private readonly ICacheService _cacheService;
    private readonly ICustomReportBuilderService _reportBuilderService;
    private readonly IDataExportService _exportService;
    private readonly ILogger<ScheduledReportService> _logger;

    public ScheduledReportService(
        AnalyticsBIDbContext context,
        ICacheService cacheService,
        ICustomReportBuilderService reportBuilderService,
        IDataExportService exportService,
        ILogger<ScheduledReportService> logger)
    {
        _context = context;
        _cacheService = cacheService;
        _reportBuilderService = reportBuilderService;
        _exportService = exportService;
        _logger = logger;
    }

    public async Task<ScheduledReportDto> CreateScheduledReportAsync(CreateScheduledReportRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating scheduled report '{Name}' for user {UserId}", request.Name, request.UserId);

            var scheduledReport = new AnalyticsBIService.Domain.Entities.ScheduledReport(
                request.Name,
                request.Description,
                JsonSerializer.Serialize(request.ReportConfiguration),
                JsonSerializer.Serialize(request.ScheduleConfiguration),
                JsonSerializer.Serialize(request.DeliveryConfiguration),
                request.UserId,
                request.UserType);

            // Set next run date
            scheduledReport.UpdateNextRunDate(CalculateNextRunDate(request.ScheduleConfiguration) ?? DateTime.UtcNow.AddDays(1));

            _context.ScheduledReports.Add(scheduledReport);
            await _context.SaveChangesAsync(cancellationToken);

            var result = MapToDto(scheduledReport);

            // Cache the scheduled report
            var cacheKey = $"scheduled:report:{scheduledReport.Id}";
            await _cacheService.SetAsync(cacheKey, result, CacheExpiration.LongTerm, cancellationToken);

            _logger.LogInformation("Scheduled report created with ID {ScheduleId}", scheduledReport.Id);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating scheduled report");
            throw;
        }
    }

    public async Task<ScheduledReportDto?> GetScheduledReportAsync(Guid scheduleId, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"scheduled:report:{scheduleId}";
            var cachedResult = await _cacheService.GetAsync<ScheduledReportDto>(cacheKey, cancellationToken);
            if (cachedResult != null)
                return cachedResult;

            var scheduledReport = await _context.ScheduledReports
                .FirstOrDefaultAsync(sr => sr.Id == scheduleId, cancellationToken);

            if (scheduledReport == null)
                return null;

            var result = MapToDto(scheduledReport);
            await _cacheService.SetAsync(cacheKey, result, CacheExpiration.LongTerm, cancellationToken);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting scheduled report {ScheduleId}", scheduleId);
            return null;
        }
    }

    public async Task<List<ScheduledReportDto>> GetUserScheduledReportsAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var scheduledReports = await _context.ScheduledReports
                .Where(sr => sr.CreatedBy == userId)
                .OrderByDescending(sr => sr.UpdatedAt)
                .ToListAsync(cancellationToken);

            return scheduledReports.Select(MapToDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user scheduled reports for user {UserId}", userId);
            return new List<ScheduledReportDto>();
        }
    }

    public async Task<bool> UpdateScheduledReportAsync(Guid scheduleId, UpdateScheduledReportRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var scheduledReport = await _context.ScheduledReports
                .FirstOrDefaultAsync(sr => sr.Id == scheduleId, cancellationToken);

            if (scheduledReport == null)
                return false;

            if (!string.IsNullOrEmpty(request.Name))
                scheduledReport.UpdateName(request.Name);

            if (!string.IsNullOrEmpty(request.Description))
                scheduledReport.UpdateDescription(request.Description);

            if (request.ReportConfiguration != null)
                scheduledReport.UpdateReportConfiguration(JsonSerializer.Serialize(request.ReportConfiguration));

            if (request.ScheduleConfiguration != null)
            {
                var nextRunDate = CalculateNextRunDate(request.ScheduleConfiguration);
                scheduledReport.UpdateScheduleConfiguration(JsonSerializer.Serialize(request.ScheduleConfiguration), nextRunDate);
            }

            if (request.DeliveryConfiguration != null)
                scheduledReport.UpdateDeliveryConfiguration(JsonSerializer.Serialize(request.DeliveryConfiguration));

            if (request.IsActive.HasValue)
                scheduledReport.SetActiveStatus(request.IsActive.Value);

            await _context.SaveChangesAsync(cancellationToken);

            // Clear cache
            var cacheKey = $"scheduled:report:{scheduleId}";
            await _cacheService.RemoveAsync(cacheKey, cancellationToken);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating scheduled report");
            return false;
        }
    }

    public async Task<bool> DeleteScheduledReportAsync(Guid scheduleId, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var scheduledReport = await _context.ScheduledReports
                .FirstOrDefaultAsync(sr => sr.Id == scheduleId && sr.CreatedBy == userId, cancellationToken);

            if (scheduledReport == null)
                return false;

            _context.ScheduledReports.Remove(scheduledReport);
            await _context.SaveChangesAsync(cancellationToken);

            // Clear cache
            var cacheKey = $"scheduled:report:{scheduleId}";
            await _cacheService.RemoveAsync(cacheKey, cancellationToken);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting scheduled report");
            return false;
        }
    }

    public async Task<ReportExecutionResultDto> ExecuteScheduledReportAsync(Guid scheduleId, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var executionId = Guid.NewGuid();

        try
        {
            _logger.LogInformation("Executing scheduled report {ScheduleId} with execution ID {ExecutionId}", scheduleId, executionId);

            var scheduledReport = await GetScheduledReportAsync(scheduleId, cancellationToken);
            if (scheduledReport == null)
                throw new InvalidOperationException($"Scheduled report {scheduleId} not found");

            // Create execution record
            var execution = AnalyticsBIService.Domain.Entities.ReportExecution.Create(
                reportId: scheduleId,
                templateId: null,
                userId: scheduledReport.CreatedBy,
                parameters: System.Text.Json.JsonSerializer.Serialize(scheduledReport.ReportConfiguration),
                outputFormat: ExportFormat.PDF,
                isManualExecution: false,
                reportName: scheduledReport.Name,
                scheduleId: scheduleId,
                triggerType: "Scheduled");

            _context.ReportExecutions.Add(execution);
            await _context.SaveChangesAsync(cancellationToken);

            try
            {
                // Generate report
                var reportResult = await GenerateReportAsync(scheduledReport.ReportConfiguration, cancellationToken);

                // Deliver report
                var deliveryResults = await DeliverReportAsync(scheduledReport.DeliveryConfiguration, reportResult, cancellationToken);

                stopwatch.Stop();

                // Update execution record using domain methods
                var deliverySuccessful = deliveryResults.All(d => d.IsSuccessful);
                execution.MarkCompleted(
                    reportResult.FilePath,
                    reportResult.FileSize,
                    reportResult.RecordCount,
                    null,
                    deliverySuccessful);

                await _context.SaveChangesAsync(cancellationToken);

                // Update scheduled report last execution
                await UpdateScheduledReportLastExecutionAsync(scheduleId, execution.Status, cancellationToken);

                return new ReportExecutionResultDto
                {
                    ExecutionId = executionId,
                    ScheduleId = scheduleId,
                    Status = execution.Status,
                    StartedAt = execution.StartedAt ?? DateTime.UtcNow,
                    CompletedAt = execution.CompletedAt ?? DateTime.UtcNow,
                    Duration = execution.Duration,
                    ReportFilePath = execution.ReportFilePath,
                    ReportFileSize = execution.ReportFileSize ?? 0,
                    RecordCount = execution.RecordCount ?? 0,
                    DeliverySuccessful = execution.DeliverySuccessful ?? false,
                    DeliveryResults = deliveryResults,
                    Metrics = CalculateExecutionMetrics(stopwatch.Elapsed, reportResult.RecordCount)
                };
            }
            catch (Exception ex)
            {
                stopwatch.Stop();

                // Update execution record with error using domain method
                execution.MarkFailed(ex.Message);
                await _context.SaveChangesAsync(cancellationToken);

                await UpdateScheduledReportLastExecutionAsync(scheduleId, execution.Status, cancellationToken);

                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing scheduled report {ScheduleId}", scheduleId);
            throw;
        }
    }

    public async Task<List<ReportExecutionHistoryDto>> GetExecutionHistoryAsync(Guid scheduleId, CancellationToken cancellationToken = default)
    {
        try
        {
            var executions = await _context.ReportExecutions
                .Where(e => e.ScheduleId == scheduleId)
                .OrderByDescending(e => e.StartedAt)
                .Take(50)
                .ToListAsync(cancellationToken);

            return executions.Select(e => new ReportExecutionHistoryDto
            {
                ExecutionId = e.Id,
                StartedAt = e.StartedAt ?? DateTime.UtcNow,
                CompletedAt = e.CompletedAt,
                Status = e.Status,
                Duration = e.Duration,
                RecordCount = e.RecordCount ?? 0,
                DeliverySuccessful = e.DeliverySuccessful ?? false,
                ErrorMessage = e.ErrorMessage,
                TriggerType = e.TriggerType ?? "Manual"
            }).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting execution history for schedule {ScheduleId}", scheduleId);
            return new List<ReportExecutionHistoryDto>();
        }
    }

    public async Task<List<ScheduledReportDto>> GetPendingReportsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var pendingReports = await _context.ScheduledReports
                .Where(sr => sr.IsActive && sr.NextRunDate <= DateTime.UtcNow)
                .ToListAsync(cancellationToken);

            return pendingReports.Select(MapToDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting pending reports");
            return new List<ScheduledReportDto>();
        }
    }

    public async Task ProcessScheduledReportsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Processing scheduled reports");

            var pendingReports = await GetPendingReportsAsync(cancellationToken);

            foreach (var report in pendingReports)
            {
                try
                {
                    await ExecuteScheduledReportAsync(report.Id, cancellationToken);

                    // Update next run date
                    var scheduleConfig = JsonSerializer.Deserialize<ScheduleConfigurationDto>(
                        await _context.ScheduledReports
                            .Where(sr => sr.Id == report.Id)
                            .Select(sr => sr.ScheduleConfiguration)
                            .FirstOrDefaultAsync(cancellationToken) ?? "{}") ?? new ScheduleConfigurationDto();

                    var nextRunDate = CalculateNextRunDate(scheduleConfig);

                    var scheduledReport = await _context.ScheduledReports
                        .FirstOrDefaultAsync(sr => sr.Id == report.Id, cancellationToken);

                    if (scheduledReport != null)
                    {
                        scheduledReport.UpdateNextRunDate(nextRunDate ?? DateTime.UtcNow.AddDays(1));
                        await _context.SaveChangesAsync(cancellationToken);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing scheduled report {ReportId}", report.Id);
                }
            }

            _logger.LogInformation("Processed {Count} scheduled reports", pendingReports.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing scheduled reports");
        }
    }

    public async Task<List<ReportTemplateDto>> GetReportTemplatesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return GetBuiltInReportTemplates();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting report templates");
            return new List<ReportTemplateDto>();
        }
    }

    public async Task<List<DeliveryMethodDto>> GetDeliveryMethodsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return GetBuiltInDeliveryMethods();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting delivery methods");
            return new List<DeliveryMethodDto>();
        }
    }

    public async Task<DeliveryTestResult> TestDeliveryConfigurationAsync(DeliveryConfigurationDto configuration, CancellationToken cancellationToken = default)
    {
        try
        {
            var stopwatch = Stopwatch.StartNew();

            // Simulate delivery test
            await Task.Delay(500, cancellationToken);

            stopwatch.Stop();

            var isSuccessful = configuration.Recipients.Any() && !string.IsNullOrEmpty(configuration.Method);

            return new DeliveryTestResult
            {
                IsSuccessful = isSuccessful,
                ErrorMessage = isSuccessful ? null : "Invalid delivery configuration",
                ResponseTime = stopwatch.Elapsed,
                TestResults = new Dictionary<string, object>
                {
                    { "Method", configuration.Method },
                    { "RecipientCount", configuration.Recipients.Count },
                    { "HasSubject", !string.IsNullOrEmpty(configuration.Subject) }
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error testing delivery configuration");
            return new DeliveryTestResult
            {
                IsSuccessful = false,
                ErrorMessage = ex.Message,
                ResponseTime = TimeSpan.Zero
            };
        }
    }

    public async Task<ScheduleStatisticsDto> GetScheduleStatisticsAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var schedules = await _context.ScheduledReports
                .Where(sr => sr.CreatedBy == userId)
                .ToListAsync(cancellationToken);

            var scheduleIds = schedules.Select(s => s.Id).ToList();
            var executions = await _context.ReportExecutions
                .Where(e => e.ScheduleId.HasValue && scheduleIds.Contains(e.ScheduleId.Value))
                .ToListAsync(cancellationToken);

            var totalSchedules = schedules.Count;
            var activeSchedules = schedules.Count(s => s.IsActive);
            var inactiveSchedules = totalSchedules - activeSchedules;

            var totalExecutions = executions.Count;
            var successfulExecutions = executions.Count(e => e.Status == "Completed");
            var failedExecutions = executions.Count(e => e.Status == "Failed");
            var successRate = totalExecutions > 0 ? (decimal)successfulExecutions / totalExecutions * 100 : 0;

            var frequencyStats = schedules
                .GroupBy(s => GetScheduleTypeFromConfiguration(s.ScheduleConfiguration))
                .Select(g => new ScheduleFrequencyStatsDto
                {
                    ScheduleType = g.Key,
                    Count = g.Count(),
                    Percentage = totalSchedules > 0 ? Math.Round((decimal)g.Count() / totalSchedules * 100, 2) : 0
                }).ToList();

            var deliveryStats = schedules
                .GroupBy(s => GetDeliveryMethodFromConfiguration(s.DeliveryConfiguration))
                .Select(g => new DeliveryMethodStatsDto
                {
                    Method = g.Key,
                    Count = g.Count(),
                    SuccessRate = 95.5m // Simplified calculation
                }).ToList();

            return new ScheduleStatisticsDto
            {
                TotalSchedules = totalSchedules,
                ActiveSchedules = activeSchedules,
                InactiveSchedules = inactiveSchedules,
                TotalExecutions = totalExecutions,
                SuccessfulExecutions = successfulExecutions,
                FailedExecutions = failedExecutions,
                SuccessRate = Math.Round(successRate, 2),
                LastExecution = executions.OrderByDescending(e => e.StartedAt).FirstOrDefault()?.StartedAt,
                NextExecution = schedules.Where(s => s.IsActive && s.NextRunDate.HasValue)
                    .OrderBy(s => s.NextRunDate).FirstOrDefault()?.NextRunDate,
                FrequencyStats = frequencyStats,
                DeliveryStats = deliveryStats
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting schedule statistics");
            return new ScheduleStatisticsDto();
        }
    }

    // Helper methods
    private ScheduledReportDto MapToDto(ScheduledReport scheduledReport)
    {
        var reportConfig = string.IsNullOrEmpty(scheduledReport.ReportConfiguration)
            ? new ReportConfigurationDto()
            : JsonSerializer.Deserialize<ReportConfigurationDto>(scheduledReport.ReportConfiguration) ?? new ReportConfigurationDto();

        var scheduleConfig = string.IsNullOrEmpty(scheduledReport.ScheduleConfiguration)
            ? new ScheduleConfigurationDto()
            : JsonSerializer.Deserialize<ScheduleConfigurationDto>(scheduledReport.ScheduleConfiguration) ?? new ScheduleConfigurationDto();

        var deliveryConfig = string.IsNullOrEmpty(scheduledReport.DeliveryConfiguration)
            ? new DeliveryConfigurationDto()
            : JsonSerializer.Deserialize<DeliveryConfigurationDto>(scheduledReport.DeliveryConfiguration) ?? new DeliveryConfigurationDto();

        return new ScheduledReportDto
        {
            Id = scheduledReport.Id,
            Name = scheduledReport.Name,
            Description = scheduledReport.Description,
            ReportConfiguration = reportConfig,
            ScheduleConfiguration = scheduleConfig,
            DeliveryConfiguration = deliveryConfig,
            Status = scheduledReport.Status,
            CreatedAt = scheduledReport.CreatedAt,
            UpdatedAt = scheduledReport.UpdatedAt ?? DateTime.UtcNow,
            CreatedBy = scheduledReport.CreatedBy,
            UserType = scheduledReport.UserType,
            IsActive = scheduledReport.IsActive,
            NextRunDate = scheduledReport.NextRunDate,
            LastRunDate = scheduledReport.LastRunDate,
            LastRunStatus = scheduledReport.LastRunStatus,
            ExecutionCount = scheduledReport.ExecutionCount
        };
    }

    private DateTime? CalculateNextRunDate(ScheduleConfigurationDto scheduleConfig)
    {
        var now = DateTime.UtcNow;

        return scheduleConfig.ScheduleType.ToUpper() switch
        {
            "DAILY" => now.Date.AddDays(1).AddHours(scheduleConfig.Hour ?? 9).AddMinutes(scheduleConfig.Minute ?? 0),
            "WEEKLY" => CalculateNextWeeklyRun(now, scheduleConfig),
            "MONTHLY" => CalculateNextMonthlyRun(now, scheduleConfig),
            "HOURLY" => now.AddHours(1),
            _ => now.Date.AddDays(1).AddHours(9) // Default to daily at 9 AM
        };
    }

    private DateTime CalculateNextWeeklyRun(DateTime now, ScheduleConfigurationDto scheduleConfig)
    {
        var daysOfWeek = scheduleConfig.DaysOfWeek.Any() ? scheduleConfig.DaysOfWeek : new List<int> { 1 }; // Default to Monday
        var hour = scheduleConfig.Hour ?? 9;
        var minute = scheduleConfig.Minute ?? 0;

        var nextRun = now.Date.AddDays(1);
        while (!daysOfWeek.Contains((int)nextRun.DayOfWeek))
        {
            nextRun = nextRun.AddDays(1);
        }

        return nextRun.AddHours(hour).AddMinutes(minute);
    }

    private DateTime CalculateNextMonthlyRun(DateTime now, ScheduleConfigurationDto scheduleConfig)
    {
        var daysOfMonth = scheduleConfig.DaysOfMonth.Any() ? scheduleConfig.DaysOfMonth : new List<int> { 1 }; // Default to 1st
        var hour = scheduleConfig.Hour ?? 9;
        var minute = scheduleConfig.Minute ?? 0;

        var nextMonth = now.AddMonths(1);
        var day = daysOfMonth.First();
        var daysInMonth = DateTime.DaysInMonth(nextMonth.Year, nextMonth.Month);

        if (day > daysInMonth)
            day = daysInMonth;

        return new DateTime(nextMonth.Year, nextMonth.Month, day, hour, minute, 0);
    }

    private async Task<ReportGenerationResult> GenerateReportAsync(ReportConfigurationDto reportConfig, CancellationToken cancellationToken)
    {
        // This would integrate with the actual report generation service
        // For now, simulating report generation
        await Task.Delay(2000, cancellationToken); // Simulate processing time

        var random = new Random();
        var recordCount = random.Next(100, 1000);
        var fileSize = random.Next(50000, 500000); // 50KB to 500KB

        return new ReportGenerationResult
        {
            FilePath = Path.Combine(Path.GetTempPath(), $"report_{Guid.NewGuid()}.{reportConfig.Formatting.Format.ToLower()}"),
            FileSize = fileSize,
            RecordCount = recordCount,
            Format = reportConfig.Formatting.Format
        };
    }

    private async Task<List<DeliveryResultDto>> DeliverReportAsync(DeliveryConfigurationDto deliveryConfig, ReportGenerationResult reportResult, CancellationToken cancellationToken)
    {
        var results = new List<DeliveryResultDto>();

        foreach (var recipient in deliveryConfig.Recipients)
        {
            try
            {
                // Simulate delivery
                await Task.Delay(500, cancellationToken);

                results.Add(new DeliveryResultDto
                {
                    Method = deliveryConfig.Method,
                    Recipient = recipient,
                    IsSuccessful = true,
                    DeliveredAt = DateTime.UtcNow,
                    Metadata = new Dictionary<string, object>
                    {
                        { "FileSize", reportResult.FileSize },
                        { "Format", reportResult.Format },
                        { "DeliveryMethod", deliveryConfig.Method }
                    }
                });
            }
            catch (Exception ex)
            {
                results.Add(new DeliveryResultDto
                {
                    Method = deliveryConfig.Method,
                    Recipient = recipient,
                    IsSuccessful = false,
                    DeliveredAt = DateTime.UtcNow,
                    ErrorMessage = ex.Message
                });
            }
        }

        return results;
    }

    private Dictionary<string, object> CalculateExecutionMetrics(TimeSpan duration, int recordCount)
    {
        return new Dictionary<string, object>
        {
            { "ExecutionTimeSeconds", Math.Round(duration.TotalSeconds, 2) },
            { "RecordsPerSecond", duration.TotalSeconds > 0 ? Math.Round(recordCount / duration.TotalSeconds, 2) : 0 },
            { "MemoryUsageMB", 25.5 }, // Simulated
            { "CpuUsagePercent", 15.2 } // Simulated
        };
    }

    private async Task UpdateScheduledReportLastExecutionAsync(Guid scheduleId, string status, CancellationToken cancellationToken)
    {
        var scheduledReport = await _context.ScheduledReports
            .FirstOrDefaultAsync(sr => sr.Id == scheduleId, cancellationToken);

        if (scheduledReport != null)
        {
            scheduledReport.RecordExecution(status);
            await _context.SaveChangesAsync(cancellationToken);
        }
    }

    private string GetScheduleTypeFromConfiguration(string configurationJson)
    {
        try
        {
            var config = JsonSerializer.Deserialize<ScheduleConfigurationDto>(configurationJson);
            return config?.ScheduleType ?? "Unknown";
        }
        catch
        {
            return "Unknown";
        }
    }

    private string GetDeliveryMethodFromConfiguration(string configurationJson)
    {
        try
        {
            var config = JsonSerializer.Deserialize<DeliveryConfigurationDto>(configurationJson);
            return config?.Method ?? "Unknown";
        }
        catch
        {
            return "Unknown";
        }
    }

    private List<ReportTemplateDto> GetBuiltInReportTemplates()
    {
        return new List<ReportTemplateDto>
        {
            new()
            {
                Id = Guid.NewGuid(),
                Name = "Daily Analytics Summary",
                Description = "Daily summary of key analytics metrics",
                Category = "Analytics",
                DefaultConfiguration = new ReportConfigurationDto
                {
                    ReportType = "Summary",
                    DataSource = "analytics_events",
                    Fields = new List<AnalyticsBIService.Application.DTOs.ReportFieldDto>
                    {
                        new() { FieldName = "event_name", DataType = "string", IsVisible = true },
                        new() { FieldName = "user_count", DataType = "int", IsVisible = true },
                        new() { FieldName = "conversion_rate", DataType = "decimal", IsVisible = true },
                        new() { FieldName = "timestamp", DataType = "datetime", IsVisible = true }
                    },
                    Formatting = new ReportFormattingDto { Format = "PDF", Theme = "Professional" }
                },
                RequiredParameters = new List<string> { "date_range" },
                IsCustomizable = true
            },
            new()
            {
                Id = Guid.NewGuid(),
                Name = "Weekly Performance Report",
                Description = "Weekly performance metrics and trends",
                Category = "Performance",
                DefaultConfiguration = new ReportConfigurationDto
                {
                    ReportType = "Trend",
                    DataSource = "metrics",
                    Fields = new List<AnalyticsBIService.Application.DTOs.ReportFieldDto>
                    {
                        new() { FieldName = "metric_name", DataType = "string", IsVisible = true },
                        new() { FieldName = "value", DataType = "decimal", IsVisible = true },
                        new() { FieldName = "trend", DataType = "string", IsVisible = true },
                        new() { FieldName = "week", DataType = "datetime", IsVisible = true }
                    },
                    Visualizations = new List<ReportVisualizationConfigDto>
                    {
                        new() { Type = "LineChart", Title = "Performance Trends", Order = 1, DataFields = new List<string> { "metric_name", "value" } }
                    },
                    Formatting = new ReportFormattingDto { Format = "PDF", Theme = "Modern" }
                },
                RequiredParameters = new List<string> { "week_range" },
                IsCustomizable = true
            },
            new()
            {
                Id = Guid.NewGuid(),
                Name = "Monthly Executive Dashboard",
                Description = "Executive summary with key business metrics",
                Category = "Executive",
                DefaultConfiguration = new ReportConfigurationDto
                {
                    ReportType = "Dashboard",
                    DataSource = "business_metrics",
                    Fields = new List<AnalyticsBIService.Application.DTOs.ReportFieldDto>
                    {
                        new() { FieldName = "revenue", DataType = "decimal", IsVisible = true },
                        new() { FieldName = "users", DataType = "int", IsVisible = true },
                        new() { FieldName = "conversion_rate", DataType = "decimal", IsVisible = true },
                        new() { FieldName = "churn_rate", DataType = "decimal", IsVisible = true }
                    },
                    Visualizations = new List<ReportVisualizationConfigDto>
                    {
                        new() { Type = "BarChart", Title = "Revenue Growth", Order = 1, DataFields = new List<string> { "revenue" } },
                        new() { Type = "PieChart", Title = "User Distribution", Order = 2, DataFields = new List<string> { "users" } }
                    },
                    Formatting = new ReportFormattingDto { Format = "PDF", Theme = "Executive" }
                },
                RequiredParameters = new List<string> { "month" },
                IsCustomizable = false
            }
        };
    }

    private List<DeliveryMethodDto> GetBuiltInDeliveryMethods()
    {
        return new List<DeliveryMethodDto>
        {
            new()
            {
                Method = "Email",
                DisplayName = "Email Delivery",
                Description = "Send reports via email",
                RequiredParameters = new List<DeliveryParameterDto>
                {
                    new() { Name = "Recipients", Type = "array", Description = "Email addresses", IsRequired = true },
                    new() { Name = "Subject", Type = "string", Description = "Email subject", IsRequired = true }
                },
                OptionalParameters = new List<DeliveryParameterDto>
                {
                    new() { Name = "Message", Type = "string", Description = "Email message body" },
                    new() { Name = "CcRecipients", Type = "array", Description = "CC email addresses" }
                },
                SupportedFormats = new List<string> { "PDF", "Excel", "CSV" },
                IsEnabled = true
            },
            new()
            {
                Method = "Slack",
                DisplayName = "Slack Integration",
                Description = "Send reports to Slack channels",
                RequiredParameters = new List<DeliveryParameterDto>
                {
                    new() { Name = "Channel", Type = "string", Description = "Slack channel", IsRequired = true },
                    new() { Name = "WebhookUrl", Type = "string", Description = "Slack webhook URL", IsRequired = true }
                },
                OptionalParameters = new List<DeliveryParameterDto>
                {
                    new() { Name = "Message", Type = "string", Description = "Custom message" }
                },
                SupportedFormats = new List<string> { "PDF", "CSV" },
                IsEnabled = true
            },
            new()
            {
                Method = "FTP",
                DisplayName = "FTP Upload",
                Description = "Upload reports to FTP server",
                RequiredParameters = new List<DeliveryParameterDto>
                {
                    new() { Name = "Server", Type = "string", Description = "FTP server address", IsRequired = true },
                    new() { Name = "Username", Type = "string", Description = "FTP username", IsRequired = true },
                    new() { Name = "Password", Type = "string", Description = "FTP password", IsRequired = true }
                },
                OptionalParameters = new List<DeliveryParameterDto>
                {
                    new() { Name = "Directory", Type = "string", Description = "Target directory", DefaultValue = "/" }
                },
                SupportedFormats = new List<string> { "PDF", "Excel", "CSV", "JSON" },
                IsEnabled = true
            },
            new()
            {
                Method = "WebHook",
                DisplayName = "Web Hook",
                Description = "Send reports via HTTP webhook",
                RequiredParameters = new List<DeliveryParameterDto>
                {
                    new() { Name = "Url", Type = "string", Description = "Webhook URL", IsRequired = true }
                },
                OptionalParameters = new List<DeliveryParameterDto>
                {
                    new() { Name = "Headers", Type = "object", Description = "HTTP headers" },
                    new() { Name = "Method", Type = "string", Description = "HTTP method", DefaultValue = "POST" }
                },
                SupportedFormats = new List<string> { "JSON", "CSV" },
                IsEnabled = true
            }
        };
    }
}

// Helper class for report generation
public class ReportGenerationResult
{
    public string FilePath { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public int RecordCount { get; set; }
    public string Format { get; set; } = string.Empty;
}




