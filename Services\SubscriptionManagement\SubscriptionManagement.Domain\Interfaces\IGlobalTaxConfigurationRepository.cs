using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Domain.Interfaces
{
    public interface IGlobalTaxConfigurationRepository
    {
        Task<GlobalTaxConfiguration?> GetByIdAsync(Guid id);
        Task<List<GlobalTaxConfiguration>> GetAllAsync();
        Task<List<GlobalTaxConfiguration>> GetActiveAsync();
        Task<List<GlobalTaxConfiguration>> GetByRegionAsync(string region);
        Task<List<GlobalTaxConfiguration>> GetByTaxTypeAsync(TaxType taxType);
        Task<GlobalTaxConfiguration?> GetByTaxTypeAndRegionAsync(TaxType taxType, string region);
        Task<List<GlobalTaxConfiguration>> GetEffectiveConfigurationsAsync(string region, DateTime? date = null);
        Task<GlobalTaxConfiguration> AddAsync(GlobalTaxConfiguration globalTaxConfiguration);
        Task<GlobalTaxConfiguration> UpdateAsync(GlobalTaxConfiguration globalTaxConfiguration);
        Task DeleteAsync(Guid id);
        Task<bool> ExistsAsync(Guid id);
        Task<bool> ExistsByTaxTypeAndRegionAsync(TaxType taxType, string region);
        Task<List<GlobalTaxConfiguration>> GetByPriorityRangeAsync(int minPriority, int maxPriority);
        Task<int> GetMaxPriorityAsync();
        Task<List<GlobalTaxConfiguration>> SearchAsync(string searchTerm, int page = 1, int pageSize = 10);
        Task<int> GetCountAsync();
    }
}
