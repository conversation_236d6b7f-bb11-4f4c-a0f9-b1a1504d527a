# Network & Fleet Management Service Test Runner

Write-Host "🧪 Running Network & Fleet Management Service Tests..." -ForegroundColor Green

# Check if .NET 8 is installed
Write-Host "Checking .NET 8 installation..." -ForegroundColor Yellow
$dotnetVersion = dotnet --version
if ($dotnetVersion -notlike "8.*") {
    Write-Host "❌ .NET 8 is required. Please install .NET 8 SDK." -ForegroundColor Red
    exit 1
}
Write-Host "✅ .NET 8 is installed: $dotnetVersion" -ForegroundColor Green

# Restore packages
Write-Host "Restoring NuGet packages..." -ForegroundColor Yellow
dotnet restore
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ NuGet packages restored" -ForegroundColor Green
} else {
    Write-Host "❌ Failed to restore NuGet packages" -ForegroundColor Red
    exit 1
}

# Build the solution
Write-Host "Building the solution..." -ForegroundColor Yellow
dotnet build --no-restore
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Solution built successfully" -ForegroundColor Green
} else {
    Write-Host "❌ Build failed" -ForegroundColor Red
    exit 1
}

# Run unit tests
Write-Host "" -ForegroundColor White
Write-Host "🔬 Running Unit Tests..." -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan

dotnet test NetworkFleetManagement.Tests --no-build --verbosity normal --logger "console;verbosity=detailed"
$unitTestResult = $LASTEXITCODE

if ($unitTestResult -eq 0) {
    Write-Host "✅ Unit tests passed" -ForegroundColor Green
} else {
    Write-Host "❌ Unit tests failed" -ForegroundColor Red
}

# Check if Docker is available for integration tests
Write-Host "" -ForegroundColor White
Write-Host "🔗 Running Integration Tests..." -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan

try {
    docker version | Out-Null
    Write-Host "✅ Docker is available for integration tests" -ForegroundColor Green
    
    # Run integration tests
    dotnet test NetworkFleetManagement.IntegrationTests --no-build --verbosity normal --logger "console;verbosity=detailed"
    $integrationTestResult = $LASTEXITCODE
    
    if ($integrationTestResult -eq 0) {
        Write-Host "✅ Integration tests passed" -ForegroundColor Green
    } else {
        Write-Host "❌ Integration tests failed" -ForegroundColor Red
    }
} catch {
    Write-Host "⚠️ Docker not available, skipping integration tests" -ForegroundColor Yellow
    $integrationTestResult = 0
}

# Generate test coverage report (if coverlet is available)
Write-Host "" -ForegroundColor White
Write-Host "📊 Generating Test Coverage Report..." -ForegroundColor Cyan
Write-Host "=====================================" -ForegroundColor Cyan

try {
    dotnet test NetworkFleetManagement.Tests --no-build --collect:"XPlat Code Coverage" --results-directory:"./TestResults"
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Test coverage report generated in ./TestResults" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Test coverage report generation failed" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️ Test coverage tools not available" -ForegroundColor Yellow
}

# Summary
Write-Host "" -ForegroundColor White
Write-Host "📋 Test Summary" -ForegroundColor Magenta
Write-Host "===============" -ForegroundColor Magenta

if ($unitTestResult -eq 0) {
    Write-Host "✅ Unit Tests: PASSED" -ForegroundColor Green
} else {
    Write-Host "❌ Unit Tests: FAILED" -ForegroundColor Red
}

if ($integrationTestResult -eq 0) {
    Write-Host "✅ Integration Tests: PASSED" -ForegroundColor Green
} else {
    Write-Host "❌ Integration Tests: FAILED" -ForegroundColor Red
}

# Overall result
if ($unitTestResult -eq 0 -and $integrationTestResult -eq 0) {
    Write-Host "" -ForegroundColor White
    Write-Host "🎉 All tests passed successfully!" -ForegroundColor Green
    exit 0
} else {
    Write-Host "" -ForegroundColor White
    Write-Host "💥 Some tests failed. Please review the output above." -ForegroundColor Red
    exit 1
}
