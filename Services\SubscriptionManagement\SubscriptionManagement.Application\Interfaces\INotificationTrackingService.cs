using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Application.Interfaces
{
    public interface INotificationTrackingService
    {
        /// <summary>
        /// Track a notification that was sent
        /// </summary>
        Task<NotificationHistory> TrackNotificationAsync(
            Guid? subscriptionId,
            Guid? userId,
            NotificationType type,
            string channel,
            string subject,
            string body,
            Guid triggeredByUserId,
            DateTime? scheduledAt = null,
            Dictionary<string, string>? metadata = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Update notification status when it's sent
        /// </summary>
        Task UpdateNotificationSentAsync(
            Guid notificationId,
            string? externalNotificationId = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Update notification status when it's delivered
        /// </summary>
        Task UpdateNotificationDeliveredAsync(
            Guid notificationId,
            Dictionary<string, string>? deliveryMetadata = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Update notification status when it's read
        /// </summary>
        Task UpdateNotificationReadAsync(
            Guid notificationId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Update notification status when it's clicked
        /// </summary>
        Task UpdateNotificationClickedAsync(
            Guid notificationId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Update notification status when it fails
        /// </summary>
        Task UpdateNotificationFailedAsync(
            Guid notificationId,
            string errorMessage,
            string? errorCode = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Cancel a scheduled notification
        /// </summary>
        Task CancelNotificationAsync(
            Guid notificationId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get notification analytics
        /// </summary>
        Task<NotificationAnalytics> GetNotificationAnalyticsAsync(
            DateTime? fromDate = null,
            DateTime? toDate = null,
            string? channel = null,
            NotificationType? type = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get conversion analytics (notifications to subscription renewals)
        /// </summary>
        Task<ConversionAnalytics> GetConversionAnalyticsAsync(
            DateTime? fromDate = null,
            DateTime? toDate = null,
            NotificationType? type = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get notifications that need to be retried
        /// </summary>
        Task<List<NotificationHistory>> GetNotificationsForRetryAsync(
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get scheduled notifications that are ready to be sent
        /// </summary>
        Task<List<NotificationHistory>> GetScheduledNotificationsReadyToSendAsync(
            CancellationToken cancellationToken = default);
    }

    public class NotificationAnalytics
    {
        public int TotalNotifications { get; set; }
        public Dictionary<string, int> NotificationsByStatus { get; set; } = new();
        public Dictionary<string, int> NotificationsByChannel { get; set; } = new();
        public Dictionary<NotificationType, int> NotificationsByType { get; set; } = new();
        public double DeliveryRate { get; set; }
        public double ReadRate { get; set; }
        public double ClickRate { get; set; }
        public TimeSpan AverageDeliveryTime { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public string? Channel { get; set; }
        public NotificationType? Type { get; set; }
    }

    public class ConversionAnalytics
    {
        public int TotalReminders { get; set; }
        public int SubscriptionsRenewed { get; set; }
        public double ConversionRate { get; set; }
        public Dictionary<string, ConversionByChannel> ConversionsByChannel { get; set; } = new();
        public TimeSpan AverageTimeToConversion { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public NotificationType? Type { get; set; }
    }

    public class ConversionByChannel
    {
        public string Channel { get; set; } = string.Empty;
        public int TotalSent { get; set; }
        public int Conversions { get; set; }
        public double ConversionRate { get; set; }
        public TimeSpan AverageTimeToConversion { get; set; }
    }
}
