using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using TripManagement.Application.Commands.UpdateTripLocation;
using TripManagement.Application.Commands.UpdateETA;
using TripManagement.Application.Commands.StartLiveTracking;
using TripManagement.Application.Commands.ProcessEnhancedLocationUpdate;
using TripManagement.Application.Queries.GetTripLocation;
using TripManagement.Application.DTOs;
using TripManagement.API.Hubs;

namespace TripManagement.API.Controllers;

[ApiController]
[Route("api/v{version:apiVersion}/realtime")]
[ApiVersion("1.0")]
[Authorize]
public class RealTimeTrackingController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly IHubContext<TripTrackingHub> _hubContext;
    private readonly ILogger<RealTimeTrackingController> _logger;

    public RealTimeTrackingController(
        IMediator mediator,
        IHubContext<TripTrackingHub> hubContext,
        ILogger<RealTimeTrackingController> logger)
    {
        _mediator = mediator;
        _hubContext = hubContext;
        _logger = logger;
    }

    /// <summary>
    /// Update trip location in real-time
    /// </summary>
    [HttpPost("trips/{tripId:guid}/location")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> UpdateLocation(
        Guid tripId,
        [FromBody] UpdateLocationRequest request)
    {
        try
        {
            var command = new UpdateTripLocationCommand
            {
                TripId = tripId,
                Latitude = request.Latitude,
                Longitude = request.Longitude,
                Altitude = request.Altitude,
                Accuracy = request.Accuracy,
                Speed = request.Speed,
                Heading = request.Heading,
                Source = request.Source,
                Timestamp = request.Timestamp ?? DateTime.UtcNow
            };

            var result = await _mediator.Send(command);

            // Broadcast location update to connected clients
            await _hubContext.Clients.Group($"Trip_{tripId}")
                .SendAsync("LocationUpdated", new
                {
                    TripId = tripId,
                    Location = new
                    {
                        request.Latitude,
                        request.Longitude,
                        request.Altitude,
                        request.Accuracy
                    },
                    request.Speed,
                    request.Heading,
                    Timestamp = command.Timestamp,
                    Source = request.Source.ToString()
                });

            return Ok(new { Success = true, LocationId = result });
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid location update for trip {TripId}", tripId);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating location for trip {TripId}", tripId);
            return StatusCode(500, "An error occurred while updating location");
        }
    }

    /// <summary>
    /// Get current trip location
    /// </summary>
    [HttpGet("trips/{tripId:guid}/location")]
    [ProducesResponseType(typeof(TripLocationDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetCurrentLocation(Guid tripId)
    {
        try
        {
            var query = new GetTripLocationQuery { TripId = tripId };
            var location = await _mediator.Send(query);

            if (location == null)
                return NotFound($"No location found for trip {tripId}");

            return Ok(location);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving location for trip {TripId}", tripId);
            return StatusCode(500, "An error occurred while retrieving location");
        }
    }

    /// <summary>
    /// Get trip location history
    /// </summary>
    [HttpGet("trips/{tripId:guid}/location/history")]
    [ProducesResponseType(typeof(List<TripLocationDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetLocationHistory(
        Guid tripId,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] int limit = 100)
    {
        try
        {
            var query = new GetTripLocationHistoryQuery
            {
                TripId = tripId,
                FromDate = fromDate,
                ToDate = toDate,
                Limit = limit
            };

            var locations = await _mediator.Send(query);
            return Ok(locations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving location history for trip {TripId}", tripId);
            return StatusCode(500, "An error occurred while retrieving location history");
        }
    }

    /// <summary>
    /// Update ETA for a trip
    /// </summary>
    [HttpPost("trips/{tripId:guid}/eta")]
    [ProducesResponseType(typeof(ETAUpdateResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> UpdateETA(
        Guid tripId,
        [FromBody] UpdateETARequest request)
    {
        try
        {
            var command = new UpdateETACommand
            {
                TripId = tripId,
                CurrentLocation = request.CurrentLocation,
                CurrentSpeed = request.CurrentSpeed,
                ConsiderTraffic = request.ConsiderTraffic,
                ConsiderWeather = request.ConsiderWeather,
                ConsiderDriverBehavior = request.ConsiderDriverBehavior
            };

            var result = await _mediator.Send(command);

            // Broadcast ETA update to connected clients
            await _hubContext.Clients.Group($"Trip_{tripId}")
                .SendAsync("ETAUpdated", new
                {
                    TripId = tripId,
                    NewETA = result.UpdatedETA,
                    PreviousETA = result.PreviousETA,
                    ConfidenceScore = result.ConfidenceScore,
                    UpdatedAt = DateTime.UtcNow
                });

            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid ETA update for trip {TripId}", tripId);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating ETA for trip {TripId}", tripId);
            return StatusCode(500, "An error occurred while updating ETA");
        }
    }

    /// <summary>
    /// Subscribe to real-time updates for multiple trips
    /// </summary>
    [HttpPost("subscribe")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> SubscribeToUpdates([FromBody] SubscribeRequest request)
    {
        try
        {
            var connectionId = HttpContext.Request.Headers["X-SignalR-ConnectionId"].FirstOrDefault();

            if (string.IsNullOrEmpty(connectionId))
                return BadRequest("SignalR connection ID is required");

            foreach (var tripId in request.TripIds)
            {
                await _hubContext.Groups.AddToGroupAsync(connectionId, $"Trip_{tripId}");
            }

            _logger.LogInformation("Client {ConnectionId} subscribed to {TripCount} trips",
                connectionId, request.TripIds.Count);

            return Ok(new { Message = $"Subscribed to {request.TripIds.Count} trips" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error subscribing to trip updates");
            return StatusCode(500, "An error occurred while subscribing to updates");
        }
    }

    /// <summary>
    /// Unsubscribe from real-time updates
    /// </summary>
    [HttpPost("unsubscribe")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> UnsubscribeFromUpdates([FromBody] SubscribeRequest request)
    {
        try
        {
            var connectionId = HttpContext.Request.Headers["X-SignalR-ConnectionId"].FirstOrDefault();

            if (string.IsNullOrEmpty(connectionId))
                return BadRequest("SignalR connection ID is required");

            foreach (var tripId in request.TripIds)
            {
                await _hubContext.Groups.RemoveFromGroupAsync(connectionId, $"Trip_{tripId}");
            }

            _logger.LogInformation("Client {ConnectionId} unsubscribed from {TripCount} trips",
                connectionId, request.TripIds.Count);

            return Ok(new { Message = $"Unsubscribed from {request.TripIds.Count} trips" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error unsubscribing from trip updates");
            return StatusCode(500, "An error occurred while unsubscribing from updates");
        }
    }

    /// <summary>
    /// Get real-time trip status
    /// </summary>
    [HttpGet("trips/{tripId:guid}/status")]
    [ProducesResponseType(typeof(RealTimeTripStatusDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetRealTimeStatus(Guid tripId)
    {
        try
        {
            var query = new GetRealTimeTripStatusQuery { TripId = tripId };
            var status = await _mediator.Send(query);

            if (status == null)
                return NotFound($"Trip {tripId} not found");

            return Ok(status);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving real-time status for trip {TripId}", tripId);
            return StatusCode(500, "An error occurred while retrieving trip status");
        }
    }

    /// <summary>
    /// Start live tracking for a trip
    /// </summary>
    [HttpPost("trips/{tripId:guid}/start-tracking")]
    [Authorize(Roles = "Driver,Admin,Carrier")]
    [ProducesResponseType(typeof(StartLiveTrackingResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> StartLiveTracking(
        Guid tripId,
        [FromBody] StartLiveTrackingRequest request)
    {
        try
        {
            var command = new StartLiveTrackingCommand
            {
                TripId = tripId,
                DriverId = request.DriverId,
                TrackingIntervalSeconds = request.TrackingIntervalSeconds,
                EnableGeofencing = request.EnableGeofencing,
                EnableETAUpdates = request.EnableETAUpdates,
                EnableSpeedMonitoring = request.EnableSpeedMonitoring,
                EnableRouteDeviation = request.EnableRouteDeviation,
                NotificationChannels = request.NotificationChannels
            };

            var result = await _mediator.Send(command);

            if (!result.Success)
                return BadRequest(result.Message);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting live tracking for trip {TripId}", tripId);
            return StatusCode(500, "An error occurred while starting live tracking");
        }
    }

    /// <summary>
    /// Process enhanced location update with geofencing, ETA, and route monitoring
    /// </summary>
    [HttpPost("trips/{tripId:guid}/enhanced-location")]
    [Authorize(Roles = "Driver,Admin,System")]
    [ProducesResponseType(typeof(ProcessEnhancedLocationUpdateResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> ProcessEnhancedLocationUpdate(
        Guid tripId,
        [FromBody] EnhancedLocationUpdateRequest request)
    {
        try
        {
            var command = new ProcessEnhancedLocationUpdateCommand
            {
                TripId = tripId,
                DriverId = request.DriverId,
                Latitude = request.Latitude,
                Longitude = request.Longitude,
                Altitude = request.Altitude,
                Accuracy = request.Accuracy,
                Speed = request.Speed,
                Heading = request.Heading,
                Source = request.Source,
                Timestamp = request.Timestamp ?? DateTime.UtcNow,
                TriggerGeofenceCheck = request.TriggerGeofenceCheck,
                UpdateETA = request.UpdateETA,
                CheckRouteDeviation = request.CheckRouteDeviation,
                MonitorSpeed = request.MonitorSpeed,
                AdditionalData = request.AdditionalData
            };

            var result = await _mediator.Send(command);

            // Broadcast enhanced location update to connected clients
            await _hubContext.Clients.Group($"Trip_{tripId}")
                .SendAsync("EnhancedLocationUpdate", new
                {
                    TripId = tripId,
                    Location = new
                    {
                        request.Latitude,
                        request.Longitude,
                        request.Altitude,
                        request.Accuracy
                    },
                    request.Speed,
                    request.Heading,
                    Timestamp = command.Timestamp,
                    Source = request.Source.ToString(),
                    GeofenceResult = result.GeofenceResult,
                    ETAResult = result.ETAResult,
                    RouteDeviationResult = result.RouteDeviationResult,
                    SpeedResult = result.SpeedResult,
                    Alerts = result.Alerts,
                    ProcessedAt = result.ProcessedAt
                });

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing enhanced location update for trip {TripId}", tripId);
            return StatusCode(500, "An error occurred while processing location update");
        }
    }
}

// Request/Response DTOs
public class UpdateLocationRequest
{
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public double? Altitude { get; set; }
    public double? Accuracy { get; set; }
    public double? Speed { get; set; }
    public double? Heading { get; set; }
    public LocationUpdateSource Source { get; set; } = LocationUpdateSource.GPS;
    public DateTime? Timestamp { get; set; }
}

public class SubscribeRequest
{
    public List<Guid> TripIds { get; set; } = new();
}

public class RealTimeTripStatusDto
{
    public Guid TripId { get; set; }
    public string TripNumber { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public TripLocationDto? CurrentLocation { get; set; }
    public DateTime? EstimatedArrival { get; set; }
    public double? ProgressPercentage { get; set; }
    public List<TripStopStatusDto> Stops { get; set; } = new();
    public DateTime LastUpdated { get; set; }
}

public class TripStopStatusDto
{
    public Guid StopId { get; set; }
    public string StopType { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime? ScheduledArrival { get; set; }
    public DateTime? EstimatedArrival { get; set; }
    public DateTime? ActualArrival { get; set; }
    public bool IsDelayed { get; set; }
}

public class StartLiveTrackingRequest
{
    public Guid DriverId { get; set; }
    public int TrackingIntervalSeconds { get; set; } = 30;
    public bool EnableGeofencing { get; set; } = true;
    public bool EnableETAUpdates { get; set; } = true;
    public bool EnableSpeedMonitoring { get; set; } = true;
    public bool EnableRouteDeviation { get; set; } = true;
    public List<string> NotificationChannels { get; set; } = new();
}

public class EnhancedLocationUpdateRequest
{
    public Guid DriverId { get; set; }
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public double? Altitude { get; set; }
    public double? Accuracy { get; set; }
    public double? Speed { get; set; }
    public double? Heading { get; set; }
    public Domain.Enums.LocationUpdateSource Source { get; set; } = Domain.Enums.LocationUpdateSource.GPS;
    public DateTime? Timestamp { get; set; }
    public bool TriggerGeofenceCheck { get; set; } = true;
    public bool UpdateETA { get; set; } = true;
    public bool CheckRouteDeviation { get; set; } = true;
    public bool MonitorSpeed { get; set; } = true;
    public Dictionary<string, object> AdditionalData { get; set; } = new();
}
