#!/bin/bash

# Network & Fleet Management Service Setup and Run Script

echo "🚀 Setting up Network & Fleet Management Service..."

# Check if .NET 8 is installed
echo "Checking .NET 8 installation..."
dotnet_version=$(dotnet --version 2>/dev/null)
if [[ ! $dotnet_version == 8.* ]]; then
    echo "❌ .NET 8 is required. Please install .NET 8 SDK."
    exit 1
fi
echo "✅ .NET 8 is installed: $dotnet_version"

# Check if Docker is running
echo "Checking Docker..."
if ! docker version >/dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker."
    exit 1
fi
echo "✅ Docker is running"

# Create network if it doesn't exist
echo "Creating TLI network..."
docker network create tli-network 2>/dev/null || true
echo "✅ TLI network ready"

# Start PostgreSQL container
echo "Starting PostgreSQL container..."
docker run -d --name networkfleet-postgres \
    --network tli-network \
    -e POSTGRES_DB=TLI_NetworkFleetManagement \
    -e POSTGRES_USER=timescale \
    -e POSTGRES_PASSWORD=timescale \
    -p 5434:5432 \
    -v networkfleet_postgres_data:/var/lib/postgresql/data \
    timescale/timescaledb:latest-pg15 2>/dev/null || true

# Wait for PostgreSQL to be ready
echo "Waiting for PostgreSQL to be ready..."
sleep 10

# Check if PostgreSQL is ready
max_attempts=30
attempt=0
while [ $attempt -lt $max_attempts ]; do
    attempt=$((attempt + 1))
    if PGPASSWORD=timescale psql -h localhost -p 5434 -U timescale -d TLI_NetworkFleetManagement -c "SELECT 1;" >/dev/null 2>&1; then
        echo "✅ PostgreSQL is ready"
        break
    fi
    
    if [ $attempt -eq $max_attempts ]; then
        echo "❌ PostgreSQL failed to start within timeout"
        exit 1
    fi
    
    echo "Waiting for PostgreSQL... (attempt $attempt/$max_attempts)"
    sleep 2
done

# Run database setup script
echo "Setting up database schema..."
PGPASSWORD=timescale psql -h localhost -p 5434 -U timescale -d TLI_NetworkFleetManagement -f database-setup.sql
if [ $? -eq 0 ]; then
    echo "✅ Database schema created"
else
    echo "⚠️ Database schema setup completed with warnings"
fi

# Start RabbitMQ container
echo "Starting RabbitMQ container..."
docker run -d --name networkfleet-rabbitmq \
    --network tli-network \
    -e RABBITMQ_DEFAULT_USER=admin \
    -e RABBITMQ_DEFAULT_PASS=admin123 \
    -p 5674:5672 \
    -p 15674:15672 \
    -v networkfleet_rabbitmq_data:/var/lib/rabbitmq \
    rabbitmq:3-management 2>/dev/null || true

# Start Redis container
echo "Starting Redis container..."
docker run -d --name networkfleet-redis \
    --network tli-network \
    -p 6381:6379 \
    -v networkfleet_redis_data:/data \
    redis:7-alpine redis-server --appendonly yes 2>/dev/null || true

echo "Waiting for services to be ready..."
sleep 5

# Restore NuGet packages
echo "Restoring NuGet packages..."
dotnet restore
if [ $? -eq 0 ]; then
    echo "✅ NuGet packages restored"
else
    echo "❌ Failed to restore NuGet packages"
    exit 1
fi

# Build the solution
echo "Building the solution..."
dotnet build --no-restore
if [ $? -eq 0 ]; then
    echo "✅ Solution built successfully"
else
    echo "❌ Build failed"
    exit 1
fi

# Run database migrations
echo "Running database migrations..."
cd NetworkFleetManagement.API
dotnet ef database update --no-build
if [ $? -eq 0 ]; then
    echo "✅ Database migrations completed"
else
    echo "⚠️ Database migrations completed with warnings"
fi
cd ..

# Run tests
echo "Running unit tests..."
dotnet test NetworkFleetManagement.Tests --no-build --verbosity minimal
if [ $? -eq 0 ]; then
    echo "✅ Unit tests passed"
else
    echo "⚠️ Some unit tests failed"
fi

# Start the API service
echo "Starting Network & Fleet Management API..."
echo "🌐 API will be available at: http://localhost:5005"
echo "📚 Swagger UI will be available at: http://localhost:5005"
echo "🐰 RabbitMQ Management UI: http://localhost:15674 (admin/admin123)"
echo "🗄️ PostgreSQL: localhost:5434 (timescale/timescale)"
echo ""
echo "Press Ctrl+C to stop the service"
echo ""

cd NetworkFleetManagement.API
export ASPNETCORE_ENVIRONMENT=Development
export ASPNETCORE_URLS=http://localhost:5005
dotnet run --no-build
