using AnalyticsBIService.Domain.Enums;
using Shared.Domain.Common;

namespace AnalyticsBIService.Application.DTOs;

/// <summary>
/// Analytics event data transfer object
/// </summary>
public class AnalyticsEventDto
{
    public Guid Id { get; set; }
    public string EventName { get; set; } = string.Empty;
    public string EventType { get; set; } = string.Empty;
    public string DataSource { get; set; } = string.Empty;
    public Guid? UserId { get; set; }
    public string? UserType { get; set; }
    public Guid? EntityId { get; set; }
    public string? EntityType { get; set; }
    public DateTime Timestamp { get; set; }
    public Dictionary<string, object> Properties { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
    public string? SessionId { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
    public DateTime CreatedAt { get; set; }
}

/// <summary>
/// Metric data transfer object
/// </summary>
public class MetricDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public decimal Value { get; set; }
    public string Unit { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
    public string Period { get; set; } = string.Empty;
    public Guid? UserId { get; set; }
    public string? UserType { get; set; }
    public string DataSource { get; set; } = string.Empty;
    public Dictionary<string, string> Tags { get; set; } = new();
    public KPITargetDto? Target { get; set; }
    public string PerformanceStatus { get; set; } = string.Empty;
    public decimal? TargetAchievementPercentage { get; set; }
}

/// <summary>
/// Dashboard metrics data transfer object
/// </summary>
public class DashboardMetricsDto
{
    public Guid? UserId { get; set; }
    public string? UserType { get; set; }
    public DateTime GeneratedAt { get; set; }
    public decimal AverageOrderFulfillmentTime { get; set; }
    public Dictionary<string, decimal> RealTimeMetrics { get; set; } = new();
    public List<MetricDataPointDto> OrderFulfillmentTrends { get; set; } = new();
    public Dictionary<string, object> RoleSpecificMetrics { get; set; } = new();
}

/// <summary>
/// Metric data point for trends and time series data
/// </summary>
public class MetricDataPointDto
{
    public DateTime Timestamp { get; set; }
    public decimal Value { get; set; }
    public string Label { get; set; } = string.Empty;
    public string MetricName { get; set; } = string.Empty;
    public string Unit { get; set; } = string.Empty;
    public Dictionary<string, object> Metadata { get; set; } = new();
    public DateTime CreatedAt { get; set; }
}

/// <summary>
/// KPI target data transfer object
/// </summary>
public class KPITargetDto
{
    public decimal TargetValue { get; set; }
    public decimal? MinThreshold { get; set; }
    public decimal? MaxThreshold { get; set; }
    public decimal? WarningThreshold { get; set; }
    public decimal? CriticalThreshold { get; set; }
    public string Unit { get; set; } = string.Empty;
    public bool IsHigherBetter { get; set; }
}

/// <summary>
/// Event statistics data transfer object
/// </summary>
public class EventStatisticsDto
{
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
    public string Period { get; set; } = string.Empty;
    public string? EventType { get; set; }
    public string? DataSource { get; set; }
    public string? UserType { get; set; }
    public long EventCount { get; set; }
    public long UniqueUsers { get; set; }
    public long UniqueSessions { get; set; }
}

/// <summary>
/// Top event data transfer object
/// </summary>
public class TopEventDto
{
    public string EventName { get; set; } = string.Empty;
    public string EventType { get; set; } = string.Empty;
    public string DataSource { get; set; } = string.Empty;
    public long EventCount { get; set; }
    public long UniqueUsers { get; set; }
    public DateTime FirstOccurrence { get; set; }
    public DateTime LastOccurrence { get; set; }
}

/// <summary>
/// Metric time series data transfer object
/// </summary>
public class MetricTimeSeriesDto
{
    public string MetricName { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Unit { get; set; } = string.Empty;
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;
    public List<MetricDataPointDto> DataPoints { get; set; } = new();
    public MetricSummaryDto Summary { get; set; } = new();
}

// MetricDataPointDto is already defined above (line 71) - using that instead

/// <summary>
/// Metric summary statistics
/// </summary>
public class MetricSummaryDto
{
    public decimal Average { get; set; }
    public decimal Minimum { get; set; }
    public decimal Maximum { get; set; }
    public decimal Sum { get; set; }
    public long Count { get; set; }
    public decimal? StandardDeviation { get; set; }
    public string TrendDirection { get; set; } = string.Empty;
    public decimal? GrowthRate { get; set; }
}

/// <summary>
/// KPI performance data transfer object
/// </summary>
public class KPIPerformanceDto
{
    public string MetricName { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string? UserType { get; set; }
    public decimal CurrentValue { get; set; }
    public decimal? TargetValue { get; set; }
    public string Unit { get; set; } = string.Empty;
    public bool? IsHigherBetter { get; set; }
    public string PerformanceStatus { get; set; } = string.Empty;
    public decimal? TargetAchievementPercentage { get; set; }
    public string AlertSeverity { get; set; } = string.Empty;
    public DateTime LastUpdated { get; set; }

    // Properties required by GetShipperAnalyticsQueryHandler
    public string KPIName { get; set; } = string.Empty;
    public decimal PerformancePercentage { get; set; }
    public string Status { get; set; } = string.Empty;
    public string Trend { get; set; } = string.Empty;
}

/// <summary>
/// Real-time metric data transfer object
/// </summary>
public class RealTimeMetricDto
{
    public string MetricName { get; set; } = string.Empty;
    public decimal CurrentValue { get; set; }
    public string Unit { get; set; } = string.Empty;
    public DateTime LastUpdated { get; set; }
    public decimal? PreviousValue { get; set; }
    public decimal? ChangePercentage { get; set; }
    public string TrendDirection { get; set; } = string.Empty;
    public string PerformanceStatus { get; set; } = string.Empty;

    // Properties required by RealTimeAnalyticsService
    public string Trend { get; set; } = string.Empty; // Alias for TrendDirection
    public string Status { get; set; } = string.Empty; // Alias for PerformanceStatus
}

/// <summary>
/// Metric trend data transfer object
/// </summary>
public class MetricTrendDto
{
    public string MetricName { get; set; } = string.Empty;
    public string TrendDirection { get; set; } = string.Empty;
    public decimal GrowthRate { get; set; }
    public decimal ChangePercentage { get; set; }
    public decimal CurrentValue { get; set; }
    public decimal PreviousValue { get; set; }
    public string Unit { get; set; } = string.Empty;
    public DateTime AnalysisPeriodStart { get; set; }
    public DateTime AnalysisPeriodEnd { get; set; }
}

/// <summary>
/// Metric comparison data transfer object
/// </summary>
public class MetricComparisonDto
{
    public string MetricName { get; set; } = string.Empty;
    public string Unit { get; set; } = string.Empty;
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;
    public List<UserMetricComparisonDto> UserComparisons { get; set; } = new();
    public MetricSummaryDto OverallSummary { get; set; } = new();
}

/// <summary>
/// User metric comparison data transfer object
/// </summary>
public class UserMetricComparisonDto
{
    public Guid UserId { get; set; }
    public string? UserType { get; set; }
    public decimal AverageValue { get; set; }
    public decimal TotalValue { get; set; }
    public long DataPointCount { get; set; }
    public string PerformanceRank { get; set; } = string.Empty;
    public List<MetricDataPointDto> DataPoints { get; set; } = new();
}

/// <summary>
/// Underperforming KPI data transfer object
/// </summary>
public class UnderperformingKPIDto
{
    public string MetricName { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public decimal CurrentValue { get; set; }
    public decimal TargetValue { get; set; }
    public string Unit { get; set; } = string.Empty;
    public decimal PerformanceGap { get; set; }
    public decimal PerformanceGapPercentage { get; set; }
    public string PerformanceStatus { get; set; } = string.Empty;
    public string AlertSeverity { get; set; } = string.Empty;
    public DateTime LastUpdated { get; set; }
    public string? RecommendedAction { get; set; }
}

/// <summary>
/// Analytics summary data transfer object
/// </summary>
public class AnalyticsSummaryDto
{
    public DateTime GeneratedAt { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public long TotalEvents { get; set; }
    public long UniqueUsers { get; set; }
    public long UniqueSessions { get; set; }
    public int TotalMetrics { get; set; }
    public int ActiveAlerts { get; set; }
    public int CriticalAlerts { get; set; }
    public List<TopEventDto> TopEvents { get; set; } = new();
    public List<KPIPerformanceDto> KeyKPIs { get; set; } = new();
    public List<UnderperformingKPIDto> UnderperformingKPIs { get; set; } = new();
}

/// <summary>
/// User engagement metrics data transfer object
/// </summary>
public class UserEngagementMetricsDto
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;
    public long TotalActiveUsers { get; set; }
    public long NewUsers { get; set; }
    public long ReturningUsers { get; set; }
    public decimal AverageSessionDuration { get; set; }
    public decimal AverageEventsPerUser { get; set; }
    public decimal UserRetentionRate { get; set; }
    public List<UserTypeEngagementDto> UserTypeBreakdown { get; set; } = new();
    public List<EngagementTrendDto> EngagementTrends { get; set; } = new();
}

/// <summary>
/// User type engagement data transfer object
/// </summary>
public class UserTypeEngagementDto
{
    public string UserType { get; set; } = string.Empty;
    public long ActiveUsers { get; set; }
    public long TotalEvents { get; set; }
    public decimal AverageEventsPerUser { get; set; }
    public decimal AverageSessionDuration { get; set; }
}

/// <summary>
/// Engagement trend data transfer object
/// </summary>
public class EngagementTrendDto
{
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
    public long ActiveUsers { get; set; }
    public long TotalEvents { get; set; }
    public long UniqueSessions { get; set; }
    public decimal AverageSessionDuration { get; set; }
}

/// <summary>
/// Platform performance metrics data transfer object
/// </summary>
public class PlatformPerformanceMetricsDto
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;
    public decimal OverallPerformanceScore { get; set; }
    public long TotalTransactions { get; set; }
    public decimal AverageResponseTime { get; set; }
    public decimal SystemUptime { get; set; }
    public decimal ErrorRate { get; set; }
    public long TotalRevenue { get; set; }
    public decimal RevenueGrowthRate { get; set; }
    public List<ServicePerformanceDto> ServicePerformance { get; set; } = new();
    public List<MetricDataPointDto> PerformanceTrends { get; set; } = new();
}

/// <summary>
/// Service performance data transfer object
/// </summary>
public class ServicePerformanceDto
{
    public string ServiceName { get; set; } = string.Empty;
    public decimal PerformanceScore { get; set; }
    public decimal AverageResponseTime { get; set; }
    public decimal ErrorRate { get; set; }
    public decimal Uptime { get; set; }
    public long RequestCount { get; set; }
}

/// <summary>
/// Dashboard data transfer object
/// </summary>
public class DashboardDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public Guid? UserId { get; set; }
    public string? UserType { get; set; }
    public bool IsDefault { get; set; }
    public bool IsPublic { get; set; }
    public Dictionary<string, object> Configuration { get; set; } = new();
    public DateTime? LastAccessedAt { get; set; }
    public int AccessCount { get; set; }
    public List<DashboardWidgetDto> Widgets { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }

    // Properties required by DataVisualizationService
    public string Title { get; set; } = string.Empty;
    public Guid CreatedBy { get; set; }
    public string Layout { get; set; } = string.Empty;
    public Dictionary<string, object> Filters { get; set; } = new();
    public int RefreshInterval { get; set; } = 300;
    public List<string> Tags { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Dashboard widget data transfer object
/// </summary>
public class DashboardWidgetDto
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string WidgetType { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty; // Alias for WidgetType for compatibility
    public int Position { get; set; }
    public int Width { get; set; }
    public int Height { get; set; }
    public string Size { get; set; } = "medium"; // small, medium, large
    public Dictionary<string, object> Configuration { get; set; } = new();
    public bool IsVisible { get; set; }
    public string? DataSource { get; set; }
    public List<string> DataSources { get; set; } = new();
    public string RefreshInterval { get; set; } = "5m";
    public DateTime? DefaultPeriodStart { get; set; }
    public DateTime? DefaultPeriodEnd { get; set; }
    public string? DefaultPeriodType { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

/// <summary>
/// Report data transfer object
/// </summary>
public class ReportDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public Guid GeneratedBy { get; set; }
    public string UserType { get; set; } = string.Empty;
    public Dictionary<string, object> Parameters { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
    public DateTime GeneratedAt { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public bool IsScheduled { get; set; }
    public string? ScheduleCron { get; set; }
    public int AccessCount { get; set; }
    public DateTime? LastAccessedAt { get; set; }
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
    public string Period { get; set; } = string.Empty;
    public List<ReportSectionDto> Sections { get; set; } = new();
    public List<ReportExportDto> Exports { get; set; } = new();
    public bool IsExpired { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

// ReportSectionDto is already defined in ReportingDTOs.cs - using that instead

/// <summary>
/// Report export data transfer object
/// </summary>
public class ReportExportDto
{
    public Guid Id { get; set; }
    public Guid ReportId { get; set; }
    public string Format { get; set; } = string.Empty;
    public Guid ExportedBy { get; set; }
    public DateTime ExportedAt { get; set; }
    public string FileName { get; set; } = string.Empty;
    public long FileSizeBytes { get; set; }
    public string? ContentType { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public int DownloadCount { get; set; }
    public bool IsExpired { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

/// <summary>
/// Alert data transfer object
/// </summary>
public class AlertDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public Guid? MetricId { get; set; }
    public string? MetricName { get; set; }
    public decimal? TriggerValue { get; set; }
    public decimal? ThresholdValue { get; set; }
    public DateTime TriggeredAt { get; set; }
    public DateTime? ResolvedAt { get; set; }
    public Guid? ResolvedBy { get; set; }
    public string? Resolution { get; set; }
    public bool IsActive { get; set; }
    public bool IsAcknowledged { get; set; }
    public Guid? AcknowledgedBy { get; set; }
    public DateTime? AcknowledgedAt { get; set; }
    public Dictionary<string, object> Context { get; set; } = new();
    public List<Guid> NotifiedUsers { get; set; } = new();
    public TimeSpan? ResolutionTime { get; set; }
    public TimeSpan ActiveTime { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }

    // Missing properties referenced in NotificationService
    public Guid? UserId { get; set; }
    public string Title { get; set; } = string.Empty; // Alias for Name
    public string AlertType { get; set; } = string.Empty;
    public decimal CurrentValue { get; set; }
}

/// <summary>
/// Alert rule data transfer object
/// </summary>
public class AlertRuleDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string MetricName { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public string Condition { get; set; } = string.Empty;
    public decimal ThresholdValue { get; set; }
    public TimeSpan EvaluationWindow { get; set; }
    public bool IsEnabled { get; set; }
    public Guid CreatedBy { get; set; }
    public string UserType { get; set; } = string.Empty;
    public List<Guid> NotificationTargets { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
    public DateTime? LastEvaluatedAt { get; set; }
    public DateTime? LastTriggeredAt { get; set; }
    public int TriggerCount { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}



/// <summary>
/// Performance insights data transfer object
/// </summary>
public class PerformanceInsightsDto
{
    public Guid UserId { get; set; }
    public string UserType { get; set; } = string.Empty;
    public DateTime GeneratedAt { get; set; }
    public decimal QuoteToOrderConversionRate { get; set; }
    public decimal ExpiredRFQRatio { get; set; }
    public decimal MilestoneComplianceRate { get; set; }
    public Dictionary<string, decimal> AdditionalMetrics { get; set; } = new();
    public List<PerformanceTrendDto> PerformanceTrends { get; set; } = new();
}

/// <summary>
/// Driver performance data transfer object
/// </summary>
public class DriverPerformanceDto
{
    public Guid DriverId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public decimal PODUploadRate { get; set; }
    public decimal MilestoneComplianceRate { get; set; }
    public decimal OnTimeDeliveryRate { get; set; }
    public decimal AverageTripRating { get; set; }
    public int TotalTripsCompleted { get; set; }
}

/// <summary>
/// Performance trend data transfer object
/// </summary>
public class PerformanceTrendDto
{
    public DateTime Timestamp { get; set; }
    public string MetricName { get; set; } = string.Empty;
    public decimal Value { get; set; }
    public string Unit { get; set; } = string.Empty;
    public string TrendDirection { get; set; } = string.Empty; // "Up", "Down", "Stable"
    public decimal ChangePercentage { get; set; }
}

/// <summary>
/// Risk monitoring dashboard data transfer object
/// </summary>
public class RiskMonitoringDto
{
    public DateTime GeneratedAt { get; set; }
    public Guid? UserId { get; set; }
    public int TotalIssueFlaggedFeedback { get; set; }
    public int RecentIssueFlaggedFeedback { get; set; }
    public int HighRiskEntitiesCount { get; set; }
    public int CriticalAlertsCount { get; set; }
    public int TrendingIssuesCount { get; set; }
    public List<RedFlagTrendDto> RedFlagTrends { get; set; } = new();
    public List<EntityPerformanceRankingDto> CarrierRankings { get; set; } = new();
    public List<EntityPerformanceRankingDto> BrokerRankings { get; set; } = new();
    public List<EntityPerformanceRankingDto> ShipperRankings { get; set; } = new();
    public List<RiskAlertDto> ActiveRiskAlerts { get; set; } = new();
}

/// <summary>
/// Red flag trend detection data transfer object
/// </summary>
public class RedFlagTrendDto
{
    public Guid EntityId { get; set; }
    public string EntityType { get; set; } = string.Empty;
    public decimal TrendPercentage { get; set; }
    public decimal CurrentNegativeFeedbackRate { get; set; }
    public decimal PreviousNegativeFeedbackRate { get; set; }
    public bool IsRedFlag { get; set; }
    public string Severity { get; set; } = string.Empty;
    public DateTime DetectedAt { get; set; }
    public List<string> RecommendedActions { get; set; } = new();
}

/// <summary>
/// Entity performance ranking data transfer object
/// </summary>
public class EntityPerformanceRankingDto
{
    public Guid EntityId { get; set; }
    public string EntityType { get; set; } = string.Empty;
    public int Rank { get; set; }
    public decimal PerformanceScore { get; set; }
    public int TotalFeedbackCount { get; set; }
    public decimal PositiveFeedbackPercentage { get; set; }
    public decimal NegativeFeedbackPercentage { get; set; }
    public decimal AverageRating { get; set; }
    public string RiskLevel { get; set; } = string.Empty;
    public DateTime LastUpdated { get; set; }
}

/// <summary>
/// Risk assessment data transfer object
/// </summary>
public class RiskAssessmentDto
{
    public Guid EntityId { get; set; }
    public string EntityType { get; set; } = string.Empty;
    public DateTime AssessmentDate { get; set; }
    public string RiskLevel { get; set; } = string.Empty;
    public decimal RiskScore { get; set; }
    public int RecentNegativeFeedbackCount { get; set; }
    public int TotalFeedbackCount { get; set; }
    public decimal NegativeFeedbackPercentage { get; set; }
    public bool HasNegativeTrend { get; set; }
    public decimal PerformanceScore { get; set; }
    public decimal AverageRating { get; set; }
    public List<string> RiskFactors { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
}

/// <summary>
/// Risk alert data transfer object
/// </summary>
public class RiskAlertDto
{
    public Guid Id { get; set; }
    public Guid? EntityId { get; set; }
    public string? EntityType { get; set; }
    public string AlertType { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string RiskLevel { get; set; } = string.Empty;
    public DateTime TriggeredAt { get; set; }
    public bool IsAcknowledged { get; set; }
    public List<string> RecommendedActions { get; set; } = new();
}

/// <summary>
/// Conversion analytics data transfer object
/// </summary>
public class ConversionAnalyticsDto
{
    public Guid? UserId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public DateTime GeneratedAt { get; set; }
    public int TotalRFQsCreated { get; set; }
    public int TotalQuotesSubmitted { get; set; }
    public int TotalOrdersCreated { get; set; }
    public decimal OverallConversionRate { get; set; }
    public decimal QuoteResponseRate { get; set; }
    public RFQLifecycleAnalyticsDto RFQLifecycleAnalytics { get; set; } = new();
    public List<FunnelAnalysisDto> FunnelAnalysis { get; set; } = new();
    public QuoteTimelinessMetricsDto QuoteTimelinessMetrics { get; set; } = new();
    public List<BehaviorPatternDto> BehaviorPatterns { get; set; } = new();
}

/// <summary>
/// RFQ lifecycle analytics data transfer object
/// </summary>
public class RFQLifecycleAnalyticsDto
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public int TotalRFQsCreated { get; set; }
    public int RFQsWithQuotes { get; set; }
    public int RFQsConverted { get; set; }
    public int RFQsExpired { get; set; }
    public decimal QuoteResponseRate { get; set; }
    public decimal ConversionRate { get; set; }
    public decimal ExpirationRate { get; set; }
    public decimal AverageTimeToFirstQuote { get; set; }
    public decimal AverageTimeToConversion { get; set; }
    public Dictionary<string, int> StageBreakdown { get; set; } = new();
}

/// <summary>
/// Funnel analysis data transfer object
/// </summary>
public class FunnelAnalysisDto
{
    public string StageName { get; set; } = string.Empty;
    public int StageOrder { get; set; }
    public int Count { get; set; }
    public decimal ConversionRate { get; set; }
    public int DropOffCount { get; set; }
    public decimal DropOffRate { get; set; }
}

/// <summary>
/// Quote timeliness metrics data transfer object
/// </summary>
public class QuoteTimelinessMetricsDto
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public int TotalQuotes { get; set; }
    public decimal AverageResponseTimeHours { get; set; }
    public decimal MedianResponseTimeHours { get; set; }
    public decimal FastestResponseTimeHours { get; set; }
    public decimal SlowestResponseTimeHours { get; set; }
    public int ExcellentResponseCount { get; set; }
    public int GoodResponseCount { get; set; }
    public int AcceptableResponseCount { get; set; }
    public int SlowResponseCount { get; set; }
    public decimal ExcellentResponsePercentage { get; set; }
    public decimal GoodResponsePercentage { get; set; }
    public decimal AcceptableResponsePercentage { get; set; }
    public decimal SlowResponsePercentage { get; set; }
}

/// <summary>
/// Behavior pattern data transfer object
/// </summary>
public class BehaviorPatternDto
{
    public string PatternType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal Confidence { get; set; }
    public int Frequency { get; set; }
    public DateTime LastObserved { get; set; }
}

/// <summary>
/// Conversion trends data transfer object
/// </summary>
public class ConversionTrendsDto
{
    public string Period { get; set; } = string.Empty;
    public DateTime GeneratedAt { get; set; }
    public List<ConversionTrendDataDto> TrendData { get; set; } = new();
}

/// <summary>
/// Conversion trend data point data transfer object
/// </summary>
public class ConversionTrendDataDto
{
    public DateTime Timestamp { get; set; }
    public int RFQsCreated { get; set; }
    public int QuotesSubmitted { get; set; }
    public int OrdersCreated { get; set; }
    public decimal ConversionRate { get; set; }
    public decimal QuoteResponseRate { get; set; }
}

/// <summary>
/// Carrier pool health data transfer object
/// </summary>
public class CarrierPoolHealthDto
{
    public string? Region { get; set; }
    public DateTime GeneratedAt { get; set; }
    public int TotalCarriers { get; set; }
    public int ActiveCarriers { get; set; }
    public int InactiveCarriers { get; set; }
    public decimal ActivityPercentage { get; set; }
    public decimal TotalCapacity { get; set; }
    public decimal UtilizedCapacity { get; set; }
    public decimal AvailableCapacity { get; set; }
    public decimal CapacityUtilizationPercentage { get; set; }
    public decimal AverageHealthScore { get; set; }
    public int HealthyCarriers { get; set; }
    public int AtRiskCarriers { get; set; }
    public decimal AverageOnTimePerformance { get; set; }
    public decimal AverageCustomerRating { get; set; }
    public string OverallHealthStatus { get; set; } = string.Empty;
    public List<string> HealthAlerts { get; set; } = new();
    public List<RegionalCarrierActivityDto> RegionalBreakdown { get; set; } = new();
}

/// <summary>
/// Regional carrier activity data transfer object
/// </summary>
public class RegionalCarrierActivityDto
{
    public string Region { get; set; } = string.Empty;
    public int TotalCarriers { get; set; }
    public int ActiveCarriers { get; set; }
    public int InactiveCarriers { get; set; }
    public decimal ActivityPercentage { get; set; }
    public string HealthStatus { get; set; } = string.Empty;
    public DateTime LastUpdated { get; set; }
}

/// <summary>
/// Carrier availability trends data transfer object
/// </summary>
public class CarrierAvailabilityTrendsDto
{
    public string? Region { get; set; }
    public string Period { get; set; } = string.Empty;
    public DateTime GeneratedAt { get; set; }
    public string TrendDirection { get; set; } = string.Empty;
    public decimal TrendPercentageChange { get; set; }
    public List<CarrierAvailabilityTrendDataDto> TrendData { get; set; } = new();
}

/// <summary>
/// Carrier availability trend data point data transfer object
/// </summary>
public class CarrierAvailabilityTrendDataDto
{
    public DateTime Timestamp { get; set; }
    public int TotalCarriers { get; set; }
    public int ActiveCarriers { get; set; }
    public decimal ActivityPercentage { get; set; }
}

/// <summary>
/// Carrier health score data transfer object
/// </summary>
public class CarrierHealthScoreDto
{
    public Guid CarrierId { get; set; }
    public string Region { get; set; } = string.Empty;
    public decimal HealthScore { get; set; }
    public string HealthStatus { get; set; } = string.Empty;
    public decimal OnTimeDeliveryRate { get; set; }
    public decimal AverageRating { get; set; }
    public int ActiveDays { get; set; }
    public int TotalTrips { get; set; }
    public DateTime LastActiveDate { get; set; }
    public List<string> RiskFactors { get; set; } = new();
}

/// <summary>
/// Carrier capacity analysis data transfer object
/// </summary>
public class CarrierCapacityAnalysisDto
{
    public string? Region { get; set; }
    public DateTime GeneratedAt { get; set; }
    public decimal TotalCapacity { get; set; }
    public decimal UtilizedCapacity { get; set; }
    public decimal AvailableCapacity { get; set; }
    public decimal UtilizationPercentage { get; set; }
    public List<int> PeakUtilizationHours { get; set; } = new();
    public Dictionary<string, decimal> CapacityByVehicleType { get; set; } = new();
    public string CapacityTrend { get; set; } = string.Empty;
}

/// <summary>
/// Carrier performance metric data transfer object
/// </summary>
public class CarrierPerformanceMetricDto
{
    public Guid CarrierId { get; set; }
    public string Region { get; set; } = string.Empty;
    public decimal OnTimeDeliveryRate { get; set; }
    public decimal AverageRating { get; set; }
    public int TotalTrips { get; set; }
    public int CompletedTrips { get; set; }
    public int CancelledTrips { get; set; }
    public decimal AverageResponseTime { get; set; }
    public decimal CustomerSatisfactionScore { get; set; }
    public decimal ReliabilityScore { get; set; }
    public decimal EfficiencyScore { get; set; }
}

