using FluentAssertions;
using MobileWorkflow.Domain.Entities;
using Xunit;

namespace MobileWorkflow.Tests.Domain;

public class RoleTemplateMappingsTests
{
    [Fact]
    public void CreateRoleTemplateMappings_WithValidData_ShouldCreateSuccessfully()
    {
        // Arrange
        var roleName = "Driver";
        var templateId = Guid.NewGuid();
        var createdBy = "<EMAIL>";
        var isDefault = true;
        var priority = 100;

        // Act
        var mapping = new RoleTemplateMappings(roleName, templateId, createdBy, isDefault, priority);

        // Assert
        mapping.Should().NotBeNull();
        mapping.RoleName.Should().Be(roleName);
        mapping.MilestoneTemplateId.Should().Be(templateId);
        mapping.CreatedBy.Should().Be(createdBy);
        mapping.IsDefault.Should().Be(isDefault);
        mapping.Priority.Should().Be(priority);
        mapping.IsActive.Should().BeTrue();
        mapping.Conditions.Should().BeNull();
        mapping.Configuration.Should().NotBeNull();
        mapping.Metadata.Should().NotBeNull();
    }

    [Theory]
    [InlineData(null)]
    [InlineData("")]
    public void CreateRoleTemplateMappings_WithInvalidRoleName_ShouldThrowArgumentNullException(string invalidRoleName)
    {
        // Act & Assert
        var act = () => new RoleTemplateMappings(invalidRoleName, Guid.NewGuid(), "admin");
        act.Should().Throw<ArgumentNullException>();
    }

    [Theory]
    [InlineData(null)]
    [InlineData("")]
    public void CreateRoleTemplateMappings_WithInvalidCreatedBy_ShouldThrowArgumentNullException(string invalidCreatedBy)
    {
        // Act & Assert
        var act = () => new RoleTemplateMappings("Driver", Guid.NewGuid(), invalidCreatedBy);
        act.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void SetAsDefault_WhenCalled_ShouldSetIsDefaultToTrue()
    {
        // Arrange
        var mapping = CreateTestRoleTemplateMappings();

        // Act
        mapping.SetAsDefault();

        // Assert
        mapping.IsDefault.Should().BeTrue();
        mapping.UpdatedAt.Should().NotBeNull();
        mapping.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void RemoveAsDefault_WhenCalled_ShouldSetIsDefaultToFalse()
    {
        // Arrange
        var mapping = CreateTestRoleTemplateMappings();
        mapping.SetAsDefault();

        // Act
        mapping.RemoveAsDefault();

        // Assert
        mapping.IsDefault.Should().BeFalse();
        mapping.UpdatedAt.Should().NotBeNull();
    }

    [Fact]
    public void Activate_WhenCalled_ShouldSetIsActiveToTrue()
    {
        // Arrange
        var mapping = CreateTestRoleTemplateMappings();
        mapping.Deactivate();

        // Act
        mapping.Activate();

        // Assert
        mapping.IsActive.Should().BeTrue();
        mapping.UpdatedAt.Should().NotBeNull();
    }

    [Fact]
    public void Deactivate_WhenCalled_ShouldSetIsActiveToFalse()
    {
        // Arrange
        var mapping = CreateTestRoleTemplateMappings();

        // Act
        mapping.Deactivate();

        // Assert
        mapping.IsActive.Should().BeFalse();
        mapping.UpdatedAt.Should().NotBeNull();
    }

    [Fact]
    public void UpdatePriority_WithValidData_ShouldUpdateSuccessfully()
    {
        // Arrange
        var mapping = CreateTestRoleTemplateMappings();
        var newPriority = 200;
        var updatedBy = "<EMAIL>";

        // Act
        mapping.UpdatePriority(newPriority, updatedBy);

        // Assert
        mapping.Priority.Should().Be(newPriority);
        mapping.UpdatedBy.Should().Be(updatedBy);
        mapping.UpdatedAt.Should().NotBeNull();
    }

    [Fact]
    public void SetConditions_WithValidConditions_ShouldSetSuccessfully()
    {
        // Arrange
        var mapping = CreateTestRoleTemplateMappings();
        var conditions = "{\"department\": \"logistics\", \"experience_level\": \"senior\"}";
        var updatedBy = "<EMAIL>";

        // Act
        mapping.SetConditions(conditions, updatedBy);

        // Assert
        mapping.Conditions.Should().Be(conditions);
        mapping.UpdatedBy.Should().Be(updatedBy);
        mapping.UpdatedAt.Should().NotBeNull();
    }

    [Fact]
    public void SetConditions_WithNull_ShouldSetToNull()
    {
        // Arrange
        var mapping = CreateTestRoleTemplateMappings();
        var updatedBy = "<EMAIL>";

        // Act
        mapping.SetConditions(null, updatedBy);

        // Assert
        mapping.Conditions.Should().BeNull();
        mapping.UpdatedBy.Should().Be(updatedBy);
    }

    [Fact]
    public void EvaluateConditions_WithNoConditions_ShouldReturnTrue()
    {
        // Arrange
        var mapping = CreateTestRoleTemplateMappings();
        var context = new Dictionary<string, object>();

        // Act
        var result = mapping.EvaluateConditions(context);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void EvaluateConditions_WithMatchingConditions_ShouldReturnTrue()
    {
        // Arrange
        var mapping = CreateTestRoleTemplateMappings();
        var conditions = "{\"department\": \"logistics\", \"experience_level\": \"senior\"}";
        mapping.SetConditions(conditions, "admin");

        var context = new Dictionary<string, object>
        {
            { "department", "logistics" },
            { "experience_level", "senior" }
        };

        // Act
        var result = mapping.EvaluateConditions(context);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void EvaluateConditions_WithNonMatchingConditions_ShouldReturnFalse()
    {
        // Arrange
        var mapping = CreateTestRoleTemplateMappings();
        var conditions = "{\"department\": \"logistics\", \"experience_level\": \"senior\"}";
        mapping.SetConditions(conditions, "admin");

        var context = new Dictionary<string, object>
        {
            { "department", "finance" },
            { "experience_level", "junior" }
        };

        // Act
        var result = mapping.EvaluateConditions(context);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void EvaluateConditions_WithMissingContextKey_ShouldReturnFalse()
    {
        // Arrange
        var mapping = CreateTestRoleTemplateMappings();
        var conditions = "{\"department\": \"logistics\", \"experience_level\": \"senior\"}";
        mapping.SetConditions(conditions, "admin");

        var context = new Dictionary<string, object>
        {
            { "department", "logistics" }
            // Missing experience_level
        };

        // Act
        var result = mapping.EvaluateConditions(context);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void EvaluateConditions_WithInvalidJsonConditions_ShouldReturnFalse()
    {
        // Arrange
        var mapping = CreateTestRoleTemplateMappings();
        var invalidConditions = "invalid_json_format";
        mapping.SetConditions(invalidConditions, "admin");

        var context = new Dictionary<string, object>();

        // Act
        var result = mapping.EvaluateConditions(context);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void EvaluateConditions_WithCaseInsensitiveMatch_ShouldReturnTrue()
    {
        // Arrange
        var mapping = CreateTestRoleTemplateMappings();
        var conditions = "{\"department\": \"logistics\"}";
        mapping.SetConditions(conditions, "admin");

        var context = new Dictionary<string, object>
        {
            { "department", "LOGISTICS" }
        };

        // Act
        var result = mapping.EvaluateConditions(context);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void GetValidationErrors_WithValidMapping_ShouldReturnEmptyList()
    {
        // Arrange
        var mapping = CreateTestRoleTemplateMappings();

        // Act
        var errors = mapping.GetValidationErrors();

        // Assert
        errors.Should().BeEmpty();
    }

    [Fact]
    public void GetValidationErrors_WithInvalidPriority_ShouldReturnError()
    {
        // Arrange
        var mapping = new RoleTemplateMappings("Driver", Guid.NewGuid(), "admin", false, -1);

        // Act
        var errors = mapping.GetValidationErrors();

        // Assert
        errors.Should().Contain("Priority must be non-negative");
    }

    [Fact]
    public void GetValidationErrors_WithInvalidJsonConditions_ShouldReturnError()
    {
        // Arrange
        var mapping = CreateTestRoleTemplateMappings();
        mapping.SetConditions("invalid_json", "admin");

        // Act
        var errors = mapping.GetValidationErrors();

        // Assert
        errors.Should().Contain("Conditions must be valid JSON format");
    }

    [Fact]
    public void CanBeDeleted_WhenNotDefault_ShouldReturnTrue()
    {
        // Arrange
        var mapping = CreateTestRoleTemplateMappings();

        // Act
        var canBeDeleted = mapping.CanBeDeleted();

        // Assert
        canBeDeleted.Should().BeTrue();
    }

    [Fact]
    public void CanBeDeleted_WhenDefault_ShouldReturnFalse()
    {
        // Arrange
        var mapping = CreateTestRoleTemplateMappings();
        mapping.SetAsDefault();

        // Act
        var canBeDeleted = mapping.CanBeDeleted();

        // Assert
        canBeDeleted.Should().BeFalse();
    }

    [Fact]
    public void UpdateConfiguration_WithValidData_ShouldUpdateSuccessfully()
    {
        // Arrange
        var mapping = CreateTestRoleTemplateMappings();
        var configuration = new Dictionary<string, object>
        {
            { "auto_assign", true },
            { "notification_enabled", false }
        };
        var updatedBy = "<EMAIL>";

        // Act
        mapping.UpdateConfiguration(configuration, updatedBy);

        // Assert
        mapping.Configuration.Should().BeEquivalentTo(configuration);
        mapping.UpdatedBy.Should().Be(updatedBy);
        mapping.UpdatedAt.Should().NotBeNull();
    }

    [Fact]
    public void AddMetadata_WithValidData_ShouldAddSuccessfully()
    {
        // Arrange
        var mapping = CreateTestRoleTemplateMappings();
        var key = "test_key";
        var value = "test_value";

        // Act
        mapping.AddMetadata(key, value);

        // Assert
        mapping.Metadata.Should().ContainKey(key);
        mapping.Metadata[key].Should().Be(value);
        mapping.UpdatedAt.Should().NotBeNull();
    }

    [Fact]
    public void GetMetadata_WithExistingKey_ShouldReturnValue()
    {
        // Arrange
        var mapping = CreateTestRoleTemplateMappings();
        var key = "test_key";
        var value = 42;
        mapping.AddMetadata(key, value);

        // Act
        var result = mapping.GetMetadata<int>(key);

        // Assert
        result.Should().Be(value);
    }

    [Fact]
    public void GetMetadata_WithNonExistingKey_ShouldReturnDefault()
    {
        // Arrange
        var mapping = CreateTestRoleTemplateMappings();
        var defaultValue = 100;

        // Act
        var result = mapping.GetMetadata("non_existing_key", defaultValue);

        // Assert
        result.Should().Be(defaultValue);
    }

    private static RoleTemplateMappings CreateTestRoleTemplateMappings()
    {
        return new RoleTemplateMappings(
            "Driver",
            Guid.NewGuid(),
            "<EMAIL>",
            false,
            100);
    }
}
