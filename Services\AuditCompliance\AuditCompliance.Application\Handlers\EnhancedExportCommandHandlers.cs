using AuditCompliance.Application.Commands;
using AuditCompliance.Application.DTOs;
using AuditCompliance.Application.Interfaces;
using AuditCompliance.Domain.Enums;
using MediatR;
using Microsoft.Extensions.Logging;
using System.Text;
using System.Text.Json;

namespace AuditCompliance.Application.Handlers
{
    /// <summary>
    /// Enhanced audit export command handler with comprehensive features
    /// </summary>
    public class EnhancedAuditExportCommandHandler : IRequestHandler<EnhancedAuditExportCommand, EnhancedAuditExportResponseDto>
    {
        private readonly IAuditLogRepository _auditLogRepository;
        private readonly IFileStorageService _fileStorageService;
        private readonly IDigitalSignatureService _digitalSignatureService;
        private readonly IBackgroundJobService _backgroundJobService;
        private readonly INotificationService _notificationService;
        private readonly IAuditService _auditService;
        private readonly ILogger<EnhancedAuditExportCommandHandler> _logger;

        public EnhancedAuditExportCommandHandler(
            IAuditLogRepository auditLogRepository,
            IFileStorageService fileStorageService,
            IDigitalSignatureService digitalSignatureService,
            IBackgroundJobService backgroundJobService,
            INotificationService notificationService,
            IAuditService auditService,
            ILogger<EnhancedAuditExportCommandHandler> logger)
        {
            _auditLogRepository = auditLogRepository;
            _fileStorageService = fileStorageService;
            _digitalSignatureService = digitalSignatureService;
            _backgroundJobService = backgroundJobService;
            _notificationService = notificationService;
            _auditService = auditService;
            _logger = logger;
        }

        public async Task<EnhancedAuditExportResponseDto> Handle(EnhancedAuditExportCommand request, CancellationToken cancellationToken)
        {
            var exportId = Guid.NewGuid().ToString("N")[..12];
            var startTime = DateTime.UtcNow;

            try
            {
                // Log the export request
                await LogExportRequestAsync(request, exportId);

                // Validate the request
                var validationResults = await ValidateExportRequestAsync(request);
                if (validationResults.Any(v => v.Type == "Error"))
                {
                    return CreateErrorResponse(exportId, "Validation failed", validationResults);
                }

                // Check if background processing is needed
                if (request.EnableBackgroundProcessing || await ShouldUseBackgroundProcessingAsync(request))
                {
                    return await QueueBackgroundExportAsync(request, exportId, validationResults);
                }

                // Process export synchronously
                return await ProcessExportAsync(request, exportId, validationResults, startTime, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing enhanced audit export {ExportId}", exportId);
                return CreateErrorResponse(exportId, ex.Message, new List<ExportValidationDto>());
            }
        }

        private async Task<EnhancedAuditExportResponseDto> ProcessExportAsync(
            EnhancedAuditExportCommand request,
            string exportId,
            List<ExportValidationDto> validationResults,
            DateTime startTime,
            CancellationToken cancellationToken)
        {
            // Get audit data with enhanced filtering
            var auditData = await GetAuditDataAsync(request, cancellationToken);
            
            // Generate export content
            var exportContent = await GenerateExportContentAsync(auditData, request, cancellationToken);
            
            // Apply compression if requested
            if (request.EnableCompression)
            {
                exportContent = await CompressContentAsync(exportContent, request.Format);
            }

            // Generate filename
            var fileName = GenerateFileName(request, exportId);
            
            // Save to storage
            var fileUrl = await _fileStorageService.SaveExportFileAsync(fileName, exportContent, GetContentType(request.Format));
            
            // Generate digital signature if required
            string? digitalSignature = null;
            if (request.RequireDigitalSignature)
            {
                digitalSignature = await _digitalSignatureService.SignContentAsync(exportContent);
            }

            // Create metadata
            var metadata = CreateExportMetadata(request, auditData, startTime, DateTime.UtcNow);

            var response = new EnhancedAuditExportResponseDto
            {
                ExportId = exportId,
                FileName = fileName,
                FileUrl = fileUrl,
                Format = request.Format,
                RecordCount = auditData.Count,
                FileSizeBytes = Encoding.UTF8.GetByteCount(exportContent),
                GeneratedAt = startTime,
                ExpiresAt = DateTime.UtcNow.AddDays(7),
                Status = ExportStatus.Completed,
                StatusMessage = "Export completed successfully",
                IsBackgroundProcessing = false,
                HasDigitalSignature = request.RequireDigitalSignature,
                DigitalSignature = digitalSignature,
                IsCompressed = request.EnableCompression,
                Metadata = metadata,
                ValidationResults = validationResults,
                Links = CreateResponseLinks(exportId)
            };

            // Send notification if email provided
            if (!string.IsNullOrEmpty(request.NotificationEmail))
            {
                await SendCompletionNotificationAsync(request.NotificationEmail, response);
            }

            return response;
        }

        private async Task<EnhancedAuditExportResponseDto> QueueBackgroundExportAsync(
            EnhancedAuditExportCommand request,
            string exportId,
            List<ExportValidationDto> validationResults)
        {
            // Queue the export job
            var jobId = await _backgroundJobService.QueueExportJobAsync(new ExportJobDto
            {
                JobId = exportId,
                JobType = "EnhancedAuditExport",
                Parameters = JsonSerializer.Serialize(request),
                RequestedBy = request.RequestedBy,
                RequestedAt = DateTime.UtcNow,
                NotificationEmail = request.NotificationEmail
            });

            return new EnhancedAuditExportResponseDto
            {
                ExportId = exportId,
                Status = ExportStatus.Queued,
                StatusMessage = "Export queued for background processing",
                IsBackgroundProcessing = true,
                GeneratedAt = DateTime.UtcNow,
                ValidationResults = validationResults,
                Links = CreateResponseLinks(exportId)
            };
        }

        private async Task<List<dynamic>> GetAuditDataAsync(EnhancedAuditExportCommand request, CancellationToken cancellationToken)
        {
            // Build query with enhanced filtering
            var query = new AuditTrailQueryDto
            {
                UserIds = request.UserIds,
                EntityTypes = request.EntityTypes,
                FromDate = request.FromDate,
                ToDate = request.ToDate,
                IncludeSensitiveData = request.IncludeSensitiveData,
                PageSize = request.PageSize,
                MaxRecords = request.MaxRecords
            };

            // Add module filtering
            if (request.ModuleNames.Any())
            {
                query.ModuleNames = request.ModuleNames;
            }

            // Add compliance flags filtering
            if (request.ComplianceFlags.Any())
            {
                query.ComplianceFlags = request.ComplianceFlags;
            }

            // Apply custom filters
            foreach (var filter in request.CustomFilters)
            {
                query.CustomFilters[filter.Key] = filter.Value;
            }

            var result = await _auditLogRepository.GetAuditTrailAsync(query, cancellationToken);
            return result.Items.Cast<dynamic>().ToList();
        }

        private async Task<string> GenerateExportContentAsync(List<dynamic> data, EnhancedAuditExportCommand request, CancellationToken cancellationToken)
        {
            return request.Format switch
            {
                ExportFormat.CSV => await GenerateCsvContentAsync(data, request.SelectedColumns),
                ExportFormat.Excel => await GenerateExcelContentAsync(data, request.SelectedColumns),
                ExportFormat.JSON => await GenerateJsonContentAsync(data, request.SelectedColumns),
                ExportFormat.PDF => await GeneratePdfContentAsync(data, request),
                ExportFormat.XML => await GenerateXmlContentAsync(data, request.SelectedColumns),
                _ => throw new ArgumentException($"Unsupported export format: {request.Format}")
            };
        }

        private async Task<string> GenerateCsvContentAsync(List<dynamic> data, List<string> selectedColumns)
        {
            var csv = new StringBuilder();
            
            // Add headers
            if (selectedColumns.Any())
            {
                csv.AppendLine(string.Join(",", selectedColumns.Select(c => $"\"{c}\"")));
            }
            else
            {
                // Use default columns
                csv.AppendLine("\"Id\",\"EventType\",\"Action\",\"EntityType\",\"UserId\",\"UserName\",\"Timestamp\",\"Description\"");
            }

            // Add data rows
            foreach (var item in data)
            {
                var row = selectedColumns.Any() 
                    ? selectedColumns.Select(col => GetPropertyValue(item, col)?.ToString() ?? "")
                    : GetDefaultCsvRow(item);
                
                csv.AppendLine(string.Join(",", row.Select(r => $"\"{r?.Replace("\"", "\"\"")}\"")));
            }

            return await Task.FromResult(csv.ToString());
        }

        private async Task<string> GenerateJsonContentAsync(List<dynamic> data, List<string> selectedColumns)
        {
            var filteredData = selectedColumns.Any() 
                ? data.Select(item => FilterProperties(item, selectedColumns))
                : data;

            return await Task.FromResult(JsonSerializer.Serialize(filteredData, new JsonSerializerOptions 
            { 
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            }));
        }

        private async Task<string> GenerateExcelContentAsync(List<dynamic> data, List<string> selectedColumns)
        {
            // For now, return CSV format - would implement actual Excel generation with a library like EPPlus
            return await GenerateCsvContentAsync(data, selectedColumns);
        }

        private async Task<string> GeneratePdfContentAsync(List<dynamic> data, EnhancedAuditExportCommand request)
        {
            // For now, return a simple text representation - would implement actual PDF generation
            var content = new StringBuilder();
            content.AppendLine($"Audit Export Report - {request.ExportTitle ?? "Audit Trail"}");
            content.AppendLine($"Generated: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC");
            content.AppendLine($"Records: {data.Count}");
            content.AppendLine(new string('-', 50));
            
            foreach (var item in data)
            {
                content.AppendLine(JsonSerializer.Serialize(item, new JsonSerializerOptions { WriteIndented = true }));
                content.AppendLine();
            }

            return await Task.FromResult(content.ToString());
        }

        private async Task<string> GenerateXmlContentAsync(List<dynamic> data, List<string> selectedColumns)
        {
            var xml = new StringBuilder();
            xml.AppendLine("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
            xml.AppendLine("<AuditExport>");
            
            foreach (var item in data)
            {
                xml.AppendLine("  <AuditRecord>");
                var properties = selectedColumns.Any() 
                    ? selectedColumns.ToDictionary(col => col, col => GetPropertyValue(item, col))
                    : GetAllProperties(item);
                
                foreach (var prop in properties)
                {
                    xml.AppendLine($"    <{prop.Key}>{System.Security.SecurityElement.Escape(prop.Value?.ToString() ?? "")}</{prop.Key}>");
                }
                xml.AppendLine("  </AuditRecord>");
            }
            
            xml.AppendLine("</AuditExport>");
            return await Task.FromResult(xml.ToString());
        }

        // Helper methods would be implemented here...
        private object? GetPropertyValue(dynamic item, string propertyName) => null;
        private IEnumerable<string> GetDefaultCsvRow(dynamic item) => new List<string>();
        private object FilterProperties(dynamic item, List<string> selectedColumns) => item;
        private Dictionary<string, object?> GetAllProperties(dynamic item) => new();
        private async Task<byte[]> CompressContentAsync(string content, ExportFormat format) => Encoding.UTF8.GetBytes(content);
        private string GenerateFileName(EnhancedAuditExportCommand request, string exportId) => $"audit_export_{exportId}.{GetFileExtension(request.Format)}";
        private string GetContentType(ExportFormat format) => "application/octet-stream";
        private string GetFileExtension(ExportFormat format) => format.ToString().ToLower();
        private ExportMetadataDto CreateExportMetadata(EnhancedAuditExportCommand request, List<dynamic> data, DateTime start, DateTime end) => new();
        private Dictionary<string, string> CreateResponseLinks(string exportId) => new();
        private async Task SendCompletionNotificationAsync(string email, EnhancedAuditExportResponseDto response) => await Task.CompletedTask;
        private async Task<List<ExportValidationDto>> ValidateExportRequestAsync(EnhancedAuditExportCommand request) => new();
        private async Task<bool> ShouldUseBackgroundProcessingAsync(EnhancedAuditExportCommand request) => false;
        private async Task LogExportRequestAsync(EnhancedAuditExportCommand request, string exportId) => await Task.CompletedTask;
        private EnhancedAuditExportResponseDto CreateErrorResponse(string exportId, string message, List<ExportValidationDto> validations) => new();
    }
}
