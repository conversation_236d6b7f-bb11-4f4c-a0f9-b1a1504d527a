using AuditCompliance.Application.Interfaces;
using AuditCompliance.Domain.Entities;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Caching.Memory;

namespace AuditCompliance.Application.Services
{
    /// <summary>
    /// Module registry service implementation
    /// </summary>
    public class ModuleRegistryService : IModuleRegistryService
    {
        private readonly IModuleRegistryRepository _moduleRegistryRepository;
        private readonly IModuleAccessControlRepository _accessControlRepository;
        private readonly IMemoryCache _cache;
        private readonly ILogger<ModuleRegistryService> _logger;
        private readonly TimeSpan _cacheExpiration = TimeSpan.FromMinutes(15);

        public ModuleRegistryService(
            IModuleRegistryRepository moduleRegistryRepository,
            IModuleAccessControlRepository accessControlRepository,
            IMemoryCache cache,
            ILogger<ModuleRegistryService> logger)
        {
            _moduleRegistryRepository = moduleRegistryRepository;
            _accessControlRepository = accessControlRepository;
            _cache = cache;
            _logger = logger;
        }

        public async Task<List<ModuleInfoDto>> GetAllModulesAsync()
        {
            const string cacheKey = "all_modules";
            
            if (_cache.TryGetValue(cacheKey, out List<ModuleInfoDto>? cachedModules))
            {
                return cachedModules!;
            }

            try
            {
                var modules = await _moduleRegistryRepository.GetAllAsync();
                var moduleInfos = modules.Select(MapToDto).ToList();
                
                _cache.Set(cacheKey, moduleInfos, _cacheExpiration);
                return moduleInfos;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all modules");
                throw;
            }
        }

        public async Task<ModuleInfoDto?> GetModuleByNameAsync(string moduleName)
        {
            var cacheKey = $"module_{moduleName}";
            
            if (_cache.TryGetValue(cacheKey, out ModuleInfoDto? cachedModule))
            {
                return cachedModule;
            }

            try
            {
                var module = await _moduleRegistryRepository.GetByModuleNameAsync(moduleName);
                if (module == null)
                    return null;

                var moduleInfo = MapToDto(module);
                _cache.Set(cacheKey, moduleInfo, _cacheExpiration);
                return moduleInfo;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving module {ModuleName}", moduleName);
                throw;
            }
        }

        public async Task RegisterModuleAsync(ModuleInfoDto moduleDto)
        {
            try
            {
                // Check if module already exists
                var existingModule = await _moduleRegistryRepository.GetByModuleNameAsync(moduleDto.ModuleName);
                if (existingModule != null)
                {
                    throw new InvalidOperationException($"Module {moduleDto.ModuleName} is already registered");
                }

                var module = new ModuleRegistry(
                    moduleDto.ModuleName,
                    moduleDto.DisplayName,
                    moduleDto.Description,
                    moduleDto.Version,
                    moduleDto.SupportedEntityTypes,
                    moduleDto.SupportedActions,
                    moduleDto.RequiredRoles,
                    moduleDto.Configuration,
                    Guid.NewGuid(), // This should come from the current user context
                    moduleDto.Configuration.TryGetValue("HealthCheckEndpoint", out var endpoint) ? endpoint?.ToString() : null);

                await _moduleRegistryRepository.AddAsync(module);
                
                // Clear cache
                _cache.Remove("all_modules");
                _cache.Remove($"module_{moduleDto.ModuleName}");

                _logger.LogInformation("Module {ModuleName} registered successfully", moduleDto.ModuleName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error registering module {ModuleName}", moduleDto.ModuleName);
                throw;
            }
        }

        public async Task UpdateModuleAsync(ModuleInfoDto moduleDto)
        {
            try
            {
                var module = await _moduleRegistryRepository.GetByModuleNameAsync(moduleDto.ModuleName);
                if (module == null)
                {
                    throw new InvalidOperationException($"Module {moduleDto.ModuleName} not found");
                }

                module.UpdateModule(
                    moduleDto.DisplayName,
                    moduleDto.Description,
                    moduleDto.Version,
                    moduleDto.SupportedEntityTypes,
                    moduleDto.SupportedActions,
                    moduleDto.RequiredRoles,
                    moduleDto.Configuration,
                    moduleDto.Configuration.TryGetValue("HealthCheckEndpoint", out var endpoint) ? endpoint?.ToString() : null);

                await _moduleRegistryRepository.UpdateAsync(module);
                
                // Clear cache
                _cache.Remove("all_modules");
                _cache.Remove($"module_{moduleDto.ModuleName}");

                _logger.LogInformation("Module {ModuleName} updated successfully", moduleDto.ModuleName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating module {ModuleName}", moduleDto.ModuleName);
                throw;
            }
        }

        public async Task<bool> HasModuleAccessAsync(Guid userId, string moduleName)
        {
            var cacheKey = $"module_access_{userId}_{moduleName}";
            
            if (_cache.TryGetValue(cacheKey, out bool cachedAccess))
            {
                return cachedAccess;
            }

            try
            {
                // Check if module exists and is active
                var module = await _moduleRegistryRepository.GetByModuleNameAsync(moduleName);
                if (module == null || !module.IsActive)
                {
                    _cache.Set(cacheKey, false, TimeSpan.FromMinutes(5));
                    return false;
                }

                // Check specific access control
                var accessControl = await _accessControlRepository.GetByUserAndModuleAsync(userId, moduleName);
                if (accessControl != null)
                {
                    var hasAccess = accessControl.IsActive && 
                                   (!accessControl.ExpiresAt.HasValue || accessControl.ExpiresAt.Value > DateTime.UtcNow);
                    
                    _cache.Set(cacheKey, hasAccess, TimeSpan.FromMinutes(5));
                    return hasAccess;
                }

                // If no specific access control, check if module has role requirements
                // This would need to be implemented with user role checking
                var hasDefaultAccess = !module.RequiredRoles.Any(); // If no roles required, allow access
                
                _cache.Set(cacheKey, hasDefaultAccess, TimeSpan.FromMinutes(5));
                return hasDefaultAccess;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking module access for user {UserId} and module {ModuleName}", userId, moduleName);
                return false;
            }
        }

        public async Task<List<ModuleInfoDto>> GetAccessibleModulesAsync(Guid userId)
        {
            var cacheKey = $"accessible_modules_{userId}";
            
            if (_cache.TryGetValue(cacheKey, out List<ModuleInfoDto>? cachedModules))
            {
                return cachedModules!;
            }

            try
            {
                var allModules = await GetAllModulesAsync();
                var accessibleModules = new List<ModuleInfoDto>();

                foreach (var module in allModules)
                {
                    if (await HasModuleAccessAsync(userId, module.ModuleName))
                    {
                        accessibleModules.Add(module);
                    }
                }

                _cache.Set(cacheKey, accessibleModules, TimeSpan.FromMinutes(10));
                return accessibleModules;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving accessible modules for user {UserId}", userId);
                throw;
            }
        }

        public async Task GrantModuleAccessAsync(Guid userId, string moduleName, List<string> allowedActions, Guid grantedBy, DateTime? expiresAt = null, string? reason = null)
        {
            try
            {
                // Check if module exists
                var module = await _moduleRegistryRepository.GetByModuleNameAsync(moduleName);
                if (module == null)
                {
                    throw new InvalidOperationException($"Module {moduleName} not found");
                }

                // Check if access control already exists
                var existingAccess = await _accessControlRepository.GetByUserAndModuleAsync(userId, moduleName);
                if (existingAccess != null)
                {
                    // Update existing access
                    foreach (var action in allowedActions)
                    {
                        existingAccess.GrantAction(action);
                    }
                    await _accessControlRepository.UpdateAsync(existingAccess);
                }
                else
                {
                    // Create new access control
                    var accessControl = new ModuleAccessControl(
                        userId,
                        moduleName,
                        allowedActions,
                        new List<string>(),
                        false,
                        grantedBy,
                        expiresAt,
                        reason);

                    await _accessControlRepository.AddAsync(accessControl);
                }

                // Clear cache
                _cache.Remove($"module_access_{userId}_{moduleName}");
                _cache.Remove($"accessible_modules_{userId}");

                _logger.LogInformation("Module access granted for user {UserId} to module {ModuleName}", userId, moduleName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error granting module access for user {UserId} to module {ModuleName}", userId, moduleName);
                throw;
            }
        }

        public async Task RevokeModuleAccessAsync(Guid userId, string moduleName, Guid revokedBy, string? reason = null)
        {
            try
            {
                var accessControl = await _accessControlRepository.GetByUserAndModuleAsync(userId, moduleName);
                if (accessControl != null)
                {
                    accessControl.RevokeAccess(revokedBy, reason);
                    await _accessControlRepository.UpdateAsync(accessControl);

                    // Clear cache
                    _cache.Remove($"module_access_{userId}_{moduleName}");
                    _cache.Remove($"accessible_modules_{userId}");

                    _logger.LogInformation("Module access revoked for user {UserId} from module {ModuleName}", userId, moduleName);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error revoking module access for user {UserId} from module {ModuleName}", userId, moduleName);
                throw;
            }
        }

        public async Task UpdateModuleHealthAsync(string moduleName, bool isHealthy, string? errorMessage = null)
        {
            try
            {
                var module = await _moduleRegistryRepository.GetByModuleNameAsync(moduleName);
                if (module != null)
                {
                    module.UpdateHealthStatus(isHealthy, errorMessage);
                    await _moduleRegistryRepository.UpdateAsync(module);

                    // Clear cache
                    _cache.Remove($"module_{moduleName}");
                    _cache.Remove("all_modules");

                    _logger.LogInformation("Module health updated for {ModuleName}: {IsHealthy}", moduleName, isHealthy);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating module health for {ModuleName}", moduleName);
                throw;
            }
        }

        private static ModuleInfoDto MapToDto(ModuleRegistry module)
        {
            return new ModuleInfoDto
            {
                ModuleName = module.ModuleName,
                DisplayName = module.DisplayName,
                Description = module.Description,
                Version = module.Version,
                IsActive = module.IsActive,
                SupportedEntityTypes = module.SupportedEntityTypes,
                SupportedActions = module.SupportedActions,
                RequiredRoles = module.RequiredRoles,
                Configuration = module.Configuration,
                RegisteredAt = module.RegisteredAt,
                LastUpdated = module.LastUpdated
            };
        }
    }
}
