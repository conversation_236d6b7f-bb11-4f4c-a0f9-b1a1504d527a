using MediatR;
using AuditCompliance.Application.Commands;
using AuditCompliance.Application.Interfaces;
using AuditCompliance.Domain.Entities;
using AuditCompliance.Domain.ValueObjects;
using AuditCompliance.Domain.Enums;
using Microsoft.Extensions.Logging;

namespace AuditCompliance.Application.Handlers;

public class CreateServiceProviderRatingCommandHandler : IRequestHandler<CreateServiceProviderRatingCommand, Guid>
{
    private readonly IServiceProviderRatingRepository _ratingRepository;
    private readonly ILogger<CreateServiceProviderRatingCommandHandler> _logger;

    public CreateServiceProviderRatingCommandHandler(
        IServiceProviderRatingRepository ratingRepository,
        ILogger<CreateServiceProviderRatingCommandHandler> logger)
    {
        _ratingRepository = ratingRepository;
        _logger = logger;
    }

    public async Task<Guid> Handle(CreateServiceProviderRatingCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var overallRating = RatingScore.FromNumeric(request.OverallRating);

            var rating = new ServiceProviderRating(
                request.ShipperId,
                request.ShipperName,
                request.TransportCompanyId,
                request.TransportCompanyName,
                overallRating,
                request.ServiceCompletedAt,
                request.OrderId,
                request.OrderNumber,
                request.TripId,
                request.TripNumber,
                request.ReviewTitle,
                request.ReviewComment,
                request.IsAnonymous);

            // Add category ratings
            foreach (var categoryRating in request.CategoryRatings)
            {
                var categoryScore = RatingScore.FromNumeric(categoryRating.Rating, categoryRating.Comment);
                rating.AddCategoryRating(categoryRating.Category, categoryScore, categoryRating.Comment);
            }

            var savedRating = await _ratingRepository.AddAsync(rating, cancellationToken);

            _logger.LogInformation("Service provider rating created with ID: {RatingId} for transport company: {TransportCompanyId}", 
                savedRating.Id, request.TransportCompanyId);

            return savedRating.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating service provider rating for transport company: {TransportCompanyId}", 
                request.TransportCompanyId);
            throw;
        }
    }
}

public class SubmitServiceProviderRatingCommandHandler : IRequestHandler<SubmitServiceProviderRatingCommand, bool>
{
    private readonly IServiceProviderRatingRepository _ratingRepository;
    private readonly ILogger<SubmitServiceProviderRatingCommandHandler> _logger;

    public SubmitServiceProviderRatingCommandHandler(
        IServiceProviderRatingRepository ratingRepository,
        ILogger<SubmitServiceProviderRatingCommandHandler> logger)
    {
        _ratingRepository = ratingRepository;
        _logger = logger;
    }

    public async Task<bool> Handle(SubmitServiceProviderRatingCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var rating = await _ratingRepository.GetByIdAsync(request.RatingId, cancellationToken);
            if (rating == null)
            {
                _logger.LogWarning("Service provider rating not found: {RatingId}", request.RatingId);
                return false;
            }

            rating.SubmitReview();
            await _ratingRepository.UpdateAsync(rating, cancellationToken);

            _logger.LogInformation("Service provider rating submitted: {RatingId}", request.RatingId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error submitting service provider rating: {RatingId}", request.RatingId);
            throw;
        }
    }
}

public class FlagServiceProviderRatingCommandHandler : IRequestHandler<FlagServiceProviderRatingCommand, bool>
{
    private readonly IServiceProviderRatingRepository _ratingRepository;
    private readonly ILogger<FlagServiceProviderRatingCommandHandler> _logger;

    public FlagServiceProviderRatingCommandHandler(
        IServiceProviderRatingRepository ratingRepository,
        ILogger<FlagServiceProviderRatingCommandHandler> logger)
    {
        _ratingRepository = ratingRepository;
        _logger = logger;
    }

    public async Task<bool> Handle(FlagServiceProviderRatingCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var rating = await _ratingRepository.GetByIdAsync(request.RatingId, cancellationToken);
            if (rating == null)
            {
                _logger.LogWarning("Service provider rating not found: {RatingId}", request.RatingId);
                return false;
            }

            rating.FlagForReview(request.Reason);
            await _ratingRepository.UpdateAsync(rating, cancellationToken);

            _logger.LogInformation("Service provider rating flagged: {RatingId}, Reason: {Reason}", 
                request.RatingId, request.Reason);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error flagging service provider rating: {RatingId}", request.RatingId);
            throw;
        }
    }
}

public class RemoveServiceProviderRatingCommandHandler : IRequestHandler<RemoveServiceProviderRatingCommand, bool>
{
    private readonly IServiceProviderRatingRepository _ratingRepository;
    private readonly ILogger<RemoveServiceProviderRatingCommandHandler> _logger;

    public RemoveServiceProviderRatingCommandHandler(
        IServiceProviderRatingRepository ratingRepository,
        ILogger<RemoveServiceProviderRatingCommandHandler> logger)
    {
        _ratingRepository = ratingRepository;
        _logger = logger;
    }

    public async Task<bool> Handle(RemoveServiceProviderRatingCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var rating = await _ratingRepository.GetByIdAsync(request.RatingId, cancellationToken);
            if (rating == null)
            {
                _logger.LogWarning("Service provider rating not found: {RatingId}", request.RatingId);
                return false;
            }

            rating.Remove(request.Reason);
            await _ratingRepository.UpdateAsync(rating, cancellationToken);

            _logger.LogInformation("Service provider rating removed: {RatingId}, Reason: {Reason}", 
                request.RatingId, request.Reason);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing service provider rating: {RatingId}", request.RatingId);
            throw;
        }
    }
}

public class ReportServiceIssueCommandHandler : IRequestHandler<ReportServiceIssueCommand, Guid>
{
    private readonly IServiceProviderRatingRepository _ratingRepository;
    private readonly ILogger<ReportServiceIssueCommandHandler> _logger;

    public ReportServiceIssueCommandHandler(
        IServiceProviderRatingRepository ratingRepository,
        ILogger<ReportServiceIssueCommandHandler> logger)
    {
        _ratingRepository = ratingRepository;
        _logger = logger;
    }

    public async Task<Guid> Handle(ReportServiceIssueCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var rating = await _ratingRepository.GetByIdAsync(request.ServiceProviderRatingId, cancellationToken);
            if (rating == null)
            {
                throw new InvalidOperationException($"Service provider rating not found: {request.ServiceProviderRatingId}");
            }

            rating.ReportIssue(request.IssueType, request.Priority, request.Description, request.Evidence);
            await _ratingRepository.UpdateAsync(rating, cancellationToken);

            var reportedIssue = rating.ReportedIssues.OrderByDescending(i => i.CreatedAt).First();

            _logger.LogInformation("Service issue reported: {IssueId} for rating: {RatingId}", 
                reportedIssue.Id, request.ServiceProviderRatingId);

            return reportedIssue.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reporting service issue for rating: {RatingId}", request.ServiceProviderRatingId);
            throw;
        }
    }
}

public class AddPreferredProviderCommandHandler : IRequestHandler<AddPreferredProviderCommand, Guid>
{
    private readonly IPreferredProviderNetworkRepository _networkRepository;
    private readonly ILogger<AddPreferredProviderCommandHandler> _logger;

    public AddPreferredProviderCommandHandler(
        IPreferredProviderNetworkRepository networkRepository,
        ILogger<AddPreferredProviderCommandHandler> logger)
    {
        _networkRepository = networkRepository;
        _logger = logger;
    }

    public async Task<Guid> Handle(AddPreferredProviderCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Check if already exists
            var existing = await _networkRepository.GetByShipperAndProviderAsync(
                request.ShipperId, request.TransportCompanyId, cancellationToken);
            
            if (existing != null)
            {
                if (!existing.IsActive)
                {
                    existing.Reactivate();
                    await _networkRepository.UpdateAsync(existing, cancellationToken);
                    return existing.Id;
                }
                throw new InvalidOperationException("Transport company is already in preferred provider network");
            }

            var network = new PreferredProviderNetwork(
                request.ShipperId,
                request.ShipperName,
                request.TransportCompanyId,
                request.TransportCompanyName,
                request.AverageRating,
                request.TotalOrders,
                request.CompletedOrders,
                request.FirstOrderDate,
                request.LastOrderDate,
                request.Notes);

            var savedNetwork = await _networkRepository.AddAsync(network, cancellationToken);

            _logger.LogInformation("Added preferred provider: {TransportCompanyId} for shipper: {ShipperId}", 
                request.TransportCompanyId, request.ShipperId);

            return savedNetwork.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding preferred provider: {TransportCompanyId} for shipper: {ShipperId}", 
                request.TransportCompanyId, request.ShipperId);
            throw;
        }
    }
}
