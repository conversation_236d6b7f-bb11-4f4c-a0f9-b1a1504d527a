namespace FinancialPayment.Application.Interfaces;

public interface IPerformanceTestingService
{
    Task<PerformanceTestResult> RunLoadTestAsync(LoadTestConfiguration configuration);
    Task<PerformanceTestResult> RunStressTestAsync(StressTestConfiguration configuration);
    Task<PerformanceTestResult> RunSpikeTestAsync(SpikeTestConfiguration configuration);
    Task<PerformanceTestResult> RunEnduranceTestAsync(EnduranceTestConfiguration configuration);
    Task<List<PerformanceTestResult>> GetTestResultsAsync(DateTime? fromDate = null, DateTime? toDate = null);
    Task<PerformanceTestResult?> GetTestResultAsync(Guid testId);
    Task<bool> StopTestAsync(Guid testId);
    Task<List<PerformanceTestScenario>> GetTestScenariosAsync();
    Task<PerformanceTestScenario> CreateTestScenarioAsync(CreateTestScenarioRequest request);
    Task<PerformanceBenchmark> GetPerformanceBenchmarkAsync();
    Task<PerformanceComparison> CompareTestResultsAsync(Guid baselineTestId, Guid comparisonTestId);
}

public interface ILoadGenerator
{
    Task<LoadTestExecution> StartLoadTestAsync(LoadTestConfiguration configuration);
    Task<StressTestExecution> StartStressTestAsync(StressTestConfiguration configuration);
    Task<SpikeTestExecution> StartSpikeTestAsync(SpikeTestConfiguration configuration);
    Task<EnduranceTestExecution> StartEnduranceTestAsync(EnduranceTestConfiguration configuration);
    Task StopTestAsync(Guid executionId);
    Task<TestExecutionStatus> GetExecutionStatusAsync(Guid executionId);
}

public interface IPerformanceMetricsCollector
{
    Task<PerformanceMetrics> CollectMetricsAsync(TimeSpan duration);
    Task<SystemResourceMetrics> CollectSystemResourcesAsync();
    Task<DatabasePerformanceMetrics> CollectDatabaseMetricsAsync();
    Task<CachePerformanceMetrics> CollectCacheMetricsAsync();
    Task<ApiPerformanceMetrics> CollectApiMetricsAsync(string endpoint);
    Task StartMetricsCollectionAsync(Guid testId);
    Task StopMetricsCollectionAsync(Guid testId);
}

public interface ITestScenarioExecutor
{
    Task<ScenarioExecutionResult> ExecuteScenarioAsync(PerformanceTestScenario scenario, TestExecutionContext context);
    Task<List<ScenarioExecutionResult>> ExecuteMultipleScenariosAsync(List<PerformanceTestScenario> scenarios, TestExecutionContext context);
}

// Configuration Classes
public class LoadTestConfiguration
{
    public string TestName { get; set; } = string.Empty;
    public int ConcurrentUsers { get; set; } = 10;
    public TimeSpan Duration { get; set; } = TimeSpan.FromMinutes(5);
    public TimeSpan RampUpTime { get; set; } = TimeSpan.FromMinutes(1);
    public List<Guid> ScenarioIds { get; set; } = new();
    public Dictionary<string, object> Parameters { get; set; } = new();
    public PerformanceThresholds Thresholds { get; set; } = new();
    public bool CollectDetailedMetrics { get; set; } = true;
    public string? Environment { get; set; }
}

public class StressTestConfiguration : LoadTestConfiguration
{
    public int MaxConcurrentUsers { get; set; } = 100;
    public TimeSpan StressRampUpTime { get; set; } = TimeSpan.FromMinutes(2);
    public TimeSpan StressDuration { get; set; } = TimeSpan.FromMinutes(10);
    public int UserIncrementStep { get; set; } = 10;
    public TimeSpan IncrementInterval { get; set; } = TimeSpan.FromSeconds(30);
}

public class SpikeTestConfiguration : LoadTestConfiguration
{
    public int SpikeUsers { get; set; } = 200;
    public TimeSpan SpikeDuration { get; set; } = TimeSpan.FromMinutes(2);
    public int NumberOfSpikes { get; set; } = 3;
    public TimeSpan TimeBetweenSpikes { get; set; } = TimeSpan.FromMinutes(5);
}

public class EnduranceTestConfiguration : LoadTestConfiguration
{
    public TimeSpan EnduranceDuration { get; set; } = TimeSpan.FromHours(2);
    public bool MonitorMemoryLeaks { get; set; } = true;
    public bool MonitorResourceDegradation { get; set; } = true;
}

public class PerformanceThresholds
{
    public TimeSpan MaxResponseTime { get; set; } = TimeSpan.FromSeconds(2);
    public decimal MinSuccessRate { get; set; } = 95.0m;
    public int MaxErrorRate { get; set; } = 5;
    public decimal MaxCpuUsage { get; set; } = 80.0m;
    public decimal MaxMemoryUsage { get; set; } = 80.0m;
    public int MaxDatabaseConnections { get; set; } = 100;
    public TimeSpan MaxDatabaseResponseTime { get; set; } = TimeSpan.FromMilliseconds(500);
}

// Result Classes
public class PerformanceTestResult
{
    public Guid TestId { get; set; }
    public string TestName { get; set; } = string.Empty;
    public string TestType { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public TimeSpan Duration { get; set; }
    public TestStatus Status { get; set; }
    public PerformanceMetrics Metrics { get; set; } = new();
    public List<ScenarioExecutionResult> ScenarioResults { get; set; } = new();
    public List<PerformanceIssue> Issues { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
    public string? ErrorMessage { get; set; }
    public PerformanceGrade Grade { get; set; }
    public List<PerformanceRecommendation> Recommendations { get; set; } = new();
}

public class PerformanceMetrics
{
    public int TotalRequests { get; set; }
    public int SuccessfulRequests { get; set; }
    public int FailedRequests { get; set; }
    public decimal SuccessRate { get; set; }
    public decimal ErrorRate { get; set; }
    public TimeSpan AverageResponseTime { get; set; }
    public TimeSpan MinResponseTime { get; set; }
    public TimeSpan MaxResponseTime { get; set; }
    public TimeSpan P50ResponseTime { get; set; }
    public TimeSpan P90ResponseTime { get; set; }
    public TimeSpan P95ResponseTime { get; set; }
    public TimeSpan P99ResponseTime { get; set; }
    public decimal RequestsPerSecond { get; set; }
    public decimal ThroughputMBps { get; set; }
    public SystemResourceMetrics SystemResources { get; set; } = new();
    public DatabasePerformanceMetrics Database { get; set; } = new();
    public CachePerformanceMetrics Cache { get; set; } = new();
    public List<ApiPerformanceMetrics> ApiMetrics { get; set; } = new();
}

public class SystemResourceMetrics
{
    public decimal CpuUsagePercent { get; set; }
    public decimal MemoryUsagePercent { get; set; }
    public long MemoryUsageBytes { get; set; }
    public decimal DiskUsagePercent { get; set; }
    public decimal NetworkInMBps { get; set; }
    public decimal NetworkOutMBps { get; set; }
    public int ActiveThreads { get; set; }
    public int TotalThreads { get; set; }
    public long GarbageCollections { get; set; }
    public TimeSpan GcTime { get; set; }
}

public class DatabasePerformanceMetrics
{
    public int ActiveConnections { get; set; }
    public int TotalConnections { get; set; }
    public TimeSpan AverageQueryTime { get; set; }
    public TimeSpan MaxQueryTime { get; set; }
    public int SlowQueries { get; set; }
    public int DeadlockCount { get; set; }
    public decimal CacheHitRatio { get; set; }
    public long TotalQueries { get; set; }
    public long FailedQueries { get; set; }
}

public class CachePerformanceMetrics
{
    public decimal HitRatio { get; set; }
    public long TotalRequests { get; set; }
    public long Hits { get; set; }
    public long Misses { get; set; }
    public TimeSpan AverageResponseTime { get; set; }
    public long MemoryUsageBytes { get; set; }
    public int KeyCount { get; set; }
    public int ExpiredKeys { get; set; }
}

public class ApiPerformanceMetrics
{
    public string Endpoint { get; set; } = string.Empty;
    public string Method { get; set; } = string.Empty;
    public int RequestCount { get; set; }
    public TimeSpan AverageResponseTime { get; set; }
    public TimeSpan MaxResponseTime { get; set; }
    public decimal SuccessRate { get; set; }
    public Dictionary<string, int> StatusCodeDistribution { get; set; } = new();
    public List<TimeSpan> ResponseTimes { get; set; } = new();
}

public class ScenarioExecutionResult
{
    public Guid ScenarioId { get; set; }
    public string ScenarioName { get; set; } = string.Empty;
    public int ExecutionCount { get; set; }
    public int SuccessfulExecutions { get; set; }
    public int FailedExecutions { get; set; }
    public decimal SuccessRate { get; set; }
    public TimeSpan AverageExecutionTime { get; set; }
    public TimeSpan MinExecutionTime { get; set; }
    public TimeSpan MaxExecutionTime { get; set; }
    public List<string> Errors { get; set; } = new();
    public Dictionary<string, object> Metrics { get; set; } = new();
}

public class PerformanceTestScenario
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string ScenarioType { get; set; } = string.Empty; // Payment, Subscription, Analytics, etc.
    public List<TestStep> Steps { get; set; } = new();
    public Dictionary<string, object> Parameters { get; set; } = new();
    public int Weight { get; set; } = 1; // For weighted execution
    public bool IsEnabled { get; set; } = true;
    public DateTime CreatedAt { get; set; }
    public Guid? CreatedBy { get; set; }
}

public class TestStep
{
    public int Order { get; set; }
    public string StepName { get; set; } = string.Empty;
    public string Action { get; set; } = string.Empty; // HTTP_REQUEST, DATABASE_QUERY, CACHE_OPERATION, etc.
    public Dictionary<string, object> Parameters { get; set; } = new();
    public TimeSpan? Delay { get; set; }
    public List<TestAssertion> Assertions { get; set; } = new();
}

public class TestAssertion
{
    public string Property { get; set; } = string.Empty;
    public string Operator { get; set; } = string.Empty; // EQUALS, GREATER_THAN, LESS_THAN, CONTAINS, etc.
    public object ExpectedValue { get; set; } = new();
    public string? ErrorMessage { get; set; }
}

public class PerformanceIssue
{
    public string IssueType { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty; // Low, Medium, High, Critical
    public string Description { get; set; } = string.Empty;
    public string Component { get; set; } = string.Empty;
    public Dictionary<string, object> Details { get; set; } = new();
    public DateTime DetectedAt { get; set; }
}

public class PerformanceRecommendation
{
    public string Category { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Priority { get; set; } = string.Empty; // Low, Medium, High
    public List<string> ActionItems { get; set; } = new();
    public string? EstimatedImpact { get; set; }
}

public class CreateTestScenarioRequest
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string ScenarioType { get; set; } = string.Empty;
    public List<TestStep> Steps { get; set; } = new();
    public Dictionary<string, object> Parameters { get; set; } = new();
    public int Weight { get; set; } = 1;
    public Guid? CreatedBy { get; set; }
}

public class PerformanceBenchmark
{
    public DateTime BenchmarkDate { get; set; }
    public string Environment { get; set; } = string.Empty;
    public PerformanceMetrics BaselineMetrics { get; set; } = new();
    public Dictionary<string, PerformanceMetrics> ScenarioBenchmarks { get; set; } = new();
    public List<PerformanceTarget> Targets { get; set; } = new();
}

public class PerformanceTarget
{
    public string MetricName { get; set; } = string.Empty;
    public object TargetValue { get; set; } = new();
    public string Operator { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
}

public class PerformanceComparison
{
    public Guid BaselineTestId { get; set; }
    public Guid ComparisonTestId { get; set; }
    public DateTime ComparisonDate { get; set; }
    public List<MetricComparison> MetricComparisons { get; set; } = new();
    public string OverallTrend { get; set; } = string.Empty; // Improved, Degraded, Stable
    public List<string> SignificantChanges { get; set; } = new();
}

public class MetricComparison
{
    public string MetricName { get; set; } = string.Empty;
    public object BaselineValue { get; set; } = new();
    public object ComparisonValue { get; set; } = new();
    public decimal PercentageChange { get; set; }
    public string Trend { get; set; } = string.Empty; // Improved, Degraded, Stable
    public bool IsSignificant { get; set; }
}

// Execution Classes
public abstract class TestExecution
{
    public Guid Id { get; set; }
    public string TestType { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public TestExecutionStatus Status { get; set; }
    public int CurrentUsers { get; set; }
    public PerformanceMetrics CurrentMetrics { get; set; } = new();
    public string? ErrorMessage { get; set; }
}

public class LoadTestExecution : TestExecution
{
    public LoadTestConfiguration Configuration { get; set; } = new();
}

public class StressTestExecution : TestExecution
{
    public StressTestConfiguration Configuration { get; set; } = new();
}

public class SpikeTestExecution : TestExecution
{
    public SpikeTestConfiguration Configuration { get; set; } = new();
}

public class EnduranceTestExecution : TestExecution
{
    public EnduranceTestConfiguration Configuration { get; set; } = new();
}

public class TestExecutionContext
{
    public Guid TestId { get; set; }
    public int VirtualUserId { get; set; }
    public Dictionary<string, object> SharedData { get; set; } = new();
    public Dictionary<string, object> UserData { get; set; } = new();
    public CancellationToken CancellationToken { get; set; }
}

// Enums
public enum TestStatus
{
    Pending = 1,
    Running = 2,
    Completed = 3,
    Failed = 4,
    Cancelled = 5
}

public enum TestExecutionStatus
{
    NotStarted = 1,
    Starting = 2,
    Running = 3,
    Stopping = 4,
    Completed = 5,
    Failed = 6,
    Cancelled = 7
}

public enum PerformanceGrade
{
    Excellent = 1,
    Good = 2,
    Fair = 3,
    Poor = 4,
    Critical = 5
}

public class PerformanceTestConfiguration
{
    public bool EnablePerformanceTesting { get; set; } = true;
    public int MaxConcurrentTests { get; set; } = 3;
    public int TestResultRetentionDays { get; set; } = 90;
    public string TestDataDirectory { get; set; } = "performance_test_data";
    public Dictionary<string, string> DefaultEndpoints { get; set; } = new();
    public PerformanceThresholds DefaultThresholds { get; set; } = new();
    public bool EnableRealTimeMonitoring { get; set; } = true;
    public int MetricsCollectionIntervalSeconds { get; set; } = 5;
    public List<string> NotificationChannels { get; set; } = new();
}
