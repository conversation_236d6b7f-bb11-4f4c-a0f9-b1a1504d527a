using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Domain.Enums;
using MediatR;
using Shared.Infrastructure.Repositories;

namespace AnalyticsBIService.Application.Queries.Admin;

/// <summary>
/// Query to get admin platform performance dashboard
/// </summary>
public record GetAdminPlatformDashboardQuery(
    DateTime? FromDate = null,
    DateTime? ToDate = null,
    TimePeriod Period = TimePeriod.Daily
) : IRequest<AdminPlatformDashboardDto>;

/// <summary>
/// Query to get platform performance metrics for admin
/// </summary>
public record GetPlatformPerformanceMetricsQuery(
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily
) : IRequest<PlatformPerformanceMetricsDto>;

/// <summary>
/// Query to get real-time KPI monitoring for admin
/// </summary>
public record GetRealTimeKPIMonitoringQuery() : IRequest<RealTimeKPIMonitoringDto>;

/// <summary>
/// Query to get revenue analytics for admin
/// </summary>
public record GetRevenueAnalyticsQuery(
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    bool IncludeSubscriptionBreakdown = true,
    bool IncludeChurnAnalysis = true
) : IRequest<RevenueAnalyticsDto>;

/// <summary>
/// Query to get user growth tracking for admin
/// </summary>
public record GetUserGrowthTrackingQuery(
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    UserType? UserType = null
) : IRequest<UserGrowthTrackingDto>;

/// <summary>
/// Query to get geographic performance analysis for admin
/// </summary>
public record GetGeographicPerformanceQuery(
    DateTime FromDate,
    DateTime ToDate,
    string? Region = null,
    string? Country = null,
    string? State = null
) : IRequest<GeographicPerformanceDto>;

/// <summary>
/// Query to get business intelligence reports for admin
/// </summary>
public record GetBusinessIntelligenceReportsQuery(
    DateTime FromDate,
    DateTime ToDate,
    ReportType? ReportType = null,
    bool IncludeComplianceReports = true
) : IRequest<List<BusinessIntelligenceReportDto>>;

/// <summary>
/// Query to get customer NPS tracking for admin
/// </summary>
public record GetCustomerNPSTrackingQuery(
    DateTime FromDate,
    DateTime ToDate,
    UserType? UserType = null,
    TimePeriod Period = TimePeriod.Monthly
) : IRequest<CustomerNPSTrackingDto>;

/// <summary>
/// Query to get subscription analytics for admin
/// </summary>
public record GetSubscriptionAnalyticsQuery(
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    bool IncludeTierDistribution = true,
    bool IncludeConversionAnalysis = true
) : IRequest<SubscriptionAnalyticsDto>;

/// <summary>
/// Query to get platform usage reports for admin
/// </summary>
public record GetPlatformUsageReportsQuery(
    DateTime FromDate,
    DateTime ToDate,
    UserType? UserType = null,
    DataSourceType? DataSource = null,
    TimePeriod Period = TimePeriod.Daily
) : IRequest<PlatformUsageReportDto>;

/// <summary>
/// Query to get regulatory compliance reports for admin
/// </summary>
public record GetRegulatoryComplianceReportsQuery(
    DateTime FromDate,
    DateTime ToDate,
    string? ComplianceType = null,
    string? Region = null
) : IRequest<List<RegulatoryComplianceReportDto>>;

/// <summary>
/// Query to get audit trail reports for admin
/// </summary>
public record GetAuditTrailReportsQuery(
    DateTime FromDate,
    DateTime ToDate,
    string? EntityType = null,
    string? Action = null,
    Guid? UserId = null,
    int PageNumber = 1,
    int PageSize = 50
) : IRequest<PagedResult<AuditTrailDto>>;

/// <summary>
/// Query to get audit trail for admin compliance
/// </summary>
public record GetAuditTrailQuery(
    DateTime FromDate,
    DateTime ToDate,
    Guid? UserId = null,
    string? Action = null,
    string? EntityType = null,
    int Page = 1,
    int PageSize = 50
) : IRequest<PagedResult<AuditTrailDto>>;

/// <summary>
/// Query to get custom report data for admin
/// </summary>
public record GetCustomReportDataQuery(
    DateTime FromDate,
    DateTime ToDate,
    Dictionary<string, object>? Configuration = null
) : IRequest<CustomReportDataDto>;

/// <summary>
/// Query to get stakeholder presentation data for admin
/// </summary>
public record GetStakeholderPresentationDataQuery(
    DateTime FromDate,
    DateTime ToDate,
    List<string>? IncludeMetrics = null,
    bool IncludeExecutiveSummary = true,
    bool IncludeFinancialHighlights = true,
    bool IncludeOperationalMetrics = true
) : IRequest<StakeholderPresentationDataDto>;

/// <summary>
/// Query to get custom report builder data for admin
/// </summary>
public record GetCustomReportBuilderDataQuery(
    Dictionary<string, object> ReportConfiguration,
    DateTime FromDate,
    DateTime ToDate,
    List<string>? MetricNames = null,
    List<UserType>? UserTypes = null,
    List<DataSourceType>? DataSources = null
) : IRequest<CustomReportDataDto>;

/// <summary>
/// Query to get transaction volume tracking for admin
/// </summary>
public record GetTransactionVolumeTrackingQuery(
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    DataSourceType? DataSource = null,
    string? TransactionType = null
) : IRequest<TransactionVolumeTrackingDto>;

/// <summary>
/// Query to get market penetration analysis for admin
/// </summary>
public record GetMarketPenetrationAnalysisQuery(
    DateTime FromDate,
    DateTime ToDate,
    string? Region = null,
    string? MarketSegment = null,
    UserType? UserType = null
) : IRequest<MarketPenetrationAnalysisDto>;
