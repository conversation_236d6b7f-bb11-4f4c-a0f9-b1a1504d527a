using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using AnalyticsBIService.Domain.Entities;

namespace AnalyticsBIService.Infrastructure.Persistence.Configurations;

public class ReportConfiguration : IEntityTypeConfiguration<Report>
{
    public void Configure(EntityTypeBuilder<Report> builder)
    {
        builder.ToTable("reports");

        builder.HasKey(r => r.Id);

        builder.Property(r => r.Name)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(r => r.Description)
            .HasMaxLength(1000);

        builder.Property(r => r.Type)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(r => r.Status)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(r => r.CreatedBy)
            .IsRequired();

        builder.Property(r => r.CreatedAt)
            .IsRequired();

        builder.Property(r => r.UpdatedAt)
            .IsRequired();

        builder.Property(r => r.GeneratedAt)
            .IsRequired(false);

        builder.Property(r => r.ExpiresAt)
            .IsRequired(false);

        // Configure Parameters as JSON
        builder.Property(r => r.Parameters)
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        // Configure Metadata as JSON
        builder.Property(r => r.Metadata)
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        // Configure relationships
        builder.HasMany(r => r.Sections)
            .WithOne()
            .HasForeignKey("ReportId")
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(r => r.Exports)
            .WithOne()
            .HasForeignKey("ReportId")
            .OnDelete(DeleteBehavior.Cascade);

        // Configure indexes
        builder.HasIndex(r => r.Type)
            .HasDatabaseName("IX_reports_type");

        builder.HasIndex(r => r.Status)
            .HasDatabaseName("IX_reports_status");

        builder.HasIndex(r => r.CreatedBy)
            .HasDatabaseName("IX_reports_created_by");

        builder.HasIndex(r => r.CreatedAt)
            .HasDatabaseName("IX_reports_created_at");

        builder.HasIndex(r => new { r.CreatedBy, r.Type })
            .HasDatabaseName("IX_reports_created_by_type");
    }
}
