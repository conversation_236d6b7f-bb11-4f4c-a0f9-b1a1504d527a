using Shared.Domain.Common;

namespace AnalyticsBIService.Domain.Entities;

/// <summary>
/// Data synchronization record entity for tracking sync operations
/// </summary>
public class DataSyncRecord : BaseEntity
{
    public Guid ConnectionId { get; private set; }
    public string Status { get; private set; } // Success, Failed, Running, Cancelled
    public DateTime StartedAt { get; private set; }
    public DateTime? CompletedAt { get; private set; }
    public TimeSpan? Duration { get; private set; }
    public long RecordsProcessed { get; private set; }
    public long RecordsInserted { get; private set; }
    public long RecordsUpdated { get; private set; }
    public long RecordsDeleted { get; private set; }
    public string? ErrorMessage { get; private set; }
    public string? Options { get; private set; } // JSON serialized sync options
    public string? TriggerType { get; private set; } // Manual, Scheduled, API

    private DataSyncRecord()
    {
        Status = string.Empty;
    }

    public DataSyncRecord(
        Guid connectionId,
        string? triggerType = null,
        string? options = null)
    {
        ConnectionId = connectionId;
        Status = "Running";
        StartedAt = DateTime.UtcNow;
        TriggerType = triggerType ?? "Manual";
        Options = options ?? "{}";
        RecordsProcessed = 0;
        RecordsInserted = 0;
        RecordsUpdated = 0;
        RecordsDeleted = 0;
    }

    public void CompleteSuccessfully(long recordsProcessed, long recordsInserted, long recordsUpdated, long recordsDeleted)
    {
        Status = "Success";
        CompletedAt = DateTime.UtcNow;
        Duration = CompletedAt - StartedAt;
        RecordsProcessed = recordsProcessed;
        RecordsInserted = recordsInserted;
        RecordsUpdated = recordsUpdated;
        RecordsDeleted = recordsDeleted;
        UpdatedAt = DateTime.UtcNow;
    }

    public void MarkAsFailed(string errorMessage)
    {
        Status = "Failed";
        CompletedAt = DateTime.UtcNow;
        Duration = CompletedAt - StartedAt;
        ErrorMessage = errorMessage ?? throw new ArgumentNullException(nameof(errorMessage));
        UpdatedAt = DateTime.UtcNow;
    }

    public void Cancel()
    {
        Status = "Cancelled";
        CompletedAt = DateTime.UtcNow;
        Duration = CompletedAt - StartedAt;
        UpdatedAt = DateTime.UtcNow;
    }

    public void UpdateProgress(long recordsProcessed)
    {
        RecordsProcessed = recordsProcessed;
        UpdatedAt = DateTime.UtcNow;
    }

    public void CompleteWithDetails(long recordsProcessed, long recordsInserted, long recordsUpdated, long recordsDeleted)
    {
        Status = "Completed";
        CompletedAt = DateTime.UtcNow;
        Duration = CompletedAt - StartedAt;
        RecordsProcessed = recordsProcessed;
        RecordsInserted = recordsInserted;
        RecordsUpdated = recordsUpdated;
        RecordsDeleted = recordsDeleted;
        UpdatedAt = DateTime.UtcNow;
    }

    public void FailWithDetails(TimeSpan duration, string errorMessage)
    {
        Status = "Failed";
        CompletedAt = DateTime.UtcNow;
        Duration = duration;
        ErrorMessage = errorMessage ?? throw new ArgumentNullException(nameof(errorMessage));
        UpdatedAt = DateTime.UtcNow;
    }
}
