# Use the official .NET 8 runtime as base image
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

# Use the official .NET 8 SDK for building
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy project files
COPY ["Services/MobileWorkflow/MobileWorkflow.API/MobileWorkflow.API.csproj", "Services/MobileWorkflow/MobileWorkflow.API/"]
COPY ["Services/MobileWorkflow/MobileWorkflow.Application/MobileWorkflow.Application.csproj", "Services/MobileWorkflow/MobileWorkflow.Application/"]
COPY ["Services/MobileWorkflow/MobileWorkflow.Domain/MobileWorkflow.Domain.csproj", "Services/MobileWorkflow/MobileWorkflow.Domain/"]
COPY ["Services/MobileWorkflow/MobileWorkflow.Infrastructure/MobileWorkflow.Infrastructure.csproj", "Services/MobileWorkflow/MobileWorkflow.Infrastructure/"]
COPY ["Shared/TLI.Shared.Domain/TLI.Shared.Domain.csproj", "Shared/TLI.Shared.Domain/"]
COPY ["Shared/TLI.Shared.Application/TLI.Shared.Application.csproj", "Shared/TLI.Shared.Application/"]
COPY ["Shared/TLI.Shared.Infrastructure/TLI.Shared.Infrastructure.csproj", "Shared/TLI.Shared.Infrastructure/"]
COPY ["Shared/TLI.Shared.API/TLI.Shared.API.csproj", "Shared/TLI.Shared.API/"]

# Restore dependencies
RUN dotnet restore "Services/MobileWorkflow/MobileWorkflow.API/MobileWorkflow.API.csproj"

# Copy all source code
COPY . .

# Build the application
WORKDIR "/src/Services/MobileWorkflow/MobileWorkflow.API"
RUN dotnet build "MobileWorkflow.API.csproj" -c Release -o /app/build

# Publish the application
FROM build AS publish
RUN dotnet publish "MobileWorkflow.API.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Final stage
FROM base AS final
WORKDIR /app

# Install curl for health checks
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

# Copy published application
COPY --from=publish /app/publish .

# Create logs directory
RUN mkdir -p /app/logs

# Set environment variables
ENV ASPNETCORE_ENVIRONMENT=Production
ENV ASPNETCORE_URLS=http://+:80

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost/health || exit 1

# Run the application
ENTRYPOINT ["dotnet", "MobileWorkflow.API.dll"]
