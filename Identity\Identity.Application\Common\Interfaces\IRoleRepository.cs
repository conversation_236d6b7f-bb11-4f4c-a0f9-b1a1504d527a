using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Identity.Domain.Entities;

namespace Identity.Application.Common.Interfaces
{
    public interface IRoleRepository
    {
        Task<Role> GetByIdAsync(Guid id);
        Task<Role> GetByNameAsync(string name);
        Task<IEnumerable<Role>> GetAllAsync();
        Task<IEnumerable<Role>> GetActiveAsync();
        Task<IEnumerable<Role>> GetByTypeAsync(RoleType type);
        Task<IEnumerable<Role>> GetTemplatesAsync();
        Task<IEnumerable<Role>> GetByDepartmentAsync(string department);
        Task<IEnumerable<Role>> GetChildRolesAsync(Guid parentRoleId);
        Task<IEnumerable<Role>> GetUserRolesAsync(Guid userId);
        Task<(IEnumerable<Role> Items, int TotalCount)> GetPagedAsync(
            int pageNumber, 
            int pageSize, 
            string searchTerm = null,
            RoleType? type = null,
            string department = null,
            bool? isActive = null);
        Task AddAsync(Role role);
        Task UpdateAsync(Role role);
        Task DeleteAsync(Guid id);
        Task<bool> ExistsAsync(Guid id);
        Task<bool> ExistsByNameAsync(string name);
        Task<IEnumerable<Role>> GetRolesWithPermissionsAsync();
        Task<IEnumerable<Role>> GetRolesByPermissionAsync(Guid permissionId);
    }
}
