using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace OrderManagement.API.HealthChecks;

public class ExternalServicesHealthCheck : IHealthCheck
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IConfiguration _configuration;
    private readonly ILogger<ExternalServicesHealthCheck> _logger;

    public ExternalServicesHealthCheck(
        IHttpClientFactory httpClientFactory,
        IConfiguration configuration,
        ILogger<ExternalServicesHealthCheck> logger)
    {
        _httpClientFactory = httpClientFactory;
        _configuration = configuration;
        _logger = logger;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        var healthyServices = new List<string>();
        var unhealthyServices = new List<string>();
        var exceptions = new List<Exception>();

        try
        {
            _logger.LogDebug("Checking external services health");

            // Check User Management Service
            await CheckService(
                "UserManagement",
                _configuration["ExternalServices:UserManagement:BaseUrl"],
                healthyServices,
                unhealthyServices,
                exceptions,
                cancellationToken);

            // Check Trip Management Service
            await CheckService(
                "TripManagement",
                _configuration["ExternalServices:TripManagement:BaseUrl"],
                healthyServices,
                unhealthyServices,
                exceptions,
                cancellationToken);

            // Check Financial Payment Service
            await CheckService(
                "FinancialPayment",
                _configuration["ExternalServices:FinancialPayment:BaseUrl"],
                healthyServices,
                unhealthyServices,
                exceptions,
                cancellationToken);

            // Check Network Fleet Management Service
            await CheckService(
                "NetworkFleetManagement",
                _configuration["ExternalServices:NetworkFleetManagement:BaseUrl"],
                healthyServices,
                unhealthyServices,
                exceptions,
                cancellationToken);

            if (unhealthyServices.Any())
            {
                var message = $"Some external services are unhealthy: {string.Join(", ", unhealthyServices)}. " +
                             $"Healthy services: {string.Join(", ", healthyServices)}";
                
                _logger.LogWarning("External services health check partially failed: {Message}", message);
                
                return HealthCheckResult.Degraded(message, exceptions.FirstOrDefault());
            }

            var successMessage = $"All external services are healthy: {string.Join(", ", healthyServices)}";
            _logger.LogDebug("External services health check passed: {Message}", successMessage);
            
            return HealthCheckResult.Healthy(successMessage);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "External services health check failed");
            
            return HealthCheckResult.Unhealthy(
                "Failed to check external services",
                ex);
        }
    }

    private async Task CheckService(
        string serviceName,
        string? baseUrl,
        List<string> healthyServices,
        List<string> unhealthyServices,
        List<Exception> exceptions,
        CancellationToken cancellationToken)
    {
        if (string.IsNullOrEmpty(baseUrl))
        {
            _logger.LogDebug("Skipping {ServiceName} health check - no base URL configured", serviceName);
            return;
        }

        try
        {
            using var httpClient = _httpClientFactory.CreateClient();
            httpClient.Timeout = TimeSpan.FromSeconds(5); // Short timeout for health checks

            var healthUrl = $"{baseUrl.TrimEnd('/')}/health";
            var response = await httpClient.GetAsync(healthUrl, cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                healthyServices.Add(serviceName);
                _logger.LogDebug("{ServiceName} service is healthy", serviceName);
            }
            else
            {
                unhealthyServices.Add(serviceName);
                var exception = new HttpRequestException($"{serviceName} returned {response.StatusCode}");
                exceptions.Add(exception);
                _logger.LogWarning("{ServiceName} service returned {StatusCode}", serviceName, response.StatusCode);
            }
        }
        catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
        {
            unhealthyServices.Add(serviceName);
            exceptions.Add(ex);
            _logger.LogWarning("{ServiceName} service health check timed out", serviceName);
        }
        catch (Exception ex)
        {
            unhealthyServices.Add(serviceName);
            exceptions.Add(ex);
            _logger.LogWarning(ex, "{ServiceName} service health check failed", serviceName);
        }
    }
}
