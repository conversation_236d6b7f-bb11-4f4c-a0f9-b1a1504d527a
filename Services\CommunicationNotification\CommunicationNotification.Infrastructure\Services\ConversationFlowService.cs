using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Domain.Entities;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;
using Microsoft.Extensions.Logging;

namespace CommunicationNotification.Infrastructure.Services;

/// <summary>
/// Service for managing conversation flow and executing actions
/// </summary>
public class ConversationFlowService : IConversationFlowService
{
    private readonly ILogger<ConversationFlowService> _logger;

    public ConversationFlowService(ILogger<ConversationFlowService> logger)
    {
        _logger = logger;
    }

    public async Task<ConversationFlowResult> DetermineNextActionAsync(
        ChatbotConversation conversation,
        NLPResult nlpResult,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Determining next action for conversation {SessionId} with intent {Intent}",
                conversation.SessionId, nlpResult.Intent);

            var result = new ConversationFlowResult();

            if (string.IsNullOrEmpty(nlpResult.Intent))
            {
                result.NextAction = "fallback";
                result.RequiresUserInput = true;
                return result;
            }

            // Determine action based on intent
            result.NextAction = nlpResult.Intent switch
            {
                "greeting" => "respond_greeting",
                "goodbye" => "end_conversation",
                "help" => "provide_help",
                "booking" => "process_booking",
                "status" => "check_status",
                "cancel" => "process_cancellation",
                "complaint" => "handle_complaint",
                "thanks" => "acknowledge_thanks",
                _ => "fallback"
            };

            // Set parameters based on extracted entities
            result.Parameters = nlpResult.Entities.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

            // Determine if user input is required
            result.RequiresUserInput = result.NextAction switch
            {
                "process_booking" => !HasRequiredBookingEntities(nlpResult.Entities),
                "check_status" => !HasRequiredStatusEntities(nlpResult.Entities),
                "process_cancellation" => !HasRequiredCancellationEntities(nlpResult.Entities),
                "end_conversation" => false,
                _ => false
            };

            _logger.LogDebug("Determined next action: {NextAction} for conversation {SessionId}",
                result.NextAction, conversation.SessionId);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error determining next action for conversation {SessionId}", conversation.SessionId);
            return new ConversationFlowResult { NextAction = "fallback", RequiresUserInput = true };
        }
    }

    public async Task<ActionExecutionResult> ExecuteActionAsync(
        ChatbotAction action,
        ChatbotConversation conversation,
        Dictionary<string, object> parameters,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Executing action {ActionName} of type {ActionType} for conversation {SessionId}",
                action.Name, action.Type, conversation.SessionId);

            return action.Type switch
            {
                ActionType.SendMessage => await ExecuteSendMessageAsync(action, conversation, parameters, cancellationToken),
                ActionType.TransferToAgent => await ExecuteTransferToAgentAsync(action, conversation, parameters, cancellationToken),
                ActionType.CreateTicket => await ExecuteCreateTicketAsync(action, conversation, parameters, cancellationToken),
                ActionType.BookTrip => await ExecuteBookTripAsync(action, conversation, parameters, cancellationToken),
                ActionType.CheckStatus => await ExecuteCheckStatusAsync(action, conversation, parameters, cancellationToken),
                ActionType.SendEmail => await ExecuteSendEmailAsync(action, conversation, parameters, cancellationToken),
                ActionType.SendSMS => await ExecuteSendSMSAsync(action, conversation, parameters, cancellationToken),
                ActionType.CallWebhook => await ExecuteCallWebhookAsync(action, conversation, parameters, cancellationToken),
                ActionType.SetVariable => await ExecuteSetVariableAsync(action, conversation, parameters, cancellationToken),
                ActionType.EndConversation => await ExecuteEndConversationAsync(action, conversation, parameters, cancellationToken),
                _ => new ActionExecutionResult { IsSuccess = false, ErrorMessage = $"Unsupported action type: {action.Type}" }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing action {ActionName} for conversation {SessionId}",
                action.Name, conversation.SessionId);
            return new ActionExecutionResult
            {
                IsSuccess = false,
                ErrorMessage = $"Error executing action: {ex.Message}"
            };
        }
    }

    public async Task<string> GenerateContextualResponseAsync(
        ChatbotIntent intent,
        ChatbotConversation conversation,
        Dictionary<string, object> entities,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Generating contextual response for intent {IntentName} in conversation {SessionId}",
                intent.Name, conversation.SessionId);

            var response = intent.Responses.FirstOrDefault()?.GetRandomVariation() ?? "I understand.";

            // Personalize response with user name and extracted entities
            response = PersonalizeResponse(response, conversation, entities);

            _logger.LogDebug("Generated contextual response for intent {IntentName}", intent.Name);
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating contextual response for intent {IntentName}", intent.Name);
            return "I understand. How can I help you further?";
        }
    }

    public async Task<bool> AreRequiredEntitiesCollectedAsync(
        ChatbotIntent intent,
        ChatbotConversation conversation,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var missingEntities = await GetMissingRequiredEntitiesAsync(intent, conversation, cancellationToken);
            return !missingEntities.Any();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking required entities for intent {IntentName}", intent.Name);
            return false;
        }
    }

    public async Task<List<string>> GetMissingRequiredEntitiesAsync(
        ChatbotIntent intent,
        ChatbotConversation conversation,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var missingEntities = new List<string>();

            foreach (var requiredEntity in intent.RequiredEntities)
            {
                if (!conversation.ExtractedEntities.ContainsKey(requiredEntity))
                {
                    missingEntities.Add(requiredEntity);
                }
            }

            _logger.LogDebug("Found {Count} missing required entities for intent {IntentName}",
                missingEntities.Count, intent.Name);

            return missingEntities;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting missing required entities for intent {IntentName}", intent.Name);
            return new List<string>();
        }
    }

    // Private helper methods for action execution
    private async Task<ActionExecutionResult> ExecuteSendMessageAsync(
        ChatbotAction action,
        ChatbotConversation conversation,
        Dictionary<string, object> parameters,
        CancellationToken cancellationToken)
    {
        var message = action.Configuration;
        message = PersonalizeResponse(message, conversation, parameters);

        return new ActionExecutionResult
        {
            IsSuccess = true,
            Response = message
        };
    }

    private async Task<ActionExecutionResult> ExecuteTransferToAgentAsync(
        ChatbotAction action,
        ChatbotConversation conversation,
        Dictionary<string, object> parameters,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Transferring conversation {SessionId} to human agent", conversation.SessionId);

        return new ActionExecutionResult
        {
            IsSuccess = true,
            Response = "I'm transferring you to one of our human agents who can better assist you. Please hold on.",
            ShouldContinueConversation = false
        };
    }

    private async Task<ActionExecutionResult> ExecuteCreateTicketAsync(
        ChatbotAction action,
        ChatbotConversation conversation,
        Dictionary<string, object> parameters,
        CancellationToken cancellationToken)
    {
        // Simulate ticket creation
        var ticketId = $"TKT-{DateTime.UtcNow:yyyyMMdd}-{new Random().Next(1000, 9999)}";
        
        _logger.LogInformation("Created support ticket {TicketId} for conversation {SessionId}",
            ticketId, conversation.SessionId);

        return new ActionExecutionResult
        {
            IsSuccess = true,
            Response = $"I've created a support ticket for you. Your ticket number is {ticketId}. Our team will get back to you soon.",
            Data = new Dictionary<string, object> { ["ticketId"] = ticketId }
        };
    }

    private async Task<ActionExecutionResult> ExecuteBookTripAsync(
        ChatbotAction action,
        ChatbotConversation conversation,
        Dictionary<string, object> parameters,
        CancellationToken cancellationToken)
    {
        // Simulate trip booking
        var bookingId = $"BKG-{DateTime.UtcNow:yyyyMMdd}-{new Random().Next(1000, 9999)}";
        
        _logger.LogInformation("Created trip booking {BookingId} for conversation {SessionId}",
            bookingId, conversation.SessionId);

        return new ActionExecutionResult
        {
            IsSuccess = true,
            Response = $"Great! I've initiated your trip booking. Your booking reference is {bookingId}. You'll receive a confirmation shortly.",
            Data = new Dictionary<string, object> { ["bookingId"] = bookingId }
        };
    }

    private async Task<ActionExecutionResult> ExecuteCheckStatusAsync(
        ChatbotAction action,
        ChatbotConversation conversation,
        Dictionary<string, object> parameters,
        CancellationToken cancellationToken)
    {
        // Simulate status check
        var trackingNumber = parameters.GetValueOrDefault("trackingNumber", "Unknown")?.ToString();
        
        _logger.LogInformation("Checking status for tracking number {TrackingNumber} in conversation {SessionId}",
            trackingNumber, conversation.SessionId);

        return new ActionExecutionResult
        {
            IsSuccess = true,
            Response = $"Your shipment {trackingNumber} is currently in transit and expected to arrive tomorrow by 5 PM.",
            Data = new Dictionary<string, object> 
            { 
                ["status"] = "In Transit",
                ["estimatedDelivery"] = DateTime.Today.AddDays(1).ToString("yyyy-MM-dd")
            }
        };
    }

    private async Task<ActionExecutionResult> ExecuteSendEmailAsync(
        ChatbotAction action,
        ChatbotConversation conversation,
        Dictionary<string, object> parameters,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Sending email for conversation {SessionId}", conversation.SessionId);

        return new ActionExecutionResult
        {
            IsSuccess = true,
            Response = "I've sent you an email with the details. Please check your inbox.",
            Data = new Dictionary<string, object> { ["emailSent"] = true }
        };
    }

    private async Task<ActionExecutionResult> ExecuteSendSMSAsync(
        ChatbotAction action,
        ChatbotConversation conversation,
        Dictionary<string, object> parameters,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Sending SMS for conversation {SessionId}", conversation.SessionId);

        return new ActionExecutionResult
        {
            IsSuccess = true,
            Response = "I've sent you an SMS with the confirmation details.",
            Data = new Dictionary<string, object> { ["smsSent"] = true }
        };
    }

    private async Task<ActionExecutionResult> ExecuteCallWebhookAsync(
        ChatbotAction action,
        ChatbotConversation conversation,
        Dictionary<string, object> parameters,
        CancellationToken cancellationToken)
    {
        var webhookUrl = action.Configuration;
        _logger.LogInformation("Calling webhook {WebhookUrl} for conversation {SessionId}",
            webhookUrl, conversation.SessionId);

        return new ActionExecutionResult
        {
            IsSuccess = true,
            Response = "I've processed your request and updated our systems.",
            Data = new Dictionary<string, object> { ["webhookCalled"] = true }
        };
    }

    private async Task<ActionExecutionResult> ExecuteSetVariableAsync(
        ChatbotAction action,
        ChatbotConversation conversation,
        Dictionary<string, object> parameters,
        CancellationToken cancellationToken)
    {
        var parts = action.Configuration.Split('=', 2);
        if (parts.Length == 2)
        {
            conversation.AddContext(parts[0], parts[1]);
            _logger.LogDebug("Set variable {Key} = {Value} for conversation {SessionId}",
                parts[0], parts[1], conversation.SessionId);
        }

        return new ActionExecutionResult
        {
            IsSuccess = true,
            Response = null // No response needed for variable setting
        };
    }

    private async Task<ActionExecutionResult> ExecuteEndConversationAsync(
        ChatbotAction action,
        ChatbotConversation conversation,
        Dictionary<string, object> parameters,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Ending conversation {SessionId}", conversation.SessionId);

        return new ActionExecutionResult
        {
            IsSuccess = true,
            Response = "Thank you for using our service. Have a great day!",
            ShouldContinueConversation = false
        };
    }

    // Helper methods
    private bool HasRequiredBookingEntities(Dictionary<string, object> entities)
    {
        return entities.ContainsKey("origin") && entities.ContainsKey("destination");
    }

    private bool HasRequiredStatusEntities(Dictionary<string, object> entities)
    {
        return entities.ContainsKey("trackingNumber") || entities.ContainsKey("bookingId");
    }

    private bool HasRequiredCancellationEntities(Dictionary<string, object> entities)
    {
        return entities.ContainsKey("bookingId") || entities.ContainsKey("referenceNumber");
    }

    private string PersonalizeResponse(string response, ChatbotConversation conversation, Dictionary<string, object> entities)
    {
        // Replace placeholders with actual values
        response = response.Replace("{userName}", conversation.UserName);
        response = response.Replace("{userId}", conversation.UserId.ToString());

        // Replace entity placeholders
        foreach (var entity in entities)
        {
            response = response.Replace($"{{{entity.Key}}}", entity.Value?.ToString() ?? "");
        }

        // Replace context placeholders
        foreach (var context in conversation.Context)
        {
            response = response.Replace($"{{{context.Key}}}", context.Value);
        }

        return response;
    }
}
