using AuditCompliance.Application.DTOs.Analytics;
using Shared.Domain.Common;

namespace AuditCompliance.Application.Interfaces;

/// <summary>
/// Service for advanced analytics and machine learning-based compliance prediction
/// </summary>
public interface IAdvancedAnalyticsService
{
    /// <summary>
    /// Predict compliance risk score for an entity
    /// </summary>
    Task<ComplianceRiskPredictionDto> PredictComplianceRiskAsync(
        ComplianceRiskInputDto input, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Predict potential compliance violations
    /// </summary>
    Task<List<ViolationPredictionDto>> PredictViolationsAsync(
        Guid entityId, 
        string entityType, 
        DateTime predictionPeriod,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Analyze compliance trends and patterns
    /// </summary>
    Task<ComplianceTrendAnalysisDto> AnalyzeComplianceTrendsAsync(
        ComplianceTrendInputDto input,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get anomaly detection results for compliance data
    /// </summary>
    Task<List<ComplianceAnomalyDto>> DetectComplianceAnomaliesAsync(
        DateTime fromDate,
        DateTime toDate,
        string? entityType = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Generate compliance insights and recommendations
    /// </summary>
    Task<ComplianceInsightsDto> GenerateComplianceInsightsAsync(
        Guid? organizationId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Train or retrain ML models with latest data
    /// </summary>
    Task<ModelTrainingResultDto> TrainModelsAsync(
        List<string>? modelTypes = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get model performance metrics
    /// </summary>
    Task<List<ModelPerformanceDto>> GetModelPerformanceAsync(
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Predict compliance score for future periods
    /// </summary>
    Task<List<ComplianceScoreForecastDto>> ForecastComplianceScoreAsync(
        Guid entityId,
        string entityType,
        int forecastDays = 30,
        CancellationToken cancellationToken = default);
}

