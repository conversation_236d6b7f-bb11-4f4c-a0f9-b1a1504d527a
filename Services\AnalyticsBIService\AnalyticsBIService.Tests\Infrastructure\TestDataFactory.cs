using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Enums;
using AnalyticsBIService.Domain.ValueObjects;
using Bogus;

namespace AnalyticsBIService.Tests.Infrastructure;

/// <summary>
/// Test data factory for creating test entities using proper domain entity constructors
/// </summary>
public static class TestDataFactory
{
    private static readonly Faker Faker = new();

    /// <summary>
    /// Generate random GUID
    /// </summary>
    public static Guid RandomGuid() => Guid.NewGuid();

    /// <summary>
    /// Generate random string
    /// </summary>
    public static string RandomString(int length = 10) => Faker.Random.String2(length);

    /// <summary>
    /// Generate random email
    /// </summary>
    public static string RandomEmail() => Faker.Internet.Email();

    /// <summary>
    /// Generate random date in the past
    /// </summary>
    public static DateTime RandomPastDate(int maxDaysAgo = 30) => Faker.Date.Past(maxDaysAgo);

    /// <summary>
    /// Generate random date in the future
    /// </summary>
    public static DateTime RandomFutureDate(int maxDaysAhead = 30) => Faker.Date.Future(maxDaysAhead);

    /// <summary>
    /// Create a test TimePeriodValue
    /// </summary>
    public static TimePeriodValue CreateTestTimePeriod(TimePeriod period = TimePeriod.Daily)
    {
        var endDate = DateTime.UtcNow;
        var startDate = period switch
        {
            TimePeriod.Daily => endDate.AddDays(-1),
            TimePeriod.Weekly => endDate.AddDays(-7),
            TimePeriod.Monthly => endDate.AddMonths(-1),
            TimePeriod.Quarterly => endDate.AddMonths(-3),
            TimePeriod.Yearly => endDate.AddYears(-1),
            _ => endDate.AddDays(-1)
        };

        return new TimePeriodValue(startDate, endDate, period);
    }

    /// <summary>
    /// Create a test Report entity using proper constructor
    /// </summary>
    public static Report CreateTestReport(
        string? name = null,
        string? description = null,
        ReportType? reportType = null,
        Guid? generatedBy = null,
        UserType? userType = null,
        TimePeriodValue? period = null,
        Dictionary<string, object>? parameters = null,
        Dictionary<string, object>? configuration = null,
        DateTime? expiresAt = null,
        bool isScheduled = false,
        string? scheduleCron = null)
    {
        return new Report(
            name ?? Faker.Commerce.ProductName(),
            description ?? Faker.Lorem.Sentence(),
            reportType ?? Faker.PickRandom<ReportType>(),
            generatedBy ?? RandomGuid(),
            userType ?? Faker.PickRandom<UserType>(),
            period ?? CreateTestTimePeriod(),
            parameters,
            configuration,
            expiresAt,
            isScheduled,
            scheduleCron);
    }

    /// <summary>
    /// Create a test Dashboard entity using proper constructor
    /// </summary>
    public static Dashboard CreateTestDashboard(
        string? name = null,
        string? description = null,
        DashboardType? dashboardType = null,
        Guid? userId = null,
        UserType? userType = null,
        bool isDefault = false,
        bool isPublic = false,
        Dictionary<string, object>? configuration = null)
    {
        return new Dashboard(
            name ?? Faker.Commerce.ProductName(),
            description ?? Faker.Lorem.Sentence(),
            dashboardType ?? Faker.PickRandom<DashboardType>(),
            userId ?? RandomGuid(),
            userType ?? Faker.PickRandom<UserType>(),
            isDefault,
            isPublic,
            configuration);
    }

    /// <summary>
    /// Create a test Alert entity using proper constructor
    /// </summary>
    public static Alert CreateTestAlert(
        string? name = null,
        string? description = null,
        AlertSeverity? severity = null,
        string? message = null,
        Guid? metricId = null,
        string? metricName = null,
        decimal? triggerValue = null,
        decimal? thresholdValue = null,
        Dictionary<string, object>? context = null,
        Guid? userId = null,
        AlertType? alertType = null,
        Guid? entityId = null,
        string? entityType = null)
    {
        return new Alert(
            name ?? Faker.Lorem.Word(),
            description ?? Faker.Lorem.Sentence(),
            severity ?? Faker.PickRandom<AlertSeverity>(),
            message ?? Faker.Lorem.Sentence(),
            metricId,
            metricName ?? Faker.Lorem.Word(),
            triggerValue ?? Faker.Random.Decimal(0, 1000),
            thresholdValue ?? Faker.Random.Decimal(0, 1000),
            context,
            userId ?? RandomGuid(),
            alertType ?? Faker.PickRandom<AlertType>(),
            entityId,
            entityType);
    }

    /// <summary>
    /// Create a test MetricValue
    /// </summary>
    public static MetricValue CreateTestMetricValue(
        decimal? value = null,
        MetricType? metricType = null,
        string? unit = null,
        DateTime? timestamp = null,
        Dictionary<string, object>? metadata = null)
    {
        return new MetricValue(
            value ?? Faker.Random.Decimal(0, 1000),
            metricType ?? Faker.PickRandom<MetricType>(),
            unit ?? "count",
            timestamp ?? DateTime.UtcNow,
            metadata);
    }

    /// <summary>
    /// Create a test KPITarget
    /// </summary>
    public static KPITarget CreateTestKPITarget(
        decimal? targetValue = null,
        string? unit = null,
        bool isHigherBetter = true,
        decimal? minThreshold = null,
        decimal? maxThreshold = null,
        decimal? warningThreshold = null,
        decimal? criticalThreshold = null)
    {
        var target = targetValue ?? Faker.Random.Decimal(50, 100);
        return new KPITarget(
            target,
            unit ?? "percent",
            isHigherBetter,
            minThreshold ?? target * 0.8m,
            maxThreshold ?? target * 1.2m,
            warningThreshold,
            criticalThreshold);
    }

    /// <summary>
    /// Create multiple test reports
    /// </summary>
    public static List<Report> CreateTestReports(int count = 5)
    {
        var reports = new List<Report>();
        for (int i = 0; i < count; i++)
        {
            reports.Add(CreateTestReport());
        }
        return reports;
    }

    /// <summary>
    /// Create multiple test dashboards
    /// </summary>
    public static List<Dashboard> CreateTestDashboards(int count = 5)
    {
        var dashboards = new List<Dashboard>();
        for (int i = 0; i < count; i++)
        {
            dashboards.Add(CreateTestDashboard());
        }
        return dashboards;
    }

    /// <summary>
    /// Create multiple test alerts
    /// </summary>
    public static List<Alert> CreateTestAlerts(int count = 5)
    {
        var alerts = new List<Alert>();
        for (int i = 0; i < count; i++)
        {
            alerts.Add(CreateTestAlert());
        }
        return alerts;
    }

    /// <summary>
    /// Create test data for specific report types
    /// </summary>
    public static Report CreatePerformanceAnalysisReport(Guid? userId = null)
    {
        return CreateTestReport(
            name: "Performance Analysis Report",
            description: "Comprehensive performance analysis",
            reportType: ReportType.PerformanceAnalysis,
            generatedBy: userId ?? RandomGuid(),
            userType: UserType.Admin,
            period: CreateTestTimePeriod(TimePeriod.Monthly));
    }

    /// <summary>
    /// Create test data for specific dashboard types
    /// </summary>
    public static Dashboard CreateAdminDashboard(Guid? userId = null)
    {
        return CreateTestDashboard(
            name: "Admin Platform Performance Dashboard",
            description: "Administrative dashboard for platform performance monitoring",
            dashboardType: DashboardType.AdminPlatformPerformance,
            userId: userId ?? RandomGuid(),
            userType: UserType.Admin,
            isDefault: true,
            isPublic: false);
    }

    /// <summary>
    /// Create test data for specific alert types
    /// </summary>
    public static Alert CreateHighSeverityAlert(Guid? userId = null)
    {
        return CreateTestAlert(
            name: "Critical System Alert",
            description: "High severity system alert requiring immediate attention",
            severity: AlertSeverity.High,
            message: "System performance has degraded below acceptable thresholds",
            userId: userId ?? RandomGuid(),
            alertType: AlertType.System);
    }
}
