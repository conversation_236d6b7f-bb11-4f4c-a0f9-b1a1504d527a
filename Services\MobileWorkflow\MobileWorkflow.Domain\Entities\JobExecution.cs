using Shared.Domain.Common;
using MobileWorkflow.Domain.Events;

namespace MobileWorkflow.Domain.Entities;

public class JobExecution : AggregateRoot
{
    public Guid JobId { get; private set; }
    public int ExecutionNumber { get; private set; }
    public string WorkerId { get; private set; }
    public string Status { get; private set; } // Running, Completed, Failed, Cancelled
    public DateTime StartedAt { get; private set; }
    public DateTime? CompletedAt { get; private set; }
    public TimeSpan? Duration { get; private set; }
    public Dictionary<string, object> Result { get; private set; }
    public string? ErrorMessage { get; private set; }
    public string? ErrorStackTrace { get; private set; }
    public Dictionary<string, object> ExecutionContext { get; private set; }
    public Dictionary<string, object> PerformanceMetrics { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    public virtual BackgroundJob Job { get; private set; } = null!;

    private JobExecution() { } // EF Core

    public JobExecution(
        Guid jobId,
        int executionNumber,
        string workerId,
        Dictionary<string, object> executionContext)
    {
        JobId = jobId;
        ExecutionNumber = executionNumber;
        WorkerId = workerId ?? throw new ArgumentNullException(nameof(workerId));
        ExecutionContext = executionContext ?? throw new ArgumentNullException(nameof(executionContext));

        Status = "Running";
        StartedAt = DateTime.UtcNow;
        Result = new Dictionary<string, object>();
        PerformanceMetrics = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();

        AddDomainEvent(new JobExecutionStartedEvent(Id, JobId, ExecutionNumber, WorkerId));
    }

    public void Complete(Dictionary<string, object> result, Dictionary<string, object>? performanceMetrics = null)
    {
        if (Status != "Running")
            throw new InvalidOperationException($"Cannot complete execution with status: {Status}");

        Status = "Completed";
        CompletedAt = DateTime.UtcNow;
        Duration = CompletedAt.Value - StartedAt;
        Result = result ?? throw new ArgumentNullException(nameof(result));

        if (performanceMetrics != null)
        {
            PerformanceMetrics = performanceMetrics;
        }

        AddDomainEvent(new JobExecutionCompletedEvent(Id, JobId, ExecutionNumber, Duration.Value.TotalMilliseconds));
    }

    public void Fail(string errorMessage, string? errorStackTrace = null, Dictionary<string, object>? performanceMetrics = null)
    {
        Status = "Failed";
        CompletedAt = DateTime.UtcNow;
        Duration = CompletedAt.Value - StartedAt;
        ErrorMessage = errorMessage;
        ErrorStackTrace = errorStackTrace;

        if (performanceMetrics != null)
        {
            PerformanceMetrics = performanceMetrics;
        }

        AddDomainEvent(new JobExecutionFailedEvent(Id, JobId, ExecutionNumber, errorMessage));
    }

    public void Cancel()
    {
        if (Status != "Running")
            throw new InvalidOperationException($"Cannot cancel execution with status: {Status}");

        Status = "Cancelled";
        CompletedAt = DateTime.UtcNow;
        Duration = CompletedAt.Value - StartedAt;

        AddDomainEvent(new JobExecutionCancelledEvent(Id, JobId, ExecutionNumber));
    }

    public bool IsCompleted()
    {
        return Status == "Completed" || Status == "Failed" || Status == "Cancelled";
    }

    public bool IsSuccessful()
    {
        return Status == "Completed";
    }

    public bool IsFailed()
    {
        return Status == "Failed";
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetResult<T>(string key, T? defaultValue = default)
    {
        if (Result.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetPerformanceMetric<T>(string key, T? defaultValue = default)
    {
        if (PerformanceMetrics.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetExecutionContext<T>(string key, T? defaultValue = default)
    {
        if (ExecutionContext.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }
}

public class JobQueue : AggregateRoot
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public int MaxConcurrency { get; private set; }
    public int Priority { get; private set; }
    public bool IsActive { get; private set; }
    public bool IsPaused { get; private set; }
    public Dictionary<string, object> Configuration { get; private set; }
    public Dictionary<string, object> Statistics { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? UpdatedAt { get; private set; }
    public string CreatedBy { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    private JobQueue() { } // EF Core

    public JobQueue(
        string name,
        string description,
        int maxConcurrency,
        int priority,
        string createdBy)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        MaxConcurrency = maxConcurrency;
        Priority = priority;
        CreatedBy = createdBy ?? throw new ArgumentNullException(nameof(createdBy));

        IsActive = true;
        IsPaused = false;
        CreatedAt = DateTime.UtcNow;
        Configuration = new Dictionary<string, object>();
        Statistics = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();

        SetDefaultConfiguration();
        InitializeStatistics();

        AddDomainEvent(new JobQueueCreatedEvent(Id, Name, MaxConcurrency, Priority, CreatedBy));
    }

    public void UpdateConfiguration(Dictionary<string, object> configuration)
    {
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new JobQueueConfigurationUpdatedEvent(Id, Name));
    }

    public void SetMaxConcurrency(int maxConcurrency)
    {
        if (maxConcurrency <= 0)
            throw new ArgumentException("Max concurrency must be greater than 0");

        MaxConcurrency = maxConcurrency;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new JobQueueMaxConcurrencyUpdatedEvent(Id, Name, maxConcurrency));
    }

    public void SetPriority(int priority)
    {
        Priority = priority;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new JobQueuePriorityUpdatedEvent(Id, Name, priority));
    }

    public void Pause()
    {
        IsPaused = true;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new JobQueuePausedEvent(Id, Name));
    }

    public void Resume()
    {
        IsPaused = false;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new JobQueueResumedEvent(Id, Name));
    }

    public void Activate()
    {
        IsActive = true;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new JobQueueActivatedEvent(Id, Name));
    }

    public void Deactivate()
    {
        IsActive = false;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new JobQueueDeactivatedEvent(Id, Name));
    }

    public void UpdateStatistics(Dictionary<string, object> statistics)
    {
        Statistics = statistics ?? throw new ArgumentNullException(nameof(statistics));
        UpdatedAt = DateTime.UtcNow;
    }

    public void IncrementStatistic(string key, int increment = 1)
    {
        if (Statistics.TryGetValue(key, out var value) && value is int currentValue)
        {
            Statistics[key] = currentValue + increment;
        }
        else
        {
            Statistics[key] = increment;
        }
        UpdatedAt = DateTime.UtcNow;
    }

    public bool CanProcessJobs()
    {
        return IsActive && !IsPaused;
    }

    private void SetDefaultConfiguration()
    {
        Configuration["retry_delay_seconds"] = 60;
        Configuration["max_execution_time_minutes"] = 30;
        Configuration["cleanup_completed_jobs_days"] = 7;
        Configuration["cleanup_failed_jobs_days"] = 30;
        Configuration["enable_dead_letter_queue"] = true;
        Configuration["batch_size"] = 10;
    }

    private void InitializeStatistics()
    {
        Statistics["total_jobs"] = 0;
        Statistics["completed_jobs"] = 0;
        Statistics["failed_jobs"] = 0;
        Statistics["cancelled_jobs"] = 0;
        Statistics["pending_jobs"] = 0;
        Statistics["running_jobs"] = 0;
        Statistics["average_execution_time_ms"] = 0.0;
        Statistics["last_processed_at"] = DateTime.UtcNow;
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
        UpdatedAt = DateTime.UtcNow;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetConfiguration<T>(string key, T? defaultValue = default)
    {
        if (Configuration.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetStatistic<T>(string key, T? defaultValue = default)
    {
        if (Statistics.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }
}

public class JobWorker : AggregateRoot
{
    public string WorkerId { get; private set; }
    public string WorkerName { get; private set; }
    public string MachineName { get; private set; }
    public string ProcessId { get; private set; }
    public List<string> SupportedQueues { get; private set; }
    public List<string> SupportedJobTypes { get; private set; }
    public string Status { get; private set; } // Active, Idle, Busy, Offline, Maintenance
    public DateTime StartedAt { get; private set; }
    public DateTime LastHeartbeat { get; private set; }
    public int MaxConcurrentJobs { get; private set; }
    public int CurrentJobCount { get; private set; }
    public Dictionary<string, object> Configuration { get; private set; }
    public Dictionary<string, object> Statistics { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    private JobWorker() { } // EF Core

    public JobWorker(
        string workerId,
        string workerName,
        string machineName,
        string processId,
        List<string> supportedQueues,
        List<string> supportedJobTypes,
        int maxConcurrentJobs = 1)
    {
        WorkerId = workerId ?? throw new ArgumentNullException(nameof(workerId));
        WorkerName = workerName ?? throw new ArgumentNullException(nameof(workerName));
        MachineName = machineName ?? throw new ArgumentNullException(nameof(machineName));
        ProcessId = processId ?? throw new ArgumentNullException(nameof(processId));
        SupportedQueues = supportedQueues ?? throw new ArgumentNullException(nameof(supportedQueues));
        SupportedJobTypes = supportedJobTypes ?? throw new ArgumentNullException(nameof(supportedJobTypes));
        MaxConcurrentJobs = maxConcurrentJobs;

        Status = "Active";
        StartedAt = DateTime.UtcNow;
        LastHeartbeat = DateTime.UtcNow;
        CurrentJobCount = 0;
        Configuration = new Dictionary<string, object>();
        Statistics = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();

        InitializeStatistics();

        AddDomainEvent(new JobWorkerStartedEvent(Id, WorkerId, WorkerName, MachineName));
    }

    public void UpdateHeartbeat()
    {
        LastHeartbeat = DateTime.UtcNow;

        if (Status == "Offline")
        {
            Status = CurrentJobCount > 0 ? "Busy" : "Idle";
        }
    }

    public void StartJob()
    {
        CurrentJobCount++;
        Status = "Busy";
        UpdateStatistic("jobs_started");
    }

    public void CompleteJob(bool successful)
    {
        CurrentJobCount = Math.Max(0, CurrentJobCount - 1);
        Status = CurrentJobCount > 0 ? "Busy" : "Idle";

        if (successful)
        {
            UpdateStatistic("jobs_completed");
        }
        else
        {
            UpdateStatistic("jobs_failed");
        }
    }

    public void SetMaintenance()
    {
        Status = "Maintenance";

        AddDomainEvent(new JobWorkerMaintenanceModeEvent(Id, WorkerId, WorkerName));
    }

    public void SetOffline()
    {
        Status = "Offline";

        AddDomainEvent(new JobWorkerOfflineEvent(Id, WorkerId, WorkerName));
    }

    public bool IsAvailable()
    {
        return (Status == "Active" || Status == "Idle") && CurrentJobCount < MaxConcurrentJobs;
    }

    public bool IsOnline()
    {
        return DateTime.UtcNow - LastHeartbeat < TimeSpan.FromMinutes(5);
    }

    public bool SupportsQueue(string queueName)
    {
        return SupportedQueues.Contains(queueName, StringComparer.OrdinalIgnoreCase);
    }

    public bool SupportsJobType(string jobType)
    {
        return SupportedJobTypes.Contains(jobType, StringComparer.OrdinalIgnoreCase);
    }

    private void InitializeStatistics()
    {
        Statistics["jobs_started"] = 0;
        Statistics["jobs_completed"] = 0;
        Statistics["jobs_failed"] = 0;
        Statistics["total_execution_time_ms"] = 0.0;
        Statistics["average_execution_time_ms"] = 0.0;
    }

    private void UpdateStatistic(string key, int increment = 1)
    {
        if (Statistics.TryGetValue(key, out var value) && value is int currentValue)
        {
            Statistics[key] = currentValue + increment;
        }
        else
        {
            Statistics[key] = increment;
        }
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }
}


