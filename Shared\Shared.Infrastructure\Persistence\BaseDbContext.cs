using Microsoft.EntityFrameworkCore;
using Shared.Domain.Common;

namespace Shared.Infrastructure.Persistence
{
    public abstract class BaseDbContext : DbContext
    {
        protected BaseDbContext(DbContextOptions options) : base(options)
        {
        }

        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            UpdateTimestamps();
            return await base.SaveChangesAsync(cancellationToken);
        }

        public override int SaveChanges()
        {
            UpdateTimestamps();
            return base.SaveChanges();
        }

        private void UpdateTimestamps()
        {
            var entries = ChangeTracker
                .Entries()
                .Where(e => e.Entity is BaseEntity && (
                    e.State == EntityState.Added ||
                    e.State == EntityState.Modified));

            foreach (var entityEntry in entries)
            {
                var entity = (BaseEntity)entityEntry.Entity;

                if (entityEntry.State == EntityState.Added)
                {
                    // CreatedAt is set in the constructor, no need to update
                }

                if (entityEntry.State == EntityState.Modified)
                {
                    // Use reflection to call protected SetUpdatedAt method
                    var method = typeof(BaseEntity).GetMethod("SetUpdatedAt", 
                        System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                    method?.Invoke(entity, null);
                }
            }
        }
    }
}
