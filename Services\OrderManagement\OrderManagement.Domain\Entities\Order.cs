using OrderManagement.Domain.Primitives;
using Shared.Domain.Common;
using OrderManagement.Domain.Enums;
using OrderManagement.Domain.ValueObjects;

namespace OrderManagement.Domain.Entities;

public class Order : Entity
{
    public Guid TransportCompanyId { get; private set; }
    public Guid BrokerId { get; private set; }
    public Guid? CarrierId { get; private set; }
    public Guid? RfqId { get; private set; }
    public Guid? AcceptedBidId { get; private set; }
    public string OrderNumber { get; private set; }
    public string Title { get; private set; }
    public string Description { get; private set; }
    public LoadDetails LoadDetails { get; private set; }
    public RouteDetails RouteDetails { get; private set; }
    public Money AgreedPrice { get; private set; }
    public OrderStatus Status { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? ConfirmedAt { get; private set; }
    public DateTime? CompletedAt { get; private set; }
    public DateTime? CancelledAt { get; private set; }
    public string? CancellationReason { get; private set; }
    public string? SpecialInstructions { get; private set; }
    public bool IsUrgent { get; private set; }
    public PaymentStatus PaymentStatus { get; private set; }

    // Navigation properties
    public RFQ? Rfq { get; private set; }
    public RfqBid? AcceptedBid { get; private set; }

    private readonly List<OrderDocument> _documents = new();
    public IReadOnlyCollection<OrderDocument> Documents => _documents.AsReadOnly();

    private readonly List<OrderStatusHistory> _statusHistory = new();
    public IReadOnlyCollection<OrderStatusHistory> StatusHistory => _statusHistory.AsReadOnly();

    private readonly List<Invoice> _invoices = new();
    public IReadOnlyCollection<Invoice> Invoices => _invoices.AsReadOnly();

    private readonly List<OrderTimeline> _timeline = new();
    public IReadOnlyCollection<OrderTimeline> Timeline => _timeline.AsReadOnly();

    private Order() { } // EF Constructor

    public Order(
        Guid transportCompanyId,
        Guid brokerId,
        string title,
        string description,
        LoadDetails loadDetails,
        RouteDetails routeDetails,
        Money agreedPrice,
        Guid? rfqId = null,
        Guid? acceptedBidId = null,
        string? specialInstructions = null,
        bool isUrgent = false)
    {
        TransportCompanyId = transportCompanyId;
        BrokerId = brokerId;
        RfqId = rfqId;
        AcceptedBidId = acceptedBidId;
        OrderNumber = GenerateOrderNumber();
        Title = title ?? throw new ArgumentNullException(nameof(title));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        LoadDetails = loadDetails ?? throw new ArgumentNullException(nameof(loadDetails));
        RouteDetails = routeDetails ?? throw new ArgumentNullException(nameof(routeDetails));
        AgreedPrice = agreedPrice ?? throw new ArgumentNullException(nameof(agreedPrice));
        Status = OrderStatus.Created;
        CreatedAt = DateTime.UtcNow;
        SpecialInstructions = specialInstructions;
        IsUrgent = isUrgent;
        PaymentStatus = PaymentStatus.Pending;

        // Add initial status history
        AddStatusHistory(OrderStatus.Created, "Order created");

        // Add initial timeline event
        AddTimelineEvent(
            OrderTimelineEventType.OrderCreated,
            "Order created successfully",
            newStatus: OrderStatus.Created,
            notes: $"Order {OrderNumber} created for transport company {TransportCompanyId}");

        // Domain event for order created
        AddDomainEvent(new OrderCreatedDomainEvent(Id, TransportCompanyId, BrokerId, OrderNumber));
    }

    public void Confirm(Guid carrierId)
    {
        if (Status != OrderStatus.Created)
            throw new InvalidOperationException("Only created orders can be confirmed");

        var previousStatus = Status;
        CarrierId = carrierId;
        Status = OrderStatus.Confirmed;
        ConfirmedAt = DateTime.UtcNow;

        AddStatusHistory(OrderStatus.Confirmed, $"Order confirmed with carrier {carrierId}");
        AddTimelineEvent(
            OrderTimelineEventType.OrderConfirmed,
            $"Order confirmed with carrier {carrierId}",
            previousStatus: previousStatus,
            newStatus: OrderStatus.Confirmed,
            notes: $"Carrier ID: {carrierId}");

        // Domain event for order confirmed
        AddDomainEvent(new OrderConfirmedDomainEvent(Id, carrierId, OrderNumber));
    }

    public void Complete()
    {
        if (Status != OrderStatus.InProgress)
            throw new InvalidOperationException("Only in-progress orders can be completed");

        var previousStatus = Status;
        Status = OrderStatus.Completed;
        CompletedAt = DateTime.UtcNow;

        AddStatusHistory(OrderStatus.Completed, "Order completed successfully");
        AddTimelineEvent(
            OrderTimelineEventType.OrderCompleted,
            "Order completed successfully",
            previousStatus: previousStatus,
            newStatus: OrderStatus.Completed,
            notes: "Order execution completed");

        // Domain event for order completed
        AddDomainEvent(new OrderCompletedDomainEvent(Id, TransportCompanyId, BrokerId, CarrierId));
    }

    public void ForceComplete(string reason, Guid adminUserId)
    {
        if (Status == OrderStatus.Completed)
            throw new InvalidOperationException("Order is already completed");

        if (Status == OrderStatus.Cancelled)
            throw new InvalidOperationException("Cannot force complete a cancelled order");

        var previousStatus = Status;
        Status = OrderStatus.Completed;
        CompletedAt = DateTime.UtcNow;

        AddStatusHistory(OrderStatus.Completed, $"Order force completed by admin: {reason}");

        // Domain event for order force completed
        AddDomainEvent(new Events.OrderForceCompletedDomainEvent(Id, TransportCompanyId, BrokerId, CarrierId, adminUserId, reason));
    }

    public void Cancel(string reason)
    {
        if (Status == OrderStatus.Completed || Status == OrderStatus.Cancelled)
            throw new InvalidOperationException("Cannot cancel completed or already cancelled orders");

        var previousStatus = Status;
        Status = OrderStatus.Cancelled;
        CancelledAt = DateTime.UtcNow;
        CancellationReason = reason;

        AddStatusHistory(OrderStatus.Cancelled, $"Order cancelled: {reason}");
        AddTimelineEvent(
            OrderTimelineEventType.OrderCancelled,
            $"Order cancelled: {reason}",
            previousStatus: previousStatus,
            newStatus: OrderStatus.Cancelled,
            notes: reason);

        // Domain event for order cancelled
        AddDomainEvent(new OrderCancelledDomainEvent(Id, reason));
    }

    public void StartProgress()
    {
        if (Status != OrderStatus.Confirmed)
            throw new InvalidOperationException("Only confirmed orders can be started");

        var previousStatus = Status;
        Status = OrderStatus.InProgress;
        AddStatusHistory(OrderStatus.InProgress, "Order execution started");
        AddTimelineEvent(
            OrderTimelineEventType.OrderStarted,
            "Order execution started",
            previousStatus: previousStatus,
            newStatus: OrderStatus.InProgress,
            notes: "Order moved to in-progress status");
    }

    public void UpdatePaymentStatus(PaymentStatus newStatus)
    {
        PaymentStatus = newStatus;
        AddStatusHistory(Status, $"Payment status updated to {newStatus}");
    }

    public void AddDocument(OrderDocument document)
    {
        _documents.Add(document);
    }

    public void AddInvoice(Invoice invoice)
    {
        _invoices.Add(invoice);
    }

    private void AddStatusHistory(OrderStatus status, string notes)
    {
        _statusHistory.Add(new OrderStatusHistory(Id, status, notes, DateTime.UtcNow));
    }

    public void AddTimelineEvent(
        OrderTimelineEventType eventType,
        string eventDescription,
        Guid? actorId = null,
        string? actorName = null,
        string? actorRole = null,
        string? additionalData = null,
        OrderStatus? previousStatus = null,
        OrderStatus? newStatus = null,
        string? notes = null,
        string? ipAddress = null,
        string? userAgent = null,
        Guid? correlationId = null,
        string? sessionId = null,
        Dictionary<string, object>? metadata = null)
    {
        var timelineEvent = OrderTimeline.CreateEvent(
            Id,
            eventType,
            eventDescription,
            actorId,
            actorName,
            actorRole,
            additionalData,
            previousStatus,
            newStatus,
            notes,
            ipAddress,
            userAgent,
            correlationId,
            sessionId,
            metadata);

        _timeline.Add(timelineEvent);
    }

    public void AddTimelineEvent(OrderTimeline timelineEvent)
    {
        if (timelineEvent.OrderId != Id)
            throw new ArgumentException("Timeline event must belong to this order");

        _timeline.Add(timelineEvent);
    }

    private static string GenerateOrderNumber()
    {
        return $"ORD-{DateTime.UtcNow:yyyyMMdd}-{Guid.NewGuid().ToString("N")[..8].ToUpper()}";
    }
}

// Domain Events
public record OrderCreatedDomainEvent(Guid OrderId, Guid TransportCompanyId, Guid BrokerId, string OrderNumber) : Shared.Domain.Common.IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}
public record OrderConfirmedDomainEvent(Guid OrderId, Guid CarrierId, string OrderNumber) : Shared.Domain.Common.IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}
public record OrderCompletedDomainEvent(Guid OrderId, Guid TransportCompanyId, Guid BrokerId, Guid? CarrierId) : Shared.Domain.Common.IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}
public record OrderCancelledDomainEvent(Guid OrderId, string Reason) : Shared.Domain.Common.IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}


