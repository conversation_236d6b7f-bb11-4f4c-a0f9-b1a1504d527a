{"ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=tli_analytics_test;User Id=timescale;Password=timescale;Include Error Detail=true"}, "Database": {"EnableSensitiveDataLogging": true, "EnableDetailedErrors": true, "UseInMemoryDatabase": true, "UseSqliteDatabase": false}, "JwtSettings": {"Issuer": "TLI.AnalyticsService.Test", "Audience": "TLI.AnalyticsService.Test", "SecretKey": "test-secret-key-for-jwt-token-generation-in-tests-only-not-for-production-use", "ExpirationMinutes": 60}, "Analytics": {"DataRetention": {"RawDataDays": 7, "AggregatedDataDays": 30, "ReportDataDays": 90}, "ReportGeneration": {"MaxConcurrentReports": 2, "ReportTimeoutMinutes": 5, "EnableScheduledReports": false, "DefaultPageSize": 50, "MaxPageSize": 100}, "Dashboard": {"RefreshIntervalSeconds": 30, "MaxWidgetsPerDashboard": 10, "EnableRealTimeUpdates": false, "CacheWidgetDataMinutes": 5}, "Alerting": {"EnableAlerts": false, "CheckIntervalMinutes": 1, "MaxAlertsPerMinute": 10, "DefaultThresholdCheckMinutes": 5}, "MachineLearning": {"EnableMLFeatures": false, "ModelUpdateIntervalHours": 1, "PredictionCacheMinutes": 15, "MinDataPointsForPrediction": 10}, "Performance": {"QueryTimeoutSeconds": 10, "MaxConcurrentQueries": 5, "EnableQueryOptimization": true, "CacheResultsMinutes": 5}}, "ExternalServices": {"EnableMockServices": true, "MockDataGeneration": true, "SimulateLatency": false, "LatencyMs": 100}, "Testing": {"EnableMockProviders": true, "EnableTestData": true, "EnablePerformanceTesting": false, "EnableLoadTesting": false, "TestDataSize": "Small", "MockExternalServices": true, "EnableRealTimeTests": false, "TestTimeout": 30000, "MaxConcurrentTests": 5}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.EntityFrameworkCore": "Warning", "AnalyticsBIService": "Debug", "AnalyticsBIService.Tests": "Debug"}, "EnableStructuredLogging": true, "EnableCorrelationIds": true, "LogSensitiveData": false}, "Performance": {"EnableMetrics": true, "EnableTracing": false, "SampleRate": 1.0, "EnableHealthChecks": true, "HealthCheckTimeout": 5000}, "Security": {"EnableAuthentication": true, "EnableAuthorization": true, "RequireHttps": false, "EnableCors": true, "AllowedOrigins": ["http://localhost:3000", "http://localhost:8080"], "EnableRateLimiting": false, "EnableApiKeyValidation": false}, "Redis": {"ConnectionString": "localhost:6379", "Password": "redis123", "Database": 2, "EnableConnectionMultiplexer": true, "ConnectTimeout": 5000, "SyncTimeout": 5000}, "RabbitMQ": {"Host": "localhost", "Port": 5672, "Username": "guest", "Password": "guest", "VirtualHost": "/", "ExchangeName": "tli.analytics.test", "QueueName": "analytics.test.queue", "EnablePublishing": false, "EnableConsuming": false}}