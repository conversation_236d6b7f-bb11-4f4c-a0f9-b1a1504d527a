using MediatR;

namespace TripManagement.Application.Queries.GetEnhancedDriverDashboard;

public class GetEnhancedDriverDashboardQuery : IRequest<GetEnhancedDriverDashboardResponse>
{
    public Guid DriverId { get; set; }
    public bool IncludeRatingStats { get; set; } = true;
    public bool IncludeCompletedTrips { get; set; } = true;
    public bool IncludeFeedback { get; set; } = true;
    public bool IncludeMapShortcuts { get; set; } = true;
    public bool IncludePerformanceMetrics { get; set; } = true;
    public int RecentTripsCount { get; set; } = 5;
    public int RecentFeedbackCount { get; set; } = 3;
    public int PerformanceDays { get; set; } = 30;
}

public class GetEnhancedDriverDashboardResponse
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public EnhancedDriverDashboardDto? Dashboard { get; set; }
}

public class EnhancedDriverDashboardDto
{
    // Basic driver information
    public Guid DriverId { get; set; }
    public string DriverName { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime LastActiveAt { get; set; }
    
    // Current trip information
    public CurrentTripDashboardInfo? CurrentTrip { get; set; }
    
    // Rating and performance statistics
    public DriverRatingStatistics RatingStats { get; set; } = new();
    
    // Recent completed trips
    public List<CompletedTripSummary> RecentCompletedTrips { get; set; } = new();
    
    // Recent feedback
    public List<FeedbackSummary> RecentFeedback { get; set; } = new();
    
    // Map shortcuts and navigation
    public List<MapShortcut> MapShortcuts { get; set; } = new();
    
    // Performance metrics
    public PerformanceMetricsSummary PerformanceMetrics { get; set; } = new();
    
    // Pending actions and notifications
    public List<PendingAction> PendingActions { get; set; } = new();
    
    // Quick stats
    public DashboardQuickStats QuickStats { get; set; } = new();
    
    // Earnings information
    public EarningsSummary Earnings { get; set; } = new();
    
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
}

public class CurrentTripDashboardInfo
{
    public Guid TripId { get; set; }
    public string TripNumber { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string Origin { get; set; } = string.Empty;
    public string Destination { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime? EstimatedEndTime { get; set; }
    public double ProgressPercentage { get; set; }
    public string? CurrentMilestone { get; set; }
    public string? NextMilestone { get; set; }
    public NavigationInfo? Navigation { get; set; }
    public List<string> AvailableActions { get; set; } = new();
}

public class NavigationInfo
{
    public double CurrentLatitude { get; set; }
    public double CurrentLongitude { get; set; }
    public double DestinationLatitude { get; set; }
    public double DestinationLongitude { get; set; }
    public double RemainingDistanceKm { get; set; }
    public int EstimatedTimeMinutes { get; set; }
    public string NavigationUrl { get; set; } = string.Empty;
    public List<string> RouteAlerts { get; set; } = new();
}

public class DriverRatingStatistics
{
    public double AverageRating { get; set; }
    public int TotalRatings { get; set; }
    public Dictionary<int, int> RatingDistribution { get; set; } = new(); // Star -> Count
    public double RecentRating { get; set; } // Last 30 days
    public int RecentRatingCount { get; set; }
    public double RatingTrend { get; set; } // Positive/negative trend
    public string RatingGrade { get; set; } = string.Empty; // A, B, C, D, F
    public List<string> RatingHighlights { get; set; } = new(); // Top feedback themes
}

public class CompletedTripSummary
{
    public Guid TripId { get; set; }
    public string TripNumber { get; set; } = string.Empty;
    public DateTime CompletedAt { get; set; }
    public string Origin { get; set; } = string.Empty;
    public string Destination { get; set; } = string.Empty;
    public decimal Distance { get; set; }
    public decimal Earnings { get; set; }
    public double? Rating { get; set; }
    public string? CustomerFeedback { get; set; }
    public TimeSpan Duration { get; set; }
    public bool OnTime { get; set; }
}

public class FeedbackSummary
{
    public Guid FeedbackId { get; set; }
    public Guid TripId { get; set; }
    public string TripNumber { get; set; } = string.Empty;
    public double Rating { get; set; }
    public string? Comments { get; set; }
    public DateTime SubmittedAt { get; set; }
    public string CustomerName { get; set; } = string.Empty;
    public string FeedbackType { get; set; } = string.Empty; // Positive, Neutral, Negative
    public List<string> Tags { get; set; } = new(); // Professional, Punctual, Helpful, etc.
}

public class MapShortcut
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public string Address { get; set; } = string.Empty;
    public string ShortcutType { get; set; } = string.Empty; // Home, Depot, FuelStation, RestArea
    public string NavigationUrl { get; set; } = string.Empty;
    public double? DistanceFromCurrentKm { get; set; }
    public int? EstimatedTimeMinutes { get; set; }
    public bool IsFavorite { get; set; }
}

public class PerformanceMetricsSummary
{
    public double OnTimePerformance { get; set; }
    public double SafetyScore { get; set; }
    public double FuelEfficiency { get; set; }
    public double CustomerSatisfaction { get; set; }
    public int TotalTripsCompleted { get; set; }
    public int TotalMilesdriven { get; set; }
    public TimeSpan TotalDrivingTime { get; set; }
    public string PerformanceGrade { get; set; } = string.Empty;
    public List<PerformanceAlert> Alerts { get; set; } = new();
    public Dictionary<string, double> MonthlyTrends { get; set; } = new();
}

public class PerformanceAlert
{
    public string Type { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public bool IsActionRequired { get; set; }
}

public class PendingAction
{
    public string ActionType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Priority { get; set; } = string.Empty;
    public DateTime DueDate { get; set; }
    public bool IsOverdue { get; set; }
    public string ActionUrl { get; set; } = string.Empty;
    public Dictionary<string, object> ActionData { get; set; } = new();
}

public class DashboardQuickStats
{
    public int TripsToday { get; set; }
    public int TripsThisWeek { get; set; }
    public int TripsThisMonth { get; set; }
    public decimal EarningsToday { get; set; }
    public decimal EarningsThisWeek { get; set; }
    public decimal EarningsThisMonth { get; set; }
    public TimeSpan DrivingTimeToday { get; set; }
    public double MilesDrivenToday { get; set; }
    public int PendingTrips { get; set; }
    public int DocumentsExpiringSoon { get; set; }
}

public class EarningsSummary
{
    public decimal TotalEarnings { get; set; }
    public decimal EarningsToday { get; set; }
    public decimal EarningsThisWeek { get; set; }
    public decimal EarningsThisMonth { get; set; }
    public decimal AverageEarningsPerTrip { get; set; }
    public decimal AverageEarningsPerMile { get; set; }
    public List<EarningsBreakdown> Breakdown { get; set; } = new();
    public EarningsTrend Trend { get; set; } = new();
}

public class EarningsBreakdown
{
    public string Category { get; set; } = string.Empty; // Base, Bonus, Fuel, Tips
    public decimal Amount { get; set; }
    public double Percentage { get; set; }
}

public class EarningsTrend
{
    public double WeeklyGrowth { get; set; }
    public double MonthlyGrowth { get; set; }
    public string TrendDirection { get; set; } = string.Empty; // Up, Down, Stable
    public List<DailyEarnings> DailyEarnings { get; set; } = new();
}

public class DailyEarnings
{
    public DateTime Date { get; set; }
    public decimal Amount { get; set; }
    public int TripCount { get; set; }
}
