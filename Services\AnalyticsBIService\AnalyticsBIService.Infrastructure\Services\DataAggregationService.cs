using Microsoft.Extensions.Logging;
using AnalyticsBIService.Application.Interfaces;
using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Domain.Repositories;
using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Enums;

namespace AnalyticsBIService.Infrastructure.Services;

/// <summary>
/// Data aggregation service for analytics processing
/// </summary>
public class DataAggregationService : IDataAggregationService
{
    private readonly IAnalyticsEventRepository _analyticsEventRepository;
    private readonly IMetricRepository _metricRepository;
    private readonly ICacheService _cacheService;
    private readonly ILogger<DataAggregationService> _logger;

    public DataAggregationService(
        IAnalyticsEventRepository analyticsEventRepository,
        IMetricRepository metricRepository,
        ICacheService cacheService,
        ILogger<DataAggregationService> logger)
    {
        _analyticsEventRepository = analyticsEventRepository;
        _metricRepository = metricRepository;
        _cacheService = cacheService;
        _logger = logger;
    }

    /// <summary>
    /// Aggregate metrics for a specific time period
    /// </summary>
    public async Task<Dictionary<string, object>> AggregateMetricsAsync(
        DateTime startDate,
        DateTime endDate,
        MetricCategory category,
        AnalyticsBIService.Application.Interfaces.AggregationType aggregationType,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"metrics_aggregation_{category}_{aggregationType}_{startDate:yyyyMMdd}_{endDate:yyyyMMdd}";

            return await _cacheService.GetOrSetAsync(cacheKey, async () =>
            {
                var metrics = await _metricRepository.GetMetricsByCategoryAsync(category, startDate, endDate, cancellationToken);

                return aggregationType switch
                {
                    AnalyticsBIService.Application.Interfaces.AggregationType.Sum => AggregateSum(metrics),
                    AnalyticsBIService.Application.Interfaces.AggregationType.Average => AggregateAverage(metrics),
                    AnalyticsBIService.Application.Interfaces.AggregationType.Count => AggregateCount(metrics),
                    AnalyticsBIService.Application.Interfaces.AggregationType.Min => AggregateMin(metrics),
                    AnalyticsBIService.Application.Interfaces.AggregationType.Max => AggregateMax(metrics),
                    _ => throw new ArgumentException($"Unsupported aggregation type: {aggregationType}")
                };
            }, TimeSpan.FromMinutes(15), cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error aggregating metrics for category {Category} and type {AggregationType}", category, aggregationType);
            throw;
        }
    }

    /// <summary>
    /// Aggregate analytics events by entity type
    /// </summary>
    public async Task<Dictionary<string, int>> AggregateEventsByEntityTypeAsync(
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"events_by_entity_{startDate:yyyyMMdd}_{endDate:yyyyMMdd}";

            return await _cacheService.GetOrSetAsync(cacheKey, async () =>
            {
                var events = await _analyticsEventRepository.GetEventsByDateRangeAsync(startDate, endDate, cancellationToken);

                return events
                    .GroupBy(e => e.EntityType)
                    .ToDictionary(g => g.Key, g => g.Count());
            }, TimeSpan.FromMinutes(10), cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error aggregating events by entity type");
            throw;
        }
    }

    /// <summary>
    /// Calculate trend data for metrics
    /// </summary>
    public async Task<Dictionary<DateTime, decimal>> CalculateTrendDataAsync(
        MetricCategory category,
        MetricType metricType,
        DateTime startDate,
        DateTime endDate,
        TimeSpan interval,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"trend_data_{category}_{metricType}_{startDate:yyyyMMdd}_{endDate:yyyyMMdd}_{interval.TotalHours}h";

            return await _cacheService.GetOrSetAsync(cacheKey, async () =>
            {
                var metrics = await _metricRepository.GetMetricsByTypeAsync(metricType, startDate, endDate, cancellationToken);

                var trendData = new Dictionary<DateTime, decimal>();
                var currentDate = startDate;

                while (currentDate <= endDate)
                {
                    var periodEnd = currentDate.Add(interval);
                    var periodMetrics = metrics.Where(m => m.Timestamp >= currentDate && m.Timestamp < periodEnd);

                    if (periodMetrics.Any())
                    {
                        trendData[currentDate] = periodMetrics.Average(m => m.Value.NumericValue);
                    }
                    else
                    {
                        trendData[currentDate] = 0;
                    }

                    currentDate = periodEnd;
                }

                return trendData;
            }, TimeSpan.FromMinutes(20), cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating trend data for category {Category} and type {MetricType}", category, metricType);
            throw;
        }
    }

    /// <summary>
    /// Generate performance summary for entity
    /// </summary>
    public async Task<Dictionary<string, object>> GeneratePerformanceSummaryAsync(
        string entityId,
        string entityType,
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"performance_summary_{entityType}_{entityId}_{startDate:yyyyMMdd}_{endDate:yyyyMMdd}";

            return await _cacheService.GetOrSetAsync(cacheKey, async () =>
            {
                var events = await _analyticsEventRepository.GetEventsByDateRangeAsync(startDate, endDate, cancellationToken);
                var metrics = await _metricRepository.GetTimeSeriesAsync($"{entityType}_{entityId}", startDate, endDate, null, cancellationToken);

                return new Dictionary<string, object>
                {
                    ["TotalEvents"] = events.Count,
                    ["UniqueEventTypes"] = events.Select(e => e.EventType).Distinct().Count(),
                    ["TotalMetrics"] = metrics.Count,
                    ["AverageMetricValue"] = metrics.Any() ? metrics.Average(m => m.Value.NumericValue) : 0,
                    ["MaxMetricValue"] = metrics.Any() ? metrics.Max(m => m.Value.NumericValue) : 0,
                    ["MinMetricValue"] = metrics.Any() ? metrics.Min(m => m.Value.NumericValue) : 0,
                    ["EventsByType"] = events.GroupBy(e => e.EventType).ToDictionary(g => g.Key, g => g.Count()),
                    ["MetricsByCategory"] = metrics.GroupBy(m => m.Category).ToDictionary(g => g.Key.ToString(), g => g.Count())
                };
            }, TimeSpan.FromMinutes(15), cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating performance summary for entity {EntityId} of type {EntityType}", entityId, entityType);
            throw;
        }
    }

    #region Private Helper Methods

    private Dictionary<string, object> AggregateSum(IEnumerable<Metric> metrics)
    {
        return new Dictionary<string, object>
        {
            ["Sum"] = metrics.Sum(m => m.Value.NumericValue),
            ["Count"] = metrics.Count()
        };
    }

    private Dictionary<string, object> AggregateAverage(IEnumerable<Metric> metrics)
    {
        return new Dictionary<string, object>
        {
            ["Average"] = metrics.Any() ? metrics.Average(m => m.Value.NumericValue) : 0m,
            ["Count"] = metrics.Count()
        };
    }

    private Dictionary<string, object> AggregateCount(IEnumerable<Metric> metrics)
    {
        return new Dictionary<string, object>
        {
            ["Count"] = metrics.Count(),
            ["UniqueEntities"] = metrics.Select(m => m.EntityId).Distinct().Count()
        };
    }

    private Dictionary<string, object> AggregateMin(IEnumerable<Metric> metrics)
    {
        return new Dictionary<string, object>
        {
            ["Min"] = metrics.Any() ? metrics.Min(m => m.Value.NumericValue) : 0m,
            ["Count"] = metrics.Count()
        };
    }

    private Dictionary<string, object> AggregateMax(IEnumerable<Metric> metrics)
    {
        return new Dictionary<string, object>
        {
            ["Max"] = metrics.Any() ? metrics.Max(m => m.Value.NumericValue) : 0m,
            ["Count"] = metrics.Count()
        };
    }

    #endregion

    #region IDataAggregationService Implementation

    /// <summary>
    /// Aggregate data from multiple services for comprehensive reporting
    /// </summary>
    public async Task<AggregatedDataResult> AggregateDataAsync(
        DataAggregationRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            var result = new AggregatedDataResult
            {
                GeneratedAt = DateTime.UtcNow,
                Metrics = request.Metrics
            };

            var dataSources = new Dictionary<string, object>();
            var aggregatedData = new Dictionary<string, object>();

            // Aggregate metrics data
            if (request.Metrics.Any())
            {
                var metricsData = new Dictionary<string, object>();
                foreach (var metric in request.Metrics)
                {
                    var metricValues = await _metricRepository.GetTimeSeriesAsync(
                        metric, request.StartDate, request.EndDate, null, cancellationToken);
                    metricsData[metric] = metricValues.Select(m => new
                    {
                        Timestamp = m.Timestamp,
                        Value = m.Value.NumericValue,
                        Category = m.Category.ToString()
                    }).ToList();
                }
                dataSources["Metrics"] = metricsData;
                aggregatedData["MetricsData"] = metricsData;
            }

            // Aggregate events data
            var events = await _analyticsEventRepository.GetEventsByDateRangeAsync(
                request.StartDate, request.EndDate, cancellationToken);

            var eventsData = events.GroupBy(e => e.EventType)
                .ToDictionary(g => g.Key, g => g.Count());
            dataSources["Events"] = eventsData;
            aggregatedData["EventsData"] = eventsData;

            // Apply filters if specified
            if (request.Filters.Any())
            {
                // Apply additional filtering logic based on request.Filters
                foreach (var filter in request.Filters)
                {
                    _logger.LogInformation("Applying filter: {FilterKey} = {FilterValue}", filter.Key, filter.Value);
                }
            }

            stopwatch.Stop();

            result.TotalRecords = events.Count;
            result.DataSources = dataSources;
            result.Data = aggregatedData;
            result.ExecutionTimeMs = stopwatch.ElapsedMilliseconds;

            _logger.LogInformation("Data aggregation completed in {ExecutionTime}ms for {ServiceCount} services",
                stopwatch.ElapsedMilliseconds, request.Services.Count);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error aggregating data for request with {ServiceCount} services", request.Services.Count);
            throw;
        }
    }

    #endregion
}

/// <summary>
/// Aggregation types for metrics
/// </summary>
public enum AggregationType
{
    Sum,
    Average,
    Count,
    Min,
    Max
}
