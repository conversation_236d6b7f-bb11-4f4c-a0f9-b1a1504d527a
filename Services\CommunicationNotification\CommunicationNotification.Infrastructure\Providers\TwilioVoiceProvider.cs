using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;
using CommunicationNotification.Infrastructure.Configuration;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Twilio;
using Twilio.Rest.Api.V2010.Account;
using Twilio.Types;

namespace CommunicationNotification.Infrastructure.Providers;

/// <summary>
/// Twilio voice provider implementation
/// </summary>
public class TwilioVoiceProvider : IVoiceProvider
{
    private readonly ILogger<TwilioVoiceProvider> _logger;
    private readonly TwilioConfiguration _configuration;

    public NotificationChannel Channel => NotificationChannel.Voice;

    public TwilioVoiceProvider(
        IConfiguration configuration,
        ILogger<TwilioVoiceProvider> logger)
    {
        _logger = logger;
        _configuration = configuration.GetSection("Twilio").Get<TwilioConfiguration>()
            ?? throw new InvalidOperationException("Twilio configuration is missing");

        InitializeTwilio();
    }

    public async Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(_configuration.AccountSid) || 
                string.IsNullOrEmpty(_configuration.AuthToken))
            {
                return false;
            }

            // Test API connectivity by fetching account info
            var account = await AccountResource.FetchAsync(cancellationToken: cancellationToken);
            return account != null && account.Status == AccountResource.StatusEnum.Active;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check Twilio Voice availability");
            return false;
        }
    }

    public async Task<NotificationResult> SendAsync(
        ContactInfo recipient,
        MessageContent content,
        Dictionary<string, string>? metadata = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (!ValidateRecipient(recipient))
            {
                return NotificationResult.Failure("Invalid phone number for voice call");
            }

            var voiceMessageRequest = new VoiceMessageRequest
            {
                PhoneNumber = recipient.PhoneNumber,
                Message = content.Body,
                LanguageCode = MapLanguageToTwilioCode(content.Language),
                Metadata = metadata
            };

            var result = await SendVoiceMessageAsync(voiceMessageRequest, cancellationToken);

            return result.IsSuccess
                ? NotificationResult.Success(result.MessageId!, DeliveryStatus.Sent)
                : NotificationResult.Failure(result.ErrorMessage!);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send voice message to {PhoneNumber}", recipient.PhoneNumber);
            return NotificationResult.Failure($"Failed to send voice message: {ex.Message}");
        }
    }

    public async Task<DeliveryStatus> GetDeliveryStatusAsync(
        string externalId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var call = await CallResource.FetchAsync(externalId, cancellationToken: cancellationToken);
            
            return call.Status switch
            {
                CallResource.StatusEnum.Queued => DeliveryStatus.Pending,
                CallResource.StatusEnum.Ringing => DeliveryStatus.Pending,
                CallResource.StatusEnum.InProgress => DeliveryStatus.Sent,
                CallResource.StatusEnum.Completed => DeliveryStatus.Delivered,
                CallResource.StatusEnum.Failed => DeliveryStatus.Failed,
                CallResource.StatusEnum.Busy => DeliveryStatus.Failed,
                CallResource.StatusEnum.NoAnswer => DeliveryStatus.Failed,
                CallResource.StatusEnum.Canceled => DeliveryStatus.Failed,
                _ => DeliveryStatus.Pending
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get voice call status for {CallId}", externalId);
            return DeliveryStatus.Failed;
        }
    }

    public bool ValidateRecipient(ContactInfo recipient)
    {
        if (string.IsNullOrWhiteSpace(recipient.PhoneNumber))
            return false;

        // Basic phone number validation
        var phoneNumber = recipient.PhoneNumber.Trim();
        return phoneNumber.StartsWith("+") && phoneNumber.Length >= 10;
    }

    public async Task<VoiceCallResult> MakeCallAsync(
        VoiceCallRequest callRequest,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var callOptions = new CreateCallOptions(
                to: new PhoneNumber(callRequest.PhoneNumber),
                from: new PhoneNumber(callRequest.FromNumber ?? _configuration.FromPhoneNumber))
            {
                Record = callRequest.RecordCall,
                Timeout = callRequest.MaxDurationSeconds
            };

            // Set TwiML for the call
            if (!string.IsNullOrEmpty(callRequest.Message))
            {
                var twiml = GenerateTwiML(callRequest.Message, callRequest.VoiceSettings);
                callOptions.Twiml = new Twiml(twiml);
            }
            else if (!string.IsNullOrEmpty(callRequest.AudioUrl))
            {
                var twiml = $"<Response><Play>{callRequest.AudioUrl}</Play></Response>";
                callOptions.Twiml = new Twiml(twiml);
            }

            if (!string.IsNullOrEmpty(callRequest.CallbackUrl))
            {
                callOptions.StatusCallback = new Uri(callRequest.CallbackUrl);
                callOptions.StatusCallbackEvent = new List<string> { "initiated", "ringing", "answered", "completed" };
            }

            var call = await CallResource.CreateAsync(callOptions, cancellationToken: cancellationToken);

            var estimatedCost = CalculateEstimatedCost(callRequest.MaxDurationSeconds);

            _logger.LogInformation("Voice call initiated to {PhoneNumber}, CallId: {CallId}, EstimatedCost: {Cost}",
                callRequest.PhoneNumber, call.Sid, estimatedCost);

            return VoiceCallResult.Success(call.Sid, estimatedCost);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to make voice call to {PhoneNumber}", callRequest.PhoneNumber);
            return VoiceCallResult.Failure($"Failed to make voice call: {ex.Message}");
        }
    }

    public async Task<VoiceMessageResult> SendVoiceMessageAsync(
        VoiceMessageRequest voiceMessageRequest,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var twiml = GenerateTwiML(voiceMessageRequest.Message, voiceMessageRequest.VoiceSettings);

            var callOptions = new CreateCallOptions(
                to: new PhoneNumber(voiceMessageRequest.PhoneNumber),
                from: new PhoneNumber(_configuration.FromPhoneNumber))
            {
                Twiml = new Twiml(twiml),
                Timeout = 30 // 30 seconds timeout for voice messages
            };

            var call = await CallResource.CreateAsync(callOptions, cancellationToken: cancellationToken);

            // Estimate duration based on message length (rough calculation)
            var estimatedDuration = EstimateMessageDuration(voiceMessageRequest.Message);
            var cost = CalculateEstimatedCost(estimatedDuration);

            _logger.LogInformation("Voice message sent to {PhoneNumber}, CallId: {CallId}, Duration: {Duration}s, Cost: {Cost}",
                voiceMessageRequest.PhoneNumber, call.Sid, estimatedDuration, cost);

            return VoiceMessageResult.Success(call.Sid, estimatedDuration, cost);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send voice message to {PhoneNumber}", voiceMessageRequest.PhoneNumber);
            return VoiceMessageResult.Failure($"Failed to send voice message: {ex.Message}");
        }
    }

    public async Task<AudioPlaybackResult> PlayAudioAsync(
        AudioPlaybackRequest audioRequest,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // This would typically be used during an active call
            // For now, we'll return a success result as a placeholder
            _logger.LogInformation("Audio playback requested for call {CallId}, Audio: {AudioUrl}",
                audioRequest.CallId, audioRequest.AudioUrl);

            return AudioPlaybackResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to play audio for call {CallId}", audioRequest.CallId);
            return AudioPlaybackResult.Failure($"Failed to play audio: {ex.Message}");
        }
    }

    public async Task<VoiceCallStatus> GetCallStatusAsync(
        string callId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var call = await CallResource.FetchAsync(callId, cancellationToken: cancellationToken);

            return new VoiceCallStatus
            {
                CallId = callId,
                PhoneNumber = call.To,
                Status = MapTwilioCallStatus(call.Status),
                StartedAt = call.StartTime,
                EndedAt = call.EndTime,
                DurationSeconds = call.Duration,
                Cost = call.Price != null ? decimal.Parse(call.Price) : null,
                ErrorMessage = call.Status == CallResource.StatusEnum.Failed ? "Call failed" : null
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get call status for {CallId}", callId);
            
            return new VoiceCallStatus
            {
                CallId = callId,
                Status = CallStatus.Failed,
                ErrorMessage = $"Failed to get call status: {ex.Message}"
            };
        }
    }

    public async Task<bool> EndCallAsync(
        string callId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            await CallResource.UpdateAsync(
                callId,
                status: CallResource.UpdateStatusEnum.Completed,
                cancellationToken: cancellationToken);

            _logger.LogInformation("Call {CallId} ended successfully", callId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to end call {CallId}", callId);
            return false;
        }
    }

    public async Task<List<VoiceOption>> GetAvailableVoicesAsync(
        string languageCode,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Twilio supports various voices for different languages
            var voices = new List<VoiceOption>();

            switch (languageCode.ToLower())
            {
                case "en-us":
                case "en":
                    voices.AddRange(new[]
                    {
                        new VoiceOption { VoiceId = "alice", Name = "Alice", Gender = "female", LanguageCode = "en-US", LanguageName = "English (US)", IsNeural = false },
                        new VoiceOption { VoiceId = "man", Name = "Man", Gender = "male", LanguageCode = "en-US", LanguageName = "English (US)", IsNeural = false },
                        new VoiceOption { VoiceId = "woman", Name = "Woman", Gender = "female", LanguageCode = "en-US", LanguageName = "English (US)", IsNeural = false }
                    });
                    break;

                case "hi-in":
                case "hi":
                    voices.AddRange(new[]
                    {
                        new VoiceOption { VoiceId = "alice", Name = "Alice", Gender = "female", LanguageCode = "hi-IN", LanguageName = "Hindi (India)", IsNeural = false }
                    });
                    break;

                case "kn-in":
                case "kn":
                    voices.AddRange(new[]
                    {
                        new VoiceOption { VoiceId = "alice", Name = "Alice", Gender = "female", LanguageCode = "kn-IN", LanguageName = "Kannada (India)", IsNeural = false }
                    });
                    break;

                default:
                    voices.Add(new VoiceOption { VoiceId = "alice", Name = "Alice", Gender = "female", LanguageCode = "en-US", LanguageName = "English (US)", IsNeural = false });
                    break;
            }

            return voices;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get available voices for language {LanguageCode}", languageCode);
            return new List<VoiceOption>();
        }
    }

    private void InitializeTwilio()
    {
        TwilioClient.Init(_configuration.AccountSid, _configuration.AuthToken);
        _logger.LogInformation("Twilio Voice provider initialized with Account SID: {AccountSid}", 
            _configuration.AccountSid);
    }

    private string GenerateTwiML(string message, VoiceSettings? voiceSettings)
    {
        var voice = voiceSettings?.VoiceId ?? "alice";
        var language = voiceSettings?.VoiceId?.Contains("hi") == true ? "hi-IN" : 
                      voiceSettings?.VoiceId?.Contains("kn") == true ? "kn-IN" : "en-US";

        return $@"
            <Response>
                <Say voice=""{voice}"" language=""{language}"">{message}</Say>
            </Response>";
    }

    private static string MapLanguageToTwilioCode(Language language)
    {
        return language switch
        {
            Language.Hindi => "hi-IN",
            Language.Kannada => "kn-IN",
            Language.English => "en-US",
            _ => "en-US"
        };
    }

    private static CallStatus MapTwilioCallStatus(CallResource.StatusEnum? status)
    {
        return status switch
        {
            CallResource.StatusEnum.Queued => CallStatus.Initiated,
            CallResource.StatusEnum.Ringing => CallStatus.Ringing,
            CallResource.StatusEnum.InProgress => CallStatus.InProgress,
            CallResource.StatusEnum.Completed => CallStatus.Completed,
            CallResource.StatusEnum.Failed => CallStatus.Failed,
            CallResource.StatusEnum.Busy => CallStatus.Busy,
            CallResource.StatusEnum.NoAnswer => CallStatus.NoAnswer,
            CallResource.StatusEnum.Canceled => CallStatus.Cancelled,
            _ => CallStatus.Failed
        };
    }

    private static int EstimateMessageDuration(string message)
    {
        // Rough estimation: 150 words per minute, average 5 characters per word
        var wordsPerMinute = 150;
        var charactersPerWord = 5;
        var estimatedWords = message.Length / charactersPerWord;
        var estimatedMinutes = (double)estimatedWords / wordsPerMinute;
        return Math.Max(10, (int)(estimatedMinutes * 60)); // Minimum 10 seconds
    }

    private decimal CalculateEstimatedCost(int durationSeconds)
    {
        // Rough estimation based on Twilio pricing (varies by region)
        var costPerMinute = 0.0075m; // $0.0075 per minute for US
        var minutes = Math.Ceiling(durationSeconds / 60.0);
        return (decimal)minutes * costPerMinute;
    }
}
