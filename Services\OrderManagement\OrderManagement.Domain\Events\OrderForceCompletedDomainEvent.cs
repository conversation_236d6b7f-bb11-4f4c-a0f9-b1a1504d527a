using Shared.Domain.Common;

namespace OrderManagement.Domain.Events;

public class OrderForceCompletedDomainEvent : DomainEvent
{
    public Guid OrderId { get; }
    public Guid TransportCompanyId { get; }
    public Guid? BrokerId { get; }
    public Guid? CarrierId { get; }
    public Guid AdminUserId { get; }
    public string Reason { get; }

    public OrderForceCompletedDomainEvent(
        Guid orderId, 
        Guid transportCompanyId, 
        Guid? brokerId, 
        Guid? carrierId,
        Guid adminUserId,
        string reason)
    {
        OrderId = orderId;
        TransportCompanyId = transportCompanyId;
        BrokerId = brokerId;
        CarrierId = carrierId;
        AdminUserId = adminUserId;
        Reason = reason;
    }
}
