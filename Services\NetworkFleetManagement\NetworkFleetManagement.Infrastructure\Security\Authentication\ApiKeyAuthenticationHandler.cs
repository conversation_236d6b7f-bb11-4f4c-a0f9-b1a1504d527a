using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Security.Claims;
using System.Text.Encodings.Web;

namespace NetworkFleetManagement.Infrastructure.Security.Authentication;

public class ApiKeyAuthenticationHandler : AuthenticationHandler<ApiKeyAuthenticationSchemeOptions>
{
    private readonly IApiKeyService _apiKeyService;
    private readonly ILogger<ApiKeyAuthenticationHandler> _logger;

    public ApiKeyAuthenticationHandler(
        IOptionsMonitor<ApiKeyAuthenticationSchemeOptions> options,
        ILoggerFactory logger,
        UrlEncoder encoder,
        ISystemClock clock,
        IApiKeyService apiKeyService)
        : base(options, logger, encoder, clock)
    {
        _apiKeyService = apiKeyService;
        _logger = logger.CreateLogger<ApiKeyAuthenticationHandler>();
    }

    protected override async Task<AuthenticateResult> HandleAuthenticateAsync()
    {
        try
        {
            var apiKey = ExtractApiKey();
            if (string.IsNullOrEmpty(apiKey))
            {
                return AuthenticateResult.NoResult();
            }

            var apiKeyInfo = await _apiKeyService.ValidateApiKeyAsync(apiKey);
            if (apiKeyInfo == null)
            {
                _logger.LogWarning("Invalid API key attempted: {ApiKey}", MaskApiKey(apiKey));
                return AuthenticateResult.Fail("Invalid API key");
            }

            if (!apiKeyInfo.IsActive)
            {
                _logger.LogWarning("Inactive API key attempted: {ApiKey}", MaskApiKey(apiKey));
                return AuthenticateResult.Fail("API key is inactive");
            }

            if (apiKeyInfo.ExpiresAt.HasValue && apiKeyInfo.ExpiresAt.Value < DateTime.UtcNow)
            {
                _logger.LogWarning("Expired API key attempted: {ApiKey}", MaskApiKey(apiKey));
                return AuthenticateResult.Fail("API key has expired");
            }

            // Check rate limits for this API key
            if (!await _apiKeyService.CheckRateLimitAsync(apiKey))
            {
                _logger.LogWarning("Rate limit exceeded for API key: {ApiKey}", MaskApiKey(apiKey));
                return AuthenticateResult.Fail("Rate limit exceeded");
            }

            // Create claims
            var claims = new List<Claim>
            {
                new(ClaimTypes.Name, apiKeyInfo.Name),
                new(ClaimTypes.NameIdentifier, apiKeyInfo.Id.ToString()),
                new("api_key_id", apiKeyInfo.Id.ToString()),
                new("client_id", apiKeyInfo.ClientId ?? "unknown"),
                new("auth_method", "api_key")
            };

            // Add role claims
            foreach (var role in apiKeyInfo.Roles)
            {
                claims.Add(new Claim(ClaimTypes.Role, role));
            }

            // Add permission claims
            foreach (var permission in apiKeyInfo.Permissions)
            {
                claims.Add(new Claim("permission", permission));
            }

            // Add carrier context if available
            if (apiKeyInfo.CarrierId.HasValue)
            {
                claims.Add(new Claim("carrierId", apiKeyInfo.CarrierId.Value.ToString()));
            }

            var identity = new ClaimsIdentity(claims, Scheme.Name);
            var principal = new ClaimsPrincipal(identity);
            var ticket = new AuthenticationTicket(principal, Scheme.Name);

            // Record successful authentication
            await _apiKeyService.RecordUsageAsync(apiKey, Context.Request.Path, true);

            _logger.LogDebug("API key authentication successful for: {ApiKeyName}", apiKeyInfo.Name);
            
            return AuthenticateResult.Success(ticket);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during API key authentication");
            return AuthenticateResult.Fail("Authentication error");
        }
    }

    protected override async Task HandleChallengeAsync(AuthenticationProperties properties)
    {
        Response.Headers.Add("WWW-Authenticate", "ApiKey");
        Response.StatusCode = 401;
        
        var response = new
        {
            error = "Unauthorized",
            message = "Valid API key required",
            details = "Include API key in X-API-Key header or api_key query parameter"
        };

        await Response.WriteAsync(System.Text.Json.JsonSerializer.Serialize(response));
    }

    protected override async Task HandleForbiddenAsync(AuthenticationProperties properties)
    {
        Response.StatusCode = 403;
        
        var response = new
        {
            error = "Forbidden",
            message = "Insufficient permissions for this operation"
        };

        await Response.WriteAsync(System.Text.Json.JsonSerializer.Serialize(response));
    }

    private string? ExtractApiKey()
    {
        // Try header first
        var headerValue = Request.Headers["X-API-Key"].FirstOrDefault();
        if (!string.IsNullOrEmpty(headerValue))
        {
            return headerValue;
        }

        // Try Authorization header with ApiKey scheme
        var authHeader = Request.Headers["Authorization"].FirstOrDefault();
        if (!string.IsNullOrEmpty(authHeader) && authHeader.StartsWith("ApiKey ", StringComparison.OrdinalIgnoreCase))
        {
            return authHeader.Substring("ApiKey ".Length).Trim();
        }

        // Try query parameter
        var queryValue = Request.Query["api_key"].FirstOrDefault();
        if (!string.IsNullOrEmpty(queryValue))
        {
            return queryValue;
        }

        return null;
    }

    private static string MaskApiKey(string apiKey)
    {
        if (string.IsNullOrEmpty(apiKey) || apiKey.Length < 8)
        {
            return "***";
        }

        return apiKey.Substring(0, 4) + "***" + apiKey.Substring(apiKey.Length - 4);
    }
}

public class ApiKeyAuthenticationSchemeOptions : AuthenticationSchemeOptions
{
    public string HeaderName { get; set; } = "X-API-Key";
    public string QueryParameterName { get; set; } = "api_key";
    public bool AllowQueryParameter { get; set; } = true;
}

public interface IApiKeyService
{
    Task<ApiKeyInfo?> ValidateApiKeyAsync(string apiKey);
    Task<bool> CheckRateLimitAsync(string apiKey);
    Task RecordUsageAsync(string apiKey, string endpoint, bool success);
    Task<ApiKeyInfo> CreateApiKeyAsync(CreateApiKeyRequest request);
    Task<bool> RevokeApiKeyAsync(Guid apiKeyId);
    Task<List<ApiKeyInfo>> GetApiKeysAsync(Guid? carrierId = null);
}

public class ApiKeyInfo
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string ClientId { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public DateTime? LastUsedAt { get; set; }
    public List<string> Roles { get; set; } = new();
    public List<string> Permissions { get; set; } = new();
    public Guid? CarrierId { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class CreateApiKeyRequest
{
    public string Name { get; set; } = string.Empty;
    public string ClientId { get; set; } = string.Empty;
    public List<string> Roles { get; set; } = new();
    public List<string> Permissions { get; set; } = new();
    public Guid? CarrierId { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public static class ApiKeyAuthenticationExtensions
{
    public const string ApiKeyScheme = "ApiKey";

    public static AuthenticationBuilder AddApiKeyAuthentication(
        this AuthenticationBuilder builder,
        Action<ApiKeyAuthenticationSchemeOptions>? configureOptions = null)
    {
        return builder.AddScheme<ApiKeyAuthenticationSchemeOptions, ApiKeyAuthenticationHandler>(
            ApiKeyScheme, configureOptions);
    }
}
