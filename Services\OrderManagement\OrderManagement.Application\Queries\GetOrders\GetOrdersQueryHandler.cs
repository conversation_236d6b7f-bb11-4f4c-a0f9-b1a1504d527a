using MediatR;
using AutoMapper;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;
using OrderManagement.Application.DTOs;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Application.Queries.GetOrders;

public class GetOrdersQueryHandler : IRequestHandler<GetOrdersQuery, PagedResult<OrderSummaryDto>>
{
    private readonly IOrderRepository _orderRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetOrdersQueryHandler> _logger;

    public GetOrdersQueryHandler(
        IOrderRepository orderRepository,
        IMapper mapper,
        ILogger<GetOrdersQueryHandler> logger)
    {
        _orderRepository = orderRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<PagedResult<OrderSummaryDto>> Handle(GetOrdersQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting orders for user {UserId} with role {UserRole}, page {Page}, pageSize {PageSize}", 
            request.UserId, request.UserRole, request.Page, request.PageSize);

        // Validate pagination parameters
        if (request.Page < 1) request.Page = 1;
        if (request.PageSize < 1 || request.PageSize > 100) request.PageSize = 20;

        try
        {
            // Get orders based on user role and filters
            var (orders, totalCount) = await GetOrdersWithFilters(request, cancellationToken);

            // Map to DTOs
            var orderDtos = _mapper.Map<List<OrderSummaryDto>>(orders);

            var result = new PagedResult<OrderSummaryDto>
            {
                Items = orderDtos,
                TotalCount = totalCount,
                Page = request.Page,
                PageSize = request.PageSize
            };

            _logger.LogInformation("Successfully retrieved {Count} orders out of {TotalCount} for user {UserId}", 
                orderDtos.Count, totalCount, request.UserId);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving orders for user {UserId}", request.UserId);
            throw;
        }
    }

    private async Task<(IEnumerable<Domain.Entities.Order> Orders, int TotalCount)> GetOrdersWithFilters(
        GetOrdersQuery request, CancellationToken cancellationToken)
    {
        // Build filters based on user role
        Guid? transportCompanyId = null;
        Guid? brokerId = null;
        Guid? carrierId = null;

        if (request.UserId.HasValue && !string.IsNullOrEmpty(request.UserRole))
        {
            switch (request.UserRole.ToLower())
            {
                case "transportcompany":
                    transportCompanyId = request.UserId.Value;
                    break;
                case "broker":
                    brokerId = request.UserId.Value;
                    break;
                case "carrier":
                    carrierId = request.UserId.Value;
                    break;
            }
        }

        // Get paginated results
        var (orders, totalCount) = await _orderRepository.GetPagedAsync(
            request.Page,
            request.PageSize,
            transportCompanyId,
            brokerId,
            carrierId,
            request.Status,
            cancellationToken);

        // Apply additional filters if needed
        var filteredOrders = orders.AsEnumerable();

        if (request.PaymentStatus.HasValue)
        {
            filteredOrders = filteredOrders.Where(o => o.PaymentStatus == request.PaymentStatus.Value);
        }

        if (request.FromDate.HasValue)
        {
            filteredOrders = filteredOrders.Where(o => o.CreatedAt >= request.FromDate.Value);
        }

        if (request.ToDate.HasValue)
        {
            filteredOrders = filteredOrders.Where(o => o.CreatedAt <= request.ToDate.Value);
        }

        if (request.IsUrgent.HasValue)
        {
            filteredOrders = filteredOrders.Where(o => o.IsUrgent == request.IsUrgent.Value);
        }

        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            var searchTerm = request.SearchTerm.ToLower();
            filteredOrders = filteredOrders.Where(o => 
                o.OrderNumber.ToLower().Contains(searchTerm) ||
                o.Title.ToLower().Contains(searchTerm) ||
                o.RouteDetails.PickupAddress.City.ToLower().Contains(searchTerm) ||
                o.RouteDetails.DeliveryAddress.City.ToLower().Contains(searchTerm));
        }

        // Apply sorting
        filteredOrders = ApplySorting(filteredOrders, request.SortBy, request.SortDirection);

        var finalOrders = filteredOrders.ToList();
        var finalCount = filteredOrders.Count();

        return (finalOrders, finalCount);
    }

    private static IEnumerable<Domain.Entities.Order> ApplySorting(
        IEnumerable<Domain.Entities.Order> orders, 
        string? sortBy, 
        string? sortDirection)
    {
        var isDescending = string.Equals(sortDirection, "desc", StringComparison.OrdinalIgnoreCase);

        return sortBy?.ToLower() switch
        {
            "ordernumber" => isDescending 
                ? orders.OrderByDescending(o => o.OrderNumber)
                : orders.OrderBy(o => o.OrderNumber),
            "title" => isDescending 
                ? orders.OrderByDescending(o => o.Title)
                : orders.OrderBy(o => o.Title),
            "status" => isDescending 
                ? orders.OrderByDescending(o => o.Status)
                : orders.OrderBy(o => o.Status),
            "paymentstatus" => isDescending 
                ? orders.OrderByDescending(o => o.PaymentStatus)
                : orders.OrderBy(o => o.PaymentStatus),
            "agreedprice" => isDescending 
                ? orders.OrderByDescending(o => o.AgreedPrice.Amount)
                : orders.OrderBy(o => o.AgreedPrice.Amount),
            "pickupdate" => isDescending 
                ? orders.OrderByDescending(o => o.RouteDetails.PreferredPickupDate)
                : orders.OrderBy(o => o.RouteDetails.PreferredPickupDate),
            "deliverydate" => isDescending 
                ? orders.OrderByDescending(o => o.RouteDetails.PreferredDeliveryDate)
                : orders.OrderBy(o => o.RouteDetails.PreferredDeliveryDate),
            "confirmedat" => isDescending 
                ? orders.OrderByDescending(o => o.ConfirmedAt)
                : orders.OrderBy(o => o.ConfirmedAt),
            "completedat" => isDescending 
                ? orders.OrderByDescending(o => o.CompletedAt)
                : orders.OrderBy(o => o.CompletedAt),
            _ => isDescending 
                ? orders.OrderByDescending(o => o.CreatedAt)
                : orders.OrderBy(o => o.CreatedAt)
        };
    }
}
