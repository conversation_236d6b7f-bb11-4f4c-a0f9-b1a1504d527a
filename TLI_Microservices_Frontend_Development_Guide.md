# TLI Microservices Frontend Development Guide

## Table of Contents

1. [Overview](#overview)
2. [Architecture Summary](#architecture-summary)
3. [Authentication & Authorization](#authentication--authorization)
4. [Core Services API Reference](#core-services-api-reference)
5. [Support Services API Reference](#support-services-api-reference)
6. [Data Models & DTOs](#data-models--dtos)
7. [Real-time Communication](#real-time-communication)
8. [Frontend Integration Patterns](#frontend-integration-patterns)
9. [Error Handling](#error-handling)
10. [Best Practices](#best-practices)

## Overview

This comprehensive guide provides frontend developers with detailed information about the TLI (Transport Logistics India) microservices architecture, API endpoints, data models, and integration patterns. The TLI platform consists of 12 microservices designed to handle various aspects of transport logistics operations.

### Key Features for Frontend Development

- **RESTful APIs**: All services expose RESTful endpoints with consistent patterns
- **JWT Authentication**: Secure token-based authentication across all services
- **Real-time Updates**: SignalR hubs for live data updates
- **Role-based Access**: Fine-grained permission system
- **Comprehensive Validation**: Server-side validation with detailed error responses
- **Pagination & Filtering**: Consistent pagination and filtering patterns
- **File Management**: Secure file upload/download capabilities
- **Offline Support**: Mobile-first design with offline capabilities

## Architecture Summary

### Service Ports and Base URLs

| Service                 | Port | Base URL             | Status           | Purpose                         |
| ----------------------- | ---- | -------------------- | ---------------- | ------------------------------- |
| API Gateway             | 5000 | `/api`               | Production Ready | Request routing, authentication |
| Identity Service        | 5001 | `/api/identity`      | Production Ready | Authentication, authorization   |
| User Management         | 5002 | `/api/users`         | 85% Complete     | User profiles, KYC workflow     |
| Subscription Management | 5003 | `/api/subscriptions` | 98% Complete     | Billing, feature management     |
| Order Management        | 5004 | `/api/orders`        | 96% Complete     | RFQ, bidding, order processing  |
| Trip Management         | 5005 | `/api/trips`         | 94% Complete     | Trip tracking, POD management   |
| Network & Fleet         | 5006 | `/api/fleet`         | 95% Complete     | Vehicle, driver management      |
| Financial & Payment     | 5007 | `/api/payments`      | 94% Complete     | Payment processing, invoicing   |
| Communication           | 5008 | `/api/communication` | 80% Complete     | Notifications, messaging        |
| Analytics & BI          | 5009 | `/api/analytics`     | 60% Complete     | Reporting, dashboards           |
| Data & Storage          | 5010 | `/api/storage`       | 75% Complete     | File management, CDN            |
| Audit & Compliance      | 5011 | `/api/audit`         | 85% Complete     | Audit trails, compliance        |
| Mobile & Workflow       | 5014 | `/api/mobile`        | 60% Complete     | Mobile features, workflows      |
| Monitoring              | 5015 | `/api/monitoring`    | 65% Complete     | Health checks, metrics          |

### Technology Stack

- **Backend**: .NET 8, ASP.NET Core Web API
- **Database**: PostgreSQL 15+, TimescaleDB (time-series), Redis (caching)
- **Message Broker**: RabbitMQ 3.12+
- **API Gateway**: Ocelot
- **Real-time**: SignalR
- **Authentication**: JWT Bearer tokens
- **Validation**: FluentValidation
- **ORM**: Entity Framework Core
- **Patterns**: CQRS with MediatR, Clean Architecture

## Authentication & Authorization

### JWT Token Structure

All API requests require a JWT Bearer token in the Authorization header:

```http
Authorization: Bearer <jwt-token>
```

### Token Claims

```json
{
  "sub": "user-id-guid",
  "email": "<EMAIL>",
  "role": ["Admin", "Broker", "Transporter", "Shipper"],
  "permissions": ["users.read", "orders.create", "trips.manage"],
  "subscription_tier": "Premium",
  "company_id": "company-guid",
  "exp": **********,
  "iat": **********
}
```

### Role Hierarchy

1. **Admin**: Full system access
2. **Broker**: Order management, carrier coordination
3. **Transporter**: Fleet management, trip execution
4. **Shipper**: Order creation, tracking
5. **Driver**: Mobile app access, trip updates

### Permission System

Permissions follow the pattern: `{resource}.{action}`

**Common Permissions:**

- `users.read`, `users.create`, `users.update`, `users.delete`
- `orders.read`, `orders.create`, `orders.update`, `orders.delete`
- `trips.read`, `trips.create`, `trips.update`, `trips.delete`
- `fleet.read`, `fleet.create`, `fleet.update`, `fleet.delete`
- `analytics.read`, `analytics.export`
- `admin.manage`, `admin.approve`

### Authentication Endpoints

#### Identity Service (`/api/identity`)

```http
POST /api/identity/auth/login
POST /api/identity/auth/register
POST /api/identity/auth/refresh
POST /api/identity/auth/logout
POST /api/identity/auth/forgot-password
POST /api/identity/auth/reset-password
POST /api/identity/auth/enable-2fa
POST /api/identity/auth/verify-2fa
GET  /api/identity/users/{userId}
POST /api/identity/users/{userId}/permissions
```

### Login Request/Response

**Request:**

```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "rememberMe": true,
  "deviceInfo": {
    "deviceId": "device-uuid",
    "platform": "web",
    "userAgent": "Mozilla/5.0..."
  }
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "refresh-token-uuid",
    "expiresIn": 3600,
    "tokenType": "Bearer",
    "user": {
      "id": "user-guid",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "roles": ["Broker"],
      "permissions": ["orders.create", "orders.read"],
      "profileComplete": true,
      "subscriptionStatus": "Active"
    }
  },
  "message": "Login successful"
}
```

## Core Services API Reference

### User Management Service (`/api/users`)

#### Key Endpoints

```http
GET    /api/users                    # Get users with filtering
POST   /api/users                    # Create user profile
GET    /api/users/{id}               # Get user by ID
PUT    /api/users/{id}               # Update user profile
DELETE /api/users/{id}               # Soft delete user
GET    /api/users/{id}/documents     # Get user documents
POST   /api/users/{id}/documents     # Upload documents
PUT    /api/users/{id}/approve       # Approve user profile
PUT    /api/users/{id}/reject        # Reject user profile
GET    /api/users/profile            # Get current user profile
PUT    /api/users/profile/personal   # Update personal details
PUT    /api/users/profile/company    # Update company details
PUT    /api/users/profile/address    # Update address details
POST   /api/users/profile/submit     # Submit for review
```

#### User Profile DTO

```typescript
interface UserProfileDto {
  id: string
  userId: string
  userType: 'Broker' | 'Transporter' | 'Shipper'
  status: 'Pending' | 'Approved' | 'Rejected' | 'Suspended'
  profileCompletionPercentage: number

  // Personal Details
  personalDetails: {
    firstName: string
    lastName: string
    dateOfBirth?: Date
    gender?: 'Male' | 'Female' | 'Other'
    phoneNumber: string
    alternatePhoneNumber?: string
    email: string
    alternateEmail?: string
    profilePicture?: string
  }

  // Company Details
  companyDetails: {
    companyName: string
    companyType:
      | 'Proprietorship'
      | 'Partnership'
      | 'PrivateLimited'
      | 'PublicLimited'
      | 'LLP'
    registrationNumber: string
    gstNumber?: string
    panNumber: string
    establishedYear: number
    website?: string
    companyLogo?: string
    description?: string
  }

  // Address Details
  addressDetails: {
    street: string
    city: string
    state: string
    postalCode: string
    country: string
    landmark?: string
    addressType: 'Registered' | 'Operational' | 'Both'
  }

  // Metadata
  createdAt: Date
  updatedAt: Date
  approvedAt?: Date
  approvedBy?: string
  rejectionReason?: string
  lastLoginAt?: Date
}
```

#### Document Management

```typescript
interface DocumentDto {
  id: string
  userId: string
  documentType:
    | 'PAN'
    | 'GST'
    | 'Aadhar'
    | 'DrivingLicense'
    | 'VehicleRC'
    | 'Insurance'
    | 'Permit'
  fileName: string
  fileUrl: string
  fileSize: number
  mimeType: string
  status: 'Pending' | 'Approved' | 'Rejected'
  uploadedAt: Date
  reviewedAt?: Date
  reviewedBy?: string
  rejectionReason?: string
  expiryDate?: Date
  ocrData?: {
    extractedText: string
    confidence: number
    fields: Record<string, any>
  }
}
```

### Subscription Management Service (`/api/subscriptions`)

#### Key Endpoints

```http
GET    /api/subscriptions           # Get user subscriptions
POST   /api/subscriptions           # Create subscription
PUT    /api/subscriptions/{id}/upgrade    # Upgrade subscription
PUT    /api/subscriptions/{id}/downgrade  # Downgrade subscription
PUT    /api/subscriptions/{id}/pause      # Pause subscription
PUT    /api/subscriptions/{id}/resume     # Resume subscription
PUT    /api/subscriptions/{id}/cancel     # Cancel subscription
GET    /api/subscriptions/my         # Get current user subscription
POST   /api/subscriptions/{id}/extend     # Extend subscription (Admin)
POST   /api/subscriptions/{id}/payment-proof  # Upload payment proof

GET    /api/plans                   # Get subscription plans
GET    /api/plans/{id}              # Get plan details
GET    /api/features/{userId}       # Get user feature flags
GET    /api/billing/history         # Get billing history
POST   /api/billing/process         # Process billing cycle (Admin)
```

#### Subscription DTO

```typescript
interface SubscriptionDto {
  id: string
  userId: string
  planId: string
  planName: string
  planType: 'Basic' | 'Standard' | 'Premium' | 'Enterprise'
  status: 'Active' | 'Inactive' | 'Cancelled' | 'Suspended' | 'PendingPayment'
  startDate: Date
  endDate?: Date
  nextBillingDate: Date
  trialEndDate?: Date
  cancelledAt?: Date
  cancellationReason?: string
  autoRenew: boolean
  currentPrice: number
  currency: string
  billingCycle: 'Monthly' | 'Quarterly' | 'Annual'
  isInTrial: boolean
  isInGracePeriod: boolean
  gracePeriodEndDate?: Date
  features: FeatureFlagDto[]
  paymentMethod?: PaymentMethodDto
  billingHistory: BillingHistoryDto[]
}
```

#### Feature Flags

```typescript
interface FeatureFlagDto {
  key: string
  name: string
  description: string
  enabled: boolean
  value?: any
  metadata?: Record<string, any>
}

// Common Feature Flags
const FEATURE_FLAGS = {
  'rfq.advanced_filtering': 'Advanced RFQ filtering options',
  'analytics.real_time': 'Real-time analytics dashboard',
  'mobile.offline_mode': 'Mobile offline capabilities',
  'api.rate_limit_extended': 'Extended API rate limits',
  'support.priority': 'Priority customer support',
  'export.bulk_data': 'Bulk data export capabilities',
}
```

### Order Management Service (`/api/orders`)

#### Key Endpoints

```http
# RFQ Management
POST   /api/rfq                     # Create RFQ
GET    /api/rfq                     # Get RFQs with filtering
GET    /api/rfq/{id}                # Get RFQ details
PUT    /api/rfq/{id}                # Update RFQ
PUT    /api/rfq/{id}/publish        # Publish RFQ
PUT    /api/rfq/{id}/close          # Close RFQ
DELETE /api/rfq/{id}                # Cancel RFQ
GET    /api/rfq/published           # Get published RFQs
POST   /api/rfq/{id}/extend         # Extend RFQ timeframe

# Bidding
POST   /api/quotes                  # Submit bid
GET    /api/quotes                  # Get bids
GET    /api/quotes/{id}             # Get bid details
PUT    /api/quotes/{id}             # Update bid
PUT    /api/quotes/{id}/accept      # Accept bid
PUT    /api/quotes/{id}/reject      # Reject bid
PUT    /api/quotes/{id}/withdraw    # Withdraw bid

# Orders
POST   /api/orders                  # Create order
GET    /api/orders                  # Get orders with filtering
GET    /api/orders/{id}             # Get order details
PUT    /api/orders/{id}/confirm     # Confirm order
PUT    /api/orders/{id}/cancel      # Cancel order
GET    /api/orders/{id}/timeline    # Get order timeline
PUT    /api/orders/{id}/force-award # Force award (Admin)

# Invoices
GET    /api/invoices                # Get invoices
GET    /api/invoices/{id}           # Get invoice details
POST   /api/invoices/{id}/generate  # Generate invoice
PUT    /api/invoices/{id}/send      # Send invoice
```

#### RFQ (Request for Quote) DTO

```typescript
interface RfqDto {
  id: string
  rfqNumber: string
  title: string
  description: string
  transportCompanyId: string
  brokerId?: string
  status: 'Draft' | 'Published' | 'Closed' | 'Cancelled' | 'Awarded'

  // Load Details
  loadDetails: {
    weight: number
    weightUnit: 'KG' | 'MT'
    dimensions?: {
      length: number
      width: number
      height: number
      unit: 'CM' | 'M'
    }
    cargoType: string
    cargoValue?: number
    specialRequirements?: string[]
    packagingType: string
    hazardous: boolean
    temperature?: {
      min: number
      max: number
      unit: 'C' | 'F'
    }
  }

  // Route Details
  routeDetails: {
    pickupLocation: LocationDto
    deliveryLocation: LocationDto
    intermediateStops?: LocationDto[]
    distance: number
    estimatedDuration: number // in hours
    routeType: 'Direct' | 'MultiStop'
  }

  // Pricing & Terms
  budgetRange?: {
    min: number
    max: number
    currency: string
  }
  paymentTerms: string

  // Timeline
  pickupDate: Date
  deliveryDate: Date
  timeframe?: {
    biddingStartTime: Date
    biddingEndTime: Date
    extensions: TimeframeExtensionDto[]
  }

  // Additional Info
  isUrgent: boolean
  contactPerson?: string
  contactPhone?: string
  contactEmail?: string

  // Shipper Portal Features
  preferredPartners: PreferredPartnerDto[]
  milestoneTemplate?: MilestoneTemplateDto
  transporterRatings: TransporterRatingDto[]
  brokerRatings: BrokerRatingDto[]
  hasPreferredPartnerFilter: boolean

  // Metadata
  createdAt: Date
  updatedAt: Date
  publishedAt?: Date
  closedAt?: Date
  awardedAt?: Date

  // Related Data
  bids: RfqBidSummaryDto[]
  documents: DocumentDto[]
}

interface LocationDto {
  address: string
  city: string
  state: string
  postalCode: string
  country: string
  latitude?: number
  longitude?: number
  contactPerson?: string
  contactPhone?: string
  landmark?: string
  accessInstructions?: string
}

interface RfqBidDto {
  id: string
  rfqId: string
  carrierId: string
  carrierName: string
  carrierRating: number

  // Pricing
  quotedPrice: {
    amount: number
    currency: string
    breakdown?: PriceBreakdownDto[]
  }

  // Terms
  paymentTerms: string
  validityPeriod: number // in days

  // Capabilities
  vehicleType: string
  vehicleCapacity: number
  driverExperience: number
  insuranceCoverage: number

  // Timeline
  proposedPickupDate: Date
  proposedDeliveryDate: Date
  estimatedTransitTime: number // in hours

  // Status
  status: 'Submitted' | 'UnderReview' | 'Accepted' | 'Rejected' | 'Withdrawn'
  submittedAt: Date
  reviewedAt?: Date

  // Additional Info
  comments?: string
  terms?: string
  attachments?: DocumentDto[]
}
```

#### Order DTO

```typescript
interface OrderDto {
  id: string
  orderNumber: string
  transportCompanyId: string
  brokerId?: string
  carrierId?: string
  rfqId?: string
  acceptedBidId?: string

  // Basic Info
  title: string
  description: string
  status: 'Created' | 'Confirmed' | 'InProgress' | 'Completed' | 'Cancelled'
  priority: 'Low' | 'Medium' | 'High' | 'Urgent'

  // Load & Route (inherited from RFQ)
  loadDetails: LoadDetailsDto
  routeDetails: RouteDetailsDto

  // Financial
  agreedPrice: {
    amount: number
    currency: string
    breakdown?: PriceBreakdownDto[]
  }
  paymentStatus: 'Pending' | 'Partial' | 'Paid' | 'Overdue'
  paymentTerms: string

  // Timeline
  createdAt: Date
  confirmedAt?: Date
  startedAt?: Date
  completedAt?: Date
  cancelledAt?: Date

  // Tracking
  currentStatus: string
  lastLocationUpdate?: Date
  estimatedDeliveryTime?: Date

  // Documents & Communication
  documents: DocumentDto[]
  timeline: OrderTimelineDto[]

  // Related Entities
  trip?: TripSummaryDto
  invoice?: InvoiceSummaryDto

  // Metadata
  cancellationReason?: string
  completionNotes?: string
  customerFeedback?: FeedbackDto
}

interface OrderTimelineDto {
  id: string
  orderId: string
  eventType:
    | 'Created'
    | 'Confirmed'
    | 'Started'
    | 'LocationUpdate'
    | 'Milestone'
    | 'Completed'
    | 'Cancelled'
  title: string
  description: string
  timestamp: Date
  location?: LocationDto
  performedBy: string
  performedByRole: string
  metadata?: Record<string, any>
}
```

### Trip Management Service (`/api/trips`)

#### Key Endpoints

```http
# Trip Management
POST   /api/trips                   # Create trip
GET    /api/trips                   # Get trips with filtering
GET    /api/trips/{id}              # Get trip details
PUT    /api/trips/{id}              # Update trip
PUT    /api/trips/{id}/start        # Start trip
PUT    /api/trips/{id}/complete     # Complete trip
PUT    /api/trips/{id}/cancel       # Cancel trip

# Location Tracking
POST   /api/trips/{id}/locations    # Update trip location
GET    /api/trips/{id}/tracking     # Get real-time tracking
GET    /api/trips/{id}/route        # Get trip route
PUT    /api/trips/{id}/eta          # Update ETA

# Stops Management
GET    /api/trips/{id}/stops        # Get trip stops
PUT    /api/trips/{id}/stops/{stopId}/arrive    # Mark arrival at stop
PUT    /api/trips/{id}/stops/{stopId}/depart    # Mark departure from stop
POST   /api/trips/{id}/stops/{stopId}/pod       # Submit proof of delivery

# Exception Handling
POST   /api/trips/{id}/exceptions   # Report exception
GET    /api/trips/{id}/exceptions   # Get trip exceptions
PUT    /api/trips/{id}/exceptions/{exceptionId}/resolve  # Resolve exception

# Driver & Vehicle
PUT    /api/trips/{id}/driver       # Assign/change driver
PUT    /api/trips/{id}/vehicle      # Assign/change vehicle
GET    /api/trips/{id}/timeline     # Get trip timeline
```

#### Trip DTO

```typescript
interface TripDto {
  id: string
  tripNumber: string
  orderId: string
  orderNumber: string

  // Assignment
  driverId: string
  driverName: string
  driverPhone: string
  vehicleId: string
  vehicleNumber: string
  vehicleType: string

  // Status
  status:
    | 'Assigned'
    | 'Started'
    | 'InTransit'
    | 'Completed'
    | 'Cancelled'
    | 'OnHold'
  currentLocation?: LocationDto
  lastLocationUpdate?: Date

  // Route & Stops
  stops: TripStopDto[]
  totalDistance: number
  estimatedDuration: number // in hours
  actualDuration?: number

  // Timeline
  scheduledStartTime: Date
  actualStartTime?: Date
  estimatedArrivalTime: Date
  actualArrivalTime?: Date

  // Tracking
  locationHistory: LocationUpdateDto[]
  milestones: MilestoneDto[]
  exceptions: TripExceptionDto[]

  // Documents
  proofOfDeliveries: ProofOfDeliveryDto[]
  documents: DocumentDto[]

  // Performance
  performanceMetrics: {
    onTimePerformance: number
    fuelEfficiency?: number
    customerRating?: number
    driverRating?: number
  }

  // Metadata
  createdAt: Date
  startedAt?: Date
  completedAt?: Date
  cancelledAt?: Date
  cancellationReason?: string
}

interface TripStopDto {
  id: string
  tripId: string
  sequence: number
  type: 'Pickup' | 'Delivery' | 'Intermediate'
  location: LocationDto

  // Timing
  scheduledArrival: Date
  actualArrival?: Date
  scheduledDeparture: Date
  actualDeparture?: Date

  // Status
  status: 'Pending' | 'Arrived' | 'InProgress' | 'Completed' | 'Skipped'

  // Load Details
  loadDetails?: {
    itemsToPickup?: string[]
    itemsToDeliver?: string[]
    weight?: number
    pieces?: number
  }

  // POD
  proofOfDelivery?: ProofOfDeliveryDto

  // Contact
  contactPerson?: string
  contactPhone?: string
  specialInstructions?: string
}

interface LocationUpdateDto {
  id: string
  tripId: string
  latitude: number
  longitude: number
  accuracy: number
  speed?: number
  heading?: number
  altitude?: number
  timestamp: Date
  address?: string
  source: 'GPS' | 'Manual' | 'Network'
}

interface ProofOfDeliveryDto {
  id: string
  tripId: string
  stopId: string

  // Recipient Details
  recipientName: string
  recipientPhone?: string
  recipientSignature: string // Base64 image

  // Delivery Details
  deliveredAt: Date
  deliveredBy: string
  itemsDelivered: string[]
  condition: 'Good' | 'Damaged' | 'Partial'

  // Evidence
  photos: string[] // URLs to uploaded photos
  notes?: string

  // Verification
  otp?: string
  verificationMethod: 'Signature' | 'OTP' | 'Photo' | 'BiometricSignature'

  // Metadata
  deviceInfo?: {
    deviceId: string
    platform: string
    appVersion: string
    location: LocationDto
  }
}
```

### Network & Fleet Management Service (`/api/fleet`)

#### Key Endpoints

```http
# Vehicle Management
GET    /api/vehicles                # Get vehicles
POST   /api/vehicles                # Add vehicle
GET    /api/vehicles/{id}           # Get vehicle details
PUT    /api/vehicles/{id}           # Update vehicle
DELETE /api/vehicles/{id}           # Remove vehicle
GET    /api/vehicles/{id}/documents # Get vehicle documents
POST   /api/vehicles/{id}/documents # Upload vehicle documents
GET    /api/vehicles/available      # Get available vehicles

# Driver Management
GET    /api/drivers                 # Get drivers
POST   /api/drivers                 # Add driver
GET    /api/drivers/{id}            # Get driver details
PUT    /api/drivers/{id}            # Update driver
DELETE /api/drivers/{id}            # Remove driver
GET    /api/drivers/{id}/documents  # Get driver documents
POST   /api/drivers/{id}/documents  # Upload driver documents
PUT    /api/drivers/{id}/assign     # Assign driver to vehicle
GET    /api/drivers/available       # Get available drivers

# Network Management
GET    /api/network/carriers        # Get carrier network
POST   /api/network/partnerships    # Create partnership
GET    /api/network/partnerships    # Get partnerships
PUT    /api/network/partnerships/{id} # Update partnership
DELETE /api/network/partnerships/{id} # Remove partnership

# Fleet Analytics
GET    /api/fleet/analytics         # Get fleet analytics
GET    /api/fleet/utilization       # Get utilization metrics
GET    /api/fleet/performance       # Get performance metrics
GET    /api/fleet/maintenance       # Get maintenance schedules
```

#### Vehicle DTO

```typescript
interface VehicleDto {
  id: string
  vehicleNumber: string
  vehicleType: 'Truck' | 'Trailer' | 'Container' | 'Tanker' | 'Flatbed'

  // Specifications
  specifications: {
    make: string
    model: string
    year: number
    engineNumber: string
    chassisNumber: string
    fuelType: 'Diesel' | 'Petrol' | 'CNG' | 'Electric'
    capacity: {
      weight: number // in KG
      volume?: number // in cubic meters
      length?: number
      width?: number
      height?: number
    }
  }

  // Registration & Legal
  registrationDetails: {
    registrationNumber: string
    registrationDate: Date
    registrationState: string
    ownerName: string
    rcNumber: string
    rcExpiryDate: Date
  }

  // Insurance
  insurance: {
    policyNumber: string
    provider: string
    coverageAmount: number
    startDate: Date
    expiryDate: Date
    policyDocument?: string
  }

  // Permits & Compliance
  permits: PermitDto[]

  // Status & Availability
  status: 'Active' | 'Inactive' | 'UnderMaintenance' | 'Retired'
  availability: 'Available' | 'Assigned' | 'InTransit' | 'Maintenance'
  currentLocation?: LocationDto

  // Assignment
  assignedDriverId?: string
  assignedDriverName?: string
  currentTripId?: string

  // Performance Metrics
  performanceMetrics: {
    totalTrips: number
    totalDistance: number
    fuelEfficiency: number
    maintenanceCost: number
    utilizationRate: number
    averageRating: number
  }

  // Maintenance
  lastMaintenanceDate?: Date
  nextMaintenanceDate?: Date
  maintenanceHistory: MaintenanceRecordDto[]

  // Documents
  documents: DocumentDto[]

  // Metadata
  createdAt: Date
  updatedAt: Date
  createdBy: string
}

interface DriverDto {
  id: string
  employeeId?: string

  // Personal Details
  personalDetails: {
    firstName: string
    lastName: string
    dateOfBirth: Date
    phoneNumber: string
    alternatePhoneNumber?: string
    email?: string
    address: AddressDto
    emergencyContact: {
      name: string
      relationship: string
      phoneNumber: string
    }
  }

  // License Details
  licenseDetails: {
    licenseNumber: string
    licenseType: string[]
    issueDate: Date
    expiryDate: Date
    issuingAuthority: string
    endorsements?: string[]
  }

  // Employment
  employment: {
    joinDate: Date
    employmentType: 'FullTime' | 'PartTime' | 'Contract'
    salary?: number
    benefits?: string[]
  }

  // Status & Availability
  status: 'Active' | 'Inactive' | 'OnLeave' | 'Suspended'
  availability: 'Available' | 'Assigned' | 'OnTrip' | 'OffDuty'
  currentLocation?: LocationDto

  // Assignment
  assignedVehicleId?: string
  assignedVehicleNumber?: string
  currentTripId?: string

  // Performance Metrics
  performanceMetrics: {
    totalTrips: number
    totalDistance: number
    onTimeDeliveries: number
    customerRating: number
    safetyScore: number
    fuelEfficiencyScore: number
  }

  // Training & Certifications
  certifications: CertificationDto[]
  trainingRecords: TrainingRecordDto[]

  // Documents
  documents: DocumentDto[]

  // Metadata
  createdAt: Date
  updatedAt: Date
  lastActiveAt?: Date
}
```

### Financial & Payment Service (`/api/payments`)

#### Key Endpoints

```http
# Payment Processing
POST   /api/payments                # Process payment
GET    /api/payments                # Get payments
GET    /api/payments/{id}           # Get payment details
PUT    /api/payments/{id}/refund    # Process refund
GET    /api/payments/methods        # Get payment methods
POST   /api/payments/methods        # Add payment method
DELETE /api/payments/methods/{id}   # Remove payment method

# Escrow Management
POST   /api/escrow/create           # Create escrow account
PUT    /api/escrow/{id}/release     # Release escrow funds
PUT    /api/escrow/{id}/dispute     # Raise dispute
GET    /api/escrow/{id}/status      # Get escrow status

# Invoicing
GET    /api/invoices                # Get invoices
POST   /api/invoices                # Create invoice
GET    /api/invoices/{id}           # Get invoice details
PUT    /api/invoices/{id}/send      # Send invoice
PUT    /api/invoices/{id}/pay       # Mark as paid
GET    /api/invoices/{id}/pdf       # Download invoice PDF

# Financial Reports
GET    /api/reports/revenue         # Revenue reports
GET    /api/reports/settlements     # Settlement reports
GET    /api/reports/tax             # Tax reports
GET    /api/reports/reconciliation  # Reconciliation reports
```

#### Payment DTO

```typescript
interface PaymentDto {
  id: string
  paymentNumber: string
  orderId?: string
  invoiceId?: string

  // Payment Details
  amount: {
    total: number
    currency: string
    breakdown: {
      subtotal: number
      tax: number
      fees: number
      discount: number
    }
  }

  // Parties
  payerId: string
  payerName: string
  payeeId: string
  payeeName: string

  // Payment Method
  paymentMethod: {
    type:
      | 'CreditCard'
      | 'DebitCard'
      | 'NetBanking'
      | 'UPI'
      | 'Wallet'
      | 'Cash'
      | 'Cheque'
    details: {
      cardLast4?: string
      bankName?: string
      upiId?: string
      walletProvider?: string
      chequeNumber?: string
    }
  }

  // Status & Timeline
  status:
    | 'Pending'
    | 'Processing'
    | 'Completed'
    | 'Failed'
    | 'Cancelled'
    | 'Refunded'
  createdAt: Date
  processedAt?: Date
  completedAt?: Date

  // Gateway Details
  gatewayResponse: {
    gatewayName: string
    transactionId: string
    gatewayTransactionId?: string
    responseCode: string
    responseMessage: string
  }

  // Additional Info
  description?: string
  reference?: string
  metadata?: Record<string, any>
}

interface InvoiceDto {
  id: string
  invoiceNumber: string
  orderId?: string
  tripId?: string

  // Parties
  billFrom: {
    companyId: string
    companyName: string
    address: AddressDto
    gstNumber?: string
    panNumber?: string
  }

  billTo: {
    companyId: string
    companyName: string
    address: AddressDto
    gstNumber?: string
    panNumber?: string
  }

  // Invoice Details
  issueDate: Date
  dueDate: Date
  currency: string

  // Line Items
  lineItems: InvoiceLineItemDto[]

  // Totals
  totals: {
    subtotal: number
    taxAmount: number
    discountAmount: number
    totalAmount: number
    amountPaid: number
    balanceAmount: number
  }

  // Status
  status: 'Draft' | 'Sent' | 'Viewed' | 'Paid' | 'Overdue' | 'Cancelled'
  paymentStatus: 'Unpaid' | 'Partial' | 'Paid' | 'Refunded'

  // Metadata
  createdAt: Date
  sentAt?: Date
  paidAt?: Date
  notes?: string
  terms?: string
}
```

## Support Services API Reference

### Communication & Notification Service (`/api/communication`)

#### Key Endpoints

```http
# Notifications
POST   /api/notifications/send      # Send notification
GET    /api/notifications           # Get notifications
PUT    /api/notifications/{id}/read # Mark as read
DELETE /api/notifications/{id}      # Delete notification
GET    /api/notifications/preferences # Get user preferences
PUT    /api/notifications/preferences # Update preferences

# Messaging
POST   /api/messages/send           # Send message
GET    /api/messages/conversations  # Get conversations
GET    /api/messages/conversations/{id} # Get conversation messages
PUT    /api/messages/{id}/read      # Mark message as read

# Templates
GET    /api/templates               # Get message templates
POST   /api/templates               # Create template
PUT    /api/templates/{id}          # Update template
DELETE /api/templates/{id}          # Delete template

# Real-time Communication
GET    /api/chat/rooms              # Get chat rooms
POST   /api/chat/rooms              # Create chat room
POST   /api/chat/rooms/{id}/join    # Join chat room
POST   /api/chat/rooms/{id}/leave   # Leave chat room
```

#### Notification DTO

```typescript
interface NotificationDto {
  id: string
  userId: string
  type: 'Info' | 'Warning' | 'Error' | 'Success'
  category: 'Order' | 'Trip' | 'Payment' | 'System' | 'Marketing'

  // Content
  title: string
  message: string
  actionUrl?: string
  actionText?: string

  // Delivery
  channels: ('Push' | 'Email' | 'SMS' | 'InApp')[]
  priority: 'Low' | 'Medium' | 'High' | 'Critical'

  // Status
  status: 'Pending' | 'Sent' | 'Delivered' | 'Read' | 'Failed'
  isRead: boolean
  readAt?: Date

  // Metadata
  createdAt: Date
  sentAt?: Date
  deliveredAt?: Date
  expiresAt?: Date
  metadata?: Record<string, any>
}
```

### Analytics & BI Service (`/api/analytics`)

#### Key Endpoints

```http
# Dashboards
GET    /api/dashboards              # Get dashboards
GET    /api/dashboards/{id}         # Get dashboard details
POST   /api/dashboards              # Create custom dashboard
PUT    /api/dashboards/{id}         # Update dashboard
DELETE /api/dashboards/{id}         # Delete dashboard

# Reports
GET    /api/reports                 # Get available reports
POST   /api/reports/generate        # Generate report
GET    /api/reports/{id}            # Get report details
GET    /api/reports/{id}/download   # Download report
POST   /api/reports/schedule        # Schedule report

# Metrics
GET    /api/metrics/kpis            # Get KPI metrics
GET    /api/metrics/performance     # Get performance metrics
GET    /api/metrics/financial       # Get financial metrics
GET    /api/metrics/operational     # Get operational metrics

# Data Export
POST   /api/export/data             # Export data
GET    /api/export/jobs             # Get export jobs
GET    /api/export/jobs/{id}        # Get export job status
GET    /api/export/jobs/{id}/download # Download exported data
```

### Data & Storage Service (`/api/storage`)

#### Key Endpoints

```http
# File Management
POST   /api/files/upload            # Upload file
GET    /api/files                   # Get files
GET    /api/files/{id}              # Get file details
DELETE /api/files/{id}              # Delete file
GET    /api/files/{id}/download     # Download file
POST   /api/files/{id}/share        # Share file

# Document Processing
POST   /api/documents/process       # Process document
GET    /api/documents/{id}/ocr      # Get OCR results
POST   /api/documents/{id}/extract  # Extract data from document

# Media Processing
POST   /api/media/upload            # Upload media
POST   /api/media/{id}/transcode    # Transcode media
GET    /api/media/{id}/thumbnail    # Get thumbnail
```

#### File DTO

```typescript
interface FileDto {
  id: string
  fileName: string
  originalFileName: string
  fileSize: number
  mimeType: string
  fileUrl: string
  thumbnailUrl?: string

  // Metadata
  uploadedBy: string
  uploadedAt: Date
  lastAccessedAt?: Date

  // Organization
  folderId?: string
  tags: string[]
  category: string

  // Security
  isPublic: boolean
  accessLevel: 'Private' | 'Internal' | 'Public'
  expiresAt?: Date

  // Processing
  processingStatus?: 'Pending' | 'Processing' | 'Completed' | 'Failed'
  ocrData?: {
    extractedText: string
    confidence: number
    language: string
  }
}
```

## Real-time Communication

### SignalR Hubs

The TLI platform uses SignalR for real-time communication across multiple hubs:

#### Trip Tracking Hub (`/hubs/trip-tracking`)

```typescript
// Client-side connection
const connection = new signalR.HubConnectionBuilder()
  .withUrl('/hubs/trip-tracking', {
    accessTokenFactory: () => getAuthToken(),
  })
  .build()

// Subscribe to trip updates
connection.on(
  'TripLocationUpdated',
  (tripId: string, location: LocationUpdateDto) => {
    // Handle location update
  }
)

connection.on(
  'TripStatusChanged',
  (tripId: string, status: string, timestamp: Date) => {
    // Handle status change
  }
)

// Send location update (for drivers)
connection.invoke('UpdateLocation', tripId, locationData)
```

#### Order Management Hub (`/hubs/order-management`)

```typescript
// Subscribe to bid updates
connection.on('NewBidReceived', (rfqId: string, bid: RfqBidDto) => {
  // Handle new bid
})

connection.on('BidStatusChanged', (bidId: string, status: string) => {
  // Handle bid status change
})

connection.on('OrderStatusChanged', (orderId: string, status: string) => {
  // Handle order status change
})
```

#### Notification Hub (`/hubs/notifications`)

```typescript
// Subscribe to notifications
connection.on('NotificationReceived', (notification: NotificationDto) => {
  // Handle new notification
})

connection.on('MessageReceived', (message: MessageDto) => {
  // Handle new message
})
```

### WebSocket Events

Common event patterns across all hubs:

```typescript
interface WebSocketEvent<T = any> {
  eventType: string
  entityId: string
  entityType: string
  data: T
  timestamp: Date
  userId?: string
  metadata?: Record<string, any>
}

// Example events
type TripEvent = WebSocketEvent<{
  tripId: string
  location?: LocationUpdateDto
  status?: string
  milestone?: MilestoneDto
}>

type OrderEvent = WebSocketEvent<{
  orderId: string
  status?: string
  bid?: RfqBidDto
  timeline?: OrderTimelineDto
}>
```

## Data Models & DTOs

### Common Data Types

```typescript
// Pagination
interface PagedResult<T> {
  data: T[]
  totalCount: number
  pageNumber: number
  pageSize: number
  totalPages: number
  hasNextPage: boolean
  hasPreviousPage: boolean
}

// API Response Wrapper
interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  errors?: ValidationError[]
  metadata?: {
    timestamp: Date
    requestId: string
    version: string
  }
}

// Validation Error
interface ValidationError {
  field: string
  message: string
  code: string
  value?: any
}

// Money/Currency
interface MoneyDto {
  amount: number
  currency: string
  formattedAmount?: string
}

// Address
interface AddressDto {
  street: string
  city: string
  state: string
  postalCode: string
  country: string
  landmark?: string
  latitude?: number
  longitude?: number
}

// Contact Information
interface ContactDto {
  name: string
  phone: string
  email?: string
  designation?: string
}
```

### Enums and Constants

```typescript
// User Types
enum UserType {
  Admin = 'Admin',
  Broker = 'Broker',
  Transporter = 'Transporter',
  Shipper = 'Shipper',
  Driver = 'Driver',
}

// Order Status
enum OrderStatus {
  Draft = 'Draft',
  Published = 'Published',
  Bidding = 'Bidding',
  Awarded = 'Awarded',
  InProgress = 'InProgress',
  Completed = 'Completed',
  Cancelled = 'Cancelled',
}

// Trip Status
enum TripStatus {
  Assigned = 'Assigned',
  Started = 'Started',
  InTransit = 'InTransit',
  AtPickup = 'AtPickup',
  AtDelivery = 'AtDelivery',
  Completed = 'Completed',
  Cancelled = 'Cancelled',
}

// Payment Status
enum PaymentStatus {
  Pending = 'Pending',
  Processing = 'Processing',
  Completed = 'Completed',
  Failed = 'Failed',
  Refunded = 'Refunded',
}

// Document Types
enum DocumentType {
  PAN = 'PAN',
  GST = 'GST',
  Aadhar = 'Aadhar',
  DrivingLicense = 'DrivingLicense',
  VehicleRC = 'VehicleRC',
  Insurance = 'Insurance',
  Permit = 'Permit',
  Invoice = 'Invoice',
  POD = 'POD',
}
```

## Frontend Integration Patterns

### API Gateway Configuration

All frontend applications should connect through the API Gateway at `http://localhost:5000/api`. The gateway handles:

- **Request Routing**: Routes requests to appropriate microservices
- **Authentication**: Validates JWT tokens
- **Rate Limiting**: Prevents API abuse
- **CORS**: Handles cross-origin requests
- **Load Balancing**: Distributes load across service instances

#### CORS Configuration

```typescript
// Allowed origins for CORS
const allowedOrigins = [
  'http://localhost:3000', // React dev server
  'http://localhost:4200', // Angular dev server
  'https://app.tli.com', // Production web app
  'https://admin.tli.com', // Admin dashboard
]

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers':
    'Content-Type, Authorization, X-Requested-With',
  'Access-Control-Max-Age': '86400',
}
```

### HTTP Client Configuration

#### Axios Configuration (React/Vue)

```typescript
import axios from 'axios'

const apiClient = axios.create({
  baseURL: process.env.REACT_APP_API_BASE_URL || 'http://localhost:5000/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    Accept: 'application/json',
  },
})

// Request interceptor for auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => Promise.reject(error)
)

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized - redirect to login
      localStorage.removeItem('authToken')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)
```

#### Angular HTTP Client

```typescript
import { Injectable } from '@angular/core'
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http'
import { Observable } from 'rxjs'

@Injectable({
  providedIn: 'root',
})
export class ApiService {
  private baseUrl = environment.apiBaseUrl || 'http://localhost:5000/api'

  constructor(private http: HttpClient) {}

  private getHeaders(): HttpHeaders {
    const token = localStorage.getItem('authToken')
    return new HttpHeaders({
      'Content-Type': 'application/json',
      Authorization: token ? `Bearer ${token}` : '',
    })
  }

  get<T>(endpoint: string, params?: any): Observable<T> {
    const httpParams = new HttpParams({ fromObject: params })
    return this.http.get<T>(`${this.baseUrl}/${endpoint}`, {
      headers: this.getHeaders(),
      params: httpParams,
    })
  }

  post<T>(endpoint: string, data: any): Observable<T> {
    return this.http.post<T>(`${this.baseUrl}/${endpoint}`, data, {
      headers: this.getHeaders(),
    })
  }
}
```

### Pagination Implementation

```typescript
interface PaginationParams {
  page: number
  pageSize: number
  sortBy?: string
  sortDirection?: 'asc' | 'desc'
  searchTerm?: string
}

// React Hook for pagination
function usePagination<T>(
  fetchFunction: (params: PaginationParams) => Promise<PagedResult<T>>,
  initialParams: PaginationParams = { page: 1, pageSize: 20 }
) {
  const [data, setData] = useState<T[]>([])
  const [loading, setLoading] = useState(false)
  const [pagination, setPagination] = useState<PagedResult<T> | null>(null)
  const [params, setParams] = useState(initialParams)

  const fetchData = useCallback(async () => {
    setLoading(true)
    try {
      const result = await fetchFunction(params)
      setData(result.data)
      setPagination(result)
    } catch (error) {
      console.error('Error fetching data:', error)
    } finally {
      setLoading(false)
    }
  }, [fetchFunction, params])

  useEffect(() => {
    fetchData()
  }, [fetchData])

  return {
    data,
    loading,
    pagination,
    params,
    setParams,
    refresh: fetchData,
  }
}
```

### File Upload Implementation

```typescript
// File upload with progress tracking
async function uploadFile(
  file: File,
  endpoint: string,
  onProgress?: (progress: number) => void
): Promise<FileDto> {
  const formData = new FormData()
  formData.append('file', file)

  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest()

    xhr.upload.addEventListener('progress', (event) => {
      if (event.lengthComputable && onProgress) {
        const progress = (event.loaded / event.total) * 100
        onProgress(progress)
      }
    })

    xhr.addEventListener('load', () => {
      if (xhr.status === 200) {
        resolve(JSON.parse(xhr.responseText))
      } else {
        reject(new Error(`Upload failed: ${xhr.statusText}`))
      }
    })

    xhr.addEventListener('error', () => {
      reject(new Error('Upload failed'))
    })

    xhr.open('POST', `${API_BASE_URL}/${endpoint}`)
    xhr.setRequestHeader('Authorization', `Bearer ${getAuthToken()}`)
    xhr.send(formData)
  })
}

// React component for file upload
function FileUpload({
  onUploadComplete,
}: {
  onUploadComplete: (file: FileDto) => void
}) {
  const [uploading, setUploading] = useState(false)
  const [progress, setProgress] = useState(0)

  const handleFileSelect = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0]
    if (!file) return

    setUploading(true)
    setProgress(0)

    try {
      const uploadedFile = await uploadFile(
        file,
        'storage/files/upload',
        setProgress
      )
      onUploadComplete(uploadedFile)
    } catch (error) {
      console.error('Upload error:', error)
    } finally {
      setUploading(false)
      setProgress(0)
    }
  }

  return (
    <div>
      <input type="file" onChange={handleFileSelect} disabled={uploading} />
      {uploading && (
        <div>
          <div>Uploading... {Math.round(progress)}%</div>
          <progress value={progress} max={100} />
        </div>
      )}
    </div>
  )
}
```

### Real-time Updates Integration

```typescript
// React hook for SignalR connection
function useSignalR(hubUrl: string, dependencies: any[] = []) {
  const [connection, setConnection] = useState<signalR.HubConnection | null>(
    null
  )
  const [connected, setConnected] = useState(false)

  useEffect(() => {
    const newConnection = new signalR.HubConnectionBuilder()
      .withUrl(hubUrl, {
        accessTokenFactory: () => getAuthToken(),
      })
      .withAutomaticReconnect()
      .build()

    setConnection(newConnection)

    newConnection
      .start()
      .then(() => {
        setConnected(true)
        console.log('SignalR connected')
      })
      .catch((err) => console.error('SignalR connection error:', err))

    return () => {
      newConnection.stop()
    }
  }, dependencies)

  return { connection, connected }
}

// Usage in component
function TripTracker({ tripId }: { tripId: string }) {
  const { connection, connected } = useSignalR('/hubs/trip-tracking', [tripId])
  const [location, setLocation] = useState<LocationUpdateDto | null>(null)

  useEffect(() => {
    if (!connection || !connected) return

    connection.on(
      'TripLocationUpdated',
      (id: string, loc: LocationUpdateDto) => {
        if (id === tripId) {
          setLocation(loc)
        }
      }
    )

    return () => {
      connection.off('TripLocationUpdated')
    }
  }, [connection, connected, tripId])

  return (
    <div>
      {location && (
        <div>
          Last update: {location.timestamp}
          Location: {location.latitude}, {location.longitude}
        </div>
      )}
    </div>
  )
}
```

## Error Handling

### Standard Error Response Format

```typescript
interface ErrorResponse {
  success: false
  message: string
  errors?: ValidationError[]
  errorCode?: string
  timestamp: Date
  requestId: string
}

// Common error codes
enum ErrorCodes {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  NOT_FOUND = 'NOT_FOUND',
  CONFLICT = 'CONFLICT',
  RATE_LIMITED = 'RATE_LIMITED',
  INTERNAL_ERROR = 'INTERNAL_ERROR',
}
```

### Error Handling Patterns

```typescript
// Global error handler
function handleApiError(error: any): string {
  if (error.response?.data?.errors) {
    // Validation errors
    return error.response.data.errors
      .map((e: ValidationError) => e.message)
      .join(', ')
  }

  if (error.response?.data?.message) {
    return error.response.data.message
  }

  switch (error.response?.status) {
    case 401:
      return 'Please log in to continue'
    case 403:
      return 'You do not have permission to perform this action'
    case 404:
      return 'The requested resource was not found'
    case 429:
      return 'Too many requests. Please try again later'
    case 500:
      return 'An internal server error occurred'
    default:
      return 'An unexpected error occurred'
  }
}

// React error boundary
class ApiErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: any) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('API Error:', error, errorInfo)
    // Log to error reporting service
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="error-boundary">
          <h2>Something went wrong</h2>
          <p>
            Please refresh the page or contact support if the problem persists.
          </p>
        </div>
      )
    }

    return this.props.children
  }
}
```

## Best Practices

### 1. Authentication & Security

- **Always use HTTPS** in production
- **Store JWT tokens securely** (httpOnly cookies preferred over localStorage)
- **Implement token refresh** before expiration
- **Validate permissions** on the frontend (but rely on backend validation)
- **Sanitize user inputs** to prevent XSS attacks

### 2. Performance Optimization

- **Implement caching** for frequently accessed data
- **Use pagination** for large datasets
- **Debounce search inputs** to reduce API calls
- **Implement virtual scrolling** for large lists
- **Lazy load components** and routes
- **Optimize bundle size** with code splitting

### 3. User Experience

- **Show loading states** for all async operations
- **Implement optimistic updates** where appropriate
- **Provide clear error messages** with actionable guidance
- **Use skeleton screens** instead of spinners
- **Implement offline support** for mobile apps
- **Add progress indicators** for long-running operations

### 4. Code Organization

- **Use TypeScript** for type safety
- **Implement proper error boundaries**
- **Create reusable API service functions**
- **Use consistent naming conventions**
- **Implement proper logging** for debugging
- **Write unit tests** for critical functionality

### 5. Mobile Considerations

- **Implement offline-first architecture**
- **Use responsive design** principles
- **Optimize for touch interactions**
- **Implement proper caching** strategies
- **Handle network connectivity** changes
- **Use native device features** when available

This comprehensive guide provides frontend developers with all the necessary information to integrate with the TLI microservices platform effectively. The detailed API specifications, data models, and integration patterns ensure consistent and efficient development across all frontend applications.
fuelEfficiencyScore: number
}

// Training & Certifications
certifications: CertificationDto[]
trainingRecords: TrainingRecordDto[]

// Documents
documents: DocumentDto[]

// Metadata
createdAt: Date
updatedAt: Date
lastActiveAt?: Date
}

```

```

```

```
