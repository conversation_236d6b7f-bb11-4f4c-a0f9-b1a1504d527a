using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Shared.Messaging
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddMessaging(this IServiceCollection services, string rabbitMQHost = "localhost")
        {
            services.AddSingleton<IMessageBroker>(provider =>
            {
                var logger = provider.GetRequiredService<ILogger<RabbitMQMessageBroker>>();
                return new RabbitMQMessageBroker(rabbitMQHost, logger);
            });

            return services;
        }
    }
}
