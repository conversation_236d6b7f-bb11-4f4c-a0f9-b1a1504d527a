using FluentValidation;

namespace SubscriptionManagement.Application.Commands.VerifyPaymentProof
{
    public class VerifyPaymentProofCommandValidator : AbstractValidator<VerifyPaymentProofCommand>
    {
        public VerifyPaymentProofCommandValidator()
        {
            RuleFor(x => x.PaymentProofId)
                .NotEmpty()
                .WithMessage("Payment proof ID is required");

            RuleFor(x => x.VerifiedByUserId)
                .NotEmpty()
                .WithMessage("Verified by user ID is required");

            RuleFor(x => x.VerificationNotes)
                .MaximumLength(2000)
                .WithMessage("Verification notes cannot exceed 2000 characters");
        }
    }
}
