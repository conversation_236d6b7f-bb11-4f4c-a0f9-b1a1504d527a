using Microsoft.EntityFrameworkCore;
using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Repositories;
using AnalyticsBIService.Infrastructure.Persistence;
using Shared.Infrastructure.Repositories;
using Shared.Infrastructure.Caching;
using Shared.Infrastructure.Monitoring;
using AnalyticsBIService.Domain.Enums;

namespace AnalyticsBIService.Infrastructure.Repositories;

public class DashboardRepository : OptimizedRepositoryBase<Dashboard, Guid>, IDashboardRepository
{
    public DashboardRepository(
        AnalyticsBIDbContext context,
        ICachingService cachingService,
        IPerformanceMetrics performanceMetrics)
        : base(context, cachingService, performanceMetrics)
    {
    }

    public async Task<PagedResult<Dashboard>> GetByUserIdAsync(Guid userId, int page, int pageSize, CancellationToken cancellationToken = default)
    {
        var query = Context.Set<Dashboard>()
            .Include(d => d.Widgets)
            .Where(d => d.CreatedBy == userId)
            .OrderByDescending(d => d.CreatedAt);

        var totalCount = await query.CountAsync(cancellationToken);
        var items = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return new PagedResult<Dashboard>
        {
            Items = items,
            TotalCount = totalCount,
            PageNumber = page,
            PageSize = pageSize
        };
    }

    public async Task<PagedResult<Dashboard>> GetByTypeAsync(DashboardType type, int page, int pageSize, CancellationToken cancellationToken = default)
    {
        var query = Context.Set<Dashboard>()
            .Include(d => d.Widgets)
            .Where(d => d.Type == type)
            .OrderByDescending(d => d.CreatedAt);

        var totalCount = await query.CountAsync(cancellationToken);
        var items = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return new PagedResult<Dashboard>
        {
            Items = items,
            TotalCount = totalCount,
            PageNumber = page,
            PageSize = pageSize
        };
    }

    public async Task<Dashboard?> GetByNameAsync(string name, Guid userId, CancellationToken cancellationToken = default)
    {
        return await Context.Set<Dashboard>()
            .Include(d => d.Widgets)
            .FirstOrDefaultAsync(d => d.Name == name && d.CreatedBy == userId, cancellationToken);
    }

    public async Task<PagedResult<Dashboard>> GetPublicDashboardsAsync(int page, int pageSize, CancellationToken cancellationToken = default)
    {
        var query = Context.Set<Dashboard>()
            .Include(d => d.Widgets)
            .Where(d => d.IsPublic)
            .OrderByDescending(d => d.CreatedAt);

        var totalCount = await query.CountAsync(cancellationToken);
        var items = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return new PagedResult<Dashboard>
        {
            Items = items,
            TotalCount = totalCount,
            PageNumber = page,
            PageSize = pageSize
        };
    }

    public async Task<PagedResult<Dashboard>> GetSharedDashboardsAsync(Guid userId, int page, int pageSize, CancellationToken cancellationToken = default)
    {
        // This would require a separate sharing table in a real implementation
        // For now, return public dashboards as a placeholder
        return await GetPublicDashboardsAsync(page, pageSize, cancellationToken);
    }

    public async Task<Dashboard?> GetWithWidgetsAsync(Guid dashboardId, CancellationToken cancellationToken = default)
    {
        return await Context.Set<Dashboard>()
            .Include(d => d.Widgets)
            .FirstOrDefaultAsync(d => d.Id == dashboardId, cancellationToken);
    }

    public async Task<Dashboard> CloneDashboardAsync(Guid sourceDashboardId, string newName, Guid newUserId, CancellationToken cancellationToken = default)
    {
        var sourceDashboard = await GetWithWidgetsAsync(sourceDashboardId, cancellationToken);
        if (sourceDashboard == null)
            throw new InvalidOperationException($"Dashboard with ID {sourceDashboardId} not found");

        var clonedDashboard = new Dashboard(
            newName,
            sourceDashboard.Description,
            sourceDashboard.Type,
            newUserId,
            sourceDashboard.UserType,
            sourceDashboard.IsDefault,
            sourceDashboard.IsPublic,
            sourceDashboard.Configuration
        );

        await Context.Set<Dashboard>().AddAsync(clonedDashboard, cancellationToken);
        await Context.SaveChangesAsync(cancellationToken);

        return clonedDashboard;
    }

    public async Task UpdateLayoutAsync(Guid dashboardId, string layout, CancellationToken cancellationToken = default)
    {
        var dashboard = await Context.Set<Dashboard>().FindAsync(new object[] { dashboardId }, cancellationToken);
        if (dashboard != null)
        {
            dashboard.UpdateConfiguration(new Dictionary<string, object> { ["layout"] = layout });
            await Context.SaveChangesAsync(cancellationToken);
        }
    }

    public async Task<List<Dashboard>> GetFavoriteDashboardsAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        // This would require a separate favorites table in a real implementation
        // For now, return user's own dashboards as a placeholder
        return await Context.Set<Dashboard>()
            .Include(d => d.Widgets)
            .Where(d => d.CreatedBy == userId)
            .OrderByDescending(d => d.LastAccessedAt)
            .Take(10)
            .ToListAsync(cancellationToken);
    }

    public async Task SetFavoriteAsync(Guid dashboardId, Guid userId, bool isFavorite, CancellationToken cancellationToken = default)
    {
        // This would require a separate favorites table in a real implementation
        // For now, just update the dashboard's last accessed time as a placeholder
        var dashboard = await Context.Set<Dashboard>().FindAsync(new object[] { dashboardId }, cancellationToken);
        if (dashboard != null && dashboard.CreatedBy == userId)
        {
            dashboard.RecordAccess(userId, dashboard.UserType ?? UserType.TransportCompany);
            await Context.SaveChangesAsync(cancellationToken);
        }
    }

    public async Task<IEnumerable<Dashboard>> GetRecentlyViewedAsync(Guid userId, int count = 10, CancellationToken cancellationToken = default)
    {
        // This would require tracking view history in a real implementation
        // For now, return user's recent dashboards
        return await Context.Set<Dashboard>()
            .Include(d => d.Widgets)
            .Where(d => d.CreatedBy == userId)
            .OrderByDescending(d => d.UpdatedAt)
            .Take(count)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Dashboard>> SearchAsync(string searchTerm, CancellationToken cancellationToken = default)
    {
        return await Context.Set<Dashboard>()
            .Include(d => d.Widgets)
            .Where(d => d.Name.Contains(searchTerm) ||
                       d.Description.Contains(searchTerm))
            .OrderByDescending(d => d.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Dashboard>> GetByTagAsync(string tag, CancellationToken cancellationToken = default)
    {
        return await Context.Set<Dashboard>()
            .Include(d => d.Widgets)
            .Where(d => d.Tags.Contains(tag))
            .OrderByDescending(d => d.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<Dictionary<DashboardType, int>> GetCountsByTypeAsync(CancellationToken cancellationToken = default)
    {
        return await Context.Set<Dashboard>()
            .GroupBy(d => d.Type)
            .Select(g => new { Type = g.Key, Count = g.Count() })
            .ToDictionaryAsync(x => x.Type, x => x.Count, cancellationToken);
    }

    public async Task<int> GetUserDashboardCountAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await Context.Set<Dashboard>()
            .CountAsync(d => d.CreatedBy == userId, cancellationToken);
    }

    public async Task<IEnumerable<Dashboard>> GetMostPopularAsync(int count = 10, CancellationToken cancellationToken = default)
    {
        // This would require tracking view counts in a real implementation
        // For now, return most recently created public dashboards
        return await Context.Set<Dashboard>()
            .Include(d => d.Widgets)
            .Where(d => d.IsPublic)
            .OrderByDescending(d => d.CreatedAt)
            .Take(count)
            .ToListAsync(cancellationToken);
    }
}
