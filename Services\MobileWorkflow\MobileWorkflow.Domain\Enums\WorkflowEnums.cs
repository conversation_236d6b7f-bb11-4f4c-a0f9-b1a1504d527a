namespace MobileWorkflow.Domain.Enums;

public enum WorkflowStatus
{
    Draft = 1,
    Active = 2,
    Inactive = 3,
    Archived = 4,
    Deprecated = 5
}

public enum WorkflowExecutionStatus
{
    Pending = 1,
    Running = 2,
    Completed = 3,
    Failed = 4,
    Cancelled = 5,
    Paused = 6,
    Timeout = 7
}

public enum WorkflowTriggerType
{
    Manual = 1,
    Event = 2,
    Schedule = 3,
    API = 4,
    Webhook = 5,
    FileUpload = 6,
    EmailReceived = 7,
    StatusChange = 8
}

public enum TaskType
{
    Manual = 1,
    Automated = 2,
    Approval = 3,
    Notification = 4,
    DataValidation = 5,
    DocumentGeneration = 6,
    EmailSend = 7,
    SMSSend = 8,
    APICall = 9,
    DatabaseUpdate = 10,
    FileProcessing = 11,
    Calculation = 12,
    Condition = 13,
    Loop = 14,
    Parallel = 15,
    Delay = 16,
    UserInput = 17,
    Integration = 18
}

public enum TaskStatus
{
    Pending = 1,
    InProgress = 2,
    Completed = 3,
    Failed = 4,
    Cancelled = 5,
    Skipped = 6,
    OnHold = 7,
    Overdue = 8
}

public enum TaskPriority
{
    Critical = 1,
    High = 2,
    Medium = 3,
    Low = 4
}

public enum WorkflowCategory
{
    OrderProcessing = 1,
    TripManagement = 2,
    DocumentVerification = 3,
    UserOnboarding = 4,
    PaymentProcessing = 5,
    ComplianceCheck = 6,
    MaintenanceScheduling = 7,
    NotificationDelivery = 8,
    DataSynchronization = 9,
    ReportGeneration = 10,
    AuditTrail = 11,
    EmergencyResponse = 12,
    QualityAssurance = 13,
    CustomerService = 14,
    InventoryManagement = 15
}

public enum SLAStatus
{
    OnTime = 1,
    AtRisk = 2,
    Overdue = 3,
    Breached = 4,
    Waived = 5
}
