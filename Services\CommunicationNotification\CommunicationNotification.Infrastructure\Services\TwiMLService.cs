using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Domain.Entities;
using CommunicationNotification.Domain.ValueObjects;
using Microsoft.Extensions.Logging;
using Twilio.TwiML;

namespace CommunicationNotification.Infrastructure.Services;

/// <summary>
/// Service for generating TwiML responses for IVR systems
/// </summary>
public class TwiMLService : ITwiMLService
{
    private readonly ILogger<TwiMLService> _logger;

    public TwiMLService(ILogger<TwiMLService> logger)
    {
        _logger = logger;
    }

    public async Task<string> GenerateMenuTwiMLAsync(
        IVRMenu menu,
        IVRSession session,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Generating TwiML for menu {MenuName} in session {SessionId}", 
                menu.Name, session.SessionId);

            var response = new VoiceResponse();

            // Add welcome message or menu prompt
            var say = new Say(menu.Prompt)
            {
                Voice = GetVoiceForLanguage(session.Language),
                Language = session.Language
            };
            response.Append(say);

            // Add gather for user input
            var gather = new Gather()
            {
                NumDigits = 1,
                Timeout = menu.TimeoutSeconds,
                Action = $"/api/communication/ivr/sessions/{session.SessionId}/input",
                Method = "POST"
            };

            // Add menu options to the gather prompt
            var optionsText = BuildMenuOptionsText(menu);
            if (!string.IsNullOrEmpty(optionsText))
            {
                var optionsSay = new Say(optionsText)
                {
                    Voice = GetVoiceForLanguage(session.Language),
                    Language = session.Language
                };
                gather.Append(optionsSay);
            }

            response.Append(gather);

            // Add fallback for no input
            var noInputSay = new Say("I didn't receive any input. Please try again.")
            {
                Voice = GetVoiceForLanguage(session.Language),
                Language = session.Language
            };
            response.Append(noInputSay);

            // Redirect back to the same menu
            response.Redirect($"/api/communication/ivr/sessions/{session.SessionId}/menu/{menu.Id}");

            var twiml = response.ToString();
            _logger.LogDebug("Generated TwiML for menu {MenuName}: {TwiML}", menu.Name, twiml);

            return twiml;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating menu TwiML for menu {MenuName}", menu.Name);
            throw;
        }
    }

    public async Task<string> GenerateMessageTwiMLAsync(
        string message,
        string language = "en-US",
        VoiceSettings? voiceSettings = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Generating TwiML for message: {Message}", message);

            var response = new VoiceResponse();

            var say = new Say(message)
            {
                Voice = voiceSettings?.VoiceId ?? GetVoiceForLanguage(language),
                Language = language
            };

            response.Append(say);

            var twiml = response.ToString();
            _logger.LogDebug("Generated message TwiML: {TwiML}", twiml);

            return twiml;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating message TwiML");
            throw;
        }
    }

    public async Task<string> GenerateInputCollectionTwiMLAsync(
        string prompt,
        int maxDigits,
        int timeoutSeconds,
        string finishOnKey = "#",
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Generating input collection TwiML for prompt: {Prompt}", prompt);

            var response = new VoiceResponse();

            var gather = new Gather()
            {
                NumDigits = maxDigits,
                Timeout = timeoutSeconds,
                FinishOnKey = finishOnKey,
                Action = "/api/communication/ivr/input/collected",
                Method = "POST"
            };

            var say = new Say(prompt)
            {
                Voice = "alice",
                Language = "en-US"
            };
            gather.Append(say);

            response.Append(gather);

            // Add fallback for no input
            response.Say("No input received. Goodbye.");
            response.Hangup();

            var twiml = response.ToString();
            _logger.LogDebug("Generated input collection TwiML: {TwiML}", twiml);

            return twiml;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating input collection TwiML");
            throw;
        }
    }

    public async Task<string> GenerateTransferTwiMLAsync(
        string transferNumber,
        string? transferMessage = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Generating transfer TwiML to number: {TransferNumber}", transferNumber);

            var response = new VoiceResponse();

            // Play transfer message if provided
            if (!string.IsNullOrEmpty(transferMessage))
            {
                response.Say(transferMessage);
            }

            // Transfer the call
            var dial = new Dial()
            {
                Timeout = 30,
                Action = "/api/communication/ivr/transfer/status",
                Method = "POST"
            };

            dial.Number(transferNumber);
            response.Append(dial);

            // Fallback if transfer fails
            response.Say("Transfer failed. Goodbye.");
            response.Hangup();

            var twiml = response.ToString();
            _logger.LogDebug("Generated transfer TwiML: {TwiML}", twiml);

            return twiml;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating transfer TwiML");
            throw;
        }
    }

    public async Task<string> GenerateVoiceRecognitionTwiMLAsync(
        string prompt,
        List<string> expectedPhrases,
        string language = "en-US",
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Generating voice recognition TwiML for prompt: {Prompt}", prompt);

            var response = new VoiceResponse();

            var gather = new Gather()
            {
                Input = new List<Gather.InputEnum> { Gather.InputEnum.Speech },
                Language = language,
                Timeout = 10,
                Action = "/api/communication/ivr/speech/recognized",
                Method = "POST"
            };

            // Add speech hints if provided
            if (expectedPhrases.Any())
            {
                gather.SpeechTimeout = "auto";
                // Note: Twilio speech hints would be configured at the account level
            }

            var say = new Say(prompt)
            {
                Voice = GetVoiceForLanguage(language),
                Language = language
            };
            gather.Append(say);

            response.Append(gather);

            // Fallback for no speech input
            response.Say("I didn't hear anything. Please try again.");
            response.Hangup();

            var twiml = response.ToString();
            _logger.LogDebug("Generated voice recognition TwiML: {TwiML}", twiml);

            return twiml;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating voice recognition TwiML");
            throw;
        }
    }

    // Helper methods
    private string BuildMenuOptionsText(IVRMenu menu)
    {
        if (!menu.Options.Any())
            return string.Empty;

        var optionsText = "Please select from the following options: ";
        var enabledOptions = menu.Options.Where(o => o.IsEnabled).ToList();

        for (int i = 0; i < enabledOptions.Count; i++)
        {
            var option = enabledOptions[i];
            optionsText += $"Press {option.Key} for {option.Description}";
            
            if (i < enabledOptions.Count - 1)
                optionsText += ", ";
            else
                optionsText += ".";
        }

        return optionsText;
    }

    private string GetVoiceForLanguage(string language)
    {
        return language switch
        {
            "hi-IN" => "Polly.Aditi",
            "kn-IN" => "Polly.Aditi", // Fallback to Hindi voice for Kannada
            "ta-IN" => "Polly.Aditi", // Fallback to Hindi voice for Tamil
            "te-IN" => "Polly.Aditi", // Fallback to Hindi voice for Telugu
            "en-GB" => "Polly.Emma",
            "en-AU" => "Polly.Nicole",
            _ => "alice" // Default Twilio voice
        };
    }

    private string GetLanguageCode(string language)
    {
        return language switch
        {
            "hi-IN" => "hi-IN",
            "kn-IN" => "hi-IN", // Fallback to Hindi for Kannada
            "ta-IN" => "hi-IN", // Fallback to Hindi for Tamil
            "te-IN" => "hi-IN", // Fallback to Hindi for Telugu
            "en-GB" => "en-GB",
            "en-AU" => "en-AU",
            _ => "en-US"
        };
    }
}
