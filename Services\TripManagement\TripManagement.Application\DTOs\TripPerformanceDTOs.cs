using TripManagement.Domain.Enums;

namespace TripManagement.Application.DTOs;

public record CarrierTripPerformanceDto
{
    public Guid CarrierId { get; init; }
    public string CarrierName { get; init; } = string.Empty;
    public DateTime FromDate { get; init; }
    public DateTime ToDate { get; init; }
    
    // Trip Statistics
    public int TotalTrips { get; init; }
    public int CompletedTrips { get; init; }
    public int CancelledTrips { get; init; }
    public int InProgressTrips { get; init; }
    public int DelayedTrips { get; init; }
    public decimal TripCompletionRate { get; init; }
    public decimal TripCancellationRate { get; init; }
    
    // On-Time Performance
    public int OnTimeTrips { get; init; }
    public decimal OnTimeDeliveryRate { get; init; }
    public TimeSpan AverageDelay { get; init; }
    public TimeSpan MaxDelay { get; init; }
    public TimeSpan MinDelay { get; init; }
    
    // Distance and Duration Metrics
    public decimal TotalDistanceKm { get; init; }
    public decimal AverageDistancePerTrip { get; init; }
    public TimeSpan TotalDuration { get; init; }
    public TimeSpan AverageDuration { get; init; }
    public decimal AverageSpeedKmh { get; init; }
    
    // Exception Metrics
    public int TotalExceptions { get; init; }
    public int ResolvedExceptions { get; init; }
    public decimal ExceptionResolutionRate { get; init; }
    public TimeSpan AverageExceptionResolutionTime { get; init; }
    public List<ExceptionTypeStatsDto> ExceptionBreakdown { get; init; } = new();
    
    // Revenue Metrics (if available)
    public decimal? TotalRevenue { get; init; }
    public decimal? AverageRevenuePerTrip { get; init; }
    public decimal? RevenuePerKm { get; init; }
    
    // Driver Performance
    public int ActiveDrivers { get; init; }
    public decimal AverageDriverRating { get; init; }
    public List<DriverPerformanceSummaryDto> TopDrivers { get; init; } = new();
    
    // Vehicle Utilization
    public int ActiveVehicles { get; init; }
    public decimal AverageVehicleUtilization { get; init; }
    public List<VehiclePerformanceSummaryDto> TopVehicles { get; init; } = new();
    
    // Trend Data
    public List<DailyPerformanceDto> DailyTrends { get; init; } = new();
    public List<WeeklyPerformanceDto> WeeklyTrends { get; init; } = new();
    public List<MonthlyPerformanceDto> MonthlyTrends { get; init; } = new();
    
    public DateTime GeneratedAt { get; init; }
}

public record ExceptionTypeStatsDto
{
    public ExceptionType ExceptionType { get; init; }
    public string ExceptionTypeName { get; init; } = string.Empty;
    public int Count { get; init; }
    public int ResolvedCount { get; init; }
    public decimal ResolutionRate { get; init; }
    public TimeSpan AverageResolutionTime { get; init; }
    public decimal PercentageOfTotal { get; init; }
}

public record DriverPerformanceSummaryDto
{
    public Guid DriverId { get; init; }
    public string DriverName { get; init; } = string.Empty;
    public int TripsCompleted { get; init; }
    public decimal OnTimeRate { get; init; }
    public decimal Rating { get; init; }
    public decimal TotalDistanceKm { get; init; }
    public int ExceptionsCount { get; init; }
}

public record VehiclePerformanceSummaryDto
{
    public Guid VehicleId { get; init; }
    public string VehicleRegistration { get; init; } = string.Empty;
    public string VehicleDisplayName { get; init; } = string.Empty;
    public int TripsCompleted { get; init; }
    public decimal UtilizationRate { get; init; }
    public decimal TotalDistanceKm { get; init; }
    public decimal AverageSpeedKmh { get; init; }
    public int MaintenanceCount { get; init; }
}

public record DailyPerformanceDto
{
    public DateTime Date { get; init; }
    public int TripsCompleted { get; init; }
    public decimal OnTimeRate { get; init; }
    public decimal TotalDistanceKm { get; init; }
    public int ExceptionsCount { get; init; }
    public decimal? Revenue { get; init; }
}

public record WeeklyPerformanceDto
{
    public DateTime WeekStartDate { get; init; }
    public DateTime WeekEndDate { get; init; }
    public int TripsCompleted { get; init; }
    public decimal OnTimeRate { get; init; }
    public decimal TotalDistanceKm { get; init; }
    public int ExceptionsCount { get; init; }
    public decimal? Revenue { get; init; }
}

public record MonthlyPerformanceDto
{
    public int Year { get; init; }
    public int Month { get; init; }
    public string MonthName { get; init; } = string.Empty;
    public int TripsCompleted { get; init; }
    public decimal OnTimeRate { get; init; }
    public decimal TotalDistanceKm { get; init; }
    public int ExceptionsCount { get; init; }
    public decimal? Revenue { get; init; }
}

public record TripPerformanceComparisonDto
{
    public Guid CarrierId { get; init; }
    public string CarrierName { get; init; } = string.Empty;
    public CarrierTripPerformanceDto CurrentPeriod { get; init; } = null!;
    public CarrierTripPerformanceDto PreviousPeriod { get; init; } = null!;
    public PerformanceChangeDto Changes { get; init; } = null!;
}

public record PerformanceChangeDto
{
    public decimal TripCompletionRateChange { get; init; }
    public decimal OnTimeDeliveryRateChange { get; init; }
    public decimal AverageDelayChange { get; init; }
    public decimal ExceptionRateChange { get; init; }
    public decimal RevenueChange { get; init; }
    public string TrendDirection { get; init; } = string.Empty; // Improving, Declining, Stable
}

public record CarrierRankingDto
{
    public Guid CarrierId { get; init; }
    public string CarrierName { get; init; } = string.Empty;
    public int Rank { get; init; }
    public decimal Score { get; init; }
    public decimal OnTimeRate { get; init; }
    public decimal CompletionRate { get; init; }
    public decimal ExceptionRate { get; init; }
    public int TotalTrips { get; init; }
}
