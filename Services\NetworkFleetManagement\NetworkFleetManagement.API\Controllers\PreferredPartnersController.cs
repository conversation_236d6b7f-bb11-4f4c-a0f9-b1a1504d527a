using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NetworkFleetManagement.Application.Commands.PreferredPartners;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Application.Queries.PreferredPartners;
using NetworkFleetManagement.Domain.Entities;
using System.Security.Claims;

namespace NetworkFleetManagement.API.Controllers;

[ApiController]
[Route("api/v1/[controller]")]
[Authorize]
public class PreferredPartnersController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<PreferredPartnersController> _logger;

    public PreferredPartnersController(IMediator mediator, ILogger<PreferredPartnersController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get preferred partners for the current user with filtering and pagination
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<PagedResult<PreferredPartnerSummaryDto>>> GetPreferredPartners(
        [FromQuery] List<string>? partnerTypes = null,
        [FromQuery] List<string>? preferenceLevels = null,
        [FromQuery] List<string>? statuses = null,
        [FromQuery] bool? autoAssignEnabled = null,
        [FromQuery] bool? isActive = null,
        [FromQuery] decimal? minRating = null,
        [FromQuery] decimal? maxRating = null,
        [FromQuery] DateTime? createdAfter = null,
        [FromQuery] DateTime? createdBefore = null,
        [FromQuery] DateTime? lastCollaborationAfter = null,
        [FromQuery] DateTime? lastCollaborationBefore = null,
        [FromQuery] string? searchTerm = null,
        [FromQuery] string sortBy = "Priority",
        [FromQuery] string sortDirection = "asc",
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var userId = GetCurrentUserId();
            var query = new GetPreferredPartnersQuery
            {
                UserId = userId,
                PartnerTypes = partnerTypes,
                PreferenceLevels = preferenceLevels,
                Statuses = statuses,
                AutoAssignEnabled = autoAssignEnabled,
                IsActive = isActive,
                MinRating = minRating,
                MaxRating = maxRating,
                CreatedAfter = createdAfter,
                CreatedBefore = createdBefore,
                LastCollaborationAfter = lastCollaborationAfter,
                LastCollaborationBefore = lastCollaborationBefore,
                SearchTerm = searchTerm,
                SortBy = sortBy,
                SortDirection = sortDirection,
                PageNumber = pageNumber,
                PageSize = pageSize
            };

            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving preferred partners for user {UserId}", GetCurrentUserId());
            return StatusCode(500, "An error occurred while retrieving preferred partners");
        }
    }

    /// <summary>
    /// Get preferred partners by role type (Broker gets carriers, Transporter gets brokers, etc.)
    /// </summary>
    [HttpGet("by-role/{roleType}")]
    public async Task<ActionResult<List<PreferredPartnerSummaryDto>>> GetPreferredPartnersByRole(
        string roleType,
        [FromQuery] bool activeOnly = true,
        [FromQuery] int? limit = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var userId = GetCurrentUserId();
            var currentUserRole = GetCurrentUserRole();

            // Validate role-based access
            if (!IsValidRoleTypeForUser(roleType, currentUserRole))
            {
                return Forbid($"User with role '{currentUserRole}' cannot access '{roleType}' partners");
            }

            var partnerType = MapRoleToPartnerType(roleType);
            var query = new GetPreferredPartnersByRoleQuery
            {
                UserId = userId,
                PartnerType = partnerType,
                ActiveOnly = activeOnly,
                Limit = limit
            };

            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving preferred partners by role {RoleType} for user {UserId}",
                roleType, GetCurrentUserId());
            return StatusCode(500, "An error occurred while retrieving preferred partners by role");
        }
    }

    /// <summary>
    /// Get a specific preferred partner by ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<PreferredPartnerDto>> GetPreferredPartner(
        Guid id,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetPreferredPartnerByIdQuery { Id = id };
            var result = await _mediator.Send(query, cancellationToken);

            if (result == null)
            {
                return NotFound($"Preferred partner with ID {id} not found");
            }

            // Verify ownership
            if (result.UserId != GetCurrentUserId())
            {
                return Forbid("You can only access your own preferred partners");
            }

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving preferred partner {Id}", id);
            return StatusCode(500, "An error occurred while retrieving the preferred partner");
        }
    }

    /// <summary>
    /// Create a new preferred partner relationship
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<Guid>> CreatePreferredPartner(
        [FromBody] CreatePreferredPartnerDto dto,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var userId = GetCurrentUserId();
            var userRole = GetCurrentUserRole();

            // Validate that the user can create this type of partner relationship
            if (!CanCreatePartnerType(dto.PartnerType, userRole))
            {
                return Forbid($"User with role '{userRole}' cannot create '{dto.PartnerType}' partner relationships");
            }

            var command = new CreatePreferredPartnerCommand
            {
                UserId = userId,
                PartnerId = dto.PartnerId,
                PartnerType = Enum.Parse<PreferredPartnerType>(dto.PartnerType),
                PreferenceLevel = Enum.Parse<PreferenceLevel>(dto.PreferenceLevel),
                Priority = dto.Priority,
                PreferredCommissionRate = dto.PreferredCommissionRate,
                PreferredServiceRate = dto.PreferredServiceRate,
                AutoAssignEnabled = dto.AutoAssignEnabled,
                Notes = dto.Notes,
                PreferredRoutes = dto.PreferredRoutes,
                PreferredLoadTypes = dto.PreferredLoadTypes,
                ExcludedRoutes = dto.ExcludedRoutes,
                ExcludedLoadTypes = dto.ExcludedLoadTypes
            };

            var result = await _mediator.Send(command, cancellationToken);
            return CreatedAtAction(nameof(GetPreferredPartner), new { id = result }, result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating preferred partner for user {UserId}", GetCurrentUserId());
            return StatusCode(500, "An error occurred while creating the preferred partner");
        }
    }

    /// <summary>
    /// Update an existing preferred partner relationship
    /// </summary>
    [HttpPut("{id}")]
    public async Task<ActionResult> UpdatePreferredPartner(
        Guid id,
        [FromBody] UpdatePreferredPartnerDto dto,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var userId = GetCurrentUserId();
            var command = new UpdatePreferredPartnerCommand
            {
                Id = id,
                UserId = userId,
                PreferenceLevel = Enum.Parse<PreferenceLevel>(dto.PreferenceLevel),
                Priority = dto.Priority,
                PreferredCommissionRate = dto.PreferredCommissionRate,
                PreferredServiceRate = dto.PreferredServiceRate,
                AutoAssignEnabled = dto.AutoAssignEnabled,
                AutoAssignThreshold = dto.AutoAssignThreshold,
                Notes = dto.Notes,
                PreferredRoutes = dto.PreferredRoutes,
                PreferredLoadTypes = dto.PreferredLoadTypes,
                ExcludedRoutes = dto.ExcludedRoutes,
                ExcludedLoadTypes = dto.ExcludedLoadTypes
            };

            var result = await _mediator.Send(command, cancellationToken);

            if (!result)
            {
                return NotFound($"Preferred partner with ID {id} not found or you don't have permission to update it");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating preferred partner {Id}", id);
            return StatusCode(500, "An error occurred while updating the preferred partner");
        }
    }

    /// <summary>
    /// Delete a preferred partner relationship
    /// </summary>
    [HttpDelete("{id}")]
    public async Task<ActionResult> DeletePreferredPartner(
        Guid id,
        [FromBody] DeletePreferredPartnerRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var userId = GetCurrentUserId();
            var command = new DeletePreferredPartnerCommand
            {
                Id = id,
                UserId = userId,
                Reason = request.Reason
            };

            var result = await _mediator.Send(command, cancellationToken);

            if (!result)
            {
                return NotFound($"Preferred partner with ID {id} not found or you don't have permission to delete it");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting preferred partner {Id}", id);
            return StatusCode(500, "An error occurred while deleting the preferred partner");
        }
    }

    /// <summary>
    /// Activate a preferred partner relationship
    /// </summary>
    [HttpPost("{id}/activate")]
    public async Task<ActionResult> ActivatePreferredPartner(
        Guid id,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var userId = GetCurrentUserId();
            var command = new ActivatePreferredPartnerCommand
            {
                Id = id,
                UserId = userId
            };

            var result = await _mediator.Send(command, cancellationToken);

            if (!result)
            {
                return NotFound($"Preferred partner with ID {id} not found or you don't have permission to activate it");
            }

            return Ok(new { Message = "Preferred partner activated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error activating preferred partner {Id}", id);
            return StatusCode(500, "An error occurred while activating the preferred partner");
        }
    }

    /// <summary>
    /// Deactivate a preferred partner relationship
    /// </summary>
    [HttpPost("{id}/deactivate")]
    public async Task<ActionResult> DeactivatePreferredPartner(
        Guid id,
        [FromBody] DeactivatePreferredPartnerRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var userId = GetCurrentUserId();
            var command = new DeactivatePreferredPartnerCommand
            {
                Id = id,
                UserId = userId,
                Reason = request.Reason
            };

            var result = await _mediator.Send(command, cancellationToken);

            if (!result)
            {
                return NotFound($"Preferred partner with ID {id} not found or you don't have permission to deactivate it");
            }

            return Ok(new { Message = "Preferred partner deactivated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deactivating preferred partner {Id}", id);
            return StatusCode(500, "An error occurred while deactivating the preferred partner");
        }
    }

    /// <summary>
    /// Get preferred carriers for brokers
    /// </summary>
    [HttpGet("carriers")]
    [Authorize(Roles = "Broker,BrokerAdmin")]
    public async Task<ActionResult<List<PreferredPartnerSummaryDto>>> GetPreferredCarriers(
        [FromQuery] bool activeOnly = true,
        [FromQuery] string? route = null,
        [FromQuery] string? loadType = null,
        [FromQuery] int? limit = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var userId = GetCurrentUserId();
            var query = new GetPreferredCarriersQuery
            {
                BrokerId = userId,
                ActiveOnly = activeOnly,
                Route = route,
                LoadType = loadType,
                Limit = limit
            };

            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving preferred carriers for broker {BrokerId}", GetCurrentUserId());
            return StatusCode(500, "An error occurred while retrieving preferred carriers");
        }
    }

    /// <summary>
    /// Get preferred brokers for transporters
    /// </summary>
    [HttpGet("brokers")]
    [Authorize(Roles = "Transporter,TransporterAdmin,Carrier,CarrierAdmin")]
    public async Task<ActionResult<List<PreferredPartnerSummaryDto>>> GetPreferredBrokers(
        [FromQuery] bool activeOnly = true,
        [FromQuery] string? route = null,
        [FromQuery] string? loadType = null,
        [FromQuery] int? limit = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var userId = GetCurrentUserId();
            var query = new GetPreferredBrokersQuery
            {
                TransporterId = userId,
                ActiveOnly = activeOnly,
                Route = route,
                LoadType = loadType,
                Limit = limit
            };

            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving preferred brokers for transporter {TransporterId}", GetCurrentUserId());
            return StatusCode(500, "An error occurred while retrieving preferred brokers");
        }
    }

    /// <summary>
    /// Get preferred transporters for shippers
    /// </summary>
    [HttpGet("transporters")]
    [Authorize(Roles = "Shipper,ShipperAdmin")]
    public async Task<ActionResult<List<PreferredPartnerSummaryDto>>> GetPreferredTransporters(
        [FromQuery] bool activeOnly = true,
        [FromQuery] string? route = null,
        [FromQuery] string? loadType = null,
        [FromQuery] int? limit = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var userId = GetCurrentUserId();
            var query = new GetPreferredTransportersQuery
            {
                ShipperId = userId,
                ActiveOnly = activeOnly,
                Route = route,
                LoadType = loadType,
                Limit = limit
            };

            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving preferred transporters for shipper {ShipperId}", GetCurrentUserId());
            return StatusCode(500, "An error occurred while retrieving preferred transporters");
        }
    }

    /// <summary>
    /// Get auto-assignment eligible partners for a specific route and load type
    /// </summary>
    [HttpGet("auto-assign-eligible")]
    public async Task<ActionResult<List<PreferredPartnerSummaryDto>>> GetAutoAssignEligiblePartners(
        [FromQuery] string partnerType,
        [FromQuery] string? route = null,
        [FromQuery] string? loadType = null,
        [FromQuery] int? limit = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var userId = GetCurrentUserId();
            var userRole = GetCurrentUserRole();

            if (!IsValidRoleTypeForUser(partnerType, userRole))
            {
                return Forbid($"User with role '{userRole}' cannot access '{partnerType}' partners");
            }

            var query = new GetAutoAssignEligiblePartnersQuery
            {
                UserId = userId,
                PartnerType = MapRoleToPartnerType(partnerType),
                Route = route,
                LoadType = loadType,
                Limit = limit
            };

            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving auto-assign eligible partners for user {UserId}", GetCurrentUserId());
            return StatusCode(500, "An error occurred while retrieving auto-assign eligible partners");
        }
    }

    /// <summary>
    /// Get partner performance analytics
    /// </summary>
    [HttpGet("{id}/analytics")]
    public async Task<ActionResult<PartnerAnalyticsDto>> GetPartnerAnalytics(
        Guid id,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var userId = GetCurrentUserId();
            var query = new GetPartnerAnalyticsQuery
            {
                PreferredPartnerId = id,
                UserId = userId,
                FromDate = fromDate ?? DateTime.UtcNow.AddMonths(-6),
                ToDate = toDate ?? DateTime.UtcNow
            };

            var result = await _mediator.Send(query, cancellationToken);

            if (result == null)
            {
                return NotFound($"Analytics not found for preferred partner {id}");
            }

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving analytics for preferred partner {Id}", id);
            return StatusCode(500, "An error occurred while retrieving partner analytics");
        }
    }

    /// <summary>
    /// Get dashboard summary for preferred partners
    /// </summary>
    [HttpGet("dashboard")]
    public async Task<ActionResult<PreferredPartnerDashboardDto>> GetDashboard(
        CancellationToken cancellationToken = default)
    {
        try
        {
            var userId = GetCurrentUserId();
            var query = new GetPreferredPartnerDashboardQuery
            {
                UserId = userId
            };

            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving preferred partner dashboard for user {UserId}", GetCurrentUserId());
            return StatusCode(500, "An error occurred while retrieving dashboard data");
        }
    }

    /// <summary>
    /// Get partner collaboration history
    /// </summary>
    [HttpGet("{id}/collaboration-history")]
    public async Task<ActionResult<List<PartnerCollaborationSummaryDto>>> GetCollaborationHistory(
        Guid id,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] int? limit = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var userId = GetCurrentUserId();
            var query = new GetPartnerCollaborationHistoryQuery
            {
                PreferredPartnerId = id,
                UserId = userId,
                FromDate = fromDate ?? DateTime.UtcNow.AddMonths(-12),
                ToDate = toDate ?? DateTime.UtcNow,
                Limit = limit
            };

            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving collaboration history for preferred partner {Id}", id);
            return StatusCode(500, "An error occurred while retrieving collaboration history");
        }
    }

    /// <summary>
    /// Get partner performance trends
    /// </summary>
    [HttpGet("{id}/performance-trends")]
    public async Task<ActionResult<List<PartnerPerformanceTrendDto>>> GetPerformanceTrends(
        Guid id,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] string period = "monthly", // daily, weekly, monthly
        CancellationToken cancellationToken = default)
    {
        try
        {
            var userId = GetCurrentUserId();
            var query = new GetPartnerPerformanceTrendsQuery
            {
                PreferredPartnerId = id,
                UserId = userId,
                FromDate = fromDate ?? DateTime.UtcNow.AddMonths(-12),
                ToDate = toDate ?? DateTime.UtcNow,
                Period = period
            };

            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving performance trends for preferred partner {Id}", id);
            return StatusCode(500, "An error occurred while retrieving performance trends");
        }
    }

    /// <summary>
    /// Get partner recommendations based on performance and preferences
    /// </summary>
    [HttpGet("recommendations")]
    public async Task<ActionResult<List<PartnerRecommendationDto>>> GetPartnerRecommendations(
        [FromQuery] string? partnerType = null,
        [FromQuery] string? route = null,
        [FromQuery] string? loadType = null,
        [FromQuery] int limit = 10,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var userId = GetCurrentUserId();
            var userRole = GetCurrentUserRole();

            var query = new GetPartnerRecommendationsQuery
            {
                UserId = userId,
                UserRole = userRole,
                PartnerType = partnerType,
                Route = route,
                LoadType = loadType,
                Limit = limit
            };

            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving partner recommendations for user {UserId}", GetCurrentUserId());
            return StatusCode(500, "An error occurred while retrieving partner recommendations");
        }
    }

    /// <summary>
    /// Update auto-assignment settings for a preferred partner
    /// </summary>
    [HttpPut("{id}/auto-assignment")]
    public async Task<ActionResult> UpdateAutoAssignmentSettings(
        Guid id,
        [FromBody] UpdateAutoAssignmentSettingsDto dto,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var userId = GetCurrentUserId();
            var command = new UpdateAutoAssignmentSettingsCommand
            {
                PreferredPartnerId = id,
                UserId = userId,
                AutoAssignEnabled = dto.AutoAssignEnabled,
                AutoAssignThreshold = dto.AutoAssignThreshold,
                PreferredRoutes = dto.PreferredRoutes,
                PreferredLoadTypes = dto.PreferredLoadTypes,
                ExcludedRoutes = dto.ExcludedRoutes,
                ExcludedLoadTypes = dto.ExcludedLoadTypes
            };

            var result = await _mediator.Send(command, cancellationToken);

            if (!result)
            {
                return NotFound($"Preferred partner with ID {id} not found or you don't have permission to update it");
            }

            return Ok(new { Message = "Auto-assignment settings updated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating auto-assignment settings for preferred partner {Id}", id);
            return StatusCode(500, "An error occurred while updating auto-assignment settings");
        }
    }

    /// <summary>
    /// Get auto-assignment settings for a preferred partner
    /// </summary>
    [HttpGet("{id}/auto-assignment")]
    public async Task<ActionResult<AutoAssignmentSettingsDto>> GetAutoAssignmentSettings(
        Guid id,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var userId = GetCurrentUserId();
            var query = new GetAutoAssignmentSettingsQuery
            {
                PreferredPartnerId = id,
                UserId = userId
            };

            var result = await _mediator.Send(query, cancellationToken);

            if (result == null)
            {
                return NotFound($"Auto-assignment settings not found for preferred partner {id}");
            }

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving auto-assignment settings for preferred partner {Id}", id);
            return StatusCode(500, "An error occurred while retrieving auto-assignment settings");
        }
    }

    /// <summary>
    /// Test auto-assignment rules for a specific scenario
    /// </summary>
    [HttpPost("test-auto-assignment")]
    public async Task<ActionResult<List<PreferredPartnerSummaryDto>>> TestAutoAssignment(
        [FromBody] TestAutoAssignmentRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var userId = GetCurrentUserId();
            var userRole = GetCurrentUserRole();

            if (!IsValidRoleTypeForUser(request.PartnerType, userRole))
            {
                return Forbid($"User with role '{userRole}' cannot test auto-assignment for '{request.PartnerType}' partners");
            }

            var query = new TestAutoAssignmentQuery
            {
                UserId = userId,
                PartnerType = MapRoleToPartnerType(request.PartnerType),
                Route = request.Route,
                LoadType = request.LoadType,
                MinRating = request.MinRating,
                MaxResults = request.MaxResults
            };

            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error testing auto-assignment for user {UserId}", GetCurrentUserId());
            return StatusCode(500, "An error occurred while testing auto-assignment");
        }
    }

    /// <summary>
    /// Get auto-assignment statistics and performance
    /// </summary>
    [HttpGet("auto-assignment/statistics")]
    public async Task<ActionResult<AutoAssignmentStatisticsDto>> GetAutoAssignmentStatistics(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var userId = GetCurrentUserId();
            var query = new GetAutoAssignmentStatisticsQuery
            {
                UserId = userId,
                FromDate = fromDate ?? DateTime.UtcNow.AddMonths(-3),
                ToDate = toDate ?? DateTime.UtcNow
            };

            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving auto-assignment statistics for user {UserId}", GetCurrentUserId());
            return StatusCode(500, "An error occurred while retrieving auto-assignment statistics");
        }
    }

    // ===== ENHANCED SHIPPER PORTAL FEATURES =====

    /// <summary>
    /// Create preferred partner wishlist with advanced settings
    /// </summary>
    [HttpPost("wishlist")]
    [Authorize]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult> CreatePreferredPartnerWishlist(
        [FromBody] CreateWishlistRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var userId = GetCurrentUserId();
            var command = new NetworkFleetManagement.Application.Commands.CreatePreferredPartnerWishlist.CreatePreferredPartnerWishlistCommand
            {
                UserId = userId,
                WishlistName = request.WishlistName,
                Description = request.Description,
                PartnerType = request.PartnerType,
                Partners = request.Partners,
                Settings = request.Settings,
                RequestingUserId = userId
            };

            var wishlistId = await _mediator.Send(command, cancellationToken);
            return CreatedAtAction(nameof(GetWishlistDetails), new { id = wishlistId },
                new { wishlistId, message = "Preferred partner wishlist created successfully" });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (UnauthorizedAccessException)
        {
            return Forbid("Access denied to create wishlist");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating preferred partner wishlist for user {UserId}", GetCurrentUserId());
            return StatusCode(500, "An error occurred while creating the wishlist");
        }
    }

    /// <summary>
    /// Generate comprehensive partner performance analytics
    /// </summary>
    [HttpPost("analytics/generate")]
    [Authorize]
    [ProducesResponseType(typeof(PartnerPerformanceAnalyticsDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<PartnerPerformanceAnalyticsDto>> GeneratePartnerPerformanceAnalytics(
        [FromBody] GenerateAnalyticsRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var userId = GetCurrentUserId();
            var command = new NetworkFleetManagement.Application.Commands.GeneratePartnerPerformanceAnalytics.GeneratePartnerPerformanceAnalyticsCommand
            {
                UserId = userId,
                PartnerType = request.PartnerType,
                SpecificPartnerIds = request.SpecificPartnerIds,
                FromDate = request.FromDate,
                ToDate = request.ToDate,
                Scope = request.Scope,
                Metrics = request.Metrics,
                IncludePredictiveInsights = request.IncludePredictiveInsights,
                IncludeRecommendations = request.IncludeRecommendations,
                IncludeBenchmarking = request.IncludeBenchmarking,
                RequestingUserId = userId
            };

            var result = await _mediator.Send(command, cancellationToken);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (UnauthorizedAccessException)
        {
            return Forbid("Access denied to generate analytics");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating partner performance analytics for user {UserId}", GetCurrentUserId());
            return StatusCode(500, "An error occurred while generating analytics");
        }
    }

    /// <summary>
    /// Get intelligent partner recommendations with advanced filtering
    /// </summary>
    [HttpGet("recommendations/enhanced")]
    [Authorize]
    [ProducesResponseType(typeof(PartnerRecommendationsDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<PartnerRecommendationsDto>> GetEnhancedPartnerRecommendations(
        [FromQuery] GetRecommendationsRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var userId = GetCurrentUserId();
            var query = new NetworkFleetManagement.Application.Queries.GetPartnerRecommendations.GetPartnerRecommendationsQuery
            {
                UserId = userId,
                PartnerType = request.PartnerType,
                ServiceAreas = request.ServiceAreas ?? new List<string>(),
                LoadTypes = request.LoadTypes ?? new List<string>(),
                BudgetRange = request.BudgetRange,
                RequiredDate = request.RequiredDate,
                Criteria = request.Criteria,
                MaxRecommendations = request.MaxRecommendations,
                IncludeNewPartners = request.IncludeNewPartners,
                IncludePerformanceAnalysis = request.IncludePerformanceAnalysis,
                IncludeAvailabilityCheck = request.IncludeAvailabilityCheck,
                RequestingUserId = userId
            };

            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (UnauthorizedAccessException)
        {
            return Forbid("Access denied to get recommendations");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting enhanced partner recommendations for user {UserId}", GetCurrentUserId());
            return StatusCode(500, "An error occurred while getting recommendations");
        }
    }

    /// <summary>
    /// Get wishlist details (placeholder for future implementation)
    /// </summary>
    [HttpGet("wishlist/{id}")]
    [Authorize]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult> GetWishlistDetails(Guid id)
    {
        // This would be implemented with a proper query handler
        return Ok(new { id, message = "Wishlist details endpoint - to be implemented" });
    }

    // Helper methods and request classes
    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return Guid.TryParse(userIdClaim, out var userId) ? userId : Guid.Empty;
    }

    private string GetCurrentUserRole()
    {
        return User.FindFirst(ClaimTypes.Role)?.Value ?? string.Empty;
    }

    private bool IsValidRoleTypeForUser(string roleType, string userRole)
    {
        return roleType.ToLower() switch
        {
            "carrier" => userRole.Contains("Broker"),
            "broker" => userRole.Contains("Transporter") || userRole.Contains("Carrier"),
            "transporter" => userRole.Contains("Shipper"),
            _ => false
        };
    }

    private PreferredPartnerType MapRoleToPartnerType(string roleType)
    {
        return roleType.ToLower() switch
        {
            "carrier" => PreferredPartnerType.Carrier,
            "broker" => PreferredPartnerType.Broker,
            "transporter" => PreferredPartnerType.Transporter,
            "shipper" => PreferredPartnerType.Shipper,
            _ => throw new ArgumentException($"Invalid role type: {roleType}")
        };
    }

    private bool CanCreatePartnerType(string partnerType, string userRole)
    {
        return partnerType.ToLower() switch
        {
            "carrier" => userRole.Contains("Broker"),
            "broker" => userRole.Contains("Transporter") || userRole.Contains("Carrier"),
            "transporter" => userRole.Contains("Shipper"),
            "shipper" => userRole.Contains("Transporter"),
            _ => false
        };
    }
}

// Request DTOs for the controller
public class DeletePreferredPartnerRequest
{
    public string Reason { get; set; } = string.Empty;
}

public class DeactivatePreferredPartnerRequest
{
    public string Reason { get; set; } = string.Empty;
}

// ===== ENHANCED SHIPPER PORTAL REQUEST DTOs =====

public class CreateWishlistRequest
{
    public string WishlistName { get; set; } = string.Empty;
    public string? Description { get; set; }
    public PreferredPartnerType PartnerType { get; set; }
    public List<WishlistPartnerRequest> Partners { get; set; } = new();
    public WishlistSettings Settings { get; set; } = new();
}

public class WishlistPartnerRequest
{
    public Guid PartnerId { get; set; }
    public PreferenceLevel PreferenceLevel { get; set; } = PreferenceLevel.Secondary;
    public int Priority { get; set; } = 10;
    public decimal? PreferredCommissionRate { get; set; }
    public decimal? PreferredServiceRate { get; set; }
    public bool AutoAssignEnabled { get; set; } = false;
    public decimal? AutoAssignThreshold { get; set; }
    public List<string> PreferredRoutes { get; set; } = new();
    public List<string> PreferredLoadTypes { get; set; } = new();
    public List<string> ExcludedRoutes { get; set; } = new();
    public List<string> ExcludedLoadTypes { get; set; } = new();
    public string? Notes { get; set; }
    public bool NotifyOnNewOpportunities { get; set; } = true;
    public bool NotifyOnPerformanceChanges { get; set; } = true;
}

public class WishlistSettings
{
    public bool AutoAddHighPerformers { get; set; } = false;
    public decimal? AutoAddThreshold { get; set; }
    public bool AutoRemovePoorPerformers { get; set; } = false;
    public decimal? AutoRemoveThreshold { get; set; }
    public int? MaxPartners { get; set; }
    public bool EnablePerformanceAlerts { get; set; } = true;
    public bool EnableContractExpiryAlerts { get; set; } = true;
    public int? AlertDaysBefore { get; set; } = 30;
}

public class GenerateAnalyticsRequest
{
    public PreferredPartnerType? PartnerType { get; set; }
    public List<Guid>? SpecificPartnerIds { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public AnalyticsScope Scope { get; set; } = AnalyticsScope.All;
    public List<PerformanceMetric> Metrics { get; set; } = new();
    public bool IncludePredictiveInsights { get; set; } = true;
    public bool IncludeRecommendations { get; set; } = true;
    public bool IncludeBenchmarking { get; set; } = true;
}

public class GetRecommendationsRequest
{
    public PreferredPartnerType? PartnerType { get; set; }
    public List<string>? ServiceAreas { get; set; }
    public List<string>? LoadTypes { get; set; }
    public decimal? BudgetRange { get; set; }
    public DateTime? RequiredDate { get; set; }
    public RecommendationCriteria Criteria { get; set; } = RecommendationCriteria.Balanced;
    public int MaxRecommendations { get; set; } = 10;
    public bool IncludeNewPartners { get; set; } = true;
    public bool IncludePerformanceAnalysis { get; set; } = true;
    public bool IncludeAvailabilityCheck { get; set; } = true;
}
