using AnalyticsBIService.Domain.Enums;
using Shared.Domain.Common;
using System;

namespace AnalyticsBIService.Domain.Entities;

/// <summary>
/// Custom report template entity for user-defined report structures
/// </summary>
public class CustomReportTemplate : BaseEntity
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public Guid CreatedBy { get; private set; }
    public UserType UserType { get; private set; }
    public string Configuration { get; private set; }
    public bool IsPublic { get; private set; }
    public bool IsActive { get; private set; }
    public int ExecutionCount { get; private set; }
    public DateTime? LastExecuted { get; private set; }

    private CustomReportTemplate()
    {
        Name = string.Empty;
        Description = string.Empty;
        Configuration = string.Empty;
    }

    public CustomReportTemplate(
        string name,
        string description,
        Guid createdBy,
        UserType userType,
        string configuration,
        bool isPublic = false)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        CreatedBy = createdBy;
        UserType = userType;
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        IsPublic = isPublic;
        IsActive = true;
        ExecutionCount = 0;
    }

    public void UpdateName(string name)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        SetUpdatedAt();
    }

    public void UpdateDescription(string description)
    {
        Description = description ?? throw new ArgumentNullException(nameof(description));
        SetUpdatedAt();
    }

    public void UpdateConfiguration(string configuration)
    {
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        SetUpdatedAt();
    }

    public void MakePublic()
    {
        IsPublic = true;
        SetUpdatedAt();
    }

    public void MakePrivate()
    {
        IsPublic = false;
        SetUpdatedAt();
    }

    public void RecordExecution()
    {
        ExecutionCount++;
        LastExecuted = DateTime.UtcNow;
        SetUpdatedAt();
    }

    public void Activate()
    {
        IsActive = true;
        SetUpdatedAt();
    }

    public void Deactivate()
    {
        IsActive = false;
        SetUpdatedAt();
    }
}
