using Microsoft.EntityFrameworkCore;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;
using FinancialPayment.Application.Interfaces.Repositories;
using FinancialPayment.Infrastructure.Data;

namespace FinancialPayment.Infrastructure.Repositories;

public class GstConfigurationRepository : IGstConfigurationRepository
{
    private readonly FinancialPaymentDbContext _context;

    public GstConfigurationRepository(FinancialPaymentDbContext context)
    {
        _context = context;
    }

    public async Task<GstConfiguration?> GetByIdAsync(Guid id)
    {
        return await _context.GstConfigurations
            .Include(g => g.History)
            .FirstOrDefaultAsync(g => g.Id == id);
    }

    public async Task<List<GstConfiguration>> GetAllAsync()
    {
        return await _context.GstConfigurations
            .Include(g => g.History)
            .OrderByDescending(g => g.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<GstConfiguration>> GetActiveAsync()
    {
        return await _context.GstConfigurations
            .Include(g => g.History)
            .Where(g => g.Status == TaxConfigurationStatus.Active)
            .OrderByDescending(g => g.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<GstConfiguration>> GetByStatusAsync(TaxConfigurationStatus status)
    {
        return await _context.GstConfigurations
            .Include(g => g.History)
            .Where(g => g.Status == status)
            .OrderByDescending(g => g.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<GstConfiguration>> GetByServiceCategoryAsync(ServiceCategory serviceCategory)
    {
        return await _context.GstConfigurations
            .Include(g => g.History)
            .Where(g => g.ServiceCategory == serviceCategory)
            .OrderByDescending(g => g.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<GstConfiguration>> GetByJurisdictionAsync(TaxJurisdiction jurisdiction)
    {
        return await _context.GstConfigurations
            .Include(g => g.History)
            .Where(g => g.Jurisdiction.Country == jurisdiction.Country &&
                       g.Jurisdiction.State == jurisdiction.State &&
                       g.Jurisdiction.City == jurisdiction.City)
            .OrderByDescending(g => g.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<GstConfiguration>> GetByGstRateAsync(GstRate gstRate)
    {
        return await _context.GstConfigurations
            .Include(g => g.History)
            .Where(g => g.GstRate == gstRate)
            .OrderByDescending(g => g.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<GstConfiguration>> GetEffectiveOnDateAsync(DateTime date)
    {
        return await _context.GstConfigurations
            .Include(g => g.History)
            .Where(g => g.Status == TaxConfigurationStatus.Active &&
                       g.TaxRate.EffectiveFrom <= date &&
                       (g.TaxRate.EffectiveTo == null || g.TaxRate.EffectiveTo >= date))
            .OrderByDescending(g => g.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<GstConfiguration>> GetByServiceCategoryAndJurisdictionAsync(ServiceCategory serviceCategory, TaxJurisdiction jurisdiction)
    {
        return await _context.GstConfigurations
            .Include(g => g.History)
            .Where(g => g.ServiceCategory == serviceCategory &&
                       g.Jurisdiction.Country == jurisdiction.Country &&
                       g.Jurisdiction.State == jurisdiction.State &&
                       g.Jurisdiction.City == jurisdiction.City)
            .OrderByDescending(g => g.CreatedAt)
            .ToListAsync();
    }

    public async Task<GstConfiguration?> GetApplicableConfigurationAsync(ServiceCategory serviceCategory, TaxJurisdiction jurisdiction, DateTime date)
    {
        return await _context.GstConfigurations
            .Include(g => g.History)
            .Where(g => g.Status == TaxConfigurationStatus.Active &&
                       g.ServiceCategory == serviceCategory &&
                       g.Jurisdiction.Country == jurisdiction.Country &&
                       g.Jurisdiction.State == jurisdiction.State &&
                       g.Jurisdiction.City == jurisdiction.City &&
                       g.TaxRate.EffectiveFrom <= date &&
                       (g.TaxRate.EffectiveTo == null || g.TaxRate.EffectiveTo >= date))
            .OrderByDescending(g => g.CreatedAt)
            .FirstOrDefaultAsync();
    }

    public async Task<GstConfiguration?> GetDefaultConfigurationAsync(TaxJurisdiction jurisdiction)
    {
        return await _context.GstConfigurations
            .Include(g => g.History)
            .Where(g => g.Status == TaxConfigurationStatus.Active &&
                       g.Jurisdiction.Country == jurisdiction.Country &&
                       g.Jurisdiction.State == jurisdiction.State &&
                       g.Jurisdiction.City == jurisdiction.City)
            .OrderBy(g => g.ServiceCategory)
            .FirstOrDefaultAsync();
    }

    public async Task<List<GstConfiguration>> GetByHsnCodeAsync(string hsnCode)
    {
        return await _context.GstConfigurations
            .Include(g => g.History)
            .Where(g => g.HsnCode == hsnCode)
            .OrderByDescending(g => g.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<GstConfiguration>> GetWithReverseChargeAsync()
    {
        return await _context.GstConfigurations
            .Include(g => g.History)
            .Where(g => g.IsReverseChargeApplicable)
            .OrderByDescending(g => g.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<GstConfiguration>> GetByCreatedByAsync(string createdBy)
    {
        return await _context.GstConfigurations
            .Include(g => g.History)
            .Where(g => g.CreatedBy == createdBy)
            .OrderByDescending(g => g.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<GstConfiguration>> GetByDateRangeAsync(DateTime from, DateTime to)
    {
        return await _context.GstConfigurations
            .Include(g => g.History)
            .Where(g => g.CreatedAt >= from && g.CreatedAt <= to)
            .OrderByDescending(g => g.CreatedAt)
            .ToListAsync();
    }

    public async Task<GstConfiguration> AddAsync(GstConfiguration gstConfiguration)
    {
        _context.GstConfigurations.Add(gstConfiguration);
        return gstConfiguration;
    }

    public async Task UpdateAsync(GstConfiguration gstConfiguration)
    {
        _context.GstConfigurations.Update(gstConfiguration);
        await Task.CompletedTask;
    }

    public async Task DeleteAsync(Guid id)
    {
        var gstConfiguration = await _context.GstConfigurations.FindAsync(id);
        if (gstConfiguration != null)
        {
            _context.GstConfigurations.Remove(gstConfiguration);
        }
    }

    public async Task<bool> ExistsAsync(Guid id)
    {
        return await _context.GstConfigurations.AnyAsync(g => g.Id == id);
    }

    public async Task<bool> ExistsByNameAsync(string name, Guid? excludeId = null)
    {
        var query = _context.GstConfigurations.Where(g => g.Name == name);
        if (excludeId.HasValue)
            query = query.Where(g => g.Id != excludeId.Value);
        
        return await query.AnyAsync();
    }

    public async Task<int> GetCountByStatusAsync(TaxConfigurationStatus status)
    {
        return await _context.GstConfigurations.CountAsync(g => g.Status == status);
    }

    public async Task<List<GstConfiguration>> SearchAsync(string searchTerm, int skip = 0, int take = 50)
    {
        return await _context.GstConfigurations
            .Include(g => g.History)
            .Where(g => g.Name.Contains(searchTerm) || 
                       g.Description.Contains(searchTerm) ||
                       (g.HsnCode != null && g.HsnCode.Contains(searchTerm)))
            .OrderByDescending(g => g.CreatedAt)
            .Skip(skip)
            .Take(take)
            .ToListAsync();
    }

    public async Task<List<GstConfigurationHistory>> GetHistoryAsync(Guid gstConfigurationId)
    {
        return await _context.Set<GstConfigurationHistory>()
            .Where(h => h.GstConfigurationId == gstConfigurationId)
            .OrderByDescending(h => h.ModifiedAt)
            .ToListAsync();
    }
}
