using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TripManagement.Domain.Entities;

namespace TripManagement.Infrastructure.Configurations;

public class DriverConfiguration : IEntityTypeConfiguration<Driver>
{
    public void Configure(EntityTypeBuilder<Driver> builder)
    {
        builder.ToTable("drivers");

        builder.HasKey(d => d.Id);

        builder.Property(d => d.Id)
            .ValueGeneratedNever();

        builder.Property(d => d.UserId)
            .IsRequired();

        builder.Property(d => d.FirstName)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(d => d.LastName)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(d => d.PhoneNumber)
            .IsRequired()
            .HasMaxLength(20);

        builder.Property(d => d.Email)
            .IsRequired()
            .HasMaxLength(255);

        builder.Property(d => d.LicenseNumber)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(d => d.LicenseExpiryDate)
            .IsRequired();

        builder.Property(d => d.Status)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(d => d.CreatedAt)
            .IsRequired();

        builder.Property(d => d.LastActiveAt);

        builder.Property(d => d.ProfilePhotoUrl)
            .HasMaxLength(500);

        builder.Property(d => d.Rating)
            .HasPrecision(3, 2);

        builder.Property(d => d.TotalTrips)
            .IsRequired();

        builder.Property(d => d.CompletedTrips)
            .IsRequired();

        builder.Property(d => d.Notes)
            .HasMaxLength(1000);

        // Configure CurrentLocation as owned entity
        builder.OwnsOne(d => d.CurrentLocation, location =>
        {
            location.Property(l => l.Latitude)
                .HasPrecision(10, 7);

            location.Property(l => l.Longitude)
                .HasPrecision(10, 7);

            location.Property(l => l.Altitude)
                .HasPrecision(10, 2);

            location.Property(l => l.Accuracy)
                .HasPrecision(10, 2);

            location.Property(l => l.Timestamp);

            location.Property(l => l.Address)
                .HasMaxLength(500);

            location.Property(l => l.City)
                .HasMaxLength(100);

            location.Property(l => l.State)
                .HasMaxLength(100);

            location.Property(l => l.Country)
                .HasMaxLength(100);

            location.Property(l => l.PostalCode)
                .HasMaxLength(20);
        });

        // Configure relationships
        builder.HasMany(d => d.Documents)
            .WithOne(doc => doc.Driver)
            .HasForeignKey(doc => doc.DriverId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(d => d.UserId)
            .IsUnique();

        builder.HasIndex(d => d.LicenseNumber)
            .IsUnique();

        builder.HasIndex(d => d.Email)
            .IsUnique();

        builder.HasIndex(d => d.Status);

        builder.HasIndex(d => d.CreatedAt);
    }
}

public class DriverDocumentConfiguration : IEntityTypeConfiguration<DriverDocument>
{
    public void Configure(EntityTypeBuilder<DriverDocument> builder)
    {
        builder.ToTable("driver_documents");

        builder.HasKey(dd => dd.Id);

        builder.Property(dd => dd.Id)
            .ValueGeneratedNever();

        builder.Property(dd => dd.DriverId)
            .IsRequired();

        builder.Property(dd => dd.DocumentType)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(dd => dd.FileName)
            .IsRequired()
            .HasMaxLength(255);

        builder.Property(dd => dd.FileUrl)
            .IsRequired()
            .HasMaxLength(500);

        builder.Property(dd => dd.Description)
            .HasMaxLength(500);

        builder.Property(dd => dd.UploadedAt)
            .IsRequired();

        builder.Property(dd => dd.ExpiryDate);

        builder.Property(dd => dd.IsVerified)
            .IsRequired();

        builder.Property(dd => dd.VerifiedAt);

        builder.Property(dd => dd.VerifiedBy)
            .HasMaxLength(100);

        // Indexes
        builder.HasIndex(dd => dd.DriverId);

        builder.HasIndex(dd => dd.DocumentType);

        builder.HasIndex(dd => dd.UploadedAt);
    }
}

public class VehicleConfiguration : IEntityTypeConfiguration<Vehicle>
{
    public void Configure(EntityTypeBuilder<Vehicle> builder)
    {
        builder.ToTable("vehicles");

        builder.HasKey(v => v.Id);

        builder.Property(v => v.Id)
            .ValueGeneratedNever();

        builder.Property(v => v.CarrierId)
            .IsRequired();

        builder.Property(v => v.RegistrationNumber)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(v => v.VehicleType)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(v => v.Make)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(v => v.Model)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(v => v.Year)
            .IsRequired();

        builder.Property(v => v.Color)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(v => v.LoadCapacityKg)
            .IsRequired()
            .HasPrecision(10, 2);

        builder.Property(v => v.VolumeCapacityM3)
            .IsRequired()
            .HasPrecision(10, 2);

        builder.Property(v => v.Status)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(v => v.CreatedAt)
            .IsRequired();

        builder.Property(v => v.LastMaintenanceDate);

        builder.Property(v => v.NextMaintenanceDate);

        builder.Property(v => v.InsuranceNumber)
            .HasMaxLength(100);

        builder.Property(v => v.InsuranceExpiryDate);

        builder.Property(v => v.FitnessNumber)
            .HasMaxLength(100);

        builder.Property(v => v.FitnessExpiryDate);

        builder.Property(v => v.FuelCapacityLiters)
            .HasPrecision(8, 2);

        builder.Property(v => v.Notes)
            .HasMaxLength(1000);

        // Configure relationships
        builder.HasMany(v => v.Documents)
            .WithOne(doc => doc.Vehicle)
            .HasForeignKey(doc => doc.VehicleId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(v => v.CarrierId);

        builder.HasIndex(v => v.RegistrationNumber)
            .IsUnique();

        builder.HasIndex(v => v.Status);

        builder.HasIndex(v => v.CreatedAt);
    }
}

public class VehicleDocumentConfiguration : IEntityTypeConfiguration<VehicleDocument>
{
    public void Configure(EntityTypeBuilder<VehicleDocument> builder)
    {
        builder.ToTable("vehicle_documents");

        builder.HasKey(vd => vd.Id);

        builder.Property(vd => vd.Id)
            .ValueGeneratedNever();

        builder.Property(vd => vd.VehicleId)
            .IsRequired();

        builder.Property(vd => vd.DocumentType)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(vd => vd.FileName)
            .IsRequired()
            .HasMaxLength(255);

        builder.Property(vd => vd.FileUrl)
            .IsRequired()
            .HasMaxLength(500);

        builder.Property(vd => vd.Description)
            .HasMaxLength(500);

        builder.Property(vd => vd.UploadedAt)
            .IsRequired();

        builder.Property(vd => vd.ExpiryDate);

        builder.Property(vd => vd.IsVerified)
            .IsRequired();

        builder.Property(vd => vd.VerifiedAt);

        builder.Property(vd => vd.VerifiedBy)
            .HasMaxLength(100);

        // Indexes
        builder.HasIndex(vd => vd.VehicleId);

        builder.HasIndex(vd => vd.DocumentType);

        builder.HasIndex(vd => vd.UploadedAt);
    }
}
