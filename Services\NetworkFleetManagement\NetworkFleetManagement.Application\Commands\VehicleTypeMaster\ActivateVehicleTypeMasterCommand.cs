using MediatR;

namespace NetworkFleetManagement.Application.Commands.VehicleTypeMaster;

/// <summary>
/// Command to activate a vehicle type master
/// </summary>
public class ActivateVehicleTypeMasterCommand : IRequest<bool>
{
    public Guid Id { get; set; }

    public ActivateVehicleTypeMasterCommand(Guid id)
    {
        Id = id;
    }
}

/// <summary>
/// Command to deactivate a vehicle type master
/// </summary>
public class DeactivateVehicleTypeMasterCommand : IRequest<bool>
{
    public Guid Id { get; set; }

    public DeactivateVehicleTypeMasterCommand(Guid id)
    {
        Id = id;
    }
}
