using AnalyticsBIService.Domain.Enums;
using Shared.Domain.Common;

namespace AnalyticsBIService.Domain.Entities;

/// <summary>
/// Trip data entity for analytics
/// </summary>
public class TripData : BaseEntity
{
    public string TripNumber { get; private set; }
    public Guid OrderId { get; private set; }
    public Guid DriverId { get; private set; }
    public Guid VehicleId { get; private set; }
    public string Status { get; private set; }
    public DateTime StartTime { get; private set; }
    public DateTime? EndTime { get; private set; }
    public string OriginLocation { get; private set; }
    public string DestinationLocation { get; private set; }
    public decimal PlannedDistance { get; private set; }
    public decimal ActualDistance { get; private set; }
    public decimal PlannedDuration { get; private set; }
    public decimal ActualDuration { get; private set; }
    public decimal FuelConsumed { get; private set; }
    public decimal FuelCost { get; private set; }
    public decimal TollCharges { get; private set; }
    public decimal OtherExpenses { get; private set; }
    public decimal AverageSpeed { get; private set; }
    public decimal MaxSpeed { get; private set; }
    public int IdleTime { get; private set; }
    public int RestStops { get; private set; }
    public bool HasIncidents { get; private set; }
    public string? IncidentDetails { get; private set; }
    public decimal DriverRating { get; private set; }

    // Private constructor for EF Core
    private TripData() 
    {
        TripNumber = string.Empty;
        Status = string.Empty;
        OriginLocation = string.Empty;
        DestinationLocation = string.Empty;
    }

    /// <summary>
    /// Create trip data for analytics
    /// </summary>
    public static TripData Create(
        string tripNumber,
        Guid orderId,
        Guid driverId,
        Guid vehicleId,
        DateTime startTime,
        string originLocation,
        string destinationLocation,
        decimal plannedDistance,
        decimal plannedDuration)
    {
        return new TripData
        {
            TripNumber = tripNumber,
            OrderId = orderId,
            DriverId = driverId,
            VehicleId = vehicleId,
            Status = "Started",
            StartTime = startTime,
            OriginLocation = originLocation,
            DestinationLocation = destinationLocation,
            PlannedDistance = plannedDistance,
            PlannedDuration = plannedDuration,
            HasIncidents = false,
            DriverRating = 0
        };
    }

    /// <summary>
    /// Complete the trip
    /// </summary>
    public void CompleteTrip(
        DateTime endTime,
        decimal actualDistance,
        decimal fuelConsumed,
        decimal fuelCost,
        decimal tollCharges,
        decimal otherExpenses,
        decimal averageSpeed,
        decimal maxSpeed,
        int idleTime,
        int restStops,
        decimal driverRating)
    {
        Status = "Completed";
        EndTime = endTime;
        ActualDistance = actualDistance;
        ActualDuration = (decimal)(endTime - StartTime).TotalHours;
        FuelConsumed = fuelConsumed;
        FuelCost = fuelCost;
        TollCharges = tollCharges;
        OtherExpenses = otherExpenses;
        AverageSpeed = averageSpeed;
        MaxSpeed = maxSpeed;
        IdleTime = idleTime;
        RestStops = restStops;
        DriverRating = driverRating;
    }

    /// <summary>
    /// Record incident
    /// </summary>
    public void RecordIncident(string incidentDetails)
    {
        HasIncidents = true;
        IncidentDetails = incidentDetails;
    }

    /// <summary>
    /// Update trip status
    /// </summary>
    public void UpdateStatus(string status)
    {
        Status = status;
    }

    /// <summary>
    /// Calculate fuel efficiency
    /// </summary>
    public decimal GetFuelEfficiency()
    {
        return FuelConsumed > 0 ? ActualDistance / FuelConsumed : 0;
    }

    /// <summary>
    /// Calculate total trip cost
    /// </summary>
    public decimal GetTotalCost()
    {
        return FuelCost + TollCharges + OtherExpenses;
    }

    /// <summary>
    /// Calculate cost per kilometer
    /// </summary>
    public decimal GetCostPerKm()
    {
        var totalCost = GetTotalCost();
        return ActualDistance > 0 ? totalCost / ActualDistance : 0;
    }

    /// <summary>
    /// Get efficiency score
    /// </summary>
    public decimal GetEfficiencyScore()
    {
        if (PlannedDistance == 0 || PlannedDuration == 0) return 0;
        
        var distanceEfficiency = PlannedDistance / Math.Max(ActualDistance, 1);
        var timeEfficiency = PlannedDuration / Math.Max(ActualDuration, 1);
        
        return (distanceEfficiency + timeEfficiency) / 2 * 100;
    }

    /// <summary>
    /// Check if trip is on schedule
    /// </summary>
    public bool IsOnSchedule()
    {
        if (!EndTime.HasValue) return true;
        return ActualDuration <= PlannedDuration * 1.1m; // 10% tolerance
    }
}
