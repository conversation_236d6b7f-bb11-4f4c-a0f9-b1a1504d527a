using OrderManagement.Domain.Primitives;
using Shared.Domain.Common;
using OrderManagement.Domain.Enums;
using OrderManagement.Domain.ValueObjects;

namespace OrderManagement.Domain.Entities;

public class Invoice : Entity
{
    public Guid OrderId { get; private set; }
    public string InvoiceNumber { get; private set; }
    public DateTime InvoiceDate { get; private set; }
    public DateTime DueDate { get; private set; }
    public Money Amount { get; private set; }
    public Money? TaxAmount { get; private set; }
    public Money TotalAmount { get; private set; }
    public InvoiceStatus Status { get; private set; }
    public string? Description { get; private set; }
    public string? Notes { get; private set; }
    public DateTime? PaidDate { get; private set; }
    public string? PaymentReference { get; private set; }
    public DateTime CreatedAt { get; private set; }

    // Navigation properties
    public Order Order { get; private set; } = null!;

    private readonly List<InvoiceLineItem> _lineItems = new();
    public IReadOnlyCollection<InvoiceLineItem> LineItems => _lineItems.AsReadOnly();

    private Invoice() { } // EF Constructor

    public Invoice(
        Guid orderId,
        Money amount,
        string? description = null,
        Money? taxAmount = null,
        string? notes = null)
    {
        OrderId = orderId;
        InvoiceNumber = GenerateInvoiceNumber();
        InvoiceDate = DateTime.UtcNow;
        DueDate = DateTime.UtcNow.AddDays(30); // Default 30 days payment terms
        Amount = amount ?? throw new ArgumentNullException(nameof(amount));
        TaxAmount = taxAmount;
        TotalAmount = taxAmount != null ? amount + taxAmount : amount;
        Status = InvoiceStatus.Draft;
        Description = description;
        Notes = notes;
        CreatedAt = DateTime.UtcNow;

        // Domain event for invoice created
        AddDomainEvent(new InvoiceCreatedDomainEvent(Id, OrderId, InvoiceNumber, TotalAmount));
    }

    public void Send(string? contactEmail = null)
    {
        if (Status != InvoiceStatus.Draft)
            throw new InvalidOperationException("Only draft invoices can be sent");

        Status = InvoiceStatus.Sent;

        // Domain event for invoice sent
        AddDomainEvent(new InvoiceSentDomainEvent(Id, InvoiceNumber, contactEmail ?? ""));
    }

    public void MarkAsPaid(string paymentReference)
    {
        if (Status == InvoiceStatus.Paid)
            throw new InvalidOperationException("Invoice is already paid");

        if (Status == InvoiceStatus.Cancelled)
            throw new InvalidOperationException("Cannot mark cancelled invoice as paid");

        Status = InvoiceStatus.Paid;
        PaidDate = DateTime.UtcNow;
        PaymentReference = paymentReference;

        // Domain event for invoice paid
        AddDomainEvent(new InvoicePaidDomainEvent(Id, OrderId, InvoiceNumber, TotalAmount, paymentReference));
    }

    public void Cancel()
    {
        if (Status == InvoiceStatus.Paid)
            throw new InvalidOperationException("Cannot cancel paid invoice");

        Status = InvoiceStatus.Cancelled;

        // Domain event for invoice cancelled
        AddDomainEvent(new InvoiceCancelledDomainEvent(Id, InvoiceNumber));
    }

    public void AddLineItem(InvoiceLineItem lineItem)
    {
        if (Status != InvoiceStatus.Draft)
            throw new InvalidOperationException("Cannot modify sent or paid invoices");

        _lineItems.Add(lineItem);
    }

    private static string GenerateInvoiceNumber()
    {
        return $"INV-{DateTime.UtcNow:yyyyMMdd}-{Guid.NewGuid().ToString("N")[..8].ToUpper()}";
    }
}

public class InvoiceLineItem : Entity
{
    public Guid InvoiceId { get; private set; }
    public string Description { get; private set; }
    public int Quantity { get; private set; }
    public Money UnitPrice { get; private set; }
    public Money TotalPrice { get; private set; }

    private InvoiceLineItem() { } // EF Constructor

    public InvoiceLineItem(string description, int quantity, Money unitPrice)
    {
        Description = description ?? throw new ArgumentNullException(nameof(description));
        Quantity = quantity;
        UnitPrice = unitPrice ?? throw new ArgumentNullException(nameof(unitPrice));
        TotalPrice = unitPrice * quantity;
    }
}

// Domain Events
public record InvoiceCreatedDomainEvent(Guid InvoiceId, Guid OrderId, string InvoiceNumber, Money Amount) : Shared.Domain.Common.IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}
public record InvoiceSentDomainEvent(Guid InvoiceId, string InvoiceNumber, string CustomerEmail) : Shared.Domain.Common.IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}
public record InvoicePaidDomainEvent(Guid InvoiceId, Guid OrderId, string InvoiceNumber, Money Amount, string PaymentReference) : Shared.Domain.Common.IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}
public record InvoiceCancelledDomainEvent(Guid InvoiceId, string InvoiceNumber) : Shared.Domain.Common.IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}


