using ContentManagement.Application.DTOs;
using ContentManagement.Domain.Enums;
using MediatR;

namespace ContentManagement.Application.Queries.Faq;

/// <summary>
/// Query to get FAQ item by ID
/// </summary>
public class GetFaqByIdQuery : IRequest<FaqItemDto?>
{
    public Guid Id { get; set; }
    public bool IncludeVersions { get; set; } = false;
    
    // User context for visibility checks
    public List<string> UserRoles { get; set; } = new();
    public bool IsAuthenticated { get; set; } = false;
}

/// <summary>
/// Query to search FAQ items with filters
/// </summary>
public class SearchFaqsQuery : IRequest<FaqSearchResultDto>
{
    public string? SearchTerm { get; set; }
    public FaqCategory? Category { get; set; }
    public ContentStatus? Status { get; set; }
    public bool? IsHighlighted { get; set; }
    public List<string>? Tags { get; set; }
    public string? SortBy { get; set; } = "SortOrder";
    public bool SortDescending { get; set; } = false;
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 20;
    
    // User context for visibility checks
    public List<string> UserRoles { get; set; } = new();
    public bool IsAuthenticated { get; set; } = false;
}

/// <summary>
/// Query to get FAQs by category
/// </summary>
public class GetFaqsByCategoryQuery : IRequest<List<FaqSummaryDto>>
{
    public FaqCategory Category { get; set; }
    public ContentStatus? Status { get; set; }
    public bool PublishedOnly { get; set; } = true;
    
    // User context for visibility checks
    public List<string> UserRoles { get; set; } = new();
    public bool IsAuthenticated { get; set; } = false;
}

/// <summary>
/// Query to get highlighted FAQs
/// </summary>
public class GetHighlightedFaqsQuery : IRequest<List<FaqSummaryDto>>
{
    public int? Limit { get; set; } = 10;
    public ContentStatus? Status { get; set; }
    
    // User context for visibility checks
    public List<string> UserRoles { get; set; } = new();
    public bool IsAuthenticated { get; set; } = false;
}

/// <summary>
/// Query to get most viewed FAQs
/// </summary>
public class GetMostViewedFaqsQuery : IRequest<List<FaqSummaryDto>>
{
    public int? Limit { get; set; } = 10;
    public FaqCategory? Category { get; set; }
    public DateTime? FromDate { get; set; }
    
    // User context for visibility checks
    public List<string> UserRoles { get; set; } = new();
    public bool IsAuthenticated { get; set; } = false;
}

/// <summary>
/// Query to get highest rated FAQs
/// </summary>
public class GetHighestRatedFaqsQuery : IRequest<List<FaqSummaryDto>>
{
    public int? Limit { get; set; } = 10;
    public FaqCategory? Category { get; set; }
    public int? MinimumRatings { get; set; } = 5;
    
    // User context for visibility checks
    public List<string> UserRoles { get; set; } = new();
    public bool IsAuthenticated { get; set; } = false;
}

/// <summary>
/// Query to get FAQ category statistics
/// </summary>
public class GetFaqCategoryStatsQuery : IRequest<List<FaqCategoryStatsDto>>
{
    public bool IncludeEmpty { get; set; } = false;
}

/// <summary>
/// Query to get FAQ analytics
/// </summary>
public class GetFaqAnalyticsQuery : IRequest<FaqAnalyticsDto>
{
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public FaqCategory? Category { get; set; }
}

/// <summary>
/// FAQ analytics DTO
/// </summary>
public class FaqAnalyticsDto
{
    public int TotalFaqs { get; set; }
    public int PublishedFaqs { get; set; }
    public int TotalViews { get; set; }
    public int TotalRatings { get; set; }
    public decimal AverageRating { get; set; }
    public Dictionary<FaqCategory, int> FaqsByCategory { get; set; } = new();
    public List<FaqSummaryDto> MostViewed { get; set; } = new();
    public List<FaqSummaryDto> HighestRated { get; set; } = new();
    public List<FaqSummaryDto> RecentlyAdded { get; set; } = new();
    public DateTime? LastUpdated { get; set; }
}
