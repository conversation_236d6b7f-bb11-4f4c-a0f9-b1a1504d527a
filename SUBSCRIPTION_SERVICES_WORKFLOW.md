# Subscription Services - Getting Started Workflow

This comprehensive guide provides step-by-step instructions for setting up and getting started with the Subscription Management Service in the TLI Logistics microservices platform.

## 📋 Overview

The Subscription Management Service is a comprehensive microservice that handles:
- Subscription lifecycle management (create, activate, cancel, suspend)
- Plan management with tiered pricing (Basic, Pro, Enterprise)
- Payment processing with RazorPay integration
- Feature access control and usage limits
- Automatic billing and renewal handling
- Multi-user type support (Transport Companies, Brokers, Carriers)

## 🏗️ Architecture

The service follows Clean Architecture principles with:
- **Domain Layer**: Core business entities and rules
- **Application Layer**: Use cases and business logic
- **Infrastructure Layer**: Data persistence and external integrations
- **API Layer**: REST endpoints and controllers

## 🚀 Step-by-Step Workflow

### Step 1: Environment Setup and Prerequisites

#### Required Software
```bash
# 1. Install .NET 8 SDK
# Download from: https://dotnet.microsoft.com/download/dotnet/8.0

# 2. Install PostgreSQL
# Download from: https://www.postgresql.org/download/

# 3. Install Git (if not already installed)
# Download from: https://git-scm.com/downloads

# 4. Optional: Install Docker Desktop
# Download from: https://www.docker.com/products/docker-desktop
```

#### Verify Installation
```bash
# Check .NET version
dotnet --version  # Should show 8.x.x

# Check PostgreSQL
psql --version    # Should show PostgreSQL version

# Check Git
git --version     # Should show Git version
```

#### Development Tools (Recommended)
- **Visual Studio 2022** or **Visual Studio Code**
- **Postman** or **Insomnia** for API testing
- **pgAdmin** for PostgreSQL management
- **RabbitMQ Management UI** for message queue monitoring

### Step 2: Database Configuration and Setup

#### Option A: Automated Setup (Recommended)
```powershell
# Navigate to subscription service directory
cd Services/SubscriptionManagement

# Run the automated setup script
./setup.ps1 -DatabaseHost localhost -DatabasePort 5432 -DatabaseUser timescale -DatabasePassword timescale
```

#### Option B: Manual Setup
```bash
# 1. Create database
psql -h localhost -U timescale -d postgres -c "CREATE DATABASE TLI_SubscriptionManagement;"

# 2. Run database setup script
psql -h localhost -U timescale -d TLI_SubscriptionManagement -f database-setup.sql

# 3. Verify database setup
psql -h localhost -U timescale -d TLI_SubscriptionManagement -c "\dt"
```

#### Database Configuration
Update connection string in `appsettings.json`:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Port=5432;Database=TLI_SubscriptionManagement;User Id=timescale;Password=timescale"
  }
}
```

### Step 3: Service Dependencies Configuration

The Subscription Service requires these services to be running:

#### 3.1 Identity Service (Port 5001)
```bash
# Navigate to Identity service
cd Identity/Identity.API

# Configure database connection
# Update appsettings.json with proper connection string

# Run the service
dotnet run
```

#### 3.2 User Management Service (Port 5002)
```bash
# Navigate to User Management service
cd Services/UserManagement/UserManagement.API

# Configure database connection
# Update appsettings.json with proper connection string

# Run the service
dotnet run
```

#### 3.3 API Gateway (Port 5000)
```bash
# Navigate to API Gateway
cd ApiGateway

# Configure Ocelot routing
# Verify ocelot.json has correct service endpoints

# Run the API Gateway
dotnet run
```

#### 3.4 RabbitMQ (Optional but Recommended)
```bash
# Using Docker
docker run -d --name rabbitmq -p 5672:5672 -p 15672:15672 rabbitmq:3-management

# Or install locally from: https://www.rabbitmq.com/download.html
```

### Step 4: Subscription Service Configuration

#### 4.1 JWT Configuration
Ensure JWT settings match across all services:
```json
{
  "JwtSettings": {
    "Secret": "your-super-secret-key-that-is-at-least-32-characters-long",
    "Issuer": "TLI-Identity-Service",
    "Audience": "TLI-Services",
    "ExpiryMinutes": 60
  }
}
```

#### 4.2 RazorPay Configuration
```json
{
  "RazorPay": {
    "KeyId": "your_razorpay_key_id",
    "KeySecret": "your_razorpay_key_secret",
    "WebhookSecret": "your_webhook_secret"
  }
}
```

#### 4.3 RabbitMQ Configuration
```json
{
  "RabbitMQ": {
    "Host": "localhost",
    "Port": 5672,
    "Username": "guest",
    "Password": "guest"
  }
}
```

### Step 5: Service Startup and Verification

#### 5.1 Start Subscription Service
```bash
# Navigate to subscription service API
cd Services/SubscriptionManagement/SubscriptionManagement.API

# Build the project
dotnet build

# Run the service
dotnet run
```

#### 5.2 Verify Service Health
```bash
# Check service health
curl http://localhost:5003/health

# Check Swagger UI
# Open browser: http://localhost:5003
```

#### 5.3 Verify API Gateway Integration
```bash
# Test through API Gateway
curl http://localhost:5000/api/plans

# Check API Gateway health
curl http://localhost:5000/health
```

### Step 6: API Testing and Integration Verification

#### 6.1 Test Public Endpoints
```bash
# Get all public plans
curl -X GET "http://localhost:5003/api/plans" \
  -H "accept: application/json"

# Get plans by user type
curl -X GET "http://localhost:5003/api/plans/by-user-type/TransportCompany" \
  -H "accept: application/json"
```

#### 6.2 Test Authenticated Endpoints
First, get a JWT token from Identity Service:
```bash
# Login to get JWT token
curl -X POST "http://localhost:5001/api/identity/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Admin@123"
  }'
```

Then use the token for subscription operations:
```bash
# Create subscription (replace YOUR_JWT_TOKEN)
curl -X POST "http://localhost:5003/api/subscriptions" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "planId": "PLAN_GUID_FROM_PLANS_ENDPOINT",
    "autoRenew": true
  }'
```

### Step 7: Sample Data and Plan Configuration

The database setup script includes sample plans:

#### Transport Company Plans
- **Basic**: ₹999/month, 10 RFQs, Basic tracking
- **Pro**: ₹2,999/month, Unlimited RFQs, Advanced features
- **Enterprise**: ₹9,999/month, Custom features, Dedicated support

#### Broker Plans
- **Basic**: ₹1,499/month, 20 RFQs, Basic features
- **Pro**: ₹4,999/month, Unlimited RFQs, Advanced analytics
- **Enterprise**: ₹14,999/month, Full feature access, API access

#### Carrier Plans
- **Basic**: ₹799/month, 15 RFQs, 2 vehicles
- **Pro**: ₹2,499/month, Unlimited RFQs, 10 vehicles
- **Enterprise**: ₹7,999/month, Unlimited fleet, Volume pricing

### Step 8: Payment Integration Testing

#### 8.1 RazorPay Setup
1. Create RazorPay account at https://razorpay.com/
2. Get API keys from RazorPay dashboard
3. Configure webhook endpoints
4. Update appsettings.json with credentials

#### 8.2 Test Payment Flow
```bash
# Test payment creation
curl -X POST "http://localhost:5003/api/subscriptions/{id}/payments" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "paymentMethodId": "pm_test_123",
    "amount": 999.00
  }'
```

### Step 9: Monitoring and Troubleshooting

#### 9.1 Health Checks
Monitor service health at:
- Subscription Service: `http://localhost:5003/health`
- Identity Service: `http://localhost:5001/health`
- User Management: `http://localhost:5002/health`
- API Gateway: `http://localhost:5000/health`

#### 9.2 Logging
Check logs for troubleshooting:
- Application logs in console output
- Structured logging with Serilog
- Database query logs (if enabled)

#### 9.3 Common Issues and Solutions

**Database Connection Issues:**
- Verify PostgreSQL is running
- Check connection string format
- Ensure database exists and is accessible

**JWT Authentication Issues:**
- Verify JWT secret matches across services
- Check token expiration
- Ensure proper Authorization header format

**Service Communication Issues:**
- Verify all required services are running
- Check port configurations
- Test service-to-service connectivity

## 🔄 Docker Deployment (Alternative)

For containerized deployment:

```bash
# Build and run with Docker Compose
docker-compose up -d

# Or run individual service
cd Services/SubscriptionManagement
docker build -t subscription-management .
docker run -p 5003:80 subscription-management
```

## 📚 Next Steps

1. **Implement Custom Plans**: Create custom subscription plans for specific business needs
2. **Add Caching**: Implement Redis caching for improved performance
3. **Set up Monitoring**: Configure application monitoring and alerting
4. **Load Testing**: Perform load testing under expected traffic
5. **Security Hardening**: Implement additional security measures for production

## 🆘 Support and Documentation

- **API Documentation**: Available at `http://localhost:5003` (Swagger UI)
- **Setup Guide**: See `Services/SubscriptionManagement/SETUP.md`
- **Architecture Details**: See `Services/SubscriptionManagement/README.md`
- **Troubleshooting**: Check service logs and health endpoints

---

This workflow provides a complete guide for getting started with Subscription services. Follow each step carefully and verify each component before proceeding to the next step.
