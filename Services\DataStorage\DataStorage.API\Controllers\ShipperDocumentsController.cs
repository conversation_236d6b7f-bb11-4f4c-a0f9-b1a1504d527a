using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using MediatR;
using DataStorage.Application.Commands;
using DataStorage.Application.Queries;
using DataStorage.Application.DTOs;
using DataStorage.Domain.Enums;

namespace DataStorage.API.Controllers;

[ApiController]
[Route("api/shipper/documents")]
[Authorize(Roles = "Shipper,Admin")]
public class ShipperDocumentsController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<ShipperDocumentsController> _logger;

    public ShipperDocumentsController(IMediator mediator, ILogger<ShipperDocumentsController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Upload E-Way Bill document
    /// </summary>
    [HttpPost("eway-bills")]
    public async Task<ActionResult<DocumentDto>> UploadEWayBill([FromForm] UploadEWayBillRequest request)
    {
        try
        {
            var shipperId = GetCurrentUserId();
            
            var command = new UploadEWayBillCommand
            {
                ShipperId = shipperId,
                Title = request.Title,
                Description = request.Description,
                FileStream = request.File.OpenReadStream(),
                FileName = request.File.FileName,
                ContentType = request.File.ContentType,
                EWayBillNumber = request.EWayBillNumber,
                ValidUntil = request.ValidUntil,
                AdditionalMetadata = request.AdditionalMetadata
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to upload E-Way Bill for shipper {ShipperId}", GetCurrentUserId());
            return StatusCode(500, "Failed to upload E-Way Bill");
        }
    }

    /// <summary>
    /// Generate digital invoice
    /// </summary>
    [HttpPost("invoices/generate")]
    public async Task<ActionResult<DocumentDto>> GenerateDigitalInvoice([FromBody] GenerateDigitalInvoiceRequest request)
    {
        try
        {
            var shipperId = GetCurrentUserId();
            
            var command = new GenerateDigitalInvoiceCommand
            {
                ShipperId = shipperId,
                OrderId = request.OrderId,
                InvoiceNumber = request.InvoiceNumber,
                Amount = request.Amount,
                Currency = request.Currency,
                InvoiceDate = request.InvoiceDate,
                DueDate = request.DueDate,
                InvoiceData = request.InvoiceData,
                AutoGenerate = request.AutoGenerate
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate digital invoice for shipper {ShipperId}", GetCurrentUserId());
            return StatusCode(500, "Failed to generate digital invoice");
        }
    }

    /// <summary>
    /// Upload tax document
    /// </summary>
    [HttpPost("tax-documents")]
    public async Task<ActionResult<DocumentDto>> UploadTaxDocument([FromForm] UploadTaxDocumentRequest request)
    {
        try
        {
            var shipperId = GetCurrentUserId();
            
            var command = new UploadTaxDocumentCommand
            {
                ShipperId = shipperId,
                Title = request.Title,
                Description = request.Description,
                FileStream = request.File.OpenReadStream(),
                FileName = request.File.FileName,
                ContentType = request.File.ContentType,
                TaxDocumentType = request.TaxDocumentType,
                GSTNumber = request.GSTNumber,
                PANNumber = request.PANNumber,
                TaxPeriodStart = request.TaxPeriodStart,
                TaxPeriodEnd = request.TaxPeriodEnd
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to upload tax document for shipper {ShipperId}", GetCurrentUserId());
            return StatusCode(500, "Failed to upload tax document");
        }
    }

    /// <summary>
    /// Upload transport documentation
    /// </summary>
    [HttpPost("transport-documents")]
    public async Task<ActionResult<DocumentDto>> UploadTransportDocumentation([FromForm] UploadTransportDocumentationRequest request)
    {
        try
        {
            var shipperId = GetCurrentUserId();
            
            var command = new UploadTransportDocumentationCommand
            {
                ShipperId = shipperId,
                Title = request.Title,
                Description = request.Description,
                FileStream = request.File.OpenReadStream(),
                FileName = request.File.FileName,
                ContentType = request.File.ContentType,
                RelatedOrderId = request.RelatedOrderId,
                RelatedTripId = request.RelatedTripId,
                TransportDocumentType = request.TransportDocumentType
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to upload transport documentation for shipper {ShipperId}", GetCurrentUserId());
            return StatusCode(500, "Failed to upload transport documentation");
        }
    }

    /// <summary>
    /// Get all documents for current shipper
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<DocumentDto>>> GetShipperDocuments([FromQuery] GetShipperDocumentsRequest request)
    {
        try
        {
            var shipperId = GetCurrentUserId();
            
            var query = new GetShipperDocumentsQuery
            {
                ShipperId = shipperId,
                DocumentType = request.DocumentType,
                FromDate = request.FromDate,
                ToDate = request.ToDate,
                RequestingUserId = shipperId
            };

            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get documents for shipper {ShipperId}", GetCurrentUserId());
            return StatusCode(500, "Failed to retrieve documents");
        }
    }

    /// <summary>
    /// Get E-Way Bills for current shipper
    /// </summary>
    [HttpGet("eway-bills")]
    public async Task<ActionResult<IEnumerable<DocumentDto>>> GetEWayBills([FromQuery] GetEWayBillsRequest request)
    {
        try
        {
            var shipperId = GetCurrentUserId();
            
            var query = new GetShipperEWayBillsQuery
            {
                ShipperId = shipperId,
                EWayBillNumber = request.EWayBillNumber,
                Status = request.Status,
                FromDate = request.FromDate,
                ToDate = request.ToDate,
                RequestingUserId = shipperId
            };

            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get E-Way Bills for shipper {ShipperId}", GetCurrentUserId());
            return StatusCode(500, "Failed to retrieve E-Way Bills");
        }
    }

    /// <summary>
    /// Get tax documents for current shipper
    /// </summary>
    [HttpGet("tax-documents")]
    public async Task<ActionResult<IEnumerable<DocumentDto>>> GetTaxDocuments([FromQuery] GetTaxDocumentsRequest request)
    {
        try
        {
            var shipperId = GetCurrentUserId();
            
            var query = new GetShipperTaxDocumentsQuery
            {
                ShipperId = shipperId,
                TaxDocumentType = request.TaxDocumentType,
                TaxYear = request.TaxYear,
                RequestingUserId = shipperId
            };

            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get tax documents for shipper {ShipperId}", GetCurrentUserId());
            return StatusCode(500, "Failed to retrieve tax documents");
        }
    }

    /// <summary>
    /// Get invoice history for current shipper
    /// </summary>
    [HttpGet("invoices")]
    public async Task<ActionResult<IEnumerable<DocumentDto>>> GetInvoiceHistory([FromQuery] GetInvoiceHistoryRequest request)
    {
        try
        {
            var shipperId = GetCurrentUserId();
            
            var query = new GetShipperInvoiceHistoryQuery
            {
                ShipperId = shipperId,
                OrderId = request.OrderId,
                InvoiceNumber = request.InvoiceNumber,
                FromDate = request.FromDate,
                ToDate = request.ToDate,
                RequestingUserId = shipperId
            };

            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get invoice history for shipper {ShipperId}", GetCurrentUserId());
            return StatusCode(500, "Failed to retrieve invoice history");
        }
    }

    /// <summary>
    /// Export shipper documents
    /// </summary>
    [HttpPost("export")]
    public async Task<ActionResult<DocumentDto>> ExportDocuments([FromBody] ExportShipperDocumentsRequest request)
    {
        try
        {
            var shipperId = GetCurrentUserId();
            
            var command = new ExportShipperDocumentsCommand
            {
                ShipperId = shipperId,
                FromDate = request.FromDate,
                ToDate = request.ToDate,
                DocumentType = request.DocumentType,
                Format = request.Format,
                IncludeMetadata = request.IncludeMetadata,
                ExportTitle = request.ExportTitle
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to export documents for shipper {ShipperId}", GetCurrentUserId());
            return StatusCode(500, "Failed to export documents");
        }
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst("sub") ?? User.FindFirst("user_id");
        if (userIdClaim != null && Guid.TryParse(userIdClaim.Value, out var userId))
        {
            return userId;
        }
        throw new UnauthorizedAccessException("User ID not found in token");
    }
}

// Request DTOs for Shipper endpoints
public class UploadEWayBillRequest
{
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; }
    public IFormFile File { get; set; } = null!;
    public string? EWayBillNumber { get; set; }
    public DateTime? ValidUntil { get; set; }
    public Dictionary<string, string>? AdditionalMetadata { get; set; }
}

public class GenerateDigitalInvoiceRequest
{
    public Guid OrderId { get; set; }
    public string InvoiceNumber { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public string Currency { get; set; } = "INR";
    public DateTime InvoiceDate { get; set; }
    public DateTime DueDate { get; set; }
    public Dictionary<string, object> InvoiceData { get; set; } = new();
    public bool AutoGenerate { get; set; } = true;
}

public class UploadTaxDocumentRequest
{
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; }
    public IFormFile File { get; set; } = null!;
    public TaxDocumentType TaxDocumentType { get; set; }
    public string? GSTNumber { get; set; }
    public string? PANNumber { get; set; }
    public DateTime? TaxPeriodStart { get; set; }
    public DateTime? TaxPeriodEnd { get; set; }
}

public class UploadTransportDocumentationRequest
{
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; }
    public IFormFile File { get; set; } = null!;
    public Guid? RelatedOrderId { get; set; }
    public Guid? RelatedTripId { get; set; }
    public TransportDocumentType TransportDocumentType { get; set; }
}

public class GetShipperDocumentsRequest
{
    public DocumentType? DocumentType { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
}

public class GetEWayBillsRequest
{
    public string? EWayBillNumber { get; set; }
    public DocumentStatus? Status { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
}

public class GetTaxDocumentsRequest
{
    public TaxDocumentType? TaxDocumentType { get; set; }
    public int? TaxYear { get; set; }
}

public class GetInvoiceHistoryRequest
{
    public Guid? OrderId { get; set; }
    public string? InvoiceNumber { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
}

public class ExportShipperDocumentsRequest
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public DocumentType? DocumentType { get; set; }
    public ExportFormat Format { get; set; } = ExportFormat.PDF;
    public bool IncludeMetadata { get; set; } = true;
    public string? ExportTitle { get; set; }
}
