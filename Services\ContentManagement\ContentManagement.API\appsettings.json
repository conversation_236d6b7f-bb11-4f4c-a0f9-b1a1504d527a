{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Host=localhost;Database=TLI_ContentManagement;Username=********;Password=********;Port=5432"}, "Jwt": {"Key": "YourSuperSecretKeyThatIsAtLeast32CharactersLong!", "Issuer": "TLI.ContentManagement", "Audience": "TLI.Users", "ExpiryInMinutes": 60}, "Services": {"DataStorage": {"BaseUrl": "http://localhost:5008"}, "AuditCompliance": {"BaseUrl": "http://localhost:5012"}, "CommunicationNotification": {"BaseUrl": "http://localhost:5009"}, "Identity": {"BaseUrl": "http://localhost:5001"}}, "RabbitMQ": {"HostName": "localhost", "Port": 5672, "UserName": "guest", "Password": "guest", "VirtualHost": "/", "ExchangeName": "tli.events", "QueueName": "content.management"}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/contentmanagement-.txt", "rollingInterval": "Day", "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}, "ContentManagement": {"MaxContentSize": 10485760, "AllowedFileTypes": [".jpg", ".jpeg", ".png", ".gif", ".pdf", ".doc", ".docx", ".txt"], "MaxAttachmentsPerContent": 10, "DefaultPageSize": 20, "MaxPageSize": 100, "EnableFullTextSearch": true, "CacheExpirationMinutes": 30, "ScheduledPublishingIntervalMinutes": 5}, "HealthChecks": {"UI": {"HealthChecksUIEnabled": true, "HealthChecksUIPath": "/healthchecks-ui", "ApiPath": "/health"}}}