using AuditCompliance.Domain.Enums;

namespace AuditCompliance.Application.DTOs
{
    /// <summary>
    /// Request DTO for exporting feedback history
    /// </summary>
    public class FeedbackExportRequestDto
    {
        public List<Guid> UserIds { get; set; } = new();
        public List<Guid> TransportCompanyIds { get; set; } = new();
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public decimal? MinRating { get; set; }
        public decimal? MaxRating { get; set; }
        public RatingStatus? Status { get; set; }
        public bool IncludeAnonymous { get; set; } = true;
        public bool AnonymizeData { get; set; } = false;
        public ExportFormat Format { get; set; } = ExportFormat.CSV;
        public List<string> SelectedColumns { get; set; } = new();
        public int? MaxRecords { get; set; }
        
        // Audit fields (set by controller)
        public Guid RequestedBy { get; set; }
        public string RequestedByRole { get; set; }
        public string IpAddress { get; set; }
        public string UserAgent { get; set; }
    }

    /// <summary>
    /// Response DTO for feedback export
    /// </summary>
    public class FeedbackExportResponseDto
    {
        public string FileName { get; set; }
        public string FileUrl { get; set; }
        public ExportFormat Format { get; set; }
        public int RecordCount { get; set; }
        public int UserCount { get; set; }
        public int TransportCompanyCount { get; set; }
        public long FileSizeBytes { get; set; }
        public DateTime GeneratedAt { get; set; }
        public DateTime ExpiresAt { get; set; }
        public string ExportId { get; set; }
        public FeedbackExportSummaryDto Summary { get; set; } = new();
        public bool DataAnonymized { get; set; }
    }

    /// <summary>
    /// Summary of exported feedback data
    /// </summary>
    public class FeedbackExportSummaryDto
    {
        public int TotalRatings { get; set; }
        public int AnonymousRatings { get; set; }
        public decimal AverageRating { get; set; }
        public Dictionary<RatingStatus, int> StatusBreakdown { get; set; } = new();
        public Dictionary<string, int> RatingDistribution { get; set; } = new();
        public Dictionary<string, int> CategoryRatingAverages { get; set; } = new();
        public int TotalIssuesReported { get; set; }
        public DateTime? EarliestRating { get; set; }
        public DateTime? LatestRating { get; set; }
    }

    /// <summary>
    /// Individual feedback entry for export
    /// </summary>
    public class FeedbackExportEntryDto
    {
        public Guid Id { get; set; }
        public Guid ShipperId { get; set; }
        public string ShipperName { get; set; }
        public Guid TransportCompanyId { get; set; }
        public string TransportCompanyName { get; set; }
        public Guid? OrderId { get; set; }
        public string? OrderNumber { get; set; }
        public Guid? TripId { get; set; }
        public string? TripNumber { get; set; }
        public decimal OverallRating { get; set; }
        public RatingStatus Status { get; set; }
        public string? ReviewTitle { get; set; }
        public string? ReviewComment { get; set; }
        public bool IsAnonymous { get; set; }
        public DateTime ServiceCompletedAt { get; set; }
        public DateTime? ReviewSubmittedAt { get; set; }
        public DateTime CreatedAt { get; set; }
        public List<CategoryRatingExportDto> CategoryRatings { get; set; } = new();
        public List<ServiceIssueExportDto> ReportedIssues { get; set; } = new();
    }

    /// <summary>
    /// Category rating for export
    /// </summary>
    public class CategoryRatingExportDto
    {
        public RatingCategory Category { get; set; }
        public string CategoryName { get; set; }
        public decimal Rating { get; set; }
        public string? Comment { get; set; }
    }

    /// <summary>
    /// Service issue for export
    /// </summary>
    public class ServiceIssueExportDto
    {
        public Guid Id { get; set; }
        public ServiceIssueType IssueType { get; set; }
        public string IssueTypeName { get; set; }
        public IssuePriority Priority { get; set; }
        public string Description { get; set; }
        public string? Evidence { get; set; }
        public DateTime ReportedAt { get; set; }
    }

    /// <summary>
    /// Export format enumeration
    /// </summary>
    public enum ExportFormat
    {
        CSV,
        Excel,
        PDF,
        JSON
    }
}
