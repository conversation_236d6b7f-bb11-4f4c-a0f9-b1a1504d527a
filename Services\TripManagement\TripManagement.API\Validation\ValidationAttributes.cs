using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;

namespace TripManagement.API.Validation;

/// <summary>
/// Validates that a string contains only safe characters (no SQL injection, XSS, etc.)
/// </summary>
public class SafeStringAttribute : ValidationAttribute
{
    private static readonly Regex UnsafeCharacters = new(@"[<>""'%;()&+]", RegexOptions.Compiled);

    public override bool IsValid(object? value)
    {
        if (value == null || value is not string stringValue)
            return true;

        return !UnsafeCharacters.IsMatch(stringValue);
    }

    public override string FormatErrorMessage(string name)
    {
        return $"The field {name} contains unsafe characters.";
    }
}

/// <summary>
/// Validates vehicle registration number format
/// </summary>
public class VehicleRegistrationAttribute : ValidationAttribute
{
    private static readonly Regex RegistrationPattern = new(@"^[A-Z0-9\-\s]{3,15}$", RegexOptions.Compiled);

    public override bool IsValid(object? value)
    {
        if (value == null || value is not string stringValue)
            return true;

        return RegistrationPattern.IsMatch(stringValue.ToUpper());
    }

    public override string FormatErrorMessage(string name)
    {
        return $"The field {name} must be a valid vehicle registration number (3-15 alphanumeric characters, spaces, and hyphens allowed).";
    }
}

/// <summary>
/// Validates driver license number format
/// </summary>
public class DriverLicenseAttribute : ValidationAttribute
{
    private static readonly Regex LicensePattern = new(@"^[A-Z0-9]{5,20}$", RegexOptions.Compiled);

    public override bool IsValid(object? value)
    {
        if (value == null || value is not string stringValue)
            return true;

        return LicensePattern.IsMatch(stringValue.ToUpper());
    }

    public override string FormatErrorMessage(string name)
    {
        return $"The field {name} must be a valid driver license number (5-20 alphanumeric characters).";
    }
}

/// <summary>
/// Validates phone number format
/// </summary>
public class PhoneNumberAttribute : ValidationAttribute
{
    private static readonly Regex PhonePattern = new(@"^\+?[1-9]\d{1,14}$", RegexOptions.Compiled);

    public override bool IsValid(object? value)
    {
        if (value == null || value is not string stringValue)
            return true;

        // Remove common formatting characters
        var cleanedNumber = stringValue.Replace(" ", "").Replace("-", "").Replace("(", "").Replace(")", "");
        
        return PhonePattern.IsMatch(cleanedNumber);
    }

    public override string FormatErrorMessage(string name)
    {
        return $"The field {name} must be a valid phone number.";
    }
}

/// <summary>
/// Validates that a date is not in the past
/// </summary>
public class FutureDateAttribute : ValidationAttribute
{
    public override bool IsValid(object? value)
    {
        if (value == null)
            return true;

        if (value is DateTime dateTime)
            return dateTime > DateTime.UtcNow;

        if (value is DateTimeOffset dateTimeOffset)
            return dateTimeOffset > DateTimeOffset.UtcNow;

        return false;
    }

    public override string FormatErrorMessage(string name)
    {
        return $"The field {name} must be a future date.";
    }
}

/// <summary>
/// Validates that a date is within a reasonable range
/// </summary>
public class ReasonableDateRangeAttribute : ValidationAttribute
{
    public int MaxDaysInFuture { get; set; } = 365;
    public int MaxDaysInPast { get; set; } = 30;

    public override bool IsValid(object? value)
    {
        if (value == null)
            return true;

        DateTime dateToValidate;
        
        if (value is DateTime dateTime)
            dateToValidate = dateTime;
        else if (value is DateTimeOffset dateTimeOffset)
            dateToValidate = dateTimeOffset.DateTime;
        else
            return false;

        var now = DateTime.UtcNow;
        var minDate = now.AddDays(-MaxDaysInPast);
        var maxDate = now.AddDays(MaxDaysInFuture);

        return dateToValidate >= minDate && dateToValidate <= maxDate;
    }

    public override string FormatErrorMessage(string name)
    {
        return $"The field {name} must be within {MaxDaysInPast} days in the past and {MaxDaysInFuture} days in the future.";
    }
}

/// <summary>
/// Validates coordinate values (latitude/longitude)
/// </summary>
public class CoordinateAttribute : ValidationAttribute
{
    public CoordinateType Type { get; set; }

    public override bool IsValid(object? value)
    {
        if (value == null)
            return true;

        if (value is not double coordinate)
            return false;

        return Type switch
        {
            CoordinateType.Latitude => coordinate >= -90.0 && coordinate <= 90.0,
            CoordinateType.Longitude => coordinate >= -180.0 && coordinate <= 180.0,
            _ => false
        };
    }

    public override string FormatErrorMessage(string name)
    {
        return Type switch
        {
            CoordinateType.Latitude => $"The field {name} must be a valid latitude (-90 to 90).",
            CoordinateType.Longitude => $"The field {name} must be a valid longitude (-180 to 180).",
            _ => $"The field {name} must be a valid coordinate."
        };
    }
}

public enum CoordinateType
{
    Latitude,
    Longitude
}

/// <summary>
/// Validates file size
/// </summary>
public class FileSizeAttribute : ValidationAttribute
{
    public long MaxSizeBytes { get; set; }

    public override bool IsValid(object? value)
    {
        if (value == null)
            return true;

        if (value is IFormFile file)
            return file.Length <= MaxSizeBytes;

        return false;
    }

    public override string FormatErrorMessage(string name)
    {
        var maxSizeMB = MaxSizeBytes / (1024.0 * 1024.0);
        return $"The field {name} must not exceed {maxSizeMB:F1} MB.";
    }
}

/// <summary>
/// Validates file type by extension
/// </summary>
public class AllowedFileTypesAttribute : ValidationAttribute
{
    public string[] AllowedExtensions { get; set; } = Array.Empty<string>();

    public override bool IsValid(object? value)
    {
        if (value == null)
            return true;

        if (value is IFormFile file)
        {
            var extension = Path.GetExtension(file.FileName)?.ToLowerInvariant();
            return !string.IsNullOrEmpty(extension) && AllowedExtensions.Contains(extension);
        }

        return false;
    }

    public override string FormatErrorMessage(string name)
    {
        return $"The field {name} must be one of the following file types: {string.Join(", ", AllowedExtensions)}.";
    }
}
