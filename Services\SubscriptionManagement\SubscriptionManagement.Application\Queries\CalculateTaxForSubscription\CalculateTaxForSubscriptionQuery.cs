using MediatR;
using SubscriptionManagement.Application.DTOs;

namespace SubscriptionManagement.Application.Queries.CalculateTaxForSubscription
{
    public class CalculateTaxForSubscriptionQuery : IRequest<TaxCalculationResponseDto>
    {
        public Guid PlanId { get; set; }
        public string CustomerRegion { get; set; } = string.Empty;
        public Guid? CustomerId { get; set; }
        public DateTime? CalculationDate { get; set; }
        public bool IncludeTaxBreakdown { get; set; } = true;
        public decimal? CustomAmount { get; set; }
        public string? CustomCurrency { get; set; }
    }
}
