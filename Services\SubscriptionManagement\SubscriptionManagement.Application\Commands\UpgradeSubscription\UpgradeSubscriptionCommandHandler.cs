using MediatR;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Exceptions;
using SubscriptionManagement.Domain.Enums;
using Shared.Messaging;

namespace SubscriptionManagement.Application.Commands.UpgradeSubscription
{
    public class UpgradeSubscriptionCommandHandler : IRequestHandler<UpgradeSubscriptionCommand, bool>
    {
        private readonly ISubscriptionRepository _subscriptionRepository;
        private readonly IPlanRepository _planRepository;
        private readonly IPaymentService _paymentService;
        private readonly IMessageBroker _messageBroker;
        private readonly ILogger<UpgradeSubscriptionCommandHandler> _logger;

        public UpgradeSubscriptionCommandHandler(
            ISubscriptionRepository subscriptionRepository,
            IPlanRepository planRepository,
            IPaymentService paymentService,
            IMessageBroker messageBroker,
            ILogger<UpgradeSubscriptionCommandHandler> logger)
        {
            _subscriptionRepository = subscriptionRepository;
            _planRepository = planRepository;
            _paymentService = paymentService;
            _messageBroker = messageBroker;
            _logger = logger;
        }

        public async Task<bool> Handle(UpgradeSubscriptionCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Upgrading subscription {SubscriptionId} to plan {NewPlanId} for user {UserId}", 
                request.SubscriptionId, request.NewPlanId, request.UserId);

            // Get current subscription
            var subscription = await _subscriptionRepository.GetByIdAsync(request.SubscriptionId);
            if (subscription == null)
            {
                throw new SubscriptionDomainException("Subscription not found");
            }

            // Authorization check
            if (subscription.UserId != request.UserId)
            {
                throw new SubscriptionDomainException("You can only upgrade your own subscription");
            }

            // Validate subscription can be upgraded
            if (subscription.Status != SubscriptionStatus.Active)
            {
                throw new SubscriptionDomainException("Only active subscriptions can be upgraded");
            }

            // Get new plan
            var newPlan = await _planRepository.GetByIdAsync(request.NewPlanId);
            if (newPlan == null)
            {
                throw new SubscriptionDomainException("New plan not found");
            }

            if (!newPlan.IsActive)
            {
                throw new SubscriptionDomainException("Cannot upgrade to inactive plan");
            }

            // Validate it's actually an upgrade (higher price)
            if (newPlan.Price.Amount <= subscription.CurrentPrice.Amount)
            {
                throw new SubscriptionDomainException("New plan must have a higher price than current plan for upgrade");
            }

            var oldPlan = await _planRepository.GetByIdAsync(subscription.PlanId);
            
            // Calculate proration if needed
            decimal prorationAmount = 0;
            if (request.ProrationMode == ProrationMode.CreateProrations)
            {
                prorationAmount = CalculateUpgradeProration(subscription, newPlan);
            }

            // Process payment for proration if required
            if (prorationAmount > 0 && !string.IsNullOrEmpty(request.PaymentMethodId))
            {
                var paymentResult = await _paymentService.ProcessProrationPaymentAsync(
                    subscription, prorationAmount, request.PaymentMethodId);

                if (!paymentResult.IsSuccess)
                {
                    _logger.LogWarning("Proration payment failed for subscription {SubscriptionId}: {Error}", 
                        request.SubscriptionId, paymentResult.ErrorMessage);
                    throw new SubscriptionDomainException($"Upgrade failed: {paymentResult.ErrorMessage}");
                }
            }

            // Perform the upgrade
            subscription.UpgradeToPlan(newPlan, request.ImmediateUpgrade, request.ProrationMode);
            
            // Save changes
            await _subscriptionRepository.UpdateAsync(subscription);

            // Publish upgrade event
            await _messageBroker.PublishAsync("subscription.upgraded", new
            {
                SubscriptionId = subscription.Id,
                UserId = subscription.UserId,
                OldPlanId = oldPlan?.Id,
                OldPlanName = oldPlan?.Name,
                NewPlanId = newPlan.Id,
                NewPlanName = newPlan.Name,
                ProrationAmount = prorationAmount,
                UpgradeReason = request.UpgradeReason,
                ImmediateUpgrade = request.ImmediateUpgrade,
                UpgradedAt = DateTime.UtcNow,
                NewPrice = subscription.CurrentPrice.Amount,
                Currency = subscription.CurrentPrice.Currency
            });

            _logger.LogInformation("Successfully upgraded subscription {SubscriptionId} from plan {OldPlanId} to {NewPlanId}", 
                request.SubscriptionId, oldPlan?.Id, request.NewPlanId);

            return true;
        }

        private decimal CalculateUpgradeProration(Subscription subscription, Plan newPlan)
        {
            var now = DateTime.UtcNow;
            var daysInCurrentPeriod = (subscription.NextBillingDate - subscription.StartDate).Days;
            var daysRemaining = (subscription.NextBillingDate - now).Days;
            
            if (daysRemaining <= 0) return 0;

            var currentPlanDailyRate = subscription.CurrentPrice.Amount / daysInCurrentPeriod;
            var newPlanDailyRate = newPlan.Price.Amount / daysInCurrentPeriod;
            
            return (newPlanDailyRate - currentPlanDailyRate) * daysRemaining;
        }
    }
}
