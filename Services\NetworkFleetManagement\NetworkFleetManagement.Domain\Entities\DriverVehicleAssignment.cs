using Shared.Domain.Common;

namespace NetworkFleetManagement.Domain.Entities;

public class DriverVehicleAssignment : BaseEntity
{
    public Guid DriverId { get; private set; }
    public Guid VehicleId { get; private set; }
    public DateTime AssignedAt { get; private set; }
    public DateTime? UnassignedAt { get; private set; }
    public bool IsActive { get; private set; }
    public string? AssignmentNotes { get; private set; }
    public string? UnassignmentReason { get; private set; }
    public Guid AssignedBy { get; private set; }
    public Guid? UnassignedBy { get; private set; }

    // Navigation properties
    public Driver Driver { get; private set; } = null!;
    public Vehicle Vehicle { get; private set; } = null!;

    // Parameterless constructor for EF Core
    private DriverVehicleAssignment() { }

    public DriverVehicleAssignment(
        Guid driverId,
        Guid vehicleId,
        Guid assignedBy,
        string? assignmentNotes = null)
    {
        DriverId = driverId;
        VehicleId = vehicleId;
        AssignedAt = DateTime.UtcNow;
        IsActive = true;
        AssignmentNotes = assignmentNotes;
        AssignedBy = assignedBy;
    }

    public void Unassign(Guid unassignedBy, string? reason = null)
    {
        if (!IsActive)
            throw new InvalidOperationException("Assignment is already inactive");

        IsActive = false;
        UnassignedAt = DateTime.UtcNow;
        UnassignmentReason = reason;
        UnassignedBy = unassignedBy;
        SetUpdatedAt();
    }

    public TimeSpan? AssignmentDuration => 
        UnassignedAt.HasValue ? UnassignedAt.Value - AssignedAt : DateTime.UtcNow - AssignedAt;
}
