using MediatR;
using Microsoft.Extensions.Logging;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Application.Interfaces;

namespace NetworkFleetManagement.Application.Commands.GeneratePartnerPerformanceAnalytics;

public class GeneratePartnerPerformanceAnalyticsCommandHandler : IRequestHandler<GeneratePartnerPerformanceAnalyticsCommand, PartnerPerformanceAnalyticsDto>
{
    private readonly IPreferredPartnerRepository _preferredPartnerRepository;
    private readonly INetworkPerformanceRepository _performanceRepository;
    private readonly IAnalyticsService _analyticsService;
    private readonly ILogger<GeneratePartnerPerformanceAnalyticsCommandHandler> _logger;

    public GeneratePartnerPerformanceAnalyticsCommandHandler(
        IPreferredPartnerRepository preferredPartnerRepository,
        INetworkPerformanceRepository performanceRepository,
        IAnalyticsService analyticsService,
        ILogger<GeneratePartnerPerformanceAnalyticsCommandHandler> logger)
    {
        _preferredPartnerRepository = preferredPartnerRepository;
        _performanceRepository = performanceRepository;
        _analyticsService = analyticsService;
        _logger = logger;
    }

    public async Task<PartnerPerformanceAnalyticsDto> Handle(GeneratePartnerPerformanceAnalyticsCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Generating partner performance analytics for user {UserId} with scope {Scope}", 
            request.UserId, request.Scope);

        try
        {
            // Validate user permissions
            await ValidateUserPermissions(request.UserId, request.RequestingUserId);

            // Get date range
            var fromDate = request.FromDate ?? DateTime.UtcNow.AddDays(-90);
            var toDate = request.ToDate ?? DateTime.UtcNow;

            // Get partners based on scope
            var partners = await GetPartnersForAnalysis(request, cancellationToken);

            // Generate analytics
            var analytics = new PartnerPerformanceAnalyticsDto
            {
                UserId = request.UserId,
                GeneratedAt = DateTime.UtcNow,
                AnalysisPeriod = new DateRangeDto { StartDate = fromDate, EndDate = toDate },
                Scope = request.Scope.ToString(),
                TotalPartnersAnalyzed = partners.Count
            };

            // Calculate overall metrics
            analytics.OverallMetrics = await CalculateOverallMetrics(partners, fromDate, toDate, cancellationToken);

            // Generate partner performance summaries
            analytics.PartnerPerformances = await GeneratePartnerPerformanceSummaries(partners, fromDate, toDate, request.Metrics, cancellationToken);

            // Generate performance trends
            analytics.PerformanceTrends = await GeneratePerformanceTrends(partners, fromDate, toDate, cancellationToken);

            // Generate benchmarking data
            if (request.IncludeBenchmarking)
            {
                analytics.BenchmarkingData = await GenerateBenchmarkingData(partners, request.PartnerType, cancellationToken);
            }

            // Generate predictive insights
            if (request.IncludePredictiveInsights)
            {
                analytics.PredictiveInsights = await GeneratePredictiveInsights(partners, cancellationToken);
            }

            // Generate recommendations
            if (request.IncludeRecommendations)
            {
                analytics.Recommendations = await GenerateRecommendations(partners, analytics.OverallMetrics, cancellationToken);
            }

            // Identify top and underperforming partners
            analytics.TopPerformers = analytics.PartnerPerformances
                .OrderByDescending(p => p.OverallScore)
                .Take(5)
                .ToList();

            analytics.UnderPerformers = analytics.PartnerPerformances
                .OrderBy(p => p.OverallScore)
                .Where(p => p.OverallScore < 3.0m)
                .Take(5)
                .ToList();

            _logger.LogInformation("Successfully generated analytics for {PartnerCount} partners for user {UserId}", 
                partners.Count, request.UserId);

            return analytics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating partner performance analytics for user {UserId}", request.UserId);
            throw;
        }
    }

    private async Task ValidateUserPermissions(Guid userId, Guid requestingUserId)
    {
        if (userId != requestingUserId)
        {
            throw new UnauthorizedAccessException("You do not have permission to generate analytics for this user");
        }
    }

    private async Task<List<Domain.Entities.PreferredPartner>> GetPartnersForAnalysis(
        GeneratePartnerPerformanceAnalyticsCommand request, 
        CancellationToken cancellationToken)
    {
        if (request.SpecificPartnerIds?.Any() == true)
        {
            return await _preferredPartnerRepository.GetByPartnerIdsAsync(request.SpecificPartnerIds, cancellationToken);
        }

        return request.Scope switch
        {
            AnalyticsScope.PreferredPartnersOnly => await _preferredPartnerRepository.GetActiveByUserIdAsync(request.UserId, cancellationToken),
            AnalyticsScope.ActivePartnersOnly => await _preferredPartnerRepository.GetActiveByUserIdAsync(request.UserId, cancellationToken),
            AnalyticsScope.TopPerformers => await _preferredPartnerRepository.GetTopPerformersByUserIdAsync(request.UserId, 10, cancellationToken),
            AnalyticsScope.UnderPerformers => await _preferredPartnerRepository.GetUnderPerformersByUserIdAsync(request.UserId, 10, cancellationToken),
            _ => await _preferredPartnerRepository.GetByUserIdAsync(request.UserId, cancellationToken)
        };
    }

    private async Task<OverallPerformanceMetricsDto> CalculateOverallMetrics(
        List<Domain.Entities.PreferredPartner> partners,
        DateTime fromDate,
        DateTime toDate,
        CancellationToken cancellationToken)
    {
        var metrics = new OverallPerformanceMetricsDto();

        if (!partners.Any())
            return metrics;

        // Calculate aggregate metrics
        metrics.AverageOnTimeDeliveryRate = partners.Average(p => p.PerformanceMetrics.OnTimeDeliveryRate);
        metrics.AverageCustomerSatisfactionScore = partners.Average(p => p.PerformanceMetrics.CustomerSatisfactionScore);
        metrics.AverageCommunicationRating = partners.Average(p => p.PerformanceMetrics.CommunicationRating);
        metrics.AverageSafetyRating = partners.Average(p => p.PerformanceMetrics.SafetyRating);
        metrics.AverageResponseTimeHours = partners.Average(p => p.PerformanceMetrics.AverageResponseTimeHours);

        // Calculate totals
        metrics.TotalTripsCompleted = partners.Sum(p => p.PerformanceMetrics.TotalTripsCompleted);
        metrics.TotalRevenue = partners.Sum(p => p.PerformanceMetrics.TotalRevenue);

        // Calculate rates
        var totalTrips = partners.Sum(p => p.PerformanceMetrics.TotalTripsAssigned);
        metrics.OverallCompletionRate = totalTrips > 0 ? (decimal)metrics.TotalTripsCompleted / totalTrips * 100 : 0;

        // Calculate performance distribution
        metrics.HighPerformersCount = partners.Count(p => p.PerformanceMetrics.OverallRating >= 4.0m);
        metrics.MediumPerformersCount = partners.Count(p => p.PerformanceMetrics.OverallRating >= 3.0m && p.PerformanceMetrics.OverallRating < 4.0m);
        metrics.LowPerformersCount = partners.Count(p => p.PerformanceMetrics.OverallRating < 3.0m);

        return metrics;
    }

    private async Task<List<PartnerPerformanceSummaryDto>> GeneratePartnerPerformanceSummaries(
        List<Domain.Entities.PreferredPartner> partners,
        DateTime fromDate,
        DateTime toDate,
        List<PerformanceMetric> requestedMetrics,
        CancellationToken cancellationToken)
    {
        var summaries = new List<PartnerPerformanceSummaryDto>();

        foreach (var partner in partners)
        {
            var summary = new PartnerPerformanceSummaryDto
            {
                PartnerId = partner.PartnerId,
                PartnerName = "Partner Name", // Would need to fetch from external service
                PartnerType = partner.PartnerType.ToString(),
                PreferenceLevel = partner.PreferenceLevel.ToString(),
                Priority = partner.Priority,
                OverallScore = partner.PerformanceMetrics.OverallRating,
                OnTimeDeliveryRate = partner.PerformanceMetrics.OnTimeDeliveryRate,
                CustomerSatisfactionScore = partner.PerformanceMetrics.CustomerSatisfactionScore,
                CommunicationRating = partner.PerformanceMetrics.CommunicationRating,
                SafetyRating = partner.PerformanceMetrics.SafetyRating,
                TotalTripsCompleted = partner.PerformanceMetrics.TotalTripsCompleted,
                TotalRevenue = partner.PerformanceMetrics.TotalRevenue,
                AverageResponseTimeHours = partner.PerformanceMetrics.AverageResponseTimeHours,
                LastPerformanceUpdate = partner.PerformanceMetrics.LastUpdated,
                IsPreferredPartner = true,
                PerformanceTrend = CalculatePerformanceTrend(partner),
                RiskLevel = CalculateRiskLevel(partner)
            };

            summaries.Add(summary);
        }

        return summaries.OrderByDescending(s => s.OverallScore).ToList();
    }

    private async Task<List<PerformanceTrendDto>> GeneratePerformanceTrends(
        List<Domain.Entities.PreferredPartner> partners,
        DateTime fromDate,
        DateTime toDate,
        CancellationToken cancellationToken)
    {
        // This would typically involve time-series analysis of performance data
        // For now, returning placeholder data
        return new List<PerformanceTrendDto>();
    }

    private async Task<BenchmarkingDataDto> GenerateBenchmarkingData(
        List<Domain.Entities.PreferredPartner> partners,
        Domain.Enums.PreferredPartnerType? partnerType,
        CancellationToken cancellationToken)
    {
        // This would involve comparing against industry benchmarks
        return new BenchmarkingDataDto();
    }

    private async Task<List<PredictiveInsightDto>> GeneratePredictiveInsights(
        List<Domain.Entities.PreferredPartner> partners,
        CancellationToken cancellationToken)
    {
        // This would involve ML-based predictive analytics
        return new List<PredictiveInsightDto>();
    }

    private async Task<List<PartnerRecommendationDto>> GenerateRecommendations(
        List<Domain.Entities.PreferredPartner> partners,
        OverallPerformanceMetricsDto overallMetrics,
        CancellationToken cancellationToken)
    {
        var recommendations = new List<PartnerRecommendationDto>();

        // Generate recommendations based on performance data
        var underPerformers = partners.Where(p => p.PerformanceMetrics.OverallRating < 3.0m).ToList();
        if (underPerformers.Any())
        {
            recommendations.Add(new PartnerRecommendationDto
            {
                Type = "Performance Improvement",
                Title = "Review Underperforming Partners",
                Description = $"Consider reviewing {underPerformers.Count} partners with ratings below 3.0",
                Priority = "High",
                ActionRequired = true,
                AffectedPartnerIds = underPerformers.Select(p => p.PartnerId).ToList()
            });
        }

        return recommendations;
    }

    private string CalculatePerformanceTrend(Domain.Entities.PreferredPartner partner)
    {
        // This would involve analyzing historical performance data
        return "Stable"; // Placeholder
    }

    private string CalculateRiskLevel(Domain.Entities.PreferredPartner partner)
    {
        if (partner.PerformanceMetrics.OverallRating < 2.5m)
            return "High";
        else if (partner.PerformanceMetrics.OverallRating < 3.5m)
            return "Medium";
        else
            return "Low";
    }
}
