using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetworkFleetManagement.Domain.Entities;

namespace NetworkFleetManagement.Infrastructure.Persistence.Configurations;

public class CarrierDocumentConfiguration : IEntityTypeConfiguration<CarrierDocument>
{
    public void Configure(EntityTypeBuilder<CarrierDocument> builder)
    {
        builder.ToTable("carrier_documents");

        builder.HasKey(d => d.Id);

        builder.Property(d => d.Id)
            .ValueGeneratedNever();

        builder.Property(d => d.CarrierId)
            .IsRequired();

        builder.Property(d => d.DocumentType)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(d => d.DocumentName)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(d => d.DocumentNumber)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(d => d.FilePath)
            .IsRequired()
            .HasMaxLength(500);

        builder.Property(d => d.FileUrl)
            .HasMaxLength(500);

        builder.Property(d => d.ExpiryDate);

        builder.Property(d => d.IsVerified)
            .IsRequired();

        builder.Property(d => d.VerifiedAt);

        builder.Property(d => d.VerifiedBy);

        builder.Property(d => d.VerificationNotes)
            .HasMaxLength(500);

        builder.Property(d => d.RejectionReason)
            .HasMaxLength(500);

        builder.Property(d => d.FileSizeBytes)
            .IsRequired();

        builder.Property(d => d.MimeType)
            .HasMaxLength(100);

        builder.Property(d => d.CreatedAt)
            .IsRequired();

        builder.Property(d => d.UpdatedAt);

        // Indexes
        builder.HasIndex(d => d.CarrierId);

        builder.HasIndex(d => d.DocumentType);

        builder.HasIndex(d => d.IsVerified);

        builder.HasIndex(d => d.ExpiryDate);

        builder.HasIndex(d => new { d.CarrierId, d.DocumentType });
    }
}

public class VehicleDocumentConfiguration : IEntityTypeConfiguration<VehicleDocument>
{
    public void Configure(EntityTypeBuilder<VehicleDocument> builder)
    {
        builder.ToTable("vehicle_documents");

        builder.HasKey(d => d.Id);

        builder.Property(d => d.Id)
            .ValueGeneratedNever();

        builder.Property(d => d.VehicleId)
            .IsRequired();

        builder.Property(d => d.DocumentType)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(d => d.DocumentName)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(d => d.DocumentNumber)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(d => d.FilePath)
            .IsRequired()
            .HasMaxLength(500);

        builder.Property(d => d.FileUrl)
            .HasMaxLength(500);

        builder.Property(d => d.ExpiryDate);

        builder.Property(d => d.IsVerified)
            .IsRequired();

        builder.Property(d => d.VerifiedAt);

        builder.Property(d => d.VerifiedBy);

        builder.Property(d => d.VerificationNotes)
            .HasMaxLength(500);

        builder.Property(d => d.RejectionReason)
            .HasMaxLength(500);

        builder.Property(d => d.FileSizeBytes)
            .IsRequired();

        builder.Property(d => d.MimeType)
            .HasMaxLength(100);

        builder.Property(d => d.CreatedAt)
            .IsRequired();

        builder.Property(d => d.UpdatedAt);

        // Indexes
        builder.HasIndex(d => d.VehicleId);

        builder.HasIndex(d => d.DocumentType);

        builder.HasIndex(d => d.IsVerified);

        builder.HasIndex(d => d.ExpiryDate);

        builder.HasIndex(d => new { d.VehicleId, d.DocumentType });
    }
}

public class DriverDocumentConfiguration : IEntityTypeConfiguration<DriverDocument>
{
    public void Configure(EntityTypeBuilder<DriverDocument> builder)
    {
        builder.ToTable("driver_documents");

        builder.HasKey(d => d.Id);

        builder.Property(d => d.Id)
            .ValueGeneratedNever();

        builder.Property(d => d.DriverId)
            .IsRequired();

        builder.Property(d => d.DocumentType)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(d => d.DocumentName)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(d => d.DocumentNumber)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(d => d.FilePath)
            .IsRequired()
            .HasMaxLength(500);

        builder.Property(d => d.FileUrl)
            .HasMaxLength(500);

        builder.Property(d => d.ExpiryDate);

        builder.Property(d => d.IsVerified)
            .IsRequired();

        builder.Property(d => d.VerifiedAt);

        builder.Property(d => d.VerifiedBy);

        builder.Property(d => d.VerificationNotes)
            .HasMaxLength(500);

        builder.Property(d => d.RejectionReason)
            .HasMaxLength(500);

        builder.Property(d => d.FileSizeBytes)
            .IsRequired();

        builder.Property(d => d.MimeType)
            .HasMaxLength(100);

        builder.Property(d => d.CreatedAt)
            .IsRequired();

        builder.Property(d => d.UpdatedAt);

        // Indexes
        builder.HasIndex(d => d.DriverId);

        builder.HasIndex(d => d.DocumentType);

        builder.HasIndex(d => d.IsVerified);

        builder.HasIndex(d => d.ExpiryDate);

        builder.HasIndex(d => new { d.DriverId, d.DocumentType });
    }
}
