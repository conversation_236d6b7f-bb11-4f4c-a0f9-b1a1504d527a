using NetworkFleetManagement.Domain.Entities;
using NetworkFleetManagement.Domain.Enums;

namespace NetworkFleetManagement.Domain.Repositories;

public interface IVehicleRepository
{
    Task<Vehicle?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<Vehicle?> GetByRegistrationNumberAsync(string registrationNumber, CancellationToken cancellationToken = default);
    Task<IEnumerable<Vehicle>> GetByCarrierIdAsync(Guid carrierId, CancellationToken cancellationToken = default);
    Task<IEnumerable<Vehicle>> GetByStatusAsync(VehicleStatus status, CancellationToken cancellationToken = default);
    Task<IEnumerable<Vehicle>> GetAvailableVehiclesAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<Vehicle>> GetVehiclesRequiringMaintenanceAsync(int daysThreshold = 7, CancellationToken cancellationToken = default);
    Task<IEnumerable<Vehicle>> GetVehiclesWithExpiredDocumentsAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<Vehicle>> GetVehiclesByTypeAsync(VehicleType vehicleType, CancellationToken cancellationToken = default);
    Task<IEnumerable<Vehicle>> SearchAsync(string searchTerm, CancellationToken cancellationToken = default);
    Task<(IEnumerable<Vehicle> Vehicles, int TotalCount)> GetPagedAsync(
        int pageNumber, 
        int pageSize, 
        Guid? carrierId = null,
        VehicleStatus? status = null,
        VehicleType? vehicleType = null,
        string? searchTerm = null,
        CancellationToken cancellationToken = default);
    
    void Add(Vehicle vehicle);
    void Update(Vehicle vehicle);
    void Remove(Vehicle vehicle);
}
