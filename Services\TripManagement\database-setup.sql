-- Trip Management Service Database Setup
-- PostgreSQL/TimescaleDB Database Setup Script

-- Create database (run as superuser)
-- CREATE DATABASE "TLI_TripManagement" WITH OWNER = timescale;

-- Connect to the database
\c TLI_TripManagement;

-- Create schema
CREATE SCHEMA IF NOT EXISTS trip_management;

-- Set default schema
SET search_path TO trip_management;

-- <PERSON>reate enum types
CREATE TYPE trip_status AS ENUM (
    'Created',
    'Assigned', 
    'InProgress',
    'Completed',
    'Cancelled',
    'OnHold',
    'Exception'
);

CREATE TYPE trip_stop_type AS ENUM (
    'Pickup',
    'Delivery',
    'Waypoint',
    'RestStop'
);

CREATE TYPE trip_stop_status AS ENUM (
    'Pending',
    'Arrived',
    'InProgress',
    'Completed',
    'Skipped',
    'Failed'
);

CREATE TYPE vehicle_type AS ENUM (
    'Truck',
    'Van',
    'Trailer',
    'Container',
    'Flatbed',
    'Refrigerated',
    'Tanker',
    'Other'
);

CREATE TYPE driver_status AS ENUM (
    'Available',
    'OnTrip',
    'OffDuty',
    'Unavailable',
    'Suspended'
);

CREATE TYPE vehicle_status AS ENUM (
    'Available',
    'InUse',
    'Maintenance',
    'OutOfService',
    'Retired'
);

CREATE TYPE document_type AS ENUM (
    'ProofOfDelivery',
    'BillOfLading',
    'Invoice',
    'Photo',
    'Signature',
    'DamageReport',
    'Other'
);

CREATE TYPE exception_type AS ENUM (
    'Delay',
    'Breakdown',
    'Accident',
    'WeatherDelay',
    'TrafficDelay',
    'CustomerUnavailable',
    'LoadDamage',
    'RouteDeviation',
    'FuelIssue',
    'DriverIssue',
    'Other'
);

CREATE TYPE location_update_source AS ENUM (
    'GPS',
    'Manual',
    'Geofence',
    'Mobile',
    'Telematics'
);

-- Enable TimescaleDB extension (if using TimescaleDB)
-- CREATE EXTENSION IF NOT EXISTS timescaledb;

-- Enable PostGIS extension for location data (optional)
-- CREATE EXTENSION IF NOT EXISTS postgis;

-- Create indexes for performance
-- These will be created automatically by Entity Framework migrations
-- but are listed here for reference

-- Indexes for trips table
-- CREATE INDEX idx_trips_order_id ON trips(order_id);
-- CREATE INDEX idx_trips_carrier_id ON trips(carrier_id);
-- CREATE INDEX idx_trips_driver_id ON trips(driver_id);
-- CREATE INDEX idx_trips_vehicle_id ON trips(vehicle_id);
-- CREATE INDEX idx_trips_status ON trips(status);
-- CREATE INDEX idx_trips_created_at ON trips(created_at);
-- CREATE INDEX idx_trips_estimated_start_time ON trips(estimated_start_time);

-- Indexes for trip_location_updates table (time-series data)
-- CREATE INDEX idx_trip_location_updates_trip_id ON trip_location_updates(trip_id);
-- CREATE INDEX idx_trip_location_updates_timestamp ON trip_location_updates(timestamp);

-- If using TimescaleDB, convert location updates to hypertable
-- SELECT create_hypertable('trip_location_updates', 'timestamp');

-- Create retention policy for location updates (keep 1 year)
-- SELECT add_retention_policy('trip_location_updates', INTERVAL '1 year');

-- Grant permissions
GRANT USAGE ON SCHEMA trip_management TO timescale;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA trip_management TO timescale;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA trip_management TO timescale;

-- Set default privileges for future tables
ALTER DEFAULT PRIVILEGES IN SCHEMA trip_management GRANT ALL ON TABLES TO timescale;
ALTER DEFAULT PRIVILEGES IN SCHEMA trip_management GRANT ALL ON SEQUENCES TO timescale;

-- Sample data (optional - for development/testing)
-- This will be handled by the application, but included for reference

/*
-- Sample driver
INSERT INTO drivers (id, user_id, first_name, last_name, phone_number, email, license_number, license_expiry_date, status, created_at, rating, total_trips, completed_trips)
VALUES (
    gen_random_uuid(),
    gen_random_uuid(),
    'John',
    'Doe',
    '+**********',
    '<EMAIL>',
    'DL123456789',
    '2025-12-31',
    'Available',
    NOW(),
    4.5,
    0,
    0
);

-- Sample vehicle
INSERT INTO vehicles (id, carrier_id, registration_number, vehicle_type, make, model, year, color, load_capacity_kg, volume_capacity_m3, status, created_at)
VALUES (
    gen_random_uuid(),
    gen_random_uuid(),
    'ABC-1234',
    'Truck',
    'Tata',
    'LPT 1618',
    2022,
    'White',
    10000.00,
    50.00,
    'Available',
    NOW()
);
*/

-- Performance optimization settings
-- Adjust these based on your specific requirements and hardware

-- For time-series data optimization
-- SET timescaledb.max_background_workers = 8;
-- SET shared_preload_libraries = 'timescaledb';

-- For general PostgreSQL optimization
-- SET work_mem = '256MB';
-- SET maintenance_work_mem = '1GB';
-- SET effective_cache_size = '4GB';
-- SET random_page_cost = 1.1;

COMMIT;
