using Newtonsoft.Json;

namespace CommunicationNotification.Infrastructure.Models;

/// <summary>
/// WhatsApp API response model
/// </summary>
public class WhatsAppApiResponse
{
    [JsonProperty("messaging_product")]
    public string? MessagingProduct { get; set; }

    [JsonProperty("contacts")]
    public List<WhatsAppContact>? Contacts { get; set; }

    [JsonProperty("messages")]
    public List<WhatsAppMessage>? Messages { get; set; }
}

/// <summary>
/// WhatsApp contact model
/// </summary>
public class WhatsAppContact
{
    [JsonProperty("input")]
    public string? Input { get; set; }

    [JsonProperty("wa_id")]
    public string? WhatsAppId { get; set; }
}

/// <summary>
/// WhatsApp message model
/// </summary>
public class WhatsAppMessage
{
    [JsonProperty("id")]
    public string? Id { get; set; }

    [JsonProperty("message_status")]
    public string? MessageStatus { get; set; }
}

/// <summary>
/// WhatsApp API error response
/// </summary>
public class WhatsAppApiError
{
    [JsonProperty("error")]
    public WhatsAppError? Error { get; set; }
}

/// <summary>
/// WhatsApp error details
/// </summary>
public class WhatsAppError
{
    [JsonProperty("message")]
    public string? Message { get; set; }

    [JsonProperty("type")]
    public string? Type { get; set; }

    [JsonProperty("code")]
    public int? Code { get; set; }

    [JsonProperty("error_subcode")]
    public int? ErrorSubcode { get; set; }

    [JsonProperty("fbtrace_id")]
    public string? FbTraceId { get; set; }
}

/// <summary>
/// WhatsApp message status response
/// </summary>
public class WhatsAppMessageStatusResponse
{
    [JsonProperty("id")]
    public string? Id { get; set; }

    [JsonProperty("to")]
    public string? To { get; set; }

    [JsonProperty("status")]
    public string? Status { get; set; }

    [JsonProperty("timestamp")]
    public long? Timestamp { get; set; }

    [JsonProperty("delivered_at")]
    public DateTime? DeliveredAt { get; set; }

    [JsonProperty("read_at")]
    public DateTime? ReadAt { get; set; }

    [JsonProperty("error")]
    public WhatsAppError? Error { get; set; }
}

/// <summary>
/// WhatsApp templates response
/// </summary>
public class WhatsAppTemplatesResponse
{
    [JsonProperty("data")]
    public List<WhatsAppTemplateData>? Data { get; set; }

    [JsonProperty("paging")]
    public WhatsAppPaging? Paging { get; set; }
}

/// <summary>
/// WhatsApp template data
/// </summary>
public class WhatsAppTemplateData
{
    [JsonProperty("name")]
    public string? Name { get; set; }

    [JsonProperty("components")]
    public List<WhatsAppTemplateComponentData>? Components { get; set; }

    [JsonProperty("language")]
    public string? Language { get; set; }

    [JsonProperty("status")]
    public string? Status { get; set; }

    [JsonProperty("category")]
    public string? Category { get; set; }

    [JsonProperty("id")]
    public string? Id { get; set; }
}

/// <summary>
/// WhatsApp template component data
/// </summary>
public class WhatsAppTemplateComponentData
{
    [JsonProperty("type")]
    public string? Type { get; set; }

    [JsonProperty("text")]
    public string? Text { get; set; }

    [JsonProperty("parameters")]
    public List<WhatsAppTemplateParameterData>? Parameters { get; set; }
}

/// <summary>
/// WhatsApp template parameter data
/// </summary>
public class WhatsAppTemplateParameterData
{
    [JsonProperty("type")]
    public string? Type { get; set; }

    [JsonProperty("text")]
    public string? Value { get; set; }
}

/// <summary>
/// WhatsApp paging information
/// </summary>
public class WhatsAppPaging
{
    [JsonProperty("cursors")]
    public WhatsAppCursors? Cursors { get; set; }

    [JsonProperty("next")]
    public string? Next { get; set; }
}

/// <summary>
/// WhatsApp cursors for paging
/// </summary>
public class WhatsAppCursors
{
    [JsonProperty("before")]
    public string? Before { get; set; }

    [JsonProperty("after")]
    public string? After { get; set; }
}

/// <summary>
/// WhatsApp webhook payload
/// </summary>
public class WhatsAppWebhookPayload
{
    [JsonProperty("object")]
    public string? Object { get; set; }

    [JsonProperty("entry")]
    public List<WhatsAppWebhookEntry>? Entry { get; set; }
}

/// <summary>
/// WhatsApp webhook entry
/// </summary>
public class WhatsAppWebhookEntry
{
    [JsonProperty("id")]
    public string? Id { get; set; }

    [JsonProperty("changes")]
    public List<WhatsAppWebhookChange>? Changes { get; set; }
}

/// <summary>
/// WhatsApp webhook change
/// </summary>
public class WhatsAppWebhookChange
{
    [JsonProperty("value")]
    public WhatsAppWebhookValue? Value { get; set; }

    [JsonProperty("field")]
    public string? Field { get; set; }
}

/// <summary>
/// WhatsApp webhook value
/// </summary>
public class WhatsAppWebhookValue
{
    [JsonProperty("messaging_product")]
    public string? MessagingProduct { get; set; }

    [JsonProperty("metadata")]
    public WhatsAppWebhookMetadata? Metadata { get; set; }

    [JsonProperty("contacts")]
    public List<WhatsAppContact>? Contacts { get; set; }

    [JsonProperty("messages")]
    public List<WhatsAppIncomingMessage>? Messages { get; set; }

    [JsonProperty("statuses")]
    public List<WhatsAppMessageStatus>? Statuses { get; set; }
}

/// <summary>
/// WhatsApp webhook metadata
/// </summary>
public class WhatsAppWebhookMetadata
{
    [JsonProperty("display_phone_number")]
    public string? DisplayPhoneNumber { get; set; }

    [JsonProperty("phone_number_id")]
    public string? PhoneNumberId { get; set; }
}

/// <summary>
/// WhatsApp incoming message
/// </summary>
public class WhatsAppIncomingMessage
{
    [JsonProperty("from")]
    public string? From { get; set; }

    [JsonProperty("id")]
    public string? Id { get; set; }

    [JsonProperty("timestamp")]
    public string? Timestamp { get; set; }

    [JsonProperty("text")]
    public WhatsAppTextMessage? Text { get; set; }

    [JsonProperty("type")]
    public string? Type { get; set; }
}

/// <summary>
/// WhatsApp text message
/// </summary>
public class WhatsAppTextMessage
{
    [JsonProperty("body")]
    public string? Body { get; set; }
}

/// <summary>
/// WhatsApp message status from webhook
/// </summary>
public class WhatsAppMessageStatus
{
    [JsonProperty("id")]
    public string? Id { get; set; }

    [JsonProperty("status")]
    public string? Status { get; set; }

    [JsonProperty("timestamp")]
    public string? Timestamp { get; set; }

    [JsonProperty("recipient_id")]
    public string? RecipientId { get; set; }

    [JsonProperty("conversation")]
    public WhatsAppConversation? Conversation { get; set; }

    [JsonProperty("pricing")]
    public WhatsAppPricing? Pricing { get; set; }
}

/// <summary>
/// WhatsApp conversation information
/// </summary>
public class WhatsAppConversation
{
    [JsonProperty("id")]
    public string? Id { get; set; }

    [JsonProperty("expiration_timestamp")]
    public string? ExpirationTimestamp { get; set; }

    [JsonProperty("origin")]
    public WhatsAppConversationOrigin? Origin { get; set; }
}

/// <summary>
/// WhatsApp conversation origin
/// </summary>
public class WhatsAppConversationOrigin
{
    [JsonProperty("type")]
    public string? Type { get; set; }
}

/// <summary>
/// WhatsApp pricing information
/// </summary>
public class WhatsAppPricing
{
    [JsonProperty("billable")]
    public bool? Billable { get; set; }

    [JsonProperty("pricing_model")]
    public string? PricingModel { get; set; }

    [JsonProperty("category")]
    public string? Category { get; set; }
}
