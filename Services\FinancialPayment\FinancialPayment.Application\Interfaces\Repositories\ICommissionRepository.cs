using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.Enums;

namespace FinancialPayment.Application.Interfaces.Repositories;

public interface ICommissionRepository
{
    Task<Commission?> GetByIdAsync(Guid id);
    Task<Commission?> GetByOrderIdAsync(Guid orderId);
    Task<List<Commission>> GetByBrokerIdAsync(Guid brokerId);
    Task<List<Commission>> GetByTransportCompanyIdAsync(Guid transportCompanyId);
    Task<List<Commission>> GetByCarrierIdAsync(Guid carrierId);
    Task<List<Commission>> GetByStatusAsync(CommissionStatus status);
    Task<List<Commission>> GetByDateRangeAsync(DateTime from, DateTime to);
    Task<Commission> AddAsync(Commission commission);
    Task UpdateAsync(Commission commission);
    Task DeleteAsync(Guid id);
    Task<bool> ExistsAsync(Guid id);
    Task<decimal> GetTotalCommissionAmountAsync(Guid brokerId, DateTime? from = null, DateTime? to = null);
    Task<List<Commission>> GetPendingCommissionsAsync();
    Task<List<Commission>> GetDisputedCommissionsAsync();
}
