using Shared.Domain.Common;

namespace OrderManagement.Domain.Events;

public class BidReactivatedDomainEvent : DomainEvent
{
    public Guid BidId { get; }
    public Guid RfqId { get; }
    public Guid BrokerId { get; }
    public string ReactivationReason { get; }

    public BidReactivatedDomainEvent(Guid bidId, Guid rfqId, Guid brokerId, string reactivationReason)
    {
        BidId = bidId;
        RfqId = rfqId;
        BrokerId = brokerId;
        ReactivationReason = reactivationReason;
    }
}
