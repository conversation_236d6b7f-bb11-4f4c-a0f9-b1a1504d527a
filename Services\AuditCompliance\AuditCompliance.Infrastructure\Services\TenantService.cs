using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using AuditCompliance.Application.Interfaces;
using AuditCompliance.Domain.Entities;
using AuditCompliance.Domain.Interfaces;
using AuditCompliance.Infrastructure.Persistence;

namespace AuditCompliance.Infrastructure.Services;

/// <summary>
/// Service for tenant management operations
/// </summary>
public class TenantService : ITenantService
{
    private readonly AuditComplianceDbContext _context;
    private readonly ILogger<TenantService> _logger;

    public TenantService(
        AuditComplianceDbContext context,
        ILogger<TenantService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<TenantDto?> GetTenantAsync(Guid tenantId, CancellationToken cancellationToken = default)
    {
        try
        {
            var tenant = await _context.Tenants
                .FirstOrDefaultAsync(t => t.Id == tenantId, cancellationToken);

            return tenant != null ? MapToDto(tenant) : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tenant {TenantId}", tenantId);
            throw;
        }
    }

    public async Task<TenantDto?> GetTenantByCodeAsync(string tenantCode, CancellationToken cancellationToken = default)
    {
        try
        {
            var tenant = await _context.Tenants
                .FirstOrDefaultAsync(t => t.Code == tenantCode, cancellationToken);

            return tenant != null ? MapToDto(tenant) : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tenant by code {TenantCode}", tenantCode);
            throw;
        }
    }

    public async Task<PagedResult<TenantDto>> GetTenantsAsync(
        int pageNumber = 1, 
        int pageSize = 50, 
        string? searchTerm = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = _context.Tenants.AsQueryable();

            if (!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(t => 
                    t.Name.Contains(searchTerm) || 
                    t.Code.Contains(searchTerm) ||
                    (t.Description != null && t.Description.Contains(searchTerm)));
            }

            var totalCount = await query.CountAsync(cancellationToken);
            
            var tenants = await query
                .OrderBy(t => t.Name)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync(cancellationToken);

            return new PagedResult<TenantDto>
            {
                Items = tenants.Select(MapToDto).ToList(),
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tenants");
            throw;
        }
    }

    public async Task<Guid> CreateTenantAsync(CreateTenantDto createTenantDto, CancellationToken cancellationToken = default)
    {
        try
        {
            // Check if tenant code already exists
            var existingTenant = await _context.Tenants
                .FirstOrDefaultAsync(t => t.Code == createTenantDto.Code, cancellationToken);

            if (existingTenant != null)
            {
                throw new InvalidOperationException($"Tenant with code '{createTenantDto.Code}' already exists");
            }

            var plan = Enum.Parse<TenantPlan>(createTenantDto.Plan, true);
            var tenant = new Tenant(
                createTenantDto.Name,
                createTenantDto.Code,
                createTenantDto.Description,
                plan,
                createTenantDto.ContactEmail);

            // Set configuration if provided
            if (createTenantDto.Configuration != null)
            {
                var configuration = MapFromConfigurationDto(createTenantDto.Configuration);
                tenant.UpdateConfiguration(configuration);
            }

            // Set compliance standards if provided
            if (createTenantDto.EnabledComplianceStandards?.Any() == true)
            {
                foreach (var standard in createTenantDto.EnabledComplianceStandards)
                {
                    tenant.EnableComplianceStandard(standard);
                }
            }

            _context.Tenants.Add(tenant);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Created tenant {TenantId} with code {TenantCode}", tenant.Id, tenant.Code);
            return tenant.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating tenant");
            throw;
        }
    }

    public async Task<bool> UpdateTenantAsync(Guid tenantId, UpdateTenantDto updateTenantDto, CancellationToken cancellationToken = default)
    {
        try
        {
            var tenant = await _context.Tenants
                .FirstOrDefaultAsync(t => t.Id == tenantId, cancellationToken);

            if (tenant == null)
            {
                _logger.LogWarning("Tenant not found: {TenantId}", tenantId);
                return false;
            }

            tenant.UpdateDetails(
                updateTenantDto.Name,
                updateTenantDto.Description,
                updateTenantDto.ContactEmail,
                updateTenantDto.ContactPhone);

            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Updated tenant {TenantId}", tenantId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating tenant {TenantId}", tenantId);
            throw;
        }
    }

    public async Task<bool> UpdateTenantConfigurationAsync(
        Guid tenantId, 
        TenantConfigurationDto configuration, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var tenant = await _context.Tenants
                .FirstOrDefaultAsync(t => t.Id == tenantId, cancellationToken);

            if (tenant == null)
            {
                _logger.LogWarning("Tenant not found: {TenantId}", tenantId);
                return false;
            }

            var tenantConfiguration = MapFromConfigurationDto(configuration);
            tenant.UpdateConfiguration(tenantConfiguration);

            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Updated configuration for tenant {TenantId}", tenantId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating tenant configuration {TenantId}", tenantId);
            throw;
        }
    }

    public async Task<bool> UpdateTenantPlanAsync(Guid tenantId, TenantPlan newPlan, CancellationToken cancellationToken = default)
    {
        try
        {
            var tenant = await _context.Tenants
                .FirstOrDefaultAsync(t => t.Id == tenantId, cancellationToken);

            if (tenant == null)
            {
                _logger.LogWarning("Tenant not found: {TenantId}", tenantId);
                return false;
            }

            tenant.UpdatePlan(newPlan);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Updated plan for tenant {TenantId} to {Plan}", tenantId, newPlan);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating tenant plan {TenantId}", tenantId);
            throw;
        }
    }

    public async Task<bool> DeactivateTenantAsync(Guid tenantId, string reason, CancellationToken cancellationToken = default)
    {
        try
        {
            var tenant = await _context.Tenants
                .FirstOrDefaultAsync(t => t.Id == tenantId, cancellationToken);

            if (tenant == null)
            {
                _logger.LogWarning("Tenant not found: {TenantId}", tenantId);
                return false;
            }

            tenant.Deactivate(reason);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Deactivated tenant {TenantId} with reason: {Reason}", tenantId, reason);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deactivating tenant {TenantId}", tenantId);
            throw;
        }
    }

    public async Task<bool> ReactivateTenantAsync(Guid tenantId, CancellationToken cancellationToken = default)
    {
        try
        {
            var tenant = await _context.Tenants
                .FirstOrDefaultAsync(t => t.Id == tenantId, cancellationToken);

            if (tenant == null)
            {
                _logger.LogWarning("Tenant not found: {TenantId}", tenantId);
                return false;
            }

            tenant.Reactivate();
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Reactivated tenant {TenantId}", tenantId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reactivating tenant {TenantId}", tenantId);
            throw;
        }
    }

    public async Task<bool> SuspendTenantAsync(Guid tenantId, string reason, CancellationToken cancellationToken = default)
    {
        try
        {
            var tenant = await _context.Tenants
                .FirstOrDefaultAsync(t => t.Id == tenantId, cancellationToken);

            if (tenant == null)
            {
                _logger.LogWarning("Tenant not found: {TenantId}", tenantId);
                return false;
            }

            tenant.Suspend(reason);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Suspended tenant {TenantId} with reason: {Reason}", tenantId, reason);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error suspending tenant {TenantId}", tenantId);
            throw;
        }
    }

    public async Task<bool> EnableComplianceStandardAsync(
        Guid tenantId, 
        string standard, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var tenant = await _context.Tenants
                .FirstOrDefaultAsync(t => t.Id == tenantId, cancellationToken);

            if (tenant == null)
            {
                _logger.LogWarning("Tenant not found: {TenantId}", tenantId);
                return false;
            }

            tenant.EnableComplianceStandard(standard);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Enabled compliance standard {Standard} for tenant {TenantId}", standard, tenantId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error enabling compliance standard for tenant {TenantId}", tenantId);
            throw;
        }
    }

    public async Task<bool> DisableComplianceStandardAsync(
        Guid tenantId, 
        string standard, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var tenant = await _context.Tenants
                .FirstOrDefaultAsync(t => t.Id == tenantId, cancellationToken);

            if (tenant == null)
            {
                _logger.LogWarning("Tenant not found: {TenantId}", tenantId);
                return false;
            }

            tenant.DisableComplianceStandard(standard);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Disabled compliance standard {Standard} for tenant {TenantId}", standard, tenantId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disabling compliance standard for tenant {TenantId}", tenantId);
            throw;
        }
    }

    public async Task<bool> UpdateComplianceSettingsAsync(
        Guid tenantId, 
        Dictionary<string, object> settings, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var tenant = await _context.Tenants
                .FirstOrDefaultAsync(t => t.Id == tenantId, cancellationToken);

            if (tenant == null)
            {
                _logger.LogWarning("Tenant not found: {TenantId}", tenantId);
                return false;
            }

            tenant.UpdateComplianceSettings(settings);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Updated compliance settings for tenant {TenantId}", tenantId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating compliance settings for tenant {TenantId}", tenantId);
            throw;
        }
    }

    public async Task<TenantUsageDto> GetTenantUsageAsync(Guid tenantId, CancellationToken cancellationToken = default)
    {
        try
        {
            var tenant = await _context.Tenants
                .FirstOrDefaultAsync(t => t.Id == tenantId, cancellationToken);

            if (tenant == null)
            {
                throw new InvalidOperationException($"Tenant {tenantId} not found");
            }

            var now = DateTime.UtcNow;
            var monthStart = new DateTime(now.Year, now.Month, 1);
            var hourStart = now.AddHours(-1);

            // Calculate usage metrics
            var auditLogsThisMonth = await _context.AuditLogs
                .CountAsync(a => a.Timestamp >= monthStart, cancellationToken);

            var complianceReportsThisMonth = await _context.ComplianceReports
                .CountAsync(r => r.CreatedAt >= monthStart, cancellationToken);

            var serviceProvidersCount = await _context.ServiceProviderRatings
                .Select(r => r.ServiceProviderId)
                .Distinct()
                .CountAsync(cancellationToken);

            return new TenantUsageDto
            {
                TenantId = tenantId,
                PeriodStart = monthStart,
                PeriodEnd = now,
                CurrentUsers = 0, // This would come from user service
                AuditLogsThisMonth = auditLogsThisMonth,
                ComplianceReportsThisMonth = complianceReportsThisMonth,
                ServiceProvidersCount = serviceProvidersCount,
                StorageBytesUsed = 0, // This would be calculated from file storage
                ApiCallsThisHour = 0, // This would come from API gateway metrics
                CurrentSessions = 0 // This would come from session store
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tenant usage for {TenantId}", tenantId);
            throw;
        }
    }

    public async Task<TenantLimitValidationResult> ValidateTenantLimitsAsync(
        Guid tenantId, 
        string resourceType, 
        int requestedAmount = 1,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var tenant = await _context.Tenants
                .FirstOrDefaultAsync(t => t.Id == tenantId, cancellationToken);

            if (tenant == null)
            {
                return new TenantLimitValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "Tenant not found",
                    ResourceType = resourceType
                };
            }

            var usage = await GetTenantUsageAsync(tenantId, cancellationToken);
            var limits = MapToLimitsDto(tenant.Limits);

            var (currentUsage, limit) = resourceType.ToLower() switch
            {
                "users" => (usage.CurrentUsers, limits.MaxUsers),
                "auditlogs" => (usage.AuditLogsThisMonth, limits.MaxAuditLogsPerMonth),
                "reports" => (usage.ComplianceReportsThisMonth, limits.MaxComplianceReports),
                "providers" => (usage.ServiceProvidersCount, limits.MaxServiceProviders),
                "apicalls" => (usage.ApiCallsThisHour, limits.MaxApiCallsPerHour),
                "sessions" => (usage.CurrentSessions, limits.MaxConcurrentSessions),
                _ => (0, 0)
            };

            var isUnlimited = limit == -1;
            var available = isUnlimited ? int.MaxValue : Math.Max(0, limit - currentUsage);
            var isValid = isUnlimited || (currentUsage + requestedAmount) <= limit;

            return new TenantLimitValidationResult
            {
                IsValid = isValid,
                ErrorMessage = isValid ? null : $"Resource limit exceeded for {resourceType}",
                CurrentUsage = currentUsage,
                Limit = limit,
                Available = available,
                IsUnlimited = isUnlimited,
                ResourceType = resourceType
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating tenant limits for {TenantId}", tenantId);
            throw;
        }
    }

    // Private mapping methods
    private TenantDto MapToDto(Tenant tenant)
    {
        return new TenantDto
        {
            Id = tenant.Id,
            Name = tenant.Name,
            Code = tenant.Code,
            Description = tenant.Description,
            Status = tenant.Status.ToString(),
            Plan = tenant.Plan.ToString(),
            CreatedAt = tenant.CreatedAt,
            UpdatedAt = tenant.UpdatedAt,
            ContactEmail = tenant.ContactEmail,
            ContactPhone = tenant.ContactPhone,
            Configuration = MapToConfigurationDto(tenant.Configuration),
            EnabledComplianceStandards = tenant.EnabledComplianceStandards.ToList(),
            Limits = MapToLimitsDto(tenant.Limits)
        };
    }

    private TenantConfigurationDto MapToConfigurationDto(TenantConfiguration config)
    {
        return new TenantConfigurationDto
        {
            EnabledFeatures = config.EnabledFeatures.ToList(),
            Settings = config.Settings.ToDictionary(kvp => kvp.Key, kvp => kvp.Value),
            TimeZone = config.TimeZone,
            DateFormat = config.DateFormat,
            Currency = config.Currency,
            EnableAuditLogging = config.EnableAuditLogging,
            EnableRealTimeNotifications = config.EnableRealTimeNotifications,
            EnableAdvancedAnalytics = config.EnableAdvancedAnalytics,
            DataRetentionDays = config.DataRetentionDays,
            EnableMobileAccess = config.EnableMobileAccess,
            EnableApiAccess = config.EnableApiAccess,
            AllowedIpRanges = config.AllowedIpRanges.ToList(),
            RequireTwoFactorAuth = config.RequireTwoFactorAuth
        };
    }

    private TenantConfiguration MapFromConfigurationDto(TenantConfigurationDto dto)
    {
        return new TenantConfiguration
        {
            EnabledFeatures = dto.EnabledFeatures.ToList(),
            Settings = dto.Settings.ToDictionary(kvp => kvp.Key, kvp => kvp.Value),
            TimeZone = dto.TimeZone,
            DateFormat = dto.DateFormat,
            Currency = dto.Currency,
            EnableAuditLogging = dto.EnableAuditLogging,
            EnableRealTimeNotifications = dto.EnableRealTimeNotifications,
            EnableAdvancedAnalytics = dto.EnableAdvancedAnalytics,
            DataRetentionDays = dto.DataRetentionDays,
            EnableMobileAccess = dto.EnableMobileAccess,
            EnableApiAccess = dto.EnableApiAccess,
            AllowedIpRanges = dto.AllowedIpRanges.ToList(),
            RequireTwoFactorAuth = dto.RequireTwoFactorAuth
        };
    }

    private TenantLimitsDto MapToLimitsDto(TenantLimits limits)
    {
        return new TenantLimitsDto
        {
            MaxUsers = limits.MaxUsers,
            MaxAuditLogsPerMonth = limits.MaxAuditLogsPerMonth,
            MaxComplianceReports = limits.MaxComplianceReports,
            MaxServiceProviders = limits.MaxServiceProviders,
            MaxStorageBytes = limits.MaxStorageBytes,
            MaxApiCallsPerHour = limits.MaxApiCallsPerHour,
            MaxConcurrentSessions = limits.MaxConcurrentSessions
        };
    }
}
