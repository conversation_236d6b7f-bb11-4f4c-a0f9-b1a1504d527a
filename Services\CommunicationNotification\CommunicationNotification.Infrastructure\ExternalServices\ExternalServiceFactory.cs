using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Domain.Enums;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace CommunicationNotification.Infrastructure.ExternalServices;

/// <summary>
/// Factory for creating external service providers
/// </summary>
public class ExternalServiceFactory : IExternalServiceFactory
{
    private readonly IServiceProvider _serviceProvider;
    private readonly IConfiguration _configuration;
    private readonly ILogger<ExternalServiceFactory> _logger;
    private readonly ExternalServiceSettings _settings;

    public ExternalServiceFactory(
        IServiceProvider serviceProvider,
        IConfiguration configuration,
        ILogger<ExternalServiceFactory> logger)
    {
        _serviceProvider = serviceProvider;
        _configuration = configuration;
        _logger = logger;
        _settings = configuration.GetSection("ExternalServices").Get<ExternalServiceSettings>() ?? new();
    }

    public ISmsProvider GetSmsProvider(SmsProviderType? providerType = null)
    {
        var provider = providerType ?? _settings.DefaultSmsProvider;
        
        return provider switch
        {
            SmsProviderType.Twilio => GetService<TwilioSmsProvider>(),
            SmsProviderType.AWS_SNS => GetService<AwsSnsProvider>(),
            SmsProviderType.Azure => GetService<AzureSmsProvider>(),
            _ => throw new NotSupportedException($"SMS provider {provider} is not supported")
        };
    }

    public IEmailProvider GetEmailProvider(EmailProviderType? providerType = null)
    {
        var provider = providerType ?? _settings.DefaultEmailProvider;
        
        return provider switch
        {
            EmailProviderType.SendGrid => GetService<SendGridEmailProvider>(),
            EmailProviderType.AWS_SES => GetService<AwsSesProvider>(),
            EmailProviderType.Azure => GetService<AzureEmailProvider>(),
            EmailProviderType.SMTP => GetService<SmtpEmailProvider>(),
            _ => throw new NotSupportedException($"Email provider {provider} is not supported")
        };
    }

    public IPushNotificationProvider GetPushProvider(PushProviderType? providerType = null)
    {
        var provider = providerType ?? _settings.DefaultPushProvider;
        
        return provider switch
        {
            PushProviderType.Firebase => GetService<FirebasePushProvider>(),
            PushProviderType.APNS => GetService<ApnsPushProvider>(),
            PushProviderType.Azure => GetService<AzurePushProvider>(),
            _ => throw new NotSupportedException($"Push provider {provider} is not supported")
        };
    }

    public IVoiceProvider GetVoiceProvider(VoiceProviderType? providerType = null)
    {
        var provider = providerType ?? _settings.DefaultVoiceProvider;
        
        return provider switch
        {
            VoiceProviderType.Twilio => GetService<TwilioVoiceProvider>(),
            VoiceProviderType.AWS_Connect => GetService<AwsConnectProvider>(),
            VoiceProviderType.Azure => GetService<AzureVoiceProvider>(),
            _ => throw new NotSupportedException($"Voice provider {provider} is not supported")
        };
    }

    public IWhatsAppProvider GetWhatsAppProvider(WhatsAppProviderType? providerType = null)
    {
        var provider = providerType ?? _settings.DefaultWhatsAppProvider;
        
        return provider switch
        {
            WhatsAppProviderType.Business_API => GetService<WhatsAppBusinessProvider>(),
            WhatsAppProviderType.Twilio => GetService<TwilioWhatsAppProvider>(),
            WhatsAppProviderType.Meta_Cloud => GetService<MetaCloudWhatsAppProvider>(),
            _ => throw new NotSupportedException($"WhatsApp provider {provider} is not supported")
        };
    }

    public async Task<ProviderHealthStatus> CheckProviderHealthAsync(
        NotificationChannel channel,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Checking health for channel {Channel}", channel);

            return channel switch
            {
                NotificationChannel.Sms => await CheckSmsProviderHealthAsync(cancellationToken),
                NotificationChannel.Email => await CheckEmailProviderHealthAsync(cancellationToken),
                NotificationChannel.Push => await CheckPushProviderHealthAsync(cancellationToken),
                NotificationChannel.Voice => await CheckVoiceProviderHealthAsync(cancellationToken),
                NotificationChannel.WhatsApp => await CheckWhatsAppProviderHealthAsync(cancellationToken),
                _ => new ProviderHealthStatus
                {
                    IsHealthy = false,
                    Channel = channel,
                    ErrorMessage = "Unsupported channel"
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking provider health for channel {Channel}", channel);
            
            return new ProviderHealthStatus
            {
                IsHealthy = false,
                Channel = channel,
                ErrorMessage = ex.Message,
                CheckedAt = DateTime.UtcNow
            };
        }
    }

    public async Task<List<ProviderHealthStatus>> CheckAllProvidersHealthAsync(CancellationToken cancellationToken = default)
    {
        var channels = Enum.GetValues<NotificationChannel>();
        var healthChecks = new List<Task<ProviderHealthStatus>>();

        foreach (var channel in channels)
        {
            healthChecks.Add(CheckProviderHealthAsync(channel, cancellationToken));
        }

        var results = await Task.WhenAll(healthChecks);
        return results.ToList();
    }

    public ProviderConfiguration GetProviderConfiguration(NotificationChannel channel)
    {
        return channel switch
        {
            NotificationChannel.Sms => new ProviderConfiguration
            {
                Channel = channel,
                ProviderType = _settings.DefaultSmsProvider.ToString(),
                IsEnabled = _settings.EnableSms,
                Configuration = _configuration.GetSection("Twilio").Get<Dictionary<string, object>>() ?? new()
            },
            NotificationChannel.Email => new ProviderConfiguration
            {
                Channel = channel,
                ProviderType = _settings.DefaultEmailProvider.ToString(),
                IsEnabled = _settings.EnableEmail,
                Configuration = _configuration.GetSection("SendGrid").Get<Dictionary<string, object>>() ?? new()
            },
            NotificationChannel.Push => new ProviderConfiguration
            {
                Channel = channel,
                ProviderType = _settings.DefaultPushProvider.ToString(),
                IsEnabled = _settings.EnablePush,
                Configuration = _configuration.GetSection("Firebase").Get<Dictionary<string, object>>() ?? new()
            },
            NotificationChannel.Voice => new ProviderConfiguration
            {
                Channel = channel,
                ProviderType = _settings.DefaultVoiceProvider.ToString(),
                IsEnabled = _settings.EnableVoice,
                Configuration = _configuration.GetSection("TwilioVoice").Get<Dictionary<string, object>>() ?? new()
            },
            NotificationChannel.WhatsApp => new ProviderConfiguration
            {
                Channel = channel,
                ProviderType = _settings.DefaultWhatsAppProvider.ToString(),
                IsEnabled = _settings.EnableWhatsApp,
                Configuration = _configuration.GetSection("WhatsAppBusiness").Get<Dictionary<string, object>>() ?? new()
            },
            _ => throw new NotSupportedException($"Channel {channel} is not supported")
        };
    }

    private T GetService<T>() where T : class
    {
        var service = _serviceProvider.GetService(typeof(T)) as T;
        if (service == null)
        {
            throw new InvalidOperationException($"Service {typeof(T).Name} is not registered");
        }
        return service;
    }

    private async Task<ProviderHealthStatus> CheckSmsProviderHealthAsync(CancellationToken cancellationToken)
    {
        try
        {
            var provider = GetSmsProvider();
            
            // Perform a basic validation check
            var isValid = await provider.ValidatePhoneNumberAsync("+**********", cancellationToken);
            
            return new ProviderHealthStatus
            {
                IsHealthy = true, // Basic check passed
                Channel = NotificationChannel.Sms,
                ProviderType = _settings.DefaultSmsProvider.ToString(),
                CheckedAt = DateTime.UtcNow,
                ResponseTime = TimeSpan.FromMilliseconds(100) // Placeholder
            };
        }
        catch (Exception ex)
        {
            return new ProviderHealthStatus
            {
                IsHealthy = false,
                Channel = NotificationChannel.Sms,
                ProviderType = _settings.DefaultSmsProvider.ToString(),
                ErrorMessage = ex.Message,
                CheckedAt = DateTime.UtcNow
            };
        }
    }

    private async Task<ProviderHealthStatus> CheckEmailProviderHealthAsync(CancellationToken cancellationToken)
    {
        try
        {
            var provider = GetEmailProvider();
            
            // Perform a basic validation check
            var isValid = await provider.ValidateEmailAddressAsync("<EMAIL>", cancellationToken);
            
            return new ProviderHealthStatus
            {
                IsHealthy = true,
                Channel = NotificationChannel.Email,
                ProviderType = _settings.DefaultEmailProvider.ToString(),
                CheckedAt = DateTime.UtcNow,
                ResponseTime = TimeSpan.FromMilliseconds(150)
            };
        }
        catch (Exception ex)
        {
            return new ProviderHealthStatus
            {
                IsHealthy = false,
                Channel = NotificationChannel.Email,
                ProviderType = _settings.DefaultEmailProvider.ToString(),
                ErrorMessage = ex.Message,
                CheckedAt = DateTime.UtcNow
            };
        }
    }

    private async Task<ProviderHealthStatus> CheckPushProviderHealthAsync(CancellationToken cancellationToken)
    {
        try
        {
            var provider = GetPushProvider();
            
            // Perform a basic validation check
            var isValid = await provider.ValidateDeviceTokenAsync("sample_device_token", cancellationToken);
            
            return new ProviderHealthStatus
            {
                IsHealthy = true,
                Channel = NotificationChannel.Push,
                ProviderType = _settings.DefaultPushProvider.ToString(),
                CheckedAt = DateTime.UtcNow,
                ResponseTime = TimeSpan.FromMilliseconds(200)
            };
        }
        catch (Exception ex)
        {
            return new ProviderHealthStatus
            {
                IsHealthy = false,
                Channel = NotificationChannel.Push,
                ProviderType = _settings.DefaultPushProvider.ToString(),
                ErrorMessage = ex.Message,
                CheckedAt = DateTime.UtcNow
            };
        }
    }

    private async Task<ProviderHealthStatus> CheckVoiceProviderHealthAsync(CancellationToken cancellationToken)
    {
        try
        {
            var provider = GetVoiceProvider();
            
            // Perform a basic validation check
            var isValid = await provider.ValidatePhoneNumberAsync("+**********", cancellationToken);
            
            return new ProviderHealthStatus
            {
                IsHealthy = true,
                Channel = NotificationChannel.Voice,
                ProviderType = _settings.DefaultVoiceProvider.ToString(),
                CheckedAt = DateTime.UtcNow,
                ResponseTime = TimeSpan.FromMilliseconds(300)
            };
        }
        catch (Exception ex)
        {
            return new ProviderHealthStatus
            {
                IsHealthy = false,
                Channel = NotificationChannel.Voice,
                ProviderType = _settings.DefaultVoiceProvider.ToString(),
                ErrorMessage = ex.Message,
                CheckedAt = DateTime.UtcNow
            };
        }
    }

    private async Task<ProviderHealthStatus> CheckWhatsAppProviderHealthAsync(CancellationToken cancellationToken)
    {
        try
        {
            var provider = GetWhatsAppProvider();
            
            // Perform a basic validation check
            var isValid = await provider.ValidatePhoneNumberAsync("+**********", cancellationToken);
            
            return new ProviderHealthStatus
            {
                IsHealthy = true,
                Channel = NotificationChannel.WhatsApp,
                ProviderType = _settings.DefaultWhatsAppProvider.ToString(),
                CheckedAt = DateTime.UtcNow,
                ResponseTime = TimeSpan.FromMilliseconds(250)
            };
        }
        catch (Exception ex)
        {
            return new ProviderHealthStatus
            {
                IsHealthy = false,
                Channel = NotificationChannel.WhatsApp,
                ProviderType = _settings.DefaultWhatsAppProvider.ToString(),
                ErrorMessage = ex.Message,
                CheckedAt = DateTime.UtcNow
            };
        }
    }
}

/// <summary>
/// External service configuration settings
/// </summary>
public class ExternalServiceSettings
{
    public SmsProviderType DefaultSmsProvider { get; set; } = SmsProviderType.Twilio;
    public EmailProviderType DefaultEmailProvider { get; set; } = EmailProviderType.SendGrid;
    public PushProviderType DefaultPushProvider { get; set; } = PushProviderType.Firebase;
    public VoiceProviderType DefaultVoiceProvider { get; set; } = VoiceProviderType.Twilio;
    public WhatsAppProviderType DefaultWhatsAppProvider { get; set; } = WhatsAppProviderType.Business_API;
    
    public bool EnableSms { get; set; } = true;
    public bool EnableEmail { get; set; } = true;
    public bool EnablePush { get; set; } = true;
    public bool EnableVoice { get; set; } = true;
    public bool EnableWhatsApp { get; set; } = true;
    
    public bool EnableFailover { get; set; } = true;
    public int HealthCheckIntervalMinutes { get; set; } = 5;
    public int ProviderTimeoutSeconds { get; set; } = 30;
}

/// <summary>
/// Provider health status
/// </summary>
public class ProviderHealthStatus
{
    public bool IsHealthy { get; set; }
    public NotificationChannel Channel { get; set; }
    public string ProviderType { get; set; } = string.Empty;
    public string? ErrorMessage { get; set; }
    public DateTime CheckedAt { get; set; }
    public TimeSpan? ResponseTime { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Provider configuration
/// </summary>
public class ProviderConfiguration
{
    public NotificationChannel Channel { get; set; }
    public string ProviderType { get; set; } = string.Empty;
    public bool IsEnabled { get; set; }
    public Dictionary<string, object> Configuration { get; set; } = new();
}

// Provider type enums
public enum SmsProviderType
{
    Twilio,
    AWS_SNS,
    Azure
}

public enum EmailProviderType
{
    SendGrid,
    AWS_SES,
    Azure,
    SMTP
}

public enum PushProviderType
{
    Firebase,
    APNS,
    Azure
}

public enum VoiceProviderType
{
    Twilio,
    AWS_Connect,
    Azure
}

public enum WhatsAppProviderType
{
    Business_API,
    Twilio,
    Meta_Cloud
}
