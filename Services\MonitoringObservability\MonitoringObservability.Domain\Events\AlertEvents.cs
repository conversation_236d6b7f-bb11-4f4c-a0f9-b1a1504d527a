using Shared.Domain.Common;
using MonitoringObservability.Domain.Enums;

namespace MonitoringObservability.Domain.Events;

// Alert lifecycle events
public record AlertTriggeredEvent(
    Guid AlertId,
    string Title,
    AlertSeverity Severity,
    string ServiceName,
    string MetricName,
    double CurrentValue) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record AlertAcknowledgedEvent(
    Guid AlertId,
    Guid AcknowledgedByUserId,
    string? UserName) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record AlertAssignedEvent(
    Guid AlertId,
    Guid AssignedToUserId,
    string UserName) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record AlertInvestigationStartedEvent(
    Guid AlertId,
    Guid UserId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record AlertResolvedEvent(
    Guid AlertId,
    Guid ResolvedByUserId,
    string? ResolutionNotes) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record AlertClosedEvent(
    Guid AlertId,
    Guid ClosedByUserId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record AlertSuppressedEvent(
    Guid AlertId,
    TimeSpan Duration,
    Guid SuppressedByUserId,
    string Reason) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record AlertEscalatedEvent(
    Guid AlertId,
    int EscalationLevel,
    string Reason) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record AlertSeverityChangedEvent(
    Guid AlertId,
    AlertSeverity OldSeverity,
    AlertSeverity NewSeverity,
    double CurrentValue) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record AlertCommentAddedEvent(
    Guid AlertId,
    Guid UserId,
    string UserName,
    string Comment) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record AlertNotificationSentEvent(
    Guid AlertId,
    NotificationChannel Channel,
    string Recipient) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// Metric events
public record MetricRecordedEvent(
    string ServiceName,
    string MetricName,
    double Value,
    string Unit,
    MetricType Type,
    Dictionary<string, string> Tags) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ThresholdBreachedEvent(
    string ServiceName,
    string MetricName,
    double Value,
    double ThresholdValue,
    AlertSeverity Severity) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// Health check events
public record HealthCheckExecutedEvent(
    string ServiceName,
    HealthStatus Status,
    TimeSpan Duration,
    string? ErrorMessage) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ServiceHealthChangedEvent(
    string ServiceName,
    HealthStatus OldStatus,
    HealthStatus NewStatus,
    string? Reason) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// Incident events
public record IncidentCreatedEvent(
    Guid IncidentId,
    string Title,
    IncidentSeverity Severity,
    string ServiceName) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record IncidentStatusChangedEvent(
    Guid IncidentId,
    IncidentStatus OldStatus,
    IncidentStatus NewStatus,
    Guid ChangedByUserId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record IncidentResolvedEvent(
    Guid IncidentId,
    Guid ResolvedByUserId,
    string? ResolutionSummary,
    TimeSpan Duration) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// Log events
public record CriticalLogEntryEvent(
    string ServiceName,
    LogLevel Level,
    string Message,
    string? Exception,
    Dictionary<string, object> Properties) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// Performance events
public record PerformanceDegradationDetectedEvent(
    string ServiceName,
    string MetricName,
    double CurrentValue,
    double BaselineValue,
    double DegradationPercentage) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record SLABreachEvent(
    string ServiceName,
    string SLAName,
    double CurrentValue,
    double SLAThreshold,
    TimeSpan BreachDuration) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}
