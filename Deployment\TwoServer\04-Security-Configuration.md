# Security Configuration Guide

## Overview

This guide provides comprehensive security configuration for the TLI microservices deployment across two servers, covering SSL/TLS, firewall rules, authentication, authorization, and security best practices.

## Security Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                     Security Layers                            │
│                                                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │              Network Security                           │   │
│  │  • Firewall Rules                                       │   │
│  │  • VPN/Private Networks                                 │   │
│  │  • SSL/TLS Encryption                                   │   │
│  └─────────────────────────────────────────────────────────┘   │
│                                                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │            Application Security                         │   │
│  │  • JWT Authentication                                   │   │
│  │  • Role-based Authorization                             │   │
│  │  • API Rate Limiting                                    │   │
│  └─────────────────────────────────────────────────────────┘   │
│                                                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │              Data Security                              │   │
│  │  • Database Encryption                                  │   │
│  │  • Password Hashing                                     │   │
│  │  • Sensitive Data Masking                               │   │
│  └─────────────────────────────────────────────────────────┘   │
│                                                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │           Infrastructure Security                       │   │
│  │  • Container Security                                   │   │
│  │  • Secret Management                                    │   │
│  │  • Audit Logging                                        │   │
│  └─────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
```

## Step 1: SSL/TLS Configuration

### 1.1 Generate SSL Certificates

#### Production SSL Certificates (Recommended)

```bash
# Install Certbot for Let's Encrypt
sudo apt install -y certbot

# Generate certificates for your domain
sudo certbot certonly --standalone -d tli.yourdomain.com -d api.tli.yourdomain.com

# Certificates will be stored in:
# /etc/letsencrypt/live/tli.yourdomain.com/fullchain.pem
# /etc/letsencrypt/live/tli.yourdomain.com/privkey.pem

# Copy certificates to TLI directory
sudo cp /etc/letsencrypt/live/tli.yourdomain.com/fullchain.pem /opt/tli/config/ssl/tli.crt
sudo cp /etc/letsencrypt/live/tli.yourdomain.com/privkey.pem /opt/tli/config/ssl/tli.key
sudo chown tli:tli /opt/tli/config/ssl/tli.*
sudo chmod 600 /opt/tli/config/ssl/tli.key
sudo chmod 644 /opt/tli/config/ssl/tli.crt
```

#### Self-Signed Certificates (Development)

```bash
# Create SSL directory
mkdir -p /opt/tli/config/ssl

# Generate self-signed certificate
openssl req -x509 -newkey rsa:4096 -keyout /opt/tli/config/ssl/tli.key -out /opt/tli/config/ssl/tli.crt -days 365 -nodes -subj "/C=IN/ST=Maharashtra/L=Mumbai/O=TLI/CN=tli.local"

# Set proper permissions
chmod 600 /opt/tli/config/ssl/tli.key
chmod 644 /opt/tli/config/ssl/tli.crt
```

### 1.2 Configure HTTPS in Applications

#### Update API Gateway for HTTPS

```bash
# Update API Gateway Docker Compose configuration
cat >> /opt/tli/tli-microservices.yml << 'EOF'
  apigateway:
    # ... existing configuration ...
    ports:
      - "5000:80"
      - "5443:443"
    environment:
      - ASPNETCORE_URLS=https://+:443;http://+:80
      - ASPNETCORE_Kestrel__Certificates__Default__Path=/app/ssl/tli.crt
      - ASPNETCORE_Kestrel__Certificates__Default__KeyPath=/app/ssl/tli.key
    volumes:
      - /opt/tli/config/ssl:/app/ssl:ro
      # ... other volumes ...
EOF
```

#### Configure nginx with SSL

```bash
# Install nginx
sudo apt install -y nginx

# Create SSL configuration
sudo tee /etc/nginx/sites-available/tli-ssl << 'EOF'
server {
    listen 80;
    server_name tli.yourdomain.com api.tli.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name tli.yourdomain.com api.tli.yourdomain.com;

    # SSL Configuration
    ssl_certificate /opt/tli/config/ssl/tli.crt;
    ssl_certificate_key /opt/tli/config/ssl/tli.key;

    # Modern SSL configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security headers
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-Frame-Options "DENY" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self'; connect-src 'self'; frame-ancestors 'none';" always;

    # API Gateway proxy
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;

        # Timeouts
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;

        # Buffer settings
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }

    # Health check endpoint
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
EOF

# Enable the site
sudo ln -s /etc/nginx/sites-available/tli-ssl /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default

# Test configuration
sudo nginx -t

# Restart nginx
sudo systemctl restart nginx
sudo systemctl enable nginx
```

## Step 2: Firewall Configuration

### 2.1 Advanced Firewall Rules

#### Server 1 (Infrastructure) - Enhanced Security

```bash
# Reset and configure UFW with strict rules
sudo ufw --force reset
sudo ufw default deny incoming
sudo ufw default allow outgoing

# Allow SSH with rate limiting
sudo ufw limit ssh

# Allow infrastructure services only from Server 2
sudo ufw allow from ********** to any port 5432 comment 'PostgreSQL from Server 2'
sudo ufw allow from ********** to any port 6379 comment 'Redis from Server 2'
sudo ufw allow from ********** to any port 5672 comment 'RabbitMQ from Server 2'

# Allow monitoring access from specific admin IPs only
sudo ufw allow from ********/24 to any port 9090 comment 'Prometheus monitoring'
sudo ufw allow from ********/24 to any port 3000 comment 'Grafana dashboard'
sudo ufw allow from ********/24 to any port 15672 comment 'RabbitMQ management'

# Log denied connections
sudo ufw logging on

# Enable firewall
sudo ufw enable

# Check status
sudo ufw status verbose
```

#### Server 2 (Applications) - Enhanced Security

```bash
# Reset and configure UFW with strict rules
sudo ufw --force reset
sudo ufw default deny incoming
sudo ufw default allow outgoing

# Allow SSH with rate limiting
sudo ufw limit ssh

# Allow HTTP/HTTPS
sudo ufw allow 80/tcp comment 'HTTP'
sudo ufw allow 443/tcp comment 'HTTPS'

# Allow API Gateway
sudo ufw allow 5000/tcp comment 'API Gateway'

# Restrict microservices access to admin networks only
sudo ufw allow from ********/24 to any port 5001:5013 comment 'Microservices admin access'

# Allow health checks from monitoring
sudo ufw allow from ********** to any port 5001:5013 comment 'Health checks from monitoring'

# Log denied connections
sudo ufw logging on

# Enable firewall
sudo ufw enable

# Check status
sudo ufw status verbose
```

### 2.2 Fail2Ban Configuration

#### Install and Configure Fail2Ban

```bash
# Install Fail2Ban
sudo apt install -y fail2ban

# Create custom configuration
sudo tee /etc/fail2ban/jail.local << 'EOF'
[DEFAULT]
# Ban time in seconds (1 hour)
bantime = 3600

# Find time window (10 minutes)
findtime = 600

# Max retry attempts
maxretry = 3

# Email notifications
destemail = <EMAIL>
sendername = Fail2Ban-TLI
mta = sendmail

# Action to take
action = %(action_mwl)s

[sshd]
enabled = true
port = ssh
filter = sshd
logpath = /var/log/auth.log
maxretry = 3
bantime = 3600

[nginx-http-auth]
enabled = true
port = http,https
filter = nginx-http-auth
logpath = /var/log/nginx/error.log
maxretry = 3

[nginx-limit-req]
enabled = true
port = http,https
filter = nginx-limit-req
logpath = /var/log/nginx/error.log
maxretry = 3

[nginx-botsearch]
enabled = true
port = http,https
filter = nginx-botsearch
logpath = /var/log/nginx/access.log
maxretry = 2
EOF

# Start and enable Fail2Ban
sudo systemctl enable fail2ban
sudo systemctl start fail2ban

# Check status
sudo fail2ban-client status
```

## Step 3: Application Security

### 3.1 JWT Configuration

#### Secure JWT Settings

```bash
# Update environment configuration with secure JWT settings
cat >> /opt/tli/config/tli-environment.env << 'EOF'

# JWT Security Configuration
JWT_SECRET=your-super-secret-jwt-key-that-is-at-least-64-characters-long-for-production-use-only
JWT_ISSUER=TLI.Identity.Production
JWT_AUDIENCE=TLI.Services.Production
JWT_EXPIRY_MINUTES=60
JWT_REFRESH_EXPIRY_DAYS=7
JWT_REQUIRE_HTTPS=true
JWT_VALIDATE_ISSUER=true
JWT_VALIDATE_AUDIENCE=true
JWT_VALIDATE_LIFETIME=true
JWT_CLOCK_SKEW_MINUTES=5

# Password Policy
PASSWORD_MIN_LENGTH=12
PASSWORD_REQUIRE_UPPERCASE=true
PASSWORD_REQUIRE_LOWERCASE=true
PASSWORD_REQUIRE_DIGITS=true
PASSWORD_REQUIRE_SPECIAL_CHARS=true
PASSWORD_MAX_AGE_DAYS=90

# Account Lockout Policy
ACCOUNT_LOCKOUT_ATTEMPTS=5
ACCOUNT_LOCKOUT_DURATION_MINUTES=30
ACCOUNT_LOCKOUT_RESET_MINUTES=60
EOF
```

### 3.2 API Rate Limiting

#### Configure Rate Limiting

```bash
# Create rate limiting configuration for nginx
sudo tee /etc/nginx/conf.d/rate-limiting.conf << 'EOF'
# Rate limiting zones
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/s;
limit_req_zone $binary_remote_addr zone=admin:10m rate=2r/s;

# Connection limiting
limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;
EOF

# Update nginx site configuration to use rate limiting
sudo tee -a /etc/nginx/sites-available/tli-ssl << 'EOF'

    # Rate limiting for API endpoints
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        limit_conn conn_limit_per_ip 10;
        proxy_pass http://127.0.0.1:5000;
        # ... other proxy settings ...
    }

    # Stricter rate limiting for authentication
    location /api/auth/ {
        limit_req zone=auth burst=10 nodelay;
        limit_conn conn_limit_per_ip 5;
        proxy_pass http://127.0.0.1:5000;
        # ... other proxy settings ...
    }

    # Very strict rate limiting for admin endpoints
    location /api/admin/ {
        limit_req zone=admin burst=5 nodelay;
        limit_conn conn_limit_per_ip 2;
        proxy_pass http://127.0.0.1:5000;
        # ... other proxy settings ...
    }
EOF

# Test and reload nginx
sudo nginx -t
sudo systemctl reload nginx
```

## Step 4: Database Security

### 4.1 PostgreSQL Security Hardening

#### Enhanced PostgreSQL Configuration

```bash
# Update PostgreSQL configuration for security
cat >> /opt/tli/config/postgres/postgresql.conf << 'EOF'

# Security Settings
ssl = on
ssl_cert_file = '/var/lib/postgresql/server.crt'
ssl_key_file = '/var/lib/postgresql/server.key'
ssl_ciphers = 'HIGH:MEDIUM:+3DES:!aNULL'
ssl_prefer_server_ciphers = on

# Connection Security
password_encryption = scram-sha-256
db_user_namespace = off

# Logging for Security
log_connections = on
log_disconnections = on
log_failed_connections = on
log_hostname = on
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '

# Statement Logging
log_statement = 'ddl'
log_min_duration_statement = 1000

# Lock Monitoring
log_lock_waits = on
deadlock_timeout = 1s

# Checkpoint and WAL Security
archive_mode = on
archive_command = 'cp %p /opt/tli/backups/postgres/wal/%f'
EOF

# Create secure pg_hba.conf
cat > /opt/tli/config/postgres/pg_hba.conf << 'EOF'
# PostgreSQL Client Authentication Configuration File
# TYPE  DATABASE        USER            ADDRESS                 METHOD

# Local connections
local   all             postgres                                peer
local   all             all                                     scram-sha-256

# IPv4 local connections
host    all             postgres        127.0.0.1/32            scram-sha-256

# Server 2 connections with SSL required
hostssl all             tli_admin       **********/32           scram-sha-256

# Admin network connections with SSL
hostssl all             all             ********/24             scram-sha-256

# Deny all other connections
host    all             all             0.0.0.0/0               reject
EOF

# Generate SSL certificates for PostgreSQL
openssl req -new -x509 -days 365 -nodes -text -out /opt/tli/config/postgres/server.crt -keyout /opt/tli/config/postgres/server.key -subj "/CN=tli-postgres"
chmod 600 /opt/tli/config/postgres/server.key
chmod 644 /opt/tli/config/postgres/server.crt
```

### 4.2 Database User Security

#### Create Role-Based Access

```bash
# Create database security script
cat > /opt/tli/config/postgres/setup-security.sql << 'EOF'
-- Create roles for different access levels
CREATE ROLE tli_readonly;
CREATE ROLE tli_readwrite;
CREATE ROLE tli_admin_role;

-- Grant permissions to roles
GRANT CONNECT ON DATABASE "TLI_Identity" TO tli_readonly;
GRANT USAGE ON SCHEMA public TO tli_readonly;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO tli_readonly;

GRANT CONNECT ON DATABASE "TLI_Identity" TO tli_readwrite;
GRANT USAGE ON SCHEMA public TO tli_readwrite;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO tli_readwrite;

GRANT ALL PRIVILEGES ON ALL DATABASES TO tli_admin_role;

-- Create service-specific users
CREATE USER tli_identity_service WITH PASSWORD 'TLI_Identity_Service_2024!@#';
CREATE USER tli_user_service WITH PASSWORD 'TLI_User_Service_2024!@#';
CREATE USER tli_readonly_user WITH PASSWORD 'TLI_Readonly_2024!@#';

-- Assign roles to users
GRANT tli_readwrite TO tli_identity_service;
GRANT tli_readwrite TO tli_user_service;
GRANT tli_readonly TO tli_readonly_user;
GRANT tli_admin_role TO tli_admin;

-- Set password policies
ALTER ROLE tli_identity_service VALID UNTIL '2025-12-31';
ALTER ROLE tli_user_service VALID UNTIL '2025-12-31';
ALTER ROLE tli_readonly_user VALID UNTIL '2025-12-31';
EOF

# Execute security setup
docker exec -i tli-postgres psql -U postgres < /opt/tli/config/postgres/setup-security.sql
```

## Step 5: Container Security

### 5.1 Docker Security Configuration

#### Secure Docker Daemon

```bash
# Create Docker daemon security configuration
sudo tee /etc/docker/daemon.json << 'EOF'
{
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  },
  "storage-driver": "overlay2",
  "data-root": "/opt/tli/data/docker",
  "userns-remap": "default",
  "no-new-privileges": true,
  "seccomp-profile": "/etc/docker/seccomp.json",
  "apparmor-profile": "docker-default",
  "selinux-enabled": false,
  "disable-legacy-registry": true,
  "live-restore": true,
  "userland-proxy": false,
  "experimental": false
}
EOF

# Restart Docker with new configuration
sudo systemctl restart docker
```

#### Container Security Scanning

```bash
# Install Docker security tools
sudo apt install -y docker-bench-security

# Run security benchmark
sudo docker run --rm --net host --pid host --userns host --cap-add audit_control \
  -e DOCKER_CONTENT_TRUST=$DOCKER_CONTENT_TRUST \
  -v /etc:/etc:ro \
  -v /usr/bin/containerd:/usr/bin/containerd:ro \
  -v /usr/bin/runc:/usr/bin/runc:ro \
  -v /usr/lib/systemd:/usr/lib/systemd:ro \
  -v /var/lib:/var/lib:ro \
  -v /var/run/docker.sock:/var/run/docker.sock:ro \
  --label docker_bench_security \
  docker/docker-bench-security

# Create container security script
cat > /opt/tli/container-security-check.sh << 'EOF'
#!/bin/bash

echo "=== Container Security Check ==="
echo "Date: $(date)"
echo

# Check for running containers with privileged mode
echo "1. Checking for privileged containers:"
docker ps --format "table {{.Names}}\t{{.Status}}" --filter "label=privileged=true"

# Check for containers running as root
echo "2. Checking containers running as root:"
for container in $(docker ps --format "{{.Names}}"); do
  user=$(docker exec $container whoami 2>/dev/null || echo "unknown")
  echo "  $container: $user"
done

# Check for containers with excessive capabilities
echo "3. Checking container capabilities:"
for container in $(docker ps --format "{{.Names}}"); do
  caps=$(docker inspect $container --format '{{.HostConfig.CapAdd}}' 2>/dev/null || echo "[]")
  if [ "$caps" != "[]" ] && [ "$caps" != "<no value>" ]; then
    echo "  $container has additional capabilities: $caps"
  fi
done

echo "=== Container Security Check Complete ==="
EOF

chmod +x /opt/tli/container-security-check.sh
```

## Step 6: Secret Management

### 6.1 Environment Variable Security

#### Secure Environment Configuration

```bash
# Create secure environment file with proper permissions
sudo tee /opt/tli/config/tli-secrets.env << 'EOF'
# Database Secrets
POSTGRES_ADMIN_PASSWORD=TLI_Postgres_Admin_2024!@#$%
POSTGRES_IDENTITY_PASSWORD=TLI_Identity_Service_2024!@#$%
POSTGRES_USER_PASSWORD=TLI_User_Service_2024!@#$%

# Redis Secrets
REDIS_PASSWORD=TLI_Redis_Cache_2024!@#$%

# RabbitMQ Secrets
RABBITMQ_ADMIN_PASSWORD=TLI_RabbitMQ_Admin_2024!@#$%
RABBITMQ_SERVICE_PASSWORD=TLI_RabbitMQ_Service_2024!@#$%

# JWT Secrets
JWT_SECRET=your-super-secret-jwt-key-that-is-at-least-64-characters-long-for-production-use-only-change-this
JWT_REFRESH_SECRET=your-refresh-token-secret-key-that-is-different-from-jwt-secret-and-equally-long

# External API Keys
GOOGLE_MAPS_API_KEY=your-google-maps-api-key-here
RAZORPAY_KEY_SECRET=your-razorpay-secret-key-here
SMS_PROVIDER_API_SECRET=your-sms-provider-secret-here
EMAIL_SMTP_PASSWORD=your-email-smtp-password-here

# Encryption Keys
DATA_ENCRYPTION_KEY=your-32-character-encryption-key-for-sensitive-data
FILE_ENCRYPTION_KEY=your-32-character-key-for-file-encryption
EOF

# Set strict permissions
sudo chmod 600 /opt/tli/config/tli-secrets.env
sudo chown tli:tli /opt/tli/config/tli-secrets.env

# Create secret rotation script
cat > /opt/tli/rotate-secrets.sh << 'EOF'
#!/bin/bash

echo "=== TLI Secret Rotation ==="
echo "Date: $(date)"
echo

# Generate new passwords
generate_password() {
  openssl rand -base64 32 | tr -d "=+/" | cut -c1-25
}

# Backup current secrets
cp /opt/tli/config/tli-secrets.env /opt/tli/config/tli-secrets.env.backup.$(date +%Y%m%d)

# Generate new secrets (manual process - requires service restart)
echo "New secrets generated. Manual update required:"
echo "POSTGRES_ADMIN_PASSWORD=$(generate_password)"
echo "REDIS_PASSWORD=$(generate_password)"
echo "RABBITMQ_ADMIN_PASSWORD=$(generate_password)"
echo "JWT_SECRET=$(openssl rand -base64 64 | tr -d '\n')"

echo "Please update /opt/tli/config/tli-secrets.env and restart services"
EOF

chmod +x /opt/tli/rotate-secrets.sh
```

### 6.2 Certificate Management

#### Automated Certificate Renewal

```bash
# Create certificate renewal script
cat > /opt/tli/renew-certificates.sh << 'EOF'
#!/bin/bash

echo "=== Certificate Renewal ==="
echo "Date: $(date)"

# Renew Let's Encrypt certificates
if command -v certbot &> /dev/null; then
  echo "Renewing Let's Encrypt certificates..."
  certbot renew --quiet

  # Copy renewed certificates
  if [ -f "/etc/letsencrypt/live/tli.yourdomain.com/fullchain.pem" ]; then
    cp /etc/letsencrypt/live/tli.yourdomain.com/fullchain.pem /opt/tli/config/ssl/tli.crt
    cp /etc/letsencrypt/live/tli.yourdomain.com/privkey.pem /opt/tli/config/ssl/tli.key
    chown tli:tli /opt/tli/config/ssl/tli.*
    chmod 600 /opt/tli/config/ssl/tli.key
    chmod 644 /opt/tli/config/ssl/tli.crt

    # Restart services to use new certificates
    systemctl reload nginx
    /opt/tli/manage-applications.sh restart

    echo "Certificates renewed and services restarted"
  fi
else
  echo "Certbot not found. Skipping certificate renewal."
fi

echo "=== Certificate Renewal Complete ==="
EOF

chmod +x /opt/tli/renew-certificates.sh

# Add to cron for monthly renewal
(crontab -l 2>/dev/null; echo "0 3 1 * * /opt/tli/renew-certificates.sh >> /opt/tli/logs/cert-renewal.log 2>&1") | crontab -
```

## Step 7: Audit and Compliance

### 7.1 Security Audit Logging

#### Configure Comprehensive Audit Logging

```bash
# Install auditd for system-level auditing
sudo apt install -y auditd audispd-plugins

# Configure audit rules
sudo tee /etc/audit/rules.d/tli-security.rules << 'EOF'
# TLI Security Audit Rules

# Monitor file access to sensitive directories
-w /opt/tli/config/ -p wa -k tli_config_access
-w /opt/tli/data/ -p wa -k tli_data_access
-w /etc/ssl/ -p wa -k ssl_config_access

# Monitor user authentication
-w /var/log/auth.log -p wa -k auth_log
-w /etc/passwd -p wa -k passwd_changes
-w /etc/group -p wa -k group_changes
-w /etc/shadow -p wa -k shadow_changes

# Monitor network configuration
-w /etc/network/ -p wa -k network_config
-w /etc/ufw/ -p wa -k firewall_config

# Monitor Docker daemon
-w /var/lib/docker/ -p wa -k docker_changes
-w /etc/docker/ -p wa -k docker_config

# Monitor systemd services
-w /etc/systemd/ -p wa -k systemd_config

# Monitor sudo usage
-w /var/log/sudo.log -p wa -k sudo_log

# Monitor file permission changes
-a always,exit -F arch=b64 -S chmod -S fchmod -S fchmodat -F auid>=1000 -F auid!=4294967295 -k perm_mod
-a always,exit -F arch=b32 -S chmod -S fchmod -S fchmodat -F auid>=1000 -F auid!=4294967295 -k perm_mod

# Monitor file ownership changes
-a always,exit -F arch=b64 -S chown -S fchown -S fchownat -S lchown -F auid>=1000 -F auid!=4294967295 -k perm_mod
-a always,exit -F arch=b32 -S chown -S fchown -S fchownat -S lchown -F auid>=1000 -F auid!=4294967295 -k perm_mod
EOF

# Restart auditd
sudo systemctl restart auditd
sudo systemctl enable auditd
```

### 7.2 Security Monitoring

#### Create Security Monitoring Script

```bash
cat > /opt/tli/security-monitor.sh << 'EOF'
#!/bin/bash

echo "=== TLI Security Monitoring ==="
echo "Date: $(date)"
echo

# Check for failed login attempts
echo "1. Failed Login Attempts (last 24 hours):"
grep "Failed password" /var/log/auth.log | grep "$(date --date='1 day ago' '+%b %d')" | wc -l

# Check for successful logins
echo "2. Successful Logins (last 24 hours):"
grep "Accepted password" /var/log/auth.log | grep "$(date --date='1 day ago' '+%b %d')" | wc -l

# Check UFW denied connections
echo "3. Firewall Denied Connections (last hour):"
grep "UFW BLOCK" /var/log/ufw.log | grep "$(date '+%b %d %H')" | wc -l

# Check for suspicious processes
echo "4. Checking for suspicious processes:"
ps aux | grep -E "(nc|netcat|nmap|tcpdump)" | grep -v grep

# Check for unusual network connections
echo "5. Unusual Network Connections:"
netstat -tuln | grep -E ":22[0-9][0-9]|:3[0-9][0-9][0-9]|:4[0-9][0-9][0-9]"

# Check Docker container security
echo "6. Docker Container Security:"
/opt/tli/container-security-check.sh

# Check SSL certificate expiry
echo "7. SSL Certificate Expiry:"
if [ -f "/opt/tli/config/ssl/tli.crt" ]; then
  expiry_date=$(openssl x509 -enddate -noout -in /opt/tli/config/ssl/tli.crt | cut -d= -f2)
  expiry_epoch=$(date -d "$expiry_date" +%s)
  current_epoch=$(date +%s)
  days_until_expiry=$(( (expiry_epoch - current_epoch) / 86400 ))
  echo "  Certificate expires in $days_until_expiry days"

  if [ $days_until_expiry -lt 30 ]; then
    echo "  WARNING: Certificate expires soon!"
  fi
fi

echo
echo "=== Security Monitoring Complete ==="
EOF

chmod +x /opt/tli/security-monitor.sh

# Add to cron for daily security monitoring
(crontab -l 2>/dev/null; echo "0 6 * * * /opt/tli/security-monitor.sh >> /opt/tli/logs/security-monitor.log 2>&1") | crontab -
```

## Step 8: Incident Response

### 8.1 Security Incident Response Plan

#### Create Incident Response Script

```bash
cat > /opt/tli/incident-response.sh << 'EOF'
#!/bin/bash

INCIDENT_TYPE="$1"
SEVERITY="$2"

if [ -z "$INCIDENT_TYPE" ] || [ -z "$SEVERITY" ]; then
  echo "Usage: $0 <incident_type> <severity>"
  echo "Incident Types: breach, malware, dos, unauthorized_access"
  echo "Severity: low, medium, high, critical"
  exit 1
fi

echo "=== TLI Security Incident Response ==="
echo "Date: $(date)"
echo "Incident Type: $INCIDENT_TYPE"
echo "Severity: $SEVERITY"
echo

# Create incident directory
INCIDENT_DIR="/opt/tli/incidents/$(date +%Y%m%d_%H%M%S)_${INCIDENT_TYPE}"
mkdir -p "$INCIDENT_DIR"

# Collect system information
echo "Collecting system information..."
ps aux > "$INCIDENT_DIR/processes.txt"
netstat -tuln > "$INCIDENT_DIR/network_connections.txt"
ss -tuln > "$INCIDENT_DIR/socket_stats.txt"
docker ps -a > "$INCIDENT_DIR/docker_containers.txt"
docker logs --tail 1000 $(docker ps -q) > "$INCIDENT_DIR/docker_logs.txt" 2>&1

# Collect logs
echo "Collecting logs..."
cp /var/log/auth.log "$INCIDENT_DIR/"
cp /var/log/syslog "$INCIDENT_DIR/"
cp /var/log/ufw.log "$INCIDENT_DIR/"
cp -r /opt/tli/logs "$INCIDENT_DIR/"

# Collect audit logs
if command -v ausearch &> /dev/null; then
  ausearch -ts today > "$INCIDENT_DIR/audit_today.txt"
fi

# Take action based on severity
case $SEVERITY in
  "critical")
    echo "CRITICAL incident detected. Taking immediate action..."
    # Block all external traffic except SSH
    sudo ufw --force reset
    sudo ufw default deny incoming
    sudo ufw default deny outgoing
    sudo ufw allow ssh
    sudo ufw enable

    # Stop all TLI services
    /opt/tli/manage-applications.sh stop

    echo "All services stopped and network restricted."
    echo "Manual intervention required."
    ;;
  "high")
    echo "HIGH severity incident. Implementing protective measures..."
    # Enable stricter firewall rules
    sudo ufw limit ssh

    # Restart services with monitoring
    /opt/tli/manage-applications.sh restart
    ;;
  "medium"|"low")
    echo "Incident logged. Continuing monitoring..."
    ;;
esac

# Create incident report
cat > "$INCIDENT_DIR/incident_report.txt" << EOF
TLI Security Incident Report
============================
Date: $(date)
Incident Type: $INCIDENT_TYPE
Severity: $SEVERITY
System: $(hostname)
User: $(whoami)

Description:
[Manual description required]

Actions Taken:
[Manual description required]

Next Steps:
[Manual description required]

EOF

echo "Incident response complete. Report saved to: $INCIDENT_DIR"
echo "Please review and complete the incident report."
EOF

chmod +x /opt/tli/incident-response.sh
```

## Step 9: Security Testing

### 9.1 Automated Security Tests

#### Create Security Test Suite

```bash
cat > /opt/tli/security-tests.sh << 'EOF'
#!/bin/bash

echo "=== TLI Security Test Suite ==="
echo "Date: $(date)"
echo

# Test 1: SSL/TLS Configuration
echo "1. Testing SSL/TLS Configuration:"
if command -v openssl &> /dev/null; then
  echo "  - Testing SSL certificate:"
  echo | openssl s_client -connect localhost:443 -servername localhost 2>/dev/null | grep -E "Verify return code|subject|issuer"

  echo "  - Testing SSL protocols:"
  for protocol in ssl3 tls1 tls1_1 tls1_2 tls1_3; do
    result=$(echo | openssl s_client -$protocol -connect localhost:443 2>&1 | grep -o "Protocol.*")
    echo "    $protocol: $result"
  done
fi

# Test 2: Firewall Configuration
echo
echo "2. Testing Firewall Configuration:"
sudo ufw status verbose | head -20

# Test 3: Authentication Security
echo
echo "3. Testing Authentication Security:"
echo "  - Testing login endpoint with invalid credentials:"
response=$(curl -s -w "%{http_code}" -o /dev/null -X POST \
  "http://localhost:5001/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"wrongpassword"}' 2>/dev/null)
echo "    Response code: $response (should be 401)"

# Test 4: SQL Injection Protection
echo
echo "4. Testing SQL Injection Protection:"
response=$(curl -s -w "%{http_code}" -o /dev/null -X POST \
  "http://localhost:5001/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email":"admin'\''OR 1=1--","password":"anything"}' 2>/dev/null)
echo "  - SQL injection attempt response: $response (should be 400 or 401)"

# Test 5: Rate Limiting
echo
echo "5. Testing Rate Limiting:"
echo "  - Sending multiple requests to test rate limiting..."
for i in {1..15}; do
  response=$(curl -s -w "%{http_code}" -o /dev/null "http://localhost:5000/health" 2>/dev/null)
  if [ "$response" = "429" ]; then
    echo "    Rate limiting triggered at request $i (HTTP 429)"
    break
  fi
done

# Test 6: Security Headers
echo
echo "6. Testing Security Headers:"
if command -v curl &> /dev/null; then
  headers=$(curl -s -I "https://localhost:443/" 2>/dev/null)
  echo "  - Checking security headers:"
  echo "$headers" | grep -E "(Strict-Transport-Security|X-Content-Type-Options|X-Frame-Options|X-XSS-Protection|Content-Security-Policy)" || echo "    Some security headers missing"
fi

# Test 7: Container Security
echo
echo "7. Testing Container Security:"
/opt/tli/container-security-check.sh

echo
echo "=== Security Test Suite Complete ==="
EOF

chmod +x /opt/tli/security-tests.sh
```

## Step 10: Compliance and Documentation

### 10.1 Security Compliance Checklist

#### Create Compliance Verification Script

```bash
cat > /opt/tli/compliance-check.sh << 'EOF'
#!/bin/bash

echo "=== TLI Security Compliance Check ==="
echo "Date: $(date)"
echo

COMPLIANCE_SCORE=0
TOTAL_CHECKS=0

check_compliance() {
  local description="$1"
  local command="$2"
  local expected="$3"

  echo "Checking: $description"
  result=$(eval "$command" 2>/dev/null)

  if [[ "$result" == *"$expected"* ]]; then
    echo "  ✓ PASS"
    ((COMPLIANCE_SCORE++))
  else
    echo "  ✗ FAIL"
  fi

  ((TOTAL_CHECKS++))
  echo
}

# Compliance Checks
check_compliance "Firewall is enabled" "sudo ufw status" "Status: active"
check_compliance "SSH rate limiting configured" "sudo ufw status | grep 'LIMIT'" "LIMIT"
check_compliance "SSL certificate present" "ls /opt/tli/config/ssl/tli.crt" "tli.crt"
check_compliance "Fail2Ban is running" "systemctl is-active fail2ban" "active"
check_compliance "Audit daemon is running" "systemctl is-active auditd" "active"
check_compliance "Docker security configured" "grep userns-remap /etc/docker/daemon.json" "userns-remap"
check_compliance "Password policy configured" "grep PASSWORD_MIN_LENGTH /opt/tli/config/tli-environment.env" "PASSWORD_MIN_LENGTH"
check_compliance "JWT security configured" "grep JWT_REQUIRE_HTTPS /opt/tli/config/tli-environment.env" "JWT_REQUIRE_HTTPS"
check_compliance "Database SSL enabled" "grep 'ssl = on' /opt/tli/config/postgres/postgresql.conf" "ssl = on"
check_compliance "Redis password protected" "grep requirepass /opt/tli/config/redis/redis.conf" "requirepass"

# Calculate compliance percentage
COMPLIANCE_PERCENTAGE=$((COMPLIANCE_SCORE * 100 / TOTAL_CHECKS))

echo "=== Compliance Summary ==="
echo "Passed: $COMPLIANCE_SCORE/$TOTAL_CHECKS"
echo "Compliance Score: $COMPLIANCE_PERCENTAGE%"

if [ $COMPLIANCE_PERCENTAGE -ge 90 ]; then
  echo "Status: EXCELLENT - Ready for production"
elif [ $COMPLIANCE_PERCENTAGE -ge 80 ]; then
  echo "Status: GOOD - Minor improvements needed"
elif [ $COMPLIANCE_PERCENTAGE -ge 70 ]; then
  echo "Status: FAIR - Several improvements needed"
else
  echo "Status: POOR - Major security improvements required"
fi

echo "=== Compliance Check Complete ==="
EOF

chmod +x /opt/tli/compliance-check.sh
```

## Security Maintenance Schedule

### Daily Tasks

- Monitor security logs
- Check failed login attempts
- Verify service health

### Weekly Tasks

- Run security test suite
- Update security patches
- Review audit logs

### Monthly Tasks

- Rotate passwords and secrets
- Update SSL certificates
- Security compliance check
- Penetration testing

### Quarterly Tasks

- Full security audit
- Update security policies
- Review and update firewall rules
- Security training for team

## Emergency Contacts

- **Security Team**: <EMAIL>
- **System Administrator**: <EMAIL>
- **Emergency Response**: +91-XXXX-XXXX-XX

## Next Steps

1. **Implement all security configurations** in this guide
2. **Test security measures** using the provided test scripts
3. **Schedule regular security monitoring** and maintenance
4. **Train team members** on security procedures
5. **Document any customizations** for your environment

```

```
