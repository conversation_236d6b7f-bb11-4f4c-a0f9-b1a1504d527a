using BenchmarkDotNet.Attributes;
using BenchmarkDotNet.Configs;
using BenchmarkDotNet.Jobs;
using BenchmarkDotNet.Toolchains.InProcess.Emit;
using Microsoft.Extensions.DependencyInjection;
using NetworkFleetManagement.Domain.Entities;
using NetworkFleetManagement.Domain.Repositories;
using NetworkFleetManagement.Infrastructure.Performance;
using NetworkFleetManagement.PerformanceTests.Infrastructure;

namespace NetworkFleetManagement.PerformanceTests.Benchmarks;

[Config(typeof(BenchmarkConfig))]
[MemoryDiagnoser]
[SimpleJob(RuntimeMoniker.Net80)]
public class QueryBenchmarks : PerformanceTestBase
{
    private IPreferredPartnerRepository _repository = null!;
    private IQueryOptimizationService _optimizationService = null!;
    private List<Guid> _testUserIds = null!;
    private List<Guid> _testPartnerIds = null!;

    [GlobalSetup]
    public async Task GlobalSetup()
    {
        await InitializeAsync();
        
        using var scope = ServiceProvider.CreateScope();
        _repository = scope.ServiceProvider.GetRequiredService<IPreferredPartnerRepository>();
        _optimizationService = scope.ServiceProvider.GetRequiredService<IQueryOptimizationService>();
        
        await SeedBenchmarkDataAsync();
    }

    [GlobalCleanup]
    public async Task GlobalCleanup()
    {
        await DisposeAsync();
    }

    [Benchmark(Baseline = true)]
    public async Task<IEnumerable<PreferredPartner>> GetPreferredPartners_Standard()
    {
        var userId = _testUserIds[Random.Shared.Next(_testUserIds.Count)];
        return await _repository.GetByUserIdAsync(userId);
    }

    [Benchmark]
    public async Task<IEnumerable<PreferredPartner>> GetPreferredPartners_Optimized()
    {
        var userId = _testUserIds[Random.Shared.Next(_testUserIds.Count)];
        return await _optimizationService.GetOptimizedPreferredPartnersAsync(
            userId, 
            activeOnly: true, 
            limit: 20);
    }

    [Benchmark]
    public async Task<IEnumerable<PreferredPartner>> GetPreferredPartners_ByType()
    {
        var userId = _testUserIds[Random.Shared.Next(_testUserIds.Count)];
        return await _repository.GetByUserAndPartnerTypeAsync(userId, PreferredPartnerType.Carrier);
    }

    [Benchmark]
    public async Task<IEnumerable<PreferredPartner>> GetAutoAssignPartners_Standard()
    {
        var userId = _testUserIds[Random.Shared.Next(_testUserIds.Count)];
        return await _repository.GetAutoAssignEligiblePartnersAsync(
            userId, 
            PreferredPartnerType.Carrier, 
            "Route1", 
            "Dry Van");
    }

    [Benchmark]
    public async Task<IEnumerable<PreferredPartner>> GetAutoAssignPartners_Optimized()
    {
        var userId = _testUserIds[Random.Shared.Next(_testUserIds.Count)];
        return await _optimizationService.GetOptimizedAutoAssignPartnersAsync(
            userId, 
            PreferredPartnerType.Carrier, 
            "Route1", 
            "Dry Van", 
            4.0m);
    }

    [Benchmark]
    public async Task<PreferredPartner?> GetPartnerById_Cached()
    {
        var partnerId = _testPartnerIds[Random.Shared.Next(_testPartnerIds.Count)];
        return await _repository.GetByIdAsync(partnerId);
    }

    [Benchmark]
    public async Task<IEnumerable<PreferredPartner>> GetActivePartners_Cached()
    {
        var userId = _testUserIds[Random.Shared.Next(_testUserIds.Count)];
        return await _repository.GetActiveByUserIdAsync(userId);
    }

    [Benchmark]
    public async Task<int> GetPartnerCounts_Aggregated()
    {
        var userId = _testUserIds[Random.Shared.Next(_testUserIds.Count)];
        var totalCount = await _repository.GetTotalPreferredPartnersCountAsync(userId);
        var activeCount = await _repository.GetActivePreferredPartnersCountAsync(userId);
        return totalCount + activeCount;
    }

    [Benchmark]
    [Arguments(PreferredPartnerType.Carrier)]
    [Arguments(PreferredPartnerType.Broker)]
    [Arguments(PreferredPartnerType.Transporter)]
    public async Task<IEnumerable<PreferredPartner>> GetPartnersByType_Parameterized(PreferredPartnerType partnerType)
    {
        var userId = _testUserIds[Random.Shared.Next(_testUserIds.Count)];
        return await _repository.GetByUserIdAndPartnerTypeAsync(userId, partnerType, activeOnly: true);
    }

    [Benchmark]
    [Arguments("Route1", "Dry Van")]
    [Arguments("Route2", "Refrigerated")]
    [Arguments("Route3", "Flatbed")]
    public async Task<IEnumerable<PreferredPartner>> GetAutoAssignPartners_ByRouteAndLoad(string route, string loadType)
    {
        var userId = _testUserIds[Random.Shared.Next(_testUserIds.Count)];
        return await _repository.GetAutoAssignEligiblePartnersAsync(
            userId, 
            PreferredPartnerType.Carrier, 
            route, 
            loadType);
    }

    private async Task SeedBenchmarkDataAsync()
    {
        _testUserIds = new List<Guid>();
        _testPartnerIds = new List<Guid>();

        const int userCount = 50;
        const int partnersPerUser = 20;

        for (int i = 0; i < userCount; i++)
        {
            var userId = Guid.NewGuid();
            _testUserIds.Add(userId);

            for (int j = 0; j < partnersPerUser; j++)
            {
                var partner = PreferredPartner.Create(
                    userId,
                    Guid.NewGuid(),
                    (PreferredPartnerType)(j % 4),
                    (PreferenceLevel)(j % 3),
                    j + 1);

                partner.Activate();
                partner.UpdateAutoAssignSettings(
                    j % 3 == 0, // 33% have auto-assign enabled
                    4.0m + (j * 0.05m),
                    new List<string> { $"Route{(j % 5) + 1}" },
                    new List<string> { j % 2 == 0 ? "Dry Van" : "Refrigerated" },
                    new List<string>(),
                    new List<string>());

                // Update performance metrics with realistic data
                partner.UpdatePerformanceMetrics(
                    3.5m + (decimal)(Random.Shared.NextDouble() * 1.5), // 3.5 to 5.0 rating
                    0.7m + (decimal)(Random.Shared.NextDouble() * 0.3), // 70% to 100% on-time
                    4.0m + (decimal)(Random.Shared.NextDouble() * 1.0), // 4.0 to 5.0 quality
                    4.0m + (decimal)(Random.Shared.NextDouble() * 1.0), // 4.0 to 5.0 communication
                    (decimal)(Random.Shared.NextDouble() * 12 + 2)); // 2 to 14 hours response

                await _repository.AddAsync(partner);
                _testPartnerIds.Add(partner.Id);
            }
        }

        Logger.LogInformation("Seeded {UserCount} users with {PartnersPerUser} partners each for benchmarking",
            userCount, partnersPerUser);
    }
}

public class BenchmarkConfig : ManualConfig
{
    public BenchmarkConfig()
    {
        AddJob(Job.Default.WithToolchain(InProcessEmitToolchain.Instance));
        WithOptions(ConfigOptions.DisableOptimizationsValidator);
    }
}

[Config(typeof(BenchmarkConfig))]
[MemoryDiagnoser]
[SimpleJob(RuntimeMoniker.Net80)]
public class CacheBenchmarks : PerformanceTestBase
{
    private IPreferredPartnerRepository _repository = null!;
    private List<Guid> _testUserIds = null!;
    private List<Guid> _testPartnerIds = null!;

    [GlobalSetup]
    public async Task GlobalSetup()
    {
        await InitializeAsync();
        
        using var scope = ServiceProvider.CreateScope();
        _repository = scope.ServiceProvider.GetRequiredService<IPreferredPartnerRepository>();
        
        await SeedCacheTestDataAsync();
        await WarmupCacheAsync();
    }

    [GlobalCleanup]
    public async Task GlobalCleanup()
    {
        await DisposeAsync();
    }

    [Benchmark(Baseline = true)]
    public async Task<PreferredPartner?> GetById_CacheHit()
    {
        var partnerId = _testPartnerIds[Random.Shared.Next(_testPartnerIds.Count)];
        return await _repository.GetByIdAsync(partnerId);
    }

    [Benchmark]
    public async Task<PreferredPartner?> GetById_CacheMiss()
    {
        var partnerId = Guid.NewGuid(); // Always a cache miss
        return await _repository.GetByIdAsync(partnerId);
    }

    [Benchmark]
    public async Task<IEnumerable<PreferredPartner>> GetByUserId_CacheHit()
    {
        var userId = _testUserIds[Random.Shared.Next(_testUserIds.Count)];
        return await _repository.GetByUserIdAsync(userId);
    }

    [Benchmark]
    public async Task<IEnumerable<PreferredPartner>> GetActiveByUserId_CacheHit()
    {
        var userId = _testUserIds[Random.Shared.Next(_testUserIds.Count)];
        return await _repository.GetActiveByUserIdAsync(userId);
    }

    [Benchmark]
    public async Task<int> GetCounts_CacheHit()
    {
        var userId = _testUserIds[Random.Shared.Next(_testUserIds.Count)];
        var total = await _repository.GetTotalPreferredPartnersCountAsync(userId);
        var active = await _repository.GetActivePreferredPartnersCountAsync(userId);
        return total + active;
    }

    private async Task SeedCacheTestDataAsync()
    {
        _testUserIds = new List<Guid>();
        _testPartnerIds = new List<Guid>();

        const int userCount = 20;
        const int partnersPerUser = 10;

        for (int i = 0; i < userCount; i++)
        {
            var userId = Guid.NewGuid();
            _testUserIds.Add(userId);

            for (int j = 0; j < partnersPerUser; j++)
            {
                var partner = PreferredPartner.Create(
                    userId,
                    Guid.NewGuid(),
                    PreferredPartnerType.Carrier,
                    PreferenceLevel.High,
                    j + 1);

                partner.Activate();
                await _repository.AddAsync(partner);
                _testPartnerIds.Add(partner.Id);
            }
        }
    }

    private async Task WarmupCacheAsync()
    {
        // Warm up cache by accessing all test data
        foreach (var userId in _testUserIds)
        {
            await _repository.GetByUserIdAsync(userId);
            await _repository.GetActiveByUserIdAsync(userId);
            await _repository.GetTotalPreferredPartnersCountAsync(userId);
            await _repository.GetActivePreferredPartnersCountAsync(userId);
        }

        foreach (var partnerId in _testPartnerIds.Take(10)) // Warm up some partner IDs
        {
            await _repository.GetByIdAsync(partnerId);
        }
    }
}
