using AutoMapper;
using FluentAssertions;
using Moq;
using NetworkFleetManagement.Application.Commands.Carriers;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Application.Mappings;
using NetworkFleetManagement.Domain.Entities;
using NetworkFleetManagement.Domain.Repositories;
using Shared.Messaging;
using Xunit;

namespace NetworkFleetManagement.Tests.Application.Commands;

public class CreateCarrierCommandHandlerTests
{
    private readonly Mock<ICarrierRepository> _carrierRepositoryMock;
    private readonly Mock<IUnitOfWork> _unitOfWorkMock;
    private readonly Mock<IMessageBroker> _messageBrokerMock;
    private readonly IMapper _mapper;
    private readonly CreateCarrierCommandHandler _handler;

    public CreateCarrierCommandHandlerTests()
    {
        _carrierRepositoryMock = new Mock<ICarrierRepository>();
        _unitOfWorkMock = new Mock<IUnitOfWork>();
        _messageBrokerMock = new Mock<IMessageBroker>();

        var mapperConfig = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<NetworkFleetMappingProfile>();
        });
        _mapper = mapperConfig.CreateMapper();

        _handler = new CreateCarrierCommandHandler(
            _carrierRepositoryMock.Object,
            _unitOfWorkMock.Object,
            _mapper,
            _messageBrokerMock.Object);
    }

    [Fact]
    public async Task Handle_WithValidRequest_ShouldCreateCarrier()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var createCarrierDto = new CreateCarrierDto
        {
            UserId = userId,
            CompanyName = "Test Logistics",
            ContactPersonName = "John Doe",
            Email = "<EMAIL>",
            PhoneNumber = "+**********",
            BusinessLicenseNumber = "BL123456",
            TaxIdentificationNumber = "TIN123456",
            Notes = "Test carrier"
        };

        var command = new CreateCarrierCommand(createCarrierDto);

        _carrierRepositoryMock
            .Setup(x => x.GetByUserIdAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((Carrier?)null);

        _unitOfWorkMock
            .Setup(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(1);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.UserId.Should().Be(userId);
        result.CompanyName.Should().Be("Test Logistics");
        result.ContactPersonName.Should().Be("John Doe");
        result.Email.Should().Be("<EMAIL>");
        result.PhoneNumber.Should().Be("+**********");

        _carrierRepositoryMock.Verify(x => x.Add(It.IsAny<Carrier>()), Times.Once);
        _unitOfWorkMock.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
        _messageBrokerMock.Verify(x => x.PublishAsync("carrier.created", It.IsAny<object>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithExistingCarrierForUser_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var existingCarrier = new Carrier(userId, "Existing Logistics", "Jane Doe", "<EMAIL>", "+9876543210");
        
        var createCarrierDto = new CreateCarrierDto
        {
            UserId = userId,
            CompanyName = "Test Logistics",
            ContactPersonName = "John Doe",
            Email = "<EMAIL>",
            PhoneNumber = "+**********"
        };

        var command = new CreateCarrierCommand(createCarrierDto);

        _carrierRepositoryMock
            .Setup(x => x.GetByUserIdAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingCarrier);

        // Act & Assert
        var action = async () => await _handler.Handle(command, CancellationToken.None);
        await action.Should().ThrowAsync<InvalidOperationException>()
            .WithMessage($"Carrier already exists for user {userId}");

        _carrierRepositoryMock.Verify(x => x.Add(It.IsAny<Carrier>()), Times.Never);
        _unitOfWorkMock.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Never);
        _messageBrokerMock.Verify(x => x.PublishAsync(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task Handle_WithBusinessAddress_ShouldCreateCarrierWithAddress()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var createCarrierDto = new CreateCarrierDto
        {
            UserId = userId,
            CompanyName = "Test Logistics",
            ContactPersonName = "John Doe",
            Email = "<EMAIL>",
            PhoneNumber = "+**********",
            BusinessAddress = new LocationDto
            {
                Latitude = 12.9716,
                Longitude = 77.5946,
                Address = "123 Test Street",
                City = "Bangalore",
                State = "Karnataka",
                Country = "India",
                PostalCode = "560001"
            }
        };

        var command = new CreateCarrierCommand(createCarrierDto);

        _carrierRepositoryMock
            .Setup(x => x.GetByUserIdAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((Carrier?)null);

        _unitOfWorkMock
            .Setup(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(1);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.BusinessAddress.Should().NotBeNull();
        result.BusinessAddress!.Latitude.Should().Be(12.9716);
        result.BusinessAddress.Longitude.Should().Be(77.5946);
        result.BusinessAddress.City.Should().Be("Bangalore");

        _carrierRepositoryMock.Verify(x => x.Add(It.Is<Carrier>(c => c.BusinessAddress != null)), Times.Once);
    }
}
