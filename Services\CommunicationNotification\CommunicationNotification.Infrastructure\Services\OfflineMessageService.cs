using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Domain.Entities;
using CommunicationNotification.Domain.Enums;
using Microsoft.Extensions.Logging;

namespace CommunicationNotification.Infrastructure.Services;

/// <summary>
/// Service for handling offline messages and notifications
/// </summary>
public interface IOfflineMessageService
{
    /// <summary>
    /// Store message for offline user
    /// </summary>
    Task StoreOfflineMessageAsync(Guid userId, Message message, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get offline messages for user
    /// </summary>
    Task<List<OfflineMessage>> GetOfflineMessagesAsync(Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Mark offline messages as delivered
    /// </summary>
    Task MarkOfflineMessagesAsDeliveredAsync(Guid userId, List<Guid> messageIds, CancellationToken cancellationToken = default);

    /// <summary>
    /// Clean up old offline messages
    /// </summary>
    Task CleanupOldOfflineMessagesAsync(TimeSpan maxAge, CancellationToken cancellationToken = default);

    /// <summary>
    /// Send push notification for offline message
    /// </summary>
    Task SendOfflineNotificationAsync(Guid userId, Message message, CancellationToken cancellationToken = default);
}

/// <summary>
/// Offline message entity
/// </summary>
public class OfflineMessage
{
    public Guid Id { get; set; }
    public Guid UserId { get; set; }
    public Guid MessageId { get; set; }
    public Guid ConversationId { get; set; }
    public Guid SenderId { get; set; }
    public string Content { get; set; } = string.Empty;
    public MessageType MessageType { get; set; }
    public DateTime CreatedAt { get; set; }
    public bool IsDelivered { get; set; }
    public DateTime? DeliveredAt { get; set; }
    public Dictionary<string, string> Metadata { get; set; } = new();
}

/// <summary>
/// Implementation of offline message service
/// </summary>
public class OfflineMessageService : IOfflineMessageService
{
    private readonly IOfflineMessageRepository _offlineMessageRepository;
    private readonly IConnectionTrackingService _connectionTracker;
    private readonly INotificationService _notificationService;
    private readonly IUserPreferenceRepository _userPreferenceRepository;
    private readonly ILogger<OfflineMessageService> _logger;

    public OfflineMessageService(
        IOfflineMessageRepository offlineMessageRepository,
        IConnectionTrackingService connectionTracker,
        INotificationService notificationService,
        IUserPreferenceRepository userPreferenceRepository,
        ILogger<OfflineMessageService> logger)
    {
        _offlineMessageRepository = offlineMessageRepository;
        _connectionTracker = connectionTracker;
        _notificationService = notificationService;
        _userPreferenceRepository = userPreferenceRepository;
        _logger = logger;
    }

    public async Task StoreOfflineMessageAsync(Guid userId, Message message, CancellationToken cancellationToken = default)
    {
        try
        {
            // Check if user is online
            var isOnline = await _connectionTracker.GetUserConnectionsAsync(userId);
            if (isOnline.Any())
            {
                // User is online, no need to store offline message
                return;
            }

            var offlineMessage = new OfflineMessage
            {
                Id = Guid.NewGuid(),
                UserId = userId,
                MessageId = message.Id,
                ConversationId = message.ConversationThreadId,
                SenderId = message.SenderId,
                Content = message.Content.Body,
                MessageType = message.MessageType,
                CreatedAt = DateTime.UtcNow,
                IsDelivered = false,
                Metadata = message.Metadata
            };

            await _offlineMessageRepository.AddAsync(offlineMessage, cancellationToken);

            // Send push notification for offline message
            await SendOfflineNotificationAsync(userId, message, cancellationToken);

            _logger.LogInformation("Offline message stored for user {UserId}, message {MessageId}", userId, message.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to store offline message for user {UserId}", userId);
            throw;
        }
    }

    public async Task<List<OfflineMessage>> GetOfflineMessagesAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _offlineMessageRepository.GetByUserIdAsync(userId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get offline messages for user {UserId}", userId);
            throw;
        }
    }

    public async Task MarkOfflineMessagesAsDeliveredAsync(Guid userId, List<Guid> messageIds, CancellationToken cancellationToken = default)
    {
        try
        {
            await _offlineMessageRepository.MarkAsDeliveredAsync(userId, messageIds, cancellationToken);
            _logger.LogDebug("Marked {Count} offline messages as delivered for user {UserId}", messageIds.Count, userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to mark offline messages as delivered for user {UserId}", userId);
            throw;
        }
    }

    public async Task CleanupOldOfflineMessagesAsync(TimeSpan maxAge, CancellationToken cancellationToken = default)
    {
        try
        {
            var cutoffDate = DateTime.UtcNow - maxAge;
            var deletedCount = await _offlineMessageRepository.DeleteOldMessagesAsync(cutoffDate, cancellationToken);
            
            _logger.LogInformation("Cleaned up {Count} old offline messages older than {CutoffDate}", deletedCount, cutoffDate);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to cleanup old offline messages");
            throw;
        }
    }

    public async Task SendOfflineNotificationAsync(Guid userId, Message message, CancellationToken cancellationToken = default)
    {
        try
        {
            // Get user preferences to determine if they want offline notifications
            var userPreference = await _userPreferenceRepository.GetByUserIdAsync(userId, cancellationToken);
            
            if (userPreference?.NotificationSettings.PushEnabled != true)
            {
                return; // User has disabled push notifications
            }

            // Create notification request for offline message
            var notificationRequest = new NotificationRequest
            {
                UserId = userId,
                MessageType = MessageType.ChatMessage,
                Content = Domain.ValueObjects.MessageContent.Create(
                    "New Message",
                    $"You have a new message: {TruncateMessage(message.Content.Body)}",
                    userPreference.NotificationSettings.PreferredLanguage),
                Priority = Priority.Normal,
                PreferredChannel = NotificationChannel.Push,
                RelatedEntityId = message.ConversationThreadId,
                RelatedEntityType = "Conversation",
                Metadata = new Dictionary<string, string>
                {
                    ["conversationId"] = message.ConversationThreadId.ToString(),
                    ["messageId"] = message.Id.ToString(),
                    ["senderId"] = message.SenderId.ToString(),
                    ["messageType"] = message.MessageType.ToString()
                }
            };

            var result = await _notificationService.SendNotificationAsync(notificationRequest, cancellationToken);
            
            if (result.IsSuccess)
            {
                _logger.LogDebug("Offline notification sent for user {UserId}, message {MessageId}", userId, message.Id);
            }
            else
            {
                _logger.LogWarning("Failed to send offline notification for user {UserId}: {Error}", userId, result.ErrorMessage);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send offline notification for user {UserId}", userId);
            // Don't rethrow - offline notification failure shouldn't break message storage
        }
    }

    private static string TruncateMessage(string message, int maxLength = 100)
    {
        if (string.IsNullOrEmpty(message))
            return string.Empty;

        return message.Length <= maxLength ? message : message[..maxLength] + "...";
    }
}

/// <summary>
/// Repository interface for offline messages
/// </summary>
public interface IOfflineMessageRepository
{
    /// <summary>
    /// Add offline message
    /// </summary>
    Task AddAsync(OfflineMessage offlineMessage, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get offline messages by user ID
    /// </summary>
    Task<List<OfflineMessage>> GetByUserIdAsync(Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Mark messages as delivered
    /// </summary>
    Task MarkAsDeliveredAsync(Guid userId, List<Guid> messageIds, CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete old messages
    /// </summary>
    Task<int> DeleteOldMessagesAsync(DateTime cutoffDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get undelivered message count for user
    /// </summary>
    Task<int> GetUndeliveredCountAsync(Guid userId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Chat event handler for offline message processing
/// </summary>
public class ChatEventHandler
{
    private readonly IOfflineMessageService _offlineMessageService;
    private readonly IConnectionTrackingService _connectionTracker;
    private readonly ILogger<ChatEventHandler> _logger;

    public ChatEventHandler(
        IOfflineMessageService offlineMessageService,
        IConnectionTrackingService connectionTracker,
        ILogger<ChatEventHandler> logger)
    {
        _offlineMessageService = offlineMessageService;
        _connectionTracker = connectionTracker;
        _logger = logger;
    }

    /// <summary>
    /// Handle message sent event
    /// </summary>
    public async Task HandleMessageSentAsync(Message message, List<Guid> recipientIds, CancellationToken cancellationToken = default)
    {
        try
        {
            // Store offline messages for offline recipients
            foreach (var recipientId in recipientIds)
            {
                if (recipientId != message.SenderId) // Don't store for sender
                {
                    await _offlineMessageService.StoreOfflineMessageAsync(recipientId, message, cancellationToken);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to handle message sent event for message {MessageId}", message.Id);
        }
    }

    /// <summary>
    /// Handle user connected event
    /// </summary>
    public async Task HandleUserConnectedAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            // Get offline messages for user
            var offlineMessages = await _offlineMessageService.GetOfflineMessagesAsync(userId, cancellationToken);
            
            if (offlineMessages.Any())
            {
                // Mark messages as delivered
                var messageIds = offlineMessages.Select(m => m.MessageId).ToList();
                await _offlineMessageService.MarkOfflineMessagesAsDeliveredAsync(userId, messageIds, cancellationToken);

                _logger.LogInformation("Delivered {Count} offline messages to user {UserId}", offlineMessages.Count, userId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to handle user connected event for user {UserId}", userId);
        }
    }
}
