using ContentManagement.Domain.Entities;
using ContentManagement.Domain.Enums;
using ContentManagement.Domain.Events;
using ContentManagement.Domain.ValueObjects;
using FluentAssertions;
using Xunit;

namespace ContentManagement.Tests.Domain;

public class ContentTests
{
    [Fact]
    public void Content_Creation_Should_Set_Properties_Correctly()
    {
        // Arrange
        var title = "Test Content";
        var slug = "test-content";
        var type = ContentType.General;
        var contentBody = "This is test content body";
        var createdBy = Guid.NewGuid();
        var createdByName = "Test User";

        // Act
        var content = new Content(title, slug, type, contentBody, createdBy, createdByName);

        // Assert
        content.Title.Should().Be(title);
        content.Slug.Should().Be(slug);
        content.Type.Should().Be(type);
        content.ContentBody.Should().Be(contentBody);
        content.CreatedBy.Should().Be(createdBy);
        content.CreatedByName.Should().Be(createdByName);
        content.Status.Should().Be(ContentStatus.Draft);
        content.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        content.Versions.Should().HaveCount(1);
        content.DomainEvents.Should().ContainSingle(e => e is ContentCreatedEvent);
    }

    [Fact]
    public void Content_Creation_With_Empty_Title_Should_Throw_Exception()
    {
        // Arrange & Act & Assert
        var act = () => new Content("", "slug", ContentType.General, "body", Guid.NewGuid(), "user");
        act.Should().Throw<ArgumentException>().WithMessage("Title cannot be empty*");
    }

    [Fact]
    public void Content_Creation_With_Empty_Slug_Should_Throw_Exception()
    {
        // Arrange & Act & Assert
        var act = () => new Content("title", "", ContentType.General, "body", Guid.NewGuid(), "user");
        act.Should().Throw<ArgumentException>().WithMessage("Slug cannot be empty*");
    }

    [Fact]
    public void Content_Creation_With_Empty_Body_Should_Throw_Exception()
    {
        // Arrange & Act & Assert
        var act = () => new Content("title", "slug", ContentType.General, "", Guid.NewGuid(), "user");
        act.Should().Throw<ArgumentException>().WithMessage("Content body cannot be empty*");
    }

    [Fact]
    public void UpdateContent_Should_Update_Properties_And_Create_New_Version()
    {
        // Arrange
        var content = CreateTestContent();
        var newTitle = "Updated Title";
        var newBody = "Updated content body";
        var updatedBy = Guid.NewGuid();
        var updatedByName = "Updater";

        // Act
        content.UpdateContent(newTitle, "Updated description", newBody, updatedBy, updatedByName);

        // Assert
        content.Title.Should().Be(newTitle);
        content.ContentBody.Should().Be(newBody);
        content.UpdatedAt.Should().NotBeNull();
        content.Versions.Should().HaveCount(2);
        content.DomainEvents.Should().Contain(e => e is ContentUpdatedEvent);
    }

    [Fact]
    public void SubmitForReview_Should_Change_Status_To_PendingReview()
    {
        // Arrange
        var content = CreateTestContent();
        var submittedBy = Guid.NewGuid();
        var submittedByName = "Submitter";

        // Act
        content.SubmitForReview(submittedBy, submittedByName);

        // Assert
        content.Status.Should().Be(ContentStatus.PendingReview);
        content.DomainEvents.Should().Contain(e => e is ContentSubmittedForReviewEvent);
    }

    [Fact]
    public void Approve_Should_Change_Status_To_Approved()
    {
        // Arrange
        var content = CreateTestContent();
        content.SubmitForReview(Guid.NewGuid(), "Submitter");
        var approvedBy = Guid.NewGuid();
        var approvedByName = "Approver";

        // Act
        content.Approve(approvedBy, approvedByName);

        // Assert
        content.Status.Should().Be(ContentStatus.Approved);
        content.ApprovedBy.Should().Be(approvedBy);
        content.ApprovedByName.Should().Be(approvedByName);
        content.ApprovedAt.Should().NotBeNull();
        content.DomainEvents.Should().Contain(e => e is ContentApprovedEvent);
    }

    [Fact]
    public void Approve_Draft_Content_Should_Throw_Exception()
    {
        // Arrange
        var content = CreateTestContent(); // Status is Draft
        var approvedBy = Guid.NewGuid();
        var approvedByName = "Approver";

        // Act & Assert
        var act = () => content.Approve(approvedBy, approvedByName);
        act.Should().Throw<InvalidOperationException>()
            .WithMessage("Only content pending review can be approved");
    }

    [Fact]
    public void Reject_Should_Change_Status_To_Rejected()
    {
        // Arrange
        var content = CreateTestContent();
        content.SubmitForReview(Guid.NewGuid(), "Submitter");
        var rejectedBy = Guid.NewGuid();
        var rejectedByName = "Rejector";
        var reason = "Content needs improvement";

        // Act
        content.Reject(rejectedBy, rejectedByName, reason);

        // Assert
        content.Status.Should().Be(ContentStatus.Rejected);
        content.DomainEvents.Should().Contain(e => e is ContentRejectedEvent);
    }

    [Fact]
    public void Publish_Should_Change_Status_To_Published()
    {
        // Arrange
        var content = CreateTestContent();
        content.SubmitForReview(Guid.NewGuid(), "Submitter");
        content.Approve(Guid.NewGuid(), "Approver");
        var publishedBy = Guid.NewGuid();
        var publishedByName = "Publisher";

        // Act
        content.Publish(publishedBy, publishedByName);

        // Assert
        content.Status.Should().Be(ContentStatus.Published);
        content.DomainEvents.Should().Contain(e => e is ContentPublishedEvent);
        content.GetLatestVersion()?.IsPublished.Should().BeTrue();
    }

    [Fact]
    public void Publish_Draft_Content_Should_Throw_Exception()
    {
        // Arrange
        var content = CreateTestContent(); // Status is Draft
        var publishedBy = Guid.NewGuid();
        var publishedByName = "Publisher";

        // Act & Assert
        var act = () => content.Publish(publishedBy, publishedByName);
        act.Should().Throw<InvalidOperationException>()
            .WithMessage("Only approved content can be published");
    }

    [Fact]
    public void Unpublish_Should_Change_Status_To_Approved()
    {
        // Arrange
        var content = CreateTestContent();
        content.SubmitForReview(Guid.NewGuid(), "Submitter");
        content.Approve(Guid.NewGuid(), "Approver");
        content.Publish(Guid.NewGuid(), "Publisher");
        var unpublishedBy = Guid.NewGuid();
        var unpublishedByName = "Unpublisher";

        // Act
        content.Unpublish(unpublishedBy, unpublishedByName);

        // Assert
        content.Status.Should().Be(ContentStatus.Approved);
        content.DomainEvents.Should().Contain(e => e is ContentUnpublishedEvent);
    }

    [Fact]
    public void Archive_Should_Change_Status_To_Archived()
    {
        // Arrange
        var content = CreateTestContent();
        var archivedBy = Guid.NewGuid();
        var archivedByName = "Archiver";

        // Act
        content.Archive(archivedBy, archivedByName);

        // Assert
        content.Status.Should().Be(ContentStatus.Archived);
        content.DomainEvents.Should().Contain(e => e is ContentArchivedEvent);
    }

    [Fact]
    public void AddAttachment_Should_Add_Attachment_To_Collection()
    {
        // Arrange
        var content = CreateTestContent();
        var attachment = new ContentAttachment(
            content.Id,
            Guid.NewGuid(),
            AttachmentType.Image,
            "Test Image",
            Guid.NewGuid(),
            "Uploader");

        // Act
        content.AddAttachment(attachment);

        // Assert
        content.Attachments.Should().HaveCount(1);
        content.Attachments.Should().Contain(attachment);
        content.DomainEvents.Should().Contain(e => e is ContentAttachmentAddedEvent);
    }

    [Fact]
    public void RemoveAttachment_Should_Remove_Attachment_From_Collection()
    {
        // Arrange
        var content = CreateTestContent();
        var attachment = new ContentAttachment(
            content.Id,
            Guid.NewGuid(),
            AttachmentType.Image,
            "Test Image",
            Guid.NewGuid(),
            "Uploader");
        content.AddAttachment(attachment);
        var removedBy = Guid.NewGuid();
        var removedByName = "Remover";

        // Act
        content.RemoveAttachment(attachment.Id, removedBy, removedByName);

        // Assert
        content.Attachments.Should().BeEmpty();
        content.DomainEvents.Should().Contain(e => e is ContentAttachmentRemovedEvent);
    }

    [Fact]
    public void IsPublished_Should_Return_True_When_Status_Is_Published()
    {
        // Arrange
        var content = CreateTestContent();
        content.SubmitForReview(Guid.NewGuid(), "Submitter");
        content.Approve(Guid.NewGuid(), "Approver");
        content.Publish(Guid.NewGuid(), "Publisher");

        // Act & Assert
        content.IsPublished().Should().BeTrue();
    }

    [Fact]
    public void CanBeEdited_Should_Return_True_For_Draft_And_Rejected_Content()
    {
        // Arrange
        var draftContent = CreateTestContent(); // Status is Draft
        var rejectedContent = CreateTestContent();
        rejectedContent.SubmitForReview(Guid.NewGuid(), "Submitter");
        rejectedContent.Reject(Guid.NewGuid(), "Rejector", "Needs work");

        // Act & Assert
        draftContent.CanBeEdited().Should().BeTrue();
        rejectedContent.CanBeEdited().Should().BeTrue();
    }

    [Fact]
    public void CanBePublished_Should_Return_True_For_Approved_Content()
    {
        // Arrange
        var content = CreateTestContent();
        content.SubmitForReview(Guid.NewGuid(), "Submitter");
        content.Approve(Guid.NewGuid(), "Approver");

        // Act & Assert
        content.CanBePublished().Should().BeTrue();
    }

    private static Content CreateTestContent()
    {
        return new Content(
            "Test Content",
            "test-content",
            ContentType.General,
            "This is test content body",
            Guid.NewGuid(),
            "Test User");
    }
}
