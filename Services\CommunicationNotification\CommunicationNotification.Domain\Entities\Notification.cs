using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;
using Shared.Domain.Common;

namespace CommunicationNotification.Domain.Entities;

public class Notification : BaseEntity
{
    public Guid UserId { get; private set; }
    public MessageType MessageType { get; private set; }
    public MessageContent Content { get; private set; }
    public Priority Priority { get; private set; }
    public NotificationChannel Channel { get; private set; }
    public MessageStatus Status { get; private set; }
    public DateTime ScheduledAt { get; private set; }
    public DateTime? SentAt { get; private set; }
    public DateTime? DeliveredAt { get; private set; }
    public DateTime? ReadAt { get; private set; }
    public Guid? RelatedEntityId { get; private set; }
    public string? RelatedEntityType { get; private set; }
    public string? ExternalId { get; private set; } // ID from external service (SMS provider, email service, etc.)
    public int RetryCount { get; private set; }
    public string? FailureReason { get; private set; }
    public Dictionary<string, string> Metadata { get; private set; }

    private Notification()
    {
        Content = MessageContent.Create("", "", Language.English);
        Metadata = new Dictionary<string, string>();
    }

    public Notification(
        Guid userId,
        MessageType messageType,
        MessageContent content,
        NotificationChannel channel,
        Priority priority = Priority.Normal,
        DateTime? scheduledAt = null,
        Guid? relatedEntityId = null,
        string? relatedEntityType = null,
        Dictionary<string, string>? metadata = null)
    {
        if (userId == Guid.Empty)
            throw new ArgumentException("User ID cannot be empty", nameof(userId));

        UserId = userId;
        MessageType = messageType;
        Content = content ?? throw new ArgumentNullException(nameof(content));
        Channel = channel;
        Priority = priority;
        Status = MessageStatus.Pending;
        ScheduledAt = scheduledAt ?? DateTime.UtcNow;
        RelatedEntityId = relatedEntityId;
        RelatedEntityType = relatedEntityType;
        RetryCount = 0;
        Metadata = metadata ?? new Dictionary<string, string>();
    }

    public static Notification Create(
        Guid userId,
        MessageType messageType,
        MessageContent content,
        NotificationChannel channel,
        Priority priority = Priority.Normal,
        DateTime? scheduledAt = null,
        Guid? relatedEntityId = null,
        string? relatedEntityType = null,
        Dictionary<string, string>? metadata = null)
    {
        return new Notification(userId, messageType, content, channel, priority, 
            scheduledAt, relatedEntityId, relatedEntityType, metadata);
    }

    public void MarkAsSent(string? externalId = null)
    {
        if (Status != MessageStatus.Pending && Status != MessageStatus.Retrying)
            throw new InvalidOperationException($"Cannot mark notification as sent when status is {Status}");

        Status = MessageStatus.Sent;
        SentAt = DateTime.UtcNow;
        ExternalId = externalId;
    }

    public void MarkAsDelivered()
    {
        if (Status != MessageStatus.Sent)
            throw new InvalidOperationException($"Cannot mark notification as delivered when status is {Status}");

        Status = MessageStatus.Delivered;
        DeliveredAt = DateTime.UtcNow;
    }

    public void MarkAsRead()
    {
        if (Status != MessageStatus.Delivered && Status != MessageStatus.Read)
            throw new InvalidOperationException($"Cannot mark notification as read when status is {Status}");

        Status = MessageStatus.Read;
        ReadAt = DateTime.UtcNow;
    }

    public void MarkAsFailed(string reason)
    {
        if (string.IsNullOrWhiteSpace(reason))
            throw new ArgumentException("Failure reason cannot be empty", nameof(reason));

        Status = MessageStatus.Failed;
        FailureReason = reason;
    }

    public void MarkForRetry()
    {
        if (Status != MessageStatus.Failed)
            throw new InvalidOperationException($"Cannot retry notification when status is {Status}");

        Status = MessageStatus.Retrying;
        RetryCount++;
        FailureReason = null;
    }

    public void Cancel()
    {
        if (Status != MessageStatus.Pending && Status != MessageStatus.Retrying)
            throw new InvalidOperationException($"Cannot cancel notification when status is {Status}");

        Status = MessageStatus.Cancelled;
    }

    public void Reschedule(DateTime newScheduledAt)
    {
        if (Status != MessageStatus.Pending)
            throw new InvalidOperationException("Cannot reschedule notification that has been processed");

        if (newScheduledAt <= DateTime.UtcNow)
            throw new ArgumentException("Scheduled time must be in the future", nameof(newScheduledAt));

        ScheduledAt = newScheduledAt;
    }

    public void UpdateContent(MessageContent content)
    {
        if (Status != MessageStatus.Pending)
            throw new InvalidOperationException("Cannot update content of a notification that has been sent");

        Content = content ?? throw new ArgumentNullException(nameof(content));
    }

    public void AddMetadata(string key, string value)
    {
        if (string.IsNullOrWhiteSpace(key))
            throw new ArgumentException("Metadata key cannot be empty", nameof(key));

        Metadata[key] = value ?? string.Empty;
    }

    public bool IsReadyToSend()
    {
        return Status == MessageStatus.Pending && ScheduledAt <= DateTime.UtcNow;
    }

    public bool IsExpired(TimeSpan expirationTime)
    {
        return DateTime.UtcNow - ScheduledAt > expirationTime;
    }

    public bool CanRetry(int maxRetries)
    {
        return Status == MessageStatus.Failed && RetryCount < maxRetries;
    }

    public TimeSpan GetNextRetryDelay()
    {
        // Exponential backoff: 1min, 2min, 4min, 8min, 16min
        var delayMinutes = Math.Pow(2, RetryCount);
        return TimeSpan.FromMinutes(Math.Min(delayMinutes, 60)); // Max 1 hour
    }
}
