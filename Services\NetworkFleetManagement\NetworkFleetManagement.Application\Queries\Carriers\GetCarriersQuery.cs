using MediatR;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Enums;

namespace NetworkFleetManagement.Application.Queries.Carriers;

public record GetCarriersQuery(
    int PageNumber = 1,
    int PageSize = 10,
    string? SearchTerm = null,
    CarrierStatus? Status = null) : IRequest<PagedResult<CarrierSummaryDto>>;

public class PagedResult<T>
{
    public IEnumerable<T> Items { get; set; } = new List<T>();
    public int TotalCount { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
    public bool HasPreviousPage => PageNumber > 1;
    public bool HasNextPage => PageNumber < TotalPages;
}
