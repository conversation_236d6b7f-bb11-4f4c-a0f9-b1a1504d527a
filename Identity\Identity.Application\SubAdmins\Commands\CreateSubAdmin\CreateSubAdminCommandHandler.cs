﻿using System;
using System.Threading;
using System.Threading.Tasks;
using Identity.Application.Common.Interfaces;
using Identity.Domain.Entities;
using Identity.Domain.Exceptions;
using Identity.Domain.Repositories;
using Identity.Domain.ValueObjects;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Messaging;

namespace Identity.Application.SubAdmins.Commands.CreateSubAdmin
{
    public class CreateSubAdminCommandHandler : IRequestHandler<CreateSubAdminCommand, Guid>
    {
        private readonly IUserRepository _userRepository;
        private readonly ISubAdminRepository _subAdminRepository;
        private readonly Identity.Domain.Repositories.IRoleRepository _roleRepository;
        private readonly IPasswordHasher _passwordHasher;
        private readonly IMessageBroker _messageBroker;
        private readonly ILogger<CreateSubAdminCommandHandler> _logger;

        public CreateSubAdminCommandHandler(
            IUserRepository userRepository,
            ISubAdminRepository subAdminRepository,
            Identity.Domain.Repositories.IRoleRepository roleRepository,
            IPasswordHasher passwordHasher,
            IMessageBroker messageBroker,
            ILogger<CreateSubAdminCommandHandler> logger)
        {
            _userRepository = userRepository;
            _subAdminRepository = subAdminRepository;
            _roleRepository = roleRepository;
            _passwordHasher = passwordHasher;
            _messageBroker = messageBroker;
            _logger = logger;
        }

        public async Task<Guid> Handle(CreateSubAdminCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // Check if email already exists
                var existingUser = await _userRepository.GetByEmailAsync(request.Email);
                if (existingUser != null)
                {
                    throw new DomainException("A user with this email already exists");
                }

                // Create email value object
                var email = Email.Create(request.Email);

                // Hash the password
                var passwordHash = _passwordHasher.HashPassword(request.Password);

                // Create the user first
                var user = new User($"subadmin_{Guid.NewGuid():N}", email, passwordHash, UserType.SubAdmin);
                user.SetPhoneNumber(""); // Sub-admins might not have phone numbers initially

                await _userRepository.AddAsync(user);

                // Create the sub-admin profile
                var subAdmin = new SubAdmin(
                    user.Id,
                    request.Name,
                    email,
                    request.CreatedBy,
                    request.Department);

                // Set IP restrictions if provided
                if (request.AllowedIpAddresses?.Count > 0)
                {
                    subAdmin.SetAllowedIpAddresses(request.AllowedIpAddresses);
                }

                // Set supervisor if provided
                if (request.SupervisorId.HasValue)
                {
                    subAdmin.SetSupervisor(request.SupervisorId.Value);
                }

                // Set temporary access if provided
                if (request.TemporaryAccessExpiry.HasValue)
                {
                    subAdmin.SetTemporaryAccess(request.TemporaryAccessExpiry.Value);
                }

                // Add roles
                if (request.RoleIds?.Count > 0)
                {
                    foreach (var roleId in request.RoleIds)
                    {
                        var role = await _roleRepository.GetByIdAsync(roleId);
                        if (role != null && role.IsActive)
                        {
                            user.AddRole(role);
                            subAdmin.AddRole(role);
                        }
                    }
                }

                await _subAdminRepository.AddAsync(subAdmin);

                // Publish integration event
                await _messageBroker.PublishAsync("subadmin.created", new
                {
                    SubAdminId = subAdmin.Id,
                    UserId = user.Id,
                    Name = request.Name,
                    Email = request.Email,
                    Department = request.Department,
                    CreatedBy = request.CreatedBy,
                    CreatedAt = DateTime.UtcNow,
                    RequireTwoFactor = request.RequireTwoFactor,
                    HasTemporaryAccess = request.TemporaryAccessExpiry.HasValue
                });

                _logger.LogInformation("Sub-admin created successfully: {SubAdminId} for user {UserId}",
                    subAdmin.Id, user.Id);

                return subAdmin.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating sub-admin with email {Email}", request.Email);
                throw;
            }
        }
    }
}

