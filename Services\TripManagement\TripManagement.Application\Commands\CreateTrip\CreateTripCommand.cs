using MediatR;
using TripManagement.Application.DTOs;

namespace TripManagement.Application.Commands.CreateTrip;

public record CreateTripCommand : IRequest<Guid>
{
    public Guid OrderId { get; init; }
    public Guid CarrierId { get; init; }
    public RouteDto Route { get; init; } = null!;
    public DateTime EstimatedStartTime { get; init; }
    public DateTime EstimatedEndTime { get; init; }
    public string? SpecialInstructions { get; init; }
    public bool IsUrgent { get; init; }
    public decimal? EstimatedDistanceKm { get; init; }
    public List<CreateTripStopDto> Stops { get; init; } = new();
}

public record CreateTripStopDto
{
    public Domain.Enums.TripStopType StopType { get; init; }
    public int SequenceNumber { get; init; }
    public LocationDto Location { get; init; } = null!;
    public string? ContactName { get; init; }
    public string? ContactPhone { get; init; }
    public DateTime? ScheduledArrival { get; init; }
    public DateTime? ScheduledDeparture { get; init; }
    public string? Instructions { get; init; }
}
