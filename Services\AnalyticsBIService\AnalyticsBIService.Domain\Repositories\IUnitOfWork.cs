namespace AnalyticsBIService.Domain.Repositories;

/// <summary>
/// Unit of Work pattern interface for managing transactions
/// </summary>
public interface IUnitOfWork : IDisposable
{
    /// <summary>
    /// Analytics Event repository
    /// </summary>
    IAnalyticsEventRepository AnalyticsEvents { get; }

    /// <summary>
    /// Metric repository
    /// </summary>
    IMetricRepository Metrics { get; }

    /// <summary>
    /// Dashboard repository
    /// </summary>
    IDashboardRepository Dashboards { get; }

    /// <summary>
    /// Dashboard Widget repository
    /// </summary>
    IDashboardWidgetRepository DashboardWidgets { get; }

    /// <summary>
    /// Report repository
    /// </summary>
    IReportRepository Reports { get; }

    /// <summary>
    /// Report Section repository
    /// </summary>
    IReportSectionRepository ReportSections { get; }

    /// <summary>
    /// Report Export repository
    /// </summary>
    IReportExportRepository ReportExports { get; }

    /// <summary>
    /// Alert repository
    /// </summary>
    IAlertRepository Alerts { get; }

    /// <summary>
    /// Alert Rule repository
    /// </summary>
    IAlertRuleRepository AlertRules { get; }

    /// <summary>
    /// Save all changes to the database
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of affected records</returns>
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Begin a database transaction
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Transaction object</returns>
    Task<IDbTransaction> BeginTransactionAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Commit the current transaction
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    Task CommitTransactionAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Rollback the current transaction
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    Task RollbackTransactionAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Database transaction interface
/// </summary>
public interface IDbTransaction : IDisposable
{
    /// <summary>
    /// Commit the transaction
    /// </summary>
    Task CommitAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Rollback the transaction
    /// </summary>
    Task RollbackAsync(CancellationToken cancellationToken = default);
}
