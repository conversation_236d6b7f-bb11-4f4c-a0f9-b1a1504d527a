{"WhatsApp": {"BaseUrl": "https://graph.facebook.com/v18.0", "AccessToken": "YOUR_WHATSAPP_ACCESS_TOKEN", "PhoneNumberId": "YOUR_PHONE_NUMBER_ID", "BusinessAccountId": "YOUR_BUSINESS_ACCOUNT_ID", "WebhookVerifyToken": "YOUR_WEBHOOK_VERIFY_TOKEN", "WebhookUrl": "https://your-domain.com/api/webhooks/whatsapp", "MaxRetryAttempts": 3, "TimeoutSeconds": 30, "EnableWebhook": true, "DefaultLanguageCode": "en", "RateLimit": {"MessagesPerSecond": 10, "MessagesPerMinute": 600, "MessagesPerHour": 10000, "EnableRateLimit": true}}, "Twilio": {"AccountSid": "YOUR_TWILIO_ACCOUNT_SID", "AuthToken": "YOUR_TWILIO_AUTH_TOKEN", "FromPhoneNumber": "+**********", "StatusCallbackUrl": "https://your-domain.com/api/webhooks/twilio", "MaxRetryAttempts": 3, "TimeoutSeconds": 30}, "SendGrid": {"ApiKey": "YOUR_SENDGRID_API_KEY", "FromEmail": "<EMAIL>", "FromName": "TLI Communication Service", "EnableClickTracking": true, "EnableOpenTracking": true, "WebhookUrl": "https://your-domain.com/api/webhooks/sendgrid", "MaxRetryAttempts": 3}, "Firebase": {"ProjectId": "your-firebase-project-id", "ServiceAccountKeyPath": "/path/to/firebase-service-account.json", "ServiceAccountKeyJson": "", "DefaultIcon": "ic_notification", "DefaultColor": "#FF0000", "EnableAnalytics": true}, "NotificationSettings": {"DefaultFailoverChannels": {"Push": ["Sms", "Email", "WhatsApp"], "Sms": ["WhatsApp", "<PERSON><PERSON>", "Email"], "Email": ["<PERSON><PERSON>", "Sms", "WhatsApp"], "WhatsApp": ["Sms", "<PERSON><PERSON>", "Email"], "Voice": ["Sms", "WhatsApp", "<PERSON><PERSON>"]}, "RetryPolicy": {"MaxRetryAttempts": 3, "RetryDelaySeconds": [30, 300, 1800], "ExponentialBackoff": true}, "RateLimiting": {"EnableGlobalRateLimit": true, "GlobalMessagesPerSecond": 50, "GlobalMessagesPerMinute": 3000, "PerUserMessagesPerMinute": 10}, "ChannelPriority": {"Emergency": ["Voice", "Sms", "WhatsApp", "<PERSON><PERSON>"], "High": ["<PERSON><PERSON>", "Sms", "WhatsApp", "Email"], "Normal": ["<PERSON><PERSON>", "Email", "WhatsApp", "Sms"], "Low": ["Email", "InApp", "<PERSON><PERSON>"]}}, "Logging": {"LogLevel": {"Default": "Information", "CommunicationNotification.Infrastructure.Providers": "Debug", "CommunicationNotification.Infrastructure.Services": "Debug", "CommunicationNotification.Infrastructure.Webhooks": "Debug"}}}