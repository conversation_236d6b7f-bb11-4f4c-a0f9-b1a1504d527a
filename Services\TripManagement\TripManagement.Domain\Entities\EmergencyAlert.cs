using TripManagement.Domain.ValueObjects;
using Shared.Domain.Common;



namespace TripManagement.Domain.Entities;

public class EmergencyAlert : AggregateRoot
{
    public Guid DriverId { get; private set; }
    public Guid? TripId { get; private set; }
    public string AlertType { get; private set; } = string.Empty;
    public string Severity { get; private set; } = string.Empty;
    public string? Message { get; private set; }
    public Location? Location { get; private set; }
    public string? DeviceId { get; private set; }
    public Dictionary<string, object> AdditionalData { get; private set; } = new();
    public DateTime AlertTime { get; private set; }
    public EmergencyAlertStatus Status { get; private set; }
    public DateTime? AcknowledgedAt { get; private set; }
    public Guid? AcknowledgedBy { get; private set; }
    public DateTime? ResolvedAt { get; private set; }
    public Guid? ResolvedBy { get; private set; }
    public string? ResolutionNotes { get; private set; }
    public TimeSpan? ResponseTime { get; private set; }

    // Navigation properties
    private readonly List<EmergencyAlertResponse> _responses = new();
    public IReadOnlyCollection<EmergencyAlertResponse> Responses => _responses.AsReadOnly();

    // Parameterless constructor for EF Core
    private EmergencyAlert() { }

    public EmergencyAlert(
        Guid id,
        Guid driverId,
        Guid? tripId,
        string alertType,
        string severity,
        string? message,
        Location? location,
        string? deviceId,
        Dictionary<string, object>? additionalData)
    {
        if (driverId == Guid.Empty)
            throw new ArgumentException("Driver ID cannot be empty", nameof(driverId));

        if (string.IsNullOrWhiteSpace(alertType))
            throw new ArgumentException("Alert type cannot be empty", nameof(alertType));

        if (string.IsNullOrWhiteSpace(severity))
            throw new ArgumentException("Severity cannot be empty", nameof(severity));

        Id = id;
        DriverId = driverId;
        TripId = tripId;
        AlertType = alertType;
        Severity = severity;
        Message = message;
        Location = location;
        DeviceId = deviceId;
        AdditionalData = additionalData ?? new Dictionary<string, object>();
        AlertTime = DateTime.UtcNow;
        Status = EmergencyAlertStatus.Active;
        CreatedAt = DateTime.UtcNow;

        AddDomainEvent(new EmergencyAlertCreatedEvent(Id, DriverId, AlertType, Severity, AlertTime));
    }

    public void Acknowledge(Guid acknowledgedBy, string? notes = null)
    {
        if (Status != EmergencyAlertStatus.Active)
            throw new InvalidOperationException("Can only acknowledge active alerts");

        Status = EmergencyAlertStatus.Acknowledged;
        AcknowledgedAt = DateTime.UtcNow;
        AcknowledgedBy = acknowledgedBy;

        if (!string.IsNullOrWhiteSpace(notes))
        {
            AddResponse(acknowledgedBy, "Acknowledgment", notes);
        }

        AddDomainEvent(new EmergencyAlertAcknowledgedEvent(Id, acknowledgedBy, AcknowledgedAt.Value));
    }

    public void Resolve(Guid resolvedBy, string resolutionNotes)
    {
        if (Status == EmergencyAlertStatus.Resolved)
            throw new InvalidOperationException("Alert is already resolved");

        if (string.IsNullOrWhiteSpace(resolutionNotes))
            throw new ArgumentException("Resolution notes are required", nameof(resolutionNotes));

        Status = EmergencyAlertStatus.Resolved;
        ResolvedAt = DateTime.UtcNow;
        ResolvedBy = resolvedBy;
        ResolutionNotes = resolutionNotes;

        // Calculate response time
        if (AcknowledgedAt.HasValue)
        {
            ResponseTime = ResolvedAt.Value - AcknowledgedAt.Value;
        }
        else
        {
            ResponseTime = ResolvedAt.Value - AlertTime;
        }

        AddResponse(resolvedBy, "Resolution", resolutionNotes);
        AddDomainEvent(new EmergencyAlertResolvedEvent(Id, resolvedBy, ResolvedAt.Value, ResponseTime.Value));
    }

    public void Escalate(Guid escalatedBy, string escalationReason)
    {
        if (Status == EmergencyAlertStatus.Resolved)
            throw new InvalidOperationException("Cannot escalate resolved alerts");

        Status = EmergencyAlertStatus.Escalated;
        AddResponse(escalatedBy, "Escalation", escalationReason);

        AddDomainEvent(new EmergencyAlertEscalatedEvent(Id, escalatedBy, escalationReason));
    }

    public void AddResponse(Guid responderId, string responseType, string responseText)
    {
        if (string.IsNullOrWhiteSpace(responseType))
            throw new ArgumentException("Response type cannot be empty", nameof(responseType));

        if (string.IsNullOrWhiteSpace(responseText))
            throw new ArgumentException("Response text cannot be empty", nameof(responseText));

        var response = new EmergencyAlertResponse(
            Guid.NewGuid(),
            Id,
            responderId,
            responseType,
            responseText);

        _responses.Add(response);
    }

    public bool IsActive => Status == EmergencyAlertStatus.Active;
    public bool IsAcknowledged => Status == EmergencyAlertStatus.Acknowledged;
    public bool IsResolved => Status == EmergencyAlertStatus.Resolved;
    public bool IsEscalated => Status == EmergencyAlertStatus.Escalated;

    public bool IsCritical => Severity.ToLower() == "critical" || AlertType.ToLower() == "sos";
    public bool RequiresImmediateResponse => IsCritical || AlertType.ToLower() == "medical";
}

public class EmergencyAlertResponse : BaseEntity
{
    public Guid AlertId { get; private set; }
    public Guid ResponderId { get; private set; }
    public string ResponseType { get; private set; } = string.Empty;
    public string ResponseText { get; private set; } = string.Empty;
    public DateTime ResponseTime { get; private set; }

    // Parameterless constructor for EF Core
    private EmergencyAlertResponse() { }

    public EmergencyAlertResponse(
        Guid id,
        Guid alertId,
        Guid responderId,
        string responseType,
        string responseText)
    {
        Id = id;
        AlertId = alertId;
        ResponderId = responderId;
        ResponseType = responseType;
        ResponseText = responseText;
        ResponseTime = DateTime.UtcNow;
        CreatedAt = DateTime.UtcNow;
    }
}

public enum EmergencyAlertStatus
{
    Active = 1,
    Acknowledged = 2,
    Escalated = 3,
    Resolved = 4,
    Cancelled = 5
}

// Domain Events
public record EmergencyAlertCreatedEvent(Guid AlertId, Guid DriverId, string AlertType, string Severity, DateTime AlertTime) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record EmergencyAlertAcknowledgedEvent(Guid AlertId, Guid AcknowledgedBy, DateTime AcknowledgedAt) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record EmergencyAlertResolvedEvent(Guid AlertId, Guid ResolvedBy, DateTime ResolvedAt, TimeSpan ResponseTime) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record EmergencyAlertEscalatedEvent(Guid AlertId, Guid EscalatedBy, string EscalationReason) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}



