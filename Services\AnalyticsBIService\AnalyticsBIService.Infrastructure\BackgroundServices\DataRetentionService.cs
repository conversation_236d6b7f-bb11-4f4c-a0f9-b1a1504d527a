using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using AnalyticsBIService.Domain.Repositories;
using AnalyticsBIService.Application.Interfaces;

namespace AnalyticsBIService.Infrastructure.BackgroundServices;

/// <summary>
/// Background service for data retention and cleanup
/// </summary>
public class DataRetentionService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly IConfiguration _configuration;
    private readonly ILogger<DataRetentionService> _logger;
    private readonly TimeSpan _processingInterval = TimeSpan.FromHours(6); // Run every 6 hours

    public DataRetentionService(
        IServiceProvider serviceProvider,
        IConfiguration configuration,
        ILogger<DataRetentionService> logger)
    {
        _serviceProvider = serviceProvider;
        _configuration = configuration;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Data Retention Service started");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await PerformDataRetentionAsync(stoppingToken);
                await Task.Delay(_processingInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("Data Retention Service is stopping");
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in Data Retention Service");
                await Task.Delay(TimeSpan.FromHours(1), stoppingToken);
            }
        }

        _logger.LogInformation("Data Retention Service stopped");
    }

    private async Task PerformDataRetentionAsync(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var analyticsEventRepository = scope.ServiceProvider.GetRequiredService<IAnalyticsEventRepository>();
        var metricRepository = scope.ServiceProvider.GetRequiredService<IMetricRepository>();
        var alertRepository = scope.ServiceProvider.GetRequiredService<IAlertRepository>();
        var reportRepository = scope.ServiceProvider.GetRequiredService<IReportRepository>();
        var cacheService = scope.ServiceProvider.GetRequiredService<ICacheService>();

        try
        {
            _logger.LogInformation("Starting data retention cycle");

            // Clean up old analytics events
            await CleanupAnalyticsEventsAsync(analyticsEventRepository, cancellationToken);

            // Clean up old metrics
            await CleanupMetricsAsync(metricRepository, cancellationToken);

            // Clean up old alerts
            await CleanupAlertsAsync(alertRepository, cancellationToken);

            // Clean up old report exports
            await CleanupReportExportsAsync(reportRepository, cancellationToken);

            // Clean up cache entries
            await CleanupCacheAsync(cacheService, cancellationToken);

            // Optimize database (if supported)
            await OptimizeDatabaseAsync(cancellationToken);

            _logger.LogInformation("Data retention cycle completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during data retention");
            throw;
        }
    }

    private async Task CleanupAnalyticsEventsAsync(
        IAnalyticsEventRepository analyticsEventRepository,
        CancellationToken cancellationToken)
    {
        try
        {
            var retentionDays = _configuration.GetValue<int>("DataRetention:AnalyticsEvents:Days", 90);
            var cutoffDate = DateTime.UtcNow.AddDays(-retentionDays);

            _logger.LogInformation("Cleaning up analytics events older than {CutoffDate} ({RetentionDays} days)",
                cutoffDate, retentionDays);

            await analyticsEventRepository.DeleteOldEventsAsync(cutoffDate, cancellationToken);
            _logger.LogInformation("Completed cleanup of analytics events older than {CutoffDate}", cutoffDate);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up analytics events");
            throw;
        }
    }

    private async Task CleanupMetricsAsync(
        IMetricRepository metricRepository,
        CancellationToken cancellationToken)
    {
        try
        {
            var retentionDays = _configuration.GetValue<int>("DataRetention:Metrics:Days", 365);
            var cutoffDate = DateTime.UtcNow.AddDays(-retentionDays);

            _logger.LogInformation("Cleaning up metrics older than {CutoffDate} ({RetentionDays} days)",
                cutoffDate, retentionDays);

            await metricRepository.DeleteOldMetricsAsync(cutoffDate, cancellationToken);
            _logger.LogInformation("Completed cleanup of metrics older than {CutoffDate}", cutoffDate);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up metrics");
            throw;
        }
    }

    private async Task CleanupAlertsAsync(
        IAlertRepository alertRepository,
        CancellationToken cancellationToken)
    {
        try
        {
            var retentionDays = _configuration.GetValue<int>("DataRetention:Alerts:Days", 180);
            var cutoffDate = DateTime.UtcNow.AddDays(-retentionDays);

            _logger.LogInformation("Cleaning up resolved/expired alerts older than {CutoffDate} ({RetentionDays} days)",
                cutoffDate, retentionDays);

            await alertRepository.DeleteOldResolvedAlertsAsync(cutoffDate, cancellationToken);
            _logger.LogInformation("Completed cleanup of resolved alerts older than {CutoffDate}", cutoffDate);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up alerts");
            throw;
        }
    }

    private async Task CleanupReportExportsAsync(
        IReportRepository reportRepository,
        CancellationToken cancellationToken)
    {
        try
        {
            var retentionDays = _configuration.GetValue<int>("DataRetention:ReportExports:Days", 30);
            var cutoffDate = DateTime.UtcNow.AddDays(-retentionDays);

            _logger.LogInformation("Cleaning up report exports older than {CutoffDate} ({RetentionDays} days)",
                cutoffDate, retentionDays);

            await reportRepository.DeleteOldExportsAsync(cutoffDate, cancellationToken);
            _logger.LogInformation("Completed cleanup of report exports older than {CutoffDate}", cutoffDate);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up report exports");
            throw;
        }
    }

    private async Task CleanupCacheAsync(
        ICacheService cacheService,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Cleaning up expired cache entries");

            // Clean up expired cache entries with specific patterns
            var patterns = new[]
            {
                "temp_*",
                "session_*",
                "hourly_*"
            };

            var totalDeleted = 0;
            foreach (var pattern in patterns)
            {
                try
                {
                    var deletedCount = await cacheService.RemoveByPatternAsync(pattern, cancellationToken);
                    totalDeleted += deletedCount;

                    if (deletedCount > 0)
                    {
                        _logger.LogDebug("Deleted {DeletedCount} cache entries matching pattern {Pattern}",
                            deletedCount, pattern);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error cleaning up cache entries with pattern {Pattern}", pattern);
                }
            }

            if (totalDeleted > 0)
            {
                _logger.LogInformation("Deleted {TotalDeleted} expired cache entries", totalDeleted);
            }
            else
            {
                _logger.LogDebug("No expired cache entries found for cleanup");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up cache");
            throw;
        }
    }

    private async Task OptimizeDatabaseAsync(CancellationToken cancellationToken)
    {
        try
        {
            // This is a placeholder for database optimization tasks
            // In a real implementation, you might:
            // - Run VACUUM on PostgreSQL
            // - Update statistics
            // - Rebuild indexes
            // - Compress TimescaleDB hypertables

            _logger.LogInformation("Running database optimization tasks");

            // Simulate optimization work
            await Task.Delay(1000, cancellationToken);

            _logger.LogInformation("Database optimization completed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during database optimization");
            // Don't throw here as optimization is not critical
        }
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Data Retention Service is stopping...");
        await base.StopAsync(cancellationToken);
    }
}
