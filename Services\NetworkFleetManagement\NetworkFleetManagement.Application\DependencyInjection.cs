using FluentValidation;
using Microsoft.Extensions.DependencyInjection;
using NetworkFleetManagement.Application.Mappings;
using System.Reflection;

namespace NetworkFleetManagement.Application;

public static class DependencyInjection
{
    public static IServiceCollection AddApplication(this IServiceCollection services)
    {
        // MediatR
        services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly()));

        // AutoMapper
        services.AddAutoMapper(typeof(NetworkFleetMappingProfile));

        // FluentValidation
        services.AddValidatorsFromAssembly(Assembly.GetExecutingAssembly());

        return services;
    }
}
