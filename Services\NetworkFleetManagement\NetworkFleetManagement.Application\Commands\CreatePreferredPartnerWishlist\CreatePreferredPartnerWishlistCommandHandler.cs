using MediatR;
using Microsoft.Extensions.Logging;
using NetworkFleetManagement.Application.Interfaces;
using NetworkFleetManagement.Domain.Entities;
using NetworkFleetManagement.Domain.Repositories;
using Shared.Domain.Common;

namespace NetworkFleetManagement.Application.Commands.CreatePreferredPartnerWishlist;

public class CreatePreferredPartnerWishlistCommandHandler : IRequestHandler<CreatePreferredPartnerWishlistCommand, Guid>
{
    private readonly IPreferredPartnerRepository _preferredPartnerRepository;
    private readonly ICarrierRepository _carrierRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<CreatePreferredPartnerWishlistCommandHandler> _logger;

    public CreatePreferredPartnerWishlistCommandHandler(
        IPreferredPartnerRepository preferredPartnerRepository,
        ICarrierRepository carrierRepository,
        IUnitOfWork unitOfWork,
        ILogger<CreatePreferredPartnerWishlistCommandHandler> logger)
    {
        _preferredPartnerRepository = preferredPartnerRepository;
        _carrierRepository = carrierRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<Guid> Handle(CreatePreferredPartnerWishlistCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Creating preferred partner wishlist for user {UserId} with {PartnerCount} partners",
            request.UserId, request.Partners.Count);

        try
        {
            // Validate user exists and has permission
            var user = await ValidateUserPermissions(request.UserId, request.RequestingUserId, cancellationToken);

            // Create wishlist ID
            var wishlistId = Guid.NewGuid();

            // Validate and create preferred partners
            var preferredPartners = new List<PreferredPartner>();

            foreach (var partnerRequest in request.Partners)
            {
                // Validate partner exists
                await ValidatePartnerExists(partnerRequest.PartnerId, request.PartnerType, cancellationToken);

                // Check if partnership already exists
                var existingPartnership = await _preferredPartnerRepository.GetByUserAndPartnerAsync(
                    request.UserId, partnerRequest.PartnerId, cancellationToken);

                if (existingPartnership != null && existingPartnership.Status == Domain.Enums.PreferenceStatus.Active)
                {
                    _logger.LogWarning("Partnership already exists between user {UserId} and partner {PartnerId}",
                        request.UserId, partnerRequest.PartnerId);
                    continue; // Skip existing active partnerships
                }

                // Create preferred partner
                var preferredPartner = new PreferredPartner(
                    request.UserId,
                    partnerRequest.PartnerId,
                    request.PartnerType,
                    partnerRequest.PreferenceLevel,
                    partnerRequest.Priority,
                    partnerRequest.PreferredCommissionRate,
                    partnerRequest.PreferredServiceRate,
                    partnerRequest.AutoAssignEnabled,
                    partnerRequest.Notes);

                // Configure auto-assignment settings
                preferredPartner.UpdateAutoAssignSettings(
                    partnerRequest.AutoAssignEnabled,
                    partnerRequest.AutoAssignThreshold,
                    partnerRequest.PreferredRoutes,
                    partnerRequest.PreferredLoadTypes,
                    partnerRequest.ExcludedRoutes,
                    partnerRequest.ExcludedLoadTypes);

                // Configure notification preferences
                preferredPartner.UpdateNotificationPreferences(
                    partnerRequest.NotifyOnNewOpportunities,
                    partnerRequest.NotifyOnPerformanceChanges,
                    request.Settings.EnableContractExpiryAlerts);

                preferredPartners.Add(preferredPartner);
            }

            // Save all preferred partners
            foreach (var partner in preferredPartners)
            {
                await _preferredPartnerRepository.AddAsync(partner, cancellationToken);
            }

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Successfully created wishlist with {Count} preferred partners for user {UserId}",
                preferredPartners.Count, request.UserId);

            return wishlistId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating preferred partner wishlist for user {UserId}", request.UserId);
            throw;
        }
    }

    private async Task<object> ValidateUserPermissions(Guid userId, Guid requestingUserId, CancellationToken cancellationToken)
    {
        // Validate requesting user has permission to create wishlist for the specified user
        if (userId != requestingUserId)
        {
            // Check if requesting user is admin or has delegation permissions
            // This would typically involve checking roles/permissions
            throw new UnauthorizedAccessException("You do not have permission to create wishlist for this user");
        }

        // Additional user validation logic would go here
        return new { UserId = userId };
    }

    private async Task ValidatePartnerExists(Guid partnerId, Domain.Enums.PreferredPartnerType partnerType, CancellationToken cancellationToken)
    {
        switch (partnerType)
        {
            case Domain.Enums.PreferredPartnerType.Carrier:
                var carrier = await _carrierRepository.GetByIdAsync(partnerId, cancellationToken);
                if (carrier == null)
                    throw new ArgumentException($"Carrier {partnerId} not found");
                if (carrier.Status != Domain.Enums.CarrierStatus.Active)
                    throw new ArgumentException($"Carrier {partnerId} is not active");
                break;

            case Domain.Enums.PreferredPartnerType.Broker:
                // Broker validation would go here - might need to call external service
                break;

            case Domain.Enums.PreferredPartnerType.Transporter:
                // Transporter validation would go here
                break;

            default:
                throw new ArgumentException($"Unsupported partner type: {partnerType}");
        }
    }
}
