using MediatR;
using Microsoft.Extensions.Logging;
using UserManagement.Domain.Repositories;
using UserManagement.Domain.Exceptions;
using Shared.Messaging;

namespace UserManagement.Application.Admin.Commands.RejectUser
{
    public class RejectUserCommandHandler : IRequestHandler<RejectUserCommand, RejectUserResponse>
    {
        private readonly IUserProfileRepository _userProfileRepository;
        private readonly IMessageBroker _messageBroker;
        private readonly ILogger<RejectUserCommandHandler> _logger;

        public RejectUserCommandHandler(
            IUserProfileRepository userProfileRepository,
            IMessageBroker messageBroker,
            ILogger<RejectUserCommandHandler> logger)
        {
            _userProfileRepository = userProfileRepository;
            _messageBroker = messageBroker;
            _logger = logger;
        }

        public async Task<RejectUserResponse> Handle(RejectUserCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Processing user rejection for UserId: {UserId} by {RejectedBy}", 
                request.UserId, request.RejectedBy);

            try
            {
                // Get the user profile
                var userProfile = await _userProfileRepository.GetByUserIdAsync(request.UserId);
                if (userProfile == null)
                {
                    _logger.LogWarning("User profile not found for UserId: {UserId}", request.UserId);
                    return new RejectUserResponse
                    {
                        Success = false,
                        Message = "User profile not found"
                    };
                }

                // Reject the user profile
                userProfile.Reject(request.Reason);

                // Update in repository
                await _userProfileRepository.UpdateAsync(userProfile);

                // Publish integration event
                await _messageBroker.PublishAsync("user.rejected", new
                {
                    UserId = request.UserId,
                    UserType = userProfile.UserType.ToString(),
                    Email = userProfile.Email,
                    DisplayName = userProfile.GetDisplayName(),
                    RejectedBy = request.RejectedBy,
                    RejectedAt = userProfile.UpdatedAt,
                    Reason = request.Reason,
                    Notes = request.Notes
                });

                _logger.LogInformation("Successfully rejected user {UserId} with reason: {Reason}", 
                    request.UserId, request.Reason);

                return new RejectUserResponse
                {
                    Success = true,
                    Message = "User rejected successfully",
                    RejectedAt = userProfile.UpdatedAt ?? DateTime.UtcNow
                };
            }
            catch (UserManagementDomainException ex)
            {
                _logger.LogWarning("Domain validation failed for user rejection: {Message}", ex.Message);
                return new RejectUserResponse
                {
                    Success = false,
                    Message = ex.Message
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error rejecting user {UserId}", request.UserId);
                return new RejectUserResponse
                {
                    Success = false,
                    Message = "An error occurred while rejecting the user"
                };
            }
        }
    }
}
