using System;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.Extensions.Logging;
using MobileWorkflow.Application.DTOs;
using MobileWorkflow.Application.Services;

namespace MobileWorkflow.Application.Commands.UIComponents
{
    public class CreateUIComponentCommandHandler : IRequestHandler<CreateUIComponentCommand, UIComponentDto>
    {
        private readonly ICrossPlatformUIService _uiService;
        private readonly ILogger<CreateUIComponentCommandHandler> _logger;

        public CreateUIComponentCommandHandler(
            ICrossPlatformUIService uiService,
            ILogger<CreateUIComponentCommandHandler> logger)
        {
            _uiService = uiService;
            _logger = logger;
        }

        public async Task<UIComponentDto> Handle(CreateUIComponentCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Creating UI component {Name} of type {Type}",
                    request.Name, request.ComponentType);

                var createRequest = new MobileWorkflow.Application.Services.CreateUIComponentRequest
                {
                    Name = request.Name,
                    DisplayName = request.DisplayName,
                    Description = request.Description,
                    ComponentType = request.ComponentType,
                    Category = request.Category,
                    Version = request.Version,
                    SupportedPlatforms = request.SupportedPlatforms,
                    Properties = request.Properties,
                    Styling = request.Styling,
                    Configuration = request.Configuration,
                    PlatformSpecific = request.PlatformSpecific,
                    Dependencies = request.Dependencies,
                    Tags = request.Tags,
                    CreatedBy = request.CreatedBy,
                    IsTemplate = request.IsTemplate,
                    IsPublic = request.IsPublic
                };

                var result = await _uiService.CreateComponentAsync(createRequest, cancellationToken);

                _logger.LogInformation("Successfully created UI component {ComponentId}", result.Id);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating UI component {Name}", request.Name);
                throw;
            }
        }
    }
}
