using CommunicationNotification.Domain.Enums;
using Shared.Domain.Common;

namespace CommunicationNotification.Domain.Entities;

public class Template : BaseEntity
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public MessageType MessageType { get; private set; }
    public NotificationChannel Channel { get; private set; }
    public Language Language { get; private set; }
    public string Subject { get; private set; }
    public string Body { get; private set; }
    public bool IsActive { get; private set; }
    public Dictionary<string, string> Variables { get; private set; }
    public Dictionary<string, string> Metadata { get; private set; }

    // Navigation properties
    public ICollection<TemplateVersion> Versions { get; private set; }

    private Template()
    {
        Name = string.Empty;
        Description = string.Empty;
        Subject = string.Empty;
        Body = string.Empty;
        Variables = new Dictionary<string, string>();
        Metadata = new Dictionary<string, string>();
        Versions = new List<TemplateVersion>();
    }

    public Template(
        string name,
        string description,
        MessageType messageType,
        NotificationChannel channel,
        Language language,
        string subject,
        string body,
        Dictionary<string, string>? variables = null,
        Dictionary<string, string>? metadata = null)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Template name cannot be empty", nameof(name));

        if (string.IsNullOrWhiteSpace(body))
            throw new ArgumentException("Template body cannot be empty", nameof(body));

        Name = name;
        Description = description ?? string.Empty;
        MessageType = messageType;
        Channel = channel;
        Language = language;
        Subject = subject ?? string.Empty;
        Body = body;
        IsActive = true;
        Variables = variables ?? new Dictionary<string, string>();
        Metadata = metadata ?? new Dictionary<string, string>();
        Versions = new List<TemplateVersion>();
    }

    public static Template Create(
        string name,
        string description,
        MessageType messageType,
        NotificationChannel channel,
        Language language,
        string subject,
        string body,
        Dictionary<string, string>? variables = null,
        Dictionary<string, string>? metadata = null)
    {
        return new Template(name, description, messageType, channel, language,
            subject, body, variables, metadata);
    }

    public void UpdateContent(string subject, string body)
    {
        if (string.IsNullOrWhiteSpace(body))
            throw new ArgumentException("Template body cannot be empty", nameof(body));

        // Create a version before updating
        CreateVersion();

        Subject = subject ?? string.Empty;
        Body = body;
    }

    public void UpdateDescription(string description)
    {
        Description = description ?? string.Empty;
    }

    public void Activate()
    {
        IsActive = true;
    }

    public void Deactivate()
    {
        IsActive = false;
    }

    public void AddVariable(string name, string description)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Variable name cannot be empty", nameof(name));

        Variables[name] = description ?? string.Empty;
    }

    public void RemoveVariable(string name)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Variable name cannot be empty", nameof(name));

        Variables.Remove(name);
    }

    public void AddMetadata(string key, string value)
    {
        if (string.IsNullOrWhiteSpace(key))
            throw new ArgumentException("Metadata key cannot be empty", nameof(key));

        Metadata[key] = value ?? string.Empty;
    }

    public string RenderSubject(Dictionary<string, string> values)
    {
        return RenderTemplate(Subject, values);
    }

    public string RenderBody(Dictionary<string, string> values)
    {
        return RenderTemplate(Body, values);
    }

    private string RenderTemplate(string template, Dictionary<string, string> values)
    {
        if (string.IsNullOrEmpty(template))
            return string.Empty;

        var result = template;

        foreach (var variable in Variables.Keys)
        {
            var placeholder = $"{{{variable}}}";
            var value = values.TryGetValue(variable, out var val) ? val : $"[{variable}]";
            result = result.Replace(placeholder, value);
        }

        return result;
    }

    public bool HasVariable(string variableName)
    {
        return Variables.ContainsKey(variableName);
    }

    public IEnumerable<string> GetMissingVariables(Dictionary<string, string> values)
    {
        return Variables.Keys.Where(variable => !values.ContainsKey(variable));
    }

    public bool IsValidForRendering(Dictionary<string, string> values)
    {
        return !GetMissingVariables(values).Any();
    }

    private void CreateVersion()
    {
        var version = new TemplateVersion(
            Id,
            Versions.Count + 1,
            Body,
            MessageType,
            Guid.Empty, // TODO: Pass actual user ID
            Subject,
            System.Text.Json.JsonSerializer.Serialize(Variables),
            "Template updated");

        Versions.Add(version);
    }

    public TemplateVersion? GetVersion(int versionNumber)
    {
        return Versions.FirstOrDefault(v => v.VersionNumber == versionNumber);
    }

    public TemplateVersion? GetLatestVersion()
    {
        return Versions.OrderByDescending(v => v.VersionNumber).FirstOrDefault();
    }

    public void RestoreVersion(int versionNumber)
    {
        var version = GetVersion(versionNumber);
        if (version == null)
            throw new ArgumentException($"Version {versionNumber} not found", nameof(versionNumber));

        // Create current version before restoring
        CreateVersion();

        Subject = version.Subject ?? string.Empty;
        Body = version.Content;
        Variables = string.IsNullOrEmpty(version.Variables)
            ? new Dictionary<string, string>()
            : System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(version.Variables) ?? new Dictionary<string, string>();
        // Note: TemplateVersion entity doesn't have Metadata property, keeping current Metadata
    }
}
