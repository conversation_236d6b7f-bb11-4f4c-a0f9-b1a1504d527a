using Shared.Domain.Common;
using AuditCompliance.Domain.Enums;
using AuditCompliance.Domain.ValueObjects;
using AuditCompliance.Domain.Events;

namespace AuditCompliance.Domain.Entities;

/// <summary>
/// Immutable audit log entry for tracking all system activities
/// </summary>
public class AuditLog : AggregateRoot
{
    public AuditEventType EventType { get; private set; }
    public AuditSeverity Severity { get; private set; }
    public Guid? UserId { get; private set; }
    public string? UserName { get; private set; }
    public string? UserRole { get; private set; }
    public string EntityType { get; private set; }
    public Guid? EntityId { get; private set; }
    public string Action { get; private set; }
    public string Description { get; private set; }
    public string? OldValues { get; private set; }
    public string? NewValues { get; private set; }
    public AuditContext Context { get; private set; }
    public RetentionPolicy RetentionPolicy { get; private set; }
    public DateTime EventTimestamp { get; private set; }
    public bool IsEncrypted { get; private set; }
    public string? EncryptionKeyId { get; private set; }
    public List<ComplianceStandard> ComplianceFlags { get; private set; }

    private AuditLog()
    {
        ComplianceFlags = new List<ComplianceStandard>();
        EntityType = string.Empty;
        Action = string.Empty;
        Description = string.Empty;
        Context = AuditContext.Create();
        RetentionPolicy = RetentionPolicy.CreateDefault();
    }

    public AuditLog(
        AuditEventType eventType,
        AuditSeverity severity,
        string entityType,
        string action,
        string description,
        Guid? userId = null,
        string? userName = null,
        string? userRole = null,
        Guid? entityId = null,
        string? oldValues = null,
        string? newValues = null,
        AuditContext? context = null,
        RetentionPolicy? retentionPolicy = null,
        List<ComplianceStandard>? complianceFlags = null,
        bool isEncrypted = false,
        string? encryptionKeyId = null)
    {
        if (string.IsNullOrWhiteSpace(entityType))
            throw new ArgumentException("Entity type cannot be empty", nameof(entityType));
        if (string.IsNullOrWhiteSpace(action))
            throw new ArgumentException("Action cannot be empty", nameof(action));
        if (string.IsNullOrWhiteSpace(description))
            throw new ArgumentException("Description cannot be empty", nameof(description));

        EventType = eventType;
        Severity = severity;
        UserId = userId;
        UserName = userName;
        UserRole = userRole;
        EntityType = entityType;
        EntityId = entityId;
        Action = action;
        Description = description;
        OldValues = oldValues;
        NewValues = newValues;
        Context = context ?? AuditContext.Create();
        RetentionPolicy = retentionPolicy ?? RetentionPolicy.CreateDefault();
        EventTimestamp = DateTime.UtcNow;
        IsEncrypted = isEncrypted;
        EncryptionKeyId = encryptionKeyId;
        ComplianceFlags = complianceFlags ?? new List<ComplianceStandard>();

        // Add domain event
        AddDomainEvent(new AuditLogCreatedEvent(this));
    }

    public static AuditLog CreateUserEvent(
        AuditEventType eventType,
        Guid userId,
        string userName,
        string userRole,
        string action,
        string description,
        AuditContext? context = null)
    {
        return new AuditLog(
            eventType,
            AuditSeverity.Info,
            "User",
            action,
            description,
            userId,
            userName,
            userRole,
            userId,
            context: context);
    }

    public static AuditLog CreateSystemEvent(
        AuditEventType eventType,
        AuditSeverity severity,
        string action,
        string description,
        AuditContext? context = null)
    {
        return new AuditLog(
            eventType,
            severity,
            "System",
            action,
            description,
            context: context);
    }

    public static AuditLog CreateSecurityEvent(
        AuditEventType eventType,
        string action,
        string description,
        Guid? userId = null,
        string? userName = null,
        AuditContext? context = null)
    {
        return new AuditLog(
            eventType,
            AuditSeverity.High,
            "Security",
            action,
            description,
            userId,
            userName,
            context: context,
            complianceFlags: new List<ComplianceStandard> { ComplianceStandard.AccessControl });
    }

    public bool IsRetentionExpired()
    {
        return RetentionPolicy.IsExpired(CreatedAt);
    }

    public bool RequiresEncryption()
    {
        return ComplianceFlags.Contains(ComplianceStandard.GDPR) ||
               ComplianceFlags.Contains(ComplianceStandard.PCI_DSS) ||
               Severity >= AuditSeverity.High;
    }


}
