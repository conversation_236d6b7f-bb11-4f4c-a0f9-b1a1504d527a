# Fix missing using statements and namespace issues
Write-Host "Fixing missing using statements and namespace issues..." -ForegroundColor Green

# Define common using statements to add
$commonUsings = @{
    "BaseEntity" = "using Shared.Domain.Common;"
    "AggregateRoot" = "using Shared.Domain.Common;"
    "DomainEvent" = "using Shared.Domain.Common;"
    "Entity" = "using Shared.Domain.Common;"
    "ValueObject" = "using Shared.Domain.Common;"
    "IFormFile" = "using Microsoft.AspNetCore.Http;"
}

# Get all .cs files in Domain projects
$csFiles = Get-ChildItem -Path "Services" -Recurse -Filter "*.cs" | Where-Object { 
    $_.FullName -match "\.Domain\\" -or $_.FullName -match "\.Application\\" 
}

Write-Host "Found $($csFiles.Count) C# files to process" -ForegroundColor Cyan

foreach ($file in $csFiles) {
    Write-Host "Processing $($file.Name)..." -ForegroundColor Yellow
    
    $content = Get-Content $file.FullName -Raw
    $modified = $false
    
    # Check if file needs any of the common using statements
    foreach ($type in $commonUsings.Keys) {
        $usingStatement = $commonUsings[$type]
        
        # If the file contains the type but doesn't have the using statement
        if ($content -match "\b$type\b" -and $content -notmatch [regex]::Escape($usingStatement)) {
            Write-Host "  Adding $usingStatement" -ForegroundColor Cyan
            
            # Find the last using statement or namespace declaration
            if ($content -match "using [^;]+;") {
                $content = $content -replace "(using [^;]+;)", "`$1`n$usingStatement"
                $modified = $true
            } elseif ($content -match "namespace ") {
                $content = $content -replace "(namespace [^{]+{)", "$usingStatement`n`n`$1"
                $modified = $true
            }
        }
    }
    
    # Fix specific namespace issues
    if ($content -match "TripManagement\.Domain\.Common") {
        Write-Host "  Fixing TripManagement.Domain.Common namespace" -ForegroundColor Cyan
        $content = $content -replace "using TripManagement\.Domain\.Common;", "using Shared.Domain.Common;"
        $modified = $true
    }
    
    if ($content -match "MobileWorkflow\.Domain\.Common") {
        Write-Host "  Fixing MobileWorkflow.Domain.Common namespace" -ForegroundColor Cyan
        $content = $content -replace "using MobileWorkflow\.Domain\.Common;", "using Shared.Domain.Common;"
        $modified = $true
    }
    
    # Write back the modified content if changes were made
    if ($modified) {
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8
        Write-Host "  Updated $($file.Name)" -ForegroundColor Green
    }
}

Write-Host "Using statement fixes completed!" -ForegroundColor Green
