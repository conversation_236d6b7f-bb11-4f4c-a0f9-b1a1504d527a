using Shared.Domain.Common;

namespace MonitoringObservability.Domain.Events;

/// <summary>
/// Event raised when a dashboard is created
/// </summary>
public record DashboardCreatedEvent(
    Guid DashboardId,
    string Name,
    Guid CreatedByUserId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

/// <summary>
/// Event raised when a dashboard is updated
/// </summary>
public record DashboardUpdatedEvent(
    Guid DashboardId,
    string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

/// <summary>
/// Event raised when a dashboard layout is updated
/// </summary>
public record DashboardLayoutUpdatedEvent(
    Guid DashboardId,
    string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

/// <summary>
/// Event raised when a dashboard widget is added
/// </summary>
public record DashboardWidgetAddedEvent(
    Guid DashboardId,
    Guid WidgetId,
    string WidgetType) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

/// <summary>
/// Event raised when a dashboard widget is removed
/// </summary>
public record DashboardWidgetRemovedEvent(
    Guid DashboardId,
    Guid WidgetId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

/// <summary>
/// Event raised when a dashboard widget is updated
/// </summary>
public record DashboardWidgetUpdatedEvent(
    Guid DashboardId,
    Guid WidgetId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

/// <summary>
/// Event raised when a dashboard filter is added
/// </summary>
public record DashboardFilterAddedEvent(
    Guid DashboardId,
    Guid FilterId,
    string FilterName) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

/// <summary>
/// Event raised when a dashboard filter is removed
/// </summary>
public record DashboardFilterRemovedEvent(
    Guid DashboardId,
    Guid FilterId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

/// <summary>
/// Event raised when a dashboard permission is granted
/// </summary>
public record DashboardPermissionGrantedEvent(
    Guid DashboardId,
    Guid? UserId,
    string? RoleName,
    string Permission) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

/// <summary>
/// Event raised when a dashboard permission is revoked
/// </summary>
public record DashboardPermissionRevokedEvent(
    Guid DashboardId,
    Guid? UserId,
    string? RoleName) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

/// <summary>
/// Event raised when a dashboard is viewed
/// </summary>
public record DashboardViewedEvent(
    Guid DashboardId,
    Guid UserId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

/// <summary>
/// Event raised when a dashboard is activated
/// </summary>
public record DashboardActivatedEvent(
    Guid DashboardId,
    string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

/// <summary>
/// Event raised when a dashboard is deactivated
/// </summary>
public record DashboardDeactivatedEvent(
    Guid DashboardId,
    string Name) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

/// <summary>
/// Event raised when a dashboard is deleted
/// </summary>
public record DashboardDeletedEvent(
    Guid DashboardId,
    string Name,
    Guid DeletedByUserId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}
