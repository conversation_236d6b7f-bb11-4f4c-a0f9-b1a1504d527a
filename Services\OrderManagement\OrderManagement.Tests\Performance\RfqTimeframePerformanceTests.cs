using FluentAssertions;
using OrderManagement.Domain.Enums;
using OrderManagement.Domain.ValueObjects;
using System.Diagnostics;
using Xunit;
using Xunit.Abstractions;

namespace OrderManagement.Tests.Performance;

public class RfqTimeframePerformanceTests
{
    private readonly ITestOutputHelper _output;

    public RfqTimeframePerformanceTests(ITestOutputHelper output)
    {
        _output = output;
    }

    [Fact]
    public void RfqTimeframe_Creation_ShouldBePerformant()
    {
        // Arrange
        const int iterations = 10000;
        var stopwatch = new Stopwatch();

        // Act
        stopwatch.Start();
        for (int i = 0; i < iterations; i++)
        {
            var timeframe = new RfqTimeframe(72, TimeframeUnit.Hours, true, 3);
            // Access properties to ensure they're calculated
            _ = timeframe.IsExpired;
            _ = timeframe.TotalMinutes;
            _ = timeframe.CanExtend;
        }
        stopwatch.Stop();

        // Assert
        var averageTimeMs = stopwatch.ElapsedMilliseconds / (double)iterations;
        _output.WriteLine($"Average RfqTimeframe creation time: {averageTimeMs:F4} ms");
        
        // Should be very fast - less than 0.1ms per creation
        averageTimeMs.Should().BeLessThan(0.1);
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(1000); // Total should be under 1 second
    }

    [Fact]
    public void RfqTimeframe_IsExpired_ShouldBePerformant()
    {
        // Arrange
        const int iterations = 100000;
        var timeframes = new List<RfqTimeframe>();
        
        // Create a mix of expired and non-expired timeframes
        for (int i = 0; i < 1000; i++)
        {
            var duration = i % 2 == 0 ? 1 : 72; // Half expired (1 minute), half not (72 hours)
            timeframes.Add(new RfqTimeframe(duration, TimeframeUnit.Minutes, true, 3));
        }

        var stopwatch = new Stopwatch();

        // Act
        stopwatch.Start();
        var expiredCount = 0;
        for (int i = 0; i < iterations; i++)
        {
            var timeframe = timeframes[i % timeframes.Count];
            if (timeframe.IsExpired)
            {
                expiredCount++;
            }
        }
        stopwatch.Stop();

        // Assert
        var averageTimeMs = stopwatch.ElapsedMilliseconds / (double)iterations;
        _output.WriteLine($"Average IsExpired check time: {averageTimeMs:F6} ms");
        _output.WriteLine($"Expired count: {expiredCount}");
        
        // Should be extremely fast - less than 0.001ms per check
        averageTimeMs.Should().BeLessThan(0.001);
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(100); // Total should be under 100ms
    }

    [Fact]
    public void RfqTimeframe_Extension_ShouldBePerformant()
    {
        // Arrange
        const int iterations = 1000;
        var stopwatch = new Stopwatch();

        // Act
        stopwatch.Start();
        for (int i = 0; i < iterations; i++)
        {
            var timeframe = new RfqTimeframe(72, TimeframeUnit.Hours, true, 10);
            
            // Extend multiple times
            for (int j = 0; j < 5; j++)
            {
                if (timeframe.CanExtend)
                {
                    timeframe = timeframe.Extend(12, TimeframeUnit.Hours);
                }
            }
        }
        stopwatch.Stop();

        // Assert
        var averageTimeMs = stopwatch.ElapsedMilliseconds / (double)iterations;
        _output.WriteLine($"Average timeframe extension (5 extensions) time: {averageTimeMs:F4} ms");
        
        // Should be fast - less than 1ms per iteration (including 5 extensions)
        averageTimeMs.Should().BeLessThan(1.0);
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(1000); // Total should be under 1 second
    }

    [Fact]
    public void TimeframeExtension_Creation_ShouldBePerformant()
    {
        // Arrange
        const int iterations = 10000;
        var stopwatch = new Stopwatch();
        var extendedBy = Guid.NewGuid();
        var newExpiresAt = DateTime.UtcNow.AddHours(48);

        // Act
        stopwatch.Start();
        for (int i = 0; i < iterations; i++)
        {
            var extension = new TimeframeExtension(
                12, 
                TimeframeUnit.Hours, 
                "Performance test extension", 
                extendedBy, 
                newExpiresAt);
            
            // Access properties to ensure they're calculated
            _ = extension.ExtensionMinutes;
        }
        stopwatch.Stop();

        // Assert
        var averageTimeMs = stopwatch.ElapsedMilliseconds / (double)iterations;
        _output.WriteLine($"Average TimeframeExtension creation time: {averageTimeMs:F4} ms");
        
        // Should be very fast - less than 0.1ms per creation
        averageTimeMs.Should().BeLessThan(0.1);
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(1000); // Total should be under 1 second
    }

    [Theory]
    [InlineData(TimeframeUnit.Minutes, 1000)]
    [InlineData(TimeframeUnit.Hours, 1000)]
    [InlineData(TimeframeUnit.Days, 1000)]
    public void RfqTimeframe_TotalMinutes_Calculation_ShouldBePerformant(TimeframeUnit unit, int iterations)
    {
        // Arrange
        var timeframes = new List<RfqTimeframe>();
        for (int i = 1; i <= 100; i++)
        {
            timeframes.Add(new RfqTimeframe(i, unit, true, 3));
        }

        var stopwatch = new Stopwatch();

        // Act
        stopwatch.Start();
        var totalMinutes = 0;
        for (int i = 0; i < iterations; i++)
        {
            var timeframe = timeframes[i % timeframes.Count];
            totalMinutes += timeframe.TotalMinutes;
        }
        stopwatch.Stop();

        // Assert
        var averageTimeMs = stopwatch.ElapsedMilliseconds / (double)iterations;
        _output.WriteLine($"Average TotalMinutes calculation time for {unit}: {averageTimeMs:F6} ms");
        _output.WriteLine($"Total minutes calculated: {totalMinutes}");
        
        // Should be extremely fast - less than 0.001ms per calculation
        averageTimeMs.Should().BeLessThan(0.001);
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(10); // Total should be under 10ms
    }

    [Fact]
    public void RfqTimeframe_ConcurrentAccess_ShouldBeThreadSafe()
    {
        // Arrange
        const int threadCount = 10;
        const int iterationsPerThread = 1000;
        var timeframe = new RfqTimeframe(72, TimeframeUnit.Hours, true, 3);
        var tasks = new List<Task>();
        var results = new List<bool>[threadCount];

        // Act
        var stopwatch = Stopwatch.StartNew();
        
        for (int t = 0; t < threadCount; t++)
        {
            var threadIndex = t;
            results[threadIndex] = new List<bool>();
            
            tasks.Add(Task.Run(() =>
            {
                for (int i = 0; i < iterationsPerThread; i++)
                {
                    // Access various properties concurrently
                    var isExpired = timeframe.IsExpired;
                    var canExtend = timeframe.CanExtend;
                    var totalMinutes = timeframe.TotalMinutes;
                    var remainingTime = timeframe.RemainingTime;
                    
                    results[threadIndex].Add(isExpired);
                }
            }));
        }

        Task.WaitAll(tasks.ToArray());
        stopwatch.Stop();

        // Assert
        var totalOperations = threadCount * iterationsPerThread;
        var averageTimeMs = stopwatch.ElapsedMilliseconds / (double)totalOperations;
        
        _output.WriteLine($"Concurrent access - Total operations: {totalOperations}");
        _output.WriteLine($"Concurrent access - Total time: {stopwatch.ElapsedMilliseconds} ms");
        _output.WriteLine($"Concurrent access - Average time per operation: {averageTimeMs:F6} ms");
        
        // Verify all threads completed successfully
        results.Should().AllSatisfy(r => r.Should().HaveCount(iterationsPerThread));
        
        // Should handle concurrent access efficiently
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(5000); // Should complete within 5 seconds
        averageTimeMs.Should().BeLessThan(0.1);
    }
}
