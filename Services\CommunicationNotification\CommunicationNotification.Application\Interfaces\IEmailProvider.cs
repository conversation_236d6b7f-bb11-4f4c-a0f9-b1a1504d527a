using CommunicationNotification.Domain.ValueObjects;

namespace CommunicationNotification.Application.Interfaces;

/// <summary>
/// Interface for email notification providers
/// </summary>
public interface IEmailProvider : INotificationProvider
{
    /// <summary>
    /// Send email with optional attachments
    /// </summary>
    /// <param name="emailRequest">Email request details</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Email send result</returns>
    Task<EmailResult> SendEmailAsync(
        EmailRequest emailRequest,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Send bulk emails
    /// </summary>
    /// <param name="bulkEmailRequest">Bulk email request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Bulk email results</returns>
    Task<BulkEmailResult> SendBulkEmailAsync(
        BulkEmailRequest bulkEmailRequest,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Send templated email
    /// </summary>
    /// <param name="templateRequest">Template email request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Email send result</returns>
    Task<EmailResult> SendTemplatedEmailAsync(
        TemplatedEmailRequest templateRequest,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get email delivery status
    /// </summary>
    /// <param name="messageId">Email message ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Email delivery status</returns>
    Task<EmailDeliveryStatus> GetEmailStatusAsync(
        string messageId,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Email request
/// </summary>
public class EmailRequest
{
    public string ToEmail { get; set; } = string.Empty;
    public string? ToName { get; set; }
    public string Subject { get; set; } = string.Empty;
    public string? TextContent { get; set; }
    public string? HtmlContent { get; set; }
    public string? FromEmail { get; set; }
    public string? FromName { get; set; }
    public List<string>? CcEmails { get; set; }
    public List<string>? BccEmails { get; set; }
    public List<EmailAttachment>? Attachments { get; set; }
    public Dictionary<string, string>? Headers { get; set; }
    public bool TrackOpens { get; set; } = true;
    public bool TrackClicks { get; set; } = true;
}

/// <summary>
/// Bulk email request
/// </summary>
public class BulkEmailRequest
{
    public List<EmailRecipient> Recipients { get; set; } = new();
    public string Subject { get; set; } = string.Empty;
    public string? TextContent { get; set; }
    public string? HtmlContent { get; set; }
    public string? FromEmail { get; set; }
    public string? FromName { get; set; }
    public List<EmailAttachment>? Attachments { get; set; }
    public bool TrackOpens { get; set; } = true;
    public bool TrackClicks { get; set; } = true;
}

/// <summary>
/// Templated email request
/// </summary>
public class TemplatedEmailRequest
{
    public string ToEmail { get; set; } = string.Empty;
    public string? ToName { get; set; }
    public string TemplateId { get; set; } = string.Empty;
    public Dictionary<string, object> TemplateData { get; set; } = new();
    public string? FromEmail { get; set; }
    public string? FromName { get; set; }
    public bool TrackOpens { get; set; } = true;
    public bool TrackClicks { get; set; } = true;
}

/// <summary>
/// Email recipient
/// </summary>
public class EmailRecipient
{
    public string Email { get; set; } = string.Empty;
    public string? Name { get; set; }
    public Dictionary<string, object>? PersonalizationData { get; set; }
}

/// <summary>
/// Email attachment
/// </summary>
public class EmailAttachment
{
    public string FileName { get; set; } = string.Empty;
    public byte[] Content { get; set; } = Array.Empty<byte>();
    public string ContentType { get; set; } = "application/octet-stream";
    public string? ContentId { get; set; }
}

/// <summary>
/// Email send result
/// </summary>
public class EmailResult
{
    public bool IsSuccess { get; set; }
    public string? MessageId { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime SentAt { get; set; } = DateTime.UtcNow;

    public static EmailResult Success(string messageId)
    {
        return new EmailResult
        {
            IsSuccess = true,
            MessageId = messageId
        };
    }

    public static EmailResult Failure(string errorMessage)
    {
        return new EmailResult
        {
            IsSuccess = false,
            ErrorMessage = errorMessage
        };
    }
}

/// <summary>
/// Bulk email result
/// </summary>
public class BulkEmailResult
{
    public int TotalEmails { get; set; }
    public int SuccessfulEmails { get; set; }
    public int FailedEmails { get; set; }
    public List<EmailResult> Results { get; set; } = new();

    public bool IsPartialSuccess => SuccessfulEmails > 0 && FailedEmails > 0;
    public bool IsCompleteSuccess => SuccessfulEmails == TotalEmails;
    public bool IsCompleteFailure => FailedEmails == TotalEmails;
}

/// <summary>
/// Email delivery status
/// </summary>
public enum EmailDeliveryStatus
{
    Pending = 1,
    Sent = 2,
    Delivered = 3,
    Opened = 4,
    Clicked = 5,
    Bounced = 6,
    Spam = 7,
    Failed = 8
}
