using MediatR;
using NetworkFleetManagement.Domain.Entities;

namespace NetworkFleetManagement.Application.Commands.PreferredPartners;

public class UpdatePreferredPartnerCommand : IRequest<bool>
{
    public Guid Id { get; set; }
    public PreferenceLevel? PreferenceLevel { get; set; }
    public int? Priority { get; set; }
    public decimal? PreferredCommissionRate { get; set; }
    public decimal? PreferredServiceRate { get; set; }
    public bool? AutoAssignEnabled { get; set; }
    public decimal? AutoAssignThreshold { get; set; }
    public string? Notes { get; set; }
    public List<string>? PreferredRoutes { get; set; }
    public List<string>? PreferredLoadTypes { get; set; }
    public List<string>? ExcludedRoutes { get; set; }
    public List<string>? ExcludedLoadTypes { get; set; }
    public bool? NotifyOnNewOpportunities { get; set; }
    public bool? NotifyOnPerformanceChanges { get; set; }
    public bool? NotifyOnContractExpiry { get; set; }
    
    // Agreement details
    public string? AgreementNumber { get; set; }
    public DateTime? AgreementStartDate { get; set; }
    public DateTime? AgreementEndDate { get; set; }
    public int? PaymentTermsDays { get; set; }
    public string? SpecialTerms { get; set; }
    
    // Exclusivity settings
    public bool? IsExclusive { get; set; }
    public DateTime? ExclusivityExpiresAt { get; set; }
}
