using Microsoft.AspNetCore.Http;

namespace UserManagement.Application.Interfaces;

public interface IImageProcessingService
{
    Task<byte[]> ResizeImageAsync(IFormFile image, int width, int height);
    Task<byte[]> ResizeImageAsync(byte[] imageData, int width, int height, string format, int quality);
    Task<bool> ValidateImageAsync(IFormFile image);
    Task<string> GetImageFormatAsync(IFormFile image);
    Task<byte[]> GenerateThumbnailAsync(IFormFile image, int size);
    Task<ImageAnalysisResult> AnalyzeImageAsync(IFormFile image);
    Task<byte[]> OptimizeForWebAsync(IFormFile image, int quality = 85);
    Task<ImageQualityResult> AnalyzeImageQualityAsync(IFormFile image);
}

public class ImageAnalysisResult
{
    public int Width { get; set; }
    public int Height { get; set; }
    public string Format { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public bool IsValid { get; set; }
    public List<string> Issues { get; set; } = new();
    public string ColorProfile { get; set; } = string.Empty;
    public bool HasTransparency { get; set; }
    public List<string> DominantColors { get; set; } = new();
}

public class ImageQualityResult
{
    public double QualityScore { get; set; }
    public bool IsAcceptable { get; set; }
    public List<string> QualityIssues { get; set; } = new();
    public Dictionary<string, object> Metrics { get; set; } = new();
}
