using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Domain.Common;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Events;
using SubscriptionManagement.Domain.Interfaces;

namespace SubscriptionManagement.Application.Commands.SendManualNotification
{
    public class SendManualNotificationCommandHandler : IRequestHandler<SendManualNotificationCommand, SendManualNotificationResponse>
    {
        private readonly ISubscriptionRepository _subscriptionRepository;
        private readonly IPlanRepository _planRepository;
        private readonly INotificationTemplateService _templateService;
        private readonly ICommunicationService _communicationService;
        private readonly ILogger<SendManualNotificationCommandHandler> _logger;

        public SendManualNotificationCommandHandler(
            ISubscriptionRepository subscriptionRepository,
            IPlanRepository planRepository,
            INotificationTemplateService templateService,
            ICommunicationService communicationService,
            ILogger<SendManualNotificationCommandHandler> logger)
        {
            _subscriptionRepository = subscriptionRepository;
            _planRepository = planRepository;
            _templateService = templateService;
            _communicationService = communicationService;
            _logger = logger;
        }

        public async Task<SendManualNotificationResponse> Handle(SendManualNotificationCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Processing manual notification request. Type: {NotificationType}, Channels: {Channels}, TriggeredBy: {TriggeredByUserId}",
                request.NotificationType, string.Join(",", request.Channels), request.TriggeredByUserId);

            var response = new SendManualNotificationResponse
            {
                NotificationId = Guid.NewGuid(),
                Channels = request.Channels
            };

            try
            {
                // Validate request
                var validationErrors = await ValidateRequest(request, cancellationToken);
                if (validationErrors.Any())
                {
                    response.Success = false;
                    response.Errors = validationErrors;
                    response.Message = "Validation failed";
                    return response;
                }

                // Get subscription and plan details if subscription ID is provided
                Subscription? subscription = null;
                Plan? plan = null;

                if (request.SubscriptionId.HasValue)
                {
                    subscription = await _subscriptionRepository.GetByIdAsync(request.SubscriptionId.Value);
                    if (subscription != null && subscription.PlanId != Guid.Empty)
                    {
                        plan = await _planRepository.GetByIdAsync(subscription.PlanId);
                    }
                }

                // Prepare notification variables
                var variables = PrepareNotificationVariables(request, subscription, plan);

                // Process each channel
                var channelResults = new List<string>();
                var channelErrors = new List<string>();

                foreach (var channel in request.Channels)
                {
                    try
                    {
                        await ProcessChannelNotification(request, channel, variables, cancellationToken);
                        channelResults.Add(channel);
                        _logger.LogInformation("Successfully processed notification for channel: {Channel}", channel);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to process notification for channel: {Channel}", channel);
                        channelErrors.Add($"{channel}: {ex.Message}");
                    }
                }

                // Create domain event for manual notification
                var domainEvent = new ManualNotificationTriggeredEvent(
                    request.SubscriptionId,
                    request.UserId,
                    request.NotificationType,
                    request.CustomMessage,
                    request.Channels,
                    request.TriggeredByUserId,
                    request.ScheduledAt);

                // TODO: Publish domain event through event bus

                response.Success = channelResults.Any();
                response.Channels = channelResults;
                response.Errors = channelErrors;
                response.ScheduledAt = request.ScheduledAt;
                response.Message = response.Success
                    ? $"Notification sent successfully to {channelResults.Count} channel(s)"
                    : "Failed to send notification to any channel";

                _logger.LogInformation("Manual notification processed. Success: {Success}, Channels: {SuccessfulChannels}, Errors: {ErrorCount}",
                    response.Success, channelResults.Count, channelErrors.Count);

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process manual notification request");
                response.Success = false;
                response.Message = $"Internal error: {ex.Message}";
                response.Errors.Add(ex.Message);
                return response;
            }
        }

        private async Task<List<string>> ValidateRequest(SendManualNotificationCommand request, CancellationToken cancellationToken)
        {
            var errors = new List<string>();

            // Either SubscriptionId or UserId must be provided
            if (!request.SubscriptionId.HasValue && !request.UserId.HasValue)
            {
                errors.Add("Either SubscriptionId or UserId must be provided");
            }

            // Validate channels
            if (!request.Channels.Any())
            {
                errors.Add("At least one notification channel must be specified");
            }

            var validChannels = new[] { "Email", "SMS", "WhatsApp", "InApp" };
            var invalidChannels = request.Channels.Where(c => !validChannels.Contains(c)).ToList();
            if (invalidChannels.Any())
            {
                errors.Add($"Invalid channels: {string.Join(", ", invalidChannels)}");
            }

            // Validate subscription exists if provided
            if (request.SubscriptionId.HasValue)
            {
                var subscription = await _subscriptionRepository.GetByIdAsync(request.SubscriptionId.Value);
                if (subscription == null)
                {
                    errors.Add($"Subscription with ID {request.SubscriptionId} not found");
                }
            }

            // Validate scheduled time
            if (request.ScheduledAt.HasValue && request.ScheduledAt.Value <= DateTime.UtcNow)
            {
                errors.Add("Scheduled time must be in the future");
            }

            return errors;
        }

        private Dictionary<string, string> PrepareNotificationVariables(
            SendManualNotificationCommand request,
            Subscription? subscription,
            Plan? plan)
        {
            var variables = new Dictionary<string, string>();

            // Add user variables
            var userId = request.UserId ?? subscription?.UserId ?? Guid.Empty;
            var userVariables = _templateService.GetUserVariables(userId);
            foreach (var kv in userVariables)
            {
                variables[kv.Key] = kv.Value;
            }

            // Add subscription variables if available
            if (subscription != null)
            {
                var subscriptionVariables = _templateService.GetSubscriptionVariables(subscription, plan);
                foreach (var kv in subscriptionVariables)
                {
                    variables[kv.Key] = kv.Value;
                }
            }

            // Add custom message if provided
            if (!string.IsNullOrEmpty(request.CustomMessage))
            {
                variables["CustomMessage"] = request.CustomMessage;
            }

            // Add additional variables
            foreach (var kv in request.AdditionalVariables)
            {
                variables[kv.Key] = kv.Value;
            }

            // Add admin information
            variables["TriggeredByUserId"] = request.TriggeredByUserId.ToString();
            variables["TriggeredAt"] = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm");

            return variables;
        }

        private async Task ProcessChannelNotification(
            SendManualNotificationCommand request,
            string channel,
            Dictionary<string, string> variables,
            CancellationToken cancellationToken)
        {
            try
            {
                // Render the notification template
                var renderedNotification = await _templateService.RenderTemplateAsync(
                    request.NotificationType,
                    channel,
                    variables,
                    request.Language,
                    cancellationToken);

                // Create notification request
                var notificationRequest = new NotificationRequest
                {
                    UserId = request.UserId,
                    Channel = channel,
                    Subject = renderedNotification.Subject,
                    Body = renderedNotification.Body,
                    Type = request.NotificationType,
                    Priority = 1, // High priority for manual notifications
                    Metadata = new Dictionary<string, string>
                    {
                        ["TriggeredByUserId"] = request.TriggeredByUserId.ToString(),
                        ["SubscriptionId"] = request.SubscriptionId?.ToString() ?? "",
                        ["Language"] = request.Language,
                        ["IsManual"] = "true"
                    }
                };

                // Send or schedule notification
                NotificationResult result;
                if (request.ScheduledAt.HasValue)
                {
                    result = await _communicationService.ScheduleNotificationAsync(
                        notificationRequest,
                        request.ScheduledAt.Value,
                        cancellationToken);

                    _logger.LogInformation("Notification scheduled for {ScheduledAt} on channel {Channel}. NotificationId: {NotificationId}",
                        request.ScheduledAt.Value, channel, result.NotificationId);
                }
                else
                {
                    result = await _communicationService.SendNotificationAsync(notificationRequest, cancellationToken);

                    _logger.LogInformation("Notification sent on channel {Channel}. Success: {Success}, NotificationId: {NotificationId}",
                        channel, result.Success, result.NotificationId);
                }

                if (!result.Success)
                {
                    throw new InvalidOperationException($"Failed to send notification: {result.ErrorMessage}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process notification for channel {Channel}", channel);
                throw;
            }
        }
    }
}

