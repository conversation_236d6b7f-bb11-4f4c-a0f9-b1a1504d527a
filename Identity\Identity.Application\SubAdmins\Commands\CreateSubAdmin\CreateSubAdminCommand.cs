using System;
using System.Collections.Generic;
using MediatR;

namespace Identity.Application.SubAdmins.Commands.CreateSubAdmin
{
    public class CreateSubAdminCommand : IRequest<Guid>
    {
        public string Name { get; set; }
        public string Email { get; set; }
        public string Password { get; set; }
        public string Department { get; set; }
        public Guid? SupervisorId { get; set; }
        public List<Guid> RoleIds { get; set; } = new();
        public List<string> AllowedIpAddresses { get; set; } = new();
        public bool RequireTwoFactor { get; set; }
        public DateTime? TemporaryAccessExpiry { get; set; }
        public Guid CreatedBy { get; set; }
    }
}
