# TLI Microservices Platform - Complete Deployment Guide

**Last Updated:** June 30, 2025
**Platform Status:** Production-Ready (85-95% Complete)
**Services:** 15 core microservices + API Gateway + Identity Service

## Table of Contents

1. [Overview](#overview)
2. [Prerequisites](#prerequisites)
3. [Infrastructure Requirements](#infrastructure-requirements)
4. [Cloud Provider Options](#cloud-provider-options)
5. [Docker Deployment](#docker-deployment)
6. [Kubernetes Deployment](#kubernetes-deployment)
7. [Third-Party Services](#third-party-services)
8. [Cost Analysis](#cost-analysis)
9. [Monitoring & Maintenance](#monitoring--maintenance)
10. [Troubleshooting](#troubleshooting)

## Overview

The TLI (Transport Logistics India) platform consists of **15 core microservices** plus **API Gateway** and **Identity Service**, designed for comprehensive logistics management. The platform has achieved **85-95% completion** across all services with **8 services production-ready**. This guide provides step-by-step deployment instructions for production environments.

### 🎯 Deployment Readiness Status

- **Production-Ready Services**: 8 services (95-100% complete)
- **Near-Complete Services**: 6 services (85-94% complete)
- **Operational Services**: 3 services (60-84% complete)
- **Infrastructure**: Docker, Kubernetes, cloud-native deployment ready

### Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Client    │    │   Mobile App    │    │  Admin Portal   │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │      API Gateway          │
                    │      (Port 5000)          │
                    └─────────────┬─────────────┘
                                  │
        ┌─────────────────────────┼─────────────────────────┐
        │                         │                         │
┌───────▼────────┐    ┌───────────▼──────────┐    ┌────────▼────────┐
│ Identity Service│    │ User Management      │    │ Subscription    │
│ (Port 5001)     │    │ (Port 5002)          │    │ (Port 5003)     │
└─────────────────┘    └──────────────────────┘    └─────────────────┘
```

### Service Ports Mapping

#### 🟢 Production-Ready Services (95-100% Complete)

| Service                          | Port | Status   | Purpose                                   |
| -------------------------------- | ---- | -------- | ----------------------------------------- |
| **API Gateway**                  | 5000 | ✅ Ready | Request routing, authentication, CORS     |
| **Identity Service**             | 5001 | ✅ Ready | Authentication, authorization, JWT        |
| **Subscription Management**      | 5003 | ✅ 98%   | Plans, billing, tax config, feature flags |
| **Order Management**             | 5004 | ✅ 96%   | RFQ, quotes, orders, invoices             |
| **Network & Fleet Management**   | 5006 | ✅ 95%   | Vehicle, driver, carrier management       |
| **Trip Management**              | 5005 | ✅ 94%   | Trip tracking, POD, real-time updates     |
| **Financial & Payment**          | 5007 | ✅ 93%   | Payments, escrow, settlements, tax        |
| **Communication & Notification** | 5008 | ✅ 92%   | WhatsApp, SMS, email, push notifications  |
| **Analytics & BI**               | 5009 | ✅ 90%   | Dashboards, reports, predictive analytics |
| **Audit & Compliance**           | 5012 | ✅ 95%   | Audit trails, compliance, export features |

#### 🟡 Near-Complete Services (85-94% Complete)

| Service                        | Port | Status | Purpose                               |
| ------------------------------ | ---- | ------ | ------------------------------------- |
| **User Management**            | 5002 | 🔄 85% | User profiles, KYC, admin panel       |
| **Data & Storage**             | 5010 | 🔄 75% | File management, CDN, search          |
| **Monitoring & Observability** | 5011 | 🔄 65% | Health checks, metrics, alerting      |
| **Mobile & Workflow**          | 5013 | 🔄 60% | Mobile APIs, workflow engine, offline |

## Prerequisites

### System Requirements

- **Operating System**: Ubuntu 20.04 LTS or higher / CentOS 8+ / RHEL 8+
- **CPU**: Minimum 8 cores (16 cores recommended for production)
- **RAM**: Minimum 16GB (32GB recommended for production)
- **Storage**: Minimum 200GB SSD (500GB recommended)
- **Network**: Stable internet connection with static IP

### Software Dependencies

- Docker 24.0+ and Docker Compose 2.0+
- Kubernetes 1.28+ (for K8s deployment)
- Git 2.30+
- curl, wget, unzip
- SSL certificates for HTTPS

### Development Tools (Optional)

- .NET 8 SDK (for local development)
- PostgreSQL client tools
- Redis CLI
- kubectl (for Kubernetes management)

## Infrastructure Requirements

### Database Requirements

```yaml
PostgreSQL 15+:
  - Primary database for all services
  - Minimum: 4 vCPU, 8GB RAM, 100GB SSD
  - Recommended: 8 vCPU, 16GB RAM, 500GB SSD
  - Backup strategy: Daily automated backups

TimescaleDB Extension:
  - For time-series data (analytics, tracking)
  - Extends PostgreSQL with time-series capabilities

Redis 7+:
  - Caching and session storage
  - Minimum: 2 vCPU, 4GB RAM
  - Recommended: 4 vCPU, 8GB RAM
```

### Message Broker

```yaml
RabbitMQ 3.12+:
  - Inter-service communication
  - Event-driven architecture
  - Minimum: 2 vCPU, 4GB RAM
  - Recommended: 4 vCPU, 8GB RAM
```

### Load Balancer

```yaml
NGINX or Cloud Load Balancer:
  - SSL termination
  - Request routing
  - Rate limiting
  - Health checks
```

## Cloud Provider Options

### 1. Digital Ocean Deployment

#### Recommended Droplet Configuration

```yaml
Production Setup:
  App Server:
    - Type: CPU-Optimized
    - Size: c-8 (8 vCPU, 16GB RAM)
    - Storage: 320GB SSD
    - Cost: ₹12,000/month

  Database Server:
    - Type: Memory-Optimized
    - Size: m-4 (4 vCPU, 32GB RAM)
    - Storage: 200GB SSD
    - Cost: ₹15,000/month

  Load Balancer:
    - Digital Ocean Load Balancer
    - Cost: ₹800/month

Total Monthly Cost: ₹27,800 (~$335)
```

#### Digital Ocean Setup Steps

```bash
# 1. Create Droplets
doctl compute droplet create tli-app-server \
  --image ubuntu-20-04-x64 \
  --size c-8 \
  --region blr1 \
  --ssh-keys your-ssh-key-id

doctl compute droplet create tli-db-server \
  --image ubuntu-20-04-x64 \
  --size m-4 \
  --region blr1 \
  --ssh-keys your-ssh-key-id

# 2. Setup Load Balancer
doctl compute load-balancer create \
  --name tli-lb \
  --forwarding-rules entry_protocol:https,entry_port:443,target_protocol:http,target_port:5000 \
  --health-check protocol:http,port:5000,path:/health \
  --region blr1
```

### 2. AWS Deployment

#### Recommended EC2 Configuration

```yaml
Production Setup:
  Application Servers (2x):
    - Type: t3.xlarge (4 vCPU, 16GB RAM)
    - Storage: 200GB gp3 SSD
    - Cost: ₹8,500/month each

  Database:
    - RDS PostgreSQL db.r5.xlarge
    - 4 vCPU, 32GB RAM, 200GB storage
    - Cost: ₹18,000/month

  ElastiCache Redis:
    - cache.r6g.large (2 vCPU, 13GB RAM)
    - Cost: ₹6,000/month

  Application Load Balancer:
    - Cost: ₹1,500/month

Total Monthly Cost: ₹42,500 (~$510)
```

#### AWS Setup with Terraform

```hcl
# main.tf
provider "aws" {
  region = "ap-south-1"
}

resource "aws_instance" "tli_app" {
  count           = 2
  ami             = "ami-0c02fb55956c7d316"
  instance_type   = "t3.xlarge"
  key_name        = "tli-keypair"
  security_groups = [aws_security_group.tli_app.name]

  user_data = file("install-docker.sh")

  tags = {
    Name = "TLI-App-Server-${count.index + 1}"
  }
}
```

### 3. Azure Deployment

#### Recommended VM Configuration

```yaml
Production Setup:
  App Servers (2x):
    - Type: Standard_D4s_v3 (4 vCPU, 16GB RAM)
    - Storage: 200GB Premium SSD
    - Cost: ₹9,000/month each

  Database:
    - Azure Database for PostgreSQL
    - General Purpose, 4 vCores, 32GB RAM
    - Cost: ₹20,000/month

  Redis Cache:
    - Azure Cache for Redis (Standard C2)
    - Cost: ₹7,000/month

  Load Balancer:
    - Azure Load Balancer Standard
    - Cost: ₹1,200/month

Total Monthly Cost: ₹46,200 (~$555)
```

## Docker Deployment

### Step 1: Server Preparation

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.21.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Verify installation
docker --version
docker-compose --version
```

### Step 2: Clone Repository

```bash
# Clone the TLI repository
git clone https://github.com/your-org/TLIMicroservices.git
cd TLIMicroservices

# Create environment file
cp .env.example .env
```

### Step 3: Configure Environment Variables

```bash
# Edit .env file
nano .env
```

```env
# Database Configuration
POSTGRES_DB=tli_microservices
POSTGRES_USER=tli_admin
POSTGRES_PASSWORD=your_secure_password_here
POSTGRES_HOST=postgres
POSTGRES_PORT=5432

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-that-is-at-least-32-characters-long
JWT_ISSUER=TLI.Identity
JWT_AUDIENCE=TLI.Services
JWT_EXPIRY_MINUTES=60

# RabbitMQ Configuration
RABBITMQ_DEFAULT_USER=tli_admin
RABBITMQ_DEFAULT_PASS=your_rabbitmq_password
RABBITMQ_HOST=rabbitmq
RABBITMQ_PORT=5672

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# External Services
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password

# Payment Gateway (Razorpay)
RAZORPAY_KEY_ID=your_razorpay_key_id
RAZORPAY_KEY_SECRET=your_razorpay_key_secret

# Maps API
GOOGLE_MAPS_API_KEY=your_google_maps_api_key

# File Storage
STORAGE_PROVIDER=Local
STORAGE_PATH=/app/storage
MAX_FILE_SIZE=104857600

# Monitoring
APPLICATION_INSIGHTS_KEY=your_app_insights_key
```

### Step 4: Build and Deploy Services

```bash
# Build all Docker images
docker-compose build

# Start infrastructure services first
docker-compose up -d postgres redis rabbitmq

# Wait for services to be ready (30 seconds)
sleep 30

# Start all application services
docker-compose up -d

# Check service status
docker-compose ps

# View logs
docker-compose logs -f apigateway
```

### Step 5: Initialize Databases

```bash
# Run database migrations
docker-compose exec identity-api dotnet ef database update
docker-compose exec usermanagement-api dotnet ef database update
docker-compose exec subscription-api dotnet ef database update
docker-compose exec order-api dotnet ef database update
docker-compose exec trip-api dotnet ef database update
docker-compose exec fleet-api dotnet ef database update
docker-compose exec payment-api dotnet ef database update
docker-compose exec communication-api dotnet ef database update
docker-compose exec analytics-api dotnet ef database update
docker-compose exec storage-api dotnet ef database update
docker-compose exec audit-api dotnet ef database update
docker-compose exec monitoring-api dotnet ef database update

# Seed initial data (optional)
docker-compose exec identity-api dotnet run --seed-data
```

### Step 6: Configure NGINX Reverse Proxy

```bash
# Install NGINX
sudo apt install nginx -y

# Create TLI configuration
sudo nano /etc/nginx/sites-available/tli
```

```nginx
upstream tli_backend {
    server localhost:5000;
}

server {
    listen 80;
    server_name your-domain.com www.your-domain.com;

    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;

    # SSL Configuration
    ssl_certificate /etc/ssl/certs/your-domain.crt;
    ssl_certificate_key /etc/ssl/private/your-domain.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    # Security Headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";

    # Rate Limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req zone=api burst=20 nodelay;

    # Main API proxy
    location / {
        proxy_pass http://tli_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # Buffer settings
        proxy_buffering on;
        proxy_buffer_size 128k;
        proxy_buffers 4 256k;
        proxy_busy_buffers_size 256k;
    }

    # WebSocket support for SignalR
    location /hubs/ {
        proxy_pass http://tli_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    # Health check endpoint
    location /health {
        proxy_pass http://tli_backend/health;
        access_log off;
    }

    # Static files (if any)
    location /static/ {
        alias /var/www/tli/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

```bash
# Enable the site
sudo ln -s /etc/nginx/sites-available/tli /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### Step 7: Setup SSL Certificate (Let's Encrypt)

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx -y

# Obtain SSL certificate
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# Test auto-renewal
sudo certbot renew --dry-run
```

### Step 8: Configure Firewall

```bash
# Setup UFW firewall
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw status
```

## Kubernetes Deployment

### Step 1: Kubernetes Cluster Setup

#### Option A: Managed Kubernetes (Recommended)

```bash
# Digital Ocean Kubernetes
doctl kubernetes cluster create tli-cluster \
  --region blr1 \
  --node-pool "name=worker-pool;size=s-4vcpu-8gb;count=3;auto-scale=true;min-nodes=2;max-nodes=5"

# AWS EKS
eksctl create cluster \
  --name tli-cluster \
  --region ap-south-1 \
  --nodegroup-name workers \
  --node-type t3.medium \
  --nodes 3 \
  --nodes-min 2 \
  --nodes-max 5

# Azure AKS
az aks create \
  --resource-group tli-rg \
  --name tli-cluster \
  --node-count 3 \
  --node-vm-size Standard_D2s_v3 \
  --enable-addons monitoring
```

#### Option B: Self-Managed Kubernetes (kubeadm)

```bash
# On master node
sudo kubeadm init --pod-network-cidr=**********/16

# Setup kubectl for regular user
mkdir -p $HOME/.kube
sudo cp -i /etc/kubernetes/admin.conf $HOME/.kube/config
sudo chown $(id -u):$(id -g) $HOME/.kube/config

# Install Flannel network plugin
kubectl apply -f https://raw.githubusercontent.com/coreos/flannel/master/Documentation/kube-flannel.yml

# Join worker nodes (run on worker nodes)
sudo kubeadm join <master-ip>:6443 --token <token> --discovery-token-ca-cert-hash <hash>
```

### Step 2: Install Required Tools

```bash
# Install kubectl
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl

# Install Helm
curl https://baltocdn.com/helm/signing.asc | gpg --dearmor | sudo tee /usr/share/keyrings/helm.gpg > /dev/null
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/helm.gpg] https://baltocdn.com/helm/stable/debian/ all main" | sudo tee /etc/apt/sources.list.d/helm-stable-debian.list
sudo apt update
sudo apt install helm

# Verify installation
kubectl version --client
helm version
```

### Step 3: Create Kubernetes Manifests

```bash
# Create namespace
kubectl create namespace tli-production

# Create secrets
kubectl create secret generic tli-secrets \
  --from-literal=postgres-password=your_secure_password \
  --from-literal=jwt-secret=your-super-secret-jwt-key \
  --from-literal=rabbitmq-password=your_rabbitmq_password \
  --from-literal=redis-password=your_redis_password \
  --namespace=tli-production
```

### Step 4: Deploy Infrastructure Services

```yaml
# postgres-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  namespace: tli-production
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
        - name: postgres
          image: postgres:15
          env:
            - name: POSTGRES_DB
              value: 'tli_microservices'
            - name: POSTGRES_USER
              value: 'tli_admin'
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: tli-secrets
                  key: postgres-password
          ports:
            - containerPort: 5432
          volumeMounts:
            - name: postgres-storage
              mountPath: /var/lib/postgresql/data
          resources:
            requests:
              memory: '2Gi'
              cpu: '1000m'
            limits:
              memory: '4Gi'
              cpu: '2000m'
      volumes:
        - name: postgres-storage
          persistentVolumeClaim:
            claimName: postgres-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: postgres
  namespace: tli-production
spec:
  selector:
    app: postgres
  ports:
    - port: 5432
      targetPort: 5432
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-pvc
  namespace: tli-production
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 100Gi
```

```yaml
# redis-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: tli-production
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
        - name: redis
          image: redis:7-alpine
          command: ['redis-server']
          args: ['--requirepass', '$(REDIS_PASSWORD)']
          env:
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: tli-secrets
                  key: redis-password
          ports:
            - containerPort: 6379
          resources:
            requests:
              memory: '1Gi'
              cpu: '500m'
            limits:
              memory: '2Gi'
              cpu: '1000m'
---
apiVersion: v1
kind: Service
metadata:
  name: redis
  namespace: tli-production
spec:
  selector:
    app: redis
  ports:
    - port: 6379
      targetPort: 6379
```

### Step 5: Deploy Application Services

```bash
# Apply infrastructure
kubectl apply -f postgres-deployment.yaml
kubectl apply -f redis-deployment.yaml
kubectl apply -f rabbitmq-deployment.yaml

# Wait for infrastructure to be ready
kubectl wait --for=condition=available --timeout=300s deployment/postgres -n tli-production
kubectl wait --for=condition=available --timeout=300s deployment/redis -n tli-production
kubectl wait --for=condition=available --timeout=300s deployment/rabbitmq -n tli-production

# Deploy application services
kubectl apply -f identity-service-deployment.yaml
kubectl apply -f user-management-deployment.yaml
kubectl apply -f subscription-management-deployment.yaml
kubectl apply -f order-management-deployment.yaml
kubectl apply -f trip-management-deployment.yaml
kubectl apply -f fleet-management-deployment.yaml
kubectl apply -f payment-service-deployment.yaml
kubectl apply -f communication-service-deployment.yaml
kubectl apply -f analytics-service-deployment.yaml
kubectl apply -f storage-service-deployment.yaml
kubectl apply -f audit-service-deployment.yaml
kubectl apply -f monitoring-service-deployment.yaml

# Deploy API Gateway last
kubectl apply -f api-gateway-deployment.yaml

# Setup ingress controller
kubectl apply -f ingress-nginx-controller.yaml
kubectl apply -f tli-ingress.yaml
```

### Step 6: Configure Ingress

```yaml
# tli-ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: tli-ingress
  namespace: tli-production
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/rate-limit: '100'
    nginx.ingress.kubernetes.io/rate-limit-window: '1m'
    nginx.ingress.kubernetes.io/ssl-redirect: 'true'
    nginx.ingress.kubernetes.io/proxy-body-size: '50m'
spec:
  tls:
    - hosts:
        - api.your-domain.com
      secretName: tli-tls
  rules:
    - host: api.your-domain.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: api-gateway
                port:
                  number: 80
```

## Third-Party Services

### 1. Email Service Providers

```yaml
SendGrid:
  - Cost: $14.95/month (40,000 emails)
  - Setup: API key configuration
  - Features: Templates, analytics, deliverability

Amazon SES:
  - Cost: $0.10 per 1,000 emails
  - Setup: AWS credentials
  - Features: High deliverability, bounce handling

SMTP (Gmail/Outlook):
  - Cost: Free (limited) / $6/user/month
  - Setup: App passwords
  - Features: Basic email sending
```

### 2. SMS Service Providers

```yaml
Twilio:
  - Cost: ₹0.60 per SMS (India)
  - Setup: Account SID, Auth Token
  - Features: Global coverage, delivery reports

AWS SNS:
  - Cost: ₹0.50 per SMS (India)
  - Setup: AWS credentials
  - Features: Reliable delivery, multiple regions

MSG91:
  - Cost: ₹0.40 per SMS (India)
  - Setup: API key
  - Features: India-focused, OTP templates
```

### 3. Payment Gateway Providers

```yaml
Razorpay:
  - Cost: 2% + ₹2 per transaction
  - Setup: Key ID, Key Secret
  - Features: UPI, cards, wallets, EMI

Stripe:
  - Cost: 2.9% + ₹2 per transaction
  - Setup: Publishable key, Secret key
  - Features: International payments, subscriptions

PayU:
  - Cost: 2.5% + ₹2 per transaction
  - Setup: Merchant key, Salt
  - Features: India-focused, multiple payment modes
```

### 4. Maps & Location Services

```yaml
Google Maps:
  - Cost: $2 per 1,000 requests (after free tier)
  - Setup: API key
  - Features: Geocoding, routing, places

MapBox:
  - Cost: $0.50 per 1,000 requests
  - Setup: Access token
  - Features: Custom styling, navigation

HERE Maps:
  - Cost: $1 per 1,000 requests
  - Setup: API key
  - Features: Fleet tracking, routing optimization
```

### 5. File Storage Providers

```yaml
AWS S3:
  - Cost: $0.023 per GB/month
  - Setup: AWS credentials, bucket configuration
  - Features: CDN integration, versioning

Google Cloud Storage:
  - Cost: $0.020 per GB/month
  - Setup: Service account, bucket
  - Features: Global distribution, lifecycle management

Azure Blob Storage:
  - Cost: $0.018 per GB/month
  - Setup: Connection string
  - Features: Hot/cool/archive tiers
```

### 6. Monitoring & Analytics

```yaml
Application Insights (Azure):
  - Cost: $2.30 per GB ingested
  - Setup: Instrumentation key
  - Features: APM, custom metrics, alerts

New Relic:
  - Cost: $25/month per host
  - Setup: License key
  - Features: Full-stack monitoring, AI insights

DataDog:
  - Cost: $15/month per host
  - Setup: API key
  - Features: Infrastructure monitoring, logs
```

## Cost Analysis (Monthly in INR)

### Small Scale Deployment (1,000 active users)

```yaml
Digital Ocean:
  - App Server (2GB): ₹1,600
  - Database (4GB): ₹3,200
  - Load Balancer: ₹800
  - Storage (50GB): ₹400
  Total Infrastructure: ₹6,000

Third-Party Services:
  - Email (SendGrid): ₹1,200
  - SMS (MSG91): ₹2,000
  - Payment (Razorpay): ₹5,000
  - Maps (Google): ₹1,500
  - Storage (AWS S3): ₹500
  Total Services: ₹10,200

Grand Total: ₹16,200 (~$195)
```

### Medium Scale Deployment (10,000 active users)

```yaml
AWS:
  - App Servers (2x t3.large): ₹12,000
  - RDS PostgreSQL: ₹18,000
  - ElastiCache Redis: ₹6,000
  - Load Balancer: ₹1,500
  - Storage (200GB): ₹1,500
  Total Infrastructure: ₹39,000

Third-Party Services:
  - Email (SendGrid): ₹3,000
  - SMS (Twilio): ₹8,000
  - Payment (Razorpay): ₹25,000
  - Maps (Google): ₹5,000
  - Storage (AWS S3): ₹2,000
  - Monitoring (New Relic): ₹6,000
  Total Services: ₹49,000

Grand Total: ₹88,000 (~$1,060)
```

### Large Scale Deployment (100,000 active users)

```yaml
Azure:
  - App Servers (4x Standard_D4s_v3): ₹36,000
  - Database (Azure PostgreSQL): ₹45,000
  - Redis Cache: ₹15,000
  - Load Balancer: ₹3,000
  - Storage (1TB): ₹5,000
  - CDN: ₹2,000
  Total Infrastructure: ₹106,000

Third-Party Services:
  - Email (SendGrid): ₹8,000
  - SMS (Twilio): ₹25,000
  - Payment (Razorpay): ₹100,000
  - Maps (Google): ₹15,000
  - Storage (AWS S3): ₹8,000
  - Monitoring (DataDog): ₹15,000
  Total Services: ₹171,000

Grand Total: ₹277,000 (~$3,330)
```

## Monitoring & Maintenance

### Health Checks Setup

```bash
# Create health check script
cat > /opt/tli/health-check.sh << 'EOF'
#!/bin/bash

SERVICES=(
  "http://localhost:5000/health"
  "http://localhost:5001/health"
  "http://localhost:5002/health"
  "http://localhost:5003/health"
  "http://localhost:5004/health"
  "http://localhost:5005/health"
  "http://localhost:5006/health"
  "http://localhost:5007/health"
  "http://localhost:5008/health"
  "http://localhost:5009/health"
  "http://localhost:5010/health"
  "http://localhost:5011/health"
  "http://localhost:5012/health"
)

for service in "${SERVICES[@]}"; do
  if curl -f -s "$service" > /dev/null; then
    echo "✅ $service - OK"
  else
    echo "❌ $service - FAILED"
    # Send alert notification
    curl -X POST "https://api.telegram.org/bot$TELEGRAM_BOT_TOKEN/sendMessage" \
      -d "chat_id=$TELEGRAM_CHAT_ID" \
      -d "text=🚨 TLI Service Alert: $service is down!"
  fi
done
EOF

chmod +x /opt/tli/health-check.sh

# Setup cron job for health checks
echo "*/5 * * * * /opt/tli/health-check.sh" | crontab -
```

### Backup Strategy

```bash
# Database backup script
cat > /opt/tli/backup-db.sh << 'EOF'
#!/bin/bash

BACKUP_DIR="/opt/tli/backups"
DATE=$(date +%Y%m%d_%H%M%S)
DATABASES=(
  "TLI_Identity"
  "TLI_UserManagement"
  "TLI_SubscriptionManagement"
  "TLI_OrderManagement"
  "TLI_TripManagement"
  "TLI_NetworkFleetManagement"
  "TLI_FinancialPayment"
  "TLI_CommunicationNotification"
  "TLI_AnalyticsBIService"
  "TLI_DataStorage"
  "TLI_AuditCompliance"
  "TLI_MonitoringObservability"
)

mkdir -p $BACKUP_DIR

for db in "${DATABASES[@]}"; do
  echo "Backing up $db..."
  docker exec tli-postgres pg_dump -U tli_admin $db | gzip > "$BACKUP_DIR/${db}_${DATE}.sql.gz"
done

# Upload to cloud storage
aws s3 sync $BACKUP_DIR s3://tli-backups/database/

# Cleanup old backups (keep 30 days)
find $BACKUP_DIR -name "*.sql.gz" -mtime +30 -delete
EOF

chmod +x /opt/tli/backup-db.sh

# Schedule daily backups
echo "0 2 * * * /opt/tli/backup-db.sh" | crontab -
```

### Log Management

```bash
# Setup log rotation
cat > /etc/logrotate.d/tli << 'EOF'
/var/log/tli/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 root root
    postrotate
        docker-compose restart rsyslog
    endscript
}
EOF

# Setup centralized logging with ELK stack
docker run -d \
  --name elasticsearch \
  -p 9200:9200 \
  -e "discovery.type=single-node" \
  elasticsearch:7.17.0

docker run -d \
  --name kibana \
  -p 5601:5601 \
  --link elasticsearch:elasticsearch \
  kibana:7.17.0

docker run -d \
  --name logstash \
  -p 5044:5044 \
  --link elasticsearch:elasticsearch \
  logstash:7.17.0
```

### Performance Monitoring

```bash
# Install monitoring tools
sudo apt install htop iotop nethogs -y

# Setup Prometheus monitoring
docker run -d \
  --name prometheus \
  -p 9090:9090 \
  -v /opt/tli/prometheus.yml:/etc/prometheus/prometheus.yml \
  prom/prometheus

# Setup Grafana dashboards
docker run -d \
  --name grafana \
  -p 3000:3000 \
  -e "GF_SECURITY_ADMIN_PASSWORD=admin123" \
  grafana/grafana
```

## Troubleshooting

### Common Issues and Solutions

#### 1. Service Not Starting

```bash
# Check service logs
docker-compose logs service-name

# Check resource usage
docker stats

# Restart specific service
docker-compose restart service-name

# Check database connectivity
docker-compose exec service-name dotnet ef database update
```

#### 2. Database Connection Issues

```bash
# Check PostgreSQL status
docker-compose exec postgres psql -U tli_admin -d tli_microservices -c "SELECT 1;"

# Reset database connections
docker-compose restart postgres

# Check connection string in environment
docker-compose exec service-name env | grep CONNECTION
```

#### 3. High Memory Usage

```bash
# Check memory usage by service
docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"

# Restart memory-intensive services
docker-compose restart analytics-api

# Scale down replicas if using Docker Swarm
docker service scale tli_analytics-api=1
```

#### 4. SSL Certificate Issues

```bash
# Check certificate expiry
openssl x509 -in /etc/ssl/certs/your-domain.crt -text -noout | grep "Not After"

# Renew Let's Encrypt certificate
sudo certbot renew

# Test SSL configuration
openssl s_client -connect your-domain.com:443 -servername your-domain.com
```

#### 5. Performance Issues

```bash
# Check API response times
curl -w "@curl-format.txt" -o /dev/null -s "http://localhost:5000/health"

# Monitor database queries
docker-compose exec postgres psql -U tli_admin -d tli_microservices -c "SELECT query, calls, total_time FROM pg_stat_statements ORDER BY total_time DESC LIMIT 10;"

# Check Redis performance
docker-compose exec redis redis-cli info stats
```

### Emergency Procedures

#### 1. Complete System Restart

```bash
# Stop all services
docker-compose down

# Clean up containers and networks
docker system prune -f

# Restart infrastructure first
docker-compose up -d postgres redis rabbitmq

# Wait and start application services
sleep 30
docker-compose up -d
```

#### 2. Database Recovery

```bash
# Stop all services
docker-compose down

# Restore from backup
gunzip -c /opt/tli/backups/TLI_UserManagement_20240101_020000.sql.gz | \
  docker exec -i tli-postgres psql -U tli_admin TLI_UserManagement

# Start services
docker-compose up -d
```

#### 3. Rollback Deployment

```bash
# For Docker deployment
docker-compose down
git checkout previous-stable-tag
docker-compose build
docker-compose up -d

# For Kubernetes deployment
kubectl rollout undo deployment/api-gateway -n tli-production
kubectl rollout undo deployment/user-management -n tli-production
```

### Support Contacts

- **Technical Support**: <EMAIL>
- **Emergency Hotline**: +91-XXXX-XXXX-XX
- **Documentation**: https://docs.tli.com
- **Status Page**: https://status.tli.com

---

## Conclusion

This deployment guide provides comprehensive instructions for deploying the TLI microservices platform across different cloud providers and deployment methods. Choose the approach that best fits your requirements, budget, and technical expertise.

For production deployments, always:

1. Use managed services where possible
2. Implement proper monitoring and alerting
3. Setup automated backups
4. Configure SSL/TLS encryption
5. Follow security best practices
6. Plan for disaster recovery

Regular maintenance, monitoring, and updates are crucial for maintaining a healthy production environment.
