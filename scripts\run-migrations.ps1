# TLI Microservices - Database Migration and Seeding Script
# This script runs Entity Framework migrations and seeds data for all services

param(
    [string]$Environment = "Development",
    [switch]$SeedData = $true,
    [switch]$Force = $false,
    [string[]]$Services = @()
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Define service configurations
$ServiceConfigs = @{
    "Identity" = @{
        "Path" = "Identity/Identity.API"
        "Project" = "Identity.API.csproj"
        "Context" = "IdentityDbContext"
        "Assembly" = "Identity.Infrastructure"
        "Database" = "TLI_Identity"
        "Port" = 7000
    }
    "UserManagement" = @{
        "Path" = "Services/UserManagement/UserManagement.API"
        "Project" = "UserManagement.API.csproj"
        "Context" = "UserManagementDbContext"
        "Assembly" = "UserManagement.Infrastructure"
        "Database" = "TLI_UserManagement"
        "Port" = 7001
    }
    "SubscriptionManagement" = @{
        "Path" = "Services/SubscriptionManagement/SubscriptionManagement.API"
        "Project" = "SubscriptionManagement.API.csproj"
        "Context" = "SubscriptionDbContext"
        "Assembly" = "SubscriptionManagement.Infrastructure"
        "Database" = "TLI_SubscriptionManagement"
        "Port" = 7003
    }
    "OrderManagement" = @{
        "Path" = "Services/OrderManagement/OrderManagement.API"
        "Project" = "OrderManagement.API.csproj"
        "Context" = "OrderManagementDbContext"
        "Assembly" = "OrderManagement.Infrastructure"
        "Database" = "TLI_OrderManagement"
        "Port" = 7002
    }
    "TripManagement" = @{
        "Path" = "Services/TripManagement/TripManagement.API"
        "Project" = "TripManagement.API.csproj"
        "Context" = "TripManagementDbContext"
        "Assembly" = "TripManagement.Infrastructure"
        "Database" = "TLI_TripManagement"
        "Port" = 7004
    }
    "NetworkFleetManagement" = @{
        "Path" = "Services/NetworkFleetManagement/NetworkFleetManagement.API"
        "Project" = "NetworkFleetManagement.API.csproj"
        "Context" = "NetworkFleetManagementDbContext"
        "Assembly" = "NetworkFleetManagement.Infrastructure"
        "Database" = "TLI_NetworkFleetManagement"
        "Port" = 7005
    }
    "FinancialPayment" = @{
        "Path" = "Services/FinancialPayment/FinancialPayment.API"
        "Project" = "FinancialPayment.API.csproj"
        "Context" = "FinancialPaymentDbContext"
        "Assembly" = "FinancialPayment.Infrastructure"
        "Database" = "TLI_FinancialPayment"
        "Port" = 7006
    }
    "CommunicationNotification" = @{
        "Path" = "Services/CommunicationNotification/CommunicationNotification.API"
        "Project" = "CommunicationNotification.API.csproj"
        "Context" = "CommunicationNotificationDbContext"
        "Assembly" = "CommunicationNotification.Infrastructure"
        "Database" = "TLI_CommunicationNotification"
        "Port" = 7007
    }
    "AnalyticsBIService" = @{
        "Path" = "Services/AnalyticsBIService/AnalyticsBIService.API"
        "Project" = "AnalyticsBIService.API.csproj"
        "Context" = "AnalyticsBIDbContext"
        "Assembly" = "AnalyticsBIService.Infrastructure"
        "Database" = "TLI_AnalyticsBI"
        "Port" = 7008
    }
    "DataStorage" = @{
        "Path" = "Services/DataStorage/DataStorage.API"
        "Project" = "DataStorage.API.csproj"
        "Context" = "DataStorageDbContext"
        "Assembly" = "DataStorage.Infrastructure"
        "Database" = "TLI_DataStorage"
        "Port" = 7009
    }
    "MonitoringObservability" = @{
        "Path" = "Services/MonitoringObservability/MonitoringObservability.API"
        "Project" = "MonitoringObservability.API.csproj"
        "Context" = "MonitoringDbContext"
        "Assembly" = "MonitoringObservability.Infrastructure"
        "Database" = "TLI_MonitoringObservability"
        "Port" = 7010
    }
    "AuditCompliance" = @{
        "Path" = "Services/AuditCompliance/AuditCompliance.API"
        "Project" = "AuditCompliance.API.csproj"
        "Context" = "AuditComplianceDbContext"
        "Assembly" = "AuditCompliance.Infrastructure"
        "Database" = "TLI_AuditCompliance"
        "Port" = 7011
    }
    "MobileWorkflow" = @{
        "Path" = "Services/MobileWorkflow/MobileWorkflow.API"
        "Project" = "MobileWorkflow.API.csproj"
        "Context" = "MobileWorkflowDbContext"
        "Assembly" = "MobileWorkflow.Infrastructure"
        "Database" = "TLI_MobileWorkflow"
        "Port" = 7012
    }
}

# Default to core services if none specified
if ($Services.Count -eq 0) {
    $Services = @(
        "Identity",
        "UserManagement",
        "SubscriptionManagement",
        "OrderManagement",
        "TripManagement",
        "NetworkFleetManagement",
        "FinancialPayment",
        "CommunicationNotification",
        "AnalyticsBIService",
        "DataStorage",
        "MonitoringObservability",
        "AuditCompliance",
        "MobileWorkflow"
    )
}

# Function to check if PostgreSQL is running
function Test-PostgreSQLConnection {
    try {
        $connectionString = "Host=localhost;Port=5432;Database=postgres;User Id=timescale;Password=timescale"
        $connection = New-Object Npgsql.NpgsqlConnection($connectionString)
        $connection.Open()
        $connection.Close()
        return $true
    }
    catch {
        return $false
    }
}

# Function to run EF migrations
function Invoke-EFMigration {
    param(
        [string]$ServiceName,
        [hashtable]$Config
    )
    
    Write-Host "🔄 Running migrations for $ServiceName..." -ForegroundColor Yellow
    
    $originalLocation = Get-Location
    try {
        Set-Location $Config.Path
        
        # Check if migrations exist
        $migrationsPath = "../$($Config.Assembly)/Migrations"
        if (-not (Test-Path $migrationsPath)) {
            Write-Host "⚠️  No migrations found for $ServiceName, creating initial migration..." -ForegroundColor Yellow
            
            # Create initial migration
            $migrationName = "InitialCreate"
            dotnet ef migrations add $migrationName --context $Config.Context --project "../$($Config.Assembly)" --startup-project $Config.Project
            
            if ($LASTEXITCODE -ne 0) {
                throw "Failed to create initial migration for $ServiceName"
            }
        }
        
        # Apply migrations
        $updateArgs = @(
            "ef", "database", "update",
            "--context", $Config.Context,
            "--project", "../$($Config.Assembly)",
            "--startup-project", $Config.Project,
            "--environment", $Environment
        )
        
        if ($Force) {
            $updateArgs += "--force"
        }
        
        & dotnet $updateArgs
        
        if ($LASTEXITCODE -ne 0) {
            throw "Failed to apply migrations for $ServiceName"
        }
        
        Write-Host "✅ Migrations completed for $ServiceName" -ForegroundColor Green
    }
    finally {
        Set-Location $originalLocation
    }
}

# Function to seed data
function Invoke-DataSeeding {
    param(
        [string]$ServiceName,
        [hashtable]$Config
    )
    
    Write-Host "🌱 Seeding data for $ServiceName..." -ForegroundColor Yellow
    
    $originalLocation = Get-Location
    try {
        Set-Location $Config.Path
        
        # Run the application with seed data flag
        $env:ASPNETCORE_ENVIRONMENT = $Environment
        $env:SEED_DATA = "true"
        
        # For now, we'll use a simple approach - the seeding will happen on application startup
        # In a production scenario, you might want a dedicated seeding command
        Write-Host "ℹ️  Data seeding will occur on application startup for $ServiceName" -ForegroundColor Cyan
        Write-Host "   Set environment variable SEED_DATA=true when starting the service" -ForegroundColor Cyan
        
        Write-Host "✅ Data seeding configured for $ServiceName" -ForegroundColor Green
    }
    finally {
        Set-Location $originalLocation
        Remove-Item Env:SEED_DATA -ErrorAction SilentlyContinue
    }
}

# Function to check service health
function Test-ServiceHealth {
    param(
        [string]$ServiceName,
        [int]$Port
    )
    
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:$Port/health" -TimeoutSec 5 -UseBasicParsing
        return $response.StatusCode -eq 200
    }
    catch {
        return $false
    }
}

# Main execution
Write-Host "🚀 TLI Microservices - Database Migration and Seeding" -ForegroundColor Cyan
Write-Host "Environment: $Environment" -ForegroundColor Cyan
Write-Host "Services: $($Services -join ', ')" -ForegroundColor Cyan
Write-Host ""

# Check prerequisites
Write-Host "🔍 Checking prerequisites..." -ForegroundColor Yellow

# Check if .NET CLI is available
try {
    $dotnetVersion = dotnet --version
    Write-Host "✅ .NET CLI found: $dotnetVersion" -ForegroundColor Green
}
catch {
    Write-Error "❌ .NET CLI not found. Please install .NET 8 SDK."
    exit 1
}

# Check if Entity Framework tools are installed
try {
    dotnet ef --version | Out-Null
    Write-Host "✅ Entity Framework tools found" -ForegroundColor Green
}
catch {
    Write-Host "⚠️  Installing Entity Framework tools..." -ForegroundColor Yellow
    dotnet tool install --global dotnet-ef
    if ($LASTEXITCODE -ne 0) {
        Write-Error "❌ Failed to install Entity Framework tools"
        exit 1
    }
}

# Check PostgreSQL connection
Write-Host "🔍 Checking PostgreSQL connection..." -ForegroundColor Yellow
if (-not (Test-PostgreSQLConnection)) {
    Write-Error "❌ Cannot connect to PostgreSQL. Please ensure PostgreSQL is running and accessible."
    Write-Host "   Connection details: Host=localhost, Port=5432, User=timescale" -ForegroundColor Yellow
    Write-Host "   Run the setup-databases.sql script first if you have not already." -ForegroundColor Yellow
    exit 1
}
Write-Host "✅ PostgreSQL connection successful" -ForegroundColor Green

Write-Host ""

# Process each service
foreach ($serviceName in $Services) {
    if (-not $ServiceConfigs.ContainsKey($serviceName)) {
        Write-Warning "⚠️  Unknown service: $serviceName. Skipping..."
        continue
    }
    
    $config = $ServiceConfigs[$serviceName]
    
    Write-Host "📦 Processing $serviceName..." -ForegroundColor Cyan
    
    # Check if service project exists
    if (-not (Test-Path $config.Path)) {
        Write-Warning "⚠️  Service path not found: $($config.Path). Skipping..."
        continue
    }
    
    try {
        # Run migrations
        Invoke-EFMigration -ServiceName $serviceName -Config $config
        
        # Seed data if requested
        if ($SeedData) {
            Invoke-DataSeeding -ServiceName $serviceName -Config $config
        }
        
        Write-Host "✅ $serviceName completed successfully" -ForegroundColor Green
    }
    catch {
        Write-Error "❌ Failed to process $serviceName`: $_"
        if (-not $Force) {
            Write-Host "Use -Force parameter to continue on errors" -ForegroundColor Yellow
            exit 1
        }
    }
    
    Write-Host ""
}

Write-Host "🎉 Migration and seeding process completed!" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Start the services using docker-compose up or individual dotnet run commands" -ForegroundColor White
Write-Host "2. Verify services are healthy by checking their /health endpoints" -ForegroundColor White
Write-Host "3. Test the API Gateway at http://localhost:5000" -ForegroundColor White
Write-Host ""
Write-Host "Service endpoints:" -ForegroundColor Cyan
Write-Host "- API Gateway: http://localhost:5000" -ForegroundColor White
Write-Host "- Data Storage: http://localhost:5010" -ForegroundColor White
Write-Host "- Monitoring: http://localhost:5011" -ForegroundColor White
