using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Exceptions;

namespace OrderManagement.Application.Commands.PublishRfq;

public class PublishRfqCommandHandler : IRequestHandler<PublishRfqCommand, bool>
{
    private readonly IRfqRepository _rfqRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<PublishRfqCommandHandler> _logger;

    public PublishRfqCommandHandler(
        IRfqRepository rfqRepository,
        IUnitOfWork unitOfWork,
        IMessageBroker messageBroker,
        ILogger<PublishRfqCommandHandler> logger)
    {
        _rfqRepository = rfqRepository;
        _unitOfWork = unitOfWork;
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task<bool> Handle(PublishRfqCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Publishing RFQ {RfqId} for transport company {TransportCompanyId}",
            request.RfqId, request.TransportCompanyId);

        var rfq = await _rfqRepository.GetByIdAsync(request.RfqId, cancellationToken);
        if (rfq == null)
        {
            throw new OrderManagementException($"RFQ with ID {request.RfqId} not found");
        }

        if (rfq.TransportCompanyId != request.TransportCompanyId)
        {
            throw new OrderManagementException("You can only publish your own RFQs");
        }

        // Publish the RFQ
        rfq.Publish();

        // Update in repository
        _rfqRepository.Update(rfq);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        // Publish integration event for broker notifications
        await _messageBroker.PublishAsync("rfq.published", new
        {
            RfqId = rfq.Id,
            RfqNumber = rfq.RfqNumber,
            TransportCompanyId = rfq.TransportCompanyId,
            Title = rfq.Title,
            LoadType = rfq.LoadDetails.LoadType.ToString(),
            RequiredVehicleType = rfq.Requirements.RequiredVehicleType.ToString(),
            PickupCity = rfq.RouteDetails.PickupAddress.City,
            PickupState = rfq.RouteDetails.PickupAddress.State,
            DeliveryCity = rfq.RouteDetails.DeliveryAddress.City,
            DeliveryState = rfq.RouteDetails.DeliveryAddress.State,
            PreferredPickupDate = rfq.RouteDetails.PreferredPickupDate,
            PreferredDeliveryDate = rfq.RouteDetails.PreferredDeliveryDate,
            EstimatedWeight = rfq.LoadDetails.Weight.ToKilograms(),
            IsUrgent = rfq.IsUrgent,
            ExpiresAt = rfq.ExpiresAt,
            BudgetRange = rfq.BudgetRange?.Amount,
            Currency = rfq.BudgetRange?.Currency,
            PublishedAt = DateTime.UtcNow
        });

        _logger.LogInformation("Successfully published RFQ {RfqId} with number {RfqNumber}",
            rfq.Id, rfq.RfqNumber);

        return true;
    }
}
