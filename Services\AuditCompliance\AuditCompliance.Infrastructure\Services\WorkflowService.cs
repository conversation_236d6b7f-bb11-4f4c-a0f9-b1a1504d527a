using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using AuditCompliance.Application.Interfaces;
using AuditCompliance.Application.DTOs.Workflow;
using AuditCompliance.Domain.Entities;
using AuditCompliance.Infrastructure.Persistence;
using System.Text.Json;

namespace AuditCompliance.Infrastructure.Services;

/// <summary>
/// Service for compliance workflow automation and management
/// </summary>
public class WorkflowService : IWorkflowService
{
    private readonly AuditComplianceDbContext _context;
    private readonly IWorkflowEngine _workflowEngine;
    private readonly IWorkflowNotificationService _notificationService;
    private readonly ITenantContext _tenantContext;
    private readonly ILogger<WorkflowService> _logger;

    public WorkflowService(
        AuditComplianceDbContext context,
        IWorkflowEngine workflowEngine,
        IWorkflowNotificationService notificationService,
        ITenantContext tenantContext,
        ILogger<WorkflowService> logger)
    {
        _context = context;
        _workflowEngine = workflowEngine;
        _notificationService = notificationService;
        _tenantContext = tenantContext;
        _logger = logger;
    }

    public async Task<Guid> CreateWorkflowDefinitionAsync(
        CreateWorkflowDefinitionDto definitionDto,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating workflow definition: {Name}", definitionDto.Name);

            // Validate workflow definition
            var tempDefinition = MapToDefinitionDto(definitionDto);
            var validationResult = await ValidateWorkflowDefinitionAsync(tempDefinition, cancellationToken);
            if (!validationResult.IsValid)
            {
                throw new InvalidOperationException($"Workflow validation failed: {string.Join(", ", validationResult.Errors)}");
            }

            var definition = new WorkflowDefinition(
                definitionDto.Name,
                definitionDto.Description,
                definitionDto.Category,
                definitionDto.Version,
                JsonSerializer.Serialize(definitionDto.Steps),
                JsonSerializer.Serialize(definitionDto.Transitions),
                definitionDto.Configuration,
                "System", // Created by
                _tenantContext.TenantId);

            _context.WorkflowDefinitions.Add(definition);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Created workflow definition {DefinitionId}", definition.Id);
            return definition.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating workflow definition");
            throw;
        }
    }

    public async Task<bool> UpdateWorkflowDefinitionAsync(
        Guid definitionId,
        UpdateWorkflowDefinitionDto definitionDto,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var definition = await _context.WorkflowDefinitions
                .FirstOrDefaultAsync(d => d.Id == definitionId, cancellationToken);

            if (definition == null)
            {
                _logger.LogWarning("Workflow definition not found: {DefinitionId}", definitionId);
                return false;
            }

            // Validate updated definition
            var tempDefinition = MapToDefinitionDto(definitionDto, definitionId);
            var validationResult = await ValidateWorkflowDefinitionAsync(tempDefinition, cancellationToken);
            if (!validationResult.IsValid)
            {
                throw new InvalidOperationException($"Workflow validation failed: {string.Join(", ", validationResult.Errors)}");
            }

            definition.UpdateDetails(
                definitionDto.Name,
                definitionDto.Description,
                definitionDto.Category,
                JsonSerializer.Serialize(definitionDto.Steps),
                JsonSerializer.Serialize(definitionDto.Transitions),
                definitionDto.Configuration,
                "System"); // Updated by

            if (definitionDto.IsActive && !definition.IsActive)
            {
                definition.Activate();
            }
            else if (!definitionDto.IsActive && definition.IsActive)
            {
                definition.Deactivate();
            }

            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Updated workflow definition {DefinitionId}", definitionId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating workflow definition {DefinitionId}", definitionId);
            throw;
        }
    }

    public async Task<WorkflowDefinitionDto?> GetWorkflowDefinitionAsync(
        Guid definitionId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var definition = await _context.WorkflowDefinitions
                .FirstOrDefaultAsync(d => d.Id == definitionId, cancellationToken);

            return definition != null ? MapToDefinitionDto(definition) : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting workflow definition {DefinitionId}", definitionId);
            throw;
        }
    }

    public async Task<List<WorkflowDefinitionDto>> GetWorkflowDefinitionsAsync(
        string? category = null,
        bool activeOnly = true,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = _context.WorkflowDefinitions.AsQueryable();

            if (!string.IsNullOrEmpty(category))
            {
                query = query.Where(d => d.Category == category);
            }

            if (activeOnly)
            {
                query = query.Where(d => d.IsActive);
            }

            if (_tenantContext.HasTenant)
            {
                query = query.Where(d => d.TenantId == _tenantContext.TenantId);
            }

            var definitions = await query
                .OrderBy(d => d.Category)
                .ThenBy(d => d.Name)
                .ToListAsync(cancellationToken);

            return definitions.Select(MapToDefinitionDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting workflow definitions");
            throw;
        }
    }

    public async Task<Guid> StartWorkflowAsync(
        StartWorkflowDto startDto,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting workflow for definition {DefinitionId}", startDto.DefinitionId);

            var definition = await _context.WorkflowDefinitions
                .FirstOrDefaultAsync(d => d.Id == startDto.DefinitionId && d.IsActive, cancellationToken);

            if (definition == null)
            {
                throw new InvalidOperationException($"Workflow definition {startDto.DefinitionId} not found or inactive");
            }

            var instance = new WorkflowInstance(
                startDto.DefinitionId,
                definition.Name,
                startDto.InitiatedBy,
                startDto.InitialContext,
                startDto.EntityType,
                startDto.EntityId,
                startDto.EntityName,
                startDto.Priority,
                _tenantContext.TenantId);

            _context.WorkflowInstances.Add(instance);
            await _context.SaveChangesAsync(cancellationToken);

            // Start workflow execution
            await ExecuteWorkflowAsync(instance.Id, cancellationToken);

            _logger.LogInformation("Started workflow instance {InstanceId}", instance.Id);
            return instance.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting workflow");
            throw;
        }
    }

    public async Task<WorkflowInstanceDto?> GetWorkflowInstanceAsync(
        Guid instanceId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var instance = await _context.WorkflowInstances
                .Include(i => i.Tasks)
                .FirstOrDefaultAsync(i => i.Id == instanceId, cancellationToken);

            return instance != null ? MapToInstanceDto(instance) : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting workflow instance {InstanceId}", instanceId);
            throw;
        }
    }

    public async Task<List<WorkflowInstanceDto>> GetWorkflowInstancesAsync(
        WorkflowInstanceFilterDto filter,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = _context.WorkflowInstances
                .Include(i => i.Tasks)
                .AsQueryable();

            if (filter.DefinitionId.HasValue)
            {
                query = query.Where(i => i.DefinitionId == filter.DefinitionId);
            }

            if (!string.IsNullOrEmpty(filter.Status))
            {
                query = query.Where(i => i.Status == filter.Status);
            }

            if (!string.IsNullOrEmpty(filter.InitiatedBy))
            {
                query = query.Where(i => i.InitiatedBy == filter.InitiatedBy);
            }

            if (filter.StartedAfter.HasValue)
            {
                query = query.Where(i => i.StartedAt >= filter.StartedAfter);
            }

            if (filter.StartedBefore.HasValue)
            {
                query = query.Where(i => i.StartedAt <= filter.StartedBefore);
            }

            if (!string.IsNullOrEmpty(filter.EntityType))
            {
                query = query.Where(i => i.EntityType == filter.EntityType);
            }

            if (filter.EntityId.HasValue)
            {
                query = query.Where(i => i.EntityId == filter.EntityId);
            }

            if (filter.Priority.HasValue)
            {
                query = query.Where(i => i.Priority == filter.Priority);
            }

            if (_tenantContext.HasTenant)
            {
                query = query.Where(i => i.TenantId == _tenantContext.TenantId);
            }

            var instances = await query
                .OrderByDescending(i => i.StartedAt)
                .Skip((filter.PageNumber - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync(cancellationToken);

            return instances.Select(MapToInstanceDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting workflow instances");
            throw;
        }
    }

    public async Task<bool> CompleteTaskAsync(
        Guid taskId,
        CompleteTaskDto completeDto,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Completing task {TaskId}", taskId);

            var task = await _context.WorkflowTasks
                .Include(t => t.Instance)
                .FirstOrDefaultAsync(t => t.Id == taskId, cancellationToken);

            if (task == null)
            {
                _logger.LogWarning("Task not found: {TaskId}", taskId);
                return false;
            }

            if (task.Status == "Completed")
            {
                _logger.LogWarning("Task already completed: {TaskId}", taskId);
                return false;
            }

            task.Complete(completeDto.CompletedBy, completeDto.Result, completeDto.Comments);
            await _context.SaveChangesAsync(cancellationToken);

            // Continue workflow execution
            await ExecuteWorkflowAsync(task.InstanceId, cancellationToken);

            _logger.LogInformation("Completed task {TaskId}", taskId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error completing task {TaskId}", taskId);
            throw;
        }
    }

    public async Task<bool> AssignTaskAsync(
        Guid taskId,
        string assigneeId,
        string? assignedBy = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var task = await _context.WorkflowTasks
                .FirstOrDefaultAsync(t => t.Id == taskId, cancellationToken);

            if (task == null)
            {
                _logger.LogWarning("Task not found: {TaskId}", taskId);
                return false;
            }

            task.Assign(assigneeId, assignedBy ?? "System");
            await _context.SaveChangesAsync(cancellationToken);

            // Send assignment notification
            var taskDto = MapToTaskDto(task);
            await _notificationService.SendTaskAssignmentNotificationAsync(taskDto, assigneeId, cancellationToken);

            _logger.LogInformation("Assigned task {TaskId} to {AssigneeId}", taskId, assigneeId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning task {TaskId}", taskId);
            throw;
        }
    }

    public async Task<List<WorkflowTaskDto>> GetUserTasksAsync(
        string userId,
        TaskFilterDto? filter = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = _context.WorkflowTasks
                .Where(t => t.AssignedTo == userId)
                .AsQueryable();

            if (filter != null)
            {
                if (!string.IsNullOrEmpty(filter.Status))
                {
                    query = query.Where(t => t.Status == filter.Status);
                }

                if (!string.IsNullOrEmpty(filter.Type))
                {
                    query = query.Where(t => t.Type == filter.Type);
                }

                if (filter.DueBefore.HasValue)
                {
                    query = query.Where(t => t.DueDate <= filter.DueBefore);
                }

                if (filter.DueAfter.HasValue)
                {
                    query = query.Where(t => t.DueDate >= filter.DueAfter);
                }

                if (filter.Priority.HasValue)
                {
                    query = query.Where(t => t.Priority == filter.Priority);
                }

                if (filter.OverdueOnly)
                {
                    query = query.Where(t => t.DueDate < DateTime.UtcNow && t.Status != "Completed");
                }
            }

            var tasks = await query
                .OrderByDescending(t => t.Priority)
                .ThenBy(t => t.DueDate)
                .Skip(((filter?.PageNumber ?? 1) - 1) * (filter?.PageSize ?? 50))
                .Take(filter?.PageSize ?? 50)
                .ToListAsync(cancellationToken);

            return tasks.Select(MapToTaskDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user tasks for {UserId}", userId);
            throw;
        }
    }

    public async Task<WorkflowTaskDto?> GetTaskAsync(
        Guid taskId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var task = await _context.WorkflowTasks
                .FirstOrDefaultAsync(t => t.Id == taskId, cancellationToken);

            return task != null ? MapToTaskDto(task) : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting task {TaskId}", taskId);
            throw;
        }
    }

    // Additional methods will be implemented in continuation due to length constraints
    public async Task<bool> CancelWorkflowAsync(
        Guid instanceId,
        string reason,
        string cancelledBy,
        CancellationToken cancellationToken = default)
    {
        // Implementation placeholder
        return true;
    }

    public async Task<bool> SuspendWorkflowAsync(
        Guid instanceId,
        string reason,
        string suspendedBy,
        CancellationToken cancellationToken = default)
    {
        // Implementation placeholder
        return true;
    }

    public async Task<bool> ResumeWorkflowAsync(
        Guid instanceId,
        string resumedBy,
        CancellationToken cancellationToken = default)
    {
        // Implementation placeholder
        return true;
    }

    public async Task<List<WorkflowHistoryDto>> GetWorkflowHistoryAsync(
        Guid instanceId,
        CancellationToken cancellationToken = default)
    {
        // Implementation placeholder
        return new List<WorkflowHistoryDto>();
    }

    public async Task<WorkflowAnalyticsDto> GetWorkflowAnalyticsAsync(
        WorkflowAnalyticsRequestDto request,
        CancellationToken cancellationToken = default)
    {
        // Implementation placeholder
        return new WorkflowAnalyticsDto();
    }

    public async Task<WorkflowValidationResult> ValidateWorkflowDefinitionAsync(
        WorkflowDefinitionDto definition,
        CancellationToken cancellationToken = default)
    {
        var result = new WorkflowValidationResult { IsValid = true };

        // Basic validation
        if (string.IsNullOrEmpty(definition.Name))
        {
            result.Errors.Add("Workflow name is required");
            result.IsValid = false;
        }

        if (!definition.Steps.Any())
        {
            result.Errors.Add("Workflow must have at least one step");
            result.IsValid = false;
        }

        var startSteps = definition.Steps.Where(s => s.IsStartStep).ToList();
        if (startSteps.Count != 1)
        {
            result.Errors.Add("Workflow must have exactly one start step");
            result.IsValid = false;
        }

        var endSteps = definition.Steps.Where(s => s.IsEndStep).ToList();
        if (!endSteps.Any())
        {
            result.Warnings.Add("Workflow should have at least one end step");
        }

        return result;
    }

    public async Task ProcessWorkflowTimersAsync(CancellationToken cancellationToken = default)
    {
        // Implementation placeholder
        _logger.LogInformation("Processing workflow timers");
    }

    public async Task<List<WorkflowTemplateDto>> GetWorkflowTemplatesAsync(
        string? category = null,
        CancellationToken cancellationToken = default)
    {
        // Implementation placeholder
        return new List<WorkflowTemplateDto>();
    }

    public async Task<Guid> CreateWorkflowFromTemplateAsync(
        Guid templateId,
        CreateFromTemplateDto createDto,
        CancellationToken cancellationToken = default)
    {
        // Implementation placeholder
        return Guid.NewGuid();
    }

    // Private helper methods
    private async Task ExecuteWorkflowAsync(Guid instanceId, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Executing workflow {InstanceId}", instanceId);

            var instance = await GetWorkflowInstanceAsync(instanceId, cancellationToken);
            if (instance == null || instance.Status != "Running")
            {
                return;
            }

            var definition = await GetWorkflowDefinitionAsync(instance.DefinitionId, cancellationToken);
            if (definition == null)
            {
                return;
            }

            // Get current step or start step
            var currentStep = instance.CurrentStepId.HasValue
                ? definition.Steps.FirstOrDefault(s => s.Id == instance.CurrentStepId)
                : definition.Steps.FirstOrDefault(s => s.IsStartStep);

            if (currentStep == null)
            {
                return;
            }

            // Execute current step
            var executionResult = await _workflowEngine.ExecuteStepAsync(instance, currentStep, instance.Context, cancellationToken);

            if (executionResult.IsCompleted)
            {
                // Mark workflow as completed
                var workflowInstance = await _context.WorkflowInstances
                    .FirstOrDefaultAsync(i => i.Id == instanceId, cancellationToken);
                
                if (workflowInstance != null)
                {
                    workflowInstance.Complete(executionResult.CompletionReason ?? "Completed");
                    await _context.SaveChangesAsync(cancellationToken);

                    // Send completion notification
                    await _notificationService.SendWorkflowCompletionNotificationAsync(instance, cancellationToken);
                }
            }
            else if (executionResult.NextSteps.Any())
            {
                // Continue to next steps
                foreach (var nextStep in executionResult.NextSteps)
                {
                    await ProcessNextStepAsync(instanceId, nextStep, cancellationToken);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing workflow {InstanceId}", instanceId);
        }
    }

    private async Task ProcessNextStepAsync(Guid instanceId, WorkflowStepDto step, CancellationToken cancellationToken)
    {
        // Implementation for processing next step
        _logger.LogDebug("Processing next step {StepName} for workflow {InstanceId}", step.Name, instanceId);
    }

    // Mapping methods
    private WorkflowDefinitionDto MapToDefinitionDto(dynamic definition)
    {
        return new WorkflowDefinitionDto
        {
            Id = definition.Id,
            Name = definition.Name,
            Description = definition.Description,
            Category = definition.Category,
            Version = definition.Version,
            IsActive = definition.IsActive,
            Steps = JsonSerializer.Deserialize<List<WorkflowStepDto>>(definition.Steps ?? "[]") ?? new List<WorkflowStepDto>(),
            Transitions = JsonSerializer.Deserialize<List<WorkflowTransitionDto>>(definition.Transitions ?? "[]") ?? new List<WorkflowTransitionDto>(),
            Configuration = JsonSerializer.Deserialize<Dictionary<string, object>>(definition.Configuration ?? "{}") ?? new Dictionary<string, object>(),
            CreatedAt = definition.CreatedAt,
            CreatedBy = definition.CreatedBy,
            UpdatedAt = definition.UpdatedAt,
            UpdatedBy = definition.UpdatedBy,
            TenantId = definition.TenantId
        };
    }

    private WorkflowDefinitionDto MapToDefinitionDto(CreateWorkflowDefinitionDto dto)
    {
        return new WorkflowDefinitionDto
        {
            Id = Guid.NewGuid(),
            Name = dto.Name,
            Description = dto.Description,
            Category = dto.Category,
            Version = dto.Version,
            IsActive = true,
            Steps = dto.Steps,
            Transitions = dto.Transitions,
            Configuration = dto.Configuration
        };
    }

    private WorkflowDefinitionDto MapToDefinitionDto(UpdateWorkflowDefinitionDto dto, Guid id)
    {
        return new WorkflowDefinitionDto
        {
            Id = id,
            Name = dto.Name,
            Description = dto.Description,
            Category = dto.Category,
            IsActive = dto.IsActive,
            Steps = dto.Steps,
            Transitions = dto.Transitions,
            Configuration = dto.Configuration
        };
    }

    private WorkflowInstanceDto MapToInstanceDto(dynamic instance)
    {
        return new WorkflowInstanceDto
        {
            Id = instance.Id,
            DefinitionId = instance.DefinitionId,
            DefinitionName = instance.DefinitionName,
            Status = instance.Status,
            CurrentStepId = instance.CurrentStepId,
            CurrentStepName = instance.CurrentStepName,
            InitiatedBy = instance.InitiatedBy,
            StartedAt = instance.StartedAt,
            CompletedAt = instance.CompletedAt,
            CompletionReason = instance.CompletionReason,
            Context = JsonSerializer.Deserialize<Dictionary<string, object>>(instance.Context ?? "{}") ?? new Dictionary<string, object>(),
            EntityType = instance.EntityType,
            EntityId = instance.EntityId,
            EntityName = instance.EntityName,
            Priority = instance.Priority,
            TenantId = instance.TenantId
        };
    }

    private WorkflowTaskDto MapToTaskDto(dynamic task)
    {
        return new WorkflowTaskDto
        {
            Id = task.Id,
            InstanceId = task.InstanceId,
            StepId = task.StepId,
            StepName = task.StepName,
            Name = task.Name,
            Description = task.Description,
            Type = task.Type,
            Status = task.Status,
            AssignedTo = task.AssignedTo,
            AssignedBy = task.AssignedBy,
            AssignedAt = task.AssignedAt,
            CreatedAt = task.CreatedAt,
            DueDate = task.DueDate,
            CompletedAt = task.CompletedAt,
            CompletedBy = task.CompletedBy,
            Data = JsonSerializer.Deserialize<Dictionary<string, object>>(task.Data ?? "{}") ?? new Dictionary<string, object>(),
            Result = JsonSerializer.Deserialize<Dictionary<string, object>>(task.Result ?? "{}") ?? new Dictionary<string, object>(),
            Priority = task.Priority,
            Comments = task.Comments
        };
    }
}
