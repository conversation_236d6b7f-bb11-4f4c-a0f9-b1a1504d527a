using MediatR;
using Microsoft.Extensions.Logging;
using TripManagement.Application.Interfaces;

namespace TripManagement.Application.Commands.AssignResourceToLeg;

public class AssignResourceToLegCommandHandler : IRequestHandler<AssignResourceToLegCommand, bool>
{
    private readonly ITripRepository _tripRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly INetworkFleetService _networkFleetService;
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<AssignResourceToLegCommandHandler> _logger;

    public AssignResourceToLegCommandHandler(
        ITripRepository tripRepository,
        IUnitOfWork unitOfWork,
        INetworkFleetService networkFleetService,
        IMessageBroker messageBroker,
        ILogger<AssignResourceToLegCommandHandler> logger)
    {
        _tripRepository = tripRepository;
        _unitOfWork = unitOfWork;
        _networkFleetService = networkFleetService;
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task<bool> Handle(AssignResourceToLegCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Assigning resources to leg {LegNumber} of trip {TripId} by user {UserId}", 
            request.LegNumber, request.TripId, request.RequestingUserId);

        try
        {
            // Get the trip with legs
            var trip = await _tripRepository.GetByIdWithLegsAsync(request.TripId, cancellationToken);
            if (trip == null)
            {
                _logger.LogWarning("Trip {TripId} not found", request.TripId);
                throw new ArgumentException($"Trip {request.TripId} not found");
            }

            // Validate user has permission to assign resources
            if (trip.CarrierId != request.RequestingUserId)
            {
                _logger.LogWarning("User {UserId} does not have permission to assign resources to trip {TripId}", 
                    request.RequestingUserId, request.TripId);
                throw new UnauthorizedAccessException("You do not have permission to assign resources to this trip");
            }

            // Find the leg
            var leg = trip.Legs.FirstOrDefault(l => l.LegNumber == request.LegNumber);
            if (leg == null)
            {
                _logger.LogWarning("Leg {LegNumber} not found in trip {TripId}", 
                    request.LegNumber, request.TripId);
                throw new ArgumentException($"Leg {request.LegNumber} not found in trip");
            }

            // Validate availability if requested
            if (request.ValidateAvailability)
            {
                await ValidateResourceAvailability(request, leg, cancellationToken);
            }

            // Assign resources to the leg
            trip.AssignResourceToLeg(request.LegNumber, request.DriverId, request.VehicleId);

            // Save changes
            _tripRepository.Update(trip);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Publish integration events
            if (request.NotifyAssignedResources)
            {
                await PublishResourceAssignmentEvents(request, trip, leg, cancellationToken);
            }

            _logger.LogInformation("Successfully assigned resources to leg {LegNumber} of trip {TripId}", 
                request.LegNumber, request.TripId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning resources to leg {LegNumber} of trip {TripId}", 
                request.LegNumber, request.TripId);
            throw;
        }
    }

    private async Task ValidateResourceAvailability(
        AssignResourceToLegCommand request, 
        Domain.Entities.TripLeg leg, 
        CancellationToken cancellationToken)
    {
        if (request.DriverId.HasValue)
        {
            var driverAvailable = await _networkFleetService.IsDriverAvailableAsync(
                request.DriverId.Value,
                leg.EstimatedStartTime,
                leg.EstimatedEndTime,
                cancellationToken);

            if (!driverAvailable)
            {
                throw new InvalidOperationException($"Driver {request.DriverId} is not available for the specified time period");
            }
        }

        if (request.VehicleId.HasValue)
        {
            var vehicleAvailable = await _networkFleetService.IsVehicleAvailableAsync(
                request.VehicleId.Value,
                leg.EstimatedStartTime,
                leg.EstimatedEndTime,
                cancellationToken);

            if (!vehicleAvailable)
            {
                throw new InvalidOperationException($"Vehicle {request.VehicleId} is not available for the specified time period");
            }
        }
    }

    private async Task PublishResourceAssignmentEvents(
        AssignResourceToLegCommand request,
        Domain.Entities.Trip trip,
        Domain.Entities.TripLeg leg,
        CancellationToken cancellationToken)
    {
        try
        {
            // Publish leg resource assigned event
            await _messageBroker.PublishAsync("trip_leg.resource_assigned", new
            {
                TripId = request.TripId,
                TripNumber = trip.TripNumber,
                LegId = leg.Id,
                LegNumber = request.LegNumber,
                LegName = leg.LegName,
                DriverId = request.DriverId,
                VehicleId = request.VehicleId,
                AssignedBy = request.RequestingUserId,
                AssignedAt = DateTime.UtcNow,
                EstimatedStartTime = leg.EstimatedStartTime,
                EstimatedEndTime = leg.EstimatedEndTime,
                AssignmentNotes = request.AssignmentNotes
            }, cancellationToken);

            _logger.LogInformation("Successfully published resource assignment events for leg {LegNumber} of trip {TripId}", 
                request.LegNumber, request.TripId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing resource assignment events for leg {LegNumber} of trip {TripId}", 
                request.LegNumber, request.TripId);
            // Don't throw here as the main operation succeeded
        }
    }
}
