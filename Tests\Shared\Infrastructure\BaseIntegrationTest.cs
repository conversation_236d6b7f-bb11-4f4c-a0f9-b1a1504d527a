using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Testcontainers.PostgreSql;
using Testcontainers.Redis;
using Testcontainers.RabbitMq;
using Xunit;

namespace Tests.Shared.Infrastructure;

/// <summary>
/// Base class for integration tests that provides common infrastructure setup
/// </summary>
public abstract class BaseIntegrationTest<TStartup> : IClassFixture<IntegrationTestWebApplicationFactory<TStartup>>, IAsyncLifetime
    where TStartup : class
{
    protected readonly IntegrationTestWebApplicationFactory<TStartup> Factory;
    protected readonly HttpClient Client;
    protected readonly IServiceScope Scope;
    protected readonly IServiceProvider Services;

    protected BaseIntegrationTest(IntegrationTestWebApplicationFactory<TStartup> factory)
    {
        Factory = factory;
        Client = factory.CreateClient();
        Scope = factory.Services.CreateScope();
        Services = Scope.ServiceProvider;
    }

    public virtual Task InitializeAsync() => Task.CompletedTask;

    public virtual Task DisposeAsync()
    {
        Scope?.Dispose();
        Client?.Dispose();
        return Task.CompletedTask;
    }

    /// <summary>
    /// Get a service from the DI container
    /// </summary>
    protected T GetService<T>() where T : notnull => Services.GetRequiredService<T>();

    /// <summary>
    /// Get a logger for the specified type
    /// </summary>
    protected ILogger<T> GetLogger<T>() => Services.GetRequiredService<ILogger<T>>();

    /// <summary>
    /// Execute a database operation within a transaction scope
    /// </summary>
    protected async Task ExecuteDbContextAsync<TDbContext>(Func<TDbContext, Task> operation)
        where TDbContext : DbContext
    {
        using var scope = Factory.Services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<TDbContext>();
        await operation(context);
    }

    /// <summary>
    /// Execute a database operation and return a result within a transaction scope
    /// </summary>
    protected async Task<T> ExecuteDbContextAsync<TDbContext, T>(Func<TDbContext, Task<T>> operation)
        where TDbContext : DbContext
    {
        using var scope = Factory.Services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<TDbContext>();
        return await operation(context);
    }
}

/// <summary>
/// Custom WebApplicationFactory for integration tests
/// </summary>
public class IntegrationTestWebApplicationFactory<TStartup> : WebApplicationFactory<TStartup>, IAsyncLifetime
    where TStartup : class
{
    private PostgreSqlContainer? _postgresContainer;
    private RedisContainer? _redisContainer;
    private RabbitMqContainer? _rabbitMqContainer;

    public async Task InitializeAsync()
    {
        // Start test containers
        _postgresContainer = new PostgreSqlBuilder()
            .WithDatabase("testdb")
            .WithUsername("testuser")
            .WithPassword("testpass")
            .Build();

        _redisContainer = new RedisBuilder()
            .Build();

        _rabbitMqContainer = new RabbitMqBuilder()
            .WithUsername("testuser")
            .WithPassword("testpass")
            .Build();

        await Task.WhenAll(
            _postgresContainer.StartAsync(),
            _redisContainer.StartAsync(),
            _rabbitMqContainer.StartAsync()
        );
    }

    public new async Task DisposeAsync()
    {
        if (_postgresContainer != null)
            await _postgresContainer.DisposeAsync();
        if (_redisContainer != null)
            await _redisContainer.DisposeAsync();
        if (_rabbitMqContainer != null)
            await _rabbitMqContainer.DisposeAsync();

        await base.DisposeAsync();
    }

    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        builder.ConfigureAppConfiguration((context, config) =>
        {
            // Override configuration for testing
            config.AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["ConnectionStrings:DefaultConnection"] = _postgresContainer?.GetConnectionString(),
                ["ConnectionStrings:Redis"] = _redisContainer?.GetConnectionString(),
                ["ConnectionStrings:RabbitMQ"] = _rabbitMqContainer?.GetConnectionString(),
                ["Environment"] = "Testing"
            });
        });

        builder.ConfigureServices(services =>
        {
            // Replace services for testing
            ConfigureTestServices(services);
        });

        builder.UseEnvironment("Testing");
    }

    protected virtual void ConfigureTestServices(IServiceCollection services)
    {
        // Override in derived classes to configure test-specific services
    }
}
