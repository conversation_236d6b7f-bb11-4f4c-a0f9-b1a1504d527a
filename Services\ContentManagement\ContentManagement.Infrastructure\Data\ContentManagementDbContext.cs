using ContentManagement.Domain.Entities;
using ContentManagement.Infrastructure.Data.Configurations;
using Microsoft.EntityFrameworkCore;
using Shared.Domain.Common;
using Shared.Domain.Primitives;

namespace ContentManagement.Infrastructure.Data;

/// <summary>
/// Entity Framework DbContext for Content Management
/// </summary>
public class ContentManagementDbContext : DbContext
{
    public ContentManagementDbContext(DbContextOptions<ContentManagementDbContext> options)
        : base(options)
    {
    }

    // DbSets
    public DbSet<Content> Contents { get; set; } = null!;
    public DbSet<ContentVersion> ContentVersions { get; set; } = null!;
    public DbSet<ContentAttachment> ContentAttachments { get; set; } = null!;
    public DbSet<FaqItem> FaqItems { get; set; } = null!;
    public DbSet<PolicyDocument> PolicyDocuments { get; set; } = null!;
    public DbSet<PolicyAcceptance> PolicyAcceptances { get; set; } = null!;

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Apply configurations
        modelBuilder.ApplyConfiguration(new ContentConfiguration());
        modelBuilder.ApplyConfiguration(new ContentVersionConfiguration());
        modelBuilder.ApplyConfiguration(new ContentAttachmentConfiguration());
        modelBuilder.ApplyConfiguration(new FaqItemConfiguration());
        modelBuilder.ApplyConfiguration(new PolicyDocumentConfiguration());
        modelBuilder.ApplyConfiguration(new PolicyAcceptanceConfiguration());

        // Configure schema
        modelBuilder.HasDefaultSchema("content_management");

        // Configure value conversions for enums
        ConfigureEnumConversions(modelBuilder);

        // Configure value objects
        ConfigureValueObjects(modelBuilder);
    }

    private static void ConfigureEnumConversions(ModelBuilder modelBuilder)
    {
        // Convert enums to strings for better readability in database
        modelBuilder.Entity<Content>()
            .Property(e => e.Type)
            .HasConversion<string>();

        modelBuilder.Entity<Content>()
            .Property(e => e.Status)
            .HasConversion<string>();

        modelBuilder.Entity<FaqItem>()
            .Property(e => e.Category)
            .HasConversion<string>();

        modelBuilder.Entity<PolicyDocument>()
            .Property(e => e.PolicyType)
            .HasConversion<string>();

        modelBuilder.Entity<ContentAttachment>()
            .Property(e => e.Type)
            .HasConversion<string>();
    }

    private static void ConfigureValueObjects(ModelBuilder modelBuilder)
    {
        // Configure ContentVisibility value object
        modelBuilder.Entity<Content>()
            .OwnsOne(c => c.Visibility, visibility =>
            {
                visibility.Property(v => v.Level)
                    .HasColumnName("VisibilityLevel")
                    .HasConversion<string>();

                visibility.Property(v => v.AllowedRoles)
                    .HasColumnName("AllowedRoles")
                    .HasConversion(
                        v => string.Join(',', v),
                        v => v.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList());

                visibility.Property(v => v.VisibleFrom)
                    .HasColumnName("VisibleFrom");

                visibility.Property(v => v.VisibleUntil)
                    .HasColumnName("VisibleUntil");
            });

        // Configure PublishSchedule value object
        modelBuilder.Entity<Content>()
            .OwnsOne(c => c.PublishSchedule, schedule =>
            {
                schedule.Property(s => s.PublishAt)
                    .HasColumnName("PublishAt");

                schedule.Property(s => s.UnpublishAt)
                    .HasColumnName("UnpublishAt");

                schedule.Property(s => s.IsRecurring)
                    .HasColumnName("IsRecurring");

                schedule.Property(s => s.Pattern)
                    .HasColumnName("RecurrencePattern")
                    .HasConversion<string>();

                schedule.Property(s => s.RecurrenceInterval)
                    .HasColumnName("RecurrenceInterval");

                schedule.Property(s => s.RecurrenceEndDate)
                    .HasColumnName("RecurrenceEndDate");
            });

        // Configure Tags as JSON
        modelBuilder.Entity<Content>()
            .Property(e => e.Tags)
            .HasConversion(
                v => string.Join(',', v),
                v => v.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList());

        modelBuilder.Entity<FaqItem>()
            .Property(e => e.RoleVisibility)
            .HasConversion(
                v => string.Join(',', v),
                v => v.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList());

        modelBuilder.Entity<PolicyDocument>()
            .Property(e => e.ApplicableRoles)
            .HasConversion(
                v => string.Join(',', v),
                v => v.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList());
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        // Update timestamps
        var entries = ChangeTracker
            .Entries()
            .Where(e => e.Entity is BaseEntity && (
                e.State == EntityState.Added ||
                e.State == EntityState.Modified));

        foreach (var entityEntry in entries)
        {
            if (entityEntry.State == EntityState.Added)
            {
                ((BaseEntity)entityEntry.Entity).CreatedAt = DateTime.UtcNow;
            }

            if (entityEntry.State == EntityState.Modified)
            {
                if (entityEntry.Entity is Content content)
                {
                    content.GetType().GetProperty("UpdatedAt")?.SetValue(content, DateTime.UtcNow);
                }
            }
        }

        return await base.SaveChangesAsync(cancellationToken);
    }
}
