using FinancialPayment.Domain.ValueObjects;
using Shared.Domain.Common;

namespace FinancialPayment.Domain.Entities;

public class FraudDetectionRule : AggregateRoot
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public FraudRuleType RuleType { get; private set; }
    public string RuleExpression { get; private set; }
    public int RiskScore { get; private set; } // 1-100
    public FraudAction Action { get; private set; }
    public bool IsActive { get; private set; }
    public int Priority { get; private set; }
    public DateTime EffectiveFrom { get; private set; }
    public DateTime? EffectiveTo { get; private set; }

    // Navigation properties
    private readonly List<FraudRuleCondition> _conditions = new();
    public IReadOnlyList<FraudRuleCondition> Conditions => _conditions.AsReadOnly();

    private FraudDetectionRule() { }

    public FraudDetectionRule(
        string name,
        string description,
        FraudRuleType ruleType,
        string ruleExpression,
        int riskScore,
        FraudAction action,
        int priority = 0)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Rule name cannot be empty", nameof(name));

        if (riskScore < 1 || riskScore > 100)
            throw new ArgumentException("Risk score must be between 1 and 100", nameof(riskScore));

        Name = name;
        Description = description;
        RuleType = ruleType;
        RuleExpression = ruleExpression;
        RiskScore = riskScore;
        Action = action;
        Priority = priority;
        IsActive = true;
        EffectiveFrom = DateTime.UtcNow;
    }

    public void AddCondition(string field, string @operator, string value, string? description = null)
    {
        var condition = new FraudRuleCondition(Id, field, @operator, value, description);
        _conditions.Add(condition);
    }

    public void UpdateRiskScore(int newRiskScore)
    {
        if (newRiskScore < 1 || newRiskScore > 100)
            throw new ArgumentException("Risk score must be between 1 and 100", nameof(newRiskScore));

        RiskScore = newRiskScore;
        SetUpdatedAt();
    }

    public void Activate() => IsActive = true;
    public void Deactivate() => IsActive = false;

    public bool EvaluateConditions(Dictionary<string, object> context)
    {
        if (!_conditions.Any()) return true;
        return _conditions.All(condition => condition.Evaluate(context));
    }
}

public class FraudRuleCondition : BaseEntity
{
    public Guid FraudRuleId { get; private set; }
    public string Field { get; private set; }
    public string Operator { get; private set; }
    public string Value { get; private set; }
    public string? Description { get; private set; }

    private FraudRuleCondition() { }

    public FraudRuleCondition(Guid fraudRuleId, string field, string @operator, string value, string? description = null)
    {
        FraudRuleId = fraudRuleId;
        Field = field;
        Operator = @operator;
        Value = value;
        Description = description;
    }

    public bool Evaluate(Dictionary<string, object> context)
    {
        if (!context.TryGetValue(Field, out var contextValue))
            return false;

        return Operator.ToLowerInvariant() switch
        {
            "equals" => contextValue?.ToString() == Value,
            "not_equals" => contextValue?.ToString() != Value,
            "greater_than" => decimal.TryParse(contextValue?.ToString(), out var gtVal) &&
                             decimal.TryParse(Value, out var gtTarget) && gtVal > gtTarget,
            "less_than" => decimal.TryParse(contextValue?.ToString(), out var ltVal) &&
                          decimal.TryParse(Value, out var ltTarget) && ltVal < ltTarget,
            "contains" => contextValue?.ToString()?.Contains(Value, StringComparison.OrdinalIgnoreCase) == true,
            "in_list" => Value.Split(',').Contains(contextValue?.ToString()),
            _ => false
        };
    }
}

public class FraudAssessment : AggregateRoot
{
    public Guid TransactionId { get; private set; }
    public Guid? UserId { get; private set; }
    public Money TransactionAmount { get; private set; }
    public string PaymentMethod { get; private set; }
    public string IpAddress { get; private set; }
    public string? DeviceFingerprint { get; private set; }
    public string? UserAgent { get; private set; }
    public string? BillingAddress { get; private set; }
    public string? ShippingAddress { get; private set; }
    public int TotalRiskScore { get; private set; }
    public FraudRiskLevel RiskLevel { get; private set; }
    public FraudAssessmentStatus Status { get; private set; }
    public FraudAction RecommendedAction { get; private set; }
    public DateTime AssessedAt { get; private set; }
    public string? Notes { get; private set; }

    // Navigation properties
    private readonly List<FraudAssessmentResult> _ruleResults = new();
    public IReadOnlyList<FraudAssessmentResult> RuleResults => _ruleResults.AsReadOnly();

    private readonly List<FraudAlert> _alerts = new();
    public IReadOnlyList<FraudAlert> Alerts => _alerts.AsReadOnly();

    private FraudAssessment() { }

    public FraudAssessment(
        Guid transactionId,
        Guid? userId,
        Money transactionAmount,
        string paymentMethod,
        string ipAddress,
        string? deviceFingerprint = null,
        string? userAgent = null,
        string? billingAddress = null,
        string? shippingAddress = null)
    {
        TransactionId = transactionId;
        UserId = userId;
        TransactionAmount = transactionAmount;
        PaymentMethod = paymentMethod;
        IpAddress = ipAddress;
        DeviceFingerprint = deviceFingerprint;
        UserAgent = userAgent;
        BillingAddress = billingAddress;
        ShippingAddress = shippingAddress;
        TotalRiskScore = 0;
        RiskLevel = FraudRiskLevel.Low;
        Status = FraudAssessmentStatus.Pending;
        RecommendedAction = FraudAction.Allow;
        AssessedAt = DateTime.UtcNow;
    }

    public void AddRuleResult(FraudDetectionRule rule, bool isTriggered, string? details = null)
    {
        var result = new FraudAssessmentResult(
            Id,
            rule.Id,
            rule.Name,
            rule.RiskScore,
            isTriggered,
            details);

        _ruleResults.Add(result);
        RecalculateRiskScore();
    }

    public void AddAlert(FraudAlertType alertType, string message, FraudAlertSeverity severity)
    {
        var alert = new FraudAlert(Id, alertType, message, severity);
        _alerts.Add(alert);
    }

    public void CompleteAssessment(string? notes = null)
    {
        Status = FraudAssessmentStatus.Completed;
        Notes = notes;
        SetUpdatedAt();
    }

    public void MarkAsReviewed(Guid reviewedBy, string reviewNotes)
    {
        Status = FraudAssessmentStatus.Reviewed;
        Notes = reviewNotes;
        SetUpdatedAt();
    }

    private void RecalculateRiskScore()
    {
        TotalRiskScore = _ruleResults.Where(r => r.IsTriggered).Sum(r => r.RiskScore);

        RiskLevel = TotalRiskScore switch
        {
            <= 30 => FraudRiskLevel.Low,
            <= 60 => FraudRiskLevel.Medium,
            <= 80 => FraudRiskLevel.High,
            _ => FraudRiskLevel.Critical
        };

        RecommendedAction = RiskLevel switch
        {
            FraudRiskLevel.Low => FraudAction.Allow,
            FraudRiskLevel.Medium => FraudAction.Review,
            FraudRiskLevel.High => FraudAction.Challenge,
            FraudRiskLevel.Critical => FraudAction.Block,
            _ => FraudAction.Allow
        };

        SetUpdatedAt();
    }

    public FraudAssessmentSummary GetSummary()
    {
        return new FraudAssessmentSummary
        {
            TransactionId = TransactionId,
            UserId = UserId,
            TransactionAmount = TransactionAmount,
            TotalRiskScore = TotalRiskScore,
            RiskLevel = RiskLevel,
            RecommendedAction = RecommendedAction,
            TriggeredRulesCount = _ruleResults.Count(r => r.IsTriggered),
            AlertsCount = _alerts.Count,
            AssessedAt = AssessedAt,
            Status = Status
        };
    }
}

public class FraudAssessmentResult : BaseEntity
{
    public Guid FraudAssessmentId { get; private set; }
    public Guid RuleId { get; private set; }
    public string RuleName { get; private set; }
    public int RiskScore { get; private set; }
    public bool IsTriggered { get; private set; }
    public string? Details { get; private set; }

    private FraudAssessmentResult() { }

    public FraudAssessmentResult(
        Guid fraudAssessmentId,
        Guid ruleId,
        string ruleName,
        int riskScore,
        bool isTriggered,
        string? details = null)
    {
        FraudAssessmentId = fraudAssessmentId;
        RuleId = ruleId;
        RuleName = ruleName;
        RiskScore = riskScore;
        IsTriggered = isTriggered;
        Details = details;
    }
}

public class FraudAlert : BaseEntity
{
    public Guid FraudAssessmentId { get; private set; }
    public FraudAlertType AlertType { get; private set; }
    public string Message { get; private set; }
    public FraudAlertSeverity Severity { get; private set; }
    public DateTime CreatedAt { get; private set; }

    private FraudAlert() { }

    public FraudAlert(Guid fraudAssessmentId, FraudAlertType alertType, string message, FraudAlertSeverity severity)
    {
        FraudAssessmentId = fraudAssessmentId;
        AlertType = alertType;
        Message = message;
        Severity = severity;
        CreatedAt = DateTime.UtcNow;
    }
}

// Enums
public enum FraudRuleType
{
    VelocityCheck = 1,
    AmountThreshold = 2,
    GeolocationCheck = 3,
    DeviceFingerprint = 4,
    BehaviorPattern = 5,
    BlacklistCheck = 6,
    WhitelistCheck = 7,
    CustomRule = 8
}

public enum FraudAction
{
    Allow = 1,
    Review = 2,
    Challenge = 3,
    Block = 4
}

public enum FraudRiskLevel
{
    Low = 1,
    Medium = 2,
    High = 3,
    Critical = 4
}

public enum FraudAssessmentStatus
{
    Pending = 1,
    Completed = 2,
    Reviewed = 3,
    Escalated = 4
}

public enum FraudAlertType
{
    HighRiskTransaction = 1,
    VelocityExceeded = 2,
    SuspiciousLocation = 3,
    BlacklistedUser = 4,
    UnusualPattern = 5,
    DeviceMismatch = 6
}

public enum FraudAlertSeverity
{
    Low = 1,
    Medium = 2,
    High = 3,
    Critical = 4
}

public class FraudAssessmentSummary
{
    public Guid TransactionId { get; set; }
    public Guid? UserId { get; set; }
    public Money TransactionAmount { get; set; } = Money.Zero("INR");
    public int TotalRiskScore { get; set; }
    public FraudRiskLevel RiskLevel { get; set; }
    public FraudAction RecommendedAction { get; set; }
    public int TriggeredRulesCount { get; set; }
    public int AlertsCount { get; set; }
    public DateTime AssessedAt { get; set; }
    public FraudAssessmentStatus Status { get; set; }
}


