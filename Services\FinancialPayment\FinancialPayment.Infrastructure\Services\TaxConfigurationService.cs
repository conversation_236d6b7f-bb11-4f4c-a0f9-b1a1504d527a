using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Application.Interfaces.Repositories;
using SharedKernel.Application.Interfaces;
using Microsoft.Extensions.Logging;

namespace FinancialPayment.Infrastructure.Services;

public class TaxConfigurationService : ITaxConfigurationService
{
    private readonly ITaxConfigurationRepository _taxConfigurationRepository;
    private readonly IGstConfigurationRepository _gstConfigurationRepository;
    private readonly ITdsConfigurationRepository _tdsConfigurationRepository;
    private readonly IHsnCodeRepository _hsnCodeRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<TaxConfigurationService> _logger;

    public TaxConfigurationService(
        ITaxConfigurationRepository taxConfigurationRepository,
        IGstConfigurationRepository gstConfigurationRepository,
        ITdsConfigurationRepository tdsConfigurationRepository,
        IHsnCodeRepository hsnCodeRepository,
        IUnitOfWork unitOfWork,
        ILogger<TaxConfigurationService> logger)
    {
        _taxConfigurationRepository = taxConfigurationRepository;
        _gstConfigurationRepository = gstConfigurationRepository;
        _tdsConfigurationRepository = tdsConfigurationRepository;
        _hsnCodeRepository = hsnCodeRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    #region GST Configuration Management

    public async Task<GstConfiguration> CreateGstConfigurationAsync(
        string name,
        string description,
        ServiceCategory serviceCategory,
        TaxJurisdiction jurisdiction,
        GstRate gstRate,
        TaxRate taxRate,
        Money minimumAmount,
        Money maximumAmount,
        string createdBy,
        string? hsnCode = null,
        bool isReverseChargeApplicable = false,
        string? reverseChargeConditions = null)
    {
        _logger.LogInformation("Creating GST configuration: {Name} for {ServiceCategory}", name, serviceCategory);

        var gstConfiguration = new GstConfiguration(
            name,
            description,
            serviceCategory,
            jurisdiction,
            gstRate,
            taxRate,
            minimumAmount,
            maximumAmount,
            createdBy,
            hsnCode,
            isReverseChargeApplicable,
            reverseChargeConditions);

        await _gstConfigurationRepository.AddAsync(gstConfiguration);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("GST configuration created successfully: {Id}", gstConfiguration.Id);
        return gstConfiguration;
    }

    public async Task<GstConfiguration> UpdateGstConfigurationAsync(
        Guid id,
        string name,
        string description,
        ServiceCategory serviceCategory,
        TaxJurisdiction jurisdiction,
        GstRate gstRate,
        TaxRate taxRate,
        Money minimumAmount,
        Money maximumAmount,
        string modifiedBy,
        string? hsnCode = null,
        bool isReverseChargeApplicable = false,
        string? reverseChargeConditions = null)
    {
        _logger.LogInformation("Updating GST configuration: {Id}", id);

        var gstConfiguration = await _gstConfigurationRepository.GetByIdAsync(id);
        if (gstConfiguration == null)
            throw new InvalidOperationException($"GST configuration with ID {id} not found");

        gstConfiguration.UpdateConfiguration(
            name,
            description,
            serviceCategory,
            jurisdiction,
            gstRate,
            taxRate,
            minimumAmount,
            maximumAmount,
            modifiedBy,
            hsnCode,
            isReverseChargeApplicable,
            reverseChargeConditions);

        await _gstConfigurationRepository.UpdateAsync(gstConfiguration);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("GST configuration updated successfully: {Id}", id);
        return gstConfiguration;
    }

    public async Task<GstConfiguration?> GetGstConfigurationAsync(Guid id)
    {
        return await _gstConfigurationRepository.GetByIdAsync(id);
    }

    public async Task<List<GstConfiguration>> GetActiveGstConfigurationsAsync()
    {
        return await _gstConfigurationRepository.GetActiveAsync();
    }

    public async Task<GstConfiguration?> GetApplicableGstConfigurationAsync(ServiceCategory serviceCategory, TaxJurisdiction jurisdiction, DateTime date)
    {
        return await _gstConfigurationRepository.GetApplicableConfigurationAsync(serviceCategory, jurisdiction, date);
    }

    public async Task<Money> CalculateGstAsync(Money baseAmount, ServiceCategory serviceCategory, TaxJurisdiction jurisdiction)
    {
        var configuration = await GetApplicableGstConfigurationAsync(serviceCategory, jurisdiction, DateTime.UtcNow);
        if (configuration == null)
        {
            _logger.LogWarning("No applicable GST configuration found for {ServiceCategory} in {Jurisdiction}", serviceCategory, jurisdiction.GetFullJurisdiction());
            return Money.Zero(baseAmount.Currency);
        }

        return configuration.CalculateGst(baseAmount);
    }

    public async Task ActivateGstConfigurationAsync(Guid id, string activatedBy)
    {
        var configuration = await _gstConfigurationRepository.GetByIdAsync(id);
        if (configuration == null)
            throw new InvalidOperationException($"GST configuration with ID {id} not found");

        configuration.Activate(activatedBy);
        await _gstConfigurationRepository.UpdateAsync(configuration);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("GST configuration activated: {Id} by {User}", id, activatedBy);
    }

    public async Task DeactivateGstConfigurationAsync(Guid id, string deactivatedBy, string reason)
    {
        var configuration = await _gstConfigurationRepository.GetByIdAsync(id);
        if (configuration == null)
            throw new InvalidOperationException($"GST configuration with ID {id} not found");

        configuration.Deactivate(deactivatedBy, reason);
        await _gstConfigurationRepository.UpdateAsync(configuration);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("GST configuration deactivated: {Id} by {User}, reason: {Reason}", id, deactivatedBy, reason);
    }

    public async Task ArchiveGstConfigurationAsync(Guid id, string archivedBy, string reason)
    {
        var configuration = await _gstConfigurationRepository.GetByIdAsync(id);
        if (configuration == null)
            throw new InvalidOperationException($"GST configuration with ID {id} not found");

        configuration.Archive(archivedBy, reason);
        await _gstConfigurationRepository.UpdateAsync(configuration);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("GST configuration archived: {Id} by {User}, reason: {Reason}", id, archivedBy, reason);
    }

    #endregion

    #region TDS Configuration Management

    public async Task<TdsConfiguration> CreateTdsConfigurationAsync(
        string name,
        string description,
        TdsSection section,
        EntityType entityType,
        TaxRate taxRate,
        TdsThreshold threshold,
        string createdBy,
        bool requiresPan = true,
        decimal higherRateWithoutPan = 0,
        string? specialConditions = null)
    {
        _logger.LogInformation("Creating TDS configuration: {Name} for {Section}-{EntityType}", name, section, entityType);

        var tdsConfiguration = new TdsConfiguration(
            name,
            description,
            section,
            entityType,
            taxRate,
            threshold,
            createdBy,
            requiresPan,
            higherRateWithoutPan,
            specialConditions);

        await _tdsConfigurationRepository.AddAsync(tdsConfiguration);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("TDS configuration created successfully: {Id}", tdsConfiguration.Id);
        return tdsConfiguration;
    }

    public async Task<TdsConfiguration> UpdateTdsConfigurationAsync(
        Guid id,
        string name,
        string description,
        TdsSection section,
        EntityType entityType,
        TaxRate taxRate,
        TdsThreshold threshold,
        string modifiedBy,
        bool requiresPan = true,
        decimal higherRateWithoutPan = 0,
        string? specialConditions = null)
    {
        _logger.LogInformation("Updating TDS configuration: {Id}", id);

        var tdsConfiguration = await _tdsConfigurationRepository.GetByIdAsync(id);
        if (tdsConfiguration == null)
            throw new InvalidOperationException($"TDS configuration with ID {id} not found");

        tdsConfiguration.UpdateConfiguration(
            name,
            description,
            section,
            entityType,
            taxRate,
            threshold,
            modifiedBy,
            requiresPan,
            higherRateWithoutPan,
            specialConditions);

        await _tdsConfigurationRepository.UpdateAsync(tdsConfiguration);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("TDS configuration updated successfully: {Id}", id);
        return tdsConfiguration;
    }

    public async Task<TdsConfiguration?> GetTdsConfigurationAsync(Guid id)
    {
        return await _tdsConfigurationRepository.GetByIdAsync(id);
    }

    public async Task<List<TdsConfiguration>> GetActiveTdsConfigurationsAsync()
    {
        return await _tdsConfigurationRepository.GetActiveAsync();
    }

    public async Task<TdsConfiguration?> GetApplicableTdsConfigurationAsync(TdsSection section, EntityType entityType, DateTime date, bool hasPan = true)
    {
        return await _tdsConfigurationRepository.GetApplicableConfigurationAsync(section, entityType, date, hasPan);
    }

    public async Task<Money> CalculateTdsAsync(Money baseAmount, TdsSection section, EntityType entityType, bool hasPan = true)
    {
        var configuration = await GetApplicableTdsConfigurationAsync(section, entityType, DateTime.UtcNow, hasPan);
        if (configuration == null)
        {
            _logger.LogWarning("No applicable TDS configuration found for {Section}-{EntityType}", section, entityType);
            return Money.Zero(baseAmount.Currency);
        }

        return configuration.CalculateTds(baseAmount, hasPan);
    }

    public async Task ActivateTdsConfigurationAsync(Guid id, string activatedBy)
    {
        var configuration = await _tdsConfigurationRepository.GetByIdAsync(id);
        if (configuration == null)
            throw new InvalidOperationException($"TDS configuration with ID {id} not found");

        configuration.Activate(activatedBy);
        await _tdsConfigurationRepository.UpdateAsync(configuration);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("TDS configuration activated: {Id} by {User}", id, activatedBy);
    }

    public async Task DeactivateTdsConfigurationAsync(Guid id, string deactivatedBy, string reason)
    {
        var configuration = await _tdsConfigurationRepository.GetByIdAsync(id);
        if (configuration == null)
            throw new InvalidOperationException($"TDS configuration with ID {id} not found");

        configuration.Deactivate(deactivatedBy, reason);
        await _tdsConfigurationRepository.UpdateAsync(configuration);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("TDS configuration deactivated: {Id} by {User}, reason: {Reason}", id, deactivatedBy, reason);
    }

    public async Task ArchiveTdsConfigurationAsync(Guid id, string archivedBy, string reason)
    {
        var configuration = await _tdsConfigurationRepository.GetByIdAsync(id);
        if (configuration == null)
            throw new InvalidOperationException($"TDS configuration with ID {id} not found");

        configuration.Archive(archivedBy, reason);
        await _tdsConfigurationRepository.UpdateAsync(configuration);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("TDS configuration archived: {Id} by {User}, reason: {Reason}", id, archivedBy, reason);
    }

    #endregion

    #region HSN Code Management

    public async Task<HsnCode> CreateHsnCodeAsync(
        HsnCodeDetails codeDetails,
        string chapter,
        string section,
        DateTime effectiveFrom,
        string createdBy,
        DateTime? effectiveTo = null,
        string? additionalNotes = null)
    {
        _logger.LogInformation("Creating HSN code: {Code}", codeDetails.Code);

        var hsnCode = new HsnCode(
            codeDetails,
            chapter,
            section,
            effectiveFrom,
            createdBy,
            effectiveTo,
            additionalNotes);

        await _hsnCodeRepository.AddAsync(hsnCode);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("HSN code created successfully: {Id}", hsnCode.Id);
        return hsnCode;
    }

    public async Task<HsnCode> UpdateHsnCodeAsync(
        Guid id,
        HsnCodeDetails codeDetails,
        string chapter,
        string section,
        string modifiedBy,
        string? additionalNotes = null)
    {
        _logger.LogInformation("Updating HSN code: {Id}", id);

        var hsnCode = await _hsnCodeRepository.GetByIdAsync(id);
        if (hsnCode == null)
            throw new InvalidOperationException($"HSN code with ID {id} not found");

        hsnCode.UpdateDetails(codeDetails, chapter, section, modifiedBy, additionalNotes);

        await _hsnCodeRepository.UpdateAsync(hsnCode);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("HSN code updated successfully: {Id}", id);
        return hsnCode;
    }

    public async Task<HsnCode?> GetHsnCodeAsync(Guid id)
    {
        return await _hsnCodeRepository.GetByIdAsync(id);
    }

    public async Task<HsnCode?> GetHsnCodeByCodeAsync(string code)
    {
        return await _hsnCodeRepository.GetByCodeAsync(code);
    }

    public async Task<List<HsnCode>> GetActiveHsnCodesAsync()
    {
        return await _hsnCodeRepository.GetActiveAsync();
    }

    public async Task<List<HsnCode>> SearchHsnCodesAsync(string searchTerm, int skip = 0, int take = 50)
    {
        return await _hsnCodeRepository.SearchAsync(searchTerm, skip, take);
    }

    public async Task<GstRate?> GetApplicableGstRateForHsnAsync(string hsnCode, DateTime date)
    {
        return await _hsnCodeRepository.GetApplicableGstRateAsync(hsnCode, date);
    }

    public async Task AddGstMappingToHsnAsync(Guid hsnCodeId, GstRate gstRate, DateTime effectiveFrom, DateTime? effectiveTo, string addedBy)
    {
        var hsnCode = await _hsnCodeRepository.GetByIdAsync(hsnCodeId);
        if (hsnCode == null)
            throw new InvalidOperationException($"HSN code with ID {hsnCodeId} not found");

        hsnCode.AddGstMapping(gstRate, effectiveFrom, effectiveTo, addedBy);

        await _hsnCodeRepository.UpdateAsync(hsnCode);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("GST mapping added to HSN code: {HsnCodeId}, Rate: {GstRate}", hsnCodeId, gstRate);
    }

    public async Task<List<HsnCode>> BulkImportHsnCodesAsync(List<HsnCode> hsnCodes)
    {
        _logger.LogInformation("Bulk importing {Count} HSN codes", hsnCodes.Count);

        var importedCodes = await _hsnCodeRepository.BulkAddAsync(hsnCodes);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Successfully imported {Count} HSN codes", importedCodes.Count);
        return importedCodes;
    }

    public async Task ActivateHsnCodeAsync(Guid id, string activatedBy)
    {
        var hsnCode = await _hsnCodeRepository.GetByIdAsync(id);
        if (hsnCode == null)
            throw new InvalidOperationException($"HSN code with ID {id} not found");

        hsnCode.Activate(activatedBy);
        await _hsnCodeRepository.UpdateAsync(hsnCode);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("HSN code activated: {Id} by {User}", id, activatedBy);
    }

    public async Task DeactivateHsnCodeAsync(Guid id, string deactivatedBy, string reason)
    {
        var hsnCode = await _hsnCodeRepository.GetByIdAsync(id);
        if (hsnCode == null)
            throw new InvalidOperationException($"HSN code with ID {id} not found");

        hsnCode.Deactivate(deactivatedBy, reason);
        await _hsnCodeRepository.UpdateAsync(hsnCode);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("HSN code deactivated: {Id} by {User}, reason: {Reason}", id, deactivatedBy, reason);
    }

    public async Task DeprecateHsnCodeAsync(Guid id, string deprecatedBy, string reason)
    {
        var hsnCode = await _hsnCodeRepository.GetByIdAsync(id);
        if (hsnCode == null)
            throw new InvalidOperationException($"HSN code with ID {id} not found");

        hsnCode.Deprecate(deprecatedBy, reason);
        await _hsnCodeRepository.UpdateAsync(hsnCode);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("HSN code deprecated: {Id} by {User}, reason: {Reason}", id, deprecatedBy, reason);
    }

    #endregion

    #region Tax Configuration Management

    public async Task<TaxConfiguration> CreateTaxConfigurationAsync(
        string name,
        string description,
        TaxJurisdiction jurisdiction,
        DateTime effectiveFrom,
        string createdBy,
        DateTime? effectiveTo = null,
        bool isDefault = false,
        int priority = 0)
    {
        _logger.LogInformation("Creating tax configuration: {Name}", name);

        var taxConfiguration = new TaxConfiguration(
            name,
            description,
            jurisdiction,
            effectiveFrom,
            createdBy,
            effectiveTo,
            isDefault,
            priority);

        await _taxConfigurationRepository.AddAsync(taxConfiguration);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Tax configuration created successfully: {Id}", taxConfiguration.Id);
        return taxConfiguration;
    }

    public async Task<TaxConfiguration> UpdateTaxConfigurationAsync(
        Guid id,
        string name,
        string description,
        TaxJurisdiction jurisdiction,
        DateTime effectiveFrom,
        DateTime? effectiveTo,
        string modifiedBy,
        int priority = 0)
    {
        _logger.LogInformation("Updating tax configuration: {Id}", id);

        var taxConfiguration = await _taxConfigurationRepository.GetByIdAsync(id);
        if (taxConfiguration == null)
            throw new InvalidOperationException($"Tax configuration with ID {id} not found");

        taxConfiguration.UpdateBasicDetails(name, description, jurisdiction, effectiveFrom, effectiveTo, modifiedBy, priority);

        await _taxConfigurationRepository.UpdateAsync(taxConfiguration);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Tax configuration updated successfully: {Id}", id);
        return taxConfiguration;
    }

    public async Task<TaxConfiguration> UpdateTaxSettingsAsync(
        Guid id,
        bool enableGstCalculation,
        bool enableTdsCalculation,
        bool enableReverseCharge,
        bool requireHsnCode,
        decimal defaultGstRate,
        decimal defaultTdsRate,
        string defaultCurrency,
        string modifiedBy)
    {
        _logger.LogInformation("Updating tax settings for configuration: {Id}", id);

        var taxConfiguration = await _taxConfigurationRepository.GetByIdAsync(id);
        if (taxConfiguration == null)
            throw new InvalidOperationException($"Tax configuration with ID {id} not found");

        taxConfiguration.UpdateTaxSettings(
            enableGstCalculation,
            enableTdsCalculation,
            enableReverseCharge,
            requireHsnCode,
            defaultGstRate,
            defaultTdsRate,
            defaultCurrency,
            modifiedBy);

        await _taxConfigurationRepository.UpdateAsync(taxConfiguration);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Tax settings updated successfully: {Id}", id);
        return taxConfiguration;
    }

    public async Task<TaxConfiguration?> GetTaxConfigurationAsync(Guid id)
    {
        return await _taxConfigurationRepository.GetByIdAsync(id);
    }

    public async Task<TaxConfiguration?> GetDefaultTaxConfigurationAsync()
    {
        return await _taxConfigurationRepository.GetDefaultAsync();
    }

    public async Task<TaxConfiguration?> GetApplicableTaxConfigurationAsync(TaxJurisdiction jurisdiction, DateTime date)
    {
        return await _taxConfigurationRepository.GetApplicableConfigurationAsync(jurisdiction, date);
    }

    public async Task<List<TaxConfiguration>> GetActiveTaxConfigurationsAsync()
    {
        return await _taxConfigurationRepository.GetActiveAsync();
    }

    public async Task SetAsDefaultTaxConfigurationAsync(Guid id, string modifiedBy)
    {
        var taxConfiguration = await _taxConfigurationRepository.GetByIdAsync(id);
        if (taxConfiguration == null)
            throw new InvalidOperationException($"Tax configuration with ID {id} not found");

        taxConfiguration.SetAsDefault(modifiedBy);
        await _taxConfigurationRepository.UpdateAsync(taxConfiguration);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Tax configuration set as default: {Id} by {User}", id, modifiedBy);
    }

    public async Task RemoveAsDefaultTaxConfigurationAsync(Guid id, string modifiedBy)
    {
        var taxConfiguration = await _taxConfigurationRepository.GetByIdAsync(id);
        if (taxConfiguration == null)
            throw new InvalidOperationException($"Tax configuration with ID {id} not found");

        taxConfiguration.RemoveAsDefault(modifiedBy);
        await _taxConfigurationRepository.UpdateAsync(taxConfiguration);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Tax configuration removed as default: {Id} by {User}", id, modifiedBy);
    }

    public async Task ActivateTaxConfigurationAsync(Guid id, string activatedBy)
    {
        var configuration = await _taxConfigurationRepository.GetByIdAsync(id);
        if (configuration == null)
            throw new InvalidOperationException($"Tax configuration with ID {id} not found");

        configuration.Activate(activatedBy);
        await _taxConfigurationRepository.UpdateAsync(configuration);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Tax configuration activated: {Id} by {User}", id, activatedBy);
    }

    public async Task DeactivateTaxConfigurationAsync(Guid id, string deactivatedBy, string reason)
    {
        var configuration = await _taxConfigurationRepository.GetByIdAsync(id);
        if (configuration == null)
            throw new InvalidOperationException($"Tax configuration with ID {id} not found");

        configuration.Deactivate(deactivatedBy, reason);
        await _taxConfigurationRepository.UpdateAsync(configuration);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Tax configuration deactivated: {Id} by {User}, reason: {Reason}", id, deactivatedBy, reason);
    }

    public async Task ArchiveTaxConfigurationAsync(Guid id, string archivedBy, string reason)
    {
        var configuration = await _taxConfigurationRepository.GetByIdAsync(id);
        if (configuration == null)
            throw new InvalidOperationException($"Tax configuration with ID {id} not found");

        configuration.Archive(archivedBy, reason);
        await _taxConfigurationRepository.UpdateAsync(configuration);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Tax configuration archived: {Id} by {User}, reason: {Reason}", id, archivedBy, reason);
    }

    #endregion

    #region Comprehensive Tax Calculation

    public async Task<decimal> CalculateComprehensiveTaxAsync(
        Money baseAmount,
        ServiceCategory serviceCategory,
        TaxJurisdiction jurisdiction,
        EntityType entityType,
        TdsSection? tdsSection = null,
        string? hsnCode = null,
        bool hasPan = true,
        DateTime? calculationDate = null)
    {
        var date = calculationDate ?? DateTime.UtcNow;
        _logger.LogInformation("Calculating comprehensive tax for amount {Amount} {Currency}", baseAmount.Amount, baseAmount.Currency);

        var result = new decimal
        {
            BaseAmount = baseAmount,
            GstAmount = Money.Zero(baseAmount.Currency),
            TdsAmount = Money.Zero(baseAmount.Currency),
            AppliedRules = new List<string>(),
            Warnings = new List<string>(),
            CalculatedAt = date
        };

        // Get applicable tax configuration
        var taxConfig = await GetApplicableTaxConfigurationAsync(jurisdiction, date);
        if (taxConfig == null)
        {
            result.Warnings.Add("No applicable tax configuration found for the jurisdiction");
            _logger.LogWarning("No tax configuration found for jurisdiction: {Jurisdiction}", jurisdiction.GetFullJurisdiction());
        }

        // Calculate GST if enabled
        if (taxConfig?.EnableGstCalculation == true)
        {
            try
            {
                result.GstAmount = await CalculateGstAsync(baseAmount, serviceCategory, jurisdiction);
                result.EffectiveGstRate = result.GstAmount.Amount > 0 ? (result.GstAmount.Amount / baseAmount.Amount) * 100 : 0;
                result.AppliedRules.Add($"GST calculated at {result.EffectiveGstRate:F2}%");

                // Check for reverse charge
                var gstConfig = await GetApplicableGstConfigurationAsync(serviceCategory, jurisdiction, date);
                if (gstConfig?.IsReverseChargeApplicable == true)
                {
                    result.IsReverseChargeApplicable = true;
                    result.AppliedRules.Add("Reverse charge mechanism applicable");
                }
            }
            catch (Exception ex)
            {
                result.Warnings.Add($"GST calculation failed: {ex.Message}");
                _logger.LogWarning(ex, "GST calculation failed");
            }
        }

        // Calculate TDS if enabled and section provided
        if (taxConfig?.EnableTdsCalculation == true && tdsSection.HasValue)
        {
            try
            {
                result.TdsAmount = await CalculateTdsAsync(baseAmount, tdsSection.Value, entityType, hasPan);
                result.EffectiveTdsRate = result.TdsAmount.Amount > 0 ? (result.TdsAmount.Amount / baseAmount.Amount) * 100 : 0;
                result.AppliedRules.Add($"TDS calculated at {result.EffectiveTdsRate:F2}% under section {tdsSection}");
            }
            catch (Exception ex)
            {
                result.Warnings.Add($"TDS calculation failed: {ex.Message}");
                _logger.LogWarning(ex, "TDS calculation failed");
            }
        }

        // Validate HSN code if required
        if (taxConfig?.RequireHsnCode == true)
        {
            if (string.IsNullOrEmpty(hsnCode))
            {
                result.Warnings.Add("HSN code is required but not provided");
            }
            else
            {
                var hsnCodeEntity = await GetHsnCodeByCodeAsync(hsnCode);
                if (hsnCodeEntity == null)
                {
                    result.Warnings.Add($"HSN code {hsnCode} not found");
                }
                else if (!hsnCodeEntity.IsEffectiveOn(date))
                {
                    result.Warnings.Add($"HSN code {hsnCode} is not effective on {date:yyyy-MM-dd}");
                }
                else
                {
                    result.AppliedHsnCode = hsnCode;
                    result.AppliedRules.Add($"HSN code {hsnCode} applied");
                }
            }
        }

        // Calculate totals
        result.TotalTaxAmount = result.GstAmount + result.TdsAmount;
        result.NetAmount = baseAmount + result.GstAmount - result.TdsAmount;

        _logger.LogInformation("Tax calculation completed. GST: {Gst}, TDS: {Tds}, Net: {Net}",
            result.GstAmount.Amount, result.TdsAmount.Amount, result.NetAmount.Amount);

        return result;
    }

    #endregion

    #region Validation and Compliance

    public async Task<bool> ValidateTaxConfigurationAsync(Guid taxConfigurationId)
    {
        var configuration = await _taxConfigurationRepository.GetByIdAsync(taxConfigurationId);
        if (configuration == null)
            return false;

        // Perform validation checks
        var isValid = true;

        // Check if configuration is effective
        if (!configuration.IsEffectiveOn(DateTime.UtcNow))
        {
            _logger.LogWarning("Tax configuration {Id} is not effective on current date", taxConfigurationId);
            isValid = false;
        }

        // Check for conflicting configurations
        var conflictingConfigs = await _taxConfigurationRepository.GetByJurisdictionAsync(configuration.Jurisdiction);
        var activeConflicts = conflictingConfigs
            .Where(c => c.Id != configuration.Id &&
                       c.Status == TaxConfigurationStatus.Active &&
                       c.IsEffectiveOn(DateTime.UtcNow))
            .ToList();

        if (activeConflicts.Any())
        {
            _logger.LogWarning("Found {Count} conflicting tax configurations for jurisdiction {Jurisdiction}",
                activeConflicts.Count, configuration.Jurisdiction.GetFullJurisdiction());
            isValid = false;
        }

        return isValid;
    }

    public async Task<List<string>> GetTaxComplianceIssuesAsync(TaxJurisdiction jurisdiction)
    {
        var issues = new List<string>();

        // Check for missing default configuration
        var defaultConfig = await _taxConfigurationRepository.GetDefaultByJurisdictionAsync(jurisdiction);
        if (defaultConfig == null)
        {
            issues.Add($"No default tax configuration found for jurisdiction {jurisdiction.GetFullJurisdiction()}");
        }

        // Check for overlapping configurations
        var activeConfigs = await _taxConfigurationRepository.GetByJurisdictionAsync(jurisdiction);
        var effectiveConfigs = activeConfigs.Where(c => c.IsEffectiveOn(DateTime.UtcNow)).ToList();

        if (effectiveConfigs.Count > 1)
        {
            issues.Add($"Multiple active tax configurations found for jurisdiction {jurisdiction.GetFullJurisdiction()}");
        }

        // Check for missing GST configurations
        var gstConfigs = await _gstConfigurationRepository.GetByJurisdictionAsync(jurisdiction);
        var activeGstConfigs = gstConfigs.Where(g => g.Status == TaxConfigurationStatus.Active).ToList();

        if (!activeGstConfigs.Any())
        {
            issues.Add($"No active GST configurations found for jurisdiction {jurisdiction.GetFullJurisdiction()}");
        }

        // Check for missing TDS configurations
        var tdsConfigs = await _tdsConfigurationRepository.GetActiveAsync();
        if (!tdsConfigs.Any())
        {
            issues.Add("No active TDS configurations found");
        }

        return issues;
    }

    public async Task<bool> IsHsnCodeRequiredAsync(ServiceCategory serviceCategory, TaxJurisdiction jurisdiction)
    {
        var taxConfig = await GetApplicableTaxConfigurationAsync(jurisdiction, DateTime.UtcNow);
        return taxConfig?.RequireHsnCode == true;
    }

    #endregion
}
