using Shared.Domain.Common;

namespace TripManagement.Domain.Events;

/// <summary>
/// Domain event raised when a cargo type master is created
/// </summary>
public class CargoTypeMasterCreatedEvent : DomainEvent
{
    public Guid CargoTypeMasterId { get; }
    public string Code { get; }
    public string Name { get; }
    public string Category { get; }

    public CargoTypeMasterCreatedEvent(Guid cargoTypeMasterId, string code, string name, string category)
    {
        CargoTypeMasterId = cargoTypeMasterId;
        Code = code;
        Name = name;
        Category = category;
    }
}

/// <summary>
/// Domain event raised when a cargo type master is updated
/// </summary>
public class CargoTypeMasterUpdatedEvent : DomainEvent
{
    public Guid CargoTypeMasterId { get; }
    public string Code { get; }
    public string Name { get; }
    public string Category { get; }
    public string OldName { get; }
    public string OldCategory { get; }

    public CargoTypeMasterUpdatedEvent(Guid cargoTypeMasterId, string code, string name, string category, string oldName, string oldCategory)
    {
        CargoTypeMasterId = cargoTypeMasterId;
        Code = code;
        Name = name;
        Category = category;
        OldName = oldName;
        OldCategory = oldCategory;
    }
}

/// <summary>
/// Domain event raised when a cargo type master is activated
/// </summary>
public class CargoTypeMasterActivatedEvent : DomainEvent
{
    public Guid CargoTypeMasterId { get; }
    public string Code { get; }
    public string Name { get; }

    public CargoTypeMasterActivatedEvent(Guid cargoTypeMasterId, string code, string name)
    {
        CargoTypeMasterId = cargoTypeMasterId;
        Code = code;
        Name = name;
    }
}

/// <summary>
/// Domain event raised when a cargo type master is deactivated
/// </summary>
public class CargoTypeMasterDeactivatedEvent : DomainEvent
{
    public Guid CargoTypeMasterId { get; }
    public string Code { get; }
    public string Name { get; }

    public CargoTypeMasterDeactivatedEvent(Guid cargoTypeMasterId, string code, string name)
    {
        CargoTypeMasterId = cargoTypeMasterId;
        Code = code;
        Name = name;
    }
}


