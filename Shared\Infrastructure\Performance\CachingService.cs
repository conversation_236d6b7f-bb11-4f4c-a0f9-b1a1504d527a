using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Shared.Infrastructure.Performance;

/// <summary>
/// Multi-level caching service with Redis distributed cache and in-memory cache
/// Provides intelligent caching strategies for different data types and access patterns
/// </summary>
public interface ICachingService
{
    Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default) where T : class;
    Task SetAsync<T>(string key, T value, TimeSpan? expiration = null, CancellationToken cancellationToken = default) where T : class;
    Task RemoveAsync(string key, CancellationToken cancellationToken = default);
    Task RemoveByPatternAsync(string pattern, CancellationToken cancellationToken = default);
    Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiration = null, CancellationToken cancellationToken = default) where T : class;
    Task InvalidateTagAsync(string tag, CancellationToken cancellationToken = default);
    Task<bool> ExistsAsync(string key, CancellationToken cancellationToken = default);
    Task<Dictionary<string, T?>> GetManyAsync<T>(IEnumerable<string> keys, CancellationToken cancellationToken = default) where T : class;
    Task SetManyAsync<T>(Dictionary<string, T> items, TimeSpan? expiration = null, CancellationToken cancellationToken = default) where T : class;
}

public class CachingService : ICachingService
{
    private readonly IDistributedCache _distributedCache;
    private readonly IMemoryCache _memoryCache;
    private readonly ILogger<CachingService> _logger;
    private readonly JsonSerializerOptions _jsonOptions;
    private readonly CachingConfiguration _config;

    public CachingService(
        IDistributedCache distributedCache,
        IMemoryCache memoryCache,
        ILogger<CachingService> logger,
        CachingConfiguration config)
    {
        _distributedCache = distributedCache;
        _memoryCache = memoryCache;
        _logger = logger;
        _config = config;
        
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
            WriteIndented = false
        };
    }

    public async Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            // Try memory cache first for frequently accessed data
            if (_config.UseMemoryCache && ShouldUseMemoryCache<T>())
            {
                if (_memoryCache.TryGetValue(key, out T? memoryCachedValue))
                {
                    _logger.LogDebug("Cache hit (memory): {Key}", key);
                    return memoryCachedValue;
                }
            }

            // Try distributed cache
            var cachedBytes = await _distributedCache.GetAsync(key, cancellationToken);
            if (cachedBytes != null)
            {
                var cachedValue = JsonSerializer.Deserialize<T>(cachedBytes, _jsonOptions);
                
                // Store in memory cache for next access
                if (_config.UseMemoryCache && ShouldUseMemoryCache<T>() && cachedValue != null)
                {
                    var memoryExpiration = GetMemoryCacheExpiration<T>();
                    _memoryCache.Set(key, cachedValue, memoryExpiration);
                }
                
                _logger.LogDebug("Cache hit (distributed): {Key}", key);
                return cachedValue;
            }

            _logger.LogDebug("Cache miss: {Key}", key);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cached value for key: {Key}", key);
            return null;
        }
    }

    public async Task SetAsync<T>(string key, T value, TimeSpan? expiration = null, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            var serializedValue = JsonSerializer.SerializeToUtf8Bytes(value, _jsonOptions);
            var distributedExpiration = expiration ?? GetDefaultExpiration<T>();
            
            var options = new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = distributedExpiration
            };

            await _distributedCache.SetAsync(key, serializedValue, options, cancellationToken);

            // Also store in memory cache if appropriate
            if (_config.UseMemoryCache && ShouldUseMemoryCache<T>())
            {
                var memoryExpiration = GetMemoryCacheExpiration<T>();
                _memoryCache.Set(key, value, memoryExpiration);
            }

            _logger.LogDebug("Cached value set: {Key}, Expiration: {Expiration}", key, distributedExpiration);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting cached value for key: {Key}", key);
        }
    }

    public async Task RemoveAsync(string key, CancellationToken cancellationToken = default)
    {
        try
        {
            await _distributedCache.RemoveAsync(key, cancellationToken);
            _memoryCache.Remove(key);
            
            _logger.LogDebug("Removed cached value: {Key}", key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing cached value for key: {Key}", key);
        }
    }

    public async Task RemoveByPatternAsync(string pattern, CancellationToken cancellationToken = default)
    {
        try
        {
            // This would require a Redis-specific implementation for pattern matching
            // For now, we'll log the pattern for manual cleanup
            _logger.LogWarning("Pattern-based cache removal not implemented: {Pattern}", pattern);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing cached values by pattern: {Pattern}", pattern);
        }
    }

    public async Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiration = null, CancellationToken cancellationToken = default) where T : class
    {
        var cachedValue = await GetAsync<T>(key, cancellationToken);
        if (cachedValue != null)
        {
            return cachedValue;
        }

        var value = await factory();
        if (value != null)
        {
            await SetAsync(key, value, expiration, cancellationToken);
        }

        return value;
    }

    public async Task InvalidateTagAsync(string tag, CancellationToken cancellationToken = default)
    {
        try
        {
            // This would require implementing tag-based caching
            // For now, we'll use a simple pattern-based approach
            await RemoveByPatternAsync($"*{tag}*", cancellationToken);
            
            _logger.LogDebug("Invalidated cache tag: {Tag}", tag);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error invalidating cache tag: {Tag}", tag);
        }
    }

    public async Task<bool> ExistsAsync(string key, CancellationToken cancellationToken = default)
    {
        try
        {
            if (_memoryCache.TryGetValue(key, out _))
            {
                return true;
            }

            var cachedBytes = await _distributedCache.GetAsync(key, cancellationToken);
            return cachedBytes != null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking cache existence for key: {Key}", key);
            return false;
        }
    }

    public async Task<Dictionary<string, T?>> GetManyAsync<T>(IEnumerable<string> keys, CancellationToken cancellationToken = default) where T : class
    {
        var result = new Dictionary<string, T?>();
        var tasks = keys.Select(async key => new { Key = key, Value = await GetAsync<T>(key, cancellationToken) });
        
        var results = await Task.WhenAll(tasks);
        
        foreach (var item in results)
        {
            result[item.Key] = item.Value;
        }

        return result;
    }

    public async Task SetManyAsync<T>(Dictionary<string, T> items, TimeSpan? expiration = null, CancellationToken cancellationToken = default) where T : class
    {
        var tasks = items.Select(kvp => SetAsync(kvp.Key, kvp.Value, expiration, cancellationToken));
        await Task.WhenAll(tasks);
    }

    private bool ShouldUseMemoryCache<T>()
    {
        // Determine if type should use memory cache based on size and access patterns
        var typeName = typeof(T).Name;
        
        return typeName switch
        {
            "UserDto" => true,
            "CarrierDto" => true,
            "VehicleDto" => true,
            "DriverDto" => true,
            "PreferredPartnerDto" => true,
            _ => false
        };
    }

    private TimeSpan GetDefaultExpiration<T>()
    {
        var typeName = typeof(T).Name;
        
        return typeName switch
        {
            "UserDto" => TimeSpan.FromMinutes(30),
            "CarrierDto" => TimeSpan.FromHours(2),
            "VehicleDto" => TimeSpan.FromHours(4),
            "DriverDto" => TimeSpan.FromHours(1),
            "PreferredPartnerDto" => TimeSpan.FromMinutes(15),
            "ReportDto" => TimeSpan.FromMinutes(10),
            "AnalyticsDto" => TimeSpan.FromMinutes(5),
            _ => TimeSpan.FromMinutes(15)
        };
    }

    private TimeSpan GetMemoryCacheExpiration<T>()
    {
        // Memory cache should have shorter expiration than distributed cache
        var distributedExpiration = GetDefaultExpiration<T>();
        return TimeSpan.FromMilliseconds(distributedExpiration.TotalMilliseconds * 0.5);
    }
}

public class CachingConfiguration
{
    public bool UseMemoryCache { get; set; } = true;
    public bool UseDistributedCache { get; set; } = true;
    public TimeSpan DefaultExpiration { get; set; } = TimeSpan.FromMinutes(15);
    public TimeSpan MemoryCacheExpiration { get; set; } = TimeSpan.FromMinutes(5);
    public int MaxMemoryCacheSize { get; set; } = 1000;
    public bool EnableCacheMetrics { get; set; } = true;
}

/// <summary>
/// Cache key builder for consistent cache key generation
/// </summary>
public static class CacheKeys
{
    private const string Separator = ":";
    
    public static string User(Guid userId) => $"user{Separator}{userId}";
    public static string UserByEmail(string email) => $"user{Separator}email{Separator}{email}";
    public static string Carrier(Guid carrierId) => $"carrier{Separator}{carrierId}";
    public static string Vehicle(Guid vehicleId) => $"vehicle{Separator}{vehicleId}";
    public static string Driver(Guid driverId) => $"driver{Separator}{driverId}";
    public static string Trip(Guid tripId) => $"trip{Separator}{tripId}";
    public static string Order(Guid orderId) => $"order{Separator}{orderId}";
    public static string PreferredPartners(Guid userId) => $"preferred-partners{Separator}{userId}";
    public static string PartnerAnalytics(Guid userId, string period) => $"partner-analytics{Separator}{userId}{Separator}{period}";
    public static string Report(string reportType, string parameters) => $"report{Separator}{reportType}{Separator}{parameters}";
    public static string Analytics(string service, string metric, string period) => $"analytics{Separator}{service}{Separator}{metric}{Separator}{period}";
    
    public static string WithTag(string key, string tag) => $"{key}{Separator}tag{Separator}{tag}";
    public static string WithExpiration(string key, string expiration) => $"{key}{Separator}exp{Separator}{expiration}";
}

/// <summary>
/// Performance metrics collector for caching operations
/// </summary>
public interface ICacheMetricsCollector
{
    void RecordCacheHit(string key, string cacheType);
    void RecordCacheMiss(string key, string cacheType);
    void RecordCacheSet(string key, string cacheType, TimeSpan duration);
    void RecordCacheRemove(string key, string cacheType);
    Task<CacheMetrics> GetMetricsAsync(TimeSpan period);
}

public class CacheMetricsCollector : ICacheMetricsCollector
{
    private readonly ILogger<CacheMetricsCollector> _logger;
    private readonly Dictionary<string, CacheOperationMetric> _metrics = new();
    private readonly object _lock = new();

    public CacheMetricsCollector(ILogger<CacheMetricsCollector> logger)
    {
        _logger = logger;
    }

    public void RecordCacheHit(string key, string cacheType)
    {
        lock (_lock)
        {
            var metricKey = $"{cacheType}:hits";
            if (!_metrics.ContainsKey(metricKey))
            {
                _metrics[metricKey] = new CacheOperationMetric();
            }
            _metrics[metricKey].Count++;
            _metrics[metricKey].LastOperation = DateTime.UtcNow;
        }
    }

    public void RecordCacheMiss(string key, string cacheType)
    {
        lock (_lock)
        {
            var metricKey = $"{cacheType}:misses";
            if (!_metrics.ContainsKey(metricKey))
            {
                _metrics[metricKey] = new CacheOperationMetric();
            }
            _metrics[metricKey].Count++;
            _metrics[metricKey].LastOperation = DateTime.UtcNow;
        }
    }

    public void RecordCacheSet(string key, string cacheType, TimeSpan duration)
    {
        lock (_lock)
        {
            var metricKey = $"{cacheType}:sets";
            if (!_metrics.ContainsKey(metricKey))
            {
                _metrics[metricKey] = new CacheOperationMetric();
            }
            _metrics[metricKey].Count++;
            _metrics[metricKey].TotalDuration += duration;
            _metrics[metricKey].LastOperation = DateTime.UtcNow;
        }
    }

    public void RecordCacheRemove(string key, string cacheType)
    {
        lock (_lock)
        {
            var metricKey = $"{cacheType}:removes";
            if (!_metrics.ContainsKey(metricKey))
            {
                _metrics[metricKey] = new CacheOperationMetric();
            }
            _metrics[metricKey].Count++;
            _metrics[metricKey].LastOperation = DateTime.UtcNow;
        }
    }

    public Task<CacheMetrics> GetMetricsAsync(TimeSpan period)
    {
        lock (_lock)
        {
            var cutoff = DateTime.UtcNow - period;
            var relevantMetrics = _metrics.Where(m => m.Value.LastOperation >= cutoff).ToList();
            
            var metrics = new CacheMetrics
            {
                Period = period,
                TotalHits = relevantMetrics.Where(m => m.Key.EndsWith(":hits")).Sum(m => m.Value.Count),
                TotalMisses = relevantMetrics.Where(m => m.Key.EndsWith(":misses")).Sum(m => m.Value.Count),
                TotalSets = relevantMetrics.Where(m => m.Key.EndsWith(":sets")).Sum(m => m.Value.Count),
                TotalRemoves = relevantMetrics.Where(m => m.Key.EndsWith(":removes")).Sum(m => m.Value.Count),
                AverageSetDuration = CalculateAverageSetDuration(relevantMetrics)
            };
            
            metrics.HitRatio = metrics.TotalHits + metrics.TotalMisses > 0 
                ? (decimal)metrics.TotalHits / (metrics.TotalHits + metrics.TotalMisses) * 100 
                : 0;
            
            return Task.FromResult(metrics);
        }
    }

    private TimeSpan CalculateAverageSetDuration(List<KeyValuePair<string, CacheOperationMetric>> relevantMetrics)
    {
        var setMetrics = relevantMetrics.Where(m => m.Key.EndsWith(":sets")).ToList();
        if (!setMetrics.Any())
            return TimeSpan.Zero;
        
        var totalDuration = setMetrics.Sum(m => m.Value.TotalDuration.TotalMilliseconds);
        var totalCount = setMetrics.Sum(m => m.Value.Count);
        
        return totalCount > 0 ? TimeSpan.FromMilliseconds(totalDuration / totalCount) : TimeSpan.Zero;
    }
}

public class CacheOperationMetric
{
    public long Count { get; set; }
    public TimeSpan TotalDuration { get; set; }
    public DateTime LastOperation { get; set; }
}

public class CacheMetrics
{
    public TimeSpan Period { get; set; }
    public long TotalHits { get; set; }
    public long TotalMisses { get; set; }
    public long TotalSets { get; set; }
    public long TotalRemoves { get; set; }
    public decimal HitRatio { get; set; }
    public TimeSpan AverageSetDuration { get; set; }
}
