using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Application.Interfaces;

// Payment Gateway Abstraction
public interface IPaymentGateway
{
    string GatewayName { get; }
    Task<PaymentResult> ProcessPaymentAsync(PaymentRequest request);
    Task<RefundResult> ProcessRefundAsync(RefundRequest request);
    Task<PaymentStatus> GetPaymentStatusAsync(string transactionId);
    Task<bool> ValidateWebhookAsync(string payload, string signature);
    Task<PaymentResult> CreatePaymentIntentAsync(PaymentIntentRequest request);
}

public interface IPaymentGatewayFactory
{
    IPaymentGateway GetGateway(string gatewayName);
    IPaymentGateway GetDefaultGateway();
    IEnumerable<string> GetAvailableGateways();
}

public interface IEnhancedPaymentService
{
    // Escrow-specific payment operations
    Task<PaymentResult> ProcessEscrowFundingAsync(Guid escrowAccountId, Money amount, string paymentMethodId);
    Task<PaymentResult> ProcessEscrowReleaseAsync(Guid escrowAccountId, Money amount, Guid recipientId, string paymentMethodId);
    Task<RefundResult> ProcessEscrowRefundAsync(Guid escrowAccountId, Money amount, string reason);

    // Settlement payment operations
    Task<PaymentResult> ProcessSettlementDistributionAsync(Guid distributionId, Money amount, Guid recipientId, string paymentMethodId);

    // Commission payment operations
    Task<PaymentResult> ProcessCommissionPaymentAsync(Guid commissionId, Money amount, Guid brokerId, string paymentMethodId);

    // Multi-payment method support
    Task<PaymentResult> ProcessUpiPaymentAsync(Money amount, Guid userId, string upiId);
    Task<PaymentResult> ProcessWalletPaymentAsync(Money amount, Guid userId, string walletType, string walletId);
    Task<PaymentResult> ProcessBankTransferAsync(Money amount, Guid userId, BankAccountDetails bankDetails);
    Task<PaymentResult> ProcessCreditTermsPaymentAsync(Money amount, Guid userId, CreditTerms creditTerms);

    // Payment scheduling and automation
    Task<PaymentResult> ScheduleRecurringPaymentAsync(Money amount, Guid userId, string paymentMethodId, RecurringPaymentSchedule schedule);
    Task<PaymentResult> ProcessAutomaticPaymentAsync(Guid orderId, Money amount, Guid userId, string paymentMethodId);

    // Payment verification and validation
    Task<bool> ValidatePaymentMethodAsync(string paymentMethodId, Guid userId);
    Task<bool> VerifyPaymentCompletionAsync(string transactionId);
    Task<PaymentStatus> GetPaymentStatusAsync(string transactionId);

    // Enhanced payment reporting
    Task<List<PaymentTransaction>> GetPaymentHistoryAsync(Guid userId, DateTime? from = null, DateTime? to = null);
    Task<PaymentSummary> GetPaymentSummaryAsync(Guid userId, DateTime? from = null, DateTime? to = null);
}

public class PaymentResult
{
    public bool IsSuccess { get; set; }
    public string? TransactionId { get; set; }
    public string? ErrorMessage { get; set; }
    public string? GatewayResponse { get; set; }
    public DateTime ProcessedAt { get; set; }
    public PaymentMethod? PaymentMethod { get; set; }
}

public class RefundResult
{
    public bool IsSuccess { get; set; }
    public string? RefundId { get; set; }
    public Money RefundAmount { get; set; } = Money.Zero("INR");
    public string? ErrorMessage { get; set; }
    public DateTime ProcessedAt { get; set; }
}

public class BankAccountDetails
{
    public string AccountNumber { get; set; } = string.Empty;
    public string IfscCode { get; set; } = string.Empty;
    public string BankName { get; set; } = string.Empty;
    public string AccountHolderName { get; set; } = string.Empty;
}

public class CreditTerms
{
    public int CreditDays { get; set; }
    public Money CreditLimit { get; set; } = Money.Zero("INR");
    public decimal InterestRate { get; set; }
    public DateTime DueDate { get; set; }
}

public class RecurringPaymentSchedule
{
    public RecurringFrequency Frequency { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public int? MaxOccurrences { get; set; }
}

public enum RecurringFrequency
{
    Daily = 1,
    Weekly = 2,
    Monthly = 3,
    Quarterly = 4,
    Yearly = 5
}

public enum PaymentStatus
{
    Pending = 1,
    Processing = 2,
    Completed = 3,
    Failed = 4,
    Cancelled = 5,
    Refunded = 6
}

public class PaymentMethod
{
    public string Id { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string? Last4 { get; set; }
    public string? Brand { get; set; }
    public bool IsDefault { get; set; }
    public DateTime CreatedAt { get; set; }
}

public class PaymentTransaction
{
    public string TransactionId { get; set; } = string.Empty;
    public Money Amount { get; set; } = Money.Zero("INR");
    public PaymentStatus Status { get; set; }
    public string PaymentMethod { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public string? Description { get; set; }
}

public class PaymentSummary
{
    public Money TotalPaid { get; set; } = Money.Zero("INR");
    public Money TotalRefunded { get; set; } = Money.Zero("INR");
    public int TotalTransactions { get; set; }
    public int SuccessfulTransactions { get; set; }
    public int FailedTransactions { get; set; }
}

// Payment Gateway Models
public class PaymentRequest
{
    public Money Amount { get; set; } = Money.Zero("INR");
    public Guid UserId { get; set; }
    public string PaymentMethodId { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public Dictionary<string, object> Metadata { get; set; } = new();
    public string? CallbackUrl { get; set; }
    public string? CancelUrl { get; set; }
}

public class RefundRequest
{
    public string TransactionId { get; set; } = string.Empty;
    public Money Amount { get; set; } = Money.Zero("INR");
    public string Reason { get; set; } = string.Empty;
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class PaymentIntentRequest
{
    public Money Amount { get; set; } = Money.Zero("INR");
    public Guid UserId { get; set; }
    public string? PaymentMethodId { get; set; }
    public string Description { get; set; } = string.Empty;
    public Dictionary<string, object> Metadata { get; set; } = new();
    public bool AutoCapture { get; set; } = true;
}

public enum PaymentGatewayType
{
    Razorpay = 1,
    Stripe = 2,
    PayPal = 3,
    Square = 4,
    Paytm = 5,
    PhonePe = 6
}
