using MediatR;
using Microsoft.Extensions.Logging;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Domain.Enums;
using Shared.Infrastructure.Interfaces;

namespace FinancialPayment.Application.Commands.CreateSettlement;

public class CreateSettlementCommandHandler : IRequestHandler<CreateSettlementCommand, Guid>
{
    private readonly ISettlementService _settlementService;
    private readonly ILogger<CreateSettlementCommandHandler> _logger;
    private readonly IUnitOfWork _unitOfWork;

    public CreateSettlementCommandHandler(
        ISettlementService settlementService,
        ILogger<CreateSettlementCommandHandler> logger,
        IUnitOfWork unitOfWork)
    {
        _settlementService = settlementService;
        _logger = logger;
        _unitOfWork = unitOfWork;
    }

    public async Task<Guid> Handle(CreateSettlementCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Creating settlement for Order {OrderId}, Trip {TripId}",
                request.OrderId, request.TripId);

            // Convert distributions
            var distributions = request.Distributions.Select(d => new SettlementDistributionRequest
            {
                RecipientId = d.ParticipantId,
                RecipientRole = Enum.Parse<ParticipantRole>(d.ParticipantRole, true),
                Amount = d.Amount.ToMoney(),
                Type = DetermineDistributionType(Enum.Parse<ParticipantRole>(d.ParticipantRole, true)),
                Description = d.Description ?? string.Empty
            }).ToList();

            var settlement = await _settlementService.CreateSettlementAsync(
                request.OrderId,
                request.TripId,
                request.EscrowAccountId,
                request.TotalAmount.ToMoney(),
                distributions);

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Settlement {SettlementId} created successfully for Order {OrderId}",
                settlement.Id, request.OrderId);

            return settlement.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating settlement for Order {OrderId}", request.OrderId);
            throw;
        }
    }

    private static DistributionType DetermineDistributionType(ParticipantRole role)
    {
        return role switch
        {
            ParticipantRole.Carrier => DistributionType.CarrierPayment,
            ParticipantRole.Broker => DistributionType.BrokerCommission,
            ParticipantRole.Platform => DistributionType.PlatformFee,
            _ => DistributionType.CarrierPayment
        };
    }
}


