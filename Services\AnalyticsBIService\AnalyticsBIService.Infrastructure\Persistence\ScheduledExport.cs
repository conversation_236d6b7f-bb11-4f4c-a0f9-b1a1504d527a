using Shared.Domain.Common;

namespace AnalyticsBIService.Infrastructure.Persistence;

/// <summary>
/// Scheduled export entity for automated data export operations
/// </summary>
public class ScheduledExport : BaseEntity
{
    public Guid UserId { get; private set; }
    public string Name { get; private set; }
    public string Description { get; private set; }
    public string ExportRequest { get; private set; } // JSON serialized export configuration
    public string ScheduleType { get; private set; } // OneTime, Daily, Weekly, Monthly, Custom
    public string CronExpression { get; private set; }
    public DateTime StartDate { get; private set; }
    public DateTime? EndDate { get; private set; }
    public DateTime NextRunDate { get; private set; }
    public DateTime? LastRunDate { get; private set; }
    public string? LastRunStatus { get; private set; } // Success, Failed, Running
    public string DeliveryMethod { get; private set; } // Email, FTP, S3, Local
    public string DeliveryOptions { get; private set; } // JSON serialized delivery configuration
    public bool IsActive { get; private set; }
    public int ExecutionCount { get; private set; }
    public int SuccessfulExecutions { get; private set; }
    public int FailedExecutions { get; private set; }
    public string? LastErrorMessage { get; private set; }

    // Private constructor for EF Core
    private ScheduledExport()
    {
        Name = string.Empty;
        Description = string.Empty;
        ExportRequest = string.Empty;
        ScheduleType = string.Empty;
        CronExpression = string.Empty;
        DeliveryMethod = string.Empty;
        DeliveryOptions = string.Empty;
    }

    public ScheduledExport(
        Guid userId,
        string name,
        string description,
        string exportRequest,
        string scheduleType,
        string cronExpression,
        DateTime startDate,
        string deliveryMethod,
        string deliveryOptions,
        DateTime? endDate = null)
    {
        UserId = userId;
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? string.Empty;
        ExportRequest = exportRequest ?? throw new ArgumentNullException(nameof(exportRequest));
        ScheduleType = scheduleType ?? throw new ArgumentNullException(nameof(scheduleType));
        CronExpression = cronExpression ?? throw new ArgumentNullException(nameof(cronExpression));
        StartDate = startDate;
        EndDate = endDate;
        DeliveryMethod = deliveryMethod ?? throw new ArgumentNullException(nameof(deliveryMethod));
        DeliveryOptions = deliveryOptions ?? "{}";
        IsActive = true;
        ExecutionCount = 0;
        SuccessfulExecutions = 0;
        FailedExecutions = 0;
        NextRunDate = CalculateNextRunDate();
    }

    public void UpdateSchedule(string scheduleType, string cronExpression, DateTime startDate, DateTime? endDate = null)
    {
        ScheduleType = scheduleType ?? throw new ArgumentNullException(nameof(scheduleType));
        CronExpression = cronExpression ?? throw new ArgumentNullException(nameof(cronExpression));
        StartDate = startDate;
        EndDate = endDate;
        NextRunDate = CalculateNextRunDate();
        UpdatedAt = DateTime.UtcNow;
    }

    public void UpdateExportRequest(string exportRequest)
    {
        ExportRequest = exportRequest ?? throw new ArgumentNullException(nameof(exportRequest));
        UpdatedAt = DateTime.UtcNow;
    }

    public void UpdateDeliveryConfiguration(string deliveryMethod, string deliveryOptions)
    {
        DeliveryMethod = deliveryMethod ?? throw new ArgumentNullException(nameof(deliveryMethod));
        DeliveryOptions = deliveryOptions ?? "{}";
        UpdatedAt = DateTime.UtcNow;
    }

    public void RecordExecution(bool success, string? errorMessage = null)
    {
        ExecutionCount++;
        LastRunDate = DateTime.UtcNow;
        
        if (success)
        {
            SuccessfulExecutions++;
            LastRunStatus = "Success";
            LastErrorMessage = null;
        }
        else
        {
            FailedExecutions++;
            LastRunStatus = "Failed";
            LastErrorMessage = errorMessage;
        }

        NextRunDate = CalculateNextRunDate();
        UpdatedAt = DateTime.UtcNow;
    }

    public void Activate()
    {
        IsActive = true;
        NextRunDate = CalculateNextRunDate();
        UpdatedAt = DateTime.UtcNow;
    }

    public void Deactivate()
    {
        IsActive = false;
        UpdatedAt = DateTime.UtcNow;
    }

    public decimal GetSuccessRate()
    {
        return ExecutionCount > 0 ? (decimal)SuccessfulExecutions / ExecutionCount * 100 : 0;
    }

    private DateTime CalculateNextRunDate()
    {
        // Simple implementation - in real scenario, would use a proper cron parser
        return ScheduleType switch
        {
            "Daily" => DateTime.UtcNow.AddDays(1),
            "Weekly" => DateTime.UtcNow.AddDays(7),
            "Monthly" => DateTime.UtcNow.AddMonths(1),
            "OneTime" => StartDate,
            _ => DateTime.UtcNow.AddDays(1) // Default to daily
        };
    }
}
