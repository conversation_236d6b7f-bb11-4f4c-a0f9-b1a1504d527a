using FinancialPayment.Domain.Entities;

namespace FinancialPayment.Application.Interfaces;

public interface IPaymentMilestoneRepository
{
    Task<PaymentMilestone?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<List<PaymentMilestone>> GetByEscrowAccountIdAsync(Guid escrowAccountId, CancellationToken cancellationToken = default);
    Task<List<PaymentMilestone>> GetPendingMilestonesAsync(CancellationToken cancellationToken = default);
    Task<List<PaymentMilestone>> GetOverdueMilestonesAsync(CancellationToken cancellationToken = default);
    Task<List<PaymentMilestone>> GetMilestonesByStatusAsync(PaymentMilestoneStatus status, CancellationToken cancellationToken = default);
    Task<List<PaymentMilestone>> GetMilestonesRequiringApprovalAsync(CancellationToken cancellationToken = default);
    Task<List<PaymentMilestone>> GetMilestonesByTripLegReferenceAsync(string tripLegReference, CancellationToken cancellationToken = default);
    Task<List<PaymentMilestone>> GetMilestonesByOrderReferenceAsync(string orderReference, CancellationToken cancellationToken = default);
    Task AddAsync(PaymentMilestone milestone, CancellationToken cancellationToken = default);
    void Update(PaymentMilestone milestone);
    void Delete(PaymentMilestone milestone);
}



public interface IMessageBroker
{
    Task PublishAsync<T>(string topic, T message, CancellationToken cancellationToken = default) where T : class;
    Task PublishAsync(string topic, object message, CancellationToken cancellationToken = default);
}

public interface IPaymentGatewayService
{
    Task<PaymentResult> ProcessPaymentAsync(
        Guid recipientId,
        FinancialPayment.Domain.ValueObjects.Money amount,
        string? paymentMethodId,
        string description,
        Dictionary<string, object>? metadata = null,
        CancellationToken cancellationToken = default);

    Task<PaymentResult> RefundPaymentAsync(
        string paymentGatewayTransactionId,
        FinancialPayment.Domain.ValueObjects.Money amount,
        string reason,
        CancellationToken cancellationToken = default);

    Task<PaymentMethodInfo?> GetPaymentMethodAsync(
        string paymentMethodId,
        CancellationToken cancellationToken = default);

    Task<List<PaymentMethodInfo>> GetUserPaymentMethodsAsync(
        Guid userId,
        CancellationToken cancellationToken = default);
}



public class PaymentMethodInfo
{
    public string Id { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public bool IsDefault { get; set; }
    public bool IsActive { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}
