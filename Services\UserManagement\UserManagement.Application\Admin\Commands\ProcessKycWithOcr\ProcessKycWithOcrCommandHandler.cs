using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.Extensions.Logging;
using UserManagement.Application.Services;
using UserManagement.Domain.Repositories;
using UserManagement.Domain.Exceptions;

namespace UserManagement.Application.Admin.Commands.ProcessKycWithOcr
{
    public class ProcessKycWithOcrCommandHandler : IRequestHandler<ProcessKycWithOcrCommand, ProcessKycWithOcrResponse>
    {
        private readonly IUserProfileRepository _userProfileRepository;
        private readonly IDocumentSubmissionRepository _documentRepository;
        private readonly IOcrService _ocrService;
        private readonly IKycApprovalHistoryRepository _historyRepository;
        private readonly INotificationService _notificationService;
        private readonly ILogger<ProcessKycWithOcrCommandHandler> _logger;

        public ProcessKycWithOcrCommandHandler(
            IUserProfileRepository userProfileRepository,
            IDocumentSubmissionRepository documentRepository,
            IOcrService ocrService,
            IKycApprovalHistoryRepository historyRepository,
            INotificationService notificationService,
            ILogger<ProcessKycWithOcrCommandHandler> logger)
        {
            _userProfileRepository = userProfileRepository;
            _documentRepository = documentRepository;
            _ocrService = ocrService;
            _historyRepository = historyRepository;
            _notificationService = notificationService;
            _logger = logger;
        }

        public async Task<ProcessKycWithOcrResponse> Handle(ProcessKycWithOcrCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Processing KYC with OCR for User {UserId}, Document {DocumentId}",
                    request.UserId, request.DocumentId);

                // Get user profile and document
                var userProfile = await _userProfileRepository.GetByUserIdAsync(request.UserId);
                if (userProfile == null)
                {
                    throw new UserManagementDomainException("User profile not found");
                }

                var documentSubmission = await _documentRepository.GetDocumentByIdAsync(request.DocumentId);
                if (documentSubmission == null)
                {
                    throw new UserManagementDomainException("Document submission not found");
                }

                // Find the specific document within the submission
                var document = documentSubmission.Documents.FirstOrDefault(d => d.Id == request.DocumentId);
                if (document == null)
                {
                    throw new UserManagementDomainException("Document not found in submission");
                }

                // Perform OCR processing
                var ocrResult = await ProcessDocumentWithOcr(document, userProfile, request.DocumentType);

                // Determine decision based on OCR results
                var decision = DetermineKycDecision(ocrResult, request.ConfidenceThreshold, request.AutoApprove);

                // Apply the decision
                await ApplyKycDecision(userProfile, document, decision, request.ProcessedBy, request.Notes);

                // Record in history
                await RecordKycHistory(request, decision, ocrResult);

                // Send notifications
                await SendKycNotification(userProfile, decision, ocrResult);

                var response = new ProcessKycWithOcrResponse
                {
                    Success = true,
                    Message = GetDecisionMessage(decision),
                    OcrResult = ocrResult,
                    Decision = decision,
                    ProcessedAt = DateTime.UtcNow
                };

                _logger.LogInformation("KYC processing completed for User {UserId} with decision {Decision}",
                    request.UserId, decision);

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing KYC with OCR for User {UserId}", request.UserId);

                return new ProcessKycWithOcrResponse
                {
                    Success = false,
                    Message = "Failed to process KYC document",
                    ValidationErrors = new List<string> { ex.Message },
                    ProcessedAt = DateTime.UtcNow
                };
            }
        }

        private async Task<OcrComparisonResult> ProcessDocumentWithOcr(
            Domain.Entities.Document document,
            Domain.Entities.UserProfile userProfile,
            string documentType)
        {
            // Extract text and data from document using OCR
            var serviceExtractionResult = await _ocrService.ExtractDataAsync(document.FilePath, documentType);

            // Convert to local OcrExtractionResult type
            var extractedData = new OcrExtractionResult
            {
                Fields = serviceExtractionResult.Fields,
                ExtractedText = serviceExtractionResult.ExtractedText,
                Confidence = serviceExtractionResult.Confidence,
                Engine = serviceExtractionResult.Engine
            };

            // Compare extracted data with user-provided data
            var fieldComparisons = CompareExtractedDataWithUserData(extractedData, userProfile, documentType);

            // Validate document quality and completeness
            var validationIssues = ValidateDocumentQuality(extractedData, documentType);

            // Calculate overall confidence
            var overallConfidence = CalculateOverallConfidence(fieldComparisons);

            return new OcrComparisonResult
            {
                DocumentType = documentType,
                OverallConfidence = overallConfidence,
                FieldComparisons = fieldComparisons,
                ExtractedText = extractedData.ExtractedText,
                ValidationIssues = validationIssues,
                PassedValidation = validationIssues.All(i => i.Severity != "Critical"),
                ProcessingEngine = extractedData.Engine,
                ProcessedAt = DateTime.UtcNow
            };
        }

        private Dictionary<string, OcrFieldComparison> CompareExtractedDataWithUserData(
            OcrExtractionResult extractedData,
            Domain.Entities.UserProfile userProfile,
            string documentType)
        {
            var comparisons = new Dictionary<string, OcrFieldComparison>();

            switch (documentType.ToLowerInvariant())
            {
                case "pan":
                    comparisons["PanNumber"] = CompareField("PanNumber",
                        extractedData.Fields.GetValueOrDefault("PanNumber"),
                        userProfile.PanNumber);
                    comparisons["Name"] = CompareField("Name",
                        extractedData.Fields.GetValueOrDefault("Name"),
                        $"{userProfile.FirstName} {userProfile.LastName}");
                    break;

                case "gst":
                    comparisons["GstNumber"] = CompareField("GstNumber",
                        extractedData.Fields.GetValueOrDefault("GstNumber"),
                        userProfile.GstNumber);
                    comparisons["CompanyName"] = CompareField("CompanyName",
                        extractedData.Fields.GetValueOrDefault("CompanyName"),
                        userProfile.CompanyName);
                    break;

                case "aadhar":
                    comparisons["AadharNumber"] = CompareField("AadharNumber",
                        extractedData.Fields.GetValueOrDefault("AadharNumber"),
                        userProfile.AadharNumber);
                    comparisons["Name"] = CompareField("Name",
                        extractedData.Fields.GetValueOrDefault("Name"),
                        $"{userProfile.FirstName} {userProfile.LastName}");
                    break;

                case "license":
                    comparisons["LicenseNumber"] = CompareField("LicenseNumber",
                        extractedData.Fields.GetValueOrDefault("LicenseNumber"),
                        userProfile.LicenseNumber);
                    comparisons["Name"] = CompareField("Name",
                        extractedData.Fields.GetValueOrDefault("Name"),
                        $"{userProfile.FirstName} {userProfile.LastName}");
                    break;
            }

            return comparisons;
        }

        private OcrFieldComparison CompareField(string fieldName, string extractedValue, string userValue)
        {
            if (string.IsNullOrEmpty(extractedValue) || string.IsNullOrEmpty(userValue))
            {
                return new OcrFieldComparison
                {
                    FieldName = fieldName,
                    ExtractedValue = extractedValue ?? "",
                    UserProvidedValue = userValue ?? "",
                    IsMatch = false,
                    Confidence = 0,
                    MatchType = "NoMatch"
                };
            }

            var isExactMatch = string.Equals(extractedValue.Trim(), userValue.Trim(), StringComparison.OrdinalIgnoreCase);
            var similarity = CalculateStringSimilarity(extractedValue, userValue);

            return new OcrFieldComparison
            {
                FieldName = fieldName,
                ExtractedValue = extractedValue,
                UserProvidedValue = userValue,
                IsMatch = isExactMatch || similarity > 0.8m,
                Confidence = isExactMatch ? 1.0m : similarity,
                MatchType = isExactMatch ? "Exact" : similarity > 0.8m ? "Fuzzy" : "NoMatch"
            };
        }

        private decimal CalculateStringSimilarity(string str1, string str2)
        {
            // Simple Levenshtein distance-based similarity
            var distance = LevenshteinDistance(str1.ToLowerInvariant(), str2.ToLowerInvariant());
            var maxLength = Math.Max(str1.Length, str2.Length);
            return maxLength == 0 ? 1.0m : 1.0m - (decimal)distance / maxLength;
        }

        private int LevenshteinDistance(string s1, string s2)
        {
            var matrix = new int[s1.Length + 1, s2.Length + 1];

            for (int i = 0; i <= s1.Length; i++)
                matrix[i, 0] = i;

            for (int j = 0; j <= s2.Length; j++)
                matrix[0, j] = j;

            for (int i = 1; i <= s1.Length; i++)
            {
                for (int j = 1; j <= s2.Length; j++)
                {
                    var cost = s1[i - 1] == s2[j - 1] ? 0 : 1;
                    matrix[i, j] = Math.Min(
                        Math.Min(matrix[i - 1, j] + 1, matrix[i, j - 1] + 1),
                        matrix[i - 1, j - 1] + cost);
                }
            }

            return matrix[s1.Length, s2.Length];
        }

        private List<OcrValidationIssue> ValidateDocumentQuality(OcrExtractionResult extractedData, string documentType)
        {
            var issues = new List<OcrValidationIssue>();

            if (extractedData.Confidence < 0.7m)
            {
                issues.Add(new OcrValidationIssue
                {
                    IssueType = "DocumentQuality",
                    Description = "Document image quality is poor, affecting OCR accuracy",
                    Severity = "Medium",
                    Recommendation = "Request user to upload a clearer image"
                });
            }

            if (extractedData.Fields.Count == 0)
            {
                issues.Add(new OcrValidationIssue
                {
                    IssueType = "TextClarity",
                    Description = "No readable text could be extracted from the document",
                    Severity = "Critical",
                    Recommendation = "Document requires manual review or resubmission"
                });
            }

            return issues;
        }

        private decimal CalculateOverallConfidence(Dictionary<string, OcrFieldComparison> fieldComparisons)
        {
            if (!fieldComparisons.Any())
                return 0;

            return fieldComparisons.Values.Average(f => f.Confidence);
        }

        private KycDecision DetermineKycDecision(OcrComparisonResult ocrResult, decimal confidenceThreshold, bool autoApprove)
        {
            if (!ocrResult.PassedValidation)
                return KycDecision.RequiresResubmission;

            if (ocrResult.OverallConfidence >= confidenceThreshold &&
                ocrResult.FieldComparisons.Values.All(f => f.IsMatch))
            {
                return autoApprove ? KycDecision.Approved : KycDecision.RequiresManualReview;
            }

            if (ocrResult.OverallConfidence < 0.5m)
                return KycDecision.RequiresResubmission;

            return KycDecision.RequiresManualReview;
        }

        private async Task ApplyKycDecision(
            Domain.Entities.UserProfile userProfile,
            Domain.Entities.Document document,
            KycDecision decision,
            Guid processedBy,
            string notes)
        {
            switch (decision)
            {
                case KycDecision.Approved:
                    userProfile.Approve(processedBy);
                    break;
                case KycDecision.Rejected:
                    userProfile.Reject("Failed OCR validation");
                    break;
                case KycDecision.RequiresResubmission:
                    // Mark document for resubmission
                    break;
                case KycDecision.RequiresManualReview:
                    // Keep status as under review
                    break;
            }

            await _userProfileRepository.UpdateAsync(userProfile);
        }

        private async Task RecordKycHistory(ProcessKycWithOcrCommand request, KycDecision decision, OcrComparisonResult ocrResult)
        {
            // Convert from Application KycDecision to Domain KycDecision
            var domainDecision = decision switch
            {
                KycDecision.Approved => Domain.Enums.KycDecision.Approved,
                KycDecision.Rejected => Domain.Enums.KycDecision.Rejected,
                KycDecision.RequiresManualReview => Domain.Enums.KycDecision.UnderReview,
                KycDecision.RequiresResubmission => Domain.Enums.KycDecision.Rejected,
                _ => Domain.Enums.KycDecision.UnderReview
            };

            var historyEntry = new Domain.Entities.KycApprovalHistoryEntry(
                request.UserId,
                request.ProcessedBy,
                domainDecision,
                request.Notes ?? GetDecisionReason(decision, ocrResult),
                request.DocumentType,
                decision == KycDecision.Rejected ? GetDecisionReason(decision, ocrResult) : null,
                decision == KycDecision.RequiresResubmission,
                decision == KycDecision.RequiresResubmission ? DateTime.UtcNow.AddDays(30) : null
            );

            await _historyRepository.AddAsync(historyEntry);
        }

        private async Task SendKycNotification(Domain.Entities.UserProfile userProfile, KycDecision decision, OcrComparisonResult ocrResult)
        {
            var notificationType = decision switch
            {
                KycDecision.Approved => "KycApproved",
                KycDecision.Rejected => "KycRejected",
                KycDecision.RequiresResubmission => "KycResubmissionRequired",
                KycDecision.RequiresManualReview => "KycUnderReview",
                _ => "KycStatusUpdate"
            };

            await _notificationService.SendKycStatusNotificationAsync(
                userProfile.UserId,
                GetDecisionMessage(decision));
        }

        private string GetDecisionReason(KycDecision decision, OcrComparisonResult ocrResult)
        {
            return decision switch
            {
                KycDecision.Approved => "Automatic approval based on OCR validation",
                KycDecision.Rejected => "Failed OCR validation with low confidence",
                KycDecision.RequiresResubmission => "Document quality issues detected",
                KycDecision.RequiresManualReview => $"OCR confidence {ocrResult.OverallConfidence:P} requires manual review",
                _ => "Unknown decision reason"
            };
        }

        private string GetDecisionMessage(KycDecision decision)
        {
            return decision switch
            {
                KycDecision.Approved => "KYC document approved successfully",
                KycDecision.Rejected => "KYC document rejected",
                KycDecision.RequiresResubmission => "KYC document requires resubmission",
                KycDecision.RequiresManualReview => "KYC document requires manual review",
                _ => "KYC processing completed"
            };
        }
    }

    // Supporting classes for OCR service
    public class OcrExtractionResult
    {
        public Dictionary<string, string> Fields { get; set; } = new();
        public List<string> ExtractedText { get; set; } = new();
        public decimal Confidence { get; set; }
        public string Engine { get; set; }
    }
}
