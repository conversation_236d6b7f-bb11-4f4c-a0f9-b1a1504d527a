# Fix final critical compilation issues
Write-Host "Fixing final critical compilation issues..." -ForegroundColor Green

# Fix remaining TLI.Shared.Domain references
Write-Host "Removing remaining TLI.Shared.Domain references..." -ForegroundColor Yellow
$allFiles = Get-ChildItem -Path "Services" -Recurse -Filter "*.cs"

foreach ($file in $allFiles) {
    $content = Get-Content $file.FullName -Raw
    $modified = $false
    
    # Remove TLI.Shared.Domain references
    if ($content -match "using TLI\.Shared\.Domain") {
        Write-Host "  Removing TLI.Shared.Domain reference in $($file.Name)" -ForegroundColor Cyan
        $content = $content -replace "using TLI\.Shared\.Domain[^;]*;", ""
        $modified = $true
    }
    
    # Fix ambiguous references
    if ($content -match "TLI\.Shared\.Domain\.") {
        $content = $content -replace "TLI\.Shared\.Domain\.Entities\.BaseEntity", "Shared.Domain.Common.BaseEntity"
        $content = $content -replace "TLI\.Shared\.Domain\.Events\.DomainEvent", "Shared.Domain.Common.DomainEvent"
        $content = $content -replace "TLI\.Shared\.Domain\.", "Shared.Domain.Common."
        $modified = $true
    }
    
    if ($modified) {
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8
        Write-Host "    Updated $($file.Name)" -ForegroundColor Green
    }
}

# Fix package version conflicts
Write-Host "Fixing remaining package version conflicts..." -ForegroundColor Yellow
$projectFiles = Get-ChildItem -Path "Services" -Recurse -Filter "*.csproj"

foreach ($projectFile in $projectFiles) {
    $content = Get-Content $projectFile.FullName -Raw
    $modified = $false
    
    # Update Microsoft.Extensions.Caching.Memory to 8.0.1
    if ($content -match '<PackageReference Include="Microsoft\.Extensions\.Caching\.Memory" Version="8\.0\.0"') {
        $content = $content -replace 'Version="8\.0\.0"', 'Version="8.0.1"'
        $modified = $true
        Write-Host "  Updated Microsoft.Extensions.Caching.Memory to 8.0.1 in $($projectFile.Name)" -ForegroundColor Cyan
    }
    
    # Update StackExchange.Redis to 2.8.16
    if ($content -match '<PackageReference Include="StackExchange\.Redis" Version="2\.7\.10"') {
        $content = $content -replace 'Version="2\.7\.10"', 'Version="2.8.16"'
        $modified = $true
        Write-Host "  Updated StackExchange.Redis to 2.8.16 in $($projectFile.Name)" -ForegroundColor Cyan
    }
    
    # Update Serilog.Sinks.File to 6.0.0
    if ($content -match '<PackageReference Include="Serilog\.Sinks\.File" Version="5\.0\.0"') {
        $content = $content -replace 'Version="5\.0\.0"', 'Version="6.0.0"'
        $modified = $true
        Write-Host "  Updated Serilog.Sinks.File to 6.0.0 in $($projectFile.Name)" -ForegroundColor Cyan
    }
    
    # Update Microsoft.EntityFrameworkCore.Design to 9.0.5 for Tools compatibility
    if ($content -match '<PackageReference Include="Microsoft\.EntityFrameworkCore\.Design" Version="8\.0\.11"') {
        $content = $content -replace 'Version="8\.0\.11"', 'Version="9.0.5"'
        $modified = $true
        Write-Host "  Updated Microsoft.EntityFrameworkCore.Design to 9.0.5 in $($projectFile.Name)" -ForegroundColor Cyan
    }
    
    if ($modified) {
        Set-Content -Path $projectFile.FullName -Value $content -Encoding UTF8
        Write-Host "    Updated $($projectFile.Name)" -ForegroundColor Green
    }
}

# Remove Microsoft.ML.Transforms package reference (doesn't exist)
Write-Host "Removing invalid package references..." -ForegroundColor Yellow
$auditApiProject = "Services\AuditCompliance\AuditCompliance.API\AuditCompliance.API.csproj"
if (Test-Path $auditApiProject) {
    $content = Get-Content $auditApiProject -Raw
    if ($content -match '<PackageReference Include="Microsoft\.ML\.Transforms"') {
        $content = $content -replace '<PackageReference Include="Microsoft\.ML\.Transforms"[^>]*>\s*', ''
        Set-Content -Path $auditApiProject -Value $content -Encoding UTF8
        Write-Host "  Removed Microsoft.ML.Transforms from AuditCompliance.API" -ForegroundColor Green
    }
}

# Fix SubscriptionManagement duplicate events
Write-Host "Fixing SubscriptionManagement duplicate events..." -ForegroundColor Yellow
$subscriptionEventsFile = "Services\SubscriptionManagement\SubscriptionManagement.Domain\Events\SubscriptionEvents.cs"
if (Test-Path $subscriptionEventsFile) {
    $content = Get-Content $subscriptionEventsFile -Raw
    
    # Remove duplicate PaymentProofVerifiedEvent (keep only the first one)
    $lines = $content -split "`n"
    $newLines = @()
    $seenPaymentProofVerified = $false
    $seenPaymentProofRejected = $false
    $skipUntilNextRecord = $false
    
    foreach ($line in $lines) {
        if ($line -match "public record PaymentProofVerifiedEvent") {
            if ($seenPaymentProofVerified) {
                $skipUntilNextRecord = $true
                continue
            } else {
                $seenPaymentProofVerified = $true
                $newLines += $line
            }
        } elseif ($line -match "public record PaymentProofRejectedEvent") {
            if ($seenPaymentProofRejected) {
                $skipUntilNextRecord = $true
                continue
            } else {
                $seenPaymentProofRejected = $true
                $newLines += $line
            }
        } elseif ($skipUntilNextRecord -and $line -match "^\s*\}\s*$") {
            $skipUntilNextRecord = $false
            continue
        } elseif (-not $skipUntilNextRecord) {
            $newLines += $line
        }
    }
    
    $newContent = $newLines -join "`n"
    Set-Content -Path $subscriptionEventsFile -Value $newContent -Encoding UTF8
    Write-Host "  Fixed duplicate events in SubscriptionEvents.cs" -ForegroundColor Green
}

# Fix ContentManagement missing Primitives namespace
Write-Host "Creating missing Primitives namespace in Shared.Domain..." -ForegroundColor Yellow
$primitivesDir = "Shared\Shared.Domain\Primitives"
if (-not (Test-Path $primitivesDir)) {
    New-Item -ItemType Directory -Path $primitivesDir -Force | Out-Null
    Write-Host "  Created Primitives directory" -ForegroundColor Green
}

# Create IDomainEvent in Primitives if needed
$primitivesIDomainEventFile = "$primitivesDir\IDomainEvent.cs"
if (-not (Test-Path $primitivesIDomainEventFile)) {
    $idomainEventContent = @"
using System;

namespace Shared.Domain.Primitives
{
    public interface IDomainEvent
    {
        Guid Id { get; }
        DateTime OccurredOn { get; }
    }
}
"@
    Set-Content -Path $primitivesIDomainEventFile -Value $idomainEventContent -Encoding UTF8
    Write-Host "  Created IDomainEvent in Primitives" -ForegroundColor Green
}

# Fix ContentManagement using statements
Write-Host "Fixing ContentManagement using statements..." -ForegroundColor Yellow
$contentFiles = Get-ChildItem -Path "Services\ContentManagement\ContentManagement.Domain" -Recurse -Filter "*.cs"

foreach ($file in $contentFiles) {
    $content = Get-Content $file.FullName -Raw
    $modified = $false
    
    # Replace Shared.Domain.Primitives with Shared.Domain.Common
    if ($content -match "using Shared\.Domain\.Primitives;") {
        $content = $content -replace "using Shared\.Domain\.Primitives;", "using Shared.Domain.Common;"
        $modified = $true
        Write-Host "  Fixed Primitives reference in $($file.Name)" -ForegroundColor Cyan
    }
    
    if ($modified) {
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8
        Write-Host "    Updated $($file.Name)" -ForegroundColor Green
    }
}

Write-Host "Final critical issues fix completed!" -ForegroundColor Green
