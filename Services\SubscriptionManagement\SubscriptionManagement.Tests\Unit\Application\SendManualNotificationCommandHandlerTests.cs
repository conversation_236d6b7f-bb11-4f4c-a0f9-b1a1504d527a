using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using SubscriptionManagement.Application.Commands.SendManualNotification;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Interfaces;
using SubscriptionManagement.Domain.ValueObjects;
using Xunit;

namespace SubscriptionManagement.Tests.Unit.Application
{
    public class SendManualNotificationCommandHandlerTests
    {
        private readonly Mock<ISubscriptionRepository> _subscriptionRepositoryMock;
        private readonly Mock<IPlanRepository> _planRepositoryMock;
        private readonly Mock<INotificationTemplateService> _templateServiceMock;
        private readonly Mock<ICommunicationService> _communicationServiceMock;
        private readonly Mock<ILogger<SendManualNotificationCommandHandler>> _loggerMock;
        private readonly SendManualNotificationCommandHandler _handler;

        public SendManualNotificationCommandHandlerTests()
        {
            _subscriptionRepositoryMock = new Mock<ISubscriptionRepository>();
            _planRepositoryMock = new Mock<IPlanRepository>();
            _templateServiceMock = new Mock<INotificationTemplateService>();
            _communicationServiceMock = new Mock<ICommunicationService>();
            _loggerMock = new Mock<ILogger<SendManualNotificationCommandHandler>>();

            _handler = new SendManualNotificationCommandHandler(
                _subscriptionRepositoryMock.Object,
                _planRepositoryMock.Object,
                _templateServiceMock.Object,
                _communicationServiceMock.Object,
                _loggerMock.Object);
        }

        [Fact]
        public async Task Handle_WithValidSubscriptionId_ShouldSendNotificationSuccessfully()
        {
            // Arrange
            var subscriptionId = Guid.NewGuid();
            var userId = Guid.NewGuid();
            var planId = Guid.NewGuid();
            var triggeredByUserId = Guid.NewGuid();

            var subscription = CreateTestSubscription(subscriptionId, userId, planId);
            var plan = CreateTestPlan(planId);

            var command = new SendManualNotificationCommand
            {
                SubscriptionId = subscriptionId,
                NotificationType = NotificationType.SubscriptionReminder,
                Channels = new List<string> { "Email" },
                TriggeredByUserId = triggeredByUserId,
                Language = "en"
            };

            var renderedNotification = new RenderedNotification
            {
                Subject = "Test Subject",
                Body = "Test Body",
                Channel = "Email"
            };

            var notificationResult = new NotificationResult
            {
                Success = true,
                NotificationId = Guid.NewGuid().ToString(),
                Channel = "Email"
            };

            _subscriptionRepositoryMock.Setup(x => x.GetByIdAsync(subscriptionId))
                .ReturnsAsync(subscription);

            _planRepositoryMock.Setup(x => x.GetByIdAsync(planId))
                .ReturnsAsync(plan);

            _templateServiceMock.Setup(x => x.GetSubscriptionVariables(subscription, plan))
                .Returns(new Dictionary<string, string> { ["SubscriptionId"] = subscriptionId.ToString() });

            _templateServiceMock.Setup(x => x.GetUserVariables(userId, null, null))
                .Returns(new Dictionary<string, string> { ["UserId"] = userId.ToString() });

            _templateServiceMock.Setup(x => x.RenderTemplateAsync(
                NotificationType.SubscriptionReminder, "Email", It.IsAny<Dictionary<string, string>>(), "en", It.IsAny<CancellationToken>()))
                .ReturnsAsync(renderedNotification);

            _communicationServiceMock.Setup(x => x.SendNotificationAsync(It.IsAny<NotificationRequest>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(notificationResult);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.Channels.Should().Contain("Email");
            result.Message.Should().Contain("successfully");

            _communicationServiceMock.Verify(x => x.SendNotificationAsync(
                It.Is<NotificationRequest>(r => r.Channel == "Email" && r.Subject == "Test Subject"),
                It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task Handle_WithScheduledNotification_ShouldScheduleNotification()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var triggeredByUserId = Guid.NewGuid();
            var scheduledAt = DateTime.UtcNow.AddHours(1);

            var command = new SendManualNotificationCommand
            {
                UserId = userId,
                NotificationType = NotificationType.Custom,
                Channels = new List<string> { "Email" },
                TriggeredByUserId = triggeredByUserId,
                ScheduledAt = scheduledAt,
                Language = "en"
            };

            var renderedNotification = new RenderedNotification
            {
                Subject = "Scheduled Subject",
                Body = "Scheduled Body",
                Channel = "Email"
            };

            var notificationResult = new NotificationResult
            {
                Success = true,
                NotificationId = Guid.NewGuid().ToString(),
                Channel = "Email"
            };

            _templateServiceMock.Setup(x => x.GetUserVariables(userId, null, null))
                .Returns(new Dictionary<string, string> { ["UserId"] = userId.ToString() });

            _templateServiceMock.Setup(x => x.RenderTemplateAsync(
                NotificationType.Custom, "Email", It.IsAny<Dictionary<string, string>>(), "en", It.IsAny<CancellationToken>()))
                .ReturnsAsync(renderedNotification);

            _communicationServiceMock.Setup(x => x.ScheduleNotificationAsync(
                It.IsAny<NotificationRequest>(), scheduledAt, It.IsAny<CancellationToken>()))
                .ReturnsAsync(notificationResult);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.ScheduledAt.Should().Be(scheduledAt);

            _communicationServiceMock.Verify(x => x.ScheduleNotificationAsync(
                It.IsAny<NotificationRequest>(), scheduledAt, It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task Handle_WithoutSubscriptionIdOrUserId_ShouldReturnValidationError()
        {
            // Arrange
            var command = new SendManualNotificationCommand
            {
                NotificationType = NotificationType.SubscriptionReminder,
                Channels = new List<string> { "Email" },
                TriggeredByUserId = Guid.NewGuid(),
                Language = "en"
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeFalse();
            result.Errors.Should().Contain("Either SubscriptionId or UserId must be provided");
        }

        [Fact]
        public async Task Handle_WithEmptyChannels_ShouldReturnValidationError()
        {
            // Arrange
            var command = new SendManualNotificationCommand
            {
                UserId = Guid.NewGuid(),
                NotificationType = NotificationType.SubscriptionReminder,
                Channels = new List<string>(),
                TriggeredByUserId = Guid.NewGuid(),
                Language = "en"
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeFalse();
            result.Errors.Should().Contain("At least one notification channel must be specified");
        }

        [Fact]
        public async Task Handle_WithInvalidChannels_ShouldReturnValidationError()
        {
            // Arrange
            var command = new SendManualNotificationCommand
            {
                UserId = Guid.NewGuid(),
                NotificationType = NotificationType.SubscriptionReminder,
                Channels = new List<string> { "InvalidChannel" },
                TriggeredByUserId = Guid.NewGuid(),
                Language = "en"
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeFalse();
            result.Errors.Should().Contain(e => e.Contains("Invalid channels"));
        }

        [Fact]
        public async Task Handle_WithNonExistentSubscription_ShouldReturnValidationError()
        {
            // Arrange
            var subscriptionId = Guid.NewGuid();
            var command = new SendManualNotificationCommand
            {
                SubscriptionId = subscriptionId,
                NotificationType = NotificationType.SubscriptionReminder,
                Channels = new List<string> { "Email" },
                TriggeredByUserId = Guid.NewGuid(),
                Language = "en"
            };

            _subscriptionRepositoryMock.Setup(x => x.GetByIdAsync(subscriptionId))
                .ReturnsAsync((Subscription?)null);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeFalse();
            result.Errors.Should().Contain($"Subscription with ID {subscriptionId} not found");
        }

        [Fact]
        public async Task Handle_WithCommunicationServiceFailure_ShouldReturnPartialFailure()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var triggeredByUserId = Guid.NewGuid();

            var command = new SendManualNotificationCommand
            {
                UserId = userId,
                NotificationType = NotificationType.Custom,
                Channels = new List<string> { "Email", "SMS" },
                TriggeredByUserId = triggeredByUserId,
                Language = "en"
            };

            var renderedNotification = new RenderedNotification
            {
                Subject = "Test Subject",
                Body = "Test Body",
                Channel = "Email"
            };

            var successResult = new NotificationResult { Success = true, Channel = "Email" };
            var failureResult = new NotificationResult { Success = false, Channel = "SMS", ErrorMessage = "SMS service unavailable" };

            _templateServiceMock.Setup(x => x.GetUserVariables(userId, null, null))
                .Returns(new Dictionary<string, string> { ["UserId"] = userId.ToString() });

            _templateServiceMock.Setup(x => x.RenderTemplateAsync(
                It.IsAny<NotificationType>(), It.IsAny<string>(), It.IsAny<Dictionary<string, string>>(), "en", It.IsAny<CancellationToken>()))
                .ReturnsAsync(renderedNotification);

            _communicationServiceMock.SetupSequence(x => x.SendNotificationAsync(It.IsAny<NotificationRequest>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(successResult)
                .ReturnsAsync(failureResult);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue(); // At least one channel succeeded
            result.Channels.Should().Contain("Email");
            result.Errors.Should().Contain("SMS: Failed to send notification: SMS service unavailable");
        }

        private Subscription CreateTestSubscription(Guid subscriptionId, Guid userId, Guid planId)
        {
            return Subscription.Create(
                userId,
                planId,
                new Money(1000, "INR"),
                new BillingCycle(BillingInterval.Monthly, 1),
                DateTime.UtcNow,
                DateTime.UtcNow.AddDays(30),
                true);
        }

        private Plan CreateTestPlan(Guid planId)
        {
            return Plan.Create(
                "Test Plan",
                "Test Description",
                PlanType.Standard,
                UserType.Transporter,
                new Money(1000, "INR"),
                new BillingCycle(BillingInterval.Monthly, 1),
                true);
        }
    }
}
