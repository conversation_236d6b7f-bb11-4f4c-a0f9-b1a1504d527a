using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using NetworkFleetManagement.Domain.Repositories;
using System.Security.Claims;

namespace NetworkFleetManagement.Infrastructure.Security.Authorization;

public class FleetAuthorizationHandler : AuthorizationHandler<FleetOperationRequirement, FleetResource>
{
    private readonly ICarrierRepository _carrierRepository;
    private readonly IVehicleRepository _vehicleRepository;
    private readonly IDriverRepository _driverRepository;
    private readonly ILogger<FleetAuthorizationHandler> _logger;

    public FleetAuthorizationHandler(
        ICarrierRepository carrierRepository,
        IVehicleRepository vehicleRepository,
        IDriverRepository driverRepository,
        ILogger<FleetAuthorizationHandler> logger)
    {
        _carrierRepository = carrierRepository;
        _vehicleRepository = vehicleRepository;
        _driverRepository = driverRepository;
        _logger = logger;
    }

    protected override async Task HandleRequirementAsync(
        AuthorizationHandlerContext context,
        FleetOperationRequirement requirement,
        FleetResource resource)
    {
        try
        {
            var user = context.User;
            var userId = GetUserId(user);
            var userRole = GetUserRole(user);
            var userCarrierId = GetUserCarrierId(user);

            _logger.LogDebug("Authorizing {Operation} on {ResourceType} {ResourceId} for user {UserId} with role {Role}",
                requirement.Operation, resource.ResourceType, resource.ResourceId, userId, userRole);

            // Super admin can do everything
            if (userRole == "SuperAdmin")
            {
                context.Succeed(requirement);
                return;
            }

            // Check based on resource type
            var isAuthorized = resource.ResourceType switch
            {
                "Carrier" => await AuthorizeCarrierOperationAsync(requirement, resource, user, userCarrierId, userRole),
                "Vehicle" => await AuthorizeVehicleOperationAsync(requirement, resource, user, userCarrierId, userRole),
                "Driver" => await AuthorizeDriverOperationAsync(requirement, resource, user, userCarrierId, userRole),
                "Network" => await AuthorizeNetworkOperationAsync(requirement, resource, user, userCarrierId, userRole),
                _ => false
            };

            if (isAuthorized)
            {
                context.Succeed(requirement);
                _logger.LogDebug("Authorization succeeded for {Operation} on {ResourceType} {ResourceId}",
                    requirement.Operation, resource.ResourceType, resource.ResourceId);
            }
            else
            {
                context.Fail();
                _logger.LogWarning("Authorization failed for {Operation} on {ResourceType} {ResourceId} for user {UserId}",
                    requirement.Operation, resource.ResourceType, resource.ResourceId, userId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during authorization for {Operation} on {ResourceType} {ResourceId}",
                requirement.Operation, resource.ResourceType, resource.ResourceId);
            context.Fail();
        }
    }

    private async Task<bool> AuthorizeCarrierOperationAsync(
        FleetOperationRequirement requirement,
        FleetResource resource,
        ClaimsPrincipal user,
        Guid? userCarrierId,
        string userRole)
    {
        // Admin users can manage any carrier
        if (userRole == "Admin")
        {
            return true;
        }

        // Carrier users can only access their own carrier
        if (userRole == "CarrierAdmin" || userRole == "CarrierUser")
        {
            return userCarrierId == resource.ResourceId;
        }

        return false;
    }

    private async Task<bool> AuthorizeVehicleOperationAsync(
        FleetOperationRequirement requirement,
        FleetResource resource,
        ClaimsPrincipal user,
        Guid? userCarrierId,
        string userRole)
    {
        // Admin users can manage any vehicle
        if (userRole == "Admin")
        {
            return true;
        }

        // Get vehicle to check carrier ownership
        var vehicle = await _vehicleRepository.GetByIdAsync(resource.ResourceId);
        if (vehicle == null)
        {
            return false;
        }

        // Carrier users can only access vehicles in their carrier
        if (userRole == "CarrierAdmin" || userRole == "CarrierUser")
        {
            if (userCarrierId != vehicle.CarrierId)
            {
                return false;
            }

            // CarrierUser has limited permissions
            if (userRole == "CarrierUser")
            {
                return requirement.Operation switch
                {
                    "Read" => true,
                    "UpdateLocation" => true,
                    "UpdateStatus" => true,
                    _ => false
                };
            }

            return true; // CarrierAdmin can do everything with their vehicles
        }

        // Driver users can only read vehicles they're assigned to
        if (userRole == "Driver")
        {
            var userId = GetUserId(user);
            if (userId == null) return false;

            // Check if driver is assigned to this vehicle
            var assignments = await _vehicleRepository.GetDriverAssignmentsAsync(resource.ResourceId);
            return assignments.Any(a => a.DriverId == userId && a.IsActive) && requirement.Operation == "Read";
        }

        return false;
    }

    private async Task<bool> AuthorizeDriverOperationAsync(
        FleetOperationRequirement requirement,
        FleetResource resource,
        ClaimsPrincipal user,
        Guid? userCarrierId,
        string userRole)
    {
        // Admin users can manage any driver
        if (userRole == "Admin")
        {
            return true;
        }

        // Get driver to check carrier ownership
        var driver = await _driverRepository.GetByIdAsync(resource.ResourceId);
        if (driver == null)
        {
            return false;
        }

        // Carrier users can only access drivers in their carrier
        if (userRole == "CarrierAdmin" || userRole == "CarrierUser")
        {
            if (userCarrierId != driver.CarrierId)
            {
                return false;
            }

            // CarrierUser has limited permissions
            if (userRole == "CarrierUser")
            {
                return requirement.Operation switch
                {
                    "Read" => true,
                    _ => false
                };
            }

            return true; // CarrierAdmin can do everything with their drivers
        }

        // Driver users can only access their own profile
        if (userRole == "Driver")
        {
            var userId = GetUserId(user);
            if (userId != resource.ResourceId)
            {
                return false;
            }

            return requirement.Operation switch
            {
                "Read" => true,
                "UpdateStatus" => true,
                "UpdateLocation" => true,
                "UpdateProfile" => true,
                _ => false
            };
        }

        return false;
    }

    private async Task<bool> AuthorizeNetworkOperationAsync(
        FleetOperationRequirement requirement,
        FleetResource resource,
        ClaimsPrincipal user,
        Guid? userCarrierId,
        string userRole)
    {
        // Admin users can manage any network
        if (userRole == "Admin")
        {
            return true;
        }

        // Broker users can manage networks they created
        if (userRole == "Broker")
        {
            // This would require checking if the user is the broker for this network
            // For now, allow brokers to manage any network
            return true;
        }

        // Carrier users can only read networks they're part of
        if (userRole == "CarrierAdmin" || userRole == "CarrierUser")
        {
            // This would require checking if the carrier is part of this network
            // For now, allow read access only
            return requirement.Operation == "Read";
        }

        return false;
    }

    private static Guid? GetUserId(ClaimsPrincipal user)
    {
        var userIdClaim = user.FindFirst(ClaimTypes.NameIdentifier)?.Value ??
                         user.FindFirst("sub")?.Value ??
                         user.FindFirst("userId")?.Value;

        return Guid.TryParse(userIdClaim, out var userId) ? userId : null;
    }

    private static string GetUserRole(ClaimsPrincipal user)
    {
        return user.FindFirst(ClaimTypes.Role)?.Value ?? "Unknown";
    }

    private static Guid? GetUserCarrierId(ClaimsPrincipal user)
    {
        var carrierIdClaim = user.FindFirst("carrierId")?.Value;
        return Guid.TryParse(carrierIdClaim, out var carrierId) ? carrierId : null;
    }
}

public class FleetOperationRequirement : IAuthorizationRequirement
{
    public string Operation { get; }

    public FleetOperationRequirement(string operation)
    {
        Operation = operation;
    }
}

public class FleetResource
{
    public Guid ResourceId { get; }
    public string ResourceType { get; }
    public Dictionary<string, object> Context { get; }

    public FleetResource(Guid resourceId, string resourceType, Dictionary<string, object>? context = null)
    {
        ResourceId = resourceId;
        ResourceType = resourceType;
        Context = context ?? new Dictionary<string, object>();
    }
}

public static class FleetOperations
{
    public const string Create = "Create";
    public const string Read = "Read";
    public const string Update = "Update";
    public const string Delete = "Delete";
    public const string UpdateLocation = "UpdateLocation";
    public const string UpdateStatus = "UpdateStatus";
    public const string UpdateProfile = "UpdateProfile";
    public const string ManageMaintenance = "ManageMaintenance";
    public const string ViewAnalytics = "ViewAnalytics";
    public const string ManageDocuments = "ManageDocuments";
}

public static class FleetAuthorizationExtensions
{
    public static IServiceCollection AddFleetAuthorization(this IServiceCollection services)
    {
        services.AddScoped<IAuthorizationHandler, FleetAuthorizationHandler>();
        
        services.AddAuthorization(options =>
        {
            options.AddPolicy("FleetRead", policy =>
                policy.Requirements.Add(new FleetOperationRequirement(FleetOperations.Read)));
            
            options.AddPolicy("FleetWrite", policy =>
                policy.Requirements.Add(new FleetOperationRequirement(FleetOperations.Update)));
            
            options.AddPolicy("FleetAdmin", policy =>
                policy.Requirements.Add(new FleetOperationRequirement(FleetOperations.Delete)));
        });

        return services;
    }
}
