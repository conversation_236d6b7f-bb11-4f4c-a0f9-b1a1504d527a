using Microsoft.EntityFrameworkCore;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Entities;
using OrderManagement.Domain.Enums;
using OrderManagement.Infrastructure.Persistence;

namespace OrderManagement.Infrastructure.Repositories;

public class AdministrativeAuditRepository : IAdministrativeAuditRepository
{
    private readonly OrderManagementDbContext _context;

    public AdministrativeAuditRepository(OrderManagementDbContext context)
    {
        _context = context;
    }

    public async Task<AdministrativeAuditLog?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.AdministrativeAuditLogs
            .FirstOrDefaultAsync(aal => aal.Id == id, cancellationToken);
    }

    public async Task<List<AdministrativeAuditLog>> GetByAdminUserIdAsync(Guid adminUserId, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default)
    {
        var query = _context.AdministrativeAuditLogs
            .Where(aal => aal.AdminUserId == adminUserId);

        if (fromDate.HasValue)
        {
            query = query.Where(aal => aal.ActionTimestamp >= fromDate.Value);
        }

        if (toDate.HasValue)
        {
            query = query.Where(aal => aal.ActionTimestamp <= toDate.Value);
        }

        return await query
            .OrderByDescending(aal => aal.ActionTimestamp)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<AdministrativeAuditLog>> GetByActionAsync(AdministrativeAction action, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default)
    {
        var query = _context.AdministrativeAuditLogs
            .Where(aal => aal.Action == action);

        if (fromDate.HasValue)
        {
            query = query.Where(aal => aal.ActionTimestamp >= fromDate.Value);
        }

        if (toDate.HasValue)
        {
            query = query.Where(aal => aal.ActionTimestamp <= toDate.Value);
        }

        return await query
            .OrderByDescending(aal => aal.ActionTimestamp)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<AdministrativeAuditLog>> GetBySeverityAsync(AuditSeverity severity, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default)
    {
        var query = _context.AdministrativeAuditLogs
            .Where(aal => aal.Severity == severity);

        if (fromDate.HasValue)
        {
            query = query.Where(aal => aal.ActionTimestamp >= fromDate.Value);
        }

        if (toDate.HasValue)
        {
            query = query.Where(aal => aal.ActionTimestamp <= toDate.Value);
        }

        return await query
            .OrderByDescending(aal => aal.ActionTimestamp)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<AdministrativeAuditLog>> GetSecurityViolationsAsync(DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default)
    {
        var query = _context.AdministrativeAuditLogs
            .Where(aal => aal.Action == AdministrativeAction.SecurityViolation);

        if (fromDate.HasValue)
        {
            query = query.Where(aal => aal.ActionTimestamp >= fromDate.Value);
        }

        if (toDate.HasValue)
        {
            query = query.Where(aal => aal.ActionTimestamp <= toDate.Value);
        }

        return await query
            .OrderByDescending(aal => aal.ActionTimestamp)
            .ToListAsync(cancellationToken);
    }

    public async Task<(List<AdministrativeAuditLog> Logs, int TotalCount)> GetPagedAsync(
        int page,
        int pageSize,
        Guid? adminUserId = null,
        AdministrativeAction? action = null,
        AuditSeverity? severity = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.AdministrativeAuditLogs.AsQueryable();

        if (adminUserId.HasValue)
        {
            query = query.Where(aal => aal.AdminUserId == adminUserId.Value);
        }

        if (action.HasValue)
        {
            query = query.Where(aal => aal.Action == action.Value);
        }

        if (severity.HasValue)
        {
            query = query.Where(aal => aal.Severity == severity.Value);
        }

        if (fromDate.HasValue)
        {
            query = query.Where(aal => aal.ActionTimestamp >= fromDate.Value);
        }

        if (toDate.HasValue)
        {
            query = query.Where(aal => aal.ActionTimestamp <= toDate.Value);
        }

        var totalCount = await query.CountAsync(cancellationToken);

        var logs = await query
            .OrderByDescending(aal => aal.ActionTimestamp)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return (logs, totalCount);
    }

    public async Task AddAsync(AdministrativeAuditLog auditLog, CancellationToken cancellationToken = default)
    {
        await _context.AdministrativeAuditLogs.AddAsync(auditLog, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public void Update(AdministrativeAuditLog auditLog)
    {
        _context.AdministrativeAuditLogs.Update(auditLog);
    }

    public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var auditLog = await GetByIdAsync(id, cancellationToken);
        if (auditLog != null)
        {
            _context.AdministrativeAuditLogs.Remove(auditLog);
            await _context.SaveChangesAsync(cancellationToken);
        }
    }
}
