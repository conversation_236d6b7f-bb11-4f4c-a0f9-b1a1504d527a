using Shared.Domain.ValueObjects;

namespace DataStorage.Domain.ValueObjects;

/// <summary>
/// Request for benchmark operations
/// </summary>
public class BenchmarkRequest : ValueObject
{
    public string Name { get; init; }
    public string Description { get; init; }
    public string BenchmarkType { get; init; }
    public Dictionary<string, object> Parameters { get; init; }
    public int Duration { get; init; }
    public string? Environment { get; init; }
    public List<string>? Tags { get; init; }

    public BenchmarkRequest(
        string name,
        string description,
        string benchmarkType,
        Dictionary<string, object> parameters,
        int duration,
        string? environment = null,
        List<string>? tags = null)
    {
        Name = name;
        Description = description;
        BenchmarkType = benchmarkType;
        Parameters = parameters;
        Duration = duration;
        Environment = environment;
        Tags = tags;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Name;
        yield return Description;
        yield return BenchmarkType;
        yield return Duration;
        yield return Environment ?? string.Empty;
        
        foreach (var param in Parameters.OrderBy(x => x.Key))
        {
            yield return param.Key;
            yield return param.Value?.ToString() ?? string.Empty;
        }
        
        if (Tags != null)
        {
            foreach (var tag in Tags.OrderBy(x => x))
            {
                yield return tag;
            }
        }
    }
}
