using Shared.Domain.Common;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Domain.Entities;

public class PreferredPartner : BaseEntity
{
    public Guid RfqId { get; private set; }
    public Guid PartnerId { get; private set; }
    public PartnerType PartnerType { get; private set; }
    public string PartnerName { get; private set; } = string.Empty;
    public string CompanyName { get; private set; } = string.Empty;
    public string Email { get; private set; } = string.Empty;
    public string PhoneNumber { get; private set; } = string.Empty;
    public decimal PreferenceRating { get; private set; }
    public DateTime AddedAt { get; private set; }
    public Guid AddedBy { get; private set; }
    public string? Notes { get; private set; }
    public bool IsActive { get; private set; }
    public int Priority { get; private set; }

    // Navigation properties
    public RFQ Rfq { get; private set; } = null!;

    private readonly List<string> _serviceAreas = new();
    public IReadOnlyCollection<string> ServiceAreas => _serviceAreas.AsReadOnly();

    private readonly List<string> _vehicleTypes = new();
    public IReadOnlyCollection<string> VehicleTypes => _vehicleTypes.AsReadOnly();

    private PreferredPartner() { } // EF Constructor

    public PreferredPartner(
        Guid rfqId,
        Guid partnerId,
        PartnerType partnerType,
        string partnerName,
        string companyName,
        string email,
        string phoneNumber,
        decimal preferenceRating,
        Guid addedBy,
        string? notes = null,
        int priority = 1)
    {
        if (string.IsNullOrWhiteSpace(partnerName))
            throw new ArgumentException("Partner name cannot be empty", nameof(partnerName));

        if (string.IsNullOrWhiteSpace(companyName))
            throw new ArgumentException("Company name cannot be empty", nameof(companyName));

        if (string.IsNullOrWhiteSpace(email))
            throw new ArgumentException("Email cannot be empty", nameof(email));

        if (preferenceRating < 0 || preferenceRating > 5)
            throw new ArgumentException("Preference rating must be between 0 and 5", nameof(preferenceRating));

        RfqId = rfqId;
        PartnerId = partnerId;
        PartnerType = partnerType;
        PartnerName = partnerName ?? throw new ArgumentNullException(nameof(partnerName));
        CompanyName = companyName ?? throw new ArgumentNullException(nameof(companyName));
        Email = email ?? throw new ArgumentNullException(nameof(email));
        PhoneNumber = phoneNumber ?? throw new ArgumentNullException(nameof(phoneNumber));
        PreferenceRating = preferenceRating;
        AddedAt = DateTime.UtcNow;
        AddedBy = addedBy;
        Notes = notes;
        IsActive = true;
        Priority = priority;
    }

    public void UpdateRating(decimal newRating)
    {
        if (newRating < 0 || newRating > 5)
            throw new ArgumentException("Rating must be between 0 and 5", nameof(newRating));

        PreferenceRating = newRating;
    }

    public void UpdatePriority(int newPriority)
    {
        if (newPriority < 1)
            throw new ArgumentException("Priority must be greater than 0", nameof(newPriority));

        Priority = newPriority;
    }

    public void UpdateNotes(string? notes)
    {
        Notes = notes;
    }

    public void Deactivate()
    {
        IsActive = false;
    }

    public void Activate()
    {
        IsActive = true;
    }

    public void AddServiceArea(string serviceArea)
    {
        if (string.IsNullOrWhiteSpace(serviceArea))
            throw new ArgumentException("Service area cannot be empty", nameof(serviceArea));

        if (!_serviceAreas.Contains(serviceArea))
        {
            _serviceAreas.Add(serviceArea);
        }
    }

    public void RemoveServiceArea(string serviceArea)
    {
        _serviceAreas.Remove(serviceArea);
    }

    public void AddVehicleType(string vehicleType)
    {
        if (string.IsNullOrWhiteSpace(vehicleType))
            throw new ArgumentException("Vehicle type cannot be empty", nameof(vehicleType));

        if (!_vehicleTypes.Contains(vehicleType))
        {
            _vehicleTypes.Add(vehicleType);
        }
    }

    public void RemoveVehicleType(string vehicleType)
    {
        _vehicleTypes.Remove(vehicleType);
    }

    public void ClearServiceAreas()
    {
        _serviceAreas.Clear();
    }

    public void ClearVehicleTypes()
    {
        _vehicleTypes.Clear();
    }

    public bool SupportsServiceArea(string serviceArea)
    {
        return _serviceAreas.Contains(serviceArea, StringComparer.OrdinalIgnoreCase);
    }

    public bool SupportsVehicleType(string vehicleType)
    {
        return _vehicleTypes.Contains(vehicleType, StringComparer.OrdinalIgnoreCase);
    }
}

public enum PartnerType
{
    Transporter = 1,
    Broker = 2
}
