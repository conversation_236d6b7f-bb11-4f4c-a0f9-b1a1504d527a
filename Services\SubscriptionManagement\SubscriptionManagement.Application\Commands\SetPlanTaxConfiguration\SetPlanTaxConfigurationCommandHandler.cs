using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Domain.Common;
using Shared.Messaging;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Exceptions;
using SubscriptionManagement.Domain.Interfaces;
using SubscriptionManagement.Domain.Services;
using SubscriptionManagement.Domain.ValueObjects;

namespace SubscriptionManagement.Application.Commands.SetPlanTaxConfiguration
{
    public class SetPlanTaxConfigurationCommandHandler : IRequestHandler<SetPlanTaxConfigurationCommand, Guid>
    {
        private readonly IPlanRepository _planRepository;
        private readonly IPlanTaxConfigurationRepository _planTaxConfigurationRepository;
        private readonly IMessageBroker _messageBroker;
        private readonly ILogger<SetPlanTaxConfigurationCommandHandler> _logger;

        public SetPlanTaxConfigurationCommandHandler(
            IPlanRepository planRepository,
            IPlanTaxConfigurationRepository planTaxConfigurationRepository,
            IMessageBroker messageBroker,
            ILogger<SetPlanTaxConfigurationCommandHandler> logger)
        {
            _planRepository = planRepository;
            _planTaxConfigurationRepository = planTaxConfigurationRepository;
            _messageBroker = messageBroker;
            _logger = logger;
        }

        public async Task<Guid> Handle(SetPlanTaxConfigurationCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Setting tax configuration for plan {PlanId} with {TaxType} at {Rate}% for regions {Regions}",
                request.PlanId, request.TaxType, request.Rate, string.Join(", ", request.ApplicableRegions));

            try
            {
                // Get the plan
                var plan = await _planRepository.GetByIdAsync(request.PlanId);
                if (plan == null)
                {
                    throw new SubscriptionDomainException($"Plan with ID {request.PlanId} not found");
                }

                if (!plan.IsActive)
                {
                    throw new SubscriptionDomainException("Cannot set tax configuration for inactive plan");
                }

                // Create tax configuration value object
                var taxConfiguration = TaxConfiguration.Create(
                    request.TaxType,
                    request.Rate,
                    request.IsIncluded,
                    request.ApplicableRegions,
                    request.EffectiveDate,
                    request.ExpirationDate);

                // Validate tax configuration
                foreach (var region in request.ApplicableRegions)
                {
                    TaxDomainService.ValidateTaxConfiguration(taxConfiguration, region);
                }

                // Check if configuration already exists for this plan and tax type
                var existingConfig = await _planTaxConfigurationRepository
                    .GetByPlanIdAndTaxTypeAsync(request.PlanId, request.TaxType);

                PlanTaxConfiguration planTaxConfig;

                if (existingConfig != null)
                {
                    // Update existing configuration
                    existingConfig.UpdateTaxConfiguration(taxConfiguration, request.Notes);
                    planTaxConfig = await _planTaxConfigurationRepository.UpdateAsync(existingConfig);

                    _logger.LogInformation("Updated existing plan tax configuration {Id} for plan {PlanId}",
                        existingConfig.Id, request.PlanId);
                }
                else
                {
                    // Create new configuration
                    planTaxConfig = new PlanTaxConfiguration(
                        request.PlanId,
                        plan,
                        taxConfiguration,
                        request.EffectiveDate,
                        request.Notes);

                    planTaxConfig = await _planTaxConfigurationRepository.AddAsync(planTaxConfig);

                    _logger.LogInformation("Created new plan tax configuration {Id} for plan {PlanId}",
                        planTaxConfig.Id, request.PlanId);
                }

                // Update the plan entity with tax configuration
                plan.AddTaxConfiguration(taxConfiguration);
                await _planRepository.UpdateAsync(plan);

                // Publish integration event
                await _messageBroker.PublishAsync("tax.plan_configuration_changed", new
                {
                    ConfigurationId = planTaxConfig.Id,
                    PlanId = request.PlanId,
                    PlanName = plan.Name,
                    TaxType = request.TaxType.ToString(),
                    Rate = request.Rate,
                    IsIncluded = request.IsIncluded,
                    ApplicableRegions = request.ApplicableRegions,
                    EffectiveDate = request.EffectiveDate,
                    ExpirationDate = request.ExpirationDate,
                    CreatedByUserId = request.CreatedByUserId,
                    CreatedAt = DateTime.UtcNow
                }, cancellationToken);

                _logger.LogInformation("Successfully set tax configuration {Id} for plan {PlanId}",
                    planTaxConfig.Id, request.PlanId);

                return planTaxConfig.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting tax configuration for plan {PlanId}", request.PlanId);
                throw;
            }
        }
    }
}

