using Shared.Domain.Common;
using UserManagement.Domain.Enums;
using UserManagement.Domain.Events;

namespace UserManagement.Domain.Entities;

/// <summary>
/// Entity representing user flags for compliance and risk management
/// </summary>
public class UserFlag : AggregateRoot
{
    public Guid UserId { get; private set; }
    public FlagType FlagType { get; private set; }
    public FlagSeverity Severity { get; private set; }
    public string Reason { get; private set; } = string.Empty;
    public string Description { get; private set; } = string.Empty;
    public string Category { get; private set; } = string.Empty; // Compliance, Behavior, Financial, etc.
    public FlagStatus Status { get; private set; }
    public DateTime FlaggedAt { get; private set; }
    public Guid FlaggedBy { get; private set; }
    public string FlaggedByRole { get; private set; } = string.Empty;
    public DateTime? ResolvedAt { get; private set; }
    public Guid? ResolvedBy { get; private set; }
    public string? ResolutionNotes { get; private set; }
    public DateTime? ExpiresAt { get; private set; }
    public bool IsAutoGenerated { get; private set; }
    public string? SourceSystem { get; private set; }
    public string? SourceReference { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; } = new();

    // Navigation properties
    public UserProfile User { get; private set; } = null!;

    // Private constructor for EF Core
    private UserFlag() { }

    public UserFlag(
        Guid userId,
        FlagType flagType,
        FlagSeverity severity,
        string reason,
        string description,
        string category,
        Guid flaggedBy,
        string flaggedByRole,
        DateTime? expiresAt = null,
        bool isAutoGenerated = false,
        string? sourceSystem = null,
        string? sourceReference = null,
        Dictionary<string, object>? metadata = null)
    {
        if (string.IsNullOrWhiteSpace(reason))
            throw new ArgumentException("Flag reason cannot be empty", nameof(reason));

        if (string.IsNullOrWhiteSpace(category))
            throw new ArgumentException("Flag category cannot be empty", nameof(category));

        UserId = userId;
        FlagType = flagType;
        Severity = severity;
        Reason = reason.Trim();
        Description = description?.Trim() ?? string.Empty;
        Category = category.Trim();
        Status = FlagStatus.Active;
        FlaggedAt = DateTime.UtcNow;
        FlaggedBy = flaggedBy;
        FlaggedByRole = flaggedByRole?.Trim() ?? string.Empty;
        ExpiresAt = expiresAt;
        IsAutoGenerated = isAutoGenerated;
        SourceSystem = sourceSystem?.Trim();
        SourceReference = sourceReference?.Trim();
        Metadata = metadata ?? new Dictionary<string, object>();

        // Add domain event
        AddDomainEvent(new UserFlaggedEvent(UserId, Id, FlagType.ToString(), Reason, Severity.ToString(), flaggedBy));
    }

    public void Resolve(Guid resolvedBy, string resolutionNotes)
    {
        if (Status == FlagStatus.Resolved)
            throw new InvalidOperationException("Flag is already resolved");

        Status = FlagStatus.Resolved;
        ResolvedAt = DateTime.UtcNow;
        ResolvedBy = resolvedBy;
        ResolutionNotes = resolutionNotes?.Trim();

        SetUpdatedAt();

        // Add domain event
        AddDomainEvent(new UserFlagResolvedEvent(UserId, Id, resolutionNotes, resolvedBy, DateTime.UtcNow));
    }

    public void Escalate(FlagSeverity newSeverity, string escalationReason, Guid escalatedBy, Guid escalatedTo)
    {
        if (newSeverity <= Severity)
            throw new ArgumentException("New severity must be higher than current severity");

        var oldSeverity = Severity;
        Severity = newSeverity;

        // Add escalation to metadata
        var escalationInfo = new
        {
            PreviousSeverity = oldSeverity.ToString(),
            NewSeverity = newSeverity.ToString(),
            EscalationReason = escalationReason,
            EscalatedAt = DateTime.UtcNow
        };

        Metadata[$"Escalation_{DateTime.UtcNow:yyyyMMddHHmmss}"] = escalationInfo;
        SetUpdatedAt();

        // Add domain event
        AddDomainEvent(new UserFlagEscalatedEvent(UserId, Id, escalationReason, escalatedBy, escalatedTo, DateTime.UtcNow));
    }

    public void UpdateExpiry(DateTime? newExpiryDate)
    {
        ExpiresAt = newExpiryDate;
        SetUpdatedAt();
    }

    public void AddMetadata(string key, object value)
    {
        if (string.IsNullOrWhiteSpace(key))
            throw new ArgumentException("Metadata key cannot be empty", nameof(key));

        Metadata[key] = value;
        SetUpdatedAt();
    }

    public bool IsExpired()
    {
        return ExpiresAt.HasValue && ExpiresAt.Value <= DateTime.UtcNow;
    }

    public bool IsActive()
    {
        return Status == FlagStatus.Active && !IsExpired();
    }

    public bool IsHighRisk()
    {
        return Severity == FlagSeverity.Critical || Severity == FlagSeverity.High;
    }

    public TimeSpan GetAge()
    {
        return DateTime.UtcNow - FlaggedAt;
    }

    public bool RequiresImmediateAction()
    {
        return Severity == FlagSeverity.Critical && Status == FlagStatus.Active;
    }
}
