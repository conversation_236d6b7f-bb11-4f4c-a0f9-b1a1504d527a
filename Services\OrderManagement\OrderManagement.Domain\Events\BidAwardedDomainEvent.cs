using Shared.Domain.Common;
using OrderManagement.Domain.ValueObjects;

namespace OrderManagement.Domain.Events;

public class BidAwardedDomainEvent : DomainEvent
{
    public Guid BidId { get; }
    public Guid RfqId { get; }
    public Guid BrokerId { get; }
    public Money QuotedPrice { get; }

    public BidAwardedDomainEvent(Guid bidId, Guid rfqId, Guid brokerId, Money quotedPrice)
    {
        BidId = bidId;
        RfqId = rfqId;
        BrokerId = brokerId;
        QuotedPrice = quotedPrice;
    }
}
