using FluentValidation;

namespace OrderManagement.Application.Commands.CreateRfq;

public class CreateRfqCommandValidator : AbstractValidator<CreateRfqCommand>
{
    public CreateRfqCommandValidator()
    {
        RuleFor(x => x.TransportCompanyId)
            .NotEmpty()
            .WithMessage("Transport company ID is required");

        RuleFor(x => x.Title)
            .NotEmpty()
            .WithMessage("Title is required")
            .MaximumLength(200)
            .WithMessage("Title cannot exceed 200 characters");

        RuleFor(x => x.Description)
            .NotEmpty()
            .WithMessage("Description is required")
            .MaximumLength(2000)
            .WithMessage("Description cannot exceed 2000 characters");

        RuleFor(x => x.LoadDetails)
            .NotNull()
            .WithMessage("Load details are required")
            .SetValidator(new CreateLoadDetailsDtoValidator());

        RuleFor(x => x.RouteDetails)
            .NotNull()
            .WithMessage("Route details are required")
            .SetValidator(new CreateRouteDetailsDtoValidator());

        RuleFor(x => x.Requirements)
            .NotNull()
            .WithMessage("Requirements are required")
            .SetValidator(new CreateRfqRequirementsDtoValidator());

        RuleFor(x => x.ExpiresAt)
            .GreaterThan(DateTime.UtcNow)
            .When(x => x.ExpiresAt.HasValue)
            .WithMessage("Expiration date must be in the future");

        RuleFor(x => x.BudgetRange)
            .SetValidator(new CreateMoneyDtoValidator()!)
            .When(x => x.BudgetRange != null);

        RuleFor(x => x.ContactEmail)
            .EmailAddress()
            .When(x => !string.IsNullOrEmpty(x.ContactEmail))
            .WithMessage("Invalid email format");
    }
}

public class CreateLoadDetailsDtoValidator : AbstractValidator<CreateLoadDetailsDto>
{
    public CreateLoadDetailsDtoValidator()
    {
        RuleFor(x => x.Description)
            .NotEmpty()
            .WithMessage("Load description is required")
            .MaximumLength(1000)
            .WithMessage("Load description cannot exceed 1000 characters");

        RuleFor(x => x.Weight)
            .NotNull()
            .WithMessage("Weight is required")
            .SetValidator(new CreateWeightDtoValidator());

        RuleFor(x => x.Quantity)
            .GreaterThan(0)
            .WithMessage("Quantity must be greater than zero");

        RuleFor(x => x.Volume)
            .SetValidator(new CreateVolumeDtoValidator()!)
            .When(x => x.Volume != null);

        RuleFor(x => x.Dimensions)
            .SetValidator(new CreateDimensionsDtoValidator()!)
            .When(x => x.Dimensions != null);

        RuleFor(x => x.TemperatureRange)
            .SetValidator(new CreateTemperatureRangeDtoValidator()!)
            .When(x => x.TemperatureRange != null);

        RuleFor(x => x.HazardousClassification)
            .NotEmpty()
            .When(x => x.IsHazardous)
            .WithMessage("Hazardous classification is required for hazardous loads");

        RuleFor(x => x.TemperatureRange)
            .NotNull()
            .When(x => x.RequiresTemperatureControl)
            .WithMessage("Temperature range is required for temperature-controlled loads");
    }
}

public class CreateRouteDetailsDtoValidator : AbstractValidator<CreateRouteDetailsDto>
{
    public CreateRouteDetailsDtoValidator()
    {
        RuleFor(x => x.PickupAddress)
            .NotNull()
            .WithMessage("Pickup address is required")
            .SetValidator(new CreateAddressDtoValidator());

        RuleFor(x => x.DeliveryAddress)
            .NotNull()
            .WithMessage("Delivery address is required")
            .SetValidator(new CreateAddressDtoValidator());

        RuleFor(x => x.PreferredDeliveryDate)
            .GreaterThan(x => x.PreferredPickupDate)
            .WithMessage("Delivery date must be after pickup date");

        RuleFor(x => x.PreferredPickupDate)
            .GreaterThanOrEqualTo(DateTime.UtcNow.Date)
            .WithMessage("Pickup date cannot be in the past");

        RuleFor(x => x.PickupTimeWindow)
            .SetValidator(new CreateTimeWindowDtoValidator()!)
            .When(x => x.PickupTimeWindow != null);

        RuleFor(x => x.DeliveryTimeWindow)
            .SetValidator(new CreateTimeWindowDtoValidator()!)
            .When(x => x.DeliveryTimeWindow != null);

        RuleFor(x => x.EstimatedDistance)
            .SetValidator(new CreateDistanceDtoValidator()!)
            .When(x => x.EstimatedDistance != null);
    }
}

public class CreateRfqRequirementsDtoValidator : AbstractValidator<CreateRfqRequirementsDto>
{
    public CreateRfqRequirementsDtoValidator()
    {
        RuleFor(x => x.MinVehicleCapacity)
            .GreaterThan(0)
            .When(x => x.MinVehicleCapacity.HasValue)
            .WithMessage("Minimum vehicle capacity must be greater than zero");

        RuleFor(x => x.MinInsuranceAmount)
            .GreaterThan(0)
            .When(x => x.MinInsuranceAmount.HasValue)
            .WithMessage("Minimum insurance amount must be greater than zero");

        RuleFor(x => x.MinYearsExperience)
            .GreaterThan(0)
            .When(x => x.MinYearsExperience.HasValue)
            .WithMessage("Minimum years of experience must be greater than zero");

        RuleFor(x => x.TemperatureRange)
            .SetValidator(new CreateTemperatureRangeDtoValidator()!)
            .When(x => x.TemperatureRange != null);
    }
}

public class CreateAddressDtoValidator : AbstractValidator<CreateAddressDto>
{
    public CreateAddressDtoValidator()
    {
        RuleFor(x => x.Street)
            .NotEmpty()
            .WithMessage("Street is required");

        RuleFor(x => x.City)
            .NotEmpty()
            .WithMessage("City is required");

        RuleFor(x => x.Country)
            .NotEmpty()
            .WithMessage("Country is required");

        RuleFor(x => x.Latitude)
            .InclusiveBetween(-90, 90)
            .When(x => x.Latitude.HasValue)
            .WithMessage("Latitude must be between -90 and 90");

        RuleFor(x => x.Longitude)
            .InclusiveBetween(-180, 180)
            .When(x => x.Longitude.HasValue)
            .WithMessage("Longitude must be between -180 and 180");
    }
}

public class CreateWeightDtoValidator : AbstractValidator<CreateWeightDto>
{
    public CreateWeightDtoValidator()
    {
        RuleFor(x => x.Value)
            .GreaterThan(0)
            .WithMessage("Weight value must be greater than zero");
    }
}

public class CreateVolumeDtoValidator : AbstractValidator<CreateVolumeDto>
{
    public CreateVolumeDtoValidator()
    {
        RuleFor(x => x.Value)
            .GreaterThan(0)
            .WithMessage("Volume value must be greater than zero");
    }
}

public class CreateDimensionsDtoValidator : AbstractValidator<CreateDimensionsDto>
{
    public CreateDimensionsDtoValidator()
    {
        RuleFor(x => x.Length)
            .GreaterThan(0)
            .WithMessage("Length must be greater than zero");

        RuleFor(x => x.Width)
            .GreaterThan(0)
            .WithMessage("Width must be greater than zero");

        RuleFor(x => x.Height)
            .GreaterThan(0)
            .WithMessage("Height must be greater than zero");
    }
}

public class CreateTemperatureRangeDtoValidator : AbstractValidator<CreateTemperatureRangeDto>
{
    public CreateTemperatureRangeDtoValidator()
    {
        RuleFor(x => x.MaxTemperature)
            .GreaterThan(x => x.MinTemperature)
            .WithMessage("Maximum temperature must be greater than minimum temperature");
    }
}

public class CreateTimeWindowDtoValidator : AbstractValidator<CreateTimeWindowDto>
{
    public CreateTimeWindowDtoValidator()
    {
        RuleFor(x => x.EndTime)
            .GreaterThan(x => x.StartTime)
            .WithMessage("End time must be after start time");
    }
}

public class CreateDistanceDtoValidator : AbstractValidator<CreateDistanceDto>
{
    public CreateDistanceDtoValidator()
    {
        RuleFor(x => x.Value)
            .GreaterThan(0)
            .WithMessage("Distance value must be greater than zero");
    }
}

public class CreateMoneyDtoValidator : AbstractValidator<CreateMoneyDto>
{
    public CreateMoneyDtoValidator()
    {
        RuleFor(x => x.Amount)
            .GreaterThanOrEqualTo(0)
            .WithMessage("Amount cannot be negative");

        RuleFor(x => x.Currency)
            .NotEmpty()
            .WithMessage("Currency is required")
            .Length(3)
            .WithMessage("Currency must be 3 characters");
    }
}
