using OrderManagement.Application.DTOs;

namespace OrderManagement.Application.Interfaces;

/// <summary>
/// Service interface for integrating with NetworkFleetManagement service
/// </summary>
public interface INetworkFleetService
{
    /// <summary>
    /// Get transporter ratings based on criteria
    /// </summary>
    Task<List<TransporterRatingDto>> GetTransporterRatingsAsync(
        List<string> serviceAreas,
        List<string> vehicleTypes,
        decimal? minRating = null,
        int? maxResults = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get broker ratings based on criteria
    /// </summary>
    Task<List<BrokerRatingDto>> GetBrokerRatingsAsync(
        List<string> serviceAreas,
        List<string> specializations,
        decimal? minRating = null,
        int? maxResults = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get detailed transporter information
    /// </summary>
    Task<TransporterRatingDto?> GetTransporterDetailsAsync(
        Guid transporterId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get detailed broker information
    /// </summary>
    Task<BrokerRatingDto?> GetBrokerDetailsAsync(
        Guid brokerId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get performance metrics for a transporter
    /// </summary>
    Task<Dictionary<string, object>> GetTransporterPerformanceMetricsAsync(
        Guid transporterId,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get performance metrics for a broker
    /// </summary>
    Task<Dictionary<string, object>> GetBrokerPerformanceMetricsAsync(
        Guid brokerId,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if a partner is available for a specific route and time
    /// </summary>
    Task<bool> IsPartnerAvailableAsync(
        Guid partnerId,
        string partnerType,
        DateTime pickupDate,
        DateTime deliveryDate,
        string pickupLocation,
        string deliveryLocation,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get recommended partners based on RFQ requirements
    /// </summary>
    Task<List<PreferredPartnerDto>> GetRecommendedPartnersAsync(
        string pickupLocation,
        string deliveryLocation,
        List<string> vehicleTypes,
        DateTime pickupDate,
        DateTime deliveryDate,
        decimal? budgetRange = null,
        int maxResults = 10,
        CancellationToken cancellationToken = default);
}
