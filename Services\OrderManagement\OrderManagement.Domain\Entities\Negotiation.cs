using Shared.Domain.Common;
using OrderManagement.Domain.Enums;
using OrderManagement.Domain.ValueObjects;

namespace OrderManagement.Domain.Entities;

public class Negotiation : BaseEntity
{
    public Guid RfqId { get; private set; }
    public Guid BrokerId { get; private set; }
    public Guid OriginalBidId { get; private set; }
    public NegotiationStatus Status { get; private set; }
    public DateTime StartedAt { get; private set; }
    public DateTime? CompletedAt { get; private set; }
    public Money OriginalPrice { get; private set; } = null!;
    public Money? FinalPrice { get; private set; }
    public string? FinalTerms { get; private set; }
    public string? Notes { get; private set; }
    public int TotalRounds { get; private set; }
    public Guid StartedBy { get; private set; }

    // Navigation properties
    public RFQ Rfq { get; private set; } = null!;
    public RfqBid OriginalBid { get; private set; } = null!;

    private readonly List<CounterOffer> _counterOffers = new();
    public IReadOnlyCollection<CounterOffer> CounterOffers => _counterOffers.AsReadOnly();

    private Negotiation() { } // EF Constructor

    public Negotiation(
        Guid rfqId,
        Guid brokerId,
        Guid originalBidId,
        Money originalPrice,
        Guid startedBy,
        string? notes = null)
    {
        RfqId = rfqId;
        BrokerId = brokerId;
        OriginalBidId = originalBidId;
        OriginalPrice = originalPrice ?? throw new ArgumentNullException(nameof(originalPrice));
        Status = NegotiationStatus.Active;
        StartedAt = DateTime.UtcNow;
        StartedBy = startedBy;
        Notes = notes;
        TotalRounds = 0;
    }

    public CounterOffer CreateCounterOffer(
        Guid bidId,
        CounterOfferType offerType,
        Money offeredPrice,
        Guid createdBy,
        string? offeredTerms = null,
        DateTime? offeredPickupDate = null,
        DateTime? offeredDeliveryDate = null,
        string? additionalServices = null,
        string? counterOfferReason = null,
        DateTime? expiresAt = null)
    {
        if (Status != NegotiationStatus.Active)
            throw new InvalidOperationException("Cannot create counter offer for inactive negotiation");

        var counterOffer = new CounterOffer(
            Id,
            bidId,
            offerType,
            offeredPrice,
            createdBy,
            offeredTerms,
            offeredPickupDate,
            offeredDeliveryDate,
            additionalServices,
            counterOfferReason,
            expiresAt);

        _counterOffers.Add(counterOffer);
        TotalRounds++;

        return counterOffer;
    }

    public void AcceptCounterOffer(Guid counterOfferId, Money finalPrice, string? finalTerms = null)
    {
        var counterOffer = _counterOffers.FirstOrDefault(co => co.Id == counterOfferId);
        if (counterOffer == null)
            throw new ArgumentException("Counter offer not found", nameof(counterOfferId));

        if (Status != NegotiationStatus.Active)
            throw new InvalidOperationException("Cannot accept counter offer for inactive negotiation");

        counterOffer.Accept();
        FinalPrice = finalPrice;
        FinalTerms = finalTerms;
        Status = NegotiationStatus.Completed;
        CompletedAt = DateTime.UtcNow;
    }

    public void RejectCounterOffer(Guid counterOfferId, string? responseNotes = null)
    {
        var counterOffer = _counterOffers.FirstOrDefault(co => co.Id == counterOfferId);
        if (counterOffer == null)
            throw new ArgumentException("Counter offer not found", nameof(counterOfferId));

        counterOffer.Reject(responseNotes);
    }

    public void Cancel(string? reason = null)
    {
        Status = NegotiationStatus.Cancelled;
        CompletedAt = DateTime.UtcNow;
        Notes = reason;
    }

    public void Expire()
    {
        Status = NegotiationStatus.Expired;
        CompletedAt = DateTime.UtcNow;
    }

    public void UpdateNotes(string? notes)
    {
        Notes = notes;
    }

    public CounterOffer? GetLatestCounterOffer()
    {
        return _counterOffers.OrderByDescending(co => co.CreatedAt).FirstOrDefault();
    }

    public CounterOffer? GetLatestShipperCounterOffer()
    {
        return _counterOffers
            .Where(co => co.OfferType == CounterOfferType.Shipper)
            .OrderByDescending(co => co.CreatedAt)
            .FirstOrDefault();
    }

    public CounterOffer? GetLatestBrokerCounterOffer()
    {
        return _counterOffers
            .Where(co => co.OfferType == CounterOfferType.Broker)
            .OrderByDescending(co => co.CreatedAt)
            .FirstOrDefault();
    }

    public bool HasActiveCounterOffer()
    {
        return _counterOffers.Any(co => co.Status == CounterOfferStatus.Pending && !co.IsExpired);
    }

    public bool IsActive => Status == NegotiationStatus.Active;
    public bool IsCompleted => Status == NegotiationStatus.Completed;
    public bool IsCancelled => Status == NegotiationStatus.Cancelled;
    public bool IsExpired => Status == NegotiationStatus.Expired;
}

public class CounterOffer : BaseEntity
{
    public Guid NegotiationId { get; private set; }
    public Guid BidId { get; private set; }
    public CounterOfferType OfferType { get; private set; }
    public Money OfferedPrice { get; private set; } = null!;
    public string? OfferedTerms { get; private set; }
    public DateTime? OfferedPickupDate { get; private set; }
    public DateTime? OfferedDeliveryDate { get; private set; }
    public string? AdditionalServices { get; private set; }
    public string? CounterOfferReason { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public Guid CreatedBy { get; private set; }
    public CounterOfferStatus Status { get; private set; }
    public DateTime? RespondedAt { get; private set; }
    public string? ResponseNotes { get; private set; }
    public DateTime? ExpiresAt { get; private set; }

    // Navigation properties
    public Negotiation Negotiation { get; private set; } = null!;

    private CounterOffer() { } // EF Constructor

    public CounterOffer(
        Guid negotiationId,
        Guid bidId,
        CounterOfferType offerType,
        Money offeredPrice,
        Guid createdBy,
        string? offeredTerms = null,
        DateTime? offeredPickupDate = null,
        DateTime? offeredDeliveryDate = null,
        string? additionalServices = null,
        string? counterOfferReason = null,
        DateTime? expiresAt = null)
    {
        NegotiationId = negotiationId;
        BidId = bidId;
        OfferType = offerType;
        OfferedPrice = offeredPrice ?? throw new ArgumentNullException(nameof(offeredPrice));
        OfferedTerms = offeredTerms;
        OfferedPickupDate = offeredPickupDate;
        OfferedDeliveryDate = offeredDeliveryDate;
        AdditionalServices = additionalServices;
        CounterOfferReason = counterOfferReason;
        CreatedAt = DateTime.UtcNow;
        CreatedBy = createdBy;
        Status = CounterOfferStatus.Pending;
        ExpiresAt = expiresAt ?? DateTime.UtcNow.AddDays(3); // Default 3 days expiry
    }

    public void Accept(string? responseNotes = null)
    {
        if (Status != CounterOfferStatus.Pending)
            throw new InvalidOperationException("Only pending counter offers can be accepted");

        if (IsExpired)
            throw new InvalidOperationException("Cannot accept expired counter offer");

        Status = CounterOfferStatus.Accepted;
        RespondedAt = DateTime.UtcNow;
        ResponseNotes = responseNotes;
    }

    public void Reject(string? responseNotes = null)
    {
        if (Status != CounterOfferStatus.Pending)
            throw new InvalidOperationException("Only pending counter offers can be rejected");

        Status = CounterOfferStatus.Rejected;
        RespondedAt = DateTime.UtcNow;
        ResponseNotes = responseNotes;
    }

    public void Expire()
    {
        if (Status == CounterOfferStatus.Pending)
        {
            Status = CounterOfferStatus.Expired;
            RespondedAt = DateTime.UtcNow;
        }
    }

    public bool IsExpired => ExpiresAt.HasValue && DateTime.UtcNow > ExpiresAt.Value;
    public bool IsAccepted => Status == CounterOfferStatus.Accepted;
    public bool IsRejected => Status == CounterOfferStatus.Rejected;
    public bool IsPending => Status == CounterOfferStatus.Pending && !IsExpired;
}

public enum NegotiationStatus
{
    Active = 1,
    Completed = 2,
    Cancelled = 3,
    Expired = 4
}

public enum CounterOfferType
{
    Shipper = 1,
    Broker = 2
}

public enum CounterOfferStatus
{
    Pending = 1,
    Accepted = 2,
    Rejected = 3,
    Expired = 4
}
