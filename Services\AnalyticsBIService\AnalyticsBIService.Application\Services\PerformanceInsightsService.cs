using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Enums;
using AnalyticsBIService.Domain.Repositories;
using AnalyticsBIService.Application.Interfaces;
using Microsoft.Extensions.Logging;

namespace AnalyticsBIService.Application.Services;

/// <summary>
/// Service for role-based performance insights and analytics
/// </summary>
public interface IPerformanceInsightsService
{
    Task<PerformanceInsightsDto> GetPerformanceInsightsAsync(Guid userId, UserType userType, CancellationToken cancellationToken = default);
    Task<decimal> CalculateQuoteToOrderConversionRateAsync(Guid userId, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default);
    Task<decimal> CalculateExpiredRFQRatioAsync(Guid userId, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default);
    Task<DriverPerformanceDto> GetDriverPerformanceAsync(Guid driverId, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default);
    Task<decimal> CalculateMilestoneComplianceRateAsync(Guid userId, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default);
    Task<List<PerformanceTrendDto>> GetPerformanceTrendsAsync(Guid userId, UserType userType, TimePeriod period = TimePeriod.Daily, CancellationToken cancellationToken = default);
}

public class PerformanceInsightsService : IPerformanceInsightsService
{
    private readonly IMetricRepository _metricRepository;
    private readonly IAnalyticsEventRepository _analyticsEventRepository;
    private readonly ICacheService _cacheService;
    private readonly ILogger<PerformanceInsightsService> _logger;

    private const string CACHE_KEY_PREFIX = "performance_insights";
    private static readonly TimeSpan DefaultCacheExpiration = TimeSpan.FromMinutes(30);

    public PerformanceInsightsService(
        IMetricRepository metricRepository,
        IAnalyticsEventRepository analyticsEventRepository,
        ICacheService cacheService,
        ILogger<PerformanceInsightsService> logger)
    {
        _metricRepository = metricRepository;
        _analyticsEventRepository = analyticsEventRepository;
        _cacheService = cacheService;
        _logger = logger;
    }

    public async Task<PerformanceInsightsDto> GetPerformanceInsightsAsync(Guid userId, UserType userType, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting performance insights for user {UserId} of type {UserType}", userId, userType);

            var cacheKey = GenerateCacheKey("insights", userId, userType);
            var cachedInsights = await _cacheService.GetAsync<PerformanceInsightsDto>(cacheKey, cancellationToken);

            if (cachedInsights != null)
            {
                return cachedInsights;
            }

            var insights = new PerformanceInsightsDto
            {
                UserId = userId,
                UserType = userType.ToString(),
                GeneratedAt = DateTime.UtcNow
            };

            // Calculate role-specific performance metrics
            switch (userType)
            {
                case UserType.TransportCompany:
                    await AddTransportCompanyInsightsAsync(insights, userId, cancellationToken);
                    break;
                case UserType.Broker:
                    await AddBrokerInsightsAsync(insights, userId, cancellationToken);
                    break;
                case UserType.Carrier:
                    await AddCarrierInsightsAsync(insights, userId, cancellationToken);
                    break;
                case UserType.Shipper:
                    await AddShipperInsightsAsync(insights, userId, cancellationToken);
                    break;
            }

            // Get performance trends
            insights.PerformanceTrends = await GetPerformanceTrendsAsync(userId, userType, cancellationToken: cancellationToken);

            // Cache the results
            await _cacheService.SetAsync(cacheKey, insights, DefaultCacheExpiration, cancellationToken);

            return insights;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting performance insights for user {UserId}", userId);
            throw;
        }
    }

    public async Task<decimal> CalculateQuoteToOrderConversionRateAsync(Guid userId, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = GenerateCacheKey("quote_conversion", userId, fromDate, toDate);
            var cachedRate = await _cacheService.GetAsync<decimal?>(cacheKey, cancellationToken);

            if (cachedRate.HasValue)
            {
                return cachedRate.Value;
            }

            var startDate = fromDate ?? DateTime.UtcNow.AddDays(-30);
            var endDate = toDate ?? DateTime.UtcNow;

            // Count quotes submitted
            var quotesSubmittedResult = await _analyticsEventRepository.GetByDateRangeAsync(
                startDate, endDate, 1, int.MaxValue, cancellationToken);
            var quotesSubmitted = quotesSubmittedResult.Items
                .Where(e => e.UserId == userId && e.EventName == "QuoteSubmitted")
                .Count();

            // Count quotes that resulted in orders
            var quotesConvertedResult = await _analyticsEventRepository.GetByDateRangeAsync(
                startDate, endDate, 1, int.MaxValue, cancellationToken);
            var quotesConverted = quotesConvertedResult.Items
                .Where(e => e.UserId == userId && e.EventName == "QuoteAccepted")
                .Count();

            var conversionRate = quotesSubmitted > 0 ? (decimal)quotesConverted / quotesSubmitted * 100 : 0;

            await _cacheService.SetAsync(cacheKey, conversionRate, DefaultCacheExpiration, cancellationToken);
            return conversionRate;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating quote to order conversion rate for user {UserId}", userId);
            return 0;
        }
    }

    public async Task<decimal> CalculateExpiredRFQRatioAsync(Guid userId, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = GenerateCacheKey("expired_rfq", userId, fromDate, toDate);
            var cachedRatio = await _cacheService.GetAsync<decimal?>(cacheKey, cancellationToken);

            if (cachedRatio.HasValue)
            {
                return cachedRatio.Value;
            }

            var startDate = fromDate ?? DateTime.UtcNow.AddDays(-30);
            var endDate = toDate ?? DateTime.UtcNow;

            // Count total RFQs created
            var totalRFQsResult = await _analyticsEventRepository.GetByDateRangeAsync(
                startDate, endDate, 1, int.MaxValue, cancellationToken);
            var totalRFQs = totalRFQsResult.Items
                .Where(e => e.UserId == userId && e.EventName == "RFQCreated")
                .Count();

            // Count expired RFQs
            var expiredRFQsResult = await _analyticsEventRepository.GetByDateRangeAsync(
                startDate, endDate, 1, int.MaxValue, cancellationToken);
            var expiredRFQs = expiredRFQsResult.Items
                .Where(e => e.UserId == userId && e.EventName == "RFQExpired")
                .Count();

            var expiredRatio = totalRFQs > 0 ? (decimal)expiredRFQs / totalRFQs * 100 : 0;

            await _cacheService.SetAsync(cacheKey, expiredRatio, DefaultCacheExpiration, cancellationToken);
            return expiredRatio;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating expired RFQ ratio for user {UserId}", userId);
            return 0;
        }
    }

    public async Task<DriverPerformanceDto> GetDriverPerformanceAsync(Guid driverId, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = GenerateCacheKey("driver_performance", driverId, fromDate, toDate);
            var cachedPerformance = await _cacheService.GetAsync<DriverPerformanceDto>(cacheKey, cancellationToken);

            if (cachedPerformance != null)
            {
                return cachedPerformance;
            }

            var startDate = fromDate ?? DateTime.UtcNow.AddDays(-30);
            var endDate = toDate ?? DateTime.UtcNow;

            var performance = new DriverPerformanceDto
            {
                DriverId = driverId,
                FromDate = startDate,
                ToDate = endDate
            };

            // Calculate POD upload rate
            var totalTripsResult = await _analyticsEventRepository.GetByDateRangeAsync(
                startDate, endDate, 1, int.MaxValue, cancellationToken);
            var totalTrips = totalTripsResult.Items
                .Where(e => e.UserId == driverId && e.EventName == "TripCompleted")
                .Count();

            var podsUploadedResult = await _analyticsEventRepository.GetByDateRangeAsync(
                startDate, endDate, 1, int.MaxValue, cancellationToken);
            var podsUploaded = podsUploadedResult.Items
                .Where(e => e.UserId == driverId && e.EventName == "PODUploaded")
                .Count();

            performance.PODUploadRate = totalTrips > 0 ? (decimal)podsUploaded / totalTrips * 100 : 0;

            // Calculate milestone compliance rate
            performance.MilestoneComplianceRate = await CalculateMilestoneComplianceRateAsync(driverId, startDate, endDate, cancellationToken);

            // Calculate on-time delivery rate
            var onTimeDeliveriesResult = await _analyticsEventRepository.GetByDateRangeAsync(
                startDate, endDate, 1, int.MaxValue, cancellationToken);
            var onTimeDeliveries = onTimeDeliveriesResult.Items
                .Where(e => e.UserId == driverId &&
                           e.EventName == "TripCompleted" &&
                           e.Properties.ContainsKey("onTime") &&
                           e.GetProperty<bool>("onTime"))
                .Count();

            performance.OnTimeDeliveryRate = totalTrips > 0 ? (decimal)onTimeDeliveries / totalTrips * 100 : 0;

            // Calculate average trip rating
            var tripRatingsResult = await _analyticsEventRepository.GetByDateRangeAsync(
                startDate, endDate, 1, int.MaxValue, cancellationToken);
            var tripRatings = tripRatingsResult.Items
                .Where(e => e.UserId == driverId &&
                           e.EventName == "TripRated" &&
                           e.Properties.ContainsKey("rating"))
                .Select(e => e.GetProperty<decimal>("rating"))
                .ToList();

            performance.AverageTripRating = tripRatings.Any() ? tripRatings.Average() : 0;
            performance.TotalTripsCompleted = totalTrips;

            await _cacheService.SetAsync(cacheKey, performance, DefaultCacheExpiration, cancellationToken);
            return performance;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting driver performance for driver {DriverId}", driverId);
            throw;
        }
    }

    public async Task<decimal> CalculateMilestoneComplianceRateAsync(Guid userId, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var startDate = fromDate ?? DateTime.UtcNow.AddDays(-30);
            var endDate = toDate ?? DateTime.UtcNow;

            // Count total milestones
            var totalMilestonesResult = await _analyticsEventRepository.GetByDateRangeAsync(
                startDate, endDate, 1, int.MaxValue, cancellationToken);
            var totalMilestones = totalMilestonesResult.Items
                .Where(e => e.UserId == userId && e.EventName == "MilestoneReached")
                .Count();

            // Count milestones reached on time
            var onTimeMilestonesResult = await _analyticsEventRepository.GetByDateRangeAsync(
                startDate, endDate, 1, int.MaxValue, cancellationToken);
            var onTimeMilestones = onTimeMilestonesResult.Items
                .Where(e => e.UserId == userId &&
                           e.EventName == "MilestoneReached" &&
                           e.Properties.ContainsKey("onTime") &&
                           e.GetProperty<bool>("onTime"))
                .Count();

            return totalMilestones > 0 ? (decimal)onTimeMilestones / totalMilestones * 100 : 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating milestone compliance rate for user {UserId}", userId);
            return 0;
        }
    }

    public async Task<List<PerformanceTrendDto>> GetPerformanceTrendsAsync(Guid userId, UserType userType, TimePeriod period = TimePeriod.Daily, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = GenerateCacheKey("trends", userId, userType, period);
            var cachedTrends = await _cacheService.GetAsync<List<PerformanceTrendDto>>(cacheKey, cancellationToken);

            if (cachedTrends != null)
            {
                return cachedTrends;
            }

            var trends = new List<PerformanceTrendDto>();
            var endDate = DateTime.UtcNow;
            var startDate = period switch
            {
                TimePeriod.Hourly => endDate.AddHours(-24),
                TimePeriod.Daily => endDate.AddDays(-30),
                TimePeriod.Weekly => endDate.AddDays(-84), // 12 weeks
                TimePeriod.Monthly => endDate.AddMonths(-12),
                _ => endDate.AddDays(-30)
            };

            // Generate trend data based on user type
            switch (userType)
            {
                case UserType.TransportCompany:
                    trends = await GetTransportCompanyTrendsAsync(userId, startDate, endDate, period, cancellationToken);
                    break;
                case UserType.Broker:
                    trends = await GetBrokerTrendsAsync(userId, startDate, endDate, period, cancellationToken);
                    break;
                case UserType.Carrier:
                    trends = await GetCarrierTrendsAsync(userId, startDate, endDate, period, cancellationToken);
                    break;
                case UserType.Shipper:
                    trends = await GetShipperTrendsAsync(userId, startDate, endDate, period, cancellationToken);
                    break;
            }

            await _cacheService.SetAsync(cacheKey, trends, DefaultCacheExpiration, cancellationToken);
            return trends;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting performance trends for user {UserId}", userId);
            return new List<PerformanceTrendDto>();
        }
    }

    // Private helper methods for role-specific insights
    private async Task AddTransportCompanyInsightsAsync(PerformanceInsightsDto insights, Guid userId, CancellationToken cancellationToken)
    {
        insights.QuoteToOrderConversionRate = await CalculateQuoteToOrderConversionRateAsync(userId, cancellationToken: cancellationToken);
        insights.ExpiredRFQRatio = await CalculateExpiredRFQRatioAsync(userId, cancellationToken: cancellationToken);

        // Add additional transport company metrics
        var last30Days = DateTime.UtcNow.AddDays(-30);

        var avgResponseTime = await CalculateAverageResponseTimeAsync(userId, last30Days, cancellationToken);
        var customerSatisfactionScore = await CalculateCustomerSatisfactionAsync(userId, last30Days, cancellationToken);

        insights.AdditionalMetrics = new Dictionary<string, decimal>
        {
            ["average_response_time_hours"] = avgResponseTime,
            ["customer_satisfaction_score"] = customerSatisfactionScore,
            ["rfq_success_rate"] = 100 - insights.ExpiredRFQRatio
        };
    }

    private async Task AddBrokerInsightsAsync(PerformanceInsightsDto insights, Guid userId, CancellationToken cancellationToken)
    {
        insights.QuoteToOrderConversionRate = await CalculateQuoteToOrderConversionRateAsync(userId, cancellationToken: cancellationToken);

        var last30Days = DateTime.UtcNow.AddDays(-30);

        var carrierUtilizationRate = await CalculateCarrierUtilizationRateAsync(userId, last30Days, cancellationToken);
        var avgMarginPercentage = await CalculateAverageMarginAsync(userId, last30Days, cancellationToken);

        insights.AdditionalMetrics = new Dictionary<string, decimal>
        {
            ["carrier_utilization_rate"] = carrierUtilizationRate,
            ["average_margin_percentage"] = avgMarginPercentage,
            ["quote_accuracy_rate"] = await CalculateQuoteAccuracyRateAsync(userId, last30Days, cancellationToken)
        };
    }

    private async Task AddCarrierInsightsAsync(PerformanceInsightsDto insights, Guid userId, CancellationToken cancellationToken)
    {
        insights.MilestoneComplianceRate = await CalculateMilestoneComplianceRateAsync(userId, cancellationToken: cancellationToken);

        var last30Days = DateTime.UtcNow.AddDays(-30);

        var vehicleUtilizationRate = await CalculateVehicleUtilizationRateAsync(userId, last30Days, cancellationToken);
        var fuelEfficiencyScore = await CalculateFuelEfficiencyScoreAsync(userId, last30Days, cancellationToken);

        insights.AdditionalMetrics = new Dictionary<string, decimal>
        {
            ["vehicle_utilization_rate"] = vehicleUtilizationRate,
            ["fuel_efficiency_score"] = fuelEfficiencyScore,
            ["safety_score"] = await CalculateSafetyScoreAsync(userId, last30Days, cancellationToken)
        };
    }

    private async Task AddShipperInsightsAsync(PerformanceInsightsDto insights, Guid userId, CancellationToken cancellationToken)
    {
        var last30Days = DateTime.UtcNow.AddDays(-30);

        var slaComplianceRate = await CalculateSLAComplianceRateAsync(userId, last30Days, cancellationToken);
        var costOptimizationScore = await CalculateCostOptimizationScoreAsync(userId, last30Days, cancellationToken);

        insights.AdditionalMetrics = new Dictionary<string, decimal>
        {
            ["sla_compliance_rate"] = slaComplianceRate,
            ["cost_optimization_score"] = costOptimizationScore,
            ["shipment_accuracy_rate"] = await CalculateShipmentAccuracyRateAsync(userId, last30Days, cancellationToken)
        };
    }

    // Helper calculation methods
    private async Task<decimal> CalculateAverageResponseTimeAsync(Guid userId, DateTime since, CancellationToken cancellationToken)
    {
        var responseEventsResult = await _analyticsEventRepository.GetByDateRangeAsync(
            since, DateTime.UtcNow, 1, int.MaxValue, cancellationToken);
        var responseEvents = responseEventsResult.Items
            .Where(e => e.UserId == userId &&
                       e.EventName.Contains("Response") &&
                       e.Properties.ContainsKey("responseTimeHours"))
            .Select(e => e.GetProperty<decimal>("responseTimeHours"))
            .ToList();

        return responseEvents.Any() ? responseEvents.Average() : 0;
    }

    private async Task<decimal> CalculateCustomerSatisfactionAsync(Guid userId, DateTime since, CancellationToken cancellationToken)
    {
        var ratingsResult = await _analyticsEventRepository.GetByDateRangeAsync(
            since, DateTime.UtcNow, 1, int.MaxValue, cancellationToken);
        var ratings = ratingsResult.Items
            .Where(e => e.UserId == userId &&
                       e.EventName == "CustomerRating" &&
                       e.Properties.ContainsKey("rating"))
            .Select(e => e.GetProperty<decimal>("rating"))
            .ToList();

        return ratings.Any() ? ratings.Average() : 0;
    }

    private async Task<decimal> CalculateCarrierUtilizationRateAsync(Guid userId, DateTime since, CancellationToken cancellationToken)
    {
        // This would calculate how efficiently the broker utilizes available carriers
        var totalCapacityResult = await _analyticsEventRepository.GetByDateRangeAsync(
            since, DateTime.UtcNow, 1, int.MaxValue, cancellationToken);
        var totalCarrierCapacity = totalCapacityResult.Items
            .Where(e => e.UserId == userId &&
                       e.EventName == "CarrierCapacityAvailable" &&
                       e.Properties.ContainsKey("capacity"))
            .Sum(e => e.GetProperty<decimal>("capacity"));

        var utilizedCapacityResult = await _analyticsEventRepository.GetByDateRangeAsync(
            since, DateTime.UtcNow, 1, int.MaxValue, cancellationToken);
        var utilizedCapacity = utilizedCapacityResult.Items
            .Where(e => e.UserId == userId &&
                       e.EventName == "CarrierCapacityUtilized" &&
                       e.Properties.ContainsKey("capacity"))
            .Sum(e => e.GetProperty<decimal>("capacity"));

        return totalCarrierCapacity > 0 ? utilizedCapacity / totalCarrierCapacity * 100 : 0;
    }

    // Additional placeholder calculation methods (would be implemented based on specific business logic)
    private async Task<decimal> CalculateAverageMarginAsync(Guid userId, DateTime since, CancellationToken cancellationToken)
    {
        // Placeholder implementation
        return 15.5m;
    }

    private async Task<decimal> CalculateQuoteAccuracyRateAsync(Guid userId, DateTime since, CancellationToken cancellationToken)
    {
        // Placeholder implementation
        return 92.3m;
    }

    private async Task<decimal> CalculateVehicleUtilizationRateAsync(Guid userId, DateTime since, CancellationToken cancellationToken)
    {
        // Placeholder implementation
        return 78.5m;
    }

    private async Task<decimal> CalculateFuelEfficiencyScoreAsync(Guid userId, DateTime since, CancellationToken cancellationToken)
    {
        // Placeholder implementation
        return 85.2m;
    }

    private async Task<decimal> CalculateSafetyScoreAsync(Guid userId, DateTime since, CancellationToken cancellationToken)
    {
        // Placeholder implementation
        return 94.7m;
    }

    private async Task<decimal> CalculateSLAComplianceRateAsync(Guid userId, DateTime since, CancellationToken cancellationToken)
    {
        // Placeholder implementation
        return 96.8m;
    }

    private async Task<decimal> CalculateCostOptimizationScoreAsync(Guid userId, DateTime since, CancellationToken cancellationToken)
    {
        // Placeholder implementation
        return 88.3m;
    }

    private async Task<decimal> CalculateShipmentAccuracyRateAsync(Guid userId, DateTime since, CancellationToken cancellationToken)
    {
        // Placeholder implementation
        return 97.2m;
    }

    // Trend calculation methods (placeholder implementations)
    private async Task<List<PerformanceTrendDto>> GetTransportCompanyTrendsAsync(Guid userId, DateTime startDate, DateTime endDate, TimePeriod period, CancellationToken cancellationToken)
    {
        // Placeholder implementation - would calculate actual trends
        return new List<PerformanceTrendDto>();
    }

    private async Task<List<PerformanceTrendDto>> GetBrokerTrendsAsync(Guid userId, DateTime startDate, DateTime endDate, TimePeriod period, CancellationToken cancellationToken)
    {
        // Placeholder implementation - would calculate actual trends
        return new List<PerformanceTrendDto>();
    }

    private async Task<List<PerformanceTrendDto>> GetCarrierTrendsAsync(Guid userId, DateTime startDate, DateTime endDate, TimePeriod period, CancellationToken cancellationToken)
    {
        // Placeholder implementation - would calculate actual trends
        return new List<PerformanceTrendDto>();
    }

    private async Task<List<PerformanceTrendDto>> GetShipperTrendsAsync(Guid userId, DateTime startDate, DateTime endDate, TimePeriod period, CancellationToken cancellationToken)
    {
        // Placeholder implementation - would calculate actual trends
        return new List<PerformanceTrendDto>();
    }

    private string GenerateCacheKey(string prefix, params object?[] parameters)
    {
        var keyParts = new List<string> { CACHE_KEY_PREFIX, prefix };

        foreach (var param in parameters)
        {
            if (param != null)
            {
                keyParts.Add(param.ToString()!);
            }
        }

        return string.Join(":", keyParts);
    }
}
