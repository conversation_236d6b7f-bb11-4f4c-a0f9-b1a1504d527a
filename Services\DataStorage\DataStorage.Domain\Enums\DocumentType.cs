namespace DataStorage.Domain.Enums;

public enum DocumentType
{
    // General Documents
    General = 0,
    Contract = 1,
    Invoice = 2,
    Receipt = 3,

    // Shipper Documents
    EWayBill = 10,
    TransportDocumentation = 11,
    DigitalInvoice = 12,
    TaxDocument = 13,
    GSTComplianceRecord = 14,

    // Carrier Documents
    PickupConfirmationPhoto = 20,
    DeliveryPhoto = 21,
    DigitalSignature = 22,
    ProofOfDelivery = 23,
    DeliveryReceipt = 24,
    UnloadingConfirmation = 25,

    // Admin Documents
    SystemConfiguration = 30,
    MultiLanguageContent = 31,
    LocalizationSettings = 32,

    // Compliance Documents
    AuditTrail = 40,
    ComplianceReport = 41,
    LegalDocument = 42
}

public enum DocumentStatus
{
    Draft = 0,
    Uploaded = 1,
    Processing = 2,
    Verified = 3,
    Approved = 4,
    Rejected = 5,
    Archived = 6,
    Deleted = 7
}

public enum AccessLevel
{
    Private = 0,
    Internal = 1,
    Shared = 2,
    Public = 3
}

public enum StorageProvider
{
    LocalFileSystem = 0,
    AzureBlobStorage = 1,
    AmazonS3 = 2,
    GoogleCloudStorage = 3,
    CDN = 4
}

public enum DocumentCategory
{
    Legal = 0,
    Financial = 1,
    Operational = 2,
    Compliance = 3,
    Marketing = 4,
    Technical = 5,
    Administrative = 6
}

public enum FileComplexity
{
    Unknown = 0,
    Simple = 1,
    Moderate = 2,
    Complex = 3,
    VeryComplex = 4
}

public enum CadFileFormat
{
    Unknown = 0,
    DWG = 1,
    DXF = 2,
    DGN = 3,
    STEP = 4,
    IGES = 5,
    STL = 6,
    OBJ = 7,
    PLY = 8,
    IFC = 9
}

public enum CadUnits
{
    Unknown = 0,
    Millimeters = 1,
    Centimeters = 2,
    Meters = 3,
    Inches = 4,
    Feet = 5,
    Points = 6,
    Pixels = 7
}

public enum MediaType
{
    Unknown = 0,
    Video = 1,
    Audio = 2,
    Image = 3
}

public enum VideoFormat
{
    Unknown = 0,
    MP4 = 1,
    AVI = 2,
    MOV = 3,
    WMV = 4,
    FLV = 5,
    WebM = 6,
    MKV = 7,
    M4V = 8
}

public enum VideoCodec
{
    Unknown = 0,
    H264 = 1,
    H265 = 2,
    VP8 = 3,
    VP9 = 4,
    AV1 = 5,
    MPEG4 = 6,
    MPEG2 = 7,
    Theora = 8
}

public enum AudioFormat
{
    Unknown = 0,
    MP3 = 1,
    WAV = 2,
    FLAC = 3,
    AAC = 4,
    OGG = 5,
    WMA = 6,
    M4A = 7,
    AIFF = 8
}

public enum AudioCodec
{
    Unknown = 0,
    MP3 = 1,
    AAC = 2,
    FLAC = 3,
    Vorbis = 4,
    Opus = 5,
    PCM = 6,
    WMA = 7,
    ALAC = 8
}

public enum VideoQuality
{
    Low = 0,
    Medium = 1,
    High = 2,
    VeryHigh = 3,
    Ultra = 4
}

public enum AudioQuality
{
    Low = 0,
    Medium = 1,
    High = 2,
    VeryHigh = 3,
    Lossless = 4
}

public enum TranscodingStatus
{
    Pending = 0,
    InProgress = 1,
    Completed = 2,
    Failed = 3,
    Cancelled = 4,
    Paused = 5
}

public enum ImageFormat
{
    Unknown = 0,
    JPEG = 1,
    PNG = 2,
    GIF = 3,
    BMP = 4,
    TIFF = 5,
    WebP = 6,
    SVG = 7
}

public enum QualityLevel
{
    VeryPoor = 0,
    Poor = 1,
    Fair = 2,
    Good = 3,
    VeryGood = 4,
    Excellent = 5
}

public enum QualityIssueType
{
    Unknown = 0,
    LowResolution = 1,
    HighNoise = 2,
    CompressionArtifacts = 3,
    ColorIssues = 4,
    AudioDistortion = 5,
    SyncIssues = 6,
    FrameDrops = 7,
    Interlacing = 8
}

public enum QualityIssueSeverity
{
    Low = 0,
    Medium = 1,
    High = 2,
    Critical = 3
}

public enum QualityRecommendationType
{
    Unknown = 0,
    IncreaseResolution = 1,
    ReduceNoise = 2,
    AdjustBitrate = 3,
    ChangeCodec = 4,
    ColorCorrection = 5,
    AudioEnhancement = 6,
    Deinterlacing = 7,
    FrameRateAdjustment = 8
}

public enum CdnProvider
{
    None = 0,
    AzureCdn = 1,
    AmazonCloudFront = 2,
    CloudFlare = 3,
    GoogleCloudCdn = 4,
    KeyCdn = 5,
    MaxCdn = 6
}

public enum CdnEndpointStatus
{
    Creating = 0,
    Running = 1,
    Stopped = 2,
    Deleting = 3,
    Failed = 4
}

public enum CacheBehavior
{
    Cache = 0,
    NoCache = 1,
    BypassCache = 2
}

public enum CacheInvalidationStatus
{
    Pending = 0,
    InProgress = 1,
    Completed = 2,
    Failed = 3
}

public enum PerformanceRecommendationType
{
    Unknown = 0,
    EnableCompression = 1,
    OptimizeCaching = 2,
    ReduceImageSizes = 3,
    MinifyAssets = 4,
    EnableHttp2 = 5,
    OptimizeOrigin = 6,
    AddEdgeLocations = 7
}

public enum PerformanceImpact
{
    Low = 0,
    Medium = 1,
    High = 2,
    Critical = 3
}

public enum ImplementationComplexity
{
    Low = 0,
    Medium = 1,
    High = 2,
    VeryHigh = 3
}

public enum AccessRuleType
{
    Allow = 0,
    Deny = 1,
    RateLimit = 2,
    Redirect = 3
}

public enum AccessRuleAction
{
    Allow = 0,
    Deny = 1,
    Block = 2,
    Redirect = 3,
    Challenge = 4
}

public enum AccessConditionType
{
    IpAddress = 0,
    Country = 1,
    UserAgent = 2,
    Referer = 3,
    RequestPath = 4,
    QueryString = 5,
    RequestHeader = 6
}

public enum AccessConditionOperator
{
    Equals = 0,
    NotEquals = 1,
    Contains = 2,
    NotContains = 3,
    StartsWith = 4,
    EndsWith = 5,
    Matches = 6,
    NotMatches = 7
}

public enum WafMode
{
    Detection = 0,
    Prevention = 1,
    Off = 2
}

public enum WafRuleType
{
    SqlInjection = 0,
    CrossSiteScripting = 1,
    CrossSiteRequestForgery = 2,
    BotDetection = 3,
    RateLimiting = 4,
    IpBlocking = 5,
    GeoBlocking = 6
}

public enum WafRuleAction
{
    Allow = 0,
    Block = 1,
    Challenge = 2,
    Log = 3
}

public enum RateLimitAction
{
    Allow = 0,
    Throttle = 1,
    Block = 2,
    Challenge = 3
}

public enum CdnHealthStatus
{
    Healthy = 0,
    Degraded = 1,
    Unhealthy = 2,
    Unknown = 3
}

public enum BooleanOperator
{
    Must = 0,
    Should = 1,
    MustNot = 2
}

public enum FilterOperator
{
    Equals = 0,
    NotEquals = 1,
    GreaterThan = 2,
    GreaterThanOrEqual = 3,
    LessThan = 4,
    LessThanOrEqual = 5,
    Contains = 6,
    NotContains = 7,
    In = 8,
    NotIn = 9,
    Range = 10
}

public enum FacetType
{
    Terms = 0,
    Range = 1,
    DateRange = 2,
    Histogram = 3
}

public enum GeoSortMode
{
    Distance = 0,
    Relevance = 1
}

public enum SuggestionType
{
    Query = 0,
    Completion = 1,
    Phrase = 2,
    Term = 3
}

public enum TrendDirection
{
    Up = 0,
    Down = 1,
    Stable = 2
}

public enum FieldType
{
    Text = 0,
    Keyword = 1,
    Integer = 2,
    Long = 3,
    Float = 4,
    Double = 5,
    Boolean = 6,
    Date = 7,
    GeoPoint = 8,
    Object = 9,
    Nested = 10
}

public enum IndexHealthStatus
{
    Green = 0,
    Yellow = 1,
    Red = 2
}

public enum IndexIssueType
{
    Performance = 0,
    Storage = 1,
    Mapping = 2,
    Sharding = 3,
    Replication = 4
}

public enum IndexIssueSeverity
{
    Low = 0,
    Medium = 1,
    High = 2,
    Critical = 3
}

public enum SortDirection
{
    Ascending = 0,
    Descending = 1
}

public enum SortMode
{
    Default = 0,
    Min = 1,
    Max = 2,
    Sum = 3,
    Average = 4,
    Median = 5
}

public enum VersionStatus
{
    Draft = 0,
    Published = 1,
    Archived = 2,
    Deleted = 3
}

public enum BranchStatus
{
    Active = 0,
    Merged = 1,
    Deleted = 2,
    Protected = 3
}

public enum MergeStrategy
{
    Merge = 0,
    Squash = 1,
    Rebase = 2,
    FastForward = 3
}

public enum ConflictType
{
    Content = 0,
    Metadata = 1,
    Structure = 2,
    Permission = 3
}

public enum ConflictSeverity
{
    Low = 0,
    Medium = 1,
    High = 2,
    Critical = 3
}

public enum ResolutionType
{
    Automatic = 0,
    Manual = 1,
    Assisted = 2
}

public enum ChangeType
{
    Added = 0,
    Modified = 1,
    Deleted = 2,
    Moved = 3,
    Renamed = 4
}

public enum DiffOperation
{
    Equal = 0,
    Insert = 1,
    Delete = 2,
    Replace = 3
}

public enum ChangeSetType
{
    Commit = 0,
    Merge = 1,
    Rollback = 2,
    Import = 3
}

public enum TagType
{
    General = 0,
    Release = 1,
    Milestone = 2,
    Hotfix = 3,
    Feature = 4
}

public enum BackupType
{
    Manual = 0,
    Automatic = 1,
    Scheduled = 2,
    Emergency = 3
}

public enum BackupStatus
{
    Creating = 0,
    Available = 1,
    Expired = 2,
    Corrupted = 3
}

public enum ActivityType
{
    VersionCreated = 0,
    BranchCreated = 1,
    Merged = 2,
    ConflictResolved = 3,
    TagCreated = 4,
    RolledBack = 5,
    Restored = 6
}

public enum PatternType
{
    FrequentCollaboration = 0,
    ConflictProne = 1,
    ParallelDevelopment = 2,
    SequentialWork = 3
}

public enum StorageTierType
{
    Hot = 0,
    Cool = 1,
    Cold = 2,
    Archive = 3,
    DeepArchive = 4
}

public enum RestorePriority
{
    Low = 0,
    Normal = 1,
    High = 2,
    Urgent = 3
}

public enum ArchivePolicyPriority
{
    Low = 0,
    Normal = 1,
    High = 2,
    Critical = 3
}

public enum ArchiveRuleType
{
    Age = 0,
    Size = 1,
    AccessPattern = 2,
    Category = 3,
    Custom = 4
}

public enum ArchiveConditionType
{
    Age = 0,
    Size = 1,
    LastAccessed = 2,
    Category = 3,
    DocumentType = 4,
    Owner = 5,
    Tags = 6
}

public enum ArchiveConditionOperator
{
    Equals = 0,
    NotEquals = 1,
    GreaterThan = 2,
    GreaterThanOrEqual = 3,
    LessThan = 4,
    LessThanOrEqual = 5,
    Contains = 6,
    NotContains = 7,
    In = 8,
    NotIn = 9
}

public enum ArchiveActionType
{
    Archive = 0,
    Delete = 1,
    Migrate = 2,
    Notify = 3
}

public enum ArchiveCandidateReason
{
    Age = 0,
    Size = 1,
    InactiveAccess = 2,
    PolicyMatch = 3,
    ManualSelection = 4
}

public enum ScheduleType
{
    OneTime = 0,
    Daily = 1,
    Weekly = 2,
    Monthly = 3,
    Custom = 4
}

public enum TierAvailability
{
    Immediate = 0,
    Minutes = 1,
    Hours = 2,
    Days = 3
}

public enum TierMigrationStatus
{
    Pending = 0,
    InProgress = 1,
    Completed = 2,
    Failed = 3,
    Cancelled = 4
}

public enum MetricsGroupBy
{
    Hour = 0,
    Day = 1,
    Week = 2,
    Month = 3
}

public enum OptimizationType
{
    TierMigration = 0,
    Compression = 1,
    Deduplication = 2,
    Cleanup = 3,
    PolicyOptimization = 4
}

public enum OptimizationImpact
{
    Low = 0,
    Medium = 1,
    High = 2,
    Critical = 3
}

public enum ComplianceStatus
{
    Compliant = 0,
    NonCompliant = 1,
    Warning = 2,
    Unknown = 3
}

public enum ComplianceStandard
{
    GDPR = 0,
    HIPAA = 1,
    SOX = 2,
    PCI_DSS = 3,
    ISO27001 = 4,
    Custom = 5
}

public enum ViolationType
{
    RetentionPeriod = 0,
    DataLocation = 1,
    AccessControl = 2,
    Encryption = 3,
    Audit = 4
}

public enum ViolationSeverity
{
    Low = 0,
    Medium = 1,
    High = 2,
    Critical = 3
}

public enum ViolationStatus
{
    Open = 0,
    InProgress = 1,
    Resolved = 2,
    Acknowledged = 3
}

public enum MetricStatus
{
    Pass = 0,
    Fail = 1,
    Warning = 2,
    NotApplicable = 3
}

public enum RetentionViolationType
{
    Overdue = 0,
    Premature = 1,
    MissingPolicy = 2,
    LegalHold = 3
}

public enum RetentionTrigger
{
    CreationDate = 0,
    LastModified = 1,
    LastAccessed = 2,
    Custom = 3
}

public enum RetentionRuleType
{
    Age = 0,
    Event = 1,
    Category = 2,
    Size = 3,
    Custom = 4
}

public enum RetentionConditionType
{
    Age = 0,
    Category = 1,
    Size = 2,
    Owner = 3,
    Tags = 4,
    LastAccessed = 5
}

public enum RetentionConditionOperator
{
    Equals = 0,
    NotEquals = 1,
    GreaterThan = 2,
    GreaterThanOrEqual = 3,
    LessThan = 4,
    LessThanOrEqual = 5,
    Contains = 6,
    NotContains = 7
}

public enum RetentionActionType
{
    Delete = 0,
    Archive = 1,
    Notify = 2,
    Review = 3
}

public enum RetentionStatus
{
    Active = 0,
    Expired = 1,
    OnHold = 2,
    PendingDeletion = 3,
    Deleted = 4
}

public enum RetentionScheduleStatus
{
    Scheduled = 0,
    PendingApproval = 1,
    Approved = 2,
    Executed = 3,
    Failed = 4,
    Cancelled = 5
}

public enum RetentionEventType
{
    PolicyAssigned = 0,
    PolicyChanged = 1,
    LegalHoldApplied = 2,
    LegalHoldReleased = 3,
    RetentionExpired = 4,
    DocumentDeleted = 5
}

public enum LegalHoldStatus
{
    Active = 0,
    Expired = 1,
    Released = 2,
    Cancelled = 3
}

public enum LegalHoldNotificationType
{
    Initial = 0,
    Reminder = 1,
    Release = 2,
    Escalation = 3
}

public enum LegalHoldNotificationStatus
{
    Sent = 0,
    Delivered = 1,
    Acknowledged = 2,
    Failed = 3
}

public enum ComplianceAuditStatus
{
    Pending = 0,
    InProgress = 1,
    Completed = 2,
    Failed = 3
}

public enum ComplianceAuditScope
{
    Full = 0,
    Sampling = 1,
    Targeted = 2,
    Quick = 3
}

public enum ComplianceFindingType
{
    PolicyViolation = 0,
    MissingPolicy = 1,
    ConfigurationIssue = 2,
    ProcessGap = 3
}

public enum ComplianceFindingSeverity
{
    Low = 0,
    Medium = 1,
    High = 2,
    Critical = 3
}

public enum ComplianceRecommendationPriority
{
    Low = 0,
    Medium = 1,
    High = 2,
    Critical = 3
}

public enum ComplianceAlertType
{
    RetentionViolation = 0,
    LegalHoldExpiring = 1,
    PolicyGap = 2,
    AuditRequired = 3
}

public enum ComplianceAlertSeverity
{
    Info = 0,
    Warning = 1,
    Error = 2,
    Critical = 3
}

public enum ComplianceAlertStatus
{
    Open = 0,
    Acknowledged = 1,
    InProgress = 2,
    Resolved = 3
}

public enum ComplianceActionType
{
    Delete = 0,
    Archive = 1,
    Notify = 2,
    ApplyLegalHold = 3,
    ReleaseLegalHold = 4
}

public enum ComplianceActionStatus
{
    Pending = 0,
    InProgress = 1,
    Completed = 2,
    Failed = 3
}

public enum ComplianceCheckType
{
    RetentionCompliance = 0,
    LegalHoldCompliance = 1,
    PolicyCompliance = 2,
    FullAudit = 3
}

public enum ComplianceJobType
{
    RetentionCheck = 0,
    LegalHoldReview = 1,
    PolicyAudit = 2,
    ComplianceReport = 3
}

public enum ComplianceJobStatus
{
    Queued = 0,
    Running = 1,
    Completed = 2,
    Failed = 3,
    Cancelled = 4
}

public enum BackupRetentionStrategy
{
    TimeBasedRetention = 0,
    CountBasedRetention = 1,
    SizeBasedRetention = 2,
    Custom = 3
}

public enum BackupVerificationStatus
{
    NotVerified = 0,
    Verified = 1,
    VerificationFailed = 2,
    VerificationInProgress = 3
}

public enum RecoveryPointType
{
    Backup = 0,
    Snapshot = 1,
    Transaction = 2,
    Checkpoint = 3
}

public enum ReplicationType
{
    Synchronous = 0,
    Asynchronous = 1,
    SemiSynchronous = 2
}

public enum ReplicationFrequency
{
    RealTime = 0,
    Hourly = 1,
    Daily = 2,
    Weekly = 3,
    Custom = 4
}

public enum ReplicationTargetType
{
    LocalStorage = 0,
    CloudStorage = 1,
    RemoteDataCenter = 2,
    HybridCloud = 3
}

public enum ReplicationTargetStatus
{
    Active = 0,
    Inactive = 1,
    Syncing = 2,
    Error = 3
}

public enum ReplicationHealth
{
    Healthy = 0,
    Warning = 1,
    Critical = 2,
    Unknown = 3
}

public enum ReplicationState
{
    Idle = 0,
    Syncing = 1,
    Paused = 2,
    Failed = 3,
    Stopped = 4
}

public enum DisasterRecoveryType
{
    FullRecovery = 0,
    PartialRecovery = 1,
    PointInTimeRecovery = 2,
    SystemRecovery = 3
}

public enum DisasterRecoveryPlanStatus
{
    Active = 0,
    Inactive = 1,
    Testing = 2,
    Executing = 3
}

public enum DisasterRecoveryStepType
{
    Backup = 0,
    Restore = 1,
    Verification = 2,
    Notification = 3,
    Custom = 4
}

public enum DisasterRecoveryTestStatus
{
    Pending = 0,
    Running = 1,
    Passed = 2,
    Failed = 3,
    Cancelled = 4
}

public enum DisasterRecoveryStepStatus
{
    Pending = 0,
    Running = 1,
    Completed = 2,
    Failed = 3,
    Skipped = 4
}

public enum DisasterRecoveryTrigger
{
    Manual = 0,
    Automatic = 1,
    Scheduled = 2,
    Emergency = 3
}

public enum RecoveryValidationType
{
    IntegrityCheck = 0,
    CompletenessCheck = 1,
    AccessibilityCheck = 2,
    PerformanceCheck = 3
}

public enum RecoveryValidationStatus
{
    Passed = 0,
    Failed = 1,
    Warning = 2,
    NotApplicable = 3
}

public enum BackupVerificationCheckType
{
    Checksum = 0,
    FileCount = 1,
    Size = 2,
    Accessibility = 3,
    Completeness = 4
}

public enum BackupVerificationCheckStatus
{
    Passed = 0,
    Failed = 1,
    Warning = 2,
    Skipped = 3
}

public enum BackupIntegrityLevel
{
    Excellent = 0,
    Good = 1,
    Fair = 2,
    Poor = 3,
    Critical = 4
}

public enum BackupHealthStatus
{
    Healthy = 0,
    Warning = 1,
    Critical = 2,
    Unknown = 3
}

public enum BackupHealthIssueType
{
    CorruptedBackup = 0,
    MissingBackup = 1,
    FailedVerification = 2,
    StorageIssue = 3,
    PerformanceIssue = 4
}

public enum BackupHealthIssueSeverity
{
    Low = 0,
    Medium = 1,
    High = 2,
    Critical = 3
}

public enum BackupHealthRecommendationPriority
{
    Low = 0,
    Medium = 1,
    High = 2,
    Critical = 3
}

public enum BackupAlertType
{
    BackupFailed = 0,
    BackupMissing = 1,
    StorageFull = 2,
    VerificationFailed = 3,
    ReplicationFailed = 4
}

public enum BackupAlertSeverity
{
    Info = 0,
    Warning = 1,
    Error = 2,
    Critical = 3
}

public enum BackupAlertStatus
{
    Open = 0,
    Acknowledged = 1,
    InProgress = 2,
    Resolved = 3
}

public enum HealthStatus
{
    Healthy = 0,
    Warning = 1,
    Critical = 2,
    Unknown = 3
}

public enum HealthIssueSeverity
{
    Low = 0,
    Medium = 1,
    High = 2,
    Critical = 3
}

public enum HealthIssueStatus
{
    Open = 0,
    InProgress = 1,
    Resolved = 2,
    Acknowledged = 3
}

public enum MetricsAggregation
{
    Average = 0,
    Sum = 1,
    Min = 2,
    Max = 3,
    Count = 4
}

public enum PerformanceAlertType
{
    HighResponseTime = 0,
    LowThroughput = 1,
    HighErrorRate = 2,
    ResourceExhaustion = 3,
    ServiceUnavailable = 4
}

public enum PerformanceAlertSeverity
{
    Info = 0,
    Warning = 1,
    Error = 2,
    Critical = 3
}

public enum PerformanceAlertStatus
{
    Open = 0,
    Acknowledged = 1,
    InProgress = 2,
    Resolved = 3
}

public enum PerformanceTestType
{
    Load = 0,
    Stress = 1,
    Volume = 2,
    Spike = 3,
    Endurance = 4
}

public enum PerformanceTestStatus
{
    Pending = 0,
    Running = 1,
    Completed = 2,
    Failed = 3,
    Cancelled = 4
}

public enum PerformanceTestScenarioStatus
{
    Pending = 0,
    Running = 1,
    Completed = 2,
    Failed = 3
}

public enum PerformanceTestStepType
{
    HttpRequest = 0,
    DatabaseQuery = 1,
    FileOperation = 2,
    Custom = 3
}

public enum LoadTestStatus
{
    Pending = 0,
    Running = 1,
    Completed = 2,
    Failed = 3,
    Cancelled = 4
}

public enum LoadTestStrategy
{
    Linear = 0,
    Exponential = 1,
    Step = 2,
    Spike = 3
}

public enum LoadTestActionType
{
    HttpGet = 0,
    HttpPost = 1,
    HttpPut = 2,
    HttpDelete = 3,
    DatabaseQuery = 4,
    FileUpload = 5,
    FileDownload = 6
}

public enum StressTestStatus
{
    Pending = 0,
    Running = 1,
    Completed = 2,
    Failed = 3,
    Cancelled = 4
}

public enum StressTestStopCondition
{
    ErrorThreshold = 0,
    ResponseTimeThreshold = 1,
    ResourceThreshold = 2,
    MaxUsers = 3
}

public enum StressTestActionType
{
    HttpRequest = 0,
    DatabaseOperation = 1,
    FileOperation = 2,
    Custom = 3
}
