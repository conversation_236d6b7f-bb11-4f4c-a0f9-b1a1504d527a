using AuditCompliance.Application.Commands;
using AuditCompliance.Application.DTOs;
using AuditCompliance.Application.Queries;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace AuditCompliance.API.Controllers
{
    /// <summary>
    /// Custom compliance report template controller
    /// </summary>
    [ApiController]
    [Route("api/audit/report-templates")]
    [Authorize]
    public class CustomReportTemplateController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly ILogger<CustomReportTemplateController> _logger;

        public CustomReportTemplateController(IMediator mediator, ILogger<CustomReportTemplateController> logger)
        {
            _mediator = mediator;
            _logger = logger;
        }

        /// <summary>
        /// Create a new custom report template
        /// </summary>
        [HttpPost]
        [Authorize(Roles = "Admin,ComplianceOfficer")]
        public async Task<ActionResult<CustomComplianceReportTemplateDto>> CreateTemplate([FromBody] CreateCustomReportTemplateRequestDto request)
        {
            try
            {
                var command = new CreateCustomReportTemplateCommand
                {
                    TemplateName = request.TemplateName,
                    Description = request.Description,
                    ComplianceStandard = request.ComplianceStandard,
                    TemplateType = request.TemplateType,
                    TemplateContent = request.TemplateContent,
                    Sections = request.Sections,
                    Parameters = request.Parameters,
                    Schedule = request.Schedule,
                    Tags = request.Tags,
                    IsPublic = request.IsPublic,
                    Metadata = request.Metadata,
                    CreatedBy = GetCurrentUserId(),
                    CreatedByName = GetCurrentUserName()
                };

                var result = await _mediator.Send(command);
                return CreatedAtAction(nameof(GetTemplateById), new { id = result.Id }, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating custom report template");
                return StatusCode(500, new { Message = "Internal server error", Error = ex.Message });
            }
        }

        /// <summary>
        /// Get template by ID
        /// </summary>
        [HttpGet("{id}")]
        [Authorize(Roles = "Admin,ComplianceOfficer,Auditor")]
        public async Task<ActionResult<CustomComplianceReportTemplateDto>> GetTemplateById(Guid id)
        {
            try
            {
                var query = new GetCustomReportTemplateByIdQuery { TemplateId = id, RequestedBy = GetCurrentUserId() };
                var result = await _mediator.Send(query);
                
                if (result == null)
                {
                    return NotFound(new { Message = "Template not found" });
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving template {TemplateId}", id);
                return StatusCode(500, new { Message = "Internal server error", Error = ex.Message });
            }
        }

        /// <summary>
        /// Search templates
        /// </summary>
        [HttpPost("search")]
        [Authorize(Roles = "Admin,ComplianceOfficer,Auditor")]
        public async Task<ActionResult<TemplateSearchResultDto>> SearchTemplates([FromBody] TemplateSearchRequestDto request)
        {
            try
            {
                var query = new SearchCustomReportTemplatesQuery
                {
                    SearchTerm = request.SearchTerm,
                    ComplianceStandard = request.ComplianceStandard,
                    TemplateType = request.TemplateType,
                    Tags = request.Tags,
                    IsPublic = request.IsPublic,
                    IsActive = request.IsActive,
                    CreatedBy = request.CreatedBy,
                    CreatedAfter = request.CreatedAfter,
                    CreatedBefore = request.CreatedBefore,
                    PageNumber = request.PageNumber,
                    PageSize = request.PageSize,
                    SortBy = request.SortBy,
                    SortDescending = request.SortDescending,
                    RequestedBy = GetCurrentUserId()
                };

                var result = await _mediator.Send(query);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching templates");
                return StatusCode(500, new { Message = "Internal server error", Error = ex.Message });
            }
        }

        /// <summary>
        /// Update template
        /// </summary>
        [HttpPut("{id}")]
        [Authorize(Roles = "Admin,ComplianceOfficer")]
        public async Task<ActionResult<CustomComplianceReportTemplateDto>> UpdateTemplate(Guid id, [FromBody] UpdateCustomReportTemplateRequestDto request)
        {
            try
            {
                var command = new UpdateCustomReportTemplateCommand
                {
                    TemplateId = id,
                    TemplateName = request.TemplateName,
                    Description = request.Description,
                    TemplateContent = request.TemplateContent,
                    Sections = request.Sections,
                    Parameters = request.Parameters,
                    Schedule = request.Schedule,
                    Tags = request.Tags,
                    Metadata = request.Metadata,
                    UpdatedBy = GetCurrentUserId()
                };

                var result = await _mediator.Send(command);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating template {TemplateId}", id);
                return StatusCode(500, new { Message = "Internal server error", Error = ex.Message });
            }
        }

        /// <summary>
        /// Delete template
        /// </summary>
        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin,ComplianceOfficer")]
        public async Task<ActionResult> DeleteTemplate(Guid id, [FromQuery] string? reason = null)
        {
            try
            {
                var command = new DeleteCustomReportTemplateCommand
                {
                    TemplateId = id,
                    DeletedBy = GetCurrentUserId(),
                    Reason = reason
                };

                var result = await _mediator.Send(command);
                if (result)
                {
                    return NoContent();
                }

                return NotFound(new { Message = "Template not found" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting template {TemplateId}", id);
                return StatusCode(500, new { Message = "Internal server error", Error = ex.Message });
            }
        }

        /// <summary>
        /// Toggle template active status
        /// </summary>
        [HttpPost("{id}/toggle-active")]
        [Authorize(Roles = "Admin,ComplianceOfficer")]
        public async Task<ActionResult> ToggleActiveStatus(Guid id, [FromBody] ToggleActiveStatusRequestDto request)
        {
            try
            {
                var command = new ToggleTemplateActiveStatusCommand
                {
                    TemplateId = id,
                    IsActive = request.IsActive,
                    UpdatedBy = GetCurrentUserId()
                };

                var result = await _mediator.Send(command);
                return Ok(new { Success = result, Message = result ? "Status updated successfully" : "Failed to update status" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error toggling template active status {TemplateId}", id);
                return StatusCode(500, new { Message = "Internal server error", Error = ex.Message });
            }
        }

        /// <summary>
        /// Toggle template visibility (public/private)
        /// </summary>
        [HttpPost("{id}/toggle-visibility")]
        [Authorize(Roles = "Admin,ComplianceOfficer")]
        public async Task<ActionResult> ToggleVisibility(Guid id, [FromBody] ToggleVisibilityRequestDto request)
        {
            try
            {
                var command = new ToggleTemplateVisibilityCommand
                {
                    TemplateId = id,
                    IsPublic = request.IsPublic,
                    UpdatedBy = GetCurrentUserId()
                };

                var result = await _mediator.Send(command);
                return Ok(new { Success = result, Message = result ? "Visibility updated successfully" : "Failed to update visibility" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error toggling template visibility {TemplateId}", id);
                return StatusCode(500, new { Message = "Internal server error", Error = ex.Message });
            }
        }

        /// <summary>
        /// Grant template permission
        /// </summary>
        [HttpPost("{id}/permissions")]
        [Authorize(Roles = "Admin,ComplianceOfficer")]
        public async Task<ActionResult> GrantPermission(Guid id, [FromBody] GrantTemplatePermissionRequestDto request)
        {
            try
            {
                var command = new GrantTemplatePermissionCommand
                {
                    TemplateId = id,
                    UserId = request.UserId,
                    UserName = "User", // This should be resolved from user service
                    PermissionType = request.PermissionType,
                    ExpiresAt = request.ExpiresAt,
                    Reason = request.Reason,
                    GrantedBy = GetCurrentUserId()
                };

                var result = await _mediator.Send(command);
                return Ok(new { Success = result, Message = result ? "Permission granted successfully" : "Failed to grant permission" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error granting template permission {TemplateId}", id);
                return StatusCode(500, new { Message = "Internal server error", Error = ex.Message });
            }
        }

        /// <summary>
        /// Revoke template permission
        /// </summary>
        [HttpDelete("{id}/permissions/{userId}")]
        [Authorize(Roles = "Admin,ComplianceOfficer")]
        public async Task<ActionResult> RevokePermission(Guid id, Guid userId, [FromQuery] string? reason = null)
        {
            try
            {
                var command = new RevokeTemplatePermissionCommand
                {
                    TemplateId = id,
                    UserId = userId,
                    RevokedBy = GetCurrentUserId(),
                    Reason = reason
                };

                var result = await _mediator.Send(command);
                return Ok(new { Success = result, Message = result ? "Permission revoked successfully" : "Failed to revoke permission" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error revoking template permission {TemplateId}", id);
                return StatusCode(500, new { Message = "Internal server error", Error = ex.Message });
            }
        }

        /// <summary>
        /// Generate report from template
        /// </summary>
        [HttpPost("{id}/generate")]
        [Authorize(Roles = "Admin,ComplianceOfficer,Auditor")]
        public async Task<ActionResult<GeneratedReportDto>> GenerateReport(Guid id, [FromBody] GenerateReportFromTemplateRequestDto request)
        {
            try
            {
                var command = new GenerateReportFromTemplateCommand
                {
                    TemplateId = id,
                    ParameterValues = request.ParameterValues,
                    OutputFormat = request.OutputFormat,
                    IncludeVisualizations = request.IncludeVisualizations,
                    IncludeInsights = request.IncludeInsights,
                    SaveResults = request.SaveResults,
                    ResultsName = request.ResultsName,
                    Recipients = request.Recipients,
                    ScheduleReport = request.ScheduleReport,
                    ScheduleExpression = request.ScheduleExpression,
                    RequestedBy = GetCurrentUserId(),
                    RequestedByName = GetCurrentUserName()
                };

                var result = await _mediator.Send(command);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating report from template {TemplateId}", id);
                return StatusCode(500, new { Message = "Internal server error", Error = ex.Message });
            }
        }

        /// <summary>
        /// Validate template
        /// </summary>
        [HttpPost("{id}/validate")]
        [Authorize(Roles = "Admin,ComplianceOfficer")]
        public async Task<ActionResult<TemplateValidationResultDto>> ValidateTemplate(Guid id, [FromBody] ValidateTemplateRequestDto request)
        {
            try
            {
                var command = new ValidateTemplateCommand
                {
                    TemplateId = id,
                    TemplateContent = request.TemplateContent,
                    Sections = request.Sections,
                    Parameters = request.Parameters,
                    PerformDeepValidation = request.PerformDeepValidation
                };

                var result = await _mediator.Send(command);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating template {TemplateId}", id);
                return StatusCode(500, new { Message = "Internal server error", Error = ex.Message });
            }
        }

        /// <summary>
        /// Clone template
        /// </summary>
        [HttpPost("{id}/clone")]
        [Authorize(Roles = "Admin,ComplianceOfficer")]
        public async Task<ActionResult<CustomComplianceReportTemplateDto>> CloneTemplate(Guid id, [FromBody] CloneTemplateRequestDto request)
        {
            try
            {
                var command = new CloneTemplateCommand
                {
                    SourceTemplateId = id,
                    NewTemplateName = request.NewTemplateName,
                    NewDescription = request.NewDescription,
                    MakePrivate = request.MakePrivate,
                    ClonedBy = GetCurrentUserId(),
                    ClonedByName = GetCurrentUserName()
                };

                var result = await _mediator.Send(command);
                return CreatedAtAction(nameof(GetTemplateById), new { id = result.Id }, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cloning template {TemplateId}", id);
                return StatusCode(500, new { Message = "Internal server error", Error = ex.Message });
            }
        }

        private Guid GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            return Guid.TryParse(userIdClaim, out var userId) ? userId : Guid.Empty;
        }

        private string GetCurrentUserName()
        {
            return User.FindFirst(ClaimTypes.Name)?.Value ?? "Unknown";
        }
    }

    // Supporting DTOs
    public class ToggleActiveStatusRequestDto
    {
        public bool IsActive { get; set; }
    }

    public class ToggleVisibilityRequestDto
    {
        public bool IsPublic { get; set; }
    }

    public class ValidateTemplateRequestDto
    {
        public string TemplateContent { get; set; } = string.Empty;
        public List<ReportSectionDto> Sections { get; set; } = new();
        public List<ReportParameterDto> Parameters { get; set; } = new();
        public bool PerformDeepValidation { get; set; } = true;
    }

    public class CloneTemplateRequestDto
    {
        public string NewTemplateName { get; set; } = string.Empty;
        public string? NewDescription { get; set; }
        public bool MakePrivate { get; set; } = true;
    }
}
