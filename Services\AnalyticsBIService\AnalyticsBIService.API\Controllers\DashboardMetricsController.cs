using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Application.Queries.Dashboard;
using AnalyticsBIService.Domain.Enums;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace AnalyticsBIService.API.Controllers;

/// <summary>
/// Controller for dashboard metrics and analytics
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class DashboardMetricsController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<DashboardMetricsController> _logger;

    public DashboardMetricsController(IMediator mediator, ILogger<DashboardMetricsController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get comprehensive dashboard metrics for the current user
    /// </summary>
    /// <param name="userType">Optional user type filter</param>
    /// <param name="forceRefresh">Force cache refresh</param>
    /// <returns>Dashboard metrics</returns>
    [HttpGet]
    public async Task<ActionResult<DashboardMetricsDto>> GetDashboardMetrics(
        [FromQuery] UserType? userType = null,
        [FromQuery] bool forceRefresh = false)
    {
        try
        {
            var userId = GetCurrentUserId();
            var currentUserType = userType ?? GetCurrentUserType();

            _logger.LogInformation("Getting dashboard metrics for user {UserId} of type {UserType}", 
                userId, currentUserType);

            var query = new GetDashboardMetricsQuery(userId, currentUserType, forceRefresh);
            var result = await _mediator.Send(query);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting dashboard metrics");
            return StatusCode(500, "An error occurred while retrieving dashboard metrics");
        }
    }

    /// <summary>
    /// Get dashboard metrics for a specific user (admin only)
    /// </summary>
    /// <param name="userId">Target user ID</param>
    /// <param name="userType">User type</param>
    /// <param name="forceRefresh">Force cache refresh</param>
    /// <returns>Dashboard metrics</returns>
    [HttpGet("user/{userId}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<DashboardMetricsDto>> GetUserDashboardMetrics(
        Guid userId,
        [FromQuery] UserType? userType = null,
        [FromQuery] bool forceRefresh = false)
    {
        try
        {
            _logger.LogInformation("Admin getting dashboard metrics for user {UserId} of type {UserType}", 
                userId, userType);

            var query = new GetDashboardMetricsQuery(userId, userType, forceRefresh);
            var result = await _mediator.Send(query);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting dashboard metrics for user {UserId}", userId);
            return StatusCode(500, "An error occurred while retrieving dashboard metrics");
        }
    }

    /// <summary>
    /// Get average order fulfillment time
    /// </summary>
    /// <param name="fromDate">Start date (optional)</param>
    /// <param name="toDate">End date (optional)</param>
    /// <returns>Average fulfillment time in hours</returns>
    [HttpGet("fulfillment-time")]
    public async Task<ActionResult<decimal>> GetAverageOrderFulfillmentTime(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        try
        {
            var userId = GetCurrentUserId();
            
            _logger.LogInformation("Getting average order fulfillment time for user {UserId}", userId);

            var query = new GetAverageOrderFulfillmentTimeQuery(userId, fromDate, toDate);
            var result = await _mediator.Send(query);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting average order fulfillment time");
            return StatusCode(500, "An error occurred while calculating fulfillment time");
        }
    }

    /// <summary>
    /// Get order fulfillment trends
    /// </summary>
    /// <param name="period">Time period for grouping (Daily, Weekly, Monthly)</param>
    /// <param name="fromDate">Start date (optional)</param>
    /// <param name="toDate">End date (optional)</param>
    /// <returns>List of trend data points</returns>
    [HttpGet("fulfillment-trends")]
    public async Task<ActionResult<List<MetricDataPointDto>>> GetOrderFulfillmentTrends(
        [FromQuery] TimePeriod period = TimePeriod.Daily,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        try
        {
            var userId = GetCurrentUserId();
            
            _logger.LogInformation("Getting order fulfillment trends for user {UserId} with period {Period}", 
                userId, period);

            var query = new GetOrderFulfillmentTrendsQuery(userId, period, fromDate, toDate);
            var result = await _mediator.Send(query);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting order fulfillment trends");
            return StatusCode(500, "An error occurred while retrieving fulfillment trends");
        }
    }

    /// <summary>
    /// Get real-time metrics
    /// </summary>
    /// <returns>Dictionary of real-time metric values</returns>
    [HttpGet("real-time")]
    public async Task<ActionResult<Dictionary<string, decimal>>> GetRealTimeMetrics()
    {
        try
        {
            var userId = GetCurrentUserId();
            
            _logger.LogInformation("Getting real-time metrics for user {UserId}", userId);

            var query = new GetRealTimeMetricsQuery(userId);
            var result = await _mediator.Send(query);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting real-time metrics");
            return StatusCode(500, "An error occurred while retrieving real-time metrics");
        }
    }

    /// <summary>
    /// Refresh metrics cache (admin only)
    /// </summary>
    /// <returns>Success status</returns>
    [HttpPost("refresh-cache")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> RefreshMetricsCache()
    {
        try
        {
            _logger.LogInformation("Admin refreshing metrics cache");

            var query = new GetDashboardMetricsQuery(forceRefresh: true);
            await _mediator.Send(query);

            return Ok(new { message = "Metrics cache refreshed successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error refreshing metrics cache");
            return StatusCode(500, "An error occurred while refreshing the cache");
        }
    }

    // Helper methods
    private Guid? GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return Guid.TryParse(userIdClaim, out var userId) ? userId : null;
    }

    private UserType? GetCurrentUserType()
    {
        var userTypeClaim = User.FindFirst("UserType")?.Value;
        return Enum.TryParse<UserType>(userTypeClaim, out var userType) ? userType : null;
    }
}
