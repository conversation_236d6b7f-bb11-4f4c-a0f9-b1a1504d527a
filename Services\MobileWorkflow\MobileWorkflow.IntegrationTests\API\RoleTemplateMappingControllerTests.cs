using FluentAssertions;
using Microsoft.AspNetCore.Mvc.Testing;
using MobileWorkflow.Application.DTOs;
using MobileWorkflow.IntegrationTests.Infrastructure;
using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using Xunit;

namespace MobileWorkflow.IntegrationTests.API;

public class RoleTemplateMappingControllerTests : IClassFixture<TestWebApplicationFactory<Program>>, IAsyncLifetime
{
    private readonly TestWebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;
    private readonly JsonSerializerOptions _jsonOptions;
    private Guid _templateId;

    public RoleTemplateMappingControllerTests(TestWebApplicationFactory<Program> factory)
    {
        _factory = factory;
        _client = _factory.CreateClient();
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
    }

    public async Task InitializeAsync()
    {
        // Create test data
        _templateId = await _factory.ExecuteDbContextAsync(async context =>
        {
            // Clear existing data
            context.RoleTemplateMappings.RemoveRange(context.RoleTemplateMappings);
            context.MilestoneTemplates.RemoveRange(context.MilestoneTemplates);
            await context.SaveChangesAsync();

            // Create test template
            var template = new Domain.Entities.MilestoneTemplate(
                "Test Template for Mappings",
                "Template for testing role mappings",
                "Trip",
                "Testing",
                "<EMAIL>");

            context.MilestoneTemplates.Add(template);

            // Create test role mappings
            var mapping1 = new Domain.Entities.RoleTemplateMappings(
                "Driver", template.Id, "<EMAIL>", true, 100);
            var mapping2 = new Domain.Entities.RoleTemplateMappings(
                "SeniorDriver", template.Id, "<EMAIL>", false, 90);

            context.RoleTemplateMappings.AddRange(mapping1, mapping2);
            await context.SaveChangesAsync();

            return template.Id;
        });
    }

    public Task DisposeAsync() => Task.CompletedTask;

    [Fact]
    public async Task GetRoleTemplateMappings_ShouldReturnAllMappings()
    {
        // Act
        var response = await _client.GetAsync("/api/RoleTemplateMapping");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var mappings = JsonSerializer.Deserialize<List<RoleTemplateMappingDto>>(content, _jsonOptions);
        
        mappings.Should().NotBeNull();
        mappings.Should().HaveCountGreaterOrEqualTo(2);
        mappings.Should().Contain(m => m.RoleName == "Driver");
        mappings.Should().Contain(m => m.RoleName == "SeniorDriver");
    }

    [Fact]
    public async Task GetRoleTemplateMappingsByRole_WithValidRole_ShouldReturnFilteredMappings()
    {
        // Act
        var response = await _client.GetAsync("/api/RoleTemplateMapping/by-role/Driver");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var mappings = JsonSerializer.Deserialize<List<RoleTemplateMappingDto>>(content, _jsonOptions);
        
        mappings.Should().NotBeNull();
        mappings.Should().OnlyContain(m => m.RoleName == "Driver");
    }

    [Fact]
    public async Task GetDefaultRoleTemplateMappingByRole_WithValidRole_ShouldReturnDefaultMapping()
    {
        // Act
        var response = await _client.GetAsync("/api/RoleTemplateMapping/default/Driver");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var mapping = JsonSerializer.Deserialize<RoleTemplateMappingDto>(content, _jsonOptions);
        
        mapping.Should().NotBeNull();
        mapping.RoleName.Should().Be("Driver");
        mapping.IsDefault.Should().BeTrue();
    }

    [Fact]
    public async Task GetBestRoleTemplateMapping_WithContext_ShouldReturnBestMatch()
    {
        // Arrange
        var context = new Dictionary<string, object>
        {
            { "experience_years", 5 },
            { "rating", 4.8 },
            { "department", "logistics" }
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/RoleTemplateMapping/best-match/Driver", context, _jsonOptions);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var mapping = JsonSerializer.Deserialize<RoleTemplateMappingDto>(content, _jsonOptions);
        
        mapping.Should().NotBeNull();
        mapping.RoleName.Should().Be("Driver");
    }

    [Fact]
    public async Task CreateRoleTemplateMapping_WithValidData_ShouldCreateSuccessfully()
    {
        // Arrange
        var request = new CreateRoleTemplateMappingRequest
        {
            RoleName = "TestRole",
            MilestoneTemplateId = _templateId,
            IsDefault = false,
            Priority = 75,
            Conditions = "{\"department\": \"testing\", \"level\": \"junior\"}",
            Configuration = new Dictionary<string, object> { { "auto_assign", true } },
            Metadata = new Dictionary<string, object> { { "test_mapping", true } }
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/RoleTemplateMapping", request, _jsonOptions);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Created);
        
        var content = await response.Content.ReadAsStringAsync();
        var mapping = JsonSerializer.Deserialize<RoleTemplateMappingDto>(content, _jsonOptions);
        
        mapping.Should().NotBeNull();
        mapping.RoleName.Should().Be(request.RoleName);
        mapping.MilestoneTemplateId.Should().Be(request.MilestoneTemplateId);
        mapping.IsDefault.Should().Be(request.IsDefault);
        mapping.Priority.Should().Be(request.Priority);
        mapping.Conditions.Should().Be(request.Conditions);

        // Verify in database
        var dbMapping = await _factory.ExecuteDbContextAsync(async context =>
        {
            return context.RoleTemplateMappings
                .FirstOrDefault(m => m.RoleName == request.RoleName);
        });

        dbMapping.Should().NotBeNull();
        dbMapping!.RoleName.Should().Be(request.RoleName);
        dbMapping.Priority.Should().Be(request.Priority);
    }

    [Fact]
    public async Task UpdateRoleTemplateMapping_WithValidData_ShouldUpdateSuccessfully()
    {
        // Arrange - Get existing mapping
        var mappingId = await _factory.ExecuteDbContextAsync(async context =>
        {
            var mapping = context.RoleTemplateMappings.First(m => m.RoleName == "SeniorDriver");
            return mapping.Id;
        });

        var request = new UpdateRoleTemplateMappingRequest
        {
            IsDefault = true,
            Priority = 95,
            Conditions = "{\"experience_years\": \">3\", \"rating\": \">4.0\"}",
            Configuration = new Dictionary<string, object> { { "priority_access", true } },
            Metadata = new Dictionary<string, object> { { "updated", true } }
        };

        // Act
        var response = await _client.PutAsJsonAsync($"/api/RoleTemplateMapping/{mappingId}", request, _jsonOptions);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var mapping = JsonSerializer.Deserialize<RoleTemplateMappingDto>(content, _jsonOptions);
        
        mapping.Should().NotBeNull();
        mapping.IsDefault.Should().Be(request.IsDefault);
        mapping.Priority.Should().Be(request.Priority);
        mapping.Conditions.Should().Be(request.Conditions);

        // Verify in database
        var dbMapping = await _factory.ExecuteDbContextAsync(async context =>
        {
            return context.RoleTemplateMappings.FirstOrDefault(m => m.Id == mappingId);
        });

        dbMapping.Should().NotBeNull();
        dbMapping!.IsDefault.Should().Be(request.IsDefault);
        dbMapping.Priority.Should().Be(request.Priority);
        dbMapping.Conditions.Should().Be(request.Conditions);
    }

    [Fact]
    public async Task DeleteRoleTemplateMapping_WithValidId_ShouldDeleteSuccessfully()
    {
        // Arrange - Create a mapping to delete
        var mappingId = await _factory.ExecuteDbContextAsync(async context =>
        {
            var mapping = new Domain.Entities.RoleTemplateMappings(
                "TempRole", _templateId, "<EMAIL>", false, 50);
            context.RoleTemplateMappings.Add(mapping);
            await context.SaveChangesAsync();
            return mapping.Id;
        });

        // Act
        var response = await _client.DeleteAsync($"/api/RoleTemplateMapping/{mappingId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NoContent);

        // Verify deletion in database
        var dbMapping = await _factory.ExecuteDbContextAsync(async context =>
        {
            return context.RoleTemplateMappings.FirstOrDefault(m => m.Id == mappingId);
        });

        dbMapping.Should().BeNull();
    }

    [Fact]
    public async Task SetRoleTemplateMappingAsDefault_WithValidId_ShouldSetAsDefault()
    {
        // Arrange - Get non-default mapping
        var mappingId = await _factory.ExecuteDbContextAsync(async context =>
        {
            var mapping = context.RoleTemplateMappings.First(m => m.RoleName == "SeniorDriver");
            return mapping.Id;
        });

        // Act
        var response = await _client.PostAsync($"/api/RoleTemplateMapping/{mappingId}/set-default", null);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        // Verify in database
        var dbMapping = await _factory.ExecuteDbContextAsync(async context =>
        {
            return context.RoleTemplateMappings.FirstOrDefault(m => m.Id == mappingId);
        });

        dbMapping.Should().NotBeNull();
        dbMapping!.IsDefault.Should().BeTrue();
    }

    [Fact]
    public async Task GetDistinctRoleNames_ShouldReturnUniqueRoles()
    {
        // Act
        var response = await _client.GetAsync("/api/RoleTemplateMapping/roles");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var roles = JsonSerializer.Deserialize<List<string>>(content, _jsonOptions);
        
        roles.Should().NotBeNull();
        roles.Should().Contain("Driver");
        roles.Should().Contain("SeniorDriver");
        roles.Should().OnlyHaveUniqueItems();
    }

    [Fact]
    public async Task GetRoleTemplateMappingsByTemplate_WithValidTemplateId_ShouldReturnMappings()
    {
        // Act
        var response = await _client.GetAsync($"/api/RoleTemplateMapping/by-template/{_templateId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var mappings = JsonSerializer.Deserialize<List<RoleTemplateMappingDto>>(content, _jsonOptions);
        
        mappings.Should().NotBeNull();
        mappings.Should().OnlyContain(m => m.MilestoneTemplateId == _templateId);
        mappings.Should().HaveCountGreaterOrEqualTo(2);
    }

    [Fact]
    public async Task GetRoleTemplateMapping_WithInvalidId_ShouldReturnNotFound()
    {
        // Arrange
        var invalidId = Guid.NewGuid();

        // Act
        var response = await _client.GetAsync($"/api/RoleTemplateMapping/{invalidId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task CreateRoleTemplateMapping_WithInvalidTemplateId_ShouldReturnBadRequest()
    {
        // Arrange
        var request = new CreateRoleTemplateMappingRequest
        {
            RoleName = "TestRole",
            MilestoneTemplateId = Guid.NewGuid(), // Invalid template ID
            IsDefault = false,
            Priority = 75
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/RoleTemplateMapping", request, _jsonOptions);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task GetDefaultRoleTemplateMappingByRole_WithNonExistentRole_ShouldReturnNotFound()
    {
        // Act
        var response = await _client.GetAsync("/api/RoleTemplateMapping/default/NonExistentRole");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }
}
