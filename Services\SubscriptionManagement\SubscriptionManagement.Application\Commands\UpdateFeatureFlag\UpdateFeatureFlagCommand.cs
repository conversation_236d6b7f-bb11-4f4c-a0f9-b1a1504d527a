using MediatR;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Application.Commands.UpdateFeatureFlag
{
    public class UpdateFeatureFlagCommand : IRequest<bool>
    {
        public Guid FeatureFlagId { get; set; }
        public string? Name { get; set; }
        public string? Description { get; set; }
        public bool? IsEnabled { get; set; }
        public FeatureFlagStatus? Status { get; set; }
        public int? RolloutPercentage { get; set; }
        public string? DefaultValue { get; set; }
        public string? TargetUserTypes { get; set; } // JSON array of user types
        public string? ABTestConfiguration { get; set; } // JSON configuration for A/B tests
        public string? Variants { get; set; } // JSON array of variants
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? UpdateReason { get; set; }
    }
}
