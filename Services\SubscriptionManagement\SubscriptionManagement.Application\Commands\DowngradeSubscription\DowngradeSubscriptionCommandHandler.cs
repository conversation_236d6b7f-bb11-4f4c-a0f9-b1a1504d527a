using MediatR;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Exceptions;
using SubscriptionManagement.Domain.Enums;
using Shared.Messaging;

namespace SubscriptionManagement.Application.Commands.DowngradeSubscription
{
    public class DowngradeSubscriptionCommandHandler : IRequestHandler<DowngradeSubscriptionCommand, bool>
    {
        private readonly ISubscriptionRepository _subscriptionRepository;
        private readonly IPlanRepository _planRepository;
        private readonly IPaymentService _paymentService;
        private readonly IMessageBroker _messageBroker;
        private readonly ILogger<DowngradeSubscriptionCommandHandler> _logger;

        public DowngradeSubscriptionCommandHandler(
            ISubscriptionRepository subscriptionRepository,
            IPlanRepository planRepository,
            IPaymentService paymentService,
            IMessageBroker messageBroker,
            ILogger<DowngradeSubscriptionCommandHandler> logger)
        {
            _subscriptionRepository = subscriptionRepository;
            _planRepository = planRepository;
            _paymentService = paymentService;
            _messageBroker = messageBroker;
            _logger = logger;
        }

        public async Task<bool> Handle(DowngradeSubscriptionCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Downgrading subscription {SubscriptionId} to plan {NewPlanId} for user {UserId}", 
                request.SubscriptionId, request.NewPlanId, request.UserId);

            // Get current subscription
            var subscription = await _subscriptionRepository.GetByIdAsync(request.SubscriptionId);
            if (subscription == null)
            {
                throw new SubscriptionDomainException("Subscription not found");
            }

            // Authorization check
            if (subscription.UserId != request.UserId)
            {
                throw new SubscriptionDomainException("You can only downgrade your own subscription");
            }

            // Validate subscription can be downgraded
            if (subscription.Status != SubscriptionStatus.Active)
            {
                throw new SubscriptionDomainException("Only active subscriptions can be downgraded");
            }

            // Get new plan
            var newPlan = await _planRepository.GetByIdAsync(request.NewPlanId);
            if (newPlan == null)
            {
                throw new SubscriptionDomainException("New plan not found");
            }

            if (!newPlan.IsActive)
            {
                throw new SubscriptionDomainException("Cannot downgrade to inactive plan");
            }

            // Validate it's actually a downgrade (lower price)
            if (newPlan.Price.Amount >= subscription.CurrentPrice.Amount)
            {
                throw new SubscriptionDomainException("New plan must have a lower price than current plan for downgrade");
            }

            var oldPlan = await _planRepository.GetByIdAsync(subscription.PlanId);
            
            // Calculate refund if needed
            decimal refundAmount = 0;
            if (request.ProcessRefund && request.ProrationMode == ProrationMode.CreateProrations)
            {
                refundAmount = CalculateDowngradeRefund(subscription, newPlan);
            }

            // Process refund if applicable
            if (refundAmount > 0 && request.ProcessRefund)
            {
                var refundResult = await _paymentService.ProcessRefundAsync(subscription, refundAmount);
                if (!refundResult.IsSuccess)
                {
                    _logger.LogWarning("Refund processing failed for subscription {SubscriptionId}: {Error}", 
                        request.SubscriptionId, refundResult.ErrorMessage);
                    // Continue with downgrade even if refund fails - can be processed manually
                }
            }

            // Perform the downgrade
            subscription.DowngradeToPlan(newPlan, request.ImmediateDowngrade, request.ProrationMode);
            
            // Save changes
            await _subscriptionRepository.UpdateAsync(subscription);

            // Publish downgrade event
            await _messageBroker.PublishAsync("subscription.downgraded", new
            {
                SubscriptionId = subscription.Id,
                UserId = subscription.UserId,
                OldPlanId = oldPlan?.Id,
                OldPlanName = oldPlan?.Name,
                NewPlanId = newPlan.Id,
                NewPlanName = newPlan.Name,
                RefundAmount = refundAmount,
                DowngradeReason = request.DowngradeReason,
                ImmediateDowngrade = request.ImmediateDowngrade,
                DowngradedAt = DateTime.UtcNow,
                NewPrice = subscription.CurrentPrice.Amount,
                Currency = subscription.CurrentPrice.Currency,
                EffectiveDate = request.ImmediateDowngrade ? DateTime.UtcNow : subscription.NextBillingDate
            });

            _logger.LogInformation("Successfully downgraded subscription {SubscriptionId} from plan {OldPlanId} to {NewPlanId}", 
                request.SubscriptionId, oldPlan?.Id, request.NewPlanId);

            return true;
        }

        private decimal CalculateDowngradeRefund(Subscription subscription, Plan newPlan)
        {
            var now = DateTime.UtcNow;
            var daysInCurrentPeriod = (subscription.NextBillingDate - subscription.StartDate).Days;
            var daysRemaining = (subscription.NextBillingDate - now).Days;
            
            if (daysRemaining <= 0) return 0;

            var currentPlanDailyRate = subscription.CurrentPrice.Amount / daysInCurrentPeriod;
            var newPlanDailyRate = newPlan.Price.Amount / daysInCurrentPeriod;
            
            return Math.Max(0, (currentPlanDailyRate - newPlanDailyRate) * daysRemaining);
        }
    }
}
