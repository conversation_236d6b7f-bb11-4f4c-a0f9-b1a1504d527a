using Shared.Domain.ValueObjects;

namespace TripManagement.Domain.ValueObjects;

public class Geofence : ValueObject
{
    public Location Center { get; private set; }
    public double RadiusMeters { get; private set; }
    public string Name { get; private set; }
    public GeofenceType Type { get; private set; }

    public Geofence(Location center, double radiusMeters, string name, GeofenceType type)
    {
        if (radiusMeters <= 0)
            throw new ArgumentException("Radius must be positive", nameof(radiusMeters));

        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Name cannot be empty", nameof(name));

        Center = center ?? throw new ArgumentNullException(nameof(center));
        RadiusMeters = radiusMeters;
        Name = name;
        Type = type;
    }

    public bool Contains(Location location)
    {
        if (location == null)
            return false;

        var distance = CalculateDistance(Center, location);
        return distance <= RadiusMeters;
    }

    public double DistanceFromBoundary(Location location)
    {
        if (location == null)
            return double.MaxValue;

        var distanceFromCenter = CalculateDistance(Center, location);
        return Math.Abs(distanceFromCenter - RadiusMeters);
    }

    private static double CalculateDistance(Location point1, Location point2)
    {
        const double earthRadiusMeters = 6371000;

        var lat1Rad = point1.Latitude * Math.PI / 180;
        var lat2Rad = point2.Latitude * Math.PI / 180;
        var deltaLatRad = (point2.Latitude - point1.Latitude) * Math.PI / 180;
        var deltaLonRad = (point2.Longitude - point1.Longitude) * Math.PI / 180;

        var a = Math.Sin(deltaLatRad / 2) * Math.Sin(deltaLatRad / 2) +
                Math.Cos(lat1Rad) * Math.Cos(lat2Rad) *
                Math.Sin(deltaLonRad / 2) * Math.Sin(deltaLonRad / 2);

        var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));

        return earthRadiusMeters * c;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Center;
        yield return RadiusMeters;
        yield return Name;
        yield return Type;
    }
}

public enum GeofenceType
{
    Pickup = 0,
    Delivery = 1,
    RestArea = 2,
    Warehouse = 3,
    Border = 4,
    Restricted = 5,
    Custom = 6
}
