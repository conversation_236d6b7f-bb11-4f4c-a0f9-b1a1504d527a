using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Application.Interfaces;
using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Enums;
using AnalyticsBIService.Domain.Repositories;
using Microsoft.Extensions.Logging;

namespace AnalyticsBIService.Application.Services;

/// <summary>
/// Service for conversion and behavior analytics including RFQ lifecycle tracking and funnel analysis
/// </summary>
public interface IConversionAnalyticsService
{
    Task<ConversionAnalyticsDto> GetConversionAnalyticsAsync(Guid? userId = null, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default);
    Task<RFQLifecycleAnalyticsDto> GetRFQLifecycleAnalyticsAsync(Guid? userId = null, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default);
    Task<List<FunnelAnalysisDto>> GetOrderDropOffAnalysisAsync(Guid? userId = null, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default);
    Task<QuoteTimelinessMetricsDto> GetQuoteTimelinessMetricsAsync(Guid? userId = null, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default);
    Task<List<BehaviorPatternDto>> AnalyzeBehaviorPatternsAsync(Guid? userId = null, string? userType = null, CancellationToken cancellationToken = default);
    Task<ConversionTrendsDto> GetConversionTrendsAsync(Guid? userId = null, TimePeriod period = TimePeriod.Daily, CancellationToken cancellationToken = default);
    Task PublishRFQLifecycleEventAsync(Guid rfqId, string eventType, Dictionary<string, object> eventData, CancellationToken cancellationToken = default);
}

public class ConversionAnalyticsService : IConversionAnalyticsService
{
    private readonly IMetricRepository _metricRepository;
    private readonly IAnalyticsEventRepository _analyticsEventRepository;
    private readonly ICacheService _cacheService;
    private readonly ILogger<ConversionAnalyticsService> _logger;

    private const string CACHE_KEY_PREFIX = "conversion_analytics";
    private static readonly TimeSpan DefaultCacheExpiration = TimeSpan.FromMinutes(30);
    private static readonly TimeSpan RealTimeCacheExpiration = TimeSpan.FromMinutes(5);

    // Performance benchmarks
    private const decimal EXCELLENT_RESPONSE_TIME_HOURS = 2;
    private const decimal GOOD_RESPONSE_TIME_HOURS = 6;
    private const decimal ACCEPTABLE_RESPONSE_TIME_HOURS = 24;

    public ConversionAnalyticsService(
        IMetricRepository metricRepository,
        IAnalyticsEventRepository analyticsEventRepository,
        ICacheService cacheService,
        ILogger<ConversionAnalyticsService> logger)
    {
        _metricRepository = metricRepository;
        _analyticsEventRepository = analyticsEventRepository;
        _cacheService = cacheService;
        _logger = logger;
    }

    public async Task<ConversionAnalyticsDto> GetConversionAnalyticsAsync(Guid? userId = null, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting conversion analytics for user {UserId}", userId);

            var cacheKey = GenerateCacheKey("conversion_analytics", userId, fromDate, toDate);
            var cachedAnalytics = await _cacheService.GetAsync<ConversionAnalyticsDto>(cacheKey, cancellationToken);

            if (cachedAnalytics != null)
            {
                return cachedAnalytics;
            }

            var startDate = fromDate ?? DateTime.UtcNow.AddDays(-30);
            var endDate = toDate ?? DateTime.UtcNow;

            var analytics = new ConversionAnalyticsDto
            {
                UserId = userId,
                FromDate = startDate,
                ToDate = endDate,
                GeneratedAt = DateTime.UtcNow
            };

            // Get RFQ lifecycle analytics
            analytics.RFQLifecycleAnalytics = await GetRFQLifecycleAnalyticsAsync(userId, startDate, endDate, cancellationToken);

            // Get funnel analysis
            analytics.FunnelAnalysis = await GetOrderDropOffAnalysisAsync(userId, startDate, endDate, cancellationToken);

            // Get quote timeliness metrics
            analytics.QuoteTimelinessMetrics = await GetQuoteTimelinessMetricsAsync(userId, startDate, endDate, cancellationToken);

            // Get behavior patterns
            analytics.BehaviorPatterns = await AnalyzeBehaviorPatternsAsync(userId, cancellationToken: cancellationToken);

            // Calculate overall conversion metrics
            await CalculateOverallConversionMetricsAsync(analytics, userId, startDate, endDate, cancellationToken);

            // Cache the results
            await _cacheService.SetAsync(cacheKey, analytics, DefaultCacheExpiration, cancellationToken);

            return analytics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting conversion analytics for user {UserId}", userId);
            throw;
        }
    }

    public async Task<RFQLifecycleAnalyticsDto> GetRFQLifecycleAnalyticsAsync(Guid? userId = null, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = GenerateCacheKey("rfq_lifecycle", userId, fromDate, toDate);
            var cachedAnalytics = await _cacheService.GetAsync<RFQLifecycleAnalyticsDto>(cacheKey, cancellationToken);

            if (cachedAnalytics != null)
            {
                return cachedAnalytics;
            }

            var startDate = fromDate ?? DateTime.UtcNow.AddDays(-30);
            var endDate = toDate ?? DateTime.UtcNow;

            _logger.LogDebug("Getting RFQ lifecycle analytics for user {UserId}", userId);

            var analytics = new RFQLifecycleAnalyticsDto
            {
                FromDate = startDate,
                ToDate = endDate
            };

            // Get RFQ events
            var rfqEvents = await GetRFQEventsAsync(userId, startDate, endDate, cancellationToken);

            // Calculate lifecycle metrics
            analytics.TotalRFQsCreated = rfqEvents.Count(e => e.EventName == "RFQCreated");
            analytics.RFQsWithQuotes = rfqEvents.Where(e => e.EventName == "QuoteSubmitted")
                                               .Select(e => e.EntityId)
                                               .Distinct()
                                               .Count();
            analytics.RFQsConverted = rfqEvents.Where(e => e.EventName == "RFQConverted")
                                              .Select(e => e.EntityId)
                                              .Distinct()
                                              .Count();
            analytics.RFQsExpired = rfqEvents.Count(e => e.EventName == "RFQExpired");

            // Calculate rates
            if (analytics.TotalRFQsCreated > 0)
            {
                analytics.QuoteResponseRate = (decimal)analytics.RFQsWithQuotes / analytics.TotalRFQsCreated * 100;
                analytics.ConversionRate = (decimal)analytics.RFQsConverted / analytics.TotalRFQsCreated * 100;
                analytics.ExpirationRate = (decimal)analytics.RFQsExpired / analytics.TotalRFQsCreated * 100;
            }

            // Calculate average times
            analytics.AverageTimeToFirstQuote = await CalculateAverageTimeToFirstQuoteAsync(rfqEvents);
            analytics.AverageTimeToConversion = await CalculateAverageTimeToConversionAsync(rfqEvents);

            // Get stage breakdown
            analytics.StageBreakdown = await GetRFQStageBreakdownAsync(rfqEvents);

            await _cacheService.SetAsync(cacheKey, analytics, DefaultCacheExpiration, cancellationToken);
            return analytics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting RFQ lifecycle analytics");
            throw;
        }
    }

    public async Task<List<FunnelAnalysisDto>> GetOrderDropOffAnalysisAsync(Guid? userId = null, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = GenerateCacheKey("funnel_analysis", userId, fromDate, toDate);
            var cachedAnalysis = await _cacheService.GetAsync<List<FunnelAnalysisDto>>(cacheKey, cancellationToken);

            if (cachedAnalysis != null)
            {
                return cachedAnalysis;
            }

            var startDate = fromDate ?? DateTime.UtcNow.AddDays(-30);
            var endDate = toDate ?? DateTime.UtcNow;

            _logger.LogDebug("Getting order drop-off analysis for user {UserId}", userId);

            var funnelStages = new List<FunnelAnalysisDto>();

            // Define funnel stages
            var stages = new[]
            {
                new { Name = "RFQ Created", EventName = "RFQCreated", Order = 1 },
                new { Name = "Quote Requested", EventName = "QuoteRequested", Order = 2 },
                new { Name = "Quote Submitted", EventName = "QuoteSubmitted", Order = 3 },
                new { Name = "Quote Reviewed", EventName = "QuoteReviewed", Order = 4 },
                new { Name = "Quote Accepted", EventName = "QuoteAccepted", Order = 5 },
                new { Name = "Order Created", EventName = "OrderCreated", Order = 6 },
                new { Name = "Order Confirmed", EventName = "OrderConfirmed", Order = 7 }
            };

            var events = await GetOrderFunnelEventsAsync(userId, startDate, endDate, cancellationToken);
            var totalStarted = events.Count(e => e.EventName == "RFQCreated");

            foreach (var stage in stages)
            {
                var stageCount = events.Count(e => e.EventName == stage.EventName);
                var conversionRate = totalStarted > 0 ? (decimal)stageCount / totalStarted * 100 : 0;
                var dropOffRate = stage.Order > 1 ?
                    (funnelStages.LastOrDefault()?.Count ?? 0) - stageCount : 0;

                funnelStages.Add(new FunnelAnalysisDto
                {
                    StageName = stage.Name,
                    StageOrder = stage.Order,
                    Count = stageCount,
                    ConversionRate = conversionRate,
                    DropOffCount = dropOffRate,
                    DropOffRate = stage.Order > 1 && (funnelStages.LastOrDefault()?.Count ?? 0) > 0 ?
                        (decimal)dropOffRate / (funnelStages.LastOrDefault()?.Count ?? 1) * 100 : 0
                });
            }

            await _cacheService.SetAsync(cacheKey, funnelStages, DefaultCacheExpiration, cancellationToken);
            return funnelStages;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting order drop-off analysis");
            return new List<FunnelAnalysisDto>();
        }
    }

    public async Task<QuoteTimelinessMetricsDto> GetQuoteTimelinessMetricsAsync(Guid? userId = null, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = GenerateCacheKey("quote_timeliness", userId, fromDate, toDate);
            var cachedMetrics = await _cacheService.GetAsync<QuoteTimelinessMetricsDto>(cacheKey, cancellationToken);

            if (cachedMetrics != null)
            {
                return cachedMetrics;
            }

            var startDate = fromDate ?? DateTime.UtcNow.AddDays(-30);
            var endDate = toDate ?? DateTime.UtcNow;

            _logger.LogDebug("Getting quote timeliness metrics for user {UserId}", userId);

            var metrics = new QuoteTimelinessMetricsDto
            {
                FromDate = startDate,
                ToDate = endDate
            };

            // Get quote response times
            var quoteResponseTimes = await GetQuoteResponseTimesAsync(userId, startDate, endDate, cancellationToken);

            if (quoteResponseTimes.Any())
            {
                metrics.AverageResponseTimeHours = quoteResponseTimes.Average();
                metrics.MedianResponseTimeHours = CalculateMedian(quoteResponseTimes);
                metrics.FastestResponseTimeHours = quoteResponseTimes.Min();
                metrics.SlowestResponseTimeHours = quoteResponseTimes.Max();

                // Calculate performance benchmarks
                metrics.ExcellentResponseCount = quoteResponseTimes.Count(t => t <= EXCELLENT_RESPONSE_TIME_HOURS);
                metrics.GoodResponseCount = quoteResponseTimes.Count(t => t > EXCELLENT_RESPONSE_TIME_HOURS && t <= GOOD_RESPONSE_TIME_HOURS);
                metrics.AcceptableResponseCount = quoteResponseTimes.Count(t => t > GOOD_RESPONSE_TIME_HOURS && t <= ACCEPTABLE_RESPONSE_TIME_HOURS);
                metrics.SlowResponseCount = quoteResponseTimes.Count(t => t > ACCEPTABLE_RESPONSE_TIME_HOURS);

                metrics.TotalQuotes = quoteResponseTimes.Count;

                // Calculate percentages
                metrics.ExcellentResponsePercentage = (decimal)metrics.ExcellentResponseCount / metrics.TotalQuotes * 100;
                metrics.GoodResponsePercentage = (decimal)metrics.GoodResponseCount / metrics.TotalQuotes * 100;
                metrics.AcceptableResponsePercentage = (decimal)metrics.AcceptableResponseCount / metrics.TotalQuotes * 100;
                metrics.SlowResponsePercentage = (decimal)metrics.SlowResponseCount / metrics.TotalQuotes * 100;
            }

            await _cacheService.SetAsync(cacheKey, metrics, DefaultCacheExpiration, cancellationToken);
            return metrics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting quote timeliness metrics");
            throw;
        }
    }

    public async Task<List<BehaviorPatternDto>> AnalyzeBehaviorPatternsAsync(Guid? userId = null, string? userType = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = GenerateCacheKey("behavior_patterns", userId, userType);
            var cachedPatterns = await _cacheService.GetAsync<List<BehaviorPatternDto>>(cacheKey, cancellationToken);

            if (cachedPatterns != null)
            {
                return cachedPatterns;
            }

            _logger.LogDebug("Analyzing behavior patterns for user {UserId} of type {UserType}", userId, userType);

            var patterns = new List<BehaviorPatternDto>();
            var last90Days = DateTime.UtcNow.AddDays(-90);

            // Get user activity events
            var activityEvents = await GetUserActivityEventsAsync(userId, userType, last90Days, cancellationToken);

            // Analyze time-based patterns
            patterns.AddRange(await AnalyzeTimeBasedPatternsAsync(activityEvents));

            // Analyze interaction patterns
            patterns.AddRange(await AnalyzeInteractionPatternsAsync(activityEvents));

            // Analyze conversion patterns
            patterns.AddRange(await AnalyzeConversionPatternsAsync(activityEvents));

            await _cacheService.SetAsync(cacheKey, patterns, DefaultCacheExpiration, cancellationToken);
            return patterns;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing behavior patterns");
            return new List<BehaviorPatternDto>();
        }
    }

    public async Task<ConversionTrendsDto> GetConversionTrendsAsync(Guid? userId = null, TimePeriod period = TimePeriod.Daily, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = GenerateCacheKey("conversion_trends", userId, period);
            var cachedTrends = await _cacheService.GetAsync<ConversionTrendsDto>(cacheKey, cancellationToken);

            if (cachedTrends != null)
            {
                return cachedTrends;
            }

            _logger.LogDebug("Getting conversion trends for user {UserId} with period {Period}", userId, period);

            var trends = new ConversionTrendsDto
            {
                Period = period.ToString(),
                GeneratedAt = DateTime.UtcNow
            };

            var endDate = DateTime.UtcNow;
            var startDate = period switch
            {
                TimePeriod.Hourly => endDate.AddHours(-24),
                TimePeriod.Daily => endDate.AddDays(-30),
                TimePeriod.Weekly => endDate.AddDays(-84),
                TimePeriod.Monthly => endDate.AddMonths(-12),
                _ => endDate.AddDays(-30)
            };

            // Get conversion events for the period
            var conversionEvents = await GetConversionEventsAsync(userId, startDate, endDate, cancellationToken);

            // Group by time period and calculate trends
            trends.TrendData = await CalculateConversionTrendDataAsync(conversionEvents, period, startDate, endDate);

            await _cacheService.SetAsync(cacheKey, trends, DefaultCacheExpiration, cancellationToken);
            return trends;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting conversion trends");
            throw;
        }
    }

    public async Task PublishRFQLifecycleEventAsync(Guid rfqId, string eventType, Dictionary<string, object> eventData, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Publishing RFQ lifecycle event {EventType} for RFQ {RFQId}", eventType, rfqId);

            var userId = eventData.TryGetValue("userId", out var userIdObj) && userIdObj is Guid userGuid ? userGuid : (Guid?)null;

            var analyticsEvent = new AnalyticsEvent(
                eventName: eventType,
                eventType: AnalyticsEventType.BusinessTransaction,
                dataSource: DataSourceType.Application,
                userId: userId,
                entityId: rfqId,
                entityType: "RFQ",
                properties: eventData);

            await _analyticsEventRepository.AddAsync(analyticsEvent, cancellationToken);

            // Invalidate related caches
            await InvalidateConversionCachesAsync(analyticsEvent.UserId, cancellationToken);

            _logger.LogDebug("Successfully published RFQ lifecycle event {EventType} for RFQ {RFQId}", eventType, rfqId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing RFQ lifecycle event {EventType} for RFQ {RFQId}", eventType, rfqId);
            throw;
        }
    }

    // Private helper methods
    private async Task<List<AnalyticsEvent>> GetRFQEventsAsync(Guid? userId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken)
    {
        var eventsResult = await _analyticsEventRepository.GetByDateRangeAsync(
            startDate, endDate, 1, int.MaxValue, cancellationToken);

        var events = eventsResult.Items
            .Where(e => (e.EventName.StartsWith("RFQ") || e.EventName.StartsWith("Quote")) &&
                       e.EntityType == "RFQ");

        if (userId.HasValue)
        {
            events = events.Where(e => e.UserId == userId.Value);
        }

        return events.OrderBy(e => e.Timestamp).ToList();
    }

    private async Task<List<AnalyticsEvent>> GetOrderFunnelEventsAsync(Guid? userId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken)
    {
        var funnelEventNames = new[] { "RFQCreated", "QuoteRequested", "QuoteSubmitted", "QuoteReviewed", "QuoteAccepted", "OrderCreated", "OrderConfirmed" };

        var eventsResult = await _analyticsEventRepository.GetByDateRangeAsync(
            startDate, endDate, 1, int.MaxValue, cancellationToken);

        var events = eventsResult.Items
            .Where(e => funnelEventNames.Contains(e.EventName));

        if (userId.HasValue)
        {
            events = events.Where(e => e.UserId == userId.Value);
        }

        return events.OrderBy(e => e.Timestamp).ToList();
    }

    private async Task<List<decimal>> GetQuoteResponseTimesAsync(Guid? userId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken)
    {
        var eventsResult = await _analyticsEventRepository.GetByDateRangeAsync(
            startDate, endDate, 1, int.MaxValue, cancellationToken);

        var events = eventsResult.Items
            .Where(e => e.EventName == "QuoteSubmitted" &&
                       e.Properties.ContainsKey("responseTimeHours"));

        if (userId.HasValue)
        {
            events = events.Where(e => e.UserId == userId.Value);
        }

        var eventsList = events.ToList();
        return eventsList.Select(e => e.GetProperty<decimal>("responseTimeHours")).ToList();
    }

    private async Task<decimal> CalculateAverageTimeToFirstQuoteAsync(List<AnalyticsEvent> rfqEvents)
    {
        var rfqCreatedEvents = rfqEvents.Where(e => e.EventName == "RFQCreated").ToList();
        var firstQuoteEvents = rfqEvents.Where(e => e.EventName == "QuoteSubmitted")
                                       .GroupBy(e => e.EntityId)
                                       .Select(g => g.OrderBy(e => e.Timestamp).First())
                                       .ToList();

        var timesToFirstQuote = new List<decimal>();

        foreach (var rfqCreated in rfqCreatedEvents)
        {
            var firstQuote = firstQuoteEvents.FirstOrDefault(q => q.EntityId == rfqCreated.EntityId);
            if (firstQuote != null)
            {
                var timeToQuote = (decimal)(firstQuote.Timestamp - rfqCreated.Timestamp).TotalHours;
                timesToFirstQuote.Add(timeToQuote);
            }
        }

        return timesToFirstQuote.Any() ? timesToFirstQuote.Average() : 0;
    }

    private async Task<decimal> CalculateAverageTimeToConversionAsync(List<AnalyticsEvent> rfqEvents)
    {
        var rfqCreatedEvents = rfqEvents.Where(e => e.EventName == "RFQCreated").ToList();
        var conversionEvents = rfqEvents.Where(e => e.EventName == "RFQConverted").ToList();

        var timesToConversion = new List<decimal>();

        foreach (var rfqCreated in rfqCreatedEvents)
        {
            var conversion = conversionEvents.FirstOrDefault(c => c.EntityId == rfqCreated.EntityId);
            if (conversion != null)
            {
                var timeToConversion = (decimal)(conversion.Timestamp - rfqCreated.Timestamp).TotalHours;
                timesToConversion.Add(timeToConversion);
            }
        }

        return timesToConversion.Any() ? timesToConversion.Average() : 0;
    }

    private async Task<Dictionary<string, int>> GetRFQStageBreakdownAsync(List<AnalyticsEvent> rfqEvents)
    {
        var breakdown = new Dictionary<string, int>
        {
            ["Created"] = rfqEvents.Count(e => e.EventName == "RFQCreated"),
            ["Quote Requested"] = rfqEvents.Count(e => e.EventName == "QuoteRequested"),
            ["Quote Received"] = rfqEvents.Count(e => e.EventName == "QuoteSubmitted"),
            ["Under Review"] = rfqEvents.Count(e => e.EventName == "QuoteReviewed"),
            ["Converted"] = rfqEvents.Count(e => e.EventName == "RFQConverted"),
            ["Expired"] = rfqEvents.Count(e => e.EventName == "RFQExpired"),
            ["Cancelled"] = rfqEvents.Count(e => e.EventName == "RFQCancelled")
        };

        return breakdown;
    }

    private async Task CalculateOverallConversionMetricsAsync(ConversionAnalyticsDto analytics, Guid? userId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken)
    {
        var events = await GetConversionEventsAsync(userId, startDate, endDate, cancellationToken);

        analytics.TotalRFQsCreated = events.Count(e => e.EventName == "RFQCreated");
        analytics.TotalQuotesSubmitted = events.Count(e => e.EventName == "QuoteSubmitted");
        analytics.TotalOrdersCreated = events.Count(e => e.EventName == "OrderCreated");

        if (analytics.TotalRFQsCreated > 0)
        {
            analytics.OverallConversionRate = (decimal)analytics.TotalOrdersCreated / analytics.TotalRFQsCreated * 100;
        }

        if (analytics.TotalRFQsCreated > 0)
        {
            analytics.QuoteResponseRate = (decimal)analytics.TotalQuotesSubmitted / analytics.TotalRFQsCreated * 100;
        }
    }

    private async Task<List<AnalyticsEvent>> GetUserActivityEventsAsync(Guid? userId, string? userType, DateTime since, CancellationToken cancellationToken)
    {
        var eventsResult = await _analyticsEventRepository.GetByDateRangeAsync(
            since, DateTime.UtcNow, 1, int.MaxValue, cancellationToken);

        var events = eventsResult.Items.AsEnumerable();

        if (userId.HasValue)
        {
            events = events.Where(e => e.UserId == userId.Value);
        }

        if (!string.IsNullOrEmpty(userType))
        {
            events = events.Where(e => e.Properties.ContainsKey("userType") &&
                                   e.GetProperty<string>("userType") == userType);
        }

        return events.OrderBy(e => e.Timestamp).ToList();
    }

    private async Task<List<BehaviorPatternDto>> AnalyzeTimeBasedPatternsAsync(List<AnalyticsEvent> events)
    {
        var patterns = new List<BehaviorPatternDto>();

        // Analyze peak activity hours
        var hourlyActivity = events.GroupBy(e => e.Timestamp.Hour)
                                  .Select(g => new { Hour = g.Key, Count = g.Count() })
                                  .OrderByDescending(x => x.Count)
                                  .Take(3)
                                  .ToList();

        if (hourlyActivity.Any())
        {
            patterns.Add(new BehaviorPatternDto
            {
                PatternType = "Peak Activity Hours",
                Description = $"Most active during hours: {string.Join(", ", hourlyActivity.Select(h => $"{h.Hour}:00"))}",
                Confidence = 0.8m,
                Frequency = hourlyActivity.Sum(h => h.Count),
                LastObserved = events.Max(e => e.Timestamp)
            });
        }

        // Analyze day-of-week patterns
        var dailyActivity = events.GroupBy(e => e.Timestamp.DayOfWeek)
                                 .Select(g => new { Day = g.Key, Count = g.Count() })
                                 .OrderByDescending(x => x.Count)
                                 .Take(2)
                                 .ToList();

        if (dailyActivity.Any())
        {
            patterns.Add(new BehaviorPatternDto
            {
                PatternType = "Peak Activity Days",
                Description = $"Most active on: {string.Join(", ", dailyActivity.Select(d => d.Day.ToString()))}",
                Confidence = 0.7m,
                Frequency = dailyActivity.Sum(d => d.Count),
                LastObserved = events.Max(e => e.Timestamp)
            });
        }

        return patterns;
    }

    private async Task<List<BehaviorPatternDto>> AnalyzeInteractionPatternsAsync(List<AnalyticsEvent> events)
    {
        var patterns = new List<BehaviorPatternDto>();

        // Analyze most common event sequences
        var eventSequences = new List<string>();
        var sortedEvents = events.OrderBy(e => e.Timestamp).ToList();

        for (int i = 0; i < sortedEvents.Count - 1; i++)
        {
            var sequence = $"{sortedEvents[i].EventName} -> {sortedEvents[i + 1].EventName}";
            eventSequences.Add(sequence);
        }

        var commonSequences = eventSequences.GroupBy(s => s)
                                           .Where(g => g.Count() >= 3)
                                           .OrderByDescending(g => g.Count())
                                           .Take(3)
                                           .ToList();

        foreach (var sequence in commonSequences)
        {
            patterns.Add(new BehaviorPatternDto
            {
                PatternType = "Common Event Sequence",
                Description = $"Frequently follows pattern: {sequence.Key}",
                Confidence = Math.Min(0.9m, (decimal)sequence.Count() / events.Count * 10),
                Frequency = sequence.Count(),
                LastObserved = events.Max(e => e.Timestamp)
            });
        }

        return patterns;
    }

    private async Task<List<BehaviorPatternDto>> AnalyzeConversionPatternsAsync(List<AnalyticsEvent> events)
    {
        var patterns = new List<BehaviorPatternDto>();

        // Analyze conversion timing patterns
        var conversionEvents = events.Where(e => e.EventName == "RFQConverted" || e.EventName == "OrderCreated").ToList();

        if (conversionEvents.Count >= 5)
        {
            var conversionHours = conversionEvents.Select(e => e.Timestamp.Hour).ToList();
            var mostCommonHour = conversionHours.GroupBy(h => h)
                                              .OrderByDescending(g => g.Count())
                                              .First();

            patterns.Add(new BehaviorPatternDto
            {
                PatternType = "Conversion Timing",
                Description = $"Most conversions occur around {mostCommonHour.Key}:00",
                Confidence = (decimal)mostCommonHour.Count() / conversionEvents.Count,
                Frequency = mostCommonHour.Count(),
                LastObserved = conversionEvents.Max(e => e.Timestamp)
            });
        }

        return patterns;
    }

    private async Task<List<AnalyticsEvent>> GetConversionEventsAsync(Guid? userId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken)
    {
        var conversionEventNames = new[] { "RFQCreated", "QuoteSubmitted", "QuoteAccepted", "OrderCreated", "RFQConverted" };

        var eventsResult = await _analyticsEventRepository.GetByDateRangeAsync(
            startDate, endDate, 1, int.MaxValue, cancellationToken);

        var events = eventsResult.Items
            .Where(e => conversionEventNames.Contains(e.EventName));

        if (userId.HasValue)
        {
            events = events.Where(e => e.UserId == userId.Value);
        }

        return events.OrderBy(e => e.Timestamp).ToList();
    }

    private async Task<List<ConversionTrendDataDto>> CalculateConversionTrendDataAsync(List<AnalyticsEvent> events, TimePeriod period, DateTime startDate, DateTime endDate)
    {
        var trendData = new List<ConversionTrendDataDto>();
        var current = startDate;

        while (current <= endDate)
        {
            var periodEnd = period switch
            {
                TimePeriod.Hourly => current.AddHours(1),
                TimePeriod.Daily => current.AddDays(1),
                TimePeriod.Weekly => current.AddDays(7),
                TimePeriod.Monthly => current.AddMonths(1),
                _ => current.AddDays(1)
            };

            var periodEvents = events.Where(e => e.Timestamp >= current && e.Timestamp < periodEnd).ToList();

            var rfqsCreated = periodEvents.Count(e => e.EventName == "RFQCreated");
            var quotesSubmitted = periodEvents.Count(e => e.EventName == "QuoteSubmitted");
            var ordersCreated = periodEvents.Count(e => e.EventName == "OrderCreated");

            trendData.Add(new ConversionTrendDataDto
            {
                Timestamp = current,
                RFQsCreated = rfqsCreated,
                QuotesSubmitted = quotesSubmitted,
                OrdersCreated = ordersCreated,
                ConversionRate = rfqsCreated > 0 ? (decimal)ordersCreated / rfqsCreated * 100 : 0,
                QuoteResponseRate = rfqsCreated > 0 ? (decimal)quotesSubmitted / rfqsCreated * 100 : 0
            });

            current = periodEnd;
        }

        return trendData;
    }

    private decimal CalculateMedian(List<decimal> values)
    {
        if (!values.Any()) return 0;

        var sorted = values.OrderBy(v => v).ToList();
        var count = sorted.Count;

        if (count % 2 == 0)
        {
            return (sorted[count / 2 - 1] + sorted[count / 2]) / 2;
        }
        else
        {
            return sorted[count / 2];
        }
    }

    private async Task InvalidateConversionCachesAsync(Guid? userId, CancellationToken cancellationToken)
    {
        var patterns = new[]
        {
            $"{CACHE_KEY_PREFIX}:conversion_analytics:*",
            $"{CACHE_KEY_PREFIX}:rfq_lifecycle:*",
            $"{CACHE_KEY_PREFIX}:funnel_analysis:*",
            $"{CACHE_KEY_PREFIX}:quote_timeliness:*",
            $"{CACHE_KEY_PREFIX}:behavior_patterns:*",
            $"{CACHE_KEY_PREFIX}:conversion_trends:*"
        };

        foreach (var pattern in patterns)
        {
            await _cacheService.RemoveByPatternAsync(pattern, cancellationToken);
        }
    }

    private string GenerateCacheKey(string prefix, params object?[] parameters)
    {
        var keyParts = new List<string> { CACHE_KEY_PREFIX, prefix };

        foreach (var param in parameters)
        {
            if (param != null)
            {
                keyParts.Add(param.ToString()!);
            }
        }

        return string.Join(":", keyParts);
    }
}
