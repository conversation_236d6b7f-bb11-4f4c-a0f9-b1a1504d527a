using FluentAssertions;
using FluentValidation.TestHelper;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Application.Validators;
using Xunit;

namespace NetworkFleetManagement.Tests.Application.Validators;

public class CreateCarrierDtoValidatorTests
{
    private readonly CreateCarrierDtoValidator _validator;

    public CreateCarrierDtoValidatorTests()
    {
        _validator = new CreateCarrierDtoValidator();
    }

    [Fact]
    public void Validate_WithValidDto_ShouldNotHaveValidationErrors()
    {
        // Arrange
        var dto = new CreateCarrierDto
        {
            UserId = Guid.NewGuid(),
            CompanyName = "Test Logistics",
            ContactPersonName = "John Doe",
            Email = "<EMAIL>",
            PhoneNumber = "+**********",
            BusinessLicenseNumber = "BL123456",
            TaxIdentificationNumber = "TIN123456",
            Notes = "Test carrier"
        };

        // Act
        var result = _validator.TestValidate(dto);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Fact]
    public void Validate_WithEmptyUserId_ShouldHaveValidationError()
    {
        // Arrange
        var dto = new CreateCarrierDto
        {
            UserId = Guid.Empty,
            CompanyName = "Test Logistics",
            ContactPersonName = "John Doe",
            Email = "<EMAIL>",
            PhoneNumber = "+**********"
        };

        // Act
        var result = _validator.TestValidate(dto);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.UserId)
            .WithErrorMessage("User ID is required");
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    public void Validate_WithInvalidCompanyName_ShouldHaveValidationError(string companyName)
    {
        // Arrange
        var dto = new CreateCarrierDto
        {
            UserId = Guid.NewGuid(),
            CompanyName = companyName,
            ContactPersonName = "John Doe",
            Email = "<EMAIL>",
            PhoneNumber = "+**********"
        };

        // Act
        var result = _validator.TestValidate(dto);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.CompanyName)
            .WithErrorMessage("Company name is required");
    }

    [Fact]
    public void Validate_WithTooLongCompanyName_ShouldHaveValidationError()
    {
        // Arrange
        var dto = new CreateCarrierDto
        {
            UserId = Guid.NewGuid(),
            CompanyName = new string('A', 201), // 201 characters
            ContactPersonName = "John Doe",
            Email = "<EMAIL>",
            PhoneNumber = "+**********"
        };

        // Act
        var result = _validator.TestValidate(dto);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.CompanyName)
            .WithErrorMessage("Company name cannot exceed 200 characters");
    }

    [Theory]
    [InlineData("invalid-email")]
    [InlineData("@invalid.com")]
    [InlineData("invalid@")]
    [InlineData("")]
    public void Validate_WithInvalidEmail_ShouldHaveValidationError(string email)
    {
        // Arrange
        var dto = new CreateCarrierDto
        {
            UserId = Guid.NewGuid(),
            CompanyName = "Test Logistics",
            ContactPersonName = "John Doe",
            Email = email,
            PhoneNumber = "+**********"
        };

        // Act
        var result = _validator.TestValidate(dto);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Email);
    }

    [Theory]
    [InlineData("123")]
    [InlineData("abc")]
    [InlineData("")]
    public void Validate_WithInvalidPhoneNumber_ShouldHaveValidationError(string phoneNumber)
    {
        // Arrange
        var dto = new CreateCarrierDto
        {
            UserId = Guid.NewGuid(),
            CompanyName = "Test Logistics",
            ContactPersonName = "John Doe",
            Email = "<EMAIL>",
            PhoneNumber = phoneNumber
        };

        // Act
        var result = _validator.TestValidate(dto);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.PhoneNumber);
    }

    [Fact]
    public void Validate_WithValidBusinessAddress_ShouldNotHaveValidationErrors()
    {
        // Arrange
        var dto = new CreateCarrierDto
        {
            UserId = Guid.NewGuid(),
            CompanyName = "Test Logistics",
            ContactPersonName = "John Doe",
            Email = "<EMAIL>",
            PhoneNumber = "+**********",
            BusinessAddress = new LocationDto
            {
                Latitude = 12.9716,
                Longitude = 77.5946,
                Address = "123 Test Street",
                City = "Bangalore",
                State = "Karnataka",
                Country = "India",
                PostalCode = "560001"
            }
        };

        // Act
        var result = _validator.TestValidate(dto);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Theory]
    [InlineData(-91)]
    [InlineData(91)]
    public void Validate_WithInvalidLatitude_ShouldHaveValidationError(double latitude)
    {
        // Arrange
        var dto = new CreateCarrierDto
        {
            UserId = Guid.NewGuid(),
            CompanyName = "Test Logistics",
            ContactPersonName = "John Doe",
            Email = "<EMAIL>",
            PhoneNumber = "+**********",
            BusinessAddress = new LocationDto
            {
                Latitude = latitude,
                Longitude = 77.5946
            }
        };

        // Act
        var result = _validator.TestValidate(dto);

        // Assert
        result.ShouldHaveValidationErrorFor("BusinessAddress.Latitude")
            .WithErrorMessage("Latitude must be between -90 and 90 degrees");
    }

    [Theory]
    [InlineData(-181)]
    [InlineData(181)]
    public void Validate_WithInvalidLongitude_ShouldHaveValidationError(double longitude)
    {
        // Arrange
        var dto = new CreateCarrierDto
        {
            UserId = Guid.NewGuid(),
            CompanyName = "Test Logistics",
            ContactPersonName = "John Doe",
            Email = "<EMAIL>",
            PhoneNumber = "+**********",
            BusinessAddress = new LocationDto
            {
                Latitude = 12.9716,
                Longitude = longitude
            }
        };

        // Act
        var result = _validator.TestValidate(dto);

        // Assert
        result.ShouldHaveValidationErrorFor("BusinessAddress.Longitude")
            .WithErrorMessage("Longitude must be between -180 and 180 degrees");
    }
}
