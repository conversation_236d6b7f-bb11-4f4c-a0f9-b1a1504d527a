using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Entities;
using NetworkFleetManagement.Domain.Repositories;
using NetworkFleetManagement.Infrastructure.Persistence;

namespace NetworkFleetManagement.Infrastructure.Repositories;

public class PreferredPartnerRepository : IPreferredPartnerRepository
{
    private readonly NetworkFleetDbContext _context;
    private readonly IMemoryCache _cache;
    private readonly ILogger<PreferredPartnerRepository> _logger;
    private readonly TimeSpan _cacheExpiration = TimeSpan.FromMinutes(15);

    public PreferredPartnerRepository(
        NetworkFleetDbContext context,
        IMemoryCache cache,
        ILogger<PreferredPartnerRepository> logger)
    {
        _context = context;
        _cache = cache;
        _logger = logger;
    }

    public async Task<PreferredPartner?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var cacheKey = $"preferred_partner_{id}";

        if (_cache.TryGetValue(cacheKey, out PreferredPartner? cachedPartner))
        {
            return cachedPartner;
        }

        var partner = await _context.PreferredPartners
            .AsNoTracking()
            .FirstOrDefaultAsync(p => p.Id == id, cancellationToken);

        if (partner != null)
        {
            _cache.Set(cacheKey, partner, _cacheExpiration);
        }

        return partner;
    }

    public async Task<IEnumerable<PreferredPartner>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        return await _context.PreferredPartners
            .AsNoTracking()
            .OrderBy(p => p.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task AddAsync(PreferredPartner preferredPartner, CancellationToken cancellationToken = default)
    {
        _context.PreferredPartners.Add(preferredPartner);
        await _context.SaveChangesAsync(cancellationToken);

        // Invalidate related caches
        InvalidateUserCache(preferredPartner.UserId);

        _logger.LogInformation("Added preferred partner {Id} for user {UserId}",
            preferredPartner.Id, preferredPartner.UserId);
    }

    public async Task UpdateAsync(PreferredPartner preferredPartner, CancellationToken cancellationToken = default)
    {
        _context.PreferredPartners.Update(preferredPartner);
        await _context.SaveChangesAsync(cancellationToken);

        // Invalidate caches
        _cache.Remove($"preferred_partner_{preferredPartner.Id}");
        InvalidateUserCache(preferredPartner.UserId);

        _logger.LogInformation("Updated preferred partner {Id} for user {UserId}",
            preferredPartner.Id, preferredPartner.UserId);
    }

    public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var partner = await _context.PreferredPartners.FindAsync(new object[] { id }, cancellationToken);
        if (partner != null)
        {
            _context.PreferredPartners.Remove(partner);
            await _context.SaveChangesAsync(cancellationToken);

            // Invalidate caches
            _cache.Remove($"preferred_partner_{id}");
            InvalidateUserCache(partner.UserId);

            _logger.LogInformation("Deleted preferred partner {Id} for user {UserId}",
                id, partner.UserId);
        }
    }

    public async Task<PreferredPartner?> GetByUserAndPartnerAsync(Guid userId, Guid partnerId, CancellationToken cancellationToken = default)
    {
        var cacheKey = $"preferred_partner_user_{userId}_partner_{partnerId}";

        if (_cache.TryGetValue(cacheKey, out PreferredPartner? cachedPartner))
        {
            return cachedPartner;
        }

        var partner = await _context.PreferredPartners
            .AsNoTracking()
            .FirstOrDefaultAsync(p => p.UserId == userId && p.PartnerId == partnerId, cancellationToken);

        if (partner != null)
        {
            _cache.Set(cacheKey, partner, _cacheExpiration);
        }

        return partner;
    }

    public async Task<IEnumerable<PreferredPartner>> GetByUserIdAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        var cacheKey = $"preferred_partners_user_{userId}";

        if (_cache.TryGetValue(cacheKey, out IEnumerable<PreferredPartner>? cachedPartners))
        {
            return cachedPartners!;
        }

        var partners = await _context.PreferredPartners
            .AsNoTracking()
            .Where(p => p.UserId == userId)
            .OrderBy(p => p.PreferenceLevel)
            .ThenBy(p => p.Priority)
            .ToListAsync(cancellationToken);

        _cache.Set(cacheKey, partners, _cacheExpiration);
        return partners;
    }

    public async Task<IEnumerable<PreferredPartner>> GetByPartnerIdAsync(Guid partnerId, CancellationToken cancellationToken = default)
    {
        return await _context.PreferredPartners
            .AsNoTracking()
            .Where(p => p.PartnerId == partnerId)
            .OrderBy(p => p.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<PreferredPartner>> GetByUserAndPartnerTypeAsync(Guid userId, PreferredPartnerType partnerType, CancellationToken cancellationToken = default)
    {
        var cacheKey = $"preferred_partners_user_{userId}_type_{partnerType}";

        if (_cache.TryGetValue(cacheKey, out IEnumerable<PreferredPartner>? cachedPartners))
        {
            return cachedPartners!;
        }

        var partners = await _context.PreferredPartners
            .AsNoTracking()
            .Where(p => p.UserId == userId && p.PartnerType == partnerType)
            .OrderBy(p => p.PreferenceLevel)
            .ThenBy(p => p.Priority)
            .ToListAsync(cancellationToken);

        _cache.Set(cacheKey, partners, TimeSpan.FromMinutes(10));
        return partners;
    }

    public async Task<IEnumerable<PreferredPartner>> GetByUserIdAndPartnerTypeAsync(Guid userId, PreferredPartnerType partnerType, bool activeOnly = true, CancellationToken cancellationToken = default)
    {
        var query = _context.PreferredPartners
            .AsNoTracking()
            .Where(p => p.UserId == userId && p.PartnerType == partnerType);

        if (activeOnly)
        {
            query = query.Where(p => p.Status == PreferenceStatus.Active);
        }

        return await query
            .OrderBy(p => p.PreferenceLevel)
            .ThenBy(p => p.Priority)
            .ThenByDescending(p => p.PerformanceMetrics.OverallRating)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<PreferredPartner>> GetByUserAndPreferenceLevelAsync(Guid userId, PreferenceLevel preferenceLevel, CancellationToken cancellationToken = default)
    {
        return await _context.PreferredPartners
            .AsNoTracking()
            .Where(p => p.UserId == userId && p.PreferenceLevel == preferenceLevel)
            .OrderBy(p => p.Priority)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<PreferredPartner>> GetActiveByUserIdAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        var cacheKey = $"active_preferred_partners_user_{userId}";

        if (_cache.TryGetValue(cacheKey, out IEnumerable<PreferredPartner>? cachedPartners))
        {
            return cachedPartners!;
        }

        var partners = await _context.PreferredPartners
            .AsNoTracking()
            .Where(p => p.UserId == userId && p.Status == PreferenceStatus.Active)
            .OrderBy(p => p.PreferenceLevel)
            .ThenBy(p => p.Priority)
            .ToListAsync(cancellationToken);

        _cache.Set(cacheKey, partners, TimeSpan.FromMinutes(10));
        return partners;
    }

    public async Task<IEnumerable<PreferredPartner>> GetActiveByPartnerIdAsync(Guid partnerId, CancellationToken cancellationToken = default)
    {
        return await _context.PreferredPartners
            .AsNoTracking()
            .Where(p => p.PartnerId == partnerId && p.Status == PreferenceStatus.Active)
            .OrderBy(p => p.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<PreferredPartner>> GetAutoAssignEligiblePartnersAsync(Guid userId, PreferredPartnerType partnerType, string? route = null, string? loadType = null, CancellationToken cancellationToken = default)
    {
        var query = _context.PreferredPartners
            .AsNoTracking()
            .Where(p => p.UserId == userId
                && p.PartnerType == partnerType
                && p.Status == PreferenceStatus.Active
                && p.AutoAssignEnabled);

        // Apply route filtering
        if (!string.IsNullOrEmpty(route))
        {
            query = query.Where(p => p.PreferredRoutes.Contains(route) || !p.ExcludedRoutes.Contains(route));
        }

        // Apply load type filtering
        if (!string.IsNullOrEmpty(loadType))
        {
            query = query.Where(p => p.PreferredLoadTypes.Contains(loadType) || !p.ExcludedLoadTypes.Contains(loadType));
        }

        return await query
            .OrderBy(p => p.PreferenceLevel)
            .ThenBy(p => p.Priority)
            .ThenByDescending(p => p.PerformanceMetrics.OverallRating)
            .ToListAsync(cancellationToken);
    }

    public async Task<int> GetTotalPreferredPartnersCountAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        var cacheKey = $"total_count_user_{userId}";

        if (_cache.TryGetValue(cacheKey, out int cachedCount))
        {
            return cachedCount;
        }

        var count = await _context.PreferredPartners
            .CountAsync(p => p.UserId == userId, cancellationToken);

        _cache.Set(cacheKey, count, TimeSpan.FromMinutes(30));
        return count;
    }

    public async Task<int> GetActivePreferredPartnersCountAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        var cacheKey = $"active_count_user_{userId}";

        if (_cache.TryGetValue(cacheKey, out int cachedCount))
        {
            return cachedCount;
        }

        var count = await _context.PreferredPartners
            .CountAsync(p => p.UserId == userId && p.Status == PreferenceStatus.Active, cancellationToken);

        _cache.Set(cacheKey, count, TimeSpan.FromMinutes(30));
        return count;
    }

    public async Task<decimal> GetAveragePartnerRatingAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        var partners = await GetActiveByUserIdAsync(userId, cancellationToken);
        return partners.Any() ? partners.Average(p => p.PerformanceMetrics.OverallRating) : 0;
    }

    public async Task<int> GetPreferredPartnersCountByTypeAsync(Guid userId, PreferredPartnerType partnerType, CancellationToken cancellationToken = default)
    {
        return await _context.PreferredPartners
            .CountAsync(p => p.UserId == userId && p.PartnerType == partnerType, cancellationToken);
    }

    public async Task<IEnumerable<PreferredPartner>> GetRecentlyActivePartnersAsync(Guid userId, DateTime since, CancellationToken cancellationToken = default)
    {
        return await _context.PreferredPartners
            .AsNoTracking()
            .Where(p => p.UserId == userId
                && p.Status == PreferenceStatus.Active
                && (p.LastCollaborationDate == null || p.LastCollaborationDate >= since))
            .OrderByDescending(p => p.LastCollaborationDate ?? p.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<PartnerAnalyticsDto?> GetPartnerAnalyticsAsync(Guid preferredPartnerId, DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default)
    {
        var partner = await GetByIdAsync(preferredPartnerId, cancellationToken);
        if (partner == null) return null;

        // This would typically integrate with analytics/reporting services
        // For now, return basic analytics based on the partner data
        return new PartnerAnalyticsDto
        {
            PreferredPartnerId = preferredPartnerId,
            PartnerId = partner.PartnerId,
            PartnerName = "Partner Name", // Would be fetched from partner service
            PartnerType = partner.PartnerType.ToString(),
            AnalysisPeriodStart = fromDate,
            AnalysisPeriodEnd = toDate,
            PerformanceMetrics = new PartnerPerformanceMetricsDto
            {
                OverallRating = partner.PerformanceMetrics.OverallRating,
                OnTimePerformance = partner.PerformanceMetrics.OnTimePerformance,
                QualityScore = partner.PerformanceMetrics.QualityScore,
                CommunicationRating = partner.PerformanceMetrics.CommunicationRating,
                TotalOrders = partner.PerformanceMetrics.TotalOrders,
                CompletedOrders = partner.PerformanceMetrics.CompletedOrders,
                CancelledOrders = partner.PerformanceMetrics.CancelledOrders,
                AverageResponseTime = partner.PerformanceMetrics.AverageResponseTime,
                LastUpdated = partner.PerformanceMetrics.LastUpdated
            },
            TotalCollaborations = partner.PerformanceMetrics.TotalOrders,
            CompletedCollaborations = partner.PerformanceMetrics.CompletedOrders,
            CompletionRate = partner.PerformanceMetrics.TotalOrders > 0
                ? (decimal)partner.PerformanceMetrics.CompletedOrders / partner.PerformanceMetrics.TotalOrders * 100
                : 0,
            TotalValue = 0, // Would be calculated from order/collaboration data
            AverageOrderValue = 0, // Would be calculated from order/collaboration data
            MonthlyBreakdown = new List<MonthlyCollaborationDto>(),
            RoutePerformance = new List<RoutePerformanceDto>(),
            LoadTypePerformance = new List<LoadTypePerformanceDto>(),
            PerformanceTrend = 0,
            TrendDescription = "Stable performance",
            Recommendations = new List<string>()
        };
    }

    public async Task AddPerformanceRecordAsync(PartnerPerformanceRecord record, CancellationToken cancellationToken = default)
    {
        // This would typically be implemented with a separate PerformanceRecord entity
        // For now, we'll update the partner's performance metrics directly
        var partner = await _context.PreferredPartners
            .FirstOrDefaultAsync(p => p.Id == record.PreferredPartnerId, cancellationToken);

        if (partner != null)
        {
            // Update performance metrics based on the record
            partner.UpdatePerformanceMetrics(
                record.Rating,
                record.OnTimeDelivery,
                record.QualityScore,
                record.CommunicationRating,
                record.ResponseTime);

            await UpdateAsync(partner, cancellationToken);
        }
    }

    public async Task<IEnumerable<PartnerPerformanceRecord>> GetRecentPerformanceChangesAsync(Guid userId, DateTime since, CancellationToken cancellationToken = default)
    {
        // This would typically query a separate performance records table
        // For now, return empty list as this would require additional infrastructure
        return new List<PartnerPerformanceRecord>();
    }

    private void InvalidateUserCache(Guid userId)
    {
        var patterns = new[]
        {
            $"preferred_partners_user_{userId}",
            $"active_preferred_partners_user_{userId}",
            $"preferred_partners_user_{userId}_type_",
            $"total_count_user_{userId}",
            $"active_count_user_{userId}"
        };

        foreach (var pattern in patterns)
        {
            // In a real implementation, you might use a more sophisticated cache invalidation strategy
            _cache.Remove(pattern);
        }
    }
}
