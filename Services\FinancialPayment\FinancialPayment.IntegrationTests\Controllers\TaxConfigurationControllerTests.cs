using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using FluentAssertions;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using FinancialPayment.Application.DTOs.TaxConfiguration;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Infrastructure.Data;
using Xunit;

namespace FinancialPayment.IntegrationTests.Controllers;

public class TaxConfigurationControllerTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;
    private readonly JsonSerializerOptions _jsonOptions;

    public TaxConfigurationControllerTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory;
        _client = _factory.CreateClient();
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
    }

    [Fact]
    public async Task GetTaxConfigurations_ShouldReturnOk_WithTaxConfigurations()
    {
        // Act
        var response = await _client.GetAsync("/api/tax-configurations");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var taxConfigurations = JsonSerializer.Deserialize<List<TaxConfigurationDto>>(content, _jsonOptions);
        
        taxConfigurations.Should().NotBeNull();
        taxConfigurations.Should().NotBeEmpty();
    }

    [Fact]
    public async Task GetTaxConfiguration_ShouldReturnOk_WhenTaxConfigurationExists()
    {
        // Arrange
        var createRequest = new CreateTaxConfigurationRequest
        {
            Name = "Test Tax Configuration",
            Description = "Test description",
            JurisdictionCountry = "India",
            JurisdictionState = "Maharashtra",
            JurisdictionCity = "Mumbai",
            JurisdictionType = LocationType.City,
            EffectiveFrom = DateTime.UtcNow,
            IsDefault = false,
            Priority = 1,
            EnableGstCalculation = true,
            EnableTdsCalculation = true,
            DefaultGstRate = 18.0m,
            DefaultTdsRate = 2.0m
        };

        var createResponse = await _client.PostAsJsonAsync("/api/tax-configurations", createRequest);
        createResponse.StatusCode.Should().Be(HttpStatusCode.Created);
        
        var createdTaxConfig = await createResponse.Content.ReadFromJsonAsync<TaxConfigurationDto>(_jsonOptions);

        // Act
        var response = await _client.GetAsync($"/api/tax-configurations/{createdTaxConfig!.Id}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var taxConfiguration = await response.Content.ReadFromJsonAsync<TaxConfigurationDto>(_jsonOptions);
        taxConfiguration.Should().NotBeNull();
        taxConfiguration!.Id.Should().Be(createdTaxConfig.Id);
        taxConfiguration.Name.Should().Be(createRequest.Name);
        taxConfiguration.Description.Should().Be(createRequest.Description);
    }

    [Fact]
    public async Task GetTaxConfiguration_ShouldReturnNotFound_WhenTaxConfigurationDoesNotExist()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid();

        // Act
        var response = await _client.GetAsync($"/api/tax-configurations/{nonExistentId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task CreateTaxConfiguration_ShouldReturnCreated_WithValidRequest()
    {
        // Arrange
        var request = new CreateTaxConfigurationRequest
        {
            Name = "New Tax Configuration",
            Description = "New tax configuration for testing",
            JurisdictionCountry = "India",
            JurisdictionState = "Karnataka",
            JurisdictionCity = "Bangalore",
            JurisdictionType = LocationType.City,
            EffectiveFrom = DateTime.UtcNow,
            EffectiveTo = DateTime.UtcNow.AddYears(1),
            IsDefault = false,
            Priority = 2,
            EnableGstCalculation = true,
            EnableTdsCalculation = true,
            EnableReverseCharge = false,
            RequireHsnCode = true,
            DefaultGstRate = 18.0m,
            DefaultTdsRate = 2.0m,
            DefaultCurrency = "INR"
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/tax-configurations", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Created);
        
        var taxConfiguration = await response.Content.ReadFromJsonAsync<TaxConfigurationDto>(_jsonOptions);
        taxConfiguration.Should().NotBeNull();
        taxConfiguration!.Name.Should().Be(request.Name);
        taxConfiguration.Description.Should().Be(request.Description);
        taxConfiguration.JurisdictionCountry.Should().Be(request.JurisdictionCountry);
        taxConfiguration.EnableGstCalculation.Should().Be(request.EnableGstCalculation);
        taxConfiguration.EnableTdsCalculation.Should().Be(request.EnableTdsCalculation);
        taxConfiguration.DefaultGstRate.Should().Be(request.DefaultGstRate);
        taxConfiguration.DefaultTdsRate.Should().Be(request.DefaultTdsRate);
        
        response.Headers.Location.Should().NotBeNull();
        response.Headers.Location!.ToString().Should().Contain($"/api/tax-configurations/{taxConfiguration.Id}");
    }

    [Fact]
    public async Task CreateTaxConfiguration_ShouldReturnBadRequest_WithInvalidRequest()
    {
        // Arrange
        var request = new CreateTaxConfigurationRequest
        {
            Name = "", // Invalid - empty name
            Description = "Test description",
            JurisdictionCountry = "India",
            JurisdictionType = LocationType.Country,
            EffectiveFrom = DateTime.UtcNow,
            Priority = 1
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/tax-configurations", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task UpdateTaxConfiguration_ShouldReturnOk_WithValidRequest()
    {
        // Arrange
        var createRequest = new CreateTaxConfigurationRequest
        {
            Name = "Original Tax Configuration",
            Description = "Original description",
            JurisdictionCountry = "India",
            JurisdictionType = LocationType.Country,
            EffectiveFrom = DateTime.UtcNow,
            Priority = 1,
            EnableGstCalculation = true,
            DefaultGstRate = 18.0m
        };

        var createResponse = await _client.PostAsJsonAsync("/api/tax-configurations", createRequest);
        var createdTaxConfig = await createResponse.Content.ReadFromJsonAsync<TaxConfigurationDto>(_jsonOptions);

        var updateRequest = new UpdateTaxConfigurationRequest
        {
            Name = "Updated Tax Configuration",
            Description = "Updated description",
            JurisdictionCountry = "India",
            JurisdictionState = "Maharashtra",
            JurisdictionType = LocationType.State,
            EffectiveFrom = DateTime.UtcNow,
            Priority = 2,
            EnableGstCalculation = true,
            EnableTdsCalculation = true,
            DefaultGstRate = 12.0m,
            DefaultTdsRate = 1.0m
        };

        // Act
        var response = await _client.PutAsJsonAsync($"/api/tax-configurations/{createdTaxConfig!.Id}", updateRequest);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var updatedTaxConfig = await response.Content.ReadFromJsonAsync<TaxConfigurationDto>(_jsonOptions);
        updatedTaxConfig.Should().NotBeNull();
        updatedTaxConfig!.Name.Should().Be(updateRequest.Name);
        updatedTaxConfig.Description.Should().Be(updateRequest.Description);
        updatedTaxConfig.JurisdictionState.Should().Be(updateRequest.JurisdictionState);
        updatedTaxConfig.Priority.Should().Be(updateRequest.Priority);
        updatedTaxConfig.DefaultGstRate.Should().Be(updateRequest.DefaultGstRate);
        updatedTaxConfig.DefaultTdsRate.Should().Be(updateRequest.DefaultTdsRate);
    }

    [Fact]
    public async Task UpdateTaxConfiguration_ShouldReturnNotFound_WhenTaxConfigurationDoesNotExist()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid();
        var updateRequest = new UpdateTaxConfigurationRequest
        {
            Name = "Updated Tax Configuration",
            Description = "Updated description",
            JurisdictionCountry = "India",
            JurisdictionType = LocationType.Country,
            EffectiveFrom = DateTime.UtcNow,
            Priority = 1
        };

        // Act
        var response = await _client.PutAsJsonAsync($"/api/tax-configurations/{nonExistentId}", updateRequest);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task DeleteTaxConfiguration_ShouldReturnNoContent_WhenTaxConfigurationExists()
    {
        // Arrange
        var createRequest = new CreateTaxConfigurationRequest
        {
            Name = "Tax Configuration to Delete",
            Description = "This will be deleted",
            JurisdictionCountry = "India",
            JurisdictionType = LocationType.Country,
            EffectiveFrom = DateTime.UtcNow,
            Priority = 1
        };

        var createResponse = await _client.PostAsJsonAsync("/api/tax-configurations", createRequest);
        var createdTaxConfig = await createResponse.Content.ReadFromJsonAsync<TaxConfigurationDto>(_jsonOptions);

        // Act
        var response = await _client.DeleteAsync($"/api/tax-configurations/{createdTaxConfig!.Id}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NoContent);
        
        // Verify the tax configuration is actually deleted
        var getResponse = await _client.GetAsync($"/api/tax-configurations/{createdTaxConfig.Id}");
        getResponse.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task DeleteTaxConfiguration_ShouldReturnNotFound_WhenTaxConfigurationDoesNotExist()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid();

        // Act
        var response = await _client.DeleteAsync($"/api/tax-configurations/{nonExistentId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task SetAsDefault_ShouldReturnOk_WhenTaxConfigurationExists()
    {
        // Arrange
        var createRequest = new CreateTaxConfigurationRequest
        {
            Name = "Tax Configuration for Default",
            Description = "This will be set as default",
            JurisdictionCountry = "India",
            JurisdictionType = LocationType.Country,
            EffectiveFrom = DateTime.UtcNow,
            Priority = 1,
            IsDefault = false
        };

        var createResponse = await _client.PostAsJsonAsync("/api/tax-configurations", createRequest);
        var createdTaxConfig = await createResponse.Content.ReadFromJsonAsync<TaxConfigurationDto>(_jsonOptions);

        // Act
        var response = await _client.PostAsync($"/api/tax-configurations/{createdTaxConfig!.Id}/set-default", null);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        // Verify the tax configuration is now default
        var getResponse = await _client.GetAsync($"/api/tax-configurations/{createdTaxConfig.Id}");
        var updatedTaxConfig = await getResponse.Content.ReadFromJsonAsync<TaxConfigurationDto>(_jsonOptions);
        updatedTaxConfig!.IsDefault.Should().BeTrue();
    }

    [Fact]
    public async Task CalculateTax_ShouldReturnOk_WithValidRequest()
    {
        // Arrange
        var request = new TaxCalculationRequest
        {
            BaseAmount = 10000m,
            Currency = "INR",
            ServiceCategory = ServiceCategory.Transportation,
            JurisdictionCountry = "India",
            JurisdictionState = "Maharashtra",
            JurisdictionCity = "Mumbai",
            JurisdictionType = LocationType.City,
            EntityType = EntityType.Company,
            TdsSection = TdsSection.Section194C,
            HsnCode = "9967",
            HasPan = true
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/tax-configurations/calculate", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var result = await response.Content.ReadFromJsonAsync<TaxCalculationResultDto>(_jsonOptions);
        result.Should().NotBeNull();
        result!.BaseAmount.Should().Be(10000m);
        result.GstAmount.Should().BeGreaterThan(0);
        result.NetAmount.Should().BeGreaterThan(result.BaseAmount);
        result.EffectiveGstRate.Should().BeGreaterThan(0);
        result.AppliedRules.Should().NotBeEmpty();
    }

    [Fact]
    public async Task CalculateTax_ShouldReturnBadRequest_WithInvalidRequest()
    {
        // Arrange
        var request = new TaxCalculationRequest
        {
            BaseAmount = 0, // Invalid - zero amount
            Currency = "INR",
            ServiceCategory = ServiceCategory.Transportation,
            JurisdictionCountry = "India",
            JurisdictionType = LocationType.Country,
            EntityType = EntityType.Company
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/tax-configurations/calculate", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (disposing)
        {
            _client?.Dispose();
        }
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
}
