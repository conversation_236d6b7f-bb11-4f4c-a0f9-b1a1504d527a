using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SubscriptionManagement.Domain.Entities;

namespace SubscriptionManagement.Infrastructure.Configurations
{
    public class SubscriptionChangeConfiguration : IEntityTypeConfiguration<SubscriptionChange>
    {
        public void Configure(EntityTypeBuilder<SubscriptionChange> builder)
        {
            builder.ToTable("SubscriptionChanges");

            builder.HasKey(sc => sc.Id);

            builder.Property(sc => sc.ChangeType)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(sc => sc.Description)
                .HasMaxLength(500);

            builder.Property(sc => sc.ChangedAt)
                .IsRequired();

            builder.Property(sc => sc.CreatedAt)
                .IsRequired();

            builder.Property(sc => sc.UpdatedAt);

            // Indexes
            builder.HasIndex("SubscriptionId");
            builder.HasIndex(sc => sc.ChangedAt);
        }
    }
}
