using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class TaxController : ControllerBase
{
    private readonly ITaxCalculationService _taxCalculationService;
    private readonly ITaxRuleRepository _taxRuleRepository;
    private readonly ILogger<TaxController> _logger;

    public TaxController(
        ITaxCalculationService taxCalculationService,
        ITaxRuleRepository taxRuleRepository,
        ILogger<TaxController> logger)
    {
        _taxCalculationService = taxCalculationService;
        _taxRuleRepository = taxRuleRepository;
        _logger = logger;
    }

    /// <summary>
    /// Calculate tax for an order
    /// </summary>
    [HttpPost("calculate")]
    [Authorize(Roles = "Admin,Broker,TransportCompany")]
    public async Task<ActionResult<TaxCalculation>> CalculateTax([FromBody] CalculateTaxRequest request)
    {
        try
        {
            var taxRequest = new TaxCalculationRequest
            {
                OrderId = request.OrderId,
                UserId = request.UserId,
                Amount = new Money(request.Amount, request.Currency),
                Jurisdiction = request.Jurisdiction,
                ProductCategory = request.ProductCategory,
                ServiceType = request.ServiceType,
                CalculationDate = request.CalculationDate,
                Context = request.Context ?? new Dictionary<string, object>(),
                ForceRecalculation = request.ForceRecalculation
            };

            var result = await _taxCalculationService.CalculateTaxAsync(taxRequest);
            
            _logger.LogInformation("Tax calculated for order {OrderId}: {TotalTax}",
                request.OrderId, result.TotalTaxAmount);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating tax for order {OrderId}", request.OrderId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get tax calculation summary for an order
    /// </summary>
    [HttpGet("summary/{orderId}")]
    public async Task<ActionResult<TaxCalculationSummary>> GetTaxSummary(Guid orderId)
    {
        try
        {
            var summary = await _taxCalculationService.GetTaxSummaryAsync(orderId);
            return Ok(summary);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Tax calculation not found for order {OrderId}", orderId);
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tax summary for order {OrderId}", orderId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get tax calculation history for an order
    /// </summary>
    [HttpGet("history/{orderId}")]
    public async Task<ActionResult<List<TaxCalculation>>> GetTaxHistory(Guid orderId)
    {
        try
        {
            var history = await _taxCalculationService.GetTaxCalculationHistoryAsync(orderId);
            return Ok(history);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tax history for order {OrderId}", orderId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get applicable tax rules for a jurisdiction
    /// </summary>
    [HttpGet("rules/{jurisdiction}")]
    public async Task<ActionResult<List<TaxRule>>> GetApplicableTaxRules(
        string jurisdiction,
        [FromQuery] DateTime? date = null,
        [FromQuery] string? productCategory = null,
        [FromQuery] string? serviceType = null)
    {
        try
        {
            var effectiveDate = date ?? DateTime.UtcNow;
            var rules = await _taxCalculationService.GetApplicableTaxRulesAsync(
                jurisdiction, effectiveDate, productCategory, serviceType);
            
            return Ok(rules);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tax rules for jurisdiction {Jurisdiction}", jurisdiction);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Recalculate tax for an existing calculation
    /// </summary>
    [HttpPost("recalculate/{taxCalculationId}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<TaxCalculation>> RecalculateTax(Guid taxCalculationId)
    {
        try
        {
            var result = await _taxCalculationService.RecalculateTaxAsync(taxCalculationId);
            
            _logger.LogInformation("Tax recalculated for calculation {TaxCalculationId}", taxCalculationId);
            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Tax calculation not found {TaxCalculationId}", taxCalculationId);
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recalculating tax for calculation {TaxCalculationId}", taxCalculationId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Validate a tax calculation
    /// </summary>
    [HttpPost("validate/{taxCalculationId}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<bool>> ValidateTaxCalculation(Guid taxCalculationId)
    {
        try
        {
            var isValid = await _taxCalculationService.ValidateTaxCalculationAsync(taxCalculationId);
            return Ok(isValid);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating tax calculation {TaxCalculationId}", taxCalculationId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Create a new tax rule
    /// </summary>
    [HttpPost("rules")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<TaxRule>> CreateTaxRule([FromBody] CreateTaxRuleRequest request)
    {
        try
        {
            var taxRule = new TaxRule(
                request.Name,
                request.Description,
                request.Jurisdiction,
                request.TaxType,
                request.Rate,
                new Money(request.MinimumAmount, request.Currency),
                new Money(request.MaximumAmount, request.Currency),
                request.EffectiveFrom,
                request.CalculationType,
                request.Priority);

            // Add conditions if provided
            if (request.Conditions != null)
            {
                foreach (var condition in request.Conditions)
                {
                    taxRule.AddCondition(condition.Field, condition.Operator, condition.Value, condition.Description);
                }
            }

            await _taxRuleRepository.AddAsync(taxRule);
            
            _logger.LogInformation("Tax rule created: {TaxRuleName} for jurisdiction {Jurisdiction}",
                request.Name, request.Jurisdiction);

            return CreatedAtAction(nameof(GetTaxRule), new { id = taxRule.Id }, taxRule);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating tax rule {TaxRuleName}", request.Name);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get a tax rule by ID
    /// </summary>
    [HttpGet("rules/{id}")]
    public async Task<ActionResult<TaxRule>> GetTaxRule(Guid id)
    {
        try
        {
            var taxRule = await _taxRuleRepository.GetByIdAsync(id);
            if (taxRule == null)
            {
                return NotFound($"Tax rule {id} not found");
            }

            return Ok(taxRule);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tax rule {TaxRuleId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get all active tax rules
    /// </summary>
    [HttpGet("rules")]
    public async Task<ActionResult<List<TaxRule>>> GetAllTaxRules()
    {
        try
        {
            var taxRules = await _taxRuleRepository.GetAllActiveAsync();
            return Ok(taxRules);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all tax rules");
            return StatusCode(500, "Internal server error");
        }
    }
}

// Request DTOs
public class CalculateTaxRequest
{
    public Guid OrderId { get; set; }
    public Guid? UserId { get; set; }
    public decimal Amount { get; set; }
    public string Currency { get; set; } = "INR";
    public string Jurisdiction { get; set; } = string.Empty;
    public string? ProductCategory { get; set; }
    public string? ServiceType { get; set; }
    public DateTime? CalculationDate { get; set; }
    public Dictionary<string, object>? Context { get; set; }
    public bool ForceRecalculation { get; set; } = false;
}

public class CreateTaxRuleRequest
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Jurisdiction { get; set; } = string.Empty;
    public string TaxType { get; set; } = string.Empty;
    public decimal Rate { get; set; }
    public decimal MinimumAmount { get; set; }
    public decimal MaximumAmount { get; set; } = decimal.MaxValue;
    public string Currency { get; set; } = "INR";
    public DateTime EffectiveFrom { get; set; } = DateTime.UtcNow;
    public TaxCalculationType CalculationType { get; set; } = TaxCalculationType.Percentage;
    public int Priority { get; set; } = 0;
    public List<TaxRuleConditionRequest>? Conditions { get; set; }
}

public class TaxRuleConditionRequest
{
    public string Field { get; set; } = string.Empty;
    public string Operator { get; set; } = string.Empty;
    public string Value { get; set; } = string.Empty;
    public string? Description { get; set; }
}
