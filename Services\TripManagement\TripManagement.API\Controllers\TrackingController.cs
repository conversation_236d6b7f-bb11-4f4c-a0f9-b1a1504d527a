using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using TripManagement.Application.Commands.OptimizeRoute;
using TripManagement.Application.Commands.ProcessGeofenceEvent;
using TripManagement.Application.Commands.UpdateETA;
using TripManagement.Application.DTOs;

namespace TripManagement.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class TrackingController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<TrackingController> _logger;

    public TrackingController(IMediator mediator, ILogger<TrackingController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Optimize route for a trip
    /// </summary>
    [HttpPost("optimize-route")]
    [Authorize(Roles = "Admin,Carrier,Driver")]
    public async Task<ActionResult<RouteOptimizationResult>> OptimizeRoute([FromBody] OptimizeRouteRequest request)
    {
        try
        {
            var command = new OptimizeRouteCommand
            {
                TripId = request.TripId,
                Waypoints = request.Waypoints,
                Criteria = request.Criteria,
                AvoidTolls = request.AvoidTolls,
                AvoidHighways = request.AvoidHighways,
                VehicleConstraints = request.VehicleConstraints
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid argument while optimizing route for trip {TripId}", request.TripId);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error optimizing route for trip {TripId}", request.TripId);
            return StatusCode(500, "An error occurred while optimizing the route");
        }
    }

    /// <summary>
    /// Process geofence event
    /// </summary>
    [HttpPost("geofence-event")]
    [Authorize(Roles = "Driver,System")]
    public async Task<ActionResult> ProcessGeofenceEvent([FromBody] ProcessGeofenceEventRequest request)
    {
        try
        {
            var command = new ProcessGeofenceEventCommand
            {
                TripId = request.TripId,
                DriverId = request.DriverId,
                CurrentLocation = request.CurrentLocation,
                EventType = request.EventType,
                GeofenceName = request.GeofenceName,
                EventTime = request.EventTime
            };

            var result = await _mediator.Send(command);

            if (result)
                return Ok(new { Message = "Geofence event processed successfully" });

            return BadRequest("Failed to process geofence event");
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid argument while processing geofence event for trip {TripId}", 
                request.TripId);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing geofence event for trip {TripId}", request.TripId);
            return StatusCode(500, "An error occurred while processing the geofence event");
        }
    }

    /// <summary>
    /// Update ETA for a trip
    /// </summary>
    [HttpPost("update-eta")]
    [Authorize(Roles = "Driver,System")]
    public async Task<ActionResult<ETAUpdateResult>> UpdateETA([FromBody] UpdateETARequest request)
    {
        try
        {
            var command = new UpdateETACommand
            {
                TripId = request.TripId,
                CurrentLocation = request.CurrentLocation,
                CurrentSpeed = request.CurrentSpeed,
                ConsiderTraffic = request.ConsiderTraffic,
                ConsiderWeather = request.ConsiderWeather,
                ConsiderDriverBehavior = request.ConsiderDriverBehavior
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid argument while updating ETA for trip {TripId}", request.TripId);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating ETA for trip {TripId}", request.TripId);
            return StatusCode(500, "An error occurred while updating the ETA");
        }
    }
}

// Request DTOs
public record OptimizeRouteRequest
{
    public Guid TripId { get; init; }
    public List<LocationDto> Waypoints { get; init; } = new();
    public OptimizationCriteria Criteria { get; init; } = OptimizationCriteria.Distance;
    public bool AvoidTolls { get; init; }
    public bool AvoidHighways { get; init; }
    public VehicleConstraints? VehicleConstraints { get; init; }
}

public record ProcessGeofenceEventRequest
{
    public Guid TripId { get; init; }
    public Guid DriverId { get; init; }
    public LocationDto CurrentLocation { get; init; } = null!;
    public GeofenceEventType EventType { get; init; }
    public string GeofenceName { get; init; } = string.Empty;
    public DateTime EventTime { get; init; }
}

public record UpdateETARequest
{
    public Guid TripId { get; init; }
    public LocationDto CurrentLocation { get; init; } = null!;
    public double? CurrentSpeed { get; init; }
    public bool ConsiderTraffic { get; init; } = true;
    public bool ConsiderWeather { get; init; } = true;
    public bool ConsiderDriverBehavior { get; init; } = true;
}
