using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;

namespace OrderManagement.Application.EventHandlers;

// Integration event from User Management Service
public record UserRegisteredIntegrationEvent : INotification
{
    public Guid UserId { get; init; }
    public string Email { get; init; } = string.Empty;
    public string UserType { get; init; } = string.Empty; // TransportCompany, Broker, Carrier
    public string CompanyName { get; init; } = string.Empty;
    public DateTime RegisteredAt { get; init; }
    public bool IsVerified { get; init; }
}

public record UserVerifiedIntegrationEvent : INotification
{
    public Guid UserId { get; init; }
    public string Email { get; init; } = string.Empty;
    public string UserType { get; init; } = string.Empty;
    public DateTime VerifiedAt { get; init; }
}

public record UserSuspendedIntegrationEvent : INotification
{
    public Guid UserId { get; init; }
    public string Email { get; init; } = string.Empty;
    public string UserType { get; init; } = string.Empty;
    public string Reason { get; init; } = string.Empty;
    public DateTime SuspendedAt { get; init; }
}

public class UserRegisteredEventHandler : INotificationHandler<UserRegisteredIntegrationEvent>
{
    private readonly ILogger<UserRegisteredEventHandler> _logger;

    public UserRegisteredEventHandler(ILogger<UserRegisteredEventHandler> logger)
    {
        _logger = logger;
    }

    public async Task Handle(UserRegisteredIntegrationEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing user registered event for user {UserId} ({UserType}): {Email}", 
            notification.UserId, notification.UserType, notification.Email);

        try
        {
            // For now, we just log the event
            // In a more complex system, you might want to:
            // - Create user-specific caches
            // - Initialize user preferences
            // - Send welcome notifications
            // - Set up default configurations

            _logger.LogInformation("User {UserId} of type {UserType} registered successfully", 
                notification.UserId, notification.UserType);

            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing user registered event for user {UserId}", notification.UserId);
            throw;
        }
    }
}

public class UserVerifiedEventHandler : INotificationHandler<UserVerifiedIntegrationEvent>
{
    private readonly ILogger<UserVerifiedEventHandler> _logger;

    public UserVerifiedEventHandler(ILogger<UserVerifiedEventHandler> logger)
    {
        _logger = logger;
    }

    public async Task Handle(UserVerifiedIntegrationEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing user verified event for user {UserId} ({UserType}): {Email}", 
            notification.UserId, notification.UserType, notification.Email);

        try
        {
            // For now, we just log the event
            // In a more complex system, you might want to:
            // - Enable full access to features
            // - Remove verification-related restrictions
            // - Send verification confirmation notifications

            _logger.LogInformation("User {UserId} of type {UserType} verified successfully", 
                notification.UserId, notification.UserType);

            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing user verified event for user {UserId}", notification.UserId);
            throw;
        }
    }
}

public class UserSuspendedEventHandler : INotificationHandler<UserSuspendedIntegrationEvent>
{
    private readonly IOrderRepository _orderRepository;
    private readonly IRfqRepository _rfqRepository;
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<UserSuspendedEventHandler> _logger;

    public UserSuspendedEventHandler(
        IOrderRepository orderRepository,
        IRfqRepository rfqRepository,
        IMessageBroker messageBroker,
        ILogger<UserSuspendedEventHandler> logger)
    {
        _orderRepository = orderRepository;
        _rfqRepository = rfqRepository;
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task Handle(UserSuspendedIntegrationEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing user suspended event for user {UserId} ({UserType}): {Email}, Reason: {Reason}", 
            notification.UserId, notification.UserType, notification.Email, notification.Reason);

        try
        {
            // Handle user suspension based on user type
            switch (notification.UserType.ToLower())
            {
                case "transportcompany":
                    await HandleTransportCompanySuspension(notification, cancellationToken);
                    break;

                case "broker":
                    await HandleBrokerSuspension(notification, cancellationToken);
                    break;

                case "carrier":
                    await HandleCarrierSuspension(notification, cancellationToken);
                    break;

                default:
                    _logger.LogWarning("Unknown user type {UserType} for suspended user {UserId}", 
                        notification.UserType, notification.UserId);
                    break;
            }

            _logger.LogInformation("Successfully processed user suspended event for user {UserId}", notification.UserId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing user suspended event for user {UserId}", notification.UserId);
            throw;
        }
    }

    private async Task HandleTransportCompanySuspension(UserSuspendedIntegrationEvent notification, CancellationToken cancellationToken)
    {
        // Get active orders for the transport company
        var activeOrders = await _orderRepository.GetByTransportCompanyIdAsync(notification.UserId, cancellationToken);
        var ordersToCancel = activeOrders.Where(o => 
            o.Status != Domain.Enums.OrderStatus.Completed && 
            o.Status != Domain.Enums.OrderStatus.Cancelled).ToList();

        foreach (var order in ordersToCancel)
        {
            order.Cancel($"Transport company suspended: {notification.Reason}");
            await _orderRepository.UpdateAsync(order, cancellationToken);

            // Publish order cancellation event
            await _messageBroker.PublishAsync("order.cancelled.user.suspended", new
            {
                OrderId = order.Id,
                OrderNumber = order.OrderNumber,
                SuspendedUserId = notification.UserId,
                SuspensionReason = notification.Reason,
                CancelledAt = order.CancelledAt
            }, cancellationToken);
        }

        _logger.LogInformation("Cancelled {Count} active orders for suspended transport company {UserId}", 
            ordersToCancel.Count, notification.UserId);
    }

    private async Task HandleBrokerSuspension(UserSuspendedIntegrationEvent notification, CancellationToken cancellationToken)
    {
        // Get active orders for the broker
        var activeOrders = await _orderRepository.GetByBrokerIdAsync(notification.UserId, cancellationToken);
        var ordersToCancel = activeOrders.Where(o => 
            o.Status != Domain.Enums.OrderStatus.Completed && 
            o.Status != Domain.Enums.OrderStatus.Cancelled).ToList();

        foreach (var order in ordersToCancel)
        {
            order.Cancel($"Broker suspended: {notification.Reason}");
            await _orderRepository.UpdateAsync(order, cancellationToken);

            // Publish order cancellation event
            await _messageBroker.PublishAsync("order.cancelled.user.suspended", new
            {
                OrderId = order.Id,
                OrderNumber = order.OrderNumber,
                SuspendedUserId = notification.UserId,
                SuspensionReason = notification.Reason,
                CancelledAt = order.CancelledAt
            }, cancellationToken);
        }

        _logger.LogInformation("Cancelled {Count} active orders for suspended broker {UserId}", 
            ordersToCancel.Count, notification.UserId);
    }

    private async Task HandleCarrierSuspension(UserSuspendedIntegrationEvent notification, CancellationToken cancellationToken)
    {
        // Get active orders for the carrier
        var activeOrders = await _orderRepository.GetByCarrierIdAsync(notification.UserId, cancellationToken);
        var ordersToCancel = activeOrders.Where(o => 
            o.Status == Domain.Enums.OrderStatus.Confirmed || 
            o.Status == Domain.Enums.OrderStatus.InProgress).ToList();

        foreach (var order in ordersToCancel)
        {
            order.Cancel($"Carrier suspended: {notification.Reason}");
            await _orderRepository.UpdateAsync(order, cancellationToken);

            // Publish order cancellation event
            await _messageBroker.PublishAsync("order.cancelled.user.suspended", new
            {
                OrderId = order.Id,
                OrderNumber = order.OrderNumber,
                SuspendedUserId = notification.UserId,
                SuspensionReason = notification.Reason,
                CancelledAt = order.CancelledAt
            }, cancellationToken);
        }

        _logger.LogInformation("Cancelled {Count} active orders for suspended carrier {UserId}", 
            ordersToCancel.Count, notification.UserId);
    }
}
