using AuditCompliance.Domain.Enums;

namespace AuditCompliance.Application.DTOs.Reporting;

/// <summary>
/// Report schedule DTO
/// </summary>
public class ReportScheduleDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public Guid TemplateId { get; set; }
    public string TemplateName { get; set; } = string.Empty;
    public string Schedule { get; set; } = string.Empty; // Cron expression
    public bool IsActive { get; set; }
    public DateTime? LastExecuted { get; set; }
    public DateTime? NextExecution { get; set; }
    public string OutputFormat { get; set; } = string.Empty;
    public List<string> Recipients { get; set; } = new();
    public Dictionary<string, object> Parameters { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public Guid? TenantId { get; set; }
}

/// <summary>
/// Create report schedule DTO
/// </summary>
public class CreateReportScheduleDto
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public Guid TemplateId { get; set; }
    public string Schedule { get; set; } = string.Empty; // Cron expression
    public bool IsActive { get; set; } = true;
    public string OutputFormat { get; set; } = "PDF";
    public List<string> Recipients { get; set; } = new();
    public Dictionary<string, object> Parameters { get; set; } = new();
}

/// <summary>
/// Update report schedule DTO
/// </summary>
public class UpdateReportScheduleDto
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Schedule { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public string OutputFormat { get; set; } = string.Empty;
    public List<string> Recipients { get; set; } = new();
    public Dictionary<string, object> Parameters { get; set; } = new();
}

/// <summary>
/// Report template DTO
/// </summary>
public class ReportTemplateDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty; // Standard, Custom, Regulatory
    public string Content { get; set; } = string.Empty; // Template content (HTML, JSON, etc.)
    public List<string> DataSources { get; set; } = new();
    public List<ReportParameterDto> Parameters { get; set; } = new();
    public List<string> SupportedFormats { get; set; } = new();
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime? UpdatedAt { get; set; }
    public string? UpdatedBy { get; set; }
    public Guid? TenantId { get; set; }
}

/// <summary>
/// Create report template DTO
/// </summary>
public class CreateReportTemplateDto
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string Type { get; set; } = "Custom";
    public string Content { get; set; } = string.Empty;
    public List<string> DataSources { get; set; } = new();
    public List<ReportParameterDto> Parameters { get; set; } = new();
    public List<string> SupportedFormats { get; set; } = new();
}

/// <summary>
/// Update report template DTO
/// </summary>
public class UpdateReportTemplateDto
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public List<string> DataSources { get; set; } = new();
    public List<ReportParameterDto> Parameters { get; set; } = new();
    public List<string> SupportedFormats { get; set; } = new();
    public bool IsActive { get; set; }
}

/// <summary>
/// Report parameter DTO
/// </summary>
public class ReportParameterDto
{
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty; // String, Number, Date, Boolean, List
    public bool IsRequired { get; set; }
    public object? DefaultValue { get; set; }
    public List<string>? AllowedValues { get; set; }
    public string? ValidationPattern { get; set; }
    public string? Description { get; set; }
}

/// <summary>
/// Generate report DTO
/// </summary>
public class GenerateReportDto
{
    public Guid TemplateId { get; set; }
    public string OutputFormat { get; set; } = "PDF";
    public Dictionary<string, object> Parameters { get; set; } = new();
    public List<string>? Recipients { get; set; }
    public bool SendNotification { get; set; } = false;
}

/// <summary>
/// Report execution DTO
/// </summary>
public class ReportExecutionDto
{
    public Guid Id { get; set; }
    public Guid? ScheduleId { get; set; }
    public string ScheduleName { get; set; } = string.Empty;
    public Guid TemplateId { get; set; }
    public string TemplateName { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty; // Pending, Running, Completed, Failed
    public DateTime StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string? ErrorMessage { get; set; }
    public string OutputFormat { get; set; } = string.Empty;
    public long? FileSizeBytes { get; set; }
    public string? FilePath { get; set; }
    public Dictionary<string, object> Parameters { get; set; } = new();
    public string ExecutedBy { get; set; } = string.Empty;
    public Guid? TenantId { get; set; }
}

/// <summary>
/// Generated report DTO
/// </summary>
public class GeneratedReportDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public Guid TemplateId { get; set; }
    public string TemplateName { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime GeneratedAt { get; set; }
    public string GeneratedBy { get; set; } = string.Empty;
    public string OutputFormat { get; set; } = string.Empty;
    public long FileSizeBytes { get; set; }
    public string? FilePath { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
    public List<string> Recipients { get; set; } = new();
    public bool IsDistributed { get; set; }
    public DateTime? ExpiresAt { get; set; }
}

/// <summary>
/// Report file DTO
/// </summary>
public class ReportFileDto
{
    public string FileName { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    public byte[] Content { get; set; } = Array.Empty<byte>();
    public long Size { get; set; }
    public DateTime GeneratedAt { get; set; }
}

/// <summary>
/// Export report DTO
/// </summary>
public class ExportReportDto
{
    public Guid ReportId { get; set; }
    public string Format { get; set; } = string.Empty; // PDF, Excel, CSV, JSON
    public Dictionary<string, object> Options { get; set; } = new();
    public string? Password { get; set; }
    public bool IncludeMetadata { get; set; } = true;
}

/// <summary>
/// Export status DTO
/// </summary>
public class ExportStatusDto
{
    public Guid ExportId { get; set; }
    public string Status { get; set; } = string.Empty; // Pending, Processing, Completed, Failed
    public int ProgressPercentage { get; set; }
    public DateTime StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string? ErrorMessage { get; set; }
    public string? DownloadUrl { get; set; }
    public DateTime? ExpiresAt { get; set; }
}

/// <summary>
/// Compliance metrics request DTO
/// </summary>
public class ComplianceMetricsRequestDto
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public List<ComplianceStandard>? Standards { get; set; }
    public List<string>? EntityTypes { get; set; }
    public Guid? OrganizationId { get; set; }
    public string? GroupBy { get; set; } // Day, Week, Month, Quarter, Year
    public bool IncludeDetails { get; set; } = false;
}

/// <summary>
/// Compliance metrics DTO
/// </summary>
public class ComplianceMetricsDto
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public ComplianceOverviewMetricsDto Overview { get; set; } = new();
    public List<ComplianceStandardMetricsDto> StandardMetrics { get; set; } = new();
    public List<ComplianceTrendPointDto> TrendData { get; set; } = new();
    public List<ComplianceViolationSummaryDto> ViolationSummary { get; set; } = new();
    public Dictionary<string, object> AdditionalMetrics { get; set; } = new();
}

/// <summary>
/// Compliance overview metrics DTO
/// </summary>
public class ComplianceOverviewMetricsDto
{
    public float OverallComplianceScore { get; set; }
    public int TotalEntities { get; set; }
    public int CompliantEntities { get; set; }
    public int NonCompliantEntities { get; set; }
    public int TotalViolations { get; set; }
    public int CriticalViolations { get; set; }
    public int ResolvedViolations { get; set; }
    public int PendingReviews { get; set; }
}

/// <summary>
/// Compliance standard metrics DTO
/// </summary>
public class ComplianceStandardMetricsDto
{
    public ComplianceStandard Standard { get; set; }
    public string StandardName { get; set; } = string.Empty;
    public float ComplianceScore { get; set; }
    public int TotalChecks { get; set; }
    public int PassedChecks { get; set; }
    public int FailedChecks { get; set; }
    public int Violations { get; set; }
    public DateTime LastAssessment { get; set; }
}

/// <summary>
/// Compliance trend point DTO
/// </summary>
public class ComplianceTrendPointDto
{
    public DateTime Date { get; set; }
    public float ComplianceScore { get; set; }
    public int ViolationCount { get; set; }
    public int AuditCount { get; set; }
    public Dictionary<string, float> StandardScores { get; set; } = new();
}

/// <summary>
/// Compliance violation summary DTO
/// </summary>
public class ComplianceViolationSummaryDto
{
    public ComplianceStandard Standard { get; set; }
    public string ViolationType { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public int Count { get; set; }
    public DateTime FirstOccurrence { get; set; }
    public DateTime LastOccurrence { get; set; }
    public int ResolvedCount { get; set; }
    public float ResolutionRate { get; set; }
}

/// <summary>
/// Regulatory report request DTO
/// </summary>
public class RegulatoryReportRequestDto
{
    public string RegulatoryFramework { get; set; } = string.Empty; // GDPR, SOX, HIPAA, etc.
    public DateTime ReportingPeriodStart { get; set; }
    public DateTime ReportingPeriodEnd { get; set; }
    public Guid? OrganizationId { get; set; }
    public List<string>? IncludedSections { get; set; }
    public Dictionary<string, object> CustomParameters { get; set; } = new();
    public string OutputFormat { get; set; } = "PDF";
    public bool IncludeEvidence { get; set; } = true;
    public bool DigitalSignature { get; set; } = false;
}

/// <summary>
/// Report distribution DTO
/// </summary>
public class ReportDistributionDto
{
    public string RecipientType { get; set; } = string.Empty; // Email, User, Role, Group
    public string RecipientIdentifier { get; set; } = string.Empty;
    public string RecipientName { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
    public string DeliveryMethod { get; set; } = "Email"; // Email, Portal, API
    public Dictionary<string, object> DeliveryOptions { get; set; } = new();
}

/// <summary>
/// Report data DTO for exports
/// </summary>
public class ReportDataDto
{
    public string Title { get; set; } = string.Empty;
    public DateTime GeneratedAt { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
    public List<ReportSectionDto> Sections { get; set; } = new();
    public List<ReportChartDto> Charts { get; set; } = new();
    public List<ReportTableDto> Tables { get; set; } = new();
}

/// <summary>
/// Report section DTO
/// </summary>
public class ReportSectionDto
{
    public string Title { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public int Order { get; set; }
    public Dictionary<string, object> Properties { get; set; } = new();
}

/// <summary>
/// Report chart DTO
/// </summary>
public class ReportChartDto
{
    public string Title { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty; // Bar, Line, Pie, etc.
    public List<ReportChartDataDto> Data { get; set; } = new();
    public Dictionary<string, object> Options { get; set; } = new();
}

/// <summary>
/// Report chart data DTO
/// </summary>
public class ReportChartDataDto
{
    public string Label { get; set; } = string.Empty;
    public float Value { get; set; }
    public string? Color { get; set; }
    public Dictionary<string, object> AdditionalData { get; set; } = new();
}

/// <summary>
/// Report table DTO
/// </summary>
public class ReportTableDto
{
    public string Title { get; set; } = string.Empty;
    public List<string> Headers { get; set; } = new();
    public List<List<object>> Rows { get; set; } = new();
    public Dictionary<string, object> Options { get; set; } = new();
}
