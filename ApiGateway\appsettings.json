{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Ocelot": "Information"}}, "AllowedHosts": "*", "JwtSettings": {"Secret": "ThisIsAVerySecureKeyThatShouldBeStoredInASecureVault", "Issuer": "TriTrackzIdentity", "Audience": "TriTrackzServices", "AccessTokenExpirationMinutes": 60, "RefreshTokenExpirationDays": 7}, "IpRateLimiting": {"EnableEndpointRateLimiting": true, "StackBlockedRequests": false, "RealIpHeader": "X-Real-IP", "ClientIdHeader": "X-ClientId", "HttpStatusCode": 429, "GeneralRules": [{"Endpoint": "*", "Period": "1m", "Limit": 1000}, {"Endpoint": "*", "Period": "1h", "Limit": 10000}, {"Endpoint": "*", "Period": "1d", "Limit": 100000}]}, "IpRateLimitPolicies": {"IpRules": [{"Ip": "127.0.0.1", "Rules": [{"Endpoint": "*", "Period": "1m", "Limit": 10000}]}]}, "ThirdPartyServices": {"GoogleMaps": {"ApiKey": "your-google-maps-api-key", "BaseUrl": "https://maps.googleapis.com", "RateLimitPerMinute": 1000, "TimeoutSeconds": 30}, "Razorpay": {"KeyId": "rzp_test_your_key_id", "KeySecret": "your_test_key_secret", "WebhookSecret": "your_test_webhook_secret", "BaseUrl": "https://api.razorpay.com/v1", "TimeoutSeconds": 30}}, "ServiceDiscovery": {"Services": [{"Name": "Identity", "Host": "localhost", "Port": 5001, "HealthCheckPath": "/health"}, {"Name": "UserManagement", "Host": "localhost", "Port": 5002, "HealthCheckPath": "/health"}, {"Name": "SubscriptionManagement", "Host": "localhost", "Port": 5003, "HealthCheckPath": "/health"}, {"Name": "OrderManagement", "Host": "localhost", "Port": 5004, "HealthCheckPath": "/health"}, {"Name": "NetworkFleetManagement", "Host": "localhost", "Port": 5005, "HealthCheckPath": "/health"}, {"Name": "TripManagement", "Host": "localhost", "Port": 5006, "HealthCheckPath": "/health"}, {"Name": "FinancialPayment", "Host": "localhost", "Port": 5007, "HealthCheckPath": "/health"}, {"Name": "CommunicationNotification", "Host": "localhost", "Port": 5008, "HealthCheckPath": "/health"}, {"Name": "AnalyticsBI", "Host": "localhost", "Port": 5009, "HealthCheckPath": "/health"}]}, "Security": {"EnableHttpsRedirection": true, "EnableSecurityHeaders": true, "AllowedOrigins": ["http://localhost:3000", "http://localhost:3001", "https://localhost:3000", "https://localhost:3001"]}, "Monitoring": {"EnableHealthChecks": true, "EnableMetrics": true, "HealthCheckIntervalSeconds": 30}}