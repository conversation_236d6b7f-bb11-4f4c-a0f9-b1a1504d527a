using MobileWorkflow.Application.DTOs;

namespace MobileWorkflow.Application.Services;

public interface IFormBuilderService
{
    Task<FormDefinitionDto> CreateFormAsync(CreateFormRequest request, CancellationToken cancellationToken = default);
    Task<FormDefinitionDto> UpdateFormAsync(Guid formId, UpdateFormRequest request, CancellationToken cancellationToken = default);
    Task<bool> DeleteFormAsync(Guid formId, CancellationToken cancellationToken = default);
    Task<FormDefinitionDto?> GetFormAsync(Guid formId, CancellationToken cancellationToken = default);
    Task<List<FormDefinitionDto>> GetFormsAsync(FormFilterRequest? filter = null, CancellationToken cancellationToken = default);
    Task<FormRenderDto> RenderFormAsync(Guid formId, FormRenderContext context, CancellationToken cancellationToken = default);
    Task<FormValidationResult> ValidateFormAsync(Guid formId, Dictionary<string, object> formData, CancellationToken cancellationToken = default);
    Task<FormSubmissionResult> SubmitFormAsync(Guid formId, FormSubmissionRequest request, CancellationToken cancellationToken = default);
    Task<List<FormFieldDto>> GetAvailableFieldTypesAsync(CancellationToken cancellationToken = default);
    Task<FormTemplateDto> CreateTemplateAsync(CreateFormTemplateRequest request, CancellationToken cancellationToken = default);
    Task<List<FormTemplateDto>> GetTemplatesAsync(CancellationToken cancellationToken = default);
    Task<FormDefinitionDto> CreateFromTemplateAsync(Guid templateId, CreateFromTemplateRequest request, CancellationToken cancellationToken = default);

    // Additional methods from Infrastructure service
    Task<List<FormDefinitionDto>> GetFormsByCategoryAsync(string category, CancellationToken cancellationToken = default);
    Task<FormFieldDto> AddFormFieldAsync(CreateFormFieldRequest request, CancellationToken cancellationToken = default);
    Task<FormSubmissionDto> CreateSubmissionAsync(CreateFormSubmissionRequest request, CancellationToken cancellationToken = default);
    Task<bool> SubmitFormAsync(Guid submissionId, CancellationToken cancellationToken = default);
    Task<Dictionary<string, object>> RenderFormAsync(Guid formId, Dictionary<string, object> context, CancellationToken cancellationToken = default);
    Task<FormDefinitionDto> CreateFormDefinitionAsync(CreateFormDefinitionRequest request, CancellationToken cancellationToken = default);
    Task<FormDefinitionDto?> GetFormDefinitionAsync(Guid formId, CancellationToken cancellationToken = default);
}

public class CreateFormRequest
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public List<FormFieldDto> Fields { get; set; } = new();
    public Dictionary<string, object> Settings { get; set; } = new();
    public bool IsActive { get; set; } = true;
}

public class UpdateFormRequest
{
    public string? Name { get; set; }
    public string? Description { get; set; }
    public string? Category { get; set; }
    public List<FormFieldDto>? Fields { get; set; }
    public Dictionary<string, object>? Settings { get; set; }
    public bool? IsActive { get; set; }
}

public class FormFilterRequest
{
    public string? Category { get; set; }
    public bool? IsActive { get; set; }
    public string? SearchTerm { get; set; }
    public int Skip { get; set; } = 0;
    public int Take { get; set; } = 50;
}

public class FormRenderContext
{
    public string Platform { get; set; } = string.Empty; // Web, Mobile, Desktop
    public string DeviceType { get; set; } = string.Empty; // Phone, Tablet, Desktop
    public Dictionary<string, object> UserContext { get; set; } = new();
    public Dictionary<string, object> SessionData { get; set; } = new();
}

public class FormRenderDto
{
    public Guid FormId { get; set; }
    public string RenderedHtml { get; set; } = string.Empty;
    public string RenderedJson { get; set; } = string.Empty;
    public Dictionary<string, object> ClientScripts { get; set; } = new();
    public Dictionary<string, object> Styles { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class FormValidationResult
{
    public bool IsValid { get; set; }
    public List<FormValidationError> Errors { get; set; } = new();
    public Dictionary<string, object> ProcessedData { get; set; } = new();
}

public class FormValidationError
{
    public string FieldName { get; set; } = string.Empty;
    public string ErrorMessage { get; set; } = string.Empty;
    public string ErrorCode { get; set; } = string.Empty;
}

public class FormSubmissionRequest
{
    public Guid UserId { get; set; }
    public Dictionary<string, object> FormData { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
    public string SubmissionSource { get; set; } = string.Empty; // Web, Mobile, API
}

public class FormSubmissionResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public Guid? SubmissionId { get; set; }
    public Dictionary<string, object> ProcessedData { get; set; } = new();
    public List<FormValidationError> ValidationErrors { get; set; } = new();
}

public class CreateFormTemplateRequest
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public FormDefinitionDto FormDefinition { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class CreateFromTemplateRequest
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public Dictionary<string, object> Customizations { get; set; } = new();
}

public class CreateFormFieldRequest
{
    public Guid FormDefinitionId { get; set; }
    public Guid FormStepId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Label { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string FieldType { get; set; } = string.Empty;
    public bool IsRequired { get; set; }
    public bool IsReadOnly { get; set; }
    public Dictionary<string, object> Properties { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
    public Dictionary<string, object> ValidationRules { get; set; } = new();
    public Dictionary<string, object> ConditionalLogic { get; set; } = new();
    public Dictionary<string, object> Styling { get; set; } = new();
    public Dictionary<string, object> DataBinding { get; set; } = new();
    public List<CreateFormFieldOptionRequest> Options { get; set; } = new();
    public string Placeholder { get; set; } = string.Empty;
    public string PlaceholderText { get; set; } = string.Empty;
    public string DefaultValue { get; set; } = string.Empty;
    public string HelpText { get; set; } = string.Empty;
    public int Order { get; set; }
}

public class CreateFormFieldOptionRequest
{
    public string Label { get; set; } = string.Empty;
    public string Value { get; set; } = string.Empty;
    public bool IsDefault { get; set; }
    public Dictionary<string, object> Properties { get; set; } = new();
}

public class CreateFormSubmissionRequest
{
    public Guid FormDefinitionId { get; set; }
    public string UserId { get; set; } = string.Empty;
    public Dictionary<string, object> FormData { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
    public string Platform { get; set; } = string.Empty;
    public string DeviceId { get; set; } = string.Empty;
    public DateTime SubmittedAt { get; set; }
    public string Status { get; set; } = string.Empty;
}

public class CreateFormDefinitionRequest
{
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public bool IsTemplate { get; set; }
    public bool IsMultiStep { get; set; }
    public Dictionary<string, object> Schema { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
    public Dictionary<string, object> Styling { get; set; } = new();
    public Dictionary<string, object> ValidationRules { get; set; } = new();
    public List<string> RequiredRoles { get; set; } = new();
    public List<string> Tags { get; set; } = new();
    public bool IsPublic { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}
