# 📚 TLI Platform - Frontend Development Documentation

## 🎯 Overview

Welcome to the comprehensive frontend development documentation for the TLI (Transport & Logistics Intelligence) platform. This documentation provides everything you need to build robust, scalable frontend applications that integrate seamlessly with our microservices architecture.

## 🏗️ Platform Architecture

The TLI platform consists of **13 core microservices** plus an **API Gateway**, all following Clean Architecture principles with CQRS patterns, Domain-Driven Design, and comprehensive real-time capabilities.

### Service Overview

| Service                          | Port | Status              | Purpose                              |
| -------------------------------- | ---- | ------------------- | ------------------------------------ |
| **API Gateway**                  | 5000 | ✅ Production Ready | Central routing, rate limiting, CORS |
| **Identity Service**             | 5001 | ✅ Production Ready | Authentication, authorization        |
| **User Management**              | 5002 | 🔄 85% Complete     | User profiles, KYC, documents        |
| **Subscription Management**      | 5003 | ✅ 98% Complete     | Plans, billing, feature flags        |
| **Order Management**             | 5004 | ✅ 96% Complete     | RFQ, bidding, order processing       |
| **Trip Management**              | 5005 | ✅ 94% Complete     | Trip tracking, POD, routes           |
| **Network & Fleet**              | 5006 | ✅ 95% Complete     | Vehicles, drivers, carriers          |
| **Financial & Payment**          | 5007 | 🔄 94% Complete     | Payments, invoicing                  |
| **Communication & Notification** | 5008 | ✅ 80% Complete     | SMS, email, push notifications       |
| **Analytics & BI**               | 5009 | 🔄 60% Complete     | Dashboards, reports                  |
| **Data & Storage**               | 5010 | 🔄 75% Complete     | File management, CDN                 |
| **Monitoring & Observability**   | 5011 | 🔄 65% Complete     | Health checks, metrics               |
| **Audit & Compliance**           | 5012 | 🔄 70% Complete     | Audit trails, compliance             |
| **Mobile & Workflow**            | 5013 | 🔄 60% Complete     | Mobile APIs, workflows               |

## 📖 Documentation Structure

### 🚀 Getting Started

1. **[Frontend Development Guide](./Frontend-Development-Guide.md)**
   - Platform overview and architecture
   - Service endpoints and ports
   - Technology stack details
   - Frontend application types

### 🔐 Authentication & Security

2. **[Authentication Guide](./Authentication-Guide.md)**
   - JWT token structure and management
   - Role-based access control (RBAC)
   - Permission system
   - Frontend implementation examples
   - Security best practices

### 📊 Data & APIs

3. **[API Documentation](./API/)**

   - **[API Overview](./API/README.md)** - Complete API reference
   - **[User Management API](./API/User-Management-API.md)** - User profiles, KYC, documents
   - **[Order Management API](./API/Order-Management-API.md)** - RFQ, bidding, orders
   - **[Trip Management API](./API/Trip-Management-API.md)** - Trip tracking, POD, routes
   - **[Subscription Management API](./API/Subscription-Management-API.md)** - Plans, billing, features
   - **[Network & Fleet Management API](./API/Network-Fleet-Management-API.md)** - Vehicle, driver, carrier management
   - **[Financial & Payment API](./API/Financial-Payment-API.md)** - Escrow, payments, settlements
   - **[Communication & Notification API](./API/Communication-Notification-API.md)** - Multi-channel messaging
   - **[Analytics & BI API](./API/Analytics-BI-API.md)** - Dashboards, reports, predictive analytics
   - **[Data & Storage API](./API/Data-Storage-API.md)** - File management, CDN, search, archiving
   - **[Monitoring & Observability API](./API/Monitoring-Observability-API.md)** - Health checks, metrics, SLA monitoring
   - **[Audit & Compliance API](./API/Audit-Compliance-API.md)** - Audit trails, compliance reporting
   - **[Mobile & Workflow API](./API/Mobile-Workflow-API.md)** - Mobile APIs, workflow engine, offline sync

4. **[Data Models & DTOs](./Data-Models.md)**
   - Complete TypeScript interfaces
   - Request/response models
   - Common data structures
   - Validation patterns

### 🔄 Real-time Features

5. **[Real-time Features Guide](./Real-time-Features.md)**
   - SignalR hub architecture
   - Connection management
   - Event handling patterns
   - Browser notifications
   - React hooks for real-time data

### 🛠️ Development Best Practices

6. **[Error Handling Guide](./Error-Handling.md)**

   - Error response structure
   - Common error codes
   - Frontend error handling patterns
   - Retry mechanisms
   - Error monitoring

7. **[Performance Guidelines](./Performance-Guidelines.md)**
   - Performance targets and metrics
   - Code splitting and lazy loading
   - Data fetching optimization
   - Mobile performance
   - Caching strategies

## 🎯 Frontend Application Types

### 1. **Admin Dashboard** (Web)

- **Users**: System administrators
- **Key Features**: User management, system monitoring, analytics
- **Tech Stack**: React/Angular, TypeScript, Chart libraries
- **Authentication**: Admin role required

### 2. **Broker Portal** (Web)

- **Users**: Logistics brokers
- **Key Features**: RFQ management, carrier network, bid analysis
- **Tech Stack**: React/Vue, TypeScript, Map integration
- **Authentication**: Broker role required

### 3. **Shipper Portal** (Web)

- **Users**: Shipping companies, individual shippers
- **Key Features**: Order creation, tracking, payment management
- **Tech Stack**: React/Angular, TypeScript, Payment integration
- **Authentication**: Shipper role required

### 4. **Transporter Portal** (Web)

- **Users**: Transport companies
- **Key Features**: Fleet management, bid submission, trip execution
- **Tech Stack**: React/Vue, TypeScript, Real-time tracking
- **Authentication**: Transporter role required

### 5. **Driver Mobile App** (Mobile)

- **Users**: Drivers
- **Key Features**: Trip dashboard, location tracking, POD submission
- **Tech Stack**: React Native/Flutter, Offline capabilities
- **Authentication**: Driver role required

### 6. **Mobile Workflow App** (PWA/Mobile)

- **Users**: Field staff, supervisors
- **Key Features**: Offline forms, workflow management, data sync
- **Tech Stack**: PWA, Service Workers, Background sync
- **Authentication**: Role-based access

## 🔧 Quick Start Guide

### 1. Environment Setup

```bash
# Clone the repository
git clone https://github.com/your-org/tli-frontend

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env.local

# Configure API endpoints
API_GATEWAY_URL=http://localhost:5000
IDENTITY_SERVICE_URL=http://localhost:5001
```

### 2. Authentication Setup

```typescript
// Configure API client with authentication
import { ApiClient } from './services/ApiClient'

const apiClient = new ApiClient({
  baseURL: process.env.API_GATEWAY_URL,
  timeout: 30000,
})

// Login and store tokens
const authService = new AuthService(apiClient)
const { accessToken, refreshToken } = await authService.login({
  username: '<EMAIL>',
  password: 'password123',
})

// Tokens are automatically managed by the client
```

### 3. Basic API Usage

```typescript
// Fetch user profile
const userService = new UserManagementService(apiClient)
const profile = await userService.getCurrentProfile()

// Create an RFQ
const orderService = new OrderManagementService(apiClient)
const rfq = await orderService.createRFQ({
  title: 'Transport electronics from Mumbai to Delhi',
  cargoDetails: {
    /* ... */
  },
  routeDetails: {
    /* ... */
  },
})

// Track a trip in real-time
const tripService = new TripManagementService(apiClient)
const { connection } = useSignalR({
  hubUrl: '/api/v1/trips/hub',
})

connection.on('LocationUpdated', (data) => {
  // Update map with new location
  updateTripLocation(data.tripId, data.location)
})
```

### 4. Real-time Integration

```typescript
// React hook for real-time order updates
function useOrderUpdates(orderId: string) {
  const [order, setOrder] = useState<Order | null>(null)

  const { connection } = useSignalR({
    hubUrl: '/api/v1/orders/hub',
  })

  useEffect(() => {
    if (connection) {
      connection.on('OrderStatusChanged', (data) => {
        if (data.orderId === orderId) {
          setOrder((prev) => ({ ...prev, status: data.newStatus }))
        }
      })
    }
  }, [connection, orderId])

  return order
}
```

## 📱 Mobile Development

### React Native Example

```typescript
// Mobile-optimized API client
import { MobileApiClient } from './services/MobileApiClient'

const mobileClient = new MobileApiClient({
  baseURL: process.env.API_GATEWAY_URL,
  offlineSupport: true,
  backgroundSync: true,
})

// Offline-capable trip updates
const updateTripLocation = async (tripId: string, location: Location) => {
  try {
    await mobileClient.post(`/trips/${tripId}/location`, location)
  } catch (error) {
    // Automatically queued for retry when online
    console.log('Location update queued for sync')
  }
}
```

### PWA Configuration

```typescript
// Service worker for offline support
const CACHE_NAME = 'tli-app-v1'
const urlsToCache = [
  '/',
  '/static/js/bundle.js',
  '/static/css/main.css',
  '/api/v1/users/profile/me',
]

self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME).then((cache) => cache.addAll(urlsToCache))
  )
})
```

## 🔍 Testing Strategy

### Unit Testing

```typescript
// Test API service
import { UserManagementService } from './UserManagementService'
import { mockApiClient } from './test-utils'

describe('UserManagementService', () => {
  it('should fetch user profile', async () => {
    const mockProfile = { id: '123', name: 'John Doe' }
    mockApiClient.get.mockResolvedValue({ data: mockProfile })

    const service = new UserManagementService(mockApiClient)
    const profile = await service.getCurrentProfile()

    expect(profile).toEqual(mockProfile)
    expect(mockApiClient.get).toHaveBeenCalledWith('/users/profile/me')
  })
})
```

### Integration Testing

```typescript
// Test real-time features
import { renderHook } from '@testing-library/react-hooks'
import { useSignalR } from './useSignalR'

describe('useSignalR', () => {
  it('should connect to hub and receive events', async () => {
    const { result, waitForNextUpdate } = renderHook(() =>
      useSignalR({ hubUrl: '/test-hub' })
    )

    await waitForNextUpdate()
    expect(result.current.connectionState).toBe('Connected')

    // Test event handling
    const mockEvent = { orderId: '123', status: 'Completed' }
    result.current.connection.emit('OrderStatusChanged', mockEvent)

    // Verify event was handled
  })
})
```

## 🚀 Deployment Considerations

### Environment Configuration

```typescript
// Environment-specific configuration
const config = {
  development: {
    apiGatewayUrl: 'http://localhost:5000',
    enableDevTools: true,
    logLevel: 'debug',
  },
  staging: {
    apiGatewayUrl: 'https://staging-api.tli.com',
    enableDevTools: false,
    logLevel: 'info',
  },
  production: {
    apiGatewayUrl: 'https://api.tli.com',
    enableDevTools: false,
    logLevel: 'error',
  },
}
```

### Performance Optimization

```typescript
// Production build optimizations
const webpackConfig = {
  optimization: {
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
        },
        common: {
          name: 'common',
          minChunks: 2,
          chunks: 'all',
        },
      },
    },
  },
}
```

## 📊 Monitoring & Analytics

### Error Tracking

```typescript
// Error monitoring setup
import * as Sentry from '@sentry/react'

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  environment: process.env.NODE_ENV,
  integrations: [new Sentry.BrowserTracing()],
  tracesSampleRate: 1.0,
})
```

### Performance Monitoring

```typescript
// Performance tracking
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals'

function sendToAnalytics(metric) {
  // Send to your analytics service
  console.log(metric)
}

getCLS(sendToAnalytics)
getFID(sendToAnalytics)
getFCP(sendToAnalytics)
getLCP(sendToAnalytics)
getTTFB(sendToAnalytics)
```

## 🆘 Support & Resources

### Documentation Links

- **[API Reference](./API/README.md)** - Complete API documentation
- **[Authentication Guide](./Authentication-Guide.md)** - Security implementation
- **[Real-time Features](./Real-time-Features.md)** - SignalR integration
- **[Error Handling](./Error-Handling.md)** - Error management patterns
- **[Performance Guidelines](./Performance-Guidelines.md)** - Optimization strategies

### Development Tools

- **Postman Collection**: [Download](./API/postman/)
- **OpenAPI Specs**: Available at each service's `/swagger` endpoint
- **TypeScript Types**: Auto-generated from OpenAPI specs
- **Mock Data**: Available in `/test-data` directory

### Getting Help

1. **Documentation**: Start with this comprehensive guide
2. **API Status**: Monitor service health at `/api/v1/monitoring/health`
3. **Development Team**: Contact for technical support
4. **Issue Tracking**: Report bugs and feature requests

---

**Last Updated**: January 2024  
**Platform Version**: v1.0  
**Documentation Version**: 1.0

This documentation is continuously updated as the platform evolves. For the latest information, always refer to the online version.
