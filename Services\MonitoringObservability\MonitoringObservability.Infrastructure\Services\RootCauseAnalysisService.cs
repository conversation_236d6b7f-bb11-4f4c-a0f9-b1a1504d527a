using Microsoft.Extensions.Logging;
using MonitoringObservability.Application.DTOs;
using MonitoringObservability.Domain.Entities;
using MonitoringObservability.Domain.Enums;
using MonitoringObservability.Domain.Interfaces;

namespace MonitoringObservability.Infrastructure.Services;

/// <summary>
/// Service for automated root cause analysis
/// </summary>
public class RootCauseAnalysisService : IRootCauseAnalysisService
{
    private readonly IAlertRepository _alertRepository;
    private readonly IMetricRepository _metricRepository;
    private readonly ITraceRepository _traceRepository;
    private readonly IServiceDependencyRepository _dependencyRepository;
    private readonly ILogger<RootCauseAnalysisService> _logger;

    public RootCauseAnalysisService(
        IAlertRepository alertRepository,
        IMetricRepository metricRepository,
        ITraceRepository traceRepository,
        IServiceDependencyRepository dependencyRepository,
        ILogger<RootCauseAnalysisService> logger)
    {
        _alertRepository = alertRepository ?? throw new ArgumentNullException(nameof(alertRepository));
        _metricRepository = metricRepository ?? throw new ArgumentNullException(nameof(metricRepository));
        _traceRepository = traceRepository ?? throw new ArgumentNullException(nameof(traceRepository));
        _dependencyRepository = dependencyRepository ?? throw new ArgumentNullException(nameof(dependencyRepository));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<RootCauseAnalysisDto> AnalyzeIncidentAsync(Guid alertId, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting root cause analysis for alert {AlertId}", alertId);

            var alert = await _alertRepository.GetByIdAsync(alertId, cancellationToken);
            if (alert == null)
                throw new InvalidOperationException($"Alert {alertId} not found");

            var analysis = new RootCauseAnalysisDto
            {
                AlertId = alertId,
                ServiceName = alert.ServiceName,
                AnalysisStarted = DateTime.UtcNow,
                PossibleCauses = new List<PossibleCauseDto>(),
                CorrelatedEvents = new List<CorrelatedEventDto>(),
                Recommendations = new List<string>()
            };

            // Analyze metrics around the time of the alert
            await AnalyzeMetricsAsync(alert, analysis, cancellationToken);

            // Analyze traces for errors
            await AnalyzeTracesAsync(alert, analysis, cancellationToken);

            // Analyze service dependencies
            await AnalyzeDependenciesAsync(alert, analysis, cancellationToken);

            // Generate recommendations
            GenerateRecommendations(analysis);

            analysis.AnalysisCompleted = DateTime.UtcNow;
            analysis.ConfidenceScore = CalculateConfidenceScore(analysis);

            _logger.LogInformation("Completed root cause analysis for alert {AlertId} with confidence {Confidence}",
                alertId, analysis.ConfidenceScore);

            return analysis;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to analyze root cause for alert {AlertId}", alertId);
            throw;
        }
    }

    private async Task AnalyzeMetricsAsync(Alert alert, RootCauseAnalysisDto analysis, 
        CancellationToken cancellationToken)
    {
        var timeWindow = TimeSpan.FromMinutes(30);
        var fromDate = alert.CreatedAt.Subtract(timeWindow);
        var toDate = alert.CreatedAt.Add(timeWindow);

        var metrics = await _metricRepository.GetMetricsByServiceAsync(alert.ServiceName, fromDate, cancellationToken);
        
        // Look for metric anomalies
        var cpuMetrics = metrics.Where(m => m.Name.Contains("cpu")).ToList();
        if (cpuMetrics.Any() && cpuMetrics.Average(m => m.Value) > 80)
        {
            analysis.PossibleCauses.Add(new PossibleCauseDto
            {
                Category = "Resource",
                Description = "High CPU utilization detected",
                Confidence = 0.8,
                Evidence = $"Average CPU: {cpuMetrics.Average(m => m.Value):F1}%"
            });
        }

        var memoryMetrics = metrics.Where(m => m.Name.Contains("memory")).ToList();
        if (memoryMetrics.Any() && memoryMetrics.Average(m => m.Value) > 85)
        {
            analysis.PossibleCauses.Add(new PossibleCauseDto
            {
                Category = "Resource",
                Description = "High memory utilization detected",
                Confidence = 0.75,
                Evidence = $"Average Memory: {memoryMetrics.Average(m => m.Value):F1}%"
            });
        }
    }

    private async Task AnalyzeTracesAsync(Alert alert, RootCauseAnalysisDto analysis, 
        CancellationToken cancellationToken)
    {
        var timeWindow = TimeSpan.FromMinutes(15);
        var fromDate = alert.CreatedAt.Subtract(timeWindow);

        var traces = await _traceRepository.SearchTracesAsync(
            serviceName: alert.ServiceName,
            fromDate: fromDate,
            toDate: alert.CreatedAt.AddMinutes(5),
            cancellationToken: cancellationToken);

        var errorTraces = traces.Where(t => t.HasError).ToList();
        if (errorTraces.Any())
        {
            var errorRate = (double)errorTraces.Count / traces.Count() * 100;
            analysis.PossibleCauses.Add(new PossibleCauseDto
            {
                Category = "Application",
                Description = "High error rate in traces",
                Confidence = 0.9,
                Evidence = $"Error rate: {errorRate:F1}% ({errorTraces.Count}/{traces.Count()} traces)"
            });

            // Add correlated events
            foreach (var errorTrace in errorTraces.Take(5))
            {
                analysis.CorrelatedEvents.Add(new CorrelatedEventDto
                {
                    Timestamp = errorTrace.StartTime,
                    Type = "Error",
                    Description = $"Error in trace {errorTrace.TraceId}",
                    ServiceName = errorTrace.ServiceName
                });
            }
        }
    }

    private async Task AnalyzeDependenciesAsync(Alert alert, RootCauseAnalysisDto analysis, 
        CancellationToken cancellationToken)
    {
        var dependencies = await _dependencyRepository.GetDependenciesForServiceAsync(alert.ServiceName, cancellationToken);
        
        foreach (var dependency in dependencies)
        {
            if (dependency.ErrorRate > 0.05) // 5% error rate
            {
                analysis.PossibleCauses.Add(new PossibleCauseDto
                {
                    Category = "Dependency",
                    Description = $"High error rate in dependency: {dependency.ToService}",
                    Confidence = 0.7,
                    Evidence = $"Error rate: {dependency.ErrorRate * 100:F1}%"
                });
            }

            if (dependency.AverageLatency?.TotalMilliseconds > 1000)
            {
                analysis.PossibleCauses.Add(new PossibleCauseDto
                {
                    Category = "Dependency",
                    Description = $"High latency in dependency: {dependency.ToService}",
                    Confidence = 0.6,
                    Evidence = $"Average latency: {dependency.AverageLatency?.TotalMilliseconds:F0}ms"
                });
            }
        }
    }

    private void GenerateRecommendations(RootCauseAnalysisDto analysis)
    {
        var highConfidenceCauses = analysis.PossibleCauses.Where(c => c.Confidence > 0.7).ToList();
        
        foreach (var cause in highConfidenceCauses)
        {
            switch (cause.Category)
            {
                case "Resource":
                    analysis.Recommendations.Add($"Scale up resources to address {cause.Description.ToLower()}");
                    break;
                case "Application":
                    analysis.Recommendations.Add("Review application logs and error handling");
                    break;
                case "Dependency":
                    analysis.Recommendations.Add($"Investigate dependency issues: {cause.Description}");
                    break;
            }
        }

        if (!analysis.Recommendations.Any())
        {
            analysis.Recommendations.Add("No clear root cause identified. Manual investigation recommended.");
        }
    }

    private double CalculateConfidenceScore(RootCauseAnalysisDto analysis)
    {
        if (!analysis.PossibleCauses.Any())
            return 0.1;

        var maxConfidence = analysis.PossibleCauses.Max(c => c.Confidence);
        var causeCount = analysis.PossibleCauses.Count;
        var eventCount = analysis.CorrelatedEvents.Count;

        // Weighted confidence based on evidence
        return Math.Min(0.95, maxConfidence * 0.7 + (causeCount * 0.1) + (eventCount * 0.05));
    }
}

// DTOs for root cause analysis
public class RootCauseAnalysisDto
{
    public Guid AlertId { get; set; }
    public string ServiceName { get; set; } = string.Empty;
    public DateTime AnalysisStarted { get; set; }
    public DateTime? AnalysisCompleted { get; set; }
    public double ConfidenceScore { get; set; }
    public List<PossibleCauseDto> PossibleCauses { get; set; } = new();
    public List<CorrelatedEventDto> CorrelatedEvents { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
}

public class PossibleCauseDto
{
    public string Category { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public double Confidence { get; set; }
    public string Evidence { get; set; } = string.Empty;
}

public class CorrelatedEventDto
{
    public DateTime Timestamp { get; set; }
    public string Type { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string ServiceName { get; set; } = string.Empty;
}

// Service interface
public interface IRootCauseAnalysisService
{
    Task<RootCauseAnalysisDto> AnalyzeIncidentAsync(Guid alertId, CancellationToken cancellationToken = default);
}
