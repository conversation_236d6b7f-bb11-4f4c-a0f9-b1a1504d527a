using Shared.Domain.Common;
using Microsoft.Extensions.Logging;
using Shared.Domain.Common;
using TripManagement.Domain.Entities;
using Shared.Domain.Common;
using TripManagement.Domain.Repositories;
using Shared.Domain.Common;
using TripManagement.Domain.ValueObjects;
using Shared.Domain.Common;
using TLI.Shared.Infrastructure.Messaging;
using Shared.Domain.Common;

namespace TripManagement.Application.Commands.SendEmergencyAlert;

public class SendEmergencyAlertCommandHandler : IRequestHandler<SendEmergencyAlertCommand, SendEmergencyAlertResponse>
{
    private readonly IDriverRepository _driverRepository;
    private readonly ITripRepository _tripRepository;
    private readonly IEmergencyAlertRepository _emergencyAlertRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<SendEmergencyAlertCommandHandler> _logger;

    public SendEmergencyAlertCommandHandler(
        IDriverRepository driverRepository,
        ITripRepository tripRepository,
        IEmergencyAlertRepository emergencyAlertRepository,
        IUnitOfWork unitOfWork,
        IMessageBroker messageBroker,
        ILogger<SendEmergencyAlertCommandHandler> logger)
    {
        _driverRepository = driverRepository;
        _tripRepository = tripRepository;
        _emergencyAlertRepository = emergencyAlertRepository;
        _unitOfWork = unitOfWork;
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task<SendEmergencyAlertResponse> Handle(SendEmergencyAlertCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogWarning("Emergency alert received from driver {DriverId}: {AlertType}", request.DriverId, request.AlertType);

            // Validate driver exists
            var driver = await _driverRepository.GetByIdAsync(request.DriverId, cancellationToken);
            if (driver == null)
            {
                return new SendEmergencyAlertResponse
                {
                    IsSuccess = false,
                    ErrorMessage = "Driver not found",
                    Status = "Failed"
                };
            }

            // Get trip if specified
            Trip? trip = null;
            if (request.TripId.HasValue)
            {
                trip = await _tripRepository.GetByIdAsync(request.TripId.Value, cancellationToken);
                if (trip == null)
                {
                    _logger.LogWarning("Trip {TripId} not found for emergency alert", request.TripId);
                }
            }

            // Create location if coordinates provided
            Location? location = null;
            if (request.Latitude.HasValue && request.Longitude.HasValue)
            {
                location = new Location(request.Latitude.Value, request.Longitude.Value);
            }

            // Create emergency alert entity
            var alertId = Guid.NewGuid();
            var emergencyAlert = new EmergencyAlert(
                alertId,
                request.DriverId,
                request.TripId,
                request.AlertType,
                request.Severity,
                request.Message,
                location,
                request.DeviceId,
                request.AdditionalData);

            // Save emergency alert
            await _emergencyAlertRepository.AddAsync(emergencyAlert, cancellationToken);

            // Update driver status if needed
            if (request.AlertType.ToLower() == "sos" || request.Severity.ToLower() == "critical")
            {
                driver.UpdateStatus(Domain.Enums.DriverStatus.Emergency);
                _driverRepository.Update(driver);
            }

            // Update trip status if applicable
            if (trip != null)
            {
                // Add emergency note to trip
                trip.AddNote($"Emergency alert: {request.AlertType} - {request.Message}");
                _tripRepository.Update(trip);
            }

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Publish integration events for immediate notification
            await PublishEmergencyAlertEvents(emergencyAlert, driver, trip, cancellationToken);

            // Determine response time based on alert type and severity
            var estimatedResponseTime = GetEstimatedResponseTime(request.AlertType, request.Severity);

            var notifiedContacts = await GetNotifiedContacts(request.DriverId, request.AlertType, cancellationToken);

            _logger.LogInformation("Emergency alert {AlertId} processed successfully for driver {DriverId}", alertId, request.DriverId);

            return new SendEmergencyAlertResponse
            {
                IsSuccess = true,
                AlertId = alertId.ToString(),
                AlertTime = emergencyAlert.CreatedAt,
                NotifiedContacts = notifiedContacts,
                Status = "Sent",
                EstimatedResponseTimeMinutes = estimatedResponseTime
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing emergency alert for driver {DriverId}", request.DriverId);
            return new SendEmergencyAlertResponse
            {
                IsSuccess = false,
                ErrorMessage = ex.Message,
                Status = "Failed",
                AlertTime = DateTime.UtcNow
            };
        }
    }

    private async Task PublishEmergencyAlertEvents(EmergencyAlert alert, Driver driver, Trip? trip, CancellationToken cancellationToken)
    {
        // Publish to Communication & Notification service for immediate alerts
        await _messageBroker.PublishAsync("emergency.alert.created", new
        {
            AlertId = alert.Id,
            DriverId = alert.DriverId,
            TripId = alert.TripId,
            AlertType = alert.AlertType,
            Severity = alert.Severity,
            Message = alert.Message,
            Location = alert.Location != null ? new { alert.Location.Latitude, alert.Location.Longitude } : null,
            Timestamp = alert.CreatedAt,
            DriverName = driver.FullName,
            DriverPhone = driver.PhoneNumber,
            TripNumber = trip?.TripNumber,
            Priority = GetAlertPriority(alert.AlertType, alert.Severity)
        }, cancellationToken);

        // Publish to Monitoring & Observability for tracking
        await _messageBroker.PublishAsync("monitoring.emergency.alert", new
        {
            AlertId = alert.Id,
            DriverId = alert.DriverId,
            AlertType = alert.AlertType,
            Severity = alert.Severity,
            Timestamp = alert.CreatedAt,
            Location = alert.Location != null ? new { alert.Location.Latitude, alert.Location.Longitude } : null
        }, cancellationToken);

        // Publish to Analytics for reporting
        await _messageBroker.PublishAsync("analytics.emergency.alert", new
        {
            AlertId = alert.Id,
            DriverId = alert.DriverId,
            TripId = alert.TripId,
            AlertType = alert.AlertType,
            Severity = alert.Severity,
            Timestamp = alert.CreatedAt,
            ResponseTime = 0 // Will be updated when resolved
        }, cancellationToken);
    }

    private int GetEstimatedResponseTime(string alertType, string severity)
    {
        return (alertType.ToLower(), severity.ToLower()) switch
        {
            ("sos", "critical") => 5,
            ("accident", "critical") => 10,
            ("medical", "critical") => 8,
            ("security", "high") => 15,
            ("breakdown", "medium") => 30,
            ("breakdown", "low") => 60,
            _ => 20
        };
    }

    private string GetAlertPriority(string alertType, string severity)
    {
        return (alertType.ToLower(), severity.ToLower()) switch
        {
            ("sos", _) => "URGENT",
            ("accident", "critical") => "URGENT",
            ("medical", "critical") => "URGENT",
            ("security", "high") => "HIGH",
            ("breakdown", "medium") => "MEDIUM",
            _ => "NORMAL"
        };
    }

    private async Task<List<string>> GetNotifiedContacts(Guid driverId, string alertType, CancellationToken cancellationToken)
    {
        // In a real implementation, this would query the appropriate contacts based on:
        // 1. Driver's emergency contacts
        // 2. Fleet manager contacts
        // 3. Company emergency response team
        // 4. Local emergency services (for critical alerts)

        var contacts = new List<string> { "Fleet Manager", "Emergency Response Team" };

        if (alertType.ToLower() == "sos" || alertType.ToLower() == "medical")
        {
            contacts.Add("Emergency Services");
            contacts.Add("Driver Emergency Contact");
        }

        if (alertType.ToLower() == "accident")
        {
            contacts.Add("Insurance Team");
            contacts.Add("Legal Team");
        }

        return contacts;
    }
}

