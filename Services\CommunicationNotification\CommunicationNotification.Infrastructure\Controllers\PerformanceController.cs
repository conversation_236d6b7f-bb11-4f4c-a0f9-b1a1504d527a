using CommunicationNotification.Infrastructure.Models;
using CommunicationNotification.Infrastructure.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CommunicationNotification.Infrastructure.Controllers;

/// <summary>
/// Performance monitoring and optimization API controller
/// </summary>
[ApiController]
[Route("api/communication/performance")]
[Authorize(Roles = "Admin,SystemMonitor")]
public class PerformanceController : ControllerBase
{
    private readonly IPerformanceMonitoringService _performanceMonitoring;
    private readonly IQueryOptimizationService _queryOptimization;
    private readonly ICacheService _cacheService;

    public PerformanceController(
        IPerformanceMonitoringService performanceMonitoring,
        IQueryOptimizationService queryOptimization,
        ICacheService cacheService)
    {
        _performanceMonitoring = performanceMonitoring;
        _queryOptimization = queryOptimization;
        _cacheService = cacheService;
    }

    /// <summary>
    /// Get real-time performance metrics
    /// </summary>
    [HttpGet("metrics/realtime")]
    public async Task<IActionResult> GetRealTimeMetrics()
    {
        var metrics = await _performanceMonitoring.GetRealTimeMetricsAsync();
        return Ok(metrics);
    }

    /// <summary>
    /// Get comprehensive performance report
    /// </summary>
    [HttpGet("report")]
    public async Task<IActionResult> GetPerformanceReport(
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null)
    {
        var report = await _performanceMonitoring.GetPerformanceReportAsync(startDate, endDate);
        return Ok(report);
    }

    /// <summary>
    /// Get active performance alerts
    /// </summary>
    [HttpGet("alerts")]
    public async Task<IActionResult> GetActiveAlerts()
    {
        var alerts = await _performanceMonitoring.GetActiveAlertsAsync();
        return Ok(alerts);
    }

    /// <summary>
    /// Record custom performance metric
    /// </summary>
    [HttpPost("metrics")]
    public async Task<IActionResult> RecordMetric([FromBody] RecordMetricRequest request)
    {
        await _performanceMonitoring.RecordMetricAsync(request.MetricName, request.Value, request.Tags);
        return Ok(new { success = true, message = "Metric recorded successfully" });
    }

    /// <summary>
    /// Record custom counter
    /// </summary>
    [HttpPost("counters")]
    public async Task<IActionResult> RecordCounter([FromBody] RecordCounterRequest request)
    {
        await _performanceMonitoring.RecordCounterAsync(request.CounterName, request.Value, request.Tags);
        return Ok(new { success = true, message = "Counter recorded successfully" });
    }

    /// <summary>
    /// Get database query performance metrics
    /// </summary>
    [HttpGet("database/metrics")]
    public async Task<IActionResult> GetQueryPerformanceMetrics()
    {
        var metrics = await _queryOptimization.GetQueryPerformanceMetricsAsync();
        return Ok(metrics);
    }

    /// <summary>
    /// Get query optimization recommendations
    /// </summary>
    [HttpGet("database/recommendations")]
    public async Task<IActionResult> GetQueryOptimizationRecommendations()
    {
        var recommendations = await _queryOptimization.GetOptimizationRecommendationsAsync();
        return Ok(recommendations);
    }

    /// <summary>
    /// Get slow queries analysis
    /// </summary>
    [HttpGet("database/slow-queries")]
    public async Task<IActionResult> GetSlowQueries([FromQuery] int? thresholdMs = null)
    {
        var threshold = thresholdMs.HasValue ? TimeSpan.FromMilliseconds(thresholdMs.Value) : (TimeSpan?)null;
        var slowQueries = await _queryOptimization.GetSlowQueriesAsync(threshold);
        return Ok(slowQueries);
    }

    /// <summary>
    /// Get index recommendations
    /// </summary>
    [HttpGet("database/index-recommendations")]
    public async Task<IActionResult> GetIndexRecommendations()
    {
        var recommendations = await _queryOptimization.GetIndexRecommendationsAsync();
        return Ok(recommendations);
    }

    /// <summary>
    /// Analyze query execution plan
    /// </summary>
    [HttpPost("database/analyze-query")]
    public async Task<IActionResult> AnalyzeQuery([FromBody] AnalyzeQueryRequest request)
    {
        var plan = await _queryOptimization.AnalyzeQueryPlanAsync(request.Sql);
        return Ok(plan);
    }

    /// <summary>
    /// Optimize database connection pool
    /// </summary>
    [HttpPost("database/optimize-pool")]
    public async Task<IActionResult> OptimizeConnectionPool()
    {
        await _queryOptimization.OptimizeConnectionPoolAsync();
        return Ok(new { success = true, message = "Connection pool optimization initiated" });
    }

    /// <summary>
    /// Get cache performance statistics
    /// </summary>
    [HttpGet("cache/statistics")]
    public async Task<IActionResult> GetCacheStatistics()
    {
        var statistics = new
        {
            // These would be implemented in the cache service
            hitRate = 0.85, // Example values
            missRate = 0.15,
            totalRequests = 10000,
            averageResponseTime = 2.5,
            memoryUsage = "45MB",
            keyCount = 1500
        };

        return Ok(statistics);
    }

    /// <summary>
    /// Clear cache by pattern
    /// </summary>
    [HttpDelete("cache/clear")]
    public async Task<IActionResult> ClearCache([FromQuery] string? pattern = null)
    {
        if (string.IsNullOrEmpty(pattern))
        {
            return BadRequest("Pattern is required for cache clearing");
        }

        await _cacheService.RemoveByPatternAsync(pattern);
        return Ok(new { success = true, message = $"Cache cleared for pattern: {pattern}" });
    }

    /// <summary>
    /// Warm up cache with frequently accessed data
    /// </summary>
    [HttpPost("cache/warmup")]
    public async Task<IActionResult> WarmUpCache([FromBody] CacheWarmupRequest request)
    {
        // Implementation would depend on your specific caching needs
        var warmedKeys = new List<string>();

        foreach (var key in request.Keys)
        {
            var exists = await _cacheService.ExistsAsync(key);
            if (!exists)
            {
                // You would implement logic to load and cache the data
                warmedKeys.Add(key);
            }
        }

        return Ok(new { 
            success = true, 
            message = $"Cache warmed up for {warmedKeys.Count} keys",
            warmedKeys 
        });
    }

    /// <summary>
    /// Get cache key information
    /// </summary>
    [HttpGet("cache/keys/{key}/info")]
    public async Task<IActionResult> GetCacheKeyInfo(string key)
    {
        var exists = await _cacheService.ExistsAsync(key);
        var ttl = await _cacheService.GetTtlAsync(key);

        var info = new
        {
            key,
            exists,
            ttl = ttl?.TotalSeconds,
            lastAccessed = DateTime.UtcNow // This would be tracked in a real implementation
        };

        return Ok(info);
    }

    /// <summary>
    /// Get system resource metrics
    /// </summary>
    [HttpGet("system/resources")]
    public async Task<IActionResult> GetSystemResourceMetrics()
    {
        var metrics = new SystemResourceMetrics
        {
            CpuUsagePercent = GetCpuUsage(),
            MemoryUsageBytes = GC.GetTotalMemory(false),
            MemoryUsagePercent = GetMemoryUsagePercent(),
            ThreadCount = System.Diagnostics.Process.GetCurrentProcess().Threads.Count,
            HandleCount = System.Diagnostics.Process.GetCurrentProcess().HandleCount,
            Timestamp = DateTime.UtcNow
        };

        return Ok(metrics);
    }

    /// <summary>
    /// Get application performance metrics
    /// </summary>
    [HttpGet("application/metrics")]
    public async Task<IActionResult> GetApplicationMetrics()
    {
        var metrics = new ApplicationPerformanceMetrics
        {
            RequestsPerSecond = CalculateRequestsPerSecond(),
            AverageResponseTime = CalculateAverageResponseTime(),
            ErrorRate = CalculateErrorRate(),
            QueuedMessages = GetQueuedMessageCount(),
            ProcessedMessages = GetProcessedMessageCount(),
            CacheHitRate = await CalculateCacheHitRate(),
            Timestamp = DateTime.UtcNow
        };

        return Ok(metrics);
    }

    /// <summary>
    /// Get performance optimization suggestions
    /// </summary>
    [HttpGet("optimization/suggestions")]
    public async Task<IActionResult> GetOptimizationSuggestions()
    {
        var suggestions = await GenerateOptimizationSuggestions();
        return Ok(suggestions);
    }

    /// <summary>
    /// Trigger performance optimization
    /// </summary>
    [HttpPost("optimization/trigger")]
    public async Task<IActionResult> TriggerOptimization([FromBody] OptimizationRequest request)
    {
        var results = new List<string>();

        if (request.OptimizeCache)
        {
            // Implement cache optimization
            results.Add("Cache optimization completed");
        }

        if (request.OptimizeDatabase)
        {
            await _queryOptimization.OptimizeConnectionPoolAsync();
            results.Add("Database optimization completed");
        }

        if (request.OptimizeMemory)
        {
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();
            results.Add("Memory optimization completed");
        }

        return Ok(new { 
            success = true, 
            message = "Optimization completed",
            results 
        });
    }

    // Helper methods (these would be implemented with actual system monitoring)
    private double GetCpuUsage()
    {
        // Placeholder - would use performance counters or similar
        return new Random().NextDouble() * 100;
    }

    private double GetMemoryUsagePercent()
    {
        // Placeholder - would calculate actual memory usage percentage
        return new Random().NextDouble() * 100;
    }

    private double CalculateRequestsPerSecond()
    {
        // Placeholder - would track actual request rates
        return new Random().NextDouble() * 1000;
    }

    private double CalculateAverageResponseTime()
    {
        // Placeholder - would calculate from actual response times
        return new Random().NextDouble() * 500;
    }

    private double CalculateErrorRate()
    {
        // Placeholder - would calculate from actual error tracking
        return new Random().NextDouble() * 0.05;
    }

    private int GetQueuedMessageCount()
    {
        // Placeholder - would get from actual message queue
        return new Random().Next(0, 100);
    }

    private int GetProcessedMessageCount()
    {
        // Placeholder - would get from actual processing metrics
        return new Random().Next(1000, 10000);
    }

    private async Task<double> CalculateCacheHitRate()
    {
        // Placeholder - would calculate from actual cache metrics
        return 0.85;
    }

    private async Task<List<PerformanceOptimizationSuggestion>> GenerateOptimizationSuggestions()
    {
        var suggestions = new List<PerformanceOptimizationSuggestion>
        {
            new PerformanceOptimizationSuggestion
            {
                Category = "Cache",
                Title = "Increase Cache TTL for Static Data",
                Description = "Static reference data could benefit from longer cache TTL",
                Impact = "Medium",
                Effort = "Low",
                Priority = 7.5,
                ActionItems = new List<string> { "Review static data access patterns", "Increase TTL for reference data" }
            },
            new PerformanceOptimizationSuggestion
            {
                Category = "Database",
                Title = "Add Index for Frequent Queries",
                Description = "Several slow queries could benefit from additional indexes",
                Impact = "High",
                Effort = "Medium",
                Priority = 8.5,
                ActionItems = new List<string> { "Analyze query execution plans", "Create composite indexes" }
            }
        };

        return suggestions;
    }
}

// Request/Response DTOs
public class RecordMetricRequest
{
    public string MetricName { get; set; } = string.Empty;
    public double Value { get; set; }
    public Dictionary<string, object>? Tags { get; set; }
}

public class RecordCounterRequest
{
    public string CounterName { get; set; } = string.Empty;
    public long Value { get; set; } = 1;
    public Dictionary<string, object>? Tags { get; set; }
}

public class AnalyzeQueryRequest
{
    public string Sql { get; set; } = string.Empty;
}

public class CacheWarmupRequest
{
    public List<string> Keys { get; set; } = new();
}

public class OptimizationRequest
{
    public bool OptimizeCache { get; set; }
    public bool OptimizeDatabase { get; set; }
    public bool OptimizeMemory { get; set; }
}
