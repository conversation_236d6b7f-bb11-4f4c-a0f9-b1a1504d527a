using System;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.Extensions.Logging;
using UserManagement.Domain.Repositories;
using UserManagement.Domain.Exceptions;

namespace UserManagement.Application.UserProfiles.Commands.UpdateCompanyDetails
{
    public class UpdateCompanyDetailsCommandHandler : IRequestHandler<UpdateCompanyDetailsCommand, bool>
    {
        private readonly IUserProfileRepository _userProfileRepository;
        private readonly ILogger<UpdateCompanyDetailsCommandHandler> _logger;

        public UpdateCompanyDetailsCommandHandler(
            IUserProfileRepository userProfileRepository,
            ILogger<UpdateCompanyDetailsCommandHandler> logger)
        {
            _userProfileRepository = userProfileRepository;
            _logger = logger;
        }

        public async Task<bool> Handle(UpdateCompanyDetailsCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Updating company details for profile {ProfileId}", request.Id);

                var userProfile = await _userProfileRepository.GetByIdAsync(request.Id);
                if (userProfile == null)
                {
                    throw new UserManagementDomainException("User profile not found");
                }

                // Update company details using domain method
                userProfile.UpdateCompanyDetails(request.CompanyName, request.GstNumber, request.PanNumber);

                // Save changes
                await _userProfileRepository.UpdateAsync(userProfile);

                _logger.LogInformation("Successfully updated company details for profile {ProfileId}", request.Id);

                return true;
            }
            catch (UserManagementDomainException)
            {
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while updating company details for profile {ProfileId}", request.Id);
                throw;
            }
        }
    }
}
