using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using StackExchange.Redis;
using System.Text.Json;
using FinancialPayment.Application.Interfaces;

namespace FinancialPayment.Infrastructure.Services;

public class RedisCacheService : IDistributedCacheService, IDisposable
{
    private readonly IDatabase _database;
    private readonly IConnectionMultiplexer _connectionMultiplexer;
    private readonly ILogger<RedisCacheService> _logger;
    private readonly CacheConfiguration _configuration;
    private readonly JsonSerializerOptions _jsonOptions;

    public RedisCacheService(
        IConnectionMultiplexer connectionMultiplexer,
        ILogger<RedisCacheService> logger,
        IOptions<CacheConfiguration> configuration)
    {
        _connectionMultiplexer = connectionMultiplexer;
        _database = connectionMultiplexer.GetDatabase();
        _logger = logger;
        _configuration = configuration.Value;
        
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false
        };
    }

    public async Task<T?> GetAsync<T>(string key) where T : class
    {
        try
        {
            var value = await _database.StringGetAsync(GetFullKey(key));
            if (!value.HasValue)
            {
                _logger.LogDebug("Cache miss for key: {Key}", key);
                return null;
            }

            var deserializedValue = JsonSerializer.Deserialize<T>(value!, _jsonOptions);
            _logger.LogDebug("Cache hit for key: {Key}", key);
            return deserializedValue;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting value from cache for key: {Key}", key);
            return null;
        }
    }

    public async Task<string?> GetStringAsync(string key)
    {
        try
        {
            var value = await _database.StringGetAsync(GetFullKey(key));
            return value.HasValue ? value.ToString() : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting string value from cache for key: {Key}", key);
            return null;
        }
    }

    public async Task SetAsync<T>(string key, T value, TimeSpan? expiry = null) where T : class
    {
        try
        {
            var serializedValue = JsonSerializer.Serialize(value, _jsonOptions);
            var effectiveExpiry = expiry ?? TimeSpan.FromMinutes(_configuration.DefaultExpiryMinutes);
            
            await _database.StringSetAsync(GetFullKey(key), serializedValue, effectiveExpiry);
            _logger.LogDebug("Value cached for key: {Key} with expiry: {Expiry}", key, effectiveExpiry);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting value in cache for key: {Key}", key);
        }
    }

    public async Task SetStringAsync(string key, string value, TimeSpan? expiry = null)
    {
        try
        {
            var effectiveExpiry = expiry ?? TimeSpan.FromMinutes(_configuration.DefaultExpiryMinutes);
            await _database.StringSetAsync(GetFullKey(key), value, effectiveExpiry);
            _logger.LogDebug("String value cached for key: {Key}", key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting string value in cache for key: {Key}", key);
        }
    }

    public async Task RemoveAsync(string key)
    {
        try
        {
            await _database.KeyDeleteAsync(GetFullKey(key));
            _logger.LogDebug("Key removed from cache: {Key}", key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing key from cache: {Key}", key);
        }
    }

    public async Task RemoveByPatternAsync(string pattern)
    {
        try
        {
            var server = _connectionMultiplexer.GetServer(_connectionMultiplexer.GetEndPoints().First());
            var keys = server.Keys(pattern: GetFullKey(pattern));
            
            foreach (var key in keys)
            {
                await _database.KeyDeleteAsync(key);
            }
            
            _logger.LogDebug("Keys removed from cache by pattern: {Pattern}", pattern);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing keys by pattern: {Pattern}", pattern);
        }
    }

    public async Task<bool> ExistsAsync(string key)
    {
        try
        {
            return await _database.KeyExistsAsync(GetFullKey(key));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if key exists: {Key}", key);
            return false;
        }
    }

    public async Task<long> IncrementAsync(string key, long value = 1, TimeSpan? expiry = null)
    {
        try
        {
            var result = await _database.StringIncrementAsync(GetFullKey(key), value);
            
            if (expiry.HasValue)
            {
                await _database.KeyExpireAsync(GetFullKey(key), expiry.Value);
            }
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error incrementing key: {Key}", key);
            return 0;
        }
    }

    public async Task<long> DecrementAsync(string key, long value = 1, TimeSpan? expiry = null)
    {
        try
        {
            var result = await _database.StringDecrementAsync(GetFullKey(key), value);
            
            if (expiry.HasValue)
            {
                await _database.KeyExpireAsync(GetFullKey(key), expiry.Value);
            }
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error decrementing key: {Key}", key);
            return 0;
        }
    }

    public async Task<bool> SetIfNotExistsAsync<T>(string key, T value, TimeSpan? expiry = null) where T : class
    {
        try
        {
            var serializedValue = JsonSerializer.Serialize(value, _jsonOptions);
            var effectiveExpiry = expiry ?? TimeSpan.FromMinutes(_configuration.DefaultExpiryMinutes);
            
            var result = await _database.StringSetAsync(GetFullKey(key), serializedValue, effectiveExpiry, When.NotExists);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting value if not exists for key: {Key}", key);
            return false;
        }
    }

    public async Task<Dictionary<string, T?>> GetManyAsync<T>(IEnumerable<string> keys) where T : class
    {
        try
        {
            var redisKeys = keys.Select(k => (RedisKey)GetFullKey(k)).ToArray();
            var values = await _database.StringGetAsync(redisKeys);
            
            var result = new Dictionary<string, T?>();
            var keyArray = keys.ToArray();
            
            for (int i = 0; i < keyArray.Length; i++)
            {
                if (values[i].HasValue)
                {
                    try
                    {
                        result[keyArray[i]] = JsonSerializer.Deserialize<T>(values[i]!, _jsonOptions);
                    }
                    catch
                    {
                        result[keyArray[i]] = null;
                    }
                }
                else
                {
                    result[keyArray[i]] = null;
                }
            }
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting multiple values from cache");
            return new Dictionary<string, T?>();
        }
    }

    public async Task SetManyAsync<T>(Dictionary<string, T> keyValuePairs, TimeSpan? expiry = null) where T : class
    {
        try
        {
            var effectiveExpiry = expiry ?? TimeSpan.FromMinutes(_configuration.DefaultExpiryMinutes);
            var tasks = new List<Task>();
            
            foreach (var kvp in keyValuePairs)
            {
                tasks.Add(SetAsync(kvp.Key, kvp.Value, effectiveExpiry));
            }
            
            await Task.WhenAll(tasks);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting multiple values in cache");
        }
    }

    public async Task<TimeSpan?> GetTtlAsync(string key)
    {
        try
        {
            return await _database.KeyTimeToLiveAsync(GetFullKey(key));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting TTL for key: {Key}", key);
            return null;
        }
    }

    public async Task ExpireAsync(string key, TimeSpan expiry)
    {
        try
        {
            await _database.KeyExpireAsync(GetFullKey(key), expiry);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting expiry for key: {Key}", key);
        }
    }

    public async Task ClearAsync()
    {
        try
        {
            var server = _connectionMultiplexer.GetServer(_connectionMultiplexer.GetEndPoints().First());
            await server.FlushDatabaseAsync();
            _logger.LogWarning("Cache cleared");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing cache");
        }
    }

    // Hash operations
    public async Task<T?> GetFromHashAsync<T>(string hashKey, string field) where T : class
    {
        try
        {
            var value = await _database.HashGetAsync(GetFullKey(hashKey), field);
            if (!value.HasValue) return null;
            
            return JsonSerializer.Deserialize<T>(value!, _jsonOptions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting hash field {Field} from {HashKey}", field, hashKey);
            return null;
        }
    }

    public async Task SetInHashAsync<T>(string hashKey, string field, T value) where T : class
    {
        try
        {
            var serializedValue = JsonSerializer.Serialize(value, _jsonOptions);
            await _database.HashSetAsync(GetFullKey(hashKey), field, serializedValue);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting hash field {Field} in {HashKey}", field, hashKey);
        }
    }

    public async Task RemoveFromHashAsync(string hashKey, string field)
    {
        try
        {
            await _database.HashDeleteAsync(GetFullKey(hashKey), field);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing hash field {Field} from {HashKey}", field, hashKey);
        }
    }

    public async Task<Dictionary<string, T?>> GetHashAsync<T>(string hashKey) where T : class
    {
        try
        {
            var hash = await _database.HashGetAllAsync(GetFullKey(hashKey));
            var result = new Dictionary<string, T?>();
            
            foreach (var item in hash)
            {
                try
                {
                    result[item.Name!] = JsonSerializer.Deserialize<T>(item.Value!, _jsonOptions);
                }
                catch
                {
                    result[item.Name!] = null;
                }
            }
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting hash {HashKey}", hashKey);
            return new Dictionary<string, T?>();
        }
    }

    public async Task SetHashAsync<T>(string hashKey, Dictionary<string, T> hash) where T : class
    {
        try
        {
            var hashEntries = hash.Select(kvp => new HashEntry(kvp.Key, JsonSerializer.Serialize(kvp.Value, _jsonOptions))).ToArray();
            await _database.HashSetAsync(GetFullKey(hashKey), hashEntries);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting hash {HashKey}", hashKey);
        }
    }

    public async Task<bool> HashExistsAsync(string hashKey, string field)
    {
        try
        {
            return await _database.HashExistsAsync(GetFullKey(hashKey), field);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking hash field existence {Field} in {HashKey}", field, hashKey);
            return false;
        }
    }

    public async Task<long> HashLengthAsync(string hashKey)
    {
        try
        {
            return await _database.HashLengthAsync(GetFullKey(hashKey));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting hash length {HashKey}", hashKey);
            return 0;
        }
    }

    // List operations
    public async Task<long> ListPushAsync<T>(string key, T value) where T : class
    {
        try
        {
            var serializedValue = JsonSerializer.Serialize(value, _jsonOptions);
            return await _database.ListLeftPushAsync(GetFullKey(key), serializedValue);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error pushing to list {Key}", key);
            return 0;
        }
    }

    public async Task<T?> ListPopAsync<T>(string key) where T : class
    {
        try
        {
            var value = await _database.ListLeftPopAsync(GetFullKey(key));
            if (!value.HasValue) return null;
            
            return JsonSerializer.Deserialize<T>(value!, _jsonOptions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error popping from list {Key}", key);
            return null;
        }
    }

    public async Task<List<T>> ListRangeAsync<T>(string key, long start = 0, long stop = -1) where T : class
    {
        try
        {
            var values = await _database.ListRangeAsync(GetFullKey(key), start, stop);
            var result = new List<T>();
            
            foreach (var value in values)
            {
                try
                {
                    var item = JsonSerializer.Deserialize<T>(value!, _jsonOptions);
                    if (item != null) result.Add(item);
                }
                catch
                {
                    // Skip invalid items
                }
            }
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting list range {Key}", key);
            return new List<T>();
        }
    }

    public async Task<long> ListLengthAsync(string key)
    {
        try
        {
            return await _database.ListLengthAsync(GetFullKey(key));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting list length {Key}", key);
            return 0;
        }
    }

    // Set operations
    public async Task<bool> SetAddAsync<T>(string key, T value) where T : class
    {
        try
        {
            var serializedValue = JsonSerializer.Serialize(value, _jsonOptions);
            return await _database.SetAddAsync(GetFullKey(key), serializedValue);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding to set {Key}", key);
            return false;
        }
    }

    public async Task<bool> SetRemoveAsync<T>(string key, T value) where T : class
    {
        try
        {
            var serializedValue = JsonSerializer.Serialize(value, _jsonOptions);
            return await _database.SetRemoveAsync(GetFullKey(key), serializedValue);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing from set {Key}", key);
            return false;
        }
    }

    public async Task<List<T>> SetMembersAsync<T>(string key) where T : class
    {
        try
        {
            var values = await _database.SetMembersAsync(GetFullKey(key));
            var result = new List<T>();
            
            foreach (var value in values)
            {
                try
                {
                    var item = JsonSerializer.Deserialize<T>(value!, _jsonOptions);
                    if (item != null) result.Add(item);
                }
                catch
                {
                    // Skip invalid items
                }
            }
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting set members {Key}", key);
            return new List<T>();
        }
    }

    public async Task<bool> SetContainsAsync<T>(string key, T value) where T : class
    {
        try
        {
            var serializedValue = JsonSerializer.Serialize(value, _jsonOptions);
            return await _database.SetContainsAsync(GetFullKey(key), serializedValue);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking set membership {Key}", key);
            return false;
        }
    }

    public async Task<long> SetLengthAsync(string key)
    {
        try
        {
            return await _database.SetLengthAsync(GetFullKey(key));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting set length {Key}", key);
            return 0;
        }
    }

    private string GetFullKey(string key)
    {
        return $"{_configuration.InstanceName}:{key}";
    }

    public void Dispose()
    {
        _connectionMultiplexer?.Dispose();
    }
}
