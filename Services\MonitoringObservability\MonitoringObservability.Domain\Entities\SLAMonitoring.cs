using MonitoringObservability.Domain.Enums;
using MonitoringObservability.Domain.Events;
using Shared.Domain.Common;

namespace MonitoringObservability.Domain.Entities;

/// <summary>
/// Represents a Service Level Agreement definition
/// </summary>
public class ServiceLevelAgreement : AggregateRoot
{
    private readonly List<SLAObjective> _objectives = new();
    private readonly List<SLABreach> _breaches = new();
    private readonly List<SLAReport> _reports = new();

    public ServiceLevelAgreement(string name, string description, string serviceName,
        DateTime startDate, DateTime endDate, Guid ownerId)
    {
        Id = Guid.NewGuid();
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        ServiceName = serviceName ?? throw new ArgumentNullException(nameof(serviceName));
        StartDate = startDate;
        EndDate = endDate;
        OwnerId = ownerId;
        CreatedAt = DateTime.UtcNow;
        LastModified = DateTime.UtcNow;
        IsActive = true;
        Status = SLAStatus.Active;
        Tags = new Dictionary<string, string>();
        Metadata = new Dictionary<string, object>();
    }

    // Required for EF Core
    private ServiceLevelAgreement() { }

    public string Name { get; private set; } = string.Empty;
    public string Description { get; private set; } = string.Empty;
    public string ServiceName { get; private set; } = string.Empty;
    public DateTime StartDate { get; private set; }
    public DateTime EndDate { get; private set; }
    public Guid OwnerId { get; private set; }
    public bool IsActive { get; private set; }
    public SLAStatus Status { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime LastModified { get; private set; }
    public DateTime? LastEvaluated { get; private set; }
    public double? CurrentCompliance { get; private set; }
    public Dictionary<string, string> Tags { get; private set; } = new();
    public Dictionary<string, object> Metadata { get; private set; } = new();

    // Navigation properties
    public IReadOnlyList<SLAObjective> Objectives => _objectives.AsReadOnly();
    public IReadOnlyList<SLABreach> Breaches => _breaches.AsReadOnly();
    public IReadOnlyList<SLAReport> Reports => _reports.AsReadOnly();

    // Computed properties
    public bool IsExpired => DateTime.UtcNow > EndDate;
    public bool IsValid => DateTime.UtcNow >= StartDate && DateTime.UtcNow <= EndDate;
    public int ObjectiveCount => _objectives.Count;
    public int BreachCount => _breaches.Count;
    public int ActiveBreachCount => _breaches.Count(b => b.Status == BreachStatus.Open);

    public void UpdateBasicInfo(string? name = null, string? description = null,
        DateTime? startDate = null, DateTime? endDate = null)
    {
        if (!string.IsNullOrWhiteSpace(name))
            Name = name;

        if (!string.IsNullOrWhiteSpace(description))
            Description = description;

        if (startDate.HasValue)
            StartDate = startDate.Value;

        if (endDate.HasValue)
            EndDate = endDate.Value;

        LastModified = DateTime.UtcNow;

        AddDomainEvent(new SLAUpdatedEvent(Id, Name));
    }

    public void AddObjective(SLAObjective objective)
    {
        if (objective == null) throw new ArgumentNullException(nameof(objective));

        _objectives.Add(objective);
        LastModified = DateTime.UtcNow;

        AddDomainEvent(new SLAObjectiveAddedEvent(Id, objective.Id, objective.Name));
    }

    public void RemoveObjective(Guid objectiveId)
    {
        var objective = _objectives.FirstOrDefault(o => o.Id == objectiveId);
        if (objective != null)
        {
            _objectives.Remove(objective);
            LastModified = DateTime.UtcNow;

            AddDomainEvent(new SLAObjectiveRemovedEvent(Id, objectiveId, "Objective Removed"));
        }
    }

    public void RecordBreach(SLABreach breach)
    {
        if (breach == null) throw new ArgumentNullException(nameof(breach));

        _breaches.Add(breach);
        LastModified = DateTime.UtcNow;

        AddDomainEvent(new SLABreachDetectedEvent(Id, breach.Description, breach.Severity.ToString()));
    }

    public void AddReport(SLAReport report)
    {
        if (report == null) throw new ArgumentNullException(nameof(report));

        _reports.Add(report);
        LastModified = DateTime.UtcNow;
    }

    public void UpdateCompliance(double compliance)
    {
        CurrentCompliance = Math.Max(0, Math.Min(100, compliance));
        LastEvaluated = DateTime.UtcNow;
        LastModified = DateTime.UtcNow;

        // Update status based on compliance
        Status = compliance switch
        {
            >= 99.0 => SLAStatus.Excellent,
            >= 95.0 => SLAStatus.Good,
            >= 90.0 => SLAStatus.Warning,
            _ => SLAStatus.Critical
        };

        AddDomainEvent(new SLAComplianceUpdatedEvent(Id, (decimal)compliance));
    }

    public void Activate()
    {
        if (!IsActive)
        {
            IsActive = true;
            Status = SLAStatus.Active;
            LastModified = DateTime.UtcNow;

            AddDomainEvent(new SLAActivatedEvent(Id, Name));
        }
    }

    public void Deactivate()
    {
        if (IsActive)
        {
            IsActive = false;
            Status = SLAStatus.Inactive;
            LastModified = DateTime.UtcNow;

            AddDomainEvent(new SLADeactivatedEvent(Id, Name));
        }
    }

    public void Suspend(string reason)
    {
        Status = SLAStatus.Suspended;
        LastModified = DateTime.UtcNow;

        AddMetadata("suspension_reason", reason);
        AddMetadata("suspended_at", DateTime.UtcNow);

        AddDomainEvent(new SLASuspendedEvent(Id, Name, reason));
    }

    public void AddTag(string key, string value)
    {
        if (string.IsNullOrWhiteSpace(key)) throw new ArgumentException("Tag key cannot be null or empty", nameof(key));
        Tags[key] = value ?? string.Empty;
        LastModified = DateTime.UtcNow;
    }

    public void AddMetadata(string key, object value)
    {
        if (string.IsNullOrWhiteSpace(key)) throw new ArgumentException("Metadata key cannot be null or empty", nameof(key));
        Metadata[key] = value;
        LastModified = DateTime.UtcNow;
    }

    public SLAObjective? GetObjective(Guid objectiveId)
    {
        return _objectives.FirstOrDefault(o => o.Id == objectiveId);
    }

    public IEnumerable<SLAObjective> GetObjectivesByType(SLAObjectiveType type)
    {
        return _objectives.Where(o => o.Type == type);
    }

    public IEnumerable<SLABreach> GetActiveBreaches()
    {
        return _breaches.Where(b => b.Status == BreachStatus.Open);
    }

    public IEnumerable<SLABreach> GetBreachesByObjective(Guid objectiveId)
    {
        return _breaches.Where(b => b.ObjectiveId == objectiveId);
    }

    public double CalculateOverallCompliance()
    {
        if (!_objectives.Any())
            return 100.0;

        var totalWeight = _objectives.Sum(o => o.Weight);
        if (totalWeight == 0)
            return 100.0;

        var weightedCompliance = _objectives.Sum(o => (o.CurrentCompliance ?? 0) * o.Weight);
        return weightedCompliance / totalWeight;
    }
}

/// <summary>
/// Represents an SLA objective (SLO)
/// </summary>
public class SLAObjective
{
    public SLAObjective(string name, string description, SLAObjectiveType type,
        string metricName, double targetValue, SLAComparisonOperator comparisonOperator,
        TimeSpan evaluationWindow, double weight = 1.0)
    {
        Id = Guid.NewGuid();
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        Type = type;
        MetricName = metricName ?? throw new ArgumentNullException(nameof(metricName));
        TargetValue = targetValue;
        ComparisonOperator = comparisonOperator;
        EvaluationWindow = evaluationWindow;
        Weight = Math.Max(0.1, weight);
        CreatedAt = DateTime.UtcNow;
        LastModified = DateTime.UtcNow;
        IsEnabled = true;
        Configuration = new Dictionary<string, object>();
    }

    // Required for EF Core
    private SLAObjective() { }

    public Guid Id { get; private set; }
    public string Name { get; private set; } = string.Empty;
    public string Description { get; private set; } = string.Empty;
    public SLAObjectiveType Type { get; private set; }
    public string MetricName { get; private set; } = string.Empty;
    public double TargetValue { get; private set; }
    public SLAComparisonOperator ComparisonOperator { get; private set; }
    public TimeSpan EvaluationWindow { get; private set; }
    public double Weight { get; private set; }
    public bool IsEnabled { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime LastModified { get; private set; }
    public DateTime? LastEvaluated { get; private set; }
    public double? CurrentValue { get; private set; }
    public double? CurrentCompliance { get; private set; }
    public Dictionary<string, object> Configuration { get; private set; } = new();

    // Navigation properties
    public ServiceLevelAgreement SLA { get; private set; } = null!;

    // Computed properties
    public bool IsInCompliance => CurrentCompliance >= 100.0;
    public bool HasRecentEvaluation => LastEvaluated.HasValue &&
                                      DateTime.UtcNow - LastEvaluated.Value < EvaluationWindow;

    public void UpdateConfiguration(double? targetValue = null, SLAComparisonOperator? comparisonOperator = null,
        TimeSpan? evaluationWindow = null, double? weight = null)
    {
        if (targetValue.HasValue)
            TargetValue = targetValue.Value;

        if (comparisonOperator.HasValue)
            ComparisonOperator = comparisonOperator.Value;

        if (evaluationWindow.HasValue)
            EvaluationWindow = evaluationWindow.Value;

        if (weight.HasValue)
            Weight = Math.Max(0.1, weight.Value);

        LastModified = DateTime.UtcNow;
    }

    public void UpdateCurrentValue(double value)
    {
        CurrentValue = value;
        LastEvaluated = DateTime.UtcNow;

        // Calculate compliance based on comparison operator
        CurrentCompliance = CalculateCompliance(value);

        LastModified = DateTime.UtcNow;
    }

    public void Enable() => IsEnabled = true;
    public void Disable() => IsEnabled = false;

    private double CalculateCompliance(double actualValue)
    {
        return ComparisonOperator switch
        {
            SLAComparisonOperator.GreaterThan => actualValue > TargetValue ? 100.0 : (actualValue / TargetValue) * 100.0,
            SLAComparisonOperator.GreaterThanOrEqual => actualValue >= TargetValue ? 100.0 : (actualValue / TargetValue) * 100.0,
            SLAComparisonOperator.LessThan => actualValue < TargetValue ? 100.0 : (TargetValue / actualValue) * 100.0,
            SLAComparisonOperator.LessThanOrEqual => actualValue <= TargetValue ? 100.0 : (TargetValue / actualValue) * 100.0,
            SLAComparisonOperator.Equals => Math.Abs(actualValue - TargetValue) < 0.001 ? 100.0 : 0.0,
            _ => 0.0
        };
    }
}

/// <summary>
/// Represents an SLA breach
/// </summary>
public class SLABreach
{
    public SLABreach(Guid slaId, Guid objectiveId, double actualValue, double targetValue,
        BreachSeverity severity, string description)
    {
        Id = Guid.NewGuid();
        SLAId = slaId;
        ObjectiveId = objectiveId;
        ActualValue = actualValue;
        TargetValue = targetValue;
        Severity = severity;
        Description = description ?? throw new ArgumentNullException(nameof(description));
        DetectedAt = DateTime.UtcNow;
        Status = BreachStatus.Open;
        Metadata = new Dictionary<string, object>();
    }

    // Required for EF Core
    private SLABreach() { }

    public Guid Id { get; private set; }
    public Guid SLAId { get; private set; }
    public Guid ObjectiveId { get; private set; }
    public double ActualValue { get; private set; }
    public double TargetValue { get; private set; }
    public BreachSeverity Severity { get; private set; }
    public string Description { get; private set; } = string.Empty;
    public DateTime DetectedAt { get; private set; }
    public DateTime? ResolvedAt { get; private set; }
    public BreachStatus Status { get; private set; }
    public string? ResolutionNotes { get; private set; }
    public Guid? ResolvedBy { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; } = new();

    // Navigation properties
    public ServiceLevelAgreement SLA { get; private set; } = null!;
    public SLAObjective Objective { get; private set; } = null!;

    // Computed properties
    public TimeSpan? Duration => ResolvedAt?.Subtract(DetectedAt);
    public double DeviationPercentage => Math.Abs((ActualValue - TargetValue) / TargetValue) * 100;

    public void Resolve(Guid resolvedBy, string? resolutionNotes = null)
    {
        if (Status == BreachStatus.Open)
        {
            Status = BreachStatus.Resolved;
            ResolvedAt = DateTime.UtcNow;
            ResolvedBy = resolvedBy;
            ResolutionNotes = resolutionNotes;
        }
    }

    public void Acknowledge(Guid acknowledgedBy, string? notes = null)
    {
        if (Status == BreachStatus.Open)
        {
            Status = BreachStatus.Acknowledged;
            Metadata["acknowledged_by"] = acknowledgedBy;
            Metadata["acknowledged_at"] = DateTime.UtcNow;
            if (!string.IsNullOrEmpty(notes))
                Metadata["acknowledgment_notes"] = notes;
        }
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }
}

/// <summary>
/// Represents an SLA report
/// </summary>
public class SLAReport
{
    public SLAReport(Guid slaId, SLAReportType reportType, DateTime periodStart, DateTime periodEnd,
        double overallCompliance, Dictionary<string, object> reportData)
    {
        Id = Guid.NewGuid();
        SLAId = slaId;
        ReportType = reportType;
        PeriodStart = periodStart;
        PeriodEnd = periodEnd;
        OverallCompliance = overallCompliance;
        ReportData = reportData ?? throw new ArgumentNullException(nameof(reportData));
        GeneratedAt = DateTime.UtcNow;
    }

    // Required for EF Core
    private SLAReport() { }

    public Guid Id { get; private set; }
    public Guid SLAId { get; private set; }
    public SLAReportType ReportType { get; private set; }
    public DateTime PeriodStart { get; private set; }
    public DateTime PeriodEnd { get; private set; }
    public double OverallCompliance { get; private set; }
    public DateTime GeneratedAt { get; private set; }
    public Dictionary<string, object> ReportData { get; private set; } = new();

    // Navigation properties
    public ServiceLevelAgreement SLA { get; private set; } = null!;

    // Computed properties
    public TimeSpan ReportPeriod => PeriodEnd.Subtract(PeriodStart);
}
