﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using NetworkFleetManagement.Domain.Entities;

namespace NetworkFleetManagement.Domain.Repositories
{
    public interface IPreferredPartnerRepository
    {
        Task<PreferredPartner?> GetByIdAsync(Guid id);
        Task<IEnumerable<PreferredPartner>> GetAllAsync();
        Task<IEnumerable<PreferredPartner>> GetByUserIdAsync(Guid userId);
        Task<PreferredPartner> AddAsync(PreferredPartner preferredPartner);
        Task UpdateAsync(PreferredPartner preferredPartner);
        Task DeleteAsync(Guid id);
        Task<bool> ExistsAsync(Guid userId, Guid partnerId);
        Task<IEnumerable<PreferredPartner>> GetActivePartnersAsync(Guid userId);
        Task<int> GetPartnerCountAsync(Guid userId);
    }
}
